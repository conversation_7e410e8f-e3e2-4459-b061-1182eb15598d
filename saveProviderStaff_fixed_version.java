// 修复后的 saveProviderStaff 方法建议版本

@Transactional(rollbackFor = Exception.class)
public void saveProviderStaff(IzuStaff izuStaff) {
    Integer staffId = izuStaff.getStaffId();
    String lockKey = String.format(RedisLockPrefix.PROVIDER_STAFF_SYNC, staffId);
    DistributionRedisLock distributionRedisLock = DistributionRedisLock.newLock(lockKey);
    
    try {
        if (!distributionRedisLock.lock()) {
            log.warn("saveProviderStaff-获取锁失败，staffId: {}", staffId);
            throw new RuntimeException("获取锁失败，请重试");
        }
        
        List<ProviderStaff> staffByMgtStaffId = providerStaffExMapper.getStaffByMgtStaffId(staffId);
        
        if (CollUtil.isEmpty(staffByMgtStaffId)) {
            // 新增场景
            ProviderStaff providerStaff = buildProviderStaff(izuStaff, true);
            providerStaffExMapper.insertSelective(providerStaff);
            staffRoleInitService.initUserRoleAuthData(providerStaff);
            log.info("新增ProviderStaff成功, staffId: {}, providerStaffCode: {}", 
                    staffId, providerStaff.getProviderStaffCode());
        } else {
            // 更新场景
            ProviderStaff existingProvider = staffByMgtStaffId.get(0);
            if (existingProvider == null) {
                log.warn("查询到的ProviderStaff为null, staffId: {}", staffId);
                return;
            }
            
            Integer providerStaffId = existingProvider.getProviderStaffId();
            ProviderStaff providerStaff = buildProviderStaff(izuStaff, false);
            
            // 设置ID和编码
            providerStaff.setProviderStaffId(providerStaffId);
            if (StringUtils.isBlank(existingProvider.getProviderStaffCode())) {
                // 如果没有编码，生成一个新的
                String providerStaffCode = sequenceGenerator.generate(new Date(), SeqTypeEnum.PRO_STAFF);
                providerStaff.setProviderStaffCode(providerStaffCode);
            } else {
                providerStaff.setProviderStaffCode(existingProvider.getProviderStaffCode());
            }
            
            // 保持原有的数据权限类型
            providerStaff.setDataPermType(existingProvider.getDataPermType());
            
            // 更新数据库
            providerStaffExMapper.updateByPrimaryKeySelective(providerStaff);
            
            // 初始化用户角色权限数据
            staffRoleInitService.initUserRoleAuthData(providerStaff);
            
            log.info("更新ProviderStaff成功, staffId: {}, providerStaffId: {}", 
                    staffId, providerStaffId);
        }
        
    } catch (Exception e) {
        log.error("saveProviderStaff-执行失败, staffId: {}", staffId, e);
        throw e;
    } finally {
        log.debug("saveProviderStaff-释放锁, lockKey: {}", lockKey);
        distributionRedisLock.unLock();
    }
}
