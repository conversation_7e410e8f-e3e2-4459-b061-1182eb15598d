<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.izu</groupId>
		<artifactId>coreSuperPOM</artifactId>
		<version>1.0.0-SNAPSHOT</version>
	</parent>


	<groupId>com.izu</groupId>
	<artifactId>mrcar-order-common</artifactId>
	<packaging>jar</packaging>
	<version>2.0.2-SNAPSHOT</version>

	<dependencies>
		<dependency>
			<groupId>com.izu.framework</groupId>
			<artifactId>izu-web-support</artifactId>
			<version>1.0.0-SNAPSHOT</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>com.izu.framework</groupId>
			<artifactId>izu-doc</artifactId>
			<version>1.0.0-SNAPSHOT</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.18.26</version>
		</dependency>
		<dependency>
			<groupId>io.swagger</groupId>
			<artifactId>swagger-annotations</artifactId>
			<version>1.6.6</version>
		</dependency>

		<!-- easypoi excel导出 start -->
		<dependency>
			<groupId>cn.afterturn</groupId>
			<artifactId>easypoi-base</artifactId>
			<version>3.2.0</version>
		</dependency>
		<dependency>
			<groupId>cn.afterturn</groupId>
			<artifactId>easypoi-web</artifactId>
			<version>3.2.0</version>
		</dependency>
		<dependency>
			<groupId>cn.afterturn</groupId>
			<artifactId>easypoi-annotation</artifactId>
			<version>3.2.0</version>
		</dependency>
        <!-- easypoi excel导出 end -->

		<!-- IZU excel导出组件 -->
		<dependency>
			<groupId>com.izu.excel</groupId>
			<artifactId>izu-excel</artifactId>
			<version>1.0.5-SNAPSHOT</version>
			<exclusions>
				<exclusion>
					<groupId>com.izu</groupId>
					<artifactId>third-common</artifactId>
				</exclusion>
				<exclusion>
					<groupId>com.izu</groupId>
					<artifactId>iot-common</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.izu</groupId>
			<artifactId>mrcar-iot-common</artifactId>
			<version>2.0.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
			<version>2.14.2</version>
		</dependency>
        <dependency>
            <groupId>org.mvel</groupId>
            <artifactId>mvel2</artifactId>
            <version>2.3.1.Final</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

	<build>
		<finalName>mrcar-order-common</finalName>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>2.18.1</version>
				<configuration>
					<skipTests>true</skipTests>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.1</version>
				<configuration>
					<target>1.8</target>
					<source>1.8</source>
					<compilerArgument>-Xlint:all</compilerArgument>
					<showWarnings>true</showWarnings>
					<showDeprecation>true</showDeprecation>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<version>2.4</version>
				<executions>
					<execution>
						<id>attach-sources</id>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>