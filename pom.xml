<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.izu</groupId>
        <artifactId>izu-parent</artifactId>
        <version>1.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>mrcar-business-core</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>jar</packaging>


    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-json</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>nacos-config-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- 基础组件 begin -->
        <dependency>
            <groupId>com.izu.framework</groupId>
            <artifactId>izu-database</artifactId>
        </dependency>
        <dependency>
            <groupId>com.izu.framework</groupId>
            <artifactId>izu-mq</artifactId>
        </dependency>
        <dependency>
            <groupId>com.izu.framework</groupId>
            <artifactId>izu-web-support</artifactId>
        </dependency>
        <dependency>
            <groupId>com.izu.framework</groupId>
            <artifactId>izu-doc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.izu</groupId>
            <artifactId>izu-job-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.izu.framework</groupId>
            <artifactId>izu-locker</artifactId>
        </dependency>
        <dependency>
            <groupId>com.izu</groupId>
            <artifactId>mrcar-order-common</artifactId>
            <version>2.0.2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.izu</groupId>
            <artifactId>mrcar-business-common</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.izu</groupId>
            <artifactId>mrcar-coupon-common</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.izu</groupId>
            <artifactId>mrcar-user-common</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.izu</groupId>
            <artifactId>mrcar-config-common</artifactId>
            <version>1.0.5-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.izu</groupId>
            <artifactId>mrcar-dispatching-common</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.izu</groupId>
            <artifactId>mrcar-asset-common</artifactId>
            <version>2.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.izu</groupId>
            <artifactId>carAsset-core-common</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.izu</groupId>
            <artifactId>mrcar-workflow-common</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <!-- 基础组件 end -->

        <!-- mongo BEGIN-->
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-mongodb</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>mongodb-driver-sync</artifactId>
        </dependency>
        <!-- mongo END -->

        <!-- mybatis 开发辅助BEGIN-->
        <dependency>
            <groupId>org.mybatis.generator</groupId>
            <artifactId>mybatis-generator-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

        <dependency>
            <groupId>io.sgr</groupId>
            <artifactId>s2-geometry-library-java</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.lbs</groupId>
            <artifactId>tools</artifactId>
            <version>1.2.4</version>
        </dependency>
        <dependency>
            <groupId>com.izu</groupId>
            <artifactId>mrcar-iot-common</artifactId>
            <version>2.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.izu</groupId>
            <artifactId>lbs-common</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.izu.threadpool</groupId>
            <artifactId>threadpool-core</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <!-- 融云SDK -->
        <dependency>
            <groupId>cn.rongcloud.im</groupId>
            <artifactId>server-sdk-java</artifactId>
            <version>3.3.9</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
	
	    <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>

        <dependency>
            <groupId>com.izu</groupId>
            <artifactId>crm-core-common</artifactId>
            <version>1.0.2-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.izu</groupId>
            <artifactId>notify-core-common</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>
        <!--支出推送相关依赖-->
        <dependency>
            <groupId>com.izu</groupId>
            <artifactId>reimbursement-core-common</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.izu</groupId>
            <artifactId>transferFssc-core-common</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>

    </dependencies>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <env.name>dev</env.name>
                <mybatis.logImpl>STDOUT_LOGGING</mybatis.logImpl>
                <env.type>ALI</env.type>
                <izunacos.serverAddress>non-prd-nacos.imrcar.com:18848</izunacos.serverAddress>
                <izunacos.namespace>mrcar-dev</izunacos.namespace>
                <izunacos.username>javaclient</izunacos.username>
                <izunacos.password>ikTVCjnNyQPe3Jt1</izunacos.password>
                <panshi.log.server>https://dev-panshi.izuche.com/api/logger</panshi.log.server>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <env.name>test</env.name>
                <mybatis.logImpl>NO_LOGGING</mybatis.logImpl>
                <env.type>ALI</env.type>
                <izunacos.serverAddress>non-prd-nacos.imrcar.com:18848</izunacos.serverAddress>
                <izunacos.namespace>mrcar-test</izunacos.namespace>
                <izunacos.username>javaclient</izunacos.username>
                <izunacos.password>ikTVCjnNyQPe3Jt1</izunacos.password>
                <panshi.log.server>https://test-panshi.izuche.com/api/logger</panshi.log.server>
            </properties>
        </profile>
        <profile>
            <id>pre</id>
            <properties>
                <env.name>pre</env.name>
                <mybatis.logImpl>NO_LOGGING</mybatis.logImpl>
                <env.type>ALI</env.type>
                <izunacos.serverAddress>non-prd-nacos.imrcar.com:18848</izunacos.serverAddress>
                <izunacos.namespace>mrcar-pre</izunacos.namespace>
                <izunacos.username>javaclient</izunacos.username>
                <izunacos.password>ikTVCjnNyQPe3Jt1</izunacos.password>
                <panshi.log.server>https://pre-panshi.izuche.com/api/logger</panshi.log.server>
            </properties>
        </profile>
        <profile>
            <id>online</id>
            <properties>
                <env.name>online</env.name>
                <mybatis.logImpl>NO_LOGGING</mybatis.logImpl>
                <env.type>MrCar</env.type>
                <izunacos.serverAddress>nacos-server.imrcar.com:18848</izunacos.serverAddress>
                <izunacos.namespace>mrcar-prd</izunacos.namespace>
                <izunacos.username>javaclient</izunacos.username>
                <izunacos.password>kFA3xoIIZtLTT4H5</izunacos.password>
                <panshi.log.server>https://panshi.izuche.com/api/logger</panshi.log.server>
            </properties>
        </profile>
    </profiles>
    <!-- 多环境配置END -->

    <build>
        <finalName>mrcar-business-core</finalName>
        <resources>
            <!-- 先排除资源根目录下各环境的配置 -->
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <excludes>
                    <exclude>dev/*</exclude>
                    <exclude>test/*</exclude>
                    <exclude>pre/*</exclude>
                    <exclude>online/*</exclude>
                </excludes>
            </resource>
            <!-- 包含当前环境下的*.properties (保持目录结构) -->
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>${env.name}/*.properties</include>
                </includes>
            </resource>
            <!-- 包含当前环境下的logback.xml (需复制到CLASSPATH根下面) -->
            <resource>
                <directory>src/main/resources/${env.name}</directory>
                <filtering>true</filtering>
                <includes>
                    <include>logback.xml</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <target>1.8</target>
                    <source>1.8</source>
                    <compilerArgument>-Xlint:all</compilerArgument>
                    <showWarnings>true</showWarnings>
                    <showDeprecation>true</showDeprecation>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
