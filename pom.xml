<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.izuche</groupId>
	<artifactId>mrcar</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<packaging>jar</packaging>
	<name>mrcar</name>

	<properties>
		<fastjson.version>2.0.43</fastjson.version>
	</properties>

	<parent>
		<groupId>com.izu</groupId>
		<artifactId>izu-parent</artifactId>
		<version>1.0.1-SNAPSHOT</version>
	</parent>

	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<!-- izu-job-core -->
		<dependency>
			<groupId>com.izu</groupId>
			<artifactId>izu-job-core</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.boot</groupId>
			<artifactId>nacos-config-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
           <groupId>org.springframework.boot</groupId>
           <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

		<!-- StringUtils -->
		<dependency>
			<groupId>commons-lang</groupId>
			<artifactId>commons-lang</artifactId>
			<version>2.6</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-mongodb</artifactId>
		</dependency>

		<!-- https://mvnrepository.com/artifact/commons-io/commons-io -->
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.5</version>
		</dependency>
		<!-- gson -->
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
		</dependency>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
		</dependency>
		<!-- BeanUtils -->
		<dependency>
			<groupId>commons-beanutils</groupId>
			<artifactId>commons-beanutils</artifactId>
			<version>1.9.3</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
		</dependency>
		<dependency>
		    <groupId>com.belerweb</groupId>
		    <artifactId>pinyin4j</artifactId>
		    <version>2.5.1</version>
		</dependency>
		<!-- 坐标转换 -->
		<dependency>
			<groupId>com.lbs</groupId>
			<artifactId>tools</artifactId>
			<version>1.2.4</version>
		</dependency>
		<dependency>
		  <groupId>org.apache.commons</groupId>
		  <artifactId>commons-pool2</artifactId>
		</dependency>

		<dependency>
		   <groupId>eu.bitwalker</groupId>
		   <artifactId>UserAgentUtils</artifactId>
		   <version>1.21</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.apache.httpcomponents/httpclient -->
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
			<version>4.5.5</version>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.18.26</version>
		</dependency>


		<dependency>
			<groupId>com.izu.framework</groupId>
			<artifactId>izu-web-support</artifactId>
		</dependency>
		<dependency>
			<groupId>com.izu.framework</groupId>
			<artifactId>izu-doc</artifactId>
		</dependency>
		<dependency>
			<groupId>com.izu.framework</groupId>
			<artifactId>izu-mail</artifactId>
		</dependency>
		<dependency>
			<groupId>com.izu</groupId>
			<artifactId>mrcar-asset-common</artifactId>
			<version>2.0.1-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.izu</groupId>
			<artifactId>mrcar-dispatching-common</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.izu</groupId>
			<artifactId>mrcar-order-common</artifactId>
			<version>2.0.2-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.izu</groupId>
			<artifactId>mrcar-config-common</artifactId>
			<version>1.0.5-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>org.quartz-scheduler</groupId>
			<artifactId>quartz</artifactId>
			<version>2.2.2</version>
		</dependency>
		<dependency>
			<groupId>com.izu</groupId>
			<artifactId>mrcar-coupon-common</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.izu</groupId>
			<artifactId>mrcar-iot-common</artifactId>
			<version>2.0.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.izu</groupId>
			<artifactId>mrcar-business-common</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.izu</groupId>
			<artifactId>mrcar-coupon-common</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.izu.framework</groupId>
			<artifactId>izu-locker</artifactId>
		</dependency>
		<dependency>
			<groupId>com.izu.framework</groupId>
			<artifactId>izu-mq</artifactId>
		</dependency>
		<dependency>
			<groupId>com.github.pagehelper</groupId>
			<artifactId>pagehelper</artifactId>
			<version>5.1.8</version>
		</dependency>
		<dependency>
			<groupId>com.izu</groupId>
			<artifactId>mrcar-user-common</artifactId>
			<version>1.0.1-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>io.sgr</groupId>
			<artifactId>s2-geometry-library-java</artifactId>
			<version>1.0.0</version>
		</dependency>
		<dependency>
			<groupId>com.izu</groupId>
			<artifactId>third-common</artifactId>
			<version>1.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.izu</groupId>
			<artifactId>lbs-common</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>com.izu</artifactId>
					<groupId>notify-common</groupId>
				</exclusion>
			</exclusions>
			<version>1.0.1-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.izu</groupId>
			<artifactId>mrcar-workflow-common</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>org.apache.shiro</groupId>
			<artifactId>shiro-core</artifactId>
			<version>1.3.0</version>
			<exclusions>
				<exclusion>
					<artifactId>commons-beanutils</artifactId>
					<groupId>commons-beanutils</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.shiro</groupId>
			<artifactId>shiro-web</artifactId>
			<version>1.3.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.shiro</groupId>
			<artifactId>shiro-spring</artifactId>
			<version>1.4.0</version>
			<exclusions>
				<exclusion>
					<artifactId>shiro-web</artifactId>
					<groupId>org.apache.shiro</groupId>
				</exclusion>
				<exclusion>
					<artifactId>shiro-core</artifactId>
					<groupId>org.apache.shiro</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.izu</groupId>
			<artifactId>notify-core-common</artifactId>
			<version>1.0.1-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>org.apache.shiro</groupId>
			<artifactId>shiro-quartz</artifactId>
			<version>1.4.0</version>
			<exclusions>
				<exclusion>
					<artifactId>shiro-core</artifactId>
					<groupId>org.apache.shiro</groupId>
				</exclusion>
			</exclusions>
		</dependency>

        <!-- easypoi excel导出 start -->
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-base</artifactId>
            <version>3.2.0</version>
        </dependency>
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-web</artifactId>
            <version>3.2.0</version>
        </dependency>
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-annotation</artifactId>
            <version>3.2.0</version>
        </dependency>
        <!-- easypoi excel导出 end -->
		<!-- IZU excel导出组件 start-->
		<dependency>
			<groupId>com.izu.excel</groupId>
			<artifactId>izu-excel</artifactId>
			<version>1.0.7-SNAPSHOT</version>
		</dependency>
		<!-- IZU excel导出组件 end-->
		<!-- Unit Test End -->


		<dependency>
			<groupId>com.izu.framework</groupId>
			<artifactId>izu-global</artifactId>
		</dependency>

		<!-- metrices start -->

		<!-- metrices end -->


		<dependency>
			<groupId>com.google.protobuf</groupId>
			<artifactId>protobuf-java</artifactId>
			<version>3.1.0</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.googlecode.protobuf-java-format</groupId>
			<artifactId>protobuf-java-format</artifactId>
			<version>1.4</version>
		</dependency>

		<dependency>
			<groupId>commons-collections</groupId>
			<artifactId>commons-collections</artifactId>
			<version>3.2.2</version>
		</dependency>

		<dependency>
			<groupId>commons-codec</groupId>
			<artifactId>commons-codec</artifactId>
		</dependency>
		<!-- GPS测试使用 -->
		<dependency>
            <groupId>com.izu</groupId>
            <artifactId>carnet-core-common</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

		<!--引入Knife4j-start包,Swagger2基于Springfox2.10.5项目-->
		<dependency>
			<groupId>com.izu</groupId>
			<artifactId>izu-knife4j-spring-boot-starter</artifactId>
			<version>2.0.9-SNAPSHOT</version>
		</dependency>

		<!-- izu线程池 -->
		<dependency>
			<groupId>com.izu.threadpool</groupId>
			<artifactId>threadpool-core</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>
		<!--图形验证-->
		<dependency>
			<groupId>com.izu</groupId>
			<artifactId>spring-boot-starter-captcha</artifactId>
			<version>1.1.1-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>alibaba-dingtalk-service-sdk</artifactId>
			<version>2.0.0</version>
		</dependency>

		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>dingtalk</artifactId>
			<version>2.0.18</version>
		</dependency>

		<dependency>
			<groupId>org.apache.pdfbox</groupId>
			<artifactId>pdfbox</artifactId>
			<version>2.0.24</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>
		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-registry-prometheus</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.skywalking</groupId>
			<artifactId>apm-toolkit-logback-1.x</artifactId>
			<version>9.2.0</version>
		</dependency>

	</dependencies>

	<!-- 多环境配置BEGIN -->
	<profiles>
		<profile>
			<id>dev</id>
			<properties>
				<env.name>dev</env.name>
				<env.type>ALI</env.type>
				<izunacos.serverAddress>non-prd-nacos.imrcar.com:18848</izunacos.serverAddress>
				<izunacos.namespace>mrcar-dev</izunacos.namespace>
				<izunacos.username>javaclient</izunacos.username>
				<izunacos.password>ikTVCjnNyQPe3Jt1</izunacos.password>
				<panshi.log.server>https://dev-panshi.izuche.com/api/logger</panshi.log.server>
			</properties>
			<!-- 本地启动时默认dev环境 -->
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
		</profile>
		<profile>
			<id>test</id>
			<properties>
				<env.name>test</env.name>
				<env.type>ALI</env.type>
				<izunacos.serverAddress>non-prd-nacos.imrcar.com:18848</izunacos.serverAddress>
				<izunacos.namespace>mrcar-test</izunacos.namespace>
				<izunacos.username>javaclient</izunacos.username>
				<izunacos.password>ikTVCjnNyQPe3Jt1</izunacos.password>
				<panshi.log.server>https://test-panshi.izuche.com/api/logger</panshi.log.server>
			</properties>
		</profile>
		<profile>
			<id>pre</id>
			<properties>
				<env.name>pre</env.name>
				<env.type>ALI</env.type>
				<izunacos.serverAddress>non-prd-nacos.imrcar.com:18848</izunacos.serverAddress>
				<izunacos.namespace>mrcar-pre</izunacos.namespace>
				<izunacos.username>javaclient</izunacos.username>
				<izunacos.password>ikTVCjnNyQPe3Jt1</izunacos.password>
				<panshi.log.server>https://pre-panshi.izuche.com/api/logger</panshi.log.server>
			</properties>
		</profile>
		<profile>
			<id>pro</id>
			<properties>
				<env.name>online</env.name>
				<env.type>MrCar</env.type>
				<izunacos.serverAddress>nacos-server.imrcar.com:18848</izunacos.serverAddress>
				<izunacos.namespace>mrcar-prd</izunacos.namespace>
				<izunacos.username>javaclient</izunacos.username>
				<izunacos.password>kFA3xoIIZtLTT4H5</izunacos.password>
				<panshi.log.server>https://panshi.izuche.com/api/logger</panshi.log.server>
			</properties>
		</profile>
	</profiles>
	<!-- 多环境配置END -->

	<build>
		<finalName>mrcar</finalName>
		<resources>
			<!-- 先排除资源根目录下各环境的配置 -->
			<resource>
				<directory>src/main/resources</directory>
				<filtering>true</filtering>
				<includes>
					<include>application.properties</include>
					<include>environment.properties</include>
					<include>application-${env.name}.properties</include>
					<include>mapper/**</include>
					<include>*.xml</include>
					<include>${env.name}/*.properties</include>
					<include>applicationContext-rocketmq-consumer.xml</include>
					<include>applicationContext-rocketmq-productor.xml</include>
					<include>template/**</include>
					<include>images/**</include>
					<include>META-INF/services/**</include>
				</includes>
			</resource>
			<resource>
				<directory>src/main/resources/${env.name}</directory>
				<filtering>true</filtering>
				<includes>
					<include>logback.xml</include>
				</includes>
			</resource>
		</resources>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<version>2.5.9</version>
				<configuration>
					<fork>true</fork>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<configuration>
					<encoding>UTF-8</encoding>
					<nonFilteredFileExtensions>
						<nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
						<nonFilteredFileExtension>xls</nonFilteredFileExtension>
						<nonFilteredFileExtension>pdf</nonFilteredFileExtension>
					</nonFilteredFileExtensions>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>
