<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN" "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
	<context id="DB2Tables"  defaultModelType="flat"  targetRuntime="MyBatis3">
		<!-- Mybatis generator mapper文件重新生成不会覆盖原文件，为了解决此问题 -->
		<plugin type="mysqlgenerate.OverIsMergeablePlugin"></plugin>
	
		<!-- 是否去除自动生成的注释 -->
		<commentGenerator type="mysqlgenerate.MyCommentGenerator">
			<property name="suppressAllComments" value="false"/>
			<property name="suppressDate" value="true"/>
		</commentGenerator>

		<!-- 数据库连接信息 -->
		<jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"
						connectionURL="*****************************************************************************************************************************"
						userId="mrcar-base-dev-mysql-rw" password="LjqPUH093LEddZYG">
            <property name="nullCatalogMeansCurrent" value="true" />
		</jdbcConnection>
		<javaTypeResolver>
			<property name="forceBigDecimals" value="true"/>
		</javaTypeResolver>

		<!-- po实体类代码位置 -->
		<javaModelGenerator targetPackage="com.izu.order.entity.mrcar" targetProject="C:\WORK\Mapper">
			<property name="enableSubPackages" value="true" />
			<property name="trimStrings" value="true" />
		</javaModelGenerator>
		<!-- sql代码的xml映射文件位置 -->
		<sqlMapGenerator targetPackage="mapper.mrcar" targetProject="C:\WORK\Mapper">
			<property name="enableSubPackages" value="true" />
		</sqlMapGenerator>
		<!-- mapper接口位置 -->
		<javaClientGenerator type="XMLMAPPER" targetPackage="mapper.mrcar" targetProject="C:\WORK\Mapper">
			<property name="enableSubPackages" value="true" />
		</javaClientGenerator>

		<!-- 需要自动生成的表 -->
		<table tableName="t_temporal_shared_vehicle_statistics" domainObjectName="TemporalSharedVehicleStatistics"
			enableSelectByExample="false" enableDeleteByExample="false"
			enableCountByExample="false" enableUpdateByExample="false">
			<property name="useActualColumnNames" value="false" />
			<generatedKey column="id" sqlStatement="MySql" identity="true" />
		</table>

		<!--<table tableName="order_comment_sub" domainObjectName="OrderCommentSub"-->
			   <!--enableSelectByExample="false" enableDeleteByExample="false"-->
			   <!--enableCountByExample="false" enableUpdateByExample="false">-->
			<!--<property name="useActualColumnNames" value="false" />-->
			<!--<generatedKey column="id" sqlStatement="MySql" identity="true" />-->
		<!--</table>-->

		<!--<table tableName="order_comment" domainObjectName="OrderComment"-->
			   <!--enableSelectByExample="false" enableDeleteByExample="false"-->
			   <!--enableCountByExample="false" enableUpdateByExample="false">-->
			<!--<property name="useActualColumnNames" value="false" />-->
			<!--<generatedKey column="comment_id" sqlStatement="MySql" identity="true" />-->
		<!--</table>-->

	</context>
</generatorConfiguration>