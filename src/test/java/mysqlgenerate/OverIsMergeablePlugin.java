package mysqlgenerate;
import java.lang.reflect.Field;
import java.util.List;
import org.mybatis.generator.api.GeneratedXmlFile;
import org.mybatis.generator.api.IntrospectedTable;
import org.mybatis.generator.api.PluginAdapter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/** Mybatis generator mapper文件重新生成不会覆盖原文件，为了解决此问题。**/
public class OverIsMergeablePlugin extends PluginAdapter {

    private static final Logger logger = LoggerFactory.getLogger(OverIsMergeablePlugin.class);

    @Override
    public boolean validate(List<String> warnings) {
        return true;
    }

    @Override
    public boolean sqlMapGenerated(GeneratedXmlFile sqlMap, IntrospectedTable introspectedTable) {
        try {
            Field field = sqlMap.getClass().getDeclaredField("isMergeable");
            field.setAccessible(true);
            field.setBoolean(sqlMap, false);
        } catch (Exception e) {
            logger.error("generator Error",e);
        }
        return true;
    }
}