package mysqlgenerate;
import org.mybatis.generator.api.MyBatisGenerator;
import org.mybatis.generator.config.Configuration;
import org.mybatis.generator.config.xml.ConfigurationParser;
import org.mybatis.generator.internal.DefaultShellCallback;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * mybatis generator反向工具类  (开发人员用滴)
 */
public class GeneratorMySqlWlh {
	public static void generateMySql( String ideName ) throws Exception {
		if("eclipse".equalsIgnoreCase(ideName)) {
			/** eclipse IDE 执行代码 start*/
			List<String> warnings = new ArrayList<>();
			ConfigurationParser cp = new ConfigurationParser(warnings);
			Configuration config = cp.parseConfiguration(GeneratorMySqlWlh.class.getClassLoader().getResourceAsStream(generatorConfigFile) );
			DefaultShellCallback callback = new DefaultShellCallback(true);
			MyBatisGenerator myBatisGenerator = new MyBatisGenerator(config, callback, warnings);
			myBatisGenerator.generate(null);
			/** eclipse IDE 执行代码 end*/
			System.out.println("eclipse-------------------GeneratorMySql OK : "+ generatorConfigFile );
		}
		if("InteliJ".equalsIgnoreCase(ideName)) {
			/** InteliJ IDE 执行代码 start*/
			List<String> warnings = new ArrayList<>();
			ConfigurationParser cp = new ConfigurationParser(warnings);
			File configFile = new File("C:\\izu\\gitlab\\mrcar-order-core\\src\\test\\java\\generatorConfig-mrcar_wlh.xml");
			Configuration config = cp.parseConfiguration(configFile);
			DefaultShellCallback callback = new DefaultShellCallback(true);
			MyBatisGenerator myBatisGenerator = new MyBatisGenerator(config, callback, warnings);
			myBatisGenerator.generate(null);
			/** InteliJ IDE 执行代码 end*/
			System.out.println("InteliJ-------------------GeneratorMySql OK : "+ generatorConfigFile );
		}
	}

	//------------------------------------------------------------------------请开发人员根据各自IDE工具调整如下代码!!!!!!!!!!!!!!!
	//指定 mybatis generator逆向工程配置文件
	private static final String generatorConfigFile = "generatorConfig-mrcar_wlh.xml";
	public static void main(String[] args) throws Exception{
		GeneratorMySqlWlh.generateMySql("InteliJ");  //InteliJ IDE 执行代码
//		GeneratorMySql.generateMySql("eclipse");//eclipse IDE 执行代码
	}
}