package mysqlgenerate;

import org.mybatis.generator.api.MyBatisGenerator;
import org.mybatis.generator.config.Configuration;
import org.mybatis.generator.config.xml.ConfigurationParser;
import org.mybatis.generator.internal.DefaultShellCallback;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * mybatis generator反向工具类  (开发人员用滴)
 */
public class GeneratorMySqlHxc {

    public static void generateMySql() throws Exception {
        List<String> warnings = new ArrayList<>();
        ConfigurationParser cp = new ConfigurationParser(warnings);
        File configFile = new File("/Users/<USER>/mrcar-project/mrcar-order-core/src/test/java/generatorConfig-mrcar-hxc.xml");
        Configuration config = cp.parseConfiguration(configFile);
        DefaultShellCallback callback = new DefaultShellCallback(true);
        MyBatisGenerator myBatisGenerator = new MyBatisGenerator(config, callback, warnings);
        myBatisGenerator.generate(null);
        myBatisGenerator.getGeneratedJavaFiles().forEach(e->{
            System.out.println(e.getTargetPackage() + e.getFileName());
        });
    }

    public static void main(String[] args) throws Exception {
        generateMySql();  //InteliJ IDE 执行代码
    }
}
