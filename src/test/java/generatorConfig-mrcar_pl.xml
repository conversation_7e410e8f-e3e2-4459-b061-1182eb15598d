<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
    <context id="DB2Tables" defaultModelType="flat" targetRuntime="MyBatis3">
        <!-- Mybatis generator mapper文件重新生成不会覆盖原文件，为了解决此问题 -->
        <plugin type="mysqlgenerate.OverIsMergeablePlugin"></plugin>

        <!-- 是否去除自动生成的注释 -->
        <commentGenerator type="com.izu.framework.database.tools.CustomFromDBCommentGenerator">
            <property name="suppressAllComments" value="false"/>
            <property name="suppressDate" value="false"/>
        </commentGenerator>

        <!-- 数据库连接信息 -->
        <jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"
                        connectionURL="*****************************************************************************************************************************"
                        userId="mrcar-base-dev-mysql-rw" password="LjqPUH093LEddZYG">
        </jdbcConnection>
        <javaTypeResolver>
            <property name="forceBigDecimals" value="true"/>
        </javaTypeResolver>

        <!-- po实体类代码位置-->
        <javaModelGenerator targetPackage="com.izu.order.entity.mrcar"
                            targetProject="/Users/<USER>/IdeaProjects/izu/mrcar-order-core/src/main/java">
            <!--<javaModelGenerator targetPackage="com.izu.business.entity" targetProject="/Users/<USER>/IdeaProjects/business/src/main/java">-->
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>
        <!-- sql代码的xml映射文件位置 -->
        <sqlMapGenerator targetPackage="mapper.mrcar"
                         targetProject="/Users/<USER>/IdeaProjects/izu/mrcar-order-core/src/main/resources">
            <!--<sqlMapGenerator targetPackage="mapper" targetProject="/Users/<USER>/IdeaProjects/business/src/main/resources">-->
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>

        <!-- mapper接口位置 -->
        <javaClientGenerator type="XMLMAPPER" targetPackage="mapper.mrcar"
                             targetProject="/Users/<USER>/IdeaProjects/izu/mrcar-order-core/src/main/java">
            <!--<javaClientGenerator type="XMLMAPPER" targetPackage="mapper" targetProject="/Users/<USER>/IdeaProjects/business/src/main/java">-->
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>

        <!-- 需要自动生成的表 -->
        <table tableName="so_statement_bill" domainObjectName="StatementBill"
        	   enableSelectByExample="true" enableDeleteByExample="false"
        	   enableCountByExample="true" enableUpdateByExample="false">
        	<property name="useActualColumnNames" value="false"/>
        	<generatedKey column="statement_bill_id" sqlStatement="MySql" identity="true"/>
        </table>
        <table tableName="so_statement_bill_detail" domainObjectName="StatementBillDetail"
        	   enableSelectByExample="true" enableDeleteByExample="false"
        	   enableCountByExample="true" enableUpdateByExample="false">
        	<property name="useActualColumnNames" value="false"/>
        	<generatedKey column="statement_bill_detail_id" sqlStatement="MySql" identity="true"/>
        </table>
        <table tableName="so_statement_bill_check_record" domainObjectName="StatementBillCheckRecord"
               enableSelectByExample="true" enableDeleteByExample="false"
               enableCountByExample="true" enableUpdateByExample="false">
            <property name="useActualColumnNames" value="false"/>
            <generatedKey column="id" sqlStatement="MySql" identity="true"/>
        </table>

    </context>
</generatorConfiguration>