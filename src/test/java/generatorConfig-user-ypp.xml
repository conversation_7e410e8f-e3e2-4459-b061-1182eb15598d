<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN" "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
	<context id="DB2Tables"  defaultModelType="flat"  targetRuntime="MyBatis3">
		<!-- Mybatis generator mapper文件重新生成不会覆盖原文件，为了解决此问题 -->
		<plugin type="com.izu.framework.database.tools.OverIsMergeablePlugin"></plugin>

		<!-- 是否去除自动生成的注释 -->
		<commentGenerator type="com.izu.framework.database.tools.CustomFromDBCommentGenerator">
			<property name="suppressAllComments" value="false"/>
			<property name="suppressDate" value="false"/>
		</commentGenerator>

		<!-- 数据库连接信息 -->
		<jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"
			connectionURL="****************************************************************************************************************************"
			userId="mrcar-base-dev-mysql-rw" password="LjqPUH093LEddZYG">
		</jdbcConnection>
		<javaTypeResolver>
			<property name="forceBigDecimals" value="true"/>
		</javaTypeResolver>

		<!-- po实体类代码位置 -->
		<javaModelGenerator targetPackage="com.izu.user.entity" targetProject="/Users/<USER>/IdeaProjects/mrcar-user-core/src/main/java">
			<property name="enableSubPackages" value="true" />
			<property name="trimStrings" value="true" />
		</javaModelGenerator>
		<!-- sql代码的xml映射文件位置 -->
		<sqlMapGenerator targetPackage="mapper" targetProject="/Users/<USER>/IdeaProjects/mrcar-user-core/src/main/resources">
			<property name="enableSubPackages" value="true" />
		</sqlMapGenerator>
		<!-- mapper接口位置 -->
		<javaClientGenerator type="XMLMAPPER" targetPackage="mapper" targetProject="/Users/<USER>/IdeaProjects/mrcar-user-core/src/main/java">
			<property name="enableSubPackages" value="true" />
		</javaClientGenerator>

		<!-- 需要自动生成的表
		<table tableName="user_permission_relation" domainObjectName="UserPermissionRelation"
			enableSelectByExample="false" enableDeleteByExample="false"
			enableCountByExample="false" enableUpdateByExample="false">
			<property name="useActualColumnNames" value="false" />
			<generatedKey column="permission_id" sqlStatement="MySql" identity="true" />
		</table>
		<table tableName="manager_permission" domainObjectName="ManagerPermission"
			enableSelectByExample="false" enableDeleteByExample="false"
			enableCountByExample="false" enableUpdateByExample="false">
			<property name="useActualColumnNames" value="false" />
			<generatedKey column="id" sqlStatement="MySql" identity="true" />
		</table>-->
<!-- 		<table tableName="t_customer" domainObjectName="Customer"
			   enableSelectByExample="false" enableDeleteByExample="false"
			   enableCountByExample="false" enableUpdateByExample="false">
			<property name="useActualColumnNames" value="false" />
			<generatedKey column="customer_id" sqlStatement="MySql" identity="true" />
		</table>
		<table tableName="motorcade" domainObjectName="Motorcade"
			enableSelectByExample="false" enableDeleteByExample="false"
			enableCountByExample="false" enableUpdateByExample="false">
			<property name="useActualColumnNames" value="false" />
			<generatedKey column="motorcade_id" sqlStatement="MySql" identity="true" />
			<columnOverride column="motorcade_status" javaType="java.lang.Integer" jdbcType="TINYINT" />
			<columnOverride column="schdule_status" javaType="java.lang.Integer" jdbcType="TINYINT" />
		</table>
		<table tableName="motorcade_driver" domainObjectName="MotorcadeDriver"
			enableSelectByExample="false" enableDeleteByExample="false"
			enableCountByExample="false" enableUpdateByExample="false">
			<property name="useActualColumnNames" value="false" />
			<generatedKey column="id" sqlStatement="MySql" identity="true" />
			<columnOverride column="status" javaType="java.lang.Integer" jdbcType="TINYINT" />
		</table>
		<table tableName="t_driver" domainObjectName="Driver"
			enableSelectByExample="false" enableDeleteByExample="false"
			enableCountByExample="false" enableUpdateByExample="false">
			<property name="useActualColumnNames" value="false" />
			<generatedKey column="driver_id" sqlStatement="MySql" identity="true" />
			<columnOverride column="driver_status" javaType="java.lang.Integer" jdbcType="TINYINT" />
		</table>
		<table tableName="company_sale_update_log" domainObjectName="CompanySaleUpdateLog"
			enableSelectByExample="false" enableDeleteByExample="false"
			enableCountByExample="false" enableUpdateByExample="false">
			<property name="useActualColumnNames" value="false" />
			<generatedKey column="id" sqlStatement="MySql" identity="true" />
		</table>
 		<table tableName="company_bussiness" domainObjectName="CompanyBussiness"
			enableSelectByExample="false" enableDeleteByExample="false"
			enableCountByExample="false" enableUpdateByExample="false">
			<property name="useActualColumnNames" value="false" />
			<generatedKey column="company_service_id" sqlStatement="MySql" identity="true" />
		</table> -->
 		<!--<table tableName="user_quick_menu" domainObjectName="UserQuickMenu"
			enableSelectByExample="false" enableDeleteByExample="false"
			enableCountByExample="false" enableUpdateByExample="false">
			<property name="useActualColumnNames" value="false" />
			<generatedKey column="id" sqlStatement="MySql" identity="true" />
		</table>-->
<!--		<table tableName="t_company" domainObjectName="Company"
			   enableSelectByExample="false" enableDeleteByExample="false"
			   enableCountByExample="false" enableUpdateByExample="false">
			<property name="useActualColumnNames" value="false" />
			<generatedKey column="company_id" sqlStatement="MySql" identity="true" />
		</table>-->
<!-- 		<table tableName="t_schedule_zy" domainObjectName="ScheduleZY"-->
<!--			enableSelectByExample="false" enableDeleteByExample="false"-->
<!--			enableCountByExample="false" enableUpdateByExample="false">-->
<!--			<property name="useActualColumnNames" value="false" />-->
<!--			<generatedKey column="struct_id" sqlStatement="MySql" identity="true" />
		</table> -->
<!--		<table tableName="t_company" domainObjectName="Company"-->
<!--			   enableSelectByExample="true" enableDeleteByExample="true"-->
<!--			   enableCountByExample="true" enableUpdateByExample="true">-->
<!--			<property name="useActualColumnNames" value="false"/>-->
<!--			<generatedKey column="company_id" sqlStatement="MySql" identity="true"/>-->
<!--			<ignoreColumn column="company_authentic"/>-->
<!--			<ignoreColumn column="account_authentic"/>-->
<!--			<ignoreColumn column="account_name"/>-->
<!--			<ignoreColumn column="company_account"/>-->
<!--			<ignoreColumn column="utm_source"/>-->
<!--		</table>-->
		<!--<table tableName="company_sale_update_log" domainObjectName="CompanySaleUpdateLog"
			   enableSelectByExample="false" enableDeleteByExample="false"
			   enableCountByExample="false" enableUpdateByExample="false">
			<property name="useActualColumnNames" value="false" />
			<generatedKey column="id" sqlStatement="MySql" identity="true" />-->

<!--		<table tableName="t_company" domainObjectName="Company"-->
<!--			   enableSelectByExample="true" enableDeleteByExample="true"-->
<!--			   enableCountByExample="true" enableUpdateByExample="true">-->
<!--			<property name="useActualColumnNames" value="false"/>-->
<!--			<generatedKey column="company_id" sqlStatement="MySql" identity="true"/>-->
<!--			<ignoreColumn column="company_authentic"/>-->
<!--			<ignoreColumn column="account_authentic"/>-->
<!--			<ignoreColumn column="account_name"/>-->
<!--			<ignoreColumn column="company_account"/>-->
<!--			<ignoreColumn column="utm_source"/>-->
<!--		</table>-->
<!--		<table tableName="maintain_garage_company_relation" domainObjectName="MaintainGarageCompanyRelation"-->
<!--			enableSelectByExample="false" enableDeleteByExample="false"-->
<!--			enableCountByExample="false" enableUpdateByExample="false">-->
<!--			<property name="useActualColumnNames" value="false" />-->
<!--			<generatedKey column="relation_id" sqlStatement="MySql" identity="true" />-->
<!--		</table>-->
<!--		<table tableName="t_file_info" domainObjectName="FileInfo"-->
<!--			   enableSelectByExample="true" enableDeleteByExample="false"-->
<!--			   enableCountByExample="false" enableUpdateByExample="false">-->
<!--			<property name="useActualColumnNames" value="false" />-->
<!--			<generatedKey column="id" sqlStatement="MySql" identity="true" />-->
<!--		</table>-->
<!--		<table tableName="supplier_synchronization_config" domainObjectName="SupplierSynchronizationConfig"
			enableSelectByExample="true" enableDeleteByExample="true"
			enableCountByExample="true" enableUpdateByExample="true">
			<property name="useActualColumnNames" value="false" />
			<generatedKey column="id" sqlStatement="MySql" identity="true" />
		</table>-->

		<!--<table tableName="t_driver" domainObjectName="Driver"
			   enableSelectByExample="true" enableDeleteByExample="true"
			   enableCountByExample="true" enableUpdateByExample="true">
			<property name="useActualColumnNames" value="false"/>
			<generatedKey column="driver_id" sqlStatement="MySql" identity="true"/>
		</table>-->

		<table tableName="company_paid_services_operation_log" domainObjectName="CompanyPaidServicesOperationLog"
			   enableSelectByExample="true" enableDeleteByExample="true"
			   enableCountByExample="true" enableUpdateByExample="true">
			<property name="useActualColumnNames" value="false"/>
			<generatedKey column="id" sqlStatement="MySql" identity="true"/>
		</table>

	<!--	<table tableName="t_company_paid_services" domainObjectName="CompanyPaidServices"
			   enableSelectByExample="true" enableDeleteByExample="true"
			   enableCountByExample="true" enableUpdateByExample="true">
			<property name="useActualColumnNames" value="false"/>
			<generatedKey column="id" sqlStatement="MySql" identity="true"/>
		</table>-->

		<!--<table tableName="company_paid_services_item" domainObjectName="CompanyPaidServicesItem"
			   enableSelectByExample="true" enableDeleteByExample="true"
			   enableCountByExample="true" enableUpdateByExample="true">
			<property name="useActualColumnNames" value="false"/>
			<generatedKey column="id" sqlStatement="MySql" identity="true"/>
			<columnOverride column="item_info" property="itemInfo" javaType="java.lang.Object" jdbcType="OTHER"/>
		</table>-->





	</context>
</generatorConfiguration>
