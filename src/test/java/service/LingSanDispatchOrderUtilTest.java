package service;

import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.izu.mrcar.order.dto.lingsan.customer.input.DemandOrderDispatchRelateVehicleInfoDTO;
import com.izu.mrcar.order.dto.lingsan.supplier.OrderSupplierSaveDTO;
import com.izu.order.entity.mrcar.OrderCustomer;
import com.izu.order.util.LingSanDispatchOrderUtil;

import java.util.Date;
import java.util.List;

public class LingSanDispatchOrderUtilTest {

    public static void main(String[] args) {
        Date now = new Date();
        String ajson = "{\n" +
                "          \"driverCount\": 1,\n" +
                "          \"levelId\": 12,\n" +
                "          \"levelName\": \"suv\",\n" +
                "          \"remark\": \"\",\n" +
                "          \"supplierId\": \"2\",\n" +
                "          \"supplierName\": \"研发自测供应商-010\",\n" +
                "          \"supplierType\": 2,\n" +
                "          \"vehicleCount\": 1,\n" +
                "          \"vehiclePriceRange\": \"13~15万\"\n" +
                "        }";
        String bjson = "{\n" +
                "  \"orderCustomerId\": 11420,\n" +
                "  \"createId\": 4321,\n" +
                "  \"createName\": \"牛子联\",\n" +
                "  \"createTime\": 1677142427000,\n" +
                "  \"updateId\": 6494,\n" +
                "  \"updateName\": \"牛子联\",\n" +
                "  \"updateTime\": 1677213748000,\n" +
                "  \"delTag\": 0,\n" +
                "  \"orderNum\": \"SCD2302230003931\",\n" +
                "  \"demandOrderNum\": \"SXQ2302220001859\",\n" +
                "  \"orderStatus\": 8,\n" +
                "  \"actualUseTime\": 1677213684000,\n" +
                "  \"actualUseCycle\": 0,\n" +
                "  \"useCycleUnit\": 0,\n" +
                "  \"vehicleLicense\": \"陕A28QH3\",\n" +
                "  \"vehicleId\": 197,\n" +
                "  \"vehicleVin\": \"LSGUD84X8GE075803\",\n" +
                "  \"driverName\": \"李伟\",\n" +
                "  \"driverId\": 3983,\n" +
                "  \"driverMobile\": \"13888987234\",\n" +
                "  \"vehicleRent\": 1,\n" +
                "  \"driverRent\": 2,\n" +
                "  \"supplierVehicleRent\": 1,\n" +
                "  \"supplierDriverRent\": 1,\n" +
                "  \"companyId\": 0,\n" +
                "  \"companyName\": \"\",\n" +
                "  \"companyBelongDepartmentName\": \"\",\n" +
                "  \"companyBelongDepartmentCode\": \"\",\n" +
                "  \"customerName\": \"1\",\n" +
                "  \"customerPhone\": \"17676767676\",\n" +
                "  \"customerCityCode\": \"1101\",\n" +
                "  \"customerCityName\": \"北京\",\n" +
                "  \"customerAddress\": \"1\",\n" +
                "  \"estimateUseDate\": 1676995200000,\n" +
                "  \"estimateReturnDate\": 1677513600000,\n" +
                "  \"hireCycle\": 7,\n" +
                "  \"hireType\": 1,\n" +
                "  \"levelId\": 12,\n" +
                "  \"levelName\": \"suv\",\n" +
                "  \"vehiclePriceRange\": \"13~15万\",\n" +
                "  \"vehicleCount\": 1,\n" +
                "  \"driverCount\": 1,\n" +
                "  \"contractCode\": \"\"\n" +
                "}";
        DemandOrderDispatchRelateVehicleInfoDTO demandOrderDispatchRelateVehicleInfoDTO = JSONUtil.toBean(ajson , DemandOrderDispatchRelateVehicleInfoDTO.class);
        OrderCustomer orderCustomer = JSONUtil.toBean(bjson , OrderCustomer.class);


        List<OrderSupplierSaveDTO> result = LingSanDispatchOrderUtil.createSupplierOrder(Lists.newArrayList(demandOrderDispatchRelateVehicleInfoDTO), Lists.newArrayList(orderCustomer), now,2);
        System.out.println(JSONUtil.toJsonStr(result));
    }

}
