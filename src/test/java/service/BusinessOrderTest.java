package service;

import com.alibaba.fastjson.JSONObject;
import com.izu.mrcar.order.consts.BillConst;
import com.izu.mrcar.order.dto.mrcar.BillAttachDTO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 商务车行程测试
 * @date 2024/5/6 9:32
 */
public class BusinessOrderTest {

    public static void main(String[] args) {
        List<BillConst.BillAttachType> attachPrices = BillConst.BillAttachType.getAttachPrices();
        List<BillAttachDTO> collect = attachPrices.stream().filter(e -> !Objects.equals(e.value(), BillConst.BillAttachType.REMARK.value())).map(e -> new BillAttachDTO(e.value(), e.text(), BigDecimal.ZERO)).collect(Collectors.toList());
        //System.out.println(JSONObject.toJSONString(collect));

        System.out.println(new BigDecimal("0.00"));
    }
}
