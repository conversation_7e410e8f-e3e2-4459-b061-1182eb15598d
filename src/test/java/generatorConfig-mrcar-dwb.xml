<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN" "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
    <context id="DB2Tables"  defaultModelType="flat"  targetRuntime="MyBatis3">
        <!-- Mybatis generator mapper文件重新生成不会覆盖原文件，为了解决此问题 -->
        <plugin type="mysqlgenerate.OverIsMergeablePlugin"></plugin>

        <!-- 是否去除自动生成的注释 -->
        <commentGenerator type="mysqlgenerate.MyCommentGenerator">
            <property name="suppressAllComments" value="false"/>
            <property name="suppressDate" value="true"/>
        </commentGenerator>

        <!-- 数据库连接信息 -->
        <jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"

                        connectionURL="************************************************************************************************************"
                        userId="mrcar-base-dev-mysql-rw" password="LjqPUH093LEddZYG">
        </jdbcConnection>
        <javaTypeResolver>
            <property name="forceBigDecimals" value="true"/>
        </javaTypeResolver>

        <!-- po实体类代码位置 -->
        <javaModelGenerator targetPackage="com.izu.order.entity.mrcar" targetProject="D:\\izu\\mrcar-order-core\\src\\main\\java">
            <property name="enableSubPackages" value="true" />
            <property name="trimStrings" value="true" />
        </javaModelGenerator>
        <!-- sql代码的xml映射文件位置 -->
        <sqlMapGenerator targetPackage="mapper.mrcar" targetProject="D:\\izu\\mrcar-order-core\\src\\main\\resources">
            <property name="enableSubPackages" value="true" />
        </sqlMapGenerator>
        <!-- mapper接口位置 -->
        <javaClientGenerator type="XMLMAPPER" targetPackage="mapper.mrcar" targetProject="D:\\izu\\mrcar-order-core\\src\\main\\java">
            <property name="enableSubPackages" value="true" />
        </javaClientGenerator>

        <!-- 需要自动生成的表 -->
        <!--<table tableName="sequence" domainObjectName="Sequence"
            enableSelectByExample="false" enableDeleteByExample="false"
            enableCountByExample="false" enableUpdateByExample="false">
            <property name="useActualColumnNames" value="false" />
            <generatedKey column="id" sqlStatement="MySql" identity="true" />
        </table>-->
        <!--<table tableName="order_time_stream" domainObjectName="OrderTimeStream"
            enableSelectByExample="false" enableDeleteByExample="false"
            enableCountByExample="false" enableUpdateByExample="false">
            <property name="useActualColumnNames" value="false" />
            <generatedKey column="id" sqlStatement="MySql" identity="true" />
        </table>-->
        <!--<table tableName="apply_pool" domainObjectName="ApplyPool"
               enableSelectByExample="false" enableDeleteByExample="false"
               enableCountByExample="false" enableUpdateByExample="false">
            <property name="useActualColumnNames" value="false" />
            <generatedKey column="apply_id" sqlStatement="MySql" identity="true" />
        </table>-->
        <!--
        <table tableName="order_info" domainObjectName="OrderInfo"
            enableSelectByExample="false" enableDeleteByExample="false"
            enableCountByExample="false" enableUpdateByExample="false">
            <property name="useActualColumnNames" value="false" />
            <generatedKey column="id" sqlStatement="MySql" identity="true" />
        </table>-->
        <!--<table tableName="order_apply_vehicle" domainObjectName="OrderApplyVehicle"
            enableSelectByExample="false" enableDeleteByExample="false"
            enableCountByExample="false" enableUpdateByExample="false">
            <property name="useActualColumnNames" value="false" />
            <generatedKey column="apply_vehicle_id" sqlStatement="MySql" identity="true" />
        </table>-->
        <!--<table tableName="order_cancel_info" domainObjectName="OrderCancelInfo"
            enableSelectByExample="false" enableDeleteByExample="false"
            enableCountByExample="false" enableUpdateByExample="false">
            <property name="useActualColumnNames" value="false" />
            <generatedKey column="id" sqlStatement="MySql" identity="true" />
        </table>-->
        <!--<table tableName="order_certificate" domainObjectName="OrderCertificate"
            enableSelectByExample="false" enableDeleteByExample="false"
            enableCountByExample="false" enableUpdateByExample="false">
            <property name="useActualColumnNames" value="false" />
            <generatedKey column="id" sqlStatement="MySql" identity="true" />
        </table>-->
        <!--<table tableName="order_price_snapshot" domainObjectName="OrderPriceSnapshot"
            enableSelectByExample="false" enableDeleteByExample="false"
            enableCountByExample="false" enableUpdateByExample="false">
            <property name="useActualColumnNames" value="false" />
            <generatedKey column="id" sqlStatement="MySql" identity="true" />
        </table>-->
        <!--<table tableName="driver_action" domainObjectName="DriverAction"
            enableSelectByExample="false" enableDeleteByExample="false"
            enableCountByExample="false" enableUpdateByExample="false">
            <property name="useActualColumnNames" value="false" />
            <generatedKey column="id" sqlStatement="MySql" identity="true" />
        </table>-->
        <!--<table tableName="order_vehicle_img" domainObjectName="OrderVehicleImg"
            enableSelectByExample="false" enableDeleteByExample="false"
            enableCountByExample="false" enableUpdateByExample="false">
            <property name="useActualColumnNames" value="false" />
            <generatedKey column="id" sqlStatement="MySql" identity="true" />
        </table>-->
        <!--		<table tableName="bill" domainObjectName="Bill"-->
        <!--			enableSelectByExample="false" enableDeleteByExample="false"-->
        <!--			enableCountByExample="false" enableUpdateByExample="false">-->
        <!--			<property name="useActualColumnNames" value="false" />-->
        <!--			<generatedKey column="id" sqlStatement="MySql" identity="true" />-->
        <!--		</table>-->
        <!--<table tableName="bill_attach" domainObjectName="BillAttach"
            enableSelectByExample="false" enableDeleteByExample="false"
            enableCountByExample="false" enableUpdateByExample="false">
            <property name="useActualColumnNames" value="false" />
            <generatedKey column="id" sqlStatement="MySql" identity="true" />
        </table>-->
        <!--		<table tableName="bill_order" domainObjectName="BillOrder"
                    enableSelectByExample="false" enableDeleteByExample="false"
                    enableCountByExample="false" enableUpdateByExample="false">
                    <property name="useActualColumnNames" value="false" />
                    <generatedKey column="id" sqlStatement="MySql" identity="true" />
                </table>-->
        <!--<table tableName="push_sms" domainObjectName="PushSms"
            enableSelectByExample="false" enableDeleteByExample="false"
            enableCountByExample="false" enableUpdateByExample="false">
            <property name="useActualColumnNames" value="false" />
            <generatedKey column="id" sqlStatement="MySql" identity="true" />
        </table>-->
        <!--		<table tableName="bill_confirm_msg" domainObjectName="BillConfirmMsg"
                    enableSelectByExample="false" enableDeleteByExample="false"
                    enableCountByExample="false" enableUpdateByExample="false">
                    <property name="useActualColumnNames" value="false" />
                    <generatedKey column="id" sqlStatement="MySql" identity="true" />
                </table>-->
        <!--<table tableName="driver_service_statistics" domainObjectName="DriverServiceStatistics"
               enableSelectByExample="false" enableDeleteByExample="false"
               enableCountByExample="false" enableUpdateByExample="false">
            <property name="useActualColumnNames" value="false" />
            <generatedKey column="id" sqlStatement="MySql" identity="true" />
        </table>-->
        <!--<table tableName="car_lock_log" domainObjectName="CarLockLog"
               enableSelectByExample="false" enableDeleteByExample="false"
               enableCountByExample="false" enableUpdateByExample="false">
            <property name="useActualColumnNames" value="false" />
            <generatedKey column="id" sqlStatement="MySql" identity="true" />
        </table>-->
        <!--<table tableName="order_apply_passenger" domainObjectName="OrderApplyPassenger"
               enableSelectByExample="false" enableDeleteByExample="false"
               enableCountByExample="false" enableUpdateByExample="false">
            <property name="useActualColumnNames" value="false" />
            <generatedKey column="id" sqlStatement="MySql" identity="true" />
        </table>-->
        <!--		<table tableName="order_apply_destination" domainObjectName="OrderApplyDestination"-->
        <!--			   enableSelectByExample="false" enableDeleteByExample="false"-->
        <!--			   enableCountByExample="false" enableUpdateByExample="false">-->
        <!--			<property name="useActualColumnNames" value="false" />-->
        <!--			<generatedKey column="id" sqlStatement="MySql" identity="true" />-->
        <!--		</table>-->

        <!--<table tableName="schedule" domainObjectName="Schedule"
               enableSelectByExample="false" enableDeleteByExample="false"
               enableCountByExample="false" enableUpdateByExample="false">
            <property name="useActualColumnNames" value="false" />
            <generatedKey column="schedule_id" sqlStatement="MySql" identity="true" />
        </table>-->

        <!--<table tableName="order_question" domainObjectName="OrderQuestion"
               enableSelectByExample="false" enableDeleteByExample="false"
               enableCountByExample="false" enableUpdateByExample="false">
            <property name="useActualColumnNames" value="false" />
            <generatedKey column="id" sqlStatement="MySql" identity="true" />
        </table>-->

       <!-- <table tableName="order_business_extend" domainObjectName="OrderBusinessExtend"
               enableSelectByExample="false" enableDeleteByExample="false"
               enableCountByExample="false" enableUpdateByExample="false">
            <property name="useActualColumnNames" value="false" />
            <generatedKey column="id" sqlStatement="MySql" identity="true" />
        </table>-->

<!--        <table tableName="bill_order" domainObjectName="BillOrder"-->
<!--               enableSelectByExample="false" enableDeleteByExample="false"-->
<!--               enableCountByExample="false" enableUpdateByExample="false">-->
<!--            <property name="useActualColumnNames" value="false" />-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true" />-->
<!--        </table>-->


<!--        <table tableName="business_supplier_expenditure" domainObjectName="BusinessSupplierExpenditure"-->
<!--               enableSelectByExample="false" enableDeleteByExample="false"-->
<!--               enableCountByExample="false" enableUpdateByExample="false">-->
<!--            <property name="useActualColumnNames" value="false" />-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true" />-->
<!--        </table>-->
<!--        <table tableName="order_apply" domainObjectName="OrderApply"-->
<!--               enableSelectByExample="true" enableDeleteByExample="true"-->
<!--               enableCountByExample="true" enableUpdateByExample="true">-->
<!--            <property name="useActualColumnNames" value="false" />-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true" />-->
<!--        </table>-->
<!--        <table tableName="order_info" domainObjectName="OrderInfo"-->
<!--               enableSelectByExample="true" enableDeleteByExample="true"-->
<!--               enableCountByExample="true" enableUpdateByExample="true">-->
<!--            <property name="useActualColumnNames" value="false" />-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true" />-->
<!--        </table>-->
<!--        <table tableName="business_supplier_expenditure_detail" domainObjectName="BusinessSupplierExpenditureDetail"-->
<!--               enableSelectByExample="true" enableDeleteByExample="true"-->
<!--               enableCountByExample="true" enableUpdateByExample="true">-->
<!--            <property name="useActualColumnNames" value="false" />-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true" />-->
<!--        </table>-->
        <table tableName="business_supplier_expenditure" domainObjectName="BusinessSupplierExpenditure"
               enableSelectByExample="true" enableDeleteByExample="true"
               enableCountByExample="true" enableUpdateByExample="true">
            <property name="useActualColumnNames" value="false" />
            <generatedKey column="id" sqlStatement="MySql" identity="true" />
        </table>
        <table tableName="business_order_addition_record" domainObjectName="BusinessOrderAdditionRecord"
               enableSelectByExample="true" enableDeleteByExample="true"
               enableCountByExample="true" enableUpdateByExample="true">
            <property name="useActualColumnNames" value="false" />
            <generatedKey column="id" sqlStatement="MySql" identity="true" />
        </table>
        <table tableName="order_business_extend" domainObjectName="OrderBusinessExtend"
               enableSelectByExample="true" enableDeleteByExample="true"
               enableCountByExample="true" enableUpdateByExample="true">
            <property name="useActualColumnNames" value="false" />
            <generatedKey column="id" sqlStatement="MySql" identity="true" />
        </table>
        <table tableName="business_supplier_expenditure_detail" domainObjectName="BusinessSupplierExpenditureDetail"
               enableSelectByExample="true" enableDeleteByExample="true"
               enableCountByExample="true" enableUpdateByExample="true">
            <property name="useActualColumnNames" value="false" />
            <generatedKey column="id" sqlStatement="MySql" identity="true" />
        </table>
<!--        <table tableName="business_operation_log" domainObjectName="BusinessOperationLog"-->
<!--               enableSelectByExample="true" enableDeleteByExample="true"-->
<!--               enableCountByExample="true" enableUpdateByExample="true">-->
<!--            <property name="useActualColumnNames" value="false" />-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true" />-->
<!--        </table>-->

    </context>
</generatorConfiguration>