<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN" "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
	<context id="DB2Tables"  defaultModelType="flat"  targetRuntime="MyBatis3">
		<!-- Mybatis generator mapper文件重新生成不会覆盖原文件，为了解决此问题 -->
		<plugin type="com.izu.framework.database.tools.OverIsMergeablePlugin"></plugin>

		<!-- 是否去除自动生成的注释 -->
		<commentGenerator type="com.izu.framework.database.tools.CustomFromDBCommentGenerator">
			<property name="suppressAllComments" value="false"/>
			<property name="suppressDate" value="false"/>
		</commentGenerator>

		<!-- 数据库连接信息 -->
		<jdbcConnection driverClass="com.mysql.jdbc.Driver"
			connectionURL="****************************************************************************************************************************"
			userId="mrcar-base-dev-mysql-rw" password="LjqPUH093LEddZYG">
		</jdbcConnection>
		<javaTypeResolver>
			<property name="forceBigDecimals" value="true"/>
		</javaTypeResolver>

		<!-- po实体类代码位置 -->
		<javaModelGenerator targetPackage="com.izu.user.entity" targetProject="D:\work\izuCode\mrcar_group\mrcar-user-core\src\main\java">
			<property name="enableSubPackages" value="true" />
			<property name="trimStrings" value="true" />
		</javaModelGenerator>
		<!-- sql代码的xml映射文件位置 -->
		<sqlMapGenerator targetPackage="mapper" targetProject="D:\work\izuCode\mrcar_group\mrcar-user-core\src\main\resources">
			<property name="enableSubPackages" value="true" />
		</sqlMapGenerator>
		<!-- mapper接口位置 -->
		<javaClientGenerator type="XMLMAPPER" targetPackage="mapper" targetProject="D:\work\izuCode\mrcar_group\mrcar-user-core\src\main\java">
			<property name="enableSubPackages" value="true" />
		</javaClientGenerator>
		<!-- 需要自动生成的表-->

<!--		<table tableName="t_driver" domainObjectName="Driver"-->
<!--			   enableSelectByExample="true" enableDeleteByExample="true"-->
<!--			   enableCountByExample="true" enableUpdateByExample="true">-->
<!--			<property name="useActualColumnNames" value="false"/>-->

<!--			<generatedKey column="driver_id" sqlStatement="MySql" identity="true"/>-->
<!--			<ignoreColumn column="white_list"/>-->

<!--			<ignoreColumn column="app_login_token"/>-->
<!--			<ignoreColumn column="is_receive"/>-->
<!--			<ignoreColumn column="registration_id"/>-->
<!--			<ignoreColumn column="device_os"/>-->
<!--			<ignoreColumn column="device_num"/>-->
<!--			<ignoreColumn column="job_no"/>-->
<!--			<ignoreColumn column="driver_telno"/>-->
<!--			<ignoreColumn column="customer_company_id"/>-->
<!--			<ignoreColumn column="staff_type"/>-->
<!--			<ignoreColumn column="driver_level"/>-->
<!--		</table>-->



<!--		<table tableName="t_customer" domainObjectName="Customer"-->
<!--			   enableSelectByExample="true" enableDeleteByExample="true"-->
<!--			   enableCountByExample="true" enableUpdateByExample="true">-->
<!--			<property name="useActualColumnNames" value="false"/>-->
<!--			<generatedKey column="customer_id" sqlStatement="MySql" identity="true"/>-->
<!--		</table>-->


<!--		&lt;!&ndash; 需要自动生成的表&ndash;&gt;-->

<!--		<table tableName="t_driver_manager_error" domainObjectName="DriverManagerError"-->
<!--			   enableSelectByExample="true" enableDeleteByExample="true"-->
<!--			   enableCountByExample="true" enableUpdateByExample="true">-->
<!--			<property name="useActualColumnNames" value="false"/>-->
<!--			<generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--		</table>-->



		<table tableName="t_company" domainObjectName="Company"
			   enableSelectByExample="true" enableDeleteByExample="true"
			   enableCountByExample="true" enableUpdateByExample="true">
			<property name="useActualColumnNames" value="false"/>
			<generatedKey column="company_id" sqlStatement="MySql" identity="true"/>
		</table>




	</context>
</generatorConfiguration>
