<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
		"http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
	<context id="DB2Tables" defaultModelType="flat" targetRuntime="MyBatis3">
		<!-- Mybatis generator mapper文件重新生成不会覆盖原文件，为了解决此问题 -->
		<plugin type="mysqlgenerate.OverIsMergeablePlugin"></plugin>

		<!-- 是否去除自动生成的注释 -->
		<commentGenerator type="com.izu.framework.database.tools.CustomFromDBCommentGenerator">
			<property name="suppressAllComments" value="false"/>
			<property name="suppressDate" value="false"/>
		</commentGenerator>

		<!-- 数据库连接信息 -->
		<jdbcConnection driverClass="com.mysql.cj.jdbc.Driver"
						connectionURL="********************************************************************************************************************************************************************************************************************************"
						userId="mrcar-base-dev-mysql-rw" password="LjqPUH093LEddZYG">
		</jdbcConnection>
		<javaTypeResolver>
			<property name="forceBigDecimals" value="true"/>
		</javaTypeResolver>

		<!-- po实体类代码位置-->
		<javaModelGenerator targetPackage="com.izu.business.entity"
							targetProject="/Users/<USER>/IdeaProjects/mrcar-business-core/src/main/java">
			<property name="enableSubPackages" value="true"/>
			<property name="trimStrings" value="true"/>
		</javaModelGenerator>
		<!-- sql代码的xml映射文件位置 -->
		<sqlMapGenerator targetPackage="mapper"
						 targetProject="/Users/<USER>/IdeaProjects/mrcar-business-core/src/main/resources">
			<property name="enableSubPackages" value="true"/>
		</sqlMapGenerator>

		<!-- mapper接口位置 -->
		<javaClientGenerator type="XMLMAPPER" targetPackage="mapper"
							 targetProject="/Users/<USER>/IdeaProjects/mrcar-business-core/src/main/java">
			<property name="enableSubPackages" value="true"/>
		</javaClientGenerator>

		<!-- 需要自动生成的表 -->
		<!--<table tableName="supplier_expenditure" domainObjectName="SupplierExpenditure"
			   enableSelectByExample="true" enableDeleteByExample="true"
			   enableCountByExample="true" enableUpdateByExample="true">
			<property name="useActualColumnNames" value="false" />
			<generatedKey column="id" sqlStatement="MySql" identity="true" />
		</table>-->
		<table tableName="supplier_expenditure_detail" domainObjectName="SupplierExpenditureDetail"
			   enableSelectByExample="true" enableDeleteByExample="true"
			   enableCountByExample="true" enableUpdateByExample="true">
			<property name="useActualColumnNames" value="false" />
			<generatedKey column="id" sqlStatement="MySql" identity="true" />
		</table>
		<!--<table tableName="expenditure_operation_log" domainObjectName="ExpenditureOperationLog"
			   enableSelectByExample="true" enableDeleteByExample="true"
			   enableCountByExample="true" enableUpdateByExample="true">
			<property name="useActualColumnNames" value="false" />
			<generatedKey column="id" sqlStatement="MySql" identity="true" />
		</table>

		<table tableName="file_info" domainObjectName="FileInfo"
			   enableSelectByExample="true" enableDeleteByExample="false"
			   enableCountByExample="true" enableUpdateByExample="true">
			<property name="useActualColumnNames" value="false" />
			<generatedKey column="id" sqlStatement="MySql" identity="true" />
		</table>-->






	</context>
</generatorConfiguration>