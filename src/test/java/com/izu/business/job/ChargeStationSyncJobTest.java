package com.izu.business.job;


import com.alibaba.fastjson2.JSONObject;
import com.izu.business.cache.BusinessJSONableCache;
import com.izu.business.cache.CacheConstant;
import com.izu.business.entity.mongo.charge.ChargeStation;
import com.izu.business.service.charge.ChargeStationSyncService;
import com.izu.config.dto.CityDicDTO;
import com.izu.user.dto.charge.ChargeOperatorOutDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.LinkedHashMap;
import java.util.Map;

@Slf4j
@SpringBootTest
class ChargeStationSyncJobTest {

    @Autowired
    private ChargeStationSyncJob chargeStationSyncJob;
    @Autowired
    private ChargeStationSyncService chargeStationSyncService;
    @Autowired
    private BusinessJSONableCache redisSentinelCache;

    @Test
    public void test(){
        redisSentinelCache.delete(CacheConstant.GO_FUN_TOKEN_KEY);

        try {
            chargeStationSyncJob.execute("");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void testDetail(){
        ChargeStation station = new ChargeStation();
        Map<String, CityDicDTO> cityMap = new LinkedHashMap<>();
        try {
            JSONObject stationInfo = station();
//            String channelOperatorId = station.getChannelOperatorId();
            ChargeOperatorOutDTO operator = new ChargeOperatorOutDTO();
            operator.setOperatorId(1);
            operator.setChannelId(1);
            operator.setChannelName("智行");
//            station.setChannelOperatorId(channelOperatorId);
            // 根据详情补全剩余信息
            chargeStationSyncService.initStationData(station, stationInfo.toJavaObject(ChargeStation.class), operator, cityMap);
//            station.setUpdateTime(syncDate);
//            saveOfReplace(station);
        } catch (Exception e) {
            log.error("保存充电站信息失败，stationId={}", 1, e);
        }
    }

    public JSONObject station(){
        String s = "{\n" +
                "  \"stationId\": \"WCWC-SD64293\",\n" +
                "  \"stationName\": \"顺德区乐从沙滘南村1号充电站\",\n" +
                "  \"stationLat\": 22.92816,\n" +
                "  \"stationLng\": 113.089023,\n" +
                "  \"stationTel\": \"4000826699\",\n" +
                "  \"address\": \"顺德区乐从沙滘南村充电站\",\n" +
                "  \"pictures\": [\n" +
                "    \n" +
                "  ],\n" +
                "  \"stationStatus\": 50,\n" +
                "  \"stationType\": 1,\n" +
                "  \"operatorId\": \"MA005DBW1\",\n" +
                "  \"busineHours\": \"00:00-24:00\",\n" +
                "  \"areaCode\": \"440606\",\n" +
                "  \"fastAllChargeConnector\": 8,\n" +
                "  \"fastUsableChargeConnector\": 2,\n" +
                "  \"slowAllChargeConnector\": 0,\n" +
                "  \"slowUsableChargeConnector\": 0,\n" +
                "  \"fastConnectInfoList\": [\n" +
                "    {\n" +
                "      \"stationId\": \"WCWC-SD64293\",\n" +
                "      \"equipmentId\": \"932222003930\",\n" +
                "      \"equipmentType\": 1,\n" +
                "      \"connectorId\": \"WCWC-SD64293#932222003931\",\n" +
                "      \"connectorType\": 4,\n" +
                "      \"power\": 120.0,\n" +
                "      \"current\": 250,\n" +
                "      \"status\": 0\n" +
                "    },\n" +
                "    {\n" +
                "      \"stationId\": \"WCWC-SD64293\",\n" +
                "      \"equipmentId\": \"932222003930\",\n" +
                "      \"equipmentType\": 1,\n" +
                "      \"connectorId\": \"WCWC-SD64293#932222003932\",\n" +
                "      \"connectorType\": 4,\n" +
                "      \"power\": 120.0,\n" +
                "      \"current\": 250,\n" +
                "      \"status\": 0\n" +
                "    },\n" +
                "    {\n" +
                "      \"stationId\": \"WCWC-SD64293\",\n" +
                "      \"equipmentId\": \"932222004230\",\n" +
                "      \"equipmentType\": 1,\n" +
                "      \"connectorId\": \"WCWC-SD64293#932222004231\",\n" +
                "      \"connectorType\": 4,\n" +
                "      \"power\": 120.0,\n" +
                "      \"current\": 250,\n" +
                "      \"status\": 1\n" +
                "    },\n" +
                "    {\n" +
                "      \"stationId\": \"WCWC-SD64293\",\n" +
                "      \"equipmentId\": \"932222004230\",\n" +
                "      \"equipmentType\": 1,\n" +
                "      \"connectorId\": \"WCWC-SD64293#932222004232\",\n" +
                "      \"connectorType\": 4,\n" +
                "      \"power\": 120.0,\n" +
                "      \"current\": 250,\n" +
                "      \"status\": 1\n" +
                "    },\n" +
                "    {\n" +
                "      \"stationId\": \"WCWC-SD64293\",\n" +
                "      \"equipmentId\": \"932222004240\",\n" +
                "      \"equipmentType\": 1,\n" +
                "      \"connectorId\": \"WCWC-SD64293#932222004241\",\n" +
                "      \"connectorType\": 4,\n" +
                "      \"power\": 120.0,\n" +
                "      \"current\": 250,\n" +
                "      \"status\": 0\n" +
                "    },\n" +
                "    {\n" +
                "      \"stationId\": \"WCWC-SD64293\",\n" +
                "      \"equipmentId\": \"932222004240\",\n" +
                "      \"equipmentType\": 1,\n" +
                "      \"connectorId\": \"WCWC-SD64293#932222004242\",\n" +
                "      \"connectorType\": 4,\n" +
                "      \"power\": 120.0,\n" +
                "      \"current\": 250,\n" +
                "      \"status\": 0\n" +
                "    },\n" +
                "    {\n" +
                "      \"stationId\": \"WCWC-SD64293\",\n" +
                "      \"equipmentId\": \"932222004260\",\n" +
                "      \"equipmentType\": 1,\n" +
                "      \"connectorId\": \"WCWC-SD64293#932222004261\",\n" +
                "      \"connectorType\": 4,\n" +
                "      \"power\": 120.0,\n" +
                "      \"current\": 250,\n" +
                "      \"status\": 0\n" +
                "    },\n" +
                "    {\n" +
                "      \"stationId\": \"WCWC-SD64293\",\n" +
                "      \"equipmentId\": \"932222004260\",\n" +
                "      \"equipmentType\": 1,\n" +
                "      \"connectorId\": \"WCWC-SD64293#932222004262\",\n" +
                "      \"connectorType\": 4,\n" +
                "      \"power\": 120.0,\n" +
                "      \"current\": 250,\n" +
                "      \"status\": 0\n" +
                "    }\n" +
                "  ],\n" +
                "  \"slowConnectInfoList\": [\n" +
                "    \n" +
                "  ],\n" +
                "  \"policyInfoList\": [\n" +
                "    {\n" +
                "      \"startTime\": \"00:00\",\n" +
                "      \"endTime\": \"08:00\",\n" +
                "      \"elecPrice\": 0.2900,\n" +
                "      \"servicePrice\": 0.306,\n" +
                "      \"sevicePrice\": 0.306\n" +
                "    },\n" +
                "    {\n" +
                "      \"startTime\": \"08:00\",\n" +
                "      \"endTime\": \"10:00\",\n" +
                "      \"elecPrice\": 0.7100,\n" +
                "      \"servicePrice\": 0.1615,\n" +
                "      \"sevicePrice\": 0.1615\n" +
                "    },\n" +
                "    {\n" +
                "      \"startTime\": \"10:00\",\n" +
                "      \"endTime\": \"11:00\",\n" +
                "      \"elecPrice\": 1.1900,\n" +
                "      \"servicePrice\": 0.136,\n" +
                "      \"sevicePrice\": 0.136\n" +
                "    },\n" +
                "    {\n" +
                "      \"startTime\": \"11:00\",\n" +
                "      \"endTime\": \"12:00\",\n" +
                "      \"elecPrice\": 1.1900,\n" +
                "      \"servicePrice\": 0.136,\n" +
                "      \"sevicePrice\": 0.136\n" +
                "    },\n" +
                "    {\n" +
                "      \"startTime\": \"12:00\",\n" +
                "      \"endTime\": \"14:00\",\n" +
                "      \"elecPrice\": 0.7100,\n" +
                "      \"servicePrice\": 0.1615,\n" +
                "      \"sevicePrice\": 0.1615\n" +
                "    },\n" +
                "    {\n" +
                "      \"startTime\": \"14:00\",\n" +
                "      \"endTime\": \"15:00\",\n" +
                "      \"elecPrice\": 1.1900,\n" +
                "      \"servicePrice\": 0.136,\n" +
                "      \"sevicePrice\": 0.136\n" +
                "    },\n" +
                "    {\n" +
                "      \"startTime\": \"15:00\",\n" +
                "      \"endTime\": \"17:00\",\n" +
                "      \"elecPrice\": 1.1900,\n" +
                "      \"servicePrice\": 0.136,\n" +
                "      \"sevicePrice\": 0.136\n" +
                "    },\n" +
                "    {\n" +
                "      \"startTime\": \"17:00\",\n" +
                "      \"endTime\": \"19:00\",\n" +
                "      \"elecPrice\": 1.1900,\n" +
                "      \"servicePrice\": 0.136,\n" +
                "      \"sevicePrice\": 0.136\n" +
                "    },\n" +
                "    {\n" +
                "      \"startTime\": \"19:00\",\n" +
                "      \"endTime\": \"24:00\",\n" +
                "      \"elecPrice\": 0.7100,\n" +
                "      \"servicePrice\": 0.1615,\n" +
                "      \"sevicePrice\": 0.1615\n" +
                "    }\n" +
                "  ]\n" +
                "}";
        return JSONObject.parseObject(s);
    }
}