package com.izu.business.service.expenditure;

import com.izu.business.dto.expenditure.req.SupplierExpenditureEditReqDTO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * 支出单编辑方法性能测试
 * 验证数据库层面汇总相比内存汇总的性能优势
 * 
 * <AUTHOR>
 * @date 2025/6/27
 */
@SpringBootTest
@ActiveProfiles("test")
public class EditExpenditurePerformanceTest {

    @Resource
    private SupplierExpenditureService supplierExpenditureService;

    @Test
    public void testEditExpenditurePerformance() {
        // 准备测试数据
        SupplierExpenditureEditReqDTO reqDTO = new SupplierExpenditureEditReqDTO();
        reqDTO.setExpenditureNo("SZC123456789");
        reqDTO.setExpenditureType(1); // 充电代垫
        reqDTO.setSupplierCode("SUPPLIER001");
        reqDTO.setSupplierName("测试供应商");
        reqDTO.setSupplierRate(new BigDecimal("0.13"));
        reqDTO.setSupplierInvoiceType(1); // 普票
        reqDTO.setOperateBussCode("ORG001");
        reqDTO.setOperateBussName("测试组织");
        
        // 模拟大量receiptIds变更
        List<Long> receiptIds = Arrays.asList(
            1001L, 1002L, 1003L, 1004L, 1005L, 1006L, 1007L, 1008L, 1009L, 1010L,
            1011L, 1012L, 1013L, 1014L, 1015L, 1016L, 1017L, 1018L, 1019L, 1020L,
            2001L, 2002L, 2003L, 2004L, 2005L // 新增的receiptIds
        );
        reqDTO.setReceiptIds(receiptIds);

        // 性能测试
        long startTime = System.currentTimeMillis();
        
        try {
            Boolean result = supplierExpenditureService.editExpenditure(reqDTO);
            
            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;
            
            System.out.println("=== 支出单编辑性能测试结果 ===");
            System.out.println("执行时间: " + executionTime + " ms");
            System.out.println("编辑结果: " + result);
            System.out.println("单据数量: " + receiptIds.size());
            System.out.println("支出单号: " + reqDTO.getExpenditureNo());
            
            // 验证结果
            assert result != null && result;
            
        } catch (Exception e) {
            System.err.println("测试执行失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testEditExpenditureWithLargeDataset() {
        // 测试大数据集场景
        SupplierExpenditureEditReqDTO reqDTO = new SupplierExpenditureEditReqDTO();
        reqDTO.setExpenditureNo("SZC987654321");
        reqDTO.setExpenditureType(1);
        reqDTO.setSupplierCode("SUPPLIER002");
        reqDTO.setSupplierName("大数据供应商");
        reqDTO.setSupplierRate(new BigDecimal("0.13"));
        reqDTO.setSupplierInvoiceType(2); // 专票
        reqDTO.setOperateBussCode("ORG002");
        reqDTO.setOperateBussName("大数据组织");
        
        // 模拟更大量的receiptIds
        List<Long> receiptIds = Arrays.asList(
            // 保留的receiptIds (1000-1099)
            1000L, 1001L, 1002L, 1003L, 1004L, 1005L, 1006L, 1007L, 1008L, 1009L,
            1010L, 1011L, 1012L, 1013L, 1014L, 1015L, 1016L, 1017L, 1018L, 1019L,
            1020L, 1021L, 1022L, 1023L, 1024L, 1025L, 1026L, 1027L, 1028L, 1029L,
            1030L, 1031L, 1032L, 1033L, 1034L, 1035L, 1036L, 1037L, 1038L, 1039L,
            1040L, 1041L, 1042L, 1043L, 1044L, 1045L, 1046L, 1047L, 1048L, 1049L,
            // 新增的receiptIds (2000-2049)
            2000L, 2001L, 2002L, 2003L, 2004L, 2005L, 2006L, 2007L, 2008L, 2009L,
            2010L, 2011L, 2012L, 2013L, 2014L, 2015L, 2016L, 2017L, 2018L, 2019L,
            2020L, 2021L, 2022L, 2023L, 2024L, 2025L, 2026L, 2027L, 2028L, 2029L,
            2030L, 2031L, 2032L, 2033L, 2034L, 2035L, 2036L, 2037L, 2038L, 2039L,
            2040L, 2041L, 2042L, 2043L, 2044L, 2045L, 2046L, 2047L, 2048L, 2049L
        );
        reqDTO.setReceiptIds(receiptIds);

        long startTime = System.currentTimeMillis();
        
        try {
            Boolean result = supplierExpenditureService.editExpenditure(reqDTO);
            
            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;
            
            System.out.println("=== 大数据集编辑性能测试结果 ===");
            System.out.println("执行时间: " + executionTime + " ms");
            System.out.println("编辑结果: " + result);
            System.out.println("单据数量: " + receiptIds.size());
            System.out.println("支出单号: " + reqDTO.getExpenditureNo());
            
        } catch (Exception e) {
            System.err.println("大数据集测试执行失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testBatchEditPerformance() {
        // 批量编辑性能测试
        System.out.println("=== 批量编辑性能测试开始 ===");
        
        int batchCount = 5;
        long totalTime = 0;
        
        for (int i = 0; i < batchCount; i++) {
            SupplierExpenditureEditReqDTO reqDTO = new SupplierExpenditureEditReqDTO();
            reqDTO.setExpenditureNo("SZC" + (100000000 + i));
            reqDTO.setExpenditureType(1);
            reqDTO.setSupplierCode("SUPPLIER" + String.format("%03d", i));
            reqDTO.setSupplierName("批量测试供应商" + i);
            reqDTO.setSupplierRate(new BigDecimal("0.13"));
            reqDTO.setSupplierInvoiceType(1);
            reqDTO.setOperateBussCode("ORG" + String.format("%03d", i));
            reqDTO.setOperateBussName("批量测试组织" + i);
            
            List<Long> receiptIds = Arrays.asList(
                (long)(1000 + i * 10), (long)(1001 + i * 10), (long)(1002 + i * 10),
                (long)(1003 + i * 10), (long)(1004 + i * 10), (long)(1005 + i * 10),
                (long)(2000 + i * 10), (long)(2001 + i * 10), (long)(2002 + i * 10)
            );
            reqDTO.setReceiptIds(receiptIds);
            
            long startTime = System.currentTimeMillis();
            
            try {
                Boolean result = supplierExpenditureService.editExpenditure(reqDTO);
                long endTime = System.currentTimeMillis();
                long executionTime = endTime - startTime;
                totalTime += executionTime;
                
                System.out.println("第" + (i + 1) + "次编辑执行时间: " + executionTime + " ms, 结果: " + result);
                
            } catch (Exception e) {
                System.err.println("第" + (i + 1) + "次编辑执行失败: " + e.getMessage());
            }
        }
        
        double averageTime = (double) totalTime / batchCount;
        System.out.println("=== 批量编辑性能测试结果 ===");
        System.out.println("总执行次数: " + batchCount);
        System.out.println("总执行时间: " + totalTime + " ms");
        System.out.println("平均执行时间: " + String.format("%.2f", averageTime) + " ms");
    }

    @Test
    public void testOperationDescriptionLogic() {
        // 测试操作描述逻辑的正确性
        System.out.println("=== 操作描述逻辑测试 ===");
        
        SupplierExpenditureEditReqDTO reqDTO = new SupplierExpenditureEditReqDTO();
        reqDTO.setExpenditureNo("SZC999999999");
        reqDTO.setExpenditureType(1);
        reqDTO.setSupplierCode("SUPPLIER999");
        reqDTO.setSupplierName("描述测试供应商");
        reqDTO.setSupplierRate(new BigDecimal("0.13"));
        reqDTO.setSupplierInvoiceType(1);
        reqDTO.setOperateBussCode("ORG999");
        reqDTO.setOperateBussName("描述测试组织");
        
        List<Long> receiptIds = Arrays.asList(9001L, 9002L, 9003L);
        reqDTO.setReceiptIds(receiptIds);
        
        try {
            Boolean result = supplierExpenditureService.editExpenditure(reqDTO);
            System.out.println("操作描述测试完成，结果: " + result);
            System.out.println("注意：检查日志中的操作描述是否正确显示'由旧金额改为新金额'");
            
        } catch (Exception e) {
            System.err.println("操作描述测试失败: " + e.getMessage());
        }
    }
}
