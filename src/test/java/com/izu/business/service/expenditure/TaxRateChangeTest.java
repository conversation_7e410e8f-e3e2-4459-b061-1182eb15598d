package com.izu.business.service.expenditure;

import com.izu.business.dto.expenditure.req.SupplierExpenditureEditReqDTO;
import com.izu.business.util.TaxCalculationUtil;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * 税率变化时保留明细重新计算测试
 * 验证当税率发生变化时，保留明细的不含税金额能够正确重新计算
 * 
 * <AUTHOR>
 * @date 2025/6/27
 */
@SpringBootTest
@ActiveProfiles("test")
public class TaxRateChangeTest {

    @Resource
    private SupplierExpenditureService supplierExpenditureService;

    @Test
    public void testTaxRateChangeRecalculation() {
        System.out.println("=== 税率变化重新计算测试 ===");
        
        // 准备测试数据
        SupplierExpenditureEditReqDTO reqDTO = new SupplierExpenditureEditReqDTO();
        reqDTO.setExpenditureNo("SZC_TAX_TEST_001");
        reqDTO.setExpenditureType(1); // 充电代垫
        reqDTO.setSupplierCode("TAX_SUPPLIER_001");
        reqDTO.setSupplierName("税率测试供应商");
        reqDTO.setSupplierInvoiceType(1); // 普票
        reqDTO.setOperateBussCode("TAX_ORG_001");
        reqDTO.setOperateBussName("税率测试组织");
        
        // 原税率13%，新税率9%
        BigDecimal oldTaxRate = new BigDecimal("0.13");
        BigDecimal newTaxRate = new BigDecimal("0.09");
        reqDTO.setSupplierRate(newTaxRate);
        
        // 模拟保留的receiptIds
        List<Long> receiptIds = Arrays.asList(
            3001L, 3002L, 3003L, 3004L, 3005L, 3006L, 3007L, 3008L, 3009L, 3010L
        );
        reqDTO.setReceiptIds(receiptIds);

        try {
            long startTime = System.currentTimeMillis();
            Boolean result = supplierExpenditureService.editExpenditure(reqDTO);
            long endTime = System.currentTimeMillis();
            
            System.out.println("税率变化编辑结果: " + result);
            System.out.println("执行时间: " + (endTime - startTime) + " ms");
            System.out.println("原税率: " + oldTaxRate);
            System.out.println("新税率: " + newTaxRate);
            System.out.println("保留明细数量: " + receiptIds.size());
            
            // 验证税率计算逻辑
            testTaxCalculationLogic(oldTaxRate, newTaxRate);
            
        } catch (Exception e) {
            System.err.println("税率变化测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testTaxCalculationLogic() {
        System.out.println("=== 税率计算逻辑验证 ===");
        
        BigDecimal oldTaxRate = new BigDecimal("0.13");
        BigDecimal newTaxRate = new BigDecimal("0.09");
        
        testTaxCalculationLogic(oldTaxRate, newTaxRate);
    }

    private void testTaxCalculationLogic(BigDecimal oldTaxRate, BigDecimal newTaxRate) {
        // 模拟含税金额
        BigDecimal taxInclusiveAmount = new BigDecimal("1130.00");
        
        // 按原税率计算的不含税金额
        BigDecimal oldNoTaxAmount = TaxCalculationUtil.calculateNoTaxAmount(taxInclusiveAmount, oldTaxRate);
        
        // 按新税率计算的不含税金额
        BigDecimal newNoTaxAmount = TaxCalculationUtil.calculateNoTaxAmount(taxInclusiveAmount, newTaxRate);
        
        System.out.println("含税金额: " + taxInclusiveAmount);
        System.out.println("原税率(" + oldTaxRate + ")计算的不含税金额: " + oldNoTaxAmount);
        System.out.println("新税率(" + newTaxRate + ")计算的不含税金额: " + newNoTaxAmount);
        System.out.println("不含税金额差异: " + newNoTaxAmount.subtract(oldNoTaxAmount));
        
        // 验证计算结果
        assert oldNoTaxAmount.compareTo(BigDecimal.ZERO) > 0;
        assert newNoTaxAmount.compareTo(BigDecimal.ZERO) > 0;
        assert newNoTaxAmount.compareTo(oldNoTaxAmount) > 0; // 税率降低，不含税金额应该增加
    }

    @Test
    public void testNoTaxRateChange() {
        System.out.println("=== 税率未变化测试 ===");
        
        // 准备测试数据
        SupplierExpenditureEditReqDTO reqDTO = new SupplierExpenditureEditReqDTO();
        reqDTO.setExpenditureNo("SZC_NO_TAX_CHANGE_001");
        reqDTO.setExpenditureType(1);
        reqDTO.setSupplierCode("NO_CHANGE_SUPPLIER_001");
        reqDTO.setSupplierName("税率不变测试供应商");
        reqDTO.setSupplierInvoiceType(1);
        reqDTO.setOperateBussCode("NO_CHANGE_ORG_001");
        reqDTO.setOperateBussName("税率不变测试组织");
        
        // 税率保持13%不变
        BigDecimal taxRate = new BigDecimal("0.13");
        reqDTO.setSupplierRate(taxRate);
        
        List<Long> receiptIds = Arrays.asList(4001L, 4002L, 4003L, 4004L, 4005L);
        reqDTO.setReceiptIds(receiptIds);

        try {
            long startTime = System.currentTimeMillis();
            Boolean result = supplierExpenditureService.editExpenditure(reqDTO);
            long endTime = System.currentTimeMillis();
            
            System.out.println("税率不变编辑结果: " + result);
            System.out.println("执行时间: " + (endTime - startTime) + " ms");
            System.out.println("税率: " + taxRate + " (未变化)");
            System.out.println("保留明细数量: " + receiptIds.size());
            System.out.println("注意：税率未变化时，应该使用数据库中已有的不含税金额，不进行重新计算");
            
        } catch (Exception e) {
            System.err.println("税率不变测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Test
    public void testMultipleTaxRateChanges() {
        System.out.println("=== 多种税率变化场景测试 ===");
        
        // 测试多种税率变化场景
        BigDecimal[] oldRates = {new BigDecimal("0.13"), new BigDecimal("0.09"), new BigDecimal("0.06")};
        BigDecimal[] newRates = {new BigDecimal("0.09"), new BigDecimal("0.13"), new BigDecimal("0.03")};
        
        for (int i = 0; i < oldRates.length; i++) {
            System.out.println("--- 场景 " + (i + 1) + " ---");
            System.out.println("税率变化: " + oldRates[i] + " -> " + newRates[i]);
            
            testTaxCalculationLogic(oldRates[i], newRates[i]);
            
            // 模拟编辑操作
            SupplierExpenditureEditReqDTO reqDTO = new SupplierExpenditureEditReqDTO();
            reqDTO.setExpenditureNo("SZC_MULTI_TAX_" + String.format("%03d", i + 1));
            reqDTO.setExpenditureType(1);
            reqDTO.setSupplierCode("MULTI_SUPPLIER_" + String.format("%03d", i + 1));
            reqDTO.setSupplierName("多税率测试供应商" + (i + 1));
            reqDTO.setSupplierRate(newRates[i]);
            reqDTO.setSupplierInvoiceType(1);
            reqDTO.setOperateBussCode("MULTI_ORG_" + String.format("%03d", i + 1));
            reqDTO.setOperateBussName("多税率测试组织" + (i + 1));
            
            List<Long> receiptIds = Arrays.asList(
                (long)(5000 + i * 10 + 1), (long)(5000 + i * 10 + 2), (long)(5000 + i * 10 + 3)
            );
            reqDTO.setReceiptIds(receiptIds);
            
            try {
                Boolean result = supplierExpenditureService.editExpenditure(reqDTO);
                System.out.println("编辑结果: " + result);
            } catch (Exception e) {
                System.err.println("场景 " + (i + 1) + " 测试失败: " + e.getMessage());
            }
            
            System.out.println();
        }
    }

    @Test
    public void testTaxRateValidation() {
        System.out.println("=== 税率验证测试 ===");
        
        // 测试各种边界情况
        BigDecimal[] testRates = {
            null,
            BigDecimal.ZERO,
            new BigDecimal("0.01"),
            new BigDecimal("0.13"),
            new BigDecimal("0.17"),
            new BigDecimal("1.00")
        };
        
        for (BigDecimal rate : testRates) {
            System.out.println("测试税率: " + rate);
            
            if (rate != null && TaxCalculationUtil.isValidTaxRate(rate)) {
                BigDecimal testAmount = new BigDecimal("100.00");
                BigDecimal noTaxAmount = TaxCalculationUtil.calculateNoTaxAmount(testAmount, rate);
                System.out.println("  含税金额: " + testAmount + " -> 不含税金额: " + noTaxAmount);
            } else {
                System.out.println("  无效税率或null值");
            }
        }
    }
}
