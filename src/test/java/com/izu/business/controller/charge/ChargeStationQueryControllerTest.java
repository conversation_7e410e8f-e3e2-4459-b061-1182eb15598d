package com.izu.business.controller.charge;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSON;
import com.izu.business.consts.ChargeGoFunEnum;
import com.izu.business.dto.bi.ReportTableConfigReqDTO;
import com.izu.business.dto.bi.resp.TableColumnRespDTO;
import com.izu.business.dto.charge.ChargeStationDTO;
import com.izu.business.dto.gofun.input.ChargeStationQueryDTO;
import com.izu.business.entity.mongo.charge.ChargeStation;
import com.izu.business.service.mrcarUseContract.MrCarBillingGenerateService;
import com.izu.business.service.bi.BIReportTableConfigService;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.user.dto.CompanyPaidRespDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.geo.GeoResults;
import org.springframework.data.geo.Metrics;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.geo.GeoJsonPoint;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.NearQuery;
import org.springframework.data.mongodb.core.query.Query;

import javax.annotation.Resource;

import java.util.List;

import static org.springframework.data.mongodb.core.query.NearQuery.near;

@Slf4j
@SpringBootTest
class ChargeStationQueryControllerTest {

    @Autowired
    private ChargeStationQueryController chargeStationQueryController;

    @Autowired
    private MongoTemplate mongoTemplate;
    @Resource
    private BIReportTableConfigService biReportTableConfigService;

    @Autowired
    private MrCarBillingGenerateService mrCarBillingGenerateService;

    @Test
    public void testPage() {

        ChargeStationQueryDTO queryDTO = new ChargeStationQueryDTO();
        queryDTO.setPage(1);
        queryDTO.setPageSize(10);
        double[] geo = new double[]{116.320791, 39.716909};
        queryDTO.setGeo(geo);
        queryDTO.setDis(30);
        RestResponse<PageDTO<ChargeStationDTO>> pageDTORestResponse = chargeStationQueryController.queryByPage(queryDTO);
        System.out.println(JSON.toJSON(pageDTORestResponse));
    }

    @Test
    public void testDetail() {
        RestResponse<ChargeStationDTO> response = chargeStationQueryController.detail("JMNY-JJ116579");
        System.out.println(JSON.toJSON(response));
    }

    @Test
    public void testQuery() {
        double[] geo = new double[]{116.42447894539293, 39.5366065489965};
        Query query = new Query();
        query.addCriteria(Criteria.where("stationStatus").is(ChargeGoFunEnum.StationStatus.NORMAL.getCode()));
        query.addCriteria(Criteria.where("stationType").is(ChargeGoFunEnum.StationType.PUBLIC.getCode()));
        query.addCriteria(Criteria.where("operatorId").in(1, 2, 3, 4));
//        query.addCriteria(Criteria.where("stationLocation").nearSphere(new Point(geo[0], geo[1])).maxDistance(30 * 1000));
        log.info("充电桩列表查询条件：{}", query);

        //1、构造中心点
        GeoJsonPoint geoJsonPoint = new GeoJsonPoint(geo[0], geo[1]);
        //2、构建NearQuery对象-会返回直线距离
        NearQuery nearQuery = near(geoJsonPoint, Metrics.KILOMETERS).maxDistance(30, Metrics.KILOMETERS);
        nearQuery.query(query);

        //3、调用mongoTemplate的geoNear方法查询
        // 查询附近的电站-返回结果包含content和distance，content=ChargeStation实体，distance包含距离和单位（千米）

        log.info("充电桩列表查询条件：{}", nearQuery);
        GeoResults<ChargeStation> results = mongoTemplate.geoNear(nearQuery, ChargeStation.class);
        System.out.println(JSON.toJSON(results));
//        long count = mongoTemplate.count(query, ChargeStation.class);
//        System.out.println(count);

//        mongoTemplate.find()
    }
    @Test
    public void test(){
        String name="[{\"id\":81,\"companyId\":257,\"companyCode\":\"122102114224065531\",\"companyName\":\"大连市甘井子区辛寨子社区卫生服务中心\",\"paidServiceCode\":\"PAID2025031500015\",\"paidServiceType\":60,\"billingDayOfMonth\":16,\"contractCategory\":3,\"contractCode\":\"SQZL-2025CRMMRCAR000005\",\"cooperationStatus\":0,\"expirationDate\":1745942400000,\"signingEntityCreditCode\":\"91110101783228756J\",\"signingEntityFinancialCode\":\"91110101783228756J\",\"signingEntityFinancialName\":\"首汽租赁有限责任公司北京分公司\",\"salesEntityCreditCode\":\"91110101783228756J\",\"salesEntityFinancialCode\":\"4862C1C0000000611D6B11B6A58A97F7\",\"salesEntityFinancialName\":\"北京首汽（集团）股份有限公司\",\"selfVehicleConfig\":0,\"violationQryPrice\":0.00,\"violationImageQryPrice\":0.00,\"creatorId\":339,\"creatorName\":\"宋祥和\",\"creationTime\":1742011055000,\"modifierId\":339,\"modifierName\":\"宋祥和\",\"modificationTime\":1742011055000,\"orderNo\":\"ZZ2025031500015\",\"businessType\":1,\"startDate\":1742054400000,\"maintainId\":\"339\",\"maintainName\":\"宋祥和\",\"signSalesId\":\"338\",\"signSalesName\":\"宋永旺\",\"paymentMethod\":3,\"paymentType\":1,\"billingCycle\":15,\"prepaidAmount\":0.00,\"lowBalanceThreshold\":0.00,\"replenishDay\":1,\"billEndPaymentDays\":0,\"monthlyBillingEntryDate\":0,\"taxRate\":0.00,\"price\":100.00,\"totalAmount\":1000.00,\"num\":10.00},{\"id\":82,\"companyId\":257,\"companyCode\":\"122102114224065531\",\"companyName\":\"大连市甘井子区辛寨子社区卫生服务中心\",\"paidServiceCode\":\"PAID2025031500016\",\"paidServiceType\":61,\"billingDayOfMonth\":16,\"contractCategory\":3,\"contractCode\":\"SQZL-2025CRMMRCAR000005\",\"cooperationStatus\":0,\"expirationDate\":1745942400000,\"signingEntityCreditCode\":\"91110101783228756J\",\"signingEntityFinancialCode\":\"91110101783228756J\",\"signingEntityFinancialName\":\"首汽租赁有限责任公司北京分公司\",\"salesEntityCreditCode\":\"91110101783228756J\",\"salesEntityFinancialCode\":\"4862C1C0000000611D6B11B6A58A97F7\",\"salesEntityFinancialName\":\"北京首汽（集团）股份有限公司\",\"selfVehicleConfig\":0,\"violationQryPrice\":0.00,\"violationImageQryPrice\":0.00,\"creatorId\":339,\"creatorName\":\"宋祥和\",\"creationTime\":1742011055000,\"modifierId\":339,\"modifierName\":\"宋祥和\",\"modificationTime\":1742011055000,\"orderNo\":\"ZZ2025031500016\",\"businessType\":1,\"startDate\":1742054400000,\"maintainId\":\"339\",\"maintainName\":\"宋祥和\",\"signSalesId\":\"338\",\"signSalesName\":\"宋永旺\",\"paymentMethod\":3,\"paymentType\":2,\"billingCycle\":15,\"prepaidAmount\":0.00,\"lowBalanceThreshold\":0.00,\"replenishDay\":1,\"billEndPaymentDays\":0,\"monthlyBillingEntryDate\":0,\"taxRate\":0.00,\"price\":200.00,\"totalAmount\":6000.00,\"num\":30.00},{\"id\":83,\"companyId\":257,\"companyCode\":\"122102114224065531\",\"companyName\":\"大连市甘井子区辛寨子社区卫生服务中心\",\"paidServiceCode\":\"PAID2025031500017\",\"paidServiceType\":0,\"billingDayOfMonth\":16,\"contractCategory\":3,\"contractCode\":\"SQZL-2025CRMMRCAR000005\",\"cooperationStatus\":0,\"expirationDate\":1745942400000,\"signingEntityCreditCode\":\"91110101783228756J\",\"signingEntityFinancialCode\":\"91110101783228756J\",\"signingEntityFinancialName\":\"首汽租赁有限责任公司北京分公司\",\"salesEntityCreditCode\":\"91110101783228756J\",\"salesEntityFinancialCode\":\"4862C1C0000000611D6B11B6A58A97F7\",\"salesEntityFinancialName\":\"北京首汽（集团）股份有限公司\",\"selfVehicleConfig\":0,\"violationQryPrice\":0.00,\"violationImageQryPrice\":0.00,\"creatorId\":339,\"creatorName\":\"宋祥和\",\"creationTime\":1742011055000,\"modifierId\":339,\"modifierName\":\"宋祥和\",\"modificationTime\":1742011055000,\"orderNo\":\"ZZ2025031500017\",\"businessType\":1,\"startDate\":1742054400000,\"maintainId\":\"339\",\"maintainName\":\"宋祥和\",\"signSalesId\":\"338\",\"signSalesName\":\"宋永旺\",\"paymentMethod\":3,\"paymentType\":2,\"billingCycle\":15,\"prepaidAmount\":0.00,\"lowBalanceThreshold\":0.00,\"replenishDay\":1,\"billEndPaymentDays\":0,\"monthlyBillingEntryDate\":0,\"taxRate\":0.00,\"price\":1000.00,\"totalAmount\":3200.00,\"num\":1.00},{\"id\":84,\"companyId\":257,\"companyCode\":\"122102114224065531\",\"companyName\":\"大连市甘井子区辛寨子社区卫生服务中心\",\"paidServiceCode\":\"PAID2025031500018\",\"paidServiceType\":0,\"billingDayOfMonth\":16,\"contractCategory\":3,\"contractCode\":\"SQZL-2025CRMMRCAR000005\",\"cooperationStatus\":0,\"expirationDate\":1745942400000,\"signingEntityCreditCode\":\"91110101783228756J\",\"signingEntityFinancialCode\":\"91110101783228756J\",\"signingEntityFinancialName\":\"首汽租赁有限责任公司北京分公司\",\"salesEntityCreditCode\":\"91110101783228756J\",\"salesEntityFinancialCode\":\"4862C1C0000000611D6B11B6A58A97F7\",\"salesEntityFinancialName\":\"北京首汽（集团）股份有限公司\",\"selfVehicleConfig\":0,\"violationQryPrice\":0.00,\"violationImageQryPrice\":0.00,\"creatorId\":339,\"creatorName\":\"宋祥和\",\"creationTime\":1742011056000,\"modifierId\":339,\"modifierName\":\"宋祥和\",\"modificationTime\":1742011056000,\"orderNo\":\"ZZ2025031500018\",\"businessType\":1,\"startDate\":1742054400000,\"maintainId\":\"339\",\"maintainName\":\"宋祥和\",\"signSalesId\":\"338\",\"signSalesName\":\"宋永旺\",\"paymentMethod\":3,\"paymentType\":2,\"billingCycle\":10,\"prepaidAmount\":0.00,\"lowBalanceThreshold\":0.00,\"replenishDay\":1,\"billEndPaymentDays\":0,\"monthlyBillingEntryDate\":0,\"taxRate\":0.06,\"price\":8888.00,\"totalAmount\":8888.00,\"num\":1.00}]";
        JSONArray jsonArray=JSON.parseArray(name);

        jsonArray.forEach(item -> {
            JSONObject obj = (JSONObject) item;
            CompanyPaidRespDTO dto=JSONObject.toJavaObject(obj, CompanyPaidRespDTO.class);
            try{
                // 账单生成
                mrCarBillingGenerateService.generate(dto);
            }catch (Exception e){
            }
        });
    }
    @Test
    public void test1(){
        String str="{\"billDate\":1744819200000,\"billEndPaymentDays\":5,\"billingCycle\":22,\"billingDayOfMonth\":17,\"businessType\":2,\"companyCode\":\"KH-11012022071900015\",\"companyId\":913,\"companyName\":\"斜斟半盏茶\",\"contractCategory\":1,\"contractCode\":\"SQZL-2021CRMCZ004459\",\"cooperationStatus\":1,\"creationTime\":1742180488000,\"creatorName\":\"牛子联\",\"expirationDate\":\"2025-04-02\",\"id\":51,\"lowBalanceThreshold\":0.00,\"maintainId\":\"4172\",\"maintainName\":\"丘燕玲(qiuyanling)\",\"modificationTime\":1742298691000,\"modifierName\":\"牛子联\",\"monthlyBillingEntryDate\":0,\"num\":0.00,\"orderNo\":\"ZZ2025031700002\",\"paidServiceCode\":\"PAID2025031700002\",\"paidServiceType\":4,\"paymentMethod\":2,\"paymentMethodStr\":\"月付\",\"paymentType\":0,\"paymentTypeStr\":\"\",\"prepaidAmount\":0.00,\"price\":0.00,\"replenishDay\":0,\"salesEntityCreditCode\":\"91110101783228756J\",\"salesEntityFinancialCode\":\"78322875600000\",\"salesEntityFinancialName\":\"首汽租赁有限责任公司北京分公司\",\"selfVehicleConfig\":0,\"signSalesId\":\"220\",\"signSalesName\":\"谢攀(xiepan)\",\"signingEntityCreditCode\":\"91310107782813806L\",\"signingEntityFinancialCode\":\"78281380600000\",\"signingEntityFinancialName\":\"首汽租赁有限责任公司上海分公司\",\"startDate\":\"2025-03-17\",\"taxRate\":0.06,\"taxRateStr\":\"6.00%\",\"totalAmount\":0.00,\"violationImageQryPrice\":0.00,\"violationQryPrice\":0.00}";
        mrCarBillingGenerateService.generate(JSONObject.parseObject(str,CompanyPaidRespDTO.class));
    }

    @Test
    public void testqq(){
        ReportTableConfigReqDTO reqDTO = new ReportTableConfigReqDTO();
        reqDTO.setLoginCompanyId(5);
        reqDTO.setReportScene(1);
        reqDTO.setLoginUserId(1);
        List<TableColumnRespDTO> reportTableConfig = biReportTableConfigService.getReportTableConfig(reqDTO);
        System.out.println(reportTableConfig);
    }
}