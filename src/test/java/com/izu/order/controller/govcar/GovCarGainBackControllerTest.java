package com.izu.order.controller.govcar;

import com.alibaba.fastjson2.JSON;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.order.consts.govCar.GovCarCheckRecordTypeEnum;
import com.izu.mrcar.order.consts.govCar.GovCarFileTypeEnum;
import com.izu.mrcar.order.dto.govcar.GovCarCheckRecordParamsDTO;
import com.izu.mrcar.order.dto.govcar.GovCarFileDTO;
import com.izu.mrcar.order.dto.govcar.GovCarGainBackRecordPageParamsDTO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;


@SpringBootTest
class GovCarGainBackControllerTest {

    @Autowired
    private GovCarGainBackController govCarGainBackController;

    @Test
    public void testStartGainBack(){
        RestResponse restResponse = govCarGainBackController.startCheck("CXDD2408130005535", GovCarCheckRecordTypeEnum.GAIN_CAR.getCode());
//        RestResponse restResponse = govCarGainBackController.startCheck("CXDD20240808001", GovCarCheckRecordTypeEnum.BACK_CAR.getCode());
        System.out.println(JSON.toJSON(restResponse));
    }


    @Test
    public void testFinishGainBack(){
        GovCarCheckRecordParamsDTO paramsDTO = new GovCarCheckRecordParamsDTO();
        paramsDTO.setOrderNo("CXDD2408130005535");
//        paramsDTO.setGainBackNo(copy.getGainBackNo());
        paramsDTO.setOilQuantity((byte) 80);
        paramsDTO.setPowerQuantity((byte) 60);
        paramsDTO.setMileageQuantity(2000);
        paramsDTO.setMemo("顺丰贵车啦");
        paramsDTO.setCheckType(GovCarCheckRecordTypeEnum.GAIN_CAR.getCode());
        paramsDTO.setCheckUserId(6104);
        paramsDTO.setCheckUserName("王洪东");
//        paramsDTO.setCheckStartTime(copy.getCheckStartTime());
//        paramsDTO.setCheckEndTime(copy.getCheckEndTime());

        paramsDTO.setLoginUserId(6104);
        paramsDTO.setLoginUserName("王洪东");
        List<GovCarFileDTO> list = new ArrayList<>();
        for (GovCarFileTypeEnum value : GovCarFileTypeEnum.values()) {
            GovCarFileDTO fileDTO = new GovCarFileDTO();
            fileDTO.setFileType(value.getCode());
            fileDTO.setFileUrl("https://sqzl-referer-only-oss.izuche.com/pc/dev/crm/leaseFrameContractUploda/f523ab51cb314c9e1804144439072618458.png");
            fileDTO.setFileName(value.getName());
            list.add(fileDTO);
        }


        paramsDTO.setFileList(list);

        RestResponse restResponse = govCarGainBackController.finishCheck(paramsDTO);
        System.out.println(JSON.toJSON(restResponse));
    }

    @Test
    public void testQueryPage(){
        GovCarGainBackRecordPageParamsDTO params = new GovCarGainBackRecordPageParamsDTO();
        RestResponse restResponse = govCarGainBackController.queryByPage(params);
        System.out.println(JSON.toJSON(restResponse));
    }

    @Test
    public void testQueryDetail(){
        GovCarGainBackRecordPageParamsDTO params = new GovCarGainBackRecordPageParamsDTO();
        RestResponse restResponse = govCarGainBackController.queryGainBackRecord("CXYC2408090001743");
        System.out.println(JSON.toJSON(restResponse));
    }


    @Test
    public void testCheckRecord(){
        GovCarGainBackRecordPageParamsDTO params = new GovCarGainBackRecordPageParamsDTO();
        RestResponse restResponse = govCarGainBackController.queryCheckRecord("CXDD20240809001", GovCarCheckRecordTypeEnum.GAIN_CAR.getCode());
        System.out.println(JSON.toJSON(restResponse));
    }

}