package com.izu.order.controller.govcar;

import com.alibaba.fastjson2.JSON;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.order.consts.govCar.GovCarOrderContractEnums;
import com.izu.mrcar.order.dto.mrcar.bus.req.BusOrderCreateDTO;
import com.izu.order.controller.bus.BusOrderSaveController;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class GovCarOrderContractControllerTest {

    @Autowired
    private BusOrderSaveController controller;

    @Test
    public void test() {
       String json = "{ \"busOrderDispatchInfos\":[ { \"assignedDriverId\":7056, \"assignedDriverMobile\":\"***********\", \"assignedDriverName\":\"测试组司机\", \"assignedVehicleId\":158019, \"assignedVehicleLicense\":\"粤C24680\", \"assignedVehicleVin\":\"\", \"passengerMobile\":\"\", \"passengerName\":\"\", \"passengerNum\":10, \"totalSettlementAmount\":100, \"vehicleLevel\":\"\" } ], \"businessType\":2, \"contactMobile\":\"***********\", \"contactName\":\"刘得\", \"customerCode\":\"KH2025031600009\", \"customerId\":44, \"customerName\":\"测试个人\", \"departmentId\":4436, \"dispatchType\":0, \"estimatedEndTime\":\"2025-03-17 19:55:00\", \"estimatedStartTime\":\"2025-03-17 19:50:00\", \"loginCompanyCode\":\"KH-33012021032300001\", \"loginCompanyId\":704, \"loginCompanyName\":\"研几（北京）新媒体文化有限公司\", \"loginUserId\":47290, \"loginUserMobile\":\"***********\", \"loginUserName\":\"刘壳壳\", \"orderCreateType\":1, \"orderRouteSnapshotList\":[ { \"cityName\":\"\", \"latitude\":39.918737, \"longitude\":116.465785, \"provinceName\":\"\", \"stationAddress\":\"北京市-朝阳区-建外街道光华路丙12号1号楼数码01大厦10层1001室\", \"stationName\":\"芝麻桌游·Sesame·BoardGame(数码01大厦店)\", \"stationType\":0 }, { \"cityName\":\"\", \"latitude\":39.968941, \"longitude\":116.394349, \"provinceName\":\"\", \"stationAddress\":\"北京市-朝阳区-黄寺大街人定湖北巷11号\", \"stationName\":\"盒马鲜生(mini黄寺店)\", \"stationType\":2 } ], \"passengerMobile\":\"\", \"passengerName\":\"\", \"remark\":\"\", \"sourceDepartmentId\":649, \"sourceDepartmentName\":\"研几（北京）新媒体文化有限公司\", \"totalSettlementAmount\":100 }";
        RestResponse restResponse = controller.createBusOrder(JSON.parseObject(json, BusOrderCreateDTO.class));
        System.out.println(JSON.toJSONString(restResponse));

    }

    @Test
    public void testSendMail() {

    }


}