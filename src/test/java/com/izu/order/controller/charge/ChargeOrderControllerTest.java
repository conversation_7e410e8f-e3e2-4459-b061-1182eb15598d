package com.izu.order.controller.charge;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.izu.asset.dto.CarInfoDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.order.dto.charge.ChargeBillQueryParams;
import com.izu.mrcar.order.dto.charge.ChargeOrderDTO;
import com.izu.mrcar.order.dto.charge.ChargeOrderQueryParams;
import com.izu.order.rpc.CarAssetRpc;
import mapper.mrcar.ex.ChargeOrderExMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import java.util.List;

@SpringBootTest
public class ChargeOrderControllerTest {

    @Autowired
    private ChargeOrderQueryController chargeOrderQueryController;
    @Autowired
    private ChargeOrderController chargeOrderController;

    @Autowired
    private ChargeOrderExMapper chargeOrderExMapper;

    @Test
    public void testSave() throws Throwable {
        String s = "{\n" +
                "        \"chargeEndTime\":\"2024-06-15 19:18:58\",\n" +
                "        \"chargeStationId\":\"JMNY-JJ116579\",\n" +
                "        \"companyId\":1509,\n" +
                "        \"companyName\":\"华坪县机关事务管理局\",\n" +
                "        \"connectorId\":\"JMNY-JJ116579#1600989\",\n" +
                "        \"enterWay\":1,\n" +
                "        \"equipmentId\":\"1600989\",\n" +
                "        \"qrCode\":\"https://www.ev-charging.com.cn/scans/result.html?data=1600989\",\n" +
                "        \"userId\":43790,\n" +
                "        \"userName\":\"牛子联\",\n" +
                "        \"userPhone\":\"15510520468\",\n" +
                "        \"vehicleLicense\":\"冀A5V8V6\",\n" +
                "        \"vehicleLicenseUrl\":\"https://sqzl-img.oss-cn-beijing.aliyuncs.com/mrcarapp/20240615191857file.png\"\n" +
                "}";
        ChargeOrderDTO chargeOrderDTO = JSON.parseObject(s, ChargeOrderDTO.class);
        RestResponse restResponse = chargeOrderController.saveChargeOrder(chargeOrderDTO);
        System.out.println(JSON.toJSON(restResponse));
    }

    @Test
    public void queryByPage() {
        String s = "{\n" +
                "        \"chargeStatusList\":[\n" +
                "\n" +
                "        ],\n" +
                "        \"companyId\":871,\n" +
                "        \"createTimeBegin\":\"2023-06-17\",\n" +
                "        \"createTimeEnd\":\"2024-06-17\",\n" +
                "        \"page\":1,\n" +
                "        \"pageSize\":10,\n" +
                "        \"searchCriteria\":\"\"\n" +
                "}";
        ChargeOrderQueryParams params = JSON.parseObject(s, ChargeOrderQueryParams.class);
        RestResponse restResponse = chargeOrderQueryController.queryByPage(params);
        System.out.println(JSON.toJSON(restResponse));
    }

    @Test
    public void detail() {
        ChargeBillQueryParams params = new ChargeBillQueryParams();
        RestResponse restResponse = chargeOrderQueryController.detail(26);
        System.out.println(JSON.toJSON(restResponse));
    }


    @Test
    public void testInsert() {

        List<CarInfoDTO> carInfoByVehicleIds = CarAssetRpc.getCarInfoByVehicleIds(Lists.newArrayList(296L
                , 3916L
                , 23780L
                , 23780L
        ));
        System.out.println(JSON.toJSONString(carInfoByVehicleIds));
    }

    public static void main(String[] args) {

    }

}