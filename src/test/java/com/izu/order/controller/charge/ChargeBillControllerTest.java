package com.izu.order.controller.charge;

import com.alibaba.fastjson2.JSON;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.exception.RestErrorException;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.order.dto.charge.ChargeBillQueryParams;
import com.izu.mrcar.order.errcode.MrCarOrderErrorCode;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
public class ChargeBillControllerTest {

    @Autowired
    private ChargeBillController chargeBillController;

    @Test
    public void queryByPage() {
        ChargeBillQueryParams params = new ChargeBillQueryParams();
        params.setBillDateBeginTime("2024-06-01 00:00:00");
        params.setBillDateEndTime("2024-06-25 23:59:59");
        params.setVehicleBelongBussCode("");
        params.setVehicleBelongBussAccountCityCode("");

        params.setType(2);
        RestResponse restResponse = chargeBillController.queryByPage(params);
        System.out.println(JSON.toJSON(restResponse));
    }

    @Test
    public void queryCustomerBillByPage() {
        ChargeBillQueryParams params = new ChargeBillQueryParams();
        params.setBillDateBeginTime("2024-01-01 00:00:00");
        params.setBillDateEndTime("2025-01-01 00:00:00");
//        params.setCompanyId(1509);

        RestResponse restResponse = chargeBillController.queryCustomerBillByPage(params);
        System.out.println(JSON.toJSON(restResponse));
    }

    @Test
    public void queryByExport() {
        ChargeBillQueryParams params = new ChargeBillQueryParams();
//        params.setOrderIds("1,2");
        params.setBillDateBeginTime("2024-06-08 00:00:00");
        params.setBillDateEndTime("2024-06-14 23:59:59");
        params.setVehicleBelongBussCode("");
        params.setVehicleBelongBussAccountCityCode("");
//        params.setCompanyId(1509);
        RestResponse restResponse = chargeBillController.queryByExport(params);
        System.out.println(JSON.toJSON(restResponse));
    }

    public static void main(String[] args) {
        int restErrCode = MrCarOrderErrorCode.GET_VEHICLE_INFO_ERROR;
        String s = RestErrorCode.renderMsg(restErrCode, new Object[0]);

        System.out.println(s);

        RestErrorException restException = ExceptionFactory.createRestException(MrCarOrderErrorCode.GET_VEHICLE_INFO_ERROR);
        System.out.println(restException);
    }

}