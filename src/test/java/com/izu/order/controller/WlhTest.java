package com.izu.order.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.izu.business.util.StockOccupyUtil;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.order.controller.charge.ChargeOrderController;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.checkerframework.checker.units.qual.A;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

@SpringBootTest
public class WlhTest {
    @Autowired
    private ChargeOrderController controller;

    @Test
    public void test() {
        String json ="{\"chargeDetailList\":[{\"detailElecMoney\":11.4100,\"detailEndTime\":1747365410000,\"detailPower\":11.413,\"detailServiceMoney\":11.4100,\"detailStartTime\":1747364747000,\"elecPrice\":1.00000,\"servicePrice\":1.00000}],\"chargeStatus\":\"0\",\"connectorId\":\"1110190000000902001\",\"endTime\":\"2025-05-16 11:16:50\",\"startChargeSeq\":\"80136551X202505161105590001\",\"startTime\":\"2025-05-16 11:05:47\",\"stopReason\":135,\"totalElecMoney\":11.41,\"totalMoney\":11.52,\"totalPower\":11.413,\"totalServiceMoney\":11.41}";


        RestResponse restResponse = controller.chargeStoppedCallBack(null,"80136551X202505161105590001");

//        QueryChargeOrderInfoDTO queryChargeOrderInfoDTO = JSON.parseObject(json, QueryChargeOrderInfoDTO.class);
//        RestResponse restResponse = controller.chargeFinishedCallBack(queryChargeOrderInfoDTO);


        System.out.println(JSON.toJSONString(restResponse));

    }


    public static void main(String[] args) throws IOException {
        String string = IOUtils.toString(Files.newInputStream(Paths.get("C:\\Users\\<USER>\\Desktop\\permissiontRE.txt")));
        RestResponse restResponse = JSONObject.parseObject(string, RestResponse.class);
        Object data = restResponse.getData();
        String dataStr = JSON.toJSONString(data);
        JSONArray objects = JSONArray.parseArray(dataStr);

        String fullName = "";
        ArrayList<ChildPermission> list =new ArrayList<>();
        findChild(objects, fullName,list);
        System.out.println( list.size() );

        EasyExcel.write("C:\\Users\\<USER>\\Desktop\\permission.xlsx")
                .sheet(0)
                .head(ChildPermission.class)
                .doWrite(list);

    }



   public static void findChild( JSONArray objects, String fName, ArrayList<ChildPermission> list){
       for (int i = 0; i < objects.size(); i++) {
           JSONObject jsonObject = objects.getJSONObject(i);
           // 是否有效
           String valid  = jsonObject.getBoolean("valid") ? "是" : "否";
           // 子项
           JSONArray childPermissions = jsonObject.getJSONArray("childPermissions");
           // 名字
           String permissionName = jsonObject.getString("permissionName");
           //菜单连接 url
           String menuUrl = jsonObject.getString("menuUrl");

           String fullName =fName +"->"+ permissionName;
           if (CollectionUtils.isNotEmpty(childPermissions)){
               findChild(childPermissions, fullName,list);
           }else {
               ChildPermission childPermission = new ChildPermission(valid, fullName, menuUrl);
               list.add(childPermission);
           }
       }
   }
}

@Data
class ChildPermission{
    private String valid;
    private String name;
    private String menuUrl;

    public ChildPermission(String valid, String name, String menuUrl) {
        this.valid = valid;
        this.name = name;
        this.menuUrl = menuUrl;
    }
}
