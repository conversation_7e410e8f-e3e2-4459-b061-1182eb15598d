package com.izu.order.controller;


import com.izu.mrcar.order.dto.third.shouqiyueche.req.OrdinaryInfoReqDTO;
import com.izu.mrcar.order.dto.third.shouqiyueche.req.OrdinaryMakeOrderInfoReqDTO;
import com.izu.mrcar.order.dto.third.shouqiyueche.resp.CityInfoRespDTO;
import com.izu.mrcar.order.dto.third.shouqiyueche.resp.GradeInfoRespDTO;
import com.izu.mrcar.order.dto.third.shouqiyueche.resp.OrdinaryInfoRespDTO;
import com.izu.mrcar.order.dto.third.shouqiyueche.resp.OrdinaryMakeOrderInfoRespDTO;
import com.izu.order.service.shouQiYueChe.ShouQiYueCheOrderInfoService;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;


@SpringBootTest
public class hhdTest {

    private static final Logger log = LoggerFactory.getLogger(hhdTest.class);
    @Resource
    private ShouQiYueCheOrderInfoService shouQiYueCheOrderInfoService;

    @Test
    public void test(){
        List<CityInfoRespDTO> cityInfoRespDTOList = shouQiYueCheOrderInfoService.queryCityList();
        System.out.println(cityInfoRespDTOList);
    }

    @Test
    public void test1(){
        List<GradeInfoRespDTO> gradeInfoRespDTOList = shouQiYueCheOrderInfoService.queryGradeList(81);
        System.out.println(gradeInfoRespDTOList);
    }

    @Test
    public void test3(){
        OrdinaryInfoReqDTO reqDTO  = new OrdinaryInfoReqDTO();
        long timestampSeconds = System.currentTimeMillis() / 1000;
        reqDTO.setBookingDate(Integer.valueOf(String.valueOf(timestampSeconds)));
        reqDTO.setCityId(44);
        reqDTO.setBookingStartPointLo("116.35348599999995");
        reqDTO.setBookingStartPointLa("39.954468073838704");
        reqDTO.setBookingEndPointLo("116.35987091");
        reqDTO.setBookingEndPointLa("39.95085526");
        reqDTO.setGroups("34:5");
        reqDTO.setBookingStartAddr("海淀区交通大学路8号");
        reqDTO.setBookingEndAddr("北京北京北站");
        List<OrdinaryInfoRespDTO> ordinaryInfoRespDTOS = shouQiYueCheOrderInfoService.queryOrdinaryPriceInfo(reqDTO);
        System.out.println(ordinaryInfoRespDTOS);
    }

    @Test
    public void test4() throws UnsupportedEncodingException {
        OrdinaryMakeOrderInfoReqDTO reqDTO = new OrdinaryMakeOrderInfoReqDTO();
        long timestamp = System.currentTimeMillis() / 1000;
        reqDTO.setBookingDate(String.valueOf(timestamp));
        reqDTO.setRiderPhone("18518915603");
        reqDTO.setBookingStartPointLo("116.35348599999995");
        reqDTO.setBookingStartPointLa("39.954468073838704");
        reqDTO.setBookingEndPointLo("116.35987091");
        reqDTO.setBookingEndPointLa("39.95085526");
        reqDTO.setBookingEndPointLo("116.35987091");
        reqDTO.setBookingEndPointLa("39.95085526");
        reqDTO.setCityId(String.valueOf(44));
        reqDTO.setGroupIds("34");
        reqDTO.setEstimatedAmount("6");
        reqDTO.setPartnerOrderNo("DDH20250624002");
        reqDTO.setPriceToken("PT685a5ae75397ac0009777cb0");
        reqDTO.setRiderName(URLEncoder.encode("小白", StandardCharsets.UTF_8.name()));
        OrdinaryMakeOrderInfoRespDTO ordinaryMakeOrderInfoRespDTO = shouQiYueCheOrderInfoService.makeOrdinaryOrderInfo(reqDTO);
        System.out.println(ordinaryMakeOrderInfoRespDTO);
    }
}
