package com.izu.mrcar;

import com.MrcarApplication;
import com.izu.mrcar.controller.asset.CarInfoImportController;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
//@RunWith(SpringRunner.class)
@SpringBootTest(classes = {MrcarApplication.class})
public class WlhTest {

    @Autowired
   private  CarInfoImportController carInfoImportController;

    @Test
    public void test() throws Exception {
        carInfoImportController.download(new MockHttpServletResponse());
    }

}

