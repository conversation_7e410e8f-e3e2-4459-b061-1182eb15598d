package com.izu.user.job;

import com.xxl.job.core.biz.model.ReturnT;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
class ChargeOperatorSyncJobTest {

    @Autowired
    private ChargeOperatorSyncJob chargeOperatorSyncJob;

    @Test
    public void test(){
        try {
            ReturnT<String> execute = chargeOperatorSyncJob.execute("");
            System.out.println(execute.toString());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

}