package com.izu.user.service.company;

import com.izu.user.dto.company.ExpenditureSupplierInfoDTO;
import com.izu.user.dto.company.ListExpenditureSupplierReDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 支出单供应商服务测试类
 * <AUTHOR>
 * @date 2025/6/26 18:00
 */
@ExtendWith(MockitoExtension.class)
class ExpenditureSupplierServiceTest {

    @InjectMocks
    private ExpenditureSupplierService expenditureSupplierService;

    @BeforeEach
    void setUp() {
        // 初始化设置
    }

    @Test
    void testGetSupplierList() {
        // 准备测试数据
        ListExpenditureSupplierReDTO reqDTO = new ListExpenditureSupplierReDTO();
        
        // 执行测试
        List<ExpenditureSupplierInfoDTO> result = expenditureSupplierService.getSupplierList(reqDTO);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        
        // 验证第一个供应商
        ExpenditureSupplierInfoDTO supplier1 = result.get(0);
        assertEquals("GYS-20210125-1436", supplier1.getSupplierCode());
        assertEquals("北京首汽智行科技有限公司", supplier1.getSupplierName());
        assertEquals("1", supplier1.getMappingCode());
        
        // 验证第二个供应商
        ExpenditureSupplierInfoDTO supplier2 = result.get(1);
        assertEquals("GYS-20250108-4931", supplier2.getSupplierCode());
        assertEquals("国网智慧车联网技术有限公司", supplier2.getSupplierName());
        assertEquals("2", supplier2.getMappingCode());
    }

    @Test
    void testGetSupplierListWithNullRequest() {
        // 测试空请求参数
        List<ExpenditureSupplierInfoDTO> result = expenditureSupplierService.getSupplierList(null);
        
        // 验证结果 - 即使请求为空，也应该返回固定的供应商列表
        assertNotNull(result);
        assertEquals(2, result.size());
        
        // 验证数据内容
        ExpenditureSupplierInfoDTO supplier1 = result.get(0);
        assertEquals("GYS-20210125-1436", supplier1.getSupplierCode());
        assertEquals("北京首汽智行科技有限公司", supplier1.getSupplierName());
        assertEquals("1", supplier1.getMappingCode());
        
        ExpenditureSupplierInfoDTO supplier2 = result.get(1);
        assertEquals("GYS-20250108-4931", supplier2.getSupplierCode());
        assertEquals("国网智慧车联网技术有限公司", supplier2.getSupplierName());
        assertEquals("2", supplier2.getMappingCode());
    }

    @Test
    void testGetSupplierListConsistency() {
        // 测试多次调用结果的一致性
        ListExpenditureSupplierReDTO reqDTO = new ListExpenditureSupplierReDTO();

        List<ExpenditureSupplierInfoDTO> result1 = expenditureSupplierService.getSupplierList(reqDTO);
        List<ExpenditureSupplierInfoDTO> result2 = expenditureSupplierService.getSupplierList(reqDTO);

        // 验证两次调用结果一致
        assertNotNull(result1);
        assertNotNull(result2);
        assertEquals(result1.size(), result2.size());

        for (int i = 0; i < result1.size(); i++) {
            ExpenditureSupplierInfoDTO supplier1 = result1.get(i);
            ExpenditureSupplierInfoDTO supplier2 = result2.get(i);

            assertEquals(supplier1.getSupplierCode(), supplier2.getSupplierCode());
            assertEquals(supplier1.getSupplierName(), supplier2.getSupplierName());
            assertEquals(supplier1.getMappingCode(), supplier2.getMappingCode());
        }
    }

    @Test
    void testGetSupplierListWithSupplierNameFilter() {
        // 测试根据供应商名称模糊匹配
        ListExpenditureSupplierReDTO reqDTO = new ListExpenditureSupplierReDTO();
        reqDTO.setSupplierName("首汽");

        List<ExpenditureSupplierInfoDTO> result = expenditureSupplierService.getSupplierList(reqDTO);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("GYS-20210125-1436", result.get(0).getSupplierCode());
        assertEquals("北京首汽智行科技有限公司", result.get(0).getSupplierName());
        assertEquals("1", result.get(0).getMappingCode());
    }

    @Test
    void testGetSupplierListWithSupplierCodeFilter() {
        // 测试根据供应商编码精确匹配
        ListExpenditureSupplierReDTO reqDTO = new ListExpenditureSupplierReDTO();
        reqDTO.setSupplierCode("GYS-20250108-4931");

        List<ExpenditureSupplierInfoDTO> result = expenditureSupplierService.getSupplierList(reqDTO);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("GYS-20250108-4931", result.get(0).getSupplierCode());
        assertEquals("国网智慧车联网技术有限公司", result.get(0).getSupplierName());
        assertEquals("2", result.get(0).getMappingCode());
    }

    @Test
    void testGetSupplierListWithMappingCodeFilter() {
        // 测试根据映射编码精确匹配
        ListExpenditureSupplierReDTO reqDTO = new ListExpenditureSupplierReDTO();
        reqDTO.setMappingCode("1");

        List<ExpenditureSupplierInfoDTO> result = expenditureSupplierService.getSupplierList(reqDTO);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("GYS-20210125-1436", result.get(0).getSupplierCode());
        assertEquals("北京首汽智行科技有限公司", result.get(0).getSupplierName());
        assertEquals("1", result.get(0).getMappingCode());
    }

    @Test
    void testGetSupplierListWithNoMatchingName() {
        // 测试没有匹配的供应商名称
        ListExpenditureSupplierReDTO reqDTO = new ListExpenditureSupplierReDTO();
        reqDTO.setSupplierName("不存在的供应商");

        List<ExpenditureSupplierInfoDTO> result = expenditureSupplierService.getSupplierList(reqDTO);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    @Test
    void testGetSupplierListWithPartialNameMatch() {
        // 测试部分名称匹配
        ListExpenditureSupplierReDTO reqDTO = new ListExpenditureSupplierReDTO();
        reqDTO.setSupplierName("技术");

        List<ExpenditureSupplierInfoDTO> result = expenditureSupplierService.getSupplierList(reqDTO);

        // 验证结果 - 应该匹配到"国网智慧车联网技术有限公司"
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("GYS-20250108-4931", result.get(0).getSupplierCode());
        assertEquals("国网智慧车联网技术有限公司", result.get(0).getSupplierName());
        assertEquals("2", result.get(0).getMappingCode());
    }
}
