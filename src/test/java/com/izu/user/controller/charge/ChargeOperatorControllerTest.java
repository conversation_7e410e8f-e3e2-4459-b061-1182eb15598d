package com.izu.user.controller.charge;

import com.alibaba.fastjson2.JSON;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.user.dto.common.PageParamDTO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class ChargeOperatorControllerTest {

    @Autowired
    private ChargeOperatorController chargeOperatorController;

    @Test
    public void test() {
        RestResponse restResponse = chargeOperatorController.queryUsableOperator();
        System.out.println("queryUsableOperator：" + JSON.toJSON(restResponse));

        restResponse = chargeOperatorController.queryAllOperator();
        System.out.println("queryAllOperator：" + JSON.toJSON(restResponse));


        PageParamDTO param = new PageParamDTO();
        restResponse = chargeOperatorController.queryByPage(param);
        System.out.println("queryByPage：" + JSON.toJSON(restResponse));
    }

    @Test
    void save() {
    }

    @Test
    void update() {
    }

    @Test
    void queryUsableOperator() {
        RestResponse restResponse = chargeOperatorController.queryUsableOperator();
        System.out.println("queryUsableOperator：{}" + JSON.toJSON(restResponse));
    }

    @Test
    void queryAllOperator() {
        RestResponse restResponse = chargeOperatorController.queryAllOperator();
        System.out.println(JSON.toJSON(restResponse));
    }

    @Test
    void queryByPage() {
        PageParamDTO param = new PageParamDTO();
        RestResponse restResponse = chargeOperatorController.queryByPage(param);
        System.out.println(JSON.toJSON(restResponse));
    }

    @Test
    void syncChargeOperator() {
        RestResponse restResponse = chargeOperatorController.syncChargeOperator();
        System.out.println(JSON.toJSON(restResponse));
    }

    @Test
    void testQueryDetail() {
        RestResponse detail = chargeOperatorController.detail(3);
        System.out.println(JSON.toJSON(detail));
    }

    @Test
    void testQueryChargeOperatorByChannelId() {
        RestResponse restResponse = chargeOperatorController.queryChargeOperatorByChannelId("313744932");
        System.out.println(JSON.toJSON(restResponse));
    }


}