import cn.hutool.core.date.DateUtil;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.izu.framework.dingding.DefaultDingTalkIZUClient;
import com.izu.order.entity.mrcar.OrderInfo;
import com.izu.utils.DateUtils;
import org.apache.commons.codec.binary.Base64;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Date;

public class OrderTest {

//    public static void main(String[] args) throws Exception {
//        BigDecimal tripMileageTotal=new BigDecimal("101.12");
//        OrderInfo orderInfo=new OrderInfo();
//        orderInfo.setFactEndDate(DateUtil.format2Date("2023-03-22 17:18:09",DateUtil.TIME_FORMAT));
//        orderInfo.setFactStartDate(DateUtil.format2Date("2023-03-22 17:14:09",DateUtil.TIME_FORMAT));
//        Long s= DateUtil.diffDateTime(orderInfo.getFactEndDate(),orderInfo.getFactStartDate());
//        System.out.println("111--"+s);
//        BigDecimal hours= new BigDecimal(DateUtil.diffDateTime(orderInfo.getFactEndDate(),orderInfo.getFactStartDate())).divide(new BigDecimal(3600),2,BigDecimal.ROUND_HALF_UP);
//
//        if(hours.compareTo(BigDecimal.ZERO)==0){
//            System.out.println("000");
//        }else{
//            BigDecimal speed=tripMileageTotal.divide(hours,2,BigDecimal.ROUND_HALF_UP);
//            System.out.println("hours"+hours+"speed="+speed);
//            if(speed.compareTo(new BigDecimal("100"))==1){
//                System.out.println("超速");
//            }else{
//                System.out.println("没");
//            }
//        }
//
//        //msg();
//
//
//    }

    public static void  noticeMessage() throws Exception {
        String noticeUrl="https://oapi.dingtalk.com/robot/send?access_token=d316e49f8395be1aa3a8394c641e612492480579ef7d800e256ddb2c3ea22614";
        String noticeUrl2="https://oapi.dingtalk.com/robot/send?access_token=d316e49f8395be1aa3a8394c641e612492480579ef7d800e256ddb2c3ea22614";
        String noticeUrl3="https://oapi.dingtalk.com/robot/send?access_token=d2d2c6512bb5f8da3df24b3e30cd949cca767bb0914ecb41b77dcf53a5ebbe28";
        OapiRobotSendRequest request = new OapiRobotSendRequest();
        request.setMsgtype("text");
        OapiRobotSendRequest.Text text = new OapiRobotSendRequest.Text();
        text.setContent("超里程报警");
        request.setText(text);
        OapiRobotSendRequest.At at = new OapiRobotSendRequest.At();
        at.setAtMobiles(Arrays.asList("17835395849"));
        request.setAt(at);
        DefaultDingTalkIZUClient.execute(noticeUrl3,request);
    }

    public static void msg() throws Exception {
        Long timestamp = System.currentTimeMillis();
        String secret = "SEC7783090acb61f84f1d419cb59d43b33f96a1d5988d59a22cf72503af5c4c53a7";
        String stringToSign = timestamp + "\n" + secret;
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256"));
        byte[] signData = mac.doFinal(stringToSign.getBytes(StandardCharsets.UTF_8));
        String sign = URLEncoder.encode(new String(Base64.encodeBase64(signData)), "UTF-8");
        String noticeUrl3="https://oapi.dingtalk.com/robot/send?access_token=7529e1fc52834150b68ca9af813b97795bea7b5a4dcf44e392a3f5bc80ef9d9a";
        OapiRobotSendRequest request = new OapiRobotSendRequest();
        request.setMsgtype("text");
        OapiRobotSendRequest.Text text = new OapiRobotSendRequest.Text();
        text.setContent("超里程报警,请关注");
        request.setText(text);
        OapiRobotSendRequest.At at = new OapiRobotSendRequest.At();
        //at.setAtMobiles(Lists.newArrayList("17835395849"));
        at.setIsAtAll(Boolean.TRUE.toString());
        request.setAt(at);
        DefaultDingTalkIZUClient.execute(noticeUrl3 + "&timestamp=" + timestamp + "&sign=" + sign,request);

    }

    public static void main(String[] args) {
        Date one=DateUtil.parse("2023-03-22 00:00:00", DateUtils.YYYY_MM_DD_HH_mm_DD);
        Date two=DateUtil.parse("2023-03-22 00:00:00", DateUtils.YYYY_MM_DD_HH_mm_DD);


        long day= DateUtil.betweenDay(one,two,true);
        System.out.println("day="+day);
        Date deliveryTime = DateUtil.beginOfDay(one);
        System.out.println("deliveryTime="+DateUtil.formatDateTime(deliveryTime));
        System.out.println(one.before(two));
    }
}
