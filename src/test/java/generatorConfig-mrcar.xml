<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN" "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
	<context id="DB2Tables"  defaultModelType="flat"  targetRuntime="MyBatis3">
		<!-- Mybatis generator mapper文件重新生成不会覆盖原文件，为了解决此问题 -->
		<plugin type="mysqlgenerate.OverIsMergeablePlugin"></plugin>

		<!-- 是否去除自动生成的注释 -->
		<commentGenerator type="com.izu.framework.database.tools.CustomFromDBCommentGenerator">
			<property name="suppressAllComments" value="false"/>
			<property name="suppressDate" value="true"/>
		</commentGenerator>

		<!-- 数据库连接信息 -->
		<jdbcConnection driverClass="com.mysql.jdbc.Driver"
						connectionURL="***************************************************************************************************************"
						userId="mrcar-base-dev-mysql-rw" password="LjqPUH093LEddZYG">
		</jdbcConnection>
		<javaTypeResolver>
			<property name="forceBigDecimals" value="true"/>
		</javaTypeResolver>

		<!-- po实体类代码位置-->
		<javaModelGenerator targetPackage="com.izu.business.entity" targetProject="C:\WORK\Mapper">
		<!--<javaModelGenerator targetPackage="com.izu.business.entity" targetProject="/Users/<USER>/IdeaProjects/business/src/main/java">-->
					<property name="enableSubPackages" value="true" />
					<property name="trimStrings" value="true" />
		</javaModelGenerator>
		<!-- sql代码的xml映射文件位置 -->
		<sqlMapGenerator targetPackage="mapper" targetProject="C:\WORK\Mapper">
		<!--<sqlMapGenerator targetPackage="mapper" targetProject="/Users/<USER>/IdeaProjects/business/src/main/resources">-->
			<property name="enableSubPackages" value="true" />
		</sqlMapGenerator>

		<!-- mapper接口位置 -->
		<javaClientGenerator type="XMLMAPPER" targetPackage="mapper" targetProject="C:\WORK\Mapper">
		<!--<javaClientGenerator type="XMLMAPPER" targetPackage="mapper" targetProject="/Users/<USER>/IdeaProjects/business/src/main/java">-->
			<property name="enableSubPackages" value="true" />
		</javaClientGenerator>

		<!-- 需要自动生成的表 -->
		<!--<table tableName="price_policy_basic_config" domainObjectName="PricePolicyBasicConfig"
			   enableSelectByExample="false" enableDeleteByExample="false"
			   enableCountByExample="false" enableUpdateByExample="false">
			<property name="useActualColumnNames" value="false" />
			<generatedKey column="id" sqlStatement="MySql" identity="true" />
		</table>-->
		<!--<table tableName="stock_usage_rerecord" domainObjectName="StockUsageRerecord"
			   enableSelectByExample="false" enableDeleteByExample="false"
			   enableCountByExample="false" enableUpdateByExample="false">
			<property name="useActualColumnNames" value="false" />
			<generatedKey column="id" sqlStatement="MySql" identity="true" />
		</table>-->
		<!--<table tableName="contract_auth_account" domainObjectName="ContractAuthAccount"-->
			   <!--enableSelectByExample="true" enableDeleteByExample="true"-->
			   <!--enableCountByExample="true" enableUpdateByExample="true">-->
			<!--<property name="useActualColumnNames" value="false" />-->
			<!--<generatedKey column="id" sqlStatement="MySql" identity="true" />-->
		<!--</table>-->
<!--		<table tableName="clue_update_record" domainObjectName="ClueUpdateRecord"-->
<!--			   enableSelectByExample="false" enableDeleteByExample="false"-->
<!--			   enableCountByExample="false" enableUpdateByExample="false">-->
<!--			<property name="useActualColumnNames" value="false" />-->
<!--			<generatedKey column="id" sqlStatement="MySql" identity="true" />-->
<!--		</table>-->
<!--		<table tableName="clue_follow_record" domainObjectName="ClueFollowRecord"-->
<!--			   enableSelectByExample="false" enableDeleteByExample="false"-->
<!--			   enableCountByExample="false" enableUpdateByExample="false">-->
<!--			<property name="useActualColumnNames" value="false" />-->
<!--			<generatedKey column="id" sqlStatement="MySql" identity="true" />-->
<!--		</table>-->
<!--		<table tableName="clue_company" domainObjectName="ClueCompany"-->
<!--			   enableSelectByExample="false" enableDeleteByExample="false"-->
<!--			   enableCountByExample="false" enableUpdateByExample="false">-->
<!--			<property name="useActualColumnNames" value="false" />-->
<!--			<generatedKey column="clueId" sqlStatement="MySql" identity="true" />-->
<!--		</table>-->
		<table tableName="accident_insure_orders" domainObjectName="AccidentInsureOrders"
			   enableSelectByExample="false" enableDeleteByExample="false"
			   enableCountByExample="false" enableUpdateByExample="false">
			<property name="useActualColumnNames" value="false" />
			<generatedKey column="insure_id" sqlStatement="MySql" identity="true" />
		</table>
		<!--<table tableName="statistics_system_view" domainObjectName="StatisticsSystemView"
			   enableSelectByExample="false" enableDeleteByExample="false"
			   enableCountByExample="false" enableUpdateByExample="false">
			<property name="useActualColumnNames" value="false" />
			<generatedKey column="id" sqlStatement="MySql" identity="true" />
		</table>-->
		<!--
		<table tableName="statistics_business" domainObjectName="StatisticsBusiness"
			   enableSelectByExample="false" enableDeleteByExample="false"
			   enableCountByExample="false" enableUpdateByExample="false">
			<property name="useActualColumnNames" value="false" />
			<generatedKey column="id" sqlStatement="MySql" identity="true" />
		</table>
		<table tableName="apply_pool" domainObjectName="ApplyPool"
			   enableSelectByExample="false" enableDeleteByExample="false"
			   enableCountByExample="false" enableUpdateByExample="false">
			<property name="useActualColumnNames" value="false" />
			<generatedKey column="apply_id" sqlStatement="MySql" identity="true" />
		</table>
		<table tableName="statis_vehicle_flow" domainObjectName="StatisVehicleFlow"
			   enableSelectByExample="false" enableDeleteByExample="false"
			   enableCountByExample="false" enableUpdateByExample="false">
			<property name="useActualColumnNames" value="false" />
			<generatedKey column="id" sqlStatement="MySql" identity="true" />
		</table>
		<table tableName="business_vehicle_base" domainObjectName="VehicleBase"
			   enableSelectByExample="false" enableDeleteByExample="false"
			   enableCountByExample="false" enableUpdateByExample="false">
			<property name="useActualColumnNames" value="false" />
			<generatedKey column="vehicle_id" sqlStatement="MySql" identity="true" />
		</table>
		<table tableName="statistics_customer_trend" domainObjectName="StatisticsCustomerTrend"
			   enableSelectByExample="false" enableDeleteByExample="false"
			   enableCountByExample="false" enableUpdateByExample="false">
			<property name="useActualColumnNames" value="false" />
			<generatedKey column="id" sqlStatement="MySql" identity="true" />
		</table>
		<table tableName="statistics_vehicle_usage" domainObjectName="StatisticsVehicleUsage"
			   enableSelectByExample="false" enableDeleteByExample="false"
			   enableCountByExample="false" enableUpdateByExample="false">
			<property name="useActualColumnNames" value="false" />
			<generatedKey column="id" sqlStatement="MySql" identity="true" />
		</table>
		-->
	</context>
</generatorConfiguration>
