package com.izu.business.service.czcontract.bussContract;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.izu.business.config.errorcode.BusinessErrorCode;

import com.izu.business.dto.ContractAuthAccountDTO;
import com.izu.business.dto.czcontract.input.AuthAccountContractInfoInputDTO;
import com.izu.business.dto.czcontract.output.AuthAccountContractDetailDTO;
import com.izu.business.dto.czcontract.output.AuthAccountContractInfoOutputDTO;
import com.izu.business.dto.provider.input.*;
import com.izu.business.dto.provider.output.BussContractOpeDetailDTO;
import com.izu.business.entity.*;
import com.izu.crm.dto.output.contract.MrCarContractOtherSubjectDTO;
import com.izu.crm.enums.MrCarContractChargeEnum;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.user.enums.AccountUseMethodEnum;
import mapper.ContractEquipmentRentPurchaseRecordMapper;
import mapper.ex.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class AuthAccountContractService {

    @Autowired
    private ContractAuthAccountExMapper contractAuthAccountExMapper;
    @Autowired
    private LeaseContractFileExMapper leaseContractFileExMapper;
    @Resource
    private ContractOtherSubjectExMapper contractOtherSubjectExMapper;
    @Resource
    private ContractChargeItemDetailExMapper chargeItemDetailExMapper;
    @Resource
    private ContractEquipmentRentPurchaseRecordExMapper purchaseRecordExMapper;

    public PageDTO getList(AuthAccountContractInfoInputDTO param) {
        Long total = 0L;
        List<AuthAccountContractInfoOutputDTO> list = new ArrayList<>();
        Page p = PageHelper.startPage(param.getPage(), param.getPageSize(), true);
        try {
            list = contractAuthAccountExMapper.getList(param);
            total = p.getTotal();
            if (total > 0) {
                List<String> contractCodeList = new ArrayList<>();
                list.forEach(item -> {
                    contractCodeList.add(item.getContractCode());
                });
                //文件信息
                List<LeaseContractFile> fileList = leaseContractFileExMapper.selectByContractCodeList(contractCodeList);
                list.forEach(w -> {
                    w.setAccountUseMethodStr(AccountUseMethodEnum.getMessageByType(w.getAccountUseMethod()));
                    if (CollectionUtils.isNotEmpty(fileList)) {
                        List<LeaseContractFile> fileEachList = fileList.stream().filter(o -> o.getOperateCode().equals(w.getContractCode())).collect(Collectors.toList());
                        w.setBackUrlList(BeanUtil.copyList(fileEachList, BussContractOpeDetailDTO.FileRecordOpe.class));
                    }
                });

            }
        } finally {
            PageHelper.clearPage();
        }
        return new PageDTO(param.getPage(), param.getPageSize(), total, list);
    }

    public RestResponse getDetail(Integer id) {
        ContractAuthAccount contractAuthAccount = contractAuthAccountExMapper.selectByPrimaryKey(id);
        if (contractAuthAccount == null) {
            return RestResponse.fail(BusinessErrorCode.INFO_NOT_EXISTS);
        }
        AuthAccountContractDetailDTO authAccountContractDetailDTO = BeanUtil.copyObject(contractAuthAccount, AuthAccountContractDetailDTO.class);
        authAccountContractDetailDTO.setAccountUseMethodStr(AccountUseMethodEnum.getMessageByType(authAccountContractDetailDTO.getAccountUseMethod()));
        // 查询附件信息
        List<LeaseContractFile> leaseContractFiles = leaseContractFileExMapper.selectByContractCode(contractAuthAccount.getContractCode(), null);
        authAccountContractDetailDTO.setAccountContractfileList(BeanUtil.copyList(leaseContractFiles, AuthAccountContractDetailDTO.AuthAccountContractfile.class));
        return RestResponse.success(authAccountContractDetailDTO);
    }


    public RestResponse getDetailByContractCode(String contractCode) {
        if(StringUtils.isEmpty(contractCode)){
            return RestResponse.success(null);
        }
        ContractAuthAccount contractAuthAccount = contractAuthAccountExMapper.getContractAuthAccount(contractCode);
        if (contractAuthAccount == null) {
            return RestResponse.success(null);
        }
        AuthAccountContractDetailDTO authAccountContractDetailDTO = BeanUtil.copyObject(contractAuthAccount, AuthAccountContractDetailDTO.class);
        return RestResponse.success(authAccountContractDetailDTO);
    }



    public RestResponse getDetailByContractCodes(String contractCodes) {
        if(StringUtils.isEmpty(contractCodes)){
            return RestResponse.success(Lists.newArrayList());
        }
        List<String> list = Arrays.asList(contractCodes.split(","));
        List<ContractAuthAccount> contractAuthAccountList = contractAuthAccountExMapper.getContractAuthAccountByCodes(list);
        if (CollectionUtils.isEmpty(contractAuthAccountList)) {
            return RestResponse.success(Lists.newArrayList());
        }
        return RestResponse.success(BeanUtil.copyList(contractAuthAccountList,AuthAccountContractDetailDTO.class));
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean createContractAuthAccount(ContractAuthAccountDTO contractAuthAccountDTO) {
        ContractAuthAccount contractAuthAccount = BeanUtil.copyObject(contractAuthAccountDTO, ContractAuthAccount.class);
        ContractAuthAccount oldContractAuthAccount = contractAuthAccountExMapper.getContractAuthAccount(contractAuthAccountDTO.getContractCode());
        if (oldContractAuthAccount != null) {
            contractAuthAccount.setId(oldContractAuthAccount.getId());
            contractAuthAccount.setUpdateTime(new Date());
            contractAuthAccountExMapper.updateByPrimaryKeySelective(contractAuthAccount);
        } else {
            contractAuthAccountExMapper.insertSelective(contractAuthAccount);
        }
        if(oldContractAuthAccount!=null){
            contractOtherSubjectExMapper.deleteByContractCode(contractAuthAccount.getContractCode());
            chargeItemDetailExMapper.deleteByContractCode(contractAuthAccount.getContractCode());
        }
        ContractOtherSubjectInputDTO signingSubjectInfo = contractAuthAccountDTO.getSigningSubjectInfo();
        ContractOtherSubject signingSubject = BeanUtil.copyObject(signingSubjectInfo, ContractOtherSubject.class);
        signingSubject.setContractCode(contractAuthAccount.getContractCode());
        signingSubject.setOperateType(MrCarContractChargeEnum.SubjectOperateTypeEnum.SignSubject.getCode());
        contractOtherSubjectExMapper.insertSelective(signingSubject);

        //保存合同相对方信息
        ContractOtherSubjectInputDTO customerInfo = contractAuthAccountDTO.getCustomerInfo();
        ContractOtherSubject customer = BeanUtil.copyObject(customerInfo, ContractOtherSubject.class);
        customer.setContractCode(contractAuthAccount.getContractCode());
        customer.setOperateType(MrCarContractChargeEnum.SubjectOperateTypeEnum.Customer.getCode());
        contractOtherSubjectExMapper.insertSelective(customer);

        //保存费用明细
        this.saveChargeItemDetail(contractAuthAccount.getId(),contractAuthAccount.getContractCode(),contractAuthAccountDTO);
        return Boolean.TRUE;
    }

    private void saveChargeItemDetail(Integer contractId, String contractCode, ContractAuthAccountDTO createParam) {
        //系统使用费
        ContractChargeItemInputDTO systemUseChargeItem = createParam.getSystemUseChargeItem();
        if (systemUseChargeItem != null) {
            ContractChargeItemDetail systemUseChargeItemDetail = BeanUtil.copyObject(systemUseChargeItem, ContractChargeItemDetail.class);
            systemUseChargeItemDetail.setChargeItem(MrCarContractChargeEnum.MrCarContractChargeItemEnum.SYSTEM_USAGE.getChargeItemCode().byteValue());
            systemUseChargeItemDetail.setPaymentMethod(systemUseChargeItem.getPaymentMethod()!=null?systemUseChargeItem.getPaymentMethod().byteValue():null);
            systemUseChargeItemDetail.setBillingMethod(systemUseChargeItem.getBillingMethod()!=null?systemUseChargeItem.getBillingMethod().byteValue():null);
            systemUseChargeItemDetail.setPaymentType(systemUseChargeItem.getPaymentType()!=null?systemUseChargeItem.getPaymentType().byteValue():null);
            systemUseChargeItemDetail.setContractCode(contractCode);
            systemUseChargeItemDetail.setContractId(contractId);
            systemUseChargeItemDetail.setIncludeHardwareCost(systemUseChargeItem.getIncludeHardwareCost()!=null?systemUseChargeItem.getIncludeHardwareCost().byteValue():null);
            List<ContractSystemUseFeeInputDTO> systemUseFeeList = systemUseChargeItem.getSystemUseFeeList();
            systemUseChargeItemDetail.setChargeItemInfo(JSONUtil.toJsonStr(systemUseFeeList));
            chargeItemDetailExMapper.insertSelective(systemUseChargeItemDetail);
        }
        //硬件采购/租赁相关费用
        ContractChargeItemInputDTO hardwarePurchaseChargeItem = createParam.getHardwarePurchase();
        if (hardwarePurchaseChargeItem != null) {
            ContractChargeItemDetail hardwarePurchaseChargeItemDetail = BeanUtil.copyObject(hardwarePurchaseChargeItem, ContractChargeItemDetail.class);
            hardwarePurchaseChargeItemDetail.setChargeItem(MrCarContractChargeEnum.MrCarContractChargeItemEnum.HARDWARE_PURCHASE.getChargeItemCode().byteValue());
            hardwarePurchaseChargeItemDetail.setPaymentMethod(hardwarePurchaseChargeItem.getPaymentMethod()!=null?hardwarePurchaseChargeItem.getPaymentMethod().byteValue():null);
            hardwarePurchaseChargeItemDetail.setBillingMethod(hardwarePurchaseChargeItem.getBillingMethod()!=null?hardwarePurchaseChargeItem.getBillingMethod().byteValue():null);
            hardwarePurchaseChargeItemDetail.setPaymentType(hardwarePurchaseChargeItem.getPaymentType()!=null?hardwarePurchaseChargeItem.getPaymentType().byteValue():null);
            hardwarePurchaseChargeItemDetail.setIncludeHardwareCost(hardwarePurchaseChargeItem.getIncludeHardwareCost()!=null?hardwarePurchaseChargeItem.getIncludeHardwareCost().byteValue():null);
            hardwarePurchaseChargeItemDetail.setContractCode(contractCode);
            hardwarePurchaseChargeItemDetail.setContractId(contractId);
            List<ContractHardwarePurchaseFeeInputDTO> hardwarePurchaseFeeList = hardwarePurchaseChargeItem.getHardwarePurchaseFeeList();
            hardwarePurchaseChargeItemDetail.setChargeItemInfo(JSONUtil.toJsonStr(hardwarePurchaseFeeList));
            chargeItemDetailExMapper.insertSelective(hardwarePurchaseChargeItemDetail);
        }
        List<ContractEquipmentFeeInputDTO> equipmentFeeList = createParam.getEquipmentFeeList();
        if(CollectionUtils.isNotEmpty(equipmentFeeList)){
            ContractChargeItemDetail equipmentFeeChargeItemDetail = new ContractChargeItemDetail();
            equipmentFeeChargeItemDetail.setChargeItem(MrCarContractChargeEnum.MrCarContractChargeItemEnum.HARDWARE_PURCHASE.getChargeItemCode().byteValue());
            equipmentFeeChargeItemDetail.setIncludeHardwareCost(Byte.valueOf("1"));
            equipmentFeeChargeItemDetail.setContractCode(contractCode);
            equipmentFeeChargeItemDetail.setContractId(contractId);
            equipmentFeeChargeItemDetail.setChargeItemInfo(JSONUtil.toJsonStr(equipmentFeeList));
            chargeItemDetailExMapper.insertSelective(equipmentFeeChargeItemDetail);
            //先删除后新增
            purchaseRecordExMapper.deleteByContractCode(contractCode);
            //保存租购记录
            equipmentFeeList.forEach(x-> saveEquipmentRentPurchaseRecord(x,createParam));
        }


        //增值服务：维保/违章/充电等
        ContractChargeItemInputDTO valueAddedServiceChargeItem = createParam.getValueAddedService();
        if (valueAddedServiceChargeItem != null) {
            ContractChargeItemDetail valueAddedServiceChargeItemDetail = BeanUtil.copyObject(valueAddedServiceChargeItem, ContractChargeItemDetail.class);
            valueAddedServiceChargeItemDetail.setPaymentMethod(valueAddedServiceChargeItem.getPaymentMethod()!=null?valueAddedServiceChargeItem.getPaymentMethod().byteValue():null);
            valueAddedServiceChargeItemDetail.setBillingMethod(valueAddedServiceChargeItem.getBillingMethod()!=null?valueAddedServiceChargeItem.getBillingMethod().byteValue():null);
            valueAddedServiceChargeItemDetail.setPaymentType(valueAddedServiceChargeItem.getPaymentType()!=null?valueAddedServiceChargeItem.getPaymentType().byteValue():null);
            valueAddedServiceChargeItemDetail.setChargeItem(MrCarContractChargeEnum.MrCarContractChargeItemEnum.VALUE_ADDED_SERVICE.getChargeItemCode().byteValue());
            valueAddedServiceChargeItemDetail.setIncludeHardwareCost(valueAddedServiceChargeItem.getIncludeHardwareCost()!=null?valueAddedServiceChargeItem.getIncludeHardwareCost().byteValue():null);
            valueAddedServiceChargeItemDetail.setContractCode(contractCode);
            valueAddedServiceChargeItemDetail.setContractId(contractId);
            List<ContractValueAddedServiceFeeInputDTO> valueAddedServiceFeeList = valueAddedServiceChargeItem.getValueAddedServiceFeeList();
            valueAddedServiceChargeItemDetail.setChargeItemInfo(JSONUtil.toJsonStr(valueAddedServiceFeeList));
            chargeItemDetailExMapper.insertSelective(valueAddedServiceChargeItemDetail);
        }

        //定制开发服务
        ContractChargeItemInputDTO customDevelopmentChargeItem = createParam.getCustomDevelopment();
        if (customDevelopmentChargeItem != null) {
            ContractChargeItemDetail customDevelopmentChargeItemDetail = BeanUtil.copyObject(customDevelopmentChargeItem, ContractChargeItemDetail.class);
            customDevelopmentChargeItemDetail.setPaymentMethod(customDevelopmentChargeItem.getPaymentMethod()!=null? customDevelopmentChargeItem.getPaymentMethod().byteValue() :null);
            customDevelopmentChargeItemDetail.setBillingMethod(customDevelopmentChargeItem.getBillingMethod()!=null?customDevelopmentChargeItem.getBillingMethod().byteValue():null);
            customDevelopmentChargeItemDetail.setPaymentType(customDevelopmentChargeItem.getPaymentType()!=null?customDevelopmentChargeItem.getPaymentType().byteValue():null);
            customDevelopmentChargeItemDetail.setChargeItem(MrCarContractChargeEnum.MrCarContractChargeItemEnum.CUSTOM_DEVELOPMENT.getChargeItemCode().byteValue());
            customDevelopmentChargeItemDetail.setIncludeHardwareCost(customDevelopmentChargeItem.getIncludeHardwareCost()!=null?customDevelopmentChargeItem.getIncludeHardwareCost().byteValue():null);
            customDevelopmentChargeItemDetail.setContractCode(contractCode);
            customDevelopmentChargeItemDetail.setContractId(contractId);
            List<ContractCustomDevelopmentFeeInputDTO> customDevelopmentFeeList = customDevelopmentChargeItem.getCustomDevelopmentFeeList();
            customDevelopmentChargeItemDetail.setChargeItemInfo(JSONUtil.toJsonStr(customDevelopmentFeeList));
            chargeItemDetailExMapper.insertSelective(customDevelopmentChargeItemDetail);
        }
    }

    //保存租购记录
    private void saveEquipmentRentPurchaseRecord(ContractEquipmentFeeInputDTO feeInputDTO,ContractAuthAccountDTO dto) {
        ContractOtherSubjectInputDTO signingSubjectInfo = dto.getSigningSubjectInfo();
        ContractEquipmentRentPurchaseRecord record = BeanUtil.copyObject(feeInputDTO, ContractEquipmentRentPurchaseRecord.class);
        record.setContractCode(dto.getContractCode());
        record.setCreateId(dto.getMaintainId());
        record.setCreateName(dto.getMaintainName());
        record.setCreateTime(dto.getSignDate());
        record.setCustomerCode(dto.getCustomerCode());
        record.setCustomerName(dto.getCustomerName());
        record.setFinanceStructCode(dto.getFinanceStructCode());
        record.setFinanceStructName(dto.getFinanceStructName());
        record.setOperateCode(signingSubjectInfo.getOperateCode());
        record.setOperateName(signingSubjectInfo.getOperateName());
        record.setLeaseStartDate(dto.getStartDate());
        record.setLeaseEndDate(dto.getEndDate());
        record.setSignSalesId(dto.getSignSalesId());
        record.setSignSalesName(dto.getSignSalesName());
        record.setSignDate(dto.getSignDate());
        purchaseRecordExMapper.insertSelective(record);
    }
}
