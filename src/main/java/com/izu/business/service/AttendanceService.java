package com.izu.business.service;

import com.alibaba.fastjson.JSON;
import com.izu.business.common.RestLocators;
import com.izu.business.component.BusinessMsgCenter;
import com.izu.business.consts.*;
import com.izu.business.dto.ApplyOptionDTO;
import com.izu.business.dto.AttendanceApplyDTO;
import com.izu.business.dto.AttendanceDTO;
import com.izu.business.dto.AttendanceInfoDTO;
import com.izu.business.entity.AttendanceRecordExtend;
import com.izu.business.entity.DailyAttendanceRecord;
import com.izu.business.exception.BusinessException;
import com.izu.business.util.DateUtil;
import com.izu.business.util.LocalDateTimeUtils;
import com.izu.config.dto.AttendanceFenceDTO;
import com.izu.config.dto.SchedulingDayDTO;
import com.izu.config.dto.TwoDaySchedulingDTO;
import com.izu.config.errcode.MrCarConfigErrorCode;
import com.izu.framework.rocketmq.CommonRocketProducer;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.RandomStringUtil;
import com.izu.mrcar.workflow.common.constants.enums.ModelEnum;
import com.izu.mrcar.workflow.common.dto.instance.ApplyStartDTO;
import com.izu.mrcar.workflow.common.dto.instance.ApplyStartSwitchDTO;
import com.izu.mrcar.workflow.common.utils.WorkflowClient;
import com.lbs.utils.CoordUtil;
import mapper.AttendanceRecordExtendMapper;
import mapper.ex.AttendanceRecordExtendExMapper;
import mapper.ex.DailyAttendanceRecordExMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.izu.business.config.errorcode.BusinessErrorCode.*;

/**
 * <AUTHOR>
 * @date 2019-11-22 16:51
 */
@Service
public class AttendanceService {

    private static final Logger logger = LoggerFactory.getLogger(AttendanceService.class);

    @Value("${attendanceBeforeStart}")
    public int BEFORE_START;

    @Value("${attendanceAfterEnd}")
    public int AFTER_END;

    @Autowired
    private DailyAttendanceRecordExMapper recordExMapper;

    @Autowired
    private AttendanceRecordExtendExMapper recordExtendExMapper;

    @Resource(name = "dispatchMQProducer")
    private CommonRocketProducer orderMessageProducer;


    public AttendanceInfoDTO getAttendanceInfo(Integer driverId, String date, Integer companyId, int needFaceVerify, double latitude, double longitude, String driverName) {
        //是否需要审批
        int needApply=0;
        ApplyStartSwitchDTO startSwitchDTO = new ApplyStartSwitchDTO();
        startSwitchDTO.setBusinessType(ModelEnum.BusinessTypeDefaultEnum.CHECK_ON_WORK.getCode());
        startSwitchDTO.setCompanyId(companyId);
        Boolean isSendApply = WorkflowClient.isActive(startSwitchDTO);
        if(isSendApply){
            needApply=ValidStatusEnum.VALID.getValid();
        }
        //查询考勤围栏
        AttendanceFenceDTO attendanceFenceDTO = getAttendanceFenceDTO(driverId, latitude, longitude);
        AttendanceInfoDTO attendanceInfoDTO = generateAttendance(date, needApply, needFaceVerify, attendanceFenceDTO, driverName);

        //查询两天排班信息
        TwoDaySchedulingDTO twoDaySchedulingDTO = getTwoDaySchedulingDTO(driverId, date, attendanceInfoDTO);
        if (twoDaySchedulingDTO == null) {
            return attendanceInfoDTO;
        }
        SchedulingDayDTO yesterday = twoDaySchedulingDTO.getYesterday();
        SchedulingDayDTO today = twoDaySchedulingDTO.getToday();
        LocalDateTime now = LocalDateTime.now();
        LocalDate parse = LocalDate.parse(date, DateTimeFormatter.ISO_LOCAL_DATE);
        //是否是今天
        if (!parse.isBefore(now.toLocalDate())) {
            //判断是否超过昨日下班时间
            if (yesterday != null) {
                String endTime = yesterday.getSchedulingDTO().getEnd();
                LocalDateTime end = LocalDateTime.from(DateUtil.parseDate(endTime).toInstant().atZone(ZoneId.systemDefault()));
                if (now.compareTo(end.plusHours(AFTER_END)) < 0) {
                    //还可以更新下班时间 需要返回昨天的考勤信息
                    List<AttendanceDTO> attendanceDTOs = calcAttendance(now, end.toLocalDate(), yesterday, driverId);
                    attendanceInfoDTO.setAttendanceInfo(attendanceDTOs);
                    boolean inSign = inSign(attendanceInfoDTO, longitude, latitude);
                    //获取下班信息
                    int status = getAttendanceStatus(inSign, attendanceDTOs);
                    attendanceInfoDTO.setStatus(status);
                } else {
                    if(today == null){
                        throw new BusinessException(ATTENDANCE_INFO_EMPTY, "考勤信息为空");
                    }
                    List<AttendanceDTO> attendanceDTOS = calcAttendance(now, now.toLocalDate(), today, driverId);
                    attendanceInfoDTO.setAttendanceInfo(attendanceDTOS);
                    boolean inSign = inSign(attendanceInfoDTO, longitude, latitude);
                    int status = getAttendanceStatus(inSign, attendanceDTOS);
                    attendanceInfoDTO.setStatus(status);
                }
            }
            //昨天排班为空,只能是今天
            else {
                List<AttendanceDTO> attendanceDTOS = calcAttendance(now, now.toLocalDate(), today, driverId);
                attendanceInfoDTO.setAttendanceInfo(attendanceDTOS);
                boolean inSign = inSign(attendanceInfoDTO, longitude, latitude);
                int status = getAttendanceStatus(inSign, attendanceDTOS);
                attendanceInfoDTO.setStatus(status);
            }
            return attendanceInfoDTO;
        } else {
            //查询历史打卡记录
            DailyAttendanceRecord attendance = recordExMapper.getOneDayAttendance(driverId, date);
            //历史考勤状态为打卡完成
            attendanceInfoDTO.setStatus(AttendanceStatusEnum.ATTENDANCE_FULL.getStatus());
            List<AttendanceDTO> attendanceDTOList = new ArrayList<>(2);
            if (attendance == null) {
                //当天考勤信息为空
                if (today == null) {
                    //当天排班也为空
                    attendanceInfoDTO.setStatus(AttendanceStatusEnum.ATTENDANCE_EMPTY.getStatus());
                    AttendanceDTO start = new AttendanceDTO();
                    AttendanceDTO end = new AttendanceDTO();
                    attendanceDTOList.add(start);
                    attendanceDTOList.add(end);
                    attendanceInfoDTO.setAttendanceInfo(attendanceDTOList);
                } else {
                    //当天排班不为空（只有跨天查询且job没有补全考勤信息才存在这种情况）
                    List<AttendanceDTO> attendanceDTOS = calcAttendance(now, parse, today, driverId);
                    attendanceInfoDTO.setAttendanceInfo(attendanceDTOS);
                }
                return attendanceInfoDTO;
            }
            //考勤信息存在,有可能存在跨天补下班卡的情况
            LocalDateTime endTime = LocalDateTime.from(attendance.getActualWorkEndTime().toInstant().atZone(ZoneId.systemDefault()));
            AttendanceDTO start = new AttendanceDTO();
            start.setRuleDate(DateUtil.date2String(attendance.getWorkStartTime(), DateUtil.TIME_FORMAT));
            start.setActualDate(DateUtil.date2String(attendance.getActualWorkStartTime(), DateUtil.TIME_FORMAT));
            start.setLocateName(attendance.getStartLocateName());
            start.setUpdateAble(false);
            start.setLate(attendance.getLateMinutes() > 0);
            start.setOutSign(attendance.getStartOutSign().intValue() == 1);
            start.setEarly(false);
            start.setLack(attendance.getStartLack().intValue() == 1 || (now.isAfter(endTime) && LocalDateTimeUtils.dateToLocalDateTime(attendance.getActualWorkStartTime()).equals(DEFAULT_DATE_TIME)));
            AttendanceDTO end = new AttendanceDTO();
            end.setRuleDate(DateUtil.date2String(attendance.getWorkEndTime(), DateUtil.TIME_FORMAT));
            end.setActualDate(DateUtil.date2String(attendance.getActualWorkEndTime(), DateUtil.TIME_FORMAT));
            end.setLocateName(attendance.getEndLocateName());
            end.setUpdateAble(now.isBefore(endTime.plusHours(AFTER_END)));
            end.setLate(false);
            end.setLack(attendance.getEndLack().intValue() == 1 || (now.isAfter(endTime.plusHours(AFTER_END)) && LocalDateTimeUtils.dateToLocalDateTime(attendance.getActualWorkEndTime()).equals(DEFAULT_DATE_TIME)));
            end.setOutSign(attendance.getEndOutSign().intValue() == 1);
            end.setEarly(attendance.getEarlyMinutes() > 0);
            attendanceDTOList.add(start);
            attendanceDTOList.add(end);
            attendanceInfoDTO.setAttendanceInfo(attendanceDTOList);
            return attendanceInfoDTO;
        }
    }

    public AttendanceInfoDTO submitAttendance(Integer driverId, int isFaceVerify, double latitude, double longitude, Integer companyId, String applyInfo, String driverName, Integer update,String companyName,Integer userId,Integer structId,String structName) {
        LocalDateTime now = LocalDateTime.now();
        String date = now.toLocalDate().toString();

        //是否需要审批
        int needApply=0;
        ApplyStartSwitchDTO startSwitchDTO = new ApplyStartSwitchDTO();
        startSwitchDTO.setBusinessType(ModelEnum.BusinessTypeDefaultEnum.CHECK_ON_WORK.getCode());
        startSwitchDTO.setCompanyId(companyId);
        Boolean isSendApply = WorkflowClient.isActive(startSwitchDTO);
        if(isSendApply){
            needApply=ValidStatusEnum.VALID.getValid();
        }
        //查询考勤围栏
        AttendanceFenceDTO attendanceFenceDTO = getAttendanceFenceDTO(driverId, latitude, longitude);
        AttendanceInfoDTO attendanceInfoDTO = generateAttendance(date, needApply, isFaceVerify, attendanceFenceDTO, driverName);
        TwoDaySchedulingDTO twoDaySchedulingDTO = getTwoDaySchedulingDTO(driverId, date, attendanceInfoDTO);
        if (twoDaySchedulingDTO == null) {
            return attendanceInfoDTO;
        }
        SchedulingDayDTO yesterday = twoDaySchedulingDTO.getYesterday();
        SchedulingDayDTO today = twoDaySchedulingDTO.getToday();
        if (yesterday != null) {
            //判断是否可以更新昨天的打卡
            String end = yesterday.getSchedulingDTO().getEnd();
            LocalDateTime endTime = LocalDateTimeUtils.dateStrToLocalDateTime(end, LocalDateTimeUtils.TIME_FORMAT);
            if (now.compareTo(endTime.plusHours(AFTER_END)) < 0) {
                //可以更新下班卡
                return submitAttendanceInfo(now, driverId, yesterday, attendanceInfoDTO, needApply, latitude, longitude, companyId, applyInfo, isFaceVerify, update, attendanceFenceDTO.getCityCode(), attendanceFenceDTO.getCityName(),companyName,userId,structId,structName);
            } else {
                //只能打今天打卡
                if (today == null) {
                    //不能打卡，当天没有排班信息
                    attendanceInfoDTO.setStatus(AttendanceStatusEnum.ATTENDANCE_EMPTY.getStatus());
                    AttendanceDTO start = new AttendanceDTO();
                    AttendanceDTO endDto = new AttendanceDTO();
                    List<AttendanceDTO> attendanceDTOList = new ArrayList<>(2);
                    attendanceDTOList.add(start);
                    attendanceDTOList.add(endDto);
                    attendanceInfoDTO.setAttendanceInfo(attendanceDTOList);
                    return attendanceInfoDTO;
                } else {
                    //查询当天打卡信息
                    return submitAttendanceInfo(now, driverId, today, attendanceInfoDTO, needApply, latitude, longitude, companyId, applyInfo, isFaceVerify, update, attendanceFenceDTO.getCityCode(), attendanceFenceDTO.getCityName(),companyName,userId,structId,structName);
                }
            }
        } else {
            //打今天的卡
            return submitAttendanceInfo(now, driverId, today, attendanceInfoDTO, needApply, latitude, longitude, companyId, applyInfo, isFaceVerify, update, attendanceFenceDTO.getCityCode(), attendanceFenceDTO.getCityName(),companyName,userId,structId,structName);
        }
    }

    public boolean updateAble(Integer driverId, Integer type) {
        LocalDateTime now = LocalDateTime.now();
        TwoDaySchedulingDTO twoDaySchedulingDTO = getTwoDaySchedulingDTO(driverId, now.toLocalDate().toString(), null);
        if (twoDaySchedulingDTO == null) {
            return false;
        }
        SchedulingDayDTO yesterday = twoDaySchedulingDTO.getYesterday();
        SchedulingDayDTO today = twoDaySchedulingDTO.getToday();
        return haveApply(yesterday, driverId, type, now) || haveApply(today, driverId, type, now);
    }


    public AttendanceApplyDTO getApplyDetail(Integer id, String type) {
        DailyAttendanceRecord record = recordExMapper.selectByPrimaryKey(id);
        AttendanceApplyDTO.AttendanceApplyDTOBuilder builder = AttendanceApplyDTO.builder();
        if (AttendanceTypeEnum.START.getValue().equals(type)) {
            return builder.id(record.getId())
                    .recordDate(DateUtil.date2String(record.getRecordDate(), DateUtil.DATE_FORMAT))
                    .workTime(DateUtil.date2String(record.getWorkStartTime(), DateUtil.TIME_FORMAT))
                    .actualWorkTime(DateUtil.date2String(record.getActualWorkStartTime(), DateUtil.TIME_FORMAT))
                    .fenceName(record.getStartFenceName())
                    .locateName(record.getStartLocateName())
                    .driverName(record.getDriverName())
                    .driverId(record.getDriverId())
                    .applyInfo(record.getStartOutSignInfo())
                    .applyStatus(record.getStartApply())
                    .faceAuth(record.getStartFaceAuth())
                    .build();
        } else {
            return builder.id(record.getId())
                    .recordDate(DateUtil.date2String(record.getRecordDate(), DateUtil.DATE_FORMAT))
                    .workTime(DateUtil.date2String(record.getWorkEndTime(), DateUtil.TIME_FORMAT))
                    .actualWorkTime(DateUtil.date2String(record.getActualWorkEndTime(), DateUtil.TIME_FORMAT))
                    .fenceName(record.getEndFenceName())
                    .locateName(record.getEndLocateName())
                    .driverName(record.getDriverName())
                    .driverId(record.getDriverId())
                    .applyInfo(record.getEndOutSignInfo())
                    .applyStatus(record.getEndApply())
                    .faceAuth(record.getEndFaceAuth())
                    .build();
        }
    }

    private AttendanceInfoDTO generateAttendance(String date, int needApply, int needFaceVerify, AttendanceFenceDTO attendanceFenceDTO, String driverName) {
        AttendanceInfoDTO attendanceInfoDTO = new AttendanceInfoDTO();
        attendanceInfoDTO.setDate(date);
        attendanceInfoDTO.setNeedApply(needApply);
        attendanceInfoDTO.setNeedFaceVerify(needFaceVerify);
        attendanceInfoDTO.setLocateName(attendanceFenceDTO.getFenceName());
        attendanceInfoDTO.setDriverName(driverName);
        String center = attendanceFenceDTO.getCenter();
        String[] split = center.split(",");
        attendanceInfoDTO.setLongitude(split[0]);
        attendanceInfoDTO.setLatitude(split[1]);
        attendanceInfoDTO.setRadius(attendanceFenceDTO.getRadius().doubleValue());
        attendanceInfoDTO.setMotorcadeName(attendanceFenceDTO.getMotorcadeName());
        return attendanceInfoDTO;
    }

    //计算上下班考勤打卡状态
    private List<AttendanceDTO> calcAttendance(LocalDateTime now, LocalDate date, SchedulingDayDTO scheduling, Integer driverId) {
        //上班时间
        LocalDateTime startTime = LocalDateTime.from(DateUtil.parseDate(scheduling.getSchedulingDTO().getStart()).toInstant().atZone(ZoneId.systemDefault()));
        //下班时间
        LocalDateTime endTime = LocalDateTime.from(DateUtil.parseDate(scheduling.getSchedulingDTO().getEnd()).toInstant().atZone(ZoneId.systemDefault()));

        LocalDateTime midTime = LocalDateTimeUtils.getMidTime(startTime, endTime);

        List<AttendanceDTO> attendanceDTOList = new ArrayList<>(2);

        DailyAttendanceRecord attendanceRecord = recordExMapper.getOneDayAttendance(driverId, date.toString());
        if (attendanceRecord == null) {
            AttendanceDTO start = new AttendanceDTO();
            AttendanceDTO end = new AttendanceDTO();
            //没有考勤 默认迟到早退外勤都是false
            start.setRuleDate(scheduling.getSchedulingDTO().getStart());
            start.setActualDate(DEFAULT_DATE_TIME.format(DateTimeFormatter.ofPattern(DateUtil.TIME_FORMAT)));
            start.setUpdateAble(now.isBefore(midTime));
            start.setLack(now.isAfter(midTime));
            end.setRuleDate(scheduling.getSchedulingDTO().getEnd());
            end.setActualDate(DEFAULT_DATE_TIME.format(DateTimeFormatter.ofPattern(DateUtil.TIME_FORMAT)));
            end.setLack(endTime.plusHours(AFTER_END).isBefore(now));
            attendanceDTOList.add(start);
            attendanceDTOList.add(end);
        } else {
            //有考勤信息
            LocalDateTime actualWorkStartTime = LocalDateTime.from(attendanceRecord.getActualWorkStartTime().toInstant().atZone(ZoneId.systemDefault()));
            LocalDateTime actualWorkEndTime = LocalDateTime.from(attendanceRecord.getActualWorkEndTime().toInstant().atZone(ZoneId.systemDefault()));

            AttendanceDTO start = new AttendanceDTO();
            AttendanceDTO end = new AttendanceDTO();

            start.setRuleDate(scheduling.getSchedulingDTO().getStart());
            start.setUpdateAble(now.isBefore(midTime) && LocalDateTimeUtils.dateToLocalDateTime(attendanceRecord.getActualWorkEndTime()).equals(DEFAULT_DATE_TIME));
            start.setEarly(false);
            start.setLate(startTime.isBefore(actualWorkStartTime));
            start.setApply(attendanceRecord.getStartApply().equals(ValidStatusEnum.VALID.getValid()));
            start.setOutSign(attendanceRecord.getStartOutSign().intValue() == ValidStatusEnum.VALID.getValid() && (ValidStatusEnum.INVALID.getValid().equals(attendanceRecord.getStartApply()) || ApplyStatusEnum.APPLY_PASS.value().equals(attendanceRecord.getStartApply())) );
            start.setLack((actualWorkStartTime.toLocalDate().isBefore(startTime.toLocalDate()) && !start.isApply()) || attendanceRecord.getStartLack().equals(ValidStatusEnum.VALID.getValid()));
            start.setLocateName(attendanceRecord.getStartLocateName());
            start.setInvalid(attendanceRecord.getStartApply().equals(ApplyStatusEnum.APPLY_CANCEL.value()) || attendanceRecord.getStartApply().equals(ApplyStatusEnum.APPLY_NOT_PASS.value()) || attendanceRecord.getStartApply().equals(ApplyStatusEnum.APPLY_OVER_Time.value()) );
            start.setActualDate(start.isApply() ? DateUtil.date2String(attendanceRecord.getStartApplyTime(), DateUtil.TIME_FORMAT) : DateUtil.date2String(attendanceRecord.getActualWorkStartTime(), DateUtil.TIME_FORMAT));

            end.setRuleDate(scheduling.getSchedulingDTO().getEnd());
            end.setUpdateAble(now.isBefore(endTime.plusHours(AFTER_END)));
            end.setEarly(actualWorkEndTime.toLocalDate().equals(endTime.toLocalDate()) && actualWorkEndTime.isBefore(endTime));
            end.setLate(false);
            end.setApply(attendanceRecord.getEndApply().equals(ValidStatusEnum.VALID.getValid()));
            end.setOutSign(attendanceRecord.getEndOutSign().intValue() == ValidStatusEnum.VALID.getValid()  && (ValidStatusEnum.INVALID.getValid().equals(attendanceRecord.getEndApply()) || ApplyStatusEnum.APPLY_PASS.value().equals(attendanceRecord.getEndApply()))  );
            end.setLack(attendanceRecord.getEndLack().equals(ValidStatusEnum.VALID.getValid()) || (now.isAfter(endTime.plusHours(AFTER_END))) && actualWorkEndTime.toLocalDate().isBefore(endTime.toLocalDate()) && !end.isApply());
            end.setLocateName(attendanceRecord.getEndLocateName());
            end.setInvalid(attendanceRecord.getEndApply().equals(ApplyStatusEnum.APPLY_CANCEL.value()) || attendanceRecord.getEndApply().equals(ApplyStatusEnum.APPLY_NOT_PASS.value()) || attendanceRecord.getEndApply().equals(ApplyStatusEnum.APPLY_OVER_Time.value()) );
            end.setActualDate(end.isApply() ? DateUtil.date2String(attendanceRecord.getEndApplyTime(), DateUtil.TIME_FORMAT) : DateUtil.date2String(attendanceRecord.getActualWorkEndTime(), DateUtil.TIME_FORMAT));

            attendanceDTOList.add(start);
            attendanceDTOList.add(end);
        }
        return attendanceDTOList;
    }

    //判断是否在围栏内
    private boolean inSign(AttendanceInfoDTO attendanceInfoDTO, double longitude, double latitude) {
        return attendanceInfoDTO.getRadius() > CoordUtil.simulateDistance(longitude, latitude, Double.parseDouble(attendanceInfoDTO.getLongitude()), Double.parseDouble(attendanceInfoDTO.getLatitude()));
    }

    //获取考勤状态
    private int getAttendanceStatus(boolean inSign, List<AttendanceDTO> attendanceDTOS) {
        //上班信息
        AttendanceDTO start = attendanceDTOS.get(0);
        //下班信息
        AttendanceDTO end = attendanceDTOS.get(1);

        LocalDateTime startTime = LocalDateTime.from(DateUtil.parseDate(start.getRuleDate()).toInstant().atZone(ZoneId.systemDefault()));

        LocalDateTime endTime = LocalDateTime.from(DateUtil.parseDate(end.getRuleDate()).toInstant().atZone(ZoneId.systemDefault()));

        //上班是否打卡
        boolean startAttendance = haveAttendance(start);
        //下班是否打卡
        boolean endAttendance = haveAttendance(end);
        //获取打卡时间
        LocalDateTime now = LocalDateTime.now();
        //是否打过上班卡
        LocalDateTime startEndTime = LocalDateTimeUtils.getMidTime(startTime, endTime);
        if (startAttendance || now.isAfter(startEndTime)) {
            //是否打过下班卡
            if (endAttendance || now.isAfter(endTime.plusHours(AFTER_END))) {
                return AttendanceStatusEnum.ATTENDANCE_FULL.getStatus();
            } else {
                return inSign ? AttendanceStatusEnum.END_ATTENDANCE.getStatus() : AttendanceStatusEnum.END_OUT_RANGE_ATTENDANCE.getStatus();
            }
        } else {
            //是否到上班时间
            if (now.isBefore(startTime.minusHours(BEFORE_START))) {
                //未到上班时间不能打卡
                return AttendanceStatusEnum.ATTENDANCE_EMPTY.getStatus();
            } else {
                //是否迟到
                if (now.isAfter(startTime)) {
                    return inSign ? AttendanceStatusEnum.START_LATE_ATTENDANCE.getStatus() : AttendanceStatusEnum.START_OUT_SIGN_LATE_ATTENDANCE.getStatus();
                } else {
                    return inSign ? AttendanceStatusEnum.START_ATTENDANCE.getStatus() : AttendanceStatusEnum.START_OUT_SIGN_ATTENDANCE.getStatus();
                }
            }
        }

    }

    //判断是否有打过卡
    private boolean haveAttendance(AttendanceDTO attendanceDTO) {
        LocalDateTime actualDate = LocalDateTime.from(DateUtil.parseDate(attendanceDTO.getActualDate()).toInstant().atZone(ZoneId.systemDefault()));
        LocalDateTime ruleDate = LocalDateTime.from(DateUtil.parseDate(attendanceDTO.getRuleDate()).toInstant().atZone(ZoneId.systemDefault()));
        //考勤日期等于排班日期 且不等于1970年1月1日等于打过卡
        if (actualDate.toLocalDate().equals(ruleDate.toLocalDate())) {
            return !DEFAULT_DATE_TIME.toLocalDate().equals(ruleDate.toLocalDate()) && !attendanceDTO.isInvalid();
        }
        return false;
    }


    //查询考勤围栏
    private AttendanceFenceDTO getAttendanceFenceDTO(Integer driverId, double latitude, double longitude) {
        Map<String, Object> params = new HashMap<>();
        params.put("driverId", driverId);
        params.put("latitude", latitude);
        params.put("longitude", longitude);
        RestResponse response = RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, RestLocators.iot().getRestUrl(BusinessMsgCenter.GET_NEAR_LEAST_FENCE), params, null, AttendanceFenceDTO.class);
        if (!response.isSuccess()) {
            throw new BusinessException(response.getCode(), response.getMsg());
        }
        return (AttendanceFenceDTO) response.getData();
    }

    //查询排班信息
    private TwoDaySchedulingDTO getTwoDaySchedulingDTO(Integer driverId, String schedulingDate, AttendanceInfoDTO attendanceInfoDTO) {
        Map<String, Object> params = new HashMap<>();
        params.put("driverId", driverId);
        params.put("schedulingDate", schedulingDate);
        RestResponse response = RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, RestLocators.user().getRestUrl(BusinessMsgCenter.TWO_DAY_SCHEDULING), params, null, TwoDaySchedulingDTO.class);
        if (!response.isSuccess()) {
            //没有考勤信息，不允许打卡
            if (response.getCode() == MrCarConfigErrorCode.ATTENDANCE_EMTYP) {
                if (attendanceInfoDTO == null) {
                    return null;
                }
                attendanceInfoDTO.setStatus(AttendanceStatusEnum.ATTENDANCE_EMPTY.getStatus());
                List<AttendanceDTO> attendanceDTOList = new ArrayList<>();
                AttendanceDTO start = new AttendanceDTO();
                AttendanceDTO end = new AttendanceDTO();
                attendanceDTOList.add(start);
                attendanceDTOList.add(end);
                attendanceInfoDTO.setAttendanceInfo(attendanceDTOList);
                return null;
            } else {
                throw new BusinessException(response.getCode(), response.getMsg());
            }
        }
        return (TwoDaySchedulingDTO) response.getData();
    }

    //考勤打卡
    private AttendanceInfoDTO submitAttendanceInfo(LocalDateTime now, Integer driverId, SchedulingDayDTO schedulingDayDTO, AttendanceInfoDTO attendanceInfoDTO, int needApply, double latitude, double longitude, Integer companyId, String applyInfo, int isFaceVerify, Integer update, Integer cityCode, String cityName,String companyName,Integer userId,Integer structId,String structName) {
        if (schedulingDayDTO == null) {
            //不能打卡，当天没有排班信息
            attendanceInfoDTO.setStatus(AttendanceStatusEnum.ATTENDANCE_EMPTY.getStatus());
            AttendanceDTO start = new AttendanceDTO();
            AttendanceDTO endDto = new AttendanceDTO();
            List<AttendanceDTO> attendanceDTOList = new ArrayList<>(2);
            attendanceDTOList.add(start);
            attendanceDTOList.add(endDto);
            attendanceInfoDTO.setAttendanceInfo(attendanceDTOList);
            return attendanceInfoDTO;
        }
        //上班时间
        LocalDateTime startTime = LocalDateTimeUtils.dateStrToLocalDateTime(schedulingDayDTO.getSchedulingDTO().getStart(), LocalDateTimeUtils.TIME_FORMAT);
        //下班时间
        LocalDateTime endTime = LocalDateTimeUtils.dateStrToLocalDateTime(schedulingDayDTO.getSchedulingDTO().getEnd(), LocalDateTimeUtils.TIME_FORMAT);
        if (now.isBefore(startTime.minusHours(BEFORE_START)) || now.isAfter(endTime.plusHours(AFTER_END))) {
            //上班时间之前X小时不能打卡
            //下班时间之后X小时不能打卡
            attendanceInfoDTO.setStatus(AttendanceStatusEnum.ATTENDANCE_EMPTY.getStatus());
            AttendanceDTO start = new AttendanceDTO();
            AttendanceDTO endDto = new AttendanceDTO();
            start.setRuleDate(schedulingDayDTO.getSchedulingDTO().getStart());
            endDto.setRuleDate(schedulingDayDTO.getSchedulingDTO().getEnd());
            List<AttendanceDTO> attendanceDTOList = new ArrayList<>(2);
            attendanceDTOList.add(start);
            attendanceDTOList.add(endDto);
            attendanceInfoDTO.setAttendanceInfo(attendanceDTOList);
            return attendanceInfoDTO;
        }
        LocalDateTime startCardEndTime = LocalDateTimeUtils.getMidTime(startTime, endTime);

        //查询当天打卡信息
        DailyAttendanceRecord attendance = recordExMapper.getOneDayAttendance(driverId, startTime.toLocalDate().toString());
        boolean inSign = inSign(attendanceInfoDTO, longitude, latitude);
        String locate = longitude + "," + latitude;
        String locateName = getLocateName(longitude, latitude);
        int type;
        if (attendance == null) {
            //新增打卡信息
            DailyAttendanceRecord attendanceRecord = new DailyAttendanceRecord();
            attendanceRecord.setRecordDate(LocalDateTimeUtils.localDateToDate(startTime.toLocalDate()));
            attendanceRecord.setWorkStartTime(LocalDateTimeUtils.localDateTimeToDate(startTime));
            attendanceRecord.setWorkEndTime(LocalDateTimeUtils.localDateTimeToDate(endTime));
            attendanceRecord.setDriverName(attendanceInfoDTO.getDriverName());
            attendanceRecord.setDriverId(driverId);
            AttendanceRecordExtend extend = new AttendanceRecordExtend();
            extend.setRecordTime(LocalDateTimeUtils.localDateTimeToDate(now));
            extend.setRecordLocate(locate);
            extend.setRecordLocateName(locateName);
            if (now.isAfter(startCardEndTime)) {
                //下班卡
                attendanceRecord.setEndFaceAuth((byte) isFaceVerify);
                attendanceRecord.setEndOutSignInfo(applyInfo);
                attendanceRecord.setEndApply(((!inSign && needApply == ValidStatusEnum.VALID.getValid()) ? ValidStatusEnum.VALID.getValid() : ValidStatusEnum.INVALID.getValid()));
                generateAttendanceRecord(attendanceRecord, inSign, needApply, now, AttendanceTypeEnum.END, locate, locateName, attendanceInfoDTO.getLocateName());
                extend.setRecordType((byte) AttendanceTypeEnum.END.getCode());
                type = AttendanceTypeEnum.END.getCode();
                attendanceRecord.setEndSeqNo(getAttendanceNo(false, DateUtil.date2String(attendanceRecord.getRecordDate(), DateUtil.DATE_FORMAT_2)));
            } else {
                //上班卡
                attendanceRecord.setStartOutSignInfo(applyInfo);
                attendanceRecord.setStartFaceAuth((byte) isFaceVerify);
                attendanceRecord.setStartApply(((!inSign && needApply == ValidStatusEnum.VALID.getValid()) ? ValidStatusEnum.VALID.getValid() : ValidStatusEnum.INVALID.getValid()));
                generateAttendanceRecord(attendanceRecord, inSign, needApply, now, AttendanceTypeEnum.START, locate, locateName, attendanceInfoDTO.getLocateName());
                extend.setRecordType((byte) AttendanceTypeEnum.START.getCode());
                type = AttendanceTypeEnum.START.getCode();
                attendanceRecord.setStartSeqNo(getAttendanceNo(true, DateUtil.date2String(attendanceRecord.getRecordDate(), DateUtil.DATE_FORMAT_2)));
            }
            logger.info("【考勤打卡】是否需要走审批标识，inSign{} & needApply{}：",inSign,needApply);
            if (!inSign && needApply == ValidStatusEnum.VALID.getValid()) {
                //generateApply(type, cityCode, cityName, companyId, attendanceRecord);
                String processId = startApply(type, companyId, companyName,attendanceRecord,userId,structId,structName);
                logger.info("【外勤打卡审批】发送审批流完成，审批id：" + processId);
                if(type == AttendanceTypeEnum.START.getCode()){
                    attendanceRecord.setStartProcessInstanceId(processId);
                    extend.setSeqNo(attendanceRecord.getStartSeqNo());
                    extend.setOutSignInfo(attendanceRecord.getStartOutSignInfo());
                }else {
                    attendanceRecord.setEndProcessInstanceId(processId);
                    extend.setSeqNo(attendanceRecord.getEndSeqNo());
                    extend.setOutSignInfo(attendanceRecord.getEndOutSignInfo());
                }
                extend.setProcessInstanceId(processId);

            }
            recordExMapper.insertSelective(attendanceRecord);
            extend.setAttendanceRecordId(attendanceRecord.getId());
            recordExtendExMapper.insertSelective(extend);
        } else {
            //更新打卡信息
            if (!inSign && needApply == ValidStatusEnum.VALID.getValid()) {
                //判断是否可以更新外勤审批
                if (update == null && ApplyStatusEnum.APPLY_WAIT.value().equals(attendance.getStartApply()) && now.isBefore(startCardEndTime)) {
                    throw new BusinessException( ATTENDANCE_APPLY_WAIT, "存在审批中的申请");
                }else if (update == null && ApplyStatusEnum.APPLY_WAIT.value().equals(attendance.getEndApply()) && now.isAfter(startCardEndTime)){
                    throw new BusinessException( ATTENDANCE_APPLY_WAIT, "存在审批中的申请");
                }
                else if (update != null && update.equals(ValidStatusEnum.INVALID.getValid().intValue()) && ApplyStatusEnum.APPLY_WAIT.value().equals(attendance.getStartApply())) {
                    throw new BusinessException( ATTENDANCE_APPLY_WAIT, "存在审批中的申请");
                } else if (update != null && update.equals(ValidStatusEnum.VALID.getValid().intValue()) && ApplyStatusEnum.APPLY_WAIT.value().equals(attendance.getEndApply())){
                    throw new BusinessException( ATTENDANCE_APPLY_WAIT, "存在审批中的申请");
                }
            }
            DailyAttendanceRecord attendanceRecord = new DailyAttendanceRecord();
            attendanceRecord.setId(attendance.getId());
            attendanceRecord.setRecordDate(attendance.getRecordDate());
            attendanceRecord.setDriverId(attendance.getDriverId());
            attendanceRecord.setDriverName(attendance.getDriverName());
            AttendanceRecordExtend extend = new AttendanceRecordExtend();
            extend.setRecordTime(LocalDateTimeUtils.localDateTimeToDate(now));
            extend.setRecordLocate(locate);
            extend.setRecordLocateName(locateName);
            extend.setAttendanceRecordId(attendance.getId());
            if (update == null) {
                if (now.isAfter(startCardEndTime)) {
                    //下班卡
                    attendanceRecord.setEndOutSignInfo(applyInfo);
                    attendanceRecord.setEndApply(((!inSign && needApply == ValidStatusEnum.VALID.getValid()) ? ValidStatusEnum.VALID.getValid() : ValidStatusEnum.INVALID.getValid()));
                    attendanceRecord.setEndFaceAuth((byte) isFaceVerify);
                    generateAttendanceRecord(attendanceRecord, inSign, needApply, now, AttendanceTypeEnum.END, locate, locateName, attendanceInfoDTO.getLocateName());
                    extend.setRecordType((byte) AttendanceTypeEnum.END.getCode());
                    type = AttendanceTypeEnum.END.getCode();
                    attendanceRecord.setEndSeqNo(getAttendanceNo(false, DateUtil.date2String(attendanceRecord.getRecordDate(), DateUtil.DATE_FORMAT_2)));
                }else {
                    //上班卡
                    attendanceRecord.setStartOutSignInfo(applyInfo);
                    attendanceRecord.setStartApply(((!inSign && needApply == ValidStatusEnum.VALID.getValid()) ? ValidStatusEnum.VALID.getValid() : ValidStatusEnum.INVALID.getValid()));
                    attendanceRecord.setStartFaceAuth((byte) isFaceVerify);
                    generateAttendanceRecord(attendanceRecord, inSign, needApply, now, AttendanceTypeEnum.START, locate, locateName, attendanceInfoDTO.getLocateName());
                    extend.setRecordType((byte) AttendanceTypeEnum.START.getCode());
                    type = AttendanceTypeEnum.START.getCode();
                    attendanceRecord.setStartSeqNo(getAttendanceNo(true, DateUtil.date2String(attendanceRecord.getRecordDate(), DateUtil.DATE_FORMAT_2)));
                }
            } else {
                //更新上下班打卡信息
                if (update.equals(ValidStatusEnum.INVALID.getValid().intValue()) && now.isBefore(startCardEndTime)) {
                    //更新上班卡
                    attendanceRecord.setStartOutSignInfo(applyInfo);
                    attendanceRecord.setStartApply(((!inSign && needApply == ValidStatusEnum.VALID.getValid()) ? ValidStatusEnum.VALID.getValid() : ValidStatusEnum.INVALID.getValid()));
                    attendanceRecord.setStartFaceAuth((byte) isFaceVerify);
                    generateAttendanceRecord(attendanceRecord, inSign, needApply, now, AttendanceTypeEnum.START, locate, locateName, attendanceInfoDTO.getLocateName());
                    extend.setRecordType((byte) AttendanceTypeEnum.START.getCode());
                    type = AttendanceTypeEnum.START.getCode();
                    attendanceRecord.setStartSeqNo(getAttendanceNo(true, DateUtil.date2String(attendanceRecord.getRecordDate(), DateUtil.DATE_FORMAT_2)));
                } else if (update.equals(ValidStatusEnum.VALID.getValid().intValue()) && now.isAfter(startCardEndTime)){
                    //更新下班卡
                    attendanceRecord.setEndOutSignInfo(applyInfo);
                    attendanceRecord.setEndApply(((!inSign && needApply == ValidStatusEnum.VALID.getValid()) ? ValidStatusEnum.VALID.getValid() : ValidStatusEnum.INVALID.getValid()));
                    attendanceRecord.setEndFaceAuth((byte) isFaceVerify);
                    generateAttendanceRecord(attendanceRecord, inSign, needApply, now, AttendanceTypeEnum.END, locate, locateName, attendanceInfoDTO.getLocateName());
                    extend.setRecordType((byte) AttendanceTypeEnum.END.getCode());
                    type = AttendanceTypeEnum.END.getCode();
                    attendanceRecord.setEndSeqNo(getAttendanceNo(false, DateUtil.date2String(attendanceRecord.getRecordDate(), DateUtil.DATE_FORMAT_2)));
                }else {
                    throw new BusinessException(ATTENDANCE_ERROR, "当前时间不允许打卡");
                }
            }
            if (type != 0) {
                if (!inSign && needApply == ValidStatusEnum.VALID.getValid()) {
                    //generateApply(type, cityCode, cityName, companyId, attendanceRecord);
                    String processId = startApply(type, companyId,companyName,attendanceRecord,userId,structId,structName);
                    logger.info("【外勤打卡审批】发送审批流完成，审批id：" + processId);
                    if(type == AttendanceTypeEnum.START.getCode()){
                        attendanceRecord.setStartProcessInstanceId(processId);
                        extend.setSeqNo(attendanceRecord.getStartSeqNo());
                        extend.setOutSignInfo(attendanceRecord.getStartOutSignInfo());
                    }else {
                        attendanceRecord.setEndProcessInstanceId(processId);
                        extend.setSeqNo(attendanceRecord.getEndSeqNo());
                        extend.setOutSignInfo(attendanceRecord.getEndOutSignInfo());
                    }
                    extend.setProcessInstanceId(processId);
                }
                recordExMapper.updateByPrimaryKeySelective(attendanceRecord);
                recordExtendExMapper.insertSelective(extend);
            }
        }
        List<AttendanceDTO> attendanceDTOs = calcAttendance(now, now.toLocalDate(), schedulingDayDTO, driverId);
        attendanceInfoDTO.setAttendanceInfo(attendanceDTOs);
        int status = getAttendanceStatus(inSign, attendanceDTOs);
        attendanceInfoDTO.setStatus(status);
        return attendanceInfoDTO;
    }

    private String getLocateName(double longitude, double latitude) {
        Map<String, Object> params = new HashMap<>(2);
        params.put("longitude", longitude);
        params.put("latitude", latitude);
        RestResponse response = RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, RestLocators.iot().getRestUrl(BusinessMsgCenter.GET_LOCATE_NAME), params, null, String.class);
        if (response.isSuccess()) {
            return (String) response.getData();
        }
        logger.error("经纬度获取定位点名称失败 经度 {} 纬度 {} ", longitude, latitude);
        return "";
    }

    private DailyAttendanceRecord generateAttendanceRecord(DailyAttendanceRecord record, boolean inSign, int needApply, LocalDateTime now, AttendanceTypeEnum typeEnum, String locate, String locateName, String fenceName) {
        if (typeEnum == AttendanceTypeEnum.START) {
            //上班考勤信息
            if (ValidStatusEnum.VALID.getValid().intValue() == needApply && !inSign) {
                record.setStartApplyTime(LocalDateTimeUtils.localDateTimeToDate(now));
            }else {
                record.setActualWorkStartTime(LocalDateTimeUtils.localDateTimeToDate(now));
            }
            record.setStartLocate(locate);
            record.setStartLocateName(locateName);
            record.setStartOutSign(inSign ? ValidStatusEnum.INVALID.getValid() : ValidStatusEnum.VALID.getValid());
            record.setStartFenceName(fenceName);
        } else {
            //下班考勤信息
            if (ValidStatusEnum.VALID.getValid().intValue() == needApply && !inSign) {
                record.setEndApplyTime(LocalDateTimeUtils.localDateTimeToDate(now));
            }else {
                record.setActualWorkEndTime(LocalDateTimeUtils.localDateTimeToDate(now));
            }
            record.setEndLocate(locate);
            record.setEndLocateName(locateName);
            record.setEndOutSign(inSign ? ValidStatusEnum.INVALID.getValid() : ValidStatusEnum.VALID.getValid());
            record.setEndFenceName(fenceName);
        }
        return record;
    }

    private boolean inWorkTime(SchedulingDayDTO schedulingDayDTO, LocalDateTime now) {
        if (schedulingDayDTO == null) {
            return false;
        }
        String start = schedulingDayDTO.getSchedulingDTO().getStart();
        String end = schedulingDayDTO.getSchedulingDTO().getEnd();
        LocalDateTime startTime = LocalDateTimeUtils.dateStrToLocalDateTime(start, LocalDateTimeUtils.TIME_FORMAT);
        LocalDateTime endTime = LocalDateTimeUtils.dateStrToLocalDateTime(end, LocalDateTimeUtils.TIME_FORMAT);
        return !(now.isBefore(startTime.minusHours(BEFORE_START)) || now.isAfter(endTime.plusHours(AFTER_END)));
    }

    private boolean haveApply(SchedulingDayDTO schedulingDayDTO, Integer driverId, Integer type, LocalDateTime now) {
        if (inWorkTime(schedulingDayDTO, now)) {
            String start = schedulingDayDTO.getSchedulingDTO().getStart();
            LocalDateTime dateTime = LocalDateTimeUtils.dateStrToLocalDateTime(start, LocalDateTimeUtils.TIME_FORMAT);
            return recordExMapper.getApplyCount(dateTime.toLocalDate().toString(), driverId, type) > 0;
        }
        return false;
    }

    private void generateApply(int type, Integer cityCode, String cityName, Integer companyId, DailyAttendanceRecord attendanceRecord) {
        boolean flag = (type == AttendanceTypeEnum.START.getCode());
        ApplyOptionDTO applyOptionDTO = new ApplyOptionDTO();
        applyOptionDTO.setApplyType(ApplyTypeEnum.EXTERNAL_ATTENDANCE_APPLY.value());
        applyOptionDTO.setCompanyId(companyId);
        applyOptionDTO.setCreateCityCode(cityCode);
        applyOptionDTO.setCreateCityName(cityName);
        applyOptionDTO.setRelationId(attendanceRecord.getId());
        applyOptionDTO.setRelationNo(generateRelationNo(flag, DateUtil.date2String(attendanceRecord.getRecordDate(), DateUtil.DATE_FORMAT_2)));
        applyOptionDTO.setApplyStatusLevelOne(ApplyStatusEnum.APPLY_WAIT.value());
        applyOptionDTO.setDate(new Date());
        applyOptionDTO.setUserId(attendanceRecord.getDriverId());
        applyOptionDTO.setUserName(attendanceRecord.getDriverName());
        applyOptionDTO.setParamA(flag ? DateUtil.date2String(attendanceRecord.getStartApplyTime(), DateUtil.MINUTES_FORMAT) : DateUtil.date2String(attendanceRecord.getEndApplyTime(), DateUtil.MINUTES_FORMAT));
        applyOptionDTO.setParamB(flag ? attendanceRecord.getStartLocateName() : attendanceRecord.getEndLocateName());
        applyOptionDTO.setParamC(flag ? attendanceRecord.getStartOutSignInfo() : attendanceRecord.getEndOutSignInfo());
        orderMessageProducer.publishMessage(BusinessMq.MAKE_APPLY, null, null, applyOptionDTO);
    }

    /**
     * 调用审批流接口发起外勤打卡审批流程
     * @param type
     * @param companyId
     * @param companyName
     * @param attendanceRecord
     * @return
     */
    public String startApply(int type, Integer companyId,String companyName,DailyAttendanceRecord attendanceRecord,Integer userId,Integer structId,String structName){

        logger.info("【外勤打卡审批】开关打开，需要走审批，企业：" + companyName);
        ApplyStartDTO applyStartDTO = new ApplyStartDTO();
        if(type == AttendanceTypeEnum.START.getCode()){
            applyStartDTO.setBusinessNo(attendanceRecord.getStartSeqNo());
        }else {
            applyStartDTO.setBusinessNo(attendanceRecord.getEndSeqNo());
        }
        applyStartDTO.setBusinessType(ModelEnum.BusinessTypeDefaultEnum.CHECK_ON_WORK.getCode());
        applyStartDTO.setLoginCompanyId(companyId);
        applyStartDTO.setLoginUserId(userId);
        applyStartDTO.setLoginUserName(attendanceRecord.getDriverName());
        applyStartDTO.setLoginCompanyName(companyName);
        applyStartDTO.setLoginDeptId(structId);
        applyStartDTO.setLoginDeptName(structName);
        Map<String,Object> variables = new HashMap<>();
        variables.put("applyRemark",attendanceRecord.getStartOutSignInfo());
        applyStartDTO.setVariables(JSON.toJSONString(variables));
        logger.info("【外勤打卡审批】发起审批，参数：" + JSON.toJSONString(applyStartDTO));
        RestResponse response =  WorkflowClient.applyStart(applyStartDTO);
        logger.info("【外勤打卡审批】发起审批，结果：" + JSON.toJSONString(response));
        if(response.isSuccess() && response.getData()!=null){
            return (String) response.getData();
        }else {
            throw new BusinessException(CONTRACT_COMMONT_MSG, response.getMsg());
        }


    }

    //默认日期
    private static final LocalDateTime DEFAULT_DATE_TIME = LocalDateTime.of(1970, 1, 1, 0, 0, 1, 0);

    private String generateRelationNo(boolean isStart, String dateStr) {
        return AttendanceApplyConstants.APPLY_PERIFX + (isStart ? AttendanceApplyConstants.START_APPLY_SUFFIX : AttendanceApplyConstants.END_APPLY_SUFFIX) + dateStr + RandomStringUtil.genRandomNumberString(6);
    }

    private String getAttendanceNo(boolean isStart, String dateStr) {
        return AttendanceApplyConstants.ATTENDANCE_PERIFX + (isStart ? AttendanceApplyConstants.START_APPLY_SUFFIX : AttendanceApplyConstants.END_APPLY_SUFFIX) + dateStr + RandomStringUtil.genRandomNumberString(6);
    }

    public AttendanceApplyDTO getAttendanceDetail(String seqNo) {
        //查询扩展表即可
        AttendanceRecordExtend extend = recordExtendExMapper.selectBySeqNo(seqNo);

        AttendanceApplyDTO.AttendanceApplyDTOBuilder builder = AttendanceApplyDTO.builder();
        if(null==extend){
            //判空处理
            return builder.build();
        }else {
            return builder.id(extend.getId())
                    .actualWorkTime(DateUtil.date2String(extend.getRecordTime(), DateUtil.TIME_FORMAT))
                    .locateName(extend.getRecordLocateName())
                    .applyInfo(extend.getOutSignInfo())
                    .processId(extend.getProcessInstanceId())
                    .build();
        }
    }
}
