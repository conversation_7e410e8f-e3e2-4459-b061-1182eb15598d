package com.izu.business.service.provider;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.izu.business.consts.BusinessMq;
import com.izu.business.dto.provider.input.StatisticsFunctionViewExportParamDTO;
import com.izu.business.dto.provider.input.StatisticsFunctionViewExportReqDTO;
import com.izu.business.dto.provider.input.StatisticsFunctionViewReqDTO;
import com.izu.business.dto.provider.input.StatisticsFunctionViewSearchReqDTO;
import com.izu.business.dto.provider.output.StatisticsFunctionViewListRespDTO;
import com.izu.business.dto.provider.output.StatisticsFunctionViewLogDetailRespDTO;
import com.izu.business.dto.provider.output.StatisticsFunctionViewRespDTO;
import com.izu.business.entity.*;
import com.izu.business.entity.mongo.StatisticsFunctionViewLog;
import com.izu.business.rpc.MrcarUserRpc;
import com.izu.business.util.Check;
import com.izu.framework.rocketmq.CommonRocketProducer;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.util.BeanUtil;
import com.izu.user.dto.ManagerPermissionDTO;
import com.izu.user.dto.company.CompanyDropDownReqDTO;
import com.izu.user.dto.company.CompanyNameRespDTO;
import com.izu.user.enums.MenuOpenModeEnum;
import com.izu.user.enums.perm.PermissionTypeEnum;
import com.mongodb.BasicDBObject;
import com.mongodb.DBObject;
import mapper.ex.StatisticsFunctionViewExMapper;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 功能访问量统计Service类.
 *
 * <AUTHOR>
 * @date 2023-05-08
 */
@Service
public class StatisticsFunctionViewService {

    private static final Logger logger = LoggerFactory.getLogger(StatisticsFunctionViewService.class);

    private final DateTimeFormatter formatter =
            DateTimeFormatter.ISO_DATE.withZone(ZoneId.systemDefault());

    @Autowired
    private MongoTemplate mongoTemplate;
    @Resource(name = "dispatchMQProducer")
    private CommonRocketProducer businessMessageProducer;
    @Autowired
    private StatisticsFunctionViewExMapper statisticsFunctionViewExMapper;

    /**
     * 记录用户对菜单的操作日志.
     *
     * @param reqDTO 操作日志记录
     * @return 返回是否成功插入异步队列中
     */
    public boolean appendUserOperateFunctionLog(StatisticsFunctionViewReqDTO reqDTO) {
        // 向消息队列发送日志内容, 异步消费进行写入Mongo中
        StatisticsFunctionViewLog log = new StatisticsFunctionViewLog();
        // 前置参数校验
        if (Check.NuNStr(reqDTO.getLoginCode())
                || Check.NuNStr(reqDTO.getLoginCompanyCode())) {
            return false;
        }
        log.setOperateTime(new Date()); // 操作时间
        log.setUserCode(reqDTO.getLoginCode()); // 用户编码
        log.setUserName(reqDTO.getLoginUserName()); // 用户名称
        log.setUserMobile(reqDTO.getLoginUserMobile()); // 用户手机号
        log.setCompanyCode(reqDTO.getLoginCompanyCode()); // 企业编码
        log.setCompanyName(reqDTO.getLoginCompanyName()); // 企业名称
        log.setPermSysCode(reqDTO.getPermSysCode()); // 权限系统编码
        log.setPermissionCode(reqDTO.getPermissionCode()); // 权限代码
        log.setMenuOpenMode(reqDTO.getMenuOpenMode()); // 菜单所属系统
        return businessMessageProducer.publishMessage(BusinessMq.USER_OPERATE_FUNCTION, null, null, log);
    }

    /**
     * 将从消息队列中取到的日志, 批量存入Mongo中.
     *
     * @param logs 操作日志
     */
    public void insertUserOperateFunctionLog(List<StatisticsFunctionViewLog> logs) {
        // 存储记录
        List<StatisticsFunctionViewLog> rows = new ArrayList<>();
        for (StatisticsFunctionViewLog log : logs) {
            // 查询权限记录
            ManagerPermissionDTO permissionInfo = null;
            if (log.getMenuOpenMode() == 2) {
                // APP, 采用唯一系统编码
                permissionInfo = MrcarUserRpc.getPermissionInfoBySysCode(log.getPermSysCode());
            } else {
                // PC, 采用权限代码
                List<ManagerPermissionDTO> permissionList = MrcarUserRpc.getPermissionListByCodeAndMode(log.getPermissionCode(), log.getMenuOpenMode());
                if (permissionList == null || permissionList.isEmpty()) {
                    continue;
                }
                permissionInfo = permissionList.get(0);
            }
            if (permissionInfo == null
                    || StringUtils.isBlank(permissionInfo.getPermSysCode())) {
                continue;
            }
            log.setPermSysCode(permissionInfo.getPermSysCode());
            log.setPermissionCode(permissionInfo.getPermissionCode());
            log.setPermissionType(permissionInfo.getPermissionType());
            log.setPermissionName(permissionInfo.getPermissionName());
            log.setMenuOpenMode(permissionInfo.getMenuOpenMode());
            // 查找祖先
            Map<Integer, List<ManagerPermissionDTO>> map =
                    MrcarUserRpc.getAncestorPermissionListById(Collections.singletonList(permissionInfo.getPermissionId()));
            if (map == null || !map.containsKey(permissionInfo.getPermissionId())) {
                continue;
            }
            List<ManagerPermissionDTO> ancestors = map.get(permissionInfo.getPermissionId());
            log.setMenuLevel(ancestors.size());
            // 反转层级
            Collections.reverse(ancestors);
            log.setMenuPath(
                    ancestors.stream()
                            .map(ManagerPermissionDTO::getPermissionName)
                            .collect(Collectors.toList()));
            rows.add(log);
        }
        if (!rows.isEmpty()) {
            mongoTemplate.insertAll(rows);
        }
    }

    public PageDTO pageSearchFunctionOperationLog(StatisticsFunctionViewSearchReqDTO reqDTO) {
        long total = 0L;
        Set<String> companyCodes = new HashSet<>(); // 企业编码
        // 查询企业状态
        if (!Check.NuNObj(reqDTO.getCompanyStatus())) {
            CompanyDropDownReqDTO dto = new CompanyDropDownReqDTO();
            dto.setCompanyStatus(reqDTO.getCompanyStatus());
            // 获取到所有公司
            List<CompanyNameRespDTO> companies = MrcarUserRpc.getCompanyListByCondition(dto);
            if (companies.isEmpty()) {
                return new PageDTO(reqDTO.getPage(), reqDTO.getPageSize(), total, Collections.emptyList());
            }
            companyCodes.addAll(companies.stream()
                    .map(CompanyNameRespDTO::getCompanyCode)
                    .collect(Collectors.toList()));
        }
        // 选择了企业编码
        if (!Check.NuNStr(reqDTO.getCompanyCode())) {
            // 条件互斥
            if (!companyCodes.isEmpty()
                    && !companyCodes.contains(reqDTO.getCompanyCode())) {
                return new PageDTO(reqDTO.getPage(), reqDTO.getPageSize(), total, Collections.emptyList());
            }
            companyCodes.clear();
            companyCodes.add(reqDTO.getCompanyCode()); // 仅查询选择的公司
        }
        // 拼接查询条件
        StatisticsFunctionViewSearchDO searchDO = new StatisticsFunctionViewSearchDO();
        // 企业候选范围
        boolean entire = false;
        if (companyCodes.isEmpty()) {
            // 全部企业
            entire = true;
            companyCodes.add("0");
        }
        searchDO.setComapnyCodes(new ArrayList<>(companyCodes));
        // 模糊查询权限名称
        if(StringUtils.isNotBlank(reqDTO.getPermissionNameLike())) {
            List<ManagerPermissionDTO> dtos=MrcarUserRpc.getChildPermListByName(reqDTO.getPermissionNameLike());
            if(CollUtil.isEmpty(dtos)) {
                return new PageDTO(reqDTO.getPage(), reqDTO.getPageSize(), total, Collections.emptyList());
            }
            searchDO.setPermSysCodes(dtos.stream().map(ManagerPermissionDTO::getPermSysCode).collect(Collectors.toList()));
        }
        // 操作范围
        if (!Check.NuNObj(reqDTO.getMenuOpenMode())) {
            searchDO.setMenuOpenMode(reqDTO.getMenuOpenMode());
        }
        // 开始和结束日期
        LocalDate startDay = reqDTO.getStart().toInstant()
                .atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate endDay = reqDTO.getEnd().toInstant()
                .atZone(ZoneId.systemDefault()).toLocalDate();
        // 规则化结束日期, 最大到当前日期的前一天
        LocalDate now = LocalDate.now();
        if (!endDay.isBefore(now)) {
            endDay = now.plusDays(-1);
        }
        // 设置规整时间
        searchDO.setStart(Date.from(startDay.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
        searchDO.setEnd(Date.from(endDay.plusDays(1).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
        // 构造分页信息
        Page<Object> p = PageHelper.startPage(reqDTO.getPage(), reqDTO.getPageSize(), true);
        try {
            // 分页查询
            List<StatisticsFunctionView> list = statisticsFunctionViewExMapper.listByCondition(searchDO);
            // logger.info("查询统计结果, condition : {}, result : {}", searchDO, list);
            total = p.getTotal();
            if (list.isEmpty()) {
                return new PageDTO(reqDTO.getPage(), reqDTO.getPageSize(), total, Collections.emptyList());
            }
            // 如果不是仅限一天的话，或者不是单企业且不是全部企业的情况下, 需重新计算uv
            if (startDay.compareTo(endDay) != 0
                    || (companyCodes.size() != 1)) {
                // 重新通过Mongo进行聚合
                List<StatisticsFunctionViewRespDTO> aggList =
                        aggregateOperationLogByCode(startDay,
                                endDay,
                                entire ? Optional.empty() : Optional.of(new ArrayList<>(companyCodes)),
                                list.stream().map(StatisticsFunctionView::getPermSysCode).collect(Collectors.toList()));
                // key-功能清单
                Map<String, StatisticsFunctionViewRespDTO> statMap =
                        aggList.stream().collect(Collectors.toMap(StatisticsFunctionViewRespDTO::getPermSysCode, Function.identity()));
                // 填充uv值
                list.forEach(s -> {
                    s.setCustomerUv(Optional.ofNullable(statMap.get(s.getPermSysCode()))
                            .map(StatisticsFunctionViewRespDTO::getCustomerUv).orElse(0));
                });
            }
            // 转换dto对象
            // 权限列表
            List<ManagerPermissionDTO> permissionList =
                    MrcarUserRpc.getPermissionListBySysCodes(
                            list.stream().map(StatisticsFunctionView::getPermSysCode).collect(Collectors.toList()));
            Map<String, ManagerPermissionDTO> permissionMap = permissionList
                    .stream()
                    .collect(Collectors.toMap(ManagerPermissionDTO::getPermSysCode, Function.identity()));
            // 日期差
            int days = (int) ChronoUnit.DAYS.between(startDay, endDay) + 1;
            // int days = Period.between(startDay, endDay).getDays() + 1;
            // 获取权限
            List<Integer> permissionIds =
                    permissionMap.values()
                            .stream()
                            .map(ManagerPermissionDTO::getPermissionId)
                            .collect(Collectors.toList());
            Map<Integer, List<ManagerPermissionDTO>> ancestorsMap =
                    MrcarUserRpc.getAncestorPermissionListById(permissionIds);
            List<StatisticsFunctionViewListRespDTO> result =
                    list.stream().map(s -> {
                        StatisticsFunctionViewListRespDTO dto = new StatisticsFunctionViewListRespDTO();
                        dto.setPermSysCode(s.getPermSysCode()); // 权限编码
                        dto.setMenuOpenMode(MenuOpenModeEnum.getByCode(s.getMenuOpenMode()).getDesc()); // 操作端
                        ManagerPermissionDTO permission = permissionMap.get(s.getPermSysCode());
                        // 查找祖先节点
                        if (ancestorsMap != null
                                && ancestorsMap.containsKey(permission.getPermissionId())) {
                            List<ManagerPermissionDTO> ancestors = ancestorsMap.get(permission.getPermissionId());
                            Collections.reverse(ancestors);
                            String name = ancestors.stream()
                                    .map(ManagerPermissionDTO::getPermissionName)
                                    .collect(Collectors.joining("-"));
                            dto.setPermissionName(name); // 权限名称
                        }
                        dto.setPermissionType(PermissionTypeEnum.getByPermissionType(permission.getPermissionType()).getDesc()); // 权限类型
                        dto.setCustomerPv(s.getCustomerPv());
                        dto.setCustomerUv(s.getCustomerUv());
                        dto.setCustomerDailyPv(computeDailyValue(s.getCustomerPv(), days));
                        dto.setCustomerDailyUv(computeDailyValue(s.getCustomerUv(), days));
                        return dto;
                    }).collect(Collectors.toList());
            return new PageDTO(reqDTO.getPage(), reqDTO.getPageSize(), total, result);
        } finally {
            PageHelper.clearPage();
        }
    }

    private String computeDailyValue(int total, int day) {
        return new BigDecimal(total)
                .divide(new BigDecimal(day), 1, RoundingMode.HALF_UP)
                .toPlainString();
    }

    // 统计待导出的记录的条数
    private long countExportLogDetail(StatisticsFunctionViewExportParamDTO param) {
        Query query = new Query();
        // 有企业范围
        if (param.getCompanyCodes() != null) {
            query.addCriteria(Criteria.where("companyCode").in(param.getCompanyCodes()));
        }
        if (!Check.NuNObj(param.getPermSysCode())) {
            query.addCriteria(Criteria.where("permSysCode").is(param.getPermSysCode()));
        }
        LocalDate start = LocalDate.parse(param.getStartStr(), formatter);
        LocalDate end = LocalDate.parse(param.getEndStr(), formatter);
        // 规则化结束日期, 最大到当前日期的前一天
        LocalDate now = LocalDate.now();
        if (!end.isBefore(now)) {
            end = now.plusDays(-1);
        }
        query.addCriteria(Criteria.where("operateTime")
                .gte(Date.from(start.atStartOfDay(ZoneId.systemDefault()).toInstant()))
                .lt(Date.from(end.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant())));
        return mongoTemplate.count(query, StatisticsFunctionViewLog.class);
    }

    public StatisticsFunctionViewExportParamDTO buildExportParam(StatisticsFunctionViewExportReqDTO reqDTO) {
        StatisticsFunctionViewExportParamDTO param = new StatisticsFunctionViewExportParamDTO();
        if (Check.NuNObj(reqDTO.getCompanyStatus())
                && Check.NuNObj(reqDTO.getCompanyCode())) {
            param.setCompanyCodes(null);
        } else {
            // 对应出企业范围
            Set<String> companyCodes = new HashSet<>(); // 企业编码
            // 查询企业状态
            if (!Check.NuNObj(reqDTO.getCompanyStatus())) {
                CompanyDropDownReqDTO dto = new CompanyDropDownReqDTO();
                dto.setCompanyStatus(reqDTO.getCompanyStatus());
                // 获取到所有公司
                List<CompanyNameRespDTO> companies = MrcarUserRpc.getCompanyListByCondition(dto);
                // 不存在对应的公司的范围, 记录为空
                companyCodes.addAll(companies.stream()
                        .map(CompanyNameRespDTO::getCompanyCode)
                        .collect(Collectors.toList()));
            }
            // 选择了企业编码
            if (!Check.NuNObj(reqDTO.getCompanyCode())) {
                // 互斥条件
                if (!companyCodes.isEmpty() &&
                        !companyCodes.contains(reqDTO.getCompanyCode())) {
                    companyCodes.clear();
                } else {
                    companyCodes.clear();
                    companyCodes.add(reqDTO.getCompanyCode());
                }
            }
            param.setCompanyCodes(new ArrayList<>(companyCodes));
        }
        // 权限编码
        param.setPermSysCode(reqDTO.getPermSysCode());
        // 所属企业
        param.setMenuOpenMode(reqDTO.getMenuOpenMode());
        // 设置规整时间
        param.setStartStr(reqDTO.getStartStr());
        param.setEndStr(reqDTO.getEndStr());
        // 通过param返回total数据
        param.setTotal(countExportLogDetail(param));
        return param;
    }


    public PageDTO pageOperationDetail(StatisticsFunctionViewExportParamDTO param) {
        // 分页信息
        Integer page = param.getPage();
        Integer pageSize = param.getPageSize();

        Query query = new Query();
        if (param.getCompanyCodes() != null) {
            query.addCriteria(Criteria.where("companyCode").in(param.getCompanyCodes()));
        }
        LocalDate start = LocalDate.parse(param.getStartStr(), formatter);
        LocalDate end = LocalDate.parse(param.getEndStr(), formatter);
        // 规则化结束日期, 最大到当前日期的前一天
        LocalDate now = LocalDate.now();
        if (!end.isBefore(now)) {
            end = now.plusDays(-1);
        }
        if (!Check.NuNObj(param.getPermSysCode())) {
            query.addCriteria(Criteria.where("permSysCode").is(param.getPermSysCode()));
        }
        query.addCriteria(Criteria.where("operateTime")
                .gte(Date.from(start.atStartOfDay(ZoneId.systemDefault()).toInstant()))
                .lt(Date.from(end.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant())));
        if (!Check.NuNObj(param.getMenuOpenMode())) {
            query.addCriteria(Criteria.where("menuOpenMode").is(param.getMenuOpenMode()));
        }
        query.with(Sort.by(Sort.Direction.ASC, "operateTime"));
        // 先计算总条数
        long count = mongoTemplate.count(query, StatisticsFunctionViewLog.class);
        // Spring-Data默认以0为开始
        query.with(PageRequest.of(page - 1, pageSize));
        // records
        List<StatisticsFunctionViewLog> logs = mongoTemplate.find(query, StatisticsFunctionViewLog.class);
        //  转dto对象
        List<StatisticsFunctionViewLogDetailRespDTO> list =
                BeanUtil.copyList(logs, StatisticsFunctionViewLogDetailRespDTO.class);
        return new PageDTO(page, pageSize, count, list);
    }
    /**
     * 在时间范围内, 根据功能进行聚合
     */
    public List<StatisticsFunctionViewRespDTO> aggregateOperationLogByCode(LocalDate start,
                                                                           LocalDate end,
                                                                           Optional<List<String>> companyCode,
                                                                           Collection<String> sysCodes) {
        // 定义mongo聚合过程
        List<AggregationOperation> pipelines = new ArrayList<>();
        // 时间范围
        pipelines.add(
                Aggregation.match(
                        Criteria.where("operateTime")
                                .gte(Date.from(start.atStartOfDay(ZoneId.systemDefault()).toInstant()))
                                .lt(Date.from(end.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant()))));
        // 筛选客户公司
        if (companyCode.isPresent()) {
            pipelines.add(Aggregation.match(Criteria.where("companyCode").in(companyCode.get())));
        }
        // 筛选功能代码
        if (!sysCodes.isEmpty()) {
            pipelines.add(Aggregation.match(Criteria.where("permSysCode").in(sysCodes)));
        }
        // 按权限code和企业code进行group操作
        pipelines.add(
                Aggregation.group("permSysCode")
                        .first("permissionType").as("permissionType") // 权限类型
                        .first("permissionName").as("permissionName") // 权限名称
                        .first("menuOpenMode").as("menuOpenMode") //
                        .count().as("customerPv")
                        .addToSet("userCode").as("distinctUser"));
        pipelines.add(
                Aggregation.project( "customerPv", "menuOpenMode")
                        .and("_id").as("permSysCode")
                        .and(ArrayOperators.Size.lengthOfArray("distinctUser")).as("customerUv"));
        TypedAggregation<StatisticsFunctionViewLog> aggregation =
                Aggregation.newAggregation(StatisticsFunctionViewLog.class, pipelines);
        logger.info("执行聚合方法, sql : {}", aggregation);
        // 返回统计结果
        AggregationResults<StatisticsFunctionViewRespDTO> results =
                mongoTemplate.aggregate(aggregation, StatisticsFunctionViewRespDTO.class);
        return results.getMappedResults();
    }

    /**
     * 在时间范围内, 根据企业和功能两个维度进行聚合
     */
    public List<StatisticsFunctionViewRespDTO> aggregateOperationLogByCompanyAndCode(LocalDate start,
                                                                                     LocalDate end,
                                                                                     Optional<List<String>> companyCode,
                                                                                     Collection<String> sysCodes) {
        // 定义mongo聚合过程
        List<AggregationOperation> pipelines = new ArrayList<>();
        // 时间范围
        pipelines.add(
                Aggregation.match(
                        Criteria.where("operateTime")
                                .gte(Date.from(start.atStartOfDay(ZoneId.systemDefault()).toInstant()))
                                .lt(Date.from(end.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant()))));
        // 筛选客户公司
        if (companyCode.isPresent()) {
            pipelines.add(Aggregation.match(Criteria.where("companyCode").in(companyCode.get())));
        }
        // 筛选功能代码
        if (!sysCodes.isEmpty()) {
            pipelines.add(Aggregation.match(Criteria.where("permSysCode").in(sysCodes)));
        }
        // 按权限code和企业code进行group操作
        pipelines.add(
                Aggregation.group("companyCode", "permSysCode")
                        .first("permissionType").as("permissionType") // 权限类型
                        .first("permissionName").as("permissionName") // 权限名称
                        .first("menuOpenMode").as("menuOpenMode") //
                        .count().as("customerPv")
                        .addToSet("userCode").as("distinctUser"));
        pipelines.add(
                Aggregation.project("companyCode", "permSysCode", "customerPv", "menuOpenMode")
                                    .and(ArrayOperators.Size.lengthOfArray("distinctUser")).as("customerUv"));
        TypedAggregation<StatisticsFunctionViewLog> aggregation =
                Aggregation.newAggregation(StatisticsFunctionViewLog.class, pipelines);
        logger.info("执行聚合方法, sql : {}", aggregation);
        // 返回统计结果
        AggregationResults<StatisticsFunctionViewRespDTO> results =
                mongoTemplate.aggregate(aggregation, StatisticsFunctionViewRespDTO.class);
        return results.getMappedResults();
    }

    /**
     * 批量插入聚合结果
     */
    public Integer batchInsertStatRecords(List<StatisticsFunctionView> records) {
        if (Check.NuNObj(records) || records.isEmpty()) {
            return 0;
        }
        return statisticsFunctionViewExMapper.batchInsert(records);
    }

    /**
     * 删除特定日期的聚合结果
     */
    public int deletePreStatRecordsByDate(LocalDate date) {
        StatisticsFunctionViewExample example = new StatisticsFunctionViewExample();
        example.createCriteria()
                .andStatDateEqualTo(Date.from(date.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
        return statisticsFunctionViewExMapper.deleteByExample(example);
    }

    public Map<String,String> getFunctionName(List<String> permSysCodes) {
        Map<String,String> permSysCodeNameMap=new HashMap<>();
        if(CollectionUtil.isEmpty(permSysCodes)){
            return permSysCodeNameMap;
        }
        // 转换dto对象
        // 权限列表
        List<ManagerPermissionDTO> permissionList =
                MrcarUserRpc.getPermissionListBySysCodes(permSysCodes);
        Map<String, ManagerPermissionDTO> permissionMap = permissionList
                .stream()
                .collect(Collectors.toMap(ManagerPermissionDTO::getPermSysCode, Function.identity()));
        // 日期差
        // 获取权限
        List<Integer> permissionIds =
                permissionMap.values()
                        .stream()
                        .map(ManagerPermissionDTO::getPermissionId)
                        .collect(Collectors.toList());
        Map<Integer, List<ManagerPermissionDTO>> ancestorsMap =
                MrcarUserRpc.getAncestorPermissionListById(permissionIds);
        for(String permSysCode:permSysCodes){
            ManagerPermissionDTO permission = permissionMap.get(permSysCode);
            // 查找祖先节点
            List<ManagerPermissionDTO> ancestors = ancestorsMap.get(permission.getPermissionId());
            Collections.reverse(ancestors);
            String name = ancestors.stream()
                    .map(ManagerPermissionDTO::getPermissionName)
                    .collect(Collectors.joining("-"));
            permSysCodeNameMap.put(permSysCode,name);
        }

        return permSysCodeNameMap;
    }
    /**
     * 在时间范围内, 根据功能进行聚合
     */
    public PageDTO aggregateOperationLogByCompanyCodeAndPrmSysCode(StatisticsFunctionViewSearchReqDTO reqDTO) {
        // 开始和结束日期
        LocalDate start = reqDTO.getStart().toInstant()
                .atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate end = reqDTO.getEnd().toInstant()
                .atZone(ZoneId.systemDefault()).toLocalDate();
        String permSysCode= reqDTO.getPermSysCode();
        Integer page=reqDTO.getPage();
        Integer size=reqDTO.getPageSize();
        // 日期差
        int days = (int) ChronoUnit.DAYS.between(start, end) + 1;

        final int skip = (page - 1) * size;
        // 定义mongo聚合过程
        List<AggregationOperation> pipelines = new ArrayList<>();
        // 时间范围
        pipelines.add(
                Aggregation.match(
                        Criteria.where("operateTime")
                                .gte(Date.from(start.atStartOfDay(ZoneId.systemDefault()).toInstant()))
                                .lt(Date.from(end.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant()))));
        // 筛选功能代码
        if (StringUtils.isNotBlank(permSysCode)) {
            pipelines.add(Aggregation.match(Criteria.where("permSysCode").is(permSysCode)));
        }
        // 按权限code和企业code进行group操作
        pipelines.add(
                Aggregation.group("permSysCode","companyCode")
                        .first("companyCode").as("companyCode")
                        .first("companyName").as("companyName")
                        .first("permissionType").as("permissionType") // 权限类型
                        .first("permissionName").as("permissionName") // 权限名称
                        .first("menuOpenMode").as("menuOpenMode") //
                        .count().as("customerPv")
                        .addToSet("userCode").as("distinctUser"));
        pipelines.add(
                Aggregation.project( "customerPv", "menuOpenMode","companyCode","companyName")
                        .and(ArrayOperators.Size.lengthOfArray("distinctUser")).as("customerUv"));

        FacetOperation facetOperation = Aggregation.facet()
                .and(Aggregation.skip(skip), Aggregation.limit(size)).as("data")
                .and(Aggregation.count().as("total")).as("totalInfo");
        pipelines.add(facetOperation);

        TypedAggregation<?> aggregation = Aggregation.newAggregation(StatisticsFunctionViewLog.class, pipelines);
        AggregationResults<Document> results = mongoTemplate.aggregate(aggregation, Document.class);

        // 解析分页结果
        Document result = results.getUniqueMappedResult();
        List<StatisticsFunctionViewListRespDTO> dataList = parseData(result);
        long total = parseTotal(result);
        //计算日均
        dataList.forEach(item->{
            item.setCustomerDailyPv(computeDailyValue(item.getCustomerPv(), days));
            item.setCustomerDailyUv(computeDailyValue(item.getCustomerUv(), days));
        });
        return new PageDTO<>(page,size,total,dataList);
    }
    private List<StatisticsFunctionViewListRespDTO> parseData(Document result) {
        List<Document> data = (List<Document>) result.get("data");
        return data.stream()
                .map(item-> JSON.parseObject(JSON.toJSONString(item), StatisticsFunctionViewListRespDTO.class))
                .collect(Collectors.toList());
    }

    private long parseTotal(Document result) {
        List<Document> totalInfo = (List<Document>) result.get("totalInfo");
        return totalInfo.isEmpty() ? 0 : (int) totalInfo.get(0).get("total");
    }

    public PageDTO queryUserPageList(StatisticsFunctionViewSearchReqDTO reqDTO) {
        Set<String> companyCodes = handleParamData(reqDTO);
        // 企业候选范围
        boolean entire = false;
        if (companyCodes.isEmpty()) {
            // 全部企业
            entire = true;
            companyCodes.add("0");
        }
        // 开始和结束日期
        LocalDate startDay = reqDTO.getStart().toInstant()
                .atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate endDay = reqDTO.getEnd().toInstant()
                .atZone(ZoneId.systemDefault()).toLocalDate();
        // 规则化结束日期, 最大到当前日期的前一天
        LocalDate now = LocalDate.now();
        if (!endDay.isBefore(now)) {
            endDay = now.plusDays(-1);
        }
        // 重新通过Mongo进行聚合
        List<StatisticsFunctionViewRespDTO> aggList =
                aggregateOperationLogPageByCode(startDay,
                        endDay,
                        entire ? Optional.empty() : Optional.of(new ArrayList<>(companyCodes)),
                        reqDTO.getPermissionNameLike(),reqDTO.getPhone(),reqDTO.getPage(),reqDTO.getPageSize(),reqDTO.getPermSysCode());
        long total = getTotal(startDay,
                endDay,
                entire ? Optional.empty() : Optional.of(new ArrayList<>(companyCodes)),
                reqDTO.getPermissionNameLike(),reqDTO.getPhone(),reqDTO.getPermSysCode());
        return new PageDTO<>(reqDTO.getPage(), reqDTO.getPageSize(),total,aggList);
    }

    private long getTotal(LocalDate start,
                          LocalDate end,
                          Optional<List<String>> companyCode,
                          String permissionNameLike, String phone,String permSysCode) {
        // 定义mongo聚合过程
        List<AggregationOperation> pipelines = handlePipelines(start,end,companyCode,permissionNameLike,phone,permSysCode);
        // 2. 添加计数阶段
        pipelines.add(Aggregation.group().count().as("total"));
        // 3. 执行计数查询
        Aggregation countAggregation = Aggregation.newAggregation(pipelines);
        AggregationResults<Map> countResult = mongoTemplate.aggregate(
                countAggregation,
                StatisticsFunctionViewLog.class,
                Map.class
        );

        // 4. 获取总记录数
        long totalCount = 0;
        if (!countResult.getMappedResults().isEmpty()) {
            Object object = countResult.getMappedResults().get(0).get("total");
            totalCount = Long.parseLong(object.toString());
        }
        return totalCount;
    }

    private List<AggregationOperation> handlePipelines(LocalDate start,
                                                       LocalDate end,Optional<List<String>> companyCode,
                                                       String permissionNameLike, String phone,String permSysCode) {
        List<AggregationOperation> pipelines = new ArrayList<>();
        // 时间范围
        pipelines.add(
                Aggregation.match(
                        Criteria.where("operateTime")
                                .gte(Date.from(start.atStartOfDay(ZoneId.systemDefault()).toInstant()))
                                .lt(Date.from(end.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant()))));
        // 筛选客户公司
        if (companyCode.isPresent()) {
            pipelines.add(Aggregation.match(Criteria.where("companyCode").in(companyCode.get())));
        }
        if(StringUtils.isNotBlank(phone)) {
            pipelines.add(Aggregation.match(Criteria.where("userMobile").is(phone)));
        }
        // 筛选功能代码
        if (StringUtils.isNotBlank(permSysCode)) {
            pipelines.add(Aggregation.match(Criteria.where("permSysCode").is(permSysCode)));
        }
        // 筛选功能名称
        if(StringUtils.isNotBlank(permissionNameLike)) {
            // 1. 直接构建正则表达式条件（无需 Example）
            String regexPattern = ".*" + permissionNameLike + ".*";

            // 2. 创建针对数组元素的模糊匹配条件
            Criteria criteria = Criteria.where("menuPath")
                    .elemMatch(
                            Criteria.where("$regex").is(regexPattern)
                                    .andOperator(
                                            Criteria.where("$options").is("i") // 忽略大小写
                                    )
                    );
            pipelines.add(Aggregation.match(criteria));
        }
        // 按权限code和企业code进行group操作
        pipelines.add(
                Aggregation.group("_id")
                        .first("userMobile").as("userMobile")
                        .first("operateTime").as("operateTime")
                        .first("userName").as("userName"));
        pipelines.add(
                Aggregation.project( "userMobile", "userName","operateTime")
                        .and("_id").as("permSysCode"));
        pipelines.add(Aggregation.sort(Sort.by(Sort.Direction.ASC, "operateTime")));
        return pipelines;
    }

    private List<StatisticsFunctionViewRespDTO> aggregateOperationLogPageByCode(LocalDate start,
                                                                                LocalDate end,
                                                                                Optional<List<String>> companyCode,
                                                                                String permissionNameLike, String phone,Integer page,Integer pageSize,String permSysCode) {
        // 定义mongo聚合过程
        List<AggregationOperation> pipelines = handlePipelines(start,end,companyCode,permissionNameLike,phone,permSysCode);
        pipelines.add(Aggregation.skip((long) (page - 1) * pageSize) // 分页
        );
        pipelines.add(Aggregation.limit(pageSize)); // 添加limit限制每页数量
        TypedAggregation<StatisticsFunctionViewLog> aggregation =
                Aggregation.newAggregation(StatisticsFunctionViewLog.class, pipelines);
        logger.info("执行聚合方法, sql : {}", aggregation);
        // 返回统计结果
        AggregationResults<StatisticsFunctionViewRespDTO> results =
                mongoTemplate.aggregate(aggregation, StatisticsFunctionViewRespDTO.class);
        return results.getMappedResults();
    }

    private Set<String> handleParamData(StatisticsFunctionViewSearchReqDTO reqDTO){
        Set<String> companyCodes = new HashSet<>(); // 企业编码
        // 查询企业状态
        if (!Check.NuNObj(reqDTO.getCompanyStatus())) {
            CompanyDropDownReqDTO dto = new CompanyDropDownReqDTO();
            dto.setCompanyStatus(reqDTO.getCompanyStatus());
            // 获取到所有公司
            List<CompanyNameRespDTO> companies = MrcarUserRpc.getCompanyListByCondition(dto);
            if (companies.isEmpty()) {
                return Collections.emptySet();
            }
            companyCodes.addAll(companies.stream()
                    .map(CompanyNameRespDTO::getCompanyCode)
                    .collect(Collectors.toList()));
        }
        // 选择了企业编码
        if (!Check.NuNStr(reqDTO.getCompanyCode())) {
            // 条件互斥
            if (!companyCodes.isEmpty()
                    && !companyCodes.contains(reqDTO.getCompanyCode())) {
                return Collections.emptySet();
            }
            companyCodes.clear();
            companyCodes.add(reqDTO.getCompanyCode()); // 仅查询选择的公司
        }
       return companyCodes;
    }
}
