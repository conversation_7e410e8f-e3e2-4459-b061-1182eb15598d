package com.izu.business.service.im.impl;

import com.alibaba.excel.util.CollectionUtils;
import com.izu.business.consts.czcontract.LeaseConstant;
import com.izu.business.dto.im.HumanCustomerRecommenderDTO;
import com.izu.business.dto.im.HumanCustomerRecommenderReqDTO;
import com.izu.business.entity.LeaseContractOrder;
import com.izu.business.entity.LeaseContractOrderExample;
import com.izu.business.util.VehicleLicenseParseUtil;
import com.izu.user.dto.CustomerDetailDTO;
import com.izu.user.dto.provider.staff.ProviderStaffBaseDetailRespDTO;
import com.izu.user.restApi.UserApi;
import mapper.LeaseContractOrderMapper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.aggregation.ArrayOperators;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;

/**
 * 根据车牌号推荐人工客服
 *
 * <AUTHOR> on  2024/6/25 下午1:29
 */
@Component("vehicleLicenseHumanCustomerServiceRecommender")
public class VehicleLicenseHumanCustomerServiceRecommender extends BaseRecommendationRule {

    private static final Logger logger = LoggerFactory.getLogger(VehicleLicenseHumanCustomerServiceRecommender.class);


    @Resource
    private LeaseContractOrderMapper leaseContractOrderMapper;


    @Override
    public HumanCustomerRecommenderDTO findSuitableHumanCustomerByCriteria(HumanCustomerRecommenderReqDTO reqDTO, CustomerDetailDTO customerDetailDTO) throws Exception {
        String message = reqDTO.getMessage();
        //解析车牌号
        String vehicleLicense = VehicleLicenseParseUtil.extractLicensePlates(message);
        return getHumanCustomerByVehicleLicense(vehicleLicense,customerDetailDTO.getCompanyCode());
    }

    @Override
    public Integer getRecommendationMessageType() {
        return 1;
    }


    /**
     * 根据车牌号推荐人工客服
     *
     * @param vehicleLicense 车牌号
     * @return HumanCustomerRecommenderDTO 人工客服的推荐结果
     */
    private HumanCustomerRecommenderDTO getHumanCustomerByVehicleLicense(String vehicleLicense,String companyCode) {
        LeaseContractOrder leaseContractOrder = getLeaseContractOrder(vehicleLicense,companyCode);
        if (leaseContractOrder == null || StringUtils.isBlank(leaseContractOrder.getMaintainStaffMobile())) {
            logger.info("找不到维护人手机号,车牌号为:{},将推荐运营人员为客服", vehicleLicense);
            return null;
        } else {
            //获取维护人ID
            Integer maintainStaffId = leaseContractOrder.getMaintainStaffId();
            HashSet<Integer> restParam = new HashSet<>(Collections.singletonList(maintainStaffId));
            List<ProviderStaffBaseDetailRespDTO> staffDetails = UserApi.batchGetStaffInfoByMgtIds(restParam);
            if (CollectionUtils.isEmpty(staffDetails)) {
                logger.info("订单维护人ID:{}，姓名为:{}的订单维护人状态错误，可能已经离职,将推荐运营人员为客服,车牌号为:{}",
                        maintainStaffId, leaseContractOrder.getMaintainStaffName(), vehicleLicense);
                return null;
            } else {
                HumanCustomerRecommenderDTO humanCustomerRecommenderDTO = mapToHumanCustomerRecommenderDTO(staffDetails.get(0));
                humanCustomerRecommenderDTO.setRecommendReason(getRecommendationText(vehicleLicense));
                return humanCustomerRecommenderDTO;
            }
        }
    }

    //根据车牌号获取在行订单
    private LeaseContractOrder getLeaseContractOrder(String vehicleLicense,String companyCode) {
        //通过车牌号获取在行订单的维护人
        LeaseContractOrderExample example = new LeaseContractOrderExample();
        example.createCriteria()
                .andCustomerCodeEqualTo(companyCode)
                .andVehicleLicenseEqualTo(vehicleLicense)
                .andOrderStatusIn(Arrays.asList(LeaseConstant.LeaseOrderStatusEnum.EXECUTING.getCode(),
                        LeaseConstant.LeaseOrderStatusEnum.RETURNED_CAR.getCode()))
                .andSettleStatusIn(Arrays.asList(LeaseConstant.LeaseSettleStatus.NON_SETTLE.getCode(),
                        LeaseConstant.LeaseSettleStatus.WAIT_SETTLE.getCode()));
        List<LeaseContractOrder> leaseContractOrders = leaseContractOrderMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(leaseContractOrders)) {
            return null;
        } else {
            return leaseContractOrders.get(0);
        }

    }

    @Override
    public String getRecommendationText(String param) {
        return "向客户推荐你的原因：你是" + param + "的订单维护人";
    }
}
