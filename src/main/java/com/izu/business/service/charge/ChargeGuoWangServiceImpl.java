package com.izu.business.service.charge;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.izu.business.consts.ChargeGoFunEnum;
import com.izu.business.dto.gofun.input.ChargeCommonReqDTO;
import com.izu.business.dto.gofun.input.QueryStartChargeReqDTO;
import com.izu.business.dto.gofun.output.*;
import com.izu.business.entity.mongo.charge.ChargeConnectInfo;
import com.izu.business.entity.mongo.charge.ChargePolicyInfo;
import com.izu.business.entity.mongo.charge.ChargeStation;
import com.izu.business.rpc.GuoWangClient;
import com.izu.business.rpc.OrderRpc;
import com.izu.business.util.DateUtil;
import com.izu.business.util.GeoValidator;
import com.izu.business.util.gofun.GuoWangConstantUtils;
import com.izu.business.util.gofun.MapUtils;
import com.izu.framework.web.exception.RestErrorException;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.mrcar.order.consts.charge.ChargeOrderEnum;
import com.izu.user.enums.ChargeChannelEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.geo.Point;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.security.SecureRandom;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 国网接口
 */
@Slf4j
@Service
public class ChargeGuoWangServiceImpl extends ChargeAbstractService {

    @Resource
    private GuoWangClient guoWangClient;
    @Resource
    private GuoWangConstantUtils guoWangConstantUtils;
    @Resource
    private MongoTemplate mongoTemplate;

    @Override
    public ChargeChannelEnum getHandleStrategyChannelEnum() {
        return ChargeChannelEnum.GUO_WANG;
    }

    @Override
    public void doHandel() {
        log.info("国网-----国网----国网--");
    }

    /**
     * 分页查询充电站信息
     */
    @Override
    public PageDTO<ChargeStation> queryStationPage(Integer pageNo, Integer pageSize) {
        Map<String, Object> params = new HashMap<>();
        params.put("PageNo", pageNo);
        params.put("PageSize", pageSize);
        JSONObject jsonObject = guoWangClient.requestGuoWang(guoWangConstantUtils.getQueryStationsInfoUrl(), params, JSONObject.class);
        List<JSONObject> stationJoList = jsonObject.getList("StationInfos", JSONObject.class);
        List<ChargeStation> stationList = formatStation(stationJoList);
        return new PageDTO<>(jsonObject.getIntValue("PageNo"), pageSize, jsonObject.getLong("ItemSize"), stationList);
    }

    /**
     * 查询充电站详情
     **/
    @Override
    public List<ChargeStation> queryStationInfos(String... stationIds) {
        Map<String, Object> params = new HashMap<>();
        params.put("StationIDs", Arrays.stream(stationIds).toArray());
        JSONObject jsonObject = guoWangClient.requestGuoWang(guoWangConstantUtils.getQueryStationsInfoUrl(), params, JSONObject.class);
        List<JSONObject> stationJoList = jsonObject.getList("StationInfos", JSONObject.class);
        if (CollectionUtils.isEmpty(stationJoList)) {
            log.warn("查询国网电站详情StationIDs={}，返回结果为空", JSON.toJSONString(stationIds));
            return new ArrayList<>();
        }
        return formatStation(stationJoList);
    }


    public List<ChargeStation> formatStation(List<JSONObject> result){
        List<ChargeStation> stationList = new ArrayList<>(result.size());

        for (JSONObject stationJo : result) {

            double stationLng = stationJo.getDoubleValue("StationLng");
            double stationLat = stationJo.getDoubleValue("StationLat");
            boolean validInChina = GeoValidator.isValid(stationLng, stationLat);
            if (!validInChina) {
                // 经纬度 不合格
                log.warn("同步国网充电站，经纬度超出范围，忽略此充电站：{}", stationJo);
                continue;
            }

            ChargeStation station = new ChargeStation();
            station.setStationId(stationJo.getString("StationID"));
            station.setStationName(stationJo.getString("StationName"));
            station.setChannelOperatorId(stationJo.getString("OperatorID"));
            station.setAreaCode(stationJo.getString("AreaCode"));
            station.setAddress(stationJo.getString("Address"));
            station.setStationTel(stationJo.getString("StationTel"));
            station.setStationType(stationJo.getIntValue("StationType"));
            station.setStationTypeName(ChargeGoFunEnum.StationType.getNameByCode(station.getStationType()));
            station.setStationStatus(stationJo.getIntValue("StationStatus"));
            station.setStationStatusName(ChargeGoFunEnum.StationStatus.getNameByCode(station.getStationStatus()));
            // 保存的是高德坐标系，需要转换为百度坐标系
            double[] bdStation = MapUtils.convertGCJ2BD(stationLng, stationLat);
            station.setStationLng(bdStation[0]);
            station.setStationLat(bdStation[1]);
            station.setStationLocation(new Point(station.getStationLng(), station.getStationLat()));
            station.setSiteGuide(stationJo.getString("SiteGuide"));
            station.setPictures(stationJo.getList("Pictures", String.class));
            station.setBusinessHours(stationJo.getString("BusineHours"));
            station.setElectricityFee(stationJo.getString("ElectricityFee"));
            //station.setCurrentElectricityFee(stationJo.getString("ElectricityFee"));
            //station.setCurrentServiceFee(stationJo.getString("ServiceFee"));
            station.setServiceFee(stationJo.getString("ServiceFee"));
            station.setParkFee(stationJo.getString("ParkFee"));
            //充电设备信息列表
            // 国网 没有看到 快慢充 标识，默认快充
            int fastUsableChargeNum = 0;
            int slowUsableChargeNum = 0;
            List<ChargeConnectInfo> fastConnectInfoList = new ArrayList<>();
            List<ChargeConnectInfo> slowConnectInfoList = new ArrayList<>();
            List<JSONObject> equipmentInfos = stationJo.getList("EquipmentInfos", JSONObject.class);
            for (JSONObject equipmentInfo : equipmentInfos) {
                int equipmentType = equipmentInfo.getIntValue("EquipmentType");
                String equipmentId = equipmentInfo.getString("EquipmentID");
                // 充电接口/枪
                List<JSONObject> connectorInfos = equipmentInfo.getList("ConnectorInfos", JSONObject.class);
                if (CollectionUtils.isNotEmpty(connectorInfos)) {
                    for (JSONObject connectorInfo : connectorInfos) {
                        ChargeConnectInfo chargeConnectInfo = new ChargeConnectInfo();
                        chargeConnectInfo.setStationId(station.getStationId());

                        chargeConnectInfo.setEquipmentId(equipmentId);
                        chargeConnectInfo.setEquipmentType(equipmentType);
                        chargeConnectInfo.setEquipmentTypeStr(ChargeGoFunEnum.EquipmentType.getNameByCode(chargeConnectInfo.getEquipmentType()));

                        chargeConnectInfo.setConnectorId(connectorInfo.getString("ConnectorID"));
                        chargeConnectInfo.setConnectorName(connectorInfo.getString("ConnectorName"));
                        chargeConnectInfo.setPower(connectorInfo.getDouble("Power"));
                        chargeConnectInfo.setCurrent(connectorInfo.getDouble("Current"));
                        chargeConnectInfo.setStatus(connectorInfo.getInteger("Status"));

                        Integer connectorType = connectorInfo.getInteger("ConnectorType");
                        chargeConnectInfo.setConnectorType(connectorType);
                        chargeConnectInfo.setConnectorTypeStr(ChargeGoFunEnum.ConnectorType.getNameByCode(chargeConnectInfo.getConnectorType()));

                        // 直流是快充
                        if (ChargeGoFunEnum.ConnectorType.DC_INTERFACE_GUN_HEAD.getCode().equals(connectorType)) {
                            if (chargeConnectInfo.getStatus() != null && chargeConnectInfo.getStatus() == 1) {
                                fastUsableChargeNum++;
                            }
                            fastConnectInfoList.add(chargeConnectInfo);
                        } else {
                            if (chargeConnectInfo.getStatus() != null && chargeConnectInfo.getStatus() == 1) {
                                slowUsableChargeNum++;
                            }
                            slowConnectInfoList.add(chargeConnectInfo);
                        }
                    }
                }
            }
            // 快充
            station.setFastConnectInfoList(fastConnectInfoList);
            station.setFastAllChargeConnector(fastConnectInfoList.size());
            station.setFastUsableChargeConnector(fastUsableChargeNum);
            // 慢充
            station.setSlowConnectInfoList(slowConnectInfoList);
            station.setSlowAllChargeConnector(slowConnectInfoList.size());
            station.setSlowUsableChargeConnector(slowUsableChargeNum);
            // 计费信息
            List<JSONObject> policyInfos = stationJo.getList("PolicyInfos", JSONObject.class);
            List<ChargePolicyInfo> chargePolicyList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(policyInfos)) {
                for (int i = 0; i < policyInfos.size(); i++) {
                        JSONObject policyInfoJo = policyInfos.get(i);
                        ChargePolicyInfo chargePolicyInfo = new ChargePolicyInfo();
                        String startTime = policyInfoJo.getString("StartTime");
                        if (StringUtils.isNotBlank(startTime)) {
                            chargePolicyInfo.setStartTime(startTime);
                        }
                        if (i == policyInfos.size() - 1) {
                            // 最后一个时间段，结束时间设为 24:00
                            chargePolicyInfo.setEndTime("24:00");
                        } else {
                            // 下一段的开始时间 作为 本段的结束时间
                            String endTime = policyInfoJo.getString("EndTime");
                            if (StringUtils.isBlank(endTime)) {
                                endTime = policyInfos.get(i + 1).getString("StartTime");
                            }
                            if (StringUtils.isNotBlank(endTime)) {
                                chargePolicyInfo.setEndTime(endTime);
                            }
                        }
                        chargePolicyInfo.setElecPrice(policyInfoJo.getBigDecimal("ElecPrice"));
                        chargePolicyInfo.setServicePrice(policyInfoJo.getBigDecimal("SevicePrice"));
                        chargePolicyList.add(chargePolicyInfo);
                }
            }
            station.setPolicyInfoList(chargePolicyList);
            stationList.add(station);
        }
        return stationList;
    }


    /**
     * 二维码解析
     **/
    @Override
    public ParseQrCodeDTO queryQrcodeData(String qrCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("QRCode", qrCode);
        JSONObject jsonObject = guoWangClient.requestGuoWang(guoWangConstantUtils.getQueryQrcodeDataUrl(), params, JSONObject.class);

        int sucStat = jsonObject.getIntValue("SuccStat");
        if (sucStat != 0) {
            int failReason = jsonObject.getIntValue("FailReason");
            String errorMsg = "";
            switch (failReason) {
                case 80:
                    errorMsg = "二维码校验异常";
                    break;
                case 81:
                    errorMsg = "二维码校验失败";
                    break;
                case 97:
                    errorMsg = "二维码数据为空";
                    break;
                case 98:
                    errorMsg = "系统异常";
                    break;
                case 99:
                    errorMsg = "二维码解析";
            }
            log.warn("国网-二维码解析失败，errorMsg:{},res:{}", errorMsg, jsonObject);
            throw new RestErrorException(errorMsg, RestErrorCode.UNKNOWN_ERROR);
        }

        String connectorId = jsonObject.getString("ConnectorID");
        String identCode = jsonObject.getString("IdentCode");

        Query query = new Query();
        query.addCriteria(
                new Criteria().orOperator(
                        Criteria.where("fastConnectInfoList.connectorId").is(connectorId),
                        Criteria.where("slowConnectInfoList.connectorId").is(connectorId)
                ));
        List<ChargeStation> chargeStations = mongoTemplate.find(query, ChargeStation.class);
        if (CollectionUtils.isEmpty(chargeStations)) {
            log.warn("对应的充电站:{}", query);
            throw new RestErrorException("未找到对应的充电站", RestErrorCode.UNKNOWN_ERROR);
        }
        ChargeStation chargeStation = chargeStations.get(0);
        ParseQrCodeDTO parseQrCodeDTO = new ParseQrCodeDTO();
        parseQrCodeDTO.setConnectorId(connectorId);
        parseQrCodeDTO.setIdentCode(identCode);
        parseQrCodeDTO.setStationId(chargeStation.getStationId());
        parseQrCodeDTO.setOperatorId(chargeStation.getChannelOperatorId());
        List<ChargeConnectInfo> fastConnectInfoList = chargeStation.getFastConnectInfoList();
        for (ChargeConnectInfo chargeConnectInfo : fastConnectInfoList) {
            if (chargeConnectInfo.getConnectorId().equals(connectorId)) {
                parseQrCodeDTO.setEquipmentId(chargeConnectInfo.getEquipmentId());
                break;
            }
        }
        return parseQrCodeDTO;
    }

    /**
     * 查询设备充电状态
     *
     * @param bizNo          业务方订单编号
     * @param startChargeSeq 格式“运营商ID+唯一编号”，27字符 为充电的订单号码
     */
    @Override
    public QueryEquipChargeStatusDTO queryEquipChargeStatus(String bizNo, String startChargeSeq) {
        Map<String, Object> params = new HashMap<>();
        params.put("StartChargeSeq", startChargeSeq);
        JSONObject jsonObject = guoWangClient.requestGuoWang(guoWangConstantUtils.getQueryEquipChargeStatusUrl(), params, JSONObject.class);
        return getQueryEquipChargeStatusDTO(bizNo, jsonObject);
    }

    @NotNull
    private QueryEquipChargeStatusDTO getQueryEquipChargeStatusDTO(String bizNo, JSONObject jsonObject) {
        QueryEquipChargeStatusDTO queryEquipChargeStatusDTO = new QueryEquipChargeStatusDTO();
        // 业务侧充电单号码
        queryEquipChargeStatusDTO.setBizNo(bizNo);
        // 国网侧充电订单号
        queryEquipChargeStatusDTO.setStartChargeSeq(jsonObject.getString("StartChargeSeq"));
        //充电订单状态 1、启动中； 2、充电中； 3、停止中； 4、已结束 5、未知
        queryEquipChargeStatusDTO.setStartChargeSeqStat(convertStartChargeSeqStat(jsonObject.getIntValue("StartChargeSeqStat")));
        queryEquipChargeStatusDTO.setConnectorID(jsonObject.getString("ConnectorID"));
        queryEquipChargeStatusDTO.setConnectorStatus(jsonObject.getIntValue("ConnectorStatus"));
        queryEquipChargeStatusDTO.setCurrentA(jsonObject.getDoubleValue("CurrentA"));
        queryEquipChargeStatusDTO.setCurrentB(jsonObject.getDoubleValue("CurrentB"));
        queryEquipChargeStatusDTO.setCurrentC(jsonObject.getDoubleValue("CurrentC"));
        queryEquipChargeStatusDTO.setVoltageA(jsonObject.getDoubleValue("VoltageA"));
        queryEquipChargeStatusDTO.setVoltageB(jsonObject.getDoubleValue("VoltageB"));
        queryEquipChargeStatusDTO.setVoltageC(jsonObject.getDoubleValue("VoltageC"));
        queryEquipChargeStatusDTO.setSoc(jsonObject.getDoubleValue("Soc"));
        queryEquipChargeStatusDTO.setStartTime(jsonObject.getString("StartTime"));
        queryEquipChargeStatusDTO.setEndTime(jsonObject.getString("EndTime"));

        queryEquipChargeStatusDTO.setTotalPower(jsonObject.getDoubleValue("TotalPower"));
        queryEquipChargeStatusDTO.setElecMoney(jsonObject.getDoubleValue("ElecMoney"));
        queryEquipChargeStatusDTO.setServiceMoney(jsonObject.getDoubleValue("SeviceMoney"));
        queryEquipChargeStatusDTO.setTotalMoney(jsonObject.getDoubleValue("TotalMoney"));

        List<JSONObject> chargeDetails = jsonObject.getList("ChargeDetails", JSONObject.class);
        queryEquipChargeStatusDTO.setChargeDetailList(getChargeDetailList(chargeDetails));
        return queryEquipChargeStatusDTO;
    }

    /**
     * 查询充电订单信息
     */
    @Override
    public QueryChargeOrderInfoDTO queryChargeOrderInfo(ChargeCommonReqDTO reqDTO) {
        String bizNo = reqDTO.getBizNo();
        String startChargeSeq = reqDTO.getStartChargeSeq();
        QueryChargeOrderInfoDTO queryChargeOrderInfoDTO = new QueryChargeOrderInfoDTO();
        QueryEquipChargeStatusDTO chargeStatusDTO = queryEquipChargeStatus(bizNo, startChargeSeq);
        queryChargeOrderInfoDTO.setBizNo(bizNo);
        queryChargeOrderInfoDTO.setStartChargeSeq(chargeStatusDTO.getStartChargeSeq());
        queryChargeOrderInfoDTO.setConnectorId(chargeStatusDTO.getConnectorID());
        queryChargeOrderInfoDTO.setStartTime(chargeStatusDTO.getStartTime());
        queryChargeOrderInfoDTO.setEndTime(chargeStatusDTO.getEndTime());
        queryChargeOrderInfoDTO.setTotalPower(chargeStatusDTO.getTotalPower());
        queryChargeOrderInfoDTO.setTotalElecMoney(chargeStatusDTO.getElecMoney());
        queryChargeOrderInfoDTO.setTotalServiceMoney(chargeStatusDTO.getServiceMoney());
        queryChargeOrderInfoDTO.setTotalMoney(chargeStatusDTO.getTotalMoney());
        queryChargeOrderInfoDTO.setStopReason(chargeStatusDTO.getStopReason());
        List<QueryChargeDetailDTO> chargeDetailList = chargeStatusDTO.getChargeDetailList();
        queryChargeOrderInfoDTO.setChargeDetailList(chargeDetailList);
        return queryChargeOrderInfoDTO;
    }


    public List<QueryChargeDetailDTO> getChargeDetailList(List<JSONObject> chargeDetails) {
        if (CollectionUtil.isEmpty(chargeDetails)) {
            return new ArrayList<>();
        }
        return chargeDetails.stream().map(chargeDetail -> {
            QueryChargeDetailDTO queryChargeDetailDTO = new QueryChargeDetailDTO();
            String detailStartTime = chargeDetail.getString("DetailStartTime");
            queryChargeDetailDTO.setDetailStartTime(DateUtil.getDate(detailStartTime, DateUtil.TIME_FORMAT).getTime());
            String detailEndTime = chargeDetail.getString("DetailEndTime");
            queryChargeDetailDTO.setDetailEndTime(DateUtil.getDate(detailEndTime, DateUtil.TIME_FORMAT).getTime());
            queryChargeDetailDTO.setElecPrice(new BigDecimal(chargeDetail.getString("ElecPrice")));
            queryChargeDetailDTO.setServicePrice(new BigDecimal(chargeDetail.getString("SevicePrice")));
            queryChargeDetailDTO.setDetailPower(new BigDecimal(chargeDetail.getString("DetailPower")));
            queryChargeDetailDTO.setDetailElecMoney(new BigDecimal(chargeDetail.getString("DetailElecMoney")));
            queryChargeDetailDTO.setDetailServiceMoney(new BigDecimal(chargeDetail.getString("DetailSeviceMoney")));
            return queryChargeDetailDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 停止充电
     */
    @Override
    public QueryStopChargeDTO stopCharge(ChargeCommonReqDTO reqDTO) {
        String connectorId = reqDTO.getConnectorId();
        String startChargeSeq = reqDTO.getStartChargeSeq();
        String bizNo = reqDTO.getBizNo();
        Map<String, Object> params = new HashMap<>();
        params.put("StartChargeSeq", startChargeSeq);
        params.put("ConnectorId", connectorId);
        JSONObject jsonObject = guoWangClient.requestGuoWang(guoWangConstantUtils.getQueryStopChargeUrl(), params, JSONObject.class);

        int sucStat = jsonObject.getIntValue("SuccStat");
        if (sucStat != 0) {
            int failReason = jsonObject.getIntValue("FailReason");
            String errorMsg;
            switch (failReason) {
                case 1:
                    errorMsg = "此设备不存在";
                    break;
                case 2:
                    errorMsg = "此设备离线";
                    break;
//                case 3:
//                    errorMsg = "设备已经停止充电";
//                    break;
                default:
                    errorMsg = "自定义";
            }
            log.warn("国网-停止充电，errorMsg:{},res:{}", errorMsg, jsonObject);
            throw new RestErrorException(errorMsg, RestErrorCode.UNKNOWN_ERROR);
        }
        QueryStopChargeDTO queryStopChargeDTO = new QueryStopChargeDTO();
        queryStopChargeDTO.setBizNo(bizNo);
        queryStopChargeDTO.setStartChargeSeq(startChargeSeq);
        queryStopChargeDTO.setStartChargeSeqStat(convertStartChargeSeqStat(jsonObject.getIntValue("StartChargeSeqStat")));
        return queryStopChargeDTO;

    }


    private Integer convertStartChargeSeqStat(Integer startChargeSeqStat) {
        if (startChargeSeqStat == 5) {
            startChargeSeqStat = ChargeOrderEnum.ChargeStatus.INVALID.getCode().intValue();
        }
        return startChargeSeqStat;
    }


    /**
     * 启动充电
     */
    @Override
    public QueryStartChargeDTO startCharge(QueryStartChargeReqDTO reqDTO) {

        // 生成 启动充电订单号 “运营商ID（9）+唯一编号（18）” 27字符
        //timestamp  14 字符
        String timestamp = (new SimpleDateFormat("yyyyMMddHHmmss")).format(new Date());
        String incrRedisKey = "guowang:param:seq:" + timestamp;
        //seq 序列号  4 字符
        String seq = guoWangClient.getSeq(incrRedisKey, timestamp, "%04d");
        // 运营商ID 9字符
        String operatorId = guoWangConstantUtils.getOwnOperatorId();
        // 启动充电订单号 27字符   9+14+4=27
        String startChargeSeq = operatorId + timestamp + seq;

        Map<String, Object> params = new HashMap<>();
        params.put("StartChargeSeq", startChargeSeq);
        params.put("ConnectorID", reqDTO.getConnectorId());
        params.put("UserID", reqDTO.getUserId());
        String qrCode = reqDTO.getQrCode();
        params.put("QRCode", qrCode);
         /*
            解析二维码 判断充电方式
            1.TCU预付费桩：动态二维码（桩上输入预充金额生成二维码） 返回 PreFrozenAmount
            2.TCU后付费桩：动态二维码（桩上不输入预充金额生成二维码
            3.SDK物联桩：固定二维码（静态）
            4.TCU微信首扫二维码：动态二维码，插枪后桩上生成二维码

            2,3,4 需要传递 PreFrozenAmount 预充金额  二维码类型为预设金额型时,必填
         */
        if (!qrCode.matches("^[0-9a-fA-F]{92}$")) {
            // 预设金额 暂定130
            params.put("PreFrozenAmount", guoWangConstantUtils.getPreFrozenAmount());
        }
        JSONObject jsonObject = guoWangClient.requestGuoWang(guoWangConstantUtils.getQueryStartChargeUrl(), params, JSONObject.class);
        int sucStat = jsonObject.getIntValue("SuccStat");
        if (sucStat != 0) {
            int failReason = jsonObject.getIntValue("FailReason");
            String errorMsg;
            switch (failReason) {
                case 1:
                    errorMsg = "此设备不存在";
                    break;
                case 2:
                    errorMsg = "此设备离线";
                    break;
                default:
                    errorMsg = "自定义";
            }
            log.error("国网-启动充电，errorMsg:{},res:{}", errorMsg, jsonObject);
            throw new RestErrorException(errorMsg, RestErrorCode.UNKNOWN_ERROR);

        }
        QueryStartChargeDTO queryStartChargeDTO = new QueryStartChargeDTO();
        queryStartChargeDTO.setBizNo(reqDTO.getBizNo());
        queryStartChargeDTO.setStartChargeSeq(startChargeSeq);
        queryStartChargeDTO.setStartChargeSeqStat(convertStartChargeSeqStat(jsonObject.getIntValue("StartChargeSeqStat")));
        queryStartChargeDTO.setConnectorID(reqDTO.getConnectorId());
        return queryStartChargeDTO;
    }


    /**
     * 推送启动充电结果
     * 实现 controller postBody
     * notification_start_charge_result
     */
    @Override
    public GuoWangClient.ResultDTO notificationStartChargeResult(String authorization, GuoWangClient.RequestDTO requestDTO) {
        Map<String, Object> data = new HashMap<>();
        try {
            //   验证token
            this.checkToken(authorization, requestDTO);
            //1. 验证签名sig 并且 解密data
            JSONObject jsonObject = guoWangClient.guoWangRequestData(requestDTO, JSONObject.class);
            String startChargeSeq = jsonObject.getString("StartChargeSeq");
            Integer startChargeSeqStat = convertStartChargeSeqStat(jsonObject.getIntValue("StartChargeSeqStat"));
            // 启动充电时收到的验证码，用于充电桩手动停止充电 可能为空
            String identCode = jsonObject.getString("IdentCode");
            String connectorId = jsonObject.getString("ConnectorID");
            // yyyy-MM-dd HH:mm:ss
           // String startTime = jsonObject.getString("StartTime");
            int failReason = 0;
            if (jsonObject.containsKey("FailReason")){
                failReason = jsonObject.getIntValue("FailReason");
            }

            //  调用 order  推送充电结果
            QueryStartChargeDTO queryStartChargeDTO = new QueryStartChargeDTO();
            queryStartChargeDTO.setStartChargeSeqStat(startChargeSeqStat);
            queryStartChargeDTO.setStartChargeSeq(startChargeSeq);
            queryStartChargeDTO.setConnectorID(connectorId);
            queryStartChargeDTO.setIdentCode(identCode);
            queryStartChargeDTO.setFailCode(Integer.toString(failReason));
            OrderRpc.chargeRunningCallBack(queryStartChargeDTO);
            data.put("StartChargeSeq", startChargeSeq);
            data.put("SuccStat", 0);
            data.put("FailReason", 0);
            return guoWangClient.encryptAndSignResponse(0, "推送启动充电结果成功", data);
        } catch (Exception e) {
            return guoWangClient.encryptAndSignResponse(1, e.getMessage(), data);
        }

    }


    /**
     *  推送充电状态
     * 实现 controller postBody
     * notification_equip_charge_status
     */
    @Override
    public GuoWangClient.ResultDTO notificationEquipChargeStatus(String authorization, GuoWangClient.RequestDTO requestDTO) {
        Map<String, Object> data = new HashMap<>();
        try {
            //   验证token
            this.checkToken(authorization, requestDTO);
            JSONObject jsonObject = guoWangClient.guoWangRequestData(requestDTO, JSONObject.class);
            QueryEquipChargeStatusDTO chargeStatusDTO = getQueryEquipChargeStatusDTO(null, jsonObject);
            // 调用 order
            OrderRpc.chargeStatusCallBack(chargeStatusDTO);
            data.put("StartChargeSeq", chargeStatusDTO.getStartChargeSeq());
            data.put("SuccStat", 0);
            data.put("FailReason", 0);
            return guoWangClient.encryptAndSignResponse(0, "推送充电状态成功", data);
        } catch (Exception e) {
            return guoWangClient.encryptAndSignResponse(1, e.getMessage(), data);
        }

    }


    @Override
    public GuoWangClient.ResultDTO notificationStationsInfo(String authorization, GuoWangClient.RequestDTO requestDTO) {
        Map<String, Object> data = new HashMap<>();
        try {
            //   验证token
            this.checkToken(authorization, requestDTO);
            JSONObject jsonObject = guoWangClient.guoWangRequestData(requestDTO, JSONObject.class);
            // todo 修改电站 也可以不做
            data.put("Status", 0);
            return guoWangClient.encryptAndSignResponse(0, "推送充电站信息成功", data);
        } catch (Exception e) {
            data.put("Status", 1);
            return guoWangClient.encryptAndSignResponse(1, e.getMessage(), data);
        }

    }


    @Override
    public GuoWangClient.ResultDTO notificationStationStatus(String authorization, GuoWangClient.RequestDTO requestDTO) {
        Map<String, Object> data = new HashMap<>();
        try {
            //   验证token
            this.checkToken(authorization, requestDTO);
            JSONObject jsonObject = guoWangClient.guoWangRequestData(requestDTO, JSONObject.class);
            // todo 修改电站电枪信息 也可以不做

            data.put("Status", 0);
            return guoWangClient.encryptAndSignResponse(0, "推送充电结果成功", data);
        } catch (Exception e) {
            return guoWangClient.encryptAndSignResponse(1, e.getMessage(), data);
        }

    }

    /**
     * 推送停止充电结果
     * 实现 controller postBody
     * notification_stop_charge_result
     */
    @Override
    public GuoWangClient.ResultDTO notificationStopChargeResult(String authorization, GuoWangClient.RequestDTO requestDTO) {
        Map<String, Object> data = new HashMap<>();
        try {
            //   验证token
            this.checkToken(authorization, requestDTO);
            //验证签名sig 并且 解密data
            JSONObject jsonObject = guoWangClient.guoWangRequestData(requestDTO, JSONObject.class);
            String startChargeSeq = jsonObject.getString("StartChargeSeq");
            Integer startChargeSeqStat = convertStartChargeSeqStat(jsonObject.getIntValue("StartChargeSeqStat"));
            // String connectorId = jsonObject.getString("ConnectorID");
            Integer sucStat = jsonObject.getIntValue("SuccStat");
            if (sucStat == 0) {
                // 若果 已停止  调用 order  推送停止充电结果
                if (startChargeSeqStat.equals(ChargeOrderEnum.ChargeStatus.FINISHED.getCode().intValue())) {
                    OrderRpc.chargeStoppedCallBack(startChargeSeq);
                }
            }
            Integer failReason = jsonObject.getIntValue("FailReason");
            data.put("StartChargeSeq", startChargeSeq);
            data.put("SuccStat", sucStat);
            data.put("FailReason", failReason);
            return guoWangClient.encryptAndSignResponse(0, "推送停止充电结果成功", data);
        } catch (Exception e) {
            return guoWangClient.encryptAndSignResponse(1, e.getMessage(), data);
        }

    }

    /**
     * 推送 充电订单信息
     * 实现 controller postBody
     * notification_charge_order_info
     */
    @Override
    public GuoWangClient.ResultDTO notificationChargeOrderInfo(String authorization, GuoWangClient.RequestDTO requestDTO) {
        Map<String, Object> data = new HashMap<>();
        try {
            //   验证token
            this.checkToken(authorization, requestDTO);
            JSONObject jsonObject = guoWangClient.guoWangRequestData(requestDTO, JSONObject.class);
            QueryChargeOrderInfoDTO queryChargeOrderInfoDTO = new QueryChargeOrderInfoDTO();
            //  调用 order   充电订单信息
            queryChargeOrderInfoDTO.setStartChargeSeq(jsonObject.getString("StartChargeSeq"));
            //充电订单状态 1、启动中； 2、充电中； 3、停止中； 4、已结束 5、未知
            queryChargeOrderInfoDTO.setChargeStatus(ChargeOrderEnum.ChargeStatus.FINISHED.getCode().toString());
            queryChargeOrderInfoDTO.setStartTime(jsonObject.getString("StartTime"));
            queryChargeOrderInfoDTO.setEndTime(jsonObject.getString("EndTime"));
            queryChargeOrderInfoDTO.setConnectorId(jsonObject.getString("ConnectorID"));
            queryChargeOrderInfoDTO.setTotalPower(jsonObject.getDoubleValue("TotalPower"));
            queryChargeOrderInfoDTO.setTotalElecMoney(jsonObject.getDoubleValue("TotalElecMoney"));
            queryChargeOrderInfoDTO.setTotalServiceMoney(jsonObject.getDoubleValue("TotalSeviceMoney"));
            queryChargeOrderInfoDTO.setTotalMoney(jsonObject.getDoubleValue("TotalMoney"));
            queryChargeOrderInfoDTO.setStopReason(jsonObject.getIntValue("StopReason"));
            List<JSONObject> chargeDetails = jsonObject.getList("ChargeDetails", JSONObject.class);
            queryChargeOrderInfoDTO.setChargeDetailList(getChargeDetailList(chargeDetails));
            // 调用 order
            OrderRpc.chargeFinishedCallBack(queryChargeOrderInfoDTO);
            data.put("StartChargeSeq", queryChargeOrderInfoDTO.getStartChargeSeq());
            data.put("ConnectorID", queryChargeOrderInfoDTO.getConnectorId());
            data.put("ConfirmResult", 0);
            return guoWangClient.encryptAndSignResponse(0, "推送充电结果成功", data);
        } catch (Exception e) {
            return guoWangClient.encryptAndSignResponse(1, e.getMessage(), data);
        }

    }

    /**
     * 获取token
     */
    @Override
    public GuoWangClient.ResultDTO queryToken(GuoWangClient.RequestDTO requestDTO) {
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("OperatorID", guoWangConstantUtils.getOwnOperatorId());
        dataMap.put("SuccStat", 0);
        dataMap.put("FailReason", 0);

        JSONObject jsonObject = guoWangClient.guoWangRequestData(requestDTO, JSONObject.class);
        String operatorId = jsonObject.getString("OperatorID");
        String operatorSecret = jsonObject.getString("OperatorSecret");

        if (StringUtils.isBlank(operatorId) || !guoWangConstantUtils.getGuoWangOperatorId().equals(operatorId)) {
            dataMap.put("SuccStat", 1);
            return guoWangClient.encryptAndSignResponse(1, "无此运营商", dataMap);
        }
        if (StringUtils.isBlank(operatorSecret) || !guoWangConstantUtils.getOwnOperatorSecret().equals(operatorSecret)) {
            dataMap.put("SuccStat", 1);
            dataMap.put("FailReason", 2);
            return guoWangClient.encryptAndSignResponse(1, "密钥错误", dataMap);
        }
        String accessTokenRedisKey = "charge:accessToken" + operatorId;
        String accessToken;
        if (redisSentinelCache.exists(accessTokenRedisKey)) {
            accessToken = redisSentinelCache.get(accessTokenRedisKey, String.class);
            dataMap.put("AccessToken", accessToken);
        } else {
            // 生成64字符Token
            SecureRandom random = new SecureRandom();
            // 48字节 = 384位，Base64编码后为64字符
            byte[] token = new byte[48];
            random.nextBytes(token);
            accessToken = Base64.getUrlEncoder().withoutPadding().encodeToString(token);
        }
        dataMap.put("AccessToken", accessToken);
        //凭证有效期，单位秒
        dataMap.put("TokenAvailableTime", 500);
        // 将token保存到redis中 设置过期时间 500S
        redisSentinelCache.set(accessTokenRedisKey, accessToken, 500);
        return guoWangClient.encryptAndSignResponse(0, "成功", dataMap);
    }


}
