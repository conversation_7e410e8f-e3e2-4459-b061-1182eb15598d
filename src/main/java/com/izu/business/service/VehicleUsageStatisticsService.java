package com.izu.business.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.izu.asset.restApi.business.BusinessAssetRestApi;
import com.izu.business.common.RestLocators;
import com.izu.business.dto.StatisticsVehicleUsageDTO;
import com.izu.business.dto.ViolationDTO;
import com.izu.business.dto.input.VehicleUsageQueryDto;
import com.izu.business.dto.output.CommonDTO;
import com.izu.business.dto.output.VehicleUsageBIDTO;
import com.izu.business.dto.statistic.VehicleUsageStatisticsDateReq;
import com.izu.business.dto.statistic.VehicleUsageStatisticsDateResp;
import com.izu.business.entity.StatisticsVehicleUsage;
import com.izu.business.entity.VehicleBase;
import com.izu.business.util.DateTimeUtils;
import com.izu.business.util.DateUtil;
import com.izu.carasset.CarAssetRestLocator;
import com.izu.carasset.CarAssetRestUrl;
import com.izu.carasset.dto.output.maintain.MaintainDTO;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.lbs.common.LbsRestLocator;
import com.izu.lbs.common.dto.vehicle.VehicleTotalMileageDTO;
import com.izu.mrcar.iot.dto.StatisVehicleLiChengDTO;
import com.izu.user.dto.company.CompanyDTO;
import com.izu.user.restApi.BusinessUserApi;
import lombok.extern.slf4j.Slf4j;
import mapper.ex.StatisticsVehicleUsageExMapper;
import mapper.ex.VehicleBaseExMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.izu.business.config.errorcode.BusinessErrorCode.CONTRACT_COMMONT_MSG;

/**
* @Description: 车辆使用状况统计服务
* @author: hxc
* @Date: 2021/7/15
**/
@Service
@Slf4j
public class VehicleUsageStatisticsService {

    @Autowired
    private StatisticsVehicleUsageExMapper statisticsVehicleUsageExMapper;

    @Autowired
    private VehicleBaseExMapper vehicleBaseExMapper;

    // 历程数据的上限
    private static final BigDecimal MILEAGE_UPPER_LIMIT = new BigDecimal("1000000");

    public PageDTO vehicleUsage(VehicleUsageQueryDto vehicleUsageReqDTO) {

        List<StatisticsVehicleUsageDTO> statisticsVehicleUsageDTOS;
        int total;
        Page<?> p = PageHelper.startPage(vehicleUsageReqDTO.getPage(), vehicleUsageReqDTO.getPageSize(), true);
        try {
            statisticsVehicleUsageDTOS = statisticsVehicleUsageExMapper.queryVehicleUsage(vehicleUsageReqDTO);
            total = (int)p.getTotal();
        } finally {
            PageHelper.clearPage();
        }
        if(statisticsVehicleUsageDTOS==null || statisticsVehicleUsageDTOS.isEmpty()) {
            return new PageDTO(vehicleUsageReqDTO.getPage(), vehicleUsageReqDTO.getPageSize(), total ,  Collections.emptyList() );
        }

        statisticsVehicleUsageDTOS = statisticsVehicleUsageDTOS.stream().map(statisticsVehicleUsageDTO -> {
            statisticsVehicleUsageDTO.setDayTravelDurationString(DateUtil.getTimeDurationHM(statisticsVehicleUsageDTO.getDayTravelDuration()));
            if(statisticsVehicleUsageDTO.getUseOil() == null || statisticsVehicleUsageDTO.getUseOil().compareTo(BigDecimal.ZERO) == 0){
                statisticsVehicleUsageDTO.setDayTravelOil("-");
            }else{
                statisticsVehicleUsageDTO.setDayTravelOil(statisticsVehicleUsageDTO.getUseOil().toString());
            }
            CompanyDTO companyDTO = BusinessUserApi.getCompanyBycompanyId(statisticsVehicleUsageDTO.getCompanyId());
            statisticsVehicleUsageDTO.setCompanyName(companyDTO.getCompanyName());
            return statisticsVehicleUsageDTO;
        }).collect(Collectors.toList());

        // 关联车牌号,历史车辆型号为空的情况进行查询
        Set<String> vehicleVins = statisticsVehicleUsageDTOS.stream().map(StatisticsVehicleUsageDTO::getVehicleVin).collect(Collectors.toSet());
        List<StatisticsVehicleUsageDTO> list = vehicleBaseExMapper.selectVehicleModelByVehicleVin(vehicleVins);
        if (CollectionUtils.isNotEmpty(list)) {
            Map<String, String> map = list.stream().collect(Collectors.toMap(StatisticsVehicleUsageDTO::getVehicleVin, StatisticsVehicleUsageDTO::getVehicleModel));
            statisticsVehicleUsageDTOS.stream().forEach(e -> e.setVehicleModel(map.get(e.getVehicleVin())));
        }
        return new PageDTO(vehicleUsageReqDTO.getPage(),vehicleUsageReqDTO.getPageSize(),total,statisticsVehicleUsageDTOS);
    }

    /**
     * 统计车辆使用状况任务处理
     * @param statisDateDay
     */
    public void statisVehicleUsageJobExcute(String statisDateDay,List<Integer> companyIdList) throws ParseException {

        int pageNum=1;
        int pageSize = 10;
        while(true){
            // 查询车辆信息,todo 排除私有车辆？
            PageInfo<VehicleBase> pageInfo = PageHelper.startPage(pageNum, pageSize, false)
                    .doSelectPageInfo(() -> vehicleBaseExMapper.selectVehicles(companyIdList));
            List<VehicleBase> vehicleBaseList = pageInfo.getList();
            if(CollectionUtils.isEmpty(vehicleBaseList)){
                return;
            }
            statLogic(vehicleBaseList,statisDateDay);
            pageNum ++;
        }

    }

    private void statLogic(List<VehicleBase> vehicleBaseList,String statisDateDay) throws ParseException {
        String vehicleVins = vehicleBaseList.stream().map(VehicleBase::getVehicleVin).collect(Collectors.joining(","));
        String vehicleLicenses = vehicleBaseList.stream().map(VehicleBase::getVehicleLicense).collect(Collectors.joining(","));
        // 查询违章总数量
        Map<String, List<ViolationDTO>> violationsmap = getViolation(vehicleVins,statisDateDay);
        // 查询维保总数
        Map<String, List<MaintainDTO>> maintainMap = getMaintain(vehicleBaseList,statisDateDay);
        // 查询汇总行程总里程
        Map<String, List<StatisVehicleLiChengDTO>> totalLichengMap = getTotalMile(vehicleLicenses,statisDateDay);

        for (VehicleBase vehicleBase:vehicleBaseList) {
            Long vehicleId = vehicleBase.getVehicleId();
            String vehicleLicense = vehicleBase.getVehicleLicense();
            String vehicleVin = vehicleBase.getVehicleVin();
            Integer companyId = vehicleBase.getCompanyId();
            StatisticsVehicleUsage statisticsVehicleUsage = new StatisticsVehicleUsage();
            statisticsVehicleUsage.setVehicleId(Integer.valueOf(vehicleId+""));
            statisticsVehicleUsage.setVehicleLicense(vehicleLicense);
            statisticsVehicleUsage.setVehicleVin(vehicleVin);
            statisticsVehicleUsage.setCompanyId(companyId);
            statisticsVehicleUsage.setStatisticsDate(DateUtil.parse(statisDateDay,DateUtil.DATE_FORMAT));
            statisticsVehicleUsage.setStructId(vehicleBase.getStructId());
            statisticsVehicleUsage.setStructName(vehicleBase.getStructName());
            statisticsVehicleUsage.setBelongCityCode(vehicleBase.getBelongCityCode());
            statisticsVehicleUsage.setBelongCityName(vehicleBase.getBelongCityName());
            List<ViolationDTO> violationDTOS = violationsmap.get(vehicleVin);
            if (violationDTOS != null && violationDTOS.size() > 0){
                statisticsVehicleUsage.setViolationCount(violationDTOS.get(0).getViolationCount());
            }
            List<MaintainDTO> maintainDTOS = maintainMap.get(vehicleVin);
            if (maintainDTOS != null && maintainDTOS.size() > 0){
                statisticsVehicleUsage.setMaintainCount(maintainDTOS.get(0).getMaintainCount());
            }
            List<StatisVehicleLiChengDTO> statisVehicleLiChengDTOS = totalLichengMap.get(vehicleLicense);
            if (statisVehicleLiChengDTOS != null && statisVehicleLiChengDTOS.size() > 0){
                StatisVehicleLiChengDTO statisVehicleLiChengDTO = statisVehicleLiChengDTOS.get(0);
                statisticsVehicleUsage.setTotalMileage(new BigDecimal(statisVehicleLiChengDTO.getMileageTotal()));
                // 汇总行程总时长
                statisticsVehicleUsage.setDayTravelDuration(
                        statisVehicleLiChengDTO.getTravelDuration() == null ? 0 : statisVehicleLiChengDTO.getTravelDuration().intValue());
                // 汇总当天的轨迹总里程
                statisticsVehicleUsage.setDayTravelMileage(statisVehicleLiChengDTO.getDayTravelMileage());
                statisticsVehicleUsage.setDayTravelOil(statisVehicleLiChengDTO.getOil());
                if (StringUtils.isNotBlank(statisVehicleLiChengDTO.getTboxTotalMileage())) {
                    // 总里程
                    BigDecimal totalMileage = new BigDecimal(statisVehicleLiChengDTO.getTboxTotalMileage());
                    if (totalMileage.abs().compareTo(MILEAGE_UPPER_LIMIT) >= 0) {
                        totalMileage = BigDecimal.ZERO;
                    }
                    statisticsVehicleUsage.setHisTotalMileage(totalMileage);

                }
                if (StringUtils.isNotBlank(statisVehicleLiChengDTO.getTboxDayMileage())) {
                    // 每日里程
                    BigDecimal dailyMileage = new BigDecimal(statisVehicleLiChengDTO.getTboxDayMileage());
                    if (dailyMileage.abs().compareTo(MILEAGE_UPPER_LIMIT) >= 0) {
                        dailyMileage = BigDecimal.ZERO;
                    }
                    statisticsVehicleUsage.setDayTotalMileage(dailyMileage);
                }
            }
            // 统计数据落库
            saveVehicleUsage(statisticsVehicleUsage);
        }
    }

    private void saveVehicleUsage(StatisticsVehicleUsage statisticsVehicleUsage) {
        StatisticsVehicleUsage isExist = statisticsVehicleUsageExMapper.selectByVehicleVinAndStatisticsDate(statisticsVehicleUsage.getVehicleVin(),statisticsVehicleUsage.getStatisticsDate());
        if (isExist != null){
            statisticsVehicleUsage.setId(isExist.getId());
            statisticsVehicleUsageExMapper.updateByPrimaryKeySelective(statisticsVehicleUsage);
        }else {
            statisticsVehicleUsageExMapper.insertSelective(statisticsVehicleUsage);
        }
    }

    private Map<String, List<ViolationDTO>> getViolation(String vehicleVins,String statisDateDay){
        Map<String, List<ViolationDTO>> violationsmap = new HashMap<>();
        RestResponse restResponse = BusinessAssetRestApi.getViolationCountByVin(vehicleVins, statisDateDay);
        if(restResponse.isSuccess()){
            List<ViolationDTO> violations = JSON.parseArray(JSON.toJSONString(restResponse.getData()),ViolationDTO.class);
            if (CollectionUtils.isNotEmpty(violations)){
                violationsmap = violations.stream().collect(Collectors.groupingBy(ViolationDTO::getVehicleVin));
            }
        }
        return violationsmap;
    }

    private Map<String, List<MaintainDTO>> getMaintain(List<VehicleBase> vehicleBaseList,String statisDateDay){
        List<VehicleBase> ownVehicleList = vehicleBaseList.stream().filter(v -> v.getOwnCompanyId() == 1).collect(Collectors.toList());
        List<VehicleBase> otherVehicleList = vehicleBaseList.stream().filter(v -> v.getOwnCompanyId() != 1).collect(Collectors.toList());
        Map<String, List<MaintainDTO>> maintainMap = new HashMap<>();
        if (otherVehicleList != null && otherVehicleList.size() > 0 ){
            // 查询维保
            String otherVehicleVins = otherVehicleList.stream().map(VehicleBase::getVehicleVin).collect(Collectors.joining(","));
            List<MaintainDTO> maintainDTOS = BusinessAssetRestApi.getMaintainCount(otherVehicleVins,statisDateDay);
            if (CollectionUtils.isNotEmpty(maintainDTOS)){
                maintainMap = maintainDTOS.stream().collect(Collectors.groupingBy(MaintainDTO::getVehicleVin));
            }
        }
        if (ownVehicleList != null && ownVehicleList.size() > 0 ){
            String ownVehicleVins = ownVehicleList.stream().map(VehicleBase::getVehicleVin).collect(Collectors.joining(","));
            // 查询综合管理维保数据
            Map<String, Object> params = new HashMap<>();
            params.put("vehicleVins",ownVehicleVins);
            params.put("statisDateDay",statisDateDay);
            String restUrl = new CarAssetRestLocator().getRestUrl(CarAssetRestUrl.MAINTAIN_QUERY_COUNT);
            RestResponse result = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, params, null, com.izu.carasset.dto.output.maintain.MaintainDTO.class);
            if (result.isSuccess()){
                List<MaintainDTO> data = (List<MaintainDTO>) result.getData();
                if (data != null && data.size() > 0 ){
                    Map<String, List<MaintainDTO>> dataMap = data.stream().collect(Collectors.groupingBy(MaintainDTO::getVehicleVin));
                    maintainMap.putAll(dataMap);
                }
            }
        }
        return maintainMap;
    }

    private Map<String, List<StatisVehicleLiChengDTO>> getTotalMile(String vehicleLicenses,String statisDateDay){
        Map<String, List<StatisVehicleLiChengDTO>> totalLichengMap = new HashMap<>();
        Map<String, Object> params = new HashMap<>();
        params.put("vehicleLicenses", vehicleLicenses);
        params.put("dateStart", statisDateDay);
        params.put("dateEnd", statisDateDay);
        params.put("pageNo", 1);
        params.put("pageSize", 100);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POST, RestLocators.iot().getRestUrl("/liChengStatistic/listForPage"), params, null, StatisVehicleLiChengDTO.class);
        if (restResponse.isSuccess()){
            PageDTO pageDTO = (PageDTO) restResponse.getData();
            if (pageDTO != null){
                List<StatisVehicleLiChengDTO>  data = pageDTO.getResult();
                if (data != null && data.size() > 0 ){
                    totalLichengMap = data.stream().collect(Collectors.groupingBy(StatisVehicleLiChengDTO::getVehicleLicense));
                }
            }
        }
        return totalLichengMap;
    }

    private Map<String, List<VehicleTotalMileageDTO>> getHisMile(String vehicleVins){
        //误差点在查的是最新的数据，统计日期在昨天
        Map<String, List<VehicleTotalMileageDTO>> hisTotalLichengMap = new HashMap<>();
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("vehicleVin", vehicleVins);
        RestResponse result = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, new LbsRestLocator().getRestUrl("/vehicle/get/totalMileage"), paramsMap, null, VehicleTotalMileageDTO.class);
        log.info("历史总里程查询：params={},result={}",paramsMap.toString(), JSON.toJSONString(result));
        if (result.isSuccess()){
            List<VehicleTotalMileageDTO>  data = (List<VehicleTotalMileageDTO>) result.getData();
            if (data != null && data.size() > 0 ){
                hisTotalLichengMap = data.stream().collect(Collectors.groupingBy(VehicleTotalMileageDTO::getVehicleVin));
            }
        }
        return hisTotalLichengMap;
    }

    public RestResponse totalVehicleUsage(VehicleUsageQueryDto vehicleUsageReqDTO) {
        VehicleUsageBIDTO usageBIDTO = new VehicleUsageBIDTO();
        BigDecimal totalMileageCurrent = BigDecimal.ZERO;
        BigDecimal totalMileagePri = BigDecimal.ZERO;
        long totalTravelDurationCurrent = 0;
        long totalTravelDurationPri = 0;
        BigDecimal totalTravelOilCurrent = BigDecimal.ZERO;
        BigDecimal totalTravelOilPri = BigDecimal.ZERO;

        CommonDTO mileage = new CommonDTO();
        CommonDTO duration = new CommonDTO();
        CommonDTO oil = new CommonDTO();

        //本期原始数据查询
        List<StatisticsVehicleUsageDTO> currentTerm = statisticsVehicleUsageExMapper.queryVehicleUsage(vehicleUsageReqDTO);
        //上期原始数据查询
        //计算本期时间段间隔多少天
        int days = DateTimeUtils.nDaysBetweenTwoDate(vehicleUsageReqDTO.getStatisticsDateStart(),vehicleUsageReqDTO.getStatisticsDateEnd())+1;
        try {
            //获取上个周期的开始时间和结束时间
            String datePriStart = DateTimeUtils.dateDiffToday(vehicleUsageReqDTO.getStatisticsDateStart(),-days);
            String datePriEnd = DateTimeUtils.dateDiffToday(vehicleUsageReqDTO.getStatisticsDateStart(),-1);
            vehicleUsageReqDTO.setStatisticsDateStart(datePriStart);
            vehicleUsageReqDTO.setStatisticsDateEnd(datePriEnd);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        List<StatisticsVehicleUsageDTO> priTerm = statisticsVehicleUsageExMapper.queryVehicleUsage(vehicleUsageReqDTO);
        if(CollectionUtils.isNotEmpty(currentTerm)) {
            totalMileageCurrent = currentTerm.stream().filter(Objects::nonNull).filter(c->null!=c.getTotalMileage()).map(StatisticsVehicleUsageDTO::getTotalMileage).reduce(BigDecimal.ZERO,BigDecimal::add);
            totalTravelDurationCurrent = currentTerm.stream().filter(Objects::nonNull).filter(c->null!=c.getDayTravelDuration()).map(StatisticsVehicleUsageDTO::getDayTravelDuration).reduce(0,Integer::sum);
            totalTravelOilCurrent = currentTerm.stream().filter(Objects::nonNull).filter(c->null!=c.getUseOil()).map(StatisticsVehicleUsageDTO::getUseOil).reduce(BigDecimal.ZERO,BigDecimal::add);
        }
        if(CollectionUtils.isNotEmpty(priTerm)){
            totalMileagePri = priTerm.stream().filter(Objects::nonNull).filter(c->null!=c.getTotalMileage()).map(StatisticsVehicleUsageDTO::getTotalMileage).reduce(BigDecimal.ZERO,BigDecimal::add);
            totalTravelDurationPri = priTerm.stream().filter(Objects::nonNull).filter(c->null!=c.getDayTravelDuration()).map(StatisticsVehicleUsageDTO::getDayTravelDuration).reduce(0,Integer::sum);
            totalTravelOilPri = priTerm.stream().filter(Objects::nonNull).filter(c->null!=c.getUseOil()).map(StatisticsVehicleUsageDTO::getUseOil).reduce(BigDecimal.ZERO,BigDecimal::add);
        }

        //结果转换
        mileage.setTotal(getFormatString(totalMileageCurrent));
        mileage.setChain(getChain(totalMileageCurrent,totalMileagePri));
        //将本期总时长转化为天数，四舍五入保留两位小数
        float durationCurrent =(float)totalTravelDurationCurrent/(60*60*24);
        BigDecimal b = BigDecimal.valueOf(durationCurrent);
        duration.setTotal(getFormatString(b));
        duration.setChain(getChainOfLong(totalTravelDurationCurrent,totalTravelDurationPri));

        oil.setTotal(getFormatString(totalTravelOilCurrent));
        oil.setChain(getChain(totalTravelOilCurrent,totalTravelOilPri));

        usageBIDTO.setTotalMile(mileage);
        usageBIDTO.setTotalRunTime(duration);
        usageBIDTO.setTotalOil(oil);

        return RestResponse.success(usageBIDTO);
    }

    private static final Double rateMax = 99.99;
    private static final float rateMaxFloat = (float) 99.99;
    private static final double w = 10000;

    private String getChain(BigDecimal current,BigDecimal pri){
        if(BigDecimal.ZERO.compareTo(current)==0 && BigDecimal.ZERO.compareTo(pri)==0){
            //两者皆为0
            return "0%";
        }else if(BigDecimal.ZERO.compareTo(current)!=0 && BigDecimal.ZERO.compareTo(pri)!=0) {
            //都不为0，进行计算，数值大于9999%时展示∞
            BigDecimal res = current.subtract(pri).divide(pri,4,BigDecimal.ROUND_HALF_UP);
            NumberFormat percent = NumberFormat.getPercentInstance();
            percent.setMaximumFractionDigits(1);
            if(res.compareTo(BigDecimal.valueOf(rateMax))>0){
                return "∞";
            }else {
                if(res.compareTo(BigDecimal.ZERO) > 0){
                    return "+"+percent.format(res);
                }
                return percent.format(res);
            }
        }else if(BigDecimal.ZERO.compareTo(current)==0 && BigDecimal.ZERO.compareTo(pri)!=0) {
            //本期数据为0，上期不为0
            return "-100%";
        }else {
            return "∞";
        }
    }

    private String getChainOfInt(Integer current,Integer pri){
        if(current == 0 && pri==0){
            //两者皆为0
            return "0%";
        }else if(current!=0 && pri!=0) {
            //都不为0，进行计算，数值大于9999%时展示∞
            float res =(float)(current-pri)/pri;
            NumberFormat percent = NumberFormat.getPercentInstance();
            percent.setMaximumFractionDigits(1);
            if(res>rateMaxFloat){
                return "∞";
            }else {
                if(res>0){
                    return "+"+percent.format(res);
                }
                return percent.format(res);
            }
        }else if(current==0 && pri!=0) {
            //本期数据为0，上期不为0
            return "-100%";
        }else {
            return "∞";
        }
    }

    private String getChainOfLong(long current,long pri){
        if(current == 0 && pri==0){
            //两者皆为0
            return "0%";
        }else if(current!=0 && pri!=0) {
            //都不为0，进行计算，数值大于9999%时展示∞
            float res =(float)(current-pri)/pri;
            NumberFormat percent = NumberFormat.getPercentInstance();
            percent.setMaximumFractionDigits(1);
            if(res>rateMaxFloat){
                return "∞";
            }else {
                if(res>0){
                    return "+"+percent.format(res);
                }
                return percent.format(res);
            }
        }else if(current==0 && pri!=0) {
            //本期数据为0，上期不为0
            return "-100%";
        }else {
            return "∞";
        }
    }

    private String getFormatString(BigDecimal input){
        if(null==input || input.equals(BigDecimal.ZERO)){
            return "0";
        }
        if(input.compareTo(BigDecimal.valueOf(w))>0){
            //转为以万为单位，并保留两位小数
            return input.divide(BigDecimal.valueOf(w)).setScale(2,BigDecimal.ROUND_HALF_UP).toPlainString()+"万";
        }else {
            return input.setScale(2,BigDecimal.ROUND_HALF_UP).toPlainString();
        }
    }


    public RestResponse getDateList(VehicleUsageStatisticsDateReq vehicleUsageStatisticsDateReq) {
        if (StringUtils.isBlank(vehicleUsageStatisticsDateReq.getStartDate()) || StringUtils.isBlank(vehicleUsageStatisticsDateReq.getEndDate())) {
            return RestResponse.fail(CONTRACT_COMMONT_MSG, "缺少必要参数");

        }
        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy/MM/dd");
        SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd");

        try {
            // 解析原始日期
            Date startDate = inputFormat.parse(vehicleUsageStatisticsDateReq.getStartDate());
            Date endDate = inputFormat.parse(vehicleUsageStatisticsDateReq.getEndDate());

            // 格式化为目标格式
            String formattedStartDate = outputFormat.format(startDate);
            String formattedEndDate = outputFormat.format(endDate);

            vehicleUsageStatisticsDateReq.setStartDate(formattedStartDate);
            vehicleUsageStatisticsDateReq.setEndDate(formattedEndDate);

            List<VehicleUsageStatisticsDateResp> vehicleUsageStatisticsList = statisticsVehicleUsageExMapper.getVehicleUsageByVehicleLicense(vehicleUsageStatisticsDateReq);
            if (CollectionUtils.isEmpty(vehicleUsageStatisticsList)) {
                return RestResponse.success(Lists.newArrayList());
            }
            vehicleUsageStatisticsList.forEach(r -> {
                Date statDate = r.getStatDate();
                if (Objects.nonNull(statDate)) {
                    r.setStatDateStr(DateUtil.date2String(statDate, DateUtil.DAY_FORMAT));
                }
                if (Objects.nonNull(r.getDayTravelMileage()) && r.getDayTravelMileage().compareTo(BigDecimal.ZERO) > 0) {
                    r.setDayTravelMileage(r.getDayTravelMileage().setScale(0, RoundingMode.HALF_UP));
                }
            });
            return RestResponse.success(vehicleUsageStatisticsList);
        } catch (Exception e) {
            log.warn("getDateList error", e);
            return RestResponse.fail(CONTRACT_COMMONT_MSG, "日期格式错误");
        }
    }
}
