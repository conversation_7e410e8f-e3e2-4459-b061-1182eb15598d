package com.izu.business.service.mrcarUseContract;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableSet;
import com.izu.business.config.errorcode.BusinessErrorCode;
import com.izu.business.consts.ContractChargeEnum;
import com.izu.business.consts.MrcarUseContractEnum;
import com.izu.business.dto.czcontract.output.BussContractDetailDTO;
import com.izu.business.dto.mrcarUseContract.BillingInfoFileDTO;
import com.izu.business.dto.mrcarUseContract.BillingOperateRecordDTO;
import com.izu.business.dto.mrcarUseContract.input.*;
import com.izu.business.dto.mrcarUseContract.output.BillingInfoPageResDTO;
import com.izu.business.dto.mrcarUseContract.output.BillingInfoResDTO;
import com.izu.business.dto.mrcarUseContract.output.ContractInfoResDTO;
import com.izu.business.dto.provider.output.ContractOtherSubjectOutputDTO;
import com.izu.business.entity.*;
import com.izu.business.exception.BusinessRunningException;
import com.izu.business.rpc.AssetRpc;
import com.izu.business.rpc.CrmRpc;
import com.izu.business.service.czcontract.bussContract.BussContractQueryService;
import com.izu.business.util.DateUtil;
import com.izu.carasset.dto.output.StructAssetDTO;
import com.izu.crm.CrmRestLocator;
import com.izu.crm.CrmRestUrl;
import com.izu.crm.dto.output.CrmSigningSubjectInfoDTO;
import com.izu.crm.enums.MrCarContractChargeEnum;
import com.izu.erplease.dto.input.bill.BillItemDTO;
import com.izu.erplease.dto.input.order.BCashBackMessageDTO;
import com.izu.erplease.enums.BillTypeEnum;
import com.izu.erplease.enums.OrderEnum;
import com.izu.framework.rocketmq.CommonRocketProducer;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.exception.RestErrorException;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.mrcar.order.consts.OrderMq;
import com.izu.user.dto.CompanyPaidRespDTO;
import com.izu.user.dto.CompanyPaidSaveDTO;
import com.izu.user.dto.CompanyPaidServiceQueryDTO;
import com.izu.user.enums.paid.CompanyPaidContractTypeEnum;
import com.izu.user.enums.paid.CooperationStatusEnum;
import com.izu.user.enums.paid.PaidBussinessTypeEnum;
import com.izu.user.enums.paid.PaymentBusinessTypeEnum;
import com.izu.user.restApi.CompanyApi;
import com.izu.user.restApi.CompanyPaidApi;
import lombok.extern.slf4j.Slf4j;
import mapper.MrCarBillingFileMapper;
import mapper.MrCarBillingInfoMapper;
import mapper.MrCarBillingOperateRecordMapper;
import mapper.ex.ContractAuthAccountExMapper;
import mapper.ex.ContractOtherSubjectExMapper;
import mapper.ex.LeaseContractBussExMapper;
import mapper.ex.MrCarBillingInfoExMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.DiffBuilder;
import org.apache.commons.lang3.builder.DiffResult;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

@Service
@Slf4j
public class MrCarBillingInfoService {
    @Resource
    private MrCarBillingInfoExMapper mrCarBillingInfoExMapper;
    @Resource
    private MrCarBillingFileService mrCarBillingFileService;
    @Resource
    private MrCarBillingOperateRecordService mrCarBillingOperateRecordService;
    @Resource
    private MrCarBillingOperateRecordMapper mrCarBillingOperateRecordMapper;
    @Resource
    private MrCarBillingFileMapper mrCarBillingFileMapper;
    @Resource
    private MrCarBillingInfoMapper mrCarBillingInfoMapper;
    @Resource
    private ContractAuthAccountExMapper contractAuthAccountExMapper;
    @Resource(name = "carAssertProducer")
    private CommonRocketProducer carAssetProducer;
    @Resource
    private BussContractQueryService bussContractQueryService;
    @Resource
    private LeaseContractBussExMapper leaseContractBussExMapper;
    @Resource
    private ContractOtherSubjectExMapper contractOtherSubjectExMapper;

    /**
     * 账单列表查询
     * @param dto 查询实体
     * @return RestResponse
     */
    public PageDTO<BillingInfoPageResDTO> pageList(BillingInfoQueryReqDTO dto) {
        Integer pageNum = dto.getPage();
        Integer pageSize = dto.getPageSize();
        try (Page<MrCarBillingInfo> ignored = PageHelper.startPage(pageNum, pageSize, true)) {
            //查询列表
            List<MrCarBillingInfo> mrcarBillingInfos = mrCarBillingInfoExMapper.selectByQueryDTO(dto);

            // 转换为响应DTO列表
            List<BillingInfoPageResDTO> billingInfoPageResDTOList = BeanUtil.copyList(mrcarBillingInfos, BillingInfoPageResDTO.class);

            return new PageDTO<>(pageNum, pageSize, ignored.getTotal(), billingInfoPageResDTOList);
        } catch (Exception e) {
            log.error("账单列表查询失败", e);
            return new PageDTO<>(pageNum, pageSize, 0, Collections.emptyList());
        } finally {
            PageHelper.clearPage(); // 确保清理线程变量
        }
    }

    /**
     * 查询详情
     * @param id 主键id
     * @return 单据详情
     */
    public BillingInfoResDTO detail(Integer id) {
        //1、查询账单信息
        MrCarBillingInfo mrCarBillingInfo=mrCarBillingInfoMapper.selectByPrimaryKey(id);
        if(null==mrCarBillingInfo){
            log.warn("未找到对应账单信息: id={}", id);
            return null;
        }
        BillingInfoResDTO billingInfoResDTO=BeanUtil.copyObject(mrCarBillingInfo, BillingInfoResDTO.class);
        //查询合同是否是mrcar使用合同
        ContractAuthAccount contractAuthAccount = contractAuthAccountExMapper.getContractAuthAccount(mrCarBillingInfo.getContractCode());
        if(contractAuthAccount==null){
            billingInfoResDTO.setContractId(null);
        } else {
            billingInfoResDTO.setContractId(contractAuthAccount.getId());
        }
        if(billingInfoResDTO.getContractType()!=null){
            billingInfoResDTO.setContractTypeStr(CompanyPaidContractTypeEnum.getName(billingInfoResDTO.getContractType().byteValue()));
        }
        // 2. 并行查询附件和操作记录（无依赖关系时）
        CompletableFuture<List<BillingInfoFileDTO>> fileFuture = CompletableFuture.supplyAsync(() -> {
            List<MrCarBillingFile> files = mrCarBillingFileService.getListByBillIdAndType(
                    id, MrcarUseContractEnum.FileTypeEnum.BILL_ATTACHMENT.getCode());
            return BeanUtil.copyList(files, BillingInfoFileDTO.class);
        });
        billingInfoResDTO.setAdjustFlag(0);
        CompletableFuture<List<BillingOperateRecordDTO>> recordFuture = CompletableFuture.supplyAsync(() -> {
            List<MrCarBillingOperateRecord> records = mrCarBillingOperateRecordService.getListByBillId(id);
            List<BillingOperateRecordDTO> recordDTOS = BeanUtil.copyList(records, BillingOperateRecordDTO.class);
            if(CollUtil.isEmpty(recordDTOS)){
                return Collections.emptyList();
            }
            recordDTOS.forEach(x->{
                if(!Objects.equals(MrcarUseContractEnum.OperateTypeEnum.ADJUST_BILL.getCode(),x.getOperateType())){
                    return;
                }
                List<MrCarBillingFile> files = mrCarBillingFileService.getListByBillIdAndType(
                        x.getId(), MrcarUseContractEnum.FileTypeEnum.PRICE_ADJUSTMENT_ATTACHMENT.getCode());
                x.setBillingOperateRecordFileDTOList( BeanUtil.copyList(files, BillingInfoFileDTO.class));
                billingInfoResDTO.setAdjustFlag(1);
            });
            return recordDTOS;
        });

        // 等待结果并设置到DTO
        try {
            billingInfoResDTO.setBillingInfoFileDTOList(fileFuture.get());
            billingInfoResDTO.setBillingOperateRecordDTOList(recordFuture.get());
        } catch (InterruptedException | ExecutionException e) {
            log.error("账单详情查询异步处理失败", e);
            Thread.currentThread().interrupt();
        }
        return billingInfoResDTO;
    }

    /**
     * 单据废弃
     * @param dto 操作实体
     * @return RestResponse
     */
    @Transactional
    public RestResponse cancel(BillingInfoOpReqDTO dto){
        //1、查询账单信息
        MrCarBillingInfo mrCarBillingInfo=mrCarBillingInfoMapper.selectByPrimaryKey(dto.getId());
        if(null==mrCarBillingInfo){
            log.warn("未找到对应账单信息: id={}",dto.getId());
            return RestResponse.fail(BusinessErrorCode.USE_CONTRACT_BILL_NOT_EXISTS);
        }
        // 2. 校验状态是否允许废弃
        Byte currentStatus = mrCarBillingInfo.getBillStatus();
        Set<Byte> allowedStatuses = ImmutableSet.of(
                MrcarUseContractEnum.BillStatusEnum.PENDING_BILLING.getCode(),
                MrcarUseContractEnum.BillStatusEnum.PENDING_PUSH.getCode()
        );
        if (!allowedStatuses.contains(currentStatus)) {
            log.warn("账单状态不合法, 无法废弃: id={}, currentStatus={}", dto.getId(), currentStatus);
            return RestResponse.fail(BusinessErrorCode.USE_CONTRACT_BILL_STATUS_ERROR_NOT_CANCEL);
        }

        //3、更新账单信息
        MrCarBillingInfo updateInfo=new MrCarBillingInfo();
        updateInfo.setId(dto.getId());
        updateInfo.setBillStatus(MrcarUseContractEnum.BillStatusEnum.DISCARDED.getCode());
        updateInfo.setUpdateId(String.valueOf(dto.getLoginUserId()));
        updateInfo.setUpdateName(dto.getLoginUserName());
        updateInfo.setUpdateTime(new Date());
        int updateResult=mrCarBillingInfoMapper.updateByPrimaryKeySelective(updateInfo);
        if(updateResult==0){
            log.error("更新账单状态失败: id={}", dto.getId());
            throw new BusinessRunningException("更新账单状态失败");
        }

        //4、记录操作日志
        MrCarBillingOperateRecord operateRecord=new MrCarBillingOperateRecord();
        operateRecord.setBillId(dto.getId());
        operateRecord.setCreateId(dto.getLoginUserId());
        operateRecord.setCreateName(dto.getLoginUserName());
        operateRecord.setOperateType(MrcarUseContractEnum.OperateTypeEnum.DISCARD_BILL.getCode());
        mrCarBillingOperateRecordMapper.insertSelective(operateRecord);
        //判断是否最后一期如果是则推送已还车，不是则不用推送
        boolean flag = existNoPushBillInfoList(mrCarBillingInfo.getOrderCode(),mrCarBillingInfo.getId());
        if(flag && mrCarBillingInfo.getOrderTotalPeriod()!=1){
            sendBillToLease(mrCarBillingInfo,Boolean.TRUE);
        }
        return RestResponse.success(null);
    }

    /**
     * 调价
     * @param dto 入参
     * @return RestResponse
     */
    public RestResponse adjustAmount(BillingInfoAdjustAmountReqDTO dto){
        //1、查询账单信息
        MrCarBillingInfo mrCarBillingInfo=mrCarBillingInfoMapper.selectByPrimaryKey(dto.getId());
        if(null==mrCarBillingInfo){
            log.warn("未找到对应账单信息: id={}",dto.getId());
            return RestResponse.fail(BusinessErrorCode.USE_CONTRACT_BILL_NOT_EXISTS);
        }
        // 2. 校验状态是否允许调价
        Byte currentStatus = mrCarBillingInfo.getBillStatus();
        Set<Byte> allowedStatuses = ImmutableSet.of(
                MrcarUseContractEnum.BillStatusEnum.PENDING_BILLING.getCode(),
                MrcarUseContractEnum.BillStatusEnum.PENDING_PUSH.getCode()
        );
        if (!allowedStatuses.contains(currentStatus)) {
            log.warn("账单状态不合法, 无法调价: id={}, currentStatus={}", dto.getId(), currentStatus);
            return RestResponse.fail(BusinessErrorCode.USE_CONTRACT_BILL_STATUS_ERROR_NOT_ADJUST_AMOUNT);
        }
        // 3. 更新账单信息
        MrCarBillingInfo updateInfo= buildUpdatedBillInfo(dto,mrCarBillingInfo);
        int updateResult=mrCarBillingInfoExMapper.updateByPrimaryKeySelective(updateInfo);
        if(updateResult==0){
            log.error("更新账单失败: id={}", dto.getId());
            throw new BusinessRunningException("更新账单失败");
        }
        // 4. 记录操作日志
        MrCarBillingOperateRecord operateRecord=mrCarBillingOperateRecordService.saveOperationRecord(dto.getId()
                ,MrcarUseContractEnum.OperateTypeEnum.ADJUST_BILL.getCode(),
                dto.getLoginUserId(),
                dto.getLoginUserName(),
                generateChangeLog(mrCarBillingInfo, updateInfo));

        // 5. 处理调价附件
        processAdjustmentAttachments(dto, operateRecord.getId());

        return RestResponse.success(null);
    }
    // 构建更新对象
    private MrCarBillingInfo buildUpdatedBillInfo(BillingInfoAdjustAmountReqDTO dto,MrCarBillingInfo mrCarBillingInfo) {
        MrCarBillingInfo update = new MrCarBillingInfo();
        update.setId(dto.getId());
        update.setFinanceStructCode(dto.getFinanceStructCode());
        update.setFinanceStructName(dto.getFinanceStructName());
        update.setOperateCode(dto.getOperateCode());
        update.setOperateName(dto.getOperateName());
        update.setAdjustReason(dto.getAdjustReason());
        update.setPaymentDueDate(dto.getPaymentDueDate());
        update.setUpdateId(String.valueOf(dto.getLoginUserId()));
        update.setUpdateName(dto.getLoginUserName());
        update.setAdjustAmount(dto.getAdjustAmount());
        update.setUpdateTime(new Date());
        //账单结束日期
        Date billEdate=mrCarBillingInfo.getBillEdate();
        //判断应回款日期是否发生变化 如果发生，付款类型变成后付 重新计算账期 否则不变
        Date newDate = dto.getPaymentDueDate();
        Date oldDate = mrCarBillingInfo.getPaymentDueDate();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        if(!Objects.equals(sdf.format(newDate),sdf.format(oldDate))){
            update.setPaymentType(ContractChargeEnum.PaymentTypeEnum.POST_PAYMENT.getCode());
            update.setBillingPeriodDays(calculateDateDifference(sdf.format(billEdate),sdf.format(newDate)));
        }
        return update;
    }

    public static Integer calculateDateDifference(String billEdateStr, String paymentDueDateStr) {
        long num;
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate date1 = LocalDate.parse(billEdateStr, formatter);
        LocalDate date2 = LocalDate.parse(paymentDueDateStr, formatter);
        if (date1.isBefore(date2)) {
            num = ChronoUnit.DAYS.between(date1, date2);
        } else {
            num = -ChronoUnit.DAYS.between(date2, date1);
        }
        return (int) num;
    }
    // 生成变更记录
    private JSONArray generateChangeLog(MrCarBillingInfo origin, MrCarBillingInfo updated) {
        //比对字段属性
        DiffResult<MrCarBillingInfo> diff = new DiffBuilder<>(origin, updated, ToStringStyle.SHORT_PREFIX_STYLE)
                .append("financeStructName", origin.getFinanceStructName(), updated.getFinanceStructName())
                .append("operateName", origin.getOperateName(), updated.getOperateName())
                .append("adjustAmount", origin.getAdjustAmount(), updated.getAdjustAmount())
                .append("paymentDueDate", DateUtil.format(origin.getPaymentDueDate(),DateUtil.DATE_FORMAT), DateUtil.format(updated.getPaymentDueDate(),DateUtil.DATE_FORMAT))
                .append("adjustReason", "", updated.getAdjustReason())
                .build();

        JSONArray changes = new JSONArray();
        diff.getDiffs().forEach(change ->
                changes.add(new JSONObject()
                        .fluentPut("fieldName", change.getFieldName())
                        .fluentPut("left", change.getLeft())
                        .fluentPut("right", change.getRight()))
        );
        return changes;
    }
    // 处理附件保存
    private void processAdjustmentAttachments(BillingInfoAdjustAmountReqDTO dto, Integer billId) {
        Optional.ofNullable(dto.getBillingInfoFileDTOList()).ifPresent(attachments ->
                attachments.forEach(attachment -> {
                            MrCarBillingFile mrCarBillingFile = BeanUtil.copyObject(attachment, MrCarBillingFile.class);
                            mrCarBillingFile.setBillId(billId);
                            mrCarBillingFile.setFileType(MrcarUseContractEnum.FileTypeEnum.PRICE_ADJUSTMENT_ATTACHMENT.getCode());
                            mrCarBillingFileMapper.insertSelective(mrCarBillingFile);
                        }
                )
        );
    }


    /**
     * 账单新建
     */
    @Transactional
    public RestResponse create(BillingInfoCreateReqDTO dto) {
        //生成账单表数据
        MrCarBillingInfo mrCarBillingInfo = handleBillInfo(dto);
        //根据合同类型和关联合同code 查询合同是否存在
        if (Objects.equals(dto.getContractType(),CompanyPaidContractTypeEnum.MR_CAR_USAGE_CONTRACT.getCode().intValue())) {
            ContractAuthAccount contractDetailDTO = contractAuthAccountExMapper.getContractAuthAccount(dto.getContractCode());
            if (Objects.isNull(contractDetailDTO)) {
                return RestResponse.fail(BusinessErrorCode.CONTRACT_COMMONT_MSG, "没有此类型的合同编码，请查证后重试");
            }
            mrCarBillingInfo.setMaintainId(contractDetailDTO.getMaintainId());
            mrCarBillingInfo.setMaintainName(contractDetailDTO.getMaintainName());
            mrCarBillingInfo.setContractId(contractDetailDTO.getContractId());
            mrCarBillingInfo.setSignSalesId(contractDetailDTO.getSignSalesId());
            mrCarBillingInfo.setSignSalesName(contractDetailDTO.getSignSalesName());
        } else {
            BussContractDetailDTO bussContractDetailDTO = queryContractDetail(dto.getContractCode());
            if(bussContractDetailDTO==null){
                return RestResponse.fail(BusinessErrorCode.CONTRACT_COMMONT_MSG, "没有此类型的合同编码，请查证后重试");
            }
            mrCarBillingInfo.setMaintainId(bussContractDetailDTO.getMaintainId());
            mrCarBillingInfo.setMaintainName(bussContractDetailDTO.getMaintainName());
            mrCarBillingInfo.setContractId(bussContractDetailDTO.getBussContractId());
            mrCarBillingInfo.setSignSalesId(bussContractDetailDTO.getSignSalesId());
            mrCarBillingInfo.setSignSalesName(bussContractDetailDTO.getSignSalesName());
        }
        //新增订单
        CompanyPaidSaveDTO companyPaidSaveDTO = addPaidServiceData(dto, mrCarBillingInfo);
        String orderNo = CompanyPaidApi.insertPaidService(companyPaidSaveDTO);
        if(StringUtils.isBlank(orderNo)){
            throw ExceptionFactory.createRestException(BusinessErrorCode.CONTRACT_COMMONT_MSG,"创建增值业务失败");
        }
        mrCarBillingInfo.setOrderCode(orderNo);
        mrCarBillingInfo.setBillCode(orderNo+"-1");
        mrCarBillingInfoMapper.insertSelective(mrCarBillingInfo);
        //新增账单附件
        processCreateAttachments(dto,mrCarBillingInfo.getId());
        //新增操作记录
        mrCarBillingOperateRecordService.saveOperationRecord(mrCarBillingInfo.getId()
                ,MrcarUseContractEnum.OperateTypeEnum.CREATE_BILL.getCode(),
                dto.getLoginUserId(),
                dto.getLoginUserName(),
                null);
        return RestResponse.success(null);
    }

    private CompanyPaidSaveDTO addPaidServiceData(BillingInfoCreateReqDTO dto, MrCarBillingInfo mrCarBillingInfo) {
        CompanyPaidSaveDTO contractServices = new CompanyPaidSaveDTO();
        contractServices.setCompanyId(dto.getCompanyId());
        contractServices.setCompanyCode(dto.getCustomerCode());
        contractServices.setCompanyName(dto.getCustomerName());
        contractServices.setContractCode(dto.getContractCode());
        contractServices.setBusinessType(PaidBussinessTypeEnum.MANUAL_RECORD.getCode());
        contractServices.setStartDate(dto.getBillSdate());
        contractServices.setExpirationDate(dto.getBillEdate());
        contractServices.setSignSalesId(mrCarBillingInfo.getSignSalesId());
        contractServices.setSignSalesName(mrCarBillingInfo.getSignSalesName());
        contractServices.setMaintainId(mrCarBillingInfo.getMaintainId());
        contractServices.setMaintainName(mrCarBillingInfo.getMaintainName());
        contractServices.setSalesEntityCreditCode(dto.getFinanceStructCode());
        contractServices.setSigningEntityCreditCode(dto.getOperateCode());
        Date contractSdate = dto.getBillSdate();
        LocalDate localDate = contractSdate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        int dayOfMonth = localDate.getDayOfMonth();
        contractServices.setBillingDayOfMonth((byte) dayOfMonth);
        contractServices.setContractCategory(dto.getContractType().byteValue());
        contractServices.setSalesEntityFinancialCode(dto.getFinanceStructCode());
        contractServices.setSalesEntityFinancialName(dto.getFinanceStructName());
        contractServices.setSigningEntityFinancialCode(dto.getOperateCode());
        contractServices.setSigningEntityFinancialName(dto.getOperateName());
        contractServices.setPrice(dto.getUnitPrice());
        contractServices.setPaymentType(mrCarBillingInfo.getPaymentType());
        contractServices.setBillingCycle(mrCarBillingInfo.getBillingPeriodDays());
        contractServices.setPaymentMethod(mrCarBillingInfo.getPaymentType());
        // 计算合作状态
        contractServices.setCooperationStatus(CooperationStatusEnum.NOT_STARTED.getCode());
        if(Objects.nonNull(contractServices.getStartDate()) && contractServices.getStartDate().before(new Date())){
            contractServices.setCooperationStatus(CooperationStatusEnum.COOPERATING.getCode());
        }
        if(Objects.nonNull(contractServices.getExpirationDate()) && contractServices.getExpirationDate().before(new Date())){
            contractServices.setCooperationStatus(CooperationStatusEnum.EXPIRED.getCode());
        }
        if(Objects.equals(dto.getBillType(), ContractChargeEnum.MrCarContractChargeFeeEnum.SYSTEM_USAGE_FEE.getPaidBusinessCode().byteValue())){
            contractServices.setPaidServiceType(PaymentBusinessTypeEnum.FIXED_PRICE.getCode());
        } else {
            contractServices.setPaidServiceType(dto.getBillType());
        }
        contractServices.setCreatorId(dto.getLoginUserId());
        contractServices.setCreatorName(dto.getLoginUserName());
        contractServices.setCreationTime(new Date());
        contractServices.setModifierId(dto.getLoginUserId());
        contractServices.setModifierName(dto.getLoginUserName());
        contractServices.setModificationTime(new Date());
        return contractServices;
    }

    private BussContractDetailDTO queryContractDetail(String contractCode) {
        try {
            RestResponse restResponse = bussContractQueryService.queryDetail(contractCode);
            if(restResponse==null ||!restResponse.isSuccess() ||restResponse.getData()==null){
                return null;
            }
            return (BussContractDetailDTO)restResponse.getData();
        } catch (Exception e) {
            log.warn(e.getMessage());
        }
        return null;
    }

    private MrCarBillingInfo handleBillInfo(BillingInfoCreateReqDTO dto) {
        MrCarBillingInfo mrCarBillingInfo = BeanUtil.copyObject(dto, MrCarBillingInfo.class);
        mrCarBillingInfo.setCreateId(String.valueOf(dto.getLoginUserId()));
        mrCarBillingInfo.setCreateName(dto.getLoginUserName());
        mrCarBillingInfo.setUpdateId(mrCarBillingInfo.getCreateId());
        mrCarBillingInfo.setCreateTime(new Date());
        mrCarBillingInfo.setUpdateTime(mrCarBillingInfo.getCreateTime());
        mrCarBillingInfo.setUpdateName(dto.getLoginUserName());
        //出账方式
        mrCarBillingInfo.setBillingMethod(MrcarUseContractEnum.BillingMethodEnum.BY_MANUAL_CREATION.getCode());
        mrCarBillingInfo.setAdjustAmount(dto.getBillAmount());
        //出账日期
        mrCarBillingInfo.setBillingDate(mrCarBillingInfo.getCreateTime());
        //支付周期
        mrCarBillingInfo.setPaymentCycle(MrcarUseContractEnum.PaymentCycleEnum.ONE_TIME.getCode());
        //账单期数
        mrCarBillingInfo.setBillPeriod(1);
        mrCarBillingInfo.setBillTotalPeriod(1);
        //订单总期数
        mrCarBillingInfo.setOrderPeriod(1);
        mrCarBillingInfo.setOrderTotalPeriod(1);
        //应回款日期
        mrCarBillingInfo.setPaymentDueDate(com.izu.business.util.DateUtil.addDay(mrCarBillingInfo.getBillEdate(), dto.getBillingPeriodDays()));
        mrCarBillingInfo.setCreateMethod(MrcarUseContractEnum.CreateMethodEnum.MANUALLY_CREATED.getCode());
        mrCarBillingInfo.setPaymentType(ContractChargeEnum.PaymentTypeEnum.POST_PAYMENT.getCode());
        //判断账单状态
        Date date = new Date();
        Date billDate = com.izu.business.util.DateUtil.addDay(mrCarBillingInfo.getBillEdate(), 1);
        mrCarBillingInfo.setBillStatus(MrcarUseContractEnum.BillStatusEnum.PENDING_BILLING.getCode());
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try{
            if(sdf.parse(sdf.format(date)).compareTo(sdf.parse(sdf.format(billDate)))>=0){
                mrCarBillingInfo.setBillStatus(MrcarUseContractEnum.BillStatusEnum.PENDING_PUSH.getCode());
            }
        }catch (Exception e){
            log.warn(e.getMessage());
        }
        return mrCarBillingInfo;
    }

    // 处理附件保存
    private void processCreateAttachments(BillingInfoCreateReqDTO dto, Integer billId) {
        Optional.ofNullable(dto.getFileDTOList()).ifPresent(attachments ->
                attachments.forEach(attachment -> {
                            MrCarBillingFile mrCarBillingFile = BeanUtil.copyObject(attachment, MrCarBillingFile.class);
                            mrCarBillingFile.setBillId(billId);
                            mrCarBillingFile.setFileType(MrcarUseContractEnum.FileTypeEnum.BILL_ATTACHMENT.getCode());
                            mrCarBillingFileMapper.insertSelective(mrCarBillingFile);
                        }
                )
        );
    }

    /**
     * 推送账单到长租
     */
    public void mrCarBillPushToLease(String param) {
        //查询待推送或推送失败的数据
       List<MrCarBillingInfo> mrCarBillingInfoList = mrCarBillingInfoExMapper.queryBillList();
       if(CollUtil.isEmpty(mrCarBillingInfoList)){
           return;
       }
        mrCarBillingInfoList.forEach(x->{
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date date = com.izu.business.util.DateUtil.addDay(x.getBillEdate(), 1);
            String dateStr = sdf.format(date);
            String nowDateStr = sdf.format(new Date());
            if(StringUtils.isBlank(param)){
                if(!Objects.equals(dateStr,nowDateStr)){
                    return;
                }
            } else {
                if(!Objects.equals(param,x.getBillCode())){
                    return;
                }
            }
            try{
                sendBillToLease(x,Boolean.FALSE);
            }catch (Exception e){
                log.warn("推送账单到综合失败",e);
            }
        });
    }

    private void sendBillToLease(MrCarBillingInfo billingInfo,boolean flag) {
        //组装参数
        BCashBackMessageDTO dto = convertToPushMessage(billingInfo,flag);
        log.info("MrCar系统合同收入账单推送,推送内容：{}", JSON.toJSONString(dto));
        Boolean result = carAssetProducer.publishMessageOrderly(OrderMq.ORDER_SETTLE_PUSH_MQ, "bill05", dto.getBillCode(), Collections.singletonList(dto));
        log.info("MrCar系统合同账单推送,收入账单号:{},推送结果：{}", dto.getBillCode(), result);
        if(flag){
            return;
        }
        //更新推送状态
        MrCarBillingInfo mrCarBillingInfo = new MrCarBillingInfo();
        mrCarBillingInfo.setId(billingInfo.getId());
        Byte code = result ? MrcarUseContractEnum.BillStatusEnum.PUSHED.getCode() : MrcarUseContractEnum.BillStatusEnum.PUSH_FAIL.getCode();
        mrCarBillingInfo.setBillStatus(code);
        mrCarBillingInfoExMapper.updateByPrimaryKeySelective(mrCarBillingInfo);

    }



    private BCashBackMessageDTO convertToPushMessage(MrCarBillingInfo billingInfo,boolean billFlag) {
        BCashBackMessageDTO dto = new BCashBackMessageDTO();
        //合同号，默认传总收入账单号
        dto.setContractCode(billingInfo.getOrderCode());
        //合同、订单、账单开始&结束时间，均默认传账单开始&结束时间
        dto.setContractSdate(billingInfo.getBillSdate());
        dto.setContractEdate(billingInfo.getBillEdate());
        //判断是否手工创建 1是  则取账单开始日期账单结束日期，否则查询增值业务
        if(Objects.equals(billingInfo.getCreateMethod(), MrcarUseContractEnum.CreateMethodEnum.MANUALLY_CREATED.getCode())){
            dto.setOrderSdate(billingInfo.getBillSdate());
            dto.setOrderEdate(billingInfo.getBillEdate());
        } else {
            CompanyPaidServiceQueryDTO dto2 = new CompanyPaidServiceQueryDTO();
            dto2.setOrderNo(billingInfo.getOrderCode());
            dto2.setCompanyCode(billingInfo.getCustomerCode());
            CompanyPaidRespDTO byOrderNo = CompanyPaidApi.getByOrderNo(dto2);
            dto.setOrderSdate(byOrderNo.getStartDate());
            dto.setOrderEdate(byOrderNo.getExpirationDate());
        }
        dto.setBillSdate(billingInfo.getBillSdate());
        dto.setBillEdate(billingInfo.getBillEdate());
        dto.setTotalRent(billingInfo.getAdjustAmount());
        //订单号、账单号均默认传单车收入账单号
        dto.setOrderCode(billingInfo.getOrderCode());
        if(!billFlag){
            dto.setBillCode(billingInfo.getBillCode());
        }
        //将Mr.Car平台的编码转换成CRM的客户编码
        String crmCode;
        try {
            crmCode = CompanyApi.convertMrCarCodeToCrmCode(billingInfo.getCustomerCode());
        } catch (RestErrorException e) {
            log.error("转换CRM客户编码失败,响应code={},msg={}", e.getErrCode(), e.getErrMsg());
            throw ExceptionFactory.createRestException("转换CRM客户编码失败，请联系管理员！");
        }
        if (StringUtils.isBlank(crmCode)) {
            log.error("客户名称:{} 客户编码:{} 没有对应的CRM客户编码，无法推送账单，需要人工确认", billingInfo.getCustomerName(), billingInfo.getCustomerCode());
            throw ExceptionFactory.createRestException("客户:" + billingInfo.getCustomerName() + "没有维护CRM的客户编码，无法推账，请联系管理员！");
        }
        dto.setCustomerCode(crmCode);

        dto.setCustomerName(billingInfo.getCustomerName());
        dto.setBelongBussCode(billingInfo.getOperateCode());
        dto.setBelongBussName(billingInfo.getOperateName());
        dto.setFinanceStructCode(billingInfo.getOperateCode());
        dto.setSigningSubjectName(billingInfo.getOperateName());
        dto.setFinanceStructName(billingInfo.getOperateName());
        dto.setOperateBussCode(billingInfo.getOperateCode());
        dto.setOperateBussName(billingInfo.getOperateName());
        CrmSigningSubjectInfoDTO crmSigningSubjectInfoDTO = CrmRpc.queryList(null, null,billingInfo.getOperateCode());
        if(crmSigningSubjectInfoDTO==null){
            throw new BusinessRunningException("签约主体为空不支持推送");
        }
        StructAssetDTO operateBussStruct = AssetRpc.getStructAssetByCode(billingInfo.getOperateCode());
        if (operateBussStruct != null) {
            dto.setMaintainStructCode(operateBussStruct.getStructBuss().getBelongCityCode());
            dto.setMaintainStructName(operateBussStruct.getStructBuss().getBelongCityName());
        }
        dto.setBusinessLicense(crmSigningSubjectInfoDTO.getBusinessLicense());
        //订单所有人传账单确认人
        dto.setCreateId(billingInfo.getSignSalesId());
        dto.setCreateName(billingInfo.getSignSalesName());
        //月租金默认传账单金额
        dto.setMonthRentAmount(billingInfo.getAdjustAmount());
        List<BillItemDTO> billItems = new ArrayList<>();
        //费用项
        BillItemDTO billItemDTO = new BillItemDTO();
        ContractChargeEnum.MrCarContractChargeFeeEnum feeEnum = ContractChargeEnum.MrCarContractChargeFeeEnum.getByPaidCode(billingInfo.getBillType().intValue());
        billItemDTO.setFeeType(feeEnum.getFeeType().byteValue());
        billItemDTO.setFeeItem(feeEnum.getFeeItem().byteValue());
        billItemDTO.setFeeCode(feeEnum.getFeeBillCode());
        billItemDTO.setFeeAmount(billingInfo.getAdjustAmount());
        billItemDTO.setFeeRate(billingInfo.getTaxRate());
        billItems.add(billItemDTO);
        dto.setBillDetails(billItems);
        //推送综合 修改成含税金额
        dto.setBillAmount(billingInfo.getAdjustAmount());
        dto.setLeasePeriod(billingInfo.getBillPeriod());
        //默认4-不归档
        dto.setReportStatus(4);
        dto.setPaymentDueDate(billingInfo.getPaymentDueDate());
        dto.setArchiveStatus(4);
        //订单期数总后一期
        dto.setOrderStatus(OrderEnum.OrderStautsEnum.EXECUTING.getCode());
        boolean flag = existNoPushBillInfoList(billingInfo.getOrderCode(),billingInfo.getId());
        //查询账单期数和订单期数是否一样
        MrCarBillingInfoExample example = new MrCarBillingInfoExample();
        example.createCriteria().andOrderPeriodEqualTo(billingInfo.getOrderTotalPeriod());
        example.createCriteria().andOrderCodeEqualTo(billingInfo.getOrderCode());
        List<MrCarBillingInfo> mrCarBillingInfoList = mrCarBillingInfoExMapper.selectByExample(example);
        if(flag && CollectionUtils.isNotEmpty(mrCarBillingInfoList)){
            dto.setOrderStatus(OrderEnum.OrderStautsEnum.BACKED_VEHICLE.getCode());
        }
        dto.setOrderType(OrderEnum.OrderTypeEnum.MR_CAR_USER_CONTRACT_VEHICLE.getCode());
        dto.setBillType(BillTypeEnum.MR_CAR_USER_CONTRACT_Bill.getCode());
        return dto;
    }

    /**
     * 账单手动推送
     * @param dto
     * @return
     */
    public RestResponse send(BillingInfoOpReqDTO dto) {
        MrCarBillingInfo mrCarBillingInfo = mrCarBillingInfoExMapper.selectByPrimaryKey(dto.getId());
        if (mrCarBillingInfo == null) {
            return RestResponse.fail(BusinessErrorCode.INFO_NOT_EXISTS);
        }
        if(Objects.equals(mrCarBillingInfo.getBillStatus(),MrcarUseContractEnum.BillStatusEnum.PUSHED.getCode())){
            return RestResponse.fail(BusinessErrorCode.CONTRACT_COMMONT_MSG,"账单已推送，无需再次推送");
        }
        if(Objects.equals(mrCarBillingInfo.getBillStatus(),MrcarUseContractEnum.BillStatusEnum.DISCARDED.getCode())){
            return RestResponse.fail(BusinessErrorCode.CONTRACT_COMMONT_MSG,"账单状态为已废弃，无法推送账单");
        }
        sendBillToLease(mrCarBillingInfo,Boolean.FALSE);
        return RestResponse.success(null);
    }

    /**
     * 账单退回
     * @param orderNo
     */
    @Transactional
    public void contractBillGenerateReturn(String orderNo) {
        //查询需要退回的账单数据
        List<MrCarBillingInfo> mrCarBillingInfoList = mrCarBillingInfoExMapper.selectByOrderNo(orderNo);
        if(CollUtil.isEmpty(mrCarBillingInfoList)){
            return;
        }
        mrCarBillingInfoExMapper.updateBillStatusByOrderNo(orderNo);
        mrCarBillingInfoList.forEach(x->{
            //新增操作记录
            mrCarBillingOperateRecordService.saveOperationRecord(x.getId()
                    ,MrcarUseContractEnum.OperateTypeEnum.RETURN_BILL.getCode(),
                    0,
                    MrcarUseContractEnum.CreateMethodEnum.SYSTEM_CREATED.getDescription(),
                    null);
        });
    }

    //账单状态从待出账变待推送账单
    @Transactional
    public void billStatusUpdate() {
        List<MrCarBillingInfo> mrCarBillingInfoList = mrCarBillingInfoExMapper.queryPendingBillingBillList();
        if(CollUtil.isEmpty(mrCarBillingInfoList)){
            return;
        }
        mrCarBillingInfoList.forEach(x->{
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date date = com.izu.business.util.DateUtil.addDay(x.getBillEdate(), 1);
            String dateStr = sdf.format(date);
            String nowDateStr = sdf.format(new Date());
            if(Objects.equals(nowDateStr,dateStr)){
                x.setBillStatus(MrcarUseContractEnum.BillStatusEnum.PENDING_PUSH.getCode());
                mrCarBillingInfoExMapper.updateByPrimaryKeySelective(x);
            }
        });
    }

    /**
     * 查询合同信息
     */
    public ContractInfoResDTO queryContract(BillingInfoQueryContractReqDTO dto) {
        //根据合同类型和关联合同code 查询合同是否存在
        ContractInfoResDTO resDTO = new ContractInfoResDTO();
        if (Objects.equals(dto.getContractType(),CompanyPaidContractTypeEnum.MR_CAR_USAGE_CONTRACT.getCode().intValue())) {
            ContractAuthAccount contractAuthAccount = contractAuthAccountExMapper.getContractAuthAccount(dto.getContractCode());
            List<ContractOtherSubject> otherSubjectList = contractOtherSubjectExMapper.selectByContractCodeAndType(dto.getContractCode(), MrCarContractChargeEnum.SubjectOperateTypeEnum.SignSubject.getCode());
            if (CollectionUtils.isNotEmpty(otherSubjectList)) {
                CrmSigningSubjectInfoDTO crmSigningSubjectInfoDTO = CrmRpc.queryList(null, otherSubjectList.get(0).getOperateCode(),null);
                if(crmSigningSubjectInfoDTO!=null){
                    resDTO.setOperateCode(crmSigningSubjectInfoDTO.getFinanceStructCode());
                    resDTO.setOperateName(crmSigningSubjectInfoDTO.getFinanceStructName());
                    resDTO.setFinanceStructCode(contractAuthAccount.getFinanceStructCode());
                    resDTO.setFinanceStructName(contractAuthAccount.getFinanceStructName());
                }
            }
        } else {
            // 查询合同基本信息
            LeaseContractBuss leaseContractBuss = leaseContractBussExMapper.selectByContractCode(dto.getContractCode());
            if(leaseContractBuss==null){
                return null;
            }
            CrmSigningSubjectInfoDTO crmSigningSubjectInfoDTO = CrmRpc.queryList(null, leaseContractBuss.getStructBusinessLicense(),null);
            if(crmSigningSubjectInfoDTO!=null){
                resDTO.setOperateCode(crmSigningSubjectInfoDTO.getFinanceStructCode());
                resDTO.setOperateName(crmSigningSubjectInfoDTO.getFinanceStructName());
            }
        }
        return resDTO;
    }

    /**
     * 查询存在未推送账单数据否
     */
    private boolean existNoPushBillInfoList(String orderNo,Integer billId){
        List<MrCarBillingInfo>  mrCarBillingInfoList = mrCarBillingInfoExMapper.queryNoPushBillInfoList(orderNo,billId);
        return CollUtil.isEmpty(mrCarBillingInfoList);
    }
}
