package com.izu.business.consts;

import lombok.Getter;

public class MrcarUseContractEnum {
    /**
     * 账单类型
     */
    @Getter
    public enum BillTypeEnum {
        CHARGING_ADVANCE(4, "充电代垫"),
        MAINTENANCE_ADVANCE(5, "维保代垫"),
        ILLEGAL_QUERY(6, "违章代查"),
        ILLEGAL_QUERY_WITH_IMAGE(7, "违章代查含图"),
        MAINTENANCE_APPROVAL(8, "维保托管审批"),
        SYSTEM_USAGE_FEE(9, "系统使用费"),
        CUSTOM_DEVELOPMENT_FEE(50, "定制开发费"),
        EQUIPMENT_PURCHASE_FEE(60, "设备购置费"),
        EQUIPMENT_RENTAL_FEE(61, "设备租赁费"),
        EQUIPMENT_DATA_FEE(62, "设备流量费"),
        EQUIPMENT_INSTALLATION_FEE(63, "设备安装费"),
        EQUIPMENT_DISASSEMBLY_FEE(64, "设备拆卸费");

        private final int id;
        private final String description;

        BillTypeEnum(int id, String description) {
            this.id = id;
            this.description = description;
        }
        // 根据ID获取枚举常量的方法
        public static BillTypeEnum getById(int id) {
            for (BillTypeEnum billTypeEnum : BillTypeEnum.values()) {
                if (billTypeEnum.id == id) {
                    return billTypeEnum;
                }
            }
            return null;
        }
    }

    /**
     * 出账方式
     */
    @Getter
    public enum BillingMethodEnum {
        BY_CONTRACT_SIGNING("by_contract_signing", "按合同签署出账"),
        BY_ACTUAL_USAGE("by_actual_usage", "按实际使用出账"),
        BY_MANUAL_CREATION("by_manual_creation", "按手动创建出账");

        private final String code;
        private final String description;

        BillingMethodEnum(String code, String description) {
            this.code = code;
            this.description = description;
        }
        // 根据code获取枚举常量的方法
        public static BillingMethodEnum getByCode(String code) {
            for (BillingMethodEnum billingMethod : BillingMethodEnum.values()) {
                if (billingMethod.code.equals(code)) {
                    return billingMethod;
                }
            }
            return null;
        }
    }

    /**
     * 账单状态
     */
    @Getter
    public enum BillStatusEnum {
        PENDING_BILLING((byte) 0, "待出账"),
        PENDING_PUSH((byte) 10, "待推送账单"),
        PUSHED((byte) 20, "已推送账单"),
        DISCARDED((byte) 30, "已废弃"),
        PUSH_FAIL((byte) 40, "推送失败");

        private final byte code;
        private final String description;

        BillStatusEnum(byte code, String description) {
            this.code = code;
            this.description = description;
        }
        // 根据code获取枚举常量的方法
        public static BillStatusEnum getByCode(byte code) {
            for (BillStatusEnum billStatus : BillStatusEnum.values()) {
                if (billStatus.code == code) {
                    return billStatus;
                }
            }
            return null;
        }
    }

    /**
     * 回款状态
     */
    @Getter
    public enum RepaymentStatusEnum {
        PENDING_REPAYMENT(0, "待回款",2),
        PARTIAL_REPAYMENT(10, "部分回款",9),
        FULL_REPAYMENT(20, "已回款",3);

        private final Integer code;
        private final String description;
        private final Integer rentCode;

        RepaymentStatusEnum(Integer code, String description, Integer rentCode) {
            this.code = code;
            this.description = description;
            this.rentCode = rentCode;
        }

        // 根据code获取枚举常量的方法
        public static RepaymentStatusEnum getByCode(Integer code) {
            for (RepaymentStatusEnum repaymentStatus : RepaymentStatusEnum.values()) {
                if (repaymentStatus.code.equals(code)) {
                    return repaymentStatus;
                }
            }
            return null;
        }
    }

    /**
     * 支付周期
     */
    @Getter
    public enum PaymentCycleEnum {
        ONE_TIME("one_time", "一次性支付",1),
        MONTHLY("monthly", "月付",2),
        QUARTERLY("quarterly", "季付",3),
        BIANNUAL("biannual", "半年付",5),
        ANNUAL("annual", "年付",4);

        private final String code;
        private final String description;
        private final Integer leaseCode;

        PaymentCycleEnum(String code, String description,Integer leaseCode) {
            this.code = code;
            this.description = description;
            this.leaseCode = leaseCode;
        }

        // 根据code获取枚举常量的方法
        public static PaymentCycleEnum getByCode(String code) {
            for (PaymentCycleEnum paymentCycle : PaymentCycleEnum.values()) {
                if (paymentCycle.code.equals(code)) {
                    return paymentCycle;
                }
            }
            return null;
        }
        // 新增：根据 leaseCode 查找 code
        public static String getCodeByLeaseCode(Integer leaseCode) {
            for (PaymentCycleEnum paymentCycle : values()) {
                if (paymentCycle.leaseCode.equals(leaseCode)) {
                    return paymentCycle.code;
                }
            }
            return null;
        }
    }

    /**
     * 创建方式
     */
    @Getter
    public enum CreateMethodEnum {
        SYSTEM_CREATED("system_created", "系统创建"),
        MANUALLY_CREATED("manually_created", "手动创建");

        private final String code;
        private final String description;

        CreateMethodEnum(String code, String description) {
            this.code = code;
            this.description = description;
        }

        // 根据code获取枚举常量的方法
        public static CreateMethodEnum getByCode(String code) {
            for (CreateMethodEnum createMethodEnum : CreateMethodEnum.values()) {
                if (createMethodEnum.code.equals(code)) {
                    return createMethodEnum;
                }
            }
            return null;
        }
    }
    @Getter
    public enum FileTypeEnum {
        BILL_ATTACHMENT(10, "账单附件"),
        PRICE_ADJUSTMENT_ATTACHMENT(20, "调价附件");

        private final int code;
        private final String description;

        FileTypeEnum(int code, String description) {
            this.code = code;
            this.description = description;
        }
        // 可选：根据默认值处理未知类型
        public static FileTypeEnum getByCodeOrDefault(int code) {
            for (FileTypeEnum fileType : FileTypeEnum.values()) {
                if (fileType.code == code) {
                    return fileType;
                }
            }
            return null; // 或返回一个默认枚举值（如 UNKNOWN）
        }
    }
    @Getter
    public enum OperateTypeEnum {
        CREATE_BILL(10, "创建账单"),
        ADJUST_BILL(20, "调账"),
        PUSH_BILL(30, "账单推送"),
        DISCARD_BILL(40, "账单废弃"),
        RETURN_BILL(50, "账单退回");

        private final Integer code;
        private final String description;

        OperateTypeEnum(Integer code, String description) {
            this.code = code;
            this.description = description;
        }

        /**
         * 根据 code 获取枚举值
         * @param code 操作类型编号
         * @return 对应的枚举值，未找到时返回 null
         */
        public static OperateTypeEnum getByCode(Integer code) {
            for (OperateTypeEnum operateType : OperateTypeEnum.values()) {
                if (operateType.code.equals(code)) {
                    return operateType;
                }
            }
            return null;
        }
    }
}
