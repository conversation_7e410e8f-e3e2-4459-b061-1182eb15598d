package com.izu.business.dto.expenditure.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 支出金额汇总响应DTO
 * <AUTHOR>
 * @date 2025/6/27
 */
@Data
public class ExpenditureAmountSumRespDTO {

    @ApiModelProperty("应支供应商金额（含税）")
    private BigDecimal totalAmount;
    
    @ApiModelProperty("应支供应商金额（不含税）")
    private BigDecimal totalAmountNoRate;
}
