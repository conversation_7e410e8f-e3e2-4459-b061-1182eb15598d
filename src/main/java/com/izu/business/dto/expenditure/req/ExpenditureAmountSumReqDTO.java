package com.izu.business.dto.expenditure.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 支出金额汇总请求DTO
 * <AUTHOR>
 * @date 2025/6/27
 */
@Data
public class ExpenditureAmountSumReqDTO {

    @ApiModelProperty("支出类型：1充电代垫；2维保代垫")
    private Integer expenditureType;

    @ApiModelProperty("渠道ID（映射编码）")
    private Integer channelId;

    @ApiModelProperty("供应商税点")
    private BigDecimal supplierRate;

    @ApiModelProperty("支出单明细业务单据id列表")
    private List<Integer> receiptIds;
}
