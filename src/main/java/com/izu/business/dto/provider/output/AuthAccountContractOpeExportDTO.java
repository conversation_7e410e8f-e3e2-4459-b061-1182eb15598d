package com.izu.business.dto.provider.output;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.izu.framework.doc.JrdApiFieldDoc;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class AuthAccountContractOpeExportDTO {

    @ApiModelProperty("合同编号")
    @ExcelProperty({"合同编号"})
    @ColumnWidth(20)
    private String contractCode;
    @ApiModelProperty("合同标题")
    @ExcelProperty({"合同标题"})
    @ColumnWidth(20)
    private String contractTitle;
    @ApiModelProperty("合同来源")
    @ExcelIgnore
    private Integer contractType;
    @ApiModelProperty("合同来源")
    @ExcelProperty({"合同来源"})
    @ColumnWidth(20)
    private String contractTypeStr;

    @ApiModelProperty("使用方式")
    @ExcelIgnore
    private Byte accountUseMethod;
    @ApiModelProperty("使用方式")
    @ExcelProperty({"使用方式"})
    @ColumnWidth(20)
    private String accountUseMethodStr;

    @JrdApiFieldDoc(desc = "客户名称")
    @ExcelProperty({"客户企业"})
    @ColumnWidth(20)
    private String customerName;

    @JrdApiFieldDoc(desc = "我司签约主体")
    @ExcelProperty({"我司签约主体"})
    @ColumnWidth(20)
    private String operateName;

    @JrdApiFieldDoc(desc = "我司销售主体")
    @ExcelProperty({"我司销售主体"})
    @ColumnWidth(20)
    private String financeStructName;

    @ApiModelProperty(value = "合同开始时间")
    @ExcelIgnore
    private Date startDate;
    @ApiModelProperty(value = "合同结束时间")
    @ExcelIgnore
    private Date endDate;
    @ApiModelProperty(value = "合同总额（不含代垫）")
    @ExcelProperty({"合同总额（不含代垫）"})
    @ColumnWidth(20)
    private BigDecimal totalAmount;

    @JrdApiFieldDoc(desc = "合同起止时间")
    @ExcelProperty({"合同起止时间"})
    @ColumnWidth(20)
    private String contractTimeStr;

    @JrdApiFieldDoc(desc = "收费项目")
    @ExcelIgnore
    private String chargeItem;
    @JrdApiFieldDoc(desc = "收费项目")
    @ExcelProperty({"收费项目"})
    @ColumnWidth(20)
    private String chargeItemStr;

    @ApiModelProperty("产品包名称")
    @ExcelProperty({"产品包"})
    @ColumnWidth(20)
    private String productPackName;

    @ApiModelProperty("合同类型")
    @ExcelIgnore
    private Integer standardContract;
    @ApiModelProperty("合同类型文本")
    @ExcelProperty({"合同类型"})
    @ColumnWidth(20)
    private String standardContractStr;

    @JrdApiFieldDoc(desc = "办理状态 100:已完善,200:办理中,300:办理完成,400:已驳回,500:已废除")
    @ExcelIgnore
    private Integer auditStatus;

    @JrdApiFieldDoc(desc = "办理状态文本")
    @ExcelProperty({"办理状态"})
    @ColumnWidth(20)
    private String auditStatusStr;


    @JrdApiFieldDoc(desc = "签署状态 1:未签署,2:已签署")
    @ExcelIgnore
    private Integer contractSignStatus;

    @JrdApiFieldDoc(desc = "签署状态文本")
    @ExcelProperty({"签署状态"})
    @ColumnWidth(20)
    private String contractSignStatusStr;


    @JrdApiFieldDoc(desc = "合同所有人")
    @ExcelProperty({"合同所有人"})
    @ColumnWidth(20)
    private String signSalesName;

    @ApiModelProperty("合同维护人")
    @ExcelProperty({"合同维护人"})
    @ColumnWidth(20)
    private String maintainName;

    @ApiModelProperty("合同回传确认时间")
    @ExcelProperty({"合同回传确认时间"})
    @ColumnWidth(20)
    private Date contractConfirmReturnTime;

    @JrdApiFieldDoc(desc = "申请部门")
    @ExcelProperty({"申请部门"})
    @ColumnWidth(20)
    private String companyName;

    @JrdApiFieldDoc(desc = "创建人")
    @ExcelProperty({"创建人"})
    @ColumnWidth(20)
    private String createName;

    @JrdApiFieldDoc(desc = "创建时间")
    @ExcelProperty({"创建时间"})
    @ColumnWidth(20)
    private Date createTime;

    @JrdApiFieldDoc(desc = "最后维护人")
    @ExcelProperty({"最后维护人"})
    @ColumnWidth(20)
    private String updateName;

    @JrdApiFieldDoc(desc = "维护时间")
    @ExcelProperty({"维护时间"})
    @ColumnWidth(20)
    private Date updateTime;

}
