package com.izu.business.dto.charge;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 充电接口信息（电枪）
 *
 * <AUTHOR>
 * @docRoot com.izu.business.entity.mongo
 * @Date 2024/5/25 下午4:44
 */
@Data
@ApiModel("充电接口信息")
public class ChargeConnectInfoDTO {

    /**
     * 充电站id
     **/
    @ApiModelProperty(value = "充电站id")
    private String stationId;
    /**
     * 充电设备id
     **/
    @ApiModelProperty(value = "充电设备id")
    private String equipmentId;
    /**
     * 充电设备类型
     **/
    @ApiModelProperty(value = "充电设备类型")
    private Integer equipmentType;
    /**
     * 充电设备类型,1:直流设备;2:交流设备;3:交直流一体设备;4:无线设备;5:其他
     **/
    @ApiModelProperty(value = "充电设备类型名称")
    private String equipmentTypeStr;
    /**
     * 充电接口id
     **/
    @ApiModelProperty(value = "充电接口id")
    private String connectorId;
    /**
     * 充电接口名称
     **/
    @ApiModelProperty(value = "充电接口名称")
    private String connectorName;
    /**
     * 接口类型,1:家用插座(模式2);2:交流接口插座(模式3，连接方式B);3:交流接口插头(带枪线，模式3，连接方式C);4:直流接口枪头(带枪线，模式 4);5:无线充电座;6:其他
     **/
    @ApiModelProperty(value = "接口类型,1:家用插座(模式2);2:交流接口插座(模式3，连接方式B);3:交流接口插头(带枪线，模式3，连接方式C);4:直流接口枪头(带枪线，模式 4);5:无线充电座;6:其他")
    private Integer connectorType;
    /**
     * 接口类型,1:家用插座(模式2);2:交流接口插座(模式3，连接方式B);3:交流接口插头(带枪线，模式3，连接方式C);4:直流接口枪头(带枪线，模式 4);5:无线充电座;6:其他
     **/
    @ApiModelProperty(value = "接口类型,1:家用插座(模式2);2:交流接口插座(模式3，连接方式B);3:交流接口插头(带枪线，模式3，连接方式C);4:直流接口枪头(带枪线，模式 4);5:无线充电座;6:其他")
    private String connectorTypeStr;
    /**
     * 功率，单位：KW；保留小数点后一位
     **/
    @ApiModelProperty(value = "功率，单位：KW；保留小数点后一位")
    private Double power;
    /**
     * 额定电流
     **/
    @ApiModelProperty(value = "额定电流")
    private Double current;
    /**
     * 充电设备接口状态，0:离网，1:空闲，2:占用(未充电)，3:占用(充电中)，4:占用(预约锁定)，255:故障
     **/
    @ApiModelProperty(value = "充电设备接口状态，0:离网，1:空闲，2:占用(未充电)，3:占用(充电中)，4:占用(预约锁定)，255:故障")
    private Integer status;

}
