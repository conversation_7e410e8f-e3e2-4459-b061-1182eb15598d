package com.izu.business.util.pdf.api;

import com.izu.business.util.pdf.layout.AlignmentType;

public class PdfTextBuilder implements LeafLikeElementBuilder {

    String text;
    PdfFontBuilder font;
    int fontSize = 10;
    AlignmentType alignment = AlignmentType.LEFT;

    public static PdfTextBuilder builder() {
        return new PdfTextBuilder();
    }

    public PdfTextBuilder text(String text) {
        this.text = text;
        return this;
    }

    public PdfTextBuilder font(PdfFontBuilder font) {
        this.font = font;
        return this;
    }

    public PdfTextBuilder fontSize(int size) {
        this.fontSize = size;
        return this;
    }

    public PdfTextBuilder alignLeft() {
        this.alignment = AlignmentType.LEFT;
        return this;
    }

    public PdfTextBuilder alignRight() {
        this.alignment = AlignmentType.RIGHT;
        return this;
    }

    public PdfTextBuilder alignCenter() {
        this.alignment = AlignmentType.CENTER;
        return this;
    }

    @Override
    public ElementType builderType() {
        return ElementType.TEXT;
    }
}
