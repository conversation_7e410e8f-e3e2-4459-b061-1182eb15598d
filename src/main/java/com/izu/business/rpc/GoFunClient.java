package com.izu.business.rpc;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.izu.business.cache.BusinessJSONableCache;
import com.izu.business.cache.CacheConstant;
import com.izu.business.config.errorcode.BusinessErrorCode;
import com.izu.business.dto.gofun.output.QueryOperatorsInfoDTO;
import com.izu.business.exception.BusinessRunningException;
import com.izu.business.service.common.SequenceGenerator;
import com.izu.business.util.gofun.GofunConstantUtils;
import com.izu.business.util.gofun.RSABlockUtils;
import com.izu.framework.web.exception.RestErrorException;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * goFun 充电对接接口
 * <p>
 * 2024/5/25下午6:02
 *
 * <AUTHOR>
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class GoFunClient {

    private final GofunConstantUtils gofunConstantUtils;
    private final BusinessJSONableCache redisSentinelCache;
    private final SequenceGenerator sequenceGenerator;
    // 最大重试次数
    private static final int MAX_RETRIES = 2;
    // 初始重试延迟（毫秒）
    private static final long INITIAL_DELAY = 1000;

    public <T> T requestGoFun(final String interfaceName, final Map<String, Object> params, Class<T> responseObjectClass) {
        String httpUrl = String.format("%s%s?access_token=%s", gofunConstantUtils.getHost(), interfaceName, getToken(false));
        log.info("调用智行接口地址:{}  ", httpUrl);
        Map<String, Object> httpParams = initParams(params);
        try {
            String signParams = RSABlockUtils.encrypt(JSON.toJSONString(httpParams), gofunConstantUtils.getPublicKey());
            log.info("调用智行接口请求参数-加密后:{}", JSONUtil.toJsonStr(signParams));
            HttpResponse execute = HttpUtil.createPost(httpUrl).header("contentType", "text/plain").body(signParams, "text/plain").execute();
            String body = execute.body();
            Result result = JSON.parseObject(body, Result.class);

            // 区分成功与否，排除成功中包含错误/异常的告警
            if (execute.isOk() && result.code == 10000) {
                log.info("调用智行接口成功,返回结果:{}", JSONUtil.toJsonStr(body));
            } else {
                log.info("调用智行接口失败,返回结果:{}", JSONUtil.toJsonStr(body));
            }

            if (!execute.isOk()) {
                throw new BusinessRunningException(BusinessErrorCode.CHARGE_PARAMS_COMMON_ERROR, result.getMsg());
            }
            if (result.code == 4217) {
                throw new BusinessRunningException(BusinessErrorCode.GO_FUN_OPERATOR_UNABLE, result.getMsg());
            }
            if (result.code != 10000) {
                throw new BusinessRunningException(BusinessErrorCode.CHARGE_PARAMS_COMMON_ERROR, result.getMsg());
            }
            return JSON.parseObject(JSON.toJSONString(result.getData()), responseObjectClass);
        } catch (BusinessRunningException restErrorException) {
            log.info("调用智行接口失败:{}", restErrorException.getMessage());
            if (restErrorException.getRestResponse().getCode() == BusinessErrorCode.GO_FUN_OPERATOR_UNABLE){
                throw new BusinessRunningException(BusinessErrorCode.GO_FUN_OPERATOR_UNABLE, restErrorException.getMessage());
            }
            throw new BusinessRunningException(BusinessErrorCode.CHARGE_PARAMS_COMMON_ERROR, restErrorException.getMessage());
        } catch (Exception e) {
            log.warn("调用智行接口异常", e);
            throw new BusinessRunningException("调用智行接口异常", e);
        }
    }


    private Map<String, Object> initParams(Map<String, Object> params) {
        Map<String, Object> httpParams = new HashMap<>();
        httpParams.put("companyCode", gofunConstantUtils.getCompanyCode());
        httpParams.put("data", params);
        httpParams.put("seq", sequenceGenerator.generate(new Date(), "gofunSeq"));
        httpParams.put("timestamp", System.currentTimeMillis());
        return httpParams;
    }

    /**
     * 获取token
     * grant_type=client_credentials&client_id=sqzl_mrcar&client_secret=EnC1Sw9CwfG1ihgmWy3bzShb
     *
     * <AUTHOR>
     **/
    private String getToken(boolean refresh) {
        String redisToken = redisSentinelCache.get(CacheConstant.GO_FUN_TOKEN_KEY, String.class);
        String tokenUrl = gofunConstantUtils.getTokenUrl();
        String httpUrl = String.format("%s%s", gofunConstantUtils.getHost(), tokenUrl);
        //判断token是否为空
        if (StringUtils.isNotBlank(redisToken) && !refresh) {
            log.info("智行token:{}", redisToken);
            return redisToken;
        }
        //token不存在或已经失效，重新获取
        Map<String, Object> httpParams = new HashMap<>();
        httpParams.put("grant_type", gofunConstantUtils.getGrantType());
        httpParams.put("client_id", gofunConstantUtils.getClientId());
        httpParams.put("client_secret", gofunConstantUtils.getClientSecret());
        Result result = RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POST, httpUrl, httpParams, null, Result.class);
        if (result.getCode() == 10000) {
            JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(result.getData()));
            String newToken = jsonObject.getString("access_token");
            String refreshToken = jsonObject.getString("refresh_token");
            //将新的token保存到redis并设置过期时间
            redisSentinelCache.set(CacheConstant.GO_FUN_TOKEN_KEY, newToken, CacheConstant.GO_FUN_TOKEN_KEY_EXPIRE);
            redisSentinelCache.set(CacheConstant.GO_FUN_REFRESH_TOKEN_KEY, refreshToken, CacheConstant.GO_FUN_REFRESH_TOKEN_KEY_EXPIRE);
            return newToken;
        } else {
            throw new RestErrorException(result.getMsg(), result.getCode());
        }
    }

    /**
     * 刷新token，token有效期2个小时，定时任务1小时执行一次刷新token的接口
     * grant_type=refresh_token&client_id=sqzl_mrcar&client_secret=EnC1Sw9CwfG1ihgmWy3bzShb
     *
     * <AUTHOR>
     **/
    public void refreshToken() {
        String redisToken = redisSentinelCache.get(CacheConstant.GO_FUN_REFRESH_TOKEN_KEY, String.class);
        String tokenUrl = gofunConstantUtils.getRefreshTokenUrl();
        String httpUrl = String.format("%s%s", gofunConstantUtils.getHost(), tokenUrl);
        if (StringUtils.isNotBlank(redisToken)) {
            log.info("智行refreshToken:{}", redisToken);
            //refreshToken不存在或已经失效，重新获取token,并更新redis中的token和refreshToken
            Map<String, Object> httpParams = new HashMap<>();
            httpParams.put("grant_type", gofunConstantUtils.getGrantTypeRefresh());
            httpParams.put("client_id", gofunConstantUtils.getClientId());
            httpParams.put("client_secret", gofunConstantUtils.getClientSecret());
            httpParams.put("refresh_token", redisToken);
            Result result = RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POST, httpUrl, httpParams, null, Result.class);
            if (result.getCode() == 10000) {
                JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(result.getData()));
                String newToken = jsonObject.getString("access_token");
                String refreshToken = jsonObject.getString("refresh_token");
                //将新的token保存到redis并设置过期时间
                redisSentinelCache.set(CacheConstant.GO_FUN_TOKEN_KEY, newToken, CacheConstant.GO_FUN_TOKEN_KEY_EXPIRE);
                redisSentinelCache.set(CacheConstant.GO_FUN_REFRESH_TOKEN_KEY, refreshToken, CacheConstant.GO_FUN_REFRESH_TOKEN_KEY_EXPIRE);
            } else {
                //与智行确认过 错误的refresh_token 不需要抛异常
                if (result.getCode() != 20003) {
                    throw new RestErrorException(result.getMsg(), result.getCode());
                }
            }
        } else {
            this.getToken(true);
        }

    }

    /**
     * goFun 通用响应信息
     **/
    @Data
    public static class Result {
        /**
         * 响应码
         **/
        private int Ret;
        /**
         * 响应码描述
         **/
        private String Msg;
        /**
         * 响应内容
         **/
        private Object Data;
        /**
         * 数字签名
         **/
        private String Sig;
        /**
         * token接口响应码
         **/
        private int code;
    }

    public static void main(String[] args) throws Exception {
        String url = "https://test-companyopenplatformapi.shouqiev.com/ev/v1/query_station_info?access_token=df904fd7-3b3c-4b8f-a6a3-c97cafee378e";
        Map<Object, Object> map = new HashMap<>();
        map.put("stationIds", new String[]{"321754"});
        Map<String, Object> httpParams = new HashMap<>();
        httpParams.put("companyCode", "SH202405300002");
        httpParams.put("data", map);
        httpParams.put("seq", "2024060300001");
        httpParams.put("timestamp", System.currentTimeMillis());
        String signParams = RSABlockUtils.encrypt(JSON.toJSONString(httpParams), "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCZktBdKtoAoJC6uPjSkytbJdcBCKmxQWEmLX+f77LolRkZ4L+T8lGavOYQwyqlwTTJi4OhZ69EKjbV4IZ7uPD1A/+DJIkUTGs0SIGuO8UTNR/0t6VqN8D3nhY80nWnZCTS1zFeJVVu4/n8qsCKL1xSyEujWk/OKrEe7VXD/e2+OQIDAQAB");
        System.out.println("signParams = " + signParams);

        String decrypt = RSABlockUtils.decrypt(signParams, "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAJmS0F0q2gCgkLq4+NKTK1sl1wEIqbFBYSYtf5/vsuiVGRngv5PyUZq85hDDKqXBNMmLg6Fnr0QqNtXghnu48PUD/4MkiRRMazRIga47xRM1H/S3pWo3wPeeFjzSdadkJNLXMV4lVW7j+fyqwIovXFLIS6NaT84qsR7tVcP97b45AgMBAAECgYBMEFd7jdnkLaMBmNRFF/eFcbyIGo5R6RwBGM/5m3N1l0OTeanp0T9x8NXSv5fD+Wn32U4dB4nbrTzzuf1iEjvlmGEWmNnPM3nCTR665nM6wmgb1u+0W+jO1qytHdIrXSxBSRw7bWBa/wwQ47U2XLaXRh3G0DLXCRoC+oSJiBaqcQJBAOi0y4QGm3mIsMG8NxoCF8IWWC9wIQzPdyiD1042zkx4wrSlRGw1X4RpKrMc/NrMfIUZyZi6mSLp2AlLQzUPt48CQQCo8jSxkrfP4V00ziwKt0TYX7c6Wtz/bnepcCuT1cnsfOnDfFKu+A5eV8DGuaZc8hq+kJvo0EzA+tLDjUtnKom3AkEA4Rm6YD0P+jFKYi2R05vApaN9rG232q95YXyfMe58AqcyGRJhLJc4jVo5zsc0pOX3cb0vMIkYBxjTPLJMEbDhawJBAKgJ6JFqrjstHAjmvHWYHfjdEyEOuVuy44B7Wky7yxDWmahfotn3TktWGx9ODoPKJwG+UeS6Mm2LzVDF2+bsL3cCQCXSVFJnAJJzEOI0qjMKijkwWAR0gq+8tNSvKilA5hpnCMbbAMR9SIY8u2j1RW+D7+yFeQOz7lbuj1ukooJvWoM=");
        System.out.println("decrypt = " + decrypt);

        HttpResponse execute = HttpUtil.createPost(url).header("contentType", "text/plain").body(signParams, "text/plain").execute();
        log.info("调用智行接口返回结果:{}", JSONUtil.toJsonStr(execute));
        if (execute.isOk()) {
            String body = execute.body();
            System.out.println(body);
            Result result = JSON.parseObject(body, Result.class);
            JSONArray jsonArray = JSON.parseObject(JSON.toJSONString(result.getData()), JSONArray.class);
            List<QueryOperatorsInfoDTO> operatorsInfoDTOList = jsonArray.toJavaList(QueryOperatorsInfoDTO.class);
        }

    }
    public <T> T requestRetryGoFun(final String interfaceName, final Map<String, Object> params, Class<T> responseObjectClass) {

        int retries = 0;
        while (true) {
            try {
                // 执行实际请求逻辑
                return requestGoFun(interfaceName, params, responseObjectClass);
            } catch (BusinessRunningException e) {
                // 超过最大重试次数或非重试错误
                if (!isRetryableError(e.getRestResponse().getCode())) {
                    throw new BusinessRunningException(BusinessErrorCode.GO_FUN_OPERATOR_UNABLE, e.getMessage());
                }
                log.info("捕获业务异常，准备重试: {}", e.getMessage());
                retries++;
                // 如果达到最大重试次数，抛出异常
                if (retries >= MAX_RETRIES) {
                    throw new BusinessRunningException("请求失败，已达到最大重试次数", e);
                }

                // 计算退避时间（指数退避：1s, 2s, 4s...）
                long delay = INITIAL_DELAY * (long) Math.pow(2, retries - 1d);

                try {
                    log.info("请求失败，{}秒后重试 (第{}/{}次)...",
                            TimeUnit.MILLISECONDS.toSeconds(delay),
                            retries,
                            MAX_RETRIES);
                    Thread.sleep(delay);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    throw new BusinessRunningException("调用智行接口异常", ie);
                }
            } catch (Exception e) {
                throw new BusinessRunningException("调用智行接口异常", e);
            }
        }
    }

    /**
     * 判断错误码是否可以重试
     */
    private boolean isRetryableError(int errorCode) {
        // 根据实际业务需求定义可重试的错误码
        if(errorCode != BusinessErrorCode.CHARGE_PARAMS_COMMON_ERROR
                && errorCode != BusinessErrorCode.GO_FUN_OPERATOR_UNABLE
                && errorCode != BusinessErrorCode.CHARGE_PARAMS_COMMON_ERROR){
            return true;
        }
        return  false;
    }
}
