package com.izu.business.entity;

import java.util.Date;

public class StatisOrderFlow {
    /**
     * 主键 id
     */
    private Integer id;

    /**
     * 统计日期 （格式：yyyy-MM-dd） statis_date
     */
    private String statisDate;

    /**
     * 账套城市编码 account_set_city_code
     */
    private String accountSetCityCode;

    /**
     * 账套城市名称 account_set_city_name
     */
    private String accountSetCityName;

    /**
     * 订单编号 order_no
     */
    private String orderNo;

    /**
     * 所属订单申请编号 order_apply_no
     */
    private String orderApplyNo;

    /**
     * 订单类型；1：内部用车；2：外部订单；3：商务用车；4：短租；5：约车；6：国际租车 order_type
     */
    private Byte orderType;

    /**
     * 服务类型编码：service_dictionary表service_code service_code
     */
    private String serviceCode;

    /**
     * 订单调度时间 schedule_time
     */
    private Date scheduleTime;

    /**
     * MrCar车辆Id vehicle_id
     */
    private Long vehicleId;

    /**
     * 车架号 vehicle_vin
     */
    private String vehicleVin;

    /**
     * 车牌号 vehicle_license
     */
    private String vehicleLicense;

    /**
     * 车辆所属系统 vehicle_belonged_sys
     */
    private String vehicleBelongedSys;

    /**
     * 新系统车辆ID vehicle_id_new_system
     */
    private Integer vehicleIdNewSystem;

    /**
     * 新系统车辆编号 vehicle_serial_no_new_system
     */
    private String vehicleSerialNoNewSystem;

    /**
     * 司机所属城市编码 driver_city_code
     */
    private String driverCityCode;

    /**
     * 司机所属城市名称 driver_city_name
     */
    private String driverCityName;

    /**
     * 机动车使用人所属城市编码 belong_city_code
     */
    private String belongCityCode;

    /**
     * 机动车使用人所属城市名称 belong_city_name
     */
    private String belongCityName;

    /**
     * 机动车使用人（运营组织机构）编码 operate_struct_code
     */
    private String operateStructCode;

    /**
     * 创建时间 create_time
     */
    private Date createTime;

    /**
     * 修改时间 update_time
     */
    private Date updateTime;

    /**
     * 机动车使用人（运营组织机构）名称 operate_struct_name
     */
    private String operateStructName;

    /**
     * 机动车使用人（资产组织机构）名称 operate_buss_name
     */
    private String operateBussName;

    /**
     * 机动车使用人（资产组织机构）编码 operate_buss_code
     */
    private String operateBussCode;

    /**
     * 主键
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @return id 
     */
    public Integer getId() {
        return id;
    }

    /**
     * 主键
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @param id 
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 统计日期 （格式：yyyy-MM-dd）
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @return statis_date 
     */
    public String getStatisDate() {
        return statisDate;
    }

    /**
     * 统计日期 （格式：yyyy-MM-dd）
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @param statisDate 
     */
    public void setStatisDate(String statisDate) {
        this.statisDate = statisDate == null ? null : statisDate.trim();
    }

    /**
     * 账套城市编码
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @return account_set_city_code 
     */
    public String getAccountSetCityCode() {
        return accountSetCityCode;
    }

    /**
     * 账套城市编码
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @param accountSetCityCode 
     */
    public void setAccountSetCityCode(String accountSetCityCode) {
        this.accountSetCityCode = accountSetCityCode == null ? null : accountSetCityCode.trim();
    }

    /**
     * 账套城市名称
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @return account_set_city_name 
     */
    public String getAccountSetCityName() {
        return accountSetCityName;
    }

    /**
     * 账套城市名称
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @param accountSetCityName 
     */
    public void setAccountSetCityName(String accountSetCityName) {
        this.accountSetCityName = accountSetCityName == null ? null : accountSetCityName.trim();
    }

    /**
     * 订单编号
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @return order_no 
     */
    public String getOrderNo() {
        return orderNo;
    }

    /**
     * 订单编号
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @param orderNo 
     */
    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo == null ? null : orderNo.trim();
    }

    /**
     * 所属订单申请编号
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @return order_apply_no 
     */
    public String getOrderApplyNo() {
        return orderApplyNo;
    }

    /**
     * 所属订单申请编号
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @param orderApplyNo 
     */
    public void setOrderApplyNo(String orderApplyNo) {
        this.orderApplyNo = orderApplyNo == null ? null : orderApplyNo.trim();
    }

    /**
     * 订单类型；1：内部用车；2：外部订单；3：商务用车；4：短租；5：约车；6：国际租车
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @return order_type 
     */
    public Byte getOrderType() {
        return orderType;
    }

    /**
     * 订单类型；1：内部用车；2：外部订单；3：商务用车；4：短租；5：约车；6：国际租车
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @param orderType 
     */
    public void setOrderType(Byte orderType) {
        this.orderType = orderType;
    }

    /**
     * 服务类型编码：service_dictionary表service_code
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @return service_code 
     */
    public String getServiceCode() {
        return serviceCode;
    }

    /**
     * 服务类型编码：service_dictionary表service_code
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @param serviceCode 
     */
    public void setServiceCode(String serviceCode) {
        this.serviceCode = serviceCode == null ? null : serviceCode.trim();
    }

    /**
     * 订单调度时间
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @return schedule_time 
     */
    public Date getScheduleTime() {
        return scheduleTime;
    }

    /**
     * 订单调度时间
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @param scheduleTime 
     */
    public void setScheduleTime(Date scheduleTime) {
        this.scheduleTime = scheduleTime;
    }

    /**
     * MrCar车辆Id
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @return vehicle_id 
     */
    public Long getVehicleId() {
        return vehicleId;
    }

    /**
     * MrCar车辆Id
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @param vehicleId 
     */
    public void setVehicleId(Long vehicleId) {
        this.vehicleId = vehicleId;
    }

    /**
     * 车架号
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @return vehicle_vin 
     */
    public String getVehicleVin() {
        return vehicleVin;
    }

    /**
     * 车架号
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @param vehicleVin 
     */
    public void setVehicleVin(String vehicleVin) {
        this.vehicleVin = vehicleVin == null ? null : vehicleVin.trim();
    }

    /**
     * 车牌号
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @return vehicle_license 
     */
    public String getVehicleLicense() {
        return vehicleLicense;
    }

    /**
     * 车牌号
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @param vehicleLicense 
     */
    public void setVehicleLicense(String vehicleLicense) {
        this.vehicleLicense = vehicleLicense == null ? null : vehicleLicense.trim();
    }

    /**
     * 车辆所属系统
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @return vehicle_belonged_sys 
     */
    public String getVehicleBelongedSys() {
        return vehicleBelongedSys;
    }

    /**
     * 车辆所属系统
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @param vehicleBelongedSys 
     */
    public void setVehicleBelongedSys(String vehicleBelongedSys) {
        this.vehicleBelongedSys = vehicleBelongedSys == null ? null : vehicleBelongedSys.trim();
    }

    /**
     * 新系统车辆ID
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @return vehicle_id_new_system 
     */
    public Integer getVehicleIdNewSystem() {
        return vehicleIdNewSystem;
    }

    /**
     * 新系统车辆ID
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @param vehicleIdNewSystem 
     */
    public void setVehicleIdNewSystem(Integer vehicleIdNewSystem) {
        this.vehicleIdNewSystem = vehicleIdNewSystem;
    }

    /**
     * 新系统车辆编号
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @return vehicle_serial_no_new_system 
     */
    public String getVehicleSerialNoNewSystem() {
        return vehicleSerialNoNewSystem;
    }

    /**
     * 新系统车辆编号
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @param vehicleSerialNoNewSystem 
     */
    public void setVehicleSerialNoNewSystem(String vehicleSerialNoNewSystem) {
        this.vehicleSerialNoNewSystem = vehicleSerialNoNewSystem == null ? null : vehicleSerialNoNewSystem.trim();
    }

    /**
     * 司机所属城市编码
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @return driver_city_code 
     */
    public String getDriverCityCode() {
        return driverCityCode;
    }

    /**
     * 司机所属城市编码
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @param driverCityCode 
     */
    public void setDriverCityCode(String driverCityCode) {
        this.driverCityCode = driverCityCode == null ? null : driverCityCode.trim();
    }

    /**
     * 司机所属城市名称
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @return driver_city_name 
     */
    public String getDriverCityName() {
        return driverCityName;
    }

    /**
     * 司机所属城市名称
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @param driverCityName 
     */
    public void setDriverCityName(String driverCityName) {
        this.driverCityName = driverCityName == null ? null : driverCityName.trim();
    }

    /**
     * 机动车使用人所属城市编码
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @return belong_city_code 
     */
    public String getBelongCityCode() {
        return belongCityCode;
    }

    /**
     * 机动车使用人所属城市编码
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @param belongCityCode 
     */
    public void setBelongCityCode(String belongCityCode) {
        this.belongCityCode = belongCityCode == null ? null : belongCityCode.trim();
    }

    /**
     * 机动车使用人所属城市名称
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @return belong_city_name 
     */
    public String getBelongCityName() {
        return belongCityName;
    }

    /**
     * 机动车使用人所属城市名称
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @param belongCityName 
     */
    public void setBelongCityName(String belongCityName) {
        this.belongCityName = belongCityName == null ? null : belongCityName.trim();
    }

    /**
     * 机动车使用人（运营组织机构）编码
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @return operate_struct_code 
     */
    public String getOperateStructCode() {
        return operateStructCode;
    }

    /**
     * 机动车使用人（运营组织机构）编码
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @param operateStructCode 
     */
    public void setOperateStructCode(String operateStructCode) {
        this.operateStructCode = operateStructCode == null ? null : operateStructCode.trim();
    }

    /**
     * 创建时间
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @return create_time 
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @param createTime 
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 修改时间
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @return update_time 
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 修改时间
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @param updateTime 
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 机动车使用人（运营组织机构）名称
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @return operate_struct_name 
     */
    public String getOperateStructName() {
        return operateStructName;
    }

    /**
     * 机动车使用人（运营组织机构）名称
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @param operateStructName 
     */
    public void setOperateStructName(String operateStructName) {
        this.operateStructName = operateStructName == null ? null : operateStructName.trim();
    }

    /**
     * 机动车使用人（资产组织机构）名称
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @return operate_buss_name 
     */
    public String getOperateBussName() {
        return operateBussName;
    }

    /**
     * 机动车使用人（资产组织机构）名称
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @param operateBussName 
     */
    public void setOperateBussName(String operateBussName) {
        this.operateBussName = operateBussName == null ? null : operateBussName.trim();
    }

    /**
     * 机动车使用人（资产组织机构）编码
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @return operate_buss_code 
     */
    public String getOperateBussCode() {
        return operateBussCode;
    }

    /**
     * 机动车使用人（资产组织机构）编码
     * <AUTHOR>
     * @date 2020-04-29 14:25:51
     * @param operateBussCode 
     */
    public void setOperateBussCode(String operateBussCode) {
        this.operateBussCode = operateBussCode == null ? null : operateBussCode.trim();
    }
}