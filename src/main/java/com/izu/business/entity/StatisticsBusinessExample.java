package com.izu.business.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class StatisticsBusinessExample {
    /**
     * statistics_business
     */
    protected String orderByClause;

    /**
     * statistics_business
     */
    protected boolean distinct;

    /**
     * statistics_business
     */
    protected List<Criteria> oredCriteria;

    public StatisticsBusinessExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andStatisticsDateIsNull() {
            addCriterion("statistics_date is null");
            return (Criteria) this;
        }

        public Criteria andStatisticsDateIsNotNull() {
            addCriterion("statistics_date is not null");
            return (Criteria) this;
        }

        public Criteria andStatisticsDateEqualTo(Date value) {
            addCriterionForJDBCDate("statistics_date =", value, "statisticsDate");
            return (Criteria) this;
        }

        public Criteria andStatisticsDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("statistics_date <>", value, "statisticsDate");
            return (Criteria) this;
        }

        public Criteria andStatisticsDateGreaterThan(Date value) {
            addCriterionForJDBCDate("statistics_date >", value, "statisticsDate");
            return (Criteria) this;
        }

        public Criteria andStatisticsDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("statistics_date >=", value, "statisticsDate");
            return (Criteria) this;
        }

        public Criteria andStatisticsDateLessThan(Date value) {
            addCriterionForJDBCDate("statistics_date <", value, "statisticsDate");
            return (Criteria) this;
        }

        public Criteria andStatisticsDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("statistics_date <=", value, "statisticsDate");
            return (Criteria) this;
        }

        public Criteria andStatisticsDateIn(List<Date> values) {
            addCriterionForJDBCDate("statistics_date in", values, "statisticsDate");
            return (Criteria) this;
        }

        public Criteria andStatisticsDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("statistics_date not in", values, "statisticsDate");
            return (Criteria) this;
        }

        public Criteria andStatisticsDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("statistics_date between", value1, value2, "statisticsDate");
            return (Criteria) this;
        }

        public Criteria andStatisticsDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("statistics_date not between", value1, value2, "statisticsDate");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Integer value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Integer value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Integer value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Integer value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Integer value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Integer> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Integer> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Integer value1, Integer value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Integer value1, Integer value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIsNull() {
            addCriterion("company_name is null");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIsNotNull() {
            addCriterion("company_name is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyNameEqualTo(String value) {
            addCriterion("company_name =", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotEqualTo(String value) {
            addCriterion("company_name <>", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameGreaterThan(String value) {
            addCriterion("company_name >", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameGreaterThanOrEqualTo(String value) {
            addCriterion("company_name >=", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLessThan(String value) {
            addCriterion("company_name <", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLessThanOrEqualTo(String value) {
            addCriterion("company_name <=", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLike(String value) {
            addCriterion("company_name like", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotLike(String value) {
            addCriterion("company_name not like", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIn(List<String> values) {
            addCriterion("company_name in", values, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotIn(List<String> values) {
            addCriterion("company_name not in", values, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameBetween(String value1, String value2) {
            addCriterion("company_name between", value1, value2, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotBetween(String value1, String value2) {
            addCriterion("company_name not between", value1, value2, "companyName");
            return (Criteria) this;
        }

        public Criteria andAccountTypeIsNull() {
            addCriterion("account_type is null");
            return (Criteria) this;
        }

        public Criteria andAccountTypeIsNotNull() {
            addCriterion("account_type is not null");
            return (Criteria) this;
        }

        public Criteria andAccountTypeEqualTo(Byte value) {
            addCriterion("account_type =", value, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeNotEqualTo(Byte value) {
            addCriterion("account_type <>", value, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeGreaterThan(Byte value) {
            addCriterion("account_type >", value, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("account_type >=", value, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeLessThan(Byte value) {
            addCriterion("account_type <", value, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeLessThanOrEqualTo(Byte value) {
            addCriterion("account_type <=", value, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeIn(List<Byte> values) {
            addCriterion("account_type in", values, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeNotIn(List<Byte> values) {
            addCriterion("account_type not in", values, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeBetween(Byte value1, Byte value2) {
            addCriterion("account_type between", value1, value2, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("account_type not between", value1, value2, "accountType");
            return (Criteria) this;
        }

        public Criteria andCompanyStatusIsNull() {
            addCriterion("company_status is null");
            return (Criteria) this;
        }

        public Criteria andCompanyStatusIsNotNull() {
            addCriterion("company_status is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyStatusEqualTo(Byte value) {
            addCriterion("company_status =", value, "companyStatus");
            return (Criteria) this;
        }

        public Criteria andCompanyStatusNotEqualTo(Byte value) {
            addCriterion("company_status <>", value, "companyStatus");
            return (Criteria) this;
        }

        public Criteria andCompanyStatusGreaterThan(Byte value) {
            addCriterion("company_status >", value, "companyStatus");
            return (Criteria) this;
        }

        public Criteria andCompanyStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("company_status >=", value, "companyStatus");
            return (Criteria) this;
        }

        public Criteria andCompanyStatusLessThan(Byte value) {
            addCriterion("company_status <", value, "companyStatus");
            return (Criteria) this;
        }

        public Criteria andCompanyStatusLessThanOrEqualTo(Byte value) {
            addCriterion("company_status <=", value, "companyStatus");
            return (Criteria) this;
        }

        public Criteria andCompanyStatusIn(List<Byte> values) {
            addCriterion("company_status in", values, "companyStatus");
            return (Criteria) this;
        }

        public Criteria andCompanyStatusNotIn(List<Byte> values) {
            addCriterion("company_status not in", values, "companyStatus");
            return (Criteria) this;
        }

        public Criteria andCompanyStatusBetween(Byte value1, Byte value2) {
            addCriterion("company_status between", value1, value2, "companyStatus");
            return (Criteria) this;
        }

        public Criteria andCompanyStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("company_status not between", value1, value2, "companyStatus");
            return (Criteria) this;
        }

        public Criteria andLastLoginTimeIsNull() {
            addCriterion("last_login_time is null");
            return (Criteria) this;
        }

        public Criteria andLastLoginTimeIsNotNull() {
            addCriterion("last_login_time is not null");
            return (Criteria) this;
        }

        public Criteria andLastLoginTimeEqualTo(Date value) {
            addCriterion("last_login_time =", value, "lastLoginTime");
            return (Criteria) this;
        }

        public Criteria andLastLoginTimeNotEqualTo(Date value) {
            addCriterion("last_login_time <>", value, "lastLoginTime");
            return (Criteria) this;
        }

        public Criteria andLastLoginTimeGreaterThan(Date value) {
            addCriterion("last_login_time >", value, "lastLoginTime");
            return (Criteria) this;
        }

        public Criteria andLastLoginTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("last_login_time >=", value, "lastLoginTime");
            return (Criteria) this;
        }

        public Criteria andLastLoginTimeLessThan(Date value) {
            addCriterion("last_login_time <", value, "lastLoginTime");
            return (Criteria) this;
        }

        public Criteria andLastLoginTimeLessThanOrEqualTo(Date value) {
            addCriterion("last_login_time <=", value, "lastLoginTime");
            return (Criteria) this;
        }

        public Criteria andLastLoginTimeIn(List<Date> values) {
            addCriterion("last_login_time in", values, "lastLoginTime");
            return (Criteria) this;
        }

        public Criteria andLastLoginTimeNotIn(List<Date> values) {
            addCriterion("last_login_time not in", values, "lastLoginTime");
            return (Criteria) this;
        }

        public Criteria andLastLoginTimeBetween(Date value1, Date value2) {
            addCriterion("last_login_time between", value1, value2, "lastLoginTime");
            return (Criteria) this;
        }

        public Criteria andLastLoginTimeNotBetween(Date value1, Date value2) {
            addCriterion("last_login_time not between", value1, value2, "lastLoginTime");
            return (Criteria) this;
        }

        public Criteria andCustomerCountIsNull() {
            addCriterion("customer_count is null");
            return (Criteria) this;
        }

        public Criteria andCustomerCountIsNotNull() {
            addCriterion("customer_count is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerCountEqualTo(Integer value) {
            addCriterion("customer_count =", value, "customerCount");
            return (Criteria) this;
        }

        public Criteria andCustomerCountNotEqualTo(Integer value) {
            addCriterion("customer_count <>", value, "customerCount");
            return (Criteria) this;
        }

        public Criteria andCustomerCountGreaterThan(Integer value) {
            addCriterion("customer_count >", value, "customerCount");
            return (Criteria) this;
        }

        public Criteria andCustomerCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("customer_count >=", value, "customerCount");
            return (Criteria) this;
        }

        public Criteria andCustomerCountLessThan(Integer value) {
            addCriterion("customer_count <", value, "customerCount");
            return (Criteria) this;
        }

        public Criteria andCustomerCountLessThanOrEqualTo(Integer value) {
            addCriterion("customer_count <=", value, "customerCount");
            return (Criteria) this;
        }

        public Criteria andCustomerCountIn(List<Integer> values) {
            addCriterion("customer_count in", values, "customerCount");
            return (Criteria) this;
        }

        public Criteria andCustomerCountNotIn(List<Integer> values) {
            addCriterion("customer_count not in", values, "customerCount");
            return (Criteria) this;
        }

        public Criteria andCustomerCountBetween(Integer value1, Integer value2) {
            addCriterion("customer_count between", value1, value2, "customerCount");
            return (Criteria) this;
        }

        public Criteria andCustomerCountNotBetween(Integer value1, Integer value2) {
            addCriterion("customer_count not between", value1, value2, "customerCount");
            return (Criteria) this;
        }

        public Criteria andDriverCountIsNull() {
            addCriterion("driver_count is null");
            return (Criteria) this;
        }

        public Criteria andDriverCountIsNotNull() {
            addCriterion("driver_count is not null");
            return (Criteria) this;
        }

        public Criteria andDriverCountEqualTo(Integer value) {
            addCriterion("driver_count =", value, "driverCount");
            return (Criteria) this;
        }

        public Criteria andDriverCountNotEqualTo(Integer value) {
            addCriterion("driver_count <>", value, "driverCount");
            return (Criteria) this;
        }

        public Criteria andDriverCountGreaterThan(Integer value) {
            addCriterion("driver_count >", value, "driverCount");
            return (Criteria) this;
        }

        public Criteria andDriverCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("driver_count >=", value, "driverCount");
            return (Criteria) this;
        }

        public Criteria andDriverCountLessThan(Integer value) {
            addCriterion("driver_count <", value, "driverCount");
            return (Criteria) this;
        }

        public Criteria andDriverCountLessThanOrEqualTo(Integer value) {
            addCriterion("driver_count <=", value, "driverCount");
            return (Criteria) this;
        }

        public Criteria andDriverCountIn(List<Integer> values) {
            addCriterion("driver_count in", values, "driverCount");
            return (Criteria) this;
        }

        public Criteria andDriverCountNotIn(List<Integer> values) {
            addCriterion("driver_count not in", values, "driverCount");
            return (Criteria) this;
        }

        public Criteria andDriverCountBetween(Integer value1, Integer value2) {
            addCriterion("driver_count between", value1, value2, "driverCount");
            return (Criteria) this;
        }

        public Criteria andDriverCountNotBetween(Integer value1, Integer value2) {
            addCriterion("driver_count not between", value1, value2, "driverCount");
            return (Criteria) this;
        }

        public Criteria andDriverShouqiCountIsNull() {
            addCriterion("driver_shouqi_count is null");
            return (Criteria) this;
        }

        public Criteria andDriverShouqiCountIsNotNull() {
            addCriterion("driver_shouqi_count is not null");
            return (Criteria) this;
        }

        public Criteria andDriverShouqiCountEqualTo(Integer value) {
            addCriterion("driver_shouqi_count =", value, "driverShouqiCount");
            return (Criteria) this;
        }

        public Criteria andDriverShouqiCountNotEqualTo(Integer value) {
            addCriterion("driver_shouqi_count <>", value, "driverShouqiCount");
            return (Criteria) this;
        }

        public Criteria andDriverShouqiCountGreaterThan(Integer value) {
            addCriterion("driver_shouqi_count >", value, "driverShouqiCount");
            return (Criteria) this;
        }

        public Criteria andDriverShouqiCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("driver_shouqi_count >=", value, "driverShouqiCount");
            return (Criteria) this;
        }

        public Criteria andDriverShouqiCountLessThan(Integer value) {
            addCriterion("driver_shouqi_count <", value, "driverShouqiCount");
            return (Criteria) this;
        }

        public Criteria andDriverShouqiCountLessThanOrEqualTo(Integer value) {
            addCriterion("driver_shouqi_count <=", value, "driverShouqiCount");
            return (Criteria) this;
        }

        public Criteria andDriverShouqiCountIn(List<Integer> values) {
            addCriterion("driver_shouqi_count in", values, "driverShouqiCount");
            return (Criteria) this;
        }

        public Criteria andDriverShouqiCountNotIn(List<Integer> values) {
            addCriterion("driver_shouqi_count not in", values, "driverShouqiCount");
            return (Criteria) this;
        }

        public Criteria andDriverShouqiCountBetween(Integer value1, Integer value2) {
            addCriterion("driver_shouqi_count between", value1, value2, "driverShouqiCount");
            return (Criteria) this;
        }

        public Criteria andDriverShouqiCountNotBetween(Integer value1, Integer value2) {
            addCriterion("driver_shouqi_count not between", value1, value2, "driverShouqiCount");
            return (Criteria) this;
        }

        public Criteria andDriverZiyouCountIsNull() {
            addCriterion("driver_ziyou_count is null");
            return (Criteria) this;
        }

        public Criteria andDriverZiyouCountIsNotNull() {
            addCriterion("driver_ziyou_count is not null");
            return (Criteria) this;
        }

        public Criteria andDriverZiyouCountEqualTo(Integer value) {
            addCriterion("driver_ziyou_count =", value, "driverZiyouCount");
            return (Criteria) this;
        }

        public Criteria andDriverZiyouCountNotEqualTo(Integer value) {
            addCriterion("driver_ziyou_count <>", value, "driverZiyouCount");
            return (Criteria) this;
        }

        public Criteria andDriverZiyouCountGreaterThan(Integer value) {
            addCriterion("driver_ziyou_count >", value, "driverZiyouCount");
            return (Criteria) this;
        }

        public Criteria andDriverZiyouCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("driver_ziyou_count >=", value, "driverZiyouCount");
            return (Criteria) this;
        }

        public Criteria andDriverZiyouCountLessThan(Integer value) {
            addCriterion("driver_ziyou_count <", value, "driverZiyouCount");
            return (Criteria) this;
        }

        public Criteria andDriverZiyouCountLessThanOrEqualTo(Integer value) {
            addCriterion("driver_ziyou_count <=", value, "driverZiyouCount");
            return (Criteria) this;
        }

        public Criteria andDriverZiyouCountIn(List<Integer> values) {
            addCriterion("driver_ziyou_count in", values, "driverZiyouCount");
            return (Criteria) this;
        }

        public Criteria andDriverZiyouCountNotIn(List<Integer> values) {
            addCriterion("driver_ziyou_count not in", values, "driverZiyouCount");
            return (Criteria) this;
        }

        public Criteria andDriverZiyouCountBetween(Integer value1, Integer value2) {
            addCriterion("driver_ziyou_count between", value1, value2, "driverZiyouCount");
            return (Criteria) this;
        }

        public Criteria andDriverZiyouCountNotBetween(Integer value1, Integer value2) {
            addCriterion("driver_ziyou_count not between", value1, value2, "driverZiyouCount");
            return (Criteria) this;
        }

        public Criteria andDriverBusinessCountIsNull() {
            addCriterion("driver_business_count is null");
            return (Criteria) this;
        }

        public Criteria andDriverBusinessCountIsNotNull() {
            addCriterion("driver_business_count is not null");
            return (Criteria) this;
        }

        public Criteria andDriverBusinessCountEqualTo(Integer value) {
            addCriterion("driver_business_count =", value, "driverBusinessCount");
            return (Criteria) this;
        }

        public Criteria andDriverBusinessCountNotEqualTo(Integer value) {
            addCriterion("driver_business_count <>", value, "driverBusinessCount");
            return (Criteria) this;
        }

        public Criteria andDriverBusinessCountGreaterThan(Integer value) {
            addCriterion("driver_business_count >", value, "driverBusinessCount");
            return (Criteria) this;
        }

        public Criteria andDriverBusinessCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("driver_business_count >=", value, "driverBusinessCount");
            return (Criteria) this;
        }

        public Criteria andDriverBusinessCountLessThan(Integer value) {
            addCriterion("driver_business_count <", value, "driverBusinessCount");
            return (Criteria) this;
        }

        public Criteria andDriverBusinessCountLessThanOrEqualTo(Integer value) {
            addCriterion("driver_business_count <=", value, "driverBusinessCount");
            return (Criteria) this;
        }

        public Criteria andDriverBusinessCountIn(List<Integer> values) {
            addCriterion("driver_business_count in", values, "driverBusinessCount");
            return (Criteria) this;
        }

        public Criteria andDriverBusinessCountNotIn(List<Integer> values) {
            addCriterion("driver_business_count not in", values, "driverBusinessCount");
            return (Criteria) this;
        }

        public Criteria andDriverBusinessCountBetween(Integer value1, Integer value2) {
            addCriterion("driver_business_count between", value1, value2, "driverBusinessCount");
            return (Criteria) this;
        }

        public Criteria andDriverBusinessCountNotBetween(Integer value1, Integer value2) {
            addCriterion("driver_business_count not between", value1, value2, "driverBusinessCount");
            return (Criteria) this;
        }

        public Criteria andDriverThirdCountIsNull() {
            addCriterion("driver_third_count is null");
            return (Criteria) this;
        }

        public Criteria andDriverThirdCountIsNotNull() {
            addCriterion("driver_third_count is not null");
            return (Criteria) this;
        }

        public Criteria andDriverThirdCountEqualTo(Integer value) {
            addCriterion("driver_third_count =", value, "driverThirdCount");
            return (Criteria) this;
        }

        public Criteria andDriverThirdCountNotEqualTo(Integer value) {
            addCriterion("driver_third_count <>", value, "driverThirdCount");
            return (Criteria) this;
        }

        public Criteria andDriverThirdCountGreaterThan(Integer value) {
            addCriterion("driver_third_count >", value, "driverThirdCount");
            return (Criteria) this;
        }

        public Criteria andDriverThirdCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("driver_third_count >=", value, "driverThirdCount");
            return (Criteria) this;
        }

        public Criteria andDriverThirdCountLessThan(Integer value) {
            addCriterion("driver_third_count <", value, "driverThirdCount");
            return (Criteria) this;
        }

        public Criteria andDriverThirdCountLessThanOrEqualTo(Integer value) {
            addCriterion("driver_third_count <=", value, "driverThirdCount");
            return (Criteria) this;
        }

        public Criteria andDriverThirdCountIn(List<Integer> values) {
            addCriterion("driver_third_count in", values, "driverThirdCount");
            return (Criteria) this;
        }

        public Criteria andDriverThirdCountNotIn(List<Integer> values) {
            addCriterion("driver_third_count not in", values, "driverThirdCount");
            return (Criteria) this;
        }

        public Criteria andDriverThirdCountBetween(Integer value1, Integer value2) {
            addCriterion("driver_third_count between", value1, value2, "driverThirdCount");
            return (Criteria) this;
        }

        public Criteria andDriverThirdCountNotBetween(Integer value1, Integer value2) {
            addCriterion("driver_third_count not between", value1, value2, "driverThirdCount");
            return (Criteria) this;
        }

        public Criteria andVehicleCountIsNull() {
            addCriterion("vehicle_count is null");
            return (Criteria) this;
        }

        public Criteria andVehicleCountIsNotNull() {
            addCriterion("vehicle_count is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleCountEqualTo(Integer value) {
            addCriterion("vehicle_count =", value, "vehicleCount");
            return (Criteria) this;
        }

        public Criteria andVehicleCountNotEqualTo(Integer value) {
            addCriterion("vehicle_count <>", value, "vehicleCount");
            return (Criteria) this;
        }

        public Criteria andVehicleCountGreaterThan(Integer value) {
            addCriterion("vehicle_count >", value, "vehicleCount");
            return (Criteria) this;
        }

        public Criteria andVehicleCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("vehicle_count >=", value, "vehicleCount");
            return (Criteria) this;
        }

        public Criteria andVehicleCountLessThan(Integer value) {
            addCriterion("vehicle_count <", value, "vehicleCount");
            return (Criteria) this;
        }

        public Criteria andVehicleCountLessThanOrEqualTo(Integer value) {
            addCriterion("vehicle_count <=", value, "vehicleCount");
            return (Criteria) this;
        }

        public Criteria andVehicleCountIn(List<Integer> values) {
            addCriterion("vehicle_count in", values, "vehicleCount");
            return (Criteria) this;
        }

        public Criteria andVehicleCountNotIn(List<Integer> values) {
            addCriterion("vehicle_count not in", values, "vehicleCount");
            return (Criteria) this;
        }

        public Criteria andVehicleCountBetween(Integer value1, Integer value2) {
            addCriterion("vehicle_count between", value1, value2, "vehicleCount");
            return (Criteria) this;
        }

        public Criteria andVehicleCountNotBetween(Integer value1, Integer value2) {
            addCriterion("vehicle_count not between", value1, value2, "vehicleCount");
            return (Criteria) this;
        }

        public Criteria andVehicleShouqiCountIsNull() {
            addCriterion("vehicle_shouqi_count is null");
            return (Criteria) this;
        }

        public Criteria andVehicleShouqiCountIsNotNull() {
            addCriterion("vehicle_shouqi_count is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleShouqiCountEqualTo(Integer value) {
            addCriterion("vehicle_shouqi_count =", value, "vehicleShouqiCount");
            return (Criteria) this;
        }

        public Criteria andVehicleShouqiCountNotEqualTo(Integer value) {
            addCriterion("vehicle_shouqi_count <>", value, "vehicleShouqiCount");
            return (Criteria) this;
        }

        public Criteria andVehicleShouqiCountGreaterThan(Integer value) {
            addCriterion("vehicle_shouqi_count >", value, "vehicleShouqiCount");
            return (Criteria) this;
        }

        public Criteria andVehicleShouqiCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("vehicle_shouqi_count >=", value, "vehicleShouqiCount");
            return (Criteria) this;
        }

        public Criteria andVehicleShouqiCountLessThan(Integer value) {
            addCriterion("vehicle_shouqi_count <", value, "vehicleShouqiCount");
            return (Criteria) this;
        }

        public Criteria andVehicleShouqiCountLessThanOrEqualTo(Integer value) {
            addCriterion("vehicle_shouqi_count <=", value, "vehicleShouqiCount");
            return (Criteria) this;
        }

        public Criteria andVehicleShouqiCountIn(List<Integer> values) {
            addCriterion("vehicle_shouqi_count in", values, "vehicleShouqiCount");
            return (Criteria) this;
        }

        public Criteria andVehicleShouqiCountNotIn(List<Integer> values) {
            addCriterion("vehicle_shouqi_count not in", values, "vehicleShouqiCount");
            return (Criteria) this;
        }

        public Criteria andVehicleShouqiCountBetween(Integer value1, Integer value2) {
            addCriterion("vehicle_shouqi_count between", value1, value2, "vehicleShouqiCount");
            return (Criteria) this;
        }

        public Criteria andVehicleShouqiCountNotBetween(Integer value1, Integer value2) {
            addCriterion("vehicle_shouqi_count not between", value1, value2, "vehicleShouqiCount");
            return (Criteria) this;
        }

        public Criteria andVehicleZiyouCountIsNull() {
            addCriterion("vehicle_ziyou_count is null");
            return (Criteria) this;
        }

        public Criteria andVehicleZiyouCountIsNotNull() {
            addCriterion("vehicle_ziyou_count is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleZiyouCountEqualTo(Integer value) {
            addCriterion("vehicle_ziyou_count =", value, "vehicleZiyouCount");
            return (Criteria) this;
        }

        public Criteria andVehicleZiyouCountNotEqualTo(Integer value) {
            addCriterion("vehicle_ziyou_count <>", value, "vehicleZiyouCount");
            return (Criteria) this;
        }

        public Criteria andVehicleZiyouCountGreaterThan(Integer value) {
            addCriterion("vehicle_ziyou_count >", value, "vehicleZiyouCount");
            return (Criteria) this;
        }

        public Criteria andVehicleZiyouCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("vehicle_ziyou_count >=", value, "vehicleZiyouCount");
            return (Criteria) this;
        }

        public Criteria andVehicleZiyouCountLessThan(Integer value) {
            addCriterion("vehicle_ziyou_count <", value, "vehicleZiyouCount");
            return (Criteria) this;
        }

        public Criteria andVehicleZiyouCountLessThanOrEqualTo(Integer value) {
            addCriterion("vehicle_ziyou_count <=", value, "vehicleZiyouCount");
            return (Criteria) this;
        }

        public Criteria andVehicleZiyouCountIn(List<Integer> values) {
            addCriterion("vehicle_ziyou_count in", values, "vehicleZiyouCount");
            return (Criteria) this;
        }

        public Criteria andVehicleZiyouCountNotIn(List<Integer> values) {
            addCriterion("vehicle_ziyou_count not in", values, "vehicleZiyouCount");
            return (Criteria) this;
        }

        public Criteria andVehicleZiyouCountBetween(Integer value1, Integer value2) {
            addCriterion("vehicle_ziyou_count between", value1, value2, "vehicleZiyouCount");
            return (Criteria) this;
        }

        public Criteria andVehicleZiyouCountNotBetween(Integer value1, Integer value2) {
            addCriterion("vehicle_ziyou_count not between", value1, value2, "vehicleZiyouCount");
            return (Criteria) this;
        }

        public Criteria andVehicleThirdCountIsNull() {
            addCriterion("vehicle_third_count is null");
            return (Criteria) this;
        }

        public Criteria andVehicleThirdCountIsNotNull() {
            addCriterion("vehicle_third_count is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleThirdCountEqualTo(Integer value) {
            addCriterion("vehicle_third_count =", value, "vehicleThirdCount");
            return (Criteria) this;
        }

        public Criteria andVehicleThirdCountNotEqualTo(Integer value) {
            addCriterion("vehicle_third_count <>", value, "vehicleThirdCount");
            return (Criteria) this;
        }

        public Criteria andVehicleThirdCountGreaterThan(Integer value) {
            addCriterion("vehicle_third_count >", value, "vehicleThirdCount");
            return (Criteria) this;
        }

        public Criteria andVehicleThirdCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("vehicle_third_count >=", value, "vehicleThirdCount");
            return (Criteria) this;
        }

        public Criteria andVehicleThirdCountLessThan(Integer value) {
            addCriterion("vehicle_third_count <", value, "vehicleThirdCount");
            return (Criteria) this;
        }

        public Criteria andVehicleThirdCountLessThanOrEqualTo(Integer value) {
            addCriterion("vehicle_third_count <=", value, "vehicleThirdCount");
            return (Criteria) this;
        }

        public Criteria andVehicleThirdCountIn(List<Integer> values) {
            addCriterion("vehicle_third_count in", values, "vehicleThirdCount");
            return (Criteria) this;
        }

        public Criteria andVehicleThirdCountNotIn(List<Integer> values) {
            addCriterion("vehicle_third_count not in", values, "vehicleThirdCount");
            return (Criteria) this;
        }

        public Criteria andVehicleThirdCountBetween(Integer value1, Integer value2) {
            addCriterion("vehicle_third_count between", value1, value2, "vehicleThirdCount");
            return (Criteria) this;
        }

        public Criteria andVehicleThirdCountNotBetween(Integer value1, Integer value2) {
            addCriterion("vehicle_third_count not between", value1, value2, "vehicleThirdCount");
            return (Criteria) this;
        }

        public Criteria andVehicleStaffCountIsNull() {
            addCriterion("vehicle_staff_count is null");
            return (Criteria) this;
        }

        public Criteria andVehicleStaffCountIsNotNull() {
            addCriterion("vehicle_staff_count is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleStaffCountEqualTo(Integer value) {
            addCriterion("vehicle_staff_count =", value, "vehicleStaffCount");
            return (Criteria) this;
        }

        public Criteria andVehicleStaffCountNotEqualTo(Integer value) {
            addCriterion("vehicle_staff_count <>", value, "vehicleStaffCount");
            return (Criteria) this;
        }

        public Criteria andVehicleStaffCountGreaterThan(Integer value) {
            addCriterion("vehicle_staff_count >", value, "vehicleStaffCount");
            return (Criteria) this;
        }

        public Criteria andVehicleStaffCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("vehicle_staff_count >=", value, "vehicleStaffCount");
            return (Criteria) this;
        }

        public Criteria andVehicleStaffCountLessThan(Integer value) {
            addCriterion("vehicle_staff_count <", value, "vehicleStaffCount");
            return (Criteria) this;
        }

        public Criteria andVehicleStaffCountLessThanOrEqualTo(Integer value) {
            addCriterion("vehicle_staff_count <=", value, "vehicleStaffCount");
            return (Criteria) this;
        }

        public Criteria andVehicleStaffCountIn(List<Integer> values) {
            addCriterion("vehicle_staff_count in", values, "vehicleStaffCount");
            return (Criteria) this;
        }

        public Criteria andVehicleStaffCountNotIn(List<Integer> values) {
            addCriterion("vehicle_staff_count not in", values, "vehicleStaffCount");
            return (Criteria) this;
        }

        public Criteria andVehicleStaffCountBetween(Integer value1, Integer value2) {
            addCriterion("vehicle_staff_count between", value1, value2, "vehicleStaffCount");
            return (Criteria) this;
        }

        public Criteria andVehicleStaffCountNotBetween(Integer value1, Integer value2) {
            addCriterion("vehicle_staff_count not between", value1, value2, "vehicleStaffCount");
            return (Criteria) this;
        }

        public Criteria andOrderApplyCountIsNull() {
            addCriterion("order_apply_count is null");
            return (Criteria) this;
        }

        public Criteria andOrderApplyCountIsNotNull() {
            addCriterion("order_apply_count is not null");
            return (Criteria) this;
        }

        public Criteria andOrderApplyCountEqualTo(Integer value) {
            addCriterion("order_apply_count =", value, "orderApplyCount");
            return (Criteria) this;
        }

        public Criteria andOrderApplyCountNotEqualTo(Integer value) {
            addCriterion("order_apply_count <>", value, "orderApplyCount");
            return (Criteria) this;
        }

        public Criteria andOrderApplyCountGreaterThan(Integer value) {
            addCriterion("order_apply_count >", value, "orderApplyCount");
            return (Criteria) this;
        }

        public Criteria andOrderApplyCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_apply_count >=", value, "orderApplyCount");
            return (Criteria) this;
        }

        public Criteria andOrderApplyCountLessThan(Integer value) {
            addCriterion("order_apply_count <", value, "orderApplyCount");
            return (Criteria) this;
        }

        public Criteria andOrderApplyCountLessThanOrEqualTo(Integer value) {
            addCriterion("order_apply_count <=", value, "orderApplyCount");
            return (Criteria) this;
        }

        public Criteria andOrderApplyCountIn(List<Integer> values) {
            addCriterion("order_apply_count in", values, "orderApplyCount");
            return (Criteria) this;
        }

        public Criteria andOrderApplyCountNotIn(List<Integer> values) {
            addCriterion("order_apply_count not in", values, "orderApplyCount");
            return (Criteria) this;
        }

        public Criteria andOrderApplyCountBetween(Integer value1, Integer value2) {
            addCriterion("order_apply_count between", value1, value2, "orderApplyCount");
            return (Criteria) this;
        }

        public Criteria andOrderApplyCountNotBetween(Integer value1, Integer value2) {
            addCriterion("order_apply_count not between", value1, value2, "orderApplyCount");
            return (Criteria) this;
        }

        public Criteria andOrderApplyInternalCountIsNull() {
            addCriterion("order_apply_internal_count is null");
            return (Criteria) this;
        }

        public Criteria andOrderApplyInternalCountIsNotNull() {
            addCriterion("order_apply_internal_count is not null");
            return (Criteria) this;
        }

        public Criteria andOrderApplyInternalCountEqualTo(Integer value) {
            addCriterion("order_apply_internal_count =", value, "orderApplyInternalCount");
            return (Criteria) this;
        }

        public Criteria andOrderApplyInternalCountNotEqualTo(Integer value) {
            addCriterion("order_apply_internal_count <>", value, "orderApplyInternalCount");
            return (Criteria) this;
        }

        public Criteria andOrderApplyInternalCountGreaterThan(Integer value) {
            addCriterion("order_apply_internal_count >", value, "orderApplyInternalCount");
            return (Criteria) this;
        }

        public Criteria andOrderApplyInternalCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_apply_internal_count >=", value, "orderApplyInternalCount");
            return (Criteria) this;
        }

        public Criteria andOrderApplyInternalCountLessThan(Integer value) {
            addCriterion("order_apply_internal_count <", value, "orderApplyInternalCount");
            return (Criteria) this;
        }

        public Criteria andOrderApplyInternalCountLessThanOrEqualTo(Integer value) {
            addCriterion("order_apply_internal_count <=", value, "orderApplyInternalCount");
            return (Criteria) this;
        }

        public Criteria andOrderApplyInternalCountIn(List<Integer> values) {
            addCriterion("order_apply_internal_count in", values, "orderApplyInternalCount");
            return (Criteria) this;
        }

        public Criteria andOrderApplyInternalCountNotIn(List<Integer> values) {
            addCriterion("order_apply_internal_count not in", values, "orderApplyInternalCount");
            return (Criteria) this;
        }

        public Criteria andOrderApplyInternalCountBetween(Integer value1, Integer value2) {
            addCriterion("order_apply_internal_count between", value1, value2, "orderApplyInternalCount");
            return (Criteria) this;
        }

        public Criteria andOrderApplyInternalCountNotBetween(Integer value1, Integer value2) {
            addCriterion("order_apply_internal_count not between", value1, value2, "orderApplyInternalCount");
            return (Criteria) this;
        }

        public Criteria andOrderApplyMotorcadeCountIsNull() {
            addCriterion("order_apply_motorcade_count is null");
            return (Criteria) this;
        }

        public Criteria andOrderApplyMotorcadeCountIsNotNull() {
            addCriterion("order_apply_motorcade_count is not null");
            return (Criteria) this;
        }

        public Criteria andOrderApplyMotorcadeCountEqualTo(Integer value) {
            addCriterion("order_apply_motorcade_count =", value, "orderApplyMotorcadeCount");
            return (Criteria) this;
        }

        public Criteria andOrderApplyMotorcadeCountNotEqualTo(Integer value) {
            addCriterion("order_apply_motorcade_count <>", value, "orderApplyMotorcadeCount");
            return (Criteria) this;
        }

        public Criteria andOrderApplyMotorcadeCountGreaterThan(Integer value) {
            addCriterion("order_apply_motorcade_count >", value, "orderApplyMotorcadeCount");
            return (Criteria) this;
        }

        public Criteria andOrderApplyMotorcadeCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_apply_motorcade_count >=", value, "orderApplyMotorcadeCount");
            return (Criteria) this;
        }

        public Criteria andOrderApplyMotorcadeCountLessThan(Integer value) {
            addCriterion("order_apply_motorcade_count <", value, "orderApplyMotorcadeCount");
            return (Criteria) this;
        }

        public Criteria andOrderApplyMotorcadeCountLessThanOrEqualTo(Integer value) {
            addCriterion("order_apply_motorcade_count <=", value, "orderApplyMotorcadeCount");
            return (Criteria) this;
        }

        public Criteria andOrderApplyMotorcadeCountIn(List<Integer> values) {
            addCriterion("order_apply_motorcade_count in", values, "orderApplyMotorcadeCount");
            return (Criteria) this;
        }

        public Criteria andOrderApplyMotorcadeCountNotIn(List<Integer> values) {
            addCriterion("order_apply_motorcade_count not in", values, "orderApplyMotorcadeCount");
            return (Criteria) this;
        }

        public Criteria andOrderApplyMotorcadeCountBetween(Integer value1, Integer value2) {
            addCriterion("order_apply_motorcade_count between", value1, value2, "orderApplyMotorcadeCount");
            return (Criteria) this;
        }

        public Criteria andOrderApplyMotorcadeCountNotBetween(Integer value1, Integer value2) {
            addCriterion("order_apply_motorcade_count not between", value1, value2, "orderApplyMotorcadeCount");
            return (Criteria) this;
        }

        public Criteria andNoSleepStatusIsNull() {
            addCriterion("no_sleep_status is null");
            return (Criteria) this;
        }

        public Criteria andNoSleepStatusIsNotNull() {
            addCriterion("no_sleep_status is not null");
            return (Criteria) this;
        }

        public Criteria andNoSleepStatusEqualTo(Integer value) {
            addCriterion("no_sleep_status =", value, "noSleepStatus");
            return (Criteria) this;
        }

        public Criteria andNoSleepStatusNotEqualTo(Integer value) {
            addCriterion("no_sleep_status <>", value, "noSleepStatus");
            return (Criteria) this;
        }

        public Criteria andNoSleepStatusGreaterThan(Integer value) {
            addCriterion("no_sleep_status >", value, "noSleepStatus");
            return (Criteria) this;
        }

        public Criteria andNoSleepStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("no_sleep_status >=", value, "noSleepStatus");
            return (Criteria) this;
        }

        public Criteria andNoSleepStatusLessThan(Integer value) {
            addCriterion("no_sleep_status <", value, "noSleepStatus");
            return (Criteria) this;
        }

        public Criteria andNoSleepStatusLessThanOrEqualTo(Integer value) {
            addCriterion("no_sleep_status <=", value, "noSleepStatus");
            return (Criteria) this;
        }

        public Criteria andNoSleepStatusIn(List<Integer> values) {
            addCriterion("no_sleep_status in", values, "noSleepStatus");
            return (Criteria) this;
        }

        public Criteria andNoSleepStatusNotIn(List<Integer> values) {
            addCriterion("no_sleep_status not in", values, "noSleepStatus");
            return (Criteria) this;
        }

        public Criteria andNoSleepStatusBetween(Integer value1, Integer value2) {
            addCriterion("no_sleep_status between", value1, value2, "noSleepStatus");
            return (Criteria) this;
        }

        public Criteria andNoSleepStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("no_sleep_status not between", value1, value2, "noSleepStatus");
            return (Criteria) this;
        }

        public Criteria andPrivateOrderCountIsNull() {
            addCriterion("private_order_count is null");
            return (Criteria) this;
        }

        public Criteria andPrivateOrderCountIsNotNull() {
            addCriterion("private_order_count is not null");
            return (Criteria) this;
        }

        public Criteria andPrivateOrderCountEqualTo(Integer value) {
            addCriterion("private_order_count =", value, "privateOrderCount");
            return (Criteria) this;
        }

        public Criteria andPrivateOrderCountNotEqualTo(Integer value) {
            addCriterion("private_order_count <>", value, "privateOrderCount");
            return (Criteria) this;
        }

        public Criteria andPrivateOrderCountGreaterThan(Integer value) {
            addCriterion("private_order_count >", value, "privateOrderCount");
            return (Criteria) this;
        }

        public Criteria andPrivateOrderCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("private_order_count >=", value, "privateOrderCount");
            return (Criteria) this;
        }

        public Criteria andPrivateOrderCountLessThan(Integer value) {
            addCriterion("private_order_count <", value, "privateOrderCount");
            return (Criteria) this;
        }

        public Criteria andPrivateOrderCountLessThanOrEqualTo(Integer value) {
            addCriterion("private_order_count <=", value, "privateOrderCount");
            return (Criteria) this;
        }

        public Criteria andPrivateOrderCountIn(List<Integer> values) {
            addCriterion("private_order_count in", values, "privateOrderCount");
            return (Criteria) this;
        }

        public Criteria andPrivateOrderCountNotIn(List<Integer> values) {
            addCriterion("private_order_count not in", values, "privateOrderCount");
            return (Criteria) this;
        }

        public Criteria andPrivateOrderCountBetween(Integer value1, Integer value2) {
            addCriterion("private_order_count between", value1, value2, "privateOrderCount");
            return (Criteria) this;
        }

        public Criteria andPrivateOrderCountNotBetween(Integer value1, Integer value2) {
            addCriterion("private_order_count not between", value1, value2, "privateOrderCount");
            return (Criteria) this;
        }

        public Criteria andSoOrderCountIsNull() {
            addCriterion("so_order_count is null");
            return (Criteria) this;
        }

        public Criteria andSoOrderCountIsNotNull() {
            addCriterion("so_order_count is not null");
            return (Criteria) this;
        }

        public Criteria andSoOrderCountEqualTo(Integer value) {
            addCriterion("so_order_count =", value, "soOrderCount");
            return (Criteria) this;
        }

        public Criteria andSoOrderCountNotEqualTo(Integer value) {
            addCriterion("so_order_count <>", value, "soOrderCount");
            return (Criteria) this;
        }

        public Criteria andSoOrderCountGreaterThan(Integer value) {
            addCriterion("so_order_count >", value, "soOrderCount");
            return (Criteria) this;
        }

        public Criteria andSoOrderCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("so_order_count >=", value, "soOrderCount");
            return (Criteria) this;
        }

        public Criteria andSoOrderCountLessThan(Integer value) {
            addCriterion("so_order_count <", value, "soOrderCount");
            return (Criteria) this;
        }

        public Criteria andSoOrderCountLessThanOrEqualTo(Integer value) {
            addCriterion("so_order_count <=", value, "soOrderCount");
            return (Criteria) this;
        }

        public Criteria andSoOrderCountIn(List<Integer> values) {
            addCriterion("so_order_count in", values, "soOrderCount");
            return (Criteria) this;
        }

        public Criteria andSoOrderCountNotIn(List<Integer> values) {
            addCriterion("so_order_count not in", values, "soOrderCount");
            return (Criteria) this;
        }

        public Criteria andSoOrderCountBetween(Integer value1, Integer value2) {
            addCriterion("so_order_count between", value1, value2, "soOrderCount");
            return (Criteria) this;
        }

        public Criteria andSoOrderCountNotBetween(Integer value1, Integer value2) {
            addCriterion("so_order_count not between", value1, value2, "soOrderCount");
            return (Criteria) this;
        }

        public Criteria andCoOrderCountIsNull() {
            addCriterion("co_order_count is null");
            return (Criteria) this;
        }

        public Criteria andCoOrderCountIsNotNull() {
            addCriterion("co_order_count is not null");
            return (Criteria) this;
        }

        public Criteria andCoOrderCountEqualTo(Integer value) {
            addCriterion("co_order_count =", value, "coOrderCount");
            return (Criteria) this;
        }

        public Criteria andCoOrderCountNotEqualTo(Integer value) {
            addCriterion("co_order_count <>", value, "coOrderCount");
            return (Criteria) this;
        }

        public Criteria andCoOrderCountGreaterThan(Integer value) {
            addCriterion("co_order_count >", value, "coOrderCount");
            return (Criteria) this;
        }

        public Criteria andCoOrderCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("co_order_count >=", value, "coOrderCount");
            return (Criteria) this;
        }

        public Criteria andCoOrderCountLessThan(Integer value) {
            addCriterion("co_order_count <", value, "coOrderCount");
            return (Criteria) this;
        }

        public Criteria andCoOrderCountLessThanOrEqualTo(Integer value) {
            addCriterion("co_order_count <=", value, "coOrderCount");
            return (Criteria) this;
        }

        public Criteria andCoOrderCountIn(List<Integer> values) {
            addCriterion("co_order_count in", values, "coOrderCount");
            return (Criteria) this;
        }

        public Criteria andCoOrderCountNotIn(List<Integer> values) {
            addCriterion("co_order_count not in", values, "coOrderCount");
            return (Criteria) this;
        }

        public Criteria andCoOrderCountBetween(Integer value1, Integer value2) {
            addCriterion("co_order_count between", value1, value2, "coOrderCount");
            return (Criteria) this;
        }

        public Criteria andCoOrderCountNotBetween(Integer value1, Integer value2) {
            addCriterion("co_order_count not between", value1, value2, "coOrderCount");
            return (Criteria) this;
        }

        public Criteria andDeviceShouqiCountIsNull() {
            addCriterion("device_shouqi_count is null");
            return (Criteria) this;
        }

        public Criteria andDeviceShouqiCountIsNotNull() {
            addCriterion("device_shouqi_count is not null");
            return (Criteria) this;
        }

        public Criteria andDeviceShouqiCountEqualTo(Integer value) {
            addCriterion("device_shouqi_count =", value, "deviceShouqiCount");
            return (Criteria) this;
        }

        public Criteria andDeviceShouqiCountNotEqualTo(Integer value) {
            addCriterion("device_shouqi_count <>", value, "deviceShouqiCount");
            return (Criteria) this;
        }

        public Criteria andDeviceShouqiCountGreaterThan(Integer value) {
            addCriterion("device_shouqi_count >", value, "deviceShouqiCount");
            return (Criteria) this;
        }

        public Criteria andDeviceShouqiCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("device_shouqi_count >=", value, "deviceShouqiCount");
            return (Criteria) this;
        }

        public Criteria andDeviceShouqiCountLessThan(Integer value) {
            addCriterion("device_shouqi_count <", value, "deviceShouqiCount");
            return (Criteria) this;
        }

        public Criteria andDeviceShouqiCountLessThanOrEqualTo(Integer value) {
            addCriterion("device_shouqi_count <=", value, "deviceShouqiCount");
            return (Criteria) this;
        }

        public Criteria andDeviceShouqiCountIn(List<Integer> values) {
            addCriterion("device_shouqi_count in", values, "deviceShouqiCount");
            return (Criteria) this;
        }

        public Criteria andDeviceShouqiCountNotIn(List<Integer> values) {
            addCriterion("device_shouqi_count not in", values, "deviceShouqiCount");
            return (Criteria) this;
        }

        public Criteria andDeviceShouqiCountBetween(Integer value1, Integer value2) {
            addCriterion("device_shouqi_count between", value1, value2, "deviceShouqiCount");
            return (Criteria) this;
        }

        public Criteria andDeviceShouqiCountNotBetween(Integer value1, Integer value2) {
            addCriterion("device_shouqi_count not between", value1, value2, "deviceShouqiCount");
            return (Criteria) this;
        }

        public Criteria andDeviceZiyouCountIsNull() {
            addCriterion("device_ziyou_count is null");
            return (Criteria) this;
        }

        public Criteria andDeviceZiyouCountIsNotNull() {
            addCriterion("device_ziyou_count is not null");
            return (Criteria) this;
        }

        public Criteria andDeviceZiyouCountEqualTo(Integer value) {
            addCriterion("device_ziyou_count =", value, "deviceZiyouCount");
            return (Criteria) this;
        }

        public Criteria andDeviceZiyouCountNotEqualTo(Integer value) {
            addCriterion("device_ziyou_count <>", value, "deviceZiyouCount");
            return (Criteria) this;
        }

        public Criteria andDeviceZiyouCountGreaterThan(Integer value) {
            addCriterion("device_ziyou_count >", value, "deviceZiyouCount");
            return (Criteria) this;
        }

        public Criteria andDeviceZiyouCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("device_ziyou_count >=", value, "deviceZiyouCount");
            return (Criteria) this;
        }

        public Criteria andDeviceZiyouCountLessThan(Integer value) {
            addCriterion("device_ziyou_count <", value, "deviceZiyouCount");
            return (Criteria) this;
        }

        public Criteria andDeviceZiyouCountLessThanOrEqualTo(Integer value) {
            addCriterion("device_ziyou_count <=", value, "deviceZiyouCount");
            return (Criteria) this;
        }

        public Criteria andDeviceZiyouCountIn(List<Integer> values) {
            addCriterion("device_ziyou_count in", values, "deviceZiyouCount");
            return (Criteria) this;
        }

        public Criteria andDeviceZiyouCountNotIn(List<Integer> values) {
            addCriterion("device_ziyou_count not in", values, "deviceZiyouCount");
            return (Criteria) this;
        }

        public Criteria andDeviceZiyouCountBetween(Integer value1, Integer value2) {
            addCriterion("device_ziyou_count between", value1, value2, "deviceZiyouCount");
            return (Criteria) this;
        }

        public Criteria andDeviceZiyouCountNotBetween(Integer value1, Integer value2) {
            addCriterion("device_ziyou_count not between", value1, value2, "deviceZiyouCount");
            return (Criteria) this;
        }

        public Criteria andApplyCountIsNull() {
            addCriterion("apply_count is null");
            return (Criteria) this;
        }

        public Criteria andApplyCountIsNotNull() {
            addCriterion("apply_count is not null");
            return (Criteria) this;
        }

        public Criteria andApplyCountEqualTo(Integer value) {
            addCriterion("apply_count =", value, "applyCount");
            return (Criteria) this;
        }

        public Criteria andApplyCountNotEqualTo(Integer value) {
            addCriterion("apply_count <>", value, "applyCount");
            return (Criteria) this;
        }

        public Criteria andApplyCountGreaterThan(Integer value) {
            addCriterion("apply_count >", value, "applyCount");
            return (Criteria) this;
        }

        public Criteria andApplyCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("apply_count >=", value, "applyCount");
            return (Criteria) this;
        }

        public Criteria andApplyCountLessThan(Integer value) {
            addCriterion("apply_count <", value, "applyCount");
            return (Criteria) this;
        }

        public Criteria andApplyCountLessThanOrEqualTo(Integer value) {
            addCriterion("apply_count <=", value, "applyCount");
            return (Criteria) this;
        }

        public Criteria andApplyCountIn(List<Integer> values) {
            addCriterion("apply_count in", values, "applyCount");
            return (Criteria) this;
        }

        public Criteria andApplyCountNotIn(List<Integer> values) {
            addCriterion("apply_count not in", values, "applyCount");
            return (Criteria) this;
        }

        public Criteria andApplyCountBetween(Integer value1, Integer value2) {
            addCriterion("apply_count between", value1, value2, "applyCount");
            return (Criteria) this;
        }

        public Criteria andApplyCountNotBetween(Integer value1, Integer value2) {
            addCriterion("apply_count not between", value1, value2, "applyCount");
            return (Criteria) this;
        }

        public Criteria andMaintenanceSqCountIsNull() {
            addCriterion("maintenance_sq_count is null");
            return (Criteria) this;
        }

        public Criteria andMaintenanceSqCountIsNotNull() {
            addCriterion("maintenance_sq_count is not null");
            return (Criteria) this;
        }

        public Criteria andMaintenanceSqCountEqualTo(Integer value) {
            addCriterion("maintenance_sq_count =", value, "maintenanceSqCount");
            return (Criteria) this;
        }

        public Criteria andMaintenanceSqCountNotEqualTo(Integer value) {
            addCriterion("maintenance_sq_count <>", value, "maintenanceSqCount");
            return (Criteria) this;
        }

        public Criteria andMaintenanceSqCountGreaterThan(Integer value) {
            addCriterion("maintenance_sq_count >", value, "maintenanceSqCount");
            return (Criteria) this;
        }

        public Criteria andMaintenanceSqCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("maintenance_sq_count >=", value, "maintenanceSqCount");
            return (Criteria) this;
        }

        public Criteria andMaintenanceSqCountLessThan(Integer value) {
            addCriterion("maintenance_sq_count <", value, "maintenanceSqCount");
            return (Criteria) this;
        }

        public Criteria andMaintenanceSqCountLessThanOrEqualTo(Integer value) {
            addCriterion("maintenance_sq_count <=", value, "maintenanceSqCount");
            return (Criteria) this;
        }

        public Criteria andMaintenanceSqCountIn(List<Integer> values) {
            addCriterion("maintenance_sq_count in", values, "maintenanceSqCount");
            return (Criteria) this;
        }

        public Criteria andMaintenanceSqCountNotIn(List<Integer> values) {
            addCriterion("maintenance_sq_count not in", values, "maintenanceSqCount");
            return (Criteria) this;
        }

        public Criteria andMaintenanceSqCountBetween(Integer value1, Integer value2) {
            addCriterion("maintenance_sq_count between", value1, value2, "maintenanceSqCount");
            return (Criteria) this;
        }

        public Criteria andMaintenanceSqCountNotBetween(Integer value1, Integer value2) {
            addCriterion("maintenance_sq_count not between", value1, value2, "maintenanceSqCount");
            return (Criteria) this;
        }

        public Criteria andMaintenanceCompanyCountIsNull() {
            addCriterion("maintenance_company_count is null");
            return (Criteria) this;
        }

        public Criteria andMaintenanceCompanyCountIsNotNull() {
            addCriterion("maintenance_company_count is not null");
            return (Criteria) this;
        }

        public Criteria andMaintenanceCompanyCountEqualTo(Integer value) {
            addCriterion("maintenance_company_count =", value, "maintenanceCompanyCount");
            return (Criteria) this;
        }

        public Criteria andMaintenanceCompanyCountNotEqualTo(Integer value) {
            addCriterion("maintenance_company_count <>", value, "maintenanceCompanyCount");
            return (Criteria) this;
        }

        public Criteria andMaintenanceCompanyCountGreaterThan(Integer value) {
            addCriterion("maintenance_company_count >", value, "maintenanceCompanyCount");
            return (Criteria) this;
        }

        public Criteria andMaintenanceCompanyCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("maintenance_company_count >=", value, "maintenanceCompanyCount");
            return (Criteria) this;
        }

        public Criteria andMaintenanceCompanyCountLessThan(Integer value) {
            addCriterion("maintenance_company_count <", value, "maintenanceCompanyCount");
            return (Criteria) this;
        }

        public Criteria andMaintenanceCompanyCountLessThanOrEqualTo(Integer value) {
            addCriterion("maintenance_company_count <=", value, "maintenanceCompanyCount");
            return (Criteria) this;
        }

        public Criteria andMaintenanceCompanyCountIn(List<Integer> values) {
            addCriterion("maintenance_company_count in", values, "maintenanceCompanyCount");
            return (Criteria) this;
        }

        public Criteria andMaintenanceCompanyCountNotIn(List<Integer> values) {
            addCriterion("maintenance_company_count not in", values, "maintenanceCompanyCount");
            return (Criteria) this;
        }

        public Criteria andMaintenanceCompanyCountBetween(Integer value1, Integer value2) {
            addCriterion("maintenance_company_count between", value1, value2, "maintenanceCompanyCount");
            return (Criteria) this;
        }

        public Criteria andMaintenanceCompanyCountNotBetween(Integer value1, Integer value2) {
            addCriterion("maintenance_company_count not between", value1, value2, "maintenanceCompanyCount");
            return (Criteria) this;
        }

        public Criteria andChargeOrderCountIsNull() {
            addCriterion("charge_order_count is null");
            return (Criteria) this;
        }

        public Criteria andChargeOrderCountIsNotNull() {
            addCriterion("charge_order_count is not null");
            return (Criteria) this;
        }

        public Criteria andChargeOrderCountEqualTo(Integer value) {
            addCriterion("charge_order_count =", value, "chargeOrderCount");
            return (Criteria) this;
        }

        public Criteria andChargeOrderCountNotEqualTo(Integer value) {
            addCriterion("charge_order_count <>", value, "chargeOrderCount");
            return (Criteria) this;
        }

        public Criteria andChargeOrderCountGreaterThan(Integer value) {
            addCriterion("charge_order_count >", value, "chargeOrderCount");
            return (Criteria) this;
        }

        public Criteria andChargeOrderCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("charge_order_count >=", value, "chargeOrderCount");
            return (Criteria) this;
        }

        public Criteria andChargeOrderCountLessThan(Integer value) {
            addCriterion("charge_order_count <", value, "chargeOrderCount");
            return (Criteria) this;
        }

        public Criteria andChargeOrderCountLessThanOrEqualTo(Integer value) {
            addCriterion("charge_order_count <=", value, "chargeOrderCount");
            return (Criteria) this;
        }

        public Criteria andChargeOrderCountIn(List<Integer> values) {
            addCriterion("charge_order_count in", values, "chargeOrderCount");
            return (Criteria) this;
        }

        public Criteria andChargeOrderCountNotIn(List<Integer> values) {
            addCriterion("charge_order_count not in", values, "chargeOrderCount");
            return (Criteria) this;
        }

        public Criteria andChargeOrderCountBetween(Integer value1, Integer value2) {
            addCriterion("charge_order_count between", value1, value2, "chargeOrderCount");
            return (Criteria) this;
        }

        public Criteria andChargeOrderCountNotBetween(Integer value1, Integer value2) {
            addCriterion("charge_order_count not between", value1, value2, "chargeOrderCount");
            return (Criteria) this;
        }

        public Criteria andViolationSqCountIsNull() {
            addCriterion("violation_sq_count is null");
            return (Criteria) this;
        }

        public Criteria andViolationSqCountIsNotNull() {
            addCriterion("violation_sq_count is not null");
            return (Criteria) this;
        }

        public Criteria andViolationSqCountEqualTo(Integer value) {
            addCriterion("violation_sq_count =", value, "violationSqCount");
            return (Criteria) this;
        }

        public Criteria andViolationSqCountNotEqualTo(Integer value) {
            addCriterion("violation_sq_count <>", value, "violationSqCount");
            return (Criteria) this;
        }

        public Criteria andViolationSqCountGreaterThan(Integer value) {
            addCriterion("violation_sq_count >", value, "violationSqCount");
            return (Criteria) this;
        }

        public Criteria andViolationSqCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("violation_sq_count >=", value, "violationSqCount");
            return (Criteria) this;
        }

        public Criteria andViolationSqCountLessThan(Integer value) {
            addCriterion("violation_sq_count <", value, "violationSqCount");
            return (Criteria) this;
        }

        public Criteria andViolationSqCountLessThanOrEqualTo(Integer value) {
            addCriterion("violation_sq_count <=", value, "violationSqCount");
            return (Criteria) this;
        }

        public Criteria andViolationSqCountIn(List<Integer> values) {
            addCriterion("violation_sq_count in", values, "violationSqCount");
            return (Criteria) this;
        }

        public Criteria andViolationSqCountNotIn(List<Integer> values) {
            addCriterion("violation_sq_count not in", values, "violationSqCount");
            return (Criteria) this;
        }

        public Criteria andViolationSqCountBetween(Integer value1, Integer value2) {
            addCriterion("violation_sq_count between", value1, value2, "violationSqCount");
            return (Criteria) this;
        }

        public Criteria andViolationSqCountNotBetween(Integer value1, Integer value2) {
            addCriterion("violation_sq_count not between", value1, value2, "violationSqCount");
            return (Criteria) this;
        }

        public Criteria andViolationCompanyCountIsNull() {
            addCriterion("violation_company_count is null");
            return (Criteria) this;
        }

        public Criteria andViolationCompanyCountIsNotNull() {
            addCriterion("violation_company_count is not null");
            return (Criteria) this;
        }

        public Criteria andViolationCompanyCountEqualTo(Integer value) {
            addCriterion("violation_company_count =", value, "violationCompanyCount");
            return (Criteria) this;
        }

        public Criteria andViolationCompanyCountNotEqualTo(Integer value) {
            addCriterion("violation_company_count <>", value, "violationCompanyCount");
            return (Criteria) this;
        }

        public Criteria andViolationCompanyCountGreaterThan(Integer value) {
            addCriterion("violation_company_count >", value, "violationCompanyCount");
            return (Criteria) this;
        }

        public Criteria andViolationCompanyCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("violation_company_count >=", value, "violationCompanyCount");
            return (Criteria) this;
        }

        public Criteria andViolationCompanyCountLessThan(Integer value) {
            addCriterion("violation_company_count <", value, "violationCompanyCount");
            return (Criteria) this;
        }

        public Criteria andViolationCompanyCountLessThanOrEqualTo(Integer value) {
            addCriterion("violation_company_count <=", value, "violationCompanyCount");
            return (Criteria) this;
        }

        public Criteria andViolationCompanyCountIn(List<Integer> values) {
            addCriterion("violation_company_count in", values, "violationCompanyCount");
            return (Criteria) this;
        }

        public Criteria andViolationCompanyCountNotIn(List<Integer> values) {
            addCriterion("violation_company_count not in", values, "violationCompanyCount");
            return (Criteria) this;
        }

        public Criteria andViolationCompanyCountBetween(Integer value1, Integer value2) {
            addCriterion("violation_company_count between", value1, value2, "violationCompanyCount");
            return (Criteria) this;
        }

        public Criteria andViolationCompanyCountNotBetween(Integer value1, Integer value2) {
            addCriterion("violation_company_count not between", value1, value2, "violationCompanyCount");
            return (Criteria) this;
        }

        public Criteria andBelongDeptIdIsNull() {
            addCriterion("belong_dept_id is null");
            return (Criteria) this;
        }

        public Criteria andBelongDeptIdIsNotNull() {
            addCriterion("belong_dept_id is not null");
            return (Criteria) this;
        }

        public Criteria andBelongDeptIdEqualTo(String value) {
            addCriterion("belong_dept_id =", value, "belongDeptId");
            return (Criteria) this;
        }

        public Criteria andBelongDeptIdNotEqualTo(String value) {
            addCriterion("belong_dept_id <>", value, "belongDeptId");
            return (Criteria) this;
        }

        public Criteria andBelongDeptIdGreaterThan(String value) {
            addCriterion("belong_dept_id >", value, "belongDeptId");
            return (Criteria) this;
        }

        public Criteria andBelongDeptIdGreaterThanOrEqualTo(String value) {
            addCriterion("belong_dept_id >=", value, "belongDeptId");
            return (Criteria) this;
        }

        public Criteria andBelongDeptIdLessThan(String value) {
            addCriterion("belong_dept_id <", value, "belongDeptId");
            return (Criteria) this;
        }

        public Criteria andBelongDeptIdLessThanOrEqualTo(String value) {
            addCriterion("belong_dept_id <=", value, "belongDeptId");
            return (Criteria) this;
        }

        public Criteria andBelongDeptIdLike(String value) {
            addCriterion("belong_dept_id like", value, "belongDeptId");
            return (Criteria) this;
        }

        public Criteria andBelongDeptIdNotLike(String value) {
            addCriterion("belong_dept_id not like", value, "belongDeptId");
            return (Criteria) this;
        }

        public Criteria andBelongDeptIdIn(List<String> values) {
            addCriterion("belong_dept_id in", values, "belongDeptId");
            return (Criteria) this;
        }

        public Criteria andBelongDeptIdNotIn(List<String> values) {
            addCriterion("belong_dept_id not in", values, "belongDeptId");
            return (Criteria) this;
        }

        public Criteria andBelongDeptIdBetween(String value1, String value2) {
            addCriterion("belong_dept_id between", value1, value2, "belongDeptId");
            return (Criteria) this;
        }

        public Criteria andBelongDeptIdNotBetween(String value1, String value2) {
            addCriterion("belong_dept_id not between", value1, value2, "belongDeptId");
            return (Criteria) this;
        }

        public Criteria andBelongDeptNameIsNull() {
            addCriterion("belong_dept_name is null");
            return (Criteria) this;
        }

        public Criteria andBelongDeptNameIsNotNull() {
            addCriterion("belong_dept_name is not null");
            return (Criteria) this;
        }

        public Criteria andBelongDeptNameEqualTo(String value) {
            addCriterion("belong_dept_name =", value, "belongDeptName");
            return (Criteria) this;
        }

        public Criteria andBelongDeptNameNotEqualTo(String value) {
            addCriterion("belong_dept_name <>", value, "belongDeptName");
            return (Criteria) this;
        }

        public Criteria andBelongDeptNameGreaterThan(String value) {
            addCriterion("belong_dept_name >", value, "belongDeptName");
            return (Criteria) this;
        }

        public Criteria andBelongDeptNameGreaterThanOrEqualTo(String value) {
            addCriterion("belong_dept_name >=", value, "belongDeptName");
            return (Criteria) this;
        }

        public Criteria andBelongDeptNameLessThan(String value) {
            addCriterion("belong_dept_name <", value, "belongDeptName");
            return (Criteria) this;
        }

        public Criteria andBelongDeptNameLessThanOrEqualTo(String value) {
            addCriterion("belong_dept_name <=", value, "belongDeptName");
            return (Criteria) this;
        }

        public Criteria andBelongDeptNameLike(String value) {
            addCriterion("belong_dept_name like", value, "belongDeptName");
            return (Criteria) this;
        }

        public Criteria andBelongDeptNameNotLike(String value) {
            addCriterion("belong_dept_name not like", value, "belongDeptName");
            return (Criteria) this;
        }

        public Criteria andBelongDeptNameIn(List<String> values) {
            addCriterion("belong_dept_name in", values, "belongDeptName");
            return (Criteria) this;
        }

        public Criteria andBelongDeptNameNotIn(List<String> values) {
            addCriterion("belong_dept_name not in", values, "belongDeptName");
            return (Criteria) this;
        }

        public Criteria andBelongDeptNameBetween(String value1, String value2) {
            addCriterion("belong_dept_name between", value1, value2, "belongDeptName");
            return (Criteria) this;
        }

        public Criteria andBelongDeptNameNotBetween(String value1, String value2) {
            addCriterion("belong_dept_name not between", value1, value2, "belongDeptName");
            return (Criteria) this;
        }

        public Criteria andPublicIdIsNull() {
            addCriterion("public_id is null");
            return (Criteria) this;
        }

        public Criteria andPublicIdIsNotNull() {
            addCriterion("public_id is not null");
            return (Criteria) this;
        }

        public Criteria andPublicIdEqualTo(String value) {
            addCriterion("public_id =", value, "publicId");
            return (Criteria) this;
        }

        public Criteria andPublicIdNotEqualTo(String value) {
            addCriterion("public_id <>", value, "publicId");
            return (Criteria) this;
        }

        public Criteria andPublicIdGreaterThan(String value) {
            addCriterion("public_id >", value, "publicId");
            return (Criteria) this;
        }

        public Criteria andPublicIdGreaterThanOrEqualTo(String value) {
            addCriterion("public_id >=", value, "publicId");
            return (Criteria) this;
        }

        public Criteria andPublicIdLessThan(String value) {
            addCriterion("public_id <", value, "publicId");
            return (Criteria) this;
        }

        public Criteria andPublicIdLessThanOrEqualTo(String value) {
            addCriterion("public_id <=", value, "publicId");
            return (Criteria) this;
        }

        public Criteria andPublicIdLike(String value) {
            addCriterion("public_id like", value, "publicId");
            return (Criteria) this;
        }

        public Criteria andPublicIdNotLike(String value) {
            addCriterion("public_id not like", value, "publicId");
            return (Criteria) this;
        }

        public Criteria andPublicIdIn(List<String> values) {
            addCriterion("public_id in", values, "publicId");
            return (Criteria) this;
        }

        public Criteria andPublicIdNotIn(List<String> values) {
            addCriterion("public_id not in", values, "publicId");
            return (Criteria) this;
        }

        public Criteria andPublicIdBetween(String value1, String value2) {
            addCriterion("public_id between", value1, value2, "publicId");
            return (Criteria) this;
        }

        public Criteria andPublicIdNotBetween(String value1, String value2) {
            addCriterion("public_id not between", value1, value2, "publicId");
            return (Criteria) this;
        }

        public Criteria andPublicPondNameIsNull() {
            addCriterion("public_pond_name is null");
            return (Criteria) this;
        }

        public Criteria andPublicPondNameIsNotNull() {
            addCriterion("public_pond_name is not null");
            return (Criteria) this;
        }

        public Criteria andPublicPondNameEqualTo(String value) {
            addCriterion("public_pond_name =", value, "publicPondName");
            return (Criteria) this;
        }

        public Criteria andPublicPondNameNotEqualTo(String value) {
            addCriterion("public_pond_name <>", value, "publicPondName");
            return (Criteria) this;
        }

        public Criteria andPublicPondNameGreaterThan(String value) {
            addCriterion("public_pond_name >", value, "publicPondName");
            return (Criteria) this;
        }

        public Criteria andPublicPondNameGreaterThanOrEqualTo(String value) {
            addCriterion("public_pond_name >=", value, "publicPondName");
            return (Criteria) this;
        }

        public Criteria andPublicPondNameLessThan(String value) {
            addCriterion("public_pond_name <", value, "publicPondName");
            return (Criteria) this;
        }

        public Criteria andPublicPondNameLessThanOrEqualTo(String value) {
            addCriterion("public_pond_name <=", value, "publicPondName");
            return (Criteria) this;
        }

        public Criteria andPublicPondNameLike(String value) {
            addCriterion("public_pond_name like", value, "publicPondName");
            return (Criteria) this;
        }

        public Criteria andPublicPondNameNotLike(String value) {
            addCriterion("public_pond_name not like", value, "publicPondName");
            return (Criteria) this;
        }

        public Criteria andPublicPondNameIn(List<String> values) {
            addCriterion("public_pond_name in", values, "publicPondName");
            return (Criteria) this;
        }

        public Criteria andPublicPondNameNotIn(List<String> values) {
            addCriterion("public_pond_name not in", values, "publicPondName");
            return (Criteria) this;
        }

        public Criteria andPublicPondNameBetween(String value1, String value2) {
            addCriterion("public_pond_name between", value1, value2, "publicPondName");
            return (Criteria) this;
        }

        public Criteria andPublicPondNameNotBetween(String value1, String value2) {
            addCriterion("public_pond_name not between", value1, value2, "publicPondName");
            return (Criteria) this;
        }

        public Criteria andCompanyAttributeIsNull() {
            addCriterion("company_attribute is null");
            return (Criteria) this;
        }

        public Criteria andCompanyAttributeIsNotNull() {
            addCriterion("company_attribute is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyAttributeEqualTo(Integer value) {
            addCriterion("company_attribute =", value, "companyAttribute");
            return (Criteria) this;
        }

        public Criteria andCompanyAttributeNotEqualTo(Integer value) {
            addCriterion("company_attribute <>", value, "companyAttribute");
            return (Criteria) this;
        }

        public Criteria andCompanyAttributeGreaterThan(Integer value) {
            addCriterion("company_attribute >", value, "companyAttribute");
            return (Criteria) this;
        }

        public Criteria andCompanyAttributeGreaterThanOrEqualTo(Integer value) {
            addCriterion("company_attribute >=", value, "companyAttribute");
            return (Criteria) this;
        }

        public Criteria andCompanyAttributeLessThan(Integer value) {
            addCriterion("company_attribute <", value, "companyAttribute");
            return (Criteria) this;
        }

        public Criteria andCompanyAttributeLessThanOrEqualTo(Integer value) {
            addCriterion("company_attribute <=", value, "companyAttribute");
            return (Criteria) this;
        }

        public Criteria andCompanyAttributeIn(List<Integer> values) {
            addCriterion("company_attribute in", values, "companyAttribute");
            return (Criteria) this;
        }

        public Criteria andCompanyAttributeNotIn(List<Integer> values) {
            addCriterion("company_attribute not in", values, "companyAttribute");
            return (Criteria) this;
        }

        public Criteria andCompanyAttributeBetween(Integer value1, Integer value2) {
            addCriterion("company_attribute between", value1, value2, "companyAttribute");
            return (Criteria) this;
        }

        public Criteria andCompanyAttributeNotBetween(Integer value1, Integer value2) {
            addCriterion("company_attribute not between", value1, value2, "companyAttribute");
            return (Criteria) this;
        }

        public Criteria andAccountOpenTimeIsNull() {
            addCriterion("account_open_time is null");
            return (Criteria) this;
        }

        public Criteria andAccountOpenTimeIsNotNull() {
            addCriterion("account_open_time is not null");
            return (Criteria) this;
        }

        public Criteria andAccountOpenTimeEqualTo(Date value) {
            addCriterion("account_open_time =", value, "accountOpenTime");
            return (Criteria) this;
        }

        public Criteria andAccountOpenTimeNotEqualTo(Date value) {
            addCriterion("account_open_time <>", value, "accountOpenTime");
            return (Criteria) this;
        }

        public Criteria andAccountOpenTimeGreaterThan(Date value) {
            addCriterion("account_open_time >", value, "accountOpenTime");
            return (Criteria) this;
        }

        public Criteria andAccountOpenTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("account_open_time >=", value, "accountOpenTime");
            return (Criteria) this;
        }

        public Criteria andAccountOpenTimeLessThan(Date value) {
            addCriterion("account_open_time <", value, "accountOpenTime");
            return (Criteria) this;
        }

        public Criteria andAccountOpenTimeLessThanOrEqualTo(Date value) {
            addCriterion("account_open_time <=", value, "accountOpenTime");
            return (Criteria) this;
        }

        public Criteria andAccountOpenTimeIn(List<Date> values) {
            addCriterion("account_open_time in", values, "accountOpenTime");
            return (Criteria) this;
        }

        public Criteria andAccountOpenTimeNotIn(List<Date> values) {
            addCriterion("account_open_time not in", values, "accountOpenTime");
            return (Criteria) this;
        }

        public Criteria andAccountOpenTimeBetween(Date value1, Date value2) {
            addCriterion("account_open_time between", value1, value2, "accountOpenTime");
            return (Criteria) this;
        }

        public Criteria andAccountOpenTimeNotBetween(Date value1, Date value2) {
            addCriterion("account_open_time not between", value1, value2, "accountOpenTime");
            return (Criteria) this;
        }

        public Criteria andDeviceWirelessCountIsNull() {
            addCriterion("device_wireless_count is null");
            return (Criteria) this;
        }

        public Criteria andDeviceWirelessCountIsNotNull() {
            addCriterion("device_wireless_count is not null");
            return (Criteria) this;
        }

        public Criteria andDeviceWirelessCountEqualTo(Integer value) {
            addCriterion("device_wireless_count =", value, "deviceWirelessCount");
            return (Criteria) this;
        }

        public Criteria andDeviceWirelessCountNotEqualTo(Integer value) {
            addCriterion("device_wireless_count <>", value, "deviceWirelessCount");
            return (Criteria) this;
        }

        public Criteria andDeviceWirelessCountGreaterThan(Integer value) {
            addCriterion("device_wireless_count >", value, "deviceWirelessCount");
            return (Criteria) this;
        }

        public Criteria andDeviceWirelessCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("device_wireless_count >=", value, "deviceWirelessCount");
            return (Criteria) this;
        }

        public Criteria andDeviceWirelessCountLessThan(Integer value) {
            addCriterion("device_wireless_count <", value, "deviceWirelessCount");
            return (Criteria) this;
        }

        public Criteria andDeviceWirelessCountLessThanOrEqualTo(Integer value) {
            addCriterion("device_wireless_count <=", value, "deviceWirelessCount");
            return (Criteria) this;
        }

        public Criteria andDeviceWirelessCountIn(List<Integer> values) {
            addCriterion("device_wireless_count in", values, "deviceWirelessCount");
            return (Criteria) this;
        }

        public Criteria andDeviceWirelessCountNotIn(List<Integer> values) {
            addCriterion("device_wireless_count not in", values, "deviceWirelessCount");
            return (Criteria) this;
        }

        public Criteria andDeviceWirelessCountBetween(Integer value1, Integer value2) {
            addCriterion("device_wireless_count between", value1, value2, "deviceWirelessCount");
            return (Criteria) this;
        }

        public Criteria andDeviceWirelessCountNotBetween(Integer value1, Integer value2) {
            addCriterion("device_wireless_count not between", value1, value2, "deviceWirelessCount");
            return (Criteria) this;
        }

        public Criteria andDeviceWiredCountIsNull() {
            addCriterion("device_wired_count is null");
            return (Criteria) this;
        }

        public Criteria andDeviceWiredCountIsNotNull() {
            addCriterion("device_wired_count is not null");
            return (Criteria) this;
        }

        public Criteria andDeviceWiredCountEqualTo(Integer value) {
            addCriterion("device_wired_count =", value, "deviceWiredCount");
            return (Criteria) this;
        }

        public Criteria andDeviceWiredCountNotEqualTo(Integer value) {
            addCriterion("device_wired_count <>", value, "deviceWiredCount");
            return (Criteria) this;
        }

        public Criteria andDeviceWiredCountGreaterThan(Integer value) {
            addCriterion("device_wired_count >", value, "deviceWiredCount");
            return (Criteria) this;
        }

        public Criteria andDeviceWiredCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("device_wired_count >=", value, "deviceWiredCount");
            return (Criteria) this;
        }

        public Criteria andDeviceWiredCountLessThan(Integer value) {
            addCriterion("device_wired_count <", value, "deviceWiredCount");
            return (Criteria) this;
        }

        public Criteria andDeviceWiredCountLessThanOrEqualTo(Integer value) {
            addCriterion("device_wired_count <=", value, "deviceWiredCount");
            return (Criteria) this;
        }

        public Criteria andDeviceWiredCountIn(List<Integer> values) {
            addCriterion("device_wired_count in", values, "deviceWiredCount");
            return (Criteria) this;
        }

        public Criteria andDeviceWiredCountNotIn(List<Integer> values) {
            addCriterion("device_wired_count not in", values, "deviceWiredCount");
            return (Criteria) this;
        }

        public Criteria andDeviceWiredCountBetween(Integer value1, Integer value2) {
            addCriterion("device_wired_count between", value1, value2, "deviceWiredCount");
            return (Criteria) this;
        }

        public Criteria andDeviceWiredCountNotBetween(Integer value1, Integer value2) {
            addCriterion("device_wired_count not between", value1, value2, "deviceWiredCount");
            return (Criteria) this;
        }

        public Criteria andDeviceObdCountIsNull() {
            addCriterion("device_obd_count is null");
            return (Criteria) this;
        }

        public Criteria andDeviceObdCountIsNotNull() {
            addCriterion("device_obd_count is not null");
            return (Criteria) this;
        }

        public Criteria andDeviceObdCountEqualTo(Integer value) {
            addCriterion("device_obd_count =", value, "deviceObdCount");
            return (Criteria) this;
        }

        public Criteria andDeviceObdCountNotEqualTo(Integer value) {
            addCriterion("device_obd_count <>", value, "deviceObdCount");
            return (Criteria) this;
        }

        public Criteria andDeviceObdCountGreaterThan(Integer value) {
            addCriterion("device_obd_count >", value, "deviceObdCount");
            return (Criteria) this;
        }

        public Criteria andDeviceObdCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("device_obd_count >=", value, "deviceObdCount");
            return (Criteria) this;
        }

        public Criteria andDeviceObdCountLessThan(Integer value) {
            addCriterion("device_obd_count <", value, "deviceObdCount");
            return (Criteria) this;
        }

        public Criteria andDeviceObdCountLessThanOrEqualTo(Integer value) {
            addCriterion("device_obd_count <=", value, "deviceObdCount");
            return (Criteria) this;
        }

        public Criteria andDeviceObdCountIn(List<Integer> values) {
            addCriterion("device_obd_count in", values, "deviceObdCount");
            return (Criteria) this;
        }

        public Criteria andDeviceObdCountNotIn(List<Integer> values) {
            addCriterion("device_obd_count not in", values, "deviceObdCount");
            return (Criteria) this;
        }

        public Criteria andDeviceObdCountBetween(Integer value1, Integer value2) {
            addCriterion("device_obd_count between", value1, value2, "deviceObdCount");
            return (Criteria) this;
        }

        public Criteria andDeviceObdCountNotBetween(Integer value1, Integer value2) {
            addCriterion("device_obd_count not between", value1, value2, "deviceObdCount");
            return (Criteria) this;
        }

        public Criteria andDeviceVideoCountIsNull() {
            addCriterion("device_video_count is null");
            return (Criteria) this;
        }

        public Criteria andDeviceVideoCountIsNotNull() {
            addCriterion("device_video_count is not null");
            return (Criteria) this;
        }

        public Criteria andDeviceVideoCountEqualTo(Integer value) {
            addCriterion("device_video_count =", value, "deviceVideoCount");
            return (Criteria) this;
        }

        public Criteria andDeviceVideoCountNotEqualTo(Integer value) {
            addCriterion("device_video_count <>", value, "deviceVideoCount");
            return (Criteria) this;
        }

        public Criteria andDeviceVideoCountGreaterThan(Integer value) {
            addCriterion("device_video_count >", value, "deviceVideoCount");
            return (Criteria) this;
        }

        public Criteria andDeviceVideoCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("device_video_count >=", value, "deviceVideoCount");
            return (Criteria) this;
        }

        public Criteria andDeviceVideoCountLessThan(Integer value) {
            addCriterion("device_video_count <", value, "deviceVideoCount");
            return (Criteria) this;
        }

        public Criteria andDeviceVideoCountLessThanOrEqualTo(Integer value) {
            addCriterion("device_video_count <=", value, "deviceVideoCount");
            return (Criteria) this;
        }

        public Criteria andDeviceVideoCountIn(List<Integer> values) {
            addCriterion("device_video_count in", values, "deviceVideoCount");
            return (Criteria) this;
        }

        public Criteria andDeviceVideoCountNotIn(List<Integer> values) {
            addCriterion("device_video_count not in", values, "deviceVideoCount");
            return (Criteria) this;
        }

        public Criteria andDeviceVideoCountBetween(Integer value1, Integer value2) {
            addCriterion("device_video_count between", value1, value2, "deviceVideoCount");
            return (Criteria) this;
        }

        public Criteria andDeviceVideoCountNotBetween(Integer value1, Integer value2) {
            addCriterion("device_video_count not between", value1, value2, "deviceVideoCount");
            return (Criteria) this;
        }

        public Criteria andContractCountIsNull() {
            addCriterion("contract_count is null");
            return (Criteria) this;
        }

        public Criteria andContractCountIsNotNull() {
            addCriterion("contract_count is not null");
            return (Criteria) this;
        }

        public Criteria andContractCountEqualTo(Integer value) {
            addCriterion("contract_count =", value, "contractCount");
            return (Criteria) this;
        }

        public Criteria andContractCountNotEqualTo(Integer value) {
            addCriterion("contract_count <>", value, "contractCount");
            return (Criteria) this;
        }

        public Criteria andContractCountGreaterThan(Integer value) {
            addCriterion("contract_count >", value, "contractCount");
            return (Criteria) this;
        }

        public Criteria andContractCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("contract_count >=", value, "contractCount");
            return (Criteria) this;
        }

        public Criteria andContractCountLessThan(Integer value) {
            addCriterion("contract_count <", value, "contractCount");
            return (Criteria) this;
        }

        public Criteria andContractCountLessThanOrEqualTo(Integer value) {
            addCriterion("contract_count <=", value, "contractCount");
            return (Criteria) this;
        }

        public Criteria andContractCountIn(List<Integer> values) {
            addCriterion("contract_count in", values, "contractCount");
            return (Criteria) this;
        }

        public Criteria andContractCountNotIn(List<Integer> values) {
            addCriterion("contract_count not in", values, "contractCount");
            return (Criteria) this;
        }

        public Criteria andContractCountBetween(Integer value1, Integer value2) {
            addCriterion("contract_count between", value1, value2, "contractCount");
            return (Criteria) this;
        }

        public Criteria andContractCountNotBetween(Integer value1, Integer value2) {
            addCriterion("contract_count not between", value1, value2, "contractCount");
            return (Criteria) this;
        }

        public Criteria andContractAmountIsNull() {
            addCriterion("contract_amount is null");
            return (Criteria) this;
        }

        public Criteria andContractAmountIsNotNull() {
            addCriterion("contract_amount is not null");
            return (Criteria) this;
        }

        public Criteria andContractAmountEqualTo(BigDecimal value) {
            addCriterion("contract_amount =", value, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andContractAmountNotEqualTo(BigDecimal value) {
            addCriterion("contract_amount <>", value, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andContractAmountGreaterThan(BigDecimal value) {
            addCriterion("contract_amount >", value, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andContractAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("contract_amount >=", value, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andContractAmountLessThan(BigDecimal value) {
            addCriterion("contract_amount <", value, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andContractAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("contract_amount <=", value, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andContractAmountIn(List<BigDecimal> values) {
            addCriterion("contract_amount in", values, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andContractAmountNotIn(List<BigDecimal> values) {
            addCriterion("contract_amount not in", values, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andContractAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("contract_amount between", value1, value2, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andContractAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("contract_amount not between", value1, value2, "contractAmount");
            return (Criteria) this;
        }

        public Criteria andSystemContractCountIsNull() {
            addCriterion("system_contract_count is null");
            return (Criteria) this;
        }

        public Criteria andSystemContractCountIsNotNull() {
            addCriterion("system_contract_count is not null");
            return (Criteria) this;
        }

        public Criteria andSystemContractCountEqualTo(Integer value) {
            addCriterion("system_contract_count =", value, "systemContractCount");
            return (Criteria) this;
        }

        public Criteria andSystemContractCountNotEqualTo(Integer value) {
            addCriterion("system_contract_count <>", value, "systemContractCount");
            return (Criteria) this;
        }

        public Criteria andSystemContractCountGreaterThan(Integer value) {
            addCriterion("system_contract_count >", value, "systemContractCount");
            return (Criteria) this;
        }

        public Criteria andSystemContractCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("system_contract_count >=", value, "systemContractCount");
            return (Criteria) this;
        }

        public Criteria andSystemContractCountLessThan(Integer value) {
            addCriterion("system_contract_count <", value, "systemContractCount");
            return (Criteria) this;
        }

        public Criteria andSystemContractCountLessThanOrEqualTo(Integer value) {
            addCriterion("system_contract_count <=", value, "systemContractCount");
            return (Criteria) this;
        }

        public Criteria andSystemContractCountIn(List<Integer> values) {
            addCriterion("system_contract_count in", values, "systemContractCount");
            return (Criteria) this;
        }

        public Criteria andSystemContractCountNotIn(List<Integer> values) {
            addCriterion("system_contract_count not in", values, "systemContractCount");
            return (Criteria) this;
        }

        public Criteria andSystemContractCountBetween(Integer value1, Integer value2) {
            addCriterion("system_contract_count between", value1, value2, "systemContractCount");
            return (Criteria) this;
        }

        public Criteria andSystemContractCountNotBetween(Integer value1, Integer value2) {
            addCriterion("system_contract_count not between", value1, value2, "systemContractCount");
            return (Criteria) this;
        }

        public Criteria andSystemContractAmountIsNull() {
            addCriterion("system_contract_amount is null");
            return (Criteria) this;
        }

        public Criteria andSystemContractAmountIsNotNull() {
            addCriterion("system_contract_amount is not null");
            return (Criteria) this;
        }

        public Criteria andSystemContractAmountEqualTo(BigDecimal value) {
            addCriterion("system_contract_amount =", value, "systemContractAmount");
            return (Criteria) this;
        }

        public Criteria andSystemContractAmountNotEqualTo(BigDecimal value) {
            addCriterion("system_contract_amount <>", value, "systemContractAmount");
            return (Criteria) this;
        }

        public Criteria andSystemContractAmountGreaterThan(BigDecimal value) {
            addCriterion("system_contract_amount >", value, "systemContractAmount");
            return (Criteria) this;
        }

        public Criteria andSystemContractAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("system_contract_amount >=", value, "systemContractAmount");
            return (Criteria) this;
        }

        public Criteria andSystemContractAmountLessThan(BigDecimal value) {
            addCriterion("system_contract_amount <", value, "systemContractAmount");
            return (Criteria) this;
        }

        public Criteria andSystemContractAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("system_contract_amount <=", value, "systemContractAmount");
            return (Criteria) this;
        }

        public Criteria andSystemContractAmountIn(List<BigDecimal> values) {
            addCriterion("system_contract_amount in", values, "systemContractAmount");
            return (Criteria) this;
        }

        public Criteria andSystemContractAmountNotIn(List<BigDecimal> values) {
            addCriterion("system_contract_amount not in", values, "systemContractAmount");
            return (Criteria) this;
        }

        public Criteria andSystemContractAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("system_contract_amount between", value1, value2, "systemContractAmount");
            return (Criteria) this;
        }

        public Criteria andSystemContractAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("system_contract_amount not between", value1, value2, "systemContractAmount");
            return (Criteria) this;
        }

        public Criteria andLoginDayIsNull() {
            addCriterion("login_day is null");
            return (Criteria) this;
        }

        public Criteria andLoginDayIsNotNull() {
            addCriterion("login_day is not null");
            return (Criteria) this;
        }

        public Criteria andLoginDayEqualTo(Integer value) {
            addCriterion("login_day =", value, "loginDay");
            return (Criteria) this;
        }

        public Criteria andLoginDayNotEqualTo(Integer value) {
            addCriterion("login_day <>", value, "loginDay");
            return (Criteria) this;
        }

        public Criteria andLoginDayGreaterThan(Integer value) {
            addCriterion("login_day >", value, "loginDay");
            return (Criteria) this;
        }

        public Criteria andLoginDayGreaterThanOrEqualTo(Integer value) {
            addCriterion("login_day >=", value, "loginDay");
            return (Criteria) this;
        }

        public Criteria andLoginDayLessThan(Integer value) {
            addCriterion("login_day <", value, "loginDay");
            return (Criteria) this;
        }

        public Criteria andLoginDayLessThanOrEqualTo(Integer value) {
            addCriterion("login_day <=", value, "loginDay");
            return (Criteria) this;
        }

        public Criteria andLoginDayIn(List<Integer> values) {
            addCriterion("login_day in", values, "loginDay");
            return (Criteria) this;
        }

        public Criteria andLoginDayNotIn(List<Integer> values) {
            addCriterion("login_day not in", values, "loginDay");
            return (Criteria) this;
        }

        public Criteria andLoginDayBetween(Integer value1, Integer value2) {
            addCriterion("login_day between", value1, value2, "loginDay");
            return (Criteria) this;
        }

        public Criteria andLoginDayNotBetween(Integer value1, Integer value2) {
            addCriterion("login_day not between", value1, value2, "loginDay");
            return (Criteria) this;
        }

        public Criteria andLoginStaffCountIsNull() {
            addCriterion("login_staff_count is null");
            return (Criteria) this;
        }

        public Criteria andLoginStaffCountIsNotNull() {
            addCriterion("login_staff_count is not null");
            return (Criteria) this;
        }

        public Criteria andLoginStaffCountEqualTo(Integer value) {
            addCriterion("login_staff_count =", value, "loginStaffCount");
            return (Criteria) this;
        }

        public Criteria andLoginStaffCountNotEqualTo(Integer value) {
            addCriterion("login_staff_count <>", value, "loginStaffCount");
            return (Criteria) this;
        }

        public Criteria andLoginStaffCountGreaterThan(Integer value) {
            addCriterion("login_staff_count >", value, "loginStaffCount");
            return (Criteria) this;
        }

        public Criteria andLoginStaffCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("login_staff_count >=", value, "loginStaffCount");
            return (Criteria) this;
        }

        public Criteria andLoginStaffCountLessThan(Integer value) {
            addCriterion("login_staff_count <", value, "loginStaffCount");
            return (Criteria) this;
        }

        public Criteria andLoginStaffCountLessThanOrEqualTo(Integer value) {
            addCriterion("login_staff_count <=", value, "loginStaffCount");
            return (Criteria) this;
        }

        public Criteria andLoginStaffCountIn(List<Integer> values) {
            addCriterion("login_staff_count in", values, "loginStaffCount");
            return (Criteria) this;
        }

        public Criteria andLoginStaffCountNotIn(List<Integer> values) {
            addCriterion("login_staff_count not in", values, "loginStaffCount");
            return (Criteria) this;
        }

        public Criteria andLoginStaffCountBetween(Integer value1, Integer value2) {
            addCriterion("login_staff_count between", value1, value2, "loginStaffCount");
            return (Criteria) this;
        }

        public Criteria andLoginStaffCountNotBetween(Integer value1, Integer value2) {
            addCriterion("login_staff_count not between", value1, value2, "loginStaffCount");
            return (Criteria) this;
        }

        public Criteria andOpenAccountMethodIsNull() {
            addCriterion("open_account_method is null");
            return (Criteria) this;
        }

        public Criteria andOpenAccountMethodIsNotNull() {
            addCriterion("open_account_method is not null");
            return (Criteria) this;
        }

        public Criteria andOpenAccountMethodEqualTo(Byte value) {
            addCriterion("open_account_method =", value, "openAccountMethod");
            return (Criteria) this;
        }

        public Criteria andOpenAccountMethodNotEqualTo(Byte value) {
            addCriterion("open_account_method <>", value, "openAccountMethod");
            return (Criteria) this;
        }

        public Criteria andOpenAccountMethodGreaterThan(Byte value) {
            addCriterion("open_account_method >", value, "openAccountMethod");
            return (Criteria) this;
        }

        public Criteria andOpenAccountMethodGreaterThanOrEqualTo(Byte value) {
            addCriterion("open_account_method >=", value, "openAccountMethod");
            return (Criteria) this;
        }

        public Criteria andOpenAccountMethodLessThan(Byte value) {
            addCriterion("open_account_method <", value, "openAccountMethod");
            return (Criteria) this;
        }

        public Criteria andOpenAccountMethodLessThanOrEqualTo(Byte value) {
            addCriterion("open_account_method <=", value, "openAccountMethod");
            return (Criteria) this;
        }

        public Criteria andOpenAccountMethodIn(List<Byte> values) {
            addCriterion("open_account_method in", values, "openAccountMethod");
            return (Criteria) this;
        }

        public Criteria andOpenAccountMethodNotIn(List<Byte> values) {
            addCriterion("open_account_method not in", values, "openAccountMethod");
            return (Criteria) this;
        }

        public Criteria andOpenAccountMethodBetween(Byte value1, Byte value2) {
            addCriterion("open_account_method between", value1, value2, "openAccountMethod");
            return (Criteria) this;
        }

        public Criteria andOpenAccountMethodNotBetween(Byte value1, Byte value2) {
            addCriterion("open_account_method not between", value1, value2, "openAccountMethod");
            return (Criteria) this;
        }

        public Criteria andActiveStatusIsNull() {
            addCriterion("active_status is null");
            return (Criteria) this;
        }

        public Criteria andActiveStatusIsNotNull() {
            addCriterion("active_status is not null");
            return (Criteria) this;
        }

        public Criteria andActiveStatusEqualTo(Integer value) {
            addCriterion("active_status =", value, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andActiveStatusNotEqualTo(Integer value) {
            addCriterion("active_status <>", value, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andActiveStatusGreaterThan(Integer value) {
            addCriterion("active_status >", value, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andActiveStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("active_status >=", value, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andActiveStatusLessThan(Integer value) {
            addCriterion("active_status <", value, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andActiveStatusLessThanOrEqualTo(Integer value) {
            addCriterion("active_status <=", value, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andActiveStatusIn(List<Integer> values) {
            addCriterion("active_status in", values, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andActiveStatusNotIn(List<Integer> values) {
            addCriterion("active_status not in", values, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andActiveStatusBetween(Integer value1, Integer value2) {
            addCriterion("active_status between", value1, value2, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andActiveStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("active_status not between", value1, value2, "activeStatus");
            return (Criteria) this;
        }

        public Criteria andCompanyCreateTimeIsNull() {
            addCriterion("company_create_time is null");
            return (Criteria) this;
        }

        public Criteria andCompanyCreateTimeIsNotNull() {
            addCriterion("company_create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyCreateTimeEqualTo(Date value) {
            addCriterion("company_create_time =", value, "companyCreateTime");
            return (Criteria) this;
        }

        public Criteria andCompanyCreateTimeNotEqualTo(Date value) {
            addCriterion("company_create_time <>", value, "companyCreateTime");
            return (Criteria) this;
        }

        public Criteria andCompanyCreateTimeGreaterThan(Date value) {
            addCriterion("company_create_time >", value, "companyCreateTime");
            return (Criteria) this;
        }

        public Criteria andCompanyCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("company_create_time >=", value, "companyCreateTime");
            return (Criteria) this;
        }

        public Criteria andCompanyCreateTimeLessThan(Date value) {
            addCriterion("company_create_time <", value, "companyCreateTime");
            return (Criteria) this;
        }

        public Criteria andCompanyCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("company_create_time <=", value, "companyCreateTime");
            return (Criteria) this;
        }

        public Criteria andCompanyCreateTimeIn(List<Date> values) {
            addCriterion("company_create_time in", values, "companyCreateTime");
            return (Criteria) this;
        }

        public Criteria andCompanyCreateTimeNotIn(List<Date> values) {
            addCriterion("company_create_time not in", values, "companyCreateTime");
            return (Criteria) this;
        }

        public Criteria andCompanyCreateTimeBetween(Date value1, Date value2) {
            addCriterion("company_create_time between", value1, value2, "companyCreateTime");
            return (Criteria) this;
        }

        public Criteria andCompanyCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("company_create_time not between", value1, value2, "companyCreateTime");
            return (Criteria) this;
        }

        public Criteria andGovPublicOrderCountIsNull() {
            addCriterion("gov_public_order_count is null");
            return (Criteria) this;
        }

        public Criteria andGovPublicOrderCountIsNotNull() {
            addCriterion("gov_public_order_count is not null");
            return (Criteria) this;
        }

        public Criteria andGovPublicOrderCountEqualTo(Integer value) {
            addCriterion("gov_public_order_count =", value, "govPublicOrderCount");
            return (Criteria) this;
        }

        public Criteria andGovPublicOrderCountNotEqualTo(Integer value) {
            addCriterion("gov_public_order_count <>", value, "govPublicOrderCount");
            return (Criteria) this;
        }

        public Criteria andGovPublicOrderCountGreaterThan(Integer value) {
            addCriterion("gov_public_order_count >", value, "govPublicOrderCount");
            return (Criteria) this;
        }

        public Criteria andGovPublicOrderCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("gov_public_order_count >=", value, "govPublicOrderCount");
            return (Criteria) this;
        }

        public Criteria andGovPublicOrderCountLessThan(Integer value) {
            addCriterion("gov_public_order_count <", value, "govPublicOrderCount");
            return (Criteria) this;
        }

        public Criteria andGovPublicOrderCountLessThanOrEqualTo(Integer value) {
            addCriterion("gov_public_order_count <=", value, "govPublicOrderCount");
            return (Criteria) this;
        }

        public Criteria andGovPublicOrderCountIn(List<Integer> values) {
            addCriterion("gov_public_order_count in", values, "govPublicOrderCount");
            return (Criteria) this;
        }

        public Criteria andGovPublicOrderCountNotIn(List<Integer> values) {
            addCriterion("gov_public_order_count not in", values, "govPublicOrderCount");
            return (Criteria) this;
        }

        public Criteria andGovPublicOrderCountBetween(Integer value1, Integer value2) {
            addCriterion("gov_public_order_count between", value1, value2, "govPublicOrderCount");
            return (Criteria) this;
        }

        public Criteria andGovPublicOrderCountNotBetween(Integer value1, Integer value2) {
            addCriterion("gov_public_order_count not between", value1, value2, "govPublicOrderCount");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}