package com.izu.business.entity;

import java.math.BigDecimal;
import java.util.Date;

/**
 *  （表名：mrcar_billing_info）
 * <br>
 * <br>
 * 【重要提示】：<br>
 * &nbsp;&nbsp;此类通过 Mybatis Generator 逆向生成，禁止手动修改！<br>（因项目需求持续性会发生变更，当调整数据表字段时，需要再重新逆向生成此类）
 **/
public class MrCarBillingInfo {
    /** 主键id **/
    private Integer id;

    /** 账单编号 **/
    private String billCode;

    /** 账单对应订单ID **/
    private Integer orderId;

    /** 账单对应订单编码 **/
    private String orderCode;

    /** 客户code **/
    private String customerCode;

    /** 账单所属客户名称 **/
    private String customerName;

    /** 账单类型 1-系统使用费 2-定制开发费 3-设备租赁费 4-设备购置费 5-设备流量费 6-设备安装费 7-违章代查含图 8-违章代查 9-充电代垫 10-维保代垫 11-维保托管审批 **/
    private Byte billType;

    /** 出账方式 按合同签署出账-by_contract_signing、按实际使用出账-by_actual_usage、按手动创建出账-by_manual_creation **/
    private String billingMethod;

    /** 已回款金额 **/
    private BigDecimal returnAmount;

    /** 账单状态 0-待出账、10-待推送账单、20-已推送账单、30-已废弃 **/
    private Byte billStatus;

    /** 账单金额 **/
    private BigDecimal billAmount;

    /** 回款状态（0-待回款、10-部分回款、20-已回款） **/
    private Integer repaymentStatus;

    /** 回款金额 **/
    private BigDecimal repaymentAmount;

    /** 账单计算起始日期；格式：yyyy-MM-dd；首付款为账单产生日期；账单产生时写入 **/
    private Date billSdate;

    /** 账单计算终止日期；格式：yyyy-MM-dd；账单产生时写入 **/
    private Date billEdate;

    /** 支付周期 一次性支付-one_time、月付-monthly、季付-quarterly、半年付-biannual、年付-annual **/
    private String paymentCycle;

    /** 账单期数，如 1 **/
    private Integer billPeriod;

    /** 账单总期数，如 3 **/
    private Integer billTotalPeriod;

    /** 订单期数，如 1 **/
    private Integer orderPeriod;

    /** 订单总期数，如 12 **/
    private Integer orderTotalPeriod;

    /** 应回款日期 **/
    private Date paymentDueDate;

    /** 税率 **/
    private BigDecimal taxRate;

    /** 合同编号 **/
    private String contractCode;

    /** 合同所有人id **/
    private String signSalesId;

    /** 合同所有人姓名 **/
    private String signSalesName;

    /** 财务组织机构编码-销售主体 **/
    private String financeStructCode;

    /** 财务组织机构名称-销售主体 **/
    private String financeStructName;

    /** 签约主体code **/
    private String operateCode;

    /** 签约主体名称 **/
    private String operateName;

    /** 单价 **/
    private BigDecimal unitPrice;

    /** 数量 **/
    private BigDecimal quantity;

    /** 调价金额 **/
    private BigDecimal adjustAmount;

    /** 创建方式（系统创建 -system_created   手动创建-manually_created） **/
    private String createMethod;

    /** 账单描述 **/
    private String billDesc;

    /** 出账日 **/
    private Date billingDate;

    /** 账期天数 **/
    private Integer billingPeriodDays;

    /** 创建人ID **/
    private String createId;

    /** 创建人姓名 **/
    private String createName;

    /** 创建时间 **/
    private Date createTime;

    /** 更新人ID **/
    private String updateId;

    /** 更新人姓名 **/
    private String updateName;

    /** 更新日期 **/
    private Date updateTime;

    /** 调价原因 **/
    private String adjustReason;

    /** 合同类型（长租业务合同/非常规业务合同/Mr.Car系统使用合同/长租补充协议） **/
    private Integer contractType;

    /** 合同id **/
    private Integer contractId;

    /** 合同维护人id **/
    private String maintainId;

    /** 合同维护人姓名 **/
    private String maintainName;

    /** 付款方式 1 预付，2后付 3预储值扣款 **/
    private Integer paymentType;

    /** 配置计费日期 **/
    private Date configDate;

    /** 主键id **/
    public Integer getId() {
        return id;
    }

    /** 主键id **/
    public void setId(Integer id) {
        this.id = id;
    }

    /** 账单编号 **/
    public String getBillCode() {
        return billCode;
    }

    /** 账单编号 **/
    public void setBillCode(String billCode) {
        this.billCode = billCode == null ? null : billCode.trim();
    }

    /** 账单对应订单ID **/
    public Integer getOrderId() {
        return orderId;
    }

    /** 账单对应订单ID **/
    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    /** 账单对应订单编码 **/
    public String getOrderCode() {
        return orderCode;
    }

    /** 账单对应订单编码 **/
    public void setOrderCode(String orderCode) {
        this.orderCode = orderCode == null ? null : orderCode.trim();
    }

    /** 客户code **/
    public String getCustomerCode() {
        return customerCode;
    }

    /** 客户code **/
    public void setCustomerCode(String customerCode) {
        this.customerCode = customerCode == null ? null : customerCode.trim();
    }

    /** 账单所属客户名称 **/
    public String getCustomerName() {
        return customerName;
    }

    /** 账单所属客户名称 **/
    public void setCustomerName(String customerName) {
        this.customerName = customerName == null ? null : customerName.trim();
    }

    /** 账单类型 1-系统使用费 2-定制开发费 3-设备租赁费 4-设备购置费 5-设备流量费 6-设备安装费 7-违章代查含图 8-违章代查 9-充电代垫 10-维保代垫 11-维保托管审批 **/
    public Byte getBillType() {
        return billType;
    }

    /** 账单类型 1-系统使用费 2-定制开发费 3-设备租赁费 4-设备购置费 5-设备流量费 6-设备安装费 7-违章代查含图 8-违章代查 9-充电代垫 10-维保代垫 11-维保托管审批 **/
    public void setBillType(Byte billType) {
        this.billType = billType;
    }

    /** 出账方式 按合同签署出账-by_contract_signing、按实际使用出账-by_actual_usage、按手动创建出账-by_manual_creation **/
    public String getBillingMethod() {
        return billingMethod;
    }

    /** 出账方式 按合同签署出账-by_contract_signing、按实际使用出账-by_actual_usage、按手动创建出账-by_manual_creation **/
    public void setBillingMethod(String billingMethod) {
        this.billingMethod = billingMethod == null ? null : billingMethod.trim();
    }

    /** 已回款金额 **/
    public BigDecimal getReturnAmount() {
        return returnAmount;
    }

    /** 已回款金额 **/
    public void setReturnAmount(BigDecimal returnAmount) {
        this.returnAmount = returnAmount;
    }

    /** 账单状态 0-待出账、10-待推送账单、20-已推送账单、30-已废弃 **/
    public Byte getBillStatus() {
        return billStatus;
    }

    /** 账单状态 0-待出账、10-待推送账单、20-已推送账单、30-已废弃 **/
    public void setBillStatus(Byte billStatus) {
        this.billStatus = billStatus;
    }

    /** 账单金额 **/
    public BigDecimal getBillAmount() {
        return billAmount;
    }

    /** 账单金额 **/
    public void setBillAmount(BigDecimal billAmount) {
        this.billAmount = billAmount;
    }

    /** 回款状态（0-待回款、10-部分回款、20-已回款） **/
    public Integer getRepaymentStatus() {
        return repaymentStatus;
    }

    /** 回款状态（0-待回款、10-部分回款、20-已回款） **/
    public void setRepaymentStatus(Integer repaymentStatus) {
        this.repaymentStatus = repaymentStatus;
    }

    /** 回款金额 **/
    public BigDecimal getRepaymentAmount() {
        return repaymentAmount;
    }

    /** 回款金额 **/
    public void setRepaymentAmount(BigDecimal repaymentAmount) {
        this.repaymentAmount = repaymentAmount;
    }

    /** 账单计算起始日期；格式：yyyy-MM-dd；首付款为账单产生日期；账单产生时写入 **/
    public Date getBillSdate() {
        return billSdate;
    }

    /** 账单计算起始日期；格式：yyyy-MM-dd；首付款为账单产生日期；账单产生时写入 **/
    public void setBillSdate(Date billSdate) {
        this.billSdate = billSdate;
    }

    /** 账单计算终止日期；格式：yyyy-MM-dd；账单产生时写入 **/
    public Date getBillEdate() {
        return billEdate;
    }

    /** 账单计算终止日期；格式：yyyy-MM-dd；账单产生时写入 **/
    public void setBillEdate(Date billEdate) {
        this.billEdate = billEdate;
    }

    /** 支付周期 一次性支付-one_time、月付-monthly、季付-quarterly、半年付-biannual、年付-annual **/
    public String getPaymentCycle() {
        return paymentCycle;
    }

    /** 支付周期 一次性支付-one_time、月付-monthly、季付-quarterly、半年付-biannual、年付-annual **/
    public void setPaymentCycle(String paymentCycle) {
        this.paymentCycle = paymentCycle == null ? null : paymentCycle.trim();
    }

    /** 账单期数，如 1 **/
    public Integer getBillPeriod() {
        return billPeriod;
    }

    /** 账单期数，如 1 **/
    public void setBillPeriod(Integer billPeriod) {
        this.billPeriod = billPeriod;
    }

    /** 账单总期数，如 3 **/
    public Integer getBillTotalPeriod() {
        return billTotalPeriod;
    }

    /** 账单总期数，如 3 **/
    public void setBillTotalPeriod(Integer billTotalPeriod) {
        this.billTotalPeriod = billTotalPeriod;
    }

    /** 订单期数，如 1 **/
    public Integer getOrderPeriod() {
        return orderPeriod;
    }

    /** 订单期数，如 1 **/
    public void setOrderPeriod(Integer orderPeriod) {
        this.orderPeriod = orderPeriod;
    }

    /** 订单总期数，如 12 **/
    public Integer getOrderTotalPeriod() {
        return orderTotalPeriod;
    }

    /** 订单总期数，如 12 **/
    public void setOrderTotalPeriod(Integer orderTotalPeriod) {
        this.orderTotalPeriod = orderTotalPeriod;
    }

    /** 应回款日期 **/
    public Date getPaymentDueDate() {
        return paymentDueDate;
    }

    /** 应回款日期 **/
    public void setPaymentDueDate(Date paymentDueDate) {
        this.paymentDueDate = paymentDueDate;
    }

    /** 税率 **/
    public BigDecimal getTaxRate() {
        return taxRate;
    }

    /** 税率 **/
    public void setTaxRate(BigDecimal taxRate) {
        this.taxRate = taxRate;
    }

    /** 合同编号 **/
    public String getContractCode() {
        return contractCode;
    }

    /** 合同编号 **/
    public void setContractCode(String contractCode) {
        this.contractCode = contractCode == null ? null : contractCode.trim();
    }

    /** 合同所有人id **/
    public String getSignSalesId() {
        return signSalesId;
    }

    /** 合同所有人id **/
    public void setSignSalesId(String signSalesId) {
        this.signSalesId = signSalesId == null ? null : signSalesId.trim();
    }

    /** 合同所有人姓名 **/
    public String getSignSalesName() {
        return signSalesName;
    }

    /** 合同所有人姓名 **/
    public void setSignSalesName(String signSalesName) {
        this.signSalesName = signSalesName == null ? null : signSalesName.trim();
    }

    /** 财务组织机构编码-销售主体 **/
    public String getFinanceStructCode() {
        return financeStructCode;
    }

    /** 财务组织机构编码-销售主体 **/
    public void setFinanceStructCode(String financeStructCode) {
        this.financeStructCode = financeStructCode == null ? null : financeStructCode.trim();
    }

    /** 财务组织机构名称-销售主体 **/
    public String getFinanceStructName() {
        return financeStructName;
    }

    /** 财务组织机构名称-销售主体 **/
    public void setFinanceStructName(String financeStructName) {
        this.financeStructName = financeStructName == null ? null : financeStructName.trim();
    }

    /** 签约主体code **/
    public String getOperateCode() {
        return operateCode;
    }

    /** 签约主体code **/
    public void setOperateCode(String operateCode) {
        this.operateCode = operateCode == null ? null : operateCode.trim();
    }

    /** 签约主体名称 **/
    public String getOperateName() {
        return operateName;
    }

    /** 签约主体名称 **/
    public void setOperateName(String operateName) {
        this.operateName = operateName == null ? null : operateName.trim();
    }

    /** 单价 **/
    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    /** 单价 **/
    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    /** 数量 **/
    public BigDecimal getQuantity() {
        return quantity;
    }

    /** 数量 **/
    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    /** 调价金额 **/
    public BigDecimal getAdjustAmount() {
        return adjustAmount;
    }

    /** 调价金额 **/
    public void setAdjustAmount(BigDecimal adjustAmount) {
        this.adjustAmount = adjustAmount;
    }

    /** 创建方式（系统创建 -system_created   手动创建-manually_created） **/
    public String getCreateMethod() {
        return createMethod;
    }

    /** 创建方式（系统创建 -system_created   手动创建-manually_created） **/
    public void setCreateMethod(String createMethod) {
        this.createMethod = createMethod == null ? null : createMethod.trim();
    }

    /** 账单描述 **/
    public String getBillDesc() {
        return billDesc;
    }

    /** 账单描述 **/
    public void setBillDesc(String billDesc) {
        this.billDesc = billDesc == null ? null : billDesc.trim();
    }

    /** 出账日 **/
    public Date getBillingDate() {
        return billingDate;
    }

    /** 出账日 **/
    public void setBillingDate(Date billingDate) {
        this.billingDate = billingDate;
    }

    /** 账期天数 **/
    public Integer getBillingPeriodDays() {
        return billingPeriodDays;
    }

    /** 账期天数 **/
    public void setBillingPeriodDays(Integer billingPeriodDays) {
        this.billingPeriodDays = billingPeriodDays;
    }

    /** 创建人ID **/
    public String getCreateId() {
        return createId;
    }

    /** 创建人ID **/
    public void setCreateId(String createId) {
        this.createId = createId == null ? null : createId.trim();
    }

    /** 创建人姓名 **/
    public String getCreateName() {
        return createName;
    }

    /** 创建人姓名 **/
    public void setCreateName(String createName) {
        this.createName = createName == null ? null : createName.trim();
    }

    /** 创建时间 **/
    public Date getCreateTime() {
        return createTime;
    }

    /** 创建时间 **/
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /** 更新人ID **/
    public String getUpdateId() {
        return updateId;
    }

    /** 更新人ID **/
    public void setUpdateId(String updateId) {
        this.updateId = updateId == null ? null : updateId.trim();
    }

    /** 更新人姓名 **/
    public String getUpdateName() {
        return updateName;
    }

    /** 更新人姓名 **/
    public void setUpdateName(String updateName) {
        this.updateName = updateName == null ? null : updateName.trim();
    }

    /** 更新日期 **/
    public Date getUpdateTime() {
        return updateTime;
    }

    /** 更新日期 **/
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /** 调价原因 **/
    public String getAdjustReason() {
        return adjustReason;
    }

    /** 调价原因 **/
    public void setAdjustReason(String adjustReason) {
        this.adjustReason = adjustReason == null ? null : adjustReason.trim();
    }

    /** 合同类型（长租业务合同/非常规业务合同/Mr.Car系统使用合同/长租补充协议） **/
    public Integer getContractType() {
        return contractType;
    }

    /** 合同类型（长租业务合同/非常规业务合同/Mr.Car系统使用合同/长租补充协议） **/
    public void setContractType(Integer contractType) {
        this.contractType = contractType;
    }

    /** 合同id **/
    public Integer getContractId() {
        return contractId;
    }

    /** 合同id **/
    public void setContractId(Integer contractId) {
        this.contractId = contractId;
    }

    /** 合同维护人id **/
    public String getMaintainId() {
        return maintainId;
    }

    /** 合同维护人id **/
    public void setMaintainId(String maintainId) {
        this.maintainId = maintainId == null ? null : maintainId.trim();
    }

    /** 合同维护人姓名 **/
    public String getMaintainName() {
        return maintainName;
    }

    /** 合同维护人姓名 **/
    public void setMaintainName(String maintainName) {
        this.maintainName = maintainName == null ? null : maintainName.trim();
    }

    /** 付款方式 1 预付，2后付 3预储值扣款 **/
    public Integer getPaymentType() {
        return paymentType;
    }

    /** 付款方式 1 预付，2后付 3预储值扣款 **/
    public void setPaymentType(Integer paymentType) {
        this.paymentType = paymentType;
    }

    /** 配置计费日期 **/
    public Date getConfigDate() {
        return configDate;
    }

    /** 配置计费日期 **/
    public void setConfigDate(Date configDate) {
        this.configDate = configDate;
    }
}