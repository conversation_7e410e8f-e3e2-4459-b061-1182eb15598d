package com.izu.business.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class LeaseContractVehicleExample {
    /**
     * lease_contract_vehicle
     */
    protected String orderByClause;

    /**
     * lease_contract_vehicle
     */
    protected boolean distinct;

    /**
     * lease_contract_vehicle
     */
    protected List<Criteria> oredCriteria;

    public LeaseContractVehicleExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andContractIdIsNull() {
            addCriterion("contract_id is null");
            return (Criteria) this;
        }

        public Criteria andContractIdIsNotNull() {
            addCriterion("contract_id is not null");
            return (Criteria) this;
        }

        public Criteria andContractIdEqualTo(Integer value) {
            addCriterion("contract_id =", value, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdNotEqualTo(Integer value) {
            addCriterion("contract_id <>", value, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdGreaterThan(Integer value) {
            addCriterion("contract_id >", value, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("contract_id >=", value, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdLessThan(Integer value) {
            addCriterion("contract_id <", value, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdLessThanOrEqualTo(Integer value) {
            addCriterion("contract_id <=", value, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdIn(List<Integer> values) {
            addCriterion("contract_id in", values, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdNotIn(List<Integer> values) {
            addCriterion("contract_id not in", values, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdBetween(Integer value1, Integer value2) {
            addCriterion("contract_id between", value1, value2, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractIdNotBetween(Integer value1, Integer value2) {
            addCriterion("contract_id not between", value1, value2, "contractId");
            return (Criteria) this;
        }

        public Criteria andContractCodeIsNull() {
            addCriterion("contract_code is null");
            return (Criteria) this;
        }

        public Criteria andContractCodeIsNotNull() {
            addCriterion("contract_code is not null");
            return (Criteria) this;
        }

        public Criteria andContractCodeEqualTo(String value) {
            addCriterion("contract_code =", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotEqualTo(String value) {
            addCriterion("contract_code <>", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeGreaterThan(String value) {
            addCriterion("contract_code >", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeGreaterThanOrEqualTo(String value) {
            addCriterion("contract_code >=", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeLessThan(String value) {
            addCriterion("contract_code <", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeLessThanOrEqualTo(String value) {
            addCriterion("contract_code <=", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeLike(String value) {
            addCriterion("contract_code like", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotLike(String value) {
            addCriterion("contract_code not like", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeIn(List<String> values) {
            addCriterion("contract_code in", values, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotIn(List<String> values) {
            addCriterion("contract_code not in", values, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeBetween(String value1, String value2) {
            addCriterion("contract_code between", value1, value2, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotBetween(String value1, String value2) {
            addCriterion("contract_code not between", value1, value2, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractTypeIsNull() {
            addCriterion("contract_type is null");
            return (Criteria) this;
        }

        public Criteria andContractTypeIsNotNull() {
            addCriterion("contract_type is not null");
            return (Criteria) this;
        }

        public Criteria andContractTypeEqualTo(Integer value) {
            addCriterion("contract_type =", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeNotEqualTo(Integer value) {
            addCriterion("contract_type <>", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeGreaterThan(Integer value) {
            addCriterion("contract_type >", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("contract_type >=", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeLessThan(Integer value) {
            addCriterion("contract_type <", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeLessThanOrEqualTo(Integer value) {
            addCriterion("contract_type <=", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeIn(List<Integer> values) {
            addCriterion("contract_type in", values, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeNotIn(List<Integer> values) {
            addCriterion("contract_type not in", values, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeBetween(Integer value1, Integer value2) {
            addCriterion("contract_type between", value1, value2, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("contract_type not between", value1, value2, "contractType");
            return (Criteria) this;
        }

        public Criteria andBuyCarDateIsNull() {
            addCriterion("buy_car_date is null");
            return (Criteria) this;
        }

        public Criteria andBuyCarDateIsNotNull() {
            addCriterion("buy_car_date is not null");
            return (Criteria) this;
        }

        public Criteria andBuyCarDateEqualTo(String value) {
            addCriterion("buy_car_date =", value, "buyCarDate");
            return (Criteria) this;
        }

        public Criteria andBuyCarDateNotEqualTo(String value) {
            addCriterion("buy_car_date <>", value, "buyCarDate");
            return (Criteria) this;
        }

        public Criteria andBuyCarDateGreaterThan(String value) {
            addCriterion("buy_car_date >", value, "buyCarDate");
            return (Criteria) this;
        }

        public Criteria andBuyCarDateGreaterThanOrEqualTo(String value) {
            addCriterion("buy_car_date >=", value, "buyCarDate");
            return (Criteria) this;
        }

        public Criteria andBuyCarDateLessThan(String value) {
            addCriterion("buy_car_date <", value, "buyCarDate");
            return (Criteria) this;
        }

        public Criteria andBuyCarDateLessThanOrEqualTo(String value) {
            addCriterion("buy_car_date <=", value, "buyCarDate");
            return (Criteria) this;
        }

        public Criteria andBuyCarDateLike(String value) {
            addCriterion("buy_car_date like", value, "buyCarDate");
            return (Criteria) this;
        }

        public Criteria andBuyCarDateNotLike(String value) {
            addCriterion("buy_car_date not like", value, "buyCarDate");
            return (Criteria) this;
        }

        public Criteria andBuyCarDateIn(List<String> values) {
            addCriterion("buy_car_date in", values, "buyCarDate");
            return (Criteria) this;
        }

        public Criteria andBuyCarDateNotIn(List<String> values) {
            addCriterion("buy_car_date not in", values, "buyCarDate");
            return (Criteria) this;
        }

        public Criteria andBuyCarDateBetween(String value1, String value2) {
            addCriterion("buy_car_date between", value1, value2, "buyCarDate");
            return (Criteria) this;
        }

        public Criteria andBuyCarDateNotBetween(String value1, String value2) {
            addCriterion("buy_car_date not between", value1, value2, "buyCarDate");
            return (Criteria) this;
        }

        public Criteria andVehicleNumbersIsNull() {
            addCriterion("vehicle_numbers is null");
            return (Criteria) this;
        }

        public Criteria andVehicleNumbersIsNotNull() {
            addCriterion("vehicle_numbers is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleNumbersEqualTo(Integer value) {
            addCriterion("vehicle_numbers =", value, "vehicleNumbers");
            return (Criteria) this;
        }

        public Criteria andVehicleNumbersNotEqualTo(Integer value) {
            addCriterion("vehicle_numbers <>", value, "vehicleNumbers");
            return (Criteria) this;
        }

        public Criteria andVehicleNumbersGreaterThan(Integer value) {
            addCriterion("vehicle_numbers >", value, "vehicleNumbers");
            return (Criteria) this;
        }

        public Criteria andVehicleNumbersGreaterThanOrEqualTo(Integer value) {
            addCriterion("vehicle_numbers >=", value, "vehicleNumbers");
            return (Criteria) this;
        }

        public Criteria andVehicleNumbersLessThan(Integer value) {
            addCriterion("vehicle_numbers <", value, "vehicleNumbers");
            return (Criteria) this;
        }

        public Criteria andVehicleNumbersLessThanOrEqualTo(Integer value) {
            addCriterion("vehicle_numbers <=", value, "vehicleNumbers");
            return (Criteria) this;
        }

        public Criteria andVehicleNumbersIn(List<Integer> values) {
            addCriterion("vehicle_numbers in", values, "vehicleNumbers");
            return (Criteria) this;
        }

        public Criteria andVehicleNumbersNotIn(List<Integer> values) {
            addCriterion("vehicle_numbers not in", values, "vehicleNumbers");
            return (Criteria) this;
        }

        public Criteria andVehicleNumbersBetween(Integer value1, Integer value2) {
            addCriterion("vehicle_numbers between", value1, value2, "vehicleNumbers");
            return (Criteria) this;
        }

        public Criteria andVehicleNumbersNotBetween(Integer value1, Integer value2) {
            addCriterion("vehicle_numbers not between", value1, value2, "vehicleNumbers");
            return (Criteria) this;
        }

        public Criteria andLeasePeriodIsNull() {
            addCriterion("lease_period is null");
            return (Criteria) this;
        }

        public Criteria andLeasePeriodIsNotNull() {
            addCriterion("lease_period is not null");
            return (Criteria) this;
        }

        public Criteria andLeasePeriodEqualTo(Integer value) {
            addCriterion("lease_period =", value, "leasePeriod");
            return (Criteria) this;
        }

        public Criteria andLeasePeriodNotEqualTo(Integer value) {
            addCriterion("lease_period <>", value, "leasePeriod");
            return (Criteria) this;
        }

        public Criteria andLeasePeriodGreaterThan(Integer value) {
            addCriterion("lease_period >", value, "leasePeriod");
            return (Criteria) this;
        }

        public Criteria andLeasePeriodGreaterThanOrEqualTo(Integer value) {
            addCriterion("lease_period >=", value, "leasePeriod");
            return (Criteria) this;
        }

        public Criteria andLeasePeriodLessThan(Integer value) {
            addCriterion("lease_period <", value, "leasePeriod");
            return (Criteria) this;
        }

        public Criteria andLeasePeriodLessThanOrEqualTo(Integer value) {
            addCriterion("lease_period <=", value, "leasePeriod");
            return (Criteria) this;
        }

        public Criteria andLeasePeriodIn(List<Integer> values) {
            addCriterion("lease_period in", values, "leasePeriod");
            return (Criteria) this;
        }

        public Criteria andLeasePeriodNotIn(List<Integer> values) {
            addCriterion("lease_period not in", values, "leasePeriod");
            return (Criteria) this;
        }

        public Criteria andLeasePeriodBetween(Integer value1, Integer value2) {
            addCriterion("lease_period between", value1, value2, "leasePeriod");
            return (Criteria) this;
        }

        public Criteria andLeasePeriodNotBetween(Integer value1, Integer value2) {
            addCriterion("lease_period not between", value1, value2, "leasePeriod");
            return (Criteria) this;
        }

        public Criteria andMonthRentIsNull() {
            addCriterion("month_rent is null");
            return (Criteria) this;
        }

        public Criteria andMonthRentIsNotNull() {
            addCriterion("month_rent is not null");
            return (Criteria) this;
        }

        public Criteria andMonthRentEqualTo(BigDecimal value) {
            addCriterion("month_rent =", value, "monthRent");
            return (Criteria) this;
        }

        public Criteria andMonthRentNotEqualTo(BigDecimal value) {
            addCriterion("month_rent <>", value, "monthRent");
            return (Criteria) this;
        }

        public Criteria andMonthRentGreaterThan(BigDecimal value) {
            addCriterion("month_rent >", value, "monthRent");
            return (Criteria) this;
        }

        public Criteria andMonthRentGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("month_rent >=", value, "monthRent");
            return (Criteria) this;
        }

        public Criteria andMonthRentLessThan(BigDecimal value) {
            addCriterion("month_rent <", value, "monthRent");
            return (Criteria) this;
        }

        public Criteria andMonthRentLessThanOrEqualTo(BigDecimal value) {
            addCriterion("month_rent <=", value, "monthRent");
            return (Criteria) this;
        }

        public Criteria andMonthRentIn(List<BigDecimal> values) {
            addCriterion("month_rent in", values, "monthRent");
            return (Criteria) this;
        }

        public Criteria andMonthRentNotIn(List<BigDecimal> values) {
            addCriterion("month_rent not in", values, "monthRent");
            return (Criteria) this;
        }

        public Criteria andMonthRentBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("month_rent between", value1, value2, "monthRent");
            return (Criteria) this;
        }

        public Criteria andMonthRentNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("month_rent not between", value1, value2, "monthRent");
            return (Criteria) this;
        }

        public Criteria andDepositAmountIsNull() {
            addCriterion("deposit_amount is null");
            return (Criteria) this;
        }

        public Criteria andDepositAmountIsNotNull() {
            addCriterion("deposit_amount is not null");
            return (Criteria) this;
        }

        public Criteria andDepositAmountEqualTo(String value) {
            addCriterion("deposit_amount =", value, "depositAmount");
            return (Criteria) this;
        }

        public Criteria andDepositAmountNotEqualTo(String value) {
            addCriterion("deposit_amount <>", value, "depositAmount");
            return (Criteria) this;
        }

        public Criteria andDepositAmountGreaterThan(String value) {
            addCriterion("deposit_amount >", value, "depositAmount");
            return (Criteria) this;
        }

        public Criteria andDepositAmountGreaterThanOrEqualTo(String value) {
            addCriterion("deposit_amount >=", value, "depositAmount");
            return (Criteria) this;
        }

        public Criteria andDepositAmountLessThan(String value) {
            addCriterion("deposit_amount <", value, "depositAmount");
            return (Criteria) this;
        }

        public Criteria andDepositAmountLessThanOrEqualTo(String value) {
            addCriterion("deposit_amount <=", value, "depositAmount");
            return (Criteria) this;
        }

        public Criteria andDepositAmountLike(String value) {
            addCriterion("deposit_amount like", value, "depositAmount");
            return (Criteria) this;
        }

        public Criteria andDepositAmountNotLike(String value) {
            addCriterion("deposit_amount not like", value, "depositAmount");
            return (Criteria) this;
        }

        public Criteria andDepositAmountIn(List<String> values) {
            addCriterion("deposit_amount in", values, "depositAmount");
            return (Criteria) this;
        }

        public Criteria andDepositAmountNotIn(List<String> values) {
            addCriterion("deposit_amount not in", values, "depositAmount");
            return (Criteria) this;
        }

        public Criteria andDepositAmountBetween(String value1, String value2) {
            addCriterion("deposit_amount between", value1, value2, "depositAmount");
            return (Criteria) this;
        }

        public Criteria andDepositAmountNotBetween(String value1, String value2) {
            addCriterion("deposit_amount not between", value1, value2, "depositAmount");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeIsNull() {
            addCriterion("vehicle_model_code is null");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeIsNotNull() {
            addCriterion("vehicle_model_code is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeEqualTo(String value) {
            addCriterion("vehicle_model_code =", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeNotEqualTo(String value) {
            addCriterion("vehicle_model_code <>", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeGreaterThan(String value) {
            addCriterion("vehicle_model_code >", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_model_code >=", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeLessThan(String value) {
            addCriterion("vehicle_model_code <", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeLessThanOrEqualTo(String value) {
            addCriterion("vehicle_model_code <=", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeLike(String value) {
            addCriterion("vehicle_model_code like", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeNotLike(String value) {
            addCriterion("vehicle_model_code not like", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeIn(List<String> values) {
            addCriterion("vehicle_model_code in", values, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeNotIn(List<String> values) {
            addCriterion("vehicle_model_code not in", values, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeBetween(String value1, String value2) {
            addCriterion("vehicle_model_code between", value1, value2, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeNotBetween(String value1, String value2) {
            addCriterion("vehicle_model_code not between", value1, value2, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameIsNull() {
            addCriterion("vehicle_model_name is null");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameIsNotNull() {
            addCriterion("vehicle_model_name is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameEqualTo(String value) {
            addCriterion("vehicle_model_name =", value, "vehicleModelName");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameNotEqualTo(String value) {
            addCriterion("vehicle_model_name <>", value, "vehicleModelName");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameGreaterThan(String value) {
            addCriterion("vehicle_model_name >", value, "vehicleModelName");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_model_name >=", value, "vehicleModelName");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameLessThan(String value) {
            addCriterion("vehicle_model_name <", value, "vehicleModelName");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameLessThanOrEqualTo(String value) {
            addCriterion("vehicle_model_name <=", value, "vehicleModelName");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameLike(String value) {
            addCriterion("vehicle_model_name like", value, "vehicleModelName");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameNotLike(String value) {
            addCriterion("vehicle_model_name not like", value, "vehicleModelName");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameIn(List<String> values) {
            addCriterion("vehicle_model_name in", values, "vehicleModelName");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameNotIn(List<String> values) {
            addCriterion("vehicle_model_name not in", values, "vehicleModelName");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameBetween(String value1, String value2) {
            addCriterion("vehicle_model_name between", value1, value2, "vehicleModelName");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameNotBetween(String value1, String value2) {
            addCriterion("vehicle_model_name not between", value1, value2, "vehicleModelName");
            return (Criteria) this;
        }

        public Criteria andConfigpackCodeIsNull() {
            addCriterion("configpack_code is null");
            return (Criteria) this;
        }

        public Criteria andConfigpackCodeIsNotNull() {
            addCriterion("configpack_code is not null");
            return (Criteria) this;
        }

        public Criteria andConfigpackCodeEqualTo(String value) {
            addCriterion("configpack_code =", value, "configpackCode");
            return (Criteria) this;
        }

        public Criteria andConfigpackCodeNotEqualTo(String value) {
            addCriterion("configpack_code <>", value, "configpackCode");
            return (Criteria) this;
        }

        public Criteria andConfigpackCodeGreaterThan(String value) {
            addCriterion("configpack_code >", value, "configpackCode");
            return (Criteria) this;
        }

        public Criteria andConfigpackCodeGreaterThanOrEqualTo(String value) {
            addCriterion("configpack_code >=", value, "configpackCode");
            return (Criteria) this;
        }

        public Criteria andConfigpackCodeLessThan(String value) {
            addCriterion("configpack_code <", value, "configpackCode");
            return (Criteria) this;
        }

        public Criteria andConfigpackCodeLessThanOrEqualTo(String value) {
            addCriterion("configpack_code <=", value, "configpackCode");
            return (Criteria) this;
        }

        public Criteria andConfigpackCodeLike(String value) {
            addCriterion("configpack_code like", value, "configpackCode");
            return (Criteria) this;
        }

        public Criteria andConfigpackCodeNotLike(String value) {
            addCriterion("configpack_code not like", value, "configpackCode");
            return (Criteria) this;
        }

        public Criteria andConfigpackCodeIn(List<String> values) {
            addCriterion("configpack_code in", values, "configpackCode");
            return (Criteria) this;
        }

        public Criteria andConfigpackCodeNotIn(List<String> values) {
            addCriterion("configpack_code not in", values, "configpackCode");
            return (Criteria) this;
        }

        public Criteria andConfigpackCodeBetween(String value1, String value2) {
            addCriterion("configpack_code between", value1, value2, "configpackCode");
            return (Criteria) this;
        }

        public Criteria andConfigpackCodeNotBetween(String value1, String value2) {
            addCriterion("configpack_code not between", value1, value2, "configpackCode");
            return (Criteria) this;
        }

        public Criteria andConfigpackNameIsNull() {
            addCriterion("configpack_name is null");
            return (Criteria) this;
        }

        public Criteria andConfigpackNameIsNotNull() {
            addCriterion("configpack_name is not null");
            return (Criteria) this;
        }

        public Criteria andConfigpackNameEqualTo(String value) {
            addCriterion("configpack_name =", value, "configpackName");
            return (Criteria) this;
        }

        public Criteria andConfigpackNameNotEqualTo(String value) {
            addCriterion("configpack_name <>", value, "configpackName");
            return (Criteria) this;
        }

        public Criteria andConfigpackNameGreaterThan(String value) {
            addCriterion("configpack_name >", value, "configpackName");
            return (Criteria) this;
        }

        public Criteria andConfigpackNameGreaterThanOrEqualTo(String value) {
            addCriterion("configpack_name >=", value, "configpackName");
            return (Criteria) this;
        }

        public Criteria andConfigpackNameLessThan(String value) {
            addCriterion("configpack_name <", value, "configpackName");
            return (Criteria) this;
        }

        public Criteria andConfigpackNameLessThanOrEqualTo(String value) {
            addCriterion("configpack_name <=", value, "configpackName");
            return (Criteria) this;
        }

        public Criteria andConfigpackNameLike(String value) {
            addCriterion("configpack_name like", value, "configpackName");
            return (Criteria) this;
        }

        public Criteria andConfigpackNameNotLike(String value) {
            addCriterion("configpack_name not like", value, "configpackName");
            return (Criteria) this;
        }

        public Criteria andConfigpackNameIn(List<String> values) {
            addCriterion("configpack_name in", values, "configpackName");
            return (Criteria) this;
        }

        public Criteria andConfigpackNameNotIn(List<String> values) {
            addCriterion("configpack_name not in", values, "configpackName");
            return (Criteria) this;
        }

        public Criteria andConfigpackNameBetween(String value1, String value2) {
            addCriterion("configpack_name between", value1, value2, "configpackName");
            return (Criteria) this;
        }

        public Criteria andConfigpackNameNotBetween(String value1, String value2) {
            addCriterion("configpack_name not between", value1, value2, "configpackName");
            return (Criteria) this;
        }

        public Criteria andVehicleCityCodeIsNull() {
            addCriterion("vehicle_city_code is null");
            return (Criteria) this;
        }

        public Criteria andVehicleCityCodeIsNotNull() {
            addCriterion("vehicle_city_code is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleCityCodeEqualTo(String value) {
            addCriterion("vehicle_city_code =", value, "vehicleCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleCityCodeNotEqualTo(String value) {
            addCriterion("vehicle_city_code <>", value, "vehicleCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleCityCodeGreaterThan(String value) {
            addCriterion("vehicle_city_code >", value, "vehicleCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleCityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_city_code >=", value, "vehicleCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleCityCodeLessThan(String value) {
            addCriterion("vehicle_city_code <", value, "vehicleCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleCityCodeLessThanOrEqualTo(String value) {
            addCriterion("vehicle_city_code <=", value, "vehicleCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleCityCodeLike(String value) {
            addCriterion("vehicle_city_code like", value, "vehicleCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleCityCodeNotLike(String value) {
            addCriterion("vehicle_city_code not like", value, "vehicleCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleCityCodeIn(List<String> values) {
            addCriterion("vehicle_city_code in", values, "vehicleCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleCityCodeNotIn(List<String> values) {
            addCriterion("vehicle_city_code not in", values, "vehicleCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleCityCodeBetween(String value1, String value2) {
            addCriterion("vehicle_city_code between", value1, value2, "vehicleCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleCityCodeNotBetween(String value1, String value2) {
            addCriterion("vehicle_city_code not between", value1, value2, "vehicleCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleCityNameIsNull() {
            addCriterion("vehicle_city_name is null");
            return (Criteria) this;
        }

        public Criteria andVehicleCityNameIsNotNull() {
            addCriterion("vehicle_city_name is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleCityNameEqualTo(String value) {
            addCriterion("vehicle_city_name =", value, "vehicleCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleCityNameNotEqualTo(String value) {
            addCriterion("vehicle_city_name <>", value, "vehicleCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleCityNameGreaterThan(String value) {
            addCriterion("vehicle_city_name >", value, "vehicleCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleCityNameGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_city_name >=", value, "vehicleCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleCityNameLessThan(String value) {
            addCriterion("vehicle_city_name <", value, "vehicleCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleCityNameLessThanOrEqualTo(String value) {
            addCriterion("vehicle_city_name <=", value, "vehicleCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleCityNameLike(String value) {
            addCriterion("vehicle_city_name like", value, "vehicleCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleCityNameNotLike(String value) {
            addCriterion("vehicle_city_name not like", value, "vehicleCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleCityNameIn(List<String> values) {
            addCriterion("vehicle_city_name in", values, "vehicleCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleCityNameNotIn(List<String> values) {
            addCriterion("vehicle_city_name not in", values, "vehicleCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleCityNameBetween(String value1, String value2) {
            addCriterion("vehicle_city_name between", value1, value2, "vehicleCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleCityNameNotBetween(String value1, String value2) {
            addCriterion("vehicle_city_name not between", value1, value2, "vehicleCityName");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}