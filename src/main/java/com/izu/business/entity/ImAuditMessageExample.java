package com.izu.business.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ImAuditMessageExample {
    /**
     * t_im_audit_message
     */
    protected String orderByClause;

    /**
     * t_im_audit_message
     */
    protected boolean distinct;

    /**
     * t_im_audit_message
     */
    protected List<Criteria> oredCriteria;

    public ImAuditMessageExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andMessageIdIsNull() {
            addCriterion("message_id is null");
            return (Criteria) this;
        }

        public Criteria andMessageIdIsNotNull() {
            addCriterion("message_id is not null");
            return (Criteria) this;
        }

        public Criteria andMessageIdEqualTo(String value) {
            addCriterion("message_id =", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdNotEqualTo(String value) {
            addCriterion("message_id <>", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdGreaterThan(String value) {
            addCriterion("message_id >", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdGreaterThanOrEqualTo(String value) {
            addCriterion("message_id >=", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdLessThan(String value) {
            addCriterion("message_id <", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdLessThanOrEqualTo(String value) {
            addCriterion("message_id <=", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdLike(String value) {
            addCriterion("message_id like", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdNotLike(String value) {
            addCriterion("message_id not like", value, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdIn(List<String> values) {
            addCriterion("message_id in", values, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdNotIn(List<String> values) {
            addCriterion("message_id not in", values, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdBetween(String value1, String value2) {
            addCriterion("message_id between", value1, value2, "messageId");
            return (Criteria) this;
        }

        public Criteria andMessageIdNotBetween(String value1, String value2) {
            addCriterion("message_id not between", value1, value2, "messageId");
            return (Criteria) this;
        }

        public Criteria andFromUserCodeIsNull() {
            addCriterion("from_user_code is null");
            return (Criteria) this;
        }

        public Criteria andFromUserCodeIsNotNull() {
            addCriterion("from_user_code is not null");
            return (Criteria) this;
        }

        public Criteria andFromUserCodeEqualTo(String value) {
            addCriterion("from_user_code =", value, "fromUserCode");
            return (Criteria) this;
        }

        public Criteria andFromUserCodeNotEqualTo(String value) {
            addCriterion("from_user_code <>", value, "fromUserCode");
            return (Criteria) this;
        }

        public Criteria andFromUserCodeGreaterThan(String value) {
            addCriterion("from_user_code >", value, "fromUserCode");
            return (Criteria) this;
        }

        public Criteria andFromUserCodeGreaterThanOrEqualTo(String value) {
            addCriterion("from_user_code >=", value, "fromUserCode");
            return (Criteria) this;
        }

        public Criteria andFromUserCodeLessThan(String value) {
            addCriterion("from_user_code <", value, "fromUserCode");
            return (Criteria) this;
        }

        public Criteria andFromUserCodeLessThanOrEqualTo(String value) {
            addCriterion("from_user_code <=", value, "fromUserCode");
            return (Criteria) this;
        }

        public Criteria andFromUserCodeLike(String value) {
            addCriterion("from_user_code like", value, "fromUserCode");
            return (Criteria) this;
        }

        public Criteria andFromUserCodeNotLike(String value) {
            addCriterion("from_user_code not like", value, "fromUserCode");
            return (Criteria) this;
        }

        public Criteria andFromUserCodeIn(List<String> values) {
            addCriterion("from_user_code in", values, "fromUserCode");
            return (Criteria) this;
        }

        public Criteria andFromUserCodeNotIn(List<String> values) {
            addCriterion("from_user_code not in", values, "fromUserCode");
            return (Criteria) this;
        }

        public Criteria andFromUserCodeBetween(String value1, String value2) {
            addCriterion("from_user_code between", value1, value2, "fromUserCode");
            return (Criteria) this;
        }

        public Criteria andFromUserCodeNotBetween(String value1, String value2) {
            addCriterion("from_user_code not between", value1, value2, "fromUserCode");
            return (Criteria) this;
        }

        public Criteria andFromUserNameIsNull() {
            addCriterion("from_user_name is null");
            return (Criteria) this;
        }

        public Criteria andFromUserNameIsNotNull() {
            addCriterion("from_user_name is not null");
            return (Criteria) this;
        }

        public Criteria andFromUserNameEqualTo(String value) {
            addCriterion("from_user_name =", value, "fromUserName");
            return (Criteria) this;
        }

        public Criteria andFromUserNameNotEqualTo(String value) {
            addCriterion("from_user_name <>", value, "fromUserName");
            return (Criteria) this;
        }

        public Criteria andFromUserNameGreaterThan(String value) {
            addCriterion("from_user_name >", value, "fromUserName");
            return (Criteria) this;
        }

        public Criteria andFromUserNameGreaterThanOrEqualTo(String value) {
            addCriterion("from_user_name >=", value, "fromUserName");
            return (Criteria) this;
        }

        public Criteria andFromUserNameLessThan(String value) {
            addCriterion("from_user_name <", value, "fromUserName");
            return (Criteria) this;
        }

        public Criteria andFromUserNameLessThanOrEqualTo(String value) {
            addCriterion("from_user_name <=", value, "fromUserName");
            return (Criteria) this;
        }

        public Criteria andFromUserNameLike(String value) {
            addCriterion("from_user_name like", value, "fromUserName");
            return (Criteria) this;
        }

        public Criteria andFromUserNameNotLike(String value) {
            addCriterion("from_user_name not like", value, "fromUserName");
            return (Criteria) this;
        }

        public Criteria andFromUserNameIn(List<String> values) {
            addCriterion("from_user_name in", values, "fromUserName");
            return (Criteria) this;
        }

        public Criteria andFromUserNameNotIn(List<String> values) {
            addCriterion("from_user_name not in", values, "fromUserName");
            return (Criteria) this;
        }

        public Criteria andFromUserNameBetween(String value1, String value2) {
            addCriterion("from_user_name between", value1, value2, "fromUserName");
            return (Criteria) this;
        }

        public Criteria andFromUserNameNotBetween(String value1, String value2) {
            addCriterion("from_user_name not between", value1, value2, "fromUserName");
            return (Criteria) this;
        }

        public Criteria andFromUserMobileIsNull() {
            addCriterion("from_user_mobile is null");
            return (Criteria) this;
        }

        public Criteria andFromUserMobileIsNotNull() {
            addCriterion("from_user_mobile is not null");
            return (Criteria) this;
        }

        public Criteria andFromUserMobileEqualTo(String value) {
            addCriterion("from_user_mobile =", value, "fromUserMobile");
            return (Criteria) this;
        }

        public Criteria andFromUserMobileNotEqualTo(String value) {
            addCriterion("from_user_mobile <>", value, "fromUserMobile");
            return (Criteria) this;
        }

        public Criteria andFromUserMobileGreaterThan(String value) {
            addCriterion("from_user_mobile >", value, "fromUserMobile");
            return (Criteria) this;
        }

        public Criteria andFromUserMobileGreaterThanOrEqualTo(String value) {
            addCriterion("from_user_mobile >=", value, "fromUserMobile");
            return (Criteria) this;
        }

        public Criteria andFromUserMobileLessThan(String value) {
            addCriterion("from_user_mobile <", value, "fromUserMobile");
            return (Criteria) this;
        }

        public Criteria andFromUserMobileLessThanOrEqualTo(String value) {
            addCriterion("from_user_mobile <=", value, "fromUserMobile");
            return (Criteria) this;
        }

        public Criteria andFromUserMobileLike(String value) {
            addCriterion("from_user_mobile like", value, "fromUserMobile");
            return (Criteria) this;
        }

        public Criteria andFromUserMobileNotLike(String value) {
            addCriterion("from_user_mobile not like", value, "fromUserMobile");
            return (Criteria) this;
        }

        public Criteria andFromUserMobileIn(List<String> values) {
            addCriterion("from_user_mobile in", values, "fromUserMobile");
            return (Criteria) this;
        }

        public Criteria andFromUserMobileNotIn(List<String> values) {
            addCriterion("from_user_mobile not in", values, "fromUserMobile");
            return (Criteria) this;
        }

        public Criteria andFromUserMobileBetween(String value1, String value2) {
            addCriterion("from_user_mobile between", value1, value2, "fromUserMobile");
            return (Criteria) this;
        }

        public Criteria andFromUserMobileNotBetween(String value1, String value2) {
            addCriterion("from_user_mobile not between", value1, value2, "fromUserMobile");
            return (Criteria) this;
        }

        public Criteria andFromCompanyCodeIsNull() {
            addCriterion("from_company_code is null");
            return (Criteria) this;
        }

        public Criteria andFromCompanyCodeIsNotNull() {
            addCriterion("from_company_code is not null");
            return (Criteria) this;
        }

        public Criteria andFromCompanyCodeEqualTo(String value) {
            addCriterion("from_company_code =", value, "fromCompanyCode");
            return (Criteria) this;
        }

        public Criteria andFromCompanyCodeNotEqualTo(String value) {
            addCriterion("from_company_code <>", value, "fromCompanyCode");
            return (Criteria) this;
        }

        public Criteria andFromCompanyCodeGreaterThan(String value) {
            addCriterion("from_company_code >", value, "fromCompanyCode");
            return (Criteria) this;
        }

        public Criteria andFromCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("from_company_code >=", value, "fromCompanyCode");
            return (Criteria) this;
        }

        public Criteria andFromCompanyCodeLessThan(String value) {
            addCriterion("from_company_code <", value, "fromCompanyCode");
            return (Criteria) this;
        }

        public Criteria andFromCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("from_company_code <=", value, "fromCompanyCode");
            return (Criteria) this;
        }

        public Criteria andFromCompanyCodeLike(String value) {
            addCriterion("from_company_code like", value, "fromCompanyCode");
            return (Criteria) this;
        }

        public Criteria andFromCompanyCodeNotLike(String value) {
            addCriterion("from_company_code not like", value, "fromCompanyCode");
            return (Criteria) this;
        }

        public Criteria andFromCompanyCodeIn(List<String> values) {
            addCriterion("from_company_code in", values, "fromCompanyCode");
            return (Criteria) this;
        }

        public Criteria andFromCompanyCodeNotIn(List<String> values) {
            addCriterion("from_company_code not in", values, "fromCompanyCode");
            return (Criteria) this;
        }

        public Criteria andFromCompanyCodeBetween(String value1, String value2) {
            addCriterion("from_company_code between", value1, value2, "fromCompanyCode");
            return (Criteria) this;
        }

        public Criteria andFromCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("from_company_code not between", value1, value2, "fromCompanyCode");
            return (Criteria) this;
        }

        public Criteria andToUserCodeIsNull() {
            addCriterion("to_user_code is null");
            return (Criteria) this;
        }

        public Criteria andToUserCodeIsNotNull() {
            addCriterion("to_user_code is not null");
            return (Criteria) this;
        }

        public Criteria andToUserCodeEqualTo(String value) {
            addCriterion("to_user_code =", value, "toUserCode");
            return (Criteria) this;
        }

        public Criteria andToUserCodeNotEqualTo(String value) {
            addCriterion("to_user_code <>", value, "toUserCode");
            return (Criteria) this;
        }

        public Criteria andToUserCodeGreaterThan(String value) {
            addCriterion("to_user_code >", value, "toUserCode");
            return (Criteria) this;
        }

        public Criteria andToUserCodeGreaterThanOrEqualTo(String value) {
            addCriterion("to_user_code >=", value, "toUserCode");
            return (Criteria) this;
        }

        public Criteria andToUserCodeLessThan(String value) {
            addCriterion("to_user_code <", value, "toUserCode");
            return (Criteria) this;
        }

        public Criteria andToUserCodeLessThanOrEqualTo(String value) {
            addCriterion("to_user_code <=", value, "toUserCode");
            return (Criteria) this;
        }

        public Criteria andToUserCodeLike(String value) {
            addCriterion("to_user_code like", value, "toUserCode");
            return (Criteria) this;
        }

        public Criteria andToUserCodeNotLike(String value) {
            addCriterion("to_user_code not like", value, "toUserCode");
            return (Criteria) this;
        }

        public Criteria andToUserCodeIn(List<String> values) {
            addCriterion("to_user_code in", values, "toUserCode");
            return (Criteria) this;
        }

        public Criteria andToUserCodeNotIn(List<String> values) {
            addCriterion("to_user_code not in", values, "toUserCode");
            return (Criteria) this;
        }

        public Criteria andToUserCodeBetween(String value1, String value2) {
            addCriterion("to_user_code between", value1, value2, "toUserCode");
            return (Criteria) this;
        }

        public Criteria andToUserCodeNotBetween(String value1, String value2) {
            addCriterion("to_user_code not between", value1, value2, "toUserCode");
            return (Criteria) this;
        }

        public Criteria andToUserNameIsNull() {
            addCriterion("to_user_name is null");
            return (Criteria) this;
        }

        public Criteria andToUserNameIsNotNull() {
            addCriterion("to_user_name is not null");
            return (Criteria) this;
        }

        public Criteria andToUserNameEqualTo(String value) {
            addCriterion("to_user_name =", value, "toUserName");
            return (Criteria) this;
        }

        public Criteria andToUserNameNotEqualTo(String value) {
            addCriterion("to_user_name <>", value, "toUserName");
            return (Criteria) this;
        }

        public Criteria andToUserNameGreaterThan(String value) {
            addCriterion("to_user_name >", value, "toUserName");
            return (Criteria) this;
        }

        public Criteria andToUserNameGreaterThanOrEqualTo(String value) {
            addCriterion("to_user_name >=", value, "toUserName");
            return (Criteria) this;
        }

        public Criteria andToUserNameLessThan(String value) {
            addCriterion("to_user_name <", value, "toUserName");
            return (Criteria) this;
        }

        public Criteria andToUserNameLessThanOrEqualTo(String value) {
            addCriterion("to_user_name <=", value, "toUserName");
            return (Criteria) this;
        }

        public Criteria andToUserNameLike(String value) {
            addCriterion("to_user_name like", value, "toUserName");
            return (Criteria) this;
        }

        public Criteria andToUserNameNotLike(String value) {
            addCriterion("to_user_name not like", value, "toUserName");
            return (Criteria) this;
        }

        public Criteria andToUserNameIn(List<String> values) {
            addCriterion("to_user_name in", values, "toUserName");
            return (Criteria) this;
        }

        public Criteria andToUserNameNotIn(List<String> values) {
            addCriterion("to_user_name not in", values, "toUserName");
            return (Criteria) this;
        }

        public Criteria andToUserNameBetween(String value1, String value2) {
            addCriterion("to_user_name between", value1, value2, "toUserName");
            return (Criteria) this;
        }

        public Criteria andToUserNameNotBetween(String value1, String value2) {
            addCriterion("to_user_name not between", value1, value2, "toUserName");
            return (Criteria) this;
        }

        public Criteria andToUserMobileIsNull() {
            addCriterion("to_user_mobile is null");
            return (Criteria) this;
        }

        public Criteria andToUserMobileIsNotNull() {
            addCriterion("to_user_mobile is not null");
            return (Criteria) this;
        }

        public Criteria andToUserMobileEqualTo(String value) {
            addCriterion("to_user_mobile =", value, "toUserMobile");
            return (Criteria) this;
        }

        public Criteria andToUserMobileNotEqualTo(String value) {
            addCriterion("to_user_mobile <>", value, "toUserMobile");
            return (Criteria) this;
        }

        public Criteria andToUserMobileGreaterThan(String value) {
            addCriterion("to_user_mobile >", value, "toUserMobile");
            return (Criteria) this;
        }

        public Criteria andToUserMobileGreaterThanOrEqualTo(String value) {
            addCriterion("to_user_mobile >=", value, "toUserMobile");
            return (Criteria) this;
        }

        public Criteria andToUserMobileLessThan(String value) {
            addCriterion("to_user_mobile <", value, "toUserMobile");
            return (Criteria) this;
        }

        public Criteria andToUserMobileLessThanOrEqualTo(String value) {
            addCriterion("to_user_mobile <=", value, "toUserMobile");
            return (Criteria) this;
        }

        public Criteria andToUserMobileLike(String value) {
            addCriterion("to_user_mobile like", value, "toUserMobile");
            return (Criteria) this;
        }

        public Criteria andToUserMobileNotLike(String value) {
            addCriterion("to_user_mobile not like", value, "toUserMobile");
            return (Criteria) this;
        }

        public Criteria andToUserMobileIn(List<String> values) {
            addCriterion("to_user_mobile in", values, "toUserMobile");
            return (Criteria) this;
        }

        public Criteria andToUserMobileNotIn(List<String> values) {
            addCriterion("to_user_mobile not in", values, "toUserMobile");
            return (Criteria) this;
        }

        public Criteria andToUserMobileBetween(String value1, String value2) {
            addCriterion("to_user_mobile between", value1, value2, "toUserMobile");
            return (Criteria) this;
        }

        public Criteria andToUserMobileNotBetween(String value1, String value2) {
            addCriterion("to_user_mobile not between", value1, value2, "toUserMobile");
            return (Criteria) this;
        }

        public Criteria andToCompanyCodeIsNull() {
            addCriterion("to_company_code is null");
            return (Criteria) this;
        }

        public Criteria andToCompanyCodeIsNotNull() {
            addCriterion("to_company_code is not null");
            return (Criteria) this;
        }

        public Criteria andToCompanyCodeEqualTo(String value) {
            addCriterion("to_company_code =", value, "toCompanyCode");
            return (Criteria) this;
        }

        public Criteria andToCompanyCodeNotEqualTo(String value) {
            addCriterion("to_company_code <>", value, "toCompanyCode");
            return (Criteria) this;
        }

        public Criteria andToCompanyCodeGreaterThan(String value) {
            addCriterion("to_company_code >", value, "toCompanyCode");
            return (Criteria) this;
        }

        public Criteria andToCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("to_company_code >=", value, "toCompanyCode");
            return (Criteria) this;
        }

        public Criteria andToCompanyCodeLessThan(String value) {
            addCriterion("to_company_code <", value, "toCompanyCode");
            return (Criteria) this;
        }

        public Criteria andToCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("to_company_code <=", value, "toCompanyCode");
            return (Criteria) this;
        }

        public Criteria andToCompanyCodeLike(String value) {
            addCriterion("to_company_code like", value, "toCompanyCode");
            return (Criteria) this;
        }

        public Criteria andToCompanyCodeNotLike(String value) {
            addCriterion("to_company_code not like", value, "toCompanyCode");
            return (Criteria) this;
        }

        public Criteria andToCompanyCodeIn(List<String> values) {
            addCriterion("to_company_code in", values, "toCompanyCode");
            return (Criteria) this;
        }

        public Criteria andToCompanyCodeNotIn(List<String> values) {
            addCriterion("to_company_code not in", values, "toCompanyCode");
            return (Criteria) this;
        }

        public Criteria andToCompanyCodeBetween(String value1, String value2) {
            addCriterion("to_company_code between", value1, value2, "toCompanyCode");
            return (Criteria) this;
        }

        public Criteria andToCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("to_company_code not between", value1, value2, "toCompanyCode");
            return (Criteria) this;
        }

        public Criteria andMessageTypeIsNull() {
            addCriterion("message_type is null");
            return (Criteria) this;
        }

        public Criteria andMessageTypeIsNotNull() {
            addCriterion("message_type is not null");
            return (Criteria) this;
        }

        public Criteria andMessageTypeEqualTo(Byte value) {
            addCriterion("message_type =", value, "messageType");
            return (Criteria) this;
        }

        public Criteria andMessageTypeNotEqualTo(Byte value) {
            addCriterion("message_type <>", value, "messageType");
            return (Criteria) this;
        }

        public Criteria andMessageTypeGreaterThan(Byte value) {
            addCriterion("message_type >", value, "messageType");
            return (Criteria) this;
        }

        public Criteria andMessageTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("message_type >=", value, "messageType");
            return (Criteria) this;
        }

        public Criteria andMessageTypeLessThan(Byte value) {
            addCriterion("message_type <", value, "messageType");
            return (Criteria) this;
        }

        public Criteria andMessageTypeLessThanOrEqualTo(Byte value) {
            addCriterion("message_type <=", value, "messageType");
            return (Criteria) this;
        }

        public Criteria andMessageTypeIn(List<Byte> values) {
            addCriterion("message_type in", values, "messageType");
            return (Criteria) this;
        }

        public Criteria andMessageTypeNotIn(List<Byte> values) {
            addCriterion("message_type not in", values, "messageType");
            return (Criteria) this;
        }

        public Criteria andMessageTypeBetween(Byte value1, Byte value2) {
            addCriterion("message_type between", value1, value2, "messageType");
            return (Criteria) this;
        }

        public Criteria andMessageTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("message_type not between", value1, value2, "messageType");
            return (Criteria) this;
        }

        public Criteria andMessageTimeIsNull() {
            addCriterion("message_time is null");
            return (Criteria) this;
        }

        public Criteria andMessageTimeIsNotNull() {
            addCriterion("message_time is not null");
            return (Criteria) this;
        }

        public Criteria andMessageTimeEqualTo(Date value) {
            addCriterion("message_time =", value, "messageTime");
            return (Criteria) this;
        }

        public Criteria andMessageTimeNotEqualTo(Date value) {
            addCriterion("message_time <>", value, "messageTime");
            return (Criteria) this;
        }

        public Criteria andMessageTimeGreaterThan(Date value) {
            addCriterion("message_time >", value, "messageTime");
            return (Criteria) this;
        }

        public Criteria andMessageTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("message_time >=", value, "messageTime");
            return (Criteria) this;
        }

        public Criteria andMessageTimeLessThan(Date value) {
            addCriterion("message_time <", value, "messageTime");
            return (Criteria) this;
        }

        public Criteria andMessageTimeLessThanOrEqualTo(Date value) {
            addCriterion("message_time <=", value, "messageTime");
            return (Criteria) this;
        }

        public Criteria andMessageTimeIn(List<Date> values) {
            addCriterion("message_time in", values, "messageTime");
            return (Criteria) this;
        }

        public Criteria andMessageTimeNotIn(List<Date> values) {
            addCriterion("message_time not in", values, "messageTime");
            return (Criteria) this;
        }

        public Criteria andMessageTimeBetween(Date value1, Date value2) {
            addCriterion("message_time between", value1, value2, "messageTime");
            return (Criteria) this;
        }

        public Criteria andMessageTimeNotBetween(Date value1, Date value2) {
            addCriterion("message_time not between", value1, value2, "messageTime");
            return (Criteria) this;
        }

        public Criteria andRiskLabelIsNull() {
            addCriterion("risk_label is null");
            return (Criteria) this;
        }

        public Criteria andRiskLabelIsNotNull() {
            addCriterion("risk_label is not null");
            return (Criteria) this;
        }

        public Criteria andRiskLabelEqualTo(String value) {
            addCriterion("risk_label =", value, "riskLabel");
            return (Criteria) this;
        }

        public Criteria andRiskLabelNotEqualTo(String value) {
            addCriterion("risk_label <>", value, "riskLabel");
            return (Criteria) this;
        }

        public Criteria andRiskLabelGreaterThan(String value) {
            addCriterion("risk_label >", value, "riskLabel");
            return (Criteria) this;
        }

        public Criteria andRiskLabelGreaterThanOrEqualTo(String value) {
            addCriterion("risk_label >=", value, "riskLabel");
            return (Criteria) this;
        }

        public Criteria andRiskLabelLessThan(String value) {
            addCriterion("risk_label <", value, "riskLabel");
            return (Criteria) this;
        }

        public Criteria andRiskLabelLessThanOrEqualTo(String value) {
            addCriterion("risk_label <=", value, "riskLabel");
            return (Criteria) this;
        }

        public Criteria andRiskLabelLike(String value) {
            addCriterion("risk_label like", value, "riskLabel");
            return (Criteria) this;
        }

        public Criteria andRiskLabelNotLike(String value) {
            addCriterion("risk_label not like", value, "riskLabel");
            return (Criteria) this;
        }

        public Criteria andRiskLabelIn(List<String> values) {
            addCriterion("risk_label in", values, "riskLabel");
            return (Criteria) this;
        }

        public Criteria andRiskLabelNotIn(List<String> values) {
            addCriterion("risk_label not in", values, "riskLabel");
            return (Criteria) this;
        }

        public Criteria andRiskLabelBetween(String value1, String value2) {
            addCriterion("risk_label between", value1, value2, "riskLabel");
            return (Criteria) this;
        }

        public Criteria andRiskLabelNotBetween(String value1, String value2) {
            addCriterion("risk_label not between", value1, value2, "riskLabel");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}