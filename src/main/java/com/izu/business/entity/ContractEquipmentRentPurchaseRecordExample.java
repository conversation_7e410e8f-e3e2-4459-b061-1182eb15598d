package com.izu.business.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class ContractEquipmentRentPurchaseRecordExample {
    /**
     * contract_equipment_rent_purchase_record
     */
    protected String orderByClause;

    /**
     * contract_equipment_rent_purchase_record
     */
    protected boolean distinct;

    /**
     * contract_equipment_rent_purchase_record
     */
    protected List<Criteria> oredCriteria;

    public ContractEquipmentRentPurchaseRecordExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeIsNull() {
            addCriterion("customer_code is null");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeIsNotNull() {
            addCriterion("customer_code is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeEqualTo(String value) {
            addCriterion("customer_code =", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotEqualTo(String value) {
            addCriterion("customer_code <>", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeGreaterThan(String value) {
            addCriterion("customer_code >", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeGreaterThanOrEqualTo(String value) {
            addCriterion("customer_code >=", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeLessThan(String value) {
            addCriterion("customer_code <", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeLessThanOrEqualTo(String value) {
            addCriterion("customer_code <=", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeLike(String value) {
            addCriterion("customer_code like", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotLike(String value) {
            addCriterion("customer_code not like", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeIn(List<String> values) {
            addCriterion("customer_code in", values, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotIn(List<String> values) {
            addCriterion("customer_code not in", values, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeBetween(String value1, String value2) {
            addCriterion("customer_code between", value1, value2, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotBetween(String value1, String value2) {
            addCriterion("customer_code not between", value1, value2, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNull() {
            addCriterion("customer_name is null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNotNull() {
            addCriterion("customer_name is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameEqualTo(String value) {
            addCriterion("customer_name =", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotEqualTo(String value) {
            addCriterion("customer_name <>", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThan(String value) {
            addCriterion("customer_name >", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThanOrEqualTo(String value) {
            addCriterion("customer_name >=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThan(String value) {
            addCriterion("customer_name <", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThanOrEqualTo(String value) {
            addCriterion("customer_name <=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLike(String value) {
            addCriterion("customer_name like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotLike(String value) {
            addCriterion("customer_name not like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIn(List<String> values) {
            addCriterion("customer_name in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotIn(List<String> values) {
            addCriterion("customer_name not in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameBetween(String value1, String value2) {
            addCriterion("customer_name between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotBetween(String value1, String value2) {
            addCriterion("customer_name not between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andContractCodeIsNull() {
            addCriterion("contract_code is null");
            return (Criteria) this;
        }

        public Criteria andContractCodeIsNotNull() {
            addCriterion("contract_code is not null");
            return (Criteria) this;
        }

        public Criteria andContractCodeEqualTo(String value) {
            addCriterion("contract_code =", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotEqualTo(String value) {
            addCriterion("contract_code <>", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeGreaterThan(String value) {
            addCriterion("contract_code >", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeGreaterThanOrEqualTo(String value) {
            addCriterion("contract_code >=", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeLessThan(String value) {
            addCriterion("contract_code <", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeLessThanOrEqualTo(String value) {
            addCriterion("contract_code <=", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeLike(String value) {
            addCriterion("contract_code like", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotLike(String value) {
            addCriterion("contract_code not like", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeIn(List<String> values) {
            addCriterion("contract_code in", values, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotIn(List<String> values) {
            addCriterion("contract_code not in", values, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeBetween(String value1, String value2) {
            addCriterion("contract_code between", value1, value2, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotBetween(String value1, String value2) {
            addCriterion("contract_code not between", value1, value2, "contractCode");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIsNull() {
            addCriterion("category_name is null");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIsNotNull() {
            addCriterion("category_name is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryNameEqualTo(String value) {
            addCriterion("category_name =", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotEqualTo(String value) {
            addCriterion("category_name <>", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameGreaterThan(String value) {
            addCriterion("category_name >", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("category_name >=", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLessThan(String value) {
            addCriterion("category_name <", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLessThanOrEqualTo(String value) {
            addCriterion("category_name <=", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameLike(String value) {
            addCriterion("category_name like", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotLike(String value) {
            addCriterion("category_name not like", value, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameIn(List<String> values) {
            addCriterion("category_name in", values, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotIn(List<String> values) {
            addCriterion("category_name not in", values, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameBetween(String value1, String value2) {
            addCriterion("category_name between", value1, value2, "categoryName");
            return (Criteria) this;
        }

        public Criteria andCategoryNameNotBetween(String value1, String value2) {
            addCriterion("category_name not between", value1, value2, "categoryName");
            return (Criteria) this;
        }

        public Criteria andGoodsDefineNameIsNull() {
            addCriterion("goods_define_name is null");
            return (Criteria) this;
        }

        public Criteria andGoodsDefineNameIsNotNull() {
            addCriterion("goods_define_name is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsDefineNameEqualTo(String value) {
            addCriterion("goods_define_name =", value, "goodsDefineName");
            return (Criteria) this;
        }

        public Criteria andGoodsDefineNameNotEqualTo(String value) {
            addCriterion("goods_define_name <>", value, "goodsDefineName");
            return (Criteria) this;
        }

        public Criteria andGoodsDefineNameGreaterThan(String value) {
            addCriterion("goods_define_name >", value, "goodsDefineName");
            return (Criteria) this;
        }

        public Criteria andGoodsDefineNameGreaterThanOrEqualTo(String value) {
            addCriterion("goods_define_name >=", value, "goodsDefineName");
            return (Criteria) this;
        }

        public Criteria andGoodsDefineNameLessThan(String value) {
            addCriterion("goods_define_name <", value, "goodsDefineName");
            return (Criteria) this;
        }

        public Criteria andGoodsDefineNameLessThanOrEqualTo(String value) {
            addCriterion("goods_define_name <=", value, "goodsDefineName");
            return (Criteria) this;
        }

        public Criteria andGoodsDefineNameLike(String value) {
            addCriterion("goods_define_name like", value, "goodsDefineName");
            return (Criteria) this;
        }

        public Criteria andGoodsDefineNameNotLike(String value) {
            addCriterion("goods_define_name not like", value, "goodsDefineName");
            return (Criteria) this;
        }

        public Criteria andGoodsDefineNameIn(List<String> values) {
            addCriterion("goods_define_name in", values, "goodsDefineName");
            return (Criteria) this;
        }

        public Criteria andGoodsDefineNameNotIn(List<String> values) {
            addCriterion("goods_define_name not in", values, "goodsDefineName");
            return (Criteria) this;
        }

        public Criteria andGoodsDefineNameBetween(String value1, String value2) {
            addCriterion("goods_define_name between", value1, value2, "goodsDefineName");
            return (Criteria) this;
        }

        public Criteria andGoodsDefineNameNotBetween(String value1, String value2) {
            addCriterion("goods_define_name not between", value1, value2, "goodsDefineName");
            return (Criteria) this;
        }

        public Criteria andRentPurchaseTypeIsNull() {
            addCriterion("rent_purchase_type is null");
            return (Criteria) this;
        }

        public Criteria andRentPurchaseTypeIsNotNull() {
            addCriterion("rent_purchase_type is not null");
            return (Criteria) this;
        }

        public Criteria andRentPurchaseTypeEqualTo(Integer value) {
            addCriterion("rent_purchase_type =", value, "rentPurchaseType");
            return (Criteria) this;
        }

        public Criteria andRentPurchaseTypeNotEqualTo(Integer value) {
            addCriterion("rent_purchase_type <>", value, "rentPurchaseType");
            return (Criteria) this;
        }

        public Criteria andRentPurchaseTypeGreaterThan(Integer value) {
            addCriterion("rent_purchase_type >", value, "rentPurchaseType");
            return (Criteria) this;
        }

        public Criteria andRentPurchaseTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("rent_purchase_type >=", value, "rentPurchaseType");
            return (Criteria) this;
        }

        public Criteria andRentPurchaseTypeLessThan(Integer value) {
            addCriterion("rent_purchase_type <", value, "rentPurchaseType");
            return (Criteria) this;
        }

        public Criteria andRentPurchaseTypeLessThanOrEqualTo(Integer value) {
            addCriterion("rent_purchase_type <=", value, "rentPurchaseType");
            return (Criteria) this;
        }

        public Criteria andRentPurchaseTypeIn(List<Integer> values) {
            addCriterion("rent_purchase_type in", values, "rentPurchaseType");
            return (Criteria) this;
        }

        public Criteria andRentPurchaseTypeNotIn(List<Integer> values) {
            addCriterion("rent_purchase_type not in", values, "rentPurchaseType");
            return (Criteria) this;
        }

        public Criteria andRentPurchaseTypeBetween(Integer value1, Integer value2) {
            addCriterion("rent_purchase_type between", value1, value2, "rentPurchaseType");
            return (Criteria) this;
        }

        public Criteria andRentPurchaseTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("rent_purchase_type not between", value1, value2, "rentPurchaseType");
            return (Criteria) this;
        }

        public Criteria andQuantityIsNull() {
            addCriterion("quantity is null");
            return (Criteria) this;
        }

        public Criteria andQuantityIsNotNull() {
            addCriterion("quantity is not null");
            return (Criteria) this;
        }

        public Criteria andQuantityEqualTo(Integer value) {
            addCriterion("quantity =", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotEqualTo(Integer value) {
            addCriterion("quantity <>", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThan(Integer value) {
            addCriterion("quantity >", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityGreaterThanOrEqualTo(Integer value) {
            addCriterion("quantity >=", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLessThan(Integer value) {
            addCriterion("quantity <", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityLessThanOrEqualTo(Integer value) {
            addCriterion("quantity <=", value, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityIn(List<Integer> values) {
            addCriterion("quantity in", values, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotIn(List<Integer> values) {
            addCriterion("quantity not in", values, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityBetween(Integer value1, Integer value2) {
            addCriterion("quantity between", value1, value2, "quantity");
            return (Criteria) this;
        }

        public Criteria andQuantityNotBetween(Integer value1, Integer value2) {
            addCriterion("quantity not between", value1, value2, "quantity");
            return (Criteria) this;
        }

        public Criteria andUnitPriceIsNull() {
            addCriterion("unit_price is null");
            return (Criteria) this;
        }

        public Criteria andUnitPriceIsNotNull() {
            addCriterion("unit_price is not null");
            return (Criteria) this;
        }

        public Criteria andUnitPriceEqualTo(BigDecimal value) {
            addCriterion("unit_price =", value, "unitPrice");
            return (Criteria) this;
        }

        public Criteria andUnitPriceNotEqualTo(BigDecimal value) {
            addCriterion("unit_price <>", value, "unitPrice");
            return (Criteria) this;
        }

        public Criteria andUnitPriceGreaterThan(BigDecimal value) {
            addCriterion("unit_price >", value, "unitPrice");
            return (Criteria) this;
        }

        public Criteria andUnitPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("unit_price >=", value, "unitPrice");
            return (Criteria) this;
        }

        public Criteria andUnitPriceLessThan(BigDecimal value) {
            addCriterion("unit_price <", value, "unitPrice");
            return (Criteria) this;
        }

        public Criteria andUnitPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("unit_price <=", value, "unitPrice");
            return (Criteria) this;
        }

        public Criteria andUnitPriceIn(List<BigDecimal> values) {
            addCriterion("unit_price in", values, "unitPrice");
            return (Criteria) this;
        }

        public Criteria andUnitPriceNotIn(List<BigDecimal> values) {
            addCriterion("unit_price not in", values, "unitPrice");
            return (Criteria) this;
        }

        public Criteria andUnitPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("unit_price between", value1, value2, "unitPrice");
            return (Criteria) this;
        }

        public Criteria andUnitPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("unit_price not between", value1, value2, "unitPrice");
            return (Criteria) this;
        }

        public Criteria andEquipmentInstallationFeeIsNull() {
            addCriterion("equipment_installation_fee is null");
            return (Criteria) this;
        }

        public Criteria andEquipmentInstallationFeeIsNotNull() {
            addCriterion("equipment_installation_fee is not null");
            return (Criteria) this;
        }

        public Criteria andEquipmentInstallationFeeEqualTo(BigDecimal value) {
            addCriterion("equipment_installation_fee =", value, "equipmentInstallationFee");
            return (Criteria) this;
        }

        public Criteria andEquipmentInstallationFeeNotEqualTo(BigDecimal value) {
            addCriterion("equipment_installation_fee <>", value, "equipmentInstallationFee");
            return (Criteria) this;
        }

        public Criteria andEquipmentInstallationFeeGreaterThan(BigDecimal value) {
            addCriterion("equipment_installation_fee >", value, "equipmentInstallationFee");
            return (Criteria) this;
        }

        public Criteria andEquipmentInstallationFeeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("equipment_installation_fee >=", value, "equipmentInstallationFee");
            return (Criteria) this;
        }

        public Criteria andEquipmentInstallationFeeLessThan(BigDecimal value) {
            addCriterion("equipment_installation_fee <", value, "equipmentInstallationFee");
            return (Criteria) this;
        }

        public Criteria andEquipmentInstallationFeeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("equipment_installation_fee <=", value, "equipmentInstallationFee");
            return (Criteria) this;
        }

        public Criteria andEquipmentInstallationFeeIn(List<BigDecimal> values) {
            addCriterion("equipment_installation_fee in", values, "equipmentInstallationFee");
            return (Criteria) this;
        }

        public Criteria andEquipmentInstallationFeeNotIn(List<BigDecimal> values) {
            addCriterion("equipment_installation_fee not in", values, "equipmentInstallationFee");
            return (Criteria) this;
        }

        public Criteria andEquipmentInstallationFeeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("equipment_installation_fee between", value1, value2, "equipmentInstallationFee");
            return (Criteria) this;
        }

        public Criteria andEquipmentInstallationFeeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("equipment_installation_fee not between", value1, value2, "equipmentInstallationFee");
            return (Criteria) this;
        }

        public Criteria andEquipmentDisassemblyFeeIsNull() {
            addCriterion("equipment_disassembly_fee is null");
            return (Criteria) this;
        }

        public Criteria andEquipmentDisassemblyFeeIsNotNull() {
            addCriterion("equipment_disassembly_fee is not null");
            return (Criteria) this;
        }

        public Criteria andEquipmentDisassemblyFeeEqualTo(BigDecimal value) {
            addCriterion("equipment_disassembly_fee =", value, "equipmentDisassemblyFee");
            return (Criteria) this;
        }

        public Criteria andEquipmentDisassemblyFeeNotEqualTo(BigDecimal value) {
            addCriterion("equipment_disassembly_fee <>", value, "equipmentDisassemblyFee");
            return (Criteria) this;
        }

        public Criteria andEquipmentDisassemblyFeeGreaterThan(BigDecimal value) {
            addCriterion("equipment_disassembly_fee >", value, "equipmentDisassemblyFee");
            return (Criteria) this;
        }

        public Criteria andEquipmentDisassemblyFeeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("equipment_disassembly_fee >=", value, "equipmentDisassemblyFee");
            return (Criteria) this;
        }

        public Criteria andEquipmentDisassemblyFeeLessThan(BigDecimal value) {
            addCriterion("equipment_disassembly_fee <", value, "equipmentDisassemblyFee");
            return (Criteria) this;
        }

        public Criteria andEquipmentDisassemblyFeeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("equipment_disassembly_fee <=", value, "equipmentDisassemblyFee");
            return (Criteria) this;
        }

        public Criteria andEquipmentDisassemblyFeeIn(List<BigDecimal> values) {
            addCriterion("equipment_disassembly_fee in", values, "equipmentDisassemblyFee");
            return (Criteria) this;
        }

        public Criteria andEquipmentDisassemblyFeeNotIn(List<BigDecimal> values) {
            addCriterion("equipment_disassembly_fee not in", values, "equipmentDisassemblyFee");
            return (Criteria) this;
        }

        public Criteria andEquipmentDisassemblyFeeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("equipment_disassembly_fee between", value1, value2, "equipmentDisassemblyFee");
            return (Criteria) this;
        }

        public Criteria andEquipmentDisassemblyFeeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("equipment_disassembly_fee not between", value1, value2, "equipmentDisassemblyFee");
            return (Criteria) this;
        }

        public Criteria andEquipmentTrafficFeeIsNull() {
            addCriterion("equipment_traffic_fee is null");
            return (Criteria) this;
        }

        public Criteria andEquipmentTrafficFeeIsNotNull() {
            addCriterion("equipment_traffic_fee is not null");
            return (Criteria) this;
        }

        public Criteria andEquipmentTrafficFeeEqualTo(BigDecimal value) {
            addCriterion("equipment_traffic_fee =", value, "equipmentTrafficFee");
            return (Criteria) this;
        }

        public Criteria andEquipmentTrafficFeeNotEqualTo(BigDecimal value) {
            addCriterion("equipment_traffic_fee <>", value, "equipmentTrafficFee");
            return (Criteria) this;
        }

        public Criteria andEquipmentTrafficFeeGreaterThan(BigDecimal value) {
            addCriterion("equipment_traffic_fee >", value, "equipmentTrafficFee");
            return (Criteria) this;
        }

        public Criteria andEquipmentTrafficFeeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("equipment_traffic_fee >=", value, "equipmentTrafficFee");
            return (Criteria) this;
        }

        public Criteria andEquipmentTrafficFeeLessThan(BigDecimal value) {
            addCriterion("equipment_traffic_fee <", value, "equipmentTrafficFee");
            return (Criteria) this;
        }

        public Criteria andEquipmentTrafficFeeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("equipment_traffic_fee <=", value, "equipmentTrafficFee");
            return (Criteria) this;
        }

        public Criteria andEquipmentTrafficFeeIn(List<BigDecimal> values) {
            addCriterion("equipment_traffic_fee in", values, "equipmentTrafficFee");
            return (Criteria) this;
        }

        public Criteria andEquipmentTrafficFeeNotIn(List<BigDecimal> values) {
            addCriterion("equipment_traffic_fee not in", values, "equipmentTrafficFee");
            return (Criteria) this;
        }

        public Criteria andEquipmentTrafficFeeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("equipment_traffic_fee between", value1, value2, "equipmentTrafficFee");
            return (Criteria) this;
        }

        public Criteria andEquipmentTrafficFeeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("equipment_traffic_fee not between", value1, value2, "equipmentTrafficFee");
            return (Criteria) this;
        }

        public Criteria andTotalFeeIsNull() {
            addCriterion("total_fee is null");
            return (Criteria) this;
        }

        public Criteria andTotalFeeIsNotNull() {
            addCriterion("total_fee is not null");
            return (Criteria) this;
        }

        public Criteria andTotalFeeEqualTo(BigDecimal value) {
            addCriterion("total_fee =", value, "totalFee");
            return (Criteria) this;
        }

        public Criteria andTotalFeeNotEqualTo(BigDecimal value) {
            addCriterion("total_fee <>", value, "totalFee");
            return (Criteria) this;
        }

        public Criteria andTotalFeeGreaterThan(BigDecimal value) {
            addCriterion("total_fee >", value, "totalFee");
            return (Criteria) this;
        }

        public Criteria andTotalFeeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_fee >=", value, "totalFee");
            return (Criteria) this;
        }

        public Criteria andTotalFeeLessThan(BigDecimal value) {
            addCriterion("total_fee <", value, "totalFee");
            return (Criteria) this;
        }

        public Criteria andTotalFeeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_fee <=", value, "totalFee");
            return (Criteria) this;
        }

        public Criteria andTotalFeeIn(List<BigDecimal> values) {
            addCriterion("total_fee in", values, "totalFee");
            return (Criteria) this;
        }

        public Criteria andTotalFeeNotIn(List<BigDecimal> values) {
            addCriterion("total_fee not in", values, "totalFee");
            return (Criteria) this;
        }

        public Criteria andTotalFeeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_fee between", value1, value2, "totalFee");
            return (Criteria) this;
        }

        public Criteria andTotalFeeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_fee not between", value1, value2, "totalFee");
            return (Criteria) this;
        }

        public Criteria andLeaseStartDateIsNull() {
            addCriterion("lease_start_date is null");
            return (Criteria) this;
        }

        public Criteria andLeaseStartDateIsNotNull() {
            addCriterion("lease_start_date is not null");
            return (Criteria) this;
        }

        public Criteria andLeaseStartDateEqualTo(Date value) {
            addCriterionForJDBCDate("lease_start_date =", value, "leaseStartDate");
            return (Criteria) this;
        }

        public Criteria andLeaseStartDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("lease_start_date <>", value, "leaseStartDate");
            return (Criteria) this;
        }

        public Criteria andLeaseStartDateGreaterThan(Date value) {
            addCriterionForJDBCDate("lease_start_date >", value, "leaseStartDate");
            return (Criteria) this;
        }

        public Criteria andLeaseStartDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("lease_start_date >=", value, "leaseStartDate");
            return (Criteria) this;
        }

        public Criteria andLeaseStartDateLessThan(Date value) {
            addCriterionForJDBCDate("lease_start_date <", value, "leaseStartDate");
            return (Criteria) this;
        }

        public Criteria andLeaseStartDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("lease_start_date <=", value, "leaseStartDate");
            return (Criteria) this;
        }

        public Criteria andLeaseStartDateIn(List<Date> values) {
            addCriterionForJDBCDate("lease_start_date in", values, "leaseStartDate");
            return (Criteria) this;
        }

        public Criteria andLeaseStartDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("lease_start_date not in", values, "leaseStartDate");
            return (Criteria) this;
        }

        public Criteria andLeaseStartDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("lease_start_date between", value1, value2, "leaseStartDate");
            return (Criteria) this;
        }

        public Criteria andLeaseStartDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("lease_start_date not between", value1, value2, "leaseStartDate");
            return (Criteria) this;
        }

        public Criteria andLeaseEndDateIsNull() {
            addCriterion("lease_end_date is null");
            return (Criteria) this;
        }

        public Criteria andLeaseEndDateIsNotNull() {
            addCriterion("lease_end_date is not null");
            return (Criteria) this;
        }

        public Criteria andLeaseEndDateEqualTo(Date value) {
            addCriterionForJDBCDate("lease_end_date =", value, "leaseEndDate");
            return (Criteria) this;
        }

        public Criteria andLeaseEndDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("lease_end_date <>", value, "leaseEndDate");
            return (Criteria) this;
        }

        public Criteria andLeaseEndDateGreaterThan(Date value) {
            addCriterionForJDBCDate("lease_end_date >", value, "leaseEndDate");
            return (Criteria) this;
        }

        public Criteria andLeaseEndDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("lease_end_date >=", value, "leaseEndDate");
            return (Criteria) this;
        }

        public Criteria andLeaseEndDateLessThan(Date value) {
            addCriterionForJDBCDate("lease_end_date <", value, "leaseEndDate");
            return (Criteria) this;
        }

        public Criteria andLeaseEndDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("lease_end_date <=", value, "leaseEndDate");
            return (Criteria) this;
        }

        public Criteria andLeaseEndDateIn(List<Date> values) {
            addCriterionForJDBCDate("lease_end_date in", values, "leaseEndDate");
            return (Criteria) this;
        }

        public Criteria andLeaseEndDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("lease_end_date not in", values, "leaseEndDate");
            return (Criteria) this;
        }

        public Criteria andLeaseEndDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("lease_end_date between", value1, value2, "leaseEndDate");
            return (Criteria) this;
        }

        public Criteria andLeaseEndDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("lease_end_date not between", value1, value2, "leaseEndDate");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateNameIsNull() {
            addCriterion("create_name is null");
            return (Criteria) this;
        }

        public Criteria andCreateNameIsNotNull() {
            addCriterion("create_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreateNameEqualTo(String value) {
            addCriterion("create_name =", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameNotEqualTo(String value) {
            addCriterion("create_name <>", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameGreaterThan(String value) {
            addCriterion("create_name >", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameGreaterThanOrEqualTo(String value) {
            addCriterion("create_name >=", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameLessThan(String value) {
            addCriterion("create_name <", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameLessThanOrEqualTo(String value) {
            addCriterion("create_name <=", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameLike(String value) {
            addCriterion("create_name like", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameNotLike(String value) {
            addCriterion("create_name not like", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameIn(List<String> values) {
            addCriterion("create_name in", values, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameNotIn(List<String> values) {
            addCriterion("create_name not in", values, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameBetween(String value1, String value2) {
            addCriterion("create_name between", value1, value2, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameNotBetween(String value1, String value2) {
            addCriterion("create_name not between", value1, value2, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateIdIsNull() {
            addCriterion("create_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateIdIsNotNull() {
            addCriterion("create_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateIdEqualTo(String value) {
            addCriterion("create_id =", value, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateIdNotEqualTo(String value) {
            addCriterion("create_id <>", value, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateIdGreaterThan(String value) {
            addCriterion("create_id >", value, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateIdGreaterThanOrEqualTo(String value) {
            addCriterion("create_id >=", value, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateIdLessThan(String value) {
            addCriterion("create_id <", value, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateIdLessThanOrEqualTo(String value) {
            addCriterion("create_id <=", value, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateIdLike(String value) {
            addCriterion("create_id like", value, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateIdNotLike(String value) {
            addCriterion("create_id not like", value, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateIdIn(List<String> values) {
            addCriterion("create_id in", values, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateIdNotIn(List<String> values) {
            addCriterion("create_id not in", values, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateIdBetween(String value1, String value2) {
            addCriterion("create_id between", value1, value2, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateIdNotBetween(String value1, String value2) {
            addCriterion("create_id not between", value1, value2, "createId");
            return (Criteria) this;
        }

        public Criteria andFinanceStructCodeIsNull() {
            addCriterion("finance_struct_code is null");
            return (Criteria) this;
        }

        public Criteria andFinanceStructCodeIsNotNull() {
            addCriterion("finance_struct_code is not null");
            return (Criteria) this;
        }

        public Criteria andFinanceStructCodeEqualTo(String value) {
            addCriterion("finance_struct_code =", value, "financeStructCode");
            return (Criteria) this;
        }

        public Criteria andFinanceStructCodeNotEqualTo(String value) {
            addCriterion("finance_struct_code <>", value, "financeStructCode");
            return (Criteria) this;
        }

        public Criteria andFinanceStructCodeGreaterThan(String value) {
            addCriterion("finance_struct_code >", value, "financeStructCode");
            return (Criteria) this;
        }

        public Criteria andFinanceStructCodeGreaterThanOrEqualTo(String value) {
            addCriterion("finance_struct_code >=", value, "financeStructCode");
            return (Criteria) this;
        }

        public Criteria andFinanceStructCodeLessThan(String value) {
            addCriterion("finance_struct_code <", value, "financeStructCode");
            return (Criteria) this;
        }

        public Criteria andFinanceStructCodeLessThanOrEqualTo(String value) {
            addCriterion("finance_struct_code <=", value, "financeStructCode");
            return (Criteria) this;
        }

        public Criteria andFinanceStructCodeLike(String value) {
            addCriterion("finance_struct_code like", value, "financeStructCode");
            return (Criteria) this;
        }

        public Criteria andFinanceStructCodeNotLike(String value) {
            addCriterion("finance_struct_code not like", value, "financeStructCode");
            return (Criteria) this;
        }

        public Criteria andFinanceStructCodeIn(List<String> values) {
            addCriterion("finance_struct_code in", values, "financeStructCode");
            return (Criteria) this;
        }

        public Criteria andFinanceStructCodeNotIn(List<String> values) {
            addCriterion("finance_struct_code not in", values, "financeStructCode");
            return (Criteria) this;
        }

        public Criteria andFinanceStructCodeBetween(String value1, String value2) {
            addCriterion("finance_struct_code between", value1, value2, "financeStructCode");
            return (Criteria) this;
        }

        public Criteria andFinanceStructCodeNotBetween(String value1, String value2) {
            addCriterion("finance_struct_code not between", value1, value2, "financeStructCode");
            return (Criteria) this;
        }

        public Criteria andFinanceStructNameIsNull() {
            addCriterion("finance_struct_name is null");
            return (Criteria) this;
        }

        public Criteria andFinanceStructNameIsNotNull() {
            addCriterion("finance_struct_name is not null");
            return (Criteria) this;
        }

        public Criteria andFinanceStructNameEqualTo(String value) {
            addCriterion("finance_struct_name =", value, "financeStructName");
            return (Criteria) this;
        }

        public Criteria andFinanceStructNameNotEqualTo(String value) {
            addCriterion("finance_struct_name <>", value, "financeStructName");
            return (Criteria) this;
        }

        public Criteria andFinanceStructNameGreaterThan(String value) {
            addCriterion("finance_struct_name >", value, "financeStructName");
            return (Criteria) this;
        }

        public Criteria andFinanceStructNameGreaterThanOrEqualTo(String value) {
            addCriterion("finance_struct_name >=", value, "financeStructName");
            return (Criteria) this;
        }

        public Criteria andFinanceStructNameLessThan(String value) {
            addCriterion("finance_struct_name <", value, "financeStructName");
            return (Criteria) this;
        }

        public Criteria andFinanceStructNameLessThanOrEqualTo(String value) {
            addCriterion("finance_struct_name <=", value, "financeStructName");
            return (Criteria) this;
        }

        public Criteria andFinanceStructNameLike(String value) {
            addCriterion("finance_struct_name like", value, "financeStructName");
            return (Criteria) this;
        }

        public Criteria andFinanceStructNameNotLike(String value) {
            addCriterion("finance_struct_name not like", value, "financeStructName");
            return (Criteria) this;
        }

        public Criteria andFinanceStructNameIn(List<String> values) {
            addCriterion("finance_struct_name in", values, "financeStructName");
            return (Criteria) this;
        }

        public Criteria andFinanceStructNameNotIn(List<String> values) {
            addCriterion("finance_struct_name not in", values, "financeStructName");
            return (Criteria) this;
        }

        public Criteria andFinanceStructNameBetween(String value1, String value2) {
            addCriterion("finance_struct_name between", value1, value2, "financeStructName");
            return (Criteria) this;
        }

        public Criteria andFinanceStructNameNotBetween(String value1, String value2) {
            addCriterion("finance_struct_name not between", value1, value2, "financeStructName");
            return (Criteria) this;
        }

        public Criteria andOperateCodeIsNull() {
            addCriterion("operate_code is null");
            return (Criteria) this;
        }

        public Criteria andOperateCodeIsNotNull() {
            addCriterion("operate_code is not null");
            return (Criteria) this;
        }

        public Criteria andOperateCodeEqualTo(String value) {
            addCriterion("operate_code =", value, "operateCode");
            return (Criteria) this;
        }

        public Criteria andOperateCodeNotEqualTo(String value) {
            addCriterion("operate_code <>", value, "operateCode");
            return (Criteria) this;
        }

        public Criteria andOperateCodeGreaterThan(String value) {
            addCriterion("operate_code >", value, "operateCode");
            return (Criteria) this;
        }

        public Criteria andOperateCodeGreaterThanOrEqualTo(String value) {
            addCriterion("operate_code >=", value, "operateCode");
            return (Criteria) this;
        }

        public Criteria andOperateCodeLessThan(String value) {
            addCriterion("operate_code <", value, "operateCode");
            return (Criteria) this;
        }

        public Criteria andOperateCodeLessThanOrEqualTo(String value) {
            addCriterion("operate_code <=", value, "operateCode");
            return (Criteria) this;
        }

        public Criteria andOperateCodeLike(String value) {
            addCriterion("operate_code like", value, "operateCode");
            return (Criteria) this;
        }

        public Criteria andOperateCodeNotLike(String value) {
            addCriterion("operate_code not like", value, "operateCode");
            return (Criteria) this;
        }

        public Criteria andOperateCodeIn(List<String> values) {
            addCriterion("operate_code in", values, "operateCode");
            return (Criteria) this;
        }

        public Criteria andOperateCodeNotIn(List<String> values) {
            addCriterion("operate_code not in", values, "operateCode");
            return (Criteria) this;
        }

        public Criteria andOperateCodeBetween(String value1, String value2) {
            addCriterion("operate_code between", value1, value2, "operateCode");
            return (Criteria) this;
        }

        public Criteria andOperateCodeNotBetween(String value1, String value2) {
            addCriterion("operate_code not between", value1, value2, "operateCode");
            return (Criteria) this;
        }

        public Criteria andOperateNameIsNull() {
            addCriterion("operate_name is null");
            return (Criteria) this;
        }

        public Criteria andOperateNameIsNotNull() {
            addCriterion("operate_name is not null");
            return (Criteria) this;
        }

        public Criteria andOperateNameEqualTo(String value) {
            addCriterion("operate_name =", value, "operateName");
            return (Criteria) this;
        }

        public Criteria andOperateNameNotEqualTo(String value) {
            addCriterion("operate_name <>", value, "operateName");
            return (Criteria) this;
        }

        public Criteria andOperateNameGreaterThan(String value) {
            addCriterion("operate_name >", value, "operateName");
            return (Criteria) this;
        }

        public Criteria andOperateNameGreaterThanOrEqualTo(String value) {
            addCriterion("operate_name >=", value, "operateName");
            return (Criteria) this;
        }

        public Criteria andOperateNameLessThan(String value) {
            addCriterion("operate_name <", value, "operateName");
            return (Criteria) this;
        }

        public Criteria andOperateNameLessThanOrEqualTo(String value) {
            addCriterion("operate_name <=", value, "operateName");
            return (Criteria) this;
        }

        public Criteria andOperateNameLike(String value) {
            addCriterion("operate_name like", value, "operateName");
            return (Criteria) this;
        }

        public Criteria andOperateNameNotLike(String value) {
            addCriterion("operate_name not like", value, "operateName");
            return (Criteria) this;
        }

        public Criteria andOperateNameIn(List<String> values) {
            addCriterion("operate_name in", values, "operateName");
            return (Criteria) this;
        }

        public Criteria andOperateNameNotIn(List<String> values) {
            addCriterion("operate_name not in", values, "operateName");
            return (Criteria) this;
        }

        public Criteria andOperateNameBetween(String value1, String value2) {
            addCriterion("operate_name between", value1, value2, "operateName");
            return (Criteria) this;
        }

        public Criteria andOperateNameNotBetween(String value1, String value2) {
            addCriterion("operate_name not between", value1, value2, "operateName");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeIsNull() {
            addCriterion("category_code is null");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeIsNotNull() {
            addCriterion("category_code is not null");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeEqualTo(String value) {
            addCriterion("category_code =", value, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeNotEqualTo(String value) {
            addCriterion("category_code <>", value, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeGreaterThan(String value) {
            addCriterion("category_code >", value, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeGreaterThanOrEqualTo(String value) {
            addCriterion("category_code >=", value, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeLessThan(String value) {
            addCriterion("category_code <", value, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeLessThanOrEqualTo(String value) {
            addCriterion("category_code <=", value, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeLike(String value) {
            addCriterion("category_code like", value, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeNotLike(String value) {
            addCriterion("category_code not like", value, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeIn(List<String> values) {
            addCriterion("category_code in", values, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeNotIn(List<String> values) {
            addCriterion("category_code not in", values, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeBetween(String value1, String value2) {
            addCriterion("category_code between", value1, value2, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andCategoryCodeNotBetween(String value1, String value2) {
            addCriterion("category_code not between", value1, value2, "categoryCode");
            return (Criteria) this;
        }

        public Criteria andGoodsDefineSerialNumIsNull() {
            addCriterion("goods_define_serial_num is null");
            return (Criteria) this;
        }

        public Criteria andGoodsDefineSerialNumIsNotNull() {
            addCriterion("goods_define_serial_num is not null");
            return (Criteria) this;
        }

        public Criteria andGoodsDefineSerialNumEqualTo(String value) {
            addCriterion("goods_define_serial_num =", value, "goodsDefineSerialNum");
            return (Criteria) this;
        }

        public Criteria andGoodsDefineSerialNumNotEqualTo(String value) {
            addCriterion("goods_define_serial_num <>", value, "goodsDefineSerialNum");
            return (Criteria) this;
        }

        public Criteria andGoodsDefineSerialNumGreaterThan(String value) {
            addCriterion("goods_define_serial_num >", value, "goodsDefineSerialNum");
            return (Criteria) this;
        }

        public Criteria andGoodsDefineSerialNumGreaterThanOrEqualTo(String value) {
            addCriterion("goods_define_serial_num >=", value, "goodsDefineSerialNum");
            return (Criteria) this;
        }

        public Criteria andGoodsDefineSerialNumLessThan(String value) {
            addCriterion("goods_define_serial_num <", value, "goodsDefineSerialNum");
            return (Criteria) this;
        }

        public Criteria andGoodsDefineSerialNumLessThanOrEqualTo(String value) {
            addCriterion("goods_define_serial_num <=", value, "goodsDefineSerialNum");
            return (Criteria) this;
        }

        public Criteria andGoodsDefineSerialNumLike(String value) {
            addCriterion("goods_define_serial_num like", value, "goodsDefineSerialNum");
            return (Criteria) this;
        }

        public Criteria andGoodsDefineSerialNumNotLike(String value) {
            addCriterion("goods_define_serial_num not like", value, "goodsDefineSerialNum");
            return (Criteria) this;
        }

        public Criteria andGoodsDefineSerialNumIn(List<String> values) {
            addCriterion("goods_define_serial_num in", values, "goodsDefineSerialNum");
            return (Criteria) this;
        }

        public Criteria andGoodsDefineSerialNumNotIn(List<String> values) {
            addCriterion("goods_define_serial_num not in", values, "goodsDefineSerialNum");
            return (Criteria) this;
        }

        public Criteria andGoodsDefineSerialNumBetween(String value1, String value2) {
            addCriterion("goods_define_serial_num between", value1, value2, "goodsDefineSerialNum");
            return (Criteria) this;
        }

        public Criteria andGoodsDefineSerialNumNotBetween(String value1, String value2) {
            addCriterion("goods_define_serial_num not between", value1, value2, "goodsDefineSerialNum");
            return (Criteria) this;
        }

        public Criteria andSignSalesIdIsNull() {
            addCriterion("sign_sales_id is null");
            return (Criteria) this;
        }

        public Criteria andSignSalesIdIsNotNull() {
            addCriterion("sign_sales_id is not null");
            return (Criteria) this;
        }

        public Criteria andSignSalesIdEqualTo(String value) {
            addCriterion("sign_sales_id =", value, "signSalesId");
            return (Criteria) this;
        }

        public Criteria andSignSalesIdNotEqualTo(String value) {
            addCriterion("sign_sales_id <>", value, "signSalesId");
            return (Criteria) this;
        }

        public Criteria andSignSalesIdGreaterThan(String value) {
            addCriterion("sign_sales_id >", value, "signSalesId");
            return (Criteria) this;
        }

        public Criteria andSignSalesIdGreaterThanOrEqualTo(String value) {
            addCriterion("sign_sales_id >=", value, "signSalesId");
            return (Criteria) this;
        }

        public Criteria andSignSalesIdLessThan(String value) {
            addCriterion("sign_sales_id <", value, "signSalesId");
            return (Criteria) this;
        }

        public Criteria andSignSalesIdLessThanOrEqualTo(String value) {
            addCriterion("sign_sales_id <=", value, "signSalesId");
            return (Criteria) this;
        }

        public Criteria andSignSalesIdLike(String value) {
            addCriterion("sign_sales_id like", value, "signSalesId");
            return (Criteria) this;
        }

        public Criteria andSignSalesIdNotLike(String value) {
            addCriterion("sign_sales_id not like", value, "signSalesId");
            return (Criteria) this;
        }

        public Criteria andSignSalesIdIn(List<String> values) {
            addCriterion("sign_sales_id in", values, "signSalesId");
            return (Criteria) this;
        }

        public Criteria andSignSalesIdNotIn(List<String> values) {
            addCriterion("sign_sales_id not in", values, "signSalesId");
            return (Criteria) this;
        }

        public Criteria andSignSalesIdBetween(String value1, String value2) {
            addCriterion("sign_sales_id between", value1, value2, "signSalesId");
            return (Criteria) this;
        }

        public Criteria andSignSalesIdNotBetween(String value1, String value2) {
            addCriterion("sign_sales_id not between", value1, value2, "signSalesId");
            return (Criteria) this;
        }

        public Criteria andSignSalesNameIsNull() {
            addCriterion("sign_sales_name is null");
            return (Criteria) this;
        }

        public Criteria andSignSalesNameIsNotNull() {
            addCriterion("sign_sales_name is not null");
            return (Criteria) this;
        }

        public Criteria andSignSalesNameEqualTo(String value) {
            addCriterion("sign_sales_name =", value, "signSalesName");
            return (Criteria) this;
        }

        public Criteria andSignSalesNameNotEqualTo(String value) {
            addCriterion("sign_sales_name <>", value, "signSalesName");
            return (Criteria) this;
        }

        public Criteria andSignSalesNameGreaterThan(String value) {
            addCriterion("sign_sales_name >", value, "signSalesName");
            return (Criteria) this;
        }

        public Criteria andSignSalesNameGreaterThanOrEqualTo(String value) {
            addCriterion("sign_sales_name >=", value, "signSalesName");
            return (Criteria) this;
        }

        public Criteria andSignSalesNameLessThan(String value) {
            addCriterion("sign_sales_name <", value, "signSalesName");
            return (Criteria) this;
        }

        public Criteria andSignSalesNameLessThanOrEqualTo(String value) {
            addCriterion("sign_sales_name <=", value, "signSalesName");
            return (Criteria) this;
        }

        public Criteria andSignSalesNameLike(String value) {
            addCriterion("sign_sales_name like", value, "signSalesName");
            return (Criteria) this;
        }

        public Criteria andSignSalesNameNotLike(String value) {
            addCriterion("sign_sales_name not like", value, "signSalesName");
            return (Criteria) this;
        }

        public Criteria andSignSalesNameIn(List<String> values) {
            addCriterion("sign_sales_name in", values, "signSalesName");
            return (Criteria) this;
        }

        public Criteria andSignSalesNameNotIn(List<String> values) {
            addCriterion("sign_sales_name not in", values, "signSalesName");
            return (Criteria) this;
        }

        public Criteria andSignSalesNameBetween(String value1, String value2) {
            addCriterion("sign_sales_name between", value1, value2, "signSalesName");
            return (Criteria) this;
        }

        public Criteria andSignSalesNameNotBetween(String value1, String value2) {
            addCriterion("sign_sales_name not between", value1, value2, "signSalesName");
            return (Criteria) this;
        }

        public Criteria andSignDateIsNull() {
            addCriterion("sign_date is null");
            return (Criteria) this;
        }

        public Criteria andSignDateIsNotNull() {
            addCriterion("sign_date is not null");
            return (Criteria) this;
        }

        public Criteria andSignDateEqualTo(Date value) {
            addCriterion("sign_date =", value, "signDate");
            return (Criteria) this;
        }

        public Criteria andSignDateNotEqualTo(Date value) {
            addCriterion("sign_date <>", value, "signDate");
            return (Criteria) this;
        }

        public Criteria andSignDateGreaterThan(Date value) {
            addCriterion("sign_date >", value, "signDate");
            return (Criteria) this;
        }

        public Criteria andSignDateGreaterThanOrEqualTo(Date value) {
            addCriterion("sign_date >=", value, "signDate");
            return (Criteria) this;
        }

        public Criteria andSignDateLessThan(Date value) {
            addCriterion("sign_date <", value, "signDate");
            return (Criteria) this;
        }

        public Criteria andSignDateLessThanOrEqualTo(Date value) {
            addCriterion("sign_date <=", value, "signDate");
            return (Criteria) this;
        }

        public Criteria andSignDateIn(List<Date> values) {
            addCriterion("sign_date in", values, "signDate");
            return (Criteria) this;
        }

        public Criteria andSignDateNotIn(List<Date> values) {
            addCriterion("sign_date not in", values, "signDate");
            return (Criteria) this;
        }

        public Criteria andSignDateBetween(Date value1, Date value2) {
            addCriterion("sign_date between", value1, value2, "signDate");
            return (Criteria) this;
        }

        public Criteria andSignDateNotBetween(Date value1, Date value2) {
            addCriterion("sign_date not between", value1, value2, "signDate");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}