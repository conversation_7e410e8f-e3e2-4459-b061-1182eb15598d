package com.izu.business.entity;

import java.math.BigDecimal;
import java.util.Date;

public class StatisServiceTypeDay {
    /**
     * 主键 id
     */
    private Integer id;

    /**
     * 统计日期 （格式：yyyy-MM-dd） statis_date
     */
    private String statisDate;

    /**
     * 所属城市编码 city_code
     */
    private Integer cityCode;

    /**
     * 城市名称 city_name
     */
    private String cityName;

    /**
     * 服务类型ID service_id
     */
    private Integer serviceId;

    /**
     * 服务编码 service_code
     */
    private String serviceCode;

    /**
     * 完成订单个数 completed_order_num
     */
    private Integer completedOrderNum;

    /**
     * 完成订单比例 completed_order_rate
     */
    private BigDecimal completedOrderRate;

    /**
     * 创建时间 create_time
     */
    private Date createTime;

    /**
     * 修改时间 update_time
     */
    private Date updateTime;

    /**
     * 主键
     * <AUTHOR>
     * @date 2020-01-14 11:54:13
     * @return id 
     */
    public Integer getId() {
        return id;
    }

    /**
     * 主键
     * <AUTHOR>
     * @date 2020-01-14 11:54:13
     * @param id 
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 统计日期 （格式：yyyy-MM-dd）
     * <AUTHOR>
     * @date 2020-01-14 11:54:13
     * @return statis_date 
     */
    public String getStatisDate() {
        return statisDate;
    }

    /**
     * 统计日期 （格式：yyyy-MM-dd）
     * <AUTHOR>
     * @date 2020-01-14 11:54:13
     * @param statisDate 
     */
    public void setStatisDate(String statisDate) {
        this.statisDate = statisDate == null ? null : statisDate.trim();
    }

    /**
     * 所属城市编码
     * <AUTHOR>
     * @date 2020-01-14 11:54:13
     * @return city_code 
     */
    public Integer getCityCode() {
        return cityCode;
    }

    /**
     * 所属城市编码
     * <AUTHOR>
     * @date 2020-01-14 11:54:13
     * @param cityCode 
     */
    public void setCityCode(Integer cityCode) {
        this.cityCode = cityCode;
    }

    /**
     * 城市名称
     * <AUTHOR>
     * @date 2020-01-14 11:54:13
     * @return city_name 
     */
    public String getCityName() {
        return cityName;
    }

    /**
     * 城市名称
     * <AUTHOR>
     * @date 2020-01-14 11:54:13
     * @param cityName 
     */
    public void setCityName(String cityName) {
        this.cityName = cityName == null ? null : cityName.trim();
    }

    /**
     * 服务类型ID
     * <AUTHOR>
     * @date 2020-01-14 11:54:13
     * @return service_id 
     */
    public Integer getServiceId() {
        return serviceId;
    }

    /**
     * 服务类型ID
     * <AUTHOR>
     * @date 2020-01-14 11:54:13
     * @param serviceId 
     */
    public void setServiceId(Integer serviceId) {
        this.serviceId = serviceId;
    }

    /**
     * 服务编码
     * <AUTHOR>
     * @date 2020-01-14 11:54:13
     * @return service_code 
     */
    public String getServiceCode() {
        return serviceCode;
    }

    /**
     * 服务编码
     * <AUTHOR>
     * @date 2020-01-14 11:54:13
     * @param serviceCode 
     */
    public void setServiceCode(String serviceCode) {
        this.serviceCode = serviceCode == null ? null : serviceCode.trim();
    }

    /**
     * 完成订单个数
     * <AUTHOR>
     * @date 2020-01-14 11:54:13
     * @return completed_order_num 
     */
    public Integer getCompletedOrderNum() {
        return completedOrderNum;
    }

    /**
     * 完成订单个数
     * <AUTHOR>
     * @date 2020-01-14 11:54:13
     * @param completedOrderNum 
     */
    public void setCompletedOrderNum(Integer completedOrderNum) {
        this.completedOrderNum = completedOrderNum;
    }

    /**
     * 完成订单比例
     * <AUTHOR>
     * @date 2020-01-14 11:54:13
     * @return completed_order_rate 
     */
    public BigDecimal getCompletedOrderRate() {
        return completedOrderRate;
    }

    /**
     * 完成订单比例
     * <AUTHOR>
     * @date 2020-01-14 11:54:13
     * @param completedOrderRate 
     */
    public void setCompletedOrderRate(BigDecimal completedOrderRate) {
        this.completedOrderRate = completedOrderRate;
    }

    /**
     * 创建时间
     * <AUTHOR>
     * @date 2020-01-14 11:54:13
     * @return create_time 
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     * <AUTHOR>
     * @date 2020-01-14 11:54:13
     * @param createTime 
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 修改时间
     * <AUTHOR>
     * @date 2020-01-14 11:54:13
     * @return update_time 
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 修改时间
     * <AUTHOR>
     * @date 2020-01-14 11:54:13
     * @param updateTime 
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}