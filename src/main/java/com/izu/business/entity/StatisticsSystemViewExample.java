package com.izu.business.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class StatisticsSystemViewExample {
    /**
     * statistics_system_view
     */
    protected String orderByClause;

    /**
     * statistics_system_view
     */
    protected boolean distinct;

    /**
     * statistics_system_view
     */
    protected List<Criteria> oredCriteria;

    public StatisticsSystemViewExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Integer value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Integer value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Integer value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Integer value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Integer value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Integer> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Integer> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Integer value1, Integer value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Integer value1, Integer value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNull() {
            addCriterion("company_code is null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNotNull() {
            addCriterion("company_code is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeEqualTo(String value) {
            addCriterion("company_code =", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotEqualTo(String value) {
            addCriterion("company_code <>", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThan(String value) {
            addCriterion("company_code >", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("company_code >=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThan(String value) {
            addCriterion("company_code <", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("company_code <=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLike(String value) {
            addCriterion("company_code like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotLike(String value) {
            addCriterion("company_code not like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIn(List<String> values) {
            addCriterion("company_code in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotIn(List<String> values) {
            addCriterion("company_code not in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeBetween(String value1, String value2) {
            addCriterion("company_code between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("company_code not between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIsNull() {
            addCriterion("company_name is null");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIsNotNull() {
            addCriterion("company_name is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyNameEqualTo(String value) {
            addCriterion("company_name =", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotEqualTo(String value) {
            addCriterion("company_name <>", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameGreaterThan(String value) {
            addCriterion("company_name >", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameGreaterThanOrEqualTo(String value) {
            addCriterion("company_name >=", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLessThan(String value) {
            addCriterion("company_name <", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLessThanOrEqualTo(String value) {
            addCriterion("company_name <=", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLike(String value) {
            addCriterion("company_name like", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotLike(String value) {
            addCriterion("company_name not like", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIn(List<String> values) {
            addCriterion("company_name in", values, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotIn(List<String> values) {
            addCriterion("company_name not in", values, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameBetween(String value1, String value2) {
            addCriterion("company_name between", value1, value2, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotBetween(String value1, String value2) {
            addCriterion("company_name not between", value1, value2, "companyName");
            return (Criteria) this;
        }

        public Criteria andCustomerPvIsNull() {
            addCriterion("customer_pv is null");
            return (Criteria) this;
        }

        public Criteria andCustomerPvIsNotNull() {
            addCriterion("customer_pv is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerPvEqualTo(Integer value) {
            addCriterion("customer_pv =", value, "customerPv");
            return (Criteria) this;
        }

        public Criteria andCustomerPvNotEqualTo(Integer value) {
            addCriterion("customer_pv <>", value, "customerPv");
            return (Criteria) this;
        }

        public Criteria andCustomerPvGreaterThan(Integer value) {
            addCriterion("customer_pv >", value, "customerPv");
            return (Criteria) this;
        }

        public Criteria andCustomerPvGreaterThanOrEqualTo(Integer value) {
            addCriterion("customer_pv >=", value, "customerPv");
            return (Criteria) this;
        }

        public Criteria andCustomerPvLessThan(Integer value) {
            addCriterion("customer_pv <", value, "customerPv");
            return (Criteria) this;
        }

        public Criteria andCustomerPvLessThanOrEqualTo(Integer value) {
            addCriterion("customer_pv <=", value, "customerPv");
            return (Criteria) this;
        }

        public Criteria andCustomerPvIn(List<Integer> values) {
            addCriterion("customer_pv in", values, "customerPv");
            return (Criteria) this;
        }

        public Criteria andCustomerPvNotIn(List<Integer> values) {
            addCriterion("customer_pv not in", values, "customerPv");
            return (Criteria) this;
        }

        public Criteria andCustomerPvBetween(Integer value1, Integer value2) {
            addCriterion("customer_pv between", value1, value2, "customerPv");
            return (Criteria) this;
        }

        public Criteria andCustomerPvNotBetween(Integer value1, Integer value2) {
            addCriterion("customer_pv not between", value1, value2, "customerPv");
            return (Criteria) this;
        }

        public Criteria andCustomerUvIsNull() {
            addCriterion("customer_uv is null");
            return (Criteria) this;
        }

        public Criteria andCustomerUvIsNotNull() {
            addCriterion("customer_uv is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerUvEqualTo(Integer value) {
            addCriterion("customer_uv =", value, "customerUv");
            return (Criteria) this;
        }

        public Criteria andCustomerUvNotEqualTo(Integer value) {
            addCriterion("customer_uv <>", value, "customerUv");
            return (Criteria) this;
        }

        public Criteria andCustomerUvGreaterThan(Integer value) {
            addCriterion("customer_uv >", value, "customerUv");
            return (Criteria) this;
        }

        public Criteria andCustomerUvGreaterThanOrEqualTo(Integer value) {
            addCriterion("customer_uv >=", value, "customerUv");
            return (Criteria) this;
        }

        public Criteria andCustomerUvLessThan(Integer value) {
            addCriterion("customer_uv <", value, "customerUv");
            return (Criteria) this;
        }

        public Criteria andCustomerUvLessThanOrEqualTo(Integer value) {
            addCriterion("customer_uv <=", value, "customerUv");
            return (Criteria) this;
        }

        public Criteria andCustomerUvIn(List<Integer> values) {
            addCriterion("customer_uv in", values, "customerUv");
            return (Criteria) this;
        }

        public Criteria andCustomerUvNotIn(List<Integer> values) {
            addCriterion("customer_uv not in", values, "customerUv");
            return (Criteria) this;
        }

        public Criteria andCustomerUvBetween(Integer value1, Integer value2) {
            addCriterion("customer_uv between", value1, value2, "customerUv");
            return (Criteria) this;
        }

        public Criteria andCustomerUvNotBetween(Integer value1, Integer value2) {
            addCriterion("customer_uv not between", value1, value2, "customerUv");
            return (Criteria) this;
        }

        public Criteria andWebPvIsNull() {
            addCriterion("web_pv is null");
            return (Criteria) this;
        }

        public Criteria andWebPvIsNotNull() {
            addCriterion("web_pv is not null");
            return (Criteria) this;
        }

        public Criteria andWebPvEqualTo(Integer value) {
            addCriterion("web_pv =", value, "webPv");
            return (Criteria) this;
        }

        public Criteria andWebPvNotEqualTo(Integer value) {
            addCriterion("web_pv <>", value, "webPv");
            return (Criteria) this;
        }

        public Criteria andWebPvGreaterThan(Integer value) {
            addCriterion("web_pv >", value, "webPv");
            return (Criteria) this;
        }

        public Criteria andWebPvGreaterThanOrEqualTo(Integer value) {
            addCriterion("web_pv >=", value, "webPv");
            return (Criteria) this;
        }

        public Criteria andWebPvLessThan(Integer value) {
            addCriterion("web_pv <", value, "webPv");
            return (Criteria) this;
        }

        public Criteria andWebPvLessThanOrEqualTo(Integer value) {
            addCriterion("web_pv <=", value, "webPv");
            return (Criteria) this;
        }

        public Criteria andWebPvIn(List<Integer> values) {
            addCriterion("web_pv in", values, "webPv");
            return (Criteria) this;
        }

        public Criteria andWebPvNotIn(List<Integer> values) {
            addCriterion("web_pv not in", values, "webPv");
            return (Criteria) this;
        }

        public Criteria andWebPvBetween(Integer value1, Integer value2) {
            addCriterion("web_pv between", value1, value2, "webPv");
            return (Criteria) this;
        }

        public Criteria andWebPvNotBetween(Integer value1, Integer value2) {
            addCriterion("web_pv not between", value1, value2, "webPv");
            return (Criteria) this;
        }

        public Criteria andAndroidPvIsNull() {
            addCriterion("android_pv is null");
            return (Criteria) this;
        }

        public Criteria andAndroidPvIsNotNull() {
            addCriterion("android_pv is not null");
            return (Criteria) this;
        }

        public Criteria andAndroidPvEqualTo(Integer value) {
            addCriterion("android_pv =", value, "androidPv");
            return (Criteria) this;
        }

        public Criteria andAndroidPvNotEqualTo(Integer value) {
            addCriterion("android_pv <>", value, "androidPv");
            return (Criteria) this;
        }

        public Criteria andAndroidPvGreaterThan(Integer value) {
            addCriterion("android_pv >", value, "androidPv");
            return (Criteria) this;
        }

        public Criteria andAndroidPvGreaterThanOrEqualTo(Integer value) {
            addCriterion("android_pv >=", value, "androidPv");
            return (Criteria) this;
        }

        public Criteria andAndroidPvLessThan(Integer value) {
            addCriterion("android_pv <", value, "androidPv");
            return (Criteria) this;
        }

        public Criteria andAndroidPvLessThanOrEqualTo(Integer value) {
            addCriterion("android_pv <=", value, "androidPv");
            return (Criteria) this;
        }

        public Criteria andAndroidPvIn(List<Integer> values) {
            addCriterion("android_pv in", values, "androidPv");
            return (Criteria) this;
        }

        public Criteria andAndroidPvNotIn(List<Integer> values) {
            addCriterion("android_pv not in", values, "androidPv");
            return (Criteria) this;
        }

        public Criteria andAndroidPvBetween(Integer value1, Integer value2) {
            addCriterion("android_pv between", value1, value2, "androidPv");
            return (Criteria) this;
        }

        public Criteria andAndroidPvNotBetween(Integer value1, Integer value2) {
            addCriterion("android_pv not between", value1, value2, "androidPv");
            return (Criteria) this;
        }

        public Criteria andIosPvIsNull() {
            addCriterion("ios_pv is null");
            return (Criteria) this;
        }

        public Criteria andIosPvIsNotNull() {
            addCriterion("ios_pv is not null");
            return (Criteria) this;
        }

        public Criteria andIosPvEqualTo(Integer value) {
            addCriterion("ios_pv =", value, "iosPv");
            return (Criteria) this;
        }

        public Criteria andIosPvNotEqualTo(Integer value) {
            addCriterion("ios_pv <>", value, "iosPv");
            return (Criteria) this;
        }

        public Criteria andIosPvGreaterThan(Integer value) {
            addCriterion("ios_pv >", value, "iosPv");
            return (Criteria) this;
        }

        public Criteria andIosPvGreaterThanOrEqualTo(Integer value) {
            addCriterion("ios_pv >=", value, "iosPv");
            return (Criteria) this;
        }

        public Criteria andIosPvLessThan(Integer value) {
            addCriterion("ios_pv <", value, "iosPv");
            return (Criteria) this;
        }

        public Criteria andIosPvLessThanOrEqualTo(Integer value) {
            addCriterion("ios_pv <=", value, "iosPv");
            return (Criteria) this;
        }

        public Criteria andIosPvIn(List<Integer> values) {
            addCriterion("ios_pv in", values, "iosPv");
            return (Criteria) this;
        }

        public Criteria andIosPvNotIn(List<Integer> values) {
            addCriterion("ios_pv not in", values, "iosPv");
            return (Criteria) this;
        }

        public Criteria andIosPvBetween(Integer value1, Integer value2) {
            addCriterion("ios_pv between", value1, value2, "iosPv");
            return (Criteria) this;
        }

        public Criteria andIosPvNotBetween(Integer value1, Integer value2) {
            addCriterion("ios_pv not between", value1, value2, "iosPv");
            return (Criteria) this;
        }

        public Criteria andWebUvIsNull() {
            addCriterion("web_uv is null");
            return (Criteria) this;
        }

        public Criteria andWebUvIsNotNull() {
            addCriterion("web_uv is not null");
            return (Criteria) this;
        }

        public Criteria andWebUvEqualTo(Integer value) {
            addCriterion("web_uv =", value, "webUv");
            return (Criteria) this;
        }

        public Criteria andWebUvNotEqualTo(Integer value) {
            addCriterion("web_uv <>", value, "webUv");
            return (Criteria) this;
        }

        public Criteria andWebUvGreaterThan(Integer value) {
            addCriterion("web_uv >", value, "webUv");
            return (Criteria) this;
        }

        public Criteria andWebUvGreaterThanOrEqualTo(Integer value) {
            addCriterion("web_uv >=", value, "webUv");
            return (Criteria) this;
        }

        public Criteria andWebUvLessThan(Integer value) {
            addCriterion("web_uv <", value, "webUv");
            return (Criteria) this;
        }

        public Criteria andWebUvLessThanOrEqualTo(Integer value) {
            addCriterion("web_uv <=", value, "webUv");
            return (Criteria) this;
        }

        public Criteria andWebUvIn(List<Integer> values) {
            addCriterion("web_uv in", values, "webUv");
            return (Criteria) this;
        }

        public Criteria andWebUvNotIn(List<Integer> values) {
            addCriterion("web_uv not in", values, "webUv");
            return (Criteria) this;
        }

        public Criteria andWebUvBetween(Integer value1, Integer value2) {
            addCriterion("web_uv between", value1, value2, "webUv");
            return (Criteria) this;
        }

        public Criteria andWebUvNotBetween(Integer value1, Integer value2) {
            addCriterion("web_uv not between", value1, value2, "webUv");
            return (Criteria) this;
        }

        public Criteria andAndroidUvIsNull() {
            addCriterion("android_uv is null");
            return (Criteria) this;
        }

        public Criteria andAndroidUvIsNotNull() {
            addCriterion("android_uv is not null");
            return (Criteria) this;
        }

        public Criteria andAndroidUvEqualTo(Integer value) {
            addCriterion("android_uv =", value, "androidUv");
            return (Criteria) this;
        }

        public Criteria andAndroidUvNotEqualTo(Integer value) {
            addCriterion("android_uv <>", value, "androidUv");
            return (Criteria) this;
        }

        public Criteria andAndroidUvGreaterThan(Integer value) {
            addCriterion("android_uv >", value, "androidUv");
            return (Criteria) this;
        }

        public Criteria andAndroidUvGreaterThanOrEqualTo(Integer value) {
            addCriterion("android_uv >=", value, "androidUv");
            return (Criteria) this;
        }

        public Criteria andAndroidUvLessThan(Integer value) {
            addCriterion("android_uv <", value, "androidUv");
            return (Criteria) this;
        }

        public Criteria andAndroidUvLessThanOrEqualTo(Integer value) {
            addCriterion("android_uv <=", value, "androidUv");
            return (Criteria) this;
        }

        public Criteria andAndroidUvIn(List<Integer> values) {
            addCriterion("android_uv in", values, "androidUv");
            return (Criteria) this;
        }

        public Criteria andAndroidUvNotIn(List<Integer> values) {
            addCriterion("android_uv not in", values, "androidUv");
            return (Criteria) this;
        }

        public Criteria andAndroidUvBetween(Integer value1, Integer value2) {
            addCriterion("android_uv between", value1, value2, "androidUv");
            return (Criteria) this;
        }

        public Criteria andAndroidUvNotBetween(Integer value1, Integer value2) {
            addCriterion("android_uv not between", value1, value2, "androidUv");
            return (Criteria) this;
        }

        public Criteria andIosUvIsNull() {
            addCriterion("ios_uv is null");
            return (Criteria) this;
        }

        public Criteria andIosUvIsNotNull() {
            addCriterion("ios_uv is not null");
            return (Criteria) this;
        }

        public Criteria andIosUvEqualTo(Integer value) {
            addCriterion("ios_uv =", value, "iosUv");
            return (Criteria) this;
        }

        public Criteria andIosUvNotEqualTo(Integer value) {
            addCriterion("ios_uv <>", value, "iosUv");
            return (Criteria) this;
        }

        public Criteria andIosUvGreaterThan(Integer value) {
            addCriterion("ios_uv >", value, "iosUv");
            return (Criteria) this;
        }

        public Criteria andIosUvGreaterThanOrEqualTo(Integer value) {
            addCriterion("ios_uv >=", value, "iosUv");
            return (Criteria) this;
        }

        public Criteria andIosUvLessThan(Integer value) {
            addCriterion("ios_uv <", value, "iosUv");
            return (Criteria) this;
        }

        public Criteria andIosUvLessThanOrEqualTo(Integer value) {
            addCriterion("ios_uv <=", value, "iosUv");
            return (Criteria) this;
        }

        public Criteria andIosUvIn(List<Integer> values) {
            addCriterion("ios_uv in", values, "iosUv");
            return (Criteria) this;
        }

        public Criteria andIosUvNotIn(List<Integer> values) {
            addCriterion("ios_uv not in", values, "iosUv");
            return (Criteria) this;
        }

        public Criteria andIosUvBetween(Integer value1, Integer value2) {
            addCriterion("ios_uv between", value1, value2, "iosUv");
            return (Criteria) this;
        }

        public Criteria andIosUvNotBetween(Integer value1, Integer value2) {
            addCriterion("ios_uv not between", value1, value2, "iosUv");
            return (Criteria) this;
        }

        public Criteria andStatDateIsNull() {
            addCriterion("stat_date is null");
            return (Criteria) this;
        }

        public Criteria andStatDateIsNotNull() {
            addCriterion("stat_date is not null");
            return (Criteria) this;
        }

        public Criteria andStatDateEqualTo(Date value) {
            addCriterionForJDBCDate("stat_date =", value, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("stat_date <>", value, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateGreaterThan(Date value) {
            addCriterionForJDBCDate("stat_date >", value, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("stat_date >=", value, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateLessThan(Date value) {
            addCriterionForJDBCDate("stat_date <", value, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("stat_date <=", value, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateIn(List<Date> values) {
            addCriterionForJDBCDate("stat_date in", values, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("stat_date not in", values, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("stat_date between", value1, value2, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("stat_date not between", value1, value2, "statDate");
            return (Criteria) this;
        }

        public Criteria andStatWeekIsNull() {
            addCriterion("stat_week is null");
            return (Criteria) this;
        }

        public Criteria andStatWeekIsNotNull() {
            addCriterion("stat_week is not null");
            return (Criteria) this;
        }

        public Criteria andStatWeekEqualTo(Integer value) {
            addCriterion("stat_week =", value, "statWeek");
            return (Criteria) this;
        }

        public Criteria andStatWeekNotEqualTo(Integer value) {
            addCriterion("stat_week <>", value, "statWeek");
            return (Criteria) this;
        }

        public Criteria andStatWeekGreaterThan(Integer value) {
            addCriterion("stat_week >", value, "statWeek");
            return (Criteria) this;
        }

        public Criteria andStatWeekGreaterThanOrEqualTo(Integer value) {
            addCriterion("stat_week >=", value, "statWeek");
            return (Criteria) this;
        }

        public Criteria andStatWeekLessThan(Integer value) {
            addCriterion("stat_week <", value, "statWeek");
            return (Criteria) this;
        }

        public Criteria andStatWeekLessThanOrEqualTo(Integer value) {
            addCriterion("stat_week <=", value, "statWeek");
            return (Criteria) this;
        }

        public Criteria andStatWeekIn(List<Integer> values) {
            addCriterion("stat_week in", values, "statWeek");
            return (Criteria) this;
        }

        public Criteria andStatWeekNotIn(List<Integer> values) {
            addCriterion("stat_week not in", values, "statWeek");
            return (Criteria) this;
        }

        public Criteria andStatWeekBetween(Integer value1, Integer value2) {
            addCriterion("stat_week between", value1, value2, "statWeek");
            return (Criteria) this;
        }

        public Criteria andStatWeekNotBetween(Integer value1, Integer value2) {
            addCriterion("stat_week not between", value1, value2, "statWeek");
            return (Criteria) this;
        }

        public Criteria andStatMonthIsNull() {
            addCriterion("stat_month is null");
            return (Criteria) this;
        }

        public Criteria andStatMonthIsNotNull() {
            addCriterion("stat_month is not null");
            return (Criteria) this;
        }

        public Criteria andStatMonthEqualTo(Integer value) {
            addCriterion("stat_month =", value, "statMonth");
            return (Criteria) this;
        }

        public Criteria andStatMonthNotEqualTo(Integer value) {
            addCriterion("stat_month <>", value, "statMonth");
            return (Criteria) this;
        }

        public Criteria andStatMonthGreaterThan(Integer value) {
            addCriterion("stat_month >", value, "statMonth");
            return (Criteria) this;
        }

        public Criteria andStatMonthGreaterThanOrEqualTo(Integer value) {
            addCriterion("stat_month >=", value, "statMonth");
            return (Criteria) this;
        }

        public Criteria andStatMonthLessThan(Integer value) {
            addCriterion("stat_month <", value, "statMonth");
            return (Criteria) this;
        }

        public Criteria andStatMonthLessThanOrEqualTo(Integer value) {
            addCriterion("stat_month <=", value, "statMonth");
            return (Criteria) this;
        }

        public Criteria andStatMonthIn(List<Integer> values) {
            addCriterion("stat_month in", values, "statMonth");
            return (Criteria) this;
        }

        public Criteria andStatMonthNotIn(List<Integer> values) {
            addCriterion("stat_month not in", values, "statMonth");
            return (Criteria) this;
        }

        public Criteria andStatMonthBetween(Integer value1, Integer value2) {
            addCriterion("stat_month between", value1, value2, "statMonth");
            return (Criteria) this;
        }

        public Criteria andStatMonthNotBetween(Integer value1, Integer value2) {
            addCriterion("stat_month not between", value1, value2, "statMonth");
            return (Criteria) this;
        }

        public Criteria andStatSeasonIsNull() {
            addCriterion("stat_season is null");
            return (Criteria) this;
        }

        public Criteria andStatSeasonIsNotNull() {
            addCriterion("stat_season is not null");
            return (Criteria) this;
        }

        public Criteria andStatSeasonEqualTo(Integer value) {
            addCriterion("stat_season =", value, "statSeason");
            return (Criteria) this;
        }

        public Criteria andStatSeasonNotEqualTo(Integer value) {
            addCriterion("stat_season <>", value, "statSeason");
            return (Criteria) this;
        }

        public Criteria andStatSeasonGreaterThan(Integer value) {
            addCriterion("stat_season >", value, "statSeason");
            return (Criteria) this;
        }

        public Criteria andStatSeasonGreaterThanOrEqualTo(Integer value) {
            addCriterion("stat_season >=", value, "statSeason");
            return (Criteria) this;
        }

        public Criteria andStatSeasonLessThan(Integer value) {
            addCriterion("stat_season <", value, "statSeason");
            return (Criteria) this;
        }

        public Criteria andStatSeasonLessThanOrEqualTo(Integer value) {
            addCriterion("stat_season <=", value, "statSeason");
            return (Criteria) this;
        }

        public Criteria andStatSeasonIn(List<Integer> values) {
            addCriterion("stat_season in", values, "statSeason");
            return (Criteria) this;
        }

        public Criteria andStatSeasonNotIn(List<Integer> values) {
            addCriterion("stat_season not in", values, "statSeason");
            return (Criteria) this;
        }

        public Criteria andStatSeasonBetween(Integer value1, Integer value2) {
            addCriterion("stat_season between", value1, value2, "statSeason");
            return (Criteria) this;
        }

        public Criteria andStatSeasonNotBetween(Integer value1, Integer value2) {
            addCriterion("stat_season not between", value1, value2, "statSeason");
            return (Criteria) this;
        }

        public Criteria andStatYearIsNull() {
            addCriterion("stat_year is null");
            return (Criteria) this;
        }

        public Criteria andStatYearIsNotNull() {
            addCriterion("stat_year is not null");
            return (Criteria) this;
        }

        public Criteria andStatYearEqualTo(Integer value) {
            addCriterion("stat_year =", value, "statYear");
            return (Criteria) this;
        }

        public Criteria andStatYearNotEqualTo(Integer value) {
            addCriterion("stat_year <>", value, "statYear");
            return (Criteria) this;
        }

        public Criteria andStatYearGreaterThan(Integer value) {
            addCriterion("stat_year >", value, "statYear");
            return (Criteria) this;
        }

        public Criteria andStatYearGreaterThanOrEqualTo(Integer value) {
            addCriterion("stat_year >=", value, "statYear");
            return (Criteria) this;
        }

        public Criteria andStatYearLessThan(Integer value) {
            addCriterion("stat_year <", value, "statYear");
            return (Criteria) this;
        }

        public Criteria andStatYearLessThanOrEqualTo(Integer value) {
            addCriterion("stat_year <=", value, "statYear");
            return (Criteria) this;
        }

        public Criteria andStatYearIn(List<Integer> values) {
            addCriterion("stat_year in", values, "statYear");
            return (Criteria) this;
        }

        public Criteria andStatYearNotIn(List<Integer> values) {
            addCriterion("stat_year not in", values, "statYear");
            return (Criteria) this;
        }

        public Criteria andStatYearBetween(Integer value1, Integer value2) {
            addCriterion("stat_year between", value1, value2, "statYear");
            return (Criteria) this;
        }

        public Criteria andStatYearNotBetween(Integer value1, Integer value2) {
            addCriterion("stat_year not between", value1, value2, "statYear");
            return (Criteria) this;
        }

        public Criteria andInternalOrderCountIsNull() {
            addCriterion("internal_order_count is null");
            return (Criteria) this;
        }

        public Criteria andInternalOrderCountIsNotNull() {
            addCriterion("internal_order_count is not null");
            return (Criteria) this;
        }

        public Criteria andInternalOrderCountEqualTo(Integer value) {
            addCriterion("internal_order_count =", value, "internalOrderCount");
            return (Criteria) this;
        }

        public Criteria andInternalOrderCountNotEqualTo(Integer value) {
            addCriterion("internal_order_count <>", value, "internalOrderCount");
            return (Criteria) this;
        }

        public Criteria andInternalOrderCountGreaterThan(Integer value) {
            addCriterion("internal_order_count >", value, "internalOrderCount");
            return (Criteria) this;
        }

        public Criteria andInternalOrderCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("internal_order_count >=", value, "internalOrderCount");
            return (Criteria) this;
        }

        public Criteria andInternalOrderCountLessThan(Integer value) {
            addCriterion("internal_order_count <", value, "internalOrderCount");
            return (Criteria) this;
        }

        public Criteria andInternalOrderCountLessThanOrEqualTo(Integer value) {
            addCriterion("internal_order_count <=", value, "internalOrderCount");
            return (Criteria) this;
        }

        public Criteria andInternalOrderCountIn(List<Integer> values) {
            addCriterion("internal_order_count in", values, "internalOrderCount");
            return (Criteria) this;
        }

        public Criteria andInternalOrderCountNotIn(List<Integer> values) {
            addCriterion("internal_order_count not in", values, "internalOrderCount");
            return (Criteria) this;
        }

        public Criteria andInternalOrderCountBetween(Integer value1, Integer value2) {
            addCriterion("internal_order_count between", value1, value2, "internalOrderCount");
            return (Criteria) this;
        }

        public Criteria andInternalOrderCountNotBetween(Integer value1, Integer value2) {
            addCriterion("internal_order_count not between", value1, value2, "internalOrderCount");
            return (Criteria) this;
        }

        public Criteria andMotorcadeOrderCountIsNull() {
            addCriterion("motorcade_order_count is null");
            return (Criteria) this;
        }

        public Criteria andMotorcadeOrderCountIsNotNull() {
            addCriterion("motorcade_order_count is not null");
            return (Criteria) this;
        }

        public Criteria andMotorcadeOrderCountEqualTo(Integer value) {
            addCriterion("motorcade_order_count =", value, "motorcadeOrderCount");
            return (Criteria) this;
        }

        public Criteria andMotorcadeOrderCountNotEqualTo(Integer value) {
            addCriterion("motorcade_order_count <>", value, "motorcadeOrderCount");
            return (Criteria) this;
        }

        public Criteria andMotorcadeOrderCountGreaterThan(Integer value) {
            addCriterion("motorcade_order_count >", value, "motorcadeOrderCount");
            return (Criteria) this;
        }

        public Criteria andMotorcadeOrderCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("motorcade_order_count >=", value, "motorcadeOrderCount");
            return (Criteria) this;
        }

        public Criteria andMotorcadeOrderCountLessThan(Integer value) {
            addCriterion("motorcade_order_count <", value, "motorcadeOrderCount");
            return (Criteria) this;
        }

        public Criteria andMotorcadeOrderCountLessThanOrEqualTo(Integer value) {
            addCriterion("motorcade_order_count <=", value, "motorcadeOrderCount");
            return (Criteria) this;
        }

        public Criteria andMotorcadeOrderCountIn(List<Integer> values) {
            addCriterion("motorcade_order_count in", values, "motorcadeOrderCount");
            return (Criteria) this;
        }

        public Criteria andMotorcadeOrderCountNotIn(List<Integer> values) {
            addCriterion("motorcade_order_count not in", values, "motorcadeOrderCount");
            return (Criteria) this;
        }

        public Criteria andMotorcadeOrderCountBetween(Integer value1, Integer value2) {
            addCriterion("motorcade_order_count between", value1, value2, "motorcadeOrderCount");
            return (Criteria) this;
        }

        public Criteria andMotorcadeOrderCountNotBetween(Integer value1, Integer value2) {
            addCriterion("motorcade_order_count not between", value1, value2, "motorcadeOrderCount");
            return (Criteria) this;
        }

        public Criteria andPrivateOrderCountIsNull() {
            addCriterion("private_order_count is null");
            return (Criteria) this;
        }

        public Criteria andPrivateOrderCountIsNotNull() {
            addCriterion("private_order_count is not null");
            return (Criteria) this;
        }

        public Criteria andPrivateOrderCountEqualTo(Integer value) {
            addCriterion("private_order_count =", value, "privateOrderCount");
            return (Criteria) this;
        }

        public Criteria andPrivateOrderCountNotEqualTo(Integer value) {
            addCriterion("private_order_count <>", value, "privateOrderCount");
            return (Criteria) this;
        }

        public Criteria andPrivateOrderCountGreaterThan(Integer value) {
            addCriterion("private_order_count >", value, "privateOrderCount");
            return (Criteria) this;
        }

        public Criteria andPrivateOrderCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("private_order_count >=", value, "privateOrderCount");
            return (Criteria) this;
        }

        public Criteria andPrivateOrderCountLessThan(Integer value) {
            addCriterion("private_order_count <", value, "privateOrderCount");
            return (Criteria) this;
        }

        public Criteria andPrivateOrderCountLessThanOrEqualTo(Integer value) {
            addCriterion("private_order_count <=", value, "privateOrderCount");
            return (Criteria) this;
        }

        public Criteria andPrivateOrderCountIn(List<Integer> values) {
            addCriterion("private_order_count in", values, "privateOrderCount");
            return (Criteria) this;
        }

        public Criteria andPrivateOrderCountNotIn(List<Integer> values) {
            addCriterion("private_order_count not in", values, "privateOrderCount");
            return (Criteria) this;
        }

        public Criteria andPrivateOrderCountBetween(Integer value1, Integer value2) {
            addCriterion("private_order_count between", value1, value2, "privateOrderCount");
            return (Criteria) this;
        }

        public Criteria andPrivateOrderCountNotBetween(Integer value1, Integer value2) {
            addCriterion("private_order_count not between", value1, value2, "privateOrderCount");
            return (Criteria) this;
        }

        public Criteria andSoOrderCountIsNull() {
            addCriterion("so_order_count is null");
            return (Criteria) this;
        }

        public Criteria andSoOrderCountIsNotNull() {
            addCriterion("so_order_count is not null");
            return (Criteria) this;
        }

        public Criteria andSoOrderCountEqualTo(Integer value) {
            addCriterion("so_order_count =", value, "soOrderCount");
            return (Criteria) this;
        }

        public Criteria andSoOrderCountNotEqualTo(Integer value) {
            addCriterion("so_order_count <>", value, "soOrderCount");
            return (Criteria) this;
        }

        public Criteria andSoOrderCountGreaterThan(Integer value) {
            addCriterion("so_order_count >", value, "soOrderCount");
            return (Criteria) this;
        }

        public Criteria andSoOrderCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("so_order_count >=", value, "soOrderCount");
            return (Criteria) this;
        }

        public Criteria andSoOrderCountLessThan(Integer value) {
            addCriterion("so_order_count <", value, "soOrderCount");
            return (Criteria) this;
        }

        public Criteria andSoOrderCountLessThanOrEqualTo(Integer value) {
            addCriterion("so_order_count <=", value, "soOrderCount");
            return (Criteria) this;
        }

        public Criteria andSoOrderCountIn(List<Integer> values) {
            addCriterion("so_order_count in", values, "soOrderCount");
            return (Criteria) this;
        }

        public Criteria andSoOrderCountNotIn(List<Integer> values) {
            addCriterion("so_order_count not in", values, "soOrderCount");
            return (Criteria) this;
        }

        public Criteria andSoOrderCountBetween(Integer value1, Integer value2) {
            addCriterion("so_order_count between", value1, value2, "soOrderCount");
            return (Criteria) this;
        }

        public Criteria andSoOrderCountNotBetween(Integer value1, Integer value2) {
            addCriterion("so_order_count not between", value1, value2, "soOrderCount");
            return (Criteria) this;
        }

        public Criteria andCoOrderCountIsNull() {
            addCriterion("co_order_count is null");
            return (Criteria) this;
        }

        public Criteria andCoOrderCountIsNotNull() {
            addCriterion("co_order_count is not null");
            return (Criteria) this;
        }

        public Criteria andCoOrderCountEqualTo(Integer value) {
            addCriterion("co_order_count =", value, "coOrderCount");
            return (Criteria) this;
        }

        public Criteria andCoOrderCountNotEqualTo(Integer value) {
            addCriterion("co_order_count <>", value, "coOrderCount");
            return (Criteria) this;
        }

        public Criteria andCoOrderCountGreaterThan(Integer value) {
            addCriterion("co_order_count >", value, "coOrderCount");
            return (Criteria) this;
        }

        public Criteria andCoOrderCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("co_order_count >=", value, "coOrderCount");
            return (Criteria) this;
        }

        public Criteria andCoOrderCountLessThan(Integer value) {
            addCriterion("co_order_count <", value, "coOrderCount");
            return (Criteria) this;
        }

        public Criteria andCoOrderCountLessThanOrEqualTo(Integer value) {
            addCriterion("co_order_count <=", value, "coOrderCount");
            return (Criteria) this;
        }

        public Criteria andCoOrderCountIn(List<Integer> values) {
            addCriterion("co_order_count in", values, "coOrderCount");
            return (Criteria) this;
        }

        public Criteria andCoOrderCountNotIn(List<Integer> values) {
            addCriterion("co_order_count not in", values, "coOrderCount");
            return (Criteria) this;
        }

        public Criteria andCoOrderCountBetween(Integer value1, Integer value2) {
            addCriterion("co_order_count between", value1, value2, "coOrderCount");
            return (Criteria) this;
        }

        public Criteria andCoOrderCountNotBetween(Integer value1, Integer value2) {
            addCriterion("co_order_count not between", value1, value2, "coOrderCount");
            return (Criteria) this;
        }

        public Criteria andGovPublicOrderCountIsNull() {
            addCriterion("gov_public_order_count is null");
            return (Criteria) this;
        }

        public Criteria andGovPublicOrderCountIsNotNull() {
            addCriterion("gov_public_order_count is not null");
            return (Criteria) this;
        }

        public Criteria andGovPublicOrderCountEqualTo(Integer value) {
            addCriterion("gov_public_order_count =", value, "govPublicOrderCount");
            return (Criteria) this;
        }

        public Criteria andGovPublicOrderCountNotEqualTo(Integer value) {
            addCriterion("gov_public_order_count <>", value, "govPublicOrderCount");
            return (Criteria) this;
        }

        public Criteria andGovPublicOrderCountGreaterThan(Integer value) {
            addCriterion("gov_public_order_count >", value, "govPublicOrderCount");
            return (Criteria) this;
        }

        public Criteria andGovPublicOrderCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("gov_public_order_count >=", value, "govPublicOrderCount");
            return (Criteria) this;
        }

        public Criteria andGovPublicOrderCountLessThan(Integer value) {
            addCriterion("gov_public_order_count <", value, "govPublicOrderCount");
            return (Criteria) this;
        }

        public Criteria andGovPublicOrderCountLessThanOrEqualTo(Integer value) {
            addCriterion("gov_public_order_count <=", value, "govPublicOrderCount");
            return (Criteria) this;
        }

        public Criteria andGovPublicOrderCountIn(List<Integer> values) {
            addCriterion("gov_public_order_count in", values, "govPublicOrderCount");
            return (Criteria) this;
        }

        public Criteria andGovPublicOrderCountNotIn(List<Integer> values) {
            addCriterion("gov_public_order_count not in", values, "govPublicOrderCount");
            return (Criteria) this;
        }

        public Criteria andGovPublicOrderCountBetween(Integer value1, Integer value2) {
            addCriterion("gov_public_order_count between", value1, value2, "govPublicOrderCount");
            return (Criteria) this;
        }

        public Criteria andGovPublicOrderCountNotBetween(Integer value1, Integer value2) {
            addCriterion("gov_public_order_count not between", value1, value2, "govPublicOrderCount");
            return (Criteria) this;
        }

        public Criteria andStaffCountIsNull() {
            addCriterion("staff_count is null");
            return (Criteria) this;
        }

        public Criteria andStaffCountIsNotNull() {
            addCriterion("staff_count is not null");
            return (Criteria) this;
        }

        public Criteria andStaffCountEqualTo(Integer value) {
            addCriterion("staff_count =", value, "staffCount");
            return (Criteria) this;
        }

        public Criteria andStaffCountNotEqualTo(Integer value) {
            addCriterion("staff_count <>", value, "staffCount");
            return (Criteria) this;
        }

        public Criteria andStaffCountGreaterThan(Integer value) {
            addCriterion("staff_count >", value, "staffCount");
            return (Criteria) this;
        }

        public Criteria andStaffCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("staff_count >=", value, "staffCount");
            return (Criteria) this;
        }

        public Criteria andStaffCountLessThan(Integer value) {
            addCriterion("staff_count <", value, "staffCount");
            return (Criteria) this;
        }

        public Criteria andStaffCountLessThanOrEqualTo(Integer value) {
            addCriterion("staff_count <=", value, "staffCount");
            return (Criteria) this;
        }

        public Criteria andStaffCountIn(List<Integer> values) {
            addCriterion("staff_count in", values, "staffCount");
            return (Criteria) this;
        }

        public Criteria andStaffCountNotIn(List<Integer> values) {
            addCriterion("staff_count not in", values, "staffCount");
            return (Criteria) this;
        }

        public Criteria andStaffCountBetween(Integer value1, Integer value2) {
            addCriterion("staff_count between", value1, value2, "staffCount");
            return (Criteria) this;
        }

        public Criteria andStaffCountNotBetween(Integer value1, Integer value2) {
            addCriterion("staff_count not between", value1, value2, "staffCount");
            return (Criteria) this;
        }

        public Criteria andSqVehicleCountIsNull() {
            addCriterion("sq_vehicle_count is null");
            return (Criteria) this;
        }

        public Criteria andSqVehicleCountIsNotNull() {
            addCriterion("sq_vehicle_count is not null");
            return (Criteria) this;
        }

        public Criteria andSqVehicleCountEqualTo(Integer value) {
            addCriterion("sq_vehicle_count =", value, "sqVehicleCount");
            return (Criteria) this;
        }

        public Criteria andSqVehicleCountNotEqualTo(Integer value) {
            addCriterion("sq_vehicle_count <>", value, "sqVehicleCount");
            return (Criteria) this;
        }

        public Criteria andSqVehicleCountGreaterThan(Integer value) {
            addCriterion("sq_vehicle_count >", value, "sqVehicleCount");
            return (Criteria) this;
        }

        public Criteria andSqVehicleCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("sq_vehicle_count >=", value, "sqVehicleCount");
            return (Criteria) this;
        }

        public Criteria andSqVehicleCountLessThan(Integer value) {
            addCriterion("sq_vehicle_count <", value, "sqVehicleCount");
            return (Criteria) this;
        }

        public Criteria andSqVehicleCountLessThanOrEqualTo(Integer value) {
            addCriterion("sq_vehicle_count <=", value, "sqVehicleCount");
            return (Criteria) this;
        }

        public Criteria andSqVehicleCountIn(List<Integer> values) {
            addCriterion("sq_vehicle_count in", values, "sqVehicleCount");
            return (Criteria) this;
        }

        public Criteria andSqVehicleCountNotIn(List<Integer> values) {
            addCriterion("sq_vehicle_count not in", values, "sqVehicleCount");
            return (Criteria) this;
        }

        public Criteria andSqVehicleCountBetween(Integer value1, Integer value2) {
            addCriterion("sq_vehicle_count between", value1, value2, "sqVehicleCount");
            return (Criteria) this;
        }

        public Criteria andSqVehicleCountNotBetween(Integer value1, Integer value2) {
            addCriterion("sq_vehicle_count not between", value1, value2, "sqVehicleCount");
            return (Criteria) this;
        }

        public Criteria andCompanyVehicleCountIsNull() {
            addCriterion("company_vehicle_count is null");
            return (Criteria) this;
        }

        public Criteria andCompanyVehicleCountIsNotNull() {
            addCriterion("company_vehicle_count is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyVehicleCountEqualTo(Integer value) {
            addCriterion("company_vehicle_count =", value, "companyVehicleCount");
            return (Criteria) this;
        }

        public Criteria andCompanyVehicleCountNotEqualTo(Integer value) {
            addCriterion("company_vehicle_count <>", value, "companyVehicleCount");
            return (Criteria) this;
        }

        public Criteria andCompanyVehicleCountGreaterThan(Integer value) {
            addCriterion("company_vehicle_count >", value, "companyVehicleCount");
            return (Criteria) this;
        }

        public Criteria andCompanyVehicleCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("company_vehicle_count >=", value, "companyVehicleCount");
            return (Criteria) this;
        }

        public Criteria andCompanyVehicleCountLessThan(Integer value) {
            addCriterion("company_vehicle_count <", value, "companyVehicleCount");
            return (Criteria) this;
        }

        public Criteria andCompanyVehicleCountLessThanOrEqualTo(Integer value) {
            addCriterion("company_vehicle_count <=", value, "companyVehicleCount");
            return (Criteria) this;
        }

        public Criteria andCompanyVehicleCountIn(List<Integer> values) {
            addCriterion("company_vehicle_count in", values, "companyVehicleCount");
            return (Criteria) this;
        }

        public Criteria andCompanyVehicleCountNotIn(List<Integer> values) {
            addCriterion("company_vehicle_count not in", values, "companyVehicleCount");
            return (Criteria) this;
        }

        public Criteria andCompanyVehicleCountBetween(Integer value1, Integer value2) {
            addCriterion("company_vehicle_count between", value1, value2, "companyVehicleCount");
            return (Criteria) this;
        }

        public Criteria andCompanyVehicleCountNotBetween(Integer value1, Integer value2) {
            addCriterion("company_vehicle_count not between", value1, value2, "companyVehicleCount");
            return (Criteria) this;
        }

        public Criteria andThirdVehicleCountIsNull() {
            addCriterion("third_vehicle_count is null");
            return (Criteria) this;
        }

        public Criteria andThirdVehicleCountIsNotNull() {
            addCriterion("third_vehicle_count is not null");
            return (Criteria) this;
        }

        public Criteria andThirdVehicleCountEqualTo(Integer value) {
            addCriterion("third_vehicle_count =", value, "thirdVehicleCount");
            return (Criteria) this;
        }

        public Criteria andThirdVehicleCountNotEqualTo(Integer value) {
            addCriterion("third_vehicle_count <>", value, "thirdVehicleCount");
            return (Criteria) this;
        }

        public Criteria andThirdVehicleCountGreaterThan(Integer value) {
            addCriterion("third_vehicle_count >", value, "thirdVehicleCount");
            return (Criteria) this;
        }

        public Criteria andThirdVehicleCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("third_vehicle_count >=", value, "thirdVehicleCount");
            return (Criteria) this;
        }

        public Criteria andThirdVehicleCountLessThan(Integer value) {
            addCriterion("third_vehicle_count <", value, "thirdVehicleCount");
            return (Criteria) this;
        }

        public Criteria andThirdVehicleCountLessThanOrEqualTo(Integer value) {
            addCriterion("third_vehicle_count <=", value, "thirdVehicleCount");
            return (Criteria) this;
        }

        public Criteria andThirdVehicleCountIn(List<Integer> values) {
            addCriterion("third_vehicle_count in", values, "thirdVehicleCount");
            return (Criteria) this;
        }

        public Criteria andThirdVehicleCountNotIn(List<Integer> values) {
            addCriterion("third_vehicle_count not in", values, "thirdVehicleCount");
            return (Criteria) this;
        }

        public Criteria andThirdVehicleCountBetween(Integer value1, Integer value2) {
            addCriterion("third_vehicle_count between", value1, value2, "thirdVehicleCount");
            return (Criteria) this;
        }

        public Criteria andThirdVehicleCountNotBetween(Integer value1, Integer value2) {
            addCriterion("third_vehicle_count not between", value1, value2, "thirdVehicleCount");
            return (Criteria) this;
        }

        public Criteria andStaffVehicleCountIsNull() {
            addCriterion("staff_vehicle_count is null");
            return (Criteria) this;
        }

        public Criteria andStaffVehicleCountIsNotNull() {
            addCriterion("staff_vehicle_count is not null");
            return (Criteria) this;
        }

        public Criteria andStaffVehicleCountEqualTo(Integer value) {
            addCriterion("staff_vehicle_count =", value, "staffVehicleCount");
            return (Criteria) this;
        }

        public Criteria andStaffVehicleCountNotEqualTo(Integer value) {
            addCriterion("staff_vehicle_count <>", value, "staffVehicleCount");
            return (Criteria) this;
        }

        public Criteria andStaffVehicleCountGreaterThan(Integer value) {
            addCriterion("staff_vehicle_count >", value, "staffVehicleCount");
            return (Criteria) this;
        }

        public Criteria andStaffVehicleCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("staff_vehicle_count >=", value, "staffVehicleCount");
            return (Criteria) this;
        }

        public Criteria andStaffVehicleCountLessThan(Integer value) {
            addCriterion("staff_vehicle_count <", value, "staffVehicleCount");
            return (Criteria) this;
        }

        public Criteria andStaffVehicleCountLessThanOrEqualTo(Integer value) {
            addCriterion("staff_vehicle_count <=", value, "staffVehicleCount");
            return (Criteria) this;
        }

        public Criteria andStaffVehicleCountIn(List<Integer> values) {
            addCriterion("staff_vehicle_count in", values, "staffVehicleCount");
            return (Criteria) this;
        }

        public Criteria andStaffVehicleCountNotIn(List<Integer> values) {
            addCriterion("staff_vehicle_count not in", values, "staffVehicleCount");
            return (Criteria) this;
        }

        public Criteria andStaffVehicleCountBetween(Integer value1, Integer value2) {
            addCriterion("staff_vehicle_count between", value1, value2, "staffVehicleCount");
            return (Criteria) this;
        }

        public Criteria andStaffVehicleCountNotBetween(Integer value1, Integer value2) {
            addCriterion("staff_vehicle_count not between", value1, value2, "staffVehicleCount");
            return (Criteria) this;
        }

        public Criteria andSqDriverCountIsNull() {
            addCriterion("sq_driver_count is null");
            return (Criteria) this;
        }

        public Criteria andSqDriverCountIsNotNull() {
            addCriterion("sq_driver_count is not null");
            return (Criteria) this;
        }

        public Criteria andSqDriverCountEqualTo(Integer value) {
            addCriterion("sq_driver_count =", value, "sqDriverCount");
            return (Criteria) this;
        }

        public Criteria andSqDriverCountNotEqualTo(Integer value) {
            addCriterion("sq_driver_count <>", value, "sqDriverCount");
            return (Criteria) this;
        }

        public Criteria andSqDriverCountGreaterThan(Integer value) {
            addCriterion("sq_driver_count >", value, "sqDriverCount");
            return (Criteria) this;
        }

        public Criteria andSqDriverCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("sq_driver_count >=", value, "sqDriverCount");
            return (Criteria) this;
        }

        public Criteria andSqDriverCountLessThan(Integer value) {
            addCriterion("sq_driver_count <", value, "sqDriverCount");
            return (Criteria) this;
        }

        public Criteria andSqDriverCountLessThanOrEqualTo(Integer value) {
            addCriterion("sq_driver_count <=", value, "sqDriverCount");
            return (Criteria) this;
        }

        public Criteria andSqDriverCountIn(List<Integer> values) {
            addCriterion("sq_driver_count in", values, "sqDriverCount");
            return (Criteria) this;
        }

        public Criteria andSqDriverCountNotIn(List<Integer> values) {
            addCriterion("sq_driver_count not in", values, "sqDriverCount");
            return (Criteria) this;
        }

        public Criteria andSqDriverCountBetween(Integer value1, Integer value2) {
            addCriterion("sq_driver_count between", value1, value2, "sqDriverCount");
            return (Criteria) this;
        }

        public Criteria andSqDriverCountNotBetween(Integer value1, Integer value2) {
            addCriterion("sq_driver_count not between", value1, value2, "sqDriverCount");
            return (Criteria) this;
        }

        public Criteria andCompanyDriverCountIsNull() {
            addCriterion("company_driver_count is null");
            return (Criteria) this;
        }

        public Criteria andCompanyDriverCountIsNotNull() {
            addCriterion("company_driver_count is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyDriverCountEqualTo(Integer value) {
            addCriterion("company_driver_count =", value, "companyDriverCount");
            return (Criteria) this;
        }

        public Criteria andCompanyDriverCountNotEqualTo(Integer value) {
            addCriterion("company_driver_count <>", value, "companyDriverCount");
            return (Criteria) this;
        }

        public Criteria andCompanyDriverCountGreaterThan(Integer value) {
            addCriterion("company_driver_count >", value, "companyDriverCount");
            return (Criteria) this;
        }

        public Criteria andCompanyDriverCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("company_driver_count >=", value, "companyDriverCount");
            return (Criteria) this;
        }

        public Criteria andCompanyDriverCountLessThan(Integer value) {
            addCriterion("company_driver_count <", value, "companyDriverCount");
            return (Criteria) this;
        }

        public Criteria andCompanyDriverCountLessThanOrEqualTo(Integer value) {
            addCriterion("company_driver_count <=", value, "companyDriverCount");
            return (Criteria) this;
        }

        public Criteria andCompanyDriverCountIn(List<Integer> values) {
            addCriterion("company_driver_count in", values, "companyDriverCount");
            return (Criteria) this;
        }

        public Criteria andCompanyDriverCountNotIn(List<Integer> values) {
            addCriterion("company_driver_count not in", values, "companyDriverCount");
            return (Criteria) this;
        }

        public Criteria andCompanyDriverCountBetween(Integer value1, Integer value2) {
            addCriterion("company_driver_count between", value1, value2, "companyDriverCount");
            return (Criteria) this;
        }

        public Criteria andCompanyDriverCountNotBetween(Integer value1, Integer value2) {
            addCriterion("company_driver_count not between", value1, value2, "companyDriverCount");
            return (Criteria) this;
        }

        public Criteria andBusinessDriverCountIsNull() {
            addCriterion("business_driver_count is null");
            return (Criteria) this;
        }

        public Criteria andBusinessDriverCountIsNotNull() {
            addCriterion("business_driver_count is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessDriverCountEqualTo(Integer value) {
            addCriterion("business_driver_count =", value, "businessDriverCount");
            return (Criteria) this;
        }

        public Criteria andBusinessDriverCountNotEqualTo(Integer value) {
            addCriterion("business_driver_count <>", value, "businessDriverCount");
            return (Criteria) this;
        }

        public Criteria andBusinessDriverCountGreaterThan(Integer value) {
            addCriterion("business_driver_count >", value, "businessDriverCount");
            return (Criteria) this;
        }

        public Criteria andBusinessDriverCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("business_driver_count >=", value, "businessDriverCount");
            return (Criteria) this;
        }

        public Criteria andBusinessDriverCountLessThan(Integer value) {
            addCriterion("business_driver_count <", value, "businessDriverCount");
            return (Criteria) this;
        }

        public Criteria andBusinessDriverCountLessThanOrEqualTo(Integer value) {
            addCriterion("business_driver_count <=", value, "businessDriverCount");
            return (Criteria) this;
        }

        public Criteria andBusinessDriverCountIn(List<Integer> values) {
            addCriterion("business_driver_count in", values, "businessDriverCount");
            return (Criteria) this;
        }

        public Criteria andBusinessDriverCountNotIn(List<Integer> values) {
            addCriterion("business_driver_count not in", values, "businessDriverCount");
            return (Criteria) this;
        }

        public Criteria andBusinessDriverCountBetween(Integer value1, Integer value2) {
            addCriterion("business_driver_count between", value1, value2, "businessDriverCount");
            return (Criteria) this;
        }

        public Criteria andBusinessDriverCountNotBetween(Integer value1, Integer value2) {
            addCriterion("business_driver_count not between", value1, value2, "businessDriverCount");
            return (Criteria) this;
        }

        public Criteria andThirdDriverCountIsNull() {
            addCriterion("third_driver_count is null");
            return (Criteria) this;
        }

        public Criteria andThirdDriverCountIsNotNull() {
            addCriterion("third_driver_count is not null");
            return (Criteria) this;
        }

        public Criteria andThirdDriverCountEqualTo(Integer value) {
            addCriterion("third_driver_count =", value, "thirdDriverCount");
            return (Criteria) this;
        }

        public Criteria andThirdDriverCountNotEqualTo(Integer value) {
            addCriterion("third_driver_count <>", value, "thirdDriverCount");
            return (Criteria) this;
        }

        public Criteria andThirdDriverCountGreaterThan(Integer value) {
            addCriterion("third_driver_count >", value, "thirdDriverCount");
            return (Criteria) this;
        }

        public Criteria andThirdDriverCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("third_driver_count >=", value, "thirdDriverCount");
            return (Criteria) this;
        }

        public Criteria andThirdDriverCountLessThan(Integer value) {
            addCriterion("third_driver_count <", value, "thirdDriverCount");
            return (Criteria) this;
        }

        public Criteria andThirdDriverCountLessThanOrEqualTo(Integer value) {
            addCriterion("third_driver_count <=", value, "thirdDriverCount");
            return (Criteria) this;
        }

        public Criteria andThirdDriverCountIn(List<Integer> values) {
            addCriterion("third_driver_count in", values, "thirdDriverCount");
            return (Criteria) this;
        }

        public Criteria andThirdDriverCountNotIn(List<Integer> values) {
            addCriterion("third_driver_count not in", values, "thirdDriverCount");
            return (Criteria) this;
        }

        public Criteria andThirdDriverCountBetween(Integer value1, Integer value2) {
            addCriterion("third_driver_count between", value1, value2, "thirdDriverCount");
            return (Criteria) this;
        }

        public Criteria andThirdDriverCountNotBetween(Integer value1, Integer value2) {
            addCriterion("third_driver_count not between", value1, value2, "thirdDriverCount");
            return (Criteria) this;
        }

        public Criteria andSqDeviceCountIsNull() {
            addCriterion("sq_device_count is null");
            return (Criteria) this;
        }

        public Criteria andSqDeviceCountIsNotNull() {
            addCriterion("sq_device_count is not null");
            return (Criteria) this;
        }

        public Criteria andSqDeviceCountEqualTo(Integer value) {
            addCriterion("sq_device_count =", value, "sqDeviceCount");
            return (Criteria) this;
        }

        public Criteria andSqDeviceCountNotEqualTo(Integer value) {
            addCriterion("sq_device_count <>", value, "sqDeviceCount");
            return (Criteria) this;
        }

        public Criteria andSqDeviceCountGreaterThan(Integer value) {
            addCriterion("sq_device_count >", value, "sqDeviceCount");
            return (Criteria) this;
        }

        public Criteria andSqDeviceCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("sq_device_count >=", value, "sqDeviceCount");
            return (Criteria) this;
        }

        public Criteria andSqDeviceCountLessThan(Integer value) {
            addCriterion("sq_device_count <", value, "sqDeviceCount");
            return (Criteria) this;
        }

        public Criteria andSqDeviceCountLessThanOrEqualTo(Integer value) {
            addCriterion("sq_device_count <=", value, "sqDeviceCount");
            return (Criteria) this;
        }

        public Criteria andSqDeviceCountIn(List<Integer> values) {
            addCriterion("sq_device_count in", values, "sqDeviceCount");
            return (Criteria) this;
        }

        public Criteria andSqDeviceCountNotIn(List<Integer> values) {
            addCriterion("sq_device_count not in", values, "sqDeviceCount");
            return (Criteria) this;
        }

        public Criteria andSqDeviceCountBetween(Integer value1, Integer value2) {
            addCriterion("sq_device_count between", value1, value2, "sqDeviceCount");
            return (Criteria) this;
        }

        public Criteria andSqDeviceCountNotBetween(Integer value1, Integer value2) {
            addCriterion("sq_device_count not between", value1, value2, "sqDeviceCount");
            return (Criteria) this;
        }

        public Criteria andSelfDeviceCountIsNull() {
            addCriterion("self_device_count is null");
            return (Criteria) this;
        }

        public Criteria andSelfDeviceCountIsNotNull() {
            addCriterion("self_device_count is not null");
            return (Criteria) this;
        }

        public Criteria andSelfDeviceCountEqualTo(Integer value) {
            addCriterion("self_device_count =", value, "selfDeviceCount");
            return (Criteria) this;
        }

        public Criteria andSelfDeviceCountNotEqualTo(Integer value) {
            addCriterion("self_device_count <>", value, "selfDeviceCount");
            return (Criteria) this;
        }

        public Criteria andSelfDeviceCountGreaterThan(Integer value) {
            addCriterion("self_device_count >", value, "selfDeviceCount");
            return (Criteria) this;
        }

        public Criteria andSelfDeviceCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("self_device_count >=", value, "selfDeviceCount");
            return (Criteria) this;
        }

        public Criteria andSelfDeviceCountLessThan(Integer value) {
            addCriterion("self_device_count <", value, "selfDeviceCount");
            return (Criteria) this;
        }

        public Criteria andSelfDeviceCountLessThanOrEqualTo(Integer value) {
            addCriterion("self_device_count <=", value, "selfDeviceCount");
            return (Criteria) this;
        }

        public Criteria andSelfDeviceCountIn(List<Integer> values) {
            addCriterion("self_device_count in", values, "selfDeviceCount");
            return (Criteria) this;
        }

        public Criteria andSelfDeviceCountNotIn(List<Integer> values) {
            addCriterion("self_device_count not in", values, "selfDeviceCount");
            return (Criteria) this;
        }

        public Criteria andSelfDeviceCountBetween(Integer value1, Integer value2) {
            addCriterion("self_device_count between", value1, value2, "selfDeviceCount");
            return (Criteria) this;
        }

        public Criteria andSelfDeviceCountNotBetween(Integer value1, Integer value2) {
            addCriterion("self_device_count not between", value1, value2, "selfDeviceCount");
            return (Criteria) this;
        }

        public Criteria andApplyCountIsNull() {
            addCriterion("apply_count is null");
            return (Criteria) this;
        }

        public Criteria andApplyCountIsNotNull() {
            addCriterion("apply_count is not null");
            return (Criteria) this;
        }

        public Criteria andApplyCountEqualTo(Integer value) {
            addCriterion("apply_count =", value, "applyCount");
            return (Criteria) this;
        }

        public Criteria andApplyCountNotEqualTo(Integer value) {
            addCriterion("apply_count <>", value, "applyCount");
            return (Criteria) this;
        }

        public Criteria andApplyCountGreaterThan(Integer value) {
            addCriterion("apply_count >", value, "applyCount");
            return (Criteria) this;
        }

        public Criteria andApplyCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("apply_count >=", value, "applyCount");
            return (Criteria) this;
        }

        public Criteria andApplyCountLessThan(Integer value) {
            addCriterion("apply_count <", value, "applyCount");
            return (Criteria) this;
        }

        public Criteria andApplyCountLessThanOrEqualTo(Integer value) {
            addCriterion("apply_count <=", value, "applyCount");
            return (Criteria) this;
        }

        public Criteria andApplyCountIn(List<Integer> values) {
            addCriterion("apply_count in", values, "applyCount");
            return (Criteria) this;
        }

        public Criteria andApplyCountNotIn(List<Integer> values) {
            addCriterion("apply_count not in", values, "applyCount");
            return (Criteria) this;
        }

        public Criteria andApplyCountBetween(Integer value1, Integer value2) {
            addCriterion("apply_count between", value1, value2, "applyCount");
            return (Criteria) this;
        }

        public Criteria andApplyCountNotBetween(Integer value1, Integer value2) {
            addCriterion("apply_count not between", value1, value2, "applyCount");
            return (Criteria) this;
        }

        public Criteria andSqMaintenanceCountIsNull() {
            addCriterion("sq_maintenance_count is null");
            return (Criteria) this;
        }

        public Criteria andSqMaintenanceCountIsNotNull() {
            addCriterion("sq_maintenance_count is not null");
            return (Criteria) this;
        }

        public Criteria andSqMaintenanceCountEqualTo(Integer value) {
            addCriterion("sq_maintenance_count =", value, "sqMaintenanceCount");
            return (Criteria) this;
        }

        public Criteria andSqMaintenanceCountNotEqualTo(Integer value) {
            addCriterion("sq_maintenance_count <>", value, "sqMaintenanceCount");
            return (Criteria) this;
        }

        public Criteria andSqMaintenanceCountGreaterThan(Integer value) {
            addCriterion("sq_maintenance_count >", value, "sqMaintenanceCount");
            return (Criteria) this;
        }

        public Criteria andSqMaintenanceCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("sq_maintenance_count >=", value, "sqMaintenanceCount");
            return (Criteria) this;
        }

        public Criteria andSqMaintenanceCountLessThan(Integer value) {
            addCriterion("sq_maintenance_count <", value, "sqMaintenanceCount");
            return (Criteria) this;
        }

        public Criteria andSqMaintenanceCountLessThanOrEqualTo(Integer value) {
            addCriterion("sq_maintenance_count <=", value, "sqMaintenanceCount");
            return (Criteria) this;
        }

        public Criteria andSqMaintenanceCountIn(List<Integer> values) {
            addCriterion("sq_maintenance_count in", values, "sqMaintenanceCount");
            return (Criteria) this;
        }

        public Criteria andSqMaintenanceCountNotIn(List<Integer> values) {
            addCriterion("sq_maintenance_count not in", values, "sqMaintenanceCount");
            return (Criteria) this;
        }

        public Criteria andSqMaintenanceCountBetween(Integer value1, Integer value2) {
            addCriterion("sq_maintenance_count between", value1, value2, "sqMaintenanceCount");
            return (Criteria) this;
        }

        public Criteria andSqMaintenanceCountNotBetween(Integer value1, Integer value2) {
            addCriterion("sq_maintenance_count not between", value1, value2, "sqMaintenanceCount");
            return (Criteria) this;
        }

        public Criteria andCompanyMaintenanceCountIsNull() {
            addCriterion("company_maintenance_count is null");
            return (Criteria) this;
        }

        public Criteria andCompanyMaintenanceCountIsNotNull() {
            addCriterion("company_maintenance_count is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyMaintenanceCountEqualTo(Integer value) {
            addCriterion("company_maintenance_count =", value, "companyMaintenanceCount");
            return (Criteria) this;
        }

        public Criteria andCompanyMaintenanceCountNotEqualTo(Integer value) {
            addCriterion("company_maintenance_count <>", value, "companyMaintenanceCount");
            return (Criteria) this;
        }

        public Criteria andCompanyMaintenanceCountGreaterThan(Integer value) {
            addCriterion("company_maintenance_count >", value, "companyMaintenanceCount");
            return (Criteria) this;
        }

        public Criteria andCompanyMaintenanceCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("company_maintenance_count >=", value, "companyMaintenanceCount");
            return (Criteria) this;
        }

        public Criteria andCompanyMaintenanceCountLessThan(Integer value) {
            addCriterion("company_maintenance_count <", value, "companyMaintenanceCount");
            return (Criteria) this;
        }

        public Criteria andCompanyMaintenanceCountLessThanOrEqualTo(Integer value) {
            addCriterion("company_maintenance_count <=", value, "companyMaintenanceCount");
            return (Criteria) this;
        }

        public Criteria andCompanyMaintenanceCountIn(List<Integer> values) {
            addCriterion("company_maintenance_count in", values, "companyMaintenanceCount");
            return (Criteria) this;
        }

        public Criteria andCompanyMaintenanceCountNotIn(List<Integer> values) {
            addCriterion("company_maintenance_count not in", values, "companyMaintenanceCount");
            return (Criteria) this;
        }

        public Criteria andCompanyMaintenanceCountBetween(Integer value1, Integer value2) {
            addCriterion("company_maintenance_count between", value1, value2, "companyMaintenanceCount");
            return (Criteria) this;
        }

        public Criteria andCompanyMaintenanceCountNotBetween(Integer value1, Integer value2) {
            addCriterion("company_maintenance_count not between", value1, value2, "companyMaintenanceCount");
            return (Criteria) this;
        }

        public Criteria andChargeOrderCountIsNull() {
            addCriterion("charge_order_count is null");
            return (Criteria) this;
        }

        public Criteria andChargeOrderCountIsNotNull() {
            addCriterion("charge_order_count is not null");
            return (Criteria) this;
        }

        public Criteria andChargeOrderCountEqualTo(Integer value) {
            addCriterion("charge_order_count =", value, "chargeOrderCount");
            return (Criteria) this;
        }

        public Criteria andChargeOrderCountNotEqualTo(Integer value) {
            addCriterion("charge_order_count <>", value, "chargeOrderCount");
            return (Criteria) this;
        }

        public Criteria andChargeOrderCountGreaterThan(Integer value) {
            addCriterion("charge_order_count >", value, "chargeOrderCount");
            return (Criteria) this;
        }

        public Criteria andChargeOrderCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("charge_order_count >=", value, "chargeOrderCount");
            return (Criteria) this;
        }

        public Criteria andChargeOrderCountLessThan(Integer value) {
            addCriterion("charge_order_count <", value, "chargeOrderCount");
            return (Criteria) this;
        }

        public Criteria andChargeOrderCountLessThanOrEqualTo(Integer value) {
            addCriterion("charge_order_count <=", value, "chargeOrderCount");
            return (Criteria) this;
        }

        public Criteria andChargeOrderCountIn(List<Integer> values) {
            addCriterion("charge_order_count in", values, "chargeOrderCount");
            return (Criteria) this;
        }

        public Criteria andChargeOrderCountNotIn(List<Integer> values) {
            addCriterion("charge_order_count not in", values, "chargeOrderCount");
            return (Criteria) this;
        }

        public Criteria andChargeOrderCountBetween(Integer value1, Integer value2) {
            addCriterion("charge_order_count between", value1, value2, "chargeOrderCount");
            return (Criteria) this;
        }

        public Criteria andChargeOrderCountNotBetween(Integer value1, Integer value2) {
            addCriterion("charge_order_count not between", value1, value2, "chargeOrderCount");
            return (Criteria) this;
        }

        public Criteria andSqViolationCountIsNull() {
            addCriterion("sq_violation_count is null");
            return (Criteria) this;
        }

        public Criteria andSqViolationCountIsNotNull() {
            addCriterion("sq_violation_count is not null");
            return (Criteria) this;
        }

        public Criteria andSqViolationCountEqualTo(Integer value) {
            addCriterion("sq_violation_count =", value, "sqViolationCount");
            return (Criteria) this;
        }

        public Criteria andSqViolationCountNotEqualTo(Integer value) {
            addCriterion("sq_violation_count <>", value, "sqViolationCount");
            return (Criteria) this;
        }

        public Criteria andSqViolationCountGreaterThan(Integer value) {
            addCriterion("sq_violation_count >", value, "sqViolationCount");
            return (Criteria) this;
        }

        public Criteria andSqViolationCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("sq_violation_count >=", value, "sqViolationCount");
            return (Criteria) this;
        }

        public Criteria andSqViolationCountLessThan(Integer value) {
            addCriterion("sq_violation_count <", value, "sqViolationCount");
            return (Criteria) this;
        }

        public Criteria andSqViolationCountLessThanOrEqualTo(Integer value) {
            addCriterion("sq_violation_count <=", value, "sqViolationCount");
            return (Criteria) this;
        }

        public Criteria andSqViolationCountIn(List<Integer> values) {
            addCriterion("sq_violation_count in", values, "sqViolationCount");
            return (Criteria) this;
        }

        public Criteria andSqViolationCountNotIn(List<Integer> values) {
            addCriterion("sq_violation_count not in", values, "sqViolationCount");
            return (Criteria) this;
        }

        public Criteria andSqViolationCountBetween(Integer value1, Integer value2) {
            addCriterion("sq_violation_count between", value1, value2, "sqViolationCount");
            return (Criteria) this;
        }

        public Criteria andSqViolationCountNotBetween(Integer value1, Integer value2) {
            addCriterion("sq_violation_count not between", value1, value2, "sqViolationCount");
            return (Criteria) this;
        }

        public Criteria andCompanyViolationCountIsNull() {
            addCriterion("company_violation_count is null");
            return (Criteria) this;
        }

        public Criteria andCompanyViolationCountIsNotNull() {
            addCriterion("company_violation_count is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyViolationCountEqualTo(Integer value) {
            addCriterion("company_violation_count =", value, "companyViolationCount");
            return (Criteria) this;
        }

        public Criteria andCompanyViolationCountNotEqualTo(Integer value) {
            addCriterion("company_violation_count <>", value, "companyViolationCount");
            return (Criteria) this;
        }

        public Criteria andCompanyViolationCountGreaterThan(Integer value) {
            addCriterion("company_violation_count >", value, "companyViolationCount");
            return (Criteria) this;
        }

        public Criteria andCompanyViolationCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("company_violation_count >=", value, "companyViolationCount");
            return (Criteria) this;
        }

        public Criteria andCompanyViolationCountLessThan(Integer value) {
            addCriterion("company_violation_count <", value, "companyViolationCount");
            return (Criteria) this;
        }

        public Criteria andCompanyViolationCountLessThanOrEqualTo(Integer value) {
            addCriterion("company_violation_count <=", value, "companyViolationCount");
            return (Criteria) this;
        }

        public Criteria andCompanyViolationCountIn(List<Integer> values) {
            addCriterion("company_violation_count in", values, "companyViolationCount");
            return (Criteria) this;
        }

        public Criteria andCompanyViolationCountNotIn(List<Integer> values) {
            addCriterion("company_violation_count not in", values, "companyViolationCount");
            return (Criteria) this;
        }

        public Criteria andCompanyViolationCountBetween(Integer value1, Integer value2) {
            addCriterion("company_violation_count between", value1, value2, "companyViolationCount");
            return (Criteria) this;
        }

        public Criteria andCompanyViolationCountNotBetween(Integer value1, Integer value2) {
            addCriterion("company_violation_count not between", value1, value2, "companyViolationCount");
            return (Criteria) this;
        }

        public Criteria andCompanyAttributeIsNull() {
            addCriterion("company_attribute is null");
            return (Criteria) this;
        }

        public Criteria andCompanyAttributeIsNotNull() {
            addCriterion("company_attribute is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyAttributeEqualTo(Byte value) {
            addCriterion("company_attribute =", value, "companyAttribute");
            return (Criteria) this;
        }

        public Criteria andCompanyAttributeNotEqualTo(Byte value) {
            addCriterion("company_attribute <>", value, "companyAttribute");
            return (Criteria) this;
        }

        public Criteria andCompanyAttributeGreaterThan(Byte value) {
            addCriterion("company_attribute >", value, "companyAttribute");
            return (Criteria) this;
        }

        public Criteria andCompanyAttributeGreaterThanOrEqualTo(Byte value) {
            addCriterion("company_attribute >=", value, "companyAttribute");
            return (Criteria) this;
        }

        public Criteria andCompanyAttributeLessThan(Byte value) {
            addCriterion("company_attribute <", value, "companyAttribute");
            return (Criteria) this;
        }

        public Criteria andCompanyAttributeLessThanOrEqualTo(Byte value) {
            addCriterion("company_attribute <=", value, "companyAttribute");
            return (Criteria) this;
        }

        public Criteria andCompanyAttributeIn(List<Byte> values) {
            addCriterion("company_attribute in", values, "companyAttribute");
            return (Criteria) this;
        }

        public Criteria andCompanyAttributeNotIn(List<Byte> values) {
            addCriterion("company_attribute not in", values, "companyAttribute");
            return (Criteria) this;
        }

        public Criteria andCompanyAttributeBetween(Byte value1, Byte value2) {
            addCriterion("company_attribute between", value1, value2, "companyAttribute");
            return (Criteria) this;
        }

        public Criteria andCompanyAttributeNotBetween(Byte value1, Byte value2) {
            addCriterion("company_attribute not between", value1, value2, "companyAttribute");
            return (Criteria) this;
        }

        public Criteria andAccountTypeIsNull() {
            addCriterion("account_type is null");
            return (Criteria) this;
        }

        public Criteria andAccountTypeIsNotNull() {
            addCriterion("account_type is not null");
            return (Criteria) this;
        }

        public Criteria andAccountTypeEqualTo(Byte value) {
            addCriterion("account_type =", value, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeNotEqualTo(Byte value) {
            addCriterion("account_type <>", value, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeGreaterThan(Byte value) {
            addCriterion("account_type >", value, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("account_type >=", value, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeLessThan(Byte value) {
            addCriterion("account_type <", value, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeLessThanOrEqualTo(Byte value) {
            addCriterion("account_type <=", value, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeIn(List<Byte> values) {
            addCriterion("account_type in", values, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeNotIn(List<Byte> values) {
            addCriterion("account_type not in", values, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeBetween(Byte value1, Byte value2) {
            addCriterion("account_type between", value1, value2, "accountType");
            return (Criteria) this;
        }

        public Criteria andAccountTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("account_type not between", value1, value2, "accountType");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}