package com.izu.business.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SupplierExpenditureDetailExample {
    /**
     * supplier_expenditure_detail
     */
    protected String orderByClause;

    /**
     * supplier_expenditure_detail
     */
    protected boolean distinct;

    /**
     * supplier_expenditure_detail
     */
    protected List<Criteria> oredCriteria;

    public SupplierExpenditureDetailExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andReceiptIdIsNull() {
            addCriterion("receipt_id is null");
            return (Criteria) this;
        }

        public Criteria andReceiptIdIsNotNull() {
            addCriterion("receipt_id is not null");
            return (Criteria) this;
        }

        public Criteria andReceiptIdEqualTo(Long value) {
            addCriterion("receipt_id =", value, "receiptId");
            return (Criteria) this;
        }

        public Criteria andReceiptIdNotEqualTo(Long value) {
            addCriterion("receipt_id <>", value, "receiptId");
            return (Criteria) this;
        }

        public Criteria andReceiptIdGreaterThan(Long value) {
            addCriterion("receipt_id >", value, "receiptId");
            return (Criteria) this;
        }

        public Criteria andReceiptIdGreaterThanOrEqualTo(Long value) {
            addCriterion("receipt_id >=", value, "receiptId");
            return (Criteria) this;
        }

        public Criteria andReceiptIdLessThan(Long value) {
            addCriterion("receipt_id <", value, "receiptId");
            return (Criteria) this;
        }

        public Criteria andReceiptIdLessThanOrEqualTo(Long value) {
            addCriterion("receipt_id <=", value, "receiptId");
            return (Criteria) this;
        }

        public Criteria andReceiptIdIn(List<Long> values) {
            addCriterion("receipt_id in", values, "receiptId");
            return (Criteria) this;
        }

        public Criteria andReceiptIdNotIn(List<Long> values) {
            addCriterion("receipt_id not in", values, "receiptId");
            return (Criteria) this;
        }

        public Criteria andReceiptIdBetween(Long value1, Long value2) {
            addCriterion("receipt_id between", value1, value2, "receiptId");
            return (Criteria) this;
        }

        public Criteria andReceiptIdNotBetween(Long value1, Long value2) {
            addCriterion("receipt_id not between", value1, value2, "receiptId");
            return (Criteria) this;
        }

        public Criteria andExpenditureNoIsNull() {
            addCriterion("expenditure_no is null");
            return (Criteria) this;
        }

        public Criteria andExpenditureNoIsNotNull() {
            addCriterion("expenditure_no is not null");
            return (Criteria) this;
        }

        public Criteria andExpenditureNoEqualTo(String value) {
            addCriterion("expenditure_no =", value, "expenditureNo");
            return (Criteria) this;
        }

        public Criteria andExpenditureNoNotEqualTo(String value) {
            addCriterion("expenditure_no <>", value, "expenditureNo");
            return (Criteria) this;
        }

        public Criteria andExpenditureNoGreaterThan(String value) {
            addCriterion("expenditure_no >", value, "expenditureNo");
            return (Criteria) this;
        }

        public Criteria andExpenditureNoGreaterThanOrEqualTo(String value) {
            addCriterion("expenditure_no >=", value, "expenditureNo");
            return (Criteria) this;
        }

        public Criteria andExpenditureNoLessThan(String value) {
            addCriterion("expenditure_no <", value, "expenditureNo");
            return (Criteria) this;
        }

        public Criteria andExpenditureNoLessThanOrEqualTo(String value) {
            addCriterion("expenditure_no <=", value, "expenditureNo");
            return (Criteria) this;
        }

        public Criteria andExpenditureNoLike(String value) {
            addCriterion("expenditure_no like", value, "expenditureNo");
            return (Criteria) this;
        }

        public Criteria andExpenditureNoNotLike(String value) {
            addCriterion("expenditure_no not like", value, "expenditureNo");
            return (Criteria) this;
        }

        public Criteria andExpenditureNoIn(List<String> values) {
            addCriterion("expenditure_no in", values, "expenditureNo");
            return (Criteria) this;
        }

        public Criteria andExpenditureNoNotIn(List<String> values) {
            addCriterion("expenditure_no not in", values, "expenditureNo");
            return (Criteria) this;
        }

        public Criteria andExpenditureNoBetween(String value1, String value2) {
            addCriterion("expenditure_no between", value1, value2, "expenditureNo");
            return (Criteria) this;
        }

        public Criteria andExpenditureNoNotBetween(String value1, String value2) {
            addCriterion("expenditure_no not between", value1, value2, "expenditureNo");
            return (Criteria) this;
        }

        public Criteria andReceiptNoIsNull() {
            addCriterion("receipt_no is null");
            return (Criteria) this;
        }

        public Criteria andReceiptNoIsNotNull() {
            addCriterion("receipt_no is not null");
            return (Criteria) this;
        }

        public Criteria andReceiptNoEqualTo(String value) {
            addCriterion("receipt_no =", value, "receiptNo");
            return (Criteria) this;
        }

        public Criteria andReceiptNoNotEqualTo(String value) {
            addCriterion("receipt_no <>", value, "receiptNo");
            return (Criteria) this;
        }

        public Criteria andReceiptNoGreaterThan(String value) {
            addCriterion("receipt_no >", value, "receiptNo");
            return (Criteria) this;
        }

        public Criteria andReceiptNoGreaterThanOrEqualTo(String value) {
            addCriterion("receipt_no >=", value, "receiptNo");
            return (Criteria) this;
        }

        public Criteria andReceiptNoLessThan(String value) {
            addCriterion("receipt_no <", value, "receiptNo");
            return (Criteria) this;
        }

        public Criteria andReceiptNoLessThanOrEqualTo(String value) {
            addCriterion("receipt_no <=", value, "receiptNo");
            return (Criteria) this;
        }

        public Criteria andReceiptNoLike(String value) {
            addCriterion("receipt_no like", value, "receiptNo");
            return (Criteria) this;
        }

        public Criteria andReceiptNoNotLike(String value) {
            addCriterion("receipt_no not like", value, "receiptNo");
            return (Criteria) this;
        }

        public Criteria andReceiptNoIn(List<String> values) {
            addCriterion("receipt_no in", values, "receiptNo");
            return (Criteria) this;
        }

        public Criteria andReceiptNoNotIn(List<String> values) {
            addCriterion("receipt_no not in", values, "receiptNo");
            return (Criteria) this;
        }

        public Criteria andReceiptNoBetween(String value1, String value2) {
            addCriterion("receipt_no between", value1, value2, "receiptNo");
            return (Criteria) this;
        }

        public Criteria andReceiptNoNotBetween(String value1, String value2) {
            addCriterion("receipt_no not between", value1, value2, "receiptNo");
            return (Criteria) this;
        }

        public Criteria andExternalReceiptNoIsNull() {
            addCriterion("external_receipt_no is null");
            return (Criteria) this;
        }

        public Criteria andExternalReceiptNoIsNotNull() {
            addCriterion("external_receipt_no is not null");
            return (Criteria) this;
        }

        public Criteria andExternalReceiptNoEqualTo(String value) {
            addCriterion("external_receipt_no =", value, "externalReceiptNo");
            return (Criteria) this;
        }

        public Criteria andExternalReceiptNoNotEqualTo(String value) {
            addCriterion("external_receipt_no <>", value, "externalReceiptNo");
            return (Criteria) this;
        }

        public Criteria andExternalReceiptNoGreaterThan(String value) {
            addCriterion("external_receipt_no >", value, "externalReceiptNo");
            return (Criteria) this;
        }

        public Criteria andExternalReceiptNoGreaterThanOrEqualTo(String value) {
            addCriterion("external_receipt_no >=", value, "externalReceiptNo");
            return (Criteria) this;
        }

        public Criteria andExternalReceiptNoLessThan(String value) {
            addCriterion("external_receipt_no <", value, "externalReceiptNo");
            return (Criteria) this;
        }

        public Criteria andExternalReceiptNoLessThanOrEqualTo(String value) {
            addCriterion("external_receipt_no <=", value, "externalReceiptNo");
            return (Criteria) this;
        }

        public Criteria andExternalReceiptNoLike(String value) {
            addCriterion("external_receipt_no like", value, "externalReceiptNo");
            return (Criteria) this;
        }

        public Criteria andExternalReceiptNoNotLike(String value) {
            addCriterion("external_receipt_no not like", value, "externalReceiptNo");
            return (Criteria) this;
        }

        public Criteria andExternalReceiptNoIn(List<String> values) {
            addCriterion("external_receipt_no in", values, "externalReceiptNo");
            return (Criteria) this;
        }

        public Criteria andExternalReceiptNoNotIn(List<String> values) {
            addCriterion("external_receipt_no not in", values, "externalReceiptNo");
            return (Criteria) this;
        }

        public Criteria andExternalReceiptNoBetween(String value1, String value2) {
            addCriterion("external_receipt_no between", value1, value2, "externalReceiptNo");
            return (Criteria) this;
        }

        public Criteria andExternalReceiptNoNotBetween(String value1, String value2) {
            addCriterion("external_receipt_no not between", value1, value2, "externalReceiptNo");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(Integer value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(Integer value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(Integer value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(Integer value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(Integer value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<Integer> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<Integer> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(Integer value1, Integer value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(Integer value1, Integer value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserNameIsNull() {
            addCriterion("user_name is null");
            return (Criteria) this;
        }

        public Criteria andUserNameIsNotNull() {
            addCriterion("user_name is not null");
            return (Criteria) this;
        }

        public Criteria andUserNameEqualTo(String value) {
            addCriterion("user_name =", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotEqualTo(String value) {
            addCriterion("user_name <>", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameGreaterThan(String value) {
            addCriterion("user_name >", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameGreaterThanOrEqualTo(String value) {
            addCriterion("user_name >=", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameLessThan(String value) {
            addCriterion("user_name <", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameLessThanOrEqualTo(String value) {
            addCriterion("user_name <=", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameLike(String value) {
            addCriterion("user_name like", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotLike(String value) {
            addCriterion("user_name not like", value, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameIn(List<String> values) {
            addCriterion("user_name in", values, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotIn(List<String> values) {
            addCriterion("user_name not in", values, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameBetween(String value1, String value2) {
            addCriterion("user_name between", value1, value2, "userName");
            return (Criteria) this;
        }

        public Criteria andUserNameNotBetween(String value1, String value2) {
            addCriterion("user_name not between", value1, value2, "userName");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Integer value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Integer value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Integer value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Integer value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Integer value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Integer> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Integer> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Integer value1, Integer value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Integer value1, Integer value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIsNull() {
            addCriterion("company_name is null");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIsNotNull() {
            addCriterion("company_name is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyNameEqualTo(String value) {
            addCriterion("company_name =", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotEqualTo(String value) {
            addCriterion("company_name <>", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameGreaterThan(String value) {
            addCriterion("company_name >", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameGreaterThanOrEqualTo(String value) {
            addCriterion("company_name >=", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLessThan(String value) {
            addCriterion("company_name <", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLessThanOrEqualTo(String value) {
            addCriterion("company_name <=", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLike(String value) {
            addCriterion("company_name like", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotLike(String value) {
            addCriterion("company_name not like", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIn(List<String> values) {
            addCriterion("company_name in", values, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotIn(List<String> values) {
            addCriterion("company_name not in", values, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameBetween(String value1, String value2) {
            addCriterion("company_name between", value1, value2, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotBetween(String value1, String value2) {
            addCriterion("company_name not between", value1, value2, "companyName");
            return (Criteria) this;
        }

        public Criteria andSupplierAmountIsNull() {
            addCriterion("supplier_amount is null");
            return (Criteria) this;
        }

        public Criteria andSupplierAmountIsNotNull() {
            addCriterion("supplier_amount is not null");
            return (Criteria) this;
        }

        public Criteria andSupplierAmountEqualTo(BigDecimal value) {
            addCriterion("supplier_amount =", value, "supplierAmount");
            return (Criteria) this;
        }

        public Criteria andSupplierAmountNotEqualTo(BigDecimal value) {
            addCriterion("supplier_amount <>", value, "supplierAmount");
            return (Criteria) this;
        }

        public Criteria andSupplierAmountGreaterThan(BigDecimal value) {
            addCriterion("supplier_amount >", value, "supplierAmount");
            return (Criteria) this;
        }

        public Criteria andSupplierAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("supplier_amount >=", value, "supplierAmount");
            return (Criteria) this;
        }

        public Criteria andSupplierAmountLessThan(BigDecimal value) {
            addCriterion("supplier_amount <", value, "supplierAmount");
            return (Criteria) this;
        }

        public Criteria andSupplierAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("supplier_amount <=", value, "supplierAmount");
            return (Criteria) this;
        }

        public Criteria andSupplierAmountIn(List<BigDecimal> values) {
            addCriterion("supplier_amount in", values, "supplierAmount");
            return (Criteria) this;
        }

        public Criteria andSupplierAmountNotIn(List<BigDecimal> values) {
            addCriterion("supplier_amount not in", values, "supplierAmount");
            return (Criteria) this;
        }

        public Criteria andSupplierAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("supplier_amount between", value1, value2, "supplierAmount");
            return (Criteria) this;
        }

        public Criteria andSupplierAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("supplier_amount not between", value1, value2, "supplierAmount");
            return (Criteria) this;
        }

        public Criteria andSupplierAmountNoRateIsNull() {
            addCriterion("supplier_amount_no_rate is null");
            return (Criteria) this;
        }

        public Criteria andSupplierAmountNoRateIsNotNull() {
            addCriterion("supplier_amount_no_rate is not null");
            return (Criteria) this;
        }

        public Criteria andSupplierAmountNoRateEqualTo(BigDecimal value) {
            addCriterion("supplier_amount_no_rate =", value, "supplierAmountNoRate");
            return (Criteria) this;
        }

        public Criteria andSupplierAmountNoRateNotEqualTo(BigDecimal value) {
            addCriterion("supplier_amount_no_rate <>", value, "supplierAmountNoRate");
            return (Criteria) this;
        }

        public Criteria andSupplierAmountNoRateGreaterThan(BigDecimal value) {
            addCriterion("supplier_amount_no_rate >", value, "supplierAmountNoRate");
            return (Criteria) this;
        }

        public Criteria andSupplierAmountNoRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("supplier_amount_no_rate >=", value, "supplierAmountNoRate");
            return (Criteria) this;
        }

        public Criteria andSupplierAmountNoRateLessThan(BigDecimal value) {
            addCriterion("supplier_amount_no_rate <", value, "supplierAmountNoRate");
            return (Criteria) this;
        }

        public Criteria andSupplierAmountNoRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("supplier_amount_no_rate <=", value, "supplierAmountNoRate");
            return (Criteria) this;
        }

        public Criteria andSupplierAmountNoRateIn(List<BigDecimal> values) {
            addCriterion("supplier_amount_no_rate in", values, "supplierAmountNoRate");
            return (Criteria) this;
        }

        public Criteria andSupplierAmountNoRateNotIn(List<BigDecimal> values) {
            addCriterion("supplier_amount_no_rate not in", values, "supplierAmountNoRate");
            return (Criteria) this;
        }

        public Criteria andSupplierAmountNoRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("supplier_amount_no_rate between", value1, value2, "supplierAmountNoRate");
            return (Criteria) this;
        }

        public Criteria andSupplierAmountNoRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("supplier_amount_no_rate not between", value1, value2, "supplierAmountNoRate");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseIsNull() {
            addCriterion("vehicle_license is null");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseIsNotNull() {
            addCriterion("vehicle_license is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseEqualTo(String value) {
            addCriterion("vehicle_license =", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseNotEqualTo(String value) {
            addCriterion("vehicle_license <>", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseGreaterThan(String value) {
            addCriterion("vehicle_license >", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_license >=", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseLessThan(String value) {
            addCriterion("vehicle_license <", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseLessThanOrEqualTo(String value) {
            addCriterion("vehicle_license <=", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseLike(String value) {
            addCriterion("vehicle_license like", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseNotLike(String value) {
            addCriterion("vehicle_license not like", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseIn(List<String> values) {
            addCriterion("vehicle_license in", values, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseNotIn(List<String> values) {
            addCriterion("vehicle_license not in", values, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseBetween(String value1, String value2) {
            addCriterion("vehicle_license between", value1, value2, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseNotBetween(String value1, String value2) {
            addCriterion("vehicle_license not between", value1, value2, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andBillingTimeIsNull() {
            addCriterion("billing_time is null");
            return (Criteria) this;
        }

        public Criteria andBillingTimeIsNotNull() {
            addCriterion("billing_time is not null");
            return (Criteria) this;
        }

        public Criteria andBillingTimeEqualTo(Date value) {
            addCriterion("billing_time =", value, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeNotEqualTo(Date value) {
            addCriterion("billing_time <>", value, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeGreaterThan(Date value) {
            addCriterion("billing_time >", value, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("billing_time >=", value, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeLessThan(Date value) {
            addCriterion("billing_time <", value, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeLessThanOrEqualTo(Date value) {
            addCriterion("billing_time <=", value, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeIn(List<Date> values) {
            addCriterion("billing_time in", values, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeNotIn(List<Date> values) {
            addCriterion("billing_time not in", values, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeBetween(Date value1, Date value2) {
            addCriterion("billing_time between", value1, value2, "billingTime");
            return (Criteria) this;
        }

        public Criteria andBillingTimeNotBetween(Date value1, Date value2) {
            addCriterion("billing_time not between", value1, value2, "billingTime");
            return (Criteria) this;
        }

        public Criteria andSigningEntityCreditCodeIsNull() {
            addCriterion("signing_entity_credit_code is null");
            return (Criteria) this;
        }

        public Criteria andSigningEntityCreditCodeIsNotNull() {
            addCriterion("signing_entity_credit_code is not null");
            return (Criteria) this;
        }

        public Criteria andSigningEntityCreditCodeEqualTo(String value) {
            addCriterion("signing_entity_credit_code =", value, "signingEntityCreditCode");
            return (Criteria) this;
        }

        public Criteria andSigningEntityCreditCodeNotEqualTo(String value) {
            addCriterion("signing_entity_credit_code <>", value, "signingEntityCreditCode");
            return (Criteria) this;
        }

        public Criteria andSigningEntityCreditCodeGreaterThan(String value) {
            addCriterion("signing_entity_credit_code >", value, "signingEntityCreditCode");
            return (Criteria) this;
        }

        public Criteria andSigningEntityCreditCodeGreaterThanOrEqualTo(String value) {
            addCriterion("signing_entity_credit_code >=", value, "signingEntityCreditCode");
            return (Criteria) this;
        }

        public Criteria andSigningEntityCreditCodeLessThan(String value) {
            addCriterion("signing_entity_credit_code <", value, "signingEntityCreditCode");
            return (Criteria) this;
        }

        public Criteria andSigningEntityCreditCodeLessThanOrEqualTo(String value) {
            addCriterion("signing_entity_credit_code <=", value, "signingEntityCreditCode");
            return (Criteria) this;
        }

        public Criteria andSigningEntityCreditCodeLike(String value) {
            addCriterion("signing_entity_credit_code like", value, "signingEntityCreditCode");
            return (Criteria) this;
        }

        public Criteria andSigningEntityCreditCodeNotLike(String value) {
            addCriterion("signing_entity_credit_code not like", value, "signingEntityCreditCode");
            return (Criteria) this;
        }

        public Criteria andSigningEntityCreditCodeIn(List<String> values) {
            addCriterion("signing_entity_credit_code in", values, "signingEntityCreditCode");
            return (Criteria) this;
        }

        public Criteria andSigningEntityCreditCodeNotIn(List<String> values) {
            addCriterion("signing_entity_credit_code not in", values, "signingEntityCreditCode");
            return (Criteria) this;
        }

        public Criteria andSigningEntityCreditCodeBetween(String value1, String value2) {
            addCriterion("signing_entity_credit_code between", value1, value2, "signingEntityCreditCode");
            return (Criteria) this;
        }

        public Criteria andSigningEntityCreditCodeNotBetween(String value1, String value2) {
            addCriterion("signing_entity_credit_code not between", value1, value2, "signingEntityCreditCode");
            return (Criteria) this;
        }

        public Criteria andSigningEntityFinancialCodeIsNull() {
            addCriterion("signing_entity_financial_code is null");
            return (Criteria) this;
        }

        public Criteria andSigningEntityFinancialCodeIsNotNull() {
            addCriterion("signing_entity_financial_code is not null");
            return (Criteria) this;
        }

        public Criteria andSigningEntityFinancialCodeEqualTo(String value) {
            addCriterion("signing_entity_financial_code =", value, "signingEntityFinancialCode");
            return (Criteria) this;
        }

        public Criteria andSigningEntityFinancialCodeNotEqualTo(String value) {
            addCriterion("signing_entity_financial_code <>", value, "signingEntityFinancialCode");
            return (Criteria) this;
        }

        public Criteria andSigningEntityFinancialCodeGreaterThan(String value) {
            addCriterion("signing_entity_financial_code >", value, "signingEntityFinancialCode");
            return (Criteria) this;
        }

        public Criteria andSigningEntityFinancialCodeGreaterThanOrEqualTo(String value) {
            addCriterion("signing_entity_financial_code >=", value, "signingEntityFinancialCode");
            return (Criteria) this;
        }

        public Criteria andSigningEntityFinancialCodeLessThan(String value) {
            addCriterion("signing_entity_financial_code <", value, "signingEntityFinancialCode");
            return (Criteria) this;
        }

        public Criteria andSigningEntityFinancialCodeLessThanOrEqualTo(String value) {
            addCriterion("signing_entity_financial_code <=", value, "signingEntityFinancialCode");
            return (Criteria) this;
        }

        public Criteria andSigningEntityFinancialCodeLike(String value) {
            addCriterion("signing_entity_financial_code like", value, "signingEntityFinancialCode");
            return (Criteria) this;
        }

        public Criteria andSigningEntityFinancialCodeNotLike(String value) {
            addCriterion("signing_entity_financial_code not like", value, "signingEntityFinancialCode");
            return (Criteria) this;
        }

        public Criteria andSigningEntityFinancialCodeIn(List<String> values) {
            addCriterion("signing_entity_financial_code in", values, "signingEntityFinancialCode");
            return (Criteria) this;
        }

        public Criteria andSigningEntityFinancialCodeNotIn(List<String> values) {
            addCriterion("signing_entity_financial_code not in", values, "signingEntityFinancialCode");
            return (Criteria) this;
        }

        public Criteria andSigningEntityFinancialCodeBetween(String value1, String value2) {
            addCriterion("signing_entity_financial_code between", value1, value2, "signingEntityFinancialCode");
            return (Criteria) this;
        }

        public Criteria andSigningEntityFinancialCodeNotBetween(String value1, String value2) {
            addCriterion("signing_entity_financial_code not between", value1, value2, "signingEntityFinancialCode");
            return (Criteria) this;
        }

        public Criteria andSigningEntityFinancialNameIsNull() {
            addCriterion("signing_entity_financial_name is null");
            return (Criteria) this;
        }

        public Criteria andSigningEntityFinancialNameIsNotNull() {
            addCriterion("signing_entity_financial_name is not null");
            return (Criteria) this;
        }

        public Criteria andSigningEntityFinancialNameEqualTo(String value) {
            addCriterion("signing_entity_financial_name =", value, "signingEntityFinancialName");
            return (Criteria) this;
        }

        public Criteria andSigningEntityFinancialNameNotEqualTo(String value) {
            addCriterion("signing_entity_financial_name <>", value, "signingEntityFinancialName");
            return (Criteria) this;
        }

        public Criteria andSigningEntityFinancialNameGreaterThan(String value) {
            addCriterion("signing_entity_financial_name >", value, "signingEntityFinancialName");
            return (Criteria) this;
        }

        public Criteria andSigningEntityFinancialNameGreaterThanOrEqualTo(String value) {
            addCriterion("signing_entity_financial_name >=", value, "signingEntityFinancialName");
            return (Criteria) this;
        }

        public Criteria andSigningEntityFinancialNameLessThan(String value) {
            addCriterion("signing_entity_financial_name <", value, "signingEntityFinancialName");
            return (Criteria) this;
        }

        public Criteria andSigningEntityFinancialNameLessThanOrEqualTo(String value) {
            addCriterion("signing_entity_financial_name <=", value, "signingEntityFinancialName");
            return (Criteria) this;
        }

        public Criteria andSigningEntityFinancialNameLike(String value) {
            addCriterion("signing_entity_financial_name like", value, "signingEntityFinancialName");
            return (Criteria) this;
        }

        public Criteria andSigningEntityFinancialNameNotLike(String value) {
            addCriterion("signing_entity_financial_name not like", value, "signingEntityFinancialName");
            return (Criteria) this;
        }

        public Criteria andSigningEntityFinancialNameIn(List<String> values) {
            addCriterion("signing_entity_financial_name in", values, "signingEntityFinancialName");
            return (Criteria) this;
        }

        public Criteria andSigningEntityFinancialNameNotIn(List<String> values) {
            addCriterion("signing_entity_financial_name not in", values, "signingEntityFinancialName");
            return (Criteria) this;
        }

        public Criteria andSigningEntityFinancialNameBetween(String value1, String value2) {
            addCriterion("signing_entity_financial_name between", value1, value2, "signingEntityFinancialName");
            return (Criteria) this;
        }

        public Criteria andSigningEntityFinancialNameNotBetween(String value1, String value2) {
            addCriterion("signing_entity_financial_name not between", value1, value2, "signingEntityFinancialName");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}