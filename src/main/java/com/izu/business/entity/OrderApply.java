package com.izu.business.entity;

import java.math.BigDecimal;
import java.util.Date;

public class OrderApply {
    /**
     * 用车申请ID id
     */
    private Integer id;

    /**
     * 订单创建时间 create_time
     */
    private Date createTime;

    /**
     * 订单更新时间 update_time
     */
    private Date updateTime;

    /**
     * 用车申请编号 order_apply_no
     */
    private String orderApplyNo;

    /**
     * 下单人所属公司ID；非企业用户为0 company_id
     */
    private Integer companyId;

    /**
     * 所属组织结构ID struct_id
     */
    private Integer structId;

    /**
     * 订单类型：1：内部用车；2：商务用车；3：短租；4：约车；5：国际租车 order_type
     */
    private Byte orderType;

    /**
     * 服务类型编码：service_dictionary表service_code service_code
     */
    private String serviceCode;

    /**
     * 下单人ID customer_id
     */
    private Integer customerId;

    /**
     * 下单人姓名 customer_name
     */
    private String customerName;

    /**
     * 下单人电话 customer_mobile
     */
    private String customerMobile;

    /**
     * 业务人员ID business_id
     */
    private Integer businessId;

    /**
     * 业务人员姓名 business_name
     */
    private String businessName;

    /**
     * 是否本人用车：1：本人；2：他人 himself
     */
    private Boolean himself;

    /**
     * 约定乘车人ID booking_passenger_user_id
     */
    private Long bookingPassengerUserId;

    /**
     * 约定乘车人姓名 booking_passenger_user_name
     */
    private String bookingPassengerUserName;

    /**
     * 约定乘车人手机号码 booking_passenger_user_phone
     */
    private String bookingPassengerUserPhone;

    /**
     * 预约订单开始服务时间 booking_order_stime
     */
    private Date bookingOrderStime;

    /**
     * 预约订单结束服务时间 booking_order_etime
     */
    private Date bookingOrderEtime;

    /**
     * 预约开始行程长地址 booking_start_long_addr
     */
    private String bookingStartLongAddr;

    /**
     * 预约开始行程短地址 booking_start_short_addr
     */
    private String bookingStartShortAddr;

    /**
     * 预约开始行程坐标点(经度,纬度;经度,纬度 前百度后高德) booking_start_point
     */
    private String bookingStartPoint;

    /**
     * 预约终止行程长地址 booking_end_long_addr
     */
    private String bookingEndLongAddr;

    /**
     * 预约终止行程短地址 booking_end_short_addr
     */
    private String bookingEndShortAddr;

    /**
     * 预约终止行程坐标点(经度,纬度;经度,纬度 前百度后高德) booking_end_point
     */
    private String bookingEndPoint;

    /**
     * 预约开始行程城市编码 booking_start_city_code
     */
    private Integer bookingStartCityCode;

    /**
     * 预约开始行程城市名称 booking_start_city_name
     */
    private String bookingStartCityName;

    /**
     * 预约终止行程城市编码 booking_end_city_code
     */
    private Integer bookingEndCityCode;

    /**
     * 预约终止行程城市名称 booking_end_city_name
     */
    private String bookingEndCityName;

    /**
     * 渠道方的订单号 channel_order_code
     */
    private String channelOrderCode;

    /**
     * 预定车辆总数 booking_vehicle_total_count
     */
    private Integer bookingVehicleTotalCount;

    /**
     * 支付方式：1：企业付款；2：个人付款 pay_type
     */
    private Byte payType;

    /**
     * 下单备注 order_detail
     */
    private String orderDetail;

    /**
     * 联系人姓名 contact_name
     */
    private String contactName;

    /**
     * 联系人手机号 contact_mobile
     */
    private String contactMobile;

    /**
     * 优惠券ID coupon_id
     */
    private Long couponId;

    /**
     * 订单渠道来源（如:安卓下单(ANDROID)、OIS下单(IOS)、PC企业端下单(PC)） order_channel_source
     */
    private String orderChannelSource;

    /**
     * 订单状态(10调度中 20调度成功 30行程中 40行程已结束 50订单待确认 60已驳回 70待结算 90已完成 100已取消) order_status
     */
    private Short orderStatus;

    /**
     * 审批状态(5待审批 10审批通过 20审批未通过 100已取消) approval_status
     */
    private Short approvalStatus;

    /**
     * 订单取消类型（0:调度-超时自动取消，1:运力不足-自动取消） order_cancellation_type
     */
    private Byte orderCancellationType;

    /**
     * 订单取消单编号 order_cancel_no
     */
    private String orderCancelNo;

    /**
     * 订单取消时间 order_cancel_time
     */
    private Date orderCancelTime;

    /**
     * 是否已结算（0否 1是 2无需结算） order_settle_status
     */
    private Byte orderSettleStatus;

    /**
     * 订单结算单编号 order_settle_no
     */
    private String orderSettleNo;

    /**
     * 是否一口价；1：是；0：不是 fixed_price_valid
     */
    private Boolean fixedPriceValid;

    /**
     * 一口价金额 fixed_price
     */
    private BigDecimal fixedPrice;

    /**
     * 订单结算时间 order_settle_time
     */
    private Date orderSettleTime;

    /**
     * 交通号码类型（1：机场-航班号、2：高铁-车次号、3：火车站-车次号） traffic_type
     */
    private Byte trafficType;

    /**
     * 交通号码 traffic_number
     */
    private String trafficNumber;

    /**
     * 是否已发表评价 appraise_submited
     */
    private Boolean appraiseSubmited;

    /**
     * 发表评价时间 appraise_submit_time
     */
    private Date appraiseSubmitTime;

    /**
     * 用车人所在部门所属城市编码 custome_city_code
     */
    private String customeCityCode;

    /**
     * 用车人所在部门所属城市名称 custome_city_name
     */
    private String customeCityName;

    /**
     * 下单人所在部门所属城市编码 create_city_code
     */
    private String createCityCode;

    /**
     * 下单人所在部门所属城市名称 create_city_name
     */
    private String createCityName;

    /**
     * 实际订单用车企业ID customer_company_id
     */
    private Integer customerCompanyId;

    /**
     * 实际订单用车企业名称 customer_company_name
     */
    private String customerCompanyName;

    /**
     * 订单是否需要审核：0-无需；1-需要审批 audit_flag
     */
    private Byte auditFlag;

    /**
     * 预估订单行驶路程：单位米 estimate_distance
     */
    private Integer estimateDistance;

    /**
     * 下单企业名称 company_name
     */
    private String companyName;

    /**
     * 用车申请ID
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return id 
     */
    public Integer getId() {
        return id;
    }

    /**
     * 用车申请ID
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param id 
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 订单创建时间
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return create_time 
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 订单创建时间
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param createTime 
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 订单更新时间
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return update_time 
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 订单更新时间
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param updateTime 
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 用车申请编号
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return order_apply_no 
     */
    public String getOrderApplyNo() {
        return orderApplyNo;
    }

    /**
     * 用车申请编号
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param orderApplyNo 
     */
    public void setOrderApplyNo(String orderApplyNo) {
        this.orderApplyNo = orderApplyNo == null ? null : orderApplyNo.trim();
    }

    /**
     * 下单人所属公司ID；非企业用户为0
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return company_id 
     */
    public Integer getCompanyId() {
        return companyId;
    }

    /**
     * 下单人所属公司ID；非企业用户为0
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param companyId 
     */
    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    /**
     * 所属组织结构ID
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return struct_id 
     */
    public Integer getStructId() {
        return structId;
    }

    /**
     * 所属组织结构ID
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param structId 
     */
    public void setStructId(Integer structId) {
        this.structId = structId;
    }

    /**
     * 订单类型：1：内部用车；2：商务用车；3：短租；4：约车；5：国际租车
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return order_type 
     */
    public Byte getOrderType() {
        return orderType;
    }

    /**
     * 订单类型：1：内部用车；2：商务用车；3：短租；4：约车；5：国际租车
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param orderType 
     */
    public void setOrderType(Byte orderType) {
        this.orderType = orderType;
    }

    /**
     * 服务类型编码：service_dictionary表service_code
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return service_code 
     */
    public String getServiceCode() {
        return serviceCode;
    }

    /**
     * 服务类型编码：service_dictionary表service_code
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param serviceCode 
     */
    public void setServiceCode(String serviceCode) {
        this.serviceCode = serviceCode == null ? null : serviceCode.trim();
    }

    /**
     * 下单人ID
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return customer_id 
     */
    public Integer getCustomerId() {
        return customerId;
    }

    /**
     * 下单人ID
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param customerId 
     */
    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    /**
     * 下单人姓名
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return customer_name 
     */
    public String getCustomerName() {
        return customerName;
    }

    /**
     * 下单人姓名
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param customerName 
     */
    public void setCustomerName(String customerName) {
        this.customerName = customerName == null ? null : customerName.trim();
    }

    /**
     * 下单人电话
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return customer_mobile 
     */
    public String getCustomerMobile() {
        return customerMobile;
    }

    /**
     * 下单人电话
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param customerMobile 
     */
    public void setCustomerMobile(String customerMobile) {
        this.customerMobile = customerMobile == null ? null : customerMobile.trim();
    }

    /**
     * 业务人员ID
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return business_id 
     */
    public Integer getBusinessId() {
        return businessId;
    }

    /**
     * 业务人员ID
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param businessId 
     */
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    /**
     * 业务人员姓名
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return business_name 
     */
    public String getBusinessName() {
        return businessName;
    }

    /**
     * 业务人员姓名
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param businessName 
     */
    public void setBusinessName(String businessName) {
        this.businessName = businessName == null ? null : businessName.trim();
    }

    /**
     * 是否本人用车：1：本人；2：他人
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return himself 
     */
    public Boolean getHimself() {
        return himself;
    }

    /**
     * 是否本人用车：1：本人；2：他人
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param himself 
     */
    public void setHimself(Boolean himself) {
        this.himself = himself;
    }

    /**
     * 约定乘车人ID
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return booking_passenger_user_id 
     */
    public Long getBookingPassengerUserId() {
        return bookingPassengerUserId;
    }

    /**
     * 约定乘车人ID
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param bookingPassengerUserId 
     */
    public void setBookingPassengerUserId(Long bookingPassengerUserId) {
        this.bookingPassengerUserId = bookingPassengerUserId;
    }

    /**
     * 约定乘车人姓名
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return booking_passenger_user_name 
     */
    public String getBookingPassengerUserName() {
        return bookingPassengerUserName;
    }

    /**
     * 约定乘车人姓名
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param bookingPassengerUserName 
     */
    public void setBookingPassengerUserName(String bookingPassengerUserName) {
        this.bookingPassengerUserName = bookingPassengerUserName == null ? null : bookingPassengerUserName.trim();
    }

    /**
     * 约定乘车人手机号码
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return booking_passenger_user_phone 
     */
    public String getBookingPassengerUserPhone() {
        return bookingPassengerUserPhone;
    }

    /**
     * 约定乘车人手机号码
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param bookingPassengerUserPhone 
     */
    public void setBookingPassengerUserPhone(String bookingPassengerUserPhone) {
        this.bookingPassengerUserPhone = bookingPassengerUserPhone == null ? null : bookingPassengerUserPhone.trim();
    }

    /**
     * 预约订单开始服务时间
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return booking_order_stime 
     */
    public Date getBookingOrderStime() {
        return bookingOrderStime;
    }

    /**
     * 预约订单开始服务时间
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param bookingOrderStime 
     */
    public void setBookingOrderStime(Date bookingOrderStime) {
        this.bookingOrderStime = bookingOrderStime;
    }

    /**
     * 预约订单结束服务时间
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return booking_order_etime 
     */
    public Date getBookingOrderEtime() {
        return bookingOrderEtime;
    }

    /**
     * 预约订单结束服务时间
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param bookingOrderEtime 
     */
    public void setBookingOrderEtime(Date bookingOrderEtime) {
        this.bookingOrderEtime = bookingOrderEtime;
    }

    /**
     * 预约开始行程长地址
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return booking_start_long_addr 
     */
    public String getBookingStartLongAddr() {
        return bookingStartLongAddr;
    }

    /**
     * 预约开始行程长地址
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param bookingStartLongAddr 
     */
    public void setBookingStartLongAddr(String bookingStartLongAddr) {
        this.bookingStartLongAddr = bookingStartLongAddr == null ? null : bookingStartLongAddr.trim();
    }

    /**
     * 预约开始行程短地址
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return booking_start_short_addr 
     */
    public String getBookingStartShortAddr() {
        return bookingStartShortAddr;
    }

    /**
     * 预约开始行程短地址
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param bookingStartShortAddr 
     */
    public void setBookingStartShortAddr(String bookingStartShortAddr) {
        this.bookingStartShortAddr = bookingStartShortAddr == null ? null : bookingStartShortAddr.trim();
    }

    /**
     * 预约开始行程坐标点(经度,纬度;经度,纬度 前百度后高德)
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return booking_start_point 
     */
    public String getBookingStartPoint() {
        return bookingStartPoint;
    }

    /**
     * 预约开始行程坐标点(经度,纬度;经度,纬度 前百度后高德)
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param bookingStartPoint 
     */
    public void setBookingStartPoint(String bookingStartPoint) {
        this.bookingStartPoint = bookingStartPoint == null ? null : bookingStartPoint.trim();
    }

    /**
     * 预约终止行程长地址
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return booking_end_long_addr 
     */
    public String getBookingEndLongAddr() {
        return bookingEndLongAddr;
    }

    /**
     * 预约终止行程长地址
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param bookingEndLongAddr 
     */
    public void setBookingEndLongAddr(String bookingEndLongAddr) {
        this.bookingEndLongAddr = bookingEndLongAddr == null ? null : bookingEndLongAddr.trim();
    }

    /**
     * 预约终止行程短地址
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return booking_end_short_addr 
     */
    public String getBookingEndShortAddr() {
        return bookingEndShortAddr;
    }

    /**
     * 预约终止行程短地址
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param bookingEndShortAddr 
     */
    public void setBookingEndShortAddr(String bookingEndShortAddr) {
        this.bookingEndShortAddr = bookingEndShortAddr == null ? null : bookingEndShortAddr.trim();
    }

    /**
     * 预约终止行程坐标点(经度,纬度;经度,纬度 前百度后高德)
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return booking_end_point 
     */
    public String getBookingEndPoint() {
        return bookingEndPoint;
    }

    /**
     * 预约终止行程坐标点(经度,纬度;经度,纬度 前百度后高德)
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param bookingEndPoint 
     */
    public void setBookingEndPoint(String bookingEndPoint) {
        this.bookingEndPoint = bookingEndPoint == null ? null : bookingEndPoint.trim();
    }

    /**
     * 预约开始行程城市编码
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return booking_start_city_code 
     */
    public Integer getBookingStartCityCode() {
        return bookingStartCityCode;
    }

    /**
     * 预约开始行程城市编码
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param bookingStartCityCode 
     */
    public void setBookingStartCityCode(Integer bookingStartCityCode) {
        this.bookingStartCityCode = bookingStartCityCode;
    }

    /**
     * 预约开始行程城市名称
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return booking_start_city_name 
     */
    public String getBookingStartCityName() {
        return bookingStartCityName;
    }

    /**
     * 预约开始行程城市名称
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param bookingStartCityName 
     */
    public void setBookingStartCityName(String bookingStartCityName) {
        this.bookingStartCityName = bookingStartCityName == null ? null : bookingStartCityName.trim();
    }

    /**
     * 预约终止行程城市编码
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return booking_end_city_code 
     */
    public Integer getBookingEndCityCode() {
        return bookingEndCityCode;
    }

    /**
     * 预约终止行程城市编码
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param bookingEndCityCode 
     */
    public void setBookingEndCityCode(Integer bookingEndCityCode) {
        this.bookingEndCityCode = bookingEndCityCode;
    }

    /**
     * 预约终止行程城市名称
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return booking_end_city_name 
     */
    public String getBookingEndCityName() {
        return bookingEndCityName;
    }

    /**
     * 预约终止行程城市名称
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param bookingEndCityName 
     */
    public void setBookingEndCityName(String bookingEndCityName) {
        this.bookingEndCityName = bookingEndCityName == null ? null : bookingEndCityName.trim();
    }

    /**
     * 渠道方的订单号
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return channel_order_code 
     */
    public String getChannelOrderCode() {
        return channelOrderCode;
    }

    /**
     * 渠道方的订单号
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param channelOrderCode 
     */
    public void setChannelOrderCode(String channelOrderCode) {
        this.channelOrderCode = channelOrderCode == null ? null : channelOrderCode.trim();
    }

    /**
     * 预定车辆总数
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return booking_vehicle_total_count 
     */
    public Integer getBookingVehicleTotalCount() {
        return bookingVehicleTotalCount;
    }

    /**
     * 预定车辆总数
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param bookingVehicleTotalCount 
     */
    public void setBookingVehicleTotalCount(Integer bookingVehicleTotalCount) {
        this.bookingVehicleTotalCount = bookingVehicleTotalCount;
    }

    /**
     * 支付方式：1：企业付款；2：个人付款
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return pay_type 
     */
    public Byte getPayType() {
        return payType;
    }

    /**
     * 支付方式：1：企业付款；2：个人付款
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param payType 
     */
    public void setPayType(Byte payType) {
        this.payType = payType;
    }

    /**
     * 下单备注
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return order_detail 
     */
    public String getOrderDetail() {
        return orderDetail;
    }

    /**
     * 下单备注
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param orderDetail 
     */
    public void setOrderDetail(String orderDetail) {
        this.orderDetail = orderDetail == null ? null : orderDetail.trim();
    }

    /**
     * 联系人姓名
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return contact_name 
     */
    public String getContactName() {
        return contactName;
    }

    /**
     * 联系人姓名
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param contactName 
     */
    public void setContactName(String contactName) {
        this.contactName = contactName == null ? null : contactName.trim();
    }

    /**
     * 联系人手机号
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return contact_mobile 
     */
    public String getContactMobile() {
        return contactMobile;
    }

    /**
     * 联系人手机号
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param contactMobile 
     */
    public void setContactMobile(String contactMobile) {
        this.contactMobile = contactMobile == null ? null : contactMobile.trim();
    }

    /**
     * 优惠券ID
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return coupon_id 
     */
    public Long getCouponId() {
        return couponId;
    }

    /**
     * 优惠券ID
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param couponId 
     */
    public void setCouponId(Long couponId) {
        this.couponId = couponId;
    }

    /**
     * 订单渠道来源（如:安卓下单(ANDROID)、OIS下单(IOS)、PC企业端下单(PC)）
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return order_channel_source 
     */
    public String getOrderChannelSource() {
        return orderChannelSource;
    }

    /**
     * 订单渠道来源（如:安卓下单(ANDROID)、OIS下单(IOS)、PC企业端下单(PC)）
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param orderChannelSource 
     */
    public void setOrderChannelSource(String orderChannelSource) {
        this.orderChannelSource = orderChannelSource == null ? null : orderChannelSource.trim();
    }

    /**
     * 订单状态(10调度中 20调度成功 30行程中 40行程已结束 50订单待确认 60已驳回 70待结算 90已完成 100已取消)
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return order_status 
     */
    public Short getOrderStatus() {
        return orderStatus;
    }

    /**
     * 订单状态(10调度中 20调度成功 30行程中 40行程已结束 50订单待确认 60已驳回 70待结算 90已完成 100已取消)
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param orderStatus 
     */
    public void setOrderStatus(Short orderStatus) {
        this.orderStatus = orderStatus;
    }

    /**
     * 审批状态(5待审批 10审批通过 20审批未通过 100已取消)
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return approval_status 
     */
    public Short getApprovalStatus() {
        return approvalStatus;
    }

    /**
     * 审批状态(5待审批 10审批通过 20审批未通过 100已取消)
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param approvalStatus 
     */
    public void setApprovalStatus(Short approvalStatus) {
        this.approvalStatus = approvalStatus;
    }

    /**
     * 订单取消类型（0:调度-超时自动取消，1:运力不足-自动取消）
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return order_cancellation_type 
     */
    public Byte getOrderCancellationType() {
        return orderCancellationType;
    }

    /**
     * 订单取消类型（0:调度-超时自动取消，1:运力不足-自动取消）
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param orderCancellationType 
     */
    public void setOrderCancellationType(Byte orderCancellationType) {
        this.orderCancellationType = orderCancellationType;
    }

    /**
     * 订单取消单编号
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return order_cancel_no 
     */
    public String getOrderCancelNo() {
        return orderCancelNo;
    }

    /**
     * 订单取消单编号
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param orderCancelNo 
     */
    public void setOrderCancelNo(String orderCancelNo) {
        this.orderCancelNo = orderCancelNo == null ? null : orderCancelNo.trim();
    }

    /**
     * 订单取消时间
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return order_cancel_time 
     */
    public Date getOrderCancelTime() {
        return orderCancelTime;
    }

    /**
     * 订单取消时间
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param orderCancelTime 
     */
    public void setOrderCancelTime(Date orderCancelTime) {
        this.orderCancelTime = orderCancelTime;
    }

    /**
     * 是否已结算（0否 1是 2无需结算）
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return order_settle_status 
     */
    public Byte getOrderSettleStatus() {
        return orderSettleStatus;
    }

    /**
     * 是否已结算（0否 1是 2无需结算）
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param orderSettleStatus 
     */
    public void setOrderSettleStatus(Byte orderSettleStatus) {
        this.orderSettleStatus = orderSettleStatus;
    }

    /**
     * 订单结算单编号
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return order_settle_no 
     */
    public String getOrderSettleNo() {
        return orderSettleNo;
    }

    /**
     * 订单结算单编号
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param orderSettleNo 
     */
    public void setOrderSettleNo(String orderSettleNo) {
        this.orderSettleNo = orderSettleNo == null ? null : orderSettleNo.trim();
    }

    /**
     * 是否一口价；1：是；0：不是
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return fixed_price_valid 
     */
    public Boolean getFixedPriceValid() {
        return fixedPriceValid;
    }

    /**
     * 是否一口价；1：是；0：不是
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param fixedPriceValid 
     */
    public void setFixedPriceValid(Boolean fixedPriceValid) {
        this.fixedPriceValid = fixedPriceValid;
    }

    /**
     * 一口价金额
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return fixed_price 
     */
    public BigDecimal getFixedPrice() {
        return fixedPrice;
    }

    /**
     * 一口价金额
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param fixedPrice 
     */
    public void setFixedPrice(BigDecimal fixedPrice) {
        this.fixedPrice = fixedPrice;
    }

    /**
     * 订单结算时间
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return order_settle_time 
     */
    public Date getOrderSettleTime() {
        return orderSettleTime;
    }

    /**
     * 订单结算时间
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param orderSettleTime 
     */
    public void setOrderSettleTime(Date orderSettleTime) {
        this.orderSettleTime = orderSettleTime;
    }

    /**
     * 交通号码类型（1：机场-航班号、2：高铁-车次号、3：火车站-车次号）
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return traffic_type 
     */
    public Byte getTrafficType() {
        return trafficType;
    }

    /**
     * 交通号码类型（1：机场-航班号、2：高铁-车次号、3：火车站-车次号）
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param trafficType 
     */
    public void setTrafficType(Byte trafficType) {
        this.trafficType = trafficType;
    }

    /**
     * 交通号码
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return traffic_number 
     */
    public String getTrafficNumber() {
        return trafficNumber;
    }

    /**
     * 交通号码
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param trafficNumber 
     */
    public void setTrafficNumber(String trafficNumber) {
        this.trafficNumber = trafficNumber == null ? null : trafficNumber.trim();
    }

    /**
     * 是否已发表评价
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return appraise_submited 
     */
    public Boolean getAppraiseSubmited() {
        return appraiseSubmited;
    }

    /**
     * 是否已发表评价
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param appraiseSubmited 
     */
    public void setAppraiseSubmited(Boolean appraiseSubmited) {
        this.appraiseSubmited = appraiseSubmited;
    }

    /**
     * 发表评价时间
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return appraise_submit_time 
     */
    public Date getAppraiseSubmitTime() {
        return appraiseSubmitTime;
    }

    /**
     * 发表评价时间
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param appraiseSubmitTime 
     */
    public void setAppraiseSubmitTime(Date appraiseSubmitTime) {
        this.appraiseSubmitTime = appraiseSubmitTime;
    }

    /**
     * 用车人所在部门所属城市编码
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return custome_city_code 
     */
    public String getCustomeCityCode() {
        return customeCityCode;
    }

    /**
     * 用车人所在部门所属城市编码
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param customeCityCode 
     */
    public void setCustomeCityCode(String customeCityCode) {
        this.customeCityCode = customeCityCode == null ? null : customeCityCode.trim();
    }

    /**
     * 用车人所在部门所属城市名称
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return custome_city_name 
     */
    public String getCustomeCityName() {
        return customeCityName;
    }

    /**
     * 用车人所在部门所属城市名称
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param customeCityName 
     */
    public void setCustomeCityName(String customeCityName) {
        this.customeCityName = customeCityName == null ? null : customeCityName.trim();
    }

    /**
     * 下单人所在部门所属城市编码
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return create_city_code 
     */
    public String getCreateCityCode() {
        return createCityCode;
    }

    /**
     * 下单人所在部门所属城市编码
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param createCityCode 
     */
    public void setCreateCityCode(String createCityCode) {
        this.createCityCode = createCityCode == null ? null : createCityCode.trim();
    }

    /**
     * 下单人所在部门所属城市名称
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return create_city_name 
     */
    public String getCreateCityName() {
        return createCityName;
    }

    /**
     * 下单人所在部门所属城市名称
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param createCityName 
     */
    public void setCreateCityName(String createCityName) {
        this.createCityName = createCityName == null ? null : createCityName.trim();
    }

    /**
     * 实际订单用车企业ID
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return customer_company_id 
     */
    public Integer getCustomerCompanyId() {
        return customerCompanyId;
    }

    /**
     * 实际订单用车企业ID
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param customerCompanyId 
     */
    public void setCustomerCompanyId(Integer customerCompanyId) {
        this.customerCompanyId = customerCompanyId;
    }

    /**
     * 实际订单用车企业名称
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return customer_company_name 
     */
    public String getCustomerCompanyName() {
        return customerCompanyName;
    }

    /**
     * 实际订单用车企业名称
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param customerCompanyName 
     */
    public void setCustomerCompanyName(String customerCompanyName) {
        this.customerCompanyName = customerCompanyName == null ? null : customerCompanyName.trim();
    }

    /**
     * 订单是否需要审核：0-无需；1-需要审批
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return audit_flag 
     */
    public Byte getAuditFlag() {
        return auditFlag;
    }

    /**
     * 订单是否需要审核：0-无需；1-需要审批
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param auditFlag 
     */
    public void setAuditFlag(Byte auditFlag) {
        this.auditFlag = auditFlag;
    }

    /**
     * 预估订单行驶路程：单位米
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return estimate_distance 
     */
    public Integer getEstimateDistance() {
        return estimateDistance;
    }

    /**
     * 预估订单行驶路程：单位米
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param estimateDistance 
     */
    public void setEstimateDistance(Integer estimateDistance) {
        this.estimateDistance = estimateDistance;
    }

    /**
     * 下单企业名称
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @return company_name 
     */
    public String getCompanyName() {
        return companyName;
    }

    /**
     * 下单企业名称
     * <AUTHOR>
     * @date 2020-02-06 17:56:07
     * @param companyName 
     */
    public void setCompanyName(String companyName) {
        this.companyName = companyName == null ? null : companyName.trim();
    }
}