package com.izu.business.entity;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class LeaseContractBussExample {
    /**
     * lease_contract_buss
     */
    protected String orderByClause;

    /**
     * lease_contract_buss
     */
    protected boolean distinct;

    /**
     * lease_contract_buss
     */
    protected List<Criteria> oredCriteria;

    public LeaseContractBussExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBussContractIdIsNull() {
            addCriterion("buss_contract_id is null");
            return (Criteria) this;
        }

        public Criteria andBussContractIdIsNotNull() {
            addCriterion("buss_contract_id is not null");
            return (Criteria) this;
        }

        public Criteria andBussContractIdEqualTo(Integer value) {
            addCriterion("buss_contract_id =", value, "bussContractId");
            return (Criteria) this;
        }

        public Criteria andBussContractIdNotEqualTo(Integer value) {
            addCriterion("buss_contract_id <>", value, "bussContractId");
            return (Criteria) this;
        }

        public Criteria andBussContractIdGreaterThan(Integer value) {
            addCriterion("buss_contract_id >", value, "bussContractId");
            return (Criteria) this;
        }

        public Criteria andBussContractIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("buss_contract_id >=", value, "bussContractId");
            return (Criteria) this;
        }

        public Criteria andBussContractIdLessThan(Integer value) {
            addCriterion("buss_contract_id <", value, "bussContractId");
            return (Criteria) this;
        }

        public Criteria andBussContractIdLessThanOrEqualTo(Integer value) {
            addCriterion("buss_contract_id <=", value, "bussContractId");
            return (Criteria) this;
        }

        public Criteria andBussContractIdIn(List<Integer> values) {
            addCriterion("buss_contract_id in", values, "bussContractId");
            return (Criteria) this;
        }

        public Criteria andBussContractIdNotIn(List<Integer> values) {
            addCriterion("buss_contract_id not in", values, "bussContractId");
            return (Criteria) this;
        }

        public Criteria andBussContractIdBetween(Integer value1, Integer value2) {
            addCriterion("buss_contract_id between", value1, value2, "bussContractId");
            return (Criteria) this;
        }

        public Criteria andBussContractIdNotBetween(Integer value1, Integer value2) {
            addCriterion("buss_contract_id not between", value1, value2, "bussContractId");
            return (Criteria) this;
        }

        public Criteria andStandardContractIsNull() {
            addCriterion("standard_contract is null");
            return (Criteria) this;
        }

        public Criteria andStandardContractIsNotNull() {
            addCriterion("standard_contract is not null");
            return (Criteria) this;
        }

        public Criteria andStandardContractEqualTo(Integer value) {
            addCriterion("standard_contract =", value, "standardContract");
            return (Criteria) this;
        }

        public Criteria andStandardContractNotEqualTo(Integer value) {
            addCriterion("standard_contract <>", value, "standardContract");
            return (Criteria) this;
        }

        public Criteria andStandardContractGreaterThan(Integer value) {
            addCriterion("standard_contract >", value, "standardContract");
            return (Criteria) this;
        }

        public Criteria andStandardContractGreaterThanOrEqualTo(Integer value) {
            addCriterion("standard_contract >=", value, "standardContract");
            return (Criteria) this;
        }

        public Criteria andStandardContractLessThan(Integer value) {
            addCriterion("standard_contract <", value, "standardContract");
            return (Criteria) this;
        }

        public Criteria andStandardContractLessThanOrEqualTo(Integer value) {
            addCriterion("standard_contract <=", value, "standardContract");
            return (Criteria) this;
        }

        public Criteria andStandardContractIn(List<Integer> values) {
            addCriterion("standard_contract in", values, "standardContract");
            return (Criteria) this;
        }

        public Criteria andStandardContractNotIn(List<Integer> values) {
            addCriterion("standard_contract not in", values, "standardContract");
            return (Criteria) this;
        }

        public Criteria andStandardContractBetween(Integer value1, Integer value2) {
            addCriterion("standard_contract between", value1, value2, "standardContract");
            return (Criteria) this;
        }

        public Criteria andStandardContractNotBetween(Integer value1, Integer value2) {
            addCriterion("standard_contract not between", value1, value2, "standardContract");
            return (Criteria) this;
        }

        public Criteria andContractCodeIsNull() {
            addCriterion("contract_code is null");
            return (Criteria) this;
        }

        public Criteria andContractCodeIsNotNull() {
            addCriterion("contract_code is not null");
            return (Criteria) this;
        }

        public Criteria andContractCodeEqualTo(String value) {
            addCriterion("contract_code =", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotEqualTo(String value) {
            addCriterion("contract_code <>", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeGreaterThan(String value) {
            addCriterion("contract_code >", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeGreaterThanOrEqualTo(String value) {
            addCriterion("contract_code >=", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeLessThan(String value) {
            addCriterion("contract_code <", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeLessThanOrEqualTo(String value) {
            addCriterion("contract_code <=", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeLike(String value) {
            addCriterion("contract_code like", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotLike(String value) {
            addCriterion("contract_code not like", value, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeIn(List<String> values) {
            addCriterion("contract_code in", values, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotIn(List<String> values) {
            addCriterion("contract_code not in", values, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeBetween(String value1, String value2) {
            addCriterion("contract_code between", value1, value2, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractCodeNotBetween(String value1, String value2) {
            addCriterion("contract_code not between", value1, value2, "contractCode");
            return (Criteria) this;
        }

        public Criteria andContractTypeIsNull() {
            addCriterion("contract_type is null");
            return (Criteria) this;
        }

        public Criteria andContractTypeIsNotNull() {
            addCriterion("contract_type is not null");
            return (Criteria) this;
        }

        public Criteria andContractTypeEqualTo(Integer value) {
            addCriterion("contract_type =", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeNotEqualTo(Integer value) {
            addCriterion("contract_type <>", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeGreaterThan(Integer value) {
            addCriterion("contract_type >", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("contract_type >=", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeLessThan(Integer value) {
            addCriterion("contract_type <", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeLessThanOrEqualTo(Integer value) {
            addCriterion("contract_type <=", value, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeIn(List<Integer> values) {
            addCriterion("contract_type in", values, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeNotIn(List<Integer> values) {
            addCriterion("contract_type not in", values, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeBetween(Integer value1, Integer value2) {
            addCriterion("contract_type between", value1, value2, "contractType");
            return (Criteria) this;
        }

        public Criteria andContractTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("contract_type not between", value1, value2, "contractType");
            return (Criteria) this;
        }

        public Criteria andMainContractCodeIsNull() {
            addCriterion("main_contract_code is null");
            return (Criteria) this;
        }

        public Criteria andMainContractCodeIsNotNull() {
            addCriterion("main_contract_code is not null");
            return (Criteria) this;
        }

        public Criteria andMainContractCodeEqualTo(String value) {
            addCriterion("main_contract_code =", value, "mainContractCode");
            return (Criteria) this;
        }

        public Criteria andMainContractCodeNotEqualTo(String value) {
            addCriterion("main_contract_code <>", value, "mainContractCode");
            return (Criteria) this;
        }

        public Criteria andMainContractCodeGreaterThan(String value) {
            addCriterion("main_contract_code >", value, "mainContractCode");
            return (Criteria) this;
        }

        public Criteria andMainContractCodeGreaterThanOrEqualTo(String value) {
            addCriterion("main_contract_code >=", value, "mainContractCode");
            return (Criteria) this;
        }

        public Criteria andMainContractCodeLessThan(String value) {
            addCriterion("main_contract_code <", value, "mainContractCode");
            return (Criteria) this;
        }

        public Criteria andMainContractCodeLessThanOrEqualTo(String value) {
            addCriterion("main_contract_code <=", value, "mainContractCode");
            return (Criteria) this;
        }

        public Criteria andMainContractCodeLike(String value) {
            addCriterion("main_contract_code like", value, "mainContractCode");
            return (Criteria) this;
        }

        public Criteria andMainContractCodeNotLike(String value) {
            addCriterion("main_contract_code not like", value, "mainContractCode");
            return (Criteria) this;
        }

        public Criteria andMainContractCodeIn(List<String> values) {
            addCriterion("main_contract_code in", values, "mainContractCode");
            return (Criteria) this;
        }

        public Criteria andMainContractCodeNotIn(List<String> values) {
            addCriterion("main_contract_code not in", values, "mainContractCode");
            return (Criteria) this;
        }

        public Criteria andMainContractCodeBetween(String value1, String value2) {
            addCriterion("main_contract_code between", value1, value2, "mainContractCode");
            return (Criteria) this;
        }

        public Criteria andMainContractCodeNotBetween(String value1, String value2) {
            addCriterion("main_contract_code not between", value1, value2, "mainContractCode");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNull() {
            addCriterion("customer_id is null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNotNull() {
            addCriterion("customer_id is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdEqualTo(Integer value) {
            addCriterion("customer_id =", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotEqualTo(Integer value) {
            addCriterion("customer_id <>", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThan(Integer value) {
            addCriterion("customer_id >", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("customer_id >=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThan(Integer value) {
            addCriterion("customer_id <", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThanOrEqualTo(Integer value) {
            addCriterion("customer_id <=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIn(List<Integer> values) {
            addCriterion("customer_id in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotIn(List<Integer> values) {
            addCriterion("customer_id not in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdBetween(Integer value1, Integer value2) {
            addCriterion("customer_id between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotBetween(Integer value1, Integer value2) {
            addCriterion("customer_id not between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeIsNull() {
            addCriterion("customer_code is null");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeIsNotNull() {
            addCriterion("customer_code is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeEqualTo(String value) {
            addCriterion("customer_code =", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotEqualTo(String value) {
            addCriterion("customer_code <>", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeGreaterThan(String value) {
            addCriterion("customer_code >", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeGreaterThanOrEqualTo(String value) {
            addCriterion("customer_code >=", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeLessThan(String value) {
            addCriterion("customer_code <", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeLessThanOrEqualTo(String value) {
            addCriterion("customer_code <=", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeLike(String value) {
            addCriterion("customer_code like", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotLike(String value) {
            addCriterion("customer_code not like", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeIn(List<String> values) {
            addCriterion("customer_code in", values, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotIn(List<String> values) {
            addCriterion("customer_code not in", values, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeBetween(String value1, String value2) {
            addCriterion("customer_code between", value1, value2, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotBetween(String value1, String value2) {
            addCriterion("customer_code not between", value1, value2, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNull() {
            addCriterion("customer_name is null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNotNull() {
            addCriterion("customer_name is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameEqualTo(String value) {
            addCriterion("customer_name =", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotEqualTo(String value) {
            addCriterion("customer_name <>", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThan(String value) {
            addCriterion("customer_name >", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThanOrEqualTo(String value) {
            addCriterion("customer_name >=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThan(String value) {
            addCriterion("customer_name <", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThanOrEqualTo(String value) {
            addCriterion("customer_name <=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLike(String value) {
            addCriterion("customer_name like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotLike(String value) {
            addCriterion("customer_name not like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIn(List<String> values) {
            addCriterion("customer_name in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotIn(List<String> values) {
            addCriterion("customer_name not in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameBetween(String value1, String value2) {
            addCriterion("customer_name between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotBetween(String value1, String value2) {
            addCriterion("customer_name not between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andLeasePeriodIsNull() {
            addCriterion("lease_period is null");
            return (Criteria) this;
        }

        public Criteria andLeasePeriodIsNotNull() {
            addCriterion("lease_period is not null");
            return (Criteria) this;
        }

        public Criteria andLeasePeriodEqualTo(Integer value) {
            addCriterion("lease_period =", value, "leasePeriod");
            return (Criteria) this;
        }

        public Criteria andLeasePeriodNotEqualTo(Integer value) {
            addCriterion("lease_period <>", value, "leasePeriod");
            return (Criteria) this;
        }

        public Criteria andLeasePeriodGreaterThan(Integer value) {
            addCriterion("lease_period >", value, "leasePeriod");
            return (Criteria) this;
        }

        public Criteria andLeasePeriodGreaterThanOrEqualTo(Integer value) {
            addCriterion("lease_period >=", value, "leasePeriod");
            return (Criteria) this;
        }

        public Criteria andLeasePeriodLessThan(Integer value) {
            addCriterion("lease_period <", value, "leasePeriod");
            return (Criteria) this;
        }

        public Criteria andLeasePeriodLessThanOrEqualTo(Integer value) {
            addCriterion("lease_period <=", value, "leasePeriod");
            return (Criteria) this;
        }

        public Criteria andLeasePeriodIn(List<Integer> values) {
            addCriterion("lease_period in", values, "leasePeriod");
            return (Criteria) this;
        }

        public Criteria andLeasePeriodNotIn(List<Integer> values) {
            addCriterion("lease_period not in", values, "leasePeriod");
            return (Criteria) this;
        }

        public Criteria andLeasePeriodBetween(Integer value1, Integer value2) {
            addCriterion("lease_period between", value1, value2, "leasePeriod");
            return (Criteria) this;
        }

        public Criteria andLeasePeriodNotBetween(Integer value1, Integer value2) {
            addCriterion("lease_period not between", value1, value2, "leasePeriod");
            return (Criteria) this;
        }

        public Criteria andContractSdateIsNull() {
            addCriterion("contract_sdate is null");
            return (Criteria) this;
        }

        public Criteria andContractSdateIsNotNull() {
            addCriterion("contract_sdate is not null");
            return (Criteria) this;
        }

        public Criteria andContractSdateEqualTo(Date value) {
            addCriterionForJDBCDate("contract_sdate =", value, "contractSdate");
            return (Criteria) this;
        }

        public Criteria andContractSdateNotEqualTo(Date value) {
            addCriterionForJDBCDate("contract_sdate <>", value, "contractSdate");
            return (Criteria) this;
        }

        public Criteria andContractSdateGreaterThan(Date value) {
            addCriterionForJDBCDate("contract_sdate >", value, "contractSdate");
            return (Criteria) this;
        }

        public Criteria andContractSdateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("contract_sdate >=", value, "contractSdate");
            return (Criteria) this;
        }

        public Criteria andContractSdateLessThan(Date value) {
            addCriterionForJDBCDate("contract_sdate <", value, "contractSdate");
            return (Criteria) this;
        }

        public Criteria andContractSdateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("contract_sdate <=", value, "contractSdate");
            return (Criteria) this;
        }

        public Criteria andContractSdateIn(List<Date> values) {
            addCriterionForJDBCDate("contract_sdate in", values, "contractSdate");
            return (Criteria) this;
        }

        public Criteria andContractSdateNotIn(List<Date> values) {
            addCriterionForJDBCDate("contract_sdate not in", values, "contractSdate");
            return (Criteria) this;
        }

        public Criteria andContractSdateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("contract_sdate between", value1, value2, "contractSdate");
            return (Criteria) this;
        }

        public Criteria andContractSdateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("contract_sdate not between", value1, value2, "contractSdate");
            return (Criteria) this;
        }

        public Criteria andContractEdateIsNull() {
            addCriterion("contract_edate is null");
            return (Criteria) this;
        }

        public Criteria andContractEdateIsNotNull() {
            addCriterion("contract_edate is not null");
            return (Criteria) this;
        }

        public Criteria andContractEdateEqualTo(Date value) {
            addCriterionForJDBCDate("contract_edate =", value, "contractEdate");
            return (Criteria) this;
        }

        public Criteria andContractEdateNotEqualTo(Date value) {
            addCriterionForJDBCDate("contract_edate <>", value, "contractEdate");
            return (Criteria) this;
        }

        public Criteria andContractEdateGreaterThan(Date value) {
            addCriterionForJDBCDate("contract_edate >", value, "contractEdate");
            return (Criteria) this;
        }

        public Criteria andContractEdateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("contract_edate >=", value, "contractEdate");
            return (Criteria) this;
        }

        public Criteria andContractEdateLessThan(Date value) {
            addCriterionForJDBCDate("contract_edate <", value, "contractEdate");
            return (Criteria) this;
        }

        public Criteria andContractEdateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("contract_edate <=", value, "contractEdate");
            return (Criteria) this;
        }

        public Criteria andContractEdateIn(List<Date> values) {
            addCriterionForJDBCDate("contract_edate in", values, "contractEdate");
            return (Criteria) this;
        }

        public Criteria andContractEdateNotIn(List<Date> values) {
            addCriterionForJDBCDate("contract_edate not in", values, "contractEdate");
            return (Criteria) this;
        }

        public Criteria andContractEdateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("contract_edate between", value1, value2, "contractEdate");
            return (Criteria) this;
        }

        public Criteria andContractEdateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("contract_edate not between", value1, value2, "contractEdate");
            return (Criteria) this;
        }

        public Criteria andPayPeriodIsNull() {
            addCriterion("pay_period is null");
            return (Criteria) this;
        }

        public Criteria andPayPeriodIsNotNull() {
            addCriterion("pay_period is not null");
            return (Criteria) this;
        }

        public Criteria andPayPeriodEqualTo(Integer value) {
            addCriterion("pay_period =", value, "payPeriod");
            return (Criteria) this;
        }

        public Criteria andPayPeriodNotEqualTo(Integer value) {
            addCriterion("pay_period <>", value, "payPeriod");
            return (Criteria) this;
        }

        public Criteria andPayPeriodGreaterThan(Integer value) {
            addCriterion("pay_period >", value, "payPeriod");
            return (Criteria) this;
        }

        public Criteria andPayPeriodGreaterThanOrEqualTo(Integer value) {
            addCriterion("pay_period >=", value, "payPeriod");
            return (Criteria) this;
        }

        public Criteria andPayPeriodLessThan(Integer value) {
            addCriterion("pay_period <", value, "payPeriod");
            return (Criteria) this;
        }

        public Criteria andPayPeriodLessThanOrEqualTo(Integer value) {
            addCriterion("pay_period <=", value, "payPeriod");
            return (Criteria) this;
        }

        public Criteria andPayPeriodIn(List<Integer> values) {
            addCriterion("pay_period in", values, "payPeriod");
            return (Criteria) this;
        }

        public Criteria andPayPeriodNotIn(List<Integer> values) {
            addCriterion("pay_period not in", values, "payPeriod");
            return (Criteria) this;
        }

        public Criteria andPayPeriodBetween(Integer value1, Integer value2) {
            addCriterion("pay_period between", value1, value2, "payPeriod");
            return (Criteria) this;
        }

        public Criteria andPayPeriodNotBetween(Integer value1, Integer value2) {
            addCriterion("pay_period not between", value1, value2, "payPeriod");
            return (Criteria) this;
        }

        public Criteria andContractStatusIsNull() {
            addCriterion("contract_status is null");
            return (Criteria) this;
        }

        public Criteria andContractStatusIsNotNull() {
            addCriterion("contract_status is not null");
            return (Criteria) this;
        }

        public Criteria andContractStatusEqualTo(Integer value) {
            addCriterion("contract_status =", value, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusNotEqualTo(Integer value) {
            addCriterion("contract_status <>", value, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusGreaterThan(Integer value) {
            addCriterion("contract_status >", value, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("contract_status >=", value, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusLessThan(Integer value) {
            addCriterion("contract_status <", value, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusLessThanOrEqualTo(Integer value) {
            addCriterion("contract_status <=", value, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusIn(List<Integer> values) {
            addCriterion("contract_status in", values, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusNotIn(List<Integer> values) {
            addCriterion("contract_status not in", values, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusBetween(Integer value1, Integer value2) {
            addCriterion("contract_status between", value1, value2, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andContractStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("contract_status not between", value1, value2, "contractStatus");
            return (Criteria) this;
        }

        public Criteria andBussStatusIsNull() {
            addCriterion("buss_status is null");
            return (Criteria) this;
        }

        public Criteria andBussStatusIsNotNull() {
            addCriterion("buss_status is not null");
            return (Criteria) this;
        }

        public Criteria andBussStatusEqualTo(Integer value) {
            addCriterion("buss_status =", value, "bussStatus");
            return (Criteria) this;
        }

        public Criteria andBussStatusNotEqualTo(Integer value) {
            addCriterion("buss_status <>", value, "bussStatus");
            return (Criteria) this;
        }

        public Criteria andBussStatusGreaterThan(Integer value) {
            addCriterion("buss_status >", value, "bussStatus");
            return (Criteria) this;
        }

        public Criteria andBussStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("buss_status >=", value, "bussStatus");
            return (Criteria) this;
        }

        public Criteria andBussStatusLessThan(Integer value) {
            addCriterion("buss_status <", value, "bussStatus");
            return (Criteria) this;
        }

        public Criteria andBussStatusLessThanOrEqualTo(Integer value) {
            addCriterion("buss_status <=", value, "bussStatus");
            return (Criteria) this;
        }

        public Criteria andBussStatusIn(List<Integer> values) {
            addCriterion("buss_status in", values, "bussStatus");
            return (Criteria) this;
        }

        public Criteria andBussStatusNotIn(List<Integer> values) {
            addCriterion("buss_status not in", values, "bussStatus");
            return (Criteria) this;
        }

        public Criteria andBussStatusBetween(Integer value1, Integer value2) {
            addCriterion("buss_status between", value1, value2, "bussStatus");
            return (Criteria) this;
        }

        public Criteria andBussStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("buss_status not between", value1, value2, "bussStatus");
            return (Criteria) this;
        }

        public Criteria andTotalRentIsNull() {
            addCriterion("total_rent is null");
            return (Criteria) this;
        }

        public Criteria andTotalRentIsNotNull() {
            addCriterion("total_rent is not null");
            return (Criteria) this;
        }

        public Criteria andTotalRentEqualTo(BigDecimal value) {
            addCriterion("total_rent =", value, "totalRent");
            return (Criteria) this;
        }

        public Criteria andTotalRentNotEqualTo(BigDecimal value) {
            addCriterion("total_rent <>", value, "totalRent");
            return (Criteria) this;
        }

        public Criteria andTotalRentGreaterThan(BigDecimal value) {
            addCriterion("total_rent >", value, "totalRent");
            return (Criteria) this;
        }

        public Criteria andTotalRentGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_rent >=", value, "totalRent");
            return (Criteria) this;
        }

        public Criteria andTotalRentLessThan(BigDecimal value) {
            addCriterion("total_rent <", value, "totalRent");
            return (Criteria) this;
        }

        public Criteria andTotalRentLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_rent <=", value, "totalRent");
            return (Criteria) this;
        }

        public Criteria andTotalRentIn(List<BigDecimal> values) {
            addCriterion("total_rent in", values, "totalRent");
            return (Criteria) this;
        }

        public Criteria andTotalRentNotIn(List<BigDecimal> values) {
            addCriterion("total_rent not in", values, "totalRent");
            return (Criteria) this;
        }

        public Criteria andTotalRentBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_rent between", value1, value2, "totalRent");
            return (Criteria) this;
        }

        public Criteria andTotalRentNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_rent not between", value1, value2, "totalRent");
            return (Criteria) this;
        }

        public Criteria andTotalDepositIsNull() {
            addCriterion("total_deposit is null");
            return (Criteria) this;
        }

        public Criteria andTotalDepositIsNotNull() {
            addCriterion("total_deposit is not null");
            return (Criteria) this;
        }

        public Criteria andTotalDepositEqualTo(BigDecimal value) {
            addCriterion("total_deposit =", value, "totalDeposit");
            return (Criteria) this;
        }

        public Criteria andTotalDepositNotEqualTo(BigDecimal value) {
            addCriterion("total_deposit <>", value, "totalDeposit");
            return (Criteria) this;
        }

        public Criteria andTotalDepositGreaterThan(BigDecimal value) {
            addCriterion("total_deposit >", value, "totalDeposit");
            return (Criteria) this;
        }

        public Criteria andTotalDepositGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_deposit >=", value, "totalDeposit");
            return (Criteria) this;
        }

        public Criteria andTotalDepositLessThan(BigDecimal value) {
            addCriterion("total_deposit <", value, "totalDeposit");
            return (Criteria) this;
        }

        public Criteria andTotalDepositLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_deposit <=", value, "totalDeposit");
            return (Criteria) this;
        }

        public Criteria andTotalDepositIn(List<BigDecimal> values) {
            addCriterion("total_deposit in", values, "totalDeposit");
            return (Criteria) this;
        }

        public Criteria andTotalDepositNotIn(List<BigDecimal> values) {
            addCriterion("total_deposit not in", values, "totalDeposit");
            return (Criteria) this;
        }

        public Criteria andTotalDepositBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_deposit between", value1, value2, "totalDeposit");
            return (Criteria) this;
        }

        public Criteria andTotalDepositNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_deposit not between", value1, value2, "totalDeposit");
            return (Criteria) this;
        }

        public Criteria andMaintainIdIsNull() {
            addCriterion("maintain_id is null");
            return (Criteria) this;
        }

        public Criteria andMaintainIdIsNotNull() {
            addCriterion("maintain_id is not null");
            return (Criteria) this;
        }

        public Criteria andMaintainIdEqualTo(String value) {
            addCriterion("maintain_id =", value, "maintainId");
            return (Criteria) this;
        }

        public Criteria andMaintainIdNotEqualTo(String value) {
            addCriterion("maintain_id <>", value, "maintainId");
            return (Criteria) this;
        }

        public Criteria andMaintainIdGreaterThan(String value) {
            addCriterion("maintain_id >", value, "maintainId");
            return (Criteria) this;
        }

        public Criteria andMaintainIdGreaterThanOrEqualTo(String value) {
            addCriterion("maintain_id >=", value, "maintainId");
            return (Criteria) this;
        }

        public Criteria andMaintainIdLessThan(String value) {
            addCriterion("maintain_id <", value, "maintainId");
            return (Criteria) this;
        }

        public Criteria andMaintainIdLessThanOrEqualTo(String value) {
            addCriterion("maintain_id <=", value, "maintainId");
            return (Criteria) this;
        }

        public Criteria andMaintainIdLike(String value) {
            addCriterion("maintain_id like", value, "maintainId");
            return (Criteria) this;
        }

        public Criteria andMaintainIdNotLike(String value) {
            addCriterion("maintain_id not like", value, "maintainId");
            return (Criteria) this;
        }

        public Criteria andMaintainIdIn(List<String> values) {
            addCriterion("maintain_id in", values, "maintainId");
            return (Criteria) this;
        }

        public Criteria andMaintainIdNotIn(List<String> values) {
            addCriterion("maintain_id not in", values, "maintainId");
            return (Criteria) this;
        }

        public Criteria andMaintainIdBetween(String value1, String value2) {
            addCriterion("maintain_id between", value1, value2, "maintainId");
            return (Criteria) this;
        }

        public Criteria andMaintainIdNotBetween(String value1, String value2) {
            addCriterion("maintain_id not between", value1, value2, "maintainId");
            return (Criteria) this;
        }

        public Criteria andMaintainNameIsNull() {
            addCriterion("maintain_name is null");
            return (Criteria) this;
        }

        public Criteria andMaintainNameIsNotNull() {
            addCriterion("maintain_name is not null");
            return (Criteria) this;
        }

        public Criteria andMaintainNameEqualTo(String value) {
            addCriterion("maintain_name =", value, "maintainName");
            return (Criteria) this;
        }

        public Criteria andMaintainNameNotEqualTo(String value) {
            addCriterion("maintain_name <>", value, "maintainName");
            return (Criteria) this;
        }

        public Criteria andMaintainNameGreaterThan(String value) {
            addCriterion("maintain_name >", value, "maintainName");
            return (Criteria) this;
        }

        public Criteria andMaintainNameGreaterThanOrEqualTo(String value) {
            addCriterion("maintain_name >=", value, "maintainName");
            return (Criteria) this;
        }

        public Criteria andMaintainNameLessThan(String value) {
            addCriterion("maintain_name <", value, "maintainName");
            return (Criteria) this;
        }

        public Criteria andMaintainNameLessThanOrEqualTo(String value) {
            addCriterion("maintain_name <=", value, "maintainName");
            return (Criteria) this;
        }

        public Criteria andMaintainNameLike(String value) {
            addCriterion("maintain_name like", value, "maintainName");
            return (Criteria) this;
        }

        public Criteria andMaintainNameNotLike(String value) {
            addCriterion("maintain_name not like", value, "maintainName");
            return (Criteria) this;
        }

        public Criteria andMaintainNameIn(List<String> values) {
            addCriterion("maintain_name in", values, "maintainName");
            return (Criteria) this;
        }

        public Criteria andMaintainNameNotIn(List<String> values) {
            addCriterion("maintain_name not in", values, "maintainName");
            return (Criteria) this;
        }

        public Criteria andMaintainNameBetween(String value1, String value2) {
            addCriterion("maintain_name between", value1, value2, "maintainName");
            return (Criteria) this;
        }

        public Criteria andMaintainNameNotBetween(String value1, String value2) {
            addCriterion("maintain_name not between", value1, value2, "maintainName");
            return (Criteria) this;
        }

        public Criteria andSignSalesIdIsNull() {
            addCriterion("sign_sales_id is null");
            return (Criteria) this;
        }

        public Criteria andSignSalesIdIsNotNull() {
            addCriterion("sign_sales_id is not null");
            return (Criteria) this;
        }

        public Criteria andSignSalesIdEqualTo(String value) {
            addCriterion("sign_sales_id =", value, "signSalesId");
            return (Criteria) this;
        }

        public Criteria andSignSalesIdNotEqualTo(String value) {
            addCriterion("sign_sales_id <>", value, "signSalesId");
            return (Criteria) this;
        }

        public Criteria andSignSalesIdGreaterThan(String value) {
            addCriterion("sign_sales_id >", value, "signSalesId");
            return (Criteria) this;
        }

        public Criteria andSignSalesIdGreaterThanOrEqualTo(String value) {
            addCriterion("sign_sales_id >=", value, "signSalesId");
            return (Criteria) this;
        }

        public Criteria andSignSalesIdLessThan(String value) {
            addCriterion("sign_sales_id <", value, "signSalesId");
            return (Criteria) this;
        }

        public Criteria andSignSalesIdLessThanOrEqualTo(String value) {
            addCriterion("sign_sales_id <=", value, "signSalesId");
            return (Criteria) this;
        }

        public Criteria andSignSalesIdLike(String value) {
            addCriterion("sign_sales_id like", value, "signSalesId");
            return (Criteria) this;
        }

        public Criteria andSignSalesIdNotLike(String value) {
            addCriterion("sign_sales_id not like", value, "signSalesId");
            return (Criteria) this;
        }

        public Criteria andSignSalesIdIn(List<String> values) {
            addCriterion("sign_sales_id in", values, "signSalesId");
            return (Criteria) this;
        }

        public Criteria andSignSalesIdNotIn(List<String> values) {
            addCriterion("sign_sales_id not in", values, "signSalesId");
            return (Criteria) this;
        }

        public Criteria andSignSalesIdBetween(String value1, String value2) {
            addCriterion("sign_sales_id between", value1, value2, "signSalesId");
            return (Criteria) this;
        }

        public Criteria andSignSalesIdNotBetween(String value1, String value2) {
            addCriterion("sign_sales_id not between", value1, value2, "signSalesId");
            return (Criteria) this;
        }

        public Criteria andSignSalesNameIsNull() {
            addCriterion("sign_sales_name is null");
            return (Criteria) this;
        }

        public Criteria andSignSalesNameIsNotNull() {
            addCriterion("sign_sales_name is not null");
            return (Criteria) this;
        }

        public Criteria andSignSalesNameEqualTo(String value) {
            addCriterion("sign_sales_name =", value, "signSalesName");
            return (Criteria) this;
        }

        public Criteria andSignSalesNameNotEqualTo(String value) {
            addCriterion("sign_sales_name <>", value, "signSalesName");
            return (Criteria) this;
        }

        public Criteria andSignSalesNameGreaterThan(String value) {
            addCriterion("sign_sales_name >", value, "signSalesName");
            return (Criteria) this;
        }

        public Criteria andSignSalesNameGreaterThanOrEqualTo(String value) {
            addCriterion("sign_sales_name >=", value, "signSalesName");
            return (Criteria) this;
        }

        public Criteria andSignSalesNameLessThan(String value) {
            addCriterion("sign_sales_name <", value, "signSalesName");
            return (Criteria) this;
        }

        public Criteria andSignSalesNameLessThanOrEqualTo(String value) {
            addCriterion("sign_sales_name <=", value, "signSalesName");
            return (Criteria) this;
        }

        public Criteria andSignSalesNameLike(String value) {
            addCriterion("sign_sales_name like", value, "signSalesName");
            return (Criteria) this;
        }

        public Criteria andSignSalesNameNotLike(String value) {
            addCriterion("sign_sales_name not like", value, "signSalesName");
            return (Criteria) this;
        }

        public Criteria andSignSalesNameIn(List<String> values) {
            addCriterion("sign_sales_name in", values, "signSalesName");
            return (Criteria) this;
        }

        public Criteria andSignSalesNameNotIn(List<String> values) {
            addCriterion("sign_sales_name not in", values, "signSalesName");
            return (Criteria) this;
        }

        public Criteria andSignSalesNameBetween(String value1, String value2) {
            addCriterion("sign_sales_name between", value1, value2, "signSalesName");
            return (Criteria) this;
        }

        public Criteria andSignSalesNameNotBetween(String value1, String value2) {
            addCriterion("sign_sales_name not between", value1, value2, "signSalesName");
            return (Criteria) this;
        }

        public Criteria andStructBusinessLicenseIsNull() {
            addCriterion("struct_business_license is null");
            return (Criteria) this;
        }

        public Criteria andStructBusinessLicenseIsNotNull() {
            addCriterion("struct_business_license is not null");
            return (Criteria) this;
        }

        public Criteria andStructBusinessLicenseEqualTo(String value) {
            addCriterion("struct_business_license =", value, "structBusinessLicense");
            return (Criteria) this;
        }

        public Criteria andStructBusinessLicenseNotEqualTo(String value) {
            addCriterion("struct_business_license <>", value, "structBusinessLicense");
            return (Criteria) this;
        }

        public Criteria andStructBusinessLicenseGreaterThan(String value) {
            addCriterion("struct_business_license >", value, "structBusinessLicense");
            return (Criteria) this;
        }

        public Criteria andStructBusinessLicenseGreaterThanOrEqualTo(String value) {
            addCriterion("struct_business_license >=", value, "structBusinessLicense");
            return (Criteria) this;
        }

        public Criteria andStructBusinessLicenseLessThan(String value) {
            addCriterion("struct_business_license <", value, "structBusinessLicense");
            return (Criteria) this;
        }

        public Criteria andStructBusinessLicenseLessThanOrEqualTo(String value) {
            addCriterion("struct_business_license <=", value, "structBusinessLicense");
            return (Criteria) this;
        }

        public Criteria andStructBusinessLicenseLike(String value) {
            addCriterion("struct_business_license like", value, "structBusinessLicense");
            return (Criteria) this;
        }

        public Criteria andStructBusinessLicenseNotLike(String value) {
            addCriterion("struct_business_license not like", value, "structBusinessLicense");
            return (Criteria) this;
        }

        public Criteria andStructBusinessLicenseIn(List<String> values) {
            addCriterion("struct_business_license in", values, "structBusinessLicense");
            return (Criteria) this;
        }

        public Criteria andStructBusinessLicenseNotIn(List<String> values) {
            addCriterion("struct_business_license not in", values, "structBusinessLicense");
            return (Criteria) this;
        }

        public Criteria andStructBusinessLicenseBetween(String value1, String value2) {
            addCriterion("struct_business_license between", value1, value2, "structBusinessLicense");
            return (Criteria) this;
        }

        public Criteria andStructBusinessLicenseNotBetween(String value1, String value2) {
            addCriterion("struct_business_license not between", value1, value2, "structBusinessLicense");
            return (Criteria) this;
        }

        public Criteria andSigningSubjectNameIsNull() {
            addCriterion("signing_subject_name is null");
            return (Criteria) this;
        }

        public Criteria andSigningSubjectNameIsNotNull() {
            addCriterion("signing_subject_name is not null");
            return (Criteria) this;
        }

        public Criteria andSigningSubjectNameEqualTo(String value) {
            addCriterion("signing_subject_name =", value, "signingSubjectName");
            return (Criteria) this;
        }

        public Criteria andSigningSubjectNameNotEqualTo(String value) {
            addCriterion("signing_subject_name <>", value, "signingSubjectName");
            return (Criteria) this;
        }

        public Criteria andSigningSubjectNameGreaterThan(String value) {
            addCriterion("signing_subject_name >", value, "signingSubjectName");
            return (Criteria) this;
        }

        public Criteria andSigningSubjectNameGreaterThanOrEqualTo(String value) {
            addCriterion("signing_subject_name >=", value, "signingSubjectName");
            return (Criteria) this;
        }

        public Criteria andSigningSubjectNameLessThan(String value) {
            addCriterion("signing_subject_name <", value, "signingSubjectName");
            return (Criteria) this;
        }

        public Criteria andSigningSubjectNameLessThanOrEqualTo(String value) {
            addCriterion("signing_subject_name <=", value, "signingSubjectName");
            return (Criteria) this;
        }

        public Criteria andSigningSubjectNameLike(String value) {
            addCriterion("signing_subject_name like", value, "signingSubjectName");
            return (Criteria) this;
        }

        public Criteria andSigningSubjectNameNotLike(String value) {
            addCriterion("signing_subject_name not like", value, "signingSubjectName");
            return (Criteria) this;
        }

        public Criteria andSigningSubjectNameIn(List<String> values) {
            addCriterion("signing_subject_name in", values, "signingSubjectName");
            return (Criteria) this;
        }

        public Criteria andSigningSubjectNameNotIn(List<String> values) {
            addCriterion("signing_subject_name not in", values, "signingSubjectName");
            return (Criteria) this;
        }

        public Criteria andSigningSubjectNameBetween(String value1, String value2) {
            addCriterion("signing_subject_name between", value1, value2, "signingSubjectName");
            return (Criteria) this;
        }

        public Criteria andSigningSubjectNameNotBetween(String value1, String value2) {
            addCriterion("signing_subject_name not between", value1, value2, "signingSubjectName");
            return (Criteria) this;
        }

        public Criteria andSignDateIsNull() {
            addCriterion("sign_date is null");
            return (Criteria) this;
        }

        public Criteria andSignDateIsNotNull() {
            addCriterion("sign_date is not null");
            return (Criteria) this;
        }

        public Criteria andSignDateEqualTo(Date value) {
            addCriterionForJDBCDate("sign_date =", value, "signDate");
            return (Criteria) this;
        }

        public Criteria andSignDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("sign_date <>", value, "signDate");
            return (Criteria) this;
        }

        public Criteria andSignDateGreaterThan(Date value) {
            addCriterionForJDBCDate("sign_date >", value, "signDate");
            return (Criteria) this;
        }

        public Criteria andSignDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("sign_date >=", value, "signDate");
            return (Criteria) this;
        }

        public Criteria andSignDateLessThan(Date value) {
            addCriterionForJDBCDate("sign_date <", value, "signDate");
            return (Criteria) this;
        }

        public Criteria andSignDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("sign_date <=", value, "signDate");
            return (Criteria) this;
        }

        public Criteria andSignDateIn(List<Date> values) {
            addCriterionForJDBCDate("sign_date in", values, "signDate");
            return (Criteria) this;
        }

        public Criteria andSignDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("sign_date not in", values, "signDate");
            return (Criteria) this;
        }

        public Criteria andSignDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("sign_date between", value1, value2, "signDate");
            return (Criteria) this;
        }

        public Criteria andSignDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("sign_date not between", value1, value2, "signDate");
            return (Criteria) this;
        }

        public Criteria andBookkeepingMethodIsNull() {
            addCriterion("bookkeeping_method is null");
            return (Criteria) this;
        }

        public Criteria andBookkeepingMethodIsNotNull() {
            addCriterion("bookkeeping_method is not null");
            return (Criteria) this;
        }

        public Criteria andBookkeepingMethodEqualTo(Byte value) {
            addCriterion("bookkeeping_method =", value, "bookkeepingMethod");
            return (Criteria) this;
        }

        public Criteria andBookkeepingMethodNotEqualTo(Byte value) {
            addCriterion("bookkeeping_method <>", value, "bookkeepingMethod");
            return (Criteria) this;
        }

        public Criteria andBookkeepingMethodGreaterThan(Byte value) {
            addCriterion("bookkeeping_method >", value, "bookkeepingMethod");
            return (Criteria) this;
        }

        public Criteria andBookkeepingMethodGreaterThanOrEqualTo(Byte value) {
            addCriterion("bookkeeping_method >=", value, "bookkeepingMethod");
            return (Criteria) this;
        }

        public Criteria andBookkeepingMethodLessThan(Byte value) {
            addCriterion("bookkeeping_method <", value, "bookkeepingMethod");
            return (Criteria) this;
        }

        public Criteria andBookkeepingMethodLessThanOrEqualTo(Byte value) {
            addCriterion("bookkeeping_method <=", value, "bookkeepingMethod");
            return (Criteria) this;
        }

        public Criteria andBookkeepingMethodIn(List<Byte> values) {
            addCriterion("bookkeeping_method in", values, "bookkeepingMethod");
            return (Criteria) this;
        }

        public Criteria andBookkeepingMethodNotIn(List<Byte> values) {
            addCriterion("bookkeeping_method not in", values, "bookkeepingMethod");
            return (Criteria) this;
        }

        public Criteria andBookkeepingMethodBetween(Byte value1, Byte value2) {
            addCriterion("bookkeeping_method between", value1, value2, "bookkeepingMethod");
            return (Criteria) this;
        }

        public Criteria andBookkeepingMethodNotBetween(Byte value1, Byte value2) {
            addCriterion("bookkeeping_method not between", value1, value2, "bookkeepingMethod");
            return (Criteria) this;
        }

        public Criteria andBillDayIsNull() {
            addCriterion("bill_day is null");
            return (Criteria) this;
        }

        public Criteria andBillDayIsNotNull() {
            addCriterion("bill_day is not null");
            return (Criteria) this;
        }

        public Criteria andBillDayEqualTo(Byte value) {
            addCriterion("bill_day =", value, "billDay");
            return (Criteria) this;
        }

        public Criteria andBillDayNotEqualTo(Byte value) {
            addCriterion("bill_day <>", value, "billDay");
            return (Criteria) this;
        }

        public Criteria andBillDayGreaterThan(Byte value) {
            addCriterion("bill_day >", value, "billDay");
            return (Criteria) this;
        }

        public Criteria andBillDayGreaterThanOrEqualTo(Byte value) {
            addCriterion("bill_day >=", value, "billDay");
            return (Criteria) this;
        }

        public Criteria andBillDayLessThan(Byte value) {
            addCriterion("bill_day <", value, "billDay");
            return (Criteria) this;
        }

        public Criteria andBillDayLessThanOrEqualTo(Byte value) {
            addCriterion("bill_day <=", value, "billDay");
            return (Criteria) this;
        }

        public Criteria andBillDayIn(List<Byte> values) {
            addCriterion("bill_day in", values, "billDay");
            return (Criteria) this;
        }

        public Criteria andBillDayNotIn(List<Byte> values) {
            addCriterion("bill_day not in", values, "billDay");
            return (Criteria) this;
        }

        public Criteria andBillDayBetween(Byte value1, Byte value2) {
            addCriterion("bill_day between", value1, value2, "billDay");
            return (Criteria) this;
        }

        public Criteria andBillDayNotBetween(Byte value1, Byte value2) {
            addCriterion("bill_day not between", value1, value2, "billDay");
            return (Criteria) this;
        }

        public Criteria andAuditStatusIsNull() {
            addCriterion("audit_status is null");
            return (Criteria) this;
        }

        public Criteria andAuditStatusIsNotNull() {
            addCriterion("audit_status is not null");
            return (Criteria) this;
        }

        public Criteria andAuditStatusEqualTo(Integer value) {
            addCriterion("audit_status =", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusNotEqualTo(Integer value) {
            addCriterion("audit_status <>", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusGreaterThan(Integer value) {
            addCriterion("audit_status >", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("audit_status >=", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusLessThan(Integer value) {
            addCriterion("audit_status <", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusLessThanOrEqualTo(Integer value) {
            addCriterion("audit_status <=", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusIn(List<Integer> values) {
            addCriterion("audit_status in", values, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusNotIn(List<Integer> values) {
            addCriterion("audit_status not in", values, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusBetween(Integer value1, Integer value2) {
            addCriterion("audit_status between", value1, value2, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("audit_status not between", value1, value2, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andContractSignStatusIsNull() {
            addCriterion("contract_sign_status is null");
            return (Criteria) this;
        }

        public Criteria andContractSignStatusIsNotNull() {
            addCriterion("contract_sign_status is not null");
            return (Criteria) this;
        }

        public Criteria andContractSignStatusEqualTo(Integer value) {
            addCriterion("contract_sign_status =", value, "contractSignStatus");
            return (Criteria) this;
        }

        public Criteria andContractSignStatusNotEqualTo(Integer value) {
            addCriterion("contract_sign_status <>", value, "contractSignStatus");
            return (Criteria) this;
        }

        public Criteria andContractSignStatusGreaterThan(Integer value) {
            addCriterion("contract_sign_status >", value, "contractSignStatus");
            return (Criteria) this;
        }

        public Criteria andContractSignStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("contract_sign_status >=", value, "contractSignStatus");
            return (Criteria) this;
        }

        public Criteria andContractSignStatusLessThan(Integer value) {
            addCriterion("contract_sign_status <", value, "contractSignStatus");
            return (Criteria) this;
        }

        public Criteria andContractSignStatusLessThanOrEqualTo(Integer value) {
            addCriterion("contract_sign_status <=", value, "contractSignStatus");
            return (Criteria) this;
        }

        public Criteria andContractSignStatusIn(List<Integer> values) {
            addCriterion("contract_sign_status in", values, "contractSignStatus");
            return (Criteria) this;
        }

        public Criteria andContractSignStatusNotIn(List<Integer> values) {
            addCriterion("contract_sign_status not in", values, "contractSignStatus");
            return (Criteria) this;
        }

        public Criteria andContractSignStatusBetween(Integer value1, Integer value2) {
            addCriterion("contract_sign_status between", value1, value2, "contractSignStatus");
            return (Criteria) this;
        }

        public Criteria andContractSignStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("contract_sign_status not between", value1, value2, "contractSignStatus");
            return (Criteria) this;
        }

        public Criteria andStructCodeIsNull() {
            addCriterion("struct_code is null");
            return (Criteria) this;
        }

        public Criteria andStructCodeIsNotNull() {
            addCriterion("struct_code is not null");
            return (Criteria) this;
        }

        public Criteria andStructCodeEqualTo(String value) {
            addCriterion("struct_code =", value, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeNotEqualTo(String value) {
            addCriterion("struct_code <>", value, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeGreaterThan(String value) {
            addCriterion("struct_code >", value, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeGreaterThanOrEqualTo(String value) {
            addCriterion("struct_code >=", value, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeLessThan(String value) {
            addCriterion("struct_code <", value, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeLessThanOrEqualTo(String value) {
            addCriterion("struct_code <=", value, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeLike(String value) {
            addCriterion("struct_code like", value, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeNotLike(String value) {
            addCriterion("struct_code not like", value, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeIn(List<String> values) {
            addCriterion("struct_code in", values, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeNotIn(List<String> values) {
            addCriterion("struct_code not in", values, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeBetween(String value1, String value2) {
            addCriterion("struct_code between", value1, value2, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeNotBetween(String value1, String value2) {
            addCriterion("struct_code not between", value1, value2, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructNameIsNull() {
            addCriterion("struct_name is null");
            return (Criteria) this;
        }

        public Criteria andStructNameIsNotNull() {
            addCriterion("struct_name is not null");
            return (Criteria) this;
        }

        public Criteria andStructNameEqualTo(String value) {
            addCriterion("struct_name =", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameNotEqualTo(String value) {
            addCriterion("struct_name <>", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameGreaterThan(String value) {
            addCriterion("struct_name >", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameGreaterThanOrEqualTo(String value) {
            addCriterion("struct_name >=", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameLessThan(String value) {
            addCriterion("struct_name <", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameLessThanOrEqualTo(String value) {
            addCriterion("struct_name <=", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameLike(String value) {
            addCriterion("struct_name like", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameNotLike(String value) {
            addCriterion("struct_name not like", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameIn(List<String> values) {
            addCriterion("struct_name in", values, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameNotIn(List<String> values) {
            addCriterion("struct_name not in", values, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameBetween(String value1, String value2) {
            addCriterion("struct_name between", value1, value2, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameNotBetween(String value1, String value2) {
            addCriterion("struct_name not between", value1, value2, "structName");
            return (Criteria) this;
        }

        public Criteria andContractTagCodeIsNull() {
            addCriterion("contract_tag_code is null");
            return (Criteria) this;
        }

        public Criteria andContractTagCodeIsNotNull() {
            addCriterion("contract_tag_code is not null");
            return (Criteria) this;
        }

        public Criteria andContractTagCodeEqualTo(String value) {
            addCriterion("contract_tag_code =", value, "contractTagCode");
            return (Criteria) this;
        }

        public Criteria andContractTagCodeNotEqualTo(String value) {
            addCriterion("contract_tag_code <>", value, "contractTagCode");
            return (Criteria) this;
        }

        public Criteria andContractTagCodeGreaterThan(String value) {
            addCriterion("contract_tag_code >", value, "contractTagCode");
            return (Criteria) this;
        }

        public Criteria andContractTagCodeGreaterThanOrEqualTo(String value) {
            addCriterion("contract_tag_code >=", value, "contractTagCode");
            return (Criteria) this;
        }

        public Criteria andContractTagCodeLessThan(String value) {
            addCriterion("contract_tag_code <", value, "contractTagCode");
            return (Criteria) this;
        }

        public Criteria andContractTagCodeLessThanOrEqualTo(String value) {
            addCriterion("contract_tag_code <=", value, "contractTagCode");
            return (Criteria) this;
        }

        public Criteria andContractTagCodeLike(String value) {
            addCriterion("contract_tag_code like", value, "contractTagCode");
            return (Criteria) this;
        }

        public Criteria andContractTagCodeNotLike(String value) {
            addCriterion("contract_tag_code not like", value, "contractTagCode");
            return (Criteria) this;
        }

        public Criteria andContractTagCodeIn(List<String> values) {
            addCriterion("contract_tag_code in", values, "contractTagCode");
            return (Criteria) this;
        }

        public Criteria andContractTagCodeNotIn(List<String> values) {
            addCriterion("contract_tag_code not in", values, "contractTagCode");
            return (Criteria) this;
        }

        public Criteria andContractTagCodeBetween(String value1, String value2) {
            addCriterion("contract_tag_code between", value1, value2, "contractTagCode");
            return (Criteria) this;
        }

        public Criteria andContractTagCodeNotBetween(String value1, String value2) {
            addCriterion("contract_tag_code not between", value1, value2, "contractTagCode");
            return (Criteria) this;
        }

        public Criteria andContractTagNameIsNull() {
            addCriterion("contract_tag_name is null");
            return (Criteria) this;
        }

        public Criteria andContractTagNameIsNotNull() {
            addCriterion("contract_tag_name is not null");
            return (Criteria) this;
        }

        public Criteria andContractTagNameEqualTo(String value) {
            addCriterion("contract_tag_name =", value, "contractTagName");
            return (Criteria) this;
        }

        public Criteria andContractTagNameNotEqualTo(String value) {
            addCriterion("contract_tag_name <>", value, "contractTagName");
            return (Criteria) this;
        }

        public Criteria andContractTagNameGreaterThan(String value) {
            addCriterion("contract_tag_name >", value, "contractTagName");
            return (Criteria) this;
        }

        public Criteria andContractTagNameGreaterThanOrEqualTo(String value) {
            addCriterion("contract_tag_name >=", value, "contractTagName");
            return (Criteria) this;
        }

        public Criteria andContractTagNameLessThan(String value) {
            addCriterion("contract_tag_name <", value, "contractTagName");
            return (Criteria) this;
        }

        public Criteria andContractTagNameLessThanOrEqualTo(String value) {
            addCriterion("contract_tag_name <=", value, "contractTagName");
            return (Criteria) this;
        }

        public Criteria andContractTagNameLike(String value) {
            addCriterion("contract_tag_name like", value, "contractTagName");
            return (Criteria) this;
        }

        public Criteria andContractTagNameNotLike(String value) {
            addCriterion("contract_tag_name not like", value, "contractTagName");
            return (Criteria) this;
        }

        public Criteria andContractTagNameIn(List<String> values) {
            addCriterion("contract_tag_name in", values, "contractTagName");
            return (Criteria) this;
        }

        public Criteria andContractTagNameNotIn(List<String> values) {
            addCriterion("contract_tag_name not in", values, "contractTagName");
            return (Criteria) this;
        }

        public Criteria andContractTagNameBetween(String value1, String value2) {
            addCriterion("contract_tag_name between", value1, value2, "contractTagName");
            return (Criteria) this;
        }

        public Criteria andContractTagNameNotBetween(String value1, String value2) {
            addCriterion("contract_tag_name not between", value1, value2, "contractTagName");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}