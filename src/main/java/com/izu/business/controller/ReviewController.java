package com.izu.business.controller;

import com.izu.business.consts.resturl.MrcarBusinessRestMsgCenter;
import com.izu.business.dto.review.ReviewDTO;
import com.izu.business.dto.review.ReviewPreAddReqDTO;
import com.izu.business.dto.review.ReviewReqDTO;
import com.izu.business.service.ReviewService;
import com.izu.framework.web.rest.response.RestResponse;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @ClassName ReviewController
 * <AUTHOR>
 * @Date 2024/6/19 09:40
 * @Version 1.0
 */

@RestController
public class ReviewController {

    @Resource
    private ReviewService reviewService;

    /**
     * 新增待评价数据
     * @param reviewPreAddReqDTO
     * @return
     */
    @PostMapping(MrcarBusinessRestMsgCenter.REVIEW_PRE_ADD)
    public RestResponse preAdd(@RequestBody ReviewPreAddReqDTO reviewPreAddReqDTO) {
        return reviewService.preAdd(reviewPreAddReqDTO);
    }

    @RequestMapping(MrcarBusinessRestMsgCenter.REVIEW_LIST)
    public RestResponse getList(@RequestBody ReviewDTO param) {
        return RestResponse.success(reviewService.getList(param));
    }

    @RequestMapping(MrcarBusinessRestMsgCenter.REVIEW_EXPORT)
    public RestResponse getExportList(@RequestBody ReviewDTO param) {
        return RestResponse.success(reviewService.getExportList(param));
    }

    /**
     * App侧我的评价列表
     * @param param
     * @return
     */
    @RequestMapping(MrcarBusinessRestMsgCenter.REVIEW_MY_LIST)
    public RestResponse getMyList(@RequestBody ReviewDTO param) {
        return RestResponse.success(reviewService.getMyList(param));
    }

    /**
     * 查询评价表单快照
     * @param reviewNo
     * @return
     */
    @PostMapping(MrcarBusinessRestMsgCenter.REVIEW_FORM_SNAPSHOT)
    public RestResponse formSnapshot(String reviewNo) {
        return reviewService.formSnapshot(reviewNo);
    }

    /**
     * 新增评价
     * @param reviewReqDTO
     * @return
     */
    @PostMapping(MrcarBusinessRestMsgCenter.REVIEW_ADD)
    public RestResponse add(@RequestBody ReviewReqDTO reviewReqDTO) {
        return reviewService.add(reviewReqDTO);
    }

    /**
     * 评价详情
     * @param reviewNo
     * @return
     */
    @RequestMapping(MrcarBusinessRestMsgCenter.REVIEW_DETAIL)
    public RestResponse detail(String reviewNo) {
        return reviewService.detail(reviewNo);
    }
}