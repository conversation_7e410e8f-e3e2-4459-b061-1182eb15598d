package com.izu.user.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> dongxiya
 * @create 2022/11/23 16:18
 */
@Data
public class CustomerDetailDTO {
    /**
     * 人员ID
     **/
    @ApiModelProperty(value = "人员ID")
    private Integer customerId;

    /**
     * 人员编码
     **/
    @ApiModelProperty(value = "人员编码")
    private String customerCode;

    /**
     * 用户姓名
     **/
    @ApiModelProperty(value = "用户姓名")
    private String customerName;

    /**
     * 用户手机号；也是登录名
     **/
    @ApiModelProperty(value = "用户手机号")
    private String mobile;

    /**
     * 用户性别；1：男；2：女
     **/
    @ApiModelProperty(value = "用户性别；1：男；2：女")
    private Byte gender;

    /**
     * 企业ID
     **/
    @ApiModelProperty(value = "企业ID")
    private Integer companyId;

    /**
     * 员工所属企业编码
     */
    @ApiModelProperty(value = "员工所属企业编码")
    private String companyCode;


    /**
     * 人员状态；1：正常；4：删除
     **/
    @ApiModelProperty(value = "人员状态；1：正常；4：删除")
    private Byte customerStatus;

    /**
     * 职位
     */
    @ApiModelProperty(value = "职位")
    private String position;
    /**
     * 角色名称
     * 逗号分割的字符串
     */
    @ApiModelProperty(value = "角色名称")
    private String roleNames;

    /**
     * 系统角色名称
     * 只展示系统角色
     */
    @ApiModelProperty(value = "系统角色名称")
    private String systemRoleNames;


    /**
     * 用户城市数据权限
     **/
    @ApiModelProperty(value = "用户城市数据权限")
    private String dataScope;


    /**
     * 员工所属部门id
     **/
    @ApiModelProperty(value = "员工所属部门id")
    private Integer structId;

    /**
     * 员工所属部门名称
     **/
    @ApiModelProperty(value = "员工所属部门名称")
    private String structName;

    /**
     * 上上签账号
     **/
    @ApiModelProperty(value = "上上签账号")
    private String bestSignAccount;

}
