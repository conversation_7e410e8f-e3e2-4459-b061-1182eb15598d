package com.izu.user.service.maintain.excel;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.consts.maintain.garage.*;
import com.izu.asset.consts.maintain.workhours.WorkHoursDataDTO;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.user.dto.maintain.GarageImportDTO;
import com.izu.user.enums.ContructionInvoiceTypeEnum;
import com.izu.user.enums.OpenAccountMethodEnum;
import com.izu.user.enums.SeqTypeEnum;
import com.izu.user.enums.maintain.ServiceScopeEnum;
import com.izu.user.rpc.baidu.BaiduApi;
import com.izu.user.rpc.baidu.Result;
import com.izu.user.service.common.SequenceGenerator;
import com.izu.user.service.maintain.AsyncMaintainGarageService;
import mapper.ex.MaintainGarageExMapper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023/5/12 14:59
 */
public class ProviderGarageDataListener extends AnalysisEventListener<ProviderMaintainImportDTO> {
    private static final Logger LOGGER = LoggerFactory.getLogger(ProviderGarageDataListener.class);
    /**
     * 每隔5条存储数据库，实际使用中可以3000条，然后清理list ，方便内存回收
     */
    private static final int BATCH_COUNT = 100;
    List<ProviderMaintainImportDTO> list = new ArrayList<>();
    /**
     * 假设这个是一个DAO，当然有业务逻辑这个也可以是一个service。当然如果不用存储这个对象没用。
     */
    private MaintainGarageExMapper maintainGarageExMapper;

    private SequenceGenerator sequenceGenerator;

    private GarageImportDTO garageImportDTO;

    private AsyncMaintainGarageService asyncMaintainGarageService;

    private BaiduApi baiduApi;

    public ProviderGarageDataListener() {
        // 这里是demo，所以随便new一个。实际使用如果到了spring,请使用下面的有参构造函数
        //testDao = new DemoDAO();
    }

    /**
     * 如果使用了spring,请使用这个构造方法。每次创建Listener的时候需要把spring管理的类传进来
     *
     * @param maintainGarageExMapper
     */
    public ProviderGarageDataListener(MaintainGarageExMapper maintainGarageExMapper, SequenceGenerator sequenceGenerator, GarageImportDTO garageImportDTO, AsyncMaintainGarageService asyncMaintainGarageService, BaiduApi baiduApi) {
        this.maintainGarageExMapper = maintainGarageExMapper;
        this.sequenceGenerator = sequenceGenerator;
        this.garageImportDTO = garageImportDTO;
        this.asyncMaintainGarageService = asyncMaintainGarageService;
        this.baiduApi = baiduApi;
    }

    /**
     * 这个每一条数据解析都会来调用
     *
     * @param data    one row value. Is is same as {@link AnalysisContext#readRowHolder()}
     * @param context
     */
    @Override
    public void invoke(ProviderMaintainImportDTO data, AnalysisContext context) {
        //LOGGER.info("解析到一条数据:{}", JSON.toJSONString(data));
        list.add(data);
        // 达到BATCH_COUNT了，需要去存储一次数据库，防止数据几万条数据在内存，容易OOM
        if (list.size() >= BATCH_COUNT) {
            saveData();
            // 存储完成清理 list
            list.clear();
        }
    }

    /**
     * 所有数据解析完成了 都会来调用
     *
     * @param context
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 这里也要保存数据，确保最后遗留的数据也存储到数据库
        saveData();
        LOGGER.info("所有数据解析完成！");
    }

    /**
     * 加上存储数据库
     */
    private void saveData() {
        LOGGER.info("{}条数据，开始存储数据库！", list.size());
        List<GarageDTO> maintainGarages = bulidMaintainGarage(list);
        maintainGarageExMapper.batchInsert(maintainGarages);
        maintainGarages.parallelStream().forEach(e -> {
                    asyncMaintainGarageService.insertGarage(e, OpenAccountMethodEnum.PROVIDER_CREATE.getType());
                }
        );
        List<GarageDTO> garages = maintainGarages.stream().filter(distinctByKey(GarageDTO::getCompanyCode)).collect(Collectors.toList());
        garages.forEach(e -> {
            if(StringUtils.isNotBlank(e.getBelongCompanyCode())){
                WorkHoursDataDTO workHoursDataDTO = new WorkHoursDataDTO();
                workHoursDataDTO.setCompanyCode(e.getCompanyCode());
                workHoursDataDTO.setCompanyName(e.getCompanyName());
                workHoursDataDTO.setValidSdate(e.getContractBeginDate());
                workHoursDataDTO.setValidEdate(e.getContractEndDate());
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, workHoursDataDTO);
                RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.WORK_HOURS_DATA_INIT), paramMap, null);
            }

        });
        LOGGER.info("存储数据库成功");
    }

    static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    private List<GarageDTO> bulidMaintainGarage(List<ProviderMaintainImportDTO> list) {
        List<GarageDTO> maintainGarages = new ArrayList<>();
        list.forEach(e -> {
            String split = "-";
            GarageDTO maintainGarage = new GarageDTO();
            maintainGarage.setCreaterId(garageImportDTO.getLoginUserId());
            maintainGarage.setCreaterName(garageImportDTO.getLoginUserName());
            maintainGarage.setCreateTime(new Date());
            maintainGarage.setUpdaterId(garageImportDTO.getLoginUserId());
            maintainGarage.setUpdaterName(garageImportDTO.getLoginUserName());
            maintainGarage.setUpdateTime(new Date());
            //所属企业
            String splitName = "&";
            String companyName = e.getCompanyName();
            if (StringUtils.isNotBlank(companyName)) {
                String[] companyNames = companyName.split(splitName);
                maintainGarage.setCompanyName(companyNames[0]);
                maintainGarage.setCompanyCode(companyNames[1]);
                maintainGarage.setBelongCompanyCode(maintainGarage.getCompanyCode());
            } else {
                maintainGarage.setCompanyName(garageImportDTO.getLoginCompanyName());
                maintainGarage.setCompanyCode(garageImportDTO.getLoginCompanyCode());
                //运营端填写的所属企业为空
                maintainGarage.setBelongCompanyCode("");

            }


            maintainGarage.setServiceScope(Objects.equals(e.getServiceScopeName(), "全部") ? ServiceScopeEnum.ALL_COMPANY.getType() : ServiceScopeEnum.ONESELF_COMPANY.getType());
            maintainGarage.setGarageNo(sequenceGenerator.generate(new Date(), SeqTypeEnum.MAINTAIN_GARAGE));
            maintainGarage.setGarageName(e.getGarageName());
            maintainGarage.setSupplierType(GarageSupplierTypeEnum.COMPANY_SELF_SUPPORT.getType());
            maintainGarage.setGarageContactorName(e.getGarageContactorName());
            maintainGarage.setGarageContactorPhone(e.getGarageContactorPhone());
            String[] provinceCity = e.getGarageProvinceName().split(split);
            maintainGarage.setGarageProvinceCode(provinceCity[2]);
            maintainGarage.setGarageProvinceName(provinceCity[0]);
            maintainGarage.setGarageCityCode(provinceCity[3]);
            maintainGarage.setGarageCityName(provinceCity[1]);

            List<Result> datas = baiduApi.queryAddressByParams(maintainGarage.getGarageCityName(), e.getAddressDetail());
            if (CollectionUtil.isNotEmpty(datas) ) {
                Result data=datas.get(0);
                maintainGarage.setAddressUuid(data.getUid());
                maintainGarage.setAddressName(data.getName());
                maintainGarage.setAddressDetail(e.getAddressDetail());
                maintainGarage.setLongitude(data.getLocation().getLng());
                maintainGarage.setLatitude(data.getLocation().getLat());
                maintainGarage.setGarageStatus(GarageStatusEnum.ENABLE.getState());
            } else {
                maintainGarage.setAddressUuid("");
                maintainGarage.setAddressName("");
                maintainGarage.setAddressDetail(e.getAddressDetail());
                maintainGarage.setLongitude(new BigDecimal("0"));
                maintainGarage.setLatitude(new BigDecimal("0"));
                maintainGarage.setGarageStatus(GarageStatusEnum.DISABLE.getState());
            }


            maintainGarage.setSupportRepair(Objects.equals("是", e.getSupportRepairName()) ? true : false);
            maintainGarage.setSupportAccidentRepair(Objects.equals("是", e.getSupportAccidentRepairName()) ? true : false);
            maintainGarage.setSupportAppoint(false);
            maintainGarage.setSupportCarType(e.getSupportCarType());

            maintainGarage.setBusinessTimeBegin(e.getBusinessTimeBegin());
            maintainGarage.setBusinessTimeEnd(e.getBusinessTimeEnd());
            String invoiceTypeName = e.getContructionInvoiceTypeName();
            ContructionInvoiceTypeEnum contructionInvoiceType = ContructionInvoiceTypeEnum.getContructionInvoiceType(invoiceTypeName);
            maintainGarage.setContructionInvoiceType(contructionInvoiceType.getType());
            ConstructionInvoiceOpenTypeEnum constructionType = ConstructionInvoiceOpenTypeEnum.getName(e.getContructionInvoiceOpenTypeName());
            maintainGarage.setContructionInvoiceOpenType(constructionType.getType());
            ConstructionInvoiceRate rateValue = ConstructionInvoiceRate.getValue(e.getContructionInvoiceRateName());
            maintainGarage.setContructionInvoiceRate(new BigDecimal(rateValue.getValue()));
            maintainGarage.setServiceTelephone(e.getServiceTelephone());
            GarageQualificationsTypeEnum qualificationsTypeEnum = GarageQualificationsTypeEnum.getName(e.getGarageQualificationsTypeName());
            maintainGarage.setGarageQualificationsType(qualificationsTypeEnum.getType());
            maintainGarage.setGarageQualifications(e.getGarageQualificationsTypeName());
            maintainGarage.setContractBeginDate(e.getContractBeginDate());
            maintainGarage.setContractEndDate(e.getContractEndDate());
            maintainGarage.setCreditCode(StringUtils.isNotBlank(e.getCreditCode()) ? e.getCreditCode() : "");
            maintainGarage.setOpenBank(StringUtils.isNotBlank(e.getOpenBank()) ? e.getOpenBank() : "");
            maintainGarage.setBankAccount(StringUtils.isNotBlank(e.getBankAccount()) ? e.getBankAccount() : "");
            maintainGarage.setSupplierFinanceNo("");
            maintainGarages.add(maintainGarage);
        });
        return maintainGarages;
    }

}
