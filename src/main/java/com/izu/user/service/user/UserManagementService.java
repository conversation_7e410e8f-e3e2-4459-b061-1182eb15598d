package com.izu.user.service.user;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.izu.asset.errcode.MrCarAssetErrorCode;
import com.izu.carasset.CarAssetRestLocator;
import com.izu.carasset.dto.output.IzuStaffDTO;
import com.izu.config.dto.ServiceConfigCityDTO;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.mrcar.iot.dto.BatchDTO;
import com.izu.mrcar.iot.utils.OSSUtils;
import com.izu.third.enums.OSSBucketEnum;
import com.izu.user.common.RestLocators;
import com.izu.user.config.PermissionConst;
import com.izu.user.config.consts.UserConstantCode;
import com.izu.user.config.errcode.UserErrorCode;
import com.izu.user.dto.*;
import com.izu.user.entity.*;
import com.izu.user.enums.CustomerStatusEnum;
import com.izu.user.enums.UserStateEnum;
import com.izu.user.enums.role.SpecialRoleEnum;
import com.izu.user.service.staffRole.StaffRoleRelationService;
import com.izu.user.service.struct.CompanyDepartmentService;
import com.izu.user.util.ExcelCommon;
import com.izu.user.util.ObjectTransferUtil;
import com.izu.user.utils.DateUtil;
import com.izu.user.utils.PinYinUtil;
import com.izu.user.utils.SMSUtil;
import lombok.extern.slf4j.Slf4j;
import mapper.CustomerMapper;
import mapper.ex.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 用户管理功能
 **/
@Slf4j
@Service
@Deprecated
public class UserManagementService {
    private static final Logger logger = LoggerFactory.getLogger(UserManagementService.class);


    @Autowired
    private UserPermissionRelationExMapper userPermissionRelationExMapper;

    @Autowired
    private PermissionManagementService permissionManagementService;

    @Autowired
    private ManagerPermissionExMapper managerPermissionExMapper;

    @Autowired
    private CustomerMapper customerMapper;

    @Autowired
    private CustomerExMapper customerExMapper;

    @Autowired
    private CompanyExMapper companyExMapper;

    @Autowired
    private PushSmsExMapper pushSmsExMapper;

    @Autowired
    private WorkStationExMapper workStationExMapper;

    @Autowired
    private CompanyStructExMapper companyStructExMapper;

    @Autowired
    private DriverExMapper driverExMapper;

    @Autowired
    private CompanyDepartmentExMapper departmentExMapper;

    @Autowired
    private StaffPermQueryService staffPermQueryService;

    @Autowired
    private CompanyDepartmentService companyDepartmentService;

    @Autowired
    private StaffRoleRelationService staffRoleRelationService;

    /**
     * 二、禁用一个用户
     **/
    public RestResponse disableUser(Integer userId) {
        return changeUserStatus(userId, (byte) UserStateEnum.DELETED.getValue());
    }

    /**
     * 三、启用一个用户
     **/
    public RestResponse enableUser(Integer userId) {
        return changeUserStatus(userId, (byte)UserStateEnum.VALID.getValue());
    }

    /**
     * 四、修改一个用户
     **/
    public RestResponse changeUser(Customer newUser) {
        Customer employeeInfo = customerMapper.selectByPrimaryKey(newUser.getCustomerId());
        if (employeeInfo == null) {
            return RestResponse.fail(UserErrorCode.USER_NOT_EXIST);
        }
        customerMapper.updateByPrimaryKeySelective(newUser);
        // TODO 清理用户会话
        return RestResponse.success(null);
    }

    @Value("${pc.mrcar.url}")
    private String PC_MRCAR_URL;

    /**
     * 添加一个员工
     * @param customer
     * @return
     */
    public RestResponse addCustomer(Customer customer) {
        //1.判断该手机号在该企业中是否已经存在
        List<Customer> customers = customerExMapper.verifyMobileUnique(customer.getMobile());
        if(null != customers && !customers.isEmpty()){
            return RestResponse.fail(UserErrorCode.CUSTOMER_EXIT_COMPANY);
        }
        //若是新用户，查询司机表是否已存在司机身份的用户,若存在司机用户，则判断司机所属企业id是否和当前请求一致，不一致，不允许添加
        List<Driver> drivers = driverExMapper.selectDriversByMobile(customer.getMobile());
        if(null!=drivers && !drivers.isEmpty()){
            if(drivers.get(0).getCompanyId()!=customer.getCompanyId()){
                return RestResponse.fail(UserErrorCode.MOBILE_IS_EXIST_IN_OTHER_COMPANY);
            }
        }
        if (StringUtils.isNotBlank(customer.getCustomerNo()) && customerExMapper.getCustomerByCustomerNo(customer.getCompanyId(), customer.getCustomerNo(), customer.getMobile()) != null) {
            return RestResponse.fail(UserErrorCode.CUSTOMER_NO_IS_EXIST_IN_COMPANY);
        }
        if (customer.getStructId() != null) {
            CompanyDepartment department = departmentExMapper.selectByPrimaryKey(customer.getStructId());
            if (department == null || !department.getCompanyId().equals(customer.getCompanyId())) {
                return RestResponse.fail(UserErrorCode.DEPARTMENT_NOT_EXISTS);
            }
            customer.setStructName(department.getDepartmentName());
        }

        //2.向t_customer表里添加用户信息
        customer.setRegisterTime(DateUtil.getCurrentDateTimeString());
        customer.setGender((byte) UserConstantCode.GENDER_MEN);
        customer.setPinyinName(PinYinUtil.getPingYin(customer.getCustomerName()));
        customer.setJoinTime(DateUtil.getCurrentDateTimeString());
        customer.setBudgetOver((byte) UserConstantCode.BUDGET_OVER_LIMIT);
        int res = customerExMapper.insertSelective(customer);

        //添加成功，则给用户发送短信通知
        if(res>0){
            //3.给用户发送短信
            Company company =companyExMapper.selectByPrimaryKey(customer.getCompanyId());
            String companyName="";
            if(null==company.getCompanyAlias()){
                companyName=company.getCompanyName();
            }else{
                companyName=company.getCompanyAlias();
            }
            String context=UserConstantCode.CUSTOMER_INIT_MESSAGE.replace("XXX", companyName).replace("###",PC_MRCAR_URL);
            String sendSms = SMSUtil.sendSms(customer.getMobile(), context);
            //插入短信发送记录
            PushSms pushSms = new PushSms();
            pushSms.setPushType((byte) UserConstantCode.PUSH_TYPE_OPENACCOUNT);
            pushSms.setReceiveMobile(customer.getMobile());
            pushSms.setSendContent(context);
            pushSms.setSendTime(DateUtil.getCurrentDateTimeString());
            pushSms.setSendStatus((byte) UserConstantCode.NORMAL_STATUS);
            pushSms.setResultContent(sendSms);
            pushSmsExMapper.insertSelective(pushSms);
            //4.更新Redis中用户所在的企业信息，放在代理层操作

            return RestResponse.success(customer.getMobile());
        }else {
            return RestResponse.fail(UserErrorCode.CUSTOMER_ADD_FAILED);
        }




    }

    public RestResponse batchInsert(List<CustomerImportDTO> customerImportList, Integer createId, String createName, Integer companyId) {
        //定义错误对象列表
        List<CustomerImportFailDTO> importErrorDTOList = new ArrayList<>();
        BatchDTO batchDTO = new BatchDTO();
        try {
            //将树状结构的部门展开为列表
            RestResponse restResponse = companyDepartmentService.getDepartmentTree(companyId, null);
            List<CompanyDepartmentDTO> departmentDTOList = treeToList(new ArrayList<>(), (List<CompanyDepartmentDTO>) restResponse.getData());
            Map<String, Integer> departmentIdMap = departmentDTOList.stream().collect(Collectors.toMap(departmentDTO -> departmentDTO.getParentDepartmentName() == null ? departmentDTO.getDepartmentName() : departmentDTO.getParentDepartmentName() + "->" + departmentDTO.getDepartmentName(), CompanyDepartmentDTO::getId));
            Date date = new Date();
            for (CustomerImportDTO importDTO : customerImportList) {

                StringBuffer sb = verifyData(importDTO, companyId, departmentIdMap);
                if (sb.length() > 0) {
                    CustomerImportFailDTO importFailDTO = BeanUtil.copyObject(importDTO, CustomerImportFailDTO.class);
                    importFailDTO.setErrorMsg(sb.toString());
                    importErrorDTOList.add(importFailDTO);
                    continue;
                }
                //设置入库实体类
                Customer newUser = BeanUtil.copyObject(importDTO, Customer.class);
                newUser.setCompanyId(companyId);
                newUser.setStructId(importDTO.getDepartmentId() == null ? 0 : importDTO.getDepartmentId());
                newUser.setStructName(importDTO.getDepartmentName() == null ? "" : importDTO.getDepartmentName());
                newUser.setUpdateId(createId);
                newUser.setUpdateName(createName);
                newUser.setUpdateDate(date);
                if (newUser.getCustomerId() == null) {
                newUser.setCustomerStatus((byte) UserStateEnum.VALID.getValue());
                newUser.setCreateId(createId);
                newUser.setCreateName(createName);
                newUser.setCreateTime(date);
                newUser.setRegisterTime(DateUtil.getCurrentDateTimeString());
                newUser.setGender((byte) UserConstantCode.GENDER_MEN);
                newUser.setPinyinName(PinYinUtil.getPingYin(importDTO.getCustomerName()));
                newUser.setJoinTime(DateUtil.getCurrentDateTimeString());
                newUser.setBudgetOver((byte) UserConstantCode.BUDGET_OVER_LIMIT);
                    customerExMapper.insertSelective(newUser);
                } else {
                    customerExMapper.updateByPrimaryKeySelective(newUser);
                }
            }
            int fail = importErrorDTOList.size(), total = customerImportList.size();
            batchDTO.setSuccess(total - fail);
            batchDTO.setError(fail);
            batchDTO.setTotal(total);
            if (CollectionUtil.isNotEmpty(importErrorDTOList)) {
                try {
                    byte[] bytes = ExcelCommon.createBigVehicleExcel(ObjectTransferUtil.objectToList(importErrorDTOList), "错误信息");
                    String filePath = OSSUtils.uploadFile("新增员工失败信息", bytes, OSSBucketEnum.TMP_IMG.getCode());
                    batchDTO.setDownloadUrl(filePath);
                } catch (Exception e) {
                    log.error("上传oss报错,e:{}", e.toString());
            }
            }
        } catch (Exception e) {
            log.error("私车导入异常,e:{}", e.toString());
            RestResponse.fail(MrCarAssetErrorCode.IMPORT_ERROR);
        }
        return RestResponse.success(batchDTO);
    }

    private StringBuffer verifyData(CustomerImportDTO customerImportDTO,Integer companyId,Map<String, Integer> departmentIdMap) {
        StringBuffer sb = new StringBuffer();

        if (StringUtils.isEmpty(customerImportDTO.getCustomerName())) {
            sb.append("员工姓名不能为空;");
        } else if (customerImportDTO.getCustomerName().length() > 30) {
            sb.append("员工姓名格式超长，更新失败;");
            }
        if (StringUtils.isBlank(customerImportDTO.getMobile())) {
            sb.append("手机号不能为空;");
        }else {
            Pattern pattern = Pattern.compile("^[1]\\d{10}$");
            if (!pattern.matcher(customerImportDTO.getMobile()).matches()) {
                sb.append("手机号格式错误;");
            }else {
                List<Customer> customers = customerExMapper.getCustomerByMobileWithoutStatus(customerImportDTO.getMobile());
                if (CollectionUtil.isNotEmpty(customers)) {
                    if (customers.stream().anyMatch(e -> !ObjectUtil.equals(companyId, e.getCompanyId())&& e.getCustomerStatus() == 1)) {
                        sb.append("该手机号已在其他企业中;");
        } else {
                        Customer customer = customers.stream().filter(e -> ObjectUtil.equals(companyId, e.getCompanyId())).findFirst().get();
                        customerImportDTO.setCustomerId(customer.getCustomerId());
                    }
                }
            }
        }
        if (StringUtils.isNotEmpty(customerImportDTO.getEmail())) {
            if (!customerImportDTO.getEmail().contains("@")){
                sb.append("邮箱格式错误;");
            }else if (customerExMapper.getCustomerByEmail(companyId, customerImportDTO.getEmail(),customerImportDTO.getMobile()) != null) {
                sb.append("当前企业已存在该邮箱，更新失败;");
            }
        }
        if (StringUtils.isNotEmpty(customerImportDTO.getPosition()) && customerImportDTO.getPosition().length() > 50){
            sb.append("职务输入过长，该员工更新失败;");
        }
        if (StringUtils.isNotEmpty(customerImportDTO.getCustomerNo())) {
            if (customerImportDTO.getCustomerNo().length() > 30) {
                sb.append("员工工号输入过长，该员工更新失败;");
            }else if (customerExMapper.getCustomerByCustomerNo(companyId, customerImportDTO.getCustomerNo(),customerImportDTO.getMobile()) != null) {
                sb.append("当前企业已存在该工号，更新失败;");
        }
    }
        if (StringUtils.isNotEmpty(customerImportDTO.getDepartmentName())){
            Integer id = departmentIdMap.get(customerImportDTO.getDepartmentName());
            if (id == null){
                sb.append("找不到该部门，该员工更新失败;");
            }else {
                //截取实际的部门名称
                String departmentName = customerImportDTO.getDepartmentName();
                String name;
                if (departmentName.contains("->")){
                    name = departmentName.substring(departmentName.lastIndexOf("->") + 2);
                }else {
                    name = departmentName;
                }
                customerImportDTO.setDepartmentId(id);
                customerImportDTO.setDepartmentName(name);
            }
        }
        return sb;
    }

    /**
     * 编辑一个员工
     * @param customer
     * @return
     */
    public RestResponse updCustomer(Customer customer){
        boolean mobileChange=false;
        String newMobile=customer.getMobile(),oldMobile="";
        Customer customerDetail = customerExMapper.selectByPrimaryKey(customer.getCustomerId());
        if(null!=customerDetail){
            oldMobile = customerDetail.getMobile();
        }else {
            return RestResponse.fail(UserErrorCode.CUSTOMER_NOT_EXIST);
        }
        if (customer.getStructId() != null) {
            CompanyDepartment department = departmentExMapper.selectByPrimaryKey(customer.getStructId());
            if (department == null || !department.getCompanyId().equals(customer.getCompanyId())) {
                return RestResponse.fail(UserErrorCode.DEPARTMENT_NOT_EXISTS);
            }
            customer.setStructName(department.getDepartmentName());
        }
        if (StringUtils.isNotBlank(customer.getCustomerNo()) && customerExMapper.getCustomerByCustomerNo(customer.getCompanyId(), customer.getCustomerNo(), customer.getMobile()) != null) {
            return RestResponse.fail(UserErrorCode.CUSTOMER_NO_IS_EXIST_IN_COMPANY);
        }
        if(!oldMobile.equals(newMobile)){
            List<Customer> customerList = customerExMapper.verifyMobileUnique(newMobile);
            if(null != customerList&& !customerList.isEmpty()&& !Objects.equals(customerList.get(0).getCustomerId(), customer.getCustomerId())){
                return RestResponse.fail(UserErrorCode.CUSTOMER_EXIT_COMPANY_UPDATE);
            }
            //若是新手机号，查询司机表是否已存在司机身份的用户,若存在司机用户，则判断司机所属企业id是否和当前请求一致，不一致，不允许添加
            List<Driver> drivers = driverExMapper.selectDriversByMobile(newMobile);
            if(null!=drivers && !drivers.isEmpty()){
                if(!Objects.equals(drivers.get(0).getCompanyId(), customer.getCompanyId())){
                    return RestResponse.fail(UserErrorCode.MOBILE_IS_EXIST_IN_OTHER_COMPANY);
                }
            }
            mobileChange = true;
        }
        //修改用户信息
        customerExMapper.updateByPrimaryKeySelective(customer);

        //如果更改了用户的手机号，则需要发送短信到新的手机号上
        if(mobileChange){
            Company companyInfo = companyExMapper.selectByPrimaryKey(customer.getCompanyId());
            String companyName="";
            if(null==companyInfo.getCompanyAlias()){
                companyName=companyInfo.getCompanyName();
            }else{
                companyName=companyInfo.getCompanyAlias();
            }
            //新手机号
            String newContext=UserConstantCode.CUSTOMER_UPDATE_MESSAGE.replace("XXX",companyName).replace("###", newMobile);
            String sendSms=SMSUtil.sendSms(newMobile, newContext);
            //记录短信发送记录
            PushSms pushSms = new PushSms();
            pushSms.setPushType((byte) UserConstantCode.PUSH_TYPE_UPDATEMOBILE);
            pushSms.setReceiveMobile(customer.getMobile());
            pushSms.setSendContent(newContext);
            pushSms.setSendTime(DateUtil.getCurrentDateTimeString());
            pushSms.setSendStatus((byte) UserConstantCode.NORMAL_STATUS);
            pushSms.setResultContent(sendSms);
            pushSmsExMapper.insertSelective(pushSms);
        }
        //最后更新Redis中的用户信息，该逻辑放在代理层操作，调用之前的接口
        String result = oldMobile+","+newMobile;
        return RestResponse.success(result);
    }

    /**
     * 变更用户状态
     * @param customer
     * @return
     */
    public RestResponse updCustomerStatus(Customer customer){
        Customer customerDetail = customerExMapper.selectByPrimaryKey(customer.getCustomerId());
        if(null==customerDetail){
            return RestResponse.fail(UserErrorCode.CUSTOMER_NOT_EXIST);
        }
        //禁用账户需要，判断用户是否有首汽司机（代垫司机）的角色，若有，则不允许禁用
        if(customer.getCustomerStatus()!=null && CustomerStatusEnum.DISABLED.getStatus().equals(customer.getCustomerStatus())){
            Set<String> roleSet = staffRoleRelationService.getRoleCodeByStaffCode(customerDetail.getCustomerCode());
            if(roleSet.contains(SpecialRoleEnum.PROVIDER_DRIVER.getRoleCode())){
                return RestResponse.fail(UserErrorCode.PROVIDER_DRIVER_NOT_DISABLE);
            }
        }
        //启用的时候判断是否有手机号在别的企业里是启用状态
        if(customer.getCustomerStatus() == 1){
            List<Customer> customers = customerExMapper.verifyMobileUnique(customerDetail.getMobile());
            if(null!=customers && customers.size()>0){
                return RestResponse.fail(UserErrorCode.CUSTOMER_STATUS_EXIST);
            }
        }
        //首汽用户并且启用
        if (customerDetail.getCompanyId() == 1 && customer.getCustomerStatus() == 1){
            //启用得判断客户状态
            Map<String, Object> params = new HashMap<>();
            params.put("userMobile", customerDetail.getMobile());
            params.put("queryBasic", false);
            CarAssetRestLocator locator = new CarAssetRestLocator();
            String restUrl = locator.getRestUrl("/staff/getIzuStaff.json");
            RestResponse restResponse = RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, params, null, IzuStaffDTO.class);
            if (!restResponse.isSuccess()){
                return RestResponse.create(RestErrorCode.UNKNOWN_ERROR, "新系统员工信息查询失败,操作失败",  false, null);
            }
            IzuStaffDTO staffDTO = (IzuStaffDTO) restResponse.getData();
            if (staffDTO.getUserState() == 4){
                return RestResponse.create(RestErrorCode.UNKNOWN_ERROR, "新系统员工已被禁用,操作失败",  false, null);
            }
        }
        //修改状态
        customerExMapper.updateByPrimaryKeySelective(customer);
        return RestResponse.success(0);
    }

    /**
     * 获取一个员工的详细信息
     * @param customer
     * @return
     */
    public RestResponse getCustomerDetail(Customer customer){
        Customer customerDetail = customerExMapper.selectByCustomerId(customer.getCustomerId());
        if(null!=customerDetail){
/*            //员工表里冗余的上级信息，要么在删除或者修改的时候，更新下属的绑定信息，要么在查询的时候重新查询一次，此处采用第二种
            if(null!=customerDetail.getSuperiorId()){
                Customer customerSuperior = customerExMapper.selectByCustomerId(customerDetail.getSuperiorId());
                if(null!=customerSuperior){
                    customerDetail.setSuperiorName(customerSuperior.getCustomerName());
                    customerDetail.setSuperiorPhone(customerSuperior.getMobile());
                }
            }*/
            return RestResponse.success(customerDetail);
        }else {
            return RestResponse.fail(UserErrorCode.CUSTOMER_NOT_EXIST);
        }
    }

    public RestResponse getCustomerDetailByMobile(Customer customer){
        Customer customerDetail = customerExMapper.selectCustomerByMobile(customer.getCompanyId(),customer.getMobile(),customer.getCustomerName());
        if(null!=customerDetail){
            return RestResponse.success(customerDetail);
        }else {
            return RestResponse.fail(UserErrorCode.CUSTOMER_NOT_EXIST);
        }
    }

    /**
     * 删除指定的员工
     * @param customerIds
     * @param companyId
     * @return
     */
    public RestResponse deleteCustomers(String customerIds,Integer companyId, byte status){
        Set<Integer> customerIdSet = transIdsStrToSet(customerIds);
        List<String> mobilesForUpdateRedis = new ArrayList<>();
        for(Integer id:customerIdSet){
            //1.查询是否存在该用户
            Customer customerDetail = customerExMapper.selectByPrimaryKey(id);
            if(null == customerDetail){
                continue;
            }
            //3.删除操作
            Customer customer = new Customer();
            customer.setCustomerId(id);
            if (status == 1){
                customer.setCustomerStatus((byte) UserConstantCode.NORMAL_STATUS);
            }else {
                customer.setCustomerStatus((byte) UserConstantCode.DEL_STATUS);
            }
            customerExMapper.updateByPrimaryKeySelective(customer);
            mobilesForUpdateRedis.add(customerDetail.getMobile());
        }
        //同样还是在代理层进行缓存的更新
        return RestResponse.success(mobilesForUpdateRedis);

    }

    public RestResponse checkCustomers(String customerIds,Integer companyId){
        Set<Integer> customerIdSet = transIdsStrToSet(customerIds);
        List<String> namesForNoDel = new ArrayList<>();
        for(Integer id:customerIdSet){
            //1.查询是否存在该用户
            Customer customerDetail = customerExMapper.selectByPrimaryKey(id);
            if(null == customerDetail){
                continue;
            }
            //2.查询改用户是否有正在进行中的订单
            String restUrl = RestLocators.order().getRestUrl("/orderApply/queryOrderApplyByCustomerId");
            Map<String, Object> params = new HashMap<>();
            params.put("companyId",companyId);
            params.put("customerId",id);
            RestResponse restResponse= RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POST, restUrl, params, null, RestResponse.class);
            int orderCount = (int) restResponse.getData();
            if(orderCount>0){
                namesForNoDel.add(customerDetail.getCustomerName());
            }
        }
        //同样还是在代理层进行缓存的更新
        return RestResponse.success(namesForNoDel);

    }

    private RestResponse changeUserStatus(int customerId, byte status) {
        Customer user = customerMapper.selectByPrimaryKey(customerId);
        if (user == null) {
            return RestResponse.fail(UserErrorCode.USER_NOT_EXIST);
        }
        Customer userForUpdate = new Customer();
        userForUpdate.setCustomerId(customerId);
        userForUpdate.setCustomerStatus(status);
        customerMapper.updateByPrimaryKeySelective(userForUpdate);
        //TODO 删除会话
        return RestResponse.success(null);
    }
    public List<Customer> getUnderlingIzuStaff(Integer synchronization,Byte mgrType,Integer staffId){
        return null;
    }




    /**
     * 返回的数据格式：列表
     **/
    @Deprecated
    private List<ManagerPermissionDTO> getAllPermissionsList(List<Integer> checkedPermissionIds) {
        List<ManagerPermissionV2> allPos = managerPermissionExMapper.queryPermissions(null, null, null, null, null, null, null);
        List<ManagerPermissionDTO> allDtos = BeanUtil.copyList(allPos, ManagerPermissionDTO.class);
        for (ManagerPermissionDTO dto : allDtos) {
            if (checkedPermissionIds.contains(dto.getPermissionId())) {
                dto.setChecked(true);
            }
        }
        return allDtos;
    }
    /**
     * 五、查询一个员工的权限（返回的数据格式：列表、树形）
     **/
    @Deprecated
    public List<ManagerPermissionDTO> getAllPermissions(Byte mgrType, Integer staffId,Integer underlingStaffId, String dataFormat,Byte valid) {
        //查询一个角色的所有权限ID（作为是否选中的依据）
        List<Integer> permissionIds =null;
        if(mgrType==1){
            permissionIds=permissionManagementService.queryPermissionId(null);
        }else if(mgrType==2){
            permissionIds=permissionManagementService.queryPermissionId(1);
        }else{
            permissionIds = userPermissionRelationExMapper.selectStaffPermissionIdsByStaffId(staffId);
        }

        List<Integer> underlingPermissionIds = underlingStaffId!=null? userPermissionRelationExMapper.selectStaffPermissionIdsByStaffId(underlingStaffId):null;
        if(permissionIds==null){
            return Collections.emptyList();
        }
        if (underlingPermissionIds == null) {
            underlingPermissionIds = new ArrayList<>();
        }
        if (PermissionConst.PermissionDataFormat.LIST.equalsIgnoreCase(dataFormat)) {
            return this.getAllPermissionsList(permissionIds);
        } else if (PermissionConst.PermissionDataFormat.TREE.equalsIgnoreCase(dataFormat)) {
            //给员工分配权限的时候需要隐藏掉首页的菜单
            List<ManagerPermissionV2> pos= managerPermissionExMapper.queryPermissions(null, null, "index_bi", null, null, null, null);
            if(org.apache.commons.collections.CollectionUtils.isNotEmpty(pos)){
                Integer indexPid = pos.get(0).getPermissionId();
                permissionIds.remove(indexPid);
            }
            return this.getAllPermissionsTree(underlingPermissionIds, permissionIds,valid);
        }
        return Collections.emptyList();
    }
    public List<Integer> getStaffPermissionIdsByStaffId(Integer staffId){
        return userPermissionRelationExMapper.selectStaffPermissionIdsByStaffId(staffId);
    }
    /**
     * 返回的数据格式：树形
     **/
    private List<ManagerPermissionDTO> getAllPermissionsTree(List<Integer> checkedPermissionIds, List<Integer>  referencePermissionIds,Byte valid) {
        return this.getChildren(checkedPermissionIds ,referencePermissionIds , 0,valid);
    }

    private List<ManagerPermissionDTO> getChildren(List<Integer> checkedPermissionIds ,List<Integer>  referencePermissionIds, Integer parentPermissionId,Byte valid) {
        List<ManagerPermissionV2> childrenPos = managerPermissionExMapper.queryPermissions(null, parentPermissionId, null, null, null, valid, null);
        if (childrenPos == null || childrenPos.isEmpty()) {
            return Collections.emptyList();
        }

        List<ManagerPermission> childrenNew=new ArrayList<>();
        for(ManagerPermission managerPermission:childrenPos){
            if(referencePermissionIds.contains(managerPermission.getPermissionId())){
                childrenNew.add(managerPermission);
            }
        }
        List<ManagerPermissionDTO> childrenDTOs = BeanUtil.copyList(childrenNew, ManagerPermissionDTO.class);
        for (ManagerPermissionDTO childrenDto : childrenDTOs) {
            if (checkedPermissionIds.contains(childrenDto.getPermissionId())) {
                childrenDto.setChecked(true);
            }
            List<ManagerPermissionDTO> childs = this.getChildren(checkedPermissionIds,referencePermissionIds, childrenDto.getPermissionId(),valid);
            childrenDto.setChildPermissions(childs);
        }
        return childrenDTOs;
    }

    public void savePermission(String customerIds, String permissionIds,Integer leaderId) {
        Set<Integer> customerIdSet = transIdsStrToSet(customerIds);
        if (customerIdSet == null || customerIdSet.isEmpty()) {
            return;
        }
            List<Integer> ids = userPermissionRelationExMapper.selectStaffPermissionIdsByStaffId(leaderId);
            if (!ids.isEmpty()) {
                userPermissionRelationExMapper.clearUserPermissionIn(customerIdSet, ids);
            }
        if (StringUtils.isNotBlank(permissionIds)) {
            Set<Integer> permission = transIdsStrToSet(permissionIds);
            if (!permission.isEmpty()) {
                userPermissionRelationExMapper.saveUserPermissions(customerIdSet, permission);
            }
        }
    }

    /**
     * 为指定的用户设置数据权限（城市编码）
     * @param customerIds
     * @param cityCodes
     */
    public void saveDataPermission(String customerIds, String cityCodes) {
        Set<Integer> customerIdSet = transIdsStrToSet(customerIds);
        if (customerIdSet == null || customerIdSet.isEmpty()){
            return;
        }
        //遍历customerIdSet，更新对应用户的data_scope字段
        customerIdSet.forEach((customerId)->{
            Customer customer = new Customer();
            customer.setCustomerId(customerId);
            customerMapper.updateByPrimaryKeySelective(customer);
        });
        //TODO 清理被分配权限的用户会话
    }

    /**
     * 批量设置用户的减免比例
     * @param customerIds
     * @param disccountProportion
     */
    public void batchSetDisccountProportion(String customerIds, Integer disccountProportion) {
        Set<Integer> customerIdSet = transIdsStrToSet(customerIds);
        if (customerIdSet == null || customerIdSet.isEmpty()){
            return;
        }
        //遍历customerIdSet，更新对应用户的disccountProportion字段
        customerIdSet.forEach((customerId)->{
            Customer customer = new Customer();
            customer.setCustomerId(customerId);
            customer.setDisccountProportion(disccountProportion);
            customerMapper.updateByPrimaryKeySelective(customer);
        });
    }

    /**
     * 为指定的用户设置设置部门信息
     * @param customerIds
     * @param structName
     * @param structId
     */
    public void setStructForCustomers(String customerIds, String structName,Integer structId) {
        Set<Integer> customerIdSet = transIdsStrToSet(customerIds);
        if (customerIdSet == null || customerIdSet.isEmpty()){
            return;
        }
        //遍历customerIdSet，更新员工的部门信息
        customerIdSet.forEach((customerId)->{
            Customer customer = new Customer();
            customer.setCustomerId(customerId);
            customer.setStructId(structId);
            customer.setStructName(structName);
            customerMapper.updateByPrimaryKeySelective(customer);
        });
    }


    public void setSuperiorForCustomers(String customerIds, String superiorName,Integer superiorId,String superiorPhone) {
        Set<Integer> customerIdSet = transIdsStrToSet(customerIds);
        if (customerIdSet == null || customerIdSet.isEmpty()){
            return;
        }
        //遍历customerIdSet，更新员工的上级信息
        customerIdSet.forEach((customerId)->{
            Customer customer = new Customer();
            customer.setCustomerId(customerId);
            customerMapper.updateByPrimaryKeySelective(customer);
        });
    }


    private Set<Integer> transIdsStrToSet(String ids){
        if (StringUtils.isBlank(ids)){
            return Collections.emptySet();
        }
        String[] split = ids.split(PermissionConst.COMMA);
        Set<Integer> idSet = new HashSet<>();
        for (String str : split){
            idSet.add(Integer.valueOf(str));
        }
        return idSet;

    }

    public Customer getCustomerById(Integer customerId){
        return customerMapper.selectByPrimaryKey(customerId);
    }


    /**
     * 查询指定部门下的的所有员工信息
     * @param pageNo
     * @param pageSize
     * @param structId
     * @param companyId
     * @return
     */
    public PageDTO queryCustomerForList(Integer pageNo, Integer pageSize,String structId,Integer companyId){
        Long total = 0L;
        List<CustomerStructInfoDTO> customerStructInfoDTOS = null;
        //若指定了部门，则将指定的部门id作为查询条件
        Page p = PageHelper.startPage(pageNo, pageSize, true);
        try {
            //根据确定的部门编号查询本部门及其下属部门的员工信息
            customerStructInfoDTOS = customerExMapper.queryCustomerListByParams(null, null,companyId,null, null, null);
            total = p.getTotal();
        } finally {
            PageHelper.clearPage();
        }
        PageDTO pageDTO = new PageDTO(pageNo, pageSize, total, customerStructInfoDTOS);
        return pageDTO;
    }

    /**
     * 分页查询员工列表数据，只能看到当前员工所属部门及其下属部门的数据(管理员除外)
     * 修改：部门被删掉了，因此部门人员权限没了，根据当前登录用户的所属企业进行查询——2020-6-19 16:17:52
     * @param pageNo
     * @param pageSize
     * @param customerName
     * @param mobile
     * @param companyId
     * @return
     */
    public PageDTO queryCustomerByPage(Integer pageNo, Integer pageSize,String customerName,String mobile,Integer companyId,Byte customerStatus, String position, Integer departmentId){
        Long total = 0L;
        List<CustomerStructInfoDTO> customerList = null;
        Page p = PageHelper.startPage(pageNo, pageSize, true);
        try {
            //根据确定的部门编号查询本部门及其下属部门的员工信息
            customerList = customerExMapper.queryCustomerByPage(customerName, mobile,companyId,customerStatus, position, departmentId);
            total = p.getTotal();
        } finally {
            PageHelper.clearPage();
        }
        PageDTO pageDTO = new PageDTO(pageNo, pageSize, total, customerList);
        return pageDTO;
    }

    /**
     * 管理员变更，需要将对应权限的赋予新的管理员
     * @return
     */
    public int changeAdminPermission(Integer oldId,Integer newId){
        // 先清除 新管理员的历史权限再赋权
        userPermissionRelationExMapper.clearUserPermissionIn(Collections.singleton(newId), null);
        return userPermissionRelationExMapper.updateByStaffId(oldId,newId);
    }

    public List<UserDataScopeDTO> queryDataScopeByCompanyIdAndCustomerId(Integer companyId, Integer customerId, Integer loginCustomerId){
        Set<Integer> loginCustomerDataScope = new HashSet<>();
        Set<Integer> selectedCustomerDataScope = new HashSet<>();
        List<ServiceConfigCityDTO> allCityList = new ArrayList<>();
        Map<Integer,String> allCityMap = new HashMap<>();
        //1.判断当前登录用户是不是该企业的管理员
        //3.查询指定用户的数据权限和非管理员的数据权限，用于回显
        //4.根据是否是管理员以及是否上送选中用户来确定返回的数据权限信息
        List<UserDataScopeDTO> resultList = new ArrayList<UserDataScopeDTO>();
        for(Integer cc:loginCustomerDataScope){
            UserDataScopeDTO dto = new UserDataScopeDTO();
            dto.setCityCode(cc);
            dto.setCityName(allCityMap.get(cc));
            if(selectedCustomerDataScope.contains(cc)){
                dto.setChecked(true);
            }
            resultList.add(dto);
        }
        return resultList;
    }

    public List<ManagerPermissionDTO> getCompanyPermission(Integer customerId, Integer staffId, Byte companyAttribute) {
        List<Integer> leaderPermissions = userPermissionRelationExMapper.selectStaffPermissionIdsByStaffId(staffId);
        if (leaderPermissions.isEmpty()){
            //领导没有权限,直接返回空列表
            return Collections.emptyList();
        }
        List<Integer> checkedPermissionId = userPermissionRelationExMapper.selectStaffPermissionIdsByStaffId(customerId);
        //一级菜单
        List<ManagerPermission> levelOne = managerPermissionExMapper.selectTopOne(leaderPermissions, companyAttribute);
        List<Integer> pid = levelOne.stream().map(ManagerPermission::getPermissionId).collect(Collectors.toList());
        List<ManagerPermissionDTO> levelOneDTO = BeanUtil.copyList(levelOne, ManagerPermissionDTO.class);
        //二级菜单
        List<ManagerPermission> levelTwo = managerPermissionExMapper.selectPermissionIdByParentList(pid, companyAttribute, leaderPermissions);
        List<ManagerPermissionDTO> levelTwoDTO = BeanUtil.copyList(levelTwo, ManagerPermissionDTO.class);
        for (ManagerPermissionDTO managerPermission : levelTwoDTO){
            if (checkedPermissionId.contains(managerPermission.getPermissionId())){
                managerPermission.setChecked(true);
            }
            managerPermission.setChildPermissions(getChildPermission(managerPermission.getPermissionId(), checkedPermissionId, companyAttribute, leaderPermissions,managerPermission.getPermissionName()));
        }
        //把二级菜单拼接到一级
        levelOneDTO.forEach(
                managerPermissionDTO -> {
                    if (checkedPermissionId.contains(managerPermissionDTO.getPermissionId())){
                        managerPermissionDTO.setChecked(true);
                    }
                    levelTwoDTO.forEach(
                            permission -> {
                                if (managerPermissionDTO.getPermissionId().equals(permission.getParentPermissionId())) {
                                    List<ManagerPermissionDTO> childPermissions = managerPermissionDTO.getChildPermissions();
                                    if (childPermissions == null) {
                                        childPermissions = new LinkedList<>();
                                    }
                                    childPermissions.add(permission);
                                    managerPermissionDTO.setChildPermissions(childPermissions);
                                }
                            }
                    );
                }

        );
        return levelOneDTO;
    }


    private List<ManagerPermissionDTO> getChildPermission(Integer parentId, List<Integer> checkedPermission, Byte companyAttribute, List<Integer> leaderPermissions,String parentName){
        List<ManagerPermission> levelTwo = managerPermissionExMapper.selectPermissionIdByParentList(Collections.singletonList(parentId), companyAttribute, leaderPermissions);
        List<ManagerPermissionDTO> levelTwoDTO = BeanUtil.copyList(levelTwo, ManagerPermissionDTO.class);
        for (ManagerPermissionDTO dto : levelTwoDTO){
            if (checkedPermission.contains(dto.getPermissionId())){
                dto.setChecked(true);
            }
            dto.setParentPermissionName(parentName);
        }
        return levelTwoDTO;
    }

    public boolean updateCompanyPermission(Integer customerId, String permissionIds, Integer companyId) {
        List<Integer> ids = permissionStrToList(permissionIds);
        List<Integer> buttonList = managerPermissionExMapper.selectButtonPermission(ids);
        List<Integer> top = managerPermissionExMapper.selectTopPermission(ids);
        Set<Integer> idSet = new HashSet<>();
        idSet.addAll(ids);
        idSet.addAll(buttonList);
        idSet.addAll(top);
        //查询当前用户的权限
        List<Integer> permissionIdsOfUser = managerPermissionExMapper.queryPermissionIdsOfUser(customerId);
        permissionIdsOfUser.removeAll(idSet);
        //获取当前企业所有员工
        Set<Integer> customerIds = customerExMapper.queryCustomerIdListByCompany(companyId);
        //清除下属员工权限,当前用户权限有修改的情况下才清理下属员工权限
        if (!permissionIdsOfUser.isEmpty()) {
            userPermissionRelationExMapper.clearUserPermissionIn(customerIds, permissionIdsOfUser);
        }

        Set<Integer> customerIdSet = Collections.singleton(customerId);
        int clearCount = userPermissionRelationExMapper.clearUserPermissionIn(customerIdSet, null);
        int updateCount = userPermissionRelationExMapper.saveUserPermissions(customerIdSet, idSet);
        return clearCount >= 0 && updateCount > 0;
    }

    private List<Integer> permissionStrToList(String permissionIds){
        if (StringUtils.isBlank(permissionIds)){
            return Collections.emptyList();
        }
        String[] split = permissionIds.split(",");
        List<Integer> idList = new ArrayList<>();
        for (String str : split){
            idList.add(Integer.valueOf(str));
        }
        return idList;
    }
    
    /**
     * 企业创建查询公司下全部人员
     * @param customerName
     * @param companyId
     * @return
     */
    public List<Customer> listSale(String customerName, Integer companyId,Integer customerStatus) {
    	return customerExMapper.listSale(customerName, companyId,customerStatus);
    }
    
    /**
     * 计算该部门下的员工数
     * @param companyId
     * @param structId
     * @return
     */
    public Integer countCustomerByStruct(Integer companyId, Integer structId) {
    	return customerExMapper.countCustomerByStruct(companyId, structId);
    }

    /**
     * 获取拥有某个权限的人员列表
     * @return
     */
    public List<CustomerDTO> getCustomerByPermission(Integer companyId, String permissionCodeStr) {
        String[] permissionCodeArray = permissionCodeStr.split(",");
        List<ManagerPermission> managerPermissions = managerPermissionExMapper.queryPermissionsByPermissionCodes(permissionCodeArray,(byte)1);
        if (CollectionUtils.isEmpty(managerPermissions)) {
            return new ArrayList<>();
        }
        List<Integer> permissionIds = new ArrayList<>();
        managerPermissions.forEach(x->{permissionIds.add(x.getPermissionId());});
        List<Customer> customers = userPermissionRelationExMapper.selectCustomerByPermissionId(companyId,permissionIds);
        return BeanUtil.copyList(customers,CustomerDTO.class);
    }

    /**
     * 获取指定企业下的所有业务城市，包括部门所在城市和开展业务的城市
     * @param companyId
     * @return
     */
    public List<UserDataScopeDTO> queryDataScopeCityInfoList(Integer companyId){
        List<ServiceConfigCityDTO> res = new ArrayList<>();
        String restUrl = RestLocators.config().getRestUrl("/city/listOpenedCityByCompany");
        Map<String, Object> paraMap = new HashMap<>();
        paraMap.put("companyId", companyId);
        RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, ServiceConfigCityDTO.class);
        if(null!=restResponse && restResponse.isSuccess() && restResponse.getData()!=null){
            res = (List<ServiceConfigCityDTO>) restResponse.getData();
        }

        //2.遍历组装城市信息
        List<UserDataScopeDTO> resultList = new ArrayList<UserDataScopeDTO>();
        for(ServiceConfigCityDTO cc:res){
            UserDataScopeDTO dto = new UserDataScopeDTO();
            dto.setCityCode(cc.getCityCode());
            dto.setCityName(cc.getCityName());
            resultList.add(dto);
        }
        return resultList;
    }

    public List<WorkStationDTO> getAppWorkStation(Integer customerId) {
        List<WorkStation> workStationList = workStationExMapper.getWorkStationByCustomerId(customerId);
        return BeanUtil.copyList(workStationList, WorkStationDTO.class);
    }


    @Deprecated
    public List<String> getUserPermissionCodeList(Integer userId) {
        return managerPermissionExMapper.queryPermissionCodesOfUser(userId);
    }

    public RestResponse getPositionList(Integer companyId) {
        try {
            List<String> positionList = customerExMapper.getPositionList(companyId);
            return RestResponse.success(positionList);
        }catch (Exception e){
            logger.error("获取员工岗位信息异常", e);
            return RestResponse.fail(UserErrorCode.POSITION_INFO_ERROR);
        }

    }

    public RestResponse clearUserPermission(Integer customerId) {
        try {
            //删除用户功能权限
            userPermissionRelationExMapper.clearUserPermissionIn(Collections.singleton(customerId), null);
            //删除用户数据权限
            Customer customer = new Customer();
            customer.setCustomerId(customerId);
            customer.setDisccountProportion(0);
            customerExMapper.updateByPrimaryKeySelective(customer);
            return RestResponse.success(null);
        }catch (Exception e){
            logger.error("清除用户权限失败", e);
            return RestResponse.fail(UserErrorCode.PERMISSION_ALLOCATION_FAILED);
        }
    }

    public RestResponse getCustomerByCompanyIdAndMobile(Customer customer){
        Customer customerDetail = customerExMapper.selectCustomerByCompanyIdAndMobile(customer.getCompanyId(),customer.getMobile());
        if(null!=customerDetail){
            return RestResponse.success(BeanUtil.copyObject(customerDetail,CustomerDetailDTO.class));
        }else {
            return RestResponse.fail(UserErrorCode.CUSTOMER_NOT_EXIST);
        }
    }


    public List<Customer> getCustomerByCustomerIds(List<Integer> customerIds) {
        return customerExMapper.getCustomerByCustomerIds(customerIds);
    }

    public List<Integer> getCustomerByStructIds(Set<String> structIds) {
        return customerExMapper.getCustomerByStructIds(structIds);
    }

    private List<CompanyDepartmentDTO> treeToList(List<CompanyDepartmentDTO> result, List<CompanyDepartmentDTO> source){
        if (source == null || source.isEmpty()){
            return Collections.emptyList();
        }
        source.forEach( departmentDTO -> {
            result.add(departmentDTO);
            treeToList(result, departmentDTO.getSubDepartment());
        });
        return result;
    }
}