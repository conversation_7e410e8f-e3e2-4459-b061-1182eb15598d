package com.izu.user.service.user;

import com.izu.framework.database.DynamicRoutingDataSource;
import com.izu.framework.database.MasterSlaveConfig;
import com.izu.framework.database.MasterSlaveConfigs;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.exception.RestErrorException;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.user.config.consts.PermissionConst;
import com.izu.user.config.errcode.UserErrorCode;
import com.izu.user.dto.DriverInfoDTO;
import com.izu.user.dto.LoginUserForApp;
import com.izu.user.dto.ManagerPermissionDTO;
import com.izu.user.dto.customer.CheckPhoneRespDTO;
import com.izu.user.dto.customer.MenuPermReqDTO;
import com.izu.user.dto.customer.MenuPermRespDTO;
import com.izu.user.dto.relation.DataRelationDTO;
import com.izu.user.dto.relation.RelationCodeDTO;
import com.izu.user.entity.*;
import com.izu.user.enums.CustomerStatusEnum;
import com.izu.user.enums.DriverEnums;
import com.izu.user.enums.WhiteListEnum;
import com.izu.user.enums.relation.DataRelationEnum;
import com.izu.user.enums.role.SpecialRoleEnum;
import com.izu.user.enums.role.SystemTypeEnum;
import com.izu.user.service.provider.staff.ClientStaffService;
import com.izu.user.service.provider.staff.ProviderStaffService;
import com.izu.user.service.relation.DataRelationOperateService;
import com.izu.user.service.staffRole.StaffRoleRelationService;
import com.izu.user.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import mapper.ex.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户登录相关功能封装
 *
 * <AUTHOR>
 * @date : 2022/12/23
 */
@Service
@Slf4j
public class UserLoginService {

    @Autowired
    private CustomerExMapper customerExMapper;

    @Autowired
    private CompanyExMapper companyExMapper;

    @Autowired
    private UserPermissionRelationExMapper userPermissionRelationExMapper;

    @Autowired
    private ManagerPermissionExMapper managerPermissionExMapper;

    @Autowired
    private DriverExMapper driverExMapper;
    @Autowired
    private ProviderStaffExMapper providerStaffExMapper;
    @Autowired
    private StaffRoleRelationService staffRoleRelationService;
    @Autowired
    private ClientStaffService clientStaffService;
    @Autowired
    private ProviderStaffService providerStaffService;

    @Autowired
    private DataRelationOperateService dataRelationOperateService;
    @Autowired
    private UserAuthInfoExMapper userAuthInfoExMapper;


    /**
     * 校验手机号是否存在
     *
     * @param phone
     * @return
     */
    @MasterSlaveConfigs(configs = {@MasterSlaveConfig(databaseTag = "mrcar-DataSource", mode = DynamicRoutingDataSource.DataSourceMode.SLAVE)})
    public CheckPhoneRespDTO checkPhone(String phone) {
        final Customer customer = customerExMapper.getActiveByPhone(phone);
        if (customer == null) {
            return new CheckPhoneRespDTO(CheckPhoneRespDTO.PHONE_NOT_EXIST);
        }
        if (customer.getCompanyId() == null) {
            return new CheckPhoneRespDTO(CheckPhoneRespDTO.COMPANY_NOT_EXIST);
        }
        if (companyExMapper.vaildCompany(customer.getCompanyId()) > 0) {
            return new CheckPhoneRespDTO(CheckPhoneRespDTO.VALID);
        } else {
            return new CheckPhoneRespDTO(CheckPhoneRespDTO.COMPANY_NOT_EXIST);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void updateLoginTime(Integer loginUserId, String lastLoginTime) {
        try {
            DateUtil.parse(lastLoginTime, DateUtil.TIME_FORMAT);
        } catch (ParseException e) {
            throw ExceptionFactory.createRestException(UserErrorCode.TIME_FORMAT_ERROR);
        }

        Customer customer = customerExMapper.selectByPrimaryKey(loginUserId);
        if (customer == null) {
            return;
        }

        Integer driverId = customer.getDriverId();
        if (driverId != null && driverId > 0) {
            updateDriverLoginTime(driverId, lastLoginTime);
        }

        updateCustomerLoginTime(loginUserId, lastLoginTime);
    }

    private void updateDriverLoginTime(Integer driverId, String lastLoginTime) {
        Driver driverDB = driverExMapper.selectByPrimaryKey(driverId);
        if (driverDB != null && DriverEnums.DriverStatusEnum.enable.getCode().equals(driverDB.getDriverStatus())) {
            Driver driver = new Driver();
            driver.setDriverId(driverId);
            driver.setLastLoginTime(lastLoginTime);
            driverExMapper.updateByPrimaryKeySelective(driver);
        }
    }

    private void updateCustomerLoginTime(Integer customerId, String lastLoginTime) {
        Customer updateCustomer = new Customer();
        updateCustomer.setCustomerId(customerId);
        updateCustomer.setLastLoginTime(lastLoginTime);
        customerExMapper.updateByPrimaryKeySelective(updateCustomer);
    }


    public List<String> getPhoneByPermId(Integer permId) {
        return userPermissionRelationExMapper.listPhoneByPermId(permId);
    }

    public List<String> getPhoneByUserIds(Set<Integer> userId) {
        return customerExMapper.getPhoneByUserIds(userId);
    }

    public List<String> getPhoneByCompanyId(Integer companyId) {
        return customerExMapper.getPhoneByCompanyId(companyId);
    }

    public List<String> getPhoneByDeptId(Integer deptId) {
        return customerExMapper.getPhoneByStructId(deptId);
    }

    public List<Integer> getCustomerIdByPhones(List<String> phones) {
        return customerExMapper.getCustomerIdByPhones(phones);
    }

    public MenuPermRespDTO getAllMenuPermOfCurrentLogin(MenuPermReqDTO reqDTO) {
        final List<ManagerPermission> allPermission = managerPermissionExMapper.getAllPermission(reqDTO);
        final Set<String> holdPerms = new HashSet<>(allPermission.size());
        final Set<String> menuUrls = new HashSet<>(allPermission.size());
        for (ManagerPermission managerPermission : allPermission) {
            holdPerms.add(managerPermission.getPermissionCode());
            if (StringUtils.isNotBlank(managerPermission.getMenuUrl())) {
                menuUrls.add(managerPermission.getMenuUrl());
            }
        }
        List<ManagerPermission> menuPermList = allPermission.stream().filter(p -> PermissionConst.PermissionType.MENU.byteValue() == p.getPermissionType()).collect(Collectors.toList());
        final List<ManagerPermissionDTO> permissionsTree = getPermissionsTree(menuPermList);
        //去掉首页的菜单
        permissionsTree.removeIf(permission -> permission.getPermissionCode().equals("index"));
        MenuPermRespDTO result = new MenuPermRespDTO();
        result.setPermissionsTree(permissionsTree);
        result.setMenuUrls(menuUrls);
        result.setHoldRoles(new HashSet<>(1));
        result.setHoldPerms(holdPerms);
        return result;
    }


    /**
     * 根据电话号码获取登录会话信息，同时考虑用户可能设置的默认登录平台。
     *
     * @param phone 用户的电话号码
     * @return 如果用户存在，返回登录用户信息；否则返回null。
     * @throws RestErrorException 如果发生REST API错误
     */
    public LoginUserForApp getLoginSessionInfoForApp(String phone) throws RestErrorException {
        // 获取用户默认登录平台
        SystemTypeEnum loginSystemType = getLoginSystemType(phone);
        LoginUserForApp loginUserForApp = loginSystemType == SystemTypeEnum.CUSTOMER ?
                this.getClientLoginSessionInfoForApp(phone) :
                this.getProviderLoginSessionInfoForApp(phone);
        if (loginUserForApp == null) {
            loginUserForApp = loginSystemType == SystemTypeEnum.CUSTOMER ?
                    this.getProviderLoginSessionInfoForApp(phone) :
                    this.getClientLoginSessionInfoForApp(phone);
        }
        // 如果没有找到用户信息，直接返回null
        if (loginUserForApp == null) {
            return null;
        }
        //加载一下用户的认证信息
        UserAuthInfo authInfo = userAuthInfoExMapper.getUserAuthInfoByMobile(phone);
        if (null != authInfo && DriverEnums.DriverVerifyEnum.authorized.getStatus() == authInfo.getVerifyStatus()) {
            loginUserForApp.setIdCardNo(authInfo.getCertNo());
            loginUserForApp.setVerifyStatus(DriverEnums.DriverVerifyEnum.authorized.getStatus());
        } else {
            loginUserForApp.setVerifyStatus(DriverEnums.DriverVerifyEnum.unauthorized.getStatus());
        }
        return loginUserForApp;
    }

    public LoginUserForApp getClientLoginSessionInfoForApp(String phone) throws RestErrorException {
        LoginUserForApp loginSessionInfoForApp = clientStaffService.getLoginSessionInfoForApp(phone);
        if (loginSessionInfoForApp == null) {
            return null;
        }
        boolean existed = providerStaffService.existsProviderAccount(phone);
        loginSessionInfoForApp.setIsAllowSwitchLoginSystem(existed ? 1 : 0);
        return loginSessionInfoForApp;
    }

    public LoginUserForApp getProviderLoginSessionInfoForApp(String phone) throws RestErrorException {
        LoginUserForApp loginSessionInfoForApp = providerStaffService.getLoginSessionInfoForApp(phone);
        if (loginSessionInfoForApp == null) {
            return null;
        }
        boolean existed = clientStaffService.existsProviderAccount(phone);
        loginSessionInfoForApp.setIsAllowSwitchLoginSystem(existed ? 1 : 0);
        return loginSessionInfoForApp;
    }


    /**
     * 查询用户应该登录哪个平台
     *
     * @param phone 用户手机号
     * @return 应该登录的平台，如果没有设置默认登录平台，则默认返回客户端
     */
    private SystemTypeEnum getLoginSystemType(String phone) {
        // 先获取用户设置的默认登录平台
        List<DataRelation> dataRelations = dataRelationOperateService.listDataRelation(DataRelationEnum.APP_USER_DEFAULT_LOGIN_PLATFORM, phone);

        // 尝试从DataRelation中提取系统类型
        Optional<DataRelation> lastDataRelation = dataRelations.stream()
                .reduce((first, second) -> second); // 使用reduce操作获取最后一个元素

        return lastDataRelation
                .map(dataRelation -> {
                    if (StringUtils.isNotBlank(dataRelation.getRelatedCode())) {
                        try {
                            return SystemTypeEnum.getByCode(Byte.parseByte(dataRelation.getRelatedCode()));
                        } catch (NumberFormatException e) {
                            log.warn("app默认登录平台配置值错误，手机号: {}, 配置值: {}", phone, dataRelation.getRelatedCode());
                            return null; // 如果解析失败，返回null
                        }
                    }
                    return null; // 如果RelatedCode为空，也返回null
                })
                .orElse(SystemTypeEnum.CUSTOMER); // 如果没有找到DataRelation，返回客户端作为默认值
    }

    public List<String> listMenuPermCodeForApp(Integer userId) {
        return managerPermissionExMapper.listMenuPermCodeForApp(userId);
    }

    public CheckPhoneRespDTO checkPhoneForProviderApp(String phone) {
        return checkProvider(phone);
    }

    public CheckPhoneRespDTO checkPhoneForClientApp(String phone) {
        return checkClient(phone);
    }

    public CheckPhoneRespDTO checkClient(String phone) {
        CustomerExample customerExample = new CustomerExample();
        customerExample.createCriteria().andMobileEqualTo(phone);
        List<Customer> customerList = customerExMapper.selectByExample(customerExample);
        if (customerList.isEmpty()) {
            //客户端账号不存在
            return new CheckPhoneRespDTO(CheckPhoneRespDTO.PHONE_NOT_EXIST);
        }
        List<Customer> enabledCustomers = customerList.stream()
                .filter(customer -> customer.getCustomerStatus().equals(CustomerStatusEnum.ENABLE.getStatus()))
                .collect(Collectors.toList());
        if (enabledCustomers.isEmpty()) {
            return new CheckPhoneRespDTO(CheckPhoneRespDTO.PHONE_DISABLED);
        }
        if (enabledCustomers.size() > 1) {
            return new CheckPhoneRespDTO(CheckPhoneRespDTO.PHONE_MORE_ONE);
        }
        Customer enabledCustomer = enabledCustomers.get(0);
        Integer companyId = enabledCustomer.getCompanyId();
        Byte whiteListEnum = enabledCustomer.getWhiteList();
        CheckPhoneRespDTO checkPhoneRespDTO;
        if (companyExMapper.vaildCompany(companyId) > 0) {
            checkPhoneRespDTO = new CheckPhoneRespDTO(CheckPhoneRespDTO.VALID);
            checkPhoneRespDTO.setWhiteListUser(whiteListEnum);
            checkPhoneRespDTO.setPwd(enabledCustomer.getLoginPassword());
            checkPhoneRespDTO.setSalt(enabledCustomer.getSalt());
        } else {
            // 首汽驾驶员 企业信息为空的兼容
            Set<String> roleSet = staffRoleRelationService.getRoleCodeByStaffCode(enabledCustomer.getCustomerCode());
            if (roleSet.contains(SpecialRoleEnum.PROVIDER_DRIVER.getRoleCode())) {
                checkPhoneRespDTO = new CheckPhoneRespDTO(CheckPhoneRespDTO.VALID);
                checkPhoneRespDTO.setWhiteListUser(whiteListEnum);
                checkPhoneRespDTO.setPwd(enabledCustomer.getLoginPassword());
                checkPhoneRespDTO.setSalt(enabledCustomer.getSalt());
            } else {
                checkPhoneRespDTO = new CheckPhoneRespDTO(CheckPhoneRespDTO.COMPANY_NOT_EXIST);
            }
        }
        return checkPhoneRespDTO;
    }

    public CheckPhoneRespDTO checkPhoneForApp(String phone) {
        //先校验客户端手机号
        CheckPhoneRespDTO checkPhoneRespDTO = checkClient(phone);
        //客户端账号ok可以直接返回
        if (checkPhoneRespDTO.getCheckCode().equals(CheckPhoneRespDTO.VALID)) {
            return checkPhoneRespDTO;
        }
        //如果客户端账号重复，直接返回即可
        if (checkPhoneRespDTO.getCheckCode().equals(CheckPhoneRespDTO.PHONE_MORE_ONE)) {
            return checkPhoneRespDTO;
        }
        CheckPhoneRespDTO providerCheckResp = checkProvider(phone);
        //如果运营端账号不存在，返回客户端的状态
        if (providerCheckResp.getCheckCode().equals(CheckPhoneRespDTO.PHONE_NOT_EXIST)) {
            return checkPhoneRespDTO;
        }
        //如果运营端账号存在，返回运营端的状态
        return providerCheckResp;
    }


    public RestResponse modifyLoginUserPhone(@Verify(param = "oldPhone", rule = "required") String oldPhone,
                                             @Verify(param = "newPhone", rule = "required") String newPhone) {
        if (oldPhone.equals(newPhone)) {
            RestResponse restResponse = RestResponse.fail(UserErrorCode.CHANGE_MOBILE_FAILED);
            restResponse.setMsg("新手机号不能与旧手机号相同");
            return restResponse;
        }
        Customer customer = customerExMapper.getActiveByPhone(oldPhone);
        if (customer.getWhiteList() == WhiteListEnum.YES.getCode().intValue()) {
            RestResponse restResponse = RestResponse.fail(UserErrorCode.CHANGE_MOBILE_FAILED);
            restResponse.setMsg("该手机号，暂不支持修改");
            return restResponse;
        }
        Customer customerNew = customerExMapper.getActiveByPhone(newPhone);
        if (null != customerNew) {
            RestResponse restResponse = RestResponse.fail(UserErrorCode.CHANGE_MOBILE_FAILED);
            restResponse.setMsg("新手机号已经是系统用户，不能修改为该手机号");
            return restResponse;
        }
        Driver driver = driverExMapper.selectActiveDriverByMobile(newPhone);
        if (null != driver) {
            RestResponse restResponse = RestResponse.fail(UserErrorCode.CHANGE_MOBILE_FAILED);
            restResponse.setMsg("新手机号已注册为司机，不能修改为该手机号");
            return restResponse;
        }
        Customer updateCustomer = new Customer();
        updateCustomer.setCustomerId(customer.getCustomerId());
        updateCustomer.setMobile(newPhone);
        customerExMapper.updateByPrimaryKeySelective(updateCustomer);
        return RestResponse.success(Boolean.TRUE);
    }


    public Boolean modifyLoginCustomerIcon(Integer userId, String headIcon) {
        Customer customer = new Customer();
        customer.setCustomerId(userId);
        customer.setHeadIcon(headIcon);
        return customerExMapper.updateByPrimaryKeySelective(customer) > 0;
    }

    public Boolean modifyDriverIcon(Integer driverId, String headIcon) {
        Driver driver = new Driver();
        driver.setDriverId(driverId);
        driver.setHeadIcon(headIcon);
        return driverExMapper.updateByPrimaryKeySelective(driver) > 0;
    }


    private List<ManagerPermissionDTO> getPermissionsTree(List<ManagerPermission> permissions) {
        final Map<Integer,/* 父权限ID */ List<ManagerPermission>> permMap = permissions.stream().collect(Collectors.groupingBy(ManagerPermission::getParentPermissionId));
        return getChildren(permMap, 0);
    }


    private List<ManagerPermissionDTO> getChildren(Map<Integer,/* 父权限ID */ List<ManagerPermission>> permMap, Integer parentPermissionId) {
        List<ManagerPermission> childPermList = permMap.get(parentPermissionId);
        if (childPermList == null || childPermList.isEmpty()) {
            return Collections.emptyList();
        }
        //将菜单排序
        childPermList = childPermList.stream().sorted(Comparator.comparing(ManagerPermission::getSortSeq).thenComparing(ManagerPermission::getParentPermissionId)).collect(Collectors.toList());
        ArrayList<ManagerPermissionDTO> childPermDTOList = new ArrayList<>(childPermList.size());
        for (ManagerPermission childPerm : childPermList) {
            final ManagerPermissionDTO childPermDTO = BeanUtil.copyObject(childPerm, ManagerPermissionDTO.class);
            childPermDTO.setChildPermissions(getChildren(permMap, childPerm.getPermissionId()));
            childPermDTOList.add(childPermDTO);
        }
        return childPermDTOList;
    }


    /**
     * @param phone
     * @return
     */
    private CheckPhoneRespDTO checkProvider(String phone) {
        CheckPhoneRespDTO checkPhoneRespDTO;
        List<ProviderStaff> providerStaffs = providerStaffExMapper.getActiveByPhone(phone);
        if (providerStaffs == null || providerStaffs.isEmpty()) {
            checkPhoneRespDTO = new CheckPhoneRespDTO(CheckPhoneRespDTO.PHONE_NOT_EXIST);
        } else {
            //判断该人是否是商务车司机/商务车调度员，如果是则允许登录
            ProviderStaff providerStaff = providerStaffs.get(0);
            Set<String> roleSet = staffRoleRelationService.getRoleCodeByStaffCode(providerStaff.getProviderStaffCode());
            if (roleSet.contains(SpecialRoleEnum.PROVIDER_BUSINESS_DRIVER.getRoleCode())
                    || roleSet.contains(SpecialRoleEnum.BUSINESS_DISPATCH.getRoleCode())
                    || roleSet.contains(SpecialRoleEnum.CHANGE_CAR_MANAGER.getRoleCode())
            ) {
                checkPhoneRespDTO = new CheckPhoneRespDTO(CheckPhoneRespDTO.VALID);
                checkPhoneRespDTO.setWhiteListUser(WhiteListEnum.NO.getCode());
                checkPhoneRespDTO.setPwd(providerStaff.getLoginPassword());
                checkPhoneRespDTO.setSalt(providerStaff.getSalt());
            } else {
                checkPhoneRespDTO = new CheckPhoneRespDTO(CheckPhoneRespDTO.PHONE_NOT_EXIST);
            }
        }
        return checkPhoneRespDTO;
    }

    public boolean recordDefaultLoginSystemType(String phone, Integer systemType) {
        DataRelationDTO dataRelationDTO = new DataRelationDTO();
        dataRelationDTO.setDataRelationCode(DataRelationEnum.APP_USER_DEFAULT_LOGIN_PLATFORM.getCode());
        dataRelationDTO.setBussCode(phone);
        RelationCodeDTO relationCodeDTO = new RelationCodeDTO();
        relationCodeDTO.setRelatedCode(systemType.toString());
        dataRelationDTO.setRelList(Collections.singletonList(relationCodeDTO));
        dataRelationOperateService.createDataRelation(dataRelationDTO);
        return true;
    }
}



