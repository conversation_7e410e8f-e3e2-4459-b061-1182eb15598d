package com.izu.user.service.provider.productpack;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Maps;
import com.izu.config.dto.BusinessCodeDTO;
import com.izu.config.dto.EnterpriseConfigDTO;
import com.izu.config.restApi.BusinessConfigApi;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.config.enums.BussinessTypeDictionary;
import com.izu.mrcar.config.enums.EnterpriseConfigEnum;
import com.izu.user.config.consts.PermissionConst;
import com.izu.user.config.errcode.UserErrorCode;
import com.izu.user.dto.ManagerPermissionDTO;
import com.izu.user.dto.config.*;
import com.izu.user.dto.provider.permission.PermTreeOption;
import com.izu.user.entity.*;
import com.izu.user.entity.mongo.ProductPack;
import com.izu.user.entity.mongo.ProductPackBusinessCode;
import com.izu.user.entity.mongo.ProductPackEnterpriseConfig;
import com.izu.user.enums.CompanyStatusEnum;
import com.izu.user.enums.LoginSystemEnum;
import com.izu.user.enums.MenuOpenModeEnum;
import com.izu.user.enums.SeqTypeEnum;
import com.izu.user.enums.productPack.ProductPackTypeEnum;
import com.izu.user.enums.productPack.ProductPackVisibilityScopeEnum;
import com.izu.user.enums.relation.DataRelationEnum;
import com.izu.user.executor.ThreadPoolManager;
import com.izu.user.repository.mongo.ProductPackRepository;
import com.izu.user.service.common.SequenceGenerator;
import com.izu.user.service.provider.permission.ManagerPermissionService;
import com.izu.user.service.relation.DataRelationOperateService;
import com.izu.user.service.user.ClearLoginInfoService;
import com.izu.user.util.SingletonFactory;
import mapper.ex.CompanyExMapper;
import mapper.ex.CustomerExMapper;
import mapper.ex.DataRelationExMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.*;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.lang.reflect.Array;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import cn.hutool.core.bean.BeanUtil;

/**
 * <AUTHOR>
 * @date 2023/1/13 14:33
 */
@Service
public class ProductPackService {
    @Resource
    @Lazy
    private ProductPackRepository productPackRepository;
    @Resource
    private MongoTemplate mongoTemplate;
    @Resource
    private ManagerPermissionService managerPermissionService;
    @Resource
    private SequenceGenerator sequenceGenerator;
    @Resource
    private DataRelationOperateService dataRelationOperateService;
    @Resource
    private CustomerExMapper customerExMapper;
    @Resource
    private CompanyExMapper companyExMapper;
    @Resource
    private ClearLoginInfoService clearLoginInfoService;

    @Autowired
    private DataRelationExMapper dataRelationMapper;

    /**
     * 保存产品包
     */
    @Transactional
    public RestResponse saveProductPack(ProductPackDTO productPackDTO) {
        RestResponse checkResponse = this.checkProductName(productPackDTO);
        if (!checkResponse.isSuccess()) {
            return checkResponse;
        }
        //处理企业配置参数：只保存打开的配置
        ProductPackEnterpriseConfigDTO enterpriseConfig =
                this.dealEnterpriseConfig(productPackDTO.getEnterpriseConfigDTO());
        productPackDTO.setEnterpriseConfigDTO(enterpriseConfig);
        ProductPack productPackToSave = BeanUtil.copyProperties(productPackDTO, ProductPack.class);
        //处理可见范围
        productPackToSave.setVisibilityScopeList(ProductPackVisibilityScopeEnum.isContainsAll(productPackDTO.getVisibilityScope()));
        productPackToSave.setUpdateId(productPackDTO.getStaffId());
        productPackToSave.setUpdateName(productPackDTO.getStaffName());
        productPackToSave.setUpdateTime(new Date());
        List<Integer> userCarTypeBusinessCodeList = enterpriseConfig
                .getUseCarType()
                .stream()
                .map(ProductPackBusinessCodeDTO::getBusinessCode)
                .collect(Collectors.toList());
        productPackToSave.setUseCarType(userCarTypeBusinessCodeList);
        //新增需要生成产品包编码
        if (StringUtils.isBlank(productPackDTO.getPackCode())) {
            // 此处应保证code唯一
            final String generate = sequenceGenerator.generate(new Date(), SeqTypeEnum.PRODUCT_PACK);
            productPackToSave.setPackCode(generate);
            productPackToSave.setValid((byte) 1);
            productPackToSave.setCreateId(productPackDTO.getStaffId());
            productPackToSave.setCreateName(productPackDTO.getStaffName());
            productPackToSave.setCreateTime(new Date());
        } else {
            // 编辑 需要考虑老版本app的权限
            ProductPack productPackDB = productPackRepository.getProductPackByCodeWithoutEnterpriseConfig(productPackDTO.getPackCode());
            if (productPackDB == null) {
                return RestResponse.fail(UserErrorCode.PRODUCT_PACK_NOT_FAIL);
            }
            //APP老版本的权限不允许被清理掉
            List<String> oldAppPermSysCodeList = new ArrayList<>();
            if (productPackDB.getAppPerm() != null && !productPackDB.getAppPerm().isEmpty()) {
                PermTreeOption permTreeOption = new PermTreeOption();
                permTreeOption.setQuerySysPermCodeList(productPackDB.getAppPerm());
                permTreeOption.setMenuOpenModeEnum(MenuOpenModeEnum.APP);
                permTreeOption.setLessAppVersion(PermissionConst.APP_HOMEPAGE_MODIFY_VERSION_NUMBER);
                permTreeOption.setValid(Boolean.TRUE);
                List<ManagerPermission> permList = managerPermissionService.getPermList(permTreeOption);
                List<String> permSysCodeList = permList.stream().map(ManagerPermission::getPermSysCode).collect(Collectors.toList());
                oldAppPermSysCodeList.addAll(permSysCodeList);
            }
            if (productPackDTO.getAppPerm() != null) {
                oldAppPermSysCodeList.addAll(productPackDTO.getAppPerm());
            }
            productPackToSave.setAppPerm(oldAppPermSysCodeList);
            productPackToSave.setId(productPackDB.getId());
            productPackToSave.setValid(productPackDB.getValid());
            productPackToSave.setCreateId(productPackDB.getCreateId());
            productPackToSave.setCreateName(productPackDB.getCreateName());
            productPackToSave.setCreateTime(productPackDB.getCreateTime());
        }
        productPackRepository.save(productPackToSave);
        // 编辑权限包需要清缓存
        if (StringUtils.isNotBlank(productPackDTO.getPackCode())) {
            clearCacheLoginInfo(CollectionUtil.newArrayList(productPackDTO.getPackCode()));
        }
        return RestResponse.success(null);
    }

    private RestResponse checkProductName(ProductPackDTO productPackDTO) {
        Query query = new Query();
        query.addCriteria(Criteria.where("packName").is(productPackDTO.getPackName()));
        query.addCriteria(Criteria.where("valid").is(1));
        //修改产品包，同样需要判断名称是否一致
        if (StringUtils.isNotBlank(productPackDTO.getPackCode())) {
            query.addCriteria(Criteria.where("packCode").ne(productPackDTO.getPackCode()));
        }
        long count = mongoTemplate.count(query, ProductPack.class);
        if (count > 0) {
            return RestResponse.fail(UserErrorCode.PRODUCT_PACK_NAME_EXIST);
        }
        return RestResponse.success(Boolean.TRUE);
    }

    private ProductPackEnterpriseConfigDTO dealEnterpriseConfig(ProductPackEnterpriseConfigDTO enterpriseConfigDTO) {
        if (enterpriseConfigDTO == null) {
            enterpriseConfigDTO = new ProductPackEnterpriseConfigDTO();
        }
        enterpriseConfigDTO.setBasicFunction(filterOpened(enterpriseConfigDTO.getBasicFunction()));
        enterpriseConfigDTO.setServiceType(filterOpened(enterpriseConfigDTO.getServiceType()));
        enterpriseConfigDTO.setInternalCar(filterOpened(enterpriseConfigDTO.getInternalCar()));
        enterpriseConfigDTO.setPrivateCarFunction(filterOpened(enterpriseConfigDTO.getPrivateCarFunction()));
        enterpriseConfigDTO.setUseCarType(filterOpened(enterpriseConfigDTO.getUseCarType()));
        enterpriseConfigDTO.setSelfCarConfig(filterOpened(enterpriseConfigDTO.getSelfCarConfig()));
        return enterpriseConfigDTO;
    }

    private List<ProductPackBusinessCodeDTO> filterOpened(List<ProductPackBusinessCodeDTO> paramList) {
        if (paramList == null || paramList.isEmpty()) {
            return Collections.emptyList();
        }
        return paramList.stream()
                .filter(ProductPackBusinessCodeDTO::getIsOpen)
                .collect(Collectors.toList());
    }


    /**
     * 根据产品包编码获取产品包,不包含企业配置
     */
    public ProductPackDTO getProductPackByCode(ProductPackInvalidReqDTO dto) {
        ProductPack productPack = productPackRepository.getProductPackByCodeWithoutEnterpriseConfig(dto.getPackCode());
        return BeanUtil.copyProperties(productPack, ProductPackDTO.class);
    }

    /**
     * 根据产品包编码获取产品包(包含权限树，企业配置)
     */
    public RestResponse getProductPackByCodeDetails(ProductPackInvalidReqDTO dto) {
        ProductPackDetailDTO productPackDetailDTO = null;
        ProductPack productPack = new ProductPack();
        if (StringUtils.isNotBlank(dto.getPackCode())) {
            productPack.setPackCode(dto.getPackCode());
            productPack = productPackRepository.findOne(buildExample(productPack)).orElse(null);
            if (productPack == null) {
                return RestResponse.fail(UserErrorCode.PRODUCT_PACK_NOT_FAIL);
            }
            productPackDetailDTO = BeanUtil
                    .copyProperties(productPack, ProductPackDetailDTO.class,"appPerm","pcPerm");
        }
        // 分为编辑和新建页面查询
        if (productPackDetailDTO == null) {
            productPackDetailDTO = new ProductPackDetailDTO();
        }
        List<BusinessCodeDTO> useCarTypeList = null;
        if (productPackDetailDTO.getEnterpriseConfigDTO() == null) {
            EnterpriseConfigDTO enterpriseConfigDTO = new EnterpriseConfigDTO();
            productPackDetailDTO.setEnterpriseConfigDTO(enterpriseConfigDTO);
            enterpriseConfigDTO.setBasicFunction(getBusinessCodeDTOS(EnterpriseConfigEnum.EnterpriseConfigGroupEnum.BASIC_FUNCTION.getCode()));
            useCarTypeList = getBusinessCodeDTOS(EnterpriseConfigEnum.EnterpriseConfigGroupEnum.USE_CAR_TYPE.getCode());
        }
        // 设置pc、app权限
        PermTreeOption option = new PermTreeOption();
        option.setMenuOpenModeEnum(MenuOpenModeEnum.ENTERPRISE);
        option.setCheckedSysCodeList(productPack.getPcPerm());
        if (ObjectUtil.equals((byte) 1, dto.getReadonly())) {
            option.setQuerySysPermCodeList(productPack.getPcPerm());
        }
        option.setValid(Boolean.TRUE);
        productPackDetailDTO.setPcPerm(managerPermissionService.getPermTree(option));
        option.setMenuOpenModeEnum(MenuOpenModeEnum.APP);
        option.setCheckedSysCodeList(productPack.getAppPerm());
        option.setStartAppVersion(PermissionConst.APP_HOMEPAGE_MODIFY_VERSION_NUMBER);
        if (ObjectUtil.equals((byte) 1, dto.getReadonly())) {
            option.setQuerySysPermCodeList(productPack.getAppPerm());
        }
        productPackDetailDTO.setAppPerm(managerPermissionService.getPermTree(option));
        // 解析企业配置
        if (CollectionUtil.isNotEmpty(productPack.getUseCarType())) {
            useCarTypeList = productPack.getUseCarType().stream().map(e -> {
                BusinessCodeDTO businessCodeDTO = new BusinessCodeDTO();
                businessCodeDTO.setBusinessCode(e);
                return businessCodeDTO;
            }).collect(Collectors.toList());
        }
        EnterpriseConfigDTO enterpriseConfigDTO = productPackDetailDTO.getEnterpriseConfigDTO();
        enterpriseConfigDTO.setUseCarType(useCarTypeList);
        productPackDetailDTO.setEnterpriseConfigDTO(BusinessConfigApi.getProductPackEnterpriseConfig(enterpriseConfigDTO));
        productPackDetailDTO.setVisibilityScope(ProductPackVisibilityScopeEnum.isEqualAll(productPack.getVisibilityScopeList()));
        productPackDetailDTO.setVisibilityScopeName(ProductPackVisibilityScopeEnum.getDescByCodes(productPack.getVisibilityScopeList()));
        productPackDetailDTO.setPackTypeDesc(ProductPackTypeEnum.getDescByType(productPack.getPackType()));
        return RestResponse.success(productPackDetailDTO);
    }

    private List<BusinessCodeDTO> getBusinessCodeDTOS(Byte code) {
        List<BussinessTypeDictionary> useCarTypeConfigGroupAndChecked = BussinessTypeDictionary.getBusinessTypeDictionaryByEnterpriseConfigGroupAndChecked(code);
        return useCarTypeConfigGroupAndChecked.stream().map(e -> {
            BusinessCodeDTO businessCodeDTO = new BusinessCodeDTO();
            businessCodeDTO.setBusinessCode(e.getValue());
            businessCodeDTO.setIsOpen(e.getChecked());
            businessCodeDTO.setBusinessValue(e.getBusinessValue());
            return businessCodeDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 分页列表
     * @param productPackQueryDTO
     * @return
     */
    public PageDTO getProductPackPage(ProductPackQueryDTO productPackQueryDTO) {
        //定义example条件对象
        int pageNum = 1, pageSize = 2000;
        if (productPackQueryDTO.getPageNum() != null) {
            pageNum = productPackQueryDTO.getPageNum();
        }
        if (productPackQueryDTO.getPageSize() != null) {
            pageSize = productPackQueryDTO.getPageSize();
        }
        Query query = new Query();
        //动态拼接查询条件
        if (StringUtils.isNotBlank(productPackQueryDTO.getPackCode())){
            query.addCriteria(Criteria.where("packCode").is(productPackQueryDTO.getPackCode()));
        }
        if (StringUtils.isNotBlank(productPackQueryDTO.getPackName())){
            String keyword = Pattern.quote(productPackQueryDTO.getPackName());
            Pattern pattern = Pattern.compile("^.*" + keyword + ".*$", Pattern.CASE_INSENSITIVE);
            query.addCriteria(Criteria.where("packName").regex(pattern));
        }
        if (productPackQueryDTO.getPackType() != null) {
            query.addCriteria(Criteria.where("packType").is(productPackQueryDTO.getPackType()));
        }
        if(productPackQueryDTO.getValid()!=null){
            query.addCriteria(Criteria.where("valid").is(productPackQueryDTO.getValid()));
        }
        if(productPackQueryDTO.getVisibilityScope()!=null){
            query.addCriteria(Criteria.where("visibilityScopeList").is(productPackQueryDTO.getVisibilityScope()));
        }

        //查询结果集
        List<ProductPack> productPackList = mongoTemplate.find(query, ProductPack.class);

        //产品包使用企业批量查询
        List<String> packCodeList = productPackList.stream().map(ProductPack::getPackCode).collect(Collectors.toList());
        Map<String, Integer> packCompanySizeMap = shareCompanyNum(packCodeList);

        List<ProductPackDTO> productPackDTOList = productPackList.stream().peek(this::setAllowedVehicleNum).map(pack -> {
            {
                ProductPackDTO productPackDTO = BeanUtil.copyProperties(pack, ProductPackDTO.class);
                final String typeName = matchVehicleType(pack.getUseCarType());
                productPackDTO.setUseCarTypeName(typeName);
                productPackDTO.setVisibilityScope(ProductPackVisibilityScopeEnum.isEqualAll(pack.getVisibilityScopeList()));
                productPackDTO.setVisibilityScopeName(ProductPackVisibilityScopeEnum.getDescByCodes(pack.getVisibilityScopeList()));
                productPackDTO.setPackTypeDesc(ProductPackTypeEnum.getDescByType(pack.getPackType()));
                //分配企业个数
                Integer size = packCompanySizeMap.get(pack.getPackCode());
                if(Objects.nonNull(size)){
                    productPackDTO.setCompanyNum(size);
                }else {
                    productPackDTO.setCompanyNum(0);
                }
                return productPackDTO;
            }
        }).collect(Collectors.toList());

        if(Objects.isNull(productPackQueryDTO.getCompanyCountSortType())){
            productPackDTOList =productPackDTOList.stream().sorted(Comparator.comparing(ProductPackDTO::getUpdateTime).reversed()).collect(Collectors.toList());
        }else {
            if(Objects.equals(productPackQueryDTO.getCompanyCountSortType(),1)){
                productPackDTOList =productPackDTOList.stream().sorted(Comparator.comparing(ProductPackDTO::getCompanyNum)).collect(Collectors.toList());
            }else {
                productPackDTOList =productPackDTOList.stream().sorted(Comparator.comparing(ProductPackDTO::getCompanyNum).reversed()).collect(Collectors.toList());
            }
        }
        //由于需要根据分配企业个数排序，故改成内存分页
        PageDTO<ProductPackDTO> page = getPage(productPackDTOList, pageNum, pageSize);

        List<CompletableFuture<ProductPackDTO>> futureList = page.getResult()
                .stream()
                .map(pack -> CompletableFuture.supplyAsync(() -> {
                    final Integer counter = countNumOfLeafPerm(countNumOfLeafPermByPermCodes(pack.getPcPerm()));
                    pack.setPcPermNum(counter);
                    return pack;
                }, ThreadPoolManager.getsInstance().getExecutor()))
                .map(future -> future.thenApply(pack -> {
                    final Integer counter = countAllCheckedNum(countNumOfLeafPermByPermCodesForApp(pack.getAppPerm()));
                    pack.setAppPermNum(counter);
                    return pack;
                })).collect(Collectors.toList());

        List<ProductPackDTO> productPackDTOS = futureList.stream().map(CompletableFuture::join).collect(Collectors.toList());
        page.setResult(productPackDTOS);
        return page;
    }


    private Map<String, Integer> shareCompanyNum(List<String> packCodeList) {
        List<DataRelation> dataRelations = dataRelationOperateService.listDataRelationByRelatedCode(DataRelationEnum.CUSTOMER_PRODUCT_FUNC, packCodeList);
        if (dataRelations.isEmpty()) {
            return new HashMap<>();
        }
        final List<String> companyCodeList = dataRelations.stream().filter(r -> StringUtils.isNotBlank(r.getRelatedCode()))
                .map(DataRelation::getBussCode)
                .collect(Collectors.toList());

        if (companyCodeList.isEmpty()) {
            return new HashMap<>();
        }

        final CompanyExample companyExample = new CompanyExample();
        companyExample.createCriteria().andCustomerCodeIn(companyCodeList)
                .andCompanyStatusEqualTo(CompanyStatusEnum.VALID.getStatus());
        List<Company> companyList = companyExMapper.selectByExample(companyExample);
        if (companyList.isEmpty()) {
            return new HashMap<>();
        }
        List<String> validCompanyCodeList = companyList.stream().map(Company::getCustomerCode).collect(Collectors.toList());

        Map<String, List<DataRelation>> packCodeCompanyMap = dataRelations.stream().collect(Collectors.groupingBy(DataRelation::getRelatedCode));

        HashMap<String, Integer> map = Maps.newHashMap();
        packCodeCompanyMap.entrySet().stream().forEach(entry -> {
            List<String> list = entry.getValue().stream().map(DataRelation::getBussCode).collect(Collectors.toList());
            List<String> containList = list.stream()
                    .filter(validCompanyCodeList::contains)
                    .collect(Collectors.toList());
            map.put(entry.getKey(), containList.size());
        });
        return map;
    }


    public static <T> PageDTO<T> getPage(List<T> allData, int currentPage, int size) {
        if (CollectionUtils.isEmpty(allData)) {
            PageDTO objectPageVO = new PageDTO<>();
            objectPageVO.setPage(1);
            objectPageVO.setPageSize(size);
            objectPageVO.setTotal(0);
            objectPageVO.setResult(Lists.newArrayList());
            return objectPageVO;
        }

        int totalElements = allData.size();
        int totalPages = (int) Math.ceil((double) totalElements / size);

        if (currentPage > totalPages) {
            currentPage = totalPages;
        }

        int fromIndex = (currentPage - 1) * size;
        int toIndex = Math.min(fromIndex + size, totalElements);

        List<T> pageContent = new ArrayList<>();
        if (fromIndex < totalElements) {
            pageContent = allData.subList(fromIndex, toIndex);
        }

        PageDTO<T> page = new PageDTO<>();
        page.setResult(pageContent);
        page.setTotal(totalElements);
        page.setPage(currentPage);
        page.setPageSize(size);

        return page;
    }

    private void setAllowedVehicleNum(ProductPack dto) {
        ProductPackEnterpriseConfig enterpriseConfigDTO = dto.getEnterpriseConfigDTO();
        if (enterpriseConfigDTO != null) {
            List<ProductPackBusinessCode> basicFunction = enterpriseConfigDTO.getBasicFunction();
            if (CollectionUtil.isNotEmpty(basicFunction)) {
                basicFunction.stream().filter(basic -> ObjectUtil.equals(BussinessTypeDictionary.ALLOWED_VEHICLE_NUM.value(), basic.getBusinessCode())).findFirst().ifPresent(productPackBusinessCodeDTO -> dto.setAllowedVehicleNum(StringUtils.isNotBlank(productPackBusinessCodeDTO.getBusinessValue())?Integer.parseInt(productPackBusinessCodeDTO.getBusinessValue()):-1));
            }
        }
    }



    /**
     * 下架产品包
     */
    public RestResponse inValidProductPack(ProductPackInvalidReqDTO dto) {
        // 查询产品包
        String packCode = dto.getPackCode();
        Byte enable = dto.getEnable();
        ProductPack productPack = new ProductPack().setPackCode(packCode);
        productPack = productPackRepository.findOne(buildExample(productPack)).orElse(null);
        if (Objects.equals(productPack.getValid(), enable)) {
            return RestResponse.fail(UserErrorCode.PRODUCT_PACK_VALID_FAIL);
        }
        // 启用
        if (enable == 1) {
            ProductPackDTO checkParam = new ProductPackDTO();
            checkParam.setPackName(productPack.getPackName());
            checkParam.setPackCode(productPack.getPackCode());
            RestResponse checkResponse = this.checkProductName(checkParam);
            if (!checkResponse.isSuccess()) {
                //启用产品包同名
                return RestResponse.fail(UserErrorCode.PRODUCT_PACK_VALID_NAME_EXIST);
            }
            productPack.setValid((byte) 1);
        } else if (enable == 0) {
            // 判断该包是否有引用
            List<DataRelation> dataRelations = dataRelationOperateService.listDataRelationByRelatedCode(DataRelationEnum.CUSTOMER_PRODUCT_FUNC, packCode);
            if (CollectionUtil.isNotEmpty(dataRelations)) {
                final List<String> companyCodeList = dataRelations.stream().filter(r -> StringUtils.isNotBlank(r.getRelatedCode()))
                        .map(DataRelation::getBussCode)
                        .collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(companyCodeList)) {
                    final CompanyExample companyExample = new CompanyExample();
                    companyExample.createCriteria().andCustomerCodeIn(companyCodeList)
                            .andCompanyStatusEqualTo(CompanyStatusEnum.VALID.getStatus());
                    long count = companyExMapper.countByExample(companyExample);
                    if (count > 0) {
                        return RestResponse.fail(UserErrorCode.PRODUCT_PACK_INVALID_FAIL);
                    }
                }
            }
            // 状态置位无效
            productPack.setValid((byte) 0);
        }
        productPack.setUpdateTime(new Date());
        productPack.setUpdateId(dto.getStaffId());
        productPack.setUpdateName(dto.getStaffName());
        productPackRepository.save(productPack);
        return RestResponse.success(null);
    }



    /**
     * 根据企业code获取对应的产品包权限编码
     */
    public ProductPackDTO getPermissionByCompanyCode(String customerCode) {
        List<DataRelation> dataRelations = dataRelationOperateService.listDataRelation(DataRelationEnum.CUSTOMER_PRODUCT_FUNC, customerCode);
        if (CollectionUtil.isNotEmpty(dataRelations)) {
            List<String> list = dataRelations.stream().map(DataRelation::getRelatedCode).collect(Collectors.toList());
            return getPermissionByPackCodes(list);
        } else {
            ProductPackDTO productPackDTO = new ProductPackDTO();
            productPackDTO.setPcPerm(Collections.emptyList());
            productPackDTO.setAppPerm(Collections.emptyList());
            return productPackDTO;
        }
    }


    public RestResponse deleteProductPack(String packCode) {
        ProductPack productPack = new ProductPack();
        productPack.setPackCode(packCode);
        productPack = productPackRepository.findOne(buildExample(productPack)).orElse(null);
        if (productPack == null) {
            return RestResponse.fail(UserErrorCode.PRODUCT_PACK_NOT_FAIL);
        }
        // 判断该包是否有引用
        List<DataRelation> dataRelations = dataRelationOperateService.listDataRelationByRelatedCode(DataRelationEnum.CUSTOMER_PRODUCT_FUNC, packCode);
        if (CollectionUtil.isNotEmpty(dataRelations)) {
            final List<String> companyCodeList = dataRelations.stream().filter(r -> StringUtils.isNotBlank(r.getRelatedCode()))
                    .map(DataRelation::getBussCode)
                    .collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(companyCodeList)) {
                final CompanyExample companyExample = new CompanyExample();
                companyExample.createCriteria().andCustomerCodeIn(companyCodeList)
                        .andCompanyStatusEqualTo(CompanyStatusEnum.VALID.getStatus());
                long count = companyExMapper.countByExample(companyExample);
                if (count > 0) {
                    return RestResponse.fail(UserErrorCode.COMMON_ERROR_MESSAGE, "当前产品包已有分配企业，不可删除");
                }
            }
        }
        Query query = Query.query(Criteria.where("packCode").is(packCode));
        mongoTemplate.remove(query, "productPack");
        return RestResponse.success(null);
    }

    /**
     * 根据企业code获取对应的产品包权限编码
     */
    public ProductPackDetailDTO getPermissionTreeByCompanyCode(String customerCode) {
        ProductPackDetailDTO productPackDetailDTO = new ProductPackDetailDTO();
        ProductPackDTO productPack = getPermissionByCompanyCode(customerCode);
        productPackDetailDTO.setPackCode(productPack.getPackCode());
        PermTreeOption option = new PermTreeOption();
        option.setMenuOpenModeEnum(MenuOpenModeEnum.ENTERPRISE);
        option.setCheckedSysCodeList(productPack.getPcPerm());
        option.setQuerySysPermCodeList(productPack.getPcPerm());
        option.setValid(Boolean.TRUE);
        productPackDetailDTO.setPcPerm(managerPermissionService.getPermTree(option));
        productPackDetailDTO.setPcPermNum(productPackDetailDTO.getPcPerm().size());
        option.setMenuOpenModeEnum(MenuOpenModeEnum.APP);
        option.setStartAppVersion(PermissionConst.APP_HOMEPAGE_MODIFY_VERSION_NUMBER);
        option.setCheckedSysCodeList(productPack.getAppPerm());
        option.setQuerySysPermCodeList(productPack.getAppPerm());
        productPackDetailDTO.setAppPerm(managerPermissionService.getPermTree(option));
        productPackDetailDTO.setAppPermNum(productPackDetailDTO.getAppPerm().size());
        return productPackDetailDTO;
    }

    /**
     * 根据产品包code获取对应的产品包权限编码
     */
    public ProductPackDTO getPermissionByPackCodes(List<String> packCodes) {
        ProductPack productPack = new ProductPack();
        Set<String> pcPerm = new HashSet<>();
        Set<String> appPerm = new HashSet<>();
        List<ProductPack> permissionByPackCodes = productPackRepository.getPermissionByPackCodes(packCodes);
        permissionByPackCodes.forEach(e -> {
            if (CollectionUtils.isNotEmpty(e.getPcPerm())) {
                pcPerm.addAll(e.getPcPerm());
            }
            if (CollectionUtils.isNotEmpty(e.getAppPerm())) {
                appPerm.addAll(e.getAppPerm());
            }
        });
        productPack.setPcPerm(new ArrayList<>(pcPerm));
        productPack.setAppPerm(new ArrayList<>(appPerm));

        return BeanUtil.copyProperties(productPack, ProductPackDTO.class);
    }

    public List<ProductPack> getProductPacks(List<String> packCodes){
        return productPackRepository.getPackCodes(packCodes);
    }

    /**
     * 根据产品包code获取对应的产品包权限编码
     */
    public List<ProductPack> getPackNameByPackCodes(List<String> packCodes) {
        return productPackRepository.getAllowedVehicleNumByCompanyCode(packCodes);
    }

    /**
     * 清理缓存
     */
    public void clearCacheLoginInfo(List<String> productCodes) {
        List<DataRelation> dataRelations = dataRelationOperateService.listDataRelationByRelatedCode(DataRelationEnum.CUSTOMER_PRODUCT_FUNC, productCodes);
        Set<String> companyCodes = dataRelations.stream().map(DataRelation::getBussCode).collect(Collectors.toSet());
        clearCacheLoginInfo(companyCodes);
    }

    /**
     * 清理缓存
     */
    public void clearCacheLoginInfo(Set<String> companyCodes) {
        if (CollectionUtil.isNotEmpty(companyCodes)) {
            List<String> mobileByCompanyCodes = customerExMapper.getMobileByCompanyCodes(companyCodes);
            clearLoginInfoService.clear(mobileByCompanyCodes, LoginSystemEnum.CLIENT);
        }
    }

    private Example<ProductPack> buildExample(ProductPack productPack) {
        if (CollectionUtil.isEmpty(productPack.getUseCarType())) {
            productPack.setUseCarType(null);
        }
        return Example.of(productPack, exampleMatcher);
    }

    private static final ExampleMatcher exampleMatcher = SingletonFactory.getSingleton(SingletonFactory.productPackExampleMatcher, () -> ExampleMatcher.matching()
            .withMatcher("packCode", ExampleMatcher.GenericPropertyMatchers.exact())
            .withMatcher("packName", ExampleMatcher.GenericPropertyMatchers.contains())
            .withMatcher("id", ExampleMatcher.GenericPropertyMatchers.exact())
            .withMatcher("useCarType", ExampleMatcher.GenericPropertyMatchers.contains())
            .withMatcher("valid", ExampleMatcher.GenericPropertyMatchers.exact())
            .withIgnoreNullValues());


    private List<ManagerPermissionDTO> countNumOfLeafPermByPermCodes(List<String> permSystemCodeList) {
        final PermTreeOption permTreeOption = new PermTreeOption();
        permTreeOption.setQuerySysPermCodeList(permSystemCodeList);
        permTreeOption.setValid(Boolean.TRUE);
        return managerPermissionService.getPermTree(permTreeOption);
    }
    private List<ManagerPermissionDTO> countNumOfLeafPermByPermCodesForApp(List<String> permSystemCodeList) {
        final PermTreeOption permTreeOption = new PermTreeOption();
        permTreeOption.setQuerySysPermCodeList(permSystemCodeList);
        permTreeOption.setValid(Boolean.TRUE);
        permTreeOption.setMenuOpenModeEnum(MenuOpenModeEnum.APP);
        permTreeOption.setStartAppVersion(PermissionConst.APP_HOMEPAGE_MODIFY_VERSION_NUMBER);
        return managerPermissionService.getPermTree(permTreeOption);
    }


    private Integer countNumOfLeafPerm(List<ManagerPermissionDTO> permTree) {
        Set<ManagerPermissionDTO> set = new HashSet<>();
        if (CollectionUtil.isEmpty(permTree)) {
            return 0;
        }
        Queue<ManagerPermissionDTO> tempQueue = new LinkedList<>();
        // 计算二级节点
        permTree.forEach(e -> {
            if (CollectionUtil.isNotEmpty(e.getChildPermissions())) {
                List<ManagerPermissionDTO> secondPermissions = e.getChildPermissions();
                secondPermissions.stream().forEach(secondPermission -> {
                    if (CollectionUtil.isNotEmpty(secondPermission.getChildPermissions())) {
                        List<ManagerPermissionDTO> thirdPermissions = secondPermission.getChildPermissions();
                        tempQueue.addAll(thirdPermissions);
                        set.addAll(thirdPermissions);
                    }
                });
            }
        });
        while (!tempQueue.isEmpty()) {
            final ManagerPermissionDTO currPerm = tempQueue.poll();
            if (CollectionUtil.isEmpty(currPerm.getChildPermissions())) {
                set.add(currPerm);
            } else {
                currPerm.getChildPermissions().forEach(tempQueue::offer);
            }
        }
        return set.size();
    }

    private Integer countAllCheckedNum(List<ManagerPermissionDTO> permTree) {
        int counter = 0;
        Queue<ManagerPermissionDTO> tempQueue = new LinkedList<>(permTree);
        while (!tempQueue.isEmpty()) {
            final ManagerPermissionDTO currPerm = tempQueue.poll();
            counter++;
            if (CollectionUtil.isNotEmpty(currPerm.getChildPermissions())) {
                currPerm.getChildPermissions().forEach(tempQueue::offer);
            }
        }
        return counter;
    }

    private Integer shareCompanyNum(String packCode) {
        List<DataRelation> dataRelations = dataRelationOperateService.listDataRelationByRelatedCode(DataRelationEnum.CUSTOMER_PRODUCT_FUNC, packCode);
        if (dataRelations.isEmpty()) {
            return 0;
        }
        final List<String> companyCodeList = dataRelations.stream().filter(r -> StringUtils.isNotBlank(r.getRelatedCode()))
                .map(DataRelation::getBussCode)
                .collect(Collectors.toList());
        if (companyCodeList.isEmpty()) {
            return 0;
        }
        final CompanyExample companyExample = new CompanyExample();
        companyExample.createCriteria().andCustomerCodeIn(companyCodeList)
                .andCompanyStatusEqualTo(CompanyStatusEnum.VALID.getStatus());
        return (int) companyExMapper.countByExample(companyExample);
    }

    private String matchVehicleType(List<Integer> useCarTypeList) {
        if (useCarTypeList == null || useCarTypeList.isEmpty()) {
            return StringUtils.EMPTY;
        }
        return useCarTypeList.stream()
                .map(BussinessTypeDictionary::getBussinessByCode)
                .filter(Objects::nonNull)
                .map(BussinessTypeDictionary::getText)
                .collect(Collectors.joining(" "));
    }

    //获取产品企业配置
    public EnterpriseConfigDTO getProductPackCompanyConfig(String packCode) {
        EnterpriseConfigDTO enterpriseConfigDTO;
        ProductPack productPack = new ProductPack();
        if (StringUtils.isBlank(packCode)) {
            return null;
        }
        productPack.setPackCode(packCode);
        productPack = productPackRepository.findOne(buildExample(productPack)).orElse(null);
        if (productPack == null) {
            return null;
        }

        enterpriseConfigDTO =  BeanUtil
                .copyProperties(productPack.getEnterpriseConfigDTO(), EnterpriseConfigDTO.class);

        // 解析企业配置
        List<BusinessCodeDTO> useCarTypeList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(productPack.getUseCarType())) {
            useCarTypeList = productPack.getUseCarType().stream().map(e -> {
                BusinessCodeDTO businessCodeDTO = new BusinessCodeDTO();
                businessCodeDTO.setBusinessCode(e);
                return businessCodeDTO;
            }).collect(Collectors.toList());
        }
        if (enterpriseConfigDTO != null) {
            enterpriseConfigDTO.setUseCarType(useCarTypeList);
            enterpriseConfigDTO = BusinessConfigApi.getProductPackEnterpriseConfig(enterpriseConfigDTO);
        }
        return enterpriseConfigDTO;
    }

    public PageDTO dropdown(ProductPackDropdownReqDTO reqDTO) {
        Sort sort = Sort.by("updateTime").descending();
        //定义example条件对象
        Pageable pageable = PageRequest.of(reqDTO.getPage()-1, reqDTO.getPageSize(), sort);
        Query query = new Query();
        //动态拼接查询条件
        if (reqDTO.getQuerySystem() != null) {
            query.addCriteria(Criteria.where("visibilityScopeList").is(reqDTO.getQuerySystem()));
        }
        if (reqDTO.getPackType() != null) {
            query.addCriteria(Criteria.where("packType").is(reqDTO.getPackType()));
        }
        if(reqDTO.getValid()!=null){
            query.addCriteria(Criteria.where("valid").is(reqDTO.getValid()));
        }
        if (reqDTO.getPackName() != null && !reqDTO.getPackName().isEmpty()) {
            // "i"表示不区分大小写
            query.addCriteria(Criteria.where("packName").regex(reqDTO.getPackName(), "i"));
        }
        query.fields().exclude("pcPerm")
                .exclude("appPerm");
        long total = mongoTemplate.count(query, ProductPack.class);
        List<ProductPack> productPackList = mongoTemplate.find(query.with(pageable), ProductPack.class);
        Page<ProductPack> pageInfo = new PageImpl<>(productPackList, pageable, total);
        List<ProductPackDropdownRespDTO> resultList =  BeanUtil
                .copyToList(pageInfo.getContent(), ProductPackDropdownRespDTO.class);
        return new PageDTO(reqDTO.getPage(), pageInfo.getSize(), pageInfo.getTotalElements(), resultList);
    }

    public MultiProductPackRespDTO getDetailByCodes(ProductPackDetailReqDTO reqDTO) {
        List<ProductPack> productPackList = productPackRepository.getPackCodes(reqDTO.getPackCodeList());
        //定义各个返回结果
        HashSet<String> pcPermCodeSet = new HashSet<>();
        HashSet<String> appPermCodeSet = new HashSet<>();
        List<String> packNameList = new ArrayList<>(productPackList.size());
        List<String> packCodeList = new ArrayList<>(productPackList.size());

        List<ProductPackDropdownRespDTO> allProduct = new ArrayList<>();
        for (ProductPack productPack : productPackList) {
            List<String> pcPermList = productPack.getPcPerm();
            List<String> appPermList = productPack.getAppPerm();
            if (pcPermList == null) {
                pcPermList = Collections.emptyList();
            }
            if (appPermList == null) {
                appPermList = Collections.emptyList();
            }
            if (productPack.getValid() != null && productPack.getValid() == 1) {
                pcPermCodeSet.addAll(pcPermList);
                appPermCodeSet.addAll(appPermList);
                packNameList.add(productPack.getPackName());
                packCodeList.add(productPack.getPackCode());
            }
            ProductPackDropdownRespDTO productPackDropdownRespDTO = new ProductPackDropdownRespDTO();
            productPackDropdownRespDTO.setPackName(productPack.getPackName());
            productPackDropdownRespDTO.setPackCode(productPack.getPackCode());
            productPackDropdownRespDTO.setPackExplain(productPack.getPackExplain());
            productPackDropdownRespDTO.setValid(productPack.getValid());
            allProduct.add(productPackDropdownRespDTO);
        }

        //封装产品包名称和编码
        MultiProductPackRespDTO resultDTO = new MultiProductPackRespDTO();
        resultDTO.setPackCodeList(packCodeList);
        resultDTO.setPackNameList(packNameList);

        //查询并封装pc和app的权限树
        PermTreeOption permTreeOption = new PermTreeOption();
        permTreeOption.setValid(Boolean.TRUE);
        ArrayList<String> pcPermCodeList = new ArrayList<>(pcPermCodeSet);
        permTreeOption.setQuerySysPermCodeList(pcPermCodeList);
        permTreeOption.setCheckedSysCodeList(pcPermCodeList);
        List<ManagerPermissionDTO> pcPermTree = managerPermissionService.getPermTree(permTreeOption);
        ArrayList<String> appPermCodeList = new ArrayList<>(appPermCodeSet);
        permTreeOption.setQuerySysPermCodeList(appPermCodeList);
        permTreeOption.setCheckedSysCodeList(appPermCodeList);
        permTreeOption.setMenuOpenModeEnum(MenuOpenModeEnum.APP);
        permTreeOption.setStartAppVersion(PermissionConst.APP_HOMEPAGE_MODIFY_VERSION_NUMBER);
        permTreeOption.setCheckedSysCodeList(appPermCodeList);
        List<ManagerPermissionDTO> appPermTree = managerPermissionService.getPermTree(permTreeOption);
        resultDTO.setPcPermTree(pcPermTree);
        resultDTO.setAppPermTree(appPermTree);

        //查询并封装权限树上叶子节点的个数
        resultDTO.setPcPermNum(countNumOfLeafPerm(pcPermTree));
        resultDTO.setAppPermNum(countAllCheckedNum(appPermTree));
        resultDTO.setProductPacks(allProduct);
        return resultDTO;
    }

    /**
     * 通过产品code集合获取客户编码集合
     * @param packCodes
     * @return
     */
    public RestResponse getCompanyCodeByPackCodes(String packCodes) {
        if(StringUtils.isEmpty(packCodes)){
            return RestResponse.success(new ArrayList<>());
        }
        List<String> packCodeList = Arrays.asList(packCodes.split(","));
        List<Integer> companyIds = new ArrayList<>();
        List<String> companyCodes = dataRelationMapper.selectCompanyCodeByRelaCodeType(packCodeList,DataRelationEnum.CUSTOMER_PRODUCT_FUNC.getCode());
        if(CollectionUtils.isNotEmpty(companyCodes)) {
            companyIds = companyExMapper.selectIdByCodes(companyCodes);
        }
        return RestResponse.success(companyIds);
    }


    public RestResponse getPackageByCompanyIds(String companyIds) {
        if (StringUtils.isEmpty(companyIds)) {
            return RestResponse.success(new ArrayList<>());
        }
        List<Integer> companyIdList = Arrays.asList(companyIds.split(",")).stream().map(Integer::valueOf).collect(Collectors.toList());
        List<Company> companyList = companyExMapper.getCompanyListByIds(companyIdList);
        if (CollectionUtils.isEmpty(companyList)) {
            return RestResponse.success(new ArrayList<>());
        }
        List<String> companyCodes = companyList.stream().map(Company::getCustomerCode).collect(Collectors.toList());
        Map<String, Integer> companyCodeIdMap = companyList.stream().collect(Collectors.toMap(Company::getCustomerCode, Company::getCompanyId, (c1, c2) -> c2));
        DataRelationExample dataRelationExample = new DataRelationExample();
        dataRelationExample.createCriteria().andRelationTypeEqualTo((byte) DataRelationEnum.CUSTOMER_PRODUCT_FUNC.getCode()).andBussCodeIn(companyCodes);
        List<DataRelation> dataRelationList = dataRelationMapper.selectByExample(dataRelationExample);
        if (CollectionUtils.isEmpty(dataRelationList)) {
            return RestResponse.success(new ArrayList<>());
        }
        //汇总所有的packCode
        List<String> packCodeList = dataRelationList.stream().map(DataRelation::getRelatedCode).collect(Collectors.toList());
        //统一查询产品包
        List<ProductPack> productPacks = getProductPacks(packCodeList);
        Map<String, List<DataRelation>> companyCodeDataRelationMap = dataRelationList.stream().collect(Collectors.groupingBy(DataRelation::getBussCode));
        List<ProductPackRespDTO> packRespList = Lists.newArrayList();
        companyCodeDataRelationMap.entrySet().stream().forEach(entry -> {
            Integer companyId = companyCodeIdMap.get(entry.getKey());
            if (Objects.nonNull(companyId)) {
                List<String> productPackCodeList = entry.getValue().stream().map(DataRelation::getRelatedCode).collect(Collectors.toList());
                List<ProductPack> productPackList = productPacks.stream().filter(productPack -> productPackCodeList.contains(productPack.getPackCode())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(productPackCodeList)) {
                    ProductPackRespDTO packRespDTO = new ProductPackRespDTO();
                    packRespDTO.setCompanyId(companyId);
                    String packNames = productPackList.stream().map(ProductPack::getPackName).collect(Collectors.joining(","));
                    packRespDTO.setProductPackNames(packNames);
                    packRespList.add(packRespDTO);
                }

            }
        });
        return RestResponse.success(packRespList);
    }
}
