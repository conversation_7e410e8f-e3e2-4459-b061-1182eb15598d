package com.izu.user.service.provider.driver;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.druid.support.ibatis.SqlMapClientImplWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.util.StringUtil;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.CarInfoDTO;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.order.OrderRestLocator;
import com.izu.user.config.consts.UserConstantCode;
import com.izu.user.config.errcode.UserErrorCode;
import com.izu.user.dto.DriverImportDTO;
import com.izu.user.dto.DriverManageNewDTO;
import com.izu.user.dto.DriverNewDTO;
import com.izu.user.dto.ImportErrorDTO;
import com.izu.user.dto.company.CompanyDropDownReqDTO;
import com.izu.user.dto.company.CompanyNameRespDTO;
import com.izu.user.dto.driver.ClientDriverDropdownRespDTO;
import com.izu.user.dto.driver.ProviderDriverPageReqDTO;
import com.izu.user.dto.provider.driver.DriverDropdownReqDTO;
import com.izu.user.dto.provider.driver.DriverDropdownRespDTO;
import com.izu.user.dto.provider.permission.PermTreeOption;
import com.izu.user.dto.relation.DataRelationDTO;
import com.izu.user.dto.relation.RelationCodeDTO;
import com.izu.user.entity.*;
import com.izu.user.enums.*;
import com.izu.user.enums.perm.ProviderDataPermTypeEnum;
import com.izu.user.enums.relation.DataRelationEnum;
import com.izu.user.enums.role.SpecialRoleEnum;
import com.izu.user.enums.role.SystemTypeEnum;
import com.izu.user.enums.user.ProviderMgtUserStateEnum;
import com.izu.user.enums.user.ProviderUserStatusEnum;
import com.izu.user.rpc.AssetApi;
import com.izu.user.service.common.PasswordManagerService;
import com.izu.user.service.common.SequenceGenerator;
import com.izu.user.service.provider.permission.ManagerPermissionService;
import com.izu.user.service.relation.DataRelationOperateService;
import com.izu.user.util.SingletonFactory;
import com.izu.user.utils.*;
import lombok.extern.slf4j.Slf4j;
import mapper.DatacenterStructBussMapper;
import mapper.ex.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProviderDriverService {
    @Resource
    private ProviderDriverExMapper providerDriverExMapper;
    @Resource
    private PushSmsExMapper pushSmsExMapper;
    @Resource
    private StaffRoleRelationExMapper staffRoleRelationExMapper;
    @Resource
    private ProviderStaffExMapper providerStaffExMapper;
    @Resource
    private DatacenterStructBussMapper datacenterStructBussMapper;
    @Resource
    private SequenceGenerator sequenceGenerator;
    @Autowired
    private ManagerPermissionService managerPermissionService;
    @Autowired
    private DataRelationOperateService dataRelationOperateService;
    @Resource
    private MotorcadeDriverExMapper motorcadeDriverExMapper;

    @Autowired
    private CompanyExMapper companyExMapper;


    @Autowired
    private PasswordManagerService passwordManagerService;

    private static final MrCarAssetRestLocator assetRestLocator = SingletonFactory.getSingleton(SingletonFactory.assetRestLocator, MrCarAssetRestLocator::new);
    private static final OrderRestLocator orderRestLocator = SingletonFactory.getSingleton(SingletonFactory.orderRestLocator, OrderRestLocator::new);
    private static final String GET_VEHICLE_URL = "/car/getById";
    private static final String CHECK_DRIVER_ORDER = "order/checkUnDoOrderByParam";

    /**
     * 司机列表方法
     */
    public PageDTO listBusinessDrivers(ProviderDriverPageReqDTO dto) {
        long total;
        Page<Object> p;
        List<DriverManageNewDTO> driverList;
        try {
            /*
              数据权限
              所有：所有数据
              负责合同、负责/指定客户、本人：登录用户所属部门对应的运营城市内的车队的所有商务车司机；并且能看见车队为空的司机
              所属部门/指定部门：司机所属车队的所属城市，匹配登录用户的数据权限内的部门转运营城市；并且能看见车队为空的司机
             */
            if (dto.getDataPermIsNotNull() && ProviderDataPermTypeEnum.SELF_DEPT.getType().equals(dto.getDataPermType()) || ProviderDataPermTypeEnum.ASSIGN_DEPT.getType().equals(dto.getDataPermType())) {
                dto.setDataPermType(ProviderDataPermTypeEnum.SELF_DEPT.getType());
                Set<String> dataCodeSet = dto.getDataCodeSet();
                List<Integer> dataCodeSetInteger = dataCodeSet.stream().map(Integer::valueOf).collect(Collectors.toList());
                DatacenterStructBussExample datacenterStructBussExample = new DatacenterStructBussExample();
                datacenterStructBussExample.createCriteria().andStructIdIn(dataCodeSetInteger);
                List<DatacenterStructBuss> datacenterStructBusses = datacenterStructBussMapper.selectByExample(datacenterStructBussExample);
                if (CollectionUtil.isNotEmpty(datacenterStructBusses)) {
                    dto.setDataCodeSet(datacenterStructBusses.stream().map(DatacenterStructBuss::getBelongCityCode).collect(Collectors.toSet()));
                }
            } else if (ProviderDataPermTypeEnum.RESPONSIBLE_CONTRACT.getType().equals(dto.getDataPermType()) || ProviderDataPermTypeEnum.RESPONSIBLE_CUSTOMER.getType().equals(dto.getDataPermType())
                    || ProviderDataPermTypeEnum.ASSIGN_CUSTOMER.getType().equals(dto.getDataPermType()) || ProviderDataPermTypeEnum.ONE_SELF.getType().equals(dto.getDataPermType())) {
                ProviderStaff providerStaff = providerStaffExMapper.selectByPrimaryKey(dto.getStaffId());
                dto.setDataPermType(ProviderDataPermTypeEnum.SELF_DEPT.getType());
                dto.setDataPermIsNotNull(Boolean.TRUE);
                if (StringUtils.isNotBlank(providerStaff.getStructIds())) {
                    String[] structIds = providerStaff.getStructIds().split(",");
                    List<Integer> dataCodeSetInteger = Arrays.stream(structIds).map(Integer::valueOf).collect(Collectors.toList());
                    DatacenterStructBussExample datacenterStructBussExample = new DatacenterStructBussExample();
                        datacenterStructBussExample.createCriteria().andStructIdIn(dataCodeSetInteger);
                        List<DatacenterStructBuss> datacenterStructBusses = datacenterStructBussMapper.selectByExample(datacenterStructBussExample);
                        if (CollectionUtil.isNotEmpty(datacenterStructBusses)) {
                            dto.setDataCodeSet(datacenterStructBusses.stream().map(DatacenterStructBuss::getBelongCityCode).collect(Collectors.toSet()));
                    }
                }
            }
            p = PageHelper.startPage(dto.getPageNum(), dto.getPageSize(), true);
            driverList = providerDriverExMapper.listBusinessDrivers(dto);
            if (CollectionUtil.isNotEmpty(driverList)) {
                //批量获取车辆信息
                String vehicleIds = driverList.stream().map(DriverManageNewDTO::getVehicleId)
                        .filter(Objects::nonNull).distinct()
                        .map(String::valueOf)
                        .collect(Collectors.joining(","));
                if (!vehicleIds.isEmpty()) {
                    List<CarInfoDTO> carInfoByIds = AssetApi.getCarInfoByIds(vehicleIds);
                    Map<Long, CarInfoDTO> vehicleMap = carInfoByIds.stream().collect(Collectors.toMap(CarInfoDTO::getVehicleId, Function.identity()));
                    driverList.forEach(driver -> {
                        if (driver.getVehicleId() != null) {
                            CarInfoDTO carInfoDTO = vehicleMap.get(driver.getVehicleId().longValue());
                            if(carInfoDTO!=null){
                                driver.setVehicleLicense(carInfoDTO.getVehicleLicense());
                            }
                        }
                    });
                }
            }
            total = p.getTotal();
            return new PageDTO(dto.getPageNum(), dto.getPageSize(), total, driverList);
        } finally {
            PageHelper.clearPage();
        }
    }

    /**
     * 插入或者更新司机信息
     */
    @Transactional(rollbackFor = Exception.class)
    public RestResponse save(DriverNewDTO dto) {
        boolean insertFlag = dto.getDriverId() == null;
        //身份证号不为空则设置驾驶证号码
        if (StringUtils.isNotBlank(dto.getCertNo())) {
            dto.setLicenseNo(dto.getCertNo());
        }
        ProviderStaff providerStaff = null;
        if (!insertFlag) {
            Driver driver = providerDriverExMapper.selectByPrimaryKey(dto.getDriverId());
            List<ProviderStaff> staffList = providerStaffExMapper.getActiveByPhone(driver.getDriverMobile());
            if (CollectionUtil.isNotEmpty(staffList)) {
                providerStaff = staffList.get(0);
            } else {
                return RestResponse.fail(UserErrorCode.DRIVER_NOT_EXIST);
            }
        } else {
            final List<ProviderStaff> staffList = providerStaffExMapper.getActiveByPhone(dto.getDriverMobile());
            if (CollectionUtil.isNotEmpty(staffList)) {
                if (staffList.size() > 1) {
                    return RestResponse.fail(UserErrorCode.MOBILE_IS_EXIST_IN_USERS);
                }
                providerStaff = staffList.get(0);
            }
        }
        if (StringUtils.isNotBlank(dto.getDriverEmail())) {
            List<ProviderStaff> activeByEmail = providerStaffExMapper.getActiveByEmail(dto.getDriverEmail());
            if (insertFlag && CollectionUtil.isNotEmpty(activeByEmail)) {
                return RestResponse.fail(UserErrorCode.EMAIL_IS_EXIST_IN_USERS);
            }
            ProviderStaff finalProviderStaff = providerStaff;
            if (!insertFlag && CollectionUtil.isNotEmpty(activeByEmail) && activeByEmail.stream().anyMatch(e -> ObjectUtil.equals(e.getEmail(), dto.getDriverEmail()) && ObjectUtil.equals(e.getProviderStaffCode(), finalProviderStaff.getProviderStaffCode()))) {
                return RestResponse.fail(UserErrorCode.EMAIL_IS_EXIST_IN_USERS);
            }
        }
        if (insertFlag) {
            dto.setCreterId(dto.getStaffId());
            dto.setCreterName(dto.getStaffName());
            dto.setUpdateId(dto.getStaffId());
            dto.setUpdateName(dto.getStaffName());
            //司机手机号校验；如果该司机的手机号在司机表中存在，则不允许使用该手机号
            if (providerDriverExMapper.checkDriverByMobile(dto.getDriverMobile(), null) > 0) {
                return RestResponse.fail(UserErrorCode.DRIVER_MOBILE_EXIST);
            }
            List<StaffRoleRelation> staffRoleRelations = null;
            if (providerStaff != null) {
                final StaffRoleRelationExample roleRelationExample = new StaffRoleRelationExample();
                roleRelationExample.createCriteria().andStaffCodeEqualTo(providerStaff.getProviderStaffCode()).andRoleCodeEqualTo(SpecialRoleEnum.PROVIDER_BUSINESS_DRIVER.getRoleCode());
                staffRoleRelations = staffRoleRelationExMapper.selectByExample(roleRelationExample);
            } else {
                providerStaff = BeanUtil.copyObject(dto, ProviderStaff.class);
                providerStaff.setMobile(dto.getDriverMobile());
                providerStaff.setIdCardNum(dto.getCertNo());
                providerStaff.setStaffName(dto.getDriverName());
                createStaff(providerStaff);
            }
            //插入自有司机角色关系
            if (CollectionUtils.isEmpty(staffRoleRelations)) {
                StaffRoleRelation insert = new StaffRoleRelation();
                insert.setRoleCode(SpecialRoleEnum.PROVIDER_BUSINESS_DRIVER.getRoleCode());
                insert.setStaffCode(providerStaff.getProviderStaffCode());
                insert.setSystemType(SystemTypeEnum.PROVIDER.getCode());
                staffRoleRelationExMapper.insertSelective(insert);
            }
            // 新增逻辑, 为当前司机添加默认的工作台设置
            ManagerPermission driverWorkbenchInfo = this.queryPermissionOfDriverWorkbench();
            this.initDefaultWorkbenchForDriver(providerStaff.getProviderStaffCode(), driverWorkbenchInfo);
        }

        String companyName = dto.getCompanyName();
        //组装并插入司机信息
        Driver driver = BeanUtil.copyObject(dto, Driver.class);
        driver.setPinyinName(PinYinUtil.getPingYin(dto.getDriverName()));
        driver.setDriverSourceType(DriverEnums.DriverSourceTypeEnum.swcsj.getCode());
        String context = UserConstantCode.CUSTOMER_NEW_DRIVER.replace("XXX", companyName);
        if (insertFlag) {
            providerDriverExMapper.insertSelective(driver);
            if (providerStaff.getProviderStaffId() == null) {
                providerStaffExMapper.insertSelective(providerStaff);
            }
            //发送短信
            String sendSms = SMSUtil.sendSms(driver.getDriverMobile(), context);
            //插入短信发送记录
            PushSms pushSms = new PushSms();
            pushSms.setPushType((byte) UserConstantCode.PUSH_TYPE_UPDATEMOBILE);
            pushSms.setReceiveMobile(driver.getDriverMobile());
            pushSms.setSendContent(context);
            pushSms.setSendTime(DateUtil.getCurrentDateTimeString());
            pushSms.setSendStatus((byte) UserConstantCode.NORMAL_STATUS);
            pushSms.setResultContent(sendSms);
            pushSmsExMapper.insertSelective(pushSms);
        } else {
            String driverMobile = dto.getDriverMobile().trim();
            Integer driverId = dto.getDriverId();
            //判断该手机号是否是白名单号码,如果是的话判断该手机号在系统中是否存在,如果存在的话提示手机号已存在,不存在的话可以添加到系统中,不影响别的手机号
            if ("13811303051".equals(driverMobile)) {
                return RestResponse.fail(UserErrorCode.UPDATE_DRIVER_MOBILE_EXIST);
            }
            //查询并判断该司机是否存在
            Driver currentDriver = providerDriverExMapper.selectByPrimaryKey(driverId);
            if (null == currentDriver) {
                return RestResponse.fail(UserErrorCode.DRIVER_NOT_EXIST);
            }
            //司机手机号校验；如果该司机的手机号在司机表中存在，并且不是当前司机ID的，则不允许修改为该手机号
            if (providerDriverExMapper.checkDriverByMobile(driverMobile, driverId) > 0) {
                return RestResponse.fail(UserErrorCode.DRIVER_MOBILE_EXIST);
            }

            //判断司机是否在接单中
            Map<String, Object> paraMap = new HashMap<>();
            paraMap.put("driverId", driverId);
            RestResponse orderRep = RestClient.requestInside(BaseHttpClient.HttpMethod.POST, orderRestLocator.getRestUrl(CHECK_DRIVER_ORDER), paraMap, null);
            if (null != orderRep.getData() && (boolean) orderRep.getData()) {
                return RestResponse.fail(UserErrorCode.DRIVER_HAVE_ORDER);
            }
            driver.setUpdateId(dto.getStaffId());
            driver.setUpdateName(dto.getStaffName());
            providerDriverExMapper.updateByPrimaryKeySelective(driver);

            //更新用户信息 只允许修改姓名手机号和邮箱
            ProviderStaff updProviderStaff = new ProviderStaff();
            updProviderStaff.setProviderStaffId(providerStaff.getProviderStaffId());
            updProviderStaff.setStaffName(dto.getDriverName());
            updProviderStaff.setMobile(dto.getDriverMobile());
            updProviderStaff.setEmail(dto.getDriverEmail());
            updProviderStaff.setUpdateId(dto.getStaffId());
            updProviderStaff.setUpdateName(dto.getStaffName());
            providerStaffExMapper.updateByPrimaryKeySelective(updProviderStaff);

            //司机手机号发生变化，则发送短信
            if (!driverMobile.equals(currentDriver.getDriverMobile())) {

                String sendSms = SMSUtil.sendSms(driverMobile, context);
                //插入短信发送记录
                PushSms pushSms = new PushSms();
                pushSms.setPushType((byte) UserConstantCode.PUSH_TYPE_UPDATEMOBILE);
                pushSms.setReceiveMobile(driverMobile);
                pushSms.setSendContent(context);
                pushSms.setSendTime(DateUtil.getCurrentDateTimeString());
                pushSms.setSendStatus((byte) UserConstantCode.NORMAL_STATUS);
                pushSms.setResultContent(sendSms);
                pushSmsExMapper.insertSelective(pushSms);

                context = UserConstantCode.UPD_DRIVER.replace("XXX", companyName).replace("###", driverMobile);
                sendSms = SMSUtil.sendSms(currentDriver.getDriverMobile(), context);
                //插入短信发送记录
                pushSms = new PushSms();
                pushSms.setPushType((byte) UserConstantCode.PUSH_TYPE_UPDATEMOBILE);
                pushSms.setReceiveMobile(currentDriver.getDriverMobile());
                pushSms.setSendContent(context);
                pushSms.setSendTime(DateUtil.getCurrentDateTimeString());
                pushSms.setSendStatus((byte) UserConstantCode.NORMAL_STATUS);
                pushSms.setResultContent(sendSms);
                pushSmsExMapper.insertSelective(pushSms);
            }

        }
        return RestResponse.success(true);
    }

    // 初始化司机的工作台设置
    private boolean initDefaultWorkbenchForDriver(String customerCode, ManagerPermission permission) {
        if (Objects.isNull(permission) || StringUtils.isBlank(customerCode)) {
            return false;
        }
        DataRelationDTO relation = new DataRelationDTO();
        relation.setDataRelationCode(DataRelationEnum.APP_USER_DESIGNATED_WORKBENCH.getCode());
        relation.setBussCode(customerCode);
        RelationCodeDTO relationCode = new RelationCodeDTO();
        relationCode.setRelatedCode(permission.getPermSysCode());
        relation.setRelList(Collections.singletonList(relationCode));
        return dataRelationOperateService.createDataRelationIfAbsent(relation);
    }

    // 查询司机工作台权限记录
    private ManagerPermission queryPermissionOfDriverWorkbench() {
        PermTreeOption cond = new PermTreeOption();
        cond.setStartAppVersion(110);
        cond.setQueryPermissioncodeList(Collections.singletonList("homePageDriver"));
        List<ManagerPermission> list = managerPermissionService.getPermList(cond);
        return list.stream().findFirst().orElse(null);
    }

    private void createStaff(ProviderStaff providerStaff) {
        providerStaff.setPinyinName(PinYinUtil.getFullSpell(providerStaff.getStaffName()));
        providerStaff.setProviderStaffCode(sequenceGenerator.generate(new Date(), SeqTypeEnum.PROVIDER_STAFF));
        String mgtStaffId = providerStaff.getProviderStaffCode().substring(providerStaff.getProviderStaffCode().length() - 9);
        providerStaff.setMgtStaffId(-Integer.parseInt(mgtStaffId));
        providerStaff.setHeadIcon("");
        providerStaff.setPosition("商务车司机");
        if (StringUtil.isNotEmpty(providerStaff.getEmail())) {
            String[] split = providerStaff.getEmail().split("@");
            providerStaff.setAccount(split[0]);
        }
        Triple<String, String, String> triple = passwordManagerService.generatePassword(providerStaff.getMobile());
        String salt = triple.getRight();
        String encryptedPassword = triple.getLeft();
        providerStaff.setLoginPassword(encryptedPassword);
        providerStaff.setSalt(salt);
        providerStaff.setProviderCode(SpecialProviderEnum.SQ.getCode());
        providerStaff.setProviderName(SpecialProviderEnum.SQ.getName());
        providerStaff.setUserState(ProviderUserStatusEnum.ENABLE.getUserState());
        providerStaff.setDataPermType(ProviderDataPermTypeEnum.ONE_SELF.getType());
        providerStaff.setMgtUserState(ProviderMgtUserStateEnum.ENABLE.getState());
        Date date = new Date();
        providerStaff.setHiredDate(date);
    }


    public RestResponse batchInsert(List<DriverImportDTO> driverImportList, Integer createId, String createName, Integer companyId) {
        //定义错误对象列表
        List<ImportErrorDTO> importErrorDTOList = new ArrayList<>();
        //定义导入Excel的首行行号
        int rowNum = 2;

        if (null != driverImportList && driverImportList.size() > 0) {
            //定义批量入库的司机实体对象
            List<Driver> batchImportList = new ArrayList<>();
            //检查导入文件中的手机号是否重复
            Set<String> mobiles = new HashSet<>(driverImportList.size());
            driverImportList.forEach(x -> {
                if (!mobiles.add(x.getDriverMobile())) {
                    ImportErrorDTO errorDTO = new ImportErrorDTO();
                    errorDTO.setRowNum(2);
                    errorDTO.setReason("上传文件中手机号：" + x.getDriverMobile() + "重复，请确认后重试");
                    importErrorDTOList.add(errorDTO);
                }
            });
            //若有重复号码，则告知用户，本次不进行保存
            if (!CollectionUtils.isEmpty(importErrorDTOList)) {
                return RestResponse.success(importErrorDTOList);
            }
            //检查文件中的手机号在司机账户系统中是否存在，存在的手机号返回前端，不进行保存
            List<Driver> oldDrivers = providerDriverExMapper.selectDriverByMobiles(mobiles);
            if (null != oldDrivers && !oldDrivers.isEmpty()) {
                for (Driver driver : oldDrivers
                ) {
                    ImportErrorDTO errorDTO = new ImportErrorDTO();
                    errorDTO.setRowNum(rowNum);
                    errorDTO.setReason("上传文件中手机号：" + driver.getDriverMobile() + "在司机账户中已经存在");
                    importErrorDTOList.add(errorDTO);
                    rowNum++;
                }

            }
            if (!CollectionUtils.isEmpty(importErrorDTOList)) {
                return RestResponse.success(importErrorDTOList);
            }

            SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd");
            List<ProviderStaff> existCustomer = providerStaffExMapper.getActiveByMobiles(mobiles);
            Map<String, ProviderStaff> existCustomerMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(existCustomer)) {
                existCustomerMap = existCustomer.stream().collect(Collectors.toMap(ProviderStaff::getMobile, Function.identity(), (k1, k2) -> k1));
            }

            for (DriverImportDTO importDTO : driverImportList) {
                //设置入库实体类
                Driver driver = new Driver();
                driver.setDriverName(importDTO.getDriverName());
                driver.setPinyinName(PinYinUtil.getPingYin(importDTO.getDriverName()));
                driver.setDriverMobile(importDTO.getDriverMobile());
                driver.setLicenseType(importDTO.getLicenseType());
                driver.setCompanyId(companyId);
                driver.setCreterId(createId);
                driver.setCreterName(createName);
                driver.setJoinTime(DateUtil.getCurrentDateTimeString());
                driver.setUpdateId(createId);
                driver.setUpdateName(createName);
                driver.setGender((byte) (importDTO.getGenderStr().equals("男") ? 1 : 2));
                try {
                    driver.setBrithDate(sd.format(sd.parse(importDTO.getBrithDate())));
                    driver.setFirstPickupTime(sd.format(sd.parse(importDTO.getFirstPickupTime())));
                    driver.setArriveTime(sd.format(sd.parse(importDTO.getArriveTime())));

                } catch (ParseException e) {
                    e.printStackTrace();
                }
                driver.setCertNo(importDTO.getCertNo());
                driver.setIssuingOrgan(importDTO.getIssuingOrgan());
                driver.setOfficeStatus(DriverOfficeStatusEnum.getKeyByValue(importDTO.getOfficeStatusStr()));

                driver.setDriverSourceType(DriverEnums.DriverSourceTypeEnum.swcsj.getCode());
                batchImportList.add(driver);

                ProviderStaff oldCus = existCustomerMap.get(driver.getDriverMobile());
                String providerStaffCode;
                if (oldCus == null) {
                    //新增用户
                    ProviderStaff providerStaff = BeanUtil.copyObject(driver, ProviderStaff.class);
                    providerStaff.setMobile(driver.getDriverMobile());
                    providerStaff.setIdCardNum(driver.getCertNo());
                    providerStaff.setStaffName(driver.getDriverName());
                    createStaff(providerStaff);
                    providerStaffExMapper.insertSelective(providerStaff);
                    providerStaffCode = providerStaff.getProviderStaffCode();
                } else {
                    providerStaffCode = oldCus.getProviderStaffCode();
                }
                //给用户增加自有司机角色
                setDriverRoleToUser(providerStaffCode, SpecialRoleEnum.PROVIDER_BUSINESS_DRIVER.getRoleCode());

            }

            //批量入库
            if (!CollectionUtils.isEmpty(batchImportList)) {
                providerDriverExMapper.batchInsertProvider(batchImportList);
            }
            return RestResponse.success(null);
        } else {
            ImportErrorDTO errorDTO = new ImportErrorDTO();
            errorDTO.setRowNum(rowNum);
            errorDTO.setReason("本次导入的数据为空！");
            importErrorDTOList.add(errorDTO);
            return RestResponse.success(importErrorDTOList);
        }
    }
    public RestResponse changeDriverStatus(DriverNewDTO dto) {
        Driver driver = providerDriverExMapper.selectByPrimaryKey(dto.getDriverId());
        if (driver == null) {
            return RestResponse.fail(UserErrorCode.DRIVER_VALUE_NULL, "司机ID");
        }
        //司机存在，则进行状态更改
        Driver upd = new Driver();
        upd.setDriverId(dto.getDriverId());
        upd.setDriverStatus(UserStateEnum.DELETED.getValue());
        //清空绑定车辆
        upd.setBindVehicleId(0);
        upd.setUpdateId(dto.getStaffId());
        upd.setUpdateName(dto.getStaffName());
        int res = providerDriverExMapper.updateByPrimaryKeySelective(upd);
        //清空车队
        motorcadeDriverExMapper.updateForDelDriver(driver.getDriverId(),new Date());
        List<ProviderStaff> staffList = providerStaffExMapper.getActiveByPhone(driver.getDriverMobile());
        if (CollectionUtil.isNotEmpty(staffList)) {
            ProviderStaff providerStaff = staffList.get(0);
            staffRoleRelationExMapper.clearRelationsByStaffCode(SpecialRoleEnum.PROVIDER_BUSINESS_DRIVER.getRoleCode(), providerStaff.getProviderStaffCode());
            // 判断有无其他角色，没有则停用用户
            StaffRoleRelationExample staffRoleRelationExample = new StaffRoleRelationExample();
            staffRoleRelationExample.createCriteria().andStaffCodeEqualTo(providerStaff.getProviderStaffCode());
            List<StaffRoleRelation> list = staffRoleRelationExMapper.selectByExample(staffRoleRelationExample);
            if (CollectionUtil.isEmpty(list)) {
                ProviderStaff providerStaffUpd = new ProviderStaff();
                providerStaffUpd.setProviderStaffId(providerStaff.getProviderStaffId());
                providerStaffUpd.setUserState(ProviderUserStatusEnum.DISABLE.getUserState());
                providerStaffUpd.setUpdateId(dto.getStaffId());
                providerStaffUpd.setUpdateName(dto.getStaffName());
                providerStaffExMapper.updateByPrimaryKeySelective(providerStaffUpd);
            }
        }
        return RestResponse.success(res);
    }


    public List<DriverDropdownRespDTO> driverDropdownRespDTOList(DriverDropdownReqDTO reqDTO) {
        PageInfo<DriverDropdownRespDTO> pageInfo = PageHelper.startPage(1, 500).doSelectPageInfo(() -> providerDriverExMapper.driverDropdownList(reqDTO));
        List<DriverDropdownRespDTO> driverList = pageInfo.getList();
        if (CollectionUtils.isEmpty(driverList)) {
            return driverList;
        }
        driverList.forEach(driver -> {
            Integer bindVehicleStatus = (Objects.nonNull(driver.getBindVehicleId()) && !Objects.equals(driver.getBindVehicleId(), 0)) ? 1 : 0;
            String bindVehicleStatusStr = (Objects.nonNull(driver.getBindVehicleId()) && !Objects.equals(driver.getBindVehicleId(), 0)) ? "已绑定" : "未绑定";
            driver.setBindVehicleStatusStr(bindVehicleStatusStr);
            driver.setBindVehicleStatus(bindVehicleStatus);
        });
        return driverList;
    }


    private void setDriverRoleToUser(String customerCode, String roleCode) {
        //检查用户的角色信息是否包含自有司机
        final StaffRoleRelationExample roleRelationExample = new StaffRoleRelationExample();
        roleRelationExample.createCriteria().andStaffCodeEqualTo(customerCode).andRoleCodeEqualTo(roleCode);
        final List<StaffRoleRelation> staffRoleRelations = staffRoleRelationExMapper.selectByExample(roleRelationExample);
        if (CollectionUtils.isEmpty(staffRoleRelations)) {
            //插入自有司机角色关系
            StaffRoleRelation insert = new StaffRoleRelation();
            insert.setRoleCode(roleCode);
            insert.setStaffCode(customerCode);
            insert.setSystemType(SystemTypeEnum.PROVIDER.getCode());
            staffRoleRelationExMapper.insertSelective(insert);
        }
    }

}
