package com.izu.user.service.driver;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.IdcardUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.CellExtra;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.util.DateUtils;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.consts.IsDelEnum;
import com.izu.asset.consts.car.VehicleStatusEnum;
import com.izu.asset.consts.insurance.InsuranceDataSourceEnum;
import com.izu.asset.consts.insurance.InsuranceInputMethodEnum;
import com.izu.asset.consts.insurance.InsuranceStatusEnum;
import com.izu.asset.dto.CarDriverDispatchDTO;
import com.izu.asset.dto.CarInfoDTO;
import com.izu.asset.dto.insurance.InsuranceInsureTypesDTO;
import com.izu.asset.dto.insurance.req.InsurancePolicyAddReqDTO;
import com.izu.asset.dto.insurance.req.InsurancePolicyImportReqDTO;
import com.izu.asset.dto.insurance.resp.BatchDTO;
import com.izu.asset.errcode.MrCarAssetErrorCode;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.exception.RestErrorException;
import com.izu.config.restApi.ConfigApi;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.mrcar.config.enums.BussinessTypeDictionary;
import com.izu.mrcar.iot.iotEnum.SelfOwnedEnum;
import com.izu.mrcar.iot.utils.OSSUtils;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.dto.dispatching.ScheduleDTO;
import com.izu.mrcar.order.dto.mrcar.OrderInfoDTO;
import com.izu.mrcar.order.restapi.ScheduleApi;
import com.izu.third.enums.OSSBucketEnum;
import com.izu.user.common.RestLocators;
import com.izu.user.config.consts.UserConstantCode;
import com.izu.user.config.errcode.UserErrorCode;
import com.izu.user.dto.*;
import com.izu.user.dto.customer.CustomerManageRespDTO;
import com.izu.user.dto.driver.*;
import com.izu.user.dto.provider.driver.QueryDriverProviderCondition;
import com.izu.user.dto.provider.roleinfo.RoleInfoGetStaffReqDTO;
import com.izu.user.entity.*;
import com.izu.user.enums.*;
import com.izu.user.enums.perm.ClientDataPermTypeEnum;
import com.izu.user.enums.perm.ProviderDataPermTypeEnum;
import com.izu.user.enums.role.SpecialRoleEnum;
import com.izu.user.enums.role.SystemTypeEnum;
import com.izu.user.rpc.AssetApi;
import com.izu.user.rpc.OrderApi;
import com.izu.user.service.maintain.MaintainGarageService;
import com.izu.user.service.provider.staff.ProviderInternalStaffManageService;
import com.izu.user.service.user.CustomerManagementService;
import com.izu.user.service.user.UserFaceAuthService;
import com.izu.user.util.DateTimeUtils;
import com.izu.user.util.ObjectTransferUtil;
import com.izu.user.utils.DateUtil;
import com.izu.user.utils.IdCardNumberValidator;
import com.izu.user.utils.PinYinUtil;
import com.izu.user.utils.SMSUtil;
import mapper.DriverMapper;
import mapper.ex.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.*;
import java.math.BigDecimal;
import java.net.URL;
import java.nio.file.Files;
import java.text.ParseException;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class DriverService {
    private static final Logger logger = LoggerFactory.getLogger(DriverService.class);

    private static final Integer DEFAULT_VEHICLE_ID = 0;
    @Autowired
    private DriverExMapper driverExMapper;

    @Autowired
    private CompanyExMapper companyExMapper;
    @Autowired
    private PushSmsExMapper pushSmsExMapper;
    @Autowired
    private MotorcadeExMapper motorcadeExMapper;
    @Autowired
    private MotorcadeDriverExMapper motorcadeDriverExMapper;
    @Autowired
    private StaffRoleRelationExMapper staffRoleRelationExMapper;
    @Autowired
    private UserFaceAuthService userFaceAuthService;
    @Autowired
    private DatacenterStructBussExMapper datacenterStructBussExMapper;
    @Autowired
    private DriverBindVehicleLogExMapper driverBindVehicleLogExMapper;

    private static final String GET_VEHICLE_URL = "/car/getById";
    //校验司机是否有订单服务地址
    private static final String CHECK_DRIVER_ORDER = "/order/checkUnDoOrderByParam";
    @Autowired
    private CustomerExMapper customerExMapper;

    @Autowired
    private CustomerManagementService customerManagementService;

    @Autowired
    private ProviderInternalStaffManageService providerInternalStaffManageService;

    @Autowired
    private CompanyDepartmentExMapper companyDepartmentExMapper;
    @Autowired
    private ProviderStaffExMapper providerStaffExMapper;
    @Autowired
    private MaintainGarageService maintainGarageService;
    @Autowired
    private CompanyDepartmentExMapper departmentExMapper;
    @Autowired
    private DriverInfoManagerService driverInfoManagerService;


    private static final String COMMA = "；";
    @Qualifier("driverMapper")
    @Autowired
    private DriverMapper driverMapper;
    public static final String LICENSE_TYPE = "A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P";
    public static final List<String> LICENSE_TYPE_LIST =
            Arrays.stream(LICENSE_TYPE.split(","))
                    .collect(Collectors.toList());

    /**
     * 重写司机列表方法（baixiaoxuan 2019.11.28）
     *
     * @param driverName
     * @param driverMobile
     * @param
     * @param workingStatus
     * @param licenseType
     * @param companyId
     * @param motorcadeId
     * @param pageNum
     * @param pageSize
     * @return
     */
    public PageDTO listDriverForPage(String driverName, String driverMobile, Integer workingStatus, Integer driverStatus,
                                     List<String> licenseType, Integer companyId, Integer motorcadeId,
                                     Integer pageNum, Integer pageSize, Integer driverType, Integer cityCode, Byte driverSourceType, Byte dataPermType, String dataScopeStr, Integer loginUserId, String companyIds) {
        long total = 0;
        //数据权限判断
        List<String> userMobiles = new ArrayList<>();
        if (ClientDataPermTypeEnum.ASSIGN_DEPT.getType().equals(dataPermType)) {
            //指定部门，需要先查询用户表的数据
            if (StringUtils.isNotBlank(dataScopeStr)) {
                List<Integer> structIds = Arrays.asList(dataScopeStr.split(",")).stream().map(e -> Integer.valueOf(e)).collect(Collectors.toList());
                CustomerExample queryCustomer = new CustomerExample();
                queryCustomer.createCriteria().andStructIdIn(structIds);
                List<Customer> customers = customerExMapper.selectByExample(queryCustomer);
                if (!CollectionUtils.isEmpty(customers)) {
                    userMobiles = customers.stream().map(Customer::getMobile).collect(Collectors.toList());
                }
            }
        }
        List<String> allMobiles = new ArrayList<>();
        //通过角色类型来区分
        if (Objects.equals(driverSourceType, DriverEnums.DriverSourceTypeEnum.sqsj.getCode())) {
            //查询首汽司机
            allMobiles = driverExMapper.selectDriverByRoleCode(SpecialRoleEnum.PROVIDER_DRIVER.getRoleCode(), null);
        } else if (Objects.equals(driverSourceType, DriverEnums.DriverSourceTypeEnum.zysj.getCode())) {
            //查询自有司机
            allMobiles = driverExMapper.selectDriverByRoleCode(SpecialRoleEnum.COMPANY_DRIVER.getRoleCode(), null);
        }
        Page p = PageHelper.startPage(pageNum, pageSize, true);
        List<DriverManageDTO> driverList = null;
        try {
            driverList = driverExMapper.listDriverForPage(driverMobile, driverName, workingStatus, 1, licenseType,
                    companyId, motorcadeId, driverType, cityCode, driverSourceType, dataPermType, dataScopeStr, userMobiles, loginUserId, companyIds, allMobiles);
            if (driverList != null && driverList.size() > 0) {
                String restUrl = new MrCarAssetRestLocator().getRestUrl(GET_VEHICLE_URL);
                for (DriverManageDTO driver : driverList) {
                    //多企业的账户查询，需要返回所属企业名称-产品：刘浩田  2023年5月6日10:50:58
                    Company company = companyExMapper.selectByPrimaryKey(driver.getCompanyId());
                    driver.setCompanyName(company.getCompanyName());

                    Integer vehicleId = driver.getVehicle_id();
                    if (vehicleId == null) {
                        continue;
                    }
                    //查询绑定车辆信息
                    Map<String, Object> paraMap = new HashMap<String, Object>();
                    paraMap.put("vehicleId", vehicleId);
                    RestResponse vehicleRep = RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null);
                    if (null != vehicleRep.getData()) {
                        Map<String, Object> vehicle = (Map<String, Object>) vehicleRep.getData();
                        driver.setVehicle_license(vehicle.get("vehicleLicense").toString());
                    }

                }
            }
            total = p.getTotal();
            return new PageDTO(pageNum, pageSize, total, driverList);
        } finally {
            PageHelper.clearPage();
        }
    }

    /**
     * 重写司机新增方法（baixiaoxuan 2019.11.28）
     *
     * @param driverName
     * @param driverMobile
     * @param licenseType
     * @param licenseNo
     * @param bindVehicleId
     * @param operateId
     * @param operateName
     * @param companyId
     * @param motorcadeId
     * @param driverStatus
     * @return
     */
    public RestResponse createDriver(String driverName, String driverMobile, String licenseType, String licenseNo,
                                     Integer bindVehicleId, Integer operateId, String operateName, Integer companyId, Integer driverType, Integer motorcadeId, Integer driverStatus) {
        //司机手机号校验；如果该司机的手机号在司机表中存在，则不允许使用该手机号
        if (driverExMapper.checkDriverByMobile(driverMobile, null) > 0) {
            return RestResponse.fail(UserErrorCode.DRIVER_MOBILE_EXIST);
        }
        //判断驾驶证号是否存在
        if (driverExMapper.checkExistByCondition(null, licenseNo, null, null)) {
            return RestResponse.fail(UserErrorCode.DRIVER_LICENSE_EXIST);
        }
        //判断新添加司机的手机号是否在其他的企业注册过用户信息，用户信息和司机信息的所属企业必须一致，否则提示用户无法操作
        List<Customer> customers = customerExMapper.verifyMobileUnique(driverMobile);
        if (null != customers && !customers.isEmpty()) {
            if (!customers.get(0).getCompanyId().equals(companyId)) {
                return RestResponse.fail(UserErrorCode.MOBILE_IS_EXIST_IN_USERS);
            } else {
                //检查用户的角色信息是否包含自有司机
                final StaffRoleRelationExample roleRelationExample = new StaffRoleRelationExample();
                roleRelationExample.createCriteria().andStaffCodeEqualTo(customers.get(0).getCustomerCode()).andRoleCodeEqualTo(SpecialRoleEnum.COMPANY_DRIVER.getRoleCode());
                final List<StaffRoleRelation> staffRoleRelations = staffRoleRelationExMapper.selectByExample(roleRelationExample);
                if (CollectionUtils.isEmpty(staffRoleRelations)) {
                    //插入自有司机角色关系
                    StaffRoleRelation insert = new StaffRoleRelation();
                    insert.setRoleCode(SpecialRoleEnum.COMPANY_DRIVER.getRoleCode());
                    insert.setStaffCode(customers.get(0).getCustomerCode());
                    insert.setSystemType(SystemTypeEnum.CUSTOMER.getCode());
                    staffRoleRelationExMapper.insertSelective(insert);
                }
            }
        } else {
            //创建用户信息并进行角色设置
            Customer newCustomer = new Customer();
            //必要的参数进行赋值
            newCustomer.setMobile(driverMobile);
            newCustomer.setCustomerName(driverName);
            newCustomer.setEmail("");
            logger.info("【企业自有司机】新增，同步创建用户账户，入参：" + JSON.toJSONString(newCustomer));
            customerManagementService.saveCustomerForDriver(newCustomer, SpecialRoleEnum.PROVIDER_DRIVER.getRoleCode());

        }

        //组装并插入司机信息
        Driver driver = new Driver();
        driver.setDriverName(driverName);
        driver.setPinyinName(PinYinUtil.getPingYin(driverName));
        driver.setDriverMobile(driverMobile);
        driver.setLicenseType(licenseType);
        driver.setLicenseNo(licenseNo);
        driver.setBindVehicleId(bindVehicleId);
        driver.setCompanyId(companyId);
        driver.setCreterId(operateId);
        driver.setCreterName(operateName);
        driver.setJoinTime(DateUtil.getCurrentDateTimeString());
        driver.setUpdateId(operateId);
        driver.setUpdateName(operateName);

        driver.setDriverStatus(driverStatus);
        driverExMapper.insertSelective(driver);

        //向车队和司机绑定关系表中添加绑定关系
        MotorcadeDriver motorcadeDriver = new MotorcadeDriver();
        motorcadeDriver.setDriverId(driver.getDriverId());
        motorcadeDriver.setCompanyId(companyId);
        motorcadeDriver.setMotorcadeId(motorcadeId);
        motorcadeDriverExMapper.insertSelective(motorcadeDriver);

        //发送短信
        Company company = companyExMapper.selectByPrimaryKey(companyId);
        String companyName = "";
        if (null == company.getCompanyAlias()) {
            companyName = company.getCompanyName();
        } else {
            companyName = company.getCompanyAlias();
        }
        String context = UserConstantCode.CUSTOMER_NEW_DRIVER.replace("XXX", companyName);
        String sendSms = SMSUtil.sendSms(driverMobile, context);
        //插入短信发送记录
        PushSms pushSms = new PushSms();
        pushSms.setPushType((byte) UserConstantCode.PUSH_TYPE_UPDATEMOBILE);
        pushSms.setReceiveMobile(driverMobile);
        pushSms.setSendContent(context);
        pushSms.setSendTime(DateUtil.getNowTime());
        pushSms.setSendStatus((byte) UserConstantCode.NORMAL_STATUS);
        pushSms.setResultContent(sendSms);
        pushSmsExMapper.insertSelective(pushSms);
        return RestResponse.success(true);
    }

    /**
     * 根据ID查询司机信息
     *
     * @param driver_id
     * @param driver_mobile
     * @param company_id
     * @return
     */
    public RestResponse getDriverById(Integer driver_id, String driver_mobile, Integer company_id) {
        DriverManageDTO driver = driverExMapper.selectByDriverId(driver_id, driver_mobile, company_id);
        if (driver != null && driver.getVehicle_id() != null) {
            final CarInfoDTO carInfoByIdIgnoreStatus = AssetApi.getCarInfoByIdIgnoreStatus(driver.getVehicle_id());
            if (carInfoByIdIgnoreStatus != null) {
                driver.setVehicle_license(carInfoByIdIgnoreStatus.getVehicleLicense());
            }
        }
        return RestResponse.success(driver);
    }


    /**
     * 重写司机更新方法（baixiaoxuan 2019.11.28）
     *
     * @param driverId
     * @param driverName
     * @param licenseNo
     * @param driverMobile
     * @param licenseType
     * @param bindVehicleId
     * @param driverStatus
     * @param operateId
     * @param operateName
     * @param companyId
     * @return
     */
    public RestResponse updateDriver(Integer driverId, String driverName, String licenseNo, String driverMobile,
                                     String licenseType, Integer bindVehicleId, Integer driverStatus, Integer operateId, String operateName, Integer companyId, Integer driverType) {
        driverMobile = driverMobile.trim();
        //判断该手机号是否是白名单号码,如果是的话判断该手机号在系统中是否存在,如果存在的话提示手机号已存在,不存在的话可以添加到系统中,不影响别的手机号
        if ("13811303051".equals(driverMobile)) {
            return RestResponse.fail(UserErrorCode.UPDATE_DRIVER_MOBILE_EXIST);
        }

        //司机手机号校验；如果该司机的手机号在司机表中存在，并且不是当前司机ID的，则不允许修改为该手机号
        if (driverExMapper.checkDriverByMobile(driverMobile, driverId) > 0) {
            return RestResponse.fail(UserErrorCode.DRIVER_MOBILE_EXIST);
        }

        //查询并判断该司机是否存在
        Driver currentDriver = driverExMapper.selectByPrimaryKey(driverId);
        if (null == currentDriver) {
            return RestResponse.fail(UserErrorCode.DRIVER_NOT_EXIST);
        }
        //判断修改的手机号是否在其他的企业注册过用户信息，用户信息和司机信息的所属企业必须一致，否则提示用户无法操作
        if (!driverMobile.equals(currentDriver.getDriverMobile())) {
            List<Customer> customers = customerExMapper.verifyMobileUnique(driverMobile);
            if (null != customers && !customers.isEmpty()) {
                if (!Objects.equals(customers.get(0).getCompanyId(), companyId)) {
                    return RestResponse.fail(UserErrorCode.MOBILE_IS_EXIST_IN_USERS);
                }
            }
        }

        //判断司机是否在接单中
        Map<String, Object> paraMap = new HashMap<String, Object>();
        paraMap.put("driverId", driverId);
        RestResponse orderRep = RestClient.requestInside(BaseHttpClient.HttpMethod.POST, RestLocators.order().getRestUrl(CHECK_DRIVER_ORDER), paraMap, null);
        if (null != orderRep.getData() && (boolean) orderRep.getData()) {
            return RestResponse.fail(UserErrorCode.DRIVER_HAVE_ORDER);
        }


        //组装并更新司机信息
        Driver driver = new Driver();
        driver.setDriverId(driverId);
        if (currentDriver.getVerifyStatus() != 1) {
            driver.setDriverName(driverName);
            driver.setPinyinName(PinYinUtil.getPingYin(driverName));
            driver.setLicenseNo(licenseNo);
        }
        driver.setDriverMobile(driverMobile);
        driver.setLicenseType(licenseType);
        driver.setBindVehicleId(bindVehicleId);
        driver.setUpdateId(operateId);
        driver.setUpdateName(operateName);

        driver.setDriverStatus(driverStatus);
        driverExMapper.updateByPrimaryKeySelective(driver);


        //司机手机号发生变化，则发送短信
        if (!driverMobile.equals(currentDriver.getDriverMobile())) {
            //查询企业信息
            Company company = companyExMapper.selectByPrimaryKey(companyId);
            String companyName = "";
            if (null == company.getCompanyAlias()) {
                companyName = company.getCompanyName();
            } else {
                companyName = company.getCompanyAlias();
            }

            String context = UserConstantCode.CUSTOMER_NEW_DRIVER.replace("XXX", companyName);
            String sendSms = SMSUtil.sendSms(driverMobile, context);
            //插入短信发送记录
            PushSms pushSms = new PushSms();
            pushSms.setPushType((byte) UserConstantCode.PUSH_TYPE_UPDATEMOBILE);
            pushSms.setReceiveMobile(driverMobile);
            pushSms.setSendContent(context);
            pushSms.setSendTime(DateUtil.getNowTime());
            pushSms.setSendStatus((byte) UserConstantCode.NORMAL_STATUS);
            pushSms.setResultContent(sendSms);
            pushSmsExMapper.insertSelective(pushSms);

            context = UserConstantCode.UPD_DRIVER.replace("XXX", companyName).replace("###", driverMobile);
            sendSms = SMSUtil.sendSms(currentDriver.getDriverMobile(), context);
            //插入短信发送记录
            pushSms = new PushSms();
            pushSms.setPushType((byte) UserConstantCode.PUSH_TYPE_UPDATEMOBILE);
            pushSms.setReceiveMobile(currentDriver.getDriverMobile());
            pushSms.setSendContent(context);
            pushSms.setSendTime(DateUtil.getNowTime());
            pushSms.setSendStatus((byte) UserConstantCode.NORMAL_STATUS);
            pushSms.setResultContent(sendSms);
            pushSmsExMapper.insertSelective(pushSms);
        }
        return RestResponse.success(true);
    }


    /**
     * 绑定车辆到司机
     *
     * @param driverId
     * @param bindVehicleId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @SuppressWarnings("unchecked")
    public RestResponse<Boolean> bindCarToDriver(Integer driverId, Integer bindVehicleId) {
        //查询并判断该司机是否存在
        Driver currentDriver = driverExMapper.selectByPrimaryKey(driverId);
        if (null == currentDriver) {
            return RestResponse.fail(UserErrorCode.DRIVER_NOT_EXIST);
        }
        if (Objects.equals(currentDriver.getBindVehicleId(),bindVehicleId)) {
            //司机已经绑定了这个车辆，直接返回成功即可
            return RestResponse.success(true);
        }
        CarInfoDTO carInfoDTO = new CarInfoDTO();
        carInfoDTO.setVehicleLicense("");
        carInfoDTO.setVehicleVin("");
        if(!Objects.equals(bindVehicleId, DEFAULT_VEHICLE_ID)){
            //校验一下车辆信息是否存在
            String restUrl = new MrCarAssetRestLocator().getRestUrl(GET_VEHICLE_URL);
            Map<String, Object> paraMap = new HashMap<String, Object>();
            paraMap.put("vehicleId", bindVehicleId);
            RestResponse<CarInfoDTO> vehicleRep = RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, CarInfoDTO.class);
            if (!vehicleRep.isSuccess() || vehicleRep.getData() == null) {
                return RestResponse.fail(UserErrorCode.VEHICLE_IS_NOT_EXIST);
            }
            carInfoDTO = vehicleRep.getData();
        }
        //组装并更新司机绑定车辆信息
        Driver driver = new Driver();
        driver.setDriverId(driverId);
        driver.setBindVehicleId(bindVehicleId);
        driverExMapper.updateByPrimaryKeySelective(driver);


        //解绑之前所有的绑定记录（正常情况下只有一条，此处做一下兼容逻辑）
        List<DriverBindVehicleLog> driverBindVehicleLogs = driverBindVehicleLogExMapper.selectCurrentBindLog(driverId);
        if (!CollectionUtils.isEmpty(driverBindVehicleLogs)) {
            List<Integer> logIds = driverBindVehicleLogs.stream().map(DriverBindVehicleLog::getId).collect(Collectors.toList());
            driverBindVehicleLogExMapper.batchUpdateUnbindTime(logIds, new Date());
        }

        //增加绑定车辆记录
        DriverBindVehicleLog insertDto = new DriverBindVehicleLog();
        insertDto.setDriverId(driverId);
        insertDto.setDriverName(currentDriver.getDriverName());
        insertDto.setVehicleId(bindVehicleId);
        insertDto.setVehicleLicense(carInfoDTO.getVehicleLicense());
        insertDto.setVehicleVin(carInfoDTO.getVehicleVin());
        insertDto.setBindTime(new Date());
        insertDto.setCreateTime(new Date());
        driverBindVehicleLogExMapper.insertSelective(insertDto);

        return RestResponse.success(true);
    }


    public RestResponse delete(List<Integer> driverIdArray, Integer companyId, String delete_time) {
        List<String> mobileList = driverExMapper.fileterDriverIds(driverIdArray, companyId);
        if (mobileList == null) {
            return RestResponse.fail(UserErrorCode.DRIVER_VALUE_NULL, "司机ID");
        }

        for (Integer driver_id : driverIdArray) {
            /*** 删除车队中的司机（baixiaoxuan 2019.11.27) */
            motorcadeDriverExMapper.updateForDelDriver(driver_id, new Date());

            int i = driverExMapper.deleteDriver(driver_id, companyId, delete_time);
        }

        return RestResponse.success(true);
    }

    public RestResponse changeDriverStatus(Integer driverId, Integer updId, String updName, Integer status) {
        Driver driver = driverExMapper.selectByPrimaryKey(driverId);
        if (driver == null) {
            return RestResponse.fail(UserErrorCode.DRIVER_VALUE_NULL, "司机ID");
        }
        if (status == 1) {
            List<Driver> drivers = driverExMapper.selectDriversByMobile(driver.getDriverMobile());
            if (null != drivers && !drivers.isEmpty()) {
                Driver enableDriverDB = drivers.get(0);
                String sourceTypeName = DriverEnums.DriverSourceTypeEnum.getValueByCode(enableDriverDB.getDriverSourceType());
                return RestResponse.fail(UserErrorCode.BUSINESS_CAR_DRIVER_USER_NOT_DISABLED, sourceTypeName);
            }
        }
        //司机存在，则进行状态更改
        Driver upd = new Driver();
        upd.setDriverId(driverId);
        upd.setDriverStatus(status);
        upd.setUpdateId(updId);
        upd.setUpdateName(updName);
        int res = driverExMapper.updateByPrimaryKeySelective(upd);
        if (res > 0 && DriverEnums.DriverStatusEnum.stop.getCode().equals(status)) {
            //停用司机需要更新对应的用户状态及角色信息
            customerManagementService.updCustomerForDriver(driver.getDriverMobile(), SpecialRoleEnum.COMPANY_DRIVER.getRoleCode(), updId, updName);

        }
        return RestResponse.success(res);
    }

    public RestResponse checkDrivers(List<Integer> driverIdArray, Integer companyId) {
        List<String> namesList = new ArrayList<>();
        for (Integer driverId : driverIdArray) {
            DriverManageDTO driver = driverExMapper.selectByDriverId(driverId, null, companyId);
            if (null == driver) {
                continue;
            }
            /** 判断司机是否在接单中  */
            Map<String, Object> paraMap = new HashMap<String, Object>();
            paraMap.put("driverId", driverId);
            RestResponse orderRep = RestClient.requestInside(BaseHttpClient.HttpMethod.POST, RestLocators.order().getRestUrl(CHECK_DRIVER_ORDER), paraMap, null);
            if (null != orderRep.getData() && (boolean) orderRep.getData() == true) {
                namesList.add(driver.getDriver_name());
            }

        }
        return RestResponse.success(namesList);
    }


    public List<Driver> listBindingDriver(Integer vehicleId, Integer companyId) {
        return driverExMapper.listBindingDriver(vehicleId, companyId);
    }

    public List<DriverBaseInfoDTO> bindingDriverInfo(Integer vehicleId, String nowDate) {

        Date bindTime = null;
        if (StringUtils.isNotBlank(nowDate)) {
            try {
                bindTime = DateUtil.parse(nowDate, DateUtil.TIME_FORMAT);
            } catch (ParseException e) {
                throw new RestErrorException("日期格式不正确", RestErrorCode.HTTP_PARAM_INVALID);
            }
        }
        List<DriverBindVehicleLog> driverBindVehicleLogList = driverBindVehicleLogExMapper.selectByVehicleId(vehicleId, bindTime);
        if (CollectionUtils.isEmpty(driverBindVehicleLogList)) {
            return new ArrayList<>();
        }
        List<DriverBaseInfoDTO> resultList = BeanUtil.copyList(driverBindVehicleLogList, DriverBaseInfoDTO.class);
        String ids = resultList.stream().map(w -> Integer.toString(w.getDriverId())).collect(Collectors.joining(","));
        List<Driver> driverList = driverExMapper.selectByDriverIds(ids);
        Map<Integer, List<Driver>> driverListMap = driverList.stream().collect(Collectors.groupingBy(Driver::getDriverId));

        resultList.forEach(item -> {
            List<Driver> drivers = driverListMap.get(item.getDriverId());
            if (!CollectionUtils.isEmpty(drivers)) {
                Driver driver = drivers.get(0);
                item.setDriverName(driver != null ? driver.getDriverName() : item.getDriverName());
            }
        });
        return resultList;
    }


    public RestResponse listMotorcade(Integer companyId) {
        List<Map<Integer, String>> map = motorcadeExMapper.ListMotorcadeName(companyId, null);

        return RestResponse.success(map);
    }

    public List<DriverDTO> getAllActiveDrivers(Integer fromIndex, Integer toIndex) {
        List<Driver> driverList = driverExMapper.selectAllActiveDrivers(fromIndex, toIndex);
        List<DriverDTO> driverDTOS = BeanUtil.copyList(driverList, DriverDTO.class);
        return driverDTOS;
    }

    /**
     * 根据企业id和司机id查询司机的基本信息、所在部门、所在车队
     *
     * @param companyId
     * @param driverId
     * @return
     */
    public DriverInfoDTO getDriverInfoById(Integer companyId, Integer driverId) {
        return driverExMapper.selectDriverById(companyId, driverId);
    }

    /**
     * 保存用户身份证信息
     *
     * @param driverId
     * @param certNo
     * @param certName
     * @param termOfValidity
     * @param frontImgUrl
     * @param backImgUrl
     * @return
     */
    public RestResponse saveDriverIDCardInfo(Integer driverId, String certNo, String certName, String termOfValidity,
                                             String frontImgUrl, String backImgUrl) {
        //查询并判断该司机是否存在
        Driver currentDriver = driverExMapper.selectByPrimaryKey(driverId);
        if (null == currentDriver || currentDriver.getDriverStatus() != 1) {
            return RestResponse.fail(UserErrorCode.DRIVER_NOT_EXIST);
        }
        //保存司机的身份证信息
        Driver driver = new Driver();
        driver.setDriverId(driverId);
        driver.setCertName(certName);
        driver.setCertNo(certNo);
        try {
            driver.setTermOfValidity(DateTimeUtils.StringToDate(termOfValidity));
        } catch (Exception e) {
            e.printStackTrace();
        }
        driver.setFrontImgUrl(frontImgUrl);
        driver.setBackImgUrl(backImgUrl);
        driverExMapper.updateByPrimaryKeySelective(driver);
        UserAuthInfoSaveDTO saveDTO = new UserAuthInfoSaveDTO();
        saveDTO.setTermOfValidity(driver.getTermOfValidity());
        saveDTO.setBackImgUrl(driver.getBackImgUrl());
        saveDTO.setFrontImgUrl(driver.getFrontImgUrl());
        saveDTO.setUserMobile(currentDriver.getDriverMobile());
        saveDTO.setCompanyId(currentDriver.getCompanyId());
        saveDTO.setCertName(driver.getCertName());
        saveDTO.setCertNo(driver.getCertNo());
        userFaceAuthService.saveUserAuthInfo(saveDTO);
        return RestResponse.success(null);
    }

    /**
     * @param driverId
     * @return
     */
    public RestResponse getIDInfoByDriverId(Integer driverId) {
        DriverIDCardInfoDTO idCardInfoDTO = new DriverIDCardInfoDTO();
        //查询并判断该司机是否存在
        Driver currentDriver = driverExMapper.selectByPrimaryKey(driverId);
        if (null == currentDriver || currentDriver.getDriverStatus() != 1) {
            return RestResponse.fail(UserErrorCode.DRIVER_NOT_EXIST);
        }
        idCardInfoDTO.setDriverId(currentDriver.getDriverId());
        idCardInfoDTO.setCertName(currentDriver.getCertName());
        idCardInfoDTO.setCertNo(currentDriver.getCertNo());
        idCardInfoDTO.setFrontImgUrl(currentDriver.getFrontImgUrl());
        idCardInfoDTO.setBackImgUrl(currentDriver.getBackImgUrl());
        idCardInfoDTO.setTermOfValidity(currentDriver.getTermOfValidity());
        return RestResponse.success(idCardInfoDTO);
    }

    /**
     * 认证成功之后，更新司机的姓名、证件号、以及认证状态
     *
     * @param driverId
     * @param driverName
     * @param licenseNo
     * @param verifyStatus
     * @return
     */
    public RestResponse updateDriverVerifyInfo(Integer driverId, String driverName, String licenseNo, Integer verifyStatus) {
        Driver old = driverExMapper.selectByPrimaryKey(driverId);
        Driver driver = new Driver();
        driver.setDriverId(driverId);
        driver.setDriverName(driverName);
        driver.setPinyinName(PinYinUtil.getPingYin(driverName));
        driver.setLicenseNo(licenseNo);
        driver.setVerifyStatus(verifyStatus);
        driverExMapper.updateByPrimaryKeySelective(driver);
        //兼容老版本司机的认证
        userFaceAuthService.updateVerifyStatus(old.getDriverMobile());
        return RestResponse.success(null);
    }

    /**
     * 根据司机姓名查询司机信息
     *
     * @param companyId
     * @param driverName
     * @return
     */
    public List<Driver> selectDriverByName(Integer companyId, String driverName, List<Object> dataScopeList) {
        return driverExMapper.selectDriverByName(companyId, driverName, dataScopeList);
    }

    /**
     * 根据车辆id将司机表中对应的司机绑定的车辆id清空
     */
    @Transactional(rollbackFor = Exception.class)
    public int unBindCarForDriver(Integer companyId, Integer vehicleId) {
        //查询一下车辆所属公司下的司机是否绑定了这个车辆
        DriverExample driverExample = new DriverExample();
        driverExample.createCriteria().andCompanyIdEqualTo(companyId).andBindVehicleIdEqualTo(vehicleId);
        List<Driver> drivers = driverMapper.selectByExample(driverExample);
        if (drivers.isEmpty()) {
            logger.info("车辆ID:{}在公司ID:{}下未绑定司机，无需解绑！", vehicleId, companyId);
            return 0;
        }

        List<Integer> driverIds = drivers.stream().map(Driver::getDriverId).collect(Collectors.toList());
        int res = driverExMapper.unBindVehicleForDriver(driverIds);
        logger.info("解绑车辆ID:{}的司机成功,影响条数:{}", vehicleId, res);

        //获取车辆和司机集合中的所有未绑定的记录
        List<DriverBindVehicleLog> driverBindVehicleLogs = driverBindVehicleLogExMapper.selectCurrentBindLogByVehicleId(vehicleId, driverIds);

        if (!driverBindVehicleLogs.isEmpty()) {
            List<Integer> logIds = driverBindVehicleLogs.stream().map(DriverBindVehicleLog::getId).collect(Collectors.toList());
            int logResult = driverBindVehicleLogExMapper.batchUpdateUnbindTime(logIds, new Date());
            logger.info("解绑车辆ID:{}的司机成功,更新日志,影响条数:{}", vehicleId, logResult);
        }
        return res;
    }


    /**
     * 为司机分配车队
     *
     * @param companyId
     * @param driverId
     * @param motorcadeId
     * @return
     */
    public int setMotorcadeForDriver(Integer companyId, Integer driverId, Integer motorcadeId) {
        int result = 0;
        MotorcadeDriver queryDriver = motorcadeDriverExMapper.queryListByDriverId(companyId, driverId);
        //向车队和司机绑定关系表中添加绑定关系
        MotorcadeDriver motorcadeDriver = new MotorcadeDriver();
        motorcadeDriver.setDriverId(driverId);
        motorcadeDriver.setCompanyId(companyId);
        motorcadeDriver.setMotorcadeId(motorcadeId);
        motorcadeDriver.setCreateTime(new Date());
        if (queryDriver != null) {
            motorcadeDriver.setId(queryDriver.getId());
            result = motorcadeDriverExMapper.updateByPrimaryKeySelective(motorcadeDriver);
            //判断当前分配的车队和原来的是否是同一个，若不相同，则需将旧的车队的排班信息清除
            if (queryDriver.getMotorcadeId() != motorcadeId) {
                motorcadeDriverExMapper.clearDriverSetting(driverId, queryDriver.getCompanyId(), queryDriver.getMotorcadeId(), new Date());
            }
        } else {
            result = motorcadeDriverExMapper.insertSelective(motorcadeDriver);
        }
        return result;
    }

    /**
     * 查询有车队的所有城市编码和名称
     *
     * @param companyId
     * @return
     */
    public RestResponse ListMotorcadeCity(Integer companyId) {
        List<Map<Integer, String>> map = motorcadeExMapper.ListMotorcadeCity(companyId, null);
        return RestResponse.success(map);
    }

    /**
     * 司机管理获取全部司机列表信息-运营端
     *
     * @param condition
     * @return
     */
    public PageDTO getAllDriverListByPage(QueryDriverProviderCondition condition) {
        int total = 0;
        List<DriverNewDTO> driverNewDTOS = null;
        List<Driver> drivers;
        if (ProviderDataPermTypeEnum.ONE_SELF.getType().equals(condition.getDataPermType())) {
            //本人无数据
            return new PageDTO(condition.getPageNo(), condition.getPageSize(), total, new ArrayList());
        }
        try {
            Page<DriverNewDTO> p = PageHelper.startPage(condition.getPageNo(), condition.getPageSize(), true);
            drivers = driverExMapper.listCustomerDrivers(condition);
            driverNewDTOS = BeanUtil.copyList(drivers, DriverNewDTO.class);
            if(CollUtil.isNotEmpty(driverNewDTOS)){
                String restUrl = new MrCarAssetRestLocator().getRestUrl(GET_VEHICLE_URL);
                driverNewDTOS.forEach(x->{
                    //多企业的账户查询，需要返回所属企业名称-产品：刘浩田  2023年5月6日10:50:58
                    Company company = companyExMapper.selectByPrimaryKey(x.getCompanyId());
                    if(company != null){
                        x.setCompanyName(company.getCompanyName());
                    }
                    Integer vehicleId = x.getBindVehicleId();
                    if (vehicleId != null) {
                        //查询绑定车辆信息
                        Map<String, Object> paraMap = new HashMap<>();
                        paraMap.put("vehicleId", vehicleId);
                        RestResponse vehicleRep = RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null);
                        if (null != vehicleRep.getData()) {
                            Map<String, Object> vehicle = (Map<String, Object>) vehicleRep.getData();
                            x.setVehicleLicense(String.valueOf(vehicle.get("vehicleLicense")));
                        }
                    }
                    //查询车队
                    DriverListRespDTO respDTO = driverExMapper.queryMotorcadeNameByDriverId(x.getDriverId());
                    if(respDTO!=null){
                        x.setMotorcadeId(respDTO.getMotorcadeId());
                        x.setMotorcadeName(respDTO.getMotorcadeName());
                    }
                });
            }
            total = (int) p.getTotal();
            // 判断查询结果
            if (driverNewDTOS == null || driverNewDTOS.isEmpty()) {
                return new PageDTO(condition.getPageNo(), condition.getPageSize(), 0, Collections.emptyList());
            }
        } catch (Exception e) {
            logger.error("司机管理查询全部司机 error.....", e);
        } finally {
            PageHelper.clearPage();
        }
        return new PageDTO(condition.getPageNo(), condition.getPageSize(), total, driverNewDTOS);

    }

    /**
     * 获取司机详情
     *
     * @param driverId
     * @return
     */
    public RestResponse getDriverDetail(Integer driverId) {
        Driver driver = driverExMapper.selectByPrimaryKey(driverId);
        DriverDetailDTO driverDetailDTO = BeanUtil.copyObject(driver, DriverDetailDTO.class);
        driverDetailDTO.setVehicleUsage(driver.getSupplierDriverLine());
        driverDetailDTO.setVehicleUsageStr(SupplierDriverLineEnum.getNameByCodes(driver.getSupplierDriverLine()));
        //去查看该司机的车队所属城市
        DriverDetailDTO belongCity = motorcadeDriverExMapper.getBelongCityByDriverId(driverId, driverDetailDTO.getCompanyId());
        if (belongCity != null) {
            driverDetailDTO.setBelongCityCode(belongCity.getBelongCityCode());
            driverDetailDTO.setBelongCityName(belongCity.getBelongCityName());
        }
        return RestResponse.success(driverDetailDTO);
    }

    public RestResponse getDriverDetailForPC(Integer driverId) {
        Driver driver = driverExMapper.selectByPrimaryKey(driverId);
        DriverNewDTO driverNewDTO = BeanUtil.copyObject(driver, DriverNewDTO.class);
        if(driverNewDTO!=null){
            Company company = companyExMapper.selectByPrimaryKey(driverNewDTO.getCompanyId());
            if(company!=null){
                driverNewDTO.setCompanyName(company.getCompanyName());
            }
            if(driverNewDTO.getGender()!=null && driverNewDTO.getGender()==0){
                driverNewDTO.setGender(null);
            }
        }
        return RestResponse.success(driverNewDTO);
    }

    public RestResponse isHaveUndoOrder(Integer driverIdOfLease, String date) {
        Driver driver = driverExMapper.selectDriverByLeaseDriverId(driverIdOfLease);
        if (null == driver) {
            return RestResponse.fail(UserErrorCode.DRIVER_NOT_EXIST);
        }
        Map<String, Object> paraMap = new HashMap<String, Object>();
        paraMap.put("driverId", driver.getDriverId());
        paraMap.put("bookingOrderStime", date);
        RestResponse orderRep = RestClient.requestInside(BaseHttpClient.HttpMethod.POST, RestLocators.order().getRestUrl(CHECK_DRIVER_ORDER), paraMap, null);
        return orderRep;
    }

    public RestResponse updateCarDriverById(Integer driverId, Byte workingStatus, String channel) {
        Driver driver = driverExMapper.selectByPrimaryKey(driverId);
        if (null == driver) {
            return RestResponse.fail(UserErrorCode.DRIVER_NOT_EXIST);
        } else {
            driver.setWorkingStatus(workingStatus);
            driver.setUpdateName(channel);
            return RestResponse.success(driverExMapper.updateByPrimaryKeySelective(driver));
        }
    }

    /**
     * 查询绑定司机信息通过绑定车辆ID
     *
     * @param vehicleIds 逗号分隔多个
     * @return
     */
    public RestResponse selectDriversByBindVehicleIds(String vehicleIds) {
        if (StringUtils.isBlank(vehicleIds)) {
            return RestResponse.success(null);
        }
        List<Driver> drivers = driverExMapper.selectDriversByBindVehicleIds(vehicleIds);
        List<DriverDTO> driverDTOS = BeanUtil.copyList(drivers, DriverDTO.class);
        return RestResponse.success(driverDTOS);
    }

    public RestResponse getSqDriverCountByCompanyId(DriverDataPanelQueryDTO driverDataPanelQueryDTO) {
        // 查询自有司机

        driverDataPanelQueryDTO.setDriverSourceType(DriverEnums.DriverSourceTypeEnum.zysj.getCode());
        int selfCount = driverExMapper.selectDriverCount(driverDataPanelQueryDTO);
        SqDriverCountRespDTO driverCount = new SqDriverCountRespDTO();
        driverCount.setSelfDriverNum(selfCount);
        driverDataPanelQueryDTO.setDriverSourceType(DriverEnums.DriverSourceTypeEnum.sqsj.getCode());
        int sqCount = driverExMapper.selectDriverCount(driverDataPanelQueryDTO);
        driverCount.setSqDriverNum(sqCount);
        return RestResponse.success(driverCount);
    }

    public PageDTO<CarDriverDispatchDTO> queryScheduledDriverList(DispatchForDriverReqDTO reqDTO) {
        ScheduleDTO scheduleDetail;
        try {
            scheduleDetail = ScheduleApi.getScheduleDetail(reqDTO.getScheduleId());
        } catch (RestErrorException e) {
            return new PageDTO<>(1, 10, 0, Collections.emptyList());
        }
        if (scheduleDetail == null) {
            return new PageDTO<>(1, 10, 0, Collections.emptyList());
        }
        //获取是否开启了重复派单的配置，如果开启了，无需过滤调度库存中被占用的车辆
        Boolean isEnableParallelOrder = ConfigApi.checkConfigByCompany(scheduleDetail.getCompanyId().intValue(),
                BussinessTypeDictionary.DISPATCH_REPEAT_ORDER.value());
        if (!isEnableParallelOrder) {
            //要排除在任务中的司机
            final List<Integer> occupiedDriverIds = OrderApi.queryOccupiedDriverIdList(reqDTO.getScheduleId());
            reqDTO.setOccupiedDriverIds(occupiedDriverIds);
        }
        //获取是否开启了多供应商的配置，如果开启了，不走数据权限，没开启还走数据权限
        Boolean supplierOrder = ConfigApi.checkConfigByCompany(scheduleDetail.getCompanyId().intValue(),
                BussinessTypeDictionary.MULTI_SUPPLIER_ORDER_RECEIPT.value());
        //处理数据权限
        DispatchForDriverReqDTO buildReqDTO = this.dispatchForDriverDataPerm(reqDTO);
        //数据权限不符合条件，直接返回空
        if (buildReqDTO == null && !supplierOrder) {
            return new PageDTO<>(reqDTO.getPageNum(), reqDTO.getPageSize(), 0, Collections.emptyList());
        }
        if(Boolean.TRUE.equals(supplierOrder)){
            if(buildReqDTO==null){
                buildReqDTO = reqDTO;
            }
            buildReqDTO.setSupplierOrderFlag(1);
        }

        //获取所有符合条件的司机ID
        final List<Integer> driverIds = driverExMapper.selectDriverWorking(buildReqDTO);
        if (driverIds.isEmpty()) {
            return new PageDTO<>(buildReqDTO.getPageNum(), buildReqDTO.getPageSize(), 0, Collections.emptyList());
        }

        Map<Long, List<String>> parallelOrderMap = batchGetParallelOrderList(driverIds,
                scheduleDetail.getCompanyId().intValue(), isEnableParallelOrder);


        //排序
        List<Integer> sortedDriverIds = this.sortDriverIdByOrderNum(
                driverIds,
                reqDTO.getSortField(),
                reqDTO.getSortOrder(),
                parallelOrderMap
        );


        //内存分页
        if (sortedDriverIds.isEmpty()) {
            sortedDriverIds = driverIds;
        }
        int fromIndex = buildReqDTO.getPageSize() * (buildReqDTO.getPageNum() - 1);
        if (fromIndex >= sortedDriverIds.size()) {
            fromIndex = sortedDriverIds.size();
        }
        int toIndex = fromIndex + buildReqDTO.getPageSize();
        if (toIndex >= sortedDriverIds.size()) {
            toIndex = sortedDriverIds.size();
        }
        final List<Integer> driverIdsList = sortedDriverIds.subList(fromIndex, toIndex);
        if (driverIdsList.isEmpty()) {
            return new PageDTO<>(buildReqDTO.getPageNum(), buildReqDTO.getPageSize(), sortedDriverIds.size(), Collections.emptyList());
        }


        //查询具体的司机信息
        final DriverExample driverExample = new DriverExample();
        driverExample.createCriteria().andDriverIdIn(driverIdsList);
        final List<Driver> drivers = driverExMapper.selectByExample(driverExample);

        Map<Integer, Driver> driversMap = drivers.stream().collect(Collectors.toMap(Driver::getDriverId, Function.identity()));



        final Integer bindVehicleId = buildReqDTO.getBindVehicleId();
        final List<CarDriverDispatchDTO> resultList = driverIdsList.stream().map(driverId -> {
            Driver d = driversMap.get(driverId);
            final CarDriverDispatchDTO carDriverDispatchDTO = BeanUtil.copyObject(d, CarDriverDispatchDTO.class);
            if (bindVehicleId != null && bindVehicleId.equals(d.getBindVehicleId())) {
                carDriverDispatchDTO.setBindStatus(1);
                carDriverDispatchDTO.setIsBindDriver(1);
            }
            final Motorcade motorcadeIdByDriverId = motorcadeDriverExMapper.getMotorcadeIdByDriverId(d.getDriverId());
            if (motorcadeIdByDriverId != null) {
                carDriverDispatchDTO.setMotorcadeName(motorcadeIdByDriverId.getMotorcadeName());
            }
            List<String> parallelOrderList = parallelOrderMap.get(d.getDriverId().longValue());
            if (CollectionUtils.isEmpty(parallelOrderList)) {
                parallelOrderList = Collections.emptyList();
            }
            carDriverDispatchDTO.setIsEnableParallelOrder(isEnableParallelOrder);
            carDriverDispatchDTO.setParallelOrderCount(parallelOrderList.size());
            carDriverDispatchDTO.setParallelOrderNoList(parallelOrderList);
            return carDriverDispatchDTO;
        }).collect(Collectors.toList());
        return new PageDTO<>(buildReqDTO.getPageNum(), buildReqDTO.getPageSize(), sortedDriverIds.size(), resultList);
    }

    //排序
    private List<Integer> sortDriverIdByOrderNum(List<Integer> driverIds,
                                                 Integer sortField,
                                                 Integer sort,
                                                 Map<Long, List<String>> parallelOrderMap) {


        List<Integer> sortedDriverIds;
        if (sortField != null && sortField == 1 && !CollectionUtils.isEmpty(parallelOrderMap)) {
            Map<Integer, Integer> driverIdOrderCountMap = new HashMap<>();
            for (Integer driverId : driverIds) {
                List<String> orders = parallelOrderMap.get(driverId.longValue());
                if (orders == null) {
                    orders = Collections.emptyList();
                }
                driverIdOrderCountMap.put(driverId, orders.size());
            }
            // 根据并行订单数量进行排序，如果数量相同则按司机ID排序
            return driverIds.stream()
                    .sorted((d1, d2) -> {
                        int count1 = driverIdOrderCountMap.get(d1);
                        int count2 = driverIdOrderCountMap.get(d2);
                        return sort == 1
                                ? Integer.compare(count1, count2)
                                : Integer.compare(count2, count1);
                    })
                    .collect(Collectors.toList());
        } else {
            sortedDriverIds = OrderApi.sortDriverIdByOrderNum(driverIds);
        }

        return sortedDriverIds;

    }

    /**
     * 批量获取司机并行订单个数
     */
    private Map<Long/*司机ID*/, List<String>/*子行程编码*/> batchGetParallelOrderList(List<Integer> driverIdsList,
                                                                                      Integer companyId,
                                                                                      Boolean isEnableParallelOrder) {
        if (!isEnableParallelOrder) {
            return Collections.emptyMap();
        } else {
            return batchGetParallelOrderList(driverIdsList, companyId);
        }
    }

    private Map<Long/*司机ID*/, List<String>/*子行程编码*/> batchGetParallelOrderList(List<Integer> driverIdsList, Integer companyId) {

        String batchGetDriverParallelOrder = MrcarOrderRestMsgCenter.BATCH_GET_DRIVER_PARALLEL_ORDER;

        String restUrl = new MrcarOrderRestLocator().getRestUrl(batchGetDriverParallelOrder);

        HashMap<String, Object> restParams = new HashMap<>();
        restParams.put("driverIds", StringUtils.join(driverIdsList, ","));
        restParams.put("companyId", companyId);

        @SuppressWarnings("unchecked")
        RestResponse<List<OrderInfoDTO>> restResponse = RestClient.requestForList(
                BaseHttpClient.HttpMethod.POST, restUrl,
                restParams, null, OrderInfoDTO.class);
        if (!restResponse.isSuccess()) {
            return Collections.emptyMap();
        }

        List<OrderInfoDTO> orderInfoDTOS = restResponse.getData();
        if (CollectionUtils.isEmpty(orderInfoDTOS)) {
            return Collections.emptyMap();
        }
        return orderInfoDTOS
                .stream()
                .collect(Collectors.groupingBy(OrderInfoDTO::getAssignDriverId,
                        Collectors.mapping(OrderInfoDTO::getOrderNo, Collectors.toList())));
    }

    private DispatchForDriverReqDTO dispatchForDriverDataPerm(DispatchForDriverReqDTO reqDTO) {
        //处理查询的数据权限
        if (LoginSystemEnum.PROVIDER.getSys().equals(reqDTO.getLoginSystemType())) {
            //负责客户、负责合同、本人、所属部门、指定部门：用户所属部门转城市后，匹配城市内的商务车队内的司机
            Set<Integer> structIdSet = new HashSet<>();
            //所属部门和指定部门直接查询即可
            if (ProviderDataPermTypeEnum.SELF_DEPT.getType().equals(reqDTO.getDataPermType()) || ProviderDataPermTypeEnum.ASSIGN_DEPT.getType().equals(reqDTO.getDataPermType())) {
                structIdSet = reqDTO.getDataPermIdSet();
            } else if (ProviderDataPermTypeEnum.ALL.getType().equals(reqDTO.getDataPermType())) {
                //数据权限为 所有，无需处理参数
            } else {
                //非指定部门、所属部门、所有之外的权限都需要获取所属部门
                final List<Integer> belongStructIds = providerInternalStaffManageService.getBelongStructIds(reqDTO.getLoginUserId());
                structIdSet.addAll(belongStructIds);
            }
            if (!structIdSet.isEmpty()) {
                final DatacenterStructBussExample structBussExample = new DatacenterStructBussExample();
                structBussExample.createCriteria().andStructIdIn(new ArrayList<>(structIdSet));
                final List<DatacenterStructBuss> datacenterStructBusses = datacenterStructBussExMapper.selectByExample(structBussExample);
                final Set<String> cityCodeSet = datacenterStructBusses.stream().map(DatacenterStructBuss::getBelongCityCode).map(String::valueOf).collect(Collectors.toSet());
                reqDTO.setDataCodeSet(cityCodeSet);
            }
        } else if (LoginSystemEnum.CLIENT.getSys().equals(reqDTO.getLoginSystemType())) {
            //客户端 数据权限是指定部门，查询指定的部门中的 拥有【首汽司机】【自有司机】角色的员工手机号码
            if (ClientDataPermTypeEnum.ASSIGN_DEPT.getType().equals(reqDTO.getDataPermType())) {
                //指定部门但是没有部门列表，返回无数据
                if (reqDTO.getDataPermIdSet() == null || reqDTO.getDataPermIdSet().isEmpty()) {
                    return null;
                }
                //获取拥有【首汽司机】【自有司机】角色的员工手机号码，用于匹配司机
                RoleInfoGetStaffReqDTO roleInfoGetStaffReqDTO = new RoleInfoGetStaffReqDTO();
                roleInfoGetStaffReqDTO.setRoleCodeList(Arrays.asList(SpecialRoleEnum.COMPANY_DRIVER.getRoleCode(),
                        SpecialRoleEnum.PROVIDER_DRIVER.getRoleCode()));
                roleInfoGetStaffReqDTO.setCompanyId(reqDTO.getLoginCompanyId());
                roleInfoGetStaffReqDTO.setStructIdList(new ArrayList<>(reqDTO.getDataPermIdSet()));
                final List<CustomerManageRespDTO> customerManageRespDTOS = staffRoleRelationExMapper.selectCustomerListByRole(roleInfoGetStaffReqDTO);
                if (customerManageRespDTOS.isEmpty()) {
                    return null;
                }
                reqDTO.setDriverMobileListUnderDept(customerManageRespDTOS.stream().filter(c -> StringUtils.isNotBlank(c.getMobile())).map(CustomerManageRespDTO::getMobile).collect(Collectors.toList()));
                return reqDTO;
            } else {
                //客户端 数据权限不是指定部门直接返回即可
                return reqDTO;
            }
        } else {
            return null;
        }
        return reqDTO;
    }

    /**
     * 根据司机名称或车队名称查询司机信息
     */
    public PageDTO getAppDriverList(AppDriverPageReqDTO dto) {
        PageDTO page = ObjectTransferUtil.page(dto.getPage(), dto.getPageSize(), () -> driverExMapper.getAppDriverList(dto));
        List<DriverInfoDTO> result = ObjectTransferUtil.cast(page.getResult());
        result.forEach(e -> {
            if (e.getBindVehicleId() != null) {
                CarInfoDTO carInfoDTO = AssetApi.getCarInfoByIdIgnoreStatus(e.getBindVehicleId());
                if (carInfoDTO != null) {
                    e.setBindVehicleLicense(carInfoDTO.getVehicleLicense());
                }
            }
        });
        return page;
    }


    public PageDTO<DriverListRespDTO> getDriverList(DriverListReqDTO driverListReqDTO) {
        long total = 0;
        int pageNum = driverListReqDTO.getPageNum();
        int pageSize = driverListReqDTO.getPageSize();

        Page p = PageHelper.startPage(pageNum, pageSize, true);
        List<DriverListRespDTO> driverList = driverExMapper.getDriverList(driverListReqDTO);
        if (driverList != null && driverList.size() > 0) {
            String restUrl = new MrCarAssetRestLocator().getRestUrl(GET_VEHICLE_URL);
            for (DriverListRespDTO driver : driverList) {
                //多企业的账户查询，需要返回所属企业名称-产品：刘浩田  2023年5月6日10:50:58
                Company company = companyExMapper.selectByPrimaryKey(driver.getCompanyId());
                if(company!=null){
                    driver.setCompanyName(company.getCompanyName());
                }
                Integer vehicleId = driver.getVehicleId();
                if (vehicleId != null) {
                    //查询绑定车辆信息
                    Map<String, Object> paraMap = new HashMap<>();
                    paraMap.put("vehicleId", vehicleId);
                    RestResponse vehicleRep = RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null);
                    if (null != vehicleRep.getData()) {
                        Map<String, Object> vehicle = (Map<String, Object>) vehicleRep.getData();
                        driver.setVehicleLicense(String.valueOf(vehicle.get("vehicleLicense")));
                    }
                }
            }
        }
        total = p.getTotal();
        return new PageDTO(pageNum, pageSize, total, driverList);
    }


    // 根据特征的部门查询司机信息(所有司机类型)
    public PageDTO<BusDriverSearchRespDTO> queryDriverByStructId(BusDriverSearchReqDTO reqDTO) {
        try (Page<Driver> page = PageHelper.startPage(reqDTO.getPage(), reqDTO.getPageSize());) {
            List<Driver> drivers = driverExMapper.queryDriversByConditions(reqDTO);
            // 查询公司
            List<Integer> companyIds = drivers.stream().map(Driver::getCompanyId).distinct().collect(Collectors.toList());
            Map<Integer, Company> companyMap =
                    companyIds.isEmpty() ?
                            Collections.emptyMap() :
                            this.companyExMapper.getCompanyListByIds(companyIds)
                                    .stream()
                                    .collect(Collectors.toMap(Company::getCompanyId, Function.identity()));
            // 查询部门
            List<Integer> departmentIds = drivers.stream()
                    .map(Driver::getStructId)
                    .distinct()
                    .collect(Collectors.toList());
            Map<Integer, CompanyDepartment> departmentMap =
                    departmentIds.isEmpty() ?
                            Collections.emptyMap() :
                            this.companyDepartmentExMapper.getDepartmentListByIds(
                                    departmentIds.stream().map(Objects::toString).collect(Collectors.joining(",")))
                                    .stream()
                                    .collect(Collectors.toMap(CompanyDepartment::getId, Function.identity()));
            List<BusDriverSearchRespDTO> list =
                    drivers.stream().map(s -> {
                        BusDriverSearchRespDTO respDTO = new BusDriverSearchRespDTO();
                        BeanUtils.copyProperties(s, respDTO);
                        Company company = companyMap.get(s.getCompanyId());
                        if (Objects.nonNull(company)) {
                            respDTO.setCompanyName(company.getCompanyName());
                        }
                        CompanyDepartment department = departmentMap.get(s.getStructId());
                        if (Objects.nonNull(department)) {
                            respDTO.setStructName(department.getDepartmentName());
                        }
                        return respDTO;
                    }).collect(Collectors.toList());

            return new PageDTO<>(page.getPageNum(), page.getPageSize(), page.getTotal(), list);
        }
    }


    public PageDTO<DriverListRespDTO> getDriverListV2(DriverListReqDTO driverListReqDTO) {
        long total = 0;
        int pageNum = driverListReqDTO.getPageNum();
        int pageSize = driverListReqDTO.getPageSize();

        Page p = PageHelper.startPage(pageNum, pageSize, true);
        List<DriverListRespDTO> driverList = driverExMapper.getDriverListV2(driverListReqDTO);
        if (driverList != null && driverList.size() > 0) {
            String restUrl = new MrCarAssetRestLocator().getRestUrl(GET_VEHICLE_URL);
            for (DriverListRespDTO driver : driverList) {
                //多企业的账户查询，需要返回所属企业名称-产品：刘浩田  2023年5月6日10:50:58
                Company company = companyExMapper.selectByPrimaryKey(driver.getCompanyId());
                driver.setCompanyName(company.getCompanyName());
                Integer vehicleId = driver.getVehicleId();
                if (vehicleId != null) {
                    //查询绑定车辆信息
                    Map<String, Object> paraMap = new HashMap<>();
                    paraMap.put("vehicleId", vehicleId);
                    RestResponse vehicleRep = RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null);
                    if (null != vehicleRep.getData()) {
                        Map<String, Object> vehicle = (Map<String, Object>) vehicleRep.getData();
                        driver.setVehicleLicense(String.valueOf(vehicle.get("vehicleLicense")));
                    }
                }
            }
        }
        total = p.getTotal();
        return new PageDTO(pageNum, pageSize, total, driverList);
    }


    public List<ClientDriverDropdownRespDTO> driverDropdownRespDTOList(DriverDownListReq reqDTO) {
        PageInfo<ClientDriverDropdownRespDTO> pageInfo = PageHelper.startPage(reqDTO.getPage(), reqDTO.getPageSize()).doSelectPageInfo(() -> driverExMapper.driverDropdownListForClient(reqDTO));
        List<ClientDriverDropdownRespDTO> driverList = pageInfo.getList();
        if (CollectionUtils.isEmpty(driverList)) {
            return driverList;
        }
        driverList.forEach(driver -> {
            Integer bindVehicleStatus = (Objects.nonNull(driver.getBindVehicleId()) && !Objects.equals(driver.getBindVehicleId(),0)) ? 1 : 0;
            String bindVehicleStatusStr = (Objects.nonNull(driver.getBindVehicleId()) && !Objects.equals(driver.getBindVehicleId(),0)) ? "已绑定" : "未绑定";
            driver.setBindVehicleStatusStr(bindVehicleStatusStr);
            driver.setBindVehicleStatus(bindVehicleStatus);
        });
        return driverList;
    }


    public BatchDTO importDriverBatch(DriverImportReqDTO reqDTO) {
        InputStream file = downloadFile(reqDTO.getExcelUrl());
        //1. 读取excel 封装数据
        List<DriverImportTemplateDTO> dataList = readExcelToDataList(file);
        if(CollUtil.isEmpty(dataList)){
            throw ExceptionFactory.createRestException(MrCarAssetErrorCode.PARAMETER_VERIFICATION_ERROR, "导入内容不能为空");
        }
        if (dataList.size() > 1000) {
            throw ExceptionFactory.createRestException(MrCarAssetErrorCode.PARAMETER_VERIFICATION_ERROR, "导入最多1000行");
        }
        // 2. 校验并保存合法数据，返回失败数据
        List<DriverImportTemplateRespDTO> errorData = checkAndSaveDataReturnErrorData(dataList, reqDTO);
        //3.  写出失败的
        String errorFileUrl = writeFailData2Exel(errorData);

        // 4.返回结果
        BatchDTO batchDTO = new BatchDTO();
        batchDTO.setError(errorData.size());
        batchDTO.setTotal(dataList.size());
        batchDTO.setSuccess(dataList.size() - errorData.size());
        batchDTO.setDownloadUrl(errorFileUrl);
        return batchDTO;
    }

    public InputStream downloadFile(String urlString) {
        File tmpFile = null;
        try {
            // 使用HttpURLConnection下载文件
            AtomicReference<String> atomicReferenceFilePath = new AtomicReference<>("");
            InputStream inputStream = maintainGarageService.getExcelInputStream(urlString, atomicReferenceFilePath);
            tmpFile = Files.createTempFile(System.currentTimeMillis() + "", ".xlsx").toFile();

            // 创建本地文件输出流
            FileOutputStream fileOutputStream = new FileOutputStream(tmpFile);
            // 读取并写入数据
            byte[] buffer = new byte[1024];
            int length;
            while ((length = inputStream.read(buffer)) != -1) {
                fileOutputStream.write(buffer, 0, length);
            }
            // 关闭输入流和输出流
            inputStream.close();
            fileOutputStream.close();

            return new FileInputStream(tmpFile);
        } catch (IOException e) {
            logger.info("excel地址：{}下载失败", urlString, e);
            throw new IllegalArgumentException("excel导入失败");
        } finally {
            if (Objects.nonNull(tmpFile)) {
                tmpFile.delete();
            }
        }
    }

    /**
     * 读取excel 封装数据
     */
    private List<DriverImportTemplateDTO> readExcelToDataList(InputStream file) {
        List<DriverImportTemplateDTO> dataList = new ArrayList<>();
        EasyExcel.read(file, new ReadListener() {
            @Override
            public void onException(Exception e, AnalysisContext analysisContext) throws Exception {
            }

            @Override
            public void invoke(Object o, AnalysisContext analysisContext) {
                LinkedHashMap<Integer, Object> cellMap = (LinkedHashMap<Integer, Object>) o;
                DriverImportTemplateDTO reqDTO = new DriverImportTemplateDTO();
                int cellNum = 0;
                // 司机姓名
                Object cell = cellMap.get(cellNum++);
                String driverName = cell == null ? "" : cell.toString();
                reqDTO.setDriverName(driverName);
                // 司机手机号
                cell = cellMap.get(cellNum++);
                String driverMobile = cell == null ? "" : cell.toString();
                reqDTO.setDriverMobile(driverMobile);
                // 所属部门
                cell = cellMap.get(cellNum++);
                String orgName = cell == null ? "" : cell.toString();
                reqDTO.setStructName(orgName);
                // 性别
                cell = cellMap.get(cellNum++);
                String gender = cell == null ? null : cell.toString();
                reqDTO.setGender(gender);
                // 身份证号码
                cell = cellMap.get(cellNum++);
                String idCard = cell == null ? null : cell.toString();
                reqDTO.setIdCardNumber(idCard);
                //准驾车型
                cell = cellMap.get(cellNum++);
                String licenseType = cell == null ? "" : cell.toString();
                reqDTO.setLicenseType(licenseType);
                //驾照初领日期
                cell = cellMap.get(cellNum++);
                try {
                    String firstPickupTime = cell == null ? null : cell.toString();
                    if (StringUtils.isNotBlank(firstPickupTime)) {
                        if(firstPickupTime.contains("/")){
                            firstPickupTime = firstPickupTime.replaceAll("/", "-");
                        }
                    }
                    reqDTO.setFirstPickupTime(DateUtil.format2Date(firstPickupTime, DateUtil.DATE_FORMAT));
                } catch (Exception e) {
                    reqDTO.setFirstPickupTime(null);
                }
                cell = cellMap.get(cellNum++);
                //驾照有效日期
                try {
                    String termOfValidity = cell == null ? null : cell.toString();
                    if (StringUtils.isNotBlank(termOfValidity)) {
                        if(termOfValidity.contains("/")){
                            termOfValidity = termOfValidity.replaceAll("/", "-");
                        }
                    }
                    reqDTO.setTermOfValidity(DateUtil.format2Date(termOfValidity, DateUtil.DATE_FORMAT));
                } catch (Exception e) {
                    reqDTO.setTermOfValidity(null);
                }
                //发证机关
                cell = cellMap.get(cellNum++);
                String issuingOrgan = cell == null ? null : cell.toString();
                reqDTO.setIssuingOrgan(issuingOrgan);
                dataList.add(reqDTO);

            }

            @Override
            public void extra(CellExtra cellExtra, AnalysisContext analysisContext) {
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
            }

            @Override
            public boolean hasNext(AnalysisContext analysisContext) {
                return true;
            }

            @Override
            public void invokeHead(Map map, AnalysisContext analysisContext) {
            }
        }).sheet(0).doRead();

        return dataList;
    }

    /**
     * 校验并保存合法数据，返回失败数据
     */
    private List<DriverImportTemplateRespDTO> checkAndSaveDataReturnErrorData(List<DriverImportTemplateDTO> dataList, DriverImportReqDTO reqDTO) {
        List<DriverImportTemplateRespDTO> errorData = new ArrayList<>();
        List<DriverNewDTO> successData = new ArrayList<>();
        for (DriverImportTemplateDTO dto : dataList) {
            DriverNewDTO newDTO = BeanUtil.copyObject(dto, DriverNewDTO.class);
            DriverImportTemplateRespDTO respDTO = BeanUtil.copyObject(dto, DriverImportTemplateRespDTO.class);
            String driverName = dto.getDriverName();
            String driverMobile = dto.getDriverMobile();
            newDTO.setCompanyId(reqDTO.getLoginCompanyId());
            //校验 司机姓名
            StringBuilder builder = new StringBuilder();
            if (org.apache.commons.lang.StringUtils.isBlank(driverName)) {
                builder.append("A.司机姓名不能为空\n");
            }
            if (org.apache.commons.lang.StringUtils.isNotBlank(driverName) && driverName.length()>20) {
                builder.append("A.司机姓名超出长度限制\n");
            }
            //校验 司机手机号不能为空
            if (org.apache.commons.lang.StringUtils.isBlank(driverMobile)) {
                builder.append("B. 司机手机号不能为空\n");
            }
            if(StringUtils.isNotBlank(driverMobile) && !isValidPhoneNumber(driverMobile) ){
                builder.append("B. 司机手机号位数不符合规则\n");
            }
            if(StringUtils.isBlank(dto.getStructName())){
                builder.append("B. 所属部门不能为空\n");
            }
            //校验部门
            if (org.apache.commons.lang.StringUtils.isNotEmpty(dto.getStructName())) {
                //处理部门信息
                String split = "->";
                String structName = dto.getStructName();
                String[] structNames = structName.split(split);
                int level = structNames.length;
                List<CompanyDepartment> departmentDTOList = departmentExMapper.getDepartmentList(reqDTO.getLoginCompanyId(), (byte) level, structNames[structNames.length - 1],null);
                if (CollectionUtils.isEmpty(departmentDTOList)) {
                    builder.append("C.所属部门不存在，请按照模板下拉选项导入");
                } else {
                    CompanyDepartment departmentDTO = departmentDTOList.stream().filter(r -> Objects.equals(r.getStatus(), (byte) 1)).findFirst().orElse(null);
                    if (Objects.isNull(departmentDTO)) {
                        builder.append("C.所属部门已停用");
                    } else {
                        newDTO.setStructId(departmentDTO.getId());
                        newDTO.setStructName(departmentDTO.getDepartmentName());
                        newDTO.setBelongStructCode(String.valueOf(departmentDTO.getId()));
                        newDTO.setBelongStructName(departmentDTO.getDepartmentName());
                    }
                }
            }
            //校验 性别
            if (StringUtils.isNotBlank(dto.getGender())) {
                newDTO.setGender(GenderEnum.getKeyByValue(dto.getGender()));
            }
            newDTO.setDriverSourceType(DriverEnums.DriverSourceTypeEnum.zysj.getCode());
            //身份证号码
            String idCard = dto.getIdCardNumber();
            if (StringUtils.isNotBlank(idCard)) {
                boolean valid = IdCardNumberValidator.isValidIDCard(idCard);
                if (!valid) {
                    builder.append("E.身份证号不符合位数规则，请修改后重新录入");
                } else {
                    String birth = IdcardUtil.getBirth(idCard);
                    if(StringUtils.isNotBlank(birth)){
                        try{
                            Date date = DateUtil.parse(birth, DatePattern.PURE_DATE_PATTERN);
                            String dateStr = DateUtil.format(date, DatePattern.NORM_DATE_FORMAT.getPattern());
                            newDTO.setBrithDate(dateStr);
                        }catch (Exception e){
                            newDTO.setBrithDate(null);
                        }
                    }
                }
                newDTO.setCertNo(idCard);
            }
            if (StringUtils.isNotBlank(dto.getIssuingOrgan()) && dto.getIssuingOrgan().length()>50) {
                builder.append("I.发证机关超出长度限制");
            }
            if(StringUtils.isBlank(dto.getLicenseType()) || !LICENSE_TYPE_LIST.contains(dto.getLicenseType())){
                newDTO.setLicenseType("C1");
            }
            if(dto.getFirstPickupTime()!=null){
                try {
                    newDTO.setFirstPickupTime(DateUtil.format(dto.getFirstPickupTime(), DateUtil.DATE_FORMAT));
                } catch (Exception e){
                    logger.info(e.getMessage());
                }
            }
            if(dto.getTermOfValidity()!=null){
                try {
                    newDTO.setArriveTime(DateUtil.format(dto.getTermOfValidity(), DateUtil.DATE_FORMAT));
                } catch (Exception e){
                    logger.info(e.getMessage());
                }
            }
            // 有错误信息
            if (builder.length() > 0) {
                respDTO.setErrMsg(builder.toString());
                errorData.add(respDTO);
            } else {
                try {
                    RestResponse<?> restResponse = driverInfoManagerService.addOrUpdateCustomerOwnDriver(newDTO);
                    if (!restResponse.isSuccess()) {
                        builder.append(restResponse.getMsg());
                        respDTO.setErrMsg(builder.toString());
                        errorData.add(respDTO);
                    }
                } catch (RestErrorException e) {
                    builder.append(e.getMessage());
                    respDTO.setErrMsg(builder.toString());
                    errorData.add(respDTO);
                } catch (Exception e) {
                    builder.append(e.getMessage());
                    respDTO.setErrMsg(builder.toString());
                    errorData.add(respDTO);
                }
                successData.add(newDTO);
            }
        }
        return errorData;
    }

    private String writeFailData2Exel(List<DriverImportTemplateRespDTO> errorData) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(errorData)) {
            return "";
        }
        logger.info("开始写失败数据到Excel");
        // 1.整理数据
        List<List<String>> errorList = Lists.newArrayList();
        for (DriverImportTemplateRespDTO errorDatum : errorData) {
            List<String> error = Lists.newArrayList();
            String driverName = Objects.nonNull(errorDatum.getDriverName()) ? errorDatum.getDriverName() : "";
            error.add(driverName);
            String driverMobile = Objects.nonNull(errorDatum.getDriverMobile()) ? errorDatum.getDriverMobile() : "";
            error.add(driverMobile);
            String structName = Objects.nonNull(errorDatum.getStructName()) ? errorDatum.getStructName() : "";
            error.add(structName);
            String gender = Objects.nonNull(errorDatum.getGender()) ? errorDatum.getGender().toString() : "";
            error.add(gender);
            String idCardNumber = Objects.nonNull(errorDatum.getIdCardNumber()) ? errorDatum.getIdCardNumber() : "";
            error.add(idCardNumber);
            String licenseType = Objects.nonNull(errorDatum.getLicenseType()) ? errorDatum.getLicenseType() : "";
            error.add(licenseType);
            String firstPickupTime = Objects.nonNull(errorDatum.getFirstPickupTime()) ? DateUtils.format(errorDatum.getFirstPickupTime(), DateUtil.DATE_FORMAT) : "";
            error.add(firstPickupTime);
            String termOfValidity = Objects.nonNull(errorDatum.getTermOfValidity()) ? DateUtils.format(errorDatum.getTermOfValidity(), DateUtil.DATE_FORMAT) : "";
            error.add(termOfValidity);
            String issuingOrgan = Objects.nonNull(errorDatum.getIssuingOrgan()) ? errorDatum.getIssuingOrgan() : "";
            error.add(issuingOrgan);
            error.add(errorDatum.getErrMsg());
            errorList.add(error);
        }
        // 2.整理表头
        List<List<String>> headerList = Lists.newArrayList();
        headerList.add(Lists.newArrayList("*司机姓名"));
        headerList.add(Lists.newArrayList("*司机手机号"));
        headerList.add(Lists.newArrayList("*所属部门"));
        headerList.add(Lists.newArrayList("性别"));
        headerList.add(Lists.newArrayList("身份证号"));
        headerList.add(Lists.newArrayList("准驾车型"));
        headerList.add(Lists.newArrayList("驾照初领日期"));
        headerList.add(Lists.newArrayList("驾驶证有效期"));
        headerList.add(Lists.newArrayList("发证机关"));
        headerList.add(Lists.newArrayList("错误原因"));

        String url = "";
        try (ByteArrayOutputStream byteOut = new ByteArrayOutputStream()) {
            ExcelWriter excelWriter = EasyExcel.write(byteOut).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "司机录入模板").build();
            writeSheet.setHead(headerList);
            excelWriter.write(errorList, writeSheet);
            excelWriter.finish();
            byteOut.close();
            String fileName = "司机导入失败数据" + System.currentTimeMillis() + ExcelTypeEnum.XLSX.getValue();
            if (CollUtil.isNotEmpty(errorList)) {
                return OSSUtils.uploadFile(fileName, byteOut.toByteArray(), OSSBucketEnum.TMP_IMG.getCode());
            }
        } catch (Exception e) {
            throw new IllegalArgumentException("司机导入失败数据文件生产异常");
        }
        return url;
    }

    public static boolean isValidPhoneNumber(String phone) {
        // 长度必须为11位
        return phone.length() == 11 && phone.matches("\\d{11}");
    }
}
