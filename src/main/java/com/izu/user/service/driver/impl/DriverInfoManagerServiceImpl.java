package com.izu.user.service.driver.impl;

import com.alibaba.fastjson.JSONObject;
import com.izu.asset.errcode.MrCarAssetErrorCode;
import com.izu.framework.rocketmq.CommonRocketProducer;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.exception.RestErrorException;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.lbs.common.dto.driver.output.DriverSendMqDTO;
import com.izu.mrcar.config.enums.MotorcadeEnums;
import com.izu.mrcar.iot.dto.BatchDTO;
import com.izu.mrcar.iot.utils.OSSUtils;
import com.izu.mrcar.order.consts.OrderEnum;
import com.izu.mrcar.order.dto.order.BusinessScheduleDriverRespDTO;
import com.izu.third.enums.OSSBucketEnum;
import com.izu.user.config.errcode.UserErrorCode;
import com.izu.user.consts.DriverAllocationMethodEnum;
import com.izu.user.consts.RocketMqConst;
import com.izu.user.dto.*;
import com.izu.user.dto.driver.*;
import com.izu.user.entity.*;
import com.izu.user.enums.*;
import com.izu.user.enums.user.ProviderMgtUserStateEnum;
import com.izu.user.enums.user.ProviderUserStatusEnum;
import com.izu.user.rpc.OrderApi;
import com.izu.user.service.driver.DriverAllocationRecordService;
import com.izu.user.service.driver.DriverInfoManagerService;
import com.izu.user.service.provider.staff.ProviderStaffService;
import com.izu.user.service.user.CustomerManagementService;
import com.izu.user.util.ExcelCommon;
import com.izu.user.util.ObjectTransferUtil;
import com.izu.user.utils.DateUtil;
import com.izu.user.utils.PinYinUtil;
import com.izu.utils.DateUtils;
import mapper.ProviderStaffMapper;
import mapper.ex.*;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 司机信息管理服务接口的实现类
 *
 * <AUTHOR> on  2024/7/4 下午1:57
 */
@Service
public class DriverInfoManagerServiceImpl implements DriverInfoManagerService {

    private static final Logger logger = LoggerFactory.getLogger(DriverInfoManagerServiceImpl.class);

    @Resource
    private DriverExMapper driverExMapper;

    @Resource
    private CompanyDepartmentExMapper companyDepartmentExMapper;

    @Resource
    private CustomerManagementService customerManagementService;

    @Resource
    private CompanyExMapper companyExMapper;

    @Resource
    private ProviderStaffService providerStaffService;

    @Resource
    private ProviderStaffMapper providerStaffMapper;

    @Resource
    private MotorcadeExMapper motorcadeExMapper;

    @Resource
    private MotorcadeDriverExMapper motorcadeDriverExMapper;


    @Resource
    private CustomerExMapper customerExMapper;

    @Resource
    private DriverAllocationRecordService driverAllocationRecordService;

    @Resource(name = "mrcarUserMQProducer")
    private CommonRocketProducer producer;


    private static final String COMMA = "；";


    @Override
    public boolean addOrUpdateShouqiDiandanDriver(DriverSendMqDTO reqDTO) {

        Driver newDriver = convertDriverDTO(reqDTO);
        Driver daiDianDriverDB = driverExMapper.selectDriverByLeaseDriverId(reqDTO.getDriverId());
        RestResponse<?> driverResult;
        if (daiDianDriverDB == null) {
            driverResult = handleNewDriver(newDriver, false, null);
        } else {
            driverResult = handleExistingDriver(newDriver, daiDianDriverDB);
        }
        if (!driverResult.isSuccess()) {
            //失败之后记录一下错误
            logger.error("代垫司机新增或者修改司机表错误 : {}", driverResult.getMsg());
        } else {
            Integer driverId = (Integer)driverResult.getData();
            //判断是否需要生成调拨单：1.司机离职 && 2.司机当前在客户名下
            DriverToCustomerSyncInfoDTO.DriverStatusChange driverStatusChange =
                    getDriverStatusChange(daiDianDriverDB, newDriver);
            if (driverStatusChange == DriverToCustomerSyncInfoDTO.DriverStatusChange.SUSPENSION) {
                //停职状态隐含了 daiDianDriverDB 不为空
                if (daiDianDriverDB != null && Objects.nonNull(daiDianDriverDB.getCompanyId()) && daiDianDriverDB.getCompanyId() > 1) {
                    logger.info("司机ID:{} 代垫司机新增或者修改司机表，司机离职，司机当前在客户名下，生成调拨单",driverId);
                    DriverAllocationRecordSaveReqDTO driverAllocationRecordSaveReqDTO = getDriverAllocationRecordSaveReqDTO(driverId);
                    driverAllocationRecordService.doAllocation(driverAllocationRecordSaveReqDTO);
                }
            }
            notifyDriverStatusChange(newDriver,driverStatusChange);
        }
        return driverResult.isSuccess();
    }

    @NotNull
    private DriverAllocationRecordSaveReqDTO getDriverAllocationRecordSaveReqDTO(Integer driverId) {
        //司机离职，需要生成司机调拨单，将司机收回首汽租赁客户下
        DriverAllocationRecordSaveReqDTO allocationRecordSaveReqDTO = new DriverAllocationRecordSaveReqDTO();
        allocationRecordSaveReqDTO.setCreatedId(0);
        allocationRecordSaveReqDTO.setCreatedName("综合推送");
        allocationRecordSaveReqDTO.setAllocationMethod(DriverAllocationMethodEnum.RECOVERY.getCode());
        allocationRecordSaveReqDTO.setTargetCompanyId(0);
        allocationRecordSaveReqDTO.setAllocationReason("司机离职，从客户企业收回");
        allocationRecordSaveReqDTO.setDriverList(Collections.singletonList(driverId));
        return allocationRecordSaveReqDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RestResponse<?> addOrUpdateCustomerOwnDriver(DriverNewDTO driverNewDTO) {
        RestResponse<?> restResponse = doAddOrUpdateCustomerOwnDriver(driverNewDTO, true);
        if (!restResponse.isSuccess()) {
            return restResponse;
        } else {
            return RestResponse.success(true);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RestResponse<?> addOrUpdateThirdPartyDriver(DriverProviderLingSanSaveReqDTO param) {
        DriverNewDTO driverNewDTO = this.convertDriverToNewDTO(param);
        RestResponse<?> restResponse = this.doAddOrUpdateCustomerOwnDriver(driverNewDTO, false);
        if (!restResponse.isSuccess()) {
            return restResponse;
        }
        //获取driverId
        Integer driverId = (Integer) restResponse.getData();
        driverNewDTO.setDriverId(driverId);
        //如果所属城市不为空，处理一下城市字段
        if (StringUtils.isNotBlank(driverNewDTO.getBelongCityCode())) {
            dealBelongCity(driverNewDTO);
        }
        return restResponse;
    }

    @Override
    @SuppressWarnings("unchecked")//去掉未检查的类转换异常
    @Transactional(rollbackFor = Exception.class)
    public RestResponse<BusinessScheduleDriverRespDTO> addThirdPartyDriverFromApp(SupplierDriverSaveDTO supplierDriverSaveDTO) {
        DriverNewDTO driverNewDTO = buildDriverProviderLingSanSave(supplierDriverSaveDTO);
        RestResponse<?> restResponse = this.doAddOrUpdateCustomerOwnDriver(driverNewDTO, false);
        if (!restResponse.isSuccess()) {
            return (RestResponse<BusinessScheduleDriverRespDTO>) restResponse;
        }
        //获取司机ID
        Integer driverId = (Integer) restResponse.getData();
        BusinessScheduleDriverRespDTO businessScheduleDriverRespDTO = new BusinessScheduleDriverRespDTO();
        Integer companyId = driverNewDTO.getCompanyId();
        businessScheduleDriverRespDTO.setSupplierId(companyId);
        //获取一下司机所在企业的名称
        Company company = companyExMapper.selectByPrimaryKey(companyId);
        businessScheduleDriverRespDTO.setSupplierName(company.getCompanyName());
        businessScheduleDriverRespDTO.setDriverId(driverId);
        businessScheduleDriverRespDTO.setDriverName(driverNewDTO.getDriverName());
        businessScheduleDriverRespDTO.setBelongCityCode(driverNewDTO.getBelongCityCode());
        businessScheduleDriverRespDTO.setBelongCityName(driverNewDTO.getBelongCityName());
        businessScheduleDriverRespDTO.setLicenseType(driverNewDTO.getLicenseType());
        businessScheduleDriverRespDTO.setDriverMobile(driverNewDTO.getDriverMobile());
        businessScheduleDriverRespDTO.setHeadIcon("");
        businessScheduleDriverRespDTO.setDriverSourceType(driverNewDTO.getDriverSourceType());

        driverNewDTO.setDriverId(driverId);
        //如果所属城市不为空，处理一下城市字段
        if (StringUtils.isNotBlank(driverNewDTO.getBelongCityCode())) {
            dealBelongCity(driverNewDTO);
        }
        return RestResponse.success(businessScheduleDriverRespDTO);
    }

    @Override
    @SuppressWarnings("unchecked")
    public RestResponse<BatchDTO> batchAddThirdPartyDriver(SupplierDriverBatchImportDTO supplierDriverBatchImportDTO) {
        List<DriverProviderLingSanSaveReqDTO> driverList = supplierDriverBatchImportDTO.getDriverList();
        if (CollectionUtils.isEmpty(driverList)) {
            return RestResponse.fail(MrCarAssetErrorCode.INFO_NULL, "导入的司机列表");
        }
        List<DriverNewDTO> driverNewDTOS = driverList.stream().map(this::convertDriverToNewDTO).collect(Collectors.toList());
        List<ImportErrorDTO> importErrorDTOS = doImport(driverNewDTOS);


        Map<Integer, ImportErrorDTO> indexAndErrorMap = importErrorDTOS.stream().collect(Collectors.toMap(ImportErrorDTO::getRowNum, e -> e));
        List<SupplierDriverImportFailDTO> errorDriverList = new ArrayList<>(importErrorDTOS.size());
        for (int i = 0; i < driverList.size(); i++) {
            ImportErrorDTO importErrorDTO = indexAndErrorMap.get(i);
            if (importErrorDTO != null) {
                DriverProviderLingSanSaveReqDTO saveReqDTO = driverList.get(i);
                SupplierDriverImportFailDTO supplierDriverImportFailDTO = convertDriverToFailDTO(saveReqDTO, importErrorDTO.getReason());
                errorDriverList.add(supplierDriverImportFailDTO);
            }
        }
        String errorFilePath = uploadErrorFile(errorDriverList);
        //定义错误对象列表
        BatchDTO batchDTO = new BatchDTO();
        int fail = errorDriverList.size();
        int total = driverList.size();
        batchDTO.setSuccess(total - fail);
        batchDTO.setError(fail);
        batchDTO.setTotal(total);
        batchDTO.setDownloadUrl(errorFilePath);
        return RestResponse.success(batchDTO);
    }

    private @NotNull List<ImportErrorDTO> doImport(List<DriverNewDTO> driverNewDTOS) {
        List<ImportErrorDTO> importErrorDTOS = validateDriverField(driverNewDTOS);
        Set<Integer> errorIndexSet = importErrorDTOS.stream().map(ImportErrorDTO::getRowNum).collect(Collectors.toSet());
        for (int i = 0, length = driverNewDTOS.size(); i < length; i++) {
            if (errorIndexSet.contains(i)) {
                continue;
            }
            DriverNewDTO driverNewDTO = driverNewDTOS.get(i);
            try {
                RestResponse<?> restResponse = this.addOrUpdateCustomerOwnDriver(driverNewDTO);
                if (!restResponse.isSuccess()) {
                    ImportErrorDTO supplierDriverImportFailDTO = new ImportErrorDTO();
                    supplierDriverImportFailDTO.setReason(restResponse.getMsg());
                    supplierDriverImportFailDTO.setRowNum(i);
                    importErrorDTOS.add(supplierDriverImportFailDTO);
                }
            } catch (RestErrorException e) {
                ImportErrorDTO supplierDriverImportFailDTO = new ImportErrorDTO();
                supplierDriverImportFailDTO.setReason(e.getMessage());
                supplierDriverImportFailDTO.setRowNum(i);
                importErrorDTOS.add(supplierDriverImportFailDTO);
            } catch (Exception e) {
                logger.error("导入司机异常", e);
                ImportErrorDTO supplierDriverImportFailDTO = new ImportErrorDTO();
                supplierDriverImportFailDTO.setReason("服务异常，请联系管理员解决");
                supplierDriverImportFailDTO.setRowNum(i);
                importErrorDTOS.add(supplierDriverImportFailDTO);
            }
        }
        return importErrorDTOS;
    }

    @Override
    @SuppressWarnings("unchecked")
    public RestResponse<BatchDTO> batchAddCustomerOwnDriver(BatchDriverInsertDTO batchDriverInsertDTO) {
        List<DriverNewDTO> driverList = convertDriverToNewDTO(batchDriverInsertDTO);
        if (CollectionUtils.isEmpty(driverList)) {
            return RestResponse.fail(MrCarAssetErrorCode.INFO_NULL, "导入的司机列表");
        }
        List<ImportErrorDTO> importErrorDTOS = this.doImport(driverList);
        importErrorDTOS.forEach(i -> i.setRowNum(i.getRowNum() + 2));
        return RestResponse.success(importErrorDTOS);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RestResponse<?> updateDriverState(DriverUpdateStateReqDTO driverUpdateStateReqDTO) {
        DriverEnums.DriverStatusEnum byCode = DriverEnums.DriverStatusEnum.getByCode(driverUpdateStateReqDTO.getDriverStatus());
        if (byCode == null) {
            //参数错误
            return RestResponse.fail(RestErrorCode.HTTP_PARAM_INVALID);
        }
        Driver driver = driverExMapper.selectByPrimaryKey(driverUpdateStateReqDTO.getDriverId());
        if (driver == null) {
            return RestResponse.fail(UserErrorCode.DRIVER_IS_NOT_EXIST);
        }
        //只适用于企业自有司机和三方司机 driverSourceType
        if (!DriverEnums.DriverSourceTypeEnum.zysj.getCode().equals(driver.getDriverSourceType())
                && !DriverEnums.DriverSourceTypeEnum.lingsan.getCode().equals(driver.getDriverSourceType())) {
            return RestResponse.fail(UserErrorCode.NOT_SUPPORT);
        }
        //启用司机，需要校验手机号是否被其他用户占用
        if (DriverEnums.DriverStatusEnum.enable == byCode && isDriverMobileExist(driver.getDriverMobile(), driver.getDriverId())) {
            return RestResponse.fail(UserErrorCode.MOBILE_IS_EXIST_IN_STATUS);
        }
        //看一下当前的司机状态是不是要修改的状态，如果是的话直接返回提示
        if (driver.getDriverStatus().equals(byCode.getCode())) {
            return RestResponse.fail(UserErrorCode.DRIVER_STATUS_SAME, byCode.getValue());
        }
        //自有司机停用的时候，需要校验一下司机有没有任务中的订单
        if (DriverEnums.DriverSourceTypeEnum.zysj.getCode().equals(driver.getDriverSourceType())) {
            RestResponse<Boolean> restResponse = OrderApi.checkDriverHasOrder(driverUpdateStateReqDTO.getDriverId(), OrderEnum.OrderType.INTERNAL_ORDER);
            if (!restResponse.isSuccess()) {
                return RestResponse.fail(UserErrorCode.DRIVER_ORDER_ERROR);
            } else if (restResponse.getData()) {
                return RestResponse.fail(UserErrorCode.DRIVER_HAS_ORDER);
            }
        }
        //修改司机状态
        Driver upd = new Driver();
        upd.setDriverId(driver.getDriverId());
        upd.setDriverStatus(byCode.getCode());
        upd.setUpdateId(driverUpdateStateReqDTO.getLoginUserId());
        upd.setUpdateName(driverUpdateStateReqDTO.getLoginUserName());
        // 如果司机停用,则清空绑定车辆  清空车队信息
        if (Objects.equals(DriverEnums.DriverStatusEnum.stop,byCode)){
            upd.setBindVehicleId(0);
            motorcadeDriverExMapper.updateForDelDriver(driver.getDriverId(),new Date());
        }
        driverExMapper.updateByPrimaryKeySelective(upd);
        DriverToCustomerSyncInfoDTO syncInfoDTO = buildDriverToCustomerSyncInfoDTO(driver.getDriverId());
        syncInfoDTO.setDriverSourceType(DriverEnums.DriverSourceTypeEnum.getByCode(driver.getDriverSourceType()));
        DriverToCustomerSyncInfoDTO.DriverStatusChange driverStatusChange;
        if (DriverEnums.DriverStatusEnum.enable == byCode) {
            //启用相当于重新入职
            driverStatusChange = DriverToCustomerSyncInfoDTO.DriverStatusChange.RECONTRACT;
        } else {
            driverStatusChange = DriverToCustomerSyncInfoDTO.DriverStatusChange.SUSPENSION;
        }
        syncInfoDTO.setDriverStatusChange(driverStatusChange);
        RestResponse<?> restResponse = customerManagementService.syncCustomerForDriver(syncInfoDTO);
        if (!restResponse.isSuccess()) {
            //回滚事务
            throw new RestErrorException(restResponse.getMsg(), restResponse.getCode());
        }
        return RestResponse.success(true);
    }

    @Override
    @SuppressWarnings("unchecked")
    public RestResponse<DriverDetailDTO> verifyDriverIsBusinessCarDriver(Integer driverId) {
        RestResponse<Driver> driverRestResponse = this.verifyDriverIsConvertToBusinessCar(driverId);
        if (driverRestResponse.isSuccess()) {
            Driver driver = driverRestResponse.getData();
            DriverDetailDTO driverDetailDTO = BeanUtil.copyObject(driver, DriverDetailDTO.class);
            driverDetailDTO.setVehicleUsage(driver.getSupplierDriverLine());
            driverDetailDTO.setVehicleUsageStr(SupplierDriverLineEnum.getNameByCodes(driver.getSupplierDriverLine()));
            return RestResponse.success(driverDetailDTO);
        } else {
            return RestResponse.create(driverRestResponse.getCode(), driverRestResponse.getMsg(), false, null);
        }
    }


    /**
     * 校验一个司机能否被转换成商务车司机
     *
     * @param driverId 页面输入的司机ID
     * @return 符合要求返回driver信息对象
     */
    @SuppressWarnings("unchecked")
    public RestResponse<Driver> verifyDriverIsConvertToBusinessCar(Integer driverId) {
        //1.获取司机详情
        Driver driver = driverExMapper.selectByPrimaryKey(driverId);
        if (driver == null) {
            return RestResponse.fail(UserErrorCode.DRIVER_IS_NOT_EXIST);
        }
        if (!DriverEnums.DriverSourceTypeEnum.sqsj.getCode().equals(driver.getDriverSourceType())) {
            return RestResponse.fail(UserErrorCode.NOT_SUPPORT);
        }
        //校验司机状态
        if (!DriverEnums.DriverStatusEnum.enable.getCode().equals(driver.getDriverStatus())) {
            return RestResponse.fail(UserErrorCode.PRODUCT_PACK_VALID_FAIL);
        }
        //司机任职状态，只能是：[正常] 和[休假]
        if (DriverOfficeStatusEnum.NORMAL.getKey() != driver.getOfficeStatus() && DriverOfficeStatusEnum.VACATION.getKey() != driver.getOfficeStatus()) {
            return RestResponse.fail(UserErrorCode.BUSINESS_CAR_DRIVER_USER_NOT_NORMAL);
        }

        //校验是否已经是商务车司机
        if (!StringUtils.isEmpty(driver.getSupplierDriverLine())) {
            String[] split = driver.getSupplierDriverLine().split(",");
            if (Arrays.asList(split).contains(String.valueOf(SupplierDriverLineEnum.COMMERCIAL_VEHICLES_USE.getCode()))) {
                return RestResponse.fail(UserErrorCode.BUSINESS_CAR_DRIVER_USER_EXIST, DriverEnums.DriverSourceTypeEnum.swcsj.getValue());
            }
        }
        return RestResponse.success(driver);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    @SuppressWarnings("unchecked")
    public RestResponse<Boolean> convertDriverToBusinessCar(ConvertDriverToBusinessCarReqDTO carReqDTO) {
        Integer driverId = carReqDTO.getDriverId();
        RestResponse<Driver> driverRestResponse = this.verifyDriverIsConvertToBusinessCar(driverId);
        if (!driverRestResponse.isSuccess()) {
            return RestResponse.create(driverRestResponse.getCode(), driverRestResponse.getMsg(), false, null);
        }
        Driver driver = driverRestResponse.getData();
        //为司机添加商务车的业务线
        String supplierDriverLine = driver.getSupplierDriverLine();
        if (StringUtils.isEmpty(supplierDriverLine)) {
            //此处只是兼容策略，如果数据异常，首汽代垫司机默认给一个内部车的业务线
            supplierDriverLine = String.valueOf(SupplierDriverLineEnum.INTERNAL_VEHICLES_USE.getCode());
        }
        List<String> lineList = new ArrayList<>(Arrays.asList(supplierDriverLine.split(",")));
        if (!lineList.contains(String.valueOf(SupplierDriverLineEnum.COMMERCIAL_VEHICLES_USE.getCode()))) {
            lineList.add(String.valueOf(SupplierDriverLineEnum.COMMERCIAL_VEHICLES_USE.getCode()));
        }
        Driver updateRecord = new Driver();
        updateRecord.setDriverId(driverId);
        updateRecord.setSupplierDriverLine(String.join(",", lineList));
        updateRecord.setUpdateId(carReqDTO.getLoginUserId());
        updateRecord.setUpdateName(carReqDTO.getLoginUserName());
        driverExMapper.updateByPrimaryKeySelective(updateRecord);
        //为内部账号添加一条用户信息，并且赋予一个[商务车司机]的角色
        DriverToCustomerSyncInfoDTO syncInfoDTO = this.buildDriverToCustomerSyncInfoDTO(driverId);
        syncInfoDTO.setDriverStatusChange(DriverToCustomerSyncInfoDTO.DriverStatusChange.RESUMPTION);
        RestResponse<Boolean> syncResponse = providerStaffService.convertDriverToStaff(syncInfoDTO);
        if (!syncResponse.isSuccess()) {
            throw new RestErrorException(syncResponse.getMsg(), syncResponse.getCode());
        }
        return RestResponse.success(true);
    }

    @Override
    @SuppressWarnings("unchecked")
    public RestResponse<CheckStaffIsBusinessCarDriverRespDTO> checkStaffIsBusinessCarDriver(Integer providerStaffId) {
        ProviderStaff providerStaff = providerStaffMapper.selectByPrimaryKey(providerStaffId);
        if (providerStaff == null) {
            return RestResponse.fail(UserErrorCode.CUSTOMER_NOT_EXIST);
        }
        //mgtStaffId如果小于等于0代表不是内部员工
        if (providerStaff.getMgtStaffId() <= 0) {
            return RestResponse.fail(UserErrorCode.BUSINESS_CAR_DRIVER_USER_EXIST, DriverEnums.DriverSourceTypeEnum.swcsj.getValue());
        }
        //校验员工状态
        if (!ProviderUserStatusEnum.ENABLE.getUserState().equals(providerStaff.getUserState()) || !ProviderMgtUserStateEnum.ENABLE.getState().equals(providerStaff.getMgtUserState())) {
            return RestResponse.fail(UserErrorCode.USER_INVALID);
        }
        Integer driverId = providerStaff.getDriverId();
        if (driverId > 0) {
            //校验一下绑定的这个司机ID是不是就是这个手机号
            Driver driver = driverExMapper.selectByPrimaryKey(driverId);
            if (!driver.getDriverMobile().equals(providerStaff.getMobile())) {
                return RestResponse.fail(UserErrorCode.BUSINESS_CAR_DRIVER_USER_EXIST, DriverEnums.DriverSourceTypeEnum.swcsj.getValue());
            } else {
                //如果就是这个手机号，看一下是不是有效的状态，并且是不是有商务车的业务线了，如果是的话给出提示
                if (DriverEnums.DriverStatusEnum.enable.getCode().equals(driver.getDriverStatus()) && isBusinessCarDriver(driver)) {
                    return RestResponse.fail(UserErrorCode.BUSINESS_CAR_DRIVER_USER_EXIST, DriverEnums.DriverSourceTypeEnum.swcsj.getValue());
                }
            }
        }
        //校验手机号是否跟其他司机账号冲突
        Driver conflictDriver;
        if (driverId > 0) {
            conflictDriver = getExistDriverMobile(providerStaff.getMobile(), driverId);
        } else {
            conflictDriver = getExistDriverMobile(providerStaff.getMobile());
        }
        if (conflictDriver != null) {
            String sourceTypeName = DriverEnums.DriverSourceTypeEnum.getValueByCode(conflictDriver.getDriverSourceType());
            return RestResponse.fail(UserErrorCode.BUSINESS_CAR_DRIVER_USER_EXIST, sourceTypeName);
        }
        return RestResponse.success(BeanUtil.copyObject(providerStaff, CheckStaffIsBusinessCarDriverRespDTO.class));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @SuppressWarnings("unchecked")
    public RestResponse<Boolean> convertStaffToBusinessCar(AddBusinessCarDriverFromStaffReqDTO reqDTO) {
        Integer providerStaffId = reqDTO.getProviderStaffId();
        RestResponse<CheckStaffIsBusinessCarDriverRespDTO> checkResponse = checkStaffIsBusinessCarDriver(providerStaffId);
        if (!checkResponse.isSuccess()) {
            return RestResponse.create(checkResponse.getCode(), checkResponse.getMsg(), false, null);
        }
        CheckStaffIsBusinessCarDriverRespDTO checkStaffRespDto = checkResponse.getData();
        //如果说driverId不为空，可能是重新开通的场景，直接将状态改为有效即可
        Driver driver = BeanUtil.copyObject(reqDTO, Driver.class);
        driver.setDriverName(checkStaffRespDto.getStaffName());
        driver.setPinyinName(PinYinUtil.getPingYin(driver.getDriverName()));
        driver.setDriverMobile(checkStaffRespDto.getMobile());
        driver.setDriverEmail(checkStaffRespDto.getEmail());
        driver.setCertNo(checkStaffRespDto.getIdCardNum());
        driver.setGender(checkStaffRespDto.getGender());
        driver.setHeadIcon(checkStaffRespDto.getHeadIcon());
        driver.setDriverStatus(DriverEnums.DriverStatusEnum.enable.getCode());
        driver.setOfficeStatus(DriverOfficeStatusEnum.NORMAL.getKey());
        driver.setWorkingStatus(DriverEnums.DriverWorkStatusEnum.notwork.getCode());
        driver.setDriverType(DriverEnums.DriverTypeEnum.inner.getCode());
        driver.setDriverSourceType(DriverEnums.DriverSourceTypeEnum.zt.getCode());
        driver.setSupplierDriverLine(String.valueOf(SupplierDriverLineEnum.COMMERCIAL_VEHICLES_USE.getCode()));
        //商务车司机所在企业默认为 1
        driver.setCompanyId(1);
        driver.setJoinTime(DateUtil.getCurrentDateTimeString());
        driver.setEntryDate(DateUtil.getCurrentDateString());
        driver.setCreterId(reqDTO.getLoginUserId());
        driver.setCertName(reqDTO.getLoginUserName());
        if (checkStaffRespDto.getDriverId() != null && checkStaffRespDto.getDriverId() > 0) {
            driver.setDriverId(checkStaffRespDto.getDriverId());
            driverExMapper.updateByPrimaryKeySelective(driver);
        } else {
            driverExMapper.insertSelective(driver);
        }
        DriverToCustomerSyncInfoDTO syncInfoDTO = buildDriverToCustomerSyncInfoDTO(driver.getDriverId());
        syncInfoDTO.setDriverStatusChange(DriverToCustomerSyncInfoDTO.DriverStatusChange.RESUMPTION);
        syncInfoDTO.setProviderStaffId(providerStaffId);
        RestResponse<Boolean> convertResp = providerStaffService.convertDriverToStaff(syncInfoDTO);
        if (!convertResp.isSuccess()) {
            //回滚事务
            throw new RestErrorException(convertResp.getMsg(), convertResp.getCode());
        }
        return RestResponse.success(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @SuppressWarnings("unchecked")
    public RestResponse<Boolean> editBusinessCarDriver(EditBusinessCarDriverFromStaffReqDTO reqDTO) {
        //判断司机是否存在
        Integer driverId = reqDTO.getDriverId();
        Driver driver = driverExMapper.selectByPrimaryKey(driverId);
        if (driver == null) {
            return RestResponse.fail(UserErrorCode.DRIVER_IS_NOT_EXIST);
        }
        //判断是否不是内部车账号转化的商务车司机
        if (!Objects.equals(driver.getDriverSourceType(), DriverEnums.DriverSourceTypeEnum.zt.getCode())) {
            return RestResponse.fail(UserErrorCode.DRIVER_NOT_BUSINESS_CAR_DRIVER);
        }
        Driver update = BeanUtil.copyObject(reqDTO, Driver.class);
        update.setUpdateId(reqDTO.getLoginUserId());
        update.setUpdateName(reqDTO.getLoginUserName());
        driverExMapper.updateByPrimaryKeySelective(update);
        return RestResponse.success(true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @SuppressWarnings("unchecked")
    public RestResponse<Boolean> deleteBusinessCarDriver(DeleteBusinessDriverReqDTO reqDTO) {
        Integer driverId = reqDTO.getDriverId();
        Driver driver = driverExMapper.selectByPrimaryKey(driverId);
        if (driver == null) {
            return RestResponse.fail(UserErrorCode.DRIVER_NOT_EXIST);
        }
        //如果是已经停用，无需再次停用
        if (Objects.equals(driver.getDriverStatus(), DriverEnums.DriverStatusEnum.stop.getCode())) {
            return RestResponse.fail(UserErrorCode.DRIVER_STATUS_SAME, "停用");
        }
        if (!isBusinessCarDriver(driver)) {
            return RestResponse.fail(UserErrorCode.DRIVER_NOT_BUSINESS_CAR_DRIVER);
        }
        if (Objects.equals(driver.getWorkingStatus(), DriverEnums.DriverWorkStatusEnum.woking.getCode())) {
            return RestResponse.fail(UserErrorCode.DRIVER_OCCUPY_FAIL_WOKING);
        }
        if (isInternalAccount(driver, reqDTO.getDeleteType())) {
            return RestResponse.fail(UserErrorCode.DRIVER_NOT_DISABLED);
        }
        //校验一下有没有进行中的商务车订单
        RestResponse<Boolean> restResponse = OrderApi.checkDriverHasOrder(driverId, OrderEnum.OrderType.MOTORCADE_ORDER);
        if (!restResponse.isSuccess()) {
            return RestResponse.fail(UserErrorCode.DRIVER_ORDER_ERROR);
        } else if (restResponse.getData()) {
            return RestResponse.fail(UserErrorCode.DRIVER_HAS_ORDER);
        }
        Driver update = new Driver();
        update.setDriverId(driverId);
        update.setUpdateId(reqDTO.getLoginUserId());
        update.setUpdateName(reqDTO.getLoginUserName());
        syncDriverStatus(driver, update);
        driverExMapper.updateByPrimaryKeySelective(update);
        DriverToCustomerSyncInfoDTO syncInfoDTO = buildDriverToCustomerSyncInfoDTO(driver.getDriverId());
        syncInfoDTO.setDriverStatusChange(DriverToCustomerSyncInfoDTO.DriverStatusChange.UPDATE);
        syncInfoDTO.setDeleteType(DriverToCustomerSyncInfoDTO.DeleteType.getByCode(reqDTO.getDeleteType()));
        RestResponse<Boolean> syncResponse = providerStaffService.convertDriverToStaff(syncInfoDTO);
        if (!syncResponse.isSuccess()) {
            //回滚事务
            throw new RestErrorException(syncResponse.getMsg(), syncResponse.getCode());
        }
        return RestResponse.success(true);
    }

    private boolean isBusinessCarDriver(Driver driver) {
        List<Byte> supplierLineList = parseSupplierLines(driver.getSupplierDriverLine());
        return supplierLineList.contains(SupplierDriverLineEnum.COMMERCIAL_VEHICLES_USE.getCode());
    }


    private boolean isInternalAccount(Driver driver, int deleteType) {
        return Objects.equals(driver.getDriverSourceType(), DriverEnums.DriverSourceTypeEnum.zt.getCode()) && deleteType == 1;
    }

    private void syncDriverStatus(Driver driver, Driver update) {
        DriverEnums.DriverSourceTypeEnum byCode = DriverEnums.DriverSourceTypeEnum.getByCode(driver.getDriverSourceType());
        switch (byCode) {
            case zt:
            case swcsj:
                update.setDriverStatus(DriverEnums.DriverStatusEnum.stop.getCode());
                break;
            default:
                List<Byte> supplierLineList = parseSupplierLines(driver.getSupplierDriverLine());
                supplierLineList.removeIf(code -> code.equals(SupplierDriverLineEnum.COMMERCIAL_VEHICLES_USE.getCode()));
                update.setSupplierDriverLine(supplierLineList.stream().map(String::valueOf).collect(Collectors.joining(",")));
                break;
        }
    }

    private List<Byte> parseSupplierLines(String lines) {
        return Arrays.stream(lines.split(",")).map(String::trim).distinct().map(Byte::parseByte).collect(Collectors.toList());
    }

    //上传错误文件到oss
    private String uploadErrorFile(List<?> list) {
        if (!list.isEmpty()) {
            try {
                byte[] bytes = ExcelCommon.createBigVehicleExcel(ObjectTransferUtil.objectToList(list), "错误信息");
                return OSSUtils.uploadFile("导入三方司机失败信息", bytes, OSSBucketEnum.TMP_IMG.getCode());
            } catch (Exception e) {
                logger.error("上传oss报错", e);
            }
        }
        return StringUtils.EMPTY;
    }

    private DriverNewDTO convertDriverToNewDTO(DriverProviderLingSanSaveReqDTO param) {
        DriverNewDTO driverNewDTO = BeanUtil.copyObject(param, DriverNewDTO.class);

        //获取公司的基本信息
        Company company = companyExMapper.selectByPrimaryKey(param.getCompanyId());
        //校验一下公司类型
        if (company == null || !CompanyAttributeEnums.SUPPLIER.getValue().equals(company.getCompanyAttribute())) {
            throw ExceptionFactory.createRestException(UserErrorCode.ERROR_COMPANY_ATTRIBUTE_CREATE_DRIVER);
        }
        driverNewDTO.setCompanyCode(company.getCustomerCode());
        driverNewDTO.setCustomerCompanyName(company.getCompanyName());
        driverNewDTO.setCompanyId(param.getCompanyId());

        driverNewDTO.setCreterId(param.getLoginUserId());
        driverNewDTO.setCreterName(param.getLoginUserName());
        driverNewDTO.setUpdateId(param.getLoginUserId());
        driverNewDTO.setUpdateName(param.getLoginUserName());
        driverNewDTO.setGenderStr(param.getGenderName());
        if (StringUtils.isNoneEmpty(param.getVehicleUsage())) {
            driverNewDTO.setSupplierDriverLine(param.getVehicleUsage());
        }
        return driverNewDTO;
    }

    private List<DriverNewDTO> convertDriverToNewDTO(BatchDriverInsertDTO batchDriverInsertDTO) {
        List<DriverImportDTO> driverList = batchDriverInsertDTO.getDriverList();
        if (CollectionUtils.isEmpty(driverList)) {
            return new ArrayList<>(1);
        }
        //获取公司信息
        Company company = companyExMapper.selectByPrimaryKey(batchDriverInsertDTO.getCompanyId());
        return driverList.stream().map(d -> {
            DriverNewDTO driverNewDTO = BeanUtil.copyObject(d, DriverNewDTO.class);
            //格式化一下初次领证日期
            if (StringUtils.isNotBlank(d.getFirstPickupTime())) {
                driverNewDTO.setFirstPickupTime(DateUtil.formatDateToString(DateUtil.parseDate(d.getFirstPickupTime())));
            }
            if (StringUtils.isNotBlank(d.getArriveTime())) {
                driverNewDTO.setArriveTime(DateUtil.formatDateToString(DateUtil.parseDate(d.getArriveTime())));
            }
            if (StringUtils.isNotBlank(d.getBrithDate())) {
                driverNewDTO.setBrithDate(DateUtil.formatDateToString(DateUtil.parseDate(d.getBrithDate())));
            }
            Byte keyByValue = GenderEnum.getKeyByValue(d.getGenderStr());
            if (StringUtils.isNotBlank(d.getOfficeStatusStr())) {
                driverNewDTO.setOfficeStatus(DriverOfficeStatusEnum.getKeyByValue(d.getOfficeStatusStr()));
            }
            //驾驶证号码=身份证号
            driverNewDTO.setLicenseNo(d.getCertNo());
            driverNewDTO.setGender(keyByValue);
            driverNewDTO.setCreateTime(new Date());
            driverNewDTO.setCreterId(batchDriverInsertDTO.getCreateId());
            driverNewDTO.setCreterName(batchDriverInsertDTO.getCreateName());
            driverNewDTO.setCompanyId(batchDriverInsertDTO.getCompanyId());
            driverNewDTO.setCustomerCompanyName(company.getCompanyName());
            driverNewDTO.setCustomerCompanyCode(company.getCustomerCode());
            return driverNewDTO;
        }).collect(Collectors.toList());


    }


    private List<ImportErrorDTO> validateDriverField(List<DriverNewDTO> driverList) {
        List<ImportErrorDTO> errorList = new ArrayList<>();
        Pattern phonePattern = Pattern.compile("^[1]\\d{10}$");

        for (int index = 0; index < driverList.size(); index++) {
            DriverNewDTO driver = driverList.get(index);
            List<String> errors = new ArrayList<>();

            checkFieldNotBlank(driver.getDriverName(), "姓名未填写", errors);
            checkFieldNotNull(driver.getGender(), "性别未填写", errors);
            //三方司机才校验城市
            if (DriverEnums.DriverSourceTypeEnum.lingsan.getCode().equals(driver.getDriverSourceType())) {
                checkFieldNotBlank(driver.getBelongCityName(), "所在城市未填写", errors);
            }
            checkFieldNotBlank(driver.getLicenseType(), "驾照类型未填写", errors);
            checkFieldNotBlank(driver.getLicenseNo(), "驾驶证号未填写", errors);

            checkPhone(driver.getDriverMobile(), phonePattern, errors);
            checkDate(driver.getFirstPickupTime(), "初次领取日期格式不正确", errors);
            checkDate(driver.getArriveTime(), "截止日期格式不正确", errors);

            if (!errors.isEmpty()) {
                ImportErrorDTO supplierDriverImportFailDTO = new ImportErrorDTO();
                supplierDriverImportFailDTO.setReason(String.join(COMMA, errors));
                supplierDriverImportFailDTO.setRowNum(index);
                errorList.add(supplierDriverImportFailDTO);
            }
        }
        return errorList;
    }


    private SupplierDriverImportFailDTO convertDriverToFailDTO(DriverProviderLingSanSaveReqDTO driver, String errors) {
        SupplierDriverImportFailDTO failDto = BeanUtil.copyObject(driver, SupplierDriverImportFailDTO.class);
        failDto.setErrorMsg(errors);
        return failDto;
    }


    private void checkFieldNotBlank(String field, String errorMessage, List<String> errors) {
        if (StringUtils.isBlank(field)) {
            errors.add(errorMessage);
        }
    }

    private void checkFieldNotNull(Object field, String errorMessage, List<String> errors) {
        if (field == null) {
            errors.add(errorMessage);
        }
    }


    private void checkPhone(String phone, Pattern pattern, List<String> errors) {
        if (StringUtils.isBlank(phone)) {
            errors.add("司机电话未填写");
        } else if (!pattern.matcher(phone).matches()) {
            errors.add("司机电话号码格式错误，请重新确认");
        }
    }

    private void checkDate(String date, String errorMessage, List<String> errors) {
        if (StringUtils.isNotBlank(date) && !DateUtil.isLegalDate(10, date, DateUtil.DATE_FORMAT)) {
            errors.add(errorMessage);
        }
    }


    /**
     * 新增或者修改客户企业的自有司机或者三方司机
     *
     * @param driverNewDTO 新增或者编辑传入的司机信息
     * @param b
     * @return 操作成功的时候返回司机的主键
     */
    public RestResponse<?> doAddOrUpdateCustomerOwnDriver(DriverNewDTO driverNewDTO, boolean b) {
        if (driverNewDTO.getDriverSourceType() == null) {
            driverNewDTO.setDriverSourceType(DriverEnums.DriverSourceTypeEnum.zysj.getCode());
        }
        //做一个基本的参数校验
        Byte driverSourceType = driverNewDTO.getDriverSourceType();
        if (DriverEnums.DriverSourceTypeEnum.getByCode(driverSourceType) == null) {
            logger.warn("新增司机传入的司机来源类型不合法，不做处理！");
            return RestResponse.fail(RestErrorCode.HTTP_PARAM_INVALID);
        }
        //预处理一些信息，司机驾驶证不存在的时候，用身份证覆盖一下
        if (StringUtils.isBlank(driverNewDTO.getLicenseNo()) && StringUtils.isNotBlank(driverNewDTO.getCertNo())) {
            driverNewDTO.setLicenseNo(driverNewDTO.getCertNo());
        }
        Driver driver = BeanUtil.copyObject(driverNewDTO, Driver.class);
        driver.setPinyinName(PinYinUtil.getPingYin(driver.getDriverName()));
        RestResponse<?> driverResult;
        DriverToCustomerSyncInfoDTO.DriverStatusChange driverStatusChange;
        if (driverNewDTO.getDriverId() == null || driverNewDTO.getDriverId() == 0) {
            newDriverDeptInfoHandle(driver);
            driverResult = handleNewDriver(driver, b, driverNewDTO);
            driverStatusChange = DriverToCustomerSyncInfoDTO.DriverStatusChange.RESUMPTION;
        } else {
            Driver driverDB = driverExMapper.selectByPrimaryKey(driverNewDTO.getDriverId());
            driverResult = handleExistingDriver(driver, driverDB);
            driverStatusChange = DriverToCustomerSyncInfoDTO.DriverStatusChange.UPDATE;
        }
        if (!driverResult.isSuccess()) {
            //司机操作失败，直接返回错误
            return driverResult;
        }
        DriverToCustomerSyncInfoDTO driverToCustomerSyncInfoDTO = buildDriverToCustomerSyncInfoDTO(driver.getDriverId());
        driverToCustomerSyncInfoDTO.setDriverStatusChange(driverStatusChange);
        RestResponse<?> customerResult = customerManagementService.syncCustomerForDriver(driverToCustomerSyncInfoDTO);
        if (!customerResult.isSuccess()) {
            //抛出异常，回滚事务
            throw new RestErrorException(customerResult.getMsg(), customerResult.getCode());
        }
        //处理完之后，返回新增和修改的司机主键
        return driverResult;
    }


    private DriverNewDTO buildDriverProviderLingSanSave(SupplierDriverSaveDTO supplierDriverSaveDTO) {
        DriverNewDTO driverNewDTO = new DriverNewDTO();
        driverNewDTO.setDriverSourceType(DriverEnums.DriverSourceTypeEnum.lingsan.getCode());
        driverNewDTO.setDriverStatus(DriverEnums.DriverStatusEnum.enable.getCode());
        Integer companyId = supplierDriverSaveDTO.getCompanyId();


        driverNewDTO.setCompanyId(companyId);
        //获取的所属公司的基本信息
        Company company = companyExMapper.selectByPrimaryKey(companyId);
        driverNewDTO.setCustomerCompanyName(company.getCompanyName());
        driverNewDTO.setCustomerCompanyCode(company.getCustomerCode());


        driverNewDTO.setDriverName(supplierDriverSaveDTO.getDriverName());
        driverNewDTO.setDriverMobile(supplierDriverSaveDTO.getDriverMobile());
        driverNewDTO.setFrontImgUrl("");
        driverNewDTO.setBackImgUrl("");
        driverNewDTO.setCertName("");
        driverNewDTO.setCertNo("");
        driverNewDTO.setGender(supplierDriverSaveDTO.getGender());
        driverNewDTO.setAddress("");
        driverNewDTO.setDrivingLicenceImgUrl("");
        driverNewDTO.setLicenseNo(supplierDriverSaveDTO.getLicenseNo());
        driverNewDTO.setLicenseType(supplierDriverSaveDTO.getLicenseType());
        driverNewDTO.setDriverArchivesNo("");
        driverNewDTO.setFirstPickupTime("");
        driverNewDTO.setIssuingOrgan("");
        driverNewDTO.setArriveTime("");
        driverNewDTO.setComment("");
        driverNewDTO.setBelongCityCode(supplierDriverSaveDTO.getCityCode());
        driverNewDTO.setBelongCityName(supplierDriverSaveDTO.getCityName());
        driverNewDTO.setCreterId(supplierDriverSaveDTO.getLoginUserId());
        driverNewDTO.setCreterName(supplierDriverSaveDTO.getLoginUserName());
        driverNewDTO.setUpdateId(supplierDriverSaveDTO.getLoginUserId());
        driverNewDTO.setUpdateName(supplierDriverSaveDTO.getLoginUserName());
        driverNewDTO.setSupplierDriverLine(String.valueOf(SupplierDriverLineEnum.COMMERCIAL_VEHICLES_USE.getCode()));
        return driverNewDTO;
    }

    /**
     * 设置默认部门
     */
    private void newDriverDeptInfoHandle(Driver newDriver) {
        //如果没有设置部门按原逻辑走
        if(newDriver.getStructId()!=null && StringUtils.isNotBlank(newDriver.getStructName())){
            return;
        }
        Customer existingCustomer = customerManagementService.getEnableCustomerByPhone(newDriver.getDriverMobile());
        // 自有企业司机，如果用户信息已存在，司机部门信息需要取默认用户信息上的部门
        if (Objects.nonNull(existingCustomer) && Objects.equals(newDriver.getCompanyId(), existingCustomer.getCompanyId())) {
            newDriver.setStructId(existingCustomer.getStructId());
            newDriver.setStructName(existingCustomer.getStructName());
            return;
        }
        CompanyDepartment defaultStruct = companyDepartmentExMapper.getDefaultStruct(newDriver.getCompanyId());
        if (defaultStruct != null) {
            newDriver.setStructId(defaultStruct.getId());
            newDriver.setStructName(defaultStruct.getDepartmentName());
        }
    }


    /**
     * 处理新增司机（新司机入职）
     *
     * @param newDriver    新增司机信息
     * @param b
     * @param driverNewDTO
     * @return 新增司机是否成功 成功之后 RestResponse.data = driver_id
     */
    private RestResponse<?> handleNewDriver(Driver newDriver, boolean b, DriverNewDTO driverNewDTO) {
        //新增司机的时候，如果没有指定业务线，需要给一个默认的业务线
        if (StringUtils.isBlank(newDriver.getSupplierDriverLine())) {
            DriverEnums.DriverSourceTypeEnum sourceTypeEnum = DriverEnums.DriverSourceTypeEnum.getByCode(newDriver.getDriverSourceType());
            //正常业务范围内，此处不会为空
            if (sourceTypeEnum != null) {
                SupplierDriverLineEnum supplierDriverLineEnum = getSupplierDriverLineEnum(sourceTypeEnum);
                if (supplierDriverLineEnum != null) {
                    newDriver.setSupplierDriverLine(String.valueOf(supplierDriverLineEnum.getCode()));
                }
            }
        }
        Driver existDriverMobile = getExistDriverMobile(newDriver.getDriverMobile());
        if (existDriverMobile != null) {
            Byte driverSourceType = existDriverMobile.getDriverSourceType();
            String valueByCode = DriverEnums.DriverSourceTypeEnum.getValueByCode(driverSourceType);
            if(Objects.equals(driverSourceType, DriverEnums.DriverSourceTypeEnum.zysj.getCode()) ||
                    Objects.equals(driverSourceType, DriverEnums.DriverSourceTypeEnum.swcsj.getCode())){
                if(Objects.equals(driverSourceType,newDriver.getDriverSourceType())){
                    if(Objects.equals(newDriver.getCompanyId(), existDriverMobile.getCompanyId())){
                        //司机手机号已有本企业启用的账号
                        return RestResponse.fail(UserErrorCode.DRIVER_MOBILE_IS_EXIST_IN_STATUS, valueByCode);
                    }
                    //司机手机号已有其他企业下的启用账号，请联系首汽运营核实后重新录入
                    return RestResponse.fail(UserErrorCode.DRIVER_MOBILE_IS_EXIST_OTHER_COMPANY_STATUS, valueByCode);
                }
                //当类型是自有司机，停用该司机
                doStopSelfDriver(existDriverMobile,b,driverNewDTO);
            }else{
                return RestResponse.fail(UserErrorCode.BUSINESS_CAR_DRIVER_USER_NOT_DISABLED, valueByCode);
            }

        }
        //代垫司机首次入职的时候，不存在司机证件号
        if (StringUtils.isNotBlank(newDriver.getLicenseNo()) && isDriverLicenseExist(newDriver.getLicenseNo())  &&
                !Objects.equals(DriverEnums.DriverSourceTypeEnum.zysj.getCode(), newDriver.getDriverSourceType())) {
            return RestResponse.fail(UserErrorCode.DRIVER_LICENSE_EXIST);
        }
        //自有司机 工作状态，性别 默认
        if(Objects.equals(newDriver.getDriverSourceType(), DriverEnums.DriverSourceTypeEnum.zysj.getCode())){
            if(newDriver.getOfficeStatus()==null){
                newDriver.setOfficeStatus(Byte.valueOf("0"));
            }
            if(newDriver.getGender()==null){
                newDriver.setGender(Byte.valueOf("0"));
            }
        }
        driverExMapper.insertSelective(newDriver);
        return RestResponse.success(newDriver.getDriverId());
    }


    private void doStopSelfDriver(Driver existDriverMobile, boolean b, DriverNewDTO driverNewDTO) {
        Driver update = new Driver();
        update.setDriverId(existDriverMobile.getDriverId());
        update.setDriverStatus(DriverEnums.DriverStatusEnum.stop.getCode());
        if (b) {
            update.setUpdateId(driverNewDTO.getCreterId());
            update.setUpdateName(driverNewDTO.getCreterName());
        } else {
            update.setUpdateId(0);
            update.setUpdateName("综合同步代垫司机");
        }
        driverExMapper.updateByPrimaryKeySelective(update);
        Customer activeByPhone = customerExMapper.getActiveByPhone(existDriverMobile.getDriverMobile());
        if (activeByPhone != null) {
            Customer updateCustomer = new Customer();
            updateCustomer.setCustomerId(activeByPhone.getCustomerId());
            updateCustomer.setCustomerStatus(CustomerStatusEnum.DISABLED.getStatus());
            if (b) {
                updateCustomer.setUpdateId(driverNewDTO.getCreterId());
                updateCustomer.setUpdateName(driverNewDTO.getCreterName());
            } else {
                updateCustomer.setUpdateId(0);
                updateCustomer.setUpdateName("综合同步代垫司机");
            }
            customerExMapper.updateByPrimaryKeySelective(updateCustomer);
        }
    }


    /**
     * 根据司机的来源类型设定业务线
     */
    private SupplierDriverLineEnum getSupplierDriverLineEnum(DriverEnums.DriverSourceTypeEnum sourceTypeEnum) {
        switch (sourceTypeEnum) {
            case zysj:
            case sqsj:
                //企业自有司机和首汽代垫司机默认都是给内部用车的业务线
                return SupplierDriverLineEnum.INTERNAL_VEHICLES_USE;
            case swcsj:
                //商务车司机给一个商务车的业务线。商务车司机改版之后，商务车司机只能又代垫司机兼任，其实不存在这种情况
                return SupplierDriverLineEnum.COMMERCIAL_VEHICLES_USE;
            case lingsan:
                //三方司机默认给零散的业务线
                return SupplierDriverLineEnum.LING_SAN_USE;
            default:
                return null;
        }

    }

    /**
     * 处理已经存在的司机（编辑）
     *
     * @param newDriver    传入的司机信息
     * @param oldNewDriver 数据库中的司机信息
     * @return 修改司机是否成功
     */
    private RestResponse<?> handleExistingDriver(Driver newDriver, Driver oldNewDriver) {
        if (null == oldNewDriver) {
            return RestResponse.fail(UserErrorCode.DRIVER_IS_NOT_EXIST);
        }
        Integer driverId = oldNewDriver.getDriverId();
        //司机手机号校验；如果该司机的手机号在司机表中存在，并且不是当前司机ID的，则不允许修改为该手机号
        if (isDriverMobileExist(newDriver.getDriverMobile(), driverId)) {
            return RestResponse.fail(UserErrorCode.DRIVER_MOBILE_EXIST);
        }
        //校验证件号是否被占用
        if (StringUtils.isNotBlank(newDriver.getLicenseNo()) && isDriverLicenseExist(newDriver.getLicenseNo(), driverId) &&
        !Objects.equals(DriverEnums.DriverSourceTypeEnum.zysj.getCode(), newDriver.getDriverSourceType())) {
            return RestResponse.fail(UserErrorCode.DRIVER_LICENSE_EXIST);
        }
        //自有司机 工作状态，性别 默认
        if(Objects.equals(newDriver.getDriverSourceType(), DriverEnums.DriverSourceTypeEnum.zysj.getCode())){
            if(newDriver.getOfficeStatus()==null){
                newDriver.setOfficeStatus(Byte.valueOf("0"));
            }
            if(newDriver.getGender()==null){
                newDriver.setGender(Byte.valueOf("0"));
            }
        }
        newDriver.setDriverId(driverId);
        driverExMapper.updateByPrimaryKeySelective(newDriver);
        return RestResponse.success(driverId);
    }


    /**
     * 发送司机修改的mq，通知customer和driver修改用户信息
     *
     * @param driver       司机信息
     * @param statusChange 司机状态
     */
    private void notifyDriverStatusChange(Driver driver, DriverToCustomerSyncInfoDTO.DriverStatusChange statusChange) {
        DriverToCustomerSyncInfoDTO driverToCustomerSyncInfoDTO = buildDriverToCustomerSyncInfoDTO(driver.getDriverId());
        driverToCustomerSyncInfoDTO.setDriverStatusChange(statusChange);
        producer.publishMessage(RocketMqConst.SQ_DRIVER_TOPIC, RocketMqConst.SQ_DRIVER_TAG, driver.getDriverId().toString(), JSONObject.toJSONString(driverToCustomerSyncInfoDTO));
    }


    /**
     * 区分司机操作
     * 判断操作是 司机入职、司机停职、
     *
     * @param oldDriver 数据库存储的司机信息
     * @param newDriver 新增或者编辑传入的司机信息
     * @return 司机操作
     */
    private DriverToCustomerSyncInfoDTO.DriverStatusChange getDriverStatusChange(Driver oldDriver, Driver newDriver) {
        //老的数据不存在，说明是新司机入职
        if (oldDriver == null) {
            return DriverToCustomerSyncInfoDTO.DriverStatusChange.RESUMPTION;
        } else if (DriverEnums.DriverStatusEnum.enable.getCode().equals(oldDriver.getDriverStatus())
                && DriverEnums.DriverStatusEnum.stop.getCode().equals(newDriver.getDriverStatus())) {
            //司机停职，或者主动离职
            return DriverToCustomerSyncInfoDTO.DriverStatusChange.SUSPENSION;
        } else if (DriverEnums.DriverStatusEnum.stop.getCode().equals(oldDriver.getDriverStatus())
                && DriverEnums.DriverStatusEnum.enable.getCode().equals(newDriver.getDriverStatus())) {
            //司机重新入职，或者叫司机启用
            return DriverToCustomerSyncInfoDTO.DriverStatusChange.RECONTRACT;
        } else {
            //编辑司机信息
            return DriverToCustomerSyncInfoDTO.DriverStatusChange.UPDATE;
        }
    }


    /**
     * 封装一些司机创建用户的基本信息，不包括司机操作
     *
     * @param driverId driverId
     * @return 封装的司机创建用户的基本信息
     */
    private DriverToCustomerSyncInfoDTO buildDriverToCustomerSyncInfoDTO(Integer driverId) {
        //重新获取一下司机信息
        Driver driverDB = driverExMapper.selectByPrimaryKey(driverId);
        DriverToCustomerSyncInfoDTO driverToCustomerSyncInfoDTO = new DriverToCustomerSyncInfoDTO();
        driverToCustomerSyncInfoDTO.setDriverId(driverDB.getDriverId());
        driverToCustomerSyncInfoDTO.setDriverMobile(driverDB.getDriverMobile());
        driverToCustomerSyncInfoDTO.setDriverName(driverDB.getDriverName());
        driverToCustomerSyncInfoDTO.setDriverEmail(driverDB.getDriverEmail());
        driverToCustomerSyncInfoDTO.setHeadIcon(driverDB.getHeadIcon());
        driverToCustomerSyncInfoDTO.setCompanyId(driverDB.getCompanyId());
        driverToCustomerSyncInfoDTO.setCreateId(driverDB.getCreterId());
        driverToCustomerSyncInfoDTO.setCreateName(driverDB.getCreterName());
        driverToCustomerSyncInfoDTO.setUpdateId(driverDB.getUpdateId());
        driverToCustomerSyncInfoDTO.setUpdateName(driverDB.getUpdateName());
        driverToCustomerSyncInfoDTO.setIdCardNum(driverDB.getCertNo());
        driverToCustomerSyncInfoDTO.setGender(driverDB.getGender());
        DriverEnums.DriverSourceTypeEnum byCode = DriverEnums.DriverSourceTypeEnum.getByCode(driverDB.getDriverSourceType());
        driverToCustomerSyncInfoDTO.setDriverSourceType(byCode);
        driverToCustomerSyncInfoDTO.setStructId(driverDB.getStructId());
        driverToCustomerSyncInfoDTO.setStructName(driverDB.getStructName());
        return driverToCustomerSyncInfoDTO;
    }


    /**
     * 司机编辑时，判断手机号是否有重复
     *
     * @param driverMobile 司机手机号
     * @param driverId     被编辑的司机ID
     * @return true:存在，false:不存在
     */
    private boolean isDriverMobileExist(String driverMobile, Integer driverId) {
        DriverExample driverExample = new DriverExample();
        driverExample.createCriteria().andDriverMobileEqualTo(driverMobile).andDriverStatusEqualTo(DriverEnums.DriverStatusEnum.enable.getCode()).andDriverIdNotEqualTo(driverId);
        return driverExMapper.countByExample(driverExample) > 0;
    }

    /**
     * 获取手机号重复的司机
     */
    private Driver getExistDriverMobile(String driverMobile, Integer driverId) {
        DriverExample driverExample = new DriverExample();
        driverExample.createCriteria().andDriverMobileEqualTo(driverMobile).andDriverStatusEqualTo(DriverEnums.DriverStatusEnum.enable.getCode()).andDriverIdNotEqualTo(driverId);
        List<Driver> driverList = driverExMapper.selectByExample(driverExample);
        if (!driverList.isEmpty()) {
            return driverList.get(0);
        }
        return null;
    }

    /**
     * 获取手机号重复的司机
     */
    private Driver getExistDriverMobile(String driverMobile) {
        DriverExample driverExample = new DriverExample();
        driverExample.createCriteria().andDriverMobileEqualTo(driverMobile)
                .andDriverStatusEqualTo(DriverEnums.DriverStatusEnum.enable.getCode());
        List<Driver> driverList = driverExMapper.selectByExample(driverExample);
        if (!driverList.isEmpty()) {
            return driverList.get(0);
        }
        return null;
    }


    /**
     * 判断某个手机号是否存在有效的司机信息
     *
     * @param driverMobile 司机手机号
     * @return true:存在，false:不存在
     */
    public boolean isDriverMobileExist(String driverMobile) {
        DriverExample driverExample = new DriverExample();
        driverExample.createCriteria().andDriverMobileEqualTo(driverMobile).andDriverStatusEqualTo(DriverEnums.DriverStatusEnum.enable.getCode());
        return driverExMapper.countByExample(driverExample) > 0;
    }

    /**
     * 判断司机驾驶证信息是否存在
     */
    private boolean isDriverLicenseExist(String driverLicense) {
        DriverExample enableExample = new DriverExample();
        enableExample.createCriteria()
                .andDriverStatusEqualTo(DriverEnums.DriverStatusEnum.enable.getCode())
                .andLicenseNoEqualTo(driverLicense);
        return driverExMapper.countByExample(enableExample) > 0;
    }

    /**
     * 判断司机驾驶证信息是否存在 -编辑使用
     */
    private boolean isDriverLicenseExist(String driverLicense, Integer driverId) {
        DriverExample enableExample = new DriverExample();
        enableExample.createCriteria()
                .andLicenseNoEqualTo(driverLicense)
                .andDriverStatusEqualTo(DriverEnums.DriverStatusEnum.enable.getCode())
                .andDriverIdNotEqualTo(driverId);
        return driverExMapper.countByExample(enableExample) > 0;
    }


    /**
     * 根据综合同步过来的首汽代垫司机参数创建司机对象
     *
     * @param sendMqDTO 首汽代垫司机参数
     * @return 司机对象
     */
    private Driver convertDriverDTO(DriverSendMqDTO sendMqDTO) {
        Driver newDriver = new Driver();
        newDriver.setLeaseDriverId(sendMqDTO.getDriverId());
        newDriver.setDriverName(sendMqDTO.getDriverName());
        //司机性别，两边都是 1:男 2：女
        newDriver.setGender(sendMqDTO.getGender());
        //存储一下司机头像
        newDriver.setHeadIcon(sendMqDTO.getUserPhotosImgUrl());
        //司机身份证号码
        newDriver.setCertNo(sendMqDTO.getIdCardNo());
        //驾驶证号=身份证号
        newDriver.setLicenseNo(sendMqDTO.getIdCardNo());
        //司机出生日期
        newDriver.setBrithDate(sendMqDTO.getBrithDate());
        //司机手机号
        newDriver.setDriverMobile(sendMqDTO.getDriverMobile());
        newDriver.setDriverEmail(sendMqDTO.getDriverEmail());
        newDriver.setAddress(sendMqDTO.getAddress());
        //政治面貌
        newDriver.setPoliticalStatus(sendMqDTO.getPoliticalStatus());
        //文化程度
        newDriver.setEducationStatus(sendMqDTO.getEducationStatus());
        //入职时间
        newDriver.setEntryDate(sendMqDTO.getEntryDate());
        //驾驶证照片地址
        newDriver.setDrivingLicenceImgUrl(sendMqDTO.getDrivingLicenceImgUrl());
        //运营组织机构编码
        newDriver.setBelongStructCode(sendMqDTO.getBelongStructCode());
        //运营组织机构名称
        newDriver.setBelongStructName(sendMqDTO.getBelongStructName());
        //驾照档案编号
        newDriver.setDriverArchivesNo(sendMqDTO.getDriverArchivesNo());
        //准驾车型
        if (sendMqDTO.getDriverType() != null) {
            newDriver.setLicenseType(DriverTypeEnum.getNameByValue(sendMqDTO.getDriverType()));
        }
        //初次领证日期；格式yyyy-MM-dd
        newDriver.setFirstPickupTime(sendMqDTO.getFirstPickupTime());
        //增加A证日期
        newDriver.setDriverADate(sendMqDTO.getDriverADate());
        //发证机关
        newDriver.setIssuingOrgan(sendMqDTO.getIssuingOrgan());
        //驾照到期日
        newDriver.setArriveTime(sendMqDTO.getArriveTime());
        //劳务公司ID(Crm供应商列表的ID)
        newDriver.setLaborCompanyId(sendMqDTO.getLaborCompanyId());
        newDriver.setLaborCompanyName(sendMqDTO.getLaborCompanyName());
        //司机任职状态
        newDriver.setOfficeStatus(sendMqDTO.getDriverStatus());
        //备注
        newDriver.setComment(sendMqDTO.getComment());
        //创建人ID
        newDriver.setCreterId(sendMqDTO.getCreaterId());
        //创建人
        newDriver.setCreterName(sendMqDTO.getCreateName());
        //修改人
        newDriver.setUpdateId(sendMqDTO.getUpdateId());
        newDriver.setUpdateName(sendMqDTO.getUpdateName());

        newDriver.setPinyinName(PinYinUtil.getPingYin(sendMqDTO.getDriverName()));
        //限定一下是首汽代垫司机
        newDriver.setDriverSourceType(DriverEnums.DriverSourceTypeEnum.sqsj.getCode());
        //创建时间
        if (StringUtils.isNotBlank(sendMqDTO.getCreateTime())) {
            newDriver.setCreateTime(DateUtils.StringToDate(sendMqDTO.getCreateTime(), DateUtils.YYYY_MM_DD_HH_mm_DD));
        }

        //根据【司机任职状态】设置司机是否可用
        if (Objects.equals(sendMqDTO.getFirstEntryStatus(), false)) {
            //司机再次启用，设置司机状态为可用
            newDriver.setDriverStatus(DriverEnums.DriverStatusEnum.enable.getCode());
        } else if (sendMqDTO.getDriverStatus() != null && (sendMqDTO.getDriverStatus() == 4 || sendMqDTO.getDriverStatus() == 5)) {
            //司机离职或者被停职 司机变为不可用
            newDriver.setDriverStatus(DriverEnums.DriverStatusEnum.stop.getCode());
        } else {
            newDriver.setDriverStatus(DriverEnums.DriverStatusEnum.enable.getCode());
        }
        //司机类型
        newDriver.setDriverType(DriverEnums.DriverTypeEnum.inner.getCode());
        //更新人ID
        newDriver.setUpdateId(sendMqDTO.getUpdateId());
        //更新人
        newDriver.setUpdateName(sendMqDTO.getUpdateName());
        //更新时间
        if (sendMqDTO.getUpdateTime() != null) {
            newDriver.setUpdateTime(DateUtils.getDayDate(sendMqDTO.getUpdateTime()));
        }
        return newDriver;
    }


    private void dealBelongCity(DriverNewDTO newDriver) {
        //所属城市相关
        // 根据所属城市code查询对应的车队是否存在
        Motorcade motorcade = motorcadeExMapper.selectMotorcadeByBelongCityCode(newDriver.getBelongCityCode(), newDriver.getCompanyId());
        //如果车队不存在
        if (motorcade == null) {
            //新建车队
            Motorcade motorcadeNew = new Motorcade();
            motorcadeNew.setMotorcadeName(newDriver.getBelongCityName() + "车队");
            //motorcadeNew.setAliasName(aliasName);  //车队简称暂时不要了
            motorcadeNew.setBelongCityCode(Integer.parseInt(newDriver.getBelongCityCode()));
            motorcadeNew.setBelongCityName(newDriver.getBelongCityName());
            motorcadeNew.setLeaderName(newDriver.getDriverName());
            motorcadeNew.setLeaderId(newDriver.getDriverId());
            motorcadeNew.setLeaderMobile(newDriver.getDriverMobile());
            motorcadeNew.setEmergencyName(newDriver.getDriverName());
            motorcadeNew.setEmergencyMobile(newDriver.getDriverMobile());
            motorcadeNew.setMotorcadeStatus(NormalStatus.STATUS_NORMAL.getValue());
            motorcadeNew.setSchduleStatus((int) MotorcadeEnums.SchduleStatusEnum.DISABLE.getStatus());
            motorcadeNew.setCompanyId(newDriver.getCompanyId());
            motorcadeNew.setCreateId(newDriver.getCreterId());
            motorcadeNew.setCreateName(newDriver.getCreterName());
            motorcadeNew.setCreateTime(new Date());
            motorcadeNew.setDriverCount(0);
            motorcadeExMapper.insertSelectiveDTO(motorcadeNew);
            motorcade = new Motorcade();
            BeanUtil.copyProperties(motorcadeNew, motorcade);
        }
        //查询原来的司机所在车队
        MotorcadeDriver motorcadeDriver = motorcadeDriverExMapper.queryListByDriverId(newDriver.getCompanyId(), newDriver.getDriverId());
        //如果原来车队mapping表为空，则新增一条 motorcade_driver
        if (motorcadeDriver == null) {
            motorcadeDriver = new MotorcadeDriver();
            motorcadeDriver.setDriverId(newDriver.getDriverId());
            motorcadeDriver.setMotorcadeId(motorcade.getMotorcadeId());
            motorcadeDriver.setCompanyId(newDriver.getCompanyId());
            motorcadeDriver.setStatus(NormalStatus.STATUS_NORMAL.getValue());
            motorcadeDriver.setCreateTime(new Date());
            motorcadeDriverExMapper.insertSelective(motorcadeDriver);
            //将原来的车队司机数量维护+1
            Motorcade motorcadeUpdate = new Motorcade();
            motorcadeUpdate.setMotorcadeId(motorcade.getMotorcadeId());
            motorcadeUpdate.setDriverCount(motorcade.getDriverCount() + 1);
            motorcadeExMapper.updateByPrimaryKeySelective(motorcadeUpdate);
            //不为空的情况，需要查看该条记录对应的车队是否为本次修改的车队
        } else {
            Integer motorcadeIdOld = motorcadeDriver.getMotorcadeId();
            Integer motorcadeIdNew = motorcade.getMotorcadeId();
            //新旧不一，证明司机换车队了
            if (!Objects.equals(motorcadeIdOld, motorcadeIdNew)) {
                //将原来的mapping表中的记录设置为删除态
                MotorcadeDriver motorcadeDriverUpdate = new MotorcadeDriver();
                motorcadeDriverUpdate.setId(motorcadeDriver.getId());
                motorcadeDriverUpdate.setStatus(NormalStatus.STATUS_DELETE.getValue());
                motorcadeDriverUpdate.setDeleteTime(new Date());
                motorcadeDriverExMapper.updateByPrimaryKeySelective(motorcadeDriverUpdate);
                //将原来的车队数量-1
                Motorcade motorcadeUpdate = new Motorcade();
                motorcadeUpdate.setMotorcadeId(motorcadeIdOld);
                MotorcadeDTO motorcadeById = motorcadeExMapper.getMotorcadeBymotorcadeId(motorcadeIdOld);
                motorcadeUpdate.setDriverCount(motorcadeById.getDriverCount() - 1);
                motorcadeExMapper.updateByPrimaryKeySelective(motorcadeUpdate);
                //新增一条新的
                motorcadeDriver = new MotorcadeDriver();
                motorcadeDriver.setDriverId(newDriver.getDriverId());
                motorcadeDriver.setMotorcadeId(motorcadeIdNew);
                motorcadeDriver.setCompanyId(newDriver.getCompanyId());
                motorcadeDriver.setStatus(NormalStatus.STATUS_NORMAL.getValue());
                motorcadeDriver.setCreateTime(new Date());
                motorcadeDriverExMapper.insertSelective(motorcadeDriver);
                //将原来的车队司机数量维护+1
                motorcadeUpdate = new Motorcade();
                motorcadeUpdate.setMotorcadeId(motorcade.getMotorcadeId());
                motorcadeUpdate.setDriverCount(motorcade.getDriverCount() + 1);
                motorcadeExMapper.updateByPrimaryKeySelective(motorcadeUpdate);
            }
        }
    }


}
