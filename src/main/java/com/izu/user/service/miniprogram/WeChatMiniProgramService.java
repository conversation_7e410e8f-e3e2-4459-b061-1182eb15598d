package com.izu.user.service.miniprogram;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.izu.business.config.BusinessRestLocator;
import com.izu.business.consts.clue.ClueSourceEnum;
import com.izu.business.consts.clue.ClueSourcePageEnum;
import com.izu.business.consts.resturl.MrcarBusinessRestMsgCenter;
import com.izu.business.dto.clue.ClueMrcarWebSaveDTO;
import com.izu.framework.cache.RedisSentinelJSONableCache;
import com.izu.framework.rocketmq.CommonRocketProducer;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.user.dto.CompanyInfoDTO;
import com.izu.user.dto.miniprogram.*;
import com.izu.user.entity.BaiduLocation;
import com.izu.user.entity.Customer;
import com.izu.user.entity.ProviderStaff;
import com.izu.user.entity.WeChatMiniProgramUser;
import com.izu.user.entity.mongo.WeChatMiniProgramLoginRecord;
import com.izu.user.utils.miniprogram.WeChatMiniProgramClient;
import com.izu.user.utils.miniprogram.entity.MiniProgramLoginSessionResponse;
import com.izu.user.utils.miniprogram.entity.MiniProgramPhoneNumberResponse;
import mapper.ex.CompanyExMapper;
import mapper.ex.CustomerExMapper;
import mapper.ex.ProviderStaffExMapper;
import mapper.ex.WeChatMiniProgramUserExMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.izu.user.config.errcode.UserErrorCode.WECHAT_MINIPROGRAM_DUPLICATED_BIND;

/**
 * 微信小程序Service类.
 *
 * <AUTHOR>
 * @date 2024-03-04
 */
@Service
public class WeChatMiniProgramService {

    public static final String REDIS_USER_SESSION_KEY = "mrcar:user:miniprogram:session:%s";

    public static final String BAIDU_API_REVERSE_GEOCODING_URL = "http://api.map.baidu.com/reverse_geocoding/v3/?ak=%s&output=json&coordtype=gcj02&location=%s";

    // session默认有效期7天
    public static final long REDIS_USER_SESSION_TIMEOUT = TimeUnit.DAYS.toSeconds(30);

    @Autowired
    private WeChatMiniProgramClient client;
    @Autowired
    private WeChatMiniProgramUserExMapper userMapper;
    @Autowired
    private ProviderStaffExMapper providerMapper;
    @Autowired
    private CustomerExMapper customerMapper;
    @Autowired
    private CompanyExMapper companyMapper;

    @Resource(name = "mrcarUserCoreRedisCache")
    private RedisSentinelJSONableCache redisCache;
    @Autowired
    private CommonRocketProducer mqProducer;
    @NacosValue(value = "${baidu.map.ak}", autoRefreshed = true)
    private String baiduAk;
    @Autowired
    private MongoTemplate mongoTemplate;

    /**
     * 用户登录
     */
    @SuppressWarnings("unchecked")
    @Transactional
    public RestResponse<String> login(
            WeChatMiniProgramUserLoginReqDTO reqDTO) throws IOException {
        // 小程序登录
        MiniProgramLoginSessionResponse response1 = this.client.code2Session(reqDTO.getLoginCode());
        if (response1.getErrcode() != 0) {
            return RestResponse.fail(response1.getErrcode(), response1.getErrmsg());
        }
        String openid = response1.getOpenid();
        String unionid = response1.getUnionid();
        String sessionKey = response1.getSessionKey();
        // 获取手机号
        MiniProgramPhoneNumberResponse response2 = this.client.getPhoneNumber(reqDTO.getPhoneCode(), openid);
        if (response2.getErrcode() != 0) {
            return RestResponse.fail(response2.getErrcode(), response2.getErrmsg());
        }
        String phone = response2.getPhoneInfo().getPhoneNumber();
        // 保存登录上下文信息
        String token = generateSessionToken();
        // 创建用户的session信息
        WeChatMiniProgramSession session = new WeChatMiniProgramSession();
        session.setToken(token);
        session.setSessionKey(sessionKey);
        session.setUnionid(unionid);
        session.setOpenid(openid);
        // 查询用户信息
        List<WeChatMiniProgramUser> users = userMapper.selectByPhone(phone);
        WeChatMiniProgramUser user = null;
        if (users.isEmpty()) {
            // 该手机号没有绑定过该微信号
            user = this.register(openid, unionid, phone);
        } else {
            boolean valid = users.stream().allMatch(s -> Objects.equals(s.getOpenid(), openid));
            if (!valid) {
                // 该手机号被绑定过其他微信号
                return RestResponse.fail(WECHAT_MINIPROGRAM_DUPLICATED_BIND);
            }
            user = users.stream().filter(s -> Objects.equals(s.getOpenid(), openid)).findFirst().get();
        }
        // 设置登录信息
        session.setUserType(user.getUserType());
        session.setUserId(user.getId());
        session.setUserName(user.getUserName());
        session.setUserCode(user.getUserCode());
        session.setUserMobile(user.getUserMobile());
        session.setLoginType((byte) 1); // 快捷登录
        session.setLoginTime(new Date());
        session.setLoginLatitude(reqDTO.getLatitude());
        session.setLoginLongitude(reqDTO.getLongitude());
        this.setSessionContext(session);
        // 记录登录日志
        this.triggerLoginEvent(session);
        return RestResponse.success(token);
    }

    /**
     * 分页查询用户信息
     */
    public PageDTO<WeChatMiniProgramUserRespDTO> pageSearchUser(WeChatMiniProgramUserSearchReqDTO reqDTO) {
        Page<Object> page = PageHelper.startPage(reqDTO.getPage(), reqDTO.getPageSize());
        List<WeChatMiniProgramUser> users = userMapper.pageSearch(reqDTO);
        List<WeChatMiniProgramUserRespDTO> list =
                users.stream().map(s -> {
                    WeChatMiniProgramUserRespDTO dto = new WeChatMiniProgramUserRespDTO();
                    BeanUtils.copyProperties(s, dto);
                    dto.setBindStatusStr(s.getBindStatus() == 1 ? "未绑定" : "已绑定");
                    dto.setCardStatusStr(s.getCardStatus() == 1 ? "未创建" : "已创建");
                    if (s.getLastLoginType() != null) {
                        dto.setLastLoginTypeStr(s.getLastLoginType() == 1 ? "微信快捷登录" : "-");
                    }
                    return dto;
                }).collect(Collectors.toList());
        PageDTO<WeChatMiniProgramUserRespDTO> result = new PageDTO<>();
        result.setPage(page.getPageNum());
        result.setPageSize(page.getPageSize());
        result.setTotal(page.getTotal());
        result.setResult(list);
        return result;
    }

    public WeChatMiniProgramUserRespDTO getUserById(Long id) {
        WeChatMiniProgramUser user = userMapper.selectByPrimaryKey(id);
        WeChatMiniProgramUserRespDTO dto = new WeChatMiniProgramUserRespDTO();
        BeanUtils.copyProperties(user, dto);
        dto.setBindStatusStr(user.getBindStatus() == 1 ? "未绑定" : "已绑定");
        dto.setCardStatusStr(user.getCardStatus() == 1 ? "未创建" : "已创建");
        if (user.getLastLoginType() != null) {
            dto.setLastLoginTypeStr(user.getLastLoginType() == 1 ? "微信快捷登录" : "-");
        }
        return dto;
    }

    /**
     * 分页查询登录日志
     */
    public PageDTO<WeChatMiniProgramLoginLogRespDTO> pageSearchLog(WeChatMiniProgramLoginLogReqDTO reqDTO) {
        Query query = new Query()
                .addCriteria(Criteria.where("userId").is(reqDTO.getUserId()))
                .addCriteria(Criteria.where("loginTime").gte(reqDTO.getLoginTimeStart()).lte(reqDTO.getLoginTimeEnd()));
        long total = mongoTemplate.count(query, WeChatMiniProgramLoginRecord.class);
        query.skip((long) (reqDTO.getPage() - 1) * reqDTO.getPageSize()).limit(reqDTO.getPageSize());
        List<WeChatMiniProgramLoginRecord> records = mongoTemplate.find(query, WeChatMiniProgramLoginRecord.class);
        List<WeChatMiniProgramLoginLogRespDTO> list =
                records.stream().map(s -> {
                    WeChatMiniProgramLoginLogRespDTO dto = new WeChatMiniProgramLoginLogRespDTO();
                    BeanUtils.copyProperties(s, dto);
                    if (s.getLoginType() != null) {
                        dto.setLoginTypeStr(s.getLoginType() == 1 ? "微信快捷登录" : "-");
                    }
                    return dto;
                }).collect(Collectors.toList());
        PageDTO<WeChatMiniProgramLoginLogRespDTO> result = new PageDTO<>();
        result.setPage(reqDTO.getPageNum());
        result.setPageSize(reqDTO.getPageSize());
        result.setTotal(total);
        result.setResult(list);
        return result;
    }

    /**
     * 用户注册
     */
    public WeChatMiniProgramUser register(String openid, String unionid, String phone) throws IOException {
        // 按顺序查找是否是内部用户
        WeChatMiniProgramUser user = new WeChatMiniProgramUser();
        boolean matched = false;
        List<ProviderStaff> staffs = this.providerMapper.getActiveByPhone(phone);
        if (staffs != null && !staffs.isEmpty()) {
            ProviderStaff staff = staffs.get(0);
            user.setUserName(staff.getStaffName()); // 用户姓名
            user.setUserCode(staff.getProviderStaffCode()); // 用户内部编码
            user.setUserType((byte) 1);
            user.setCompanyCode(staff.getProviderCode()); // 公司名称
            user.setCompanyName(staff.getProviderName()); // 公司编码
            matched = true;
        }
        if (!matched) {
            Customer customer = this.customerMapper.getActiveByPhone(phone);
            if (customer != null) {
                user.setUserName(customer.getCustomerName()); // 用户姓名
                user.setUserCode(customer.getCustomerCode()); // 用户内部编码
                user.setUserType((byte) 2);
                user.setCompanyCode(customer.getCompanyCode()); // 公司名称
                if (StringUtils.isNotBlank(customer.getCompanyCode())) {
                    CompanyInfoDTO company = companyMapper.getCompanyByCode(customer.getCompanyCode());
                    if (company != null) {
                        user.setCompanyName(company.getCompanyName());
                    }
                }
                matched = true;
            }
        }
        if (!matched) {
            user.setUserName(phone);
            user.setUserType((byte) 3);
            user.setCompanyCode("0");
            user.setCompanyName("Mr.Car个人用户");
        }
        user.setUserMobile(phone);
        user.setBindStatus((byte) 2); // 绑定状态
        user.setOpenid(openid);
        user.setUnionid(unionid);
        user.setCardStatus((byte) 1);
        user.setCreateTime(new Date());
        user.setUpdateTime(new Date());
        // 创建用户
        this.userMapper.insertSelective(user);
        return user;
    }

    @Transactional
    public void onLoginEvent(WeChatMiniProgramLoginEvent event) {
        // 更新记录
        WeChatMiniProgramUser user = userMapper.selectByPrimaryKey(event.getUserId());
        if (user == null) {
            return;
        }
        user.setLastLoginTime(event.getLoginTime());
        user.setLastLoginType(event.getLoginType());
        String address = this.translateToBaiduAddress(event.getLoginLatitude(), event.getLoginLongitude());
        user.setLastLoginAddress(address);
        this.userMapper.updateByPrimaryKeySelective(user);
        // 记录操作日志
        WeChatMiniProgramLoginRecord record = new WeChatMiniProgramLoginRecord();
        record.setLoginType(event.getLoginType());
        record.setLoginTime(event.getLoginTime());
        record.setMobilePhone(event.getMobilePhone());
        record.setOpenid(event.getOpenid());
        record.setLoginAddress(address);
        record.setUserId(event.getUserId());
        this.mongoTemplate.insert(record);
    }

    public void setSessionContext(WeChatMiniProgramSession session) {
        String token = session.getToken();
        this.redisCache.set(formatSessionKey(token), session, (int) REDIS_USER_SESSION_TIMEOUT);
    }

    public WeChatMiniProgramSession getSessionContext(String token) {
        return this.redisCache.get(formatSessionKey(token), WeChatMiniProgramSession.class);
    }

    // 增加新的
    private void addNewClue(WeChatMiniProgramUser user, WeChatMiniProgramUserLoginReqDTO reqDTO) {
        if (user.getUserType() == 3) {
            ClueMrcarWebSaveDTO param = new ClueMrcarWebSaveDTO();
            param.setCompanyName("Mr.Car个人用户");
            param.setClueSource(ClueSourceEnum.SQ_MINI.getCode());
            param.setContactsName(user.getUserMobile());
            param.setContactsMobile(user.getUserMobile());
            param.setClueSourcePageCode((byte)0);
            param.setClueSourceIp(reqDTO.getIpAddress());

            String url = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.CLUE_MRCAR_WEB_SAVE);
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
            RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null,Boolean.class);

        }
    }

    // 生成系统的登录态token信息
    private String generateSessionToken() {
        return UUID.randomUUID().toString().replace("-", "");
    }

    private String formatSessionKey(String token) {
        return String.format(REDIS_USER_SESSION_KEY, token);
    }

    private void triggerLoginEvent(WeChatMiniProgramSession session) {
        WeChatMiniProgramLoginEvent event = new WeChatMiniProgramLoginEvent();
        event.setUnionid(session.getUnionid());
        event.setOpenid(session.getOpenid());
        event.setUserId(session.getUserId());
        event.setMobilePhone(session.getUserMobile());
        event.setLoginType(session.getLoginType());
        event.setLoginTime(session.getLoginTime());
        event.setLoginLongitude(session.getLoginLongitude());
        event.setLoginLatitude(session.getLoginLatitude());
        mqProducer.publishMessage("mrcar_wechat_miniprogram_login_event",
                null,
                event.getOpenid(),
                event);
    }

    //
    private String translateToBaiduAddress(BigDecimal latitude, BigDecimal longitude){
        if (latitude == null || longitude == null) {
            return "未获取";
        }
        String location = latitude.toPlainString() + "," + longitude.toPlainString();
        String restUrl = String.format(BAIDU_API_REVERSE_GEOCODING_URL, baiduAk, location);
        BaiduLocation result = RestClient.requestThirdApi(BaseHttpClient.HttpMethod.GET, restUrl, null, null, BaiduLocation.class);
        return result.getResult().getFormattedAddress();
    }

}
