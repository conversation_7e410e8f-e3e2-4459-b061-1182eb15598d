package com.izu.user.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class UserAuthInfoExample {
    /**
     * user_auth_info
     */
    protected String orderByClause;

    /**
     * user_auth_info
     */
    protected boolean distinct;

    /**
     * user_auth_info
     */
    protected List<Criteria> oredCriteria;

    public UserAuthInfoExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andUserMobileIsNull() {
            addCriterion("user_mobile is null");
            return (Criteria) this;
        }

        public Criteria andUserMobileIsNotNull() {
            addCriterion("user_mobile is not null");
            return (Criteria) this;
        }

        public Criteria andUserMobileEqualTo(String value) {
            addCriterion("user_mobile =", value, "userMobile");
            return (Criteria) this;
        }

        public Criteria andUserMobileNotEqualTo(String value) {
            addCriterion("user_mobile <>", value, "userMobile");
            return (Criteria) this;
        }

        public Criteria andUserMobileGreaterThan(String value) {
            addCriterion("user_mobile >", value, "userMobile");
            return (Criteria) this;
        }

        public Criteria andUserMobileGreaterThanOrEqualTo(String value) {
            addCriterion("user_mobile >=", value, "userMobile");
            return (Criteria) this;
        }

        public Criteria andUserMobileLessThan(String value) {
            addCriterion("user_mobile <", value, "userMobile");
            return (Criteria) this;
        }

        public Criteria andUserMobileLessThanOrEqualTo(String value) {
            addCriterion("user_mobile <=", value, "userMobile");
            return (Criteria) this;
        }

        public Criteria andUserMobileLike(String value) {
            addCriterion("user_mobile like", value, "userMobile");
            return (Criteria) this;
        }

        public Criteria andUserMobileNotLike(String value) {
            addCriterion("user_mobile not like", value, "userMobile");
            return (Criteria) this;
        }

        public Criteria andUserMobileIn(List<String> values) {
            addCriterion("user_mobile in", values, "userMobile");
            return (Criteria) this;
        }

        public Criteria andUserMobileNotIn(List<String> values) {
            addCriterion("user_mobile not in", values, "userMobile");
            return (Criteria) this;
        }

        public Criteria andUserMobileBetween(String value1, String value2) {
            addCriterion("user_mobile between", value1, value2, "userMobile");
            return (Criteria) this;
        }

        public Criteria andUserMobileNotBetween(String value1, String value2) {
            addCriterion("user_mobile not between", value1, value2, "userMobile");
            return (Criteria) this;
        }

        public Criteria andCertNameIsNull() {
            addCriterion("cert_name is null");
            return (Criteria) this;
        }

        public Criteria andCertNameIsNotNull() {
            addCriterion("cert_name is not null");
            return (Criteria) this;
        }

        public Criteria andCertNameEqualTo(String value) {
            addCriterion("cert_name =", value, "certName");
            return (Criteria) this;
        }

        public Criteria andCertNameNotEqualTo(String value) {
            addCriterion("cert_name <>", value, "certName");
            return (Criteria) this;
        }

        public Criteria andCertNameGreaterThan(String value) {
            addCriterion("cert_name >", value, "certName");
            return (Criteria) this;
        }

        public Criteria andCertNameGreaterThanOrEqualTo(String value) {
            addCriterion("cert_name >=", value, "certName");
            return (Criteria) this;
        }

        public Criteria andCertNameLessThan(String value) {
            addCriterion("cert_name <", value, "certName");
            return (Criteria) this;
        }

        public Criteria andCertNameLessThanOrEqualTo(String value) {
            addCriterion("cert_name <=", value, "certName");
            return (Criteria) this;
        }

        public Criteria andCertNameLike(String value) {
            addCriterion("cert_name like", value, "certName");
            return (Criteria) this;
        }

        public Criteria andCertNameNotLike(String value) {
            addCriterion("cert_name not like", value, "certName");
            return (Criteria) this;
        }

        public Criteria andCertNameIn(List<String> values) {
            addCriterion("cert_name in", values, "certName");
            return (Criteria) this;
        }

        public Criteria andCertNameNotIn(List<String> values) {
            addCriterion("cert_name not in", values, "certName");
            return (Criteria) this;
        }

        public Criteria andCertNameBetween(String value1, String value2) {
            addCriterion("cert_name between", value1, value2, "certName");
            return (Criteria) this;
        }

        public Criteria andCertNameNotBetween(String value1, String value2) {
            addCriterion("cert_name not between", value1, value2, "certName");
            return (Criteria) this;
        }

        public Criteria andCertNoIsNull() {
            addCriterion("cert_no is null");
            return (Criteria) this;
        }

        public Criteria andCertNoIsNotNull() {
            addCriterion("cert_no is not null");
            return (Criteria) this;
        }

        public Criteria andCertNoEqualTo(String value) {
            addCriterion("cert_no =", value, "certNo");
            return (Criteria) this;
        }

        public Criteria andCertNoNotEqualTo(String value) {
            addCriterion("cert_no <>", value, "certNo");
            return (Criteria) this;
        }

        public Criteria andCertNoGreaterThan(String value) {
            addCriterion("cert_no >", value, "certNo");
            return (Criteria) this;
        }

        public Criteria andCertNoGreaterThanOrEqualTo(String value) {
            addCriterion("cert_no >=", value, "certNo");
            return (Criteria) this;
        }

        public Criteria andCertNoLessThan(String value) {
            addCriterion("cert_no <", value, "certNo");
            return (Criteria) this;
        }

        public Criteria andCertNoLessThanOrEqualTo(String value) {
            addCriterion("cert_no <=", value, "certNo");
            return (Criteria) this;
        }

        public Criteria andCertNoLike(String value) {
            addCriterion("cert_no like", value, "certNo");
            return (Criteria) this;
        }

        public Criteria andCertNoNotLike(String value) {
            addCriterion("cert_no not like", value, "certNo");
            return (Criteria) this;
        }

        public Criteria andCertNoIn(List<String> values) {
            addCriterion("cert_no in", values, "certNo");
            return (Criteria) this;
        }

        public Criteria andCertNoNotIn(List<String> values) {
            addCriterion("cert_no not in", values, "certNo");
            return (Criteria) this;
        }

        public Criteria andCertNoBetween(String value1, String value2) {
            addCriterion("cert_no between", value1, value2, "certNo");
            return (Criteria) this;
        }

        public Criteria andCertNoNotBetween(String value1, String value2) {
            addCriterion("cert_no not between", value1, value2, "certNo");
            return (Criteria) this;
        }

        public Criteria andTermOfValidityIsNull() {
            addCriterion("term_of_validity is null");
            return (Criteria) this;
        }

        public Criteria andTermOfValidityIsNotNull() {
            addCriterion("term_of_validity is not null");
            return (Criteria) this;
        }

        public Criteria andTermOfValidityEqualTo(Date value) {
            addCriterionForJDBCDate("term_of_validity =", value, "termOfValidity");
            return (Criteria) this;
        }

        public Criteria andTermOfValidityNotEqualTo(Date value) {
            addCriterionForJDBCDate("term_of_validity <>", value, "termOfValidity");
            return (Criteria) this;
        }

        public Criteria andTermOfValidityGreaterThan(Date value) {
            addCriterionForJDBCDate("term_of_validity >", value, "termOfValidity");
            return (Criteria) this;
        }

        public Criteria andTermOfValidityGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("term_of_validity >=", value, "termOfValidity");
            return (Criteria) this;
        }

        public Criteria andTermOfValidityLessThan(Date value) {
            addCriterionForJDBCDate("term_of_validity <", value, "termOfValidity");
            return (Criteria) this;
        }

        public Criteria andTermOfValidityLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("term_of_validity <=", value, "termOfValidity");
            return (Criteria) this;
        }

        public Criteria andTermOfValidityIn(List<Date> values) {
            addCriterionForJDBCDate("term_of_validity in", values, "termOfValidity");
            return (Criteria) this;
        }

        public Criteria andTermOfValidityNotIn(List<Date> values) {
            addCriterionForJDBCDate("term_of_validity not in", values, "termOfValidity");
            return (Criteria) this;
        }

        public Criteria andTermOfValidityBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("term_of_validity between", value1, value2, "termOfValidity");
            return (Criteria) this;
        }

        public Criteria andTermOfValidityNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("term_of_validity not between", value1, value2, "termOfValidity");
            return (Criteria) this;
        }

        public Criteria andFrontImgUrlIsNull() {
            addCriterion("front_img_url is null");
            return (Criteria) this;
        }

        public Criteria andFrontImgUrlIsNotNull() {
            addCriterion("front_img_url is not null");
            return (Criteria) this;
        }

        public Criteria andFrontImgUrlEqualTo(String value) {
            addCriterion("front_img_url =", value, "frontImgUrl");
            return (Criteria) this;
        }

        public Criteria andFrontImgUrlNotEqualTo(String value) {
            addCriterion("front_img_url <>", value, "frontImgUrl");
            return (Criteria) this;
        }

        public Criteria andFrontImgUrlGreaterThan(String value) {
            addCriterion("front_img_url >", value, "frontImgUrl");
            return (Criteria) this;
        }

        public Criteria andFrontImgUrlGreaterThanOrEqualTo(String value) {
            addCriterion("front_img_url >=", value, "frontImgUrl");
            return (Criteria) this;
        }

        public Criteria andFrontImgUrlLessThan(String value) {
            addCriterion("front_img_url <", value, "frontImgUrl");
            return (Criteria) this;
        }

        public Criteria andFrontImgUrlLessThanOrEqualTo(String value) {
            addCriterion("front_img_url <=", value, "frontImgUrl");
            return (Criteria) this;
        }

        public Criteria andFrontImgUrlLike(String value) {
            addCriterion("front_img_url like", value, "frontImgUrl");
            return (Criteria) this;
        }

        public Criteria andFrontImgUrlNotLike(String value) {
            addCriterion("front_img_url not like", value, "frontImgUrl");
            return (Criteria) this;
        }

        public Criteria andFrontImgUrlIn(List<String> values) {
            addCriterion("front_img_url in", values, "frontImgUrl");
            return (Criteria) this;
        }

        public Criteria andFrontImgUrlNotIn(List<String> values) {
            addCriterion("front_img_url not in", values, "frontImgUrl");
            return (Criteria) this;
        }

        public Criteria andFrontImgUrlBetween(String value1, String value2) {
            addCriterion("front_img_url between", value1, value2, "frontImgUrl");
            return (Criteria) this;
        }

        public Criteria andFrontImgUrlNotBetween(String value1, String value2) {
            addCriterion("front_img_url not between", value1, value2, "frontImgUrl");
            return (Criteria) this;
        }

        public Criteria andBackImgUrlIsNull() {
            addCriterion("back_img_url is null");
            return (Criteria) this;
        }

        public Criteria andBackImgUrlIsNotNull() {
            addCriterion("back_img_url is not null");
            return (Criteria) this;
        }

        public Criteria andBackImgUrlEqualTo(String value) {
            addCriterion("back_img_url =", value, "backImgUrl");
            return (Criteria) this;
        }

        public Criteria andBackImgUrlNotEqualTo(String value) {
            addCriterion("back_img_url <>", value, "backImgUrl");
            return (Criteria) this;
        }

        public Criteria andBackImgUrlGreaterThan(String value) {
            addCriterion("back_img_url >", value, "backImgUrl");
            return (Criteria) this;
        }

        public Criteria andBackImgUrlGreaterThanOrEqualTo(String value) {
            addCriterion("back_img_url >=", value, "backImgUrl");
            return (Criteria) this;
        }

        public Criteria andBackImgUrlLessThan(String value) {
            addCriterion("back_img_url <", value, "backImgUrl");
            return (Criteria) this;
        }

        public Criteria andBackImgUrlLessThanOrEqualTo(String value) {
            addCriterion("back_img_url <=", value, "backImgUrl");
            return (Criteria) this;
        }

        public Criteria andBackImgUrlLike(String value) {
            addCriterion("back_img_url like", value, "backImgUrl");
            return (Criteria) this;
        }

        public Criteria andBackImgUrlNotLike(String value) {
            addCriterion("back_img_url not like", value, "backImgUrl");
            return (Criteria) this;
        }

        public Criteria andBackImgUrlIn(List<String> values) {
            addCriterion("back_img_url in", values, "backImgUrl");
            return (Criteria) this;
        }

        public Criteria andBackImgUrlNotIn(List<String> values) {
            addCriterion("back_img_url not in", values, "backImgUrl");
            return (Criteria) this;
        }

        public Criteria andBackImgUrlBetween(String value1, String value2) {
            addCriterion("back_img_url between", value1, value2, "backImgUrl");
            return (Criteria) this;
        }

        public Criteria andBackImgUrlNotBetween(String value1, String value2) {
            addCriterion("back_img_url not between", value1, value2, "backImgUrl");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Integer value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Integer value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Integer value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Integer value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Integer value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Integer> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Integer> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Integer value1, Integer value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Integer value1, Integer value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNull() {
            addCriterion("company_code is null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNotNull() {
            addCriterion("company_code is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeEqualTo(String value) {
            addCriterion("company_code =", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotEqualTo(String value) {
            addCriterion("company_code <>", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThan(String value) {
            addCriterion("company_code >", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("company_code >=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThan(String value) {
            addCriterion("company_code <", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("company_code <=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLike(String value) {
            addCriterion("company_code like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotLike(String value) {
            addCriterion("company_code not like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIn(List<String> values) {
            addCriterion("company_code in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotIn(List<String> values) {
            addCriterion("company_code not in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeBetween(String value1, String value2) {
            addCriterion("company_code between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("company_code not between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIsNull() {
            addCriterion("company_name is null");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIsNotNull() {
            addCriterion("company_name is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyNameEqualTo(String value) {
            addCriterion("company_name =", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotEqualTo(String value) {
            addCriterion("company_name <>", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameGreaterThan(String value) {
            addCriterion("company_name >", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameGreaterThanOrEqualTo(String value) {
            addCriterion("company_name >=", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLessThan(String value) {
            addCriterion("company_name <", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLessThanOrEqualTo(String value) {
            addCriterion("company_name <=", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLike(String value) {
            addCriterion("company_name like", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotLike(String value) {
            addCriterion("company_name not like", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIn(List<String> values) {
            addCriterion("company_name in", values, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotIn(List<String> values) {
            addCriterion("company_name not in", values, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameBetween(String value1, String value2) {
            addCriterion("company_name between", value1, value2, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotBetween(String value1, String value2) {
            addCriterion("company_name not between", value1, value2, "companyName");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andVerifyStatusIsNull() {
            addCriterion("verify_status is null");
            return (Criteria) this;
        }

        public Criteria andVerifyStatusIsNotNull() {
            addCriterion("verify_status is not null");
            return (Criteria) this;
        }

        public Criteria andVerifyStatusEqualTo(Integer value) {
            addCriterion("verify_status =", value, "verifyStatus");
            return (Criteria) this;
        }

        public Criteria andVerifyStatusNotEqualTo(Integer value) {
            addCriterion("verify_status <>", value, "verifyStatus");
            return (Criteria) this;
        }

        public Criteria andVerifyStatusGreaterThan(Integer value) {
            addCriterion("verify_status >", value, "verifyStatus");
            return (Criteria) this;
        }

        public Criteria andVerifyStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("verify_status >=", value, "verifyStatus");
            return (Criteria) this;
        }

        public Criteria andVerifyStatusLessThan(Integer value) {
            addCriterion("verify_status <", value, "verifyStatus");
            return (Criteria) this;
        }

        public Criteria andVerifyStatusLessThanOrEqualTo(Integer value) {
            addCriterion("verify_status <=", value, "verifyStatus");
            return (Criteria) this;
        }

        public Criteria andVerifyStatusIn(List<Integer> values) {
            addCriterion("verify_status in", values, "verifyStatus");
            return (Criteria) this;
        }

        public Criteria andVerifyStatusNotIn(List<Integer> values) {
            addCriterion("verify_status not in", values, "verifyStatus");
            return (Criteria) this;
        }

        public Criteria andVerifyStatusBetween(Integer value1, Integer value2) {
            addCriterion("verify_status between", value1, value2, "verifyStatus");
            return (Criteria) this;
        }

        public Criteria andVerifyStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("verify_status not between", value1, value2, "verifyStatus");
            return (Criteria) this;
        }

        public Criteria andSexIsNull() {
            addCriterion("sex is null");
            return (Criteria) this;
        }

        public Criteria andSexIsNotNull() {
            addCriterion("sex is not null");
            return (Criteria) this;
        }

        public Criteria andSexEqualTo(Byte value) {
            addCriterion("sex =", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexNotEqualTo(Byte value) {
            addCriterion("sex <>", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexGreaterThan(Byte value) {
            addCriterion("sex >", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexGreaterThanOrEqualTo(Byte value) {
            addCriterion("sex >=", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexLessThan(Byte value) {
            addCriterion("sex <", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexLessThanOrEqualTo(Byte value) {
            addCriterion("sex <=", value, "sex");
            return (Criteria) this;
        }

        public Criteria andSexIn(List<Byte> values) {
            addCriterion("sex in", values, "sex");
            return (Criteria) this;
        }

        public Criteria andSexNotIn(List<Byte> values) {
            addCriterion("sex not in", values, "sex");
            return (Criteria) this;
        }

        public Criteria andSexBetween(Byte value1, Byte value2) {
            addCriterion("sex between", value1, value2, "sex");
            return (Criteria) this;
        }

        public Criteria andSexNotBetween(Byte value1, Byte value2) {
            addCriterion("sex not between", value1, value2, "sex");
            return (Criteria) this;
        }

        public Criteria andAddressIsNull() {
            addCriterion("address is null");
            return (Criteria) this;
        }

        public Criteria andAddressIsNotNull() {
            addCriterion("address is not null");
            return (Criteria) this;
        }

        public Criteria andAddressEqualTo(String value) {
            addCriterion("address =", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotEqualTo(String value) {
            addCriterion("address <>", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressGreaterThan(String value) {
            addCriterion("address >", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressGreaterThanOrEqualTo(String value) {
            addCriterion("address >=", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressLessThan(String value) {
            addCriterion("address <", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressLessThanOrEqualTo(String value) {
            addCriterion("address <=", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressLike(String value) {
            addCriterion("address like", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotLike(String value) {
            addCriterion("address not like", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressIn(List<String> values) {
            addCriterion("address in", values, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotIn(List<String> values) {
            addCriterion("address not in", values, "address");
            return (Criteria) this;
        }

        public Criteria andAddressBetween(String value1, String value2) {
            addCriterion("address between", value1, value2, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotBetween(String value1, String value2) {
            addCriterion("address not between", value1, value2, "address");
            return (Criteria) this;
        }

        public Criteria andAgeIsNull() {
            addCriterion("age is null");
            return (Criteria) this;
        }

        public Criteria andAgeIsNotNull() {
            addCriterion("age is not null");
            return (Criteria) this;
        }

        public Criteria andAgeEqualTo(Integer value) {
            addCriterion("age =", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeNotEqualTo(Integer value) {
            addCriterion("age <>", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeGreaterThan(Integer value) {
            addCriterion("age >", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeGreaterThanOrEqualTo(Integer value) {
            addCriterion("age >=", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeLessThan(Integer value) {
            addCriterion("age <", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeLessThanOrEqualTo(Integer value) {
            addCriterion("age <=", value, "age");
            return (Criteria) this;
        }

        public Criteria andAgeIn(List<Integer> values) {
            addCriterion("age in", values, "age");
            return (Criteria) this;
        }

        public Criteria andAgeNotIn(List<Integer> values) {
            addCriterion("age not in", values, "age");
            return (Criteria) this;
        }

        public Criteria andAgeBetween(Integer value1, Integer value2) {
            addCriterion("age between", value1, value2, "age");
            return (Criteria) this;
        }

        public Criteria andAgeNotBetween(Integer value1, Integer value2) {
            addCriterion("age not between", value1, value2, "age");
            return (Criteria) this;
        }

        public Criteria andDriverCertMainImgUrlIsNull() {
            addCriterion("driver_cert_main_img_url is null");
            return (Criteria) this;
        }

        public Criteria andDriverCertMainImgUrlIsNotNull() {
            addCriterion("driver_cert_main_img_url is not null");
            return (Criteria) this;
        }

        public Criteria andDriverCertMainImgUrlEqualTo(String value) {
            addCriterion("driver_cert_main_img_url =", value, "driverCertMainImgUrl");
            return (Criteria) this;
        }

        public Criteria andDriverCertMainImgUrlNotEqualTo(String value) {
            addCriterion("driver_cert_main_img_url <>", value, "driverCertMainImgUrl");
            return (Criteria) this;
        }

        public Criteria andDriverCertMainImgUrlGreaterThan(String value) {
            addCriterion("driver_cert_main_img_url >", value, "driverCertMainImgUrl");
            return (Criteria) this;
        }

        public Criteria andDriverCertMainImgUrlGreaterThanOrEqualTo(String value) {
            addCriterion("driver_cert_main_img_url >=", value, "driverCertMainImgUrl");
            return (Criteria) this;
        }

        public Criteria andDriverCertMainImgUrlLessThan(String value) {
            addCriterion("driver_cert_main_img_url <", value, "driverCertMainImgUrl");
            return (Criteria) this;
        }

        public Criteria andDriverCertMainImgUrlLessThanOrEqualTo(String value) {
            addCriterion("driver_cert_main_img_url <=", value, "driverCertMainImgUrl");
            return (Criteria) this;
        }

        public Criteria andDriverCertMainImgUrlLike(String value) {
            addCriterion("driver_cert_main_img_url like", value, "driverCertMainImgUrl");
            return (Criteria) this;
        }

        public Criteria andDriverCertMainImgUrlNotLike(String value) {
            addCriterion("driver_cert_main_img_url not like", value, "driverCertMainImgUrl");
            return (Criteria) this;
        }

        public Criteria andDriverCertMainImgUrlIn(List<String> values) {
            addCriterion("driver_cert_main_img_url in", values, "driverCertMainImgUrl");
            return (Criteria) this;
        }

        public Criteria andDriverCertMainImgUrlNotIn(List<String> values) {
            addCriterion("driver_cert_main_img_url not in", values, "driverCertMainImgUrl");
            return (Criteria) this;
        }

        public Criteria andDriverCertMainImgUrlBetween(String value1, String value2) {
            addCriterion("driver_cert_main_img_url between", value1, value2, "driverCertMainImgUrl");
            return (Criteria) this;
        }

        public Criteria andDriverCertMainImgUrlNotBetween(String value1, String value2) {
            addCriterion("driver_cert_main_img_url not between", value1, value2, "driverCertMainImgUrl");
            return (Criteria) this;
        }

        public Criteria andDriverCertSubImgUrlIsNull() {
            addCriterion("driver_cert_sub_img_url is null");
            return (Criteria) this;
        }

        public Criteria andDriverCertSubImgUrlIsNotNull() {
            addCriterion("driver_cert_sub_img_url is not null");
            return (Criteria) this;
        }

        public Criteria andDriverCertSubImgUrlEqualTo(String value) {
            addCriterion("driver_cert_sub_img_url =", value, "driverCertSubImgUrl");
            return (Criteria) this;
        }

        public Criteria andDriverCertSubImgUrlNotEqualTo(String value) {
            addCriterion("driver_cert_sub_img_url <>", value, "driverCertSubImgUrl");
            return (Criteria) this;
        }

        public Criteria andDriverCertSubImgUrlGreaterThan(String value) {
            addCriterion("driver_cert_sub_img_url >", value, "driverCertSubImgUrl");
            return (Criteria) this;
        }

        public Criteria andDriverCertSubImgUrlGreaterThanOrEqualTo(String value) {
            addCriterion("driver_cert_sub_img_url >=", value, "driverCertSubImgUrl");
            return (Criteria) this;
        }

        public Criteria andDriverCertSubImgUrlLessThan(String value) {
            addCriterion("driver_cert_sub_img_url <", value, "driverCertSubImgUrl");
            return (Criteria) this;
        }

        public Criteria andDriverCertSubImgUrlLessThanOrEqualTo(String value) {
            addCriterion("driver_cert_sub_img_url <=", value, "driverCertSubImgUrl");
            return (Criteria) this;
        }

        public Criteria andDriverCertSubImgUrlLike(String value) {
            addCriterion("driver_cert_sub_img_url like", value, "driverCertSubImgUrl");
            return (Criteria) this;
        }

        public Criteria andDriverCertSubImgUrlNotLike(String value) {
            addCriterion("driver_cert_sub_img_url not like", value, "driverCertSubImgUrl");
            return (Criteria) this;
        }

        public Criteria andDriverCertSubImgUrlIn(List<String> values) {
            addCriterion("driver_cert_sub_img_url in", values, "driverCertSubImgUrl");
            return (Criteria) this;
        }

        public Criteria andDriverCertSubImgUrlNotIn(List<String> values) {
            addCriterion("driver_cert_sub_img_url not in", values, "driverCertSubImgUrl");
            return (Criteria) this;
        }

        public Criteria andDriverCertSubImgUrlBetween(String value1, String value2) {
            addCriterion("driver_cert_sub_img_url between", value1, value2, "driverCertSubImgUrl");
            return (Criteria) this;
        }

        public Criteria andDriverCertSubImgUrlNotBetween(String value1, String value2) {
            addCriterion("driver_cert_sub_img_url not between", value1, value2, "driverCertSubImgUrl");
            return (Criteria) this;
        }

        public Criteria andDriverCertNoIsNull() {
            addCriterion("driver_cert_no is null");
            return (Criteria) this;
        }

        public Criteria andDriverCertNoIsNotNull() {
            addCriterion("driver_cert_no is not null");
            return (Criteria) this;
        }

        public Criteria andDriverCertNoEqualTo(String value) {
            addCriterion("driver_cert_no =", value, "driverCertNo");
            return (Criteria) this;
        }

        public Criteria andDriverCertNoNotEqualTo(String value) {
            addCriterion("driver_cert_no <>", value, "driverCertNo");
            return (Criteria) this;
        }

        public Criteria andDriverCertNoGreaterThan(String value) {
            addCriterion("driver_cert_no >", value, "driverCertNo");
            return (Criteria) this;
        }

        public Criteria andDriverCertNoGreaterThanOrEqualTo(String value) {
            addCriterion("driver_cert_no >=", value, "driverCertNo");
            return (Criteria) this;
        }

        public Criteria andDriverCertNoLessThan(String value) {
            addCriterion("driver_cert_no <", value, "driverCertNo");
            return (Criteria) this;
        }

        public Criteria andDriverCertNoLessThanOrEqualTo(String value) {
            addCriterion("driver_cert_no <=", value, "driverCertNo");
            return (Criteria) this;
        }

        public Criteria andDriverCertNoLike(String value) {
            addCriterion("driver_cert_no like", value, "driverCertNo");
            return (Criteria) this;
        }

        public Criteria andDriverCertNoNotLike(String value) {
            addCriterion("driver_cert_no not like", value, "driverCertNo");
            return (Criteria) this;
        }

        public Criteria andDriverCertNoIn(List<String> values) {
            addCriterion("driver_cert_no in", values, "driverCertNo");
            return (Criteria) this;
        }

        public Criteria andDriverCertNoNotIn(List<String> values) {
            addCriterion("driver_cert_no not in", values, "driverCertNo");
            return (Criteria) this;
        }

        public Criteria andDriverCertNoBetween(String value1, String value2) {
            addCriterion("driver_cert_no between", value1, value2, "driverCertNo");
            return (Criteria) this;
        }

        public Criteria andDriverCertNoNotBetween(String value1, String value2) {
            addCriterion("driver_cert_no not between", value1, value2, "driverCertNo");
            return (Criteria) this;
        }

        public Criteria andLicenseTypeIsNull() {
            addCriterion("license_type is null");
            return (Criteria) this;
        }

        public Criteria andLicenseTypeIsNotNull() {
            addCriterion("license_type is not null");
            return (Criteria) this;
        }

        public Criteria andLicenseTypeEqualTo(String value) {
            addCriterion("license_type =", value, "licenseType");
            return (Criteria) this;
        }

        public Criteria andLicenseTypeNotEqualTo(String value) {
            addCriterion("license_type <>", value, "licenseType");
            return (Criteria) this;
        }

        public Criteria andLicenseTypeGreaterThan(String value) {
            addCriterion("license_type >", value, "licenseType");
            return (Criteria) this;
        }

        public Criteria andLicenseTypeGreaterThanOrEqualTo(String value) {
            addCriterion("license_type >=", value, "licenseType");
            return (Criteria) this;
        }

        public Criteria andLicenseTypeLessThan(String value) {
            addCriterion("license_type <", value, "licenseType");
            return (Criteria) this;
        }

        public Criteria andLicenseTypeLessThanOrEqualTo(String value) {
            addCriterion("license_type <=", value, "licenseType");
            return (Criteria) this;
        }

        public Criteria andLicenseTypeLike(String value) {
            addCriterion("license_type like", value, "licenseType");
            return (Criteria) this;
        }

        public Criteria andLicenseTypeNotLike(String value) {
            addCriterion("license_type not like", value, "licenseType");
            return (Criteria) this;
        }

        public Criteria andLicenseTypeIn(List<String> values) {
            addCriterion("license_type in", values, "licenseType");
            return (Criteria) this;
        }

        public Criteria andLicenseTypeNotIn(List<String> values) {
            addCriterion("license_type not in", values, "licenseType");
            return (Criteria) this;
        }

        public Criteria andLicenseTypeBetween(String value1, String value2) {
            addCriterion("license_type between", value1, value2, "licenseType");
            return (Criteria) this;
        }

        public Criteria andLicenseTypeNotBetween(String value1, String value2) {
            addCriterion("license_type not between", value1, value2, "licenseType");
            return (Criteria) this;
        }

        public Criteria andDriverArchivesNoIsNull() {
            addCriterion("driver_archives_no is null");
            return (Criteria) this;
        }

        public Criteria andDriverArchivesNoIsNotNull() {
            addCriterion("driver_archives_no is not null");
            return (Criteria) this;
        }

        public Criteria andDriverArchivesNoEqualTo(String value) {
            addCriterion("driver_archives_no =", value, "driverArchivesNo");
            return (Criteria) this;
        }

        public Criteria andDriverArchivesNoNotEqualTo(String value) {
            addCriterion("driver_archives_no <>", value, "driverArchivesNo");
            return (Criteria) this;
        }

        public Criteria andDriverArchivesNoGreaterThan(String value) {
            addCriterion("driver_archives_no >", value, "driverArchivesNo");
            return (Criteria) this;
        }

        public Criteria andDriverArchivesNoGreaterThanOrEqualTo(String value) {
            addCriterion("driver_archives_no >=", value, "driverArchivesNo");
            return (Criteria) this;
        }

        public Criteria andDriverArchivesNoLessThan(String value) {
            addCriterion("driver_archives_no <", value, "driverArchivesNo");
            return (Criteria) this;
        }

        public Criteria andDriverArchivesNoLessThanOrEqualTo(String value) {
            addCriterion("driver_archives_no <=", value, "driverArchivesNo");
            return (Criteria) this;
        }

        public Criteria andDriverArchivesNoLike(String value) {
            addCriterion("driver_archives_no like", value, "driverArchivesNo");
            return (Criteria) this;
        }

        public Criteria andDriverArchivesNoNotLike(String value) {
            addCriterion("driver_archives_no not like", value, "driverArchivesNo");
            return (Criteria) this;
        }

        public Criteria andDriverArchivesNoIn(List<String> values) {
            addCriterion("driver_archives_no in", values, "driverArchivesNo");
            return (Criteria) this;
        }

        public Criteria andDriverArchivesNoNotIn(List<String> values) {
            addCriterion("driver_archives_no not in", values, "driverArchivesNo");
            return (Criteria) this;
        }

        public Criteria andDriverArchivesNoBetween(String value1, String value2) {
            addCriterion("driver_archives_no between", value1, value2, "driverArchivesNo");
            return (Criteria) this;
        }

        public Criteria andDriverArchivesNoNotBetween(String value1, String value2) {
            addCriterion("driver_archives_no not between", value1, value2, "driverArchivesNo");
            return (Criteria) this;
        }

        public Criteria andDriverCertStartDateIsNull() {
            addCriterion("driver_cert_start_date is null");
            return (Criteria) this;
        }

        public Criteria andDriverCertStartDateIsNotNull() {
            addCriterion("driver_cert_start_date is not null");
            return (Criteria) this;
        }

        public Criteria andDriverCertStartDateEqualTo(Date value) {
            addCriterionForJDBCDate("driver_cert_start_date =", value, "driverCertStartDate");
            return (Criteria) this;
        }

        public Criteria andDriverCertStartDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("driver_cert_start_date <>", value, "driverCertStartDate");
            return (Criteria) this;
        }

        public Criteria andDriverCertStartDateGreaterThan(Date value) {
            addCriterionForJDBCDate("driver_cert_start_date >", value, "driverCertStartDate");
            return (Criteria) this;
        }

        public Criteria andDriverCertStartDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("driver_cert_start_date >=", value, "driverCertStartDate");
            return (Criteria) this;
        }

        public Criteria andDriverCertStartDateLessThan(Date value) {
            addCriterionForJDBCDate("driver_cert_start_date <", value, "driverCertStartDate");
            return (Criteria) this;
        }

        public Criteria andDriverCertStartDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("driver_cert_start_date <=", value, "driverCertStartDate");
            return (Criteria) this;
        }

        public Criteria andDriverCertStartDateIn(List<Date> values) {
            addCriterionForJDBCDate("driver_cert_start_date in", values, "driverCertStartDate");
            return (Criteria) this;
        }

        public Criteria andDriverCertStartDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("driver_cert_start_date not in", values, "driverCertStartDate");
            return (Criteria) this;
        }

        public Criteria andDriverCertStartDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("driver_cert_start_date between", value1, value2, "driverCertStartDate");
            return (Criteria) this;
        }

        public Criteria andDriverCertStartDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("driver_cert_start_date not between", value1, value2, "driverCertStartDate");
            return (Criteria) this;
        }

        public Criteria andDriverCertValidDateIsNull() {
            addCriterion("driver_cert_valid_date is null");
            return (Criteria) this;
        }

        public Criteria andDriverCertValidDateIsNotNull() {
            addCriterion("driver_cert_valid_date is not null");
            return (Criteria) this;
        }

        public Criteria andDriverCertValidDateEqualTo(Date value) {
            addCriterionForJDBCDate("driver_cert_valid_date =", value, "driverCertValidDate");
            return (Criteria) this;
        }

        public Criteria andDriverCertValidDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("driver_cert_valid_date <>", value, "driverCertValidDate");
            return (Criteria) this;
        }

        public Criteria andDriverCertValidDateGreaterThan(Date value) {
            addCriterionForJDBCDate("driver_cert_valid_date >", value, "driverCertValidDate");
            return (Criteria) this;
        }

        public Criteria andDriverCertValidDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("driver_cert_valid_date >=", value, "driverCertValidDate");
            return (Criteria) this;
        }

        public Criteria andDriverCertValidDateLessThan(Date value) {
            addCriterionForJDBCDate("driver_cert_valid_date <", value, "driverCertValidDate");
            return (Criteria) this;
        }

        public Criteria andDriverCertValidDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("driver_cert_valid_date <=", value, "driverCertValidDate");
            return (Criteria) this;
        }

        public Criteria andDriverCertValidDateIn(List<Date> values) {
            addCriterionForJDBCDate("driver_cert_valid_date in", values, "driverCertValidDate");
            return (Criteria) this;
        }

        public Criteria andDriverCertValidDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("driver_cert_valid_date not in", values, "driverCertValidDate");
            return (Criteria) this;
        }

        public Criteria andDriverCertValidDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("driver_cert_valid_date between", value1, value2, "driverCertValidDate");
            return (Criteria) this;
        }

        public Criteria andDriverCertValidDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("driver_cert_valid_date not between", value1, value2, "driverCertValidDate");
            return (Criteria) this;
        }

        public Criteria andDriverCertFirstDateIsNull() {
            addCriterion("driver_cert_first_date is null");
            return (Criteria) this;
        }

        public Criteria andDriverCertFirstDateIsNotNull() {
            addCriterion("driver_cert_first_date is not null");
            return (Criteria) this;
        }

        public Criteria andDriverCertFirstDateEqualTo(Date value) {
            addCriterionForJDBCDate("driver_cert_first_date =", value, "driverCertFirstDate");
            return (Criteria) this;
        }

        public Criteria andDriverCertFirstDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("driver_cert_first_date <>", value, "driverCertFirstDate");
            return (Criteria) this;
        }

        public Criteria andDriverCertFirstDateGreaterThan(Date value) {
            addCriterionForJDBCDate("driver_cert_first_date >", value, "driverCertFirstDate");
            return (Criteria) this;
        }

        public Criteria andDriverCertFirstDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("driver_cert_first_date >=", value, "driverCertFirstDate");
            return (Criteria) this;
        }

        public Criteria andDriverCertFirstDateLessThan(Date value) {
            addCriterionForJDBCDate("driver_cert_first_date <", value, "driverCertFirstDate");
            return (Criteria) this;
        }

        public Criteria andDriverCertFirstDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("driver_cert_first_date <=", value, "driverCertFirstDate");
            return (Criteria) this;
        }

        public Criteria andDriverCertFirstDateIn(List<Date> values) {
            addCriterionForJDBCDate("driver_cert_first_date in", values, "driverCertFirstDate");
            return (Criteria) this;
        }

        public Criteria andDriverCertFirstDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("driver_cert_first_date not in", values, "driverCertFirstDate");
            return (Criteria) this;
        }

        public Criteria andDriverCertFirstDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("driver_cert_first_date between", value1, value2, "driverCertFirstDate");
            return (Criteria) this;
        }

        public Criteria andDriverCertFirstDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("driver_cert_first_date not between", value1, value2, "driverCertFirstDate");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andVerifyDriverCardStatusIsNull() {
            addCriterion("verify_driver_card_status is null");
            return (Criteria) this;
        }

        public Criteria andVerifyDriverCardStatusIsNotNull() {
            addCriterion("verify_driver_card_status is not null");
            return (Criteria) this;
        }

        public Criteria andVerifyDriverCardStatusEqualTo(Integer value) {
            addCriterion("verify_driver_card_status =", value, "verifyDriverCardStatus");
            return (Criteria) this;
        }

        public Criteria andVerifyDriverCardStatusNotEqualTo(Integer value) {
            addCriterion("verify_driver_card_status <>", value, "verifyDriverCardStatus");
            return (Criteria) this;
        }

        public Criteria andVerifyDriverCardStatusGreaterThan(Integer value) {
            addCriterion("verify_driver_card_status >", value, "verifyDriverCardStatus");
            return (Criteria) this;
        }

        public Criteria andVerifyDriverCardStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("verify_driver_card_status >=", value, "verifyDriverCardStatus");
            return (Criteria) this;
        }

        public Criteria andVerifyDriverCardStatusLessThan(Integer value) {
            addCriterion("verify_driver_card_status <", value, "verifyDriverCardStatus");
            return (Criteria) this;
        }

        public Criteria andVerifyDriverCardStatusLessThanOrEqualTo(Integer value) {
            addCriterion("verify_driver_card_status <=", value, "verifyDriverCardStatus");
            return (Criteria) this;
        }

        public Criteria andVerifyDriverCardStatusIn(List<Integer> values) {
            addCriterion("verify_driver_card_status in", values, "verifyDriverCardStatus");
            return (Criteria) this;
        }

        public Criteria andVerifyDriverCardStatusNotIn(List<Integer> values) {
            addCriterion("verify_driver_card_status not in", values, "verifyDriverCardStatus");
            return (Criteria) this;
        }

        public Criteria andVerifyDriverCardStatusBetween(Integer value1, Integer value2) {
            addCriterion("verify_driver_card_status between", value1, value2, "verifyDriverCardStatus");
            return (Criteria) this;
        }

        public Criteria andVerifyDriverCardStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("verify_driver_card_status not between", value1, value2, "verifyDriverCardStatus");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIsNull() {
            addCriterion("update_user_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIsNotNull() {
            addCriterion("update_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdEqualTo(Integer value) {
            addCriterion("update_user_id =", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotEqualTo(Integer value) {
            addCriterion("update_user_id <>", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdGreaterThan(Integer value) {
            addCriterion("update_user_id >", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("update_user_id >=", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLessThan(Integer value) {
            addCriterion("update_user_id <", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLessThanOrEqualTo(Integer value) {
            addCriterion("update_user_id <=", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIn(List<Integer> values) {
            addCriterion("update_user_id in", values, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotIn(List<Integer> values) {
            addCriterion("update_user_id not in", values, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdBetween(Integer value1, Integer value2) {
            addCriterion("update_user_id between", value1, value2, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotBetween(Integer value1, Integer value2) {
            addCriterion("update_user_id not between", value1, value2, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNameIsNull() {
            addCriterion("update_user_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNameIsNotNull() {
            addCriterion("update_user_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNameEqualTo(String value) {
            addCriterion("update_user_name =", value, "updateUserName");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNameNotEqualTo(String value) {
            addCriterion("update_user_name <>", value, "updateUserName");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNameGreaterThan(String value) {
            addCriterion("update_user_name >", value, "updateUserName");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNameGreaterThanOrEqualTo(String value) {
            addCriterion("update_user_name >=", value, "updateUserName");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNameLessThan(String value) {
            addCriterion("update_user_name <", value, "updateUserName");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNameLessThanOrEqualTo(String value) {
            addCriterion("update_user_name <=", value, "updateUserName");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNameLike(String value) {
            addCriterion("update_user_name like", value, "updateUserName");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNameNotLike(String value) {
            addCriterion("update_user_name not like", value, "updateUserName");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNameIn(List<String> values) {
            addCriterion("update_user_name in", values, "updateUserName");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNameNotIn(List<String> values) {
            addCriterion("update_user_name not in", values, "updateUserName");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNameBetween(String value1, String value2) {
            addCriterion("update_user_name between", value1, value2, "updateUserName");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNameNotBetween(String value1, String value2) {
            addCriterion("update_user_name not between", value1, value2, "updateUserName");
            return (Criteria) this;
        }

        public Criteria andUpdateUserMobileIsNull() {
            addCriterion("update_user_mobile is null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserMobileIsNotNull() {
            addCriterion("update_user_mobile is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserMobileEqualTo(String value) {
            addCriterion("update_user_mobile =", value, "updateUserMobile");
            return (Criteria) this;
        }

        public Criteria andUpdateUserMobileNotEqualTo(String value) {
            addCriterion("update_user_mobile <>", value, "updateUserMobile");
            return (Criteria) this;
        }

        public Criteria andUpdateUserMobileGreaterThan(String value) {
            addCriterion("update_user_mobile >", value, "updateUserMobile");
            return (Criteria) this;
        }

        public Criteria andUpdateUserMobileGreaterThanOrEqualTo(String value) {
            addCriterion("update_user_mobile >=", value, "updateUserMobile");
            return (Criteria) this;
        }

        public Criteria andUpdateUserMobileLessThan(String value) {
            addCriterion("update_user_mobile <", value, "updateUserMobile");
            return (Criteria) this;
        }

        public Criteria andUpdateUserMobileLessThanOrEqualTo(String value) {
            addCriterion("update_user_mobile <=", value, "updateUserMobile");
            return (Criteria) this;
        }

        public Criteria andUpdateUserMobileLike(String value) {
            addCriterion("update_user_mobile like", value, "updateUserMobile");
            return (Criteria) this;
        }

        public Criteria andUpdateUserMobileNotLike(String value) {
            addCriterion("update_user_mobile not like", value, "updateUserMobile");
            return (Criteria) this;
        }

        public Criteria andUpdateUserMobileIn(List<String> values) {
            addCriterion("update_user_mobile in", values, "updateUserMobile");
            return (Criteria) this;
        }

        public Criteria andUpdateUserMobileNotIn(List<String> values) {
            addCriterion("update_user_mobile not in", values, "updateUserMobile");
            return (Criteria) this;
        }

        public Criteria andUpdateUserMobileBetween(String value1, String value2) {
            addCriterion("update_user_mobile between", value1, value2, "updateUserMobile");
            return (Criteria) this;
        }

        public Criteria andUpdateUserMobileNotBetween(String value1, String value2) {
            addCriterion("update_user_mobile not between", value1, value2, "updateUserMobile");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}