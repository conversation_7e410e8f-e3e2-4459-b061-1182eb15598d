package com.izu.user.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DatacenterStructBussExample {
    /**
     * datacenter_struct_buss
     */
    protected String orderByClause;

    /**
     * datacenter_struct_buss
     */
    protected boolean distinct;

    /**
     * datacenter_struct_buss
     */
    protected List<Criteria> oredCriteria;

    public DatacenterStructBussExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andStructIdIsNull() {
            addCriterion("struct_id is null");
            return (Criteria) this;
        }

        public Criteria andStructIdIsNotNull() {
            addCriterion("struct_id is not null");
            return (Criteria) this;
        }

        public Criteria andStructIdEqualTo(Integer value) {
            addCriterion("struct_id =", value, "structId");
            return (Criteria) this;
        }

        public Criteria andStructIdNotEqualTo(Integer value) {
            addCriterion("struct_id <>", value, "structId");
            return (Criteria) this;
        }

        public Criteria andStructIdGreaterThan(Integer value) {
            addCriterion("struct_id >", value, "structId");
            return (Criteria) this;
        }

        public Criteria andStructIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("struct_id >=", value, "structId");
            return (Criteria) this;
        }

        public Criteria andStructIdLessThan(Integer value) {
            addCriterion("struct_id <", value, "structId");
            return (Criteria) this;
        }

        public Criteria andStructIdLessThanOrEqualTo(Integer value) {
            addCriterion("struct_id <=", value, "structId");
            return (Criteria) this;
        }

        public Criteria andStructIdIn(List<Integer> values) {
            addCriterion("struct_id in", values, "structId");
            return (Criteria) this;
        }

        public Criteria andStructIdNotIn(List<Integer> values) {
            addCriterion("struct_id not in", values, "structId");
            return (Criteria) this;
        }

        public Criteria andStructIdBetween(Integer value1, Integer value2) {
            addCriterion("struct_id between", value1, value2, "structId");
            return (Criteria) this;
        }

        public Criteria andStructIdNotBetween(Integer value1, Integer value2) {
            addCriterion("struct_id not between", value1, value2, "structId");
            return (Criteria) this;
        }

        public Criteria andStructNameIsNull() {
            addCriterion("struct_name is null");
            return (Criteria) this;
        }

        public Criteria andStructNameIsNotNull() {
            addCriterion("struct_name is not null");
            return (Criteria) this;
        }

        public Criteria andStructNameEqualTo(String value) {
            addCriterion("struct_name =", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameNotEqualTo(String value) {
            addCriterion("struct_name <>", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameGreaterThan(String value) {
            addCriterion("struct_name >", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameGreaterThanOrEqualTo(String value) {
            addCriterion("struct_name >=", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameLessThan(String value) {
            addCriterion("struct_name <", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameLessThanOrEqualTo(String value) {
            addCriterion("struct_name <=", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameLike(String value) {
            addCriterion("struct_name like", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameNotLike(String value) {
            addCriterion("struct_name not like", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameIn(List<String> values) {
            addCriterion("struct_name in", values, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameNotIn(List<String> values) {
            addCriterion("struct_name not in", values, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameBetween(String value1, String value2) {
            addCriterion("struct_name between", value1, value2, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameNotBetween(String value1, String value2) {
            addCriterion("struct_name not between", value1, value2, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameShortIsNull() {
            addCriterion("struct_name_short is null");
            return (Criteria) this;
        }

        public Criteria andStructNameShortIsNotNull() {
            addCriterion("struct_name_short is not null");
            return (Criteria) this;
        }

        public Criteria andStructNameShortEqualTo(String value) {
            addCriterion("struct_name_short =", value, "structNameShort");
            return (Criteria) this;
        }

        public Criteria andStructNameShortNotEqualTo(String value) {
            addCriterion("struct_name_short <>", value, "structNameShort");
            return (Criteria) this;
        }

        public Criteria andStructNameShortGreaterThan(String value) {
            addCriterion("struct_name_short >", value, "structNameShort");
            return (Criteria) this;
        }

        public Criteria andStructNameShortGreaterThanOrEqualTo(String value) {
            addCriterion("struct_name_short >=", value, "structNameShort");
            return (Criteria) this;
        }

        public Criteria andStructNameShortLessThan(String value) {
            addCriterion("struct_name_short <", value, "structNameShort");
            return (Criteria) this;
        }

        public Criteria andStructNameShortLessThanOrEqualTo(String value) {
            addCriterion("struct_name_short <=", value, "structNameShort");
            return (Criteria) this;
        }

        public Criteria andStructNameShortLike(String value) {
            addCriterion("struct_name_short like", value, "structNameShort");
            return (Criteria) this;
        }

        public Criteria andStructNameShortNotLike(String value) {
            addCriterion("struct_name_short not like", value, "structNameShort");
            return (Criteria) this;
        }

        public Criteria andStructNameShortIn(List<String> values) {
            addCriterion("struct_name_short in", values, "structNameShort");
            return (Criteria) this;
        }

        public Criteria andStructNameShortNotIn(List<String> values) {
            addCriterion("struct_name_short not in", values, "structNameShort");
            return (Criteria) this;
        }

        public Criteria andStructNameShortBetween(String value1, String value2) {
            addCriterion("struct_name_short between", value1, value2, "structNameShort");
            return (Criteria) this;
        }

        public Criteria andStructNameShortNotBetween(String value1, String value2) {
            addCriterion("struct_name_short not between", value1, value2, "structNameShort");
            return (Criteria) this;
        }

        public Criteria andProviderCodeIsNull() {
            addCriterion("provider_code is null");
            return (Criteria) this;
        }

        public Criteria andProviderCodeIsNotNull() {
            addCriterion("provider_code is not null");
            return (Criteria) this;
        }

        public Criteria andProviderCodeEqualTo(String value) {
            addCriterion("provider_code =", value, "providerCode");
            return (Criteria) this;
        }

        public Criteria andProviderCodeNotEqualTo(String value) {
            addCriterion("provider_code <>", value, "providerCode");
            return (Criteria) this;
        }

        public Criteria andProviderCodeGreaterThan(String value) {
            addCriterion("provider_code >", value, "providerCode");
            return (Criteria) this;
        }

        public Criteria andProviderCodeGreaterThanOrEqualTo(String value) {
            addCriterion("provider_code >=", value, "providerCode");
            return (Criteria) this;
        }

        public Criteria andProviderCodeLessThan(String value) {
            addCriterion("provider_code <", value, "providerCode");
            return (Criteria) this;
        }

        public Criteria andProviderCodeLessThanOrEqualTo(String value) {
            addCriterion("provider_code <=", value, "providerCode");
            return (Criteria) this;
        }

        public Criteria andProviderCodeLike(String value) {
            addCriterion("provider_code like", value, "providerCode");
            return (Criteria) this;
        }

        public Criteria andProviderCodeNotLike(String value) {
            addCriterion("provider_code not like", value, "providerCode");
            return (Criteria) this;
        }

        public Criteria andProviderCodeIn(List<String> values) {
            addCriterion("provider_code in", values, "providerCode");
            return (Criteria) this;
        }

        public Criteria andProviderCodeNotIn(List<String> values) {
            addCriterion("provider_code not in", values, "providerCode");
            return (Criteria) this;
        }

        public Criteria andProviderCodeBetween(String value1, String value2) {
            addCriterion("provider_code between", value1, value2, "providerCode");
            return (Criteria) this;
        }

        public Criteria andProviderCodeNotBetween(String value1, String value2) {
            addCriterion("provider_code not between", value1, value2, "providerCode");
            return (Criteria) this;
        }

        public Criteria andStructParentIdHierarchyIsNull() {
            addCriterion("struct_parent_id_hierarchy is null");
            return (Criteria) this;
        }

        public Criteria andStructParentIdHierarchyIsNotNull() {
            addCriterion("struct_parent_id_hierarchy is not null");
            return (Criteria) this;
        }

        public Criteria andStructParentIdHierarchyEqualTo(String value) {
            addCriterion("struct_parent_id_hierarchy =", value, "structParentIdHierarchy");
            return (Criteria) this;
        }

        public Criteria andStructParentIdHierarchyNotEqualTo(String value) {
            addCriterion("struct_parent_id_hierarchy <>", value, "structParentIdHierarchy");
            return (Criteria) this;
        }

        public Criteria andStructParentIdHierarchyGreaterThan(String value) {
            addCriterion("struct_parent_id_hierarchy >", value, "structParentIdHierarchy");
            return (Criteria) this;
        }

        public Criteria andStructParentIdHierarchyGreaterThanOrEqualTo(String value) {
            addCriterion("struct_parent_id_hierarchy >=", value, "structParentIdHierarchy");
            return (Criteria) this;
        }

        public Criteria andStructParentIdHierarchyLessThan(String value) {
            addCriterion("struct_parent_id_hierarchy <", value, "structParentIdHierarchy");
            return (Criteria) this;
        }

        public Criteria andStructParentIdHierarchyLessThanOrEqualTo(String value) {
            addCriterion("struct_parent_id_hierarchy <=", value, "structParentIdHierarchy");
            return (Criteria) this;
        }

        public Criteria andStructParentIdHierarchyLike(String value) {
            addCriterion("struct_parent_id_hierarchy like", value, "structParentIdHierarchy");
            return (Criteria) this;
        }

        public Criteria andStructParentIdHierarchyNotLike(String value) {
            addCriterion("struct_parent_id_hierarchy not like", value, "structParentIdHierarchy");
            return (Criteria) this;
        }

        public Criteria andStructParentIdHierarchyIn(List<String> values) {
            addCriterion("struct_parent_id_hierarchy in", values, "structParentIdHierarchy");
            return (Criteria) this;
        }

        public Criteria andStructParentIdHierarchyNotIn(List<String> values) {
            addCriterion("struct_parent_id_hierarchy not in", values, "structParentIdHierarchy");
            return (Criteria) this;
        }

        public Criteria andStructParentIdHierarchyBetween(String value1, String value2) {
            addCriterion("struct_parent_id_hierarchy between", value1, value2, "structParentIdHierarchy");
            return (Criteria) this;
        }

        public Criteria andStructParentIdHierarchyNotBetween(String value1, String value2) {
            addCriterion("struct_parent_id_hierarchy not between", value1, value2, "structParentIdHierarchy");
            return (Criteria) this;
        }

        public Criteria andParentIdIsNull() {
            addCriterion("parent_id is null");
            return (Criteria) this;
        }

        public Criteria andParentIdIsNotNull() {
            addCriterion("parent_id is not null");
            return (Criteria) this;
        }

        public Criteria andParentIdEqualTo(Integer value) {
            addCriterion("parent_id =", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotEqualTo(Integer value) {
            addCriterion("parent_id <>", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdGreaterThan(Integer value) {
            addCriterion("parent_id >", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("parent_id >=", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdLessThan(Integer value) {
            addCriterion("parent_id <", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdLessThanOrEqualTo(Integer value) {
            addCriterion("parent_id <=", value, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdIn(List<Integer> values) {
            addCriterion("parent_id in", values, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotIn(List<Integer> values) {
            addCriterion("parent_id not in", values, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdBetween(Integer value1, Integer value2) {
            addCriterion("parent_id between", value1, value2, "parentId");
            return (Criteria) this;
        }

        public Criteria andParentIdNotBetween(Integer value1, Integer value2) {
            addCriterion("parent_id not between", value1, value2, "parentId");
            return (Criteria) this;
        }

        public Criteria andDingIdIsNull() {
            addCriterion("ding_id is null");
            return (Criteria) this;
        }

        public Criteria andDingIdIsNotNull() {
            addCriterion("ding_id is not null");
            return (Criteria) this;
        }

        public Criteria andDingIdEqualTo(Long value) {
            addCriterion("ding_id =", value, "dingId");
            return (Criteria) this;
        }

        public Criteria andDingIdNotEqualTo(Long value) {
            addCriterion("ding_id <>", value, "dingId");
            return (Criteria) this;
        }

        public Criteria andDingIdGreaterThan(Long value) {
            addCriterion("ding_id >", value, "dingId");
            return (Criteria) this;
        }

        public Criteria andDingIdGreaterThanOrEqualTo(Long value) {
            addCriterion("ding_id >=", value, "dingId");
            return (Criteria) this;
        }

        public Criteria andDingIdLessThan(Long value) {
            addCriterion("ding_id <", value, "dingId");
            return (Criteria) this;
        }

        public Criteria andDingIdLessThanOrEqualTo(Long value) {
            addCriterion("ding_id <=", value, "dingId");
            return (Criteria) this;
        }

        public Criteria andDingIdIn(List<Long> values) {
            addCriterion("ding_id in", values, "dingId");
            return (Criteria) this;
        }

        public Criteria andDingIdNotIn(List<Long> values) {
            addCriterion("ding_id not in", values, "dingId");
            return (Criteria) this;
        }

        public Criteria andDingIdBetween(Long value1, Long value2) {
            addCriterion("ding_id between", value1, value2, "dingId");
            return (Criteria) this;
        }

        public Criteria andDingIdNotBetween(Long value1, Long value2) {
            addCriterion("ding_id not between", value1, value2, "dingId");
            return (Criteria) this;
        }

        public Criteria andDingParentIdIsNull() {
            addCriterion("ding_parent_id is null");
            return (Criteria) this;
        }

        public Criteria andDingParentIdIsNotNull() {
            addCriterion("ding_parent_id is not null");
            return (Criteria) this;
        }

        public Criteria andDingParentIdEqualTo(Long value) {
            addCriterion("ding_parent_id =", value, "dingParentId");
            return (Criteria) this;
        }

        public Criteria andDingParentIdNotEqualTo(Long value) {
            addCriterion("ding_parent_id <>", value, "dingParentId");
            return (Criteria) this;
        }

        public Criteria andDingParentIdGreaterThan(Long value) {
            addCriterion("ding_parent_id >", value, "dingParentId");
            return (Criteria) this;
        }

        public Criteria andDingParentIdGreaterThanOrEqualTo(Long value) {
            addCriterion("ding_parent_id >=", value, "dingParentId");
            return (Criteria) this;
        }

        public Criteria andDingParentIdLessThan(Long value) {
            addCriterion("ding_parent_id <", value, "dingParentId");
            return (Criteria) this;
        }

        public Criteria andDingParentIdLessThanOrEqualTo(Long value) {
            addCriterion("ding_parent_id <=", value, "dingParentId");
            return (Criteria) this;
        }

        public Criteria andDingParentIdIn(List<Long> values) {
            addCriterion("ding_parent_id in", values, "dingParentId");
            return (Criteria) this;
        }

        public Criteria andDingParentIdNotIn(List<Long> values) {
            addCriterion("ding_parent_id not in", values, "dingParentId");
            return (Criteria) this;
        }

        public Criteria andDingParentIdBetween(Long value1, Long value2) {
            addCriterion("ding_parent_id between", value1, value2, "dingParentId");
            return (Criteria) this;
        }

        public Criteria andDingParentIdNotBetween(Long value1, Long value2) {
            addCriterion("ding_parent_id not between", value1, value2, "dingParentId");
            return (Criteria) this;
        }

        public Criteria andStructCodeIsNull() {
            addCriterion("struct_code is null");
            return (Criteria) this;
        }

        public Criteria andStructCodeIsNotNull() {
            addCriterion("struct_code is not null");
            return (Criteria) this;
        }

        public Criteria andStructCodeEqualTo(String value) {
            addCriterion("struct_code =", value, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeNotEqualTo(String value) {
            addCriterion("struct_code <>", value, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeGreaterThan(String value) {
            addCriterion("struct_code >", value, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeGreaterThanOrEqualTo(String value) {
            addCriterion("struct_code >=", value, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeLessThan(String value) {
            addCriterion("struct_code <", value, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeLessThanOrEqualTo(String value) {
            addCriterion("struct_code <=", value, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeLike(String value) {
            addCriterion("struct_code like", value, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeNotLike(String value) {
            addCriterion("struct_code not like", value, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeIn(List<String> values) {
            addCriterion("struct_code in", values, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeNotIn(List<String> values) {
            addCriterion("struct_code not in", values, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeBetween(String value1, String value2) {
            addCriterion("struct_code between", value1, value2, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeNotBetween(String value1, String value2) {
            addCriterion("struct_code not between", value1, value2, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructTypeIsNull() {
            addCriterion("struct_type is null");
            return (Criteria) this;
        }

        public Criteria andStructTypeIsNotNull() {
            addCriterion("struct_type is not null");
            return (Criteria) this;
        }

        public Criteria andStructTypeEqualTo(Byte value) {
            addCriterion("struct_type =", value, "structType");
            return (Criteria) this;
        }

        public Criteria andStructTypeNotEqualTo(Byte value) {
            addCriterion("struct_type <>", value, "structType");
            return (Criteria) this;
        }

        public Criteria andStructTypeGreaterThan(Byte value) {
            addCriterion("struct_type >", value, "structType");
            return (Criteria) this;
        }

        public Criteria andStructTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("struct_type >=", value, "structType");
            return (Criteria) this;
        }

        public Criteria andStructTypeLessThan(Byte value) {
            addCriterion("struct_type <", value, "structType");
            return (Criteria) this;
        }

        public Criteria andStructTypeLessThanOrEqualTo(Byte value) {
            addCriterion("struct_type <=", value, "structType");
            return (Criteria) this;
        }

        public Criteria andStructTypeIn(List<Byte> values) {
            addCriterion("struct_type in", values, "structType");
            return (Criteria) this;
        }

        public Criteria andStructTypeNotIn(List<Byte> values) {
            addCriterion("struct_type not in", values, "structType");
            return (Criteria) this;
        }

        public Criteria andStructTypeBetween(Byte value1, Byte value2) {
            addCriterion("struct_type between", value1, value2, "structType");
            return (Criteria) this;
        }

        public Criteria andStructTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("struct_type not between", value1, value2, "structType");
            return (Criteria) this;
        }

        public Criteria andRegionTypeIsNull() {
            addCriterion("region_type is null");
            return (Criteria) this;
        }

        public Criteria andRegionTypeIsNotNull() {
            addCriterion("region_type is not null");
            return (Criteria) this;
        }

        public Criteria andRegionTypeEqualTo(Byte value) {
            addCriterion("region_type =", value, "regionType");
            return (Criteria) this;
        }

        public Criteria andRegionTypeNotEqualTo(Byte value) {
            addCriterion("region_type <>", value, "regionType");
            return (Criteria) this;
        }

        public Criteria andRegionTypeGreaterThan(Byte value) {
            addCriterion("region_type >", value, "regionType");
            return (Criteria) this;
        }

        public Criteria andRegionTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("region_type >=", value, "regionType");
            return (Criteria) this;
        }

        public Criteria andRegionTypeLessThan(Byte value) {
            addCriterion("region_type <", value, "regionType");
            return (Criteria) this;
        }

        public Criteria andRegionTypeLessThanOrEqualTo(Byte value) {
            addCriterion("region_type <=", value, "regionType");
            return (Criteria) this;
        }

        public Criteria andRegionTypeIn(List<Byte> values) {
            addCriterion("region_type in", values, "regionType");
            return (Criteria) this;
        }

        public Criteria andRegionTypeNotIn(List<Byte> values) {
            addCriterion("region_type not in", values, "regionType");
            return (Criteria) this;
        }

        public Criteria andRegionTypeBetween(Byte value1, Byte value2) {
            addCriterion("region_type between", value1, value2, "regionType");
            return (Criteria) this;
        }

        public Criteria andRegionTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("region_type not between", value1, value2, "regionType");
            return (Criteria) this;
        }

        public Criteria andBelongProvinceCodeIsNull() {
            addCriterion("belong_province_code is null");
            return (Criteria) this;
        }

        public Criteria andBelongProvinceCodeIsNotNull() {
            addCriterion("belong_province_code is not null");
            return (Criteria) this;
        }

        public Criteria andBelongProvinceCodeEqualTo(String value) {
            addCriterion("belong_province_code =", value, "belongProvinceCode");
            return (Criteria) this;
        }

        public Criteria andBelongProvinceCodeNotEqualTo(String value) {
            addCriterion("belong_province_code <>", value, "belongProvinceCode");
            return (Criteria) this;
        }

        public Criteria andBelongProvinceCodeGreaterThan(String value) {
            addCriterion("belong_province_code >", value, "belongProvinceCode");
            return (Criteria) this;
        }

        public Criteria andBelongProvinceCodeGreaterThanOrEqualTo(String value) {
            addCriterion("belong_province_code >=", value, "belongProvinceCode");
            return (Criteria) this;
        }

        public Criteria andBelongProvinceCodeLessThan(String value) {
            addCriterion("belong_province_code <", value, "belongProvinceCode");
            return (Criteria) this;
        }

        public Criteria andBelongProvinceCodeLessThanOrEqualTo(String value) {
            addCriterion("belong_province_code <=", value, "belongProvinceCode");
            return (Criteria) this;
        }

        public Criteria andBelongProvinceCodeLike(String value) {
            addCriterion("belong_province_code like", value, "belongProvinceCode");
            return (Criteria) this;
        }

        public Criteria andBelongProvinceCodeNotLike(String value) {
            addCriterion("belong_province_code not like", value, "belongProvinceCode");
            return (Criteria) this;
        }

        public Criteria andBelongProvinceCodeIn(List<String> values) {
            addCriterion("belong_province_code in", values, "belongProvinceCode");
            return (Criteria) this;
        }

        public Criteria andBelongProvinceCodeNotIn(List<String> values) {
            addCriterion("belong_province_code not in", values, "belongProvinceCode");
            return (Criteria) this;
        }

        public Criteria andBelongProvinceCodeBetween(String value1, String value2) {
            addCriterion("belong_province_code between", value1, value2, "belongProvinceCode");
            return (Criteria) this;
        }

        public Criteria andBelongProvinceCodeNotBetween(String value1, String value2) {
            addCriterion("belong_province_code not between", value1, value2, "belongProvinceCode");
            return (Criteria) this;
        }

        public Criteria andBelongProvinceNameIsNull() {
            addCriterion("belong_province_name is null");
            return (Criteria) this;
        }

        public Criteria andBelongProvinceNameIsNotNull() {
            addCriterion("belong_province_name is not null");
            return (Criteria) this;
        }

        public Criteria andBelongProvinceNameEqualTo(String value) {
            addCriterion("belong_province_name =", value, "belongProvinceName");
            return (Criteria) this;
        }

        public Criteria andBelongProvinceNameNotEqualTo(String value) {
            addCriterion("belong_province_name <>", value, "belongProvinceName");
            return (Criteria) this;
        }

        public Criteria andBelongProvinceNameGreaterThan(String value) {
            addCriterion("belong_province_name >", value, "belongProvinceName");
            return (Criteria) this;
        }

        public Criteria andBelongProvinceNameGreaterThanOrEqualTo(String value) {
            addCriterion("belong_province_name >=", value, "belongProvinceName");
            return (Criteria) this;
        }

        public Criteria andBelongProvinceNameLessThan(String value) {
            addCriterion("belong_province_name <", value, "belongProvinceName");
            return (Criteria) this;
        }

        public Criteria andBelongProvinceNameLessThanOrEqualTo(String value) {
            addCriterion("belong_province_name <=", value, "belongProvinceName");
            return (Criteria) this;
        }

        public Criteria andBelongProvinceNameLike(String value) {
            addCriterion("belong_province_name like", value, "belongProvinceName");
            return (Criteria) this;
        }

        public Criteria andBelongProvinceNameNotLike(String value) {
            addCriterion("belong_province_name not like", value, "belongProvinceName");
            return (Criteria) this;
        }

        public Criteria andBelongProvinceNameIn(List<String> values) {
            addCriterion("belong_province_name in", values, "belongProvinceName");
            return (Criteria) this;
        }

        public Criteria andBelongProvinceNameNotIn(List<String> values) {
            addCriterion("belong_province_name not in", values, "belongProvinceName");
            return (Criteria) this;
        }

        public Criteria andBelongProvinceNameBetween(String value1, String value2) {
            addCriterion("belong_province_name between", value1, value2, "belongProvinceName");
            return (Criteria) this;
        }

        public Criteria andBelongProvinceNameNotBetween(String value1, String value2) {
            addCriterion("belong_province_name not between", value1, value2, "belongProvinceName");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeIsNull() {
            addCriterion("belong_city_code is null");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeIsNotNull() {
            addCriterion("belong_city_code is not null");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeEqualTo(String value) {
            addCriterion("belong_city_code =", value, "belongCityCode");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeNotEqualTo(String value) {
            addCriterion("belong_city_code <>", value, "belongCityCode");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeGreaterThan(String value) {
            addCriterion("belong_city_code >", value, "belongCityCode");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("belong_city_code >=", value, "belongCityCode");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeLessThan(String value) {
            addCriterion("belong_city_code <", value, "belongCityCode");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeLessThanOrEqualTo(String value) {
            addCriterion("belong_city_code <=", value, "belongCityCode");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeLike(String value) {
            addCriterion("belong_city_code like", value, "belongCityCode");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeNotLike(String value) {
            addCriterion("belong_city_code not like", value, "belongCityCode");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeIn(List<String> values) {
            addCriterion("belong_city_code in", values, "belongCityCode");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeNotIn(List<String> values) {
            addCriterion("belong_city_code not in", values, "belongCityCode");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeBetween(String value1, String value2) {
            addCriterion("belong_city_code between", value1, value2, "belongCityCode");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeNotBetween(String value1, String value2) {
            addCriterion("belong_city_code not between", value1, value2, "belongCityCode");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameIsNull() {
            addCriterion("belong_city_name is null");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameIsNotNull() {
            addCriterion("belong_city_name is not null");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameEqualTo(String value) {
            addCriterion("belong_city_name =", value, "belongCityName");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameNotEqualTo(String value) {
            addCriterion("belong_city_name <>", value, "belongCityName");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameGreaterThan(String value) {
            addCriterion("belong_city_name >", value, "belongCityName");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameGreaterThanOrEqualTo(String value) {
            addCriterion("belong_city_name >=", value, "belongCityName");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameLessThan(String value) {
            addCriterion("belong_city_name <", value, "belongCityName");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameLessThanOrEqualTo(String value) {
            addCriterion("belong_city_name <=", value, "belongCityName");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameLike(String value) {
            addCriterion("belong_city_name like", value, "belongCityName");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameNotLike(String value) {
            addCriterion("belong_city_name not like", value, "belongCityName");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameIn(List<String> values) {
            addCriterion("belong_city_name in", values, "belongCityName");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameNotIn(List<String> values) {
            addCriterion("belong_city_name not in", values, "belongCityName");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameBetween(String value1, String value2) {
            addCriterion("belong_city_name between", value1, value2, "belongCityName");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameNotBetween(String value1, String value2) {
            addCriterion("belong_city_name not between", value1, value2, "belongCityName");
            return (Criteria) this;
        }

        public Criteria andAccountSetCityCodeIsNull() {
            addCriterion("account_set_city_code is null");
            return (Criteria) this;
        }

        public Criteria andAccountSetCityCodeIsNotNull() {
            addCriterion("account_set_city_code is not null");
            return (Criteria) this;
        }

        public Criteria andAccountSetCityCodeEqualTo(String value) {
            addCriterion("account_set_city_code =", value, "accountSetCityCode");
            return (Criteria) this;
        }

        public Criteria andAccountSetCityCodeNotEqualTo(String value) {
            addCriterion("account_set_city_code <>", value, "accountSetCityCode");
            return (Criteria) this;
        }

        public Criteria andAccountSetCityCodeGreaterThan(String value) {
            addCriterion("account_set_city_code >", value, "accountSetCityCode");
            return (Criteria) this;
        }

        public Criteria andAccountSetCityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("account_set_city_code >=", value, "accountSetCityCode");
            return (Criteria) this;
        }

        public Criteria andAccountSetCityCodeLessThan(String value) {
            addCriterion("account_set_city_code <", value, "accountSetCityCode");
            return (Criteria) this;
        }

        public Criteria andAccountSetCityCodeLessThanOrEqualTo(String value) {
            addCriterion("account_set_city_code <=", value, "accountSetCityCode");
            return (Criteria) this;
        }

        public Criteria andAccountSetCityCodeLike(String value) {
            addCriterion("account_set_city_code like", value, "accountSetCityCode");
            return (Criteria) this;
        }

        public Criteria andAccountSetCityCodeNotLike(String value) {
            addCriterion("account_set_city_code not like", value, "accountSetCityCode");
            return (Criteria) this;
        }

        public Criteria andAccountSetCityCodeIn(List<String> values) {
            addCriterion("account_set_city_code in", values, "accountSetCityCode");
            return (Criteria) this;
        }

        public Criteria andAccountSetCityCodeNotIn(List<String> values) {
            addCriterion("account_set_city_code not in", values, "accountSetCityCode");
            return (Criteria) this;
        }

        public Criteria andAccountSetCityCodeBetween(String value1, String value2) {
            addCriterion("account_set_city_code between", value1, value2, "accountSetCityCode");
            return (Criteria) this;
        }

        public Criteria andAccountSetCityCodeNotBetween(String value1, String value2) {
            addCriterion("account_set_city_code not between", value1, value2, "accountSetCityCode");
            return (Criteria) this;
        }

        public Criteria andAccountSetCityNameIsNull() {
            addCriterion("account_set_city_name is null");
            return (Criteria) this;
        }

        public Criteria andAccountSetCityNameIsNotNull() {
            addCriterion("account_set_city_name is not null");
            return (Criteria) this;
        }

        public Criteria andAccountSetCityNameEqualTo(String value) {
            addCriterion("account_set_city_name =", value, "accountSetCityName");
            return (Criteria) this;
        }

        public Criteria andAccountSetCityNameNotEqualTo(String value) {
            addCriterion("account_set_city_name <>", value, "accountSetCityName");
            return (Criteria) this;
        }

        public Criteria andAccountSetCityNameGreaterThan(String value) {
            addCriterion("account_set_city_name >", value, "accountSetCityName");
            return (Criteria) this;
        }

        public Criteria andAccountSetCityNameGreaterThanOrEqualTo(String value) {
            addCriterion("account_set_city_name >=", value, "accountSetCityName");
            return (Criteria) this;
        }

        public Criteria andAccountSetCityNameLessThan(String value) {
            addCriterion("account_set_city_name <", value, "accountSetCityName");
            return (Criteria) this;
        }

        public Criteria andAccountSetCityNameLessThanOrEqualTo(String value) {
            addCriterion("account_set_city_name <=", value, "accountSetCityName");
            return (Criteria) this;
        }

        public Criteria andAccountSetCityNameLike(String value) {
            addCriterion("account_set_city_name like", value, "accountSetCityName");
            return (Criteria) this;
        }

        public Criteria andAccountSetCityNameNotLike(String value) {
            addCriterion("account_set_city_name not like", value, "accountSetCityName");
            return (Criteria) this;
        }

        public Criteria andAccountSetCityNameIn(List<String> values) {
            addCriterion("account_set_city_name in", values, "accountSetCityName");
            return (Criteria) this;
        }

        public Criteria andAccountSetCityNameNotIn(List<String> values) {
            addCriterion("account_set_city_name not in", values, "accountSetCityName");
            return (Criteria) this;
        }

        public Criteria andAccountSetCityNameBetween(String value1, String value2) {
            addCriterion("account_set_city_name between", value1, value2, "accountSetCityName");
            return (Criteria) this;
        }

        public Criteria andAccountSetCityNameNotBetween(String value1, String value2) {
            addCriterion("account_set_city_name not between", value1, value2, "accountSetCityName");
            return (Criteria) this;
        }

        public Criteria andStructAddrIsNull() {
            addCriterion("struct_addr is null");
            return (Criteria) this;
        }

        public Criteria andStructAddrIsNotNull() {
            addCriterion("struct_addr is not null");
            return (Criteria) this;
        }

        public Criteria andStructAddrEqualTo(String value) {
            addCriterion("struct_addr =", value, "structAddr");
            return (Criteria) this;
        }

        public Criteria andStructAddrNotEqualTo(String value) {
            addCriterion("struct_addr <>", value, "structAddr");
            return (Criteria) this;
        }

        public Criteria andStructAddrGreaterThan(String value) {
            addCriterion("struct_addr >", value, "structAddr");
            return (Criteria) this;
        }

        public Criteria andStructAddrGreaterThanOrEqualTo(String value) {
            addCriterion("struct_addr >=", value, "structAddr");
            return (Criteria) this;
        }

        public Criteria andStructAddrLessThan(String value) {
            addCriterion("struct_addr <", value, "structAddr");
            return (Criteria) this;
        }

        public Criteria andStructAddrLessThanOrEqualTo(String value) {
            addCriterion("struct_addr <=", value, "structAddr");
            return (Criteria) this;
        }

        public Criteria andStructAddrLike(String value) {
            addCriterion("struct_addr like", value, "structAddr");
            return (Criteria) this;
        }

        public Criteria andStructAddrNotLike(String value) {
            addCriterion("struct_addr not like", value, "structAddr");
            return (Criteria) this;
        }

        public Criteria andStructAddrIn(List<String> values) {
            addCriterion("struct_addr in", values, "structAddr");
            return (Criteria) this;
        }

        public Criteria andStructAddrNotIn(List<String> values) {
            addCriterion("struct_addr not in", values, "structAddr");
            return (Criteria) this;
        }

        public Criteria andStructAddrBetween(String value1, String value2) {
            addCriterion("struct_addr between", value1, value2, "structAddr");
            return (Criteria) this;
        }

        public Criteria andStructAddrNotBetween(String value1, String value2) {
            addCriterion("struct_addr not between", value1, value2, "structAddr");
            return (Criteria) this;
        }

        public Criteria andOperateLeaseIsNull() {
            addCriterion("operate_lease is null");
            return (Criteria) this;
        }

        public Criteria andOperateLeaseIsNotNull() {
            addCriterion("operate_lease is not null");
            return (Criteria) this;
        }

        public Criteria andOperateLeaseEqualTo(Byte value) {
            addCriterion("operate_lease =", value, "operateLease");
            return (Criteria) this;
        }

        public Criteria andOperateLeaseNotEqualTo(Byte value) {
            addCriterion("operate_lease <>", value, "operateLease");
            return (Criteria) this;
        }

        public Criteria andOperateLeaseGreaterThan(Byte value) {
            addCriterion("operate_lease >", value, "operateLease");
            return (Criteria) this;
        }

        public Criteria andOperateLeaseGreaterThanOrEqualTo(Byte value) {
            addCriterion("operate_lease >=", value, "operateLease");
            return (Criteria) this;
        }

        public Criteria andOperateLeaseLessThan(Byte value) {
            addCriterion("operate_lease <", value, "operateLease");
            return (Criteria) this;
        }

        public Criteria andOperateLeaseLessThanOrEqualTo(Byte value) {
            addCriterion("operate_lease <=", value, "operateLease");
            return (Criteria) this;
        }

        public Criteria andOperateLeaseIn(List<Byte> values) {
            addCriterion("operate_lease in", values, "operateLease");
            return (Criteria) this;
        }

        public Criteria andOperateLeaseNotIn(List<Byte> values) {
            addCriterion("operate_lease not in", values, "operateLease");
            return (Criteria) this;
        }

        public Criteria andOperateLeaseBetween(Byte value1, Byte value2) {
            addCriterion("operate_lease between", value1, value2, "operateLease");
            return (Criteria) this;
        }

        public Criteria andOperateLeaseNotBetween(Byte value1, Byte value2) {
            addCriterion("operate_lease not between", value1, value2, "operateLease");
            return (Criteria) this;
        }

        public Criteria andOrangeBusinessIsNull() {
            addCriterion("orange_business is null");
            return (Criteria) this;
        }

        public Criteria andOrangeBusinessIsNotNull() {
            addCriterion("orange_business is not null");
            return (Criteria) this;
        }

        public Criteria andOrangeBusinessEqualTo(Byte value) {
            addCriterion("orange_business =", value, "orangeBusiness");
            return (Criteria) this;
        }

        public Criteria andOrangeBusinessNotEqualTo(Byte value) {
            addCriterion("orange_business <>", value, "orangeBusiness");
            return (Criteria) this;
        }

        public Criteria andOrangeBusinessGreaterThan(Byte value) {
            addCriterion("orange_business >", value, "orangeBusiness");
            return (Criteria) this;
        }

        public Criteria andOrangeBusinessGreaterThanOrEqualTo(Byte value) {
            addCriterion("orange_business >=", value, "orangeBusiness");
            return (Criteria) this;
        }

        public Criteria andOrangeBusinessLessThan(Byte value) {
            addCriterion("orange_business <", value, "orangeBusiness");
            return (Criteria) this;
        }

        public Criteria andOrangeBusinessLessThanOrEqualTo(Byte value) {
            addCriterion("orange_business <=", value, "orangeBusiness");
            return (Criteria) this;
        }

        public Criteria andOrangeBusinessIn(List<Byte> values) {
            addCriterion("orange_business in", values, "orangeBusiness");
            return (Criteria) this;
        }

        public Criteria andOrangeBusinessNotIn(List<Byte> values) {
            addCriterion("orange_business not in", values, "orangeBusiness");
            return (Criteria) this;
        }

        public Criteria andOrangeBusinessBetween(Byte value1, Byte value2) {
            addCriterion("orange_business between", value1, value2, "orangeBusiness");
            return (Criteria) this;
        }

        public Criteria andOrangeBusinessNotBetween(Byte value1, Byte value2) {
            addCriterion("orange_business not between", value1, value2, "orangeBusiness");
            return (Criteria) this;
        }

        public Criteria andBelongFinanceIdIsNull() {
            addCriterion("belong_finance_id is null");
            return (Criteria) this;
        }

        public Criteria andBelongFinanceIdIsNotNull() {
            addCriterion("belong_finance_id is not null");
            return (Criteria) this;
        }

        public Criteria andBelongFinanceIdEqualTo(String value) {
            addCriterion("belong_finance_id =", value, "belongFinanceId");
            return (Criteria) this;
        }

        public Criteria andBelongFinanceIdNotEqualTo(String value) {
            addCriterion("belong_finance_id <>", value, "belongFinanceId");
            return (Criteria) this;
        }

        public Criteria andBelongFinanceIdGreaterThan(String value) {
            addCriterion("belong_finance_id >", value, "belongFinanceId");
            return (Criteria) this;
        }

        public Criteria andBelongFinanceIdGreaterThanOrEqualTo(String value) {
            addCriterion("belong_finance_id >=", value, "belongFinanceId");
            return (Criteria) this;
        }

        public Criteria andBelongFinanceIdLessThan(String value) {
            addCriterion("belong_finance_id <", value, "belongFinanceId");
            return (Criteria) this;
        }

        public Criteria andBelongFinanceIdLessThanOrEqualTo(String value) {
            addCriterion("belong_finance_id <=", value, "belongFinanceId");
            return (Criteria) this;
        }

        public Criteria andBelongFinanceIdLike(String value) {
            addCriterion("belong_finance_id like", value, "belongFinanceId");
            return (Criteria) this;
        }

        public Criteria andBelongFinanceIdNotLike(String value) {
            addCriterion("belong_finance_id not like", value, "belongFinanceId");
            return (Criteria) this;
        }

        public Criteria andBelongFinanceIdIn(List<String> values) {
            addCriterion("belong_finance_id in", values, "belongFinanceId");
            return (Criteria) this;
        }

        public Criteria andBelongFinanceIdNotIn(List<String> values) {
            addCriterion("belong_finance_id not in", values, "belongFinanceId");
            return (Criteria) this;
        }

        public Criteria andBelongFinanceIdBetween(String value1, String value2) {
            addCriterion("belong_finance_id between", value1, value2, "belongFinanceId");
            return (Criteria) this;
        }

        public Criteria andBelongFinanceIdNotBetween(String value1, String value2) {
            addCriterion("belong_finance_id not between", value1, value2, "belongFinanceId");
            return (Criteria) this;
        }

        public Criteria andStructTelnoIsNull() {
            addCriterion("struct_telno is null");
            return (Criteria) this;
        }

        public Criteria andStructTelnoIsNotNull() {
            addCriterion("struct_telno is not null");
            return (Criteria) this;
        }

        public Criteria andStructTelnoEqualTo(String value) {
            addCriterion("struct_telno =", value, "structTelno");
            return (Criteria) this;
        }

        public Criteria andStructTelnoNotEqualTo(String value) {
            addCriterion("struct_telno <>", value, "structTelno");
            return (Criteria) this;
        }

        public Criteria andStructTelnoGreaterThan(String value) {
            addCriterion("struct_telno >", value, "structTelno");
            return (Criteria) this;
        }

        public Criteria andStructTelnoGreaterThanOrEqualTo(String value) {
            addCriterion("struct_telno >=", value, "structTelno");
            return (Criteria) this;
        }

        public Criteria andStructTelnoLessThan(String value) {
            addCriterion("struct_telno <", value, "structTelno");
            return (Criteria) this;
        }

        public Criteria andStructTelnoLessThanOrEqualTo(String value) {
            addCriterion("struct_telno <=", value, "structTelno");
            return (Criteria) this;
        }

        public Criteria andStructTelnoLike(String value) {
            addCriterion("struct_telno like", value, "structTelno");
            return (Criteria) this;
        }

        public Criteria andStructTelnoNotLike(String value) {
            addCriterion("struct_telno not like", value, "structTelno");
            return (Criteria) this;
        }

        public Criteria andStructTelnoIn(List<String> values) {
            addCriterion("struct_telno in", values, "structTelno");
            return (Criteria) this;
        }

        public Criteria andStructTelnoNotIn(List<String> values) {
            addCriterion("struct_telno not in", values, "structTelno");
            return (Criteria) this;
        }

        public Criteria andStructTelnoBetween(String value1, String value2) {
            addCriterion("struct_telno between", value1, value2, "structTelno");
            return (Criteria) this;
        }

        public Criteria andStructTelnoNotBetween(String value1, String value2) {
            addCriterion("struct_telno not between", value1, value2, "structTelno");
            return (Criteria) this;
        }

        public Criteria andStructStateIsNull() {
            addCriterion("struct_state is null");
            return (Criteria) this;
        }

        public Criteria andStructStateIsNotNull() {
            addCriterion("struct_state is not null");
            return (Criteria) this;
        }

        public Criteria andStructStateEqualTo(Byte value) {
            addCriterion("struct_state =", value, "structState");
            return (Criteria) this;
        }

        public Criteria andStructStateNotEqualTo(Byte value) {
            addCriterion("struct_state <>", value, "structState");
            return (Criteria) this;
        }

        public Criteria andStructStateGreaterThan(Byte value) {
            addCriterion("struct_state >", value, "structState");
            return (Criteria) this;
        }

        public Criteria andStructStateGreaterThanOrEqualTo(Byte value) {
            addCriterion("struct_state >=", value, "structState");
            return (Criteria) this;
        }

        public Criteria andStructStateLessThan(Byte value) {
            addCriterion("struct_state <", value, "structState");
            return (Criteria) this;
        }

        public Criteria andStructStateLessThanOrEqualTo(Byte value) {
            addCriterion("struct_state <=", value, "structState");
            return (Criteria) this;
        }

        public Criteria andStructStateIn(List<Byte> values) {
            addCriterion("struct_state in", values, "structState");
            return (Criteria) this;
        }

        public Criteria andStructStateNotIn(List<Byte> values) {
            addCriterion("struct_state not in", values, "structState");
            return (Criteria) this;
        }

        public Criteria andStructStateBetween(Byte value1, Byte value2) {
            addCriterion("struct_state between", value1, value2, "structState");
            return (Criteria) this;
        }

        public Criteria andStructStateNotBetween(Byte value1, Byte value2) {
            addCriterion("struct_state not between", value1, value2, "structState");
            return (Criteria) this;
        }

        public Criteria andBussIsLeafIsNull() {
            addCriterion("buss_is_leaf is null");
            return (Criteria) this;
        }

        public Criteria andBussIsLeafIsNotNull() {
            addCriterion("buss_is_leaf is not null");
            return (Criteria) this;
        }

        public Criteria andBussIsLeafEqualTo(Byte value) {
            addCriterion("buss_is_leaf =", value, "bussIsLeaf");
            return (Criteria) this;
        }

        public Criteria andBussIsLeafNotEqualTo(Byte value) {
            addCriterion("buss_is_leaf <>", value, "bussIsLeaf");
            return (Criteria) this;
        }

        public Criteria andBussIsLeafGreaterThan(Byte value) {
            addCriterion("buss_is_leaf >", value, "bussIsLeaf");
            return (Criteria) this;
        }

        public Criteria andBussIsLeafGreaterThanOrEqualTo(Byte value) {
            addCriterion("buss_is_leaf >=", value, "bussIsLeaf");
            return (Criteria) this;
        }

        public Criteria andBussIsLeafLessThan(Byte value) {
            addCriterion("buss_is_leaf <", value, "bussIsLeaf");
            return (Criteria) this;
        }

        public Criteria andBussIsLeafLessThanOrEqualTo(Byte value) {
            addCriterion("buss_is_leaf <=", value, "bussIsLeaf");
            return (Criteria) this;
        }

        public Criteria andBussIsLeafIn(List<Byte> values) {
            addCriterion("buss_is_leaf in", values, "bussIsLeaf");
            return (Criteria) this;
        }

        public Criteria andBussIsLeafNotIn(List<Byte> values) {
            addCriterion("buss_is_leaf not in", values, "bussIsLeaf");
            return (Criteria) this;
        }

        public Criteria andBussIsLeafBetween(Byte value1, Byte value2) {
            addCriterion("buss_is_leaf between", value1, value2, "bussIsLeaf");
            return (Criteria) this;
        }

        public Criteria andBussIsLeafNotBetween(Byte value1, Byte value2) {
            addCriterion("buss_is_leaf not between", value1, value2, "bussIsLeaf");
            return (Criteria) this;
        }

        public Criteria andIsExistDingIsNull() {
            addCriterion("is_exist_ding is null");
            return (Criteria) this;
        }

        public Criteria andIsExistDingIsNotNull() {
            addCriterion("is_exist_ding is not null");
            return (Criteria) this;
        }

        public Criteria andIsExistDingEqualTo(Byte value) {
            addCriterion("is_exist_ding =", value, "isExistDing");
            return (Criteria) this;
        }

        public Criteria andIsExistDingNotEqualTo(Byte value) {
            addCriterion("is_exist_ding <>", value, "isExistDing");
            return (Criteria) this;
        }

        public Criteria andIsExistDingGreaterThan(Byte value) {
            addCriterion("is_exist_ding >", value, "isExistDing");
            return (Criteria) this;
        }

        public Criteria andIsExistDingGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_exist_ding >=", value, "isExistDing");
            return (Criteria) this;
        }

        public Criteria andIsExistDingLessThan(Byte value) {
            addCriterion("is_exist_ding <", value, "isExistDing");
            return (Criteria) this;
        }

        public Criteria andIsExistDingLessThanOrEqualTo(Byte value) {
            addCriterion("is_exist_ding <=", value, "isExistDing");
            return (Criteria) this;
        }

        public Criteria andIsExistDingIn(List<Byte> values) {
            addCriterion("is_exist_ding in", values, "isExistDing");
            return (Criteria) this;
        }

        public Criteria andIsExistDingNotIn(List<Byte> values) {
            addCriterion("is_exist_ding not in", values, "isExistDing");
            return (Criteria) this;
        }

        public Criteria andIsExistDingBetween(Byte value1, Byte value2) {
            addCriterion("is_exist_ding between", value1, value2, "isExistDing");
            return (Criteria) this;
        }

        public Criteria andIsExistDingNotBetween(Byte value1, Byte value2) {
            addCriterion("is_exist_ding not between", value1, value2, "isExistDing");
            return (Criteria) this;
        }

        public Criteria andAssetStructIdIsNull() {
            addCriterion("asset_struct_id is null");
            return (Criteria) this;
        }

        public Criteria andAssetStructIdIsNotNull() {
            addCriterion("asset_struct_id is not null");
            return (Criteria) this;
        }

        public Criteria andAssetStructIdEqualTo(String value) {
            addCriterion("asset_struct_id =", value, "assetStructId");
            return (Criteria) this;
        }

        public Criteria andAssetStructIdNotEqualTo(String value) {
            addCriterion("asset_struct_id <>", value, "assetStructId");
            return (Criteria) this;
        }

        public Criteria andAssetStructIdGreaterThan(String value) {
            addCriterion("asset_struct_id >", value, "assetStructId");
            return (Criteria) this;
        }

        public Criteria andAssetStructIdGreaterThanOrEqualTo(String value) {
            addCriterion("asset_struct_id >=", value, "assetStructId");
            return (Criteria) this;
        }

        public Criteria andAssetStructIdLessThan(String value) {
            addCriterion("asset_struct_id <", value, "assetStructId");
            return (Criteria) this;
        }

        public Criteria andAssetStructIdLessThanOrEqualTo(String value) {
            addCriterion("asset_struct_id <=", value, "assetStructId");
            return (Criteria) this;
        }

        public Criteria andAssetStructIdLike(String value) {
            addCriterion("asset_struct_id like", value, "assetStructId");
            return (Criteria) this;
        }

        public Criteria andAssetStructIdNotLike(String value) {
            addCriterion("asset_struct_id not like", value, "assetStructId");
            return (Criteria) this;
        }

        public Criteria andAssetStructIdIn(List<String> values) {
            addCriterion("asset_struct_id in", values, "assetStructId");
            return (Criteria) this;
        }

        public Criteria andAssetStructIdNotIn(List<String> values) {
            addCriterion("asset_struct_id not in", values, "assetStructId");
            return (Criteria) this;
        }

        public Criteria andAssetStructIdBetween(String value1, String value2) {
            addCriterion("asset_struct_id between", value1, value2, "assetStructId");
            return (Criteria) this;
        }

        public Criteria andAssetStructIdNotBetween(String value1, String value2) {
            addCriterion("asset_struct_id not between", value1, value2, "assetStructId");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andCreterIdIsNull() {
            addCriterion("creter_id is null");
            return (Criteria) this;
        }

        public Criteria andCreterIdIsNotNull() {
            addCriterion("creter_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreterIdEqualTo(Integer value) {
            addCriterion("creter_id =", value, "creterId");
            return (Criteria) this;
        }

        public Criteria andCreterIdNotEqualTo(Integer value) {
            addCriterion("creter_id <>", value, "creterId");
            return (Criteria) this;
        }

        public Criteria andCreterIdGreaterThan(Integer value) {
            addCriterion("creter_id >", value, "creterId");
            return (Criteria) this;
        }

        public Criteria andCreterIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("creter_id >=", value, "creterId");
            return (Criteria) this;
        }

        public Criteria andCreterIdLessThan(Integer value) {
            addCriterion("creter_id <", value, "creterId");
            return (Criteria) this;
        }

        public Criteria andCreterIdLessThanOrEqualTo(Integer value) {
            addCriterion("creter_id <=", value, "creterId");
            return (Criteria) this;
        }

        public Criteria andCreterIdIn(List<Integer> values) {
            addCriterion("creter_id in", values, "creterId");
            return (Criteria) this;
        }

        public Criteria andCreterIdNotIn(List<Integer> values) {
            addCriterion("creter_id not in", values, "creterId");
            return (Criteria) this;
        }

        public Criteria andCreterIdBetween(Integer value1, Integer value2) {
            addCriterion("creter_id between", value1, value2, "creterId");
            return (Criteria) this;
        }

        public Criteria andCreterIdNotBetween(Integer value1, Integer value2) {
            addCriterion("creter_id not between", value1, value2, "creterId");
            return (Criteria) this;
        }

        public Criteria andCreterNameIsNull() {
            addCriterion("creter_name is null");
            return (Criteria) this;
        }

        public Criteria andCreterNameIsNotNull() {
            addCriterion("creter_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreterNameEqualTo(String value) {
            addCriterion("creter_name =", value, "creterName");
            return (Criteria) this;
        }

        public Criteria andCreterNameNotEqualTo(String value) {
            addCriterion("creter_name <>", value, "creterName");
            return (Criteria) this;
        }

        public Criteria andCreterNameGreaterThan(String value) {
            addCriterion("creter_name >", value, "creterName");
            return (Criteria) this;
        }

        public Criteria andCreterNameGreaterThanOrEqualTo(String value) {
            addCriterion("creter_name >=", value, "creterName");
            return (Criteria) this;
        }

        public Criteria andCreterNameLessThan(String value) {
            addCriterion("creter_name <", value, "creterName");
            return (Criteria) this;
        }

        public Criteria andCreterNameLessThanOrEqualTo(String value) {
            addCriterion("creter_name <=", value, "creterName");
            return (Criteria) this;
        }

        public Criteria andCreterNameLike(String value) {
            addCriterion("creter_name like", value, "creterName");
            return (Criteria) this;
        }

        public Criteria andCreterNameNotLike(String value) {
            addCriterion("creter_name not like", value, "creterName");
            return (Criteria) this;
        }

        public Criteria andCreterNameIn(List<String> values) {
            addCriterion("creter_name in", values, "creterName");
            return (Criteria) this;
        }

        public Criteria andCreterNameNotIn(List<String> values) {
            addCriterion("creter_name not in", values, "creterName");
            return (Criteria) this;
        }

        public Criteria andCreterNameBetween(String value1, String value2) {
            addCriterion("creter_name between", value1, value2, "creterName");
            return (Criteria) this;
        }

        public Criteria andCreterNameNotBetween(String value1, String value2) {
            addCriterion("creter_name not between", value1, value2, "creterName");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateIdIsNull() {
            addCriterion("update_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdateIdIsNotNull() {
            addCriterion("update_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateIdEqualTo(Integer value) {
            addCriterion("update_id =", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdNotEqualTo(Integer value) {
            addCriterion("update_id <>", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdGreaterThan(Integer value) {
            addCriterion("update_id >", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("update_id >=", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdLessThan(Integer value) {
            addCriterion("update_id <", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdLessThanOrEqualTo(Integer value) {
            addCriterion("update_id <=", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdIn(List<Integer> values) {
            addCriterion("update_id in", values, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdNotIn(List<Integer> values) {
            addCriterion("update_id not in", values, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdBetween(Integer value1, Integer value2) {
            addCriterion("update_id between", value1, value2, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdNotBetween(Integer value1, Integer value2) {
            addCriterion("update_id not between", value1, value2, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateNameIsNull() {
            addCriterion("update_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdateNameIsNotNull() {
            addCriterion("update_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateNameEqualTo(String value) {
            addCriterion("update_name =", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotEqualTo(String value) {
            addCriterion("update_name <>", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameGreaterThan(String value) {
            addCriterion("update_name >", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameGreaterThanOrEqualTo(String value) {
            addCriterion("update_name >=", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameLessThan(String value) {
            addCriterion("update_name <", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameLessThanOrEqualTo(String value) {
            addCriterion("update_name <=", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameLike(String value) {
            addCriterion("update_name like", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotLike(String value) {
            addCriterion("update_name not like", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameIn(List<String> values) {
            addCriterion("update_name in", values, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotIn(List<String> values) {
            addCriterion("update_name not in", values, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameBetween(String value1, String value2) {
            addCriterion("update_name between", value1, value2, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotBetween(String value1, String value2) {
            addCriterion("update_name not between", value1, value2, "updateName");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}