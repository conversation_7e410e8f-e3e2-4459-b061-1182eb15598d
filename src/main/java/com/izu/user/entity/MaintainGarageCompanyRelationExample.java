package com.izu.user.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class MaintainGarageCompanyRelationExample {
    /**
     * maintain_garage_company_relation
     */
    protected String orderByClause;

    /**
     * maintain_garage_company_relation
     */
    protected boolean distinct;

    /**
     * maintain_garage_company_relation
     */
    protected List<Criteria> oredCriteria;

    public MaintainGarageCompanyRelationExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andRelationIdIsNull() {
            addCriterion("relation_id is null");
            return (Criteria) this;
        }

        public Criteria andRelationIdIsNotNull() {
            addCriterion("relation_id is not null");
            return (Criteria) this;
        }

        public Criteria andRelationIdEqualTo(Integer value) {
            addCriterion("relation_id =", value, "relationId");
            return (Criteria) this;
        }

        public Criteria andRelationIdNotEqualTo(Integer value) {
            addCriterion("relation_id <>", value, "relationId");
            return (Criteria) this;
        }

        public Criteria andRelationIdGreaterThan(Integer value) {
            addCriterion("relation_id >", value, "relationId");
            return (Criteria) this;
        }

        public Criteria andRelationIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("relation_id >=", value, "relationId");
            return (Criteria) this;
        }

        public Criteria andRelationIdLessThan(Integer value) {
            addCriterion("relation_id <", value, "relationId");
            return (Criteria) this;
        }

        public Criteria andRelationIdLessThanOrEqualTo(Integer value) {
            addCriterion("relation_id <=", value, "relationId");
            return (Criteria) this;
        }

        public Criteria andRelationIdIn(List<Integer> values) {
            addCriterion("relation_id in", values, "relationId");
            return (Criteria) this;
        }

        public Criteria andRelationIdNotIn(List<Integer> values) {
            addCriterion("relation_id not in", values, "relationId");
            return (Criteria) this;
        }

        public Criteria andRelationIdBetween(Integer value1, Integer value2) {
            addCriterion("relation_id between", value1, value2, "relationId");
            return (Criteria) this;
        }

        public Criteria andRelationIdNotBetween(Integer value1, Integer value2) {
            addCriterion("relation_id not between", value1, value2, "relationId");
            return (Criteria) this;
        }

        public Criteria andGarageNoIsNull() {
            addCriterion("garage_no is null");
            return (Criteria) this;
        }

        public Criteria andGarageNoIsNotNull() {
            addCriterion("garage_no is not null");
            return (Criteria) this;
        }

        public Criteria andGarageNoEqualTo(String value) {
            addCriterion("garage_no =", value, "garageNo");
            return (Criteria) this;
        }

        public Criteria andGarageNoNotEqualTo(String value) {
            addCriterion("garage_no <>", value, "garageNo");
            return (Criteria) this;
        }

        public Criteria andGarageNoGreaterThan(String value) {
            addCriterion("garage_no >", value, "garageNo");
            return (Criteria) this;
        }

        public Criteria andGarageNoGreaterThanOrEqualTo(String value) {
            addCriterion("garage_no >=", value, "garageNo");
            return (Criteria) this;
        }

        public Criteria andGarageNoLessThan(String value) {
            addCriterion("garage_no <", value, "garageNo");
            return (Criteria) this;
        }

        public Criteria andGarageNoLessThanOrEqualTo(String value) {
            addCriterion("garage_no <=", value, "garageNo");
            return (Criteria) this;
        }

        public Criteria andGarageNoLike(String value) {
            addCriterion("garage_no like", value, "garageNo");
            return (Criteria) this;
        }

        public Criteria andGarageNoNotLike(String value) {
            addCriterion("garage_no not like", value, "garageNo");
            return (Criteria) this;
        }

        public Criteria andGarageNoIn(List<String> values) {
            addCriterion("garage_no in", values, "garageNo");
            return (Criteria) this;
        }

        public Criteria andGarageNoNotIn(List<String> values) {
            addCriterion("garage_no not in", values, "garageNo");
            return (Criteria) this;
        }

        public Criteria andGarageNoBetween(String value1, String value2) {
            addCriterion("garage_no between", value1, value2, "garageNo");
            return (Criteria) this;
        }

        public Criteria andGarageNoNotBetween(String value1, String value2) {
            addCriterion("garage_no not between", value1, value2, "garageNo");
            return (Criteria) this;
        }

        public Criteria andGarageNameIsNull() {
            addCriterion("garage_name is null");
            return (Criteria) this;
        }

        public Criteria andGarageNameIsNotNull() {
            addCriterion("garage_name is not null");
            return (Criteria) this;
        }

        public Criteria andGarageNameEqualTo(String value) {
            addCriterion("garage_name =", value, "garageName");
            return (Criteria) this;
        }

        public Criteria andGarageNameNotEqualTo(String value) {
            addCriterion("garage_name <>", value, "garageName");
            return (Criteria) this;
        }

        public Criteria andGarageNameGreaterThan(String value) {
            addCriterion("garage_name >", value, "garageName");
            return (Criteria) this;
        }

        public Criteria andGarageNameGreaterThanOrEqualTo(String value) {
            addCriterion("garage_name >=", value, "garageName");
            return (Criteria) this;
        }

        public Criteria andGarageNameLessThan(String value) {
            addCriterion("garage_name <", value, "garageName");
            return (Criteria) this;
        }

        public Criteria andGarageNameLessThanOrEqualTo(String value) {
            addCriterion("garage_name <=", value, "garageName");
            return (Criteria) this;
        }

        public Criteria andGarageNameLike(String value) {
            addCriterion("garage_name like", value, "garageName");
            return (Criteria) this;
        }

        public Criteria andGarageNameNotLike(String value) {
            addCriterion("garage_name not like", value, "garageName");
            return (Criteria) this;
        }

        public Criteria andGarageNameIn(List<String> values) {
            addCriterion("garage_name in", values, "garageName");
            return (Criteria) this;
        }

        public Criteria andGarageNameNotIn(List<String> values) {
            addCriterion("garage_name not in", values, "garageName");
            return (Criteria) this;
        }

        public Criteria andGarageNameBetween(String value1, String value2) {
            addCriterion("garage_name between", value1, value2, "garageName");
            return (Criteria) this;
        }

        public Criteria andGarageNameNotBetween(String value1, String value2) {
            addCriterion("garage_name not between", value1, value2, "garageName");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNull() {
            addCriterion("company_code is null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNotNull() {
            addCriterion("company_code is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeEqualTo(String value) {
            addCriterion("company_code =", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotEqualTo(String value) {
            addCriterion("company_code <>", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThan(String value) {
            addCriterion("company_code >", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("company_code >=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThan(String value) {
            addCriterion("company_code <", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("company_code <=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLike(String value) {
            addCriterion("company_code like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotLike(String value) {
            addCriterion("company_code not like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIn(List<String> values) {
            addCriterion("company_code in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotIn(List<String> values) {
            addCriterion("company_code not in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeBetween(String value1, String value2) {
            addCriterion("company_code between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("company_code not between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyIdIsNull() {
            addCriterion("relation_company_id is null");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyIdIsNotNull() {
            addCriterion("relation_company_id is not null");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyIdEqualTo(Integer value) {
            addCriterion("relation_company_id =", value, "relationCompanyId");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyIdNotEqualTo(Integer value) {
            addCriterion("relation_company_id <>", value, "relationCompanyId");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyIdGreaterThan(Integer value) {
            addCriterion("relation_company_id >", value, "relationCompanyId");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("relation_company_id >=", value, "relationCompanyId");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyIdLessThan(Integer value) {
            addCriterion("relation_company_id <", value, "relationCompanyId");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyIdLessThanOrEqualTo(Integer value) {
            addCriterion("relation_company_id <=", value, "relationCompanyId");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyIdIn(List<Integer> values) {
            addCriterion("relation_company_id in", values, "relationCompanyId");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyIdNotIn(List<Integer> values) {
            addCriterion("relation_company_id not in", values, "relationCompanyId");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyIdBetween(Integer value1, Integer value2) {
            addCriterion("relation_company_id between", value1, value2, "relationCompanyId");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyIdNotBetween(Integer value1, Integer value2) {
            addCriterion("relation_company_id not between", value1, value2, "relationCompanyId");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyCodeIsNull() {
            addCriterion("relation_company_code is null");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyCodeIsNotNull() {
            addCriterion("relation_company_code is not null");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyCodeEqualTo(String value) {
            addCriterion("relation_company_code =", value, "relationCompanyCode");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyCodeNotEqualTo(String value) {
            addCriterion("relation_company_code <>", value, "relationCompanyCode");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyCodeGreaterThan(String value) {
            addCriterion("relation_company_code >", value, "relationCompanyCode");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("relation_company_code >=", value, "relationCompanyCode");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyCodeLessThan(String value) {
            addCriterion("relation_company_code <", value, "relationCompanyCode");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("relation_company_code <=", value, "relationCompanyCode");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyCodeLike(String value) {
            addCriterion("relation_company_code like", value, "relationCompanyCode");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyCodeNotLike(String value) {
            addCriterion("relation_company_code not like", value, "relationCompanyCode");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyCodeIn(List<String> values) {
            addCriterion("relation_company_code in", values, "relationCompanyCode");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyCodeNotIn(List<String> values) {
            addCriterion("relation_company_code not in", values, "relationCompanyCode");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyCodeBetween(String value1, String value2) {
            addCriterion("relation_company_code between", value1, value2, "relationCompanyCode");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("relation_company_code not between", value1, value2, "relationCompanyCode");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyNameIsNull() {
            addCriterion("relation_company_name is null");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyNameIsNotNull() {
            addCriterion("relation_company_name is not null");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyNameEqualTo(String value) {
            addCriterion("relation_company_name =", value, "relationCompanyName");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyNameNotEqualTo(String value) {
            addCriterion("relation_company_name <>", value, "relationCompanyName");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyNameGreaterThan(String value) {
            addCriterion("relation_company_name >", value, "relationCompanyName");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyNameGreaterThanOrEqualTo(String value) {
            addCriterion("relation_company_name >=", value, "relationCompanyName");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyNameLessThan(String value) {
            addCriterion("relation_company_name <", value, "relationCompanyName");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyNameLessThanOrEqualTo(String value) {
            addCriterion("relation_company_name <=", value, "relationCompanyName");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyNameLike(String value) {
            addCriterion("relation_company_name like", value, "relationCompanyName");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyNameNotLike(String value) {
            addCriterion("relation_company_name not like", value, "relationCompanyName");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyNameIn(List<String> values) {
            addCriterion("relation_company_name in", values, "relationCompanyName");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyNameNotIn(List<String> values) {
            addCriterion("relation_company_name not in", values, "relationCompanyName");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyNameBetween(String value1, String value2) {
            addCriterion("relation_company_name between", value1, value2, "relationCompanyName");
            return (Criteria) this;
        }

        public Criteria andRelationCompanyNameNotBetween(String value1, String value2) {
            addCriterion("relation_company_name not between", value1, value2, "relationCompanyName");
            return (Criteria) this;
        }

        public Criteria andRelationTypeIsNull() {
            addCriterion("relation_type is null");
            return (Criteria) this;
        }

        public Criteria andRelationTypeIsNotNull() {
            addCriterion("relation_type is not null");
            return (Criteria) this;
        }

        public Criteria andRelationTypeEqualTo(Byte value) {
            addCriterion("relation_type =", value, "relationType");
            return (Criteria) this;
        }

        public Criteria andRelationTypeNotEqualTo(Byte value) {
            addCriterion("relation_type <>", value, "relationType");
            return (Criteria) this;
        }

        public Criteria andRelationTypeGreaterThan(Byte value) {
            addCriterion("relation_type >", value, "relationType");
            return (Criteria) this;
        }

        public Criteria andRelationTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("relation_type >=", value, "relationType");
            return (Criteria) this;
        }

        public Criteria andRelationTypeLessThan(Byte value) {
            addCriterion("relation_type <", value, "relationType");
            return (Criteria) this;
        }

        public Criteria andRelationTypeLessThanOrEqualTo(Byte value) {
            addCriterion("relation_type <=", value, "relationType");
            return (Criteria) this;
        }

        public Criteria andRelationTypeIn(List<Byte> values) {
            addCriterion("relation_type in", values, "relationType");
            return (Criteria) this;
        }

        public Criteria andRelationTypeNotIn(List<Byte> values) {
            addCriterion("relation_type not in", values, "relationType");
            return (Criteria) this;
        }

        public Criteria andRelationTypeBetween(Byte value1, Byte value2) {
            addCriterion("relation_type between", value1, value2, "relationType");
            return (Criteria) this;
        }

        public Criteria andRelationTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("relation_type not between", value1, value2, "relationType");
            return (Criteria) this;
        }

        public Criteria andRelationBeginDateIsNull() {
            addCriterion("relation_begin_date is null");
            return (Criteria) this;
        }

        public Criteria andRelationBeginDateIsNotNull() {
            addCriterion("relation_begin_date is not null");
            return (Criteria) this;
        }

        public Criteria andRelationBeginDateEqualTo(Date value) {
            addCriterionForJDBCDate("relation_begin_date =", value, "relationBeginDate");
            return (Criteria) this;
        }

        public Criteria andRelationBeginDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("relation_begin_date <>", value, "relationBeginDate");
            return (Criteria) this;
        }

        public Criteria andRelationBeginDateGreaterThan(Date value) {
            addCriterionForJDBCDate("relation_begin_date >", value, "relationBeginDate");
            return (Criteria) this;
        }

        public Criteria andRelationBeginDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("relation_begin_date >=", value, "relationBeginDate");
            return (Criteria) this;
        }

        public Criteria andRelationBeginDateLessThan(Date value) {
            addCriterionForJDBCDate("relation_begin_date <", value, "relationBeginDate");
            return (Criteria) this;
        }

        public Criteria andRelationBeginDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("relation_begin_date <=", value, "relationBeginDate");
            return (Criteria) this;
        }

        public Criteria andRelationBeginDateIn(List<Date> values) {
            addCriterionForJDBCDate("relation_begin_date in", values, "relationBeginDate");
            return (Criteria) this;
        }

        public Criteria andRelationBeginDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("relation_begin_date not in", values, "relationBeginDate");
            return (Criteria) this;
        }

        public Criteria andRelationBeginDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("relation_begin_date between", value1, value2, "relationBeginDate");
            return (Criteria) this;
        }

        public Criteria andRelationBeginDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("relation_begin_date not between", value1, value2, "relationBeginDate");
            return (Criteria) this;
        }

        public Criteria andRelationEndDateIsNull() {
            addCriterion("relation_end_date is null");
            return (Criteria) this;
        }

        public Criteria andRelationEndDateIsNotNull() {
            addCriterion("relation_end_date is not null");
            return (Criteria) this;
        }

        public Criteria andRelationEndDateEqualTo(Date value) {
            addCriterionForJDBCDate("relation_end_date =", value, "relationEndDate");
            return (Criteria) this;
        }

        public Criteria andRelationEndDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("relation_end_date <>", value, "relationEndDate");
            return (Criteria) this;
        }

        public Criteria andRelationEndDateGreaterThan(Date value) {
            addCriterionForJDBCDate("relation_end_date >", value, "relationEndDate");
            return (Criteria) this;
        }

        public Criteria andRelationEndDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("relation_end_date >=", value, "relationEndDate");
            return (Criteria) this;
        }

        public Criteria andRelationEndDateLessThan(Date value) {
            addCriterionForJDBCDate("relation_end_date <", value, "relationEndDate");
            return (Criteria) this;
        }

        public Criteria andRelationEndDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("relation_end_date <=", value, "relationEndDate");
            return (Criteria) this;
        }

        public Criteria andRelationEndDateIn(List<Date> values) {
            addCriterionForJDBCDate("relation_end_date in", values, "relationEndDate");
            return (Criteria) this;
        }

        public Criteria andRelationEndDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("relation_end_date not in", values, "relationEndDate");
            return (Criteria) this;
        }

        public Criteria andRelationEndDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("relation_end_date between", value1, value2, "relationEndDate");
            return (Criteria) this;
        }

        public Criteria andRelationEndDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("relation_end_date not between", value1, value2, "relationEndDate");
            return (Criteria) this;
        }

        public Criteria andRelationStatusIsNull() {
            addCriterion("relation_status is null");
            return (Criteria) this;
        }

        public Criteria andRelationStatusIsNotNull() {
            addCriterion("relation_status is not null");
            return (Criteria) this;
        }

        public Criteria andRelationStatusEqualTo(Byte value) {
            addCriterion("relation_status =", value, "relationStatus");
            return (Criteria) this;
        }

        public Criteria andRelationStatusNotEqualTo(Byte value) {
            addCriterion("relation_status <>", value, "relationStatus");
            return (Criteria) this;
        }

        public Criteria andRelationStatusGreaterThan(Byte value) {
            addCriterion("relation_status >", value, "relationStatus");
            return (Criteria) this;
        }

        public Criteria andRelationStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("relation_status >=", value, "relationStatus");
            return (Criteria) this;
        }

        public Criteria andRelationStatusLessThan(Byte value) {
            addCriterion("relation_status <", value, "relationStatus");
            return (Criteria) this;
        }

        public Criteria andRelationStatusLessThanOrEqualTo(Byte value) {
            addCriterion("relation_status <=", value, "relationStatus");
            return (Criteria) this;
        }

        public Criteria andRelationStatusIn(List<Byte> values) {
            addCriterion("relation_status in", values, "relationStatus");
            return (Criteria) this;
        }

        public Criteria andRelationStatusNotIn(List<Byte> values) {
            addCriterion("relation_status not in", values, "relationStatus");
            return (Criteria) this;
        }

        public Criteria andRelationStatusBetween(Byte value1, Byte value2) {
            addCriterion("relation_status between", value1, value2, "relationStatus");
            return (Criteria) this;
        }

        public Criteria andRelationStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("relation_status not between", value1, value2, "relationStatus");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateIdIsNull() {
            addCriterion("create_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateIdIsNotNull() {
            addCriterion("create_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateIdEqualTo(Integer value) {
            addCriterion("create_id =", value, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateIdNotEqualTo(Integer value) {
            addCriterion("create_id <>", value, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateIdGreaterThan(Integer value) {
            addCriterion("create_id >", value, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("create_id >=", value, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateIdLessThan(Integer value) {
            addCriterion("create_id <", value, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateIdLessThanOrEqualTo(Integer value) {
            addCriterion("create_id <=", value, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateIdIn(List<Integer> values) {
            addCriterion("create_id in", values, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateIdNotIn(List<Integer> values) {
            addCriterion("create_id not in", values, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateIdBetween(Integer value1, Integer value2) {
            addCriterion("create_id between", value1, value2, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateIdNotBetween(Integer value1, Integer value2) {
            addCriterion("create_id not between", value1, value2, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateNameIsNull() {
            addCriterion("create_name is null");
            return (Criteria) this;
        }

        public Criteria andCreateNameIsNotNull() {
            addCriterion("create_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreateNameEqualTo(String value) {
            addCriterion("create_name =", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameNotEqualTo(String value) {
            addCriterion("create_name <>", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameGreaterThan(String value) {
            addCriterion("create_name >", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameGreaterThanOrEqualTo(String value) {
            addCriterion("create_name >=", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameLessThan(String value) {
            addCriterion("create_name <", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameLessThanOrEqualTo(String value) {
            addCriterion("create_name <=", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameLike(String value) {
            addCriterion("create_name like", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameNotLike(String value) {
            addCriterion("create_name not like", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameIn(List<String> values) {
            addCriterion("create_name in", values, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameNotIn(List<String> values) {
            addCriterion("create_name not in", values, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameBetween(String value1, String value2) {
            addCriterion("create_name between", value1, value2, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameNotBetween(String value1, String value2) {
            addCriterion("create_name not between", value1, value2, "createName");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateIdIsNull() {
            addCriterion("update_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdateIdIsNotNull() {
            addCriterion("update_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateIdEqualTo(Integer value) {
            addCriterion("update_id =", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdNotEqualTo(Integer value) {
            addCriterion("update_id <>", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdGreaterThan(Integer value) {
            addCriterion("update_id >", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("update_id >=", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdLessThan(Integer value) {
            addCriterion("update_id <", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdLessThanOrEqualTo(Integer value) {
            addCriterion("update_id <=", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdIn(List<Integer> values) {
            addCriterion("update_id in", values, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdNotIn(List<Integer> values) {
            addCriterion("update_id not in", values, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdBetween(Integer value1, Integer value2) {
            addCriterion("update_id between", value1, value2, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdNotBetween(Integer value1, Integer value2) {
            addCriterion("update_id not between", value1, value2, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateNameIsNull() {
            addCriterion("update_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdateNameIsNotNull() {
            addCriterion("update_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateNameEqualTo(String value) {
            addCriterion("update_name =", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotEqualTo(String value) {
            addCriterion("update_name <>", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameGreaterThan(String value) {
            addCriterion("update_name >", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameGreaterThanOrEqualTo(String value) {
            addCriterion("update_name >=", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameLessThan(String value) {
            addCriterion("update_name <", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameLessThanOrEqualTo(String value) {
            addCriterion("update_name <=", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameLike(String value) {
            addCriterion("update_name like", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotLike(String value) {
            addCriterion("update_name not like", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameIn(List<String> values) {
            addCriterion("update_name in", values, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotIn(List<String> values) {
            addCriterion("update_name not in", values, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameBetween(String value1, String value2) {
            addCriterion("update_name between", value1, value2, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotBetween(String value1, String value2) {
            addCriterion("update_name not between", value1, value2, "updateName");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}