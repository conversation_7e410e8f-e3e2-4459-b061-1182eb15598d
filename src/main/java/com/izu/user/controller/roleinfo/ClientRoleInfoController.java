package com.izu.user.controller.roleinfo;

import com.izu.framework.doc.JrdApiDoc;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.dto.provider.roleinfo.RoleInfoGetStaffReqDTO;
import com.izu.user.dto.roleinfo.*;
import com.izu.user.service.role.ClientRoleInfoService;
import com.izu.user.service.provider.role.ProviderRoleInfoService;
import com.izu.user.service.role.StaffRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @program: mrcar-user-core
 * @description: 角色管理-客户端
 * @author: ljw
 * @create: 2023-01-17 14:12
 **/
@RestController
public class ClientRoleInfoController {

    @Autowired
    private ClientRoleInfoService clientRoleInfoService;

    @Autowired
    private ProviderRoleInfoService providerRoleInfoService;

    @Autowired
    private StaffRoleService staffRoleService;

    @RequestMapping(UserUrlCenter.ROLE_INFO_QUERY_PAGE_COMPANY)
    @JrdApiDoc(simpleDesc = "角色列表查询" ,detailDesc = "角色列表查询" ,author = "连江伟",resDataClass = RoleInfoDTO.class)
    public RestResponse listMotorcade(@RequestBody RoleInfoReqDTO req){
        return RestResponse.success(clientRoleInfoService.getPageListForCompany(req));
    }

    @RequestMapping(UserUrlCenter.ROLE_INFO_QUERY_LIKE_COMPANY)
    @JrdApiDoc(simpleDesc = "角色名称模糊查询" ,detailDesc = "角色名称模糊查询" ,author = "连江伟",resDataClass = RoleInfoDTO.class)
    public RestResponse queryLikePrams(@RequestBody RoleInfoReqDTO req){
        return RestResponse.success(clientRoleInfoService.queryLikePramsForCompany(req));
    }

    @RequestMapping(UserUrlCenter.ROLE_INFO_CREATE_COMPANY)
    @JrdApiDoc(simpleDesc = "保存角色信息" ,detailDesc = "保存角色信息" ,author = "连江伟",resDataClass = RoleInfoDTO.class)
    public RestResponse saveRoleInfo(@RequestBody RoleInfoSaveDTO req){
        return clientRoleInfoService.saveRoleInfo(req);
    }

    @RequestMapping(UserUrlCenter.ROLE_INFO_CHECK_NAME_COMPANY)
    @JrdApiDoc(simpleDesc = "校验角色名称" ,detailDesc = "校验角色名称" ,author = "连江伟",resDataClass = RoleInfoDTO.class)
    public RestResponse checkNameValid(@RequestBody RoleInfoSaveDTO req){
        return providerRoleInfoService.checkNameValid(req);
    }

    @RequestMapping(UserUrlCenter.ROLE_INFO_UPDATE_COMPANY)
    @JrdApiDoc(simpleDesc = "修改角色" ,detailDesc = "" ,author = "连江伟",resDataClass = RoleInfoDTO.class)
    public RestResponse updateRoleInfo(@RequestBody RoleInfoSaveDTO req){
        return clientRoleInfoService.updateRoleInfo(req);
    }

    @RequestMapping(UserUrlCenter.ROLE_INFO_DETAIL_COMPANY)
    @JrdApiDoc(simpleDesc = "角色详情" ,detailDesc = "" ,author = "连江伟",resDataClass = RoleInfoDTO.class)
    public RestResponse getRoleInfoDetail(Integer roleId){
        return clientRoleInfoService.getRoleInfoDetail(roleId);
    }

    @RequestMapping(UserUrlCenter.ROLE_DETAIL_COMPANY)
    @JrdApiDoc(simpleDesc = "角色详情" ,detailDesc = "" ,author = "连江伟",resDataClass = RoleInfoDTO.class)
    public RestResponse getRoleDetail(Integer roleId){
        return clientRoleInfoService.getRoleDetail(roleId);
    }

    @RequestMapping(UserUrlCenter.ROLE_INFO_CHANGE_STATUS_COMPANY)
    @JrdApiDoc(simpleDesc = "启用停用角色" ,detailDesc = "" ,author = "连江伟",resDataClass = RoleInfoDTO.class)
    public RestResponse changeStatus(Integer roleId,Boolean status,Integer userId,String userName){
        return providerRoleInfoService.changeStatus(roleId,status,userId,userName);
    }

    @RequestMapping(UserUrlCenter.ROLE_INFO_RELATION_STAFF_COMPANY)
    @JrdApiDoc(simpleDesc = "分配角色指定用户" ,detailDesc = "" ,author = "连江伟",resDataClass = RoleInfoDTO.class)
    public RestResponse assignUserToRole(@RequestBody RoleUserSaveDTO saveDTO){
        return providerRoleInfoService.assignUserToRole(saveDTO);
    }

    @PostMapping(UserUrlCenter.CLIENT_ROLE_GET_PERM_SCOPE_LIST)
    public RestResponse getPermScopeList(@Verify(param = "companyCode",rule = "required") String companyCode) {
        return RestResponse.success(clientRoleInfoService.getPermScopeList(companyCode,null));
    }

    @PostMapping(UserUrlCenter.CLIENT_ROLE_GET_STAFF_LIST)
    public RestResponse getStaffList(@RequestBody @Validated RoleInfoGetStaffReqDTO reqDTO) {
        return RestResponse.success(clientRoleInfoService.getStaffList(reqDTO));
    }

    @PostMapping(UserUrlCenter.ROLE_INFO_SELECT_USER)
    public RestResponse getSelectUserList(@RequestBody RoleUserReqDTO reqDTO) {
        return RestResponse.success(clientRoleInfoService.getSelectUserList(reqDTO));
    }

    @PostMapping(UserUrlCenter.ROLE_INFO_HAVE_COUNT)
    public RestResponse getSelectUserList(String roleCode) {
        return RestResponse.success(clientRoleInfoService.getCountOfRoleUser(roleCode));
    }

    @PostMapping(UserUrlCenter.ROLE_INFO_STAFF_CODE)
    public RestResponse getRoleUserCodeList(String roleCode,String companyCode,Byte systemType) {
        return RestResponse.success(staffRoleService.getStaffCodeListOfRole(roleCode,companyCode,systemType));
    }


}
