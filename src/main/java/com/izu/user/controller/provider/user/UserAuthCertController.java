package com.izu.user.controller.provider.user;

import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.dto.customer.CustomerAuthAddReqDTO;
import com.izu.user.dto.customer.CustomerAuthListReqDTO;
import com.izu.user.dto.customer.CustomerAuthUpdateReqDTO;
import com.izu.user.service.user.UserAuthCertService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @description: 双证管理
 * @date 2024/8/7 8:32
 */
@RestController
@Slf4j
public class UserAuthCertController {

    @Autowired
    private UserAuthCertService userAuthCertService;


    /**
     * 双证管理列表查询
     *
     * @param customerAuthListReqDTO
     * @return
     */
    @PostMapping(UserUrlCenter.PROVIDER_USER_AUTH_LIST)
    public RestResponse getUserAuthList(@RequestBody CustomerAuthListReqDTO customerAuthListReqDTO) {
        return RestResponse.success(userAuthCertService.getUserAuthList(customerAuthListReqDTO));
    }

    /**
     * 双证管理新增
     *
     * @param customerAuthAddReqDTO
     * @return
     */
    @PostMapping(UserUrlCenter.PROVIDER_ADD_USER_AUTH)
    public RestResponse addUserAuth(@RequestBody @Valid CustomerAuthAddReqDTO customerAuthAddReqDTO) {
        return userAuthCertService.addUserAuth(customerAuthAddReqDTO);
    }
    /**
     * 双证管理编辑
     *
     * @param customerAuthUpdateReqDTO
     * @return
     */
    @PostMapping(UserUrlCenter.PROVIDER_UPDATE_USER_AUTH)
    public RestResponse updateUserAuth(@RequestBody @Valid CustomerAuthUpdateReqDTO customerAuthUpdateReqDTO) {
        return userAuthCertService.updateUserAuth(customerAuthUpdateReqDTO);
    }
    /**
     * 双证管理-详情
     *
     * @param id
     * @return
     */
    @GetMapping(UserUrlCenter.PROVIDER_USER_AUTH_DETAIL)
    public RestResponse getUserAuthDetail(@RequestParam(value = "id") @Verify(param = "id", rule = "required") Integer id) {
        return RestResponse.success(userAuthCertService.getUserAuthDetail(id));
    }
    /**
     * 双证管理-详情
     *
     * @param companyId
     * @param mobile
     * @return
     */
    @GetMapping(UserUrlCenter.PROVIDER_USER_AUTH_STATUS)
    public RestResponse getAuthStatus(@RequestParam(value = "companyId") @Verify(param = "companyId", rule = "required") Integer companyId,
                                      @RequestParam(value = "mobile") @Verify(param = "mobile", rule = "required") String mobile) {
        return RestResponse.success(userAuthCertService.getAuthStatus(companyId,mobile));
    }

    /**
     * 双证管理新增-H5
     *
     * @param customerAuthAddReqDTO
     * @return
     */
    @PostMapping(UserUrlCenter.CLIENT_USER_AUTH_SAVE_INFO)
    public RestResponse saveDriverCardInfo(@RequestBody CustomerAuthAddReqDTO customerAuthAddReqDTO) {
        return userAuthCertService.saveDriverCardInfo(customerAuthAddReqDTO);
    }

    /**
     * 双证管理-详情-H5
     *
     * @param companyId
     * @param mobile
     * @return
     */
    @GetMapping(UserUrlCenter.H5_USER_AUTH_INFO)
    public RestResponse queryCardInfo(@RequestParam(value = "companyId") @Verify(param = "companyId", rule = "required") Integer companyId,
                                      @RequestParam(value = "mobile") @Verify(param = "mobile", rule = "required") String mobile) {
        return RestResponse.success(userAuthCertService.queryCardInfo(companyId,mobile));
    }
}
