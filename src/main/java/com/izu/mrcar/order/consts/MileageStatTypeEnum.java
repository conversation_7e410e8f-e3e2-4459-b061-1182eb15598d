package com.izu.mrcar.order.consts;

import lombok.Getter;

import java.util.Objects;

/**
 * 里程统计类型
 * <AUTHOR>
 * @date : 2022/11/30
 */
@Getter
public enum MileageStatTypeEnum {
    DEFAULT(Byte.valueOf("0"),"默认"),
    BAIDU_YING_YAN(Byte.valueOf("1"), "百度鹰眼"),
    OBD(Byte.valueOf("2"), "车机上报"),
    APP_GPS(Byte.valueOf("3"), "APP GPS上报"),
    CAR_GPS(Byte.valueOf("4"), "车载GPS上报"),
    MANUAL(Byte.valueOf("5"), "人工上报"),
    SYSTEM_CORRECT(Byte.valueOf("6"),"系统纠偏");

    private Byte code;

    private String desc;

    MileageStatTypeEnum(Byte code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static MileageStatTypeEnum getMileageStatTypeEnum(Byte code){
        for(MileageStatTypeEnum statTypeEnum:values()){
            if(Objects.equals(statTypeEnum.code,code)){
                return statTypeEnum;
            }
        }
        return null;
    }

    //通过code获取desc
    public static String getDescByCode(Byte code){
    	for(MileageStatTypeEnum statTypeEnum:values()){
    		if(Objects.equals(statTypeEnum.code,code)){
    			return statTypeEnum.desc;
    		}
    	}
    	return "";
    }

}
