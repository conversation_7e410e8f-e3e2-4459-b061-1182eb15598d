package com.izu.mrcar.order.consts;

public enum AttachmentTypeEnum {
        ORDER_PRICE((byte) 1, "订单调价"),
        ORDER_SETTLE((byte) 2, "订单结算"),
        ORDER_QUESTION((byte) 3, "问题反馈")
        ;
        //--------------------------------------------
        private Byte value = -1;
        private String text = "";

        private AttachmentTypeEnum(byte value, String text) {
            this.value = value;
            this.text = text;
        }

        public static AttachmentTypeEnum getByValue(byte value) {
            for (AttachmentTypeEnum typeEnum : values()) {
                if (typeEnum.value == value ) {
                    return typeEnum;
                }
            }
            return null;
        }

        public Byte value() {
            return this.value;
        }

        public String text() {
            return this.text;
        }
}
