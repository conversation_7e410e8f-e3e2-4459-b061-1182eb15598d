package com.izu.mrcar.order.consts.govCar;

import lombok.AllArgsConstructor;
import lombok.Getter;
import java.util.Arrays;

/**
 * 公务用车订单取消单枚举
 * <AUTHOR>
 */
public class GovCarOrderCancellationEnums {

    /**
     * 订单类型
     */
    @Getter
    @AllArgsConstructor
    public enum OrderType {
        RENTAL_ORDER((byte) 1, "租车订单"),
        ADDITIONAL_FEE_ORDER((byte) 2, "附加费订单");

        private final Byte type;
        private final String description;

        public static String getName(Byte type) {
            return Arrays.stream(OrderType.values())
                    .filter(e -> e.getType().equals(type))
                    .findAny()
                    .map(OrderType::getDescription)
                    .orElse(null);
        }
    }

    /**
     * 取消类型
     */
    @Getter
    @AllArgsConstructor
    public enum CancellationType {
        USER_CANCEL((byte) 0, "用户取消"),
        SYSTEM_CANCEL((byte) 1, "系统取消"),
        ADMIN_CANCEL((byte) 2, "管理员取消");

        private final Byte type;
        private final String description;

        public static String getName(Byte code) {
            return Arrays.stream(CancellationType.values())
                    .filter(e -> e.getType().equals(code))
                    .findAny()
                    .map(CancellationType::getDescription)
                    .orElse(null);
        }
    }
}
