package com.izu.mrcar.order.consts.workstatus;

import com.izu.framework.web.exception.ExceptionFactory;

/**
 * <AUTHOR> on  2025/3/8 11:24
 */
public enum EntityType {

    VEHICLE(1, "车辆"),

    DRIVER(2, "司机");

    private final int code;

    private final String description;

    EntityType(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static EntityType fromCode(int code) {
        for (EntityType type : EntityType.values()) {
            if (type.code == code) {
                return type;
            }
        }
        throw ExceptionFactory.createRestException("无效的实体类型代码: " + code);
    }


}
