package com.izu.mrcar.order.consts.daibuche;

import org.apache.commons.lang.StringUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * @Program: mrcar-order-common
 * @Description: 订单费用项枚举
 * @Author: ty
 * @Create: 2024-05-15
 */
@Getter
@AllArgsConstructor
public enum OrderFeeEnum {
    FEE_RENT("01", "租金"),
    FEE_OVER_MILE("02", "配送超里程费"),
    FEE_PETROL("03", "加油费"),
    FEE_VIOLATE("04", "违章费"),
    FEE_PICK_UP("05", "取送车服务费"),
    FEE_OTHER("99", "其他费用"),
    FEE_OTHER_DECREASE("98", "其他核减费用"),
    ;

    /**
     * 费用项编码
     */
    private String feeCode;
    /**
     * 费用项名称
     */
    private String feeName;

    public static OrderFeeEnum getEnumByFeeCode(String feeCode) {
        return Arrays.stream(OrderFeeEnum.values()).filter(e -> StringUtils.equals(e.getFeeCode(), feeCode)).findAny().orElse(null);
    }

    public static String getFeeNameByFeeCode(String feeCode) {
        OrderFeeEnum orderFeeEnum = getEnumByFeeCode(feeCode);
        return orderFeeEnum != null ? orderFeeEnum.getFeeName() : StringUtils.EMPTY;
    }

}
