package com.izu.mrcar.order.consts.daibuche;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;


@Getter
@AllArgsConstructor
public enum RequestStatusEnum {

    FAIL(0,"失败"),
    SUCCESS(1,"成功"),
    ;

    private Integer code;

    private String name;

    public static String getName(Integer code) {
        if(Objects.nonNull(code)){
            for (RequestStatusEnum requestStatusEnum : RequestStatusEnum.values()) {
                if (requestStatusEnum.getCode().equals(code)) {
                    return requestStatusEnum.getName();
                }
            }
        }
        return null;
    }

}
