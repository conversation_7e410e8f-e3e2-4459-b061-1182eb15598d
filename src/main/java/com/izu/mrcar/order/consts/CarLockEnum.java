package com.izu.mrcar.order.consts;

/**
 * <AUTHOR>
 * @date 2019-12-24 15:16
 */
public class CarLockEnum {

    public enum Message {

        CLOSE_FAILED((byte) 0, (byte) 0,"关锁失败,请检查车辆状态（车窗及是否熄火）"),
        CLOSE_SUCCESS((byte) 0, (byte) 1,"关锁成功"),
        CLOSE_TIMEOUT((byte) 0, (byte) 2,"关锁超时"),
        CLOSE_DISTANCE_TOO_LONG((byte) 0, (byte) 3,"关锁距离超过100米"),
        CLOSE_CAR_STATUS_ERROR((byte) 0, (byte) 4,"车辆已被锁定"),
        CLOSE_LOCK_STATUS_ERROR((byte) 0, (byte) 5,"用户不存在占用车辆"),
        OPEN_FAILED((byte) 1, (byte) 0,"开锁失败"),
        OPEN_SUCCESS((byte) 1, (byte) 1,"开锁成功"),
        OPEN_TIMEOUT((byte) 1, (byte) 2,"开锁超时"),
        OPEN_DISTANCE_TOO_LONG((byte) 1, (byte) 3,"开锁距离超过100米"),
        OPEN_CAR_STATUS_ERROR((byte) 1, (byte) 4,"车辆已被占用"),
        OPEN_LOCK_STATUS_ERROR((byte) 1, (byte) 5,"用户已占用其他车辆")
        ;

        Message(byte type, byte code, String msg) {
            this.type = type;
            this.code = code;
            this.msg = msg;
        }

        private byte type;
        private byte code;
        private String msg;

        public byte getType() {
            return type;
        }

        public byte getCode() {
            return code;
        }

        public String getMsg() {
            return msg;
        }

        public static Message getMessage(byte type, byte code){
            for (Message message : values()){
                if (message.type == type && message.code == code){
                    return message;
                }
            }
            throw new IllegalArgumentException("IllegalArgument code: " + type + ", code:" + code);
        }
    }

    public enum Type {

        FAILED((byte) 0, "失败"),
        SUCCESS((byte) 1,"成功"),
        TIMEOUT((byte) 2,"超时"),
        DISTANCE((byte) 3,"距离"),
        STATUS((byte) 4,"车辆状态"),
        LOCK_STATUS((byte) 5,"锁定状态")
        ;

        Type(byte code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        private byte code;
        private String msg;


        public static Type getTypeByCode(byte code){
            for (Type type : values()){
                if (type.code == code){
                    return type;
                }
            }
            throw new IllegalArgumentException("IllegalArgument code");
        }

        public byte getCode() {
            return code;
        }

        public String getMsg() {
            return msg;
        }
    }

    public enum Operation{
        CLOSE((byte) 0,"关锁"),
        OPEN((byte) 1,"开锁");

        private byte code;
        private String msg;

        Operation(byte code, String msg) {
            this.code = code;
            this.msg = msg;
        }

        public byte getCode() {
            return code;
        }

        public String getMsg() {
            return msg;
        }
    }
}
