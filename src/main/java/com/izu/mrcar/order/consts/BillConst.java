package com.izu.mrcar.order.consts;

import com.izu.mrcar.order.dto.mrcar.BillAttachDTO;
import lombok.Getter;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
* @Description: 账单常量定义
* @author: hxc
* @Date: 2019/9/8
**/
public class BillConst {

	/**
	 * 策略不设置基础费信息
	 */
	public static final int UNSET_BASE_FEE=4;
	/**
	 * 策略设置基础费信息
	 */
	public static final int SET_BASE_FEE=1;
	/**
	 * 按照公里定价策略
	 */
	public static final int MILEAGE_POLICY=1;
	/**
	 * 按照时间定价策略
	 */
	public static final int TIME_POLICY=2;
	/**
	 * 分段定价策略
	 */
	public static final int SECTION_POLICY=3;

	/**
	 * 需要计算夜间服务费
	 */
	public static final int NEED_CALCULATE_NIGHT_SERVICE_FEE = 1;

	/**
	 * 需要计算反空里程费
	 */
	public static final int NEED_CALCULATE_RETURN_EMPTY_FEE = 1;

	/**
	 * 计费模式
	 */
	public enum ChargeType{
		/**
		 * 标准
		 */
		STANDARD(1),
		/**
		 * 向上取整
		 */
		ROUND_UP(2),
		/**
		 * 向下取整
		 */
		ROUND_DOWN(3);

		private int value;

		public int getValue() {
			return value;
		}

		private ChargeType(int value) {
			this.value = value;
		}
	}


	public enum SECTION_FEE_TYPE{
		/**
		 * 基础费类型按次收费
		 */
		TIMES(1),
		/**
		 * 基础费类型按天收费
		 */
		DAYS(2);

		private int value;

		public int getValue() {
			return value;
		}

		private SECTION_FEE_TYPE(int value) {
			this.value = value;
		}
	}

	public enum BASE_PRICE_TYPE{
		/**
		 * 计价单位：元/公里
		 */
		MILEAGE(1),
		/**
		 * 计价单位：元/分
		 */
		MINUTE(2),
		/**
		 * 计价单位：元/小时
		 */
		HOUR(3);

		private int value;

		public int getValue() {
			return value;
		}

		private BASE_PRICE_TYPE(int value) {
			this.value = value;
		}
	}

	/**账单：附加费类型**/
	public enum BillAttachType {
		// 1：停车费；2：路桥费；3：住宿费；4：餐饮费；5：其他费
		/**
		 * 返空里程费，这个费用不在附加费用表展示
		 * 写到此处的目的只是因为调价页面将其归为司机附加费用，方便展示
		 */
		EMPTY_MILEAGE_FEE((byte) -1, "返空里程费", 5,""),
		PARKING_FEE((byte) 1, "停车费", 1,"parkingFee"),
		TOLL_FEE((byte) 2, "路桥费", 2,"tollFee"),
		QUARTERAGE_FEE((byte) 3, "住宿费", 3,"quarterageFee"),
		MEALS_FEE((byte) 4, "餐饮费", 4,"mealsFee"),
		OTHER_FEE((byte) 5, "其他费", 6,"otherFee"),

		REMARK((byte) 99,"备注",7,"remarks");
		//--------------------------------------------
		private byte value = -1;
		private String text = "";
		private Integer sort;
		//对应司机补录费用接口中的字段名称
		private String requestParamName;

		BillAttachType(byte value, String text, Integer sort, String requestParamName) {
			this.value = value;
			this.text = text;
			this.sort = sort;
			this.requestParamName = requestParamName;
		}

		public static String getText(byte value){
			return Arrays.stream(BillAttachType.values())
					.filter(e -> e.value == value)
					.findAny()
					.map(BillAttachType::text)
					.orElse(StringUtils.EMPTY);
		}

		public static BillAttachType getByValue(byte value) {
			for (BillAttachType attachType : values()) {
				if (attachType.value == value ) {
					return attachType;
				}
			}
			return null;
		}

		public static List<BillAttachType> getAttachPrices() {
			return Arrays.stream(BillAttachType.values())
					.sorted(Comparator.comparing(BillAttachType::getSort))
					.collect(Collectors.toList());
		}




		public byte value() {
			return this.value;
		}

		public String text() {
			return this.text;
		}

		public Integer getSort() {
			return sort;
		}

		public String getRequestParamName() {
			return requestParamName;
		}
	}

	@Getter
	public enum BillStatusEnum {
		ENTRY_PRICE(Byte.valueOf("11"), "费用补录中"),
		WAIT_SETTLE(Byte.valueOf("12"), "订单待结算"),
		SETTLED(Byte.valueOf("13"), "已结算"),
		;

		private Byte code;

		private String desc;

		BillStatusEnum(Byte code, String desc) {
			this.code = code;
			this.desc = desc;
		}
	}

	/**
	 * 百度鹰眼返回结果
	 * <AUTHOR>
	 *
	 */
	public enum BAIDULDB_RESULT{
		//成功
		SUCCESS(0),
		//该服务响应超时或系统内部错误
		OUTTIME(1),
		//参数错误
		PARAMETER(2),
		//http method错误
		METHOD_ERROR(3),
		//指定entity 不存在
		ENTITY_NOT_EXIST(3003),
		//查询时间段内的轨迹点过多，无法进行纠偏，请缩短查询时间
		TRACK_TOO_MANY_TO_PROCESS(3006),
		//loc_time太晚
		LOC_TIME_TOO_LATE(3100),
		//起止时间差不可超过24小时
		TIME_DIFF_OVER_24(3200),
		//结束时间不可小于开始时间
		START_TIME_BIGGER_END(3201),
		//查询时间段内的轨迹点过多,无法进行轨迹分析
		TRACK_TOO_MANY_TO_ANALYSIS(9001);

		private int value;

		public int getValue() {
			return value;
		}

		private BAIDULDB_RESULT(int value) {
			this.value = value;
		}


	}

	//百度鹰眼查数据时默认分页条数
	public static final int BAIDU_LBS_PAGESIZE=5000;
	public static final int BAIDU_LBS_PAGENUM=1;
}
