package com.izu.mrcar.order.consts.daibuche;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 操作类型 10调度 20送车 30还车 40补录
 */
@Getter
@AllArgsConstructor
public enum OperateTypeEnum {

    DISPATCH(10,"确认调度"),
    DELIVERY(20,"确认送达"),
    RETURN(30,"确认还车"),
    ADDITIONAL(40,"费用补录"),
    CREATE_ORDER(50,"创建订单"),
    CANCEL_ORDER(60,"取消订单"),
    MODIFY_ORDER(70,"修改订单"),
    WAIVE_DEPOSIT(80,"免付押金"),
    DEDUCT_DEPOSIT(90,"押金扣款"),
    UNFREEZE_DEPOSIT(100,"押金解冻"),
    ;

    private Integer code;

    private String name;

    public static String getName(int code) {
        for (OperateTypeEnum operateTypeEnum : OperateTypeEnum.values()) {
            if (operateTypeEnum.getCode() == code) {
                return operateTypeEnum.getName();
            }
        }
        return null;
    }

}
