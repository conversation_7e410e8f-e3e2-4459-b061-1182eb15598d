package com.izu.mrcar.order.consts.govPublicCar;

import com.izu.mrcar.order.dto.temporalSharedVehicle.DataPermDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/8/23 10:46
 */
@Data
public class ProviderPublicGovCarOrderOptReqDTO extends DataPermDTO {

    @ApiModelProperty(value = "订单号", required = true)
    private String orderNo;

    @ApiModelProperty(value = "订单所属企业id", required = true)
    private Integer orderCompanyId;

    @ApiModelProperty(value = "订单所属企业编码", required = true)
    private String orderCompanyCode;

    @ApiModelProperty(value = "当前登录人手机号", hidden = true)
    private String loginUserMobile;




}
