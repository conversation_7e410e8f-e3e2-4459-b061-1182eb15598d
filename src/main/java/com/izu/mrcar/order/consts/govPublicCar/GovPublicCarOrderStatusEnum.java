package com.izu.mrcar.order.consts.govPublicCar;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;


@Getter
@AllArgsConstructor
public enum GovPublicCarOrderStatusEnum {

    //订单状态 0-空；1-待出发 10-用车中 20-待核实 30-已完成 100-已取消
    NONE((byte)0, "空"),
    PENDING_DEPARTURE((byte)1, "待出发"),
    IN_USE((byte)10, "用车中"),
    PENDING_VERIFICATION((byte)20, "待核实"),
    COMPLETED((byte)30, "已完成"),
    CANCELED((byte)100, "已取消");

    private final Byte code;
    private final String name;

    public static String getName(Byte code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findFirst().map(GovPublicCarOrderStatusEnum::getName).orElse(null);
    }

    public static GovPublicCarOrderStatusEnum getByValue(byte value) {
        for (GovPublicCarOrderStatusEnum dateType : values()) {
            if (dateType.code == value ) {
                return dateType;
            }
        }
        return null;
    }
}
