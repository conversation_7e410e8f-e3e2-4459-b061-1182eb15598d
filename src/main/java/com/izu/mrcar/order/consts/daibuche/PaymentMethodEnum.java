package com.izu.mrcar.order.consts.daibuche;

import lombok.Getter;

@Getter
public enum PaymentMethodEnum {

    ALIPAY_PRE_AUTH((byte) 1, "支付宝预授权");

    private final byte code;
    private final String name;

    PaymentMethodEnum(byte code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据code查找对应的name
     *
     * @param code 支付方式code
     * @return 支付方式name，如果未找到返回null
     */
    public static String getNameByCode(byte code) {
        for (PaymentMethodEnum method : PaymentMethodEnum.values()) {
            if (method.getCode() == code) {
                return method.getName();
            }
        }
        return null; // 如果未找到对应的code，返回null
    }
}
