package com.izu.mrcar.order.consts.govCar;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 验车单类型
 * <p>
 * 2024/8/7 下午4:13
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum GovCarCheckRecordTypeEnum {

    GAIN_CAR((byte) 10, "取车验车"),
    BACK_CAR((byte) 20, "还车验车"),
    ;

    private final Byte code;
    private final String name;

    public static String getName(Byte code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findFirst().map(GovCarCheckRecordTypeEnum::getName).orElse(null);
    }
}
