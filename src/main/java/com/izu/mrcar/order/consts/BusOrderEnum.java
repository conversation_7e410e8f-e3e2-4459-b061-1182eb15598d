package com.izu.mrcar.order.consts;

import lombok.Data;

import java.util.Arrays;
import java.util.List;

/**
 * 大巴车订单相关枚举
 * <AUTHOR>
 * */
public class BusOrderEnum {

    /**订单状态**/
    public enum OrderStatus {
        PENDING_DISPATCH(10, "待调度"),
        PENDING_DEPARTURE(20, "待出发"),
        DRIVER_DEPARTED(30, "司机已出发"),
        TRIP_IN_PROGRESS(40, "行程中"),
        PENDING_CHECK_IN(50, "待收车"),
        TASK_PAUSED(60, "任务暂停"),
        COST_ENTRY_IN_PROGRESS(70, "费用录入中"),
        PENDING_SETTLEMENT(80, "待结算"),
        SETTLEMENT_APPROVAL_IN_PROGRESS(90, "结算审批中"),
        SETTLEMENT_APPROVAL_REJECTED(100, "结算审批驳回"),
        COMPLETED(110, "已完成"),
        CANCELLED(120, "已取消");

        private final int code;
        private final String desc;

        OrderStatus(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }


        public static OrderStatus getByCode(int code) {
            for (OrderStatus status : OrderStatus.values()) {
                if (status.getCode() == code) {
                    return status;
                }
            }
            return null;
        }

        public static String getDescByCode(int code) {
            for (OrderStatus status : OrderStatus.values()) {
                if (status.getCode() == code) {
                    return status.getDesc();
                }
            }
            return "";
        }

        public static final List<Integer> PENDING_STATUSES = Arrays.asList(
                BusOrderEnum.OrderStatus.PENDING_DISPATCH.getCode(),
                BusOrderEnum.OrderStatus.PENDING_DEPARTURE.getCode()
        );

        public static final List<Integer> DIVER_PARTED_STATUSES = Arrays.asList(
                BusOrderEnum.OrderStatus.PENDING_DEPARTURE.getCode(),
                BusOrderEnum.OrderStatus.DRIVER_DEPARTED.getCode(),
                BusOrderEnum.OrderStatus.TRIP_IN_PROGRESS.getCode(),
                BusOrderEnum.OrderStatus.TASK_PAUSED.getCode(),
                BusOrderEnum.OrderStatus.PENDING_CHECK_IN.getCode(),
                BusOrderEnum.OrderStatus.COST_ENTRY_IN_PROGRESS.getCode()
        );


        public static final List<Integer> DIVER_USE_STATUSES = Arrays.asList(
                BusOrderEnum.OrderStatus.DRIVER_DEPARTED.getCode(),
                BusOrderEnum.OrderStatus.TRIP_IN_PROGRESS.getCode(),
                BusOrderEnum.OrderStatus.TASK_PAUSED.getCode(),
                BusOrderEnum.OrderStatus.PENDING_CHECK_IN.getCode(),
                BusOrderEnum.OrderStatus.COST_ENTRY_IN_PROGRESS.getCode()
        );

        public static final List<Integer> SETTLEMENT_STATUSES = Arrays.asList(
                BusOrderEnum.OrderStatus.PENDING_SETTLEMENT.getCode(),
                BusOrderEnum.OrderStatus.SETTLEMENT_APPROVAL_IN_PROGRESS.getCode(),
                BusOrderEnum.OrderStatus.SETTLEMENT_APPROVAL_REJECTED.getCode()
        );

        public static final List<Integer> FINISH_STATUSES = Arrays.asList(
                BusOrderEnum.OrderStatus.PENDING_SETTLEMENT.getCode(),
                BusOrderEnum.OrderStatus.COMPLETED.getCode(),
                BusOrderEnum.OrderStatus.SETTLEMENT_APPROVAL_IN_PROGRESS.getCode(),
                BusOrderEnum.OrderStatus.SETTLEMENT_APPROVAL_REJECTED.getCode()
        );

        public static final List<Integer> COMPLETED_STATUSES = Arrays.asList(
                BusOrderEnum.OrderStatus.COMPLETED.getCode(),
                BusOrderEnum.OrderStatus.CANCELLED.getCode()
        );


        public static final List<Integer> DISPATCH_COMPLETED_STATUSES = Arrays.asList(
                BusOrderEnum.OrderStatus.DRIVER_DEPARTED.getCode(),
                BusOrderEnum.OrderStatus.TRIP_IN_PROGRESS.getCode(),
                BusOrderEnum.OrderStatus.PENDING_CHECK_IN.getCode(),
                BusOrderEnum.OrderStatus.TASK_PAUSED.getCode(),
                BusOrderEnum.OrderStatus.COST_ENTRY_IN_PROGRESS.getCode(),
                BusOrderEnum.OrderStatus.PENDING_SETTLEMENT.getCode(),
                BusOrderEnum.OrderStatus.SETTLEMENT_APPROVAL_IN_PROGRESS.getCode(),
                BusOrderEnum.OrderStatus.SETTLEMENT_APPROVAL_REJECTED.getCode(),
                BusOrderEnum.OrderStatus.COMPLETED.getCode(),
                BusOrderEnum.OrderStatus.CANCELLED.getCode()
                );

        @Override
        public String toString() {
            return "{" +
                    "code=" + code +
                    ", desc=" + desc + "}";
        }
    }


    /**订单创建类型**/
    public enum OrderCreationType {
        SCHEDULED_TASK(1, "排班创建"),
        MANUAL_CREATION(2, "正常创建"),
        ORDER_REENTRY(3, "补录创建");

        private final int code;
        private final String desc;

        OrderCreationType(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }


        public static OrderCreationType getByCode(int code) {
            for (OrderCreationType status : OrderCreationType.values()) {
                if (status.getCode() == code) {
                    return status;
                }
            }
            return null;
        }

        public static String getDescByCode(int code) {
            for (OrderCreationType status : OrderCreationType.values()) {
                if (status.getCode() == code) {
                    return status.getDesc();
                }
            }
            return "";
        }


        @Override
        public String toString() {
            return "{" +
                    "code=" + code +
                    ", desc=" + desc + "}";
        }
    }


    /**业务类型**/
    public enum BusinessType {
        BUS(1, "班车"),
        TEMPORARY(2, "临时"),
        CONTRACT(3, "承包"),
        LEASE(4, "租赁"),
        NON_OPERATIONAL(5, "非营业");

        private final int code;
        private final String desc;

        BusinessType(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }



        public static BusinessType getByCode(int code) {
            for (BusinessType status : BusinessType.values()) {
                if (status.getCode() == code) {
                    return status;
                }
            }
            return null;
        }
        public static String getDescByCode(int code) {
            for (BusinessType status : BusinessType.values()) {
                if (status.getCode() == code) {
                    return status.getDesc();
                }
            }
            return "";
        }

        @Override
        public String toString() {
            return "{" +
                    "code=" + code +
                    ", desc=" + desc + "}";
        }
    }


    /**路线规划策略 **/
    public enum RouteType {
        SHORTEST_DISTANCE(1, "最短距离"),
        SHORTEST_TIME(0, "最短时间");
        private final int code;
        private final String desc;

        RouteType(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }


        public static RouteType getByCode(int code) {
            for (RouteType status : RouteType.values()) {
                if (status.getCode() == code) {
                    return status;
                }
            }
            return null;
        }
        public static String getDescByCode(int code) {
            for (RouteType status : RouteType.values()) {
                if (status.getCode() == code) {
                    return status.getDesc();
                }
            }
            return "";
        }

        @Override
        public String toString() {
            return "{" +
                    "code=" + code +
                    ", desc=" + desc + "}";
        }
    }


    /**调度策略 **/
    public enum DispatchType {
        NO_DISPATCH(0, "无需调度"),
        DISPATCH(1, "由调度员派车");
        private final int code;
        private final String desc;

        DispatchType(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
        public static String getDescByCode(int code) {
            for (DispatchType status : DispatchType.values()) {
                if (status.getCode() == code) {
                    return status.getDesc();
                }
            }
            return "";
        }
        @Override
        public String toString() {
            return "{" +
                    "code=" + code +
                    ", desc=" + desc + "}";
        }
    }

    /**行程类型 **/
    public enum TraceType {
        EMPTY_TRIP(1, "空驶行程"),
        OPERATIONAL_TRIP(2, "运营行程");
        private final int code;
        private final String desc;

        TraceType(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }


        public static TraceType getByCode(int code) {
            for (TraceType status : TraceType.values()) {
                if (status.getCode() == code) {
                    return status;
                }
            }
            return null;
        }


        public static String getDescByCode(int code) {
            for (TraceType status : TraceType.values()) {
                if (status.getCode() == code) {
                    return status.getDesc();
                }
            }
            return "";
        }
        @Override
        public String toString() {
            return "{" +
                    "code=" + code +
                    ", desc=" + desc + "}";
        }
    }


    /** 日志操作项 **/
    public enum LogOperatorItem
    {
        CREATE_ORDER(0, "新建订单"),
        CANCEL_ORDER(10, "取消订单"),
        SCHEDULED(20, "调度"),
        RESCHEDULE(30, "重新调度"),
        DRIVER_DEPARTURE(40, "司机出发"),
        PAUSE_TASK(50, "暂停任务"),
        PASSENGER_ON_BOARD(60, "乘客已上车"),
        ARRIVED_AT_START_POINT(70, "到达起点站"),
        PASSENGER_OFF_BOARD(80, "乘客已下车"),
        ARRIVED_AT_END_POINT(90, "到达终点站"),
        END_OF_SERVICE(100, "收车结束"),
        CONTINUE_TASK(110, "继续任务"),
        ENTER_FEES(120, "录入费用"),
        ADJUST_PRICE(130, "调价"),
        CONFIRM_SETTLEMENT(140, "确认结算"),
        EXPORT_SETTLEMENT_SHEET(150, "结算单导出"),
        WITHDRAW_SETTLEMENT_APPROVE(160, "撤回结算审批"),
        APPROVE_PASS(170, "审批通过"),
        APPROVE_REJECT(180, "审批驳回");



        private final int code;
        private final String desc;

        LogOperatorItem(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
        public static String getDescByCode(int code) {
            for (LogOperatorItem status : LogOperatorItem.values()) {
                if (status.getCode() == code) {
                    return status.getDesc();
                }
            }
            return "";
        }

        public static LogOperatorItem getByCode(int code) {
            for (LogOperatorItem status : LogOperatorItem.values()) {
                if (status.getCode() == code) {
                    return status;
                }
            }
            return null;
        }

        @Override
        public String toString() {
            return "{" +
                    "code=" + code +
                    ", desc=" + desc + "}";
        }
    }
}
