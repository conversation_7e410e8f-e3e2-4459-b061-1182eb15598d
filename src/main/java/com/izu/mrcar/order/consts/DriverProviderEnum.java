package com.izu.mrcar.order.consts;

/**
 * <AUTHOR> on  2024/4/8 10:22
 */
public enum DriverProviderEnum {


    SQ((byte) 1, "首汽司机"),
    THIRD_PARTY((byte) 2, "三方司机");

    private final byte value;
    private final String description;

    DriverProviderEnum(byte value, String description) {
        this.value = value;
        this.description = description;
    }

    public byte getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public static DriverProviderEnum fromValue(byte value) {
        for (DriverProviderEnum provider : DriverProviderEnum.values()) {
            if (provider.getValue() == value) {
                return provider;
            }
        }
        throw new IllegalArgumentException("Invalid DriverProvider value: " + value);
    }

}
