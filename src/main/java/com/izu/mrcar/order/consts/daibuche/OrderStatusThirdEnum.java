package com.izu.mrcar.order.consts.daibuche;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

@Getter
@AllArgsConstructor
public enum OrderStatusThirdEnum {

    END(0,"结束"),
    TO_BE_CONFIRM(1,"待确认"),
    TO_BE_PAY(4,"待支付"),
    TO_BE_DISPATCH(8,"待调度"),
    TO_BE_TAKE(16,"待取车"),
    TO_BE_RETURN(32,"待还车"),
    TO_BE_SETTLE(64,"待结算"),
    TO_BE_VIOLATION_SETTLE(128,"待违章结算"),
    TO_BE_CLAIM_DEAL(256,"待理赔处理"),
    COMPLETED(512,"完成"),
    ;

    private Integer code;

    private String name;

    public static String getName(Integer code) {
        if(Objects.nonNull(code)){
            for (OrderStatusThirdEnum orderStatusEnum : OrderStatusThirdEnum.values()) {
                if (orderStatusEnum.getCode().equals(code)) {
                    return orderStatusEnum.getName();
                }
            }
        }
        return null;
    }

}
