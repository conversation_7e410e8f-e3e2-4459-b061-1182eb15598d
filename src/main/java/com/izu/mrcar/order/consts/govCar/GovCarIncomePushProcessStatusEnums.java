package com.izu.mrcar.order.consts.govCar;

public enum GovCarIncomePushProcessStatusEnums {

    // 推送状态
    PUSH_STATUS_PENDING((byte) 0, "待推送", "push_status"),
    PUSH_STATUS_SUCCESS((byte) 1, "推送成功", "push_status"),
    PUSH_STATUS_FAILED((byte) 2, "推送失败", "push_status"),

    // 单据状态
    BILL_STATUS_FAILED((byte) 0, "失败", "bill_status"),
    BILL_STATUS_SUCCESS((byte) 1, "成功", "bill_status"),
    BILL_STATUS_REJECTED((byte) 2, "驳回", "bill_status"),
    BILL_STATUS_VOIDED((byte) 3, "废止", "bill_status");

    private final Byte code;
    private final String description;
    private final String type;

    GovCarIncomePushProcessStatusEnums(Byte code, String description, String type) {
        this.code = code;
        this.description = description;
        this.type = type;
    }

    public Byte getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public String getType() {
        return type;
    }

    public static GovCarIncomePushProcessStatusEnums fromCode(Byte code, String type) {
        for (GovCarIncomePushProcessStatusEnums status : GovCarIncomePushProcessStatusEnums.values()) {
            if (status.getCode().equals(code) && status.getType().equals(type)) {
                return status;
            }
        }
        return null;
    }
}

