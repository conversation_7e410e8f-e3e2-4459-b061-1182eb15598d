package com.izu.mrcar.order.consts.daibuche;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 交易类型(预授权发起8103，预授权撤销8104，预授权完成 8105)
 */
@Getter
@AllArgsConstructor
public enum TradeTypeEnum {

    PRE_AUTH_INITIATE(8103,"预授权发起"),
    PRE_AUTH_VOID(8104,"预授权撤销"),
    PRE_AUTH_COMPLETION(8105,"预授权完成"),
    ;

    private final Integer code;

    private final String name;

    public static String getName(Integer code) {
        if(Objects.nonNull(code)){
            for (TradeTypeEnum tradeTypeEnum : TradeTypeEnum.values()) {
                if (tradeTypeEnum.getCode().equals(code)) {
                    return tradeTypeEnum.getName();
                }
            }
        }
        return null;
    }

}
