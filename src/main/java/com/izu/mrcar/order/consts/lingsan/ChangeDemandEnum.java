package com.izu.mrcar.order.consts.lingsan;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Program: mrcar-order-common
 * @Description: 换车单-更换要求
 * @Author: inT
 * @Create: 2023-01-17
 */
@Getter
@AllArgsConstructor
public enum ChangeDemandEnum {
    ORIGINAL_ORDER_INFORMATION(0,"原订单信息"),
    CHANGE_VEHICLE(1,"换车"),
    TRAIN_CHANGE_AND_DRIVER(2,"换车和司机"),
    CHANGE_DRIVER(3,"换司机"),
    ADD_DRIVER(4,"新增司机"),


    ;

    private Integer type;

    private String typeDesc;

    public static String getTypeDesc(int type) {
        for (ChangeDemandEnum demandEnum : ChangeDemandEnum.values()) {
            if (demandEnum.getType() == type) {
                return demandEnum.getTypeDesc();
            }
        }
        return null;
    }
}
