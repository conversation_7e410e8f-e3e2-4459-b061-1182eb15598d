package com.izu.mrcar.order.consts;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 司机动作事件
 * @author: hxc
 * @create: 2019-09-08 20:56
 **/
public enum DriverActionEnum {

    ACTION_LOG_ON(1, "司机登录"),
    ACTION_LOG_START(2, "司机出发"),
    ACTION_LOG_ARRIVE(3, "司机到达"),
    ACTION_LOG_START_TRIP(4, "开始行程"),
    ACTION_LOG_FINISH_TRIP(5, "结束行程"),
    ACTION_LOG_ATTACH(6, "费用补录"),
    ACTION_LOG_UPLOADPHOTO(7, "服务照片上传");
    // --------------------------------------------
    private int value = -1;
    private String text  = "";
    private DriverActionEnum(int value, String text){
        this.value = value;
        this.text  = text;
    }
    public int value() {
        return this.value;
    }
    public String text() {
        return this.text;
    }

    /**司机动作是否合法**/
    public static boolean isValid( final int actionEnum) {
        List<Integer> codes = new ArrayList<>( 6 );
        codes.add( DriverActionEnum.ACTION_LOG_ON.value()  );
        codes.add( DriverActionEnum.ACTION_LOG_START.value()  );
        codes.add( DriverActionEnum.ACTION_LOG_ARRIVE.value()  );
        codes.add( DriverActionEnum.ACTION_LOG_START_TRIP.value()  );
        codes.add( DriverActionEnum.ACTION_LOG_FINISH_TRIP.value()  );
        codes.add( DriverActionEnum.ACTION_LOG_ATTACH.value()  );
        codes.add( DriverActionEnum.ACTION_LOG_UPLOADPHOTO.value()  );
        return codes.contains(actionEnum);
    }

    public static DriverActionEnum getByValue(int value) {
        for (DriverActionEnum actionEnum : values()) {
            if (actionEnum.value==(value) ) {
                return actionEnum;
            }
        }
        return null;
    }
}
