package com.izu.mrcar.order.consts.daibuche;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang.StringUtils;

import java.util.Arrays;

/**
 * '订单来源 1手动创建 2系统推送
 */
@Getter
@AllArgsConstructor
public enum OrderOperatorSourceEnum {
    SHOUQI((byte)1, "首汽操作"),
    THIRD((byte)2, "三方操作"),

    ;


    private Byte code;

    private String name;


    public static OrderOperatorSourceEnum getByCode(Byte code) {
        return Arrays.stream(OrderOperatorSourceEnum.values()).filter(e -> e.getCode().equals(code)).findAny().orElse(null);
    }

    public static String getNameByCode(Byte code) {
        OrderOperatorSourceEnum orderFeeEnum = getByCode(code);
        return orderFeeEnum != null ? orderFeeEnum.getName() : StringUtils.EMPTY;
    }

}
