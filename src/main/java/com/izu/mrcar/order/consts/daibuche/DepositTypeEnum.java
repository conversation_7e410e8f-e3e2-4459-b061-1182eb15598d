package com.izu.mrcar.order.consts.daibuche;

import lombok.Getter;

@Getter
public enum DepositTypeEnum {

    VEHICLE_DEPOSIT((byte) 1, "车辆押金"),
    VIOLATION_DEPOSIT((byte) 2, "违章押金");

    private final byte code;
    private final String name;

    DepositTypeEnum(byte code, String name) {
        this.code = code;
        this.name = name;
    }
    /**
     * 根据code查找对应的name
     *
     * @param code 押金类型code
     * @return 押金类型name，如果未找到返回null
     */
    public static String getNameByCode(byte code) {
        for (DepositTypeEnum type : DepositTypeEnum.values()) {
            if (type.getCode() == code) {
                return type.getName();
            }
        }
        return null; // 如果未找到对应的code，返回null
    }
}