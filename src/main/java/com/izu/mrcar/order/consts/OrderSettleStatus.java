package com.izu.mrcar.order.consts;

/**
 * orderApply 的 order_settle_status 状态枚举
 * <AUTHOR> on  2024/4/13 14:45
 */
public enum OrderSettleStatus {

    //是否已结算（0否 1是 2无需结算）
    NOT_SETTLED((byte) 0),
    SETTLED((byte) 1),
    NOT_REQUIRED((byte) 2);

    private final byte value;

    OrderSettleStatus(byte value) {
        this.value = value;
    }

    public byte getValue() {
        return value;
    }

}
