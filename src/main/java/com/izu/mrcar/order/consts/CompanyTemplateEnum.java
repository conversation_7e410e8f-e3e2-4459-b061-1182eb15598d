package com.izu.mrcar.order.consts;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 模板配置
 * @date 2024/5/17 20:45
 */
@AllArgsConstructor
@Getter
public enum CompanyTemplateEnum {
    //测试环境的配置
   // LE_TU_TEMPLATE("871","order-pdf-letu"),
    //LI_JIANG_TEMPLATE("1033,913,803", "order-pdf-lijiang")
    LE_TU_TEMPLATE("1778,2","order-pdf-letu"),
    LI_JIANG_TEMPLATE("1601,1618,1509,1504", "order-pdf-lijiang"),
    TIE_LU_TEMPLATE("2736", "order-pdf-tielu"),
    ;

    private String companyId;

    private String templateName;

    public static String getTemplate(Integer companyId){
        if(companyId ==null){
            return "";
        }
        for(CompanyTemplateEnum companyTemplateEnum:values()){
            String[] split = companyTemplateEnum.getCompanyId().split(",");
           List<Integer> companyIds=  Arrays.stream(split).map(s -> Integer.parseInt(s)).collect(Collectors.toList());
           if(companyIds.contains(companyId)){
               return companyTemplateEnum.getTemplateName();
           }
        }
        return "";
    }
}
