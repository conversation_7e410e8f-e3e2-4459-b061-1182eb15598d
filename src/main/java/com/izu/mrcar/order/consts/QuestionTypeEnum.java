package com.izu.mrcar.order.consts;

import org.apache.commons.lang.StringUtils;

import java.util.Arrays;

public enum QuestionTypeEnum {
    FUNCTION_ERROR(Byte.valueOf("1"),"功能报障"),
    MILEAGE_NOT_CORRECT(Byte.valueOf("2"),"里程计算不准"),
    MAINTAIN(Byte.valueOf("3"),"维修保养"),
    LONG_RENT_QUESTION(Byte.valueOf("4"),"长租服务问题"),
    FALSE_ALARM(Byte.valueOf("5"),"误报问题"),
    CANNOT_RETURN_CAR(Byte.valueOf("6"), "无法还车"),
    OTHER(Byte.valueOf("0"),"其他")
    ;

    private Byte type;

    private String desc;

    QuestionTypeEnum(Byte type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static String getDescByStatus(Byte type){
        return Arrays.stream(QuestionTypeEnum.values())
                .filter(en->en.getType().equals(type))
                .findAny()
                .map(QuestionTypeEnum::getDesc)
                .orElse(StringUtils.EMPTY);
    }

    public Byte getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }


}
