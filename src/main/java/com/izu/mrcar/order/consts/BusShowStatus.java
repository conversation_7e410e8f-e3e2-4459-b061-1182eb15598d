package com.izu.mrcar.order.consts;

import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * 大巴车展示状态
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum BusShowStatus {
    PENDING_DISPATCH(1, "待调度"),
    PENDING_DEPARTURE(2, "待出发"),
    TRIP_IN_PROGRESS(3, "用车中"),
    PENDING_CHECK_IN(4, "待结算"),
    COST_ENTRY_IN_PROGRESS(5, "审批中"),
    COMPLETED(6, "已结束"),
    RE_DISPATCH(7, "重新调度"),
    DISPATCH_COMPLETED(8, "调度完成"),
    NOT_COMPLETED(9, "未完成"),
    ALREADY(10, "已结束");

    private final int code;
    private final String description;

    public static String getDescriptionByCode(int code) {
        for (BusShowStatus status : BusShowStatus.values()) {
            if (status.getCode() == code) {
                return status.getDescription();
            }
        }
        return "";
    }
}