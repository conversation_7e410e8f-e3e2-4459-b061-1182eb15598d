package com.izu.mrcar.order.consts.dispatching;

/**
 * Created by hp on 2019/11/16.
 */
public class ScheduleEnum {

    /**订单申请单状态**/
    public enum ScheduleStatus {
        // (1:待调度 20调度成功 30行程中 40行程已结束 50订单待确认 60已驳回 70待结算 90已完成 100已取消
        UN_SCHEDULE((short) 1, "待调度"),
        SCHEDULEED((short) 2, "已调度"),
        CANCEL((short) 3, "已取消");
        //--------------------------------------------
        private short value;
        private String text;

        private ScheduleStatus(short value, String text) {
            this.value = value;
            this.text = text;
        }

        public static ScheduleStatus getByValue(short value) {
            for (ScheduleStatus scheduleStatus : values()) {
                if (scheduleStatus.value == value ) {
                    return scheduleStatus;
                }
            }
            return null;
        }

        public short value() {
            return this.value;
        }

        public String text() {
            return this.text;
        }
    }
}
