package com.izu.mrcar.order.consts;

/**
 * <AUTHOR>
 * @description: 通用审批单-审批类型枚举类
 * @date 2024/4/7 14:51
 */
public enum CommonApplyTypeEnum {

    BUSINESS_SUPPLIER_EXPENDITURE_ORDER((byte)1,"商务车供应商支出单审批")
    ;

    private Byte type;
    private String message;

    CommonApplyTypeEnum(Byte type, String message) {
        this.type = type;
        this.message = message;
    }

    public static CommonApplyTypeEnum getByValue(byte value) {
        for (CommonApplyTypeEnum typeEnum : values()) {
            if (typeEnum.type == value ) {
                return typeEnum;
            }
        }
        return null;
    }

    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        this.type = type;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
