package com.izu.mrcar.order.consts.workstatus;

import com.izu.framework.web.exception.ExceptionFactory;

/**
 * 业务线枚举
 */
public enum BusinessLine {

    INTERNAL_USE("001", "内部用车"),

    BUSINESS_USE("002", "商务用车"),

    CASUAL_USE("003", "零散用车"),

    COMMUTER_USE("004", "代步车"),

    YUNNAN_PERSONAL_SHORT_TERM("005", "云南公务用车-个人短租"),

    YUNNAN_CORPORATE_RENTAL("006", "云南公务用车-对公租车");

    private final String code; // 业务线代码

    private final String description; // 业务线描述

    BusinessLine(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 根据业务线代码获取枚举值
     *
     * @param code 业务线代码
     * @return 对应的枚举值
     * @throws IllegalArgumentException 如果代码无效
     */
    public static BusinessLine fromCode(String code) {
        for (BusinessLine line : BusinessLine.values()) {
            if (line.code.equals(code)) {
                return line;
            }
        }
        throw ExceptionFactory.createRestException("无效的业务线代码: " + code);
    }
}