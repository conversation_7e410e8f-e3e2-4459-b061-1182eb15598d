package com.izu.mrcar.order.consts;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @description: 司机控制行为
 * @date 2024/4/15 15:46
 */
@AllArgsConstructor
@Getter
public enum DriverControlEnum {
    NORMAL_DRIVING(1, "按正常步骤操作"),
    JUMP_DRIVING(2, "跳过司机App操作"),
    JUMP_PHOTO_DRIVING(3, "跳过司机前后拍照操作"),
    JUMP_DRIVER_PHOTO_DRIVING(4, "跳过司机App操作和拍照操作"),
    ;

    private Integer code;
    private String desc;

}
