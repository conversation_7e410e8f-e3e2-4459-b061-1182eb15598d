package com.izu.mrcar.order.consts;

/**订单子系统    常量定义**/
public final class OrderConst {

	 /**账单编号前缀: 总账单 **/
	 public static final String BILL_NO_PREFIX = "BT";
	 /**账单编号前缀: 费用补录账单 **/
	 public static final String BILL_ATTACH_NO_PREFIX  = "BA";
	 /**账单编号前缀: 订单用车行程账单 **/
	 public static final String BILL_ORDER_NO_PREFIX  = "BO";
     /**订单取消单编号前缀**/
     public static final String ORDER_CANCEL_NO_PREFIX = "Q";
	/**订单编号前缀**/
	public static final String ORDER_NO_PREFIX = "O";
     /**订单申请编号前缀：内部订单**/
     public static final String ORDER_NO_INTERNAL_PREFIX = "I";
     /**订单申请编号前缀：商务用车**/
     public static final String ORDER_NO_MOTORCADE_PREFIX = "M";
	/**
	 * 私车公用订单前缀：私车公用订单
	 */
	public static final String PRIVATE_CAR_ORDER_PREFIX = "S";
	/**
	 * 里程纠偏记录编号前缀
	 */
	public static final String ORDER_MILEAGE_CORRECT_PREFIX = "JP";
	/**
	 * 问题反馈记录编号前缀
	 */
	public static final String QUESTION_BACK_PREFIX = "WT";

	public static final String CHARGE_ORDER_CODE_PREFIX = "CD";
	/**
	 * 商务车支出单单号前缀
	 */
	public static final String BUSINESS_SUPPLIER_EXPENDITURE_PREFIX = "ZC";

	/**
	* 公务员用车、取还记录
	**/
	public static final String GOV_CAR_GAIN_BACK_RECORD_PREFIX = "CXYC";

	/**
	 * 公务员用车、验车记录
	 **/
	public static final String GOV_CAR_CHECK_RECORD_PREFIX = "CXYCD";

    /**
     * 公务员用车 订单前缀
     */
    public static final String GOV_CAR_ORDER_PREFIX = "CXDD";

	/**
     * 公务员用车 取消记录单号前缀
     */
    public static final String GOV_CAR_CANCELLATION_PREFIX = "CXC";

	/**
     * 公务员用车 账单前缀
     */
    public static final String GOV_CAR_ORDER_BILL_PREFIX = "CXZDC";

	/**
	 * 公务员用车 附加费订单前缀
	 */
    public static final String GOV_CAR_EXTEND_ORDER_PREFIX = "CXFJ";

	/**
	 * 公务用车 账单收入推送数据处理ID前缀
	 */
    public static final String GOV_CAR_INCOME_PUSH_DATA_ID_PREFIX = "DZCX";

	/**
	 * 公务用车对公 订单前缀
	 */
	public static final String PUBLIC_GOV_CAR_ORDER_PREFIX = "FS";

	/**
	 * 商务包车订单前缀
	 */
	public static final String BUSINESS_PACKAGE_ORDER_PREFIX = "BUS";


	/**
	 * 大巴车订单前缀
	 */
	public static final String BUS_ORDER_PREFIX = "BC";

     public static String generateOrderPrefix(Byte orderType){
		 return OrderEnum.OrderType.INTERNAL_ORDER.value() == orderType.intValue() ?
				 ORDER_NO_INTERNAL_PREFIX : ORDER_NO_MOTORCADE_PREFIX;
	 }

	public static String generateOrderPrefix2(Byte orderType){
		return OrderEnum.OrderType.INTERNAL_ORDER.value() == orderType.intValue() ?
				ORDER_NO_INTERNAL_PREFIX : ORDER_NO_MOTORCADE_PREFIX;
	}


}
