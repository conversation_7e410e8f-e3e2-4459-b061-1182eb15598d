package com.izu.mrcar.order.consts;

/**
 * <AUTHOR>
 * @description: 支出状态 0-无需支出；1-行程未完成；2-待支出；3-支出中；4-已支出
 * @date 2024/4/11 11:22
 */
public enum ExpenseStatusEnum {

//    支出状态枚举类 0-无需支出；1-行程未完成；2-待支出；3-支出中；4-已支出
    NO_EXPENSE((byte)0,"无需支出"),
    TRIP_NOT_FINISH((byte)1, "待行程结束"),
    WAIT_EXPENSE((byte)2, "待支出"),
    EXPENSEING((byte)3, "支出中"),
    EXPENSE_FINISH((byte)4, "推送成功"),
    EXPENDITURE_FINISH((byte)5, "支出成功"),
    ;


    private Byte type;
    private String message;

    ExpenseStatusEnum(Byte type, String message) {
        this.type = type;
        this.message = message;
    }
    public static ExpenseStatusEnum getByValue(byte value) {
        for (ExpenseStatusEnum typeEnum : values()) {
            if (typeEnum.type == value ) {
                return typeEnum;
            }
        }
        return null;
    }

    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        this.type = type;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
