package com.izu.mrcar.order.consts;

/**
 * <AUTHOR>
 * @date 2019-12-24 19:44
 */
public class OrderMq {

    public static final String AIRBUS_MQ = "mq_mrcar_asset_success_message";

    public static final String AIRBUS_LOCK_PREFIX = "izu_mrcar_order_core-selfhelp_rentorder-";

    public static final String FINISH_TRIP_MQ = "izu_mrcar_order_core-finish_trip";

    public static final String ORDER_OPERATION_MQ = "izu_mrcar_order_core-order_operation";

    public static final String ASYNC_UPDATE_STATUS_MQ = "izu_mrcar_order_core_update_status";

    public static final String ORDER_SETTLE_MQ = "izu_mrcar_order_core-order_settle";

    public static final String ORDER_SETTLE_PUSH_MQ = "mq_cz_third_return_amount";

    /** 队列名称：埋点消息（push推送消息） **/
    public static final String IZU_MQ_MSG_MODEL_POIT_MESSAGE = "msg_model_point_message";

    /** 订单（包含主行程、子行程）取消 **/
    public static final String ORDER_CANCEL_MQ = "izu_mrcar_order_core-order_cancel";

    /** 零散用车订单完成mq **/
    public static final String LINGSAN_ORDER_COMPLETE_MQ = "izu_mrcar_core-order_lingsan_order_complete";
    public static final String LINGSAN_ORDER_COMPLETE_MQ_TAG = "lingsan_order_complete";

    /**
     * 代步车
     */
    public static final String DAIBUCHE_ORDER_MQ = "daibuche_order_message";

    /**
     * 公务用车出入围栏mq消息通知
     */
    public static final String GOV_CAR_ORDER_FENCE_NOTICE_MQ = "mrcar_monitor_official_vehicle_fence_notice";

    /**
     * 公务用车订单还车mq
     */
    public static final String GOV_CAR_ORDER_RETURN_MQ = "mrcar_order_core_gov_car_order_return";


    /**
     *  实体任务状态管理服务的任务状态发生变化时，发送异步消息通知
     *  车辆和司机的任务状态，包括任务开始、任务结束等操作。
     */
    public static final String ENTITY_STATUS_UPDATE_MQ = "mrcar_order_entity_status_update";

    /**
     *
     *  支出单支出成功后发送长租mq消息
     */
    public static final String MR_CAR_BILLING_EXPENSE = "mr_car_billing_expense";




}
