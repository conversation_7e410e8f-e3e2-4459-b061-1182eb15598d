package com.izu.mrcar.order.consts.dispatching;

/**
 * Created by hp on 2019/9/3.
 */
public enum ScheduleStatusEnum {

    NOT_SCHEDULE( "调度中", (byte)1),
    SCHEDULED( "调度成功", (byte)2),
    CANCELED( "已取消", (byte)3),
    OVERTIME( "已过期", (byte)4),
    WAITING_ASSIGN("待指派供应商", (byte) 5),
    VENDOR_SCHEDULING("供应商调度中", (byte) 6);

    private String name;
    private byte value;

    ScheduleStatusEnum(String name, byte value){

        this.name=name;
        this.value=value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public byte getValue() {
        return value;
    }

    public void setValue(byte value) {
        this.value = value;
    }

    public static ScheduleStatusEnum getByValue(Byte value) {
        for (ScheduleStatusEnum scheduleStatusEnum : values()) {
            if (scheduleStatusEnum.value==value ) {
                return scheduleStatusEnum;
            }
        }
        return null;
    }
}
