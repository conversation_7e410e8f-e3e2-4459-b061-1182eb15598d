package com.izu.mrcar.order.consts.daibuche;

import lombok.Getter;

@Getter
public enum UnfreezeStatusEnum {

    NOT_UNFROZEN((byte) 0, "未解冻"),
    UNFREEZING((byte) 1, "解冻中"),
    UNFREEZE_SUCCESS((byte) 2, "解冻成功"),
    UNFREEZE_FAILED((byte) 3, "解冻失败");

    private final byte code;
    private final String name;

    UnfreezeStatusEnum(byte code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据code查找对应的name
     *
     * @param code 解冻状态code
     * @return 解冻状态name，如果未找到返回null
     */
    public static String getNameByCode(byte code) {
        for (UnfreezeStatusEnum status : UnfreezeStatusEnum.values()) {
            if (status.getCode() == code) {
                return status.getName();
            }
        }
        return null; // 如果未找到对应的code，返回null
    }
}