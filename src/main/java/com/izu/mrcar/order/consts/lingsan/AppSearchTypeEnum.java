package com.izu.mrcar.order.consts.lingsan;

import lombok.Getter;

@Getter
public enum AppSearchTypeEnum {

    DEMAND_ORDER(1,"需求单"),
    CUSTOMER_ORDER(2,"订单"),
    VEHICLE_CHANGE_ORDER(3,"换车单"),
    ;

    private final Integer type;
    private final String name;

    AppSearchTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public static String getTypeDesc(int type) {
        for (AppSearchTypeEnum typeEnum : AppSearchTypeEnum.values()) {
            if (typeEnum.getType() == type) {
                return typeEnum.getName();
            }
        }
        return null;
    }

}
