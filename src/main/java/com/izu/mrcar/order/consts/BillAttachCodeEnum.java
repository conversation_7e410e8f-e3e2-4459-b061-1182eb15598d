package com.izu.mrcar.order.consts;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/3/27 20:08
 */
@Getter
@AllArgsConstructor
public enum BillAttachCodeEnum {

    PARKING_FEE((byte)1, "停车费"),
    HIGHWAY_FEE((byte)2, "高速费"),
    ACCOMMODATION_FEE((byte)3, "住宿费"),
    CATERING_FEE((byte)4, "餐饮费"),
    OTHER_FEE((byte)5, "其他费");

    private final byte code;
    private final String description;
}
