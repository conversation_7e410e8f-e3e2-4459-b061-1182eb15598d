package com.izu.mrcar.order.consts.daibuche;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;


@Getter
@AllArgsConstructor
public enum ContentTypeEnum {

    FORM_DATA(0, "表单"),
    POST_BODY(1, "请求体"),
    ;

    private Integer code;

    private String name;

    public static String getName(Integer code) {
        if (Objects.nonNull(code)) {
            for (ContentTypeEnum contentTypeEnum : ContentTypeEnum.values()) {
                if (contentTypeEnum.getCode().equals(code)) {
                    return contentTypeEnum.getName();
                }
            }
        }
        return null;
    }

}
