package com.izu.mrcar.order.consts.lingsan;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Program: mrcar-order-common
 * @Description: ${description}
 * @Author: inT
 * @Create: 2023-01-18
 */
public class AuditConstants {
    /**
     * 审核单类型（1：发车前确认审核；2：用车前资料审核；3：换车资料审核）
     */
    @Getter
    @AllArgsConstructor
    public enum AuditTypeEnum {
        CONFIRMATION_BEFORE_DELIVERY(1,"发车前确认审核"),
        REVIEW_OF_DATA_BEFORE_USE(2,"用车前资料审核"),
        REVIEW_OF_VEHICLE_RETURN_DATA(3,"还车资料审核"),
        ;
        private Integer auditType;

        private String auditTypeDesc;

        public static String getAuditTypeDesc(int auditType) {
            for (AuditTypeEnum auditTypeEnum : AuditTypeEnum.values()) {
                if (auditTypeEnum.getAuditType() == auditType) {
                    return auditTypeEnum.getAuditTypeDesc();
                }
            }
            return null;
        }
    }

    /**
     * 审核状态（1：待审核；2：审核通过；2：审核驳回；）
     */
    @Getter
    @AllArgsConstructor
    public enum AuditStatusEnum {
        TO_BE_REVIEWED(1,"待审核"),
        APPROVED(2,"审核通过"),
        APPROVAL_REJECTION(3,"审核驳回"),
        ;
        private Integer auditStatus;

        private String auditStatusDesc;

        public static String getAuditStatusDesc(int auditStatus) {
            for (AuditStatusEnum auditStatusEnum : AuditStatusEnum.values()) {
                if (auditStatusEnum.getAuditStatus() == auditStatus) {
                    return auditStatusEnum.getAuditStatusDesc();
                }
            }
            return null;
        }
    }
}
