package com.izu.mrcar.order.consts;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: 纠偏状态,0:未纠偏 1:纠偏中 2:纠偏完成
 * @date 2023/7/28 16:30
 */
public enum CorrectStatusEnum {
    WITHOUT_CORRECT(new Byte("0"),"未纠偏","未纠偏"),
    CORRECT_DOING(new Byte("1"),"纠偏中","里程计算中"),
    CORRECT_DONE(new Byte("2"),"纠偏完成","纠偏完成")
    ;

    private Byte code;

    private String message;

    private String desc;

    CorrectStatusEnum(Byte code, String message, String desc) {
        this.code = code;
        this.message = message;
        this.desc = desc;
    }

    public Byte getCode() {
        return code;
    }

    public void setCode(Byte code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static CorrectStatusEnum getCorrectStatusEnum(Byte code){
        return Arrays.stream(values()).filter(e-> Objects.equals(e.getCode(),code)).findFirst().orElse(WITHOUT_CORRECT);
    }
}
