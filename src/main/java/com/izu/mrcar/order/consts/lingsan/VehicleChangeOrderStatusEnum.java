package com.izu.mrcar.order.consts.lingsan;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Program: mrcar-order-common
 * @Description: ${description}
 * @Author: inT
 * @Create: 2023-01-17
 */
@Getter
@AllArgsConstructor
public enum VehicleChangeOrderStatusEnum {
    TO_BE_CHANGED(1,"待换车/司机"),
    CONFIRMATION_BEFORE_DELIVERY(2,"派送前确认"),
    WAITING_DELIVERY(5,"待派送"),
    DISPATCHING(6,"派送中"),
    COMPLETED(11,"已完成"),
    ORDER_CANCELED(13,"已取消"),
    DISPATCH_RETURN(14,"派单退回"),
    ;
    private Integer status;

    private String statusDesc;

    public static String getVehicleChangeOrderStatusDesc(int status) {
        for (VehicleChangeOrderStatusEnum statusEnum : VehicleChangeOrderStatusEnum.values()) {
            if (statusEnum.getStatus() == status) {
                return statusEnum.getStatusDesc();
            }
        }
        return null;
    }
}
