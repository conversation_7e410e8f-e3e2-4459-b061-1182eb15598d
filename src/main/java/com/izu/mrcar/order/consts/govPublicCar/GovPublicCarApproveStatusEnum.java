package com.izu.mrcar.order.consts.govPublicCar;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;


@Getter
@AllArgsConstructor
public enum GovPublicCarApproveStatusEnum {

    // 审批状态:0-无审批 1-待审批,2-审批撤回 3-审批通过, 4-审批驳回
    NO_APPROVAL((byte)0, "无审批"),
    PENDING((byte)1, "待审批"),
    WITHDRAW((byte)2, "审批撤回"),
    PASS((byte)3, "审批通过"),
    TURN_DOWN((byte)4, "审批驳回"),
    CANCEL((byte)5, "审批取消"),
    ;

    private final Byte code;
    private final String name;

    public static String getName(Byte code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findFirst().map(GovPublicCarApproveStatusEnum::getName).orElse(null);
    }
}
