package com.izu.mrcar.order.consts;

/**
 * <AUTHOR>
 * @description: 纠偏类型,0:未纠偏 1:系统纠偏 2:手工纠偏
 * @date 2023/7/28 16:24
 */
public enum CorrectTypeEnum {
    WITHOUT_CORRECT(new Byte("0"),"未纠偏"),
    SYSTEM_CORRECT(new Byte("1"),"系统纠偏"),
    MANUAL_CORRECT(new Byte("2"),"手工纠偏"),
    ;
    private Byte type;
    private String message;

    CorrectTypeEnum(Byte type, String message) {
        this.type = type;
        this.message = message;
    }

    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        this.type = type;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
