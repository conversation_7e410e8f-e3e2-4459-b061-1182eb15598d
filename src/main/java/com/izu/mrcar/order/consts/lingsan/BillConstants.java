package com.izu.mrcar.order.consts.lingsan;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Optional;

/**
 * 零散用车-账单常量
 */
public class BillConstants {
    /**
     * 系统生成的账单明细
     */
    public static final Integer BILL_DETAIL_SYSTEM = 0;
    /**
     * 调整后的最新的账单明细
     */
    public static final Integer BILL_DETAIL_ADJUSTED = -1;
    /**
     * 首汽核对的草稿数据
     */
    public static final Integer BILL_DETAIL_DRAFT = -2;
    /**
     * 核对状态为已核对的草稿数据
     */
    public static final Integer BILL_DETAIL_DRAFT_CHECKED = -3;

    public static final Integer CREATE_ID_SYSTEM = 0;
    public static final String CREATE_NAME_SYSTEM = "system";

    public static final String SYSTEM_REJECT = "系统驳回";

    public interface RedisLock {
        String checkBill4ShouqiLockKey = "lingsan_check_bill_4_shouqi_lock_key_";
        String pushIncomeBillLockKey = "lingsan_push_income_bill_lock_key_";
        String saveCheckBill4ShouqiLockKey = "lingsan_save_check_bill_4_shouqi_lock_key_";
        String checkBill4CustomerLockKey = "lingsan_check_bill_4_customer_lock_key_";
        String incomeAndExpenditureDetailExportLockKey = "lingsan_platformIncomeAndExpenditureDetail_export_lock_key_";
    }

    @Getter
    @AllArgsConstructor
    public enum BillType {
        // 1 收入账单（客户账单） 2 支出账单（供应商账单）
        INCOME_BILL(1, "收入账单"),
        EXPENDITURE_BILL(2, "支出账单");
        private final Integer code;
        private final String desc;

        public static BillType getByCode(Integer code) {
            return Arrays.stream(BillType.values()).filter(item -> item.getCode().equals(code)).findFirst().orElse(null);
        }
    }

    /**
     * 首汽收入账单状态
     */
    @Getter
    @AllArgsConstructor
    public enum ShouqiBillStatus {
        // 110 待客户核对账单 120 待首汽确认 130 待供应商确认 140 已确认 150 已驳回
        TO_BE_CHECKED_BY_CUSTOMER(110, "待客户核对", "待客户核对"),
        TO_BE_CHECKED_BY_SHOUQI(120, "待首汽确认", "待确认"),
        TO_BE_CHECKED_BY_SUPPLIER(130, "待供应商确认", "待供应商确认"),
        CHECK_FINISHED(140, "对账完成", "对账完成"),
        REJECTED(150, "已驳回给客户", "已驳回给客户"),
        ;
        private final Integer code;
        private final String desc;
        private final String displayDesc;

        public static ShouqiBillStatus getByCode(Integer code) {
            return Arrays.stream(ShouqiBillStatus.values()).filter(item -> item.getCode().equals(code)).findFirst().orElse(null);
        }
    }

    /**
     * 供应商支出账单状态
     */
    @Getter
    @AllArgsConstructor
    public enum SupplierBillStatus {
        //  230 待供应商确认 240 已确认 250 已驳回
        TO_BE_CHECKED_BY_SUPPLIER(230, "待供应商确认","待核对","待供应商核对"),
        CHECK_FINISHED(240, "对账完成","已核对","对帐完成"),
        REJECTED(250, "已驳回","已驳回","对账驳回"),
        ;
        private final Integer code;
        private final String desc;
        private final String supplierDisplayDesc;
        private final String platformDisplayDesc;

        public static Optional<SupplierBillStatus> getByCode(Integer code){
            return Arrays.stream(SupplierBillStatus.values()).filter(c -> c.getCode().equals(code)).findFirst();
        }
    }

    /**
     * 客户账单状态
     */
    @Getter
    @AllArgsConstructor
    public enum CustomerBillStatus {
        //  110 待客户核对账单 120 待首汽确认 140 已确认 150 已驳回
        TO_BE_CHECKED_BY_CUSTOMER(110, "待客户确认", "待核对"),
        TO_BE_CHECKED_BY_SHOUQI(120, "待首汽确认", "待首汽确认"),
        CHECK_FINISHED(140, "对账完成", "对账完成"),
        REJECTED(150, "首汽驳回", "已驳回"),
        ;
        private final Integer code;
        private final String desc;
        private final String displayDesc;

        public static CustomerBillStatus getByCode(Integer code) {
            return Arrays.stream(CustomerBillStatus.values()).filter(item -> item.getCode().equals(code)).findFirst().orElse(null);
        }
    }

    /**
     * 账单推送状态
     */
    @Getter
    @AllArgsConstructor
    public enum SettleStatus {
        TO_BE_PUSHED(0, "待推送"),
        PUSH_SUCCESS(1, "推送成功"),
        PUSH_FAIL(2, "推送失败"),
        THIRD_PARTY_SUCCESS(4,"已完成"),
        THIRD_PARTY_REJECT(5,"已驳回"),
        ;
        private final Integer code;
        private final String desc;

        public static SettleStatus getByCode(Integer code) {
            return Arrays.stream(SettleStatus.values()).filter(item -> item.getCode().equals(code)).findFirst().orElse(null);
        }
    }

    @Getter
    @AllArgsConstructor
    public enum BillDetailType {
        VEHICLE(1, "车辆"),
        DRIVER(2, "司机"),
        VEHICLE_DRIVER(3, "车辆和司机");
        private final Integer code;
        private final String desc;

        public static BillDetailType getByCode(Integer code) {
            return Arrays.stream(BillDetailType.values()).filter(item -> item.getCode().equals(code)).findFirst().orElse(null);
        }
    }

    /**
     * 供应商类型
     */
    @Getter
    @AllArgsConstructor
    public enum SupplierType {
        SHOUQI(1, "首汽"),
        THIRD_PARTY(2, "三方");
        private final Integer code;
        private final String desc;

        public static SupplierType getByCode(Integer code) {
            return Arrays.stream(SupplierType.values()).filter(item -> item.getCode().equals(code)).findFirst().orElse(null);
        }
    }

    /**
     * 核对账单结果
     */
    @Getter
    @AllArgsConstructor
    public enum CheckResult {
        NO_PROBLEM(1, "无问题"),
        HAVE_PROBLEM(2, "有问题");
        private final Integer code;
        private final String desc;

        public static CheckResult getByCode(Integer code) {
            return Arrays.stream(CheckResult.values()).filter(item -> item.getCode().equals(code)).findFirst().orElse(null);
        }
    }

    @Getter
    @AllArgsConstructor
    public enum RoleType {
        // 1首汽 2客户 3供应商
        SHOUQI(1, "首汽"),
        CUSTOMER(2, "客户"),
        SUPPLIER(3, "供应商");
        private final Integer code;
        private final String desc;

        public static RoleType getByCode(Integer code) {
            return Arrays.stream(RoleType.values()).filter(item -> item.getCode().equals(code)).findFirst().orElse(null);
        }
    }

}
