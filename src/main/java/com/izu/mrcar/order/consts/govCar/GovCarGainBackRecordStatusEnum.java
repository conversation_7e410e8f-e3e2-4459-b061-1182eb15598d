package com.izu.mrcar.order.consts.govCar;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 验车单状态
 * <p>
 * 2024/8/7 下午6:41
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum GovCarGainBackRecordStatusEnum {

    WAIT_GAIN((byte) 0, "待取车"),
    START_GAIN((byte) 10, "开始取车"),
    SIGN_GAIN((byte) 20, "签署取车合同"),
    GAINED((byte) 30, "取车完成"),
    START_BACK((byte) 40, "开始还车"),
    SIGN_BACK((byte) 50, "签署还车合同"),
    BACKED((byte) 60, "已还车"),
    ;

    private final Byte code;
    private final String name;

    public static String getName(Byte code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findAny().map(GovCarGainBackRecordStatusEnum::getName).orElse(null);
    }

}
