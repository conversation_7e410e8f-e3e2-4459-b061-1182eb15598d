package com.izu.mrcar.order.consts;


/**
 * 文件记录表  file_info 的类型
 */
public enum FileTypeEnum {
    BUSINESS_ORDER_ADDITION_ORDER_CONTRACT(1,"商务车补录订单合同附件"),
    BUSINESS_ORDER_EXPENDITURE_CONTRACT(2,"商务车供应商支出单上传附件"),
    CO_BILL_FEE_ADJUST_FILE(3,"代步车账单费用调整附件"),
    BUS_ORDER_BILL_FILE(4,"大巴车账单附件")
    ;

    private int type;

    private String desc;


    FileTypeEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }


    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}