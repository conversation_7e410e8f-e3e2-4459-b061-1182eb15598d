package com.izu.mrcar.order.consts.daibuche;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 业务类型 1送车 2还车
 */
@Getter
@AllArgsConstructor
public enum DeliveryReturnTypeEnum {

    CONFIRMATION_BEFORE_DELIVERY((byte)1,"送车"),
    DISPATCH_RETURN((byte)2,"还车"),
    ;

    private Byte code;

    private String name;


    public static String getName(int code) {
        for (DeliveryReturnTypeEnum deliveryReturnTypeEnum : DeliveryReturnTypeEnum.values()) {
            if (deliveryReturnTypeEnum.getCode() == code) {
                return deliveryReturnTypeEnum.getName();
            }
        }
        return null;
    }

}
