package com.izu.mrcar.order.consts;

import lombok.Getter;

/**
 * <AUTHOR>
 * @description: 问题反馈分类
 * @date 2023/8/28 16:01
 */
@Getter
public enum FeedbackTypeEnum {


    //反馈分类:1.里程纠偏;2.自助取还设备异常

    MILEAGE_CORRECT(1,"里程纠偏问题反馈"),
    SELF_SET_EXCEPTION(2,"自助取还设备异常反馈"),
    PUBLIC_GOVERNMENT_CAR_EXCEPTION(3,"公务用车还车异常反馈")

    ;

    private Integer type;

    private String message;

    FeedbackTypeEnum(Integer type, String message) {
        this.type = type;
        this.message = message;
    }
}
