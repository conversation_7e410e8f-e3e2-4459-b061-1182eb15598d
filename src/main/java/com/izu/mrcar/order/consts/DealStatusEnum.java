package com.izu.mrcar.order.consts;

import org.apache.commons.lang.StringUtils;

import java.util.Arrays;

public enum DealStatusEnum {
    NOT_DEAL(Byte.valueOf("0"),"待处理"),
    YES_DEAL(Byte.valueOf("1"),"已处理")
    ;

    private Byte status;

    private String desc;

    DealStatusEnum(Byte status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static String getDescByStatus(Byte status){
        return Arrays.stream(DealStatusEnum.values())
                .filter(en->en.getStatus().equals(status))
                .findAny()
                .map(DealStatusEnum::getDesc)
                .orElse(StringUtils.EMPTY);
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
