package com.izu.mrcar.order.consts.govPublicCar;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum GovPublicCarVerifyStatusEnum {
//核实状态 0:未核实 1:已核实 2:无需核实
    UNVERIFIED(0, "未核实"),
    VERIFIED(1, "已核实"),
    NO_VERIFICATION_REQUIRED(2, "无需核实"),
    ;

    private final Integer code;
    private final String name;

    public static String getName(Integer code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findFirst().map(GovPublicCarVerifyStatusEnum::getName).orElse(null);
    }
}