package com.izu.mrcar.order.consts.govPublicCar;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;


@Getter
@AllArgsConstructor
public enum GovPublicCarDeploymentModeEnum {

    SHARED_PLATFORM((byte)1, "平台共享车"),
    POINT_PLACEMENT((byte)2, "定点投放");

    private final Byte code;
    private final String name;

    public static String getName(Byte code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findFirst().map(GovPublicCarDeploymentModeEnum::getName).orElse(null);
    }
}
