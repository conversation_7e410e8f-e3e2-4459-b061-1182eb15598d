package com.izu.mrcar.order.consts;

import lombok.Data;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * @description: 订单相关枚举
 * @author: hxc
 * @create: 2019-09-06 11:14
 **/
public class OrderEnum {


    /**订单类型**/
    public enum OrderType {
        /**
         * 订单类型
         */
        INTERNAL_ORDER((byte)1,(byte) 1, "内部订单"),
        MOTORCADE_ORDER((byte)2,(byte) 3, "商务用车"),
        PRIVATE_ORDER((byte)3,(byte) 12, "私车公用"),
        LS((byte) 6,(byte) 14, "零散用车"),
        ;
        /**
         * 订单类型编码
         */
        private Byte value = -1;
        /**
         * 对应的配置编码
         * 该订单类型在企业配置模块对应的编码
         * 因为历史原因导致配置模块的编码和订单类型编码不一致且暂时无法统一所以记录对应关系
         * 之后添加的订单类型必须指定配置模块应该对应的编码
         */
        private Byte businessConfigCode;
        /**
         * 订单类型描述
         */
        private String text  = "";

        OrderType(Byte value, Byte businessConfigCode, String text) {
            this.value = value;
            this.businessConfigCode = businessConfigCode;
            this.text = text;
        }

        public static String getTextByType(Byte value) {
            OrderType byValue = getByValue(value);
            if (byValue == null){
                return "";
            }
            return byValue.text;
        }

        public Byte value() {
            return this.value;
        }
        public String text() {
            return this.text;
        }
        public Byte getBusinessConfigCode(){
            return this.businessConfigCode;
        }

        /**订单类型是否合法**/
        public static boolean isValid( final Byte orderType) {
            List<Byte> codes = new ArrayList<>( 2 );
            codes.add( OrderType.INTERNAL_ORDER.value()  );
            codes.add( OrderType.MOTORCADE_ORDER.value()  );
            return codes.contains(orderType);
        }

        public static OrderType getByValue(Byte value) {
            for (OrderType orderType : values()) {
                if (orderType.value.equals(value) ) {
                    return orderType;
                }
            }
            return null;
        }

        public static Optional<OrderType> getByBusinessConfigCode(Byte businessConfigCode){
            return Arrays.stream(OrderType.values())
                    .filter(en->en.businessConfigCode.equals(businessConfigCode))
                    .findAny();
        }
    }

    /**订单类型**/
    public enum OrderTypeForApp {
        /**订单类型**/
        INTERNAL_ORDER((byte)1, "内部用车","Internal"),
        MOTORCADE_ORDER((byte)2, "商务用车","Business"),
        PRIVATE_ORDER((byte)3, "私车公用","私车公用"),
        LS((byte) 6, "零散用车","零散用车"),
        ;
        // --------------------------------------------
        private Byte value = -1;
        private String text  = "";
        private String textEn  = "";
        private OrderTypeForApp(Byte value, String text,String textEn){
            this.value = value;
            this.text  = text;
            this.textEn = textEn;
        }
        public Byte value() {
            return this.value;
        }
        public String text() {
            return this.text;
        }

        public String textEn() {
            return this.textEn;
        }
        public static OrderTypeForApp getByValue(Byte value) {
            for (OrderTypeForApp orderType : values()) {
                if (orderType.value.equals(value) ) {
                    return orderType;
                }
            }
            return null;
        }

        //根据value获取text
        public static String getTextByValue(Byte value) {
            OrderTypeForApp byValue = getByValue(value);
            if (byValue == null){
                return "";
            }
            return byValue.text();
        }
    }

    public enum PayTypeName{
        INDIVIDUAL_PAYMENT(Byte.valueOf("1"),"企业付款"),
        ENTERPRISES_PAY(Byte.valueOf("2"),"个人付款");
        private Byte value ;
        private String text ;
         PayTypeName(Byte value, String text){
            this.value = value;
            this.text  = text;
        }
        public Byte value() {
            return this.value;
        }
        public String text() {
            return this.text;
        }

        /**订单类型是否合法**/
        public static boolean isValid( final Byte orderType) {
            return OrderType.isValid(orderType);
        }

        public static PayTypeName getByValue(Byte value) {
            for (PayTypeName payTypeName : values()) {
                if (payTypeName.value.equals(value) ) {
                    return payTypeName;
                }
            }
            return null;
        }

        /**
         * 根据value获取text
         */
        public static String getTextByValue(Byte value) {
            PayTypeName byValue = getByValue(value);
            if (byValue == null){
                return "";
            }
            return byValue.text();
        }
    }
    /**订单服务类型**/
    public enum OrderServiceType {
        // 1单程服务 2半日 3整日 4接机 5送机 6接站 7送站 8批量用车 9单次 10自助取还
        ONE_WAY_SERVICE("1", "单程服务", "One way"),
        HALF_DAY_RENT("2", "半日", "Half day"),
        ONE_DAY_RENT("3", "整日", "One day"),//内部用车、商务用车公用公用的服务类型
        AIRPORT_PICKUP("4", "接机", "接机"),
        AIRPORT_DROPOFF("5", "送机", "送机"),
        STATION_PICKUP("6", "接站", "接站"),
        STATION_DROPOFF("7", "送站", "送站"),
        DAYS_RENT("8", "多日用车", "Multi day"),//内部用车的服务类型
        ONCE("9", "单程服务", "One way"),//内部用车的服务类型
        SELF_HELP_RENT("10", "自助取还", "Self-service"),//内部用车的服务类型
        PRIVATE_S1("11", "私车公用", "private-service");
        // --------------------------------------------
        private String value = "";
        private String text = "";
        private String enName = "";

        private OrderServiceType(String value, String text, String enName) {
            this.value = value;
            this.text = text;
            this.enName = enName;
        }

        public String value() {
            return this.value;
        }

        public String text() {
            return this.text;
        }

        public String getEnName() {
            return enName;
        }

        /**
         * 订单服务类型是否合法
         **/
        public static boolean isValid(final String orderType) {
            List<String> codes = new ArrayList<>(10);
            codes.add(OrderServiceType.ONE_WAY_SERVICE.value());
            codes.add(OrderServiceType.HALF_DAY_RENT.value());
            codes.add(OrderServiceType.ONE_DAY_RENT.value());
            codes.add(OrderServiceType.AIRPORT_PICKUP.value());
            codes.add(OrderServiceType.AIRPORT_DROPOFF.value());
            codes.add(OrderServiceType.STATION_PICKUP.value());
            codes.add(OrderServiceType.STATION_DROPOFF.value());
            codes.add(OrderServiceType.DAYS_RENT.value());
            codes.add(OrderServiceType.ONCE.value());
            codes.add(OrderServiceType.SELF_HELP_RENT.value());
            return codes.contains(orderType);
        }

        public static OrderServiceType getByValue(String value) {
            for (OrderServiceType orderType : values()) {
                if (orderType.value.equals(value)) {
                    return orderType;
                }
            }
            return null;
        }

        public static String getTextByValue(String value) {
            // 假设getByValue方法返回OrderServiceType类型或者null
            OrderServiceType byValue = getByValue(value);
            // 使用Optional.ofNullable来包装可能为null的值
            return Optional.ofNullable(byValue)
                    // 如果byValue不为null，则调用text()方法，否则返回空字符串
                    .map(OrderServiceType::text)
                    // 如果Optional为空，则返回空字符串作为默认值
                    .orElse("");
        }
    }
    /**订单申请单状态**/
    public enum OrderApplyStatus {
        // (5 待审核 10调度中 20待出发 30行程中 40行程已结束 50待确认 60已驳回 70待结算 90已完成 100已取消
        WAIT_AUTH((short) 5, "待审核","Waiting"),
        APPLY_NOT_PASS((short) 8, "审批驳回","Rejected"),
        APPLY_CANCEL((short) 9, "已撤回","Withdrew"),
        DISPATCHING((short) 10, "调度中","Dispatching"),
        DISPATCHED((short) 20, "待出发","Departing"),
        DURING_TRIP((short) 30, "行程中","During the route"),
        END_TRIP((short) 40, "行程已结束","End of the route"),
        WAIT_CONFIRM((short) 50, "账单待确认","Confirm billing"),
        REJECT((short) 60, "账单已驳回","Reject billing"),
        WAIT_ACCOUNT((short) 70, "待推送账单","To be billed"), //将待结算 改为待推送账单 2024-04-23
        COMPLETED((short) 90, "已完成","Confirmed"),
        CANCEL((short) 100, "已取消","Cancelled"),
        ASSIGN_SUPPLIER((short) 110, "分配供应商","assign supplier"),
        SUPPLIER_RETURN((short) 120, "供应商退单","supplier return"),
        CLEAN_SCHEDULE((short) 130, "清空调度","clean schedule"),;
        //--------------------------------------------
        private short value = -1;
        private String text = "";

        private String textEn = "";

        private OrderApplyStatus(short value, String text,String textEn) {
            this.value = value;
            this.text = text;
            this.textEn=textEn;
        }

        public static OrderApplyStatus getByValue(Byte value) {
            if (value == null) {
                return null;
            }
            return getByValue(value.shortValue());
        }


        public static OrderApplyStatus getByValue(Short value) {
            if (value == null) {
                return null;
            }
            for (OrderApplyStatus orderStatus : values()) {
                if (orderStatus.value == value) {
                    return orderStatus;
                }
            }
            return null;
        }

        public static String getTextByValue(Byte value){
            if(value == null){
                return "";
            }

            return getTextByValue(value.shortValue());
        }


        public static String getTextByValue(Short value) {
            // 假设getByValue方法返回OrderApplyStatus类型或者null
            OrderApplyStatus byValue = getByValue(value);
            // 使用Optional.ofNullable来包装可能为null的值
            return Optional.ofNullable(byValue)
                    // 如果byValue不为null，则调用text()方法，否则返回空字符串
                    .map(OrderApplyStatus::text)
                    // 如果Optional为空，则返回空字符串作为默认值
                    .orElse("");
                }

        public short value() {
            return this.value;
        }

        public String text() {
            return this.text;
        }

        public String textEn() {
            return this.textEn;
        }
    }

    /**订单状态**/
    public enum OrderStatus {
        // (10调度中 20待出发 25司机已出发 26司机已到达 30行程中 40行程已结束 50照片上传中 60费用补录中 70待结算 90已完成 100已取消
        UNKNOWN((short) 0, "未知"),
        WAIT_AUTH((short) 5, "待审核"),
        DISPATCHING((short) 10, "调度中"),
        DISPATCHED((short) 20, "待出发"),
        DRIVER_START((short) 25, "司机已出发"),
        DRIVER_ARRIVE((short) 26, "司机已到达"),
        DURING_TRIP((short) 30, "行程中"),
        @Deprecated //这个状态没有使用过。
        END_TRIP((short) 40, "行程已结束"),
        @Deprecated//这个状态之前是商务车独有，主要是排座椅，车前车后照片的，商务车二期之后被废除了。行程结束之后直接跳转到费用补录中。
        WAIT_CONFIRM((short) 50, "照片上传中"),
        REJECT((short) 60, "费用补录中"),
        @Deprecated//这个状态应该是主行程才有的状态，子行程没有这个状态。
        WAIT_ACCOUNT((short) 70, "待结算"),
        //自助取还的订单会直接从行程中跳转到已完成
        COMPLETED((short) 90, "已完成"),
        CANCEL((short) 100, "已取消");
        //--------------------------------------------
        private short value = -1;
        private String text = "";

        private OrderStatus(short value, String text) {
            this.value = value;
            this.text = text;
        }

        public static OrderStatus getByValue(Byte value) {
            if (value == null) {
                return null;
            }
            return getByValue(value.shortValue());
        }

        public static OrderStatus getByValue(Short value) {
            if (value == null) {
                return null;
            }
            for (OrderStatus orderStatus : values()) {
                if (orderStatus.value == value ) {
                    return orderStatus;
                }
            }
            return null;
        }

        public static String getTextByValue(Byte value) {
            if(value == null){
                return "";
            }
            return OrderStatus.getByValue(value.shortValue()).text();
        }

        /**
         * 通过枚举类型获取text
         */
        public static String getTextByValue(Short value) {
            // 假设getByValue方法返回OrderApplyStatus类型或者null
            OrderStatus byValue = getByValue(value);
            // 使用Optional.ofNullable来包装可能为null的值
            return Optional.ofNullable(byValue)
                    // 如果byValue不为null，则调用text()方法，否则返回空字符串
                    .map(OrderStatus::text)
                    // 如果Optional为空，则返回空字符串作为默认值
                    .orElse("");
                }

        public short value() {
            return this.value;
        }

        public String text() {
            return this.text;
        }
    }

    /**订单审批状态**/
    public enum OrderApprovalStatus {
        // 5待审批 10审批通过 20审批未通过 100已取消
        APPROVEL_WAIT((short) 5, "待审批"),
        APPROVEL_PASS((short) 10, "审批通过"),
        APPROVEL_NOT_PASS((short) 20, "审批驳回"),
        APPROVEL_CANCEL((short) 100, "已取消");
        //--------------------------------------------
        private short value = -1;
        private String text = "";

        OrderApprovalStatus(short value, String text) {
            this.value = value;
            this.text = text;
        }


        public static OrderApprovalStatus getByValue(Byte value) {
            if (value == null) {
                return null;
            }
            return getByValue(value.shortValue());
        }



        public static OrderApprovalStatus getByValue(Short value) {
            if (value == null) {
                return null;
            }
            for (OrderApprovalStatus orderStatus : values()) {
                if (orderStatus.value == value) {
                    return orderStatus;
                }
            }
            return null;
        }

        public static String getTextByValue(Byte value){
            if(value == null){
                return "";
            }
            return getTextByValue(value.shortValue());
        }

        //根据value获取text
        public static String getTextByValue(Short value) {
            // 假设getByValue方法返回OrderApplyStatus类型或者null
            OrderApprovalStatus byValue = getByValue(value);
            // 使用Optional.ofNullable来包装可能为null的值
            return Optional.ofNullable(byValue)
                    // 如果byValue不为null，则调用text()方法，否则返回空字符串
                    .map(OrderApprovalStatus::text)
                    // 如果Optional为空，则返回空字符串作为默认值
                    .orElse("");
        }

        public short value() {
            return this.value;
        }

        public String text() {
            return this.text;
        }
    }

    /**订单审批取消状态，供APP使用**/
    public enum OrderApprovalCancelStatus {
        // 1已撤回 2已驳回 3已过期 4已取消
        APPROVAL_RECALL((byte) 1, "已撤回"),
        APPROVAL_REJECT((byte) 2, "已驳回"),
        APPROVAL_TIME_OUT((byte) 3, "已过期"),
        APPLY_CANCEL((byte) 4, "已取消");
        //--------------------------------------------
        private Byte value = -1;
        private String text = "";

        private OrderApprovalCancelStatus(byte value, String text) {
            this.value = value;
            this.text = text;
        }

        public static OrderApprovalCancelStatus getByValue(byte value) {
            for (OrderApprovalCancelStatus orderStatus : values()) {
                if (orderStatus.value == value ) {
                    return orderStatus;
                }
            }
            return null;
        }

        public Byte value() {
            return this.value;
        }

        public String text() {
            return this.text;
        }
    }

    /**申请取消类型**/
    public enum OrderCancellationType {
        APPROVAL_RECALL((byte) 3, "用户撤回申请"),
        APPROVAL_TIME_OUT((byte) 4, "超时自动取消审批");
        //--------------------------------------------
        private Byte value = -1;
        private String text = "";

        private OrderCancellationType(byte value, String text) {
            this.value = value;
            this.text = text;
        }

        public static OrderCancellationType getByValue(byte value) {
            for (OrderCancellationType orderStatus : values()) {
                if (orderStatus.value == value ) {
                    return orderStatus;
                }
            }
            return null;
        }

        public Byte value() {
            return this.value;
        }

        public String text() {
            return this.text;
        }
    }

    @Getter
    public enum TripType {
        //主行程
        MAIN(1, "主行程"),
        //子行程
        SUB(2, "子行程"),
        ;
        private Integer type;
        private String desc;

        private TripType(Integer type, String desc) {
            this.type = type;
            this.desc = desc;
        }

    }


    //内部用车订单强制结束，还是正常结束
    @Getter
    public enum InternalOrderTerminationType {
        //内部用车订单强制结束
        FORCE(2, "强制结束"),
        //内部用车订单正常结束
        NORMAL(1, "正常结束");

        private Integer value;
        private String text;

        InternalOrderTerminationType(Integer value, String text) {
            this.value = value;
            this.text = text;
        }
    }

}
