package com.izu.mrcar.order.consts;

import org.apache.commons.lang.StringUtils;

import java.util.Arrays;

public enum QuestionSourceEnum {
    MY(Byte.valueOf("9"),"我的"),
    ORDER(Byte.valueOf("1"),"子行程详情"),
    ORDER_APPLY(Byte.valueOf("2"),"主行程详情"),
    self_help_order(Byte.valueOf("10"),"自助取还问题反馈"),
    PUBLIC_GOVERNMENT_CAR_ORDER(Byte.valueOf("3"), "公务用车问题反馈")
    ;

    private Byte type;

    private String desc;

    QuestionSourceEnum(Byte type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static String getDescByStatus(Byte type){
        return Arrays.stream(QuestionSourceEnum.values())
                .filter(en->en.getType().equals(type))
                .findAny()
                .map(QuestionSourceEnum::getDesc)
                .orElse(StringUtils.EMPTY);
    }

    public Byte getType() {
        return type;
    }


    public String getDesc() {
        return desc;
    }


}
