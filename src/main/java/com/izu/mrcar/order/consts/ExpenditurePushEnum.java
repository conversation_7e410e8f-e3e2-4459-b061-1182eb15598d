package com.izu.mrcar.order.consts;

/**
 * <AUTHOR>
 * @description: 供应商支出单推送状态枚举
 * @date 2024/4/7 14:51
 */
public enum ExpenditurePushEnum {
    PUSH_SUCCESS(20,"推送成功"),
    PUSH_FAILED(10,"推送失败"),
    REJECT(30, "驳回"),
    ABOLISH(40, "废止"),
    SUCCESS(50, "支付成功"),
    ;

    private Integer type;
    private String message;

    ExpenditurePushEnum(Integer type, String message) {
        this.type = type;
        this.message = message;
    }

    public static ExpenditurePushEnum getByValue(int value) {
        for (ExpenditurePushEnum typeEnum : values()) {
            if (typeEnum.type == value ) {
                return typeEnum;
            }
        }
        return null;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
