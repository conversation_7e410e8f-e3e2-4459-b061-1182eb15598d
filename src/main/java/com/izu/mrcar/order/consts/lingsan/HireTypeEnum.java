package com.izu.mrcar.order.consts.lingsan;

import lombok.Getter;

@Getter
public enum HireTypeEnum {


    HIRE_DAY(1,"日租"),
    HIRE_MONTH(2,"月租"),
    HIRE_ONE_YEAR(3,"一年租"),
    HIRE_TWO_YEAR(4,"两年租"),
    ;

    HireTypeEnum(Integer type, String typeDesc){
        this.type = type;
        this.typeDesc = typeDesc;
    }

    private final Integer type;

    private final String typeDesc;

    public static String getTypeDesc(int type) {
        for (HireTypeEnum hireTypeEnum : HireTypeEnum.values()) {
            if (hireTypeEnum.getType() == type) {
                return hireTypeEnum.getTypeDesc();
            }
        }
        return null;
    }


}
