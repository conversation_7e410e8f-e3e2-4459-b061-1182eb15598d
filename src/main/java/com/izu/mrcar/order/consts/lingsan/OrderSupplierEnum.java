package com.izu.mrcar.order.consts.lingsan;


import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 供应商订单枚举类
 */
@Getter
@AllArgsConstructor
public class OrderSupplierEnum {

    /**
     * 供应商类型
     */
    @Getter
    @AllArgsConstructor
    public enum SupplierType {
        IZU(1, "首汽"),
        THIRD_PARTY(2, "三方");
        private final Integer code;
        private final String desc;

        public static String getNameByCode(int code) {
            for (SupplierType modelEnum : SupplierType.values()) {
                if (modelEnum.getCode() == code) {
                    return modelEnum.getDesc();
                }
            }
            return null;
        }

    }

    /**
     * 订单类型
     */
    @Getter
    @AllArgsConstructor
    public enum SupplierOrderType {
        VEHICLE(1, "车辆订单"),
        DRIVER(2, "司机订单");
        private final Integer code;
        private final String desc;

        public static String getNameByCode(int code) {
            for (SupplierOrderType modelEnum : SupplierOrderType.values()) {
                if (modelEnum.getCode() == code) {
                    return modelEnum.getDesc();
                }
            }
            return null;
        }
    }

    /**
     *  派车前确认展示订单
     */
    @Getter
    @AllArgsConstructor
    public enum ResourceDisplay {
        VEHICLE_DRIVER(1, "车和司机"),
        VEHICLE(2, "车辆"),
        DRIVER(3, "司机");
        private final Integer code;
        private final String desc;
    }

    /**
     * 换车标识
     */
    @Getter
    @AllArgsConstructor
    public enum ChangeVehicleFlag {
        NORMAL(1, "正常"),
        OLD(2, "换车原记录"),
        NEW(3, "换车后生成记录");
        private final Integer code;
        private final String desc;
    }

    /**
     * 换车标识
     */
    @Getter
    @AllArgsConstructor
    public enum PowerType {
        GASOLINE((byte)1, "汽油",1,"油车"),
        DIESEL_OIL((byte)2, "柴油",1,"油车"),
        MOTOR_DRIVEN((byte)3, "电动",2,"新能源"),
        HYBRID((byte)4, "混合",3,"油电混合");

        private final Byte fuelType;
        private final String fuelTypeDesc;
        private final Integer powerType;
        private final String powerTypeDesc;

        public static String getPowerTypeDescByCode(int code) {
            for (PowerType modelEnum : PowerType.values()) {
                if ( modelEnum.getFuelType().intValue() == code) {
                    return modelEnum.getPowerTypeDesc();
                }
            }
            return null;
        }

        public static Integer getPowerTypeByCode(Integer code) {
            if(Objects.isNull(code)){
                return null;
            }
            for (PowerType modelEnum : PowerType.values()) {
                if ( modelEnum.getFuelType().intValue() == code) {
                    return modelEnum.getPowerType();
                }
            }
            return null;
        }

        public static String getPowerTypeDescByPowerType(Integer code) {
            if (Objects.isNull(code)) {
                return null;
            }
            for (PowerType modelEnum : PowerType.values()) {
                if (modelEnum.getPowerType().equals(code)) {
                    return modelEnum.getPowerTypeDesc();
                }
            }
            return null;
        }
    }

}
