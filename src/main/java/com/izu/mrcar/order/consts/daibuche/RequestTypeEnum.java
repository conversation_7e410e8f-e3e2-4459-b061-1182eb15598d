package com.izu.mrcar.order.consts.daibuche;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;


@Getter
@AllArgsConstructor
public enum RequestTypeEnum {

    GET(0, "get"),
    POST(1, "post"),
    PUT(2, "put"),
    DELETE(3, "delete"),
    ;

    private Integer code;

    private String name;

    public static String getName(Integer code) {
        if (Objects.nonNull(code)) {
            for (RequestTypeEnum requestTypeEnum : RequestTypeEnum.values()) {
                if (requestTypeEnum.getCode().equals(code)) {
                    return requestTypeEnum.getName();
                }
            }
        }
        return null;
    }

}
