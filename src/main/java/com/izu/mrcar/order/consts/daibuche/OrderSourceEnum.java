package com.izu.mrcar.order.consts.daibuche;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang.StringUtils;

import java.util.Arrays;

/**
 * '订单来源 1手动创建 2系统推送
 */
@Getter
@AllArgsConstructor
public enum OrderSourceEnum {
    HAND((byte)1, "手动创建"),
    AUTO((byte)2, "系统推送"),

    ;


    private Byte code;

    private String name;


    public static OrderSourceEnum getByCode(Byte code) {
        return Arrays.stream(OrderSourceEnum.values()).filter(e -> e.getCode().equals(code)).findAny().orElse(null);
    }

    public static String getNameByCode(Byte code) {
        OrderSourceEnum orderFeeEnum = getByCode(code);
        return orderFeeEnum != null ? orderFeeEnum.getName() : StringUtils.EMPTY;
    }

}
