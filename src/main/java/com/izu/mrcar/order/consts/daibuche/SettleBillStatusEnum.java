package com.izu.mrcar.order.consts.daibuche;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang.StringUtils;

import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

/**
 * 结算账单状态（订单账单、单车账单、公司账单统一状态）
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum SettleBillStatusEnum {

    //注：待出账为订单数据的初始账单状态，实际账单无此状态
    WAIT_GEN_BILL(0, "待出账"),
    TO_BE_CONFIRMED(10, "账单待确认"),
    PUSHED(20, "账单已推送"),
    PUSH_FAIL(30, "账单推送失败"),
    ;

    private final Integer code;

    private final String name;

    public static SettleBillStatusEnum getEnumByCode(Integer code) {
        return Arrays.stream(SettleBillStatusEnum.values()).filter(e -> Objects.equals(e.getCode(), code)).findAny().orElse(null);
    }

    public static String getNameByCode(Integer code) {
        return Optional.ofNullable(getEnumByCode(code)).map(SettleBillStatusEnum::getName).orElse(StringUtils.EMPTY);
    }

}
