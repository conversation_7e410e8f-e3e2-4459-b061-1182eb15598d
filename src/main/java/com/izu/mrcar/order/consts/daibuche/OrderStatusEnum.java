package com.izu.mrcar.order.consts.daibuche;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 订单状态 10待调度 20待送车 30待还车 40已完成 50已取消
 */
@Getter
@AllArgsConstructor
public enum OrderStatusEnum {

    TO_BE_DISPATCHED(10,"待调度"),
    TO_BE_PICKEDUP(20,"待送车"),
    TO_BE_RETURNED(30,"待还车"),
    COMPLETED(40,"已完成"),
    CANCELED(50,"已取消"),
    PENDING_DEPOSIT(0,"押金待支付")
    ;

    private Integer code;

    private String name;

    public static String getName(Integer code) {
        if(Objects.nonNull(code)){
            for (OrderStatusEnum orderStatusEnum : OrderStatusEnum.values()) {
                if (orderStatusEnum.getCode().equals(code)) {
                    return orderStatusEnum.getName();
                }
            }
        }
        return null;
    }

}
