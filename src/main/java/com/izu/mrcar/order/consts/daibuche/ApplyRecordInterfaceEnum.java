package com.izu.mrcar.order.consts.daibuche;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;


@Getter
@AllArgsConstructor
public enum ApplyRecordInterfaceEnum {

    NIO_UPDATE(0, "蔚来订单信息更新接口"),
    NIO_CANCEL(1, "蔚来订单取消接口"),
    ;

    private Integer code;

    private String name;

    public static String getName(Integer code) {
        if (Objects.nonNull(code)) {
            for (ApplyRecordInterfaceEnum applyRecordInterfaceEnum : ApplyRecordInterfaceEnum.values()) {
                if (applyRecordInterfaceEnum.getCode().equals(code)) {
                    return applyRecordInterfaceEnum.getName();
                }
            }
        }
        return null;
    }

}
