package com.izu.mrcar.order.consts.govPublicCar;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;


@Getter
@AllArgsConstructor
public enum GovPublicCarOrderApproveStatusEnum {
    PENDING_DEPARTURE((byte)7, "待出发"),
    IN_USE((byte)8, "用车中"),
    COMPLETED((byte)10, "已完成"),
    CANCELED((byte)11, "已取消");

    private final Byte code;
    private final String name;

    public static String getName(Byte code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findFirst().map(GovPublicCarOrderApproveStatusEnum::getName).orElse(null);
    }
}
