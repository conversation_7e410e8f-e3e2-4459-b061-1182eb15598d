package com.izu.mrcar.order.consts.lingsan;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @Program: mrcar-order-common
 * @Description: ${description}
 * @Author: inT
 * @Create: 2023-01-16
 */
@Getter
@AllArgsConstructor
public enum OrderStatusEnum {

    CONFIRMATION_BEFORE_DELIVERY(1,"派送前确认",1),
    DISPATCH_RETURN(2,"派单退回",2),
    PRE_DELIVERY_REVIEW(3,"派送前审核",2),
    PRE_DELIVERY_REVIEW_REJECT(4,"派送前审核驳回",1),
    WAITING_DELIVERY(5,"待派送",1),
    DISPATCHING(6,"派送中",1),
    IN_USE(7,"使用中",2),
    CHANGING_TRAINS_OR_DRIVERS(8,"换车/换司机中",2),
    RETURNED_TO_BE_CONFIRMED(9,"归还待确认",1),
//    RETURNED_DATA_REVIEW(10,"归还资料审核中",2),
    COMPLETED(11,"已完成",1),
    ORDER_CANCELED(12,"已取消",2),
//    DISPATCHING_DATA_REVIEW(13,"派送资料审核中",2),
//    WAITING_CUSTOMER_CONFIRMED(14,"待客户确认送达",2),
    ;

    private Integer status;

    private String statusDesc;

    /**
     * 待操作的订单
     */
    private Integer flag;

    public static String getStatusDesc(int status) {
        for (OrderStatusEnum statusEnum : OrderStatusEnum.values()) {
            if (statusEnum.getStatus() == status) {
                return statusEnum.getStatusDesc();
            }
        }
        return null;
    }

}
