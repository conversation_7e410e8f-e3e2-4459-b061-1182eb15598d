package com.izu.mrcar.order.consts.govPublicCar;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;


@Getter
@AllArgsConstructor
public enum GovPublicCarApproveStatusCompleteEnum {

    PENDING_APPROVAL((byte)1, "待审批"),
    APPROVAL_WITHDRAWN((byte)2, "审批撤回"),
    APPROVAL_PASSED((byte)3, "审批通过"),
    APPROVAL_REJECTED((byte)4, "审批驳回"),
    PENDING_DEPARTURE((byte)7, "待出发"),
    IN_USE((byte)8, "用车中"),
    PENDING_VERIFICATION((byte)9, "待核实"),
    COMPLETED((byte)10, "已完成"),
    CANCELED((byte)11, "已取消");

    private final Byte code;
    private final String name;

    public static String getName(Byte code) {
        return Arrays.stream(values()).filter(e -> e.getCode().equals(code)).findFirst().map(GovPublicCarApproveStatusCompleteEnum::getName).orElse(null);
    }


    public static Byte getCode(String name) {
        return Arrays.stream(values()).filter(e -> e.getName().equals(name)).findFirst().map(GovPublicCarApproveStatusCompleteEnum::getCode).orElse(null);
    }
}
