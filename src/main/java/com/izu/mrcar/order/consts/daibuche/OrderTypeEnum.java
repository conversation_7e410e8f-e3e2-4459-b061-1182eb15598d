package com.izu.mrcar.order.consts.daibuche;

/**
 * 代步车订单类型枚举
 * <AUTHOR>
 * @date 2024/5/21 15:29
 */
public enum OrderTypeEnum {
    NIO( (byte) 1, "nio");

    /**
     * 订单类型code
     */
    private final Byte type;
    /**
     * 订单类型tag
     */
    private final String tag;

    OrderTypeEnum(Byte type, String tag) {
        this.type = type;
        this.tag = tag;
    }

    public Byte getType() {
        return type;
    }

    public String getTag() {
        return tag;
    }


    public static OrderTypeEnum getEnumByType(Byte type){
        for (OrderTypeEnum typeEnum : values()){
            if (typeEnum.getType().equals(type)){
                return typeEnum;
            }
        }
        throw new IllegalArgumentException("no enum match");
    }
}
