package com.izu.mrcar.order.consts;

import lombok.Getter;

/**
 * 里程来源（轨迹来源）
 *
 * <AUTHOR>
 * @date : 2022/11/27
 */
@Getter
public enum MileageSourceEnum {
    BAIDU_YING_YAN(Byte.valueOf("1"), "百度鹰眼","百度鹰眼"),
    OBD(Byte.valueOf("2"), "车机上报","车机"),
    APP_GPS(Byte.valueOf("3"), "APP GPS上报","APP"),
    CAR_GPS(Byte.valueOf("4"), "车载GPS上报","车载GPS");

    private Byte code;

    private String desc;

    private String show;

    MileageSourceEnum(Byte code, String desc,String show) {
        this.code = code;
        this.desc = desc;
        this.show=show;
    }


}
