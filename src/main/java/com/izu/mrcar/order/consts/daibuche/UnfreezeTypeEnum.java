package com.izu.mrcar.order.consts.daibuche;

import lombok.Getter;

@Getter
public enum UnfreezeTypeEnum {

    MANUAL_UNFREEZE((byte) 0, "手动解冻"),
    AUTO_UNFREEZE((byte) 1, "自动解冻");

    private final byte code;
    private final String name;

    UnfreezeTypeEnum(byte code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 根据code查找对应的name
     *
     * @param code 解冻类型code
     * @return 解冻类型name，如果未找到返回null
     */
    public static String getNameByCode(byte code) {
        for (UnfreezeTypeEnum type : UnfreezeTypeEnum.values()) {
            if (type.getCode() == code) {
                return type.getName();
            }
        }
        return null; // 如果未找到对应的code，返回null
    }
}