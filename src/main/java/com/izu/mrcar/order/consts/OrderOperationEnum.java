package com.izu.mrcar.order.consts;

/**
 * @description: 订单操作
 **/
public enum OrderOperationEnum {

    OPERATION_OTHER(-1, "其他"),
    OPERATION_CANCEL(0, "取消"),
    OPERATION_CREATE(10, "下单"),
    OPERATION_SCHEDULE(20, "调度"),
    OPERATION_START(30, "司机出发"),
    OPERATION_ARRIVE(40, "司机到达"),
    OPERATION_START_TRIP(50, "开始行程"),
    OPERATION_FINISH_TRIP(60, "结束行程"),
    OPERATION_ATTACH(70, "费用补录"),
    OPERATION_ADJUSTMENT(80, "调价"),
    OPERATION_SETTLEMENT(90, "结算");
    // --------------------------------------------
    private int value = -1;
    private String text  = "";
    private OrderOperationEnum(int value, String text){
        this.value = value;
        this.text  = text;
    }
    public int value() {
        return this.value;
    }
    public String text() {
        return this.text;
    }

    /**订单操作是否合法**/
    public static boolean isValid( final int actionEnum) {
        boolean include = false;
        for (OrderOperationEnum e: values()){
            if(e.value==actionEnum){
                include = true;
                break;
            }
        }
        return include;
    }

    public static OrderOperationEnum getByValue(int value) {
        for (OrderOperationEnum actionEnum : values()) {
            if (actionEnum.value==(value) ) {
                return actionEnum;
            }
        }
        return null;
    }
}
