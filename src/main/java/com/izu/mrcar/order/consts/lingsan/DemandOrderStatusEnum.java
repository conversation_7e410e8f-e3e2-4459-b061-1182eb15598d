package com.izu.mrcar.order.consts.lingsan;


import lombok.Getter;

@Getter
public enum DemandOrderStatusEnum {

    DISPATCH_WAIT(1,"待派单"),
    DISPATCH_FINISH(2,"已派单"),
    DISPATCH_CANCEL(3,"已取消"),
    ;

    DemandOrderStatusEnum(Integer status, String statusDesc){
        this.status = status;
        this.statusDesc = statusDesc;
    }

    private final Integer status;

    private final String statusDesc;

    public static String getStatusDesc(int status) {
        for (DemandOrderStatusEnum demandOrderStatusEnum : DemandOrderStatusEnum.values()) {
            if (demandOrderStatusEnum.getStatus() == status) {
                return demandOrderStatusEnum.getStatusDesc();
            }
        }
        return null;
    }

}
