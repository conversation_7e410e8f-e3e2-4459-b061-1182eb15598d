package com.izu.mrcar.order;

import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: 设备配置
 * com.izu.mrcar.iot.iotEnum.DeviceTypeEnum
 * @date 2023/8/25 10:42
 */
@Getter
public enum MileageSetTypeEnum {

    OBD(Byte.valueOf("2"), "车机上报","车机",4),
    APP_GPS(Byte.valueOf("3"), "APP GPS上报","APP",3),
    CAR_GPS(Byte.valueOf("4"), "车载GPS上报","车载GPS",1);

    private Byte code;

    private String desc;

    private String show;

    private Integer deviceType;


    MileageSetTypeEnum(Byte code, String desc, String show, Integer deviceType) {
        this.code = code;
        this.desc = desc;
        this.show = show;
        this.deviceType = deviceType;
    }

    public static MileageSetTypeEnum getSetType(Byte code){
        return Arrays.stream(values()).filter(e-> Objects.equals(code,e.getCode())).findFirst().orElse(null);
    }

//    WIRED(1, "有线设备"),
//    WIFI(2, "无线设备"),
//    OBD(3, "OBD"),
//    TBOX(4, "车机");
}
