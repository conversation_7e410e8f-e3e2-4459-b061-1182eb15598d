package com.izu.mrcar.order.restapi;

import com.alibaba.fastjson.JSON;
import com.izu.asset.MrCarAssetRestCenter;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.dto.businessOrder.*;
import com.izu.mrcar.order.dto.charge.ChargeBillReqDTO;
import com.izu.mrcar.order.util.MrCarOrderDateUtil;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> ON 2022/12/6.
 */
public class BusinessOrderApi {
    public static RestResponse getAdditionRecord(BillAttachReqDTO billAttachReqDTO){
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.BUSINESS_GET_ADDITION_RECORD);
        Map<String,Object> params = JSON.parseObject(JSON.toJSONString(billAttachReqDTO));
        return RestClient.requestInside(BaseHttpClient.HttpMethod.GET,url,params,null);
    }
    public static RestResponse getAdditionRecordByBussCode(BillAttachReqDTO billAttachReqDTO){
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.BUSINESS_GET_ADDITION_RECORD_BY_BUSSCODE);
        Map<String,Object> params = JSON.parseObject(JSON.toJSONString(billAttachReqDTO));
        return RestClient.requestInside(BaseHttpClient.HttpMethod.GET,url,params,null);
    }

    public static RestResponse getBillOrder(String orderNo){
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.BUSINESS_GET_ORDER_BILL);
        Map<String,Object> params = new HashMap<>();
        params.put("orderNo",orderNo);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.GET,url,params,null);
    }

    public static RestResponse getBillOrderSettled(BillAttachReqDTO billAttachReqDTO){
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.BUSINESS_GET_ORDER_BILL_SETTLED_BUSSCODE);
        Map<String,Object> params = JSON.parseObject(JSON.toJSONString(billAttachReqDTO));
        return RestClient.requestInside(BaseHttpClient.HttpMethod.GET,url,params,null);
    }

    public static RestResponse getBillOrderCompletedCity(BillAttachReqDTO billAttachReqDTO){
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.BUSINESS_GET_ORDER_BILL_COMPLETE_CITY);
        Map<String,Object> params = JSON.parseObject(JSON.toJSONString(billAttachReqDTO));
        return RestClient.requestInside(BaseHttpClient.HttpMethod.GET,url,params,null);
    }

    public static RestResponse getBillOrderCompletedBuss(BillAttachReqDTO billAttachReqDTO){
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.BUSINESS_GET_ORDER_BILL_COMPLETE_BUSSCODE);
        Map<String,Object> params = JSON.parseObject(JSON.toJSONString(billAttachReqDTO));
        return RestClient.requestInside(BaseHttpClient.HttpMethod.GET,url,params,null);
    }

    public static RestResponse getBill(String orderApplyNo){
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.BUSINESS_GET_BILL);
        Map<String,Object> params = new HashMap<>();
        params.put("orderApplyNo",orderApplyNo);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.GET,url,params,null);
    }

    public static RestResponse getOrderInfo(String orderApplyNo){
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.BUSINESS_GET_ORDER_INFO);
        Map<String,Object> params = new HashMap<>();
        params.put("orderApplyNo",orderApplyNo);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.GET,url,params,null);
    }

    public static RestResponse getOrderInfoByOrderNo(String orderNo){
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.BUSINESS_GET_ORDER_INFO_BY_ORDER_NO);
        Map<String,Object> params = new HashMap<>();
        params.put("orderNo",orderNo);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.GET,url,params,null);
    }

    public static RestResponse listOrderInfo(String statisSTime,String statisETime){
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.BUSINESS_LIST_ORDER_INFO);
        Map<String,Object> params = new HashMap<>();
        params.put("statisSTime",statisSTime);
        params.put("statisETime",statisETime);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.GET,url,params,null);
    }

    public static RestResponse getBillSettled(BillAttachReqDTO attachReqDTO){
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.BUSINESS_GET_BILL_SETTLED);
        Map<String,Object> params = JSON.parseObject(JSON.toJSONString(attachReqDTO));
        return RestClient.requestInside(BaseHttpClient.HttpMethod.GET,url,params,null);
    }

    public static PageDTO getOrderApplyByCustomer(OrderApplyReqDTO orderApplyReqDTO){
        String bookingOrderStime = MrCarOrderDateUtil.dateTime2string(MrCarOrderDateUtil.getTimeBegin(orderApplyReqDTO.getBookingOrderStime()));
        String bookingOrderEtime = MrCarOrderDateUtil.dateTime2string(MrCarOrderDateUtil.getTimeEnd(orderApplyReqDTO.getBookingOrderEtime()));
        orderApplyReqDTO.setBookingOrderStime(bookingOrderStime);
        orderApplyReqDTO.setBookingOrderEtime(bookingOrderEtime);
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.BUSINESS_GET_ORDER_APPLY_CUSTOMER);
        if(orderApplyReqDTO.getCustomerType() != null && orderApplyReqDTO.getCustomerType().intValue() != 1){
            url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.BUSINESS_GET_ORDER_APPLY_PASSENGER);
        }
        Map<String,Object> params = JSON.parseObject(JSON.toJSONString(orderApplyReqDTO));
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.GET,url,params,null,ApplyOrderStatementCustomerDTO.class);
        if(restResponse.isSuccess()){
            return (PageDTO) restResponse.getData();
        }else{
            return new PageDTO(orderApplyReqDTO.getPage(),orderApplyReqDTO.getPageSize(),0,new ArrayList());
        }
    }

    /**
     * 上线时替换上面的方法
     */
    public static PageDTO getOrderApplyByCustomerPOST(OrderApplyReqDTO orderApplyReqDTO){
        String bookingOrderStime = MrCarOrderDateUtil.dateTime2string(MrCarOrderDateUtil.getTimeBegin(orderApplyReqDTO.getBookingOrderStime()));
        String bookingOrderEtime = MrCarOrderDateUtil.dateTime2string(MrCarOrderDateUtil.getTimeEnd(orderApplyReqDTO.getBookingOrderEtime()));
        orderApplyReqDTO.setBookingOrderStime(bookingOrderStime);
        orderApplyReqDTO.setBookingOrderEtime(bookingOrderEtime);
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.BUSINESS_GET_ORDER_APPLY_CUSTOMER);
        if(orderApplyReqDTO.getCustomerType() != null && orderApplyReqDTO.getCustomerType().intValue() != 1){
            url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.BUSINESS_GET_ORDER_APPLY_PASSENGER);
        }
        Map<String,Object> params = JSON.parseObject(JSON.toJSONString(orderApplyReqDTO));
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY,url,params,null,ApplyOrderStatementCustomerDTO.class);
        if(restResponse.isSuccess()){
            return (PageDTO) restResponse.getData();
        }else{
            return new PageDTO(orderApplyReqDTO.getPage(),orderApplyReqDTO.getPageSize(),0,new ArrayList());
        }
    }

    public static PageDTO getOrderApplyByPassenger(OrderApplyReqDTO orderApplyReqDTO){
        String bookingOrderStime = MrCarOrderDateUtil.dateTime2string(MrCarOrderDateUtil.getTimeBegin(orderApplyReqDTO.getBookingOrderStime()));
        String bookingOrderEtime = MrCarOrderDateUtil.dateTime2string(MrCarOrderDateUtil.getTimeEnd(orderApplyReqDTO.getBookingOrderEtime()));
        orderApplyReqDTO.setBookingOrderStime(bookingOrderStime);
        orderApplyReqDTO.setBookingOrderEtime(bookingOrderEtime);
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.BUSINESS_GET_ORDER_APPLY_PASSENGER);
        Map<String,Object> params = JSON.parseObject(JSON.toJSONString(orderApplyReqDTO));
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.GET,url,params,null,ApplyOrderStatementCustomerDTO.class);
        if(restResponse.isSuccess()){
            return (PageDTO) restResponse.getData();
        }else{
            return new PageDTO(orderApplyReqDTO.getPage(),orderApplyReqDTO.getPageSize(),0,new ArrayList());
        }
    }

    public static PageDTO getOrderApplyByVehicle(OrderApplyByVehicleReqDTO orderApplyByVehicleReqDTO){
        if(orderApplyByVehicleReqDTO.getOrderTypeList() == null || orderApplyByVehicleReqDTO.getOrderTypeList().size() < 0){
            if(orderApplyByVehicleReqDTO.getOrderType() != null && orderApplyByVehicleReqDTO.getOrderType().size() > 0){
                List<Byte> orderType = orderApplyByVehicleReqDTO.getOrderType().stream().map(Byte::parseByte).collect(Collectors.toList());
                orderApplyByVehicleReqDTO.setOrderTypeList(orderType);
            }
        }
        String bookingOrderStime = MrCarOrderDateUtil.dateTime2string(MrCarOrderDateUtil.getTimeBegin(orderApplyByVehicleReqDTO.getBookingOrderStime()));
        String bookingOrderEtime = MrCarOrderDateUtil.dateTime2string(MrCarOrderDateUtil.getTimeEnd(orderApplyByVehicleReqDTO.getBookingOrderEtime()));
        orderApplyByVehicleReqDTO.setBookingOrderStime(bookingOrderStime);
        orderApplyByVehicleReqDTO.setBookingOrderEtime(bookingOrderEtime);
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.BUSINESS_GET_ORDER_APPLY_VEHICLE);
        Map<String,Object> params = JSON.parseObject(JSON.toJSONString(orderApplyByVehicleReqDTO));
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY,url,params,null, ApplyOrderStatementVehicleDTO.class);
        if(restResponse.isSuccess()){
            PageDTO pageDTO = (PageDTO) restResponse.getData();
            return pageDTO;
        }else{
            return new PageDTO(orderApplyByVehicleReqDTO.getPage(),orderApplyByVehicleReqDTO.getPageSize(),0,new ArrayList());
        }
    }

    public static PageDTO getOrderApplyByDriver(OrderApplyDriverReqDTO orderApplyDriverReqDTO){
        String bookingOrderStime = MrCarOrderDateUtil.dateTime2string(MrCarOrderDateUtil.getTimeBegin(orderApplyDriverReqDTO.getBookingOrderStime()));
        String bookingOrderEtime = MrCarOrderDateUtil.dateTime2string(MrCarOrderDateUtil.getTimeEnd(orderApplyDriverReqDTO.getBookingOrderEtime()));
        orderApplyDriverReqDTO.setBookingOrderStime(bookingOrderStime);
        orderApplyDriverReqDTO.setBookingOrderEtime(bookingOrderEtime);
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.BUSINESS_GET_ORDER_APPLY_DRIVER);
        Map<String,Object> params = JSON.parseObject(JSON.toJSONString(orderApplyDriverReqDTO));
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.GET,url,params,null,ApplyOrderStatementDriverDTO.class);
        if(restResponse.isSuccess()){
            PageDTO pageDTO = (PageDTO) restResponse.getData();
            List<ApplyOrderStatementDriverDTO> applyOrderStatementDriverDTOS = pageDTO.getResult();
            applyOrderStatementDriverDTOS.stream().forEach(o->{
                o.setTotalTime(o.getTotalTime()==null? BigDecimal.ZERO:(o.getTotalTime().divide(new BigDecimal(3600), 2, RoundingMode.HALF_UP)));
            });
            return pageDTO;
        }else{
            return new PageDTO(orderApplyDriverReqDTO.getPage(),orderApplyDriverReqDTO.getPageSize(),0,new ArrayList());
        }
    }
    /**
     * 上线时替换上面的方法
     */
    public static PageDTO getOrderApplyByDriverPOST(OrderApplyDriverReqDTO orderApplyDriverReqDTO){
        String bookingOrderStime = MrCarOrderDateUtil.dateTime2string(MrCarOrderDateUtil.getTimeBegin(orderApplyDriverReqDTO.getBookingOrderStime()));
        String bookingOrderEtime = MrCarOrderDateUtil.dateTime2string(MrCarOrderDateUtil.getTimeEnd(orderApplyDriverReqDTO.getBookingOrderEtime()));
        orderApplyDriverReqDTO.setBookingOrderStime(bookingOrderStime);
        orderApplyDriverReqDTO.setBookingOrderEtime(bookingOrderEtime);
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.BUSINESS_GET_ORDER_APPLY_DRIVER);
        Map<String,Object> params = JSON.parseObject(JSON.toJSONString(orderApplyDriverReqDTO));
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY,url,params,null,ApplyOrderStatementDriverDTO.class);
        if(restResponse.isSuccess()){
            PageDTO pageDTO = (PageDTO) restResponse.getData();
            List<ApplyOrderStatementDriverDTO> applyOrderStatementDriverDTOS = pageDTO.getResult();
            applyOrderStatementDriverDTOS.stream().forEach(o->{
                o.setTotalTime(o.getTotalTime()==null? BigDecimal.ZERO:(o.getTotalTime().divide(new BigDecimal(3600), 2, RoundingMode.HALF_UP)));
            });
            return pageDTO;
        }else{
            return new PageDTO(orderApplyDriverReqDTO.getPage(),orderApplyDriverReqDTO.getPageSize(),0,new ArrayList());
        }
    }

    public static RestResponse getOrderApplyByCompany(List<Integer> companyIds){
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.BUSINESS_GET_ORDER_APPLY_COMPANY);
        Map<String,Object> params = new HashMap<>();
        params.put("companyIds", StringUtils.join(companyIds.toArray(),","));
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST,url,params,null);
    }

    public static RestResponse getOrderApplyByCompany(List<Integer> companyIds,String queryDate){
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.BUSINESS_GET_ORDER_APPLY_COMPANY);
        Map<String,Object> params = new HashMap<>();
        params.put("companyIds", StringUtils.join(companyIds.toArray(),","));
        params.put("queryDate",queryDate);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST,url,params,null);
    }

    public static BigDecimal getViolationCallCount(ChargeBillReqDTO param) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        RestResponse restResponse = RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, new MrcarOrderRestLocator().getRestUrl("/charge/bill/getTotalAmount"), paramMap, null, BigDecimal.class);
        if (restResponse.isSuccess()) {
            return (BigDecimal) restResponse.getData();
        }
        return null;
    }
}
