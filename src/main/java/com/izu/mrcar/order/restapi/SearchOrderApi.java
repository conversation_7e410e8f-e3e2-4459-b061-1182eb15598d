package com.izu.mrcar.order.restapi;

import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.dto.mrcar.OrderInfoDTO;
import com.izu.mrcar.order.dto.mrcar.output.OrderInfoForBenefitDTO;
import com.izu.user.util.ObjectTransferUtil;
import com.izu.user.util.SingletonFactory;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> dongxiya 2023/12/4 13:37
 */
public class SearchOrderApi {

    private static final MrcarOrderRestLocator mrcarOrderRestLocator = SingletonFactory.getSingleton(SingletonFactory.orderRestLocator, MrcarOrderRestLocator::new);

    public static List<OrderInfoForBenefitDTO> queryByOrderNos(@RequestBody List<String> orderNos) {
        String url = mrcarOrderRestLocator.getRestUrl("/order/queryByOrderNos");
        Map<String, Object> params = new HashMap<>();
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, orderNos);
        RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, url, params, null, OrderInfoForBenefitDTO.class);
        if (restResponse.isSuccess() && Objects.nonNull(restResponse.getData())) {
            return ObjectTransferUtil.cast(restResponse.getData());
        } else {
            return Collections.emptyList();
        }
    }


    public static List<OrderInfoDTO> getOrderInfoByVehicle(String vehicleIds, Date nowDate) {
        String url = mrcarOrderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.GET_ORDER_INFO_BY_VEHICLE_IDS);
        Map<String, Object> params = new HashMap<>();
        params.put("vehicleIds", vehicleIds);
        params.put("nowDate", nowDate);
        RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, url, params, null, OrderInfoDTO.class);
        if (restResponse.isSuccess() && Objects.nonNull(restResponse.getData())) {
            return ObjectTransferUtil.cast(restResponse.getData());
        } else {
            return Collections.emptyList();
        }
    }

    public static List<OrderInfoDTO> getDriverByName(Integer companyId, String driverName, Date nowDate) {
        String url = mrcarOrderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.GET_ORDER_INFO_BY_DRIVER_NAME);
        HashMap<String, Object> params = new HashMap<>();
        params.put("driverName", driverName);
        params.put("companyId", companyId);
        params.put("nowDate", nowDate);
        RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POST,url, params,null, OrderInfoDTO.class);
        if(restResponse!=null && restResponse.isSuccess()){
            return ObjectTransferUtil.cast(restResponse.getData());
        }else{
            return Collections.emptyList();
        }
    }
}
