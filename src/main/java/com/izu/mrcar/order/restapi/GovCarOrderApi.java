package com.izu.mrcar.order.restapi;

import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.dto.govcar.order.GovCarOrderGetNoFinishByVehicleVinReqDTO;
import com.izu.mrcar.order.dto.govcar.order.GovCarOrderInfoDTO;
import com.izu.mrcar.order.dto.mrcar.OrderInfoDTO;
import com.izu.user.util.ObjectTransferUtil;
import com.izu.user.util.SingletonFactory;

import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.izu.framework.web.rest.client.BaseHttpClient.POSTBODY_MAP_KEY;

/**
 * <AUTHOR>
 * @date 2024/8/10 18:34
 */
public class GovCarOrderApi {

    private static final MrcarOrderRestLocator mrcarOrderRestLocator = SingletonFactory.getSingleton(SingletonFactory.orderRestLocator, MrcarOrderRestLocator::new);

    public static GovCarOrderInfoDTO getNoFinishOrderByVehicleVin(GovCarOrderGetNoFinishByVehicleVinReqDTO reqDTO) {
        String url = mrcarOrderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.GOV_CAR_ORDER_GET_NO_FINISH_BY_VEHICLE_VIN);
        Map<String, Object> params = new HashMap<>();
        params.put(POSTBODY_MAP_KEY, reqDTO);
        RestResponse restResponse =
                RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, params, null, GovCarOrderInfoDTO.class);
        if (restResponse.isSuccess() && Objects.nonNull(restResponse.getData())) {
            return ObjectTransferUtil.cast(restResponse.getData());
        }
        return null;
    }
}
