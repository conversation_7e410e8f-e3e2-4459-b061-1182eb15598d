package com.izu.mrcar.order.restapi;

import com.alibaba.fastjson.JSON;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.exception.RestErrorException;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.dto.dispatching.ScheduleDTO;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;

/**
 * 调度单相关API
 *
 * <AUTHOR> on  2024/11/4 下午3:54
 */
@Slf4j
public class ScheduleApi {


    /**
     * 获取调度详情
     *
     * @param scheduleId 调度单ID
     * @return 调度详情
     */
    public static ScheduleDTO getScheduleDetail(Long scheduleId) throws RestErrorException {
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.GET_SCHEDULE_DETAIL_BY_ID);
        HashMap<String, Object> restParam = new HashMap<>();
        restParam.put("scheduleId", scheduleId);
        RestResponse restResponse = RestClient.requestForObject(BaseHttpClient.HttpMethod.POST,
                restUrl, restParam, null, ScheduleDTO.class);
        if (!restResponse.isSuccess()) {
            log.error("调度详情获取失败,结果:{}", JSON.toJSONString(restResponse));
            throw ExceptionFactory.createRestException("调度详情获取失败");
        }
        return (ScheduleDTO) restResponse.getData();
    }


}
