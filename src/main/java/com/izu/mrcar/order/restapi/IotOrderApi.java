package com.izu.mrcar.order.restapi;

import com.alibaba.fastjson.JSON;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.dto.businessOrder.OrderInfoMileageByDateDTO;
import com.izu.mrcar.order.dto.iotOrder.OrderDriverInfoDTO;
import com.izu.mrcar.order.dto.iotOrder.OrderInfoByVehicleReqDTO;
import com.izu.mrcar.order.dto.iotOrder.OrderInfoStatResDTO;
import com.izu.mrcar.order.dto.order.TraceOrderByTimeReq;
import com.izu.mrcar.order.dto.order.TraceOrderByTimeResp;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.izu.framework.web.rest.client.BaseHttpClient.POSTBODY_MAP_KEY;

/**
 * <AUTHOR> ON 2022/12/26.
 */
public class IotOrderApi {


    public static List<OrderInfoStatResDTO> listOrderInfoByVehicle(OrderInfoByVehicleReqDTO orderInfoByVehicleReqDTO){
        List<OrderInfoStatResDTO> list = new ArrayList<>();
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.IOT_ORDER_INFO_LIST_BY_VEHICLE);
        Map<String,Object> params = JSON.parseObject(JSON.toJSONString(orderInfoByVehicleReqDTO));
        RestResponse restResponse = RestClient.requestInside(BaseHttpClient.HttpMethod.GET,url,params,null);
        if(restResponse.isSuccess()){
            list = JSON.parseArray(JSON.toJSONString(restResponse.getData()),OrderInfoStatResDTO.class);
        }
        return list;
    }

    @Deprecated
    public static List<OrderInfoMileageByDateDTO> listOrderInfoByDate(OrderInfoByVehicleReqDTO orderInfoByVehicleReqDTO){
        List<OrderInfoMileageByDateDTO> list = new ArrayList<>();
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.IOT_ORDER_INFO_LIST_BY_DATE);
        Map<String,Object> params = JSON.parseObject(JSON.toJSONString(orderInfoByVehicleReqDTO));
        RestResponse restResponse = RestClient.requestInside(BaseHttpClient.HttpMethod.GET,url,params,null);
        if(restResponse.isSuccess()){
            list = JSON.parseArray(JSON.toJSONString(restResponse.getData()),OrderInfoMileageByDateDTO.class);
        }
        return list;
    }
    public static List<OrderInfoMileageByDateDTO> listOrderInfoByDateNew(OrderInfoByVehicleReqDTO orderInfoByVehicleReqDTO){
        List<OrderInfoMileageByDateDTO> list = new ArrayList<>();
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.IOT_ORDER_INFO_LIST_BY_DATE);
        Map<String, Object> params = new HashMap<>();
        params.put(POSTBODY_MAP_KEY, orderInfoByVehicleReqDTO);
        RestResponse restResponse = RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY,url,params,null);
        if(restResponse.isSuccess()){
            list = JSON.parseArray(JSON.toJSONString(restResponse.getData()),OrderInfoMileageByDateDTO.class);
        }
        return list;
    }

    public static OrderDriverInfoDTO getOrderInfoByVehicle(Integer vehicleId,String vehicleLicense,String nowDate){
        OrderDriverInfoDTO orderDriverInfoDTO = new OrderDriverInfoDTO();
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.GET_ORDER_INFO_BY_VEHICLE);
        Map<String,Object> params = new HashMap<>();
        params.put("vehicleId",vehicleId);
        params.put("vehicleLicense",vehicleLicense);
        params.put("nowDate",nowDate);
        RestResponse restResponse = RestClient.requestForObject(BaseHttpClient.HttpMethod.POST,url,params,null,OrderDriverInfoDTO.class);
        if(restResponse!=null && restResponse.isSuccess()){
            orderDriverInfoDTO = (OrderDriverInfoDTO) restResponse.getData();
        }
        return orderDriverInfoDTO;
    }
    public static List<TraceOrderByTimeResp> getTraceOrderByTime(TraceOrderByTimeReq traceOrderByTimeReq){
        List<TraceOrderByTimeResp> traceOrderByTimeResps = new ArrayList<>();
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.TRACE_ORDER_SEARCH);
        Map<String, Object> params = new HashMap<>();
        params.put(POSTBODY_MAP_KEY, traceOrderByTimeReq);
        RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY,url,params,null,TraceOrderByTimeResp.class);
        if(restResponse!=null && restResponse.isSuccess()){
            traceOrderByTimeResps = (List<TraceOrderByTimeResp>) restResponse.getData();
        }
        return traceOrderByTimeResps;
    }
}
