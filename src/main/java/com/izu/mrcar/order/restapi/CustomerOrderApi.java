package com.izu.mrcar.order.restapi;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.dto.lingsan.customer.input.CustomerOrderJobSearchDTO;
import com.izu.mrcar.order.dto.lingsan.customer.output.CustomerOrderForJobList;
import lombok.extern.slf4j.Slf4j;

import java.util.*;

@Slf4j
public class CustomerOrderApi {

    public static List<CustomerOrderForJobList> listCustomerOrder(CustomerOrderJobSearchDTO customerOrderStatisticsSearch){
        log.info("查询客户订单信息参数:{}", JSONUtil.toJsonStr(customerOrderStatisticsSearch));
        if(Objects.isNull(customerOrderStatisticsSearch)){
            return new ArrayList<>(0);
        }
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.CUSTOMER_ORDER_JOB_LIST);
        Map<String,Object> params = new HashMap<>();
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, customerOrderStatisticsSearch);
        try {
            RestResponse restResponse = RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, params,null);
            if(!restResponse.isSuccess()){
                log.error("查询客户订单信息失败。{}", restResponse.getMsg());
                return new ArrayList<>(0);
            }
            return JSON.parseArray(JSON.toJSONString(restResponse.getData()), CustomerOrderForJobList.class);
        }catch (Exception e){
            log.error("查询客户订单信息异常。", e);
        }
        return new ArrayList<>(0);
    }

}
