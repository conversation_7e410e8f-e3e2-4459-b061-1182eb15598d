package com.izu.mrcar.order.exception;

/**
 * <AUTHOR>
 * @date 2024/8/18 13:33
 */
public class OrderException extends RuntimeException{

    private int errorCode;
    private String msg;


    public OrderException(String msg, int errorCode) {
        super(msg);
        this.msg = msg;
        this.errorCode = errorCode;
    }



    public void setMsg(String msg) {
        this.msg = msg;
    }
    public String getMsg() {
        return msg;
    }

    public void setErrorCode(int errorCode){
        this.errorCode = errorCode;
    }

    public int getErrorCode() {
        return errorCode;
    }

}
