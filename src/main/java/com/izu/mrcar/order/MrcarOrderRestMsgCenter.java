package com.izu.mrcar.order;

public class MrcarOrderRestMsgCenter {

    /**
     * 移动端创建订单_前置信息缓存
     **/
    public static final String ORDER_MAKEORDERCHECK = "/orderApp/makeOrderCheck.json";

    /**
     * 移动端创建订单_计算中间态信息
     **/
    public static final String ORDER_MAKEORDERPRICECHANGE = "/orderApp/makeOrderPriceChange.json";

    /**
     * 移动端创建订单_变更优惠券
     **/
    public static final String ORDER_MAKEORDERCOUPONCHANGE = "/orderApp/makeOrderCouponChange.json";

    /**
     * 移动端创建订单_确认下单
     **/
    public static final String ORDER_MAKEORDER = "/orderApp/makeOrder.json";

    /**
     * 移动端_下单可用优惠券列表
     **/
    public static final String ACTIVITYCOUPON_CHOOSECOUPONLIST = "/activityCoupon/chooseCouponList.json";

    /**
     * 司机操作订单接口
     **/
    public static final String DRIVER_ACTION = "/orderOperation/driverAction";

    /**
     * 司机结束行程接口
     **/
    public static final String FINISH_TRIP_V1 = "/orderOperation/driverFinishTrip/v1";

    //强制结束行程
    public static final String FORCE_FINISH_TRIP = "/orderOperation/forceFinishTrip";

    /**
     * 司机费用补录接口
     **/
    @Deprecated
    public static final String ADD_ATTACH = "orderOperation/driverAddAttach";
    /**
     * 获取费用补录详情接口
     **/
    public static final String GET_ORDER_ATTACH = "consumeBill/getAttachOrder";
    /**
     * 获取司机任务订单列表接口
     **/
    public static final String GET_ORDER_LIST = "order/driverOrderList";
    /**
     * 上传费用补录凭证照片接口
     **/
    public static final String UPLOAD_ORDER_CERTIFICATE = "order/uploadCertificate";
    /**
     * 商务用车上传服务前后照片接口
     **/
    public static final String UPLOAD_ORDER_VEHICLE_IMG = "order/uploadOrderVehicleImg";
    /**
     * 商务用车上传服务前后照片接口
     **/
    public static final String UPLOAD_ORDER_VEHICLE_IMG_V1 = "order/uploadOrderVehicleImg/v1";
    /**
     * 获取订单状态接口
     **/
    public static final String GET_ORDER_STATUS = "/order/queryByOrderNo";
    /**
     * 判断是否上传过照片接口
     **/
    public static final String IS_UPLOAD_ORDER_IMG = "order/checkimg";
    /**
     * 获取司机各个状态订单列表接口
     **/
    public static final String GET_USER_CENTER_ORDER_LIST = "order/driverUserCenterOrderList";
    /**
     * 获取司机首页接单列表接口
     **/
    public static final String GET_HOME_PAGE_ORDER_LIST = "order/homePageOrderList";
    /**
     * 查询司机是否有正在服务的订单
     */
    public static final String EXISTS_SERVICE_ORDER = "order/existsServiceOrder";
    /**
     * 有线 GPS上报行驶距离计算
     */
    public static final String CAR_GPS_LOCUS_DISTANCE = "/car/location/distance/estimate/wired.json";
    /**
     * APP GPS上报行驶距离计算
     */
    public static final String APP_GPS_LOCUS_DISTANCE = "/car/location/distance/estimate/mr.json";
    /**
     * 车机计算行驶距离
     */
    public static final String QUBE_LOCUS_DISTANCE = "/car/location/distance/estimate/for.json";
    /**
     * 司机操作事件上报
     **/
    public static final String DRIVER_ACTION_SAVE = "driver/driverAction";
    /**
     * 自助取还行程操作
     **/
    public static final String ORDER_SELF_HELP_RENT_ACTION = "/orderOperation/orderSelfHelpRentAction";

    /**
     * 撤回用车申请
     **/
    public static final String CANCEL_ORDER_APPLY = "/orderApply/cancelApply";


    public static final String BUSINESS_GET_ADDITION_RECORD = "/business/order/v1/get";

    public static final String BUSINESS_GET_ADDITION_RECORD_BY_BUSSCODE = "/business/order/v1/getByBuss";

    public static final String BUSINESS_GET_ORDER_APPLY_CUSTOMER = "/business/orderApply/v1/getByCustomer";

    public static final String BUSINESS_GET_ORDER_APPLY_PASSENGER = "/business/orderApply/v1/getByPassenger";

    public static final String BUSINESS_GET_ORDER_APPLY_VEHICLE = "/business/orderApply/v1/getByVehicle";

    public static final String BUSINESS_GET_ORDER_APPLY_DRIVER = "/business/orderApply/v1/getByDriver";

    public static final String BUSINESS_GET_ORDER_APPLY_COMPANY = "/business/orderApply/v1/getByCompany";

    public static final String BUSINESS_GET_ORDER_BILL = "/business/billOrder/v1/get";

    public static final String BUSINESS_GET_ORDER_BILL_SETTLED_BUSSCODE = "/business/billOrder/v1/getSettled";

    public static final String BUSINESS_GET_ORDER_BILL_COMPLETE_BUSSCODE = "/business/billOrder/v1/getCompleted";
    public static final String BUSINESS_GET_ORDER_BILL_COMPLETE_CITY = "/business/billOrder/v1/getCompCity";

    public static final String BUSINESS_GET_BILL = "/business/bill/v1/get";

    public static final String BUSINESS_GET_BILL_SETTLED = "/business/bill/v1/getSettled";

    public static final String BUSINESS_GET_ORDER_INFO = "/business/orderInfo/v1/get";

    public static final String BUSINESS_GET_ORDER_INFO_BY_ORDER_NO = "/business/orderInfo/v1/getByOrderNo";

    public static final String BUSINESS_LIST_ORDER_INFO = "/business/orderInfo/v1/list";
    // 按日订单统计
    public static final String BUSINESS_DAY_ORDER_STATISTICS = "/business/day/order/statistics";

    /**
     * 查询任务中订单号通过车辆ID和时间
     **/
    public static final String ORDER_WORK_QUERY_BY_VEHICLE_ID = "/order/queryWorkOrderNoByVehicleId";

    public static final String WORK_ORDER_APPLY_NO_QUERY_BY_VEHICLE_ID = "/order/queryWorkOrderApplyNoByVehicleId";

    public static final String ORDER_APPLY_NO_QUERY_BY_VEHICLE_ID = "/order/queryOrderApplyNoByVehicleId";

    public static final String ORDER_TRACK = "/order/track";

    public static final String ORDER_LOCK_OPERATION = "/order/operation/lock";

    public static final String EXIST_SERVICE_ORDER_V2 = "/order/v2/existsServiceOrder";

    public static final String TRY_START_TRAVEL_DIRECTLY = "/order/orderInfo/tryStartTravelDirectly";

    public static final String START_TRAVEL_DIRECTLY = "/order/orderInfo/startTravelDirectly";


    /**
     * 行程列表查询
     **/
    public static final String ORDER_APPLY_QUERY_ORDER_PAGE_LIST = "/orderApply/queryOrderPageList";

    /**
     * 行程详情查询
     **/
    public static final String QUERY_ORDER_APPLY_DETAIL = "/orderApply/queryByOrderApplyNo";

    /**
     * PC总调度单列表
     */
    public static final String LIST_SCHEDULE_PAGE = "/scheduleCenter/listSchedulePage";
    /**
     * 订单指派
     * 代码已经删除 商务车二期上线之后，可以直接删除这个字段
     */
    @Deprecated
    public static final String ORDER_DISPATCHER = "/scheduleCenter/dispatcher";
    /**
     * 订单重新指派
     */
    @Deprecated
    public static final String ORDER_RE_DISPATCHER = "/scheduleCenter/reDispatcher";
    /**
     * 订单调度
     */
    @Deprecated
    public static final String ORDER_SCHEDULE = "/scheduleCenter/schedule";
    /**
     * 订单重新调度
     */
    @Deprecated
    public static final String ORDER_RE_SCHEDULE = "/scheduleCenter/reSchedule";
    /**
     * 订单时间范围内各部门可用车辆数接口
     */
    public static final String LIST_AVAILABLE_VEHICLE = "/scheduleCenter/listAvailableVehicle";
    /**
     * PC总调度单详情
     */
    public static final String GET_SCHEDULE_DETAIL = "/scheduleCenter/getScheduleDetail";
    /**
     * PC子调度单列表
     */
    @Deprecated
    public static final String LIST_SUB_SCHEDULE_PAGE = "/scheduleCenter/listSubSchedulePage";
    /**
     * 调度、指派时获取需要调度或者指派的车辆数
     */
    public static final String GET_SCHEDULE_VEHICLE = "/scheduleCenter/getScheduleVehicle";
    /**
     * 车辆列表(PC/APP可选范围改为一致，按照数据权限)
     */
    public static final String SCHEDULE_LISTVEHICLE = "/scheduleCenter/listVehicle";
    /**
     * 校验车辆是否可用
     */
    public static final String CHECK_VEHICLE_IS_USABLE = "/scheduleCenter/checkVehicleIsUsable";
    /**
     * 校验司机是否可用
     */
    public static final String CHECK_DRIVER_IS_USABLE = "/scheduleCenter/checkDriverIsUsable";
    /**
     * 根据企业和用车时间段确定当前可用的车辆数据
     */
    public static final String LIST_VEHICLE_MODEL_COUNT = "/scheduleCenter/listVehicleModelCount";
    /**
     * 调度中心：司机列表
     */
    public static final String SCHEDULE_LIST_DRIVER = "/scheduleCenter/listDriver";
    /**
     * PC司机列表
     */
    public static final String LIST_DRIVER_FOR_PC = "/scheduleCenter/listDriverForPc";
    /**
     * 取消调度单
     */
    public static final String CANCEL_SCHEDULE = "/scheduleCenter/cancelSchedule";
    /**
     * 调度单过期
     */
    public static final String OVER_TIME_SCHEDULE = "/scheduleCenter/overTimeSchedule";
    /**
     * 释放库存
     */
    public static final String SCHEDULE_FINISH_TRIP = "/scheduleCenter/finishTrip";
    /**
     * 创建调度单
     */
    public static final String CREATE_SCHEDULE_ORDER = "/scheduleCenter/createScheduleOrder";

    /**
     * 获取调度单预约时间范围之内被占用的司机ID
     */
    public static final String QUERY_OCCUPIED_DRIVER_ID_LIST = "/scheduleCenter/queryOccupiedDriverIdList";

    /**
     * 获取调度单预约时间范围之内被占用的车辆ID
     */
    public static final String QUERY_OCCUPIED_VEHICLE_ID_LIST = "/scheduleCenter/queryOccupiedVehicleIdList";
    /**
     * 获取该车辆是否在预约时间内
     */
    public static final String QUERY_CHECK_CAR_STATUS_BY_LICENSE = "/scheduleCenter/checkCarStatus";

    /**
     * 批量获取车辆的并行订单个数
     */
    public static final String BATCH_GET_VEHICLE_PARALLEL_ORDER = "/scheduleCenter/batchGetVehicleParallelOrder";

    /**
     * 批量获取司机的并行订单个数
     */
    public static final String BATCH_GET_DRIVER_PARALLEL_ORDER = "/scheduleCenter/batchGetDriverParallelOrder";

    /**
     * 根据调度单ID获取开始用车时间
     */
    public static final String GET_SCHEDULE_BOOKING_START_DATE = "/scheduleCenter/getScheduleBookingStartDate";

    /**
     * 将一批司机按照订单量排序
     */
    public static final String SORT_DRIVER_ID_BY_ORDER_NUM = "/driverServiceStatistics/sortDriverIdByOrderNum";


    public static final String SCHEDULE_FINISH_SCHEDULE = "/scheduleFinish/schedule";

    /**
     * app端订单调度列表
     */
    public static final String LIST_SCHEDULE_PAGE_FOR_APP = "/scheduleQuery/listSchedulePageForApp";
    /**
     * APP待调度数量
     */
    public static final String COUNT_SCHEDULE_FOR_APP = "/scheduleQuery/countScheduleForApp";

    /**
     * APP总调度单详情
     */
    public static final String GET_SCHEDULE_DETAIL_FOR_APP = "/scheduleQuery/getScheduleDetail";

    /**
     * 判断车辆是否被占用
     */
    public static final String CHECK_CAR_USED = "/scheduleQuery/checkCarUsed";
    /**
     * 调度流水信息
     */
    public static final String GET_SCHEDULE_ORDER_BY_TIME = "/scheduleStatis/getScheduleOrderByTime";


    public static final String IOT_ORDER_INFO_LIST_BY_VEHICLE = "/iot/orderInfo/v1/listByVehicle";

    public static final String IOT_ORDER_INFO_LIST_BY_DATE = "/iot/orderInfo/v1/listByDate";

    public static final String GET_ORDER_INFO_BY_VEHICLE = "/get/orderInfo/byVehicle";

    public static final String GET_ORDER_INFO_BY_VEHICLE_IDS = "/order/get/orderInfo/byVehicleIds";

    public static final String GET_ORDER_INFO_BY_DRIVER_NAME = "/order/get/orderInfo/byDriverName";

    /**
     * 校验车辆是否有订单服务地址
     **/
    public static final String CHECK_VEHICLE_ORDER = "/order/checkUnDoOrderByParam";


    public static final String SAVE_CAR_LOCK_LOG = "/carLock/saveCarLockLog";

    /**
     * 需求单相关地址
     */
    public static final String DEMAND_ORDER_CREATE = "/lingsan/demandOrder/v1/create";
    public static final String DEMAND_ORDER_LIST_WEB = "/lingsan/demandOrder/v1/list/web";
    public static final String DEMAND_ORDER_LIST_APP = "/lingsan/demandOrder/v1/list/app";
    public static final String DEMAND_ORDER_DETAIL = "/lingsan/demandOrder/v1/detail";
    public static final String DEMAND_ORDER_DETAIL_ORDER = "/lingsan/demandOrder/v1/detail/order";
    public static final String DEMAND_ORDER_DETAIL_RELATE_ORDER = "/lingsan/demandOrder/v1/detail/relate/order";
    public static final String DEMAND_ORDER_SUPPLIER_DETAIL_ORDER = "/lingsan/demandOrder/v1/supplier/detail/order";
    public static final String DEMAND_ORDER_CANCEL = "/lingsan/demandOrder/v1/cancel";
    public static final String DEMAND_ORDER_CONTRACT_INFO = "/lingsan/demandOrder/getContractInfo";
    public static final String DEMAND_ORDER_LIST_PLATFORM = "/lingsan/demandOrder/v1/list/platform";
    public static final String DEMAND_ORDER_DETAIL_PLATFORM = "/lingsan/demandOrder/v1/detail/platform";
    public static final String DEMAND_ORDER_CREATE_PLATFORM = "/lingsan/demandOrder/v1/create/platform";
    public static final String DEMAND_ORDER_DETAIL_ORDER_PLATFORM = "/lingsan/demandOrder/v1/detail/order/platform";
    public static final String DEMAND_ORDER_DETAIL_RELATE_ORDER_PLATFORM = "/lingsan/demandOrder/v1/detail/relate/order/platform";

    /**
     * 客户订单相关地址
     */
    public static final String CUSTOMER_ORDER_LIST_WEB = "/lingsan/customerOrder/v1/list/web";
    public static final String CUSTOMER_ORDER_LIST_PLATFORM = "/lingsan/customerOrder/v1/list/platform";
    public static final String CUSTOMER_ORDER_LIST_APP = "/lingsan/customerOrder/v1/list/app";
    public static final String CUSTOMER_ORDER_DETAIL = "/lingsan/customerOrder/v1/detail";
    public static final String CUSTOMER_ORDER_COMPOSITE_DETAIL = "/lingsan/customerOrder/v1/composite/detail";
    public static final String CUSTOMER_ORDER_COMPOSITE_DETAIL_PLATFORM = "/lingsan/customerOrder/v1/composite/detail/platform";
    public static final String CUSTOMER_ORDER_CANCEL = "/lingsan/customerOrder/v1/cancel";
    public static final String CUSTOMER_ORDER_DISPATCH_INFO = "/lingsan/customerOrder/v1/dispatch/info";
    public static final String CUSTOMER_ORDER_DISPATCH = "/lingsan/customerOrder/v1/dispatch";
    public static final String CUSTOMER_ORDER_CONFIRMDELIVERY_SAVE = "/lingsan/customerOrder/confirmDelivery/save";
    public static final String CUSTOMER_ORDER_JOB_LIST = "/lingsan/customerOrder/v1/job/list";
    public static final String CUSTOMER_ORDER_TODO_PLATFORM = "/lingsan/customerOrder/v1/todo/platform";
    public static final String CUSTOMER_ORDER_CONFIRMDELIVERY_CHECK = "/lingsan/customerOrder/confirmDelivery/check";

    /**
     * 零散用车供应商支出账单相关url
     */
    public static final String LINGSAN_SUPPLIER_EXPEND_BILL_STATUS_LIST = "/lingsan/supplierExpendBill/statusList";
    public static final String LINGSAN_SUPPLIER_EXPEND_BILL_PAGE_LIST = "/lingsan/supplierExpendBill/pageList";
    public static final String LINGSAN_SUPPLIER_EXPEND_BILL_CONFIRM = "/lingsan/supplierExpendBill/confirm";
    public static final String LINGSAN_SUPPLIER_EXPEND_BILL_DETAIL = "/lingsan/supplierExpendBill/detail";
    public static final String LINGSAN_SUPPLIER_EXPEND_BILL_DETAIL_TYPE_LIST = "/lingsan/supplierExpendBill/detail/typeList";
    public static final String LINGSAN_SUPPLIER_EXPEND_BILL_DETAIL_PAGE_LIST = "/lingsan/supplierExpendBill/detail/pageList";
    public static final String LINGSAN_SUPPLIER_EXPEND_BILL_DETAIL_EXPORT = "/lingsan/supplierExpendBill/detail/export";

    /**
     * 零散用车平台支出账单相关url
     */
    public static final String LINGSAN_PLATFORM_EXPEND_BILL_STATUS_LIST = "/lingsan/platformExpendBill/statusList";
    public static final String LINGSAN_PLATFORM_EXPEND_BILL_SETTLE_STATUS_LIST = "/lingsan/platformExpendBill/settleStatusList";
    public static final String LINGSAN_PLATFORM_EXPEND_BILL_PAGE_LIST = "/lingsan/platformExpendBill/pageList";
    public static final String LINGSAN_PLATFORM_EXPEND_BILL_DETAIL = "/lingsan/platformExpendBill/detail";
    public static final String LINGSAN_PLATFORM_EXPEND_BILL_DETAIL_TYPE_LIST = "/lingsan/platformExpendBill/detail/typeList";
    public static final String LINGSAN_PLATFORM_EXPEND_BILL_DETAIL_PAGE_LIST = "/lingsan/platformExpendBill/detail/pageList";

    /**
     * 零散用车平台账单相关URL
     */
    public static final String PLATFORM_BILL_LIST = "/lingsan/bill/platformBillList";
    public static final String PLATFORM_CHECK_BILL = "/lingsan/bill/platformCheckBill";
    public static final String PLATFORM_SAVE_CHECK_BILL = "/lingsan/bill/platformSaveCheckBill";
    public static final String PLATFORM_CHECK_BILL_GET_TOTAL_AMOUNT_SUM = "/lingsan/bill/platformCheckBill/getTotalAmountSum";
    public static final String PLATFORM_SYSTEM_BILL_DETAIL = "/lingsan/bill/platformSystemBillDetail";
    public static final String PLATFORM_SYSTEM_BILL_DETAIL_EXPORT = "/lingsan/bill/platformSystemBillDetail/export";
    public static final String PLATFORM_NEWEST_BILL_DETAIL = "/lingsan/bill/platformNewestBillDetail";
    public static final String PLATFORM_BILL_DETAIL = "/lingsan/bill/platformBillDetail";
    public static final String PLATFORM_INCOME_AND_EXPENDITURE_DETAIL_LIST = "/lingsan/bill/platformIncomeAndExpenditureDetail/list";
    public static final String PLATFORM_INCOME_AND_EXPENDITURE_DETAIL_EXPORT_2_EMAIL = "/lingsan/bill/platformIncomeAndExpenditureDetail/export2Email";
    public static final String PLATFORM_INCOME_BILL_PUSH = "/lingsan/income/bill/push";

    /**
     * 零散用车客户账单相关URL
     */
    public static final String BILL_LIST = "/lingsan/bill/billList";
    public static final String CHECK_BILL_4_CUSTOMER = "/lingsan/bill/checkBill4Customer";
    public static final String SYSTEM_BILL_DETAIL = "/lingsan/bill/systemBillDetail";
    public static final String NEWEST_BILL_DETAIL = "/lingsan/bill/newestBillDetail";
    public static final String BILL_DETAIL = "/lingsan/bill/billDetail";

    /**
     * 零散用车供应商订单相关的URl
     */
    public static final String LINGSAN_ORDER_SUPPLIER_LIST = "/lingsan/order/supplier/list";
    public static final String LINGSAN_ORDER_SUPPLIER_LIST_APP = "/lingsan/order/supplier/listForApp";
    public static final String LINGSAN_ORDER_SUPPLIER_LIST_EXPORT = "/lingsan/order/supplier/listExport";
    public static final String LINGSAN_ORDER_SUPPLIER_DISPATCH_CONFIRM_QUERY = "/lingsan/order/supplier/dispatchConfirmQuery";
    public static final String LINGSAN_ORDER_SUPPLIER_DISPATCH_CONFIRM_SUBMIT = "/lingsan/order/supplier/dispatchConfirmSubmit";
    public static final String LINGSAN_ORDER_SUPPLIER_DETAIL = "/lingsan/order/supplier/detail";
    public static final String LINGSAN_ORDER_SUPPLIER_START_DELIVERY = "/lingsan/order/supplier/startDelivery";
    public static final String LINGSAN_ORDER_SUPPLIER_RETURN_DISPATCH = "/lingsan/order/supplier/returnDispatch";
    public static final String LINGSAN_ORDER_SUPPLIER_RETURN_INIT = "/lingsan/order/supplier/return/init";
    public static final String LINGSAN_ORDER_SUPPLIER_RETURN_CANCEL_QUERY = "/lingsan/order/supplier/returnCancelQuery";
    public static final String LINGSAN_ORDER_SUPPLIER_DELIVERY_INFORMATION = "/lingsan/order/supplier/deliveryInformation";
    public static final String LINGSAN_ORDER_SUPPLIER_TO_DO_ITEM = "/lingsan/order/supplier/toDoItem";
    //运营端
    public static final String LINGSAN_PLATFORM_ORDER_SUPPLIER_LIST = "/lingsan/order/supplier/platformList";
    public static final String LINGSAN_PLATFORM_ORDER_SUPPLIER_LIST_EXPORT = "/lingsan/order/supplier/platformListExport";
    public static final String LINGSAN_PLATFORM_ORDER_SUPPLIER_DISPATCH_CONFIRM_QUERY = "/lingsan/order/supplier/platformDispatchConfirmQuery";
    public static final String LINGSAN_PLATFORM_ORDER_SUPPLIER_DISPATCH_CONFIRM_SUBMIT = "/lingsan/order/supplier/platformDispatchConfirmSubmit";
    public static final String LINGSAN_PLATFORM_ORDER_SUPPLIER_DETAIL = "/lingsan/order/supplier/platformDetail";
    public static final String LINGSAN_PLATFORM_ORDER_SUPPLIER_START_DELIVERY = "/lingsan/order/supplier/platformStartDelivery";
    public static final String LINGSAN_PLATFORM_ORDER_SUPPLIER_RETURN_DISPATCH = "/lingsan/order/supplier/platformReturnDispatch";
    public static final String LINGSAN_PLATFORM_ORDER_SUPPLIER_RETURN_CANCEL_QUERY = "/lingsan/order/supplier/platformReturnCancelQuery";
    public static final String LINGSAN_PLATFORM_ORDER_SUPPLIER_DELIVERY_INFORMATION = "/lingsan/order/supplier/platformDeliveryInformation";
    public static final String LINGSAN_PLATFORM_ORDER_SUPPLIER_TO_DO_ITEM = "/lingsan/order/supplier/platformToDoItem";
    //APP
    public static final String V1_LINGSAN_ORDER_SUPPLIER_LIST = "/v1/lingsan/order/supplier/list";
    public static final String V1_LINGSAN_ORDER_SUPPLIER_DISPATCH_CONFIRM_QUERY = "/v1/lingsan/order/supplier/dispatchConfirmQuery";
    public static final String V1_LINGSAN_ORDER_SUPPLIER_DISPATCH_CONFIRM_SUBMIT = "/v1/lingsan/order/supplier/dispatchConfirmSubmit";
    public static final String V1_LINGSAN_ORDER_SUPPLIER_DETAIL = "/v1/lingsan/order/supplier/detail";
    public static final String V1_LINGSAN_ORDER_SUPPLIER_START_DELIVERY = "/v1/lingsan/order/supplier/startDelivery";
    public static final String V1_LINGSAN_ORDER_SUPPLIER_RETURN_DISPATCH = "/v1/lingsan/order/supplier/returnDispatch";
    public static final String V1_LINGSAN_ORDER_SUPPLIER_RETURN_CANCEL_QUERY = "/v1/lingsan/order/supplier/returnCancelQuery";
    public static final String V1_LINGSAN_ORDER_SUPPLIER_DELIVERY_INFORMATION = "/v1/lingsan/order/supplier/deliveryInformation";

    /**
     * 零散用车-换车相关接口地址
     */
    public static final String LINGSAN_VEHICLE_CHANGE_ORDER_CREATE_INIT = "/lingsan/vehicleChangeOrder/create/init";
    public static final String LINGSAN_VEHICLE_CHANGE_ORDER_CREATE_WEB_INIT = "/lingsan/vehicleChangeOrder/create/web/init";
    public static final String LINGSAN_VEHICLE_CHANGE_ORDER_SAVE = "/lingsan/vehicleChangeOrder/save";
    public static final String LINGSAN_VEHICLE_CHANGE_ORDER_CANCE = "/lingsan/vehicleChangeOrder/cancel";
    public static final String LINGSAN_VEHICLE_CHANGE_ORDER_STATUS_LIST = "/lingsan/vehicleChangeOrder/status/list";
    public static final String LINGSAN_VEHICLE_CHANGE_ORDER_PAGE_LIST = "/lingsan/vehicleChangeOrder/page/list";
    public static final String LINGSAN_VEHICLE_CHANGE_ORDER_APP_PAGE_LIST = "/lingsan/vehicleChangeOrder/app/page/list";
    public static final String LINGSAN_VEHICLE_CHANGE_ORDER_LIST = "/lingsan/vehicleChangeOrder/list";
    public static final String LINGSAN_VEHICLE_CHANGE_ORDER_DETAIL = "/lingsan/vehicleChangeOrder/detail";
    public static final String LINGSAN_VEHICLE_CHANGE_ORDER_WEB_DETAIL = "/lingsan/vehicleChangeOrder/web/detail";
    public static final String LINGSAN_VEHICLE_CHANGE_ORDER_USAGERECORD_LIST = "/lingsan/vehicleChangeOrder/usageRecord/list";

    /**
     * 零散用车-平台换车相关接口地址
     */

    public static final String LINGSAN_PLATFORM_VEHICLE_CHANGE_ORDER_STATUS_LIST = "/lingsan/platformVehicleChangeOrder/status/list";
    public static final String LINGSAN_PLATFORM_VEHICLE_CHANGE_ORDER_PAGE_LIST = "/lingsan/platformVehicleChangeOrder/page/list";
    public static final String LINGSAN_PLATFORM_VEHICLE_CHANGE_ORDER_LIST = "/lingsan/platformVehicleChangeOrder/list";
    public static final String LINGSAN_PLATFORM_VEHICLE_CHANGE_ORDER_DETAIL = "/lingsan/platformVehicleChangeOrder/detail";
    public static final String LINGSAN_PLATFORM_VEHICLE_CHANGE_ORDER_USAGERECORD_LIST = "/lingsan/platformVehicleChangeOrder/usageRecord/list";
    ;


    /**
     * 零散用车-审核记录相关接口地址
     */

    public static final String LINGSAN_AUDIT_RECORD_SAVE = "/lingsan/auditRecord/save";
    public static final String LINGSAN_AUDIT_RECORD_DETAIL = "/lingsan/auditRecord/detail";
    public static final String LINGSAN_AUDIT_RECORD_APP_PAGE_LIST = "/lingsan/auditRecord/app/page/list";
    public static final String LINGSAN_AUDIT_RECORD_DISPATCH_APP_PAGE_LIST = "/lingsan/auditRecord/dispatch/app/page/list";
    public static final String LINGSAN_AUDIT_RECORD_BEFOREUSE_APP_PAGE_LIST = "/lingsan/auditRecord/beforeUse/app/page/list";
    public static final String LINGSAN_AUDIT_RECORD_RETURN_APP_PAGE_LIST = "/lingsan/auditRecord/return/app/page/list";

    /**
     * 零散用车-平台审核记录相关接口地址
     */
    public static final String LINGSAN_PLATFORM_AUDIT_RECORD_AUDIT_TYPEANDSTATUS_LIST = "/lingsan/platformAuditRecord/auditTypeAndStatus/list";
    public static final String LINGSAN_PLATFORM_AUDIT_RECORD_SAVE = "/lingsan/platformAuditRecord/save";
    public static final String LINGSAN_PLATFORM_AUDIT_RECORD_DETAIL = "/lingsan/platformAuditRecord/detail";
    public static final String LINGSAN_PLATFORM_AUDIT_RECORD_PAGE_LIST = "/lingsan/platformAuditRecord/page/list";
    public static final String LINGSAN_PLATFORM_AUDIT_RECORD_AUDIT_RESULT_SAVE = "/lingsan/platformAuditRecord/auditResult/save";
    public static final String LINGSAN_PLATFORM_AUDIT_RECORD_DATA_RETURN_LIST = "/lingsan/platformAuditRecord/auditResult/dataReturn/list";
    /**
     * 零散用车-还车相关接口地址
     */
    public static final String LINGSAN_RETURN_VEHICLE_RECORD_PAGE_INIT = "/lingsan/returnVehicleRecord/page/init";
    public static final String LINGSAN_PLATFORM_RETURN_VEHICLE_RECORD_PAGE_INIT = "/lingsan/platformReturnVehicleRecord/page/init";
    public static final String LINGSAN_RETURN_VEHICLE_RECORD_RETURN_DATERANGE = "/lingsan/returnVehicleRecord/return/dateRange";
    public static final String LINGSAN_RETURN_VEHICLE_RECORD_RETURN_SAVE = "/lingsan/returnVehicleRecord/return/save";
    public static final String LINGSAN_RETURN_VEHICLE_RECORD_DETAIL = "/lingsan/returnVehicleRecord/detail";

    @Deprecated
    public static final String LINGSAN_SAVE_ORDER_NIO = "/lingsan/save/order/nio";
    //蔚来取消订单接口（蔚来调用）
    public static final String NIO_DEMAND_ORDER_CANCEL = "/lingsan/demandOrder/nio/cancel";

    //创建订单接口（蔚来会调用）
    public static final String DEMAND_ORDER_CREATE_NIO = "/lingsan/demandOrder/create/nio";

    //更新订单接口（蔚来会调用）
    public static final String DEMAND_ORDER_UPDATE_NIO = "/lingsan/demandOrder/update/nio";

    /**********代步车业务相关***********/
    /** 代步车-订单相关接口**/
    //TODO

    /**
     * 代步车-财务账单相关接口地址
     **/
    public static final String CO_ORDER_BILL_PAGE_LIST = "/commuting/orderBill/pageList";
    public static final String CO_ORDER_BILL_EXHAUSTIVE_LIST = "/commuting/orderBill/exhaustiveList";
    public static final String CO_ORDER_BILL_DETAIL = "/commuting/orderBill/detail";
    public static final String CO_ORDER_BILL_ADJUST_SAVE = "/commuting/orderBill/adjustSave";

    public static final String CO_SINGLE_BILL_PAGE_LIST = "/commuting/singleBill/pageList";

    public static final String CO_COMPANY_BILL_PAGE_LIST = "/commuting/companyBill/pageList";
    public static final String CO_COMPANY_BILL_DETAIL = "/commuting/companyBill/detail";
    public static final String CO_COMPANY_BILL_CONFIRM = "/commuting/companyBill/confirm";
    public static final String CO_COMPANY_BILL_PUSH = "/commuting/companyBill/push";
    public static final String CO_ORDER_BILL_PUSH = "/commuting/orderBill/push";
    public static final String CO_ORDER_BILL_GENERATE_ORDER_BILL = "/commuting/orderBill/generateOrderBill";

    public static final String CO_ORDER_BILL_EXPORT = "/commuting/orderBill/export";
    public static final String CO_SINGLE_BILL_EXPORT = "/commuting/singleBill/export";
    public static final String CO_COMPANY_BILL_EXPORT = "/commuting/companyBill/export";
    public static final String THIRD_APPLY_RECORD_LIST = "/third/apply/record/list";
    public static final String THIRD_APPLY_RECORD_HANDLE = "/third/apply/record/handle";
    public static final String THIRD_APPLY_RECORD_REAPPLY = "/third/apply/record/reApply";

    /**********代步车业务相关***********/


    /**********运营端行程相关***********/
    /**
     * 行程列表查询（运营端）
     **/
    public static final String ORDER_APPLY_PROVIDER_LIST = "/orderApplyProvider/getList";
    public static final String ORDER_APPLY_EXPORT_PROVIDER_LIST = "/provider/OrderApply/exportOrderApply";
    public static final String PROVIDER_ORDER_APPLY_EXPORT_TO_MAIL = "/provider/OrderApply/exportOrderApplyToMail";
    /**
     * 行程订单列表查询（客户端）
     **/
    public static final String EXPORT_CLIENT_ORDER_APPLY = "/client/orderApply/exportOrderApply";
    public static final String EXPORT_CLIENT_ORDER_APPLY_TO_MAIL = "/client/orderApply/exportOrderApplyToMail";
    /**
     * 创建行程（运营端）
     **/
    public static final String ORDER_APPLY_PROVIDER_CREATE = "/orderApplyProvider/makeOrder";
    /**
     * 子行程列表查询（运营端）
     **/
    public static final String ORDER_INFO_PROVIDER_LIST = "/orderProvider/getList";

    /**
     * 商务车调度查询（运营端）
     **/
    public static final String SCHEDULE_CENTER_PROVIDER_LIST = "/scheduleCenterProvider/getList";

    /**
     * 商务车调度详情（运营端）
     */
    public static final String BUSINESS_SCHEDULE_DETAIL = "/provider/business/schedule/detail";

    /**
     * 商务车调度车辆列表
     */
    public static final String BUSINESS_SCHEDULE_VEHICLE_LIST = "/provider/business/schedule/vehicleList";

    /**
     * 商务车调度司机列表
     */
    public static final String BUSINESS_SCHEDULE_DRIVER_LIST = "/provider/business/schedule/driverList";
    /**
     * 商务车立即调度
     */
    public static final String BUSINESS_ORDER_DISPATCH = "/provider/business/schedule/doDispatch";


    /**
     * 内部用车立即调度
     * 客户端接口：运营端暂时没有办法为客户进行内部用车订单调度
     */
    public static final String INTERNAL_ORDER_DISPATCH = "/internal/schedule/doDispatchForInternal";


    public static final String BUSINESS_ORDER_ADDITION_RECORD_SAVE = "/business/order/addition/record/save";
    public static final String BUSINESS_ORDER_ADDITION_RECORD_UPDATE = "/business/order/addition/record/update";

    public static final String BUSINESS_ORDER_ADDITION_RECORD_PAGE_LIST = "/business/order/addition/record/pageList";
    public static final String BUSINESS_ORDER_ADDITION_RECORD_DETAIL = "/business/order/addition/record/detail";
    public static final String BUSINESS_ORDER_ADDITION_RECORD_ABOLISH = "/business/order/addition/record/abolish";
    public static final String BUSINESS_ORDER_ADDITION_RECORD_SUBMIT_TO_LEASE = "/business/order/addition/record/submitToLease";

    public static final String BUSINESS_ORDER_ADDITION_RECORD_VEHICLE_LIST = "/business/order/addition/record/vehicleList";

    public static final String BUSINESS_ORDER_ADDITION_RECORD_DRIVER_LIST = "/business/order/addition/record/driverList";
    public static final String BUSINESS_ORDER_ADDITION_RECORD_DRIVER_EXPORT = "/business/order/addition/record/export";

    /** 内部用车->调度中心->分配供应商 **/
    public static final String INTERNAL_ORDER_ASSIGN_SUPPLIER="/internal/schedule/assignSupplier";
    /** 内部用车->调度中心->退回 **/
    public static final String INTERNAL_ORDER_RETURN="/internal/schedule/dispatchReturn";
    /** 内部用车->调度中心->清空调度 **/
    public static final String INTERNAL_ORDER_CLEAN_SCHEDULE="/internal/schedule/cleanSchedule";
    /** 内部用车->调度中心->供应商调度 **/
    public static final String LIST_SCHEDULE_SUPPLIER_PAGE="/scheduleCenter/supplierDispatcher";
    /**********运营端里程纠偏及问题反馈相关***********/
    public static final String PROVIDER_ORDER_QUESTION_GET_PAGE_LIST = "/provider/order/question/record/getPageList";
    public static final String PROVIDER_ORDER_QUESTION_EXPORT = "/provider/order/question/record/export";
    public static final String PROVIDER_ORDER_QUESTION_DEAL = "/provider/order/question/record/deal";
    public static final String PROVIDER_ORDER_QUESTION_GET_DETAIL = "/provider/order/question/record/getDetail";
    public static final String PROVIDER_ORDER_QUESTION_SUBMIT = "/order/question/record/submit";
    public static final String PROVIDER_ORDER_QUESTION_GET_PHOTO = "/order/question/record/getPhotos";

    public static final String PROVIDER_ORDER_MILEAGE_GET_PAGE_LIST = "/provider/order/mileage/record/getPageList";
    public static final String PROVIDER_ORDER_MILEAGE_EXPORT = "/provider/order/mileage/record/export";
    public static final String PROVIDER_ORDER_MILEAGE_DEAL = "/provider/order/mileage/record/deal";

    /**
     * 保存纠偏接口 -客户端和运营端公用，所以废弃这个接口路径
     */
    @Deprecated
    public static final String PROVIDER_SAVE_ORDER_CORRECT = "/provider/save/order/correct";

    /**
     * 查询纠偏行程详情接口-客户端和运营端公用，所以废弃这个接口路径
     */
    @Deprecated
    public static final String PROVIDER_GET_ORDER_CORRECT = "/provider/get/order/correct";


    /**
     * 保存纠偏接口
     */
    public static final String SAVE_ORDER_CORRECT = "/save/order/correct";

    /**
     * 查询纠偏行程详情接口
     */
    public static final String GET_ORDER_CORRECT = "/get/order/correct";


    /**
     * 查询自助取还行程详情接口
     */
    public static final String GET_SELF_RENT_ORDER_DETAIL = "/get/self/rent/order/detail";

    /**
     * 自助取还-开始行程接口
     */
    public static final String SELF_RENT_ORDER_START = "/self/rent/order/start";
    /**
     * 自助取还-开始行程接口
     */
    public static final String SELF_RENT_ORDER_END = "/self/rent/order/end";

    /**
     * 自助取还问题上报接口
     */
    public static final String ORDER_QUESTION_SET_SUBMIT = "/order/question/set/submit";

    /**
     * 子行程取消
     */
    public static final String ORDER_INFO_CANCEL = "/order/orderCancel";

    /**
     * 主行程取消
     */
    public static final String ORDER_APPLY_CANCEL = "/order/orderApplyCancel";

    /**
     * 商务车订单申请取消
     */
    public static final String MOTORCADE_ORDER_APPLY_CANCEL = "/order/motorcadeOrderApplyCancel";
    /**
     * 查询mrcar订单申请详情微服务地址
     */
    public static final String QUERY_BY_ORDER_APPLY_NO = "/orderApply/queryByOrderApplyNo";

    /**
     * 查询主行程列表
     */
    public static final String QUERY_ORDER_MANAGE_PAGE_LIST = "/orderApply/queryOrderPageList";
    /**
     * 导出订单列表
     **/
    public static final String EXPORT_TAKE_ORDER_LIST = "/order/queryOrderNoList";
    /**
     * 查询子行程列表
     **/
    public static final String ORDER_INFO_LIST = "/order/queryOrderInfoList";

    /**
     * 我的行程
     */
    public static final String QUERY_MY_ORDER_LIST = "/order/queryMyOrderList";

    /**
     * 我的行程详情
     */
    public static final String QUERY_MY_ORDER_DETAIL = "/order/queryMyOrderDetail";

    /**
     * 工作台-查询子行程列表-商务车需求使用 后续有可能迁移
     **/
    public static final String ORDER_INFO_LIST_FOR_APP = "/order/orderInfoListForApp";


    /**
     * 运营端获取子行程需要导出的数据列表
     */
    public static final String GET_EXPORT_ORDER_INFO_LIST = "/orderProvider/getExportOrderInfoList";
    /**
     * 商务车-运营端获取子行程需要导出的数据列表
     */
    public static final String GET_EXPORT_BUSINESS_ORDER_INFO_LIST = "/orderProvider/getExportBusinessOrderInfoList";

    /**
     * 客户端获取子行程需要导出的数据列表
     */
    public static final String GET_EXPORT_ORDER_INFO_LIST_CLIENT = "/order/getExportOrderInfoList";

    /**
     * 商务车订单评价列表
     ***/
    public static final String ORDER_EVALUATION_LIST_INFO = "/order/evalueation/getList";
    /**
     * 商务车订单评价详情
     ***/
    public static final String ORDER_EVALUATION_DETAIL = "/order/evalueation/getDetail";
    /**
     * 商务车订单评价保存
     ***/
    public static final String ORDER_EVALUATION_SAVE = "/order/evalueation/save";
    /**
     * 商务车订单评价导出
     ***/
    public static final String ORDER_EVALUATION_EXPORT = "/order/evalueation/export";
    /**
     * @description: 取消行程
     */
    public static final String PROVIDER_APPLY_ORDER_CANCEL = "/provider/apply/order/cancel";
    /**
     * 商务车下单预估金额
     */
    public static final String PROVIDER_BUSINESS_ESTIMATE_AMOUNT = "/provider/business/estimate/amount";

    /**
     * 权益卡校验
     */
    public static final String BENEFIT_CARD_CHECK = "/provider/benefit/card/check";
    /**
     * 商务车下单快捷词
     */
    public static final String BUSINESS_ORDER_FAST_REMARK = "/business/order/fast/remark";
    /**
     * 商务车调价
     */
    public static final String BUSINESS_ORDER_ADJUST = "/business/order/bill/adjust";


    /**
     * 获取商务车调价页面详情
     */
    public static final String BUSINESS_GET_ORDER_ADJUST_INFO = "/business/order/bill/adjust/info";
    /**
     * 查询调价记录
     */
    public static final String QUERY_ADJUST_PRICE_LOG = "/query/adjust/price/log";

    public static final String QUERY_ORDER_PRICE_CONFIG = "/query/order/price/config";

    /**
     * ------------------- 分时用车 -------------------
     **/
    public static final String TEMPORAL_SHARED_VEHICLE_RECORD_PAGE = "/temporalSharedVehicleRecord/page";
    public static final String TEMPORAL_SHARED_VEHICLE_RECORD_DETAIL = "/temporalSharedVehicleRecord/detail";
    public static final String TEMPORAL_SHARED_VEHICLE_RECORD_INVALID = "/temporalSharedVehicleRecord/invalid";
    public static final String TEMPORAL_SHARED_VEHICLE_RECORD_QUERY_ORDER = "/temporalSharedVehicleRecord/queryOrder";
    public static final String TEMPORAL_SHARED_VEHICLE_RECORD_ASSOCIATION_ORDER = "/temporalSharedVehicleRecord/associationOrder";
    public static final String TEMPORAL_SHARED_VEHICLE_RECORD_EXPORT = "/temporalSharedVehicleRecord/export";

    /**
     * 分时用车单日统计列表
     */
    public static final String TIME_REPORT_DAY_STATISTICS_LIST = "/share/time/day/getList";
    /**
     * 分时用车单日统计导出
     */
    public static final String TIME_REPORT_DAY_STATISTICS_EXPORT = "/share/time/day/statistics/export";

    /**
     * 分时用车月度统计列表
     */
    public static final String TIME_REPORT_MONTH_STATISTICS_LIST = "/share/time/month/getList";
    /**
     * 分时用车日统计列表更新数据
     */
    public static final String TIME_SHARE_STATISTICS_REFRESH = "/time/share/statistics/refresh";
    public static final String TIME_SHARE_STATISTICS_GET_NOW_TIME = "/time/share/statistics/getNowTime";
    /**
     * 分时用车月度统计导出
     */
    public static final String TIME_REPORT_MONTH_STATISTICS_EXPORT = "/share/time/month/statistics/export";

    /**
     * 分时用车单日统计列表（运营端）
     */
    public static final String PROVIDER_TIME_REPORT_DAY_STATISTICS_LIST = "/provider/share/time/day/getList";
    /**
     * 分时用车单日统计导出（运营端）
     */
    public static final String PROVIDER_TIME_REPORT_DAY_STATISTICS_EXPORT = "/provider/share/time/day/statistics/export";

    /**
     * 分时用车月度统计列表（运营端）
     */
    public static final String PROVIDER_TIME_REPORT_MONTH_STATISTICS_LIST = "/provider/share/time/month/getList";
    /**
     * 分时用车月度统计导出（运营端）
     */
    public static final String PROVIDER_TIME_REPORT_MONTH_STATISTICS_EXPORT = "/provider/share/time/month/statistics/export";

    /**
     * 分时用车单日明细查询
     */
    public static final String TIME_REPORT_DAILY_USAGE_SEARCH = "/share/time/daily/search";

    /**
     * 分时用车单日明细导出
     */
    public static final String TIME_REPORT_DAILY_USAGE_EXPORT = "/share/time/daily/export";

    /**
     * 商务车供应商支出单列表查询接口
     */
    public static final String PROVIDER_BUSINESS_EXPENDITURE_SUPPLIER_SPEND_LIST = "/provider/business/expenditure/getSupplierSpendList";
    /**
     * 导出商务车供应商支出单报表
     */
    public static final String PROVIDER_BUSINESS_EXPENDITURE_EXPORT_SUPPLIER_SPEND_LIST = "/provider/business/expenditure/exportSupplierSpendList";
    /**
     * 导出商务车供应商支出单明细
     */
    public static final String PROVIDER_BUSINESS_EXPENDITURE_EXPORT_SUB_SUPPLIER_SPEND_LIST = "/provider/business/expenditure/exportSubSupplierSpendList";

    /**
     * 商务车供应商支出单操作日志查询接口
     */
    public static final String PROVIDER_BUSINESS_EXPENDITURE_GET_OPERATION_LIST = "/provider/business/expenditure/getOperationList";

    /**
     * 商务车供应商支出单-新增支出单
     */
    public static final String PROVIDER_BUSINESS_EXPENDITURE_ADD_SUPPLIER_SPEND = "/provider/business/expenditure/addSupplierSpend";
    /**
     * 商务车供应商支出单-编辑支出单
     */
    public static final String PROVIDER_BUSINESS_EXPENDITURE_UPDATE_SUPPLIER_SPEND = "/provider/business/expenditure/updateSupplierSpend";

    /**
     * 商务车供应商支出单-支出单详情页
     */
    public static final String PROVIDER_BUSINESS_EXPENDITURE_GET_SUPPLIER_SPEND_DETAIL = "/provider/business/expenditure/getSupplierSpendDetail";

    /**
     * 商务车供应商支出单-子行程列表
     */
    public static final String PROVIDER_BUSINESS_EXPENDITURE_GET_SUB_TRIP_DETAIL_LIST = "/provider/business/expenditure/getSubTripDetailList";

    /**
     * 商务车供应商支出单-子行程列表导出
     */
    public static final String PROVIDER_BUSINESS_EXPENDITURE_SUB_TRIP_DETAIL_LIST_EXPORT = "/provider/business/expenditure/exportSubTripDetailList";
    /**
     * 商务车供应商支出单-添加子行程-子行程列表
     */
    public static final String PROVIDER_BUSINESS_EXPENDITURE_GET_SUB_TRIP_DETAIL_LIST_FOR_ADD = "/provider/business/expenditure/getSubTripDetailListForAdd";
    /**
     * 商务车供应商支出单-提交&废除&审批驳回&审批通过
     */
    public static final String PROVIDER_BUSINESS_EXPENDITURE_UPDATE_STATUS = "/provider/business/expenditure/updateExpenditureStatus";

    /**
     * 调度详情接口
     */
    public static final String GET_SCHEDULE_DETAIL_V2 = "/scheduleCenter/getScheduleDetailV2";

    /**
     * 三方服务是否支持使用权益卡
     */
    public static final String CHECK_BENEFIT_CARD_THIRD_SUPPORT = "/benefitCard/checkBenefitCardThirdSupport";
    /**
     * 调价前获取账单金额
     */
    public static final String GET_BILL_INFO_BEFORE_SETTLEMENT = "/provider/consumeBill/getInfoBeforeSettlement";

    /**
     * 添加途经点-司机接单
     **/
    public static final String ORDER_WAYPOINT_SAVE = "/order/waypoint/save";
    /**
     * 商务车调度列表
     **/
    public static final String BUSINESS_ORDER_LIST = "/scheduleQuery/queryBusinessScheduleList";
    /**
     * 商务车调度详情
     **/
    public static final String BUSINESS_ORDER_DETAIL = "/scheduleQuery/getBusinessScheduleDetail";

    /**
     * 商务车主行程取消  商务车调度使用
     **/
    public static final String BUSINESS_ORDER_APPLY_CANCEL = "/businessOrder/orderApplyCancel";
    /**
     * 商务车子行程取消 商务车调度使用
     **/
    public static final String BUSINESS_ORDER_INFO_CANCEL = "/businessOrder/orderInfoCancel";

    public static final String GET_BUSINESS_PRICE_STRATEGY = "/provider/order/business/getBusinessPriceStrategy";
    /**
     * 校验车辆或者司机有没有预计用车时间交叉的子行程
     */
    public static final String CHECK_DRIVER_VEHICLE_TIME_CROSS = "/provider/order/business/checkDriverVehicleTimeCross";

    public static final String GET_CAR_USAGE_DETAILS = "/provider/order/business/getCardUsageDetails";

    /**
     * 查看订单抵扣明细
     */
    public static final String QUERY_ORDER_DISCOUNT = "/query/order/discount/details";

    public static final String DRIVER_ADD_ATTACH = "/order/bill/driverAddAttachFee";
    /**
     * 订单签字确认
     */
    public static final String ORDER_SIGN_CONFIRM = "/order/bill/orderSignConfirm";

    /**
     * 查询订单费用
     */
    public static final String QUERY_ORDER_BILL_FEE = "/order/bill/queryOrderBillFee";

    /**
     * 代步车相关接口地址
     */
    public static final String DAIBUCHE_ORDER_LIST = "/daibuche/order/list";
    public static final String DAIBUCHE_ORDER_SAVE_OR_UPDATE = "/daibuche/order/saveOrUpdate";
    public static final String DAIBUCHE_ORDER_DETAIL = "/daibuche/order/detail";
    public static final String DAIBUCHE_ORDER_LIST_EXPORT = "/daibuche/order/export";
    public static final String DAIBUCHE_ORDER_LIST_DISPATCH = "/daibuche/dispatch";

    public static final String DAIBUCHE_ORDER_COUNT = "/daibuche/order/count";
    public static final String DAIBUCHE_ORDER_DELIVERY = "/daibuche/order/delivery";
    public static final String DAIBUCHE_ORDER_ESTIMATE_FEE = "/daibuche/order/estimateFee";
    public static final String DAIBUCHE_ORDER_RETURN = "/daibuche/order/return";
    public static final String DAIBUCHE_ORDER_RECORDING = "/daibuche/order/record";
    public static final String DAIBUCHE_ORDER_CANCEL = "/daibuche/order/cancel";
    public static final String DAIBUCHE_ORDER_CAL_ESTIMATE_USE_CYCLE = "/daibuche/order/calEstimateUseCycle";
    public static final String DAIBUCHE_ORDER_IMPORT = "/daibuche/order/import";
    public static final String DAIBUCHE_ORDER_DEPOSIT_FREE = "/daibuche/order/deposit/free";
    public static final String DAIBUCHE_ORDER_DEPOSIT_DEDUCT = "/daibuche/order/deposit/deduct";
    public static final String DAIBUCHE_ORDER_DEPOSIT_UNFREEZE = "/daibuche/order/deposit/unfreeze";
    public static final String DAIBUCHE_ORDER_DEPOSIT_PREAUTH = "/daibuche/order/deposit/preauth";
    public static final String DAIBUCHE_CALLBACK_TRADE_NOTIFY = "/daibuche/callback/trade/v1/notify";

    public static final String DAIBUCHE_ORDER_TRADE_RECORD_LIST = "/daibuche/order/trade/record/queryListByPage";
    public static final String DAIBUCHE_ORDER_TRADE_RECORD_SAVE = "/daibuche/order/trade/record/save";
    public static final String DAIBUCHE_ORDER_TRADE_RECORD_UPDATE = "/daibuche/order/trade/record/update";
    public static final String DAIBUCHE_ORDER_TRADE_RECORD_EXPORT = "/daibuche/order/trade/record/export";
    public static final String DAIBUCHE_ORDER_TRADE_RECORD_GET_TRADE_TYPE = "/daibuche/order/trade/record/getTradeType";
    public static final String DAIBUCHE_ORDER_TRADE_RECORD_GET_TRADE_CHANNEL = "/daibuche/order/trade/record/getTradeChannel";


    //获取优惠明细-包含权益卡和优惠券

    /**
     * 派车单pdf导出
     **/
    public static final String EXPENSE_ORDER_EXPORT = "/expense/order/export";
    /**
     * 查询派车单信息
     **/
    public static final String EXPENSE_ORDER_INFO = "/expense/order/info";
    //=====  聚合充电订单=====

    /**
     * 检查企业限额 (app)
     */
    public static final String CHARGE_ORDER_CHECK_COMPANY_LIMIT = "/charge/order/checkCompanyLimit";

    /**
     * 检验车辆是否可以充电 (app)
     */
    public static final String CHARGE_ORDER_CHECK_CAN_CHARGED = "/charge/order/checkCanBeCharged";

    /**
     * 创建充电订单 启动充电 (app)
     */
    public static final String CHARGE_ORDER_CREATE = "/charge/order/save";

    /**
     * 手动完成订单(停止充电) (app)
     */
    public static final String CHARGE_ORDER_FINISH_BY_ID = "/charge/order/finishById";
    /**
     * 查询进行中的订单  (app)
     */
    public static final String CHARGE_ORDER_QUERY_ONGOING_BY_ID = "/charge/order/queryOngoingById";
    /**
     * 查询已完成的订单详情 (app)
     */
    public static final String CHARGE_ORDER_QUERY_FINISHED_BY_ID = "/charge/order/queryFinishedById";
    /**
     * 查询该用户下的订单分页 (app)
     */
    public static final String CHARGE_ORDER_QUERY_BY_USER_ID = "/charge/order/queryByUserId";
    /**
     * 充电完成推送账账单回调
     */
    public static final String CHARGE_FINISHED_CALL_BACK = "/charge/order/chargeFinishedCallBack";

    /**
     * 充电状态回调
     */
    public static final String CHARGE_STATUS_CALL_BACK = "/charge/order/chargeStatusCallBack";

    /**
     * 充电中 回调
     */
    public static final String CHARGE_RUNNING_CALL_BACK = "/charge/order/chargeRunningCallBack";

    /**
     * 充电中 回调V2
     */
    public static final String CHARGE_RUNNING_CALL_BACK_V2 = "/charge/order/chargeRunningCallBack/V2";

    /**
     * 停止充电回调
     */
    public static final String CHARGE_STOPPED_CALL_BACK = "/charge/order/chargeStoppedCallBack";


    /**
     * 修改充电订单中的 充电量限额 (app)
     */
    public static final String CHARGE_ORDER_UPDATE_POWER_LIMIT_BY_ID = "/charge/order/updatePowerLimitById";

    public static final String CHARGE_ORDER_QUERY_BY_PAGE = "/charge/order/queryByPage";
    public static final String CHARGE_ORDER_DETAIL = "/charge/order/detail";
    public static final String CHARGE_ORDER_LIST_FOR_CAR_TIME_LINE = "/charge/order/listForCarTimeLine";


    // ---------------------云南公务用车个人短租-begin---------------------------------
    // app端订单相关接口
    /**
     * 客户端-公务用车订单分页查询
     */
    public static final String GOV_CAR_ORDER_PAGE_FOR_APP = "/govCar/order/pageForApp";
    /**
     * 客户端-公务用车订单详情
     */
    public static final String GOV_CAR_ORDER_DETAIL_FOR_APP = "/govCar/order/detailForApp";
    /**
     * 客户端-公务用车订单取消
     */
    public static final String GOV_CAR_ORDER_CANCEL_FOR_APP = "/govCar/order/cancelForApp";
    /**
     * 客户端-校验用户身份证和驾驶证是否已认证
     */
    public static final String GOV_CAR_ORDER_CHECK_USER_IDENTITY = "/govCar/order/checkUserIdentity";
    /**
     * 客户端-确认下单前置校验
     */
    public static final String GOV_CAR_ORDER_VERIFY_PRE = "/govCar/order/verifyPre";
    /**
     * 客户端-验证订单规则并准备确认下单页面的数据
     */
    public static final String GOV_CAR_ORDER_VERIFY_AND_PREPARE_ORDER_DATA = "/govCar/order/verifyAndPrepareData";
    /**
     * 客户端-获取下单快照信息
     */
    public static final String GOV_CAR_ORDER_GET_ORDER_SNAPSHOT_INFO = "/govCar/order/getOrderSnapshotInfo";
    /**
     * 客户端-修改公务用车订单的租车时间
     */
    public static final String GOV_CAR_ORDER_UPDATE_RENTAL_TIME = "/govCar/order/updateRentalTime";
    /**
     * 客户端-确认并提交订单
     */
    public static final String GOV_CAR_ORDER_CONFIRM_AND_SUBMIT_ORDER = "/govCar/order/confirmAndSubmit";
    /**
     * 客户端-公务用车附加费订单分页查询
     */
    public static final String GOV_CAR_ORDER_ADDITIONAL_FEE_PAGE_FOR_APP = "/govCar/orderAdditionalFee/pageForApp";
    /**
     * 客户端-公务用车附加费订单详情
     */
    public static final String GOV_CAR_ORDER_ADDITIONAL_FEE_DETAIL_FOR_APP = "/govCar/orderAdditionalFee/detailForApp";

    // 支付相关接口
    /**
     * 客户端-获取公务用车订单支付信息
     */
    public static final String GET_GOV_CAR_ORDER_PAYMENT_INFO = "/govCar/order/paymentInfo";
    /**
     * 客户端-公务用车订单支付回调
     */
    public static final String GOV_CAR_ORDER_PAYMENT_CALLBACK = "/govCar/order/paymentCallback";
    /**
     * 客户端-获取公务用车订单支付状态
     */
    public static final String GET_GOV_CAR_ORDER_PAYMENT_STATUS = "/govCar/order/paymentStatus";
    /**
     * 客户端-订单结算
     */
    public static final String GET_GOV_CAR_ORDER_SETTLEMENT = "/govCar/order/orderSettlement";
    /**
     * 客户端-订单支付交易流水号更新
     */
    public static final String GET_GOV_CAR_ORDER_PAYMENT_TRANS_NO_UPDATE = "/govCar/order/paymentTransNoUpdate";

    // 订单相关接口
    public static final String GOV_CAR_ORDER_PAGE = "/govcar/orderInfo/queryByPage";
    public static final String GOV_CAR_ORDER_CANCEL = "/govcar/orderInfo/cancelOrder";
    public static final String GOV_CAR_ORDER_FORCE_RETURN = "/govcar/orderInfo/forceReturn";
    public static final String GOV_CAR_ORDER_ADJUSTMENT = "/govcar/orderInfo/adjustment";
    public static final String GOV_CAR_ORDER_ADJUSTMENT_CONFIG = "/govcar/orderInfo/adjustmentConfig";
    public static final String GOV_CAR_ORDER_DETAIL = "/govcar/orderInfo/detail";
    public static final String GOV_CAR_ORDER_CANCEL_PRE_CHECK = "/govcar/orderInfo/cancelPreCheck";

    public static final String GOV_CAR_ORDER_GET_NO_FINISH_BY_VEHICLE_VIN = "/govcar/orderInfo/getNoFinishByVehicleVin";
    //订单回调更新接口
    public static final String GOV_CAR_ORDER_CALLBACK = "/govcar/order/callback";

    // 附加费订单相关接口
    public static final String GOV_CAR_ORDER_EXTEND_PAGE = "/govcar/orderExtend/queryByPage";
    public static final String GOV_CAR_ORDER_EXTEND_CANCEL = "/govcar/orderExtend/cancelOrder";
    public static final String GOV_CAR_ORDER_EXTEND_SAVE = "/govcar/orderExtend/saveOrder";
    public static final String GOV_CAR_ORDER_EXTEND_DETAIL = "/govcar/orderExtend/detail";

    // ---------------------云南公务用车个人短租-end---------------------------------

    // ---------------------云南公务用车对公-begin---------------------------------
    // app端订单相关接口
    /**
     * app-立即下单接口
     */
    public static final String PUBLIC_GOV_CAR_CREATE_ORDER_FOR_APP = "/publicGovCar/order/createOrder";

    public static final String PUBLIC_GOV_CAR_ORDER_LIST_FOR_APP = "/publicGovCar/order/list";


    public static final String PUBLIC_GOV_CAR_ORDER_DETAIL_FOR_APP = "/publicGovCar/order/detail";


    public static final String PUBLIC_GOV_CAR_ORDER_APPROVAL_ENUM_FOR_APP = "/publicGovCar/order/approval/enum";


    /**
     * pc端相关接口
     */

    public static final String PROVIDER_PUBLIC_GOV_CAR_ORDER_DETAIL = "/provider/publicGovCar/order/getDetail";

    public static final String PROVIDER_PUBLIC_GOV_CAR_ORDER_LIST = "/provider/publicGovCar/order/list";
    public static final String PROVIDER_PUBLIC_GOV_CAR_ORDER_LIST_EXPORT = "/provider/publicGovCar/order/list/export";

    public static final String PROVIDER_PUBLIC_GOV_CAR_ORDER_INFO_DETAIL = "/provider/publicGovCar/order/info/detail";

    public static final String PROVIDER_PUBLIC_GOV_CAR_EXPORT_DELIVERY_PDF = "/provider/publicGovCar/order/exportDeliveryPdf";
    public static final String PROVIDER_PUBLIC_GOV_CAR_EXPORT_DISPATCH_ORDER = "/provider/publicGovCar/order/exportDispatchOrder";
    public static final String PROVIDER_PUBLIC_GOV_CAR_EXPORT_DISPATCH_ORDER_TO_MAIL = "/provider/publicGovCar/order/exportDispatchOrderToMail";


    public static final String CLIENT_PUBLIC_GOV_CAR_ORDER_LIST = "/client/publicGovCar/order/list";
    public static final String CLIENT_PUBLIC_GOV_CAR_ORDER_LIST_EXPORT = "/client/publicGovCar/order/list/export";
    public static final String CLIENT_PUBLIC_GOV_CAR_ORDER_INFO_DETAIL = "/clinet/publicGovCar/order/info/detail";
    public static final String CLIENT_PUBLIC_GOV_CAR_EXPORT_DELIVERY_PDF = "/client/publicGovCar/order/exportDeliveryPdf";
    public static final String GET_SCHEDULE_DETAIL_BY_ID = "/schedule/getScheduleDetailById";


    // ---------------------云南公务用车对公-end---------------------------------


    // ---------------------车辆统计-start---------------------------------
    public static final String VEHICLE_STATISTICS_COUNT = "/vehicle/statisticsCount";
    public static final String VEHICLE_STATISTICS_MILEAGE_COUNT = "/vehicle/statisticsMileage";
    public static final String VEHICLE_STATISTICS_MILEAGE_UPDATE = "/vehicle/statisticsMileageUpdate";
    public static final String VEHICLE_STATISTICS_CHARGE = "/vehicle/chargeStatistics";

    // ---------------------车辆统计-end---------------------------------


    //商务包车
    public static final String BUSINESS_PACKAGE_ORDER_LIST = "/businessPackage/order/list";

    public static final String BUSINESS_PACKAGE_ORDER_DETAIL = "/businessPackage/order/detail";

    public static final String BUSINESS_PACKAGE_ORDER_CREATE = "/businessPackage/order/create";
    //导出
    public static final String BUSINESS_PACKAGE_ORDER_LIST_EXPORT = "/businessPackage/order/list/export";

    public static final String APP_BUS_ORDER_DETAIL = "/app/bus/order/detail";
    public static final String APP_BUS_ORDER_DISPATCH = "/app/bus/order/dispatch";

    public static final String APP_BUS_ORDER_LATEST_POSITION = "/app/bus/order/latest/position";
    public static final String APP_BUS_ORDER_LIST = "/app/bus/order/list";
    public static final String APP_BUS_LATEST_ORDER_CUSTOMER = "/app/bus/order/latestCustomer";
    public static final String APP_BUS_GET_DRIVER_OPERATE_BUTTON = "/app/bus/order/getDriverOperateButton";
    public static final String PROVIDER_BUS_ORDER_LIST = "/provider/bus/order/list";
    public static final String PROVIDER_BUS_ORDER_EXPORT = "/provider/bus/order/export";

    public static final String PROVIDER_BUS_ORDER_DETAIL = "/provider/bus/order/detail";
    public static final String PROVIDER_BUS_ORDER_EXPORT_PDF = "/provider/bus/order/export/pdf";
    public static final String PROVIDER_BUS_ORDER_STATUS_ENUM = "/provider/bus/order/status/enum";
    public static final String CLIENT_BUS_ORDER_STATUS_ENUM = "/client/bus/order/status/enum";
    public static final String CLIENT_BUS_ORDER_EXPORT = "/client/bus/order/export";

    public static final String CLIENT_BUS_ORDER_LIST = "/client/bus/order/list";
    public static final String CLIENT_BUS_ORDER_DETAIL = "/client/bus/order/detail";
    public static final String CLIENT_BUS_ORDER_EXPORT_PDF = "/client/bus/order/export/pdf";

    //大巴车创建订单
    public static final String BUS_ORDER_CREATE = "/bus/order/create";
    //大巴车创建补录订单
    public static final String BUS_ORDER_MAKEUP = "/bus/order/makeup";
    //大巴车修改订单
    public static final String BUS_ORDER_UPDATE = "/bus/order/update";
    //大巴车-取消订单
    public static final String BUS_ORDER_CANCEL = "/bus/order/cancel";
    //大巴车订单调度/重新调度
    public static final String BUS_ORDER_DISPATCH = "/bus/order/dispatch";
    //大巴车订单司机操作
    public static final String BUS_ORDER_DRIVER_OPERATE = "/bus/order/driver/operate";
    //大巴车订单费用录入
    public static final String BUS_ORDER_FEE_ENTER = "/bus/order/fee/enter";
    //大巴车订单调价
    public static final String BUS_ORDER_ADJUST_PRICE = "/bus/order/adjust/price";
    //大巴车订单撤回
    public static final String BUS_ORDER_WITHDRAW = "/bus/order/withdraw";
    //大巴车订单 确认结算
    public static final String BUS_ORDER_CONFIRM_SETTLEMENT = "/bus/order/confirm/settlement";

    /**
     * 轨迹染色-内部用车订单查询接口
     **/
    public static final String TRACE_ORDER_SEARCH = "/order/getTraceOrderByTime";


}
