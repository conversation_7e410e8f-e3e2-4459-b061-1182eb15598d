package com.izu.mrcar.order;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: 抵扣类型 0:没有抵扣,1:优惠券,2:权益卡
 * @date 2023/12/8 16:03
 */
public enum DiscountTypeEnum {

    DEFAULT(Byte.valueOf("0"),"无"),
    COUPON(Byte.valueOf("1"),"优惠券"),
    BENEFIT_CARD(Byte.valueOf("2"),"权益卡")
    ;
    private Byte type;

    private String desc;

    DiscountTypeEnum(Byte type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
    public static DiscountTypeEnum getDiscountType(byte type){
        return Arrays.stream(values()).filter(e-> Objects.equals(type,e.getType())).findFirst().orElse(DEFAULT);
    }
}
