package com.izu.mrcar.order.errcode;

import com.izu.framework.web.rest.response.ResultMessage;


/**
 * 订单业务 错误码
 * note:编码从80001开始 到89999 结束 创建时 注意有无重复项
 **/
@Deprecated
public class OrderErrorCode {

    // 参数验证类
    @ResultMessage("参数{0}必填")
    public static final int PARAMETER_REQUIRED=80000;

    @ResultMessage("查询用户信息失败。")
    public static final int QUERY_USER_ERROR = 80001;
    @ResultMessage("查询车型信息失败。")
    public static final int QUERY_CAR_MODEL_ERROR = 80002;
    @ResultMessage("订单申请不存在")
    public static final int QUERY_ORDER_APPLY_NOT_EXISTS = 80003;
    @ResultMessage("订单信息不存在")
    public static final int QUERY_ORDER_NOT_EXISTS = 80004;
    @ResultMessage("支付方式错误，不能下单。")
    public static final int WRONG_PAY_METHOD_TYPE = 80006;
    @ResultMessage("您选取的时间有误，请重新选取。")
    public static final int WRONG_GAINCARTIME_BACKCARTIME = 80007;
    @ResultMessage("查询价格日历失败")
    public static final int QUERY_PRICE_CALENDAR_ERROR=80009;

    // =========================================订单下单类==============================================
    @ResultMessage("创建订单申请失败")
    public static final int CREATE_ORDER_APPLY_ERROR = 80010;
    @ResultMessage("上传订单服务前后照片失败")
    public static final int CREATE_ORDER_VEHICLE_IMG_ERROR = 80011;
    @ResultMessage("上传订单费用补录凭证失败")
    public static final int CREATE_ORDER_CERTIFICATE_ERROR = 80012;
    @ResultMessage("订单取消失败")
    public static final int ORDER_CANCEL_ERROR = 80013;
    @ResultMessage("设置一口价失败")
    public static final int SET_FIXED_PRICE_FAIL = 80014;
    @ResultMessage("订单申请取消失败")
    public static final int ORDER_APPLY_CANCEL_ERROR = 80015;

    // =========================================订单行程操作类==============================================
    @ResultMessage("司机动作不合法")
    public static final int DRIVER_ACTION_ILLEGAL = 80060;
    @ResultMessage("司机操作失败")
    public static final int DRIVER_ACTION_ERROR = 80061;
    @ResultMessage("该行程订单不符合当前操作条件")
    public static final int DRIVER_ACTION_NOT_ALLOW = 80062;
    @ResultMessage("订单调度失败")
    public static final int ORDER_DIPATCH_ERROR = 80063;
    @ResultMessage("订单状态不符合调度条件")
    public static final int ORDER_DIPATCH_FAIL = 80064;
    @ResultMessage("订单调度详情信息为空")
    public static final int ORDER_DIPATCH_NOT_EXSIST = 80065;
    @ResultMessage("该行程订单非申请人不符合当前操作条件")
    public static final int DRIVER_PROPOSER_ACTION_NOT_ALLOW = 80066;
    @ResultMessage("司机事件上报失败")
    public static final int DRIVER_ACTION_SAVE_ERROR = 80067;
    @ResultMessage("您有未完成订单，请处理后再出发！")
    public static final int DRIVER_ACTION_NOT_ALLOW_START = 80068;
    @ResultMessage("您有未完成的行程{0}")
    public static final int DRIVER_ACTION_NOT_ALLOW_START_DRIVER = 80069;
    @ResultMessage("当前车辆已被行程{0}占用")
    public static final int DRIVER_ACTION_NOT_ALLOW_START_CAR = 80070;
    @ResultMessage("{0},开锁失败，请重试")
    public static final int CAR_LOCK_ON_FAIL = 80080;
    @ResultMessage("{0},关锁失败，请重试")
    public static final int CAR_LOCK_OFF_FAIL = 80081;
    @ResultMessage("订单指派详情信息为空")
    public static final int ORDER_APPOINT_NOT_EXSIST = 80090;
    // =========================================订单取消类==============================================
    @ResultMessage("当前订单不可取消")
    public static final int CANCEL_NOT_ALLOWED = 80101;
    @ResultMessage("客服类型不存在")
    public static final int CANCEL_STAFF_TYPE_NOT_EXIST = 80102;
    @ResultMessage("此订单存在未支付的账单无法取消，请稍后重试")
    public static final int CANCEL_NOT_ALLOWED_BECAUSE_EXIST_UNPAID_AMOUNT = 80103;
    @ResultMessage("仅支持取消状态订单红冲收入，当前订单状态不支持")
    public static final int ORDER_RED_REDUCE_MUST_CANCEL_STATUS = 80104;
    @ResultMessage("已申请开具发票，不可操作红冲")
    public static final int ORDER_RED_REDUCE_MUST_NOT_APPLY_INVOICE = 80105;
    @ResultMessage("红冲金额不符，不可操作红冲")
    public static final int ORDER_RED_REDUCE_MUST_EQUEAL_AMOUNT = 80106;
    @ResultMessage("订单尚未核算完成，不可操作红冲")
    public static final int ORDER_RED_REDUCE_MUST_AFTER_FINANCIAL_FINISH = 80107;
    @ResultMessage("该订单不属于当前用户")
    public static final int ORDER_NOT_BELONG_CURRENCT_USER = 80108;

    // =========================================订单审批类==============================================
    @ResultMessage("当前订单不可审批")
    public static final int APPROVAL_NOT_ALLOWED = 80201;
    @ResultMessage("审批操作不合法")
    public static final int APPROVAL_OPTION_ERROR = 80202;
    @ResultMessage("审批失败")
    public static final int APPROVAL_OPTION_FAIL = 80203;
    @ResultMessage("驳回原因不能为空")
    public static final int REJECT_REASON_NOT_NULL = 80204;

    // =========================================消费账单类==============================================
    @ResultMessage("当前消费账单不存在")
    public static final int BILL_ORDER_NOT_EXIST=80301;
    @ResultMessage("账单信息不存在")
    public static final int QUERY_BILL_NOT_EXISTS = 80302;
    @ResultMessage("账单确认短信发送失败")
    public static final int CONFIRM_MESSAGE_SEND_FAIL=80303;
    @ResultMessage("当前账单不可确认")
    public static final int CAN_NOT_CONFIRM_ORDER_BILL=80304;
    @ResultMessage("账单确认短息发送成功{0}条，失败{1}条，失败原因{2}")
    public static final int CONFIRM_FAIL_MSG=80305;
    @ResultMessage("账单确认失败")
    public static final int CONFIRM_ORDER_BILL_FAIL=80306;
    @ResultMessage("获取账单确认信息失败")
    public static final int QUERY_CONFIRM_ORDER_BILL_FAIL=80307;
    @ResultMessage("结算账单已支付")
    public static final int SETTLEMENT_BILL_PAYED=80308;
    @ResultMessage("费用明细格式错误")
    public static final int PRICEDETAILS_ERROR=80309;
    @ResultMessage("费用明细中存在重复费用编码")
    public static final int PRICEDETAILS_REPEAT_CODE=80310;
    @ResultMessage("存在重复退款账单编号")
    public static final int CONSUME_BILL_REPEAT_CODE=80311;
    @ResultMessage("填写退款金额总和与可退金额不相等")
    public static final int SETTLEMENT_REFUND_AMOUNT_ERROR=80312;
    @ResultMessage("需退款的消费账单必须为租车账单")
    public static final int SETTLEMENT_REFUND_MUST_RENTCONSUME=80313;
    @ResultMessage("需退款的消费账单可退金额不足")
    public static final int SETTLEMENT_REFUND_CONSUME_AMOUNT_LESS=80314;
    @ResultMessage("订单未完成全部收款，尚不可结算")
    public static final int SETTLEMENT_CONSUME_AMOUNT_NOT_FINISH=80315;
    @ResultMessage("减免金额不能大于总金额")
    public static final int REDUCT_AMOUNT_MAX_TOTAL_AMOUNT=80316;
    @ResultMessage("将抵扣的消费账单已收款")
    public static final int CONSUME_BILL_EXIST_PAYED = 80317;
    @ResultMessage("押金未完成全部收款，尚不可结算")
    public static final int SETTLEMENT_DEPOSIT_AMOUNT_NOT_FINISH=80318;
    @ResultMessage("消费账单已支付，无法修改金额")
    public static final int ADD_FEE_CONSUME_EXSIT_PAY=80319;
    @ResultMessage("请等待订单结算后再进行此次操作")
    public static final int SETTLEMENT_BILL_UN_END=80320;
    @ResultMessage("费用项总金额为0，无法添加附加费")
    public static final int SETTLEMENT_BILL_TOTAL_AMOUNT_IS_ZERO=80321;
    @ResultMessage("减免总额超出当前操作人可减免上限")
    public static final int REDUCT_AMOUNT_BEYOND_LIMIT=80322;
    @ResultMessage("费用项已存在，无法再次添加")
    public static final int PRICEDETAILS_REPEAT_CODE_EXSIT=80323;
    @ResultMessage("当前订单未处于待取车状态，无法修改可选费用项")
    public static final int UPDATE_PRICEITEMS_FOR_GAINCAR_STATUS_ERROR=80324;
    @ResultMessage("当前用户可能正在进行支付，请稍后重试")
    public static final int UPDATE_PRICEITEMS_ERROR_MAY_PAYING=80325;
    @ResultMessage("时间超时，请重新进入当前页面")
    public static final int UPDATE_PRICEITEMS_ERROR_OVER_TIME=80326;
    @ResultMessage("价格快照不存在")
    public static final int ORDER_PRICE_SNAPSHOT_NOT_EXISTS=80327;
    @ResultMessage("订单调价失败,支付金额不能为负")
    public static final int ORDER_ADJUST_FAILED = 80328;
    @ResultMessage("订单服务客户已禁用,下单失败")
    public static final int ORDER_CUSTOMER_FORBIDDEN = 80329;

    // =========================================结算账单========================
    @ResultMessage("当前订单未处于行程已结束状态,无法进行调价")
    public static  final int CAN_NOT_PRICE_ORDER_STATUS=80601;
    @ResultMessage("当前订单未处于待结算状态,无法进行结算")
    public static final int CAN_NOT_SETTLEMENT_ORDER_STATUS=80602;
    @ResultMessage("未获取到车辆油耗,无法确认费用项")
    public static final int CAN_NOT_CONFIRM_ORDER_BECAUSE_OVERTIME_OIL_QUERY_FAILED=80603;
    @ResultMessage("账单费用项异常,无法确认")
    public static final int WRONG_PRICE_ITEM_INFO=80604;
    @ResultMessage("结算账单不存在,请联系店员")
    public static final int SETTLE_BILL_NOT_EXIST=80605;
    @ResultMessage("结算账单存在多条")
    public static final int SETTLE_BILL_REPEAT=80606;
    @ResultMessage("结算账单已结算")
    public static final int SETTLE_BILL_IS_SETTLEMENT=80607;
    @ResultMessage("需退款的消费账单必须属于本订单")
    public static final int SETTLEMENT_REFUND_MUST_OWN_ORDER=80608;
    @ResultMessage("实际取还车时间异常,无法计算费用项")
    public static final int SETTLE_BILL_WRONG_TIME=80609;
    @ResultMessage("实际用还车时间缺失,无法计算费用项")
    public static final int SETTLE_BILL_LACK_OF_TIME=80610;
    @ResultMessage("订单结算，占用优惠券错误")
    public static final int SETTLE_BILL_COUPON_USED_ERROR=80611;
    @ResultMessage("订单未还车或已取消，不可进行结算")
    public static final int SETTLE_BILL_SETTLEMENT_STATUS_ERROR=80612;
    @ResultMessage("未能获取首租预付账单信息，无法结算")
    public static final int SETTLE_BILL_FIRST_ORDER_HEAD_FEE_ERROR=80613;
    @ResultMessage("当前订单所属的账单信息异常，无法结算")
    public static final int SETTLE_BILL_INFO_ERROR=80614;
    @ResultMessage("当前存在未支付续租申请，请让用户在App上取消续租")
    public static final int CAN_NOT_SETTLE_BECAUSE_EXIST_UNPAYED_AMOUNT=80615;
    @ResultMessage("当前订单正在结算，请稍后")
    public static final int CAN_NOT_SETTLE_BECAUSE_IS_SETTLING=80616;
    @ResultMessage("订单结算状态异常")
    public static final int WRONG_ORDER_SETTLE_STATUS=80617;
    @ResultMessage("当前订单正在被其他员工接待，无法再次接待")
    public static final int CAN_NOT_RECEIVE_BECAUSE_HAVE_RECEIVED_BY_OTHER=80618;
    @ResultMessage("订单未开始接待或者已完成结算接待")
    public static final int ORDER_NOT_IN_RECEIVING_STATUS=80619;
    @ResultMessage("无权取消其他员工的结算接待")
    public static final int CAN_NOT_CANCEL_OTHER_EMPLOYEE_RECEIVE=80620;
    @ResultMessage("当前订单已完成结算或无需结算")
    public static final int ORDER_HAVE_SETTLED_OR_NO_NEED_SETTLE=80621;
    @ResultMessage("减免金额不可大于费用项金额减去全部优惠金额")
    public static final int REDUCTION_AMOUNT_MUST_LESS_OTHER_AMOUNT=80622;
    @ResultMessage("您已添加过所有费用项，不能再次添加")
    public static final int CAN_NOT_ADD_PRICE_ITEM_BECAUSE_HAVE_ADDED_ALL=80623;
    @ResultMessage("减免费用项不存在")
    public static final int NOT_EXIST_PRICE_DETAIL_FOR_REDUCT=80624;
    //==========================================订单结算相关===========================
    @ResultMessage("订单未结算，不支持当前操作")
    public static  final int ORDER_UN_SETTLE  = 80900;
    @ResultMessage("订单已结算，不支持当前操作")
    public static  final int ORDER_END_SETTLE = 80901;

    //==========================================业财对接========82000-82499===================
    @ResultMessage("退款推送业财数据失败。")
    public static final int REFUND_PUSH_FINANCIAL_DATA_ERROR = 82001;

    // region 附加费 82500-82999
    @ResultMessage("订单未完成，无法添加附加费")
    public static final int CAN_NOT_ADD_SURCHARGE_BECAUSE_ORDER_NOT_FINISH=82500;
    @ResultMessage("当前账单非附加费账单，无法操作")
    public static final int CAN_NOT_UPDATE_BECAUSE_NOT_SURCHARGE_BILL=82501;
    // endregion

    // region 库存 83000-83499
    @ResultMessage("释放库存失败")
    public static final int ORDER_RELEASE_STOCK_FAIL=83000;
    // endregion

    // region 优惠券 83500-83999
    @ResultMessage("解除优惠券占用失败")
    public static final int ORDER_CANCEL_COUPON_FAIL=83500;

    // region 费用明细 84000-84499
    @ResultMessage("费用明细项不存在")
    public static final int NOT_EXIST_PRICE_DETAIL=84000;
    @ResultMessage("订单状态异常，无法获取费用明细")
    public static final int CAN_NOT_GET_PRICE_DETAIL_BECAUSE_ORDER_STATUS_EXCEPTION=84001;
    @ResultMessage("未能获取订单租车账单信息")
    public static final int NOT_EXIST_RENT_CAR_BILL=84002;
    @ResultMessage("未能获取账单费用明细")
    public static final int NOT_EXIST_BILL_DETAIL=84003;

    // region 修改订单 84500-84999
    @ResultMessage("当前订单状态，不能再修改订单取车截止时间")
    public static final int CAN_NOT_MODIFY_GAINCAR_DEADLINE_TIME      = 84500;
    @ResultMessage("取车截止时间不能早于 {0}")
    public static final int GAINCAR_DEADLINE_TIME_TOO_EARLY                  = 84501;
    @ResultMessage("取车截止时间不能晚于 {0}")
    public static final int GAINCAR_DEADLINE_TIME_TOO_LATE                    = 84502;





}
