package com.izu.mrcar.order.errcode;

import com.izu.framework.web.rest.response.ResultMessage;
import org.springframework.web.bind.annotation.RestController;

/**商务车队订单评价业务错误码**/
public class MrCarOrderErrorCode {

    @ResultMessage("{0}")
    public static final int COMMON_ERROR_CODE =9999;
    // =========================================调度 3000~4000==============================================
    @ResultMessage("一口价金额不能为空")
    public static final int FIXED_PRICE_NULL = 3000;
    @ResultMessage("一口价金额不能小于0")
    public static final int FIXED_PRICE_ERROR = 3001;
    @ResultMessage("查询调度单信息为空")
    public static final int QUERY_SCHEDULE_NULL = 3002;
    @ResultMessage("调度单已经被指派或者已经被调度")
    public static final int SCHEDULE_ALREADY_DISPATCH_SCHEDULE = 3003;
    @ResultMessage("指派失败，该订单正在被别的调度员指派")
    public static final int DISPATCHING = 3004;
    @ResultMessage("调度失败，该订单正在被别的调度员调度")
    public static final int SCHEDULING = 3005;
    @ResultMessage("调度失败，请确认订单或者车辆司机的状态后重新再试")
    public static final int SCHEDULE_FAILED = 3006;
    @ResultMessage("指派失败，请确认总调度单的状态后重新再试")
    public static final int DISPATCH_FAILED = 3007;
    @ResultMessage("重新指派失败，请确认子调度单的状态后重新再试")
    public static final int REDISPATCH_FAILED = 3008;
    @ResultMessage("创建调度单失败")
    public static final int CREATE_SCHEDULE_FAILED = 3009;
    @ResultMessage("调度单主键不能为空")
    public static final int SCHEDULE_NOT_NULL = 3010;
    @ResultMessage("调度失败")
    public static final int SCHEDULE_ERROR = 3011;
    @ResultMessage("指派失败")
    public static final int DISPATCH_ERROR = 3012;
    @ResultMessage("查询信息为空")
    public static final int QUERY_NULL = 3013;
    @ResultMessage("不能重复选择司机")
    public static final int DUPLICATE_DRIVER_ERROR = 3014;
    @ResultMessage("不能重复选择车辆")
    public static final int DUPLICATE_VEHICLE_ERROR = 3015;
    @ResultMessage("所选车辆或司机在该时段内已有订单，请重新调度")
    public static final int SCHEDULE_DRIVER_VEHICLE_ERROR = 3016;
    @ResultMessage("获取车级列表失败！")
    public static final int GET_CAR_LEVEL_LIST_ERROR = 3017;


    @ResultMessage("司机已经出发，无法再调度")
    public static final int DRIVER_ALREADY_DISPATCH = 3018;
    @ResultMessage("未调度完成，无需清空调度")
    public static final int CLEAN_SCHEDULE_STATUS_ERROR = 3019;



    //=========================展示给用户的业务提醒信息
    @ResultMessage("{0}")
    public static final int DISPATCH_NOTICE_USER_MESSAGE = 88881;







    @ResultMessage("查询订单评价配置信息为空")
    public static final int QUERY_APPRAISAL_TYPE_LIST_NULL = 88001;

    @ResultMessage("评价已提交，请勿重复操作")
    public static final int ORDER_COMMENT_ALREAD_EXIST = 88002;

    @ResultMessage("查询订单评价信息为空")
    public static final int QUERY_ORDER_APPRAISAL_NULL = 88003;

    @ResultMessage("评价明细不能为空！")
    public static final int ORDER_EVALUATION_DETAIL_NOT_NULL=88010;




    @ResultMessage("操作失败，请联系客服人员！")
    public static final int OPRATE_FAIL = 88099;
    // =========================================订单下单类89100~89199==============================================
    @ResultMessage("您预订的车型发生了调整，请您重新下单！")
    public static final int MRCAR_ORDER_SNAPSHORT_TIMEOUT = 89100;
    @ResultMessage("预定车级无价格策略，请联系车队管理人员！")
    public static final int MRCAR_ORDER_PRICE_NOTEXIT = 89101;
    @ResultMessage("当前版本不支持自助取还用车，请升级到最新版本！")
    public static final int MRCAR_ORDER_SERVICE_CODE_UNSUPPORT = 89102;
    @ResultMessage("您预订的车辆信息发生变化，请您重新下单！")
    public static final int MRCAR_ORDER_BOOKING_VEHICLE_CHANGE = 89103;

    @ResultMessage("当前版本不支持商务用车下单，请升级到最新版本！")
    public static final int MRCAR_ORDER_VERSION_BUSINESS_UNSUPPORT = 89104;

    @ResultMessage("APP版本过低，请前往应用商店下载Mr.Car最新版APP！")
    public static final int MRCAR_DRIVER_VERSION_BUSINESS_UNSUPPORT = 89105;

    // 参数验证类
    @ResultMessage("参数{0}必填")
    public static final int PARAMETER_REQUIRED=80000;

    @ResultMessage("查询用户信息失败。")
    public static final int QUERY_USER_ERROR = 80001;
    @ResultMessage("查询车型信息失败。")
    public static final int QUERY_CAR_MODEL_ERROR = 80002;
    @ResultMessage("订单申请不存在")
    public static final int QUERY_ORDER_APPLY_NOT_EXISTS = 80003;
    @ResultMessage("订单信息不存在")
    public static final int QUERY_ORDER_NOT_EXISTS = 80004;
    @ResultMessage("查询单据号不存在")
    public static final int QUERY_RECEIPT_NOT_EXISTS = 80005;
    @ResultMessage("支付方式错误，不能下单。")
    public static final int WRONG_PAY_METHOD_TYPE = 80006;
    @ResultMessage("您选取的时间有误，请重新选取。")
    public static final int WRONG_GAINCARTIME_BACKCARTIME = 80007;
    @ResultMessage("查询价格日历失败")
    public static final int QUERY_PRICE_CALENDAR_ERROR=80009;

    // =========================================订单下单类==============================================
    @ResultMessage("创建订单申请失败")
    public static final int CREATE_ORDER_APPLY_ERROR = 80010;
    @ResultMessage("上传订单服务前后照片失败")
    public static final int CREATE_ORDER_VEHICLE_IMG_ERROR = 80011;
    @ResultMessage("上传订单费用补录凭证失败")
    public static final int CREATE_ORDER_CERTIFICATE_ERROR = 80012;
    @ResultMessage("订单取消失败")
    public static final int ORDER_CANCEL_ERROR = 80013;
    @ResultMessage("设置一口价失败")
    public static final int SET_FIXED_PRICE_FAIL = 80014;
    @ResultMessage("订单申请取消失败")
    public static final int ORDER_APPLY_CANCEL_ERROR = 80015;

    @ResultMessage("order apply not exists")
    public static final int QUERY_ORDER_APPLY_NOT_EXISTS_EN = 80016;
    @ResultMessage("The bill is not available")
    public static final int CAN_NOT_CONFIRM_ORDER_BILL_EN=80017;
    @ResultMessage("参数{0}的格式不正确")
    public static final int PARAMETER_INCORRECT_FORMAT=80018;
    @ResultMessage("乘车人有重复，请确认")
    public static final int PASSENGER_REPEAT=80019;

    @ResultMessage("系统异常，订单类型和服务类型未找到对应的处理方法")
    public static final int ORDER_TYPE_ERR=80020;
    @ResultMessage("下单异常，多日用车的开始时间和结束时间不匹配")
    public static final int ORDER_TIME_ERR=80021;

    @ResultMessage("乘车人姓名超过{}个字符，请确认")
    public static final int PASSENGER_NAME_TOO_LONG=80022;

    // =========================================订单行程操作类==============================================
    @ResultMessage("司机动作不合法")
    public static final int DRIVER_ACTION_ILLEGAL = 80060;
    @ResultMessage("司机操作失败")
    public static final int DRIVER_ACTION_ERROR = 80061;
    @ResultMessage("该行程订单不符合当前操作条件")
    public static final int DRIVER_ACTION_NOT_ALLOW = 80062;
    @ResultMessage("订单调度失败")
    public static final int ORDER_DIPATCH_ERROR = 80063;
    @ResultMessage("订单状态不符合调度条件")
    public static final int ORDER_DIPATCH_FAIL = 80064;
    @ResultMessage("订单调度详情信息为空")
    public static final int ORDER_DIPATCH_NOT_EXSIST = 80065;
    @ResultMessage("该行程订单非申请人不符合当前操作条件")
    public static final int DRIVER_PROPOSER_ACTION_NOT_ALLOW = 80066;
    @ResultMessage("司机事件上报失败")
    public static final int DRIVER_ACTION_SAVE_ERROR = 80067;
    @ResultMessage("您有未完成订单，请处理后再出发！")
    public static final int DRIVER_ACTION_NOT_ALLOW_START = 80068;
    @ResultMessage("您有未完成的行程{0}")
    public static final int DRIVER_ACTION_NOT_ALLOW_START_DRIVER = 80069;
    @ResultMessage("当前车辆已被行程{0}占用")
    public static final int DRIVER_ACTION_NOT_ALLOW_START_CAR = 80070;
    @ResultMessage("{0}，请重试")
    public static final int CAR_LOCK_ON_FAIL = 80080;
    @ResultMessage("{0},关锁失败，请重试")
    public static final int CAR_LOCK_OFF_FAIL = 80081;
    @ResultMessage("{0},当前车辆不在围栏内")
    public static final int CAR_NOT_IN_FENCE = 80082;
    @ResultMessage("{0},供油异常")
    public static final int GIVE_OIL_FAIL = 80083;
    @ResultMessage("{0},响应超时")
    public static final int SELF_HELP_ORDER_TIME_OUT = 80084;
    @ResultMessage("订单指派详情信息为空")
    public static final int ORDER_APPOINT_NOT_EXSIST = 80090;
    // =========================================订单取消类==============================================
    @ResultMessage("当前订单不可取消")
    public static final int CANCEL_NOT_ALLOWED = 80101;
    @ResultMessage("客服类型不存在")
    public static final int CANCEL_STAFF_TYPE_NOT_EXIST = 80102;
    @ResultMessage("此订单存在未支付的账单无法取消，请稍后重试")
    public static final int CANCEL_NOT_ALLOWED_BECAUSE_EXIST_UNPAID_AMOUNT = 80103;
    @ResultMessage("仅支持取消状态订单红冲收入，当前订单状态不支持")
    public static final int ORDER_RED_REDUCE_MUST_CANCEL_STATUS = 80104;
    @ResultMessage("已申请开具发票，不可操作红冲")
    public static final int ORDER_RED_REDUCE_MUST_NOT_APPLY_INVOICE = 80105;
    @ResultMessage("红冲金额不符，不可操作红冲")
    public static final int ORDER_RED_REDUCE_MUST_EQUEAL_AMOUNT = 80106;
    @ResultMessage("订单尚未核算完成，不可操作红冲")
    public static final int ORDER_RED_REDUCE_MUST_AFTER_FINANCIAL_FINISH = 80107;
    @ResultMessage("该订单不属于当前用户")
    public static final int ORDER_NOT_BELONG_CURRENCT_USER = 80108;

    @ResultMessage("当前行程已出发,无法取消")
    public static final int ORDER_CANCEL_STARTING = 80109;

    @ResultMessage("当前行程已结束,无法取消")
    public static final int ORDER_CANCEL_FINISHED = 80110;

    @ResultMessage("订单状态已变更，请刷新页面重试")
    public static final int ORDER_STATUS_HAS_CHANGE = 80111;

    @ResultMessage("订单状态不支持当前操作")
    public static final int ORDER_STATUS_ERROR_NOT_SUPPORT_OPT = 80112;

    // =========================================订单审批类==============================================
    @ResultMessage("当前订单不可审批")
    public static final int APPROVAL_NOT_ALLOWED = 80201;
    @ResultMessage("审批操作不合法")
    public static final int APPROVAL_OPTION_ERROR = 80202;
    @ResultMessage("审批失败")
    public static final int APPROVAL_OPTION_FAIL = 80203;
    @ResultMessage("驳回原因不能为空")
    public static final int REJECT_REASON_NOT_NULL = 80204;

    // =========================================消费账单类==============================================
    @ResultMessage("当前消费账单不存在")
    public static final int BILL_ORDER_NOT_EXIST=80301;
    @ResultMessage("账单信息不存在")
    public static final int QUERY_BILL_NOT_EXISTS = 80302;
    @ResultMessage("账单确认短信发送失败")
    public static final int CONFIRM_MESSAGE_SEND_FAIL=80303;
    @ResultMessage("当前账单不可确认")
    public static final int CAN_NOT_CONFIRM_ORDER_BILL=80304;
    @ResultMessage("账单确认短息发送成功{0}条，失败{1}条，失败原因{2}")
    public static final int CONFIRM_FAIL_MSG=80305;
    @ResultMessage("账单确认失败")
    public static final int CONFIRM_ORDER_BILL_FAIL=80306;
    @ResultMessage("获取账单确认信息失败")
    public static final int QUERY_CONFIRM_ORDER_BILL_FAIL=80307;
    @ResultMessage("结算账单已支付")
    public static final int SETTLEMENT_BILL_PAYED=80308;
    @ResultMessage("费用明细格式错误")
    public static final int PRICEDETAILS_ERROR=80309;
    @ResultMessage("费用明细中存在重复费用编码")
    public static final int PRICEDETAILS_REPEAT_CODE=80310;
    @ResultMessage("存在重复退款账单编号")
    public static final int CONSUME_BILL_REPEAT_CODE=80311;
    @ResultMessage("填写退款金额总和与可退金额不相等")
    public static final int SETTLEMENT_REFUND_AMOUNT_ERROR=80312;
    @ResultMessage("需退款的消费账单必须为租车账单")
    public static final int SETTLEMENT_REFUND_MUST_RENTCONSUME=80313;
    @ResultMessage("需退款的消费账单可退金额不足")
    public static final int SETTLEMENT_REFUND_CONSUME_AMOUNT_LESS=80314;
    @ResultMessage("订单未完成全部收款，尚不可结算")
    public static final int SETTLEMENT_CONSUME_AMOUNT_NOT_FINISH=80315;
    @ResultMessage("减免金额不能大于总金额")
    public static final int REDUCT_AMOUNT_MAX_TOTAL_AMOUNT=80316;
    @ResultMessage("将抵扣的消费账单已收款")
    public static final int CONSUME_BILL_EXIST_PAYED = 80317;
    @ResultMessage("押金未完成全部收款，尚不可结算")
    public static final int SETTLEMENT_DEPOSIT_AMOUNT_NOT_FINISH=80318;
    @ResultMessage("消费账单已支付，无法修改金额")
    public static final int ADD_FEE_CONSUME_EXSIT_PAY=80319;
    @ResultMessage("请等待订单结算后再进行此次操作")
    public static final int SETTLEMENT_BILL_UN_END=80320;
    @ResultMessage("费用项总金额为0，无法添加附加费")
    public static final int SETTLEMENT_BILL_TOTAL_AMOUNT_IS_ZERO=80321;
    @ResultMessage("减免总额超出当前操作人可减免上限")
    public static final int REDUCT_AMOUNT_BEYOND_LIMIT=80322;
    @ResultMessage("费用项已存在，无法再次添加")
    public static final int PRICEDETAILS_REPEAT_CODE_EXSIT=80323;
    @ResultMessage("当前订单未处于待取车状态，无法修改可选费用项")
    public static final int UPDATE_PRICEITEMS_FOR_GAINCAR_STATUS_ERROR=80324;
    @ResultMessage("当前用户可能正在进行支付，请稍后重试")
    public static final int UPDATE_PRICEITEMS_ERROR_MAY_PAYING=80325;
    @ResultMessage("时间超时，请重新进入当前页面")
    public static final int UPDATE_PRICEITEMS_ERROR_OVER_TIME=80326;
    @ResultMessage("价格快照不存在")
    public static final int ORDER_PRICE_SNAPSHOT_NOT_EXISTS=80327;
    @ResultMessage("订单调价失败,支付金额不能为负")
    public static final int ORDER_ADJUST_FAILED = 80328;
    @ResultMessage("订单服务客户已禁用,下单失败")
    public static final int ORDER_CUSTOMER_FORBIDDEN = 80329;
    @ResultMessage("当前行程{0},无法取消")
    public static final int ORDER_CANCEL_FAILED = 80330;

    // =========================================结算账单========================
    @ResultMessage("当前订单未处于行程已结束状态,无法进行调价")
    public static  final int CAN_NOT_PRICE_ORDER_STATUS=80601;
    @ResultMessage("当前订单未处于待结算状态,无法进行结算")
    public static final int CAN_NOT_SETTLEMENT_ORDER_STATUS=80602;
    @ResultMessage("未获取到车辆油耗,无法确认费用项")
    public static final int CAN_NOT_CONFIRM_ORDER_BECAUSE_OVERTIME_OIL_QUERY_FAILED=80603;
    @ResultMessage("账单费用项异常,无法确认")
    public static final int WRONG_PRICE_ITEM_INFO=80604;
    @ResultMessage("结算账单不存在,请联系店员")
    public static final int SETTLE_BILL_NOT_EXIST=80605;
    @ResultMessage("结算账单存在多条")
    public static final int SETTLE_BILL_REPEAT=80606;
    @ResultMessage("结算账单已结算")
    public static final int SETTLE_BILL_IS_SETTLEMENT=80607;
    @ResultMessage("需退款的消费账单必须属于本订单")
    public static final int SETTLEMENT_REFUND_MUST_OWN_ORDER=80608;
    @ResultMessage("实际取还车时间异常,无法计算费用项")
    public static final int SETTLE_BILL_WRONG_TIME=80609;
    @ResultMessage("实际用还车时间缺失,无法计算费用项")
    public static final int SETTLE_BILL_LACK_OF_TIME=80610;
    @ResultMessage("订单结算，占用优惠券错误")
    public static final int SETTLE_BILL_COUPON_USED_ERROR=80611;
    @ResultMessage("订单未还车或已取消，不可进行结算")
    public static final int SETTLE_BILL_SETTLEMENT_STATUS_ERROR=80612;
    @ResultMessage("未能获取首租预付账单信息，无法结算")
    public static final int SETTLE_BILL_FIRST_ORDER_HEAD_FEE_ERROR=80613;
    @ResultMessage("当前订单所属的账单信息异常，无法结算")
    public static final int SETTLE_BILL_INFO_ERROR=80614;
    @ResultMessage("当前存在未支付续租申请，请让用户在App上取消续租")
    public static final int CAN_NOT_SETTLE_BECAUSE_EXIST_UNPAYED_AMOUNT=80615;
    @ResultMessage("当前订单正在结算，请稍后")
    public static final int CAN_NOT_SETTLE_BECAUSE_IS_SETTLING=80616;
    @ResultMessage("订单结算状态异常")
    public static final int WRONG_ORDER_SETTLE_STATUS=80617;
    @ResultMessage("当前订单正在被其他员工接待，无法再次接待")
    public static final int CAN_NOT_RECEIVE_BECAUSE_HAVE_RECEIVED_BY_OTHER=80618;
    @ResultMessage("订单未开始接待或者已完成结算接待")
    public static final int ORDER_NOT_IN_RECEIVING_STATUS=80619;
    @ResultMessage("无权取消其他员工的结算接待")
    public static final int CAN_NOT_CANCEL_OTHER_EMPLOYEE_RECEIVE=80620;
    @ResultMessage("当前订单已完成结算或无需结算")
    public static final int ORDER_HAVE_SETTLED_OR_NO_NEED_SETTLE=80621;
    @ResultMessage("减免金额不可大于费用项金额减去全部优惠金额")
    public static final int REDUCTION_AMOUNT_MUST_LESS_OTHER_AMOUNT=80622;
    @ResultMessage("您已添加过所有费用项，不能再次添加")
    public static final int CAN_NOT_ADD_PRICE_ITEM_BECAUSE_HAVE_ADDED_ALL=80623;
    @ResultMessage("减免费用项不存在")
    public static final int NOT_EXIST_PRICE_DETAIL_FOR_REDUCT=80624;
    @ResultMessage("综合管理平台没有维护此客户，无法推送账单")
    public static final int CRM_NO_COMPANY=80625;
    @ResultMessage("MR.CAR没有维护此客户，无法推送账单")
    public static final int NO_COMPANY=80626;
    @ResultMessage("使用权益卡后不支持修改客户企业和用车时间")
    public static final int CAN_NOT_UPDATE_CUSTOMER_COMPANY=80627;
    //==========================================订单结算相关===========================
    @ResultMessage("订单未结算，不支持当前操作")
    public static  final int ORDER_UN_SETTLE  = 80900;
    @ResultMessage("订单已结算，不支持当前操作")
    public static  final int ORDER_END_SETTLE = 80901;

    //==========================================业财对接========82000-82499===================
    @ResultMessage("退款推送业财数据失败。")
    public static final int REFUND_PUSH_FINANCIAL_DATA_ERROR = 82001;

    // region 附加费 82500-82999
    @ResultMessage("订单未完成，无法添加附加费")
    public static final int CAN_NOT_ADD_SURCHARGE_BECAUSE_ORDER_NOT_FINISH=82500;
    @ResultMessage("当前账单非附加费账单，无法操作")
    public static final int CAN_NOT_UPDATE_BECAUSE_NOT_SURCHARGE_BILL=82501;
    // endregion

    // region 库存 83000-83499
    @ResultMessage("释放库存失败")
    public static final int ORDER_RELEASE_STOCK_FAIL=83000;
    // endregion

    // region 优惠券 83500-83999
    @ResultMessage("解除优惠券占用失败")
    public static final int ORDER_CANCEL_COUPON_FAIL=83500;

    // region 费用明细 84000-84499
    @ResultMessage("费用明细项不存在")
    public static final int NOT_EXIST_PRICE_DETAIL=84000;
    @ResultMessage("订单状态异常，无法获取费用明细")
    public static final int CAN_NOT_GET_PRICE_DETAIL_BECAUSE_ORDER_STATUS_EXCEPTION=84001;
    @ResultMessage("未能获取订单租车账单信息")
    public static final int NOT_EXIST_RENT_CAR_BILL=84002;
    @ResultMessage("未能获取账单费用明细")
    public static final int NOT_EXIST_BILL_DETAIL=84003;

    // region 修改订单 84500-84999
    @ResultMessage("当前订单状态，不能再修改订单取车截止时间")
    public static final int CAN_NOT_MODIFY_GAINCAR_DEADLINE_TIME      = 84500;
    @ResultMessage("取车截止时间不能早于 {0}")
    public static final int GAINCAR_DEADLINE_TIME_TOO_EARLY                  = 84501;
    @ResultMessage("取车截止时间不能晚于 {0}")
    public static final int GAINCAR_DEADLINE_TIME_TOO_LATE                    = 84502;

    @ResultMessage("对不起，当前乘车人暂未绑定员工车辆，无法发起私车公用")
    public static final int PRIVATE_CAR_NOT_FIND                    = 84503;

    @ResultMessage("获取报警信息失败")
    public static final int GET_WARN_INFO_ERROR                    = 84504;
    @ResultMessage("缺失价格策略，请联系运营人员进行配置！")
    public static final int GET_PRICE_POLICY_ERROR = 84505;
    @ResultMessage("{0}")
    public static final int SUBMIT_APPROVAL_ERROR=84506;

    @ResultMessage("行程出发失败——未到达预计出发日期  如需变更行程，可取消重提")
    public static final int START_TRAVEL_DATE_ERROR = 84507;

    @ResultMessage("未检测到车机在线，请开启车机再启动行程")
    public static final int START_TRAVEL_OBD_ERROR = 84508;

    @ResultMessage("开始行程前置校验失败")
    public static final int START_TRAVEL_PER_CHECK_ERROR = 84509;

    @ResultMessage("获取里程异常")
    public static final int GET_MILEAGE_ERROR = 84510;

    @ResultMessage("距您预计用车结束时间已超3小时，请确认您真实的用车结束时间，避免影响报销")
    public static final int END_DATE_ERROR = 84511;

    @ResultMessage("创建失败，您存在「待出发」或「行程中」的行程单，请结束后申请。")
    public static final int HAVE_AN_ORDER_IN_PROGRESS = 84512;

    @ResultMessage("需要手动录入里程")
    public static final int NEED_ENTER_MILEAGE = 84513;

    @ResultMessage("获取企业配置失败")
    public static final int GET_COMPANY_CONF_ERROR = 84514;

    @ResultMessage("获取车辆实时状态失败")
    public static final int GET_VEHICLE_REAL_TIME_STATUS_ERROR = 84515;

    @ResultMessage("获取设备信息失败")
    public static final int GET_VEHICLE_INFO_ERROR = 84516;

    @ResultMessage("距乘客上车未满一分钟")
    public static final int END_TIME_ERROR=84517;

    @ResultMessage("该类型的订单不支持此操作！")
    public static final int ORDER_TYPE_NOT_SUPPORT=84518;

    @ResultMessage("获取车辆信息失败！")
    public static final int GET_VEHICLE_INFO_BY_ID_ERROR=84519;

    @ResultMessage("对不起，当前企业暂未开通私车公用权限！")
    public static final int PRIVATE_CAR_SWITCH_OFF = 84520;

    @ResultMessage("司机或者车辆被占用，请先结束该司机或车辆的其他行程！")
    public static final int DRIVER_OR_VEHICLE_IS_OCCUPIED = 84521;

    @ResultMessage("结束行程的里程数必须大于开始行程时的里程数！")
    public static final int ENTERED_MILEAGE_MUST_GREATER_BEFORE= 84522;

    @ResultMessage("获取共享锁超时！")
    public static final int DISTRIBUTION_LOCK_TIMOUT=84523;


    @ResultMessage("请升级版本后，录入结束里程！")
    public static final int UPGRADE_VERSION = 84524;

    @ResultMessage("存在行程中的订单，请在结束后开始新的行程！")
    public static final int CAN_NOT_START_TRAVEL=84525;

    @ResultMessage("请填写行程备注后提交")
    public static final int ORDER_DETAIL_NULL = 84526;

    @ResultMessage("对不起，当前企业暂未开通内部用车权限！")
    public static final int INNER_CAR_SWITCH_OFF = 84527;

    @ResultMessage("对不起，您暂未绑定私车，请联系贵司行政录入后使用")
    public static final int PRIVATE_CAR_NOT_FIND_FOR_APP  = 84528;

    @ResultMessage("对不起，该车暂未绑定设备，请您联系管理员")
    public static final int ORDER_SELF_HELP_DEVICE_NOT_EXIST  = 84529;


    // ==========================零散用车========================
    // region 账单 90000-90499
    @ResultMessage("调整后租金需与客户确认租金一致才可提交，请修改账单明细")
    public static final int ADJUST_BILL_AMOUNT_NOT_MATCH = 90000;
    @ResultMessage("账单信息不存在")
    public static final int NOT_EXIST_BILL_INFO = 90001;
    @ResultMessage("账单状态异常")
    public static final int BILL_INFO_STATUS_EXCEPTION = 90002;
    @ResultMessage("账单调整后天数之和不能超过实际天数")
    public static final int BILL_ADJUST_DAY_TOO_LARGE = 90003;
    @ResultMessage("没有可提交的数据 ，提交失败")
    public static final int BILL_ADJUST_DATA_EMPTY = 90004;
    @ResultMessage("收入账单未对账完成")
    public static final int INCOME_BILL_STATUS_NOT_CHECK_FINISHED = 90005;
    @ResultMessage("收入账单推送状态不是待推送或推送失败")
    public static final int INCOME_BILL_SETTLE_STATUS_NOT_MATCH = 90006;

    // 90500-90599 需求单
    @ResultMessage("附件数量最多支持9个")
    public static final int DEMAND_ORDER_CREATE_ATTACHMENT_MORE_ERROR = 90500;
    @ResultMessage("司机数量不可大于车辆数量")
    public static final int DEMAND_ORDER_CREATE_DRIVER_MORE_ERROR = 90501;
    @ResultMessage("单价总额不正确")
    public static final int DEMAND_ORDER_CREATE_UNIT_AMOUNT_ERROR = 90502;
    @ResultMessage("预计单价总额错误")
    public static final int DEMAND_ORDER_CREATE_ESTIMATE_UNIT_AMOUNT_ERROR = 90503;
    @ResultMessage("需求单号不能为空")
    public static final int DEMAND_ORDER_DETAIL_NUM_NOT_NULL = 90504;
    @ResultMessage("未查询到需求单详情")
    public static final int DEMAND_ORDER_DETAIL_NULL_ERROR = 90505;
    @ResultMessage("当前需求单状态不支持取消")
    public static final int DEMAND_ORDER_CANCEL_STATUS_ERROR = 90506;
    @ResultMessage("客户信息不能为空")
    public static final int DEMAND_ORDER_COMPANY_NULL_ERROR = 90507;
    @ResultMessage("用车人姓名不能超过10个字符")
    public static final int DEMAND_ORDER_CUSTOMER_NAME_TOO_LONG = 90508;
    @ResultMessage("用车地点不能超过50个字符")
    public static final int DEMAND_ORDER_CUSTOMER_ADDRESS_TOO_LONG = 90509;
    @ResultMessage("其他说明不能超过100个字符")
    public static final int DEMAND_ORDER_OTHER_EXPLAIN_TOO_LONG = 90510;
    @ResultMessage("车辆最多支持1000辆")
    public static final int DEMAND_ORDER_VEHICLE_COUNT_MAX = 90511;
    @ResultMessage("合同参数不能为空")
    public static final int DEMAND_ORDER_CONTRACT_NULL = 90512;
    @ResultMessage("客户所属部门不可以是总部部门")
    public static final int DEMAND_ORDER_DO_NOT_EQUALS_HQ = 90513;
    @ResultMessage("附件名字长度过长")
    public static final int DEMAND_ORDER_ATTACHMENT_NAME_TOO_LONG = 90514;
    @ResultMessage("价格配置被禁用")
    public static final int DEMAND_ORDER_PRICE_CONFIG_DISABLED = 90515;
    @ResultMessage("预计用车时间最早只可以选当前时间1年内的时间")
    public static final int DEMAND_ORDER_ESTIMATE_USE_DATE = 90516;
    @ResultMessage("预计还车时间最早只可以选当前时间1年内的时间")
    public static final int DEMAND_ORDER_ESTIMATE_RETURN_DATE = 90517;

    // 90600-90699 订单
    @ResultMessage("需求单号不能为空")
    public static final int DEMAND_ORDER_NUM_NOT_NULL_ERROR = 90601;
    @ResultMessage("未找到派单类型")
    public static final int DISPATCH_ORDER_TYPE_NULL_ERROR = 90602;
    @ResultMessage("派单类型不一致")
    public static final int DISPATCH_ORDER_TYPE_DIFF_ERROR = 90603;
    @ResultMessage("当前需求单/订单正在操作派单，请稍后再试")
    public static final int DISPATCH_ORDER_CLASH_ERROR = 90604;
    @ResultMessage("当前订单状态不支持派单")
    public static final int DISPATCH_ORDER_STATUS_ERROR = 90605;
    @ResultMessage("当前状态不能取消订单")
    public static final int CANCEL_ORDER_STATUS_ERROR = 90606;
    @ResultMessage("操作失败，请稍后再试")
    public static final int CUSTOMER_ORDER_LOCK_ACQUIRE_ERROR = 90607;
    @ResultMessage("客户订单状态不支持当前操作")
    public static final int CUSTOMER_ORDER_STATUS_ERROR = 90608;
    @ResultMessage("未查询到客户订单信息")
    public static final int CUSTOMER_ORDER_NULL_ERROR = 90609;
    @ResultMessage("未查到需求单")
    public static final int DISPATCH_ORDER_DEMAND_ORDER_NULL_ERROR = 90610;
    @ResultMessage("当前需求单已派单或已取消")
    public static final int DISPATCH_ORDER_DISPATCHED_ERROR = 90611;
    @ResultMessage("未查到需求单关联车辆信息")
    public static final int DISPATCH_ORDER_VEHICLE_NULL_ERROR = 90612;
    @ResultMessage("派单车数与订单信息的车辆数不同，不可提交派单")
    public static final int DISPATCH_ORDER_VEHICLE_COUNT_NO_EQUAL_ERROR = 90613;
    @ResultMessage("派单司机数与订单信息的司机数不同，不可提交派单")
    public static final int DISPATCH_ORDER_DRIVER_COUNT_NO_EQUAL_ERROR = 90614;
    @ResultMessage("派单司机数大于车辆数，不可提交派单")
    public static final int DISPATCH_ORDER_DRIVER_MORE_THEN_VEHICLE_ERROR = 90615;
    @ResultMessage("换车单状态不支持派单操作")
    public static final int DISPATCH_ORDER_CHANGE_ORDER_STATUS_ERROR = 90616;
    @ResultMessage("供应商{0}状态不支持派单操作")
    public static final int DISPATCH_ORDER_SUPPLIER_STATUS_ERROR = 90617;
    @ResultMessage("客户所属部门与当前登录人的部门不一致，不能派单")
    public static final int DISPATCH_ORDER_CUSTOMER_DIFF_ERROR = 90618;
    @ResultMessage("订单不支持新增司机")
    public static final int DISPATCH_ORDER_ADD_DRIVER_ERROR = 90619;

    @ResultMessage("创建订单失败")
    public static final int DISPATCH_ORDER_FAIL = 90620;

    /** 蔚来专用错误码 start**/
    @ResultMessage("服务商订单状态不支持取消，请线下联系服务商")
    public static final int DISPATCH_ORDER_CANCEL_STATUS_ERROR = 90621;

    @ResultMessage("服务商订单状态不支持编辑，请线下联系服务商")
    public static final int DISPATCH_ORDER_EDIT_STATUS_ERROR = 90622;
    /** 蔚来专用错误码 end**/

    // 90700-90799 供应商订单
    @ResultMessage("系统错误")
    public static final int SYSTEM_ERROR = 90700;
    @ResultMessage("获取分布式锁失败")
    public static final int GET_LOCK_ERROR = 90701;
    @ResultMessage("供应商订单查询结果为空")
    public static final int SUPPLIER_ORDER_NULL_ERROR = 90702;
    @ResultMessage("供应商订单查询结果状态不正确")
    public static final int SUPPLIER_ORDER_STATUS_ERROR = 90703;
    @ResultMessage("供应商订单导出数据超过限制条数:{0}")
    public static final int SUPPLIER_ORDER_MORE_ERROR = 90704;
    @ResultMessage("供应商订单查询车辆信息为空")
    public static final int SUPPLIER_ORDER_VEHICLE_ERROR = 90705;
    @ResultMessage("供应商订单查询司机信息为空")
    public static final int SUPPLIER_ORDER_DRIVER_ERROR = 90706;
    @ResultMessage("总价格过高，请与首汽联系后再录入")
    public static final int SUPPLIER_ORDER_TOTAL_PRICE_ERROR = 90707;
    @ResultMessage("供应商操作处理的订单和后台查询到的订单数量不一致")
    public static final int SUPPLIER_ORDER_EQUALS_ERROR = 90708;
    @ResultMessage("派送车辆失败")
    public static final int SUPPLIER_CAR_ERROR = 90709;
    @ResultMessage("派送司机失败")
    public static final int SUPPLIER_DRIVER_ERROR = 90710;
    @ResultMessage("上传文件名称长度大于100个字符")
    public static final int FILE_NAME_LENGTH_ERROR = 90711;
    @ResultMessage("上传文件地址长度大于255个字符")
    public static final int FILE_URL_LENGTH = 90712;
    @ResultMessage("车牌号不能为空")
    public static final int VEHICLE_LICENSE_NOT_NULL = 90713;
    @ResultMessage("车辆日租金不能为空")
    public static final int VEHICLE_DAILY_RENT_NOT_NULL = 90714;
    @ResultMessage("司机姓名不能为空")
    public static final int DRIVER_NAME_NOT_NULL = 90715;
    @ResultMessage("司机日租金不能为空")
    public static final int DRIVER_DAILY_RENT_NOT_NULL = 90716;
    @ResultMessage("送车人姓名不能为空")
    public static final int DELIVERY_USE_NAME_NOT_NULL = 90717;
    @ResultMessage("送车人手机号不能为空")
    public static final int DELIVERY_USE_PHONE_NOT_NULL = 90718;
    @ResultMessage("送车人姓名不能超过10个字符")
    public static final int DELIVERY_USE_NAME_LENGTH = 90719;
    @ResultMessage("请联系首汽工作人员对供应商配置普票/专票规则")
    public static final int SUPPLIER_INVOICE_CONFIG_NOT_EXIST = 90720;
    @ResultMessage("请对客户配置普票/专票规则")
    public static final int CUSTOMER_INVOICE_CONFIG_NOT_EXIST = 90721;
    @ResultMessage("车辆价格过高，请与首汽联系后再录入")
    public static final int SUPPLIER_VEHICLE_RENT_ERROR = 90722;
    @ResultMessage("司机价格过高，请与首汽联系后再录入")
    public static final int SUPPLIER_DRIVER_RENT_ERROR = 90723;
    @ResultMessage("获取零散用车配置失败")
    public static final int GET_SO_PAYMENT_CONFIG_ERROR = 90724;
    @ResultMessage("所选时间范围不符合要求，您可选{0}时间之后的时间")
    public static final int DELIVERY_TIME_ERROR = 90725;


    // endregion
    // 90800-90899 换车单
    @ResultMessage("客户订单号不能为空")
    public static final int CUSTOMER_ORDER_NOT_NULL_ERROR = 90800;
    @ResultMessage("处理中，请勿重复提交")
    public static final int PROCESSING = 90801;
    @ResultMessage("{0}")
    public static final int CUSTOM_ERROR = 90802;
    @ResultMessage("换车单保存失败")
    public static final int VEHICLE_SAVE_ERROR = 90803;
    @ResultMessage("换车单详情查询失败")
    public static final int VEHICLE_QUERY_DETAIL_ERROR = 90804;

    @ResultMessage("换车单号不能为空")
    public static final int VEHICLE_NUM_ERROR = 90805;

    @ResultMessage("订单当前状态,不允许发起换车/司机")
    public static final int VEHICLE_OPERATION_NOT_ALLOWED_ERROR = 90806;

    @ResultMessage("当前订单有进行中的换车单,请完成后在操作换车/司机")
    public static final int VEHICLE_HAVING_OPERATION_ERROR = 90807;

    @ResultMessage("更换要求不能为空")
    public static final int VEHICLE_DEMAND_TYPE_NULL_ERROR = 90808;

    @ResultMessage("更换要求不正确")
    public static final int VEHICLE_DEMAND_TYPE_ERROR = 90809;

    @ResultMessage("预计换车用车时间不正确")
    public static final int VEHICLE_DEMAND_TIME_ERROR = 90810;

    @ResultMessage("更换说明不能为空")
    public static final int VEHICLE_DEMAND_EXPLAIN_ERROR = 90811;

    @ResultMessage("换车单不存在")
    public static final int VEHICLE_ORDER_NULL_ERROR = 90812;

    @ResultMessage("当前换车单不允许取消")
    public static final int VEHICLE_ORDER_NOT_OPERATION_ERROR = 90813;

    @ResultMessage("取消失败")
    public static final int VEHICLE_ORDER_FAIL_ERROR = 90814;

    @ResultMessage("该订单提交过“确认送达”，本次提交无效")
    public static final int VEHICLE_CONFIRM_DELIVERY_REPEAT_CLICK_ERROR = 90815;

    @ResultMessage("距还车时间较近，不可发起换车")
    public static final int VEHICLE_CANNOT_INITIATE_TRANSFER_ERROR = 90816;

    // 审核单
    @ResultMessage("已存在待审核或审核通过的记录，请勿重复提交")
    public static final int AUDIT_REPEAT_OPERATION_ERROR = 90900;

    @ResultMessage("审核类型不正确")
    public static final int AUDIT_TYPE_ERROR = 90901;

    @ResultMessage("还车时间不能为空")
    public static final int AUDIT_RETURN_TIME_NULL_ERROR = 90902;

    @ResultMessage("客户尚未还车")
    public static final int AUDIT_CUSTOMER_NOT_RETURN_ERROR = 90903;

    @ResultMessage("还车时间不能小于客户还车时间")
    public static final int AUDIT_TIME_LESS_THAN_CUSTOMER_RETURN_TIME_ERROR = 90904;

    @ResultMessage("还车时间不能大于还车时间上限")
    public static final int AUDIT_TIME_MORE_THAN_TOP_TIME_ERROR = 90905;

    @ResultMessage("交车时间不能为空")
    public static final int AUDIT_DELIVERY_TIME_ERROR = 90906;

    @ResultMessage("交车地址不能为空")
    public static final int AUDIT_DELIVERY_ADDRESS_ERROR = 90907;

    @ResultMessage("能源剩余不能为空")
    public static final int AUDIT_ENERGY_NULL_ERROR = 90908;

    @ResultMessage("里程数不能为空")
    public static final int AUDIT_MILEAGE_NULL_ERROR = 90909;

    @ResultMessage("交接单附件不能为空")
    public static final int AUDIT_HANDOVER_ATTACHMENT_NULL_ERROR = 90910;

    @ResultMessage("人车合影附件不能为空")
    public static final int AUDIT_PEOPLE_AND_CARS_NULL_ERROR = 90911;

    @ResultMessage("供应商订单号不能为空")
    public static final int AUDIT_SUPPLIER_ORDER_NUM_NULL_ERROR = 90912;

    @ResultMessage("客户订单状态更新失败")
    public static final int AUDIT_CUSTOMER_ORDER_STATUS_UPDATE_ERROR = 90913;

    @ResultMessage("客户或供应商，主动还车记录为空")
    public static final int AUDIT_RETURN_RECORD_NULL_ERROR = 90914;

    @ResultMessage("订单号不正确")
    public static final int AUDIT_ORDER_NUM_ERROR = 90915;

    @ResultMessage("审核结果保存失败")
    public static final int AUDIT_SAVE_RESULT_ERROR = 90916;

    @ResultMessage("审核单号不能为空")
    public static final int AUDIT_NUM_NULL_ERROR = 90917;

    @ResultMessage("审核单号不正确")
    public static final int AUDIT_NUM_ERROR = 90918;

    @ResultMessage("审核状态前后不能一样")
    public static final int AUDIT_STATUS_NO_CHANGE_ERROR = 90919;

    @ResultMessage("该单已经审核结束，不能重复审核")
    public static final int AUDIT_COMPLETE_ERROR = 90920;

    @ResultMessage("审核驳回原因不能为空")
    public static final int AUDIT_REJECT_REASON_NULL_ERROR = 90921;

    @ResultMessage("不存在的审核状态")
    public static final int AUDIT_STATUS_ERROR = 90922;

    @ResultMessage("里程数不正确")
    public static final int AUDIT_MILEAGE_NOT_NUMBER_ERROR = 90923;

    @ResultMessage("最大里程数不能大于9999999.99且小数点后最多保留两位有效数字")
    public static final int AUDIT_MILEAGE_PRECISION_ERROR = 90924;

    // 还车

    // 审核单
    @ResultMessage("当前订单状态不为使用中，不能执行该操作")
    public static final int RETURN_ORDER_STATUS_ERROR = 91000;

    @ResultMessage("还车时间不能小于最小时间")
    public static final int RETURN_TIME_LESS_THAN_MIN_TIME_ERROR = 91001;

    @ResultMessage("还车时间不能大于最大时间")
    public static final int RETURN_TIME_MOTE_THAN_MAX_TIME_ERROR = 91002;

    @ResultMessage("供应商订单号不存在")
    public static final int RETURN_SUPPLIER_NUM_ERROR = 91003;

    @ResultMessage("客户尚未还车，不允许该操作")
    public static final int RETURN_CUSTOMER_NOT_RTURN_ERROR = 91004;

    @ResultMessage("客户还车记录不存在")
    public static final int RETURN_CUSTOMER_RTURN_RECORD_NULL_ERROR = 91005;

    @ResultMessage("确认送达保存失败")
    public static final int CONFIRM_DELIVERY_SAVE_ERROR = 91006;

    @ResultMessage("系统还未收到交接单和人车合照资料，请联系首汽员工回传资料后，才可确认送达")
    public static final int CONFIRM_DELIVERY_SYSTEM_NOT_ATTACHMENT_ERROR = 91007;

    @ResultMessage("保存失败")
    public static final int RETURN_SAVE_ERROR = 91008;


    @ResultMessage("结束时间不能小于开始时间")
    public static final int START_DATE_LESS_END_DATE = 91009;

    @ResultMessage("商务车补录订单不存在！")
    public static final int BUSINESS_ORDER_ADDITION_NOT_FIND = 91010;


    @ResultMessage("该订单状态不允许被废除！")
    public static final int BUSINESS_ORDER_STATUS_CANNOT_TO_ABOLISH = 91011;

    @ResultMessage("该订单状态不允许被提交！")
    public static final int BUSINESS_ORDER_STATUS_CANNOT_TO_SUBMIT = 91012;


    @ResultMessage("非首汽车辆需要填写机动车所有人和机动车使用人！")
    public static final int BUSINESS_ORDER_STRUCT_CODE_NOT_FIND = 91013;

    @ResultMessage("机动车使用人或者机动车所有人的部门不存在！")
    public static final int DATACENTER_STRUCT_NOT_FIND = 91014;


    @ResultMessage("司机手机号和司机姓名必须同时存在或者同时不存在！")
    public static final int DRIVER_DESC_FIELD_NOT_FIND = 91015;

    @ResultMessage("该订单状态不允许被修改！")
    public static final int BUSINESS_ORDER_STATUS_CANNOT_TO_UPDATE = 91016;

    @ResultMessage("您的企业仍在试用阶段，暂时无法下单!")
    public static final int TRIAL_ACCOUNT_NOT_ALLOWED_CREATE_BUSINESS_ORDER = 91017;

    @ResultMessage("此类行程不支持修改")
    public static final int NOT_SUPPORT_MODIFY_TRIP=91018;

    @ResultMessage("当前车辆暂不支持，请使用钥匙开关锁")
    public static final int NOT_SUPPORT_LOCK=91019;


    @ResultMessage("请不要频繁点击!")
    public static final int NOT_CLICK_MANY=91020;

    @ResultMessage("该行程已反馈成功，请勿重复反馈")
    public static final int FEEDBACK_SUCCESS=91021;

    @ResultMessage("该行程已取消，请勿反馈")
    public static final int CANCEL_ORDER_FEEDBACK_SUCCESS=91022;


    @ResultMessage("当前行程不可取消")
    public static final int CANCEL_ORDER_APPLY=91023;

    /** 权益卡 */
    @ResultMessage("权益卡激活码找不到")
    public static final int BENEFIT_CARD_NOT_EXIST=91024;

    @ResultMessage("权益卡张数错误！")
    public static final int BENEFIT_NUM_NOT=91025;


    @ResultMessage("使用的多个激活码存在重复的情况,请检查后提交")
    public static final int BENEFIT_CARD_REPEAT=91026;

    @ResultMessage("权益卡激活码找不到")
    public static final int BENEFIT_SECRET_NOT_EXIST=91027;

    @ResultMessage("权益卡的订单类型不匹配")
    public static final int BENEFIT_SERVICE_NOT_MATCH = 91028;

    //权益卡的服务类型不匹配
    @ResultMessage("权益卡的用车型不匹配")
    public static final int BENEFIT_CAR_LEVEL_NOT_MATCH = 91029;

    //权益卡的城市不匹配
    @ResultMessage("权益卡的城市不匹配")
    public static final int BENEFIT_CITY_NOT_MATCH = 91030;

    //权益卡未激活
    @ResultMessage("权益卡未激活")
    public static final int BENEFIT_NOT_USE=91031;
    //权益卡的可用时间和下单不匹配
    @ResultMessage("权益卡的可用时间和下单不匹配")
    public static final int BENEFIT_TIME_NOT_MARCH=91032;

    //不使用快捷调度，选择的【预计用车时间】不得早于当前时间
    @ResultMessage("创建失败，【预计用车时间】不得早于当前")
    public static final int BENEFIT_FAST_DISPATCH_NOT_MARCH=91033;

    //用车开始时间格式不正确
    @ResultMessage("用车开始时间格式不正确")
    public static final int ORDER_TIME_NOT_MATCH=91034;

    @ResultMessage("输入的权益卡有误，请检查后重新创建")
    public static final int BENEFIT_CARD_MORE_CHECK=91035;
    /** 权益卡 */

    @ResultMessage("所选时间范围不符合要求，请在【{0},{1}】区间内选择")
    public static final int CONFIRM_DELIVERY_TIME_ERROR=91036;

    @ResultMessage("调价合计计算错误！")
    public static final int PRICE_CALCULATE_ERROR=91037;

    @ResultMessage("抵扣金额不能大于合计金额")
    public static final int DISCOUNT_MONEY_ERROR=91038;

    @ResultMessage("权益卡占用失败！")
    public static final int BENEFIT_CARD_OCCUPY_FAIL=91039;

    @ResultMessage("应收金额计算错误")
    public static final int RECEIPT_MONEY_ERROR=91040;

    @ResultMessage("输入的激活码不能为空")
    public static final int BENEFIT_SECRET_NOT_BLANK=91041;

    @ResultMessage("所选时间范围不符合要求")
    public static final int CONFIRM_DELIVERY_ERROR=91042;

    @ResultMessage("清空失败，存在车辆司机已出发")
    public static final int CLEAN_SCHEDULE_FAIL=91043;

    @ResultMessage("信息不存在")
    public static final int QUESTION_NOT_NULL=91100;

    @ResultMessage("当前已处理，无需在处理")
    public static final int QUESTION_STATUS_IS_HADNLE=91101;

    // 蓝牙关锁后检验车辆状态
    @ResultMessage("关锁失败，{0}")
    public static final int DEVICE_LOCK_WARN = 2040;


    @ResultMessage("获取司机信息失败！")
    public static final int GET_DRIVER_ERROR = 91102;

    @ResultMessage("获取下单人企业信息失败！")
    public static final int GET_CUSTOMER_COMPANY_ERROR =91103;

    @ResultMessage("存在三方服务，应该支出金额不能为空！")
    public static final int SUPPLIER_PAYABLE_AMOUNT_IS_NULL = 91104;


    @ResultMessage("服务类型错误暂时不支持计算")
    public static final int SERVICE_TYPE_ERROR = 91105;
    @ResultMessage("调度指派的车辆信息为空")
    public static final int SCHEDULE_CAR_IS_NULL = 91106;
    @ResultMessage("调度指派的司机信息为空")
    public static final int SCHEDULE_DRIVER_IS_NULL = 91107;
    @ResultMessage("调度指派的车辆和司机所属供应商不一致")
    public static final int SCHEDULE_DRIVER_AND_CAR_IS_NOT_SAME_COMPANY = 91108;
    @ResultMessage("选择的车辆重复，请重新选择！")
    public static final int SCHEDULE_VEHICLE_REPEAT = 91109;
    @ResultMessage("选择的司机重复，请重新选择！")
    public static final int SCHEDULE_DRIVER_REPEAT = 91110;
    @ResultMessage("您选择的{0}在计划用车时段有冲突的行程。是否仍旧选择？")
    public static final int DRIVER_VEHICLE_TIME_CROSS = 91111;

    @ResultMessage("车辆*租期个数不能大于30")
    public static final int CAR_RENT_NUM_MORE_THAN_30 = 91112;

    @ResultMessage("距离待出发不满4小时，无法取消。")
    public static final int CANCEL_ORDER_TIME_ERROR = 91113;

    // ----------- 订单开关锁错误提示 ------------
    // 业务逻辑错误, 移动端无需重试
    @ResultMessage("{0}")
    public static final int CAR_LOCK_LOGICAL_FAIL = 91114;
    // 调用链失败, 需要进行重试
    @ResultMessage("{0}")
    public static final int CAR_LOCK_RETRYABLE_FAIL = 91115;


    // 91200-91299 代步车
    @ResultMessage("订单信息不存在")
    public static final int ORDER_INFO_NOT_EXIST = 91200;
    @ResultMessage("查询车辆信息服务异常")
    public static final int ORDER_INFO_QUERY_VEHICLE_ERROR = 91201;
    @ResultMessage("车辆信息不存在")
    public static final int ORDER_INFO_VEHICLE_INFO_NOT_EXIST = 91202;
    @ResultMessage("城市配置不存在")
    public static final int ORDER_INFO_CITY_CONFIG_INFO_NOT_EXIST = 91203;
    @ResultMessage("价格配置不存在")
    public static final int ORDER_INFO_PRICE_CONFIG_INFO_NOT_EXIST = 91204;
    @ResultMessage("代步车订单导出数据超过限制条数:{0}")
    public static final int ORDER_EXPORT_MORE_ERROR = 91205;
    @ResultMessage("{0}")
    public static final int ORDER_COMMON_ERROR = 91206;
    @ResultMessage("订单状态错误，不支持取消操作")
    public static final int ORDER_STATUS_ERROR = 91207;
    @ResultMessage("车辆机动车使用人与当前订单接单分公司不一致")
    public static final int ORDER_OPERATE_BUSS_CODE_ERROR = 91208;

    @ResultMessage("机动车使用人或者机动车所有人未维护完成，请到调价页面维护机动车使用人")
    public static final int VEHICLE_MAINTAIN_NOT_COMPLETE = 91209;

    @ResultMessage("请求频繁，请稍后重试！")
    public static final int ORDER_LOCK_FAIL = 91210;

    @ResultMessage("指定的取车人或送车人不存在")
    public static final int ORDER_OPERATE_PERSON_NOT_EXIST = 91211;
    @ResultMessage("下单客户企业不存在")
    public static final int ORDER_COMPANY_NOT_EXIST = 91212;
    @ResultMessage("订单状态错误，不支持免押操作")
    public static final int DEPOSIT_FREE_ORDER_STATUS_ERROR = 91313; //91213公务用车已使用

    @ResultMessage("订单状态错误，不支持扣款操作")
    public static final int DEPOSIT_DEDUCT_ORDER_STATUS_ERROR = 91314;

    @ResultMessage("订单状态错误，不支持解冻操作")
    public static final int DEPOSIT_UNFREEZE_ORDER_STATUS_ERROR = 91315;

    @ResultMessage("订单扣款金额错误，不支持扣款操作")
    public static final int DEPOSIT_DEDUCT_AMOUNT_ERROR = 91316;

    @ResultMessage("预授权发生异常")
    public static final int PREAUTH_ERROR = 91317; //91213公务用车已使用

    @ResultMessage("订单状态错误，不支持预授权操作")
    public static final int DEPOSIT_PREAUTH_ERROR = 91318;

    @ResultMessage("订单预授权金额错误，不支持预授权操作")
    public static final int DEPOSIT_PREAUTH_AMOUNT_ERROR = 91319;

    @ResultMessage("订单解冻金额错误，不支持解冻操作")
    public static final int DEPOSIT_UNFREEZE_AMOUNT_ERROR = 91320;

    @ResultMessage("交易记录不存在")
    public static final int TRADE_RECORD_NOT_EXIST_ERROR = 91321;

    @ResultMessage("交易记录存在多条")
    public static final int TRADE_RECORD_EXIST_MULTIPLE_ERROR = 91322;

    @ResultMessage("押金记录不存在")
    public static final int DEPOSIT_RECORD_NOT_EXIST_ERROR = 91323;

    @ResultMessage("押金记录存在多条")
    public static final int DEPOSIT_RECORD_EXIST_MULTIPLE_ERROR = 91324;

    @ResultMessage("解冻调用收银台失败")
    public static final int DEPOSIT_UNFREEZE_FAIL_ERROR = 91325;

    @ResultMessage("扣款调用收银台失败")
    public static final int DEPOSIT_DEDUCT_FAIL_ERROR = 91326;
    // ----------- 公务用车错误码定义 ---------------

    @ResultMessage("车辆设备离线，暂不可用")
    public static final int VEHICLE_DEVICE_OFFLINE = 91213;

    @ResultMessage("车辆不在围栏内，暂不可用")
    public static final int VEHICLE_LEAVE_FENCE_ERROR = 91214;

    @ResultMessage("车辆有进行中的订单，暂不可用")
    public static final int VEHICLE_EXIST_ORDER_ERROR = 91215;

    @ResultMessage("订单正在处理, 请稍后")
    public static final int ORDER_EXIST_OTHER_TASK = 91216;

    @ResultMessage("订单任务不存在")
    public static final int ORDER_TASK_NOT_EXISTED = 91217;

    @ResultMessage("车辆处于任务中，暂不可用")
    public static final int VEHICLE_IN_USE_ERROR = 91218;

    @ResultMessage("车辆不存在")
    public static final int VEHICLE_NOT_EXIST_ERROR = 91219;

    @ResultMessage("车辆暂停运营，不可下单")
    public static final int VEHICLE_NOT_OPERATIONS_ERROR = 91220;

    @ResultMessage("车辆已不在当前企业下，不可下单")
    public static final int VEHICLE_NOT_EXISTED_COMPANY_ERROR = 91221;

    @ResultMessage("车辆已不在当前部门下，不可下单")
    public static final int VEHICLE_NOT_EXISTED_STRUCT_ERROR = 91222;

    @ResultMessage("订单不在当前企业下，不可操作")
    public static final int PUBLIC_GOV_ORDER_NOT_EXISTED_COMPANY_ERROR = 91223;

    @ResultMessage("预计还车时间超过企业配置最大可用车天数，请重新选择")
    public static final int PUBLIC_GOV_ORDER_EXCEED_MAX_AVAILABLE_DAYS = 91224;

    // ------- 设备操作相关 ----------

    @ResultMessage("设备离线, 结束失败")
    public static final int GOV_DEVICE_OFFLINE = 92001;

    @ResultMessage("结束失败, 请检查")
    public static final int GOV_DEVICE_NOT_TURNOFF = 92002;

    @ResultMessage("结束失败")
    public static final int GOV_DEVICE_LOCK_ERROR = 92003;

    @ResultMessage("结束失败")
    public static final int GOV_DEVICE_NOT_IN_FENCE = 92004;

    @ResultMessage("强制还车失败")
    public static final int GOV_DEVICE_FORCE_LOCK_ERROR = 92005;

    @ResultMessage("结束失败, 请打开手机定位权限")
    public static final int GOV_DEVICE_NO_GPS_POSITION = 92006;

    @ResultMessage("结束失败, 请尝试蓝牙设备")
    public static final int GOV_DEVICE_TRY_BLUETOOTH = 92007;


    @ResultMessage("未定义对应操作处理类")
    public static final int UNDEFINED_OPT_HANDLER = 93001;

    @ResultMessage("调度配置变更，请更新APP版本后重新进入。")
    public static final int MRCAR_DRIVER_VERSION_INTERNAL_CAR_UNSUPPORT = 93002;

    @ResultMessage("首约订单不支持调度或取消，请联系首约处理")
    public static final int MRCAR_SHOU_QI_YUE_CHE_ORDER_CANCEL = 93003;

    @ResultMessage("调度供应商和分配供应商不一致,请重新刷新页面操作")
    public static final int SCHEDULE_SUPPLIER_NOT_EQUALS_ASSIGN_SUPPLIER = 93004;


}
