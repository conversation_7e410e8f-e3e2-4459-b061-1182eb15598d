package com.izu.mrcar.order.dto.publicGovcar.app.req;

import com.izu.mrcar.order.dto.common.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 *
 */
@Data
public class AppPublicGovCarOrderEndTripReqDTO extends BaseDTO {

    @ApiModelProperty(value = "订单号", required = true)
    private String orderNo;

    @ApiModelProperty(value = "是否检查车辆在围栏")
    private Boolean ignoreFence;

    @ApiModelProperty(value = "是否忽略设备离线")
    private Boolean ignoreDevice;

    @ApiModelProperty(value = "是否经过蓝牙关锁成功")
    private Boolean lockByBluetooth;

    @ApiModelProperty(value = "手机定位纬度")
    private BigDecimal latitude;

    @ApiModelProperty(value = "手机定位经度")
    private BigDecimal longitude;

}
