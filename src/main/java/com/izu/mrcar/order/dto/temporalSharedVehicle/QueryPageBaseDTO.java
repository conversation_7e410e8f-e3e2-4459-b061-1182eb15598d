package com.izu.mrcar.order.dto.temporalSharedVehicle;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR> dongxiya 2023/11/22 13:50
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QueryPageBaseDTO extends DataPermDTO{
    @ApiModelProperty(value = "页码", example = "1", required = true)
    private Integer page=1;
    @ApiModelProperty(value = "页大小", example = "10", required = true)
    private Integer pageSize=10;
}
