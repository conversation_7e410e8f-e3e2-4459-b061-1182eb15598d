package com.izu.mrcar.order.dto.publicGovcar;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName PublicGovCarOrderSubDailyDTO
 * <AUTHOR>
 * @Date 2024/8/16 15:24
 * @Version 1.0
 */

@ApiModel("订单按日拆分明细信息")
@Data
public class PublicGovCarOrderSubDailyDTO {

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "车牌号")
    private String vehicleLicense;

    @ApiModelProperty(value = "车辆所属部门")
    private String vehicleStructName;

    @ApiModelProperty(value = "用车人部门")
    private String passengerStructName;

    @ApiModelProperty(value = "费用归属部门")
    private String feeStructName;

    @ApiModelProperty(value = "日期")
    private String statDate;

    @ApiModelProperty(value = "开始时间")
    private String expectedPickupTime;

    @ApiModelProperty(value = "结束时间")
    private String actualReturnTime;

    @ApiModelProperty(value = "用车时长(小时)")
    private String userTime;

    @ApiModelProperty(value = "里程数(km)")
    private BigDecimal totalMileage;


}