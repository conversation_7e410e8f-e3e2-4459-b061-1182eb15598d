package com.izu.mrcar.order.dto.businessOrder;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description: 计算商务车预估金额入参
 * @date 2023/12/6 14:32
 */
@Data
public class OrderEstimateReqDTO {

    @ApiModelProperty(value = "服务类型", required = true, example = "1", notes = "半日、整日、接机、送机等")
    @NotBlank(message = "服务类型不能为空")
    private String serviceCode;

    /**
     * 预约开始行程城市编码
     */
    @ApiModelProperty(value = "预约开始行程城市编码", required = true, example = "1101")
    @NotNull(message = "预约开始行程城市编码不能为空")
    private Integer bookingStartCityCode;

    @ApiModelProperty("车级ID")
    @NotNull(message = "车级不能为空")
    private Byte assignCarlevelId;

    /**
     * 预约订单开始服务时间,多个逗号分隔（用于多日订单）
     */
    @ApiModelProperty(value = "预计订单开始时间", required = true, example = "2019-01-01 14:00:00", notes = "多日订单逗号分割")
    @NotBlank(message = "预约开始时间不能为空")
    private String bookingOrderStime;
    /**
     * 预计订单结束时间,多个逗号分隔（用于多日订单)
     */
    @ApiModelProperty(value = "预计订单结束时间", example = "2019-01-01 14:00:00", notes = "多日订单逗号分割")
    @NotBlank(message = "预计订单结束时间不能为空")
    private String bookingOrderEtime;

}
