package com.izu.mrcar.order.dto.publicGovcar;

import com.izu.mrcar.order.dto.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 对公公务用车创建订单请求参数（App）
 * @date 2024/8/15 9:45
 */
@ApiModel("创建订单请求参数")
@Data
public class AppPublicGovCarCreateOrderReqDTO extends BaseDTO {


    @ApiModelProperty(value = "车辆部门id")
    private Integer vehicleStructId;

    @ApiModelProperty(value = "车辆部门Code")
    private String vehicleStructCode;

    @ApiModelProperty(value = "车辆部门名称")
    private String vehicleStructName;

    @ApiModelProperty(value = "投放模式：1-平台共享车，2-定点投放")
    private Byte deploymentMode;

    @ApiModelProperty(value = "乘车人信息", required = true)
    @NotNull(message = "乘车人不能为空")
    List<PassengersInfo> passengersInfoList;


    @ApiModelProperty(value = "司机id", required = true)
    @NotNull(message = "司机id不能为空")
    private Integer driverId;

    @ApiModelProperty(value = "司机code", required = true)
    @NotEmpty(message = "司机code不能为空")
    private String driverCode;

    @ApiModelProperty(value = "司机姓名", required = true)
    @NotEmpty(message = "司机姓名不能为空")
    private String driverName;

    @ApiModelProperty(value = "司机电话", required = true)
    @NotEmpty(message = "司机电话不能为空")
    private String driverMobile;

    @ApiModelProperty(value = "司机部门id", required = true)
    @NotNull(message = "司机部门id不能为空")
    private Integer driverStructId;

    @ApiModelProperty(value = "司机部门Code", required = true)
    @NotEmpty(message = "司机部门Code不能为空")
    private String driverStructCode;

    @ApiModelProperty(value = "司机部门名称", required = true)
    @NotEmpty(message = "司机部门名称不能为空")
    private String driverStructName;

    @ApiModelProperty(value = "预计出发地长地址", required = true)
    @NotEmpty(message = "预计出发地不能为空")
    private String estimatedDepartureLocation;

    @ApiModelProperty(value = "预计目的地长地址", required = true)
    @NotEmpty(message = "预计目的地不能为空")
    private String estimatedDestinationLocation;

    @ApiModelProperty(value = "预计出发地短地址", required = true)
    @NotEmpty(message = "预计出发地短地址不能为空")
    private String estimatedDepartureShortLocation;

    @ApiModelProperty(value = "预计目的地短地址", required = true)
    @NotEmpty(message = "预计目的地短地址不能为空")
    private String estimatedDestinationShortLocation;

    @ApiModelProperty(value = "预计出发地纬度", required = true)
    @NotNull(message = "预计出发地纬度不能为空")
    private BigDecimal estimatedDepartureLatitude;

    @ApiModelProperty(value = "预计出发地经度", required = true)
    @NotNull(message = "预计出发地经度不能为空")
    private BigDecimal estimatedDepartureLongitude;

    @ApiModelProperty(value = "预计目的地纬度", required = true)
    @NotNull(message = "预计目的地纬度不能为空")
    private BigDecimal estimatedDestinationLatitude;

    @ApiModelProperty(value = "预计目的地经度", required = true)
    @NotNull(message = "预计目的地经度不能为空")
    private BigDecimal estimatedDestinationLongitude;

    @ApiModelProperty(value = "车辆ID", required = true)
    @NotNull(message = "车辆ID不能为空")
    private Integer vehicleId;

    @ApiModelProperty(value = "车辆车牌号")
    private String vehicleLicense;

    @ApiModelProperty(value = "车架号")
    private String vehicleVin;

    @ApiModelProperty(value = "无钥匙用车")
    private Byte noKeyUseCar;

    @ApiModelProperty(value = "车辆品牌编码")
    private String vehicleBrandCode;

    @ApiModelProperty(value = "车辆品牌")
    private String vehicleBrandName;

    @ApiModelProperty(value = "车型编码")
    private String vehicleModelCode;

    @ApiModelProperty(value = "车型名称")
    private String vehicleModelName;

    @ApiModelProperty(value = "车辆图片URL路径")
    private String vehiclePicUrl;

    @ApiModelProperty(value = "用车事由", required = true)
    @NotEmpty(message = "用车事由不能为空")
    private String carUseReason;

    @ApiModelProperty(value = "预计取车时间", required = true)
    @NotNull(message = "预计取车时间不能为空")
    private Date expectedPickupTime;

    @ApiModelProperty(value = "预计还车时间", required = true)
    @NotNull(message = "预计还车时间不能为空")
    private Date expectedReturnTime;

    @ApiModelProperty(value = "备注")
    private String orderUserMemo;

    /** 下单时电子围栏快照ID **/
    @ApiModelProperty(value = "下单时电子围栏快照ID")
    private Integer initialFenceId;

    /** 下单时车辆纬度 **/
    @ApiModelProperty(value = "下单时车辆纬度")
    private BigDecimal initialLatitude;

    /** 下单时车辆经度 **/
    @ApiModelProperty(value = "下单时车辆经度")
    private BigDecimal initialLongitude;

    /** 下单时车辆逆地理位置 **/
    @ApiModelProperty(value = "下单时车辆逆地理位置")
    private String initialLocation;


    @Data
    @ApiModel("乘车人信息")
    public static class PassengersInfo {

        @ApiModelProperty(value = "乘车人ID", required = true)
        @NotNull(message = "乘车人ID不能为空")
        private Integer passengerId;

        @ApiModelProperty(value = "乘车人编码", required = true)
        @NotEmpty(message = "乘车人编码不能为空")
        private String passengerCode;

        @ApiModelProperty(value = "乘车人姓名", required = true)
        @NotEmpty(message = "乘车人姓名不能为空")
        private String passengerName;

        @ApiModelProperty(value = "乘车人电话", required = true)
        @NotEmpty(message = "乘车人电话不能为空")
        private String passengerMobile;

        @ApiModelProperty(value = "乘车人部门id", required = true)
        @NotNull(message = "乘车人部门id不能为空")
        private Integer passengerStructId;

        @ApiModelProperty(value = "乘车人部门Code", required = true)
        @NotEmpty(message = "乘车人部门Code不能为空")
        private String passengerStructCode;

        @ApiModelProperty(value = "乘车人部门名称", required = true)
        @NotEmpty(message = "乘车人部门名称不能为空")
        private String passengerStructName;

    }


}
