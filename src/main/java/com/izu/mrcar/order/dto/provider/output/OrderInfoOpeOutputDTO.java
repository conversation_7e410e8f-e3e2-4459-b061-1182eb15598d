package com.izu.mrcar.order.dto.provider.output;

import com.izu.mrcar.order.consts.MileageStatTypeEnum;
import com.izu.mrcar.order.consts.OrderEnum;
import com.izu.mrcar.order.dto.mrcar.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Data
public class OrderInfoOpeOutputDTO {
    /**
     * 订单ID id
     */
    @ApiModelProperty("订单id")
    private Long id;

    /**
     * 订单创建时间 create_time
     */
    private Date createTime;

    /**
     * 订单更新时间 update_time
     */
    private Date updateTime;

    @ApiModelProperty("供应商名称")
    private String supplierName;

    /**
     * 订单编号 order_no
     */
    @ApiModelProperty("子行程编号")
    private String orderNo;

    /**
     * 所属订单申请编号 order_apply_no
     */
    @ApiModelProperty("主行程编号")
    private String orderApplyNo;

    /**
     * 提供车辆或者司机的供应商编码
     */
    private String supplierProviderCode;


    /**
     * 订单所属企业id company_id
     */
    private Integer companyId;

    /**
     * 订单所属企业名称
     */
    @ApiModelProperty(value = "下单人部门")
    private String companyName;

    @ApiModelProperty(value = "车辆供应商名称")
    private String vehicleProviderName;

    @ApiModelProperty(value = "司机供应商名称")
    private String driverProviderName;
    /**
     * 所属组织结构ID struct_id
     */
    private Integer structId;

    /**
     * 订单类型：1：内部用车；2：外部订单；3：商务用车；4：短租；5：约车；6：国际租车 order_type
     */
    private Byte orderType;

    /**
     * 服务类型编码：service_dictionary表service_code service_code
     */
    private String serviceCode;

    /**
     * 下单人ID customer_id
     */
    private Integer customerId;

    /**
     * 下单人姓名 customer_name
     */
    @ApiModelProperty(value = "下单人")
    private String customerName;

    /**
     * 下单人电话 customer_mobile
     */
    @ApiModelProperty(value = "下单人电话")
    private String customerMobile;

    /**
     * 是否本人用车：1：本人；2：他人 himself
     */
    private Boolean himself;

    /**
     * 约定乘车人ID booking_passenger_user_id
     */
    private Long bookingPassengerUserId;

    /**
     * 约定乘车人姓名 booking_passenger_user_name
     */
    @ApiModelProperty(value = "乘车人姓名")
    private String bookingPassengerUserName;

    /**
     * 约定乘车人手机号码 booking_passenger_user_phone
     */
    @ApiModelProperty(value = "乘车人电话")
    private String bookingPassengerUserPhone;

    /**
     * 预约订单开始服务时间 booking_order_stime
     */
    @ApiModelProperty("预约订单开始服务时间")
    private Date bookingOrderStime;

    /**
     * 预约订单结束服务时间 booking_order_etime
     */
    @ApiModelProperty("预约订单结束服务时间")
    private Date bookingOrderEtime;

    /**
     * 渠道方的订单号 channel_order_code
     */
    private String channelOrderCode;

    /**
     * 预约开始行程长地址 booking_start_long_addr
     */
    private String bookingStartLongAddr;

    /**
     * 预约开始行程短地址 booking_start_short_addr
     */
    private String bookingStartShortAddr;

    /**
     * 预约开始行程坐标点(经度,纬度;经度,纬度 前高德后百度) booking_start_point
     */
    private String bookingStartPoint;

    /**
     * 预约终止行程长地址 booking_end_long_addr
     */
    private String bookingEndLongAddr;

    /**
     * 预约终止行程短地址 booking_end_short_addr
     */
    private String bookingEndShortAddr;

    /**
     * 预约终止行程坐标点(经度,纬度;经度,纬度 前高德后百度) booking_end_point
     */
    private String bookingEndPoint;

    /**
     * 开始行程城市编码 start_city_code
     */
    private Integer startCityCode;

    /**
     * 开始行程城市名称 start_city_name
     */
    private String startCityName;

    /**
     * 终止行程城市编码 end_city_code
     */
    private Integer endCityCode;

    /**
     * 终止行程城市名称 end_city_name
     */
    private String endCityName;

    /**
     * 预订车级ID booking_carlevel_id
     */
    private Byte bookingCarlevelId;

    /**
     * 预订车级名称 booking_carlevel_name
     */
    private String bookingCarlevelName;

    /**
     * 订单取消类型（0:调度-超时自动取消，1:运力不足-自动取消） order_cancellation_type
     */
    private Byte orderCancellationType;

    /**
     * 是否可以派车并通知用户（0不可以 1可以） assign_car_can_able
     */
    private Boolean assignCarCanAble;

    /**
     * 派车通知用户的状态（0未派车通知 1派车通知用户成功 2派车通知用户成功[但车辆不可用] 3派车通知用户失败[可重试]） assign_car_status
     */
    private Byte assignCarStatus;

    /**
     * 派车通知用户申请时间 assign_car_apply_time
     */
    private Date assignCarApplyTime;

    /**
     * 派车通知用户申请次数 assign_car_apply_count
     */
    private Short assignCarApplyCount;

    /**
     * 派车通知用户成功时间 assign_car_success_time
     */
    private Date assignCarSuccessTime;

    /**
     * 派车通知用户失败时间 assign_car_failure_time
     */
    private Date assignCarFailureTime;

    /**
     * 排车、派车成功后的车级ID assign_carlevel_id
     */
    private Byte assignCarlevelId;

    /**
     * 排车、派车成功后的车级名称 assign_carlevel_name
     */
    @ApiModelProperty(value ="车级名称")
    private String assignCarlevelName;

    /**
     * 排车、派车成功后的车辆ID assign_car_id
     */
    private Long assignCarId;

    /**
     * 排车、派车成功后的车牌号码 assign_car_license
     */
    @ApiModelProperty(value = "车牌号")
    private String assignCarLicense;

    /**
     * 排车、派车成功后的司机ID assign_driver_id
     */
    private Long assignDriverId;

    /**
     * 排车、派车成功后的司机姓名 assign_driver_name
     */
    @ApiModelProperty(value = "车辆司机信息-司机姓名")
    private String assignDriverName;

    /**
     * 排车、派车成功后的司机手机号码 assign_driver_phone
     */
    @ApiModelProperty(value = "司机电话")
    private String assignDriverPhone;

    /**
     * 排车、派车成功后的车型ID assign_carmodel_id
     */
    private Integer assignCarmodelId;

    /**
     * 排车、派车成功后的车型名称 assign_carmodel_name
     */
    @ApiModelProperty(value = "车型")
    private String assignCarmodelName;

    /**
     * 订单取消单编号 order_cancel_no
     */
    private String orderCancelNo;

    /**
     * 订单取消时间 order_cancel_time
     */
    private Date orderCancelTime;

    /**
     * 是否已发表评价 appraise_submited
     */
    private Boolean appraiseSubmited;

    /**
     * 发表评价时间 appraise_submit_time
     */
    private Date appraiseSubmitTime;

    /**
     * 交通号码类型（1：机场-航班号、2：高铁-车次号、3：火车站-车次号） traffic_type
     */
    private Byte trafficType;

    /**
     * 交通号码 traffic_number
     */
    private String trafficNumber;

    /**
     * 实际开始行程长地址 fact_start_long_addr
     */
    @ApiModelProperty("出发地")
    private String factStartLongAddr;

    /**
     * 实际开始行程短地址 fact_start_short_addr
     */
    private String factStartShortAddr;

    /**
     * 实际开始行程坐标点(经度,纬度;经度,纬度 前高德后百度) fact_start_point
     */
    private String factStartPoint;

    /**
     * 实际终止行程长地址 fact_end_long_addr
     */
    private String factEndLongAddr;

    /**
     * 实际终止行程短地址 fact_end_short_addr
     */
    @ApiModelProperty("目的地")
    private String factEndShortAddr;

    /**
     * 实际终止行程坐标点(经度,纬度;经度,纬度 前高德后百度) fact_end_point
     */
    private String factEndPoint;

    /**
     * 实际开始行程时间 fact_start_date
     */
    @ApiModelProperty("乘客上车时间/实际出发时间")
    private Date factStartDate;

    /**
     * 实际终止行程时间 fact_end_date
     */
    @ApiModelProperty("乘客下车时间/实际结束时间")
    private Date factEndDate;

    /**
     * 司机出发时间 driver_start_time
     */
    private Date driverStartTime;

    /**
     * 司机到达时间 driver_arrive_time
     */
    private Date driverArriveTime;

    /**
     * 乘客上车时间 get_on_time
     */
    private Date getOnTime;

    /**
     * 订单状态(10调度中 20调度成功 25司机出发 26司机到达 30行程中 40行程已结束 70待结算 90已完成 100已取消) order_status
     */
    @ApiModelProperty("子行程状态(10待调度 20待出发 25司机已出发 26司机已到达 30行程中 40行程已结束 50照片上传中 60费用补录中 70待结算 90已完成 100已取消 )")
    private Short orderStatus;

    /**
     * 订单状态名称
     */
    @ApiModelProperty(value = "行程状态名称")
    private String orderStatusName;

    /**
     * 总金额 total_amount
     */
    private BigDecimal totalAmount;

    /**
     * 应支付金额 shouldpay_amount
     */
    private BigDecimal shouldpayAmount;
    /**
     * 是否一口价；1：是；0：不是 fixed_price_valid
     */
    private Boolean fixedPriceValid;

    /**
     * 一口价金额 fixed_price
     */
    private BigDecimal fixedPrice;

    /**
     * 订单服务前后照片信息
     */
    @ApiModelProperty("服务前后照片信息")
    private List<OrderVehicleImgDTO> orderVehicleImgList;

    private String vehicleStructName;
    @ApiModelProperty(value = "车辆品牌")
    private String vehicleBrand;
    private String vehicleModel;
    private String vehicleLevel;
    private String vehicleColour;
    @ApiModelProperty(value = "座位")
    private Integer vehicleSeatCount;

    /**
     * 订单费用明细
     */
    @ApiModelProperty("订单费用明细")
    private BillOrderDTO billOrderDTO;

    /**
     * 夜间服务开始时间，格式：HH:mm:ss night_service_stime
     */
    private String nightServiceStime;

    /**
     * 夜间服务结束时间，格式：HH:mm:ss night_service_etime
     */
    private String nightServiceEtime;

    /**
     * 下单备注 order_detail
     */
    @ApiModelProperty(value = "备注")
    private String orderDetail;
    /**
     * 价格策略名称
     */
    @ApiModelProperty("价格策略名称")
    private String policyName;

    private OrderPriceSnapshotDTO orderPriceSnapshot;

    /**
     * 订单里程来源
     */
    @ApiModelProperty("订单里程来源 1百度鹰眼 2 车机上报 3 APP GPS上报 4 车载GPS上报 5人工上报 ")
    private Byte mileageSource;

    /**
     * 服务类型名称
     */
    @ApiModelProperty(value = "基本信息-行程类型")
    private String serviceName;

    /**
     * 订单类型名称
     */
    @ApiModelProperty(value = "基本信息-用车类型")
    private String orderTypeName;

    /**
     * 是否需要上传服务后照片
     */
    private Boolean isUpload;

    /**
     * 车队ID motorcade_id
     */
    private Integer motorcadeId;

    /**
     * 车队名称 motorcade_name
     */
    @ApiModelProperty(value = "司机车队")
    private String motorcadeName;

    /**
     * 订单申请状态时间流
     */
    @ApiModelProperty("时间流")
    List<OrderTimeStreamDTO> orderTimeStreamDTOList;

    /**
     * 实际订单用车企业ID customer_company_id
     */
    private Integer customerCompanyId;

    /**
     * 实际订单用车企业名称 customer_company_name
     */
    @ApiModelProperty("乘车人部门")
    private String customerCompanyName;

    /**
     * 多目的地的集合
     */
    @ApiModelProperty("多目的地集合")
    private List<OrderApplyDestinationDTO> destinationDTOS;
    /**
     * 多乘车人集合
     */
    @ApiModelProperty("多乘车人集合")
    private List<OrderApplyPassengerDTO> passengerDTOS;

    /**
     * 下单人部门
     */
    private String structName;

    /**
     * 主乘车人所属部门名称
     */
    private String passengerStructName;

    @ApiModelProperty("手动服务前人工录入里程数")
    private BigDecimal startManualMileage;

    @ApiModelProperty("手动服务后人工录入里程数")
    private BigDecimal endManualMileage;

    /** 订单里程（公里） **/
    @ApiModelProperty("里程数")
    private BigDecimal tripMileage;

    @ApiModelProperty("服务前录入时间")
    private Date startDeviceDate;

    @ApiModelProperty("服务后录入时间")
    private Date endDeviceDate;


    /** 用车异常标记,多异常用逗号分割,对应：UserExceptionMarkEnum **/
    @ApiModelProperty("用车异常标记")
    private String userExceptionMark;

    @ApiModelProperty("用车异常标记名称")
    private String userExceptionMarkName;

    //app端按钮
    @ApiModelProperty(value = "取消行程-私车公用新增",example = "true")
    private Boolean cancelOrder;

    @ApiModelProperty(value = "立刻出发-私车公用新增",example = "true")
    private Boolean immediateTrip;
    @ApiModelProperty(value = "开始导航-私车公用新增",example = "true")
    private Boolean startNavigation;
    @ApiModelProperty(value = "结束行程-私车公用新增",example = "true")
    private Boolean finishTrip;
    @ApiModelProperty(value = "再次下单-私车公用新增",example = "true")
    private Boolean againOrder;

    /** 里程统计类型:0-默认 1-百度鹰眼 2-车机上报 3-APP GPS上报 4-车载GPS上报 5-人工录入 **/
    @ApiModelProperty("里程统计类型:0-默认 1-百度鹰眼 2-车机上报 3-APP GPS上报 4-车载GPS上报 5-人工录入")
    private Byte mileageStatType;

    @ApiModelProperty("里程统计类型名称")
    private String mileageStatTypeName;

    @ApiModelProperty(value = "用车人数量")
    private Integer bookingPassengerCount;

    @ApiModelProperty("车辆圆形图片")
    private String vehicleRoundOn;

    /**
     * 派车司机所属城市代码 assign_driver_city_code
     */
    @ApiModelProperty("派车司机所属城市代码")
    private Integer assignDriverCityCode;

    /**
     * 派车司机所属城市名称 assign_driver_city_name
     */
    @ApiModelProperty("派车司机所属城市名称")
    private String assignDriverCityName;

    @ApiModelProperty("是否展示取消按钮")
    private Boolean cancelButtonDisplay;

    /**自助取还 蓝牙开锁**/
    @ApiModelProperty("是否强制结束")
    private Boolean forceEndBtn;

    @ApiModelProperty("是否强制结束(内部用车-非自助取还)")
    private Boolean innerForceEndBtn;


    //里程纠偏按钮是否展示
    @ApiModelProperty("里程纠偏按钮是否展示")
    private Boolean isShowMileageAdjustBtn;


    /**
     * 服务类型名称
     */
    @ApiModelProperty("服务类型名称")
    private String providerName;

    /**
     * 支出单状态名称
     */
    @ApiModelProperty("支出单状态名称")
    private String expenditureStatusStr;



    //车辆是否并行
    @ApiModelProperty(value = "车辆是否并行")
    private String vehicleParallelOrderStr;

    @ApiModelProperty(value = "司机是否并行")
    private String driverParallelOrderStr;




    /**
     * 订单类型：1：内部用车；2：外部订单；3：商务用车；4：短租；5：约车；6：国际租车
     */
    public void setOrderType(Byte orderType) {
        this.orderType = orderType;
        this.orderTypeName = OrderEnum.OrderTypeForApp.getTextByValue(orderType);
    }

    /**
     * 服务类型编码：service_dictionary表service_code
     */
    public void setServiceCode(String serviceCode) {
        this.serviceCode = serviceCode == null ? null : serviceCode.trim();
        this.serviceName = OrderEnum.OrderServiceType.getTextByValue(serviceCode);
    }


    /**
     * 订单状态(10调度中 20调度成功 25司机出发 26司机到达 30行程中 40行程已结束 70待结算 90已完成 100已取消)
     */
    public void setOrderStatus(Short orderStatus) {

        this.orderStatus = orderStatus;
        this.orderStatusName =OrderEnum.OrderStatus.getTextByValue(orderStatus);

    }


    public void setMileageStatType(Byte mileageStatType) {
        this.mileageStatType = mileageStatType;
        MileageStatTypeEnum mileageStatTypeEnum=MileageStatTypeEnum.getMileageStatTypeEnum(mileageStatType);
        this.mileageStatTypeName = Objects.nonNull(mileageStatTypeEnum)?mileageStatTypeEnum.getDesc():"";
    }
}
