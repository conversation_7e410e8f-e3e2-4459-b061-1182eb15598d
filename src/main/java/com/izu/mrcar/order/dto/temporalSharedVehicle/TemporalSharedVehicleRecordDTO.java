package com.izu.mrcar.order.dto.temporalSharedVehicle;

import com.izu.mrcar.order.dto.mrcar.OrderApplyDestinationDTO;
import com.izu.mrcar.order.dto.mrcar.OrderApplyPassengerDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> dongxiya 2024/1/27 15:26
 */
@Data
public class TemporalSharedVehicleRecordDTO {
    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "报警编号(用车记录编号)")
    private String warnSn;

    @ApiModelProperty(value = "报警记录表ID")
    private Long warnId;

    @ApiModelProperty(value = "单据状态1-进行中2-已完成3-已废除")
    private Byte recordStatus;

    @ApiModelProperty(value = "单据状态1-进行中2-已完成3-已废除")
    private String recordStatusName;

    @ApiModelProperty(value = "报警开始时间")
    private Date warnStartTime;

    @ApiModelProperty(value = "报警结束时间")
    private Date warnEndTime;

    @ApiModelProperty(value = "报警开始纬度")
    private BigDecimal warnStartLatitude;

    @ApiModelProperty(value = "报警开始经度")
    private BigDecimal warnStartLongitude;

    @ApiModelProperty(value = "报警结束纬度")
    private BigDecimal warnEndLatitude;

    @ApiModelProperty(value = "报警开始经度")
    private BigDecimal warnEndLongitude;

    @ApiModelProperty(value = "持续时长(分钟)")
    private Integer duration;

    @ApiModelProperty(value = "持续时长(小时)")
    private BigDecimal durationHour;

    @ApiModelProperty(value = "行驶里程(公里)")
    private BigDecimal tripMileage;

    @ApiModelProperty(value = "申请单关联关系1-未关联2-已关联")
    private Byte orderAssociateStatus;

    @ApiModelProperty(value = "申请单关联关系1-未关联2-已关联")
    private String orderAssociateStatusName;

    @ApiModelProperty(value = "订单创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改人ID")
    private Integer updateUserId;

    @ApiModelProperty(value = "修改人姓名")
    private String updateUserName;

    @ApiModelProperty(value = "订单更新时间")
    private Date updateTime;

    @ApiModelProperty(value = "修改备注")
    private String updateRemark;

    @ApiModelProperty(value = "车辆ID")
    private Long vehicleId;

    @ApiModelProperty(value = "车牌号码")
    private String vehicleLicense;

    @ApiModelProperty(value = "车架号")
    private String vehicleVin;

    @ApiModelProperty(value = "车型ID")
    private Integer vehicleBrandId;

    @ApiModelProperty(value = "车辆品牌CODE码")
    private String vehicleBrandCode;

    @ApiModelProperty(value = "车辆品牌")
    private String vehicleBrand;

    @ApiModelProperty(value = "车型ID")
    private Integer vehicleModelId;

    @ApiModelProperty(value = "车型码")
    private String vehicleModelCode;

    @ApiModelProperty(value = "车辆型号")
    private String vehicleModel;

    @ApiModelProperty(value = "车辆类型，1：标准型；2：舒适型；3：豪华型；4：商务型；5：尊贵型")
    private Byte vehicleType;

    @ApiModelProperty(value = "车辆类型，1：标准型；2：舒适型；3：豪华型；4：商务型；5：尊贵型")
    private String vehicleTypeName;

    @ApiModelProperty(value = "所属城市代码")
    private String vehicleBelongCityCode;

    @ApiModelProperty(value = "所属城市名称")
    private String vehicleBelongCityName;

    @ApiModelProperty(value = "所属企业客户ID")
    private Integer vehicleCompanyId;

    @ApiModelProperty(value = "所属企业客户")
    private String vehicleCompanyName;

    @ApiModelProperty(value = "车辆所在部门ID；实际使用车辆部门")
    private Integer vehicleStructId;

    @ApiModelProperty(value = "车辆所在部门名称；实际使用车辆部门")
    private String vehicleStructName;

    @ApiModelProperty(value = "是否自有车辆；1：首汽自有；2：企业自有车辆;3:员工车辆")
    private Byte vehicleSelfOwned;

    @ApiModelProperty(value = "车辆运营状态；1:无任务；2：任务中；3：停运")
    private Byte vehicleWorkingStatus;

    @ApiModelProperty(value = "车辆所属部门ID；车辆所有权部门")
    private Integer vehicleOwnStructId;

    @ApiModelProperty(value = "车辆所属部门名称；车辆所有权部门")
    private String vehicleOwnStructName;

    @ApiModelProperty(value = "运营组织机构所在单位名")
    private String vehicleBelongStructName;

    @ApiModelProperty(value = "运营组织机构所在单位编码")
    private String vehicleBelongStructCode;

    @ApiModelProperty(value = "机动车所有人编码(资产组织机构)")
    private String vehicleBelongBussCode;

    @ApiModelProperty(value = "机动车所有人名称(资产组织机构)")
    private String vehicleBelongBussName;

    @ApiModelProperty(value = "运营组织机构所属单位名")
    private String vehicleOperateStructName;

    @ApiModelProperty(value = "运营组织机构所属单位编码")
    private String vehicleOperateStructCode;

    @ApiModelProperty(value = "使用资产组织机构名称")
    private String vehicleOperateBussName;

    @ApiModelProperty(value = "使用资产组织机构编码")
    private String vehicleOperateBussCode;

    @ApiModelProperty(value = "创建人ID")
    private Integer vehicleCreatorId;

    @ApiModelProperty(value = "创建人名称")
    private String vehicleCreatorName;

    @ApiModelProperty(value = "所属首汽城市编码")
    private String vehicleAssetCityCode;

    @ApiModelProperty(value = "所属首汽城市名称")
    private String vehicleAssetCityName;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "所属订单申请编号")
    private String orderApplyNo;

    @ApiModelProperty(value = "订单所属企业")
    private Integer orderCompanyId;

    @ApiModelProperty(value = "所属组织结构ID")
    private Integer orderStructId;

    @ApiModelProperty(value = "下单人所属部门名称")
    private String orderStructName;

    @ApiModelProperty(value = "订单类型：1：内部用车；2：外部订单；3：商务用车；4：短租；5：约车；6：国际租车；7:私车公用")
    private Byte orderType;

    @ApiModelProperty(value = "订单类型：1：内部用车；2：外部订单；3：商务用车；4：短租；5：约车；6：国际租车；7:私车公用")
    private String orderTypeName;

    @ApiModelProperty(value = "下单人ID")
    private Integer orderCustomerId;

    @ApiModelProperty(value = "下单人姓名")
    private String orderCustomerName;

    @ApiModelProperty(value = "下单人电话")
    private String orderCustomerMobile;

    @ApiModelProperty(value = "订单预约开始时间")
    private Date orderBookingStartTime;

    @ApiModelProperty(value = "订单预约结束时间")
    private Date orderBookingEndTime;

    @ApiModelProperty(value = "订单司机ID")
    private Long orderDriverId;

    @ApiModelProperty(value = "订单司机姓名")
    private String orderDriverName;

    @ApiModelProperty(value = "订单司机手机号")
    private String orderDriverPhone;

    @ApiModelProperty(value = "用车备注")
    private String orderDetail;

    @ApiModelProperty(value = "电子围栏")
    private Integer fenceId;

    @ApiModelProperty(value = "电子围栏名称")
    private String fenceName;

    @ApiModelProperty(value = "城市CODE")
    private Integer fenceCityCode;

    @ApiModelProperty(value = "城市名称")
    private String fenceCityName;

    @ApiModelProperty(value = "报警类型；1：出栏报警；2：入栏报警3：超速报警4:出栏记录")
    private Byte fenceWarnType;

    @ApiModelProperty(value = "报警类型；1：出栏报警；2：入栏报警3：超速报警4:出栏记录")
    private String fenceWarnTypeName;

    @ApiModelProperty(value = "围栏地址关键字")
    private String fenceAddress;

    @ApiModelProperty(value = "申请单关联备注")
    private String orderAssociateRemark;

    @ApiModelProperty(value = "按钮控制1.关联申请单2.废除")
    private String buttonTypes;

    @ApiModelProperty(value = "SIM卡号")
    private String vehicleSimNo;

    @ApiModelProperty(value = "设备编号")
    private String vehicleDeviceNo;

    @ApiModelProperty(value = "设备类型；1：有线；2：无线")
    private Byte vehicleDeviceType;

    @ApiModelProperty(value = "乘车人")
    private OrderApplyPassengerDTO[] passengers;

    @ApiModelProperty(value = "目的地")
    private OrderApplyDestinationDTO[] destinations;

}
