package com.izu.mrcar.order.dto.businessOrder;

import com.izu.mrcar.order.consts.OrderEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @description: 行程报表车辆维度
 * @author: hxc
 * @create: 2021-04-24 14:39
 **/
@Data
public class ApplyOrderStatementVehicleDTO {

    @ApiModelProperty(value = "车牌号")
    private String vehicleLicense;
    @ApiModelProperty(value = "所在城市")
    private String cityName;
    @ApiModelProperty(value = "所属部门")
    private String structName;
    @ApiModelProperty(value = "用车类型编码",example = "1")
    private Byte orderType;
    @ApiModelProperty(value = "用车类型",example = "内部用车")
    private String orderTypeName;
    @ApiModelProperty(value = "总金额",example = "1.11")
    private BigDecimal totalAmount;
    @ApiModelProperty(value = "订单数",example = "12")
    private Integer orderCount;
    @ApiModelProperty(value = "总里程",example = "11.11")
    private BigDecimal totalMileage;
    @ApiModelProperty(value = "订单总里程",example = "1.22")
    private BigDecimal totalOrderMileage;
    @ApiModelProperty(value = "订单总时长小时",example = "1")
    private BigDecimal totalTime;
    @ApiModelProperty(value = "企业名称")
    private Integer companyId;
    @ApiModelProperty(value = "企业名称")
    private String companyName;

    public void setOrderType(Byte orderType) {
        this.orderType = orderType;
        this.orderTypeName = OrderEnum.OrderType.getByValue(orderType).text();
    }
}
