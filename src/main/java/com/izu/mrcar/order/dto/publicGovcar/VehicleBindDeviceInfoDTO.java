package com.izu.mrcar.order.dto.publicGovcar;

import com.izu.mrcar.iot.iotEnum.DeviceBelongTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/10/28 13:44
 * 车辆绑定设备信息
 */
@Data
public class VehicleBindDeviceInfoDTO {

    @ApiModelProperty(value = "SIM卡号")
    private String simNo;

    @ApiModelProperty(value = "设备编号")
    private String deviceNo;

    @ApiModelProperty(value = "车架号")
    private String vehicleVin;

    @ApiModelProperty(value = "车牌号")
    private String vehicleLicense;

    @ApiModelProperty(value = "设备类型名称；1：有线；2：无线", hidden = true)
    private String deviceTypeName;

    private Integer companyId;

    /** 型号id **/
    private Integer modelId;

    /** 型号名称 **/
    @ApiModelProperty(value = "设备型号")
    private String modelName;


    /** 厂商code码 **/
    private String manfactCode;

    /** 厂商名称 **/
    @ApiModelProperty(value = "设备厂商")
    private String manfactName;


    private Byte deviceBelongType;

    /** 设备归属类型，1：首汽设备，2：自有设备 **/
    @ApiModelProperty(value = "设备归属")
    private String deviceBelongTypeName;



}
