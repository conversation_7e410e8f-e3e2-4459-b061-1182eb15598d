package com.izu.mrcar.order.dto.businessOrder;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: 调价信息
 * @date 2024/3/31 17:42
 */
@Data
public class BillAdjustPriceLogDTO {

    /**
     * 主键ID id
     */
    @ApiModelProperty("主键id")
    private Long id;

    /**
     * 行程编号 order_apply_no
     */
    @ApiModelProperty("行程编号")
    private String orderApplyNo;

    /**
     * 子行程编号 order_no
     */
    @ApiModelProperty("子行程编号")
    private String orderNo;

    /**
     * 减免金额 reduction_amount
     */
    private BigDecimal reductionAmount;

    /**
     * 其他费用 other_amount
     */
    private BigDecimal otherAmount;

    /**
     * 订单调价原因 price_explain
     */
    @ApiModelProperty("调价原因")
    private String priceExplain;

    /**
     * 操作前应收金额 before_should_pay_amount
     */
    @ApiModelProperty("操作前应收金额")
    private BigDecimal beforeShouldPayAmount;

    /**
     * 操作后应收价格 after_should_pay_amount
     */
    @ApiModelProperty("操作后应收价格")
    private BigDecimal afterShouldPayAmount;

    /**
     * 创建人ID creter_id
     */
    private Integer creterId;

    /**
     * 创建人姓名 creter_name
     */
    @ApiModelProperty("创建人姓名")
    private String creterName;

    /**
     * 创建时间 create_date
     */
    @ApiModelProperty("创建时间")
    private Date createDate;


    @ApiModelProperty("创建人电话")
    private String creterPhone;
}
