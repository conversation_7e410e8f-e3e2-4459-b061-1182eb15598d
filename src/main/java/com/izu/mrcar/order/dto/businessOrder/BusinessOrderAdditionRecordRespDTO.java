package com.izu.mrcar.order.dto.businessOrder;


import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.izu.mrcar.order.consts.DriverProviderEnum;
import com.izu.mrcar.order.consts.ServiceProviderEnum;
import com.izu.mrcar.order.consts.VehicleProviderEnum;
import com.izu.mrcar.order.dto.common.FileInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@ApiModel("商务车补录订单返回结果")
@ColumnWidth(25) // 导出默认列宽度
public class BusinessOrderAdditionRecordRespDTO {

    /**
     * 记录表主键
     **/
    @ApiModelProperty("记录表主键")
    @ExcelIgnore
    private Integer recordId;

    /**
     * 订单编号
     **/
    @ApiModelProperty("订单编号")
    @ExcelProperty(value = "订单编号",index = 0)
    private String orderCode;

    /**
     * 订单状态;1-已保存;2-已结算;4-已废除
     **/
    @ApiModelProperty("订单状态;1-已保存;2-已结算;4-已废除")
    @ExcelIgnore
    private Integer orderStatus;

    @ApiModelProperty("订单状态描述;1-已保存;2-已结算;4-已废除")
    @ExcelProperty(value = "订单状态",index = 1)
    private String orderStatusDesc;

    /**
     * 客户编码
     **/
    @ApiModelProperty("客户编码")
    @ExcelIgnore
    private String customerCode;

    /**
     * 客户名称
     **/
    @ApiModelProperty("客户名称")
    @ExcelProperty(value = "客户名称",index = 11)
    private String customerName;

    /**
     * 我司签约主体的社会信用代码
     **/
    @ApiModelProperty("我司签约主体的社会信用代码")
    @ExcelIgnore
    private String ourSocialCreditCode;

    /**
     * 我司签约主体名称
     **/
    @ApiModelProperty("我司签约主体名称")
    @ExcelProperty(value = "我司签约主体",index = 12)
    private String ourContractingEntity;

    /**
     * 车牌号
     **/
    @ApiModelProperty("车牌号")
    @ExcelProperty(value = "车牌号",index = 5)
    private String vehicleLicense;

    /**
     * 车辆对应的车型名称
     **/
    @ApiModelProperty("车辆对应的车型名称")
    @ExcelIgnore
    private String vehicleModelName;

    /**
     * 车辆对应的车型编码
     **/
    @ApiModelProperty("车辆对应的车型编码")
    @ExcelIgnore
    private String vehicleModelCode;

    /**
     * 机动车使用人编码
     **/
    @ApiModelProperty("机动车使用人编码")
    @ExcelIgnore
    private String vehicleOperateBussCode;

    /**
     * 机动车使用人名称
     **/
    @ApiModelProperty("机动车使用人名称")
    @ExcelProperty(value = "机动车使用人",index = 6)
    private String vehicleOperateBussName;

    /**
     * 机动车所有人编码
     **/
    @ApiModelProperty("机动车所有人编码")
    @ExcelIgnore
    private String vehicleBelongBussCode;

    /**
     * 机动车使用人对应的城市编码
     **/
    @ApiModelProperty("机动车使用人对应的城市编码")
    @ExcelIgnore
    private String vehicleOperateCityCode;

    /**
     * 机动车使用人对应的城市名称
     **/
    @ApiModelProperty("机动车使用人对应的城市名称")
    @ExcelIgnore
    private String vehicleOperateCityName;


    /**
     * 机动车所有人名称
     **/
    @ApiModelProperty("机动车所有人名称")
    @ExcelProperty(value = "机动车所有人",index = 7)
    private String vehicleBelongBussName;

    /** 1-首汽车辆; 2-外部车辆 **/
    @ApiModelProperty("1-首汽车辆; 2-外部车辆")
    @ExcelIgnore
    private Byte vehicleSelfOwned;

    /** 1-首汽车辆; 2-外部车辆 **/
    @ApiModelProperty("1-首汽车辆; 2-外部车辆")
    @ExcelProperty(value = "车辆类型",index = 8)
    private String vehicleSelfOwnedDesc;

    @ApiModelProperty("司机手机号")
    @ExcelProperty(value = "司机手机号",index = 10)
    private String driverMobile;

    /**
     * 司机姓名
     **/
    @ApiModelProperty("司机姓名")
    @ExcelProperty(value = "司机姓名",index = 9)
    private String driverName;

    /**
     * 用车开始日期
     **/
    @ApiModelProperty("用车开始日期")
    @ExcelProperty(value = "开始日期",index = 14)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date startDate;

    /**
     * 用车结束日期
     **/
    @ApiModelProperty("用车结束日期")
    @ExcelProperty(value = "结束日期",index = 15)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date endDate;

    /**
     * 订单金额
     **/
    @ApiModelProperty("订单金额")
    @ExcelProperty(value = "金额",index = 16)
    private BigDecimal amount;

    /**
     * 税率(小数)
     **/
    @ApiModelProperty("税率(小数) ")
    @ExcelProperty(value = "税率(小数)",index = 13)
    private BigDecimal taxRate;

    /**
     * 账期(天数)
     **/
    @ApiModelProperty("账期(天数)")
    @ExcelProperty(value = "账期(天数)",index = 17)
    private Integer paymentDays;

    /**
     * 备注
     **/
    @ApiModelProperty("备注")
    @ExcelProperty(value = "备注",index = 18)
    private String remark;

    /**
     * 创建人编码
     **/
    @ApiModelProperty("创建人编码")
    @ExcelIgnore
    private String createCode;

    /**
     * 创建人名称
     **/
    @ApiModelProperty("创建人名称")
    @ExcelProperty(value = "创建人名称",index = 3)
    private String createName;

    /**
     * 创建人所属部门编码
     */
    @ApiModelProperty("创建人所属部门编码")
    @ExcelIgnore
    private String createStructCode;

    /**
     * 创建人所属部门名称
     */
    @ApiModelProperty("创建人所属部门名称")
    @ExcelProperty(value = "创建人部门",index = 4)
    private String createStructName;

    /**
     * 修改人编码
     **/
    @ApiModelProperty("修改人编码")
    @ExcelIgnore
    private String updateCode;

    /**
     * 修改人姓名
     **/
    @ApiModelProperty("修改人姓名")
    @ExcelIgnore
    private String updateName;

    /**
     *
     **/
    @ApiModelProperty("创建时间")
    @ExcelProperty(value = "创建时间",index = 2)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     *
     **/
    @ApiModelProperty("修改时间")
    @ExcelIgnore
    private Date updateTime;

    @ApiModelProperty("合同文件列表")
    @ExcelIgnore
    private List<FileInfoDTO> contractFileList;


    @ApiModelProperty("我司签约主体适用的税率列表字符串")
    private String taxListStr;

    @ApiModelProperty(value = "供应商编码",required = true)
    private String supplierCode;
    @ApiModelProperty(value = "供应商名称",required = true)
    private String supplierName;

    @ApiModelProperty(value = "应支供应商金额")
    private BigDecimal shouldSupplierAmount;
    @ApiModelProperty(value = "服务方")
    private String serviceProvider;

    @ApiModelProperty(value = "服务方名称")
    private String serviceProviderName;

    private Byte vehicleProvider;
    @ApiModelProperty(value = "供应商税率", example = "25",required = true)
    private BigDecimal supplierRate;
    @ApiModelProperty("是否被退回过")
    private Boolean isRollBack;

    /**
     * 司机提供方;1-首汽车辆;2-三方车辆 driver_provider
     */
    private Byte driverProvider;

    public String getServiceProvider() {
        if(this.vehicleProvider==null||this.driverProvider==null||this.vehicleProvider==0||this.driverProvider==0){
            return "";
        }
        //首汽车辆+首汽司机=首汽服务
        if(VehicleProviderEnum.SQ.equals(VehicleProviderEnum.fromValue(this.vehicleProvider))&&(DriverProviderEnum.SQ.equals(DriverProviderEnum.fromValue(this.driverProvider)))){
            serviceProvider= ServiceProviderEnum.VEHICLE_DRIVER.getType().toString();
            //首汽车辆+三方司机
        }else if(VehicleProviderEnum.SQ.equals(VehicleProviderEnum.fromValue(this.vehicleProvider))&&(DriverProviderEnum.THIRD_PARTY.equals(DriverProviderEnum.fromValue(this.driverProvider)))){
            serviceProvider= ServiceProviderEnum.VEHICLE_TRIPARTITE.getType().toString();
            //首汽司机+三方车辆
        }else if(VehicleProviderEnum.THIRD_PARTY.equals(VehicleProviderEnum.fromValue(this.vehicleProvider))&&(DriverProviderEnum.SQ.equals(DriverProviderEnum.fromValue(this.driverProvider)))){
            serviceProvider= ServiceProviderEnum.TRIPARTITE_DRIVER.getType().toString();
            //三方车辆+三方司机 = 三方服务
        }else if(VehicleProviderEnum.THIRD_PARTY.equals(VehicleProviderEnum.fromValue(this.vehicleProvider))&&(DriverProviderEnum.THIRD_PARTY.equals(DriverProviderEnum.fromValue(this.driverProvider)))){
            serviceProvider= ServiceProviderEnum.TRIPARTITE_TRIPARTITE.getType().toString();
        }
        return serviceProvider;
    }


    public String getServiceProviderName() {
        if(this.vehicleProvider==null||this.driverProvider==null||this.vehicleProvider==0||this.driverProvider==0){
            return "";
        }
        //首汽车辆+首汽司机=首汽服务
        if(VehicleProviderEnum.SQ.equals(VehicleProviderEnum.fromValue(this.vehicleProvider))&&(DriverProviderEnum.SQ.equals(DriverProviderEnum.fromValue(this.driverProvider)))){
            serviceProviderName= ServiceProviderEnum.VEHICLE_DRIVER.getDesc();
            //首汽车辆+三方司机
        }else if(VehicleProviderEnum.SQ.equals(VehicleProviderEnum.fromValue(this.vehicleProvider))&&(DriverProviderEnum.THIRD_PARTY.equals(DriverProviderEnum.fromValue(this.driverProvider)))){
            serviceProviderName= ServiceProviderEnum.VEHICLE_TRIPARTITE.getDesc();
            //首汽司机+三方车辆
        }else if(VehicleProviderEnum.THIRD_PARTY.equals(VehicleProviderEnum.fromValue(this.vehicleProvider))&&(DriverProviderEnum.SQ.equals(DriverProviderEnum.fromValue(this.driverProvider)))){
            serviceProviderName= ServiceProviderEnum.TRIPARTITE_DRIVER.getDesc();
            //三方车辆+三方司机 = 三方服务
        }else if(VehicleProviderEnum.THIRD_PARTY.equals(VehicleProviderEnum.fromValue(this.vehicleProvider))&&(DriverProviderEnum.THIRD_PARTY.equals(DriverProviderEnum.fromValue(this.driverProvider)))){
            serviceProviderName= ServiceProviderEnum.TRIPARTITE_TRIPARTITE.getDesc();
        }
        return serviceProviderName;
    }


}
