package com.izu.mrcar.order.dto.publicGovcar.app.req;

import com.izu.business.dto.common.PageParamDTO;
import com.izu.mrcar.order.dto.temporalSharedVehicle.QueryPageBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/15 19:59
 */
@ApiModel("公务用车对公订单结束行程")
@Data
public class AppPublicGovCarOrderListReqDTO extends QueryPageBaseDTO {

    @ApiModelProperty(value = "订单类型  1-办公用车，2-无任务用车", required = true)
    private Integer orderType;

    @ApiModelProperty(value = "展示状态(订单状态和审批状态的汇总):0-无审批 1-待审批,2-审批撤回 3-审批通过, 4-审批驳回 5,审批取消  6-空；7-待出发 8-用车中 9-待核实  10-已完成 11-已取消")
    private Byte orderApprovalStatus;

    @ApiModelProperty(value = "订单状态", hidden = true)
    private Byte orderStatus;


    @ApiModelProperty(value = "订单状态", hidden = true)
    private List<Byte> orderStatusList;


    @ApiModelProperty(value = "审批状态", hidden = true)
    private Byte approvalStatus;

    @ApiModelProperty(value = "审批状态", hidden = true)
    private List<Byte> approvalStatusList;


    @ApiModelProperty(value = "入口类型 1-订单管理 2-我的订单 3-无任务订单", required = true)
    private Integer entryType;
}
