package com.izu.mrcar.order.dto.businessOrder;

import com.izu.mrcar.order.consts.OrderEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
* @Description: 司机行程报表
* @author: hxc
* @Date: 2021/7/14
**/
@Data
public class ApplyOrderStatementDriverDTO {

    private String assignDriverName;
    private String assignDriverPhone;
    private Integer motorcadeId;
    private String motorcadeName;
    private Byte orderType;
    private String orderTypeName;
    private BigDecimal totalAmount;
    private Integer orderCount;
    private BigDecimal attachFee;
    private BigDecimal totalMileage;
    private BigDecimal totalTime;
    @ApiModelProperty(value = "企业名称")
    private Integer companyId;

    @ApiModelProperty(value = "企业名称")
    private String companyName;

    public void setOrderType(Byte orderType) {
        this.orderType = orderType;
        OrderEnum.OrderType type = OrderEnum.OrderType.getByValue(orderType);
        this.orderTypeName = type != null ? type.text() : "";
    }
}
