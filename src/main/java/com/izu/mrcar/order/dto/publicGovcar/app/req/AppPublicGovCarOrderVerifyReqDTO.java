package com.izu.mrcar.order.dto.publicGovcar.app.req;

import com.izu.mrcar.order.dto.common.BaseDTO;
import com.izu.mrcar.order.dto.publicGovcar.AppPublicGovCarCreateOrderReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/15 19:59
 */
@ApiModel("公务用车对公订单核实")
@Data
public class AppPublicGovCarOrderVerifyReqDTO extends BaseDTO {

    @ApiModelProperty(value = "订单号", required = true)
    private String orderNo;


    @ApiModelProperty(value = "乘车人信息", required = true)
    @NotNull(message = "乘车人不能为空")
    List<AppPublicGovCarCreateOrderReqDTO.PassengersInfo> passengersInfoList;


    @ApiModelProperty(value = "司机id", required = true)
    @NotNull(message = "司机id不能为空")
    private Integer driverId;

    @ApiModelProperty(value = "司机code", required = true)
    @NotEmpty(message = "司机code不能为空")
    private String driverCode;

    @ApiModelProperty(value = "司机姓名", required = true)
    @NotEmpty(message = "司机姓名不能为空")
    private String driverName;

    @ApiModelProperty(value = "司机电话", required = true)
    @NotEmpty(message = "司机电话不能为空")
    private String driverMobile;

    @ApiModelProperty(value = "用车事由", required = true)
    @NotEmpty(message = "用车事由不能为空")
    private String carUseReason;

    @ApiModelProperty(value = "备注")
    private String orderUserMemo;

    @ApiModelProperty(value = "司机部门id", required = true)
    private Integer driverStructId;

    @ApiModelProperty(value = "司机部门Code", required = true)
    private String driverStructCode;

    @ApiModelProperty(value = "司机部门名称", required = true)
    private String driverStructName;


}
