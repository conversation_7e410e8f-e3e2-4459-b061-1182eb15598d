package com.izu.mrcar.order.dto.businessOrder;


import com.izu.mrcar.order.dto.common.BaseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 废除一个单据请求参数
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class BusinessOrderAdditionRecordStatusReqDTO extends BaseDTO {

    @ApiModelProperty(value = "订单记录ID不能为空", required = true)
    private Integer recordId;

}
