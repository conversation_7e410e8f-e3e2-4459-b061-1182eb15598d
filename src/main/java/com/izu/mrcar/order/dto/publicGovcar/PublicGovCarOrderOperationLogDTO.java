package com.izu.mrcar.order.dto.publicGovcar;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
/**
 * @ClassName PublicGovCarOrderOperationLogDTO
 * <AUTHOR>
 * @Date 2024/8/16 14:07
 * @Version 1.0
 */

@ApiModel("详情-操作日志")
@Data
public class PublicGovCarOrderOperationLogDTO {

    /** 主键ID **/
    private Long id;

    @ApiModelProperty(value = "操作项")
    private String operation;

    @ApiModelProperty(value = "订单状态")
    private Integer orderStatus;

    @ApiModelProperty(value = "订单状态")
    private String orderStatusStr;

    @ApiModelProperty(value = "操作人")
    private String operatorName;

    @ApiModelProperty(value = "操作时间")
    private Date operationTime;
}