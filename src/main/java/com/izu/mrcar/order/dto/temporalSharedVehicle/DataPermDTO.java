package com.izu.mrcar.order.dto.temporalSharedVehicle;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> dongxiya 2023/11/22 13:50
 */
@Data
public class DataPermDTO {
    @ApiModelProperty(value = "系统类型", hidden = true)
    private Byte systemType;
    @ApiModelProperty(value = "登录人企业id", hidden = true)
    private Integer companyId;
    @ApiModelProperty(value = "登录人企业code", hidden = true)
    private String companyCode;
    @ApiModelProperty(value = "登录人企业name", hidden = true)
    private String companyName;
    @ApiModelProperty(value = "登录人id", hidden = true)
    private Integer staffId;
    @ApiModelProperty(value = "登录人司机表id", hidden = true)
    private Integer driverId;
    @ApiModelProperty(value = "登录人code", hidden = true)
    private String staffCode;
    @ApiModelProperty(value = "登录人name", hidden = true)
    private String staffName;
    @ApiModelProperty(value = "数据权限类型", hidden = true)
    private Byte dataPermType;
    @ApiModelProperty(value = "具体需要查询的权限编码集合", hidden = true)
    private Set<String> dataCodeSet;
    @ApiModelProperty(value = "登录人的权限不是空值", hidden = true)
    private Boolean dataPermIsNotNull;

    @ApiModelProperty(value = "用户登录部门编码（运营）", hidden = true)
    private List<String> loginStructCodes;
}
