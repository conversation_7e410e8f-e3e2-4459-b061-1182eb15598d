package com.izu.mrcar.order.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

/**
 * 通用的登录用户信息
 *
 * <AUTHOR>
 */
@Data
public class LoginUserInfo {

    @ApiModelProperty("登录用户ID")
    private Integer loginUserId;

    @ApiModelProperty("登录用户名称")
    private String loginUserName;

    @ApiModelProperty("综合管理平台员工ID")
    private Integer mgtStaffId;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("权限部门（分公司）Code集合")
    private Set<String> permDeptCodes;
}
