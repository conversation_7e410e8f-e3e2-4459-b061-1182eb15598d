package com.izu.mrcar.order.dto.businessOrder;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel("商务车订单补录记录修改参数")
public class BusinessOrderAdditionRecordUpdateReqDTO extends BusinessOrderAdditionRecordSaveReqDTO{

    @ApiModelProperty(value = "订单记录ID不能为空", required = true)
    private Integer recordId;

}
