package com.izu.mrcar.order.dto.publicGovcar;
import com.izu.mrcar.iot.dto.CarGpsFenceDTO;
import com.izu.mrcar.iot.dto.warn.OfficialVehicleWarnRecordDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 对公公务用车订单列表
 * @date 2024/8/16 9:45
 */
@ApiModel("订单列表")
@Data
public class PublicGovCarOrderDetailInfoRespDTO {

    @ApiModelProperty(value = "订单基本信息")
    private PublicGovCarOrderInfoDTO orderInfo;

    @ApiModelProperty(value = "用车信息")
    private PublicGovVehicleInfoDTO vehicleInfo;

    @ApiModelProperty(value = "操作日志")
    private List<PublicGovCarOrderOperationLogDTO> operationLogList;

    @ApiModelProperty(value = "单日用车时长")
    private List<PublicGovCarOrderSubDailyDTO> orderSubDailyList;

    @ApiModelProperty(value = "订单报警记录")
    private OfficialVehicleWarnRecordDTO warnRecord;

    @ApiModelProperty(value = "围栏信息")
    private List<CarGpsFenceDTO> gpsFenceList;

    @ApiModelProperty(value = "设备信息")
    private List<VehicleBindDeviceInfoDTO> deviceList;

    @ApiModelProperty(value = "工作流实例id")
    private String processInstanceId;

}
