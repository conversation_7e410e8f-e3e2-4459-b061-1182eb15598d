package com.izu.mrcar.order.dto.publicGovcar;

import io.swagger.annotations.ApiModel;
import lombok.Data;

@ApiModel("订单列表详情附加实体:用于修改行程")
@Data
public class AppPublicGovCarOrderPassengersInfoRespDTO {

    private String passengerCode;             // 乘车人编码
    private Integer passengerId;                 // 乘车人ID
    private String passengerMobile;          // 乘车人电话
    private String passengerName;             // 乘车人姓名
    private String passengerStructCode;  // 乘车人部门Code
    private Integer passengerStructId;      // 乘车人部门id
    private String passengerStructName;  // 乘车人部门名称
}
