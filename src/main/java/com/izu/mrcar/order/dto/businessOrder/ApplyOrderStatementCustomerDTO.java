package com.izu.mrcar.order.dto.businessOrder;

import com.izu.mrcar.order.consts.OrderEnum;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @description: 行程报表客户维度
 * @author: hxc
 * @create: 2021-04-24 14:39
 **/
@Data
public class ApplyOrderStatementCustomerDTO {

    private String customerName;
    private String customerMobile;
    private String companyName;
    private Integer companyId;
    private Byte orderType;
    private String orderTypeName;
    private String departmentName;
    private BigDecimal totalAmount;
    private Integer orderCount;
    private Integer vehicleCount;
    private BigDecimal totalMileage;
    private BigDecimal totalTime;

    public void setOrderType(Byte orderType) {
        this.orderType = orderType;
        this.orderTypeName = OrderEnum.OrderType.getByValue(orderType).text();
    }
}
