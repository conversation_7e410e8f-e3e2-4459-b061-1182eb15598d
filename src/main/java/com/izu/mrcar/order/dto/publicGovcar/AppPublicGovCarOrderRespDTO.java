package com.izu.mrcar.order.dto.publicGovcar;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @description: 对公公务用车创建订单返回参数（App）
 * @date 2024/8/15 9:45
 */
@ApiModel("订单列表返回参数")
@Data
public class AppPublicGovCarOrderRespDTO {

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "订单类型：1-办公用车，2-无任务用车")
    private Integer orderType;

    @ApiModelProperty(value = "订单类型：1-办公用车，2-无任务用车")
    private String orderTypeStr;

    @ApiModelProperty(value = "订单状态(0-空；1-待出发 10-用车中 20-待核实 21-已核实 30-已完成 100-已取消)")
    private Byte orderStatus;

    @ApiModelProperty(value = "订单状态(0-空；1-待出发 10-用车中 20-待核实 21-已核实 30-已完成 100-已取消)")
    private String orderStatusStr;

    @ApiModelProperty(value = "审批状态:0-无审批 1-待审批,2-审批撤回 3-审批通过, 4-审批驳回")
    private Byte approvalStatus;

    @ApiModelProperty(value = "审批状态:0-无审批 1-待审批,2-审批撤回 3-审批通过, 4-审批驳回")
    private String approvalStatusStr;

    @ApiModelProperty(value = "展示状态:1-待审批,2-审批撤回 3-审批通过, 4-审批驳回  7-待出发 8-用车中 9-待核实 10-已完成  11-已取消")
    private Byte orderApprovalStatus;

    @ApiModelProperty(value = "展示状态:0-无审批 1-待审批,2-审批撤回 3-审批通过, 4-审批驳回 6-空 7-待出发 8-用车中 9-待核实 10-已完成  11-已取消")
    private String orderApprovalStatusStr;

    @ApiModelProperty(value = "下单人信息 接口拼好返回")
    private String createUserInfo;

    @ApiModelProperty(value = "乘车人信息 接口拼好返回")
    private String passengerUserInfo;

    @ApiModelProperty(value = "司机信息 接口拼好返回")
    private String driverInfo;

    @ApiModelProperty(value = "车牌号")
    private String vehicleLicense;

    @ApiModelProperty(value = "品牌")
    private String vehicleBrandName;

    @ApiModelProperty(value = "车型")
    private String vehicleModelName;

    @ApiModelProperty(value = "车辆部门名称")
    private String vehicleStructName;

    @ApiModelProperty(value = "车辆投放类型 1-平台共享车，2-定点投放")
    private Integer deploymentMode;

    @ApiModelProperty(value = "车辆投放类型 1-平台共享车，2-定点投放")
    private String deploymentModeStr;

    @ApiModelProperty(value = "出发地")
    private String estimatedDepartureLocation;

    @ApiModelProperty(value = "目的地")
    private String estimatedDestinationLocation;

    @ApiModelProperty(value = "预计开始时间")
    private String expectedPickupTime;

    @ApiModelProperty(value = "预计结束时间")
    private String expectedReturnTime;

    @ApiModelProperty(value = "车架号,获取围栏信息时使用")
    private String vehicleVin;

    @ApiModelProperty(value = "关联的报警编码 调用根据报警编号查询报警记录接口使用,最终目的用于调用行车轨迹")
    private String alarmCode;

    @ApiModelProperty(value = "用于修改行程的参数")
    private AppPublicGovCarOrderModifyTravelDTO modifyTravelDTO;

    @ApiModelProperty(value = "无任务状态 false:未处理 true:已处理 ")
    private Boolean noTaskOrderHandleStatus;

    private Integer verifyStatus;
}
