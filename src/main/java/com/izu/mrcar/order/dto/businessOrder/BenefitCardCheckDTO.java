package com.izu.mrcar.order.dto.businessOrder;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @description: 权益卡校验
 * @date 2023/12/5 10:44
 */
@Data
public class BenefitCardCheckDTO {

    @ApiModelProperty(value = "权益卡激活码",required = true)
    @NotBlank(message = "权益卡激活码不能为空")
    private String cardSecret;

    @ApiModelProperty(
            value = "订单类型",
            required = true,
            example = "1",
            notes = "订单类型：1：内部用车；2：商务用车；3:私车公用"
    )
    private @NotNull(
            message = "订单类型不能为空"
    ) Byte orderType;
    @ApiModelProperty(
            value = "服务类型",
            required = true,
            example = "1",
            notes = "半日、整日、接机、送机等"
    )
    private @NotBlank(
            message = "服务类型不能为空"
    ) String serviceCode;

    /** 排车、派车成功后的车级ID
     * 商务车二期之后不再需要校验车辆级别
     *  **/
    @ApiModelProperty("车级ID")
    @Deprecated
    private Byte assignCarlevelId;

    @ApiModelProperty("车级名称,快捷调度需要传")
    @Deprecated
    private String assignCarlevelName;

    @ApiModelProperty(
            value = "预约开始行程城市编码",
            required = true,
            example = "1101"
    )
    private @NotNull(
            message = "预约开始行程城市编码不能为空"
    ) Integer bookingStartCityCode;
    @ApiModelProperty(
            value = "预约开始行程城市名称",
            required = true,
            example = "北京"
    )
    private @NotBlank(
            message = "预约开始行程城市名称不能为空"
    ) String bookingStartCityName;

    private String test;

    private long time;
}
