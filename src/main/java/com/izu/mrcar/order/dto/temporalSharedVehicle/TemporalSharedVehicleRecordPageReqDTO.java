package com.izu.mrcar.order.dto.temporalSharedVehicle;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> dongxiya 2024/1/27 15:29
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TemporalSharedVehicleRecordPageReqDTO extends QueryPageBaseDTO{
    @ApiModelProperty(value = "报警编号(用车记录编号)")
    private String warnSn;
    @ApiModelProperty(value = "单据状态1-进行中2-已完成3-已废除")
    private List<Integer> recordStatuses;
    @ApiModelProperty(value = "车牌号码")
    private String vehicleLicense;
    @ApiModelProperty(value = "车架号")
    private String vehicleVin;
    @ApiModelProperty(value = "车辆所在企业")
    private Integer vehicleCompanyId;
    @ApiModelProperty(value = "车辆所在部门ID；实际使用车辆部门")
    private Integer vehicleStructId;
    @ApiModelProperty(value = "用车开始日期-开始")
    private Date warnStartBeginTime;
    @ApiModelProperty(value = "用车开始日期-结束")
    private Date warnStartEndTime;
    @ApiModelProperty(value = "用车时长-开始")
    private BigDecimal durationBegin;
    @ApiModelProperty(value = "用车时长-结束")
    private BigDecimal durationEnd;
    @ApiModelProperty(value = "用车时长反算用车开始时间最小值", hidden = true)
    private Date durationBeginMinTime;
    @ApiModelProperty(value = "用车时长反算用车开始时间最大值", hidden = true)
    private Date durationBeginMaxTime;
    @ApiModelProperty(value = "申请单编号")
    private String orderNo;
    @ApiModelProperty(value = "用车申请人")
    private String orderCustomerName;
    @ApiModelProperty(value = "申请人部门")
    private Integer orderStructId;
    @ApiModelProperty(value = "申请单关联关系1-未关联2-已关联")
    private Byte orderAssociateStatus;
    @ApiModelProperty(value = "排序字段1开始时间2时长3里程")
    private Byte sortField;
    @ApiModelProperty(value = "排序方式true正序false倒序")
    private Boolean sortFieldToAsc;

}
