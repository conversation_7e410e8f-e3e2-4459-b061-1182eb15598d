package com.izu.mrcar.order.dto.publicGovcar;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import izu.org.apache.poi.ss.usermodel.BorderStyle;
import izu.org.apache.poi.ss.usermodel.HorizontalAlignment;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@HeadStyle(
        fillForegroundColor = 22,
        fillBackgroundColor = 22
)
@ColumnWidth(20)
@ContentStyle(
        horizontalAlignment = HorizontalAlignment.CENTER,
        borderBottom = BorderStyle.THIN,
        borderLeft = BorderStyle.THIN,
        borderRight = BorderStyle.THIN,
        borderTop = BorderStyle.THIN,
        wrapped = true
)
@Data
@ApiModel("订单列表导出")
public class PublicGovCarOrderExportDTO {

    @ExcelProperty(value = "订单号")
    @ColumnWidth(value = 20)
    private String orderNo;

    @ExcelProperty(value = "订单类型")
    @ColumnWidth(value = 20)
    private String orderTypeStr;

    @ExcelProperty(value = "订单状态")
    @ColumnWidth(value = 20)
    private String orderStatusStr;

    @ExcelProperty(value = "审批状态")
    @ColumnWidth(value = 20)
    private String approvalStatusStr;



    @ExcelProperty(value = "下单人企业")
    @ColumnWidth(value = 20)
    private String createCompanyName;

    @ExcelProperty(value = "下单人部门")
    @ColumnWidth(value = 20)
    private String createStructName;

    @ExcelProperty(value = "下单人")
    @ColumnWidth(value = 20)
    private String createUserName;

    @ExcelProperty(value = "主用车人")
    @ColumnWidth(value = 20)
    private String passengerUserName;

    @ExcelProperty(value = "司机")
    @ColumnWidth(value = 20)
    private String driverName;

    @ExcelProperty(value = "车牌号")
    @ColumnWidth(value = 20)
    private String vehicleLicense;

    @ExcelProperty(value = "车型")
    @ColumnWidth(value = 20)
    private String vehicleModelName;

    @ExcelProperty(value = "车辆投放类型")
    @ColumnWidth(value = 20)
    private String deploymentModeStr;

    @ExcelProperty(value = "出发地")
    @ColumnWidth(value = 20)
    private String estimatedDepartureLocation;

    @ExcelProperty(value = "目的地")
    @ColumnWidth(value = 20)
    private String estimatedDestinationLocation;

    @ExcelProperty(value = "用车事由")
    @ColumnWidth(value = 20)
    private String carUseReason;

    @ExcelProperty(value = "用车备注")
    @ColumnWidth(value = 20)
    private String orderUserMemo;

    @ExcelProperty(value = "预计开始时间")
    @ColumnWidth(value = 20)
    private Date expectedPickupTime;

    @ExcelProperty(value = "预计结束时间")
    @ColumnWidth(value = 20)
    private Date expectedReturnTime;

    @ExcelProperty(value = "实际开始时间")
    @ColumnWidth(value = 20)
    private Date orderStartTime;

    @ExcelProperty(value = "实际结束时间")
    @ColumnWidth(value = 20)
    private Date orderEndTime;

    @ExcelProperty(value = "用车时长(小时)")
    @ColumnWidth(value = 20)
    private String userTime;

    @ExcelProperty(value = "里程数(km)")
    @ColumnWidth(value = 20)
    private BigDecimal totalMileage;

    @ExcelProperty(value = "下单时间")
    @ColumnWidth(value = 20)
    private Date createTime;

    @ExcelProperty(value = "费用归属部门")
    @ColumnWidth(value = 20)
    private String orderStructName;

    /**
     * 和费用归属部门是一个
     */
    @ExcelProperty(value = "用车人部门")
    @ColumnWidth(value = 20)
    private String vehicleUsageStructName;

    @ExcelProperty(value = "车辆所属部门")
    @ColumnWidth(value = 20)
    private String vehicleStructName;

    @ExcelProperty(value = "订单所属企业")
    @ColumnWidth(value = 20)
    private String orderCompanyName;

    @ExcelIgnore
    private String companyName;

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
        this.orderCompanyName = companyName;
    }

    @ExcelProperty(value = "出入栏总时长(小时)")
    @ColumnWidth(value = 20)
    private String outInFenceUseTime;

    @ExcelProperty(value = "出入栏总里程(km)")
    @ColumnWidth(value = 20)
    private BigDecimal outInFenceTotalMileage;


}