package com.izu.mrcar.order.dto.publicGovcar;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @ClassName PublicGovCarOrderInfo
 * <AUTHOR>
 * @Date 2024/8/16 13:41
 * @Version 1.0
 */

@ApiModel("详情-订单信息")
@Data
public class PublicGovCarOrderInfoDTO {

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "订单类型：1-办公用车，2-无任务用车")
    private Integer orderType;

    @ApiModelProperty(value = "订单类型：1-办公用车，2-无任务用车")
    private String orderTypeStr;

    @ApiModelProperty(value = "订单状态(0-空；1-待出发 10-用车中 20-已完成 100-已取消)")
    private Byte orderStatus;

    @ApiModelProperty(value = "订单状态(0-空；1-待出发 10-用车中 20-已完成 100-已取消)")
    private String orderStatusStr;

    @ApiModelProperty(value = "审批状态:0-无审批 1-待审批,2-审批撤回 3-审批通过, 4-审批驳回")
    private Byte approvalStatus;

    @ApiModelProperty(value = "审批状态:0-无审批 1-待审批,2-审批撤回 3-审批通过, 4-审批驳回")
    private String approvalStatusStr;

    @ApiModelProperty(value = "下单人企业")
    private String createCompanyName;

    @ApiModelProperty(value = "下单人部门")
    private String createStructName;

    @ApiModelProperty(value = "下单人")
    private String createUserName;

    @ApiModelProperty(value = "下单时间")
    private Date createTime;

    @ApiModelProperty(value = "用车人")
    private List<String> passengerUserList;

    @ApiModelProperty(value = "用车人部门")
    private List<String> passengerStructList;

    @ApiModelProperty(value = "车牌号")
    private String vehicleLicense;

    @ApiModelProperty(value = "出发地")
    private String estimatedDepartureLocation;

    @ApiModelProperty(value = "目的地")
    private String estimatedDestinationLocation;


    @ApiModelProperty(value = "预计开始时间")
    private Date expectedPickupTime;

    @ApiModelProperty(value = "预计结束时间")
    private Date expectedReturnTime;

    @ApiModelProperty(value = "实际开始时间")
    private Date orderStartTime;

    @ApiModelProperty(value = "实际结束时间")
    private Date orderEndTime;

    @ApiModelProperty(value = "用车事由")
    private String carUseReason;

    @ApiModelProperty(value = "用车备注")
    private String orderUserMemo;



}