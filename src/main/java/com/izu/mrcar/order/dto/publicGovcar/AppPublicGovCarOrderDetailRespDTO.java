package com.izu.mrcar.order.dto.publicGovcar;

import com.izu.mrcar.iot.dto.CarGpsFenceDTO;
import com.izu.mrcar.iot.dto.warn.OfficialVehicleWarnRecordDTO;
import com.izu.mrcar.iot.dto.warn.VehicleRealtimeStatusDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 对公公务用车创建订单返回参数（App）
 * @date 2024/8/15 9:45
 */
@ApiModel("APP订单详情返回参数")
@Data
public class AppPublicGovCarOrderDetailRespDTO {

    @ApiModelProperty(value = "车牌号")
    private String vehicleLicense;

    @ApiModelProperty(value = "车型")
    private String vehicleModelName;

    @ApiModelProperty(value = "车辆图片")
    private String vehicleImgUrl;

    @ApiModelProperty(value = "下单围栏")
    private String fenceName;

    @ApiModelProperty(value = "说明")
    private String tips;

    @ApiModelProperty(value = "预计开始时间")
    private String expectedPickupTime;

    @ApiModelProperty(value = "预计结束时间")
    private String expectedReturnTime;

    @ApiModelProperty(value = "核实时间")
    private Date verifierTime;

    @ApiModelProperty(value = "出发地")
    private String estimatedDepartureLocation;

    @ApiModelProperty(value = "目的地")
    private String estimatedDestinationLocation;

//    @ApiModelProperty(value = "地图")
//    private String vehicleModelName;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "取消时间")
    private Date cancellationTime;

    @ApiModelProperty(value = "审批时间")
    private Date approvalCompletedTime;

    @ApiModelProperty(value = "订单类型：1-办公用车，2-无任务用车")
    private Integer orderType;

    @ApiModelProperty(value = "订单类型：1-办公用车，2-无任务用车")
    private String orderTypeStr;

    @ApiModelProperty(value = "订单状态(0-空；1-待出发 10-用车中 20-待核实 30-已完成 100-已取消)")
    private Byte orderStatus;

    @ApiModelProperty(value = "订单状态(0-空；1-待出发 10-用车中 20-待核实 30-已完成 100-已取消)")
    private String orderStatusStr;

    @ApiModelProperty(value = "审批状态:0-无审批 1-待审批,2-审批撤回 3-审批通过, 4-审批驳回")
    private Byte approvalStatus;

    @ApiModelProperty(value = "审批状态:0-无审批 1-待审批,2-审批撤回 3-审批通过, 4-审批驳回")
    private String approvalStatusStr;

    @ApiModelProperty(value = "展示状态(订单状态和审批状态的汇总):0-无审批 1-待审批,2-审批撤回 3-审批通过, 4-审批驳回  6-空；7-待出发 8-用车中 9-待核实 10-已完成 10-已取消")
    private Byte orderApprovalStatus;

    @ApiModelProperty(value = "展示状态(订单状态和审批状态的汇总):0-无审批 1-待审批,2-审批撤回 3-审批通过, 4-审批驳回  6-空；7-待出发 8-用车中 9-待核实 10-已完成 10-已取消")
    private String orderApprovalStatusStr;

    @ApiModelProperty(value = "下单人信息 接口拼好返回")
    private String createUserInfo;

    @ApiModelProperty(value = "乘车人信息 接口拼好返回 名称/部门/手机号")
    private List<String> passengerUserInfo;

    @ApiModelProperty(value = "司机信息 接口拼好返回")
    private String driverInfo;

    @ApiModelProperty(value = "用车事由")
    private String carUseReason;

    @ApiModelProperty(value = "用车备注")
    private String orderUserMemo;

    @ApiModelProperty(value = "实际开始时间")
    private Date orderStartTime;

    @ApiModelProperty(value = "实际结束时间")
    private Date orderEndTime;

    @ApiModelProperty(value = "用车时长(小时) 年月日 xx分钟")
    private List<String> userTimeList;

    @ApiModelProperty(value = "车架号,获取围栏信息时使用")
    private String vehicleVin;

    @ApiModelProperty(value = "报警记录")
    private OfficialVehicleWarnRecordDTO warnRecordDTO;

    @ApiModelProperty(value = "实时围栏信息")
    private VehicleRealtimeStatusDTO vehicleRealtimeStatusDTO;

    @ApiModelProperty("快照围栏信息")
    private List<CarGpsFenceDTO> gpsFenceList;

    @ApiModelProperty(value = "无钥匙用车 0-否 1-是")
    private Byte noKeyUseCar;

    @ApiModelProperty(value = "用于修改行程的参数")
    private AppPublicGovCarOrderModifyTravelDTO modifyTravelDTO;

    @ApiModelProperty("是否展示围栏快照 0:不展示 1:展示")
    private Integer showSnapshotFence;

    @ApiModelProperty("是否展示实时围栏 0:不展示 1:展示")
    private Integer showRealFence;

    @ApiModelProperty("是否展示行驶轨迹 0:不展示 1:展示")
    private Integer showTrail;

    @ApiModelProperty("是否展示车辆定位 0:不展示 1:展示")
    private Integer showVehiclePoi;
}
