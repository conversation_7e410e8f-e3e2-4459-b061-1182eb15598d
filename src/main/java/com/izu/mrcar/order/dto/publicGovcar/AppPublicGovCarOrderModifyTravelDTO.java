package com.izu.mrcar.order.dto.publicGovcar;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


@ApiModel("订单列表详情附加实体:用于修改行程")
@Data
public class AppPublicGovCarOrderModifyTravelDTO {

    private String carUseReason;                       //   用车事由
    private Byte deploymentMod;                //   投放模式
    private String driverCode;                   // 	司机code
    private Integer driverId;                      // 	司机id
    private String driverMobile;                     // 	司机电话
    private String driverName;                       // 	司机姓名
    private String driverStructCode;                 // 	司机部门Code
    private Integer driverStructId;                  // 	司机部门id
    private String driverStructName;                 // 	司机部门名称
    private BigDecimal estimatedDepartureLatitude;       // 	预计出发地纬度
    private String estimatedDepartureLocation;       // 	预计出发地长地址
    private BigDecimal estimatedDepartureLongitude;     // 	预计出发地经度
    private String estimatedDepartureShortLocation;  // 	预计出发地短地址
    private BigDecimal estimatedDestinationLatitude;     // 	预计目的地纬度
    private String estimatedDestinationLocation;    // 	预计目的地长地址
    private BigDecimal estimatedDestinationLongitude;    // 	预计目的地经度
    private String estimatedDestinationShortLocation; // 	预计目的地短地址
    private Date expectedPickupTime;               // 	预计取车时间
    private Date expectedReturnTime;               // 	预计还车时间
    private Integer initialFenceId;                   //   下单时电子围栏快照ID
    private BigDecimal initialLatitude;                //   下单时车辆纬度
    private String initialLocation;                  //   下单时车辆逆地理位置
    private BigDecimal initialLongitude;                //   下单时车辆经度
    private String orderUserMemo;                    // 	备注
    private String vehicleBrandName;                //   车辆品牌
    private Integer vehicleId;                        // 	车辆ID
    private String vehicleLicense;                   // 	车辆车牌号
    private String vehicleModelName;                 //   车型名称
    private List<AppPublicGovCarOrderPassengersInfoRespDTO> passengersInfoList;  // 	乘车人信息
}
