package com.izu.mrcar.order.dto.businessOrder;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("商务车订单补录记录车辆信息")
public class BusinessOrderAdditionRecordVehicleRespDTO {

    /**
     * 车牌号码
     **/
    @ApiModelProperty("车牌号")
    private String vehicleLicense;

    /**
     * 是否自有车辆；1：首汽自有；2：企业自有车辆;3:员工车辆
     **/
    @ApiModelProperty("是否自有车辆；1：首汽自有；2-外部车辆")
    private Byte selfOwned;

    /**
     * 机动车所有人编码(资产组织机构)
     **/
    @ApiModelProperty("机动车所有人编码(资产组织机构)-selfOwned=1时存在")
    private String belongBussCode;

    /**
     * 机动车所有人名称(资产组织机构)
     **/
    @ApiModelProperty("机动车所有人名称(资产组织机构)-selfOwned=1时存在")
    private String belongBussName;
    /**
     * 使用资产组织机构名称
     **/
    @ApiModelProperty("使用资产组织机构名称-selfOwned=1时存在")
    private String operateBussName;
    /**
     * 使用资产组织机构编码
     **/
    @ApiModelProperty("使用资产组织机构编码-selfOwned=1时存在")
    private String operateBussCode;

    @ApiModelProperty("车型名称-selfOwned=1时存在")
    private String vehicleModelName;

    @ApiModelProperty("车型编码-selfOwned=1时存在")
    private String vehicleModelCode;


    /**
     * 车牌号码
     **/
    @ApiModelProperty("车辆id")
    private Integer vehicleId;
}
