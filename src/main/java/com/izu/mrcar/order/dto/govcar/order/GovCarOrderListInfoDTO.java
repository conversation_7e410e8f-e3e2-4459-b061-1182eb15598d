package com.izu.mrcar.order.dto.govcar.order;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/8/6 22:08
 */
@ApiModel("订单列表信息")
@Data
public class GovCarOrderListInfoDTO {

    @ApiModelProperty("所属企业")
    @ExcelProperty("所属企业")
    private String companyName;

    @ApiModelProperty("部门名称")
    @ExcelProperty("部门名称")
    private String structName;

    @ApiModelProperty("订单编号")
    @ExcelProperty("订单编号")
    private String orderNo;

    @ApiModelProperty("车牌号")
    @ExcelProperty("车牌号")
    private String vehicleLicense;

    @ApiModelProperty("用车人姓名")
    @ExcelProperty("用车人姓名")
    private String customerName;

    @ApiModelProperty("用车人电话")
    @ExcelProperty("用车人电话")
    private String customerMobile;

    @ApiModelProperty("下单时间")
    @ExcelProperty("下单时间")
    private String createTime;

    @ApiModelProperty("预计用车时间")
    @ExcelProperty("预计用车时间")
    private String expectedPickupTime;

    @ApiModelProperty("预计还车时间")
    @ExcelProperty("预计还车时间")
    private String expectedReturnTime;

    @ApiModelProperty("预计使用天数")
    @ExcelProperty("预计使用天数")
    private String expectedUseDays;

    @ApiModelProperty("实际取车时间")
    @ExcelProperty("实际取车时间")
    private String actualPickupTime;

    @ApiModelProperty("实际还车时间")
    @ExcelProperty("实际还车时间")
    private String actualReturnTime;

    @ApiModelProperty("实际用车天数")
    @ExcelProperty("实际用车天数")
    private String actualUseDays;

    @ExcelIgnore
    private Byte orderStatus;

    @ApiModelProperty("订单状态")
    @ExcelProperty("订单状态")
    private String orderStatusStr;

    @ExcelIgnore
    private Byte orderSettleStatus;

    @ApiModelProperty("订单结算状态")
    @ExcelProperty("结算状态")
    private String orderSettleStatusStr;

    @ApiModelProperty("订单总计(元)")
    @ExcelProperty("订单总计(元)")
    private BigDecimal totalAmount;

    @ApiModelProperty("是否展示取消按钮")
    @ExcelIgnore
    private Boolean showCancelButton;

    @ApiModelProperty("是否展示强制还车按钮")
    @ExcelIgnore
    private Boolean showForceReturnButton;

    @ApiModelProperty("是否展示费用调整按钮")
    @ExcelIgnore
    private Boolean showAdjustButton;

}
