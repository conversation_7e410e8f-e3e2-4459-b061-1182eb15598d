package com.izu.mrcar.order.dto.publicGovcar;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName PublicGovVehicleInfoDTO
 * <AUTHOR>
 * @Date 2024/8/16 13:46
 * @Version 1.0
 */

@ApiModel("详情-用车信息")
@Data
public class PublicGovVehicleInfoDTO {

    @ApiModelProperty(value = "车牌号")
    private String vehicleLicense;

    @ApiModelProperty(value = "司机")
    private String driverName;

    @ApiModelProperty(value = "品牌车型")
    private String vehicleModelName;

    @ApiModelProperty(value = "车辆投放类型 1-平台共享车，2-定点投放")
    private Byte deploymentMode;

    @ApiModelProperty(value = "车辆投放类型 1-平台共享车，2-定点投放")
    private String deploymentModeStr;

    @ApiModelProperty(value = "订单开始操作人")
    private String orderStartOperator;

    @ApiModelProperty(value = "订单结束操作人")
    private String orderEndOperator;

    @ApiModelProperty(value = "实际开始时间")
    private Date orderStartTime;

    @ApiModelProperty(value = "实际结束时间")
    private Date orderEndTime;

    @ApiModelProperty(value = "用车时长(小时)")
    private String userTime;

    @ApiModelProperty(value = "里程数(km)")
    private BigDecimal totalMileage;

    @ApiModelProperty(value = "报警类型")
    private Integer alarmType;

    @ApiModelProperty(value = "报警类型")
    private String alarmTypeStr;

    @ApiModelProperty(value = "报警编号")
    private String alarmCode;

    @ApiModelProperty(value = "出入围栏时长(小时)")
    private String outInFenceUseTime;

    @ApiModelProperty(value = "出入围栏里程数(km)")
    private BigDecimal outInFenceTotalMileage;

}