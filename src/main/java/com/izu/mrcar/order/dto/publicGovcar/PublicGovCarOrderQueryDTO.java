package com.izu.mrcar.order.dto.publicGovcar;

import com.izu.mrcar.order.dto.temporalSharedVehicle.QueryPageBaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description: 对公公务用车订单列表查询
 * @date 2024/8/16 9:45
 */
@ApiModel("订单列表查询")
@Data
public class PublicGovCarOrderQueryDTO extends QueryPageBaseDTO {

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "订单类型：1-办公用车，2-无任务用车")
    private Integer orderType;

    @ApiModelProperty(value = "订单状态(0-空；1-待出发 10-用车中 20-已完成 100-已取消)")
    private Byte orderStatus;


    @ApiModelProperty(value = "订单状态")
    private List<Byte> orderStatusList;

    @ApiModelProperty(value = "审批状态:0-无审批 1-待审批,2-审批撤回 3-审批通过, 4-审批驳回")
    private Byte approvalStatus;


    @ApiModelProperty(value = "审批状态:0-无审批 1-待审批,2-审批撤回 3-审批通过, 4-审批驳回",hidden = true)
    private List<Byte> approvalStatusList;

    @ApiModelProperty(value = "下单人企业")
    private String createCompanyCode;

    @ApiModelProperty(value = "下单人企业(客户端有子企业的时候用)")
    private List<String> companyCodes;

    @ApiModelProperty(value = "下单人部门")
    private String createStructCode;

    @ApiModelProperty(value = "下单人")
    private String createUserName;

    @ApiModelProperty(value = "下单人电话")
    private String createUserMobile;

    @ApiModelProperty(value = "用车人")
    private String passengerUserName;

    @ApiModelProperty(value = "用车人电话")
    private String passengerUserMobile;

    @ApiModelProperty(value = "司机")
    private String driverName;

    @ApiModelProperty(value = "司机电话")
    private String driverMobile;

    @ApiModelProperty(value = "车牌号")
    private String vehicleLicense;

    @ApiModelProperty(value = "预计用车日期开始")
    private String expectedPickupTimeStart;

    @ApiModelProperty(value = "预计用车日期结束")
    private String expectedPickupTimeEnd;

    @ApiModelProperty(value = "实际用车日期开始")
    private String useTimeStart;

    @ApiModelProperty(value = "实际用车日期结束")
    private String useTimeEnd;

    @ApiModelProperty(value = "订单创建日期开始")
    private String orderCreateTimeStart;

    @ApiModelProperty(value = "订单创建日期结束")
    private String orderCreateTimeEnd;

    @ApiModelProperty(value = "车辆投放类型 1-平台共享车，2-定点投放")
    private Integer deploymentMode;

    @ApiModelProperty(value = "符合条件的订单号",hidden = true)
    private List<String> orderNoList;

    @ApiModelProperty(value = "符合权限的企业编码")
    private Set<String> permCompanyList;

    @ApiModelProperty(value = "符合权限的订单编码")
    private Set<String> permOrderList;

    @ApiModelProperty(value = "(app)展示状态(订单状态和审批状态的汇总):0-无审批 1-待审批,2-审批撤回 3-审批通过, 4-审批驳回 5,审批取消  6-空；7-待出发 8-用车中 9-待核实  10-已完成 11-已取消")
    private Byte orderApprovalStatus;

    @ApiModelProperty(value = "入口类型 1:公务用车订单 2:我的订单")
    private Integer entryType;

    @ApiModelProperty(value = "订单所属企业code")
    private String orderCompanyCode;

    @ApiModelProperty(value = "核实状态", hidden = true)
    private Integer verifyStatus;

}
