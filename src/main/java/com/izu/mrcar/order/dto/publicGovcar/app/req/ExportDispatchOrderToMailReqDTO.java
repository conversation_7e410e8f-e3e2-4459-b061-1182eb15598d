package com.izu.mrcar.order.dto.publicGovcar.app.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/28 15:13
 */
@Data
public class ExportDispatchOrderToMailReqDTO {

    @ApiModelProperty(value = "订单号", required = true)
    @NotEmpty(message = "订单号不能为空")
    private String orderNo;

    @ApiModelProperty(value = "收件人邮箱列表", required = true)
    @NotEmpty(message = "收件人邮箱列表不能为空")
    private List<String> emails;
}
