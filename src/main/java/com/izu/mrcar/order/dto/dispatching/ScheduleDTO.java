package com.izu.mrcar.order.dto.dispatching;

import com.izu.framework.web.validate.hiberate.sequence.Seq1;
import com.izu.framework.web.validate.hiberate.sequence.Seq2;
import com.izu.mrcar.order.consts.OrderEnum;
import com.izu.mrcar.order.consts.dispatching.ScheduleEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * Created by hp on 2019/9/3.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ScheduleDTO {
    /**
     * 总调度单id schedule_id
     */
    @NotNull(message = "总调度单ID不能为空",groups = {Seq1.class, Seq2.class})
    private Long scheduleId;

    /**
     * 调度单编号 schedule_no
     */
    private String scheduleNo;

    /**
     * 对应的申请单id order_apply_id
     */
    private Long orderApplyId;

    /**
     * 对应的申请单编码 order_apply_no
     */
    private String orderApplyNo;

    /**
     * 企业id company_id
     */
    @NotNull(message = "企业ID不能为空",groups = {Seq1.class, Seq2.class})
    private Long companyId;

    /**
     * 下单人所在部门id struct_id
     */
    private Integer structId;

    /**
     * 调度状态；1：调度中；2：调度成功；3：已取消；4：已过期 schedule_status
     */
    private Byte scheduleStatus;

    private String scheduleStatusName;

    /**
     * 指派状态；1：未指派；2：已指派 dispatcher_status
     */
    private Byte dispatcherStatus;

    /**
     * 指派时间；总调度单直接指派的时候该时间不为空 dispatcher_time
     */
    private Date dispatcherTime;

    /**
     * 调度时间；总调度单直接调度的时候该时间不为空 schedule_time
     */
    private Date scheduleTime;

    /**
     * 总调度单生成时间 create_time
     */
    private Date createTime;

    /**
     * 最后修改人ID last_update_id
     */
    private Long lastUpdateId;

    /**
     * 最后修改人姓名 last_update_name
     */
    private String lastUpdateName;

    /**
     * 最后修改时间 last_update_time
     */
    private Date lastUpdateTime;

    /**
     * 订单类型：1：内部用车；2：商务用车；3：短租；4：约车；5：国际租车 order_type
     */
    private Byte orderType;

    /**
     * 下单人ID customer_id
     */
    @NotNull(message = "下单人ID不能为空",groups = {Seq1.class, Seq2.class})
    private Integer customerId;

    /**
     * 下单人姓名 customer_name
     */
    @NotBlank(message = "下单人姓名不能为空",groups = {Seq1.class, Seq2.class})
    private String customerName;

    /**
     * 下单人电话 customer_mobile
     */
    private String customerMobile;

    /**
     * 预约订单开始服务时间 booking_order_stime
     */
    private Date bookingOrderStime;

    /**
     * 预约订单结束服务时间 booking_order_etime
     */
    private Date bookingOrderEtime;

    /**
     * 预约开始行程短地址 booking_start_short_addr
     */
    private String bookingStartShortAddr;

    /**
     * 预约开始行程长地址 booking_start_long_addr
     */
    private String bookingStartLongAddr;

    /**
     * 预约终止行程短地址 booking_end_short_addr
     */
    private String bookingEndShortAddr;

    /**
     * 预约终止行程长地址 booking_end_long_addr
     */
    private String bookingEndLongAddr;

    /**
     * 预约开始行程城市编码 booking_start_city_code
     */
    private Integer bookingStartCityCode;

    /**
     * 预约开始行程城市名称 booking_start_city_name
     */
    private String bookingStartCityName;

    /**
     * 预约终止行程城市编码 booking_end_city_code
     */
    private Integer bookingEndCityCode;

    /**
     * 预约终止行程城市名称 booking_end_city_name
     */
    private String bookingEndCityName;

    /**
     * 预定车辆总数 booking_vehicle_total_count
     */
    private Integer bookingVehicleTotalCount;

    @NotNull(message = "调度单明细不能为空",groups = {Seq1.class, Seq2.class})
    private List<ScheduleInfo> scheduleList;

    @NotNull(message = "子行程编号不能为空",groups = {Seq2.class})
    private List<String> orderNoList;

    private Long subScheduleId;

    private Boolean isFixedPrice;

    private BigDecimal fixedPrice;
    
    private String serviceCode;

    private String serviceName;

    /**
     * 乘车人姓名 passenger_name
     */
    private String passengerName;

    /**
     * 乘车人手机号码 passenger_phone
     */
    private String passengerPhone;

    private Short orderStatus;
    private String orderStatusName;

    /**
     * 乘车人部门
     */
    private String passengerStructName;

    private Boolean isShowCancelButton;

    //是否展示分配供应商按钮
    private Boolean isShowSupplierButton;

    //是否展示退回按钮
    private Boolean isShowBackButton;

    //供应商名称
    private String supplierName;

    private Integer supplierId;
    private Integer supplierFlag;

    //是否展示重新调度按钮
    private Boolean isShowRescheduleButton;
    //是否展示清空调度按钮
    private Boolean isShowClearScheduleButton;

    /**
     * 用车人数量 booking_passenger_count
     */
    private Integer bookingPassengerCount;

    public String getServiceCode() {
        return serviceCode;
    }

    public void setServiceCode(String serviceCode) {
        this.serviceCode = serviceCode;
        OrderEnum.OrderServiceType typ=null;
        this.serviceName = serviceCode == null ? null :(typ=OrderEnum.OrderServiceType.getByValue(serviceCode))==null?"":typ.text();
    }

    @Data
    public static class ScheduleInfo implements Serializable{

        private static final long serialVersionUID = 1L;

        private Integer vehicleId;

        private String vehicleLicense;

        private Integer driverId;

        private String driverName;

        private Integer policyId;
        //对应的订单车辆级别
        private Integer orderVehicleType;
        /**
	     * 是否一口价；1：是；0：不是 fixed_price_valid
	     */
	    private Boolean fixedPriceValid;

	    /**
	     * 一口价金额 fixed_price
	     */
	    private BigDecimal fixedPrice;

        public Integer getVehicleId() {
            return vehicleId;
        }

        public void setVehicleId(Integer vehicleId) {
            this.vehicleId = vehicleId;
        }

        public String getVehicleLicense() {
            return vehicleLicense;
        }

        public void setVehicleLicense(String vehicleLicense) {
            this.vehicleLicense = vehicleLicense;
        }

        public Integer getDriverId() {
            return driverId;
        }

        public void setDriverId(Integer driverId) {
            this.driverId = driverId;
        }

        public String getDriverName() {
            return driverName;
        }

        public void setDriverName(String driverName) {
            this.driverName = driverName;
        }

        public Integer getPolicyId() {
            return policyId;
        }

        public void setPolicyId(Integer policyId) {
            this.policyId = policyId;
        }

        public Integer getOrderVehicleType() {
            return orderVehicleType;
        }

        public void setOrderVehicleType(Integer orderVehicleType) {
            this.orderVehicleType = orderVehicleType;
        }
    }

    public Byte getScheduleStatus() {
        return scheduleStatus;
    }

    public void setScheduleStatus(Byte scheduleStatus) {
        this.scheduleStatus = scheduleStatus;
        this.scheduleStatusName = scheduleStatus==null?"":(ScheduleEnum.ScheduleStatus.getByValue(scheduleStatus)==null?"":(ScheduleEnum.ScheduleStatus.getByValue(scheduleStatus).text()));
    }

    public void setOrderStatus(Short orderStatus) {
        this.orderStatus = orderStatus;
    }
}
