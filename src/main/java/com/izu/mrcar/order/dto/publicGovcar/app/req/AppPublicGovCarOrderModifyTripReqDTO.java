package com.izu.mrcar.order.dto.publicGovcar.app.req;

import com.izu.mrcar.order.dto.common.BaseDTO;
import com.izu.mrcar.order.dto.publicGovcar.AppPublicGovCarCreateOrderReqDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/15 19:59
 */
@ApiModel("公务用车对公订单修改行程")
@Data
public class AppPublicGovCarOrderModifyTripReqDTO extends BaseDTO {

    @ApiModelProperty(value = "订单号", required = true)
    private String orderNo;

    @ApiModelProperty(value = "预计目的地长地址", required = true)
    private String estimatedDestinationLocation;

    @ApiModelProperty(value = "预计目的地短地址", required = true)
    private String estimatedDestinationShortLocation;

    @ApiModelProperty(value = "预计目的地纬度", required = true)
    private BigDecimal estimatedDestinationLatitude;

    @ApiModelProperty(value = "预计目的地经度", required = true)
    private BigDecimal estimatedDestinationLongitude;

    @ApiModelProperty(value = "预计出发地长地址", required = true)
    private String estimatedDepartureLocation;

    @ApiModelProperty(value = "预计出发地短地址", required = true)
    private String estimatedDepartureShortLocation;

    @ApiModelProperty(value = "预计出发地纬度", required = true)
    private BigDecimal estimatedDepartureLatitude;

    @ApiModelProperty(value = "预计出发地经度", required = true)
    private BigDecimal estimatedDepartureLongitude;

    @ApiModelProperty(value = "乘车人信息", required = true)
    @NotNull(message = "乘车人不能为空")
    List<AppPublicGovCarCreateOrderReqDTO.PassengersInfo> passengersInfoList;


    @ApiModelProperty(value = "司机id", required = true)
    @NotNull(message = "司机id不能为空")
    private Integer driverId;

    @ApiModelProperty(value = "司机code", required = true)
    @NotEmpty(message = "司机code不能为空")
    private String driverCode;

    @ApiModelProperty(value = "司机姓名", required = true)
    @NotEmpty(message = "司机姓名不能为空")
    private String driverName;

    @ApiModelProperty(value = "司机电话", required = true)
    @NotEmpty(message = "司机电话不能为空")
    private String driverMobile;

    @ApiModelProperty(value = "用车事由", required = true)
    @NotEmpty(message = "用车事由不能为空")
    private String carUseReason;

    @ApiModelProperty(value = "备注")
    private String orderUserMemo;

    @ApiModelProperty(value = "司机部门id", required = true)
    private Integer driverStructId;

    @ApiModelProperty(value = "司机部门Code", required = true)
    private String driverStructCode;

    @ApiModelProperty(value = "司机部门名称", required = true)
    private String driverStructName;


    @ApiModelProperty(value = "车辆ID", required = true)
    @NotNull(message = "车辆ID不能为空")
    private Integer vehicleId;

    @ApiModelProperty(value = "车辆车牌号")
    private String vehicleLicense;

    @ApiModelProperty(value = "车架号")
    private String vehicleVin;

    @ApiModelProperty(value = "无钥匙用车")
    private Byte noKeyUseCar;

    @ApiModelProperty(value = "车辆品牌编码")
    private String vehicleBrandCode;

    @ApiModelProperty(value = "车辆品牌")
    private String vehicleBrandName;

    @ApiModelProperty(value = "车型编码")
    private String vehicleModelCode;

    @ApiModelProperty(value = "车型名称")
    private String vehicleModelName;

    @ApiModelProperty(value = "车辆图片URL路径")
    private String vehiclePicUrl;

    @ApiModelProperty(value = "预计取车时间", required = true)
    @NotNull(message = "预计取车时间不能为空")
    private Date expectedPickupTime;

    @ApiModelProperty(value = "预计还车时间", required = true)
    @NotNull(message = "预计还车时间不能为空")
    private Date expectedReturnTime;

    @ApiModelProperty(value = "投放模式：1-平台共享车，2-定点投放")
    private Byte deploymentMode;


}
