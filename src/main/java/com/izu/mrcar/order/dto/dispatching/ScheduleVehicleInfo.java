package com.izu.mrcar.order.dto.dispatching;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: 调度车辆信息
 * @date 2024/4/9 9:58
 */
@Data
public class ScheduleVehicleInfo {

    @ApiModelProperty("车牌号")
    private String assignCarLicense;
    @ApiModelProperty("司机id")
    private Long assignDriverId;
    @ApiModelProperty("司机")
    private String assignDriverName;
    @ApiModelProperty("司机电话")
    private String assignDriverPhone;
    private Integer assignCarmodelId;
    @ApiModelProperty("车型")
    private String assignCarmodelName;
    private String orderCancelNo;
    private Date orderCancelTime;
    private Boolean appraiseSubmited;
    private Date appraiseSubmitTime;
    private Byte trafficType;
    private String trafficNumber;
    private String factStartLongAddr;
    private String factStartShortAddr;
    private String factStartPoint;
    private String factEndLongAddr;
    private String factEndShortAddr;
    private String factEndPoint;
    private Date factStartDate;
    private Date factEndDate;
    private Date driverStartTime;
    private Date driverArriveTime;
    private Date getOnTime;

    private Short orderStatus;
    @ApiModelProperty("行程状态")
    private String orderStatusName;
    private BigDecimal totalAmount;
    private BigDecimal shouldpayAmount;
    private String vehicleStructName;
    @ApiModelProperty("品牌")
    private String vehicleBrand;
    private String vehicleModel;
    @ApiModelProperty("车级")
    private String vehicleLevel;
    @ApiModelProperty("颜色")
    private String vehicleColour;
    private String policyName;
    private Integer policyId;
    private Byte policyType;
    private Integer vehicleSeatCount;

    //选中图片路径
    private String vehicleImageOn;
    //未选中图片路径
    private String vehicleImageOff;
    //未选中圆形图片路径
    private String vehicleRoundOff;
    //选中圆形图片路径
    private String vehicleRoundOn;
    /**
     * 是否一口价；1：是；0：不是 fixed_price_valid
     */
    private Boolean fixedPriceValid;

    /**
     * 一口价金额 fixed_price
     */
    private BigDecimal fixedPrice;

    @ApiModelProperty("供应商编码")
    private String supplier_code;

    @ApiModelProperty("供应商名称")
    private String supplier_name;

    @ApiModelProperty("服务方")
    private String providerService;

    @ApiModelProperty("价格id")
    private Long price_config_id;

    @ApiModelProperty("价格编码")
    private String priceConfigCode;

    @ApiModelProperty("预估金额 基本费*租期")
    private String estimatedAmount;
}
