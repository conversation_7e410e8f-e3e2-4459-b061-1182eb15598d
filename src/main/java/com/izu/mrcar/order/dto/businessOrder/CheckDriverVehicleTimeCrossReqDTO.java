package com.izu.mrcar.order.dto.businessOrder;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;


/**
 * 校验司机和车辆是否有交叉的行程
 *
 * <AUTHOR> on  2024/4/21 11:14
 */
@ApiModel(value = "商务车下单-司机车辆时间交叉请求")
@Data
public class CheckDriverVehicleTimeCrossReqDTO {
    /**
     * 车辆id
     */
    @ApiModelProperty(value = "车辆id")
    private Integer vehicleId;
    /**
     * 司机ID
     */
    @ApiModelProperty(value = "司机id")
    private Integer driverId;
    /**
     * 预约用车开始时间
     */
    @ApiModelProperty(value = "预约用车开始时间")
    @NotNull(message = "预约用车开始时间不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date bookingStartTime;

    /**
     * 预约用车结束时间
     */
    @ApiModelProperty(value = "预约用车结束时间")
    @NotNull(message = "预约用车结束时间不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date bookingEndTime;
}
