package com.izu.mrcar.order.dto.publicGovcar;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 异步轮询订单结束任务返回对象.
 *
 * <AUTHOR>
 * @date 2024-08-19
 */
@Data
public class AppPublicGovCarOrderEndTripTaskRespDTO {

    @ApiModelProperty(value = "任务id")
    private String taskId;

    @ApiModelProperty(value = "任务是否完成")
    private boolean completed;

    @ApiModelProperty(value = "任务返回状态. 当任务完成时有效")
    private int code;

    @ApiModelProperty(value = "任务返回信息, 当任务完成时有效")
    private String message;

    @ApiModelProperty(value = "返回额外数据")
    private Object data;

}
