package com.izu.mrcar.order.dto.businessOrder;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> ON 2022/12/7.
 */
@Data
public class BillDTO {
    /**
     * 账单ID，自增 id
     */
    private Long id;

    /**
     * 创建时间 create_time
     */
    private Date createTime;

    /**
     * 更新时间 update_time
     */
    private Date updateTime;

    /**
     * 账单编号 bill_no
     */
    private String billNo;

    /**
     * 用车申请编号 order_apply_no
     */
    private String orderApplyNo;

    /**
     * 是否一口价；1：是；0：不是 fixed_price_valid
     */
    private Boolean fixedPriceValid;

    /**
     * 一口价金额 fixed_price
     */
    private BigDecimal fixedPrice;

    /**
     * 总金额 total_amount
     */
    private BigDecimal totalAmount;

    /**
     * 应支付金额 shouldpay_amount
     */
    private BigDecimal shouldpayAmount;

    /**
     * 未支付金额 unpayed_amount
     */
    private BigDecimal unpayedAmount;

    /**
     * 已支付金额 payed_amount
     */
    private BigDecimal payedAmount;

    /**
     * 优惠券抵扣金额 coupon_deduct_amount
     */
    private BigDecimal couponDeductAmount;

    /**
     * 减免金额 reduction_amount
     */
    private BigDecimal reductionAmount;

    /**
     * 实际计为收入金额 actual_income_amount
     */
    private BigDecimal actualIncomeAmount;

    /**
     * 其他费用 other_amount
     */
    private BigDecimal otherAmount;

    /**
     * 订单调价原因 price_explain
     */
    private String priceExplain;

    /**
     * 账单状态；11：费用补录中；12：订单待结算；13：已结算 bill_status
     */
    private Byte billStatus;

    /**
     * 汇总额度锁定状态 fee_lock
     */
    private Boolean feeLock;

    /**
     * 调价人ID adjust_price_user_id
     */
    private Integer adjustPriceUserId;

    /**
     * 调价人姓名 adjust_price_user_name
     */
    private String adjustPriceUserName;

    /**
     * 调价时间 adjust_price_time
     */
    private Date adjustPriceTime;
}
