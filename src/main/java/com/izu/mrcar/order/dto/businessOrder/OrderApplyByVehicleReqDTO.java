package com.izu.mrcar.order.dto.businessOrder;

import com.izu.user.dto.staff.pc.DataPermDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> ON 2022/12/14.
 */
@Data
public class OrderApplyByVehicleReqDTO extends DataPermDTO {
    @ApiModelProperty(value = "车牌号",example = "京A88888")
    private String vehicleLicense;
    @ApiModelProperty(value = "用车类型",example = "[1,2,3]")
    private List<String> orderType;
    @ApiModelProperty(hidden = true)
    private List<Byte> orderTypeList;
    @ApiModelProperty(value = "预约开始时间",example = "2022-11-19")
    private String bookingOrderStime;
    @ApiModelProperty(value = "预约结束时间",example = "2022-11-19")
    private String bookingOrderEtime;
    @ApiModelProperty(value = "排序",example = "1 totalAmount 2 orderCount 3 totalOrderMileage 4 totalTime")
    private Byte sortField;
    @ApiModelProperty(value = "升序降序",example = "true asc|false desc")
    private Boolean sortFieldToAsc;
}
