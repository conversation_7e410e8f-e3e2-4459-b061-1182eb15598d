package com.izu.mrcar.order.dto.publicGovcar;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 对公公务用车订单详情（审批、详情）
 * @date 2024/8/15 9:45
 */
@ApiModel("订单详情")
@Data
public class PublicGovCarOrderDetailRespDTO {

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "用车类型")
    private String vehicleUsageStr;

    @ApiModelProperty(value = "用车类型枚举")
    private String vehicleUsage;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "下单人")
    private String createUserName;

    @ApiModelProperty(value = "下单人手机号")
    private String createUserPhone;

    @ApiModelProperty(value = "下单人部门")
    private String createUserStructName;

    @ApiModelProperty(value = "乘车人信息")
    List<PassengersInfo> passengersInfoList;

    @ApiModelProperty(value = "预计出发时间")
    private Date expectedPickupTime;

    @ApiModelProperty(value = "预计结束时间")
    private Date expectedReturnTime;

    @ApiModelProperty(value = "用车事由")
    private String carUseReason;

    @ApiModelProperty(value = "预计出发地")
    private String estimatedDepartureLocation;

    @ApiModelProperty(value = "预计目的地")
    private String estimatedDestinationLocation;

    @ApiModelProperty(value = "备注")
    private String orderUserMemo;

    @ApiModelProperty(value = "车辆车牌号")
    private String vehicleLicense;

    @ApiModelProperty(value = "车辆品牌")
    private String vehicleBrandName;

    @ApiModelProperty(value = "车型名称")
    private String vehicleModelName;
    @ApiModelProperty(value = "用车人姓名")
    private String useVehicleName;
    @ApiModelProperty(value = "用车人手机号")
    private String useVehicleMobile;
    @ApiModelProperty(value = "用车人部门")
    private String useVehicleStructName;

    @ApiModelProperty(value = "用车人(前端展示)")
    private String useVehicleUser;

    @ApiModelProperty(value = "预计用车时间")
    private String expectedUseTime;

    @ApiModelProperty(value = "品牌车型")
    private String brandAndModel;

    public String getBrandAndModel() {
        return this.vehicleBrandName+this.vehicleModelName;
    }

    public String getExpectedUseTime() {
        if(expectedPickupTime!=null&&expectedReturnTime!=null){
            return new SimpleDateFormat("yyyy-MM-dd HH:mm").format(expectedPickupTime)+"~"+new SimpleDateFormat("yyyy-MM-dd HH:mm").format(expectedReturnTime);
        }
        return "";
    }

    public String getUseVehicleUser() {
        return this.useVehicleName+"/"+this.useVehicleMobile;
    }

    @ApiModelProperty(value = "司机姓名")
    private String driverName;

    @ApiModelProperty(value = "司机手机号")
    private String driverMobile;

    @ApiModelProperty(value = "司机部门")
    private String driverStructName;

    @ApiModelProperty(value = "司机姓名（前端展示）")
    private String driverNameStr;

    public String getDriverNameStr() {
        return this.driverName+"/"+this.driverMobile;
    }

    @Data
    @ApiModel("乘车人信息")
    public static class PassengersInfo {

        @ApiModelProperty(value = "乘车人姓名")
        private String passengerName;

        @ApiModelProperty(value = "乘车人电话")
        private String passengerMobile;

        @ApiModelProperty(value = "车辆部门名称")
        private String passengerStructName;

    }

}
