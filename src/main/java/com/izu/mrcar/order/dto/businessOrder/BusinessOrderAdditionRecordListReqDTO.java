package com.izu.mrcar.order.dto.businessOrder;

import com.izu.mrcar.order.dto.common.PageParamDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


@EqualsAndHashCode(callSuper = false)
@Data
@ApiModel("商务车补录订单列表查询")
public class BusinessOrderAdditionRecordListReqDTO extends PageParamDTO {

    /**
     * 订单编号
     **/
    @ApiModelProperty(value = "订单编号")
    private String orderCode;

    /**
     * 订单状态;1-已保存;2-已结算;4-已废除
     **/
    @ApiModelProperty(value = "订单状态;1-已保存;2-已结算;4-已废除")
    private Integer orderStatus;

    /**
     * 客户编码
     **/
    @ApiModelProperty(value = "客户编码")
    private String customerCode;

    /**
     * 我司签约主体的社会信用代码
     **/
    @ApiModelProperty(value = "我司签约主体的社会信用代码")
    private String ourSocialCreditCode;


    /** 1-首汽车辆; 2-外部车辆 **/
    @ApiModelProperty("1-首汽车辆; 2-外部车辆")
    private Byte vehicleSelfOwned;


    /** 创建人编码 **/
    @ApiModelProperty("创建人编码")
    private String createCode;





}
