package com.izu.mrcar.order.dto.businessOrder;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 商务车权益卡使用明细
 * <AUTHOR> on  2024/4/21 13:58
 */
@ApiModel(value = "商务车权益卡使用明细")
@Data
public class BusinessOrderCarUsageDetailRespDTO {

    /**
     * 权益卡编号 card_code
     */
    @ApiModelProperty(value = "权益卡编号")
    private String cardCode;

    /**
     * 权益卡名称 card_name
     */
    @ApiModelProperty(value = "权益卡名称")
    private String cardName;


    /**
     * 权益卡激活码 card_secret
     */
    @ApiModelProperty(value = "权益卡激活码")
    private String cardSecret;

    /**
     * 抵扣金额
     */
    @ApiModelProperty(value = "抵扣金额")
    private String deductionAmount;

    /**
     * 成本归属
     * 权益卡的投放渠道-比如：首汽租赁北京分公司
     */
    @ApiModelProperty(value = "成本归属")
    private String costAttributionDepartment;
}
