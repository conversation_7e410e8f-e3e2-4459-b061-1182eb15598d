package com.izu.mrcar.order.dto.businessOrder;


import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.izu.mrcar.order.dto.common.BaseDTO;
import com.izu.mrcar.order.dto.common.FileInfoDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 商务车订单补录记录保存参数
 */
@EqualsAndHashCode(callSuper = false)
@Data
@ApiModel("商务车订单补录记录保存参数")
public class BusinessOrderAdditionRecordSaveReqDTO extends BaseDTO {

    /**
     * 客户编码
     **/
    @ApiModelProperty(value = "客户编码", required = true)
    @NotBlank(message = "客户编码不能为空")
    private String customerCode;

    /**
     * 客户名称
     **/
    @ApiModelProperty(value = "客户名称", required = true)
    @NotBlank(message = "客户名称不能为空")
    private String customerName;

    /**
     * 我司签约主体的社会信用代码
     **/
    @ApiModelProperty(value = "我司签约主体的社会信用代码", required = true)
    @NotBlank(message = "我司签约主体的社会信用代码不能为空")
    private String ourSocialCreditCode;

    /**
     * 我司签约主体名称
     **/
    @ApiModelProperty(value = "我司签约主体名称", required = true)
    @NotBlank(message = "我司签约主体名称不能为空")
    private String ourContractingEntity;

    /**
     * 车牌号
     **/
    @ApiModelProperty(value = "车牌号", required = true)
    @NotBlank(message = "车牌号不能为空")
    private String vehicleLicense;

    /**
     * 机动车使用人编码
     **/
    @ApiModelProperty(value = "机动车使用人编码", required = true)
    @NotBlank(message = "机动车使用人编码不能为空")
    private String vehicleOperateBussCode;


    /**
     * 机动车使用人名称 vehicle_operate_buss_name
     */
    @ApiModelProperty(value = "机动车使用人名称", required = false)
    private String vehicleOperateBussName;


    /**
     * 机动车所有人编码
     **/
    @ApiModelProperty(value = "机动车所有人编码", required = true)
    @NotBlank(message = "机动车所有人编码不能为空")
    private String vehicleBelongBussCode;


    /**
     * 机动车所有人名称 vehicle_belong_buss_name
     */
    @ApiModelProperty(value = "机动车使用人名称", required = false)
    private String vehicleBelongBussName;

    /**
     * 司机姓名
     */
    @ApiModelProperty(value = "司机姓名", required = false)
    private String driverName;

    /**
     * 司机手机号
     */
    @ApiModelProperty(value = "司机手机号", required = false)
    private String driverMobile;

    /**
     * 用车开始日期
     **/
    @ApiModelProperty(value = "用车开始日期", required = true)
    @NotNull(message = "用车开始日期不能为空")
    @JSONField(format = "yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date startDate;

    /**
     * 用车结束日期
     **/
    @ApiModelProperty(value = "用车结束日期", required = true)
    @NotNull(message = "用车结束日期不能为空")
    @JSONField(format = "yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm")
    private Date endDate;

    /**
     * 订单金额
     **/
    @ApiModelProperty(value = "订单金额", required = true)
    @NotNull(message = "订单金额不能为空")
    private BigDecimal amount;

    /**
     * 税率(小数)
     **/
    @ApiModelProperty(value = "税率(小数) ", required = true)
    @NotNull(message = "税率(小数) 不能为空")
    @DecimalMin(message = "最小税率为0", value = "0")
    @DecimalMax(message = "最大税率为1", value = "1")
    private BigDecimal taxRate;

    /**
     * 账期(天数)
     **/
    @ApiModelProperty(value = " 账期(天数)  ", required = true)
    @NotNull(message = " 账期(天数)不能为空")
    @Min(value = 0, message = "账期最小值为0")
    @Max(value = 1000, message = "账期最大值为1000")
    private Integer paymentDays;


    @ApiModelProperty(value = "备注")
    private String remark;


    @ApiModelProperty(value = "合同文件列表", required = true)
    @NotNull(message = "合同文件列表不能为空")
    @Size(max = 100, message = "文件最大个数100")
    @Valid
    private List<FileInfoDTO> contractFileList;

    @ApiModelProperty(value = "供应商编码",required = true)
    private String supplierCode;
    @ApiModelProperty(value = "供应商名称",required = true)
    private String supplierName;

    @ApiModelProperty(value = "应支供应商金额")
    private BigDecimal shouldSupplierAmount;
    @ApiModelProperty(value = "服务方")
    private String ServiceProvider;

    @ApiModelProperty(value = "车辆id")
    private Integer vehicleId;
    @ApiModelProperty(value = "供应商税率", example = "25",required = true)
    private BigDecimal supplierRate;


}
