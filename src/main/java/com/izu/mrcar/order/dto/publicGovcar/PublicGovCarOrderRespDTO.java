package com.izu.mrcar.order.dto.publicGovcar;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: 对公公务用车订单列表
 * @date 2024/8/16 9:45
 */
@ApiModel("订单列表")
@Data
public class PublicGovCarOrderRespDTO {

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "订单类型：1-办公用车，2-无任务用车")
    private Integer orderType;

    @ApiModelProperty(value = "订单类型：1-办公用车，2-无任务用车")
    private String orderTypeStr;

    @ApiModelProperty(value = "订单状态(0-空；1-待出发 10-用车中 20-待核实 30-已完成 100-已取消)")
    private Byte orderStatus;

    @ApiModelProperty(value = "订单状态(0-空；1-待出发 10-用车中 20-待核实 30-已完成 100-已取消)")
    private String orderStatusStr;

    @ApiModelProperty(value = "审批状态:0-无审批 1-待审批,2-审批撤回 3-审批通过, 4-审批驳回")
    private Byte approvalStatus;

    @ApiModelProperty(value = "审批状态:0-无审批 1-待审批,2-审批撤回 3-审批通过, 4-审批驳回")
    private String approvalStatusStr;

    @ApiModelProperty(value = "下单人企业")
    private String createCompanyName;

    @ApiModelProperty(value = "下单人部门")
    private String createStructName;

    @ApiModelProperty(value = "下单人")
    private String createUserName;

    @ApiModelProperty(value = "主用车人")
    private String passengerUserName;

    @ApiModelProperty(value = "司机")
    private String driverName;

    @ApiModelProperty(value = "车牌号")
    private String vehicleLicense;

    @ApiModelProperty(value = "车型")
    private String vehicleModelName;

    @ApiModelProperty(value = "车辆投放类型 1-平台共享车，2-定点投放")
    private Integer deploymentMode;


    @ApiModelProperty(value = "车辆投放类型 1-平台共享车，2-定点投放")
    private String deploymentModeStr;


    @ApiModelProperty(value = "出发地")
    private String estimatedDepartureLocation;


    @ApiModelProperty(value = "目的地")
    private String estimatedDestinationLocation;


    @ApiModelProperty(value = "预计开始时间")
    private Date expectedPickupTime;

    @ApiModelProperty(value = "预计结束时间")
    private Date expectedReturnTime;


    @ApiModelProperty(value = "实际开始时间")
    private Date orderStartTime;

    @ApiModelProperty(value = "实际结束时间")
    private Date orderEndTime;

    @ApiModelProperty(value = "用车时长(小时)")
    private String userTime;

    @ApiModelProperty(value = "里程数(km)")
    private BigDecimal totalMileage;

    @ApiModelProperty(value = "下单时间")
    private Date createTime;

    @ApiModelProperty(value = "出围栏时的电子围栏快照ID 调用围栏信息接口时使用")
    private Integer startFenceId;

    @ApiModelProperty(value = "还车时电子围栏快照ID 调用围栏信息接口时使用")
    private Integer returnFenceId;

    @ApiModelProperty(value = "关联的报警编码 调用根据报警编号查询报警记录接口使用,最终目的用于调用行车轨迹")
    private String alarmCode;

    @ApiModelProperty(value = "费用归属部门")
    private String orderStructName;

    @ApiModelProperty("是否展示派车单按钮 0:不展示 1:展示")
    private Integer showDispatchButton;

    @ApiModelProperty("是否展示取消按钮 0:不展示 1:展示")
    private Integer showCancelButton;

    @ApiModelProperty("是否展示强制结束按钮 0:不展示 1:展示")
    private Integer showForceFinishButton;

    @ApiModelProperty("订单所属企业")
    private String orderCompanyName;

    @ApiModelProperty("用车人部门")
    private String vehicleUsageStructName;

    @ApiModelProperty("车辆所属部门")
    private String vehicleStructName;

    @ApiModelProperty("出入围栏时长(小时)")
    private String outInFenceUseTime;

    @ApiModelProperty("出入围栏里程数(km)")
    private BigDecimal outInFenceTotalMileage;



    /**
     * 驶出围栏的时间 pickup_lot_exit_time
     */
    private Date pickupLotExitTime;

    /**
     * 驶入围栏时间 return_lot_entry_time
     */
    private Date returnLotEntryTime;


    /** 用车事由，描述用户为何需要使用车辆 **/
    @ApiModelProperty("用车事由")
    private String carUseReason;

    /** 订单用户备注 **/
    @ApiModelProperty("用车备注")
    private String orderUserMemo;



    private String companyName;

    public void setCompanyName(String companyName){
        this.companyName = companyName;
        this.orderCompanyName = companyName;
    }
}
