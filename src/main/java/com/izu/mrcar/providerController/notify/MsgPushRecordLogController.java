package com.izu.mrcar.providerController.notify;

import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.ParamsUtil;
import com.izu.notify.NotifyRestLocator;
import com.izu.notify.NotifyRestUrl;
import com.izu.notify.enums.MsgModelEnum;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@RestController
@RequestMapping("/notify/msgPushRecordLog")
public class MsgPushRecordLogController {

    @RequestMapping("/queryByPage")
    @RequestFunction(functionName = "推送日志分页查询")
    public RestResponse queryByPage(HttpServletRequest request) {
        Map<String,Object> paramMap = ParamsUtil.getParams(request);
        paramMap.put("modelCode", MsgModelEnum.ModelCodeEnum.MR_CAR.getModelCode());
        paramMap.put("modelName", MsgModelEnum.ModelCodeEnum.MR_CAR.getModelName());
        String restUrl = new NotifyRestLocator().getRestUrl(NotifyRestUrl.MSG_PUSH_RECORD_LOG_QUERY_BY_PAGE_FOR_BACK);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY,restUrl,paramMap, null, null);
    }

    @RequestMapping("/updatePushIntoMsg")
    @RequestFunction(functionName = "推送日志修改状态")
    public RestResponse updatePushIntoMsg(HttpServletRequest request){
        Map<String,Object> paramMap = ParamsUtil.getParams(request);
        AccountBaseInfo baseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
        paramMap.put("currentLoginUserId",baseInfo.getStaffId());
        paramMap.put("currentLoginUserName",baseInfo.getStaffName());
        String restUrl = new NotifyRestLocator().getRestUrl(NotifyRestUrl.MSG_LOG_UPDATEPUSHINTOMSG);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl,paramMap, null, null);
    }
}
