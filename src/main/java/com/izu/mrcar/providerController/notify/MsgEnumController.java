package com.izu.mrcar.providerController.notify;

import com.izu.framework.doc.JrdApiParamDoc;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.ParamsUtil;
import com.izu.notify.NotifyRestLocator;
import com.izu.notify.NotifyRestUrl;
import com.izu.notify.enums.MsgModelEnum;
import com.izu.user.enums.UserLabelEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping(value = "/msgEnum")
public class MsgEnumController {

    @Value("${mrcar-user-core.host.url}")
    private String USER_HOST_URL;

    /**
     * 查询基础枚举
     */
    @RequestMapping("/findAll")
    @RequestFunction(functionName = "查询基础枚举")
    public RestResponse findAll(HttpServletRequest request) {
        Map<String,Object> paramMap = ParamsUtil.getParams(request);
        String restUrl =  new NotifyRestLocator().getRestUrl(NotifyRestUrl.MSG_ENUM_FINDALL);
        RestResponse restResponse = RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, paramMap, null);
        if(restResponse != null && restResponse.isSuccess()){
            Map<String, Object> returnMap =(Map<String, Object>) restResponse.getData();
            returnMap.put("secondModelCodeEnum",MsgModelEnum.SecondModelCodeEnum.getEnums(MsgModelEnum.ModelCodeEnum.MR_CAR.getModelCode()));
            returnMap.put("UserLabelEnum", UserLabelEnum.getEnums());
        }
        return restResponse;
    }

    /**
     * 查询基础枚举
     */
    @RequestMapping("/getUserGroupLable")
    @RequestFunction(functionName = "用户标签枚举")
    public RestResponse getUserGroupLable(HttpServletRequest request) {
        Map<String,Object> paramMap = ParamsUtil.getParams(request);
        paramMap.put("modelCode", MsgModelEnum.ModelCodeEnum.MR_CAR.getModelCode());
        String restUrl =  new NotifyRestLocator().getRestUrl(NotifyRestUrl.MSG_ENUM_FINDUSERGROUPLABEL);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, paramMap, null);
    }

    /**
     * 企业下拉框（启用、短信通知是否开启）
     * @param pushMethod
     * @return
     */
    @RequestMapping("/getCompanyBussList")
    public RestResponse getCompanyBussList(
            @Verify(param="pushMethod",rule="") @JrdApiParamDoc(desc = "推送方式", example = "2") Integer pushMethod) {
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("pushMethod",pushMethod);
        String restUrl =  USER_HOST_URL + "companyManage/getCompanyBussList";
        RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, paramMap, null,null);
        return restResponse;
    }
}
