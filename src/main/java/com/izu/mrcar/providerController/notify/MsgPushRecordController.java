package com.izu.mrcar.providerController.notify;

import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.ParamsUtil;
import com.izu.notify.NotifyRestLocator;
import com.izu.notify.NotifyRestUrl;
import com.izu.notify.enums.MsgModelEnum;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@RestController
@RequestMapping("/notify/msgPushRecord")
public class MsgPushRecordController {

    @RequestMapping("/queryByPage")
    @RequestFunction(functionName = "消息记录分页查询")
    public RestResponse queryByPage(HttpServletRequest request){
        Map<String,Object> paramMap = ParamsUtil.getParams(request);
        AccountBaseInfo baseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
        paramMap.put("currentLoginUserId",baseInfo.getStaffId());
        paramMap.put("currentLoginUserName",baseInfo.getStaffName());
        paramMap.put("modelCode", MsgModelEnum.ModelCodeEnum.MR_CAR.getModelCode());
        paramMap.put("modelName", MsgModelEnum.ModelCodeEnum.MR_CAR.getModelName());
        String restUrl = new NotifyRestLocator().getRestUrl(NotifyRestUrl.MSG_PUSH_PUSH_QUERY_BY_PAGE);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl,paramMap, null, null);
    }

    @RequestMapping("/delete")
    @RequestFunction(functionName = "消息记录清除记录")
    public RestResponse delete(HttpServletRequest request){
        Map<String,Object> paramMap = ParamsUtil.getParams(request);
        AccountBaseInfo baseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
        paramMap.put("currentLoginUserId",baseInfo.getStaffId());
        paramMap.put("currentLoginUserName",baseInfo.getStaffName());
        String restUrl = new NotifyRestLocator().getRestUrl(NotifyRestUrl.MSG_PUSH_PUSH_DELETE);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl,paramMap, null, null);
    }
}
