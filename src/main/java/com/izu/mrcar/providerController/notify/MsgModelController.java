package com.izu.mrcar.providerController.notify;

import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.ParamsUtil;
import com.izu.notify.NotifyRestLocator;
import com.izu.notify.NotifyRestUrl;
import com.izu.notify.enums.MsgModelEnum;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping(value = "/notify/msgModel")
public class MsgModelController {
    /**
     * 查询消息模版分页
     * @param request
     * @return
     * @throws IOException
     */
    @RequestMapping("/findList")
    @RequestFunction(functionName = "公告、埋点消息分页查询")
    public RestResponse findList(HttpServletRequest request) {
        Map<String,Object> paramMap = ParamsUtil.getParams(request);
        paramMap.put("modelCode", MsgModelEnum.ModelCodeEnum.MR_CAR.getModelCode());
        String restUrl = new NotifyRestLocator().getRestUrl(NotifyRestUrl.MSG_MODEL_FINDLIST);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl,paramMap, null, null);
    }

    /**
     * 根据id查询消息模版详情
     * @param request
     * @return
     * @throws IOException
     */
    @RequestMapping("/findDetailById")
    @RequestFunction(functionName = "公告、埋点消息详情")
    public RestResponse findDetailById(HttpServletRequest request) {
        Map<String,Object> paramMap = ParamsUtil.getParams(request);
//        paramMap.put("modelCode", MsgModelEnum.ModelCodeEnum.MR_CAR.getModelCode());
//        paramMap.put("modelName", MsgModelEnum.ModelCodeEnum.MR_CAR.getModelName());
        String restUrl = new NotifyRestLocator().getRestUrl(NotifyRestUrl.MSG_MODEL_FINDDETAILBYID);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl,paramMap, null, null);
    }

    /**
     * 根据id查询消息模版详情
     * @param request
     * @return
     * @throws IOException
     */
    @RequestMapping("/saveOrUpdate")
    @RequestFunction(functionName = "公告、埋点消息编辑")
    public RestResponse saveOrUpdate(HttpServletRequest request)  {
        Map<String,Object> paramMap = ParamsUtil.getParams(request);
        paramMap.put("modelCode", MsgModelEnum.ModelCodeEnum.MR_CAR.getModelCode());
        paramMap.put("modelName", MsgModelEnum.ModelCodeEnum.MR_CAR.getModelName());
        AccountBaseInfo baseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
        paramMap.put("currentLoginUserId",baseInfo.getStaffId());
        paramMap.put("currentLoginUserName",baseInfo.getStaffName());
        String restUrl = new NotifyRestLocator().getRestUrl(NotifyRestUrl.MSG_MODEL_SAVEORUPDATE);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY,restUrl, paramMap, null, null);
    }

    /**
     * 根据id修改状态
     * @param request
     * @return
     */
    @RequestMapping("/updateState")
    @RequestFunction(functionName = "公告、埋点消息修改状态")
    public RestResponse updateState(HttpServletRequest request){
        Map<String,Object> paramMap = ParamsUtil.getParams(request);
        AccountBaseInfo baseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
        paramMap.put("currentLoginUserId",baseInfo.getStaffId());
        paramMap.put("currentLoginUserName",baseInfo.getStaffName());
        String restUrl = new NotifyRestLocator().getRestUrl(NotifyRestUrl.MSG_MODEL_UPDATESTATE);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST,restUrl, paramMap, null, null);
    }

}
