package com.izu.mrcar.providerController.device;

import com.alibaba.excel.EasyExcel;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.controller.export.CarGpsRelationListExport;
import com.izu.mrcar.controller.export.DeviceExport;
import com.izu.mrcar.iot.config.IotCoreRestMsgCenter;
import com.izu.mrcar.iot.config.IotRestLocator;
import com.izu.mrcar.iot.dto.GpsVehicleDTO;
import com.izu.mrcar.iot.dto.device.GpsBindHistoryListDTO;
import com.izu.mrcar.iot.dto.device.GpsBindRecordDTO;
import com.izu.mrcar.iot.dto.input.DeviceQueryDTO;
import com.izu.mrcar.iot.dto.input.DeviceSaveInputDTO;
import com.izu.mrcar.service.excel.MergeCellStrategyHandler;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.enums.perm.ClientDataPermTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.*;

/**
 * <AUTHOR>
 * @description: 设备接口
 * @date 2024/9/22 19:08
 */
@RestController
@Slf4j
@Api(tags = "运营端-设备接口")
public class ProviderDeviceController {


    //查询设备列表
    @RequestMapping(IotCoreRestMsgCenter.PROVIDER_DEVICE_LIST_PAGE)
    public RestResponse<List<GpsVehicleDTO>> deviceListPage(@RequestBody DeviceQueryDTO param) {
        String restUrl = new IotRestLocator().getRestUrl(IotCoreRestMsgCenter.DEVICE_LIST_PAGE);
        Map<String, Object> httpParams = new HashMap<>();
        httpParams.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, httpParams, null);

    }


    //导出
    @RequestMapping(IotCoreRestMsgCenter.PROVIDER_DEVICE_LIST_EXPORT)
    @ExportExcelWeb(fileName = "设备列表导出.xlsx",filePath = ExportExcelConstants.FILE_PATH,
            sheet = "设备列表导出", c = DeviceExport.class)
    public PageDTO deviceListPageExport(@RequestBody DeviceQueryDTO param, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response) {
        Map<String, Object> paramMap = new HashMap<>();
        String url = new IotRestLocator().getRestUrl(IotCoreRestMsgCenter.PROVIDER_DEVICE_LIST_PAGE);
        param.setPageNo(1);
        param.setPageSize(10000);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, DeviceExport.class);
        if(restResponse!=null && restResponse.isSuccess()){
            PageDTO pageDTO = (PageDTO)restResponse.getData();
            if (pageDTO.getTotal() > 10000){
                throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
            }
            return pageDTO;
        }
        else {
            return null;
        }
    }




    //编辑接口
    //删除接口
    @RequestMapping(IotCoreRestMsgCenter.PROVIDER_DEVICE_UPDATE)
    @ApiOperation(value = "编辑设备")
    public RestResponse deviceEdit(@RequestBody @Valid DeviceSaveInputDTO param) {
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        param.setUserId(loginBaseInfo.obtainBaseInfo().getStaffId());
        param.setUserName(loginBaseInfo.obtainBaseInfo().getStaffName());
        Integer source=10;
        param.setSource(source);
        Map<String, Object> params = new HashMap<>();
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        String restUrl = new IotRestLocator().getRestUrl(IotCoreRestMsgCenter.DEVICE_UPDATE);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, params, null,Boolean.class);

    }
    @RequestMapping(IotCoreRestMsgCenter.PROVIDER_DEVICE_DELETE)
    public RestResponse deviceDelete( @Verify(param = "deviceId",rule="required") Long deviceId) {
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        Map<String, Object> params = new HashMap<>(2);
        params.put("deviceId",deviceId);
        params.put("userId",loginBaseInfo.obtainBaseInfo().getStaffId());
        params.put("userName",loginBaseInfo.obtainBaseInfo().getStaffName());
//        params.put("dataScope",loginUser.getCityDataScope());
        String restUrl = new IotRestLocator().getRestUrl(IotCoreRestMsgCenter.DEVICE_DELETE);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, params, null,Boolean.class);
    }

    //设备历史记录导出
    @RequestMapping(IotCoreRestMsgCenter.PROVIDER_DEVICE_HISTORY_EXPORT)
    public void deviceHistoryExport(@RequestBody DeviceQueryDTO param,HttpServletResponse response) {
        Map<String, Object> paramMap = new HashMap<>();
        String url = new IotRestLocator().getRestUrl(IotCoreRestMsgCenter.DEVICE_BIND_RECORD_LIST);
        param.setPageNo(1);
        param.setPageSize(10000);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, GpsBindHistoryListDTO.class);
        if(restResponse!=null && restResponse.isSuccess()){
            PageDTO pageDTO = (PageDTO)restResponse.getData();
            if (pageDTO.getTotal() > 10000){
                throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
            }
            List<GpsBindHistoryListDTO> gpsBindHistoryList = (List<GpsBindHistoryListDTO>)pageDTO.getResult();
            List<CarGpsRelationListExport> export = buildCarGpsRelationListExport(gpsBindHistoryList);
            exportExcel(export,response);
        }
    }

    private List<CarGpsRelationListExport> buildCarGpsRelationListExport(List<GpsBindHistoryListDTO> gpsBindHistoryList) {
        List<CarGpsRelationListExport> export = new ArrayList<>();
        for(GpsBindHistoryListDTO gpsBindHistory :gpsBindHistoryList){
            List<GpsBindRecordDTO> gpsBindRecordList = gpsBindHistory.getGpsBindRecordList();
            for(GpsBindRecordDTO gpsBindRecord:gpsBindRecordList){
                CarGpsRelationListExport carGpsRelationListExport = new CarGpsRelationListExport();
                carGpsRelationListExport.setSimNo(gpsBindHistory.getSimNo());
                carGpsRelationListExport.setDeviceNo(gpsBindHistory.getDeviceNo());
                //carGpsRelationListExport.setDeviceType(gpsBindHistory.getDeviceType());
                carGpsRelationListExport.setDeviceTypeName(gpsBindHistory.getDeviceTypeName());
                carGpsRelationListExport.setModelName(gpsBindHistory.getModelName());
                carGpsRelationListExport.setBindNum(gpsBindHistory.getBindNum());
                carGpsRelationListExport.setManfactName(gpsBindHistory.getManfactName());
                carGpsRelationListExport.setVehicleLicense(gpsBindRecord.getVehicleLicense());
                carGpsRelationListExport.setVehicleVin(gpsBindRecord.getVehicleVin());
                carGpsRelationListExport.setEngineNum(gpsBindRecord.getEngineNum());
                carGpsRelationListExport.setVehicleModel(gpsBindRecord.getVehicleModel());
                carGpsRelationListExport.setVehicleBrand(gpsBindRecord.getVehicleBrand());
                carGpsRelationListExport.setBindTime(gpsBindRecord.getBindTime());
                carGpsRelationListExport.setUbindTime(gpsBindRecord.getUbindTime());
                carGpsRelationListExport.setBindName(gpsBindRecord.getBindName());
                //carGpsRelationListExport.setBindStatus(gpsBindRecord.getBindStatus());
                carGpsRelationListExport.setBindStatusName(gpsBindRecord.getBindStatusName());
                export.add(carGpsRelationListExport);
            }

        }
        return export;
    }
    private void exportExcel(List<CarGpsRelationListExport> data,HttpServletResponse response){
        List<List<String>> head = new ArrayList<>();
        head.add(Lists.newArrayList("设备号"));
        head.add(Lists.newArrayList("SIM卡号"));
        head.add(Lists.newArrayList("设备类型"));
        head.add(Lists.newArrayList("设备品牌/型号"));
        head.add(Lists.newArrayList("绑定次数"));
        head.add(Lists.newArrayList("设备厂商"));
        head.add(Lists.newArrayList("车牌号"));
        head.add(Lists.newArrayList("车架号"));
        head.add(Lists.newArrayList("发动机号"));
        head.add(Lists.newArrayList("车辆型号"));
        head.add(Lists.newArrayList("车辆品牌"));
        head.add(Lists.newArrayList("绑定时间"));
        head.add(Lists.newArrayList("解绑时间"));
        head.add(Lists.newArrayList("绑定人"));
        head.add(Lists.newArrayList("状态"));
        try (OutputStream outputStream = response.getOutputStream()) {
            response.setContentType("application/vnd.ms-excel");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("设备历史记录导出.xlsx", "utf-8"));
            EasyExcel.write(outputStream)
                    .head(head)
                    .sheet()
                    // 开启相等值 行合并,从行索引1(excel中第二行)开始合并, 指定跨行合并的列索引
                    //.registerWriteHandler(new MergeCellStrategyHandler(true, 1, Sets.newHashSet(0, 1,2,3,4,5)))
                    .doWrite(data);
            log.info("导出设备历史记录成功");
            outputStream.flush();
        }catch (Exception e){
            log.error("导出设备历史记录异常",e);
        }

    }


}
