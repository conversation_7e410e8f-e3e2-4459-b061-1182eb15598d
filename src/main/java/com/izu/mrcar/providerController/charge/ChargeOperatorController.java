package com.izu.mrcar.providerController.charge;

import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.dto.charge.ChargeOperatorOutDTO;
import com.izu.user.dto.charge.ChargeOperatorSaveDTO;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.izu.framework.web.rest.client.BaseHttpClient.POSTBODY_MAP_KEY;

/**
 * 运营商管理-运营商
 *
 * <AUTHOR>
 * @docRoot com.izu.user.controller.charge
 * @Date 2024/5/23 下午4:26
 */
@Api(tags = "聚合充电-运营商")
@RestController("providerChargeOperatorController")
@RequestMapping("/provider/charge/operator")
public class ChargeOperatorController {

    @PostMapping("/updateStatus")
    @ApiOperation(value = "更新运营商状态")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "operatorId", value = "运营商id", required = true),
            @ApiImplicitParam(name = "operatorStatus", value = "运营商状态", required = true, allowableValues = "true,false")
    })
    public RestResponse updateStatus(Integer operatorId, Boolean operatorStatus) {

        final LoginBaseInfo loginInfo = LoginSessionUtil.getBaseLoginInfo();

        ChargeOperatorSaveDTO saveDTO = ChargeOperatorSaveDTO.builder().operatorId(operatorId).operatorStatus(operatorStatus).build();

        saveDTO.setUserId(loginInfo.obtainBaseInfo().getStaffId());
        saveDTO.setUserName(loginInfo.obtainBaseInfo().getStaffName());

        Map<String, Object> httpParams = new HashMap<>();
        httpParams.put(POSTBODY_MAP_KEY, saveDTO);
        String url = new UserRestLocator().getRestUrl("/charge/operator/update");
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, httpParams, null);
    }


    @PostMapping("/queryAllOperator")
    @ApiOperation(value = "查询所有运营商")
    public RestResponse<List<ChargeOperatorOutDTO>> queryAllOperator() {
        String url = new UserRestLocator().getRestUrl("/charge/operator/queryAllOperator");
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, null, null);
    }

    @PostMapping("/queryByPage")
    @ApiOperation(value = "查询分页所有运营商")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "page", value = "当前页", required = true),
            @ApiImplicitParam(name = "pageSize", value = "每页大小", required = true)
    })
    public RestResponse<PageDTO<ChargeOperatorOutDTO>> queryByPage(
            @Verify(rule = "required", param = "page") Integer page,
            @Verify(rule = "required", param = "pageSize") Integer pageSize
    ) {
        String url = new UserRestLocator().getRestUrl("/charge/operator/queryByPage");
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, null, null);
    }

}
