package com.izu.mrcar.providerController.charge;

import com.google.common.collect.Lists;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.exception.RestErrorException;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.dto.ListBillDetailDTO;
import com.izu.mrcar.dto.charge.ChargeOrderExportToMailDTO;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.consts.RequestSourceEnum;
import com.izu.mrcar.order.dto.charge.ChargeOrderDetailDTO;
import com.izu.mrcar.order.dto.charge.ChargeOrderPageDTO;
import com.izu.mrcar.order.dto.charge.ChargeOrderQueryParams;
import com.izu.mrcar.service.charge.ChargeOrderService;
import com.izu.mrcar.service.charge.ProviderChargeOrderExportService;
import com.izu.mrcar.service.user.CompanyService;
import com.izu.mrcar.utils.DateUtil;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.company.CompanyDTO;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.izu.framework.web.rest.client.BaseHttpClient.POSTBODY_MAP_KEY;

/**
 * 运营商管理-运营商
 *
 * <AUTHOR>
 * @docRoot com.izu.user.controller.charge
 * @Date 2024/5/28 下午4:26
 */
@Slf4j
@Api(tags = "聚合充电-订单信息")
@RestController("providerChargeOrderController")
@RequestMapping("/provider/charge/order")
public class ChargeOrderController  {

    @Resource
    private CompanyService companyService;
    @Resource
    private ChargeOrderService chargeOrderService;
    @Resource
    private ProviderChargeOrderExportService providerChargeOrderExportService;

    @PostMapping("/queryByPage")
    @ApiOperation(value = "分页查询订单信息")
    public RestResponse<PageDTO<ChargeOrderPageDTO>> queryByPage(@RequestBody ChargeOrderQueryParams params) {
        Map<String, Object> httpParams = new HashMap<>();
        httpParams.put(POSTBODY_MAP_KEY, params);
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.CHARGE_ORDER_QUERY_BY_PAGE);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, httpParams, null);
    }

    @PostMapping("/detail")
    @ApiOperation(value = "查询订单详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderId", value = "订单id", required = true),
    })
    public RestResponse<ChargeOrderDetailDTO> detail(@Verify(param = "orderId", rule = "required") Integer orderId) {
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.CHARGE_ORDER_DETAIL);
        Map<String, Object> httpParams = new HashMap<>();
        httpParams.put("orderId", orderId);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, httpParams, null);
    }


    @PostMapping(value = "/export")
    @ApiOperation(value = "导出充电订单")
    public RestResponse exportOutBill(@RequestBody ChargeOrderQueryParams params, IzuEasyExcelSession izuEasyExcelSession,
                                 HttpServletResponse response) {
        final LoginBaseInfo loginInfo = LoginSessionUtil.getBaseLoginInfo();
        params.setPageSize(2000);
        try {
            PageDTO pageDTO = providerChargeOrderExportService.getDataByPage(params, 1, 1);
            if (Objects.nonNull(pageDTO)) {
                if (pageDTO.getTotal() > ExportExcelConstants.CHARGE_ORDER_EXPORT_MAX_LINE) {
                    throw ExceptionFactory.createRestException(ErrorCode.STAT_OPERATION_LOG_EXPORT_EXCEED, pageDTO.getTotal(), ExportExcelConstants.CHARGE_ORDER_EXPORT_MAX_LINE);
                }
            }
            izuEasyExcelSession.setPageNo(1);
            izuEasyExcelSession.setUserName(loginInfo.obtainBaseInfo().getStaffName());
            providerChargeOrderExportService.exportToExcel(params, izuEasyExcelSession, response);
            return null;
        } catch (RestErrorException e) {
            log.info("充电订单导出异常", e);
            return RestResponse.create(e.getErrCode(), e.getErrMsg(),false,null);
        }catch (Exception e){
            log.error("充电订单导出异常", e);
            throw e;
        }
    }




    @PostMapping("/listBillDetail")
    @ApiOperation(value = "账单-分页查询充电订单信息")
    public RestResponse<PageDTO<ChargeOrderPageDTO>> listBillDetail(@RequestBody ListBillDetailDTO dto) {
        ChargeOrderQueryParams params = new ChargeOrderQueryParams();
        CompanyDTO companyDTO = companyService.getByCustomerCode(dto.getCompanyCode());
        if(Objects.isNull(companyDTO)){
            return RestResponse.success(new PageDTO<ChargeOrderPageDTO>(dto.getPage(), dto.getPageSize(), 0, Lists.newArrayList()));
        }
        params.setCompanyId(companyDTO.getCompanyId());
        if(Objects.nonNull(dto.getStartDate())){
            params.setOrderEndTimeStart(DateUtil.format(dto.getStartDate(), DateUtil.TIME_FORMAT));
        }
        if(Objects.nonNull(dto.getEndDate())){
            params.setOrderEndTimeEnd(DateUtil.format(dto.getEndDate(), DateUtil.TIME_FORMAT));
        }
        Map<String, Object> httpParams = new HashMap<>();
        httpParams.put(POSTBODY_MAP_KEY, params);
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.CHARGE_ORDER_QUERY_BY_PAGE);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, httpParams, null);
    }

    @PostMapping(value = "/exportToMail")
    @ApiOperation(value = "导出充电订单到邮箱")
    public RestResponse exportToMail(@RequestBody ChargeOrderExportToMailDTO reqDTO,
                                     IzuEasyExcelSession izuEasyExcelSession,
                                     HttpServletResponse response) {
        String lockKey = "mrcar:provider:violation:exportChargeOrder:asyn:";
        return chargeOrderService.exportToMailCommonHandle(reqDTO, izuEasyExcelSession, response, lockKey, RequestSourceEnum.PROVIDER);
    }

}
