package com.izu.mrcar.providerController.charge;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import io.swagger.annotations.ApiModel;
import izu.org.apache.poi.ss.usermodel.BorderStyle;
import izu.org.apache.poi.ss.usermodel.HorizontalAlignment;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单导出信息
 * <p>
 * 2024/6/1 下午3:33
 *
 * <AUTHOR>
 */
@HeadStyle(
        fillForegroundColor = 22,
        fillBackgroundColor = 22
)
@ColumnWidth(20)
@ContentStyle(
        horizontalAlignment = HorizontalAlignment.CENTER,
        borderBottom = BorderStyle.THIN,
        borderLeft = BorderStyle.THIN,
        borderRight = BorderStyle.THIN,
        borderTop = BorderStyle.THIN,
        wrapped = true
)
@Data
@ApiModel("充电账单-入账账单-导出信息")
public class ChargeInBillExportDTO {

    @ExcelProperty(value = "账单日期", order = 1)
    @DateTimeFormat(value = "yyyy-MM-dd")
    private Date billDate;

    @ExcelProperty(value = "订单号", order = 2)
    private String orderCode;

    @ExcelProperty(value = "充电单号", order = 3)
    private String startChargeSeq;

    @ExcelProperty(value = "客户", order = 4)
    private String companyName;

    @ExcelProperty(value = "用户姓名", order = 5)
    private String userName;

    @ExcelProperty(value = "手机号", order = 6)
    private String userPhone;

    @ExcelProperty(value = "车辆类型", order = 6)
    private String paidServiceTypeStr;

    @ExcelProperty(value = "车牌号", order = 7)
    private String vehicleLicense;

    @ExcelProperty(value = "车架号", order = 8)
    private String vehicleVin;

    @ExcelProperty(value = "应收合计（元）", order = 9)
    private BigDecimal totalAmount;

    @ExcelProperty(value = "应收服务费（元）", order = 10)
    private BigDecimal serviceAmount;

    @ExcelProperty(value = "应收电费（元）", order = 11)
    private BigDecimal totalEleAmount;

    @ExcelProperty(value = "充电开始时间", order = 12)
    @DateTimeFormat(value = "yyyy-MM-dd HH:mm:ss")
    private Date chargeStartTime;

    @ExcelProperty(value = "充电结束时间", order = 13)
    @DateTimeFormat(value = "yyyy-MM-dd HH:mm:ss")
    private Date chargeEndTime;

    @ExcelProperty(value = "充电量（kWh）", order = 14)
    private BigDecimal totalPower;

    @ExcelProperty(value = "电站供应商", order = 15)
    private String operatorName;

    @ExcelProperty(value = "站点城市", order = 16)
    private String stationCityName;

    @ExcelProperty(value = "站点名称", order = 17)
    private String chargeStationName;

    @ExcelProperty(value = "签约主体", order = 17)
    private String signingEntityFinancialName;

    @ExcelProperty(value = "销售主体", order = 17)
    private String salesEntityFinancialName;

    @ExcelProperty(value = "机动车所有人", order = 18)
    private String vehicleBelongBussName;

    @ExcelProperty(value = "所有人账套城市", order = 19)
    private String vehicleBelongAccountCityName;

    @ExcelProperty(value = "机动车使用人", order = 20)
    private String vehicleOperateBussName;

    @ExcelProperty(value = "使用人运营城市", order = 21)
    private String vehicleOperateCityName;

    @ExcelProperty(value = "关联长租合同", order = 22)
    private String leaseContractCode;

    @ExcelProperty(value = "关联长租订单号", order = 23)
    private String leaseOrderCode;

    @ExcelProperty(value = "长租订单所有人", order = 24)
    private String leaseOrderOwnerName;

    @ExcelProperty(value = "关联长租预先发车审批", order = 25)
    private String advanceDispatchCarApplyNo;

}
