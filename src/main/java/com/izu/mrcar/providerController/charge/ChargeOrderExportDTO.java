package com.izu.mrcar.providerController.charge;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import izu.org.apache.poi.ss.usermodel.BorderStyle;
import izu.org.apache.poi.ss.usermodel.HorizontalAlignment;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@HeadStyle(
        fillForegroundColor = 22,
        fillBackgroundColor = 22
)
@ColumnWidth(20)
@ContentStyle(
        horizontalAlignment = HorizontalAlignment.CENTER,
        borderBottom = BorderStyle.THIN,
        borderLeft = BorderStyle.THIN,
        borderRight = BorderStyle.THIN,
        borderTop = BorderStyle.THIN,
        wrapped = true
)
@Data
@ApiModel("充电订单导出")
public class ChargeOrderExportDTO {
    @ExcelProperty("订单编号")
    private String orderCode;

    @ExcelProperty("充电单号")
    private String startChargeSeq;

    @ExcelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ExcelProperty("充电状态")
    private String chargeStatusStr;

    @ExcelProperty("订单状态")
    private String orderStatusStr;

    @ExcelProperty("应收合计（元）")
    private BigDecimal totalAmount;

    @ExcelProperty("用户姓名")
    private String userName;

    @ExcelProperty("所属企业")
    private String companyName;

    @ExcelProperty("车辆类型")
    private String paidServiceTypeStr;

    @ExcelProperty("充电车辆")
    private String vehicleLicense;

    @ExcelProperty("电站")
    private String chargeStationName;

    @ExcelProperty("运营商")
    private String operatorName;

    @ExcelProperty("签约主体")
    @ExcelIgnore
    private String signingEntityFinancialName;

    @ExcelProperty("销售主体")
    @ExcelIgnore
    private String salesEntityFinancialName;

    @ExcelProperty("机动车使用人")
    @ExcelIgnore
    private String vehicleOperateBussName;

    @ExcelProperty("机动车所有人")
    @ExcelIgnore
    private String vehicleBelongBussName;

    @ExcelProperty("开始充电时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date chargeStartTime;

    @ExcelProperty("结束充电时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date chargeEndTime;

    @ExcelProperty("使用人运营城市")
    @ExcelIgnore
    private String vehicleOperateCityName;

    @ExcelProperty("长租订单所有人")
    @ExcelIgnore
    private String leaseOrderOwnerName;
}
