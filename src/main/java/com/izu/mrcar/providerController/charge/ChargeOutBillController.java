package com.izu.mrcar.providerController.charge;

import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.excel.mail.AbstractIzuMailExcel;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.dto.charge.ChargeBillPageDTO;
import com.izu.mrcar.order.dto.charge.ChargeBillQueryParams;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

import static com.izu.framework.web.rest.client.BaseHttpClient.POSTBODY_MAP_KEY;
import static com.izu.mrcar.common.constants.ExportExcelConstants.FILE_PATH;

/**
 * 聚合充电-运营端账单查询
 * <p>
 * 2024/5/30 下午10:41
 *
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "聚合充电-账单信息")
@RestController("providerChargeBillController")
@RequestMapping("/provider/charge/bill")
public class ChargeOutBillController implements AbstractIzuMailExcel<ChargeBillQueryParams, ChargeOutBillExportDTO> {


    @PostMapping("/queryOutByPage")
    @ApiOperation(value = "分页查询账单信息")
    public RestResponse<PageDTO<ChargeBillPageDTO>> queryOutByPage(@RequestBody ChargeBillQueryParams params) {
        Map<String, Object> httpParams = new HashMap<>();
        params.setType(2);
        httpParams.put(POSTBODY_MAP_KEY, params);
        String url = new MrcarOrderRestLocator().getRestUrl("/charge/bill/queryByPage");
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, httpParams, null);
    }


    @PostMapping(value = "/exportOutBill")
    @ApiOperation(value = "导出出账账单信息")
    @ExportExcelWeb(fileName = "出账账单.xlsx", filePath = FILE_PATH, sheet = "出账账单", c = ChargeOutBillExportDTO.class)
    public PageDTO exportOutBill(@RequestBody ChargeBillQueryParams params, IzuEasyExcelSession izuEasyExcelSession,
                                 HttpServletResponse response) {
        LoginBaseInfo loginInfo = LoginSessionUtil.getBaseLoginInfo();

        izuEasyExcelSession.setUserName(loginInfo.obtainBaseInfo().getStaffName());

        params.setPage(izuEasyExcelSession.getPageNo());
        params.setPageSize(1000);
        // 初始化数据权限
        try {
            return this.getDataByPage(params, izuEasyExcelSession.getPageNo(), params.getPageSize());
        } catch (Exception e) {
            logger.error("充电账单-入账账单导出异常", e);
            return null;
        }
    }

    @Override
    public PageDTO getDataByPage(Object req, int pageNo, int pageSize) {
        ChargeBillQueryParams params = (ChargeBillQueryParams) req;
        try {
            params.setPage(pageNo);
            params.setPageSize(pageSize);
            Map<String, Object> httpParams = new HashMap<>();
            httpParams.put(POSTBODY_MAP_KEY, params);
            String url = new MrcarOrderRestLocator().getRestUrl("/charge/bill/queryByExport.json");
            RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, httpParams, null, ChargeOutBillExportDTO.class);
            if (restResponse.isSuccess()) {
                return (PageDTO) restResponse.getData();
            } else {
                return null;
            }
        } catch (Exception e) {
            log.error("运营端-充电账单导出-出账账单查询数据异常", e);
            return null;
        }
    }

}
