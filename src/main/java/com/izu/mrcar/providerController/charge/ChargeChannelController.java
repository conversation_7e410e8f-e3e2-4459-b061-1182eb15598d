package com.izu.mrcar.providerController.charge;

import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.user.config.UserRestLocator;
import com.izu.user.dto.charge.ChargeChannelOutDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 充电渠道商
 * <p>
 * 2024/6/12 上午10:26
 *
 * <AUTHOR>
 */
@Api(tags = "聚合充电-渠道商信息")
@RestController("providerChargeChannelController")
@RequestMapping("/provider/charge/channel")
public class ChargeChannelController {

    @RequestMapping("/queryAll")
    @ApiOperation(value = "查询所有渠道商信息")
    public RestResponse<List<ChargeChannelOutDTO>> queryAll() {
        String url = new UserRestLocator().getRestUrl("/charge/channel/queryAll");
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, url, null, null);
    }

}
