package com.izu.mrcar.providerController.publicGovcar;

import com.google.common.collect.Maps;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.order.ExportPDFDTO;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.dto.publicGovcar.*;
import com.izu.mrcar.order.dto.publicGovcar.req.ListPublicGovCarOptLogReqDTO;
import com.izu.mrcar.service.publicGovOrder.PublicGovCarOrderExportService;
import com.izu.mrcar.order.consts.govPublicCar.ProviderPublicGovCarOrderOptReqDTO;
import com.izu.mrcar.order.dto.publicGovcar.PublicGovCarOrderDetailInfoRespDTO;
import com.izu.mrcar.order.dto.publicGovcar.PublicGovCarOrderDetailRespDTO;
import com.izu.mrcar.order.dto.publicGovcar.PublicGovCarOrderQueryDTO;
import com.izu.mrcar.order.dto.publicGovcar.PublicGovCarOrderRespDTO;
import com.izu.mrcar.service.publicGovOrder.PublicGovCarOrderOptLogService;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.perm.provider.ProviderOrderDataPermUtil;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 公务用车对公 app相关接口
 * @date 2024/8/15 10:02
 */
@RestController
@Api(tags = "公务用车对公-订单")
public class PublicGovCarOrderController {

    private static final MrcarOrderRestLocator mrcarOrderRestLocator = new MrcarOrderRestLocator();

    @Resource
    private PublicGovCarOrderExportService orderExportService;

    @ApiImplicitParams({
            @ApiImplicitParam(name="orderNo",value="订单号",required = true),
    })
    @GetMapping(MrcarOrderRestMsgCenter.PROVIDER_PUBLIC_GOV_CAR_ORDER_DETAIL)
    @RequestFunction(functionName = "公务用车对公-审批订单详情")
    @ApiOperation(value = "审批订单详情",notes = "作者：丁伟兵")
    public RestResponse<PublicGovCarOrderDetailRespDTO> getDetail(@RequestParam("orderNo") String orderNo) {
        String url = mrcarOrderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.PROVIDER_PUBLIC_GOV_CAR_ORDER_DETAIL);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("orderNo", orderNo);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.GET, url, paramMap, null);
    }


    @PostMapping(MrcarOrderRestMsgCenter.PROVIDER_PUBLIC_GOV_CAR_ORDER_LIST)
    @RequestFunction(functionName = "公务用车对公-订单列表")
    @ApiOperation(value = "订单列表")
    public RestResponse<PageDTO<PublicGovCarOrderRespDTO>> getList(@RequestBody PublicGovCarOrderQueryDTO reqDTO) {
        String restUrl =mrcarOrderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.PROVIDER_PUBLIC_GOV_CAR_ORDER_LIST);
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        ProviderOrderDataPermUtil.setDataPerm(reqDTO);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, PublicGovCarOrderRespDTO.class);
    }


    @GetMapping(MrcarOrderRestMsgCenter.PROVIDER_PUBLIC_GOV_CAR_ORDER_INFO_DETAIL)
    @RequestFunction(functionName = "公务用车对公-订单详情")
    @ApiOperation(value = "订单详情")
    public RestResponse<PublicGovCarOrderDetailInfoRespDTO> getOrderInfoDetail(@RequestParam("orderNo") String orderNo) {
        String restUrl =mrcarOrderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.PROVIDER_PUBLIC_GOV_CAR_ORDER_INFO_DETAIL);
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put("orderNo", orderNo);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.GET, restUrl, paramMap, null);
    }


    /**
     * @param reqDTO
     * @param izuEasyExcelSession
     * @param request
     * @param response
     * @return
     * @Description: 公务用车对公-订单列表导出
     */
    @PostMapping(value = MrcarOrderRestMsgCenter.PROVIDER_PUBLIC_GOV_CAR_ORDER_LIST_EXPORT)
    @RequestFunction(functionName = "公务用车对公-订单列表导出")
    @ApiOperation(value = "公务用车对公-订单列表导出", notes = "作者：任伟光")
    public void exportGovPublicOrderList(@RequestBody PublicGovCarOrderQueryDTO reqDTO,
                                               IzuEasyExcelSession izuEasyExcelSession,
                                               HttpServletRequest request, HttpServletResponse response) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        ProviderOrderDataPermUtil.setDataPerm(reqDTO);
        izuEasyExcelSession.setUserName(providerLoginInfo.getBaseInfo().getStaffName());
        reqDTO.setPage(1);
        reqDTO.setPageSize(10000);
        orderExportService.exportGovPublicOrderList(reqDTO, izuEasyExcelSession, request, response);
    }

    @PostMapping("/provider/publicGovCar/order/forceFinishTrip")
    @RequestFunction(functionName = "公务用车对公-订单强制结束行程")
    @ApiOperation(value = "公务用车对公-订单强制结束行程",notes = "作者：叶鹏鹏")
    public RestResponse<Boolean> orderForceFinishTrip(@RequestBody ProviderPublicGovCarOrderOptReqDTO reqDTO) {
        setOrderOptCommonInfo(reqDTO);
        String url = mrcarOrderRestLocator.getRestUrl("/provider/publicGovCar/order/forceFinishTrip");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, Boolean.class);

    }


    @PostMapping("/provider/publicGovCar/order/cancelTrip")
    @RequestFunction(functionName = "公务用车对公-订单取消行程")
    @ApiOperation(value = "公务用车对公-订单取消行程",notes = "作者：叶鹏鹏")
    public RestResponse<Boolean> orderCancelTrip(@RequestBody ProviderPublicGovCarOrderOptReqDTO reqDTO) {
        setOrderOptCommonInfo(reqDTO);
        String url = mrcarOrderRestLocator.getRestUrl("/provider/publicGovCar/order/cancelTrip");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, Boolean.class);

    }

	@ApiImplicitParams({
            @ApiImplicitParam(name="orderNo",value="订单号",required = true),
    })
    @PostMapping(MrcarOrderRestMsgCenter.PROVIDER_PUBLIC_GOV_CAR_EXPORT_DELIVERY_PDF)
    @RequestFunction(functionName = "下载派车单")
    @ApiOperation(value = "下载派车单",notes = "作者：及晓林")
    @SuppressWarnings("unchecked")
    public RestResponse<ExportPDFDTO> exportDeliveryPdf(@RequestParam("orderNo") String orderNo) {
        String restUrl =mrcarOrderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.PROVIDER_PUBLIC_GOV_CAR_EXPORT_DELIVERY_PDF);
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put("orderNo", orderNo);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paramMap, null, ExportPDFDTO.class);
    }

    private void setOrderOptCommonInfo(ProviderPublicGovCarOrderOptReqDTO reqDTO){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        reqDTO.setCompanyId(providerLoginInfo.obtainBelongCompanyId());
        reqDTO.setCompanyCode(providerLoginInfo.obtainBelongCompanyCode());
        reqDTO.setCompanyName(providerLoginInfo.obtainBelongCompanyName());
        reqDTO.setStaffId(providerLoginInfo.getBaseInfo().getStaffId());
        reqDTO.setStaffName(providerLoginInfo.getBaseInfo().getStaffName());
        reqDTO.setStaffCode(providerLoginInfo.getBaseInfo().getStaffCode());
        reqDTO.setLoginUserMobile(providerLoginInfo.getBaseInfo().getMobile());
    }


    @Resource
    private PublicGovCarOrderOptLogService publicGovCarOrderOptLogService;

    @PostMapping("/provider/publicGovCar/order/listOptLog")
    @RequestFunction(functionName = "查询公务车订单操作日志列表")
    @ApiOperation(value = "查询公务车订单操作日志列表")
    public RestResponse<PageDTO<List<PublicGovCarOrderOperationLogDTO>>> listOptLog(@RequestBody ListPublicGovCarOptLogReqDTO reqDTO) {
        return publicGovCarOrderOptLogService.listOptLog(reqDTO);
    }


}
