package com.izu.mrcar.providerController.bus.route;

import com.izu.config.MrCarConfigRestLocator;
import com.izu.config.dto.busroute.*;
import com.izu.consts.ConfigURI;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.controller.bus.route.BusRouteModelExportService;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.enums.perm.ClientDataPermTypeEnum;
import com.izu.user.enums.perm.ProviderDataPermTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 大巴车-路线（运营端）
 */
@RestController
public class BusRouteProviderController {


    @Autowired
    private BusRouteModelExportService busRouteModelExportService;

    /**
     * 大巴车-路线列表-运营端-分页
     *
     * @param param 请求参数
     * @return 路线基础信息
     * <AUTHOR>
     */
    @PostMapping(ConfigURI.BUS_ROUTE_PROVIDER_PAGE_LIST)
    public RestResponse<PageDTO<BusRouteDTO>> getRoutePageList(@RequestBody BusRoutePageQueryReqDTO param) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        param.setLoginUserId(providerLoginInfo.obtainBaseInfo().getStaffId());
        param.setLoginUserName(providerLoginInfo.obtainBaseInfo().getStaffName());
        param.setLoginCompanyId(providerLoginInfo.obtainBelongCompanyId());
        param.setLoginSystemType(providerLoginInfo.getSystemType());
        ProviderDataPermTypeEnum dataPermTypeEnum = ProviderDataPermTypeEnum.getByType(providerLoginInfo.obtainDataPerm().getDataPermType());
        param.setDataPermType(dataPermTypeEnum.getType());
        String url = new MrCarConfigRestLocator().getRestUrl(ConfigURI.BUS_ROUTE_PAGE_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, BusRouteDTO.class);
    }


    /**
     * 大巴车-路线列表-运营端-不分页
     *
     * @param param 查询参数
     */
    @PostMapping(ConfigURI.BUS_ROUTE_PROVIDER_LIST)
    public RestResponse<List<BusRouteDTO>> getRouteList(@RequestBody BusRouteQueryReqDTO param) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        param.setLoginUserId(providerLoginInfo.obtainBaseInfo().getStaffId());
        param.setLoginUserName(providerLoginInfo.obtainBaseInfo().getStaffName());
        param.setLoginCompanyId(providerLoginInfo.obtainBelongCompanyId());
        param.setLoginSystemType(providerLoginInfo.getSystemType());
        ProviderDataPermTypeEnum dataPermTypeEnum = ProviderDataPermTypeEnum.getByType(providerLoginInfo.obtainDataPerm().getDataPermType());
        param.setDataPermType(dataPermTypeEnum.getType());
        String url = new MrCarConfigRestLocator().getRestUrl(ConfigURI.BUS_ROUTE_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, BusRouteDTO.class);
    }


    /**
     * 大巴车-路线站点列表-运营端
     *
     * @param id 线路主键
     * @return 线路明细信息
     * <AUTHOR>
     */
    @GetMapping(ConfigURI.BUS_ROUTE_PROVIDER_STATION_LIST)
    public RestResponse<BusRouteDetailDTO> getDetail(@Verify(param = "id", rule = "required") Integer id) {
        String url = new MrCarConfigRestLocator().getRestUrl(ConfigURI.BUS_ROUTE_STATION_LIST);
        Map<String, Object> paramMap = new HashMap<>(1);
        paramMap.put("id", id);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.GET, url, paramMap, null, BusRouteDetailDTO.class);
    }


    /**
     * 大巴车-路线导出-运营端
     *
     * @param param
     * @param izuEasyExcelSession
     * @param request
     * @param response
     * @return
     */

    @PostMapping(ConfigURI.BUS_ROUTE_PROVIDER_EXPORT)
    public void exprotBusRoute(@RequestBody BusRoutePageQueryReqDTO param,
                               IzuEasyExcelSession izuEasyExcelSession,
                               HttpServletRequest request,
                               HttpServletResponse response) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        param.setLoginUserId(providerLoginInfo.obtainBaseInfo().getStaffId());
        param.setLoginUserName(providerLoginInfo.obtainBaseInfo().getStaffName());
        param.setLoginCompanyId(providerLoginInfo.obtainBelongCompanyId());
        param.setLoginSystemType(providerLoginInfo.getSystemType());
        ProviderDataPermTypeEnum dataPermTypeEnum = ProviderDataPermTypeEnum.getByType(providerLoginInfo.obtainDataPerm().getDataPermType());
        param.setDataPermType(dataPermTypeEnum.getType());
        izuEasyExcelSession.setUserName(providerLoginInfo.getBaseInfo().getStaffName());
        busRouteModelExportService.exportBusRouteList(param, izuEasyExcelSession, request, response);
    }


}
