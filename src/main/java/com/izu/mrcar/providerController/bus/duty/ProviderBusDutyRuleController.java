package com.izu.mrcar.providerController.bus.duty;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.izu.config.MrCarConfigRestLocator;
import com.izu.config.dto.bus.*;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.utils.DataPermUtil;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.company.CompanyNameRespDTO;
import com.izu.user.dto.staff.pc.*;
import com.izu.user.enums.AssociateCompanyTypeEnum;
import com.izu.user.enums.perm.ClientDataPermTypeEnum;
import io.swagger.annotations.Api;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

import static com.izu.mrcar.common.errorcode.ErrorCode.STAT_FUNCTION_OPERATION_EXPORT_EXCEED;

/**
 * 大巴车排班规则
 */
@RestController
@RequestMapping("/provider/bus/duty/rule")
@Api(tags = "大巴车排班规则")
public class ProviderBusDutyRuleController {

    private final MrCarConfigRestLocator locator = new MrCarConfigRestLocator();

    /**
     * 分页查询(运营端)
     */
    @PostMapping("/search")
    @SuppressWarnings("unchecked")
    public RestResponse<PageDTO<BusDutyRuleRespDTO>> search(@RequestBody BusDutyRuleSearchDTO searchDTO) {
        // 权限
        DataPermUtil.putDataPerm(searchDTO);
        // restful
        Map<String, Object> params = new HashMap<>();
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, searchDTO);
        String restUrl = locator.getRestUrl("/bus/duty/rule/search");
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, params, null, BusDutyRuleRespDTO.class);
    }

    /**
     * 启用/停用(运营端)
     */
    @PostMapping("/updateStatus")
    @SuppressWarnings("unchecked")
    public RestResponse<Boolean> updateStatus(Integer id,
                                              Integer status) {
        BusDutyRuleReqDTO reqDTO = new BusDutyRuleReqDTO();
        reqDTO.setId(id);
        reqDTO.setDisabledStatus(status);
        // 权限
        DataPermUtil.putDataPerm(reqDTO);
        // restful
        Map<String, Object> params = new HashMap<>();
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        String restUrl = locator.getRestUrl("/bus/duty/rule/updateStatus");
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, params, null, Boolean.class);
    }

    /**
     * 导出(运营端)
     */
    @PostMapping("/export")
    public void export(@RequestBody BusDutyRuleSearchDTO searchDTO,
                       HttpServletResponse response) throws IOException {
        // 权限
        DataPermUtil.putDataPerm(searchDTO);

        int page = 1;
        int pageSize = 100;
        int pages;

        List<ProviderBusDutyRuleExportDTO> result = new ArrayList<>();

        do {
            searchDTO.setPage(page);
            searchDTO.setPageSize(pageSize);

            String url = locator.getRestUrl("/bus/duty/rule/search");
            Map<String, Object> params = new HashMap<>();
            params.put(BaseHttpClient.POSTBODY_MAP_KEY, searchDTO);
            @SuppressWarnings("unchecked")
            RestResponse<PageDTO<BusDutyRuleRespDTO>> restResponse =
                    RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, params, null,
                            BusDutyRuleRespDTO.class);
            PageDTO<BusDutyRuleRespDTO> pageDTO = restResponse.getData();

            // 校验记录条数
            if (pageDTO.getTotal() > ExportExcelConstants.EXPORT_MAX_LINE) {
                RestResponse<?> rest = RestResponse.fail(STAT_FUNCTION_OPERATION_EXPORT_EXCEED,
                        pageDTO.getTotal(), ExportExcelConstants.EXPORT_MAX_LINE);
                JSON.writeJSONString(response.getOutputStream(), rest);
                response.setContentType("application/json");
                return;
            }

            // 设置页码信息
            pages = pageDTO.getPages();
            result.addAll(
                    pageDTO.getResult()
                            .stream()
                            .map(s -> {
                                ProviderBusDutyRuleExportDTO dto = new ProviderBusDutyRuleExportDTO();
                                BeanUtils.copyProperties(s, dto);
                                // 是否跳过节假日
                                dto.setSkipHolidayStr(Objects.equals(s.getSkipHoliday(), 0) ? "否" : "是");
                                return dto;
                            }).collect(Collectors.toList()));
            page++;
        } while (page <= pages);

        String fileName = URLEncoder.encode("排班规则", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), ProviderBusDutyRuleExportDTO.class)
                .sheet("排班规则").doWrite(result);
    }

}
