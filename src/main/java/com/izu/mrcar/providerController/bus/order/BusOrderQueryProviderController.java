package com.izu.mrcar.providerController.bus.order;

import com.google.common.collect.Maps;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.dto.DicKeyValueDTO;
import com.izu.mrcar.order.ExportPDFDTO;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.consts.BusOrderEnum;
import com.izu.mrcar.order.dto.mrcar.bus.req.BusOrderListQueryDTO;
import com.izu.mrcar.order.dto.mrcar.bus.resp.BusOrderDetailRespDTO;
import com.izu.mrcar.order.dto.mrcar.bus.resp.BusOrderListRespDTO;
import com.izu.mrcar.service.bus.order.BusOrderExportService;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 大巴车列表详情-运营端
 * <AUTHOR>
 */
@RestController
public class BusOrderQueryProviderController {

    private static final MrcarOrderRestLocator mrcarOrderRestLocator = new MrcarOrderRestLocator();

    @Resource
    private BusOrderExportService busOrderExportService;

    /**
     * 运营端-大巴车订单列表
     */
    @PostMapping(MrcarOrderRestMsgCenter.PROVIDER_BUS_ORDER_LIST)
    public RestResponse<PageDTO<BusOrderListRespDTO>> getList(@RequestBody BusOrderListQueryDTO reqDTO) {
        String restUrl =mrcarOrderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.PROVIDER_BUS_ORDER_LIST);
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        reqDTO.setDataPermType(providerLoginInfo.obtainDataPerm().getDataPermType());
        reqDTO.setSystemType(providerLoginInfo.getSystemType());
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, BusOrderListRespDTO.class);
    }

    /**
     * 运营端-大巴车订单详情
     */
    @GetMapping(MrcarOrderRestMsgCenter.PROVIDER_BUS_ORDER_DETAIL)
    public RestResponse<BusOrderDetailRespDTO> getOrderInfoDetail(@RequestParam("orderNo") String orderNo) {
        String restUrl = mrcarOrderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.PROVIDER_BUS_ORDER_DETAIL);
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put("orderNo", orderNo);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.GET, restUrl, paramMap, null);
    }


    /**
     * 运营端-大巴车结算单
     */
    @PostMapping(MrcarOrderRestMsgCenter.PROVIDER_BUS_ORDER_EXPORT_PDF)
    public RestResponse<ExportPDFDTO> exportDeliveryPdf(@RequestParam("orderNo") String orderNo) {
        String restUrl =mrcarOrderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.PROVIDER_BUS_ORDER_EXPORT_PDF);
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put("orderNo", orderNo);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paramMap, null, ExportPDFDTO.class);
    }

    /**
     * 运营端-大巴车订单状态枚举
     */
    @GetMapping(MrcarOrderRestMsgCenter.PROVIDER_BUS_ORDER_STATUS_ENUM)
    public RestResponse<List<DicKeyValueDTO>> getOrderStatusEnum() {
        List<DicKeyValueDTO> collect = Arrays.stream(BusOrderEnum.OrderStatus.values())
                .map(c -> new DicKeyValueDTO(c.getCode(), c.getDesc()))
                .collect(Collectors.toList());
        return RestResponse.success(collect);
    }

    /**
     * 运营端-大巴车订单导出
     */
    @PostMapping(value = MrcarOrderRestMsgCenter.PROVIDER_BUS_ORDER_EXPORT)
    public void exportBusOrderList(@RequestBody BusOrderListQueryDTO reqDTO,
                                   IzuEasyExcelSession izuEasyExcelSession,
                                   HttpServletRequest request, HttpServletResponse response) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        reqDTO.setPage(1);
        reqDTO.setPageSize(500);
        izuEasyExcelSession.setUserName(providerLoginInfo.getBaseInfo().getStaffName());
        reqDTO.setDataPermType(providerLoginInfo.obtainDataPerm().getDataPermType());
        reqDTO.setSystemType(providerLoginInfo.getSystemType());
        busOrderExportService.exportBusOrderList(reqDTO, izuEasyExcelSession, request, response);
    }
}
