package com.izu.mrcar.providerController.oilConsumption;

import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.oilConsumption.AppCarRefuelingRecordReqDTO;
import com.izu.asset.dto.oilConsumption.CarRefuelingRecordDetailDTO;
import com.izu.asset.dto.oilConsumption.CarRefuelingRecordExportDTO;
import com.izu.asset.dto.oilConsumption.CarRefuelingRecordPageReqDTO;
import com.izu.asset.util.ObjectTransferUtil;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.controller.export.CarRefuelingRecordExport;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.perm.AssetDataPermUtil;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.enums.perm.ProviderDataPermTypeEnum;
import com.izu.user.util.SingletonFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;


/**
 * 加油记录-运营端
 */
@RestController
public class CarRefuelingRecordProviderController {
    protected static final MrCarAssetRestLocator mrCarAssetRestLocator = SingletonFactory.getSingleton(SingletonFactory.assetRestLocator, MrCarAssetRestLocator::new);

    /**
     * 分页查询-运营端
     *
     * @param dto
     * @return
     */
    @PostMapping(MrCarAssetRestCenter.CAR_REFUELING_RECORD_PROVIDER_LIST_PAGE)
    public RestResponse<PageDTO<CarRefuelingRecordExportDTO>> listPage(@RequestBody CarRefuelingRecordPageReqDTO dto) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        ProviderDataPermTypeEnum dataPermTypeEnum = ProviderDataPermTypeEnum.getByType(providerLoginInfo.obtainDataPerm().getDataPermType());
        if (dataPermTypeEnum.getType() != 6) {//非所有权限，展示为空
            return RestResponse.success(null);
        }
        Map<String, Object> params = new HashMap<>(1);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CAR_REFUELING_RECORD_LIST_PAGE_EXPORT), params, null, Map.class);
    }


    /**
     * 查看详情-运营端
     *
     * @param dto
     * @return
     */
    @PostMapping(MrCarAssetRestCenter.CAR_REFUELING_RECORD_PROVIDER_DETAIL)
    public RestResponse<CarRefuelingRecordDetailDTO> detail(@RequestBody AppCarRefuelingRecordReqDTO dto) {
        Map<String, Object> params = new HashMap<>(1);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CAR_REFUELING_RECORD_DETAIL), params, null, CarRefuelingRecordDetailDTO.class);
    }


    /**
     * 导出-运营端
     *
     * @param dto
     * @param izuEasyExcelSession
     * @param request
     * @param response
     * @return
     */
    @PostMapping(MrCarAssetRestCenter.CAR_REFUELING_RECORD_PROVIDER_LIST_PAGE_EXPORT)
    @ExportExcelWeb(fileName = ExportExcelConstants.FILE_NAME_CAR_REFUELING_RECORD + ExportExcelConstants.XLSX, filePath = ExportExcelConstants.FILE_PATH,
            sheet = ExportExcelConstants.FILE_NAME_CAR_REFUELING_RECORD, c = CarRefuelingRecordExport.class)
    public PageDTO export(@RequestBody CarRefuelingRecordPageReqDTO dto, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        ProviderDataPermTypeEnum dataPermTypeEnum = ProviderDataPermTypeEnum.getByType(providerLoginInfo.obtainDataPerm().getDataPermType());
        if (dataPermTypeEnum.getType() != 6) {//非所有权限，展示为空
            return null;
        }
        dto.setPage(izuEasyExcelSession.getPageNo());
        izuEasyExcelSession.setUserName(dto.getStaffName());
        dto.setPageSize(1000);
        Map<String, Object> params = new HashMap<>();
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CAR_REFUELING_RECORD_LIST_PAGE_EXPORT), params, null, CarRefuelingRecordExport.class);
        if (restResponse != null && restResponse.isSuccess()) {
            return ObjectTransferUtil.cast(restResponse.getData());
        }
        return null;
    }
}
