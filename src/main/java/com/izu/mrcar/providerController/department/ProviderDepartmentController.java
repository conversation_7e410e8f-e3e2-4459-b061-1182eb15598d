package com.izu.mrcar.providerController.department;

import com.google.common.collect.Lists;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.controller.base.UserBaseController;
import com.izu.user.config.UserRestLocator;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.dto.CompanyDepartmentDTO;
import com.izu.user.dto.DepartmentTrajectoryItemDTO;
import com.izu.user.dto.dept.DeptTrajectoryQueryReqDTO;
import com.izu.user.dto.dept.ListValidDeptByCompanyIdReqDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/8/6 8:43
 */
@RestController
@Api(tags = "部门信息")
public class ProviderDepartmentController extends UserBaseController {

    @ApiOperation(value = "部门信息-列表")
    @RequestMapping(value = UserUrlCenter.PROVIDER_QUERY_DEPARTMENT_TREE,method = {RequestMethod.GET,RequestMethod.POST})
    @RequestFunction(functionName = "部门信息-列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name="status",value="部门状态 1 :启用 ，不传时：返回启用和禁用",paramType="form"),
            @ApiImplicitParam(name="companyId",value="公司id",paramType="form"),
    })
    public RestResponse<List<CompanyDepartmentDTO>> departmentTree(Byte status,Integer companyId){
        UserRestLocator locator = new UserRestLocator();
        Map<String, Object> params = new HashMap<>();
        params.put("status",status);
        params.put("companyId",companyId);
        String restUrl = locator.getRestUrl("/department/tree");
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, params, null);
    }

    @ApiOperation(value = "获取所在公司的所有员工下拉框")
    @RequestFunction(functionName = "获取所在公司的所有员工下拉框")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "customerName", value = "员工名称-模糊搜索", required = true, paramType = "form"),
            @ApiImplicitParam(name = "customerStatus", value = "员工状态;1-启用;4-停用", required = true, paramType = "form"),
            @ApiImplicitParam(name = "companyId", value = "公司id", required = true, paramType = "form"),
    })
    @GetMapping(UserUrlCenter.PROVIDER_STAFF_MANAGE_GET_ALL_CUSTOMER)
    public RestResponse getAllCustomer(Byte customerStatus, String customerName,Integer companyId) {
        final HashMap<String, Object> restParam = new HashMap<String, Object>() {{
            put("companyId", companyId);
            put("customerName", customerName);
            put("customerStatus", customerStatus);
        }};
        return post(UserUrlCenter.CLIENT_STAFF_MANAGE_GET_ALL_CUSTOMER, restParam);
    }

    @RequestFunction(functionName = "根据企业id获取两级部门信息")
    @ApiOperation(value = "根据企业id获取两级部门信息")
    @PostMapping("/provider/department/listTwoLevelDeptByCompanyId")
    public RestResponse<List<CompanyDepartmentDTO>> listTwoLevelDeptByCompanyId(@RequestBody ListValidDeptByCompanyIdReqDTO reqDTO) {
        Map<String, Object> param = new HashMap<>();
        reqDTO.setLevelList(Lists.newArrayList(1, 2));
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        UserRestLocator locator = new UserRestLocator();
        String restUrl = locator.getRestUrl(UserUrlCenter.DEPARTMENT_LISTVALIDDEPTBYCOMPANYID);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null, CompanyDepartmentDTO.class);
    }

    @RequestFunction(functionName = "轨迹染色-左侧部门树查看")
    @ApiOperation(value = "轨迹染色-左侧部门树查看")
    @PostMapping(UserUrlCenter.PROVIDER_DEPARTMENT_TRAJECTORY_LIST)
    public RestResponse<List<DepartmentTrajectoryItemDTO>> getDeptTrajectoryList(@RequestBody DeptTrajectoryQueryReqDTO deptTrajectoryQueryReqDTO) {
        return postBodyWithLogin(UserUrlCenter.CLIENT_DEPARTMENT_TRAJECTORY_LIST, deptTrajectoryQueryReqDTO);
    }

}
