package com.izu.mrcar.providerController.department;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.style.row.SimpleRowHeightStyleStrategy;
import com.izu.framework.doc.JrdApiDoc;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.controller.base.UserBaseController;
import com.izu.mrcar.controller.maintain.garage.CustomCellWriteHandler;
import com.izu.mrcar.controller.maintain.garage.MaintainImportDTO;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.dto.CompanyDepartmentDTO;
import com.izu.user.dto.ImportErrorDTO;
import com.izu.user.dto.company.CompanyDepartmentImportDTO;
import com.izu.user.dto.dept.*;
import com.izu.user.dto.driver.BatchDTO;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.enums.perm.ClientDataPermTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/8/6 8:43
 */
@RestController
@Slf4j
@Api(tags = "部门信息-v2")
public class ProviderDepartmentV2Controller extends UserBaseController {

    @ApiOperation(value = "运营端-部门信息-列表")
    @PostMapping(UserUrlCenter.PROVIDER_V2_DEPARTMENT_LIST)
    @RequestFunction(functionName = "运营端-部门信息-列表")
    public RestResponse<List<CompanyDepartmentDTO>> departmentTree(@RequestBody DeptQueryReqDTO reqDTO){
        if(reqDTO.getCompanyId()==null){
            return RestResponse.fail(RestErrorCode.HTTP_PARAM_INVALID, "请选择企业");
        }
        UserRestLocator locator = new UserRestLocator();
        Map<String, Object> params = new HashMap<>();
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        reqDTO.setLoginSystemType(providerLoginInfo.getSystemType());
        reqDTO.setLoginCompanyId(reqDTO.getCompanyId());
        params.put(BaseHttpClient.POSTBODY_MAP_KEY,reqDTO);
        String restUrl = locator.getRestUrl(UserUrlCenter.CLIENT_DEPARTMENT_LIST);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, params, null);
    }
    @ApiOperation(value = "运营端-部门管理-员工列表")
    @RequestFunction(functionName = "运营端-部门管理-员工列表")
    @PostMapping(UserUrlCenter.PROVIDER_V2_DEPARTMENT_MANAGE_STAFF_LIST)
    public RestResponse<PageDTO<DeptStaffListRespDTO>> staffList(@RequestBody DeptStaffListReqDTO reqDTO) {
        UserRestLocator locator = new UserRestLocator();
        Map<String, Object> params = new HashMap<>();
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        reqDTO.setLoginSystemType(providerLoginInfo.getSystemType());
        reqDTO.setDataPermType(ClientDataPermTypeEnum.SELF_COMPANY.getType());
        params.put(BaseHttpClient.POSTBODY_MAP_KEY,reqDTO);
        String restUrl = locator.getRestUrl(UserUrlCenter.CLIENT_DEPARTMENT_MANAGE_STAFF_LIST);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, params,  null);
    }

    @ApiOperation(value = "获取所在公司的所有员工下拉框")
    @RequestFunction(functionName = "获取所在公司的所有员工下拉框")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "customerName", value = "员工名称-模糊搜索", required = true, paramType = "form"),
            @ApiImplicitParam(name = "customerStatus", value = "员工状态;1-启用;4-停用", required = true, paramType = "form"),
            @ApiImplicitParam(name = "companyId", value = "公司id", required = true, paramType = "form"),
    })
    @GetMapping(UserUrlCenter.PROVIDER_V2_STAFF_MANAGE_GET_ALL_CUSTOMER)
    public RestResponse getAllCustomer(Byte customerStatus, String customerName,Integer companyId) {
        final HashMap<String, Object> restParam = new HashMap<String, Object>() {{
            put("companyId", companyId);
            put("customerName", customerName);
            put("customerStatus", customerStatus);
        }};
        return post(UserUrlCenter.CLIENT_STAFF_MANAGE_GET_ALL_CUSTOMER, restParam);
    }

    @GetMapping(UserUrlCenter.PROVIDER_V2_SAVE_OR_UPDATE_DEPT)
    @RequestFunction(functionName = "部门信息-编辑|增加下级机构")
    @ApiOperation(value = "部门信息-新增编辑")
    @ApiImplicitParams({
            @ApiImplicitParam(name="id",value="部门id",required=true,paramType="form"),
            @ApiImplicitParam(name="departmentName",value="部门名称",required=true,paramType="form"),
            @ApiImplicitParam(name="parentId",value="父级部门id",required=false,paramType="form"),
            @ApiImplicitParam(name="leaderIds",value="负责人id，多个英文逗号间隔",required=false,paramType="form"),
            @ApiImplicitParam(name="companyId",value="企业id",required=true,paramType="form"),
            @ApiImplicitParam(name="companyCode",value="企业code",required=true,paramType="form"),
    })
    public RestResponse saveDepartmentInfo(DeptSaveReqDTO reqDTO) {
        UserRestLocator locator = new UserRestLocator();
        Map<String, Object> params = new HashMap<>();
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        reqDTO.setLoginCompanyId(reqDTO.getCompanyId());
        reqDTO.setCompanyCode(reqDTO.getCompanyCode());
        reqDTO.setLoginUserId(providerLoginInfo.getBaseInfo().getStaffId());
        reqDTO.setLoginUserName(providerLoginInfo.getBaseInfo().getStaffName());
        String restUrl = locator.getRestUrl("/department/save");
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, BeanUtil.beanToMap(reqDTO),  null);
    }

    @ApiOperation(value = "运营端-部门信息-列表")
    @RequestMapping(value = UserUrlCenter.PROVIDER_V2_QUERY_DEPARTMENT_TREE,method = {RequestMethod.GET,RequestMethod.POST})
    @RequestFunction(functionName = "部门信息-列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name="status",value="部门状态 1 :启用 ，不传时：返回启用和禁用",paramType="form"),
            @ApiImplicitParam(name="companyId",value="公司id",paramType="form"),
    })
    public RestResponse<List<CompanyDepartmentDTO>> departmentTree(Byte status,Integer companyId){
        UserRestLocator locator = new UserRestLocator();
        Map<String, Object> params = new HashMap<>();
        params.put("status",status);
        params.put("companyId",companyId);
        String restUrl = locator.getRestUrl("/department/tree");
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, params, null);
    }

    @RequestFunction(functionName = "部门信息-批量设置角色和数据权限")
    @ApiOperation(value = "部门信息-批量设置角色和数据权限")
    @PostMapping(UserUrlCenter.PROVIDER_BATCH_UPDATE_ROLE_AND_DATA_PERMISSION)
    public RestResponse<Boolean> batchUpdateRoleAndDataPermission(@RequestBody RoleAndDataPermissionReqDTO roleAndDataPermissionReqDTO) {
        return postBodyWithLogin(UserUrlCenter.BATCH_UPDATE_ROLE_AND_DATA_PERMISSION, roleAndDataPermissionReqDTO);
    }

    @PostMapping(UserUrlCenter.PROVIDER_DEPARTMENT_DOWNLOAD_IMPORT_TEMPLATE)
    @RequestFunction(functionName = "部门信息-导入部门模板下载")
    @ApiOperation("部门信息-导入部门模板下载")
    public void downloadImportTemplate(HttpServletResponse response) throws Exception {
        // 导出模板名称
        String fileName = "部门导入模板";

        // 设置响应类型（推荐设置为xlsx标准MIME）
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");

        // 防止中文乱码（兼容大部分浏览器）
        String fileNameEncode = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''"  + fileNameEncode + ExcelTypeEnum.XLSX.getValue());

        // 模板数据
        List<MaintainImportDTO> list = new ArrayList<>();

        // 写入 Excel（注意：不要在写入前有任何输出）
        try (ServletOutputStream out = response.getOutputStream()) {
            EasyExcelFactory.write(out)
                    .registerWriteHandler(new CustomCellWriteHandler())
                    .registerWriteHandler(new SimpleRowHeightStyleStrategy((short) 35, (short) 22))
                    .head(CompanyDepartmentImportDTO.class)
                    .excelType(ExcelTypeEnum.XLSX)
                    .sheet("导入部门")
                    .doWrite(list);
        } catch (Exception e) {
            log.error("导出Excel失败", e);
            throw e;
        }
    }
    @RequestFunction(functionName = "部门信息-禁用")
    @ApiOperation(value = "部门信息-禁用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "deptId", value = "部门id", required = true, paramType = "form")
    })
    @PostMapping(UserUrlCenter.PROVIDER_DEPARTMENT_MANAGE_DISABLE)
    public RestResponse disable(@Verify(param = "deptId", rule = "required") Integer deptId) {
        final LoginBaseInfo loginInfo = LoginSessionUtil.getBaseLoginInfo();
        final HashMap<String, Object> hashMap = new HashMap<String, Object>() {{
            put("deptId", deptId);
            put("loginUserId", loginInfo.obtainBaseInfo().getStaffId());
            put("loginUserName", loginInfo.obtainBaseInfo().getStaffName());
        }};
        RestResponse restResponse = post(UserUrlCenter.CLIENT_DEPARTMENT_MANAGE_DISABLE, hashMap);
        if(restResponse.isSuccess()){
            restResponse.setMsg("部门停用成功");
        }
        return restResponse;

    }


    @RequestFunction(functionName = "部门信息-启用")
    @ApiOperation(value = "部门信息-启用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "deptId", value = "部门id", required = true, paramType = "form")
    })
    @PostMapping(UserUrlCenter.PROVIDER_DEPARTMENT_MANAGE_ENABLE)
    public RestResponse enable(@Verify(param = "deptId", rule = "required") Integer deptId) {
        final LoginBaseInfo loginInfo = LoginSessionUtil.getBaseLoginInfo();
        final HashMap<String, Object> hashMap = new HashMap<String, Object>() {{
            put("deptId", deptId);
            put("loginUserId", loginInfo.obtainBaseInfo().getStaffId());
            put("loginUserName", loginInfo.obtainBaseInfo().getStaffName());
        }};
        RestResponse restResponse = post(UserUrlCenter.CLIENT_DEPARTMENT_MANAGE_ENABLE, hashMap);
        if(restResponse.isSuccess()){
            restResponse.setMsg("部门启用成功");
        }
        return restResponse;
    }

    @PostMapping(UserUrlCenter.PROVIDER_DEPARTMENT_BATCH_INSERT)
    @RequestFunction(functionName = "部门信息-批量导入部门")
    @ApiOperation("部门信息-批量导入部门")
    @JrdApiDoc(simpleDesc = "上传Excel批量更新", detailDesc = "", author = "丁伟兵", resDataDesc = "是否成功，失败时返回错误的行数和错误信息。", resDataClass = ImportErrorDTO.class)
    public RestResponse batchInsert(
            @Verify(param = "excelUrl", rule = "required") String excelUrl,
            @Verify(param="companyId",rule ="required") Integer companyId,
            HttpServletRequest request,
            HttpServletResponse response) {
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.DEPARTMENT_BATCH_INSERT);
        Map<String, Object> paraMap = new HashMap<>(1);
        ProviderLoginInfo loginUser = LoginSessionUtil.getProviderLoginInfo();
        DepartmentImportReqDTO reqDTO = new DepartmentImportReqDTO();
        reqDTO.setLoginCompanyId(companyId);
        reqDTO.setExcelUrl(excelUrl);
        reqDTO.setLoginUserId(loginUser.getBaseInfo().getStaffId());
        reqDTO.setLoginUserName(loginUser.getBaseInfo().getStaffName());
        paraMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paraMap, null, BatchDTO.class);
    }

}
