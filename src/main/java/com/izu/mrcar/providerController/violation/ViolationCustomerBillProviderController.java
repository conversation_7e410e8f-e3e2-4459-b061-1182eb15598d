package com.izu.mrcar.providerController.violation;

import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.violation.ViolationCustomerBillExportProviderDTO;
import com.izu.asset.dto.violation.ViolationCustomerBillReqDTO;
import com.izu.asset.dto.violation.ViolationCustomerBillRespDTO;
import com.izu.carasset.CarAssetRestLocator;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 客户端-12123账号相关
 * @date 2024/9/26 11:13
 */
@Api(tags = "违章管理")
@RestController
public class ViolationCustomerBillProviderController {


    @PostMapping(value = MrCarAssetRestCenter.VIOLATION_CUSTOMER_BILL_LIST_URL_PROVIDER)
    @RequestFunction(functionName = "违章管理-客户账单-列表")
    @ApiOperation(value = "客户账单列表")
    public RestResponse<PageDTO<ViolationCustomerBillRespDTO>> getList(@RequestBody ViolationCustomerBillReqDTO violationAccountReqDTO) {
        Map<String, Object> param = new HashMap<>();
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, violationAccountReqDTO);
        String restUrl = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.VIOLATION_CUSTOMER_BILL_LIST_URL);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null, ViolationCustomerBillRespDTO.class);
    }

    @RequestMapping(MrCarAssetRestCenter.VIOLATION_CUSTOMER_BILL_LIST_URL_PROVIDER_EXPORT)
    @ApiOperation(value = "导出客户账单列表", notes = "作者:丁伟兵")
    @RequestFunction(functionName = "违章管理-客户账单-导出-客户端")
    @ExportExcelWeb(fileName = "维修厂列表.xlsx", filePath = "/data/logs/izubackground/tmp", sheet = "客户账单",
            c = ViolationCustomerBillExportProviderDTO.class
    )
    public PageDTO export(@RequestBody ViolationCustomerBillReqDTO violationAccountReqDTO, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response) {

        Map<String, Object> paramMap = new HashMap<>();
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        violationAccountReqDTO.setPageSize(500);
        violationAccountReqDTO.setPage(izuEasyExcelSession.getPageNo());
        izuEasyExcelSession.setUserName(providerLoginInfo.getBaseInfo().getStaffName());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, violationAccountReqDTO);
        String restUrl = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.VIOLATION_CUSTOMER_BILL_LIST_URL);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, ViolationCustomerBillExportProviderDTO.class);
        if (restResponse != null && restResponse.isSuccess()) {
            PageDTO pageDTO = (PageDTO) restResponse.getData();
            if (pageDTO.getTotal()>10000){
                throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
            }
            return pageDTO;
        } else {
            return null;
        }
    }

}
