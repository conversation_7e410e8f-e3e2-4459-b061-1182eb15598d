package com.izu.mrcar.providerController.violation;

import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.violation.*;
import com.izu.carasset.CarAssetRestLocator;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.dto.DicKeyValueDTO;
import com.izu.mrcar.dto.violation.ViolationAccountExportDTO;
import com.izu.mrcar.service.violation.ViolationAccountService;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.dto.CompanyPaidSearchReq;
import com.izu.user.dto.SearchCompanyPaidDTO;
import com.izu.user.dto.ViolationCustomerReq;
import com.izu.user.dto.ViolationCustomerRespDTO;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.enums.paid.SelfVehicleConfigEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.izu.asset.consts.violation.ViolationSyncStatusEnum;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @description: 运营端-12123账号相关
 * @date 2024/9/26 11:13
 */
@Api(tags = "违章管理")
@RestController
public class ViolationAccountController {

    @Resource
    private ViolationAccountService violationAccountService;

    /**
     * 12123账号列表查询接口
     * @param violationAccountReqDTO
     * @return
     */
    @PostMapping(value = MrCarAssetRestCenter.VIOLATION_COMPANY_ACCOUNT_LIST_URL_PROVIDER)
    @RequestFunction(functionName = "违章管理-12123账号-列表")
    @ApiOperation(value = "12123账号列表")
    public RestResponse<PageDTO<ViolationAccountRespDTO>> getList(@RequestBody ViolationAccountReqDTO violationAccountReqDTO) {
        Map<String, Object> param = new HashMap<>();
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, violationAccountReqDTO);
        String restUrl = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.VIOLATION_COMPANY_ACCOUNT_LIST_URL);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null, ViolationAccountRespDTO.class);
    }

    @PostMapping(value = "/provider/violation/company/account/export")
    @RequestFunction(functionName = "12123账号列表导出")
    @ApiOperation(value = "12123账号列表导出")
    public RestResponse export(@RequestBody ViolationAccountReqDTO reqDTO, IzuEasyExcelSession izuEasyExcelSession, HttpServletResponse response) {
        reqDTO.setPage(1);
        reqDTO.setPageSize(2000);
        izuEasyExcelSession.setPageNo(1);
        violationAccountService.providerExport(reqDTO, izuEasyExcelSession, response);
        return null;
    }

    @PostMapping(value = MrCarAssetRestCenter.VIOLATION_COMPANY_ACCOUNT_SAVE_OR_UPDATE_URL_PROVIDER)
    @RequestFunction(functionName = "违章管理-12123账号-新增编辑")
    @ApiOperation(value = "新增/编辑12123账号")
    public RestResponse saveOrUpdate(@RequestBody ViolationAccountSaveOrUpdateReqDTO violationAccountSaveOrUpdateReqDTO) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        Map<String, Object> param = new HashMap<>();
        violationAccountSaveOrUpdateReqDTO.setLoginUserId(providerLoginInfo.obtainBaseInfo().getStaffId());
        violationAccountSaveOrUpdateReqDTO.setLoginUserName(providerLoginInfo.obtainBaseInfo().getStaffName());
        violationAccountSaveOrUpdateReqDTO.setLoginUserPhone(providerLoginInfo.obtainBaseInfo().getMobile());
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, violationAccountSaveOrUpdateReqDTO);
        String restUrl = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.VIOLATION_COMPANY_ACCOUNT_SAVE_OR_UPDATE_URL);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null);
    }

    @PostMapping(value = MrCarAssetRestCenter.VIOLATION_COMPANY_ACCOUNT_UPDATE_STATUS_URL_PROVIDER)
    @RequestFunction(functionName = "违章管理-12123账号-启用禁用")
    @ApiOperation(value = "启用或禁用12123账号")
    public RestResponse updateAccountStatus(@RequestBody ViolationAccountUpdateStatusReqDTO violationAccountUpdateStatusReqDTO) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        Map<String, Object> param = new HashMap<>();
        violationAccountUpdateStatusReqDTO.setLoginUserId(providerLoginInfo.obtainBaseInfo().getStaffId());
        violationAccountUpdateStatusReqDTO.setLoginUserName(providerLoginInfo.obtainBaseInfo().getStaffName());
        violationAccountUpdateStatusReqDTO.setLoginUserPhone(providerLoginInfo.obtainBaseInfo().getMobile());
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, violationAccountUpdateStatusReqDTO);
        String restUrl = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.VIOLATION_COMPANY_ACCOUNT_UPDATE_STATUS_URL);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null);
    }

    @PostMapping(value = MrCarAssetRestCenter.VIOLATION_COMPANY_ACCOUNT_LOG_LIST_URL_PROVIDER)
    @RequestFunction(functionName = "违章管理-12123账号-日志")
    @ApiOperation(value = "账号日志查询列表")
    public RestResponse<PageDTO<ViolationAccountLogRespDTO>> getAccountLogList(@RequestBody ViolationAccountLogReqDTO violationAccountLogReqDTO) {
        Map<String, Object> param = new HashMap<>();
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, violationAccountLogReqDTO);
        String restUrl = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.VIOLATION_COMPANY_ACCOUNT_LOG_LIST_URL);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null, ViolationAccountLogRespDTO.class);
    }
    @PostMapping(value = UserUrlCenter.VIOLATION_CUSTOMER_COMPANY_LIST_URL)
    @RequestFunction(functionName = "违章管理-违章客户-列表")
    @ApiOperation(value = "违章客户列表查询")
    public RestResponse<PageDTO<ViolationCustomerRespDTO>> getAccountLogList(@RequestBody ViolationCustomerReq violationCustomerReq) {
        Map<String, Object> param = new HashMap<>();
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, violationCustomerReq);
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.VIOLATION_CUSTOMER_COMPANY_LIST_URL);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null, ViolationCustomerRespDTO.class);
    }
    @PostMapping(value = UserUrlCenter.PAID_CUSTOMER_COMPANY_LIST_BY_JOB_URL)
    @RequestFunction(functionName = "违章付费客户查询")
    @ApiOperation(value = "违章付费客户查询")
    public RestResponse<List<SearchCompanyPaidDTO>> getSearchPaidCompany(@RequestBody CompanyPaidSearchReq companyPaidSearchReq) {
        Map<String, Object> param = new HashMap<>();
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, companyPaidSearchReq);
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.PAID_CUSTOMER_COMPANY_LIST_BY_JOB_URL);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null, SearchCompanyPaidDTO.class);
    }

    @PostMapping(value = "/violation/account/violationSyncStatusEnum")
    @RequestFunction(functionName = "违章同步状态枚举")
    @ApiOperation(value = "违章同步状态枚举")
    public RestResponse<List<DicKeyValueDTO>> violationSyncStatusEnum(@RequestBody CompanyPaidSearchReq companyPaidSearchReq) {
        List<DicKeyValueDTO> collect = Arrays.stream(ViolationSyncStatusEnum.values())
                .map(c -> new DicKeyValueDTO(c.getCode(), c.getDesc()))
                .collect(Collectors.toList());
        return RestResponse.success(collect);
    }
}
