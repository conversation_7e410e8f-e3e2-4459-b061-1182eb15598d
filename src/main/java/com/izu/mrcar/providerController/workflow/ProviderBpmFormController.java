package com.izu.mrcar.providerController.workflow;

import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.workflow.common.MrcarWorkflowRestLocator;
import com.izu.mrcar.workflow.common.constants.MrCarWorkflowResturl;
import com.izu.mrcar.workflow.common.dto.form.BpmProviderFormPageReqDTO;
import com.izu.mrcar.workflow.common.dto.form.BpmProviderFormPageRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/9/3 16:14
 */
@RestController
@Api(tags = "表单列表")
public class ProviderBpmFormController {

    @PostMapping(MrCarWorkflowResturl.PROVIDER_BPM_FORM_PAGE)
    @RequestFunction(functionName = "运营端-获得动态表单分页")
    @ApiOperation(value = "运营端-表单分页列表",notes = "作者：丁伟兵")
    public RestResponse<PageDTO<BpmProviderFormPageRespDTO>> getProviderFormPage(@RequestBody BpmProviderFormPageReqDTO pageReqDTO) {
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.PROVIDER_BPM_FORM_PAGE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, pageReqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }
}
