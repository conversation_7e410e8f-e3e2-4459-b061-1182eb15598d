package com.izu.mrcar.providerController.workflow;

import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.workflow.common.MrcarWorkflowRestLocator;
import com.izu.mrcar.workflow.common.constants.MrCarWorkflowResturl;
import com.izu.mrcar.workflow.common.dto.model.*;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import java.util.HashMap;
import java.util.Map;

@Validated
@RestController("providerBpmModelController")
@Api(tags = "基础配置-流程管理")
public class BpmModelController {

    @PostMapping(MrCarWorkflowResturl.PROVIDER_BPM_MODEL_CONFIG_PAGE)
    @RequestFunction(functionName = "获得模型分页")
    @ApiOperation(value = "获得模型分页",notes = "作者：贺新春")
    public RestResponse<PageDTO<BpmModelPageItemRespDTO>> getModelConfigPage(@RequestBody BpmModelPageReqDTO pageReqDTO) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        pageReqDTO.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        pageReqDTO.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        pageReqDTO.setLoginUserId(baseInfo.getStaffId());
        pageReqDTO.setLoginUserName(baseInfo.getStaffName());
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.PROVIDER_BPM_MODEL_CONFIG_PAGE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, pageReqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(MrCarWorkflowResturl.PROVIDER_BPM_MODEL_GET)
    @RequestFunction(functionName = "获得模型")
    @ApiOperation(value = "获得模型",notes = "作者：贺新春")
    public RestResponse<BpmModelRespDTO> getModel(@RequestBody BpmModelUpdateReqDTO modelUpdateReqDTO) {
        HashMap<String,Object> params = new HashMap<>(1);
        params.put("id",modelUpdateReqDTO.getId());
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.PROVIDER_BPM_MODEL_GET);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, url, params, null);
    }

    @PostMapping(MrCarWorkflowResturl.PROVIDER_BPM_MODEL_CONFIG_CREATE)
    @RequestFunction(functionName = "新建模型")
    @ApiOperation(value = "新建模型",notes = "作者：贺新春")
    public RestResponse<String> createModelConfig( @RequestBody BpmModelCreateReqDTO createRetDTO) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        createRetDTO.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        createRetDTO.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        createRetDTO.setLoginUserId(baseInfo.getStaffId());
        createRetDTO.setLoginUserName(baseInfo.getStaffName());
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.PROVIDER_BPM_MODEL_CONFIG_CREATE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, createRetDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(MrCarWorkflowResturl.PROVIDER_BPM_MODEL_CONFIG_UPDATE)
    @RequestFunction(functionName = "修改模型")
    @ApiOperation(value = "修改模型",notes = "作者：贺新春")
    public RestResponse<Boolean> updateConfigModel( @RequestBody BpmModelUpdateReqDTO modelUpdateReqDTO) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        modelUpdateReqDTO.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        modelUpdateReqDTO.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        modelUpdateReqDTO.setLoginUserId(baseInfo.getStaffId());
        modelUpdateReqDTO.setLoginUserName(baseInfo.getStaffName());
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.PROVIDER_BPM_MODEL_CONFIG_UPDATE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, modelUpdateReqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(MrCarWorkflowResturl.PROVIDER_BPM_MODEL_CONFIG_DEPLOY)
    @RequestFunction(functionName = "部署模型")
    @ApiOperation(value = "部署模型",notes = "作者：贺新春")
    public RestResponse<Boolean> deployModelConfig(@RequestBody BpmModelUpdateReqDTO modelUpdateReqDTO) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        modelUpdateReqDTO.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        modelUpdateReqDTO.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        modelUpdateReqDTO.setLoginUserId(baseInfo.getStaffId());
        modelUpdateReqDTO.setLoginUserName(baseInfo.getStaffName());
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.PROVIDER_BPM_MODEL_CONFIG_DEPLOY);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, modelUpdateReqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(MrCarWorkflowResturl.PROVIDER_BPM_MODEL_DELETE)
    @RequestFunction(functionName = "删除模型")
    @ApiOperation(value = "删除模型",notes = "作者：贺新春")
    public RestResponse<Boolean> deleteModel(@RequestBody BpmModelUpdateReqDTO modelUpdateReqDTO) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        modelUpdateReqDTO.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        modelUpdateReqDTO.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        modelUpdateReqDTO.setLoginUserId(baseInfo.getStaffId());
        modelUpdateReqDTO.setLoginUserName(baseInfo.getStaffName());
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.PROVIDER_BPM_MODEL_DELETE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, modelUpdateReqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(MrCarWorkflowResturl.BPM_PROVIDER_MODEL_PAGE)
    @RequestFunction(functionName = "获得模型分页")
    @ApiOperation(value = "获得模型分页",notes = "作者：贺新春")
    public RestResponse<PageDTO<BpmProviderModelPageReqDTO>> getModelPage(@RequestBody BpmModelPageReqDTO pageReqDTO) {
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_PROVIDER_MODEL_PAGE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, pageReqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }
}
