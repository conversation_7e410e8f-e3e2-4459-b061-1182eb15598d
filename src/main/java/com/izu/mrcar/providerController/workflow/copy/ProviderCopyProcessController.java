package com.izu.mrcar.providerController.workflow.copy;

import com.google.common.collect.Lists;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.perm.WorkFlowDataPermUtil;
import com.izu.mrcar.workflow.common.MrcarWorkflowRestLocator;
import com.izu.mrcar.workflow.common.constants.MrCarWorkflowResturl;
import com.izu.mrcar.workflow.common.dto.copy.CopyProcessExport;
import com.izu.mrcar.workflow.common.dto.copy.CopyProcessRespDTO;
import com.izu.mrcar.workflow.common.dto.copy.ProviderCopyProcessReqDTO;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: 抄送相关
 * @date 2024/9/3 15:35
 */
@Validated
@RestController
@Api(tags = "运营端工作流-抄送")
public class ProviderCopyProcessController {

    @PostMapping(MrCarWorkflowResturl.PROVIDER_BPM_COPY_LIST)
    @RequestFunction(functionName = "抄送列表")
    @ApiOperation(value = "抄送列表",notes = "作者：叶鹏鹏")
    public RestResponse<List<CopyProcessRespDTO>> searchProviderCopyList(@RequestBody ProviderCopyProcessReqDTO copyProcessReqDTO){
        Integer companyId = copyProcessReqDTO.getCompanyId();
        if(Objects.nonNull(companyId)){
            copyProcessReqDTO.setCompanyIds(Lists.newArrayList(companyId));
            copyProcessReqDTO.setCompanyId(null);
        }
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.PROVIDER_BPM_COPY_LIST);
        WorkFlowDataPermUtil.setProviderDataPerm(copyProcessReqDTO);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, copyProcessReqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(MrCarWorkflowResturl.PROVIDER_BPM_COPY_LIST_EXPORT)
    @RequestFunction(functionName = "抄送记录-导出-运营端")
    @ApiOperation(value = "抄送记录-导出-运营端",notes = "作者：dingweibing")
    @ExportExcelWeb(fileName = "抄送记录.xlsx",filePath = "/data/logs/excel/tmp",sheet = "抄送记录",
            c = CopyProcessExport.class,isAsync = false
    )
    public PageDTO exportCopyProcessList(@RequestBody ProviderCopyProcessReqDTO copyProcessReqDTO, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response){
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.PROVIDER_BPM_COPY_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        copyProcessReqDTO.setPageSize(500);
        copyProcessReqDTO.setPageNum(izuEasyExcelSession.getPageNo());
        ProviderLoginInfo clientLoginInfo= LoginSessionUtil.getProviderLoginInfo();
        izuEasyExcelSession.setUserName(clientLoginInfo.getBaseInfo().getStaffName());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, copyProcessReqDTO);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, CopyProcessExport.class);
        if(restResponse!=null && restResponse.isSuccess()) {
            PageDTO pageDTO = (PageDTO) restResponse.getData();
            if (pageDTO.getTotal()>10000){
                throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
            }
            return pageDTO;
        }else{
            return null;
        }
    }
}
