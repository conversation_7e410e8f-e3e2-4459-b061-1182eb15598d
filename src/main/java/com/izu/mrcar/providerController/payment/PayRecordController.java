package com.izu.mrcar.providerController.payment;

import cn.hutool.core.bean.BeanUtil;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.payment.common.PaymentRestLocator;
import com.izu.payment.common.bean.payment.PaymentRecordRequestDTO;
import io.swagger.annotations.Api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;


@RestController
@RequestMapping("/payment")
@Api(tags = "支付-支付记录")
public class PayRecordController {

    private static final PaymentRestLocator paymentRestLocator = new PaymentRestLocator();
    private static final Logger log = LoggerFactory.getLogger(PayRecordController.class);


    /**
     * 查询交易记录接口
     *
     * @param reqDTO
     * @return
     */
    @ApiOperation(value = "获取支付记录接口",notes = "作者：吴辉")
    @PostMapping("/v1/record/list")
    public RestResponse recordList(@RequestBody PaymentRecordRequestDTO reqDTO) {
        String url = paymentRestLocator.getRestUrl("/pay/v1/record/list.json");
        reqDTO.setBizType(30);
        Map<String, Object> params = BeanUtil.beanToMap(reqDTO);
        log.info("查询支付记录接口入参：{}", params);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST,
                url, params, null);
    }

}
