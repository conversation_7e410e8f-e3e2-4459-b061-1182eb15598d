package com.izu.mrcar.providerController.user;

import com.alibaba.fastjson.JSONObject;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.config.RestUrlConfig;
import com.izu.mrcar.controller.base.UserBaseController;
import com.izu.user.config.UserRestLocator;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.dto.DriverCardInfo;
import com.izu.user.dto.DriverLicenseInfo;
import com.izu.user.dto.IDCardInfo;
import com.izu.user.dto.customer.*;
import com.izu.user.util.DateUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;


/**
 * <AUTHOR>
 * @description: 运营端双证管理相关接口
 * @date 2024/8/7 10:12
 */
@Api(tags = "双证管理")
@RestController
@Slf4j
public class ProviderUserAuthController extends UserBaseController {

    private static final String OCR_IDCARD_URL ="/ocr/license";

    @PostMapping(value = UserUrlCenter.PROVIDER_USER_AUTH_LIST)
    @RequestFunction(functionName = "双证管理-列表查询")
    @ApiOperation(value = "双证管理列表查询")
    public RestResponse<PageDTO<CustomerAuthListDTO>> getList(@RequestBody CustomerAuthListReqDTO customerAuthListReqDTO) {
        Map<String, Object> param = new HashMap<>();
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, customerAuthListReqDTO);
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.PROVIDER_USER_AUTH_LIST);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null, CustomerAuthListDTO.class);
    }

    @ApiOperation(value = "双证管理-新增")
    @RequestFunction(functionName = "双证管理-新增")
    @PostMapping(UserUrlCenter.PROVIDER_ADD_USER_AUTH)
    public RestResponse<String> addUserAuth(@RequestBody CustomerAuthAddReqDTO reqDTO){
        return super.postBodyWithLogin(UserUrlCenter.PROVIDER_ADD_USER_AUTH,reqDTO);
    }
    @ApiOperation(value = "双证管理-编辑")
    @RequestFunction(functionName = "双证管理-编辑")
    @PostMapping(UserUrlCenter.PROVIDER_UPDATE_USER_AUTH)
    public RestResponse updateUserAuth(@RequestBody CustomerAuthUpdateReqDTO reqDTO){
        return super.postBodyWithLogin(UserUrlCenter.PROVIDER_UPDATE_USER_AUTH,reqDTO);
    }
    @ApiOperation(value = "双证管理-详情")
    @RequestFunction(functionName = "双证管理-详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "主键id", required = true)
    })
    @GetMapping(UserUrlCenter.PROVIDER_USER_AUTH_DETAIL)
    public RestResponse<CustomerAuthDetailDTO> getUserAuthDetail(@RequestParam(value = "id") @Verify(param = "id", rule = "required") Integer id){
        final HashMap<String, Object> restParam = new HashMap<String, Object>() {{
            put("id", id);
        }};
        return super.get(UserUrlCenter.PROVIDER_USER_AUTH_DETAIL, restParam);
    }

    @ApiOperation(value = "驾驶证OCR识别")
    @RequestFunction(functionName = "驾驶证OCR识别")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "imgUrl", value = "照片URL", required = true),
            @ApiImplicitParam(name = "side", value = "1：主页 2：副页", required = true)
    })
    @GetMapping(UserUrlCenter.PROVIDER_USER_DRIVER_OCR_DETAIL)
    public RestResponse<DriverCardInfo> getDriverOCRDetail(@RequestParam(value = "imgUrl") @Verify(param = "imgUrl", rule = "required") String imgUrl,
                                                          @RequestParam(value = "side") @Verify(param = "side", rule = "required") Integer side
    ){
        Map<String, Object> params = new HashMap<>();
        DriverCardInfo driverCardInfo = new DriverCardInfo();
        params.put("type", 2);
        params.put("photoUrl", imgUrl);
        params.put("side", side);
        String requestURL = RestUrlConfig.getConfig().getThirdCoreUrl()+OCR_IDCARD_URL;
        RestResponse response = RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, requestURL, params, null, JSONObject.class);
        if (response.isSuccess()){
            JSONObject data = (JSONObject) response.getData();
            if(side==1){
                driverCardInfo.setCustomerName(data.get("name").toString());
                driverCardInfo.setLicenseType(data.get("vehicleType").toString());
                driverCardInfo.setDriverCertNo(data.get("num").toString());
                driverCardInfo.setDriverCertFirstDate(DateUtil.parse(data.get("issueDate").toString(),DateUtil.FMT_YYYYMMDD));
                driverCardInfo.setDriverCertValidDate(DateUtil.parse(data.get("endDate").toString(),DateUtil.FMT_YYYYMMDD));
                driverCardInfo.setDriverCertStartDate(DateUtil.parse(data.get("startDate").toString(),DateUtil.FMT_YYYYMMDD));
            }else {
                driverCardInfo.setDriverArchivesNo(data.get("archiveNo").toString());
            }
        }
        return RestResponse.success(driverCardInfo);
    }


    /**
     * 行驶证OCR识别
     * @param imgUrl
     * @return
     */
    @GetMapping(UserUrlCenter.PROVIDER_USER_DRIVER_LICENSE_OCR_DETAIL)
    public RestResponse<DriverLicenseInfo> getLicenseOCRDetail(@RequestParam(value = "imgUrl") @Verify(param = "imgUrl", rule = "required") String imgUrl) throws ParseException {
        Map<String, Object> params = new HashMap<>();
        DriverLicenseInfo driverLicenseInfo = new DriverLicenseInfo();
        params.put("type", 5);
        params.put("photoUrl", imgUrl);
        String requestURL = RestUrlConfig.getConfig().getThirdCoreUrl() + OCR_IDCARD_URL;
        RestResponse response = RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, requestURL, params, null, JSONObject.class);
        if (response.isSuccess()) {
            JSONObject data = (JSONObject) response.getData();
            driverLicenseInfo.setVehicleLicense(data.get("plateNum").toString());
            driverLicenseInfo.setVehicleVin(data.get("vin").toString());
            driverLicenseInfo.setVehicleEngineNo(data.get("engineNum").toString());
            DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyyMMdd");
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern(DateUtil.FMT_YYYYMMDD);
            String registerDate = data.get("registerDate").toString();
            if(StringUtils.isNotBlank(registerDate)){
                LocalDate date = LocalDate.parse(registerDate, inputFormatter);
                String formattedDate = date.format(outputFormatter);
                driverLicenseInfo.setVehicleRegisterDate(formattedDate);
            }
            //营运性质
            Object useCharacter = data.get("useCharacter");
            Integer operationStatus = 2;
            if (Objects.nonNull(useCharacter) && !useCharacter.toString().contains("转非") && !useCharacter.toString().equals("非营运")) {
                operationStatus = 1;
            }
            driverLicenseInfo.setOperationStatus(operationStatus);
        }
        return RestResponse.success(driverLicenseInfo);
    }



    @ApiOperation(value = "身份证OCR识别")
    @RequestFunction(functionName = "身份证OCR识别")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "imgUrl", value = "照片URL", required = true),
            @ApiImplicitParam(name = "side", value = "1：正面 2：反面", required = true)
    })
    @GetMapping(UserUrlCenter.PROVIDER_USER_IDCARD_OCR_DETAIL)
    public RestResponse<IDCardInfo> getIDCardOCRDetail(@RequestParam(value = "imgUrl") @Verify(param = "imgUrl", rule = "required") String imgUrl,
                                                       @RequestParam(value = "side") @Verify(param = "side", rule = "required") Integer side
    ){
        Map<String, Object> params = new HashMap<>();
        IDCardInfo idCardInfo = new IDCardInfo();
        params.put("type", 1);
        params.put("photoUrl", imgUrl);
        params.put("side", side);
        String requestURL = RestUrlConfig.getConfig().getThirdCoreUrl()+OCR_IDCARD_URL;
        RestResponse response = RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, requestURL, params, null, JSONObject.class);
        if (response.isSuccess()){
            JSONObject data = (JSONObject) response.getData();
            if(side==1){
                idCardInfo.setCustomerName(data.get("name").toString());
                idCardInfo.setCertNo(data.get("num").toString());
                idCardInfo.setSex(data.get("sex").toString().equals("男")?(byte)1:(byte)2);
                idCardInfo.setSexStr(data.get("sex").toString());
                idCardInfo.setAddress(data.get("address").toString());
                idCardInfo.setAge(com.izu.erplease.util.DateUtil.getAgeFromIDcard(data.get("num").toString()));
            }else {
                idCardInfo.setTermOfValidity(DateUtil.parse(data.get("endDate").toString(),DateUtil.FMT_YYYYMMDD));

            }
        }
        return RestResponse.success(idCardInfo);
    }
}
