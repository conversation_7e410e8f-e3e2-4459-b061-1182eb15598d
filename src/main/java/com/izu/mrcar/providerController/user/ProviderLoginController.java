package com.izu.mrcar.providerController.user;

import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.config.ConstansConfig;
import com.izu.mrcar.common.constants.MrcarUrl;
import com.izu.mrcar.common.enums.AuthStrategy;
import com.izu.mrcar.common.enums.ThirdpartyLoginTypeEnum;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.controller.base.UserBaseController;
import com.izu.mrcar.dto.IzuStaffExDTO;
import com.izu.mrcar.service.login.AccountLoginService;
import com.izu.mrcar.service.login.v2.token.ThirdpartyMobileToken;
import com.izu.mrcar.shiro.exception.CustomAuthenticationException;
import com.izu.mrcar.shiro.exception.VerificationCodeErrorException;
import com.izu.mrcar.shiro.exception.VerificationCodeInvalidException;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.config.errcode.UserErrorCode;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.AccountException;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.IncorrectCredentialsException;
import org.apache.shiro.subject.Subject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 服务商登录
 *
 * <AUTHOR>
 * @date : 2023/1/27
 */
@Api(tags = "个人中心")
@RestController
@Slf4j
public class ProviderLoginController extends UserBaseController {

    private static final Logger logger = LoggerFactory.getLogger(ProviderLoginController.class);

    @Autowired
    private AccountLoginService accountLoginService;

    /**
     * 兼容之前的逻辑, 实现从综合管理平台跳转到Mr.Car平台
     */
    @GetMapping(MrcarUrl.LOGIN_NEW_SYSTEM)
    @ApiOperation("综合跳转mr.car登录")
    @ApiImplicitParam(name = "tokenStr", value = "综合管理平台会话token", required = true)
    @RequestFunction(functionName = "综合跳转mr.car登录")
    @SuppressWarnings("unchecked")
    public RestResponse<LoginBaseInfo> loginFromNewSystem(@Verify(param = "tokenStr", rule = "required") String tokenStr) {
        return accountLoginService.login(AuthStrategy.MANAGEMENT, null, tokenStr);
    }

}
