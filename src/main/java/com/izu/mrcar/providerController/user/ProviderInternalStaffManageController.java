package com.izu.mrcar.providerController.user;

import com.google.common.collect.Maps;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.controller.base.UserBaseController;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.dto.company.CompanyDTO;
import com.izu.user.dto.provider.staff.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;

/**
 * 运营端-内部账号管理
 * <AUTHOR>
 * @date : 2023/2/22
 */
@Api(tags = "个人中心")
@RestController
public class ProviderInternalStaffManageController extends UserBaseController {


    @RequestFunction(functionName = "内部账号列表")
    @ApiOperation("运营端-内部账号列表")
    @PostMapping(UserUrlCenter.PROVIDER_INTERNAL_STAFF_MANAGE_PAGE_LIST)
    public RestResponse<PageDTO<ProviderStaffListRespDTO>> pageList(@RequestBody ProviderStaffListReqDTO reqDTO) {
        return postBody(UserUrlCenter.PROVIDER_INTERNAL_STAFF_MANAGE_PAGE_LIST,reqDTO);
    }


    @RequestFunction(functionName = "内部账号详情")
    @ApiOperation("运营端-内部账号详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "providerStaffId", value = "员工ID", required = true, paramType = "form")
    })
    @PostMapping(UserUrlCenter.PROVIDER_INTERNAL_STAFF_MANAGE_DETAIL)
    public RestResponse<ProviderStaffDetailRespDTO> detail(Integer providerStaffId){
        final HashMap<String, Object> param = Maps.newHashMapWithExpectedSize(1);
        param.put("providerStaffId", providerStaffId);
        return post(UserUrlCenter.PROVIDER_INTERNAL_STAFF_MANAGE_DETAIL,param);
    }



    @RequestFunction(functionName = "内部账号编辑")
    @ApiOperation("运营端-内部账号编辑")
    @PostMapping(UserUrlCenter.PROVIDER_INTERNAL_STAFF_MANAGE_EDIT)
    public RestResponse edit(@RequestBody ProviderStaffEditReqDTO reqDTO){
        return postBodyWithLogin(UserUrlCenter.PROVIDER_INTERNAL_STAFF_MANAGE_EDIT,reqDTO);
    }


    /**
     * 指定客户列表
     * @return
     */
    @RequestFunction(functionName = "指定客户列表")
    @ApiOperation("运营端-指定客户列表")
    @PostMapping(UserUrlCenter.PROVIDER_INTERNAL_STAFF_MANAGE_SPECIFY_COMPANY_LIST)
    public RestResponse<PageDTO<CompanyDTO>> specifyCompanyList(@RequestBody ProviderSpecifyCustomerReqDTO reqDTO){
        return postBody(UserUrlCenter.PROVIDER_INTERNAL_STAFF_MANAGE_SPECIFY_COMPANY_LIST,reqDTO);
    }


    @RequestFunction(functionName = "运营端-部门树")
    @ApiOperation("运营端-部门树")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "structStatus", value = "部门状态;1-正常；2-托管；3-关闭；不传加载所有", required = false, paramType = "form")
    })
    @GetMapping(UserUrlCenter.PROVIDER_DEPARTMENT_TREE)
    public RestResponse<ProviderDeptRespDTO> deptTree(Byte structStatus){
        final HashMap<String, Object> restParam = new HashMap<>();
        restParam.put("structStatus",structStatus);
        return get(UserUrlCenter.PROVIDER_DEPARTMENT_TREE,restParam);
    }

    @RequestFunction(functionName = "运营端-部门下拉列表")
    @ApiOperation("运营端-部门（一级部门和城分）")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "structStatus", value = "部门状态;1-正常；2-托管；3-关闭；不传加载所有", required = false, paramType = "form"),
            @ApiImplicitParam(name = "deptName", value = "名称", required = false, paramType = "form"),
    })
    @GetMapping(UserUrlCenter.PROVIDER_DEPARTMENT_LIST)
    public RestResponse<ProviderDeptRespDTO> deptList(Byte structStatus,String deptName){
        final HashMap<String, Object> restParam = new HashMap<>();
        restParam.put("structStatus",structStatus);
        restParam.put("deptName",deptName);
        return get(UserUrlCenter.PROVIDER_DEPARTMENT_LIST,restParam);
    }



    @RequestFunction(functionName = "运营端-员工下拉框")
    @ApiOperation("运营端-员工下拉框")
    @RequestMapping(value = UserUrlCenter.PROVIDER_DROPDOWN_LIST,method = {RequestMethod.GET,RequestMethod.POST})
    public RestResponse<List<ProviderStaffDropdownRespDTO>> providerStaffDropdownList(ProviderStaffDropdownReqDTO reqDTO)  {
        //现在员工的下卡框默认是第一页的前一百条就行
        reqDTO.setPage(1);
        reqDTO.setPageSize(100);
        return postBody(UserUrlCenter.PROVIDER_DROPDOWN_LIST,reqDTO);
    }

    @RequestFunction(functionName = "运营端-部门员工下拉框")
    @ApiOperation("运营端-部门员工下拉框")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "structCode", value = "部门编码", required = true, paramType = "form"),
    })
    @RequestMapping(value = UserUrlCenter.PROVIDER_STAFF_BY_STRUCT_DROPDOWN_LIST,method = {RequestMethod.GET,RequestMethod.POST})
    public RestResponse<List<ProviderStaffDropdownRespDTO>> providerStructStaffDropdownList(String structCode) {
        final HashMap<String, Object> restParam = new HashMap<>();
        restParam.put("structCode",structCode);
        return get(UserUrlCenter.PROVIDER_STAFF_BY_STRUCT_DROPDOWN_LIST,restParam);
    }

}
