package com.izu.mrcar.providerController.benefit;

import com.izu.CouponCoreRestMsgCenter;
import com.izu.MrCarCouponRestLocator;
import com.izu.dto.benefit.BenefitCardDTO;
import com.izu.dto.benefit.BenefitCardQueryDTO;
import com.izu.excel.annotation.ExportExcel;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.excel.dto.IzuMailSession;
import com.izu.excel.mail.AbstractIzuMailExcel;
import com.izu.framework.web.exception.RestErrorException;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.config.ExecutorConfig;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.util.ObjectTransferUtil;
import com.izu.user.util.SingletonFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> dongxiya 2023/11/22 15:26
 */
@Api(tags = "权益卡")
@RestController
public class BenefitCarController implements AbstractIzuMailExcel<BenefitCardQueryDTO,BenefitCardExportDTO> {

    private final MrCarCouponRestLocator couponRestLocator = SingletonFactory.getSingleton("couponRestLocator", MrCarCouponRestLocator::new);

    @ApiOperation("全部权益卡-列表")
    @PostMapping(CouponCoreRestMsgCenter.BENEFIT_CARD_QUERY_PAGE)
    public RestResponse<PageDTO<BenefitCardDTO>> queryPage(@RequestBody BenefitCardQueryDTO dto) {
        Map<String, Object> params = new HashMap<>(1);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, couponRestLocator.getRestUrl(CouponCoreRestMsgCenter.BENEFIT_CARD_QUERY_PAGE), params, null, Map.class);
    }


    @ApiOperation("全部权益卡-导出")
    @PostMapping(CouponCoreRestMsgCenter.BENEFIT_CARD_EXPORT)
    @ExportExcelWeb(fileName = ExportExcelConstants.FILE_NAME_ALL_BENEFIT_CARD+ ExportExcelConstants.XLSX,filePath = ExportExcelConstants.FILE_PATH,
            sheet = ExportExcelConstants.FILE_NAME_ALL_BENEFIT_CARD, c = BenefitCardExportDTO.class)
    public PageDTO<BenefitCardExportDTO> export(@RequestBody BenefitCardQueryDTO dto, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response) {
        Map<String, Object> params = new HashMap<>(1);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        LoginBaseInfo baseInfo = LoginSessionUtil.getProviderLoginInfo();
        izuEasyExcelSession.setUserName(baseInfo.obtainBaseInfo().getStaffName());
        dto.setPageSize(ExportExcelConstants.PAGE_SIZE);
        dto.setPage(izuEasyExcelSession.getPageNo());
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, couponRestLocator.getRestUrl(CouponCoreRestMsgCenter.BENEFIT_CARD_QUERY_PAGE), params, null, BenefitCardExportDTO.class);
        if(restResponse.isSuccess()){
            PageDTO<BenefitCardExportDTO> pageDTO = ObjectTransferUtil.cast(restResponse.getData());
            if (pageDTO.getTotal()> ExportExcelConstants.EXPORT_MAX_LINE){
                throw new RestErrorException(pageDTO.getTotal() + "", ErrorCode.EXPORT_LIMITED_WARN);
            }
            return pageDTO;
        }else {
            return null;
        }
    }


    @ApiOperation("全部权益卡-导出至邮箱")
    @PostMapping(CouponCoreRestMsgCenter.BENEFIT_CARD_EXPORT_EMAIL)
    public RestResponse exportBenefitCardToMail(@RequestBody BenefitCardQueryDTO dto) {
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        dto.setLoginUserName(loginBaseInfo.obtainBaseInfo().getStaffName());
        dto.setEmail(loginBaseInfo.obtainBaseInfo().getEmail());
        if (StringUtils.isBlank(dto.getEmail())){
            return RestResponse.create(9999,"该用户未绑定邮箱",false,null);
        }
        ExecutorConfig.getThreadPool().execute(() -> BenefitCarController.this.sendEmails(dto));
        return RestResponse.create(0,"提交成功！请稍后查收电子邮件。（大约耗时3分钟！）",true,0);
    }

    @ExportExcel(fileName = "全部权益卡列表导出.xlsx",filePath = ExportExcelConstants.FILE_PATH, sheet = "权益卡列表",isImportant = true)
    private void sendEmails(BenefitCardQueryDTO dto){
        IzuMailSession izuMailSession = new IzuMailSession();
        izuMailSession.setUserName(dto.getLoginUserName());
        izuMailSession.setMailOpName("全部权益卡列表");
        izuMailSession.setMailSubject("【全部权益卡列表】导出数据:{0}条");
        izuMailSession.setToMail(dto.getEmail());
        this.send2EmailWithCommon(izuMailSession,dto, BenefitCardExportDTO.class);
    }


    @Override
    public PageDTO getDataByPage(Object o, int i, int i1) {
        BenefitCardQueryDTO dto = (BenefitCardQueryDTO) o;
        dto.setPage(i);
        dto.setPageSize(i1);
        Map<String, Object> params = new HashMap<>(1);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, couponRestLocator.getRestUrl(CouponCoreRestMsgCenter.BENEFIT_CARD_QUERY_PAGE), params, null, BenefitCardExportDTO.class);
        if(restResponse.isSuccess()){
            return ObjectTransferUtil.cast(restResponse.getData());
        }else {
            return null;
        }
    }


    @ApiOperation("按照卡密批量查询权益卡")
    @RequestFunction(functionName = "按照卡密批量查询权益卡")
    @PostMapping(CouponCoreRestMsgCenter.BENEFIT_CARD_SECRET)
    public RestResponse getBenefitCard(@Verify(param = "benefitSecret", rule = "required") String benefitSecret) {
        HashMap<String, Object> param = new HashMap<>();
        param.put("benefitSecret", benefitSecret);
        String restUrl = couponRestLocator.getRestUrl(CouponCoreRestMsgCenter.BENEFIT_CARD_QUERY_PAGE);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, param, null);
    }

}
