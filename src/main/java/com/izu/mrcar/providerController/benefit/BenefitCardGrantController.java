package com.izu.mrcar.providerController.benefit;

import cn.hutool.core.collection.CollectionUtil;
import com.izu.CouponCoreRestMsgCenter;
import com.izu.carasset.util.StringUtils;
import com.izu.dto.benefit.*;
import com.izu.excel.annotation.ExportExcel;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.excel.dto.IzuMailSession;
import com.izu.excel.mail.AbstractIzuMailExcel;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.exception.RestErrorException;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.controller.base.CouponBaseController;
import com.izu.mrcar.service.benefit.ProviderBenefitLaunchImportService;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.provider.staff.ProviderDeptRespDTO;
import com.izu.user.dto.staff.pc.AccountBelongDepartment;
import com.izu.user.dto.staff.pc.DataPermInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.enums.perm.ProviderDataPermTypeEnum;
import com.izu.user.util.ObjectTransferUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: mrcar
 * @description: 权益卡发放
 * @author: ljw
 * @create: 2023-11-23 14:12
 **/
@Api(tags = "权益卡-发放")
@RestController
public class BenefitCardGrantController extends CouponBaseController implements AbstractIzuMailExcel<BenefitCardQueryDTO,BenefitCardExportDTO> {

    @Autowired
    private ProviderBenefitLaunchImportService providerBenefitLaunchImportService;


    @ApiOperation("权益卡列表")
    @PostMapping(CouponCoreRestMsgCenter.BENEFIT_CARD_GRANT_SELECT_PAGE)
    public RestResponse<PageDTO<BenefitCardDTO>> queryPage(@RequestBody BenefitCardQueryDTO param) {
        return postBodyWithLoginForPage(CouponCoreRestMsgCenter.BENEFIT_CARD_GRANT_SELECT_PAGE,param,BenefitCardDTO.class);
    }

    @ApiOperation("权益卡发放（支持批量）")
    @PostMapping(CouponCoreRestMsgCenter.BENEFIT_CARD_GRANT)
    public RestResponse grantBenefitCard(@RequestBody BenefitCardGrantSaveDTO param) {
        return postBodyWithLogin(CouponCoreRestMsgCenter.BENEFIT_CARD_GRANT,param);
    }

    @ApiOperation("导出权益卡")
    @PostMapping(CouponCoreRestMsgCenter.BENEFIT_CARD_GRANT_EXPORT)
    @ExportExcelWeb(fileName = ExportExcelConstants.FILE_NAME_BENEFIT_CARD_LIST+ ExportExcelConstants.XLSX,filePath = ExportExcelConstants.FILE_PATH,
            sheet = ExportExcelConstants.FILE_NAME_BENEFIT_CARD_LIST, c = BenefitCardExportDTO.class)
    public PageDTO exportBenefitCard(@RequestBody BenefitCardQueryDTO param,
                                     IzuEasyExcelSession izuEasyExcelSession,
                                     HttpServletRequest request, HttpServletResponse response) {
        LoginBaseInfo baseInfo = LoginSessionUtil.getProviderLoginInfo();
        izuEasyExcelSession.setUserName(baseInfo.obtainBaseInfo().getStaffName());
        param.setPageSize(ExportExcelConstants.PAGE_SIZE);
        param.setPage(izuEasyExcelSession.getPageNo());
        RestResponse restResponse = postBodyWithLoginForPage(CouponCoreRestMsgCenter.BENEFIT_CARD_GRANT_SELECT_PAGE,param,BenefitCardDTO.class);
        if(restResponse.isSuccess()){
            PageDTO<BenefitCardDTO> pageDTO = ObjectTransferUtil.cast(restResponse.getData());
            if (pageDTO.getTotal()> ExportExcelConstants.EXPORT_MAX_LINE){
                throw new RestErrorException(pageDTO.getTotal() + "", ErrorCode.EXPORT_LIMITED_WARN);
            }
            List<BenefitCardExportDTO> list = BeanUtil.copyList(pageDTO.getResult(),BenefitCardExportDTO.class);
            return new PageDTO(pageDTO.getPage(), pageDTO.getPageSize(), pageDTO.getTotal(), list);
        }else {
            return null;
        }
    }

    @ApiOperation("导出权益卡至邮箱")
    @PostMapping(CouponCoreRestMsgCenter.BENEFIT_CARD_GRANT_EXPORT_TO_MAIL)
    public RestResponse exportBenefitCardToMail(@RequestBody BenefitCardQueryDTO param) {
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        param.setLoginUserName(loginBaseInfo.obtainBaseInfo().getStaffName());
        if(StringUtils.isBlank(param.getEmail())){
            param.setEmail(loginBaseInfo.obtainBaseInfo().getEmail());
        }
        if (StringUtils.isBlank(param.getEmail())){
            return RestResponse.create(9999,"该用户未绑定邮箱",false,null);
        }
        new Thread(new Runnable() {
            @Override
            public void run() {
                BenefitCardGrantController.this.sendEmails(param);
            }
        }).start();
        return RestResponse.create(0,"提交成功！请稍后查收电子邮件。（大约耗时3分钟！）",true,0);
    }

    @PostMapping(CouponCoreRestMsgCenter.BENEFIT_CARD_GRANT_TEMPLATE_AND_COUNT_QUERY)
    @ApiOperation("根据渠道查询可发放权益卡及其剩余数量")
    @ApiImplicitParam(name = "channelDepartmentCode",value = "渠道编码",required = true,paramType="form")
    public RestResponse<List<BenefitCardTemplateAndNumDTO>> queryTemplateAndCount(String channelDepartmentCode){
        Map<String, Object> paramMap = new HashMap<>(1);
        paramMap.put("channelDepartmentCode", channelDepartmentCode);
        return post(CouponCoreRestMsgCenter.BENEFIT_CARD_GRANT_TEMPLATE_AND_COUNT_QUERY,paramMap);
    }

    @PostMapping(CouponCoreRestMsgCenter.BENEFIT_CARD_GRANT_CHANNEL_QUERY)
    @ApiOperation("查询用户可见的渠道")
    public RestResponse<List<BenefitChannelForGrantDTO>> queryGrantChannel(){
        List<BenefitChannelForGrantDTO> res = new ArrayList<>();
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        Byte dataPermType = providerLoginInfo.getProviderDataPerm().getDataPermType();
        List<ProviderDeptRespDTO> deptRespDTOS = providerBenefitLaunchImportService.getDeptList();
        Map<String,String> deptMap = deptRespDTOS.stream().collect(Collectors.toMap(ProviderDeptRespDTO::getStructCode,ProviderDeptRespDTO::getStructName));
        List<Byte> belongDeptList = Arrays.asList(ProviderDataPermTypeEnum.ASSIGN_CUSTOMER.getType(),
                ProviderDataPermTypeEnum.RESPONSIBLE_CUSTOMER.getType(),
                ProviderDataPermTypeEnum.RESPONSIBLE_CONTRACT.getType(),
                ProviderDataPermTypeEnum.ONE_SELF.getType());
        if(belongDeptList.contains(dataPermType)){
            //本人、指定客户/负责客户、负责合同：登录人的所属部门
            if(CollectionUtils.isNotEmpty(providerLoginInfo.getBelongDepartmentList())){
                providerLoginInfo.getBelongDepartmentList().stream().forEach(e->{
                    String name = deptMap.get(e.getDeptCode());
                    if(StringUtils.isNotBlank(name)){
                        BenefitChannelForGrantDTO channelForGrantDTO = new BenefitChannelForGrantDTO();
                        channelForGrantDTO.setChannelDepartmentCode(e.getDeptCode());
                        channelForGrantDTO.setChannelDepartmentName(name);
                        res.add(channelForGrantDTO);
                    }
                });
            }
             providerLoginInfo.getBelongDepartmentList().stream().map(AccountBelongDepartment::getDeptCode).collect(Collectors.joining(","));
        }
        if(ProviderDataPermTypeEnum.ASSIGN_DEPT.getType().equals(dataPermType) || ProviderDataPermTypeEnum.SELF_DEPT.getType().equals(dataPermType)){
            //指定部门/所属部门：登录人的数据权限中指定部门和所属部门
            if(CollectionUtils.isNotEmpty(providerLoginInfo.obtainDataPerm().getDataPermInfoList())){
                providerLoginInfo.obtainDataPerm().getDataPermInfoList().stream().forEach(e->{
                    String name = deptMap.get(e.getDataPermCode());
                    if(StringUtils.isNotBlank(name)){
                        BenefitChannelForGrantDTO channelForGrantDTO = new BenefitChannelForGrantDTO();
                        channelForGrantDTO.setChannelDepartmentCode(e.getDataPermCode());
                        channelForGrantDTO.setChannelDepartmentName(name);
                        res.add(channelForGrantDTO);
                    }
                });
            }
        }
        if(ProviderDataPermTypeEnum.ALL.getType().equals(dataPermType)){
            //所有的渠道数据
            if(CollectionUtils.isNotEmpty(deptRespDTOS)){
                deptRespDTOS.stream().forEach(e->{
                    BenefitChannelForGrantDTO channelForGrantDTO = new BenefitChannelForGrantDTO();
                    channelForGrantDTO.setChannelDepartmentCode(e.getStructCode());
                    channelForGrantDTO.setChannelDepartmentName(e.getStructName());
                    res.add(channelForGrantDTO);
                });
            }
        }
        return RestResponse.success(res);

    }

    @PostMapping(CouponCoreRestMsgCenter.BENEFIT_CARD_GRANT_BY_CHANNEL)
    @ApiOperation("查询可发放的权益卡号")
    public RestResponse<List<String>> queryBenefitCardListForGrant(@RequestBody BenefitCardForGrantQueryDTO queryDTO){
        return postBody(CouponCoreRestMsgCenter.BENEFIT_CARD_GRANT_BY_CHANNEL,queryDTO);
    }

    @ExportExcel(fileName = "权益卡列表导出.xlsx",filePath = ExportExcelConstants.FILE_PATH, sheet = "权益卡列表",isImportant = true)
    private void sendEmails(BenefitCardQueryDTO queryDTO){
        IzuMailSession izuMailSession = new IzuMailSession();
        izuMailSession.setUserName(queryDTO.getLoginUserName());
        izuMailSession.setMailOpName("权益卡列表");
        izuMailSession.setMailSubject("【权益卡列表】导出数据:{0}条");
        izuMailSession.setToMail(queryDTO.getEmail());
        //izuMailSession.setBccMail("<EMAIL>");
        this.send2EmailWithCommon(izuMailSession,queryDTO, BenefitCardExportDTO.class);
    }


    @Override
    public PageDTO getDataByPage(Object o, int i, int i1) {
        BenefitCardQueryDTO queryDTO = (BenefitCardQueryDTO) o;
        queryDTO.setPage(i);
        queryDTO.setPageSize(i1);
        RestResponse restResponse = postBodyWithLoginForPage(CouponCoreRestMsgCenter.BENEFIT_CARD_GRANT_SELECT_PAGE,queryDTO,BenefitCardDTO.class);
        if(restResponse.isSuccess()){
            PageDTO<BenefitCardDTO> pageDTO = ObjectTransferUtil.cast(restResponse.getData());
            List<BenefitCardExportDTO> list = BeanUtil.copyList(pageDTO.getResult(),BenefitCardExportDTO.class);
            return new PageDTO(pageDTO.getPage(), pageDTO.getPageSize(), pageDTO.getTotal(), list);
        }else {
            return null;
        }
    }
}
