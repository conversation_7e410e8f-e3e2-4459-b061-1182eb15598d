package com.izu.mrcar.providerController.benefit;

import com.izu.CouponCoreRestMsgCenter;
import com.izu.MrCarCouponRestLocator;
import com.izu.dto.benefit.*;
import com.izu.excel.annotation.ExportExcel;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.excel.dto.IzuMailSession;
import com.izu.excel.mail.AbstractIzuMailExcel;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.exception.RestErrorException;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.config.ExecutorConfig;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.controller.base.CouponBaseController;
import com.izu.mrcar.service.benefit.ProviderBenefitLaunchImportService;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: mrcar
 * @description: 权益卡投放活动
 * @author: ljw
 * @create: 2023-11-23 14:12
 **/
@Api(tags = "权益卡-投放")
@RestController
public class BenefitLaunchActivityController extends CouponBaseController implements AbstractIzuMailExcel<BenefitCardNoImportQueryDTO,BenefitCardNoExportDTO> {

    private final MrCarCouponRestLocator couponRestLocator = new MrCarCouponRestLocator();

    @Autowired
    private ProviderBenefitLaunchImportService providerBenefitLaunchImportService;

    private final long maxLine = 10000;

    @ApiOperation("投放活动分页查询")
    @PostMapping(CouponCoreRestMsgCenter.BENEFIT_CARD_LAUNCH_ACTIVITY_SELECT_PAGE)
    public RestResponse<PageDTO<BenefitLaunchActivityDTO>> queryPage(@RequestBody BenefitLaunchActivityQueryDTO param) {
        return postBodyWithLoginForPage(CouponCoreRestMsgCenter.BENEFIT_CARD_LAUNCH_ACTIVITY_SELECT_PAGE,param,BenefitLaunchActivityDTO.class);
    }

    @ApiOperation("新建投放活动")
    @PostMapping(CouponCoreRestMsgCenter.BENEFIT_CARD_LAUNCH_ACTIVITY_SINGLE_CREATE)
    public RestResponse singleCreate(@RequestBody BenefitLaunchActivitySaveDTO param) {
        return postBodyWithLogin(CouponCoreRestMsgCenter.BENEFIT_CARD_LAUNCH_ACTIVITY_SINGLE_CREATE,param);
    }

    @ApiOperation("批量投放活动")
    @PostMapping(CouponCoreRestMsgCenter.BENEFIT_CARD_LAUNCH_ACTIVITY_BATCH_CREATE)
    @ApiImplicitParam(name = "excelUrl",value = "导入Excel文件地址",required = true,paramType="form")
    public RestResponse batchCreate(@Verify(param = "excelUrl", rule = "required") String excelUrl) {
        return providerBenefitLaunchImportService.importBenefitLaunchActivity(excelUrl);
    }

    @ApiOperation("导出投放卡号")
    @PostMapping(CouponCoreRestMsgCenter.BENEFIT_CARD_LAUNCH_ACTIVITY_EXPORT_CARD_NO)
    @ApiImplicitParam(name = "activityNo",value = "活动编号",required = true,paramType="form")
    @ExportExcelWeb(fileName = ExportExcelConstants.FILE_NAME_BENEFIT_CARD_NO+ ExportExcelConstants.XLSX,filePath = ExportExcelConstants.FILE_PATH,
            sheet = ExportExcelConstants.FILE_NAME_BENEFIT_CARD_NO, c = BenefitCardNoExportDTO.class)
    public PageDTO exportCardNo(String activityNo, IzuEasyExcelSession izuEasyExcelSession,
                                HttpServletRequest request, HttpServletResponse response) {
        LoginBaseInfo baseInfo = LoginSessionUtil.getProviderLoginInfo();
        izuEasyExcelSession.setUserName(baseInfo.obtainBaseInfo().getStaffName());
        String url = couponRestLocator.getRestUrl(CouponCoreRestMsgCenter.BENEFIT_CARD_LAUNCH_ACTIVITY_EXPORT_CARD_NO);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("activityNo", activityNo);
        paramMap.put("page", izuEasyExcelSession.getPageNo());
        paramMap.put("pageSize", ExportExcelConstants.PAGE_SIZE);
        RestResponse<PageDTO<BenefitCardDTO>> res = RestClient.requestForPage(BaseHttpClient.HttpMethod.POST, url, paramMap, null, BenefitCardDTO.class);
        if(null!=res && res.isSuccess()){
            PageDTO<BenefitCardDTO> pageDTO = res.getData();
            if (pageDTO.getTotal()>ExportExcelConstants.EXPORT_MAX_LINE){
                throw new RestErrorException(pageDTO.getTotal() + "", ErrorCode.EXPORT_LIMITED_WARN);
            }
            List<BenefitCardNoExportDTO> list = BeanUtil.copyList(pageDTO.getResult(),BenefitCardNoExportDTO.class);
            return new PageDTO(pageDTO.getPage(), pageDTO.getPageSize(), pageDTO.getTotal(), list);
        }else {
            return null;
        }
    }

    @ApiOperation("下载投放导入模板")
    @PostMapping(CouponCoreRestMsgCenter.BENEFIT_CARD_LAUNCH_ACTIVITY_DOWNLOAD_TEMPLATE)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "templateName", value = "权益卡模板名称", required = true, paramType = "form"),
    })
    public RestResponse downLoadTemplate(String templateName,HttpServletRequest request,HttpServletResponse response) {
        providerBenefitLaunchImportService.downloadImportTemplate(request,response,templateName);
        return null;
    }

    @ApiOperation("查询可投放权益卡")
    @GetMapping(CouponCoreRestMsgCenter.BENEFIT_CARD_LAUNCH_ACTIVITY_TEMPLATE)
    public RestResponse<List<BenefitCardTemplateAndNumDTO>> getTemplateAndCount() {
        return RestClient.requestForList(BaseHttpClient.HttpMethod.GET, couponRestLocator.getRestUrl(CouponCoreRestMsgCenter.BENEFIT_CARD_LAUNCH_ACTIVITY_TEMPLATE), null, null, BenefitCardTemplateAndNumDTO.class);
    }

    @ApiOperation("导出卡号至邮箱")
    @PostMapping(CouponCoreRestMsgCenter.BENEFIT_CARD_LAUNCH_ACTIVITY_EXPORT_CARD_NO_MAIL)
    @ApiImplicitParam(name = "activityNo",value = "活动编号",required = true,paramType="form")
    public RestResponse exportBenefitCardToMail(String activityNo) {
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        BenefitCardNoImportQueryDTO queryDTO  = new BenefitCardNoImportQueryDTO();
        queryDTO.setActivityNo(activityNo);
        queryDTO.setEmail(loginBaseInfo.obtainBaseInfo().getEmail());
        queryDTO.setLoginUserName(loginBaseInfo.obtainBaseInfo().getStaffName());
        if (StringUtils.isBlank(queryDTO.getEmail())){
            return RestResponse.create(9999,"该用户未绑定邮箱",false,null);
        }
        ExecutorConfig.getThreadPool().execute(() -> BenefitLaunchActivityController.this.sendEmails(queryDTO));
        return RestResponse.create(0,"提交成功！请稍后查收电子邮件。（大约耗时3分钟！）",true,0);
    }

    @ExportExcel(fileName = "权益卡号导出.xlsx",filePath = ExportExcelConstants.FILE_PATH, sheet = "权益卡号",isImportant = true)
    private void sendEmails(BenefitCardNoImportQueryDTO dto){
        IzuMailSession izuMailSession = new IzuMailSession();
        izuMailSession.setUserName(dto.getLoginUserName());
        izuMailSession.setMailOpName("权益卡号导出");
        izuMailSession.setMailSubject("【权益卡号导出】导出数据:{0}条");
        izuMailSession.setToMail(dto.getEmail());
        this.send2EmailWithCommon(izuMailSession,dto, BenefitCardNoExportDTO.class);
    }

    @Override
    public PageDTO getDataByPage(Object o, int i, int i1) {
        BenefitCardNoImportQueryDTO queryDTO  = (BenefitCardNoImportQueryDTO) o;
        String url = couponRestLocator.getRestUrl(CouponCoreRestMsgCenter.BENEFIT_CARD_LAUNCH_ACTIVITY_EXPORT_CARD_NO);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("activityNo", queryDTO.getActivityNo());
        paramMap.put("page", i);
        paramMap.put("pageSize", i1);
        RestResponse<PageDTO<BenefitCardDTO>> res = RestClient.requestForPage(BaseHttpClient.HttpMethod.POST, url, paramMap, null, BenefitCardDTO.class);
        if(null!=res && res.isSuccess()){
            PageDTO<BenefitCardDTO> pageDTO = res.getData();
            List<BenefitCardNoExportDTO> list = BeanUtil.copyList(pageDTO.getResult(),BenefitCardNoExportDTO.class);
            return new PageDTO(pageDTO.getPage(), pageDTO.getPageSize(), pageDTO.getTotal(), list);
        }else {
            return null;
        }
    }
}
