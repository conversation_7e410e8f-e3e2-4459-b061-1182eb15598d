package com.izu.mrcar.providerController.benefit;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class BenefitCardExportDTO {

    @ColumnWidth(value = 20)
    @ExcelProperty("权益卡号")
    private String cardCode;
    @ColumnWidth(value = 20)
    @ExcelProperty("权益卡名称")
    private String cardName;
    @ColumnWidth(value = 30)
    @ExcelProperty("服务类型")
    private String orderServiceTypeName;
    @ColumnWidth(value = 20)
    @ExcelProperty("权益卡状态")
    private String cardStatusStr;
    @ColumnWidth(value = 20)
    @ExcelProperty("投放渠道")
    private String channelDepartmentName;
    @ColumnWidth(value = 20)
    @ExcelProperty("投放时间")
    private Date deliveryDate;
    @ColumnWidth(value = 20)
    @ExcelProperty("领取客户")
    private String customCompanyName;
    @ColumnWidth(value = 20)
    @ExcelProperty("领取职员")
    private String receiveStaffName;
    @ColumnWidth(value = 20)
    @ExcelProperty("领取时间")
    private Date receiveDate;
    @ColumnWidth(value = 20)
    @ExcelProperty("下单时间")
    private Date createOrderTime;
    @ColumnWidth(value = 20)
    @ExcelProperty("下单人")
    private String customerName;
    @ColumnWidth(value = 20)
    @ExcelProperty("下单人手机号")
    private String customerMobile;
    @ColumnWidth(value = 20)
    @ExcelProperty("用车人")
    private String bookingPassengerUserName;
    @ColumnWidth(value = 20)
    @ExcelProperty("用车人手机号")
    private String bookingPassengerUserPhone;
    @ColumnWidth(value = 20)
    @ExcelProperty("用车人企业")
    private String customerCompanyName;
    @ColumnWidth(value = 30)
    @ExcelProperty("服务类型（实际）")
    private String serviceName;
    @ColumnWidth(value = 20)
    @ExcelProperty("订单号")
    private String orderNo;
    @ColumnWidth(value = 20)
    @ExcelProperty("账单推送时间")
    private String billPushTime;
    @ColumnWidth(value = 20)
    @ExcelProperty("收入部门")
    private String operateBussName;
    @ColumnWidth(value = 20)
    @ExcelProperty("优惠金额（元）")
    private BigDecimal couponDeductAmount;
    @ColumnWidth(value = 20)
    @ExcelProperty("生效日期")
    private Date beginEffectiveDate;
    @ColumnWidth(value = 20)
    @ExcelProperty("失效日期")
    private Date endEffectiveDate;

}