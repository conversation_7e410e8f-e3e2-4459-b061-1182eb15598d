package com.izu.mrcar.providerController.benefit;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("权益卡号导出")
public class BenefitCardNoExportDTO {

    @ExcelProperty(value = "权益卡名称")
    @ColumnWidth(value = 20)
    private String cardName;

    @ExcelProperty(value = "投放渠道（部门名称）")
    @ColumnWidth(value = 30)
    private String channelDepartmentName;

    @ExcelProperty(value = "权益卡号")
    @ColumnWidth(value = 20)
    private String cardCode;

}

