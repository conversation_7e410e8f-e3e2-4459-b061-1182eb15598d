package com.izu.mrcar.providerController.benefit;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR> dongxiya 2023/12/29 13:45
 */
@Data
public class TBoxStatusExport {
    @ExcelProperty(value = "车牌号码")
    @ColumnWidth(value = 30)
    private String carNo;
    @ExcelProperty(value = "工作模式")
    @ColumnWidth(value = 30)
    private String  workPatternMsg;
    @ExcelProperty(value = "动力状态")
    @ColumnWidth(value = 30)
    private String  powerStatusMsg;
    @ExcelProperty(value = "钥匙状态")
    @ColumnWidth(value = 30)
    private String  keyStatusMsg;
    @ExcelProperty(value = "钥匙插入状态")
    @ColumnWidth(value = 30)
    private String  keyInsertionStatusMsg;
    @ExcelProperty(value = "剩余油量/电量(%)")
    @ColumnWidth(value = 30)
    private String remainingFuel;
    @ExcelProperty(value = "仪表盘里程")
    @ColumnWidth(value = 30)
    private String totalMileage;
    @ExcelProperty(value = "续航里程")
    @ColumnWidth(value = 30)
    private String enduranceMileage;
    @ExcelProperty(value = "档位")
    @ColumnWidth(value = 30)
    private String  gearsStatusMsg;
    @ExcelProperty(value = "供应商")
    @ColumnWidth(value = 30)
    private String manfactName;
    @ExcelProperty(value = "电瓶电压")
    @ColumnWidth(value = 30)
    private String batteryStatus;
    @ExcelProperty(value = "车速")
    @ColumnWidth(value = 30)
    private String speed;
    @ExcelProperty(value = "制动器")
    @ColumnWidth(value = 30)
    private String  parkingStatusMsg;
    @ExcelProperty(value = "车门状态")
    @ColumnWidth(value = 30)
    private String  doorStatusMsg;
    @ExcelProperty(value = "车门锁状态")
    @ColumnWidth(value = 30)
    private String  doorLockStatusMsg;
    @ExcelProperty(value = "车窗状态")
    @ColumnWidth(value = 30)
    private String  windowStatusMsg;
    @ExcelProperty(value = "车灯状态")
    @ColumnWidth(value = 30)
    private String  lampStatusMsg;
    @ExcelProperty(value = "天窗状态")
    @ColumnWidth(value = 30)
    private String  sunroofStatusMsg;
    @ExcelProperty(value = "后尾箱状态")
    @ColumnWidth(value = 30)
    private String rearTailboxStatusMsg;
    @ExcelProperty(value = "上报时间")
    @ColumnWidth(value = 30)
    private Date updateDate;
    @ExcelIgnore
    private Date createDate;

    public Date getUpdateDate() {
        return updateDate!=null?this.updateDate:this.createDate;
    }
}
