package com.izu.mrcar.providerController.benefit;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * <AUTHOR> dongxiya 2023/11/22 15:22
 */
@Data
@ApiModel("权益卡号导出")
public class BenefitCardCodeExportDTO {

    @ExcelProperty(value = "权益卡号")
    @ColumnWidth(value = 30)
    private String cardCode;

    @ExcelProperty(value = "激活码")
    @ColumnWidth(value = 30)
    private String cardSecret;

    @ExcelProperty(value = "权益卡状态")
    @ColumnWidth(value = 20)
    private String cardStatusName;

}

