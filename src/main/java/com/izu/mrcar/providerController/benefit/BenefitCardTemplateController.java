package com.izu.mrcar.providerController.benefit;

import com.izu.CouponCoreRestMsgCenter;
import com.izu.MrCarCouponRestLocator;
import com.izu.dto.benefit.BenefitCardTemplateDetailReqDTO;
import com.izu.dto.benefit.BenefitCardTemplateNameReqDTO;
import com.izu.dto.benefit.BenefitCardTemplateQueryNamesResDTO;
import com.izu.dto.benefit.BenefitCardTemplateQueryPageReqDTO;
import com.izu.dto.benefit.BenefitCardTemplateQueryPageResDTO;
import com.izu.dto.benefit.BenefitCardTemplateSaveReqDTO;
import com.izu.excel.annotation.ExportExcel;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.excel.dto.IzuMailSession;
import com.izu.excel.mail.AbstractIzuMailExcel;
import com.izu.framework.web.exception.RestErrorException;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.config.ExecutorConfig;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.utils.DataPermUtil;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.util.ObjectTransferUtil;
import com.izu.user.util.SingletonFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> dongxiya 2023/11/22 15:26
 */
@Api(tags = "权益卡模板")
@RestController
public class BenefitCardTemplateController  implements AbstractIzuMailExcel<BenefitCardTemplateDetailReqDTO,BenefitCardCodeExportDTO> {

    private final MrCarCouponRestLocator couponRestLocator = SingletonFactory.getSingleton("couponRestLocator", MrCarCouponRestLocator::new);

    @ApiOperation("保存")
    @PostMapping(CouponCoreRestMsgCenter.BENEFIT_TEMPLATE_SAVE)
    public RestResponse save(@RequestBody BenefitCardTemplateSaveReqDTO dto) {
        DataPermUtil.putDataPerm(dto);
        Map<String, Object> params = new HashMap<>(1);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, couponRestLocator.getRestUrl(CouponCoreRestMsgCenter.BENEFIT_TEMPLATE_SAVE), params, null, Map.class);
    }
    @ApiOperation("分页查询")
    @PostMapping(CouponCoreRestMsgCenter.BENEFIT_TEMPLATE_QUERY_PAGE)
    public RestResponse<PageDTO<BenefitCardTemplateQueryPageResDTO>> queryPage(@RequestBody BenefitCardTemplateQueryPageReqDTO dto) {
        DataPermUtil.putDataPerm(dto);
        Map<String, Object> params = new HashMap<>(1);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, couponRestLocator.getRestUrl(CouponCoreRestMsgCenter.BENEFIT_TEMPLATE_QUERY_PAGE), params, null, Map.class);
    }
    @ApiOperation("作废")
    @PostMapping(CouponCoreRestMsgCenter.BENEFIT_TEMPLATE_INVALID)
    public RestResponse invalid(@RequestBody BenefitCardTemplateDetailReqDTO dto) {
        DataPermUtil.putDataPerm(dto);
        Map<String, Object> params = new HashMap<>(1);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, couponRestLocator.getRestUrl(CouponCoreRestMsgCenter.BENEFIT_TEMPLATE_INVALID), params, null, Map.class);
    }
    @ApiOperation("详情")
    @PostMapping(CouponCoreRestMsgCenter.BENEFIT_TEMPLATE_DETAIL)
    public RestResponse<BenefitCardTemplateQueryPageResDTO> detail(@RequestBody BenefitCardTemplateDetailReqDTO dto) {
        DataPermUtil.putDataPerm(dto);
        Map<String, Object> params = new HashMap<>(1);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, couponRestLocator.getRestUrl(CouponCoreRestMsgCenter.BENEFIT_TEMPLATE_DETAIL), params, null, BenefitCardTemplateQueryPageResDTO.class);
    }
    @ApiOperation("生成权益卡")
    @PostMapping(CouponCoreRestMsgCenter.BENEFIT_TEMPLATE_GENERATE_CARDS)
    public RestResponse generateCards(@RequestBody BenefitCardTemplateDetailReqDTO dto) {
        DataPermUtil.putDataPerm(dto);
        Map<String, Object> params = new HashMap<>(1);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, couponRestLocator.getRestUrl(CouponCoreRestMsgCenter.BENEFIT_TEMPLATE_GENERATE_CARDS), params, null, Map.class);
    }

    @ApiOperation("导出券码")
    @PostMapping(CouponCoreRestMsgCenter.BENEFIT_TEMPLATE_EXPORT)
    @ExportExcelWeb(fileName = ExportExcelConstants.FILE_NAME_BENEFIT_CARD_CODE+ ExportExcelConstants.XLSX,filePath = ExportExcelConstants.FILE_PATH,
            sheet = ExportExcelConstants.FILE_NAME_BENEFIT_CARD_CODE, c = BenefitCardCodeExportDTO.class)
    public PageDTO<BenefitCardCodeExportDTO> export(@RequestBody BenefitCardTemplateDetailReqDTO dto, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response) {
        DataPermUtil.putDataPerm(dto);
        Map<String, Object> params = new HashMap<>(1);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        izuEasyExcelSession.setUserName(dto.getStaffName());
        dto.setPageSize(ExportExcelConstants.PAGE_SIZE);
        dto.setPage(izuEasyExcelSession.getPageNo());
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, couponRestLocator.getRestUrl(CouponCoreRestMsgCenter.BENEFIT_TEMPLATE_EXPORT), params, null, BenefitCardCodeExportDTO.class);
        if(restResponse.isSuccess()){
            PageDTO<BenefitCardCodeExportDTO> pageDTO = ObjectTransferUtil.cast(restResponse.getData());
            if (pageDTO.getTotal()> ExportExcelConstants.EXPORT_MAX_LINE){
                throw new RestErrorException(pageDTO.getTotal() + "", ErrorCode.EXPORT_LIMITED_WARN);
            }
            return pageDTO;
        }else {
            return null;
        }
    }

    @ApiOperation("获取有效模板名称")
    @PostMapping(CouponCoreRestMsgCenter.BENEFIT_TEMPLATE_QUERY_NAMES)
    public RestResponse<List<BenefitCardTemplateQueryNamesResDTO>> queryTemplateNames(@RequestBody BenefitCardTemplateNameReqDTO dto) {
        DataPermUtil.putDataPerm(dto);
        Map<String, Object> params = new HashMap<>(1);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, couponRestLocator.getRestUrl(CouponCoreRestMsgCenter.BENEFIT_TEMPLATE_QUERY_NAMES), params, null, BenefitCardTemplateQueryPageResDTO.class);
    }

    @ApiOperation("导出券码至邮箱")
    @PostMapping(CouponCoreRestMsgCenter.BENEFIT_TEMPLATE_EXPORT_EMAIL)
    public RestResponse exportBenefitCardToMail(@RequestBody BenefitCardTemplateDetailReqDTO dto) {
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        dto.setStaffName(loginBaseInfo.obtainBaseInfo().getStaffName());
        dto.setEmail(loginBaseInfo.obtainBaseInfo().getEmail());
        if (StringUtils.isBlank(dto.getEmail())){
            return RestResponse.create(9999,"该用户未绑定邮箱",false,null);
        }
        ExecutorConfig.getThreadPool().execute(() -> BenefitCardTemplateController.this.sendEmails(dto));
        return RestResponse.create(0,"提交成功！请稍后查收电子邮件。（大约耗时3分钟！）",true,0);
    }

    @ExportExcel(fileName = "权益卡券码导出.xlsx",filePath = ExportExcelConstants.FILE_PATH, sheet = "权益卡券码",isImportant = true)
    private void sendEmails(BenefitCardTemplateDetailReqDTO dto){
        IzuMailSession izuMailSession = new IzuMailSession();
        izuMailSession.setUserName(dto.getStaffName());
        izuMailSession.setMailOpName("权益卡券码导出");
        izuMailSession.setMailSubject("【权益卡券码导出】导出数据:{0}条");
        izuMailSession.setToMail(dto.getEmail());
        this.send2EmailWithCommon(izuMailSession,dto, BenefitCardCodeExportDTO.class);
    }

    @Override
    public PageDTO getDataByPage(Object o, int i, int i1) {
        BenefitCardTemplateDetailReqDTO dto = (BenefitCardTemplateDetailReqDTO) o;
        dto.setPage(i);
        dto.setPageSize(i1);
        Map<String, Object> params = new HashMap<>(1);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, couponRestLocator.getRestUrl(CouponCoreRestMsgCenter.BENEFIT_TEMPLATE_EXPORT), params, null, BenefitCardCodeExportDTO.class);
        if(restResponse.isSuccess()){
            return ObjectTransferUtil.cast(restResponse.getData());
        }else {
            return null;
        }
    }
}
