package com.izu.mrcar.providerController.expenditure;

import com.google.common.collect.Lists;
import com.izu.business.config.BusinessRestLocator;
import com.izu.business.consts.expenditure.BusinessExpenditureTypeEnum;
import com.izu.business.dto.expenditure.req.ExpenditureAmountSum4AddReqDTO;
import com.izu.business.dto.expenditure.req.ListSupplierExpenditureDetail4AddDTO;
import com.izu.business.dto.expenditure.req.ListSupplierExpenditureDetailDTO;
import com.izu.business.dto.expenditure.req.ListSupplierExpenditureReqDTO;
import com.izu.business.dto.expenditure.req.SupplierExpenditureAddReqDTO;
import com.izu.business.dto.expenditure.req.SupplierExpenditureDTO;
import com.izu.business.dto.expenditure.req.SupplierExpenditureDetailQueryReqDTO;
import com.izu.business.dto.expenditure.req.SupplierExpenditureEditReqDTO;
import com.izu.business.dto.expenditure.req.SupplierExpenditureOperationLogReqDTO;
import com.izu.business.dto.expenditure.req.SupplierExpenditureOptReqDTO;
import com.izu.business.dto.expenditure.resp.ExpenditureAmountSumRespDTO;
import com.izu.business.dto.expenditure.resp.SupplierExpenditureDetailDTO;
import com.izu.business.dto.expenditure.resp.SupplierExpenditureDetailExportDTO;
import com.izu.business.dto.expenditure.resp.SupplierExpenditureDetailRespDTO;
import com.izu.business.dto.expenditure.resp.SupplierExpenditureOperationLogDTO;
import com.izu.carasset.dto.input.StructAssetParam;
import com.izu.carasset.dto.output.StructAssetDTO;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.dto.charge.ExpenditureChargeAmountSumReqDTO;
import com.izu.mrcar.order.dto.charge.ExpenditureChargeAmountSumRespDTO;
import com.izu.mrcar.order.dto.charge.ListExpenditureChargeDetailReqDTO;
import com.izu.mrcar.service.carAsset.CarAssetStructService;
import com.izu.mrcar.service.expenditure.SupplierExpenditureService;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.business.BusinessUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.dto.company.ExpenditureSupplierInfoDTO;
import com.izu.user.dto.company.ListExpenditureSupplierReDTO;
import com.izu.user.dto.staff.pc.DataPermInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.enums.perm.ProviderDataPermTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/provider/supplier/expenditure")
@Api(tags = "供应商支出单相关操作")
public class ProviderSupplierExpenditureController {
    private static final BusinessRestLocator businessRestLocator =  new BusinessRestLocator();
    private static final MrcarOrderRestLocator orderRestLocator = new MrcarOrderRestLocator();
    private static final UserRestLocator userRestLocator = new UserRestLocator();



    @Resource
    private SupplierExpenditureService supplierExpenditureService;
    @Resource
    private CarAssetStructService carAssetStructService;


    @RequestFunction(functionName = "支出单确认提交")
    @ApiOperation(value = "支出单确认提交")
    @PostMapping("/confirmSubmit")
    public RestResponse<Boolean> confirmSubmit(@RequestBody SupplierExpenditureEditReqDTO reqDTO) {
        BusinessUtil.setDataPerm(reqDTO);
        String url = businessRestLocator.getRestUrl("/supplier/expenditure/confirmSubmit");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, Boolean.class);
    }


    @RequestFunction(functionName = "添加支出单")
    @ApiOperation(value = "添加支出单")
    @PostMapping("/add")
    public RestResponse<Boolean> add(@RequestBody SupplierExpenditureAddReqDTO reqDTO) {
        BusinessUtil.setDataPerm(reqDTO);
        String url = businessRestLocator.getRestUrl("/supplier/expenditure/add");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, Boolean.class);
    }

    @RequestFunction(functionName = "编辑支出单")
    @ApiOperation(value = "编辑支出单")
    @PostMapping("/edit")
    public RestResponse<Boolean> edit(@RequestBody SupplierExpenditureEditReqDTO reqDTO) {
        BusinessUtil.setDataPerm(reqDTO);
        String url = businessRestLocator.getRestUrl("/supplier/expenditure/edit");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, Boolean.class);
    }


    private void calculatePermInfo(ListSupplierExpenditureReqDTO reqDTO){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        Byte dataPermType = providerLoginInfo.getProviderDataPerm().getDataPermType();
        // 全部则不设条件
        if(Objects.equals(ProviderDataPermTypeEnum.ALL.getType(), dataPermType)){
            return;
        }
        Boolean selfDeptOrAssignDept = Objects.equals(ProviderDataPermTypeEnum.SELF_DEPT.getType(), dataPermType) ||
                Objects.equals(ProviderDataPermTypeEnum.ASSIGN_DEPT.getType(), dataPermType);
        List<DataPermInfo> dataPermInfoList = providerLoginInfo.getProviderDataPerm().getDataPermInfoList();
        if(selfDeptOrAssignDept && CollectionUtils.isNotEmpty(dataPermInfoList)){
            //查询所属部门
            String url = userRestLocator.getRestUrl(UserUrlCenter.PROVIDER_HEAD_DEPARTMENT_LIST);
            RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, url, null, null, String.class);
            if(restResponse!=null && restResponse.isSuccess() && restResponse.getData()!=null){
                List<String> list = (List<String>) restResponse.getData();
                for(DataPermInfo permInfo : dataPermInfoList){
                    if(list.contains(permInfo.getDataPermCode())){
                        // 等同于所有
                        reqDTO.setDataPermType(ProviderDataPermTypeEnum.ALL.getType());
                        return;
                    }
                }
            }
            List<String> bussStructCodes = dataPermInfoList.stream().map(
                    dp -> dp.getDataPermCode()).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            StructAssetParam structAssetParam = new StructAssetParam();
            structAssetParam.setBussStructCodes(bussStructCodes);
            List<StructAssetDTO> structAssetList = carAssetStructService.listStructAsset(structAssetParam);
            if(CollectionUtils.isNotEmpty(structAssetList)){
                reqDTO.setOperateBussCodeList(structAssetList.stream().map(StructAssetDTO::getAssetStructId).collect(Collectors.toList()));
            }

        }
    }

    @RequestFunction(functionName = "支出单列表")
    @ApiOperation(value = "支出单列表")
    @PostMapping("/list")
    public RestResponse<PageDTO<SupplierExpenditureDTO>> list(@RequestBody ListSupplierExpenditureReqDTO reqDTO) {
        BusinessUtil.setDataPerm(reqDTO);
        // 计算权限信息
        calculatePermInfo(reqDTO);
        String url = businessRestLocator.getRestUrl("/supplier/expenditure/list");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, SupplierExpenditureDTO.class);
    }

    @RequestFunction(functionName = "支出单导出")
    @ApiOperation(value = "支出单导出")
    @PostMapping("/export")
    public void export(@RequestBody ListSupplierExpenditureReqDTO reqDTO,
                       IzuEasyExcelSession izuEasyExcelSession,
                       HttpServletResponse response) {
        BusinessUtil.setDataPerm(reqDTO);
        // 计算权限信息
        calculatePermInfo(reqDTO);
        reqDTO.setPage(1);
        reqDTO.setPageSize(2000);
        izuEasyExcelSession.setPageNo(1);
        supplierExpenditureService.export(reqDTO, izuEasyExcelSession, response);
        return ;
    }

    @RequestFunction(functionName = "支出单明细列表")
    @ApiOperation(value = "支出单明细列表")
    @PostMapping("/listDetail")
    public RestResponse<PageDTO<SupplierExpenditureDetailDTO>> listDetail(@RequestBody ListSupplierExpenditureDetailDTO reqDTO) {
        String url = businessRestLocator.getRestUrl("/supplier/expenditure/detail/list");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, SupplierExpenditureDetailDTO.class);
    }

    @RequestFunction(functionName = "支出单明细导出")
    @ApiOperation(value = "支出单明细导出")
    @PostMapping("/exportDetail")
    public void exportDetail(@RequestBody ListSupplierExpenditureDetailDTO reqDTO,
                             IzuEasyExcelSession izuEasyExcelSession,
                             HttpServletResponse response) {

        reqDTO.setPage(1);
        reqDTO.setPageSize(2000);
        izuEasyExcelSession.setPageNo(1);
        PageDTO<SupplierExpenditureDetailExportDTO>  pageDTO = supplierExpenditureService.exportDetail(reqDTO, izuEasyExcelSession, response);
        return;
    }

    @RequestFunction(functionName = "支出单信息")
    @ApiOperation(value = "支出单信息")
    @PostMapping("/expenditureInfo")
    public RestResponse<SupplierExpenditureDetailRespDTO> expenditureInfo(@RequestBody SupplierExpenditureDetailQueryReqDTO reqDTO) {
        String url = businessRestLocator.getRestUrl("/supplier/expenditure/expenditureInfo");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, SupplierExpenditureDetailRespDTO.class);
    }

    @RequestFunction(functionName = "推送支出单")
    @ApiOperation(value = "推送支出单")
    @PostMapping("/push")
    public RestResponse<Boolean> push(@RequestBody SupplierExpenditureOptReqDTO reqDTO) {
        BusinessUtil.setDataPerm(reqDTO);
        String url = businessRestLocator.getRestUrl("/supplier/expenditure/push");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, Boolean.class);
    }

    @RequestFunction(functionName = "废弃支出单")
    @ApiOperation(value = "废弃支出单")
    @PostMapping("/discard")
    public RestResponse<Boolean> discard(@RequestBody SupplierExpenditureOptReqDTO reqDTO) {
        BusinessUtil.setDataPerm(reqDTO);
        String url = businessRestLocator.getRestUrl("/supplier/expenditure/discard");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, Boolean.class);
    }


    @RequestFunction(functionName = "支出单重新推送")
    @ApiOperation(value = "支出单重新推送")
    @PostMapping("/repush")
    public RestResponse<Boolean> repush(@RequestBody SupplierExpenditureOptReqDTO reqDTO) {
        BusinessUtil.setDataPerm(reqDTO);
        String url = businessRestLocator.getRestUrl("/supplier/expenditure/repush");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, Boolean.class);
    }


    @RequestFunction(functionName = "查询支出单明细列表为添加场景使用")
    @ApiOperation(value = "查询支出单明细列表为添加场景使用")
    @PostMapping("/listExpenditureDetail4Add")
    public RestResponse<PageDTO<SupplierExpenditureDetailDTO>> listExpenditureDetail4Add(@RequestBody ListSupplierExpenditureDetail4AddDTO reqDTO) {

        PageDTO<SupplierExpenditureDetailDTO> pageDTO = supplierExpenditureService.listExpenditureDetail4Add(reqDTO);
        return RestResponse.success(pageDTO);

    }

    @RequestFunction(functionName = "查询支出单操作日志列表")
    @ApiOperation(value = "查询支出单操作日志列表")
    @PostMapping("/listOperationLog")
    public RestResponse<PageDTO<SupplierExpenditureOperationLogDTO>> listOperationLog(@RequestBody SupplierExpenditureOperationLogReqDTO reqDTO) {
        String url = businessRestLocator.getRestUrl("/supplier/expenditure/listOperationLog");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, SupplierExpenditureOperationLogDTO.class);
    }

    @RequestFunction(functionName = "查询支出单明细包含单据ID列表")
    @ApiOperation(value = "查询支出单明细包含单据ID列表")
    @PostMapping("/listExpenditureReceiptId")
    public RestResponse<List<Long>> listExpenditureReceiptId(@RequestBody ListSupplierExpenditureDetail4AddDTO reqDTO) {

        if(Objects.equals(reqDTO.getExpenditureType(), BusinessExpenditureTypeEnum.CHARGE.getCode())){
            // 转换入参
            ListExpenditureChargeDetailReqDTO chargeReqDTO = BeanUtil.copyObject(reqDTO, ListExpenditureChargeDetailReqDTO.class);
            // 调用mrcar-order-core接口
            String url = orderRestLocator.getRestUrl("/charge/bill/listExpenditureChargeOrderId");
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, chargeReqDTO);

            RestResponse<List<Long>> result = RestClient.requestForList(
                    BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, Long.class);
            return result;
        }
        return RestResponse.success(Lists.newArrayList());
    }




    @RequestFunction(functionName = "查询支出供应商列表")
    @ApiOperation(value = "查询支出供应商列表")
    @PostMapping("/listSupplierInfo")
    public RestResponse<List<ExpenditureSupplierInfoDTO>> listSupplierInfo(@RequestBody ListExpenditureSupplierReDTO reqDTO) {
        String url = userRestLocator.getRestUrl("/expenditure/supplier/list");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, ExpenditureSupplierInfoDTO.class);
    }

    @RequestFunction(functionName = "新增场景计算明细总金额")
    @ApiOperation(value = "新增场景计算明细总金额（含税）和明细总金额（不含税）")
    @PostMapping("/sumExpenditureAmount4Add")
    public RestResponse<ExpenditureAmountSumRespDTO> sumExpenditureAmountForAdd(@RequestBody ExpenditureAmountSum4AddReqDTO reqDTO) {
        // 新增场景：调用 mrcar-order-core 的充电支出明细金额汇总接口
        if (Objects.equals(reqDTO.getExpenditureType(), BusinessExpenditureTypeEnum.CHARGE.getCode())) {
            // 转换请求参数
            ExpenditureChargeAmountSumReqDTO chargeReqDTO = BeanUtil.copyObject(reqDTO, ExpenditureChargeAmountSumReqDTO.class);

            // 调用 mrcar-order-core 接口
            String url = orderRestLocator.getRestUrl("/charge/bill/sumExpenditureChargeAmount");
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, chargeReqDTO);

            RestResponse<ExpenditureChargeAmountSumRespDTO> result = RestClient.requestForObject(
                    BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, ExpenditureChargeAmountSumRespDTO.class);

            if (result.isSuccess() && result.getData() != null) {
                // 转换响应参数
                ExpenditureAmountSumRespDTO respDTO = BeanUtil.copyObject(result.getData(), ExpenditureAmountSumRespDTO.class);
                return RestResponse.success(respDTO);
            }
            return RestResponse.create(99,"调用充电支出明细金额汇总接口失败", false, null);
        }
        return RestResponse.success(new ExpenditureAmountSumRespDTO());
    }

}