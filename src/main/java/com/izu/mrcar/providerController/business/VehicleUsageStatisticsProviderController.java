package com.izu.mrcar.providerController.business;

import com.google.common.collect.Lists;
import com.izu.business.config.BusinessRestLocator;
import com.izu.business.dto.StatisticsVehicleUsageDTO;
import com.izu.business.dto.input.VehicleUsageQueryDto;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.excel.Column;
import com.izu.framework.web.util.excel.ExSheet;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.controller.AbstractExcelDownloadController;
import com.izu.mrcar.utils.DataPermUtil;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.enums.perm.ClientDataPermTypeEnum;
import com.izu.user.enums.perm.ProviderDataPermTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.*;

@RestController
@RequestMapping("/provider/statistics")
@Api(tags = "车辆行程统计")
@Slf4j
public class VehicleUsageStatisticsProviderController extends AbstractExcelDownloadController {

    @PostMapping("/vehicleUsage")
    @RequestFunction(functionName = "车辆使用详情")
    @ApiOperation(value = "车辆使用详情")
    public RestResponse<PageDTO<StatisticsVehicleUsageDTO>> vehicleUsage(@RequestBody VehicleUsageQueryDto param) {

        String restUrl = new BusinessRestLocator().getRestUrl("/statistics/vehicleUsage");
        Map<String,Object> params=new HashMap<>();
        setDataPerm(param);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, params, null, Map.class);
    }

    @PostMapping("/vehicleUsage/export")
    @RequestFunction(functionName = "车辆使用详情导出")
    @ApiOperation(value = "车辆使用详情导出")
    public void vehicleUsage(@RequestBody VehicleUsageQueryDto param, HttpServletRequest request, HttpServletResponse response) {
        try {
            List<StatisticsVehicleUsageDTO> resultData;
            Map<String,Object> params=new HashMap<>();
            setDataPerm(param);
            params.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
            Integer pageSize = 5000;
            Integer maxSize = 20000;
            param.setPage(1);
            param.setPageSize(pageSize);
            String restUrl = new BusinessRestLocator().getRestUrl("/statistics/vehicleUsage");

            RestResponse result = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, params, null, StatisticsVehicleUsageDTO.class);
            if (!result.isSuccess()) {
                return;
            }

            PageDTO pageDTO = (PageDTO) result.getData();
            resultData = pageDTO.getResult();
            if (pageDTO.getTotal() > pageSize) {
                // 一次拿pageSize条
                int totalPage = (int) ((Math.min(pageDTO.getTotal(), maxSize) + pageSize - 1) / pageSize);
                for (int i = 2; i <= totalPage; i++) {
                    param.setPage(i);
                    result = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, params, null, StatisticsVehicleUsageDTO.class);
                    pageDTO = (PageDTO) result.getData();
                    resultData.addAll(pageDTO.getResult());
                }
            }

            exportExcel(resultData, request, response);
        } catch (Exception e) {
            log.error("导出车辆使用状况异常", e);
        }
    }

    private void exportExcel(List<StatisticsVehicleUsageDTO> datas, HttpServletRequest request ,HttpServletResponse response) {

        List<Column> columnModes = new ArrayList<>();
        columnModes.add(new Column("vehicleLicense", "车牌号", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("vehicleVin", "车架号", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("vehicleModel", "车型", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("belongCityName", "所在城市", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("structName", "所属部门", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        // add
        columnModes.add(new Column("dayTravelDurationString", "行驶时长（时:分）", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("totalMileage", "行程明细汇总里程（km）", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("hisTotalMileage", "仪表盘里程数（km）", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("dayTotalMileage", "仪表盘行驶里程（km）", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        // add
        columnModes.add(new Column("dayTravelMileage", "轨迹总里程（km）", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("violationCount", "违章总数量", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("maintainCount", "维保总次数", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("companyName", "所属企业", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        final List<Map<String, String>> rowDatas = new ArrayList<Map<String, String>>(datas.size() + 1);
        for (StatisticsVehicleUsageDTO data : datas) {
            final Map<String, String> rowdata = new HashMap<String, String>(columnModes.size() * 2);
            rowdata.put("vehicleLicense", data.getVehicleLicense());
            rowdata.put("vehicleVin", data.getVehicleVin());
            rowdata.put("vehicleModel", data.getVehicleModel());
            rowdata.put("belongCityName", data.getBelongCityName());
            rowdata.put("structName", data.getStructName());
            rowdata.put("dayTravelDurationString",data.getDayTravelDurationString());
            rowdata.put("totalMileage", data.getTotalMileage().toString());
            rowdata.put("hisTotalMileage", data.getHisTotalMileage().toString());
            rowdata.put("dayTotalMileage", data.getDayTotalMileage().toString());
            rowdata.put("dayTravelMileage", data.getDayTravelMileage().toString());
            rowdata.put("violationCount", data.getViolationCount().toString());
            rowdata.put("maintainCount", data.getMaintainCount().toString());
            rowdata.put("companyName", data.getCompanyName());
            rowDatas.add(rowdata);
        }
        List<ExSheet> exSheetList = new ArrayList<>(1);
        exSheetList.add(new ExSheet(columnModes, rowDatas, "车辆使用状况"));
        //---------------------------B: 根据上面创建好的Sheet，生成Excel
        String excelType = "xlsx";
        String fileNameCn = "车辆使用状况_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + "." + excelType;
        String fileNameEn = "vehicle_usage_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + "." + excelType;
        this.exportExcel(request, response, fileNameCn, fileNameEn, exSheetList);
    }

    private void setDataPerm(VehicleUsageQueryDto param) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        param.setDataPermType(providerLoginInfo.obtainDataPerm().getDataPermType());
        param.setSystemType(providerLoginInfo.getSystemType());
        ProviderDataPermTypeEnum dataPermTypeEnum = ProviderDataPermTypeEnum.getByType(providerLoginInfo.obtainDataPerm().getDataPermType());
        switch (dataPermTypeEnum) {
            case RESPONSIBLE_CONTRACT:// 负责合同
                param.setDataCodeSet(providerLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.VEHICLE).getDataCodeSet());
                break;
            case RESPONSIBLE_CUSTOMER:// 负责客户
                param.setDataCodeSet(providerLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.ID).getDataCodeSet());
                break;
            case ASSIGN_CUSTOMER:// 指定客户
                param.setDataCodeSet(providerLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.ID).getDataCodeSet());
                break;
            case SELF_DEPT:// 所属部门
                param.setDataCodeSet(providerLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet());
                break;
            case ASSIGN_DEPT:// 指定部门
                param.setDataCodeSet(providerLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet());
                break;
            case ALL:// 所有
                break;
            case ONE_SELF:// 本人
                break;
        }
    }
}
