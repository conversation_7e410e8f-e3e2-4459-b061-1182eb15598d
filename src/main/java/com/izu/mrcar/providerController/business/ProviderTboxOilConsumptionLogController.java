package com.izu.mrcar.providerController.business;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.izu.business.config.BusinessRestLocator;
import com.izu.business.dto.oil.ProviderVehicleTboxOilConsumptionExportDTO;
import com.izu.business.dto.oil.VehicleTboxOilConsumptionExportDTO;
import com.izu.business.dto.oil.VehicleTboxOilConsumptionRespDTO;
import com.izu.business.dto.oil.VehicleTboxOilConsumptionSearchDTO;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import com.izu.user.dto.staff.pc.DataPerm;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.enums.perm.ClientDataPermTypeEnum;
import com.izu.user.enums.perm.ProviderDataPermTypeEnum;
import com.izu.user.enums.role.SystemTypeEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

import static com.izu.mrcar.common.errorcode.ErrorCode.STAT_FUNCTION_OPERATION_EXPORT_EXCEED;

/**
 * 车机油耗日志
 */
@RestController
@RequestMapping("/provider/oil/tbox/log")
public class ProviderTboxOilConsumptionLogController {

    /**
     * 列表(运营端)
     */
    @PostMapping("/search")
    @SuppressWarnings("unchecked")
    public RestResponse<PageDTO<VehicleTboxOilConsumptionRespDTO>> search(
            @RequestBody VehicleTboxOilConsumptionSearchDTO searchDTO) {
        // 数据权限
        LoginBaseInfo loginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = loginInfo.obtainBaseInfo();

        DataPerm dataPerm = loginInfo.obtainDataPerm();
        // 登录方式
        searchDTO.setSystemType(loginInfo.getSystemType());
        // 权限范围
        searchDTO.setDataPermType(dataPerm.getDataPermType());
        // 登录人信息
        searchDTO.setLoginUserId(baseInfo.getStaffId());

        Map<String, Object> params = new HashMap<>();
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, searchDTO);
        String restUrl = new BusinessRestLocator().getRestUrl("/oil/tbox/search");
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, params, null,
                VehicleTboxOilConsumptionRespDTO.class);
    }


    /**
     * 导出(运营端)
     */
    @PostMapping("/export")
    @SuppressWarnings("unchecked")
    public void export(@RequestBody VehicleTboxOilConsumptionSearchDTO searchDTO,
                       HttpServletRequest request,
                       HttpServletResponse response) throws IOException {
        // 数据权限
        LoginBaseInfo loginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = loginInfo.obtainBaseInfo();

        DataPerm dataPerm = loginInfo.obtainDataPerm();
        // 登录方式
        searchDTO.setSystemType(loginInfo.getSystemType());
        // 权限范围
        searchDTO.setDataPermType(dataPerm.getDataPermType());
        // 登录人信息
        searchDTO.setLoginUserId(baseInfo.getStaffId());

        int page = 1;
        int pageSize = 100;
        int pages;
        List<ProviderVehicleTboxOilConsumptionExportDTO> result = new ArrayList<>();
        do {
            searchDTO.setPage(page);
            searchDTO.setPageSize(pageSize);

            String url = new BusinessRestLocator().getRestUrl("/oil/tbox/search");
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, searchDTO);
            @SuppressWarnings("unchecked")
            RestResponse<PageDTO<VehicleTboxOilConsumptionRespDTO>> restResponse =
                    RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null,
                            VehicleTboxOilConsumptionRespDTO.class);
            PageDTO<VehicleTboxOilConsumptionRespDTO> pageDTO = restResponse.getData();

            // 校验记录条数
            if (pageDTO.getTotal() > ExportExcelConstants.EXPORT_MAX_LINE) {
                RestResponse<?> rest = RestResponse.fail(STAT_FUNCTION_OPERATION_EXPORT_EXCEED,
                        pageDTO.getTotal(), ExportExcelConstants.EXPORT_MAX_LINE);
                JSON.writeJSONString(response.getOutputStream(), rest);
                response.setContentType("application/json");
                return;
            }

            // 设置页码信息
            pages = pageDTO.getPages();
            result.addAll(
                    pageDTO.getResult()
                            .stream()
                            .map(s -> {
                                ProviderVehicleTboxOilConsumptionExportDTO dto = new ProviderVehicleTboxOilConsumptionExportDTO();
                                BeanUtils.copyProperties(s, dto);
                                return dto;
                            }).collect(Collectors.toList()));
            page++;
        } while (page <= pages);

        String fileName = URLEncoder.encode("车机油耗日志", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), ProviderVehicleTboxOilConsumptionExportDTO.class)
                .sheet("车机油耗日志").doWrite(result);
    }

}
