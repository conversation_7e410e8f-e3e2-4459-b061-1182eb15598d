package com.izu.mrcar.providerController.business;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.consts.VehicleEnum;
import com.izu.asset.dto.vehicle.CarInfoClientRespDTO;
import com.izu.asset.dto.vehicle.CarInfoOperationPageReqDTO;
import com.izu.asset.dto.vehicle.CarInfoQueryConditionDTO;
import com.izu.carasset.util.StringUtils;
import com.izu.excel.annotation.ExportExcel;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.excel.dto.IzuMailSession;
import com.izu.excel.mail.AbstractIzuMailExcel;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.lbs.common.LbsRestLocator;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.constants.MrcarUrl;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.controller.business.LbsTravelSearchDTO;
import com.izu.mrcar.controller.business.VehicleTravelExportDTO;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.perm.VehicleListDataPermUtil;
import com.izu.user.dto.company.CompanyDTO;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.restApi.BusinessUserApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 运营端-行驶里程明细和里程统计
 *
 * <AUTHOR> on  2024/3/18 9:40
 */
@Api(tags = "BI与运营")
@RestController
public class VehicleTravelProviderController implements AbstractIzuMailExcel<LbsTravelSearchDTO, VehicleTravelExportDTO> {

    private static Logger logger = LoggerFactory.getLogger(VehicleTravelProviderController.class);

    public static final String FILE_PATH = "/data/logs/mrcar/tmp";


    @PostMapping(MrcarUrl.PROVIDER_TRIP_DETAIL_LIST)
    @ApiOperation(value = "客户报表-车辆里程-车辆行程明细")
    @RequestFunction(functionName = "客户报表-车辆里程-车辆行程明细")
    public RestResponse<PageDTO<VehicleTravelExportDTO>> getTravelInfoList(@RequestBody LbsTravelSearchDTO lbsTravelSearchDTO) {
        //没有选择客户，不允许查询
        if (CollectionUtil.isEmpty(lbsTravelSearchDTO.getCompanyIds())) {
            return RestResponse.success(new PageDTO(lbsTravelSearchDTO.getPageNo(), lbsTravelSearchDTO.getPageSize(),
                    0, new ArrayList<VehicleTravelExportDTO>(1)));
        }
        return RestResponse.success(getDataByPage(lbsTravelSearchDTO, lbsTravelSearchDTO.getPageNo(), lbsTravelSearchDTO.getPageSize()));
    }


    @RequestMapping(value = MrcarUrl.PROVIDER_TRIP_DETAIL_EXPORT, method = RequestMethod.POST)
    @RequestFunction(functionName = "客户报表-车辆里程-车辆行程明细-导出")
    @ApiOperation(value = "客户报表-车辆里程-车辆行程明细-导出")
    @ExportExcelWeb(fileName = "车辆行程明细.xlsx", filePath = FILE_PATH, sheet = "车辆行程明细", c = VehicleTravelExportDTO.class)
    public PageDTO exportTravelInfo(@RequestBody LbsTravelSearchDTO lbsTravelSearchDTO, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response) {
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        String userName = loginBaseInfo.obtainBaseInfo().getStaffName();
        lbsTravelSearchDTO.setPageNo(izuEasyExcelSession.getPageNo());
        izuEasyExcelSession.setUserName(userName);
        lbsTravelSearchDTO.setPageSize(1000);
        // 初始化数据权限
        try {
            return this.getDataByPage(lbsTravelSearchDTO, lbsTravelSearchDTO.getPageNo(), lbsTravelSearchDTO.getPageSize());
        } catch (Exception e) {
            logger.error("车辆行程明细-导出异常", e);
            return null;
        }
    }

    @RequestMapping(value = MrcarUrl.PROVIDER_TRIP_DETAIL_EXPORT_TO_EMAIL,method = RequestMethod.POST)
    @ApiOperation(value = "车辆行程明细-导出邮箱")
    @RequestFunction(functionName = "车辆行程明细-导出邮箱")
    public RestResponse sendEmail(@RequestBody LbsTravelSearchDTO lbsTravelSearchDTO){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        lbsTravelSearchDTO.setUserName(loginBaseInfo.obtainBaseInfo().getStaffName());
        if (StringUtils.isBlank(lbsTravelSearchDTO.getEmail())){
            return RestResponse.create(9999,"邮箱号不能为空",false,null);
        }
        new Thread(new Runnable() {
            @Override
            public void run() {
                VehicleTravelProviderController.this.sendEmails(lbsTravelSearchDTO);
            }
        }).start();
        return RestResponse.create(0,"提交成功！请稍后查收电子邮件。（大约耗时3分钟！）",true,0);
    }

    @ExportExcel(fileName = "车辆行程明细.xlsx",filePath = FILE_PATH, sheet = "车辆行程明细",isImportant = true)
    private void sendEmails(LbsTravelSearchDTO lbsTravelSearchDTO){
        IzuMailSession izuMailSession = new IzuMailSession();
        izuMailSession.setUserName(lbsTravelSearchDTO.getUserName());
        izuMailSession.setMailOpName("车辆行程明细");
        izuMailSession.setMailSubject("【车辆行程明细】导出数据:{0}条");
        izuMailSession.setToMail(lbsTravelSearchDTO.getEmail());
        this.send2EmailWithCommon(izuMailSession,lbsTravelSearchDTO, VehicleTravelExportDTO.class);
    }


    @Override
    public PageDTO getDataByPage(Object param, int pageNum, int pageSize) {
        //没有选择客户，不允许查询
        LbsTravelSearchDTO lbsTravelSearchDTO = (LbsTravelSearchDTO) param;
        lbsTravelSearchDTO.setPageNo(pageNum);
        lbsTravelSearchDTO.setPageSize(pageSize);
        //获取当前客户运营端的数据权限
        CarInfoOperationPageReqDTO operationPageReqDTO = new CarInfoOperationPageReqDTO();
        VehicleListDataPermUtil.setProviderDataPerm(operationPageReqDTO);
        //设置车辆相关的查询条件
        operationPageReqDTO.setVehicleLicense(lbsTravelSearchDTO.getVehicleLicense());
        operationPageReqDTO.setVehicleVin(lbsTravelSearchDTO.getVehicleVin());
        operationPageReqDTO.setCompanyId(lbsTravelSearchDTO.getCompanyIds().get(0));
        operationPageReqDTO.setPage(1);
        operationPageReqDTO.setPageSize(Integer.MAX_VALUE);
        CarInfoQueryConditionDTO carInfoQueryCondition = BeanUtil.copyObject(operationPageReqDTO, CarInfoQueryConditionDTO.class);
        carInfoQueryCondition.setVehicleStatus(VehicleEnum.VehicleStatus.normal.getValue());
        carInfoQueryCondition.setIsMrcar((byte) 1);
        String restUrl = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.CAR_INFO_GET_SIMPLE_FILED_LIST);
        Map<String, Object> params = new HashMap<>();
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, carInfoQueryCondition);
        RestResponse vehicleInfoResp = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, params, null, CarInfoClientRespDTO.class);
        if (!vehicleInfoResp.isSuccess()) {
            logger.error("获取车辆信息失败 param={},response={}", JSON.toJSONString(operationPageReqDTO), JSON.toJSONString(vehicleInfoResp));
            throw ExceptionFactory.createRestException(ErrorCode.GET_CAR_LIST_FAIL);
        }
        PageDTO<CarInfoClientRespDTO> pageDTO = (PageDTO<CarInfoClientRespDTO>) vehicleInfoResp.getData();
        List<CarInfoClientRespDTO> result = pageDTO.getResult();
        if (CollectionUtil.isEmpty(result)) {
            return new PageDTO(lbsTravelSearchDTO.getPageNo(), lbsTravelSearchDTO.getPageSize(),
                    0, new ArrayList<VehicleTravelExportDTO>(1));
        }
        params = JSON.parseObject(JSON.toJSONString(lbsTravelSearchDTO)).getInnerMap();
        List<String> vehicleVinList = result.stream().map(CarInfoClientRespDTO::getVehicleVin).collect(Collectors.toList());
        params.put("vehicleVinList", vehicleVinList);
        restUrl = new LbsRestLocator().getRestUrl("/lbs/travelInfo/v2/list.json");
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, params, null, VehicleTravelExportDTO.class);
        if (!restResponse.isSuccess()) {
            logger.error("获取车辆行程信息失败 param={},response={}", JSON.toJSONString(params), JSON.toJSONString(restResponse));
            throw ExceptionFactory.createRestException(ErrorCode.GET_CAR_TRAVEL_INFO_FAIL);
        }
        PageDTO<VehicleTravelExportDTO> travelPageDTO = (PageDTO) restResponse.getData();
        List<VehicleTravelExportDTO> list = travelPageDTO.getResult();
        CompanyDTO companyDTO = BusinessUserApi.getCompanyBycompanyId(lbsTravelSearchDTO.getCompanyIds().get(0));
        list.forEach(vehicleTravelExportDTO -> {
            String avgOil = vehicleTravelExportDTO.getAvgOil();
            String useOil = "-";
            if (StringUtils.isNotBlank(avgOil) && !avgOil.equals("-")) {
                BigDecimal avgOilB = new BigDecimal(avgOil);
                BigDecimal useOilB = avgOilB.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)
                        .multiply(vehicleTravelExportDTO.getTravelMile()).setScale(2, RoundingMode.HALF_UP);
                useOil = useOilB.toString();
            }
            vehicleTravelExportDTO.setUseOil(useOil);
            vehicleTravelExportDTO.setCompanyName(companyDTO.getCompanyName());
        });
        return travelPageDTO;
    }
}
