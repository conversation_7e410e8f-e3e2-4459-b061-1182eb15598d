package com.izu.mrcar.providerController.business;

import com.izu.business.config.BusinessRestLocator;
import com.izu.business.consts.resturl.MrcarBusinessRestMsgCenter;
import com.izu.business.dto.statistic.VehicleUsageStatisticsDateReq;
import com.izu.business.dto.statistic.VehicleUsageStatisticsDateResp;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @description:
 * @date 2025/6/9 10:42
 */
@RestController
@Slf4j
public class ProviderVehicleUsageStatisticsDateController {

    @PostMapping(MrcarBusinessRestMsgCenter.PROVIDER_VEHICLE_USAGE_STATISTICS_DATE_LIST)
    @RequestFunction(functionName = "轨迹分析-通过车牌号查询时间里程日历")
    @ApiOperation(value = "轨迹分析-通过车牌号查询时间里程日历")
    public RestResponse<List<VehicleUsageStatisticsDateResp>> getDateList(@RequestBody VehicleUsageStatisticsDateReq vehicleUsageStatisticsDateReq) {
        Map<String, Object> params = new HashMap<>();
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, vehicleUsageStatisticsDateReq);
        String restUrl = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.VEHICLE_USAGE_STATISTICS_DATE_LIST);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, restUrl, params, null, VehicleUsageStatisticsDateResp.class);
    }
}
