package com.izu.mrcar.providerController.insure;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.izu.business.config.BusinessRestLocator;
import com.izu.business.consts.resturl.MrcarBusinessRestMsgCenter;
import com.izu.business.dto.insure.*;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.controller.insure.AccidentInsureExcelDTO;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.dto.company.CompanyDTO;
import com.izu.user.dto.company.CompanyQueryDTO;
import com.izu.user.dto.staff.pc.DataPerm;
import com.izu.user.dto.staff.pc.DataPermInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.enums.CompanyAttributeEnums;
import com.izu.user.enums.CompanyBusinessTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@Api(tags = "保险出险")
public class AccidentInsureProviderController {
    @PostMapping(MrcarBusinessRestMsgCenter.ACCIDENT_INSURE_PROVIDER_LIST_INFO)
    @RequestFunction(functionName = "保险出险列表-运营端")
    @ApiOperation(value = "保险出险列表（运营端）",notes = "作者：mapp")
    public RestResponse<PageDTO<AccidentInsureListProviderOutPutDTO>> getList(@RequestBody AccidentInsureListProviderInputDTO param){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        String url = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.ACCIDENT_INSURE_PROVIDER_LIST_INFO);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, AccidentInsureListOutPutDTO.class);
    }

    @PostMapping(MrcarBusinessRestMsgCenter.ACCIDENT_INSURE_PROVIDER_DETAIL)
    @RequestFunction(functionName = "保险出险详情-客户端")
    @ApiOperation(value = "保险出险详情（客户端）",notes = "作者：mapp")
    @ApiImplicitParam(name = "reportNumber", value = "报案号", required = true)
    public RestResponse<AccidentInsureProviderDetailDTO> getDetail(String reportNumber){
        String url = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.ACCIDENT_INSURE_PROVIDER_DETAIL);
        Map<String,Object> param = new HashMap<>();
        param.put("reportNumber",reportNumber);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, url, param, null, AccidentInsureProviderDetailDTO.class);
    }

    @PostMapping(MrcarBusinessRestMsgCenter.ACCIDENT_INSURE_PROVIDER_EXPORT)
    @RequestFunction(functionName = "保险出险导出（运营端）")
    @ApiOperation(value = "保险出险导出（运营端）")
    @ExportExcelWeb(fileName = ExportExcelConstants.FILE_NAME_INSURE_EXPORT_INFO,filePath = ExportExcelConstants.FILE_PATH,
            sheet = ExportExcelConstants.SHEET_NAME_INSURE_EXPORT_INFO, c =AccidentInsureExcelDTO.class)
    public PageDTO<AccidentInsureExcelDTO> exportPartInfo(@RequestBody AccidentInsureListInputDTO param, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        String userName = loginBaseInfo.obtainBaseInfo().getStaffName();
        param.setPage(izuEasyExcelSession.getPageNo());
        izuEasyExcelSession.setUserName(userName);
        param.setPageSize(10000);
        String url = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.ACCIDENT_INSURE_PROVIDER_LIST_INFO);

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, AccidentInsureListProviderOutPutDTO.class);
        if(restResponse!=null && restResponse.isSuccess()){
            PageDTO<AccidentInsureListProviderOutPutDTO> pageDTO = (PageDTO) restResponse.getData();
            List<AccidentInsureExcelDTO> exportList = new ArrayList<>();
            if(pageDTO != null) {
                List<AccidentInsureListProviderOutPutDTO> listDTOS = pageDTO.getResult();
                exportList = BeanUtil.copyList(listDTOS,AccidentInsureExcelDTO.class);
            }
            return new PageDTO<>(param.getPage(),param.getPageSize(),exportList.size(),exportList);
        }
        return null;
    }

    @PostMapping(UserUrlCenter.GET_INSURANCE_LIST)
    @ApiOperation(value = "保险公司列表",notes = "作者：haobinjie")
    @RequestFunction(functionName = "保险公司列表")
    public RestResponse<CompanyDTO> getInsuranceList(@RequestBody CompanyQueryDTO companyQueryDTO){

        ProviderLoginInfo loginUser = LoginSessionUtil.getProviderLoginInfo();
        DataPerm providerDataPerm= loginUser.obtainDataPerm();
        companyQueryDTO.setDataPermType(providerDataPerm.getDataPermType());
        List<DataPermInfo> dataPermInfoList = providerDataPerm.getDataPermInfoList();
        if(CollectionUtil.isNotEmpty(dataPermInfoList)){
            companyQueryDTO.setDataPermIdSet(dataPermInfoList.stream().map(DataPermInfo::getDataPermId).collect(Collectors.toSet()));
            companyQueryDTO.setDataCodeSet(dataPermInfoList.stream().map(DataPermInfo::getDataPermCode).collect(Collectors.toSet()));
        }else{
            companyQueryDTO.setDataPermIdSet(Sets.newHashSet(-1));
            companyQueryDTO.setDataCodeSet(Sets.newHashSet("-1"));
        }

        final String restUrl = new UserRestLocator().getRestUrl("/companyManage/listForPage");
        Map<String, Object> paraMap=new HashMap<>();
        companyQueryDTO.setCompanyAttribute(CompanyAttributeEnums.SUPPLIER.getValue());
        companyQueryDTO.setBusinessTypes(Lists.newArrayList(CompanyBusinessTypeEnum.INSURANCE_COMPANY.getValue()));
        paraMap.put(BaseHttpClient.POSTBODY_MAP_KEY,companyQueryDTO);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paraMap, null, CompanyDTO.class);
    }

    @PostMapping(UserUrlCenter.INSURANCE_SELECT_LIST)
    @ApiOperation("保险公司下拉列表")
    public RestResponse<List<CompanyDTO>> insuranceSelectList(@RequestBody CompanyQueryDTO companyQueryDTO){

        final String restUrl = new UserRestLocator().getRestUrl("/companyManage/listForPageNoDataPerm");
        Map<String, Object> paraMap=new HashMap<>();
        Integer pageNo=companyQueryDTO.getPageNo();
        Integer pageSize= companyQueryDTO.getPageSize();
        if(pageNo == null){
            companyQueryDTO.setPageNo(1);
        }
        if(pageSize == null){
            //为空时，查询前100个
            companyQueryDTO.setPageSize(100);
        }
        companyQueryDTO.setCompanyAttribute(CompanyAttributeEnums.SUPPLIER.getValue());
        companyQueryDTO.setBusinessTypes(Lists.newArrayList(CompanyBusinessTypeEnum.INSURANCE_COMPANY.getValue()));
        paraMap.put(BaseHttpClient.POSTBODY_MAP_KEY,companyQueryDTO);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paraMap, null, CompanyDTO.class);
    }
}
