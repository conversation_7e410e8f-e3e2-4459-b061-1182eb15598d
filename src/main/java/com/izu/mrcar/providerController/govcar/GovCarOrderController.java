package com.izu.mrcar.providerController.govcar;

import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.dto.DicKeyValueDTO;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.consts.govCar.GovCarOrderEnums;
import com.izu.mrcar.order.dto.govcar.order.*;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.izu.mrcar.order.MrcarOrderRestMsgCenter.GOV_CAR_ORDER_CANCEL_PRE_CHECK;

/**
 * <AUTHOR>
 * @date 2024/8/6 23:29
 */
@Slf4j
@Api(tags = "公务员用车-订单")
@RestController("providerGovCarOrderController")
@RequestMapping("/provider/govcar/orderInfo")
public class GovCarOrderController {

    @PostMapping("/queryByPage")
    @ApiOperation(value = "分页查询订单信息")
    public RestResponse<PageDTO<GovCarOrderListInfoDTO>> queryOrderList(@RequestBody GovCarOrderQueryParams params){
        MrcarOrderRestLocator locator = new MrcarOrderRestLocator();
        Map<String, Object> paramMap = new HashMap<>();
        params.setFormProvider(true);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, params);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, locator.getRestUrl(MrcarOrderRestMsgCenter.GOV_CAR_ORDER_PAGE), paramMap, null, GovCarOrderListInfoDTO.class);
    }


    @PostMapping("/export")
    @ApiOperation(value = "导出订单信息")
    @ExportExcelWeb(fileName = ExportExcelConstants.FILE_NAME_GOV_CAR_ORDER_EXPORT_INFO + ExportExcelConstants.XLSX,filePath = ExportExcelConstants.FILE_PATH,
            sheet = ExportExcelConstants.SHEET_NAME_GOV_CAR_ORDER_EXPORT_INFO, c = GovCarOrderListInfoDTO.class)
    public PageDTO<GovCarOrderListInfoDTO> exportOrderList(@RequestBody GovCarOrderQueryParams params, IzuEasyExcelSession izuEasyExcelSession,
                                                           HttpServletRequest request, HttpServletResponse response){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        params.setPageNum(1);
        params.setPageSize(1000);
        izuEasyExcelSession.setUserName(providerLoginInfo.obtainBaseInfo().getStaffName());
        MrcarOrderRestLocator locator = new MrcarOrderRestLocator();
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, params);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, locator.getRestUrl(MrcarOrderRestMsgCenter.GOV_CAR_ORDER_PAGE), paramMap, null, GovCarOrderListInfoDTO.class);
        if(restResponse!=null && restResponse.isSuccess()){
            PageDTO pageDTO = (PageDTO)restResponse.getData();
            if (pageDTO.getTotal() > 10000){
                throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
            }
            return pageDTO;
        }
        else {
            return null;
        }
    }

    @PostMapping("/cancelOrder")
    @ApiOperation(value = "取消订单")
    public RestResponse<Void> cancelOrder(@RequestBody GovCarOrderCancelParams params){
        MrcarOrderRestLocator locator = new MrcarOrderRestLocator();
        Map<String, Object> paramMap = new HashMap<>();
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        AccountBaseInfo baseInfo = providerLoginInfo.getBaseInfo();
        params.setLoginUserId(baseInfo.getStaffId());
        params.setLoginUserName(baseInfo.getStaffName());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, params);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, locator.getRestUrl(MrcarOrderRestMsgCenter.GOV_CAR_ORDER_CANCEL), paramMap, null);
    }

    @PostMapping(GOV_CAR_ORDER_CANCEL_PRE_CHECK)
    @ApiOperation(value = "订单车辆是否驶出围栏")
    public RestResponse<Boolean> cancelPre(@RequestBody GovCarOrderDetailParam params) {
        MrcarOrderRestLocator locator = new MrcarOrderRestLocator();
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, params);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, locator.getRestUrl(MrcarOrderRestMsgCenter.GOV_CAR_ORDER_CANCEL_PRE_CHECK), paramMap, null);
    }

    @PostMapping("/forceReturn")
    @ApiOperation(value = "强制还车")
    public RestResponse<Void> forceReturn(@RequestBody GovCarOrderForceReturnParams params){
        MrcarOrderRestLocator locator = new MrcarOrderRestLocator();
        Map<String, Object> paramMap = new HashMap<>();
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        AccountBaseInfo baseInfo = providerLoginInfo.getBaseInfo();
        params.setLoginUserId(baseInfo.getStaffId());
        params.setLoginUserName(baseInfo.getStaffName());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, params);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, locator.getRestUrl(MrcarOrderRestMsgCenter.GOV_CAR_ORDER_FORCE_RETURN), paramMap, null);
    }

    @PostMapping("/adjustment")
    @ApiOperation(value = "订单调价")
    public RestResponse<Void> adjustment(@RequestBody GovCarOrderAdjustmentParams params){
        MrcarOrderRestLocator locator = new MrcarOrderRestLocator();
        Map<String, Object> paramMap = new HashMap<>();
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        AccountBaseInfo baseInfo = providerLoginInfo.getBaseInfo();
        params.setLoginUserId(baseInfo.getStaffId());
        params.setLoginUserName(baseInfo.getStaffName());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, params);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, locator.getRestUrl(MrcarOrderRestMsgCenter.GOV_CAR_ORDER_ADJUSTMENT), paramMap, null);
    }

    @PostMapping("/detail")
    @ApiOperation(value = "订单详情")
    public RestResponse<GovCarOrderDetailInfoDTO> detail(@RequestBody GovCarOrderDetailParam params){
        MrcarOrderRestLocator locator = new MrcarOrderRestLocator();
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, params);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, locator.getRestUrl(MrcarOrderRestMsgCenter.GOV_CAR_ORDER_DETAIL), paramMap, null, GovCarOrderDetailInfoDTO.class);
    }


    @GetMapping("/statusEnum")
    @ApiOperation(value = "订单状态")
    public RestResponse<List<DicKeyValueDTO<Byte, String>>> statusEnum(){
        List<DicKeyValueDTO<Byte, String>> result = new ArrayList<>();
        for (GovCarOrderEnums.OrderStatus statusEnum : GovCarOrderEnums.OrderStatus.values()){
            result.add(new DicKeyValueDTO<>(statusEnum.getStatus(), statusEnum.getDescription()));
        }
        return RestResponse.success(result);
    }

    @PostMapping("/adjustmentConfig")
    @ApiOperation(value = "订单调价项")
    public RestResponse<GovCarOrderAdjustmentItem> adjustmentConfig(@RequestBody GovCarOrderAdjustmentParams params){
        MrcarOrderRestLocator locator = new MrcarOrderRestLocator();
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, params);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, locator.getRestUrl(MrcarOrderRestMsgCenter.GOV_CAR_ORDER_ADJUSTMENT_CONFIG), paramMap, null, GovCarOrderAdjustmentItem.class);
    }
}
