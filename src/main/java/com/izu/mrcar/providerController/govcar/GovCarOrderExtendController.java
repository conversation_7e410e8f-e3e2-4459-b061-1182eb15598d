package com.izu.mrcar.providerController.govcar;

import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.dto.DicKeyValueDTO;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.consts.govCar.GovCarOrderBillEnums;
import com.izu.mrcar.order.dto.govcar.order.*;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/8/6 23:40
 */
@Slf4j
@Api(tags = "公务员用车-附加费订单")
@RestController("providerGovCarOrderExtendController")
@RequestMapping("/provider/govcar/orderExtend")
public class GovCarOrderExtendController {


    @PostMapping("/queryByPage")
    @ApiOperation(value = "分页查询附加费订单信息")
    public RestResponse<PageDTO<GovCarOrderExtendListInfoDTO>> queryOrderList(@RequestBody GovCarOrderExtendQueryParams params){
        MrcarOrderRestLocator locator = new MrcarOrderRestLocator();
        Map<String, Object> paramMap = new HashMap<>();
        params.setFormProvider(true);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, params);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, locator.getRestUrl(MrcarOrderRestMsgCenter.GOV_CAR_ORDER_EXTEND_PAGE), paramMap, null, GovCarOrderExtendListInfoDTO.class);
    }

    @PostMapping("/cancelOrder")
    @ApiOperation(value = "取消附加费订单")
    public RestResponse<Void> cancelOrder(@RequestBody GovCarOrderExtendCancelParams params){
        MrcarOrderRestLocator locator = new MrcarOrderRestLocator();
        Map<String, Object> paramMap = new HashMap<>();
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        AccountBaseInfo baseInfo = providerLoginInfo.getBaseInfo();
        params.setLoginUserId(baseInfo.getStaffId());
        params.setLoginUserName(baseInfo.getStaffName());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, params);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, locator.getRestUrl(MrcarOrderRestMsgCenter.GOV_CAR_ORDER_EXTEND_CANCEL), paramMap, null);
    }

    @PostMapping("/saveOrder")
    @ApiOperation(value = "保存附加费订单")
    public RestResponse<Void> cancelOrder(@RequestBody GovCarOrderExtendSaveParams params){
        MrcarOrderRestLocator locator = new MrcarOrderRestLocator();
        Map<String, Object> paramMap = new HashMap<>();
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        AccountBaseInfo baseInfo = providerLoginInfo.getBaseInfo();
        params.setLoginUserId(baseInfo.getStaffId());
        params.setLoginUserName(baseInfo.getStaffName());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, params);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, locator.getRestUrl(MrcarOrderRestMsgCenter.GOV_CAR_ORDER_EXTEND_SAVE), paramMap, null);
    }

    @PostMapping("/detail")
    @ApiOperation(value = "附加费订单详情")
    public RestResponse<GovCarOrderExtendDetailInfoDTO> cancelOrder(@RequestBody GovCarOrderExtendDetailParam params){
        MrcarOrderRestLocator locator = new MrcarOrderRestLocator();
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, params);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, locator.getRestUrl(MrcarOrderRestMsgCenter.GOV_CAR_ORDER_EXTEND_DETAIL), paramMap, null, GovCarOrderExtendDetailInfoDTO.class);
    }


    @GetMapping("/statusEnum")
    @ApiOperation(value = "附加费订单状态")
    public RestResponse<List<DicKeyValueDTO<Byte, String>>> statusEnum(){
        List<DicKeyValueDTO<Byte, String>> result = new ArrayList<>();
        for (GovCarOrderBillEnums.BillStatus statusEnum : GovCarOrderBillEnums.BillStatus.values()){
            if (statusEnum.isShowToUser()){
                result.add(new DicKeyValueDTO<>(statusEnum.getStatus(), statusEnum.getDescription()));
            }
        }
        return RestResponse.success(result);
    }

    @GetMapping("/surchargeType")
    @ApiOperation(value = "附加费类型")
    public RestResponse<List<DicKeyValueDTO<String, String>>> surchargeType(){
        List<DicKeyValueDTO<String, String>> result = new ArrayList<>();
        for (GovCarOrderBillEnums.SurchargeType statusEnum : GovCarOrderBillEnums.SurchargeType.values()){
            result.add(new DicKeyValueDTO<>(statusEnum.getType(), statusEnum.getDescription()));
        }
        return RestResponse.success(result);
    }


}
