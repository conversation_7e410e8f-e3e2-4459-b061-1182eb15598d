package com.izu.mrcar.providerController.govcar;

import com.izu.config.dto.govCar.req.GovBaseConfigQueryReqDTO;
import com.izu.config.dto.govCar.req.GovBaseConfigReqDTO;
import com.izu.config.MrCarConfigRestLocator;
import com.izu.consts.ConfigURI;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * Author:  wangM
 * Date:  2024/8/7 9:21
 */
@RestController
@Slf4j
@Api(tags = "公务车配置信息")
public class GovCarConfigController {
    private final MrCarConfigRestLocator mrCarConfigRestLocator = new MrCarConfigRestLocator();

    @ApiOperation(value = "根据查询条件，查询公务车配置信息",notes = "作者：wangM")
    @PostMapping(ConfigURI.GOV_CAR_CONFIG)
    public RestResponse selectGovCarConfig(@RequestBody GovBaseConfigQueryReqDTO dto){
        String restUrl = mrCarConfigRestLocator.getRestUrl(ConfigURI.GOV_CAR_CONFIG);
        Map<String, Object> paraMap = new HashMap<>(1);
        paraMap.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paraMap, null);
    }
    @ApiOperation(value = "增加公务车配置信息",notes = "作者：wangM")
    @PostMapping(ConfigURI.GOV_CAR_ADD_CONFIG)
    public RestResponse addGovCarConfig(@RequestBody GovBaseConfigReqDTO dto){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        dto.setCreateId(providerLoginInfo.obtainBaseInfo().getStaffId());
        dto.setUpdateId(providerLoginInfo.obtainBaseInfo().getStaffId());
        dto.setCreateName(providerLoginInfo.obtainBaseInfo().getStaffName());
        dto.setUpdateName(providerLoginInfo.obtainBaseInfo().getStaffName());
        String restUrl = mrCarConfigRestLocator.getRestUrl(ConfigURI.GOV_CAR_ADD_CONFIG);
        Map<String, Object> paraMap = new HashMap<>(1);
        paraMap.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paraMap, null);
    }
    @ApiOperation(value = "修改公务车配置信息",notes = "作者：wangM")
    @PostMapping(ConfigURI.GOV_CAR_EDIT_CONFIG)
    public RestResponse editGovCarConfig(@RequestBody GovBaseConfigReqDTO dto){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        dto.setUpdateId(providerLoginInfo.obtainBaseInfo().getStaffId());
        dto.setUpdateName(providerLoginInfo.obtainBaseInfo().getStaffName());
        String restUrl = mrCarConfigRestLocator.getRestUrl(ConfigURI.GOV_CAR_EDIT_CONFIG);
        Map<String, Object> paraMap = new HashMap<>(1);
        paraMap.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paraMap, null);
    }
    @ApiOperation(value = "启动或暂停公务车配置信息",notes = "作者：wangM")
    @PostMapping(ConfigURI.GOV_CAR_OPEN_CONFIG)
    public RestResponse editGovCarConfig(Integer id, Integer status){
        String restUrl = mrCarConfigRestLocator.getRestUrl(ConfigURI.GOV_CAR_OPEN_CONFIG);
        HashMap<String, Object> hashMap =  new HashMap<>();
        hashMap.put("id",id);
        hashMap.put("status",status);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, hashMap, null);
    }
    @ApiOperation(value = "根据公司ID查询保险配置信息",notes = "作者：wangM")
    @PostMapping(ConfigURI.GOV_CAR_CONFIG_INSURANCE_BY_COMPANY)
    public RestResponse selectInsuranceByCompany(Integer companyId){
        String restUrl = mrCarConfigRestLocator.getRestUrl(ConfigURI.GOV_CAR_CONFIG_INSURANCE_BY_COMPANY);
        HashMap<String, Object> hashMap =  new HashMap<>();
        log.info("根据公司ID查询基础服务配置信息" + companyId);
        hashMap.put("companyId",companyId);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, hashMap, null);
    }
    @ApiOperation(value = "根据公司ID查询基础服务配置信息",notes = "作者：wangM")
    @PostMapping(ConfigURI.GOV_CAR_CONFIG_BASE_SERVICE_BY_COMPANY)
    public RestResponse selectBaseServiceByCompany(Integer companyId){
        String restUrl = mrCarConfigRestLocator.getRestUrl(ConfigURI.GOV_CAR_CONFIG_BASE_SERVICE_BY_COMPANY);
        HashMap<String, Object> hashMap =  new HashMap<>();
        log.info("根据公司ID查询基础服务配置信息" + companyId);
        hashMap.put("companyId",companyId);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, hashMap, null);
    }
    /**
     * 获取时间配置详情
     */
    @ApiOperation(value = "根据id获取时间配置详情",notes = "作者：wangM")
    @PostMapping(ConfigURI.GOV_CAR_TIME_CONFIG_DETAIL)
    public RestResponse selectTimeConfigDetail(Integer id)  {
        String restUrl = mrCarConfigRestLocator.getRestUrl(ConfigURI.GOV_CAR_TIME_CONFIG_DETAIL);
        HashMap<String, Object> hashMap =  new HashMap<>();
        hashMap.put("id",id);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, hashMap, null);
    }
    @ApiOperation(value = "根据公司ID与部门查询相同时间配置的部门",notes = "作者：wangM")
    @PostMapping(ConfigURI.GOV_CAR_CONFIG_SAME_DEPARTMENT)
    public RestResponse selectSameDepartment(Integer companyId,String companyCode,Integer structId,String structCode){
        String restUrl = mrCarConfigRestLocator.getRestUrl(ConfigURI.GOV_CAR_CONFIG_SAME_DEPARTMENT);
        HashMap<String, Object> hashMap =  new HashMap<>();
        hashMap.put("companyId",companyId);
        hashMap.put("companyCode",companyCode);
        hashMap.put("structId",structId);
        hashMap.put("structCode",structCode);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, hashMap, null);
    }
}
