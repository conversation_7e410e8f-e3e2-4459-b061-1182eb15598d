package com.izu.mrcar.providerController.govcar;

import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.dto.govcar.GovCarGainBackRecordDetailDTO;
import com.izu.mrcar.order.dto.govcar.GovCarGainBackRecordPageDTO;
import com.izu.mrcar.order.dto.govcar.GovCarGainBackRecordPageParamsDTO;
import com.izu.user.enums.LoginSystemEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;


/**
 * 公务用车-验车单
 * <p>
 * 2024/8/6 下午10:40
 *
 * <AUTHOR>
 */

@Slf4j
@Api(tags = "公务员用车-取还记录")
@RestController()
@RequestMapping("/provider/govcar/gainBack")
public class ProviderGovCarGainBackRecordController {

    @PostMapping("/queryByPage")
    @ApiOperation(value = "分页查询验车单")
    public RestResponse<PageDTO<GovCarGainBackRecordPageDTO>> queryByPage(@RequestBody GovCarGainBackRecordPageParamsDTO params) {
        params.setLoginSystemType(LoginSystemEnum.PROVIDER.getSys());
        String restUrl = new MrcarOrderRestLocator().getRestUrl("/govCar/gainBack/queryByPage");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, params);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, GovCarGainBackRecordPageDTO.class);
    }

    @PostMapping("/queryDetail")
    @ApiOperation(value = "查询验车单详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "gainBackNo", value = "验车单号", required = true, paramType = "form")
    })
    public RestResponse<GovCarGainBackRecordDetailDTO> detail(
            @Verify(rule = "required", param = "gainBackNo") String gainBackNo
    ) {
        String restUrl = new MrcarOrderRestLocator().getRestUrl("/govCar/gainBack/queryGainBackRecord");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("gainBackNo", gainBackNo);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paramMap, null, GovCarGainBackRecordDetailDTO.class);
    }

}
