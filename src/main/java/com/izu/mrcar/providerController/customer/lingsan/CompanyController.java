package com.izu.mrcar.providerController.customer.lingsan;

import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.lingsan.ProviderLingsanDataPermUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.dto.company.lingsan.CompanySimpleRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * @description: 零散用车
 * @author: hxc
 * @create: 2023-02-12 16:40
 **/
@RestController("lingsanProviderCompanyController")
@Api(tags = "零散用车")
public class CompanyController {

    private final UserRestLocator userRestLocator = new UserRestLocator();

    @GetMapping(UserUrlCenter.PROVIDER_LING_SAN_GET_COMPANY_SIMPLE_LIST)
    @RequestFunction(functionName = "零散用车-客户精简列表")
    @ApiOperation(value = "企业客户下拉选",notes = "作者：贺新春")
    public RestResponse<CompanySimpleRespDTO> getSupplierSimpleList(){

        Set<String> datePermDeptCode = ProviderLingsanDataPermUtil.getDatePermDeptCode();
        String restUrl =  userRestLocator.getRestUrl(UserUrlCenter.PROVIDER_LING_SAN_GET_COMPANY_SIMPLE_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("deptIds",String.join(",",datePermDeptCode));
        return RestClient.requestForList(BaseHttpClient.HttpMethod.GET, restUrl, paramMap, null, CompanySimpleRespDTO.class);
    }

}
