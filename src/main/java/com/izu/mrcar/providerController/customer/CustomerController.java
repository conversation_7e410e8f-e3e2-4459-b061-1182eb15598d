package com.izu.mrcar.providerController.customer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import com.izu.config.MrCarConfigRestLocator;
import com.izu.config.dto.EnterpriseConfigDTO;
import com.izu.config.dto.SupplierCityConfigSaveDTO;
import com.izu.config.errcode.MrCarConfigErrorCode;
import com.izu.consts.ConfigURI;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.excel.Column;
import com.izu.framework.web.util.excel.ExSheet;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.controller.AbstractExcelDownloadController;
import com.izu.mrcar.dto.CompanyInfoConfigDTO;
import com.izu.mrcar.service.CustomerService;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.maintain.ProviderMaintainDataPermUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.config.errcode.UserErrorCode;
import com.izu.user.dto.company.CompanyDTO;
import com.izu.user.dto.company.CompanyEditDTO;
import com.izu.user.dto.company.CompanyQueryDTO;
import com.izu.user.dto.staff.pc.DataPerm;
import com.izu.user.dto.staff.pc.DataPermInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.entity.Company;
import com.izu.user.enums.CompanyAttributeEnum;
import com.izu.user.enums.CompanyAttributeEnums;
import com.izu.user.enums.CompanyBusinessTypeEnum;
import com.izu.user.enums.NormalStatus;
import com.izu.user.restApi.CompanyApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 客户管理
 * @date 2023/2/18 17:29
 */
@RestController
@RequestMapping("/customer")
@Api(tags = "运营端-客户管理")
@Slf4j
public class CustomerController extends AbstractExcelDownloadController {

    @Value("${mrcar-user-core.host.url}")
    private String USER_HOST_URL;

    private static final String LIST_COMPANY_URL = "companyManage/listForPage";

    private final MrCarConfigRestLocator configRestLocator = new MrCarConfigRestLocator();

    @Autowired
    private CustomerService customerService;

    @Autowired
    private ProviderMaintainDataPermUtil providerMaintainDataPermUtil;


    @RequestMapping(value = "/listForPage", method = RequestMethod.POST)
    @RequestFunction(functionName = "客户列表")
    @ApiOperation("客户列表")
    public RestResponse<CompanyDTO> listForPage(@RequestBody CompanyQueryDTO companyQueryDTO) {
        ProviderLoginInfo loginUser = LoginSessionUtil.getProviderLoginInfo();
        DataPerm providerDataPerm= loginUser.obtainDataPerm();
        companyQueryDTO.setDataPermType(providerDataPerm.getDataPermType());
        List<DataPermInfo> dataPermInfoList = providerDataPerm.getDataPermInfoList();
        if(CollectionUtil.isNotEmpty(dataPermInfoList)){
            companyQueryDTO.setDataPermIdSet(dataPermInfoList.stream().map(DataPermInfo::getDataPermId).collect(Collectors.toSet()));
            companyQueryDTO.setDataCodeSet(dataPermInfoList.stream().map(DataPermInfo::getDataPermCode).collect(Collectors.toSet()));
        }else{
            companyQueryDTO.setDataPermIdSet(Sets.newHashSet(-1));
            companyQueryDTO.setDataCodeSet(Sets.newHashSet("-1"));
        }

        Map<String, Object> paraMap=new HashMap<>();
        companyQueryDTO.setCompanyAttribute(CompanyAttributeEnums.RENTAL_CUSTOMER.getValue());
        paraMap.put(BaseHttpClient.POSTBODY_MAP_KEY,companyQueryDTO);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, USER_HOST_URL + "/companyManage/listForPage", paraMap, null, Map.class);
    }

    @RequestMapping(value = "/listForPageNoDataPerm", method = RequestMethod.POST)
    @RequestFunction(functionName = "客户列表")
    @ApiOperation("客户列表")
    public RestResponse<CompanyDTO> listForPageNoDataPerm(@RequestBody CompanyQueryDTO companyQueryDTO) {
        Map<String, Object> paraMap=new HashMap<>();
        companyQueryDTO.setCompanyAttribute(CompanyAttributeEnums.RENTAL_CUSTOMER.getValue());
        paraMap.put(BaseHttpClient.POSTBODY_MAP_KEY,companyQueryDTO);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, USER_HOST_URL + "/companyManage/listForPageNoDataPerm", paraMap, null, Map.class);
    }

    @RequestMapping("/editPack")
    @RequestFunction(functionName = "客户管理|切换产品包")
    @ApiOperation("切换产品包")
    public RestResponse editCustomerPack(@RequestBody CompanyEditDTO companyEditDTO) {
        ProviderLoginInfo loginUser = LoginSessionUtil.getProviderLoginInfo();
        companyEditDTO.setLoginUserId(loginUser.getBaseInfo().getStaffId());
        companyEditDTO.setLoginUserName(loginUser.getBaseInfo().getStaffName());
        String restUrl = USER_HOST_URL+"/company/checkoutPackage";
        Map<String, Object> param = new HashMap<>();
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, companyEditDTO);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null, Map.class);

    }

    @RequestMapping("/editExpireDate")
    @RequestFunction(functionName = "客户管理|修改到期日")
    @ApiOperation("修改到期日")
    public RestResponse editCustomerExpireDate(@RequestBody CompanyEditDTO companyEditDTO) {
        ProviderLoginInfo loginUser = LoginSessionUtil.getProviderLoginInfo();
        companyEditDTO.setLoginUserId(loginUser.getBaseInfo().getStaffId());
        companyEditDTO.setLoginUserName(loginUser.getBaseInfo().getStaffName());
        String restUrl = new UserRestLocator().getRestUrl("/company/editExpireDate");
        Map<String, Object> param = new HashMap<>();
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, companyEditDTO);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null, Map.class);

    }

    @PostMapping(value = "/edit")
    @RequestFunction(functionName = "编辑企业信息")
    @ApiOperation(value = "编辑企业信息")
    public RestResponse editCompany(CompanyEditDTO companyEditDTO) {
        String restUrl = new UserRestLocator().getRestUrl("/companyManage/edit");

        ProviderLoginInfo loginUser = LoginSessionUtil.getProviderLoginInfo();
        if (loginUser == null) {
            return RestResponse.fail(RestErrorCode.HTTP_UNAUTHORIZED);
        }
        companyEditDTO.setLoginUserId(loginUser.getBaseInfo().getStaffId());
        companyEditDTO.setLoginUserName(loginUser.getBaseInfo().getStaffName());
        final Map<String, Object> paraMap = new HashMap<>();
        paraMap.put(BaseHttpClient.POSTBODY_MAP_KEY, companyEditDTO);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paraMap, null, Boolean.class);
    }


    @PostMapping(ConfigURI.CONFIG_ENTERPRISE_SAVE)
    @ApiOperation("保存企业配置")
    public RestResponse saveEnterpriseConfig(@RequestBody EnterpriseConfigDTO dto) {
        try {
            ProviderLoginInfo loginUser = LoginSessionUtil.getProviderLoginInfo();
            String restUrl = configRestLocator.getRestUrl(ConfigURI.CONFIG_ENTERPRISE_SAVE);
            dto.setLoginName(loginUser.obtainBaseInfo().getStaffName());
            dto.setLoginId(loginUser.obtainBaseInfo().getStaffId());
            Map<String, Object> paraMap = new HashMap<>(1);
            paraMap.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paraMap, null, EnterpriseConfigDTO.class);
        } catch (Exception e) {
            log.error("保存企业配置异常...", e);
            return RestResponse.fail(RestErrorCode.SYSTEM_EXCEPTION);
        }
    }

    @PostMapping("/config/enterprise/get")
    @ApiOperation("获取企业配置")
    public RestResponse<EnterpriseConfigDTO> getEnterpriseConfig(@RequestBody EnterpriseConfigDTO dto) {
        try {
            Map<String, Object> paraMap = new HashMap<>(1);
            paraMap.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
            String restUrl = configRestLocator.getRestUrl(ConfigURI.CONFIG_ENTERPRISE_GET);
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paraMap, null, EnterpriseConfigDTO.class);
        } catch (Exception e) {
            log.error("获取企业配置异常...", e);
            return RestResponse.fail(RestErrorCode.SYSTEM_EXCEPTION);
        }
    }

    @RequestMapping(value = "/getCompanyById",method = {RequestMethod.GET,RequestMethod.POST})
    @RequestFunction(functionName = "企业信息")
    @ApiOperation(value = "查询企业信息")
    public RestResponse<CompanyInfoConfigDTO> getCarCompanyById(Integer companyId) {
        ProviderLoginInfo loginBaseInfo = LoginSessionUtil.getProviderLoginInfo();
        if (loginBaseInfo == null) {
            return RestResponse.fail(RestErrorCode.HTTP_UNAUTHORIZED);
        }
        CompanyInfoConfigDTO companyInfoConfigDTO= customerService.getCustomerDetail(companyId);
        return RestResponse.success(companyInfoConfigDTO);

    }

    @RequestMapping(value="/export",method=RequestMethod.POST)
    @RequestFunction(functionName = "导出客户列表")
    public void exportCompanyInfo(@RequestBody Map<String,Object> paraMap,HttpServletRequest request, HttpServletResponse response) {
        paraMap.put("pageNo", 1);
        paraMap.put("pageSize", 100);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POST, USER_HOST_URL + LIST_COMPANY_URL, paraMap, null, Company.class);
        if (restResponse.isSuccess()) {
            PageDTO pageInfo = (PageDTO) restResponse.getData();
            List<Company> result = new ArrayList<>( (int)pageInfo.getTotal());
            result.addAll(pageInfo.getResult());
            int page = pageInfo.getPage();
            while (page <= pageInfo.getPages()){
                paraMap.put("pageNo", ++page);
                restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POST, USER_HOST_URL + LIST_COMPANY_URL, paraMap, null, Company.class);
                if (restResponse.isSuccess()){
                    pageInfo = (PageDTO) restResponse.getData();
                    result.addAll(pageInfo.getResult());
                }
            }
            List<Column> columnModes = new ArrayList<>();
            columnModes.add(new Column("companyName", "客户名称", (short) 12000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            columnModes.add(new Column("companyAlias", "客户简称", (short) 3000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            columnModes.add(new Column("linkerName", "客户管理员", (short) 2000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            columnModes.add(new Column("linkerMobile", "管理员手机号", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            columnModes.add(new Column("linkerEmail", "管理员邮箱", (short) 6000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            columnModes.add(new Column("saleName", "客户销售", (short) 2000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            columnModes.add(new Column("companyAttribute", "客户类型", (short) 2000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            columnModes.add(new Column("cityName", "所属城市", (short) 2000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            columnModes.add(new Column("companyStatus", "客户状态", (short) 2000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            columnModes.add(new Column("createName", "客户创建人", (short) 2000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            columnModes.add(new Column("createTime", "创建时间", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            columnModes.add(new Column("companyAccount", "客户对公账户", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            columnModes.add(new Column("accountName", "账户开户行", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            columnModes.add(new Column("creditCode", "社会统一信息代码", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            ExSheet exSheet;
            List<Map<String, String>> collect = result.stream().map( company -> {
                HashMap<String, String> map = new HashMap<>();
                map.put("companyName", company.getCompanyName());
                map.put("companyAlias", company.getCompanyAlias());
                map.put("linkerName", company.getLinkerName());
                map.put("linkerMobile", company.getLinkerMobile());
                map.put("linkerEmail", company.getLinkerEmail());
                map.put("saleName", company.getSaleName() == null ? "" : company.getSaleName());
                CompanyAttributeEnum companyAttributeEnum = CompanyAttributeEnum.getByValue(company.getCompanyAttribute());
                map.put("companyAttribute", companyAttributeEnum == null ? "" : companyAttributeEnum.getName());
                map.put("cityName", company.getCityName());
                map.put("companyStatus", NormalStatus.STATUS_DELETE.getValue() == company.getCompanyStatus().intValue() ? "停用" : "启用");
                map.put("createName", company.getCreateName());
                map.put("createTime", company.getCreateTime());
                map.put("companyAccount", company.getCompanyAccount() == null ? "" : company.getCompanyAccount());
                map.put("accountName", company.getAccountName() == null ? "" : company.getAccountName());
                map.put("creditCode", company.getCreditCode() == null ? "" : company.getCreditCode());
                return map;
            }).collect(Collectors.toList());
            exSheet = new ExSheet(columnModes, collect, "客户信息");
            String dateTime = DateUtil.formatDateTime(new Date());
            exportExcel(request, response, "客户列表_" + dateTime + ".xlsx", "customer_list_" + dateTime + ".xlsx", Collections.singletonList(exSheet));
        }else {
            //导出失败
            try(PrintWriter outputStream = response.getWriter()){
                response.setStatus(HttpStatus.SC_OK);
                response.setContentType("application/json");
                RestResponse failed = RestResponse.create(RestErrorCode.UNKNOWN_ERROR, "导出客户列表失败", false, null);
                outputStream.write(JSON.toJSONString(failed, true));
            }catch (Exception e){
                log.error("导出客户列表失败");
            }
            log.error("导出客户列表失败");
        }
    }

    @PostMapping("/userCarConfig")
    @ApiOperation("保存企业-用车配置")
    public RestResponse saveCarConfig(@RequestBody EnterpriseConfigDTO dto) {
        try {
            ProviderLoginInfo loginUser = LoginSessionUtil.getProviderLoginInfo();
            String restUrl = configRestLocator.getRestUrl("/config/enterprise/saveCarConfig");
            dto.setLoginName(loginUser.obtainBaseInfo().getStaffName());
            dto.setLoginId(loginUser.obtainBaseInfo().getStaffId());
            Map<String, Object> paraMap = new HashMap<>(1);
            paraMap.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paraMap, null, EnterpriseConfigDTO.class);
        } catch (Exception e) {
            log.error("保存企业配置异常...", e);
            return RestResponse.fail(RestErrorCode.SYSTEM_EXCEPTION);
        }
    }


    @PostMapping("/basicFunction")
    @ApiOperation("保存企业-基础配置")
    public RestResponse saveBasicFunction(@RequestBody EnterpriseConfigDTO dto) {
        try {
            ProviderLoginInfo loginUser = LoginSessionUtil.getProviderLoginInfo();
            String restUrl = configRestLocator.getRestUrl("/config/enterprise/saveBasicFunction");
            dto.setLoginName(loginUser.obtainBaseInfo().getStaffName());
            dto.setLoginId(loginUser.obtainBaseInfo().getStaffId());
            Map<String, Object> paraMap = new HashMap<>(1);
            paraMap.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paraMap, null, EnterpriseConfigDTO.class);
        } catch (Exception e) {
            log.error("保存企业配置异常...", e);
            return RestResponse.fail(RestErrorCode.SYSTEM_EXCEPTION);
        }
    }

    @PostMapping("/busFunction")
    @ApiOperation("保存企业-大巴车配置")
    public RestResponse saveBusFunction(@RequestBody EnterpriseConfigDTO dto) {
        try {
            ProviderLoginInfo loginUser = LoginSessionUtil.getProviderLoginInfo();
            String restUrl = configRestLocator.getRestUrl("/config/enterprise/saveBusFunction");
            dto.setLoginName(loginUser.obtainBaseInfo().getStaffName());
            dto.setLoginId(loginUser.obtainBaseInfo().getStaffId());
            Map<String, Object> paraMap = new HashMap<>(1);
            paraMap.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paraMap, null, EnterpriseConfigDTO.class);
        } catch (Exception e) {
            log.error("保存企业配置异常...", e);
            return RestResponse.fail(RestErrorCode.SYSTEM_EXCEPTION);
        }
    }

    @PostMapping("/supplier/saveCity")
    @ApiOperation("供应商-保存城市")
    public RestResponse<Boolean> saveSupplierCity(@RequestBody SupplierCityConfigSaveDTO dto) {
        Integer companyId = dto.getCompanyId();
        CompanyDTO companyById = CompanyApi.getCompanyById(companyId);
        //商务车供应商时 城市可以为空,零散供应商时 不能为空
        if (companyById == null) {
            return RestResponse.fail(UserErrorCode.COMPANY_NOT_FIND);
        }
        List<String> businessTypes = Arrays.stream(companyById.getBusinessType().split(",")).collect(Collectors.toList());
        if(businessTypes.contains(CompanyBusinessTypeEnum.LING_SAN_SUPPLIER.getValue().toString())){
            if(CollUtil.isEmpty(dto.getCity())){
                return RestResponse.fail(MrCarConfigErrorCode.COMMON_PARAM_MSG, "城市不能为空");
            }
        }
        ProviderLoginInfo loginUser = LoginSessionUtil.getProviderLoginInfo();
        String restUrl = configRestLocator.getRestUrl("/config/supplier/saveCity");
        dto.setLoginName(loginUser.obtainBaseInfo().getStaffName());
        dto.setLoginId(loginUser.obtainBaseInfo().getStaffId());
        Map<String, Object> paraMap = new HashMap<>(1);
        paraMap.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paraMap, null);
    }

    @RequestMapping(value = "/garageListForPage", method = RequestMethod.POST)
    @RequestFunction(functionName = "维修厂列表")
    @ApiOperation("客户列表")
    public RestResponse<CompanyDTO> listForPageGarage(@RequestBody CompanyQueryDTO companyQueryDTO) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        companyQueryDTO.setDataPermType(providerLoginInfo.obtainDataPerm().getDataPermType());
        companyQueryDTO.setDataCodeSet(providerMaintainDataPermUtil.getDatePermScope(null));

        Map<String, Object> paraMap=new HashMap<>();
        companyQueryDTO.setCompanyAttribute(CompanyAttributeEnums.GARAGE.getValue());
        paraMap.put(BaseHttpClient.POSTBODY_MAP_KEY,companyQueryDTO);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, USER_HOST_URL + "/companyManage/garageListForPage", paraMap, null, Map.class);
    }

}
