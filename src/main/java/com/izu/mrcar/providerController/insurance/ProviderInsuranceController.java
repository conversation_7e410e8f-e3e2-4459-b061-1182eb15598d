package com.izu.mrcar.providerController.insurance;

import cn.hutool.core.collection.CollUtil;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.consts.insurance.InsuranceAttachmentTypeEnum;
import com.izu.asset.dto.insurance.InsuranceAttachmentDTO;
import com.izu.asset.dto.insurance.InsurancePolicyDTO;
import com.izu.asset.dto.insurance.InsurancePolicyExportDTO;
import com.izu.asset.dto.insurance.req.InsurancePolicyDetailReqDTO;
import com.izu.asset.dto.insurance.req.InsurancePolicyDownloadReqDTO;
import com.izu.asset.dto.insurance.req.InsurancePolicyListDTO;
import com.izu.carasset.enums.insurance.InsuranceTypeEnum;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.doc.JrdApiParamDoc;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.service.insurance.InsuranceService;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.PdfZipUtil;
import com.izu.user.dto.staff.pc.ClientCompany;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.enums.perm.ProviderDataPermTypeEnum;
import com.izu.user.util.ObjectTransferUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/31 14:03
 */
@RestController
@RequestMapping("/provider/insurance")
@Api(tags = "mrcar保险相关接口")
public class ProviderInsuranceController {
    private static final MrCarAssetRestLocator mrCarAssetRestLocator = new MrCarAssetRestLocator();
    @Resource
    private PdfZipUtil pdfZipUtil;

    @Resource
    private InsuranceService insuranceService;

    @PostMapping("/listInsurancePolice")
    @RequestFunction(functionName = "保单列表查询")
    @ApiOperation(value = "保单列表查询")
    public RestResponse<PageDTO<InsurancePolicyDTO>> listInsurancePolice(@RequestBody InsurancePolicyListDTO reqDto) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        //数据权限如果不是所有得将直接返回空
        if(!ProviderDataPermTypeEnum.ALL.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())){
            return RestResponse.success(new PageDTO(reqDto.getPage(), reqDto.getPageSize(), 0, Collections.emptyList()));
        }
        String restUrl = mrCarAssetRestLocator.getRestUrl("/insurance/listInsurancePolice");
        Map<String, Object> paraMap = new HashMap<>(1);
        paraMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDto);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paraMap, null, InsurancePolicyDTO.class);

    }

    @PostMapping("/exportInsurancePolice")
    @RequestFunction(functionName = "保单列表导出")
    @ApiOperation(value = "保单列表导出")
    public RestResponse exportInsurancePolice(@RequestBody InsurancePolicyListDTO reqDto, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        reqDto.setDataPermType(providerLoginInfo.getProviderDataPerm().getDataPermType());
        reqDto.setPage(1);
        reqDto.setPageSize(2000);
        izuEasyExcelSession.setPageNo(1);
        izuEasyExcelSession.setUserName(providerLoginInfo.getBaseInfo().getStaffName());
        insuranceService.providerExportInsurancePolice(reqDto, izuEasyExcelSession, response);
        return null;

    }

    @PostMapping("/insurancePolicyDetail")
    @RequestFunction(functionName = "保单详情")
    @ApiOperation(value = "保单详情")
    public RestResponse<InsurancePolicyDTO> insurancePolicyDetail(@RequestBody InsurancePolicyDetailReqDTO reqDto) {
        String restUrl = mrCarAssetRestLocator.getRestUrl("/insurance/insurancePolicyDetail");
        Map<String, Object> paraMap = new HashMap<>(1);
        paraMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDto);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paraMap, null, InsurancePolicyDTO.class);
    }


    @PostMapping("/batchDownloadPolicyPdf")
    @RequestFunction(functionName = "批量下载车险保单PDF")
    @ApiOperation(value = "批量下载车险保单PDF")
    public void batchDownloadPolicyPdf(@RequestBody InsurancePolicyListDTO reqDto, HttpServletResponse resp, HttpServletRequest request) throws IOException {
        handleZip(reqDto,resp,request, InsuranceAttachmentTypeEnum.INSURANCE_POLICY.getCode());

    }

    @RequestMapping("/downloadPolicyPdf")
    @RequestFunction(functionName = "保单文件下载")
    public RestResponse download(HttpServletResponse response, HttpServletRequest request,
                                 @Verify(rule = "required", param = "policyNum") @JrdApiParamDoc(desc = "保单号") String policyNum,
                                 @Verify(param = "fileType", rule = "required") @JrdApiParamDoc(desc = "文件类型，2：批单工单， 9：保单") Byte fileType
    ) throws IOException  {

        InsurancePolicyListDTO reqDto = new InsurancePolicyListDTO();
        reqDto.setPolicyNum(policyNum);
        handleZip(reqDto,response,request,fileType);
        return null;
    }

    private void handleZip(InsurancePolicyListDTO reqDto, HttpServletResponse resp,HttpServletRequest request,Byte fileType) throws IOException{
        String restUrl = mrCarAssetRestLocator.getRestUrl("/insurance/listInsurancePolice");
        Map<String, Object> paraMap = new HashMap<>(1);
        reqDto.setPageSize(100);
        reqDto.setPageNum(1);
        paraMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDto);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paraMap, null, InsurancePolicyDTO.class);
        if(restResponse!=null && restResponse.isSuccess() && restResponse.getData()!=null){
            List<InsurancePolicyDTO> policyDTOList = ObjectTransferUtil.transferRestResponseToList(restResponse);
            if(CollUtil.isNotEmpty(policyDTOList)){
                resp.setContentType("application/zip");
                List<InsurancePolicyDTO> policyDTOS = policyDTOList.stream().filter(x -> fileType==9?
                        Objects.equals(x.getEPolicyFile(), Byte.valueOf("1")):
                        Objects.equals(x.getEChangeFile(), Byte.valueOf("1"))
                ).collect(Collectors.toList());
                if(CollUtil.isEmpty(policyDTOS)){
                    return;
                }
                //查询保单附件
                String url = mrCarAssetRestLocator.getRestUrl("/insurance/queryInsuranceFileList");
                Map<String, Object> map = new HashMap<>();
                map.put("policyNumList", policyDTOS.stream().map(InsurancePolicyDTO::getPolicyNum).collect(Collectors.joining(",")));
                map.put("attachmentType", fileType);
                RestResponse restResponse1 = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, url, map, null, InsuranceAttachmentDTO.class);
                if(restResponse1==null || !restResponse1.isSuccess() || restResponse1.getData()==null ){
                    return;
                }
                List<InsuranceAttachmentDTO> list = (List<InsuranceAttachmentDTO>) restResponse1.getData();
                if(fileType==2){
                    list.forEach(
                            x->{
                                x.setInsuranceNo(reqDto.getPolicyNum());
                            }
                    );
                }
                Map<String, List<InsuranceAttachmentDTO>> listMap = list.stream().collect(Collectors.groupingBy(InsuranceAttachmentDTO::getInsuranceNo));
                String zipName = "";
                if (InsuranceTypeEnum.MAND_INSURANCE.getCode().equals(reqDto.getInsuranceType())) {
                    zipName = zipName+"交强险保单"+policyDTOS.size()+"个.zip";
                } else if (InsuranceTypeEnum.BUSINESS_INSURANCE.getCode().equals(reqDto.getInsuranceType())) {
                    zipName = zipName+"商业险保单"+policyDTOS.size()+"个.zip";
                }
                // 设置中文文件名，并使用 UTF-8 编码进行处理
                String encodedFileName = URLEncoder.encode(zipName, StandardCharsets.UTF_8.toString()).replace("+", "%20");

                // 设置 Content-Disposition 头部，指示附件下载，并设置文件名
                resp.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"; filename*=UTF-8''" + encodedFileName);
                pdfZipUtil.downloadAndZipPdfs(policyDTOS,listMap, resp.getOutputStream(),request);
            }
        }
    }
    
}
