package com.izu.mrcar.providerController.bi;

import cn.hutool.core.collection.CollUtil;
import com.izu.business.config.BusinessRestLocator;
import com.izu.business.consts.resturl.MrcarBusinessRestMsgCenter;
import com.izu.business.dto.bi.ReportTableConfigReqDTO;
import com.izu.business.dto.bi.VehicleBiAnalyseReqDTO;
import com.izu.business.dto.bi.resp.TableChildColumnRespDTO;
import com.izu.business.dto.bi.resp.TableColumnRespDTO;
import com.izu.business.dto.bi.resp.VehicleBiAnalyseRespDTO;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.doc.JrdApiDoc;
import com.izu.framework.doc.JrdDoc;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.excel.Column;
import com.izu.framework.web.util.excel.ExSheet;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.controller.AbstractExcelDownloadController;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.dto.CompanyDepartmentDTO;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.enums.perm.ClientDataPermTypeEnum;
import com.izu.user.enums.perm.ProviderDataPermTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@RestController
@Slf4j
@Api(tags = "客户端")
@JrdDoc(name = "BI统计分析-车辆分析")
public class BIVehicleAnalysisController extends AbstractExcelDownloadController {

    @PostMapping(MrcarBusinessRestMsgCenter.MRCAR_PROVIDER_VEHICLE_STATISTICAL_ANALYSIS_LIST)
    @RequestFunction(functionName = "车辆分析列表")
    @JrdApiDoc(simpleDesc = "车辆分析列表", author = "hhd", resDataClass = VehicleBiAnalyseRespDTO.class)
    @ApiOperation(value = "车辆分析列表",notes = "作者：hhd")
    public RestResponse<PageDTO<VehicleBiAnalyseRespDTO>> getVehicleList(@RequestBody VehicleBiAnalyseReqDTO param){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        param.setLoginUserId(providerLoginInfo.getBaseInfo().getStaffId());
        param.setLoginUserName(providerLoginInfo.getBaseInfo().getStaffName());
        param.setLoginCompanyId(providerLoginInfo.obtainBelongCompanyId());
        param.setDataPermSet(getDataScopeList(providerLoginInfo));
        if(!Objects.equals(ProviderDataPermTypeEnum.ALL.getType(),providerLoginInfo.obtainDataPerm().getDataPermType())){
            return RestResponse.success(new PageDTO<>());
        }
        String url = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.MRCAR_VEHICLE_STATISTICAL_ANALYSIS_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, VehicleBiAnalyseRespDTO.class);
    }

    @PostMapping(value = MrcarBusinessRestMsgCenter.MRCAR_PROVIDER_VEHICLE_STATISTICAL_ANALYSIS_EXPORT)
    @JrdApiDoc(simpleDesc = "车辆分析列表导出", author = "hhd", resDataClass = Object.class)
    @RequestFunction(functionName = "车辆分析列表导出")
    public RestResponse exportVehicleList(@RequestBody VehicleBiAnalyseReqDTO param, IzuEasyExcelSession izuEasyExcelSession,
                                                                            HttpServletRequest request, HttpServletResponse response) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        param.setLoginUserId(providerLoginInfo.getBaseInfo().getStaffId());
        param.setLoginUserName(providerLoginInfo.getBaseInfo().getStaffName());
        param.setLoginCompanyId(providerLoginInfo.obtainBelongCompanyId());
        param.setDataPermSet(getDataScopeList(providerLoginInfo));
        if(!Objects.equals(ProviderDataPermTypeEnum.ALL.getType(),providerLoginInfo.obtainDataPerm().getDataPermType())){
            return RestResponse.success(null);
        }
        //查询是否配置权限
        String url = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.MRCAR_VEHICLE_STATISTICAL_ANALYSIS_EXPORT);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        RestResponse<List<VehicleBiAnalyseRespDTO>> res = RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, VehicleBiAnalyseRespDTO.class);
        if (!res.isSuccess()) {
           return null;
        }
        List<VehicleBiAnalyseRespDTO> resultData = res.getData();
        //查询配置字段
        String configUrl = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.MRCAR_REPORT_TABLE_CONFIG_LIST);
        Map<String, Object> map = new HashMap<>();
        ReportTableConfigReqDTO configReqDTO = new ReportTableConfigReqDTO();
        configReqDTO.setLoginCompanyId(param.getLoginCompanyId());
        configReqDTO.setReportScene(1);
        configReqDTO.setLoginUserId(param.getLoginUserId());
        //默认成多企业
        List<Integer> list = new ArrayList<>();
        list.add(param.getLoginCompanyId());
        list.add(0);
        configReqDTO.setCompanyIds(list);
        map.put(BaseHttpClient.POSTBODY_MAP_KEY, configReqDTO);
        RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, configUrl, map, null, TableColumnRespDTO.class);
        if(restResponse==null || !restResponse.isSuccess() || restResponse.getData() ==null){
            return null;
        }
        List<TableColumnRespDTO> respDTOList = (List<TableColumnRespDTO>) restResponse.getData();
        listExport(resultData, request, response,respDTOList,param.getCompanyId());
        return null;
    }

    private void listExport(List<VehicleBiAnalyseRespDTO> datas, HttpServletRequest request, HttpServletResponse response,List<TableColumnRespDTO> respDTOList,Integer companyId) {

        List<Column> columnModes = new ArrayList<>();
        respDTOList.forEach(respDTO -> {
            if (Objects.equals(respDTO.getProp(), "vehicleLicense")) {
                columnModes.add(new Column(respDTO.getProp(), respDTO.getLabel(), (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            } else {
                List<TableChildColumnRespDTO> children = respDTO.getChildren();
                if(CollUtil.isEmpty(children)){
                    return;
                }
                children.forEach(x-> {
                    if(Objects.equals(x.getProp(),"drivingTime") || Objects.equals(x.getProp(),"vehicleUseRateStr")  || Objects.equals(x.getProp(),"usageDurationRateStr") || Objects.equals(x.getProp(),"companyName")
                            || Objects.equals(x.getProp(),"cityName") || Objects.equals(x.getProp(),"driverName") || Objects.equals(x.getProp(),"useCarName") || Objects.equals(x.getProp(),"vehicleTypeStr") || Objects.equals(x.getProp(),"brandStr") ||
                            Objects.equals(x.getProp(),"vehicleSeriesStr")|| Objects.equals(x.getProp(), "deptName") || Objects.equals(x.getProp(), "deptPath")){
                        columnModes.add(new Column(x.getProp(), x.getLabel(), (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
                    } else {
                        columnModes.add(new Column(x.getProp(), x.getLabel(), (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0.00", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
                    }
                });
            }
        });
        final List<Map<String, String>> rowDatas = new ArrayList<Map<String, String>>(datas.size() + 1);
        List<String> keyList = new ArrayList<>();
        respDTOList.stream().map(TableColumnRespDTO::getProp).collect(Collectors.toList());
        respDTOList.forEach(respDTO -> {
            if (Objects.equals(respDTO.getProp(), "vehicleLicense")) {
                keyList.add(respDTO.getProp());
            } else {
                List<TableChildColumnRespDTO> children = respDTO.getChildren();
                if(CollUtil.isEmpty(children)){
                    return;
                }
                children.forEach(x-> keyList.add(x.getProp()));
            }
        });
        List<CompanyDepartmentDTO> departmentDTOList = queryDepartmentList(companyId, null);
        Map<Integer, CompanyDepartmentDTO> deptMap = departmentDTOList.stream().collect(Collectors.toMap(CompanyDepartmentDTO::getId, Function.identity(),(k1, k2)->k2));
        for (VehicleBiAnalyseRespDTO data : datas) {
            final Map<String, String> rowdata = new HashMap<String, String>(columnModes.size());
            if(keyList.contains("vehicleLicense")){
                rowdata.put("vehicleLicense", data.getVehicleLicense());
            }
            if(keyList.contains("deptName")){
                rowdata.put("deptName", data.getDeptName());
            }
            if(keyList.contains("drivingMileage")){
                rowdata.put("drivingMileage", data.getDrivingMileage()!=null?data.getDrivingMileage().toPlainString():"");
            }
            if(keyList.contains("everyDayDrivingMileage")){
                rowdata.put("everyDayDrivingMileage", data.getEveryDayDrivingMileage()!=null?data.getEveryDayDrivingMileage().toPlainString():"");
            }
            if(keyList.contains("drivingTime")){
                rowdata.put("drivingTime", data.getDrivingTimeStr());
            }
            if(keyList.contains("vehicleUseRateStr")){
                rowdata.put("vehicleUseRateStr", data.getVehicleUseRateStr());
            }
            if(keyList.contains("usageDurationRateStr")){
                rowdata.put("usageDurationRateStr", data.getUsageDurationRateStr());
            }
            if(keyList.contains("tripCount")){
                rowdata.put("tripCount", data.getTripCount()!=null?data.getTripCount().toPlainString():"");
            }
            if(keyList.contains("singleTrip")){
                rowdata.put("singleTrip", data.getSingleTrip()!=null?data.getSingleTrip().toPlainString():"");
            }
            if(keyList.contains("totalFee")){
                rowdata.put("totalFee", data.getTotalFee()!=null?data.getTotalFee().toPlainString():"");
            }
            if(keyList.contains("hundredKMFee")){
                rowdata.put("hundredKMFee", data.getHundredKMFee()!=null?data.getHundredKMFee().toPlainString():"");
            }
            if(keyList.contains("oilFee")){
                rowdata.put("oilFee", data.getOilFee()!=null?data.getOilFee().toPlainString():"");
            }
            if(keyList.contains("oilQuantity")){
                rowdata.put("oilQuantity", data.getOilQuantity()!=null?data.getOilQuantity().toPlainString():"");
            }
            if(keyList.contains("chargeFee")){
                rowdata.put("chargeFee", data.getChargeFee()!=null?data.getChargeFee().toPlainString():"");
            }
            if(keyList.contains("chargeQuantity")){
                rowdata.put("chargeQuantity", data.getChargeQuantity()!=null?data.getChargeQuantity().toPlainString():"");
            }
            if(keyList.contains("maintainFee")){
                rowdata.put("maintainFee", data.getMaintainFee()!=null?data.getMaintainFee().toPlainString():"");
            }
            if(keyList.contains("insuranceFee")){
                rowdata.put("insuranceFee", data.getInsuranceFee()!=null?data.getInsuranceFee().toPlainString():"");
            }
            if(keyList.contains("tollFee")){
                rowdata.put("tollFee", data.getTollFee()!=null?data.getTollFee().toPlainString():"");
            }
            if(keyList.contains("carParkFee")){
                rowdata.put("carParkFee", data.getCarParkFee()!=null?data.getCarParkFee().toPlainString():"");
            }
            if(keyList.contains("carWashFee")){
                rowdata.put("carWashFee", data.getCarWashFee()!=null?data.getCarWashFee().toPlainString():"");
            }
            if(keyList.contains("annualInspectionFee")){
                rowdata.put("annualInspectionFee", data.getAnnualInspectionFee()!=null?data.getAnnualInspectionFee().toPlainString():"");
            }
            if(keyList.contains("accidentFee")){
                rowdata.put("accidentFee", data.getAccidentFee()!=null?data.getAccidentFee().toPlainString():"");
            }
            if(keyList.contains("violationFee")){
                rowdata.put("violationFee", data.getViolationFee()!=null?data.getViolationFee().toPlainString():"");
            }
            if(keyList.contains("otherFee")){
                rowdata.put("otherFee", data.getOtherFee()!= null?data.getOtherFee().toPlainString():"");
            }
            if(keyList.contains("companyName")){
                rowdata.put("companyName", data.getCompanyName());
            }
            if(keyList.contains("cityName")){
                rowdata.put("cityName", data.getCityName());
            }
            if(keyList.contains("driverName")){
                rowdata.put("driverName", data.getDriverName());
            }
            if(keyList.contains("useCarName")){
                rowdata.put("useCarName", data.getUseCarName());
            }
            if(keyList.contains("vehicleTypeStr")){
                rowdata.put("vehicleTypeStr", data.getVehicleTypeStr());
            }
            if(keyList.contains("brandStr")){
                rowdata.put("brandStr", data.getBrandStr());
            }
            if(keyList.contains("vehicleSeriesStr")){
                rowdata.put("vehicleSeriesStr", data.getVehicleSeriesStr());
            }
            if(keyList.contains("deptPath")){
                Map<String, String> map = getDepartmentNamesByParentId(data.getDeptId(), deptMap);
                rowdata.put("deptPath", map.get("deptPath"));
            }
            rowDatas.add(rowdata);
        }
        List<ExSheet> exSheetList = new ArrayList<>(1);
        exSheetList.add(new ExSheet(columnModes, rowDatas, "车辆分析导出"));
        //---------------------------B: 根据上面创建好的Sheet，生成Excel
        String excelType = "xlsx";
        String fileNameCn = "车辆分析导出_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + "." + excelType;
        String fileNameEn = "bi_vehicle_analysis_export_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + "." + excelType;
        this.exportExcel(request, response, fileNameCn, fileNameEn, exSheetList);
    }

    /**
     * 将运营端的会话信息中数据权限转为具体的业务参数集合
     * @param providerLoginInfo
     * @return
     */
    private Set<String> getDataScopeList(ProviderLoginInfo providerLoginInfo){
        Set<String> res = null;
        if(ProviderDataPermTypeEnum.ALL.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())){
            return null;
        }else if(ProviderDataPermTypeEnum.RESPONSIBLE_CONTRACT.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())){
            res = providerLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet();
        }else if(ProviderDataPermTypeEnum.RESPONSIBLE_CUSTOMER.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())||
                ProviderDataPermTypeEnum.ASSIGN_CUSTOMER.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())){
            res = providerLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet();
        }else if(ProviderDataPermTypeEnum.SELF_DEPT.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())||
                ProviderDataPermTypeEnum.ASSIGN_DEPT.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())){
            res = providerLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet();
        }
        return res;
    }
    public static  List<CompanyDepartmentDTO> queryDepartmentList(Integer companyId, Integer status){
        UserRestLocator locator = new UserRestLocator();
        Map<String, Object> params = new HashMap<>();
        params.put("status", status);
        params.put("companyId", companyId);
        String restUrl = locator.getRestUrl("/department/queryDepartmentList");
        RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, params, null, CompanyDepartmentDTO.class);
        if(restResponse==null || !restResponse.isSuccess() || restResponse.getData()==null){
            return Collections.emptyList();
        }
        return   (List<CompanyDepartmentDTO>)restResponse.getData();
    }

    // 根据第五层部门的父级ID查询一到四层的部门名称
    public static Map<String,String> getDepartmentNamesByParentId(Integer parentId,Map<Integer, CompanyDepartmentDTO> deptMap) {
        List<BIDeptAnalysisController.DeptNameDTO> deptNameDTOS = new ArrayList<>();
        String deptPath = "";
        Map<String,String> map = new HashMap<>();
        CompanyDepartmentDTO current = deptMap.get(parentId);
        if (current == null) {
            log.info("未找到对应的部门: {}", parentId);
            return Collections.emptyMap();
        }
        map.put("superDept",current.getDepartmentName());
        // 逐级向上查找，直到第一层
        while (current != null && current.getLevel() > 0) {
            BIDeptAnalysisController.DeptNameDTO deptNameDTO = new BIDeptAnalysisController.DeptNameDTO();
            deptNameDTO.setId(current.getLevel());
            deptNameDTO.setName(current.getDepartmentName());
            deptNameDTOS.add(deptNameDTO);
            if (current.getLevel() == 1) {
                break; // 已经到达第一层
            }
            current = deptMap.get(current.getParentId());
        }
        deptNameDTOS.sort(Comparator.comparing(BIDeptAnalysisController.DeptNameDTO::getId));
        for(BIDeptAnalysisController.DeptNameDTO deptNameDTO : deptNameDTOS){
            deptPath = StringUtils.isBlank(deptPath)
                    ? deptNameDTO.getName()
                    : deptPath + "->" + deptNameDTO.getName();
        }
        map.put("deptPath",deptPath);
        return map;
    }

    @Data
    public static class DeptNameDTO{
        private int id;
        private String name;
    }
}
