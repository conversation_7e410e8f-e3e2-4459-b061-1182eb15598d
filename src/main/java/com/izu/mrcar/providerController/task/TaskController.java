package com.izu.mrcar.providerController.task;

import com.izu.business.config.BusinessRestLocator;
import com.izu.business.consts.resturl.MrcarBusinessRestMsgCenter;
import com.izu.business.dto.TaskAddDTO;
import com.izu.business.dto.TaskQueryDTO;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.lbs.common.dto.task.TaskDTO;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.lingsan.ProviderDaiBuCheDataPermUtil;
import com.izu.user.dto.staff.pc.DataPerm;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;

@Slf4j
@RestController
@Api(tags = "任务管理")
public class TaskController {
    @PostMapping(MrcarBusinessRestMsgCenter.MRCAR_TASK_LIST)
    public RestResponse getList(@RequestBody TaskQueryDTO reqDTO){
        HashMap<String, Object> restParam = new HashMap<>(2,1);
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        String restUrl = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.MRCAR_TASK_LIST);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null, Object.class);
    }
    @PostMapping(MrcarBusinessRestMsgCenter.MRCAR_TASK_ADD)
    public RestResponse add(@RequestBody TaskAddDTO reqDTO){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        reqDTO.setLoginUserId(providerLoginInfo.getBaseInfo().getStaffId());
        reqDTO.setLoginUserName(providerLoginInfo.getBaseInfo().getStaffName());

        HashMap<String, Object> restParam = new HashMap<>(2,1);
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        String restUrl = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.MRCAR_TASK_ADD);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null, Object.class);
    }
}
