package com.izu.mrcar.providerController.permission;

import com.google.common.collect.Maps;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.user.config.UserRestLocator;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.dto.ManagerPermissionDTO;
import com.izu.user.dto.provider.permission.AddPermissionReqDTO;
import com.izu.user.dto.provider.permission.ModifyPermissionReqDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 权限管理-API
 * <AUTHOR>
 * @date : 2023/2/11
 */
@Api(tags = "账号权限")
@RestController
public class PermissionController {

    private final static UserRestLocator USER_REST_LOCATOR = new UserRestLocator();


    @RequestFunction(functionName = "获取全量权限列表")
    @ApiOperation("获取全量权限列表-多颗树")
    @ApiImplicitParam(name = "menuOpenMode", value = "权限所属系统;1-客户端；2-APP；3-运营端", required = true,paramType = "form")
    @PostMapping(UserUrlCenter.PROVIDER_MANAGER_PERMISSION_TREE)
    public RestResponse<ManagerPermissionDTO> getPermTree(@Verify(param = "menuOpenMode", rule = "required") Byte menuOpenMode) {
        final HashMap<String, Object> restParam = Maps.newHashMapWithExpectedSize(1);
        restParam.put("menuOpenMode", menuOpenMode);
        final String restUrl = USER_REST_LOCATOR.getRestUrl(UserUrlCenter.PROVIDER_MANAGER_PERMISSION_TREE);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, restParam, null);
    }

    @RequestFunction(functionName = "新增一个权限")
    @ApiOperation("新增一个权限")
    @PostMapping(UserUrlCenter.PROVIDER_MANAGER_PERMISSION_ADD)
    public RestResponse addPerm(@RequestBody @Validated AddPermissionReqDTO reqDTO) {
        final HashMap<String, Object> restParam = Maps.newHashMapWithExpectedSize(1);
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        final String restUrl = USER_REST_LOCATOR.getRestUrl(UserUrlCenter.PROVIDER_MANAGER_PERMISSION_ADD);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }

    @RequestFunction(functionName = "编辑一个权限")
    @ApiOperation("编辑一个权限")
    @PostMapping(UserUrlCenter.PROVIDER_MANAGER_PERMISSION_MODIFY)
    public RestResponse modifyPerm(@RequestBody @Validated ModifyPermissionReqDTO reqDTO) {
        final HashMap<String, Object> restParam = Maps.newHashMapWithExpectedSize(1);
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        final String restUrl = USER_REST_LOCATOR.getRestUrl(UserUrlCenter.PROVIDER_MANAGER_PERMISSION_MODIFY);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }


    @RequestFunction(functionName = "启用一个权限")
    @ApiOperation("启用一个权限")
    @ApiImplicitParam(name = "permissionId", value = "权限ID", required = true,paramType = "form")
    @PostMapping(UserUrlCenter.PROVIDER_MANAGER_PERMISSION_ENABLE)
    public RestResponse enable(@Verify(param = "permissionId", rule = "required") Integer permissionId) {
        final HashMap<String, Object> restParam = Maps.newHashMapWithExpectedSize(1);
        restParam.put("permissionId", permissionId);
        final String restUrl = USER_REST_LOCATOR.getRestUrl(UserUrlCenter.PROVIDER_MANAGER_PERMISSION_ENABLE);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, restParam, null);
    }

    @RequestFunction(functionName = "禁用一个权限")
    @ApiOperation("禁用一个权限")
    @ApiImplicitParam(name = "permissionId", value = "权限ID", required = true,paramType = "form")
    @PostMapping(UserUrlCenter.PROVIDER_MANAGER_PERMISSION_DISABLE)
    public RestResponse disable(@Verify(param = "permissionId", rule = "required") Integer permissionId) {
        final HashMap<String, Object> restParam = Maps.newHashMapWithExpectedSize(1);
        restParam.put("permissionId", permissionId);
        final String restUrl = USER_REST_LOCATOR.getRestUrl(UserUrlCenter.PROVIDER_MANAGER_PERMISSION_DISABLE);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, restParam, null);
    }


    @RequestFunction(functionName = "app权限清单-可见工作台下拉框（app的一级菜单）")
    @ApiOperation("app权限清单-可见工作台下拉框（app的一级菜单）")
    @ApiImplicitParam(name = "permName", value = "app功能权限名称", required = false, paramType = "form")
    @RequestMapping(value = UserUrlCenter.PROVIDER_MANAGER_PERMISSION_VISIBLE_WORKBENCH,method = {RequestMethod.POST,RequestMethod.GET})
    public RestResponse<ManagerPermissionDTO> visibleWorkbenchDropdown(String permName){
        Map<String, Object> param = new HashMap<>();
        param.put("permName", permName);
        String restUrl = USER_REST_LOCATOR.getRestUrl(UserUrlCenter.PROVIDER_MANAGER_PERMISSION_VISIBLE_WORKBENCH);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, param, null);

    }



}
