package com.izu.mrcar.providerController.permission;


import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.controller.base.UserBaseController;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.dto.ManagerPermissionDTO;
import com.izu.user.dto.provider.permission.AppFunctionPageListReqDTO;
import com.izu.user.dto.provider.permission.AppFunctionRespDTO;
import com.izu.user.dto.provider.permission.AppFunctionSaveReqDTO;
import com.izu.user.dto.provider.permission.AppFunctionUpdateReqDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * app端权限清单管理api
 */
@Api(tags = "账号权限")
@RestController
public class AppFunctionController extends UserBaseController {


    @RequestFunction(functionName = "app权限清单-保存")
    @ApiOperation("app权限清单-保存")
    @PostMapping(UserUrlCenter.APP_FUNCTION_SAVE)
    public RestResponse<Boolean> save(@RequestBody AppFunctionSaveReqDTO reqDTO) {
        return postBodyWithLogin(UserUrlCenter.APP_FUNCTION_SAVE, reqDTO);
    }


    @RequestFunction(functionName = "app权限清单-修改")
    @ApiOperation("app权限清单-修改")
    @PostMapping(UserUrlCenter.APP_FUNCTION_UPDATE)
    public RestResponse<Boolean> update(@RequestBody AppFunctionUpdateReqDTO reqDTO) {
        return postBodyWithLogin(UserUrlCenter.APP_FUNCTION_UPDATE, reqDTO);
    }

    @RequestFunction(functionName = "app权限清单-分页列表")
    @ApiOperation("app权限清单-分页列表")
    @PostMapping(UserUrlCenter.APP_FUNCTION_PAGE_LIST)
    public RestResponse<PageDTO<AppFunctionRespDTO>> pageList(@RequestBody AppFunctionPageListReqDTO reqDTO) {
        return postBodyWithLogin(UserUrlCenter.APP_FUNCTION_PAGE_LIST, reqDTO);
    }


    @RequestFunction(functionName = "app权限清单-详情")
    @ApiOperation("app权限清单-详情")
    @ApiImplicitParam(name = "functionId", value = "功能ID", required = true, paramType = "form")
    @RequestMapping(value = UserUrlCenter.APP_FUNCTION_DETAIL, method = {RequestMethod.GET, RequestMethod.POST})
    public RestResponse<AppFunctionRespDTO> detail(@Verify(param = "functionId", rule = "required") Integer functionId) {
        Map<String, Object> param = new HashMap<>();
        param.put("functionId", functionId);
        return post(UserUrlCenter.APP_FUNCTION_DETAIL, param);
    }


    @RequestFunction(functionName = "app权限清单-获取菜单下可见的功能下拉框")
    @ApiOperation("app权限清单-获取菜单下可见的功能下拉框")
    @ApiImplicitParams(
            {@ApiImplicitParam(name = "visibleCode", value = "app功能二级(或三级)菜单编码", required = true, paramType = "form"),
                    @ApiImplicitParam(name = "functionName", value = "功能名称，模糊搜索", required = false, paramType = "form")
            })
    @RequestMapping(value = UserUrlCenter.APP_FUNCTION_DROP_DOWN,method = {RequestMethod.POST,RequestMethod.GET})
    public RestResponse<List<AppFunctionRespDTO>> dropdown(
            @Verify(param = "visibleCode", rule = "required") String visibleCode,String functionName
    ) {
        Map<String, Object> param = new HashMap<>();
        param.put("visibleCode", visibleCode);
        param.put("functionName", functionName);
        return post(UserUrlCenter.APP_FUNCTION_DROP_DOWN, param);
    }


}
