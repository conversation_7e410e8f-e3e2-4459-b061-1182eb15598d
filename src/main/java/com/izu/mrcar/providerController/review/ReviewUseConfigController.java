package com.izu.mrcar.providerController.review;

import com.google.common.collect.Maps;
import com.izu.config.dto.*;
import com.izu.consts.ConfigURI;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.config.RestUrlConfig;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import java.util.*;

/**
 * @ClassName ReviewOptionController
 * <AUTHOR>
 * @Date 2024/6/14 14:05
 * @Version 1.0
 */
@Slf4j
@RestController
@Api(tags = "评价引用配置")
public class ReviewUseConfigController {

    @PostMapping(ConfigURI.REVIEW_USE_CONFIG_LIST)
    @ApiOperation(value = "评价引用配置列表")
    @RequestFunction(functionName = "评价引用配置列表")
    public RestResponse<PageDTO<ReviewUseConfigRespDTO>> getList(@RequestBody ReviewUseConfigQueryReqDTO param) {
        String restUrl = RestUrlConfig.getConfig().getConfigCoreUrl() + ConfigURI.REVIEW_USE_CONFIG_LIST;
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, ReviewUseConfigRespDTO.class);
    }

    @PostMapping(ConfigURI.REVIEW_USE_CONFIG_DETAIL)
    @ApiOperation("评价引用配置详情")
    @RequestFunction(functionName = "评价引用配置详情")
    public RestResponse<ReviewUseConfigDetailRespDTO> detail(Integer id) {
        String restUrl = RestUrlConfig.getConfig().getConfigCoreUrl() + ConfigURI.REVIEW_USE_CONFIG_DETAIL;
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, id);
        paramMap.put("id", id);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null);
    }

    @PostMapping(ConfigURI.REVIEW_USE_CONFIG_ADD_OR_UPDATE)
    @ApiOperation("添加或更新评价引用配置")
    @RequestFunction(functionName = "添加或更新评价引用配置")
    public RestResponse<Void> addOrUpdate(@RequestBody ReviewUseConfigDTO reviewUseConfigDTO) {
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        String restUrl = RestUrlConfig.getConfig().getConfigCoreUrl() + ConfigURI.REVIEW_USE_CONFIG_ADD_OR_UPDATE;
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        if (Objects.isNull(reviewUseConfigDTO.getId())) {
            reviewUseConfigDTO.setCreator(loginBaseInfo.obtainBaseInfo().getStaffName());
            reviewUseConfigDTO.setCreateTime(new Date());
        }
        reviewUseConfigDTO.setUpdator(loginBaseInfo.obtainBaseInfo().getStaffName());
        reviewUseConfigDTO.setUpdateTime(new Date());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reviewUseConfigDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null);
    }

    @PostMapping(ConfigURI.REVIEW_USE_CONFIG_DELETE)
    @ApiOperation("删除评价引用配置")
    @RequestFunction(functionName = "删除评价引用配置")
    public RestResponse<Void> delete(Integer id) {
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        String restUrl = RestUrlConfig.getConfig().getConfigCoreUrl() + ConfigURI.REVIEW_USE_CONFIG_DELETE;
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("id", id);
        paramMap.put("updator", loginBaseInfo.obtainBaseInfo().getStaffName());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, paramMap);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null);
    }
}