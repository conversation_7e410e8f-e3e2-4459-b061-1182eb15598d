package com.izu.mrcar.providerController.review;

import com.google.common.collect.Maps;
import com.izu.business.config.BusinessRestLocator;
import com.izu.business.consts.resturl.MrcarBusinessRestMsgCenter;
import com.izu.business.dto.czcontract.output.BussContractLiteDTO;
import com.izu.business.dto.review.ReviewDTO;
import com.izu.business.dto.review.ReviewExportDTO;
import com.izu.business.dto.review.ReviewRespDTO;
import com.izu.excel.annotation.ExportExcel;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.cache.RedisJsonCache;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.service.review.ReviewExportService;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.dto.provider.staff.ProviderStaffBaseDetailRespDTO;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import static com.izu.user.enums.perm.ProviderDataPermTypeEnum.*;
import static com.izu.user.enums.perm.ProviderDataPermTypeEnum.ASSIGN_DEPT;

/**
 * @ClassName ReviewController
 * <AUTHOR>
 * @Date 2024/6/21 10:03
 * @Version 1.0
 */

@Slf4j
@RestController
@Api(tags = "评价")
public class ReviewController {

    @Autowired
    private RedisJsonCache redisJsonCache;

    @Value("${mrcar-business-core.host.url}")
    private String BUSINESS_HOST_URL;

    @Autowired
    private ReviewExportService exportService;

    @PostMapping(MrcarBusinessRestMsgCenter.REVIEW_LIST)
    @RequestFunction(functionName = "评价列表")
    @ApiOperation(value = "评价列表")
    public RestResponse<PageDTO<ReviewRespDTO>> getList(@RequestBody ReviewDTO reqDTO) {
        String restUrl = BUSINESS_HOST_URL + MrcarBusinessRestMsgCenter.REVIEW_LIST;
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        // 权限类型
        reqDTO.setDataPermType(LoginSessionUtil.getBaseLoginInfo().obtainDataPerm().getDataPermType());
        // 候选用户集合
        Set<String> users = dataPermToUsers();
        reqDTO.setDataPermCodeSet(users);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, ReviewRespDTO.class);
    }

    /**
     * @param reqDTO
     * @param izuEasyExcelSession
     * @param request
     * @param response
     * @return
     * @Description: 评价列表列表导出
     */
    @RequestMapping(value = MrcarBusinessRestMsgCenter.REVIEW_EXPORT)
    @RequestFunction(functionName = "服务流水列表导出-运营端")
    @ApiOperation(value = "服务流水列表导出-运营端", notes = "作者：任伟光")
    @ExportExcelWeb(fileName = "评价列表.xlsx", filePath = "/data/logs/excel/tmp", sheet = "评价列表",
            c = ReviewExportDTO.class, isAsync = true)
    public PageDTO exportBusinessOrderInfoList(@RequestBody ReviewDTO reqDTO,
                                               IzuEasyExcelSession izuEasyExcelSession,
                                               HttpServletRequest request, HttpServletResponse response) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        // 权限类型
        reqDTO.setDataPermType(LoginSessionUtil.getBaseLoginInfo().obtainDataPerm().getDataPermType());
        // 候选用户集合
        Set<String> users = dataPermToUsers();
        reqDTO.setDataPermCodeSet(users);
        izuEasyExcelSession.setUserName(providerLoginInfo.getBaseInfo().getStaffName());
        return exportService.getDataByPage(reqDTO, izuEasyExcelSession.getPageNo(), 10000);
    }


    @RequestMapping(value = MrcarBusinessRestMsgCenter.REVIEW_EXPORT_EMAIL_ASYN)
    @RequestFunction(functionName = "服务流水列表导出到邮件-运营端")
    @ApiOperation(value = "服务流水列表导出到邮件-运营端", notes = "作者：任伟光")
    @ExportExcel(fileName = "评价列表.xlsx", filePath = "/data/logs/mrcar-business-core/tmp", sheet = "评价列表")
    public RestResponse exportEmailAsyn(@RequestBody ReviewDTO reqDTO) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        if (StringUtils.isEmpty(providerLoginInfo.getBaseInfo().getEmail())) {
            return RestResponse.create(9999, "邮箱号不能为空", false, null);
        }
        // 权限类型
        reqDTO.setDataPermType(LoginSessionUtil.getBaseLoginInfo().obtainDataPerm().getDataPermType());
        // 候选用户集合
        Set<String> users = dataPermToUsers();
        reqDTO.setDataPermCodeSet(users);
        reqDTO.setEmail(providerLoginInfo.getBaseInfo().getEmail());
        // 防止上次导出未完成的情况下，再次导出
        String lockKey = "mrcar:provider:review:exportReviewList:asyn:" + reqDTO.getEmail();
        final String cacheValue = redisJsonCache.get(lockKey, String.class);
        if (StringUtils.isNotBlank(cacheValue)) {
            log.warn("已提交申请，请稍等");
            return RestResponse.create(RestErrorCode.SYSTEM_EXCEPTION, "您已经提交了导出请求，请稍后查收电子邮件。（大约耗时3分钟）", false, null);
        }
        String staffName = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo().getStaffName();
        redisJsonCache.set(lockKey, "Y", 3 * 60);
        CompletableFuture.runAsync(() ->
                exportService.exportToEmail(reqDTO,staffName))
                .whenComplete((unused, throwable) -> {
                    redisJsonCache.delete(lockKey);
                    if (throwable != null) {
                        log.error("服务流水列表导出至邮箱异常", throwable);
                    }
                });
        return RestResponse.create(0, "提交成功！请稍后查收电子邮件。（大约耗时3分钟！）", true, null);
    }

    // 将权限转为用户候选集合
    private Set<String> dataPermToUsers() {
        // 用户编码
        HashSet<String> users = new HashSet<>();
        // 权限
        LoginBaseInfo.SimpleDataPerm dataPerm =
                LoginSessionUtil.getBaseLoginInfo()
                        .obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE);
        // 本人
        if (Objects.equals(dataPerm.getDataPermType(), ONE_SELF.getType())) {
            // 当前登陆人
            users.add(LoginSessionUtil.getBaseLoginInfo()
                    .obtainBaseInfo().getStaffCode());
        } else if (Objects.equals(dataPerm.getDataPermType(), RESPONSIBLE_CONTRACT.getType())) {
            // 负责合同
            Set<String> contractCodes = dataPerm.getDataCodeSet();
            // 根据合同编码查询所有合同信息
            String restUrl = new BusinessRestLocator().getRestUrl("/bussContract/getByContractCodes");
            Map<String, Object> params = new HashMap<>();
            params.put("contractCodes", String.join(",", contractCodes));
            @SuppressWarnings("unchecked")
            RestResponse<List<BussContractLiteDTO>> response =
                    RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, params, null, BussContractLiteDTO.class);
            for (BussContractLiteDTO contract : response.getData()) {
                if (org.apache.commons.lang3.StringUtils.isNotBlank(contract.getMaintainId())) {
                    users.add(contract.getMaintainId());
                }
                if (org.apache.commons.lang3.StringUtils.isNotBlank(contract.getSignSalesId())) {
                    users.add(contract.getSignSalesId());
                }
            }
            // 当前登陆人
            users.add(LoginSessionUtil.getBaseLoginInfo()
                    .obtainBaseInfo().getStaffCode());
        } else if (Objects.equals(dataPerm.getDataPermType(), RESPONSIBLE_CUSTOMER.getType())
                || Objects.equals(dataPerm.getDataPermType(), ASSIGN_CUSTOMER.getType())) {
            // 负责客户 | 指定客户
            // 客户编码
            Set<String> userCodes = dataPerm.getDataCodeSet();
            users.addAll(userCodes);
            // 当前登陆人
            users.add(LoginSessionUtil.getBaseLoginInfo()
                    .obtainBaseInfo().getStaffCode());
        } else if (Objects.equals(dataPerm.getDataPermType(), SELF_DEPT.getType())
                || Objects.equals(dataPerm.getDataPermType(), ASSIGN_DEPT.getType())) {
            // 所属部门 | 指定部门
            dataPerm =
                    LoginSessionUtil.getBaseLoginInfo()
                            .obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.ID);
            Set<String> structIds = dataPerm.getDataCodeSet();
            // 根据合同编码查询所有合同信息
            String restUrl = new UserRestLocator().getRestUrl("/provider/staff/getByStructIds");
            Map<String, Object> params = new HashMap<>();
            params.put("structIds", String.join(",", structIds));
            @SuppressWarnings("unchecked")
            RestResponse<List<ProviderStaffBaseDetailRespDTO>> response =
                    RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, params, null, ProviderStaffBaseDetailRespDTO.class);
            for (ProviderStaffBaseDetailRespDTO staff : response.getData()) {
                users.add(staff.getProviderStaffCode());
            }
            // 当前登陆人
            users.add(LoginSessionUtil.getBaseLoginInfo()
                    .obtainBaseInfo().getStaffCode());
        }
        return users;
    }

}