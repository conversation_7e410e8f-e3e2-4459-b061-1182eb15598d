package com.izu.mrcar.providerController.review;

import com.google.common.collect.Maps;
import com.izu.config.dto.*;
import com.izu.consts.ConfigURI;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.config.RestUrlConfig;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * @ClassName ReviewOptionController
 * <AUTHOR>
 * @Date 2024/6/14 14:05
 * @Version 1.0
 */
@Slf4j
@RestController
@Api(tags="评价表单")
public class ReviewFormController {

    @PostMapping(ConfigURI.REVIEW_FORM_LIST)
    @RequestFunction(functionName = "评价表单列表")
    @ApiOperation(value = "评价表单列表")
    public RestResponse<PageDTO<ReviewFormRespDTO>> getList(@RequestBody ReviewFormQueryReqDTO param) {
        String restUrl = RestUrlConfig.getConfig().getConfigCoreUrl() + ConfigURI.REVIEW_FORM_LIST;
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, ReviewFormRespDTO.class);
    }

    @PostMapping(ConfigURI.REVIEW_FORM_ALL)
    @RequestFunction(functionName = "已启用评价表单列表")
    @ApiOperation(value = "已启用评价表单列表")
    public RestResponse<List<ReviewFormRespDTO>> getAll() {
        String restUrl = RestUrlConfig.getConfig().getConfigCoreUrl() + ConfigURI.REVIEW_FORM_ALL;
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, null, null);
    }

    @PostMapping(ConfigURI.REVIEW_FORM_DETAIL)
    @ApiOperation("评价表单详情")
    @RequestFunction(functionName = "评价表单详情")
    public RestResponse<ReviewFormDetailRespDTO> detail(Integer id) {
        String restUrl = RestUrlConfig.getConfig().getConfigCoreUrl() + ConfigURI.REVIEW_FORM_DETAIL;
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, id);
        paramMap.put("id",id);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null);
    }

    @PostMapping(ConfigURI.REVIEW_FORM_OPEN_OR_CLOSE)
    @ApiOperation("评价表单启用/停用")
    @RequestFunction(functionName = "评价表单启用/停用")
    public RestResponse<ReviewOptionRespDTO> openOrClose(@RequestBody ReviewFormDTO reviewFormDTO) {
        String restUrl = RestUrlConfig.getConfig().getConfigCoreUrl() + ConfigURI.REVIEW_FORM_OPEN_OR_CLOSE;
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        reviewFormDTO.setUpdator(loginBaseInfo.obtainBaseInfo().getStaffName());
        reviewFormDTO.setUpdateTime(new Date());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reviewFormDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null);
    }

    @PostMapping(ConfigURI.REVIEW_FORM_ADD_OR_UPDATE)
    @RequestFunction(functionName = "添加或更新评价表单")
    @ApiOperation("添加或更新评价表单")
    public RestResponse<Void> addOrUpdate(@RequestBody ReviewFormDTO reviewFormDTO) {
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        String restUrl = RestUrlConfig.getConfig().getConfigCoreUrl() + ConfigURI.REVIEW_FORM_ADD_OR_UPDATE;
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        if (Objects.isNull(reviewFormDTO.getId())) {
            reviewFormDTO.setCreator(loginBaseInfo.obtainBaseInfo().getStaffName());
            reviewFormDTO.setCreateTime(new Date());
        }
        reviewFormDTO.setUpdator(loginBaseInfo.obtainBaseInfo().getStaffName());
        reviewFormDTO.setUpdateTime(new Date());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reviewFormDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null);
    }
}