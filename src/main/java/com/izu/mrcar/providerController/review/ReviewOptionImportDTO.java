package com.izu.mrcar.providerController.review;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
/**
 * <AUTHOR>
 * @date 2023/5/8 15:58
 */
@Data
public class ReviewOptionImportDTO {

    /** 评价项名称 **/
    @ExcelProperty(value = "*评价项名称",index = 0)
    private String reviewName;

    /** 满分 **/
    @ExcelProperty(value = "*满分(输入3-10之间的整数)",index = 1)
    private String fullScore;

    /** 好评标签 **/
    @ExcelProperty(value = "好评标签(用英文逗号分隔)",index = 2)
    private String positiveReviewTags;

    /** 差评标签 **/
    @ExcelProperty(value = "差评标签(用英文逗号分隔)",index = 3)
    private String badReviewTags;
}
