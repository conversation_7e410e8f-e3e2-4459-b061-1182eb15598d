package com.izu.mrcar.providerController.review;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.style.row.SimpleRowHeightStyleStrategy;
import com.google.common.collect.Maps;
import com.izu.config.dto.ReviewOptionDTO;
import com.izu.config.dto.ReviewOptionQueryReqDTO;
import com.izu.config.dto.ReviewOptionRespDTO;
import com.izu.consts.ConfigURI;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.config.RestUrlConfig;
import com.izu.mrcar.controller.maintain.garage.CustomCellWriteHandler;
import com.izu.mrcar.controller.maintain.garage.MaintainImportDTO;
import com.izu.mrcar.service.review.ReviewOptionImportService;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.excel.SelectDataSheetWriteHandler;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.enums.role.SystemTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @ClassName ReviewOptionController
 * <AUTHOR>
 * @Date 2024/6/14 14:05
 * @Version 1.0
 */
@Slf4j
@RestController
@Api(tags = "评价项")
public class ReviewOptionController {

    @Autowired
    private ReviewOptionImportService reviewOptionImportService;

    @PostMapping(ConfigURI.REVIEW_OPTION_LIST)
    @RequestFunction(functionName = "评价项列表")
    @ApiOperation(value = "评价项列表")
    public RestResponse<PageDTO<ReviewOptionRespDTO>> getList(@RequestBody ReviewOptionQueryReqDTO param) {
        String restUrl = RestUrlConfig.getConfig().getConfigCoreUrl() + ConfigURI.REVIEW_OPTION_LIST;
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, ReviewOptionRespDTO.class);
    }

    @PostMapping(ConfigURI.REVIEW_OPTION_ALL)
    @RequestFunction(functionName = "已启用评价项列表")
    @ApiOperation(value = "已启用评价项列表")
    public RestResponse<List<ReviewOptionRespDTO>> getAll() {
        String restUrl = RestUrlConfig.getConfig().getConfigCoreUrl() + ConfigURI.REVIEW_OPTION_ALL;
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, null, null);
    }

    @PostMapping(ConfigURI.REVIEW_OPTION_ADD_OR_UPDATE)
    @ApiOperation("添加或更新评价项")
    @RequestFunction(functionName = "添加或更新评价项")
    public RestResponse<Void> addOrUpdate(@RequestBody ReviewOptionDTO reviewOptionDTO) {
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        String restUrl = RestUrlConfig.getConfig().getConfigCoreUrl() + ConfigURI.REVIEW_OPTION_ADD_OR_UPDATE;
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        if (Objects.isNull(reviewOptionDTO.getId())) {
            reviewOptionDTO.setCreator(loginBaseInfo.obtainBaseInfo().getStaffName());
            reviewOptionDTO.setCreateTime(new Date());
        }
        reviewOptionDTO.setUpdator(loginBaseInfo.obtainBaseInfo().getStaffName());
        reviewOptionDTO.setUpdateTime(new Date());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reviewOptionDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null);
    }


    @PostMapping(ConfigURI.REVIEW_OPTION_DETAIL)
    @ApiOperation("评价项详情")
    @RequestFunction(functionName = "评价项详情")
    public RestResponse<ReviewOptionRespDTO> detail(Integer id) {
        String restUrl = RestUrlConfig.getConfig().getConfigCoreUrl() + ConfigURI.REVIEW_OPTION_DETAIL;
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, id);
        paramMap.put("id", id);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null);
    }

    @PostMapping(ConfigURI.REVIEW_OPTION_OPEN_OR_CLOSE)
    @ApiOperation("评价项启用/停用")
    @RequestFunction(functionName = "评价项启用/停用")
    public RestResponse<ReviewOptionRespDTO> openOrClose(@RequestBody ReviewOptionDTO reviewOptionDTO) {
        String restUrl = RestUrlConfig.getConfig().getConfigCoreUrl() + ConfigURI.REVIEW_OPTION_OPEN_OR_CLOSE;
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        reviewOptionDTO.setUpdator(loginBaseInfo.obtainBaseInfo().getStaffName());
        reviewOptionDTO.setUpdateTime(new Date());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reviewOptionDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null);
    }

    @PostMapping(ConfigURI.REVIEW_OPTION_DELETE)
    @ApiOperation("删除评价项")
    @RequestFunction(functionName = "删除评价项")
    public RestResponse<Void> delete(Integer id) {
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        String restUrl = RestUrlConfig.getConfig().getConfigCoreUrl() + ConfigURI.REVIEW_OPTION_DELETE;
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("id", id);
        paramMap.put("updator", loginBaseInfo.obtainBaseInfo().getStaffName());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, paramMap);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null);
    }


    @GetMapping(ConfigURI.REVIEW_OPTION_DOWN_TEMPLATE)
    @ApiOperation(value = "下载导入模板")
    @RequestFunction(functionName = "下载评价项导入模板")
    public void download(HttpServletResponse response) throws Exception {
        //导出模板名称
        String fileName = "导入评价项模板";
        response.setContentType("application/msexcel");
        response.setCharacterEncoding("utf-8");
        //这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        String fileNameEncode = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileNameEncode + ExcelTypeEnum.XLSX.getValue());
        //设置下拉框内容
        Map<Integer, List<String>> selectMap = new HashMap<>();
        //表头
        List<MaintainImportDTO> list = new ArrayList<>();
        EasyExcelFactory.write(response.getOutputStream())
                .registerWriteHandler(new CustomCellWriteHandler())
                .registerWriteHandler(new SelectDataSheetWriteHandler(selectMap))//设置字典
                .registerWriteHandler(new SimpleRowHeightStyleStrategy((short) 28, (short) 18))//设置行高度
                .head(ReviewOptionImportDTO.class)//此处对应的是实体类
                .excelType(ExcelTypeEnum.XLSX)//设置导出格式为xls后缀
                .sheet("sheet1")
                .doWrite(list);
    }


    @ApiOperation(value = "导入评价项")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "文件名称", required = true, paramType = "form")
    })
    @PostMapping(ConfigURI.REVIEW_OPTION_IMPORT)
    @RequestFunction(functionName = "导入评价项")
    public RestResponse importStaff(@Verify(param = "excelUrl", rule = "required") String excelUrl) {
        return reviewOptionImportService.importReviewOption(getExcelInputStream(excelUrl), SystemTypeEnum.PROVIDER.getCode());
    }


    private InputStream getExcelInputStream(final String excelUrl) {
        String LOGTAG = "importReviewOption";
        if (excelUrl.toLowerCase().startsWith("http")) {//--->>>URL地址
            final String baseDir = System.getProperty("java.io.tmpdir") + File.separator + "upload";
            final String excelFileName = excelUrl.substring(excelUrl.lastIndexOf("/") + 1);
            final String storeFilePath = baseDir + File.separator + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + "_" + excelFileName;
            log.info(LOGTAG + "上传文件名称=" + excelFileName);
            log.info(LOGTAG + "存储保存路径=" + storeFilePath);
            Map<String, String> httpHeader = new HashMap<String, String>(2);
            httpHeader.put("Referer", "https://prd-third.izuche.com");//有防盗链机制，必须加上Referer
            boolean downloadOK = BaseHttpClient.download(BaseHttpClient.HttpMethod.GET, excelUrl, null, httpHeader, storeFilePath);
            if (downloadOK) {
                log.info(LOGTAG + "文件下载成功");
                File storeFile = new File(storeFilePath);
                try {
                    return new FileInputStream(storeFile);
                } catch (FileNotFoundException e) {
                    log.error(LOGTAG + "文件下载失败", e);
                    return null;
                }
            } else {
                log.error(LOGTAG + "文件下载失败");
                return null;
            }
        } else if (excelUrl.toLowerCase().startsWith("file:")) {//--->>>本地文件路径，方便开发调试
            final String storeFilePath = excelUrl.substring(5);
            log.info(LOGTAG + "存储保存路径=" + storeFilePath);
            if (!storeFilePath.toLowerCase().endsWith("xls") && !storeFilePath.toLowerCase().endsWith("xlsx")) {
                log.error(LOGTAG + "不是Excel文件");
                return null;
            }
            File storeFile = new File(storeFilePath);
            if (storeFile.exists() == false) {
                log.error(LOGTAG + "文件不存在");
                return null;
            }
            if (storeFile.isFile() == false) {
                log.error(LOGTAG + "文件不存在");
                return null;
            }
            try {
                return new FileInputStream(storeFile);
            } catch (FileNotFoundException e) {
                log.error(LOGTAG + "文件不存在", e);
                return null;
            }
        } else {
            log.error(LOGTAG + "不支持此类型的URL！");
            return null;
        }
    }

}