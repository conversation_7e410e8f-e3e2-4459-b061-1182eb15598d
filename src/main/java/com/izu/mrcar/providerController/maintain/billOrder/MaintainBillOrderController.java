package com.izu.mrcar.providerController.maintain.billOrder;

import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.maintain.billorder.MaintainAdvanceDTO;
import com.izu.asset.dto.maintain.billorder.MaintainAdvanceReqDTO;
import com.izu.asset.dto.maintain.billorder.MaintainBillDTO;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@Api(tags = "维保事故账单明细")
public class MaintainBillOrderController {
    private final MrCarAssetRestLocator mrCarAssetRestLocator = new MrCarAssetRestLocator();


    @PostMapping(MrCarAssetRestCenter.MAINTAIN_ADVANCE_DETAIL)
    @RequestFunction(functionName = "维保代垫账单明细-运营端")
    @ApiOperation(value = "维保代垫账单明细", notes = "作者：丁伟兵")
    public RestResponse<PageDTO<MaintainAdvanceDTO>> getAdvanceDetailList(@RequestBody MaintainAdvanceReqDTO param) {
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.MAINTAIN_ADVANCE_DETAIL);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, MaintainAdvanceDTO.class);
    }

    @PostMapping(MrCarAssetRestCenter.MAINTAIN_HOSTING_DETAIL)
    @RequestFunction(functionName = "维保托管账单明细-运营端")
    @ApiOperation(value = "维保托管账单明细", notes = "作者：丁伟兵")
    public RestResponse<PageDTO<MaintainBillDTO>> getHostingDetailList(@RequestBody MaintainAdvanceReqDTO param) {
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.MAINTAIN_HOSTING_DETAIL);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, MaintainBillDTO.class);
    }


}
