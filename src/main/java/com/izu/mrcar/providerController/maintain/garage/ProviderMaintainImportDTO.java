package com.izu.mrcar.providerController.maintain.garage;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023/6/3 17:37
 */
@Data
public class ProviderMaintainImportDTO {

    @ExcelProperty(value = "*维修厂名称",index = 0)
    private String garageName;

    //所在省份名称
    @ExcelProperty(value = "*所属省份及城市",index = 1)
    private String garageProvinceName;

    //所在城市name
    //@ExcelProperty(value = "所属城市")
    @ExcelIgnore
    private String garageCityName;
    //地址名称

    /** 详细地址 **/
    @ExcelProperty(value = "*详细地址",index = 2)
    private String addressDetail;

    //维修厂负责人（联系人）姓名
    @ExcelProperty(value = "*维修厂负责人",index = 3)
    private String garageContactorName;

    /** 维修厂负责人（联系人）手机号 **/
    @ExcelProperty(value = "*负责人手机号",index = 4)
    private String garageContactorPhone;


    /** 发票类型（0:无发票 1:增值税专用发票 2:增值税普通发票） **/
    @ExcelProperty(value = "*发票类型",index = 5)
    private Byte contructionInvoiceType;

    /** 发票开具类型（0:纸质发票 1:电子发票） **/
    @ExcelProperty(value = "*发票开具类型",index = 6)
    private Byte contructionInvoiceOpenType;

    /** 发票开具税率 **/
    @ExcelProperty(value = "*税率",index = 7)
    private BigDecimal contructionInvoiceRate;


    /** 是否支持维保（1:是 0:否） **/
    @ExcelProperty(value = "*支持维保",index = 8)
    private String supportRepairName;

    @ExcelIgnore
    private Boolean supportRepair;

    /** 是否支持事故维修（1:是 0:否） **/
    @ExcelIgnore
    private Boolean supportAccidentRepair;
    @ExcelProperty(value = "*支持事故",index = 9)
    private Boolean supportAccidentRepairName;


    /** 准修车型 电车或者油车，逗号间隔 **/
    @ExcelProperty(value = "*准修车型",index = 10)
    private String supportCarType;

    /** 营业开始时间 **/
    @ExcelProperty(value = "*营业开始时间",index = 11)
    private String businessTimeBegin;

    /** 营业结束时间 **/
    @ExcelProperty(value = "*营业结束时间",index = 12)
    private String businessTimeEnd;

    @ExcelProperty(value = "*营业电话(以半角逗号分隔，1-3个)",index = 13)
    private String serviceTelephone;


    /** 工时基数模板ID **/
    @ExcelIgnore
    private Integer workBaseTemplateId;

    /** 工时基数模板名称 **/
    //@ExcelProperty(value = "工时基数模板",index = 14)
    @ExcelIgnore
    private String workBaseTemplateName;

    /** 工时系数模板ID **/
    @ExcelIgnore
    private Integer workFactorTemplateId;

    /** 工时系数模板名称 **/
    //@ExcelProperty(value = "工时系数模板名称",index = 15)
    @ExcelIgnore
    private String workFactorTemplateName;

    /** 合同开始日期 **/
    @ExcelProperty(value = "*合作开始日期",index = 14)
    private Date contractBeginDate;

    /** 合同结束日期 **/
    @ExcelProperty(value = "*合作结束日期",index = 15)
    private Date contractEndDate;


    /** 维修厂资质类型（1一类 2二类 3三类） **/
    @ExcelProperty(value = "*维修厂资质",index = 16)
    private Byte garageQualificationsType;


    /** 开户银行 **/
    @ExcelProperty(value = "开户银行",index = 17)
    private String openBank;

    /** 开户银行账号 **/
    @ExcelProperty(value = "开户银行账号",index = 18)
    private String bankAccount;

    /** 统一社会信用代码 **/
    @ExcelProperty(value = "统一社会信用代码",index = 19)
    private String creditCode;


    /** 服务范围（1仅本企业 2全部） **/
    @ExcelIgnore
    private Byte serviceScope;
    @ExcelProperty(value = "*服务范围",index = 20)
    private String serviceScopeName;

    @ExcelProperty(value = "所属企业",index = 21)
    private String relationCompany;
}
