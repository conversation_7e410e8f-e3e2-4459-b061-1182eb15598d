package com.izu.mrcar.providerController.maintain.workhours;

import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.maintain.workHour.*;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.controller.maintain.workhours.WorkBaseExportDTO;
import com.izu.mrcar.controller.maintain.workhours.WorkFactorExportDTO;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 工时
 * @date 2023/4/25 13:55
 */
@RestController
@Api(tags = "维保管理")
@RequestMapping("/provider")
public class ProviderWorkHoursController {

    private final MrCarAssetRestLocator mrCarAssetRestLocator = new MrCarAssetRestLocator();

    @PostMapping(MrCarAssetRestCenter.WORK_HOURS_TEMPLATE_LIST)
    @ApiOperation(value = "查询工时模板下拉列表",notes = "作者:郝彬杰")
    public RestResponse getWorkTemplateList(@RequestBody MaintainWorkTemplateReqDTO maintainWorkTemplateReqDTO){
        Map<String, Object> paramMap = new HashMap<>();
        maintainWorkTemplateReqDTO.setState(new Byte("1"));
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, maintainWorkTemplateReqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY,  mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.WORK_HOURS_TEMPLATE_LIST), paramMap, null);
    }
    @PostMapping(MrCarAssetRestCenter.WORK_BASE_LIST)
    @ApiOperation(value = "合作关系-工时基数（运营端）",notes = "作者：郝彬杰")
    public RestResponse<PageDTO<WorkBaseDTO>> getWorkBaseDetail(@RequestBody WorkTemplateQueryDTO workTemplateQueryDTO){
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, workTemplateQueryDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY,  mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.WORK_BASE_LIST), paramMap, null);
    }

    @PostMapping(MrCarAssetRestCenter.WORK_FACTOR_LIST)
    @ApiOperation(value = "合作关系-工时系数（运营端）",notes = "作者：郝彬杰")
    public RestResponse<PageDTO<WorkFactorDTO>> getWorkFactorDetail(@RequestBody WorkTemplateQueryDTO workTemplateQueryDTO){
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, workTemplateQueryDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY,  mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.WORK_FACTOR_LIST), paramMap, null);
    }


    @PostMapping(MrCarAssetRestCenter.WORK_BASE_EXPORT)
    @ApiOperation(value = "导出工时基数列表",notes = "作者：郝彬杰")
    @ExportExcelWeb(fileName = "工时基数数据.xlsx",filePath = "/data/logs/excel/tmp",sheet = "工时基数数据",
            c = WorkBaseExportDTO.class
    )
    public PageDTO exportWorkBase(@RequestBody WorkTemplateQueryDTO workBaseQueryDTO, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response){
        Map<String, Object> paramMap = new HashMap<>();
        workBaseQueryDTO.setPageSize(10000);
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        izuEasyExcelSession.setUserName(providerLoginInfo.getBaseInfo().getStaffName());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, workBaseQueryDTO);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.WORK_BASE_LIST), paramMap, null, WorkBaseExportDTO.class);
        if(restResponse!=null && restResponse.isSuccess()) {
            PageDTO pageDTO = (PageDTO) restResponse.getData();
            if (pageDTO.getTotal()>10000){
                throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
            }
            return pageDTO;
        }else{
            return null;
        }
    }

    @PostMapping(MrCarAssetRestCenter.WORK_FACTOR_EXPORT)
    @ApiOperation(value = "导出工时系数",notes = "作者：郝彬杰")
    @ExportExcelWeb(fileName = "导出工时系数数据.xlsx",filePath = "/data/logs/excel/tmp",sheet = "导出工时系数数据",
            c = WorkFactorExportDTO.class
    )
    public PageDTO exportWorkFactor(@RequestBody WorkTemplateQueryDTO workFactorQueryDTO, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response){

        Map<String, Object> paramMap = new HashMap<>();
        workFactorQueryDTO.setPageSize(10000);
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        izuEasyExcelSession.setUserName(providerLoginInfo.getBaseInfo().getStaffName());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, workFactorQueryDTO);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.WORK_FACTOR_LIST), paramMap, null, WorkFactorExportDTO.class);
        if(restResponse!=null && restResponse.isSuccess()) {
            PageDTO pageDTO = (PageDTO) restResponse.getData();
            if (pageDTO.getTotal()>10000){
                throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
            }
            return pageDTO;
        }else{
            return null;
        }

    }
}
