package com.izu.mrcar.providerController.maintain.part;

import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.maintain.part.input.provider.MaintainProviderPartWarehouseInputDTO;
import com.izu.asset.dto.maintain.part.output.provider.MaintainProviderPartWarehouseListDTO;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.maintain.ProviderMaintainDataPermUtil;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@Api(tags = "维保管理")
public class ProviderMaintainWarehouseController {

    @Autowired
    private ProviderMaintainDataPermUtil providerMaintainDataPermUtil;

    @PostMapping(MrCarAssetRestCenter.PROVIDER_MAINTAIN_WAREHOUSE_LIST)
    @RequestFunction(functionName = "总库存列表-运营端")
    @ApiOperation(value = "总库存列表（运营端）",notes = "作者：mapp")
    public RestResponse<PageDTO<MaintainProviderPartWarehouseListDTO>> getList(@RequestBody MaintainProviderPartWarehouseInputDTO param){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        param.setDataPermType(providerLoginInfo.obtainDataPerm().getDataPermType());
        param.setCompanyCodes(providerMaintainDataPermUtil.getDatePermScope(param.getCompanyCodes()));
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.PROVIDER_MAINTAIN_WAREHOUSE_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, MaintainProviderPartWarehouseListDTO.class);
    }
}
