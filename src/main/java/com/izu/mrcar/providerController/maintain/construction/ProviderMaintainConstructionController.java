package com.izu.mrcar.providerController.maintain.construction;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.consts.maintain.construction.ConstructionTypeEnum;
import com.izu.asset.dto.maintain.construction.*;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.controller.maintain.construction.AccidentConstructionExportDTO;
import com.izu.mrcar.controller.maintain.construction.MaintainConstructionExportDTO;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.maintain.ProviderMaintainDataPermUtil;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.httpclient.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.*;

@RestController
@Api(tags = "维保管理")
public class ProviderMaintainConstructionController {
    private static final Logger log = LoggerFactory.getLogger(ProviderMaintainConstructionController.class);
    private final MrCarAssetRestLocator mrCarAssetRestLocator = new MrCarAssetRestLocator();
    private final Byte provider = Byte.valueOf("3");
    private final long maxLine = 10000;

    @Autowired
    private ProviderMaintainDataPermUtil providerMaintainDataPermUtil;


    @PostMapping(MrCarAssetRestCenter.PROVIDER_MAINTAIN__PAGE)
    @RequestFunction(functionName = "维保单分页列表-运营端端")
    @ApiOperation(value = "维保单分页列表（运营端）", notes = "作者：连江伟")
    public RestResponse<PageDTO<MaintainConstructionDTO>> getPageList(@RequestBody MaintainSearchReqDTO param) {
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.PROVIDER_MAINTAIN__PAGE);
        param.setConstructionType(ConstructionTypeEnum.REPAIR_UPKEEP.getType());
        param.setQueryType(provider);
        param.setCompanyCodes(providerMaintainDataPermUtil.getDatePermScope(null));
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, MaintainConstructionDTO.class);
    }

    @PostMapping(MrCarAssetRestCenter.PROVIDER_MAINTAIN_EXPORT)
    @RequestFunction(functionName = "维保单导出-运营端")
    @ApiOperation(value = "维保单导出（运营端）", notes = "作者：连江伟")
    public void exportClient(@RequestBody MaintainSearchReqDTO param, HttpServletResponse response) throws IOException {
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo accountBaseInfo = loginBaseInfo.obtainBaseInfo();
        String logUserName = accountBaseInfo.getStaffName() + "(" + accountBaseInfo.getPinyinName() + ")";
        param.setPage(1);
        param.setPageSize(2000);
        param.setConstructionType(ConstructionTypeEnum.REPAIR_UPKEEP.getType());
        param.setCompanyCodes(providerMaintainDataPermUtil.getDatePermScope(null));
        param.setQueryType(provider);
        param.setLoginUserName(logUserName);
        // 查询维保单 数据
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.PROVIDER_MAINTAIN_EXPORT);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        RestResponse<PageDTO<MaintainConstructionDTO>> res = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, MaintainConstructionDTO.class);

        // 整理 查询sheet1 sheet2 数据
        //sheet1维保单数据
        List<MaintainConstructionExportDTO> sheetResult1= new ArrayList<>();
        //sheet1 维保单明细数据
        List<MaintainConstructionDetailExportDTO> sheetResult2 = new ArrayList<>();
        ServletOutputStream outputStream = response.getOutputStream();
        if (null != res && res.isSuccess()) {
            PageDTO<MaintainConstructionDTO> pageDTO = res.getData();
            long total = pageDTO.getTotal();
            if (total > 2000) {
                response.setStatus(HttpStatus.SC_OK);
                response.setContentType("application/json");
                RestResponse failed = RestResponse.create(RestErrorCode.UNKNOWN_ERROR, "导出数据超过2000条，请使用导出至邮箱", false, null);
                outputStream.write(JSON.toJSONString(failed).getBytes());
                return;
            }
            List<MaintainConstructionDTO> result = pageDTO.getResult();
            sheetResult1 = BeanUtil.copyList(result, MaintainConstructionExportDTO.class);
            // 查询sheet2 维保单明细 数据
            result.forEach(o -> {
                List<MaintainConstructionDetailDTO> maintainConstructionDetailDTOList = o.getMaintainConstructionDetailDTOList();
                List<MaintainConstructionDetailExportDTO> maintainConstructionDetailExportDTOS = BeanUtil.copyList(maintainConstructionDetailDTOList, MaintainConstructionDetailExportDTO.class);
                for (MaintainConstructionDetailExportDTO detailExportDTO : maintainConstructionDetailExportDTOS) {
                    //车牌号
                    detailExportDTO.setVehicleLicense(o.getVehicleLicense());
                    //车辆品牌
                    detailExportDTO.setVehicleBrandName(o.getVehicleBrandName());
                    //车型
                    detailExportDTO.setVehicleModelName(o.getVehicleModelName());
                    //出厂时间
                    detailExportDTO.setConstructionOutTime(o.getConstructionOutTime());
                    //维修时公里数
                    detailExportDTO.setReceptionFactoryInMileage(o.getReceptionFactoryInMileage());
                    sheetResult2.add(detailExportDTO);
                }

            });
        }
        response.setCharacterEncoding("UTF-8");
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + ExportExcelConstants.FILE_NAME_MAINTAIN_EXPORT_INFO);
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        ExcelWriter excelWriter = EasyExcel.write(outputStream).registerWriteHandler(new SimpleColumnWidthStyleStrategy(20)).build();
        WriteSheet writeSheet = EasyExcel.writerSheet(0, ExportExcelConstants.SHEET_NAME_MAINTAIN_EXPORT_INFO).head(MaintainConstructionExportDTO.class).build();
        excelWriter.write(sheetResult1, writeSheet);
        WriteSheet writeSheet2 = EasyExcel.writerSheet(1, ExportExcelConstants.SHEET2_NAME_MAINTAIN_EXPORT_INFO).head(MaintainConstructionDetailExportDTO.class).build();
        excelWriter.write(sheetResult2, writeSheet2);
        excelWriter.finish();
        outputStream.flush();
        outputStream.close();
    }


    @PostMapping(MrCarAssetRestCenter.PROVIDER_MAINTAIN_EXPORT_EMAIL)
    @RequestFunction(functionName = "维保单导出到邮箱-运营端")
    @ApiOperation(value = "维保单导出到邮箱（运营端）", notes = "作者：吴林豪")
    public RestResponse exportClientToEmail(@RequestBody MaintainSearchReqDTO param) {
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo accountBaseInfo = loginBaseInfo.obtainBaseInfo();
        String logUserName = accountBaseInfo.getStaffName() + "(" + accountBaseInfo.getPinyinName() + ")";
        param.setLoginUserName(logUserName);
        param.setConstructionType(ConstructionTypeEnum.REPAIR_UPKEEP.getType());
        param.setCompanyCodes(providerMaintainDataPermUtil.getDatePermScope(null));
        param.setQueryType(provider);

        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.PROVIDER_MAINTAIN_EXPORT_EMAIL);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(MrCarAssetRestCenter.PROVIDER_ACCIDENT_PAGE)
    @RequestFunction(functionName = "事故单分页列表-运营端")
    @ApiOperation(value = "事故单分页列表（运营端）", notes = "作者：连江伟")
    public RestResponse<PageDTO<MaintainConstructionDTO>> getPageListForAccident(@RequestBody MaintainSearchReqDTO param) {
        param.setConstructionType(ConstructionTypeEnum.ACCIDENT_REPAIR.getType());
        param.setCompanyCodes(providerMaintainDataPermUtil.getDatePermScope(null));
        param.setQueryType(provider);

        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.PROVIDER_ACCIDENT_PAGE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, MaintainConstructionDTO.class);
    }

    @PostMapping(MrCarAssetRestCenter.PROVIDER_ACCIDENT_EXPORT)
    @RequestFunction(functionName = "事故单导出-运营端")
    @ApiOperation(value = "事故单导出（运营端）", notes = "作者：连江伟")
    @ExportExcelWeb(fileName = ExportExcelConstants.FILE_NAME_ACCIDENT_EXPORT_INFO, filePath = ExportExcelConstants.FILE_PATH,
            sheet = ExportExcelConstants.SHEET_NAME_ACCIDENT_EXPORT_INFO, c = AccidentConstructionExportDTO.class)
    public PageDTO exportClientAccident(@RequestBody MaintainSearchReqDTO param, IzuEasyExcelSession izuEasyExcelSession,
                                        HttpServletRequest request, HttpServletResponse response) {
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getProviderLoginInfo();
        izuEasyExcelSession.setUserName(loginBaseInfo.obtainBaseInfo().getStaffName());
        param.setPage(izuEasyExcelSession.getPageNo());
        param.setPageSize(ExportExcelConstants.PAGE_SIZE);
        param.setConstructionType(ConstructionTypeEnum.ACCIDENT_REPAIR.getType());
        param.setCompanyCodes(providerMaintainDataPermUtil.getDatePermScope(null));
        param.setQueryType(provider);

        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.PROVIDER_ACCIDENT_EXPORT);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        RestResponse<PageDTO<MaintainConstructionDTO>> res = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, MaintainConstructionDTO.class);
        if (null != res && res.isSuccess()) {
            PageDTO<MaintainConstructionDTO> pageDTO = res.getData();
            if (pageDTO.getTotal() > maxLine) {
                throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
            }
            List<AccidentConstructionExportDTO> list = BeanUtil.copyList(pageDTO.getResult(), AccidentConstructionExportDTO.class);
            return new PageDTO(pageDTO.getPage(), pageDTO.getPageSize(), pageDTO.getTotal(), list);
        } else {
            return null;
        }
    }

    @PostMapping(MrCarAssetRestCenter.PROVIDER_MAINTAIN_DETAIL)
    @RequestFunction(functionName = "事故单详情-运营端")
    @ApiOperation(value = "事故单详情（运营端）", notes = "作者：连江伟")
    public RestResponse<MaintainDetailDTO> getPageListForAccident(String constructionNo) {
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.PROVIDER_MAINTAIN_DETAIL);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("constructionNo", constructionNo);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, url, paramMap, null);
    }
}
