package com.izu.mrcar.providerController.maintain.booking;

import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.maintain.booking.MaintainBookingConfigReqDTO;
import com.izu.asset.dto.maintain.booking.MaintainBookingOrderProviderPageReqDTO;
import com.izu.asset.dto.maintain.booking.MaintainBookingOrderProviderPageRespDTO;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.maintain.ProviderMaintainDataPermUtil;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
* @Description: 维保评价接口
* @author: hxc
* @Date: 2023/3/30
**/
@RestController
@Api(tags = "维保管理")
public class ProviderMaintainBookingController {

    private final MrCarAssetRestLocator mrCarAssetRestLocator = new MrCarAssetRestLocator();

    @Autowired
    private ProviderMaintainDataPermUtil providerMaintainDataPermUtil;

    @PostMapping(MrCarAssetRestCenter.PROVIDER_MAINTAIN_BOOKING_GET_PAGE_LIST)
    @RequestFunction(functionName = "维保预约单分页列表-运营端")
    @ApiOperation(value = "维保预约单分页列表",notes = "作者：贺新春")
    public RestResponse<PageDTO<MaintainBookingOrderProviderPageRespDTO>> getPageList(@RequestBody MaintainBookingOrderProviderPageReqDTO param){

        param.setCompanyCodes(providerMaintainDataPermUtil.getDatePermScope(param.getCompanyCodes()));
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.PROVIDER_MAINTAIN_BOOKING_GET_PAGE_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, MaintainBookingOrderProviderPageRespDTO.class);
    }

    @PostMapping(MrCarAssetRestCenter.PROVIDER_MAINTAIN_BOOKING_EXPORT)
    @RequestFunction(functionName = "维保预约单导出-运营端")
    @ApiOperation(value = "维保预约单导出",notes = "作者：贺新春")
    @ExportExcelWeb(fileName = ExportExcelConstants.FILE_NAME_MAINTAIN_BOOKING_ORDER, filePath = ExportExcelConstants.FILE_PATH,
            sheet = ExportExcelConstants.SHEET_NAME_MAINTAIN_BOOKING_ORDER, c = ProviderMaintainBookingOrderExportDTO.class)
    public PageDTO<ProviderMaintainBookingOrderExportDTO> export(@RequestBody MaintainBookingOrderProviderPageReqDTO param, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response) {

        param.setCompanyCodes(providerMaintainDataPermUtil.getDatePermScope(param.getCompanyCodes()));
        AccountBaseInfo baseInfo = LoginSessionUtil.getProviderLoginInfo().getBaseInfo();
        izuEasyExcelSession.setUserName(baseInfo.getStaffName());
        param.setPage(izuEasyExcelSession.getPageNo());
        param.setPageSize(ExportExcelConstants.PAGE_SIZE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.PROVIDER_MAINTAIN_BOOKING_GET_PAGE_LIST);
        RestResponse<PageDTO<MaintainBookingOrderProviderPageRespDTO>> restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null,MaintainBookingOrderProviderPageRespDTO.class);
        if(restResponse != null && restResponse.isSuccess()){
            PageDTO<MaintainBookingOrderProviderPageRespDTO> data = restResponse.getData();
            if (data.getTotal()>10000){
                throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
            }
            List<ProviderMaintainBookingOrderExportDTO> exportDTOList = BeanUtil.copyList(data.getResult(), ProviderMaintainBookingOrderExportDTO.class);
            return new PageDTO<>(data.getPage(), data.getPageSize(), data.getTotal(), exportDTOList);
        }
        return null;
    }

    @PostMapping(MrCarAssetRestCenter.PROVIDER_MAINTAIN_BOOKING_CONFIG)
    @RequestFunction(functionName = "维保预约配置")
    @ApiOperation(value = "维保预约配置",notes = "作者：贺新春")
    public RestResponse<Boolean> config(@RequestBody MaintainBookingConfigReqDTO param){

        AccountBaseInfo baseInfo = LoginSessionUtil.getProviderLoginInfo().getBaseInfo();
        param.setLoginUserId(baseInfo.getStaffId());
        param.setLoginUserName(baseInfo.getStaffName());
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.PROVIDER_MAINTAIN_BOOKING_CONFIG);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @GetMapping(MrCarAssetRestCenter.PROVIDER_MAINTAIN_BOOKING_CONFIG_GET)
    @RequestFunction(functionName = "维保预约配置信息获取")
    @ApiOperation(value = "维保预约配置信息获取",notes = "作者：贺新春")
    public RestResponse<Integer> getConfig(){

        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.PROVIDER_MAINTAIN_BOOKING_CONFIG_GET);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.GET, url, null, null);
    }
}
