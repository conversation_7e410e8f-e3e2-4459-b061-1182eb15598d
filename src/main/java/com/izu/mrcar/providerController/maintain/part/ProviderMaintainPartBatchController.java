package com.izu.mrcar.providerController.maintain.part;

import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.maintain.part.input.provider.MaintainProviderPartBatchInputDTO;
import com.izu.asset.dto.maintain.part.output.provider.MaintainProviderPartBatchDetailDTO;
import com.izu.asset.dto.maintain.part.output.provider.MaintainProviderPartBatchListDTO;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.maintain.ProviderMaintainDataPermUtil;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@Api(tags = "维保管理")
public class ProviderMaintainPartBatchController {

    @Autowired
    private ProviderMaintainDataPermUtil providerMaintainDataPermUtil;

    @PostMapping(MrCarAssetRestCenter.PROVIDER_MAINTAIN_PART_BATCH_LIST)
    @RequestFunction(functionName = "入库记录列表-运营端")
    @ApiOperation(value = "入库记录列表（运营端）",notes = "作者：mapp")
    public RestResponse<PageDTO<MaintainProviderPartBatchListDTO>> getList(@RequestBody MaintainProviderPartBatchInputDTO param){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        param.setDataPermType(providerLoginInfo.obtainDataPerm().getDataPermType());
        param.setCompanyCodes(providerMaintainDataPermUtil.getDatePermScope(param.getCompanyCodes()));
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.PROVIDER_MAINTAIN_PART_BATCH_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, MaintainProviderPartBatchListDTO.class);
    }

    @PostMapping(MrCarAssetRestCenter.PROVIDER_MAINTAIN_PART_BATCH_DETAIL)
    @RequestFunction(functionName = "入库记录明细-运营端")
    @ApiOperation(value = "入库记录明细（运营端）",notes = "作者：mapp")
    @ApiImplicitParam(name = "batchNo", value = "批次编号", required = true)
    public RestResponse<MaintainProviderPartBatchDetailDTO> getDetail(String batchNo){
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.PROVIDER_MAINTAIN_PART_BATCH_DETAIL);
        Map<String,Object> param = new HashMap<>();
        param.put("batchNo",batchNo);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, url, param, null, MaintainProviderPartBatchDetailDTO.class);
    }

}
