package com.izu.mrcar.providerController.maintain.part;

import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.maintain.part.input.provider.MaintainProviderPartOutboundInputDTO;
import com.izu.asset.dto.maintain.part.output.provider.MaintainProviderPartOutboundDetailDTO;
import com.izu.asset.dto.maintain.part.output.provider.MaintainProviderPartOutboundListDTO;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.maintain.ProviderMaintainDataPermUtil;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@Api(tags = "维保管理")
public class ProviderMaintainPartOutboundController {

    @Autowired
    private ProviderMaintainDataPermUtil providerMaintainDataPermUtil;

    @PostMapping(MrCarAssetRestCenter.PROVIDER_MAINTAIN_PART_OUTBOUND_LIST)
    @RequestFunction(functionName = "出库记录列表-运营端")
    @ApiOperation(value = "出库记录列表（运营端）",notes = "作者：mapp")
    public RestResponse<PageDTO<MaintainProviderPartOutboundListDTO>> getList(@RequestBody MaintainProviderPartOutboundInputDTO param){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        param.setDataPermType(providerLoginInfo.obtainDataPerm().getDataPermType());
        param.setCompanyCodes(providerMaintainDataPermUtil.getDatePermScope(param.getCompanyCodes()));
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.PROVIDER_MAINTAIN_PART_OUTBOUND_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, MaintainProviderPartOutboundListDTO.class);
    }

    @PostMapping(MrCarAssetRestCenter.PROVIDER_MAINTAIN_PART_OUTBOUND_DETAIL)
    @RequestFunction(functionName = "出库记录明细-运营端")
    @ApiOperation(value = "出库记录明细（运营端）",notes = "作者：mapp")
    @ApiImplicitParam(name = "outboundNo", value = "出库单号", required = true)
    public RestResponse<MaintainProviderPartOutboundDetailDTO> getDetail(String outboundNo){
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.PROVIDER_MAINTAIN_PART_OUTBOUND_DETAIL);
        Map<String,Object> param = new HashMap<>();
        param.put("outboundNo",outboundNo);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, url, param, null, MaintainProviderPartOutboundDetailDTO.class);
    }
}
