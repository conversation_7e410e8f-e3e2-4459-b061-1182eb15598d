package com.izu.mrcar.providerController.maintain.part;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import izu.org.apache.poi.ss.usermodel.BorderStyle;
import izu.org.apache.poi.ss.usermodel.HorizontalAlignment;
import lombok.Data;

import java.math.BigDecimal;

@HeadStyle(
        fillForegroundColor = 22,
        fillBackgroundColor = 22
)
@ColumnWidth(20)
@ContentStyle(
        horizontalAlignment = HorizontalAlignment.CENTER,
        borderBottom = BorderStyle.THIN,
        borderLeft = BorderStyle.THIN,
        borderRight = BorderStyle.THIN,
        borderTop = BorderStyle.THIN,
        wrapped = true
)

@Data
public class ProviderPartInventoryExport {

    @ExcelProperty(
            value = {"企业名称"},
            order = 1
    )
    private String companyName;

    @ExcelProperty(
            value = {"维修厂名称"},
            order = 2
    )
    private String garageName;
    @ExcelProperty(
            value = {"配件名称"},
            order = 3
    )
    private String partName;
    @ExcelProperty(
            value = {"配件编码"},
            order = 4
    )
    private String partNo;
    @ExcelProperty(
            value = {"配件分类"},
            order = 5
    )
    private String categoryName;
    @ExcelProperty(
            value = {"单位"},
            order = 6
    )
    private String partUnit;
    @ExcelProperty(
            value = {"库存数量"},
            order = 7
    )
    private Integer storageQuantity;
    @ExcelProperty(
            value = {"可用数量"},
            order = 8
    )
    private Integer availableQuantity;
    @ExcelProperty(
            value = {"预占数量"},
            order = 9
    )
    private Integer occupiedQuantity;
    @ExcelProperty(
            value = {"库存金额"},
            order = 10
    )
    private BigDecimal storageTotalPrice;
}
