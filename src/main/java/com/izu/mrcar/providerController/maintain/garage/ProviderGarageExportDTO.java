package com.izu.mrcar.providerController.maintain.garage;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.izu.utils.DateUtils;
import lombok.Data;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: 维修厂导出
 * @date 2023/4/24 19:15
 */
@Data
@ColumnWidth(25) // 默认列宽度
public class ProviderGarageExportDTO {


    //维修厂编号
    @ExcelProperty(value = "维修厂编号",index = 0)
    private String garageNo;
    //维修厂名称
    @ExcelProperty(value = "维修厂名称",index = 1)
    private String garageName;

//    @ExcelProperty(value = "合作关系",index = 2)
//    @ColumnWidth(10)
//    private String relationTypeName;

    //所在省份名称
    @ExcelProperty(value = "所属省份",index = 2)
    @ColumnWidth(10)
    private String garageProvinceName;

    //所在城市name
    @ExcelProperty(value = "所属城市",index = 3)
    @ColumnWidth(10)
    private String garageCityName;
    //地址名称

    @ExcelProperty(value = "地址",index = 4)
    @ColumnWidth(30)
    private String addressName;

    //是否支持维保（1:是 0:否
    @ExcelIgnore
    private Boolean supportRepair;

    @ExcelProperty(value = "支持维保",index = 5)
    @ColumnWidth(10)
    private String supportRepairString;
    //是否支持事故维修（1:是 0:否

    @ExcelIgnore
    private Boolean supportAccidentRepair;



    @ExcelProperty(value = "支持事故",index = 6)
    @ColumnWidth(10)
    private String supportAccidentRepairString;
    //准修车型 电车或者油车，逗号间隔

    @ExcelProperty(value = "准修车型",index = 7)
    @ColumnWidth(10)
    private String supportCarType;
    //服务电话（多个英文逗号分隔）
    @ExcelProperty(value = "24小时服务电话",index = 8)
    private String serviceTelephone;
    //维修厂负责人（联系人）姓名
    @ExcelProperty(value = "维修厂负责人",index = 9)
    private String garageContactorName;
    //合同开始日期

    @ExcelIgnore
    private Date contractBeginDate;
    //合同结束日期

    @ExcelIgnore
    private Date contractEndDate;

    @ExcelProperty(value = "合同起止日期",index = 10)
    private String contractBeginEndDate;
    //维修厂状态（1启用 2禁用）


    @ExcelIgnore
    private Byte garageStatus;

    @ExcelProperty(value = "维修厂状态",index = 11)
    private String garageStatusString;
    //更新人名称

    @ExcelProperty(value = "修改人",index = 12)
    private String updaterName;
    //更新时间

    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "修改时间",index = 13)
    private Date updateTime;


    //关系类型：1创建 2合作

    @ExcelIgnore
    private Byte relationType;
    //关系类型名称：1创建 2合作


    public void setSupportRepair(Boolean supportRepair) {
        this.supportRepair = supportRepair;
        this.supportRepairString = Objects.equals(supportRepair,true)?"是":"否";
    }


    public void setSupportAccidentRepair(Boolean supportAccidentRepair) {
        this.supportAccidentRepair=supportAccidentRepair;
        this.supportAccidentRepairString = Objects.equals(supportAccidentRepair,true)?"是":"否";
    }

    public void setContractBeginDate(Date contractBeginDate) {
        this.contractBeginEndDate = DateUtils.getDayStr1(contractBeginDate)+"~"+DateUtils.getDayStr1(contractEndDate);
        this.contractBeginDate = contractBeginDate;
    }

    public void setContractEndDate(Date contractEndDate) {
        this.contractBeginEndDate = DateUtils.getDayStr1(contractBeginDate)+"~"+DateUtils.getDayStr1(contractEndDate);
        this.contractEndDate = contractEndDate;
    }

    public void setContractBeginEndDate(String contractBeginEndDate) {
        this.contractBeginEndDate = DateUtils.getDayStr1(contractBeginDate)+"~"+DateUtils.getDayStr1(contractEndDate);
    }

    public void setGarageStatus(Byte garageStatus) {
        this.garageStatusString = Objects.equals(garageStatus,new Byte("1"))?"启用":"禁用";
        this.garageStatus = garageStatus;
    }
}
