package com.izu.mrcar.providerController.maintain.part;

import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.maintain.part.input.provider.MaintainProviderPartInventoryInputDTO;
import com.izu.asset.dto.maintain.part.output.provider.MaintainProviderAllPartInventoryListDTO;
import com.izu.asset.dto.maintain.part.output.provider.MaintainProviderPartInventoryListDTO;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.controller.maintain.part.PartInventoryExport;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.maintain.ProviderMaintainDataPermUtil;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

@RestController
@Api(tags = "维保管理")
public class ProviderMaintainPartInventoryController {

    public static final String FILE_PATH = "/data/logs/mrcar/tmp";

    @Autowired
    private ProviderMaintainDataPermUtil providerMaintainDataPermUtil;

    @PostMapping(MrCarAssetRestCenter.PROVIDER_MAINTAIN_PART_INVENTORY_LIST)
    @RequestFunction(functionName = "配件库存列表-运营端")
    @ApiOperation(value = "配件库存列表（运营端）",notes = "作者：mapp")
    public RestResponse<PageDTO<MaintainProviderAllPartInventoryListDTO>> getList(@RequestBody MaintainProviderPartInventoryInputDTO param){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        param.setDataPermType(providerLoginInfo.obtainDataPerm().getDataPermType());
        param.setCompanyCodes(providerMaintainDataPermUtil.getDatePermScope(param.getCompanyCodes()));
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.PROVIDER_MAINTAIN_PART_INVENTORY_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, MaintainProviderAllPartInventoryListDTO.class);
    }

    @PostMapping(MrCarAssetRestCenter.PROVIDER_MAINTAIN_PART_INVENTORY_ONE_LIST)
    @RequestFunction(functionName = "单个配件库存批次列表-运营端")
    @ApiOperation(value = "单个配件库存批次列表（运营端）",notes = "作者：mapp")
    public RestResponse<PageDTO<MaintainProviderPartInventoryListDTO>> getOnePartList(@RequestBody MaintainProviderPartInventoryInputDTO param){
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.PROVIDER_MAINTAIN_PART_INVENTORY_ONE_LIST);
        param.setCompanyCodes(providerMaintainDataPermUtil.getDatePermScope(param.getCompanyCodes()));
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, MaintainProviderPartInventoryListDTO.class);
    }

    @PostMapping(MrCarAssetRestCenter.PROVIDER_MAINTAIN_PART_INVENTORY_LIST_EXPORT)
    @RequestFunction(functionName = "配件库存列表导出(运营端)")
    @ApiOperation(value = "配件库存列表导出（运营端）")
    @ExportExcelWeb(fileName = ExportExcelConstants.FILE_NAME_MAINTAIN_PART_INVENTORY,filePath = ExportExcelConstants.FILE_PATH,
            sheet = ExportExcelConstants.SHEET_NAME_MAINTAIN_PART_INVENTORY, c = PartInventoryExport.class)
    public PageDTO exportPartInfo(@RequestBody MaintainProviderPartInventoryInputDTO param, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        String userName = loginBaseInfo.obtainBaseInfo().getStaffName();
        param.setPage(izuEasyExcelSession.getPageNo());
        izuEasyExcelSession.setUserName(userName);
        param.setPageSize(10000);
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.PROVIDER_MAINTAIN_PART_INVENTORY_LIST);
        param.setCompanyCodes(providerMaintainDataPermUtil.getDatePermScope(param.getCompanyCodes()));
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, ProviderPartInventoryExport.class);
        if(restResponse!=null && restResponse.isSuccess()){
            PageDTO<ProviderPartInventoryExport> pageDTO = (PageDTO) restResponse.getData();
            if(pageDTO.getTotal() > 10000){
                throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
            }
            return pageDTO;
        }
        return null;
    }
}
