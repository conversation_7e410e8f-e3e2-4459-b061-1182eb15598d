package com.izu.mrcar.providerController.driver.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/1/9 16:39
 */
@Data
@ColumnWidth(25) // 默认列宽度
public class DriverAllocationListExportDTO {


    @ExcelProperty(value = "分配记录编码",index = 0)
    private String allocationNo;

    @ExcelProperty(value = "分配方式",index = 1)
    private String allocationMethodName;

    @ExcelProperty(value = "司机姓名",index = 2)
    private String driverNames;

    @ExcelProperty(value = "司机数量",index = 3)
    private Integer driverCount;

    @ExcelProperty(value = "调入方企业名称",index = 4)
    private String targetCompanyName;
    @ExcelProperty(value = "分配事由",index = 5)
    private String reason;
    @ExcelProperty(value = "创建人",index = 6)
    private String createdName;
    @ExcelProperty(value = "创建时间",index = 7)
    private Date createdTime;


}
