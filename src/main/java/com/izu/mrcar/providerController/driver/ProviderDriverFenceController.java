package com.izu.mrcar.providerController.driver;

import com.alibaba.fastjson.JSON;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.enums.perm.ClientDataPermTypeEnum;
import com.izu.user.enums.perm.ProviderDataPermTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.*;


/**
 * 司机考勤围栏管理代理层相关服务接口
 */
@RestController
@RequestMapping("/provider/driverFenceManage")
@Api(tags = "司机管理")
public class ProviderDriverFenceController {
	private static final Logger logger = LoggerFactory.getLogger(ProviderDriverFenceController.class);

	/**
	 * 加载配置服务mrcar-config-core访问域名及项目名
	 */
	@Value("${mrcar-config-core.host.url}")
	private String CONFIG_HOST_URL;

	@Value("${mrcar-user-core.host.url}")
	private String USER_HOST_URL;

	@Value("${mrcar-iot-core.host.url}")
	private String IOT_HOST_URL;

	/**
	 * 司机考勤围栏信息列表查询
	 * @param paraMap
	 * @return
	 */
    @PostMapping(value = "/selectDriverFenceByPage")
	@RequiresPermissions(value="sopAttendanceFenceList")
	@RequestFunction(functionName = "考勤围栏")
	@ApiOperation(value = "考勤围栏-列表")
	public RestResponse selectDriverFenceByPage(@RequestBody Map<String,Object> paraMap){
    	String restUrl = IOT_HOST_URL + "provider/driverFenceManage/selectDriverFenceByPage";
		// 获取当前登录用户信息
		ProviderLoginInfo loginUser = LoginSessionUtil.getProviderLoginInfo();
		int companyId = loginUser.obtainBelongCompanyId();
		Byte dataPermType = loginUser.obtainSimpleDataPerm().getDataPermType();
		paraMap.put("companyId", companyId);
		paraMap.put("permissions",String.join(",",loginUser.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CITY).getDataCodeSet()));
		if(ProviderDataPermTypeEnum.ASSIGN_DEPT.getType().equals(dataPermType) || ProviderDataPermTypeEnum.SELF_DEPT.getType().equals(dataPermType)){
			paraMap.put("permissions",String.join(",",loginUser.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet()));
		}
		paraMap.put("dataPermType",dataPermType);
		paraMap.put("loginUserId",loginUser.getBaseInfo().getStaffId());
    	return RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, RestResponse.class);
    }
	@PostMapping(value = "/addOrUpdDriverFence")
	@RequiresPermissions(value ={"sop_attendanceFenceAddd","sop_attendanceFenceModifye"},logical = Logical.OR )
	@RequestFunction(functionName = "考勤围栏编辑")
	@ApiOperation(value = "考勤围栏-新增或编辑")
	public RestResponse addOrUpdDriverFence(@RequestBody Map<String,Object> paraMap){
		String restUrl = IOT_HOST_URL + "provider/driverFenceManage/addOrUpdDriverFence";
		// 获取当前登录用户信息
		ProviderLoginInfo loginUser = LoginSessionUtil.getProviderLoginInfo();
		paraMap.put("companyId", loginUser.obtainBelongCompanyId());
		paraMap.put("loginUserId",loginUser.obtainBaseInfo().getStaffId());
		paraMap.put("loginUserName",loginUser.obtainBaseInfo().getStaffName());
		return RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, RestResponse.class);
	}

	@PostMapping(value = "/deleteDriverFence")
	@RequestFunction(functionName = "删除考勤围栏")
	@ApiOperation(value = "考勤围栏-删除")
	public RestResponse deleteDriverFence(@RequestBody Map<String,Object> paraMap){
		String restUrl = IOT_HOST_URL + "provider/driverFenceManage/deleteDriverFence";
		// 获取当前登录用户信息
		ProviderLoginInfo loginUser = LoginSessionUtil.getProviderLoginInfo();
		paraMap.put("companyId", loginUser.obtainBelongCompanyId());
		paraMap.put("loginUserId",loginUser.obtainBaseInfo().getStaffId());
		paraMap.put("loginUserName",loginUser.obtainBaseInfo().getStaffName());
		return RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, RestResponse.class);
	}

	@PostMapping(value = "/getDriverFenceDetail")
	@RequestFunction(functionName = "考勤围栏详情")
	@ApiOperation(value = "考勤围栏-详情")
	public RestResponse getDriverFenceDetail(@RequestBody Map<String,Object> paraMap){
		String restUrl = IOT_HOST_URL + "provider/driverFenceManage/getDriverFenceDetail";
		// 获取当前登录用户信息
		ProviderLoginInfo loginUser = LoginSessionUtil.getProviderLoginInfo();
		paraMap.put("companyId", loginUser.obtainBelongCompanyId());
		return RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, RestResponse.class);
	}

	@RequestMapping(value = "/getMotorcadeByCityCodes",method = {RequestMethod.POST,RequestMethod.GET})
	public RestResponse getMotorcadeByCityCodes(){
		String restUrl = USER_HOST_URL + "/provider/motorcade/getMotorcadeByCityCodes";
		// 获取当前登录用户信息
		ProviderLoginInfo loginUser = LoginSessionUtil.getProviderLoginInfo();
		Byte dataPermType = loginUser.obtainSimpleDataPerm().getDataPermType();

		Map<String,Object> paraMap = new HashMap<>();
		paraMap.put("companyId", loginUser.obtainBelongCompanyId());
		paraMap.put("loginUserId", loginUser.getBaseInfo().getStaffId());
		paraMap.put("dataPermType", dataPermType);
		if(ProviderDataPermTypeEnum.ASSIGN_DEPT.getType().equals(dataPermType) || ProviderDataPermTypeEnum.SELF_DEPT.getType().equals(dataPermType)){
			paraMap.put("deptCodeStr", String.join(",",loginUser.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet()));
		}
		List<Byte> typeList = Arrays.asList(ProviderDataPermTypeEnum.RESPONSIBLE_CONTRACT.getType(),ProviderDataPermTypeEnum.RESPONSIBLE_CUSTOMER.getType(),ProviderDataPermTypeEnum.ASSIGN_CUSTOMER.getType(),ProviderDataPermTypeEnum.ONE_SELF.getType());
		if(typeList.contains(dataPermType)){
			return RestResponse.success(Collections.emptyList());
		}
		return RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, RestResponse.class);
	}

	@RequestMapping(value = "/getCityCodesOfUser",method = {RequestMethod.POST,RequestMethod.GET})
	public RestResponse getCityCodesOfUser(){
		String restUrl = CONFIG_HOST_URL + "city/selectCityInfoByCityCode";
		// 获取当前登录用户信息
		ProviderLoginInfo loginUser = LoginSessionUtil.getProviderLoginInfo();
		Byte dataPermType = loginUser.obtainSimpleDataPerm().getDataPermType();
		logger.info("【获取当前登录用户的信息】"+ JSON.toJSONString(loginUser));
		Map<String,Object> paraMap = new HashMap<>();
		paraMap.put("companyId",loginUser.obtainBelongCompanyId());
		return RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, RestResponse.class);
	}

    
}
