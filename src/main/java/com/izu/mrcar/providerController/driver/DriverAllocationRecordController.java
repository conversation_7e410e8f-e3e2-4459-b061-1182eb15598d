package com.izu.mrcar.providerController.driver;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.style.row.SimpleRowHeightStyleStrategy;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.controller.maintain.garage.CustomCellWriteHandler;
import com.izu.mrcar.providerController.driver.excel.DriverAllocationListExportDTO;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.dto.common.BaseDTO;
import com.izu.user.dto.driver.*;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.enums.DriverEnums;
import com.izu.user.enums.perm.ProviderDataPermTypeEnum;
import com.izu.user.enums.role.SystemTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.*;


/**
 * <AUTHOR>
 * @date 2025/1/7 20:42
 */
@RestController
@Api(tags = "司机调拨")
public class DriverAllocationRecordController {


    /**
     * 司机调拨
     */
    @PostMapping(UserUrlCenter.PROVIDER_DRIVER_DO_ALLOCATION)
    @ApiOperation(value = "提交司机调拨")
    public RestResponse<Void> doAllocation(@RequestBody DriverAllocationRecordSaveReqDTO reqDTO){

        ProviderLoginInfo loginUser = LoginSessionUtil.getProviderLoginInfo();
        reqDTO.setCreatedId(loginUser.getBaseInfo().getStaffId());
        reqDTO.setCreatedName(loginUser.getBaseInfo().getStaffName());
        HashMap<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.PROVIDER_DRIVER_DO_ALLOCATION);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }
    /**
     * 查询司机调拨列表
     */
    @ApiOperation(value = "查询司机调拨列表")
    @PostMapping(UserUrlCenter.PROVIDER_DRIVER_ALLOCATION_LIST)
    public RestResponse<PageDTO<List<DriverAllocationRecordListRespDTO>>> allocationList(@RequestBody DriverAllocationRecordListReqDTO reqDTO){
        HashMap<String, Object> restParam = new HashMap<>();
        setPerm(reqDTO);
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.PROVIDER_DRIVER_ALLOCATION_LIST);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }
    /**
     * 司机调拨列表导出
     */
    @PostMapping(UserUrlCenter.PROVIDER_DRIVER_ALLOCATION_LIST_EXPORT)
    @ApiOperation(value = "司机调拨列表导出(运营端)", notes = "作者:郝彬杰")
    @ExportExcelWeb(fileName = "司机调拨列表导出.xlsx", filePath = "/data/logs/excel/tmp", sheet = "司机调拨列表数据",
            c = DriverAllocationListExportDTO.class
    )
    public PageDTO exportGarageList(@RequestBody DriverAllocationRecordListReqDTO reqDTO, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response) {

        Map<String, Object> paramMap = new HashMap<>();
        reqDTO.setPageSize(10000);
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        reqDTO.setDataPermType(providerLoginInfo.obtainDataPerm().getDataPermType());
        // maintainGarageQueryReqDTO.setDataCodeSet(providerMaintainDataPermUtil.getDatePermScope(null));
        izuEasyExcelSession.setUserName(providerLoginInfo.getBaseInfo().getStaffName());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, new UserRestLocator().getRestUrl(UserUrlCenter.PROVIDER_DRIVER_ALLOCATION_LIST), paramMap, null, DriverAllocationListExportDTO.class);
        if (restResponse != null && restResponse.isSuccess()) {
            PageDTO pageDTO = (PageDTO) restResponse.getData();
            if (pageDTO.getTotal()>10000){
                throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
            }
            return pageDTO;
        } else {
            return null;
        }
    }
    /**
     * 司机调拨记录详情
     */
    @ApiOperation(value = "司机调拨记录详情")
    @PostMapping(UserUrlCenter.PROVIDER_DRIVER_ALLOCATION_DETAIL)
    public RestResponse<DriverAllocationRecordDetailReqDTO> allocationDetail(@RequestBody DriverAllocationSimpleQueryDTO reqDTO){
        HashMap<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.PROVIDER_DRIVER_ALLOCATION_DETAIL);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }

    /**
     * 调拨司机批量导入
     */
    @ApiOperation(value = "调拨司机批量导入")
    @PostMapping(UserUrlCenter.PROVIDER_DRIVER_ALLOCATION_IMPORT)
    @ApiParam(value = "excelUrl", required = true)
    public RestResponse<Void> allocationImport(@RequestParam(value = "excelUrl",required = true) String excelUrl){
        HashMap<String, Object> restParam = new HashMap<>();
        restParam.put("excelUrl", excelUrl);
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.PROVIDER_DRIVER_ALLOCATION_IMPORT);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, restParam, null);
    }

    /**
     * 查询待调拨司机列表
     */
    @ApiOperation(value = "查询待调拨司机列表")
    @PostMapping(UserUrlCenter.PROVIDER_DRIVER_ALLOCATION_WAIT_LIST)
    public RestResponse<PageDTO<List<DriverWaitAllocationRespDTO>>> allocationWaitList(@RequestBody DriverWaitAllocationReqDTO reqDTO){
        HashMap<String, Object> restParam = new HashMap<>();
        reqDTO.setDriverSourceType(DriverEnums.DriverSourceTypeEnum.sqsj.getCode());
        doPerm(reqDTO);
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);

        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.PROVIDER_DRIVER_ALLOCATION_WAIT_LIST);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }
    /**
     * 根据司机id查询司机列表
     */
    @ApiOperation(value = "根据司机id查询司机列表")
    @PostMapping(UserUrlCenter.PROVIDER_DRIVER_ALLOCATION_BATCH_LIST)
    public RestResponse<List<DriverWaitAllocationRespDTO>> allocationWaitListByDriverId(@RequestBody DriverWaitAllocationSimpleReqDTO reqDTO){
        HashMap<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.PROVIDER_DRIVER_ALLOCATION_BATCH_LIST);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }

    /**
     * 下载调拨司机模板
     */
    @ApiOperation(value = "下载调拨司机导入模板")
    @PostMapping(UserUrlCenter.PROVIDER_DRIVER_ALLOCATION_IMPORT_TEMPLATE)
    public void allocationDownloadTemplate(HttpServletResponse response ) throws Exception {
        //导出模板名称
        String fileName = "下载调拨司机导入模板";
        response.setContentType("application/msexcel");
        response.setCharacterEncoding("utf-8");
        //这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        String fileNameEncode = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''"  + fileNameEncode + ExcelTypeEnum.XLSX.getValue());
        //表头
        List<DriverAllocationImportDTO> list = new ArrayList<>();
        EasyExcelFactory.write(response.getOutputStream())
                .registerWriteHandler(new CustomCellWriteHandler())
                //.registerWriteHandler(new SimpleColumnWidthStyleStrategy(30))//设置列宽度
                .registerWriteHandler(new SimpleRowHeightStyleStrategy((short) 28, (short) 18))//设置行高度
                .head(DriverAllocationImportDTO.class)//此处对应的是实体类
                .excelType(ExcelTypeEnum.XLSX)//设置导出格式为xls后缀
                .sheet("sheet1")
                .doWrite(list);
    }

    private void setPerm(BaseDTO reqDTO){
        ProviderLoginInfo loginUser = LoginSessionUtil.getProviderLoginInfo();
        reqDTO.setLoginUserId(loginUser.getBaseInfo().getStaffId());
        reqDTO.setLoginUserName(loginUser.getBaseInfo().getStaffName());
        reqDTO.setLoginUserMobile(loginUser.getBaseInfo().getMobile());
        reqDTO.setLoginSystemType(SystemTypeEnum.PROVIDER.getCode());
        reqDTO.setDataPermType(loginUser.obtainDataPerm().getDataPermType());
        Set<String> dataPermCodes = loginUser.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet();
        reqDTO.setDataCodeSet(dataPermCodes);

    }

    private void doPerm(DriverWaitAllocationReqDTO reqDTO){
        ProviderLoginInfo loginUser = LoginSessionUtil.getProviderLoginInfo();
        reqDTO.setLoginUserId(loginUser.getBaseInfo().getStaffId());
        reqDTO.setLoginUserName(loginUser.getBaseInfo().getStaffName());
        reqDTO.setLoginUserMobile(loginUser.getBaseInfo().getMobile());
        reqDTO.setLoginSystemType(SystemTypeEnum.PROVIDER.getCode());
        reqDTO.setDataPermType(loginUser.getProviderDataPerm().getDataPermType());
        Byte dataPermType = loginUser.obtainDataPerm().getDataPermType();
        List<Byte> dataPermTypes= Lists.newArrayList(
                ProviderDataPermTypeEnum.RESPONSIBLE_CONTRACT.getType(),
                ProviderDataPermTypeEnum.RESPONSIBLE_CUSTOMER.getType(),
                ProviderDataPermTypeEnum.ASSIGN_CUSTOMER.getType(),
                ProviderDataPermTypeEnum.ONE_SELF.getType()
                );
        if(dataPermTypes.contains(dataPermType)){
            reqDTO.setBelongStructCodeList(Sets.newHashSet("-1"));
            return;
        }
        if(Objects.equals(ProviderDataPermTypeEnum.SELF_DEPT.getType(),dataPermType)){
            Set<String> dataPermCodes = loginUser.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet();
            reqDTO.setBelongStructCodeList(dataPermCodes);
            return;
        }
        if(Objects.equals(ProviderDataPermTypeEnum.ASSIGN_DEPT.getType(),dataPermType)){
            Set<String> dataPermCodes = loginUser.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet();
            reqDTO.setBelongStructCodeList(dataPermCodes);
            return;
        }


    }
}
