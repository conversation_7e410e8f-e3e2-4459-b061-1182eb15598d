package com.izu.mrcar.providerController.driver.lingsan;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.style.row.SimpleRowHeightStyleStrategy;
import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.dto.vehicle.SupplierCarBatchImportDTO;
import com.izu.mrcar.providerController.asset.lingsan.CarInfoImportLingSanExcelComponent;
import com.izu.mrcar.providerController.asset.lingsan.DriverInfoImportLingSanExcelComponent;
import com.izu.user.enums.DriverSexEnum;
import com.izu.config.MrCarConfigRestLocator;
import com.izu.config.dto.CityDicDTO;
import com.izu.consts.ConfigURI;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.controller.maintain.garage.CustomCellWriteHandler;
import com.izu.mrcar.controller.maintain.garage.MaintainImportDTO;
import com.izu.mrcar.dto.DicKeyValueDTO;
import com.izu.mrcar.providerController.asset.lingsan.ProviderDriverImportDTO;
import com.izu.mrcar.service.common.ProviderStateEnumService;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.excel.SelectDataSheetMergeWriteHandler;
import com.izu.mrcar.utils.lingsan.ProviderLingsanDataPermUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.dto.company.lingsan.SupplierSimpleRespDTO;
import com.izu.user.dto.driver.*;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import com.izu.user.enums.DriverEnums;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@Api(tags = {"零散用车-司机管理"})
public class DriverProviderLingSanController {

    private final UserRestLocator userRestLocator = new UserRestLocator();

    @Autowired
    private ProviderStateEnumService providerStateEnumService;

    @PostMapping(UserUrlCenter.PROVIDER_LING_SAN_GET_DRIVER_PAGE)
	@RequestFunction(functionName = "司机列表-零散用车")
    @ApiOperation(value = "司机列表",notes = "作者：贺新春")
    public RestResponse<DriverProviderLingSanPageRespDTO> getDriverPage(@RequestBody DriverProviderLingSanPageReqDTO param){

        if (DriverEnums.DriverSourceTypeEnum.sqsj.getCode().equals(param.getDriverSourceType())){
            param.setDataCodeSet(ProviderLingsanDataPermUtil.getDatePermDeptCode());
        }
        String restUrl =  userRestLocator.getRestUrl(UserUrlCenter.PROVIDER_LING_SAN_GET_DRIVER_PAGE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, DriverProviderLingSanPageRespDTO.class);
    }

    @GetMapping(UserUrlCenter.PROVIDER_LING_SAN_GET_DRIVER_DETAIL)
    @RequestFunction(functionName = "司机详情-零散用车")
    @ApiImplicitParam(name = "driverId",value = "司机ID",required = true,paramType="form")
    @ApiOperation(value = "司机详情",notes = "作者：贺新春")
    public RestResponse<DriverDetailDTO> getDriverDetail(@Verify(param = "driverId",rule = "required") Integer driverId){

        String restUrl = userRestLocator.getRestUrl(UserUrlCenter.PROVIDER_LING_SAN_GET_DRIVER_DETAIL);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("driverId", driverId);
        RestResponse restResponse = RestClient.requestForObject(BaseHttpClient.HttpMethod.GET, restUrl, paramMap, null, DriverDetailDTO.class);
        return restResponse;

    }

    @PostMapping(UserUrlCenter.PROVIDER_LING_SAN_ADD_OR_UPD_DRIVER)
    @RequestFunction(functionName = "保存司机-零散用车")
    @ApiOperation(value = "司机新建||编辑",notes = "作者：贺新春")
    public RestResponse<Boolean> addOrUpdDriver(@RequestBody DriverProviderLingSanSaveReqDTO param){

        AccountBaseInfo baseInfo = LoginSessionUtil.getProviderLoginInfo().getBaseInfo();
        param.setLoginUserId(baseInfo.getStaffId());
        param.setLoginUserName(baseInfo.getStaffName());
        String restUrl = userRestLocator.getRestUrl(UserUrlCenter.PROVIDER_LING_SAN_ADD_OR_UPD_DRIVER);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null);
    }

    @PostMapping(UserUrlCenter.PROVIDER_LING_SAN_DRIVER_UPD_STATE)
    @RequestFunction(functionName = "启用停用司机-零散用车")
    @ApiOperation(value = "司机启用||停用",notes = "作者：贺新春")
    public RestResponse<Boolean> changeDriverStatus(@RequestBody DriverUpdateStateReqDTO param) {
        AccountBaseInfo baseInfo = LoginSessionUtil.getProviderLoginInfo().getBaseInfo();
        param.setLoginUserId(baseInfo.getStaffId());
        param.setLoginUserName(baseInfo.getStaffName());
        String restUrl = userRestLocator.getRestUrl(UserUrlCenter.PROVIDER_LING_SAN_DRIVER_UPD_STATE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null);
    }

    @PostMapping(UserUrlCenter.PROVIDER_LING_SAN_DISPATCH_SELECTION_DRIVER_LIST)
    @ApiOperation(value = "零散用车-首汽派单选司机列表",notes = "作者：贺新春")
    public RestResponse<List<DriverSimpleRespDTO>> getSqLsDriverSimpleList(@Valid @RequestBody DispatchSelectionDriverSimpleReqDTO param) {
        Set<String> datePermDeptCode = ProviderLingsanDataPermUtil.getDatePermDeptCode();
        if (!datePermDeptCode.isEmpty() && !datePermDeptCode.contains(param.getStructCode())){
            return RestResponse.success(Collections.emptyList());
        }
        Set<String> structCode = new HashSet<>(1);
        structCode.add(param.getStructCode());
        param.setDataCodeSet(structCode);
        String url = userRestLocator.getRestUrl(UserUrlCenter.PROVIDER_LING_SAN_DISPATCH_SELECTION_DRIVER_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(UserUrlCenter.PROVIDER_LING_SAN_DISPATCH_DOWNLOAD_IMPORT_TEMPLATE)
    @RequestFunction(functionName = "三方司机-导入司机模板下载")
    @ApiOperation("三方司机-导入司机模板下载")
    public void downloadImportTemplate(HttpServletResponse response) throws Exception {

        // 导出模板名称
        String fileName = "三方司机导入模板";
        response.setContentType("application/msexcel");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码
        String fileNameEncode = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''"  + fileNameEncode + ExcelTypeEnum.XLSX.getValue());
        // 设置下拉框内容
        Map<Integer, List<String>> selectMap = buildSelectMap();
        //表头
        List<MaintainImportDTO> list = new ArrayList<>();
        EasyExcelFactory.write(response.getOutputStream())
                .registerWriteHandler(new CustomCellWriteHandler())
                // 设置字典
                .registerWriteHandler(new SelectDataSheetMergeWriteHandler(selectMap))
                // 设置行高度
                .registerWriteHandler(new SimpleRowHeightStyleStrategy((short) 35, (short) 22))
                // 此处对应的是实体类
                .head(ProviderDriverImportDTO.class)
                // 设置导出格式为xls后缀
                .excelType(ExcelTypeEnum.XLSX)
                .sheet("导入司机")
                .doWrite(list);
    }

    private Map<Integer, List<String>> buildSelectMap(){
        //驾照类型
        Map<Integer, List<String>> selectMap = new HashMap<>();
        List<DicKeyValueDTO> result = providerStateEnumService.getResult((byte) 12);
        if(CollectionUtils.isNotEmpty(result)){
            List<String> licenseType = result.stream().map(item->String.valueOf(item.getValue())).collect(Collectors.toList());
            selectMap.put(4,licenseType);
        }

        //司机性别
        List<String> driverSex = new ArrayList<>();
        for (DriverSexEnum driverSexEnum : DriverSexEnum.getAllSex()) {
            driverSex.add(driverSexEnum.getName());
        }
        selectMap.put(1, driverSex);

        //所属城市
        RestResponse cityResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, new MrCarConfigRestLocator().getRestUrl(ConfigURI.DIC_GET_CITY_DIC_LIST), null, null, CityDicDTO.class);
        if(cityResponse!=null && cityResponse.isSuccess()){
            List<CityDicDTO> cityDicDTOS = (List<CityDicDTO>) cityResponse.getData();
            if(CollectionUtils.isNotEmpty(cityDicDTOS)){
                List<String> cityList = cityDicDTOS.stream().map(CityDicDTO::getCityName).collect(Collectors.toList());
                selectMap.put(3,cityList);
            }
        }
        return selectMap;
    }

    @PostMapping(UserUrlCenter.PROVIDER_LING_SAN_DISPATCH_BATCH_INSERT)
    @RequestFunction(functionName = "零散用车-导入司机信息")
    @ApiOperation("零散用车-导入司机信息")
    public RestResponse batchInsert(
            @Validated SupplierDriverBatchImportDTO supplierDriverBatchImportDTO,
            HttpServletRequest request,
            HttpServletResponse response) {
        final DriverInfoImportLingSanExcelComponent driverInfoImportLingSanExcelComponent = new DriverInfoImportLingSanExcelComponent(supplierDriverBatchImportDTO, false);
        return driverInfoImportLingSanExcelComponent.dealExcel(request, response);
    }

}
