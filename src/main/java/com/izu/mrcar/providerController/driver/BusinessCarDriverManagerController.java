package com.izu.mrcar.providerController.driver;

import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.dto.DriverNewDTO;
import com.izu.user.dto.driver.*;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;

/**
 * 商务车订单司机管理相关的接口
 *
 * <AUTHOR> on  2024/7/14 下午3:48
 */
@Api(tags = "司机管理")
@RestController
public class BusinessCarDriverManagerController {

    /**
     * 劳务派遣司机的下拉框
     * 劳务派遣司机=首汽代垫司机
     * 查询司机状态是有效 司机任职状态:正常/休假
     */
    @RequestFunction(functionName = "劳务派遣司机的下拉框")
    @ApiOperation(value = "劳务派遣司机的下拉框")
    @PostMapping(UserUrlCenter.ELIGIBLE_FOR_DISPATCH)
    @SuppressWarnings("unchecked")
    public RestResponse<List<DriverSimpleRespDTO>> getDriversEligibleForDispatch(@RequestBody DriverSimpleReqDTO param) {
        HashMap<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.ELIGIBLE_FOR_DISPATCH);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }

    /**
     * 校验当前司机是否存在商务车司机角色
     *
     * @param driverId UserUrlCenter.ELIGIBLE_FOR_DISPATCH 接口选中的司机ID
     * @return 不存在的话允许创建，此时会返回司机详情。存在的话返回null，msg会给出提示：当前司机已存在商务司机账号，不得重复创建
     */
    @RequestFunction(functionName = "校验当前司机是否存在商务车司机角色")
    @ApiOperation(value = "校验当前司机是否存在商务车司机角色")
    @GetMapping(UserUrlCenter.VERIFY_DRIVER_IS_BUSINESS_CAR_DRIVER)
    @ApiParam(name = "driverId", value = "司机ID", required = true)
    public RestResponse<DriverDetailDTO> verifyDriverIsBusinessCarDriver(Integer driverId) {
        HashMap<String, Object> restParam = new HashMap<>();
        restParam.put("driverId", driverId);
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.VERIFY_DRIVER_IS_BUSINESS_CAR_DRIVER);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, restParam, null);
    }

    /**
     * 代垫司机转换为商务车司机
     */
    @RequestFunction(functionName = "代垫司机转换为商务车司机")
    @ApiOperation(value = "代垫司机转换为商务车司机")
    @PostMapping(UserUrlCenter.CONVERT_DRIVER_TO_BUSINESS_CAR_DRIVER)
    public RestResponse<Boolean> convertDriverToBusinessCarDriver(@RequestBody ConvertDriverToBusinessCarReqDTO carReqDTO) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        carReqDTO.setLoginUserId(baseLoginInfo.obtainBaseInfo().getStaffId());
        carReqDTO.setLoginUserName(baseLoginInfo.obtainBaseInfo().getStaffName());
        HashMap<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, carReqDTO);
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.CONVERT_DRIVER_TO_BUSINESS_CAR_DRIVER);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }


    /**
     * 校验内部账号是否可以转化为商务车司机
     */
    @RequestFunction(functionName = "校验内部账号是否可以转化为商务车司机")
    @ApiOperation(value = "校验内部账号是否可以转化为商务车司机")
    @GetMapping(UserUrlCenter.VERIFY_STAFF_IS_BUSINESS_CAR_DRIVER)
    @ApiParam(name = "providerStaffId", value = "内部账号ID", required = true)
    public RestResponse<CheckStaffIsBusinessCarDriverRespDTO> checkStaffIsBusinessCarDriver(@Verify(param = "providerStaffId", rule = "required") Integer providerStaffId) {
        HashMap<String, Object> restParam = new HashMap<>();
        restParam.put("providerStaffId", providerStaffId);
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.VERIFY_STAFF_IS_BUSINESS_CAR_DRIVER);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, restParam, null);
    }


    /**
     * 将内部账号转换为商务车司机
     */
    @RequestFunction(functionName = "将内部账号转换为商务车司机")
    @ApiOperation(value = "将内部账号转换为商务车司机")
    @PostMapping(UserUrlCenter.CONVERT_STAFF_TO_BUSINESS_CAR_DRIVER)
    public RestResponse<Boolean> convertStaffToBusinessCar(@RequestBody AddBusinessCarDriverFromStaffReqDTO reqDTO) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        reqDTO.setLoginUserId(baseLoginInfo.obtainBaseInfo().getStaffId());
        reqDTO.setLoginUserName(baseLoginInfo.obtainBaseInfo().getStaffName());
        HashMap<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.CONVERT_STAFF_TO_BUSINESS_CAR_DRIVER);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }


    /**
     * 编辑内部账号转换的商务车司机
     */
    @RequestFunction(functionName = "将内部账号转换为商务车司机")
    @ApiOperation(value = "将内部账号转换为商务车司机")
    @PostMapping(UserUrlCenter.EDIT_BUSINESS_CAR_DRIVER)
    public RestResponse<Boolean> editBusinessCarDriver(@RequestBody @Valid EditBusinessCarDriverFromStaffReqDTO reqDTO) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        reqDTO.setLoginUserId(baseLoginInfo.obtainBaseInfo().getStaffId());
        reqDTO.setLoginUserName(baseLoginInfo.obtainBaseInfo().getStaffName());
        HashMap<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.EDIT_BUSINESS_CAR_DRIVER);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }


    /**
     * 删除商务车司机
     * @param reqDTO 删除信息
     * @return 是否删除成功
     */
    @RequestFunction(functionName = "删除商务车司机")
    @ApiOperation(value = "删除商务车司机")
    @PostMapping(UserUrlCenter.DELETE_BUSINESS_CAR_DRIVER)
    public RestResponse<Boolean> deleteBusinessCarDriver(@RequestBody DeleteBusinessDriverReqDTO reqDTO) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        reqDTO.setLoginUserId(baseLoginInfo.obtainBaseInfo().getStaffId());
        reqDTO.setLoginUserName(baseLoginInfo.obtainBaseInfo().getStaffName());
        HashMap<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.DELETE_BUSINESS_CAR_DRIVER);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }


}
