package com.izu.mrcar.providerController.driver;

import cn.hutool.core.util.IdcardUtil;
import com.alibaba.fastjson.JSON;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.controller.AbstractExcelUploadController;
import com.izu.mrcar.controller.driver.DriverController;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.dto.DriverImportDTO;
import com.izu.user.dto.DriverManageNewDTO;
import com.izu.user.dto.DriverNewDTO;
import com.izu.user.dto.ImportErrorDTO;
import com.izu.user.dto.driver.DriverSimpleReqDTO;
import com.izu.user.dto.driver.DriverSimpleRespDTO;
import com.izu.user.dto.driver.ProviderDriverPageReqDTO;
import com.izu.user.dto.provider.driver.DriverDropdownReqDTO;
import com.izu.user.dto.provider.driver.DriverDropdownRespDTO;
import com.izu.user.dto.provider.driver.QueryDriverProviderCondition;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.enums.DriverEnums;
import com.izu.user.enums.DriverOfficeStatusEnum;
import com.izu.user.enums.perm.ProviderDataPermTypeEnum;
import com.izu.user.util.ObjectTransferUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping
@Api(tags = "司机管理")
public class DriverProviderController extends AbstractExcelUploadController<DriverImportDTO, AbstractExcelUploadController.ExcelUploadResult> {

    @PostMapping(UserUrlCenter.PROVIDER_DRIVER_LIST_DRIVER_PAGE)
    @ApiOperation(value = "商务车司机列表")
    public RestResponse<DriverManageNewDTO> getAllDriverListByPage(@RequestBody ProviderDriverPageReqDTO dto) {
        if (dto.getPerm()) {
            dto.putDataPerm(LoginSessionUtil.getBaseLoginInfo());
        } else {
            dto.setDataPermIsNotNull(false);
        }
        //获取运营端会话信息
        String restUrl = UserRestLocator.instance.getRestUrl(UserUrlCenter.PROVIDER_DRIVER_LIST_DRIVER_PAGE);
        Map<String, Object> paramMap = new HashMap<>(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        return ObjectTransferUtil.cast(RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, Map.class));
    }

    @PostMapping(UserUrlCenter.PROVIDER_DRIVER_SAVE_DRIVER)
    @ApiOperation(value = "商务车司机-新建或者编辑")
    public RestResponse<DriverNewDTO> save(@RequestBody DriverNewDTO dto) {
        dto.putDataPerm(LoginSessionUtil.getBaseLoginInfo());
        //获取运营端会话信息
        String restUrl = UserRestLocator.instance.getRestUrl(UserUrlCenter.PROVIDER_DRIVER_SAVE_DRIVER);
        Map<String, Object> paramMap = new HashMap<>(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        return ObjectTransferUtil.cast(RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, Map.class));

    }


    @PostMapping(UserUrlCenter.PROVIDER_DRIVER_CHANGE_DRIVER_STATUS)
    @ApiOperation(value = "商务车司机-停用")
    public RestResponse<DriverNewDTO> changeDriverStatus(@RequestBody DriverNewDTO dto) {
        dto.putDataPerm(LoginSessionUtil.getBaseLoginInfo());
        //获取运营端会话信息
        String restUrl = UserRestLocator.instance.getRestUrl(UserUrlCenter.PROVIDER_DRIVER_CHANGE_DRIVER_STATUS);
        Map<String, Object> paramMap = new HashMap<>(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        return ObjectTransferUtil.cast(RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, Map.class));

    }

    @PostMapping(UserUrlCenter.PROVIDER_DRIVER_BATCH_INSERT)
    @ApiOperation(value = "商务车司机-批量导入")
    public RestResponse batchInsert(
            @Verify(param = "excelUrl", rule = "required") String excelUrl,
            HttpServletRequest request,
            HttpServletResponse response) {
        ExcelUploadResult eur = new ExcelUploadResult();
        return this.start(excelUrl, request, response, eur, 5);
    }


    @Override
    protected List<Integer> getResolveSheetIndexes() {
        return Collections.singletonList(1);
    }

    @Override
    protected String getColumnMappingConfigFile(HttpServletRequest request, HttpServletResponse response) {
        return null;
    }

    @Override
    protected List<DriverImportDTO> convertStringDataToObject(List<Map<Integer, String>> rowDatas, ExcelUploadResult excelUploadResult) {
        List<Map<Integer, String>> collect = rowDatas.stream().filter(row -> !checkFieldAllNull(row)).collect(Collectors.toList());
        return collect.stream().map(row -> {
            DriverImportDTO dto = new DriverImportDTO();
            dto.setDriverName(row.get(0));
            dto.setDriverMobile(row.get(1));
            dto.setGenderStr(row.get(2));
            dto.setOfficeStatusStr(row.get(3));
            dto.setBrithDate(row.get(4));
            dto.setCertNo(row.get(5));
            dto.setLicenseType(row.get(6));
            dto.setFirstPickupTime(row.get(7));
            dto.setIssuingOrgan(row.get(8));
            dto.setArriveTime(row.get(9));
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 校验对象属性是否全为空，不适用于继承的对象
     *
     * @param obj
     * @return
     */
    private boolean checkFieldAllNull(Object obj) {
        Field[] declaredFields = obj.getClass().getDeclaredFields();
        boolean flag = true;
        if (obj instanceof Map) {
            Map<Integer, String> map = ObjectTransferUtil.cast(obj);
            for (Map.Entry entry : map.entrySet()) {
                if (!ObjectUtils.isEmpty(entry.getValue())) {
                    flag = false;
                    break;
                }
            }
            return flag;
        }
        for (Field field : declaredFields) {
            field.setAccessible(true);
            Object o = null;
            try {
                o = field.get(obj);
            } catch (IllegalAccessException e) {
                log.error("校验对象属性值异常");
            }
            if (!ObjectUtils.isEmpty(o)) {
                flag = false;
                break;
            }
        }
        return flag;
    }

    @Override
    protected List<ExcelUploadRowError> checkExcelData(List<DriverImportDTO> rowDatas, HttpServletRequest request, HttpServletResponse response, ExcelUploadResult excelUploadResult) {
        List<ExcelUploadRowError> errorList = new ArrayList<>();
        for (int i = 0; i < rowDatas.size(); i++) {
            DriverImportDTO driverImportDTO = rowDatas.get(i);
/*			if(checkFieldAllNull(driverImportDTO)){
				continue;
			}*/
            String error = "";
            int rowNum = i + 1;

            if (org.apache.commons.lang3.StringUtils.isEmpty(driverImportDTO.getDriverName())) {
                error = error + "司机姓名不能为空,";
            }
            if (org.apache.commons.lang3.StringUtils.isBlank(driverImportDTO.getDriverMobile())) {
                error = error + "手机号不能为空";
            } else {
                Pattern pattern = Pattern.compile("^[1]\\d{10}$");
                if (!pattern.matcher(driverImportDTO.getDriverMobile()).matches()) {
                    error = error + "手机号格式错误,";
                }
            }
            if (org.apache.commons.lang3.StringUtils.isBlank(driverImportDTO.getLicenseType())) {
                error = error + "准驾车型不能为空";
            } else {
                if (!DriverController.LICENSE_TYPE.contains(driverImportDTO.getLicenseType())) {
                    error = error + "准驾车型不合法,请输入正确的准驾车型";
                }
            }
            if (StringUtils.isBlank(driverImportDTO.getGenderStr())) {
                error = error + "司机性别不能为空";
            } else {
                if (!DriverController.GenderType.contains(driverImportDTO.getGenderStr().trim())) {
                    error = error + "司机性别不合法,请输入男或女";
                }
            }
            if (StringUtils.isBlank(driverImportDTO.getGenderStr())) {
                error = error + "工作状态不能为空";
            } else {
                if (DriverOfficeStatusEnum.getKeyByValue(driverImportDTO.getOfficeStatusStr().trim()) == 0) {
                    error = error + "工作状态不合法,请输入正确的工作状态";
                }
            }
            if (StringUtils.isBlank(driverImportDTO.getBrithDate())) {
                error = error + "出生日期不能为空";
            } else {
                if (!DriverController.check(driverImportDTO.getBrithDate().trim())) {
                    error = error + "出生日期不合法,请输入正确格式的日期";
                }
            }
            if (StringUtils.isBlank(driverImportDTO.getCertNo())) {
                error = error + "身份证号不能为空";
            } else {
                if (!IdcardUtil.isValidCard(driverImportDTO.getCertNo().trim())) {
                    error = error + "身份证号不合法,请输入正确格式的身份证号";
                }
            }
            if (StringUtils.isBlank(driverImportDTO.getLicenseType())) {
                error = error + "准驾车型不能为空";
            } else {
                if (!DriverController.LICENSE_TYPE.contains(driverImportDTO.getLicenseType().trim())) {
                    error = error + "准驾车型不合法,请输入正确格式的准驾车型";
                }
            }
            if (StringUtils.isBlank(driverImportDTO.getFirstPickupTime())) {
                error = error + "驾照初领日期不能为空";
            } else {
                if (!DriverController.check(driverImportDTO.getFirstPickupTime().trim())) {
                    error = error + "驾照初领日期不合法,请输入正确格式的日期";
                }
            }
            if (StringUtils.isBlank(driverImportDTO.getIssuingOrgan())) {
                error = error + "发证机关不能为空";
            }
            if (StringUtils.isBlank(driverImportDTO.getArriveTime())) {
                error = error + "驾驶证有效期不能为空";
            } else {
                if (!DriverController.check(driverImportDTO.getArriveTime().trim())) {
                    error = error + "驾驶证有效期不合法,请输入正确格式的日期";
                }
            }

            if (org.apache.commons.lang3.StringUtils.isNotEmpty(error)) {
                if (error.lastIndexOf(",") + 1 == error.length()) {
                    error = error.substring(0, error.length() - 1);
                }
                ExcelUploadRowError rowError = new ExcelUploadRowError(rowNum, error);
                errorList.add(rowError);
            }
        }
        return errorList;
    }

    @Override
    protected boolean beforePersist(List<DriverImportDTO> rowDatas, HttpServletRequest request, HttpServletResponse response, ExcelUploadResult excelUploadResult) {
        return true;
    }

    @Override
    protected RestResponse executePersist(List<DriverImportDTO> rowDatas, HttpServletRequest request, HttpServletResponse response, ExcelUploadResult excelUploadResult) {
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        Map<String, Object> paramMap = new HashMap<>(4);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, rowDatas);
        paramMap.put("createId", loginBaseInfo.obtainBaseInfo().getStaffId());
        paramMap.put("createName", loginBaseInfo.obtainBaseInfo().getStaffName());
        paramMap.put("companyId", loginBaseInfo.obtainBelongCompanyId());
        String restUrl = UserRestLocator.instance.getRestUrl(UserUrlCenter.PROVIDER_DRIVER_BATCH_INSERT);
        RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, ImportErrorDTO.class);
        log.info("【batchInsert】响应response={}", JSON.toJSONString(restResponse));
        if (restResponse != null && restResponse.isSuccess()) {
            List<ExcelUploadRowError> errorList = new ArrayList<>();
            List<ImportErrorDTO> list = ObjectTransferUtil.cast(restResponse.getData());
            if (!CollectionUtils.isEmpty(list)) {
                list.forEach(error -> {
                    ExcelUploadRowError excelUploadRowError = new ExcelUploadRowError(error.getRowNum(), error.getReason());
                    errorList.add(excelUploadRowError);
                });
                excelUploadResult.setCheckErrorMessages(errorList);
                excelUploadResult.setResolvedRows(rowDatas.size());
                excelUploadResult.setPersistSuccessRows(0);
                excelUploadResult.setPersistFailedRows(rowDatas.size());
                excelUploadResult.setErrorMessage("数据未通过校验，请确认后重新上传");
                return this.fail(null, request, response, excelUploadResult);
            }
            excelUploadResult.setPersistSuccessRows(rowDatas.size());
            return this.success(null, request, response, excelUploadResult);
        } else {
            if (restResponse != null) {
                excelUploadResult.setErrorMessage(restResponse.getMsg());
            }
            return this.fail(null, request, response, excelUploadResult);
        }
    }

    /**
     * 全部司机分页查询接口
     *
     * @param condition
     * @return
     */
    @PostMapping(UserUrlCenter.PROVIDER_DRIVER_LIST_COMPANY_DRIVER_PAGE)
    @RequestFunction(functionName = "客户司机列表")
    @ApiOperation(value = "客户司机列表")
    public RestResponse getAllDriverListByPage(@RequestBody QueryDriverProviderCondition condition) {

        //获取运营端会话信息
        ProviderLoginInfo loginUser = LoginSessionUtil.getProviderLoginInfo();
        Byte dataPermType = loginUser.obtainSimpleDataPerm().getDataPermType();
        condition.setDataPermType(loginUser.obtainSimpleDataPerm().getDataPermType());
        if (ProviderDataPermTypeEnum.ASSIGN_CUSTOMER.getType().equals(dataPermType) || ProviderDataPermTypeEnum.RESPONSIBLE_CUSTOMER.getType().equals(dataPermType)) {
            condition.setDataPermSet(loginUser.obtainSimpleDataPerm().getDataCodeSet());
        } else {
            condition.setDataPermSet(loginUser.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet());
        }
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.PROVIDER_DRIVER_LIST_COMPANY_DRIVER_PAGE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, condition);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, null);
        return restResponse;

    }

    /**
     * 司机详情接口
     *
     * @param driverId
     * @return
     */
    @PostMapping(UserUrlCenter.PROVIDER_DRIVER_DETAIL)
    @RequestFunction(functionName = "司机详情查询")
    @ApiOperation(value = "司机详情")
    public RestResponse getDriverDetail(Integer driverId) {
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.PROVIDER_DRIVER_DETAIL);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("driverId", driverId);
        RestResponse restResponse = RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paramMap, null, Map.class);
        return restResponse;

    }

    @PostMapping(UserUrlCenter.PROVIDER_DRIVER_DROPDOWN_LIST)
    @RequestFunction(functionName = "客户司机下拉列表")
    @ApiOperation(value = "客户司机下拉列表")
    public RestResponse driverDropdown(@RequestBody DriverDropdownReqDTO reqDTO) {
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.PROVIDER_DRIVER_DROPDOWN_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, DriverDropdownRespDTO.class);
    }


}
