package com.izu.mrcar.providerController.driver;

import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.shiro.session.WebSessionUtil;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.dto.LoginUser;
import com.izu.user.dto.MotorcadeDTO;
import com.izu.user.dto.input.MotorcadeSaveDTO;
import com.izu.user.dto.provider.driver.MotorcadeQueryDTO;
import com.izu.user.dto.staff.pc.DataPerm;
import com.izu.user.dto.staff.pc.DataPermInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.enums.perm.ProviderDataPermTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@RestController
@RequestMapping()
@Api(tags = "司机管理")
public class MotorcadeProviderController {
	private static final Logger logger = LoggerFactory.getLogger(MotorcadeProviderController.class);

	@PostMapping(value = "/provider/motorcade/listAllByPage")
	@RequiresPermissions(value ="sopAllTeam")
	@RequestFunction(functionName = "全部车队")
	@ApiOperation(value = "客户车队列表",notes = "作者：连江伟")
	public RestResponse<PageDTO<MotorcadeDTO>> listForPage(@RequestBody MotorcadeQueryDTO queryDTO){
		String restUrl =  new UserRestLocator().getRestUrl("/provider/motorcade/listAllByPage");
		ProviderLoginInfo loginUser = LoginSessionUtil.getProviderLoginInfo();
		Byte dataPermType = loginUser.obtainDataPerm().getDataPermType();
		queryDTO.setDataPermType(dataPermType);
		queryDTO.setDataPermSet(loginUser.obtainSimpleDataPerm().getDataCodeSet());
		if(ProviderDataPermTypeEnum.SELF_DEPT.getType().equals(dataPermType) || ProviderDataPermTypeEnum.ASSIGN_DEPT.getType().equals(dataPermType)){
			queryDTO.setDataPermSet(loginUser.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet());
		}
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, queryDTO);
		RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, null);
		return restResponse;
    }

    @PostMapping(value = "/provider/motorcade/listSqByPage")
    @RequiresPermissions(value ="sopTeamList")
    @RequestFunction(functionName = "商务车队")
    @ApiOperation(value = "商务车队列表",notes = "作者：连江伟")
    public RestResponse<PageDTO<MotorcadeDTO>> listForSQPage(@RequestBody MotorcadeQueryDTO queryDTO){
        String restUrl =  new UserRestLocator().getRestUrl("/provider/motorcade/listSqByPage");
        ProviderLoginInfo loginUser = LoginSessionUtil.getProviderLoginInfo();
		Byte dataPermType = loginUser.obtainDataPerm().getDataPermType();
		queryDTO.setDataPermType(dataPermType);
        queryDTO.setDataPermSet(loginUser.obtainSimpleDataPerm().getDataCodeSet());
		if(ProviderDataPermTypeEnum.SELF_DEPT.getType().equals(dataPermType) || ProviderDataPermTypeEnum.ASSIGN_DEPT.getType().equals(dataPermType)){
			queryDTO.setDataPermSet(loginUser.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet());
		}
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, queryDTO);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, null);
        return restResponse;
    }
    
    @PostMapping(value = "/provider/motorcade/create")
	@RequiresPermissions(value = "sop_car_team_add")
	@RequestFunction(functionName = "新增商务车队")
	@ApiOperation(value = "新增商务车队",notes = "作者：连江伟")
	public RestResponse create(@RequestBody MotorcadeSaveDTO saveDTO){
		String restUrl =  new UserRestLocator().getRestUrl("/provider/motorcade/create");
		try {
			ProviderLoginInfo loginUser = LoginSessionUtil.getProviderLoginInfo();
			Integer companyId = loginUser.obtainBelongCompanyId();
			Integer customerId = loginUser.getBaseInfo().getStaffId();
			String customerName = loginUser.getBaseInfo().getStaffName();
			saveDTO.setCompanyId(companyId);
			saveDTO.setOperateId(customerId);
			saveDTO.setOperateName(customerName);
			Map<String, Object> param = new HashMap<>();
			param.put(BaseHttpClient.POSTBODY_MAP_KEY, saveDTO);
			return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null, Map.class);
		} catch(Exception e) {
			logger.error("create Exception：", e);
			return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
		}       	
    }
    
    @PostMapping(value = "/provider/motorcade/getDetailById")
	@RequestFunction(functionName = "车队详情")
	@ApiOperation(value = "车队详情:运营端通用",notes = "作者：连江伟")
	@ApiImplicitParam(name = "motorcadeId",value = "车队id",required = true)
	public RestResponse<MotorcadeDTO> getById(@RequestBody Map<String,Object> paraMap, HttpServletRequest request){
		String restUrl =  new UserRestLocator().getRestUrl("/provider/motorcade/getDetailById");
		return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Map.class);
    }
    
    @PostMapping(value = "/provider/motorcade/update")
	@RequiresPermissions(value="sop_car_team_modify")
	@RequestFunction(functionName = "编辑车队")
	@ApiOperation(value = "编辑车队")
	public RestResponse update(@RequestBody MotorcadeSaveDTO saveDTO){
		String restUrl =  new UserRestLocator().getRestUrl("/provider/motorcade/update");
		try {
			ProviderLoginInfo loginUser = LoginSessionUtil.getProviderLoginInfo();
			String customerName = loginUser.getBaseInfo().getStaffName();
			Integer customerId = loginUser.getBaseInfo().getStaffId();
			saveDTO.setOperateId(customerId);
			saveDTO.setOperateName(customerName);
			Map<String, Object> param = new HashMap<>();
			param.put(BaseHttpClient.POSTBODY_MAP_KEY, saveDTO);
			return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null, Map.class);
		} catch(Exception e) {
			logger.error("getById Exception：", e);
			return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
		}    	
    }

	/**
	 * 启用/停用车队
	 * @param paraMap
	 * @param request
	 * @return
	 */
	@PostMapping(value = "/provider/motorcade/updStatus")
	@RequestFunction(functionName = "编辑车队状态")
	@ApiOperation(value = "启用停用车队",notes = "作者：连江伟")
	public RestResponse updStatus(@RequestBody Map<String,Object> paraMap, HttpServletRequest request){
		String restUrl =  new UserRestLocator().getRestUrl("/provider/motorcade/updStatus");
		try {
			ProviderLoginInfo loginUser = LoginSessionUtil.getProviderLoginInfo();
			String customerName = loginUser.getBaseInfo().getStaffName();
			Integer customerId = loginUser.getBaseInfo().getStaffId();
			paraMap.put("operateId", customerId);
			paraMap.put("operateName", customerName);
			return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Map.class);
		} catch(Exception e) {
			logger.error("getById Exception：", e);
			return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
		}
	}

}
