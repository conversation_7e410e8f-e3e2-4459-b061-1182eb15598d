package com.izu.mrcar.providerController.driver;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.style.row.SimpleRowHeightStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.business.dto.provider.output.AuthAccountContractExportDTO;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.doc.JrdApiDoc;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.constants.Constant;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.common.constants.GlobalResponse;
import com.izu.mrcar.controller.AbstractExcelUploadController;
import com.izu.mrcar.controller.driver.DriverController;
import com.izu.mrcar.controller.maintain.garage.CustomCellWriteHandler;
import com.izu.mrcar.controller.maintain.garage.DbCarExcelSheetWriteHandler;
import com.izu.mrcar.controller.maintain.garage.MaintainImportDTO;
import com.izu.mrcar.service.user.ClientUserManageImportStaffService;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.excel.SelectDataSheetMergeWriteHandler;
import com.izu.mrcar.utils.perm.UserDataPermUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.dto.DriverImportDTO;
import com.izu.user.dto.DriverManageNewDTO;
import com.izu.user.dto.DriverNewDTO;
import com.izu.user.dto.ImportErrorDTO;
import com.izu.user.dto.driver.*;
import com.izu.user.dto.provider.driver.DriverDropdownReqDTO;
import com.izu.user.dto.provider.driver.DriverDropdownRespDTO;
import com.izu.user.dto.provider.driver.QueryDriverProviderCondition;
import com.izu.user.dto.staff.pc.ClientCompany;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.enums.DriverOfficeStatusEnum;
import com.izu.user.enums.DriverSexEnum;
import com.izu.user.enums.perm.ProviderDataPermTypeEnum;
import com.izu.user.util.ObjectTransferUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping
@Api(tags = "司机管理运营端")
public class DriverListProviderController extends AbstractExcelUploadController<DriverImportDTO, AbstractExcelUploadController.ExcelUploadResult> {

    @Value("${file.upload.dir}")
    private String path;

    @Value("${mrcar-user-core.host.url}")
    private String USER_HOST_URL;
    private static final Logger logger = LoggerFactory.getLogger(DriverController.class);
    @Resource
    private ClientUserManageImportStaffService clientUserManageImportStaffService;
    public static final String LICENSE_TYPE = "A1,A2,A3,B1,B2,C1,C2,C3,C4,C5,D,E,F,M,N,P";

    @Override
    protected List<Integer> getResolveSheetIndexes() {
        return Collections.singletonList(1);
    }

    @Override
    protected String getColumnMappingConfigFile(HttpServletRequest request, HttpServletResponse response) {
        return null;
    }

    @Override
    protected List<DriverImportDTO> convertStringDataToObject(List<Map<Integer, String>> rowDatas, ExcelUploadResult excelUploadResult) {
        List<Map<Integer, String>> collect = rowDatas.stream().filter(row -> !checkFieldAllNull(row)).collect(Collectors.toList());
        return collect.stream().map(row -> {
            DriverImportDTO dto = new DriverImportDTO();
            dto.setDriverName(row.get(0));
            dto.setDriverMobile(row.get(1));
            dto.setGenderStr(row.get(2));
            dto.setOfficeStatusStr(row.get(3));
            dto.setBrithDate(row.get(4));
            dto.setCertNo(row.get(5));
            dto.setLicenseType(row.get(6));
            dto.setFirstPickupTime(row.get(7));
            dto.setIssuingOrgan(row.get(8));
            dto.setArriveTime(row.get(9));
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 校验对象属性是否全为空，不适用于继承的对象
     *
     * @param obj
     * @return
     */
    private boolean checkFieldAllNull(Object obj) {
        Field[] declaredFields = obj.getClass().getDeclaredFields();
        boolean flag = true;
        if (obj instanceof Map) {
            Map<Integer, String> map = ObjectTransferUtil.cast(obj);
            for (Map.Entry entry : map.entrySet()) {
                if (!ObjectUtils.isEmpty(entry.getValue())) {
                    flag = false;
                    break;
                }
            }
            return flag;
        }
        for (Field field : declaredFields) {
            field.setAccessible(true);
            Object o = null;
            try {
                o = field.get(obj);
            } catch (IllegalAccessException e) {
                log.error("校验对象属性值异常");
            }
            if (!ObjectUtils.isEmpty(o)) {
                flag = false;
                break;
            }
        }
        return flag;
    }

    @Override
    protected List<ExcelUploadRowError> checkExcelData(List<DriverImportDTO> rowDatas, HttpServletRequest request, HttpServletResponse response, ExcelUploadResult excelUploadResult) {
        List<ExcelUploadRowError> errorList = new ArrayList<>();
        for (int i = 0; i < rowDatas.size(); i++) {
            DriverImportDTO driverImportDTO = rowDatas.get(i);
/*			if(checkFieldAllNull(driverImportDTO)){
				continue;
			}*/
            String error = "";
            int rowNum = i + 1;

            if (StringUtils.isEmpty(driverImportDTO.getDriverName())) {
                error = error + "司机姓名不能为空,";
            }
            if (StringUtils.isBlank(driverImportDTO.getDriverMobile())) {
                error = error + "手机号不能为空";
            } else {
                Pattern pattern = Pattern.compile("^[1]\\d{10}$");
                if (!pattern.matcher(driverImportDTO.getDriverMobile()).matches()) {
                    error = error + "手机号格式错误,";
                }
            }
            if (StringUtils.isBlank(driverImportDTO.getLicenseType())) {
                error = error + "准驾车型不能为空";
            } else {
                if (!DriverController.LICENSE_TYPE.contains(driverImportDTO.getLicenseType())) {
                    error = error + "准驾车型不合法,请输入正确的准驾车型";
                }
            }
            if (StringUtils.isBlank(driverImportDTO.getGenderStr())) {
                error = error + "司机性别不能为空";
            } else {
                if (!DriverController.GenderType.contains(driverImportDTO.getGenderStr().trim())) {
                    error = error + "司机性别不合法,请输入男或女";
                }
            }
            if (StringUtils.isBlank(driverImportDTO.getGenderStr())) {
                error = error + "工作状态不能为空";
            } else {
                if (DriverOfficeStatusEnum.getKeyByValue(driverImportDTO.getOfficeStatusStr().trim()) == 0) {
                    error = error + "工作状态不合法,请输入正确的工作状态";
                }
            }
            if (StringUtils.isBlank(driverImportDTO.getBrithDate())) {
                error = error + "出生日期不能为空";
            } else {
                if (!DriverController.check(driverImportDTO.getBrithDate().trim())) {
                    error = error + "出生日期不合法,请输入正确格式的日期";
                }
            }
           /* if (StringUtils.isBlank(driverImportDTO.getCertNo())) {
                error = error + "身份证号不能为空";
            } else {
                if (DriverController.IDCardpattern.matcher(driverImportDTO.getCertNo().trim()).matches()) {
                    error = error + "身份证号不合法,请输入正确格式的身份证号";
                }
            }*/
            if (StringUtils.isBlank(driverImportDTO.getLicenseType())) {
                error = error + "准驾车型不能为空";
            } else {
                if (!DriverController.LICENSE_TYPE.contains(driverImportDTO.getLicenseType().trim())) {
                    error = error + "准驾车型不合法,请输入正确格式的准驾车型";
                }
            }
            if (StringUtils.isBlank(driverImportDTO.getFirstPickupTime())) {
                error = error + "驾照初领日期不能为空";
            } else {
                if (!DriverController.check(driverImportDTO.getFirstPickupTime().trim())) {
                    error = error + "驾照初领日期不合法,请输入正确格式的日期";
                }
            }
            if (StringUtils.isBlank(driverImportDTO.getIssuingOrgan())) {
                error = error + "发证机关不能为空";
            }
            if (StringUtils.isBlank(driverImportDTO.getArriveTime())) {
                error = error + "驾驶证有效期不能为空";
            } else {
                if (!DriverController.check(driverImportDTO.getArriveTime().trim())) {
                    error = error + "驾驶证有效期不合法,请输入正确格式的日期";
                }
            }

            if (StringUtils.isNotEmpty(error)) {
                if (error.lastIndexOf(",") + 1 == error.length()) {
                    error = error.substring(0, error.length() - 1);
                }
                ExcelUploadRowError rowError = new ExcelUploadRowError(rowNum, error);
                errorList.add(rowError);
            }
        }
        return errorList;
    }

    @Override
    protected boolean beforePersist(List<DriverImportDTO> rowDatas, HttpServletRequest request, HttpServletResponse response, ExcelUploadResult excelUploadResult) {
        return true;
    }

    @Override
    protected RestResponse executePersist(List<DriverImportDTO> rowDatas, HttpServletRequest request, HttpServletResponse response, ExcelUploadResult excelUploadResult) {
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        Map<String, Object> paramMap = new HashMap<>(4);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, rowDatas);
        paramMap.put("createId", loginBaseInfo.obtainBaseInfo().getStaffId());
        paramMap.put("createName", loginBaseInfo.obtainBaseInfo().getStaffName());
        paramMap.put("companyId", loginBaseInfo.obtainBelongCompanyId());
        String restUrl = UserRestLocator.instance.getRestUrl(UserUrlCenter.PROVIDER_DRIVER_BATCH_INSERT);
        RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, ImportErrorDTO.class);
        log.info("【batchInsert】响应response={}", JSON.toJSONString(restResponse));
        if (restResponse != null && restResponse.isSuccess()) {
            List<ExcelUploadRowError> errorList = new ArrayList<>();
            List<ImportErrorDTO> list = ObjectTransferUtil.cast(restResponse.getData());
            if (!CollectionUtils.isEmpty(list)) {
                list.forEach(error -> {
                    ExcelUploadRowError excelUploadRowError = new ExcelUploadRowError(error.getRowNum(), error.getReason());
                    errorList.add(excelUploadRowError);
                });
                excelUploadResult.setCheckErrorMessages(errorList);
                excelUploadResult.setResolvedRows(rowDatas.size());
                excelUploadResult.setPersistSuccessRows(0);
                excelUploadResult.setPersistFailedRows(rowDatas.size());
                excelUploadResult.setErrorMessage("数据未通过校验，请确认后重新上传");
                return this.fail(null, request, response, excelUploadResult);
            }
            excelUploadResult.setPersistSuccessRows(rowDatas.size());
            return this.success(null, request, response, excelUploadResult);
        } else {
            if (restResponse != null) {
                excelUploadResult.setErrorMessage(restResponse.getMsg());
            }
            return this.fail(null, request, response, excelUploadResult);
        }
    }

    @PostMapping("/provider/driver/export")
    @JrdApiDoc(simpleDesc = "司机导出", author = "hhd", resDataClass = Objects.class)
    @ExportExcelWeb(fileName = ExportExcelConstants.FILE_NAME_DRIVER_LIST_EXPORT_INFO ,filePath = ExportExcelConstants.FILE_PATH,
            sheet = ExportExcelConstants.SHEET_NAME_DRIVER_LIST_EXPORT_INFO, c = DriverListExportRespDTO.class)
    public PageDTO<DriverListExportRespDTO> export(@RequestBody QueryDriverProviderCondition reqDTO, IzuEasyExcelSession izuEasyExcelSession,
                                                   HttpServletRequest request, HttpServletResponse response) {
        //获取运营端会话信息
        ProviderLoginInfo loginUser = LoginSessionUtil.getProviderLoginInfo();
        Byte dataPermType = loginUser.obtainSimpleDataPerm().getDataPermType();
        reqDTO.setDataPermType(loginUser.obtainSimpleDataPerm().getDataPermType());
        if (ProviderDataPermTypeEnum.ASSIGN_CUSTOMER.getType().equals(dataPermType) || ProviderDataPermTypeEnum.RESPONSIBLE_CUSTOMER.getType().equals(dataPermType)) {
            reqDTO.setDataPermSet(loginUser.obtainSimpleDataPerm().getDataCodeSet());
        } else {
            reqDTO.setDataPermSet(loginUser.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet());
        }
        String url = new UserRestLocator().getRestUrl(UserUrlCenter.PROVIDER_DRIVER_LIST_COMPANY_DRIVER_PAGE);
        izuEasyExcelSession.setUserName(loginUser.obtainBaseInfo().getStaffName());
        reqDTO.setPageNo(izuEasyExcelSession.getPageNo());
        reqDTO.setPageSize(1000);
        final HashMap<String, Object> restParam = Maps.newHashMapWithExpectedSize(1);
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        RestResponse<PageDTO<DriverNewDTO>> res = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, restParam, null, DriverNewDTO.class);
        if(null!=res && res.isSuccess()){
            PageDTO<DriverNewDTO> pageDTO = res.getData();
            List<DriverListExportRespDTO> driverListExportRespDTOS = BeanUtil.copyList(pageDTO.getResult(), DriverListExportRespDTO.class);
            return new PageDTO<>(pageDTO.getPage(), pageDTO.getPageSize(), pageDTO.getTotal(), driverListExportRespDTOS);
        }else {
            return null;
        }
    }


    @RequestMapping("/provider/driver/bindCarTodriver")
    @RequestFunction(functionName = "司机分配车辆")
    public RestResponse getDriverDetail(Integer driverId,Integer bindVehicleId){
        String restUrl = USER_HOST_URL + "driver/bindCarTodriver";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("driverId", driverId);
        paramMap.put("bindVehicleId", bindVehicleId);
        RestResponse restResponse = RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, paramMap, null);
        return restResponse;

    }

    @PostMapping("/provider/driver/addOrUpdDriver")
    @RequestFunction(functionName = "保存司机")
    public RestResponse addOrUpdDriver(@RequestBody DriverNewDTO driverDto){
        String restUrl = USER_HOST_URL + "driver/addOrUpdDriver";
        ProviderLoginInfo loginUser = LoginSessionUtil.getProviderLoginInfo();
        Map<String, Object> paramMap = new HashMap<>();
        if(null!=driverDto.getDriverId()){
            driverDto.setUpdateId(loginUser.obtainBaseInfo().getStaffId());
            driverDto.setUpdateName(loginUser.obtainBaseInfo().getStaffName());
        }else {
            driverDto.setCreterName(loginUser.obtainBaseInfo().getStaffName());
            driverDto.setCreterId(loginUser.obtainBaseInfo().getStaffId());
        }
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, driverDto);
        RestResponse restResponse = RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, Object.class);
        return restResponse;

    }

    /**
     * 启用停用司机
     * @return
     */
    @RequestMapping("/provider/driver/changeDriverStatus")
    @RequestFunction(functionName = "启用/停用司机")
    public RestResponse changeDriverStatus(@RequestBody DriverUpdateStateReqDTO driverUpdateStateReqDTO) {
        ProviderLoginInfo loginUser = LoginSessionUtil.getProviderLoginInfo();
        driverUpdateStateReqDTO.setLoginUserId(loginUser.obtainBaseInfo().getStaffId());
        driverUpdateStateReqDTO.setLoginUserName(loginUser.obtainBaseInfo().getStaffName());
        Map<String,Object> paraMap = new HashMap<>();
        paraMap.put(BaseHttpClient.POSTBODY_MAP_KEY, driverUpdateStateReqDTO);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, USER_HOST_URL + "driver/changeDriverStatus", paraMap, null, Object.class);
    }


    /**
     * 为司机分配车队
     * @param paraMap
     * @param request
     * @return
     */
    @RequestMapping("/provider/driver/setMotorcadeForDriver")
    @RequestFunction(functionName = "司机分配车队")
    public RestResponse setMotorcadeForDriver(@RequestBody Map<String,Object> paraMap, HttpServletRequest request) {
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, USER_HOST_URL + "driver/setMotorcadeForDriver", paraMap, null, Object.class);
    }

    @PostMapping("/provider/driver/downloadDriverTemplate")
    @RequestFunction(functionName = "下载司机导入模板")
    public void downloadDriverTemplate(@Verify(param = "companyId",rule = "required") Integer companyId,HttpServletRequest request, HttpServletResponse response)throws Exception {
        // 导出模板名称
        String fileName = "司机导入模板";
        response.setContentType("application/msexcel");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码
        String fileNameEncode = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileNameEncode + ExcelTypeEnum.XLSX.getValue());
        // 设置下拉框内容
        Map<Integer, List<String>> selectMap = buildSelectMap(companyId);
        //表头
        List<MaintainImportDTO> list = new ArrayList<>();
        EasyExcelFactory.write(response.getOutputStream())
                .registerWriteHandler(new CustomCellWriteHandler())
                .registerWriteHandler(new DbCarExcelSheetWriteHandler(Sets.newHashSet(6, 9)))
                // 设置字典
                .registerWriteHandler(new SelectDataSheetMergeWriteHandler(selectMap))
                // 设置行高度
                .registerWriteHandler(new SimpleRowHeightStyleStrategy((short) 35, (short) 22))
                // 此处对应的是实体类
                .head(DriverImportTemplateDTO.class)
                // 设置导出格式为xls后缀
                .excelType(ExcelTypeEnum.XLSX)
                .sheet("导入司机")
                .doWrite(list);
    }

    @PostMapping(value = "/provider/driver/batchInsertV2")
    @RequestFunction(functionName = "批量导入司机")
    @JrdApiDoc(simpleDesc = "上传Excel批量更新", detailDesc = "", author = "hhd", resDataDesc = "是否成功，失败时返回错误的行数和错误信息。", resDataClass = ImportErrorDTO.class)
    public RestResponse batchInsert(
            @Verify(param = "excelUrl", rule = "required") String excelUrl,
            @Verify(param="companyId",rule ="required") Integer companyId,
            HttpServletRequest request,
            HttpServletResponse response) {
        String restUrl = new UserRestLocator().getRestUrl("/driver/batchInsertV2");
        Map<String, Object> paraMap = new HashMap<>(1);
        ProviderLoginInfo loginUser = LoginSessionUtil.getProviderLoginInfo();
        DriverImportReqDTO reqDTO = new DriverImportReqDTO();
        reqDTO.setLoginCompanyId(companyId);
        reqDTO.setExcelUrl(excelUrl);
        reqDTO.setLoginUserId(loginUser.getBaseInfo().getStaffId());
        reqDTO.setLoginUserName(loginUser.getBaseInfo().getStaffName());
        paraMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paraMap, null, BatchDTO.class);
    }

    @PostMapping("/provider/driver/listMotorcade")
    @RequestFunction(functionName = "车队列表")
    public RestResponse listMotorcade(@RequestParam(value = "companyId") Integer companyId) {
        String restUrl = USER_HOST_URL + "driver/listMotorcade";
        Map<String,Object> paraMap = new HashMap<>();
        try {
            paraMap.put("company_id", companyId);
            RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Map.class);
            return restResponse;
        } catch(Exception e) {
            logger.error("listMotorcade Exception：", e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }

    /**
     * 根据企业id 查询该企业下的所有建立车队的城市
     * @param
     * @return
     */
    @RequestMapping("/provider/driver/getMotorcadeCity")
    public RestResponse getMotorcadeCity(@RequestParam(value = "companyId") Integer companyId) {
        Map<String,Object> paraMap = new HashMap<>();
        paraMap.put("companyId", companyId);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POST, USER_HOST_URL + "driver/getMotorcadeCity", paraMap, null, Map.class);
    }
    private Map<Integer, List<String>> buildSelectMap(Integer companyId) {
        Map<Integer, List<String>> selectMap = new HashMap<>();
        // 部门列表
        List<String> departmentNames = clientUserManageImportStaffService.getDeptNames(companyId);
        selectMap.put(2, departmentNames);
        // 性别
        List<String> genders = DriverSexEnum.getAllSex().stream().map(DriverSexEnum::getName).collect(Collectors.toList());
        selectMap.put(3, genders);
        // 准驾车型
        List<String> licenseTypes = Arrays.stream(LICENSE_TYPE.split(","))
                .map(String::trim) // 去除空格
                .collect(Collectors.toList());
        selectMap.put(5, licenseTypes);
        return selectMap;
    }

    @PostMapping("/provider/car/getCarByLicense")
    public RestResponse getCarByLicense(@RequestBody Map<String, Object> paraMap, HttpServletRequest request) {
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.SEARCH_CAR_URL), paraMap, null);
    }
}
