package com.izu.mrcar.providerController.driver;

import com.izu.config.dto.SchedulingDTO;
import com.izu.config.dto.SchedulingDayDTO;
import com.izu.config.dto.SchedulingSettingDTO;
import com.izu.config.dto.SettingDTO;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.config.RestUrlConfig;
import com.izu.mrcar.controller.MrCarHttpPostBody;
import com.izu.mrcar.controller.SchedulingRoleController;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;

@RestController
@RequestMapping("/provider/schedulingDetail")
@Api(tags = "司机管理")
public class ProviderSchedulingDetailController {
    private static final Logger logger = LoggerFactory.getLogger(SchedulingRoleController.class);


    @Autowired
    private RestUrlConfig restUrlConfig;

    @RequestMapping(value = "/updateStatus",method = {RequestMethod.POST})
    @ApiOperation(value = "车队状态修改",notes = "作者：连江伟")
    public RestResponse updateStatus(@RequestBody Map<String, Object> paraMap, HttpServletRequest request) {
        String restUrl = restUrlConfig.getUserCoreUrl() + "provider/schedulingDetail/updateStatus";

        try {
            LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
            int companyId = loginBaseInfo.obtainBelongCompanyId();
            paraMap.put("companyId", companyId);
            paraMap.put("updateId", loginBaseInfo.obtainBaseInfo().getStaffId());
            paraMap.put("updateName", loginBaseInfo.obtainBaseInfo().getStaffName());
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Object.class);
        } catch (Exception e) {
            logger.error("updateStatus Exception：", e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }

    /**
     * 查询车队下的所有正常且启用的排班规则
     * @param paraMap
     * @param request
     * @return
     */
    @RequestMapping(value = "/roleListForMotorcade",method = {RequestMethod.POST})
    @RequestFunction(functionName = "排班规则查询")
    @ApiOperation(value = "车队排班规则查询",notes = "作者：连江伟")
    public RestResponse listForPage(@RequestBody Map<String, Object> paraMap, HttpServletRequest request) {
        String restUrl = restUrlConfig.getConfigCoreUrl() + "schedulingRole/getListForMotorcade";

        try {
//			获取企业id
            LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
            int companyId = loginBaseInfo.obtainBelongCompanyId();
            paraMap.put("companyId", companyId);
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Object.class);
        } catch (Exception e) {
            logger.error("listForPage Exception：", e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }

    @RequestMapping(value = "/detail",method = {RequestMethod.POST})
    @RequestFunction(functionName = "车队排班详情")
    @ApiOperation(value = "车队排班详情",notes = "作者：连江伟")
    public RestResponse detail(@RequestBody Map<String, Object> paraMap, HttpServletRequest request) {
        String restUrl = restUrlConfig.getUserCoreUrl() + "provider/schedulingDetail/detail";

        try {
//			获取企业id
            LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
            int companyId = loginBaseInfo.obtainBelongCompanyId();
            paraMap.put("companyId", companyId);
            paraMap.put("createId", loginBaseInfo.obtainBaseInfo().getStaffId());
            paraMap.put("createName", loginBaseInfo.obtainBaseInfo().getStaffName());
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Object.class);
        } catch (Exception e) {
            logger.error("detail Exception：", e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }

    @RequestMapping(value = "/detailExport",method = {RequestMethod.POST})
    @RequestFunction(functionName = "车队排班导出")
    @ApiOperation(value = "车队排班导出",notes = "作者：连江伟")
    public void detailExport(@RequestBody Map<String, Object> paraMap, HttpServletRequest request, HttpServletResponse response) {
        String restUrl = restUrlConfig.getUserCoreUrl() + "provider/schedulingDetail/detailForExport";
        try {
//			获取企业id
            LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
            int companyId = loginBaseInfo.obtainBelongCompanyId();
            paraMap.put("companyId", companyId);
            paraMap.put("createId", loginBaseInfo.obtainBaseInfo().getStaffId());
            paraMap.put("createName", loginBaseInfo.obtainBaseInfo().getStaffName());
            RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, SchedulingSettingDTO.class);

            List<SchedulingSettingDTO> setting = (List<SchedulingSettingDTO>) restResponse.getData();

            String dateTime = (String) paraMap.get("dateTime");
            String[] split = dateTime.split("-");
            Calendar instance = Calendar.getInstance(Locale.CHINA);
            instance.set(Integer.parseInt(split[0]), Integer.parseInt(split[1]) - 1, 1);
            int year = instance.get(Calendar.YEAR);
            int month = instance.get(Calendar.MONTH) + 1;
            int maxDay = instance.getActualMaximum(Calendar.DATE);

            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet();
            int rowNum = 0;

            //第一行所有星期
            Row row1 = sheet.createRow(rowNum);
            Cell cell1 = row1.createCell(0);
            cell1.setCellValue("");
            for (int i = 1; i <= maxDay; i++) {
                sheet.autoSizeColumn(i);
                Cell cell2 = row1.createCell(i);
                instance.set(year, month - 1, i);
                int day = instance.get(Calendar.DAY_OF_WEEK);
                String dayStr;
                switch (day) {
                    case 1:
                        dayStr = "周日";
                        break;
                    case 2:
                        dayStr = "周一";
                        break;
                    case 3:
                        dayStr = "周二";
                        break;
                    case 4:
                        dayStr = "周三";
                        break;
                    case 5:
                        dayStr = "周四";
                        break;
                    case 6:
                        dayStr = "周五";
                        break;
                    case 7:
                        dayStr = "周六";
                        break;
                    default:
                        dayStr = "";
                }
                cell2.setCellValue(dayStr);
            }
            //第二行所有日期
            Row row2 = sheet.createRow(++rowNum);
            Cell cell2 = row2.createCell(0);
            cell2.setCellValue("");
            for (int dayNum = 1; dayNum <= maxDay; dayNum++) {
                Cell cell3 = row2.createCell(dayNum);
                cell3.setCellValue(dayNum + "号");
            }
            int j = ++rowNum;
            for (SchedulingSettingDTO schedulingSettingDTO : setting) {
                if(schedulingSettingDTO.getSchedulingDayDTOs().isEmpty()){
                    continue;
                }
                Row row3 = sheet.createRow(j);
                j++;
                List<SchedulingDayDTO> schedulingDayDTOs = schedulingSettingDTO.getSchedulingDayDTOs();
                Cell cell3 = row3.createCell(0);
                cell3.setCellValue(schedulingSettingDTO.getDriveName());
                for (int dayIndex = 1,schedulingIndex = 0; dayIndex <= maxDay && schedulingIndex < schedulingDayDTOs.size(); dayIndex++) {
                    Cell cell4 = row3.createCell(dayIndex);
                    SchedulingDayDTO schedulingDayDTO = schedulingDayDTOs.get(schedulingIndex);
                    if (schedulingDayDTO.getDayOfMonth().equals(""+dayIndex)){
                        SchedulingDTO schedulingDTO = schedulingDayDTO.getSchedulingDTO();
                        cell4.setCellValue(schedulingDTO.getStart() + "-" + schedulingDTO.getEnd());
                        schedulingIndex += 1;
                    }else {
                        cell4.setCellValue("");
                    }
                }
            }
            workbook.write(response.getOutputStream());
            workbook.close();

        } catch (Exception e) {
            logger.info("detailExport Exception：", e);
        }
    }

    @RequestMapping(value = "/setting",method = {RequestMethod.POST})
    @RequestFunction(functionName = "排班规则设置")
    @ApiOperation(value = "车队排班保存",notes = "作者：连江伟")
    public RestResponse setting(@RequestBody SettingDTO settingDTO, HttpServletRequest request) {
        String restUrl = restUrlConfig.getUserCoreUrl() + "provider/schedulingDetail/setting";

        try {
            //获取参数
            List<SchedulingSettingDTO> details = settingDTO.getDetails();
            Integer motorcadeId = settingDTO.getMotorcadeId();

//			获取企业id
            LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
            int companyId = loginBaseInfo.obtainBelongCompanyId();
            //修改车队排班状态
            Map updMap = new HashMap();
            updMap.put("companyId", companyId);
            updMap.put("updateId", loginBaseInfo.obtainBaseInfo().getStaffId());
            updMap.put("updateName", loginBaseInfo.obtainBaseInfo().getStaffName());
            updMap.put("schduleStatus", settingDTO.getSchduleStatus());
            updMap.put("motorcadeId", motorcadeId);
            String restUrlForUpdate = restUrlConfig.getUserCoreUrl() + "provider/schedulingDetail/updateStatus";
            RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrlForUpdate, updMap, null, Object.class);

            details.forEach(o -> {
                o.setCompanyId(companyId);
                o.setCreateId(loginBaseInfo.obtainBaseInfo().getStaffId());
                o.setCreateName(loginBaseInfo.obtainBaseInfo().getStaffName());
            });
            return MrCarHttpPostBody.requestBodyForObject(restUrl, details, null, Object.class);
        } catch (Exception e) {
            logger.info("setting Exception：", e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }

    @RequestMapping("getServerTime")
    public RestResponse getServerTime() {
        LocalDate localDate = LocalDate.now(ZoneId.systemDefault());
        return RestResponse.success(localDate.toString());
    }

    @RequestMapping(value = "/getCountDetailByDriverId",method = {RequestMethod.POST})
    @ApiOperation(value = "当前司机未来排班日的数量",notes = "作者：连江伟")
    public RestResponse getCountDetailByDriverId(@RequestBody Map<String, Object> paraMap, HttpServletRequest request) {
        String restUrl = restUrlConfig.getUserCoreUrl() + "provider/schedulingDetail/getCountDetailByDriverId";
        try {
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Object.class);
        } catch (Exception e) {
            logger.error("detail Exception：", e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }
}
