package com.izu.mrcar.providerController.listfield;

import com.google.common.collect.Maps;
import com.izu.config.dto.ListFieldConfigReq;
import com.izu.config.dto.ListFieldConfigRespDTO;
import com.izu.consts.ConfigURI;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.config.RestUrlConfig;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;


@Slf4j
@RestController
public class ListFieldConfigController {

    @PostMapping(ConfigURI.PROVIDER_LIST_FIELD_CONFIG_LIST)
    @RequestFunction(functionName = "列表配置")
    @ApiOperation(value = "列表配置")
    public RestResponse<List<ListFieldConfigRespDTO>> getList(@RequestBody ListFieldConfigReq param) {
        String restUrl = RestUrlConfig.getConfig().getConfigCoreUrl() + ConfigURI.PROVIDER_LIST_FIELD_CONFIG_LIST;
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, ListFieldConfigRespDTO.class);
    }
}