package com.izu.mrcar.providerController.listfield;

import com.google.common.collect.Maps;
import com.izu.config.dto.*;
import com.izu.consts.ConfigURI;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.config.RestUrlConfig;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;


@Slf4j
@RestController
public class ProviderFieldConfigDetailController {

    @PostMapping(ConfigURI.PROVIDER_FIELD_CONFIG_DETAIL)
    @RequestFunction(functionName = "列表字段配置-详情")
    @ApiOperation(value = "列表字段配置详情")
    public RestResponse<FieldConfigDetailRespDTO> getDetail(@RequestBody FieldConfigDetailReq param) {
        String restUrl = RestUrlConfig.getConfig().getConfigCoreUrl() + ConfigURI.FIELD_CONFIG_DETAIL;
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null);
    }

    @PostMapping(ConfigURI.PROVIDER_FIELD_CONFIG_SAVE)
    @RequestFunction(functionName = "列表字段配置-保存")
    @ApiOperation(value = "列表字段配置保存")
    public RestResponse<Void> saveConfig(@RequestBody FieldConfigDetailSaveReq param) {
        String restUrl = RestUrlConfig.getConfig().getConfigCoreUrl() + ConfigURI.FIELD_CONFIG_SAVE;
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        AccountBaseInfo baseInfo = LoginSessionUtil.getProviderLoginInfo().getBaseInfo();
        param.setCreateId(baseInfo.getStaffId());
        param.setUpdateId(baseInfo.getStaffId());
        param.setCreateName(baseInfo.getStaffName());
        param.setUpdateName(baseInfo.getStaffName());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null);
    }



    @PostMapping(ConfigURI.PROVIDER_FIELD_CONFIG_OPERATE_FIELD)
    @RequestFunction(functionName = "列表字段配置-新增/删除字段")
    @ApiOperation(value = "列表字段配置-新增/删除字段")
    public RestResponse<Void> operateField(@RequestBody FieldConfigDetailAddFieldReq param) {
        String restUrl = RestUrlConfig.getConfig().getConfigCoreUrl() + ConfigURI.PROVIDER_FIELD_CONFIG_OPERATE_FIELD;
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        AccountBaseInfo baseInfo = LoginSessionUtil.getProviderLoginInfo().getBaseInfo();
        param.setUpdateId(baseInfo.getStaffId());
        param.setUpdateName(baseInfo.getStaffName());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null);
    }
}