package com.izu.mrcar.providerController.iot;

import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.iot.config.IotCoreRestMsgCenter;
import com.izu.mrcar.iot.config.IotRestLocator;
import com.izu.mrcar.iot.dto.CarWarnFenceDTO;
import com.izu.mrcar.iot.dto.input.WarnDealSaveDTO;
import com.izu.mrcar.iot.dto.input.WarnFenceQueryDTO;
import com.izu.mrcar.iot.dto.iot.ProviderGetWarnFenceByIdReqDTO;
import com.izu.mrcar.iot.dto.iot.req.ProviderFenceWarnDetailReqDTO;
import com.izu.mrcar.iot.dto.output.FenceWarnDetailDTO;
import com.izu.mrcar.service.order.WarnFenceExportService;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.ProviderDataPermUtil;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2024/8/6 16:12
 */
@Slf4j
@RestController
@RequestMapping("/provider/warn")
@Api(tags = "运营端-围栏报警")
public class ProviderWarnController {


    @Value("${mrcar-iot-core.host.url}")
    private String IOT_HOST_URL;


    @Resource
    private WarnFenceExportService warnFenceExportService;


    @PostMapping("/listWarnFence")
//    @RequiresPermissions(value = "fence_alert")
    @RequestFunction(functionName = "围栏报警列表")
    @ApiOperation(value = "围栏报警列表", notes = "作者：叶鹏鹏")
    public RestResponse<PageDTO<CarWarnFenceDTO>> listWarnFence(@RequestBody WarnFenceQueryDTO dto) {
        try {
            // 需要进行数据校验 TODO
            String restUrl = IOT_HOST_URL + "provider/warn/listWarnFenceForPage";
            // setPermData(dto);
            Map<String, Object> params = new HashMap<>(1);
            params.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
            return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, params, null, CarWarnFenceDTO.class);
        } catch(Exception e) {
            log.error("listWarnFence Exception：", e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }

    @PostMapping("/listWarnFence/export")
//    @RequiresPermissions(value = "fence_alert")
    @RequestFunction(functionName = "围栏报警信息导出")
    @ApiOperation(value = "围栏报警信息导出", notes = "作者：叶鹏鹏")
    public void listWarnFenceExport(@RequestBody WarnFenceQueryDTO dto, IzuEasyExcelSession izuEasyExcelSession, HttpServletResponse response) {
        try {
            // 需要进行数据校验
            // setPermData(dto);
            dto.setPage(1);
            dto.setPageSize(2000);
            izuEasyExcelSession.setPageNo(1);
            warnFenceExportService.listWarnFenceExport(dto, izuEasyExcelSession, response);
        } catch (Exception e) {
            log.warn("provider listWarnFenceExport Exception：", e);
        }
    }

    private void setPermData(WarnFenceQueryDTO dto){
        // 查询需要改造
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        AccountBaseInfo baseInfo = providerLoginInfo.obtainBaseInfo();
        dto.setSystemType(providerLoginInfo.getSystemType());
        dto.setStaffId(baseInfo.getStaffId());
        dto.setStaffCode(baseInfo.getStaffCode());
        dto.setDataPermType(providerLoginInfo.getProviderDataPerm().getDataPermType());
        dto.setDataCodeSet(ProviderDataPermUtil.getDatePermScope(providerLoginInfo));
        dto.setDataPermIsNotNull(CollectionUtils.isNotEmpty(dto.getDataCodeSet()));
        dto.setLoginCompanyId(providerLoginInfo.obtainBelongCompanyId());
    }

    @PostMapping("/deal")
    @ApiOperation(value = "报警处理", notes = "作者：叶鹏鹏")
    @RequestFunction(functionName = "报警处理")
    public RestResponse<Boolean> dealWarn(@RequestBody WarnDealSaveDTO dealSaveDTO){
        // 需要增加参数校验 TODO
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        dealSaveDTO.setDealId(providerLoginInfo.obtainBaseInfo().getStaffId());
        dealSaveDTO.setDealName(providerLoginInfo.obtainBaseInfo().getStaffName());

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, dealSaveDTO);
        String restUrl = new IotRestLocator().getRestUrl(IotCoreRestMsgCenter.WARN_DEAL);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, Boolean.class);
    }

    @PostMapping("/fence/getDetail")
    @RequestFunction(functionName = "围栏报警详情")
    @ApiOperation(value = "围栏报警详情", notes = "作者：叶鹏鹏")
    public RestResponse<FenceWarnDetailDTO> getDetail(@RequestBody ProviderFenceWarnDetailReqDTO reqDTO){
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("id", reqDTO.getId());
        String restUrl = new IotRestLocator().getRestUrl(IotCoreRestMsgCenter.FENCE_WARN_DETAIL);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paramMap, null, FenceWarnDetailDTO.class);
    }
}
