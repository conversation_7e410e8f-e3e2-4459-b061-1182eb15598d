package com.izu.mrcar.providerController.iot;

import com.alibaba.fastjson.JSON;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.exception.RestErrorException;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.iot.config.IotCoreRestMsgCenter;
import com.izu.mrcar.iot.config.IotRestLocator;
import com.izu.mrcar.iot.dto.tbox.TBoxStatusDTO;
import com.izu.mrcar.iot.dto.tbox.TboxLogReqDTO;
import com.izu.mrcar.providerController.benefit.TBoxStatusExport;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.util.ObjectTransferUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 查询车机日志相关接口
 * <AUTHOR> ON 2023/2/18.
 */
@RestController
@Api(tags = "车辆监控")
public class ProviderTboxRecordController {

    /**
     * 查询车机日志
     * 默认查询近7天内的数据
     * @param tboxLogReqDTO
     * @return
     */
    @RequestFunction(functionName = "车辆监控-车机日志查询")
    @ApiOperation("查询车机日志")
    @PostMapping(IotCoreRestMsgCenter.PROVIDER_CAR_STATUS_LIST)
    public RestResponse<PageDTO<List<TBoxStatusDTO>>> getCarTboxLog(@RequestBody TboxLogReqDTO tboxLogReqDTO){
        String url = new IotRestLocator().getRestUrl(IotCoreRestMsgCenter.CAR_STATUS_LIST);
        Map<String,Object> param = JSON.parseObject(JSON.toJSONString(tboxLogReqDTO));
        return RestClient.requestInside(BaseHttpClient.HttpMethod.GET,url,param,null);
    }

    @ApiOperation("车机日志导出")
    @PostMapping(IotCoreRestMsgCenter.PROVIDER_CAR_STATUS_LIST_EXPORT)
    @ExportExcelWeb(fileName = ExportExcelConstants.TBOX_STATUS_EXPORT+ ExportExcelConstants.XLSX,filePath = ExportExcelConstants.FILE_PATH,
            sheet = ExportExcelConstants.TBOX_STATUS_EXPORT, c = TBoxStatusExport.class)
    public PageDTO<TBoxStatusExport> export(@RequestBody TboxLogReqDTO tboxLogReqDTO, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response) {
        LoginBaseInfo baseInfo = LoginSessionUtil.getProviderLoginInfo();
        izuEasyExcelSession.setUserName(baseInfo.obtainBaseInfo().getStaffName());
        String url = new IotRestLocator().getRestUrl(IotCoreRestMsgCenter.CAR_STATUS_LIST);
        Map<String,Object> param = JSON.parseObject(JSON.toJSONString(tboxLogReqDTO));
        param.put("page", izuEasyExcelSession.getPageNo());
        param.put("pageSize", ExportExcelConstants.PAGE_SIZE);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.GET,url,param,null,TBoxStatusExport.class);
        if(restResponse.isSuccess()){
            PageDTO<TBoxStatusExport> pageDTO = ObjectTransferUtil.cast(restResponse.getData());
            if (pageDTO.getTotal()> ExportExcelConstants.EXPORT_MAX_LINE){
                throw new RestErrorException("导出条数超限制", ErrorCode.EXPORT_LIMITED_WARN);
            }
            return pageDTO;
        }else {
            return null;
        }
    }

}
