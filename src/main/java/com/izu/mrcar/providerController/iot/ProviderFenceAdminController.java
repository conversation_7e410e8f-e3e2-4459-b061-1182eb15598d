package com.izu.mrcar.providerController.iot;

import com.google.common.collect.Maps;
import com.izu.config.MrCarConfigRestLocator;
import com.izu.consts.ConfigURI;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.config.enums.BussinessTypeDictionary;
import com.izu.mrcar.iot.dto.CarGpsFenceDTO;
import com.izu.mrcar.iot.dto.CarGpsFenceInputDTO;
import com.izu.mrcar.iot.dto.CarGpsFenceRegionalDTO;
import com.izu.mrcar.iot.dto.CarGpsFenceVehicleDTO;
import com.izu.mrcar.iot.dto.car.VehicleSimpleDTO;
import com.izu.mrcar.iot.dto.iot.ProviderAddFenceVehicleReqDTO;
import com.izu.mrcar.iot.dto.iot.ProviderCarGpsFenceReqDTO;
import com.izu.mrcar.iot.dto.iot.ProviderGetSelfCarConfigReqDTO;
import com.izu.mrcar.iot.dto.iot.ProviderListFenceByVehicleId;
import com.izu.mrcar.iot.dto.iot.ProviderListFenceReqDTO;
import com.izu.mrcar.iot.dto.iot.ProviderListFenceVehicleReqDTO;
import com.izu.mrcar.iot.dto.iot.ProviderListRegionalismReqDTO;
import com.izu.mrcar.iot.dto.iot.ProviderListVehicleByFenceIdReqDTO;
import com.izu.mrcar.iot.dto.iot.ProviderQryFenceDetailReqDTO;
import com.izu.mrcar.iot.dto.iot.ProviderStartOrStopFenceReqDTO;
import com.izu.mrcar.iot.dto.iot.resp.ListFenceVehicleRespDTO;
import com.izu.mrcar.utils.ClientDataPermUtil;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.ProviderDataPermUtil;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.entity.Company;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/6 09:26
 */
@Slf4j
@Controller
@RestController
@RequestMapping("/provider/fence")
@Api(tags = "运营端-围栏设置")
public class ProviderFenceAdminController {

    @Value("${mrcar-iot-core.host.url}")
    private String IOT_HOST_URL;

    @Value("${mrcar-user-core.host.url}")
    private String USER_HOST_URL;

    @PostMapping("/queryFencePageList")
//    @RequiresPermissions(value = "fence_alert")
    @ApiOperation(value = "围栏列表", notes = "作者：叶鹏鹏")
    @RequestFunction(functionName = "围栏列表")
    public RestResponse<PageDTO<CarGpsFenceDTO>> queryFencePageList(@RequestBody ProviderListFenceReqDTO reqDTO) {
        try {

            String restUrl = IOT_HOST_URL + "provider/fence/queryFencePageList";
            ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
            reqDTO.setStaffId(providerLoginInfo.obtainBaseInfo().getStaffId());
            reqDTO.setDataPermType(providerLoginInfo.obtainDataPerm().getDataPermType());
            Set<String> dataPermSet = ProviderDataPermUtil.getDatePermScope(providerLoginInfo);
            if(CollectionUtils.isNotEmpty(dataPermSet)){
                reqDTO.setDataScope(String.join(",", dataPermSet));
            }
            Map<String, Object> paraMap = transQueryFencePageListParaMap(reqDTO);
            RestResponse<PageDTO<CarGpsFenceDTO>> restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POST,restUrl,paraMap,null, CarGpsFenceDTO.class);

            if(restResponse.isSuccess() && Objects.nonNull(restResponse.getData()) && CollectionUtils.isNotEmpty(restResponse.getData().getResult())){
                List<Integer> companyIds = restResponse.getData().getResult().stream().
                        map(fence -> fence.getCompanyId()).distinct().collect(Collectors.toList());
                StringBuilder companyIdsStrBuilder = new StringBuilder();
                companyIds.forEach(companyId -> companyIdsStrBuilder.append(companyId).append(","));
                String companyIdStr = companyIdsStrBuilder.toString();

                Map<Integer, String> companyNameMap = getCompanyNameMap(companyIdStr, companyIds.size());
                restResponse.getData().getResult().forEach(fence -> {
                    if(Objects.nonNull(companyNameMap.get(fence.getCompanyId()))){
                        fence.setCompanyName(companyNameMap.get(fence.getCompanyId()));
                    }
                });
            }
            return restResponse;
        } catch(Exception e) {
            log.error("provider queryFencePageList Exception：", e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }
    private Map<String, Object> transQueryFencePageListParaMap(ProviderListFenceReqDTO reqDTO) {
        Map<String, Object> paraMap = new HashMap<String, Object>();
        paraMap.put("fenceName", reqDTO.getFenceName());
        paraMap.put("fenceType", reqDTO.getFenceType());
        paraMap.put("warnType", reqDTO.getWarnType());
        paraMap.put("fenceStatus", reqDTO.getFenceStatus());
        paraMap.put("pageNo", reqDTO.getPageNo());
        paraMap.put("pageSize", reqDTO.getPageSize());
        paraMap.put("companyId", reqDTO.getCompanyId());
        paraMap.put("regionalismCode", reqDTO.getRegionalismCode());
        paraMap.put("regionalismName", reqDTO.getRegionalismName());
        paraMap.put("dataPermType", reqDTO.getDataPermType());
        paraMap.put("dataScope", reqDTO.getDataScope());
        paraMap.put("staffId", reqDTO.getStaffId());
        paraMap.put("structId", reqDTO.getStructId());
        paraMap.put("fenceBusinessType", reqDTO.getFenceBusinessType());
        return paraMap;
    }

    @PostMapping("/queryFenceDetail")
    @ApiOperation(value = "围栏详情", notes = "作者：叶鹏鹏")
    @RequestFunction(functionName = "围栏详情")
    public RestResponse<CarGpsFenceDTO> queryFenceDetail(@RequestBody ProviderQryFenceDetailReqDTO qryFenceDetailReqDTO) {
        try {
            // 底层方法未用到 companyId TODO
            String restUrl = IOT_HOST_URL + "fence/queryFenceDetail";
            Map<String, Object> paraMap = new HashMap<String, Object>();
            paraMap.put("fenceId", qryFenceDetailReqDTO.getFenceId());
            paraMap.put("companyId", qryFenceDetailReqDTO.getCompanyId());

            RestResponse<CarGpsFenceDTO> restResponse =
                    RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, CarGpsFenceDTO.class);
            if(restResponse.isSuccess() && Objects.nonNull(restResponse.getData())){
                Integer companyId = restResponse.getData().getCompanyId();
                Map<Integer, String> companyNameMap = getCompanyNameMap(companyId + "", 1);
                if(Objects.nonNull(companyNameMap.get(companyId))){
                    restResponse.getData().setCompanyName(companyNameMap.get(companyId));
                }
            }
            return restResponse;
        } catch(Exception e) {
            log.error("provider queryFenceDetail Exception：" + e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }

    private Map<Integer, String> getCompanyNameMap(String companyIdStr, int companyIdSize){
        Map<String, Object> reMap = new HashMap<>();
        reMap.put("companyIdStr", companyIdStr);
        reMap.put("pageNo", 1);
        reMap.put("pageSize", companyIdSize);
        RestResponse<PageDTO<Company>> companyPageResp =
                RestClient.requestForPage(BaseHttpClient.HttpMethod.POST, USER_HOST_URL + "companyManage/listForConfigService", reMap, null, Company.class);
        if(companyPageResp.isSuccess() && Objects.nonNull(companyPageResp.getData()) && CollectionUtils.isNotEmpty(companyPageResp.getData().getResult())){
            Map<Integer, String> companyNameMap =
                    companyPageResp.getData().getResult().stream().collect(Collectors.toMap(Company::getCompanyId, Company::getCompanyName, (c1, c2) -> c1));
            return companyNameMap;
        }
        return Maps.newHashMap();
    }

    @PostMapping("/getFenceVehicleListByFenceId")
    @ApiOperation(value = "根据围栏id分页查询指派信息", notes = "作者：叶鹏鹏")
    @RequestFunction(functionName = "根据围栏id分页查询指派信息")
    public RestResponse<PageDTO<CarGpsFenceVehicleDTO>> getFenceVehicleListByFenceId(@RequestBody ProviderListVehicleByFenceIdReqDTO reqDTO) {
        try {
            String restUrl = IOT_HOST_URL + "fence/getFenceVehicleListByFenceId";
            Map<String, Object> paraMap = new HashMap<String, Object>();
            paraMap.put("fenceId", reqDTO.getFenceId());
            paraMap.put("pageNo", reqDTO.getPageNo());
            paraMap.put("pageSize", reqDTO.getPageSize());
            paraMap.put("companyId", reqDTO.getCompanyId());
            return RestClient.requestForPage(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, CarGpsFenceVehicleDTO.class);
        } catch(Exception e) {
            log.error("provider getFenceVehicleList Exception：" + e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }

    }

    @PostMapping("/getCheckedVehicleList")
    @ApiOperation(value = "登录用户看到的选中车辆", notes = "作者：叶鹏鹏")
    @RequestFunction(functionName = "登录用户看到的选中车辆")
    public RestResponse<List<VehicleSimpleDTO>> getCheckedVehicleList(@RequestBody ProviderListFenceVehicleReqDTO reqDTO) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();

        String restUrl = IOT_HOST_URL + "fence/getCheckedVehicleList";
        Map<String, Object> paraMap = new HashMap<String, Object>();
        paraMap.put("fenceId", reqDTO.getFenceId());
        paraMap.put("companyId", reqDTO.getCompanyId());
        Set<String> dataPermSet = ProviderDataPermUtil.getDatePermScope(providerLoginInfo);
        if(CollectionUtils.isNotEmpty(dataPermSet)){
            paraMap.put("dataScope", String.join(",", dataPermSet));
        }
        paraMap.put("dataPermType",providerLoginInfo.obtainDataPerm().getDataPermType());
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, VehicleSimpleDTO.class);
    }

    @PostMapping("/getRegionalismList")
    @ApiOperation(value = "获取行政区下拉信息", notes = "作者：叶鹏鹏")
    @RequestFunction(functionName = "获取行政区下拉信息")
    public RestResponse<List<CarGpsFenceRegionalDTO>> getRegionalismList(@RequestBody ProviderListRegionalismReqDTO reqDTO) {
        try{
            Map<String, Object> paraMap = new HashMap<>();
            paraMap.put("fenceBusinessType", reqDTO.getFenceBusinessType());
            String restUrl = IOT_HOST_URL + "provider/fence/getRegionalismList";
            return RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, CarGpsFenceRegionalDTO.class);
        } catch(Exception e) {
            log.error("getRegionalismList Exception：" + e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }


    @PostMapping("/createOrUpdate")
//    @RequiresPermissions(value = {"fence_create","fence_modify"},logical = Logical.OR)
    @ApiOperation(value = "围栏设置-创建或编辑", notes = "作者：叶鹏鹏")
    @RequestFunction(functionName = "围栏设置-创建或编辑")
    public RestResponse<Boolean> createOrUpdate(@RequestBody ProviderCarGpsFenceReqDTO reqDTO) {
        try {
            ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
            reqDTO.setOperateId(providerLoginInfo.obtainBaseInfo().getStaffId());
            reqDTO.setOperateName(providerLoginInfo.obtainBaseInfo().getStaffName());
            CarGpsFenceInputDTO carGpsFenceInputDTO = new CarGpsFenceInputDTO();
            BeanUtils.copyProperties(reqDTO, carGpsFenceInputDTO);

            String restUrl = IOT_HOST_URL + "fence/createOrUpdate";
            Map<String, Object> paraMap = new HashMap<>();
            paraMap.put(BaseHttpClient.POSTBODY_MAP_KEY, carGpsFenceInputDTO);
            return RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paraMap, null, Map.class);
        } catch(Exception e) {
            log.error("provider createOrUpdate Exception：" + e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }

    }

    @PostMapping("/startOrStopFence")
    @ApiOperation(value = "启用或停用围栏", notes = "作者：叶鹏鹏")
    @RequestFunction(functionName = "启用或停用围栏")
    public RestResponse<Boolean> startOrStopFence(@RequestBody ProviderStartOrStopFenceReqDTO reqDTO) {
        try {
            ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
            reqDTO.setOperateId(providerLoginInfo.obtainBaseInfo().getStaffId());
            reqDTO.setOperateName(providerLoginInfo.obtainBaseInfo().getStaffName());

            String restUrl = IOT_HOST_URL + "fence/startOrStopFence";
            Map<String, Object> paraMap = new HashMap<>();
            paraMap.put("fenceId", reqDTO.getFenceId());
            paraMap.put("fenceStatus", reqDTO.getFenceStatus());
            paraMap.put("comment", reqDTO.getComment());
            paraMap.put("operateId", reqDTO.getOperateId());
            paraMap.put("operateName", reqDTO.getOperateName());
            // 底层未用到 companyId
            paraMap.put("companyId", reqDTO.getCompanyId());
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Map.class);
        } catch(Exception e) {
            log.error("provider startOrStopFence Exception：" + e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }

    }

    @PostMapping("/queryFenceVehicleList")
    @ApiOperation(value = "围栏指派车辆查询", notes = "作者：叶鹏鹏")
    @RequestFunction(functionName = "围栏指派车辆查询")
    public RestResponse<PageDTO<ListFenceVehicleRespDTO>> queryFenceVehicleList(@RequestBody ProviderListFenceVehicleReqDTO reqDTO) {
        try {
            ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
            String restUrl = IOT_HOST_URL + "provider/fence/queryFenceVehicleList";
            Map<String, Object> paraMap = new HashMap<String, Object>();
            paraMap.put("fenceId", reqDTO.getFenceId());
            paraMap.put("vehicleLicense", reqDTO.getVehicleLicense());
            paraMap.put("vehicleVin", reqDTO.getVehicleVin());
            paraMap.put("pageNo", reqDTO.getPageNo());
            paraMap.put("pageSize", reqDTO.getPageSize());
            paraMap.put("companyId", reqDTO.getCompanyId());
            Set<String> dataPermSet = ProviderDataPermUtil.getDatePermScope(providerLoginInfo);
            if(CollectionUtils.isNotEmpty(dataPermSet)){
                paraMap.put("dataScope", String.join(",", dataPermSet));
            }
            paraMap.put("structId",reqDTO.getStructId());
            paraMap.put("cityCode",reqDTO.getCityCode());
            return RestClient.requestForPage(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, ListFenceVehicleRespDTO.class);
        } catch(Exception e) {
            log.error("provider queryFenceVehicleList Exception：" + e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }

    }

    @PostMapping("/insertFenceVehicle")
//    @RequiresPermissions(value = "assign_car")
    @ApiOperation(value = "添加围栏指派车辆", notes = "作者：叶鹏鹏")
    @RequestFunction(functionName = "添加围栏指派车辆")
    public RestResponse<Boolean> insertFenceVehicle(@RequestBody ProviderAddFenceVehicleReqDTO reqDTO, HttpServletRequest request) {
        try {
            ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
            reqDTO.setOperateId(providerLoginInfo.obtainBaseInfo().getStaffId());
            reqDTO.setOperateName(providerLoginInfo.obtainBaseInfo().getStaffName());
            String restUrl = IOT_HOST_URL + "fence/insertFenceVehicle";
            Map<String, Object> paraMap = new HashMap<String, Object>();
            paraMap.put("fenceId", reqDTO.getFenceId());
            paraMap.put("assignComment", reqDTO.getAssignComment());
            paraMap.put("vehicleIds", reqDTO.getVehicleIds());
            paraMap.put("operateId", reqDTO.getOperateId());
            paraMap.put("operateName", reqDTO.getOperateName());
            paraMap.put("companyId", reqDTO.getCompanyId());
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Map.class);
        } catch(Exception e) {
            log.error("provider insertFenceVehicle Exception：" + e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }

    /**
     * 这个接口可能用不上 --- TODO
     * @param reqDTO
     * @return
     */
//    @PostMapping("/queryVehicleFenceList")
//    @ApiOperation(value = "根据车辆ID查询围栏列表", notes = "作者：叶鹏鹏")
//    @RequestFunction(functionName = "根据车辆ID查询围栏列表")
//    public RestResponse<List<CarGpsFenceDTO>> queryVehicleFenceList(@RequestBody ProviderListFenceByVehicleId reqDTO) {
//        try {
//            // companyId 需要传递, 如果不传，需要改底层逻辑 TODO
//            Map<String, Object> paraMap = new HashMap<>();
//            paraMap.put("vehicleId", reqDTO.getVehicleId());
//            paraMap.put("companyId", reqDTO.getCompanyId());
//            String restUrl = IOT_HOST_URL + "fence/queryVehicleFenceList";
//            return RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, CarGpsFenceDTO.class);
//        } catch(Exception e) {
//            log.error("queryFenceDetail Exception：" + e);
//            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
//        }
//    }

    // 这个接口底层返回的是个对象，强转为boolean类型
    @PostMapping("/getSelfCarConfig")
    @ApiOperation(value = "获取指定企业自助取还配置", notes = "作者：叶鹏鹏")
    @RequestFunction(functionName = "获取指定企业自助取还配置")
    public RestResponse<Boolean> getSelfCarConfig(@RequestBody ProviderGetSelfCarConfigReqDTO reqDTO){
        Map<String, Object> restParam = new HashMap<>();
        // companyId 需要传递 TODO
        restParam.put("companyId", reqDTO.getCompanyId());
        restParam.put("businessCode", BussinessTypeDictionary.SELF_OPERATE_FENCE.getValue());
        final String restUrl = new MrCarConfigRestLocator().getRestUrl(ConfigURI.CONFIG_COMPANY_BUSINESS_GET_BY_BUSINESS_CODE);
        return  RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, restParam, null, Boolean.class);
    }


}
