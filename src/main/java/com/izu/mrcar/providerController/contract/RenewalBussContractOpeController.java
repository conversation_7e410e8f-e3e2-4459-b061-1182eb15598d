package com.izu.mrcar.providerController.contract;

import com.izu.business.config.BusinessRestLocator;
import com.izu.business.consts.resturl.MrcarBusinessRestMsgCenter;
import com.izu.business.dto.provider.input.RenewalBussContractOpeInputDTO;
import com.izu.business.dto.provider.output.RenewalBussContractOpeDTO;
import com.izu.business.dto.provider.output.RenewalBussContractOpeDetailDTO;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.shiro.session.WebSessionUtil;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.LoginUser;
import com.izu.user.dto.staff.pc.DataPerm;
import com.izu.user.dto.staff.pc.DataPermInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.enums.perm.ProviderDataPermTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@RestController
@Api(tags = "运营端")
@Slf4j
public class RenewalBussContractOpeController {

    @PostMapping(MrcarBusinessRestMsgCenter.PROVIDER_RENEWAL_BUSS_CONTRACT_LIST)
    @RequestFunction(functionName = "续租协议分页列表")
    @ApiOperation(value = "续租协议分页列表",notes = "作者：mapp")
    public RestResponse<PageDTO<RenewalBussContractOpeDTO>> getList(@RequestBody RenewalBussContractOpeInputDTO condition){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        condition.setStaffId(providerLoginInfo.getBaseInfo().getStaffId());
        condition.setStaffCode(providerLoginInfo.getBaseInfo().getStaffCode());
        condition.setMgtStaffId(providerLoginInfo.getBaseInfo().getMgtStaffId());
        condition.setDataPermType(providerLoginInfo.obtainDataPerm().getDataPermType());
        condition.setDataPermSet(getDataScopeList(providerLoginInfo));
        String url = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.PROVIDER_RENEWAL_BUSS_CONTRACT_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, condition);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, RenewalBussContractOpeDTO.class);
    }

    @GetMapping(MrcarBusinessRestMsgCenter.PROVIDER_RENEWAL_BUSS_CONTRACT_DETAIL)
    @ApiImplicitParam(name = "bussContractCode", value = "合同编号", required = true)
    @RequestFunction(functionName = "续租协议详情")
    @ApiOperation(value = "续租协议详情",notes = "作者：mapp")
    public RestResponse<RenewalBussContractOpeDetailDTO> getDetail(@Verify(param = "bussContractCode",rule="required")String bussContractCode ){
        String url = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.PROVIDER_RENEWAL_BUSS_CONTRACT_DETAIL);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("bussContractCode", bussContractCode);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.GET, url, paramMap, null, RenewalBussContractOpeDetailDTO.class);
    }

    /**
     * 将运营端的会话信息中数据权限转为具体的业务参数集合
     * @param providerLoginInfo
     * @return
     */
    private Set<String> getDataScopeList(ProviderLoginInfo providerLoginInfo){
        Set<String> res = null;
        if(ProviderDataPermTypeEnum.ALL.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())){
            return null;
        }else if(ProviderDataPermTypeEnum.RESPONSIBLE_CONTRACT.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())){
            res = providerLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet();
        }else if(ProviderDataPermTypeEnum.RESPONSIBLE_CUSTOMER.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())||
                ProviderDataPermTypeEnum.ASSIGN_CUSTOMER.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())){
            res = providerLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet();
        }else if(ProviderDataPermTypeEnum.SELF_DEPT.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())||
                ProviderDataPermTypeEnum.ASSIGN_DEPT.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())){
            res = providerLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet();
        }
        return res;
    }
}
