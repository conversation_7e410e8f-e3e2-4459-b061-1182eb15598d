package com.izu.mrcar.providerController.contract;

import com.izu.business.config.BusinessRestLocator;
import com.izu.business.config.errorcode.BusinessErrorCode;
import com.izu.business.consts.resturl.MrcarBusinessRestMsgCenter;
import com.izu.business.dto.provider.input.BussContractOpeInputDTO;
import com.izu.business.dto.provider.output.BussContractOpeDTO;
import com.izu.business.dto.provider.output.BussContractOpeDetailDTO;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.shiro.session.WebSessionUtil;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.LoginUser;
import com.izu.user.dto.staff.pc.DataPerm;
import com.izu.user.dto.staff.pc.DataPermInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.enums.perm.ProviderDataPermTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

@RestController
@Api(tags = "运营端")
@Slf4j
public class BussContractOpeController {
    @Autowired
    @Qualifier("rest-client-pool")
    private ExecutorService executorService;

    @PostMapping(MrcarBusinessRestMsgCenter.PROVIDER_BUSS_CONTRACT_PAGE_LIST)
    @RequestFunction(functionName = "业务合同分页列表")
    @ApiOperation(value = "业务合同分页列表",notes = "作者：mapp")
    public RestResponse<PageDTO<BussContractOpeDTO>> getList(@Valid @RequestBody BussContractOpeInputDTO param){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        param.setStaffId(providerLoginInfo.getBaseInfo().getStaffId());
        param.setStaffCode(providerLoginInfo.getBaseInfo().getStaffCode());
        param.setMgtStaffId(providerLoginInfo.getBaseInfo().getMgtStaffId());
        param.setDataPermType(providerLoginInfo.obtainDataPerm().getDataPermType());
        param.setDataPermSet(getDataScopeList(providerLoginInfo));
        String url = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.PROVIDER_BUSS_CONTRACT_PAGE_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, BussContractOpeDTO.class);

    }

    /**
     * 长租业务合同详情
     * @param bussContractCode
     * @return
     */
    @GetMapping(MrcarBusinessRestMsgCenter.PROVIDER_BUSS_CONTRACT_DETAIL)
    @ApiImplicitParam(name = "bussContractCode", value = "合同编号", required = true)
    @RequestFunction(functionName = "业务合同详情")
    @ApiOperation(value = "业务合同详情",notes = "作者：mapp")
    public RestResponse<BussContractOpeDetailDTO> getDetail(@Verify(param = "bussContractCode",rule="required")String bussContractCode) {

        BussContractOpeDetailDTO bussContractDetailDTO = null;
        try {
            // 查询合同基本信息
            CompletableFuture<BussContractOpeDetailDTO> contractDetailDTO = CompletableFuture.supplyAsync(() -> {
                String url = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.PROVIDER_BUSS_CONTRACT_DETAIL);
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("bussContractCode", bussContractCode);
                RestResponse restResponse = RestClient.requestForObject(BaseHttpClient.HttpMethod.GET, url, paramMap, null, BussContractOpeDetailDTO.class);
                return (BussContractOpeDetailDTO) restResponse.getData();
            }, executorService);

            // 查询订单信息
            CompletableFuture<List<BussContractOpeDetailDTO.BussOrderOpe>> bussOrderList = CompletableFuture.supplyAsync(() -> {
                String url = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.CONTRACT_ORDER_QUERY_BY_CONTRACT_CODE);
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("bussContractCode", bussContractCode);
                RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.GET, url, paramMap, null, BussContractOpeDetailDTO.BussOrderOpe.class);
                return (List<BussContractOpeDetailDTO.BussOrderOpe>) restResponse.getData();
            }, executorService);

            // 查询账单信息
            CompletableFuture<List<BussContractOpeDetailDTO.OrderBillOpe>> orderBillList = CompletableFuture.supplyAsync(() -> {
                String url = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.BILL_INFO_LIST_QUERY_BY_CONTRACT_CODE);
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("bussContractCode", bussContractCode);
                RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.GET, url, paramMap, null, BussContractOpeDetailDTO.OrderBillOpe.class);
                return (List<BussContractOpeDetailDTO.OrderBillOpe>) restResponse.getData();
            }, executorService);

            // 汇总数据
            bussContractDetailDTO = CompletableFuture.allOf(contractDetailDTO, bussOrderList, orderBillList).thenApplyAsync((v) -> {
                try {
                    BussContractOpeDetailDTO contractDetail = contractDetailDTO.get();
                    List<BussContractOpeDetailDTO.BussOrderOpe> bussOrders = bussOrderList.get();
                    List<BussContractOpeDetailDTO.OrderBillOpe> orderBills = orderBillList.get();
                    contractDetail.setBussOrderOpeList(bussOrders);
                    if (bussOrders == null || orderBills == null) {
                        contractDetail.setOrderBillOpeList(orderBills);
                        return contractDetail;
                    }
                    Map<String, List<BussContractOpeDetailDTO.BussOrderOpe>> bussOrderMap = bussOrders.stream().collect(Collectors.groupingBy(BussContractOpeDetailDTO.BussOrderOpe::getOrderCode));
                    orderBills.forEach(orderBill -> {
                        List<BussContractOpeDetailDTO.BussOrderOpe> bussOrderL = bussOrderMap.get(orderBill.getOrderCode());
                        if (bussOrderL != null) {
                            BussContractOpeDetailDTO.BussOrderOpe order = bussOrderL.get(0);
                            orderBill.setVehicleLicense(order.getVehicleLicense());
                            orderBill.setVehicleModelName(order.getVehicleModelName());
                            orderBill.setOrderStatus(order.getOrderStatus());
                            orderBill.setOrderStatusStr(order.getOrderStatusStr());
                        }
                    });
                    contractDetail.setOrderBillOpeList(orderBills);
                    return contractDetail;
                } catch (InterruptedException e) {
                    log.error("补充账单中的订单车辆信息异常(运营端)", e);
                    Thread.currentThread().interrupt();
                    return null;
                } catch (ExecutionException e) {
                    log.error("补充账单中的订单车辆信息异常(运营端)", e);
                    return null;
                }
            }, executorService).get();

        } catch (InterruptedException e) {
            log.error("运营端查询合同详情异常", e);
            Thread.currentThread().interrupt();
        } catch (ExecutionException e) {
            log.error("运营端查询合同详情异常", e);
        }

        return bussContractDetailDTO==null? RestResponse.fail(BusinessErrorCode.CONTRACT_DETAIL_ERROR):RestResponse.success(bussContractDetailDTO);
    }

    /**
     * 将运营端的会话信息中数据权限转为具体的业务参数集合
     * @param providerLoginInfo
     * @return
     */
    private Set<String> getDataScopeList(ProviderLoginInfo providerLoginInfo){
        Set<String> res = null;
        if(ProviderDataPermTypeEnum.ALL.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())){
            return null;
        }else if(ProviderDataPermTypeEnum.RESPONSIBLE_CONTRACT.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())){
            res = providerLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet();
        }else if(ProviderDataPermTypeEnum.RESPONSIBLE_CUSTOMER.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())||
                ProviderDataPermTypeEnum.ASSIGN_CUSTOMER.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())){
            res = providerLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet();
        }else if(ProviderDataPermTypeEnum.SELF_DEPT.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())||
                ProviderDataPermTypeEnum.ASSIGN_DEPT.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())){
            res = providerLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet();
        }
        return res;
    }
}
