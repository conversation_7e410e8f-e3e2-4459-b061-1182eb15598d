package com.izu.mrcar.providerController.contract;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.google.common.collect.Maps;
import com.izu.business.config.BusinessRestLocator;
import com.izu.business.consts.resturl.MrcarBusinessRestMsgCenter;
import com.izu.business.dto.mrcarUseContract.input.*;
import com.izu.business.dto.mrcarUseContract.output.BillingInfoExportResDTO;
import com.izu.business.dto.mrcarUseContract.output.BillingInfoPageResDTO;
import com.izu.business.dto.mrcarUseContract.output.BillingInfoResDTO;
import com.izu.business.dto.mrcarUseContract.output.ContractInfoResDTO;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.doc.JrdApiDoc;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.dto.crmCustomer.CrmCustomerQueryReqDTO;
import com.izu.user.dto.staff.pc.*;
import com.izu.user.enums.perm.ProviderDataPermTypeEnum;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;


@RestController
@Api(tags = "运营端-账单管理")
@Slf4j
public class MrCarBillingInfoController {
    private static final Integer PAGE_SIZE = 10000;

    @JrdApiDoc(simpleDesc = "账单管理新建", author = "hhd", resDataClass = Object.class)
    @PostMapping(value = MrcarBusinessRestMsgCenter.MRCAR_USE_CONTRACT_BILLING_ADJUST_CREATE)
    @RequestFunction(functionName = "账单管理新建")
    public RestResponse create(@RequestBody @Validated BillingInfoCreateReqDTO reqDTO) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        reqDTO.setLoginUserId(providerLoginInfo.getBaseInfo().getStaffId());
        reqDTO.setLoginUserName(providerLoginInfo.getBaseInfo().getStaffName());
        reqDTO.setDataPermType(providerLoginInfo.obtainDataPerm().getDataPermType());
        String restUrl = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.MRCAR_USE_CONTRACT_BILLING_ADJUST_CREATE);
        Map<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }

    @PostMapping(value = MrcarBusinessRestMsgCenter.MRCAR_USE_CONTRACT_BILLING_EXPORT)
    @JrdApiDoc(simpleDesc = "账单管理导出", author = "hhd", resDataClass = BillingInfoExportResDTO.class)
    @RequestFunction(functionName = "账单管理导出")
    @ExportExcelWeb(fileName = ExportExcelConstants.FILE_NAME_BILL_LIST_EXPORT_INFO ,filePath = ExportExcelConstants.FILE_PATH,
            sheet = ExportExcelConstants.SHEET_NAME_BILL_LIST_EXPORT_INFO, c = BillingInfoExportResDTO.class)
    public PageDTO<BillingInfoExportResDTO> export(@RequestBody BillingInfoQueryReqDTO reqDTO, IzuEasyExcelSession izuEasyExcelSession,
                                                           HttpServletRequest request, HttpServletResponse response) {
        String restUrl = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.MRCAR_USE_CONTRACT_BILLING_LIST);
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        //数据权限不等于所有并且所属部门不等于总部的
        AtomicBoolean flag= new AtomicBoolean(false);
        List<String> companyIds = new ArrayList<>();
        if(ProviderDataPermTypeEnum.ALL.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())){
            flag.set(true);
        }
        if(ProviderDataPermTypeEnum.SELF_DEPT.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType()) &&
                CollUtil.isNotEmpty(providerLoginInfo.getProviderDataPerm().getDataPermInfoList())){
            List<DataPermInfo> dataPermInfoList = providerLoginInfo.getProviderDataPerm().getDataPermInfoList();
            //查询所属部门
            String url = new UserRestLocator().getRestUrl(UserUrlCenter.PROVIDER_HEAD_DEPARTMENT_LIST);
            RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, url, null, null, String.class);
            if(restResponse!=null && restResponse.isSuccess() && restResponse.getData()!=null){
                List<String> list = (List<String>) restResponse.getData();
                dataPermInfoList.forEach(x->{
                    if(list.contains(x.getDataPermCode())){
                        flag.set(true);
                    }
                    companyIds.add(String.valueOf(x.getDataPermCode()));
                });
            }
        }
        List<String>  customerCodeList = new ArrayList<>();
        if(!flag.get()){
            //先查询用户是否存在数据权限
            String userUrl = new UserRestLocator().getRestUrl(UserUrlCenter.GET_CUSTOMER_CODE_LIST_BY_STAFF_COMPANY);
            CrmCustomerQueryReqDTO dto = new CrmCustomerQueryReqDTO();
            dto.setOwnerOrCreateOrUpdateId(providerLoginInfo.getBaseInfo().getMgtStaffId());
            if(Objects.equals(providerLoginInfo.obtainDataPerm().getDataPermType(),ProviderDataPermTypeEnum.SELF_DEPT.getType())){
                dto.setCompanyIds(companyIds);
            }
            Map<String, Object> param = new HashMap<>();
            param.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
            RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, userUrl, param, null, String.class);
            if(restResponse!=null && restResponse.isSuccess() && restResponse.getData()!=null){
                customerCodeList=(List<String>) restResponse.getData();
            }
            reqDTO.setUserId(String.valueOf(providerLoginInfo.getBaseInfo().getStaffId()));
        }

        reqDTO.setCustomerCodeList(customerCodeList);
        izuEasyExcelSession.setUserName(providerLoginInfo.getBaseInfo().getStaffName());
        reqDTO.setPage(izuEasyExcelSession.getPageNo());
        reqDTO.setPageSize(PAGE_SIZE);

        final HashMap<String, Object> restParam = Maps.newHashMapWithExpectedSize(1);
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        RestResponse<PageDTO<BillingInfoPageResDTO>> res = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null, BillingInfoPageResDTO.class);
        if(null!=res && res.isSuccess()){
            PageDTO<BillingInfoPageResDTO> pageDTO = res.getData();
            List<BillingInfoExportResDTO> list = BeanUtil.copyList(pageDTO.getResult(),BillingInfoExportResDTO.class);
            return new PageDTO<>(pageDTO.getPage(), pageDTO.getPageSize(), pageDTO.getTotal(), list);
        }else {
            return null;
        }
    }

    @JrdApiDoc(simpleDesc = "账单列表", author = "hhd", resDataClass = BillingInfoPageResDTO.class)
    @PostMapping(value = MrcarBusinessRestMsgCenter.MRCAR_USE_CONTRACT_BILLING_LIST)
    @RequestFunction(functionName = "账单列表")
    public RestResponse pageList(@RequestBody BillingInfoQueryReqDTO reqDTO) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        //数据权限不等于所有并且所属部门不等于总部的
        AtomicBoolean flag= new AtomicBoolean(false);
        List<String> companyIds = new ArrayList<>();
        if(ProviderDataPermTypeEnum.ALL.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())){
            flag.set(true);
        }
        if(ProviderDataPermTypeEnum.SELF_DEPT.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType()) &&
                CollUtil.isNotEmpty(providerLoginInfo.getProviderDataPerm().getDataPermInfoList())){
            List<DataPermInfo> dataPermInfoList = providerLoginInfo.getProviderDataPerm().getDataPermInfoList();
            //查询所属部门
            String url = new UserRestLocator().getRestUrl(UserUrlCenter.PROVIDER_HEAD_DEPARTMENT_LIST);
            RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, url, null, null, String.class);
            if(restResponse!=null && restResponse.isSuccess() && restResponse.getData()!=null){
                List<String> list = (List<String>) restResponse.getData();
                dataPermInfoList.forEach(x->{
                    if(list.contains(x.getDataPermCode())){
                        flag.set(true);
                    }
                    companyIds.add(String.valueOf(x.getDataPermCode()));
                });
            }
        }
        List<String>  customerCodeList = new ArrayList<>();
        if(!flag.get()){
            //先查询用户是否存在数据权限
            String userUrl = new UserRestLocator().getRestUrl(UserUrlCenter.GET_CUSTOMER_CODE_LIST_BY_STAFF_COMPANY);
            CrmCustomerQueryReqDTO dto = new CrmCustomerQueryReqDTO();
            dto.setOwnerOrCreateOrUpdateId(providerLoginInfo.getBaseInfo().getMgtStaffId());
            if(Objects.equals(providerLoginInfo.obtainDataPerm().getDataPermType(),ProviderDataPermTypeEnum.SELF_DEPT.getType())){
                dto.setCompanyIds(companyIds);
            }
            Map<String, Object> param = new HashMap<>();
            param.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
            RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, userUrl, param, null, String.class);
            if(restResponse!=null && restResponse.isSuccess() && restResponse.getData()!=null){
                customerCodeList=(List<String>) restResponse.getData();
            }
            reqDTO.setUserId(String.valueOf(providerLoginInfo.getBaseInfo().getStaffId()));
        }

        reqDTO.setCustomerCodeList(customerCodeList);
        reqDTO.setLoginUserId(providerLoginInfo.getBaseInfo().getStaffId());
        reqDTO.setLoginUserName(providerLoginInfo.getBaseInfo().getStaffName());
        reqDTO.setDataPermType(providerLoginInfo.obtainDataPerm().getDataPermType());
        String restUrl = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.MRCAR_USE_CONTRACT_BILLING_LIST);
        Map<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null,BillingInfoPageResDTO.class);
    }

    @JrdApiDoc(simpleDesc = "账单详情", author = "hhd", resDataClass = BillingInfoResDTO.class)
    @PostMapping(value = MrcarBusinessRestMsgCenter.MRCAR_USE_CONTRACT_BILLING_DETAIL)
    @RequestFunction(functionName = "账单详情")
    public RestResponse detail(@RequestParam(name = "id") Integer id) {
        String restUrl = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.MRCAR_USE_CONTRACT_BILLING_DETAIL);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("id", id);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.GET, restUrl, paramMap, null, BillingInfoResDTO.class);
    }

    /**
     * 账单废弃
     *
     * @param dto 入参
     * @return RestResponse
     */
    @PostMapping(MrcarBusinessRestMsgCenter.MRCAR_USE_CONTRACT_BILLING_CANCEL)
    public RestResponse cancel(@RequestBody BillingInfoOpReqDTO dto){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        dto.setLoginUserId(providerLoginInfo.getBaseInfo().getStaffId());
        dto.setLoginUserName(providerLoginInfo.getBaseInfo().getStaffName());
        dto.setDataPermType(providerLoginInfo.obtainDataPerm().getDataPermType());
        String restUrl = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.MRCAR_USE_CONTRACT_BILLING_CANCEL);
        Map<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }
    /**
     * 账单推送
     *
     * @param dto 入参
     * @return 账单信息
     */
    @PostMapping(MrcarBusinessRestMsgCenter.MRCAR_USE_CONTRACT_BILLING_SEND)
    public RestResponse send(@RequestBody BillingInfoOpReqDTO dto){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        dto.setLoginUserId(providerLoginInfo.getBaseInfo().getStaffId());
        dto.setLoginUserName(providerLoginInfo.getBaseInfo().getStaffName());
        dto.setDataPermType(providerLoginInfo.obtainDataPerm().getDataPermType());
        String restUrl = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.MRCAR_USE_CONTRACT_BILLING_SEND);
        Map<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }
    /**
     * 账单调价
     *
     * @param dto 账单dto
     * @return 账单信息
     */
    @PostMapping(MrcarBusinessRestMsgCenter.MRCAR_USE_CONTRACT_BILLING_ADJUST_AMOUNT)
    public RestResponse adjustAmount(@RequestBody BillingInfoAdjustAmountReqDTO dto){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        dto.setLoginUserId(providerLoginInfo.getBaseInfo().getStaffId());
        dto.setLoginUserName(providerLoginInfo.getBaseInfo().getStaffName());
        dto.setDataPermType(providerLoginInfo.obtainDataPerm().getDataPermType());
        String restUrl = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.MRCAR_USE_CONTRACT_BILLING_ADJUST_AMOUNT);
        Map<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }

    /**
     * 根据合同类型和合同编号查询签约主体信息
     */
    @PostMapping(MrcarBusinessRestMsgCenter.MRCAR_USE_CONTRACT_BILLING_QUERY_CONTRACT)
    public RestResponse queryContract(@RequestBody BillingInfoQueryContractReqDTO dto){
        String restUrl = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.MRCAR_USE_CONTRACT_BILLING_QUERY_CONTRACT);
        Map<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null, ContractInfoResDTO.class);
    }
}
