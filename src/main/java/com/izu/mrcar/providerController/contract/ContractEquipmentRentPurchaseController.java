package com.izu.mrcar.providerController.contract;

import com.google.common.collect.Maps;
import com.izu.business.config.BusinessRestLocator;
import com.izu.business.consts.resturl.MrcarBusinessRestMsgCenter;
import com.izu.business.dto.mrcarUseContract.output.BillingInfoPageResDTO;
import com.izu.business.dto.provider.input.ContractEquipmentRentPurchaseRecordReqDTO;
import com.izu.business.dto.provider.output.ContractEquipmentRentPurchaseRecordExportRespDTO;
import com.izu.business.dto.provider.output.ContractEquipmentRentPurchaseRecordRespDTO;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.doc.JrdApiDoc;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@RestController
@Api(tags = "运营端-租购管理")
@Slf4j
public class ContractEquipmentRentPurchaseController {
    private static final Integer PAGE_SIZE = 10000;


    @PostMapping(value = MrcarBusinessRestMsgCenter.MRCAR_USE_CONTRACT_EQUIPMENT_RENT_PURCHASE_EXPORT)
    @JrdApiDoc(simpleDesc = "设备租购记录导出", author = "hhd", resDataClass = ContractEquipmentRentPurchaseRecordExportRespDTO.class)
    @RequestFunction(functionName = "设备租购记录导出")
    @ExportExcelWeb(fileName = ExportExcelConstants.FILE_NAME_RENT_LIST_EXPORT_INFO ,filePath = ExportExcelConstants.FILE_PATH,
            sheet = ExportExcelConstants.SHEET_NAME_RENT_LIST_EXPORT_INFO, c = ContractEquipmentRentPurchaseRecordExportRespDTO.class)
    public PageDTO<ContractEquipmentRentPurchaseRecordExportRespDTO> export(@RequestBody ContractEquipmentRentPurchaseRecordReqDTO reqDTO, IzuEasyExcelSession izuEasyExcelSession,
                                                                            HttpServletRequest request, HttpServletResponse response) {
        String restUrl = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.MRCAR_USE_CONTRACT_EQUIPMENT_RENT_PURCHASE_LIST);
        AccountBaseInfo baseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
        izuEasyExcelSession.setUserName(baseInfo.getStaffName());
        reqDTO.setPage(izuEasyExcelSession.getPageNo());
        reqDTO.setPageSize(PAGE_SIZE);

        final HashMap<String, Object> restParam = Maps.newHashMapWithExpectedSize(1);
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        RestResponse<PageDTO<ContractEquipmentRentPurchaseRecordRespDTO>> res = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null, ContractEquipmentRentPurchaseRecordRespDTO.class);
        if(null!=res && res.isSuccess()){
            PageDTO<ContractEquipmentRentPurchaseRecordRespDTO> pageDTO = res.getData();
            List<ContractEquipmentRentPurchaseRecordExportRespDTO> list = BeanUtil.copyList(pageDTO.getResult(),ContractEquipmentRentPurchaseRecordExportRespDTO.class);
            return new PageDTO<>(pageDTO.getPage(), pageDTO.getPageSize(), pageDTO.getTotal(), list);
        }else {
            return null;
        }
    }

    @JrdApiDoc(simpleDesc = "账单列表", author = "hhd", resDataClass = BillingInfoPageResDTO.class)
    @PostMapping(value = MrcarBusinessRestMsgCenter.MRCAR_USE_CONTRACT_EQUIPMENT_RENT_PURCHASE_LIST)
    @RequestFunction(functionName = "账单列表")
    public RestResponse pageList(@RequestBody ContractEquipmentRentPurchaseRecordReqDTO reqDTO) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        reqDTO.setLoginUserId(providerLoginInfo.getBaseInfo().getStaffId());
        reqDTO.setLoginUserName(providerLoginInfo.getBaseInfo().getStaffName());
        reqDTO.setDataPermType(providerLoginInfo.obtainDataPerm().getDataPermType());
        String restUrl = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.MRCAR_USE_CONTRACT_EQUIPMENT_RENT_PURCHASE_LIST);
        Map<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null,ContractEquipmentRentPurchaseRecordRespDTO.class);
    }
}
