package com.izu.mrcar.providerController.contract.lingsan;

import com.alibaba.fastjson.JSON;
import com.izu.business.config.BusinessRestLocator;
import com.izu.business.consts.resturl.MrcarBusinessRestMsgCenter;
import com.izu.business.dto.czcontract.input.BussContractSoInputDTO;
import com.izu.business.dto.czcontract.output.BussContractSoDTO;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.lingsan.ProviderLingsanDataPermUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.enums.CompanyBusinessTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import javax.validation.Valid;
import java.util.*;

/**
* @Description: 零散用车合同管理
* @author: hxc
* @Date: 2023/1/30
**/
@RestController("lingsanContractController")
@Api(tags = {"零散用车-设置"})
@Slf4j
public class ContractController {

    /**
     * 零散用车合同分页列表查询
     * @param param
     * @return
     */
    @PostMapping(MrcarBusinessRestMsgCenter.LINGSAN_CONTRACT_PAGE_LIST)
    @RequestFunction(functionName = "零散用车合同列表")
    @ApiOperation(value = "零散用车-合同列表",notes = "作者：贺新春")
    public RestResponse<PageDTO<BussContractSoDTO>> contractPageList(@Valid @RequestBody BussContractSoInputDTO param){
        Integer contractType = param.getContractType();
        String dataPerms = null;
        if (contractType==1){
            Set<String> datePermDeptCode = ProviderLingsanDataPermUtil.getDatePermDeptCode();
            dataPerms = String.join(",",datePermDeptCode);
        }
        // 查询已开通账号的客户||供应商
        String userUrl = new UserRestLocator().getRestUrl(UserUrlCenter.PROVIDER_GET_LING_SAN_CUSTOMER_CODES);
        Map<String, Object> params = new HashMap<>();
        params.put("businessType", contractType==1?CompanyBusinessTypeEnum.LING_SAN_CUSTOMER.getValue():CompanyBusinessTypeEnum.LING_SAN_SUPPLIER.getValue());
        params.put("dataPerms",dataPerms);
        RestResponse restResponse = RestClient.requestInside(BaseHttpClient.HttpMethod.GET, userUrl, params, null);
        if (!restResponse.isSuccess() || restResponse == null || restResponse.getData() == null){
            return RestResponse.success(new PageDTO(param.getPage(),param.getPageSize(),0, Collections.emptyList()));
        }
        List<String> list = JSON.parseArray(restResponse.getData().toString(), String.class);
        if (list==null || list.isEmpty()){
            return RestResponse.success(new PageDTO(param.getPage(),param.getPageSize(),0, Collections.emptyList()));
        }
        param.setCustomerCodes(new HashSet<>(list));
        String url = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.LINGSAN_CONTRACT_PAGE_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, BussContractSoDTO.class);
    }


    @GetMapping(MrcarBusinessRestMsgCenter.PROVIDER_LINGSAN_CONTRACT_QUERY_BY_COMPANY_ID)
    @ApiOperation(value = "合同信息查询",notes = "作者：贺新春")
    public RestResponse<List<BussContractSoDTO>> getContract(@Verify(param = "companyId",rule="required") Integer companyId){

        String url = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.PROVIDER_LINGSAN_CONTRACT_QUERY_BY_COMPANY_ID);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("companyId",companyId);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.GET, url, paramMap, null);
    }



}
