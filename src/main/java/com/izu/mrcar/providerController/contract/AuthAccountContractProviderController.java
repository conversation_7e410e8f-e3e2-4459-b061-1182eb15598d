package com.izu.mrcar.providerController.contract;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.google.common.collect.Maps;
import com.izu.business.config.BusinessRestLocator;
import com.izu.business.consts.resturl.MrcarBusinessRestMsgCenter;
import com.izu.business.dto.provider.input.AuthAccountContractOpeInputDTO;
import com.izu.business.dto.provider.output.AuthAccountContractOpeDTO;
import com.izu.business.dto.provider.output.AuthAccountContractOpeDetailDTO;
import com.izu.business.dto.provider.output.AuthAccountContractOpeExportDTO;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.doc.JrdApiDoc;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.dto.daibuche.order.req.CoOrderTradeRecordQueryReqDTO;
import com.izu.mrcar.order.dto.daibuche.order.resp.CoOrderTradeRecordExportRespDTO;
import com.izu.mrcar.order.dto.daibuche.order.resp.CoOrderTradeRecordQueryRespDTO;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.dto.crmCustomer.CrmCustomerQueryReqDTO;
import com.izu.user.dto.staff.pc.*;
import com.izu.user.enums.perm.ProviderDataPermTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static com.izu.mrcar.order.MrcarOrderRestMsgCenter.DAIBUCHE_ORDER_TRADE_RECORD_EXPORT;
import static com.izu.mrcar.order.MrcarOrderRestMsgCenter.DAIBUCHE_ORDER_TRADE_RECORD_LIST;

@RestController
@Slf4j
@Api(tags = "运营端")
public class AuthAccountContractProviderController {
    private static final Integer PAGE_SIZE = 10000;

    @PostMapping(MrcarBusinessRestMsgCenter.PROVIDER_AUTH_ACCOUNT_CONTRACT_LIST)
    @RequestFunction(functionName = "使用许可合同列表")
    @ApiOperation(value = "使用许可合同列表",notes = "作者：mapp")
    @JrdApiDoc(simpleDesc = "使用许可合同列表", author = "hhd", resDataClass = AuthAccountContractOpeDTO.class)
    public RestResponse<PageDTO<AuthAccountContractOpeDTO>> getList(@RequestBody AuthAccountContractOpeInputDTO param){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        //数据权限不等于所有并且所属部门不等于总部的
        AtomicBoolean flag= new AtomicBoolean(false);
        List<String> companyIds = new ArrayList<>();
        if(ProviderDataPermTypeEnum.ALL.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())){
            flag.set(true);
        }
        if(ProviderDataPermTypeEnum.SELF_DEPT.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType()) &&
                CollUtil.isNotEmpty(providerLoginInfo.getProviderDataPerm().getDataPermInfoList())){
            List<DataPermInfo> dataPermInfoList = providerLoginInfo.getProviderDataPerm().getDataPermInfoList();
            //查询所属部门
            String url = new UserRestLocator().getRestUrl(UserUrlCenter.PROVIDER_HEAD_DEPARTMENT_LIST);
            RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, url, null, null, String.class);
            if(restResponse!=null && restResponse.isSuccess() && restResponse.getData()!=null){
                List<String> list = (List<String>) restResponse.getData();
                dataPermInfoList.forEach(x->{
                    if(list.contains(x.getDataPermCode())){
                        flag.set(true);
                    }
                    companyIds.add(String.valueOf(x.getDataPermCode()));
                });
            }
        }
        List<String>  customerCodeList = new ArrayList<>();
        if(!flag.get()){
            //先查询用户是否存在数据权限
            String userUrl = new UserRestLocator().getRestUrl(UserUrlCenter.GET_CUSTOMER_CODE_LIST_BY_STAFF_COMPANY);
            CrmCustomerQueryReqDTO dto = new CrmCustomerQueryReqDTO();
            dto.setOwnerOrCreateOrUpdateId(providerLoginInfo.getBaseInfo().getMgtStaffId());
            if(Objects.equals(providerLoginInfo.obtainDataPerm().getDataPermType(),ProviderDataPermTypeEnum.SELF_DEPT.getType())){
                dto.setCompanyIds(companyIds);
            }
            Map<String, Object> param1 = new HashMap<>();
            param1.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
            RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, userUrl, param1, null, String.class);
            if(restResponse!=null && restResponse.isSuccess() && restResponse.getData()!=null){
                customerCodeList=(List<String>) restResponse.getData();
            }
            param.setUserId(String.valueOf(providerLoginInfo.getBaseInfo().getMgtStaffId()));
        }
        param.setCustomerCodeList(customerCodeList);
        /*param.setStaffId(providerLoginInfo.getBaseInfo().getStaffId());
        param.setStaffCode(providerLoginInfo.getBaseInfo().getStaffCode());
        param.setMgtStaffId(providerLoginInfo.getBaseInfo().getMgtStaffId());
        param.setDataPermType(providerLoginInfo.obtainDataPerm().getDataPermType());
        param.setDataPermSet(getDataScopeList(providerLoginInfo));*/
        String url = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.PROVIDER_AUTH_ACCOUNT_CONTRACT_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, AuthAccountContractOpeDTO.class);
    }

    @GetMapping(MrcarBusinessRestMsgCenter.PROVIDER_AUTH_ACCOUNT_CONTRACT_DETAIL)
    @RequestFunction(functionName = "使用许可合同详情")
    @JrdApiDoc(simpleDesc = "使用许可合同详情", author = "hhd", resDataClass = AuthAccountContractOpeDetailDTO.class)
    @ApiOperation(value = "使用许可合同详情",notes = "作者：mapp")
    public RestResponse<AuthAccountContractOpeDetailDTO> getDetail(Integer id){

        String url = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.PROVIDER_AUTH_ACCOUNT_CONTRACT_DETAIL);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("id", id);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.GET, url, paramMap, null, AuthAccountContractOpeDetailDTO.class);

    }

    /**
     * 将运营端的会话信息中数据权限转为具体的业务参数集合
     * @param providerLoginInfo
     * @return
     */
    private Set<String> getDataScopeList(ProviderLoginInfo providerLoginInfo){
        Set<String> res = null;
        if(ProviderDataPermTypeEnum.ALL.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())){
            return null;
        }else if(ProviderDataPermTypeEnum.RESPONSIBLE_CONTRACT.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())){
            res = providerLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet();
        }else if(ProviderDataPermTypeEnum.RESPONSIBLE_CUSTOMER.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())||
                ProviderDataPermTypeEnum.ASSIGN_CUSTOMER.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())){
            res = providerLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet();
        }else if(ProviderDataPermTypeEnum.SELF_DEPT.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())||
                ProviderDataPermTypeEnum.ASSIGN_DEPT.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())){
            res = providerLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet();
        }
        return res;
    }

    @PostMapping(value = MrcarBusinessRestMsgCenter.PROVIDER_AUTH_ACCOUNT_CONTRACT_EXPORT)
    @JrdApiDoc(simpleDesc = "合同导出", author = "hhd", resDataClass = AuthAccountContractOpeExportDTO.class)
    @RequestFunction(functionName = "合同导出")
    @ExportExcelWeb(fileName = ExportExcelConstants.FILE_NAME_CONTRACT_LIST_EXPORT_INFO ,filePath = ExportExcelConstants.FILE_PATH,
            sheet = ExportExcelConstants.SHEET_NAME_CONTRACT_LIST_EXPORT_INFO, c = AuthAccountContractOpeExportDTO.class)
    public PageDTO<AuthAccountContractOpeExportDTO> export(@RequestBody AuthAccountContractOpeInputDTO reqDTO, IzuEasyExcelSession izuEasyExcelSession,
                                                           HttpServletRequest request, HttpServletResponse response) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        //数据权限不等于所有并且所属部门不等于总部的
        AtomicBoolean flag= new AtomicBoolean(false);
        List<String> companyIds = new ArrayList<>();
        if(ProviderDataPermTypeEnum.ALL.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())){
            flag.set(true);
        }
        if(ProviderDataPermTypeEnum.SELF_DEPT.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType()) &&
                CollUtil.isNotEmpty(providerLoginInfo.getProviderDataPerm().getDataPermInfoList())){
            List<DataPermInfo> dataPermInfoList = providerLoginInfo.getProviderDataPerm().getDataPermInfoList();
            //查询所属部门
            String url = new UserRestLocator().getRestUrl(UserUrlCenter.PROVIDER_HEAD_DEPARTMENT_LIST);
            RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, url, null, null, String.class);
            if(restResponse!=null && restResponse.isSuccess() && restResponse.getData()!=null){
                List<String> list = (List<String>) restResponse.getData();
                dataPermInfoList.forEach(x->{
                    if(list.contains(x.getDataPermCode())){
                        flag.set(true);
                    }
                    companyIds.add(String.valueOf(x.getDataPermCode()));
                });
            }
        }
        List<String>  customerCodeList = new ArrayList<>();
        if(!flag.get()){
            //先查询用户是否存在数据权限
            String userUrl = new UserRestLocator().getRestUrl(UserUrlCenter.GET_CUSTOMER_CODE_LIST_BY_STAFF_COMPANY);
            CrmCustomerQueryReqDTO dto = new CrmCustomerQueryReqDTO();
            dto.setOwnerOrCreateOrUpdateId(providerLoginInfo.getBaseInfo().getMgtStaffId());
            if(Objects.equals(providerLoginInfo.obtainDataPerm().getDataPermType(),ProviderDataPermTypeEnum.SELF_DEPT.getType())){
                dto.setCompanyIds(companyIds);
            }
            Map<String, Object> param1 = new HashMap<>();
            param1.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
            RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, userUrl, param1, null, String.class);
            if(restResponse!=null && restResponse.isSuccess() && restResponse.getData()!=null){
                customerCodeList=(List<String>) restResponse.getData();
            }
            reqDTO.setUserId(String.valueOf(providerLoginInfo.getBaseInfo().getMgtStaffId()));
        }
        reqDTO.setCustomerCodeList(customerCodeList);
        /*reqDTO.setStaffId(providerLoginInfo.getBaseInfo().getStaffId());
        reqDTO.setStaffCode(providerLoginInfo.getBaseInfo().getStaffCode());
        reqDTO.setMgtStaffId(providerLoginInfo.getBaseInfo().getMgtStaffId());
        reqDTO.setDataPermType(providerLoginInfo.obtainDataPerm().getDataPermType());
        reqDTO.setDataPermSet(getDataScopeList(providerLoginInfo));*/
        String url = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.PROVIDER_AUTH_ACCOUNT_CONTRACT_LIST);
        AccountBaseInfo baseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
        izuEasyExcelSession.setUserName(baseInfo.getStaffName());
        reqDTO.setPage(izuEasyExcelSession.getPageNo());
        reqDTO.setPageSize(PAGE_SIZE);

        final HashMap<String, Object> restParam = Maps.newHashMapWithExpectedSize(1);
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        RestResponse<PageDTO<AuthAccountContractOpeDTO>> res = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, restParam, null, AuthAccountContractOpeDTO.class);
        if(null!=res && res.isSuccess()){
            PageDTO<AuthAccountContractOpeDTO> pageDTO = res.getData();
            List<AuthAccountContractOpeExportDTO> list = BeanUtil.copyList(pageDTO.getResult(),AuthAccountContractOpeExportDTO.class);
            return new PageDTO<>(pageDTO.getPage(), pageDTO.getPageSize(), pageDTO.getTotal(), list);
        }else {
            return null;
        }
    }
}
