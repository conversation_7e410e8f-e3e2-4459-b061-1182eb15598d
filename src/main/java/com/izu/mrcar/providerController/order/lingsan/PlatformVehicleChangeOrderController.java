package com.izu.mrcar.providerController.order.lingsan;

import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.dto.lingsan.audit.AuditInitDTO;
import com.izu.mrcar.order.dto.lingsan.vehicleChangeOrder.VehicleChangeOrderDetailDTO;
import com.izu.mrcar.order.dto.lingsan.vehicleChangeOrder.VehicleChangeOrderPageDTO;
import com.izu.mrcar.order.dto.lingsan.vehicleChangeOrder.req.VehicleChangeOrderReqDTO;
import com.izu.mrcar.shiro.session.WebSessionUtil;
import com.izu.mrcar.utils.lingsan.PermissionUtil;
import com.izu.user.dto.LoginUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * @Program: mrcar
 * @Description: 零散用车-换车单
 * @Author: inT
 * @Create: 2023-01-29
 */
@Api(tags = "零散用车")
@Slf4j
@RestController
public class PlatformVehicleChangeOrderController {

    @GetMapping(MrcarOrderRestMsgCenter.LINGSAN_PLATFORM_VEHICLE_CHANGE_ORDER_STATUS_LIST)
    @ApiOperation(value = "换车单-状态枚举")
    public RestResponse listStatus() {
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.LINGSAN_PLATFORM_VEHICLE_CHANGE_ORDER_STATUS_LIST);
        RestResponse restResponse = RestClient.requestForObject(BaseHttpClient.HttpMethod.GET, restUrl, new HashMap<>(2), null, Object.class);
        return restResponse;
    }

    @SuppressWarnings("all")
    @PostMapping(MrcarOrderRestMsgCenter.LINGSAN_PLATFORM_VEHICLE_CHANGE_ORDER_PAGE_LIST)
    @ApiOperation(value = "换车单-分页列表查询")
    public RestResponse<PageDTO<VehicleChangeOrderPageDTO>> listChangeOrders(@RequestBody VehicleChangeOrderReqDTO reqDTO) {
        reqDTO.setLoginUser(PermissionUtil.getLoginUserInfo());
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.LINGSAN_PLATFORM_VEHICLE_CHANGE_ORDER_PAGE_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, VehicleChangeOrderPageDTO.class);
    }

    @GetMapping("/detail")
    @ApiOperation(value = "换车单-详情")
    public RestResponse<VehicleChangeOrderDetailDTO> getChangeOrderDetail(@RequestParam("vehicleChangeOrderNum") String vehicleChangeOrderNum){
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.LINGSAN_PLATFORM_VEHICLE_CHANGE_ORDER_DETAIL);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("vehicleChangeOrderNum", vehicleChangeOrderNum);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.GET, url, paramMap, null, VehicleChangeOrderDetailDTO.class);

    }

}
