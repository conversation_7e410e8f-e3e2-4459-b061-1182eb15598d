package com.izu.mrcar.providerController.order;

import com.izu.asset.dto.vehicle.SchedulableVehicleReqDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.controller.base.AssetBaseController;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 可用于调度的车辆列表
 * <AUTHOR>
 * @date : 2023/3/20
 */
@RestController
public class ProviderSchedulableVehicleListController extends AssetBaseController {


    @RequestMapping("/provider/listVehicle")
    public RestResponse selectSchedulableVehicleList(@RequestBody SchedulableVehicleReqDTO reqDTO){
        reqDTO.setIsFromPc(Boolean.TRUE);
        return postBodyWithLogin("/car/selectSchedulableVehicleList",reqDTO);
    }

}
