package com.izu.mrcar.providerController.order.daibuche;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.izu.mrcar.order.dto.LoginUserInfo;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.lingsan.ProviderDaiBuCheDataPermUtil;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.enums.perm.ProviderDataPermTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Sets;

import java.util.*;

/**
 * 代步车通用方法
 *
 * <AUTHOR>
 */
@Slf4j
public class CoBaseController {

    public static LoginUserInfo getLoginUserInfo() {
        LoginUserInfo loginUserInfo = new LoginUserInfo();
        AccountBaseInfo accountBaseInfo = LoginSessionUtil.getProviderLoginInfo().getBaseInfo();
        loginUserInfo.setLoginUserId(accountBaseInfo.getStaffId());
        loginUserInfo.setLoginUserName(accountBaseInfo.getStaffName());
        loginUserInfo.setMgtStaffId(accountBaseInfo.getMgtStaffId());
        loginUserInfo.setEmail(accountBaseInfo.getEmail());
        loginUserInfo.setPermDeptCodes(ProviderDaiBuCheDataPermUtil.getDatePermDeptCode());
        return loginUserInfo;
    }

    /**
     * 获取运营端-代步车数据权限
     *
     * @return
     */
    @Deprecated
    public static Set<String> getPermDeptCodes(ProviderLoginInfo providerLoginInfo) {
        Set<String> deptCode = new HashSet<>();
        Byte dataPermType = providerLoginInfo.obtainDataPerm().getDataPermType();
        //前期测试阶段默认全部数据权限
//        dataPermType = ProviderDataPermTypeEnum.ALL.getType();
        if (ProviderDataPermTypeEnum.ONE_SELF.getType().equals(dataPermType)) {
            //【本人】权限
            deptCode.add("-1");
        } else if (ProviderDataPermTypeEnum.ALL.getType().equals(dataPermType)) {
            //【所有】权限
            return deptCode;
        } else if (Objects.equals(ProviderDataPermTypeEnum.ASSIGN_DEPT.getType(), dataPermType)) {
            //【指定部门权限】
            Set<String> dataCodeSet = providerLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet();
            deptCode.addAll(CollectionUtils.isEmpty(dataCodeSet) ? Sets.newHashSet("-1") : dataCodeSet);
        } else {
            //其他权限【负责合同、负责客户、指定客户、所属部门】默认部门权限均为用户所属部门（权限获取方式与【指定部门权限一致】，为逻辑清晰else处理）
            Set<String> dataCodeSet = providerLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet();
            deptCode.addAll(CollectionUtils.isEmpty(dataCodeSet) ? Sets.newHashSet("-1") : dataCodeSet);
        }
        return deptCode;
    }

}
