package com.izu.mrcar.providerController.order;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("问题反馈记录")
public class OrderQuestionExportDTO {

    @ExcelProperty(value = "问题ID")
    @ColumnWidth(value = 20)
    private String questionNo;

    @ExcelProperty(value = "问题描述")
    @ColumnWidth(value = 40)
    private String questionDescription;

    @ExcelProperty(value = "问题提报分类")
    @ColumnWidth(value = 40)
    private String questionTypeStr;

    @ExcelProperty(value = "状态")
    @ColumnWidth(value = 20)
    private String dealStatusStr;

    @ExcelProperty(value = "处理分类")
    @ColumnWidth(value = 40)
    private String dealQuestionTypeStr;

    @ExcelProperty(value = "客户企业")
    @ColumnWidth(value = 20)
    private String companyName;

    @ExcelProperty(value = "上报人")
    @ColumnWidth(value = 20)
    private String reportName;

    @ExcelProperty(value = "上报人手机号")
    @ColumnWidth(value = 20)
    private String reportMobile;

    @ExcelProperty(value = "上报时间")
    @ColumnWidth(value = 20)
    private Date reportTime;

    @ExcelProperty(value = "问题来源")
    @ColumnWidth(value = 20)
    private String questionSourceStr;

    @ExcelProperty(value = "来源ID")
    @ColumnWidth(value = 20)
    private String businessNo;

    @ExcelProperty(value = "处理人")
    @ColumnWidth(value = 20)
    private String dealName;

    @ExcelProperty(value = "处理时间")
    @ColumnWidth(value = 20)
    private Date dealTime;

    @ExcelProperty(value = "创建时间")
    @ColumnWidth(value = 20)
    private Date createTime;
}

