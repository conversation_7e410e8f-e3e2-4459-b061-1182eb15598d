package com.izu.mrcar.providerController.order.daibuche;


import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.dto.DicKeyValueDTO;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.consts.daibuche.OrderFeeEnum;
import com.izu.mrcar.order.dto.common.BaseDTO;
import com.izu.mrcar.order.dto.daibuche.order.req.*;
import com.izu.mrcar.order.dto.daibuche.order.resp.CoOrderCountResDTO;
import com.izu.mrcar.order.dto.daibuche.order.resp.CoOrderEstimateRespDTO;
import com.izu.mrcar.order.dto.daibuche.order.resp.CoOrderInfoRespDTO;
import com.izu.mrcar.providerController.order.OrderQuestionExportDTO;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.lingsan.ProviderDaiBuCheDataPermUtil;
import com.izu.order.dto.rentorder.KeyValuePairDTO;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import com.izu.user.dto.staff.pc.DataPerm;
import com.izu.user.dto.staff.pc.DataPermInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

import static com.izu.mrcar.order.MrcarOrderRestMsgCenter.*;


@Api(tags="代步车-订单")
@RestController
@Slf4j
public class CoOrderInfoController {

    private static final Integer MAX_LINE = 10000;
    private static final Integer PAGE_SIZE = 10000;

    @PostMapping(value = DAIBUCHE_ORDER_LIST)
    @RequestFunction(functionName = "代步车-查询订单列表")
    @ApiOperation(value = "代步车订单列表查询")
    public RestResponse<PageDTO<CoOrderInfoRespDTO>> list(@RequestBody CoOrderInfoQueryReqDTO reqDTO) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        DataPerm providerDataPerm= providerLoginInfo.obtainDataPerm();

        reqDTO.setDataPermType(providerDataPerm.getDataPermType());
        reqDTO.setDataPermCodeSet(ProviderDaiBuCheDataPermUtil.getDatePermDeptCode());
        reqDTO.setLoginUserId(providerLoginInfo.getBaseInfo().getStaffId());

        Map<String, Object> param = new HashMap<>();
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        String restUrl = new MrcarOrderRestLocator().getRestUrl(DAIBUCHE_ORDER_LIST);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null, CoOrderInfoRespDTO.class);
    }

    @PostMapping(value = DAIBUCHE_ORDER_DETAIL)
    @RequestFunction(functionName = "代步车-查询订单详情")
    @ApiOperation(value = "代步车订单详情")
    public RestResponse<CoOrderInfoRespDTO> detail(@RequestBody CoOrderInfoQueryReqDTO reqDTO) {
        Map<String, Object> param = new HashMap<>();
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        String restUrl = new MrcarOrderRestLocator().getRestUrl(DAIBUCHE_ORDER_DETAIL);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null, CoOrderInfoRespDTO.class);
    }

    @PostMapping(value = DAIBUCHE_ORDER_LIST_EXPORT)
    @ApiOperation(value = "代步车订单导出")
    @ExportExcelWeb(fileName = ExportExcelConstants.FILE_NAME_DAIBUCHE_ORDER_EXPORT_INFO,filePath = ExportExcelConstants.FILE_PATH,
            sheet = ExportExcelConstants.SHEET_NAME_DAIBUCHE_ORDER_EXPORT_INFO, c = CoOrderInfoExportDTO.class)
    public PageDTO<CoOrderInfoExportDTO> export(@RequestBody CoOrderInfoQueryReqDTO reqDTO, IzuEasyExcelSession izuEasyExcelSession,
                                                  HttpServletRequest request, HttpServletResponse response) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        DataPerm providerDataPerm= providerLoginInfo.obtainDataPerm();

        reqDTO.setDataPermType(providerDataPerm.getDataPermType());
        reqDTO.setDataPermCodeSet(ProviderDaiBuCheDataPermUtil.getDatePermDeptCode());
        reqDTO.setLoginUserId(providerLoginInfo.getBaseInfo().getStaffId());

        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.DAIBUCHE_ORDER_LIST);
        izuEasyExcelSession.setUserName(providerLoginInfo.obtainBaseInfo().getStaffName());
        reqDTO.setIsExport(Boolean.TRUE);
        reqDTO.setPageNum(izuEasyExcelSession.getPageNo());
        reqDTO.setPageSize(PAGE_SIZE);

        final HashMap<String, Object> restParam = Maps.newHashMapWithExpectedSize(1);
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        RestResponse<PageDTO<CoOrderInfoRespDTO>> res = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null, CoOrderInfoRespDTO.class);
        if(null!=res && res.isSuccess()){
            PageDTO<CoOrderInfoRespDTO> pageDTO = res.getData();
            if (pageDTO.getTotal()>MAX_LINE){
                throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
            }
            List<CoOrderInfoExportDTO> list = BeanUtil.copyList(pageDTO.getResult(),CoOrderInfoExportDTO.class);
            return new PageDTO<>(pageDTO.getPage(), pageDTO.getPageSize(), pageDTO.getTotal(), list);
        }else {
            return null;
        }

    }

    @ApiOperation(value = "代步车订单送车确认")
    @PostMapping(value = DAIBUCHE_ORDER_DELIVERY)
    public RestResponse<Void> orderDelivery(@RequestBody CoOrderDeliveryReqDTO param) {
        MrcarOrderRestLocator restLocator = new MrcarOrderRestLocator();
        String restUrl = restLocator.getRestUrl(MrcarOrderRestMsgCenter.DAIBUCHE_ORDER_DELIVERY);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, buildParams(param), null, Object.class);
    }

    @ApiOperation(value = "代步车订单还车费用预估")
    @PostMapping(value = DAIBUCHE_ORDER_ESTIMATE_FEE)
    public RestResponse<CoOrderEstimateRespDTO> orderEstimateFee(@RequestBody CoOrderEstimateReqDTO param) {
        MrcarOrderRestLocator restLocator = new MrcarOrderRestLocator();
        String restUrl = restLocator.getRestUrl(MrcarOrderRestMsgCenter.DAIBUCHE_ORDER_ESTIMATE_FEE);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, buildParams(param), null, CoOrderEstimateRespDTO.class);
    }


    @ApiOperation(value = "代步车订单还车确认")
    @PostMapping(value = DAIBUCHE_ORDER_RETURN)
    public RestResponse<Void> orderReturn(@RequestBody CoOrderReturnReqDTO param) {
        MrcarOrderRestLocator restLocator = new MrcarOrderRestLocator();
        String restUrl = restLocator.getRestUrl(MrcarOrderRestMsgCenter.DAIBUCHE_ORDER_RETURN);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, buildParams(param), null, Object.class);
    }

    @PostMapping(path = DAIBUCHE_ORDER_RECORDING)
    @ApiOperation(value = "代步车订单补录")
    @RequestFunction(functionName = "代步车订单补录")
    public RestResponse<Void> recordOrder(@RequestBody CoOrderRecordReqDTO param){
        MrcarOrderRestLocator restLocator = new MrcarOrderRestLocator();
        String restUrl = restLocator.getRestUrl(DAIBUCHE_ORDER_RECORDING);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, restUrl, buildParams(param), null, CoOrderCountResDTO.class);
    }


    @PostMapping(path = "/daibuche/order/recordFeeConfig")
    @ApiOperation(value = "订单补录费用项列表")
    @RequestFunction(functionName = "订单补录费用项列表")
    public RestResponse<List<DicKeyValueDTO<String, String>>> recordFeeConfig(){
        // 二期可以修改为配置表
        List<DicKeyValueDTO<String, String>> result = new ArrayList<>();
        result.add(new DicKeyValueDTO<>(OrderFeeEnum.FEE_PETROL.getFeeName(), OrderFeeEnum.FEE_PETROL.getFeeCode()));
        result.add(new DicKeyValueDTO<>(OrderFeeEnum.FEE_VIOLATE.getFeeName(), OrderFeeEnum.FEE_VIOLATE.getFeeCode()));
        result.add(new DicKeyValueDTO<>(OrderFeeEnum.FEE_OTHER.getFeeName(), OrderFeeEnum.FEE_OTHER.getFeeCode()));
        return RestResponse.success(result);
    }




    private Map<String, Object> buildParams(BaseDTO params){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        AccountBaseInfo baseInfo = providerLoginInfo.getBaseInfo();
        params.setLoginUserId(baseInfo.getStaffId());
        params.setLoginCode(baseInfo.getStaffCode());
        params.setLoginUserName(baseInfo.getStaffName());
        params.setLoginUserMobile(baseInfo.getMobile());
        return JSON.parseObject(JSON.toJSONString(params)).getInnerMap();

    }
    @ApiOperation(value = "免押金")
    @PostMapping(value = DAIBUCHE_ORDER_DEPOSIT_FREE)
    public RestResponse<Void> freeDepositOrder(@RequestBody CoOrderInfoFreeReqDTO reqDTO) {
        MrcarOrderRestLocator restLocator = new MrcarOrderRestLocator();
        String restUrl = restLocator.getRestUrl(MrcarOrderRestMsgCenter.DAIBUCHE_ORDER_DEPOSIT_FREE);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, buildParams(reqDTO), null, Object.class);
    }

    @ApiOperation(value = "扣款")
    @PostMapping(value = DAIBUCHE_ORDER_DEPOSIT_DEDUCT)
    public RestResponse<Void> deductDepositOrder(@RequestBody CoOrderInfoDeductReqDTO reqDTO) {
        MrcarOrderRestLocator restLocator = new MrcarOrderRestLocator();
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        AccountBaseInfo baseInfo = providerLoginInfo.getBaseInfo();
        reqDTO.setLoginUserId(baseInfo.getStaffId());
        reqDTO.setLoginUserName(baseInfo.getStaffName());
        String restUrl = restLocator.getRestUrl(MrcarOrderRestMsgCenter.DAIBUCHE_ORDER_DEPOSIT_DEDUCT);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, buildParams(reqDTO), null, Object.class);
    }

    @ApiOperation(value = "解冻")
    @PostMapping(value = DAIBUCHE_ORDER_DEPOSIT_UNFREEZE)
    public RestResponse<Void> unfreezeDepositOrder(@RequestBody CoOrderInfoUnFreezeReqDTO reqDTO) {
        MrcarOrderRestLocator restLocator = new MrcarOrderRestLocator();
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        AccountBaseInfo baseInfo = providerLoginInfo.getBaseInfo();
        reqDTO.setLoginUserId(baseInfo.getStaffId());
        reqDTO.setLoginUserName(baseInfo.getStaffName());
        String restUrl = restLocator.getRestUrl(MrcarOrderRestMsgCenter.DAIBUCHE_ORDER_DEPOSIT_UNFREEZE);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, buildParams(reqDTO), null, Object.class);
    }



}
