package com.izu.mrcar.providerController.order;

import com.github.pagehelper.Page;
import com.izu.config.MrCarConfigRestLocator;
import com.izu.config.dto.expenditure.CompanyTaxConfig;
import com.izu.consts.ConfigURI;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.controller.export.SubTripDetailExport;
import com.izu.mrcar.controller.export.SupplierSpendListExport;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.dto.provider.input.*;
import com.izu.mrcar.order.dto.provider.output.*;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.AccountBelongDepartment;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 商务车支出单
 * @date 2024/4/8 9:50
 */
@RestController
@Api(tags = "供应商支出单")
public class SupplierSpendController {

    @PostMapping(MrcarOrderRestMsgCenter.PROVIDER_BUSINESS_EXPENDITURE_SUPPLIER_SPEND_LIST)
    @RequestFunction(functionName = "商务车支出单列表-运营端")
    @ApiOperation(value = "商务车支出单列表-运营端",notes = "作者：dingweibing")
    public RestResponse<PageDTO<SupplierSpendOutputDTO>> getSupplierSpendList(@RequestBody @Valid SupplierSpendInputDTO supplierSpendInputDTO){
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.PROVIDER_BUSINESS_EXPENDITURE_SUPPLIER_SPEND_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, supplierSpendInputDTO);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, SupplierSpendOutputDTO.class);
    }

    @PostMapping(MrcarOrderRestMsgCenter.PROVIDER_BUSINESS_EXPENDITURE_EXPORT_SUPPLIER_SPEND_LIST)
    @RequestFunction(functionName = "商务车支出单列表-导出-运营端")
    @ApiOperation(value = "商务车支出单列表-导出-运营端",notes = "作者：dingweibing")
    @ExportExcelWeb(fileName = "商务车支出单列表.xlsx",filePath = "/data/logs/excel/tmp",sheet = "商务车支出单",
            c = SupplierSpendListExport.class,isAsync = false
    )
    public PageDTO exportSupplierSpendList(@RequestBody @Valid SupplierSpendInputDTO supplierSpendInputDTO, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response){
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.PROVIDER_BUSINESS_EXPENDITURE_SUPPLIER_SPEND_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        supplierSpendInputDTO.setPageSize(500);
        supplierSpendInputDTO.setPageNo(izuEasyExcelSession.getPageNo());
        supplierSpendInputDTO.setPage(izuEasyExcelSession.getPageNo());
        supplierSpendInputDTO.setPageNum(izuEasyExcelSession.getPageNo());
        ProviderLoginInfo clientLoginInfo=LoginSessionUtil.getProviderLoginInfo();
        izuEasyExcelSession.setUserName(clientLoginInfo.getBaseInfo().getStaffName());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, supplierSpendInputDTO);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, SupplierSpendListExport.class);
        if(restResponse!=null && restResponse.isSuccess()) {
            PageDTO pageDTO = (PageDTO) restResponse.getData();
            if (pageDTO.getTotal()>10000){
                throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
            }
            return pageDTO;
        }else{
            return null;
        }
    }
    @PostMapping(MrcarOrderRestMsgCenter.PROVIDER_BUSINESS_EXPENDITURE_EXPORT_SUB_SUPPLIER_SPEND_LIST)
    @RequestFunction(functionName = "商务车支出单明细-导出-运营端")
    @ApiOperation(value = "商务车支出单明细列表-导出-运营端",notes = "作者：dingweibing")
    @ExportExcelWeb(fileName = "商务车支出单明细列表.xlsx",filePath = "/data/logs/excel/tmp",sheet = "商务车支出单明细",
            c = ExportSubTripDetailDTO.class,isAsync = false
    )
    public PageDTO exportSubSupplierSpendList(@RequestBody @Valid SupplierSpendInputDTO supplierSpendInputDTO, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response){
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.PROVIDER_BUSINESS_EXPENDITURE_EXPORT_SUB_SUPPLIER_SPEND_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        supplierSpendInputDTO.setPageSize(500);
        supplierSpendInputDTO.setPageNo(izuEasyExcelSession.getPageNo());
        supplierSpendInputDTO.setPage(izuEasyExcelSession.getPageNo());
        supplierSpendInputDTO.setPageNum(izuEasyExcelSession.getPageNo());
        ProviderLoginInfo clientLoginInfo=LoginSessionUtil.getProviderLoginInfo();
        izuEasyExcelSession.setUserName(clientLoginInfo.getBaseInfo().getStaffName());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, supplierSpendInputDTO);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, ExportSubTripDetailDTO.class);
        if(restResponse!=null && restResponse.isSuccess()) {
            PageDTO pageDTO = (PageDTO) restResponse.getData();
            if (pageDTO.getTotal()>10000){
                throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
            }
            return pageDTO;
        }else{
            return null;
        }
    }

    @PostMapping(MrcarOrderRestMsgCenter.PROVIDER_BUSINESS_EXPENDITURE_GET_OPERATION_LIST)
    @RequestFunction(functionName = "商务车支出单操作日志查询列表-运营端")
    @ApiOperation(value = "商务车支出单操作日志查询列表-运营端",notes = "作者：dingweibing")
    public RestResponse<PageDTO<OperationOutputDTO>> getOperationList(@Valid OperationInputDTO operationInputDTO){
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.PROVIDER_BUSINESS_EXPENDITURE_GET_OPERATION_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, operationInputDTO);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, OperationOutputDTO.class);
    }

    @PostMapping(MrcarOrderRestMsgCenter.PROVIDER_BUSINESS_EXPENDITURE_ADD_SUPPLIER_SPEND)
    @RequestFunction(functionName = "新建商务车支出单-运营端")
    @ApiOperation(value = "新建商务车支出单-运营端",notes = "作者：dingweibing")
    public RestResponse<Boolean> addSupplierSpend(@RequestBody @Valid AddSupplierSpendInput addSupplierSpendInput){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        addSupplierSpendInput.setLoginUserId(providerLoginInfo.obtainBaseInfo().getStaffId());
        addSupplierSpendInput.setLoginUserName(providerLoginInfo.obtainBaseInfo().getStaffName());
        addSupplierSpendInput.setLoginUserMobile(providerLoginInfo.obtainBaseInfo().getMobile());
        if(providerLoginInfo.getBelongDepartmentList()!=null&&providerLoginInfo.getBelongDepartmentList().size()!=0){
            addSupplierSpendInput.setLoginDeptCode(providerLoginInfo.getBelongDepartmentList().stream().map(AccountBelongDepartment::getDeptCode).collect(Collectors.joining(",")));
            addSupplierSpendInput.setLoginDeptName(providerLoginInfo.getBelongDepartmentList().stream().map(AccountBelongDepartment::getDeptName).collect(Collectors.joining(",")));
        }
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.PROVIDER_BUSINESS_EXPENDITURE_ADD_SUPPLIER_SPEND);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, addSupplierSpendInput);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, Boolean.class);
    }
    @PostMapping(MrcarOrderRestMsgCenter.PROVIDER_BUSINESS_EXPENDITURE_UPDATE_SUPPLIER_SPEND)
    @RequestFunction(functionName = "编辑商务车支出单-运营端")
    @ApiOperation(value = "编辑商务车支出单-运营端",notes = "作者：dingweibing")
    public RestResponse<Boolean> updateSupplierSpend(@RequestBody @Valid UpdateSupplierSpendInput updateSupplierSpendInput){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        updateSupplierSpendInput.setLoginUserId(providerLoginInfo.obtainBaseInfo().getStaffId());
        updateSupplierSpendInput.setLoginUserName(providerLoginInfo.obtainBaseInfo().getStaffName());
        updateSupplierSpendInput.setLoginUserMobile(providerLoginInfo.obtainBaseInfo().getMobile());
        if(providerLoginInfo.getBelongDepartmentList()!=null&&providerLoginInfo.getBelongDepartmentList().size()!=0){
            updateSupplierSpendInput.setLoginDeptCode(providerLoginInfo.getBelongDepartmentList().stream().map(AccountBelongDepartment::getDeptCode).collect(Collectors.joining(",")));
            updateSupplierSpendInput.setLoginDeptName(providerLoginInfo.getBelongDepartmentList().stream().map(AccountBelongDepartment::getDeptName).collect(Collectors.joining(",")));
        }
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.PROVIDER_BUSINESS_EXPENDITURE_UPDATE_SUPPLIER_SPEND);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, updateSupplierSpendInput);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, Boolean.class);
    }

    @PostMapping(MrcarOrderRestMsgCenter.PROVIDER_BUSINESS_EXPENDITURE_GET_SUB_TRIP_DETAIL_LIST)
    @RequestFunction(functionName = "供应商支出单详情页子行程列表-运营端")
    @ApiOperation(value = "供应商支出单详情页子行程列表-运营端",notes = "作者：dingweibing")
    public RestResponse<Page<SubTripDetailOutputDTO>> getSubTripDetailList(@RequestBody @Valid SubTripDetailInputDTO subTripDetailInputDTO){
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.PROVIDER_BUSINESS_EXPENDITURE_GET_SUB_TRIP_DETAIL_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, subTripDetailInputDTO);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, SubTripDetailOutputDTO.class);
    }
    @PostMapping(MrcarOrderRestMsgCenter.PROVIDER_BUSINESS_EXPENDITURE_SUB_TRIP_DETAIL_LIST_EXPORT)
    @RequestFunction(functionName = "供应商支出单详情页子行程列表-导出-运营端")
    @ApiOperation(value = "供应商支出单详情页子行程列表-导出-运营端",notes = "作者：dingweibing")
    @ExportExcelWeb(fileName = "商务车子行程明细.xlsx",filePath = "/data/logs/excel/tmp",sheet = "商务车子行程明细",
            c = SubTripDetailExport.class
    )
    public PageDTO exportSubTripDetailList(@RequestBody @Valid SubTripDetailInputDTO subTripDetailInputDTO, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response){
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.PROVIDER_BUSINESS_EXPENDITURE_GET_SUB_TRIP_DETAIL_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        subTripDetailInputDTO.setPageSize(1000);
        ProviderLoginInfo clientLoginInfo=LoginSessionUtil.getProviderLoginInfo();
        izuEasyExcelSession.setUserName(clientLoginInfo.getBaseInfo().getStaffName());
        subTripDetailInputDTO.setPageNo(izuEasyExcelSession.getPageNo());
        subTripDetailInputDTO.setPage(izuEasyExcelSession.getPageNo());
        subTripDetailInputDTO.setPageNum(izuEasyExcelSession.getPageNo());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, subTripDetailInputDTO);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, SubTripDetailExport.class);
        if(restResponse!=null && restResponse.isSuccess()) {
            PageDTO pageDTO = (PageDTO) restResponse.getData();
            if (pageDTO.getTotal()>10000){
                throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
            }
            return pageDTO;
        }else{
            return null;
        }
    }

    @PostMapping(MrcarOrderRestMsgCenter.PROVIDER_BUSINESS_EXPENDITURE_GET_SUB_TRIP_DETAIL_LIST_FOR_ADD)
    @RequestFunction(functionName = "供应商支出单详情页子行程列表(添加使用)-运营端")
    @ApiOperation(value = "供应商支出单详情页子行程列表（添加使用）-运营端",notes = "作者：dingweibing")
    public RestResponse<Page<SubTripDetailForAddOutputDTO>> getSubTripDetailListForAdd(@RequestBody @Valid SubTripDetailForAddInputDTO subTripDetailForAddInputDTO){
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.PROVIDER_BUSINESS_EXPENDITURE_GET_SUB_TRIP_DETAIL_LIST_FOR_ADD);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, subTripDetailForAddInputDTO);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, SubTripDetailForAddOutputDTO.class);
    }
    @RequestFunction(functionName = "新建供应商支出单-查询发票类型和税率-运营端")
    @ApiOperation(value = "新建供应商支出单-查询发票类型和税率-运营端",notes = "作者：dingweibing")
    @GetMapping(ConfigURI.GET_COMPANY_CONFIG_BY_COMPANY_CODE)
    public RestResponse<CompanyTaxConfig> getCompanyConfigByCodeList(@RequestParam(value = "companyId") @Verify(param="companyId",rule="required")Integer companyId){
        String url = new MrCarConfigRestLocator().getRestUrl(ConfigURI.GET_COMPANY_CONFIG_BY_COMPANY_CODE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("companyId", companyId);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.GET, url, paramMap, null, CompanyTaxConfig.class);
    }

    @RequestFunction(functionName = "供应商支出单详情页-运营端")
    @ApiOperation(value = "供应商支出单详情页-运营端",notes = "作者：dingweibing")
    @PostMapping(MrcarOrderRestMsgCenter.PROVIDER_BUSINESS_EXPENDITURE_GET_SUPPLIER_SPEND_DETAIL)
    public RestResponse<SupplierSpendDetailOutputDTO> getSupplierSpendDetail(@RequestBody @Valid SupplierSpendDetailInputDTO supplierSpendDetailInputDTO){
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.PROVIDER_BUSINESS_EXPENDITURE_GET_SUPPLIER_SPEND_DETAIL);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, supplierSpendDetailInputDTO);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, SupplierSpendDetailOutputDTO.class);
    }

    @RequestFunction(functionName = "商务车供应商支出单-提交&废除&审批驳回&审批通过-运营端")
    @ApiOperation(value = "商务车供应商支出单-提交&废除&审批驳回&审批通过-运营端",notes = "作者：dingweibing")
    @PostMapping(MrcarOrderRestMsgCenter.PROVIDER_BUSINESS_EXPENDITURE_UPDATE_STATUS)
    public RestResponse<UpdateExpenditureStatusOutputDTO> updateExpenditureStatus(@RequestBody @Valid UpdateExpenditureStatusInputDTO updateExpenditureStatusInputDTO){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        updateExpenditureStatusInputDTO.setLoginUserId(providerLoginInfo.obtainBaseInfo().getStaffId());
        updateExpenditureStatusInputDTO.setLoginUserName(providerLoginInfo.obtainBaseInfo().getStaffName());
        updateExpenditureStatusInputDTO.setLoginCode(providerLoginInfo.obtainBaseInfo().getStaffCode());
        updateExpenditureStatusInputDTO.setLoginUserMobile(providerLoginInfo.obtainBaseInfo().getMobile());
        if(providerLoginInfo.getBelongDepartmentList()!=null&&providerLoginInfo.getBelongDepartmentList().size()!=0){
            updateExpenditureStatusInputDTO.setLoginDeptCode(providerLoginInfo.getBelongDepartmentList().stream().map(AccountBelongDepartment::getDeptCode).collect(Collectors.joining(",")));
            updateExpenditureStatusInputDTO.setLoginDeptName(providerLoginInfo.getBelongDepartmentList().stream().map(AccountBelongDepartment::getDeptName).collect(Collectors.joining(",")));
        }
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.PROVIDER_BUSINESS_EXPENDITURE_UPDATE_STATUS);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, updateExpenditureStatusInputDTO);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, UpdateExpenditureStatusOutputDTO.class);
    }
}
