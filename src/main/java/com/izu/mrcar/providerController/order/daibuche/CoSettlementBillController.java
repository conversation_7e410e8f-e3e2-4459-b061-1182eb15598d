package com.izu.mrcar.providerController.order.daibuche;

import cn.hutool.core.collection.CollectionUtil;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.controller.export.CoCompanyBillExcelDTO;
import com.izu.mrcar.controller.export.CoOrderBillExcelDTO;
import com.izu.mrcar.controller.export.CoSingleBillExcelDTO;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.consts.daibuche.ExportConstants;
import com.izu.mrcar.order.dto.LoginUserInfo;
import com.izu.mrcar.order.dto.daibuche.bill.req.*;
import com.izu.mrcar.order.dto.daibuche.bill.resp.*;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.NioExcelExportUtil;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

import static com.izu.mrcar.order.MrcarOrderRestMsgCenter.*;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@SuppressWarnings("all")
@Api(tags = "代步车-账单")
public class CoSettlementBillController extends CoBaseController {

    private final MrcarOrderRestLocator mrcarOrderRestLocator = new MrcarOrderRestLocator();

    private static final Integer EXPORT_LIMIT = 10000;

    @ApiOperation(value = "账单明细列表分页查询")
    @RequestFunction(functionName = "订单账单列表分页查询")
    @PostMapping(value = CO_ORDER_BILL_PAGE_LIST)
    public RestResponse<PageDTO<CoOrderBillResDTO>> orderBillPageList(@RequestBody @Validated CoOrderBillReqDTO reqDTO) {
        reqDTO.setLoginUser(getLoginUserInfo());
        String restUrl = mrcarOrderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.CO_ORDER_BILL_PAGE_LIST);
        Map<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }

    @ApiOperation(value = "账单明细详情查询")
    @RequestFunction(functionName = "订单账单详情查询")
    @PostMapping(value = CO_ORDER_BILL_DETAIL)
    public RestResponse<CoOrderBillDetailResDTO> orderBillDetail(@RequestBody @Validated CoOrderBillDetailReqDTO reqDTO) {
        reqDTO.setLoginUser(getLoginUserInfo());
        String restUrl = mrcarOrderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.CO_ORDER_BILL_DETAIL);
        Map<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }

    @ApiOperation(value = "账单明细调整保存")
    @RequestFunction(functionName = "订单账单费用明细调整保存")
    @PostMapping(value = CO_ORDER_BILL_ADJUST_SAVE)
    public RestResponse<Void> orderBillAdjustSave(@RequestBody @Validated CoOrderBillAdjustSaveDTO saveDTO) {
        saveDTO.setLoginUser(getLoginUserInfo());
        String restUrl = mrcarOrderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.CO_ORDER_BILL_ADJUST_SAVE);
        Map<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, saveDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }

    @ApiOperation(value = "单车收入账单列表分页查询")
    @RequestFunction(functionName = "单车收入账单列表分页查询")
    @PostMapping(value = CO_SINGLE_BILL_PAGE_LIST)
    public RestResponse<PageDTO<CoSingleBillResDTO>> singleBillPageList(@RequestBody @Validated CoSingleBillReqDTO reqDTO) {
        reqDTO.setLoginUser(getLoginUserInfo());
        String restUrl = mrcarOrderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.CO_SINGLE_BILL_PAGE_LIST);
        Map<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }

    @ApiOperation(value = "总收入账单列表分页查询")
    @RequestFunction(functionName = "总收入账单列表分页查询")
    @PostMapping(value = CO_COMPANY_BILL_PAGE_LIST)
    public RestResponse<PageDTO<CoCompanyBillResDTO>> companyBillPageList(@RequestBody @Validated CoCompanyBillReqDTO reqDTO) {
        reqDTO.setLoginUser(getLoginUserInfo());
        String restUrl = mrcarOrderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.CO_COMPANY_BILL_PAGE_LIST);
        Map<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }

    @ApiOperation(value = "总收入账单详情查询")
    @RequestFunction(functionName = "总收入账单详情查询")
    @PostMapping(value = CO_COMPANY_BILL_DETAIL)
    public RestResponse<CoCompanyBillResDTO> companyBillDetail(@RequestBody @Validated CoCompanyBillDetailReqDTO reqDTO) {
        reqDTO.setLoginUser(getLoginUserInfo());
        String restUrl = mrcarOrderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.CO_COMPANY_BILL_DETAIL);
        Map<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }

    @ApiOperation(value = "总收入账单确认")
    @RequestFunction(functionName = "总收入账单确认（只有账单状态为：账单待确认 时支持该操作）")
    @PostMapping(value = CO_COMPANY_BILL_CONFIRM)
    public RestResponse<Void> companyBillConfirm(@RequestBody @Validated CoCompanyBillConfirmReqDTO reqDTO) {
        reqDTO.setLoginUser(getLoginUserInfo());
        String restUrl = mrcarOrderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.CO_COMPANY_BILL_CONFIRM);
        Map<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }

    @ApiOperation(value = "总收入账单重新推送")
    @RequestFunction(functionName = "总收入账单重新推送（只有账单状态为：账单推送失败 时支持该操作）")
    @PostMapping(value = CO_COMPANY_BILL_PUSH)
    public RestResponse<Void> companyBillPush(@RequestBody @Validated CoCompanyBillConfirmReqDTO reqDTO) {
        reqDTO.setLoginUser(getLoginUserInfo());
        String restUrl = mrcarOrderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.CO_COMPANY_BILL_PUSH);
        Map<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }


    @ApiOperation(value = "总收入账单导出")
    @RequestFunction(functionName = "总收入账单导出")
    @ExportExcelWeb(fileName = ExportConstants.CO_COMPANY_BILL_EXPORT_FILE_NAME, filePath = ExportConstants.CO_EXPORT_FILE_PATH_COMMON,
            sheet = ExportConstants.CO_COMPANY_BILL_EXPORT_SHEET_NAME, c = CoCompanyBillExcelDTO.class)
    @PostMapping(value = CO_COMPANY_BILL_EXPORT)
    public Object companyBillExport(@RequestBody CoCompanyBillReqDTO reqDTO,
                                    IzuEasyExcelSession izuEasyExcelSession,
                                    HttpServletResponse response) {
        reqDTO.setLoginUser(getLoginUserInfo());
        reqDTO.setPageNum(izuEasyExcelSession.getPageNo());
        reqDTO.setPageSize(EXPORT_LIMIT);
        String restUrl = mrcarOrderRestLocator.getRestUrl(CO_COMPANY_BILL_PAGE_LIST);
        Map<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        izuEasyExcelSession.setUserName(reqDTO.getLoginUser().getLoginUserName());
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null, CoCompanyBillResDTO.class);
        if (Objects.nonNull(restResponse) && restResponse.isSuccess()) {
            PageDTO<CoCompanyBillResDTO> pageInfo = (PageDTO<CoCompanyBillResDTO>) restResponse.getData();
            if (pageInfo.getTotal() > EXPORT_LIMIT) {
                throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
            }
            if (!CollectionUtils.isEmpty(pageInfo.getResult())) {
                List<CoCompanyBillExcelDTO> list = BeanUtil.copyList(pageInfo.getResult(), CoCompanyBillExcelDTO.class);
                return new PageDTO<>(pageInfo.getPage(), pageInfo.getPageSize(), pageInfo.getTotal(), list);
            }
        }
        return null;
    }

    @ApiOperation(value = "单车收入账单导出")
    @RequestFunction(functionName = "单车收入账单导出")
    @ExportExcelWeb(fileName = ExportConstants.CO_SINGLE_BILL_EXPORT_FILE_NAME, filePath = ExportConstants.CO_EXPORT_FILE_PATH_COMMON,
            sheet = ExportConstants.CO_SINGLE_BILL_EXPORT_SHEET_NAME, c = CoSingleBillExcelDTO.class)
    @PostMapping(value = CO_SINGLE_BILL_EXPORT)
    public Object singleBillExport(@RequestBody CoSingleBillReqDTO reqDTO,
                                   IzuEasyExcelSession izuEasyExcelSession,
                                   HttpServletResponse response) {
        reqDTO.setLoginUser(getLoginUserInfo());
        reqDTO.setPageNum(izuEasyExcelSession.getPageNo());
        reqDTO.setPageSize(EXPORT_LIMIT);
        String restUrl = mrcarOrderRestLocator.getRestUrl(CO_SINGLE_BILL_PAGE_LIST);
        Map<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        izuEasyExcelSession.setUserName(reqDTO.getLoginUser().getLoginUserName());
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null, CoSingleBillResDTO.class);
        if (Objects.nonNull(restResponse) && restResponse.isSuccess()) {
            PageDTO<CoSingleBillResDTO> pageInfo = (PageDTO<CoSingleBillResDTO>) restResponse.getData();
            if (pageInfo.getTotal() > EXPORT_LIMIT) {
                throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
            }
            if (!CollectionUtils.isEmpty(pageInfo.getResult())) {
                List<CoSingleBillExcelDTO> list = BeanUtil.copyList(pageInfo.getResult(), CoSingleBillExcelDTO.class);
                return new PageDTO<>(pageInfo.getPage(), pageInfo.getPageSize(), pageInfo.getTotal(), list);
            }
        }
        return null;
    }

    @ApiOperation(value = "账单明细导出")
    @RequestFunction(functionName = "订单账单导出")
    @ExportExcelWeb(fileName = ExportConstants.CO_ORDER_BILL_EXPORT_FILE_NAME, filePath = ExportConstants.CO_EXPORT_FILE_PATH_COMMON,
            sheet = ExportConstants.CO_ORDER_BILL_EXPORT_SHEET_NAME, c = CoOrderBillExcelDTO.class)
    @PostMapping(value = CO_ORDER_BILL_EXPORT)
    public Object orderBillExport(@RequestBody CoOrderBillReqDTO reqDTO,
                                  IzuEasyExcelSession izuEasyExcelSession,
                                  HttpServletResponse response) {
        reqDTO.setLoginUser(getLoginUserInfo());
        reqDTO.setPageNum(izuEasyExcelSession.getPageNo());
        reqDTO.setPageSize(EXPORT_LIMIT);
        String restUrl = mrcarOrderRestLocator.getRestUrl(CO_ORDER_BILL_EXHAUSTIVE_LIST);
        Map<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        izuEasyExcelSession.setUserName(reqDTO.getLoginUser().getLoginUserName());
        RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null, CoOrderBillExhaustiveResDTO.class);
        if (Objects.nonNull(restResponse) && restResponse.isSuccess()) {
            List<CoOrderBillExhaustiveResDTO> result = (List<CoOrderBillExhaustiveResDTO>) restResponse.getData();
            if (result.size() > EXPORT_LIMIT) {
                throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
            }
            List<CoOrderBillExcelDTO> coOrderBillExcelDTOS = BeanUtil.copyList(result, CoOrderBillExcelDTO.class);
            coOrderBillExcelDTOS.stream().forEach(order -> {
                if(StringUtils.isNotEmpty(order.getThirdOrderNo())){
                    String[] arr = order.getThirdOrderNo().split("_");
                    order.setThirdOrderNo(arr[0]);
                }
            });
            return new PageDTO<>(1, result.size(), result.size(), coOrderBillExcelDTOS);
        }
        return null;
    }

    @ApiOperation("按照合作方（样式）导出")
    @PostMapping("/commuting/orderBill/exportByPartner")
    public RestResponse exportByCooperator(@RequestBody CoOrderBillReqDTO reqDTO, HttpServletRequest request, HttpServletResponse response) {
        reqDTO.setLoginUser(getLoginUserInfo());
        reqDTO.setPageNum(1);
        reqDTO.setPageSize(EXPORT_LIMIT);
        String restUrl = mrcarOrderRestLocator.getRestUrl(CO_ORDER_BILL_EXHAUSTIVE_LIST);
        Map<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null, CoOrderBillExhaustiveResDTO.class);
        if (Objects.isNull(restResponse) || !restResponse.isSuccess()) {
            throw ExceptionFactory.createRestException(ErrorCode.CALL_SYSTEM_ERROR);
        }
        List<CoOrderBillExhaustiveResDTO> result = CollectionUtil.defaultIfEmpty((List<CoOrderBillExhaustiveResDTO>) restResponse.getData(), Lists.newArrayList());
        if (result.size() > EXPORT_LIMIT) {
            throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
        }
        //蔚来导出数据按确认送达时间正序进行排序
        Map<String, List<LinkedHashMap<String, Object>>> data = result.stream().sorted(Comparator.comparing(CoOrderBillExhaustiveResDTO::getActualUseTime))
                .map(e -> NioExcelExportUtil.convertToNioExcelMap(e)).collect(Collectors.groupingBy(e -> String.valueOf(e.get("realCity"))));
        try (OutputStream outputStream = response.getOutputStream()) {
            Workbook workbook = NioExcelExportUtil.exportExcelWithMultipleSheet(data);
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            String fileName = URLEncoder.encode("蔚来代步车对账单", "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            workbook.write(outputStream);
        } catch (Exception e) {
            log.error("导出蔚来账单Excel异常", e);
        }
        return null;
    }

    @ApiOperation(value = "订单账单推送")
    @RequestFunction(functionName = "订单账单推送（只有账单状态为：账单推送失败 时支持该操作）")
    @PostMapping(value = CO_ORDER_BILL_PUSH)
    public RestResponse<Void> companyOrderBillPush(@RequestBody @Validated CoOrderBillPushReqDTO reqDTO) {
        LoginUserInfo loginUserInfo = new LoginUserInfo();
        AccountBaseInfo accountBaseInfo = LoginSessionUtil.getProviderLoginInfo().getBaseInfo();
        reqDTO.setLoginUserId(accountBaseInfo.getStaffId());
        reqDTO.setLoginUserName(accountBaseInfo.getStaffName());
        String restUrl = mrcarOrderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.CO_ORDER_BILL_PUSH);
        Map<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }

}