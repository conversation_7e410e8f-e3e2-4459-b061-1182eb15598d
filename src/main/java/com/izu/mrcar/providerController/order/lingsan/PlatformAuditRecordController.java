package com.izu.mrcar.providerController.order.lingsan;

import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.dto.lingsan.audit.AuditBaseDTO;
import com.izu.mrcar.order.dto.lingsan.audit.AuditDTO;
import com.izu.mrcar.order.dto.lingsan.audit.DataReturnDTO;
import com.izu.mrcar.order.dto.lingsan.audit.AuditVehicleReturnDTO;
import com.izu.mrcar.order.dto.lingsan.audit.req.AuditPageReqDTO;
import com.izu.mrcar.order.dto.lingsan.audit.req.AuditSaveAppReqDTO;
import com.izu.mrcar.order.dto.lingsan.audit.res.AuditDetailResDTO;
import com.izu.mrcar.order.dto.lingsan.bill.LoginUserDTO;
import com.izu.mrcar.order.dto.lingsan.vehicleChangeOrder.VehicleChangeOrderPageDTO;
import com.izu.mrcar.shiro.session.WebSessionUtil;
import com.izu.mrcar.utils.lingsan.PermissionUtil;
import com.izu.user.dto.LoginUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * @Program: mrcar
 * @Description: 审核单-平台
 * @Author: inT
 * @Create: 2023-02-06
 */
@Api(tags = "零散用车")
@Slf4j
@RestController
@SuppressWarnings("all")
public class PlatformAuditRecordController {

    @GetMapping(MrcarOrderRestMsgCenter.LINGSAN_PLATFORM_AUDIT_RECORD_AUDIT_TYPEANDSTATUS_LIST)
    @ApiOperation(value = "审核-审核类型和审核状态")
    @RequestFunction(functionName = "审核类型和审核状态")
    public RestResponse listAuditTypeAndStatus() {
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.LINGSAN_PLATFORM_AUDIT_RECORD_AUDIT_TYPEANDSTATUS_LIST);
        RestResponse restResponse = RestClient.requestForObject(BaseHttpClient.HttpMethod.GET, restUrl, new HashMap<>(2), null, Object.class);
        return restResponse;
    }

    /**
     * 审核记录分页列表查询
     * @param auditPageReqDTO
     * @return
     */
    @ApiOperation(value = "审核-审核记录分页列表查询")
    @RequestFunction(functionName = "审核记录分页列表查询")
    @PostMapping(MrcarOrderRestMsgCenter.LINGSAN_PLATFORM_AUDIT_RECORD_PAGE_LIST)
    public RestResponse<PageDTO<AuditDTO>> auditRecordPageList(@RequestBody AuditPageReqDTO auditPageReqDTO) {
        Map<String, Object> paramMap = new HashMap<>();
        auditPageReqDTO.setLoginUser(PermissionUtil.getLoginUserInfo());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, auditPageReqDTO);
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.LINGSAN_PLATFORM_AUDIT_RECORD_PAGE_LIST);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, AuditDTO.class);

    }



    /**
     * 审核单详情
     * @param auditRecordNum
     * @return
     */
    @ApiOperation(value = "审核-审核单详情")
    @RequestFunction(functionName = "审核单详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name="auditRecordNum",value="审核单单号",paramType="String"),
    })
    @GetMapping(MrcarOrderRestMsgCenter.LINGSAN_PLATFORM_AUDIT_RECORD_DETAIL)
    public RestResponse<AuditDetailResDTO> getAuditRecordDetail(@RequestParam("auditRecordNum") String auditRecordNum) {
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.LINGSAN_PLATFORM_AUDIT_RECORD_DETAIL);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("auditRecordNum", auditRecordNum);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.GET, url, paramMap, null, AuditDetailResDTO.class);

    }

    /**
     * 保存审核结果
     * @param auditBaseDTO
     * @return
     */
    @ApiOperation(value = "审核-保存审核结果")
    @RequestFunction(functionName = "保存审核结果")
    @PostMapping(MrcarOrderRestMsgCenter.LINGSAN_PLATFORM_AUDIT_RECORD_AUDIT_RESULT_SAVE)
    public RestResponse saveAuditResult(@RequestBody AuditBaseDTO auditBaseDTO) {
        auditBaseDTO.setLoginUser(PermissionUtil.getLoginUserInfo());
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, auditBaseDTO);
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.LINGSAN_PLATFORM_AUDIT_RECORD_AUDIT_RESULT_SAVE);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null);
    }

    @ApiOperation(value = "审核-已完成-待回传资料列表")
    @RequestFunction(functionName = "已完成-待回传资料列表")
    @GetMapping(MrcarOrderRestMsgCenter.LINGSAN_PLATFORM_AUDIT_RECORD_DATA_RETURN_LIST)
    public RestResponse<DataReturnDTO> listDataReturnDTOs(@RequestParam("customerOrderNum") String customerOrderNum) {
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.LINGSAN_PLATFORM_AUDIT_RECORD_DATA_RETURN_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("customerOrderNum", customerOrderNum);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.GET, url, paramMap, null, DataReturnDTO.class);
    }


    @ApiOperation(value = "审核-新建保存")
    @RequestFunction(functionName = "新建保存")
    @PostMapping(MrcarOrderRestMsgCenter.LINGSAN_PLATFORM_AUDIT_RECORD_SAVE)
    public RestResponse saveAuditRecord(@RequestBody AuditSaveAppReqDTO auditSaveAppReqDTO) {
        auditSaveAppReqDTO.setLoginUser(PermissionUtil.getLoginUserInfo());
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.LINGSAN_PLATFORM_AUDIT_RECORD_SAVE);
        Map<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, auditSaveAppReqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);

    }







}
