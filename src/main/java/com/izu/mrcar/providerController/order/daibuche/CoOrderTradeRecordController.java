package com.izu.mrcar.providerController.order.daibuche;


import com.google.common.collect.Maps;
import com.izu.config.dto.daibuche.resp.CoOrderWhiteListExportRespDTO;

import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.doc.JrdApiDoc;
import com.izu.framework.doc.JrdDoc;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.consts.daibuche.TradeChannelEnum;
import com.izu.mrcar.order.consts.daibuche.TradeTypeEnum;
import com.izu.mrcar.order.dto.daibuche.order.req.CoOrderTradeRecordQueryReqDTO;
import com.izu.mrcar.order.dto.daibuche.order.req.CoOrderTradeRecordSaveReqDTO;
import com.izu.mrcar.order.dto.daibuche.order.resp.CoOrderTradeRecordExportRespDTO;
import com.izu.mrcar.order.dto.daibuche.order.resp.CoOrderTradeRecordQueryRespDTO;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.izu.mrcar.order.MrcarOrderRestMsgCenter.*;


@JrdDoc(name = "代步车-交易记录")
@RestController
@Slf4j
public class CoOrderTradeRecordController extends CoBaseController {

    private static final Integer PAGE_SIZE = 10000;

    @PostMapping(value = DAIBUCHE_ORDER_TRADE_RECORD_LIST)
    @JrdApiDoc(simpleDesc = "交易记录列表查询", author = "hhd", resDataClass = CoOrderTradeRecordQueryRespDTO.class)
    @RequestFunction(functionName = "交易记录列表查询")
    public RestResponse<PageDTO<CoOrderTradeRecordQueryRespDTO>> queryListByPage(@RequestBody CoOrderTradeRecordQueryReqDTO reqDTO) {
        Map<String, Object> param = new HashMap<>();
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        String restUrl = new MrcarOrderRestLocator().getRestUrl(DAIBUCHE_ORDER_TRADE_RECORD_LIST);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null, CoOrderTradeRecordQueryRespDTO.class);
    }

    @JrdApiDoc(simpleDesc = "交易记录保存", author = "hhd", resDataClass = Object.class)
    @PostMapping(value = DAIBUCHE_ORDER_TRADE_RECORD_SAVE)
    @RequestFunction(functionName = "交易记录保存")
    public RestResponse saveOrderWhiteList(@RequestBody @Validated CoOrderTradeRecordSaveReqDTO reqDTO) {
        String restUrl = new MrcarOrderRestLocator().getRestUrl(DAIBUCHE_ORDER_TRADE_RECORD_SAVE);
        Map<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }

    @PostMapping(value = DAIBUCHE_ORDER_TRADE_RECORD_EXPORT)
    @JrdApiDoc(simpleDesc = "交易记录导出", author = "hhd", resDataClass = CoOrderTradeRecordQueryRespDTO.class)
    @RequestFunction(functionName = "交易记录导出")
    @ExportExcelWeb(fileName = ExportExcelConstants.FILE_NAME_ORDER_WHITE_LIST_EXPORT_INFO ,filePath = ExportExcelConstants.FILE_PATH,
            sheet = ExportExcelConstants.SHEET_NAME_ORDER_WHITE_LIST_EXPORT_INFO, c = CoOrderTradeRecordExportRespDTO.class)
    public PageDTO<CoOrderTradeRecordExportRespDTO> export(@RequestBody CoOrderTradeRecordQueryReqDTO reqDTO, IzuEasyExcelSession izuEasyExcelSession,
                                                  HttpServletRequest request, HttpServletResponse response) {
        String restUrl = new MrcarOrderRestLocator().getRestUrl(DAIBUCHE_ORDER_TRADE_RECORD_LIST);
        AccountBaseInfo baseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
        izuEasyExcelSession.setUserName(baseInfo.getStaffName());
        reqDTO.setPage(izuEasyExcelSession.getPageNo());
        reqDTO.setPageSize(PAGE_SIZE);

        final HashMap<String, Object> restParam = Maps.newHashMapWithExpectedSize(1);
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        RestResponse<PageDTO<CoOrderTradeRecordQueryRespDTO>> res = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null, CoOrderTradeRecordQueryRespDTO.class);
        if(null!=res && res.isSuccess()){
            PageDTO<CoOrderTradeRecordQueryRespDTO> pageDTO = res.getData();
            List<CoOrderTradeRecordExportRespDTO> list = BeanUtil.copyList(pageDTO.getResult(),CoOrderTradeRecordExportRespDTO.class);
            return new PageDTO<>(pageDTO.getPage(), pageDTO.getPageSize(), pageDTO.getTotal(), list);
        }else {
            return null;
        }
    }

    @GetMapping(value = DAIBUCHE_ORDER_TRADE_RECORD_GET_TRADE_TYPE)
    @JrdApiDoc(simpleDesc = "交易类型枚举", detailDesc = "", author = "hhd", resDataClass = RestResponse.class)
    @RequestFunction(functionName = "交易类型枚举")
    public RestResponse getTradeType(){
        List<Map<String, Object>> mapList = Arrays.stream(TradeTypeEnum.values()).map(item -> {
            Map<String, Object> map = new HashMap<>();
            map.put("key", item.getCode());
            map.put("value", item.getName());
            return map;
        }).collect(Collectors.toList());
        return RestResponse.success(mapList);
    }

    @GetMapping(value = DAIBUCHE_ORDER_TRADE_RECORD_GET_TRADE_CHANNEL)
    @JrdApiDoc(simpleDesc = "支付渠道枚举", detailDesc = "", author = "hhd", resDataClass = RestResponse.class)
    @RequestFunction(functionName = "支付渠道枚举")
    public RestResponse getTradeChannel(){
        List<Map<String, Object>> mapList = Arrays.stream(TradeChannelEnum.values()).map(item -> {
            Map<String, Object> map = new HashMap<>();
            map.put("key", item.getCode());
            map.put("value", item.getName());
            return map;
        }).collect(Collectors.toList());
        return RestResponse.success(mapList);
    }
}
