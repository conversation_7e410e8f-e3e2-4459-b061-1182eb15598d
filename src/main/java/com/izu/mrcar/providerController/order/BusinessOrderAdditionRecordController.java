package com.izu.mrcar.providerController.order;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.izu.excel.annotation.ExportExcel;
import com.izu.excel.download.AbstractIzuWebExcel;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.controller.base.OrderBaseController;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.dto.businessOrder.*;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.provider.driver.DriverDropdownRespDTO;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 商务车订单补录 api
 */
@Api(tags = "商务订单补录")
@RestController
@Slf4j
public class BusinessOrderAdditionRecordController extends OrderBaseController implements AbstractIzuWebExcel {

    @ApiOperation(value = "新增补录商务订单记录", notes = "author:niuzilan")
    @RequestFunction(functionName = "新增补录商务订单记录")
    @PostMapping(MrcarOrderRestMsgCenter.BUSINESS_ORDER_ADDITION_RECORD_SAVE)
    public RestResponse<Boolean> saveAdditionRecord(@RequestBody BusinessOrderAdditionRecordSaveReqDTO reqDTO) {
        return postBodyWithLogin(MrcarOrderRestMsgCenter.BUSINESS_ORDER_ADDITION_RECORD_SAVE, reqDTO);
    }

    @ApiOperation(value = "修改补录商务订单记录", notes = "author:niuzilan")
    @RequestFunction(functionName = "修改补录商务订单记录")
    @PostMapping(MrcarOrderRestMsgCenter.BUSINESS_ORDER_ADDITION_RECORD_UPDATE)
    public RestResponse<Boolean> updateAdditionRecord(@RequestBody BusinessOrderAdditionRecordUpdateReqDTO reqDTO) {
        return postBodyWithLogin(MrcarOrderRestMsgCenter.BUSINESS_ORDER_ADDITION_RECORD_UPDATE, reqDTO);
    }

    @ApiOperation(value = "商务订单记录分页列表", notes = "author:niuzilan")
    @RequestFunction(functionName = "商务订单记录分页列表")
    @PostMapping(MrcarOrderRestMsgCenter.BUSINESS_ORDER_ADDITION_RECORD_PAGE_LIST)
    public RestResponse<PageDTO<BusinessOrderAdditionRecordRespDTO>> pageList(@RequestBody BusinessOrderAdditionRecordListReqDTO reqDTO) {
        return postBodyWithLoginForPage(MrcarOrderRestMsgCenter.BUSINESS_ORDER_ADDITION_RECORD_PAGE_LIST, reqDTO,BusinessOrderAdditionRecordRespDTO.class);
    }

    @ApiOperation(value = "补录商务订单记录详情", notes = "author:niuzilan")
    @RequestFunction(functionName = "补录商务订单记录详情")
    @ApiImplicitParam(name = "recordId",value = "订单记录ID",required = true,paramType="form")
    @RequestMapping(value = MrcarOrderRestMsgCenter.BUSINESS_ORDER_ADDITION_RECORD_DETAIL,method = {RequestMethod.GET,RequestMethod.POST})
    public RestResponse<BusinessOrderAdditionRecordRespDTO> detail(@Verify(param = "recordId", rule = "required") Integer recordId) {
        HashMap<String, Object> param = new HashMap<>();
        param.put("recordId", recordId);
        return post(MrcarOrderRestMsgCenter.BUSINESS_ORDER_ADDITION_RECORD_DETAIL, param);
    }


    @ApiOperation(value = "废除商务订单记录", notes = "author:niuzilan")
    @RequestFunction(functionName = "废除商务订单记录")
    @PostMapping(MrcarOrderRestMsgCenter.BUSINESS_ORDER_ADDITION_RECORD_ABOLISH)
    public RestResponse<Boolean> abolish(@RequestBody BusinessOrderAdditionRecordStatusReqDTO reqDTO) {
        return postBodyWithLogin(MrcarOrderRestMsgCenter.BUSINESS_ORDER_ADDITION_RECORD_ABOLISH, reqDTO);
    }

    @ApiOperation(value = "提交订单记录", notes = "author:niuzilan")
    @RequestFunction(functionName = "提交订单记录")
    @PostMapping(MrcarOrderRestMsgCenter.BUSINESS_ORDER_ADDITION_RECORD_SUBMIT_TO_LEASE)
    public RestResponse<Boolean> submitToLease(@RequestBody BusinessOrderAdditionRecordStatusReqDTO reqDTO) {
        return postBodyWithLogin(MrcarOrderRestMsgCenter.BUSINESS_ORDER_ADDITION_RECORD_SUBMIT_TO_LEASE, reqDTO);
    }


    /**
     * 补录订单可选择的车辆列表
     *
     * @param vehicleLicense
     * @return
     */
    @ApiOperation(value = "商务车补录订单可选择的车辆列表", notes = "author:niuzilan")
    @RequestFunction(functionName = "商务车补录订单可选择的车辆列表")
    @ApiImplicitParam(name = "vehicleLicense",value = "车牌号",required = false,paramType="form")
    @RequestMapping(value=MrcarOrderRestMsgCenter.BUSINESS_ORDER_ADDITION_RECORD_VEHICLE_LIST,method = {RequestMethod.GET,RequestMethod.POST})
    public RestResponse<List<BusinessOrderAdditionRecordVehicleRespDTO>> selectableVehicleList(String vehicleLicense) {
        HashMap<String, Object> param = new HashMap<>();
        param.put("vehicleLicense", vehicleLicense);
        return post(MrcarOrderRestMsgCenter.BUSINESS_ORDER_ADDITION_RECORD_VEHICLE_LIST, param);
    }


    /**
     * 可调度的司机列表
     *
     * @param driverName
     * @return
     */
    @ApiOperation(value = "商务车补录订单可选择的司机列表", notes = "author:niuzilan")
    @RequestFunction(functionName = "商务车补录订单可选择的司机列表")
    @ApiImplicitParam(name = "driverName",value = "司机姓名",required = false,paramType="form")
    @RequestMapping(value=MrcarOrderRestMsgCenter.BUSINESS_ORDER_ADDITION_RECORD_DRIVER_LIST,method = {RequestMethod.POST,RequestMethod.GET})
    public RestResponse<List<DriverDropdownRespDTO>> selectableDriverList(String driverName) {
        HashMap<String, Object> param = new HashMap<>();
        param.put("driverName", driverName);
        return post(MrcarOrderRestMsgCenter.BUSINESS_ORDER_ADDITION_RECORD_DRIVER_LIST, param);
    }


    @Override
    public PageDTO getWebDataByPage(Map<String, Object> map, int page, int pageSize) {
        BusinessOrderAdditionRecordListReqDTO param = JSONObject.parseObject(JSON.toJSONString(map), BusinessOrderAdditionRecordListReqDTO.class);
        param.setPage(page);
        //微服务限制最大pageSize为100
        param.setPageSize(100);
        RestResponse<PageDTO<BusinessOrderAdditionRecordRespDTO>> restResponse = this.pageList(param);
        if(restResponse.isSuccess()){
            return restResponse.getData();
        }else{
            log.error("调用order获取商务车补录订单列表失败;response={}", JSON.toJSONString(restResponse));
            return null;
        }
    }
    @ApiOperation(value = "商务订单记录导出", notes = "author:niuzilan")
    @RequestFunction(functionName = "商务订单记录导出")
    @PostMapping(MrcarOrderRestMsgCenter.BUSINESS_ORDER_ADDITION_RECORD_DRIVER_EXPORT)
    @ExportExcel(fileName = "商务车补录订单导出.xlsx",
            filePath = "/data/logs/mrcar/tmp",
            sheet = "商务车补录订单"
    )
    public void export(@RequestBody BusinessOrderAdditionRecordListReqDTO reqDTO, HttpServletResponse response){
        //调用统一的web导出Excel工具类
        Map<String, Object> params = JSONObject.parseObject(JSONObject.toJSONString(reqDTO)).getInnerMap();
        final LoginBaseInfo loginInfo = LoginSessionUtil.getBaseLoginInfo();
        this.webRepeatExportExcel(response, params, BusinessOrderAdditionRecordRespDTO.class, loginInfo.obtainBaseInfo().getStaffName());
    }
}
