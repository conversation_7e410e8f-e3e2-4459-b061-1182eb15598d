package com.izu.mrcar.providerController.order;

import com.izu.config.MrCarConfigRestLocator;
import com.izu.config.dto.business.BusinessCompanyConfigDetailDTO;
import com.izu.config.dto.business.BusinessPriceSnapshotRespDTO;
import com.izu.config.dto.business.VehicleModelPriceReqDTO;
import com.izu.config.dto.business.VehicleModelPriceRespDTO;
import com.izu.consts.ConfigURI;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.config.ConstansConfig;
import com.izu.mrcar.controller.base.OrderBaseController;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.dto.OrderSimpleQueryDTO;
import com.izu.mrcar.order.dto.businessOrder.*;
import com.izu.mrcar.order.dto.mrcar.OrderPriceDTO;
import com.izu.mrcar.order.dto.order.OrderDiscountReqDTO;
import com.izu.mrcar.order.dto.order.OrderDiscountRespDTO;
import com.izu.mrcar.order.dto.provider.input.OrderModifyInputDTO;
import com.izu.mrcar.order.dto.provider.input.ProviderOrderInfoQueryDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> on  2023/12/16 10:48
 */
@Api(tags = "商务车订单")
@RestController
public class BusinessCarPriceAdjustmentController extends OrderBaseController {


    @PostMapping(MrcarOrderRestMsgCenter.PROVIDER_BUSINESS_ESTIMATE_AMOUNT)
    @ApiOperation(value = "获取商务车下单预估金额")
    @RequestFunction(functionName = "获取商务车下单预估金额")
    public RestResponse<BusinessOrderDTO> getPredictBusinessAmount(@RequestBody OrderEstimateReqDTO orderCreateReqDTO){

        String restUrl=new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.PROVIDER_BUSINESS_ESTIMATE_AMOUNT);
        Map<String, Object> httpParams=new HashMap<>();
        httpParams.put(BaseHttpClient.POSTBODY_MAP_KEY,orderCreateReqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, httpParams, null);
    }

    @ApiOperation("商务车下单权益卡校验")
    @RequestFunction(functionName = "商务车下单权益卡校验")
    @PostMapping(MrcarOrderRestMsgCenter.BENEFIT_CARD_CHECK)
    public RestResponse checkBenefitCard(@RequestBody BenefitCardCheckDTO benefitCardCheckDTO){

        String restUrl=new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.BENEFIT_CARD_CHECK);
        Map<String, Object> params = new HashMap<>(1);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, benefitCardCheckDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, params, null);
    }

    @ApiOperation(value = "商务车下单快捷词")
    @RequestFunction(functionName = "商务车下单快捷词")
    @PostMapping(MrcarOrderRestMsgCenter.BUSINESS_ORDER_FAST_REMARK)
    public RestResponse getBusinessRemark(){
        String businessRemark= ConstansConfig.getConfig().getBusinessRemark();
        String remark=businessRemark;
        String[] split = remark.split(",");
        List<String> remarks= Arrays.asList(split);
        return RestResponse.success(remarks);
    }


    /**
     * 调价
     */
    @ApiOperation(value = "商务车订单调价")
    @RequestFunction(functionName = "商务车订单调价")
    @PostMapping(MrcarOrderRestMsgCenter.BUSINESS_ORDER_ADJUST)
    public RestResponse<Boolean> adjustPrice(@RequestBody OrderModifyInputDTO orderModifyInputDTO) {
        return postBodyWithLogin(MrcarOrderRestMsgCenter.BUSINESS_ORDER_ADJUST, orderModifyInputDTO);
    }

    /**
     * 获取条件详情
     */
    @ApiOperation(value = "获取条件详情")
    @RequestFunction(functionName = "获取条件详情")
    @PostMapping(MrcarOrderRestMsgCenter.BUSINESS_GET_ORDER_ADJUST_INFO)
    public RestResponse<OrderPriceDTO> getOrderPriceInfo(@RequestBody ProviderOrderInfoQueryDTO orderInfoQueryDTO) {
        return postBody(MrcarOrderRestMsgCenter.BUSINESS_GET_ORDER_ADJUST_INFO, orderInfoQueryDTO);
    }
    /**调价记录**/
    @PostMapping(MrcarOrderRestMsgCenter.QUERY_ADJUST_PRICE_LOG)
    @ApiOperation("查询调价记录")
    @RequestFunction(functionName = "查询调价记录")
    public RestResponse<List<BillAdjustPriceLogDTO>> queryBillAdjustPriceLogList(@RequestBody OrderSimpleQueryDTO orderSimpleQueryDTO){
        return postBody(MrcarOrderRestMsgCenter.QUERY_ADJUST_PRICE_LOG,orderSimpleQueryDTO);
    }

    /**
     * 获取价格列表配置
     */
    @RequestFunction(functionName = "获取价格列表配置")
    @ApiOperation(value = "获取价格列表配置")
    @PostMapping(ConfigURI.GET_VEHICLE_MODEL_PRICE_SERVICE_LIST)
    public RestResponse<PageDTO<VehicleModelPriceRespDTO>> getVehicleModelPriceServiceList(@RequestBody VehicleModelPriceReqDTO reqDTO) {
        HashMap<String, Object> param = new HashMap<>();
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        String restUrl = new MrCarConfigRestLocator().getRestUrl(ConfigURI.GET_VEHICLE_MODEL_PRICE_SERVICE_LIST);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null);
    }

    @PostMapping(ConfigURI.BUSINESS_COMPANY_CONFIG_ONE_DETAIL)
    @RequestFunction(functionName = "根据客户编码获取商务车配置流程详情")
    @ApiOperation(value = "根据客户编码获取商务车配置流程详情",notes = "作者：chenxi3")
    @ApiImplicitParams(
            {@ApiImplicitParam(name = "customerCode",value = "客户编码", paramType = "form")})
    public RestResponse<BusinessCompanyConfigDetailDTO> getOneByCustomer(String customerCode){
        String url = new MrCarConfigRestLocator().getRestUrl(ConfigURI.BUSINESS_COMPANY_CONFIG_ONE_DETAIL);
        Map<String,Object> param = new HashMap<>();
        param.put("customerCode",customerCode);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, url, param, null, BusinessCompanyConfigDetailDTO.class);
    }

    /**
     * 三方服务是否支持使用权益卡
     */
    @RequestFunction(functionName = "三方服务是否支持使用权益卡")
    @ApiOperation(value = "三方服务是否支持使用权益卡")
    @GetMapping(MrcarOrderRestMsgCenter.CHECK_BENEFIT_CARD_THIRD_SUPPORT)
    public RestResponse checkBenefitCardThirdSupport(){
        return get(MrcarOrderRestMsgCenter.CHECK_BENEFIT_CARD_THIRD_SUPPORT,null);
    }


    /**
     * 获取商务车价格策略
     */
    @RequestFunction(functionName = "获取商务车价格策略")
    @ApiOperation(value = "获取商务车价格策略")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "orderNo", value = "订单号", paramType = "query")
    })
    @GetMapping(MrcarOrderRestMsgCenter.GET_BUSINESS_PRICE_STRATEGY)
    public RestResponse<BusinessPriceSnapshotRespDTO> getBusinessPriceStrategy(@Verify(param = "orderNo", rule = "required") String orderNo) {
        HashMap<String, Object> param = new HashMap<>();
        param.put("orderNo", orderNo);
        return get(MrcarOrderRestMsgCenter.GET_BUSINESS_PRICE_STRATEGY, param);
    }

    /**
     * 校验车辆或者司机有没有预计用车时间交叉的子行程
     */
    @RequestFunction(functionName = "校验车辆或者司机有没有预计用车时间交叉的子行程")
    @ApiOperation(value = "校验车辆或者司机有没有预计用车时间交叉的子行程")
    @PostMapping(MrcarOrderRestMsgCenter.CHECK_DRIVER_VEHICLE_TIME_CROSS)
    public RestResponse<Boolean> checkDriverVehicleTimeCross(@RequestBody CheckDriverVehicleTimeCrossReqDTO reqDTO){
        return postBody(MrcarOrderRestMsgCenter.CHECK_DRIVER_VEHICLE_TIME_CROSS,reqDTO);
    }

    /**
     * 获取权益卡使用明细
     */
    @RequestFunction(functionName = "获取权益卡使用明细")
    @ApiOperation(value = "获取权益卡使用明细")
    @GetMapping(MrcarOrderRestMsgCenter.GET_CAR_USAGE_DETAILS)
    public RestResponse<List<BusinessOrderCarUsageDetailRespDTO>> getCardUsageDetails(@Verify(param = "orderNo",rule = "required") String orderNo){
        HashMap<Object, Object> param = new HashMap<>();
        param.put("orderNo",orderNo);
        return get(MrcarOrderRestMsgCenter.GET_CAR_USAGE_DETAILS,param);
    }


    @PostMapping(MrcarOrderRestMsgCenter.QUERY_ORDER_DISCOUNT)
    @ApiOperation("子行程详情-查询优惠信息")
    public RestResponse<List<OrderDiscountRespDTO>> queryOrderDiscount(@RequestBody OrderDiscountReqDTO orderDiscountReqDTO){
        HashMap<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, orderDiscountReqDTO);
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.QUERY_ORDER_DISCOUNT);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }

}
