package com.izu.mrcar.providerController.order;

import com.google.common.collect.Maps;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.dto.order.OrderMileageCorrectDTO;
import com.izu.mrcar.order.dto.order.OrderMileageCorrectQueryDTO;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 里程纠偏记录
 * @date 2023/7/26 10:04
 */
@RestController
@Api(tags="里程纠偏")
public class ProviderMileageCorrectController {
    private static final long MAX_LINE = 10000;

    @ApiOperation(value = "纠偏记录列表",notes = "作者：连江伟")
    @PostMapping(MrcarOrderRestMsgCenter.PROVIDER_ORDER_MILEAGE_GET_PAGE_LIST)
    public RestResponse<PageDTO<OrderMileageCorrectDTO>> pageList(@RequestBody OrderMileageCorrectQueryDTO param){
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.PROVIDER_ORDER_MILEAGE_GET_PAGE_LIST);
        final HashMap<String, Object> restParam = Maps.newHashMapWithExpectedSize(1);
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null,OrderMileageCorrectDTO.class);
    }

    @ApiOperation(value = "纠偏记录导出",notes = "作者：连江伟")
    @PostMapping(MrcarOrderRestMsgCenter.PROVIDER_ORDER_MILEAGE_EXPORT)
    @ExportExcelWeb(fileName = ExportExcelConstants.FILE_NAME_MILEAGE_CORRECT_RECORD+ ExportExcelConstants.XLSX,filePath = ExportExcelConstants.FILE_PATH,
            sheet = ExportExcelConstants.FILE_NAME_MILEAGE_CORRECT_RECORD, c = OrderMileageCorrectExportDTO.class)
    public PageDTO<OrderMileageCorrectExportDTO> exportRecord(@RequestBody OrderMileageCorrectQueryDTO param, IzuEasyExcelSession izuEasyExcelSession,
                                     HttpServletRequest request, HttpServletResponse response){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.PROVIDER_ORDER_MILEAGE_GET_PAGE_LIST);
        izuEasyExcelSession.setUserName(providerLoginInfo.obtainBaseInfo().getStaffName());
        param.setPage(izuEasyExcelSession.getPageNo());
        param.setPageSize(ExportExcelConstants.PAGE_SIZE);

        final HashMap<String, Object> restParam = Maps.newHashMapWithExpectedSize(1);
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        RestResponse<PageDTO<OrderMileageCorrectDTO>> res = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null, OrderMileageCorrectDTO.class);
        if(null!=res && res.isSuccess()){
            PageDTO<OrderMileageCorrectDTO> pageDTO = res.getData();
            if (pageDTO.getTotal()>MAX_LINE){
                throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
            }
            List<OrderMileageCorrectExportDTO> list = BeanUtil.copyList(pageDTO.getResult(),OrderMileageCorrectExportDTO.class);
            return new PageDTO<>(pageDTO.getPage(), pageDTO.getPageSize(), pageDTO.getTotal(), list);
        }else {
            return null;
        }
    }
}
