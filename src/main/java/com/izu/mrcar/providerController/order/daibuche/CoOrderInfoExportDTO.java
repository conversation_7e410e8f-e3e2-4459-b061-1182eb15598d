package com.izu.mrcar.providerController.order.daibuche;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 *  （表名：co_order_info）
 * <br>
 * <br>
 * 【重要提示】：<br>
 * &nbsp;&nbsp;此类通过 Mybatis Generator 逆向生成，禁止手动修改！<br>（因项目需求持续性会发生变更，当调整数据表字段时，需要再重新逆向生成此类）
 **/
@Data
@ApiModel("订单列表导出")
public class CoOrderInfoExportDTO {

    @ExcelProperty(value = "订单号")
    @ColumnWidth(value = 20)
    private String orderNum;

    @ExcelProperty(value = "订单状态")
    @ColumnWidth(value = 20)
    private String orderStatusStr;

    @ExcelProperty(value = "账单状态")
    @ColumnWidth(value = 20)
    private String billStatusStr;

//    @ExcelProperty(value = "接单分公司")
//    @ColumnWidth(value = 20)
    @ExcelIgnore
    private String structName;

    @ExcelProperty(value = "客户企业名称")
    @ColumnWidth(value = 20)
    private String companyName;

    @ExcelProperty(value = "用车人姓名")
    @ColumnWidth(value = 20)
    private String customerName;

    @ExcelProperty(value = "用车人手机号")
    @ColumnWidth(value = 20)
    private String customerPhone;

    @ExcelProperty(value = "预约用车日期")
    @ColumnWidth(value = 20)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date estimateUseDate;

    @ExcelProperty(value = "预约还车时间")
    @ColumnWidth(value = 20)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date estimateReturnDate;

    @ExcelProperty(value = "预约用车天数")
    @ColumnWidth(value = 20)
    private Integer estimateUseCycle;

    @ExcelProperty(value = "用车城市")
    @ColumnWidth(value = 20)
    private String estimateUseCityName;

    @ExcelProperty(value = "预约用车地址")
    @ColumnWidth(value = 20)
    private String estimateUseAddress;

    @ExcelProperty(value = "预约还车地址")
    @ColumnWidth(value = 20)
    private String estimateReturnAddress;

    @ExcelProperty(value = "用车备注")
    @ColumnWidth(value = 20)
    private String useRemark;

    @ExcelProperty(value = "调度员")
    @ColumnWidth(value = 20)
    private String dispatchOperateName;

    @ExcelProperty(value = "调度时间")
    @ColumnWidth(value = 20)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date dispatchOperateTime;

    @ExcelProperty(value = "送车人")
    @ColumnWidth(value = 20)
    private String deliveryPersonName;

    @ExcelProperty(value = "取车人")
    @ColumnWidth(value = 20)
    private String pickPersonName;

    @ExcelProperty(value = "车牌号")
    @ColumnWidth(value = 20)
    private String vehicleLicense;

    // 推送放品牌
    @ExcelIgnore
    private String brand;

    @ExcelProperty(value = "品牌")
    @ColumnWidth(value = 20)
    private String vehicleBrand;
    @ExcelProperty(value = "车型")
    @ColumnWidth(value = 20)
    private String vehicleModel;

    @ExcelProperty(value = "车辆类型")
    @ColumnWidth(value = 20)
    private String selfOwnedStr;

    // 价格录入类型
    @ExcelIgnore
    private String taxTypeStr;


    @ExcelProperty(value = "送车操作人")
    @ColumnWidth(value = 20)
    private String deliveryOperateName;

    // 送车操作时间
    @ExcelIgnore
    private Date deliveryOperateTime;

    @ExcelProperty(value = "确认送达时间")
    @ColumnWidth(value = 20)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date actualUseTime;

    @ExcelProperty(value = "确认送达城市")
    @ColumnWidth(value = 20)
    private String actualUseCityName;

    @ExcelProperty(value = "确认送达地址")
    @ColumnWidth(value = 20)
    private String actualUseAddress;

    @ExcelProperty(value = "送车超公里数")
    @ColumnWidth(value = 20)
    private BigDecimal deliveryOverMileageKilometer;

    @ExcelProperty(value = "送车备注")
    @ColumnWidth(value = 20)
    private String deliveryRemark;

    @ExcelProperty(value = "还车操作人")
    @ColumnWidth(value = 20)
    private String returnOperateName;

    // 还车操作时间
    @ExcelIgnore
    private Date returnOperateTime;

    @ExcelProperty(value = "确认还车时间")
    @ColumnWidth(value = 20)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date actualReturnTime;

    @ExcelProperty(value = "确认还车城市")
    @ColumnWidth(value = 20)
    private String actualReturnCityName;

    @ExcelProperty(value = "确认还车地址")
    @ColumnWidth(value = 20)
    private String actualReturnAddress;

    @ExcelProperty(value = "还车超公里数")
    @ColumnWidth(value = 20)
    private BigDecimal returnOverMileageKilometer;

    @ExcelProperty(value = "还车备注")
    @ColumnWidth(value = 20)
    private String returnRemark;

    @ExcelProperty(value = "实际租期")
    @ColumnWidth(value = 20)
    private Integer actualUseCycle;
    // (含税)
    @ExcelProperty(value = "合计金额")
    @ColumnWidth(value = 30)
    private BigDecimal taxTotalAmount;

//    @ExcelProperty(value = "合计金额(不含税)")
//    @ColumnWidth(value = 30)
//    private BigDecimal totalAmount;

    @ExcelProperty(value = "租金")
    @ColumnWidth(value = 20)
    private BigDecimal vehicleRentAmount;

    @ExcelProperty(value = "配送超里程费")
    @ColumnWidth(value = 20)
    private BigDecimal totalOverMileageAmount;

    @ExcelProperty(value = "加油费")
    @ColumnWidth(value = 20)
    private BigDecimal petrolAmount;

    @ExcelProperty(value = "违章费")
    @ColumnWidth(value = 20)
    private BigDecimal violateAmount;

    @ExcelProperty(value = "取送车服务费")
    @ColumnWidth(value = 20)
    private BigDecimal pickUpAmount;

    @ExcelProperty(value = "其他费用")
    @ColumnWidth(value = 20)
    private BigDecimal otherAmount;

//    @ExcelProperty(value = "其他核减费用")
//    @ColumnWidth(value = 20)
//    private BigDecimal otherDecreaaseAmount;

    @ExcelProperty(value = "下单人")
    @ColumnWidth(value = 20)
    private String createName;

    @ExcelProperty(value = "下单人手机号")
    @ColumnWidth(value = 20)
    private String createMobile;

    @ExcelProperty(value = "下单时间")
    @ColumnWidth(value = 20)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ExcelProperty(value = "下单人企业")
    @ColumnWidth(value = 20)
    private String createCompanyName;

    @ExcelProperty(value = "下单方式")
    @ColumnWidth(value = 20)
    private String orderSourceStr;

    @ExcelProperty(value = "推送方编号")
    @ColumnWidth(value = 20)
    private String thirdOrderNum;

}