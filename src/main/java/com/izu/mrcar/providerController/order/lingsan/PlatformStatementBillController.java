package com.izu.mrcar.providerController.order.lingsan;

import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.controller.export.SystemBillDetailExcelDTO;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.consts.lingsan.ExportConstants;
import com.izu.mrcar.order.dto.lingsan.bill.*;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.lingsan.PermissionUtil;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Api(tags = "零散用车-账单")
@RestController
@SuppressWarnings({"unchecked", "rawtypes"})
public class PlatformStatementBillController {
    @ApiOperation(value = "平台（首汽）账单列表")
    @RequestFunction(functionName = "首汽账单列表")
    @RequestMapping(value = MrcarOrderRestMsgCenter.PLATFORM_BILL_LIST, method = RequestMethod.POST)
    public RestResponse<PageDTO<IncomeBillRespDTO>> incomeBillList(@RequestBody IncomeBillReqDTO reqDTO) {
        reqDTO.setLoginUser(PermissionUtil.getLoginUserInfo());
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.PLATFORM_BILL_LIST);
        Map<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }

    @ApiOperation(value = "平台（首汽）系统账单明细")
    @RequestFunction(functionName = "首汽系统账单明细")
    @RequestMapping(value = MrcarOrderRestMsgCenter.PLATFORM_SYSTEM_BILL_DETAIL, method = RequestMethod.POST)
    public RestResponse<PageDTO<SystemBillDetailRespDTO>> systemBillDetail(@RequestBody SystemBillDetailReqDTO reqDTO) {
        reqDTO.setLoginUser(PermissionUtil.getLoginUserInfo());
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.PLATFORM_SYSTEM_BILL_DETAIL);
        Map<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }

    @ApiOperation(value = "平台（首汽）系统账单明细导出")
    @RequestFunction(functionName = "首汽系统账单明细导出")
    @ExportExcelWeb(fileName = ExportConstants.SYS_BILL_DETAIL_EXPORT_FILE_NAME, filePath = ExportConstants.SYS_BILL_DETAIL_EXPORT_FILE_PATH,
            sheet = ExportConstants.SYS_BILL_DETAIL_EXPORT_SHEET_NAME, c = SystemBillDetailExcelDTO.class)
    @RequestMapping(value = MrcarOrderRestMsgCenter.PLATFORM_SYSTEM_BILL_DETAIL_EXPORT, method = RequestMethod.POST)
    public Object systemBillDetailExport(@RequestBody SystemBillDetailReqDTO reqDTO,
                                         IzuEasyExcelSession izuEasyExcelSession,
                                         HttpServletResponse response) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        reqDTO.setLoginUser(PermissionUtil.getLoginUserInfo());
        reqDTO.setPageNum(izuEasyExcelSession.getPageNo());
        reqDTO.setPageSize(1000);
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.PLATFORM_SYSTEM_BILL_DETAIL);
        Map<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        izuEasyExcelSession.setUserName(providerLoginInfo.getBaseInfo().getStaffName());
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null, SystemBillDetailRespDTO.class);
        if (Objects.nonNull(restResponse) && restResponse.isSuccess()) {
            PageDTO<SystemBillDetailRespDTO> data = (PageDTO<SystemBillDetailRespDTO>) restResponse.getData();
            if (!CollectionUtils.isEmpty(data.getResult())) {
                List<SystemBillDetailExcelDTO> list = BeanUtil.copyList(data.getResult(), SystemBillDetailExcelDTO.class);
                return new PageDTO<>(data.getPage(), data.getPageSize(), data.getTotal(), list);
            }
        }
        return null;
    }

    @ApiOperation(value = "平台（首汽）最新账单明细")
    @RequestFunction(functionName = "首汽最新账单明细")
    @RequestMapping(value = MrcarOrderRestMsgCenter.PLATFORM_NEWEST_BILL_DETAIL, method = RequestMethod.POST)
    public RestResponse<PageDTO<SystemBillDetailRespDTO>> newestBillDetail(@RequestBody SystemBillDetailReqDTO reqDTO) {
        reqDTO.setLoginUser(PermissionUtil.getLoginUserInfo());
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.PLATFORM_NEWEST_BILL_DETAIL);
        Map<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }

    @ApiOperation(value = "平台（首汽）账单详情")
    @RequestFunction(functionName = "首汽账单详情")
    @RequestMapping(value = MrcarOrderRestMsgCenter.PLATFORM_BILL_DETAIL, method = RequestMethod.POST)
    public RestResponse<StatementBillDetailRespDTO> billDetail(@ApiParam(value = "收入账单号", required = true) @RequestParam @Verify(param = "statementBillNum", rule = "required") String statementBillNum) {
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.PLATFORM_BILL_DETAIL);
        Map<String, Object> restParam = new HashMap<>();
        IncomeBillReqDTO reqDTO = new IncomeBillReqDTO();
        reqDTO.setStatementBillNum(statementBillNum);
        reqDTO.setLoginUser(PermissionUtil.getLoginUserInfo());
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }

    @ApiOperation(value = "平台（首汽）核对账单")
    @RequestFunction(functionName = "首汽核对账单")
    @RequestMapping(value = MrcarOrderRestMsgCenter.PLATFORM_CHECK_BILL, method = RequestMethod.POST)
    public RestResponse checkBill4Shouqi(@RequestBody @Validated ShouqiCheckBillReqDTO checkBillReqDTO) {
        checkBillReqDTO.setLoginUser(PermissionUtil.getLoginUserInfo());
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.PLATFORM_CHECK_BILL);
        Map<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, checkBillReqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }

    @ApiOperation(value = "平台（首汽）保存核对账单")
    @RequestFunction(functionName = "首汽保存核对账单")
    @RequestMapping(value = MrcarOrderRestMsgCenter.PLATFORM_SAVE_CHECK_BILL, method = RequestMethod.POST)
    public RestResponse saveCheckBill(@RequestBody @Validated ShouqiSaveCheckBillReqDTO reqDTO) {
        reqDTO.setLoginUser(PermissionUtil.getLoginUserInfo());
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.PLATFORM_SAVE_CHECK_BILL);
        Map<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }

    @ApiOperation(value = "平台（首汽）核对账单时查询总租金")
    @RequestFunction(functionName = "平台（首汽）核对账单时查询总租金")
    @RequestMapping(value = MrcarOrderRestMsgCenter.PLATFORM_CHECK_BILL_GET_TOTAL_AMOUNT_SUM, method = RequestMethod.GET)
    public RestResponse getTotalAmountSum(@Verify(param = "statementBillNum", rule = "required") String statementBillNum) {
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.PLATFORM_CHECK_BILL_GET_TOTAL_AMOUNT_SUM);
        Map<String, Object> restParam = new HashMap<>();
        restParam.put("statementBillNum", statementBillNum);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.GET, restUrl, restParam, null);
    }

    @ApiOperation(value = "平台（首汽）收支明细")
    @RequestFunction(functionName = "首汽收支明细")
    @RequestMapping(value = MrcarOrderRestMsgCenter.PLATFORM_INCOME_AND_EXPENDITURE_DETAIL_LIST, method = RequestMethod.POST)
    public RestResponse<IncomeAndExpenditureDetailRespDTO> incomeAndExpenditureDetail(@RequestBody IncomeAndExpenditureDetailReqDTO reqDTO) {
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.PLATFORM_INCOME_AND_EXPENDITURE_DETAIL_LIST);
        reqDTO.setLoginUser(PermissionUtil.getLoginUserInfo());
        Map<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }

    @ApiOperation(value = "平台（首汽）收支明细导出")
    @RequestFunction(functionName = "首汽收支明细导出")
    @RequestMapping(value = MrcarOrderRestMsgCenter.PLATFORM_INCOME_AND_EXPENDITURE_DETAIL_EXPORT_2_EMAIL, method = RequestMethod.POST)
    public RestResponse incomeAndExpenditureDetailExport(@RequestBody IncomeAndExpenditureDetailReqDTO reqDTO) {
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.PLATFORM_INCOME_AND_EXPENDITURE_DETAIL_EXPORT_2_EMAIL);
        reqDTO.setLoginUser(PermissionUtil.getLoginUserInfo());
        Map<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }

    @ApiOperation(value = "推送收入账单到综合")
    @RequestFunction(functionName = "推送收入账单到综合")
    @RequestMapping(value = MrcarOrderRestMsgCenter.PLATFORM_INCOME_BILL_PUSH, method = RequestMethod.POST)
    public RestResponse pushIncomeBill(@RequestBody @Validated PushIncomeBillDTO reqDTO) {
        reqDTO.setLoginUser(PermissionUtil.getLoginUserInfo());
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.PLATFORM_INCOME_BILL_PUSH);
        Map<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }
}
