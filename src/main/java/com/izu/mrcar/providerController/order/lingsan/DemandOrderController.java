package com.izu.mrcar.providerController.order.lingsan;


import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.controller.export.DemandOrderExport;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.consts.lingsan.ExportConstants;
import com.izu.mrcar.order.consts.lingsan.RoleEnum;
import com.izu.mrcar.order.dto.lingsan.customer.input.DemandOrderCreateDTO;
import com.izu.mrcar.order.dto.lingsan.customer.input.DemandOrderCustomerOrderSearchDTO;
import com.izu.mrcar.order.dto.lingsan.customer.input.DemandOrderListSearchDTO;
import com.izu.mrcar.order.dto.lingsan.customer.output.DemandOrderDetailDTO;
import com.izu.mrcar.order.dto.lingsan.customer.output.DemandOrderListForWebDTO;
import com.izu.mrcar.order.dto.lingsan.customer.output.DemandOrderRelateOrderDetailDTO;
import com.izu.mrcar.order.dto.lingsan.customer.output.DemandOrderRelateOrderInfoDTO;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.lingsan.ProviderLingsanDataPermUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.dto.company.CompanyDTO;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.utils.JsonUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Api(tags="零散用车")
@RestController
@RequestMapping("/lingsan/demandOrder")
@SuppressWarnings("unchecked")
public class DemandOrderController {


    @PostMapping(path = "create")
    @ApiOperation(value = "平台-需求单-创建")
    @RequestFunction(functionName = "平台创建需求单")
    public RestResponse<Void> create(@RequestBody DemandOrderCreateDTO demandOrderCreate){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        RestResponse<CompanyDTO> response = getCompanyInfo(demandOrderCreate.getCompanyId());
        log.info("{}", JsonUtil.getJsonString(response));
        if(!response.isSuccess()) {
            log.error("获取客户所属部门接口失败。{}", response.getMsg());
            return RestResponse.fail(RestErrorCode.SYSTEM_EXCEPTION);
        }
        CompanyDTO companyInfo = response.getData();
        Map<String, Object> param = new HashMap<>();
        demandOrderCreate.setLoginUserId(providerLoginInfo.getBaseInfo().getStaffId());
        demandOrderCreate.setLoginUserName(providerLoginInfo.getBaseInfo().getStaffName());
        demandOrderCreate.setRoleEnum(RoleEnum.SHOUQI);
        demandOrderCreate.setCompanyBelongDepartmentCode(companyInfo.getDeptId());
        demandOrderCreate.setCompanyBelongDepartmentName(companyInfo.getDeptName());
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, demandOrderCreate);
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.DEMAND_ORDER_CREATE_PLATFORM);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null);
    }

    private static RestResponse<CompanyDTO> getCompanyInfo(Integer companyId) {
        String userRestLocator = new UserRestLocator().getRestUrl(UserUrlCenter.GET_COMPANY_BY_COMPANY_ID);
        Map<String, Object> companyParam = new HashMap<>();
        companyParam.put("companyId", companyId);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.GET, userRestLocator, companyParam, null, CompanyDTO.class);
    }

    @PostMapping(path = "list")
    @ApiOperation(value = "平台-需求单-列表")
    @RequestFunction(functionName = "平台需求单列表")
    public RestResponse<PageDTO<DemandOrderListForWebDTO>> list(@RequestBody DemandOrderListSearchDTO demandOrderListSearch){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        Map<String, Object> param = new HashMap<>();
        demandOrderListSearch.setLoginUserId(providerLoginInfo.getBaseInfo().getStaffId());
        demandOrderListSearch.setLoginUserName(providerLoginInfo.getBaseInfo().getStaffName());
        demandOrderListSearch.setDataPermCodeSet(ProviderLingsanDataPermUtil.getDatePermDeptCode());
        demandOrderListSearch.setRole(RoleEnum.SHOUQI);
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, demandOrderListSearch);
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.DEMAND_ORDER_LIST_PLATFORM);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null, DemandOrderListForWebDTO.class);
    }

    @GetMapping(path = "detail")
    @ApiOperation(value = "平台-需求单-详情")
    @RequestFunction(functionName = "平台需求单详情")
    public RestResponse<DemandOrderDetailDTO> detail(@RequestParam("demandOrderNum") String demandOrderNum){

        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        Map<String, Object> param = new HashMap<>();
        param.put("demandOrderNum", demandOrderNum);
        param.put("loginUserId", providerLoginInfo.getBaseInfo().getStaffId());
        param.put("loginUserName", providerLoginInfo.getBaseInfo().getStaffName());
        param.put("role", RoleEnum.SHOUQI);
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.DEMAND_ORDER_DETAIL_PLATFORM);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.GET, restUrl, param, null);
    }

    @PostMapping(path = "export")
    @ApiOperation(value = "平台-需求单-导出")
    @RequestFunction(functionName = "平台-导出需求单")
    @ExportExcelWeb(fileName = ExportConstants.DEMAND_ORDER_EXPORT_FILE_NAME,filePath = ExportConstants.DEMAND_ORDER_EXPORT_FILE_PATH,sheet = ExportConstants.DEMAND_ORDER_EXPORT_SHEET_NAME,
            c = DemandOrderExport.class)
    public PageDTO<DemandOrderExport> export(@RequestBody DemandOrderListSearchDTO demandOrderListSearch, IzuEasyExcelSession izuEasyExcelSession,
                                                    HttpServletResponse response){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        Map<String, Object> param = new HashMap<>();
        izuEasyExcelSession.setUserName(providerLoginInfo.getBaseInfo().getStaffName());
        demandOrderListSearch.setPage(izuEasyExcelSession.getPageNo());
        demandOrderListSearch.setLoginUserId(providerLoginInfo.getBaseInfo().getStaffId());
        demandOrderListSearch.setLoginUserName(providerLoginInfo.getBaseInfo().getStaffName());
        demandOrderListSearch.setDataPermCodeSet(ProviderLingsanDataPermUtil.getDatePermDeptCode());
        demandOrderListSearch.setRole(RoleEnum.SHOUQI);
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, demandOrderListSearch);
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.DEMAND_ORDER_LIST_PLATFORM);
        RestResponse<PageDTO<DemandOrderListForWebDTO>> serverResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null, DemandOrderListForWebDTO.class);
        if(serverResponse!=null && serverResponse.isSuccess()) {
            PageDTO<DemandOrderListForWebDTO> data = serverResponse.getData();
            if(CollectionUtils.isNotEmpty(data.getResult())){
                List<DemandOrderExport> list = BeanUtil.copyList(data.getResult(), DemandOrderExport.class);
                return new PageDTO<>(data.getPage(), data.getPageSize(), data.getTotal(), list);
            }
            return null;
        }else{
            return null;
        }
    }

    @PostMapping(path = "detail/order")
    @ApiOperation(value = "平台-需求单-订单列表")
    @RequestFunction(functionName = "平台需求单订单列表")
    public RestResponse<PageDTO<DemandOrderRelateOrderDetailDTO>> detailOrder(@RequestBody DemandOrderCustomerOrderSearchDTO demandOrderCustomerOrderSearchDTO){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        Map<String, Object> param = new HashMap<>();
        demandOrderCustomerOrderSearchDTO.setLoginUserId(providerLoginInfo.getBaseInfo().getStaffId());
        demandOrderCustomerOrderSearchDTO.setLoginUserName(providerLoginInfo.getBaseInfo().getStaffName());
        demandOrderCustomerOrderSearchDTO.setRole(RoleEnum.SHOUQI);
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, demandOrderCustomerOrderSearchDTO);
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.DEMAND_ORDER_DETAIL_RELATE_ORDER_PLATFORM);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null, DemandOrderRelateOrderDetailDTO.class);
    }

}
