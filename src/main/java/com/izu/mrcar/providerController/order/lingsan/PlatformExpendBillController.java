package com.izu.mrcar.providerController.order.lingsan;

import static com.izu.mrcar.order.MrcarOrderRestMsgCenter.LINGSAN_PLATFORM_EXPEND_BILL_DETAIL;
import static com.izu.mrcar.order.MrcarOrderRestMsgCenter.LINGSAN_PLATFORM_EXPEND_BILL_DETAIL_PAGE_LIST;
import static com.izu.mrcar.order.MrcarOrderRestMsgCenter.LINGSAN_PLATFORM_EXPEND_BILL_DETAIL_TYPE_LIST;
import static com.izu.mrcar.order.MrcarOrderRestMsgCenter.LINGSAN_PLATFORM_EXPEND_BILL_PAGE_LIST;
import static com.izu.mrcar.order.MrcarOrderRestMsgCenter.LINGSAN_PLATFORM_EXPEND_BILL_SETTLE_STATUS_LIST;
import static com.izu.mrcar.order.MrcarOrderRestMsgCenter.LINGSAN_PLATFORM_EXPEND_BILL_STATUS_LIST;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.dto.lingsan.ValueTextPairRespDTO;
import com.izu.mrcar.order.dto.lingsan.bill.platform.PlatformExpendBillDetailListItemRespDTO;
import com.izu.mrcar.order.dto.lingsan.bill.platform.PlatformExpendBillDetailListReqDTO;
import com.izu.mrcar.order.dto.lingsan.bill.platform.PlatformExpendBillDetailReqDTO;
import com.izu.mrcar.order.dto.lingsan.bill.platform.PlatformExpendBillDetailRespDTO;
import com.izu.mrcar.order.dto.lingsan.bill.platform.PlatformExpendBillListItemRespDTO;
import com.izu.mrcar.order.dto.lingsan.bill.platform.PlatformExpendBillListReqDTO;
import com.izu.mrcar.utils.DateUtil;
import com.izu.mrcar.utils.lingsan.ProviderLingsanDataPermUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * PlatformExpendBillController.
 *
 * <AUTHOR>
 */
@Api(tags = {"零散用车-账单"})
@RestController
public class PlatformExpendBillController {
    /**
     * MrcarOrderRestLocator.
     */
    private final MrcarOrderRestLocator orderRestLocator;

    /**
     * 默认构造方法.
     * 初始化orderRestLocator
     */
    public PlatformExpendBillController() {
        orderRestLocator = new MrcarOrderRestLocator();
    }

    /**
     * 平台（首汽）支出账单对账状态下拉框.
     *
     * @return ValueTextPairRespDTO list
     */
    @ApiOperation(value = "平台（首汽）支出账单对账状态下拉框")
    @GetMapping(LINGSAN_PLATFORM_EXPEND_BILL_STATUS_LIST)
    public RestResponse<List<ValueTextPairRespDTO>> statusList() {
        String url = orderRestLocator.getRestUrl(LINGSAN_PLATFORM_EXPEND_BILL_STATUS_LIST);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.GET, url, null, null,
                ValueTextPairRespDTO.class);
    }

    /**
     * 平台（首汽）支出账单推送状态下拉框.
     *
     * @return ValueTextPairRespDTO list
     */
    @ApiOperation(value = "平台（首汽）支出账单推送状态下拉框")
    @GetMapping(LINGSAN_PLATFORM_EXPEND_BILL_SETTLE_STATUS_LIST)
    public RestResponse<List<ValueTextPairRespDTO>> settleStatusList() {
        String url = orderRestLocator.getRestUrl(LINGSAN_PLATFORM_EXPEND_BILL_SETTLE_STATUS_LIST);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.GET, url, null, null,
                ValueTextPairRespDTO.class);
    }

    /**
     * 平台（首汽）支出账单分页列表.
     *
     * @param reqDTO 请求参数
     * @return 分页列表
     */
    @ApiOperation(value = "平台（首汽）支出账单分页列表")
    @RequestFunction(functionName = "平台（首汽）支出账单分页列表")
    @GetMapping(value = LINGSAN_PLATFORM_EXPEND_BILL_PAGE_LIST)
    public RestResponse<PageDTO<PlatformExpendBillListItemRespDTO>> pageList(
            final @Validated PlatformExpendBillListReqDTO reqDTO) {
        Set<String> datePermDeptCode = ProviderLingsanDataPermUtil.getDatePermDeptCode();
        reqDTO.setSubsidiaryCompanyCodes(StrUtil.join(",", datePermDeptCode));
        String url = orderRestLocator.getRestUrl(LINGSAN_PLATFORM_EXPEND_BILL_PAGE_LIST);

        Map<String, Object> params = BeanUtil.beanToMap(reqDTO);
        if (reqDTO.getCreateTimeStart() != null) {
            params.put("createTimeStart", DateUtil.date2String(reqDTO.getCreateTimeStart(), DateUtil.TIME_FORMAT));
        }
        if (reqDTO.getCreateTimeEnd() != null) {
            params.put("createTimeEnd", DateUtil.date2String(reqDTO.getCreateTimeEnd(), DateUtil.TIME_FORMAT));
        }
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.GET, url, params, null,
                PlatformExpendBillListItemRespDTO.class);
    }

    /**
     * 零散用车-平台（首汽）支出账单详情.
     *
     * @param reqDTO 请求参数
     * @return PlatformExpendBillDetailRespDTO
     */
    @ApiOperation("平台（首汽）支出账单详情")
    @RequestFunction(functionName = "平台（首汽）支出账单详情")
    @GetMapping(value = LINGSAN_PLATFORM_EXPEND_BILL_DETAIL)
    public RestResponse<PlatformExpendBillDetailRespDTO> detail(
            final @Validated PlatformExpendBillDetailReqDTO reqDTO) {
        Set<String> datePermDeptCode = ProviderLingsanDataPermUtil.getDatePermDeptCode();
        reqDTO.setSubsidiaryCompanyCodes(StrUtil.join(",", datePermDeptCode));
        String url = orderRestLocator.getRestUrl(LINGSAN_PLATFORM_EXPEND_BILL_DETAIL);
        Map<String, Object> params = BeanUtil.beanToMap(reqDTO);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.GET, url, params, null,
                PlatformExpendBillDetailRespDTO.class);
    }

    /**
     * 零散用车-平台（首汽）支出账单明细账单类型下拉框.
     *
     * @return ValueTextPairRespDTO
     */
    @ApiOperation(value = "平台（首汽）支出账单明细账单类型下拉框")
    @GetMapping(LINGSAN_PLATFORM_EXPEND_BILL_DETAIL_TYPE_LIST)
    public RestResponse<List<ValueTextPairRespDTO>> detailTypeList() {
        String url = orderRestLocator.getRestUrl(LINGSAN_PLATFORM_EXPEND_BILL_DETAIL_TYPE_LIST);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.GET, url, null, null,
                ValueTextPairRespDTO.class);
    }

    /**
     * 零散用车-平台（首汽）支出账单明细.
     *
     * @param reqDTO 请求参数
     * @return PlatformExpendBillDetailListItemRespDTO page
     */
    @ApiOperation("平台（首汽）支出账单明细")
    @RequestFunction(functionName = "平台（首汽）支出账单明细")
    @GetMapping(value = LINGSAN_PLATFORM_EXPEND_BILL_DETAIL_PAGE_LIST)
    public RestResponse<PageDTO<PlatformExpendBillDetailListItemRespDTO>> detailPageList(
            final @Validated PlatformExpendBillDetailListReqDTO reqDTO) {
        Set<String> datePermDeptCode = ProviderLingsanDataPermUtil.getDatePermDeptCode();
        reqDTO.setSubsidiaryCompanyCodes(StrUtil.join(",", datePermDeptCode));
        String url = orderRestLocator.getRestUrl(LINGSAN_PLATFORM_EXPEND_BILL_DETAIL_PAGE_LIST);
        Map<String, Object> params = BeanUtil.beanToMap(reqDTO);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.GET, url, params, null,
                PlatformExpendBillDetailListItemRespDTO.class);
    }
}
