package com.izu.mrcar.providerController.order.lingsan;

import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.controller.export.CustomerOrderExport;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.consts.lingsan.ExportConstants;
import com.izu.mrcar.order.consts.lingsan.RoleEnum;
import com.izu.mrcar.order.dto.lingsan.customer.CustomerOrderBaseDTO;
import com.izu.mrcar.order.dto.lingsan.customer.input.CustomerOrderListSearchDTO;
import com.izu.mrcar.order.dto.lingsan.customer.input.DemandOrderDispatchDTO;
import com.izu.mrcar.order.dto.lingsan.customer.input.PlatformOrderTodoDTO;
import com.izu.mrcar.order.dto.lingsan.customer.output.*;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.lingsan.ProviderLingsanDataPermUtil;
import com.izu.user.dto.staff.pc.AccountBelongDepartment;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Program: mrcar
 * @Description: 零散用车-客户订单
 * @Author: inT
 * @Create: 2023-01-30
 */
@Api(tags = "零散用车")
@Slf4j
@RestController
@RequestMapping("/lingsan/customerOrder")
@SuppressWarnings("unchecked")
public class OrderCustomerController {

    private static final Integer EXPORT_PAGE_SIZE = 1000;

    @PostMapping(path = "list")
    @ApiOperation(value = "客户订单-列表")
    @RequestFunction(functionName = "平台-客户订单列表")
    public RestResponse<PageDTO<CustomerOrderListForWebDTO>> list(@RequestBody CustomerOrderListSearchDTO customerOrderListSearchDTO) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        Map<String, Object> param = new HashMap<>();
        customerOrderListSearchDTO.setLoginUserId(providerLoginInfo.getBaseInfo().getStaffId());
        customerOrderListSearchDTO.setLoginUserName(providerLoginInfo.getBaseInfo().getStaffName());
        customerOrderListSearchDTO.setDataPermCodeSet(ProviderLingsanDataPermUtil.getDatePermDeptCode());
        customerOrderListSearchDTO.setRole(RoleEnum.SHOUQI);
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, customerOrderListSearchDTO);
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.CUSTOMER_ORDER_LIST_PLATFORM);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null, CustomerOrderListForWebDTO.class);
    }

    @GetMapping(path = "detail")
    @ApiOperation(value = "客户订单-详情")
    @RequestFunction(functionName = "平台-客户订单详情")
    public RestResponse<CustomerOrderDetailDTO> detail(@RequestParam("orderNum") String orderNum) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        Map<String, Object> param = new HashMap<>();
        param.put("orderNum", orderNum);
        param.put("loginUserId", providerLoginInfo.getBaseInfo().getStaffId());
        param.put("loginUserName", providerLoginInfo.getBaseInfo().getStaffName());
        param.put("role", RoleEnum.SHOUQI);
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.CUSTOMER_ORDER_COMPOSITE_DETAIL_PLATFORM);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.GET, restUrl, param, null);
    }

    @PostMapping(path = "export")
    @ApiOperation(value = "客户订单-导出")
    @RequestFunction(functionName = "平台-导出客户订单")
    @ExportExcelWeb(fileName = ExportConstants.CUSTOMER_ORDER_EXPORT_FILE_NAME,filePath = ExportConstants.CUSTOMER_ORDER_EXPORT_FILE_PATH,sheet = ExportConstants.CUSTOMER_ORDER_EXPORT_SHEET_NAME,
            c = CustomerOrderExport.class)
    public PageDTO<CustomerOrderExport> export(@RequestBody CustomerOrderListSearchDTO customerOrderListSearchDTO,  IzuEasyExcelSession izuEasyExcelSession,
                                                      HttpServletResponse response) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        Map<String, Object> param = new HashMap<>();
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, customerOrderListSearchDTO);
        izuEasyExcelSession.setUserName(providerLoginInfo.getBaseInfo().getStaffName());
        customerOrderListSearchDTO.setPage(izuEasyExcelSession.getPageNo());
        customerOrderListSearchDTO.setLoginUserId(providerLoginInfo.getBaseInfo().getStaffId());
        customerOrderListSearchDTO.setLoginUserName(providerLoginInfo.getBaseInfo().getStaffName());
        customerOrderListSearchDTO.setDataPermCodeSet(ProviderLingsanDataPermUtil.getDatePermDeptCode());
        customerOrderListSearchDTO.setRole(RoleEnum.SHOUQI);
        customerOrderListSearchDTO.setPageSize(EXPORT_PAGE_SIZE);
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.CUSTOMER_ORDER_LIST_PLATFORM);
        RestResponse<PageDTO<CustomerOrderListForWebDTO>> serviceResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null,CustomerOrderListForWebDTO.class);
        if(Objects.nonNull(serviceResponse) && serviceResponse.isSuccess()){
            PageDTO<CustomerOrderListForWebDTO> data = serviceResponse.getData();
            if(CollectionUtils.isNotEmpty(data.getResult())){
                List<CustomerOrderExport> list = BeanUtil.copyList(data.getResult(), CustomerOrderExport.class);
                return new PageDTO<>(data.getPage(), data.getPageSize(), data.getTotal(), list);
            }
            return null;
        }
        return null;
    }

    @GetMapping(path = "dispatch/info")
    @ApiOperation(value = "需求单/订单-派单信息")
    @RequestFunction(functionName = "需求单/订单派单信息")
    public RestResponse<DispatchInfoViewDTO> dispatchInfo(@ApiParam(name = "需求单编号", required = true) @RequestParam(value = "demandOrderNum") String demandOrderNum,
                                                          @ApiParam(name = "订单编号") @RequestParam(value = "orderNum", required = false) String orderNum){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        Map<String, Object> param = new HashMap<>();
        param.put("demandOrderNum", demandOrderNum);
        param.put("orderNum", orderNum);
        param.put("loginUserId", providerLoginInfo.getBaseInfo().getStaffId());
        param.put("loginUserName", providerLoginInfo.getBaseInfo().getStaffName());
        param.put("role", RoleEnum.SHOUQI);
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.CUSTOMER_ORDER_DISPATCH_INFO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.GET, restUrl, param, null);
    }

    @PostMapping(path = "dispatch")
    @ApiOperation(value = "需求单/订单-派单")
    @RequestFunction(functionName = "需求单/订单派单")
    public RestResponse<Void> dispatch(@RequestBody DemandOrderDispatchDTO demandOrderDispatch){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        Map<String, Object> param = new HashMap<>();
        demandOrderDispatch.setLoginUserId(providerLoginInfo.getBaseInfo().getStaffId());
        demandOrderDispatch.setLoginUserName(providerLoginInfo.getBaseInfo().getStaffName());
        List<AccountBelongDepartment> belongDepartmentList = providerLoginInfo.getBelongDepartmentList();
        if(CollectionUtils.isNotEmpty(belongDepartmentList)){
            AccountBelongDepartment belongDepartment = belongDepartmentList.get(0);
            demandOrderDispatch.setCompanyBelongDepartmentCode(belongDepartment.getDeptCode());
            demandOrderDispatch.setCompanyBelongDepartmentName(belongDepartment.getDeptName());
        }
        demandOrderDispatch.setRole(RoleEnum.SHOUQI);
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, demandOrderDispatch);
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.CUSTOMER_ORDER_DISPATCH);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null);
    }

    @PostMapping(path = "cancel")
    @ApiOperation(value = "客户订单-取消")
    @RequestFunction(functionName = "平台-取消客户订单")
    public RestResponse<Void> cancel(@RequestBody CustomerOrderBaseDTO customerOrderBaseDTO) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        Map<String, Object> param = new HashMap<>();
        customerOrderBaseDTO.setLoginUserId(providerLoginInfo.getBaseInfo().getStaffId());
        customerOrderBaseDTO.setLoginUserName(providerLoginInfo.getBaseInfo().getStaffName());
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, customerOrderBaseDTO);
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.CUSTOMER_ORDER_CANCEL);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null);
    }

    @GetMapping(path = "todo/list")
    @ApiOperation(value = "平台订单-待办事项")
    @RequestFunction(functionName = "平台订单-待办事项")
    public RestResponse<PlatformOrderTodoListDTO> todo(){
        PlatformOrderTodoDTO platformOrderTodoDTO = new PlatformOrderTodoDTO();
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        platformOrderTodoDTO.setLoginUserId(providerLoginInfo.getBaseInfo().getStaffId());
        platformOrderTodoDTO.setLoginUserName(providerLoginInfo.getBaseInfo().getStaffName());
        platformOrderTodoDTO.setDataPermCodeSet(ProviderLingsanDataPermUtil.getDatePermDeptCode());
        platformOrderTodoDTO.setRole(RoleEnum.SHOUQI);
        Map<String, Object> param = new HashMap<>();
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, platformOrderTodoDTO);
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.CUSTOMER_ORDER_TODO_PLATFORM);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null);
    }

}
