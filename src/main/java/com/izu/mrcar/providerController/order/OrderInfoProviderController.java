package com.izu.mrcar.providerController.order;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.izu.excel.annotation.ExportExcel;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.excel.dto.IzuMailSession;
import com.izu.framework.web.exception.RestErrorException;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.cache.RedisJsonCache;
import com.izu.mrcar.common.config.ConstansConfig;
import com.izu.mrcar.common.constants.Constant;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.dto.mrcar.BusinessOrderInfoV2Export;
import com.izu.mrcar.order.dto.mrcar.OrderInfoV2Export;
import com.izu.mrcar.order.dto.provider.input.OrderInfoOpeInputDTO;
import com.izu.mrcar.order.dto.provider.output.OrderInfoOpeOutputDTO;
import com.izu.mrcar.service.order.BusinessOrderInfoExportService;
import com.izu.mrcar.service.order.OrderInfoExportService;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.enums.LoginSystemEnum;
import com.izu.user.enums.perm.ProviderDataPermTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

@RestController
@Api(tags = "行程相关")
@Slf4j
public class OrderInfoProviderController {

    private static final Logger logger = LoggerFactory.getLogger(OrderInfoProviderController.class);

    @Autowired
    private OrderInfoExportService orderInfoExportService;
    @Autowired
    private BusinessOrderInfoExportService businessOrderInfoExportService;

    @Autowired
    private RedisJsonCache redisJsonCache;
    @Resource
    private ConstansConfig constansConfig;

    @PostMapping(MrcarOrderRestMsgCenter.ORDER_INFO_PROVIDER_LIST)
//    @RequiresPermissions(value = "childProcessList")
    @ApiOperation(value = "子行程列表", notes = "作者：mapp")
    public RestResponse<PageDTO<OrderInfoOpeOutputDTO>> getList(@RequestBody OrderInfoOpeInputDTO orderInfoParam) {
        addPermData(orderInfoParam);
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.ORDER_INFO_PROVIDER_LIST);
        final HashMap<String, Object> restParam = Maps.newHashMapWithExpectedSize(1);
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, orderInfoParam);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }

    private void addPermData(OrderInfoOpeInputDTO orderInfoParam) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        orderInfoParam.setStaffId( providerLoginInfo.getBaseInfo().getStaffId());
        orderInfoParam.setLoginId( providerLoginInfo.obtainBaseInfo().getStaffId());
        orderInfoParam.setStaffCode(providerLoginInfo.getBaseInfo().getStaffCode());
        orderInfoParam.setDataPermType(providerLoginInfo.obtainDataPerm().getDataPermType());
        providerLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.ID);
        orderInfoParam.setDataPermCodeSet(getDataScopeList(providerLoginInfo));
    }

    /**
     * @Description: 子行程列表异步导出
     * @author: hxc
     * @Date: 2020/9/3
     **/
    @RequestMapping(value = "/orderProvider/exportOrderInfoList/asyn")
    public RestResponse exportOrderInfoListAsyn(@RequestBody OrderInfoOpeInputDTO reqDTO) {
        if (StringUtils.isBlank(reqDTO.getEmail())) {
            RestResponse restResponse = RestResponse.fail(9999);
            restResponse.setMsg("邮箱号不能为空！！！");
            return restResponse;
        }
        // 防止上次导出未完成的情况下，再次导出
        String lockKey = "mrcar:provider:order:exportOrderInfoList:asyn:" + reqDTO.getEmail();
        final String cacheValue = redisJsonCache.get(lockKey, String.class);
        if (StringUtils.isNotBlank(cacheValue)) {
            log.warn("已提交申请，请稍等");
            return RestResponse.create(RestErrorCode.SYSTEM_EXCEPTION, "您已经提交了导出请求，请稍后查收电子邮件。（大约耗时3分钟）", false, null);
        }
        redisJsonCache.set(lockKey, "Y", 3 * 60);
        addPermData(reqDTO);
        IzuMailSession izuMailSession = new IzuMailSession();
        AccountBaseInfo accountBaseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
        izuMailSession.setUserName(accountBaseInfo.getStaffName());
        izuMailSession.setMailOpName("子行程列表");
        izuMailSession.setMailSubject("子行程列表");
        String email = reqDTO.getEmail();
        izuMailSession.setToMail(email);
        CompletableFuture.runAsync(() ->
                        orderInfoExportService.exportToEmail(reqDTO, izuMailSession))
                .whenComplete((unused, throwable) -> {
                    redisJsonCache.delete(lockKey);
                    if (throwable != null) {
                        log.error("子行程列表异步导出至邮箱异常", throwable);
                    }
                });
        return RestResponse.create(0, "提交成功！请稍后查收电子邮件。（大约耗时3分钟！）", true, null);
    }


    /**
     * @Description: 导出子行程列表
     * @author: hxc
     * @Date: 2020/1/7
     **/
    @RequestMapping(value = "/orderProvider/order/exportOrderInfoList")
    @ApiOperation(value = "导出子行程列表", notes = "作者：mapp")
    public PageDTO exportOrderInfoList(@RequestBody OrderInfoOpeInputDTO reqDTO,
                                       IzuEasyExcelSession izuEasyExcelSession,
                                       HttpServletRequest request, HttpServletResponse response) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        addPermData(reqDTO);
        izuEasyExcelSession.setUserName(providerLoginInfo.getBaseInfo().getStaffName());
        PageDTO<OrderInfoV2Export> pageDTO = orderInfoExportService.getDataByPage(reqDTO, 1, 1);
        if(Objects.nonNull(pageDTO) && pageDTO.getTotal() > constansConfig.getExcelSyncExportMaxSize()){
            try {
                response.setContentType("application/json");
                response.setCharacterEncoding("utf-8");
                response.getWriter().write(JSON.toJSONString(RestResponse.create(Constant.ORDER_TRIP_EXPORT_NUM_LIMIT,
                        String.format(Constant.ORDER_TRIP_EXPORT_NUM_LIMIT_MSG, constansConfig.getExcelSyncExportMaxSize()), false, null)));
                response.getWriter().flush();
            } catch (IOException e) {
                log.warn("exportOrderInfoList fail", e);
            }
            return null;
        }
        return orderInfoExportService.exportOrderInfoList(reqDTO, izuEasyExcelSession, request, response);
    }

    /**
     * @Description: 商务车导出子行程列表
     * @param reqDTO
     * @param izuEasyExcelSession
     * @param request
     * @param response
     * @return
     */
    @PostMapping(value = "/orderProvider/order/exportBusinessOrderInfoList")
    @RequestFunction(functionName = "商务车子行程列表导出-运营端")
    @ApiOperation(value = "商务车子行程列表导出-运营端", notes = "作者：丁伟兵")
    public PageDTO exportBusinessOrderInfoList(@RequestBody OrderInfoOpeInputDTO reqDTO,
                                       IzuEasyExcelSession izuEasyExcelSession,
                                       HttpServletRequest request, HttpServletResponse response) {
            ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
            addPermData(reqDTO);
            izuEasyExcelSession.setUserName(providerLoginInfo.getBaseInfo().getStaffName());
            PageDTO pageDTO = businessOrderInfoExportService.getDataByPage(reqDTO, 1, 1);
            if(Objects.nonNull(pageDTO) && pageDTO.getTotal() > constansConfig.getExcelSyncExportMaxSize()){
                try {
                    response.setContentType("application/json");
                    response.setCharacterEncoding("utf-8");
                    response.getWriter().write(JSON.toJSONString(RestResponse.create(Constant.ORDER_TRIP_EXPORT_NUM_LIMIT,
                            String.format(Constant.ORDER_TRIP_EXPORT_NUM_LIMIT_MSG, constansConfig.getExcelSyncExportMaxSize()), false, null)));
                    response.getWriter().flush();
                } catch (IOException e) {
                    log.warn("exportOrderInfoList fail", e);
                }
                return null;
            }
        return businessOrderInfoExportService.exportBusinessOrderInfoList(reqDTO, izuEasyExcelSession, request, response);
    }

    /**
     * @Description: 子行程列表异步导出
     * @author: hxc
     * @Date: 2020/9/3
     **/
    @PostMapping(value = "/orderProvider/exportBusinessOrderInfoList/asyn")
    @RequestFunction(functionName = "商务车子行程列表导出到邮件-运营端")
    @ApiOperation(value = "商务车子行程列表导出到邮件-运营端", notes = "作者：丁伟兵")
//    @ExportExcel(fileName = "子行程列表.xlsx", filePath = "/data/logs/mrcar-order-core/tmp", sheet = "商务车子行程列表")
    public RestResponse exportOrderBusinessInfoListAsyn(@RequestBody OrderInfoOpeInputDTO reqDTO) {
        if (StringUtils.isBlank(reqDTO.getEmail())) {
            RestResponse restResponse = RestResponse.fail(9999);
            restResponse.setMsg("邮箱号不能为空！！！");
            return restResponse;
        }
        // 防止上次导出未完成的情况下，再次导出
        String lockKey = "mrcar:provider:order:exportBusinessOrderInfoList:asyn:" + reqDTO.getEmail();
        final String cacheValue = redisJsonCache.get(lockKey, String.class);
        if (StringUtils.isNotBlank(cacheValue)) {
            log.warn("已提交申请，请稍等");
            return RestResponse.create(RestErrorCode.SYSTEM_EXCEPTION, "您已经提交了导出请求，请稍后查收电子邮件。（大约耗时3分钟）", false, null);
        }
        redisJsonCache.set(lockKey, "Y", 3 * 60);
        addPermData(reqDTO);
        IzuMailSession izuMailSession = new IzuMailSession();
        AccountBaseInfo accountBaseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
        izuMailSession.setUserName(accountBaseInfo.getStaffName());
        izuMailSession.setMailOpName("商务车子行程列表");
        izuMailSession.setMailSubject("商务车子行程列表");
        String email = reqDTO.getEmail();
        izuMailSession.setToMail(email);
        CompletableFuture.runAsync(() ->{
                        businessOrderInfoExportService.exportToEmail(reqDTO, izuMailSession);
                }).whenComplete((unused, throwable) -> {
                    redisJsonCache.delete(lockKey);
                    if (throwable != null) {
                        log.error("子行程列表异步导出至邮箱异常", throwable);
                    }
                });
        return RestResponse.create(0, "提交成功！请稍后查收电子邮件。（大约耗时3分钟！）", true, null);
    }



    /**
     * 强制结束
     * 暂时只支持内部用车
     */
    @ApiOperation(value = "强制结束行程", notes = "作者：牛子联")
    @PostMapping(MrcarOrderRestMsgCenter.FORCE_FINISH_TRIP)
    @ApiImplicitParams({@ApiImplicitParam(name = "orderNo", value = "订单编号", required = true, dataType = "String")})
    @SuppressWarnings("unchecked")
    public RestResponse<Void> forceFinish(String orderNo) {
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.FORCE_FINISH_TRIP);
        HashMap<String, Object> restParam = new HashMap<>(2);
        restParam.put("orderNo", orderNo);
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        if (LoginSystemEnum.CLIENT.getSys().equals(baseLoginInfo.getSystemType())) {
            ClientLoginInfo clientLoginInfo = (ClientLoginInfo) baseLoginInfo;
            restParam.put("userId",clientLoginInfo.obtainBaseInfo().getStaffId());
            restParam.put("userName",clientLoginInfo.obtainBaseInfo().getStaffName());
        } else {
            ProviderLoginInfo providerLoginInfo = (ProviderLoginInfo) baseLoginInfo;
            restParam.put("userId",providerLoginInfo.getBaseInfo().getStaffId());
            restParam.put("userName",providerLoginInfo.getBaseInfo().getStaffName());
        }
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, restParam, null);
    }



    private Set<String> getDataScopeList(ProviderLoginInfo providerLoginInfo) {
        Set<String> res = null;
        if (ProviderDataPermTypeEnum.ALL.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())) {
            return null;
        } else if (ProviderDataPermTypeEnum.RESPONSIBLE_CONTRACT.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())) {
            res = providerLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.VEHICLE).getDataCodeSet();
        } else if (ProviderDataPermTypeEnum.RESPONSIBLE_CUSTOMER.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType()) ||
                ProviderDataPermTypeEnum.ASSIGN_CUSTOMER.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())) {
            res = providerLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.ID).getDataCodeSet();
        } else if (ProviderDataPermTypeEnum.SELF_DEPT.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType()) ||
                ProviderDataPermTypeEnum.ASSIGN_DEPT.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())) {
            res = providerLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet();
        }
        return res;
    }


}
