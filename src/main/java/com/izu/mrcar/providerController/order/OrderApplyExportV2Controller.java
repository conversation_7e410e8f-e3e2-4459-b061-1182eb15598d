package com.izu.mrcar.providerController.order;

import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.cache.RedisJsonCache;
import com.izu.mrcar.common.config.ExecutorConfig;
import com.izu.mrcar.common.constants.Constant;
import com.izu.mrcar.common.enums.ProviderOrderFunctionTypeEnum;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.consts.ExportOrderApplyEnum;
import com.izu.mrcar.order.dto.mrcar.OrderApplyBusinessExport;
import com.izu.mrcar.order.dto.provider.input.OrderApplyOpeInputDTO;
import com.izu.mrcar.service.order.OrderApplyExportService;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/7/16 16:06
 */
@RestController
@Slf4j
@Api(tags = "运营端")
public class OrderApplyExportV2Controller {

    @Autowired
    private OrderApplyExportService orderApplyExportService;

    private static final String CACHE_PREFIX_ORDER_APPLY = "mrcar_order_apply_createexcelsendmail_frequency_control_";

    private static final int CREATE_EXCEL_TO_MAIL_TIMEOUT_MINUTES = 3; //等待时长，平均耗时


    @Autowired
    private RedisJsonCache cache;

    @PostMapping(MrcarOrderRestMsgCenter.ORDER_APPLY_EXPORT_PROVIDER_LIST)
    @RequestFunction(functionName = "行程列表导出")
    @ApiOperation(value = "行程列表导出", notes = "作者：yepengpeng")
    public void exportOrderApply(@RequestBody OrderApplyOpeInputDTO queryDTO,
                                 HttpServletResponse response) {
        setQueryDtoCommonInfo(queryDTO, LoginSessionUtil.getProviderLoginInfo());
        orderApplyExportService.exportOrderApply(queryDTO, response, ExportOrderApplyEnum.PROVIDER, getExcelFileName(queryDTO.getFunctionType()), OrderApplyBusinessExport.class);
    }


    @PostMapping(MrcarOrderRestMsgCenter.PROVIDER_ORDER_APPLY_EXPORT_TO_MAIL)
    @RequestFunction(functionName = "行程列表导出到邮箱")
    @ApiOperation(value = "行程列表导出到邮箱", notes = "作者：yepengpeng")
    public RestResponse exportOrderApplyToMail(@RequestBody OrderApplyOpeInputDTO queryDTO) {
        if (Objects.isNull(queryDTO) || StringUtils.isBlank(queryDTO.getEmail())) {
            RestResponse restResponse = RestResponse.fail(Constant.ORDER_TRIP_EXPORT_WITHOUT_EMAIL);
            restResponse.setMsg(Constant.ORDER_TRIP_EXPORT_WITHOUT_EMAIL_MSG);
            return restResponse;
        }
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        String cacheKey = CACHE_PREFIX_ORDER_APPLY + providerLoginInfo.getBaseInfo().getStaffId() + "_" + queryDTO.getFunctionType();
        final String cacheValue = cache.get(cacheKey, String.class);
        String excelFileName = getExcelFileName(queryDTO.getFunctionType());
        if(cacheValue != null) {
            RestResponse restResponse = RestResponse.fail(Constant.ORDER_TRIP_EXPORT_LIMIT);
            restResponse.setMsg(String.format(Constant.ORDER_TRIP_EXPORT_LIMIT_MSG, excelFileName, CREATE_EXCEL_TO_MAIL_TIMEOUT_MINUTES));
            return restResponse;
        }
        ExecutorConfig.getMrCarExportThreadPool().execute(() -> {
            setQueryDtoCommonInfo(queryDTO, providerLoginInfo);
            orderApplyExportService.exportOrderApplyToMail(providerLoginInfo.getBaseInfo().getStaffName(),
                    queryDTO, queryDTO.getEmail(), ExportOrderApplyEnum.PROVIDER, cacheKey, excelFileName,OrderApplyBusinessExport.class);
        });
        //设置频率控制标识
        cache.set(cacheKey, "Y", CREATE_EXCEL_TO_MAIL_TIMEOUT_MINUTES*2*60 );
        RestResponse restResponse = RestResponse.success(null);
        restResponse.setMsg( "提交成功！请稍后查收电子邮件。（数据处理大约耗时"+ CREATE_EXCEL_TO_MAIL_TIMEOUT_MINUTES +"分钟）" );
        return restResponse;
    }


    private void setQueryDtoCommonInfo(OrderApplyOpeInputDTO queryDTO, ProviderLoginInfo providerLoginInfo){
        queryDTO.setStaffId(providerLoginInfo.getBaseInfo().getStaffId());
        queryDTO.setStaffCode(providerLoginInfo.getBaseInfo().getStaffCode());
        queryDTO.setDataPermType(providerLoginInfo.obtainDataPerm().getDataPermType());
        queryDTO.setDataPermCodeSet(orderApplyExportService.getDataScopeList(providerLoginInfo));
        queryDTO.setLoginCompanyId(providerLoginInfo.getProviderCompany().getProviderId());
        queryDTO.setLoginId(providerLoginInfo.getBaseInfo().getStaffId());
    }

    private String getExcelFileName(Integer functionType){
        String excelFileName = "行程列表";
        if(Objects.isNull(functionType)){
            return excelFileName;
        }
        return ProviderOrderFunctionTypeEnum.getDescByFunctionType(functionType);
    }

}
