package com.izu.mrcar.providerController.order;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("里程纠偏记录")
public class OrderMileageCorrectExportDTO {

    @ExcelProperty(value = "记录ID")
    @ColumnWidth(value = 20)
    private String correctNo;

    @ExcelProperty(value = "子行程单号")
    @ColumnWidth(value = 20)
    private String orderNo;

    @ExcelProperty(value = "行程类型")
    @ColumnWidth(value = 20)
    private String serviceName;

    @ExcelProperty(value = "状态")
    @ColumnWidth(value = 20)
    private String statusStr;

    @ExcelProperty(value = "客户企业")
    @ColumnWidth(value = 20)
    private String companyName;

    @ExcelProperty(value = "乘车人")
    @ColumnWidth(value = 20)
    private String passengerName;

    @ExcelProperty(value = "车牌号")
    @ColumnWidth(value = 20)
    private String assignCarLicense;

    @ExcelProperty(value = "纠正前里程")
    @ColumnWidth(value = 20)
    private BigDecimal mileageBefore;

    @ExcelProperty(value = "纠正前里程来源")
    @ColumnWidth(value = 20)
    private String mileageSourceStr;

    @ExcelProperty(value = "纠正后里程")
    @ColumnWidth(value = 20)
    private BigDecimal mileageCorrect;

    @ExcelProperty(value = "行程开始时间")
    @ColumnWidth(value = 20)
    private Date factStartDate;

    @ExcelProperty(value = "行程结束时间")
    @ColumnWidth(value = 20)
    private Date factEndDate;

    @ExcelProperty(value = "操作人")
    @ColumnWidth(value = 20)
    private String dealName;

    @ExcelProperty(value = "操作时间")
    @ColumnWidth(value = 20)
    private Date dealTime;

}

