package com.izu.mrcar.providerController.order;

import com.google.common.collect.Maps;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.dto.mrcar.FileDTO;
import com.izu.mrcar.order.dto.order.OrderQuestionDTO;
import com.izu.mrcar.order.dto.order.OrderQuestionDealDTO;
import com.izu.mrcar.order.dto.order.OrderQuestionQueryDTO;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

@RestController
@Api(tags = "问题反馈")
public class OrderQuestionController {

    private static final long MAX_LINE = 10000;

    @PostMapping(MrcarOrderRestMsgCenter.PROVIDER_ORDER_QUESTION_GET_PAGE_LIST)
    @ApiOperation(value = "问题反馈列表", notes = "作者：连江伟")
    public RestResponse<PageDTO<OrderQuestionDTO>> getList(@RequestBody OrderQuestionQueryDTO param) {
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.PROVIDER_ORDER_QUESTION_GET_PAGE_LIST);
        final HashMap<String, Object> restParam = Maps.newHashMapWithExpectedSize(1);
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }

    @PostMapping(MrcarOrderRestMsgCenter.PROVIDER_ORDER_QUESTION_EXPORT)
    @ApiOperation(value = "问题反馈导出", notes = "作者：连江伟")
    @ExportExcelWeb(fileName = ExportExcelConstants.FILE_NAME_QUESTION_RECORD+ ExportExcelConstants.XLSX,filePath = ExportExcelConstants.FILE_PATH,
            sheet = ExportExcelConstants.FILE_NAME_QUESTION_RECORD, c = OrderQuestionExportDTO.class)
    public PageDTO<OrderQuestionExportDTO> export(@RequestBody OrderQuestionQueryDTO param, IzuEasyExcelSession izuEasyExcelSession,
                          HttpServletRequest request, HttpServletResponse response) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.PROVIDER_ORDER_QUESTION_GET_PAGE_LIST);
        izuEasyExcelSession.setUserName(providerLoginInfo.obtainBaseInfo().getStaffName());
        param.setPage(izuEasyExcelSession.getPageNo());
        param.setPageSize(ExportExcelConstants.PAGE_SIZE);

        final HashMap<String, Object> restParam = Maps.newHashMapWithExpectedSize(1);
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        RestResponse<PageDTO<OrderQuestionDTO>> res = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null, OrderQuestionDTO.class);
        if(null!=res && res.isSuccess()){
            PageDTO<OrderQuestionDTO> pageDTO = res.getData();
            if (pageDTO.getTotal()>MAX_LINE){
                throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
            }
            List<OrderQuestionExportDTO> list = BeanUtil.copyList(pageDTO.getResult(),OrderQuestionExportDTO.class);
            return new PageDTO<>(pageDTO.getPage(), pageDTO.getPageSize(), pageDTO.getTotal(), list);
        }else {
            return null;
        }

    }

    @PostMapping(MrcarOrderRestMsgCenter.PROVIDER_ORDER_QUESTION_DEAL)
    @ApiOperation(value = "问题反馈处理", notes = "作者：连江伟")
    public RestResponse<Boolean> deal(@RequestBody OrderQuestionDealDTO param) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        param.setLoginCode(providerLoginInfo.getBaseInfo().getStaffCode());
        param.setLoginUserName(providerLoginInfo.getBaseInfo().getStaffName());
        param.setDealMobile(providerLoginInfo.obtainBaseInfo().getMobile());
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.PROVIDER_ORDER_QUESTION_DEAL);
        final HashMap<String, Object> restParam = Maps.newHashMapWithExpectedSize(1);
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }

    @PostMapping(MrcarOrderRestMsgCenter.PROVIDER_ORDER_QUESTION_GET_DETAIL)
    @ApiOperation(value = "问题反馈详情", notes = "作者：连江伟")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "questionNo",value = "问题编号",required = true)
    })
    public RestResponse<OrderQuestionDTO> detail(String questionNo) {
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.PROVIDER_ORDER_QUESTION_GET_DETAIL);
        final HashMap<String, Object> restParam = Maps.newHashMapWithExpectedSize(1);
        restParam.put("questionNo", questionNo);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, restParam, null);
    }

    @PostMapping(MrcarOrderRestMsgCenter.PROVIDER_ORDER_QUESTION_GET_PHOTO)
    @ApiOperation(value = "查看反馈照片", notes = "作者：连江伟")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "questionNo",value = "问题编号",required = true)
    })
    public RestResponse<List<FileDTO>> getQuestionPhotos(String questionNo) {
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.PROVIDER_ORDER_QUESTION_GET_PHOTO);
        final HashMap<String, Object> restParam = Maps.newHashMapWithExpectedSize(1);
        restParam.put("questionNo", questionNo);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, restParam, null);
    }

}
