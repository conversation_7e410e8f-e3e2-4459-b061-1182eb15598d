package com.izu.mrcar.providerController.order.lingsan;

import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.dto.lingsan.ReturnVehicle.ReturnVehicleRangeDateDTO;
import com.izu.mrcar.order.dto.lingsan.ReturnVehicle.ReturnVehicleRecordDTO;
import com.izu.mrcar.order.dto.lingsan.audit.AuditInitDTO;
import com.izu.mrcar.utils.lingsan.PermissionUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * @Program: mrcar_app
 * @Description: 零散用车-还车
 * @Author: inT
 * @Create: 2023-02-03
 */
@SuppressWarnings("all")
@Api(tags="零散用车")
@RestController
public class PlatformReturnVehicleRecordController {


    @ApiImplicitParams({
            @ApiImplicitParam(name="auditType",value="审核类型（1：发车前确认审核；2：用车前资料审核；3：换车资料审核）",paramType="query",required = true),
            @ApiImplicitParam(name="supplierOrderNum",value="供应商订单号",paramType="query",required = true),
    })
    @ApiOperation(value = "还车-资料回传-页面初始化")
    @RequestFunction(functionName = "页面初始化")
    @GetMapping(MrcarOrderRestMsgCenter.LINGSAN_PLATFORM_RETURN_VEHICLE_RECORD_PAGE_INIT)
    public RestResponse<AuditInitDTO> returnDataPageInit(@RequestParam("auditType") Integer auditType,
                                           @RequestParam("supplierOrderNum") String supplierOrderNum) {
        String url = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.LINGSAN_RETURN_VEHICLE_RECORD_PAGE_INIT);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("auditType", auditType);
        paramMap.put("supplierOrderNum", supplierOrderNum);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.GET, url, paramMap, null, AuditInitDTO.class);
    }


}
