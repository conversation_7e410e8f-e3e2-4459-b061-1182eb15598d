package com.izu.mrcar.providerController.order.daibuche;

import com.alibaba.nacos.shaded.com.google.common.collect.Maps;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.dto.daibuche.bill.req.ApplyRecordQueryReqDTO;
import com.izu.mrcar.order.dto.daibuche.bill.req.ApplyRecordResDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * @ClassName ThirdApplyRecordController
 * <AUTHOR>
 * @Date 2024/6/30 16:03
 * @Version 1.0
 */

@Slf4j
@Api(tags = "申请记录")
@RestController
public class ThirdApplyRecordController {

    @PostMapping(MrcarOrderRestMsgCenter.THIRD_APPLY_RECORD_LIST)
    @RequestFunction(functionName = "申请记录列表")
    @ApiOperation(value = "申请记录列表")
    public RestResponse<PageDTO<ApplyRecordResDTO>> getList(@RequestBody ApplyRecordQueryReqDTO param) {
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.THIRD_APPLY_RECORD_LIST);
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, ApplyRecordResDTO.class);
    }


    @PostMapping(MrcarOrderRestMsgCenter.THIRD_APPLY_RECORD_HANDLE)
    @ApiOperation("人工处理请求记录")
    @RequestFunction(functionName = "人工处理请求记录")
    public RestResponse<Void> detail(Integer id) {
        String restUrl =  new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.THIRD_APPLY_RECORD_HANDLE);
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, id);
        paramMap.put("id",id);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null);
    }


    @PostMapping(MrcarOrderRestMsgCenter.THIRD_APPLY_RECORD_REAPPLY)
    @ApiOperation("重新请求")
    @RequestFunction(functionName = "重新请求")
    public RestResponse<Void> reApply(Integer id) {
        String restUrl =  new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.THIRD_APPLY_RECORD_REAPPLY);
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, id);
        paramMap.put("id",id);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null);
    }
}