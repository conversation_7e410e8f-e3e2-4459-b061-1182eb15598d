package com.izu.mrcar.providerController.order;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.common.collect.Maps;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.constants.Constant;
import com.izu.mrcar.common.constants.ConstantCode;
import com.izu.mrcar.controller.base.OrderBaseController;

import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.dto.dispatching.ScheduleCarDriverDTO;
import com.izu.mrcar.order.dto.order.BusinessScheduleDetailRespDTO;
import com.izu.mrcar.order.dto.order.BusinessScheduleDriverReqDTO;
import com.izu.mrcar.order.dto.order.BusinessScheduleVehicleReqDTO;
import com.izu.mrcar.order.dto.order.BusinessScheduleVehicleRespDTO;
import com.izu.mrcar.order.dto.order.*;
import com.izu.mrcar.order.dto.provider.input.ScheduleListInputDTO;
import com.izu.mrcar.order.dto.provider.output.ScheduleListDTO;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.driver.DispatchForDriverReqDTO;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.enums.perm.ProviderDataPermTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@RestController
@Api(tags = "商务车订单")
public class ScheduleCenterProviderController extends OrderBaseController {

    private static final Logger logger = LoggerFactory.getLogger(ScheduleCenterProviderController.class);

    @PostMapping(MrcarOrderRestMsgCenter.SCHEDULE_CENTER_PROVIDER_LIST)
    @ApiOperation(value = "商务车调度列表", notes = "作者：mapp")
    public RestResponse<PageDTO<ScheduleListDTO>> getList(@RequestBody ScheduleListInputDTO schedule) {
        if (null == schedule.getPageNo()) {
            schedule.setPageNo(ConstantCode.PAGE_NO);
        }
        if (null == schedule.getPageSize()) {
            schedule.setPageSize(ConstantCode.PAGE_SIZE);
        }
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        if (providerLoginInfo == null) {
            return RestResponse.fail(Constant.CUSTOMER_NOT_LOGIN);
        }
        schedule.setCompanyId(Long.valueOf(providerLoginInfo.getProviderCompany().getProviderId()));
        schedule.setStaffId(providerLoginInfo.obtainBaseInfo().getStaffId());
        schedule.setLoginId(providerLoginInfo.obtainBaseInfo().getStaffId());
        schedule.setStaffCode(providerLoginInfo.obtainBaseInfo().getStaffCode());
        schedule.setDataPermType(providerLoginInfo.obtainDataPerm().getDataPermType());
        schedule.setDataPermCodeSet(getDataScopeList(providerLoginInfo));
        final String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.SCHEDULE_CENTER_PROVIDER_LIST);
        final HashMap<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, schedule);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null);
    }

    @GetMapping(MrcarOrderRestMsgCenter.BUSINESS_SCHEDULE_DETAIL)
    @ApiOperation(value = "商务车调度详情", notes = "作者：牛子联")
    @RequestFunction(functionName = "商务车调度详情")
    public RestResponse<BusinessScheduleDetailRespDTO> getScheduleDetail(@Verify(param = "orderApplyNo", rule = "required") String orderApplyNo) {
        final HashMap<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put("orderApplyNo", orderApplyNo);
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.BUSINESS_SCHEDULE_DETAIL);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.GET, restUrl, paramMap, null);
    }

    /**
     * 商务车调度选择车辆列表
     */
    @PostMapping(MrcarOrderRestMsgCenter.BUSINESS_SCHEDULE_VEHICLE_LIST)
    @ApiOperation(value = "商务车调度选择车辆列表", notes = "作者：牛子联")
    @RequestFunction(functionName = "商务车调度选择车辆列表")
    public RestResponse<PageDTO<BusinessScheduleVehicleRespDTO>> getScheduleVehicleList(@RequestBody  BusinessScheduleVehicleReqDTO reqDTO){
        final HashMap<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.BUSINESS_SCHEDULE_VEHICLE_LIST);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null);
    }

    /**
     * 商务车调度司机列表
     */
    @PostMapping(MrcarOrderRestMsgCenter.BUSINESS_SCHEDULE_DRIVER_LIST)
    @ApiOperation(value = "商务车调度司机列表", notes = "作者：牛子联")
    @RequestFunction(functionName = "商务车调度司机列表")
    public RestResponse<PageDTO<BusinessScheduleDriverRespDTO>> getScheduleDriverList(@RequestBody BusinessScheduleDriverReqDTO reqDTO){
        final HashMap<String, Object> paramMap = new HashMap<String, Object>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.BUSINESS_SCHEDULE_DRIVER_LIST);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null);
    }

    /**
     * 立即调度
     */
    @PostMapping(MrcarOrderRestMsgCenter.BUSINESS_ORDER_DISPATCH)
    @ApiOperation(value = "商务车立即调度", notes = "作者：牛子联")
    @RequestFunction(functionName = "商务车立即调度")
    public RestResponse<Boolean> doDispatchOrder(@RequestBody ScheduleCarDriverDTO scheduleCarDriverDTO) {
        return postBodyWithLogin(MrcarOrderRestMsgCenter.BUSINESS_ORDER_DISPATCH, scheduleCarDriverDTO);
    }


    @RequestMapping("/scheduleCenterProvider/queryOrderInfoListByDate")
    public RestResponse queryOrderInfoListForScheduleCalendar(@RequestBody JSONObject restObject) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        if (providerLoginInfo == null) {
            return RestResponse.fail(Constant.CUSTOMER_NOT_LOGIN);
        }
        Integer page = restObject.getInteger("page");
        Integer pageSize = restObject.getInteger("pageSize");
        int companyId = providerLoginInfo.getProviderCompany().getProviderId();
        Map<String, Object> params = restObject.getInnerMap();
        params.put("loginCompanyId", companyId);
        params.put("loginId", providerLoginInfo.getBaseInfo().getStaffId());
        params.put("dataPermType",providerLoginInfo.getProviderDataPerm().getDataPermType());
        Set<String> permSet = getDataScopeList(providerLoginInfo);
        params.put("permissions", permSet == null ? "" : String.join(",",permSet));
        String restUrl = new MrcarOrderRestLocator().getRestUrl("/scheduleCenterProvider/queryOrderInfoListForScheduleCalendar");

        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, params, null);
    }

    @RequestMapping("/scheduleCenterProvider/getOrderInfoCountByDay")
    //@RequiresPermissions(value = "childProcessList")
    public RestResponse getOrderInfoCountByDay(@RequestBody JSONObject restObject) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        if (providerLoginInfo == null) {
            return RestResponse.fail(Constant.CUSTOMER_NOT_LOGIN);
        }
        logger.info("providerLoginInfo={}", JSON.toJSONString(providerLoginInfo));
        int companyId = providerLoginInfo.getProviderCompany().getProviderId();
        Map<String, Object> params = restObject.getInnerMap();
        params.put("loginCompanyId", companyId);
        params.put("loginId", providerLoginInfo.getBaseInfo().getStaffId());
        params.put("dataPermType",providerLoginInfo.getProviderDataPerm().getDataPermType());
        Set<String> permSet = getDataScopeList(providerLoginInfo);
        params.put("permissions", permSet == null ? "" : String.join(",",permSet));
        String restUrl = new MrcarOrderRestLocator().getRestUrl("/scheduleCenterProvider/getOrderInfoCountByDay");
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, params, null);
    }

    private Set<String> getDataScopeList(ProviderLoginInfo providerLoginInfo){
        Set<String> res = null;
        if(ProviderDataPermTypeEnum.ALL.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())){
            return null;
        }else if(ProviderDataPermTypeEnum.RESPONSIBLE_CONTRACT.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())){
            res = providerLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.VEHICLE).getDataCodeSet();
        }else if(ProviderDataPermTypeEnum.RESPONSIBLE_CUSTOMER.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())||
                ProviderDataPermTypeEnum.ASSIGN_CUSTOMER.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())){
            res = providerLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.ID).getDataCodeSet();
        }else if(ProviderDataPermTypeEnum.SELF_DEPT.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())||
                ProviderDataPermTypeEnum.ASSIGN_DEPT.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())){
            res = providerLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet();
        }
        return res;
    }


    /**
     * 司机列表
     * @param
     * @return
     * <AUTHOR>
     * 2018年11月29日
     */
    @PostMapping("/provider/listDriver")
    public RestResponse listDriver(@RequestBody DispatchForDriverReqDTO reqDTO) {
        reqDTO.setIsFromPC(Boolean.TRUE);
        return postBodyWithLogin("/driver/querySchedulableDriverList", reqDTO);
    }

}
