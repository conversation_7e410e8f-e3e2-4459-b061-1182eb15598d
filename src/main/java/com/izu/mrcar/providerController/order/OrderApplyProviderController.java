package com.izu.mrcar.providerController.order;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.merge.AbstractMergeStrategy;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.izu.CouponCoreRestMsgCenter;
import com.izu.MrCarCouponRestLocator;
import com.izu.carasset.CarAssetRestLocator;
import com.izu.carasset.CarAssetRestUrl;
import com.izu.carasset.dto.output.AreaDTO;
import com.izu.crm.CrmRestLocator;
import com.izu.framework.doc.JrdApiDoc;
import com.izu.framework.doc.JrdApiParamDoc;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.config.ConstansConfig;
import com.izu.mrcar.common.constants.Constant;
import com.izu.mrcar.controller.order.ExcelWidthStyleStrategy;
import com.izu.mrcar.controller.order.OrderController;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.consts.OrderChannelSourceEnum;
import com.izu.mrcar.order.consts.OrderEnum;
import com.izu.mrcar.order.dto.businessOrder.*;
import com.izu.mrcar.order.dto.mrcar.OrderInfoDTO;
import com.izu.mrcar.order.dto.mrcar.OrderInfoExport;
import com.izu.mrcar.order.dto.mrcar.OrderPriceDTO;
import com.izu.mrcar.order.dto.order.OrderApplyExportDTO;
import com.izu.mrcar.order.dto.order.OrderCreateReqDTO;
import com.izu.mrcar.order.dto.order.OrderInfoExportDTO;
import com.izu.mrcar.order.dto.order.OrderInterface;
import com.izu.mrcar.order.dto.provider.input.OrderApplyOpeInputDTO;
import com.izu.mrcar.order.dto.provider.input.OrderModifyInputDTO;
import com.izu.mrcar.order.dto.provider.input.ProviderOrderInfoQueryDTO;
import com.izu.mrcar.order.dto.provider.output.OrderApplyOpeOutputDTO;
import com.izu.mrcar.service.order.ExportOrderService;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.dto.company.CrmCompanyDTO;
import com.izu.user.dto.staff.pc.AccountBelongCity;
import com.izu.user.dto.staff.pc.AccountBelongDepartment;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.enums.perm.ProviderDataPermTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import izu.org.apache.poi.ss.usermodel.Cell;
import izu.org.apache.poi.ss.usermodel.HorizontalAlignment;
import izu.org.apache.poi.ss.usermodel.IndexedColors;
import izu.org.apache.poi.ss.usermodel.Sheet;
import izu.org.apache.poi.ss.usermodel.VerticalAlignment;
import izu.org.apache.poi.ss.util.CellRangeAddress;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.alibaba.excel.EasyExcelFactory.write;

@RestController
@Slf4j
@Api(tags = "运营端")
public class OrderApplyProviderController{

    private static final Logger logger = LoggerFactory.getLogger(OrderApplyProviderController.class);

    private static final String QUERY_ORDER_EXPORT_LIST = "/order/queryOrderExportList";

    @Autowired
    private ExportOrderService exportOrderService;



    @PostMapping(MrcarOrderRestMsgCenter.ORDER_APPLY_PROVIDER_LIST)
    @ApiOperation(value = "行程列表", notes = "作者：mapp")
    @RequestFunction(functionName = "行程列表")
//    @RequiresPermissions(value = {"ApplyVehicleList", "SelfApplyVehicleList"}, logical = Logical.OR)
    public RestResponse<PageDTO<OrderApplyOpeOutputDTO>> getList(@RequestBody OrderApplyOpeInputDTO queryDTO) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        if (providerLoginInfo == null) {
            return RestResponse.fail(Constant.CUSTOMER_NOT_LOGIN);
        }
        queryDTO.setStaffId(providerLoginInfo.getBaseInfo().getStaffId());
        queryDTO.setStaffCode(providerLoginInfo.getBaseInfo().getStaffCode());
        queryDTO.setDataPermType(providerLoginInfo.obtainDataPerm().getDataPermType());
        queryDTO.setDataPermCodeSet(getDataScopeList(providerLoginInfo));
        queryDTO.setLoginCompanyId(providerLoginInfo.getProviderCompany().getProviderId());
        queryDTO.setLoginId(providerLoginInfo.getBaseInfo().getStaffId());
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.ORDER_APPLY_PROVIDER_LIST);
        final HashMap<String, Object> restParam = Maps.newHashMapWithExpectedSize(1);
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, queryDTO);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null, OrderApplyOpeOutputDTO.class);
    }

    @PostMapping("/orderApplyProvider/makeOrder")
//    @RequiresPermissions(value = {"car_approval_apply", "self_car_approval_apply"}, logical = Logical.OR)
    @RequestFunction(functionName = "创建订单(运营端)")
    @ApiOperation(value = "创建订单(运营端)")
    public RestResponse makeOrder(@RequestBody OrderCreateReqDTO orderCreateReqDTO) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        orderCreateReqDTO.setCustomerId(providerLoginInfo.getBaseInfo().getStaffId());
        orderCreateReqDTO.setCustomerName(providerLoginInfo.getBaseInfo().getStaffName());
        orderCreateReqDTO.setCustomerMobile(providerLoginInfo.getBaseInfo().getMobile());
        orderCreateReqDTO.setCompanyId(providerLoginInfo.getProviderCompany().getProviderId());
        orderCreateReqDTO.setCompanyName(providerLoginInfo.getProviderCompany().getProviderName());
        List<AccountBelongDepartment> structList = providerLoginInfo.getBelongDepartmentList();
        if(structList != null && structList.size() > 0){
            orderCreateReqDTO.setStructId(structList.get(0).getDeptId());
            orderCreateReqDTO.setStructName(structList.get(0).getDeptName());
        }
        if (orderCreateReqDTO.getCustomerCompanyId() == null || orderCreateReqDTO.getCustomerCompanyId()<=0) {
            orderCreateReqDTO.setCustomerCompanyId(providerLoginInfo.obtainBelongCompanyId());
        }
        List<AccountBelongCity> belongCityList = providerLoginInfo.getBelongCityList();
        if(belongCityList != null && belongCityList.size() > 0){
            orderCreateReqDTO.setLoginCityCode(belongCityList.get(0).getCityCode());
            orderCreateReqDTO.setLoginCityName(belongCityList.get(0).getCityName());
        }
        if(Boolean.TRUE.equals(orderCreateReqDTO.getHandyDispatch())){
            orderCreateReqDTO.setOrderChannelSource(OrderChannelSourceEnum.PC_ORDER_QUICK.getDesc());
        }else{
            orderCreateReqDTO.setOrderChannelSource(OrderChannelSourceEnum.PC_ORDER.getDesc());
        }
        String restUrl = new MrcarOrderRestLocator().getRestUrl(OrderInterface.createOrder);
        Map<String,Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY,orderCreateReqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl,restParam, null);
    }



    @PostMapping("/orderApplyProvider/rollback")
    @RequestFunction(functionName = "商务车账单退回(运营端)")
    @ApiOperation(value = "商务车账单退回(运营端)")
    public RestResponse rollback(@RequestParam("orderApplyNo") String orderApplyNo) {
        String restUrl = new MrcarOrderRestLocator().getRestUrl("/orderApplyProvider/rollback");
        Map<String,Object> restParam = new HashMap<>();
        restParam.put("orderApplyNo",orderApplyNo);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.GET, restUrl,restParam, null);
    }

    @PostMapping(value = "/provider/order/orderApplyCancel")
    @RequestFunction(functionName = "取消订单(运营端)")
    @ApiOperation(value = "取消订单(运营端)")
    @ResponseBody
    public RestResponse orderApplyCancel(@RequestBody JSONObject restObject) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        if (providerLoginInfo == null) {
            return RestResponse.fail(Constant.CUSTOMER_NOT_LOGIN);
        }
        String restUrl = new MrcarOrderRestLocator().getRestUrl("/order/orderApplyCancel");
        restObject.put("cancelSourceType", "0");
        restObject.put("cancelUserId", providerLoginInfo.getBaseInfo().getStaffId());
        restObject.put("cancelUserName", providerLoginInfo.getBaseInfo().getStaffName());
        try {
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, restObject.getInnerMap(), null, Boolean.class);
        } catch (Exception e) {
            return RestResponse.fail(Constant.SYS_ERROR);
        }

    }

    /**
     * 子订单取消
     *
     * @param restObject
     * @return
     */
    @PostMapping(value = "/provider/order/orderCancel")
    @RequestFunction(functionName = "取消子订单(运营端)")
    @ApiOperation(value = "取消子订单(运营端)")
    @ResponseBody
    public RestResponse orderCancel(@RequestBody JSONObject restObject) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        String restUrl = new MrcarOrderRestLocator().getRestUrl("/order/orderCancel");
        //运营端取消不需要校验取消人
        restObject.put("cancelSourceType", "1");
        restObject.put("cancelUserId", providerLoginInfo.getBaseInfo().getStaffId());
        restObject.put("cancelUserName", providerLoginInfo.getBaseInfo().getStaffName());
        try {
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, restObject.getInnerMap(), null, Boolean.class);
        } catch (Exception e) {
            return RestResponse.fail(Constant.SYS_ERROR);
        }

    }

    //    @RequiresPermissions(value = {"modify_price", "self_modify_price"}, logical = Logical.OR)
    @PostMapping(value = "/provider/order/v2/orderPrice")
    @ApiOperation(value = "调价接口")
    public RestResponse orderPriceV2(@RequestBody OrderModifyInputDTO orderModifyInputDTO) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        if (providerLoginInfo == null) {
            return RestResponse.fail(Constant.CUSTOMER_NOT_LOGIN);
        }
        int loginUserId = providerLoginInfo.getBaseInfo().getStaffId();
        String loginName = providerLoginInfo.getBaseInfo().getStaffName();
        String restUrl = new MrcarOrderRestLocator().getRestUrl("/consumeBill/v2/orderPrice");
        Map<String, Object> params = new HashMap<>();
//        params.put("loginUserId", loginUserId);
//        params.put("loginName", loginName);
//        params.put("orderApplyNo", restObject.getString("orderApplyNo"));
//        params.put("reductionAmount", restObject.getBigDecimal("reductionAmount"));
//        params.put("otherAmount", restObject.getBigDecimal("otherAmount"));
//        params.put("priceExplain", restObject.getString("priceExplain"));
//        params.put("subModify", restObject.getJSONArray("subModify").toString());
//        params.put("fileList", restObject.getJSONArray("fileList").toString());
        orderModifyInputDTO.setLoginUserId(loginUserId);
        orderModifyInputDTO.setLoginUserName(loginName);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY,orderModifyInputDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, params, null);
    }

    //    @RequiresPermissions(value = {"modify_price", "self_modify_price"}, logical = Logical.OR)
    @PostMapping(value = "/provider/order/v2/getOrderPriceInfo")
    @ApiOperation(value = "查询调价接口")
    public RestResponse<OrderPriceDTO> getOrderPriceInfo(@RequestBody ProviderOrderInfoQueryDTO orderInfoQueryDTO) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        if (providerLoginInfo == null) {
            return RestResponse.fail(Constant.CUSTOMER_NOT_LOGIN);
        }
        String restUrl = new MrcarOrderRestLocator().getRestUrl("/consumeBill/v2/getOrderPriceInfo");
        Map<String, Object> params = new HashMap<>();
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, orderInfoQueryDTO);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, params, null, null);
    }

    /**
     * @Description: 发送账单确认短信
     * @author: hxc
     * @Date: 2019/9/17
     **/
    @PostMapping(value = "/provider/consumeBill/confirmmessage")
    @ResponseBody
    public RestResponse confirmmessage(@RequestBody JSONObject restObject) {
        Map<String, Object> params = restObject.getInnerMap();
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        if (providerLoginInfo == null) {
            return RestResponse.fail(Constant.CUSTOMER_NOT_LOGIN);
        }
        int customerId = providerLoginInfo.getBaseInfo().getStaffId();
        String customerName = providerLoginInfo.getBaseInfo().getStaffName();
        params.put("customerId", customerId);
        params.put("customerName", customerName);
        String restUrl = new UserRestLocator().getRestUrl("/consumeBill/confirmmessage");
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, params, null);
    }

    @RequestMapping(value = "/provider/order/reductionLimit")
    public RestResponse orderApplyReductionLimit(HttpServletRequest request) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        if (providerLoginInfo == null) {
            return RestResponse.fail(Constant.CUSTOMER_NOT_LOGIN);
        }
        int customerId = providerLoginInfo.getBaseInfo().getStaffId();
        try {
            String restUrl = new UserRestLocator().getRestUrl("/userManage/getPercentageByCustomerId");
            Map<String, Object> params = new HashMap<>();
            params.put("customerId", customerId);
            RestResponse restResponse = RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, params, null);
            return RestResponse.success(restResponse.getData());
        } catch (Exception e) {
            logger.error("获取订单减免上限失败", e);
        }
        return RestResponse.success(0);
    }

    /**
     * @Description: 订单列表异步导出
     * @author: hxc
     * @Date: 2020/9/3
     **/
    @RequestMapping(value = "/provider/order/orderExport/asyn")
//    @RequiresPermissions(value = {"orderApplyExportAsyn", "selfOrderApplyExportAsyn"}, logical = Logical.OR)
    public RestResponse exportOrderListAsyn(String orderApplyNo, Byte orderType, String serviceCode, String customerName, Integer companyId, String bookingPassengerUserName,
                                            Integer customerCompanyId, String orderStatus, String createTimeStart, String createTimeEnd, String bookingOrderStime, String bookingOrderEtime, String email,Byte auditFlag,
                                            @Verify(param = "customerMobile", rule = "") String customerMobile,
                                            @Verify(param = "structId",rule="") @JrdApiParamDoc(desc = "下单人部门id",example="" ) Integer structId,
                                            @Verify(param = "passengerStructId",rule="") @JrdApiParamDoc(desc = "乘车人部门id",example="" ) Integer passengerStructId,
                                            @Verify(param = "sort",rule="") @JrdApiParamDoc(desc = "排序字段",example="" ) String sort,
                                            @Verify(param = "bookingPassengerUserPhone", rule = "") String bookingPassengerUserPhone, Boolean onlySelf) {
        if (StringUtils.isBlank(email)) {
            RestResponse restResponse = RestResponse.fail(9999);
            restResponse.setMsg("邮箱号不能为空！！！");
            return restResponse;
        }
        Map<String, Object> param = new HashMap<>();
        param.put("orderType", orderType);
        param.put("orderApplyNo", orderApplyNo);
        param.put("serviceCode", serviceCode);
        param.put("customerCompanyId", customerCompanyId);
        param.put("customerName", customerName);
        param.put("companyId", companyId);
        param.put("bookingPassengerUserName", bookingPassengerUserName);
        param.put("createTimeStart", createTimeStart);
        param.put("createTimeEnd", createTimeEnd);
        param.put("orderStatus", orderStatus);
        param.put("bookingOrderStime", bookingOrderStime);
        param.put("bookingOrderEtime", bookingOrderEtime);
        ProviderLoginInfo providerLoginInfo =  LoginSessionUtil.getProviderLoginInfo();
        int loginCompanyId = providerLoginInfo.getProviderCompany().getProviderId();
        param.put("loginCompanyId", loginCompanyId);
        param.put("loginId", providerLoginInfo.getBaseInfo().getStaffId());
        param.put("loginUserName", providerLoginInfo.getBaseInfo().getStaffName());
        Set<String> permSet = getDataScopeList(providerLoginInfo);
        param.put("permissions", permSet == null ? "" : String.join(",",permSet));
        param.put("isAsynExport", true);
        param.put("email", email);
        param.put("customerMobile", customerMobile);
        param.put("bookingPassengerUserPhone", bookingPassengerUserPhone);
        param.put("onlySelf", onlySelf);
        param.put("structId", structId);
        param.put("passengerStructId", passengerStructId);
        param.put("sort", sort);
        param.put("auditFlag", auditFlag);
        param.put("dataPermType",providerLoginInfo.getProviderDataPerm().getDataPermType());
        param.put("loginSystemType", providerLoginInfo.getSystemType());
        String restUrl = new MrcarOrderRestLocator().getRestUrl(QUERY_ORDER_EXPORT_LIST);
        RestResponse result = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, param, null, OrderInfoExport.class);
        return result;
    }



    /**
     * 导出订单列表
     *
     * @param
     * @return
     */
    @PostMapping("/orderApplyProvider/exportOrderList")
    @ApiOperation(value = "行程列表导出", notes = "作者：mapp")
    @RequestFunction(functionName = "行程列表导出")
    public void exportOrderList(String orderApplyNo, Byte orderType, String serviceCode, String customerName, Integer companyId, String bookingPassengerUserName,
                                Integer customerCompanyId, String orderStatus, String createTimeStart, String createTimeEnd, String bookingOrderStime, String bookingOrderEtime,Byte auditFlag,
                                @Verify(param = "customerMobile", rule = "") String customerMobile,
                                @Verify(param = "structId",rule="") @JrdApiParamDoc(desc = "下单人部门id",example="" ) Integer structId,
                                @Verify(param = "passengerStructId",rule="") @JrdApiParamDoc(desc = "乘车人部门id",example="" ) Integer passengerStructId,
                                @Verify(param = "sort",rule="") @JrdApiParamDoc(desc = "排序字段",example="" ) String sort,
                                @Verify(param = "bookingPassengerUserPhone", rule = "") String bookingPassengerUserPhone, Boolean onlySelf, HttpServletResponse response) {
        try {
            Map<String, Object> param = new HashMap<>();
            param.put("orderType", orderType);
            param.put("orderApplyNo", orderApplyNo);
            param.put("serviceCode", serviceCode);
            param.put("customerCompanyId", customerCompanyId);
            param.put("customerName", customerName);
            param.put("companyId", companyId);
            param.put("bookingPassengerUserName", bookingPassengerUserName);
            param.put("createTimeStart", createTimeStart);
            param.put("createTimeEnd", createTimeEnd);
            param.put("orderStatus", orderStatus);
            param.put("bookingOrderStime", bookingOrderStime);
            param.put("bookingOrderEtime", bookingOrderEtime);
            param.put("structId", structId);
            param.put("passengerStructId", passengerStructId);
            param.put("sort", sort);
            ProviderLoginInfo providerLoginInfo =  LoginSessionUtil.getProviderLoginInfo();
            int loginCompanyId = providerLoginInfo.getProviderCompany().getProviderId();
            param.put("loginCompanyId", loginCompanyId);
            param.put("loginId", providerLoginInfo.getBaseInfo().getStaffId());
            Set<String> permSet = getDataScopeList(providerLoginInfo);
            param.put("permissions", permSet == null ? "" : String.join(",",permSet));
            param.put("customerMobile", customerMobile);
            param.put("bookingPassengerUserPhone", bookingPassengerUserPhone);
            param.put("onlySelf", onlySelf);
            param.put("auditFlag", auditFlag);
            param.put("dataPermType",providerLoginInfo.getProviderDataPerm().getDataPermType());
            param.put("loginSystemType", providerLoginInfo.getSystemType());
//            String restUrl = new MrcarOrderRestLocator().getRestUrl("/provider/queryOrderNoList");
//            RestResponse result = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, param, null, OrderInfoExport.class);
//            if (!result.isSuccess()) {
//                return;
//            }
//            List<OrderInfoExport> resultData = (List<OrderInfoExport>) result.getData();
            exportOrderService.requestData(param, onlySelf, loginCompanyId, response,orderType);
        } catch (Exception e) {
            logger.error("导出订单信息失败", e);
        }
    }


    private void exportExcel(List<OrderInfoExport> data, int type, Boolean onlySelf, boolean isCustomer, Byte orderType, HttpServletResponse response) {
        try (OutputStream outputStream = response.getOutputStream()) {
            List<List<String>> head = new ArrayList<>();
            List<List<Object>> excelData = new ArrayList<>();

            String fileName;
            boolean auditFlag = false;
            if (null != onlySelf && onlySelf) {
                fileName = "我的行程_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
            } else {
                fileName = "行程列表_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
            }
            if (type==1&&isCustomer){
                auditFlag = true;
            }
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''"  + URLEncoder.encode(fileName, "UTF-8") + ".xls");
            int[] type1Merge = new int[]{0, 1, 2, 3, 4, 5, 6,7, 45, 46, 47, 48, 49,50,51,52,53,54};
            int[] orderNoMerge1 = new int[]{8,9,10,11,12,13,14,15,16,17,18,19,20,
                    21,22,23,24,25,26,27,28,29,
                    30,31,32,33,34,35,36,37,38,39,40,41,42,43, 44};
            // 第8列是子行程编号
//            if (onlySelf || (orderType != null && orderType != 1)) {
//                type1Merge = new int[]{0, 1, 2, 3, 4, 5, 6, 7, 44, 45, 46, 47, 48, 49, 50, 51, 52,53};
//                orderNoMerge1 = new int[]{8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20,
//                        21, 22, 23, 24, 25, 26, 27, 28, 29,
//                        30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41,42,43};
//            }

            //生成表头
            List<String> applyNo = new ArrayList<>();
            applyNo.add("主行程编号");
            head.add(applyNo);
            List<String> applyCreateTime = new ArrayList<>();
            applyCreateTime.add("主行程创建时间");
            head.add(applyCreateTime);
            List<String> orderTypeList = new ArrayList<>();
            orderTypeList.add("订单类型");
            head.add(orderTypeList);
            List<String> serviceName = new ArrayList<>();
            serviceName.add("产品类型");
            head.add(serviceName);
            List<String> customerMobile = new ArrayList<>();
            customerMobile.add("下单人手机号");
            head.add(customerMobile);
            if (!isCustomer) {
                //首汽企业显示签约主体
                List<String> settleCompanyName = new ArrayList<>();
                settleCompanyName.add("签约主体");
                head.add(settleCompanyName);
                type1Merge = new int[]{0, 1, 2, 3, 4, 5, 6, 7, 43, 44, 45, 46, 47, 48, 49,50,51,52,53};
                orderNoMerge1 = new int[]{8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,
                        30,31,32,33,34,35,36,37,38,39,40,41,42,43};
            }
            List<String> customerName = new ArrayList<>();
            customerName.add("下单人姓名");
            head.add(customerName);
            if (type == 2){
                //接单管理显示下单企业
                List<String> companyName = new ArrayList<>();
                companyName.add("下单企业");
                head.add(companyName);
            }

            List<String> applyStatusName = new ArrayList<>();
            applyStatusName.add("订单状态");
            head.add(applyStatusName);

            if (auditFlag){
                List<String> applyAuditFlag = new ArrayList<>();
                applyAuditFlag.add("是否审批");
                head.add(applyAuditFlag);
            }

            //子行程信息表头
            generateSubHead(head, type, isCustomer, onlySelf, orderType);

            List<String> otherAmount = new ArrayList<>();
            otherAmount.add("主行程其他费用");
            head.add(otherAmount);

            List<String> reductionAmount = new ArrayList<>();
            reductionAmount.add("主行程减免费用");
            head.add(reductionAmount);

            List<String> couponDeductAmount = new ArrayList<>();
            couponDeductAmount.add("优惠券减免金额");
            head.add(couponDeductAmount);

            List<String> totalAmount = new ArrayList<>();
            totalAmount.add("合计金额(基础费用+总里程费用+总时长费用+司机补录费用)");
            head.add(totalAmount);

            List<String> fixAmount = new ArrayList<>();
            fixAmount.add("一口价");
            head.add(fixAmount);

            List<String> allTotalAmount = new ArrayList<>();
            allTotalAmount.add("应收金额");
            head.add(allTotalAmount);

            List<String> payTypeName = new ArrayList<>();
            payTypeName.add("支付方式");
            head.add(payTypeName);

            List<String> activityName = new ArrayList<>();
            activityName.add("优惠券名称");
            head.add(activityName);

            List<String> priceExplain = new ArrayList<>();
            priceExplain.add("调价原因");
            head.add(priceExplain);

            List<String> orderDetail = new ArrayList<>();
            orderDetail.add("备注");
            head.add(orderDetail);

            //int[] type1Merge = new int[]{0, 1, 2, 3, 4, 5, 6, 7, 42, 43, 44, 45, 46, 47, 48, 49,50,51,52,53,54,55,56};
            int[] type2Merge = new int[]{0, 1, 2, 3, 4, 5, 6, 7, 8, 41, 42, 43, 44, 45, 46, 47, 48,49,50};

            if (data == null || data.isEmpty()) {
                write(outputStream).head(head).excelType(ExcelTypeEnum.XLS).sheet(fileName).doWrite(excelData);
                return;
            } else {

                Map<String, List<OrderInfoExport>> applyList = data.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(OrderInfoExport::getOrderApplyNo,LinkedHashMap::new,Collectors.toList()));

                for (Map.Entry<String, List<OrderInfoExport>> entry : applyList.entrySet()) {
                    List<OrderInfoExport> value = entry.getValue();
                    if (value == null || value.isEmpty()) {
                        continue;
                    }

                    Map<String, List<OrderInfoExport>> orderList = value.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(OrderInfoExport::getOrderNo));

                    for (Map.Entry<String, List<OrderInfoExport>> orders : orderList.entrySet()) {

                        for (OrderInfoExport orderInfoExport : orders.getValue()) {
                            if (orderInfoExport == null) {
                                continue;
                            }
                            List<Object> info = new ArrayList<>();
                            info.add(orderInfoExport.getOrderApplyNo());
                            info.add(orderInfoExport.getCreateTime());
                            info.add(orderInfoExport.getOrderTypeName());
                            info.add(orderInfoExport.getServiceName());
                            info.add(orderInfoExport.getCustomerMobile());
                            if (!isCustomer) {
                                info.add(Optional.ofNullable(orderInfoExport.getSettleCompanyName()).orElse(StringUtils.EMPTY));
                            }
                            info.add(orderInfoExport.getCustomerName());
                            if (type == 2){
                                //接单列表才有下单企业
                                info.add(Optional.ofNullable(orderInfoExport.getCompanyName()).orElse(StringUtils.EMPTY));
                            }
                            info.add(orderInfoExport.getApplyStatusName());
                            if (auditFlag){
                                info.add(orderInfoExport.getAuditFlag()==1?"是":"否");
                            }
                            info.add(orderInfoExport.getOrderNo());
                            info.add(Optional.ofNullable(orderInfoExport.getCancelUserName()).orElse(StringUtils.EMPTY));
                            info.add(orderInfoExport.getPassengerUserNames());
                            info.add(orderInfoExport.getPassengerUserPhones());
                            if (!isCustomer) {
                                info.add(orderInfoExport.getCustomerCompanyName());
                            }else {
                                info.add(Optional.ofNullable(orderInfoExport.getPassengerStructName()).orElse(StringUtils.EMPTY));
                            }
                            if (type == 1) {
                                //行程列表才有联系人信息
                                info.add(Optional.ofNullable(orderInfoExport.getContactName()).orElse(StringUtils.EMPTY));
                                info.add(Optional.ofNullable(orderInfoExport.getContactMobile()).orElse(StringUtils.EMPTY));
                            }
                            info.add(Optional.ofNullable(orderInfoExport.getBookingOrderStime()).orElse(StringUtils.EMPTY));
                            info.add(Optional.ofNullable(orderInfoExport.getFactStartDate()).orElse(StringUtils.EMPTY));
                            info.add(Optional.ofNullable(orderInfoExport.getBookingOrderEtime()).orElse(StringUtils.EMPTY));
                            info.add(Optional.ofNullable(orderInfoExport.getFactEndDate()).orElse(StringUtils.EMPTY));
                            info.add(Optional.ofNullable(orderInfoExport.getStartCityName()).orElse(StringUtils.EMPTY));
                            info.add(Optional.ofNullable(orderInfoExport.getBookingStartShortAddr()).orElse(StringUtils.EMPTY));
                            info.add(Optional.ofNullable(orderInfoExport.getDestinationCityNames()).orElse(StringUtils.EMPTY));
                            info.add(Optional.ofNullable(orderInfoExport.getDestinations()).orElse(StringUtils.EMPTY));
                            info.add(Optional.ofNullable(orderInfoExport.getAssignCarlevelName()).orElse(StringUtils.EMPTY));
                            info.add(Optional.ofNullable(orderInfoExport.getAssignCarmodelName()).orElse(StringUtils.EMPTY));
                            info.add(Optional.ofNullable(orderInfoExport.getVehicleBrand()).orElse(StringUtils.EMPTY));
                            info.add(Optional.ofNullable(orderInfoExport.getAssignCarLicense()).orElse(StringUtils.EMPTY));
                            info.add(Optional.ofNullable(orderInfoExport.getAssignDriverName()).orElse(StringUtils.EMPTY));
                            info.add(Optional.ofNullable(orderInfoExport.getAssignDriverPhone()).orElse(StringUtils.EMPTY));
                            info.add(Optional.ofNullable(orderInfoExport.getOrderStatusName()).orElse(StringUtils.EMPTY));
                            info.add(Optional.ofNullable(orderInfoExport.getTotalDays()).orElse(StringUtils.EMPTY));
                            info.add(Optional.ofNullable(orderInfoExport.getTotalMileage()).orElse(StringUtils.EMPTY));
                            if (!onlySelf) {
                                info.add(Optional.ofNullable(orderInfoExport.getStartManualMileage()).orElse(StringUtils.EMPTY));
                                info.add(Optional.ofNullable(orderInfoExport.getEndManualMileage()).orElse(StringUtils.EMPTY));
                            }
                            info.add(Optional.ofNullable(orderInfoExport.getTotalTime()).orElse(StringUtils.EMPTY));
                            info.add(Optional.ofNullable(orderInfoExport.getBaseFee()).orElse(StringUtils.EMPTY));
                            info.add(Optional.ofNullable(orderInfoExport.getTotalMileageFee()).orElse(StringUtils.EMPTY));
                            info.add(Optional.ofNullable(orderInfoExport.getTotalTimeFee()).orElse(StringUtils.EMPTY));
                            info.add(Optional.ofNullable(orderInfoExport.getFeeAmount()).orElse(StringUtils.EMPTY));
                            info.add(Optional.ofNullable(orderInfoExport.getFeeAmount2()).orElse(StringUtils.EMPTY));
                            info.add(Optional.ofNullable(orderInfoExport.getFeeAmount3()).orElse(StringUtils.EMPTY));
                            info.add(Optional.ofNullable(orderInfoExport.getFeeAmount4()).orElse(StringUtils.EMPTY));
                            info.add(Optional.ofNullable(orderInfoExport.getReturnEmptyFee()).orElse(StringUtils.EMPTY));
                            info.add(Optional.ofNullable(orderInfoExport.getFeeAmount5()).orElse(StringUtils.EMPTY));
                            info.add(Optional.ofNullable(orderInfoExport.getFeeAmountRemark()).orElse(StringUtils.EMPTY));
                            info.add(Optional.ofNullable(orderInfoExport.getOtherAmountSub()).orElse(StringUtils.EMPTY));
                            info.add(Optional.ofNullable(orderInfoExport.getReductionAmountSub()).orElse(StringUtils.EMPTY));
                            info.add(Optional.ofNullable(orderInfoExport.getOtherAmount()).orElse(StringUtils.EMPTY));
                            info.add(Optional.ofNullable(orderInfoExport.getReductionAmount()).orElse(StringUtils.EMPTY));
                            info.add(Optional.ofNullable(orderInfoExport.getCouponDeductAmount()).orElse(StringUtils.EMPTY));
                            info.add(Optional.ofNullable(orderInfoExport.getTotalAmount()).orElse(StringUtils.EMPTY));
                            info.add(Optional.ofNullable(orderInfoExport.getFixAmount()).orElse(StringUtils.EMPTY));
                            info.add(Optional.ofNullable(orderInfoExport.getAllTotalAmount()).orElse(StringUtils.EMPTY));
                            info.add(Optional.ofNullable(orderInfoExport.getPayTypeName()).orElse(StringUtils.EMPTY));
                            info.add(Optional.ofNullable(orderInfoExport.getActivityName()).orElse(StringUtils.EMPTY));
                            info.add(Optional.ofNullable(orderInfoExport.getPriceExplain()).orElse(StringUtils.EMPTY));
                            info.add(Optional.ofNullable(orderInfoExport.getOrderDetail()).orElse(StringUtils.EMPTY));
                            excelData.add(info);
                        }
                    }

                }
            }
            List<CellRangeAddress> merge = new ArrayList<>();
            for (int index = 0; index < excelData.size(); index ++){
                String orderApplyNo = (String) excelData.get(index).get(0);
                int end = index;
                while (++end < excelData.size()){
                    String nextOrderApplyNo = (String) excelData.get(end).get(0);
                    if (!orderApplyNo.equals(nextOrderApplyNo)){
                        break;
                    }
                }
                end = end - 1;
                if (end > index){
                    //需要合并
                    if (type == 1){
                        for (int col : type1Merge){
                            CellRangeAddress mergeAddress = new CellRangeAddress(index + 2, end + 2, col, col);
                            merge.add(mergeAddress);
                        }
                    }else {
                        for (int col : type2Merge){
                            CellRangeAddress mergeAddress = new CellRangeAddress(index + 2, end + 2, col, col);
                            merge.add(mergeAddress);
                        }
                    }
                }
                index = end;

            }

            if (type == 1){
                for (int index = 0; index < excelData.size(); index ++){
                    String orderNo = (String) excelData.get(index).get(8);
                    int end = index;
                    while (++end < excelData.size()){
                        String nextOrderNo = (String) excelData.get(end).get(8);
                        if (StringUtils.isBlank(orderNo) || !orderNo.equals(nextOrderNo)){
                            break;
                        }
                    }
                    end = end - 1;
                    if (end > index){
                        //需要合并
                        for (int col : orderNoMerge1){
                            CellRangeAddress mergeAddress = new CellRangeAddress(index + 2, end + 2, col, col);
                            merge.add(mergeAddress);
                        }
                    }
                    index = end;
                }
            }

            WriteCellStyle headWriteCellStyle = new WriteCellStyle();
            //设置背景颜色
            headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            //设置头字体
            WriteFont headWriteFont = new WriteFont();
            headWriteFont.setFontHeightInPoints((short)14);
            headWriteFont.setBold(true);
            headWriteCellStyle.setWriteFont(headWriteFont);
            //设置头居中
            headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

            //内容策略
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            //设置 水平居中
            contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            contentWriteCellStyle.setWrapped(true);
            write(outputStream).head(head)
                    .excelType(ExcelTypeEnum.XLS)
                    //.registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .registerWriteHandler(new ExcelWidthStyleStrategy()) // 设置宽度
                    .registerWriteHandler(new HorizontalCellStyleStrategy(headWriteCellStyle,contentWriteCellStyle))
                    .registerWriteHandler(new AbstractMergeStrategy() {
                        @Override
                        protected void merge(Sheet sheet, Cell cell, Head head, Integer rowNum) {
                            if (CollectionUtils.isNotEmpty(merge)) {
                                if (cell.getRowIndex() == 2 && cell.getColumnIndex() == 0) {
                                    for (CellRangeAddress item : merge) {
                                        sheet.addMergedRegionUnsafe(item);
                                    }
                                }
                            }
                        }
                    }).sheet(fileName).doWrite(excelData);
        } catch (Exception e) {
            logger.error("导出Excel异常", e);
        }
    }

    private void generateSubHead(List<List<String>> head, int type, boolean isCustomer, Boolean onlySelf, Byte orderType){
        String orderInfoHead = "子行程信息";

        List<String> orderNo = new ArrayList<>(2);
        orderNo.add(orderInfoHead);
        orderNo.add("子行程编号");
        head.add(orderNo);

        List<String> cancelUserName = new ArrayList<>(2);
        cancelUserName.add(orderInfoHead);
        cancelUserName.add("订单取消操作人");
        head.add(cancelUserName);

        List<String> bookingPassengerUserName = new ArrayList<>(2);
        bookingPassengerUserName.add(orderInfoHead);
        bookingPassengerUserName.add("乘车人姓名");
        head.add(bookingPassengerUserName);

        List<String> bookingPassengerUserPhone = new ArrayList<>(2);
        bookingPassengerUserPhone.add(orderInfoHead);
        bookingPassengerUserPhone.add("乘车人手机号");
        head.add(bookingPassengerUserPhone);

        if (isCustomer){
            List<String> departmentName = new ArrayList<>(2);
            departmentName.add(orderInfoHead);
            departmentName.add("乘车人部门");
            head.add(departmentName);
        }else {
            List<String> customerCompanyName = new ArrayList<>(2);
            customerCompanyName.add(orderInfoHead);
            customerCompanyName.add("乘车人企业名称");
            head.add(customerCompanyName);
        }

        if (type == 1){
            //行程列表新增 联系人信息
            List<String> contactName = new ArrayList<>(2);
            contactName.add(orderInfoHead);
            contactName.add("联系人姓名");
            head.add(contactName);

            List<String> contactMobile = new ArrayList<>(2);
            contactMobile.add(orderInfoHead);
            contactMobile.add("联系人手机号");
            head.add(contactMobile);

        }

        List<String> bookingOrderStime = new ArrayList<>(2);
        bookingOrderStime.add(orderInfoHead);
        bookingOrderStime.add("用车开始时间");
        head.add(bookingOrderStime);

        List<String> factStartDate = new ArrayList<>(2);
        factStartDate.add(orderInfoHead);
        factStartDate.add("实际开始时间");
        head.add(factStartDate);

        List<String> bookingOrderEtime = new ArrayList<>(2);
        bookingOrderEtime.add(orderInfoHead);
        bookingOrderEtime.add("用车结束时间");
        head.add(bookingOrderEtime);

        List<String> factEndDate = new ArrayList<>(2);
        factEndDate.add(orderInfoHead);
        factEndDate.add("实际结束时间");
        head.add(factEndDate);

        List<String> startCityName = new ArrayList<>(2);
        startCityName.add(orderInfoHead);
        startCityName.add("出发城市");
        head.add(startCityName);

        List<String> bookingStartShortAddr = new ArrayList<>(2);
        bookingStartShortAddr.add(orderInfoHead);
        bookingStartShortAddr.add("出发地");
        head.add(bookingStartShortAddr);

        List<String> endCityName = new ArrayList<>(2);
        endCityName.add(orderInfoHead);
        endCityName.add("目的城市");
        head.add(endCityName);

        List<String> bookingEndShortAddr = new ArrayList<>(2);
        bookingEndShortAddr.add(orderInfoHead);
        bookingEndShortAddr.add("目的地");
        head.add(bookingEndShortAddr);

        List<String> assignCarLevelName = new ArrayList<>(2);
        assignCarLevelName.add(orderInfoHead);
        assignCarLevelName.add("车辆级别");
        head.add(assignCarLevelName);

        List<String> assignCarModelName = new ArrayList<>(2);
        assignCarModelName.add(orderInfoHead);
        assignCarModelName.add("车系");
        head.add(assignCarModelName);

        List<String> vehicleBrand = new ArrayList<>(2);
        vehicleBrand.add(orderInfoHead);
        vehicleBrand.add("车辆品牌");
        head.add(vehicleBrand);

        List<String> assignCarLicense = new ArrayList<>(2);
        assignCarLicense.add(orderInfoHead);
        assignCarLicense.add("车牌号");
        head.add(assignCarLicense);

        List<String> assignDriverName = new ArrayList<>(2);
        assignDriverName.add(orderInfoHead);
        assignDriverName.add("司机");
        head.add(assignDriverName);

        List<String> assignDriverPhone = new ArrayList<>(2);
        assignDriverPhone.add(orderInfoHead);
        assignDriverPhone.add("司机手机号");
        head.add(assignDriverPhone);

        List<String> orderStatusName = new ArrayList<>(2);
        orderStatusName.add(orderInfoHead);
        orderStatusName.add("行程状态");
        head.add(orderStatusName);

        List<String> totalDays = new ArrayList<>(2);
        totalDays.add(orderInfoHead);
        totalDays.add("用车天数");
        head.add(totalDays);

        List<String> totalMileage = new ArrayList<>(2);
        totalMileage.add(orderInfoHead);
        totalMileage.add("行驶里程");
        head.add(totalMileage);

        if (!onlySelf) {
            List<String> startManualMileage = new ArrayList<>(2);
            startManualMileage.add(orderInfoHead);
            startManualMileage.add("服务前里程数");
            head.add(startManualMileage);

            List<String> endManualMileage = new ArrayList<>(2);
            endManualMileage.add(orderInfoHead);
            endManualMileage.add("服务后里程数");
            head.add(endManualMileage);
        }

        List<String> totalTime = new ArrayList<>(2);
        totalTime.add(orderInfoHead);
        totalTime.add("行驶时长(小时)");
        head.add(totalTime);

        List<String> baseFee = new ArrayList<>(2);
        baseFee.add(orderInfoHead);
        baseFee.add("基础费用");
        head.add(baseFee);

        List<String> totalMileageFee = new ArrayList<>(2);
        totalMileageFee.add(orderInfoHead);
        totalMileageFee.add("总里程费");
        head.add(totalMileageFee);

        List<String> totalTimeFee = new ArrayList<>(2);
        totalTimeFee.add(orderInfoHead);
        totalTimeFee.add("总时长费用");
        head.add(totalTimeFee);

        List<String> feeAmount = new ArrayList<>(2);
        feeAmount.add(orderInfoHead);
        feeAmount.add("路桥费");
        head.add(feeAmount);

        List<String> feeAmount2 = new ArrayList<>(2);
        feeAmount2.add(orderInfoHead);
        feeAmount2.add("停车费");
        head.add(feeAmount2);

        List<String> feeAmount3 = new ArrayList<>(2);
        feeAmount3.add(orderInfoHead);
        feeAmount3.add("住宿费");
        head.add(feeAmount3);


        List<String> feeAmount4 = new ArrayList<>(2);
        feeAmount4.add(orderInfoHead);
        feeAmount4.add("餐饮费");
        head.add(feeAmount4);

        List<String> returnEmptyFee = new ArrayList<>(2);
        returnEmptyFee.add(orderInfoHead);
        returnEmptyFee.add("返空里程费");
        head.add(returnEmptyFee);


        List<String> feeAmount5 = new ArrayList<>(2);
        feeAmount5.add(orderInfoHead);
        feeAmount5.add("其他费");
        head.add(feeAmount5);

        List<String> feeAmountRemark = new ArrayList<>(2);
        feeAmountRemark.add(orderInfoHead);
        feeAmountRemark.add("费用备注");
        head.add(feeAmountRemark);

        List<String> otherAmountSub = new ArrayList<>(2);
        otherAmountSub.add(orderInfoHead);
        otherAmountSub.add("子行程其他费用（元）");
        head.add(otherAmountSub);

        List<String> reductionAmountSub = new ArrayList<>(2);
        reductionAmountSub.add(orderInfoHead);
        reductionAmountSub.add("子行程减免金额（元）");
        head.add(reductionAmountSub);

    }

    private Set<String> getDataScopeList(ProviderLoginInfo providerLoginInfo){
        Set<String> res = null;
        if(ProviderDataPermTypeEnum.ALL.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())){
            return null;
        }else if(ProviderDataPermTypeEnum.RESPONSIBLE_CONTRACT.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())){
            res = providerLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.VEHICLE).getDataCodeSet();
        }else if(ProviderDataPermTypeEnum.RESPONSIBLE_CUSTOMER.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())||
                ProviderDataPermTypeEnum.ASSIGN_CUSTOMER.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())){
            res = providerLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.ID).getDataCodeSet();
        }else if(ProviderDataPermTypeEnum.SELF_DEPT.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())||
                ProviderDataPermTypeEnum.ASSIGN_DEPT.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())){
            res = providerLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet();
        }
        return res;
    }


    @PostMapping(MrcarOrderRestMsgCenter.PROVIDER_APPLY_ORDER_CANCEL)
    @ApiOperation(value = "商务车订单申请取消")
    public RestResponse motorcadeOrderApplyCancel(@RequestBody JSONObject restObject) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        if (providerLoginInfo == null) {
            return RestResponse.fail(Constant.CUSTOMER_NOT_LOGIN);
        }
        String restUrl = new MrcarOrderRestLocator().getRestUrl("/order/motorcadeOrderApplyCancel");
        //运营端的取消接口应该是1，首汽的员工取消订单不校验下单人或者时间等
        restObject.put("cancelSourceType", "1");
        restObject.put("cancelUserId", providerLoginInfo.obtainBaseInfo().getStaffId());
        restObject.put("cancelUserName", providerLoginInfo.obtainBaseInfo().getStaffName());
        try {
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, restObject.getInnerMap(), null, Boolean.class);
        } catch (Exception e) {
            return RestResponse.fail(Constant.SYS_ERROR);
        }
    }

    /**查询客户列表**/
    @PostMapping(UserUrlCenter.GET_CUSTOMER_LIST_BY_NAME)
    @ApiOperation("查询客户列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name="companyName",value="客户名称",example = "天津广播电视台",required=true),
    })
    public RestResponse<CrmCompanyDTO> getCustomerList(String companyName){
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.GET_CUSTOMER_LIST_BY_NAME);
        Map<String, Object> params = new HashMap<>();
        params.put("companyName", companyName);
        RestResponse restResponse = RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, params, null);
        return restResponse;
    }
    /**保存客户**/
    @PostMapping(UserUrlCenter.SAVE_CUSTOMER)
    @ApiOperation("保存客户")
    @ApiImplicitParams({
            @ApiImplicitParam(name="companyCode",value="客户编码",example = "xxxxxxx",required=true),
    })
    public RestResponse saveCustomer(String companyCode){
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.SAVE_CUSTOMER);
        Map<String, Object> params = new HashMap<>();
        params.put("companyCode", companyCode);
        RestResponse restResponse = RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, params, null);
        return restResponse;
    }

    @RequestMapping("/publicpond/list")
    @ApiOperation("公海基础数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name="areaName",value="公海名称",example = "xxxxxxx",required=false),
    })
    public RestResponse<AreaDTO> queryList(String areaName){
        String url = new CarAssetRestLocator().getRestUrl(CarAssetRestUrl.AREA_GETAREALIST);
        Map<String, Object> httpParams = new HashMap<>();
        httpParams.put("areaType", 2);
        httpParams.put("areaName", areaName);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POST, url, httpParams, null, AreaDTO.class);
    }


}
