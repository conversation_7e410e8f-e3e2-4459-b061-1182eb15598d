package com.izu.mrcar.providerController.order.internalCar;

import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.constants.Constant;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.dto.provider.input.OrderApplyOpeInputDTO;
import com.izu.mrcar.order.dto.provider.output.OrderApplyOpeOutputDTO;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.enums.perm.ProviderDataPermTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

@Api(tags = "供应商企业用车")
@RestController
public class OrderInfoApplySupplierProviderController {
    @PostMapping("/orderApplyProvider/supplier/getList")
    @ApiOperation(value = "行程列表", notes = "作者：mapp")
    @RequestFunction(functionName = "行程列表")
    public RestResponse<PageDTO<OrderApplyOpeOutputDTO>> getList(@RequestBody OrderApplyOpeInputDTO queryDTO) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        if (providerLoginInfo == null) {
            return RestResponse.fail(Constant.CUSTOMER_NOT_LOGIN);
        }
        queryDTO.setStaffId(providerLoginInfo.getBaseInfo().getStaffId());
        queryDTO.setStaffCode(providerLoginInfo.getBaseInfo().getStaffCode());
        queryDTO.setDataPermType(providerLoginInfo.obtainDataPerm().getDataPermType());
        queryDTO.setDataPermCodeSet(getDataScopeList(providerLoginInfo));
        queryDTO.setLoginCompanyId(providerLoginInfo.getProviderCompany().getProviderId());
        queryDTO.setLoginId(providerLoginInfo.getBaseInfo().getStaffId());
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.ORDER_APPLY_PROVIDER_LIST);
        final HashMap<String, Object> restParam = new HashMap<String, Object>() {{
            put(BaseHttpClient.POSTBODY_MAP_KEY, queryDTO);
        }};
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null, OrderApplyOpeOutputDTO.class);
    }

    private Set<String> getDataScopeList(ProviderLoginInfo providerLoginInfo){
        Set<String> res = null;
        if(ProviderDataPermTypeEnum.ALL.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())){
            return Collections.emptySet();
        }else if(ProviderDataPermTypeEnum.RESPONSIBLE_CONTRACT.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())){
            res = providerLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.VEHICLE).getDataCodeSet();
        }else if(ProviderDataPermTypeEnum.RESPONSIBLE_CUSTOMER.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())||
                ProviderDataPermTypeEnum.ASSIGN_CUSTOMER.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())){
            res = providerLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.ID).getDataCodeSet();
        }else if(ProviderDataPermTypeEnum.SELF_DEPT.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())||
                ProviderDataPermTypeEnum.ASSIGN_DEPT.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())){
            res = providerLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet();
        }
        return res;
    }

    @PostMapping("/provider/OrderApply/supplier/exportOrderApply")
    @RequestFunction(functionName = "行程列表导出")
    @ApiOperation(value = "行程列表导出", notes = "作者：hhd")
    public void exportOrderApply(@RequestBody OrderApplyOpeInputDTO queryDTO,
                                 HttpServletResponse response) {
    }


    @PostMapping("/provider/OrderApply/supplier/exportOrderApplyToMail")
    @RequestFunction(functionName = "行程列表导出到邮箱")
    @ApiOperation(value = "行程列表导出到邮箱", notes = "作者：hhd")
    public RestResponse exportOrderApplyToMail(@RequestBody OrderApplyOpeInputDTO queryDTO) {
       return RestResponse.success(null);
    }
}
