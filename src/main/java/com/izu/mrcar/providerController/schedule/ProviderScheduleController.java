package com.izu.mrcar.providerController.schedule;

import com.izu.dispatch.ScheduleDTO;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.dto.dispatching.ScheduleInfoDTO;
import com.izu.mrcar.order.dto.provider.input.ScheduleSimpleReqDTO;
import com.izu.mrcar.order.dto.provider.output.ScheduleDetailDTO;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 调度
 * @date 2024/4/8 20:50
 */
@RestController
@Api(tags = "调度接口")
public class ProviderScheduleController {

    @PostMapping(MrcarOrderRestMsgCenter.GET_SCHEDULE_DETAIL_V2)
    @ApiOperation("调度详情接口")
    public RestResponse<ScheduleDetailDTO> getScheduleOrder(@RequestBody ScheduleSimpleReqDTO scheduleDTO) {
        final String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.GET_SCHEDULE_DETAIL);
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        Map<String, Object> paraMap = new HashMap<>();
        paraMap.put("scheduleId", scheduleDTO.getScheduleId());
        paraMap.put("subScheduleId", scheduleDTO.getSubScheduleId());
        paraMap.put("orderApplyNo", scheduleDTO.getOrderApplyNo());
        paraMap.put("loginCompanyId",loginBaseInfo.obtainBelongCompanyId());
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, ScheduleDetailDTO.class);
    }
}
