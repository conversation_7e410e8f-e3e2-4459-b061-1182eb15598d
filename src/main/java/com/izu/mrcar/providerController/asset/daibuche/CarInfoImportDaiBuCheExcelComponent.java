package com.izu.mrcar.providerController.asset.daibuche;

import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.consts.ModelSourceEnum;
import com.izu.asset.consts.VehicleColorEnum;
import com.izu.asset.dto.CarBrandDTO;
import com.izu.asset.dto.CarModelDTO;
import com.izu.asset.dto.vehicle.CoSupplierCarBatchImportDTO;
import com.izu.asset.dto.vehicle.SupplierCarImportDTO;
import com.izu.asset.errcode.MrCarAssetErrorCode;
import com.izu.config.MrCarConfigRestLocator;
import com.izu.config.dto.CityDicDTO;
import com.izu.consts.ConfigURI;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.controller.AbstractExcelUploadController;
import com.izu.mrcar.iot.dto.BatchDTO;
import com.izu.mrcar.service.asset.CarModelService;
import com.izu.mrcar.utils.DateUtil;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.config.errcode.UserErrorCode;
import com.izu.user.dto.company.CompanyNameRespDTO;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* @Description: 零散用车运营端车辆导入
* @author: hxc
* @Date: 2023/2/6
**/
@Slf4j
public class CarInfoImportDaiBuCheExcelComponent extends AbstractExcelUploadController<SupplierCarImportDTO, AbstractExcelUploadController.ExcelUploadResult> {

    private  static final String COMMA = "；";

    private final MrCarAssetRestLocator mrCarAssetRestLocator = new MrCarAssetRestLocator();

    private static UserRestLocator userRestLocator = new UserRestLocator();

    private CoSupplierCarBatchImportDTO batchImportDTO;
    private CarModelService carModelService;
    /**
     * 是否是客户端操作，这里决定如何获取供应商信息
     */
    private boolean isClient;

    public CarInfoImportDaiBuCheExcelComponent(CoSupplierCarBatchImportDTO batchImportDTO,
                                               CarModelService carModelService,
                                               boolean isClient) {
        this.batchImportDTO = batchImportDTO;
        this.carModelService = carModelService;
        this.isClient = isClient;
    }

    public RestResponse dealExcel(HttpServletRequest request,
                                  HttpServletResponse response) {
        if (!isClient) {
            if (Objects.isNull(this.batchImportDTO.getProviderId()) || this.batchImportDTO.getProviderId() < 0) {
                return RestResponse.fail(UserErrorCode.COMMON_ERROR_MESSAGE, "请输入对应的供应商编码");
            }
            long start = System.currentTimeMillis();
            final List<CompanyNameRespDTO> supplierDic = getSupplierDic();
            log.info("getSupplierDic cost:{}", System.currentTimeMillis() - start);
            final Map<Integer, CompanyNameRespDTO> supplierMap = supplierDic.stream().collect(Collectors.toMap(CompanyNameRespDTO::getCompanyId, Function.identity(), (e, r) -> e));
            final CompanyNameRespDTO supplierSimpleRespDTO = supplierMap.get(batchImportDTO.getProviderId());
            if (Objects.isNull(supplierSimpleRespDTO)) {
                return RestResponse.fail(UserErrorCode.COMMON_ERROR_MESSAGE, "对应的供应商不存在");
            }
            request.setAttribute("providerName", supplierSimpleRespDTO.getCompanyName());
            request.setAttribute("providerId", supplierSimpleRespDTO.getCompanyId());
        }
        ExcelUploadResult eur = new ExcelUploadResult();
        return this.start(batchImportDTO.getExcelUrl(), request, response, eur,11);
    }

    @Override
    protected List<Integer> getResolveSheetIndexes() {
        return Arrays.asList(1);
    }

    @Override
    protected String getColumnMappingConfigFile(HttpServletRequest request, HttpServletResponse response) {
        return null;
    }

    @Override
    protected List<SupplierCarImportDTO> convertStringDataToObject(List<Map<Integer, String>> rowDatas, ExcelUploadResult excelUploadResult) {

        String providerName = "";
        Integer providerId = null;
        AccountBaseInfo baseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
        if (isClient) {
            ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
            providerId = clientLoginInfo.getClientCompany().getCompanyId();
            providerName = clientLoginInfo.getClientCompany().getCompanyName();
        } else {
//            List<CompanyNameRespDTO> supplierDic = getSupplierDic();
//            final Map<Integer, CompanyNameRespDTO> supplierMap = supplierDic.stream().collect(Collectors.toMap(CompanyNameRespDTO::getCompanyId, Function.identity(), (e, r) -> e));
//            final CompanyNameRespDTO supplierSimpleRespDTO = supplierMap.get(batchImportDTO.getProviderId());
//            if (Objects.nonNull(supplierSimpleRespDTO)) {
//                providerName = supplierSimpleRespDTO.getCompanyName();
//                providerId = supplierSimpleRespDTO.getCompanyId();
//            }
            HttpServletRequest request = getRequest();
            providerName = (String) request.getAttribute("providerName");
            providerId = (Integer) request.getAttribute("providerId");
            log.info("providerName:{}, providerId:{}", providerName, providerId);
        }

        List<Map<Integer, String>> collect = rowDatas.stream().filter(row -> !checkFieldAllNull(row)).collect(Collectors.toList());
        long start = System.currentTimeMillis();
        List<CarBrandDTO> carBrandDTOS = carModelService.getCarBrandDicV2();
        log.info("getCarBrandDic cost:{}", System.currentTimeMillis() - start);
        start = System.currentTimeMillis();
        List<CarModelDTO> carModelDTOS = carModelService.getCarModelDicV2();
        log.info("getCarModelDic cost:{}", System.currentTimeMillis() - start);
        start = System.currentTimeMillis();
        List<CityDicDTO> cityDicDTOList = carModelService.getCityDic();
        log.info("getCityDic cost:{}", System.currentTimeMillis() - start);
        List<SupplierCarImportDTO> supplierCarImportDTOList = new ArrayList<>();
        for (Map<Integer, String> row : collect) {
            SupplierCarImportDTO dto = new SupplierCarImportDTO();
            dto.setCompanyName(providerName);
            dto.setCompanyId(providerId);
            dto.setVehicleLicense(row.get(0));
            dto.setVehicleVin(row.get(1));
            dto.setEngineNum(row.get(2));
            dto.setVehicleBrand(row.get(3));
            dto.setVehicleModel(row.get(4));
            String belongCityName = row.get(5);
            if(StringUtils.isNotBlank(belongCityName)){
                dto.setBelongCityName(belongCityName);
                CityDicDTO cityDicDTO = cityDicDTOList.stream().filter(o->o.getCityName().equals(belongCityName)).findFirst().orElse(null);
                if(cityDicDTO!=null){
                    dto.setBelongCityCode(cityDicDTO.getCityCode().toString());
                }
            }
            if(StringUtils.isNotEmpty(row.get(6))){
                dto.setRegisterDate(DateUtil.formatExcelStr(row.get(6)));
            }else{
                dto.setRegisterDate(row.get(6));
            }
            if(StringUtils.isNotEmpty(row.get(7))){
                dto.setLastInspectDate(DateUtil.formatExcelStr(row.get(7)));
            }else{
                dto.setLastInspectDate(row.get(7));
            }
            dto.setLoginUserId(baseInfo.getStaffId());
            dto.setLoginUserName(baseInfo.getStaffName());
            String vehicleModel = row.get(4);
            CarModelDTO carModelDTO = null;
            if(StringUtils.isNotBlank(vehicleModel)){
                dto.setVehicleModel(vehicleModel);
                carModelDTO = carModelDTOS.stream().filter(o->o.getModelName().equals(vehicleModel)).findFirst().orElse(null);
                if(carModelDTO!= null) {
                    dto.setVehicleModelCode(carModelDTO.getModelCode());
                    dto.setVehicleModel(carModelDTO.getModelName());
                }
            }
            String vehicleBrand = row.get(3);
            if(StringUtils.isNotBlank(vehicleBrand)){
                dto.setVehicleBrand(vehicleBrand);
                CarBrandDTO carBrandDTO = null;
                if(carModelDTO != null){
                    CarModelDTO finalCarModelDTO = carModelDTO;
                    carBrandDTO = carBrandDTOS.stream().filter(o -> o.getBrandName().equals(vehicleBrand)
                            && o.getBrandId().equals(finalCarModelDTO.getBrandId())).findFirst().orElse(null);
                }
                if(Objects.isNull(carBrandDTO)){
                    carBrandDTO = carBrandDTOS.stream().filter(o->o.getBrandName().equals(vehicleBrand)).findFirst().orElse(null);
                }
                if(carBrandDTO !=null) {
                    dto.setVehicleBrandCode(carBrandDTO.getBrandCode());
                    dto.setVehicleBrand(carBrandDTO.getBrandName());
                }
            }

            supplierCarImportDTOList.add(dto);
        }
        return supplierCarImportDTOList;
    }

    @Override
    protected List<ExcelUploadRowError> checkExcelData(List<SupplierCarImportDTO> rowDatas, HttpServletRequest request, HttpServletResponse response, ExcelUploadResult excelUploadResult) {
        // 参数校验放到assert服务中
        return null;
    }

    @Override
    protected boolean beforePersist(List<SupplierCarImportDTO> rowDatas, HttpServletRequest request, HttpServletResponse response, ExcelUploadResult excelUploadResult) {
        return true;
    }

    @Override
    protected RestResponse executePersist(List<SupplierCarImportDTO> rowDatas, HttpServletRequest request, HttpServletResponse response, ExcelUploadResult excelUploadResult) {
        batchImportDTO.setCarList(rowDatas);
        Map<String, Object> paramMap = new HashMap<>(4);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, batchImportDTO);
        String restUrl = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CO_PROVIDER_CAR_BATCH_INSERT);
        long start = System.currentTimeMillis();
        RestResponse restResponse = RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, BatchDTO.class);
        log.info("batchImport cost:{}", System.currentTimeMillis() - start);
        return restResponse;
    }

    private boolean checkFieldAllNull(Object obj) {
        Field[] declaredFields = obj.getClass().getDeclaredFields();
        boolean flag = true;
        if(obj instanceof Map){
            Map<Integer,String> map = (Map) obj;
            for(Map.Entry entry : map.entrySet()){
                if (!ObjectUtils.isEmpty(entry.getValue())) {
                    flag = false;
                    break;
                }
            }
            return flag;
        }
        for (Field field : declaredFields) {
            field.setAccessible(true);
            Object o = null;
            try {
                o = field.get(obj);
            } catch (IllegalAccessException e) {
                log.error("校验对象属性值异常");
            }
            if (!ObjectUtils.isEmpty(o)) {
                flag = false;
                break;
            }
        }
        return flag;
    }

    private List<CarBrandDTO> getCarBrandDic(){
        List<CarBrandDTO> brandList = new ArrayList<>();
        Map<String, Object> paraMap = new HashMap<>();
        paraMap.put("brandSource", ModelSourceEnum.COMPANY_SELF.getCode());
        paraMap.put("valid", (byte)1);
        RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, mrCarAssetRestLocator.getRestUrl("/brand/getCarBrandList"), paraMap, null, CarBrandDTO.class);
        if (restResponse.isSuccess()) {
            brandList = (List<CarBrandDTO>) restResponse.getData();
        }
        return brandList;
    }

    private List<CarModelDTO> getCarModelDic(){
        List<CarModelDTO> listCarModel = new ArrayList<>();
        Map<String, Object> paraMap = new HashMap<>();
        paraMap.put("modeSource", ModelSourceEnum.COMPANY_SELF.getCode());
        paraMap.put("status", (byte)1);
        RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, mrCarAssetRestLocator.getRestUrl("/dic/getModelListByParam"), paraMap, null, CarModelDTO.class);
        if (restResponse.isSuccess()) {
            listCarModel = (List<CarModelDTO>) restResponse.getData();
        }
        return listCarModel;
    }

    private List<CompanyNameRespDTO> getSupplierDic(){
        List<CompanyNameRespDTO> supplierSimpleRespDTOList = new ArrayList<>();
        Map<String, Object> paraMap = new HashMap<>();
        paraMap.put("pageSize",1000);
        paraMap.put("companyStatus",2);
        paraMap.put("businessType","21,22");
        paraMap.put("companyAttribute",3);
        RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, userRestLocator.getRestUrl(UserUrlCenter.PROVIDER_COMPANY_SELECT_BY_BUSINESS_TYPE), paraMap, null, CompanyNameRespDTO.class);
        if (restResponse.isSuccess()) {
            supplierSimpleRespDTOList = (List<CompanyNameRespDTO>) restResponse.getData();
        }
        return supplierSimpleRespDTOList;
    }

    private Map<Integer, List<String>> buildSelectMap(){
        Map<Integer, List<String>> selectMap = new HashMap<>();
        List<CarBrandDTO> carBrandDTOS = carModelService.getCarBrandDicV2();
        List<String> brandList = carBrandDTOS.stream().map(CarBrandDTO::getBrandName).collect(Collectors.toList());
        selectMap.put(2, brandList);

        List<CarModelDTO> carModelDTOS = carModelService.getCarModelDicV2();
        List<String> modelList = carModelDTOS.stream().map(CarModelDTO::getModelName).collect(Collectors.toList());
        selectMap.put(3, modelList);

        List<String> brandForModelList = new ArrayList<>();
        for (CarModelDTO carModelDTO : carModelDTOS) {
            CarBrandDTO brandDTO = carBrandDTOS.stream().filter(o->o.getBrandId().equals(carModelDTO.getBrandId())).findFirst().orElse(null);
            if(brandDTO != null){
                brandForModelList.add(brandDTO.getBrandName());
            }else{
                brandForModelList.add("");
            }
        }
        selectMap.put(11, brandForModelList);
        selectMap.put(12, modelList);

        List<String> vehicleColorList = new ArrayList<>();
        for (VehicleColorEnum vehicleColorEnum : VehicleColorEnum.values()) {
            vehicleColorList.add(vehicleColorEnum.getValue());
        }
        selectMap.put(4, vehicleColorList);

        RestResponse cityResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, new MrCarConfigRestLocator().getRestUrl(ConfigURI.DIC_GET_CITY_DIC_LIST), null, null, CityDicDTO.class);
        if(cityResponse!=null && cityResponse.isSuccess()){
            List<CityDicDTO> cityDicDTOS = (List<CityDicDTO>) cityResponse.getData();
            if(CollectionUtils.isNotEmpty(cityDicDTOS)){
                List<String> cityList = cityDicDTOS.stream().map(CityDicDTO::getCityName).collect(Collectors.toList());
                selectMap.put(8,cityList);
            }
        }
        return selectMap;
    }
}
