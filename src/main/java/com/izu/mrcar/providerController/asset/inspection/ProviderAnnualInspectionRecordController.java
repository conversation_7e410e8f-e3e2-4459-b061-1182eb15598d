package com.izu.mrcar.providerController.asset.inspection;

import cn.hutool.core.bean.BeanUtil;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.inspection.req.AnnualInspectionRecordAddReq;
import com.izu.asset.dto.inspection.req.AnnualInspectionRecordDetailReq;
import com.izu.asset.dto.inspection.req.ListAnnualInspectionRecordReq;
import com.izu.asset.dto.inspection.resp.AnnualInspectionRecordDTO;
import com.izu.asset.dto.inspection.resp.AnnualInspectionRecordDetailResp;
import com.izu.asset.dto.inspection.resp.AnnualInspectionRecordExportDTO;
import com.izu.asset.dto.insurance.req.InsurancePolicyEditReqDTO;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.service.asset.inspection.AnnualInspectionRecordService;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.asset.AssetUtil;
import com.izu.mrcar.utils.perm.VehicleListDataPermUtil;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.enums.perm.ClientDataPermTypeEnum;
import com.izu.user.enums.perm.ProviderDataPermTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/4/21 18:15
 */
@Slf4j
@RestController
@Api(tags = "年检记录")
@RequestMapping("/provider/annualInspection")
public class ProviderAnnualInspectionRecordController {
    private final MrCarAssetRestLocator mrCarAssetRestLocator = new MrCarAssetRestLocator();

    @Resource
    private AnnualInspectionRecordService annualInspectionRecordService;


    @PostMapping("/annualInspectionRecordDetail")
    @ApiOperation(value = "年检记录明细")
    public RestResponse<AnnualInspectionRecordDetailResp> annualInspectionRecordDetail(@RequestBody AnnualInspectionRecordDetailReq reqDto) {

        AssetUtil.setProviderLoginInfo(reqDto);
        String restUrl = mrCarAssetRestLocator.getRestUrl("/annualInspection/annualInspectionRecordDetail");
        Map<String, Object> paraMap = new HashMap<>(1);
        paraMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDto);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paraMap, null, AnnualInspectionRecordDetailResp.class);
    }

    @PostMapping("/listAnnualInspectionRecord")
    @ApiOperation(value = "年检记录列表")
    public RestResponse<PageDTO<AnnualInspectionRecordDTO>> listAnnualInspectionRecord(@RequestBody ListAnnualInspectionRecordReq reqDto) {

        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        if(!providerLoginInfo.obtainDataPerm().getDataPermType().equals(ProviderDataPermTypeEnum.ALL.getType())){
            return null;
        }
        String restUrl = mrCarAssetRestLocator.getRestUrl("/annualInspection/listAnnualInspectionRecord");
        Map<String, Object> paraMap = new HashMap<>(1);
        paraMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDto);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paraMap, null, AnnualInspectionRecordDTO.class);
    }


    @PostMapping("/exportAnnualInspectionRecord")
    @ApiOperation(value = "年检记录导出")
    @ExportExcelWeb(fileName = "年检记录.xlsx", filePath = "/data/logs/mrcar/tmp", sheet = "年检记录",
            c = AnnualInspectionRecordExportDTO.class, isAsync = true)
    public PageDTO exportAnnualInspectionRecord(@RequestBody ListAnnualInspectionRecordReq reqDto, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response) {

        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        if(!providerLoginInfo.obtainDataPerm().getDataPermType().equals(ProviderDataPermTypeEnum.ALL.getType())){
            return new PageDTO(reqDto.getPage(), reqDto.getPageSize(), 0L, null);
        }
        reqDto.setPage(izuEasyExcelSession.getPageNo());
        reqDto.setPageSize(500);
        String restUrl = mrCarAssetRestLocator.getRestUrl("/annualInspection/listAnnualInspectionRecord");
        Map<String, Object> paraMap = new HashMap<>(1);
        paraMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDto);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paraMap, null, AnnualInspectionRecordDTO.class);
        if(!restResponse.isSuccess() || Objects.isNull(restResponse.getData())){
            return null;
        }
        PageDTO pageDTO = (PageDTO) restResponse.getData();
        if(pageDTO.getTotal()>10000){
            throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
        }
        List<AnnualInspectionRecordExportDTO> annualInspectionRecordExportDTOS = BeanUtil.copyToList(pageDTO.getResult(), AnnualInspectionRecordExportDTO.class);
        return new PageDTO(reqDto.getPage(), reqDto.getPageSize(), pageDTO.getTotal(), annualInspectionRecordExportDTOS);
    }
}
