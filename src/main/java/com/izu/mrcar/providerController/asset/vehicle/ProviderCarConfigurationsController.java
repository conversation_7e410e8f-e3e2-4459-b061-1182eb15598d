package com.izu.mrcar.providerController.asset.vehicle;

import com.google.common.collect.Maps;
import com.izu.asset.FuelGradeEnum;
import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.consts.TurboTypeEnum;
import com.izu.asset.dto.vehicle.CarConfigFuelTypeRespDTO;
import com.izu.asset.dto.vehicle.CarConfigQueryDTO;
import com.izu.asset.dto.vehicle.CarConfigRespDTO;
import com.izu.asset.dto.vehicle.resp.VehicleFuelInfoDTO;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.dto.DicKeyValueDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 运营端-车300相关接口
 * <AUTHOR>
 */
@RestController
@Slf4j
public class ProviderCarConfigurationsController {

    private final MrCarAssetRestLocator mrCarAssetRestLocator = new MrCarAssetRestLocator();

    /**
     * 根据mrcar配置款code 查询车300配置款数据
     * @param configurationCode
     * @return
     */
    @GetMapping(MrCarAssetRestCenter.PROVIDER_QUERY_CAR300_CONFIGURATION_DETAIL)
    public RestResponse<VehicleFuelInfoDTO> queryCar300ConfigurationInfoByCode(@RequestParam("configurationCode") String configurationCode) {
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.QUERY_CAR300_CONFIGURATION_DETAIL);
        Map<String, Object> paramMap = new HashMap<>(1);
        paramMap.put("configurationCode", configurationCode);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.GET, url, paramMap, null);
    }


    /**
     * 配置款下拉列表
     * @param queryDTO
     * @return
     */
    @RequestMapping(MrCarAssetRestCenter.PROVIDER_CAR_INFO_CONFIG_LIST)
    public RestResponse<List<CarConfigRespDTO>> carConfigList(@RequestBody CarConfigQueryDTO queryDTO) {
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CAR_INFO_CONFIG_LIST);
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, queryDTO);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null,CarConfigRespDTO.class);
    }


    /**
     * 排放标准下拉列表
     * @param
     * @return
     */
    @RequestMapping(MrCarAssetRestCenter.PROVIDER_CAR_INFO_CONFIG_DISCHARGE_STANDARD_LIST)
    public RestResponse<List<DicKeyValueDTO>> carConfigDischargeStandardList() {
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CAR_INFO_CONFIG_DISCHARGE_STANDARD_LIST);
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        RestResponse response = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, url, paramMap, null, String.class);
        List<DicKeyValueDTO> keyValueDTOList = new ArrayList<>();
        if (response != null && response.isSuccess()) {
            List<String> stringList = (List<String>) response.getData();
            stringList.forEach(r -> {
                if (StringUtils.isNotBlank(r)) {
                    DicKeyValueDTO dicKeyValueDTO = new DicKeyValueDTO(r, r);
                    keyValueDTOList.add(dicKeyValueDTO);
                }
            });
        }
        return RestResponse.success(keyValueDTOList);
    }


    /**
     * 动力类型下拉框
     * @param
     * @return
     */
    @RequestMapping(MrCarAssetRestCenter.PROVIDER_CAR_INFO_CONFIG_FUEL_TYPE_LIST)
    public RestResponse<List<DicKeyValueDTO>> carConfigFuelTypeList() {
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CAR_INFO_CONFIG_FUEL_TYPE_LIST);
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        RestResponse response = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, url, paramMap, null, CarConfigFuelTypeRespDTO.class);
        List<DicKeyValueDTO> keyValueDTOList = new ArrayList<>();
        if (response != null && response.isSuccess()) {
            List<CarConfigFuelTypeRespDTO> fuelTypeRespDTOList = (List<CarConfigFuelTypeRespDTO>) response.getData();
            fuelTypeRespDTOList.forEach(fuelType -> {
                if (Objects.nonNull(fuelType.getFuelTypeId())) {
                    DicKeyValueDTO dicKeyValueDTO = new DicKeyValueDTO(fuelType.getFuelTypeId(), fuelType.getFuelTypeName());
                    keyValueDTOList.add(dicKeyValueDTO);
                }
            });
        }
        return RestResponse.success(keyValueDTOList);
    }


    /**
     * 燃油标号下拉列表
     * @param
     * @return
     */
    @RequestMapping(MrCarAssetRestCenter.PROVIDER_CAR_INFO_CONFIG_FUEL_GRADE_LIST)
    public RestResponse<List<DicKeyValueDTO>> carConfigFuelGradeList() {
         List<DicKeyValueDTO> keyValueDTOList = Arrays.stream(FuelGradeEnum.values())
                .map(e -> new DicKeyValueDTO(e.getGradeValue(),e.getGradeValue()))
                .collect(Collectors.toList());
         return RestResponse.success(keyValueDTOList);

    }


    /**
     * 变速箱下拉列表
     * @param
     * @return
     */
    @RequestMapping(MrCarAssetRestCenter.PROVIDER_CAR_INFO_CONFIG_GEAR_LIST)
    public RestResponse<List<DicKeyValueDTO>> carConfigGearList() {
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CAR_INFO_CONFIG_GEAR_LIST);
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        RestResponse response = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, url, paramMap, null, String.class);
        List<DicKeyValueDTO> keyValueDTOList = new ArrayList<>();
        if (response != null && response.isSuccess()) {
            List<String> stringList = (List<String>) response.getData();
            stringList.forEach(r -> {
                if (StringUtils.isNotBlank(r)) {
                    DicKeyValueDTO dicKeyValueDTO = new DicKeyValueDTO(r, r);
                    keyValueDTOList.add(dicKeyValueDTO);
                }
            });
        }
        return RestResponse.success(keyValueDTOList);
    }

    /**
     * 进气方式下拉列表
     * @param
     * @return
     */
    @RequestMapping(MrCarAssetRestCenter.PROVIDER_CAR_INFO_CONFIG_INTAKE_LIST)
    public RestResponse<List<DicKeyValueDTO>> carConfigIntakeList() {
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CAR_INFO_CONFIG_INTAKE_LIST);
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        RestResponse response = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, url, paramMap, null, String.class);
        List<DicKeyValueDTO> keyValueDTOList = new ArrayList<>();
        if (response != null && response.isSuccess()) {
            List<String> stringList = (List<String>) response.getData();
            stringList.forEach(r -> {
                if (StringUtils.isNotBlank(r)) {
                    String turbo = TurboTypeEnum.getDesc(r);
                    DicKeyValueDTO dicKeyValueDTO = new DicKeyValueDTO(turbo, turbo);
                    keyValueDTOList.add(dicKeyValueDTO);
                }
            });
        }
        return RestResponse.success(keyValueDTOList);
    }

    /**
     * 发动机排量下拉列表
     * @param
     * @return
     */
    @RequestMapping(MrCarAssetRestCenter.PROVIDER_CAR_INFO_CONFIG_LITER_LIST)
    public RestResponse<List<DicKeyValueDTO>> carConfigLiterList() {
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CAR_INFO_CONFIG_LITER_LIST);
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        RestResponse response = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, url, paramMap, null, String.class);
        List<DicKeyValueDTO> keyValueDTOList = new ArrayList<>();
        if (response != null && response.isSuccess()) {
            List<String> stringList = (List<String>) response.getData();
            stringList.forEach(r -> {
                if (StringUtils.isNotBlank(r)) {
                    DicKeyValueDTO dicKeyValueDTO = new DicKeyValueDTO(r, r);
                    keyValueDTOList.add(dicKeyValueDTO);
                }
            });
        }
        return RestResponse.success(keyValueDTOList);
    }
}
