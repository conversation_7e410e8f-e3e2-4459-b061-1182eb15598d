package com.izu.mrcar.providerController.asset.daibuche;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.vehicle.*;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.controller.export.CarProviderSupplierExport;
import com.izu.mrcar.controller.export.CoCarProviderExport;
import com.izu.mrcar.controller.export.CoCarProviderSupplierExport;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.lingsan.ProviderDaiBuCheDataPermUtil;
import com.izu.mrcar.utils.lingsan.ProviderLingsanDataPermUtil;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

/**
* @Description: 运营端代步车车辆列表
* @author: ljw
* @Date: 2024-5-21 10:33:25
**/
@RestController
@Api(tags = {"代步车-车辆管理"})
@Slf4j
public class CoCarInfoController {

    private final MrCarAssetRestLocator mrCarAssetRestLocator = new MrCarAssetRestLocator();

    @PostMapping(MrCarAssetRestCenter.CO_PROVIDER_CAR_INFO_GET_PAGE_LIST)
    @RequestFunction(functionName = "首汽车辆列表-代步车-运营端")
    @ApiOperation(value = "代步车-首汽车辆列表",notes = "作者：连江伟")
    public RestResponse<PageDTO<CoCarInfoProviderPageRespDTO>> getPageList(@RequestBody CarInfoQueryConditionDTO param){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        param.setDataPermType(providerLoginInfo.obtainDataPerm().getDataPermType());
        param.setOperateBussCodeSet(ProviderDaiBuCheDataPermUtil.getDatePermDeptCode());
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CO_PROVIDER_CAR_INFO_GET_PAGE_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, CoCarInfoProviderPageRespDTO.class);
    }

    @GetMapping(MrCarAssetRestCenter.CO_PROVIDER_CAR_INFO_GET_DETAIL)
    @RequestFunction(functionName = "车辆详情-代步车-运营端")
    @ApiImplicitParam(name = "vehicleId",value = "车辆ID",required = true,paramType="form")
    @ApiOperation(value = "代步车-首汽车辆详情",notes = "作者：连江伟")
    public RestResponse<CarInfoDetailDTO> getDetailForProvider(@Verify(param = "vehicleId",rule = "required") Long vehicleId){
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CO_PROVIDER_CAR_INFO_GET_DETAIL);
        Map<String, Object> paramMap = new HashMap<>(1);
        paramMap.put("vehicleId",vehicleId);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.GET, url, paramMap, null, CarInfoDetailDTO.class);
    }

    @PostMapping(MrCarAssetRestCenter.CO_PROVIDER_CAR_INFO_EXPORT)
    @RequestFunction(functionName = "车辆列表导出-运营端")
    @ApiOperation(value = "代步车-首汽车辆导出",notes = "作者：连江伟")
    public RestResponse exportForIzu(@RequestBody CarInfoQueryConditionDTO param, HttpServletResponse response) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        param.setDataPermType(providerLoginInfo.obtainDataPerm().getDataPermType());
        param.setOperateBussCodeSet(ProviderDaiBuCheDataPermUtil.getDatePermDeptCode());
        Integer page = param.getPage();
        param.setPageSize(1000);
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CO_PROVIDER_CAR_INFO_GET_PAGE_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, CoCarInfoProviderPageRespDTO.class);
        if (restResponse.isSuccess()){
            PageDTO pageDto = (PageDTO) restResponse.getData();
            List<CoCarInfoProviderPageRespDTO> dtos = new ArrayList<>((int)pageDto.getTotal());
            dtos.addAll(pageDto.getResult());
            int pages = pageDto.getPages();
            while (page <= pages){
                page+=1;
                param.setPage(page);
                paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
                restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, CoCarInfoProviderPageRespDTO.class);
                if (!restResponse.isSuccess()){
                    continue;
                }
                pageDto = (PageDTO) restResponse.getData();
                dtos.addAll(pageDto.getResult());
            }
            List<CoCarProviderExport> carProviderSupplierExports = BeanUtil.copyList(dtos, CoCarProviderExport.class);
            // 创建导出参数
            ExportParams exportParams = new ExportParams();
            exportParams.setType(ExcelType.XSSF);
            exportParams.setSheetName("车辆信息");
            Workbook workbook = ExcelExportUtil.exportExcel(exportParams, CoCarProviderExport.class, carProviderSupplierExports);
            String fileName = "车辆信息_"+new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
            workbook.setSheetName(0, fileName);
            try (OutputStream outputStream = response.getOutputStream()){
/*                response.setContentType("application/vnd.ms-excel;charset=utf-8");
                fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
                response.setHeader("Content-disposition",   "attachment;filename*=utf-8''"  + fileName + ".xls");
                workbook.write(outputStream);*/
                response.setContentType("application/vnd.ms-excel");
                response.setCharacterEncoding("utf-8");
                fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
                response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);
                response.setHeader("Download-Filename", fileName);
                workbook.write(outputStream);
            }catch (Exception e){
                log.error("导出车辆列表Excel异常");
            }
            return null;
        }
        return restResponse;
    }

    /**
     * 运营端-代步车-三方车辆列表
     * @param param
     * @return
     */
    @PostMapping(MrCarAssetRestCenter.CO_PROVIDER_SUPPLIER_CAR_INFO_GET_PAGE_LIST)
    @RequestFunction(functionName = "三方车辆列表-代步车-运营端")
    @ApiOperation(value = "代步车-三方车辆列表",notes = "作者：连江伟")
    public RestResponse<PageDTO<CarInfoOperationLingSanSupplierPageRespDTO>> getPageList(@RequestBody CoCarInfoSupplierReqDTO param){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        param.setDataPermType(providerLoginInfo.obtainDataPerm().getDataPermType());
        param.setOperateBussCodeSet(ProviderDaiBuCheDataPermUtil.getDatePermDeptCode());
        param.setLoginUserId(providerLoginInfo.getBaseInfo().getStaffId());
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CO_PROVIDER_SUPPLIER_CAR_INFO_GET_PAGE_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, CarInfoOperationLingSanSupplierPageRespDTO.class);
    }

    /**
                * 运营端-三方车辆详情
     * @param vehicleId
     * @return
             */
    @GetMapping(MrCarAssetRestCenter.CO_CAR_INFO_DETAIL_PROVIDER_SUPPLIER_GET)
    @RequestFunction(functionName = "车辆详情-代步车-运营端")
    @ApiImplicitParam(name = "vehicleId",value = "车辆ID",required = true,paramType="form")
    @ApiOperation(value = "代步车-三方车辆详情",notes = "作者：连江伟")
    public RestResponse<CarInfoLingSanSupplierRespDTO> getDetail(@Verify(param = "vehicleId",rule = "required") Long vehicleId){
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CO_CAR_INFO_DETAIL_PROVIDER_SUPPLIER_GET);
        Map<String, Object> paramMap = new HashMap<>(1);
        paramMap.put("vehicleId",vehicleId);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.GET, url, paramMap, null, CarInfoLingSanSupplierRespDTO.class);
    }

    @PostMapping(MrCarAssetRestCenter.CO_SUPPLIER_CAR_UPLOAD_ATTACHMENT)
    @RequestFunction(functionName = "代步车-上传附件-运营端")
    @ApiOperation(value = "代步车-上传附件",notes = "作者：连江伟")
    public RestResponse<Boolean> uploadAttachment(@Valid @RequestBody CarInfoUploadAttachmentReqDTO param) {

        AccountBaseInfo baseInfo = LoginSessionUtil.getProviderLoginInfo().getBaseInfo();
        param.setLoginUserId(baseInfo.getStaffId());
        param.setLoginUserName(baseInfo.getStaffName());
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CO_SUPPLIER_CAR_UPLOAD_ATTACHMENT);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(MrCarAssetRestCenter.CO_PROVIDER_CAR_UPDATE)
    @RequestFunction(functionName = "代步车-车辆编辑-运营端")
    @ApiOperation(value = "代步车-车辆编辑",notes = "作者：连江伟")
    public RestResponse<Boolean> updateCarInfo(@RequestBody CarInfoUpdateCoReqDTO param) {

        AccountBaseInfo baseInfo = LoginSessionUtil.getProviderLoginInfo().getBaseInfo();
        param.setLoginUserId(baseInfo.getStaffId());
        param.setLoginUserName(baseInfo.getStaffName());
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CO_PROVIDER_CAR_UPDATE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }
    @PostMapping(MrCarAssetRestCenter.CO_PROVIDER_CAR_ADD)
    @RequestFunction(functionName = "代步车-车辆新增-运营端")
    @ApiOperation(value = "代步车-车辆新增",notes = "作者：连江伟")
    public RestResponse<Boolean> addCarInfo(@RequestBody CarInfoAddCoReqDTO param) {

        AccountBaseInfo baseInfo = LoginSessionUtil.getProviderLoginInfo().getBaseInfo();
        param.setLoginUserId(baseInfo.getStaffId());
        param.setLoginUserName(baseInfo.getStaffName());
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CO_PROVIDER_CAR_ADD);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(MrCarAssetRestCenter.CO_PROVIDER_SUPPLIER_EXPORT_PROVIDER)
    @RequestFunction(functionName = "车辆列表导出-运营端")
    @ApiOperation(value = "三方车辆导出",notes = "作者：连江伟")
    public RestResponse export(@RequestBody CoCarInfoSupplierReqDTO param, HttpServletResponse response) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        param.setDataPermType(providerLoginInfo.obtainDataPerm().getDataPermType());
        param.setOperateBussCodeSet(ProviderDaiBuCheDataPermUtil.getDatePermDeptCode());
        param.setLoginUserId(providerLoginInfo.getBaseInfo().getStaffId());
        Integer page = param.getPage();
        param.setPageSize(1000);
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CO_PROVIDER_SUPPLIER_CAR_INFO_GET_PAGE_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, CarInfoOperationPageRespDTO.class);
        if (restResponse.isSuccess()){
            PageDTO pageDto = (PageDTO) restResponse.getData();
            List<CarInfoOperationPageRespDTO> dtos = new ArrayList<>((int)pageDto.getTotal());
            dtos.addAll(pageDto.getResult());
            int pages = pageDto.getPages();
            while (page <= pages){
                page+=1;
                param.setPage(page);
                paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
                restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, CarInfoOperationPageRespDTO.class);
                if (!restResponse.isSuccess()){
                    continue;
                }
                pageDto = (PageDTO) restResponse.getData();
                dtos.addAll(pageDto.getResult());
            }
            List<CoCarProviderSupplierExport> carProviderSupplierExports = BeanUtil.copyList(dtos, CoCarProviderSupplierExport.class);
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(), CoCarProviderSupplierExport.class, carProviderSupplierExports);
            String fileName = "车辆信息_"+new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
            workbook.setSheetName(0, fileName);
            try (OutputStream outputStream = response.getOutputStream()){
                response.setContentType("application/vnd.ms-excel;charset=utf-8");
                fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
                response.setHeader("Content-disposition",   "attachment;filename*=utf-8''"  + fileName + ".xls");
                workbook.write(outputStream);
            }catch (Exception e){
                log.error("导出车辆列表Excel异常");
            }
            return null;
        }
        return restResponse;
    }

    @PostMapping(MrCarAssetRestCenter.CO_PROVIDER_CAR_UPDATE_STATE)
    @RequestFunction(functionName = "代步车删除-运营端")
    @ApiOperation(value = "代步车-删除",notes = "作者：连江伟")
    public RestResponse<Boolean> updateState(@RequestBody CoCarInfoUpdateStateReqDTO param){

        AccountBaseInfo baseInfo = LoginSessionUtil.getProviderLoginInfo().getBaseInfo();
        param.setLoginUserId(baseInfo.getStaffId());
        param.setLoginUserName(baseInfo.getStaffName());
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CO_PROVIDER_CAR_UPDATE_STATE);
        Map<String, Object> paramMap = new HashMap<>(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

}
