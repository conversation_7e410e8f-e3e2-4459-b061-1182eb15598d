package com.izu.mrcar.providerController.asset.violation;

import com.izu.asset.dto.violation.req.ListViolationSupplierBillReqDTO;
import com.izu.asset.dto.violation.resp.ViolationSupplierBillDTO;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.service.asset.violation.ViolationSupplierBillService;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.asset.AssetUtil;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @date 2024/9/27 15:03
 */
@Api(tags = "违章管理")
@RestController
public class ViolationSupplierBillController {

    @Resource
    private ViolationSupplierBillService violationSupplierBillService;


    @PostMapping("/violation/supplier/bill/list")
    @RequestFunction(functionName = "供应商账单查询")
    @ApiOperation("供应商账单查询")
    public RestResponse<PageDTO<ViolationSupplierBillDTO>> listSupplierBill(@RequestBody ListViolationSupplierBillReqDTO reqDTO) {
        AssetUtil.setProviderLoginInfo(reqDTO);
        return violationSupplierBillService.listSupplierBill(reqDTO);
    }


    @PostMapping("/violation/supplier/bill/export")
    @RequestFunction(functionName = "导出供应商账单")
    @ApiOperation("导出供应商账单")
    public RestResponse exportSupplierBill(@RequestBody ListViolationSupplierBillReqDTO reqDTO,
                                           IzuEasyExcelSession izuEasyExcelSession,
                                           HttpServletRequest request, HttpServletResponse response) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        reqDTO.setPage(1);
        reqDTO.setPageSize(500);
        izuEasyExcelSession.setUserName(providerLoginInfo.getBaseInfo().getStaffName());
        izuEasyExcelSession.setPageNo(1);
        violationSupplierBillService.exportSupplierBill(reqDTO, izuEasyExcelSession, request, response);
        return null;
    }
}
