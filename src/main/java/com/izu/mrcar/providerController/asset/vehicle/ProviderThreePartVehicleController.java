package com.izu.mrcar.providerController.asset.vehicle;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.style.row.SimpleRowHeightStyleStrategy;
import com.google.common.collect.Sets;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.CarBrandDTO;
import com.izu.asset.dto.CarModelDTO;
import com.izu.asset.dto.vehicle.CarInfoLingSanSupplierRespDTO;
import com.izu.asset.dto.vehicle.SupplierCarBatchImportDTO;
import com.izu.asset.dto.vehicle.req.ThirdPartVehicleAddReqDTO;
import com.izu.asset.dto.vehicle.req.ThirdPartVehicleDeleteReqDTO;
import com.izu.asset.dto.vehicle.req.ThirdPartVehicleDetailReqDTO;
import com.izu.asset.dto.vehicle.req.ThirdPartVehicleModifyReqDTO;
import com.izu.asset.dto.vehicle.req.ThirdPartVehicleUploadAttachmentReqDTO;
import com.izu.asset.dto.vehicle.req.ThreePartVehicleListReqDTO;
import com.izu.asset.dto.vehicle.resp.ThirdPartVehicleDTO;
import com.izu.asset.dto.vehicle.resp.ThirdPartVehicleDetailRespDTO;
import com.izu.config.MrCarConfigRestLocator;
import com.izu.config.dto.CityDicDTO;
import com.izu.consts.ConfigURI;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.exception.RestErrorException;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.controller.maintain.garage.CustomCellWriteHandler;
import com.izu.mrcar.controller.maintain.garage.DbCarExcelSheetWriteHandler;
import com.izu.asset.dto.insurance.resp.BatchDTO;
import com.izu.mrcar.providerController.asset.lingsan.ProviderCarInfoImportDTO;
import com.izu.mrcar.service.asset.CarModelService;
import com.izu.mrcar.service.asset.vehicle.ThreePartVehicleImportService;
import com.izu.mrcar.service.asset.vehicle.ThreePartVehicleService;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.excel.SelectDataSheetMergeWriteHandler;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@Api(tags = {"三方车辆"})
@Slf4j
@RequestMapping("/provider/threePart/vehicle")
public class ProviderThreePartVehicleController {



    @Autowired
    private CarModelService carModelService;

    @Resource
    private ThreePartVehicleService threePartVehicleService;

    private static final String URL_PRE_FIX = "/threePart/vehicle/";
    private final MrCarAssetRestLocator mrCarAssetRestLocator = new MrCarAssetRestLocator();

    @PostMapping("/add")
    @RequestFunction(functionName = "三方车辆添加")
    @ApiOperation(value = "三方车辆添加")
    public RestResponse<Boolean> add(@RequestBody ThirdPartVehicleAddReqDTO param) {
        AccountBaseInfo baseInfo = LoginSessionUtil.getProviderLoginInfo().getBaseInfo();
        param.setLoginUserId(baseInfo.getStaffId());
        param.setLoginUserName(baseInfo.getStaffName());
        String url = mrCarAssetRestLocator.getRestUrl(URL_PRE_FIX + "add");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping("/modify")
    @RequestFunction(functionName = "三方车辆编辑")
    @ApiOperation(value = "三方车辆编辑")
    public RestResponse<Boolean> modify(@RequestBody ThirdPartVehicleModifyReqDTO param) {
        AccountBaseInfo baseInfo = LoginSessionUtil.getProviderLoginInfo().getBaseInfo();
        param.setLoginUserId(baseInfo.getStaffId());
        param.setLoginUserName(baseInfo.getStaffName());
        String url = mrCarAssetRestLocator.getRestUrl(URL_PRE_FIX + "modify");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping("/remove")
    @RequestFunction(functionName = "三方车辆删除")
    @ApiOperation(value = "三方车辆删除")
    public RestResponse<Boolean> remove(@RequestBody ThirdPartVehicleDeleteReqDTO param) {
        AccountBaseInfo baseInfo = LoginSessionUtil.getProviderLoginInfo().getBaseInfo();
        param.setLoginUserId(baseInfo.getStaffId());
        param.setLoginUserName(baseInfo.getStaffName());
        String url = mrCarAssetRestLocator.getRestUrl(URL_PRE_FIX + "remove");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping("/list")
    @RequestFunction(functionName = "三方车辆列表")
    @ApiOperation(value = "三方车辆列表")
    public RestResponse<PageDTO<ThirdPartVehicleDTO>> list(@RequestBody ThreePartVehicleListReqDTO reqDto){
        return threePartVehicleService.list(reqDto);
    }

    @PostMapping("/export")
    @RequestFunction(functionName = "三方车辆导出")
    @ApiOperation(value = "三方车辆导出")
    public void export(@RequestBody ThreePartVehicleListReqDTO reqDto, IzuEasyExcelSession izuEasyExcelSession, HttpServletResponse response) {
        try {
            reqDto.setPage(1);
            reqDto.setPageSize(2000);
            izuEasyExcelSession.setPageNo(1);
            threePartVehicleService.export(reqDto, izuEasyExcelSession, response);
        } catch (Exception e) {
            log.warn("ProviderThreePartVehicleController#export Exception：", e);
        }
    }



    @PostMapping("/detail")
    @RequestFunction(functionName = "三方车辆详情")
    @ApiOperation(value = "三方车辆详情")
    public RestResponse<ThirdPartVehicleDetailRespDTO> detail(@RequestBody ThirdPartVehicleDetailReqDTO reqDTO){
        String url = mrCarAssetRestLocator.getRestUrl(URL_PRE_FIX + "detail");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, CarInfoLingSanSupplierRespDTO.class);
    }

    @PostMapping("/uploadAttachment")
    @RequestFunction(functionName = "上传附件")
    @ApiOperation(value = "上传附件")
    public RestResponse<Boolean> uploadAttachment(@Valid @RequestBody ThirdPartVehicleUploadAttachmentReqDTO param) {
        AccountBaseInfo baseInfo = LoginSessionUtil.getProviderLoginInfo().getBaseInfo();
        param.setLoginUserId(baseInfo.getStaffId());
        param.setLoginUserName(baseInfo.getStaffName());
        String url = mrCarAssetRestLocator.getRestUrl(URL_PRE_FIX + "uploadAttachment");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping("/import")
    @RequestFunction(functionName = "三方车辆导入")
    @ApiOperation("三方车辆导入")
    public RestResponse<BatchDTO> batchInsert(
            @Validated @RequestBody SupplierCarBatchImportDTO importReqDTO,
            HttpServletRequest request,
            HttpServletResponse response) {
        try {
            final ThreePartVehicleImportService threePartVehicleImportService = new ThreePartVehicleImportService(importReqDTO, carModelService, false);
            return threePartVehicleImportService.dealExcel(request, response);
        } catch (RestErrorException e) {
            return RestResponse.create(e.getErrCode(), e.getMessage(), false, null);
        }catch (Exception e){
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }

    @PostMapping("/downloadImportTemplate")
    @RequestFunction(functionName = "三方车辆导入模板下载")
    @ApiOperation("三方车辆导入模板下载")
    public void downloadImportTemplate(HttpServletResponse response) throws Exception {

        // 导出模板名称
        String fileName = "三方车辆导入模板";
        response.setContentType("application/msexcel");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码
        String fileNameEncode = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''"  + fileNameEncode + ExcelTypeEnum.XLSX.getValue());
        // 设置下拉框内容
        Map<Integer, List<String>> selectMap = buildSelectMap();
        //表头
        List<ProviderCarInfoImportDTO> list = new ArrayList<>();
        EasyExcelFactory.write(response.getOutputStream())
                .registerWriteHandler(new CustomCellWriteHandler())
                .registerWriteHandler(new DbCarExcelSheetWriteHandler(Sets.newHashSet(6,7)))
                // 设置字典
                .registerWriteHandler(new SelectDataSheetMergeWriteHandler(selectMap))
                // 设置行高度
                .registerWriteHandler(new SimpleRowHeightStyleStrategy((short) 35, (short) 22))
                // 此处对应的是实体类
                .head(ProviderCarInfoImportDTO.class)
                // 设置导出格式为xls后缀
                .excelType(ExcelTypeEnum.XLSX)
                .sheet("导入车辆")
                .doWrite(list);
    }

    private Map<Integer, List<String>> buildSelectMap(){
        Map<Integer, List<String>> selectMap = new HashMap<>();
        List<CarBrandDTO> carBrandDTOS = carModelService.getCarBrandDicV2();
        List<String> brandList = carBrandDTOS.stream().map(CarBrandDTO::getBrandName).collect(Collectors.toList());
        selectMap.put(3, brandList);

        List<CarModelDTO> carModelDTOS = carModelService.getCarModelDicV2();
        List<String> modelList = carModelDTOS.stream().map(CarModelDTO::getModelName).collect(Collectors.toList());
        selectMap.put(4, modelList);

        RestResponse cityResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, new MrCarConfigRestLocator().getRestUrl(ConfigURI.DIC_GET_CITY_DIC_LIST), null, null, CityDicDTO.class);
        if(cityResponse!=null && cityResponse.isSuccess()){
            List<CityDicDTO> cityDicDTOS = (List<CityDicDTO>) cityResponse.getData();
            if(CollectionUtils.isNotEmpty(cityDicDTOS)){
                List<String> cityList = cityDicDTOS.stream().map(CityDicDTO::getCityName).collect(Collectors.toList());
                selectMap.put(5,cityList);
            }
        }
        return selectMap;
    }
}
