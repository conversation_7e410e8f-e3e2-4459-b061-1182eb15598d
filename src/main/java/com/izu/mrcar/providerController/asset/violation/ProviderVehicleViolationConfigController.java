package com.izu.mrcar.providerController.asset.violation;

import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.consts.RequestSourceEnum;
import com.izu.asset.dto.violation.req.DownloadViolationVehicleReqDTO;
import com.izu.asset.dto.violation.req.ListViolationVehicleReqDTO;
import com.izu.asset.dto.violation.req.RemoveAllViolationVehicleReqDTO;
import com.izu.asset.dto.violation.req.RemoveViolationVehicleReqDTO;
import com.izu.asset.dto.violation.resp.ViolationVehicleRespDTO;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.controller.AbstractExcelUploadController;
import com.izu.mrcar.dto.violation.ImportViolationVehicleParamDTO;
import com.izu.mrcar.service.asset.violation.VehicleViolationConfigImportService;
import com.izu.mrcar.service.asset.violation.VehicleViolationConfigService;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.asset.AssetUtil;
import com.izu.user.dto.staff.pc.AccountBelongDepartment;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2024/9/25 19:43
 */
@Api(tags = "违章管理")
@RestController
@RequestMapping("/provider/violation")
public class ProviderVehicleViolationConfigController {

    private final MrCarAssetRestLocator mrCarAssetRestLocator = new MrCarAssetRestLocator();

    @Resource
    private VehicleViolationConfigService vehicleViolationConfigService;
    @Resource
    private VehicleViolationConfigImportService vehicleViolationConfigImportService;



    @PostMapping("/vehicle/import/template/download")
    @RequestFunction(functionName = "下载车辆导入模板")
    @ApiOperation("下载车辆导入模板")
    public void downloadVehicleImportTemplate(HttpServletResponse response) throws Exception{
        vehicleViolationConfigService.downloadVehicleImportTemplate(response);
    }


    @PostMapping("/vehicle/list")
    @RequestFunction(functionName = "车辆列表")
    @ApiOperation("车辆列表")
    public RestResponse<PageDTO<ViolationVehicleRespDTO>> listVehicle(@RequestBody ListViolationVehicleReqDTO reqDTO) {
        AssetUtil.setProviderLoginInfo(reqDTO);
        return vehicleViolationConfigService.listVehicle(reqDTO);
    }

    @PostMapping("/vehicle/export")
    @RequestFunction(functionName = "车辆导出")
    @ApiOperation("车辆导出")
    public RestResponse exportVehicle(@RequestBody ListViolationVehicleReqDTO reqDTO,
                                      IzuEasyExcelSession izuEasyExcelSession,
                                      HttpServletRequest request, HttpServletResponse response){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        ListViolationVehicleReqDTO reqDto = new ListViolationVehicleReqDTO();
        reqDto.setPage(1);
        reqDto.setPageSize(500);
        izuEasyExcelSession.setUserName(providerLoginInfo.getBaseInfo().getStaffName());
        izuEasyExcelSession.setPageNo(1);
        vehicleViolationConfigService.exportVehicle(reqDTO, izuEasyExcelSession, request, response);
        return null;

    }


    @PostMapping("/vehicle/import")
    @RequestFunction(functionName = "车辆导入")
    @ApiOperation("车辆导入")
    public RestResponse<Boolean> importVehicle(@RequestBody ImportViolationVehicleParamDTO reqDTO,
                                               HttpServletRequest request,
                                               HttpServletResponse response) {
        AbstractExcelUploadController.ExcelUploadResult eur = new AbstractExcelUploadController.ExcelUploadResult();
        request.setAttribute("companyId", reqDTO.getCompanyId());
        request.setAttribute("account", reqDTO.getAccount());
        request.setAttribute("requestSource", RequestSourceEnum.PROVIDER.getRequestSource());
        return vehicleViolationConfigImportService.start(reqDTO.getExcelUrl(), request, response, eur, 1);
    }


    @PostMapping("/vehicle/remove")
    @RequestFunction(functionName = "移除单个车辆")
    @ApiOperation("移除单个车辆")
    public RestResponse<Boolean> removeVehicle(@RequestBody RemoveViolationVehicleReqDTO reqDTO) {
        AssetUtil.setProviderLoginInfo(reqDTO);
        HashMap<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        String restUrl = mrCarAssetRestLocator.getRestUrl("/violation/vehicle/remove");
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null, Boolean.class);
    }

    @PostMapping("/vehicle/removeAll")
    @RequestFunction(functionName = "移除所有车辆")
    @ApiOperation("移除所有车辆")
    public RestResponse<Boolean> removeAllVehicle(@RequestBody RemoveAllViolationVehicleReqDTO reqDTO) {
        AssetUtil.setProviderLoginInfo(reqDTO);
        HashMap<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        String restUrl = mrCarAssetRestLocator.getRestUrl("/violation/vehicle/removeAll");
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null, Boolean.class);
    }



}
