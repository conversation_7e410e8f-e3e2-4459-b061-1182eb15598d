package com.izu.mrcar.providerController.asset.violation;

import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.consts.RequestSourceEnum;
import com.izu.asset.consts.ViolationStatusEnum;
import com.izu.asset.dto.CarViolationDTO;
import com.izu.asset.dto.ProviderCarViolationQueryEmailPageReqDTO;
import com.izu.asset.dto.ProviderCarViolationQueryPageReqDTO;
import com.izu.asset.dto.violation.req.GetCarViolationReqDTO;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.excel.dto.IzuMailSession;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.cache.RedisJsonCache;
import com.izu.mrcar.dto.DicKeyValueDTO;
import com.izu.mrcar.order.dto.provider.input.OrderInfoOpeInputDTO;
import com.izu.mrcar.service.asset.violation.CarViolationEmailService;
import com.izu.mrcar.service.asset.violation.CarViolationService;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.asset.AssetUtil;
import com.izu.user.dto.CompanyPaidSearchReq;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;


@Slf4j
@RestController
@Api(tags = "违章管理")
@RequestMapping("/provider")
public class ProviderCarViolationController {


    @Resource
    private CarViolationService carViolationService;
    @Resource
    private CarViolationEmailService carViolationEmailService;

    @Autowired
    private RedisJsonCache redisJsonCache;

    @PostMapping("/violation/queryCarViolationPageList")
    @RequestFunction(functionName = "查询车辆违章列表")
    @ApiOperation("查询车辆违章列表")
    public RestResponse<PageDTO<CarViolationDTO>> queryCarViolationPageList(@RequestBody ProviderCarViolationQueryPageReqDTO dto) {
        AssetUtil.setProviderLoginInfo(dto);
        return carViolationService.listProviderCarViolation(dto);
    }


    @PostMapping("/violation/exportCarViolation")
    @RequestFunction(functionName = "违章信息导出")
    @ApiOperation("违章信息导出")
    public void exportCarViolation(@RequestBody ProviderCarViolationQueryPageReqDTO reqDto,
                                      IzuEasyExcelSession izuEasyExcelSession,
                                      HttpServletRequest request, HttpServletResponse response){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        izuEasyExcelSession.setUserName(providerLoginInfo.getBaseInfo().getStaffName());
        izuEasyExcelSession.setPageNo(1);
        Boolean checkRst = carViolationService.checkExportDataNum(reqDto, RequestSourceEnum.PROVIDER, response);
        if(!checkRst){
            return;
        }
        reqDto.setPage(1);
        reqDto.setPageSize(2000);
        request.setAttribute("requestSource", RequestSourceEnum.PROVIDER.getRequestSource());
        carViolationService.exportCarViolation(reqDto, izuEasyExcelSession, request, response);
        return;

    }
    /**
     * @Description: 违章信息导出
     **/
    @PostMapping(value = "/violation/exportCarViolation/exportEmail")
    @RequestFunction(functionName = "违章信息导出到邮件-运营端")
    @ApiOperation(value = "违章信息导出到邮件-运营端", notes = "作者：丁伟兵")
    public RestResponse exportOrderBusinessInfoListAsyn(@RequestBody ProviderCarViolationQueryEmailPageReqDTO reqDTO) {
        if (StringUtils.isBlank(reqDTO.getEmail())) {
            RestResponse restResponse = RestResponse.fail(9999);
            restResponse.setMsg("邮箱号不能为空！！！");
            return restResponse;
        }
        // 防止上次导出未完成的情况下，再次导出
        String lockKey = "mrcar:provider:violation:exportCarViolation:asyn:" + reqDTO.getEmail();
        final String cacheValue = redisJsonCache.get(lockKey, String.class);
        if (StringUtils.isNotBlank(cacheValue)) {
            log.warn("已提交申请，请稍等");
            return RestResponse.create(RestErrorCode.SYSTEM_EXCEPTION, "您已经提交了导出请求，请稍后查收电子邮件。（大约耗时3分钟）", false, null);
        }
        redisJsonCache.set(lockKey, "Y", 3 * 60);
        IzuMailSession izuMailSession = new IzuMailSession();
        AccountBaseInfo accountBaseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
        izuMailSession.setUserName(accountBaseInfo.getStaffName());
        izuMailSession.setMailOpName("违章信息列表");
        izuMailSession.setMailSubject("违章信息列表");
        String email = reqDTO.getEmail();
        izuMailSession.setToMail(email);
        CompletableFuture.runAsync(() ->{
            carViolationEmailService.exportToEmail(reqDTO, izuMailSession);
        }).whenComplete((unused, throwable) -> {
            redisJsonCache.delete(lockKey);
            if (throwable != null) {
                log.error("违章信息列表异步导出至邮箱异常", throwable);
            }
        });
        return RestResponse.create(0, "提交成功！请稍后查收电子邮件。（大约耗时3分钟！）", true, null);
    }


    @PostMapping(value = "/getCarViolation")
    @RequestFunction(functionName = "违章详情查询")
    @ApiOperation("违章详情查询")
    public RestResponse<CarViolationDTO> getCarViolation(@RequestBody GetCarViolationReqDTO dto) {
        HashMap<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        String restUrl = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.VEHICLE_GET_CAR_VIOLATION);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null, CarViolationDTO.class);
    }

    @PostMapping(value = "/violation/violationStatusEnum")
    @RequestFunction(functionName = "违章状态枚举")
    @ApiOperation(value = "违章状态枚举")
    public RestResponse<List<DicKeyValueDTO>> violationSyncStatusEnum(@RequestBody CompanyPaidSearchReq companyPaidSearchReq) {
        List<DicKeyValueDTO> collect = Arrays.stream(ViolationStatusEnum.values())
                .map(c -> new DicKeyValueDTO(c.getCode(), c.getDescription()))
                .collect(Collectors.toList());
        return RestResponse.success(collect);
    }

}
