package com.izu.mrcar.providerController.asset.violation;

import com.google.common.collect.Lists;
import com.izu.asset.consts.violation.ViolationServiceCallResultEnum;
import com.izu.asset.consts.violation.ViolationServiceCallTypeEnum;
import com.izu.asset.dto.violation.req.ListViolationServiceCallDetailReqDTO;
import com.izu.asset.dto.violation.resp.ViolationServiceCallLogDTO;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.dto.ListBillDetailDTO;
import com.izu.mrcar.service.asset.violation.ViolationServiceCallService;
import com.izu.mrcar.service.user.CompanyService;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.asset.AssetUtil;
import com.izu.user.dto.company.CompanyDTO;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/9/27 15:03
 */
@Api(tags = "违章管理")
@RestController
public class ViolationServiceCallController {

    @Resource
    private ViolationServiceCallService violationServiceCallService;
    @Resource
    private CompanyService companyService;




    @PostMapping("/violation/service/call/detail/list")
    @RequestFunction(functionName = "查询调用明细")
    @ApiOperation("查询调用明细")
    public RestResponse<PageDTO<ViolationServiceCallLogDTO>> listCallDetail(@RequestBody ListViolationServiceCallDetailReqDTO reqDTO) {
        AssetUtil.setProviderLoginInfo(reqDTO);
        return violationServiceCallService.listCallDetail(reqDTO);
    }



    @PostMapping("/violation/service/call/detail/export")
    @RequestFunction(functionName = "导出调用明细")
    @ApiOperation("导出调用明细")
    public RestResponse exportCallDetail(@RequestBody ListViolationServiceCallDetailReqDTO reqDTO,
                                         IzuEasyExcelSession izuEasyExcelSession,
                                         HttpServletRequest request, HttpServletResponse response) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        reqDTO.setPage(1);
        reqDTO.setPageSize(500);
        izuEasyExcelSession.setUserName(providerLoginInfo.getBaseInfo().getStaffName());
        izuEasyExcelSession.setPageNo(1);
//        violationServiceCallService.exportCallDetailCsv(reqDTO, izuEasyExcelSession, request, response);
        violationServiceCallService.exportCallDetailCsv(reqDTO, response);
        return null;
    }


    @PostMapping("/violation/service/call/listViolationBillDetail")
    @RequestFunction(functionName = "违章查询信息")
    @ApiOperation("违章查询信息")
    public RestResponse<PageDTO<ViolationServiceCallLogDTO>> listViolationBillDetail(@RequestBody ListBillDetailDTO reqDTO) {
        return doViolationBillDetail(reqDTO, ViolationServiceCallTypeEnum.QUERY_VIOLATION.getCode());
    }
    @PostMapping("/violation/service/call/listViolationImgBillDetail")
    @RequestFunction(functionName = "违章查询图片")
    @ApiOperation("违章查询图片")
    public RestResponse<PageDTO<ViolationServiceCallLogDTO>> listViolationImgBillDetail(@RequestBody ListBillDetailDTO reqDTO) {
        return doViolationBillDetail(reqDTO, ViolationServiceCallTypeEnum.QUERY_VIOLATION_WITH_PICTURE.getCode());
    }

    private RestResponse<PageDTO<ViolationServiceCallLogDTO>> doViolationBillDetail(ListBillDetailDTO reqDTO, Byte callTyp) {
        CompanyDTO companyDTO = companyService.getByCustomerCode(reqDTO.getCompanyCode());
        if(Objects.isNull(companyDTO)){
            return RestResponse.success(new PageDTO<ViolationServiceCallLogDTO>(reqDTO.getPage(), reqDTO.getPageSize(), 0, Lists.newArrayList()));
        }
        ListViolationServiceCallDetailReqDTO req = new ListViolationServiceCallDetailReqDTO();
        AssetUtil.setProviderLoginInfo(req);
        req.setCompanyId(companyDTO.getCompanyId());
        if(Objects.nonNull(reqDTO.getStartDate())){
            req.setQryBeginTime(reqDTO.getStartDate());
        }
        if(Objects.nonNull(reqDTO.getEndDate())){
            req.setQryEndTime(reqDTO.getEndDate());
        }
        if(Objects.nonNull(callTyp)){
            req.setCallType(callTyp);
        }
        req.setCallResult(ViolationServiceCallResultEnum.SUCCESS.getCode());
        return violationServiceCallService.listCallDetail(req);
    }
}
