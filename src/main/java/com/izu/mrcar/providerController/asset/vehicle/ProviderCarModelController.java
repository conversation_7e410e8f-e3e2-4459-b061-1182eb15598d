package com.izu.mrcar.providerController.asset.vehicle;

import com.google.common.collect.Maps;
import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.BrandModelReqDTO;
import com.izu.asset.dto.vehicle.CarModelExportDTO;
import com.izu.asset.dto.vehicle.CarModelListRespDTO;
import com.izu.asset.dto.violation.BrandModelListReqDTO;
import com.izu.asset.dto.violation.BrandModelPictureReqDTO;
import com.izu.asset.dto.violation.CarModelPictureRespDTO;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;

import java.util.*;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 */
@Api(tags = "车辆品牌车型")
@RestController
public class ProviderCarModelController {

	@Autowired
	private ProviderCarModelExportService providerCarModelExportService;

	@GetMapping(MrCarAssetRestCenter.PROVIDER_CAR_MODEL_LIST)
	@RequestFunction(functionName = "车型列表")
	@ApiOperation(value = "车型列表",notes = "作者：贺新春")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "brandName",value = "品牌名称",paramType="form"),
			@ApiImplicitParam(name = "modelName",value = "车型名称",paramType="form")
	})
	public RestResponse<List<CarModelListRespDTO>> carModelList(String brandName, String modelName){
		String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.PROVIDER_CAR_MODEL_LIST);
		Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(2);
		paramMap.put("brandName", brandName);
		paramMap.put("modelName", modelName);
		return RestClient.requestInside(BaseHttpClient.HttpMethod.GET, url, paramMap, null);
	}

	@RequestMapping(value=MrCarAssetRestCenter.PROVIDER_CAR_MODEL_LIST_V2,method = {RequestMethod.POST})
	@RequestFunction(functionName = "车型列表")
	@ApiOperation(value = "车型列表",notes = "作者：贺新春")
	public RestResponse<List<CarModelListRespDTO>> carModelList(@RequestBody BrandModelReqDTO brandModelReqDTO){
		String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.PROVIDER_CAR_MODEL_LIST_V2);
		Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(2);
		paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, brandModelReqDTO);
		return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
	}


	@RequestMapping(value = MrCarAssetRestCenter.PROVIDER_CAR_MODEL_LIST_V3, method = {RequestMethod.POST})
	@RequestFunction(functionName = "车型列表")
	@ApiOperation(value = "车型列表", notes = "作者：rwg")
	public RestResponse<PageDTO<CarModelListRespDTO>> carModelList(@RequestBody BrandModelListReqDTO param) {
		String restUrl = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.PROVIDER_CAR_MODEL_LIST_V3);
		Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
		paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
		return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, CarModelListRespDTO.class);
	}


	@RequestMapping(value = MrCarAssetRestCenter.PROVIDER_CAR_MODEL_PICTURE_DETAIL, method = {RequestMethod.POST})
	@RequestFunction(functionName = "车型图片详情")
	@ApiOperation(value = "车型图片详情", notes = "作者：rwg")
	public RestResponse<CarModelPictureRespDTO> carModelPictureDetail(@RequestParam(value = "modelCode", required = true) String modelCode) {
		String restUrl = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.PROVIDER_CAR_MODEL_PICTURE_DETAIL);
		Map<String, Object> paraMap = new HashMap<String, Object>();
		paraMap.put("modelCode", modelCode);
		return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null);
	}


	@RequestMapping(value = MrCarAssetRestCenter.PROVIDER_CAR_MODEL_PICTURE_UPDATE, method = {RequestMethod.POST})
	@RequestFunction(functionName = "编辑车型图片")
	@ApiOperation(value = "编辑车型图片", notes = "作者：rwg")
	public RestResponse carModelPictureUpdate(@RequestBody BrandModelPictureReqDTO param) {
		LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
		param.setCreatorId(loginBaseInfo.obtainBaseInfo().getStaffId());
		param.setCreatorName(loginBaseInfo.obtainBaseInfo().getStaffName());
		param.setUpdateId(loginBaseInfo.obtainBaseInfo().getStaffId());
		param.setUpdateName(loginBaseInfo.obtainBaseInfo().getStaffName());
		String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.PROVIDER_CAR_MODEL_PICTURE_UPDATE);
		Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(2);
		paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
		return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
	}

	@RequestMapping(value = MrCarAssetRestCenter.PROVIDER_CAR_MODEL_LIST_V3_EXPORT, method = {RequestMethod.POST})
	@RequestFunction(functionName = "车型信息导出-运营端")
	@ApiOperation(value = "车型信息导出-运营端", notes = "作者：任伟光")
	@ExportExcelWeb(fileName = "车型信息.xlsx", filePath = "/data/logs/excel/tmp", sheet = "车型信息",
			c = CarModelExportDTO.class, isAsync = true)
	public PageDTO exportBusinessOrderInfoList(@RequestBody BrandModelListReqDTO brandModelReqDTO,
											   IzuEasyExcelSession izuEasyExcelSession,
											   HttpServletRequest request, HttpServletResponse response) {
		ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
		izuEasyExcelSession.setUserName(providerLoginInfo.getBaseInfo().getStaffName());
		return providerCarModelExportService.getDataByPage(brandModelReqDTO, izuEasyExcelSession.getPageNo(), 10000);
	}
}
