package com.izu.mrcar.providerController.asset.lingsan;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.style.row.SimpleRowHeightStyleStrategy;
import com.google.common.collect.Sets;
import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.consts.AssetConstant;
import com.izu.asset.consts.ModelSourceEnum;
import com.izu.asset.consts.VehicleColorEnum;
import com.izu.asset.consts.VehicleEnum;
import com.izu.asset.dto.CarBrandDTO;
import com.izu.asset.dto.CarModelDTO;
import com.izu.asset.dto.vehicle.SupplierCarBatchImportDTO;
import com.izu.asset.dto.vehicle.SupplierCarImportDTO;
import com.izu.config.MrCarConfigRestLocator;
import com.izu.config.dto.CityDicDTO;
import com.izu.consts.ConfigURI;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.VinUtil;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.controller.AbstractExcelUploadController;
import com.izu.mrcar.controller.device.DeviceGpsController;
import com.izu.mrcar.controller.maintain.garage.CustomCellWriteHandler;
import com.izu.mrcar.controller.maintain.garage.DbCarExcelSheetWriteHandler;
import com.izu.mrcar.controller.maintain.garage.MaintainImportDTO;
import com.izu.mrcar.service.asset.CarModelService;
import com.izu.mrcar.utils.DateUtil;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.excel.SelectDataSheetMergeWriteHandler;
import com.izu.user.config.UserRestLocator;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.dto.ImportErrorDTO;
import com.izu.user.dto.company.lingsan.SupplierSimpleRespDTO;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
* @Description: 零散用车运营端车辆导入
* @author: hxc
* @Date: 2023/2/6
**/
@RestController
@Api(tags = {"零散用车-车辆管理"})
@Slf4j
public class CarInfoImportLingSanController {

    @Autowired
    private CarModelService carModelService;

    @PostMapping(MrCarAssetRestCenter.LING_SAN_PROVIDER_CAR_BATCH_INSERT)
    @RequestFunction(functionName = "零散用车-导入车辆信息")
    @ApiOperation("零散用车-导入车辆信息")
    public RestResponse batchInsert(
            @Validated SupplierCarBatchImportDTO supplierCarBatchImportDTO,
            HttpServletRequest request,
            HttpServletResponse response) {
        final CarInfoImportLingSanExcelComponent carInfoImportLingSanExcelComponent = new CarInfoImportLingSanExcelComponent(supplierCarBatchImportDTO, carModelService, false);
        return carInfoImportLingSanExcelComponent.dealExcel(request, response);
    }

    @PostMapping(MrCarAssetRestCenter.LING_SAN_PROVIDER_CAR_DOWNLOAD_IMPORT_TEMPLATE)
    @RequestFunction(functionName = "三方车辆-导入车辆模板下载")
    @ApiOperation("三方车辆-导入车辆模板下载")
    public void downloadImportTemplate(HttpServletResponse response) throws Exception {

        // 导出模板名称
        String fileName = "三方车辆导入模板";
        response.setContentType("application/msexcel");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码
        String fileNameEncode = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''"  + fileNameEncode + ExcelTypeEnum.XLSX.getValue());
        // 设置下拉框内容
        Map<Integer, List<String>> selectMap = buildSelectMap();
        //表头
        List<ProviderCarInfoImportDTO> list = new ArrayList<>();
        EasyExcelFactory.write(response.getOutputStream())
                .registerWriteHandler(new CustomCellWriteHandler())
                .registerWriteHandler(new DbCarExcelSheetWriteHandler(Sets.newHashSet(6,7)))
                // 设置字典
                .registerWriteHandler(new SelectDataSheetMergeWriteHandler(selectMap))
                // 设置行高度
                .registerWriteHandler(new SimpleRowHeightStyleStrategy((short) 35, (short) 22))
                // 此处对应的是实体类
                .head(ProviderCarInfoImportDTO.class)
                // 设置导出格式为xls后缀
                .excelType(ExcelTypeEnum.XLSX)
                .sheet("导入车辆")
                .doWrite(list);
    }

    private Map<Integer, List<String>> buildSelectMap(){
        Map<Integer, List<String>> selectMap = new HashMap<>();
        List<CarBrandDTO> carBrandDTOS = carModelService.getCarBrandDicV2();
        List<String> brandList = carBrandDTOS.stream().map(CarBrandDTO::getBrandName).collect(Collectors.toList());
        selectMap.put(3, brandList);

        List<CarModelDTO> carModelDTOS = carModelService.getCarModelDicV2();
        List<String> modelList = carModelDTOS.stream().map(CarModelDTO::getModelName).collect(Collectors.toList());
        selectMap.put(4, modelList);

        RestResponse cityResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, new MrCarConfigRestLocator().getRestUrl(ConfigURI.DIC_GET_CITY_DIC_LIST), null, null, CityDicDTO.class);
        if(cityResponse!=null && cityResponse.isSuccess()){
            List<CityDicDTO> cityDicDTOS = (List<CityDicDTO>) cityResponse.getData();
            if(CollectionUtils.isNotEmpty(cityDicDTOS)){
                List<String> cityList = cityDicDTOS.stream().map(CityDicDTO::getCityName).collect(Collectors.toList());
                selectMap.put(5,cityList);
            }
        }
        return selectMap;
    }
}
