package com.izu.mrcar.providerController.asset.violation;

import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.violation.req.CheckSyncAccountViolationReqDTO;
import com.izu.asset.dto.violation.req.GetAccountHostingLoginStatusReqDTO;
import com.izu.asset.dto.violation.req.GetAccountLoginQrcodeReqDTO;
import com.izu.asset.dto.violation.req.ModifyAccountVehicleQryTypeReqDTO;
import com.izu.asset.dto.violation.req.SyncAccountViolationReqDTO;
import com.izu.asset.dto.violation.resp.CheckSyncAccountViolationRespDTO;
import com.izu.asset.dto.violation.resp.GetAccountHostingLoginStatusRespDTO;
import com.izu.asset.dto.violation.resp.GetAccountLoginQrcodeRespDTO;
import com.izu.asset.dto.violation.resp.ModifyAccountVehicleQryTypeRespDTO;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.service.asset.violation.TrafficAccountService;
import com.izu.mrcar.utils.asset.AssetUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2024/9/29 09:25
 */
@Api(tags = "违章管理")
@RestController
public class ProviderTrafficAccountController {

    private final MrCarAssetRestLocator mrCarAssetRestLocator = new MrCarAssetRestLocator();

    @Resource
    private TrafficAccountService trafficAccountService;


    @PostMapping("/provider/traffic/account/violation/sync")
    @RequestFunction(functionName = "同步账号违章信息")
    @ApiOperation("同步账号违章信息")
    public RestResponse<Boolean> syncAccountViolation(@RequestBody SyncAccountViolationReqDTO reqDTO) {
        AssetUtil.setProviderLoginInfo(reqDTO);
        HashMap<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        String restUrl = mrCarAssetRestLocator.getRestUrl("/traffic/account/violation/sync");
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null, Boolean.class);
    }

    @PostMapping("/provider/traffic/account/violation/sync/check")
    @RequestFunction(functionName = "同步账号违章信息检查")
    @ApiOperation("同步账号违章信息检查")
    public RestResponse<CheckSyncAccountViolationRespDTO> checkSyncAccountViolation(@RequestBody CheckSyncAccountViolationReqDTO reqDTO) {
        AssetUtil.setProviderLoginInfo(reqDTO);
        HashMap<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        String restUrl = mrCarAssetRestLocator.getRestUrl("/traffic/account/violation/sync/check");
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null, CheckSyncAccountViolationRespDTO.class);
    }

    @PostMapping("/provider/traffic/account/violation/modifyVehicleQryType")
    @RequestFunction(functionName = "修改交管账号车辆查询范围")
    @ApiOperation("修改交管账号车辆查询范围")
    public RestResponse<ModifyAccountVehicleQryTypeRespDTO> modifyVehicleQryType(@RequestBody ModifyAccountVehicleQryTypeReqDTO reqDTO) {
        AssetUtil.setProviderLoginInfo(reqDTO);
        HashMap<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        String restUrl = mrCarAssetRestLocator.getRestUrl("/traffic/account/violation/modifyVehicleQryType");
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null, ModifyAccountVehicleQryTypeRespDTO.class);
    }


    @PostMapping("/provider/traffic/account/violation/getLoginQrcode")
    @RequestFunction(functionName = "获取交管账号登录二维码")
    @ApiOperation("获取交管账号登录二维码")
    public RestResponse<GetAccountLoginQrcodeRespDTO> getLoginQrcode(@RequestBody GetAccountLoginQrcodeReqDTO reqDTO) {
        AssetUtil.setProviderLoginInfo(reqDTO);
        return trafficAccountService.getLoginQrcode(reqDTO);

    }


    @PostMapping("/provider/traffic/account/violation/getHostingLoginStatus")
    @RequestFunction(functionName = "获取托管登录状态")
    @ApiOperation("获取托管登录状态")
    public RestResponse<GetAccountHostingLoginStatusRespDTO> getHostingLoginStatus(@RequestBody GetAccountHostingLoginStatusReqDTO reqDTO) {
        AssetUtil.setProviderLoginInfo(reqDTO);
        return trafficAccountService.getHostingLoginStatus(reqDTO);
    }



}
