package com.izu.mrcar.providerController.asset.vehicle;

import com.google.common.collect.Maps;
import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.CarBrandDTO;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Map;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@Api(tags = "车辆品牌车型")
@RestController
public class ProviderCarBrandController {

	@GetMapping(MrCarAssetRestCenter.PROVIDER_CAR_BRAND_NAME_LIST)
	@RequestFunction(functionName = "品牌名称列表")
	@ApiOperation(value = "品牌名称列表",notes = "作者：贺新春")
	@ApiImplicitParam(name = "brandName",value = "品牌名称",paramType="form")
	public RestResponse<List<String>> carBrandNameList(String brandName){
		String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.PROVIDER_CAR_BRAND_NAME_LIST);
		Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
		paramMap.put("brandName", brandName);
		return RestClient.requestInside(BaseHttpClient.HttpMethod.GET, url, paramMap, null);
	}
	@GetMapping(MrCarAssetRestCenter.PROVIDER_CAR_BRAND_NAME_LIST_V2)
	@RequestFunction(functionName = "品牌名称列表")
	@ApiOperation(value = "品牌名称列表",notes = "作者：贺新春")
	@ApiImplicitParam(name = "brandName",value = "品牌名称",paramType="form")
	public RestResponse<List<CarBrandDTO>> carBrandNameListV2(String brandName){
		String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.PROVIDER_CAR_BRAND_NAME_LIST_V2);
		Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
		paramMap.put("brandName", brandName);
		return RestClient.requestInside(BaseHttpClient.HttpMethod.GET, url, paramMap, null);
	}
}
