package com.izu.mrcar.providerController.asset.lingsan;

import com.izu.config.MrCarConfigRestLocator;
import com.izu.config.dto.CityDicDTO;
import com.izu.consts.ConfigURI;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.controller.AbstractExcelUploadController;
import com.izu.mrcar.iot.dto.BatchDTO;
import com.izu.mrcar.utils.DateUtil;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.dto.company.CompanyNameRespDTO;
import com.izu.user.dto.company.lingsan.SupplierSimpleRespDTO;
import com.izu.user.dto.driver.DriverProviderLingSanSaveReqDTO;
import com.izu.user.dto.driver.SupplierDriverBatchImportDTO;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.enums.DriverSexEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 导入司机信息转换类
 * @date 2024/4/16 10:34
 */
@Slf4j
public class DriverInfoImportLingSanExcelComponent  extends AbstractExcelUploadController<DriverProviderLingSanSaveReqDTO, AbstractExcelUploadController.ExcelUploadResult> {

    private static UserRestLocator userRestLocator = new UserRestLocator();

    private SupplierDriverBatchImportDTO batchImportDTO;

    /**
     * 是否是客户端操作，这里决定如何获取供应商信息
     */
    private boolean isClient;

    public DriverInfoImportLingSanExcelComponent(SupplierDriverBatchImportDTO batchImportDTO,
                                              boolean isClient) {
        this.batchImportDTO = batchImportDTO;
        this.isClient = isClient;
    }

    public RestResponse dealExcel(HttpServletRequest request,
                                  HttpServletResponse response) {
        if (!isClient) {
            if (Objects.isNull(this.batchImportDTO.getProviderId()) || this.batchImportDTO.getProviderId() < 0) {
                return RestResponse.fail(RestErrorCode.HTTP_PARAM_INVALID, "请输入对应的供应商编码");
            }
            final List<CompanyNameRespDTO> supplierDic = getSupplierDic();
            final Map<Integer, CompanyNameRespDTO> supplierMap = supplierDic.stream().collect(Collectors.toMap(CompanyNameRespDTO::getCompanyId, Function.identity(), (e, r) -> e));
            final CompanyNameRespDTO supplierSimpleRespDTO = supplierMap.get(batchImportDTO.getProviderId());
            if (Objects.isNull(supplierSimpleRespDTO)) {
                return RestResponse.fail(RestErrorCode.HTTP_PARAM_INVALID, "对应的供应商不存在");
            }
        }
        ExcelUploadResult eur = new ExcelUploadResult();
        return this.start(batchImportDTO.getExcelUrl(), request, response, eur,11);
    }

    @Override
    protected List<Integer> getResolveSheetIndexes() {
        return Arrays.asList(1);
    }

    @Override
    protected String getColumnMappingConfigFile(HttpServletRequest request, HttpServletResponse response) {
        return null;
    }

    @Override
    protected List<DriverProviderLingSanSaveReqDTO> convertStringDataToObject(List<Map<Integer, String>> rowDatas, ExcelUploadResult excelUploadResult) {
        Integer providerId = null;
        AccountBaseInfo baseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
        if (isClient) {
            ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
            providerId = clientLoginInfo.getClientCompany().getCompanyId();
        } else {
            List<CompanyNameRespDTO> supplierDic = getSupplierDic();
            final Map<Integer, CompanyNameRespDTO> supplierMap = supplierDic.stream().collect(Collectors.toMap(CompanyNameRespDTO::getCompanyId, Function.identity(), (e, r) -> e));
            final CompanyNameRespDTO supplierSimpleRespDTO = supplierMap.get(batchImportDTO.getProviderId());
            if (Objects.nonNull(supplierSimpleRespDTO)) {
                providerId = supplierSimpleRespDTO.getCompanyId();
            }
        }

        List<Map<Integer, String>> collect = rowDatas.stream().filter(row -> !checkFieldAllNull(row)).collect(Collectors.toList());
        List<CityDicDTO> cityDicDTOList = new ArrayList<>();
        //所属城市
        RestResponse cityResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, new MrCarConfigRestLocator().getRestUrl(ConfigURI.DIC_GET_CITY_DIC_LIST), null, null, CityDicDTO.class);
        if(cityResponse!=null && cityResponse.isSuccess()){
            cityDicDTOList = (List<CityDicDTO>) cityResponse.getData();
        }
        List<DriverProviderLingSanSaveReqDTO> driverProviderLingSanSaveReqDTOList = new ArrayList<>();
        for (Map<Integer, String> row : collect) {
            DriverProviderLingSanSaveReqDTO dto = new DriverProviderLingSanSaveReqDTO();
            dto.setCompanyId(providerId);
            dto.setDriverName(row.get(0));
            dto.setGender(DriverSexEnum.getCodeByName(row.get(1)));
            dto.setGenderName(row.get(1));
            dto.setDriverMobile(row.get(2));
            String belongCityName = row.get(3);
            dto.setLicenseType(row.get(4));
            dto.setLicenseNo(row.get(5));
            dto.setCertNo(row.get(6));
            dto.setAddress(row.get(7));
            dto.setDriverArchivesNo(row.get(8));
            if(StringUtils.isNotBlank(row.get(9))){
                dto.setFirstPickupTime(DateUtil.formatExcelStr(row.get(9)));
            }
            if(StringUtils.isNotBlank(row.get(10))) {
                dto.setArriveTime(DateUtil.formatExcelStr(row.get(10)));
            }
            dto.setLoginUserId(baseInfo.getStaffId());
            dto.setLoginUserName(baseInfo.getStaffName());
            dto.setVehicleUsage(batchImportDTO.getVehicleUsage());
            if(StringUtils.isNotBlank(belongCityName)){
                dto.setBelongCityName(belongCityName);
                CityDicDTO cityDicDTO = cityDicDTOList.stream().filter(o->o.getCityName().equals(belongCityName)).findFirst().orElse(null);
                if(cityDicDTO!=null){
                    dto.setBelongCityCode(cityDicDTO.getCityCode().toString());
                }
            }
            driverProviderLingSanSaveReqDTOList.add(dto);
        }
        return driverProviderLingSanSaveReqDTOList;
    }

    @Override
    protected List<ExcelUploadRowError> checkExcelData(List<DriverProviderLingSanSaveReqDTO> rowDatas, HttpServletRequest request, HttpServletResponse response, ExcelUploadResult excelUploadResult) {
        // 参数校验放到assert服务中
        return null;
    }

    @Override
    protected boolean beforePersist(List<DriverProviderLingSanSaveReqDTO> rowDatas, HttpServletRequest request, HttpServletResponse response, ExcelUploadResult excelUploadResult) {
        return true;
    }

    @Override
    protected RestResponse executePersist(List<DriverProviderLingSanSaveReqDTO> rowDatas, HttpServletRequest request, HttpServletResponse response, ExcelUploadResult excelUploadResult) {
        batchImportDTO.setDriverList(rowDatas);
        Map<String, Object> paramMap = new HashMap<>(4);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, batchImportDTO);
        String restUrl = userRestLocator.getRestUrl(UserUrlCenter.PROVIDER_LING_SAN_DISPATCH_BATCH_INSERT);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, BatchDTO.class);
    }

    private boolean checkFieldAllNull(Object obj) {
        Field[] declaredFields = obj.getClass().getDeclaredFields();
        boolean flag = true;
        if(obj instanceof Map){
            Map<Integer,String> map = (Map) obj;
            for(Map.Entry entry : map.entrySet()){
                if (!ObjectUtils.isEmpty(entry.getValue())) {
                    flag = false;
                    break;
                }
            }
            return flag;
        }
        for (Field field : declaredFields) {
            field.setAccessible(true);
            Object o = null;
            try {
                o = field.get(obj);
            } catch (IllegalAccessException e) {
                log.error("校验对象属性值异常");
            }
            if (!ObjectUtils.isEmpty(o)) {
                flag = false;
                break;
            }
        }
        return flag;
    }


    private List<CompanyNameRespDTO> getSupplierDic(){
        List<CompanyNameRespDTO> supplierSimpleRespDTOList = new ArrayList<>();
        Map<String, Object> paraMap = new HashMap<>();
        paraMap.put("pageSize",1000);
        paraMap.put("companyStatus",2);
        paraMap.put("businessType","21,22");
        paraMap.put("companyAttribute",3);
        RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, userRestLocator.getRestUrl(UserUrlCenter.PROVIDER_COMPANY_SELECT_BY_BUSINESS_TYPE), paraMap, null, CompanyNameRespDTO.class);
        if (restResponse.isSuccess()) {
            supplierSimpleRespDTOList = (List<CompanyNameRespDTO>) restResponse.getData();
        }
        return supplierSimpleRespDTOList;
    }

}
