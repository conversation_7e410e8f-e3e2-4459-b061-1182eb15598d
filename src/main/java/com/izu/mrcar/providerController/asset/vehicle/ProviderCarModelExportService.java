package com.izu.mrcar.providerController.asset.vehicle;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.vehicle.CarModelExportDTO;
import com.izu.asset.dto.vehicle.CarModelListRespDTO;
import com.izu.asset.dto.violation.BrandModelListReqDTO;
import com.izu.excel.mail.AbstractIzuMailExcel;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.mrcar.common.errorcode.ErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class ProviderCarModelExportService implements AbstractIzuMailExcel<BrandModelListReqDTO, CarModelExportDTO> {

    @Override
    public PageDTO getDataByPage(Object params, int pageNum, int pageSize) {
        BrandModelListReqDTO reqDTO = (BrandModelListReqDTO) params;
        reqDTO.setPage(pageNum);
        reqDTO.setPageSize(pageSize);
        String restUrl = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.PROVIDER_CAR_MODEL_LIST_V3);
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        RestResponse result = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, CarModelListRespDTO.class);
        if (result.isSuccess()) {
            PageDTO pageDTO = (PageDTO) result.getData();
            List<CarModelListRespDTO> carModelListRespDTOList = (List<CarModelListRespDTO>) pageDTO.getResult();
            List<CarModelExportDTO> resultList = BeanUtil.copyList(carModelListRespDTOList, CarModelExportDTO.class);
            pageDTO.setResult(resultList);
            if (pageDTO.getTotal() > 10000) {
                throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
            }
            return pageDTO;
        } else {
            log.error("调用车型信息导出接口失败：{}", JSON.toJSONString(result));
            return null;
        }
    }
}
