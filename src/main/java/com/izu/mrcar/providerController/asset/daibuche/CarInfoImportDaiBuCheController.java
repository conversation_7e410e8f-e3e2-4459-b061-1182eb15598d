package com.izu.mrcar.providerController.asset.daibuche;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.style.row.SimpleRowHeightStyleStrategy;
import com.google.common.collect.Sets;
import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.dto.CarBrandDTO;
import com.izu.asset.dto.CarModelDTO;
import com.izu.asset.dto.vehicle.CoSupplierCarBatchImportDTO;
import com.izu.asset.dto.vehicle.SupplierCarBatchImportDTO;
import com.izu.config.MrCarConfigRestLocator;
import com.izu.config.dto.CityDicDTO;
import com.izu.consts.ConfigURI;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.controller.maintain.garage.CustomCellWriteHandler;
import com.izu.mrcar.controller.maintain.garage.DbCarExcelSheetWriteHandler;
import com.izu.mrcar.controller.maintain.garage.MaintainImportDTO;
import com.izu.mrcar.providerController.asset.lingsan.CarInfoImportLingSanExcelComponent;
import com.izu.mrcar.providerController.asset.lingsan.ProviderCarInfoImportDTO;
import com.izu.mrcar.service.asset.CarModelService;
import com.izu.mrcar.utils.excel.SelectDataSheetMergeWriteHandler;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
* @Description: 代步车运营端车辆导入
* @author: ljw
* @Date: 2024-5-24 10:15:46
**/
@RestController
@Api(tags = {"代步车-车辆管理"})
@Slf4j
public class CarInfoImportDaiBuCheController {

    @Autowired
    private CarModelService carModelService;

    @PostMapping(MrCarAssetRestCenter.CO_PROVIDER_CAR_BATCH_INSERT)
    @RequestFunction(functionName = "代步车-导入车辆信息")
    @ApiOperation("代步车-导入车辆信息")
    public RestResponse batchInsert(
            CoSupplierCarBatchImportDTO supplierCarBatchImportDTO,
            HttpServletRequest request,
            HttpServletResponse response) {
        final CarInfoImportDaiBuCheExcelComponent carInfoImportLingSanExcelComponent = new CarInfoImportDaiBuCheExcelComponent(supplierCarBatchImportDTO, carModelService, false);
        return carInfoImportLingSanExcelComponent.dealExcel(request, response);
    }

    @PostMapping(MrCarAssetRestCenter.CO_PROVIDER_CAR_UPLOAD_ATTACHMENT)
    @RequestFunction(functionName = "三方车辆-导入车辆模板下载")
    @ApiOperation("三方车辆-导入车辆模板下载")
    public void downloadImportTemplate(HttpServletResponse response) throws Exception {

        // 导出模板名称
        String fileName = "三方车辆导入模板";
        response.setContentType("application/msexcel");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码
        String fileNameEncode = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''"  + fileNameEncode + ExcelTypeEnum.XLSX.getValue());
        // 设置下拉框内容
        Map<Integer, List<String>> selectMap = buildSelectMap();
        //表头
        List<MaintainImportDTO> list = new ArrayList<>();
        EasyExcelFactory.write(response.getOutputStream())
                .registerWriteHandler(new CustomCellWriteHandler())
                .registerWriteHandler(new DbCarExcelSheetWriteHandler(Sets.newHashSet(6,7)))
                // 设置字典
                .registerWriteHandler(new SelectDataSheetMergeWriteHandler(selectMap))
                // 设置行高度
                .registerWriteHandler(new SimpleRowHeightStyleStrategy((short) 35, (short) 22))
                // 此处对应的是实体类
                .head(ProviderCarInfoImportDTO.class)
                // 设置导出格式为xls后缀
                .excelType(ExcelTypeEnum.XLSX)
                .sheet("导入车辆")
                .doWrite(list);
    }

    private Map<Integer, List<String>> buildSelectMap(){
        Map<Integer, List<String>> selectMap = new HashMap<>();
        List<CarBrandDTO> carBrandDTOS = carModelService.getCarBrandDicV2();
        List<String> brandList = carBrandDTOS.stream().map(CarBrandDTO::getBrandName).collect(Collectors.toList());
        selectMap.put(3, brandList);

        List<CarModelDTO> carModelDTOS = carModelService.getCarModelDicV2();
        List<String> modelList = carModelDTOS.stream().map(CarModelDTO::getModelName).collect(Collectors.toList());
        selectMap.put(4, modelList);

        RestResponse cityResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, new MrCarConfigRestLocator().getRestUrl(ConfigURI.DIC_GET_CITY_DIC_LIST), null, null, CityDicDTO.class);
        if(cityResponse!=null && cityResponse.isSuccess()){
            List<CityDicDTO> cityDicDTOS = (List<CityDicDTO>) cityResponse.getData();
            if(CollectionUtils.isNotEmpty(cityDicDTOS)){
                List<String> cityList = cityDicDTOS.stream().map(CityDicDTO::getCityName).collect(Collectors.toList());
                selectMap.put(5,cityList);
            }
        }
        return selectMap;
    }
}
