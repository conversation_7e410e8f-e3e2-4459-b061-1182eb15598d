package com.izu.mrcar.providerController.asset;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.net.HttpHeaders;
import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.govCar.VehicleEditWorkStatusReqDTO;
import com.izu.asset.dto.vehicle.*;
import com.izu.asset.dto.vehicle.allocation.*;
import com.izu.business.consts.resturl.MrcarBusinessRestMsgCenter;
import com.izu.business.dto.review.ReviewDTO;
import com.izu.business.dto.review.ReviewExportDTO;
import com.izu.config.dto.CityDicDTO;
import com.izu.excel.annotation.ExportExcel;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.iot.dto.LocationsDetailDTO;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.cache.RedisJsonCache;
import com.izu.mrcar.common.config.ExecutorConfig;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.controller.export.CarClientExport;
import com.izu.mrcar.controller.export.CarProviderExport;
import com.izu.mrcar.controller.export.CarProviderSqExport;
import com.izu.mrcar.dto.ListBillDetailDTO;
import com.izu.mrcar.dto.TrailExport;
import com.izu.mrcar.iot.config.IotCoreRestMsgCenter;
import com.izu.mrcar.iot.config.IotRestLocator;
import com.izu.mrcar.iot.dto.location.CarLocationTrailPageReqDTO;
import com.izu.mrcar.iot.dto.location.CarLocationTrailReqDTO;
import com.izu.mrcar.iot.dto.output.DeviceListDTO;
import com.izu.mrcar.iot.dto.video.DeviceHistoryTraceDTO;
import com.izu.mrcar.iot.iotEnum.SelfOwnedEnum;
import com.izu.mrcar.providerController.bi.BIDeptAnalysisController;
import com.izu.mrcar.service.carAsset.CarInfoExportClientService;
import com.izu.mrcar.service.carAsset.CarInfoExportProviderService;
import com.izu.mrcar.service.carAsset.CarInfoForBusinessExportProviderService;
import com.izu.mrcar.service.user.CompanyService;
import com.izu.mrcar.utils.DateTimeUtils;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.perm.VehicleListDataPermUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.dto.CompanyDepartmentDTO;
import com.izu.user.dto.DepartmentTrajectoryItemDTO;
import com.izu.user.dto.company.CompanyDTO;
import com.izu.user.dto.dept.DeptVehicleQueryReqDTO;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description: 运营端车辆列表
 * @author: hxc
 * @Date: 2023/1/31
 **/
@RestController("providerCarInfoController")
@Api(tags = "车辆管理")
@Slf4j
public class CarInfoController {
    private static final Logger logger = LoggerFactory.getLogger(CarInfoController.class);
    private final MrCarAssetRestLocator mrCarAssetRestLocator = new MrCarAssetRestLocator();


    private static final String CACHE_PREFIX_CAR_INFO_EXPROT = "mrcar_car_info_exprot_createexcelsendmail_frequency_control_";
    private static final int MAX_EXPORT_SIZE = 2000;
    private static final int CREATE_EXCEL_TO_MAIL_TIMEOUT_MINUTES = 8; //等待时长，平均耗时


    @Autowired
    private RedisJsonCache cache;

    @Autowired
    private CarInfoExportProviderService carInfoExportProviderService;

    @Autowired
    private CarInfoForBusinessExportProviderService carInfoForBusinessExportProviderService;


    @Resource
    private CompanyService companyService;

    /**
     * 运营端车辆列表
     *
     * @param param
     * @return
     */
    @PostMapping(MrCarAssetRestCenter.CAR_INFO_PROVIDER_GET_PAGE_LIST)
    @RequestFunction(functionName = "车辆列表-运营端")
    @ApiOperation(value = "车辆列表", notes = "作者：贺新春")
    public RestResponse<PageDTO<CarInfoOperationPageRespDTO>> getPageList(@RequestBody CarInfoOperationPageReqDTO param) {

        if (param.getPerm()) {
            VehicleListDataPermUtil.setProviderDataPerm(param);
            param.setIsDeviceList(true);
        }
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CAR_INFO_PROVIDER_GET_PAGE_LIST);
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, CarInfoOperationPageRespDTO.class);
    }

    /**
     * 运营端车辆详情
     *
     * @param vehicleId
     * @return
     */
    @GetMapping(MrCarAssetRestCenter.CAR_INFO_DETAIL_PROVIDER_GET)
    @RequestFunction(functionName = "车辆详情-运营端")
    @ApiImplicitParam(name = "vehicleId", value = "车辆ID", required = true, paramType = "form")
    @ApiOperation(value = "车辆详情", notes = "作者：贺新春")
    public RestResponse<CarInfoDetailDTO> getDetail(@Verify(param = "vehicleId", rule = "required") Long vehicleId) {

        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CAR_INFO_DETAIL_PROVIDER_GET);
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put("vehicleId", vehicleId);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.GET, url, paramMap, null);
    }


    /**
     * 运营端-获取车辆最后点位
     *
     * @param param
     * @return
     */
    @PostMapping(MrCarAssetRestCenter.CAR_INFO_PROVIDER_GET_VEHICLE_POINT_LIST)
    public RestResponse<List<CarInfoPointRespDTO>> getVehiclePointList(@Valid @RequestBody CarInfoPointReq param) {
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CAR_INFO_GET_VEHICLE_POINT_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }


    @RequestMapping(value = MrCarAssetRestCenter.CAR_INFO_EXPORT_PROVIDER)
    @RequestFunction(functionName = "车辆列表导出-运营端")
    @ApiOperation(value = "车辆列表导出-运营端（新改造）", notes = "作者：孟爽")
    @ExportExcelWeb(fileName = "车辆信息.xlsx", filePath = "/data/logs/excel/tmp", sheet = "车辆信息",
            c = CarProviderExport.class, isAsync = true)
    public PageDTO exportCarInfoProvider(@RequestBody CarInfoOperationPageReqDTO param,
                                         IzuEasyExcelSession izuEasyExcelSession,
                                         HttpServletRequest request, HttpServletResponse response) {

        validateExportParam(param);
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        izuEasyExcelSession.setUserName(providerLoginInfo.getBaseInfo().getStaffName());
        //分时租赁模块车辆列表导出
        Integer queryType = 1;
        if (Objects.equals(param.getQueryType(), queryType)) {
            return carInfoForBusinessExportProviderService.getDataByPage(param, izuEasyExcelSession.getPageNo(), 1000);
        } else {
            PageDTO dataByPage = carInfoExportProviderService.getDataByPage(param, izuEasyExcelSession.getPageNo(), 1000);
            if (dataByPage.getTotal() > MAX_EXPORT_SIZE) {
                throw ExceptionFactory.createRestException("导出数据超过" + MAX_EXPORT_SIZE + "条，请使用导出至邮箱");
            }
            return dataByPage;
        }
    }


    /**
     * 运营端-车辆列表导出到邮箱
     *
     * @param param
     * @return
     */
    @RequestMapping(value = MrCarAssetRestCenter.CAR_INFO_EXPORT_PROVIDER_MAIL)
    @RequestFunction(functionName = "车辆列表导出到邮件-运营端")
    @ApiOperation(value = "车辆列表导出到邮件-运营端", notes = "作者：孟爽")
    @ExportExcel(fileName = "车辆信息.xlsx", filePath = "/data/logs/mrcar-business-core/tmp", sheet = "车辆信息")
    public RestResponse exportProviderToEmail(@RequestBody CarInfoOperationPageReqDTO param) {
        // 1. 基础校验
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        if (Objects.isNull(loginBaseInfo) || StringUtils.isBlank(param.getEmail())) {
            return RestResponse.create(RestErrorCode.UNKNOWN_ERROR, "邮箱号不能为空", false, null);
        }
        validateExportParam(param);

        // 2. 频率控制
        String cacheKey = CACHE_PREFIX_CAR_INFO_EXPROT + loginBaseInfo.obtainBaseInfo().getStaffId();
        final String cacheValue = cache.get(cacheKey, String.class);
        if (cacheValue != null) {
            return RestResponse.create(RestErrorCode.UNKNOWN_ERROR, String.format("操作过于频繁，请%d分钟后再试", CREATE_EXCEL_TO_MAIL_TIMEOUT_MINUTES), false, null);
        }
        String staffName = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo().getStaffName();
        cache.set(cacheKey, "LOCK", CREATE_EXCEL_TO_MAIL_TIMEOUT_MINUTES * 2 * 10);
        long startTime = System.currentTimeMillis();
        CompletableFuture.runAsync(() ->
                        carInfoExportProviderService.exportToEmail(param, staffName))
                .whenComplete((unused, throwable) -> {
                    log.info("运营端导出数据耗时：{}", (System.currentTimeMillis() - startTime) / 1000 + "秒");
                    cache.delete(cacheKey);
                    if (throwable != null) {
                        log.error("车辆信息列表导出至邮箱异常", throwable);
                    }
                });
        return RestResponse.create(0, "提交成功！请稍后查收电子邮件。（数据处理大约耗时" + CREATE_EXCEL_TO_MAIL_TIMEOUT_MINUTES + "分钟）", true, null);
    }


    private void validateExportParam(CarInfoOperationPageReqDTO param) {
        VehicleListDataPermUtil.setProviderDataPerm(param);
        param.setIsExport(true);
    }



    @PostMapping(MrCarAssetRestCenter.CAR_UPDATE_PROVIDER_FOR_ASSIGN_CAR)
    @RequestFunction(functionName = "车辆分配")
    @ApiOperation(value = "车辆分配", notes = "作者：贺新春")
    public RestResponse updateForAssignCar(@RequestBody CarInfoAssignReqDTO param) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        param.setLoginUserId(providerLoginInfo.obtainBaseInfo().getStaffId());
        param.setLoginUserName(providerLoginInfo.obtainBaseInfo().getStaffName());
        param.setLoginCompanyId(providerLoginInfo.obtainBelongCompanyId());
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CAR_UPDATE_PROVIDER_FOR_ASSIGN_CAR);
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(MrCarAssetRestCenter.CAR_UPDATE_PROVIDER_FOR_RETRIEVE_CAR)
    @RequestFunction(functionName = "车辆收回")
    @ApiOperation(value = "车辆收回", notes = "作者：贺新春")
    public RestResponse updateForRetrieveCar(@RequestBody CarInfoRetrieveReqDTO param) {

        AccountBaseInfo baseInfo = LoginSessionUtil.getProviderLoginInfo().getBaseInfo();
        param.setLoginUserId(baseInfo.getStaffId());
        param.setLoginUserName(baseInfo.getStaffName());
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CAR_UPDATE_PROVIDER_FOR_RETRIEVE_CAR);
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(MrCarAssetRestCenter.CAR_UPDATE_PROVIDER)
    @RequestFunction(functionName = "车辆编辑-运营端")
    @ApiOperation(value = "车辆编辑", notes = "作者：贺新春")
    public RestResponse<Boolean> updateCarInfo(@Valid @RequestBody CarInfoUpdateReqDTO param) {

        AccountBaseInfo baseInfo = LoginSessionUtil.getProviderLoginInfo().getBaseInfo();
        param.setLoginUserId(baseInfo.getStaffId());
        param.setLoginUserName(baseInfo.getStaffName());
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CAR_UPDATE_PROVIDER);
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(MrCarAssetRestCenter.CAR_TO_SELL_PROVIDER)
    @RequestFunction(functionName = "车辆转待售")
    @ApiImplicitParam(name = "vehicleId", value = "车辆ID", required = true, paramType = "form")
    @ApiOperation(value = "车辆转待售", notes = "作者：贺新春")
    public RestResponse<Boolean> carToSell(@Verify(param = "vehicleId", rule = "required") Long vehicleId) {

        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CAR_TO_SELL_PROVIDER);
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put("vehicleId", vehicleId);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.GET, url, paramMap, null);
    }

    /**
     * 运营端车辆定位轨迹
     *
     * @param carLocationTrailReqDTO
     * @return
     */
    @PostMapping(IotCoreRestMsgCenter.CAR_LOCATION_TRAIL)
    @RequestFunction(functionName = "车辆定位轨迹-运营端")
    @ApiOperation(value = "车辆定位轨迹", notes = "作者：jhmi")
    @Deprecated
    public RestResponse getCarLocationTrail(@RequestBody CarLocationTrailReqDTO carLocationTrailReqDTO) {
        String url = new IotRestLocator().getRestUrl("/deviceBind/getCarLocationTrail");
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, carLocationTrailReqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    //历史视频文件列表
    @PostMapping(IotCoreRestMsgCenter.PROVIDER_VIDEO_HISTORY_TRACE)
    @RequestFunction(functionName = "新车辆定位轨迹-运营端")
    @ApiOperation("历史轨迹接口")
    public RestResponse<DeviceHistoryTraceDTO> getHistoryTrace(@RequestBody CarLocationTrailReqDTO carLocationTrailReqDTO) {
        Map<String, Object> httpParams = new HashMap<>();
        httpParams.put(BaseHttpClient.POSTBODY_MAP_KEY, carLocationTrailReqDTO);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, new IotRestLocator().getRestUrl(IotCoreRestMsgCenter.VIDEO_HISTORY_TRACE), httpParams, null, DeviceHistoryTraceDTO.class);
    }

    /**
     * 运营端车辆定位轨迹分页查询
     *
     * @param carLocationTrailPageReqDTO
     * @return
     */
    @PostMapping(IotCoreRestMsgCenter.CAR_LOCATION_TRAIL_PAGE)
    @RequestFunction(functionName = "车辆定位轨迹分页-运营端")
    @ApiOperation(value = "车辆定位轨迹分页查询", notes = "作者：jhmi")
    public RestResponse getCarLocationTrailPage(@RequestBody CarLocationTrailPageReqDTO carLocationTrailPageReqDTO) {
        String url = new IotRestLocator().getRestUrl(IotCoreRestMsgCenter.CAR_LOCATION_TRAIL_PAGE);
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, carLocationTrailPageReqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    /**
     * 运营端车辆设备 历史绑定记录和解绑记录
     *
     * @param vehicleId
     * @return
     */
    @PostMapping(IotCoreRestMsgCenter.LIST_ALL_BIND_HISTORY)
    @RequestFunction(functionName = "设备绑定解绑记录-运营端")
    @ApiOperation(value = "设备绑定解绑记录", notes = "作者：jhmi")
    @ApiImplicitParam(name = "vehicleId", value = "车辆ID", required = true, paramType = "form")
    public RestResponse<List<DeviceListDTO>> getDeviceListHistory(@Verify(param = "vehicleId", rule = "required") Integer vehicleId) {
        String url = new IotRestLocator().getRestUrl(IotCoreRestMsgCenter.LIST_ALL_BIND_HISTORY);
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put("vehicleId", vehicleId);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.GET, url, paramMap, null);
    }

    /**
     * 运营端车辆设备按照日期查询绑定的设备
     *
     * @param vehicleId
     * @return
     */
    @PostMapping(IotCoreRestMsgCenter.LIST_BIND_RECORD_BY_DATE)
    @RequestFunction(functionName = "设备绑定记录-运营端")
    @ApiOperation(value = "设备历史绑定记录", notes = "作者：jhmi")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vehicleId", value = "车辆ID", required = true, paramType = "form"),
            @ApiImplicitParam(name = "startDate", value = "开始日期", required = true, paramType = "form", example = "2022-11-22 11:22:22"),
            @ApiImplicitParam(name = "endDate", value = "结束日期", required = true, paramType = "form", example = "2022-11-22 11:22:22")
    })
    public RestResponse<List<DeviceListDTO>> getDeviceListDate(@Verify(param = "vehicleId", rule = "required") Integer vehicleId,
                                                               @Verify(param = "startDate", rule = "required") String startDate,
                                                               @Verify(param = "endDate", rule = "required") String endDate) {
        String url = new IotRestLocator().getRestUrl(IotCoreRestMsgCenter.LIST_BIND_RECORD_BY_DATE);
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(3);
        paramMap.put("vehicleId", vehicleId);
        paramMap.put("startDate", startDate);
        paramMap.put("endDate", endDate);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.GET, url, paramMap, null);
    }

    /**
     * 运营端车辆实时定位
     *
     * @param vehicleId
     * @return
     */
    @PostMapping(IotCoreRestMsgCenter.CAR_LOCATION_REALTIME)
    @RequestFunction(functionName = "车辆实时定位-运营端")
    @ApiOperation(value = "车辆实时定位", notes = "作者：jhmi")
    @ApiImplicitParam(name = "vehicleId", value = "车辆ID", required = true, paramType = "form")
    public RestResponse<List<DeviceListDTO>> getDeviceListDate(@Verify(param = "vehicleId", rule = "required") Integer vehicleId) {
        String url = new IotRestLocator().getRestUrl(IotCoreRestMsgCenter.CAR_LOCATION_REALTIME);
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put("vehicleId", vehicleId);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.GET, url, paramMap, null);
    }

    /**
     * 车辆全部定位数据导出
     *
     * @param response
     * @param carLocationTrailPageReqDTO
     */
    @PostMapping(value = IotCoreRestMsgCenter.CAR_LOCATION_TRAIL_EXPORT)
    @RequestFunction(functionName = "车辆定位导出-运营端")
    @ApiOperation(value = "车辆定位导出", notes = "作者：jhmi")
    @ApiImplicitParam(name = "vehicleId", value = "车辆ID", required = true, paramType = "form")
    public void trailDownload(HttpServletResponse response, CarLocationTrailPageReqDTO carLocationTrailPageReqDTO) {
        Integer pageNo = 1;
        Integer pageSize = 1000;
        List<LocationsDetailDTO> list = new ArrayList<>();
        carLocationTrailPageReqDTO.setPageSize(pageSize);
        while (true) {
            carLocationTrailPageReqDTO.setPageNo(pageNo);
            List<LocationsDetailDTO> trail = getTrail(carLocationTrailPageReqDTO);
            if (CollectionUtils.isEmpty(trail)) {
                break;
            }
            list.addAll(trail);
            pageNo++;
        }
        String fileName = "车辆行驶轨迹导出";
        Workbook workbook;
        if (list == null || list.isEmpty()) {
            workbook = ExcelExportUtil.exportExcel(new ExportParams(), TrailExport.class, Collections.emptyList());
        } else {
            List<TrailExport> trailExports = new ArrayList<>();
            for (LocationsDetailDTO detailDTO : list) {
                trailExports.add(BeanUtil.copyObject(detailDTO, TrailExport.class));
            }
            workbook = ExcelExportUtil.exportExcel(new ExportParams(), TrailExport.class, trailExports);
        }
        try (OutputStream outputStream = response.getOutputStream()) {
            String time = DateTimeUtils.getCurrentDate(DateTimeUtils.FILE_DATE_TIME_FORMAT);
            response.setContentType("application/vnd.ms-excel; charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename*=utf-8''" + URLEncoder.encode(fileName + time + ".xls", "UTF-8"));
            workbook.write(outputStream);
        } catch (Exception e) {
            logger.error("导出车辆行驶轨迹Excel异常");
        }
    }


    @PostMapping(MrCarAssetRestCenter.CAR_INFO_QUERY_BELONG_CITY_NAMES)
    @ApiOperation(value = "查询企业的所有车辆所在城市")
    public RestResponse<List<CityDicDTO>> queryBelongCityNames() {
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CAR_INFO_QUERY_BELONG_CITY_NAMES);
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put("companyId", LoginSessionUtil.getBaseLoginInfo().obtainBelongCompanyId());
        return RestClient.requestForList(BaseHttpClient.HttpMethod.GET, url, paramMap, null, CityDicDTO.class);
    }


    public static List<LocationsDetailDTO> getTrail(CarLocationTrailPageReqDTO carLocationTrailPageReqDTO) {
        String url = new IotRestLocator().getRestUrl(IotCoreRestMsgCenter.CAR_LOCATION_TRAIL_PAGE);
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, carLocationTrailPageReqDTO);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, LocationsDetailDTO.class);
        if (restResponse.isSuccess() || restResponse.getData() != null) {
            PageDTO pageDTO = (PageDTO) restResponse.getData();
            if (null == pageDTO.getResult()) {
                return null;
            }
            List<LocationsDetailDTO> list = (List<LocationsDetailDTO>) pageDTO.getResult();
            return list;
        }
        return null;
    }

    @PostMapping("/car/getVehicleForParams")
    @RequestFunction(functionName = "根据公司id获取公务用车类型的车辆-运营端")
    @ApiOperation(value = "根据公司id获取公务用车类型的车辆", notes = "作者：丁伟兵")
    public RestResponse<List<SearchVehicleByParamRespDTO>> getVehicleForParams(@RequestBody SearchVehicleByParamReqDTO searchVehicleByParamReqDTO) {
        String url = mrCarAssetRestLocator.getRestUrl("/car/getVehicleForParams");
        Map<String, Object> paramMap = new HashMap<>();
        List<Integer> structIds = searchVehicleByParamReqDTO.getStructIds();
        if (CollectionUtils.isNotEmpty(structIds)) {
            String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.DEPARTMENT_LISTDEPTIDBYJUDGELEVEL);
            HashMap<String, Object> hashMap = new HashMap<>();
            hashMap.put("deptId", structIds.get(0));
            RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, hashMap, null, Integer.class);
            if (!restResponse.isSuccess()) {
                log.error("查询部门失败 deptId = {}", structIds);
            }
            searchVehicleByParamReqDTO.setStructIds((List<Integer>) restResponse.getData());
        }
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, searchVehicleByParamReqDTO);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, SearchVehicleByParamRespDTO.class);
    }

    @GetMapping("/car/selectOfficialCarModel")
    @RequestFunction(functionName = "根据公司id获取公务用车可选车型列表-运营端")
    @ApiOperation(value = "根据公司id获取公务用车可选车型列表", notes = "作者：丁伟兵")
    public RestResponse<List<OfficialCarModel>> selectOfficialCarModel(@ApiParam(value = "企业id", required = true) @RequestParam @Verify(param = "companyId", rule = "required") Integer companyId,
                                                                       @ApiParam(value = "车辆用途 6：公务用车", required = false) @RequestParam(value = "vehicleUsage") Byte vehicleUsage) {
        String url = mrCarAssetRestLocator.getRestUrl("/car/selectOfficialCarModel");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("companyId", companyId);
        paramMap.put("vehicleUsage", vehicleUsage);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.GET, url, paramMap, null, OfficialCarModel.class);
    }

    @ApiOperation(value = "车辆调拨-运营端", notes = "作者：牛子联")
    @RequestFunction(functionName = "车辆调拨-运营端")
    @PostMapping(MrCarAssetRestCenter.CAR_TRANSFER_PROVIDER)
    public RestResponse<Boolean> carAllocation(@RequestBody VehicleAllocationReqDTO reqDTO) {
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CAR_TRANSFER_PROVIDER);
        final LoginBaseInfo loginInfo = LoginSessionUtil.getBaseLoginInfo();
        reqDTO.setLoginUserId(loginInfo.obtainBaseInfo().getStaffId());
        reqDTO.setLoginUserCode(loginInfo.obtainBaseInfo().getStaffCode());
        reqDTO.setLoginUserName(loginInfo.obtainBaseInfo().getStaffName());
        HashMap<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, restParam, null);
    }


    @ApiOperation(value = "车辆调拨列表-运营端", notes = "作者：牛子联")
    @RequestFunction(functionName = "车辆调拨列表-运营端")
    @PostMapping(MrCarAssetRestCenter.ALLOCATION_PAGE_LIST)
    public RestResponse<PageDTO<AllocationPageRespDTO>> vehicleAllocationPage(@RequestBody AllocationPageReqDTO reqDTO) {
        VehicleListDataPermUtil.setProviderDataPerm(reqDTO);
        HashMap<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        String restUrl = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.ALLOCATION_PAGE_LIST);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }


    @ApiOperation(value = "车辆调拨可选择的车辆列表-运营端", notes = "作者：牛子联")
    @RequestFunction(functionName = "车辆调拨可选择的车辆列表-运营端")
    @PostMapping(MrCarAssetRestCenter.FIND_VEHICLE_FOR_ALLOCATION)
    public RestResponse<PageDTO<VehicleListForAllocationRespDTO>> findVehicleForAllocation(@RequestBody VehicleListForAllocationReqDTO reqDTO) {
        HashMap<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        String restUrl = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.FIND_VEHICLE_FOR_ALLOCATION);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }


    @ApiOperation(value = "车辆调拨详情-运营端", notes = "作者：牛子联")
    @RequestFunction(functionName = "车辆调拨详情-运营端")
    @PostMapping(MrCarAssetRestCenter.ALLOCATION_DETAIL)
    public RestResponse<AllocationDetailPageRespDTO> allocationDetail(@Verify(param = "allocationId",
            rule = "required") Integer allocationId) {
        HashMap<String, Object> restParam = new HashMap<>();
        restParam.put("allocationId", allocationId);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST,
                mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.ALLOCATION_DETAIL), restParam, null);
    }


    //运营端 上下架接口
    @ApiOperation(value = "车辆上下架接口")
    @PostMapping(MrCarAssetRestCenter.PROVIDER_EDIT_VEHICLE_WORKINGSTATUS)
    @RequestFunction(functionName = "车辆上下架-运营端")
    public RestResponse<Boolean> editVehicleWorkStatus(@RequestBody VehicleEditWorkStatusReqDTO vehicleEditWorkStatusReqDTO) {
        final LoginBaseInfo loginInfo = LoginSessionUtil.getBaseLoginInfo();
        vehicleEditWorkStatusReqDTO.setLoginUserId(loginInfo.obtainBaseInfo().getStaffId());
        vehicleEditWorkStatusReqDTO.setLoginUserCode(loginInfo.obtainBaseInfo().getStaffCode());
        vehicleEditWorkStatusReqDTO.setLoginUserName(loginInfo.obtainBaseInfo().getStaffName());
        HashMap<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, vehicleEditWorkStatusReqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY,
                mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.PROVIDER_EDIT_VEHICLE_WORKINGSTATUS), restParam, null);
    }


    @PostMapping("/car/carInfo/provider/listBillDetail")
    @RequestFunction(functionName = "账单-车辆列表")
    @ApiOperation(value = "账单-车辆列表")
    public RestResponse<PageDTO<CarInfoOperationPageRespDTO>> listBillDetail(@RequestBody ListBillDetailDTO dto) {

        CarInfoOperationPageReqDTO param = new CarInfoOperationPageReqDTO();
        CompanyDTO companyDTO = companyService.getByCustomerCode(dto.getCompanyCode());
        if (Objects.isNull(companyDTO)) {
            return RestResponse.success(new PageDTO<CarInfoOperationPageRespDTO>(dto.getPage(), dto.getPageSize(), 0, Lists.newArrayList()));
        }
        param.setCompanyId(companyDTO.getCompanyId());
        param.setOwnedType(Lists.newArrayList(SelfOwnedEnum.CUSTOMER_CAR.getType()));
        param.setQryEndCreateTime(dto.getEndDate());
        if (Objects.nonNull(dto.getConfigDate())) {
            param.setQryEndCreateTime(dto.getEndDate());
        }
        // 后续增加 配置时间查询 TODO
        /*if(param.getPerm()){
            VehicleListDataPermUtil.setProviderDataPerm(param);
            param.setIsDeviceList(true);
        }*/
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CAR_INFO_PROVIDER_GET_PAGE_LIST);
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, CarInfoOperationPageRespDTO.class);
    }

    /**
     * 详情搜索 车牌
     *
     * @param param
     * @return
     */
    @PostMapping(MrCarAssetRestCenter.CAR_INFO_PROVIDER_GET_VEHICLE_LICENSE_LIST)
    public RestResponse<PageDTO<VehicleLicenseRespDTO>> searchVehicleLicenseList(@RequestBody CarInfoOperationPageReqDTO param) {
        if (param.getPerm()) {
            VehicleListDataPermUtil.setProviderDataPerm(param);
        }
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CAR_INFO_PROVIDER_GET_VEHICLE_LICENSE_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, VehicleLicenseRespDTO.class);
    }

    /**
     * 单独更新车辆里程
     *
     * @param param
     * @return
     */
    @PostMapping(MrCarAssetRestCenter.CAR_INFO_PROVIDER_UPDATE_VEHICLE_MILES)
    public RestResponse updateVehicleMiles(@Valid @RequestBody VehicleMilesUpdateReqDTO param) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        param.setLoginUserId(providerLoginInfo.obtainBaseInfo().getStaffId());
        param.setLoginUserName(providerLoginInfo.obtainBaseInfo().getStaffName());
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CAR_INFO_CLIENT_UPDATE_VEHICLE_MILES);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @RequestFunction(functionName = "轨迹染色-点击部门查看车辆")
    @ApiOperation(value = "轨迹染色-点击部门查看车辆")
    @PostMapping(MrCarAssetRestCenter.PROVIDER_DEPARTMENT_VEHICLE_LIST)
    public RestResponse<List<DepartmentTrajectoryItemDTO>> getDeptVehicleList(@RequestBody DeptVehicleQueryReqDTO deptVehicleQueryReqDTO) {
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CLIENT_DEPARTMENT_VEHICLE_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, deptVehicleQueryReqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }


}
