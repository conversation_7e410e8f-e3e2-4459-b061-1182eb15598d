package com.izu.mrcar.providerController.businesscompany;

import com.izu.config.MrCarConfigRestLocator;
import com.izu.config.dto.business.*;
import com.izu.consts.ConfigURI;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@Api(tags = "商务车配置")
public class BusinessDispatcherConfigController {

    @PostMapping(ConfigURI.BUSINESS_DISPATER_CONFIG_LIST_INFO)
    @RequestFunction(functionName = "调度员配置列表-运营端")
    @ApiOperation(value = "调度员配置列表（运营端）",notes = "作者：mapp")
    public RestResponse<PageDTO<BusinessDispatcherConfigListDTO>> getList(@RequestBody BusinessDispatcherConfigInputDTO param){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        param.setLoginUserId(providerLoginInfo.obtainBaseInfo().getStaffId());
        param.setLoginUserName(providerLoginInfo.obtainBaseInfo().getStaffName());
        String url = new MrCarConfigRestLocator().getRestUrl(ConfigURI.BUSINESS_DISPATER_CONFIG_LIST_INFO);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, BusinessDispatcherConfigListDTO.class);
    }

    @PostMapping(ConfigURI.BUSINESS_DISPATER_CONFIG_DETAIL)
    @RequestFunction(functionName = "调度员配置详情-运营端")
    @ApiOperation(value = "调度员配置详情（运营端）",notes = "作者：mapp")
    @ApiImplicitParam(name = "id", value = "主鍵id",required = true, paramType = "form")
    public RestResponse<BusinessDispatcherConfigDetailDTO> getDetail(Integer id){
        String url = new MrCarConfigRestLocator().getRestUrl(ConfigURI.BUSINESS_DISPATER_CONFIG_DETAIL);
        Map<String,Object> param = new HashMap<>();
        param.put("id",id);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, url, param, null, BusinessDispatcherConfigDetailDTO.class);
    }

    @PostMapping(ConfigURI.BUSINESS_DISPATER_CONFIG_SAVE)
    @RequestFunction(functionName = "调度员配置编辑-运营端")
    @ApiOperation(value = "调度员配置编辑（运营端）",notes = "作者：mapp")
    public RestResponse<Boolean> save(@RequestBody BusinessDispatcherConfigSaveDTO param){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        param.setLoginUserId(providerLoginInfo.obtainBaseInfo().getStaffId());
        param.setLoginUserName(providerLoginInfo.obtainBaseInfo().getStaffName());
        String url = new MrCarConfigRestLocator().getRestUrl(ConfigURI.BUSINESS_DISPATER_CONFIG_SAVE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, Boolean.class);
    }

    @PostMapping(ConfigURI.BUSINESS_DISPATER_CONFIG_DETELE)
    @RequestFunction(functionName = "调度员配置删除-运营端")
    @ApiOperation(value = "调度员配置删除（运营端）",notes = "作者：mapp")
    @ApiImplicitParam(name = "id", value = "主鍵id",required = true, paramType = "form")
    public RestResponse<Boolean> delete(Integer id){
        String url = new MrCarConfigRestLocator().getRestUrl(ConfigURI.BUSINESS_DISPATER_CONFIG_DETELE);
        Map<String,Object> param = new HashMap<>();
        param.put("id",id);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, url, param, null, Boolean.class);
    }

}
