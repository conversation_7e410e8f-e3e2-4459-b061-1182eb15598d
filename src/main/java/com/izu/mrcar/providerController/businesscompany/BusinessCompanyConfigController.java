package com.izu.mrcar.providerController.businesscompany;

import com.izu.config.MrCarConfigRestLocator;
import com.izu.config.dto.business.BusinessCompanyConfigDetailDTO;
import com.izu.config.dto.business.BusinessCompanyConfigInputDTO;
import com.izu.config.dto.business.BusinessCompanyConfigListDTO;
import com.izu.config.dto.business.BusinessCompanyConfigSaveDTO;
import com.izu.consts.ConfigURI;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@Api(tags = "商务车配置")
public class BusinessCompanyConfigController {

    @PostMapping(ConfigURI.BUSINESS_COMPANY_CONFIG_LIST_INFO)
    @RequestFunction(functionName = "商务车配置流程列表-运营端")
    @ApiOperation(value = "商务车配置流程列表（运营端）",notes = "作者：mapp")
    public RestResponse<PageDTO<BusinessCompanyConfigListDTO>> getList(@RequestBody BusinessCompanyConfigInputDTO param){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        param.setLoginUserId(providerLoginInfo.obtainBaseInfo().getStaffId());
        param.setLoginUserName(providerLoginInfo.obtainBaseInfo().getStaffName());
        String url = new MrCarConfigRestLocator().getRestUrl(ConfigURI.BUSINESS_COMPANY_CONFIG_LIST_INFO);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, BusinessCompanyConfigListDTO.class);
    }

    @PostMapping(ConfigURI.BUSINESS_COMPANY_CONFIG_DETAIL)
    @RequestFunction(functionName = "商务车配置流程详情-运营端")
    @ApiOperation(value = "商务车配置流程详情（运营端）",notes = "作者：mapp")
    @ApiImplicitParams(
            {@ApiImplicitParam(name = "id",value = "主键id", paramType = "form"),
            @ApiImplicitParam(name = "ruleType", value = "流程类型（0 通用配置 1 客户配置）",required = true, paramType = "form")
    })
    public RestResponse<BusinessCompanyConfigDetailDTO> getDetail(Integer id, Byte ruleType){
        String url = new MrCarConfigRestLocator().getRestUrl(ConfigURI.BUSINESS_COMPANY_CONFIG_DETAIL);
        Map<String,Object> param = new HashMap<>();
        param.put("id",id);
        param.put("ruleType",ruleType);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, url, param, null, BusinessCompanyConfigDetailDTO.class);
    }

    @PostMapping(ConfigURI.BUSINESS_COMPANY_CONFIG_SAVE)
    @RequestFunction(functionName = "商务车配置流程编辑-运营端")
    @ApiOperation(value = "商务车配置流程编辑（运营端）",notes = "作者：mapp")
    public RestResponse<Boolean> save(@RequestBody @Validated BusinessCompanyConfigSaveDTO param){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        param.setLoginUserId(providerLoginInfo.obtainBaseInfo().getStaffId());
        param.setLoginUserName(providerLoginInfo.obtainBaseInfo().getStaffName());
        String url = new MrCarConfigRestLocator().getRestUrl(ConfigURI.BUSINESS_COMPANY_CONFIG_SAVE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, Boolean.class);
    }

}
