package com.izu.mrcar.providerController.businesscompany;

import com.izu.config.MrCarConfigRestLocator;
import com.izu.config.dto.business.*;
import com.izu.consts.ConfigURI;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@Api(tags = "商务车配置")
public class BusinessPriceConfigController {

    @PostMapping(ConfigURI.BUSINESS_PRICE_CONFIG_LIST_INFO)
    @RequestFunction(functionName = "客户、供应商价格配置列表-运营端")
    @ApiOperation(value = "客户、供应商配置列表-运营端",notes = "作者：chenxi3")
    public RestResponse<PageDTO<BusinessPriceConfigListDTO>> getList(@RequestBody BusinessPriceConfigInputDTO param){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        param.setLoginUserId(providerLoginInfo.obtainBaseInfo().getStaffId());
        param.setLoginUserName(providerLoginInfo.obtainBaseInfo().getStaffName());
        String url = new MrCarConfigRestLocator().getRestUrl(ConfigURI.BUSINESS_PRICE_CONFIG_LIST_INFO);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, BusinessPriceConfigListDTO.class);
    }

    @PostMapping(ConfigURI.BUSINESS_PRICE_CONFIG_DETAIL)
    @RequestFunction(functionName = "客户、供应商价格详情-运营端")
    @ApiOperation(value = "客户、供应商价格详情（运营端）",notes = "作者：chenxi3")
    @ApiImplicitParam(name = "id", value = "主鍵id",required = true, paramType = "form")
    public RestResponse<BusinessPriceConfigDetailDTO> getDetail(Integer id){
        String url = new MrCarConfigRestLocator().getRestUrl(ConfigURI.BUSINESS_PRICE_CONFIG_DETAIL);
        Map<String,Object> param = new HashMap<>();
        param.put("id",id);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, url, param, null, BusinessPriceConfigDetailDTO.class);
    }

    @PostMapping(ConfigURI.BUSINESS_PRICE_CONFIG_CONFIGURATION)
    @RequestFunction(functionName = "客户、供应商价格相关枚举类-运营端")
    @ApiOperation(value = "客户、供应商价格相关枚举类（运营端）",notes = "作者：chenxi3")
    public RestResponse<BusinessPriceConfigConfigurationDTO> getConfiguration(){
        String url = new MrCarConfigRestLocator().getRestUrl(ConfigURI.BUSINESS_PRICE_CONFIG_CONFIGURATION);
        Map<String,Object> param = new HashMap<>();
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, url, param, null, BusinessPriceConfigConfigurationDTO.class);
    }

    @PostMapping(ConfigURI.BUSINESS_PRICE_CONFIG_SAVE)
    @RequestFunction(functionName = "客户、供应商价格编辑-运营端")
    @ApiOperation(value = "客户、供应商价格编辑（运营端）",notes = "作者：chenxi3")
    public RestResponse<Boolean> save(@Validated @RequestBody BusinessPriceConfigSaveDTO param){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        param.setLoginUserId(providerLoginInfo.obtainBaseInfo().getStaffId());
        param.setLoginUserName(providerLoginInfo.obtainBaseInfo().getStaffName());
        param.setMobile(providerLoginInfo.obtainBaseInfo().getMobile());
        String url = new MrCarConfigRestLocator().getRestUrl(ConfigURI.BUSINESS_PRICE_CONFIG_SAVE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, Boolean.class);
    }

    @PostMapping(ConfigURI.BUSINESS_PRICE_CONFIG_CHANGE_STATUS)
    @RequestFunction(functionName = "客户、供应商价格上下架-运营端")
    @ApiOperation(value = "客户、供应商价格上下架-运营端",notes = "作者：chenxi3")
    public RestResponse<Boolean> changeStatus(@RequestBody BusinessPriceConfigStatusChangeDTO param){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        param.setLoginUserId(providerLoginInfo.obtainBaseInfo().getStaffId());
        param.setLoginUserName(providerLoginInfo.obtainBaseInfo().getStaffName());
        param.setMobile(providerLoginInfo.obtainBaseInfo().getMobile());
        String url = new MrCarConfigRestLocator().getRestUrl(ConfigURI.BUSINESS_PRICE_CONFIG_CHANGE_STATUS);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);;
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, Boolean.class);
    }

    @PostMapping(ConfigURI.BUSINESS_PRICE_CONFIG_OPERATION_LOG)
    @RequestFunction(functionName = "客户、供应商价格操作日志列表-运营端")
    @ApiOperation(value = "客户、供应商价格操作日志列表-运营端",notes = "作者：chenxi3")
    public RestResponse<PageDTO<BusinessPriceOperationLogListDTO>> getOperationLog(@RequestBody BusinessPriceConfigLogInputDTO param){
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        param.setLoginUserId(providerLoginInfo.obtainBaseInfo().getStaffId());
        param.setLoginUserName(providerLoginInfo.obtainBaseInfo().getStaffName());
        String url = new MrCarConfigRestLocator().getRestUrl(ConfigURI.BUSINESS_PRICE_CONFIG_OPERATION_LOG);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, BusinessPriceOperationLogListDTO.class);
    }
}
