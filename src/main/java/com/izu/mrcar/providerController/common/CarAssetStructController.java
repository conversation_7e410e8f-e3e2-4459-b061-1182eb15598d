package com.izu.mrcar.providerController.common;

import com.izu.carasset.CarAssetRestLocator;
import com.izu.carasset.dto.output.StructAssetDTO;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.constants.MrcarUrl;
import com.izu.mrcar.dto.CarAssetStructDropdownRespDTO;
import com.izu.user.config.consts.UserUrlCenter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 综合组织机构相关接口
 */
@Api(tags = "通用数据项")
@RestController
public class CarAssetStructController {

    @RequestFunction(functionName = "查询综合资产组织机构下拉框")
    @ApiOperation(value = "查询综合资产组织机构下拉框",notes = "作者：牛子联")
    @ApiImplicitParam(name = "assetName",value = "组织机构名称",required = false)
    @RequestMapping(value = MrcarUrl.PROVIDER_CAR_ASSET_STRUCT_DROPDOWN,method = {RequestMethod.POST,RequestMethod.GET})
    public RestResponse<CarAssetStructDropdownRespDTO> getPCStructAssetList(String assetName) {
        HashMap<String, Object> param = new HashMap<>();
        param.put("assetName", assetName);
        //资产组织机构状态 1：有效、4、无效
        param.put("assetState", 1);
        String restUrl = new CarAssetRestLocator().getRestUrl("/asset/v1/getPCStructAssetList.json");
        RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, param, null, StructAssetDTO.class);
        if (!restResponse.isSuccess()) {
            return restResponse;
        }
        List<StructAssetDTO> list = (List<StructAssetDTO>) restResponse.getData();
        return RestResponse.success(list.stream().map(s ->{
            CarAssetStructDropdownRespDTO respDTO = new CarAssetStructDropdownRespDTO();
            respDTO.setAssetStructId(s.getAssetStructId());
            respDTO.setAssetName(s.getStructName());
            respDTO.setBelongCityCode(s.getBelongCityCode());
            respDTO.setBelongCityName(s.getBelongCityName());
            respDTO.setAccountSetCityCode(s.getAccountSetCityCode());
            respDTO.setAccountSetCityName(s.getAccountSetCityName());
            respDTO.setStructId(s.getStructId());
            respDTO.setBussStructCode(s.getBussStructCode());
            respDTO.setAssetStructCode(s.getAssetStructCode());
            return respDTO;
        }).collect(Collectors.toList()));
    }


}
