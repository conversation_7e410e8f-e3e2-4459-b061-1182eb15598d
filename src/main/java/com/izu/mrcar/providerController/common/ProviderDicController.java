package com.izu.mrcar.providerController.common;

import com.izu.asset.MrCarAssetRestLocator;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.dto.DicKeyValueDTO;
import com.izu.mrcar.service.common.ProviderStateEnumService;
import com.izu.mrcar.service.common.ProviderTypeEnumService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.*;
/**
* Description:运营端静态字典获取
* @Author:Hxc
* @Date:2023/7/21 2:35 PM
*/
@RestController
@Api(tags = "字典管理")
public class ProviderDicController {

    @Autowired
    private ProviderStateEnumService providerStateEnumService;
    @Autowired
    private ProviderTypeEnumService providerTypeEnumService;

    @GetMapping("/provider/common/dic/getStatusEnum")
    @ApiOperation(value = "状态枚举",notes = "状态枚举：1报警豁免状态 " +
            "5零散用车-订单状态 6零散用车-需求单状态 7零散用车-换车单状态 8零散用车-收入账单推送综合状态 9零散用车-客户订单状态 " +
            "10司机任务状态 11司机状态 12驾照准驾车型 13代步车-账单状态" +
            "21车辆任务状态" +
            "31维保-预约状态 32维保-税率 33 维保-施工单状态 34维保-维修组 35维保-施工类型 36 维保-支持类型 37 维保-事故单状态 38 私车公用-设备类型" +
            "39 权益卡-状态枚举 40 权益卡模板-状态 41 权益卡投放-状态"
    )
    @ApiImplicitParam(name = "type",value = "枚举类型",required = true)
    public RestResponse<DicKeyValueDTO> getStatusEnums(Byte type) {
        return RestResponse.success(providerStateEnumService.getResult(type));
    }

    @GetMapping("/provider/common/dic/getTypeEnum")
    @ApiOperation(value = "类型枚举",notes = "类型枚举：1工作流-流程分类 2工作流-默认业务类型 3工作流-OA业务类型 4工作流-任务分配规则的类型 " +
            "10报警-处理方式  11角色管理-角色类型 12系统平台类型 15获取APP权限跳转类型 20零散用车-供应商订单类型 21零散用车-租赁类型 22零散用车-收入账单、客户账单的账单类型" +
            "31车辆级别 32车辆用途 33车辆类型 34租赁用途（首汽车）35车辆动力类型 36车辆燃油标号 37发动机排量 " +
            "41维保-预约类型 42维保-维修供应商 43维保-施工类型 44维保-税率 45维修评价-维修供应商 46维修厂资质类型  51问题反馈-问题分类 52 下单-备注快捷词枚举 53 商务车-供应商类型 54 商务车-服务方枚举"+
    "55投放模式")
    @ApiImplicitParam(name = "type",value = "枚举类型",required = true)
    public RestResponse<DicKeyValueDTO> getTypeEnum(Byte type) {
        return RestResponse.success(providerTypeEnumService.getResult(type));
    }

    /**
     * 查询车级信息(开城时所有车级的查询)
     **/
    @GetMapping(value = "/provider/common/dic/listCarLevel")
    @ApiImplicitParams({
            @ApiImplicitParam(name="levelName",value="级别名称",paramType="String"),
            @ApiImplicitParam(name="usableRange",value="数据范围3首汽，2其他企业（新的企业配置编辑里需要传2",paramType="Byte",example = "2"),
            @ApiImplicitParam(name="valid",value="密码",paramType="Byte"),
    })
    @ApiOperation("查询车级信息(开城时所有车级的查询)")
    public RestResponse listCarLevel(String levelName,Byte usableRange, Byte valid) {
        String restUrl = new MrCarAssetRestLocator().getRestUrl("/leve/getCarLevelList");
        Map<String, Object> paraMap = new HashMap<>(4);
        paraMap.put("levelName", levelName);
        //通用车型
        paraMap.put("usableRange", 1);
        paraMap.put("valid", valid);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Map.class);
    }
}
