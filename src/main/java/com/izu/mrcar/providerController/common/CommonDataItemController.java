package com.izu.mrcar.providerController.common;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.izu.config.MrCarConfigRestLocator;
import com.izu.config.dto.lingsan.CitySimpleListRespDTO;
import com.izu.consts.ConfigURI;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.config.RestUrlConfig;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.dto.company.lingsan.SupplierSimpleRespDTO;
import com.izu.user.dto.crmCustomer.CrmCustomerQueryReqDTO;
import com.izu.user.dto.provider.company.CompositeCompanyReqDTO;
import com.izu.user.dto.provider.company.CompositeCompanyRespDTO;
import com.izu.user.dto.provider.company.ProviderCompanySimpleReqDTO;
import com.izu.user.dto.provider.company.ProviderCompanySimpleRespDTO;
import com.izu.user.dto.staff.pc.DataPermInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.enums.CompanyBusinessTypeEnum;
import com.izu.user.enums.perm.ProviderDataPermTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.apache.commons.compress.utils.Sets;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static com.izu.user.config.consts.UserUrlCenter.PROVIDER_COMPOSITE_COMPANY_LIST;

/**
* @Description: 运营端通用数据项接口
* @author: hxc
* @Date: 2023/1/31
**/
@RestController
@Api(tags = "通用数据项")
public class CommonDataItemController {

    private final UserRestLocator userRestLocator = new UserRestLocator();

    private final MrCarConfigRestLocator configRestLocator = new MrCarConfigRestLocator();



    @PostMapping(UserUrlCenter.GET_SIMPLE_COMPANY_LIST)
    @RequestFunction(functionName = "企业精简列表（分页）")
    @ApiOperation(value = "企业精简列表（分页）",notes = "作者：贺新春")
    public RestResponse<PageDTO<ProviderCompanySimpleRespDTO>> listForPage(@RequestBody ProviderCompanySimpleReqDTO param) {
        if (param.getIsDataPermission()){
            ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
            ProviderDataPermTypeEnum dataPermTypeEnum = ProviderDataPermTypeEnum.getByType(providerLoginInfo.obtainDataPerm().getDataPermType());
            switch (dataPermTypeEnum){
                case RESPONSIBLE_CONTRACT:// 负责合同
                    param.setCompanyIds(Sets.newHashSet(-1));
                    break;
                case RESPONSIBLE_CUSTOMER:// 负责客户
                    Set<String> dataCodeSet = providerLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.ID).getDataCodeSet();
                    if (dataCodeSet == null || dataCodeSet.isEmpty()){
                        param.setCompanyIds(Sets.newHashSet(-1));
                    }else {
                        Set<Integer> ids = dataCodeSet.stream()
                                .map(Integer::parseInt)
                                .collect(Collectors.toSet());
                        param.setCompanyIds(ids);
                    }
                    break;
                case ASSIGN_CUSTOMER:// 指定客户
                    Set<String> dataCodeSet2 = providerLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.ID).getDataCodeSet();
                    if (dataCodeSet2 == null || dataCodeSet2.isEmpty()){
                        param.setCompanyIds(Sets.newHashSet(-1));
                    }else {
                        Set<Integer> ids2 = dataCodeSet2.stream()
                                .map(Integer::parseInt)
                                .collect(Collectors.toSet());
                        param.setCompanyIds(ids2);
                    }
                    break;
                case SELF_DEPT:// 所属部门
                    param.setDeptIds(providerLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet());
                    break;
                case ASSIGN_DEPT:// 指定部门
                    param.setDeptIds(providerLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet());
                    break;
                case ALL:// 所有
                    break;
                case ONE_SELF:// 本人
                    param.setCompanyIds(Sets.newHashSet(-1));
                    break;
            }
        }
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, userRestLocator.getRestUrl(UserUrlCenter.GET_SIMPLE_COMPANY_LIST), paramMap, null);
    }

    @GetMapping(ConfigURI.PROVIDER_LINGSAN_CITY_CONFIG_LIST)
    @ApiImplicitParam(name = "companyId",value = "供应商ID",required = true,paramType="form")
    @ApiOperation(value = "开通城市列表(零散供应商)",notes = "作者：贺新春")
    public RestResponse<CitySimpleListRespDTO> getLsCitySimpleList(@Verify(param = "companyId", rule = "required") Integer companyId){
        String url = configRestLocator.getRestUrl(ConfigURI.PROVIDER_LINGSAN_CITY_CONFIG_LIST);
        Map<String, Object> paramMap = new HashMap<>(1);
        paramMap.put("companyId",companyId);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.GET, url, paramMap, null);
    }

    @PostMapping(PROVIDER_COMPOSITE_COMPANY_LIST)
    @RequestFunction(functionName = "查询企业列表(包含服务商)")
    @ApiOperation(value = "查询企业列表(包含服务商)",notes = "author: 及晓林")
    public RestResponse<PageDTO<CompositeCompanyRespDTO>> listCompositeCompanyList(
            @RequestBody CompositeCompanyReqDTO reqDTO) {
        String url = RestUrlConfig.getConfig().getUserCoreUrl() + PROVIDER_COMPOSITE_COMPANY_LIST;
        // params
        final Map<String, Object> innerMap = JSON.parseObject(JSON.toJSONString(reqDTO)).getInnerMap();
        Map<String, Object> restParam = new HashMap<>(innerMap);
        // invoke
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POST, url,
                restParam, null, CompositeCompanyRespDTO.class);
    }

    /**
     * 获取商务车的供应商
     */
    @RequestFunction(functionName = "获取商务车的供应商")
    @ApiOperation(value = "获取商务车的供应商",notes = "author: 牛子联")
    @PostMapping(UserUrlCenter.PROVIDER_GET_BUSINESS_SUPPLIER_PAGE_LIST)
    public RestResponse<List<SupplierSimpleRespDTO>> getSupplierList(){
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.PROVIDER_GET_BUSINESS_SUPPLIER_PAGE_LIST);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.GET, restUrl, null,null);
    }


    @PostMapping(UserUrlCenter.GET_SIMPLE_COMPANY_LIST_DATA_PERM)
    @RequestFunction(functionName = "企业精简列表-数据权限（分页）")
    @ApiOperation(value = "企业精简列表-数据权限（分页）",notes = "作者：丁伟兵")
    public RestResponse<PageDTO<ProviderCompanySimpleRespDTO>> getCompanyByDataPerm(@RequestBody ProviderCompanySimpleReqDTO reqDTO) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        //数据权限不等于所有并且所属部门不等于总部的
        AtomicBoolean flag= new AtomicBoolean(false);
        List<String> companyIds = new ArrayList<>();
        if(ProviderDataPermTypeEnum.ALL.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())){
            flag.set(true);
        }
        if(ProviderDataPermTypeEnum.ASSIGN_DEPT.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType()) &&
                CollUtil.isNotEmpty(providerLoginInfo.getProviderDataPerm().getDataPermInfoList())){
            List<DataPermInfo> dataPermInfoList = providerLoginInfo.getProviderDataPerm().getDataPermInfoList();
            //查询所属部门
            String url = new UserRestLocator().getRestUrl(UserUrlCenter.PROVIDER_HEAD_DEPARTMENT_LIST);
            RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, url, null, null, String.class);
            if(restResponse!=null && restResponse.isSuccess() && restResponse.getData()!=null){
                List<String> list = (List<String>) restResponse.getData();
                dataPermInfoList.forEach(x->{
                    if(list.contains(x.getDataPermCode())){
                        flag.set(true);
                    }
                    companyIds.add(String.valueOf(x.getDataPermCode()));
                });
            }
        }
        List<String>  customerCodeList = new ArrayList<>();
        if(!flag.get()){
            //先查询用户是否存在数据权限
            String userUrl = new UserRestLocator().getRestUrl(UserUrlCenter.GET_CUSTOMER_CODE_LIST_BY_STAFF_COMPANY);
            CrmCustomerQueryReqDTO dto = new CrmCustomerQueryReqDTO();
            dto.setOwnerOrCreateOrUpdateId(providerLoginInfo.getBaseInfo().getMgtStaffId());
            if(Objects.equals(providerLoginInfo.obtainDataPerm().getDataPermType(),ProviderDataPermTypeEnum.ASSIGN_DEPT.getType())){
                dto.setCompanyIds(companyIds);
            }
            Map<String, Object> param = new HashMap<>();
            param.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
            RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, userUrl, param, null, String.class);
            if(restResponse!=null && restResponse.isSuccess() && restResponse.getData()!=null){
                customerCodeList=(List<String>) restResponse.getData();
            }
        }
        //没有企业，并且不是全部企业
        if(!flag.get()&& CollectionUtil.isEmpty(customerCodeList)){
            PageDTO<ProviderCompanySimpleRespDTO> pageDTO = new PageDTO<>();
            pageDTO.setResult(Collections.emptyList());
            pageDTO.setTotal(0);
            pageDTO.setPage(reqDTO.getPage());
            return RestResponse.success(pageDTO);
        }
        reqDTO.setCompanyCodeList(customerCodeList);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, userRestLocator.getRestUrl(UserUrlCenter.GET_SIMPLE_COMPANY_LIST), paramMap, null);

    }
    
}
