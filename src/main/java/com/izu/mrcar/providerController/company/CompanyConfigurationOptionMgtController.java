package com.izu.mrcar.providerController.company;

import com.izu.config.dto.company.req.CompanyConfigurationSaveReqDTO;
import com.izu.config.dto.company.resp.ConfigModuleRespDTO;
import com.izu.consts.ConfigURI;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.controller.base.ConfigBaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/18 16:09
 */
@Api(tags = "企业信息")
@RestController
public class CompanyConfigurationOptionMgtController extends ConfigBaseController {

    /**
     * 通过公司ID获取公司配置详情
     * 供公司配置编辑页面拉取详情使用。
     *
     * @param companyId 公司ID
     * @return 配置列表
     */
    @RequestFunction(functionName = "获取企业配置详情")
    @ApiOperation(value = "获取企业配置详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "companyId", value = "公司ID", paramType = "form")
    })
    @PostMapping(ConfigURI.COMPANY_CONFIG_GET_CONFIG)
    public RestResponse<List<ConfigModuleRespDTO>> getByCompanyId(Integer companyId) {
        HashMap<Object, Object> param = new HashMap<>();
        param.put("companyId", companyId);
        return post(ConfigURI.COMPANY_CONFIG_GET_CONFIG, param);
    }

    /**
     * 保存公司基础配置
     *
     * @param reqDTO
     * @return
     */
    @RequestFunction(functionName = "保存企业配置")
    @ApiOperation(value = "保存企业配置")
    @PostMapping(ConfigURI.COMPANY_CONFIG_SAVE_BASIC_CONFIG)
    public RestResponse<Boolean> save(@RequestBody CompanyConfigurationSaveReqDTO reqDTO) {
        return post(ConfigURI.COMPANY_CONFIG_SAVE_BASIC_CONFIG, reqDTO);
    }

}
