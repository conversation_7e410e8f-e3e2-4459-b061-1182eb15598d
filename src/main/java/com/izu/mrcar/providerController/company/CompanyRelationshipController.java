package com.izu.mrcar.providerController.company;


import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.controller.base.UserBaseController;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.dto.company.*;
import com.izu.user.dto.staff.pc.DataPerm;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.enums.perm.ProviderDataPermTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 维护企业父子关系
 */
@Api(tags = "企业信息")
@RestController
public class CompanyRelationshipController extends UserBaseController {


    @RequestFunction(functionName = "为某企业添加子企业")
    @ApiOperation(value = "为某企业添加子企业")
    @ApiImplicitParams({
            @ApiImplicitParam(name="parentCompanyCode",value="父企业编码",paramType="form"),
            @ApiImplicitParam(name="childCompanyCode",value="子企业编码",paramType="form")
    })
    @PostMapping(UserUrlCenter.PROVIDER_COMPANY_RELATIONSHIP_LINK_CHILD_COMPANY)
    public RestResponse link(@Verify(param = "parentCompanyCode", rule = "required") String parentCompanyCode,
                             @Verify(param = "childCompanyCode", rule = "required") String childCompanyCode) {
        Map<String, Object> param = new HashMap<>();
        param.put("parentCompanyCode", parentCompanyCode);
        param.put("childCompanyCode", childCompanyCode);
        return post(UserUrlCenter.PROVIDER_COMPANY_RELATIONSHIP_LINK_CHILD_COMPANY, param);
    }


    @RequestFunction(functionName = "获取某企业的子企业树")
    @ApiOperation(value = "获取某企业的子企业树",notes = "返回结果中包含其本身")
    @ApiImplicitParams({
            @ApiImplicitParam(name="companyCode",value="企业编码",paramType="form"),
    })
    @GetMapping(UserUrlCenter.PROVIDER_COMPANY_RELATIONSHIP_QUERY_COMPANY_TREE)
    public RestResponse<CompanyTreeNoteRespDTO> queryCompanyTree(@Verify(param = "companyCode", rule = "required") String companyCode) {
        Map<String, Object> param = new HashMap<>();
        param.put("companyCode", companyCode);
        return get(UserUrlCenter.PROVIDER_COMPANY_RELATIONSHIP_QUERY_COMPANY_TREE,param);
    }


    @RequestFunction(functionName = "移除某企业下的子企业")
    @ApiOperation(value = "移除某企业下的子企业")
    @ApiImplicitParams({
            @ApiImplicitParam(name="parentCompanyCode",value="父企业编码",paramType="form"),
            @ApiImplicitParam(name="childCompanyCode",value="子企业编码",paramType="form")
    })
    @PostMapping(UserUrlCenter.PROVIDER_COMPANY_RELATIONSHIP_UNLINK)
    public RestResponse unlink(@Verify(param = "parentCompanyCode", rule = "required") String parentCompanyCode,
                               @Verify(param = "childCompanyCode", rule = "required") String childCompanyCode) {
        Map<String, Object> param = new HashMap<>();
        param.put("parentCompanyCode", parentCompanyCode);
        param.put("childCompanyCode", childCompanyCode);
        return post(UserUrlCenter.PROVIDER_COMPANY_RELATIONSHIP_UNLINK, param);
    }

    @ApiOperation(value = "运营端企业下拉框")
    @RequestFunction(functionName = "运营端企业下拉框")
    @PostMapping(UserUrlCenter.PROVIDER_COMPANY_SELECT)
    public RestResponse<PageDTO<CompanyNameRespDTO>> dropDownList(@RequestBody CompanyDropDownReqDTO reqDTO) {
        if(Objects.nonNull(reqDTO.getPageSize())){
            reqDTO.setPageSize(1000);
        }
        if(Objects.nonNull(reqDTO.getCompanyStatus())){
            reqDTO.setCompanyStatus((byte) 2);
        }
        return postBody(UserUrlCenter.PROVIDER_COMPANY_SELECT, reqDTO);
    }
    @ApiOperation(value = "运营端企业下拉框-businesstype可多传")
    @RequestFunction(functionName = "运营端企业下拉框-businesstype可多传")
    @PostMapping(UserUrlCenter.PROVIDER_COMPANY_SELECT_BY_BUSINESS_TYPE)
    public RestResponse<PageDTO<CompanyNameRespDTO>> providerDropDownListByBusinessType(@RequestBody CompanyDropDownBusinessReqDTO reqDTO) {
        if(Objects.nonNull(reqDTO.getPageSize())){
            reqDTO.setPageSize(1000);
        }
        if(Objects.nonNull(reqDTO.getCompanyStatus())){
            reqDTO.setCompanyStatus((byte) 2);
        }
        return postBody(UserUrlCenter.PROVIDER_COMPANY_SELECT_BY_BUSINESS_TYPE, reqDTO);
    }

    @ApiOperation(value = "运营端企业下拉框只返回数据权限为All的")
    @RequestFunction(functionName = "运营端企业下拉框只返回数据权限为All的")
    @PostMapping(UserUrlCenter.PROVIDER_COMPANY_SELECT_ONLY_ALL)
    public RestResponse<CompanyNameRespDTO> dropDownListByDataPermTypeEqAll(@RequestBody CompanyDropDownReqDTO reqDTO) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        DataPerm providerDataPerm = providerLoginInfo.getProviderDataPerm();
        Byte dataPermType = providerDataPerm.getDataPermType();
        // 运营端登录数据权限不是All 不展示
        if (!dataPermType.equals(ProviderDataPermTypeEnum.ALL.getType())){
            return RestResponse.success(new ArrayList<>());
        }

        if(Objects.nonNull(reqDTO.getPageSize())){
            reqDTO.setPageSize(1000);
        }
        if(Objects.nonNull(reqDTO.getCompanyStatus())){
            reqDTO.setCompanyStatus((byte) 2);
        }
        return postBody(UserUrlCenter.PROVIDER_COMPANY_SELECT, reqDTO);
    }

    @ApiOperation(value = "获取某企业的子企业分页列表")
    @RequestFunction(functionName = "获取某企业的子企业分页列表")
    @PostMapping(UserUrlCenter.PROVIDER_COMPANY_RELATIONSHIP_DIRECTLY_CHILD_COMPANIES)
    public RestResponse queryDirectlyChildCompanies(@RequestBody CompanyChildQueryReqDTO reqDTO){
        return postBody(UserUrlCenter.PROVIDER_COMPANY_RELATIONSHIP_DIRECTLY_CHILD_COMPANIES,reqDTO);
    }

    @ApiOperation(value = "运营端企业下拉框")
    @RequestFunction(functionName = "运营端企业下拉框")
    @PostMapping("/provider/company/remind/select")
    public RestResponse<PageDTO<CompanyNameRespDTO>> queryList(@RequestBody CompanyDropDownReqDTO reqDTO) {
        if(Objects.nonNull(reqDTO.getPageSize())){
            reqDTO.setPageSize(1000);
        }
        if(Objects.nonNull(reqDTO.getCompanyStatus())){
            reqDTO.setCompanyStatus((byte) 2);
        }
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        reqDTO.setDataPermType(providerLoginInfo.obtainDataPerm().getDataPermType());
        if(!Objects.equals(ProviderDataPermTypeEnum.ALL.getType(),providerLoginInfo.obtainDataPerm().getDataPermType())){
            return RestResponse.success(new ArrayList<>());
        }
        return postBody(UserUrlCenter.PROVIDER_COMPANY_SELECT, reqDTO);
    }



}
