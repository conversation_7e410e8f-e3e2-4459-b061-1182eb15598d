package com.izu.mrcar.providerController.company;

import com.google.common.collect.Maps;
import com.izu.config.dto.GovCompanyConfigReqDTO;
import com.izu.framework.web.exception.RestErrorException;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.config.RestUrlConfig;
import com.izu.mrcar.common.constants.MrcarUrl;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.controller.base.UserBaseController;
import com.izu.mrcar.service.user.CompanyService;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.dto.company.CompanyAddTagReqDTO;
import com.izu.user.dto.company.CompanyNameRespDTO;
import com.izu.user.dto.company.CompanyQueryDTO;
import com.izu.user.dto.company.GovCompanyDTO;
import com.izu.user.dto.provider.company.OpenTrialAccountReqDTO;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.dto.user.req.CoCrmCompanySyncReqDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.izu.user.dto.company.CompanyDTO;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 运营端-企业管理（t_company）
 *
 * <AUTHOR> on  2024/8/15 下午11:11
 */
@Api(tags = "企业信息")
@RestController
public class CompanyManagementController extends UserBaseController {

    @Resource
    private CompanyService companyService;

    private static final UserRestLocator userRestLocator = new UserRestLocator();

    @PostMapping(MrcarUrl.PROVIDER_BUSINESS_CUSTOMER_COMPANY_LIST)
    @ApiOperation(value = "公务用车-客户列表")
    public RestResponse<GovCompanyDTO> govCompanyPageList(@RequestBody CompanyQueryDTO companyQueryDTO) {
        String url = "/companyManage/getPageListForGov";
        return postBody(url, companyQueryDTO);
    }

    @PostMapping(MrcarUrl.PROVIDER_BUSINESS_CUSTOMER_COMPANY_CONFIG_UPDATE)
    @ApiOperation(value = "公务用车-编辑")
    public RestResponse<Boolean> govCompanyConfigAddOrUpdate(@RequestBody GovCompanyConfigReqDTO govCompanyConfigReqDTO) {
        CompanyDTO companyDTO = companyService.getCompanyInfo(govCompanyConfigReqDTO.getCompanyId());
        if(Objects.isNull(companyDTO)){
            throw new RestErrorException("企业信息不存在", ErrorCode.COMPANY_NOT_EXIST);
        }
        String restUrl = RestUrlConfig.getConfig().getConfigCoreUrl() + "/companyManage/config/update";
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        govCompanyConfigReqDTO.setCompanyCode(companyDTO.getCustomerCode());
        govCompanyConfigReqDTO.setCompanyName(companyDTO.getCompanyName());
        govCompanyConfigReqDTO.setCreateId(loginBaseInfo.obtainBaseInfo().getStaffId());
        govCompanyConfigReqDTO.setUpdateId(loginBaseInfo.obtainBaseInfo().getStaffId());
        govCompanyConfigReqDTO.setCreateName(loginBaseInfo.obtainBaseInfo().getStaffName());
        govCompanyConfigReqDTO.setUpdateName(loginBaseInfo.obtainBaseInfo().getStaffName());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, govCompanyConfigReqDTO);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, Boolean.class);
    }

    //给客户添加一个标签
    @PostMapping(MrcarUrl.PROVIDER_BUSINESS_CUSTOMER_ADD_TAG)
    @ApiOperation(value = "给客户添加一个标签")
    public RestResponse<String> addTag(@RequestBody CompanyAddTagReqDTO reqDTO) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        reqDTO.setLoginUserId(baseLoginInfo.obtainBaseInfo().getStaffId());
        reqDTO.setLoginUserName(baseLoginInfo.obtainBaseInfo().getStaffName());
        String url = "/companyManage/addTag";
        return postBody(url, reqDTO);
    }

    ///getDropDownForGov
    @PostMapping(MrcarUrl.PROVIDER_BUSINESS_CUSTOMER_DROP_DOWN)
    @ApiOperation(value = "公务用车-客户下拉框")
    public RestResponse<List<CompanyNameRespDTO>> getDropDownForGov() {
        String url = "/companyManage/getDropDownForGov";
        return post(url, null);
    }


    /**
     * 该方法为异步生成方法。
     * 开通试用账号的客户
     * 1.该功能为运营端功能
     * 2.目的是为各个城市创建独立的演示账号
     */
    @ApiOperation(value = "开通试用账号的客户",notes = "该功能为运营端功能，目的是为各个城市创建独立的演示账号，该接口是异步接口，请求之后会立即返回。")
    @PostMapping(UserUrlCenter.OPEN_TRIAL_ACCOUNT)
    @SuppressWarnings("unchecked")
    public RestResponse<Boolean> openTrialAccount(@RequestBody OpenTrialAccountReqDTO reqDTO) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        reqDTO.setLoginUserId(providerLoginInfo.getBaseInfo().getStaffId());
        reqDTO.setLoginUserName(providerLoginInfo.getBaseInfo().getStaffName());
        reqDTO.setLoginUserMobile(providerLoginInfo.getBaseInfo().getMobile());
        HashMap<String, Object> restParams = new HashMap<>();
        restParams.put(BaseHttpClient.POSTBODY_MAP_KEY,reqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY,
                new UserRestLocator().getRestUrl(UserUrlCenter.OPEN_TRIAL_ACCOUNT), restParams, null);
    }



    @PostMapping(path = "/provider/companyManagement/crmCompanySync")
    @ApiOperation(value = "crm企业同步。同步综合企业到mrcar系统")
    public RestResponse<CompanyDTO> crmCompanySync(CoCrmCompanySyncReqDTO reqDTO){
        Map<String, Object> param = new HashMap<>();
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        String url = userRestLocator.getRestUrl("/provider/company/coCrmCompanySync");
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, param, null, CompanyDTO.class);
    }

    @PostMapping(path = "/provider/companyManagement/crmSupplierSync")
    @ApiOperation(value = "crm供应商同步。同步综合供应商到mrcar系统")
    public RestResponse<CompanyDTO> crmSupplierSync(CoCrmCompanySyncReqDTO reqDTO){
        Map<String, Object> param = new HashMap<>();
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        String url = userRestLocator.getRestUrl("/provider/company/crmSupplierSync");
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, param, null, CompanyDTO.class);
    }



}
