package com.izu.mrcar.providerController.company;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.utils.DataPermUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.dto.company.*;
import io.swagger.annotations.Api;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

import static com.izu.mrcar.common.errorcode.ErrorCode.STAT_FUNCTION_OPERATION_EXPORT_EXCEED;

/**
 * 企业自有客户
 */
@RestController
@RequestMapping("/provider/company/self/customer")
@Api(tags = "企业自有客户")
public class ProviderCompanySelfCustomerController {

    private final UserRestLocator locator = new UserRestLocator();

    /**
     * 分页查询(运营端)
     */
    @PostMapping("/search")
    @SuppressWarnings("unchecked")
    public RestResponse<PageDTO<CompanySelfCustomerRespDTO>> search(@RequestBody CompanySelfCustomerSearchDTO searchDTO) {
        // 权限
        DataPermUtil.putDataPerm(searchDTO);
        // restful
        Map<String, Object> params = new HashMap<>();
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, searchDTO);
        String restUrl = locator.getRestUrl("/company/self/customer/search");
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, params, null, CompanySelfCustomerRespDTO.class);
    }

    /**
     * 下拉列表(运营端)
     */
    @PostMapping("/droDown")
    @SuppressWarnings("unchecked")
    public RestResponse<List<CompanySelfCustomerRespDTO>> dropDown(@RequestBody CompanySelfCustomerSearchDTO searchDTO) {
        // 权限
        DataPermUtil.putDataPerm(searchDTO);
        // restful
        Map<String, Object> params = new HashMap<>();
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, searchDTO);
        String restUrl = locator.getRestUrl("/company/self/customer/dropDown");
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, restUrl, params, null, CompanySelfCustomerRespDTO.class);
    }

    /**
     * 导出
     */
    @PostMapping("/export")
    public void export(@RequestBody CompanySelfCustomerSearchDTO searchDTO,
                       HttpServletResponse response) throws IOException {
        // 权限
        DataPermUtil.putDataPerm(searchDTO);

        int page = 1;
        int pageSize = 100;
        int pages;

        List<ProviderCompanySelfCustomerExportDTO> result = new ArrayList<>();

        do {
            searchDTO.setPage(page);
            searchDTO.setPageSize(pageSize);

            String url = locator.getRestUrl("/company/self/customer/search");
            Map<String, Object> params = new HashMap<>();
            params.put(BaseHttpClient.POSTBODY_MAP_KEY, searchDTO);
            @SuppressWarnings("unchecked")
            RestResponse<PageDTO<CompanySelfCustomerRespDTO>> restResponse =
                    RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, params, null,
                            CompanySelfCustomerRespDTO.class);
            PageDTO<CompanySelfCustomerRespDTO> pageDTO = restResponse.getData();

            // 校验记录条数
            if (pageDTO.getTotal() > ExportExcelConstants.EXPORT_MAX_LINE) {
                RestResponse<?> rest = RestResponse.fail(STAT_FUNCTION_OPERATION_EXPORT_EXCEED,
                        pageDTO.getTotal(), ExportExcelConstants.EXPORT_MAX_LINE);
                JSON.writeJSONString(response.getOutputStream(), rest);
                response.setContentType("application/json");
                return;
            }

            // 设置页码信息
            pages = pageDTO.getPages();
            result.addAll(
                    pageDTO.getResult()
                            .stream()
                            .map(s -> {
                                ProviderCompanySelfCustomerExportDTO dto = new ProviderCompanySelfCustomerExportDTO();
                                BeanUtils.copyProperties(s, dto);
                                // 客户类型
                                dto.setCustomerTypeStr(Objects.equals(s.getCustomerType(), 1) ? "企业" : "个人");
                                return dto;
                            }).collect(Collectors.toList()));
            page++;
        } while (page <= pages);

        String fileName = URLEncoder.encode("客户列表", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), ProviderCompanySelfCustomerExportDTO.class)
                .sheet("客户列表").doWrite(result);
    }


}
