package com.izu.mrcar.providerController.dictionary;

import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.shiro.session.WebSessionUtil;
import com.izu.user.dto.LoginUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@Api(tags = "字典管理")
public class ProviderDictionaryController {

    private final MrCarAssetRestLocator assetRestLocator = new MrCarAssetRestLocator();

    /**
     * 根据企业类型查询全部服务类型
     **/
    @PostMapping(value = MrCarAssetRestCenter.PROVIDER_DICTIONARY_LIST_SERVICE_BY_TYPE)
    @ApiOperation("根据企业类型查询全部服务类型")
    @ApiImplicitParam(name = "serviceType", value = "服务类型，查客户的传1，首汽的传2", required = true)
    public RestResponse listServiceByType(Byte serviceType) {
        Map<String, Object> paramMap = new HashMap<>();
        if (serviceType == null) {
            serviceType = 2;
        }
        paramMap.put("serviceType", serviceType);
        String restUrl = assetRestLocator.getRestUrl("/service/getServiceTypeListByType");
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, paramMap, null, Map.class);
    }

    /**
     * 查询车级信息(开城时所有车级的查询)
     **/
    @GetMapping(value = MrCarAssetRestCenter.PROVIDER_DICTIONARY_LIST_CAR_LEVEL)
    @ApiImplicitParams({
            @ApiImplicitParam(name="levelName",value="级别名称",paramType="String"),
            @ApiImplicitParam(name="usableRange",value="车级适用范围；1:通用;2-内部用车;3-商务用车(外部用车);4:零散用车",paramType="Byte",example = "2"),
            @ApiImplicitParam(name="valid",value="密码",paramType="Byte"),
    })
    @ApiOperation("查询车级信息(开城时所有车级的查询)")
    public RestResponse listCarLevel(String levelName,Byte usableRange, Byte valid) {
        String restUrl = assetRestLocator.getRestUrl("/leve/getCarLevelList");
        Map<String, Object> paraMap = new HashMap<>();
        paraMap.put("levelName", levelName);
        //首汽企业才能看到接单管理
        if (usableRange == null) {
            usableRange = 3;
        }
        paraMap.put("usableRange", usableRange);
        paraMap.put("valid", valid);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Map.class);
    }
}
