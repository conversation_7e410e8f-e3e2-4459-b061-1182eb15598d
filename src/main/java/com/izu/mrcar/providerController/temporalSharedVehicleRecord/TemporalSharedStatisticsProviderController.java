package com.izu.mrcar.providerController.temporalSharedVehicleRecord;

import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.controller.temporalSharedVehicleRecord.TemporalSharedDayStatisticsExport;
import com.izu.mrcar.controller.temporalSharedVehicleRecord.TemporalSharedMonthStatisticsExport;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.dto.timeShare.DayStatisticsListDTO;
import com.izu.mrcar.order.dto.timeShare.DayStatisticsListInputDTO;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.AccountBelongDepartment;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.enums.perm.ProviderDataPermTypeEnum;
import com.izu.user.util.SingletonFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

@Api(tags = "分时用车")
@RestController
public class TemporalSharedStatisticsProviderController {

    private final MrcarOrderRestLocator orderRestLocator = SingletonFactory.getSingleton(SingletonFactory.orderRestLocator, MrcarOrderRestLocator::new);

    @PostMapping(MrcarOrderRestMsgCenter.PROVIDER_TIME_REPORT_DAY_STATISTICS_LIST)
    @RequestFunction(functionName = "分时用车日统计列表查询（运营端）")
    @ApiOperation(value = "分时用车日统计列表查询（运营端）",notes = "作者：mapp")
    public RestResponse<PageDTO<DayStatisticsListDTO>> getDayList(@RequestBody DayStatisticsListInputDTO param){
        setDataPerm(param);
        Map<String, Object> params = new HashMap<>(1);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, orderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.PROVIDER_TIME_REPORT_DAY_STATISTICS_LIST), params, null, DayStatisticsListDTO.class);
    }

    @PostMapping(MrcarOrderRestMsgCenter.TIME_SHARE_STATISTICS_REFRESH)
    @RequestFunction(functionName = "分时用车日统计更新操作（运营端）")
    @ApiOperation(value = "分时用车日统计更新操作（运营端）",notes = "作者：mapp")
    public RestResponse refresh(String dateStr){
        Map<String, Object> params = new HashMap<>(1);
        params.put("dateStr",dateStr);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, orderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.TIME_SHARE_STATISTICS_REFRESH), params, null, Boolean.class);
    }

    @PostMapping(MrcarOrderRestMsgCenter.TIME_SHARE_STATISTICS_GET_NOW_TIME)
    @RequestFunction(functionName = "分时用车日统计获取更新时间（运营端）")
    @ApiOperation(value = "分时用车日统计获取更新时间（运营端）",notes = "作者：mapp")
    public RestResponse getNowTime(){
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, orderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.TIME_SHARE_STATISTICS_GET_NOW_TIME), null, null, String.class);
    }

    @PostMapping(MrcarOrderRestMsgCenter.PROVIDER_TIME_REPORT_MONTH_STATISTICS_LIST)
    @RequestFunction(functionName = "分时用车月度统计列表查询（运营端）")
    @ApiOperation(value = "分时用车月度统计列表查询（运营端）",notes = "作者：mapp")
    public RestResponse<PageDTO<DayStatisticsListDTO>> getMonthList(@RequestBody DayStatisticsListInputDTO param){
        setDataPerm(param);
        Map<String, Object> params = new HashMap<>(1);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, orderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.PROVIDER_TIME_REPORT_MONTH_STATISTICS_LIST), params, null, DayStatisticsListDTO.class);
    }

    @PostMapping(MrcarOrderRestMsgCenter.PROVIDER_TIME_REPORT_DAY_STATISTICS_EXPORT)
    @RequestFunction(functionName = "分时用车日统计列表导出（运营端）")
    @ApiOperation(value = "分时用车日统计列表导出（运营端）",notes = "作者：mapp")
    @ExportExcelWeb(fileName = ExportExcelConstants.FILE_NAME_TIME_REPORT_DAY_EXPORT_INFO,filePath = ExportExcelConstants.FILE_PATH,
            sheet = ExportExcelConstants.SHEET_NAME_TIME_REPORT_DAY_EXPORT_INFO, c = TemporalSharedDayStatisticsExport.class)
    public PageDTO<DayStatisticsListDTO> exportDayInfo(@RequestBody DayStatisticsListInputDTO param, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response){
        setDataPerm(param);
        Map<String, Object> params = new HashMap<>(1);
        param.setPageSize(ExportExcelConstants.EXPORT_MAX_LINE);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, orderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.PROVIDER_TIME_REPORT_DAY_STATISTICS_LIST), params, null, TemporalSharedDayStatisticsExport.class);
        if(restResponse!=null && restResponse.isSuccess()){
            PageDTO pageDTO = (PageDTO)restResponse.getData();
            if (pageDTO.getTotal() > ExportExcelConstants.EXPORT_MAX_LINE){
                throw ExceptionFactory.createRestException(ErrorCode.EXPORT_EXCEED, pageDTO.getTotal(), ExportExcelConstants.EXPORT_MAX_LINE);
            }
            return pageDTO;
        }
        else {
            return null;
        }
    }

    @PostMapping(MrcarOrderRestMsgCenter.PROVIDER_TIME_REPORT_MONTH_STATISTICS_EXPORT)
    @RequestFunction(functionName = "分时用车月度统计列表导出（运营端）")
    @ApiOperation(value = "分时用车月度统计列表导出（运营端）",notes = "作者：mapp")
    @ExportExcelWeb(fileName = ExportExcelConstants.FILE_NAME_TIME_REPORT_MONTH_EXPORT_INFO,filePath = ExportExcelConstants.FILE_PATH,
            sheet = ExportExcelConstants.SHEET_NAME_TIME_REPORT_MONTH_EXPORT_INFO, c = TemporalSharedMonthStatisticsExport.class)
    public PageDTO<DayStatisticsListDTO> exportMonthInfo(@RequestBody DayStatisticsListInputDTO param, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response){
        setDataPerm(param);
        Map<String, Object> params = new HashMap<>(1);
        param.setPageSize(ExportExcelConstants.EXPORT_MAX_LINE);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, orderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.PROVIDER_TIME_REPORT_MONTH_STATISTICS_LIST), params, null, TemporalSharedMonthStatisticsExport.class);
        if(restResponse!=null && restResponse.isSuccess()){
            PageDTO pageDTO = (PageDTO)restResponse.getData();
            if (pageDTO.getTotal() > ExportExcelConstants.EXPORT_MAX_LINE){
                throw ExceptionFactory.createRestException(ErrorCode.EXPORT_EXCEED, pageDTO.getTotal(), ExportExcelConstants.EXPORT_MAX_LINE);
            }
            return pageDTO;
        }
        else {
            return null;
        }
    }

    private void setDataPerm(DayStatisticsListInputDTO param) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        param.setDataPermType(providerLoginInfo.obtainDataPerm().getDataPermType());
        ProviderDataPermTypeEnum dataPermTypeEnum = ProviderDataPermTypeEnum.getByType(providerLoginInfo.obtainDataPerm().getDataPermType());
        switch (dataPermTypeEnum) {
            case RESPONSIBLE_CONTRACT:// 负责合同
                param.setDataPermCodeSet(providerLoginInfo.getBelongDepartmentList().stream().map(AccountBelongDepartment::getDeptCode).collect(Collectors.toSet()));
                break;
            case RESPONSIBLE_CUSTOMER:// 负责客户
                param.setDataPermCodeSet(providerLoginInfo.getBelongDepartmentList().stream().map(AccountBelongDepartment::getDeptCode).collect(Collectors.toSet()));
                break;
            case ASSIGN_CUSTOMER:// 指定客户
                param.setDataPermCodeSet(providerLoginInfo.getBelongDepartmentList().stream().map(AccountBelongDepartment::getDeptCode).collect(Collectors.toSet()));
                break;
            case SELF_DEPT:// 所属部门
                param.setDataPermCodeSet(providerLoginInfo.getBelongDepartmentList().stream().map(AccountBelongDepartment::getDeptCode).collect(Collectors.toSet()));
                break;
            case ASSIGN_DEPT:// 指定部门
                param.setDataPermCodeSet(providerLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet());
                break;
            case ALL:// 所有
                break;
            case ONE_SELF:// 本人
                param.setDataPermCodeSet(providerLoginInfo.getBelongDepartmentList().stream().map(AccountBelongDepartment::getDeptCode).collect(Collectors.toSet()));
                break;
        }
    }
}
