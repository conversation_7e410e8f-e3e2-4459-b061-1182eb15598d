package com.izu.mrcar.providerController.temporalSharedVehicleRecord;

import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.exception.RestErrorException;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.controller.temporalSharedVehicleRecord.TemporalSharedVehicleRecordExportDTO;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.dto.mrcar.OrderApplyDestinationDTO;
import com.izu.mrcar.order.dto.mrcar.OrderApplyPassengerDTO;
import com.izu.mrcar.order.dto.temporalSharedVehicle.DataPermDTO;
import com.izu.mrcar.order.dto.temporalSharedVehicle.TemporalSharedVehicleRecordDTO;
import com.izu.mrcar.order.dto.temporalSharedVehicle.TemporalSharedVehicleRecordDetailReqDTO;
import com.izu.mrcar.order.dto.temporalSharedVehicle.TemporalSharedVehicleRecordPageReqDTO;
import com.izu.mrcar.order.dto.temporalSharedVehicle.TemporalSharedVehicleRecordQueryOrderReqDTO;
import com.izu.mrcar.order.dto.temporalSharedVehicle.TemporalSharedVehicleRecordQueryOrderResDTO;
import com.izu.mrcar.utils.DataPermUtil;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.AccountBelongDepartment;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.enums.perm.ProviderDataPermTypeEnum;
import com.izu.user.util.ObjectTransferUtil;
import com.izu.user.util.SingletonFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> dongxiya 2024/1/29 15:37
 */
@Api(tags = "分时用车")
@RequestMapping("/provider")
@RestController
public class TemporalSharedVehicleRecordProviderController {

    private final MrcarOrderRestLocator orderRestLocator = SingletonFactory.getSingleton(SingletonFactory.orderRestLocator, MrcarOrderRestLocator::new);

    @ApiOperation("用车记录明细-列表")
    @PostMapping(MrcarOrderRestMsgCenter.TEMPORAL_SHARED_VEHICLE_RECORD_PAGE)
    public RestResponse<PageDTO<TemporalSharedVehicleRecordDTO>> page(@RequestBody TemporalSharedVehicleRecordPageReqDTO dto) {
        Map<String, Object> params = new HashMap<>(1);
        DataPermUtil.putDataPerm(dto);
        dealSpecialDataPerm(dto);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, orderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.TEMPORAL_SHARED_VEHICLE_RECORD_PAGE), params, null, Map.class);
    }

    public static void dealSpecialDataPerm(DataPermDTO dto) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        if (Objects.equals(ProviderDataPermTypeEnum.RESPONSIBLE_CONTRACT.getType(), dto.getDataPermType())
        || Objects.equals(ProviderDataPermTypeEnum.RESPONSIBLE_CUSTOMER.getType(), dto.getDataPermType())
        || Objects.equals(ProviderDataPermTypeEnum.ASSIGN_CUSTOMER.getType(), dto.getDataPermType())
        || Objects.equals(ProviderDataPermTypeEnum.SELF_DEPT.getType(), dto.getDataPermType())
        || Objects.equals(ProviderDataPermTypeEnum.ONE_SELF.getType(), dto.getDataPermType())
        ) {
            dto.setDataCodeSet(providerLoginInfo.getBelongDepartmentList().stream().map(AccountBelongDepartment::getDeptCode).collect(Collectors.toSet()));
        } else if (Objects.equals(ProviderDataPermTypeEnum.ASSIGN_DEPT.getType(), dto.getDataPermType())) {
            dto.setDataCodeSet(providerLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet());
        }
    }

    @ApiOperation("用车记录明细-详情")
    @PostMapping(MrcarOrderRestMsgCenter.TEMPORAL_SHARED_VEHICLE_RECORD_DETAIL)
    public RestResponse<TemporalSharedVehicleRecordDTO> detail(@RequestBody TemporalSharedVehicleRecordDetailReqDTO dto) {
        Map<String, Object> params = new HashMap<>(1);
        DataPermUtil.putDataPerm(dto);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, orderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.TEMPORAL_SHARED_VEHICLE_RECORD_DETAIL), params, null, Map.class);
    }
    @ApiOperation("用车记录明细-废除")
    @PostMapping(MrcarOrderRestMsgCenter.TEMPORAL_SHARED_VEHICLE_RECORD_INVALID)
    public RestResponse TemporalSharedVehicleRecordDTO(@RequestBody TemporalSharedVehicleRecordDetailReqDTO dto) {
        Map<String, Object> params = new HashMap<>(1);
        DataPermUtil.putDataPerm(dto);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, orderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.TEMPORAL_SHARED_VEHICLE_RECORD_INVALID), params, null, Map.class);
    }
    @ApiOperation("用车记录明细-关联申请单查询")
    @PostMapping(MrcarOrderRestMsgCenter.TEMPORAL_SHARED_VEHICLE_RECORD_QUERY_ORDER)
    public RestResponse<PageDTO<TemporalSharedVehicleRecordQueryOrderResDTO>> queryOrder(@RequestBody TemporalSharedVehicleRecordQueryOrderReqDTO dto) {
        Map<String, Object> params = new HashMap<>(1);
        DataPermUtil.putDataPerm(dto);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, orderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.TEMPORAL_SHARED_VEHICLE_RECORD_QUERY_ORDER), params, null, Map.class);
    }
    @ApiOperation("用车记录明细-确认关联")
    @PostMapping(MrcarOrderRestMsgCenter.TEMPORAL_SHARED_VEHICLE_RECORD_ASSOCIATION_ORDER)
    public RestResponse associationOrder(@RequestBody TemporalSharedVehicleRecordDetailReqDTO dto) {
        Map<String, Object> params = new HashMap<>(1);
        DataPermUtil.putDataPerm(dto);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, orderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.TEMPORAL_SHARED_VEHICLE_RECORD_ASSOCIATION_ORDER), params, null, Map.class);
    }

    @ApiOperation("用车记录明细-导出")
    @PostMapping(MrcarOrderRestMsgCenter.TEMPORAL_SHARED_VEHICLE_RECORD_EXPORT)
    @ExportExcelWeb(fileName = ExportExcelConstants.FILE_NAME_TEMPORAL_SHARED_VEHICLE_RECORD+ ExportExcelConstants.XLSX,filePath = ExportExcelConstants.FILE_PATH,
            sheet = ExportExcelConstants.FILE_NAME_TEMPORAL_SHARED_VEHICLE_RECORD, c = TemporalSharedVehicleRecordProviderExportDTO.class)
    public PageDTO<TemporalSharedVehicleRecordProviderExportDTO> export(@RequestBody TemporalSharedVehicleRecordPageReqDTO dto, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response) {
        DataPermUtil.putDataPerm(dto);
        dealSpecialDataPerm(dto);
        Map<String, Object> params = new HashMap<>(1);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        izuEasyExcelSession.setUserName(dto.getStaffName());
        dto.setPageSize(ExportExcelConstants.PAGE_SIZE);
        dto.setPage(izuEasyExcelSession.getPageNo());
        @SuppressWarnings("unchecked")
        RestResponse<PageDTO<TemporalSharedVehicleRecordDTO>> restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, orderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.TEMPORAL_SHARED_VEHICLE_RECORD_PAGE), params, null, TemporalSharedVehicleRecordDTO.class);
        if(restResponse.isSuccess()){
            PageDTO<TemporalSharedVehicleRecordDTO> pageDTO = restResponse.getData();
            if (pageDTO.getTotal()> ExportExcelConstants.EXPORT_MAX_LINE){
                RestResponse fail = RestResponse.fail(ErrorCode.EXPORT_EXCEED, pageDTO.getTotal(), ExportExcelConstants.EXPORT_MAX_LINE);
                throw new RestErrorException(fail.getMsg(), ErrorCode.EXPORT_LIMITED_WARN);
            }
            List<TemporalSharedVehicleRecordProviderExportDTO> result =
                    pageDTO.getResult().stream().map(s -> {
                        TemporalSharedVehicleRecordProviderExportDTO export = new TemporalSharedVehicleRecordProviderExportDTO();
                        BeanUtils.copyProperties(s, export);
                        // 乘车人
                        OrderApplyPassengerDTO[] passengers = s.getPassengers();
                        if (Objects.nonNull(passengers) && passengers.length > 0) {
                            String names = Arrays.stream(passengers)
                                    .map(OrderApplyPassengerDTO::getBookingPassengerUserName)
                                    .collect(Collectors.joining("\n"));
                            export.setPassengerName(names);
                        }
                        // 出发地
                        OrderApplyDestinationDTO[] destinations = s.getDestinations();
                        if (Objects.nonNull(destinations) && destinations.length > 0) {
                            String destinationStartNames = Arrays.stream(destinations)
                                    .filter(d -> Objects.equals(d.getAddressType(), 1))
                                    .map(OrderApplyDestinationDTO::getBookingEndLongAddr)
                                    .collect(Collectors.joining("\n"));
                            export.setDestinationStartName(destinationStartNames);
                        }
                        // 目的地
                        if (Objects.nonNull(destinations) && destinations.length > 0) {
                            String destinationEndNames = Arrays.stream(destinations)
                                    .filter(d -> Objects.equals(d.getAddressType(), 3)
                                                || Objects.equals(d.getAddressType(), 2))
                                    .map(OrderApplyDestinationDTO::getBookingEndLongAddr)
                                    .collect(Collectors.joining("\n"));
                            export.setDestinationEndName(destinationEndNames);
                        }
                        return export;
                    }).collect(Collectors.toList());
            return new PageDTO<>(pageDTO.getPage(), pageDTO.getPageSize(), pageDTO.getTotal(), result);
        }else {
            return null;
        }
    }
}
