package com.izu.mrcar.providerController.config;

import com.alibaba.fastjson.JSONObject;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.common.constants.Constant;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@RestController("providerOrderCommentController")
@RequestMapping("/appraise")
public class OrderCommentController {
    private static final Logger logger = LoggerFactory.getLogger(OrderCommentController.class);

    private final MrcarOrderRestLocator mrcarOrderRestLocator = new MrcarOrderRestLocator();

    /**
     * 以下为配置管理模块关于评价配置项的相关接口
     */
    /**
     * 分页查询评价项配置信息列表
     * @return
     */
    @RequestMapping("/queryAppraisalConfig")
//    @RequiresPermissions(value = "recommend_choice_manager")
    @RequestFunction(functionName = "评论项配置列表")
    public RestResponse queryAppraisalConfig(@RequestBody JSONObject jsonObject){
        String restUrl = mrcarOrderRestLocator.getRestUrl("/appraise/queryAppraisalConfig");
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.GET,restUrl,jsonObject.getInnerMap(),null, Map.class);

    }

    /**
     * 添加一个评价项
     * @return
     */
    @RequestMapping("/saveAppraisalConfig")
    @RequestFunction(functionName = "新增评论项配置")
    public RestResponse saveAppraisalConfig(@RequestBody JSONObject jsonObject,HttpServletRequest request){
        String restUrl = mrcarOrderRestLocator.getRestUrl("/appraise/saveAppraisalConfig");
        logger.info("【评价项管理】-【添加评价项】-【代理层Controller】入参"+jsonObject.toJSONString());
        try {
            AccountBaseInfo baseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
            jsonObject.getInnerMap().put("createUser", baseInfo.getStaffName());
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.GET,restUrl,jsonObject.getInnerMap(),null, Map.class);
        } catch (Exception e) {
            logger.error("添加评论项异常", e);
            return RestResponse.fail(Constant.SYS_ERROR);
        }


    }

    /**
     * 编辑更新一个评价项
     * @return
     */
    @RequestMapping("/updateAppraisalConfig")
    @RequestFunction(functionName = "编辑评论项配置")
    public RestResponse updateAppraisalConfig(@RequestBody JSONObject jsonObject,HttpServletRequest request){
        String restUrl = mrcarOrderRestLocator.getRestUrl("/appraise/updateAppraisalConfig");
        try {
            AccountBaseInfo baseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
            jsonObject.getInnerMap().put("updateUser", baseInfo.getStaffName());
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.GET,restUrl,jsonObject.getInnerMap(),null, Map.class);
        } catch (Exception e) {
            logger.error("编辑评论项异常", e);
            return RestResponse.fail(Constant.SYS_ERROR);
        }
    }

    /**
     * 删除一个评价项
     * @return
     */
    @RequestMapping("/deleteAppraisalConfig")
    @RequestFunction(functionName = "删除评论项配置")
    public RestResponse deleteAppraisalConfig(@RequestBody JSONObject jsonObject,HttpServletRequest request){
        String restUrl = mrcarOrderRestLocator.getRestUrl("/appraise/deleteAppraisalConfig");
        try {
            AccountBaseInfo baseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
            jsonObject.getInnerMap().put("updateUser", baseInfo.getStaffName());
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.GET,restUrl,jsonObject.getInnerMap(),null, Map.class);
        } catch (Exception e) {
            logger.error("删除评论项异常", e);
            return RestResponse.fail(Constant.SYS_ERROR);
        }

    }


}
