package com.izu.mrcar.providerController.config;

import com.izu.config.MrCarConfigRestLocator;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

@RestController("providerSysStartupPageConfigController")
@RequestMapping("/provider/sysStartupPageConfig")
public class SysStartupPageConfigController {

    private static final Logger logger = LoggerFactory.getLogger(SysStartupPageConfigController.class);

    private final MrCarConfigRestLocator configRestLocator = new MrCarConfigRestLocator();

    @RequestMapping(value = "/detail")
    @RequestFunction(functionName = "企业开屏详情")
    public RestResponse detail(HttpServletRequest request){
        String restUrl = configRestLocator.getRestUrl("/sysStartupPageConfig/detail");

        try {
            Map<String,Object> paraMap = new HashMap<>();
            paraMap.put("companyId", LoginSessionUtil.getBaseLoginInfo().obtainBelongCompanyId());
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Object.class);
        } catch(Exception e) {
            logger.error("detail Exception：", e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }

    @RequestMapping(value = "/add")
    public RestResponse add(@RequestBody Map<String,Object> paraMap, HttpServletRequest request){
        String restUrl = configRestLocator.getRestUrl("/sysStartupPageConfig/add");

        try {
            LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
            paraMap.put("companyId", baseLoginInfo.obtainBelongCompanyId());
            paraMap.put("createName", baseLoginInfo.obtainBaseInfo().getStaffName());
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Object.class);
        } catch(Exception e) {
            logger.error("add Exception：", e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }

    @RequestMapping(value = "/update")
    public RestResponse update(@RequestBody Map<String,Object> paraMap, HttpServletRequest request){
        String restUrl = configRestLocator.getRestUrl("/sysStartupPageConfig/update");

        try {
            LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
            paraMap.put("companyId", baseLoginInfo.obtainBelongCompanyId());
            paraMap.put("updateName", baseLoginInfo.obtainBaseInfo().getStaffName());
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Object.class);
        } catch(Exception e) {
            logger.error("update Exception：", e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }

    @RequestMapping(value = "/SetDefaultConfig")
    public RestResponse SetDefaultConfig(@RequestBody Map<String,Object> paraMap, HttpServletRequest request){
        String restUrl = configRestLocator.getRestUrl("/sysStartupPageConfig/SetDefaultConfig");
        try {
            paraMap.put("companyId", LoginSessionUtil.getBaseLoginInfo().obtainBelongCompanyId());
            return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null);
        } catch(Exception e) {
            logger.error("SetDefaultConfig Exception：", e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }
}
