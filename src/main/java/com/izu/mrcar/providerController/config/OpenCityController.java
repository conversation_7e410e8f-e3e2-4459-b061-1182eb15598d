package com.izu.mrcar.providerController.config;

import com.izu.config.MrCarConfigRestLocator;
import com.izu.config.dto.SaveCityResDTO;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.enums.ProviderTypeEnum;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 商务车城市配置
 * @date 2023/3/6 16:49
 */
@RequestMapping("/city")
@RestController
@Api(tags = "城市配置")
public class OpenCityController {

    @Value("${mrcar-config-core.host.url}")
    private String CONFIG_HOST_URL;

    private final MrCarConfigRestLocator configRestLocator = new MrCarConfigRestLocator();


    @RequestMapping(value = "/listBussCityForPage")
    @RequestFunction(functionName = "开城列表")
    public RestResponse listBussCityForPage(@RequestBody Map<String,Object> paraMap){
        String restUrl = configRestLocator.getRestUrl("/city/listBussCityForPage");
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        paraMap.put("companyId", providerLoginInfo.obtainBelongCompanyId());
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Map.class);
    }

    @RequestMapping(value = "/closeCity")
    @RequestFunction(functionName = "取消开城")
    public RestResponse closeCity(@RequestBody Map<String,Object> paraMap){
        String restUrl =configRestLocator.getRestUrl( "/city/closeCity");
    	/*Map<String, Object> paraMap = new HashMap<String, Object>();
    	paraMap.put("pageSize", pageSize);
    	paraMap.put("pageNo", pageNo);
    	paraMap.put("provinceCode", provinceCode);
    	paraMap.put("cityCode", cityCode);
        */
        // 获取当前登录用户信息
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        paraMap.put("loginCompanyId", providerLoginInfo.obtainBelongCompanyId());
        paraMap.put("companyAttribute", ProviderTypeEnum.RENT_CAR_SERVICE.getType());
        paraMap.put("loginId", providerLoginInfo.getBaseInfo().getStaffId());
        paraMap.put("loginName", providerLoginInfo.getBaseInfo().getStaffName());
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Object.class);
    }

    @RequestMapping(value = "/openCity")
    @RequestFunction(functionName = "开通城市")
    public RestResponse openCity(@RequestBody Map<String,Object> paraMap){
        String restUrl =configRestLocator.getRestUrl("/city/openCity");
    	/*Map<String, Object> paraMap = new HashMap<String, Object>();
    	paraMap.put("pageSize", pageSize);
    	paraMap.put("pageNo", pageNo);
    	paraMap.put("provinceCode", provinceCode);
    	paraMap.put("cityCode", cityCode);
        */
        // 获取当前登录用户信息
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        paraMap.put("loginCompanyId", providerLoginInfo.obtainBelongCompanyId());
        paraMap.put("companyAttribute", ProviderTypeEnum.RENT_CAR_SERVICE.getType());
        paraMap.put("loginId", providerLoginInfo.getBaseInfo().getStaffId());
        paraMap.put("loginName", providerLoginInfo.getBaseInfo().getStaffName());
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Object.class);
    }

    @PostMapping("/v2/saveCityServiceConfig")
    @RequestFunction(functionName = "开通用车城市")
    @ApiOperation("用车开城")
    public RestResponse<SaveCityResDTO> saveCityServiceConfig(String cityJsonStr){
        Map<String, Object> map = new HashMap<>();
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        map.put("companyId", providerLoginInfo.obtainBelongCompanyId());
        map.put("companyAttribute", ProviderTypeEnum.RENT_CAR_SERVICE.getType());
        map.put("cityJsonStr", cityJsonStr); //cityJsonStr:[{"cityCode":1302},{"cityCode":1404}]
        map.put("loginId", providerLoginInfo.getBaseInfo().getStaffId());
        map.put("loginName", providerLoginInfo.getBaseInfo().getStaffName());
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, configRestLocator.getRestUrl("/city/v2/saveCityServiceConfig"), map, null);
    }
}
