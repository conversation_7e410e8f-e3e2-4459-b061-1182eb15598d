package com.izu.mrcar.providerController.config.charging;

import com.izu.config.MrCarConfigRestLocator;
import com.izu.config.dto.charging.ChargingQuotaCompanyEditDTO;
import com.izu.config.dto.charging.ChargingQuotaCompanyListDTO;
import com.izu.config.dto.charging.ChargingQuotaCompanyReqDTO;
import com.izu.consts.ConfigURI;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

import static com.izu.framework.web.rest.client.BaseHttpClient.POSTBODY_MAP_KEY;

/**
 * <AUTHOR>
 * @version 1.0
 * {@code @date} 2024/5/29 下午4:16
 */
@Api(tags = "聚合充电-运营端-充电客户")
@RestController("providerChargingQuotaCompanyController")
@RequestMapping("/provider")
public class ChargingQuotaCompanyController {


    @PostMapping(ConfigURI.CHARGING_QUOTA_COMPANY_PAGE_LIST)
    @ApiOperation(value = "分页查询充电客户列表")
    public RestResponse<PageDTO<ChargingQuotaCompanyListDTO>> list(@RequestBody ChargingQuotaCompanyReqDTO params) {
        Map<String, Object> httpParams = new HashMap<>();
        httpParams.put(POSTBODY_MAP_KEY, params);
        String url = new MrCarConfigRestLocator().getRestUrl(ConfigURI.CHARGING_QUOTA_COMPANY_PAGE_LIST);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, httpParams, null, ChargingQuotaCompanyListDTO.class);
    }

    @ApiOperation(value = "编辑充电客户")
    @PostMapping(ConfigURI.CHARGING_QUOTA_COMPANY_EDIT)
    public RestResponse<Boolean> edit(@RequestBody ChargingQuotaCompanyEditDTO params) {
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        params.setLoginUserId(loginBaseInfo.obtainBaseInfo().getStaffId());
        params.setLoginUserName(loginBaseInfo.obtainBaseInfo().getStaffName());
        params.setLoginCompanyId(loginBaseInfo.obtainBelongCompanyId());
        params.setLoginCompanyName(loginBaseInfo.obtainBelongCompanyName());
        params.setMobile(loginBaseInfo.obtainBaseInfo().getMobile());
        Map<String, Object> httpParams = new HashMap<>();
        httpParams.put(POSTBODY_MAP_KEY, params);
        String url = new MrCarConfigRestLocator().getRestUrl(ConfigURI.CHARGING_QUOTA_COMPANY_EDIT);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, httpParams, null, Boolean.class);
    }

}
