package com.izu.mrcar.providerController.config.lingsan;

import com.izu.config.MrCarConfigRestLocator;
import com.izu.config.dto.lingsan.*;
import com.izu.consts.ConfigURI;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.lingsan.ProviderLingsanDataPermUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.dto.company.lingsan.CompanySimpleRespDTO;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
* @Description: 零散用车价格配置
* @author: hxc
* @Date: 2023/1/30
**/
@RestController
@Api(tags = {"零散用车-设置"})
@Slf4j
public class PriceConfigController {

    private final MrCarConfigRestLocator mrCarConfigRestLocator = new MrCarConfigRestLocator();

    private final UserRestLocator userRestLocator = new UserRestLocator();

    /**
     * 零散用车价格配置
     * @param param
     * @return
     */
    @PostMapping(ConfigURI.LINGSAN_PRICE_CONFIG_CREATE)
    @RequestFunction(functionName = "零散用车价格配置新建")
    @ApiOperation(value = "零散用车-价格配置创建",notes = "作者：贺新春")
    public RestResponse<Boolean> create(@Valid @RequestBody SoPriceConfigCreateReqDTO param){

        AccountBaseInfo baseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
        param.setLoginUserId(baseInfo.getStaffId());
        param.setLoginUserName(baseInfo.getStaffName());
        String url = mrCarConfigRestLocator.getRestUrl(ConfigURI.LINGSAN_PRICE_CONFIG_CREATE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    /**
     * 零散用车费用项设置
     * @param param
     * @return
     */
    @PostMapping(ConfigURI.LINGSAN_PRICE_CONFIG_SET_ITEM)
    @RequestFunction(functionName = "零散用车价格配置新建")
    @ApiOperation(value = "零散用车-费用项设置",notes = "作者：贺新春")
    public RestResponse<Boolean> setPriceItem(@Valid @RequestBody SoPriceConfigReqDTO param){

        AccountBaseInfo baseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
        param.setLoginUserId(baseInfo.getStaffId());
        param.setLoginUserName(baseInfo.getStaffName());
        String url = mrCarConfigRestLocator.getRestUrl(ConfigURI.LINGSAN_PRICE_CONFIG_SET_ITEM);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    /**
     * 零散用车价格配置启用/停用
     * @param param
     * @return
     */
    @PostMapping(ConfigURI.LINGSAN_PRICE_CONFIG_SWITCH)
    @RequestFunction(functionName = "零散用车价格配置启用/停用")
    @ApiOperation(value = "零散用车-价格配置启用/停用",notes = "作者：贺新春")
    public RestResponse<Boolean> priceSwitch(@Valid @RequestBody SoPriceConfigSwitchReqDTO param){

        AccountBaseInfo baseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
        param.setLoginUserId(baseInfo.getStaffId());
        param.setLoginUserName(baseInfo.getStaffName());
        String url = mrCarConfigRestLocator.getRestUrl(ConfigURI.LINGSAN_PRICE_CONFIG_SWITCH);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    /**
     * 零散用车价格配置分页列表查询
     * @param param
     * @return
     */
    @PostMapping(ConfigURI.LINGSAN_PRICE_CONFIG_GET_PAGE_LIST)
    @RequestFunction(functionName = "零散用车价格配置列表")
    @ApiOperation(value = "零散用车-价格配置列表",notes = "作者：贺新春")
    public RestResponse<PageDTO<SoPriceConfigPageRespDTO>> getPageList(@RequestBody SoPriceConfigPageReqDTO param){

        Set<String> datePermDeptCode = ProviderLingsanDataPermUtil.getDatePermDeptCode();
        String restUrl =  userRestLocator.getRestUrl(UserUrlCenter.PROVIDER_LING_SAN_GET_COMPANY_SIMPLE_LIST);
        Map<String, Object> params = new HashMap<>();
        params.put("deptIds",String.join(",",datePermDeptCode));
        RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.GET, restUrl, params, null, CompanySimpleRespDTO.class);
        if (!restResponse.isSuccess() || restResponse.getData() == null){
            return RestResponse.success(new PageDTO(param.getPage(),param.getPageSize(), 0, Collections.emptyList()));
        }
        List<CompanySimpleRespDTO> companySimpleRespDTOS = (List<CompanySimpleRespDTO>) restResponse.getData();
        if (companySimpleRespDTOS == null || companySimpleRespDTOS.isEmpty()){
            return RestResponse.success(new PageDTO(param.getPage(),param.getPageSize(), 0, Collections.emptyList()));
        }
        param.setDataPerms(companySimpleRespDTOS.stream().map(CompanySimpleRespDTO::getCompanyId).collect(Collectors.toList()));
        String url = mrCarConfigRestLocator.getRestUrl(ConfigURI.LINGSAN_PRICE_CONFIG_GET_PAGE_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, SoPriceConfigPageRespDTO.class);
    }

    /**
     * 零散用车价格配置详情
     * @param priceId
     * @return
     */
    @GetMapping(ConfigURI.LINGSAN_PRICE_CONFIG_GET_DETAIL)
    @RequestFunction(functionName = "零散用车价格配置详情")
    @ApiImplicitParam(name = "priceId",value = "价格配置ID",required = true)
    @ApiOperation(value = "零散用车-价格配置详情",notes = "作者：贺新春")
    public RestResponse<SoPriceConfigDetailDTO> getPriceConfigDetail(Integer priceId){

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("priceId",priceId);
        String url = mrCarConfigRestLocator.getRestUrl(ConfigURI.LINGSAN_PRICE_CONFIG_GET_DETAIL);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.GET, url, paramMap, null, SoPriceConfigDetailDTO.class);
    }

    /**
     * 获取车级列表通过企业ID
     * @return
     */
    @GetMapping(ConfigURI.PROVIDER_LINGSAN_PRICE_CONFIG_GET_LEVEL_BY_COMPANY)
    @ApiOperation(value = "价格配置列表",notes = "作者：贺新春")
    @ApiImplicitParam(name = "companyId",value = "价格配置ID",required = true)
    public RestResponse<List<SoPriceConfigCarLevelRespDTO>> getLevelListByCompanyId(Integer companyId){

        String restUrl = mrCarConfigRestLocator.getRestUrl(ConfigURI.PROVIDER_LINGSAN_PRICE_CONFIG_GET_LEVEL_BY_COMPANY);
        Map<String,Object> paraMap = new HashMap<>();
        paraMap.put("companyId", companyId);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.GET, restUrl, paraMap, null);
    }

}
