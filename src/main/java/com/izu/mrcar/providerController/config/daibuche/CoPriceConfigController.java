package com.izu.mrcar.providerController.config.daibuche;

import com.izu.config.MrCarConfigRestLocator;
import com.izu.config.dto.CityDTO;
import com.izu.config.dto.daibuche.*;
import com.izu.config.dto.lingsan.*;
import com.izu.consts.ConfigURI;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.lingsan.ProviderLingsanDataPermUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.dto.company.lingsan.CompanySimpleRespDTO;
import com.izu.user.dto.provider.staff.ProviderStaffDropdownRespDTO;
import com.izu.user.dto.provider.staff.ProviderStaffOrderReqDTO;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
* @Description: 代步车账期，城市及价格配置
* @author: ljw
* @Date: 2024-5-17 10:43:33
**/
@RestController
@Api(tags = {"代步车-配置"})
@Slf4j
public class CoPriceConfigController {

    private final MrCarConfigRestLocator mrCarConfigRestLocator = new MrCarConfigRestLocator();

    private final UserRestLocator userRestLocator = new UserRestLocator();

    /**
     * 代步车账期设置
     * @param param
     * @return
     */
    @PostMapping(ConfigURI.CO_CONTRACT_BILL_CONFIG_SAVE_AND_UPDATE)
    @RequestFunction(functionName = "代步车账期设置")
    @ApiOperation(value = "代步车-合同账期设置",notes = "作者：连江伟")
    public RestResponse<Boolean> create(@Valid @RequestBody CoContractBillConfigSaveDTO param){
        AccountBaseInfo baseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
        param.setLoginUserId(baseInfo.getStaffId());
        param.setLoginUserName(baseInfo.getStaffName());
        String url = mrCarConfigRestLocator.getRestUrl(ConfigURI.CO_CONTRACT_BILL_CONFIG_SAVE_AND_UPDATE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    /**
     * 代步车合同账期分页查询
     * @param param
     * @return
     */
    @PostMapping(ConfigURI.CO_CONTRACT_BILL_CONFIG_PAGE_LIST)
    @RequestFunction(functionName = "代步车合同账期分页查询")
    @ApiOperation(value = "代步车-价合同账期列表",notes = "作者：连江伟")
    public RestResponse<PageDTO<CoContractBillConfigDTO>> getPageList(@RequestBody CoContractBillConfigSearchDTO param){
        String url = mrCarConfigRestLocator.getRestUrl(ConfigURI.CO_CONTRACT_BILL_CONFIG_PAGE_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, CoContractBillConfigDTO.class);
    }

    @PostMapping(ConfigURI.CO_CITY_CONFIG_SAVE_AND_UPDATE)
    @RequestFunction(functionName = "代步车城市设置")
    @ApiOperation(value = "代步车-城市设置保存及编辑",notes = "作者：连江伟")
    public RestResponse<Boolean> cityConfigSave(@Valid @RequestBody CoOrderCityConfigSaveDTO param){
        AccountBaseInfo baseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
        param.setLoginUserId(baseInfo.getStaffId());
        param.setLoginUserName(baseInfo.getStaffName());
        String url = mrCarConfigRestLocator.getRestUrl(ConfigURI.CO_CITY_CONFIG_SAVE_AND_UPDATE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(ConfigURI.CO_CITY_CONFIG_PAGE_LIST)
    @RequestFunction(functionName = "代步车城市设置分页查询")
    @ApiOperation(value = "代步车-城市设置列表",notes = "作者：连江伟")
    public RestResponse<PageDTO<CoOrderCityConfigDTO>> getCityConfigPageList(@RequestBody CoOrderCityConfigSearchDTO param){
        String url = mrCarConfigRestLocator.getRestUrl(ConfigURI.CO_CITY_CONFIG_PAGE_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, CoOrderCityConfigDTO.class);
    }

    @PostMapping(ConfigURI.CO_CITY_CONFIG_CHANGE_STATUS)
    @RequestFunction(functionName = "代步车城市设置启停")
    @ApiOperation(value = "代步车-城市设置启停",notes = "作者：连江伟")
    public RestResponse<Boolean> cityConfigChangeStatus(@Valid @RequestBody CoOrderCityConfigSaveDTO param){
        AccountBaseInfo baseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
        param.setLoginUserId(baseInfo.getStaffId());
        param.setLoginUserName(baseInfo.getStaffName());
        String url = mrCarConfigRestLocator.getRestUrl(ConfigURI.CO_CITY_CONFIG_CHANGE_STATUS);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(ConfigURI.CO_PRICE_CONFIG_PAGE_LIST)
    @RequestFunction(functionName = "代步车价格设置分页查询")
    @ApiOperation(value = "代步车-价格设置列表",notes = "作者：连江伟")
    public RestResponse<PageDTO<CoPriceConfigDTO>> getPriceConfigPageList(@RequestBody CoPriceConfigSearchDTO param){
        String url = mrCarConfigRestLocator.getRestUrl(ConfigURI.CO_PRICE_CONFIG_PAGE_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, CoPriceConfigDTO.class);
    }

    @PostMapping(ConfigURI.CO_PRICE_CONFIG_SAVE_AND_UPDATE)
    @RequestFunction(functionName = "代步车价格设置")
    @ApiOperation(value = "代步车-价格设置保存及编辑",notes = "作者：连江伟")
    public RestResponse<Boolean> priceConfigSave(@Valid @RequestBody CoPriceConfigSaveDTO param){
        AccountBaseInfo baseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
        param.setLoginUserId(baseInfo.getStaffId());
        param.setLoginUserName(baseInfo.getStaffName());
        String url = mrCarConfigRestLocator.getRestUrl(ConfigURI.CO_PRICE_CONFIG_SAVE_AND_UPDATE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(ConfigURI.CO_PRICE_CONFIG_CHANGE_STATUS)
    @RequestFunction(functionName = "代步车价格设置上下架")
    @ApiOperation(value = "代步车-价格设置上下架",notes = "作者：连江伟")
    public RestResponse<Boolean> cityConfigChangeStatus(@Valid @RequestBody CoPriceConfigSaveDTO param){
        AccountBaseInfo baseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
        param.setLoginUserId(baseInfo.getStaffId());
        param.setLoginUserName(baseInfo.getStaffName());
        String url = mrCarConfigRestLocator.getRestUrl(ConfigURI.CO_PRICE_CONFIG_CHANGE_STATUS);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(UserUrlCenter.CO_GET_PROVIDER_STAFF_LIST)
    @RequestFunction(functionName = "代步车获取维护人员")
    @ApiOperation(value = "代步车-获取维护人员",notes = "作者：连江伟")
    public RestResponse<List<ProviderStaffDropdownRespDTO>> getStaffInfoListForCityConfig(@Valid @RequestBody ProviderStaffOrderReqDTO param){
        String url = userRestLocator.getRestUrl(UserUrlCenter.CO_GET_PROVIDER_STAFF_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, ProviderStaffDropdownRespDTO.class);
    }

    @PostMapping(ConfigURI.CO_CONTRACT_BILL_CONFIG_GET_COMPANY)
    @RequestFunction(functionName = "代步车客户企业")
    @ApiOperation(value = "代步车-获取客户企业下拉列表",notes = "作者：连江伟")
    @ApiImplicitParam(name = "companyName",value = "名称",required = false,paramType="form")
    public RestResponse<List<CoContractBillConfigDTO>> getCoCompanyList(String companyName){
        String url = mrCarConfigRestLocator.getRestUrl(ConfigURI.CO_CONTRACT_BILL_CONFIG_GET_COMPANY);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("companyName", companyName);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POST, url, paramMap, null, CoContractBillConfigDTO.class);
    }

    @PostMapping(ConfigURI.CO_PRICE_CONFIG_CITY_GET_BY_COMPANY)
    @RequestFunction(functionName = "代步车开通城市")
    @ApiOperation(value = "代步车-获取客户企业开通城市",notes = "作者：连江伟")
    @ApiImplicitParam(name = "companyId",value = "客户企业id",required = true,paramType="form")
    public RestResponse<List<CityDTO>> getCoCompanyList(Integer companyId){
        String url = mrCarConfigRestLocator.getRestUrl(ConfigURI.CO_PRICE_CONFIG_CITY_GET_BY_COMPANY);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("companyId", companyId);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POST, url, paramMap, null, CityDTO.class);
    }

    @PostMapping(ConfigURI.CO_CONTRACT_BILL_CONFIG_GET_BY_COMPANY)
    @RequestFunction(functionName = "合同账单通用配置")
    @ApiOperation(value = "合同账单通用配置",notes = "作者：郝浩东")
    public RestResponse<CoContractBillConfigDTO> getConfigByCompany(@RequestParam(value = "companyId",required = true) Integer companyId,
                                                    @RequestParam(value = "companyCode",required = false) String companyCode){
        String url = mrCarConfigRestLocator.getRestUrl(ConfigURI.CO_CONTRACT_BILL_CONFIG_GET_BY_COMPANY);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("companyId", companyId);
        paramMap.put("companyCode", companyCode);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, url, paramMap, null,CoContractBillConfigDTO.class);
    }

}
