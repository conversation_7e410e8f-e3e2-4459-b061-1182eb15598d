package com.izu.mrcar.providerController.config.daibuche;


import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.consts.MrcarAssetRestMsgCenter;
import com.izu.asset.dto.vehicle.req.VehicleGradeReqDTO;
import com.izu.asset.dto.vehicle.req.VehicleGradeSaveOrUpdateReqDTO;
import com.izu.asset.dto.vehicle.resp.VehicleGradeRespDTO;
import com.izu.config.MrCarConfigRestLocator;
import com.izu.framework.doc.JrdApiDoc;
import com.izu.framework.doc.JrdDoc;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import java.util.HashMap;

import java.util.List;
import java.util.Map;


@JrdDoc(name = "车级配置")
@RestController
@Slf4j
public class CoVehicleGradeController {
    

    @PostMapping(value = MrcarAssetRestMsgCenter.CO_VEHICLE_GRADE_CONFIG_PAGE_LIST)
    @JrdApiDoc(simpleDesc = "车级配置列表查询", author = "hhd", resDataClass = VehicleGradeRespDTO.class)
    @RequestFunction(functionName = "车级配置列表查询")
    public RestResponse<PageDTO<VehicleGradeRespDTO>> queryListByPage(@RequestBody VehicleGradeReqDTO reqDTO) {
        Map<String, Object> param = new HashMap<>();
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        String restUrl = new MrCarAssetRestLocator().getRestUrl(MrcarAssetRestMsgCenter.CO_VEHICLE_GRADE_CONFIG_PAGE_LIST);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null, VehicleGradeRespDTO.class);
    }

    @JrdApiDoc(simpleDesc = "车级配置保存", author = "hhd", resDataClass = Object.class)
    @PostMapping(value = MrcarAssetRestMsgCenter.CO_VEHICLE_GRADE_CONFIG_SAVE)
    @RequestFunction(functionName = "车级配置保存")
    public RestResponse save(@RequestBody @Validated VehicleGradeSaveOrUpdateReqDTO reqDTO) {
        AccountBaseInfo baseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
        reqDTO.setLoginUserId(baseInfo.getStaffId());
        reqDTO.setLoginUserName(baseInfo.getStaffName());
        String restUrl = new MrCarAssetRestLocator().getRestUrl(MrcarAssetRestMsgCenter.CO_VEHICLE_GRADE_CONFIG_SAVE);
        Map<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }


    @JrdApiDoc(simpleDesc = "车级配置修改", author = "hhd", resDataClass = Object.class)
    @PostMapping(value = MrcarAssetRestMsgCenter.CO_VEHICLE_GRADE_CONFIG_UPDATE)
    @RequestFunction(functionName = "车级配置修改")
    public RestResponse update(@RequestBody @Validated VehicleGradeSaveOrUpdateReqDTO reqDTO) {
        AccountBaseInfo baseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
        reqDTO.setLoginUserId(baseInfo.getStaffId());
        reqDTO.setLoginUserName(baseInfo.getStaffName());
        String restUrl = new MrCarAssetRestLocator().getRestUrl(MrcarAssetRestMsgCenter.CO_VEHICLE_GRADE_CONFIG_UPDATE);
        Map<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }

    @JrdApiDoc(simpleDesc = "车级配置删除", author = "hhd", resDataClass = Object.class)
    @PostMapping(value = MrcarAssetRestMsgCenter.CO_VEHICLE_GRADE_CONFIG_DELETE)
    @RequestFunction(functionName = "车级配置删除")
    public RestResponse delete(@RequestBody @Validated VehicleGradeSaveOrUpdateReqDTO reqDTO) {
        AccountBaseInfo baseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
        reqDTO.setLoginUserId(baseInfo.getStaffId());
        reqDTO.setLoginUserName(baseInfo.getStaffName());
        String restUrl = new MrCarAssetRestLocator().getRestUrl(MrcarAssetRestMsgCenter.CO_VEHICLE_GRADE_CONFIG_DELETE);
        Map<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }

    @PostMapping(value = MrcarAssetRestMsgCenter.CO_VEHICLE_GRADE_CONFIG_LIST)
    @JrdApiDoc(simpleDesc = "车级配置列表查询", author = "hhd", resDataClass = VehicleGradeRespDTO.class)
    @RequestFunction(functionName = "车级配置列表查询")
    public RestResponse<List<VehicleGradeRespDTO>> list(@RequestBody VehicleGradeReqDTO reqDTO) {
        Map<String, Object> param = new HashMap<>();
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        String restUrl = new MrCarAssetRestLocator().getRestUrl(MrcarAssetRestMsgCenter.CO_VEHICLE_GRADE_CONFIG_LIST);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null, VehicleGradeRespDTO.class);
    }
}
