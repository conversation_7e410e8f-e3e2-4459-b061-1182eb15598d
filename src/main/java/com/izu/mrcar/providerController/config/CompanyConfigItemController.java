package com.izu.mrcar.providerController.config;

import com.izu.config.dto.*;
import com.izu.consts.ConfigURI;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.controller.base.ConfigBaseController;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;

/**
 * 企业配置项接口
 *
 * <AUTHOR> on  2024/12/18 下午8:25
 */
@RestController
@RequestMapping(value = "/provider")
public class CompanyConfigItemController extends ConfigBaseController {


    /**
     * 根据公司ID获取配置列表
     *
     * @param companyId  公司ID
     * @param configType 配置类型 0-代表空，1-内部用车通用配置 config_type
     * @return 配置项列表
     */
    @GetMapping(value = ConfigURI.COMPANY_CONFIG_ITEM_LIST)
    public RestResponse<List<CompanyConfigItemValueDTO>> getCompanyConfigItemList(@Verify(param = "companyId", rule = "required") Integer companyId,
                                                                                  @Verify(param = "configType", rule = "required") Integer configType) {


        HashMap<String, Object> restParam = new HashMap<>();

        restParam.put("companyId", companyId);

        restParam.put("configType", configType);

        return get(ConfigURI.COMPANY_CONFIG_ITEM_LIST, restParam);

    }


    /**
     * 编辑某公司的配置项的值
     *
     * @param reqDTO 配置项请求参数
     * @return boolean
     */
    @PostMapping(value = ConfigURI.COMPANY_CONFIG_ITEM_MODIFY, produces = "application/json")
    public RestResponse<Boolean> modifyCompanyConfigItem(@RequestBody CompanyConfigItemModifyReqDTO reqDTO) {

        setLastModifierInfo(reqDTO);

        return postBody(ConfigURI.COMPANY_CONFIG_ITEM_MODIFY, reqDTO);
    }


    /**
     * 校验当前配置下下拉选项值是否为空
     *
     * @param companyId 公司ID
     * @param itemId    每一项的ID
     * @return true:为空，false:不为空
     */
    @GetMapping(value = ConfigURI.COMPANY_CONFIG_ITEM_OPTION_CHECK)
    public RestResponse<Boolean> checkCompanyConfigItemOptionIsEmpty(@Verify(param = "itemId", rule = "required") Integer itemId,
                                                                     @Verify(param = "companyId", rule = "required") Integer companyId) {

        HashMap<String, Object> restParam = new HashMap<>();

        restParam.put("itemId", itemId);
        restParam.put("companyId", companyId);

        return get(ConfigURI.COMPANY_CONFIG_ITEM_OPTION_CHECK, restParam);
    }

    /**
     * 获取配置下所有的选项列表
     *
     * @param companyId 公司ID
     * @param itemId    每一项的ID
     * @return
     */
    @GetMapping(value = ConfigURI.COMPANY_CONFIG_VALUE_OPTION_LIST)
    public RestResponse<List<CompanyConfigValueOptionDTO>> getCompanyConfigItemOptionList(@Verify(param = "itemId", rule = "required") Integer itemId,
                                                                                          @Verify(param = "companyId", rule = "required") Integer companyId) {

        HashMap<String, Object> restParam = new HashMap<>();

        restParam.put("itemId", itemId);
        restParam.put("companyId", companyId);

        return get(ConfigURI.COMPANY_CONFIG_VALUE_OPTION_LIST, restParam);

    }


    /**
     * 新增或者编辑下拉框的选项内容
     */
    @PostMapping(value = ConfigURI.COMPANY_CONFIG_VALUE_OPTION_ADD_OR_UPDATE, produces = "application/json")
    public RestResponse<Boolean> modifyCompanyConfigItemOption(@RequestBody CompanyConfigItemOptionSaveOrModifyReqDTO reqDTO) {

        setLastModifierInfo(reqDTO);

        return postBody(ConfigURI.COMPANY_CONFIG_VALUE_OPTION_ADD_OR_UPDATE, reqDTO);

    }

    /**
     * 删除某个选项
     *
     * @param reqDTO
     * @return
     */
    @PostMapping(value = ConfigURI.COMPANY_CONFIG_VALUE_OPTION_DELETE, produces = "application/json")
    public RestResponse<Boolean> deleteCompanyConfigItemOption(@RequestBody CompanyConfigItemOptionOperateReqDTO reqDTO) {

        setLastModifierInfo(reqDTO);

        return postBody(ConfigURI.COMPANY_CONFIG_VALUE_OPTION_DELETE, reqDTO);


    }

    /**
     * 将某个选项设置为默认选中
     */
    @PostMapping(value = ConfigURI.COMPANY_CONFIG_VALUE_OPTION_DEFAULT_CHECKED, produces = "application/json")
    public RestResponse<Boolean> setCompanyConfigItemOptionDefault(@RequestBody CompanyConfigItemOptionOperateReqDTO reqDTO) {

        setLastModifierInfo(reqDTO);

        return postBody(ConfigURI.COMPANY_CONFIG_VALUE_OPTION_DEFAULT_CHECKED, reqDTO);
    }


    //设置修改或者新增人信息
    private void setLastModifierInfo(CompanyConfigItemBaseDTO reqDTO) {

        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();

        reqDTO.setLastModifierCode(baseLoginInfo.obtainBaseInfo().getStaffCode());

        reqDTO.setLastModifierName(baseLoginInfo.obtainBaseInfo().getStaffName());
    }


}
