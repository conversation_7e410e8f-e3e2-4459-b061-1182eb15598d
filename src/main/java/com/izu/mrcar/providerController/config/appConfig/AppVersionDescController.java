package com.izu.mrcar.providerController.config.appConfig;


import com.izu.config.dto.appConfig.AppVersionDescPageListReqDTO;
import com.izu.config.dto.appConfig.AppVersionDescRespDTO;
import com.izu.config.dto.appConfig.AppVersionDescSaveReqDTO;
import com.izu.config.dto.appConfig.AppVersionDescUpdateReqDTO;
import com.izu.consts.ConfigURI;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.controller.base.ConfigBaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;

/**
 * app版本说明配置
 */
@RestController
@Slf4j
@Api(tags = "app配置")
public class AppVersionDescController extends ConfigBaseController {


    @RequestFunction(functionName = "app版本说明配置-保存")
    @ApiOperation("app版本说明配置-保存")
    @PostMapping(ConfigURI.PROVIDER_APP_VERSION_DESC_SAVE)
    public RestResponse<Boolean> save(@RequestBody AppVersionDescSaveReqDTO reqDTO) {
        return postBodyWithLogin(ConfigURI.PROVIDER_APP_VERSION_DESC_SAVE, reqDTO);
    }


    @RequestFunction(functionName = "app版本说明配置-修改")
    @ApiOperation("app版本说明配置-修改")
    @PostMapping(ConfigURI.PROVIDER_APP_VERSION_DESC_UPDATE)
    public RestResponse<Boolean> update(@RequestBody AppVersionDescUpdateReqDTO reqDTO) {
        return postBodyWithLogin(ConfigURI.PROVIDER_APP_VERSION_DESC_UPDATE, reqDTO);
    }


    @RequestFunction(functionName = "app版本说明配置-分页列表")
    @ApiOperation("app版本说明配置-分页列表")
    @PostMapping(ConfigURI.PROVIDER_APP_VERSION_DESC_PAGE_LIST)
    public RestResponse<PageDTO<AppVersionDescRespDTO>> pageList(@RequestBody AppVersionDescPageListReqDTO reqDTO) {
        return postBodyWithLogin(ConfigURI.PROVIDER_APP_VERSION_DESC_PAGE_LIST, reqDTO);
    }


    @RequestFunction(functionName = "app版本说明配置-详情")
    @ApiOperation("app版本说明配置-详情")
    @ApiImplicitParam(name = "descId",value = "配置说明主键",required = true,paramType="form")
    @RequestMapping(value = ConfigURI.PROVIDER_APP_VERSION_DESC_DETAIL, method = {RequestMethod.GET, RequestMethod.POST})
    public RestResponse<AppVersionDescRespDTO> detail(@Verify(param = "descId", rule = "required") Integer descId) {
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("descId", descId);
        return post(ConfigURI.PROVIDER_APP_VERSION_DESC_DETAIL, paramMap);
    }

    @RequestFunction(functionName = "app版本说明配置-删除")
    @ApiOperation("app版本说明配置-删除")
    @ApiImplicitParam(name = "descId",value = "配置说明主键",required = true,paramType="form")
    @RequestMapping(value = ConfigURI.PROVIDER_APP_VERSION_DESC_DELETE, method = {RequestMethod.GET, RequestMethod.POST})
    public RestResponse<Boolean> delete(@Verify(param = "descId", rule = "required") Integer descId) {
        HashMap<String, Object> paramMap = new HashMap<>();
        paramMap.put("descId", descId);
        return post(ConfigURI.PROVIDER_APP_VERSION_DESC_DELETE, paramMap);
    }
}
