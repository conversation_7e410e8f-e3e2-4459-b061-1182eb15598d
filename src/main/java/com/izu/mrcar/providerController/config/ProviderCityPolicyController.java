package com.izu.mrcar.providerController.config;

import com.izu.consts.ConfigURI;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.config.RestUrlConfig;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-02-14 11:07
 */
@Api(tags = "价格配置")
@RestController
public class ProviderCityPolicyController {

    @PostMapping(ConfigURI.PROVIDER_CITY_POLICY_COPY)
    @RequestFunction(functionName = "拷贝基础价格策略")
    @ApiOperation("拷贝基础价格策略")
    public RestResponse copyOtherCity(@Verify(param = "cityCodes", rule = "required") String cityCodes){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        Map<String, Object> params = new HashMap<>();
        params.put("cityCodes", cityCodes);
        params.put("companyId", loginBaseInfo.obtainBelongCompanyId());
        params.put("companyName", loginBaseInfo.obtainBelongCompanyName());
        params.put("createId", loginBaseInfo.obtainBaseInfo().getStaffId());
        params.put("createName", loginBaseInfo.obtainBaseInfo().getStaffName());
        params.put("companyAttribute", 1);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, RestUrlConfig.getConfig().getConfigCoreUrl() + "cityPolicy/copyBasicPolicy", params, null);
    }


    @PostMapping(ConfigURI.PROVIDER_CITY_POLICY_SAVE)
    @RequestFunction(functionName = "保存城市价格策略")
    @ApiOperation("保存城市价格策略")
    public RestResponse saveCityPolicy(@Verify(param = "list", rule = "required") String list, @Verify(param = "cityCodes", rule = "required") String cityCodes){
        Map<String, Object> params = new HashMap<>();
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        params.put("companyId", loginBaseInfo.obtainBelongCompanyId());
        params.put("createId", loginBaseInfo.obtainBaseInfo().getStaffId());
        params.put("createName", loginBaseInfo.obtainBaseInfo().getStaffName());
        params.put("companyName", loginBaseInfo.obtainBelongCompanyName());
        params.put("companyAttribute", 1);
        params.put("cityCodes", cityCodes);
        params.put("list", list);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, RestUrlConfig.getConfig().getConfigCoreUrl() + "cityPolicy/saveCityPolicy", params, null);
    }


}
