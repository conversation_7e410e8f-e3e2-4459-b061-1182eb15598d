package com.izu.mrcar.providerController.config;

import com.izu.config.MrCarConfigRestLocator;
import com.izu.config.dto.MileageCorrectConfigDTO;
import com.izu.consts.ConfigURI;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
* @Description: 里程纠偏配置
* @author: ljw
* @Date: 2023-7-25 14:55:31
**/
@RestController
@Api(tags="里程纠偏")
@Slf4j
public class MileageCorrectConfigController {

    private final MrCarConfigRestLocator mrCarConfigRestLocator = new MrCarConfigRestLocator();

    /**
     * 获取平台里程纠偏配置信息
     * @return
     */
    @GetMapping(ConfigURI.PROVIDER_GET_MILEAGE_CORRECT_CONFIG)
    @ApiOperation(value = "里程纠偏配置查询",notes = "作者：连江伟")
    public RestResponse<MileageCorrectConfigDTO> getMileageCorrectConfig(){
        String restUrl = mrCarConfigRestLocator.getRestUrl(ConfigURI.PROVIDER_GET_MILEAGE_CORRECT_CONFIG);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.GET, restUrl, null, null);
    }

    @PostMapping(ConfigURI.PROVIDER_UPDATE_MILEAGE_CORRECT_CONFIG)
    @ApiOperation(value = "里程纠偏配置修改",notes = "作者：连江伟")
    public RestResponse<Boolean> updateMileageCorrectConfig(@RequestBody MileageCorrectConfigDTO dto){
        String restUrl = mrCarConfigRestLocator.getRestUrl(ConfigURI.PROVIDER_UPDATE_MILEAGE_CORRECT_CONFIG);
        Map<String, Object> paraMap = new HashMap<>(1);
        paraMap.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paraMap, null);
    }

}
