package com.izu.mrcar.providerController.config;

import com.google.common.collect.Maps;
import com.izu.carasset.util.StringUtils;
import com.izu.config.MrCarConfigRestLocator;
import com.izu.config.dto.MrcarCourseDTO;
import com.izu.config.dto.MrcarCourseParams;
import com.izu.consts.ConfigURI;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.RandomStringUtil;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpMethod;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * @program: mrcar-config-core
 * @description: 使用教程controller类
 * @author: ljw
 * @create: 2021-09-07 10:27
 **/
@RestController("providerMrcarCourseController")
@Api(tags = "帮助中心")
public class MrcarCourseController {
    private static final Logger logger = LoggerFactory.getLogger(MrcarCourseController.class);

    private final MrCarConfigRestLocator mrCarConfigRestLocator = new MrCarConfigRestLocator();

    @PostMapping(ConfigURI.PROVIDER_COURSE_ADD)
    @RequestFunction(functionName = "使用教程-新增")
    @ApiOperation(value = "使用教程-新增",notes = "作者：贺新春")
    public RestResponse add(@RequestBody MrcarCourseDTO courseDTO){
        AccountBaseInfo baseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
        courseDTO.setCreateName(baseInfo.getStaffName());
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, courseDTO);
        String restUrl =  mrCarConfigRestLocator.getRestUrl(ConfigURI.PROVIDER_COURSE_ADD);
        return RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, RestResponse.class);
    }

    @PostMapping(ConfigURI.PROVIDER_COURSE_UPD)
    @RequestFunction(functionName = "使用教程-编辑")
    @ApiOperation(value = "使用教程-编辑",notes = "作者：贺新春")
    public RestResponse upd(@RequestBody MrcarCourseDTO courseDTO){
        AccountBaseInfo baseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
        courseDTO.setUpdateName(baseInfo.getStaffName());
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, courseDTO);
        String restUrl =  mrCarConfigRestLocator.getRestUrl(ConfigURI.PROVIDER_COURSE_UPD);
        return RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, RestResponse.class);
    }

    @PostMapping(ConfigURI.PROVIDER_COURSE_DEL)
    @RequestFunction(functionName = "使用教程-删除")
    @ApiOperation(value = "使用教程-删除",notes = "作者：贺新春")
    public RestResponse del(@RequestBody MrcarCourseDTO courseDTO){
        AccountBaseInfo baseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
        courseDTO.setUpdateName(baseInfo.getStaffName());
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, courseDTO);
        String restUrl =  mrCarConfigRestLocator.getRestUrl(ConfigURI.PROVIDER_COURSE_DEL);
        return RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, RestResponse.class);
    }

    @PostMapping(ConfigURI.PROVIDER_COURSE_PAGE_LIST)
    @RequestFunction(functionName = "使用教程-查询列表")
    @ApiOperation(value = "使用教程-分页列表",notes = "作者：贺新春")
    public RestResponse pageList(@RequestBody MrcarCourseParams params){
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, params);
        String restUrl =  mrCarConfigRestLocator.getRestUrl(ConfigURI.PROVIDER_COURSE_PAGE_LIST);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, Map.class);
    }

    private File getFile(final String url, final String fileName) {
        String substring = fileName.substring(0, fileName.lastIndexOf("."));
        String substring1 = fileName.substring(fileName.lastIndexOf("."));
        String excelFileName = substring + "-" + System.currentTimeMillis() + substring1;
        final String baseDir = System.getProperty("java.io.tmpdir") + File.separator + "upload";
        String storeFilePath = baseDir + File.separator + excelFileName;
        String lenthStr = storeFilePath.replaceAll( "[^\\x00-\\xff]" , "**" );
        if (lenthStr.length()>255){
            storeFilePath = baseDir + File.separator + RandomStringUtil.genRandomString(8)+substring1;
        }
        logger.info("上传文件名称={}, 存储保存路径={}", excelFileName, storeFilePath);
        Map<String, String> httpHeader = Maps.newHashMapWithExpectedSize(1);
        //有防盗链机制，必须加上Referer
        httpHeader.put("Referer", "https://prd-third.izuche.com");
        boolean downloadOK = BaseHttpClient.download(BaseHttpClient.HttpMethod.GET, url, null, httpHeader, storeFilePath);
        if (downloadOK) {
            logger.info("文件下载成功");
            return new File(storeFilePath);
        } else {
            logger.error("文件下载失败");
            return null;
        }
    }

    @GetMapping(ConfigURI.PROVIDER_COURSE_DOWNLOAD)
    @RequestFunction(functionName = "使用教程-下载")
    @ApiOperation(value = "使用教程-下载",notes = "作者：贺新春")
    public RestResponse downLoadFile(HttpServletResponse response, HttpServletRequest request,Integer id){
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put("id", id);
        String restUrl =  mrCarConfigRestLocator.getRestUrl(ConfigURI.PROVIDER_COURSE_GET);
        RestResponse res = RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paramMap, null, MrcarCourseDTO.class);
        if(null!=res && res.isSuccess() && res.getData()!=null){
            MrcarCourseDTO dto = (MrcarCourseDTO) res.getData();
            final File file = this.getFile(dto.getCourseUrl(), dto.getFileName());
            if (file != null) {
                try (InputStream is = new FileInputStream(file)) {
                    this.downloadLocal(request, response, is, dto.getFileName());
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

        return null;
    }

    public void downloadLocal(HttpServletRequest request, HttpServletResponse response, InputStream inputStream, String fileName) {
        // 设置输出的格式
        MrcarCourseController.setResponse(request, response, fileName);
        // 循环取出流中的数据
        byte[] b = new byte[100];
        int len;
        try {
            while ((len = inputStream.read(b)) > 0) {
                response.getOutputStream().write(b, 0, len);
            }
            inputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    protected static void setResponse(HttpServletRequest request, HttpServletResponse response, String fileName) {
        try {
            //根据文件名，获知文件类型。
            //扩展名
            final String excelType = fileName.substring(fileName.trim().lastIndexOf(".") + 1).toLowerCase();
            // 决定文件名(需要对不同浏览器进行兼容，尤其是Firefox)
            String ua = request.getHeader("User-Agent");
            //如果没有UA，则采用英文文件名
            if (StringUtils.isNotBlank(ua)) {
                fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.name());
                //UA是Firefox
                if (ua.toLowerCase().contains("firefox")) {
                    if (!HttpMethod.POST.name().equalsIgnoreCase(request.getMethod()) || request.getContentType() == null || !request.getContentType().toLowerCase().contains("application/json")) {
                        fileName = new String(fileName.getBytes(StandardCharsets.UTF_8), "ISO8859-1");
                    }
                }
            }
            response.reset();
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
            response.setHeader("Download-Filename", URLEncoder.encode(fileName, "UTF8"));//这是和前端约定的自定义响应头
            if ("xls".equalsIgnoreCase(excelType)) {
                response.setContentType("application/vnd.ms-excel;charset=gbk");
            } else if ("xlsx".equalsIgnoreCase(excelType)) {
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=gbk");
            } else if ("pdf".equalsIgnoreCase(excelType)) {
                response.setContentType("application/pdf;charset=gbk");
            } else if ("jpeg".equalsIgnoreCase(excelType) || "jpg".equalsIgnoreCase(excelType)) {
                response.setContentType("image/jpeg;charset=gbk");
            } else if ("png".equalsIgnoreCase(excelType)) {
                response.setContentType("image/png;charset=gbk");
            } else {
                response.setContentType("application/octet-stream;charset=gbk");
            }
            response.setCharacterEncoding("gbk");
            //B3 输出EXCEL
        } catch (Exception e) {
            logger.error("导出文件 [ " + fileName + " ] 发生异常！", e);
        }
    }


}
