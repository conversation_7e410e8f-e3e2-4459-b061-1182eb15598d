package com.izu.mrcar.providerController.config;

import com.google.common.collect.Maps;
import com.izu.config.MrCarConfigRestLocator;
import com.izu.config.dto.TrajectoryConfigDTO;
import com.izu.config.dto.common.BaseDTO;
import com.izu.consts.ConfigURI;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2025/7/16 10:00
 */
@Api(tags = "轨迹染色配置")
@RestController
public class ProviderTrajectoryConfigController {


    @ApiOperation(value = "客户端-配置新增或更新",notes = "作者：丁伟兵")
    @PostMapping(ConfigURI.PROVIDER_SAVE_OR_UPDATE_TRAJECTORY_CONFIG)
    public RestResponse<Void> saveOrUpdate(@RequestBody TrajectoryConfigDTO param) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        param.setLoginCompanyId(0);
        param.setLoginUserId(providerLoginInfo.getBaseInfo().getStaffId());
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        String url = new MrCarConfigRestLocator().getRestUrl(ConfigURI.SAVE_OR_UPDATE_TRAJECTORY_CONFIG);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @ApiOperation(value = "客户端-配置获取",notes = "作者：丁伟兵")
    @GetMapping(ConfigURI.PROVIDER_GET_TRAJECTORY_CONFIG)
    public RestResponse<String> getTrajectoryConfig() {
        BaseDTO param = new BaseDTO();
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        param.setLoginCompanyId(0);
        param.setLoginUserId(providerLoginInfo.getBaseInfo().getStaffId());
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        String url = new MrCarConfigRestLocator().getRestUrl(ConfigURI.GET_TRAJECTORY_CONFIG);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, String.class);
    }
}
