package com.izu.mrcar.providerController.config.daibuche;


import com.google.common.collect.Maps;
import com.izu.config.MrCarConfigRestLocator;
import com.izu.config.dto.daibuche.req.CoOrderWhiteListDelReqDTO;
import com.izu.config.dto.daibuche.req.CoOrderWhiteListQueryReqDTO;
import com.izu.config.dto.daibuche.req.CoOrderWhiteListSaveReqDTO;
import com.izu.config.dto.daibuche.resp.CoOrderWhiteListExportRespDTO;
import com.izu.config.dto.daibuche.resp.CoOrderWhiteListQueryRespDTO;
import com.izu.consts.ConfigURI;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.doc.JrdApiDoc;
import com.izu.framework.doc.JrdDoc;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.providerController.order.daibuche.CoBaseController;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@JrdDoc(name = "代步车-白名单")
@RestController
@Slf4j
public class CoOrderWhiteListController extends CoBaseController {

    private static final Integer PAGE_SIZE = 10000;

    @PostMapping(value = ConfigURI.CO_ORDER_WHITE_LIST)
    @JrdApiDoc(simpleDesc = "白名单列表查询", author = "hhd", resDataClass = CoOrderWhiteListQueryRespDTO.class)
    @RequestFunction(functionName = "白名单列表查询")
    public RestResponse<PageDTO<CoOrderWhiteListQueryRespDTO>> queryListByPage(@RequestBody CoOrderWhiteListQueryReqDTO reqDTO) {
        Map<String, Object> param = new HashMap<>();
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        String restUrl = new MrCarConfigRestLocator().getRestUrl(ConfigURI.CO_ORDER_WHITE_LIST);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null, CoOrderWhiteListQueryRespDTO.class);
    }

    @JrdApiDoc(simpleDesc = "白名单保存", author = "hhd", resDataClass = Object.class)
    @PostMapping(value = ConfigURI.CO_ORDER_WHITE_LIST_SAVE)
    @RequestFunction(functionName = "白名单保存")
    public RestResponse saveOrderWhiteList(@RequestBody @Validated CoOrderWhiteListSaveReqDTO reqDTO) {
        AccountBaseInfo baseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
        reqDTO.setLoginUserId(baseInfo.getStaffId());
        reqDTO.setLoginUserName(baseInfo.getStaffName());
        String restUrl = new MrCarConfigRestLocator().getRestUrl(ConfigURI.CO_ORDER_WHITE_LIST_SAVE);
        Map<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }


    @JrdApiDoc(simpleDesc = "白名单修改", author = "hhd", resDataClass = Object.class)
    @PostMapping(value = ConfigURI.CO_ORDER_WHITE_LIST_UPDATE)
    @RequestFunction(functionName = "白名单修改")
    public RestResponse updateOrderWhiteList(@RequestBody @Validated CoOrderWhiteListSaveReqDTO reqDTO) {
        AccountBaseInfo baseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
        reqDTO.setLoginUserId(baseInfo.getStaffId());
        reqDTO.setLoginUserName(baseInfo.getStaffName());
        String restUrl = new MrCarConfigRestLocator().getRestUrl(ConfigURI.CO_ORDER_WHITE_LIST_UPDATE);
        Map<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }

    @PostMapping(value = ConfigURI.CO_ORDER_WHITE_LIST_DETAIL)
    @JrdApiDoc(simpleDesc = "白名单详情", author = "hhd", resDataClass = CoOrderWhiteListQueryRespDTO.class)
    @RequestFunction(functionName = "白名单详情")
    public RestResponse<CoOrderWhiteListQueryRespDTO> detail(@RequestParam("id") Integer id) {
        Map<String, Object> param = new HashMap<>();
        param.put("id",id);
        String restUrl = new MrCarConfigRestLocator().getRestUrl(ConfigURI.CO_ORDER_WHITE_LIST_DETAIL);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, param, null, CoOrderWhiteListQueryRespDTO.class);
    }

    @PostMapping(value = ConfigURI.CO_ORDER_WHITE_LIST_EXPORT)
    @JrdApiDoc(simpleDesc = "白名单导出", author = "hhd", resDataClass = CoOrderWhiteListQueryRespDTO.class)
    @RequestFunction(functionName = "白名单导出")
    @ExportExcelWeb(fileName = ExportExcelConstants.FILE_NAME_ORDER_WHITE_LIST_EXPORT_INFO,filePath = ExportExcelConstants.FILE_PATH,
            sheet = ExportExcelConstants.SHEET_NAME_ORDER_WHITE_LIST_EXPORT_INFO, c = CoOrderWhiteListExportRespDTO.class)
    public PageDTO<CoOrderWhiteListExportRespDTO> export(@RequestBody CoOrderWhiteListQueryReqDTO reqDTO, IzuEasyExcelSession izuEasyExcelSession,
                                                  HttpServletRequest request, HttpServletResponse response) {
        String restUrl = new MrCarConfigRestLocator().getRestUrl(ConfigURI.CO_ORDER_WHITE_LIST);
        AccountBaseInfo baseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
        izuEasyExcelSession.setUserName(baseInfo.getStaffName());
        reqDTO.setPageNum(izuEasyExcelSession.getPageNo());
        reqDTO.setPageSize(PAGE_SIZE);

        final HashMap<String, Object> restParam = Maps.newHashMapWithExpectedSize(1);
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        RestResponse<PageDTO<CoOrderWhiteListQueryRespDTO>> res = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null, CoOrderWhiteListQueryRespDTO.class);
        if(null!=res && res.isSuccess()){
            PageDTO<CoOrderWhiteListQueryRespDTO> pageDTO = res.getData();
            List<CoOrderWhiteListExportRespDTO> list = BeanUtil.copyList(pageDTO.getResult(),CoOrderWhiteListExportRespDTO.class);
            return new PageDTO<>(pageDTO.getPage(), pageDTO.getPageSize(), pageDTO.getTotal(), list);
        }else {
            return null;
        }

    }

    @ApiOperation(value = "白名单删除")
    @PostMapping(value = ConfigURI.CO_ORDER_WHITE_LIST_DELETE)
    @JrdApiDoc(simpleDesc = "白名单删除", author = "hhd", resDataClass = Object.class)
    @RequestFunction(functionName = "白名单删除")
    public RestResponse deleteOrderWhiteList(@RequestBody CoOrderWhiteListDelReqDTO reqDTO) {
        AccountBaseInfo baseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
        reqDTO.setLoginUserId(baseInfo.getStaffId());
        reqDTO.setLoginUserName(baseInfo.getStaffName());
        Map<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        String restUrl = new MrCarConfigRestLocator().getRestUrl(ConfigURI.CO_ORDER_WHITE_LIST_DELETE);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }

}
