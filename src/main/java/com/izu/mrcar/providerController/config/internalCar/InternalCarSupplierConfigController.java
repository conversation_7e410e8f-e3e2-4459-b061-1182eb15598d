package com.izu.mrcar.providerController.config.internalCar;

import com.izu.config.MrCarConfigRestLocator;
import com.izu.config.dto.internalCar.req.InternalCarSupplierConfigReqDTO;
import com.izu.config.dto.internalCar.resp.InternalCarSupplierConfigRespDTO;
import com.izu.consts.ConfigURI;
import com.izu.framework.doc.JrdApiDoc;
import com.izu.framework.doc.JrdDoc;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import com.izu.user.enums.perm.ClientDataPermTypeEnum;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@JrdDoc(name = "内部用车-多供应商")
@RestController
@Slf4j
public class InternalCarSupplierConfigController  {

    @PostMapping(value = ConfigURI.INTERNAL_CAR_SUPPLIER_LIST)
    @JrdApiDoc(simpleDesc = "多供应商列表查询", author = "hhd", resDataClass = InternalCarSupplierConfigRespDTO.class)
    @RequestFunction(functionName = "多供应商列表查询")
    public RestResponse<List<InternalCarSupplierConfigRespDTO>> queryList(@RequestParam("companyId") Integer companyId) {
        Map<String, Object> param = new HashMap<>();
        param.put("companyId", companyId);
        String restUrl = new MrCarConfigRestLocator().getRestUrl(ConfigURI.INTERNAL_CAR_SUPPLIER_LIST);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, param, null, InternalCarSupplierConfigRespDTO.class);
    }

    @JrdApiDoc(simpleDesc = "多供应商保存", author = "hhd", resDataClass = Object.class)
    @PostMapping(value = ConfigURI.INTERNAL_CAR_SUPPLIER_SAVE)
    @RequestFunction(functionName = "多供应商保存")
    public RestResponse save(@RequestBody @Validated List<InternalCarSupplierConfigReqDTO> list) {
        AccountBaseInfo baseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
        list.forEach(x->{
            x.setLoginUserId(baseInfo.getStaffId());
            x.setLoginUserName(baseInfo.getStaffName());
        });

        String restUrl = new MrCarConfigRestLocator().getRestUrl(ConfigURI.INTERNAL_CAR_SUPPLIER_SAVE);
        Map<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, list);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }

    @ApiOperation(value = "多供应商删除")
    @PostMapping(value = ConfigURI.INTERNAL_CAR_SUPPLIER_DELETE)
    @JrdApiDoc(simpleDesc = "多供应商删除", author = "hhd", resDataClass = Object.class)
    @RequestFunction(functionName = "多供应商删除")
    public RestResponse delete(@RequestParam("idList") List<Integer> idList) {
        Map<String, Object> restParam = new HashMap<>();
        restParam.put("idList", idList);
        String restUrl = new MrCarConfigRestLocator().getRestUrl(ConfigURI.INTERNAL_CAR_SUPPLIER_DELETE);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, restParam, null);
    }

    @PostMapping(value = ConfigURI.INTERNAL_CAR_ASSIGN_SUPPLIER_LIST)
    @JrdApiDoc(simpleDesc = "分配供应商列表查询", author = "hhd", resDataClass = InternalCarSupplierConfigRespDTO.class)
    @RequestFunction(functionName = "分配供应商列表查询")
    public RestResponse<List<InternalCarSupplierConfigRespDTO>> queryAssignList(@RequestParam(value = "customerName",required = false) String customerName) {
        Map<String, Object> param = new HashMap<>();
        ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
        param.put("companyId", clientLoginInfo.obtainBelongCompanyId());
        param.put("customerName", customerName);
        String restUrl = new MrCarConfigRestLocator().getRestUrl(ConfigURI.INTERNAL_CAR_ASSIGN_SUPPLIER_LIST);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, param, null, InternalCarSupplierConfigRespDTO.class);
    }

}
