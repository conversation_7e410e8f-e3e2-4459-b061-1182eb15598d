package com.izu.mrcar.providerController.config;

import com.izu.config.MrCarConfigRestLocator;
import com.izu.config.dto.AppDownloadInfoRespDTO;
import com.izu.consts.ConfigURI;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.LoginSessionUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@Api(tags="app配置")
@RestController
public class SysMobilePublishConfigController {
    private static final Logger logger = LoggerFactory.getLogger(SysMobilePublishConfigController.class);

    private final MrCarConfigRestLocator configRestLocator = new MrCarConfigRestLocator();

    @RequestMapping(value = "/sysMobilePublishConfig/select")
//    @RequiresPermissions(value = "AppVersionControl")
    @RequestFunction(functionName = "app版本列表")
    public RestResponse listForPage(@RequestBody Map<String,Object> paraMap, HttpServletRequest request){
        String restUrl = configRestLocator.getRestUrl("/sysMobilePublishConfig/select");

        try {
            return RestClient.requestForPage(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Map.class);
        } catch(Exception e) {
            logger.error("select Exception：", e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }

    @RequestMapping(value = "/sysMobilePublishConfig/add")
    @RequestFunction(functionName = "新增app版本信息")
    public RestResponse add(@RequestBody Map<String,Object> paraMap, HttpServletRequest request){
        String restUrl = configRestLocator.getRestUrl("/sysMobilePublishConfig/add");

        try {
            paraMap.put("createName", LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo().getStaffName());
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Object.class);
        } catch(Exception e) {
            logger.error("add Exception：", e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }

    @RequestMapping(value = "/sysMobilePublishConfig/update")
    @RequestFunction(functionName = "编辑app版本信息")
    public RestResponse update(@RequestBody Map<String,Object> paraMap, HttpServletRequest request){
        String restUrl = configRestLocator.getRestUrl("/sysMobilePublishConfig/update");

        try {
            paraMap.put("updateName", LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo().getStaffName());
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Object.class);
        } catch(Exception e) {
            logger.error("update Exception：", e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }

    @RequestMapping(value = "/sysMobilePublishConfig/updateStatus")
    @RequestFunction(functionName = "修改app版本是否可用")
    public RestResponse updateStatus(@RequestBody Map<String,Object> paraMap, HttpServletRequest request){
        String restUrl = configRestLocator.getRestUrl("/sysMobilePublishConfig/updateStatus");

        try {
            paraMap.put("updateName", LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo().getStaffName());
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Object.class);
        } catch(Exception e) {
            logger.error("updateStatus Exception：", e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }

    @RequestMapping(value = "/sysMobilePublishConfig/findChannels")
    public RestResponse findChannels(@RequestBody Map<String,Object> paraMap, HttpServletRequest request){
        String restUrl = configRestLocator.getRestUrl("/sysMobilePublishConfig/findChannels");
        try {
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Object.class);
        } catch(Exception e) {
            logger.error("findChannels Exception：", e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }


    /**
     * 为官网返回Mr.Car app 最新包的下载地址
     */
    @RequestFunction(functionName = "查询最新的app包的下载地址")
    @ApiOperation("查询最新的app包的下载地址")
    @GetMapping(ConfigURI.GET_APP_DOWNLOAD_URL)
    public RestResponse<AppDownloadInfoRespDTO> getMrCarAppDownloadUrl() {
        //查询安卓、ios在去掉CDN上的下载地址
        String restUrl = configRestLocator.getRestUrl(ConfigURI.GET_APP_DOWNLOAD_URL);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.GET, restUrl, null,null);
    }

}
