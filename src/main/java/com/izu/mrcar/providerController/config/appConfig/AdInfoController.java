package com.izu.mrcar.providerController.config.appConfig;

import com.izu.config.MrCarConfigRestLocator;
import com.izu.config.dto.appConfig.AdInfoRespDTO;
import com.izu.config.dto.appConfig.AdInfoSaveReqDTO;
import com.izu.config.dto.appConfig.AdInfoUpdateReqDTO;
import com.izu.config.dto.common.PageParamDTO;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.controller.base.ConfigBaseController;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/adInfo")
@Api(tags = "app配置")
public class AdInfoController extends ConfigBaseController {
    private static final Logger logger = LoggerFactory.getLogger(AdInfoController.class);

    private final MrCarConfigRestLocator configRestLocator = new MrCarConfigRestLocator();

    @RequestMapping(value = "/detail")
//    @RequiresPermissions(value = "advert_modify")
    @RequestFunction(functionName = "广告位详情")
    public RestResponse detail(@RequestBody Map<String,Object> paraMap){
        return post("/adInfo/detail",paraMap);
    }

    @PostMapping(value = "/list")
//    @RequiresPermissions(value = "advert_manage")
    @RequestFunction(functionName = "企业广告位列表")
    @ApiOperation("企业广告位列表-分页列表")
    public RestResponse<PageDTO<AdInfoRespDTO>> list(@RequestBody PageParamDTO pageParamDTO) {
       return postBody("/adInfo/list",pageParamDTO);
    }

    @PostMapping(value = "/add")
//    @RequiresPermissions(value = "advert_add")
    @RequestFunction(functionName = "新增广告位")
    @ApiOperation("企业广告位列表-新增广告位")
    public RestResponse add(@RequestBody AdInfoSaveReqDTO adInfoSaveReqDTO){
        return postBodyWithLogin("/adInfo/add",adInfoSaveReqDTO);
    }

    @PostMapping(value = "/update")
    @RequestFunction(functionName = "编辑广告位")
    @ApiOperation("企业广告位列表-编辑广告位")
    public RestResponse update(@RequestBody  AdInfoUpdateReqDTO reqDTO){
       return postBodyWithLogin("/adInfo/update",reqDTO);
    }

    @RequestMapping(value = "/delete")
    @RequestFunction(functionName = "删除广告位")
    public RestResponse delete(@RequestBody Map<String,Object> paraMap){
        String restUrl = configRestLocator.getRestUrl("/adInfo/delete");

        try {
            paraMap.put("updateName", LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo().getStaffName());
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Object.class);
        } catch(Exception e) {
            logger.error("delete Exception：" + e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }

    @RequestMapping(value = "/setEnable")
    public RestResponse setEnable(@RequestBody Map<String,Object> paraMap, HttpServletRequest request){
        String restUrl = configRestLocator.getRestUrl("/adInfo/setEnable");

        try {
            paraMap.put("updateName", LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo().getStaffName());
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Object.class);
        } catch(Exception e) {
            logger.error("setEnable Exception：" + e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }
}
