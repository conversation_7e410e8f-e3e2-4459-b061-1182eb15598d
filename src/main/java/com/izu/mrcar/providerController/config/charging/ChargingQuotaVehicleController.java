package com.izu.mrcar.providerController.config.charging;

import com.izu.config.MrCarConfigRestLocator;
import com.izu.config.dto.charging.*;
import com.izu.consts.ConfigURI;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.izu.framework.web.rest.client.BaseHttpClient.POSTBODY_MAP_KEY;

/**
 * <AUTHOR>
 * @version 1.0
 * {@code @date} 2024/5/29 下午4:35
 */
@Api(tags = "聚合充电-运营端-车辆限额")
@RequestMapping("/provider")
@RestController("providerChargingQuotaVehicleController")
public class ChargingQuotaVehicleController {

    /**
     * 获取某个充电配置下的车辆信息
     *
     * @param reqDTO 请求参数 configId 必传
     * @return 充电配置中的车辆列表
     */
    @ApiOperation(value = "获取某个充电配置下的车辆信息")
    @PostMapping(ConfigURI.CHARGING_QUOTA_VEHICLE_LIST_BY_CONFIG_ID)
    @SuppressWarnings("unchecked")
    public RestResponse<List<ChargingQuotaVehicleListDTO>> listByConfigId(@RequestBody  ChargingQuotaVehicleListByConfigReqDTO reqDTO) {
        Map<String, Object> httpParams = new HashMap<>();
        httpParams.put(POSTBODY_MAP_KEY, reqDTO);
        String url = new MrCarConfigRestLocator().getRestUrl(ConfigURI.CHARGING_QUOTA_VEHICLE_LIST_BY_CONFIG_ID);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, url, httpParams, null, ChargingQuotaVehicleListDTO.class);
    }



    @ApiOperation(value = "查询客户下的充电车辆限额-废弃")
    @PostMapping(ConfigURI.CHARGING_QUOTA_VEHICLE_LIST)
    public RestResponse<List<ChargingQuotaVehicleListDTO>> listByCompany(@RequestBody ChargingQuotaVehicleListByCompanyReqDTO params) {
        Map<String, Object> httpParams = new HashMap<>();
        httpParams.put(POSTBODY_MAP_KEY, params);
        String url = new MrCarConfigRestLocator().getRestUrl(ConfigURI.CHARGING_QUOTA_VEHICLE_LIST);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, url, httpParams, null, ChargingQuotaVehicleListDTO.class);
    }

    @ApiOperation(value = "新增车辆充电限额")
    @PostMapping(ConfigURI.CHARGING_QUOTA_VEHICLE_CREATE)
    public RestResponse<Boolean> edit(@RequestBody ChargingQuotaVehicleCreateDTO params) {
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        params.setLoginUserId(loginBaseInfo.obtainBaseInfo().getStaffId());
        params.setLoginUserName(loginBaseInfo.obtainBaseInfo().getStaffName());
        params.setLoginCompanyId(loginBaseInfo.obtainBelongCompanyId());
        params.setLoginCompanyName(loginBaseInfo.obtainBelongCompanyName());
        params.setMobile(loginBaseInfo.obtainBaseInfo().getMobile());
        Map<String, Object> httpParams = new HashMap<>();
        httpParams.put(POSTBODY_MAP_KEY, params);
        String url = new MrCarConfigRestLocator().getRestUrl(ConfigURI.CHARGING_QUOTA_VEHICLE_CREATE);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, httpParams, null, Boolean.class);
    }

    @ApiOperation(value = "编辑车辆充电限额")
    @PostMapping(ConfigURI.CHARGING_QUOTA_VEHICLE_EDIT)
    public RestResponse<Boolean> edit(@RequestBody ChargingQuotaVehicleEditDTO params) {
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        params.setLoginUserId(loginBaseInfo.obtainBaseInfo().getStaffId());
        params.setLoginUserName(loginBaseInfo.obtainBaseInfo().getStaffName());
        params.setLoginCompanyId(loginBaseInfo.obtainBelongCompanyId());
        params.setLoginCompanyName(loginBaseInfo.obtainBelongCompanyName());
        params.setMobile(loginBaseInfo.obtainBaseInfo().getMobile());
        Map<String, Object> httpParams = new HashMap<>();
        httpParams.put(POSTBODY_MAP_KEY, params);
        String url = new MrCarConfigRestLocator().getRestUrl(ConfigURI.CHARGING_QUOTA_VEHICLE_EDIT);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, httpParams, null, Boolean.class);
    }

    @ApiOperation(value = "删除车辆充电限额")
    @PostMapping(ConfigURI.CHARGING_QUOTA_VEHICLE_DELETE)
    public RestResponse<Boolean> del(@RequestBody ChargingQuotaVehicleEditDTO params) {
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        params.setLoginUserId(loginBaseInfo.obtainBaseInfo().getStaffId());
        params.setLoginUserName(loginBaseInfo.obtainBaseInfo().getStaffName());
        params.setLoginCompanyId(loginBaseInfo.obtainBelongCompanyId());
        params.setLoginCompanyName(loginBaseInfo.obtainBelongCompanyName());
        params.setMobile(loginBaseInfo.obtainBaseInfo().getMobile());
        Map<String, Object> httpParams = new HashMap<>();
        httpParams.put(POSTBODY_MAP_KEY, params);
        String url = new MrCarConfigRestLocator().getRestUrl(ConfigURI.CHARGING_QUOTA_VEHICLE_DELETE);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, httpParams, null, Boolean.class);
    }

    @ApiOperation(value = "查询可添加限额的车辆列表")
    @PostMapping(ConfigURI.CHARGING_QUOTA_VEHICLE_QUERY)
    public RestResponse<List<ChargingQuotaVehicleQueryListDTO>> queryAddVehicle(@RequestBody ChargingQuotaVehicleReqDTO params) {
        params.setFromProvider(true);
        Map<String, Object> httpParams = new HashMap<>();
        httpParams.put(POSTBODY_MAP_KEY, params);
        String url = new MrCarConfigRestLocator().getRestUrl(ConfigURI.CHARGING_QUOTA_VEHICLE_QUERY);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, url, httpParams, null, ChargingQuotaVehicleQueryListDTO.class);
    }

    //

}

