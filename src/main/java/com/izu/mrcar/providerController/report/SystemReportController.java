package com.izu.mrcar.providerController.report;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.izu.business.config.BusinessRestLocator;
import com.izu.business.consts.resturl.MrcarBusinessRestMsgCenter;
import com.izu.business.dto.provider.input.*;
import com.izu.business.dto.provider.output.StatisticsFunctionViewListRespDTO;
import com.izu.business.dto.provider.output.StatisticsFunctionViewLogDetailRespDTO;
import com.izu.business.dto.provider.output.StatisticsFunctionViewRespDTO;
import com.izu.business.dto.provider.output.UserOperationLogSearchRespDTO;
import com.izu.erplease.util.Check;
import com.izu.excel.annotation.ExportExcel;
import com.izu.excel.download.AbstractIzuWebExcel;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.config.ExecutorConfig;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.dto.StatisticsFunctionViewExportDetail;
import com.izu.mrcar.dto.StatisticsFunctionViewExportList;
import com.izu.mrcar.dto.UserOperationExportDTO;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.enums.LoginSystemEnum;
import com.izu.user.enums.MenuOpenModeEnum;
import com.izu.user.enums.perm.PermissionTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import static com.izu.business.consts.resturl.MrcarBusinessRestMsgCenter.*;
import static com.izu.mrcar.common.errorcode.ErrorCode.STAT_FUNCTION_OPERATION_EXPORT_EXCEED;
import static com.izu.mrcar.common.errorcode.ErrorCode.STAT_OPERATION_LOG_EXPORT_EXCEED;

/**
 * 系统报表接口.
 *
 * <AUTHOR>
 * @date 2023-05-10
 */
@RestController
@Api(tags = "系统报表")
public class SystemReportController implements AbstractIzuWebExcel {

    private static final Logger logger = LoggerFactory.getLogger(SystemReportController.class);

    private final DateTimeFormatter formatter =
            DateTimeFormatter.ISO_DATE.withZone(ZoneId.systemDefault());

    // 默认导出时的每页记录条数
    private static final int DEFAULT_EXPORT_SIZE_PER_PAGE = 200;

    @NacosValue("${export.max.line}")
    private Integer exportLimit;

    /**
     * 增加用户操作日志
     */
    @ApiOperation(value = "添加用户功能操作日志")
    @PostMapping(REPORT_USER_FUNCTION_OPERATION_APPEND)
    @ApiImplicitParam(name = "permissionCode", value = "权限代码(PC端使用)",
            required = true, example = "safety_manager", paramType="form")
    public RestResponse<String> appendUserFunctionOperationLog(
            @Verify(param = "permissionCode", rule = "required") String permissionCode) {
        String restUrl = new BusinessRestLocator()
                .getRestUrl(MrcarBusinessRestMsgCenter.REPORT_USER_FUNCTION_OPERATION_APPEND);
        StatisticsFunctionViewReqDTO reqDTO = new StatisticsFunctionViewReqDTO();
        reqDTO.setPermissionCode(permissionCode);
        // 填充用户信息
        LoginBaseInfo loginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = loginInfo.obtainBaseInfo();
        reqDTO.setLoginUserId(baseInfo.getStaffId());
        reqDTO.setLoginCode(baseInfo.getStaffCode());
        reqDTO.setLoginUserName(baseInfo.getStaffName());
        reqDTO.setLoginUserMobile(baseInfo.getMobile());
        reqDTO.setLoginCompanyId(loginInfo.obtainBelongCompanyId());
        reqDTO.setLoginCompanyName(loginInfo.obtainBelongCompanyName());
        reqDTO.setLoginCompanyCode(loginInfo.obtainBelongCompanyCode());
        // 针对权限角色进行转换
        LoginSystemEnum loginType = LoginSystemEnum.getBySys(loginInfo.getSystemType());
        Byte menuOpenMode = null;
        switch (loginType) {
            case PROVIDER:
                menuOpenMode = MenuOpenModeEnum.MANAGEMENT.getCode();
                break;
            case CLIENT:
                menuOpenMode = MenuOpenModeEnum.ENTERPRISE.getCode();
                break;
            default:
                break;
        }
        reqDTO.setMenuOpenMode(menuOpenMode);

        final Map<String, Object> innerMap = JSON.parseObject(JSON.toJSONString(reqDTO)).getInnerMap();
        Map<String, Object> restParam = new HashMap<>(innerMap);

        RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null, String.class);
        return RestResponse.success("OK");
    }

    /**
     * 分页查询统计结果
     */
    @ApiOperation(value = "分页查询用户功能操作统计结果")
    @PostMapping(REPORT_USER_FUNCTION_OPERATION_LIST)
    public RestResponse<PageDTO<StatisticsFunctionViewListRespDTO>> pageSearchFunctionOperationLog(
            @RequestBody StatisticsFunctionViewSearchReqDTO reqDTO) {
        // 日期转换
        reqDTO.setStart(Date.from(LocalDate.parse(reqDTO.getStartStr())
                .atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
        LocalDate endDay = LocalDate.parse(reqDTO.getEndStr());
        // 规则化结束日期
        LocalDate now = LocalDate.now();
        if (!endDay.isBefore(now)) {
            endDay = now.plusDays(-1);
        }
        reqDTO.setEnd(Date.from(endDay.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
        String restUrl = new BusinessRestLocator()
                .getRestUrl(MrcarBusinessRestMsgCenter.REPORT_USER_FUNCTION_OPERATION_LIST);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY,
                restUrl,
                JSONObject.parseObject(JSON.toJSONString(reqDTO)),
                null,
                StatisticsFunctionViewListRespDTO.class);
    }

    @ApiOperation(value = "导出功能统计结果列表")
    @PostMapping(REPORT_USER_FUNCTION_OPERATION_EXPORT_LIST)
    public void exportFunctionOperationLogList(
            @RequestBody StatisticsFunctionViewExportReqDTO reqDTO,
            HttpServletResponse response) throws Exception {
        // 拼接查询条件
        StatisticsFunctionViewSearchReqDTO condition = new StatisticsFunctionViewSearchReqDTO();
        condition.setCompanyCode(reqDTO.getCompanyCode());
        condition.setCompanyStatus(reqDTO.getCompanyStatus());
        condition.setMenuOpenMode(reqDTO.getMenuOpenMode());
        condition.setStart(Date.from(LocalDate.parse(reqDTO.getStartStr())
                .atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
        LocalDate endDay = LocalDate.parse(reqDTO.getEndStr());
        // 规则化结束日期
        LocalDate now = LocalDate.now();
        if (!endDay.isBefore(now)) {
            endDay = now.plusDays(-1);
        }
        condition.setEnd(Date.from(endDay.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
        condition.setPage(1);
        condition.setPageSize(DEFAULT_EXPORT_SIZE_PER_PAGE);
        String restUrl = new BusinessRestLocator()
                .getRestUrl(MrcarBusinessRestMsgCenter.REPORT_USER_FUNCTION_OPERATION_LIST);
        // 缓存所有分页结果
        List<StatisticsFunctionViewListRespDTO> rows = new ArrayList<>();
        // 第一次获取总记录条数
        RestResponse<PageDTO<StatisticsFunctionViewListRespDTO>> resp =
                RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY,
                        restUrl,
                        JSONObject.parseObject(JSON.toJSONString(condition)),
                        null,
                        StatisticsFunctionViewListRespDTO.class);
        // 总记录条数
        PageDTO<StatisticsFunctionViewListRespDTO> page = resp.getData();
        // 总页数
        int totalPage = (int) (page.getTotal() % DEFAULT_EXPORT_SIZE_PER_PAGE == 0 ?
                page.getTotal() / DEFAULT_EXPORT_SIZE_PER_PAGE
                    : page.getTotal() / DEFAULT_EXPORT_SIZE_PER_PAGE + 1);
        rows.addAll(page.getResult());
        // 线程池计算剩余页码的记录
        List<Future<List<StatisticsFunctionViewListRespDTO>>> futures = new ArrayList<>();
        for (int curPage = 2; curPage <= totalPage; curPage++) {
            futures.add(ExecutorConfig.getThreadPool().submit(new ExportFunctionOperationLogPageTask(condition, curPage, restUrl)));
        }
        for (Future<List<StatisticsFunctionViewListRespDTO>> future : futures) {
            rows.addAll(future.get());
        }
        // 这里注意 有同学反应使用swagger 会导致各种问题，请直接用浏览器或者用postman
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        String fileName = URLEncoder.encode("功能访问量列表", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        List<StatisticsFunctionViewExportList> exportList =
                rows.stream().map(s -> {
                    StatisticsFunctionViewExportList record = new StatisticsFunctionViewExportList();
                    record.setPermissionName(s.getPermissionName());
                    record.setMenuOpenMode(s.getMenuOpenMode());
                    record.setPermissionType(s.getPermissionType());
                    record.setCustomerPv(String.valueOf(s.getCustomerPv()));
                    record.setCustomerUv(String.valueOf(s.getCustomerUv()));
                    record.setCustomerDailyPv(s.getCustomerDailyPv());
                    record.setCustomerDailyUv(s.getCustomerDailyUv());
                    return record;
                }).collect(Collectors.toList());
        EasyExcel.write(response.getOutputStream(), StatisticsFunctionViewExportList.class).sheet("模板").doWrite(exportList);
    }

    /**
     * 导出功能统计结果
     */
    @ApiOperation(value = "导出功能统计结果")
    @PostMapping(REPORT_USER_FUNCTION_OPERATION_EXPORT)
    @ExportExcel(fileName = "用户功能明细.xlsx",
            filePath = "/data/logs/tmp",
            sheet = "Sheet1"
    )
    public void exportFunctionOperationLogDetail(
            @RequestBody StatisticsFunctionViewExportReqDTO reqDTO,
            HttpServletResponse response) throws IOException {
        // 前置记录条数校验
        Long total = getTotalWithExport(reqDTO);
        if (total > ExportExcelConstants.EXPORT_MAX_LINE) {
            RestResponse<?> rest = RestResponse.fail(STAT_FUNCTION_OPERATION_EXPORT_EXCEED,
                    total, ExportExcelConstants.EXPORT_MAX_LINE);
            JSON.writeJSONString(response.getOutputStream(), rest);
            response.setContentType("application/json");
            return;
        }
        // 根据导出条件构建param对象
        String paramUrl = new BusinessRestLocator()
                .getRestUrl(MrcarBusinessRestMsgCenter.REPORT_USER_FUNCTION_OPERATION_DETAIL_PARAMS);
        RestResponse paramResp = RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY,
                paramUrl,
                JSONObject.parseObject(JSON.toJSONString(reqDTO)),
                null,
                StatisticsFunctionViewExportParamDTO.class);
        StatisticsFunctionViewExportParamDTO param =
                (StatisticsFunctionViewExportParamDTO) paramResp.getData();
        // 将param转为map
        Map<String, Object> params = new HashMap<>();
        if (!Check.NuNObj(param.getMenuOpenMode())) {
            params.put("menuOpenMode", param.getMenuOpenMode());
        }
        if (!Check.NuNObj(param.getPermSysCode())) {
            params.put("permSysCode", param.getPermSysCode());
        }
        if (!Check.NuNObj(param.getCompanyCodes())) {
            params.put("companyCodes", param.getCompanyCodes());
        }
        params.put("startStr", param.getStartStr());
        params.put("endStr", param.getEndStr());
        LoginBaseInfo loginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = loginInfo.obtainBaseInfo();
        this.webRepeatExportExcel(response, params,
                StatisticsFunctionViewExportDetail.class, baseInfo.getStaffName());
    }

    @Override
    public PageDTO getWebDataByPage(Map<String, Object> params, int pageNo, int pageSize) {
        // 根据导出条件构建param对象
        String restUrl = new BusinessRestLocator()
                .getRestUrl(MrcarBusinessRestMsgCenter.REPORT_USER_FUNCTION_OPERATION_DETAIL_LIST);
        // 添加分页信息
        params.put("page", pageNo);
        params.put("pageSize", pageSize);
        RestResponse response = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY,
                restUrl,
                params,
                null,
                StatisticsFunctionViewLogDetailRespDTO.class);
        // 转化对象
        PageDTO<StatisticsFunctionViewLogDetailRespDTO> data =
                (PageDTO<StatisticsFunctionViewLogDetailRespDTO>) response.getData();
        PageDTO result = new PageDTO();
        result.setPage(pageNo);
        result.setPageSize(pageSize);
        result.setTotal(data.getTotal());
        result.setResult(data.getResult().stream().map(s -> {
            StatisticsFunctionViewExportDetail respDTO = new StatisticsFunctionViewExportDetail();
            respDTO.setUserMobile(s.getUserMobile());
            respDTO.setOperationTerminal(MenuOpenModeEnum.getByCode(s.getMenuOpenMode()).getDesc());
            respDTO.setPermSysCode(s.getPermSysCode());
            respDTO.setPermissionType(PermissionTypeEnum.getByPermissionType(s.getPermissionType()).getDesc());
            respDTO.setPermissionLevel(formatMenuLevel(s.getMenuLevel()));
            respDTO.setPermissionPath(String.join("-", s.getMenuPath()));
            respDTO.setCompanyName(s.getCompanyName());
            respDTO.setOperateTime(s.getOperateTime());
            return respDTO;
        }).collect(Collectors.toList()));
        return result;
    }

    private Long getTotalWithExport(StatisticsFunctionViewExportReqDTO reqDTO) {
        // 根据导出条件构建param对象
        String restUrl = new BusinessRestLocator()
                .getRestUrl(MrcarBusinessRestMsgCenter.REPORT_USER_FUNCTION_OPERATION_DETAIL_PARAMS);
        RestResponse paramResp = RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY,
                restUrl,
                JSONObject.parseObject(JSON.toJSONString(reqDTO)),
                null,
                StatisticsFunctionViewExportParamDTO.class);
        // 返回记录条数
        StatisticsFunctionViewExportParamDTO param =
                (StatisticsFunctionViewExportParamDTO) paramResp.getData();
        return param.getTotal();
    }

    private String formatMenuLevel(int level) {
        StringBuilder sb = new StringBuilder();
        switch (level) {
            case 1:
                sb.append("一");
                break;
            case 2:
                sb.append("二");
                break;
            case 3:
                sb.append("三");
                break;
            case 4:
                sb.append("四");
                break;
            default:
                break;
        }
        sb.append("级");
        return sb.toString();
    }

    // 为加快导出速度, 同时复用分页查询方法, 采用多线程的方式并行查询记录
    public static class ExportFunctionOperationLogPageTask
            implements Callable<List<StatisticsFunctionViewListRespDTO>> {
        private final StatisticsFunctionViewSearchReqDTO condition;
        private final int page;
        private final String restUrl;

        public ExportFunctionOperationLogPageTask(StatisticsFunctionViewSearchReqDTO condition, int page, String restUrl) {
            this.condition = condition;
            this.page = page;
            this.restUrl = restUrl;
        }

        @Override
        public List<StatisticsFunctionViewListRespDTO> call() throws Exception {
            StatisticsFunctionViewSearchReqDTO search =
                    BeanUtil.copyObject(this.condition, StatisticsFunctionViewSearchReqDTO.class);
            search.setPage(page);
            RestResponse<PageDTO<StatisticsFunctionViewListRespDTO>> resp =
                    RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY,
                            restUrl,
                            JSONObject.parseObject(JSON.toJSONString(search)),
                            null,
                            StatisticsFunctionViewListRespDTO.class);
            return resp.getData().getResult();
        }
    }

    // --------------------- 用户操作日志导出 -------------------

    /**
     * 分页查询统计结果
     */
    @ApiOperation(value = "分页查询用户操作日志")
    @PostMapping(OPERATION_LOG_PAGE_SEARCH)
    public RestResponse<PageDTO<UserOperationLogSearchRespDTO>> pageSearchUserOperationLog(
            @RequestBody UserOperationLogSearchReqDTO reqDTO) {
        return doPageSearchForOperationLog(reqDTO);
    }

    /**
     * 分页查询统计结果
     */
    @ApiOperation(value = "用户操作日志导出本地")
    @PostMapping(OPERATION_LOG_EXPORT_LOCAL)
    public void exportLocalUserOperationLog(@RequestBody UserOperationLogSearchReqDTO reqDTO,
                                            HttpServletResponse response) throws IOException {
        reqDTO.setPage(1); reqDTO.setPageSize(0);
        // 第一次查询总的记录条数
        PageDTO<UserOperationLogSearchRespDTO> page = doPageSearchForOperationLog(reqDTO).getData();
        long total = page.getTotal();
        if (total > this.exportLimit) {
            RestResponse<?> rest = RestResponse.fail(STAT_OPERATION_LOG_EXPORT_EXCEED, total, this.exportLimit);
            JSON.writeJSONString(response.getOutputStream(), rest);
            response.setContentType("application/json");
            return;
        }
        // 总页数
        int totalPage = (int) (page.getTotal() % DEFAULT_EXPORT_SIZE_PER_PAGE == 0 ?
                page.getTotal() / DEFAULT_EXPORT_SIZE_PER_PAGE
                : page.getTotal() / DEFAULT_EXPORT_SIZE_PER_PAGE + 1);

        List<UserOperationExportDTO> exportList = new ArrayList<>((int) total);

        for (int i = 1; i <= totalPage; i++) {
            reqDTO.setPage(i);
            reqDTO.setPageSize(DEFAULT_EXPORT_SIZE_PER_PAGE);
            PageDTO<UserOperationLogSearchRespDTO> data =
                    doPageSearchForOperationLog(reqDTO).getData();
            exportList.addAll(
                    data.getResult()
                            .stream()
                            .map(s -> {
                                UserOperationExportDTO dto = new UserOperationExportDTO();
                                dto.setOperateTime(s.getOperateTime());
                                dto.setUserName(s.getUserName());
                                dto.setUserMobile(s.getUserMobile());
                                dto.setCompanyName(s.getCompanyName());
                                dto.setUserIp(s.getUserIp());
                                dto.setFuncName(s.getFuncName());
                                dto.setRequestUrl(s.getRequestUrl());
                                dto.setRequestType(s.getRequestType());
                                dto.setRequestCost(s.getRequestCost());
                                dto.setRequestParams(s.getRequestParams());
                                return dto;
                            }).collect(Collectors.toList()));
        }
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        String fileName = URLEncoder.encode("用户操作日志", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), UserOperationExportDTO.class)
                .sheet("访问日志").doWrite(exportList);
    }


    @ApiOperation(value = "用户操作日志导出邮箱")
    @PostMapping(OPERATION_LOG_EXPORT_MAIL)
    public RestResponse<?> exportMailUserOperationLog(@RequestBody UserOperationLogSearchReqDTO reqDTO) throws IOException {
        String restUrl = new BusinessRestLocator()
                .getRestUrl(MrcarBusinessRestMsgCenter.OPERATION_LOG_EXPORT_MAIL);

        UserOperationLogExportReqDTO dto = new UserOperationLogExportReqDTO();
        BeanUtils.copyProperties(reqDTO, dto);
        LoginBaseInfo loginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = loginInfo.obtainBaseInfo();
        dto.setStaffId(baseInfo.getStaffId());
        dto.setStaffCode(baseInfo.getStaffCode());
        dto.setMobile(baseInfo.getMobile());
        dto.setEmail(baseInfo.getEmail());

        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY,
                restUrl,
                JSONObject.parseObject(JSON.toJSONString(dto)),
                null,
                Object.class);
    }


    @SuppressWarnings("unchecked")
    private RestResponse<PageDTO<UserOperationLogSearchRespDTO>> doPageSearchForOperationLog(
            UserOperationLogSearchReqDTO reqDTO) {
        String restUrl = new BusinessRestLocator()
                .getRestUrl(MrcarBusinessRestMsgCenter.OPERATION_LOG_PAGE_SEARCH);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY,
                restUrl,
                JSONObject.parseObject(JSON.toJSONString(reqDTO)),
                null,
                UserOperationLogSearchRespDTO.class);
    }

    /**
     * 分页查询统计结果
     */
    @ApiOperation(value = "分页查询用户功能操作统计结果")
    @PostMapping(REPORT_USER_FUNCTION_OPERATION_PAGE_LIST)
    public RestResponse<PageDTO<StatisticsFunctionViewRespDTO>> queryUserPageList(
            @RequestBody StatisticsFunctionViewSearchReqDTO reqDTO) {
        // 日期转换
        reqDTO.setStart(Date.from(LocalDate.parse(reqDTO.getStartStr())
                .atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
        LocalDate endDay = LocalDate.parse(reqDTO.getEndStr());
        // 规则化结束日期
        LocalDate now = LocalDate.now();
        if (!endDay.isBefore(now)) {
            endDay = now.plusDays(-1);
        }
        reqDTO.setEnd(Date.from(endDay.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
        String restUrl = new BusinessRestLocator()
                .getRestUrl(MrcarBusinessRestMsgCenter.REPORT_USER_FUNCTION_OPERATION_PAGE_LIST);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY,
                restUrl,
                JSONObject.parseObject(JSON.toJSONString(reqDTO)),
                null,
                StatisticsFunctionViewRespDTO.class);
    }
    /**
     * 根据权限code 查询用户功能日志
     */
    @ApiOperation(value = "根据权限code 查询用户功能日志")
    @PostMapping(REPORT_USER_FUNCTION_OPERATION_LIST_BY_PERM_SYS_CODE)
    public RestResponse<PageDTO<StatisticsFunctionViewListRespDTO>> listUserFunctionOperationBySysCode(@RequestBody StatisticsFunctionViewSearchReqDTO reqDTO) {
        // 日期转换
        reqDTO.setStart(Date.from(LocalDate.parse(reqDTO.getStartStr())
                .atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));
        LocalDate endDay = LocalDate.parse(reqDTO.getEndStr());
        // 规则化结束日期
        LocalDate now = LocalDate.now();
        if (!endDay.isBefore(now)) {
            endDay = now.plusDays(-1);
        }
        reqDTO.setEnd(Date.from(endDay.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant()));

        String restUrl = new BusinessRestLocator()
                .getRestUrl(MrcarBusinessRestMsgCenter.REPORT_USER_FUNCTION_OPERATION_LIST_BY_PERM_SYS_CODE);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY,
                restUrl,
                JSONObject.parseObject(JSON.toJSONString(reqDTO)),
                null,
                Object.class);
    }
}
