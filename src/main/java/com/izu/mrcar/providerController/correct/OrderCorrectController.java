package com.izu.mrcar.providerController.correct;

import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.controller.base.OrderBaseController;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.dto.common.BaseDTO;
import com.izu.mrcar.order.dto.order.correct.OrderCorrectReqDTO;
import com.izu.mrcar.order.dto.order.correct.OrderCorrectRespDTO;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 手动里程纠偏相关接口-运营端
 *
 * <AUTHOR>
 * &#064;date  2023/7/27 10:04
 */
@RestController
@Api(tags = "里程纠偏")
@RequestMapping("/provider")
public class OrderCorrectController extends OrderBaseController {


    /**
     * 手动纠偏
     */
    @ApiOperation(value = "保存里程纠正接口", notes = "作者：haobinjie")
    @PostMapping(MrcarOrderRestMsgCenter.SAVE_ORDER_CORRECT)
    @SuppressWarnings("unchecked")
    public RestResponse<Void> saveCorrectMileage(@RequestBody OrderCorrectReqDTO orderCorrectReqDTO) {

        this.buildLoginInfo(orderCorrectReqDTO);

        return postBody(MrcarOrderRestMsgCenter.SAVE_ORDER_CORRECT, orderCorrectReqDTO);

    }


    /**
     * 查询纠偏面板信息
     */
    @ApiOperation(value = "查询纠偏面板信息", notes = "作者：haobinjie")
    @PostMapping(MrcarOrderRestMsgCenter.GET_ORDER_CORRECT)
    @SuppressWarnings("unchecked")
    public RestResponse<OrderCorrectRespDTO> getCorrectMileageInfo(String orderNo) {

        OrderCorrectReqDTO orderCorrectReqDTO = new OrderCorrectReqDTO();

        orderCorrectReqDTO.setOrderNo(orderNo);

        return postBody(MrcarOrderRestMsgCenter.GET_ORDER_CORRECT, orderCorrectReqDTO);

    }


    private void buildLoginInfo(BaseDTO baseDTO) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        baseDTO.setLoginUserId(providerLoginInfo.getBaseInfo().getStaffId());
        baseDTO.setLoginUserName(providerLoginInfo.getBaseInfo().getStaffName());
    }
}
