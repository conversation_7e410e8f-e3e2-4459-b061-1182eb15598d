package com.izu.mrcar.providerController.vehicleRemind;

import cn.hutool.core.collection.CollUtil;
import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.vehicleRemind.req.VehicleRemindInfoReqDTO;
import com.izu.asset.dto.vehicleRemind.resp.VehicleRemindInfoRespDTO;
import com.izu.business.dto.bi.resp.VehicleBiAnalyseRespDTO;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.doc.JrdApiDoc;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.excel.Column;
import com.izu.framework.web.util.excel.ExSheet;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.controller.AbstractExcelDownloadController;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.enums.perm.ProviderDataPermTypeEnum;
import io.swagger.annotations.Api;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.*;

@Api(tags = "车务管理")
@RestController
public class ProviderVehicleRemindController extends AbstractExcelDownloadController {

    @PostMapping(value = MrCarAssetRestCenter.PROVIDER_VEHICLE_REMIND_INFO_LIST)
    @RequestFunction(functionName = "车务管理-列表")
    @JrdApiDoc(simpleDesc = "车务管理列表", author = "hhd", resDataClass = VehicleRemindInfoRespDTO.class)
    public RestResponse<PageDTO<VehicleRemindInfoRespDTO>> getList(@RequestBody VehicleRemindInfoReqDTO vehicleRemindInfoReqDTO) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        if(!Objects.equals(ProviderDataPermTypeEnum.ALL.getType(),providerLoginInfo.obtainDataPerm().getDataPermType())){
            return RestResponse.success(new PageDTO<>());
        }
        if(vehicleRemindInfoReqDTO.getCompanyId()!=null){
            List<Integer> list = new ArrayList<>();
            list.add(vehicleRemindInfoReqDTO.getCompanyId());
            vehicleRemindInfoReqDTO.setCompanyIds(list);
        }
        Map<String, Object> param = new HashMap<>();
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, vehicleRemindInfoReqDTO);
        String restUrl = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.VEHICLE_REMIND_INFO_LIST);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null, VehicleRemindInfoRespDTO.class);
    }
    @PostMapping(value = MrCarAssetRestCenter.PROVIDER_VEHICLE_REMIND_INFO_EXPORT)
    @RequestFunction(functionName = "车务管理-导出")
    @JrdApiDoc(simpleDesc = "车务管理列表导出", author = "hhd", resDataClass = Object.class)
    public RestResponse export(@RequestBody VehicleRemindInfoReqDTO vehicleRemindInfoReqDTO, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        if(!Objects.equals(ProviderDataPermTypeEnum.ALL.getType(),providerLoginInfo.obtainDataPerm().getDataPermType())){
            listExport(new ArrayList<>(), request, response,false);
            return null;
        }
        vehicleRemindInfoReqDTO.setPage(1);
        vehicleRemindInfoReqDTO.setPageSize(1000);
        if(vehicleRemindInfoReqDTO.getCompanyId()!=null){
            List<Integer> list = new ArrayList<>();
            list.add(vehicleRemindInfoReqDTO.getCompanyId());
            vehicleRemindInfoReqDTO.setCompanyIds(list);
        }
        izuEasyExcelSession.setPageNo(1);
        Map<String, Object> param = new HashMap<>();
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, vehicleRemindInfoReqDTO);
        String restUrl = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.VEHICLE_REMIND_INFO_LIST);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null, VehicleRemindInfoRespDTO.class);
        if(restResponse==null || !restResponse.isSuccess() || restResponse.getData() ==null){
            return null;
        }
        PageDTO pageDto = (PageDTO) restResponse.getData();
        if (pageDto.getTotal()>10000){
            return RestResponse.fail(ErrorCode.EXPORT_LIMITED_ERROR);
        }
        Integer page = vehicleRemindInfoReqDTO.getPage();
        List<VehicleRemindInfoRespDTO> respDTOList = (List<VehicleRemindInfoRespDTO>)pageDto.getResult();
        List<VehicleRemindInfoRespDTO> allList = new ArrayList<>(respDTOList);
        int pages = pageDto.getPages();
        while (page <= pages){
            page+=1;
            vehicleRemindInfoReqDTO.setPage(page);
            Map<String, Object> param1 = new HashMap<>();
            param1.put(BaseHttpClient.POSTBODY_MAP_KEY, vehicleRemindInfoReqDTO);
            restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param1, null, VehicleRemindInfoRespDTO.class);
            if (!restResponse.isSuccess()){
                continue;
            }
            pageDto = (PageDTO) restResponse.getData();
            allList.addAll(pageDto.getResult());
        }
        boolean falg = true;
        if((CollUtil.isEmpty(vehicleRemindInfoReqDTO.getCompanyIds())  ||  vehicleRemindInfoReqDTO.getCompanyIds().size() == 1)){
            falg = false;
        }
        listExport(allList, request, response,falg);
        return null;
    }

    private void listExport(List<VehicleRemindInfoRespDTO> datas, HttpServletRequest request, HttpServletResponse response, boolean falg) {

        List<Column> columnModes = new ArrayList<>();
        columnModes.add(new Column("vehicleLicense", "车牌号", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("vehicleBrandName", "车辆品牌", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("vehicleSeriesName", "车系", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("lastMaintainDate", "上次保养日期", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("nextMaintenanceDate", "下次保养日期", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("maintainExpireStr", "距保养到期", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("compulsoryInsuranceStatusStr", "交强险状态", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("compulsoryInsuranceRestStr", "距交强险到期", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("commercialInsuranceStatusStr", "商业险状态", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("commercialInsuranceRestStr", "距商业险到期", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("annualInspectionStatusStr", "年检状态", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("annualInspectionExpireStr", "距年检到期", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("violationPendingNum", "违章待处理", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("belongCityName", "所在城市", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("companyName", "所在企业", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("structName", "所属部门", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("selfOwnedName", "车辆来源", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("useCarName", "用车人", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("driverName", "司机", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("vehicleVin", "车架号", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        final List<Map<String, String>> rowDatas = new ArrayList<Map<String, String>>(datas.size() + 1);
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");
        for (VehicleRemindInfoRespDTO data : datas) {
            final Map<String, String> rowdata = new HashMap<String, String>(columnModes.size());
            rowdata.put("vehicleLicense", data.getVehicleLicense());
            rowdata.put("vehicleBrandName", data.getVehicleBrandName());
            rowdata.put("vehicleSeriesName", data.getVehicleSeriesName());
            rowdata.put("lastMaintainDate", data.getLastMaintainDateStr());
            rowdata.put("nextMaintenanceDate", data.getNextMaintenanceDateStr());
            rowdata.put("maintainExpireStr", data.getMaintainExpireStr());
            rowdata.put("compulsoryInsuranceStatusStr", data.getCompulsoryInsuranceStatusStr());
            rowdata.put("compulsoryInsuranceRestStr", data.getCompulsoryInsuranceRestStr());
            rowdata.put("commercialInsuranceStatusStr", data.getCommercialInsuranceStatusStr());
            rowdata.put("commercialInsuranceRestStr", data.getCommercialInsuranceRestStr());
            rowdata.put("annualInspectionStatusStr", data.getAnnualInspectionStatusStr());
            rowdata.put("annualInspectionExpireStr", data.getAnnualInspectionExpireStr());
            rowdata.put("violationPendingNum", data.getViolationPendingNum()!=null?String.valueOf(data.getViolationPendingNum()):"");
            rowdata.put("belongCityName", data.getBelongCityName());
            rowdata.put("companyName", data.getCompanyName());
            rowdata.put("structName", data.getStructName());
            rowdata.put("selfOwnedName", data.getSelfOwnedName());
            rowdata.put("useCarName", data.getUseCarName());
            rowdata.put("driverName", data.getDriverName());
            rowdata.put("vehicleVin", data.getVehicleVin());
            rowDatas.add(rowdata);
        }
        List<ExSheet> exSheetList = new ArrayList<>(1);
        exSheetList.add(new ExSheet(columnModes, rowDatas, "车务提醒导出"));
        //---------------------------B: 根据上面创建好的Sheet，生成Excel
        String excelType = "xlsx";
        String fileNameCn = "车务提醒导出_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + "." + excelType;
        String fileNameEn = "vehicle_remind_export_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + "." + excelType;
        this.exportExcel(request, response, fileNameCn, fileNameEn, exSheetList);
    }
}
