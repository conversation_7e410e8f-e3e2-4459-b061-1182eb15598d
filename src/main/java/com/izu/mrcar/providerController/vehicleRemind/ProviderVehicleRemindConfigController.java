package com.izu.mrcar.providerController.vehicleRemind;

import com.google.common.collect.Maps;
import com.izu.config.MrCarConfigRestLocator;
import com.izu.config.dto.common.BaseDTO;
import com.izu.config.dto.daibuche.req.CoOrderWhiteListQueryReqDTO;
import com.izu.config.dto.daibuche.resp.CoOrderWhiteListExportRespDTO;
import com.izu.config.dto.daibuche.resp.CoOrderWhiteListQueryRespDTO;
import com.izu.config.dto.vehicleRemind.VehicleRemindConfigDTO;
import com.izu.config.dto.vehicleRemind.VehicleRemindConfigReqDTO;
import com.izu.config.dto.vehicleRemind.VehicleRemindConfigRespDTO;
import com.izu.config.dto.vehicleRemind.VehicleRemindConfigRespondDTO;
import com.izu.consts.ConfigURI;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.doc.JrdApiDoc;
import com.izu.framework.doc.JrdApiParamDoc;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.enums.perm.ProviderDataPermTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description:
 * @date 2025/4/21 16:35
 */
@RestController
@Api(tags = "车务提醒配置")
public class ProviderVehicleRemindConfigController {
    private static final Integer PAGE_SIZE = 10000;
    private final MrCarConfigRestLocator mrCarConfigRestLocator = new MrCarConfigRestLocator();
    @GetMapping("/provider/vehicleRemind/getDetail")
    @RequestFunction(functionName = "车务提醒配置详情")
    @ApiOperation(value = "车务提醒配置详情（运营端）", notes = "作者：丁伟兵")
    public RestResponse<VehicleRemindConfigDTO> getDetail(@JrdApiParamDoc(desc = "公司id", example = "1509") @RequestParam(value = "companyId") @Verify(param="companyId",rule="required")Integer companyId) {
        BaseDTO param = new BaseDTO();
        param.setLoginCompanyId(companyId);
        String url = mrCarConfigRestLocator.getRestUrl("/vehicleRemind/getDetail");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, VehicleRemindConfigDTO.class);
    }
    @PostMapping("/provider/vehicleRemind/saveOrUpdate")
    @RequestFunction(functionName = "车务提醒配置新增或更新")
    @ApiOperation(value = "车务提醒配置新增或更新（运营端）", notes = "作者：丁伟兵")
    public RestResponse<Void> saveOrUpdate(@RequestBody VehicleRemindConfigDTO param) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        param.setLoginCompanyId(param.getCompanyId());
        param.setLoginCompanyName(param.getCompanyName());
        param.setCustomerCode(param.getCompanyCode());
        param.setLoginUserId(providerLoginInfo.getBaseInfo().getStaffId());
        param.setLoginUserCode(providerLoginInfo.getBaseInfo().getStaffCode());
        param.setLoginUserName(providerLoginInfo.getBaseInfo().getStaffName());
        String url = mrCarConfigRestLocator.getRestUrl("/vehicleRemind/saveOrUpdate");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }


    @PostMapping(value = "/provider/vehicleRemind/queryListByPage")
    @JrdApiDoc(simpleDesc = "车务提醒配置列表", author = "hhd", resDataClass = VehicleRemindConfigRespDTO.class)
    @RequestFunction(functionName = "车务提醒配置列表")
    public RestResponse<PageDTO<VehicleRemindConfigRespDTO>> queryListByPage(@RequestBody VehicleRemindConfigReqDTO reqDTO) {
        Map<String, Object> param = new HashMap<>();
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        String restUrl = new MrCarConfigRestLocator().getRestUrl("/vehicleRemind/queryListByPage");
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        reqDTO.setDataPermType(providerLoginInfo.obtainDataPerm().getDataPermType());
        if(!Objects.equals(ProviderDataPermTypeEnum.ALL.getType(),providerLoginInfo.obtainDataPerm().getDataPermType())){
            return RestResponse.success(new PageDTO<>(reqDTO.getPage(), reqDTO.getPageSize(), 0L, null));
        }
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null, VehicleRemindConfigRespDTO.class);
    }
    //导出

    @PostMapping(value = "/provider/vehicleRemind/export")
    @JrdApiDoc(simpleDesc = "车务提醒配置导出", author = "hhd", resDataClass = Object.class)
    @RequestFunction(functionName = "车务提醒配置导出")
    @ExportExcelWeb(fileName = ExportExcelConstants.FILE_NAME_VEHICLE_REMIND_LIST_EXPORT_INFO,filePath = ExportExcelConstants.FILE_PATH,
            sheet = ExportExcelConstants.SHEET_NAME_VEHICLE_REMIND_LIST_EXPORT_INFO, c = VehicleRemindConfigRespDTO.class)
    public PageDTO export(@RequestBody VehicleRemindConfigReqDTO reqDTO, IzuEasyExcelSession izuEasyExcelSession,
                                                      HttpServletRequest request, HttpServletResponse response) {
        String restUrl = new MrCarConfigRestLocator().getRestUrl("/vehicleRemind/queryListByPage");
        AccountBaseInfo baseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
        izuEasyExcelSession.setUserName(baseInfo.getStaffName());
        reqDTO.setPageNum(izuEasyExcelSession.getPageNo());
        reqDTO.setPageSize(PAGE_SIZE);
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        reqDTO.setDataPermType(providerLoginInfo.obtainDataPerm().getDataPermType());
        if(!Objects.equals(ProviderDataPermTypeEnum.ALL.getType(),providerLoginInfo.obtainDataPerm().getDataPermType())){
            return null;
        }
        final HashMap<String, Object> restParam = Maps.newHashMapWithExpectedSize(1);
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        RestResponse<PageDTO<VehicleRemindConfigRespDTO>> res = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null, VehicleRemindConfigRespDTO.class);
        if(null!=res && res.isSuccess()){
            PageDTO<VehicleRemindConfigRespDTO> pageDTO = res.getData();
            return new PageDTO<>(pageDTO.getPage(), pageDTO.getPageSize(), pageDTO.getTotal(), pageDTO.getResult());
        }else {
            return null;
        }

    }
}
