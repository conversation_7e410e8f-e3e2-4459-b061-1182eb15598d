package com.izu.mrcar.providerController.paid;

import com.google.common.collect.Maps;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.dto.DicKeyValueDTO;
import com.izu.mrcar.service.paid.CompanyPaidExportService;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.dto.CompanyPaidOperationLogQryDTO;
import com.izu.user.dto.CompanyPaidQueryDTO;
import com.izu.user.dto.CompanyPaidRespDTO;
import com.izu.user.dto.CompanyPaidSaveDTO;
import com.izu.user.dto.paid.CompanyPaidServicesOperationLogDTO;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.enums.paid.CompanyPaidContractTypeEnum;
import com.izu.user.enums.paid.CooperationStatusEnum;
import com.izu.user.enums.paid.PamentMehodEnum;
import com.izu.user.enums.paid.PaymentBusinessTypeEnum;
import com.izu.user.enums.paid.SelfVehicleConfigEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@Api(tags = "付费业务管理")
public class CompanyPaidController {

    @Resource
    private CompanyPaidExportService companyPaidExportService;

    @PostMapping(UserUrlCenter.COMPANY_PAID_SERVICE_LIST)
    @RequestFunction(functionName = "付费业务管理列表")
    @ApiOperation(value = "付费业务管理列表")
    public RestResponse<PageDTO<CompanyPaidRespDTO>> getList(@RequestBody CompanyPaidQueryDTO reqDTO) {
        String restUrl =new UserRestLocator().getRestUrl(UserUrlCenter.COMPANY_PAID_SERVICE_LIST);
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, CompanyPaidRespDTO.class);
    }


    /**
     * @param reqDTO
     * @param izuEasyExcelSession
     * @param response
     * @return
     * @Description: 付费业务管理列表导出
     */
    @PostMapping(value = UserUrlCenter.COMPANY_PAID_SERVICE_LIST_EXPORT)
    @RequestFunction(functionName = "付费业务管理列表导出")
    @ApiOperation(value = "付费业务管理列表导出", notes = "作者：任伟光")
    public PageDTO exportGovPublicOrderList(@RequestBody CompanyPaidQueryDTO reqDTO,
                                            IzuEasyExcelSession izuEasyExcelSession,
                                            HttpServletResponse response) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        izuEasyExcelSession.setUserName(providerLoginInfo.getBaseInfo().getStaffName());
        reqDTO.setPage(1);
        reqDTO.setPageSize(2000);
        izuEasyExcelSession.setPageNo(1);
        return companyPaidExportService.getDataByPage(reqDTO, izuEasyExcelSession, response);
    }

    @PostMapping(UserUrlCenter.COMPANY_PAID_SERVICE_ADD_OR_UPDATE)
    @RequestFunction(functionName = "添加或更新付费业务管理")
    @ApiOperation("添加或更新付费业务管理")
    public RestResponse<Void> addOrUpdate(@RequestBody CompanyPaidSaveDTO companyPaidSaveDTO) {
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        String restUrl =new UserRestLocator().getRestUrl(UserUrlCenter.COMPANY_PAID_SERVICE_ADD_OR_UPDATE);
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        companyPaidSaveDTO.setOperatorId(loginBaseInfo.obtainBaseInfo().getStaffId());
        companyPaidSaveDTO.setOperatorCode(loginBaseInfo.obtainBaseInfo().getStaffCode());
        companyPaidSaveDTO.setOperatorName(loginBaseInfo.obtainBaseInfo().getStaffName());
        companyPaidSaveDTO.setOperatorMobile(loginBaseInfo.obtainBaseInfo().getMobile());
        if (Objects.isNull(companyPaidSaveDTO.getId())) {
            companyPaidSaveDTO.setCreatorName(loginBaseInfo.obtainBaseInfo().getStaffName());
            companyPaidSaveDTO.setCreationTime(new Date());
            companyPaidSaveDTO.setCreatorId(loginBaseInfo.obtainBaseInfo().getStaffId());
        }
        companyPaidSaveDTO.setModifierName(loginBaseInfo.obtainBaseInfo().getStaffName());
        companyPaidSaveDTO.setModificationTime(new Date());
        companyPaidSaveDTO.setModifierId(loginBaseInfo.obtainBaseInfo().getStaffId());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, companyPaidSaveDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null);
    }

    @PostMapping(UserUrlCenter.COMPANY_PAID_SERVICE_DETAIL)
    @ApiOperation("付费业务管理详情")
    @RequestFunction(functionName = "付费业务管理详情")
    public RestResponse<CompanyPaidRespDTO> detail(Integer id) {
        String restUrl =new UserRestLocator().getRestUrl(UserUrlCenter.COMPANY_PAID_SERVICE_DETAIL);
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, id);
        paramMap.put("id",id);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null);
    }


    @GetMapping(UserUrlCenter.COMPANY_PAID_SERVICE_PAID_ENUM)
    @ApiOperation("付费业务枚举")
    @RequestFunction(functionName = "付费业务枚举")
    public RestResponse<List<DicKeyValueDTO>> getPaidEnum() {
        List<DicKeyValueDTO> collect = Arrays.stream(PaymentBusinessTypeEnum.values()).filter(PaymentBusinessTypeEnum::getShow)
                .map(c -> new DicKeyValueDTO(c.getCode(), c.getName()))
                .collect(Collectors.toList());
        return RestResponse.success(collect);
    }


    @GetMapping(UserUrlCenter.COMPANY_PAID_SERVICE_CONTRACT_TYPE_ENUM)
    @ApiOperation("合同类型枚举")
    @RequestFunction(functionName = "合同类型枚举")
    public RestResponse<List<DicKeyValueDTO>> getContractTypeEnum() {
        List<DicKeyValueDTO> collect = Arrays.stream(CompanyPaidContractTypeEnum.values())
                .map(c -> new DicKeyValueDTO(c.getCode(), c.getName()))
                .collect(Collectors.toList());
        return RestResponse.success(collect);
    }
    @GetMapping(UserUrlCenter.COMPANY_PAID_SERVICE_VEHICLE_CONFIG_ENUM)
    @ApiOperation("企业违章查询类型枚举")
    @RequestFunction(functionName = "企业违章查询类型枚举")
    public RestResponse<List<DicKeyValueDTO>> getSelfVehicleConfigEnum() {
        List<DicKeyValueDTO> collect = Arrays.stream(SelfVehicleConfigEnum.values())
                .map(c -> new DicKeyValueDTO(c.getCode(), c.getName()))
                .collect(Collectors.toList());
        return RestResponse.success(collect);
    }



    @GetMapping(UserUrlCenter.COMPANY_PAID_SERVICE_PAYMENT_METHOD_ENUM)
    @ApiOperation("支付类型枚举")
    @RequestFunction(functionName = "支付类型枚举")
    public RestResponse<List<DicKeyValueDTO>> getPaymentMethodEnum() {
        List<DicKeyValueDTO> collect = Arrays.stream(PamentMehodEnum.values()).filter(PamentMehodEnum::getShow)
                .map(c -> new DicKeyValueDTO(c.getCode(), c.getDesc()))
                .collect(Collectors.toList());
        return RestResponse.success(collect);
    }
    @GetMapping(UserUrlCenter.COMPANY_PAID_SERVICE_COOPERATION_STATUS_ENUM)
    @ApiOperation("合作状态枚举")
    @RequestFunction(functionName = "合作状态枚举")
    public RestResponse<List<DicKeyValueDTO>> getCooperationStatusEnum() {
        List<DicKeyValueDTO> collect = Arrays.stream(CooperationStatusEnum.values())
                .map(c -> new DicKeyValueDTO(c.getCode(), c.getName()))
                .collect(Collectors.toList());
        return RestResponse.success(collect);
    }


    @PostMapping(UserUrlCenter.COMPANY_PAID_SERVICE_LIST_OPERATION_LOG)
    @ApiOperation("付费业务操作日志列表")
    @RequestFunction(functionName = "付费业务操作日志列表")
    public RestResponse<PageDTO<CompanyPaidServicesOperationLogDTO>> listOperationLog(@RequestBody CompanyPaidOperationLogQryDTO param) {
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.COMPANY_PAID_SERVICE_LIST_OPERATION_LOG);
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, CompanyPaidServicesOperationLogDTO.class);
    }
}
