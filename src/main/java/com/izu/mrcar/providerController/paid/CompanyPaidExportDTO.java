package com.izu.mrcar.providerController.paid;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import izu.org.apache.poi.ss.usermodel.BorderStyle;
import izu.org.apache.poi.ss.usermodel.HorizontalAlignment;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单导出信息
 * <p>
 * 2024/6/1 下午3:33
 *
 * <AUTHOR>
 */
@HeadStyle(
        fillForegroundColor = 22,
        fillBackgroundColor = 22
)
@ColumnWidth(20)
@ContentStyle(
        horizontalAlignment = HorizontalAlignment.CENTER,
        borderBottom = BorderStyle.THIN,
        borderLeft = BorderStyle.THIN,
        borderRight = BorderStyle.THIN,
        borderTop = BorderStyle.THIN,
        wrapped = true
)
@Data
@ApiModel("增值业务管理列表")
public class CompanyPaidExportDTO {

    @ApiModelProperty(value = "订单号")
    @ExcelProperty(value = "订单编号", order = 1)
    private String orderNo;

    @ApiModelProperty(value = "客户名称")
    @ExcelProperty(value = "客户名称", order = 2)
    private String companyName;

    @ExcelProperty(value = "增值业务类型", order = 3)
    private String paidServiceTypeStr;

    @ApiModelProperty(value = "单价")
    @ExcelProperty(value = "单价", order = 4)
    private BigDecimal price;

    @ExcelProperty(value = "税率", order = 5)
    private String taxRateStr;

    @ExcelProperty(value = "合同开始日期", order = 6)
    private Date startDate;

    @ExcelProperty(value = "合同结束日期", order = 7)
    private Date expirationDate;

    @ExcelProperty(value = "合作状态", order = 8)
    private String cooperationStatusStr;

    @ExcelProperty(value = "出账日", order = 9)
    private Byte billingDayOfMonth;

    @ExcelProperty(value = "支付类型", order = 10)
    private String paymentMethodStr;

    @ExcelProperty(value = "关联合同类型", order = 11)
    private String contractCategoryStr;

    @ExcelProperty(value = "合同编码", order = 12)
    private String contractCode;

    @ExcelProperty(value = "合同所有人", order = 13)
    private String contractSalesName;

    @ExcelProperty(value = "合同维护人", order = 14)
    private String maintainName;

    @ExcelProperty(value = "签约主体", order = 15)
    private String signingEntityFinancialName;

    @ExcelProperty(value = "销售主体", order = 16)
    private String salesEntityFinancialName;

    @ExcelProperty(value = "创建人", order = 17)
    private String creatorName;

    @ExcelProperty(value = "创建时间", order = 18)
    private Date creationTime;

    @ExcelProperty(value = "修改人", order = 19)
    private String modifierName;

    @ExcelProperty(value = "修改时间", order = 20)
    private Date modificationTime;
}
