package com.izu.mrcar.providerController.coupon;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.izu.MrCarCouponRestLocator;
import com.izu.dto.ActivityCouponEntityDTO;
import com.izu.dto.ActivityDTO;
import com.izu.dto.CouponDTO;
import com.izu.enums.ActivityEnum;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.StructNodeDTO;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import com.izu.mrcar.common.constants.Constant;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.enums.perm.ProviderDataPermTypeEnum;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2019-09-07 16:03
 */
@RequestMapping("/activityCoupon")
@RestController("providerCouponController")
public class CouponController {

    private static final Logger logger = LoggerFactory.getLogger(CouponController.class);

    private final MrCarCouponRestLocator couponRestLocator = new MrCarCouponRestLocator();

    /**
     * 新建优惠活动
     *
     * @param activityDTO
     * @param request
     * @return
     */
    @RequestMapping("/create")
//    @RequiresPermissions(value = "coupon_activity_add")
    @RequestFunction(functionName = "新增优惠券活动")
    public RestResponse create(@RequestBody ActivityDTO activityDTO, HttpServletRequest request) {
        try {
            Map<String, String> userPermissionData = getUserPermissionData(request);
            Map<String, Object> paramsMap = transDtoToParamsMap(activityDTO);
            return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, couponRestLocator.getRestUrl(CREATE_COUPON), paramsMap, userPermissionData);
        } catch (Exception e) {
            logger.error("创建优惠券异常", e);
            return RestResponse.fail(Constant.SYS_ERROR);
        }
    }


    /**
     * 优惠券列表查询
     *
     * @param activityDTO
     * @param request
     * @return
     */
    @RequestMapping("/list")
//    @RequiresPermissions(value = "coupon_activity")
    @RequestFunction(functionName = "优惠券列表查询")
    public RestResponse list(@RequestBody ActivityDTO activityDTO, HttpServletRequest request) {
        try {
            ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
            if (!ProviderDataPermTypeEnum.ALL.getType().equals(providerLoginInfo.obtainDataPerm().getDataPermType())){
                return RestResponse.success(new PageDTO(1,10,0,Collections.emptyList()));
            }
            Map<String, String> userPermissionData = getUserPermissionData(request);
            Map<String, Object> paramsMap = transDtoToParamsMap(activityDTO);
            return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, couponRestLocator.getRestUrl(COUPON_LIST), paramsMap, userPermissionData);
        } catch (Exception e) {
            logger.error("查询优惠券异常", e);
            return RestResponse.fail(Constant.SYS_ERROR);
        }
    }


    /**
     * 优惠券券码生成
     *
     * @param couponDTO
     * @param request
     * @return
     */
    @RequestMapping("/couponGenerate")
//    @RequiresPermissions(value = "coupon_generate")
    @RequestFunction(functionName = "优惠券生成")
    public RestResponse couponGenerate(@RequestBody CouponDTO couponDTO, HttpServletRequest request) {
        try {
            Map<String, String> userPermissionData = getUserPermissionData(request);
            Map<String, Object> paramsMap = transDtoToParamsMap(couponDTO);
            return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, couponRestLocator.getRestUrl(COUPON_GENERATE), paramsMap, userPermissionData);
        } catch (Exception e) {
            logger.error("优惠券券码生成失败", e);
            return RestResponse.fail(Constant.SYS_ERROR);
        }
    }


    /**
     * 优惠券定向发送
     *
     * @param couponDTO
     * @param request
     * @return
     */
    @RequestMapping("/couponPublish")
//    @RequiresPermissions(value = "coupon_send")
    @RequestFunction(functionName = "定向发送优惠券")
    public RestResponse couponPublish(@RequestBody CouponDTO couponDTO, HttpServletRequest request) {
        try {
            Map<String, String> userPermissionData = getUserPermissionData(request);
            Map<String, Object> paramsMap = transDtoToParamsMap(couponDTO);
            return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, couponRestLocator.getRestUrl(COUPON_PUBLISH), paramsMap, userPermissionData);
        } catch (Exception e) {
            logger.error("优惠券发送失败", e);
            return RestResponse.fail(Constant.FAIL_STATUS);
        }
    }


    /**
     * 优惠券详情查询
     *
     * @param activityDTO
     * @param request
     * @return
     */
    @RequestMapping("/activityDetail")
    @RequestFunction(functionName = "优惠券详情查询")
    public RestResponse activityDetail(@RequestBody ActivityDTO activityDTO, HttpServletRequest request) {
        try {
            Map<String, String> userPermissionData = getUserPermissionData(request);
            Map<String, Object> paramsMap = transDtoToParamsMap(activityDTO);
            return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, couponRestLocator.getRestUrl(ACTIVITY_DETAIL), paramsMap, userPermissionData);
        } catch (Exception e) {
            logger.error("优惠券详情查询失败", e);
            return RestResponse.fail(Constant.SYS_ERROR);
        }
    }


    /**
     * 优惠券发放信息查询
     *
     * @param couponDTO
     * @param request
     * @return
     */
    @RequestMapping("/couponPublishList")
    @RequestFunction(functionName = "优惠券发放记录")
    public RestResponse couponPublishList(@RequestBody CouponDTO couponDTO, HttpServletRequest request) {
        try {
            Map<String, String> userPermissionData = getUserPermissionData(request);
            Map<String, Object> paramsMap = transDtoToParamsMap(couponDTO);
            return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, couponRestLocator.getRestUrl(COUPON_PUBLISH_LIST), paramsMap, userPermissionData);
        } catch (Exception e) {
            logger.error("查询优惠券发放信息异常", e);
            return RestResponse.fail(Constant.SYS_ERROR);
        }
    }


    /**
     * 优惠券列表查询
     * 修改时间：2019-12-26 16:56:20
     * @param restObject
     * @return
     */
    @RequestMapping("/provider/couponEntityList")
//    @RequiresPermissions(value = "couponNumList")
    @RequestFunction(functionName = "优惠券实体列表")
    public RestResponse couponList(@RequestBody JSONObject restObject, HttpServletRequest request) {
        try {
            ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
            if (!ProviderDataPermTypeEnum.ALL.getType().equals(providerLoginInfo.obtainDataPerm().getDataPermType())){
                return RestResponse.success(new PageDTO(1,10,0,Collections.emptyList()));
            }
            RestResponse response = RestClient.requestInside(BaseHttpClient.HttpMethod.POST, couponRestLocator.getRestUrl(COUPONS), restObject.getInnerMap(), null);
            return response;
        } catch (Exception e) {
            logger.error("查询优惠券列表失败", e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }

    @RequestMapping(value = "/provider/downLoadCouponList", method = RequestMethod.POST)
    @RequestFunction(functionName = "优惠券信息导出")
    public void downloadStructList(String activityNo, String couponCode, String activityName, Byte couponType,
                                   Byte usageStatus,String customerName,String orderNo, HttpServletResponse response) throws Exception{
        try {
            //查询该企业下的部门列表信息
            List<ActivityCouponEntityDTO> couponEntityDTOList = null;
            ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
            if (ProviderDataPermTypeEnum.ALL.getType().equals(providerLoginInfo.obtainDataPerm().getDataPermType())){
                Map<String, Object> paramsMap = new HashMap<>();
                paramsMap.put("activityNo",activityNo);
                paramsMap.put("couponCode",couponCode);
                paramsMap.put("activityName",activityName);
                paramsMap.put("couponType",couponType);
                paramsMap.put("usageStatus",usageStatus);
                paramsMap.put("customerName",customerName);
                paramsMap.put("orderNo",orderNo);
                logger.info("【优惠券列表】请求优惠券微服务查询即将导出的数据，入参：{}",JSON.toJSONString(paramsMap));
                RestResponse restResponse= RestClient.requestForList(BaseHttpClient.HttpMethod.POST, couponRestLocator.getRestUrl(COUPON_QUERY_FOR_EXPORT), paramsMap, null, ActivityCouponEntityDTO.class);
                if(restResponse.isSuccess()){
                    couponEntityDTOList = (List<ActivityCouponEntityDTO>) restResponse.getData();
                }
            }

            response.setContentType("application/vnd.ms-excel");
            response.setHeader("Content-disposition",  "attachment;filename*=utf-8''"  + URLEncoder.encode("优惠券导出"+ ".xls", "UTF-8"));
            ServletOutputStream out = response.getOutputStream();
            BufferedOutputStream buffOut = null;

            XSSFWorkbook wb = new XSSFWorkbook();
            XSSFSheet sheet = wb.createSheet("优惠券数据");
            XSSFRow row = sheet.createRow(0);
            row.createCell(0).setCellValue("优惠券码");
            row.createCell(1).setCellValue("生成批次");
            row.createCell(2).setCellValue("优惠活动编号");
            row.createCell(3).setCellValue("优惠活动名称");
            row.createCell(4).setCellValue("优惠活动类型");
            row.createCell(5).setCellValue("券码状态");
            row.createCell(6).setCellValue("客户名称");
            row.createCell(7).setCellValue("订单编号");
            row.createCell(8).setCellValue("活动时长");
            row.createCell(9).setCellValue("活动开始时间");
            row.createCell(10).setCellValue("活动结束时间");
            row.createCell(11).setCellValue("创建人");
            row.createCell(12).setCellValue("发放人");
            row.createCell(13).setCellValue("创建时间");
            row.createCell(14).setCellValue("发放时间");
            row.createCell(15).setCellValue("使用时间");
            if(couponEntityDTOList == null || couponEntityDTOList.size() == 0){
                //异常情况，企业下没有部门信息,则设置一个默认部门
                row = sheet.createRow(1);
                row.createCell(0).setCellValue("暂无数据");
            }else{
                int i = 0;
                for( i= 0;i<couponEntityDTOList.size();i++){
                    ActivityCouponEntityDTO couponEntityDTO = couponEntityDTOList.get(i);
                    StructNodeDTO parentStruct = null;
                    row = sheet.createRow(i+1);
                    row.createCell(0).setCellValue(couponEntityDTO.getCouponCode() != null ? couponEntityDTO.getCouponCode() : "");
                    row.createCell(1).setCellValue(couponEntityDTO.getBatchNo() != null ? couponEntityDTO.getBatchNo() : "");
                    row.createCell(2).setCellValue(couponEntityDTO.getActivityNo() != null ? couponEntityDTO.getActivityNo() : "");
                    row.createCell(3).setCellValue(couponEntityDTO.getActivityName() != null ? couponEntityDTO.getActivityName() : "");
                    row.createCell(4).setCellValue(ActivityEnum.CouponTypeEnum.getNameByCode(couponEntityDTO.getCouponType()));
                    row.createCell(5).setCellValue(ActivityEnum.CouponStatusEnum.getNameByCode(couponEntityDTO.getUsageStatus()));
                    row.createCell(6).setCellValue(couponEntityDTO.getCustomerName() != null ? couponEntityDTO.getCustomerName() : "");
                    row.createCell(7).setCellValue(couponEntityDTO.getOrderNo() != null ? couponEntityDTO.getOrderNo() : "");
                    row.createCell(8).setCellValue(couponEntityDTO.getActivityDuration() != null ? couponEntityDTO.getActivityDuration()+"" : "");
                    row.createCell(9).setCellValue(couponEntityDTO.getActivityDateStart() != null ? DateUtil.formatDate(couponEntityDTO.getActivityDateStart()) : "");
                    row.createCell(10).setCellValue(couponEntityDTO.getActivityDateEnd() != null ? DateUtil.formatDate(couponEntityDTO.getActivityDateEnd()) : "");
                    row.createCell(11).setCellValue(couponEntityDTO.getActivityCreateName() != null ? couponEntityDTO.getActivityCreateName() : "");
                    row.createCell(12).setCellValue(couponEntityDTO.getPublishCreateName() != null ? couponEntityDTO.getPublishCreateName() : "");
                    row.createCell(13).setCellValue(couponEntityDTO.getActivityCreateDate() != null ? DateUtil.formatDateTime(couponEntityDTO.getActivityDateStart()) : "");
                    row.createCell(14).setCellValue(couponEntityDTO.getReceiveTime() != null ? DateUtil.formatDateTime(couponEntityDTO.getReceiveTime()) : "");
                    row.createCell(15).setCellValue(couponEntityDTO.getUsedTime() != null ? DateUtil.formatDateTime(couponEntityDTO.getUsedTime()) : "");

/*                    row.createCell(1).setCellValue(structNodeDTO.getStructNo() !=null ? structNodeDTO.getStructNo() : "");
                    //判断上级部门是否为空
                    if(parentStruct ==null){
                        row.createCell(2).setCellValue("");
                        row.createCell(3).setCellValue("");
                    }else{
                        row.createCell(2).setCellValue(parentStruct.getStructName()!=null ? parentStruct.getStructName() : "");
                        row.createCell(3).setCellValue(parentStruct.getStructNo() != null ? parentStruct.getStructNo() : "");
                    }
                    if(null != structNodeDTO.getParentId() && structNodeDTO.getParentId() ==-1){ //parent_id 为-1时，赋值为企业名称
                        row.createCell(2).setCellValue(companyName);
                    }*/
                }
            }
            buffOut = new BufferedOutputStream(out);
            wb.write(buffOut);
            logger.info("【优惠券列表】导出优惠券信息成功！");
        } catch (Exception e) {
            logger.error("【优惠券列表】导出优惠券列表数据downLoadCouponList error", e);
            throw e;
        }
    }

    private Map<String, String> getUserPermissionData(HttpServletRequest request) throws UnsupportedEncodingException {
        AccountBaseInfo baseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
        HashMap<String, String> headers = new HashMap<>();
        headers.put("customerId", baseInfo.getStaffId() + "");
        headers.put("customerName", URLEncoder.encode(baseInfo.getStaffName(), "utf-8"));
        return headers;
    }

    private Map<String, Object> transDtoToParamsMap(Object object) throws IllegalAccessException {
        Map<String, Object> params = new HashMap<>();
        Class clazz = object.getClass();
        List<Field> fields = new ArrayList<>();
        while (clazz != null) {
            fields.addAll(Arrays.asList(clazz.getDeclaredFields()));
            clazz = clazz.getSuperclass();
        }
        for (Field field : fields) {
            field.setAccessible(true);
            params.put(field.getName(), field.get(object));
        }
        return params;
    }

    /**
     * 企业优惠券信息列表
     *
     * @param params
     * @return
     */
    @RequestMapping("/provider/companyCouponList")
    @RequestFunction(functionName = "企业优惠券信息")
    public RestResponse companyCouponList(@RequestBody JSONObject params) {
        try {
            return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, couponRestLocator.getRestUrl(COMPANY_COUPON_QUERY), params.getInnerMap(), null);
        } catch (Exception e) {
            logger.error("查询企业优惠券失败", e);
            return RestResponse.fail(Constant.SYS_ERROR);
        }
    }


    private static final String CREATE_COUPON = "/activityCoupon/create";
    private static final String COUPON_LIST = "/activityCoupon/list";
    private static final String COUPON_GENERATE = "/activityCoupon/couponGenerate";
    private static final String COUPON_PUBLISH = "/activityCoupon/couponPublish";
    private static final String ACTIVITY_DETAIL = "/activityCoupon/activityDetail";
    private static final String COUPON_PUBLISH_LIST = "/activityCoupon/couponPublishList";
    private static final String COUPONS = "/activityCoupon/provider/couponEntityList";
    private static final String COMPANY_COUPON_QUERY = "/provider/activityCoupon/companyCouponList";
    private static final String COUPON_QUERY_FOR_EXPORT = "/activityCoupon/provider/couponEntityListForExport";


}
