package com.izu.mrcar.providerController.coupon;

import com.izu.config.MrCarConfigRestLocator;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.enums.perm.ProviderDataPermTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/couponType")
public class CouponTypeController {
    private static final Logger logger = LoggerFactory.getLogger(CouponTypeController.class);

    private final MrCarConfigRestLocator configRestLocator = new MrCarConfigRestLocator();


    @RequestMapping(value = "/detail")
//    @RequiresPermissions(value = "couponImg_modify")
    @RequestFunction(functionName = "优惠券配置详情")
    public RestResponse detail(@RequestBody Map<String,Object> paraMap, HttpServletRequest request){
        String restUrl = configRestLocator.getRestUrl("/couponType/detail");

        try {
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Object.class);
        } catch(Exception e) {
            logger.error("detail Exception：" + e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }

    @RequestMapping(value = "/list")
//    @RequiresPermissions(value = "couponImg_manage")
    @RequestFunction(functionName = "优惠券配置列表")
    public RestResponse list(HttpServletRequest request){
        String restUrl = configRestLocator.getRestUrl("/couponType/list");

        try {
            ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
            if (!ProviderDataPermTypeEnum.ALL.getType().equals(providerLoginInfo.obtainDataPerm().getDataPermType())){
                return RestResponse.success(new PageDTO(1,10,0, Collections.emptyList()));
            }
            Map<String,Object> paraMap=new HashMap<>();
            return RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Object.class);
        } catch(Exception e) {
            logger.error("list Exception：" + e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }

    @RequestMapping(value = "/add")
//    @RequiresPermissions(value = "couponImg_add")
    @RequestFunction(functionName = "新增优惠券配置")
    public RestResponse add(@RequestBody Map<String,Object> paraMap, HttpServletRequest request){
        String restUrl = configRestLocator.getRestUrl("/couponType/add");

        try {
            AccountBaseInfo baseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
            paraMap.put("createId", baseInfo.getStaffId());
            paraMap.put("createName", baseInfo.getStaffName());
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Object.class);
        } catch(Exception e) {
            logger.error("add Exception：" + e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }

    @RequestMapping(value = "/update")
    @RequestFunction(functionName = "编辑优惠券配置")
    public RestResponse update(@RequestBody Map<String,Object> paraMap, HttpServletRequest request){
        String restUrl = configRestLocator.getRestUrl("/couponType/update");

        try {
            AccountBaseInfo baseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
            paraMap.put("updateId", baseInfo.getStaffId());
            paraMap.put("updateName", baseInfo.getStaffName());
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Object.class);
        } catch(Exception e) {
            logger.error("update Exception：" + e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }

    @RequestMapping(value = "/updateStatus")
    public RestResponse updateStatus(@RequestBody Map<String,Object> paraMap, HttpServletRequest request){
        String restUrl = configRestLocator.getRestUrl("/couponType/updateStatus");

        try {
            AccountBaseInfo baseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
            paraMap.put("updateId", baseInfo.getStaffId());
            paraMap.put("updateName", baseInfo.getStaffName());
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Object.class);
        } catch(Exception e) {
            logger.error("updateStatus Exception：" + e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }

    @RequestMapping(value = "/delete")
    @RequestFunction(functionName = "删除优惠券配置")
    public RestResponse delete(@RequestBody Map<String,Object> paraMap, HttpServletRequest request){
        String restUrl = configRestLocator.getRestUrl("/couponType/delete");

        try {
            AccountBaseInfo baseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
            paraMap.put("updateId", baseInfo.getStaffId());
            paraMap.put("updateName", baseInfo.getStaffName());
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Object.class);
        } catch(Exception e) {
            logger.error("delete Exception：" + e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }
}
