package com.izu.mrcar.interceptor;

import org.apache.http.HttpException;
import org.apache.http.HttpRequest;
import org.apache.http.HttpRequestInterceptor;
import org.apache.http.protocol.HttpContext;
import org.slf4j.MDC;

import java.io.IOException;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2022/11/26 0:28
 */
public class HttpClientTraceIdInterceptor implements HttpRequestInterceptor {
    @Override
    public void process(HttpRequest httpRequest, HttpContext httpContext) throws HttpException, IOException {
        //String traceId = MDC.get("reqId");
        //当前线程调用中有traceId，则将该traceId进行透传
//        if (traceId != null) {
//            //添加请求体
//            httpRequest.addHeader("reqId", traceId);
//        }
    }
}
