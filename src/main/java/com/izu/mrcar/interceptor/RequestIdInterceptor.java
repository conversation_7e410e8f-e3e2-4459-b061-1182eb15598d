package com.izu.mrcar.interceptor;

import com.izu.framework.web.rest.util.RequestIdProvider;
import org.springframework.web.servlet.HandlerInterceptor;
import org.slf4j.MDC;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @description: 请求id
 * @date 2022/11/26 0:04
 */
public class RequestIdInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        //如果有上层调用就用上层的ID
        String reqId = RequestIdProvider.getRequestId(request);

        MDC.put("reqId", reqId);

        return true;
    }


    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception {
        //调用结束后删除
        MDC.remove("reqId");
    }

}
