package com.izu.mrcar.controller.dictionary;

import com.izu.asset.consts.VehicleColorEnum;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.dto.DicKeyValueDTO;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.enums.CompanyAttributeEnum;
import com.izu.user.enums.role.SystemTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/dictionary")
@Api(tags = "字典管理")
public class CarController {

	private static final Logger logger = LoggerFactory.getLogger(CarController.class);
	/**
	 * 加载配置服务mrcar-config-core访问域名及项目名
	 */
	@Value("${mrcar-asset-core.host.url}")
	private String ASSET_HOST_URL;
	@Value("${bussiness.chedui.companyid}")
	private Integer CHEDUI_COMPANYID;

    /**
     * 查询车级信息(开城时所有车级的查询)
     **/
	@RequestMapping(value = "/listCarLevel", method = {RequestMethod.GET, RequestMethod.POST})
	@ApiImplicitParams({
			@ApiImplicitParam(name="levelName",value="级别名称",paramType="String"),
			@ApiImplicitParam(name="usableRange",value="车级适用范围;1:通用;2:内部用车;3:商务用车;4:零散用车",paramType="Byte",example = "2"),
			@ApiImplicitParam(name="valid",value="状态: 1 有效 0 无效",paramType="Byte"),
	})
	@ApiOperation("查询车级信息(开城时所有车级的查询)")
    public RestResponse listCarLevel(String levelName,Byte usableRange, Byte valid) {
		String restUrl = ASSET_HOST_URL + "leve/getCarLevelList";
		Map<String, Object> paraMap = new HashMap<String, Object>();
		paraMap.put("levelName", levelName);
		LoginBaseInfo loginBaseInfo1 = LoginSessionUtil.getBaseLoginInfo();
		if(loginBaseInfo1.getSystemType().equals(SystemTypeEnum.CUSTOMER.getCode())) {
			ClientLoginInfo loginBaseInfo = LoginSessionUtil.getClientLoginInfo();
			//首汽企业才能看到接单管理
			if (usableRange == null) {
				if (loginBaseInfo.getClientCompany().getCompanyAttribute().byteValue() == CompanyAttributeEnum.SQ_QY.getValue()) {
					usableRange = 3;
				} else {
					usableRange = 2;
				}
			}
		}else if(loginBaseInfo1.getSystemType().equals(SystemTypeEnum.PROVIDER.getCode())){
            if(usableRange == null){
            	usableRange = 3;
			}
		}else{
			usableRange = 2;
		}
    	paraMap.put("usableRange", usableRange);
    	paraMap.put("valid", valid);
    	return RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Map.class);
    } 
    
    @RequestMapping(value = "/listAllCarLevel")
    public RestResponse listAllCarLevel(String levelName){
    	String restUrl = ASSET_HOST_URL + "leve/getCarLevelList";
    	Map<String, Object> paraMap = new HashMap<String, Object>();
		paraMap.put("levelName", levelName);
		paraMap.put("valid", 1);
    	paraMap.put("usableRange", 2);
    	return RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Map.class);
    } 
    
    /**
     * 查询车级详情
     **/
    @RequestMapping(value = "/getCarLevelById")
    public RestResponse getCarLevelById(Integer leveId){
    	String restUrl = ASSET_HOST_URL + "leve/getCarLeveInfo";
    	Map<String, Object> paraMap = new HashMap<String, Object>();
    	paraMap.put("leveId", leveId);
    	return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Map.class);
    }     
	/**
     * 查询车型列表
     **/
    @RequestMapping(value = "/getModelList")
    public RestResponse getModelList(
									 String modelName, Integer levelId, Byte status,String modeCode,Integer brandId,HttpServletRequest request){
    	String restUrl = ASSET_HOST_URL + "dic/getModelListByParam";
    	Map<String, Object> paraMap = new HashMap<String, Object>();
		LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
		Integer modeSource=0;
		if(loginBaseInfo!=null){
			Integer companyId=loginBaseInfo.obtainBelongCompanyId();
			if(companyId!=null&&companyId.intValue()==CHEDUI_COMPANYID.intValue()){
				modeSource=1;
			}
		}
    	paraMap.put("levelId", levelId);
    	paraMap.put("modeSource", modeSource);
    	paraMap.put("modelName", modelName);
    	paraMap.put("status", status);
    	paraMap.put("modeCode", modeCode);
    	paraMap.put("brandId", brandId);
    	return RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Map.class);
    }
	/**
     * 查询车级详情
     **/
    @RequestMapping(value = "/getModelByModeCode")
    public RestResponse getModelByCode(String modelCode,HttpServletRequest request){
    	String restUrl = ASSET_HOST_URL + "dic/getModelByModeCode";
    	Map<String, Object> paraMap = new HashMap<>();
		LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
		Integer modeSource=0;
		if(loginBaseInfo!=null){
			Integer companyId=loginBaseInfo.obtainBelongCompanyId();
			if(companyId!=null&&companyId.intValue()==CHEDUI_COMPANYID.intValue()){
				modeSource=1;
			}
		}
    	paraMap.put("modelCode", modelCode);
    	paraMap.put("modeSource", modeSource);
    	return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Map.class);
    }
	/**
	 * 查询品牌列表
	 **/
	@RequestMapping(value = "/getCarBrandList")
	public RestResponse getCarBrandList(String brandName, Byte valid,HttpServletRequest request){
		String restUrl = ASSET_HOST_URL + "brand/getCarBrandList";
		LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
		Integer brandSource=0;
		if(loginBaseInfo!=null){
			Integer companyId=loginBaseInfo.obtainBelongCompanyId();
			if(companyId!=null&&companyId.intValue()==CHEDUI_COMPANYID.intValue()){
				brandSource=1;
			}
		}
		Map<String, Object> paraMap = new HashMap<String, Object>();
		paraMap.put("brandSource", brandSource);
		paraMap.put("brandName", brandName);
		paraMap.put("valid", valid);
		return RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Map.class);
	}
	/**
	 * 查询品牌详情
	 **/
	@RequestMapping(value = "/getCarBrandByBrandCode")
	public RestResponse getCarBrandByBrandCode(
											   String  brandCode,HttpServletRequest request){
		String restUrl = ASSET_HOST_URL + "brand/getCarBrandByBrandCode";
		Map<String, Object> paraMap = new HashMap<>();
		LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
		Integer brandSource=0;
		if(loginBaseInfo!=null){
			Integer companyId=loginBaseInfo.obtainBelongCompanyId();
			if(companyId!=null&&companyId.intValue()==CHEDUI_COMPANYID.intValue()){
				brandSource=1;
			}
		}
		paraMap.put("brandSource", brandSource);
		paraMap.put("brandCode", brandCode);
		return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Map.class);
	}

	/**
	 * 车辆颜色
	 * @return
	 */
	@GetMapping("/getVehicleColorList")
	@ApiOperation("车辆颜色枚举信息")
	public RestResponse getVehicleColorList(){
		List<DicKeyValueDTO> colorList =  Arrays.stream(VehicleColorEnum.values())
				.map(c -> new DicKeyValueDTO(c.getCode(), c.getValue()))
				.collect(Collectors.toList());
		return RestResponse.success(colorList);
	}

}
