package com.izu.mrcar.controller.dictionary;

import com.izu.mrcar.common.constants.Constant;
import com.izu.mrcar.common.constants.GlobalResponse;
import com.izu.mrcar.utils.DateUtil;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/dictionary")
public class TimeController {

	@RequestMapping("/getcurrenttime")
	public GlobalResponse getcurrenttime(){
		try {
			String currentDateTime = DateUtil.getCurrentDateTime(DateUtil.TIME_FORMAT);
			return GlobalResponse.getResult(Constant.SUCCESS_STATUS, currentDateTime);
		} catch (Exception e) {
			e.printStackTrace();
			throw e;
		}
	}
}
