package com.izu.mrcar.controller.businessRentalOrder;

import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.dto.order.businessRentalOrder.BusinessRentalOrderListReqDTO;
import com.izu.mrcar.order.dto.order.businessRentalOrder.BusinessRentalOrderListRespDTO;
import com.izu.mrcar.service.order.BusinessRentalOrderService;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 商务包车相关订单接口-客户端
 *
 * <AUTHOR> on  2024/11/26 下午8:02
 */


@RestController
@RequestMapping("client")
public class BusinessRentalOrderController {


    private final BusinessRentalOrderService businessRentalOrderService;

    public BusinessRentalOrderController(BusinessRentalOrderService businessRentalOrderService) {
        this.businessRentalOrderService = businessRentalOrderService;
    }


    /**
     * 分页列表
     */
    @PostMapping(value = MrcarOrderRestMsgCenter.BUSINESS_PACKAGE_ORDER_LIST, produces = "application/json;charset=UTF-8")
    public RestResponse<PageDTO<BusinessRentalOrderListRespDTO>> pageList(@RequestBody BusinessRentalOrderListReqDTO listReqDTO) {
        return businessRentalOrderService.pageList(listReqDTO);
    }

    /**
     * 详情
     */
    @GetMapping(value = MrcarOrderRestMsgCenter.BUSINESS_PACKAGE_ORDER_DETAIL)
    public RestResponse<BusinessRentalOrderListRespDTO> detail(Long orderId) {

        return businessRentalOrderService.detail(orderId);
    }


    /**
     * 商务包车导出
     */
    @PostMapping(value = MrcarOrderRestMsgCenter.BUSINESS_PACKAGE_ORDER_LIST_EXPORT)
    @ExportExcelWeb(fileName = "商务包车订单.xlsx",
            filePath = "/data/logs/mrcar/log/temp",
            sheet = "商务包车订单", c = BusinessRentalOrderListRespDTO.class)
    public PageDTO<BusinessRentalOrderListRespDTO> exportOrderList(@RequestBody BusinessRentalOrderListReqDTO reqDto,
                                                                   IzuEasyExcelSession izuEasyExcelSession,
                                                                   HttpServletRequest request, HttpServletResponse response) {
        return businessRentalOrderService.exportOrderList(reqDto, izuEasyExcelSession);
    }


}
