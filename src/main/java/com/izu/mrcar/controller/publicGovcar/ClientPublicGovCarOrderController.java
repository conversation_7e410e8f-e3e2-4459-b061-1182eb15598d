package com.izu.mrcar.controller.publicGovcar;

import com.google.common.collect.Maps;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.order.ExportPDFDTO;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.consts.govPublicCar.ClientPublicGovCarOrderOptReqDTO;
import com.izu.mrcar.order.dto.publicGovcar.PublicGovCarOrderDetailInfoRespDTO;
import com.izu.mrcar.order.dto.publicGovcar.PublicGovCarOrderOperationLogDTO;
import com.izu.mrcar.order.dto.publicGovcar.PublicGovCarOrderQueryDTO;
import com.izu.mrcar.order.dto.publicGovcar.PublicGovCarOrderRespDTO;
import com.izu.mrcar.order.dto.publicGovcar.req.ListPublicGovCarOptLogReqDTO;
import com.izu.mrcar.service.publicGovOrder.PublicGovCarOrderExportService;
import com.izu.mrcar.service.publicGovOrder.PublicGovCarOrderOptLogService;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.perm.OrderDataPermUtil;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 公务用车对公 app相关接口
 * @date 2024/8/15 10:02
 */
@RestController
@Api(tags = "公务用车对公-订单")
public class ClientPublicGovCarOrderController {

    @Resource
    private PublicGovCarOrderExportService orderExportService;

    private static final MrcarOrderRestLocator mrcarOrderRestLocator = new MrcarOrderRestLocator();

    @PostMapping(MrcarOrderRestMsgCenter.CLIENT_PUBLIC_GOV_CAR_ORDER_LIST)
    @RequestFunction(functionName = "公务用车对公-订单列表")
    @ApiOperation(value = "订单列表")
    public RestResponse<PageDTO<PublicGovCarOrderRespDTO>> getList(@RequestBody PublicGovCarOrderQueryDTO reqDTO) {
        String restUrl =mrcarOrderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.PROVIDER_PUBLIC_GOV_CAR_ORDER_LIST);
        OrderDataPermUtil.setDatePerm(reqDTO);
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, PublicGovCarOrderRespDTO.class);
    }


    @GetMapping(MrcarOrderRestMsgCenter.CLIENT_PUBLIC_GOV_CAR_ORDER_INFO_DETAIL)
    @RequestFunction(functionName = "公务用车对公-订单详情")
    @ApiOperation(value = "订单详情")
    public RestResponse<PublicGovCarOrderDetailInfoRespDTO> getOrderInfoDetail(@RequestParam("orderNo") String orderNo) {
        String restUrl =mrcarOrderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.PROVIDER_PUBLIC_GOV_CAR_ORDER_INFO_DETAIL);
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put("orderNo", orderNo);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.GET, restUrl, paramMap, null);
    }


    /**
     * @param reqDTO
     * @param izuEasyExcelSession
     * @param request
     * @param response
     * @return
     * @Description: 公务用车对公-订单列表导出
     */
    @PostMapping(value = MrcarOrderRestMsgCenter.CLIENT_PUBLIC_GOV_CAR_ORDER_LIST_EXPORT)
    @RequestFunction(functionName = "公务用车对公-订单列表导出")
    @ApiOperation(value = "公务用车对公-订单列表导出", notes = "作者：任伟光")
    public void exportGovPublicOrderList(@RequestBody PublicGovCarOrderQueryDTO reqDTO,
                                            IzuEasyExcelSession izuEasyExcelSession,
                                            HttpServletRequest request, HttpServletResponse response) {
        ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
        OrderDataPermUtil.setDatePerm(reqDTO);
        reqDTO.setPage(1);
        reqDTO.setPageSize(10000);
        izuEasyExcelSession.setUserName(clientLoginInfo.getBaseInfo().getStaffName());
        orderExportService.exportGovPublicOrderList(reqDTO, izuEasyExcelSession, request, response);
    }


    @PostMapping("/client/publicGovCar/order/forceFinishTrip")
    @RequestFunction(functionName = "公务用车对公-订单强制结束行程")
    @ApiOperation(value = "公务用车对公-订单强制结束行程",notes = "作者：叶鹏鹏")
    public RestResponse<Boolean> orderForceFinishTrip(@RequestBody ClientPublicGovCarOrderOptReqDTO reqDTO) {
        OrderDataPermUtil.setDatePerm(reqDTO);
        ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
        reqDTO.setLoginUserMobile(clientLoginInfo.getBaseInfo().getMobile());
        String url = mrcarOrderRestLocator.getRestUrl("/client/publicGovCar/order/forceFinishTrip");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, Boolean.class);

    }


    @PostMapping("/client/publicGovCar/order/cancelTrip")
    @RequestFunction(functionName = "公务用车对公-订单取消行程")
    @ApiOperation(value = "公务用车对公-订单取消行程",notes = "作者：叶鹏鹏")
    public RestResponse<Boolean> orderCancelTrip(@RequestBody ClientPublicGovCarOrderOptReqDTO reqDTO) {
        OrderDataPermUtil.setDatePerm(reqDTO);
        ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
        reqDTO.setLoginUserMobile(clientLoginInfo.getBaseInfo().getMobile());
        String url = mrcarOrderRestLocator.getRestUrl("/client/publicGovCar/order/cancelTrip");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, Boolean.class);

    }

	@ApiImplicitParams({
            @ApiImplicitParam(name="orderNo",value="订单号",required = true),
    })
    @PostMapping(MrcarOrderRestMsgCenter.CLIENT_PUBLIC_GOV_CAR_EXPORT_DELIVERY_PDF)
    @RequestFunction(functionName = "下载派车单")
    @ApiOperation(value = "下载派车单",notes = "作者：及晓林")
    @SuppressWarnings("unchecked")
    public RestResponse<ExportPDFDTO> exportDeliveryPdf(@RequestParam("orderNo") String orderNo) {
        String restUrl =mrcarOrderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.PROVIDER_PUBLIC_GOV_CAR_EXPORT_DELIVERY_PDF);
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put("orderNo", orderNo);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paramMap, null, ExportPDFDTO.class);
    }

    @Resource
    private PublicGovCarOrderOptLogService publicGovCarOrderOptLogService;

    @PostMapping("/client/publicGovCar/order/listOptLog")
    @RequestFunction(functionName = "查询公务车订单操作日志列表")
    @ApiOperation(value = "查询公务车订单操作日志列表")
    public RestResponse<PageDTO<List<PublicGovCarOrderOperationLogDTO>>> listOptLog(@RequestBody ListPublicGovCarOptLogReqDTO reqDTO) {
        return publicGovCarOrderOptLogService.listOptLog(reqDTO);
    }

}
