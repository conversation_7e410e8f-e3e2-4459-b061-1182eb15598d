package com.izu.mrcar.controller.bi;

import cn.hutool.core.collection.CollUtil;
import com.izu.business.config.BusinessRestLocator;
import com.izu.business.config.errorcode.BusinessErrorCode;
import com.izu.business.consts.bi.LevelEnum;
import com.izu.business.consts.resturl.MrcarBusinessRestMsgCenter;
import com.izu.business.dto.bi.DeptBiAnalyseReqDTO;
import com.izu.business.dto.bi.ReportTableConfigReqDTO;
import com.izu.business.dto.bi.resp.DeptBiAnalyseRespDTO;
import com.izu.business.dto.bi.resp.TableChildColumnRespDTO;
import com.izu.business.dto.bi.resp.TableColumnRespDTO;
import com.izu.config.MrCarConfigRestLocator;
import com.izu.config.dto.BusinessCodeDTO;
import com.izu.config.dto.CompanyBussinessDTO;
import com.izu.config.dto.EnterpriseConfigDTO;
import com.izu.consts.ConfigURI;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.doc.JrdApiDoc;
import com.izu.framework.doc.JrdDoc;
import com.izu.framework.web.exception.RestErrorException;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.excel.Column;
import com.izu.framework.web.util.excel.ExSheet;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.controller.AbstractExcelDownloadController;
import com.izu.mrcar.providerController.bi.BIDeptAnalysisController;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.dto.CompanyDepartmentDTO;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.enums.perm.ClientDataPermTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@RestController
@Slf4j
@Api(tags = "客户端")
@JrdDoc(name = "BI统计分析-部门分析")
public class BIDeptAnalysisCustomerController extends AbstractExcelDownloadController {

    @PostMapping(MrcarBusinessRestMsgCenter.MRCAR_DEPT_STATISTICAL_ANALYSIS_LIST)
    @RequestFunction(functionName = "部门分析列表")
    @JrdApiDoc(simpleDesc = "部门分析列表", author = "hhd", resDataClass = DeptBiAnalyseRespDTO.class)
    @ApiOperation(value = "部门分析列表",notes = "作者：hhd")
    public RestResponse<List<DeptBiAnalyseRespDTO>> getDeptList(@RequestBody DeptBiAnalyseReqDTO param){
        //查询是否配置权限
        setClientDataPerm(param);
        if(param.getCompanyId()==null){
            param.setCompanyId(param.getLoginCompanyId());
        }
        Map<String, Object> configParaMap = new HashMap<>(1);
        configParaMap.put("companyId", param.getCompanyId());
        configParaMap.put("businessCode", 43);
        String configRestUrl = new MrCarConfigRestLocator().getRestUrl(ConfigURI.CONFIG_COMPANY_BUSINESS_GET_DTO_BY_BUSINESS_CODE);
        RestResponse companyConfigRestResponse = RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, configRestUrl, configParaMap, null, CompanyBussinessDTO.class);
        if (companyConfigRestResponse.isSuccess()) {
            if(Objects.isNull(companyConfigRestResponse.getData())){
                throw new RestErrorException("功能未开通",BusinessErrorCode.FUNCTION_IS_NOT_AVAILABLE);
            }
            CompanyBussinessDTO dto = (CompanyBussinessDTO) companyConfigRestResponse.getData();
            if(dto.getIsOpen()!=null && dto.getIsOpen()==0){
                throw new RestErrorException("功能未开通",BusinessErrorCode.FUNCTION_IS_NOT_AVAILABLE);
            }
        }
        String url = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.MRCAR_DEPT_STATISTICAL_ANALYSIS_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, DeptBiAnalyseRespDTO.class);
    }

    @PostMapping(value = MrcarBusinessRestMsgCenter.MRCAR_DEPT_STATISTICAL_ANALYSIS_EXPORT)
    @JrdApiDoc(simpleDesc = "部门分析列表导出", author = "hhd", resDataClass = Object.class)
    @RequestFunction(functionName = "部门分析列表导出")
    public RestResponse exportDeptList(@RequestBody DeptBiAnalyseReqDTO param, IzuEasyExcelSession izuEasyExcelSession,
                                          HttpServletRequest request, HttpServletResponse response) {
        //查询是否配置权限
        setClientDataPerm(param);
        if(param.getCompanyId()==null){
            param.setCompanyId(param.getLoginCompanyId());
        }
        Map<String, Object> configParaMap = new HashMap<>(1);
        configParaMap.put("companyId", param.getCompanyId());
        configParaMap.put("businessCode", 43);
        String configRestUrl = new MrCarConfigRestLocator().getRestUrl(ConfigURI.CONFIG_COMPANY_BUSINESS_GET_DTO_BY_BUSINESS_CODE);
        RestResponse companyConfigRestResponse = RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, configRestUrl, configParaMap, null, CompanyBussinessDTO.class);
        if (companyConfigRestResponse.isSuccess()) {
            if(Objects.isNull(companyConfigRestResponse.getData())){
                throw new RestErrorException("功能未开通", BusinessErrorCode.FUNCTION_IS_NOT_AVAILABLE);
            }
            CompanyBussinessDTO dto = (CompanyBussinessDTO) companyConfigRestResponse.getData();
            if(dto.getIsOpen()!=null && dto.getIsOpen()==0){
                throw new RestErrorException("功能未开通",BusinessErrorCode.FUNCTION_IS_NOT_AVAILABLE);
            }
        }
        String url = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.MRCAR_DEPT_STATISTICAL_ANALYSIS_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        RestResponse<List<DeptBiAnalyseRespDTO>> res = RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, DeptBiAnalyseRespDTO.class);
        if (!res.isSuccess()) {
            return null;
        }
        List<DeptBiAnalyseRespDTO> resultData = (List<DeptBiAnalyseRespDTO>) res.getData();
        //查询配置字段
        String configUrl = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.MRCAR_REPORT_TABLE_CONFIG_LIST);
        Map<String, Object> map = new HashMap<>();
        ReportTableConfigReqDTO configReqDTO = new ReportTableConfigReqDTO();
        configReqDTO.setLoginCompanyId(param.getLoginCompanyId());
        configReqDTO.setCompanyId(param.getCompanyId());
        configReqDTO.setReportScene(2);
        configReqDTO.setLoginUserId(param.getLoginUserId());
        map.put(BaseHttpClient.POSTBODY_MAP_KEY, configReqDTO);
        RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, configUrl, map, null, TableColumnRespDTO.class);
        if(restResponse==null || !restResponse.isSuccess() || restResponse.getData() ==null){
            return null;
        }
        List<TableColumnRespDTO> respDTOList = (List<TableColumnRespDTO>) restResponse.getData();
        listExport(resultData, request, response,respDTOList,param.getLevel());
        return null;
    }

    private void listExport(List<DeptBiAnalyseRespDTO> datas, HttpServletRequest request, HttpServletResponse response,List<TableColumnRespDTO> respDTOList,Byte level) {

        List<Column> columnModes = new ArrayList<>();
        respDTOList.forEach(respDTO -> {
            if (Objects.equals(respDTO.getProp(), "deptName")) {
                columnModes.add(new Column(respDTO.getProp(), respDTO.getLabel(), (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            } else {
                List<TableChildColumnRespDTO> children = respDTO.getChildren();
                List<String> values = respDTO.getValue();
                if(CollUtil.isEmpty(values)){
                    return;
                }
                children.forEach(x-> {
                    if(values.contains(x.getProp())){
                        if(Objects.equals(x.getProp(),"drivingTime") || Objects.equals(x.getProp(),"vehicleUseRateStr")  || Objects.equals(x.getProp(),"usageDurationRateStr") || Objects.equals(x.getProp(),"companyName")){
                            columnModes.add(new Column(x.getProp(), x.getLabel(), (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
                        } else {
                            columnModes.add(new Column(x.getProp(), x.getLabel(), (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0.00", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
                        }
                    }
                });
            }
        });
        columnModes.add(new Column("deptPath", "部门路径", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("superDept", "上级部门", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        List<Map<String, String>> rowDatas = new ArrayList<Map<String, String>>();
        List<String> keyList = new ArrayList<>();
        respDTOList.stream().map(TableColumnRespDTO::getProp).collect(Collectors.toList());
        respDTOList.forEach(respDTO -> {
            if (Objects.equals(respDTO.getProp(), "deptName")) {
                keyList.add(respDTO.getProp());
            } else {
                List<TableChildColumnRespDTO> children = respDTO.getChildren();
                List<String> values = respDTO.getValue();
                if(CollUtil.isEmpty(values)){
                    return;
                }
                children.forEach(x->{
                    if(values.contains(x.getProp())){
                        keyList.add(x.getProp());
                    }
                });
            }
        });
        for (DeptBiAnalyseRespDTO data : datas) {
            List<CompanyDepartmentDTO> departmentDTOList = queryDepartmentList(data.getCompanyId(), null);
            Map<Integer, CompanyDepartmentDTO> deptMap = departmentDTOList.stream().collect(Collectors.toMap(CompanyDepartmentDTO::getId, Function.identity(),(k1, k2)->k2));
            Map<String, String> rowdata = handleKeyData(keyList, data,deptMap);
            rowDatas.add(rowdata);
            preDFS(rowDatas,data,keyList,level,1,deptMap);
        }
        List<ExSheet> exSheetList = new ArrayList<>(1);
        exSheetList.add(new ExSheet(columnModes, rowDatas, "部门分析导出"));
        //---------------------------B: 根据上面创建好的Sheet，生成Excel
        String excelType = "xlsx";
        String fileNameCn = "部门分析导出_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + "." + excelType;
        String fileNameEn = "bi_dept_analysis_export_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + "." + excelType;
        this.exportExcel(request, response, fileNameCn, fileNameEn, exSheetList);
    }

    public static void preDFS(List<Map<String, String>> rowDatas, DeptBiAnalyseRespDTO respDTO,List<String> keyList,Byte level,int depth,Map<Integer, CompanyDepartmentDTO> deptMap) {
        if (CollUtil.isEmpty(respDTO.getChildList())) return;
        for(DeptBiAnalyseRespDTO data : respDTO.getChildList()){
            Map<String, String> rowdata = handleKeyData(keyList, data,deptMap);
            rowDatas.add(rowdata);
            if (level != null && (level == 0 || depth < level)) {
                preDFS(rowDatas, data, keyList, level, depth + 1,deptMap);
            }
        }
    }

    private static Map<String, String> handleKeyData(List<String> keyList, DeptBiAnalyseRespDTO data,Map<Integer, CompanyDepartmentDTO> deptMap){
        Map<String, String> rowdata = new HashMap<String, String>();
        if(keyList.contains("deptName")){
            rowdata.put("deptName", data.getDeptName());
        }
        if(keyList.contains("drivingMileage")){
            rowdata.put("drivingMileage", data.getDrivingMileage()!=null?data.getDrivingMileage().toPlainString():"");
        }
        if(keyList.contains("everyDayDrivingMileage")){
            rowdata.put("everyDayDrivingMileage", data.getEveryDayDrivingMileage()!=null?data.getEveryDayDrivingMileage().toPlainString():"");
        }
        if(keyList.contains("drivingTime")){
            rowdata.put("drivingTime", data.getDrivingTimeStr());
        }
        if(keyList.contains("vehicleUseRateStr")){
            rowdata.put("vehicleUseRateStr", data.getVehicleUseRateStr());
        }
        if(keyList.contains("usageDurationRateStr")){
            rowdata.put("usageDurationRateStr", data.getUsageDurationRateStr());
        }
        if(keyList.contains("tripCount")){
            rowdata.put("tripCount", data.getTripCount()!=null?data.getTripCount().toPlainString():"");
        }
        if(keyList.contains("singleTrip")){
            rowdata.put("singleTrip", data.getSingleTrip()!=null?data.getSingleTrip().toPlainString():"");
        }
        if(keyList.contains("totalFee")){
            rowdata.put("totalFee", data.getTotalFee()!=null?data.getTotalFee().toPlainString():"");
        }
        if(keyList.contains("hundredKMFee")){
            rowdata.put("hundredKMFee", data.getHundredKMFee()!=null?data.getHundredKMFee().toPlainString():"");
        }
        if(keyList.contains("oilFee")){
            rowdata.put("oilFee", data.getOilFee()!=null?data.getOilFee().toPlainString():"");
        }
        if(keyList.contains("oilQuantity")){
            rowdata.put("oilQuantity", data.getOilQuantity()!=null?data.getOilQuantity().toPlainString():"");
        }
        if(keyList.contains("chargeFee")){
            rowdata.put("chargeFee", data.getChargeFee()!=null?data.getChargeFee().toPlainString():"");
        }
        if(keyList.contains("chargeQuantity")){
            rowdata.put("chargeQuantity", data.getChargeQuantity()!=null?data.getChargeQuantity().toPlainString():"");
        }
        if(keyList.contains("maintainFee")){
            rowdata.put("maintainFee", data.getMaintainFee()!=null?data.getMaintainFee().toPlainString():"");
        }
        if(keyList.contains("insuranceFee")){
            rowdata.put("insuranceFee", data.getInsuranceFee()!=null?data.getInsuranceFee().toPlainString():"");
        }
        if(keyList.contains("tollFee")){
            rowdata.put("tollFee", data.getTollFee()!=null?data.getTollFee().toPlainString():"");
        }
        if(keyList.contains("carParkFee")){
            rowdata.put("carParkFee", data.getCarParkFee()!=null?data.getCarParkFee().toPlainString():"");
        }
        if(keyList.contains("carWashFee")){
            rowdata.put("carWashFee", data.getCarWashFee()!=null?data.getCarWashFee().toPlainString():"");
        }
        if(keyList.contains("annualInspectionFee")){
            rowdata.put("annualInspectionFee", data.getAnnualInspectionFee()!=null?data.getAnnualInspectionFee().toPlainString():"");
        }
        if(keyList.contains("accidentFee")){
            rowdata.put("accidentFee", data.getAccidentFee()!=null?data.getAccidentFee().toPlainString():"");
        }
        if(keyList.contains("violationFee")){
            rowdata.put("violationFee", data.getViolationFee()!=null?data.getViolationFee().toPlainString():"");
        }
        if(keyList.contains("otherFee")){
            rowdata.put("otherFee", data.getOtherFee()!= null?data.getOtherFee().toPlainString():"");
        }
        if(keyList.contains("vehicleTotalNum")){
            rowdata.put("vehicleTotalNum", data.getVehicleTotalNum()!=null?String.valueOf(data.getVehicleTotalNum()):"");
        }
        if(data.getLevel()!=null){
            LevelEnum byValue = LevelEnum.getByValue(data.getLevel());
            rowdata.put("level",byValue!=null?byValue.text():"");
        } else {
            rowdata.put("level","");
        }
        //赋值上级部门 赋值上级部门路径
        Map<String, String> map = getDepartmentNamesByParentId(data.getParentId(), deptMap);
        rowdata.put("deptPath", map.get("deptPath")==null? data.getDeptName() : map.get("deptPath")+"->"+ data.getDeptName());
        rowdata.put("superDept", map.get("superDept"));
        return rowdata;
    }

    public static void setClientDataPerm(DeptBiAnalyseReqDTO param){
        ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
        ClientDataPermTypeEnum dataPermTypeEnum = ClientDataPermTypeEnum.getByType(clientLoginInfo.obtainDataPerm().getDataPermType());
        param.setLoginUserId(clientLoginInfo.obtainBaseInfo().getStaffId());
        param.setDataPermType(dataPermTypeEnum.getType());
        param.setLoginCompanyId(clientLoginInfo.obtainBelongCompanyId());
        param.setLoginCompanyCode(clientLoginInfo.obtainBelongCompanyCode());
        param.setLoginUserName(clientLoginInfo.getBaseInfo().getStaffName());
        switch (dataPermTypeEnum){
            case ASSIGN_DEPT:// 指定部门
                Set<String> structIds= clientLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.ID).getDataCodeSet();
                param.setStructIds(structIds);
                break;
        }

    }

    public static  List<CompanyDepartmentDTO> queryDepartmentList(Integer companyId, Integer status){
        UserRestLocator locator = new UserRestLocator();
        Map<String, Object> params = new HashMap<>();
        params.put("status", status);
        params.put("companyId", companyId);
        String restUrl = locator.getRestUrl("/department/queryDepartmentList");
        RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, params, null, CompanyDepartmentDTO.class);
        if(restResponse==null || !restResponse.isSuccess() || restResponse.getData()==null){
            return Collections.emptyList();
        }
        return   (List<CompanyDepartmentDTO>)restResponse.getData();
    }

    // 根据第五层部门的父级ID查询一到四层的部门名称
    public static Map<String,String> getDepartmentNamesByParentId(Integer parentId,Map<Integer, CompanyDepartmentDTO> deptMap) {
        List<BIDeptAnalysisController.DeptNameDTO> deptNameDTOS = new ArrayList<>();
        String deptPath = "";
        Map<String,String> map = new HashMap<>();
        CompanyDepartmentDTO current = deptMap.get(parentId);
        if (current == null) {
            log.info("未找到对应的部门: {}", parentId);
            return Collections.emptyMap();
        }
        map.put("superDept",current.getDepartmentName());
        // 逐级向上查找，直到第一层
        while (current != null && current.getLevel() > 0) {
            BIDeptAnalysisController.DeptNameDTO deptNameDTO = new BIDeptAnalysisController.DeptNameDTO();
            deptNameDTO.setId(current.getLevel());
            deptNameDTO.setName(current.getDepartmentName());
            deptNameDTOS.add(deptNameDTO);
            if (current.getLevel() == 1) {
                break; // 已经到达第一层
            }
            current = deptMap.get(current.getParentId());
        }
        deptNameDTOS.sort(Comparator.comparing(BIDeptAnalysisController.DeptNameDTO::getId));
        for(BIDeptAnalysisController.DeptNameDTO deptNameDTO : deptNameDTOS){
            deptPath = StringUtils.isBlank(deptPath)
                    ? deptNameDTO.getName()
                    : deptPath + "->" + deptNameDTO.getName();
        }
        map.put("deptPath",deptPath);
        return map;
    }

    @Data
    public static class DeptNameDTO{
        private int id;
        private String name;
    }
}
