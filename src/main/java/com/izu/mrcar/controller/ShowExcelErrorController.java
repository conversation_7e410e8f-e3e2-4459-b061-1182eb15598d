package com.izu.mrcar.controller;

import com.alibaba.fastjson.JSON;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.excel.Column;
import com.izu.framework.web.util.excel.ExSheet;
import com.izu.mrcar.common.cache.SessionRedisSentinelCache;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * Excel文件上传错误信息的导出下载
 */
@Controller
public class ShowExcelErrorController extends AbstractExcelDownloadController{
    private static final Logger logger = LoggerFactory.getLogger(AbstractExcelUploadController.class);
    @Autowired
    private SessionRedisSentinelCache sessionRedisSentinelCache;

	@RequestMapping("/showexcelerror")
    @SuppressWarnings("deprecation")
    public RestResponse showexcelerror(String checkErrorId, Boolean debug, String from, String to, HttpServletRequest request, HttpServletResponse response) {
		//---------------------------A: 读取Excel文件上传的校验错误数据
    	AbstractExcelUploadController.ExcelUploadRowError[] errors = (AbstractExcelUploadController.ExcelUploadRowError[]) sessionRedisSentinelCache.get(AbstractExcelUploadController.EXCEL_CHECK_ERRORS_CACHE_KEY+checkErrorId);
		logger.info("缓存中取出的错误数据="+ JSON.toJSONString(errors) );
		List<AbstractExcelUploadController.ExcelUploadRowError> errorList = Arrays.asList(errors);
		logger.info("读取Excel文件上传的校验错误数="+ JSON.toJSONString(errorList) );
		
		/**DEBUG，生成模拟数据，用于验证导出EXCEL效果**/
		if(debug!=null && debug.booleanValue()==true ) {
			errorList = new ArrayList<AbstractExcelUploadController.ExcelUploadRowError>();
			errorList.add(new AbstractExcelUploadController.ExcelUploadRowError(1,"车辆编号不存在。" ) );
			errorList.add(new AbstractExcelUploadController.ExcelUploadRowError(2,"车辆发票金额不能为负数。" ) );
			errorList.add(new AbstractExcelUploadController.ExcelUploadRowError(3,"使用状态数据不合法。" ) );
		}
		
		//---------------------------B: 定义列与行
		/**步骤一：定义每列的数据模式**/
		final List<Column> columnModes = new ArrayList<Column>(2);
		columnModes.add( new Column("error_rownum","行号",(short)2800, CellStyle.ALIGN_CENTER,CellStyle.VERTICAL_CENTER,Cell.CELL_TYPE_STRING,"",HSSFColor.WHITE.index, HSSFColor.BLACK.index ) );
		columnModes.add( new Column("error_message","发生数据错误的原因、提示信息",Short.MAX_VALUE, CellStyle.ALIGN_LEFT,CellStyle.VERTICAL_TOP,Cell.CELL_TYPE_STRING,"",HSSFColor.LIGHT_TURQUOISE.index, HSSFColor.RED.index ) );
		/**步骤二：定义每行的数据**/
		final List<Map<String,String>> rowDatas            = new ArrayList<Map<String,String>>(errorList.size()+1);
		for( AbstractExcelUploadController.ExcelUploadRowError excelUploadRowError : errorList ) {
			Map<String,String> rowdata = new HashMap<String,String>( columnModes.size()*2 );
			rowdata.put("error_rownum", excelUploadRowError.getRowNum()==null? "未知行" : "第"+excelUploadRowError.getRowNum().intValue()+"行"  );
			rowdata.put("error_message", excelUploadRowError.getErrorMessage() );
			rowDatas.add(rowdata);
		}
		//---------------------------C: 生成Excel
        final List<ExSheet> exSheets = new ArrayList<ExSheet>(1);
        exSheets.add(new ExSheet( columnModes, rowDatas, "文件上传错误信息") );
        String excelType = "xlsx";
        String fileNameCn = "文件上传错误信息."+excelType;
        String fileNameEn = "FileUploadError."+excelType;
        this.exportExcel(request, response, fileNameCn, fileNameEn, exSheets);
		return null;
    }	
}