package com.izu.mrcar.controller.business;

import com.izu.business.dto.AttendStaticInfoDTO;
import com.izu.business.dto.AttendanceApplyDTO;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.LoginUser;
import com.izu.mrcar.shiro.session.WebSessionUtil;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/driverAttend")
@Api(tags = "司机考勤")
public class DriverAttendanceController {
    private static final Logger logger = LoggerFactory.getLogger(DriverAttendanceController.class);

    @Value("${mrcar-business-core.host.url}")
    private String BUSINESS_HOST_URL;

    @RequestMapping(value = "/queryAttendStatisticsByPage")
    @RequiresPermissions(value = "driverAttend")
    @RequestFunction(functionName = "司机考勤")
    public RestResponse queryAttendStatisticsByPage(@RequestBody Map<String,Object> paraMap, HttpServletRequest request){
        String restUrl = BUSINESS_HOST_URL + "driver/queryAttendanceStatisticsForPC";
        try {
//			获取企业id
            final ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
            int companyId = clientLoginInfo.obtainBelongCompanyId();
            paraMap.put("companyId", companyId);
            paraMap.put("permissions",String.join(",",clientLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet()));
            paraMap.put("dataPermType",clientLoginInfo.obtainSimpleDataPerm().getDataPermType());
            paraMap.put("loginUserId",clientLoginInfo.getBaseInfo().getStaffId());
            return RestClient.requestForPage(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Map.class);
        } catch(Exception e) {
            logger.error("detail Exception：" + e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }

    @RequestMapping(value = "/getAttendanceDetail")
    @RequestFunction(functionName = "考勤详情查询")
    public RestResponse getAttendanceDetail(@RequestBody Map<String,Object> paraMap, HttpServletRequest request){
        String restUrl = BUSINESS_HOST_URL + "driver/getAttendanceDetail";

        try {
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Object.class);
        } catch(Exception e) {
            logger.error("list Exception：" + e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }

    @RequestMapping(value = "/exportAttendance", method = RequestMethod.POST)
    @RequestFunction(functionName = "导出考勤信息")
    public void exportAttendance(String attendanceMonth,String driverName,String driverCell,Integer motorcadeId, HttpServletResponse response) throws Exception{
        final ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
        int companyId = clientLoginInfo.obtainBelongCompanyId();
        //获取企业名称
        Map<String,Object> paraMap = new HashMap<>();
        paraMap.put("companyId", companyId);
        paraMap.put("attendanceMonth", attendanceMonth);
        paraMap.put("driverName", driverName);
        paraMap.put("driverCell", driverCell);
        paraMap.put("motorcadeId", motorcadeId);
        paraMap.put("permissions",String.join(",",clientLoginInfo.obtainSimpleDataPerm().getDataCodeSet()));
        paraMap.put("dataPermType",clientLoginInfo.obtainSimpleDataPerm().getDataPermType());
        paraMap.put("loginUserId",clientLoginInfo.getBaseInfo().getStaffId());

        try {
            //查询该企业下的部门列表信息
            List<AttendStaticInfoDTO> attendStaticInfoDTOList = null;
            RestResponse restResponse= RestClient.requestForList(BaseHttpClient.HttpMethod.POST, BUSINESS_HOST_URL + "driver/getAttendanceList", paraMap, null, AttendStaticInfoDTO.class);
            if(restResponse.isSuccess()){
                attendStaticInfoDTOList = (List<AttendStaticInfoDTO>) restResponse.getData();
            }
            response.setContentType("application/vnd.ms-excel");
            response.setHeader("Content-disposition","attachment;filename*=utf-8''"  + URLEncoder.encode("考勤记录"+ ".xls", "UTF-8"));
            ServletOutputStream out = response.getOutputStream();
            BufferedOutputStream buffOut = null;

            XSSFWorkbook wb = new XSSFWorkbook();
            XSSFSheet sheet = wb.createSheet("考勤记录");
            XSSFRow row = sheet.createRow(0);
            row.createCell(0).setCellValue("司机姓名");
            row.createCell(1).setCellValue("司机电话");
            row.createCell(2).setCellValue("所属车队");
            row.createCell(3).setCellValue("考勤月份");
            row.createCell(4).setCellValue("应出勤次数（次）");
            row.createCell(5).setCellValue("出勤次数（次）");
            row.createCell(6).setCellValue("迟到（次）");
            row.createCell(7).setCellValue("早退（次）");
            row.createCell(8).setCellValue("缺卡（次）");
            row.createCell(9).setCellValue("旷工（天）");
            row.createCell(10).setCellValue("外勤（次）");
            if(attendStaticInfoDTOList == null || attendStaticInfoDTOList.size() == 0){
                //数据为空的情况暂时先这么处理
                row = sheet.createRow(1);
                row.createCell(0).setCellValue("暂无相关考勤记录");
            }else{
                int i = 0;
                for( i= 0;i<attendStaticInfoDTOList.size();i++){
                    AttendStaticInfoDTO staticInfoDTO = attendStaticInfoDTOList.get(i);
                    row = sheet.createRow(i+1);
                    row.createCell(0).setCellValue(staticInfoDTO.getDriverName() != null ? staticInfoDTO.getDriverName() : "");
                    row.createCell(1).setCellValue(staticInfoDTO.getDriverCell() !=null ? staticInfoDTO.getDriverCell() : "");
                    row.createCell(2).setCellValue(staticInfoDTO.getMotorcadeName() !=null ? staticInfoDTO.getMotorcadeName() : "");
                    row.createCell(3).setCellValue(staticInfoDTO.getAttendanceMonth() !=null ? staticInfoDTO.getAttendanceMonth() : "");
                    row.createCell(4).setCellValue(staticInfoDTO.getShouldAttendCount());
                    row.createCell(5).setCellValue(staticInfoDTO.getFactAttendCount());
                    row.createCell(6).setCellValue(staticInfoDTO.getLateCount());
                    row.createCell(7).setCellValue(staticInfoDTO.getEarlyLeaveCount());
                    row.createCell(8).setCellValue(staticInfoDTO.getMissSignCount());
                    row.createCell(9).setCellValue(staticInfoDTO.getAbsentCount());
                    row.createCell(10).setCellValue(staticInfoDTO.getOutSignCount());
                }
            }
            buffOut = new BufferedOutputStream(out);
            wb.write(buffOut);
        } catch (Exception e) {
            logger.error("【考勤管理】导出考勤信息 error", e);
            throw e;
        }
    }

    @GetMapping(value = "/getAttendanceInfo")
    @RequestFunction(functionName = "获取打卡信息")
    @ApiOperation(value = "获取打卡信息")
    @ApiImplicitParam(name = "seqNo", value = "考勤编号", required = true)
    public RestResponse<AttendanceApplyDTO> getAttendanceDetail(String seqNo){
        String restUrl = BUSINESS_HOST_URL + "/attendance/getAttendanceInfo";
        try {
            Map<String,Object> paraMap = new HashMap<>();
            paraMap.put("seqNo", seqNo);
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Map.class);
        } catch(Exception e) {
            logger.error("list Exception：" + e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }

}
