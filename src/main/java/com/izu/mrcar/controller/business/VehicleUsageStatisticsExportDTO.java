package com.izu.mrcar.controller.business;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;

@ExcelTarget("applyStatementCustomerExport")
@Data
public class VehicleUsageStatisticsExportDTO implements Serializable {

    @Excel(name = "车牌号", needMerge = true, orderNum = "1", width = 25)
    private String vehicleLicense;

    @Excel(name = "车架号", needMerge = true, orderNum = "2", width = 25)
    private String vehicleVin;

    @Excel(name = "行驶总里程（所选时间内）", needMerge = true, orderNum = "3", width = 25)
    private BigDecimal totalMileage;

    @Excel(name = "历史总里程（出厂至今）", needMerge = true, orderNum = "4", width = 25)
    private BigDecimal hisTotalMileage;

    @Excel(name = "违章总数量", needMerge = true, orderNum = "5", width = 25)
    private Integer violationCount;

    @Excel(name = "维保总次数", needMerge = true, orderNum = "6", width = 25)
    private Integer maintainCount;
}
