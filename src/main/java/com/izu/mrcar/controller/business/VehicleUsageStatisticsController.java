package com.izu.mrcar.controller.business;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.izu.business.dto.StatisticsVehicleUsageDTO;
import com.izu.business.dto.input.VehicleUsageQueryDto;
import com.izu.config.dto.FieldConfigContentDTO;
import com.izu.config.dto.FieldConfigDetailReq;
import com.izu.config.dto.FieldConfigDetailRespDTO;
import com.izu.consts.ConfigURI;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.excel.Column;
import com.izu.framework.web.util.excel.ExSheet;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.config.RestUrlConfig;
import com.izu.mrcar.controller.AbstractExcelDownloadController;
import com.izu.mrcar.utils.DataPermUtil;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.enums.perm.ClientDataPermTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
* @Description: 车辆使用状况
* @author: hxc
* @Date: 2021/7/15
**/
@RestController
@RequestMapping("/statistics")
@Api(tags = "车辆行程统计")
@Slf4j
public class VehicleUsageStatisticsController extends AbstractExcelDownloadController {

    @Value("${mrcar-business-core.host.url}")
    private String BUSINESS_HOST_URL;

    @PostMapping("/vehicleUsage")
    @RequestFunction(functionName = "车辆使用详情")
    @ApiOperation(value = "车辆使用详情")
    public RestResponse<PageDTO<StatisticsVehicleUsageDTO>> vehicleUsage(@RequestBody VehicleUsageQueryDto param) {

        String restUrl = BUSINESS_HOST_URL + "/statistics/vehicleUsage";
        Map<String,Object> params=new HashMap<>();
        DataPermUtil.putDataPerm(param);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        List<Byte> dataPermTypes = Lists.newArrayList(ClientDataPermTypeEnum.SELF_COMPANY.getType(),ClientDataPermTypeEnum.ASSIGN_DEPT.getType());
        if (!dataPermTypes.contains(param.getDataPermType())) {
            // 非本企业和指定部门的数据权限都为空
            return RestResponse.success(new PageDTO<>());
        }
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, params, null, Map.class);
    }

    @PostMapping("/vehicleUsage/export")
    @RequestFunction(functionName = "车辆使用详情导出")
    @ApiOperation(value = "车辆使用详情导出")
    public void vehicleUsage(@RequestBody VehicleUsageQueryDto param, HttpServletRequest request, HttpServletResponse response) {
        try {
            List<StatisticsVehicleUsageDTO> resultData;
            Map<String,Object> params=new HashMap<>();
            DataPermUtil.putDataPerm(param);
            params.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
            List<Byte> dataPermTypes = Lists.newArrayList(ClientDataPermTypeEnum.SELF_COMPANY.getType(),ClientDataPermTypeEnum.ASSIGN_DEPT.getType(),ClientDataPermTypeEnum.ASSIGN_CITY.getType());
            if (!dataPermTypes.contains(param.getDataPermType())) {
                // 非本企业的数据权限都为空
                resultData = new ArrayList<>();
            } else {
                Integer pageSize = 5000;
                Integer maxSize = 20000;
                param.setPage(1);
                param.setPageSize(pageSize);
                String restUrl = BUSINESS_HOST_URL + "/statistics/vehicleUsage";

                RestResponse result = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, params, null, StatisticsVehicleUsageDTO.class);
                if (!result.isSuccess()) {
                    return;
                }

                PageDTO pageDTO = (PageDTO) result.getData();
                resultData = pageDTO.getResult();
                if (pageDTO.getTotal() > pageSize) {
                    // 一次拿pageSize条
                    int totalPage = (int) ((Math.min(pageDTO.getTotal(), maxSize) + pageSize - 1) / pageSize);
                    for (int i = 2; i <= totalPage; i++) {
                        param.setPage(i);
                        result = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, params, null, StatisticsVehicleUsageDTO.class);
                        pageDTO = (PageDTO) result.getData();
                        resultData.addAll(pageDTO.getResult());
                    }
                }
            }
            exportExcel(resultData, request, response);
        } catch (Exception e) {
            log.error("导出车辆使用状况异常", e);
        }
    }

    private void exportExcel(List<StatisticsVehicleUsageDTO> datas, HttpServletRequest request ,HttpServletResponse response) {
        //查询列表展示配置
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        String fieldConfigUrl = RestUrlConfig.getConfig().getConfigCoreUrl() + ConfigURI.FIELD_CONFIG_DETAIL;
        FieldConfigDetailReq param = new FieldConfigDetailReq();
        param.setId(1);
        param.setCompanyId(loginBaseInfo.obtainBelongCompanyId());
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        RestResponse requestInside = RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, fieldConfigUrl, paramMap, null, FieldConfigDetailRespDTO.class);
        List<Column> columnModes = new ArrayList<>();

        if (requestInside.isSuccess()) {
            FieldConfigDetailRespDTO fieldConfigDetail = (FieldConfigDetailRespDTO) requestInside.getData();
            List<String> showFieldNameList = fieldConfigDetail.getContentList().stream().filter(fieldConfig -> Objects.equals(fieldConfig.getIsShow(), 1)).map(FieldConfigContentDTO::getFieldName).collect(Collectors.toList());
            if (showFieldNameList.contains("车牌号")) {
                columnModes.add(new Column("vehicleLicense", "车牌号", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            }
            if (showFieldNameList.contains("车架号")) {
                columnModes.add(new Column("vehicleVin", "车架号", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            }
            if (showFieldNameList.contains("车型")) {
                columnModes.add(new Column("vehicleModel", "车型", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            }
            if (showFieldNameList.contains("所在城市")) {
                columnModes.add(new Column("belongCityName", "所在城市", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            }
            if (showFieldNameList.contains("所属部门")) {
                columnModes.add(new Column("structName", "所属部门", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            }
            if (showFieldNameList.contains("行驶时长(时:分)")) {
                columnModes.add(new Column("dayTravelDurationString", "行驶时长(时:分)", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            }
            if (showFieldNameList.contains("仪表盘里程数(km)")) {
                columnModes.add(new Column("hisTotalMileage", "仪表盘里程数(km)", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            }
            if (showFieldNameList.contains("仪表盘行驶里程(km)")) {
                columnModes.add(new Column("dayTotalMileage", "仪表盘行驶里程(km)", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            }
            if (showFieldNameList.contains("行程明细汇总里程(km)")) {
                columnModes.add(new Column("totalMileage", "行程明细汇总里程(km)", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            }
            if (showFieldNameList.contains("轨迹总里程(km)")) {
                columnModes.add(new Column("dayTravelMileage", "轨迹总里程(km)", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            }
            if (showFieldNameList.contains("违章总数量")) {
                columnModes.add(new Column("violationCount", "违章总数量", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            }
            if (showFieldNameList.contains("维保总次数")) {
                columnModes.add(new Column("maintainCount", "维保总次数", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            }
            if (showFieldNameList.contains("所属企业")) {
                columnModes.add(new Column("companyName", "所属企业", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            }
        }
        final List<Map<String, String>> rowDatas = new ArrayList<Map<String, String>>(datas.size() + 1);
        for (StatisticsVehicleUsageDTO data : datas) {
            final Map<String, String> rowdata = new HashMap<String, String>(columnModes.size() * 2);
            rowdata.put("vehicleLicense", data.getVehicleLicense());
            rowdata.put("vehicleVin", data.getVehicleVin());
            rowdata.put("vehicleModel", data.getVehicleModel());
            rowdata.put("belongCityName", data.getBelongCityName());
            rowdata.put("structName", data.getStructName());
            rowdata.put("dayTravelDurationString",data.getDayTravelDurationString());
            rowdata.put("totalMileage", data.getTotalMileage().toString());
            rowdata.put("hisTotalMileage", data.getHisTotalMileage().toString());
            rowdata.put("dayTotalMileage", data.getDayTotalMileage().toString());
            rowdata.put("dayTravelMileage", data.getDayTravelMileage().toString());
            rowdata.put("violationCount", data.getViolationCount().toString());
            rowdata.put("maintainCount", data.getMaintainCount().toString());
            rowdata.put("companyName", data.getCompanyName());
            rowDatas.add(rowdata);
        }
        List<ExSheet> exSheetList = new ArrayList<>(1);
        exSheetList.add(new ExSheet(columnModes, rowDatas, "车辆使用状况"));
        //---------------------------B: 根据上面创建好的Sheet，生成Excel
        String excelType = "xlsx";
        String fileNameCn = "车辆使用状况_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + "." + excelType;
        String fileNameEn = "vehicle_usage_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + "." + excelType;
        this.exportExcel(request, response, fileNameCn, fileNameEn, exSheetList);
    }
}
