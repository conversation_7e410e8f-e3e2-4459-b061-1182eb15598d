package com.izu.mrcar.controller.business;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.CarInfoDTO;
import com.izu.asset.dto.CarLocationFocusQueryDTO;
import com.izu.carasset.util.StringUtils;
import com.izu.config.dto.FieldConfigContentDTO;
import com.izu.config.dto.FieldConfigDetailReq;
import com.izu.config.dto.FieldConfigDetailRespDTO;
import com.izu.consts.ConfigURI;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.excel.dto.IzuMailSession;
import com.izu.excel.mail.AbstractIzuMailExcel;
import com.izu.excel.util.MemoryUnit;
import com.izu.excel.util.ZipUtils;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.lbs.common.LbsRestLocator;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.config.RestUrlConfig;
import com.izu.mrcar.utils.DataPermUtil;
import com.izu.mrcar.utils.DateUtil;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.company.CompanyDTO;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.enums.perm.ClientDataPermTypeEnum;
import com.izu.user.restApi.BusinessUserApi;
import com.xxl.job.core.util.EnvUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import com.izu.framework.web.util.excel.Column;
import com.izu.framework.web.util.excel.ExSheet;
import com.izu.mrcar.controller.AbstractExcelDownloadController;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.text.MessageFormat;
import java.util.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Description: 行驶里程明细
 * @author: hxc
 * @Date: 2021/12/17
 **/
@RestController
@RequestMapping("/travelInfo")
@Slf4j
@Api(tags = "车辆行程统计")
public class VehicleTravelController extends AbstractExcelDownloadController implements AbstractIzuMailExcel<LbsTravelSearchDTO, VehicleTravelExportDTO> {

    public static final String FILE_PATH = "/data/logs/mrcar/tmp";

    @PostMapping("/list")
    @RequestFunction(functionName = "车辆行程明细-查询列表")
    @ApiOperation(value = "车辆行程明细")
    public RestResponse<PageDTO<VehicleTravelExportDTO>> getTravelInfoList(@RequestBody LbsTravelSearchDTO lbsTravelSearchDTO) {

        // 初始化数据权限
        List<CarInfoDTO> cars = getCarScope(lbsTravelSearchDTO.getCompanyIds());
        if (CollectionUtil.isEmpty(cars)) {
            return RestResponse.success(new PageDTO(lbsTravelSearchDTO.getPageNo(), lbsTravelSearchDTO.getPageSize(), 0, null));
        }
        lbsTravelSearchDTO.setContainMrcar(true);
        Map<String, Object> params = JSON.parseObject(JSON.toJSONString(lbsTravelSearchDTO)).getInnerMap();
        List<String> vehicleVinList = cars.stream().map(p -> p.getVehicleVin()).collect(Collectors.toList());
        Map<String, Integer> companyIds = cars.stream().collect(Collectors.toMap(CarInfoDTO::getVehicleVin, CarInfoDTO::getCompanyId, (e1, e2) -> e1));
        Map<Integer, String> companyNames = new HashMap<>();
        params.put("vehicleVinList", vehicleVinList);
        String restUrl = new LbsRestLocator().getRestUrl("/lbs/travelInfo/v2/list.json");
        String env = EnvUtil.getEnvProp().getProperty("environment.name", "online");
        if (env.equals("dev") || env.equals("pre")) {
            restUrl = "https://prd-inside-lbs.izuche.com/lbs-core".concat("/lbs/travelInfo/v2/list.json");
        }
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, params, null, VehicleTravelExportDTO.class);
        if (restResponse.isSuccess()) {
            PageDTO<VehicleTravelExportDTO> pageDTO = (PageDTO) restResponse.getData();
            List<VehicleTravelExportDTO> list = pageDTO.getResult();
            list.stream().forEach(vehicleTravelExportDTO -> {
                String avgOil = vehicleTravelExportDTO.getAvgOil();
                String useOil = "-";
                if (StringUtils.isNotBlank(avgOil) && !avgOil.equals("-")) {
                    BigDecimal avgOilB = new BigDecimal(avgOil);
                    BigDecimal useOilB = avgOilB.divide(new BigDecimal(100)).multiply(vehicleTravelExportDTO.getTravelMile()).setScale(2, BigDecimal.ROUND_HALF_UP);
                    useOil = useOilB.toString();
                }
                vehicleTravelExportDTO.setUseOil(useOil);
                Integer companyId = companyIds.get(vehicleTravelExportDTO.getVehicleVin());
                if (companyId != null) {
                    if (StringUtils.isNotBlank(companyNames.get(companyId))) {
                        vehicleTravelExportDTO.setCompanyName(companyNames.get(companyId));
                    } else {
                        CompanyDTO companyDTO = BusinessUserApi.getCompanyBycompanyId(companyId);
                        vehicleTravelExportDTO.setCompanyName(companyDTO.getCompanyName());
                        companyNames.put(companyId, vehicleTravelExportDTO.getCompanyName());
                    }
                }
            });
        }
        return restResponse;
    }

    @RequestMapping(value = "/export", method = RequestMethod.POST)
    @RequestFunction(functionName = "车辆行程明细-导出")
    public void exportTravelInfo(@RequestBody LbsTravelSearchDTO lbsTravelSearchDTO, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response) {
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        String userName = loginBaseInfo.obtainBaseInfo().getStaffName();
        lbsTravelSearchDTO.setPageNo(izuEasyExcelSession.getPageNo());
        izuEasyExcelSession.setUserName(userName);
        ExSheet export = export(lbsTravelSearchDTO, request, response);
        if (Objects.nonNull(export)) {
            exportExcel(request, response, "车辆行程明细.xlsx", "车辆行程明细.xlsx", Collections.singletonList(export));
        }
    }


    @RequestMapping(value = "/export/sendMail", method = RequestMethod.POST)
    @ApiOperation(value = "车辆行程明细-导出邮箱")
    @RequestFunction(functionName = "车辆行程明细-导出邮箱")
    public RestResponse sendEmail(@RequestBody LbsTravelSearchDTO lbsTravelSearchDTO, HttpServletRequest request, HttpServletResponse response) {

        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        lbsTravelSearchDTO.setUserName(loginBaseInfo.obtainBaseInfo().getStaffName());
        if (StringUtils.isBlank(lbsTravelSearchDTO.getEmail())) {
            return RestResponse.create(9999, "邮箱号不能为空", false, null);
        }
        List<CarInfoDTO> cars = getCarScope(lbsTravelSearchDTO.getCompanyIds());
        if (CollectionUtil.isEmpty(cars)) {
            return RestResponse.create(9999, "暂无数据，无需导出", false, null);
        }

        new Thread(new Runnable() {
            @Override
            public void run() {
                VehicleTravelController.this.sendEmails(lbsTravelSearchDTO, request, response, loginBaseInfo);
            }
        }).start();
        return RestResponse.create(0, "提交成功！请稍后查收电子邮件。（大约耗时3分钟！）", true, 0);
    }

    private List<CarInfoDTO> getCarScope() {
        return getCarScope(null);
    }

    private List<CarInfoDTO> getCarScope(List<Integer> companyIds) {

        List<CarInfoDTO> carInfoDTOS = new ArrayList<>();
        CarLocationFocusQueryDTO dto = new CarLocationFocusQueryDTO();
        dto.setPageNo(1);
        dto.setPageSize(Integer.MAX_VALUE);
        DataPermUtil.putDataPerm(dto);
        dto.setCompanyIds(companyIds);
        Map<String, Object> paraMap = new HashMap<>(1);
        paraMap.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        List<Byte> dataPermTypeList = Lists.newArrayList(ClientDataPermTypeEnum.SELF_COMPANY.getType(), ClientDataPermTypeEnum.ASSIGN_DEPT.getType());
        if (!dataPermTypeList.contains(dto.getDataPermType())) {
            // 非本企业的数据权限都为空 非指定部门 权限为空
            log.info("getCarScope is null");
            return null;
        }
        String assetUrl = new MrCarAssetRestLocator().getRestUrl("/car/selectVehicleVinListByCompanyId");
        RestResponse response = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, assetUrl, paraMap, null, CarInfoDTO.class);
        if (null != response && response.isSuccess()) {
            if (null != response.getData()) {
                PageDTO carInfoPageDto = (PageDTO) response.getData();
                carInfoDTOS = carInfoPageDto.getResult();
            }
        }
        return carInfoDTOS;
    }


    private void sendEmails(LbsTravelSearchDTO lbsTravelSearchDTO, HttpServletRequest request, HttpServletResponse response, LoginBaseInfo loginBaseInfo) {
        ExSheet export = export(lbsTravelSearchDTO, request, response);
        if (Objects.nonNull(export)) {
            String fileName = getFileName(request, response, "车辆行程明细.xlsx", "车辆行程明细.xlsx", Collections.singletonList(export));
            IzuMailSession izuMailSession = new IzuMailSession();
            izuMailSession.setUserName(lbsTravelSearchDTO.getUserName());
            izuMailSession.setMailOpName("车辆行程明细");
            izuMailSession.setMailSubject("【车辆行程明细】");
            izuMailSession.setToMail(lbsTravelSearchDTO.getEmail());
            izuMailSession.setBccMail("<EMAIL>");
            if (izuMailSession == null) throw new NullPointerException("邮件信息不能为空!");
            File tmpFile = new File(fileName);
            File zipFile = null;
            try {
                izuMailSession.setStartDate(new Date());

                //判断文件大小，网易邮箱限制附件<100M
                long fileSizeMB = MemoryUnit.MB.convert(tmpFile.length(), MemoryUnit.B);
                logger.info("当前文件大小:{}MB", fileSizeMB);
                if (fileSizeMB > MAIL_MAX_SIZE) {
                    logger.info("当前文件大于:{}MB进行压缩", MAIL_MAX_SIZE);
                    String zipFileName = tmpFile.getPath().substring(0, tmpFile.getPath().lastIndexOf(File.separator))
                            .concat(File.separator).concat(tmpFile.getName().split("\\.")[0]).concat(".zip");
                    zipFile = new File(zipFileName);
                    ZipUtils.toZip(Arrays.asList(tmpFile), new FileOutputStream(zipFile));
                    logger.info("压缩后文件大小:{}MB", MemoryUnit.MB.convert(zipFile.length(), MemoryUnit.B));
                }
                //格式化加上导出总条数,标题请使用占位符，例如 导出车辆信息共：{0},条
                String subject = MessageFormat.format(izuMailSession.getMailSubject(),
                        THREAD_LOCAL.get() == null ? 0 : THREAD_LOCAL.get());
                izuMailSession.setMailSubject(subject);
                izuMailSession.setFileName(tmpFile.getName());
                this.sendMail(izuMailSession, zipFile == null ? tmpFile : zipFile);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                if (tmpFile != null && tmpFile.exists()) {
                    tmpFile.delete();
                }
                if (zipFile != null && zipFile.exists()) {
                    zipFile.delete();
                }
                THREAD_LOCAL.remove();
            }
        }
    }


    public ExSheet export(LbsTravelSearchDTO lbsTravelSearchDTO, HttpServletRequest request, HttpServletResponse response) {
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        lbsTravelSearchDTO.setPageNo(1);
        lbsTravelSearchDTO.setPageSize(10000);
        // 初始化数据权限
        List<CarInfoDTO> cars = getCarScope(lbsTravelSearchDTO.getCompanyIds());
        if (CollectionUtil.isEmpty(cars)) {
            return null;
        }
        lbsTravelSearchDTO.setContainMrcar(true);
        String restUrl = new LbsRestLocator().getRestUrl("/lbs/travelInfo/v2/list.json");
        Map<String, Object> params = JSON.parseObject(JSON.toJSONString(lbsTravelSearchDTO)).getInnerMap();
        List<String> vehicleVinList = cars.stream().map(p -> p.getVehicleVin()).collect(Collectors.toList());
        Map<String, Integer> companyIds = cars.stream().collect(Collectors.toMap(CarInfoDTO::getVehicleVin, CarInfoDTO::getCompanyId, (e1, e2) -> e1));
        Map<Integer, String> companyNames = new HashMap<>();
        params.put("vehicleVinList", vehicleVinList);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, params, null, VehicleTravelExportDTO.class);
        if (restResponse.isSuccess()) {
            PageDTO<VehicleTravelExportDTO> pageDTO = (PageDTO) restResponse.getData();
            List<VehicleTravelExportDTO> list = pageDTO.getResult();
            list.stream().forEach(vehicleTravelExportDTO -> {
                String avgOil = vehicleTravelExportDTO.getAvgOil();
                String useOil = "-";
                if (StringUtils.isNotBlank(avgOil) && !avgOil.equals("-")) {
                    BigDecimal avgOilB = new BigDecimal(avgOil);
                    BigDecimal useOilB = avgOilB.divide(new BigDecimal(100)).multiply(vehicleTravelExportDTO.getTravelMile()).setScale(2, BigDecimal.ROUND_HALF_UP);
                    useOil = useOilB.toString();
                }
                vehicleTravelExportDTO.setUseOil(useOil);
                Integer companyId = companyIds.get(vehicleTravelExportDTO.getVehicleVin());
                if (companyId != null) {
                    if (StringUtils.isNotBlank(companyNames.get(companyId))) {
                        vehicleTravelExportDTO.setCompanyName(companyNames.get(companyId));
                    } else {
                        CompanyDTO companyDTO = BusinessUserApi.getCompanyBycompanyId(companyId);
                        vehicleTravelExportDTO.setCompanyName(companyDTO.getCompanyName());
                        companyNames.put(companyId, vehicleTravelExportDTO.getCompanyName());
                    }
                }
            });

            //查询列表展示配置
            String fieldConfigUrl = RestUrlConfig.getConfig().getConfigCoreUrl() + ConfigURI.FIELD_CONFIG_DETAIL;
            FieldConfigDetailReq param = new FieldConfigDetailReq();
            param.setId(2);
            param.setCompanyId(loginBaseInfo.obtainBelongCompanyId());
            Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
            paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
            RestResponse requestInside = RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, fieldConfigUrl, paramMap, null, FieldConfigDetailRespDTO.class);
            if (requestInside.isSuccess()) {
                FieldConfigDetailRespDTO fieldConfigDetail = (FieldConfigDetailRespDTO) requestInside.getData();
                List<String> showFieldNameList = fieldConfigDetail.getContentList().stream().filter(fieldConfig -> Objects.equals(fieldConfig.getIsShow(), 1)).map(FieldConfigContentDTO::getFieldName).collect(Collectors.toList());
                List<Column> columnModes = new ArrayList<>();

                if (showFieldNameList.contains("行程编号")) {
                    columnModes.add(new Column("travelSerialNo", "行程编号", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
                }
                if (showFieldNameList.contains("车牌号")) {
                    columnModes.add(new Column("vehicleLicense", "车牌号", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
                }
                if (showFieldNameList.contains("车架号")) {
                    columnModes.add(new Column("vehicleVin", "车架号", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
                }
                if (showFieldNameList.contains("起点城市")) {
                    columnModes.add(new Column("startCity", "起点城市", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
                }
                if (showFieldNameList.contains("终点城市")) {
                    columnModes.add(new Column("endCity", "终点城市", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
                }
                if (showFieldNameList.contains("起点位置")) {
                    columnModes.add(new Column("startPosition", "起点位置", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
                }
                if (showFieldNameList.contains("终点位置")) {
                    columnModes.add(new Column("endPosition", "终点位置", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
                }
                if (showFieldNameList.contains("起点时间")) {
                    columnModes.add(new Column("startTime", "起点时间", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
                }
                if (showFieldNameList.contains("终点时间")) {
                    columnModes.add(new Column("endTime", "终点时间", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
                }
                if (showFieldNameList.contains("所属企业")) {
                    columnModes.add(new Column("companyName", "所属企业", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
                }
                if (showFieldNameList.contains("行驶时长(时:分)")) {
                    columnModes.add(new Column("travelDurationName", "行驶时长(时:分)", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
                }
                if (showFieldNameList.contains("行驶里程(公里)")) {
                    columnModes.add(new Column("travelMileBak", "行驶里程(公里)", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
                }
                if (showFieldNameList.contains("设备类型")) {
                    columnModes.add(new Column("deviceTypeName", "设备类型", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
                }
                if (showFieldNameList.contains("SIM卡号")) {
                    columnModes.add(new Column("simNo", "SIM卡号", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
                }
                if (showFieldNameList.contains("百公里油耗(升/100公里)")) {
                    columnModes.add(new Column("avgOil", "百公里油耗(升/100公里)", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
                }
                if (showFieldNameList.contains("油耗总数(L)")) {
                    columnModes.add(new Column("useOil", "油耗总数(L)", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
                }
                ExSheet exSheet;
                List<Map<String, String>> collect = list.stream().map(vehicleTravelExport -> {
                    HashMap<String, String> map = new HashMap<>();
                    map.put("a", "b");
                    map.put("vehicleLicense", vehicleTravelExport.getVehicleLicense());
                    map.put("travelSerialNo", vehicleTravelExport.getTravelSerialNo());
                    map.put("vehicleVin", vehicleTravelExport.getVehicleVin());
                    map.put("startCity", vehicleTravelExport.getStartCity());
                    map.put("endCity", vehicleTravelExport.getEndCity());
                    map.put("startPosition", vehicleTravelExport.getStartPosition());
                    map.put("endPosition", vehicleTravelExport.getEndPosition());
                    map.put("startTime", DateUtil.date2String(vehicleTravelExport.getStartTime(),DateUtil.TIME_FORMAT));
                    map.put("endTime", DateUtil.date2String(vehicleTravelExport.getEndTime(), DateUtil.TIME_FORMAT));
                    map.put("companyName", vehicleTravelExport.getCompanyName());
                    map.put("travelDurationName", vehicleTravelExport.getTravelDurationName());
                    map.put("travelMileBak", vehicleTravelExport.getTravelMileBak() + "");
                    map.put("deviceTypeName", vehicleTravelExport.getDeviceTypeName());
                    map.put("simNo", vehicleTravelExport.getSimNo());
                    map.put("avgOil", vehicleTravelExport.getAvgOil());
                    map.put("useOil", vehicleTravelExport.getUseOil());
                    return map;
                }).collect(Collectors.toList());
                exSheet = new ExSheet(columnModes, collect, "车辆行程明细");
                return exSheet;
            }
        }
        return null;
    }

    @Override
    public PageDTO getDataByPage(Object req, int pageNo, int pageSize) {
        return null;
    }
}
