package com.izu.mrcar.controller.business;

import com.izu.excel.dto.IzuMailDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 查询行程信息请求DTO
 * <AUTHOR> ON 2021/7/27.
 */
@Data
@ApiModel("车辆行程统计-请求参数")
public class LbsTravelSearchDTO extends IzuMailDTO {

    @NotNull(message = "pageNo不能为空")
    private Integer pageNo;
    @NotNull(message = "pageSize不能为空")
    private Integer pageSize;
    @ApiModelProperty(value = "车牌号")
    private String vehicleLicense;
    @ApiModelProperty(value = "车架号")
    private String vehicleVin;
    @ApiModelProperty(value = "车辆编码",hidden = true)
    private String vehicleSerialNo;
    @ApiModelProperty(value = "账套城市",hidden = true)
    private String accountCityCode;
    @ApiModelProperty(value = "机动车使用人编码",hidden = true)
    private List<String> operateBussCode;
    @ApiModelProperty(value = "租赁用途",hidden = true)
    private String leasePurpose;
    @ApiModelProperty(value = "起点开始时间",example = "2022-01-01")
    private String startPointStartDate;
    @ApiModelProperty(value = "起点结束时间",example = "2022-01-02")
    private String startPointEndDate;
    @ApiModelProperty(value = "终点开始时间",example = "2022-01-02")
    private String endPointStartDate;
    @ApiModelProperty(value = "终点结束时间",example = "2022-01-03")
    private String endPointEndDate;
    @ApiModelProperty(value = "SIM卡号")
    private String simNo;
    @ApiModelProperty(value = "设备类型")
    private Integer deviceType;

    private Boolean containMrcar;
    /**
     * 行程编号
     */
    @ApiModelProperty(value = "行程编号")
    private String travelSerialNo;
    /**
     * 途径位置
     */
    @ApiModelProperty(value = "途径位置")
    private String routeLocation;
    @ApiModelProperty(value = "车辆编号集合")
    private List<String> vehicleSerialNoList;
    @ApiModelProperty(value = "车架号集合集合")
    private List<String> vehicleVinList;
    @ApiModelProperty(value = "搜索条件-所属企业id")
    private List<Integer> companyIds;
}
