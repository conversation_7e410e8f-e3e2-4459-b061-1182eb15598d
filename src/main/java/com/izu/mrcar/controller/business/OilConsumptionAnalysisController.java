package com.izu.mrcar.controller.business;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.google.common.collect.Sets;
import com.izu.business.config.BusinessRestLocator;
import com.izu.business.dto.fuel.input.OilConsumptionAnalysisReqDTO;
import com.izu.business.dto.fuel.output.*;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.DataPerm;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.enums.perm.ClientDataPermTypeEnum;
import com.izu.user.enums.role.SystemTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import static com.izu.framework.web.rest.client.BaseHttpClient.POSTBODY_MAP_KEY;

/**
 * 油耗分析
 *
 * <AUTHOR>
 */
@RestController
@Slf4j
public class OilConsumptionAnalysisController {


    /**
     * 获取油耗分析分页
     */
    @PostMapping("/oil/consumption/getOilByPageParam")
    public RestResponse<PageDTO<OilConsumptionAnalysisResDTO>> getOilByPageParam(@RequestBody OilConsumptionAnalysisReqDTO reqDTO) {
        String url = new BusinessRestLocator().getRestUrl("/oil/consumption/getOilByPageParam");
        //String url = "http://localhost:8080/oil/consumption/getOilByPageParam";
        setLoginInfoParam(reqDTO);
        Map<String, Object> params = new HashMap<>();
        params.put(POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, params, null, OilConsumptionAnalysisResDTO.class);
    }


    /**
     * 获取油耗费用分析分页
     */
    @PostMapping("/oil/consumption/getFeeByPageParam")
    public RestResponse<PageDTO<OilConsumptionAnalysisFeeResDTO>> getFeeByPageParam(@RequestBody OilConsumptionAnalysisReqDTO reqDTO) {
        String url = new BusinessRestLocator().getRestUrl("/oil/consumption/getFeeByPageParam");
        setLoginInfoParam(reqDTO);
        Map<String, Object> params = new HashMap<>();
        params.put(POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, params, null, OilConsumptionAnalysisFeeResDTO.class);
    }


    /**
     * 取 车机/加油记录 油耗分析对比
     */
    @PostMapping("/oil/consumption/getComparePageParam")
    public RestResponse<PageDTO<OilConsumptionAnalysisCompareResDTO>> getComparePageParam(@RequestBody OilConsumptionAnalysisReqDTO reqDTO) {
        String url = new BusinessRestLocator().getRestUrl("/oil/consumption/getComparePageParam");
        setLoginInfoParam(reqDTO);
        Map<String, Object> params = new HashMap<>();
        params.put(POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, params, null, OilConsumptionAnalysisCompareResDTO.class);
    }


    /**
     * 获取 汇总数据
     */
    @PostMapping("/oil/consumption/getSummary")
    public RestResponse<OilConsumptionAnalysisSummaryResDTO> getSummary(@RequestBody OilConsumptionAnalysisReqDTO reqDTO) {
        String url = new BusinessRestLocator().getRestUrl("/oil/consumption/getSummary");
        setLoginInfoParam(reqDTO);
        Map<String, Object> params = new HashMap<>();
        params.put(POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, params, null, OilConsumptionAnalysisSummaryResDTO.class);
    }


    /**
     * 导出数据
     */
    @PostMapping("/oil/consumption/export")
    public void export(@RequestBody OilConsumptionAnalysisReqDTO reqDTO,HttpServletResponse response) throws IOException {
        Integer exportType = reqDTO.getExportType();
        reqDTO.setPage(1);
        reqDTO.setPageSize(10000);
        ServletOutputStream outputStream = response.getOutputStream();
        response.setCharacterEncoding("UTF-8");
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        //response.setContentType("application/vnd.ms-excel");
        ExcelWriter excelWriter = null;
        if (exportType == 1) {
            //导出油耗分析
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=油耗分析.xlsx");
            excelWriter = EasyExcel.write(outputStream).registerWriteHandler(new SimpleColumnWidthStyleStrategy(20)).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "油耗分析")
                    .head(OilConsumptionAnalysisResDTO.class).build();
            PageDTO<OilConsumptionAnalysisResDTO> page;
            do {
                page = this.getOilByPageParam(reqDTO).getData();
                List<OilConsumptionAnalysisResDTO> sheetResult = page.getResult();
                reqDTO.setPage(page.getPage() + 1);
                excelWriter.write(sheetResult, writeSheet);
            } while (page.getPage() < page.getPages());
        } else if (exportType == 2) {
            //导出油耗费用分析
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=油耗费用分析.xlsx");
            excelWriter = EasyExcel.write(outputStream).registerWriteHandler(new SimpleColumnWidthStyleStrategy(20)).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "油耗费用分析")
                    .head(OilConsumptionAnalysisFeeResDTO.class).build();
            PageDTO<OilConsumptionAnalysisFeeResDTO> page;
            do {
                page = this.getFeeByPageParam(reqDTO).getData();
                List<OilConsumptionAnalysisFeeResDTO> sheetResult = page.getResult();
                reqDTO.setPage(page.getPage() + 1);
                excelWriter.write(sheetResult, writeSheet);
            } while (page.getPage() < page.getPages());
        } else if (exportType == 3) {
            //导出油耗对比
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=油耗费用对比分析.xlsx");
            excelWriter = EasyExcel.write(outputStream).registerWriteHandler(new SimpleColumnWidthStyleStrategy(20)).build();
            WriteSheet writeSheet = EasyExcel.writerSheet(0, "油耗费用对比分析")
                    .head(OilConsumptionAnalysisCompareResDTO.class).build();
            PageDTO<OilConsumptionAnalysisCompareResDTO> page;
            do {
                page = this.getComparePageParam(reqDTO).getData();
                List<OilConsumptionAnalysisCompareResDTO> sheetResult = page.getResult();
                reqDTO.setPage(page.getPage() + 1);
                excelWriter.write(sheetResult, writeSheet);
            } while (page.getPage() < page.getPages());
        }

        outputStream.flush();
        excelWriter.finish();
        outputStream.close();
    }


    /**
     * 执行历史任务  只对运营段开放
     */
    @PostMapping("/oil/consumption/saveHistoryTask")
    public RestResponse saveHistoryTask(@RequestBody OilConsumptionAnalysisReqDTO reqDTO) {
        String url = new BusinessRestLocator().getRestUrl("/oil/consumption/saveHistoryTask");
        setLoginInfoParam(reqDTO);
        Map<String, Object> params = new HashMap<>();
        params.put(POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, params, null, OilConsumptionAnalysisReqDTO.class);
    }



    /**
     * 查询历史任务  只对运营段开放
     */
    @PostMapping("/oil/consumption/getHistoryTaskByPage")
    public RestResponse<PageDTO<RefuelAnalysisHistoryTaskDTO>> getHistoryTaskByPage(@RequestBody OilConsumptionAnalysisReqDTO reqDTO) {
        String url = new BusinessRestLocator().getRestUrl("/oil/consumption/getHistoryTaskByPage");
        setLoginInfoParam(reqDTO);
        Map<String, Object> params = new HashMap<>();
        params.put(POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, params, null, RefuelAnalysisHistoryTaskDTO.class);
    }



    private void setLoginInfoParam(OilConsumptionAnalysisReqDTO reqDTO) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        reqDTO.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        reqDTO.setLoginCompanyCode(baseLoginInfo.obtainBelongCompanyCode());
        reqDTO.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        reqDTO.setLoginUserId(baseLoginInfo.obtainBaseInfo().getStaffId());
        reqDTO.setLoginUserName(baseLoginInfo.obtainBaseInfo().getStaffName());
        reqDTO.setSystemType(baseLoginInfo.getSystemType());
        LoginBaseInfo.SimpleDataPerm simpleDataPerm = baseLoginInfo.obtainSimpleDataPerm();
        reqDTO.setDataPermType(simpleDataPerm.getDataPermType());

        SystemTypeEnum systemTypeEnum = SystemTypeEnum.getByCode(baseLoginInfo.getSystemType());
        DataPerm dataPerm = baseLoginInfo.obtainDataPerm();
        Byte dataPermType = dataPerm.getDataPermType();
        reqDTO.setDataPermSet(Sets.newHashSet("-1"));
        if (systemTypeEnum == SystemTypeEnum.CUSTOMER) {
            // 客户端
            ClientDataPermTypeEnum typeEnum = ClientDataPermTypeEnum.getByType(dataPermType);
            switch (typeEnum) {
                case SELF_COMPANY:
                    // 公司ID
                    Set<String> companyIdSet = baseLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.ID).getDataCodeSet();
                    reqDTO.setDataPermSet(companyIdSet);
                    break;
                case ASSIGN_DEPT:
                    // 部门ID
                    Set<String> departmentIdSet = baseLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.ID).getDataCodeSet();
                    reqDTO.setDataPermSet(departmentIdSet);
                    break;
                case ASSIGN_CITY:
                    Set<String> cityIdSet = baseLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet();
                    reqDTO.setDataPermSet(cityIdSet);
                    break;
                case ONESELF:
                default:
                    break;
            }
        } else {
            // 运营端
            // ProviderDataPermTypeEnum typeEnum = ProviderDataPermTypeEnum.getByType(dataPermType);
        }
    }

}
