package com.izu.mrcar.controller.business;

import com.alibaba.fastjson.JSON;
import com.izu.config.dto.CompanyBussinessDTO;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.config.enums.BussinessTypeDictionary;
import com.izu.mrcar.shiro.session.WebSessionUtil;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.LoginUser;
import com.izu.mrcar.common.constants.Constant;
import com.izu.mrcar.common.constants.GlobalResponse;
import com.izu.mrcar.common.config.RestUrlConfig;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@RestController
@RequestMapping("/bussservice")
public class BussServiceController {

    private static final Logger logger = LoggerFactory.getLogger(BussServiceController.class);


    /**
     * 13.15申请用车开通服务列表接口
     * @param
     * @return
     * @throws Exception
     * <AUTHOR> 2019年06月27日
     */
    @PostMapping("/listapplyopen")
    public GlobalResponse getApplyTypeOn() throws Exception{
		LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        if (loginBaseInfo == null){
            return GlobalResponse.getResult(Constant.FAIL_STATUS);
        }
        try {
        	//开通业务编码；1：内部用车；2：商务用车
        	String restUrlAuth = RestUrlConfig.getConfig().getConfigCoreUrl() + "/companyBussiness/getListByCompanyId";
			Map<String,Object> paramsAuth = new HashMap<>();
			paramsAuth.put("companyId",loginBaseInfo.obtainBelongCompanyId());
			RestResponse restResponseAuth = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrlAuth, paramsAuth, null, CompanyBussinessDTO.class);
			logger.info("查询"+ "参数：" + paramsAuth.toString()+ "返回结果："+ JSON.toJSONString(restResponseAuth));
			if(restResponseAuth.isSuccess()==false||restResponseAuth.getData()==null){
				return GlobalResponse.getResult(Constant.SUCCESS_STATUS, null, 0);
			}
			List<CompanyBussinessDTO> companyBussinessDTOList = (List<CompanyBussinessDTO>) restResponseAuth.getData();
			List<CompanyBussinessDTO> resultList = companyBussinessDTOList
                    .stream()
                    .filter(p->p.getBussinessCode().intValue()==BussinessTypeDictionary.BUSSINESS_INSIDE_VEHICLE.value().intValue()
					||p.getBussinessCode().intValue()==BussinessTypeDictionary.BUSSINESS_OUTSIDE_VEHICLE.value().intValue()
					||p.getBussinessCode().intValue()==BussinessTypeDictionary.BUSSINESS_PRIVATECAR.value().intValue())
                    .collect(Collectors.toList());
			List<Map<String, Object>> result= new ArrayList<Map<String,Object>>();
			for (CompanyBussinessDTO companyBussinessDTO : resultList) {
				Map<String, Object> map =new HashMap<String, Object>();
				map.put("apply_type", companyBussinessDTO.getBussinessCode());
				map.put("is_open", companyBussinessDTO.getIsOpen());
				result.add(map);
			}
			return GlobalResponse.getResult(Constant.SUCCESS_STATUS, result, result.size());
        } catch (Exception e) {
            logger.error("申请用车开通服务错误", e);
            throw e;
        }
    }

}
