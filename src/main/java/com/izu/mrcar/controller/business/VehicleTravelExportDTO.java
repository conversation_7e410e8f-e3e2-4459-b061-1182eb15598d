package com.izu.mrcar.controller.business;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import izu.org.apache.poi.ss.usermodel.BorderStyle;
import izu.org.apache.poi.ss.usermodel.HorizontalAlignment;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

@HeadStyle(
        fillForegroundColor = 22,
        fillBackgroundColor = 22
)
@ColumnWidth(20)
@ContentStyle(
        horizontalAlignment = HorizontalAlignment.CENTER,
        borderBottom = BorderStyle.THIN,
        borderLeft = BorderStyle.THIN,
        borderRight = BorderStyle.THIN,
        borderTop = BorderStyle.THIN,
        wrapped = true
)
@Data
@ApiModel("车辆行程明细表单")
public class VehicleTravelExportDTO {

    @ExcelIgnore
    private Long id;
    @ExcelProperty(
            value = {"车牌号"},
            order = 2
    )
    @ApiModelProperty(value = "车牌号")
    private String vehicleLicense;
    @ExcelProperty(
            value = {"车架号"},
            order = 3
    )
    @ApiModelProperty(value = "车架号")
    private String vehicleVin;
    @ExcelIgnore
    private String vehicleSerialNo;
    @ExcelIgnore
    private String fuelType;
    @ExcelIgnore
    private String leasePurpose;
    @ExcelIgnore
    private String leasePurposeName;
    @ExcelIgnore
    private Integer rentStatus;
    @ExcelIgnore
    private String custNo;
    @ExcelIgnore
    private String custName;
    @ExcelIgnore
    private String orderNo;
    @ExcelIgnore
    private Integer deviceType;
    @ExcelProperty(
            value = {"SIM卡号"},
            order = 15
    )
    private String simNo;
    @ExcelIgnore
    private String deviceNo;
    @ExcelIgnore
    private String belongBussCode;
    @ExcelIgnore
    private String belongBussName;
    @ExcelIgnore
    private String operateBussCode;
    @ExcelIgnore
    private String operateBussName;
    @ExcelIgnore
    private String accountCityCode;
    @ExcelIgnore
    private String accountCityName;
    @ExcelProperty(
            value = {"行程编号"},
            order = 1
    )
    @ApiModelProperty(value = "行程编号")
    private String travelSerialNo;
    @ExcelIgnore
    private String startTotalMile;
    @ExcelIgnore
    private String endTotalMile;
    @ExcelIgnore
    private BigDecimal fuel;
    @ExcelProperty(
            value = {"百公里油耗(升/100公里)"},
            order = 16
    )
    @ApiModelProperty(value = "百公里油耗(升/100公里)",example = "无显示 - ，有则为9.12")
    private String avgOil;
    @ExcelProperty(
            value = {"油耗总数(L)"},
            order = 17
    )
    @ApiModelProperty(value = "当前行程耗油量(L)",example = "无显示 - ，有则为9.12")
    private String useOil;
    @ExcelProperty(
            value = {"起点城市"},
            order = 4
    )
    @ApiModelProperty(value = "起点城市")
    private String startCity;
    @ExcelProperty(
            value = {"起点时间"},
            order = 8
    )
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @ApiModelProperty(value = "起点时间")
    private Date startTime;
    @ExcelProperty(
            value = {"起点位置"},
            order = 6
    )
    @ApiModelProperty(value = "起点位置")
    private String startPosition;
    @ExcelIgnore
    private String startLocation;
    @ExcelProperty(
            value = {"终点城市"},
            order = 5
    )
    @ApiModelProperty(value = "终点城市")
    private String endCity;
    @ExcelProperty(
            value = {"终点时间"},
            order = 9
    )
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @ApiModelProperty(value = "终点时间")
    private Date endTime;
    @ExcelProperty(
            value = {"终点位置"},
            order = 7
    )
    @ApiModelProperty(value = "终点位置")
    private String endPosition;
    @ExcelIgnore
    private String endLocation;
    @ExcelIgnore
    private BigDecimal travelMile;
    @ExcelProperty(
            value = {"行驶里程（位移向量算法：公里）"},
            order = 12
    )
    @ApiModelProperty(value = "行驶里程（位移向量算法：公里）")
    private BigDecimal travelMileBak;
    @ExcelProperty(
            value = {"行驶里程（里程表读数：公里）"},
            order = 13
    )
    @ApiModelProperty(value = "行驶里程（里程表读数：公里）")
    private String travelMileRead;
    @ExcelIgnore
    private String avgSpeed;
    @ExcelIgnore
    private String maxSpeed;
    @ExcelIgnore
    private String travelTag;
    @ExcelIgnore
    private Date createTime;
    @ExcelIgnore
    private Date updateTime;
    @ExcelProperty(
            value = {"所属企业"},
            order = 10
    )
    @ApiModelProperty(value = "所属企业")
    private String companyName;
    @ExcelIgnore
    @JsonFormat(
            pattern = "yyyy-MM-dd"
    )
    private Date statDate;
    @ExcelProperty(
            value = {"行驶时长（时:分）"},
            order = 11
    )
    @ApiModelProperty(value = "行驶时长（时:分）")
    private String travelDurationName;
    @ExcelProperty(
            value = {"设备类型"},
            order = 14
    )
    @ApiModelProperty(value = "设备类型")
    private String deviceTypeName;
    @ExcelIgnore
    private String rentStatusName;
    @ExcelIgnore
    private String manfactName;
    @ExcelIgnore
    private String modelName;
}
