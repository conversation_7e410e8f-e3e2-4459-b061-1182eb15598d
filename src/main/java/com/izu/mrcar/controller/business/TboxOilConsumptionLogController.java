package com.izu.mrcar.controller.business;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.izu.business.config.BusinessRestLocator;
import com.izu.business.dto.oil.VehicleTboxOilConsumptionExportDTO;
import com.izu.business.dto.oil.VehicleTboxOilConsumptionRespDTO;
import com.izu.business.dto.oil.VehicleTboxOilConsumptionSearchDTO;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.dto.WeChatMiniProgramUserExportDTO;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.dto.miniprogram.WeChatMiniProgramUserRespDTO;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import com.izu.user.dto.staff.pc.DataPerm;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.enums.perm.ClientDataPermTypeEnum;
import com.izu.user.enums.perm.ProviderDataPermTypeEnum;
import com.izu.user.enums.role.SystemTypeEnum;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

import static com.izu.mrcar.common.errorcode.ErrorCode.STAT_FUNCTION_OPERATION_EXPORT_EXCEED;

/**
 * 车机油耗日志
 */
@RestController
@RequestMapping("/oil/tbox/log")
public class TboxOilConsumptionLogController {

    /**
     * 列表(客户端)
     */
    @PostMapping("/search")
    @SuppressWarnings("unchecked")
    public RestResponse<PageDTO<VehicleTboxOilConsumptionRespDTO>> search(
            @RequestBody VehicleTboxOilConsumptionSearchDTO searchDTO) {
        // 数据权限
        LoginBaseInfo loginInfo = LoginSessionUtil.getBaseLoginInfo();
        DataPerm dataPerm = loginInfo.obtainDataPerm();
        SystemTypeEnum systemType = SystemTypeEnum.getByCode(loginInfo.getSystemType());
        // 基本信息
        AccountBaseInfo baseInfo = loginInfo.obtainBaseInfo();
        // 客户端
        ClientDataPermTypeEnum dataPermType = ClientDataPermTypeEnum.getByType(dataPerm.getDataPermType());
        switch (dataPermType) {
            case SELF_COMPANY:
            {
                // 本企业
                Set<String> companyIds = loginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.ID).getDataCodeSet();
                searchDTO.setDataPermSet(companyIds);
                break;
            }
            case ASSIGN_DEPT:
            {
                // 指定部门
                Set<String> structIds = loginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.ID).getDataCodeSet();
                searchDTO.setDataPermSet(structIds);
                // 登录人
                searchDTO.setLoginUserId(baseInfo.getStaffId());
                // 所属公司
                searchDTO.setCompanyId(loginInfo.obtainBelongCompanyId());
                break;
            }
            case ASSIGN_CITY:
            {
                // 指定城市
                Set<String> belongCityCodes = loginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet();
                searchDTO.setDataPermSet(belongCityCodes);
                // 登录人
                searchDTO.setLoginUserId(baseInfo.getStaffId());
                // 所属公司
                searchDTO.setCompanyId(loginInfo.obtainBelongCompanyId());
                break;
            }
            case ONESELF:
            {
                // 本人
                // 登录人
                searchDTO.setLoginUserId(baseInfo.getStaffId());
                break;
            }
        }
        // 登录方式
        searchDTO.setSystemType(loginInfo.getSystemType());
        // 权限范围
        searchDTO.setDataPermType(dataPerm.getDataPermType());
        // 登录公司
        searchDTO.setLoginCompanyId(loginInfo.obtainBelongCompanyId());
        Map<String, Object> params = new HashMap<>();
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, searchDTO);
        String restUrl = new BusinessRestLocator().getRestUrl("/oil/tbox/search");
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, params, null,
                VehicleTboxOilConsumptionRespDTO.class);
    }

    /**
     * 导出(客户端)
     */
    @PostMapping("/export")
    @SuppressWarnings("unchecked")
    public void export(@RequestBody VehicleTboxOilConsumptionSearchDTO searchDTO,
                       HttpServletRequest request,
                       HttpServletResponse response) throws IOException {
        // 数据权限
        LoginBaseInfo loginInfo = LoginSessionUtil.getBaseLoginInfo();
        DataPerm dataPerm = loginInfo.obtainDataPerm();
        SystemTypeEnum systemType = SystemTypeEnum.getByCode(loginInfo.getSystemType());
        // 基本信息
        AccountBaseInfo baseInfo = loginInfo.obtainBaseInfo();
        // 客户端
        ClientDataPermTypeEnum dataPermType = ClientDataPermTypeEnum.getByType(dataPerm.getDataPermType());
        switch (dataPermType) {
            case SELF_COMPANY:
            {
                // 本企业
                Set<String> companyIds = loginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.ID).getDataCodeSet();
                searchDTO.setDataPermSet(companyIds);
                break;
            }
            case ASSIGN_DEPT:
            {
                // 指定部门
                Set<String> structIds = loginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.ID).getDataCodeSet();
                searchDTO.setDataPermSet(structIds);
                // 所属公司
                searchDTO.setCompanyId(loginInfo.obtainBelongCompanyId());
                break;
            }
            case ASSIGN_CITY:
            {
                // 指定城市
                Set<String> belongCityCodes = loginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet();
                searchDTO.setDataPermSet(belongCityCodes);
                // 所属公司
                searchDTO.setCompanyId(loginInfo.obtainBelongCompanyId());
                break;
            }
            default:
                break;
        }
        // 登录方式
        searchDTO.setSystemType(loginInfo.getSystemType());
        // 权限范围
        searchDTO.setDataPermType(dataPerm.getDataPermType());
        // 登录公司
        searchDTO.setLoginCompanyId(loginInfo.obtainBelongCompanyId());
        // 登录人信息
        searchDTO.setLoginUserId(baseInfo.getStaffId());
        int page = 1;
        int pageSize = 100;
        int pages;
        List<VehicleTboxOilConsumptionExportDTO> result = new ArrayList<>();
        do {
            searchDTO.setPage(page);
            searchDTO.setPageSize(pageSize);

            String url = new BusinessRestLocator().getRestUrl("/oil/tbox/search");
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, searchDTO);
            @SuppressWarnings("unchecked")
            RestResponse<PageDTO<VehicleTboxOilConsumptionRespDTO>> restResponse =
                    RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null,
                            VehicleTboxOilConsumptionRespDTO.class);
            if (!restResponse.isSuccess()) {
                JSON.writeJSONString(response.getOutputStream(), restResponse);
                response.setContentType("application/json");
                return;
            }

            PageDTO<VehicleTboxOilConsumptionRespDTO> pageDTO = restResponse.getData();

            // 校验记录条数
            if (pageDTO.getTotal() > ExportExcelConstants.EXPORT_MAX_LINE) {
                RestResponse<?> rest = RestResponse.fail(STAT_FUNCTION_OPERATION_EXPORT_EXCEED,
                        pageDTO.getTotal(), ExportExcelConstants.EXPORT_MAX_LINE);
                JSON.writeJSONString(response.getOutputStream(), rest);
                response.setContentType("application/json");
                return;
            }

            // 设置页码信息
            pages = pageDTO.getPages();
            result.addAll(
                    pageDTO.getResult()
                            .stream()
                            .map(s -> {
                                VehicleTboxOilConsumptionExportDTO dto = new VehicleTboxOilConsumptionExportDTO();
                                BeanUtils.copyProperties(s, dto);
                                return dto;
                            }).collect(Collectors.toList()));
            page++;
        } while (page <= pages);

        // 动态表头
        Set<String> columns = new HashSet<>();
        Field[] fields = VehicleTboxOilConsumptionExportDTO.class.getDeclaredFields();
        for (Field field : fields) {
            columns.add(field.getName());
        }
        // 如果不包含子企业
        if (dataPermType == ClientDataPermTypeEnum.SELF_COMPANY) {
            Set<String> companyIds =
                    loginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.ID).getDataCodeSet();
            if (companyIds.size() <= 1) {
                columns.remove("companyName");
            }
        } else {
            columns.remove("companyName");
        }

        String fileName = URLEncoder.encode("车机油耗日志", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), VehicleTboxOilConsumptionExportDTO.class)
                .sheet("车机油耗日志")
                .includeColumnFiledNames(columns)
                .doWrite(result);
    }

}
