package com.izu.mrcar.controller.business;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.izu.business.dto.StatisticsBusinessCollectDTO;
import com.izu.business.dto.StatisticsBusinessDTO;
import com.izu.business.dto.TopFunctionDTO;
import com.izu.business.dto.provider.input.CompanyStatisticsReqDTO;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.excel.Column;
import com.izu.framework.web.util.excel.ExSheet;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.controller.AbstractExcelDownloadController;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.perm.provider.ProviderBusinessDataPermUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.dto.crmCustomer.CrmCustomerQueryReqDTO;
import com.izu.user.dto.provider.company.ProviderCompanySimpleRespDTO;
import com.izu.user.dto.staff.pc.DataPermInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.enums.perm.ProviderDataPermTypeEnum;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;

/**
* @Description: 企业经营数据分析
* @author: hxc
* @Date: 2021/12/22
**/
@RestController
@RequestMapping("/statistics/business")
@Slf4j
public class BusinessStatisticsController extends AbstractExcelDownloadController {

    @Value("${mrcar-business-core.host.url}")
    private String BUSINESS_HOST_URL;

    @PostMapping("/searchPageList")
    @RequestFunction(functionName = "企业经营数据分析")
    @ApiOperation(value = "企业经营数据分析")
    public RestResponse<StatisticsBusinessCollectDTO> searchPageList(@RequestBody CompanyStatisticsReqDTO companyStatisticsReqDTO) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        //数据权限不等于所有并且所属部门不等于总部的
        AtomicBoolean flag= new AtomicBoolean(false);
        List<String> companyIds = new ArrayList<>();
        if(ProviderDataPermTypeEnum.ALL.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType())){
            flag.set(true);
        }
        if(ProviderDataPermTypeEnum.ASSIGN_DEPT.getType().equals(providerLoginInfo.getProviderDataPerm().getDataPermType()) &&
                CollUtil.isNotEmpty(providerLoginInfo.getProviderDataPerm().getDataPermInfoList())){
            List<DataPermInfo> dataPermInfoList = providerLoginInfo.getProviderDataPerm().getDataPermInfoList();
            //查询所属部门
            String url = new UserRestLocator().getRestUrl(UserUrlCenter.PROVIDER_HEAD_DEPARTMENT_LIST);
            RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, url, null, null, String.class);
            if(restResponse!=null && restResponse.isSuccess() && restResponse.getData()!=null){
                List<String> list = (List<String>) restResponse.getData();
                dataPermInfoList.forEach(x->{
                    if(list.contains(x.getDataPermCode())){
                        flag.set(true);
                    }
                    companyIds.add(String.valueOf(x.getDataPermCode()));
                });
            }
        }
        List<String>  customerCodeList = new ArrayList<>();
        if(!flag.get()){
            //先查询用户是否存在数据权限
            String userUrl = new UserRestLocator().getRestUrl(UserUrlCenter.GET_CUSTOMER_CODE_LIST_BY_STAFF_COMPANY);
            CrmCustomerQueryReqDTO dto = new CrmCustomerQueryReqDTO();
            dto.setOwnerOrCreateOrUpdateId(providerLoginInfo.getBaseInfo().getMgtStaffId());
            if(Objects.equals(providerLoginInfo.obtainDataPerm().getDataPermType(),ProviderDataPermTypeEnum.ASSIGN_DEPT.getType())){
                dto.setCompanyIds(companyIds);
            }
            Map<String, Object> param = new HashMap<>();
            param.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
            RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, userUrl, param, null, String.class);
            if(restResponse!=null && restResponse.isSuccess() && restResponse.getData()!=null){
                customerCodeList=(List<String>) restResponse.getData();
            }
        }
        //没有企业，并且不是全部企业
        if(!flag.get()&& CollectionUtil.isEmpty(customerCodeList)){
            PageDTO<ProviderCompanySimpleRespDTO> pageDTO = new PageDTO<>();
            pageDTO.setResult(Collections.emptyList());
            pageDTO.setTotal(0);
            pageDTO.setPage(companyStatisticsReqDTO.getPage());
            return RestResponse.success(pageDTO);
        }
        companyStatisticsReqDTO.setCompanyCodeList(customerCodeList);
        Map<String, Object> params = new HashMap<>();
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, companyStatisticsReqDTO);
        String restUrl = BUSINESS_HOST_URL + "/statistics/business/searchPageList";
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, params, null, StatisticsBusinessCollectDTO.class);
    }

    @RequestMapping("/export")
    @RequestFunction(functionName = "企业经营数据分析导出")
    public void export(CompanyStatisticsReqDTO companyStatisticsReqDTO, HttpServletRequest request, HttpServletResponse response) {
        try {
            Map<String, Object> params = new HashMap<>();
            ProviderBusinessDataPermUtil.setProviderDataPerm(companyStatisticsReqDTO);
            companyStatisticsReqDTO.setPageSize(Integer.MAX_VALUE);
            params.put(BaseHttpClient.POSTBODY_MAP_KEY,companyStatisticsReqDTO);
            String restUrl = BUSINESS_HOST_URL + "/statistics/business/searchPageList";
            RestResponse result = RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, params, null, StatisticsBusinessCollectDTO.class);
            if (!result.isSuccess()){
                return;
            }
            StatisticsBusinessCollectDTO businessCollectDTO = (StatisticsBusinessCollectDTO) result.getData();
            PageDTO pageInfo = businessCollectDTO.getPageInfo();
            List<StatisticsBusinessDTO> resultData = JSONObject.parseArray(JSON.toJSONString(pageInfo.getResult()), StatisticsBusinessDTO.class);
            StatisticsBusinessDTO total = businessCollectDTO.getTotal();
            exportExcel(resultData,total,request,response);
        }catch (Exception e){
            log.error("导出企业经营数据异常",e);
        }
    }

    private void exportExcel(List<StatisticsBusinessDTO> datas, StatisticsBusinessDTO total, HttpServletRequest request, HttpServletResponse response) {

        List<Column> columnModes = new ArrayList<>();
        columnModes.add(new Column("companyName", "客户名称", (short) 8000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("belongDeptName", "所属部门", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("publicPondName", "所属公海", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("accountTypeStr", "账号类型", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("companyStatusName", "客户状态", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("lastLoginTime", "最后登录日期", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("companyCreateTime", "创建时间", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("accountOpenTime", "开通时间", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("openAccountMethodStr", "开通方式", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("packNames", "产品包", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("activeStatusStr", "近30天活跃度", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("loginDays", "近30天登录天数", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("customerCount", "员工总数", (short) 3000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("loginStaffCount", "近30天登录员工数", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("vehicleCount", "车辆总数", (short) 3000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("driverCount", "司机总数", (short) 3000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("orderApplyCount", "用车订单总数", (short) 3000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("applyCount", "审批单总数", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("vehicleShouqiCount", "首汽车辆", (short) 3000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("vehicleZiyouCount", "自有车辆", (short) 3000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("vehicleThirdCount", "三方车辆", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("vehicleStaffCount", "员工车辆", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("driverShouqiCount", "首汽司机", (short) 3000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("driverZiyouCount", "自有司机", (short) 3000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("driverThirdCount", "三方司机", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("deviceShouqiCount", "首汽设备总数", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("deviceSelfCount", "自有设备总数", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("deviceWirelessCount", "无线", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("deviceWiredCount", "有线", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("deviceObdCount", "车机", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("deviceVideoCount", "视频设备", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("contractCount", "业务合同数", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("contractAmount", "业务合同金额", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0.00", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("systemContractCount", "系统合同数", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("systemContractAmount", "系统合同金额", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0.00", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("billAmount", "累计账单金额", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0.00", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("returnAmount", "累计回款金额", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0.00", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("deviceAmount", "设备费用", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0.00", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("vehicleAmount", "车务费用", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0.00", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("systemAmount", "系统费用", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0.00", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("orderApplyInternalCount", "内部用车订单", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("orderApplyMotorcadeCount", "商务用车订单", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("privateOrderCount", "私车公用订单数", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("soOrderCount", "零散用车需求单", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("coOrderCount", "代步车订单", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("govPublicOrderCount", "公务用车订单", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("chargeOrderCount", "充电订单", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("violationCount", "违章记录", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("maintenanceSqCount", "首汽维保单", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("maintenanceCompanyCount", "企业维保单", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("top1", "TOP1", (short) 4500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("top1Time", "访问次数", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("top2", "TOP2", (short) 4500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("top2Time", "访问次数", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("top3", "TOP3", (short) 4500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("top3Time", "访问次数", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("top4", "TOP4", (short) 4500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("top4Time", "访问次数", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("top5", "TOP5", (short) 4500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("top5Time", "访问次数", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_NUMERIC, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        final List<Map<String, String>> rowDatas = new ArrayList<Map<String, String>>(datas.size() + 2);
        final SimpleDateFormat yyyyMMdd_sdf = new SimpleDateFormat("yyyy-MM-dd");
        final SimpleDateFormat yyyyHHMMss = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (StatisticsBusinessDTO data : datas) {
            final Map<String, String> rowdata = new HashMap<String, String>(columnModes.size() * 2);
            rowdata.put("companyName", data.getCompanyName());
            rowdata.put("belongDeptName", data.getBelongDeptName());
            rowdata.put("publicPondName", data.getPublicPondName());
            rowdata.put("accountTypeStr", data.getAccountTypeStr());
            rowdata.put("companyStatusName", data.getCompanyStatusName());
            rowdata.put("lastLoginTime", data.getLastLoginTime() == null ? "" : yyyyMMdd_sdf.format(data.getLastLoginTime()));
            rowdata.put("companyCreateTime", data.getCompanyCreateTime()==null?"":yyyyHHMMss.format(data.getCompanyCreateTime()));
            rowdata.put("accountOpenTime", data.getAccountOpenTime()==null?"":yyyyHHMMss.format(data.getAccountOpenTime()));
            rowdata.put("openAccountMethodStr", data.getOpenAccountMethodStr());
            rowdata.put("packNames", data.getPackNames());
            rowdata.put("activeStatusStr", data.getActiveStatusStr());
            rowdata.put("loginDays", data.getLoginDays()==null?"":data.getLoginDays().toString());
            rowdata.put("customerCount", data.getCustomerCount().toString());
            rowdata.put("loginStaffCount", data.getLoginStaffCount()==null?"":data.getLoginStaffCount().toString());
            rowdata.put("vehicleCount", data.getVehicleCount().toString());
            rowdata.put("driverCount", data.getDriverCount().toString());
            rowdata.put("orderApplyCount", data.getOrderApplyCount().toString());
            rowdata.put("applyCount", data.getApplyCount()==null?"":data.getApplyCount().toString());
            rowdata.put("vehicleShouqiCount", data.getVehicleShouqiCount().toString());
            rowdata.put("vehicleZiyouCount", data.getVehicleZiyouCount().toString());
            rowdata.put("vehicleThirdCount", data.getVehicleThirdCount()==null?"":data.getVehicleThirdCount().toString());
            rowdata.put("vehicleStaffCount", data.getVehicleStaffCount()==null?"":data.getVehicleStaffCount().toString());
            rowdata.put("driverShouqiCount", data.getDriverShouqiCount().toString());
            rowdata.put("driverZiyouCount", data.getDriverZiyouCount().toString());
            rowdata.put("driverThirdCount", data.getDriverThirdCount()==null?"":data.getDriverThirdCount().toString());
            rowdata.put("deviceShouqiCount", data.getDeviceShouqiCount()==null?"":data.getDeviceShouqiCount().toString());
            rowdata.put("deviceSelfCount", data.getDeviceZiyouCount()==null?"":data.getDeviceZiyouCount().toString());
            rowdata.put("deviceWirelessCount", data.getDeviceWirelessCount()==null?"":data.getDeviceWirelessCount().toString());
            rowdata.put("deviceWiredCount", data.getDeviceWiredCount()==null?"":data.getDeviceWiredCount().toString());
            rowdata.put("deviceObdCount", data.getDeviceObdCount()==null?"":data.getDeviceObdCount().toString());
            rowdata.put("deviceVideoCount", data.getDeviceVideoCount()==null?"":data.getDeviceVideoCount().toString());

            rowdata.put("contractCount", data.getContractCount()==null?"":data.getContractCount().toString());
            rowdata.put("contractAmount", data.getContractAmount()==null?"":data.getContractAmount().toString());
            rowdata.put("systemContractCount", data.getSystemContractCount()==null?"":data.getSystemContractCount().toString());
            rowdata.put("systemContractAmount", data.getSystemContractAmount()==null?"":data.getSystemContractAmount().toString());
            rowdata.put("billAmount", data.getBillAmount()==null?"":data.getBillAmount().toString());
            rowdata.put("returnAmount", data.getReturnAmount()==null?"":data.getReturnAmount().toString());
            rowdata.put("deviceAmount", data.getDeviceAmount()==null?"":data.getDeviceAmount().toString());
            rowdata.put("vehicleAmount", data.getVehicleAmount()==null?"":data.getVehicleAmount().toString());
            rowdata.put("systemAmount", data.getSystemAmount()==null?"":data.getSystemAmount().toString());
            rowdata.put("deviceVideoCount", data.getDeviceVideoCount()==null?"":data.getDeviceVideoCount().toString());
            rowdata.put("deviceVideoCount", data.getDeviceVideoCount()==null?"":data.getDeviceVideoCount().toString());
            rowdata.put("orderApplyInternalCount", data.getOrderApplyInternalCount().toString());
            rowdata.put("orderApplyMotorcadeCount", data.getOrderApplyMotorcadeCount().toString());
            rowdata.put("privateOrderCount", String.valueOf(data.getPrivateOrderCount()));

            rowdata.put("soOrderCount", String.valueOf(data.getSoOrderCount()));
            rowdata.put("coOrderCount", String.valueOf(data.getCoOrderCount()));
            rowdata.put("govPublicOrderCount", String.valueOf(data.getGovPublicOrderCount()));
            rowdata.put("chargeOrderCount", String.valueOf(data.getChargeOrderCount()));
            rowdata.put("violationCount", String.valueOf(data.getViolationCount()));
            rowdata.put("maintenanceSqCount", String.valueOf(data.getMaintenanceSqCount()));
            rowdata.put("maintenanceCompanyCount", String.valueOf(data.getMaintenanceCompanyCount()));
            List<TopFunctionDTO> topFunctions = data.getTopFunction();
            if(CollectionUtils.isNotEmpty(topFunctions)) {
                for (TopFunctionDTO topFunction : topFunctions) {
                    rowdata.put("top" + topFunction.getOrder(), topFunction.getFunctionName());
                    rowdata.put("top" + topFunction.getOrder() + "Time", String.valueOf(topFunction.getCount()));
                }
            }
            rowDatas.add(rowdata);
        }
        if (!datas.isEmpty()){
            final Map<String, String> rowdata = new HashMap<String, String>(columnModes.size() * 2);
            rowdata.put("companyName", "合计");
            rowdata.put("belongDeptName", "-");
            rowdata.put("publicPondName", "-");
            rowdata.put("accountTypeStr", "-");
            rowdata.put("companyStatusName", "-");
            rowdata.put("lastLoginTime", "-");
            rowdata.put("companyCreateTime","-");
            rowdata.put("accountOpenTime","-");
            rowdata.put("openAccountMethodStr","-");
            rowdata.put("packNames", "-");
            rowdata.put("activeStatusStr", "-");
            rowdata.put("loginDays", total.getLoginDays().toString());
            rowdata.put("customerCount", total.getCustomerCount().toString());
            rowdata.put("loginStaffCount",total.getLoginStaffCount().toString());
            rowdata.put("vehicleCount", total.getVehicleCount().toString());
            rowdata.put("driverCount", total.getDriverCount().toString());
            rowdata.put("orderApplyCount", total.getOrderApplyCount().toString());
            rowdata.put("applyCount", total.getApplyCount().toString());
            rowdata.put("vehicleShouqiCount", total.getVehicleShouqiCount().toString());
            rowdata.put("vehicleZiyouCount", total.getVehicleZiyouCount().toString());
            rowdata.put("vehicleThirdCount", total.getVehicleThirdCount().toString());
            rowdata.put("vehicleStaffCount", total.getVehicleStaffCount().toString());
            rowdata.put("driverShouqiCount", total.getDriverShouqiCount().toString());
            rowdata.put("driverZiyouCount", total.getDriverZiyouCount().toString());
            rowdata.put("driverThirdCount", total.getDriverThirdCount().toString());
            rowdata.put("deviceShouqiCount", total.getDeviceShouqiCount().toString());
            rowdata.put("deviceSelfCount", total.getDeviceZiyouCount().toString());
            rowdata.put("deviceWirelessCount", total.getDeviceWirelessCount().toString());
            rowdata.put("deviceWiredCount", total.getDeviceWiredCount().toString());
            rowdata.put("deviceObdCount", total.getDeviceObdCount().toString());
            rowdata.put("deviceVideoCount", total.getDeviceVideoCount().toString());
            rowdata.put("contractCount", total.getContractCount().toString());
            rowdata.put("contractAmount", total.getContractAmount().toString());
            rowdata.put("systemContractCount", total.getSystemContractCount().toString());
            rowdata.put("systemContractAmount", total.getSystemContractAmount().toString());
            rowdata.put("billAmount", total.getBillAmount().toString());
            rowdata.put("returnAmount", total.getReturnAmount().toString());
            rowdata.put("deviceAmount", total.getDeviceAmount().toString());
            rowdata.put("vehicleAmount", total.getVehicleAmount().toString());
            rowdata.put("systemAmount", total.getSystemAmount().toString());
            rowdata.put("orderApplyInternalCount", total.getOrderApplyInternalCount().toString());
            rowdata.put("orderApplyMotorcadeCount", total.getOrderApplyMotorcadeCount().toString());
            rowdata.put("privateOrderCount", String.valueOf(total.getPrivateOrderCount()));
            rowdata.put("soOrderCount", String.valueOf(total.getSoOrderCount()));
            rowdata.put("coOrderCount", String.valueOf(total.getCoOrderCount()));
            rowdata.put("govPublicOrderCount", String.valueOf(total.getGovPublicOrderCount()));
            rowdata.put("chargeOrderCount", String.valueOf(total.getChargeOrderCount()));
            rowdata.put("violationCount", String.valueOf(total.getViolationCount()));
            rowdata.put("maintenanceSqCount", String.valueOf(total.getMaintenanceSqCount()));
            rowdata.put("maintenanceCompanyCount", String.valueOf(total.getMaintenanceCompanyCount()));
            rowdata.put("top1", "-");
            rowdata.put("top1Time", "-");
            rowdata.put("top2", "-");
            rowdata.put("top2Time", "-");
            rowdata.put("top3", "-");
            rowdata.put("top3Time", "-");
            rowdata.put("top4", "-");
            rowdata.put("top4Time", "-");
            rowdata.put("top5", "-");
            rowdata.put("top5Time", "-");
            rowDatas.add(rowdata);
        }
        List<ExSheet> exSheetList = new ArrayList<>(1);
        exSheetList.add(new ExSheet(columnModes, rowDatas, "经营数据统计"));
        //---------------------------B: 根据上面创建好的Sheet，生成Excel
        String excelType = "xlsx";
        String fileNameCn = "经营数据统计_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + "." + excelType;
        String fileNameEn = "company_business_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + "." + excelType;
        this.exportExcel(request, response, fileNameCn, fileNameEn, exSheetList);
    }
}
