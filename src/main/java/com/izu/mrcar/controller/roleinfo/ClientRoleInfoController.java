package com.izu.mrcar.controller.roleinfo;

import com.google.common.collect.Maps;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.perm.UserDataPermUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.dto.customer.CustomerManageRespDTO;
import com.izu.user.dto.provider.roleinfo.RoleInfoGetStaffReqDTO;
import com.izu.user.dto.provider.roleinfo.RoleInfoPermScopeRespDTO;
import com.izu.user.dto.roleinfo.*;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.enums.perm.ClientDataPermTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * @program: mrcar
 * @description: 客户端角色管理
 * @author: ljw
 * @create: 2023-01-31 09:17
 **/
@RestController
@RequestMapping()
@Slf4j
@Api(tags = "角色管理-客户端")
public class ClientRoleInfoController {


    @PostMapping(UserUrlCenter.ROLE_INFO_QUERY_PAGE_COMPANY)
    @RequestFunction(functionName = "角色列表查询")
    @ApiOperation(value = "角色列表查询",notes = "作者：连江伟")
    public RestResponse<RoleInfoDTO> getPageListForProvider(@RequestBody RoleInfoReqDTO req){
        Map<String,Object> restParam = new HashMap<>();
        final ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
        ClientDataPermTypeEnum dataPermTypeEnum = ClientDataPermTypeEnum.getByType(clientLoginInfo.obtainDataPerm().getDataPermType());
        req.setCompanyCode(clientLoginInfo.obtainBelongCompanyCode());
        req.setLoginSystemType(clientLoginInfo.getSystemType());
        req.setLoginUserId(clientLoginInfo.obtainBaseInfo().getStaffId());
        req.setLoginCompanyId(clientLoginInfo.getClientCompany().getCompanyId());
        req.setDataPermType(dataPermTypeEnum.getType());
        if(Objects.equals(dataPermTypeEnum.getType(), ClientDataPermTypeEnum.SELF_COMPANY.getType())){
            Set<String> dataCodeSet = clientLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet();
            req.setDataCodeSet(dataCodeSet);
        }else{
            req.setLoginCompanyCode(clientLoginInfo.obtainBelongCompanyCode());
        }
        //UserDataPermUtil.setClientDataPerm(req);
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY,req);
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.ROLE_INFO_QUERY_PAGE_COMPANY);
        RestResponse response = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam,null,Map.class);
        return response;
    }

    @PostMapping(UserUrlCenter.ROLE_INFO_CREATE_COMPANY)
    @RequestFunction(functionName = "创建角色")
    @ApiOperation(value = "创建角色",notes = "作者：连江伟")
    public RestResponse saveRoleInfo(@RequestBody RoleInfoSaveDTO req){
        final ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
        req.setCreateId(clientLoginInfo.getBaseInfo().getStaffId());
        req.setCreateName(clientLoginInfo.getBaseInfo().getStaffName());
        req.setCompanyCode(clientLoginInfo.getClientCompany().getCompanyCode());
        req.setCompanyName(clientLoginInfo.getClientCompany().getCompanyName());
        req.setProviderCode(clientLoginInfo.getClientCompany().getProviderCode());
        req.setProviderName(clientLoginInfo.getClientCompany().getProviderName());
        Map<String,Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY,req);
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.ROLE_INFO_CREATE_COMPANY);
        RestResponse response = RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam,null);
        return response;
    }


    @PostMapping(UserUrlCenter.ROLE_INFO_UPDATE_COMPANY)
    @RequestFunction(functionName = "修改角色")
    @ApiOperation(value = "修改角色",notes = "作者：连江伟")
    public RestResponse updateRoleInfo(@RequestBody RoleInfoSaveDTO req){
        final ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
        req.setUpdateId(clientLoginInfo.getBaseInfo().getStaffId());
        req.setUpdateName(clientLoginInfo.getBaseInfo().getStaffName());
        Map<String,Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY,req);
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.ROLE_INFO_UPDATE_COMPANY);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam,null);
    }

    @PostMapping(UserUrlCenter.ROLE_INFO_DETAIL_COMPANY)
    @RequestFunction(functionName = "角色详情")
    @ApiOperation(value = "角色详情",notes = "作者：连江伟")
    @ApiImplicitParam(name = "roleId",value = "角色id",required = true)
    public RestResponse<RoleInfoDetailDTO> getRoleInfoDetail(Integer roleId){
        Map<String,Object> restParam = new HashMap<>();
        restParam.put("roleId",roleId);
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.ROLE_INFO_DETAIL_COMPANY);
        RestResponse response = RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, restParam,null);
        return response;
    }

    @PostMapping(UserUrlCenter.ROLE_DETAIL_COMPANY)
    @RequestFunction(functionName = "角色详情-详情专用")
    @ApiOperation(value = "角色详情-详情专用",notes = "作者：连江伟")
    @ApiImplicitParam(name = "roleId",value = "角色id",required = true)
    public RestResponse<RoleInfoDetailDTO> getRoleDetail(Integer roleId){
        Map<String,Object> restParam = new HashMap<>();
        restParam.put("roleId",roleId);
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.ROLE_DETAIL_COMPANY);
        RestResponse response = RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, restParam,null);
        return response;
    }

    @PostMapping(UserUrlCenter.ROLE_INFO_RELATION_STAFF_COMPANY)
    @RequestFunction(functionName = "角色添加用户")
    @ApiOperation(value = "角色添加用户",notes = "作者：连江伟")
    public RestResponse assignUserToRole (@RequestBody RoleUserSaveDTO saveDTO){
        final ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
        saveDTO.setLoginUserId(clientLoginInfo.getBaseInfo().getStaffId());
        saveDTO.setLoginUserName(clientLoginInfo.getBaseInfo().getStaffName());
        saveDTO.setCompanyCode(clientLoginInfo.obtainBelongCompanyCode());
        Map<String,Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY,saveDTO);
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.ROLE_INFO_RELATION_STAFF_COMPANY);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam,null);
    }

    @PostMapping(UserUrlCenter.ROLE_INFO_QUERY_LIKE_COMPANY)
    @RequestFunction(functionName = "角色名称模糊匹配")
    @ApiOperation(value = "角色名称模糊匹配",notes = "作者：连江伟")
    @ApiImplicitParam(name = "roleName",value = "角色名称输入内容",required = true)
    public RestResponse<RoleInfoDTO> getRoleInfoDetail(String roleName){
        final LoginBaseInfo loginInfo = LoginSessionUtil.getBaseLoginInfo();
        RoleInfoReqDTO req = new RoleInfoReqDTO();
        req.setRoleName(roleName);
        req.setCompanyCode(loginInfo.obtainBelongCompanyCode());
        Map<String,Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY,req);
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.ROLE_INFO_QUERY_LIKE_COMPANY);
        RestResponse response = RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam,null);
        return response;
    }

    @RequestFunction(functionName = "新建角色时的权限范围-客户端")
    @ApiOperation(value = "新建角色时的权限范围-客户端", notes = "作者：牛子联")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "",value = "",required = true,paramType = "form")
    })
    @PostMapping(UserUrlCenter.CLIENT_ROLE_GET_PERM_SCOPE_LIST)
    public RestResponse<RoleInfoPermScopeRespDTO> getPermScopeList() {
        final LoginBaseInfo loginInfo = LoginSessionUtil.getBaseLoginInfo();
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.CLIENT_ROLE_GET_PERM_SCOPE_LIST);
        final HashMap<String, Object> restParam = Maps.newHashMapWithExpectedSize(1);
        restParam.put("companyCode", loginInfo.obtainBelongCompanyCode());
        RestResponse response = RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, restParam,null);
        return response;
    }


    @RequestFunction(functionName = "角色详情-查询员工列表")
    @ApiOperation(value = "角色详情-查询员工列表", notes = "作者：牛子联")
    @PostMapping(UserUrlCenter.CLIENT_ROLE_GET_STAFF_LIST)
    public RestResponse<PageDTO<CustomerManageRespDTO>> getStaffList(@RequestBody @Validated RoleInfoGetStaffReqDTO reqDTO) {
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.CLIENT_ROLE_GET_STAFF_LIST);
        final LoginBaseInfo loginInfo = LoginSessionUtil.getBaseLoginInfo();
        reqDTO.setCompanyId(loginInfo.obtainBelongCompanyId());
        final HashMap<String, Object> restParam = Maps.newHashMapWithExpectedSize(1);
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        RestResponse response = RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam,null);
        return response;
    }

    @RequestFunction(functionName = "角色详情-查询员工列表")
    @ApiOperation(value = "选择成员", notes = "作者：连江伟")
    @PostMapping(UserUrlCenter.ROLE_INFO_SELECT_USER)
    public RestResponse<PageDTO<UserBaseInfo>> getSelectUserList(@RequestBody RoleUserReqDTO reqDTO) {
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.ROLE_INFO_SELECT_USER);
        final LoginBaseInfo loginInfo = LoginSessionUtil.getBaseLoginInfo();
        reqDTO.setLoginSystemType(loginInfo.getSystemType());
        if(StringUtils.isBlank(reqDTO.getCompanyCode())){
            reqDTO.setLoginUserId(loginInfo.obtainBaseInfo().getStaffId());
            reqDTO.setLoginCompanyId(loginInfo.obtainBelongCompanyId());
            reqDTO.setDataPermType(loginInfo.obtainSimpleDataPerm().getDataPermType());
            reqDTO.setDataCodeSet(loginInfo.obtainSimpleDataPerm().getDataCodeSet());
            //reqDTO.setCompanyCode(loginInfo.obtainBelongCompanyCode());
            //reqDTO.setCompanyId(loginInfo.obtainBelongCompanyId());
        }
        HashMap<String, Object> restParam = new HashMap<String, Object>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        RestResponse response = RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam,null);
        return response;
    }

    @PostMapping(UserUrlCenter.ROLE_INFO_HAVE_COUNT)
    @RequestFunction(functionName = "角色下成员数量")
    @ApiOperation(value = "角色下成员数量",notes = "作者：连江伟")
    @ApiImplicitParam(name = "roleCode",value = "角色编码",required = true)
    public RestResponse getCountOfRoleUser(String roleCode){
        Map<String,Object> restParam = new HashMap<>();
        restParam.put("roleCode",roleCode);
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.ROLE_INFO_HAVE_COUNT);
        RestResponse response = RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, restParam,null);
        return response;
    }

    @RequestFunction(functionName = "选择成员-查询角色员工编码列表")
    @ApiOperation(value = "角色成员编码", notes = "作者：连江伟")
    @PostMapping(UserUrlCenter.ROLE_INFO_STAFF_CODE)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "roleCode",value = "角色编码",required = true),
            @ApiImplicitParam(name = "companyCode",value = "所属企业编码",required = true),
            @ApiImplicitParam(name = "systemType",value = "所属平台编码",required = true),
    })
    public RestResponse getRoleUserCodeList(String roleCode,String companyCode,Byte systemType) {
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.ROLE_INFO_STAFF_CODE);
        final HashMap<String, Object> restParam = Maps.newHashMapWithExpectedSize(3);
        restParam.put("roleCode", roleCode);
        restParam.put("companyCode", companyCode);
        restParam.put("systemType", systemType);
        RestResponse response = RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, restParam,null);
        return response;
    }




    @PostMapping(UserUrlCenter.ROLE_INFO_DELETE_CUSTOM)
    @RequestFunction(functionName = "删除自定义角色-客户端")
    @ApiOperation(value = "角色管理-删除自定义角色-客户端",notes = "作者：吴林豪")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "roleId",value = "角色id",required = true),
    })
    public RestResponse changeStatus(@Verify(param = "roleId", rule = "required") Integer roleId){
        Map<String,Object> restParam = new HashMap<>();
        restParam.put("roleId",roleId);
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.ROLE_INFO_DELETE_CUSTOM);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, restParam,null);
    }
}
