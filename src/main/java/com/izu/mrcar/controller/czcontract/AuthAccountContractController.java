package com.izu.mrcar.controller.czcontract;

import com.google.common.collect.Maps;
import com.izu.business.config.BusinessRestLocator;
import com.izu.business.consts.resturl.MrcarBusinessRestMsgCenter;
import com.izu.business.dto.czcontract.input.AuthAccountContractInfoInputDTO;
import com.izu.business.dto.czcontract.output.AuthAccountContractDetailDTO;
import com.izu.business.dto.czcontract.output.AuthAccountContractInfoOutputDTO;
import com.izu.business.dto.mrcarUseContract.input.BillingInfoQueryReqDTO;
import com.izu.business.dto.mrcarUseContract.output.BillingInfoExportResDTO;
import com.izu.business.dto.mrcarUseContract.output.BillingInfoPageResDTO;
import com.izu.business.dto.provider.input.AuthAccountContractOpeInputDTO;
import com.izu.business.dto.provider.output.AuthAccountContractExportDTO;
import com.izu.business.dto.provider.output.AuthAccountContractOpeDTO;
import com.izu.business.dto.provider.output.AuthAccountContractOpeDetailDTO;
import com.izu.business.dto.provider.output.AuthAccountContractOpeExportDTO;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.doc.JrdApiDoc;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

@RestController
@RequestMapping("/authAccountContract")
@Slf4j
@Api(tags = "合同管理")
public class AuthAccountContractController {

    @PostMapping("/getList")
    @RequestFunction(functionName = "使用许可合同列表")
    @ApiOperation(value = "使用许可合同列表",notes = "作者：mapp")
    public RestResponse<PageDTO<AuthAccountContractInfoOutputDTO>> getList(@RequestBody AuthAccountContractOpeInputDTO param){
        ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
        param.setStaffId(clientLoginInfo.obtainBaseInfo().getStaffId());
        if(CollectionUtils.isEmpty(param.getCustomerCodeList())){
            Set<String> codeSet = clientLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet();
            param.setCustomerCodeList(new ArrayList<>(codeSet));
        }
        String url = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.PROVIDER_AUTH_ACCOUNT_CONTRACT_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, AuthAccountContractOpeDTO.class);
    }

    @GetMapping("/getDetail")
    @RequestFunction(functionName = "使用许可合同详情")
    @ApiOperation(value = "使用许可合同详情",notes = "作者：mapp")
    public RestResponse<AuthAccountContractOpeDetailDTO> getDetail(Integer id){
        String url = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.PROVIDER_AUTH_ACCOUNT_CONTRACT_DETAIL);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("id", id);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.GET, url, paramMap, null, AuthAccountContractOpeDetailDTO.class);
    }

    @PostMapping("/export")
    @JrdApiDoc(simpleDesc = "使用许可合同导出", author = "hhd", resDataClass = BillingInfoExportResDTO.class)
    @RequestFunction(functionName = "使用许可合同导出")
    @ExportExcelWeb(fileName = ExportExcelConstants.FILE_NAME_CONTRACT_LIST_EXPORT_INFO ,filePath = ExportExcelConstants.FILE_PATH,
            sheet = ExportExcelConstants.SHEET_NAME_CONTRACT_LIST_EXPORT_INFO, c = AuthAccountContractExportDTO.class)
    public PageDTO<AuthAccountContractExportDTO> export(@RequestBody AuthAccountContractOpeInputDTO reqDTO, IzuEasyExcelSession izuEasyExcelSession,
                                                        HttpServletRequest request, HttpServletResponse response) {
        ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
        reqDTO.setStaffId(clientLoginInfo.obtainBaseInfo().getStaffId());
        if(CollectionUtils.isEmpty(reqDTO.getCustomerCodeList())){
            Set<String> codeSet = clientLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet();
            reqDTO.setCustomerCodeList(new ArrayList<>(codeSet));
        }
        String url = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.PROVIDER_AUTH_ACCOUNT_CONTRACT_LIST);
        izuEasyExcelSession.setUserName(clientLoginInfo.obtainBaseInfo().getStaffName());
        reqDTO.setPage(izuEasyExcelSession.getPageNo());
        reqDTO.setPageSize(1000);

        final HashMap<String, Object> restParam = Maps.newHashMapWithExpectedSize(1);
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        RestResponse<PageDTO<AuthAccountContractOpeDTO>> res = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, restParam, null, AuthAccountContractOpeDTO.class);
        if(null!=res && res.isSuccess()){
            PageDTO<AuthAccountContractOpeDTO> pageDTO = res.getData();
            List<AuthAccountContractExportDTO> list = BeanUtil.copyList(pageDTO.getResult(),AuthAccountContractExportDTO.class);
            return new PageDTO<>(pageDTO.getPage(), pageDTO.getPageSize(), pageDTO.getTotal(), list);
        }else {
            return null;
        }
    }
}
