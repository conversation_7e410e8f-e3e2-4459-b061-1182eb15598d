package com.izu.mrcar.controller.czcontract;

import com.izu.business.config.BusinessRestLocator;
import com.izu.business.consts.resturl.MrcarBusinessRestMsgCenter;
import com.izu.business.dto.czcontract.input.RenewalBussContractInputDTO;
import com.izu.business.dto.czcontract.output.RenewalBussContractDTO;
import com.izu.business.dto.czcontract.output.RenewalBussContractDetailDTO;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.shiro.session.WebSessionUtil;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.LoginUser;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@RestController
@Api(tags = "合同管理")
@RequestMapping("/renewalBussContract")
public class RenewalBussContractController {

    @PostMapping("/getList")
    @RequestFunction(functionName = "续租协议分页列表")
    @ApiOperation(value = "续租协议分页列表")
    public RestResponse<PageDTO<RenewalBussContractDTO>> getList(@RequestBody RenewalBussContractInputDTO condition){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        if(StringUtils.isBlank(loginBaseInfo.obtainBelongCompanyCode())){
            return RestResponse.success(new PageDTO(1,10,0, Collections.emptyList()));
        }
        if(CollectionUtils.isEmpty(condition.getCustomerCodeList())){
            Set<String> codeSet = loginBaseInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet();
            condition.setCustomerCodeList(new ArrayList<>(codeSet));
        }
        String url = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.RENEWAL_BUSS_CONTRACT_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, condition);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, RenewalBussContractDTO.class);
    }

    @GetMapping("/getDetail")
    @ApiImplicitParam(name = "bussContractCode", value = "合同编号", required = true)
    @RequestFunction(functionName = "续租协议详情")
    @ApiOperation(value = "续租协议详情")
    public RestResponse<RenewalBussContractDetailDTO> getDetail(@Verify(param = "bussContractCode",rule="required")String bussContractCode ){
        String url = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.RENEWAL_BUSS_CONTRACT_DETAIL);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("bussContractCode", bussContractCode);
        RestResponse restResponse = RestClient.requestForObject(BaseHttpClient.HttpMethod.GET, url, paramMap, null, RenewalBussContractDetailDTO.class);
        if(restResponse.isSuccess()){
            RenewalBussContractDetailDTO data = (RenewalBussContractDetailDTO) restResponse.getData();
            data.setRenewalBussContractFileList(new ArrayList<>());
        }
        return restResponse;
    }
}
