package com.izu.mrcar.controller.czcontract;

import com.izu.business.config.BusinessRestLocator;
import com.izu.business.config.errorcode.BusinessErrorCode;
import com.izu.business.consts.resturl.MrcarBusinessRestMsgCenter;
import com.izu.business.dto.czcontract.input.BussContractAccountWayInputDTO;
import com.izu.business.dto.czcontract.input.BussContractInputDTO;
import com.izu.business.dto.czcontract.output.BussContractBillDaysDTO;
import com.izu.business.dto.czcontract.output.BussContractDTO;
import com.izu.business.dto.czcontract.output.BussContractDetailDTO;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.shiro.session.WebSessionUtil;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.LoginUser;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.stream.Collectors;

/**
* @Description: 长租业务合同
* @author: hxc
* @Date: 2022/10/24
**/
@RestController
@Api(tags = "合同管理")
@Slf4j
public class BussContractController {

    @Autowired
    @Qualifier("rest-client-pool")
    private ExecutorService executorService;

    /**
     * 长租业务合同分页列表查询
     * @param param
     * @return
     */
    @PostMapping(MrcarBusinessRestMsgCenter.BUSS_CONTRACT_PAGE_LIST)
    @RequestFunction(functionName = "业务合同分页列表")
    @ApiOperation(value = "业务合同分页列表",notes = "作者：贺新春")
    public RestResponse<PageDTO<BussContractDTO>> findContractPageList(@Valid @RequestBody BussContractInputDTO param){

        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        if(StringUtils.isBlank(loginBaseInfo.obtainBelongCompanyCode())){
            return RestResponse.success(new PageDTO(1,10,0, Collections.emptyList()));
        }
        if(CollectionUtils.isEmpty(param.getCustomerCodeList())){
            Set<String> codeSet = loginBaseInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet();
            param.setCustomerCodeList(new ArrayList<>(codeSet));
        }
        String url = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.BUSS_CONTRACT_PAGE_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, BussContractDTO.class);

    }

    /**
     * 长租业务合同详情
     * @param bussContractCode
     * @return
     */
    @GetMapping(MrcarBusinessRestMsgCenter.BUSS_CONTRACT_DETAIL)
    @ApiImplicitParam(name = "bussContractCode", value = "合同编号", required = true)
    @RequestFunction(functionName = "业务合同详情")
    @ApiOperation(value = "业务合同详情",notes = "作者：贺新春")
    public RestResponse<BussContractDetailDTO> findBussContractDetail(@Verify(param = "bussContractCode",rule="required")String bussContractCode) {

        BussContractDetailDTO bussContractDetailDTO = null;
        try{
            // 查询合同基本信息
            CompletableFuture<BussContractDetailDTO> contractDetailDTO = CompletableFuture.supplyAsync(() -> {
                String url = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.BUSS_CONTRACT_DETAIL);
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("bussContractCode", bussContractCode);
                RestResponse restResponse = RestClient.requestForObject(BaseHttpClient.HttpMethod.GET, url, paramMap, null, BussContractDetailDTO.class);
                return (BussContractDetailDTO)restResponse.getData();
            },executorService);

            // 查询订单信息
            CompletableFuture<List<BussContractDetailDTO.BussOrder>> bussOrderList = CompletableFuture.supplyAsync(() -> {
                String url = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.CONTRACT_ORDER_QUERY_BY_CONTRACT_CODE);
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("bussContractCode", bussContractCode);
                RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.GET, url, paramMap, null, BussContractDetailDTO.BussOrder.class);
                return (List<BussContractDetailDTO.BussOrder>)restResponse.getData();
            },executorService);

            // 查询账单信息
            CompletableFuture<List<BussContractDetailDTO.OrderBill>> orderBillList = CompletableFuture.supplyAsync(() -> {
                String url = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.BILL_INFO_LIST_QUERY_BY_CONTRACT_CODE);
                Map<String, Object> paramMap = new HashMap<>();
                paramMap.put("bussContractCode", bussContractCode);
                RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.GET, url, paramMap, null, BussContractDetailDTO.OrderBill.class);
                return (List<BussContractDetailDTO.OrderBill>)restResponse.getData();
            },executorService);

            // 汇总数据
            bussContractDetailDTO = CompletableFuture.allOf(contractDetailDTO,bussOrderList, orderBillList).thenApplyAsync((v) -> {
                try {
                    BussContractDetailDTO contractDetail = contractDetailDTO.get();
                    List<BussContractDetailDTO.BussOrder> bussOrders = bussOrderList.get();
                    List<BussContractDetailDTO.OrderBill> orderBills = orderBillList.get();
                    contractDetail.setBussOrderList(bussOrders);
                    if (bussOrders == null || orderBills == null){
                        contractDetail.setOrderBillList(orderBills);
                        return contractDetail;
                    }
                    Map<String, List<BussContractDetailDTO.BussOrder>> bussOrderMap = bussOrders.stream().collect(Collectors.groupingBy(BussContractDetailDTO.BussOrder::getOrderCode));
                    orderBills.forEach(orderBill -> {
                        List<BussContractDetailDTO.BussOrder> bussOrderL = bussOrderMap.get(orderBill.getOrderCode());
                        if (bussOrderL != null){
                            BussContractDetailDTO.BussOrder order = bussOrderL.get(0);
                            orderBill.setVehicleLicense(order.getVehicleLicense());
                            orderBill.setVehicleModelName(order.getVehicleModelName());
                            orderBill.setOrderStatus(order.getOrderStatus());
                            orderBill.setOrderStatusStr(order.getOrderStatusStr());
                        }
                    });
                    contractDetail.setOrderBillList(orderBills);
                    return contractDetail;
                } catch (Exception e) {
                    log.error("补充账单中的订单车辆信息异常",e);
                    Thread.currentThread().interrupt();
                    return null;
                }
            },executorService).get();

        }catch (Exception e){
            log.error("查询合同详情异常",e);
            Thread.currentThread().interrupt();
        }

        return bussContractDetailDTO==null?RestResponse.fail(BusinessErrorCode.CONTRACT_DETAIL_ERROR):RestResponse.success(bussContractDetailDTO);
    }

    /**
     * 长租业务合同出账方式修改
     * @param param
     * @return
     */
    @PostMapping(MrcarBusinessRestMsgCenter.BUSS_CONTRACT_MODIFY_ACCOUNT_WAY)
    @RequestFunction(functionName = "业务合同出账方式修改")
    @ApiOperation(value = "业务合同出账方式修改",notes = "作者：贺新春")
    public RestResponse<Boolean> accountWay(@Valid @RequestBody BussContractAccountWayInputDTO param){

        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        param.setLoginUserId(loginBaseInfo.obtainBaseInfo().getStaffId());
        param.setLoginUserName(loginBaseInfo.obtainBaseInfo().getStaffName());
        String url = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.BUSS_CONTRACT_MODIFY_ACCOUNT_WAY);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);

    }

    /**
     * 每月固定日出账生成账单周期示例
     * @param param
     * @return
     */
    @PostMapping(MrcarBusinessRestMsgCenter.BUSS_CONTRACT_BILL_DAYS_SAMPLE)
    @RequestFunction(functionName = "业务合同每月固定日账期示例生成")
    @ApiOperation(value = "业务合同每月固定日账期示例生成",notes = "作者：贺新春")
    public RestResponse<BussContractBillDaysDTO> billDaysSample(@Valid @RequestBody BussContractAccountWayInputDTO param){

        String url = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.BUSS_CONTRACT_BILL_DAYS_SAMPLE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null,BussContractBillDaysDTO.class);

    }



}
