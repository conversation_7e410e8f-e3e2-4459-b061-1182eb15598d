package com.izu.mrcar.controller.czcontract;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.izu.erplease.dto.output.contract.BussContractDetailDTO;
import com.izu.carasset.CarAssetRestLocator;
import com.izu.carasset.CarAssetRestUrl;
import com.izu.carasset.dto.input.StaffQueryDTO;
import com.izu.carasset.dto.output.AreaExDTO;
import com.izu.carasset.dto.output.IzuStaffDTO;
import com.izu.carasset.dto.output.IzuStaffDeptDTO;
import com.izu.crm.CrmRestLocator;
import com.izu.crm.CrmRestUrl;
import com.izu.crm.dto.input.ContractQueryParam;
import com.izu.crm.dto.input.ContractReletQueryDTO;
import com.izu.crm.dto.output.*;
import com.izu.crm.enums.ContractTypeEnum;
import com.izu.crm.errcode.CrmContractErrorCode;
import com.izu.erplease.ErpLeaseRestLocator;
import com.izu.erplease.ErpLeaseRestUrl;
import com.izu.erplease.dto.input.order.QueryOrderInputDTO;
import com.izu.erplease.dto.output.order.OrderOutputDTO;
import com.izu.framework.doc.JrdApiDoc;
import com.izu.framework.doc.JrdApiParamDoc;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;

import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.shiro.session.WebSessionUtil;

import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.LoginUser;

import com.izu.user.dto.staff.pc.LoginBaseInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


import java.util.*;


/**
 * 长租合同相关接口
 * <AUTHOR>
 * @date 2021年7月17日13:46:52
 */
@RestController
@RequestMapping("")
public class ContractController {

    private static final Logger logger = LoggerFactory.getLogger(ContractController.class);

    /**查询综合管理系统的员工信息**/
    @RequestMapping("/userManage/getfirstDepartmentStaff")
    public RestResponse getfirstDepartmentStaff(String structCode,String userName,String userStates,String belongCityCode){
        Map<String, Object> httpParams = new HashMap<>();
        httpParams.put("structCode", structCode);
        httpParams.put("userName", userName);
        httpParams.put("userStates", userStates);
        httpParams.put("belongCityCode", belongCityCode);
        String url = new CarAssetRestLocator().getRestUrl(CarAssetRestUrl.IZUSTAFF_FIRST_DEPARTMENT_STAFF);
        RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, url, httpParams, null, IzuStaffDeptDTO.class);
        return restResponse;
    }

    /**查询综合管理系统的员工信息**/
    @RequestMapping("/userManage/selectStaffListByParam")
    public RestResponse selectStaffListByParam(String userName){
        StaffQueryDTO staffQueryDTO = new StaffQueryDTO();
        staffQueryDTO.setUserName(userName);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, staffQueryDTO);
        String url = new CarAssetRestLocator().getRestUrl(CarAssetRestUrl.STAFF_LIST);
        RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, IzuStaffDTO.class);
        return restResponse;
    }

    /**
     * 长租业务合同列表查询接口，按照登录人所在客户企业编码或者名称查询
     * @param param
     * @return
     */
    @RequestMapping("/crm/contract/findContractPageList")
    public RestResponse findContractPageList(
            @RequestBody ContractQueryParam param
    ){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        if(null == loginBaseInfo){
            return RestResponse.fail(CrmContractErrorCode.PARAM_ERR);
        }
        //BeanUtils.copyProperties(loginUser,param);
        //param.setCurrentLoginId(String.valueOf(loginUser.getId()));
        if(StringUtils.isBlank(loginBaseInfo.obtainBelongCompanyCode())){
            return RestResponse.success(new PageDTO(1,10,0,Collections.emptyList()));
        }
        param.setCustomerCode(loginBaseInfo.obtainBelongCompanyCode());

        String url = new ErpLeaseRestLocator().getRestUrl(ErpLeaseRestUrl.FIND_BUSS_CONTRACT_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage
                (BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, JSONObject.class);

    }

    /**
     * 长租业务合同详情接口，按合同编号查询
     * @param bussContractCode
     * @param contractType
     * @param pageSource
     * @param orderCode
     * @return
     */
    @RequestMapping("/bussContract/findBussContractDetail")
    @RequestFunction(functionName = "业务合同-查询合同列表")
    public RestResponse findBussContractDetail(
            @Verify(param="bussContractCode",rule = "required") String bussContractCode,
            @JrdApiParamDoc(desc = "合同类型") Integer contractType,
            @JrdApiParamDoc(desc = "页面类型0：合同列表查合同详情，1：非合同列表查合同包含订单，账单，交接单，结算") Integer pageSource,
            @JrdApiParamDoc(desc = "pageSource=1传订单号") String orderCode){
        String restUrl = new ErpLeaseRestLocator().getRestUrl(ErpLeaseRestUrl.FIND_BUSS_CONTRACT_DETAIL);
        Map<String, Object> param = new HashMap<>(4);
        param.put("bussContractCode", bussContractCode);
        param.put("contractType", contractType);
        param.put("pageSource", pageSource);
        param.put("orderCode", orderCode);
        RestResponse restResponse = RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, param, null, BussContractDetailDTO.class);
        if(restResponse.isSuccess()){
            BussContractDetailDTO data = (BussContractDetailDTO)restResponse.getData();
            //不给客户展示回传合同的信息
            data.setBackUrlList(new ArrayList<>());
            data.setPreUrlList(new ArrayList<>());
        }
        return restResponse;
    }

    /**
     * 长租续租协议列表查询接口
     * @param contractReletQueryDTO
     * @return
     */
    @RequestMapping("/crm/contract/findContractReletList")
    @RequestFunction(functionName = "续租协议-查询列表")
    public RestResponse queryReletContractList(@RequestBody ContractReletQueryDTO contractReletQueryDTO){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        if(null == loginBaseInfo){
            return RestResponse.fail(CrmContractErrorCode.PARAM_ERR);
        }
/*        BeanUtils.copyProperties(loginUser,contractReletQueryDTO);
        contractReletQueryDTO.setCurrentLoginId(String.valueOf(loginUser.getId()));*/
        if(StringUtils.isBlank(loginBaseInfo.obtainBelongCompanyCode())){
            return RestResponse.success(new PageDTO(1,10,0,Collections.emptyList()));
        }
        contractReletQueryDTO.setCustomerCode(loginBaseInfo.obtainBelongCompanyCode());
        String url = new CrmRestLocator().getRestUrl(CrmRestUrl.CONTRACT_RELET_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, contractReletQueryDTO);
        logger.info("-queryReletContractList查询已经签署的代垫协议列表 入参:{}", JSON.toJSONString(contractReletQueryDTO));
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, JSONObject.class);
    }

    /**
     * 长租续租协议详情查询接口
     * @param param
     * @return
     */
    @RequestMapping("/crm/contract/findContractDetail")
    @RequestFunction(functionName = "续租协议-查询详情")
    public RestResponse findContractDetail(
            @RequestBody ContractQueryParam param
    ){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        if(null == loginBaseInfo){
            return RestResponse.fail(CrmContractErrorCode.PARAM_ERR);
        }
        //BeanUtils.copyProperties(loginUser,param);
        String url = new CrmRestLocator().getRestUrl(CrmRestUrl.CONTRACT_QUERY_DETAIL);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        if(param.getContractType().equals(ContractTypeEnum.STANDARD_C.value())  || param.getContractType().equals(ContractTypeEnum.OFF_STANDARD_C.value()) ){
            //长租业务合同
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, BussContractDTO.class);

        }else if(param.getContractType().equals(ContractTypeEnum.FIX_ATTACH.value())
                || param.getContractType().equals(ContractTypeEnum.FIX_ATTACH_OFF.value())
                || param.getContractType().equals(ContractTypeEnum.FIX_ATTACH_DRIVER.value())
                || param.getContractType().equals(ContractTypeEnum.FIX_ATTACH_DRIVER_OFF.value()) ){
            //增值合同

            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, AttachContractDTO.class);

        }else if(param.getContractType().equals(ContractTypeEnum.COMMON_C.value())
                || param.getContractType().equals(ContractTypeEnum.BUYER_C.value())
        ){
            //通用/采购合同
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, CommonAssetContractDTO.class);

        }else if(param.getContractType().equals(ContractTypeEnum.RELET_D.value())  || param.getContractType().equals(ContractTypeEnum.RELET_D_OFF.value()) ){
            //续租合同
            RestResponse restResponse = RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, ReletContractDTO.class);
            return restResponse;
        }else if(param.getContractType().equals(ContractTypeEnum.STANDARD_F.value())  || param.getContractType().equals(ContractTypeEnum.OFF_STANDARD_F.value())  ){
            //框架合同
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, FrameContractDTO.class);

        }else{
            return RestResponse.fail(CrmContractErrorCode.CONTRACT_STATUS_NONCONF);
        }
    }

    /**
     * 长租订单管理订单列表查询接口
     * @param queryOrder
     * @return
     */
    @RequestMapping("/erplease/order/selectOrderListByPage")
    //@RequiresPermissions(value = {"CZORDER_LIST", "long_rent_order_search"}, logical = Logical.OR)
    @JrdApiDoc(simpleDesc = "查询订单列表" ,detailDesc = "分页查询订单列表" ,author = "jiangxi",resDataClass = PageDTO.class)
    public RestResponse selectOrderListByPage(@RequestBody  @JrdApiParamDoc(desc = "分页查询付款申请列表", example = "") QueryOrderInputDTO queryOrder){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
/*        queryOrder.setLeaseDataPermissionScope(ssoLoginUser.getLeaseDataPermissionScope());
        queryOrder.setLoginUserId(ssoLoginUser.getId());
        queryOrder.setLoginUserDefaultDepartmentId(ssoLoginUser.getDefaultDepartment().getStructCode());
        queryOrder.setLeaseDataPermissionAreaCodes(ssoLoginUser.getLeaseDataPermissionAreaCodes());
        queryOrder.setLeaseDataPermissionStructCode(ssoLoginUser.getLeaseDataPermissionStructCode());*/

        Map<String, Object> params = new HashMap<String, Object>();
        if(StringUtils.isBlank(loginBaseInfo.obtainBelongCompanyCode())){
            return RestResponse.success(new PageDTO(1,10,0,Collections.emptyList()));
        }
        queryOrder.setCustomerCode(loginBaseInfo.obtainBelongCompanyCode());
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, queryOrder);
        String url = new ErpLeaseRestLocator().getRestUrl(ErpLeaseRestUrl.SELECT_ORDER_LIST_BY_PAGE);
        RestResponse response = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, params, null, OrderOutputDTO.class);
        logger.info("分页查询订单列表，params={}", params.toString());
        if (response.getCode() == RestErrorCode.SUCCESS && Objects.nonNull(response.getData())) {
            PageDTO pageDTO = (PageDTO) response.getData();
            List<OrderOutputDTO> orderOutputDTOList = pageDTO.getResult();
            //orderOutputDTOList.forEach(e -> setButtonShows(e));
            pageDTO.setResult(orderOutputDTOList);
        }
        return response;
    }

    /**
     * 长租订单详情接口
     * @param queryOrder
     * @return
     */
    @RequestMapping("/erplease/order/findOrderDetail")
    @JrdApiDoc(simpleDesc = "查询订单详情信息", author = "jiangxi", resDataClass = JSONObject.class)
    public RestResponse findOrderDetail(@RequestBody  @JrdApiParamDoc(desc = "查询订单详情信息", example = "") QueryOrderInputDTO queryOrder){
        Map<String, Object> params = new HashMap<String, Object>();
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, queryOrder);
        String url = new ErpLeaseRestLocator().getRestUrl(ErpLeaseRestUrl.FIND_ORDER_DETAIL);
        RestResponse response = RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, params, null, JSONObject.class);
//        logger.info("查询订单详情信，params={}，response={}", params.toString(), response!=null ? JSON.toJSONString(response) : "");
        return response;
    }

    @RequestMapping("/crm/signingSubject/queryAll")
    @JrdApiDoc(simpleDesc = "查询所有签约主体信息", detailDesc = "查询所有签约主体信息", author = "linxin", resDataClass = CrmSigningSubjectInfoDTO.class)
    public RestResponse queryAllSigningSubject(@RequestParam("signSubjectName") String signSubjectName,
                                               @RequestParam(value = "accountType", required = false) Integer accountType) {
        String url = new CrmRestLocator().getRestUrl(CrmRestUrl.SELECT_ALL_SIGN_SUBJECT);
        Map<String, Object> param = new HashMap<>(2);
        param.put("signSubjectName", signSubjectName);
        param.put("accountType", accountType);
        // 设置登录人信息
        // PermissionInfomationUtil.setLoginUserInfo(param);
        RestResponse response = RestClient.requestForObject(BaseHttpClient.HttpMethod.GET, url, param, null, null);
        return response;
    }

    /**
     * 根据运营纬度查询城市列表
     * @param areaName
     * @return
     */
    @RequestMapping("/getAreaByBussCode")
    public RestResponse getAreaByBussCode(@JrdApiParamDoc(desc = "地区名称", example = "11") String areaName,Boolean isData,Boolean isLeaseData,Boolean isBiData,Boolean isAccountSetCityCode,Boolean isHailing){
        String url = new CarAssetRestLocator().getRestUrl(CarAssetRestUrl.AREA_GETAREABYBUSSCODE);
        Map<String, Object> httpParams = new HashMap<>();
        httpParams.put("areaName", areaName);
        httpParams.put("isAccountSetCityCode", isAccountSetCityCode);
        RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, url, httpParams, null, AreaExDTO.class);
        if(restResponse.isSuccess()&&restResponse.getData()!=null){
            List<AreaExDTO> areaList=(List<AreaExDTO>)restResponse.getData();
            if(areaList!=null&&areaList.size()>0){
                return RestResponse.success(areaList);
            }
        }
        return restResponse;
    }
}
