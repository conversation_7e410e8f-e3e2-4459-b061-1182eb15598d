package com.izu.mrcar.controller.czcontract;


import com.izu.business.config.BusinessRestLocator;
import com.izu.business.consts.resturl.MrcarBusinessRestMsgCenter;
import com.izu.business.dto.czcontract.input.ContractOrderQueryInputDTO;
import com.izu.business.dto.czcontract.output.ContractOrderDTO;
import com.izu.business.dto.czcontract.output.ContractOrderDetailDTO;
import com.izu.business.dto.input.VehicleQueryDTO;
import com.izu.business.dto.output.VehicleInfoDetailDTO;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.shiro.session.WebSessionUtil;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.LoginUser;
import com.izu.user.dto.staff.pc.DataPerm;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.enums.perm.ClientDataPermTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
* @Description: 长租合同订单管理
* @author: ljw
* @Date: 2022/10/24
**/
@RestController
@RequestMapping("")
@Api(tags = "合同管理")
public class ContractOrderController {

    private static final Logger logger = LoggerFactory.getLogger(ContractOrderController.class);

    /**
     * 长租合同订单分页列表
     * @param param
     * @return
     */
    @PostMapping(MrcarBusinessRestMsgCenter.CONTRACT_ORDER_LIST_PAGE)
    @RequestFunction(functionName = "长租合同订单分页列表")
    @ApiOperation(value = "合同订单分页列表")
    public RestResponse<PageDTO<ContractOrderDTO>> findContractOrderPageList(@RequestBody ContractOrderQueryInputDTO param){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        if(StringUtils.isBlank(loginBaseInfo.obtainBelongCompanyCode())){
            return RestResponse.success(new PageDTO(1,10,0, Collections.emptyList()));
        }
        DataPerm dataPerm = loginBaseInfo.obtainDataPerm();
        if(!Objects.equals(dataPerm.getDataPermType(), ClientDataPermTypeEnum.SELF_COMPANY.getType())){
            // 本企业才有权限查询，非本企业 查询展示为0
            return RestResponse.success(new PageDTO(1,10,0, Collections.emptyList()));
        }
        if(CollectionUtils.isEmpty(param.getCustomerCodeList())){
            Set<String> codeSet = loginBaseInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet();
            param.setCustomerCodeList(new ArrayList<>(codeSet));
        }
        String url = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.CONTRACT_ORDER_LIST_PAGE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, ContractOrderDTO.class);

    }

    /**
     * 长租合同订单详情
     * @param orderCode
     * @return
     */
    @GetMapping(MrcarBusinessRestMsgCenter.CONTRACT_ORDER_DETAIL)
    @ApiImplicitParam(name = "orderCode", value = "订单编号", required = true)
    @RequestFunction(functionName = "长租合同订单详情")
    @ApiOperation(value = "合同订单详情")
    public RestResponse<ContractOrderDetailDTO> findBussContractOrderDetail(String orderCode){
        String url = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.CONTRACT_ORDER_DETAIL);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("orderCode", orderCode);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, url, paramMap, null, ContractOrderDetailDTO.class);
    }

    @PostMapping(MrcarBusinessRestMsgCenter.VEHICLE_DETAIL)
    @RequestFunction(functionName = "合同订单详情跳转车辆详情")
    @ApiOperation(value = "合同订单详情跳转车辆详情")
    public RestResponse<VehicleInfoDetailDTO> getVehicleDetailInfo(@RequestBody VehicleQueryDTO param){
        String url = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.VEHICLE_DETAIL);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, VehicleInfoDetailDTO.class);

    }

}
