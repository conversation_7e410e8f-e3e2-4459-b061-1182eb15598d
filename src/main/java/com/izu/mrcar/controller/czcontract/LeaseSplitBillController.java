package com.izu.mrcar.controller.czcontract;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.hutool.core.date.DateUtil;
import com.izu.business.config.BusinessRestLocator;
import com.izu.business.consts.resturl.MrcarBusinessRestMsgCenter;
import com.izu.business.dto.czcontract.input.BillInfoExcelInputDTO;
import com.izu.business.dto.czcontract.input.BillInfoListInputDTO;
import com.izu.business.dto.czcontract.output.BillExcelInfoDTO;
import com.izu.business.dto.czcontract.output.BillInfoDTO;
import com.izu.carasset.CarAssetRestLocator;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.controller.export.BillInfoExport;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.shiro.session.WebSessionUtil;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

@RestController
@RequestMapping("/leaseSplitBill")
@Api(tags = "合同管理")
public class LeaseSplitBillController {

    private static final Logger logger = LoggerFactory.getLogger(LeaseSplitBillController.class);

    //查询车型列表并且返回上级信息
    public static final String VEHICLE_MODEL_LIST="/vehicleBrand/getVehicleModeList.json";

    @PostMapping("/getList")
    @RequestFunction(functionName = "月租金账单列表分页查询")
    @ApiOperation(value = "月租金账单列表")
    public RestResponse<PageDTO<BillInfoDTO>> getList(@RequestBody BillInfoListInputDTO param){

        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        if(StringUtils.isBlank(loginBaseInfo.obtainBelongCompanyCode())){
            return RestResponse.success(new PageDTO(1,10,0, Collections.emptyList()));
        }
        if(CollectionUtils.isEmpty(param.getCustomerCodeList())){
            Set<String> codeSet = loginBaseInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet();
            param.setCustomerCodeList(new ArrayList<>(codeSet));
        }
        String url = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.BILL_INFO_LIST_INFO);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, BillInfoDTO.class);

    }

    @PostMapping("/export")
    @RequestFunction(functionName = "月租金账单列表导出")
    @ApiOperation(value = "月租金账单列表导出")
    public RestResponse export(@RequestBody BillInfoExcelInputDTO param, HttpServletResponse response){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        if(StringUtils.isBlank(loginBaseInfo.obtainBelongCompanyCode())){
            return RestResponse.success(new PageDTO(1,10,0, Collections.emptyList()));
        }
        if(CollectionUtils.isEmpty(param.getCustomerCodeList())){
            Set<String> codeSet = loginBaseInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet();
            param.setCustomerCodeList(new ArrayList<>(codeSet));
        }
        param.setPage(1);
        param.setPageSize(1000);
        String url = new BusinessRestLocator().getRestUrl(MrcarBusinessRestMsgCenter.BILL_EXCEL_LIST_INFO);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, BillExcelInfoDTO.class);
        int totalPage = 0;
        List<BillExcelInfoDTO> list = new ArrayList<>();
        if(restResponse !=null && restResponse.isSuccess()){
            PageDTO pageDTO = (PageDTO) restResponse.getData();
            long total =  pageDTO.getTotal();
            if(total <= 0){
                return RestResponse.fail(ErrorCode.NULL_DATA_ERROR);
            }
            if(total > 50000){
                return RestResponse.fail(ErrorCode.DATA_TO_MORE);
            }
            totalPage = pageDTO.getPages();
            list.addAll(pageDTO.getResult());
            if(totalPage > 2){
                for(int i=2;i<totalPage;i++){
                    BillInfoExcelInputDTO pageParam = BeanUtil.copyObject(param,BillInfoExcelInputDTO.class);
                    pageParam.setPage(i);
                    Map<String, Object> tempMap = new HashMap<>();
                    paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, pageParam);
                    RestResponse pageRestResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, tempMap, null, BillExcelInfoDTO.class);
                    if(pageRestResponse!=null && pageRestResponse.isSuccess()){
                        PageDTO pageAddDto = (PageDTO) pageRestResponse.getData();
                        list.addAll(pageAddDto.getResult());
                    }
                }
            }
            List<BillInfoExport> exportList = new ArrayList<>(list.size());
            list.forEach(item->{
                BillInfoExport excelDto = BeanUtil.copyObject(item,BillInfoExport.class);
                excelDto.setBillStageStr(item.getBillStage() + "/" + item.getBillStageTotal());
                excelDto.setBillStartDate(DateUtil.formatDate(item.getBillStartDate()));
                excelDto.setBillEndDate(DateUtil.formatDate(item.getBillEndDate()));
                exportList.add(excelDto);
            });

            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(), BillInfoExport.class, exportList);
            String fileName = "月租金账单信息_"+new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
            workbook.setSheetName(0, fileName);
            try (OutputStream outputStream = response.getOutputStream()){
                response.setContentType("application/vnd.ms-excel;charset=utf-8");
                fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
                response.setHeader("Content-disposition",  "attachment;filename*=utf-8''"  + fileName + ".xls");
                workbook.write(outputStream);
            }catch (Exception e){
                logger.error("导出Excel异常");
            }
        }else{

        }
        return null;
    }

    @PostMapping("/getVehicleModeList")
    @RequestFunction(functionName = "车型模糊查询")
    @ApiOperation(value = "车型模糊查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name="vehicleModelName",value="输入的值",required=true,paramType="form"),
            @ApiImplicitParam(name="isLimit",value="是否显示10条",required=true,paramType="form")
    })
    public RestResponse getVehicleModeList(@RequestParam String vehicleModelName,
                                           @RequestParam Boolean isLimit ){
        String restUrl = new CarAssetRestLocator().getRestUrl(VEHICLE_MODEL_LIST);
        Map<String, Object> paramMap = new HashMap<>(2);
        paramMap.put("vehicleModelName", vehicleModelName);
        paramMap.put("isLimit", isLimit);
        RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, paramMap, null, Object.class);
        return restResponse;
    }
}
