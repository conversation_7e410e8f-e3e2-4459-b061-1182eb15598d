package com.izu.mrcar.controller;

import com.alibaba.fastjson.JSON;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import org.apache.commons.lang.StringUtils;

import java.util.Map;

public class MrCarHttpPostBody {
    /**请求结果为对象**/
    public static <T> RestResponse requestBodyForObject(final String url, final Object requestBody, Map<String, String> httpHeaders, Class<T> responseObjectClass ) {
        String json = BaseHttpClient.postBody(url, requestBody, null);
        if(StringUtils.isBlank(json)) {
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
        RestResponse response = JSON.parseObject(json, RestResponse.class);
        if(response!=null && response.getData()!=null) {
            Object data = null;
            if( responseObjectClass!=null && responseObjectClass.equals(String.class) ) {
                data = response.getData().toString(); //如果是字符串，fastjson会报错的，所以要特殊处理
            }else {
                data = JSON.parseObject(response.getData().toString(), responseObjectClass);
            }
            response.setData(data);
        }
        return response;
    }
}
