package com.izu.mrcar.controller;

import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.config.RestUrlConfig;
import com.izu.mrcar.service.user.SessionClearService;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019-11-17 21:41
 */
@Deprecated
@RestController
public class CompanyPermissionController {

    private static final Logger logger = LoggerFactory.getLogger(CompanyPermissionController.class);


    @Autowired
    private SessionClearService sessionClearService;


    @Deprecated
    @RequestMapping("/companyPermission/getByCompany")
    @RequestFunction(functionName = "企业权限查询")
    public RestResponse getCompanyPermissionMenu(@Verify(param = "companyId",rule = "required") String companyId){
        ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
        Map<String, Object> params = new HashMap<>();
        params.put("companyId", companyId);
        params.put("staffId", clientLoginInfo.getBaseInfo().getStaffId());
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, RestUrlConfig.getConfig().getUserCoreUrl() + UserUrlCenter.GET_COMPANY_PERMISSION, params, null);
    }

    @Deprecated
    @RequestMapping("/companyPermission/update")
    @RequiresPermissions(value = "company_permission")
    @RequestFunction(functionName = "企业权限保存")
    public RestResponse updateCompanyPermissionMenu(@Verify(param = "companyId", rule = "required") String companyId,
                                                    @Verify(param = "permissionIds", rule = "required") String permissionIds){
        Map<String, Object> params = new HashMap<>();
        params.put("companyId", companyId);
        params.put("permissionIds", permissionIds);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, RestUrlConfig.getConfig().getUserCoreUrl() + UserUrlCenter.UPDATE_COMPANY_PERMISSION, params, null);
    }
}
