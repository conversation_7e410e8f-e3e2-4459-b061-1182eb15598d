package com.izu.mrcar.controller.insurance;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.alibaba.nacos.common.utils.JacksonUtils;
import com.google.common.collect.Maps;
import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.CarInfoDTO;
import com.izu.asset.dto.param.CarInfoForVheList;
import com.izu.asset.dto.param.VehicleSerialNoReqQTO;
import com.izu.asset.dto.vehicle.CarInfoBaseDTO;
import com.izu.asset.dto.vehicle.MrcarInsurancePolicyDTO;
import com.izu.asset.dto.vehicle.MrcarInsurancePolicyExportDTO;
import com.izu.asset.dto.vehicle.MrcarInsurancePolicyParamDTO;
import com.izu.carasset.CarAssetRestLocator;
import com.izu.carasset.CarAssetRestUrl;
import com.izu.carasset.dto.input.insurance.InsuranceAttachmentDTO;
import com.izu.carasset.dto.input.insurance.InsuranceAttachmentQueryDTO;
import com.izu.carasset.dto.input.insurance.InsurancePolicyParam;
import com.izu.carasset.dto.input.insurance.InsurancePolicySaveDTO;
import com.izu.carasset.dto.output.insurance.InsurancePolicyDTO;
import com.izu.carasset.dto.output.insurance.InsuranceUnderwriterDTO;
import com.izu.carasset.enums.insurance.InsuranceAttachmentEnum;
import com.izu.carasset.enums.insurance.InsuranceTypeEnum;
import com.izu.carasset.enums.insurance.PolicyStatusEnum;
import com.izu.carasset.util.StringUtils;
import com.izu.crm.CrmRestLocator;
import com.izu.crm.CrmRestUrl;
import com.izu.crm.dto.output.*;
import com.izu.crm.enums.SupplierTypeEnum;
import com.izu.disposal.common.dto.carSaleHandoverTransfer.output.CarInfo;
import com.izu.framework.doc.JrdApiDoc;
import com.izu.framework.doc.JrdApiParamDoc;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.RandomStringUtil;
import com.izu.framework.web.util.ZipFileUtil;
import com.izu.framework.web.util.excel.Column;
import com.izu.framework.web.util.excel.ExSheet;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.controller.AbstractExcelUploadController;
import com.izu.mrcar.utils.DataPermUtil;
import com.izu.mrcar.utils.PdfZipUtil;
import com.izu.user.dto.company.CompanyDTO;
import com.izu.user.enums.perm.ClientDataPermTypeEnum;
import com.izu.user.restApi.CompanyApi;
import com.izu.user.util.ObjectTransferUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 综合管理平台保险相关接口
 * <AUTHOR>
 * @date 2021年7月17日13:46:52
 */
@RestController
@RequestMapping("/insurancePolicy")
@Api(tags = "综合管理平台保险相关接口")
public class InsurancePolicyController extends AbstractExcelUploadController<InsurancePolicySaveDTO, AbstractExcelUploadController.ExcelUploadResult> {

    private static final Logger logger = LoggerFactory.getLogger(InsurancePolicyController.class);

    @Value("${mrcar-asset-core.host.url}")
    private String ASSET_HOST_URL;

    @Autowired
    private PdfZipUtil pdfZipUtil;

    /**
     * 查询该企业下所有的首汽车辆编号集合，用于查看综合平台的维保和保险等信息
     * @return
     */
    public List<CarInfoDTO> selectVehicleSerialNoListByCompanyId(VehicleSerialNoReqQTO dto) {
        DataPermUtil.putDataPerm(dto);
        Map<String, Object> paramMap = new HashMap<>();
        List<CarInfoDTO> carLicenseList = null;
        dto.setLoginCompanyId(dto.getCompanyId());
        dto.setCompanyId(null);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        RestResponse response = RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, ASSET_HOST_URL+"/car/selectVehicleSerialNoListByCompanyId", paramMap, null, CarInfoDTO.class);
        if(null!=response && response.isSuccess()){
            if(null!=response.getData()){
                carLicenseList = (List<CarInfoDTO>) response.getData();
            }
        }
        return carLicenseList;
    }
    /**
     * 根据车牌号查询车辆相关信息
     * @return
     */
    public List<CarInfoDTO> getCarInfoByVehicleLicenseList(List<String> vehicleLicenseList) {
        Map<String, Object> paramMap = new HashMap<>();
        List<CarInfoDTO> carLicenseList = null;
        CarInfoForVheList carInfoForVheList = new CarInfoForVheList();
        carInfoForVheList.setVehicleLicenseList(vehicleLicenseList);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, carInfoForVheList);
        RestResponse response = RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, ASSET_HOST_URL+"/car/getCarInfoByVehicleLicenseList", paramMap, null, CarInfoDTO.class);
        if(null!=response && response.isSuccess()){
            if(null!=response.getData()){
                carLicenseList = (List<CarInfoDTO>) response.getData();
            }
        }
        return carLicenseList;
    }


    /**
     * 保单列表(交强险，商业险)
     *
     * @return
     */
    @RequestMapping("/mandatory/selectPolicyList")
    @JrdApiDoc(simpleDesc = "保单列表信息查询", author = "quchuanming", resDataClass = InsurancePolicyDTO.class)
    @RequestFunction(functionName = "保单列表查询")
    public RestResponse selectPolicyList(@RequestBody MrcarInsurancePolicyParamDTO params) {
        VehicleSerialNoReqQTO dto = new VehicleSerialNoReqQTO().setVehicleLicense(params.getVehicleLicense());
        dto.setCityCode(params.getCityCode());
        dto.setStructId(params.getStructId());
        dto.setCompanyIds(params.getCompanyIds());
        List<CarInfoDTO> carInfoDTOs = selectVehicleSerialNoListByCompanyId(dto);
        Map<Integer, CompanyDTO> finalCompanyInfoMap = getIntegerCompanyDTOMap(carInfoDTOs);
        if(CollectionUtil.isNotEmpty(carInfoDTOs)){
            Map<String, CarInfoDTO> carInfoMap = carInfoDTOs.stream().collect(Collectors.toMap(CarInfoDTO::getVehicleLicense, e -> e, (v1, v2) -> v1));
            List<String> vehicleSerialNoList = carInfoDTOs.stream().map(CarInfoDTO::getVehicleSerialNoNewSystem).collect(Collectors.toList());
            params.setVehicleSerialNoList(vehicleSerialNoList);
            String restUrl = new CarAssetRestLocator().getRestUrl(CarAssetRestUrl.INSURANCE_SELECT_LIST_POLICY);
            Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
            paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, params);
            RestResponse response = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, MrcarInsurancePolicyDTO.class);
            if (response.isSuccess()) {
                List<MrcarInsurancePolicyDTO> list = ObjectTransferUtil.transferRestResponseToList(response);
                list.forEach(e -> {
                    CarInfoDTO carInfoDTO = carInfoMap.get(e.getVehicleLicense());
                    if (carInfoDTO != null) {
                        e.setBelongCityName(carInfoDTO.getBelongCityName());
                        e.setStructName(carInfoDTO.getStructName());
                        CompanyDTO companyDTO = finalCompanyInfoMap.get(carInfoDTO.getCompanyId());
                        if (companyDTO != null) {
                            e.setCompanyName(companyDTO.getCompanyName());
                        }
                    }
                });
            }
            return response;
        }else {
            //如果该企业下一辆车都没有，那么返回空对象
            return RestResponse.success(new PageDTO(1, 10, 0, Collections.emptyList()));
        }

    }
    /**
     * 下载zip保单列表(交强险，商业险)
     *
     * @return
     */
    @RequestMapping("/mandatory/downloadPolicyPDF")
    @JrdApiDoc(simpleDesc = "下载zip保单列表", author = "dingweibing", resDataClass = InsurancePolicyDTO.class)
    @RequestFunction(functionName = "下载zip保单列表")
    @ApiOperation(value = "下载zip保单列表-客户端",notes = "作者：dingweibing")
    public void downloadPolicyPDF(@RequestBody MrcarInsurancePolicyParamDTO params, HttpServletResponse resp,HttpServletRequest request) throws IOException{
        VehicleSerialNoReqQTO dto = new VehicleSerialNoReqQTO().setVehicleLicense(params.getVehicleLicense());
        dto.setCityCode(params.getCityCode());
        dto.setStructId(params.getStructId());
        dto.setCompanyIds(params.getCompanyIds());
        List<CarInfoDTO> carInfoDTOs = selectVehicleSerialNoListByCompanyId(dto);
        if(CollectionUtil.isNotEmpty(carInfoDTOs)){
            Map<String, CarInfoDTO> carInfoMap = carInfoDTOs.stream().collect(Collectors.toMap(CarInfoDTO::getVehicleLicense, e -> e, (v1, v2) -> v1));
            List<String> vehicleSerialNoList = carInfoDTOs.stream().map(CarInfoDTO::getVehicleSerialNoNewSystem).collect(Collectors.toList());
            params.setVehicleSerialNoList(vehicleSerialNoList);
            String restUrl = new CarAssetRestLocator().getRestUrl(CarAssetRestUrl.INSURANCE_SELECT_LIST_POLICY);
            Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
            params.setPageSize(100);
            params.setPageNum(1);
            paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, params);
            RestResponse response = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, MrcarInsurancePolicyDTO.class);
            if (response.isSuccess()) {
                List<MrcarInsurancePolicyDTO> list = ObjectTransferUtil.transferRestResponseToList(response);
                list.forEach(e -> {
                    CarInfoDTO carInfoDTO = carInfoMap.get(e.getVehicleLicense());
                    if (carInfoDTO != null) {
                        e.setBelongCityName(carInfoDTO.getBelongCityName());
                        e.setStructName(carInfoDTO.getStructName());
                    }
                });
            }
            if(response.getData()!=null){
                List<MrcarInsurancePolicyDTO> mrcarInsurancePolicyDTOList = ObjectTransferUtil.transferRestResponseToList(response);
                List<Map<String,String>> pdfUrls = mrcarInsurancePolicyDTOList
                        .stream()
                        .filter(item -> StringUtils.isNotBlank(item.getPolicyNum())&&item.getHaveInsureFile())
                        .map(item -> {
                            Map<String,String> map = new HashMap<>();
                            map.put("url",item.getPolicyNum());
                            map.put("fileName","车牌号"+item.getVehicleLicense()+"-保单号"+item.getPolicyNum()+".pdf");
                            return map;
                        })
                        .collect(Collectors.toList());
                resp.setContentType("application/zip");
                Byte insuranceType = params.getInsuranceType();
                String zipName = "";
                if (InsuranceTypeEnum.MAND_INSURANCE.getCode().equals(insuranceType)) {
                    zipName = zipName+"交强险保单"+pdfUrls.size()+"个.zip";
                } else if (InsuranceTypeEnum.BUSINESS_INSURANCE.getCode().equals(insuranceType)) {
                    zipName = zipName+"商业险保单"+pdfUrls.size()+"个.zip";
                }
                // 设置中文文件名，并使用 UTF-8 编码进行处理
                String encodedFileName = URLEncoder.encode(zipName, StandardCharsets.UTF_8.toString()).replace("+", "%20");

                // 设置 Content-Disposition 头部，指示附件下载，并设置文件名
                resp.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"; filename*=UTF-8''" + encodedFileName);
                pdfZipUtil.downloadAndZipPdfs(pdfUrls, resp.getOutputStream(),request);
            }
        }
    }

    /**
     * 保单详情信息查询（交强险，商业险）
     *
     * @return
     */
    @RequestMapping(value = "/mandatory/selectPolicyDetail")
    @JrdApiDoc(simpleDesc = "保险保单详情", detailDesc = "保险保单详情", author = "quchuanming", resDataClass = InsurancePolicyDTO.class)
    @RequestFunction(functionName = "保单详情查询")
    public RestResponse selectPolicyDetail(@Verify(param = "policyNum", rule = "required") @JrdApiParamDoc(desc = "保单号", example = "1") String policyNum) {
        String restUrl = new CarAssetRestLocator().getRestUrl(CarAssetRestUrl.INSURANCE_SELECT_DETAIL_POLICY);
        Map<String, Object> paramMap = new HashMap<>();
        Map<String,Object> param = new HashMap<>();
        param.put("policyNum",policyNum);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        RestResponse restResponse = RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, MrcarInsurancePolicyDTO.class);
        logger.info("调用carAsset-core参数:{} 结果:{}", paramMap, JSON.toJSONString(restResponse));
        if (restResponse.isSuccess()) {
            MrcarInsurancePolicyDTO dto = (MrcarInsurancePolicyDTO) restResponse.getData();
            paramMap = new HashMap<>();
            paramMap.put("vehicleLicense", dto.getVehicleLicense());
            restUrl = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.CAR_INFO_GET_BY_VEHICLE_VIN);
            RestResponse restResponse1 = RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, CarInfoBaseDTO.class);
            if (restResponse1.isSuccess()) {
                CarInfoBaseDTO data = (CarInfoBaseDTO) restResponse1.getData();
                if(Objects.nonNull(data)){
                    if(StringUtils.isNotBlank(data.getBelongCityName())){
                        dto.setBelongCityName(data.getBelongCityName());
                    }
                    if (StringUtils.isNotBlank(data.getStructName())) {
                        dto.setStructName(data.getStructName());
                    }
                }
            }
        }
        return restResponse;
    }

    /**
     * 非车险列表查询接口
     * @param insurancePolicyParam
     * @return
     */
    @RequestMapping("/selectInsurancePolicyList")
    @JrdApiDoc(simpleDesc = "查询保单管理列表", detailDesc = "分页查询投保工单列表", author = "jiangxi", resDataClass = PageDTO.class)
    @RequestFunction(functionName = "非车险保单列表")
    public RestResponse selectnonAutoInsuranceList(@RequestBody @JrdApiParamDoc(desc = "保单查询参数") MrcarInsurancePolicyParamDTO insurancePolicyParam){
        Map<String, Object> params = new HashMap<>();
        VehicleSerialNoReqQTO dto = new VehicleSerialNoReqQTO().setVehicleLicense(insurancePolicyParam.getVehicleLicense());
        dto.setCityCode(insurancePolicyParam.getCityCode());
        dto.setStructId(insurancePolicyParam.getStructId());
        dto.setCompanyIds(insurancePolicyParam.getCompanyIds());
        List<CarInfoDTO> carInfoDTOs = selectVehicleSerialNoListByCompanyId(dto);
        if(CollectionUtil.isNotEmpty(carInfoDTOs)){
            Map<String, CarInfoDTO> carInfoMap = carInfoDTOs.stream().collect(Collectors.toMap(CarInfoDTO::getVehicleLicense, e -> e, (v1, v2) -> v1));
            List<String> vehicleSerialNoList = carInfoDTOs.stream().map(CarInfoDTO::getVehicleSerialNoNewSystem).collect(Collectors.toList());
            insurancePolicyParam.setVehicleSerialNoList(vehicleSerialNoList);
            params.put(BaseHttpClient.POSTBODY_MAP_KEY, insurancePolicyParam);
            String url = new CarAssetRestLocator().getRestUrl(CarAssetRestUrl.SELECCT_NON_AUTO_POLICY_LIST);
            RestResponse response = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, params, null, MrcarInsurancePolicyDTO.class);
            if (response.isSuccess()) {
                List<MrcarInsurancePolicyDTO> list = ObjectTransferUtil.transferRestResponseToList(response);
                //将所有车牌号拿出来，然后去车表查询公司相关信息
                List<String> vehicleLicenseList = list.stream().map(MrcarInsurancePolicyDTO::getVehicleLicensesStr)
                        .flatMap(s -> Arrays.stream(s.split(","))).distinct()
                        .collect(Collectors.toList());
                Map<Integer, CompanyDTO> companyDTOMap = new HashMap<>();
                Map<String, CarInfoDTO> carInfoMapByVehicleLicenseFinal = new HashMap<>();
                if (CollectionUtil.isNotEmpty(vehicleLicenseList)){
                    List<CarInfoDTO> carInfoByVehicleLicenseList = getCarInfoByVehicleLicenseList(vehicleLicenseList);
                    //按照车牌号分组
                    carInfoMapByVehicleLicenseFinal = carInfoByVehicleLicenseList.stream().collect(Collectors.toMap(CarInfoDTO::getVehicleLicense, e1 -> e1, (v1, v2) -> v1));
                    //只查询本企业的公司信息
                    if(ClientDataPermTypeEnum.SELF_COMPANY.getType().equals(dto.getDataPermType())){
                        List<CompanyDTO> companyByIds = CompanyApi.getCompanyByIds(dto.getDataCodeSet().stream().map(Integer::parseInt).collect(Collectors.toList()));
                        //按照公司id分组
                        companyDTOMap = companyByIds.stream().collect(Collectors.toMap(CompanyDTO::getCompanyId, e -> e));
                    }else{
                        List<CompanyDTO> companyByIds = CompanyApi.getCompanyByIds(CollectionUtil.toList(dto.getLoginCompanyId()));
                        //按照公司id分组
                        companyDTOMap = companyByIds.stream().collect(Collectors.toMap(CompanyDTO::getCompanyId, e -> e));
                    }


                }
                Map<String, CarInfoDTO> finalCarInfoMapByVehicleLicenseFinal = carInfoMapByVehicleLicenseFinal;
                Map<Integer, CompanyDTO> finalCompanyDTOMap = companyDTOMap;
                list.forEach(e -> {
                    CarInfoDTO carInfoDTO = carInfoMap.get(e.getVehicleLicense());
                    if (carInfoDTO != null) {
                        e.setBelongCityName(carInfoDTO.getBelongCityName());
                        e.setStructName(carInfoDTO.getStructName());
                    }
                    Set<String> companyNameList = new HashSet<>();
                    if(StringUtils.isNotBlank(e.getVehicleLicensesStr())){
                        Arrays.asList(e.getVehicleLicensesStr().split(",")).forEach(vehicleLicense -> {
                            CarInfoDTO carInfoDTOInfo = finalCarInfoMapByVehicleLicenseFinal.get(vehicleLicense);
                            if(carInfoDTOInfo!=null){
                                CompanyDTO companyDTO = finalCompanyDTOMap.get(carInfoDTOInfo.getCompanyId());
                                if(companyDTO!=null){
                                    companyNameList.add(companyDTO.getCompanyName());
                                }

                            }

                        });
                    }
                    String companyNameStr = companyNameList.stream().map(String::valueOf).collect(Collectors.joining(","));
                    e.setCompanyName(companyNameStr);
                });
            }
            logger.info("分页查询投保工单列表，params={}，response={}", params.toString(), JSON.toJSONString(response));
            return response;
        }else {
            //如果该企业下一辆车都没有，那么返回空对象
            return RestResponse.success(new PageDTO(1, 10, 0, Collections.emptyList()));
        }
    }
    /**
     * 下载zip非车险列表查询接口
     * @param insurancePolicyParam
     * @return
     */
    @RequestMapping("/downloadInsurancePolicyPDF")
    @JrdApiDoc(simpleDesc = "下载非车险保单ZIP", detailDesc = "下载非车险保单ZIP", author = "丁伟兵", resDataClass = PageDTO.class)
    @RequestFunction(functionName = "下载非车险保单ZIP")
    @ApiOperation(value = "下载非车险保单ZIP-客户端",notes = "作者：dingweibing")
    public void downloadInsurancePolicyPDF(@RequestBody @JrdApiParamDoc(desc = "保单查询参数") MrcarInsurancePolicyParamDTO insurancePolicyParam, HttpServletResponse resp,HttpServletRequest request) throws IOException {
        Map<String, Object> params = new HashMap<>();
        VehicleSerialNoReqQTO dto = new VehicleSerialNoReqQTO().setVehicleLicense(insurancePolicyParam.getVehicleLicense());
        dto.setCityCode(insurancePolicyParam.getCityCode());
        dto.setStructId(insurancePolicyParam.getStructId());
        dto.setCompanyIds(insurancePolicyParam.getCompanyIds());
        List<CarInfoDTO> carInfoDTOs = selectVehicleSerialNoListByCompanyId(dto);
        if(CollectionUtil.isNotEmpty(carInfoDTOs)){
            Map<String, CarInfoDTO> carInfoMap = carInfoDTOs.stream().collect(Collectors.toMap(CarInfoDTO::getVehicleLicense, e -> e, (v1, v2) -> v1));
            List<String> vehicleSerialNoList = carInfoDTOs.stream().map(CarInfoDTO::getVehicleSerialNoNewSystem).collect(Collectors.toList());
            insurancePolicyParam.setVehicleSerialNoList(vehicleSerialNoList);
            insurancePolicyParam.setPageSize(100);
            insurancePolicyParam.setPageNum(1);
            params.put(BaseHttpClient.POSTBODY_MAP_KEY, insurancePolicyParam);
            String url = new CarAssetRestLocator().getRestUrl(CarAssetRestUrl.SELECCT_NON_AUTO_POLICY_LIST);
            RestResponse response = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, params, null, MrcarInsurancePolicyDTO.class);
            if (response.isSuccess()) {
                List<MrcarInsurancePolicyDTO> list = ObjectTransferUtil.transferRestResponseToList(response);
                list.forEach(e -> {
                    CarInfoDTO carInfoDTO = carInfoMap.get(e.getVehicleLicense());
                    if (carInfoDTO != null) {
                        e.setBelongCityName(carInfoDTO.getBelongCityName());
                        e.setStructName(carInfoDTO.getStructName());
                    }
                });
            }
            if(response.getData()!=null){
                List<MrcarInsurancePolicyDTO> mrcarInsurancePolicyDTOList = ObjectTransferUtil.transferRestResponseToList(response);
                List<Map<String,String>> pdfUrls = mrcarInsurancePolicyDTOList
                        .stream()
                        .filter(item -> StringUtils.isNotBlank(item.getPolicyNum())&&item.getHaveInsureFile())
                        .map(item -> {
                            Map<String,String> map = new HashMap<>();
                            map.put("url",item.getPolicyNum());
                            map.put("fileName","保单号"+item.getPolicyNum()+".pdf");
                            return map;
                        })
                        .collect(Collectors.toList());
                resp.setContentType("application/zip");
                String zipName = "非车保险单"+pdfUrls.size()+"个.zip";
                // 设置中文文件名，并使用 UTF-8 编码进行处理
                String encodedFileName = URLEncoder.encode(zipName, StandardCharsets.UTF_8.toString()).replace("+", "%20");

                // 设置 Content-Disposition 头部，指示附件下载，并设置文件名
                resp.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"; filename*=UTF-8''" + encodedFileName);
                pdfZipUtil.downloadAndZipPdfs(pdfUrls, resp.getOutputStream(),request);
            }
        }
    }

    /**
     * 非车险详情查询接口
     * @param insurancePolicyParam
     * @return
     */
    @RequestMapping("/selectInsurancePolicyDetail")
    @JrdApiDoc(simpleDesc = "查询非车险投保工单详情" ,detailDesc = "查询非车险投保工单详情" ,author = "jiangxi",resDataClass = JSONObject.class)
    @RequestFunction(functionName = "非车险保单详情")
    public RestResponse findPaymentRequestDetail(@RequestBody @JrdApiParamDoc(desc = "保单查询参数") InsurancePolicyParam insurancePolicyParam){
        Map<String, Object> params = new HashMap<>();
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, insurancePolicyParam);
        String url = new CarAssetRestLocator().getRestUrl(CarAssetRestUrl.SELECT_NON_AUTO_POLICY_DETAIL);
        RestResponse response = RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, params, null, InsurancePolicyDTO.class);
        logger.info("查询非车险投保工单详情，params={}，response={}", params.toString(), response!=null ? JSON.toJSONString(response) : "");
        return response;
    }

    @RequestMapping("/download")
    @JrdApiDoc(simpleDesc = "下载电子保单、电子批单文件", author = "whd", resDataClass = Map.class)
    @RequestFunction(functionName = "保单文件下载")
    public RestResponse download(HttpServletResponse response, HttpServletRequest request,
                                 @Verify(rule = "required", param = "policyNum") @JrdApiParamDoc(desc = "保单号") String policyNum,
                                 @Verify(param = "fileType", rule = "required") @JrdApiParamDoc(desc = "文件类型，2：批单工单， 9：保单") Byte fileType
    ) {
        Map<String, Object> params = new HashMap<String, Object>();
        InsuranceAttachmentQueryDTO queryDTO = new InsuranceAttachmentQueryDTO();
        queryDTO.setInsuranceNo(policyNum);
        queryDTO.setAttachmentType(fileType);
        if (InsuranceAttachmentEnum.POLICY.getCode().equals(fileType)) {
            queryDTO.setPictureCategory((byte) 0);
        } else if (InsuranceAttachmentEnum.CHANGE_ORDER.getCode().equals(fileType)) {
            queryDTO.setPictureCategory((byte) 2);
        } else {
            return RestResponse.fail(RestErrorCode.HTTP_PARAM_INVALID);
        }
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, queryDTO);
        String url = new CarAssetRestLocator().getRestUrl(CarAssetRestUrl.INSURANCE_ATTACHMENT_QUERY_BY_POLICY);
        RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, url, params, null, InsuranceAttachmentDTO.class);
        if (restResponse != null && restResponse.isSuccess() && restResponse.getData() != null) {
            List<InsuranceAttachmentDTO> data = (List<InsuranceAttachmentDTO>) restResponse.getData();
            if (data.size() == 1) {
                InsuranceAttachmentDTO insuranceAttachmentDTO = data.get(0);
                final File file = this.getFile(insuranceAttachmentDTO.getAttachmentUrl(), insuranceAttachmentDTO.getAttachmentName());
                if (file != null) {
                    try (InputStream is = new FileInputStream(file)) {
                        this.downloadLocal(request, response, is, insuranceAttachmentDTO.getAttachmentName());
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            } else if (data.size() > 1) {
                List<File> files = data.stream().map(e -> this.getFile(e.getAttachmentUrl(), e.getAttachmentName())).collect(Collectors.toList());
                String baseDir = System.getProperty("java.io.tmpdir") + File.separator + "upload";
                String fileName = policyNum + ".zip";
                String zipPath = baseDir + File.separator + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".zip";
                File targetZipFile = new File(zipPath);
                ZipFileUtil.zip(files, targetZipFile);
                try (InputStream is = new FileInputStream(targetZipFile)) {
                    this.downloadLocal(request, response, is, fileName);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }

    private File getFile(final String url, final String fileName) {
        String substring = fileName.substring(0, fileName.lastIndexOf("."));
        String substring1 = fileName.substring(fileName.lastIndexOf("."));
        String excelFileName = substring + "-" + System.currentTimeMillis() + substring1;
        final String baseDir = System.getProperty("java.io.tmpdir") + File.separator + "upload";
        String storeFilePath = baseDir + File.separator + excelFileName;
        String lenthStr = storeFilePath.replaceAll( "[^\\x00-\\xff]" , "**" );
        if (lenthStr.length()>255){
            storeFilePath = baseDir + File.separator + RandomStringUtil.genRandomString(8)+substring1;
        }
        logger.info("上传文件名称={}, 存储保存路径={}", excelFileName, storeFilePath);
        Map<String, String> httpHeader = Maps.newHashMapWithExpectedSize(1);
        //有防盗链机制，必须加上Referer
        httpHeader.put("Referer", "https://prd-third.izuche.com");
        boolean downloadOK = BaseHttpClient.download(BaseHttpClient.HttpMethod.GET, url, null, httpHeader, storeFilePath);
        if (downloadOK) {
            logger.info("文件下载成功");
            return new File(storeFilePath);
        } else {
            logger.error("文件下载失败");
            return null;
        }
    }

    public void downloadLocal(HttpServletRequest request, HttpServletResponse response, InputStream inputStream, String fileName) {
        // 设置输出的格式
        setResponse(request, response, fileName);
        // 循环取出流中的数据
        byte[] b = new byte[100];
        int len;
        try {
            while ((len = inputStream.read(b)) > 0) {
                response.getOutputStream().write(b, 0, len);
            }
            inputStream.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    protected static void setResponse(HttpServletRequest request, HttpServletResponse response, String fileName) {
        try {
            //根据文件名，获知文件类型。
            //扩展名
            final String excelType = fileName.substring(fileName.trim().lastIndexOf(".") + 1).toLowerCase();
            // 决定文件名(需要对不同浏览器进行兼容，尤其是Firefox)
            String ua = request.getHeader("User-Agent");
            //如果没有UA，则采用英文文件名
            if (StringUtils.isNotBlank(ua)) {
                fileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.name());
                //UA是Firefox
                if (ua.toLowerCase().contains("firefox")) {
                    if (!HttpMethod.POST.name().equalsIgnoreCase(request.getMethod()) || request.getContentType() == null || !request.getContentType().toLowerCase().contains("application/json")) {
                        fileName = new String(fileName.getBytes(StandardCharsets.UTF_8), "ISO8859-1");
                    }
                }
            }
            response.reset();
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
            response.setHeader("Download-Filename", URLEncoder.encode(fileName, "UTF8"));//这是和前端约定的自定义响应头
            if ("xls".equalsIgnoreCase(excelType)) {
                response.setContentType("application/vnd.ms-excel;charset=gbk");
            } else if ("xlsx".equalsIgnoreCase(excelType)) {
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=gbk");
            } else if ("pdf".equalsIgnoreCase(excelType)) {
                response.setContentType("application/pdf;charset=gbk");
            } else if ("jpeg".equalsIgnoreCase(excelType) || "jpg".equalsIgnoreCase(excelType)) {
                response.setContentType("image/jpeg;charset=gbk");
            } else if ("png".equalsIgnoreCase(excelType)) {
                response.setContentType("image/png;charset=gbk");
            } else {
                response.setContentType("application/octet-stream;charset=gbk");
            }
            response.setCharacterEncoding("gbk");
            //B3 输出EXCEL
        } catch (Exception e) {
            logger.error("导出文件 [ " + fileName + " ] 发生异常！", e);
        }
    }

    @Override
    protected List<Integer> getResolveSheetIndexes() {
        return null;
    }

    @Override
    protected String getColumnMappingConfigFile(HttpServletRequest request, HttpServletResponse response) {
        return null;
    }

    @Override
    protected List<InsurancePolicySaveDTO> convertStringDataToObject(List<Map<Integer, String>> rowDatas, ExcelUploadResult excelUploadResult) {
        return null;
    }

    @Override
    protected List<ExcelUploadRowError> checkExcelData(List<InsurancePolicySaveDTO> rowDatas, HttpServletRequest request, HttpServletResponse response, ExcelUploadResult excelUploadResult) {
        return null;
    }

    @Override
    protected boolean beforePersist(List<InsurancePolicySaveDTO> rowDatas, HttpServletRequest request, HttpServletResponse response, ExcelUploadResult excelUploadResult) {
        return false;
    }

    @Override
    protected RestResponse executePersist(List<InsurancePolicySaveDTO> rowDatas, HttpServletRequest request, HttpServletResponse response, ExcelUploadResult excelUploadResult) {
        return null;
    }
    @RequestMapping("/mandatory/findExport")
    @JrdApiDoc(simpleDesc = "保单导出", detailDesc = "保单导出", author = "luol", resDataClass = String.class)
    @RequestFunction(functionName = "保单信息导出")
    public RestResponse findExport(@RequestBody @JrdApiParamDoc(desc = "保单查询参数") MrcarInsurancePolicyParamDTO insurancePolicyParam, HttpServletRequest request, HttpServletResponse response) {
        String restUrl = new CarAssetRestLocator().getRestUrl("/insurancePolicy/findExportMrcar.json");
        RestResponse restResponseCount = null;
        List<MrcarInsurancePolicyExportDTO> list = new ArrayList<>();
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        VehicleSerialNoReqQTO dto = new VehicleSerialNoReqQTO().setVehicleLicense(insurancePolicyParam.getVehicleLicense());
        dto.setCityCode(insurancePolicyParam.getCityCode());
        dto.setStructId(insurancePolicyParam.getStructId());
        dto.setCompanyIds(insurancePolicyParam.getCompanyIds());
        List<CarInfoDTO> carInfoDTOs = selectVehicleSerialNoListByCompanyId(dto);
        Map<String, CarInfoDTO> carInfoMap = carInfoDTOs.stream().collect(Collectors.toMap(CarInfoDTO::getVehicleLicense, e -> e, (o1, o2) -> o1));
        Map<Integer, CompanyDTO> companyDTOMap = new HashMap<>();
        Map<String, CarInfoDTO> carInfoMapByVehicleLicenseFinal = new HashMap<>();
        if(CollectionUtil.isNotEmpty(carInfoDTOs)){
            logger.info("【保单导出】查询有车辆信息直接导出。");
            List<String> vehicleSerialNoList = carInfoDTOs.stream().map(CarInfoDTO::getVehicleSerialNoNewSystem).collect(Collectors.toList());
            insurancePolicyParam.setVehicleSerialNoList(vehicleSerialNoList);
            paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, insurancePolicyParam);
            restResponseCount= RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, MrcarInsurancePolicyExportDTO.class);
            if (restResponseCount != null && restResponseCount.isSuccess()) {
                list = ObjectTransferUtil.transferRestResponseToList(restResponseCount);
                if (insurancePolicyParam.getInsuranceType().equals(InsuranceTypeEnum.NO_CAR_INSURANCE.getCode())){
                    //将所有车牌号拿出来，然后去车表查询公司相关信息
                    List<String> vehicleLicenseList = list.stream().map(MrcarInsurancePolicyExportDTO::getVehicleLicensesStr)
                            .flatMap(s -> Arrays.stream(s.split(","))).distinct()
                            .collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(vehicleLicenseList)){
                        List<CarInfoDTO> carInfoByVehicleLicenseList = getCarInfoByVehicleLicenseList(vehicleLicenseList);
                        //按照车牌号分组
                        carInfoMapByVehicleLicenseFinal = carInfoByVehicleLicenseList.stream().collect(Collectors.toMap(CarInfoDTO::getVehicleLicense, e1 -> e1, (o1, o2) -> o1));
                        //只查询本企业的公司信息
                        if(ClientDataPermTypeEnum.SELF_COMPANY.getType().equals(dto.getDataPermType())){
                            List<CompanyDTO> companyByIds = CompanyApi.getCompanyByIds(dto.getDataCodeSet().stream().map(Integer::parseInt).collect(Collectors.toList()));
                            //按照公司id分组
                            companyDTOMap = companyByIds.stream().collect(Collectors.toMap(CompanyDTO::getCompanyId, e -> e));
                        }else{
                            List<CompanyDTO> companyByIds = CompanyApi.getCompanyByIds(CollectionUtil.toList(dto.getLoginCompanyId()));
                            //按照公司id分组
                            companyDTOMap = companyByIds.stream().collect(Collectors.toMap(CompanyDTO::getCompanyId, e -> e));
                        }
                    }
                }

            }
        }else {
            logger.info("【保单导出】该企业下无车辆信息，直接返回空表");
        }
        Map<Integer, CompanyDTO> finalCompanyInfoMap = new HashMap<>();
        if(!insurancePolicyParam.getInsuranceType().equals(InsuranceTypeEnum.NO_CAR_INSURANCE.getCode())){
            finalCompanyInfoMap = getIntegerCompanyDTOMap(carInfoDTOs);
        }
        Map<String, CarInfoDTO> finalCarInfoMapByVehicleLicenseFinal = carInfoMapByVehicleLicenseFinal;
        Map<Integer, CompanyDTO> finalCompanyDTOMap = companyDTOMap;
        Map<Integer, CompanyDTO> finalCompanyInfoMap1 = finalCompanyInfoMap;
        list.forEach(e -> {
            CarInfoDTO carInfoDTO = carInfoMap.get(e.getVehicleLicense());
            if (carInfoDTO != null) {
                e.setBelongCityName(carInfoDTO.getBelongCityName());
                e.setStructName(carInfoDTO.getStructName());
                if(!insurancePolicyParam.getInsuranceType().equals(InsuranceTypeEnum.NO_CAR_INSURANCE.getCode())) {
                    CompanyDTO companyDTO = finalCompanyInfoMap1.get(carInfoDTO.getCompanyId());
                    if (companyDTO != null) {
                        e.setCompanyName(companyDTO.getCompanyName());
                    }
                }
            }
            if(insurancePolicyParam.getInsuranceType().equals(InsuranceTypeEnum.NO_CAR_INSURANCE.getCode())){
                Set<String> companyNameList = new HashSet<>();
                if(StringUtils.isNotBlank(e.getVehicleLicensesStr())){
                    Arrays.asList(e.getVehicleLicensesStr().split(",")).forEach(vehicleLicense -> {
                        CarInfoDTO carInfoDTOInfo = finalCarInfoMapByVehicleLicenseFinal.get(vehicleLicense);
                        if(carInfoDTOInfo!=null){
                            CompanyDTO companyDTO = finalCompanyDTOMap.get(carInfoDTOInfo.getCompanyId());
                            if(companyDTO!=null){
                                companyNameList.add(companyDTO.getCompanyName());
                            }

                        }

                    });
                }
                String companyNameStr = companyNameList.stream().map(String::valueOf).collect(Collectors.joining(","));
                e.setCompanyName(companyNameStr);
            }
        });
        final List<ExSheet> exSheets = new ArrayList<ExSheet>();
        ExSheet sheet1 = this.makeSheet(list,insurancePolicyParam.getInsuranceType());
        exSheets.add(sheet1);
        String excelType = "xlsx";
        String fileNameCn = InsuranceTypeEnum.getMsg(insurancePolicyParam.getInsuranceType())+"保单信息_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + "." + excelType;
        String fileNameEn = "policy" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + "." + excelType;
        this.exportExcel(request, response, fileNameCn, fileNameEn, exSheets);
        return null;
    }

    @NotNull
    private Map<Integer, CompanyDTO> getIntegerCompanyDTOMap(List<CarInfoDTO> carInfoDTOs) {
        //导出新增所属企业
        Map<Integer, CompanyDTO> companyInfoMap = new HashMap<>();
        List<Integer> companyIds = carInfoDTOs.stream().map(CarInfoDTO::getCompanyId).distinct().collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(companyIds)){
            List<CompanyDTO> companyByIds = CompanyApi.getCompanyByIds(companyIds);
            if(CollectionUtil.isNotEmpty(companyByIds)){
                //按公司id分组并返回map
                companyInfoMap = companyByIds.stream().collect(Collectors.toMap(CompanyDTO::getCompanyId, e -> e));
            }
        }
        Map<Integer, CompanyDTO> finalCompanyInfoMap = companyInfoMap;
        return finalCompanyInfoMap;
    }

    private ExSheet makeSheet(List<MrcarInsurancePolicyExportDTO> list, Byte insuranceType) {
        String title = InsuranceTypeEnum.MAND_INSURANCE.getMsg();
        if (insuranceType.equals(InsuranceTypeEnum.BUSINESS_INSURANCE.getCode())) {
            title = InsuranceTypeEnum.BUSINESS_INSURANCE.getMsg();
        } else if (insuranceType.equals(InsuranceTypeEnum.NO_CAR_INSURANCE.getCode())) {
            title = InsuranceTypeEnum.NO_CAR_INSURANCE.getMsg();
        } else if (insuranceType.equals(InsuranceTypeEnum.TRAVEL_TAX_INSURANCE.getCode())) {
            title = InsuranceTypeEnum.TRAVEL_TAX_INSURANCE.getMsg();
        }

        final List<Column> columnModes = new ArrayList<Column>(19);
        columnModes.add(new Column("policyNum", "保单号", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("underwriterName", "承保单位", (short) 7000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));

        if (insuranceType.equals(InsuranceTypeEnum.MAND_INSURANCE.getCode())) {
            columnModes.add(new Column("vehicleLicense", "车牌号", (short) 3000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            columnModes.add(new Column("belongCityName", "车辆所在城市", (short) 3000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            columnModes.add(new Column("structName", "车辆所属部门", (short) 3000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            columnModes.add(new Column("companyName", "所属企业", (short) 3000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        } else if (insuranceType.equals(InsuranceTypeEnum.BUSINESS_INSURANCE.getCode())) {
            columnModes.add(new Column("vehicleLicense", "车牌号", (short) 3000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            columnModes.add(new Column("belongCityName", "车辆所在城市", (short) 3000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            columnModes.add(new Column("structName", "车辆所属部门", (short) 3000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            columnModes.add(new Column("companyName", "所属企业", (short) 3000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        } else if (insuranceType.equals(InsuranceTypeEnum.NO_CAR_INSURANCE.getCode())) {
            columnModes.add(new Column("carNum", "车辆数量", (short) 3000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            columnModes.add(new Column("companyName", "所属企业", (short) 3000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            columnModes.add(new Column("insureTypeName", "险种名称", (short) 3000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            columnModes.add(new Column("limitOfLiability", "责任限额", (short) 3000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        }
        columnModes.add(new Column("beginTimeStr", "保单起始日", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("endTimeStr", "保单到期日", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("status", "生效状态", (short) 3500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));

        final List<Map<String, String>> rowDatas = new ArrayList<Map<String, String>>(list.size() + 1);
        for(MrcarInsurancePolicyExportDTO exportDTO:list)
        {
            final Map<String, String> rowdata = new HashMap<String, String>(columnModes.size() * 2);
            rowdata.put("policyNum", exportDTO.getPolicyNum());
            rowdata.put("underwriterName", exportDTO.getUnderwriterName());
            if (insuranceType.equals(InsuranceTypeEnum.MAND_INSURANCE.getCode())) {
                rowdata.put("vehicleLicense", exportDTO.getVehicleLicense());
                rowdata.put("belongCityName", exportDTO.getBelongCityName());
                rowdata.put("structName", exportDTO.getStructName());
                rowdata.put("companyName", exportDTO.getCompanyName());
            } else if (insuranceType.equals(InsuranceTypeEnum.BUSINESS_INSURANCE.getCode())) {
                rowdata.put("vehicleLicense", exportDTO.getVehicleLicense());
                rowdata.put("belongCityName", exportDTO.getBelongCityName());
                rowdata.put("structName", exportDTO.getStructName());
                rowdata.put("companyName", exportDTO.getCompanyName());
            } else if (insuranceType.equals(InsuranceTypeEnum.NO_CAR_INSURANCE.getCode())) {
                rowdata.put("carNum", exportDTO.getCarNum()+"");
                rowdata.put("companyName", exportDTO.getCompanyName());
                rowdata.put("insureTypeName", exportDTO.getInsureTypeName());
                rowdata.put("limitOfLiability", exportDTO.getLimitOfLiability()+exportDTO.getLimitOfLiabilityUnit());
            }
            rowdata.put("beginTimeStr", exportDTO.getBeginTimeStr());
            rowdata.put("endTimeStr", exportDTO.getEndTimeStr());
            rowdata.put("status", PolicyStatusEnum.getMsg(exportDTO.getStatus()));
            rowDatas.add(rowdata);
        }
        StringBuffer sheetName = new StringBuffer(title+"保单信息");
        return new ExSheet(columnModes, rowDatas, sheetName.toString());
    }

    @RequestMapping("/underwriterList")
    @JrdApiDoc(simpleDesc = "供应商列表", detailDesc = "供应商列表", author = "morty", resDataClass = Map.class)
    public RestResponse underwriterList(String supplierName){
        String restUrl = new CrmRestLocator().getRestUrl(CrmRestUrl.SIMPLE_SUPPLIER_LIST);
        HashMap<String, Object> params = Maps.newHashMapWithExpectedSize(2);
        params.put("supplierName", supplierName);
        params.put("supplierType", SupplierTypeEnum.INSURANCE.getCode());
        RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, params, null, SupplierSimpleDTO.class);
        if (restResponse !=null && restResponse.isSuccess() && restResponse.getData() !=null){
            List<SupplierSimpleDTO> data = (List<SupplierSimpleDTO>) restResponse.getData();
            List<InsuranceUnderwriterDTO> maps = data.stream().map(e -> {
                InsuranceUnderwriterDTO insuranceUnderwriterDTO = new InsuranceUnderwriterDTO();
                insuranceUnderwriterDTO.setUnderwriterNo(e.getSupplierCode());
                insuranceUnderwriterDTO.setUnderwriterName(e.getSupplierName());
                insuranceUnderwriterDTO.setUnderwriterAlias(e.getSupplierAbridge());
                return insuranceUnderwriterDTO;
            }).collect(Collectors.toList());
            return RestResponse.success(maps);
        }
        return null;
    }



}
