package com.izu.mrcar.controller.worktable;

import com.google.common.collect.Maps;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.mrcar.common.config.ConstansConfig;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.dto.MsgPushRecordLogRespDTO;
import com.izu.mrcar.dto.ProblemFeedbackDTO;
import com.izu.mrcar.order.dto.mrcar.param.OrderInfoParam;
import com.izu.mrcar.utils.DateUtil;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.workflow.common.MrcarWorkflowRestLocator;
import com.izu.mrcar.workflow.common.constants.MrCarWorkflowResturl;
import com.izu.mrcar.workflow.common.dto.task.BpmTaskPcPageReqDTO;
import com.izu.mrcar.workflow.common.dto.task.BpmTaskPcPageRespDTO;
import com.izu.notify.NotifyRestLocator;
import com.izu.notify.NotifyRestUrl;
import com.izu.notify.dto.input.MsgPushRecordLogInputDTO;
import com.izu.notify.dto.input.base.OrderByParam;
import com.izu.notify.dto.output.MsgPushRecordLogOutputDTO;
import com.izu.notify.enums.MsgCommonEnum;
import com.izu.notify.enums.MsgModelEnum;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.dto.mrcar.OrderApplyDTO;
import com.izu.mrcar.order.dto.order.OrderApplyListReqDTO;
import com.izu.mrcar.order.dto.order.OrderApplyListRespDTO;
import com.izu.mrcar.order.dto.order.OrderWorktableRespDTO;
import com.izu.user.config.UserRestLocator;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.dto.LoginUser;
import com.izu.user.dto.provider.staff.OrderGetProviderStaffRespDTO;
import com.izu.user.dto.staff.pc.AccountBelongDepartment;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.enums.UserStateEnum;
import com.izu.user.restApi.OrderUserApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
* @Description: 工作台
* @author: hxc
* @Date: 2022/12/21
**/
@Api(tags="工作台")
@RestController
@RequestMapping("/worktable")
public class WorktableController {

    /**
     * 我的行程分页列表
     * @param queryDTO
     * @return
     */
    @PostMapping("/queryOrderPageList")
    @ApiOperation(value = "我的行程", notes = "作者：贺新春")
    @RequestFunction(functionName = "工作台-我的行程")
    public RestResponse<PageDTO<OrderWorktableRespDTO>> queryOrderList(@RequestBody OrderApplyListReqDTO queryDTO) {

        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        queryDTO.setLoginCompanyId(loginBaseInfo.obtainBelongCompanyId());
        queryDTO.setLoginId(loginBaseInfo.obtainBaseInfo().getStaffId());
        queryDTO.setOnlySelf(true);// 只查询自己的，不用设置数据权限
        String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.ORDER_APPLY_QUERY_ORDER_PAGE_LIST);
        final HashMap<String, Object> param = Maps.newHashMapWithExpectedSize(1);
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, queryDTO);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null, OrderApplyListRespDTO.class);
        PageDTO<OrderWorktableRespDTO> pageDTO = new PageDTO<>();
        pageDTO.setPage(queryDTO.getPage());
        pageDTO.setPageSize(queryDTO.getPageSize());
        pageDTO.setTotal(0);
        if (restResponse.isSuccess() && restResponse.getData() != null){
            PageDTO<OrderApplyListRespDTO> data = (PageDTO<OrderApplyListRespDTO>) restResponse.getData();
            List<OrderApplyListRespDTO> result = data.getResult();
            List<OrderWorktableRespDTO> orderWorktableRespDTOS = BeanUtil.copyList(result, OrderWorktableRespDTO.class);
            pageDTO.setResult(orderWorktableRespDTOS);
            pageDTO.setTotal(data.getTotal());
        }
        return RestResponse.success(pageDTO);
    }

    @PostMapping("/queryByOrderApplyNo")
    @RequestFunction(functionName = "工作台-我的行程详情")
    @ApiOperation(value = "我的行程详情", notes = "作者：贺新春")
    public RestResponse<OrderApplyDTO> queryOrderDetail(@RequestBody OrderInfoParam param) {
        String uri = StringUtils.isNotBlank(param.getOrderNo())?"/order/queryByOrderNo":"/orderApply/queryByOrderApplyNo";
        String restUrl = new MrcarOrderRestLocator().getRestUrl(uri);
        Map<String, Object> params = new HashMap<>();
        params.put("includeOrderAmount", true);
        params.put("includeOrderTimeStream", true);
        params.put("includeOrderInfo", true);
        params.put("includeOrderInfoAmont", true);
        params.put("fromPc", true);
        params.put("isQueryDestinations", true);
        params.put("isQueryPassengers", true);
        params.put("includeApprovalInfo", true);
        params.put("includePriceSnapshot",true);
        params.put("includeOrderVehicleImg", true);
        params.put("includeOrderVehicle", true);
        params.put("orderApplyNo",param.getOrderApplyNo());
        params.put("orderNo",param.getOrderNo());
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, params, null);
    }

    @PostMapping("/taskList")
    @RequestFunction(functionName = "工作台-审批任务列表")
    @ApiOperation(value = "我发起的流程|我处理的流程",notes = "作者：贺新春")
    public RestResponse<BpmTaskPcPageRespDTO> getTaskListPage(@RequestBody BpmTaskPcPageReqDTO pageVO) {
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        pageVO.setLoginCompanyId(loginBaseInfo.obtainBelongCompanyId());
        pageVO.setLoginCompanyName(loginBaseInfo.obtainBelongCompanyName());
        pageVO.setLoginUserId(loginBaseInfo.obtainBaseInfo().getStaffId());
        pageVO.setLoginUserName(loginBaseInfo.obtainBaseInfo().getStaffName());
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_TASK_LIST_PAGE_FOR_PC);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, pageVO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @GetMapping("/problemFeedback")
    @RequestFunction(functionName = "工作台-问题反馈")
    @ApiOperation(value = "问题反馈",notes = "作者：贺新春")
    public RestResponse<ProblemFeedbackDTO> ProblemFeedback() {
        ClientLoginInfo loginBaseInfo = LoginSessionUtil.getClientLoginInfo();
        ProblemFeedbackDTO problemFeedbackDTO = new ProblemFeedbackDTO();
        problemFeedbackDTO.setCompanyAdminName(loginBaseInfo.getClientCompany().getLinkerName());
        problemFeedbackDTO.setCompanyAdminPhone(loginBaseInfo.getClientCompany().getLinkerMobile());
        problemFeedbackDTO.setProductManagerName(ConstansConfig.getConfig().getProductManagerName());
        problemFeedbackDTO.setProductManagerPhone(ConstansConfig.getConfig().getProductManagerPhone());
        // 查询销售人员信息
        OrderGetProviderStaffRespDTO internalStaffInfo = OrderUserApi.getInternalStaffInfo(loginBaseInfo.getClientCompany().getSaleId());
        if (internalStaffInfo != null) {
            problemFeedbackDTO.setMarketName(internalStaffInfo.getStaffName());
            problemFeedbackDTO.setMarketPhone(internalStaffInfo.getMobile());
        }
        return RestResponse.success(problemFeedbackDTO);
    }

    @GetMapping("/msgPushRecordLog")
    @RequestFunction(functionName = "工作台-消息列表")
    @ApiOperation(value = "我的消息",notes = "作者：贺新春")
    @ApiImplicitParams({
            @ApiImplicitParam(name="pageNo",value="页码",required=true),
            @ApiImplicitParam(name="pageSize",value="每页显示条数",required=true)
    })
    public RestResponse<PageDTO<MsgPushRecordLogRespDTO>> queryMsgByPage(Integer pageNo, Integer pageSize){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        MsgPushRecordLogInputDTO inputDTO = new MsgPushRecordLogInputDTO();
        inputDTO.setModelName(MsgModelEnum.ModelCodeEnum.MR_CAR.getModelName());
        inputDTO.setPushPhone(loginBaseInfo.obtainBaseInfo().getMobile());
        inputDTO.setPageNum(pageNo);
        inputDTO.setPageSize(pageSize);
        inputDTO.setIntoMsgCenter(MsgCommonEnum.YesOrNoEnum.YES.getValue());
        inputDTO.setIsPage(true);
        inputDTO.setPushMode(MsgCommonEnum.PushModelEnum.MR_PC.getPushMode());
        OrderByParam orderByParam = new OrderByParam();
        orderByParam.setFieldName("createTime");
        orderByParam.setDescOrAsc("desc");
        List<OrderByParam> orderByParams = Arrays.asList(orderByParam);
        inputDTO.setOrders(orderByParams);
        HashMap<String, Object> params = Maps.newHashMapWithExpectedSize(1);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, inputDTO);
        String restUrl = new NotifyRestLocator().getRestUrl(NotifyRestUrl.MSG_PUSH_RECORD_LOG_QUERY_BY_PAGE_FOR_BACK);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, params,null, MsgPushRecordLogOutputDTO.class);
        //时间格式化处理
        if(Objects.isNull(restResponse)
                || Objects.isNull(restResponse.getData())
                || CollectionUtils.isEmpty(((PageDTO) restResponse.getData()).getResult())){
            return RestResponse.success(new PageDTO<MsgPushRecordLogRespDTO>(pageNo, pageSize, 0, Lists.newArrayList()));
        }else {
            PageDTO pageDTO = (PageDTO) restResponse.getData();
            List<MsgPushRecordLogOutputDTO> list = pageDTO.getResult();
            for (MsgPushRecordLogOutputDTO m : list) {
                m.setPushTimeStr(DateUtil.formatTimeForMsg(m.getPushTime()));
            }
            List<MsgPushRecordLogRespDTO> msgPushRecordLogRespDTOS = BeanUtil.copyList(list, MsgPushRecordLogRespDTO.class);
            for(MsgPushRecordLogRespDTO logRespDTO:msgPushRecordLogRespDTOS){
                //设置icon
                String secondName=logRespDTO.getSecondModelName();
                String icon=Arrays.stream(MsgModelEnum.SecondModelCodeEnum.values()).filter(e->Objects.equals(e.getModelCode(), MsgModelEnum.ModelCodeEnum.MR_CAR.getModelCode()))
                        .filter(e->Objects.equals(e.getSecondModelName(),secondName)).findFirst().map(MsgModelEnum.SecondModelCodeEnum::getSecondModelUrl).orElse("");
                logRespDTO.setSecondModelIcon(icon);
            }
            pageDTO.setResult(msgPushRecordLogRespDTOS);
            return RestResponse.success(pageDTO);
        }
    }

}
