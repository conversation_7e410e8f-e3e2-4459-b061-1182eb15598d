package com.izu.mrcar.controller.worktable;

import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.shiro.session.WebSessionUtil;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.dto.LoginUser;
import com.izu.user.dto.ManagerPermissionDTO;
import com.izu.user.dto.input.QuickMenuSaveDTO;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * @program: mrcar
 * @description: 快捷入口
 * @author: ljw
 * @create: 2022-12-23 13:51
 **/
@Api(tags="工作台")
@RestController
@RequestMapping("")
public class QuickMenuController {


    @PostMapping(UserUrlCenter.USER_QUICK_MENU_QUERY)
    @ApiOperation(value = "快捷入口菜单", notes = "作者：连江伟")
    @RequestFunction(functionName = "工作台-快捷入口")
    public RestResponse<ManagerPermissionDTO> queryUserQuickMenuList() {
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        Map<String,Object> paraMap = new HashMap<>();
        paraMap.put("customerId", loginBaseInfo.obtainBaseInfo().getStaffId());
//        paraMap.put("companyAttribute",loginUser.getCompany().getCompanyAttribute());
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, new UserRestLocator().getRestUrl(UserUrlCenter.USER_QUICK_MENU_QUERY), paraMap, null);
    }


    @PostMapping(UserUrlCenter.USER_HOLD_PERMISSION_INFO)
    @ApiOperation(value = "用户可见菜单", notes = "作者：连江伟")
    @RequestFunction(functionName = "工作台-获取菜单")
    public RestResponse<ManagerPermissionDTO> queryPermissionOfUser() {
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        Map<String,Object> paraMap = new HashMap<>();
        paraMap.put("customerId", loginBaseInfo.obtainBaseInfo().getStaffId());
//        paraMap.put("companyAttribute",loginUser.getCompany().getCompanyAttribute());
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, new UserRestLocator().getRestUrl(UserUrlCenter.USER_HOLD_PERMISSION_INFO), paraMap, null);
    }

    @PostMapping(UserUrlCenter.USER_QUICK_MENU_SAVE)
    @ApiOperation(value = "保存快捷菜单", notes = "作者：连江伟")
    @RequestFunction(functionName = "工作台-保存快捷菜单")
    public RestResponse saveQuickMenu(@RequestBody QuickMenuSaveDTO saveDTO) {
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        Map<String,Object> paraMap = new HashMap<>();
        saveDTO.setCustomerId(loginBaseInfo.obtainBaseInfo().getStaffId());
        paraMap.put(BaseHttpClient.POSTBODY_MAP_KEY,saveDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, new UserRestLocator().getRestUrl(UserUrlCenter.USER_QUICK_MENU_SAVE), paraMap, null);
    }
}
