package com.izu.mrcar.controller.company;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.style.row.SimpleRowHeightStyleStrategy;
import com.alibaba.nacos.shaded.com.google.common.collect.Sets;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.izu.framework.doc.JrdApiDoc;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.controller.base.UserBaseController;
import com.izu.mrcar.controller.maintain.garage.CustomCellWriteHandler;
import com.izu.mrcar.controller.maintain.garage.MaintainImportDTO;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.perm.UserDataPermUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.dto.CompanyDepartmentDTO;
import com.izu.user.dto.DepartmentDropdownItemDTO;
import com.izu.user.dto.DepartmentTrajectoryItemDTO;
import com.izu.user.dto.ImportErrorDTO;
import com.izu.user.dto.company.CompanyDepartmentImportDTO;
import com.izu.user.dto.dept.*;
import com.izu.user.dto.driver.BatchDTO;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.enums.perm.ClientDataPermTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

import static com.izu.user.config.consts.UserUrlCenter.CLIENT_DEPARTMENT_PERM_TREE;

/**
 * <AUTHOR>
 * @date 2021/7/19 1:49 下午
 */
@RestController
@Api(tags = "权限管理")
@Slf4j
public class CompanyDepartmentController extends UserBaseController {

    @ApiOperation(value = "部门信息-列表")
    @RequestMapping(value = UserUrlCenter.CLIENT_DEPARTMENT_TREE,method = {RequestMethod.GET,RequestMethod.POST})
    @RequestFunction(functionName = "部门信息-列表")
    @ApiImplicitParams({
            @ApiImplicitParam(name="status",value="部门状态 1 :启用 ，不传时：返回启用和禁用",paramType="form"),
            @ApiImplicitParam(name="companyId",value="公司id,不传取当前登录公司，传了取该公司",paramType="form"),
    })
    public RestResponse<List<CompanyDepartmentDTO>> departmentTree(Byte status,Integer companyId){
        final LoginBaseInfo loginInfo = LoginSessionUtil.getBaseLoginInfo();
        UserRestLocator locator = new UserRestLocator();
        Map<String, Object> params = new HashMap<>();
        params.put("status",status);
        if(companyId ==null){
            params.put("companyId",loginInfo.obtainBelongCompanyId());
        }else{
            params.put("companyId",companyId);
        }

        String restUrl = locator.getRestUrl(UserUrlCenter.CLIENT_DEPARTMENT_TREE);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, params, null);
    }


    @ApiOperation(value = "查询用户部门信息-列表树")
    @RequestMapping(value = UserUrlCenter.CLIENT_USER_DEPARTMENT_TREE,method = {RequestMethod.GET,RequestMethod.POST})
    @RequestFunction(functionName = "查询用户部门信息-列表树")
    @ApiImplicitParams({
            @ApiImplicitParam(name="status",value="部门状态 1 :启用 ，不传时：返回启用和禁用",paramType="form"),
            @ApiImplicitParam(name="companyId",value="公司id,不传取当前登录公司，传了取该公司",paramType="form"),
    })
    public RestResponse<List<CompanyDepartmentDTO>> getUserDepartmentTree(Byte status,Integer companyId){
        final LoginBaseInfo loginInfo = LoginSessionUtil.getBaseLoginInfo();
        UserRestLocator locator = new UserRestLocator();
        Map<String, Object> params = new HashMap<>();
        params.put("status",status);
        params.put("loginUserId",loginInfo.obtainBaseInfo().getStaffId());
        if(companyId ==null){
            params.put("companyId",loginInfo.obtainBelongCompanyId());
        }else{
            params.put("companyId",companyId);
        }

        String restUrl = locator.getRestUrl(UserUrlCenter.CLIENT_USER_DEPARTMENT_TREE);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, params, null);
    }



    @ApiOperation(value = "客户端-部门信息-列表")
    @RequestMapping(value = UserUrlCenter.CLIENT_DEPARTMENT_LIST,method = {RequestMethod.GET,RequestMethod.POST})
    @RequestFunction(functionName = "客户端-部门信息-列表")
    public RestResponse<List<CompanyDepartmentDTO>> departmentTree(DeptQueryReqDTO reqDTO){
        UserRestLocator locator = new UserRestLocator();
        Map<String, Object> params = new HashMap<>();
        UserDataPermUtil.setClientDataPerm(reqDTO);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY,reqDTO);
        String restUrl = locator.getRestUrl(UserUrlCenter.CLIENT_DEPARTMENT_LIST);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, params, null);
    }


    @ApiOperation(value = "部门管理-员工列表")
    @RequestFunction(functionName = "部门管理-员工列表")
    @PostMapping(UserUrlCenter.CLIENT_DEPARTMENT_MANAGE_STAFF_LIST)
    public RestResponse<PageDTO<DeptStaffListRespDTO>> staffList(@RequestBody DeptStaffListReqDTO reqDTO) {
        return postBodyWithLogin(UserUrlCenter.CLIENT_DEPARTMENT_MANAGE_STAFF_LIST, reqDTO);
    }



    @PostMapping("/department/getTreeByCompanyCode")
    @RequestFunction(functionName = "部门数根据企业编码")
    @ApiOperation(value = "部门信息-获取部门下拉数据")
    @ApiImplicitParams({
            @ApiImplicitParam(name="status",value="部门状态",required=false,paramType="form"),
            @ApiImplicitParam(name="companyCode",value="企业编码",required=true,paramType="form")
    })
    public RestResponse departmentTreeByCompanyCode(Byte status,String companyCode){
        UserRestLocator locator = new UserRestLocator();
        Map<String, Object> params = new HashMap<>();
        params.put("status",status);
        params.put("companyCode",companyCode);
        String restUrl = locator.getRestUrl("/department/getTreeByCompanyCode");
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, params, null);
    }

    @RequestMapping("/department/list")
    @RequestFunction(functionName = "企业部门层级查询")
    public RestResponse departmentList(@Verify(param = "level", rule = "required") Byte level, String name){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        if (loginBaseInfo == null){
            return RestResponse.fail(RestErrorCode.HTTP_UNAUTHORIZED);
        }
        UserRestLocator locator = new UserRestLocator();
        Map<String, Object> params = new HashMap<>();
        params.put("companyId", loginBaseInfo.obtainBelongCompanyId());
        params.put("level", level);
        params.put("name", name);
        String restUrl = locator.getRestUrl("/department/list");
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, params, null);
    }

    @GetMapping("/department/save")
    @RequestFunction(functionName = "部门信息-编辑|增加下级机构")
    @ApiOperation(value = "部门信息-新增编辑")
    @ApiImplicitParams({
            @ApiImplicitParam(name="id",value="部门id",required=true,paramType="form"),
            @ApiImplicitParam(name="departmentName",value="部门名称",required=true,paramType="form"),
            @ApiImplicitParam(name="parentId",value="父级部门id",required=false,paramType="form"),
            @ApiImplicitParam(name="leaderIds",value="负责人id，多个英文逗号间隔",required=false,paramType="form"),
    })
    public RestResponse saveDepartmentInfo(DeptSaveReqDTO reqDTO) {
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        if (loginBaseInfo == null) {
            return RestResponse.fail(RestErrorCode.HTTP_UNAUTHORIZED);
        }
        return super.postWithLogin("/department/save",reqDTO);
    }


    @RequestFunction(functionName = "部门信息-禁用")
    @ApiOperation(value = "部门信息-禁用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "deptId", value = "部门id", required = true, paramType = "form")
    })
    @PostMapping(UserUrlCenter.CLIENT_DEPARTMENT_MANAGE_DISABLE)
    public RestResponse disable(@Verify(param = "deptId", rule = "required") Integer deptId) {
        final LoginBaseInfo loginInfo = LoginSessionUtil.getBaseLoginInfo();
        final HashMap<String, Object> hashMap = Maps.newHashMapWithExpectedSize(3);
        hashMap.put("deptId", deptId);
        hashMap.put("loginUserId", loginInfo.obtainBaseInfo().getStaffId());
        hashMap.put("loginUserName", loginInfo.obtainBaseInfo().getStaffName());
        RestResponse restResponse = post(UserUrlCenter.CLIENT_DEPARTMENT_MANAGE_DISABLE, hashMap);
         if(restResponse.isSuccess()){
             restResponse.setMsg("部门停用成功");
         }
         return restResponse;

    }


    @RequestFunction(functionName = "部门信息-启用")
    @ApiOperation(value = "部门信息-启用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "deptId", value = "部门id", required = true, paramType = "form")
    })
    @PostMapping(UserUrlCenter.CLIENT_DEPARTMENT_MANAGE_ENABLE)
    public RestResponse enable(@Verify(param = "deptId", rule = "required") Integer deptId) {
        final LoginBaseInfo loginInfo = LoginSessionUtil.getBaseLoginInfo();
        final HashMap<String, Object> hashMap = Maps.newHashMapWithExpectedSize(3);
        hashMap.put("deptId", deptId);
        hashMap.put("loginUserId", loginInfo.obtainBaseInfo().getStaffId());
        hashMap.put("loginUserName", loginInfo.obtainBaseInfo().getStaffName());
        RestResponse restResponse = post(UserUrlCenter.CLIENT_DEPARTMENT_MANAGE_ENABLE, hashMap);
        if(restResponse.isSuccess()){
            restResponse.setMsg("部门启用成功");
        }
        return restResponse;
    }

    @ApiOperation(value = "部门信息-带权限列表")
    @RequestMapping(value = CLIENT_DEPARTMENT_PERM_TREE,method = {RequestMethod.GET,RequestMethod.POST})
    @RequestFunction(functionName = "带权限部门信息-列表")
    public RestResponse<List<CompanyDepartmentDTO>> getDepartmentPermTree( DeptPermQueryDTO deptPermQueryDTO){
        final ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
        UserRestLocator locator = new UserRestLocator();
        Map<String, Object> params = new HashMap<>();
        deptPermQueryDTO.setCompanyId(clientLoginInfo.obtainBelongCompanyId());
        if(Objects.equals(clientLoginInfo.obtainDataPerm().getDataPermType(), ClientDataPermTypeEnum.ASSIGN_DEPT.getType())){
            Set<String> structIds= clientLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.ID).getDataCodeSet();
            if(CollUtil.isEmpty(structIds)){
                structIds= Sets.newHashSet("-1");
            }
            List<Integer> deptIds = structIds.stream().map(Integer::parseInt).collect(Collectors.toList());
            deptPermQueryDTO.setStructIds(deptIds);
        }
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, deptPermQueryDTO);
        String restUrl = locator.getRestUrl(CLIENT_DEPARTMENT_PERM_TREE);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, params, null);
    }



//    @RequestFunction(functionName = "判断层级返回部门id列表。层级1返回自己,层级大于1返回自己和自己下的子部门id")
//    @ApiOperation(value = "判断层级返回部门id列表.层级1返回自己,层级大于1返回自己和自己下的子部门id")
//    @PostMapping(UserUrlCenter.DEPARTMENT_LISTDEPTIDBYJUDGELEVEL)
//    public RestResponse listDeptIdByJudgeLevel(@RequestBody ListDeptIdByJudgeLevelReqDTO reqDTO) {
//        Map<String, Object> param = new HashMap<>();
//        param.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
//        UserRestLocator locator = new UserRestLocator();
//        String restUrl = locator.getRestUrl(UserUrlCenter.DEPARTMENT_LISTDEPTIDBYJUDGELEVEL);
//        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null);
//    }


    @RequestFunction(functionName = "获取当前登录企业下的两级部门信息")
    @ApiOperation(value = "获取当前登录企业下的两级部门信息")
    @PostMapping("/client/department/listTwoLevelDept")
    public RestResponse<List<CompanyDepartmentDTO>> listTwoLevelDept() {
        LoginBaseInfo loginInfo = LoginSessionUtil.getBaseLoginInfo();
        ListValidDeptByCompanyIdReqDTO reqDTO = new ListValidDeptByCompanyIdReqDTO();
        reqDTO.setCompanyId(loginInfo.obtainBelongCompanyId() + "");
        reqDTO.setLevelList(Lists.newArrayList(1, 2));
        Map<String, Object> param = new HashMap<>();
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        UserRestLocator locator = new UserRestLocator();
        String restUrl = locator.getRestUrl(UserUrlCenter.DEPARTMENT_LISTVALIDDEPTBYCOMPANYID);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null, CompanyDepartmentDTO.class);
    }





    /**
     * 获取部门下拉列表（带数据权限控制）
     *
     * @param deptQueryReqDTO 查询请求参数
     *                        必传字段: loginCompanyId（当前登录公司ID）
     *                        数据权限相关必传条件:
     *                        - 当 dataPermType=ASSIGN_DEPT 时，dataPermIdSet 必须非空
     *                        - 当 dataPermType=ONESELF 时，loginUseId 必须存在
     * @return 部门下拉列表数据，包含层级路径信息（平铺样式）
     * @throws IllegalArgumentException 当数据权限参数不合法时抛出
     * @throws NullPointerException     当未传递 loginCompanyId 时抛出
     */
    @PostMapping(UserUrlCenter.CLIENT_DEPARTMENT_DROPDOWN_LIST)
    public RestResponse<DepartmentDropdownItemDTO> getDeptDropdownList(@RequestBody DeptQueryReqDTO deptQueryReqDTO) {
        return postBodyWithLogin(UserUrlCenter.CLIENT_DEPARTMENT_DROPDOWN_LIST, deptQueryReqDTO);
    }





    @RequestFunction(functionName = "轨迹染色-左侧部门树查看")
    @ApiOperation(value = "轨迹染色-左侧部门树查看")
    @PostMapping(UserUrlCenter.CLIENT_DEPARTMENT_TRAJECTORY_LIST)
    public RestResponse<List<DepartmentTrajectoryItemDTO>> getDeptTrajectoryList(@RequestBody DeptTrajectoryQueryReqDTO deptTrajectoryQueryReqDTO) {
        return postBodyWithLogin(UserUrlCenter.CLIENT_DEPARTMENT_TRAJECTORY_LIST, deptTrajectoryQueryReqDTO);
    }
    @RequestFunction(functionName = "部门信息-批量设置角色和数据权限")
    @ApiOperation(value = "部门信息-批量设置角色和数据权限")
    @PostMapping(UserUrlCenter.CLIENT_BATCH_UPDATE_ROLE_AND_DATA_PERMISSION)
    public RestResponse<Boolean> batchUpdateRoleAndDataPermission(@RequestBody RoleAndDataPermissionReqDTO roleAndDataPermissionReqDTO) {
        return postBodyWithLogin(UserUrlCenter.BATCH_UPDATE_ROLE_AND_DATA_PERMISSION, roleAndDataPermissionReqDTO);
    }

    @PostMapping(UserUrlCenter.CLIENT_DEPARTMENT_DOWNLOAD_IMPORT_TEMPLATE)
    @RequestFunction(functionName = "部门信息-导入部门模板下载")
    @ApiOperation("部门信息-导入部门模板下载")
    public void downloadImportTemplate(HttpServletResponse response) throws Exception {
        // 导出模板名称
        String fileName = "部门导入模板";

        // 设置响应类型（推荐设置为xlsx标准MIME）
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");

        // 防止中文乱码（兼容大部分浏览器）
        String fileNameEncode = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''"  + fileNameEncode + ExcelTypeEnum.XLSX.getValue());

        // 模板数据
        List<MaintainImportDTO> list = new ArrayList<>();

        // 写入 Excel（注意：不要在写入前有任何输出）
        try (ServletOutputStream out = response.getOutputStream()) {
            EasyExcelFactory.write(out)
                    .registerWriteHandler(new CustomCellWriteHandler())
                    .registerWriteHandler(new SimpleRowHeightStyleStrategy((short) 35, (short) 22))
                    .head(CompanyDepartmentImportDTO.class)
                    .excelType(ExcelTypeEnum.XLSX)
                    .sheet("导入部门")
                    .doWrite(list);
        } catch (Exception e) {
            log.error("导出Excel失败", e);
            throw e;
        }
    }

    @PostMapping(UserUrlCenter.CLIENT_DEPARTMENT_BATCH_INSERT)
    @RequestFunction(functionName = "部门信息-批量导入部门")
    @ApiOperation("部门信息-批量导入部门")
    @JrdApiDoc(simpleDesc = "上传Excel批量更新", detailDesc = "", author = "丁伟兵", resDataDesc = "是否成功，失败时返回错误的行数和错误信息。", resDataClass = ImportErrorDTO.class)
    public RestResponse batchInsert(
            @Verify(param = "excelUrl", rule = "required") String excelUrl,
            HttpServletRequest request,
            HttpServletResponse response) {
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.DEPARTMENT_BATCH_INSERT);
        Map<String, Object> paraMap = new HashMap<>(1);
        ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
        DepartmentImportReqDTO reqDTO = new DepartmentImportReqDTO();
        reqDTO.setLoginCompanyId(clientLoginInfo.obtainBelongCompanyId());
        reqDTO.setExcelUrl(excelUrl);
        reqDTO.setLoginUserId(clientLoginInfo.getBaseInfo().getStaffId());
        reqDTO.setLoginUserName(clientLoginInfo.getBaseInfo().getStaffName());
        paraMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paraMap, null, BatchDTO.class);
    }

}
