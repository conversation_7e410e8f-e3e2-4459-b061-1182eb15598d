package com.izu.mrcar.controller.workflow.task;

import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.workflow.common.MrcarWorkflowRestLocator;
import com.izu.mrcar.workflow.common.constants.MrCarWorkflowResturl;
import com.izu.mrcar.workflow.common.dto.activity.BpmActivityRespDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.HashMap;
import java.util.List;

@RestController
@Validated
@Api(tags = "工作流")
public class BpmActivityController {

    @PostMapping(MrCarWorkflowResturl.BPM_ACTIVITY_LIST)
    @RequestFunction(functionName = "生成指定流程实例的高亮流程图")
    @ApiOperation(value = "生成指定流程实例的高亮流程图",notes = "作者：贺新春")
    @ApiImplicitParam(name = "processInstanceId",value = "流程实例的编号",required = true)
    public RestResponse<List<BpmActivityRespDTO>> getActivityList(String processInstanceId) {
        HashMap<String,Object> params = new HashMap<>(1);
        params.put("processInstanceId",processInstanceId);
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_ACTIVITY_LIST);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, url, params, null);
    }
}
