package com.izu.mrcar.controller.workflow;

import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.workflow.common.MrcarWorkflowRestLocator;
import com.izu.mrcar.workflow.common.constants.MrCarWorkflowResturl;
import com.izu.mrcar.workflow.common.dto.group.*;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@Validated
@Api(tags = "工作流")
public class BpmUserModelController {

    @PostMapping(MrCarWorkflowResturl.BPM_USER_MODEL_CREATE)
    @RequestFunction(functionName = "创建部门审批组")
    @ApiOperation(value = "创建部门审批组",notes = "作者：丁伟兵")
    public RestResponse<Boolean> createUserModel(@RequestBody BpmUserModelCreateReqDTO createReq) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        createReq.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        createReq.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        createReq.setLoginUserId(baseInfo.getStaffId());
        createReq.setLoginUserName(baseInfo.getStaffName());
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_USER_MODEL_CREATE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, createReq);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(MrCarWorkflowResturl.BPM_USER_MODEL_UPDATE)
    @RequestFunction(functionName = "更新部门审批组")
    @ApiOperation(value = "更新部门审批组",notes = "作者：丁伟兵")
    public RestResponse<Boolean> updateUserModel(@RequestBody BpmUserModelUpdateReqDTO updateReq) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        updateReq.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        updateReq.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        updateReq.setLoginUserId(baseInfo.getStaffId());
        updateReq.setLoginUserName(baseInfo.getStaffName());
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_USER_MODEL_UPDATE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, updateReq);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(MrCarWorkflowResturl.BPM_USER_MODEL_DELETE)
    @RequestFunction(functionName = "删除部门审批组")
    @ApiOperation(value = "删除部门审批组",notes = "作者：丁伟兵")
    public RestResponse<Boolean> deleteUserModel(@RequestBody BpmUserModelUpdateReqDTO updateReq) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        updateReq.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        updateReq.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        updateReq.setLoginUserId(baseInfo.getStaffId());
        updateReq.setLoginUserName(baseInfo.getStaffName());
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_USER_MODEL_DELETE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, updateReq);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(MrCarWorkflowResturl.BPM_USER_MODEL_PAGE)
    @RequestFunction(functionName = "获得部门审批组分页")
    @ApiOperation(value = "部门审批组分页列表",notes = "作者：丁伟兵")
    public RestResponse<PageDTO<BpmUserModelPageDTO>> getUserModelPage(@RequestBody BpmUserModelPageReqDTO pageDTO) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        pageDTO.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        pageDTO.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        pageDTO.setLoginUserId(baseInfo.getStaffId());
        pageDTO.setLoginUserName(baseInfo.getStaffName());
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_USER_MODEL_PAGE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, pageDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(MrCarWorkflowResturl.BPM_USER_MODEL_SEARCH)
    @RequestFunction(functionName = "获得流程定义列表")
    @ApiOperation(value = "获得流程定义列表",notes = "作者：丁伟兵")
    public RestResponse<List<BpmUserModelSearchRespDTO>> getProcessDefinitionList(@RequestBody BpmUserModelSearchReqDTO listReqVO) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        listReqVO.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        listReqVO.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        listReqVO.setLoginUserId(baseInfo.getStaffId());
        listReqVO.setLoginUserName(baseInfo.getStaffName());
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_USER_MODEL_SEARCH);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, listReqVO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }


}
