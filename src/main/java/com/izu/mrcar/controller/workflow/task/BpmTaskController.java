package com.izu.mrcar.controller.workflow.task;

import com.izu.config.dto.busroute.BusRouteDTO;
import com.izu.config.dto.busroute.BusRouteDetailDTO;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.utils.EmojiUtils;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.workflow.common.MrcarWorkflowRestLocator;
import com.izu.mrcar.workflow.common.constants.MrCarWorkflowResturl;
import com.izu.mrcar.workflow.common.dto.instance.BpmProcessInstanceMyPageReqDTO;
import com.izu.mrcar.workflow.common.dto.instance.BpmProcessInstancePageItemClientExport;
import com.izu.mrcar.workflow.common.dto.task.*;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
/**
 * 工作流接口
 */
@RestController
@Validated
@Api(tags = "工作流")
public class BpmTaskController {

    @PostMapping(MrCarWorkflowResturl.BPM_TASK_TODO_PAGE)
    @RequestFunction(functionName = "获取 Todo 待办任务分页")
    @ApiOperation(value = "获取 Todo 待办任务分页", notes = "作者：贺新春")
    public RestResponse<PageDTO<BpmTaskTodoPageItemRespDTO>> getTodoTaskPage(@RequestBody BpmTaskTodoPageReqDTO pageVO) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        pageVO.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        pageVO.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        pageVO.setLoginUserId(baseInfo.getStaffId());
        pageVO.setLoginUserName(baseInfo.getStaffName());
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_TASK_TODO_PAGE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, pageVO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(MrCarWorkflowResturl.BPM_TASK_DONE_PAGE)
    @RequestFunction(functionName = "获取 Done 已办任务分页")
    @ApiOperation(value = "获取 Done 已办任务分页", notes = "作者：贺新春")
    public RestResponse<PageDTO<BpmTaskDonePageItemRespDTO>> getDoneTaskPage(@RequestBody BpmTaskDonePageReqDTO pageVO) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        pageVO.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        pageVO.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        pageVO.setLoginUserId(baseInfo.getStaffId());
        pageVO.setLoginUserName(baseInfo.getStaffName());
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_TASK_DONE_PAGE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, pageVO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(MrCarWorkflowResturl.BPM_TASK_LIST_BY_PROCESS_INSTANCE_ID)
    @RequestFunction(functionName = "获得指定流程实例的任务列表")
    @ApiOperation(value = "获得指定流程实例的任务列表", notes = "作者：贺新春")
    @ApiImplicitParam(name = "processInstanceId", value = "流程实例编号", required = true)
    public RestResponse<List<BpmTaskRespDTO>> getTaskListByProcessInstanceId(String processInstanceId) {
        HashMap<String, Object> params = new HashMap<>(1);
        params.put("processInstanceId", processInstanceId);
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_TASK_LIST_BY_PROCESS_INSTANCE_ID);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, url, params, null);
    }

    @PostMapping(MrCarWorkflowResturl.BPM_TASK_APPROVE)
    @RequestFunction(functionName = "通过任务")
    @ApiOperation(value = "通过任务", notes = "作者：贺新春")
    public RestResponse<Boolean> approveTask(@RequestBody BpmTaskApproveReqDTO reqVO) {
        if (EmojiUtils.containsEmoji(reqVO.getReason())) {
            return RestResponse.fail(ErrorCode.DO_NOT_INPUT_CONTAINS_EMOJI_REASON);
        }
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        reqVO.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        reqVO.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        reqVO.setLoginUserId(baseInfo.getStaffId());
        reqVO.setLoginUserName(baseInfo.getStaffName());
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_TASK_APPROVE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqVO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    /**
     * 通过并打开下一条
     *
     * @param reqVO
     * @return BpmTaskAppPageRespDTO
     */
    @PostMapping(MrCarWorkflowResturl.BPM_TASK_APPROVE_GET_NEXT_TASK)
    public RestResponse<BpmTaskAppPageRespDTO> approveGetNextTask(@RequestBody BpmTaskApproveReqDTO reqVO) {
        if (EmojiUtils.containsEmoji(reqVO.getReason())) {
            return RestResponse.fail(ErrorCode.DO_NOT_INPUT_CONTAINS_EMOJI_REASON);
        }
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        reqVO.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        reqVO.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        reqVO.setLoginUserId(baseInfo.getStaffId());
        reqVO.setLoginUserName(baseInfo.getStaffName());
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_TASK_APPROVE_GET_NEXT_TASK);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqVO);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, BpmTaskAppPageRespDTO.class);
    }




    @PostMapping(MrCarWorkflowResturl.BPM_TASK_REJECT)
    @RequestFunction(functionName = "不通过任务")
    @ApiOperation(value = "不通过任务", notes = "作者：贺新春")
    public RestResponse<Boolean> rejectTask(@RequestBody BpmTaskRejectReqDTO reqVO) {
        if (EmojiUtils.containsEmoji(reqVO.getReason())) {
            return RestResponse.fail(ErrorCode.DO_NOT_INPUT_CONTAINS_EMOJI_REASON);
        }
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        reqVO.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        reqVO.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        reqVO.setLoginUserId(baseInfo.getStaffId());
        reqVO.setLoginUserName(baseInfo.getStaffName());
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_TASK_REJECT);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqVO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(MrCarWorkflowResturl.BPM_TASK_UPDATE_ASSIGNEE)
    @RequestFunction(functionName = "更新任务的负责人")
    @ApiOperation(value = "更新任务的负责人", notes = "作者：贺新春")
    public RestResponse<Boolean> updateTaskAssignee(@RequestBody BpmTaskUpdateAssigneeReqDTO reqVO) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        reqVO.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        reqVO.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        reqVO.setLoginUserId(baseInfo.getStaffId());
        reqVO.setLoginUserName(baseInfo.getStaffName());
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_TASK_UPDATE_ASSIGNEE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqVO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(MrCarWorkflowResturl.BPM_TASK_GET_HISTORY_TASK)
    @RequestFunction(functionName = "获取历史任务节点")
//    @ApiOperation("获取历史任务节点")
    public RestResponse<List<BpmHistoricTaskInstanceRespDTO>> getHistoryTasKInstance(@RequestBody BpmHistoricTaskInstanceReqDTO reqVO) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        reqVO.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        reqVO.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        reqVO.setLoginUserId(baseInfo.getStaffId());
        reqVO.setLoginUserName(baseInfo.getStaffName());
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_TASK_GET_HISTORY_TASK);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqVO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(MrCarWorkflowResturl.BPM_TASK_GET_OPERABLE_ITEM)
    @RequestFunction(functionName = "获取审批任务支持的操作项")
    @ApiOperation("获取审批任务支持的操作项")
    public RestResponse<BpmTaskOperableItemRespDTO> getOperableItem(@RequestBody BpmTaskOperableItemReqDTO reqVO) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        reqVO.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        reqVO.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        reqVO.setLoginUserId(baseInfo.getStaffId());
        reqVO.setLoginUserName(baseInfo.getStaffName());
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_TASK_GET_OPERABLE_ITEM);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqVO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(MrCarWorkflowResturl.BPM_TASK_BACK)
    @RequestFunction(functionName = "退回任务")
    @ApiOperation("退回任务")
    public RestResponse<Boolean> backTask(@RequestBody BpmTaskBackReqDTO reqVO) {
        if (EmojiUtils.containsEmoji(reqVO.getReason())) {
            return RestResponse.fail(ErrorCode.DO_NOT_INPUT_CONTAINS_EMOJI_REASON);
        }
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        reqVO.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        reqVO.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        reqVO.setLoginUserId(baseInfo.getStaffId());
        reqVO.setLoginUserName(baseInfo.getStaffName());
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_TASK_BACK);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqVO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(MrCarWorkflowResturl.BPM_TASK_TODO_PAGE_EXPORT)
    @RequestFunction(functionName = "待办任务-导出-客户端")
    @ApiOperation(value = "待办任务-导出-运营端", notes = "作者：dingweibing")
    @ExportExcelWeb(fileName = "待办任务.xlsx", filePath = "/data/logs/excel/tmp", sheet = "待办任务",
            c = BpmTaskTodoPageItemExport.class, isAsync = false
    )
    public PageDTO exportToDoPageList(@RequestBody BpmTaskTodoPageReqDTO pageVO, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response) {
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_TASK_TODO_PAGE);
        Map<String, Object> paramMap = new HashMap<>();
        pageVO.setPageSize(500);
        pageVO.setPageNum(izuEasyExcelSession.getPageNo());
        ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
        izuEasyExcelSession.setUserName(clientLoginInfo.getBaseInfo().getStaffName());
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        pageVO.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        pageVO.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        pageVO.setLoginUserId(baseInfo.getStaffId());
        pageVO.setLoginUserName(baseInfo.getStaffName());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, pageVO);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, BpmTaskTodoPageItemExport.class);
        if (restResponse != null && restResponse.isSuccess()) {
            PageDTO pageDTO = (PageDTO) restResponse.getData();
            if (pageDTO.getTotal() > 10000) {
                throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
            }
            return pageDTO;
        } else {
            return null;
        }
    }

    @PostMapping(MrCarWorkflowResturl.BPM_TASK_DONE_PAGE_EXPORT)
    @RequestFunction(functionName = "已办任务-导出-客户端")
    @ApiOperation(value = "已办任务-导出-运营端", notes = "作者：dingweibing")
    @ExportExcelWeb(fileName = "已办任务.xlsx", filePath = "/data/logs/excel/tmp", sheet = "已办任务",
            c = BpmTaskDonePageItemExport.class, isAsync = false
    )
    public PageDTO exportDonePageList(@RequestBody BpmTaskDonePageReqDTO pageVO, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response) {
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_TASK_DONE_PAGE);
        Map<String, Object> paramMap = new HashMap<>();
        pageVO.setPageSize(500);
        pageVO.setPageNum(izuEasyExcelSession.getPageNo());
        ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
        izuEasyExcelSession.setUserName(clientLoginInfo.getBaseInfo().getStaffName());
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        pageVO.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        pageVO.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        pageVO.setLoginUserId(baseInfo.getStaffId());
        pageVO.setLoginUserName(baseInfo.getStaffName());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, pageVO);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, BpmTaskDonePageItemExport.class);
        if (restResponse != null && restResponse.isSuccess()) {
            PageDTO pageDTO = (PageDTO) restResponse.getData();
            if (pageDTO.getTotal() > 10000) {
                throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
            }
            return pageDTO;
        } else {
            return null;
        }
    }
}
