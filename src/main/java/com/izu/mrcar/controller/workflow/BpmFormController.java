package com.izu.mrcar.controller.workflow;

import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.workflow.common.MrcarWorkflowRestLocator;
import com.izu.mrcar.workflow.common.constants.MrCarWorkflowResturl;
import com.izu.mrcar.workflow.common.dto.BaseDTO;
import com.izu.mrcar.workflow.common.dto.form.*;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@Validated
@Api(tags = "工作流")
public class BpmFormController {

    @PostMapping(MrCarWorkflowResturl.BPM_FORM_CREATE)
    @RequestFunction(functionName = "创建动态表单")
    @ApiOperation(value = "创建表单",notes = "作者：贺新春")
    public RestResponse<Boolean> createForm(@RequestBody BpmFormCreateReqDTO createReqDTO) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        createReqDTO.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        createReqDTO.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        createReqDTO.setLoginUserId(baseInfo.getStaffId());
        createReqDTO.setLoginUserName(baseInfo.getStaffName());
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_FORM_CREATE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, createReqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(MrCarWorkflowResturl.BPM_FORM_UPDATE)
    @RequestFunction(functionName = "更新动态表单")
    @ApiOperation(value = "更新表单",notes = "作者：贺新春")
    public RestResponse<Boolean> updateForm(@RequestBody BpmFormUpdateReqDTO updateReq) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        updateReq.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        updateReq.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        updateReq.setLoginUserId(baseInfo.getStaffId());
        updateReq.setLoginUserName(baseInfo.getStaffName());
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_FORM_UPDATE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, updateReq);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(MrCarWorkflowResturl.BPM_FORM_DELETE)
    @RequestFunction(functionName = "删除动态表单")
    @ApiOperation(value = "删除表单",notes = "作者：贺新春")
    public RestResponse<Boolean> deleteForm(@RequestBody BpmFormUpdateReqDTO updateReq) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        updateReq.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        updateReq.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        updateReq.setLoginUserId(baseInfo.getStaffId());
        updateReq.setLoginUserName(baseInfo.getStaffName());
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_FORM_DELETE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, updateReq);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(MrCarWorkflowResturl.BPM_FORM_GET)
    @RequestFunction(functionName = "获得动态表单")
    @ApiOperation(value = "表单详情",notes = "作者：贺新春")
    public RestResponse<BpmFormRespDTO> getForm(@RequestBody BpmFormUpdateReqDTO updateReq) {
        HashMap<String,Object> params = new HashMap<>(1);
        params.put("id",updateReq.getId());
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_FORM_GET);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, url, params, null);
    }

    @PostMapping(MrCarWorkflowResturl.BPM_FORM_LIST_ALL_SIMPLE)
    @RequestFunction(functionName = "获得动态表单的精简列表")
    @ApiOperation(value = "表单精简列表",notes = "作者：贺新春")
    public RestResponse<List<BpmFormSimpleRespDTO>> getSimpleForms(@RequestBody BaseDTO baseDTO) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        baseDTO.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        baseDTO.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        baseDTO.setLoginUserId(baseInfo.getStaffId());
        baseDTO.setLoginUserName(baseInfo.getStaffName());
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_FORM_LIST_ALL_SIMPLE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, baseDTO);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, BpmFormSimpleRespDTO.class);
    }

    @PostMapping(MrCarWorkflowResturl.BPM_FORM_PAGE)
    @RequestFunction(functionName = "获得动态表单分页")
    @ApiOperation(value = "表单分页列表",notes = "作者：贺新春")
    public RestResponse<PageDTO<BpmFormRespDTO>> getFormPage(@RequestBody BpmFormPageReqDTO pageReqDTO) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        pageReqDTO.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        pageReqDTO.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        pageReqDTO.setLoginUserId(baseInfo.getStaffId());
        pageReqDTO.setLoginUserName(baseInfo.getStaffName());
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_FORM_PAGE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, pageReqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

}
