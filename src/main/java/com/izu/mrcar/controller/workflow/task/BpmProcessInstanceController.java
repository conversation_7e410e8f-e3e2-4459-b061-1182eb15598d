package com.izu.mrcar.controller.workflow.task;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.google.common.collect.Lists;
import com.izu.business.util.pdf.PdfCreator;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.utils.DateUtil;
import com.izu.mrcar.utils.EmojiUtils;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.workflow.common.MrcarWorkflowRestLocator;
import com.izu.mrcar.workflow.common.constants.MrCarWorkflowResturl;
import com.izu.mrcar.workflow.common.dto.bpm.ExportProcessInstanceFormReqDTO;
import com.izu.mrcar.workflow.common.dto.copy.CopyProcessExport;
import com.izu.mrcar.workflow.common.dto.copy.ProviderCopyProcessReqDTO;
import com.izu.mrcar.workflow.common.dto.form.BpmFormExportDTO;
import com.izu.mrcar.workflow.common.dto.instance.*;
import com.izu.mrcar.workflow.common.dto.process.req.AppBpmProcessInstanceDetailReqDTO;
import com.izu.mrcar.workflow.common.dto.process.resp.AppBpmProcessInstanceDetailRespDTO;
import com.izu.mrcar.workflow.common.utils.BpmFormExportUtil;
import com.izu.user.dto.CustomerDTO;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.restApi.OrderUserApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@RestController
@Validated
@Api(tags = "工作流")
public class BpmProcessInstanceController {


    @PostMapping(MrCarWorkflowResturl.BPM_PROCESS_INSTANCE_MY_PAGE)
    @RequestFunction(functionName = "获得我的实例分页列表")
    @ApiOperation(value = "获得我的实例分页列表",notes = "作者：贺新春")
    public RestResponse<PageDTO<BpmProcessInstancePageItemRespDTO>> getMyProcessInstancePage(@RequestBody BpmProcessInstanceMyPageReqDTO pageReqVO) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        pageReqVO.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        pageReqVO.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        pageReqVO.setLoginUserId(baseInfo.getStaffId());
        pageReqVO.setLoginUserName(baseInfo.getStaffName());
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_PROCESS_INSTANCE_MY_PAGE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, pageReqVO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(MrCarWorkflowResturl.BPM_PROCESS_INSTANCE_CREATE)
    @RequestFunction(functionName = "新建流程实例")
    @ApiOperation(value = "新建流程实例",notes = "作者：贺新春")
    public RestResponse<String> createProcessInstance( @RequestBody BpmProcessInstanceCreateReqDTO createReqVO) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        createReqVO.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        createReqVO.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        createReqVO.setLoginUserId(baseInfo.getStaffId());
        createReqVO.setLoginUserName(baseInfo.getStaffName());
        //根据当前登录人id获取部门信息
        CustomerDTO customerById = OrderUserApi.getCustomerById(baseInfo.getStaffId());
        if(customerById!=null){
            createReqVO.setLoginDeptId(customerById.getStructId());
            createReqVO.setLoginDeptName(customerById.getStructName());
        }
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_PROCESS_INSTANCE_CREATE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, createReqVO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(MrCarWorkflowResturl.BPM_PROCESS_INSTANCE_GET)
    @RequestFunction(functionName = "获得指定流程实例")
    @ApiOperation(value = "获得指定流程实例",notes = "作者：贺新春")
    @ApiImplicitParam(name = "id",value = "流程编号",required = true)
    public RestResponse<BpmProcessInstanceRespDTO> getProcessInstance(String id) {
        HashMap<String,Object> params = new HashMap<>(1);
        params.put("id",id);
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_PROCESS_INSTANCE_GET);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, url, params, null);
    }

    @PostMapping(MrCarWorkflowResturl.BPM_PROCESS_INSTANCE_CANCEL)
    @RequestFunction(functionName = "取消流程实例")
    @ApiOperation(value = "取消流程实例",notes = "作者：贺新春")
    public RestResponse<Boolean> cancelProcessInstance(@RequestBody BpmProcessInstanceCancelReqDTO cancelReqVO) {
        if(EmojiUtils.containsEmoji(cancelReqVO.getReason())){
            return RestResponse.fail(ErrorCode.DO_NOT_INPUT_CONTAINS_EMOJI_REASON);
        }
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        cancelReqVO.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        cancelReqVO.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        cancelReqVO.setLoginUserId(baseInfo.getStaffId());
        cancelReqVO.setLoginUserName(baseInfo.getStaffName());
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_PROCESS_INSTANCE_CANCEL);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, cancelReqVO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping("/bpm/processInstance/exportForm")
    @RequestFunction(functionName = "导出流程实例表单信息")
    @ApiOperation(value = "导出流程实例表单信息",notes = "作者：叶鹏鹏")
    public RestResponse exportProcessInstanceForm(@RequestBody ExportProcessInstanceFormReqDTO reqDTO,
                                                        HttpServletResponse response) {

        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        AppBpmProcessInstanceDetailReqDTO reqDto = new AppBpmProcessInstanceDetailReqDTO();
        reqDto.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        reqDto.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        reqDto.setLoginUserId(baseInfo.getStaffId());
        reqDto.setLoginUserName(baseInfo.getStaffName());
        reqDto.setProcessInstanceId(reqDTO.getProcessInstanceId());
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.APP_BPM_PROCESS_INSTANCE_DETAIL_GET);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDto);
        RestResponse<AppBpmProcessInstanceDetailRespDTO> resp = RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, AppBpmProcessInstanceDetailRespDTO.class);
        List<BpmFormExportDTO> forms = Lists.newArrayList();
        if(resp.isSuccess() && Objects.nonNull(resp.getData())){
            forms = BpmFormExportUtil.buildBpmFormExportDTO(Lists.newArrayList(resp.getData()));
            try {
                response.setContentType("application/pdf;charset=gbk");
                response.setCharacterEncoding("utf-8");
                String fileName = System.currentTimeMillis() + "";
                fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
                response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);
                response.setHeader("Download-Filename", fileName);
            } catch (IOException e) {
                log.warn("processInstanceId:{} exportProcessInstanceForm exception", reqDTO.getProcessInstanceId(), e);
            }
        }
        try {
            PdfCreator pdfCreator =
                    BpmFormExportUtil.builderPdfCreator(forms, baseInfo.getStaffName(), DateUtil.format(new Date(), DateUtil.TIMESTAMP_CHINA));
            pdfCreator.export(response.getOutputStream());
        } catch (IOException e) {
            log.warn("processInstanceId:{} exportProcessInstanceForm exception", reqDTO.getProcessInstanceId(), e);
        }
        return null;
    }

    @PostMapping(MrCarWorkflowResturl.BPM_PROCESS_INSTANCE_MY_PAGE_EXPORT)
    @RequestFunction(functionName = "我的流程-导出-运营端")
    @ApiOperation(value = "我的流程-导出-运营端",notes = "作者：dingweibing")
    @ExportExcelWeb(fileName = "我的流程.xlsx",filePath = "/data/logs/excel/tmp",sheet = "我的流程",
            c = BpmProcessInstancePageItemClientExport.class,isAsync = false
    )
    public PageDTO exportCopyProcessList(@RequestBody BpmProcessInstanceMyPageReqDTO pageReqVO, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response){
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_PROCESS_INSTANCE_MY_PAGE);
        Map<String, Object> paramMap = new HashMap<>();
        pageReqVO.setPageSize(500);
        pageReqVO.setPageNum(izuEasyExcelSession.getPageNo());
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        izuEasyExcelSession.setUserName(baseInfo.getStaffName());
        pageReqVO.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        pageReqVO.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        pageReqVO.setLoginUserId(baseInfo.getStaffId());
        pageReqVO.setLoginUserName(baseInfo.getStaffName());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, pageReqVO);
        RestResponse restResponse = RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
        if(restResponse!=null && restResponse.isSuccess()) {
            RestResponse responseData =  JSON.parseObject(restResponse.getData().toString(),RestResponse.class);
            PageDTO<BpmProcessInstancePageItemClientExport> pageDTO = JSON.parseObject(responseData.getData().toString(), new TypeReference<PageDTO<BpmProcessInstancePageItemClientExport>>() {
            });
            if (pageDTO.getTotal()>10000){
                throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
            }
            return pageDTO;
        }else{
            return null;
        }
    }

}
