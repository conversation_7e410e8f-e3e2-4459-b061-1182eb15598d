package com.izu.mrcar.controller.workflow.definition;

import com.alibaba.fastjson.JSONObject;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.workflow.common.MrcarWorkflowRestLocator;
import com.izu.mrcar.workflow.common.constants.MrCarWorkflowResturl;
import com.izu.mrcar.workflow.common.dto.process.BpmProcessDefinitionListReqDTO;
import com.izu.mrcar.workflow.common.dto.process.BpmProcessDefinitionPageItemRespDTO;
import com.izu.mrcar.workflow.common.dto.process.BpmProcessDefinitionPageReqDTO;
import com.izu.mrcar.workflow.common.dto.process.BpmProcessDefinitionRespDTO;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@Validated
@Api(tags = "工作流")
public class BpmProcessDefinitionController {

    @PostMapping(MrCarWorkflowResturl.BPM_PROCESS_DEFINITION_PAGE)
    @RequestFunction(functionName = "获得流程定义分页")
    @ApiOperation(value = "获得流程定义分页",notes = "作者：贺新春")
    public RestResponse<PageDTO<BpmProcessDefinitionPageItemRespDTO>> getProcessDefinitionPage(@RequestBody BpmProcessDefinitionPageReqDTO pageReqVO) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        pageReqVO.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        pageReqVO.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        pageReqVO.setLoginUserId(baseInfo.getStaffId());
        pageReqVO.setLoginUserName(baseInfo.getStaffName());
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_PROCESS_DEFINITION_PAGE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, pageReqVO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(MrCarWorkflowResturl.BPM_PROCESS_DEFINITION_LIST)
    @RequestFunction(functionName = "获得流程定义列表")
    @ApiOperation(value = "获得流程定义列表",notes = "作者：贺新春")
    public RestResponse<List<BpmProcessDefinitionRespDTO>> getProcessDefinitionList(@RequestBody BpmProcessDefinitionListReqDTO listReqVO) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        listReqVO.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        listReqVO.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        listReqVO.setLoginUserId(baseInfo.getStaffId());
        listReqVO.setLoginUserName(baseInfo.getStaffName());
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_PROCESS_DEFINITION_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, listReqVO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(MrCarWorkflowResturl.BPM_PROCESS_DEFINITION_GET_BPMN_XML)
    @RequestFunction(functionName = "获得流程定义的 BPMN XML")
    @ApiOperation(value = "获得流程定义的 BPMN XML",notes = "作者：贺新春")
    public RestResponse<String> getProcessDefinitionBpmnXML(@RequestBody JSONObject jsonObject) {
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_PROCESS_DEFINITION_GET_BPMN_XML);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, url, jsonObject.getInnerMap(), null);
    }
}
