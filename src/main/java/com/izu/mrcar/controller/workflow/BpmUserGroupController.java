package com.izu.mrcar.controller.workflow;

import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.workflow.common.MrcarWorkflowRestLocator;
import com.izu.mrcar.workflow.common.constants.MrCarWorkflowResturl;
import com.izu.mrcar.workflow.common.dto.BaseDTO;
import com.izu.mrcar.workflow.common.dto.group.*;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@Validated
@Api(tags = "工作流")
public class BpmUserGroupController {

    @PostMapping(MrCarWorkflowResturl.BPM_USER_GROUP_CREATE)
    @RequestFunction(functionName = "创建用户组")
    @ApiOperation(value = "创建用户组",notes = "作者：贺新春")
    public RestResponse<Boolean> createUserGroup(@RequestBody BpmUserGroupCreateReqDTO createReq) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        createReq.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        createReq.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        createReq.setLoginUserId(baseInfo.getStaffId());
        createReq.setLoginUserName(baseInfo.getStaffName());
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_USER_GROUP_CREATE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, createReq);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(MrCarWorkflowResturl.BPM_USER_GROUP_UPDATE)
    @RequestFunction(functionName = "更新用户组")
    @ApiOperation(value = "更新用户组",notes = "作者：贺新春")
    public RestResponse<Boolean> updateUserGroup(@RequestBody BpmUserGroupUpdateReqDTO updateReq) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        updateReq.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        updateReq.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        updateReq.setLoginUserId(baseInfo.getStaffId());
        updateReq.setLoginUserName(baseInfo.getStaffName());
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_USER_GROUP_UPDATE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, updateReq);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(MrCarWorkflowResturl.BPM_USER_GROUP_DELETE)
    @RequestFunction(functionName = "删除用户组")
    @ApiOperation(value = "删除用户组",notes = "作者：贺新春")
    public RestResponse<Boolean> deleteUserGroup(@RequestBody BpmUserGroupUpdateReqDTO updateReq) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        updateReq.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        updateReq.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        updateReq.setLoginUserId(baseInfo.getStaffId());
        updateReq.setLoginUserName(baseInfo.getStaffName());
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_USER_GROUP_DELETE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, updateReq);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(MrCarWorkflowResturl.BPM_USER_GROUP_GET)
    @RequestFunction(functionName = "获得用户组")
    @ApiOperation(value = "用户组详情",notes = "作者：贺新春")
    public RestResponse<BpmUserGroupRespDTO> getUserGroup(@RequestBody BpmUserGroupUpdateReqDTO updateReq) {
        HashMap<String,Object> params = new HashMap<>(1);
        params.put("id",updateReq.getId());
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_USER_GROUP_GET);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, url, params, null);
    }

    @PostMapping(MrCarWorkflowResturl.BPM_USER_GROUP_PAGE)
    @RequestFunction(functionName = "获得用户组分页")
    @ApiOperation(value = "用户组分页列表",notes = "作者：贺新春")
    public RestResponse<PageDTO<BpmUserGroupRespDTO>> getUserGroupPage(@RequestBody BpmUserGroupPageReqDTO pageDTO) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        pageDTO.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        pageDTO.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        pageDTO.setLoginUserId(baseInfo.getStaffId());
        pageDTO.setLoginUserName(baseInfo.getStaffName());
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_USER_GROUP_PAGE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, pageDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(MrCarWorkflowResturl.BPM_USER_GROUP_LIST_ALL_SIMPLE)
    @RequestFunction(functionName = "获取用户组精简信息列表")
    @ApiOperation(value = "用户组精简信息列表",notes = "作者：贺新春")
    public RestResponse<List<BpmUserGroupSimpleRespDTO>> getSimpleUserGroups() {
        BaseDTO baseDTO = new BaseDTO();
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        baseDTO.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        baseDTO.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        baseDTO.setLoginUserId(baseInfo.getStaffId());
        baseDTO.setLoginUserName(baseInfo.getStaffName());
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_USER_GROUP_LIST_ALL_SIMPLE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, baseDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

}
