package com.izu.mrcar.controller.workflow.copy;

import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.workflow.common.MrcarWorkflowRestLocator;
import com.izu.mrcar.workflow.common.constants.MrCarWorkflowResturl;
import com.izu.mrcar.workflow.common.dto.copy.CopyProcessListExport;
import com.izu.mrcar.workflow.common.dto.copy.CopyProcessReqDTO;
import com.izu.mrcar.workflow.common.dto.copy.CopyProcessRespDTO;
import com.izu.mrcar.workflow.common.dto.copy.CopyProcessSaveReqDTO;
import com.izu.mrcar.workflow.common.dto.task.BpmTaskDonePageItemExport;
import com.izu.mrcar.workflow.common.dto.task.BpmTaskDonePageReqDTO;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 抄送相关
 * @date 2024/9/3 15:35
 */
@Validated
@RestController
@Api(tags = "工作流")
public class CopyProcessController {

    @PostMapping(MrCarWorkflowResturl.BPM_PROCESS_COPY_PAGE)
    @RequestFunction(functionName = "抄送给我列表")
    @ApiOperation(value = "抄送给我列表",notes = "作者：丁伟兵")
    public RestResponse<List<CopyProcessRespDTO>> searchMyCopyList(@RequestBody CopyProcessReqDTO copyProcessReqDTO){
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_PROCESS_COPY_PAGE);
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        copyProcessReqDTO.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        copyProcessReqDTO.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        copyProcessReqDTO.setLoginUserId(baseInfo.getStaffId());
        copyProcessReqDTO.setLoginUserName(baseInfo.getStaffName());
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, copyProcessReqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }
    @PostMapping(MrCarWorkflowResturl.BPM_PROCESS_COPY)
    @RequestFunction(functionName = "抄送-流程表单")
    @ApiOperation(value = "抄送-流程表单",notes = "作者：丁伟兵")
    public RestResponse<Void> copy(@RequestBody CopyProcessSaveReqDTO copyProcessSaveReqDTO){
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_PROCESS_COPY);
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        copyProcessSaveReqDTO.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        copyProcessSaveReqDTO.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        copyProcessSaveReqDTO.setLoginUserId(baseInfo.getStaffId());
        copyProcessSaveReqDTO.setLoginUserName(baseInfo.getStaffName());
        copyProcessSaveReqDTO.setLoginMobile(baseInfo.getMobile());
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, copyProcessSaveReqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(MrCarWorkflowResturl.BPM_PROCESS_COPY_PAGE_EXPORT)
    @RequestFunction(functionName = "抄送给我-导出-客户端")
    @ApiOperation(value = "抄送给我-导出-运营端",notes = "作者：dingweibing")
    @ExportExcelWeb(fileName = "抄送给我.xlsx",filePath = "/data/logs/excel/tmp",sheet = "抄送给我",
            c = CopyProcessListExport.class,isAsync = false
    )
    public PageDTO exportCopyMyList(@RequestBody CopyProcessReqDTO pageVO, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response){
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_PROCESS_COPY_PAGE);
        Map<String, Object> paramMap = new HashMap<>();
        pageVO.setPageSize(500);
        pageVO.setPageNum(izuEasyExcelSession.getPageNo());
        ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
        izuEasyExcelSession.setUserName(clientLoginInfo.getBaseInfo().getStaffName());
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        AccountBaseInfo baseInfo = baseLoginInfo.obtainBaseInfo();
        pageVO.setLoginCompanyId(baseLoginInfo.obtainBelongCompanyId());
        pageVO.setLoginCompanyName(baseLoginInfo.obtainBelongCompanyName());
        pageVO.setLoginUserId(baseInfo.getStaffId());
        pageVO.setLoginUserName(baseInfo.getStaffName());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, pageVO);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, CopyProcessListExport.class);
        if(restResponse!=null && restResponse.isSuccess()) {
            PageDTO pageDTO = (PageDTO) restResponse.getData();
            if (pageDTO.getTotal()>10000){
                throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
            }
            return pageDTO;
        }else{
            return null;
        }
    }
}
