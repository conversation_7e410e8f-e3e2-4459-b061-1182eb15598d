package com.izu.mrcar.controller.workflow;

import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.workflow.common.MrcarWorkflowRestLocator;
import com.izu.mrcar.workflow.common.constants.MrCarWorkflowResturl;
import com.izu.mrcar.workflow.common.dto.BaseDTO;
import com.izu.mrcar.workflow.common.dto.instance.BpmProcessInstanceMyPageReqDTO;
import com.izu.mrcar.workflow.common.dto.instance.BpmProcessInstancePageItemExport;
import com.izu.mrcar.workflow.common.dto.instance.BpmProcessInstancePageItemRespDTO;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.enums.perm.ClientDataPermTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/11/1 10:52
 */
@RestController
@Validated
@Api(tags = "工作流")
public class BpmProcessInstanceClientController {

    @PostMapping(MrCarWorkflowResturl.BPM_PROCESS_INSTANCE_CLIENT_PAGE)
    @RequestFunction(functionName = "客户端查询流程实例列表")
    @ApiOperation(value = "客户端查询流程实例列表",notes = "作者：丁伟兵")
    public RestResponse<PageDTO<BpmProcessInstancePageItemRespDTO>> providerPage(@RequestBody BpmProcessInstanceMyPageReqDTO pageReqVO) {
        //数据权限不支持城市
        setClientDataPerm(pageReqVO);
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_PROCESS_INSTANCE_PROVIDER_PAGE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, pageReqVO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }

    @PostMapping(MrCarWorkflowResturl.BPM_PROCESS_INSTANCE_CLIENT_PAGE_EXPORT)
    @RequestFunction(functionName = "客户端查询流程实例列表-导出-运营端")
    @ApiOperation(value = "客户端查询流程实例列表-导出-运营端",notes = "作者：dingweibing")
    @ExportExcelWeb(fileName = "全部流程.xlsx",filePath = "/data/logs/excel/tmp",sheet = "全部流程",
            c = BpmProcessInstancePageItemExport.class,isAsync = false
    )
    public PageDTO providerPageExport(@RequestBody BpmProcessInstanceMyPageReqDTO pageReqVO, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response){
        //数据权限不支持城市
        setClientDataPerm(pageReqVO);
        String url = new MrcarWorkflowRestLocator().getRestUrl(MrCarWorkflowResturl.BPM_PROCESS_INSTANCE_PROVIDER_PAGE);
        Map<String, Object> paramMap = new HashMap<>();
        pageReqVO.setPageSize(500);
        pageReqVO.setPageNum(izuEasyExcelSession.getPageNo());
        ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
        izuEasyExcelSession.setUserName(clientLoginInfo.getBaseInfo().getStaffName());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, pageReqVO);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, BpmProcessInstancePageItemExport.class);
        if(restResponse!=null && restResponse.isSuccess()) {
            PageDTO pageDTO = (PageDTO) restResponse.getData();
            if (pageDTO.getTotal()>10000){
                throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
            }
            return pageDTO;
        }else{
            return null;
        }
    }

    public static void setClientDataPerm(BaseDTO param){
        ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
        ClientDataPermTypeEnum dataPermTypeEnum = ClientDataPermTypeEnum.getByType(clientLoginInfo.obtainDataPerm().getDataPermType());
        param.setLoginUserId(null);
        param.setDataPermType(ClientDataPermTypeEnum.SELF_COMPANY.getType());
        param.setLoginCompanyId(clientLoginInfo.obtainBelongCompanyId());
        param.setLoginSystemType(clientLoginInfo.getSystemType());
        param.setLoginCompanyCode(clientLoginInfo.obtainBelongCompanyCode());
        switch (dataPermTypeEnum){
            case SELF_COMPANY:// 本企业
                Set<String> dataCodeSet = clientLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.ID).getDataCodeSet();
                List<Integer> companyIdScope = new ArrayList<>(dataCodeSet).stream().map(Integer::valueOf).collect(Collectors.toList());
                param.setCompanyIds(companyIdScope);
                param.setDataPermCodeSet(clientLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet());
                break;
            case ASSIGN_DEPT:// 指定部门
            case ONESELF:// 本人
            case ASSIGN_CITY:// 指定城市
                param.setCompanyIds(Collections.singletonList(clientLoginInfo.getClientCompany().getCompanyId()));
                param.setDataPermCodeSet(new HashSet<>(Collections.singleton(clientLoginInfo.getClientCompany().getCompanyCode())));
                break;
        }

    }

}
