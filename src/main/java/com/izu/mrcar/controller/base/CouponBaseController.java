package com.izu.mrcar.controller.base;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.izu.MrCarCouponRestLocator;
import com.izu.dto.common.BaseDTO;
import com.izu.framework.web.rest.locator.AbstractRestLocator;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.DataPerm;
import com.izu.user.dto.staff.pc.DataPermInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date : 2023/2/14
 */
public class CouponBaseController extends BaseController {

    protected static final MrCarCouponRestLocator COUPON_REST_LOCATOR = new MrCarCouponRestLocator();

    @Override
    protected AbstractRestLocator getRestLocator() {
        return COUPON_REST_LOCATOR;
    }

    @Override
    protected Map<String, Object> buildLoginInfo(Boolean isJSON) {
        final LoginBaseInfo loginInfo = LoginSessionUtil.getBaseLoginInfo();
        final BaseDTO baseDTO = new BaseDTO();
        baseDTO.setLoginUserId(loginInfo.obtainBaseInfo().getStaffId());
        baseDTO.setLoginUserMobile(loginInfo.obtainBaseInfo().getMobile());
        baseDTO.setLoginCode(loginInfo.obtainBaseInfo().getStaffCode());
        baseDTO.setLoginUserName(loginInfo.obtainBaseInfo().getStaffName());
        baseDTO.setLoginCompanyId(loginInfo.obtainBelongCompanyId());
        baseDTO.setLoginCompanyName(loginInfo.obtainBelongCompanyName());
        baseDTO.setLoginCompanyCode(loginInfo.obtainBelongCompanyCode());
        baseDTO.setLoginSystemType(loginInfo.getSystemType());
        final DataPerm dataPerm = loginInfo.obtainDataPerm();
        if (dataPerm != null) {
            baseDTO.setDataPermType(dataPerm.getDataPermType());
            final List<DataPermInfo> dataPermInfoList = dataPerm.getDataPermInfoList();
            if(dataPermInfoList!=null) {
                baseDTO.setDataPermIdSet(dataPermInfoList.stream().map(DataPermInfo::getDataPermId).collect(Collectors.toSet()));
                baseDTO.setDataPermCodeSet(dataPermInfoList.stream().map(DataPermInfo::getDataPermCode).collect(Collectors.toSet()));
            }
        }
        final JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(baseDTO));
        //非json请求需要将层次化数据改成逗号分割字符串，否则微服务无法解析
        if (!isJSON) {
            final JSONArray dataPermIdSet = jsonObject.getJSONArray("dataPermIdSet");
            final JSONArray dataCodeSet = jsonObject.getJSONArray("dataCodeSet");
            if (dataPermIdSet != null) {
                final String idStr = dataPermIdSet.stream().map(String::valueOf).collect(Collectors.joining(","));
                jsonObject.put("dataPermIdSet", idStr);
            }
            if (dataCodeSet != null) {
                final String codeStr = dataCodeSet.stream().map(String::valueOf).collect(Collectors.joining(","));
                jsonObject.put("dataCodeSet", codeStr);
            }
        }
        return jsonObject.getInnerMap();
    }
}
