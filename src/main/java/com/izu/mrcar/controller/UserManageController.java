package com.izu.mrcar.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.alibaba.fastjson.JSONObject;
import com.izu.framework.doc.JrdApiDoc;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.dto.CustomerStructInfoExportDTO;
import com.izu.mrcar.iot.dto.BatchDTO;
import com.izu.mrcar.shiro.session.WebSessionUtil;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.config.consts.PermissionConst;
import com.izu.user.dto.*;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


@Deprecated
@RestController
@Api(tags = "权限管理")
public class UserManageController extends AbstractExcelUploadController<CustomerImportDTO, AbstractExcelUploadController.ExcelUploadResult>{
	private static final Logger logger = LoggerFactory.getLogger(UserManageController.class);

	@Value("${mrcar-user-core.host.url}")
	private String USER_HOST_URL;

	/**
	 * 员工权限管理的列表信息分页查询接口
	 * @param restObject
	 * @param request
	 * @return
	 */


	@RequestMapping(value = "/userManage/queryCustomerByPage")
//	@RequiresPermissions(value = "user_manager")
	@RequestFunction(functionName = "员工信息")
	public RestResponse queryCustomerByPage(@RequestBody JSONObject restObject, HttpServletRequest request){
		String restUrl = USER_HOST_URL + "userManage/queryCustomerByPage";
		try {
			LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
			int companyId = loginBaseInfo.obtainBelongCompanyId();
			int loginCustomerId = loginBaseInfo.obtainBaseInfo().getStaffId();//获取当前登录用户id
			restObject.put("loginCustomerId", loginCustomerId);
			restObject.put("companyId", companyId);
//			if(loginBaseInfo.obtainSimpleDataPerm().getDataPermType().equals(ClientDataPermTypeEnum.ASSIGN_DEPT.getType())){
//			    Set<String> deptIdList = loginBaseInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.ID).getDataCodeSet();
//			    if(CollectionUtils.isNotEmpty(deptIdList)) {
//					String departmentIds = StringUtils.join(",", deptIdList);
//					restObject.put("departmentIds", departmentIds);
//				}
//			}
			return RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POST, restUrl, restObject.getInnerMap(),null,RestResponse.class);
		} catch(Exception e) {
			logger.error("【用户管理】列表查询发生异常 Exception：{}",e);
			return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
		}
	}



	@RequestMapping("/userManage/queryPositionList")
	public RestResponse getPositionList(){
		try {
			LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
			Integer companyId = loginBaseInfo.obtainBelongCompanyId();
			Map<String, Object> params = new HashMap<>();
			params.put("companyId", companyId);
			UserRestLocator locator = new UserRestLocator();
			String restUrl = locator.getRestUrl("/userManage/queryPositionList");
			return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, params, null);
		}catch (Exception e){
			logger.error("查询岗位列表失败", e);
			return RestResponse.create(RestErrorCode.UNKNOWN_ERROR, "查询岗位列表失败", false, null);
		}
	}


	@RequestMapping(value = "/userManage/addCustomer")
	@RequiresPermissions(value = "customer_add")
	@RequestFunction(functionName = "新增员工")
	public RestResponse addCustomer(@RequestBody JSONObject restObject, HttpServletRequest request){
		String restUrl = USER_HOST_URL + "userManage/addCustomer";
		try {
			LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
			int companyId = loginBaseInfo.obtainBelongCompanyId();
			restObject.put("companyId", companyId);
			restObject.put("createId", loginBaseInfo.obtainBaseInfo().getStaffId());
			restObject.put("createName",loginBaseInfo.obtainBaseInfo().getStaffName());
			//调用微服务接口进行业务操作
			RestResponse response = RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POST, restUrl, restObject.getInnerMap(),null,RestResponse.class);
			return response;
		} catch(Exception e) {
			logger.error("【用户管理】添加员工发生异常 Exception：{}",e);
			return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
		}
	}
	@RequestMapping(value = "/userManage/updCustomer")
	@RequiresPermissions(value = "customer_modify")
	@RequestFunction(functionName = "编辑员工信息")
	public RestResponse updCustomer(@RequestBody JSONObject restObject, HttpServletRequest request){
		String restUrl = USER_HOST_URL + "userManage/updCustomer";
		try {
			LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
			int companyId = loginBaseInfo.obtainBelongCompanyId();
			restObject.put("companyId", companyId);
			restObject.put("updateId", loginBaseInfo.obtainBaseInfo().getStaffId());
			restObject.put("updateName", loginBaseInfo.obtainBaseInfo().getStaffName());
			//调用微服务接口进行业务操作
			return RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POST, restUrl, restObject.getInnerMap(),null,RestResponse.class);
		} catch(Exception e) {
			logger.error("【用户管理】编辑员工信息发生异常 Exception：{}",e);
			return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
		}
	}


	@RequestMapping(value = "/userManage/getCustomerDetail")
	@RequestFunction(functionName = "员工信息查询")
	public RestResponse getCustomerDetail(@RequestBody JSONObject restObject, HttpServletRequest request){
		String restUrl = USER_HOST_URL + "userManage/getCustomerDetail";
		try {
			LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
			int companyId = loginBaseInfo.obtainBelongCompanyId();
			restObject.put("companyId", companyId);
			return RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POST, restUrl, restObject.getInnerMap(),null,RestResponse.class);
		} catch(Exception e) {
			logger.error("【用户管理】获取员工详情发生异常 Exception：{}",e);
			return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
		}
	}
	@RequestMapping(value = "/userManage/deleteCustomers")
	@RequiresPermissions(value = "customer_disable")
	@RequestFunction(functionName = "禁用员工")
	public RestResponse deleteCustomers(@RequestBody JSONObject restObject, HttpServletRequest request){
		String restUrl = USER_HOST_URL + "userManage/deleteCustomers";
		try {
			LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
			int companyId = loginBaseInfo.obtainBelongCompanyId();
			restObject.put("companyId", companyId);
			//调用微服务接口进行业务操作
			return RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POST, restUrl, restObject.getInnerMap(),null,RestResponse.class);
		} catch(Exception e) {
			logger.error("【用户管理】deleteCustomers发生异常 Exception：{}",e);
			return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
		}
	}

    @RequestMapping(value = "/userManage/updStatus")
	@RequestFunction(functionName = "修改员工状态")
    public RestResponse updStatus(@RequestBody JSONObject restObject, HttpServletRequest request){
        String restUrl = USER_HOST_URL + "userManage/changeStatus";
        try {
			LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
            int companyId = loginBaseInfo.obtainBelongCompanyId();
            restObject.put("companyId", companyId);
            restObject.put("updateId",loginBaseInfo.obtainBaseInfo().getStaffId());
            restObject.put("updateName",loginBaseInfo.obtainBaseInfo().getStaffName());
            restObject.put("disableSource",1);
            //调用微服务接口进行业务操作
            RestResponse response = RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POST, restUrl, restObject.getInnerMap(),null,RestResponse.class);
            return response;
        } catch(Exception e) {
            logger.error("【用户管理】启用停用 发生异常 Exception：{}",e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }


	@RequestMapping(value = "/userManage/checkCustomers")
	public RestResponse checkCustomers(@RequestBody JSONObject restObject, HttpServletRequest request){
		String restUrl = USER_HOST_URL + "userManage/checkCustomers";
		try {
			LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
			int companyId = loginBaseInfo.obtainBelongCompanyId();
			restObject.put("companyId", companyId);
			//调用微服务接口进行业务操作
			RestResponse response = RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POST, restUrl, restObject.getInnerMap(),null,RestResponse.class);
			return response;
		} catch(Exception e) {
			logger.error("【用户管理】checkCustomers发生异常 Exception：{}",e);
			return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
		}
	}

	/**
	 * 为员工批量设置部门信息
	 * @param restObject
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/userManage/setStructForCustomers")
	@RequestFunction(functionName = "批量设置员工部门")
	public RestResponse setStructForCustomers(@RequestBody JSONObject restObject, HttpServletRequest request){
		String restUrl = USER_HOST_URL + "userManage/setStructForCustomers";
		try {
			return RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POST, restUrl, restObject.getInnerMap(),null,RestResponse.class);
		} catch(Exception e) {
			logger.error("setStructForCustomers Exception：", e);
			return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
		}
	}

	/**
	 * 批量为员工设置上级信息
	 * @param restObject
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/userManage/setSuperiorForCustomers")
	@RequestFunction(functionName = "批量设置员工上级")
	public RestResponse setSuperiorForCustomers(@RequestBody JSONObject restObject, HttpServletRequest request){
		String restUrl = USER_HOST_URL + "userManage/setSuperiorForCustomers";
		try {
			return RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POST, restUrl, restObject.getInnerMap(),null,RestResponse.class);
		} catch(Exception e) {
			logger.error("【员工管理】setSuperiorForCustomers Exception：", e);
			return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
		}
	}

	/**
	 * 为员工分配数据权限
	 * @param restObject
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/userManage/saveDataPermission")
	@RequestFunction(functionName = "设置员工数据权限")
	public RestResponse saveDataPermission(@RequestBody JSONObject restObject, HttpServletRequest request){
		String restUrl = USER_HOST_URL + "userManage/saveDataPermission";
		try {
			return RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POST, restUrl, restObject.getInnerMap(), null, RestResponse.class);
		} catch(Exception e) {
			logger.info("listForPage Exception：", e);
			return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
		}
	}

	/**
	 * 为员工分配菜单权限
	 * @param restObject
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/userManage/savePermission")
	@RequestFunction(functionName = "设置员工功能权限")
	public RestResponse savePermission(@RequestBody JSONObject restObject, HttpServletRequest request){
		String restUrl = USER_HOST_URL + "userManage/savePermission";
		try {
			LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
			restObject.put("leaderId",loginBaseInfo.obtainBaseInfo().getStaffId());//获取当前登录用户的用户id
			return RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POST, restUrl, restObject.getInnerMap(), null, RestResponse.class);
		} catch(Exception e) {
			logger.error("listForPage Exception：", e);
			return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
		}
	}

	/**
	 * 权限配置页面反显配置信息
	 * @param restObject
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/userManage/getUserInfoAndPermission")
	public RestResponse getUserInfoAndPermission(@RequestBody JSONObject restObject, HttpServletRequest request){
		String restUrl = USER_HOST_URL + "userManage/getUserInfoAndPermission";
		try {
			LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
			int loginUserId = loginBaseInfo.obtainBaseInfo().getStaffId();
			restObject.put("loginUserId",loginUserId);//获取登录人员的权限id
			restObject.put("companyId",loginBaseInfo.obtainBelongCompanyId());
			return RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POST, restUrl, restObject.getInnerMap(),null,RestResponse.class);
		} catch(Exception e) {
			logger.error("listForPage Exception：", e);
			return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
		}
	}

	@RequestMapping(value = "/userManage/batchSetDisccountProportion")
	public RestResponse batchSetDisccountProportion(@RequestBody JSONObject restObject, HttpServletRequest request){
		String restUrl = USER_HOST_URL + "userManage/batchSetDisccountProportion";
		try {
			LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
			int loginUserId = loginBaseInfo.obtainBaseInfo().getStaffId();
			//restObject.put("loginUserId",loginUserId);//获取登录人员的权限id
			return RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POST, restUrl, restObject.getInnerMap(),null,RestResponse.class);
		} catch(Exception e) {
			logger.error("listForPage Exception：", e);
			return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
		}
	}

	@RequestMapping(value = "/userManage/getUserInfoById")
	public RestResponse getUserInfoById(@RequestBody JSONObject restObject, HttpServletRequest request){
		String restUrl = USER_HOST_URL + "userManage/getUserInfoById";
		try {
			LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
			int loginUserId = loginBaseInfo.obtainBaseInfo().getStaffId();
			//restObject.put("loginUserId",loginUserId);//获取登录人员的权限id
			return RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POST, restUrl, restObject.getInnerMap(),null,RestResponse.class);
		} catch(Exception e) {
			logger.error("listForPage Exception：", e);
			return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
		}
	}

	private Set<Integer> transIdsStrToSet(String ids){
		if (org.apache.commons.lang3.StringUtils.isBlank(ids)){
			return Collections.emptySet();
		}
		String[] split = ids.split(PermissionConst.COMMA);
		Set<Integer> idSet = new HashSet<>();
		for (String str : split){
			idSet.add(Integer.valueOf(str));
		}
		return idSet;

	}


	@RequestMapping("/userManage/downloadUserTemplate")
	public void downloadMould(HttpServletRequest request, HttpServletResponse response) {
		createMould(request, response);
	}

	@RequestMapping(value = "/userManage/batchInsert")
	@JrdApiDoc(simpleDesc = "上传Excel批量更新", detailDesc = "", author = "baixiaoxuan", resDataDesc = "是否成功，失败时返回错误的行数和错误信息。", resDataClass = ImportErrorDTO.class)
	@RequestFunction(functionName = "批量导入员工")
	public RestResponse batchInsert(
			@Verify(param = "excelUrl", rule = "required") String excelUrl,
			HttpServletRequest request,
			HttpServletResponse response) {
		ExcelUploadResult eur = new ExcelUploadResult();
		return this.start(excelUrl, request, response, eur,2);
	}

	protected void createMould( HttpServletRequest request, HttpServletResponse response) {
		try (Workbook workbook = new XSSFWorkbook()){
			Sheet sheet = workbook.createSheet("员工信息");
			int rowIndex = 0;
			//创建表头
			Row row = sheet.createRow(rowIndex);
			Cell cell1 = row.createCell(rowIndex);
			cell1.setCellValue("员工姓名");
			Cell cell2 = row.createCell(++rowIndex);
			cell2.setCellValue("员工电话");
			sheet.setColumnWidth(rowIndex, 10000);// 设置宽度
			Cell cell6 = row.createCell(++rowIndex);
			cell6.setCellValue("员工工号");
			sheet.setColumnWidth(rowIndex, 10000);// 设置宽度
			Cell cell3 = row.createCell(++rowIndex);
			cell3.setCellValue("邮箱");
			sheet.setColumnWidth(rowIndex, 10000);// 设置宽度
			Cell cell4 = row.createCell(++rowIndex);
			cell4.setCellValue("所属部门");
			sheet.setColumnWidth(rowIndex, 20000);// 设置宽度
			Cell cell5 = row.createCell(++rowIndex);
			cell5.setCellValue("职位");
			sheet.setColumnWidth(rowIndex, 10000);// 设置宽度
			sheet.createFreezePane(0, 1);// 冻结首行表头
			//查询部门列表树
			LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
			if (loginBaseInfo == null){
				logger.error("获取用户登录信息异常");
				return;
			}
			UserRestLocator locator = new UserRestLocator();
			Map<String, Object> params = new HashMap<>();
			params.put("companyId", loginBaseInfo.obtainBelongCompanyId());
			String restUrl = locator.getRestUrl("/department/tree");
			RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, params, null, CompanyDepartmentDTO.class);
			if (!restResponse.isSuccess()){
				logger.error("获取公司部门树异常");
				return;
			}
			//将树状结构的部门展开为列表
			List<CompanyDepartmentDTO> departmentDTOList = treeToList(new ArrayList<>(), (List<CompanyDepartmentDTO>) restResponse.getData());
			int departmentCount = departmentDTOList.size();
			Sheet sheet1 = workbook.createSheet("部门信息");
			for (int index = 0; index < departmentCount; index++){
				row = sheet1.createRow(index);
				Cell cell = row.createCell(0, CellType.STRING);
				CompanyDepartmentDTO dto = departmentDTOList.get(index);
				String name = dto.getParentDepartmentName() == null ? dto.getDepartmentName() : dto.getParentDepartmentName() + "->" + dto.getDepartmentName();
				cell.setCellValue(name);
			}
			//默认将sheet2的第一列作为部门下拉选项
			String formula = "=部门信息!$A$1:$A$" + departmentCount;
			DataValidationHelper helper = sheet.getDataValidationHelper();
			DataValidationConstraint explicitListConstraint = helper.createFormulaListConstraint(formula);
			CellRangeAddressList addressList = new CellRangeAddressList(0, 1000, 4, 4);
			DataValidation validation = helper.createValidation(explicitListConstraint, addressList);
			validation.setSuppressDropDownArrow(true);
			validation.setShowErrorBox(true);
			sheet.addValidationData(validation);
			//B2 决定文件名(需要对不同浏览器进行兼容，尤其是Firefox)
			String filename = "员工信息导入.xlsx";//中文文件名
			String ua = request.getHeader("User-Agent");
			if( StringUtils.isEmpty(ua) ){//如果没有UA，则采用英文文件名
				filename = "employee.xlsx";
			}else if( ua.toLowerCase().indexOf("firefox")!=-1 ){//UA是Firefox
				if( HttpMethod.POST.name().equalsIgnoreCase(request.getMethod()) && request.getContentType()!=null &&  request.getContentType().toLowerCase().indexOf("application/json")>=0 ) {
					filename = URLEncoder.encode(filename, "UTF8");
				}else {
					filename = new String(filename.getBytes("UTF8"),"ISO8859-1");
				}
			}else if( ua.toLowerCase().indexOf("chrome")!=-1 ){//UA是Chrome
				filename = URLEncoder.encode(filename, "UTF8");
			}else if( ua.toLowerCase().indexOf("safari")!=-1 ){//UA是safari
				filename = URLEncoder.encode(filename, "UTF8");
			}else{//UA是IE、Chrome或其它
				filename = URLEncoder.encode(filename, "UTF8");
			}
			response.setHeader("Content-Disposition", "attachment; filename=\""+filename+"\"");
			response.setHeader("Download-Filename", URLEncoder.encode(filename, "UTF8") );//这是和前端约定的自定义响应头
			response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=gbk");
			response.setCharacterEncoding("gbk");
			workbook.write(response.getOutputStream());
		}catch (Exception e){
			logger.error("生成员工导入模板异常", e);
		}
	}


	private List<CompanyDepartmentDTO> treeToList(List<CompanyDepartmentDTO> result, List<CompanyDepartmentDTO> source){
		if (source == null || source.isEmpty()){
			return Collections.emptyList();
		}
		source.forEach( departmentDTO -> {
			result.add(departmentDTO);
			treeToList(result, departmentDTO.getSubDepartment());
		});
		return result;
	}

	@Override
	protected List<Integer> getResolveSheetIndexes() {
		return Arrays.asList(1);
	}

	@Override
	protected String getColumnMappingConfigFile(HttpServletRequest request, HttpServletResponse response) {
		return null;
	}

	@Override
	protected List<CustomerImportDTO> convertStringDataToObject(List<Map<Integer, String>> rowDatas, ExcelUploadResult excelUploadResult) {
		List<Map<Integer, String>> collect = rowDatas.stream().filter(row -> !checkFieldAllNull(row)).collect(Collectors.toList());
		return collect.stream().map( row ->{
			CustomerImportDTO dto = new CustomerImportDTO();
			int i = -1;
			dto.setCustomerName(row.get(++i));
			dto.setMobile(row.get(++i));
			dto.setCustomerNo(row.get(++i));
			dto.setEmail(row.get(++i));
			dto.setDepartmentName(row.get(++i));
			dto.setPosition(row.get(++i));
			return dto;
		}).collect(Collectors.toList());
	}
	/**
	 * 校验对象属性是否全为空，不适用于继承的对象
	 *
	 * @param obj
	 * @return
	 */
	private boolean checkFieldAllNull(Object obj) {
		Field[] declaredFields = obj.getClass().getDeclaredFields();
		boolean flag = true;
		if(obj instanceof Map){
			Map<Integer,String> map = (Map) obj;
			for(Map.Entry entry : map.entrySet()){
				if (!ObjectUtils.isEmpty(entry.getValue())) {
					flag = false;
					break;
				}
			}
			return flag;
		}
		for (Field field : declaredFields) {
			field.setAccessible(true);
			Object o = null;
			try {
				o = field.get(obj);
			} catch (IllegalAccessException e) {
				logger.error("校验对象属性值异常");
			}
			if (!ObjectUtils.isEmpty(o)) {
				flag = false;
				break;
			}
		}
		return flag;
	}

	@Override
	protected List<ExcelUploadRowError> checkExcelData(List<CustomerImportDTO> rowDatas, HttpServletRequest request, HttpServletResponse response, ExcelUploadResult excelUploadResult) {
		return null;
	}

	@Override
	protected boolean beforePersist(List<CustomerImportDTO> rowDatas, HttpServletRequest request, HttpServletResponse response, ExcelUploadResult excelUploadResult) {
		return true;
	}

	@Override
	protected RestResponse executePersist(List<CustomerImportDTO> rowDatas, HttpServletRequest request, HttpServletResponse response, ExcelUploadResult excelUploadResult) {
		LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
		Map<String, Object> paramMap = new HashMap<>(4);
		paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, rowDatas);
		paramMap.put("createId", loginBaseInfo.obtainBaseInfo().getStaffId());
		paramMap.put("createName", loginBaseInfo.obtainBaseInfo().getStaffName());
		paramMap.put("companyId", loginBaseInfo.obtainBelongCompanyId());
		String restUrl = USER_HOST_URL+"/userManage/batchInsert.json";
		return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, BatchDTO.class);
	}

	@PostMapping("/getCustomerByCompanyIdAndMobile")
	@ApiOperation("根据手机号获取员工信息")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "mobile",value = "手机号",required = true)
	})
	public RestResponse<CustomerDetailDTO> getCustomerDetail(
			@Verify(param = "mobile", rule = "") String mobile
	) {
		LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
		Integer companyId = loginBaseInfo.obtainBelongCompanyId();
		String restUrl = USER_HOST_URL + "getCustomerByCompanyIdAndMobile";
		Map<String, Object> paraMap = new HashMap<>(2);
		paraMap.put("companyId", companyId);
		paraMap.put("mobile", mobile);
		return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, CustomerDetailDTO.class);
	}


	@RequestMapping(value = "/userManage/queryCustomerInfoByPage")
//	@RequiresPermissions(value = "user_manager")
	@RequestFunction(functionName = "运营端员工信息")
	public RestResponse queryCustomerInfoByPage(@RequestBody JSONObject restObject, HttpServletRequest request){
		String restUrl = USER_HOST_URL + "userManage/queryCustomerByPage";
		try {
			return RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POST, restUrl, restObject.getInnerMap(),null,RestResponse.class);
		} catch(Exception e) {
			logger.error("【用户管理】列表查询发生异常 Exception：{}",e);
			return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
		}
	}

}
