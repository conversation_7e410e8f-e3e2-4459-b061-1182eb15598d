package com.izu.mrcar.controller.configcore;

import com.izu.config.dto.ServiceConfigCityDTO;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.LoginUser;
import com.izu.mrcar.shiro.session.WebSessionUtil;

import com.izu.user.dto.staff.pc.ClientAccountBaseInfo;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.enums.perm.ClientDataPermTypeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 企业开通城市相关接口
 * <AUTHOR>
 *
 */

@RestController
@RequestMapping("/busscity")
public class CompanyCityController {
	/**
	 * 加载配置服务mrcar-config-core访问域名及项目名
	 */
	@Value("${mrcar-config-core.host.url}")
	private String CONFIG_HOST_URL;

	@Value("${mrcar-user-core.host.url}")
	private String USER_HOST_URL;

	/**
	 * 加载商务车队的企业ID
	 */
	@Value("${bussiness.chedui.companyid}")
	private int BUSS_COMPANY_ID;

	/**
	 * 管理端城市列表
	 * @param paraMap
	 * @return
	 */
    @RequestMapping(value = "/listBussCityForPage")
	@RequiresPermissions(value = "city_manager")
	@RequestFunction(functionName = "开城列表")
    public RestResponse listBussCityForPage(@RequestBody Map<String,Object> paraMap){
    	String restUrl = CONFIG_HOST_URL + "city/listBussCityForPage";
		paraMap.put("companyId", LoginSessionUtil.getBaseLoginInfo().obtainBelongCompanyId());
    	return RestClient.requestForPage(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Map.class);
    }

    @RequestMapping(value = "/closeCity")
	@RequestFunction(functionName = "取消开城")
    public RestResponse closeCity(@RequestBody Map<String,Object> paraMap){
    	String restUrl = CONFIG_HOST_URL + "city/closeCity";
    	/*Map<String, Object> paraMap = new HashMap<String, Object>();
    	paraMap.put("pageSize", pageSize);
    	paraMap.put("pageNo", pageNo);
    	paraMap.put("provinceCode", provinceCode);
    	paraMap.put("cityCode", cityCode);
        */
    	// 获取当前登录用户信息
		ClientLoginInfo loginBaseInfo = LoginSessionUtil.getClientLoginInfo();
		paraMap.put("loginCompanyId", loginBaseInfo.obtainBelongCompanyId());
		paraMap.put("companyAttribute", loginBaseInfo.getClientCompany().getCompanyAttribute());
		paraMap.put("loginId", loginBaseInfo.getBaseInfo().getStaffId());
		paraMap.put("loginName", loginBaseInfo.getBaseInfo().getStaffName());
    	return RestClient.requestForPage(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Object.class);
    }

    @RequestMapping(value = "/openCity")
	@RequiresPermissions(value = "city_switch")
	@RequestFunction(functionName = "开通城市")
    public RestResponse openCity(@RequestBody Map<String,Object> paraMap){
    	String restUrl = CONFIG_HOST_URL + "city/openCity";
    	/*Map<String, Object> paraMap = new HashMap<String, Object>();
    	paraMap.put("pageSize", pageSize);
    	paraMap.put("pageNo", pageNo);
    	paraMap.put("provinceCode", provinceCode);
    	paraMap.put("cityCode", cityCode);
        */
    	// 获取当前登录用户信息
		ClientLoginInfo loginBaseInfo = LoginSessionUtil.getClientLoginInfo();
		paraMap.put("loginCompanyId", loginBaseInfo.obtainBelongCompanyId());
		paraMap.put("companyAttribute", loginBaseInfo.getClientCompany().getCompanyAttribute());
		paraMap.put("loginId", loginBaseInfo.getBaseInfo().getStaffId());
		paraMap.put("loginName", loginBaseInfo.getBaseInfo().getStaffName());
    	return RestClient.requestForPage(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Object.class);
    }

	@RequestMapping(value = "/getCityByCompany")
	public RestResponse getCityByCompany(@RequestBody Map<String,Object> paraMap){
		String restUrl = USER_HOST_URL + "userManage/getCityInfoOfCompany";
		// 获取当前登录用户信息
		paraMap.put("companyId", LoginSessionUtil.getBaseLoginInfo().obtainBelongCompanyId());
		return RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Map.class);
	}

	/**
	 * 查询当前企业下的所有开通业务的城市信息
	 * @param
	 * @return
	 */
	@RequestMapping(value = "/getCityByCompanyId")
	public RestResponse getCityByCompanyId(){
		String restUrl = CONFIG_HOST_URL + "city/getCityByCompanyId";
		// 获取当前登录用户信息
		Map<String,Object> paraMap = new HashMap<>();
		LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
		paraMap.put("companyId", loginBaseInfo.obtainBelongCompanyId());
		return RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, ServiceConfigCityDTO.class);
	}
	/**
	 * 查询当前企业下的所有开通业务的城市信息和用户的城市权限交集
	 * @param
	 * @return
	 */
	@RequestMapping(value = "/getCityByCompanyId_MixedDataScope")
	public RestResponse getCityByCompanyId_MixedDataScope() {
		String restUrl = CONFIG_HOST_URL + "city/getCityByCompanyId";
		// 获取当前登录用户信息
		Map<String, Object> paraMap = new HashMap<>();
		LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
		paraMap.put("companyId", loginBaseInfo.obtainBelongCompanyId());
//		String cityDataScope = "";
//		if(loginBaseInfo.obtainSimpleDataPerm().getDataPermType().equals(ClientDataPermTypeEnum.ASSIGN_CITY.getType())){
//			cityDataScope = String.join(",", loginBaseInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet());
//		}
		RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, ServiceConfigCityDTO.class);
//		if (restResponse.isSuccess()) {
//			List<ServiceConfigCityDTO> data = (List<ServiceConfigCityDTO>) restResponse.getData();
//			if (CollectionUtils.isNotEmpty(data) && StringUtils.isNotBlank(cityDataScope)) {
//				Set<String> set = Arrays.stream(cityDataScope.split(",")).collect(Collectors.toSet());
//				data.removeIf(e -> !set.contains(e.getCityCode() + ""));
//			}
//		}
		return restResponse;
	}



}
