package com.izu.mrcar.controller.configcore;

import com.izu.config.dto.*;
import com.izu.consts.ConfigURI;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.controller.base.ConfigBaseController;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;

/**
 * 企业配置项接口-客户端
 *
 * <AUTHOR> on  2024/12/18 下午8:25
 */
@RestController
@RequestMapping(value = "/client")
public class ClientCompanyConfigItemController extends ConfigBaseController {


    /**
     * 根据公司ID获取配置列表
     *
     * @param configType 配置类型 0-代表空，1-内部用车通用配置 config_type
     * @return 配置项列表
     */
    @GetMapping(value = ConfigURI.COMPANY_CONFIG_ITEM_LIST)
    public RestResponse<List<CompanyConfigItemValueDTO>> getCompanyConfigItemList(
            @Verify(param = "configType", rule = "required") Integer configType) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getClientLoginInfo();


        HashMap<String, Object> restParam = new HashMap<>();

        restParam.put("companyId", baseLoginInfo.obtainBelongCompanyId());

        restParam.put("configType", configType);

        return get(ConfigURI.COMPANY_CONFIG_ITEM_LIST, restParam);

    }



    //设置修改或者新增人信息
    private void setLastModifierInfo(CompanyConfigItemBaseDTO reqDTO) {

        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();

        reqDTO.setLastModifierCode(baseLoginInfo.obtainBaseInfo().getStaffCode());

        reqDTO.setLastModifierName(baseLoginInfo.obtainBaseInfo().getStaffName());
    }


}
