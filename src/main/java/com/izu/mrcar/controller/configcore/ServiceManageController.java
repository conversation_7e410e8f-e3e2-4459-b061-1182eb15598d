package com.izu.mrcar.controller.configcore;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.izu.config.dto.CompanyBussinessDTO;
import com.izu.config.dto.PricePolicyDTO;
import com.izu.config.dto.ServiceConfigCityDTO;
import com.izu.config.dto.ServiceConfigPolicyDTO;
import com.izu.config.errcode.MrCarConfigErrorCode;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.config.enums.BussinessTypeDictionary;
import com.izu.mrcar.config.enums.businessTypeEnum;
import com.izu.mrcar.shiro.session.WebSessionUtil;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.LoginUser;
import com.izu.mrcar.common.config.RestUrlConfig;
import com.izu.mrcar.utils.HttpPostBody;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/serviceManage")
public class ServiceManageController {
	/**
	 * 加载配置服务mrcar-config-core访问域名及项目名
	 */
	@Value("${mrcar-config-core.host.url}")
	private String CONFIG_HOST_URL;

	/**
	 * 加载商务车队的企业ID
	 */
	@Value("${bussiness.chedui.companyid}")
	private int BUSS_COMPANY_ID;


    @RequestMapping(value = "/getOnlyPolicyByParams")
    public RestResponse getOnlyPolicyByParams(Integer companyId, String serviceCode, Integer cityCode, Integer levelId){
    	String restUrl = CONFIG_HOST_URL + "serviceConfig/getOnlyPolicyByParams";
    	Map<String, Object> paraMap = new HashMap<String, Object>();
    	paraMap.put("companyId", companyId);
    	paraMap.put("cityCode", cityCode);
    	paraMap.put("serviceCode", serviceCode);
    	paraMap.put("levelId", levelId);
    	return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, PricePolicyDTO.class);
    }

    @RequestMapping("/saveCompanyServiceConfig")
    public RestResponse saveCompanyServiceConfig(@RequestBody Map<String, Object> map) {
    	// 获取当前登录用户信息
		LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
		map.put("loginCompanyId", loginBaseInfo.obtainBelongCompanyId());
    	String restUrl =  CONFIG_HOST_URL + "city/saveCompanyServiceConfig";
    	return RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POSTBODY, restUrl, map, null, RestResponse.class);
	}

	@RequestMapping("/saveCityServiceConfig")
	public RestResponse saveCityServiceConfig(@RequestBody Map<String, Object> map) {
		// 获取当前登录用户信息
		ClientLoginInfo loginBaseInfo = LoginSessionUtil.getClientLoginInfo();
		map.put("loginCompanyId", loginBaseInfo.obtainBelongCompanyId());
		map.put("companyAttribute", loginBaseInfo.getClientCompany().getCompanyAttribute());

		String restUrl =  CONFIG_HOST_URL + "city/saveCityServiceConfig";

		return HttpPostBody.requestBodyForObject(restUrl, JSONObject.toJSONString(map), null,Map.class );
	}

	@RequestMapping("/v2/saveCityServiceConfig")
	@RequiresPermissions(value = "city_open")
	@RequestFunction(functionName = "开通用车城市")
	public RestResponse saveCityServiceConfig(String cityJsonStr){
    	Map<String, Object> map = new HashMap<>();
		ClientLoginInfo loginBaseInfo = LoginSessionUtil.getClientLoginInfo();
		map.put("companyId", loginBaseInfo.obtainBelongCompanyId());
		map.put("companyAttribute", loginBaseInfo.getClientCompany().getCompanyAttribute());
		map.put("cityJsonStr", cityJsonStr); //cityJsonStr:[{"cityCode":1302},{"cityCode":1404}]
		map.put("loginId", loginBaseInfo.getBaseInfo().getStaffId());
		map.put("loginName", loginBaseInfo.getBaseInfo().getStaffName());
		return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, RestUrlConfig.getConfig().getConfigCoreUrl() + "city/v2/saveCityServiceConfig", map, null);
	}

	@RequestMapping("/getCityServiceConfig")
	public RestResponse getCityServiceConfig(@RequestBody Map<String, Object> paraMap) {
		String restUrl = CONFIG_HOST_URL + "serviceConfig/getAllServiceConfig";
		// 获取当前登录用户信息
		LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
		paraMap.put("configOwnCompanyId", loginBaseInfo.obtainBelongCompanyId());
    	return RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Map.class);
	}
	/**后台企业查询特殊价格配置**/
	@RequestMapping("/getCompanyServiceConfig")
	public RestResponse getCompanyServiceConfig(@RequestBody Map<String,Object> paraMap) {
		String restUrl = CONFIG_HOST_URL + "serviceConfig/getAllServiceConfig";
    	paraMap.put("cityCode", 0);
    	paraMap.put("isOpen", 1);
		LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
		paraMap.put("configOwnCompanyId", loginBaseInfo.obtainBelongCompanyId());
		RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Map.class);
		if(restResponse.isSuccess()==false||restResponse.getData()==null) return restResponse;
		List<Map<String, Object>> resultList =(List<Map<String, Object>>) restResponse.getData();
		if(resultList.size()<=0)  return restResponse;
		//根据PC页面展示要求，转换格式
		//1.1获取服务的集合
		Set<String> serviceCodeSet = resultList.stream().map(p->p.get("serviceCode").toString()).collect(Collectors.toSet());
		List<Map<String, Object>> result = new ArrayList<Map<String,Object>>();
		//1.2遍历服务集合，将内容按照服务聚合
		for (String serviceCode : serviceCodeSet) {
			Map<String, Object> resultOne= new HashMap<String, Object>();
			List<Map<String, Object>> serviceCodeMatchList = resultList.stream().filter(p->p.get("serviceCode").toString().equals(serviceCode)).collect(Collectors.toList());
			Map<String, Object> serviceCodeMatchFirst = serviceCodeMatchList.get(0);
			resultOne.put("isConfig", serviceCodeMatchFirst.get("isConfig"));
			resultOne.put("serviceCode", serviceCodeMatchFirst.get("serviceCode"));
			resultOne.put("serviceName", serviceCodeMatchFirst.get("serviceName"));
			List<Object> cityList = new ArrayList<Object>();//城市列表
			for (Map<String, Object> serviceCodeMatchOne : serviceCodeMatchList) {
				JSONArray jsonArray= JSON.parseArray(serviceCodeMatchOne.get("cityList").toString());
				cityList.add(jsonArray.get(0));
			}
			resultOne.put("cityList", cityList);
			result.add(resultOne);
		}
		return RestResponse.success(result);
	}

	 @RequestMapping(value = "/getCompanyBusiness")
	    public RestResponse getCompanyBusiness( @RequestBody Map<String,Object> jsondata){
		 Integer companyId=0;
		 if(jsondata==null){
			return RestResponse.fail(MrCarConfigErrorCode.PARAMS_IS_NUL);
		 }else{
              if(jsondata.get("companyId")!=null){
            	  companyId=Integer.valueOf(jsondata.get("companyId").toString());
              }
		 }
	    	String restUrl = CONFIG_HOST_URL + "city/getCompanyBusiness";
	    	Map<String, Object> paraMap = new HashMap<String, Object>();
	    	paraMap.put("companyId", companyId);
	    	List<Map<String,Object>> list=new ArrayList<Map<String,Object>>();
	    	RestResponse result =RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, CompanyBussinessDTO.class);
	    	if(result.getData()!=null){
	    	List<CompanyBussinessDTO> business =(List<CompanyBussinessDTO>) result.getData();
	    	for(CompanyBussinessDTO data:business){
	    		if(data.getBussinessCode()==1){
	    			Map<String,Object> map =new HashMap<>();
	    			map.put("business_code", 1);
	    			map.put("is_open", data.getIsOpen()+"");
	    			map.put("business_name", BussinessTypeDictionary.BUSSINESS_INSIDE_VEHICLE.getText());
	    			list.add(map);
	    		}else if(data.getBussinessCode()==2){
	    			Map<String,Object> map =new HashMap<>();
	    			map.put("business_code", 2);
	    			map.put("is_open", data.getIsOpen()+"");
	    			map.put("business_name", BussinessTypeDictionary.BUSSINESS_OUTSIDE_VEHICLE.getText());
	    			list.add(map);
	    		}else if(data.getBussinessCode()==3){
	    			Map<String,Object> map =new HashMap<>();
	    			map.put("business_code", 3);
	    			map.put("is_open", data.getIsOpen()+"");
	    			map.put("business_name", BussinessTypeDictionary.BUSSINESS_INSIDE_AUDIT.getText());
	    			list.add(map);
	    		}else if(data.getBussinessCode()==5){
	    			Map<String,Object> map =new HashMap<>();
	    			map.put("business_code", 5);
	    			map.put("is_open", data.getIsOpen()+"");
	    			map.put("business_name", BussinessTypeDictionary.BUSSINESS_FACE_CLOCK.getText());
	    			list.add(map);
	    		}else if(data.getBussinessCode()==6){
	    			Map<String,Object> map =new HashMap<>();
	    			map.put("business_code", 6);
	    			map.put("is_open", data.getIsOpen()+"");
	    			map.put("business_name", BussinessTypeDictionary.BUSSINESS_OUTSIDE_CLOCK.getText());
	    			list.add(map);
	    		}
	    	 }
	    	}
	    	result.setData(list);
	    	return result;
	    }

	 /**
	  * 后台企业分配 特殊策略时调用,查询当前登录企业已开通的服务，城市信息
	  * 查询的内容为当前登录人所属企业自身配置的开城信息且为商务用车用途
	  * @param recievMap
	  * @return
	  */
	  @RequestMapping(value = "/getCityByService")
	    public RestResponse getCityByService( @RequestBody Map<String,Object> recievMap){
		  	List<Map<String,Object>> lists =new ArrayList<>();
	    	String restUrl = CONFIG_HOST_URL + "serviceConfig/getCityByService";
		  LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
	    	recievMap.put("configOwnCompanyId", loginBaseInfo.obtainBelongCompanyId());
	    	recievMap.put("companyId", loginBaseInfo.obtainBelongCompanyId());
//	    	if(recievMap.get("companyId")==null){
//
//	    	}
	    	recievMap.put("businessType", businessTypeEnum.OUT_USE.getCode());
	    	RestResponse result =RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, recievMap, null, ServiceConfigCityDTO.class);
	    	if(result.getData()!=null){
	    		List<ServiceConfigCityDTO> list =(List<ServiceConfigCityDTO>)result.getData();
	    		for(ServiceConfigCityDTO data :list){
	    			Map<String,Object> map =new HashMap<>();
	    			map.put("cityCode", data.getCityCode());
	    			map.put("cityName", data.getCityName());
	    			//请求城市下可用车级
	    			map.put("serviceCode", recievMap.get("serviceCode"));
	    			String url = CONFIG_HOST_URL + "serviceConfig/getLevelByParams";
	    			map.put("companyId", loginBaseInfo.obtainBelongCompanyId());
	    			map.put("configOwnCompanyId", loginBaseInfo.obtainBelongCompanyId());
	    			map.put("businessType", businessTypeEnum.OUT_USE.getCode());

	    			RestResponse results =RestClient.requestForList(BaseHttpClient.HttpMethod.POST, url, map, null, ServiceConfigPolicyDTO.class);
	    			if(results.getData()!=null){
	    				List<ServiceConfigPolicyDTO> level =(List<ServiceConfigPolicyDTO>) results.getData();
	    				map.put("level_policy", level);
	    			}
	    			lists.add(map);
	    		}
	    	}
	    	result.setData(lists);
	    	return result;
	    }
	  /**商务用车根据用车类型，serviceCode获取车级**/
	  @RequestMapping(value = "/getLevelByParams")
	  public RestResponse getLevelByParams( @RequestBody Map<String,Object> recievMap){
		  LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
	    	String restUrl = CONFIG_HOST_URL + "serviceConfig/getLevelByParams";
	    	byte businessType = 0;
	    	byte flag=Byte.valueOf(recievMap.get("flag").toString());
	    	if(flag==2){//商务用车
	    		businessType=businessTypeEnum.OUT_USE.getCode();
	    		recievMap.put("configOwnCompanyId", BUSS_COMPANY_ID);
	    	}else{
	    		businessType=businessTypeEnum.INNER_USE.getCode();
	    		recievMap.put("configOwnCompanyId", loginBaseInfo.obtainBelongCompanyId());
	    	}
	    	recievMap.put("businessType", businessType);
	    	recievMap.put("companyId", loginBaseInfo.obtainBelongCompanyId());
	    	return RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, recievMap, null, ServiceConfigPolicyDTO.class);
	    }

	  @RequestMapping(value = "/getPolicysByLevelId")
	    public RestResponse getPolicysByLevelId( @RequestBody Map<String,Object> recievMap){
		  LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
	    	String restUrl = CONFIG_HOST_URL + "serviceConfig/getPolicysByLevelId";
	    	recievMap.put("companyId", loginBaseInfo.obtainBelongCompanyId());
	    	return RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, recievMap, null, PricePolicyDTO.class);
	    }

	  @RequestMapping(value = "/getPolicyDetail")
	    public RestResponse getPolicyDetail( @RequestBody Map<String,Object> recievMap){
	    	String restUrl = CONFIG_HOST_URL + "serviceConfig/getPolicyDetail";
	    	return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, recievMap, null, PricePolicyDTO.class);
	    }

}
