package com.izu.mrcar.controller.configcore;

import com.google.common.collect.Maps;
import com.izu.config.dto.OrderCommonAddressDTO;
import com.izu.config.dto.OrderCommonAddressParams;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.shiro.session.WebSessionUtil;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.LoginUser;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * @program: mrcar-config-core
 * @description: 常用地址controller类
 * @author: ljw
 * @create: 2021-09-07 10:27
 **/
@RestController
@RequestMapping("/commonAddress")
public class CommonAddressController {
    private static final Logger logger = LoggerFactory.getLogger(CommonAddressController.class);

    @Value("${mrcar-config-core.host.url}")
    private String CONFIG_HOST_URL;

    @RequestMapping("/add")
    @RequestFunction(functionName = "常用地址-添加")
    public RestResponse add(@RequestBody OrderCommonAddressDTO addressDTO){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        addressDTO.setCreateName(loginBaseInfo.obtainBaseInfo().getStaffName());
        addressDTO.setCreateId(loginBaseInfo.obtainBaseInfo().getStaffId());
        addressDTO.setCompanyId(loginBaseInfo.obtainBelongCompanyId());
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, addressDTO);
        String restUrl =  CONFIG_HOST_URL + "commonAddress/add";
        return RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, RestResponse.class);
    }

    @RequestMapping("/upd")
    @RequestFunction(functionName = "常用地址-编辑")
    public RestResponse upd(@RequestBody OrderCommonAddressDTO addressDTO){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        addressDTO.setUpdateName(loginBaseInfo.obtainBaseInfo().getStaffName());
        addressDTO.setUpdateId(loginBaseInfo.obtainBaseInfo().getStaffId());
        addressDTO.setCompanyId(loginBaseInfo.obtainBelongCompanyId());
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, addressDTO);
        String restUrl =  CONFIG_HOST_URL + "commonAddress/upd";
        return RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, RestResponse.class);
    }

    @RequestMapping("/del")
    @RequestFunction(functionName = "常用地址-删除")
    public RestResponse del(@RequestBody OrderCommonAddressDTO addressDTO){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        addressDTO.setUpdateName(loginBaseInfo.obtainBaseInfo().getStaffName());
        addressDTO.setUpdateId(loginBaseInfo.obtainBaseInfo().getStaffId());
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, addressDTO);
        String restUrl =  CONFIG_HOST_URL + "commonAddress/del";
        return RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, RestResponse.class);
    }

    @RequestMapping("/pageList")
    @RequestFunction(functionName = "常用地址-查询列表")
    public RestResponse pageList(@RequestBody OrderCommonAddressParams params){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        params.setCompanyId(loginBaseInfo.obtainBelongCompanyId());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, params);
        String restUrl =  CONFIG_HOST_URL + "commonAddress/pageList";
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, Map.class);
    }


}
