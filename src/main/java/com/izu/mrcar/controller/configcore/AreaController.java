package com.izu.mrcar.controller.configcore;

import com.izu.asset.dto.CarAirportDicDTO;
import com.izu.config.dto.CityDicDTO;
import com.izu.config.MrCarConfigRestLocator;
import com.izu.config.dto.CarRailwayStationDicDTO;
import com.izu.consts.ConfigURI;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;


@RestController
@RequestMapping("/dictionary")
public class AreaController {


	/**
	 * 加载配置服务mrcar-config-core访问域名及项目名
	 */
	@Value("${mrcar-config-core.host.url}")
	private String CONFIG_HOST_URL;


    /**
     * 查询所有省份列表
     **/
    @RequestMapping(value = "/listAllProvince")
    public RestResponse listAllProvince(){
		String restUrl = new MrCarConfigRestLocator().getRestUrl(ConfigURI.DIC_GET_PROVINCE_DIC_LIST);
    	return RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, null, null, Map.class);
    }

    /**
     * 查询所有城市列表
     **/
    @RequestMapping(value = "/listAllCity")
    public RestResponse listAllCity(Integer state, String provinceCode, Boolean includeHeadquarters){
		LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
		String restUrl = new MrCarConfigRestLocator().getRestUrl(ConfigURI.DIC_GET_CITY_DIC_LIST);
    	Map<String, Object> paraMap = new HashMap<String, Object>();
    	paraMap.put("state", state);
    	paraMap.put("provinceCode", provinceCode);
    	paraMap.put("companyId", loginBaseInfo.obtainBelongCompanyId());
    	paraMap.put("includeHeadquarters", includeHeadquarters);
    	return RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, CityDicDTO.class);
    }

    /**
     * 查询所有城市分页列表
     **/
    @RequestMapping(value = "/listCityForPage")
    public RestResponse listCityForPage(Integer pageSize, Integer page){
		String restUrl = new MrCarConfigRestLocator().getRestUrl(ConfigURI.DIC_GET_CITY_DIC_PAGE);
    	Map<String, Object> paraMap = new HashMap<String, Object>();
    	paraMap.put("pageSize", pageSize);
    	paraMap.put("page", page);
    	return RestClient.requestForPage(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, CityDicDTO.class);
    }

    /**
     * 根据城市编码查询城市信息
     **/
    @RequestMapping(value = "/getCityByCode")
    public RestResponse getCityByCode(Integer cityCode){
		String restUrl = new MrCarConfigRestLocator().getRestUrl(ConfigURI.DIC_GET_CITY_DIC_BY_CODE);
    	Map<String, Object> paraMap = new HashMap<String, Object>();
    	paraMap.put("cityCode", cityCode);
    	return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, CityDicDTO.class);
    }

    /**
     * 根据城市查询机场
     **/
    @RequestMapping(value = "/getAirportByCityCode")
    public RestResponse getAirportByCityCode(String cityCode, String airportName, Integer status){
    	String restUrl = CONFIG_HOST_URL + "dic/getAirportDicList";
    	Map<String, Object> paraMap = new HashMap<String, Object>();
    	paraMap.put("status", status);
    	paraMap.put("airportName", airportName);
    	paraMap.put("cityCode", cityCode);
    	return RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, CarAirportDicDTO.class);
    }

    /**
     * 根据机场ID查询机场信息
     **/
    @RequestMapping(value = "/getAirportById")
    public RestResponse getAirportById(Integer airportId){
    	String restUrl = CONFIG_HOST_URL + "dic/queryByAirportId";
    	Map<String, Object> paraMap = new HashMap<String, Object>();
    	paraMap.put("airportId", airportId);
    	return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, CarAirportDicDTO.class);
    }
	/**
	 * 火车站根据城市code码查询集合
	 * @param cityCode
	 * @param status
	 * @return
	 */
	@RequestMapping("/getRailwayByCityCode")
	public RestResponse getRailwayByCityCode(@RequestParam(value = "cityCode") @Verify(param = "cityCode", rule = "required") Integer cityCode, Byte status){
		String restUrl = CONFIG_HOST_URL + "railway/getRailwayByCityCode";
		Map<String, Object> paraMap = new HashMap<String, Object>();
		paraMap.put("cityCode", cityCode);
		paraMap.put("status", status);
		return RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, CarRailwayStationDicDTO.class);
	}

	/**
	 * 火车站根据火车ID查询
	 * @param stationId
	 * @return
	 */
	@RequestMapping("/getRailwayById")
	public RestResponse getRailwayById(@RequestParam(value = "stationId") @Verify(param = "stationId", rule = "required") Integer stationId){
		String restUrl = CONFIG_HOST_URL + "railway/getRailwayById";
		Map<String, Object> paraMap = new HashMap<String, Object>();
		paraMap.put("stationId", stationId);
		return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, CarRailwayStationDicDTO.class);
	}
	/**
	 * @Description: 获取省级城市列表
	 * @author: caodb
	 * @Date: 2020/1/4
	 **/
	@RequestMapping("/getProvinceCity")
	public RestResponse getProvinceCity(){
		String restUrl = new MrCarConfigRestLocator().getRestUrl(ConfigURI.DIC_GET_PROVINCE_CITY_DIC);
		return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, null, null, null);
	}
}
