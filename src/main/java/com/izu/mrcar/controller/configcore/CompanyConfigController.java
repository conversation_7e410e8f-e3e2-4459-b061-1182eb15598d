package com.izu.mrcar.controller.configcore;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.user.config.errcode.UserErrorCode;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;


@RestController
@RequestMapping("/companyConfig")
public class CompanyConfigController {
	@Value("${mrcar-user-core.host.url}")
	private String USER_HOST_URL;

	/**
	 * 加载配置服务mrcar-config-core访问域名及项目名
	 */
	@Value("${mrcar-config-core.host.url}")
	private String CONFIG_HOST_URL;

    @SuppressWarnings("unchecked")
	@RequestMapping(value = "/listForConfigService")
	@RequiresPermissions(value = "company_config_page")
	@RequestFunction(functionName = "企业开通业务")
    public RestResponse listForConfigService(@RequestBody Map<String,Object> paraMap){
    	//如果搜索的开通业务类型不是全部，则查询过滤条件
		if(null != paraMap) {
			if((null != paraMap.get("bussinessCode1") && !paraMap.get("bussinessCode1").equals("2")) || 
				(null != paraMap.get("bussinessCode2") && !paraMap.get("bussinessCode2").equals("2")) ||
				(null != paraMap.get("bussinessCode3") && !paraMap.get("bussinessCode3").equals("2")) ||
				(null != paraMap.get("bussinessCode4") && !paraMap.get("bussinessCode4").equals("2")) ||
				(null != paraMap.get("bussinessCode5") && !paraMap.get("bussinessCode5").equals("2")) ||
				(null != paraMap.get("bussinessCode6") && !paraMap.get("bussinessCode6").equals("2")) ||
				(null != paraMap.get("bussinessCode7") && !paraMap.get("bussinessCode7").equals("2")) ||
				(null != paraMap.get("bussinessCode8") && !paraMap.get("bussinessCode8").equals("2")) ||
				(null != paraMap.get("bussinessCode9") && !paraMap.get("bussinessCode9").equals("2")) ||
				(null != paraMap.get("bussinessCode10") && !paraMap.get("bussinessCode10").equals("2")) ||
				(null != paraMap.get("bussinessCode11") && !paraMap.get("bussinessCode11").equals("2"))
			) {
				RestResponse response = RestClient.requestInside(BaseHttpClient.HttpMethod.POST, CONFIG_HOST_URL + "companyBussiness/getBusinessSearchCondition", paraMap, null);
				if(null != response && null != response.getData()) {
					Map<String, Object> reMap = (Map<String, Object>) response.getData();
					paraMap.put("companyIdStr", reMap.get("companyIdStr"));
					paraMap.put("notCompanyIdStr", reMap.get("noCompanyIdStr"));
				}
			}
		}
		//查询符合条件的企业列表
		RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POST, USER_HOST_URL + "companyManage/listForConfigService", paraMap, null, Map.class);
		if(null != restResponse && null != restResponse.getData()) {
			PageDTO pageDTO =  (PageDTO) restResponse.getData();
			if(null != pageDTO && null != pageDTO.getResult()) {
				List<Map<String, Object>> companyList = (List<Map<String, Object>>) pageDTO.getResult();
				if(null!=companyList && !companyList.isEmpty()){
					StringBuffer sbf = new StringBuffer();
					for(Map<String, Object> companyMap : companyList) {
						sbf.append(companyMap.get("companyId") + ",");
					}
					String cIdStr = sbf.toString();
					//查询企业列表中的企业，当前开通的服务
					Map<String, Object> parasMap = new HashMap<String, Object>();
					if(StringUtils.isNotBlank(cIdStr)) {
						parasMap.put("companyIdStr", cIdStr.substring(0, cIdStr.length() - 1));
					}
					RestResponse serviceResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, CONFIG_HOST_URL + "companyBussiness/selectBussinessByCompanyIdList", parasMap, null, Map.class);
					List<Map<String, Object>> serviceList = null;
					if(null != serviceResponse && null != serviceResponse.getData()) {
						serviceList = (List<Map<String, Object>>) serviceResponse.getData();
					}
					companyList = formatCompanyList(companyList, serviceList);
				}
			}
		}
    	return restResponse;
    }
    
    private List<Map<String, Object>> formatCompanyList(List<Map<String, Object>> companyList, List<Map<String, Object>> serviceList) {
    	if(null != companyList && companyList.size() > 0) {
    		for(Map<String, Object> paraMap : companyList) {
    			paraMap.put("bussinessCode1", isOpen(paraMap.get("companyId").toString(), 1, serviceList));
    			paraMap.put("bussinessCode2", isOpen(paraMap.get("companyId").toString(), 2, serviceList));
    			paraMap.put("bussinessCode3", isOpen(paraMap.get("companyId").toString(), 3, serviceList));
    			paraMap.put("bussinessCode4", isOpen(paraMap.get("companyId").toString(), 4, serviceList));
    			paraMap.put("bussinessCode5", isOpen(paraMap.get("companyId").toString(), 5, serviceList));
    			paraMap.put("bussinessCode6", isOpen(paraMap.get("companyId").toString(), 6, serviceList));
    			paraMap.put("bussinessCode7", isOpen(paraMap.get("companyId").toString(), 7, serviceList));
    			paraMap.put("bussinessCode8", isOpen(paraMap.get("companyId").toString(), 8, serviceList));
    			paraMap.put("bussinessCode9", isOpen(paraMap.get("companyId").toString(), 9, serviceList));
    			paraMap.put("bussinessCode10", isOpen(paraMap.get("companyId").toString(), 10, serviceList));
    			paraMap.put("bussinessCode11", isOpen(paraMap.get("companyId").toString(), 11, serviceList));
    		}
    		return companyList;
    	} else {
    		return null;
    	}
    }
    
    private Integer isOpen(String companyId, Integer serviceCode, List<Map<String, Object>> serviceList) {
    	if(null != serviceList && serviceList.size() > 0) {
    		for(Map<String, Object> paraMap : serviceList) {
    			String cId = paraMap.get("companyId").toString();
    			Integer sCode = Integer.parseInt(paraMap.get("bussinessCode").toString());
    			if(cId.equals(companyId) && sCode.equals(serviceCode)) {
    				return 1;
    			}
    		}
    		return 0;
    	} else {
    		return 0;
    	}
    }
    
	@SuppressWarnings("unchecked")
	@RequestMapping(value = "/getCompanyBussinessByCompanyId")
	@RequestFunction(functionName = "企业开通业务详情")
    public RestResponse getCompanyBussinessByCompanyId(@RequestBody Map<String,Object> paraMap){
		RestResponse companyRep = RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, USER_HOST_URL + "companyManage/getById", paraMap, null, Map.class);
		if(null != companyRep.getData()) {
			Map<String, Object> restMap = new HashMap<String, Object>();
			restMap.putAll((Map<String, Object>) companyRep.getData());
			RestResponse bussinessRep = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, CONFIG_HOST_URL + "companyBussiness/getCompanyBussinessByCompanyId", paraMap, null, Map.class);
			if(null !=bussinessRep.getData()) {
				restMap.put("bussinessList", bussinessRep.getData());
			}
			return RestResponse.success(restMap);
		} else {
			return RestResponse.fail(UserErrorCode.QUERY_NOT_EXIST);
		}
	} 
	
	@RequestMapping(value = "/setCompanyBussiness")
	@RequiresPermissions(value = "company_business")
	@RequestFunction(functionName = "企业开通业务修改")
	public RestResponse setCompanyBussiness(@RequestBody Map<String,Object> paraMap) {
		Map<String, Object> pMap = new HashMap<String, Object>();
		pMap.put("companyId", paraMap.get("companyId"));
		String bussinessListStr = paraMap.get("bussinessList").toString();
		if(StringUtils.isNotBlank(bussinessListStr)) {
			bussinessListStr = bussinessListStr.replaceAll("=",":");
			List<Map<String, Object>> paraList = new ArrayList<Map<String, Object>>();
			JSONArray bussinessArray = JSONArray.parseArray(bussinessListStr);
			if(null != bussinessArray && bussinessArray.size() > 0) {
				for(int i = 0; i < bussinessArray.size(); i++) {
					JSONObject bussinessObj = bussinessArray.getJSONObject(i);
					if(null != bussinessObj) {
						Map<String, Object> parasMap = new HashMap<String, Object>();
						parasMap.put("bussinessCode", bussinessObj.getInteger("bussinessCode"));
						parasMap.put("isOpen", bussinessObj.getInteger("isOpen"));
						paraList.add(parasMap);
					}
				}
				pMap.put("bussinessJSONArray", JSON.toJSONString(paraList));
			}
		}
		return RestResponse.success(RestClient.requestInside(BaseHttpClient.HttpMethod.POST, CONFIG_HOST_URL + "companyBussiness/setCompanyBussiness", pMap, null));
	}
}
