package com.izu.mrcar.controller.configcore;

import com.alibaba.fastjson.JSON;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.enums.perm.ClientDataPermTypeEnum;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;


/**
 * 司机考勤围栏管理代理层相关服务接口
 */
@RestController
@RequestMapping("/driverFenceManage")
public class DriverFenceController {
	private static final Logger logger = LoggerFactory.getLogger(DriverFenceController.class);

	/**
	 * 加载配置服务mrcar-config-core访问域名及项目名
	 */
	@Value("${mrcar-config-core.host.url}")
	private String CONFIG_HOST_URL;

	@Value("${mrcar-user-core.host.url}")
	private String USER_HOST_URL;

	@Value("${mrcar-iot-core.host.url}")
	private String IOT_HOST_URL;

	/**
	 * 加载商务车队的企业ID
	 */
	@Value("${bussiness.chedui.companyid}")
	private int BUSS_COMPANY_ID;

	/**
	 * 司机考勤围栏信息列表查询
	 * @param paraMap
	 * @return
	 */
    @RequestMapping(value = "/selectDriverFenceByPage")
	@RequiresPermissions(value="attendanceFence")
	@RequestFunction(functionName = "考勤围栏")
    public RestResponse selectDriverFenceByPage(@RequestBody Map<String,Object> paraMap){
    	String restUrl = IOT_HOST_URL + "driverFenceManage/selectDriverFenceByPage";
		// 获取当前登录用户信息
		final ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
		int companyId = clientLoginInfo.obtainBelongCompanyId();
		paraMap.put("companyId", companyId);
		paraMap.put("permissions",String.join(",",clientLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet()));
		paraMap.put("dataPermType",clientLoginInfo.obtainSimpleDataPerm().getDataPermType());
		paraMap.put("loginUserId",clientLoginInfo.getBaseInfo().getStaffId());
    	return RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, RestResponse.class);
    }
	@RequestMapping(value = "/addOrUpdDriverFence")
	@RequiresPermissions(value ={"attendanceFenceAdd","attendanceFenceModify"},logical = Logical.OR )
	@RequestFunction(functionName = "考勤围栏编辑")
	public RestResponse addOrUpdDriverFence(@RequestBody Map<String,Object> paraMap){
		String restUrl = IOT_HOST_URL + "driverFenceManage/addOrUpdDriverFence";
		// 获取当前登录用户信息
		final ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
		paraMap.put("companyId", clientLoginInfo.obtainBelongCompanyId());
		paraMap.put("loginUserId",clientLoginInfo.obtainBaseInfo().getStaffId());
		paraMap.put("loginUserName",clientLoginInfo.obtainBaseInfo().getStaffName());
		return RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, RestResponse.class);
	}

	@RequestMapping(value = "/deleteDriverFence")
	@RequestFunction(functionName = "删除考勤围栏")
	public RestResponse deleteDriverFence(@RequestBody Map<String,Object> paraMap){
		String restUrl = IOT_HOST_URL + "driverFenceManage/deleteDriverFence";
		// 获取当前登录用户信息
		final ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
		paraMap.put("companyId", clientLoginInfo.obtainBelongCompanyId());
		paraMap.put("loginUserId",clientLoginInfo.obtainBaseInfo().getStaffId());
		paraMap.put("loginUserName",clientLoginInfo.obtainBaseInfo().getStaffName());
		return RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, RestResponse.class);
	}

	@RequestMapping(value = "/getDriverFenceDetail")
	@RequestFunction(functionName = "考勤围栏详情")
	public RestResponse getDriverFenceDetail(@RequestBody Map<String,Object> paraMap){
		String restUrl = IOT_HOST_URL + "driverFenceManage/getDriverFenceDetail";
		// 获取当前登录用户信息
		final ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
		paraMap.put("companyId", clientLoginInfo.obtainBelongCompanyId());
		return RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, RestResponse.class);
	}

	@RequestMapping(value = "/getMotorcadeByCityCodes")
	public RestResponse getMotorcadeByCityCodes(){
		String restUrl = USER_HOST_URL + "motorcade/getMotorcadeByCityCodes";
		// 获取当前登录用户信息
		final ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
		Map<String,Object> paraMap = new HashMap<>();
		paraMap.put("companyId", clientLoginInfo.obtainBelongCompanyId());
		Byte dataPermType = clientLoginInfo.obtainSimpleDataPerm().getDataPermType();
		paraMap.put("loginUserId", clientLoginInfo.getBaseInfo().getStaffId());
		paraMap.put("dataPermType", dataPermType);
		if(ClientDataPermTypeEnum.ASSIGN_CITY.getType().equals(dataPermType)){
			paraMap.put("cityCodeSet", String.join(",",clientLoginInfo.obtainSimpleDataPerm().getDataCodeSet()));
		}
		return RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, RestResponse.class);
	}

	@RequestMapping(value = "/getCityCodesOfUser")
	public RestResponse getCityCodesOfUser(){
		String restUrl = CONFIG_HOST_URL + "city/selectCityInfoByCityCode";
		// 获取当前登录用户信息
		final ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
		Byte dataPermType = clientLoginInfo.obtainSimpleDataPerm().getDataPermType();
		logger.info("【获取当前登录用户的信息】"+ JSON.toJSONString(clientLoginInfo));
		Map<String,Object> paraMap = new HashMap<>();
		if(ClientDataPermTypeEnum.ASSIGN_CITY.getType().equals(dataPermType)){
			paraMap.put("cityCodeSet", String.join(",",clientLoginInfo.obtainSimpleDataPerm().getDataCodeSet()));
		}
		paraMap.put("companyId",clientLoginInfo.obtainBelongCompanyId());
		return RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, RestResponse.class);
	}

    
}
