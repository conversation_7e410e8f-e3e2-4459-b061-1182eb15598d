package com.izu.mrcar.controller.configcore;

import com.google.common.collect.Maps;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.shiro.session.WebSessionUtil;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.LoginUser;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * @program: mrcar
 * @description: 用户消息通知设置
 * @author: ljw
 * @create: 2022-09-21 13:43
 **/
@RestController
@RequestMapping("/msgConfig")
public class UserMsgConfigController {

    @Value("${mrcar-config-core.host.url}")
    private String CONFIG_HOST_URL;

    @RequestMapping("/getUserAllMsgConfig")
    @RequestFunction(functionName = "消息配置-获取消息配置")
    public RestResponse getUserAllMsgConfig(){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put("userMobile", loginBaseInfo.obtainBaseInfo().getMobile());
        String restUrl =  CONFIG_HOST_URL + "msgConfig/getUserAllMsgConfig";
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, paramMap, null);
    }

    @RequestMapping("/changeStatus")
    @RequestFunction(functionName = "消息配置-开关接口")
    public RestResponse changeStatus(String configCode,Byte busType,Byte status){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put("userMobile", loginBaseInfo.obtainBaseInfo().getMobile());
        paramMap.put("configCode",configCode);
        paramMap.put("busType",busType);
        paramMap.put("status",status);
        String restUrl =  CONFIG_HOST_URL + "msgConfig/changeStatus";
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, paramMap, null);
    }
}
