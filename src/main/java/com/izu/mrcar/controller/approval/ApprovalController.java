package com.izu.mrcar.controller.approval;

import com.izu.business.consts.ApproveSourceEnum;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.dto.mrcar.input.OrderApplyCancelReqDTO;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.LoginUser;
import com.izu.mrcar.dto.ApproveOptionDTO;
import com.izu.mrcar.shiro.session.WebSessionUtil;
import com.izu.user.dto.staff.pc.AccountBelongDepartment;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by hp on 2019/11/17.
 */
@RestController
@RequestMapping("")
@Api(tags = "企业用车")
public class ApprovalController {

    @Value("${mrcar-business-core.host.url}")
    private String BUSINESS_HOST_URL;

    @Value("${mrcar-order-core.host.url}")
    private String ORDER_HOST_URL;

    /**
     *取消申请
     * @return
     */
    @RequestMapping("/approval/cancelApply")
    @RequestFunction(functionName = "取消申请")
    public RestResponse cancelApply (@RequestBody ApproveOptionDTO approveOptionDTO){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        Map<String, Object> param = new HashMap<>();
        param.put("relationNo", approveOptionDTO.getRelationNo());
        param.put("userId", loginBaseInfo.obtainBaseInfo().getStaffId());
        param.put("userName", loginBaseInfo.obtainBaseInfo().getStaffName());
        param.put("mobile", loginBaseInfo.obtainBaseInfo().getMobile());
        List<AccountBelongDepartment> structList = loginBaseInfo.getBelongDepartmentList();
        if(structList != null && structList.size() > 0){
            param.put("structId", structList.get(0).getDeptId());
            param.put("structName", structList.get(0).getDeptName());
        }

        param.put("source", ApproveSourceEnum.PC.text());
        String restUrl = BUSINESS_HOST_URL + "/applyOption/cancelApply/v2";
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, param, null);
    }

    /**
     *审批申请
     * @return
     */
    @RequestMapping("/approval/doApproval")
    @RequestFunction(functionName = "发起申请")
    public RestResponse doApproval (@RequestBody ApproveOptionDTO approveOptionDTO){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        Map<String, Object> param = new HashMap<>();
        param.put("relationNo", approveOptionDTO.getRelationNo());
        param.put("approveStatus", approveOptionDTO.getApproveStatus());
        param.put("rejectReasonText", approveOptionDTO.getRejectReasonText());
        param.put("userId", loginBaseInfo.obtainBaseInfo().getStaffId());
        param.put("userName", loginBaseInfo.obtainBaseInfo().getStaffName());
        param.put("mobile", loginBaseInfo.obtainBaseInfo().getMobile());
        List<AccountBelongDepartment> structList = loginBaseInfo.getBelongDepartmentList();
        if(structList != null && structList.size() > 0){
            param.put("structId", structList.get(0).getDeptId());
            param.put("structName", structList.get(0).getDeptName());
        }
        String restUrl = BUSINESS_HOST_URL + "/applyOption/approveApply/v2";
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, param, null);
    }

    /**
     *撤回用车申请
     * @return
     */
    @PostMapping(MrcarOrderRestMsgCenter.CANCEL_ORDER_APPLY)
    @RequestFunction(functionName = "行程-撤回申请")
    @ApiOperation(value = "行程-撤回申请")
    public RestResponse cancelOrderApply (@RequestBody OrderApplyCancelReqDTO cancelReqDTO){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        Map<String, Object> param = new HashMap<>();
        cancelReqDTO.setLoginCompanyId(loginBaseInfo.obtainBelongCompanyId());
        cancelReqDTO.setLoginCompanyName(loginBaseInfo.obtainBelongCompanyName());
        List<AccountBelongDepartment> structList = loginBaseInfo.getBelongDepartmentList();
        if(structList != null && structList.size() > 0){
            cancelReqDTO.setStructId(structList.get(0).getDeptId());
            cancelReqDTO.setStructName(structList.get(0).getDeptName());
        }
        cancelReqDTO.setLoginUserId(loginBaseInfo.obtainBaseInfo().getStaffId());
        cancelReqDTO.setLoginUserName(loginBaseInfo.obtainBaseInfo().getStaffName());
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, cancelReqDTO);
        String restUrl =  new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.CANCEL_ORDER_APPLY);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null);
    }
}
