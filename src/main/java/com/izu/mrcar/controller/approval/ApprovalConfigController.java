package com.izu.mrcar.controller.approval;

import com.google.common.collect.Maps;
import com.izu.config.dto.ApprovalConfigDTO;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.shiro.session.WebSessionUtil;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.LoginUser;
import com.izu.mrcar.common.config.RestUrlConfig;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.HashMap;
import java.util.Map;

/**
 * @description:
 * @author: hxc
 * @create: 2022-06-15 16:25
 **/
@RestController
@RequestMapping("/approvalConfig")
public class ApprovalConfigController {

    @RequestMapping("/getCityOut")
    public RestResponse getCityOut(){

        Map<String, Object> paraMap =  new HashMap<String, Object>();
        String restUrl = RestUrlConfig.getConfig().getConfigCoreUrl() + "/approvalConfig/getCityOut";
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        paraMap.put("companyId", loginBaseInfo.obtainBelongCompanyId());
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Boolean.class);
    }

    @RequestMapping("/getDetailByType")
    public RestResponse getDetailByType(@RequestBody ApprovalConfigDTO configDTO){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        String restUrl = RestUrlConfig.getConfig().getConfigCoreUrl() + "/approvalConfig/getDetailByType";
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        configDTO.setCompanyId(loginBaseInfo.obtainBelongCompanyId());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, configDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null);
    }

    @RequestMapping("/saveOrUpdate")
    public RestResponse saveOrUpdate(@RequestBody ApprovalConfigDTO configDTO){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        String restUrl = RestUrlConfig.getConfig().getConfigCoreUrl() + "/approvalConfig/saveOrUpdate";
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        configDTO.setCompanyId(loginBaseInfo.obtainBelongCompanyId());
        if(null!=configDTO.getId()){
            configDTO.setUpdaterId(loginBaseInfo.obtainBaseInfo().getStaffId());
            configDTO.setUpdaterName(loginBaseInfo.obtainBaseInfo().getStaffName());
        }else {
            configDTO.setCreaterId(loginBaseInfo.obtainBaseInfo().getStaffId());
            configDTO.setCreaterName(loginBaseInfo.obtainBaseInfo().getStaffName());
        }
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, configDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null);
    }
}
