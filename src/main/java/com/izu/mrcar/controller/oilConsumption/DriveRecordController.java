package com.izu.mrcar.controller.oilConsumption;

import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.consts.AssetConstant;
import com.izu.asset.dto.oilConsumption.AppDriveRecordReqDTO;
import com.izu.asset.dto.oilConsumption.DriveFeeDTO;
import com.izu.asset.dto.oilConsumption.DriveFeeDetailDTO;
import com.izu.asset.dto.oilConsumption.DriveRecordDTO;
import com.izu.asset.dto.oilConsumption.DriveRecordDetailDTO;
import com.izu.asset.dto.oilConsumption.DriveRecordPageReqDTO;
import com.izu.asset.util.ObjectTransferUtil;
import com.izu.config.dto.common.BaseEnumDTO;
import com.izu.config.restApi.BaseEnumApi;
import com.izu.consts.BaseEnum;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.excel.Column;
import com.izu.framework.web.util.excel.ExSheet;
import com.izu.mrcar.common.cache.RedisJsonCache;
import com.izu.mrcar.controller.AbstractExcelDownloadController;
import com.izu.mrcar.utils.DataPermUtil;
import com.izu.mrcar.utils.DateUtil;
import com.izu.user.util.SingletonFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> dongxiya 2023/3/30 14:46
 */
@Api(tags = "出车日志")
@RestController
public class DriveRecordController extends AbstractExcelDownloadController {
    @Resource
    private RedisJsonCache redisJsonCache;
    protected static final MrCarAssetRestLocator mrCarAssetRestLocator = SingletonFactory.getSingleton(SingletonFactory.assetRestLocator, MrCarAssetRestLocator::new);

    @ApiOperation("分页查询")
    @PostMapping(MrCarAssetRestCenter.DRIVE_RECORD_LIST_PAGE)
    public RestResponse<PageDTO<DriveRecordDTO>> listPage(@RequestBody DriveRecordPageReqDTO dto) {
        DataPermUtil.putDataPerm(dto);
        Map<String, Object> params = new HashMap<>(1);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.DRIVE_RECORD_LIST_PAGE), params, null, Map.class);
    }

    @ApiOperation("查看详情")
    @PostMapping(MrCarAssetRestCenter.DRIVE_RECORD_DETAIL)
    public RestResponse<DriveRecordDetailDTO> detail(@RequestBody AppDriveRecordReqDTO dto) {
        Map<String, Object> params = new HashMap<>(1);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.DRIVE_RECORD_DETAIL), params, null, DriveRecordDetailDTO.class);
    }

    @PostMapping(MrCarAssetRestCenter.DRIVE_RECORD_LIST_PAGE_EXPORT)
    @ApiOperation(value = "导出")
    public RestResponse export(@RequestBody DriveRecordPageReqDTO dto, HttpServletRequest request, HttpServletResponse response) {
        DataPermUtil.putDataPerm(dto);
        dto.setPageSize(1000);
        dto.setPage(1);
        Map<String, Object> params = new HashMap<>();
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.DRIVE_RECORD_LIST_PAGE_EXPORT), params, null, DriveRecordDTO.class);
        if (restResponse != null && restResponse.isSuccess()) {
            PageDTO<DriveRecordDTO> pageDTO = ObjectTransferUtil.cast(restResponse.getData());
            List<DriveRecordDTO> list = new ArrayList<>(ObjectTransferUtil.cast(pageDTO.getResult()));
            long pages = pageDTO.getTotal();
            while (dto.getPage() <= pages) {
                dto.setPage(dto.getPage() + 1);
                restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.DRIVE_RECORD_LIST_PAGE_EXPORT), params, null, DriveRecordDTO.class);
                if (!restResponse.isSuccess()) {
                    continue;
                }
                pageDTO = ObjectTransferUtil.cast(restResponse.getData());
                list.addAll(ObjectTransferUtil.cast(pageDTO.getResult()));
            }
            export(list, request, response);
        }
        return null;
    }

    private void export(List<DriveRecordDTO> list, HttpServletRequest request, HttpServletResponse response) {

        List<BaseEnumDTO> feeList = BaseEnumApi.getEnumListByParentCode(BaseEnum.FirstLevelEnum.DRIVER_FEE.getCode(), redisJsonCache);
        List<Column> columnModes = new ArrayList<>();
        columnModes.add(new Column("driveRecordCode", "出车日志编号", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("approvalStatusValue", "工单状态", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("driveTime", "出车日期", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("vehicleLicense", "车牌号", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("belongCityName", "车辆所在城市", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("structName", "车辆所属部门", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("driverName", "司机", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("passengerName", "用车人", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("beginTime", "开始时间", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("endTime", "结束时间", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("mileage", "行驶总里程(km)", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        final List<Map<String, String>> rowDatas = new ArrayList<Map<String, String>>(list.size() + 1);
        for (DriveRecordDTO data : list) {
            final Map<String, String> rowdata = new HashMap<String, String>(columnModes.size());
            rowdata.put("driveRecordCode", data.getDriveRecordCode());
            rowdata.put("approvalStatusValue", data.getApprovalStatusValue());
            rowdata.put("driveTime", DateUtil.date3String(data.getDriveTime(), DateUtil.DATE_FORMAT));
            rowdata.put("vehicleLicense", data.getVehicleLicense());
            rowdata.put("belongCityName", data.getBelongCityName());
            rowdata.put("structName", data.getStructName());
            rowdata.put("driverName", data.getDriverName());
            rowdata.put("passengerName", data.getPassengerName());
            rowdata.put("beginTime", DateUtil.date3String(data.getBeginTime(), DateUtil.TIME_FORMAT));
            rowdata.put("endTime", DateUtil.date3String(data.getEndTime(), DateUtil.TIME_FORMAT));
            String mileage = data.getMileage() != null ? data.getMileage().toString() : "0";
            mileage = mileage.replaceAll(AssetConstant.replaceLastZero, "$1");
            rowdata.put("mileage", mileage);
            rowDatas.add(rowdata);
        }
        List<ExSheet> exSheetList = new ArrayList<>(1);
        exSheetList.add(new ExSheet(columnModes, rowDatas, "出车日志"));
        //---------------------------B: 根据上面创建好的Sheet，生成Excel
        String excelType = "xlsx";
        String fileNameCn = "出车日志_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + "." + excelType;
        String fileNameEn = "drive_record_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + "." + excelType;
        this.exportExcel(request, response, fileNameCn, fileNameEn, exSheetList);
    }

}
