package com.izu.mrcar.controller.oilConsumption;

import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.oilConsumption.AppDriveFeeReqDTO;
import com.izu.asset.dto.oilConsumption.DriveFeeDTO;
import com.izu.asset.dto.oilConsumption.DriveFeeDetailDTO;
import com.izu.asset.dto.oilConsumption.DriveFeePageReqDTO;
import com.izu.asset.util.ObjectTransferUtil;
import com.izu.config.dto.common.BaseEnumDTO;
import com.izu.config.restApi.BaseEnumApi;
import com.izu.consts.BaseEnum;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.excel.Column;
import com.izu.framework.web.util.excel.ExSheet;
import com.izu.mrcar.common.cache.RedisJsonCache;
import com.izu.mrcar.controller.AbstractExcelDownloadController;
import com.izu.mrcar.utils.DataPermUtil;
import com.izu.mrcar.utils.DateUtil;
import com.izu.mrcar.utils.perm.AssetDataPermUtil;
import com.izu.user.util.SingletonFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> dongxiya 2023/3/30 14:46
 */
@Api(tags = "车杂费")
@RestController
public class DriveFeeController extends AbstractExcelDownloadController {
    @Resource
    private RedisJsonCache redisJsonCache;
    protected static final MrCarAssetRestLocator mrCarAssetRestLocator = SingletonFactory.getSingleton(SingletonFactory.assetRestLocator, MrCarAssetRestLocator::new);

    @ApiOperation("分页查询")
    @PostMapping(MrCarAssetRestCenter.DRIVE_FEE_LIST_PAGE)
    public RestResponse<PageDTO<DriveFeeDTO>> listPage(@RequestBody DriveFeePageReqDTO dto) {
        AssetDataPermUtil.setClientDataPerm(dto);
        Map<String, Object> params = new HashMap<>(1);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.DRIVE_FEE_LIST_PAGE), params, null, Map.class);
    }

    @ApiOperation("查看详情")
    @PostMapping(MrCarAssetRestCenter.DRIVE_FEE_DETAIL)
    public RestResponse<DriveFeeDTO> detail(@RequestBody AppDriveFeeReqDTO dto) {
        Map<String, Object> params = new HashMap<>(1);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.DRIVE_FEE_DETAIL), params, null, DriveFeeDTO.class);
    }

    @PostMapping(MrCarAssetRestCenter.DRIVE_FEE_LIST_PAGE_EXPORT)
    @ApiOperation(value = "导出")
    public RestResponse export(@RequestBody DriveFeePageReqDTO dto, HttpServletRequest request, HttpServletResponse response) {
        AssetDataPermUtil.setClientDataPerm(dto);
        dto.setPageSize(1000);
        dto.setPage(1);
        Map<String, Object> params = new HashMap<>();
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.DRIVE_FEE_LIST_PAGE_EXPORT), params, null, DriveFeeDTO.class);
        if (restResponse != null && restResponse.isSuccess()) {
            PageDTO<DriveFeeDTO> pageDTO = ObjectTransferUtil.cast(restResponse.getData());
            List<DriveFeeDTO> list = new ArrayList<>(ObjectTransferUtil.cast(pageDTO.getResult()));
            long pages = pageDTO.getTotal();
            while (dto.getPage() <= pages) {
                dto.setPage(dto.getPage() + 1);
                restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.DRIVE_FEE_LIST_PAGE_EXPORT), params, null, DriveFeeDTO.class);
                if (!restResponse.isSuccess()) {
                    continue;
                }
                pageDTO = ObjectTransferUtil.cast(restResponse.getData());
                list.addAll(ObjectTransferUtil.cast(pageDTO.getResult()));
            }
            export(list, request, response);
        }
        return null;
    }

    private void export(List<DriveFeeDTO> list, HttpServletRequest request, HttpServletResponse response) {

        List<BaseEnumDTO> feeList = BaseEnumApi.getEnumListByParentCode(BaseEnum.FirstLevelEnum.DRIVER_FEE.getCode(), redisJsonCache);
        List<Column> columnModes = new ArrayList<>();
        columnModes.add(new Column("driveFeeCode", "车杂费单号", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("approvalStatusValue", "工单状态", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("vehicleLicense", "车牌号", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("belongCityName", "车辆所在城市", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("structName", "车辆所属部门", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("companyName", "所属企业", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("remark", "备注", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("totalFee", "费用总计(元)", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        if (CollectionUtils.isNotEmpty(feeList)) {
            feeList.forEach(e -> {
                columnModes.add(new Column(e.getCode() + "", e.getName(), (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
            });
        }
        columnModes.add(new Column("createName", "创建人", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("createTime", "创建时间", (short) 5000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        final List<Map<String, String>> rowDatas = new ArrayList<Map<String, String>>(list.size() + 1);
        for (DriveFeeDTO data : list) {
            final Map<String, String> rowdata = new HashMap<String, String>(columnModes.size());
            rowdata.put("driveFeeCode", data.getDriveFeeCode());
            rowdata.put("approvalStatusValue", data.getApprovalStatusValue());
            rowdata.put("vehicleLicense", data.getVehicleLicense());
            rowdata.put("belongCityName", data.getBelongCityName());
            rowdata.put("structName", data.getStructName());
            rowdata.put("companyName", data.getCompanyName());
            rowdata.put("remark", data.getRemark());
            rowdata.put("totalFee", data.getTotalFee() != null ? data.getTotalFee().toString() : "");
            if (CollectionUtils.isNotEmpty(feeList)) {
                Map<String, String> feeMap = new HashMap<>();
                List<DriveFeeDetailDTO> driveFeeDetails = data.getDriveFeeDetails();
                if (CollectionUtils.isNotEmpty(driveFeeDetails)) {
                    feeMap = driveFeeDetails.stream().collect(Collectors.toMap(e -> e.getFeeCode() + "", e -> e.getFee() + ""));
                }
                Map<String, String> finalFeeMap = feeMap;
                feeList.forEach(e -> {
                    rowdata.put(e.getCode() + "", finalFeeMap.get(e.getCode() + ""));
                });
            }
            rowdata.put("createName", data.getCreateName());
            rowdata.put("createTime", DateUtil.date3String(data.getCreateTime(), DateUtil.TIME_FORMAT));
            rowDatas.add(rowdata);
        }
        List<ExSheet> exSheetList = new ArrayList<>(1);
        exSheetList.add(new ExSheet(columnModes, rowDatas, "车杂费"));
        //---------------------------B: 根据上面创建好的Sheet，生成Excel
        String excelType = "xlsx";
        String fileNameCn = "车杂费_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + "." + excelType;
        String fileNameEn = "drive_fee_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + "." + excelType;
        this.exportExcel(request, response, fileNameCn, fileNameEn, exSheetList);
    }
}
