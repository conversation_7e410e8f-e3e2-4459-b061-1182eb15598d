package com.izu.mrcar.controller.oilConsumption;

import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.oilConsumption.CarRefuelingRecordExportDTO;
import com.izu.asset.dto.oilConsumption.CarRefuelingRecordPageReqDTO;
import com.izu.asset.dto.oilConsumption.DriveFeeDTO;
import com.izu.asset.dto.oilConsumption.DriveFeePageReqDTO;
import com.izu.asset.dto.oilConsumption.DriveRecordPageReqDTO;
import com.izu.asset.dto.oilConsumption.DriveRecordZHONGYANDTO;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.user.enums.perm.ClientDataPermTypeEnum;
import com.izu.user.enums.role.SystemTypeEnum;
import com.izu.user.util.SingletonFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> dongxiya 2023/3/30 14:46
 */
@Api(tags = "湖南中烟取数API")
@RestController
public class HuNanZhongYanController {
    protected static final MrCarAssetRestLocator mrCarAssetRestLocator = SingletonFactory.getSingleton(SingletonFactory.assetRestLocator, MrCarAssetRestLocator::new);

    @ApiOperation("分页查询中烟加油记录")
    @PostMapping("/zhongyan/oil/v1/list")
    public RestResponse<PageDTO<CarRefuelingRecordExportDTO>> listPage(@RequestBody CarRefuelingRecordPageReqDTO dto) {
        dto.setSystemType(SystemTypeEnum.CUSTOMER.getCode());
        dto.setCompanyId(675);
        dto.setCompanyCode("KH-66012020110600001");
        dto.setCompanyName("湖南中烟工业有限责任公司");
        dto.setDataPermType(ClientDataPermTypeEnum.SELF_COMPANY.getType());
        Map<String, Object> params = new HashMap<>(1);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CAR_REFUELING_RECORD_LIST_PAGE_EXPORT), params, null, Map.class);
    }

    @ApiOperation("分页查询中烟车杂费")
    @PostMapping("/zhongyan/fee/v1/list")
    public RestResponse<PageDTO<DriveFeeDTO>> listPage(@RequestBody DriveFeePageReqDTO dto) {
        dto.setSystemType(SystemTypeEnum.CUSTOMER.getCode());
        dto.setCompanyId(675);
        dto.setCompanyCode("KH-66012020110600001");
        dto.setCompanyName("湖南中烟工业有限责任公司");
        dto.setDataPermType(ClientDataPermTypeEnum.SELF_COMPANY.getType());
        Map<String, Object> params = new HashMap<>(1);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.DRIVE_FEE_LIST_PAGE), params, null, Map.class);
    }
    @ApiOperation("分页查询中烟出车日志")
    @PostMapping("/zhongyan/driveRecord/v1/list")
    public RestResponse<PageDTO<DriveRecordZHONGYANDTO>> listPage(@RequestBody DriveRecordPageReqDTO dto) {
        dto.setSystemType(SystemTypeEnum.CUSTOMER.getCode());
        dto.setCompanyId(675);
        dto.setCompanyCode("KH-66012020110600001");
        dto.setCompanyName("湖南中烟工业有限责任公司");
        dto.setDataPermType(ClientDataPermTypeEnum.SELF_COMPANY.getType());
        Map<String, Object> params = new HashMap<>(1);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.DRIVE_RECORD_LIST_PAGE_ZHONGYAN), params, null, Map.class);
    }

}
