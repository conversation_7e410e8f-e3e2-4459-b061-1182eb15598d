package com.izu.mrcar.controller.oilConsumption;

import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.oilConsumption.AppCarRefuelingRecordReqDTO;
import com.izu.asset.dto.oilConsumption.CarRefuelingRecordDetailDTO;
import com.izu.asset.dto.oilConsumption.CarRefuelingRecordExportDTO;
import com.izu.asset.dto.oilConsumption.CarRefuelingRecordPageReqDTO;
import com.izu.asset.util.ObjectTransferUtil;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.controller.export.CarRefuelingRecordExport;
import com.izu.mrcar.utils.asset.AssetUtil;
import com.izu.mrcar.utils.perm.AssetDataPermUtil;
import com.izu.user.util.SingletonFactory;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR> dongxiya 2023/3/30 14:46
 */

/**
 * 加油记录-客户端
 */
@RestController
public class CarRefuelingRecordController {
    protected static final MrCarAssetRestLocator mrCarAssetRestLocator = SingletonFactory.getSingleton(SingletonFactory.assetRestLocator, MrCarAssetRestLocator::new);

    @ApiOperation("分页查询")
    @PostMapping(MrCarAssetRestCenter.CAR_REFUELING_RECORD_LIST_PAGE)
    public RestResponse<PageDTO<CarRefuelingRecordExportDTO>> listPage(@RequestBody CarRefuelingRecordPageReqDTO dto) {
        AssetDataPermUtil.setCarRefuelingRecordDataPerm(dto);
        Map<String, Object> params = new HashMap<>(1);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CAR_REFUELING_RECORD_LIST_PAGE_EXPORT), params, null, Map.class);
    }

    @ApiOperation("查看详情")
    @PostMapping(MrCarAssetRestCenter.CAR_REFUELING_RECORD_DETAIL)
    public RestResponse<CarRefuelingRecordDetailDTO> detail(@RequestBody AppCarRefuelingRecordReqDTO dto) {
        Map<String, Object> params = new HashMap<>(1);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CAR_REFUELING_RECORD_DETAIL), params, null, CarRefuelingRecordDetailDTO.class);
    }


    @ApiOperation("查看修改详情")
    @PostMapping(MrCarAssetRestCenter.CAR_REFUELING_RECORD_MODIFY_DETAIL)
    public RestResponse<CarRefuelingRecordDetailDTO> modifyDetail(@RequestBody AppCarRefuelingRecordReqDTO dto) {
        Map<String, Object> params = new HashMap<>(1);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CAR_REFUELING_RECORD_MODIFY_DETAIL), params, null, CarRefuelingRecordDetailDTO.class);
    }


    @PostMapping(MrCarAssetRestCenter.CAR_REFUELING_RECORD_LIST_PAGE_EXPORT)
    @ApiOperation(value = "导出")
    @ExportExcelWeb(fileName = ExportExcelConstants.FILE_NAME_CAR_REFUELING_RECORD + ExportExcelConstants.XLSX, filePath = ExportExcelConstants.FILE_PATH,
            sheet = ExportExcelConstants.FILE_NAME_CAR_REFUELING_RECORD, c = CarRefuelingRecordExport.class)
    public PageDTO export(@RequestBody CarRefuelingRecordPageReqDTO dto, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response) {
        AssetDataPermUtil.setCarRefuelingRecordDataPerm(dto);
        dto.setPage(izuEasyExcelSession.getPageNo());
        izuEasyExcelSession.setUserName(dto.getStaffName());
        dto.setPageSize(1000);
        Map<String, Object> params = new HashMap<>();
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CAR_REFUELING_RECORD_LIST_PAGE_EXPORT), params, null, CarRefuelingRecordExport.class);
        if (restResponse != null && restResponse.isSuccess()) {
            return ObjectTransferUtil.cast(restResponse.getData());
        }
        return null;
    }

    /**
     * 修改加油记录
     *
     * @param dto
     * @return
     */
    @PostMapping(MrCarAssetRestCenter.CAR_REFUELING_RECORD_UPDATE)
    public RestResponse update(AppCarRefuelingRecordReqDTO dto) {
        AssetUtil.setClientLoginInfo(dto);
        HashMap<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        String restUrl = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.CAR_REFUELING_RECORD_UPDATE);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null, Map.class);
    }


}
