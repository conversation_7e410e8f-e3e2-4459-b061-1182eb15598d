package com.izu.mrcar.controller.asset;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.izu.DTO.violation.ViolationRecordsDTO;
import com.izu.ThirdRestLocator;
import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.consts.RequestSourceEnum;
import com.izu.asset.consts.ViolationStatusEnum;
import com.izu.asset.dto.CarViolationDTO;
import com.izu.asset.dto.*;
import com.izu.asset.dto.violation.CreateViolationReqDTO;
import com.izu.asset.dto.violation.ModifyViolationReqDTO;
import com.izu.asset.dto.violation.ViolationQueryEmailPageReqDTO;
import com.izu.asset.dto.violation.ViolationQueryPageReqDTO;
import com.izu.asset.dto.violation.req.GetCarViolationReqDTO;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.excel.dto.IzuMailSession;
import com.izu.framework.doc.JrdApiDoc;
import com.izu.framework.doc.JrdApiParamDoc;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.cache.RedisJsonCache;
import com.izu.mrcar.dto.DicKeyValueDTO;
import com.izu.mrcar.service.asset.violation.CarViolationClientEmailService;
import com.izu.mrcar.service.asset.violation.CarViolationEmailService;
import com.izu.mrcar.service.asset.violation.CarViolationService;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.dto.publicGovcar.PublicGovCarOrderDetailInfoRespDTO;
import com.izu.mrcar.order.dto.publicGovcar.PublicGovCarOrderQueryDTO;
import com.izu.mrcar.order.dto.publicGovcar.PublicGovCarOrderRespDTO;
import com.izu.mrcar.utils.perm.AssetDataPermUtil;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.common.constants.Constant;
import com.izu.mrcar.utils.perm.provider.ProviderOrderDataPermUtil;
import com.izu.user.dto.CompanyPaidSearchReq;
import com.izu.user.dto.staff.pc.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @description: 违章管理
 * @author: hxc
 * @create: 2019-10-24 15:03
 **/
@Api(tags = "资产管理")
@RestController
@RequestMapping("/violation")
public class CarViolationController {

    private static final Logger log = LoggerFactory.getLogger(CarViolationController.class);


    @Value("${mrcar-asset-core.host.url}")
    private String ASSET_HOST_URL;

    private static final String QUERY_CAR_VIOLATION_PAGE_LIST = "/violation/queryCarViolationPageList";
    private static final String QUERY_CAR_VIOLATION_EXPORT = "/violation/export";

    @Resource
    private CarViolationService carViolationService;

    @Autowired
    private RedisJsonCache redisJsonCache;

    @Resource
    private CarViolationClientEmailService carViolationClientEmailService;


    /**
    * @Description: 查询车辆违章列表数据
    * @author: hxc
    * @Date: 2019/10/24
    **/
    @PostMapping(value = "/queryCarViolationPageList")
    @RequiresPermissions(value = "violation_manager")
    @RequestFunction(functionName = "违章列表查询")
    @ApiOperation("违章列表查询")
    public RestResponse<PageDTO<CarViolationDTO>> queryCarViolationPageList(@RequestBody ViolationQueryPageReqDTO dto) {
        AssetDataPermUtil.setClientDataPerm(dto);
        return carViolationService.listClientCarViolation(dto);
    }

    @PostMapping("/querySummary")
    @RequestFunction(functionName = "查询车辆违章汇总数据")
    @ApiOperation(value = "查询车辆违章汇总数据")
    public RestResponse<List<CarViolationSummaryDTO>> querySummary(@RequestBody CarViolationSummaryQueryDTO param) {
        AssetDataPermUtil.setClientDataPerm(param);
        String restUrl = ASSET_HOST_URL + MrCarAssetRestCenter.VIOLATION_QUERY_SUMMARY;
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, CarViolationSummaryDTO.class);
    }


    @PostMapping("/month/trend")
    @RequestFunction(functionName = "查询月违章趋势")
    @ApiOperation(value = "查询月违章趋势")
    public RestResponse<List<CarViolationMonthTrendDTO>> queryViolationMonthTrend(@RequestBody CarViolationSummaryQueryDTO param) {
        AssetDataPermUtil.setClientDataPerm(param);
        String restUrl = ASSET_HOST_URL + MrCarAssetRestCenter.VIOLATION_MONTH_TREND;
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, CarViolationMonthTrendDTO.class);
    }


    @PostMapping("/queryHighType")
    @RequestFunction(functionName = "高频违章分页")
    @ApiOperation(value = "高频违章分页")
    public RestResponse<PageDTO<CarViolationHighTypeDTO>> queryViolationHighType(@RequestBody CarViolationHighTypeQueryPageDTO reqDTO) {
        AssetDataPermUtil.setClientDataPerm(reqDTO);
        String restUrl = ASSET_HOST_URL + MrCarAssetRestCenter.VIOLATION_QUERY_HIGH_TYPE;
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, CarViolationHighTypeDTO.class);
    }



    @PostMapping("/queryViolationSingleCar")
    @RequestFunction(functionName = "单车违章排行分页")
    @ApiOperation(value = "单车违章排行分页")
    public RestResponse<PageDTO<CarViolationSingleCarDTO>> queryViolationSingleCar(@RequestBody CarViolationSingleCarQueryDTO reqDTO) {
        AssetDataPermUtil.setClientDataPerm(reqDTO);
        String restUrl = ASSET_HOST_URL + MrCarAssetRestCenter.VIOLATION_SINGLE_CAR;
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, CarViolationSingleCarDTO.class);
    }

    @PostMapping("unHandle/ratio")
    @RequestFunction(functionName = "未处理违章时长占比")
    @ApiOperation(value = "未处理违章时长占比")
    public RestResponse<List<CarViolationUnHandleRatioDTO>> queryViolationUnHandleRatio(@RequestBody CarViolationUnHandleRatioQueryDTO param) {
        AssetDataPermUtil.setClientDataPerm(param);
        String restUrl = ASSET_HOST_URL + MrCarAssetRestCenter.VIOLATION_UN_HANDLE_RATIO;
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, CarViolationUnHandleRatioDTO.class);
    }


    @RequestMapping("postRealTimeViolation")
    @JrdApiDoc(simpleDesc = "实时查询违章信息", author = "cos", resDataClass = ViolationRecordsDTO.class)
    @RequestFunction(functionName = "实时违章查询")
    public RestResponse postRealTimeViolation(
            @JrdApiParamDoc(desc = "车牌号") @Verify(param = "carNo", rule = "required") String carNo,
            @JrdApiParamDoc (desc = "发动机号") @Verify(param = "engineNo", rule = "required") String engineNo,
            @JrdApiParamDoc (desc = "车架号") @Verify(param = "vin", rule = "required|vin") String vin){
        ThirdRestLocator locator = new ThirdRestLocator();
        String restUrl = locator.getRestUrl("/violation/postRealTimeViolation");
        Map<String, Object> params = new HashMap<>();
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        params.put("fromPc", true);
        params.put("carNo", carNo);
        params.put("engineNo", engineNo);
        params.put("vin", vin);
        params.put("createName", loginBaseInfo.obtainBaseInfo().getStaffName());
        params.put("createId", loginBaseInfo.obtainBaseInfo().getStaffId());
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, params, null);
    }

    /**
     * 支持违章查询省份列表
     * @return
     */
    @RequestMapping("/getProvince")
    @RequestFunction(functionName = "支持违章查询省份")
    public RestResponse getProvinceList(){
        ThirdRestLocator locator = new ThirdRestLocator();
        String restUrl = locator.getRestUrl("/violation/getProvince");
        return RestClient.requestInside( BaseHttpClient.HttpMethod.POST, restUrl, null, null);
    }

    /**
     * 省份下支持城市列表
     * @param provinceId    省份id
     * @return
     */
    @RequestMapping("/getCityByCityID")
    @RequestFunction(functionName = "违章省份城市查询")
    public RestResponse getViolationCityList(@Verify(param = "provinceId", rule = "required") String provinceId){
        ThirdRestLocator locator = new ThirdRestLocator();
        String restUrl = locator.getRestUrl("/violation/getCityByCityID");
        Map<String, Object> params = new HashMap<>();
        params.put("provinceId", provinceId);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, params, null);
    }


    /**
    * @Description: 违章列表导出
    * @author: hxc
    * @Date: 2021/1/4
    **/
    @PostMapping(value = "/export")
    @RequestFunction(functionName = "违章信息导出")
    @ApiOperation("违章信息导出")
    public void export(@RequestBody ViolationQueryPageReqDTO dto,
                       IzuEasyExcelSession izuEasyExcelSession,
                       HttpServletRequest request,
                       HttpServletResponse response){
        ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
        izuEasyExcelSession.setUserName(clientLoginInfo.getBaseInfo().getStaffName());
        izuEasyExcelSession.setPageNo(1);
        AssetDataPermUtil.setClientDataPerm(dto);
        Boolean checkRst = carViolationService.checkExportDataNum(dto, RequestSourceEnum.CLIENT, response);
        if(!checkRst){
            return;
        }
        dto.setPage(1);
        dto.setPageSize(2000);
        request.setAttribute("requestSource", RequestSourceEnum.CLIENT.getRequestSource());
        carViolationService.exportCarViolation(dto, izuEasyExcelSession, request, response);
//        RestResponse result = getCarViolationQueryPage(dto);
//        if (!result.isSuccess()) {
//            return;
//        }
//        PageDTO<CarViolationDTO> pageDTO = ObjectTransferUtil.cast(result.getData());
//        List<CarViolationDTO> resultData = pageDTO.getResult();
//        exportExcel(resultData, response);
    }
    /**
     * @Description: 违章信息导出
     **/
    @PostMapping(value = "/export/exportEmail")
    @RequestFunction(functionName = "违章信息导出到邮件-客户端")
    @ApiOperation(value = "违章信息导出到邮件-客户端", notes = "作者：丁伟兵")
    public RestResponse exportOrderBusinessInfoListAsyn(@RequestBody ViolationQueryEmailPageReqDTO reqDTO) {
        if (StringUtils.isBlank(reqDTO.getEmail())) {
            RestResponse restResponse = RestResponse.fail(9999);
            restResponse.setMsg("邮箱号不能为空！！！");
            return restResponse;
        }
        // 防止上次导出未完成的情况下，再次导出
        String lockKey = "mrcar:client:violation:exportCarViolation:asyn:" + reqDTO.getEmail();
        final String cacheValue = redisJsonCache.get(lockKey, String.class);
        if (StringUtils.isNotBlank(cacheValue)) {
            log.warn("已提交申请，请稍等");
            return RestResponse.create(RestErrorCode.SYSTEM_EXCEPTION, "您已经提交了导出请求，请稍后查收电子邮件。（大约耗时3分钟）", false, null);
        }
        redisJsonCache.set(lockKey, "Y", 3 * 60);
        IzuMailSession izuMailSession = new IzuMailSession();
        AccountBaseInfo accountBaseInfo = LoginSessionUtil.getBaseLoginInfo().obtainBaseInfo();
        izuMailSession.setUserName(accountBaseInfo.getStaffName());
        izuMailSession.setMailOpName("违章信息列表");
        izuMailSession.setMailSubject("违章信息列表");
        String email = reqDTO.getEmail();
        izuMailSession.setToMail(email);
        AssetDataPermUtil.setClientDataPerm(reqDTO);
        CompletableFuture.runAsync(() ->{
            carViolationClientEmailService.exportToEmail(reqDTO, izuMailSession);
        }).whenComplete((unused, throwable) -> {
            redisJsonCache.delete(lockKey);
            if (throwable != null) {
                log.error("违章信息列表异步导出至邮箱异常", throwable);
            }
        });
        return RestResponse.create(0, "提交成功！请稍后查收电子邮件。（大约耗时3分钟！）", true, null);
    }

    private RestResponse<PageDTO<CarViolationDTO>> getCarViolationQueryPage(ViolationQueryPageReqDTO dto) {
        AssetDataPermUtil.setClientDataPerm(dto);
        Integer page = dto.getPage();
        if (page == null) {
            page = 1;
        }
        Integer pageSize = dto.getPageSize();
        if (pageSize == null) {
            pageSize = 10;
        }
        dto.setPage(page);
        dto.setPageSize(pageSize);
        Map<String, Object> params = new HashMap<>(1);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        String restUrl = ASSET_HOST_URL + MrCarAssetRestCenter.CLIENT_GET_VIOLATION_LIST;
        RestResponse<PageDTO<CarViolationDTO>> result = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, params, null, CarViolationDTO.class);
        return result;
    }

    /**
    * @Description: 车辆违章列表导出
    * @author: hxc
    * @Date: 2021/1/4
    **/
//    private void exportExcel(List<CarViolationDTO> data, HttpServletResponse response) {
//
//        Workbook workbook;
//        if (data == null || data.isEmpty()){
//            workbook = ExcelExportUtil.exportExcel(new ExportParams(), CarViolationExportDTO.class, Collections.emptyList());
//        }else {
//            List<CarViolationExportDTO> exportDTOS = BeanUtil.copyList(data, CarViolationExportDTO.class);
//            workbook = ExcelExportUtil.exportExcel(new ExportParams(), CarViolationExportDTO.class, exportDTOS);
//        }
//        String fileName = "车辆违章列表_"+new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
//        workbook.setSheetName(0,fileName);
//        try (OutputStream outputStream = response.getOutputStream()){
//            response.setContentType("application/vnd.ms-excel;charset=utf-8");
//            fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
//            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xls");
//            workbook.write(outputStream);
//        }catch (Exception e){
//            log.error("导出车辆违章列表Excel异常");
//        }
//    }

    /**
    * @Description: 创建违章
    * @author: hxc
    * @Date: 2019/10/24
    **/
    @RequestMapping(value = "/makeViolation")
    @RequiresPermissions(value = "violation_add")
    @RequestFunction(functionName = "新增违章信息")
    @ApiOperation("新增违章信息")
    @SuppressWarnings("unchecked")
    public RestResponse<Boolean> makeViolation(@RequestBody CreateViolationReqDTO reqDTO) {
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        reqDTO.setCreateId(loginBaseInfo.obtainBaseInfo().getStaffId().longValue());
        reqDTO.setCreateName(loginBaseInfo.obtainBaseInfo().getStaffName());
        reqDTO.setCompanyId(loginBaseInfo.obtainBelongCompanyId());
        List<AccountBelongDepartment> structList = loginBaseInfo.getBelongDepartmentList();
        if (structList != null && !structList.isEmpty()) {
            reqDTO.setStructId(structList.get(0).getDeptId());
        }
        HashMap<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        String restUrl = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.VEHICLE_VIOLATION_CREATE);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }

    @PostMapping(value = "/modifyViolation")
    @RequestFunction(functionName = "更新车辆违章信息")
    @ApiOperation("更新车辆违章信息")
    @SuppressWarnings("unchecked")
    public RestResponse<Boolean> modifyViolation(@RequestBody ModifyViolationReqDTO reqDTO) {
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        reqDTO.setLoginUserId(loginBaseInfo.obtainBaseInfo().getStaffId());
        reqDTO.setLoginUserName(loginBaseInfo.obtainBaseInfo().getStaffName());
        reqDTO.setLoginCompanyId(loginBaseInfo.obtainBelongCompanyId());
        reqDTO.setLoginUserPhone(loginBaseInfo.obtainBaseInfo().getMobile());
        List<AccountBelongDepartment> structList = loginBaseInfo.getBelongDepartmentList();
        if (CollectionUtils.isNotEmpty(structList)) {
            reqDTO.setLoginStructId(structList.get(0).getDeptId());
            reqDTO.setLoginStructName(structList.get(0).getDeptName());
        }
        HashMap<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        String restUrl = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.VEHICLE_VIOLATION_MODIFY);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
    }

    @PostMapping(value = "/getCarViolation")
    @RequestFunction(functionName = "违章详情查询")
    @ApiOperation("违章详情查询")
    public RestResponse<CarViolationDTO> getCarViolation(@RequestBody GetCarViolationReqDTO dto) {
        HashMap<String, Object> restParam = new HashMap<>();
        restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        String restUrl = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.VEHICLE_GET_CAR_VIOLATION);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null, CarViolationDTO.class);
    }

    @PostMapping(value = "/violationStatusEnum")
    @RequestFunction(functionName = "违章状态枚举")
    @ApiOperation(value = "违章状态枚举")
    public RestResponse<List<DicKeyValueDTO>> violationSyncStatusEnum(@RequestBody CompanyPaidSearchReq companyPaidSearchReq) {
        List<DicKeyValueDTO> collect = Arrays.stream(ViolationStatusEnum.values())
                .map(c -> new DicKeyValueDTO(c.getCode(), c.getDescription()))
                .collect(Collectors.toList());
        return RestResponse.success(collect);
    }
}
