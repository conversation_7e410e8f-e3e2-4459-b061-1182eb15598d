package com.izu.mrcar.controller.temporalSharedVehicleRecord;

import com.izu.asset.dto.vehicle.CarInfoClientPageReqDTO;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.dto.timeShare.DayStatisticsListDTO;
import com.izu.mrcar.order.dto.timeShare.DayStatisticsListInputDTO;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.enums.perm.ClientDataPermTypeEnum;
import com.izu.user.util.SingletonFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

@Api(tags = "分时用车")
@RestController
public class TemporalSharedStatisticsController {

    private final MrcarOrderRestLocator orderRestLocator = SingletonFactory.getSingleton(SingletonFactory.orderRestLocator, MrcarOrderRestLocator::new);

    @PostMapping(MrcarOrderRestMsgCenter.TIME_REPORT_DAY_STATISTICS_LIST)
    @RequestFunction(functionName = "分时用车日统计列表查询（客户端）")
    @ApiOperation(value = "分时用车日统计列表查询（客户端）",notes = "作者：mapp")
    public RestResponse<PageDTO<DayStatisticsListDTO>> getDayList(@RequestBody DayStatisticsListInputDTO param){
        setDataPerm(param);
        Map<String, Object> params = new HashMap<>(1);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, orderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.TIME_REPORT_DAY_STATISTICS_LIST), params, null, DayStatisticsListDTO.class);
    }

    @PostMapping(MrcarOrderRestMsgCenter.TIME_REPORT_MONTH_STATISTICS_LIST)
    @RequestFunction(functionName = "分时用车月度统计列表查询（客户端）")
    @ApiOperation(value = "分时用车月度统计列表查询（客户端）",notes = "作者：mapp")
    public RestResponse<PageDTO<DayStatisticsListDTO>> getMonthList(@RequestBody DayStatisticsListInputDTO param){
        setDataPerm(param);
        Map<String, Object> params = new HashMap<>(1);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, orderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.TIME_REPORT_MONTH_STATISTICS_LIST), params, null, DayStatisticsListDTO.class);
    }

    @PostMapping(MrcarOrderRestMsgCenter.TIME_REPORT_DAY_STATISTICS_EXPORT)
    @RequestFunction(functionName = "分时用车日统计列表导出（客户端）")
    @ApiOperation(value = "分时用车日统计列表导出（客户端）",notes = "作者：mapp")
    @ExportExcelWeb(fileName = ExportExcelConstants.FILE_NAME_TIME_REPORT_DAY_EXPORT_INFO,filePath = ExportExcelConstants.FILE_PATH,
            sheet = ExportExcelConstants.SHEET_NAME_TIME_REPORT_DAY_EXPORT_INFO, c = TemporalSharedDayStatisticsExport.class)
    public PageDTO<DayStatisticsListDTO> exportDayInfo(@RequestBody DayStatisticsListInputDTO param, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response){
        setDataPerm(param);
        Map<String, Object> params = new HashMap<>(1);
        param.setPageSize(ExportExcelConstants.EXPORT_MAX_LINE);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, orderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.TIME_REPORT_DAY_STATISTICS_LIST), params, null, TemporalSharedDayStatisticsExport.class);
        if(restResponse!=null && restResponse.isSuccess()){
            PageDTO pageDTO = (PageDTO)restResponse.getData();
            if (pageDTO.getTotal() > ExportExcelConstants.EXPORT_MAX_LINE){
                throw ExceptionFactory.createRestException(ErrorCode.STAT_FUNCTION_OPERATION_EXPORT_EXCEED, pageDTO.getTotal(), ExportExcelConstants.EXPORT_MAX_LINE);
            }
            return pageDTO;
        }
        else {
            return null;
        }
    }

    @PostMapping(MrcarOrderRestMsgCenter.TIME_REPORT_MONTH_STATISTICS_EXPORT)
    @RequestFunction(functionName = "分时用车月度统计列表导出（客户端）")
    @ApiOperation(value = "分时用车月度统计列表导出（客户端）",notes = "作者：mapp")
    @ExportExcelWeb(fileName = ExportExcelConstants.FILE_NAME_TIME_REPORT_MONTH_EXPORT_INFO,filePath = ExportExcelConstants.FILE_PATH,
            sheet = ExportExcelConstants.SHEET_NAME_TIME_REPORT_MONTH_EXPORT_INFO, c = TemporalSharedMonthStatisticsExport.class)
    public PageDTO<DayStatisticsListDTO> exportMonthInfo(@RequestBody DayStatisticsListInputDTO param, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response){
        setDataPerm(param);
        Map<String, Object> params = new HashMap<>(1);
        param.setPageSize(ExportExcelConstants.EXPORT_MAX_LINE);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, orderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.TIME_REPORT_MONTH_STATISTICS_LIST), params, null, TemporalSharedMonthStatisticsExport.class);
        if(restResponse!=null && restResponse.isSuccess()){
            PageDTO pageDTO = (PageDTO)restResponse.getData();
            if (pageDTO.getTotal() > ExportExcelConstants.EXPORT_MAX_LINE){
                throw ExceptionFactory.createRestException(ErrorCode.STAT_FUNCTION_OPERATION_EXPORT_EXCEED, pageDTO.getTotal(), ExportExcelConstants.EXPORT_MAX_LINE);
            }
            return pageDTO;
        }
        else {
            return null;
        }
    }

    /**
     * 设置数据权限
     * @param param
     */
    private void setDataPerm(DayStatisticsListInputDTO param){
        ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
        param.setDataPermType(clientLoginInfo.obtainDataPerm().getDataPermType());
        param.setLoginCompanyId(clientLoginInfo.getClientCompany().getCompanyId());
        ClientDataPermTypeEnum dataPermTypeEnum = ClientDataPermTypeEnum.getByType(clientLoginInfo.obtainDataPerm().getDataPermType());
        param.setLoginUserId(clientLoginInfo.obtainBaseInfo().getStaffId());
        switch (dataPermTypeEnum){
            case SELF_COMPANY:// 本企业
                param.setDataPermCodeSet(clientLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.ID).getDataCodeSet());
                break;
            case ASSIGN_DEPT:// 指定部门
                param.setDataPermCodeSet(clientLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.ID).getDataCodeSet());
                break;
            case ASSIGN_CITY:// 指定城市
                param.setDataPermCodeSet(clientLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet());
                break;
            case ONESELF:// 本人
                param.setDataPermCodeSet(new HashSet<String>() {
                    {
                        this.add("-1");
                    }
                });
                break;
        }
    }
}
