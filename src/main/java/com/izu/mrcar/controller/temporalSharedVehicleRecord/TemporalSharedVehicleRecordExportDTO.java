package com.izu.mrcar.controller.temporalSharedVehicleRecord;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> dongxiya 2024/1/27 15:26
 */
@Data
public class TemporalSharedVehicleRecordExportDTO {

    @ExcelProperty(value = "用车记录编号")
    @ColumnWidth(value = 30)
    private String warnSn;
    @ExcelProperty(value = "单据状态")
    @ColumnWidth(value = 20)
    private String recordStatusName;
    @ExcelProperty(value = "车牌号")
    @ColumnWidth(value = 20)
    private String vehicleLicense;
    @ExcelProperty(value = "车架号")
    @ColumnWidth(value = 20)
    private String vehicleVin;
    @ExcelProperty(value = "车型")
    @ColumnWidth(value = 20)
    private String vehicleModel;
    @ExcelProperty(value = "车辆所在企业")
    @ColumnWidth(value = 20)
    private String vehicleCompanyName;
    @ExcelProperty(value = "车辆所属部门")
    @ColumnWidth(value = 20)
    private String vehicleStructName;
    @ExcelProperty(value = "开始时间")
    @ColumnWidth(value = 20)
    private Date warnStartTime;
    @ExcelProperty(value = "结束时间")
    @ColumnWidth(value = 20)
    private Date warnEndTime;
    @ExcelProperty(value = "时长(小时)")
    @ColumnWidth(value = 20)
    private BigDecimal durationHour;
    @ExcelProperty(value = "里程(km)")
    @ColumnWidth(value = 20)
    private BigDecimal tripMileage;
    @ExcelProperty(value = "用车申请人")
    @ColumnWidth(value = 20)
    private String orderCustomerName;
    @ExcelProperty(value = "申请人部门")
    @ColumnWidth(value = 20)
    private String orderStructName;
    @ExcelProperty(value = "申请单编号")
    @ColumnWidth(value = 30)
    private String orderNo;
    @ExcelProperty(value = "乘车人")
    @ColumnWidth(value = 20)
    private String passengerName;
    @ExcelProperty(value = "司机")
    @ColumnWidth(value = 20)
    private String orderDriverName;
    @ExcelProperty(value = "预计出发时间")
    @ColumnWidth(value = 20)
    private Date orderBookingStartTime;
    @ExcelProperty(value = "预计结束时间")
    @ColumnWidth(value = 20)
    private Date orderBookingEndTime;
    @ExcelProperty(value = "出发地")
    @ColumnWidth(value = 30)
    private String destinationStartName;
    @ExcelProperty(value = "目的地")
    @ColumnWidth(value = 30)
    private String destinationEndName;
    @ExcelProperty(value = "用车备注")
    @ColumnWidth(value = 20)
    private String orderDetail;

}
