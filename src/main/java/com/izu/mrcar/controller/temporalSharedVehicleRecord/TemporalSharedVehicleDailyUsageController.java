package com.izu.mrcar.controller.temporalSharedVehicleRecord;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.izu.framework.web.exception.RestErrorException;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.dto.TemporalSharedVehicleDailyUsageExportDTO;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.order.dto.temporalSharedVehicle.DataPermDTO;
import com.izu.mrcar.order.dto.temporalSharedVehicle.TemporalSharedVehicleRecordDTO;
import com.izu.mrcar.order.dto.temporalSharedVehicle.TemporalSharedVehicleRecordPageReqDTO;
import com.izu.mrcar.order.dto.timeShare.TemporalSharedVehicleDailyUsageRespDTO;
import com.izu.mrcar.order.dto.timeShare.TemporalSharedVehicleDailyUsageSearchReqDTO;
import com.izu.mrcar.utils.DataPermUtil;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.AccountBelongDepartment;
import com.izu.user.dto.staff.pc.ProviderLoginInfo;
import com.izu.user.enums.perm.ProviderDataPermTypeEnum;
import com.izu.user.enums.role.SystemTypeEnum;
import com.izu.user.util.SingletonFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.izu.mrcar.common.errorcode.ErrorCode.STAT_FUNCTION_OPERATION_EXPORT_EXCEED;

@RestController
@Api(tags = "分时用车")
public class TemporalSharedVehicleDailyUsageController {

    private final MrcarOrderRestLocator orderRestLocator = SingletonFactory.getSingleton(SingletonFactory.orderRestLocator, MrcarOrderRestLocator::new);

    @ApiOperation("分时用车单日明细查询")
    @PostMapping(MrcarOrderRestMsgCenter.TIME_REPORT_DAILY_USAGE_SEARCH)
    public RestResponse<PageDTO<TemporalSharedVehicleDailyUsageRespDTO>> pageSearch(
            @RequestBody TemporalSharedVehicleDailyUsageSearchReqDTO dto) {
        Map<String, Object> params = new HashMap<>(1);
        DataPermUtil.putDataPerm(dto);
        if (Objects.equals(SystemTypeEnum.PROVIDER.getCode(),
                LoginSessionUtil.getBaseLoginInfo().getSystemType())) {
            dealSpecialDataPerm(dto);
        }
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY,
                    orderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.TIME_REPORT_DAILY_USAGE_SEARCH), params, null,
                        TemporalSharedVehicleDailyUsageRespDTO.class);


    }

    @ApiOperation("分时用车单日明细导出")
    @PostMapping(MrcarOrderRestMsgCenter.TIME_REPORT_DAILY_USAGE_EXPORT)
    public void export(@RequestBody TemporalSharedVehicleDailyUsageSearchReqDTO dto,
                       HttpServletResponse response) throws IOException {
        Map<String, Object> params = new HashMap<>(1);
        DataPermUtil.putDataPerm(dto);
        if (Objects.equals(SystemTypeEnum.PROVIDER.getCode(),
                LoginSessionUtil.getBaseLoginInfo().getSystemType())) {
            dealSpecialDataPerm(dto);
        }
        dto.setPageSize(ExportExcelConstants.EXPORT_MAX_LINE);
        params.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);

        @SuppressWarnings("unchecked")
        RestResponse<PageDTO<TemporalSharedVehicleDailyUsageRespDTO>> resp =
                RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY,
                        orderRestLocator.getRestUrl(MrcarOrderRestMsgCenter.TIME_REPORT_DAILY_USAGE_SEARCH), params, null,
                        TemporalSharedVehicleDailyUsageRespDTO.class);

        PageDTO<TemporalSharedVehicleDailyUsageRespDTO> pageDTO = resp.getData();
        // 超过导出限制
        if (pageDTO.getTotal() > ExportExcelConstants.EXPORT_MAX_LINE) {
            RestResponse<?> rest = RestResponse.fail(ErrorCode.EXPORT_EXCEED, pageDTO.getTotal(), ExportExcelConstants.EXPORT_MAX_LINE);
            JSON.writeJSONString(response.getOutputStream(), rest);
            response.setContentType("application/json");
            return;
        }

        List<TemporalSharedVehicleDailyUsageExportDTO> list =
                pageDTO.getResult().stream().map(s -> {
                    TemporalSharedVehicleDailyUsageExportDTO export = new TemporalSharedVehicleDailyUsageExportDTO();
                    BeanUtils.copyProperties(s, export);
                    return export;
                }).collect(Collectors.toList());

        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");

        String fileName = URLEncoder.encode("单日用车明细", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

        EasyExcel.write(response.getOutputStream(), TemporalSharedVehicleDailyUsageExportDTO.class)
                .autoCloseStream(Boolean.FALSE).sheet("模板").doWrite(list);

    }

    private static void dealSpecialDataPerm(DataPermDTO dto) {
        ProviderLoginInfo providerLoginInfo = LoginSessionUtil.getProviderLoginInfo();
        if (Objects.equals(ProviderDataPermTypeEnum.RESPONSIBLE_CONTRACT.getType(), dto.getDataPermType())
                || Objects.equals(ProviderDataPermTypeEnum.RESPONSIBLE_CUSTOMER.getType(), dto.getDataPermType())
                || Objects.equals(ProviderDataPermTypeEnum.ASSIGN_CUSTOMER.getType(), dto.getDataPermType())
                || Objects.equals(ProviderDataPermTypeEnum.SELF_DEPT.getType(), dto.getDataPermType())
                || Objects.equals(ProviderDataPermTypeEnum.ONE_SELF.getType(), dto.getDataPermType())
        ) {
            dto.setDataCodeSet(providerLoginInfo.getBelongDepartmentList().stream().map(AccountBelongDepartment::getDeptCode).collect(Collectors.toSet()));
        }
    }

}
