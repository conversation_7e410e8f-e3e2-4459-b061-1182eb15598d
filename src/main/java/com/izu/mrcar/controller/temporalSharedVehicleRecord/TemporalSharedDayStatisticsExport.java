package com.izu.mrcar.controller.temporalSharedVehicleRecord;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import io.swagger.annotations.ApiModelProperty;
import izu.org.apache.poi.ss.usermodel.BorderStyle;
import izu.org.apache.poi.ss.usermodel.HorizontalAlignment;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@HeadStyle(
        fillForegroundColor = 22,
        fillBackgroundColor = 22
)
@ColumnWidth(20)
@ContentStyle(
        horizontalAlignment = HorizontalAlignment.CENTER,
        borderBottom = BorderStyle.THIN,
        borderLeft = BorderStyle.THIN,
        borderRight = BorderStyle.THIN,
        borderTop = BorderStyle.THIN,
        wrapped = true
)

@Data
public class TemporalSharedDayStatisticsExport {

    @ExcelProperty(
            value = {"车辆所在企业"},
            order = 1
    )
    private String vehicleCompanyName;

    @ExcelProperty(
            value = {"车辆所属部门"},
            order = 2
    )
    private String vehicleStructName;

    @ExcelProperty(
            value = {"车辆所在城市"},
            order = 3
    )
    private String vehicleBelongCityName;

    @ExcelProperty(
            value = {"车牌号"},
            order = 4
    )
    private String vehicleLicense;

    @ExcelProperty(
            value = {"车架号"},
            order = 5
    )
    private String vehicleVin;

    @ExcelProperty(
            value = {"车型"},
            order = 6
    )
    private String vehicleBrandName;

    @ExcelProperty(
            value = {"用车日期"},
            order = 7
    )
    @DateTimeFormat(value = "yyyy-MM-dd")
    private Date dayTime;

    @ExcelProperty(
            value = {"时长（小时）"},
            order = 8
    )
    private BigDecimal dailyDurationHour;

    @ExcelProperty(
            value = {"里程（km）"},
            order = 9
    )
    private BigDecimal dailyTripMileage;

    @ExcelProperty(
            value = {"日期类型"},
            order = 10
    )
    private String dayTypeStr;
}
