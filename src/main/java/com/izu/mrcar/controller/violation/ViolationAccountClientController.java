package com.izu.mrcar.controller.violation;

import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.consts.violation.ViolationSyncStatusEnum;
import com.izu.asset.dto.violation.*;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.dto.DicKeyValueDTO;
import com.izu.mrcar.service.violation.ViolationAccountService;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.CompanyPaidSearchReq;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import com.izu.excel.dto.IzuEasyExcelSession;

/**
 * <AUTHOR>
 * @description: 客户端-12123账号相关
 * @date 2024/9/26 11:13
 */
@Api(tags = "违章管理")
@RestController
public class ViolationAccountClientController {

    @Resource
    private ViolationAccountService violationAccountService;

    /**
     * 12123账号列表查询接口
     * @param violationAccountReqDTO
     * @return
     */
    @PostMapping(value = MrCarAssetRestCenter.VIOLATION_COMPANY_ACCOUNT_LIST_URL_CLIENT)
    @RequestFunction(functionName = "违章管理-12123账号-列表")
    @ApiOperation(value = "12123账号列表")
    public RestResponse<PageDTO<ViolationAccountRespDTO>> getList(@RequestBody ViolationAccountReqDTO violationAccountReqDTO) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        Map<String, Object> param = new HashMap<>();
        violationAccountReqDTO.setCompanyId(baseLoginInfo.obtainBelongCompanyId());
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, violationAccountReqDTO);
        String restUrl = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.VIOLATION_COMPANY_ACCOUNT_LIST_URL);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null, ViolationAccountRespDTO.class);
    }
    @PostMapping(value = "/client/violation/company/account/export")
    @RequestFunction(functionName = "12123账号列表导出")
    @ApiOperation(value = "12123账号列表导出")
    public RestResponse export(@RequestBody ViolationAccountReqDTO reqDTO, IzuEasyExcelSession izuEasyExcelSession, HttpServletResponse response) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        reqDTO.setCompanyId(baseLoginInfo.obtainBelongCompanyId());
        reqDTO.setPage(1);
        reqDTO.setPageSize(2000);
        izuEasyExcelSession.setPageNo(1);
        violationAccountService.providerExport(reqDTO, izuEasyExcelSession, response);
        return null;
    }

    @PostMapping(value = MrCarAssetRestCenter.VIOLATION_COMPANY_ACCOUNT_SAVE_OR_UPDATE_URL_CLIENT)
    @RequestFunction(functionName = "违章管理-12123账号-新增编辑")
    @ApiOperation(value = "新增/编辑12123账号")
    public RestResponse saveOrUpdate(@RequestBody ViolationAccountSaveOrUpdateReqDTO violationAccountSaveOrUpdateReqDTO) {
        ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
        Map<String, Object> param = new HashMap<>();
        violationAccountSaveOrUpdateReqDTO.setLoginUserId(clientLoginInfo.obtainBaseInfo().getStaffId());
        violationAccountSaveOrUpdateReqDTO.setLoginUserName(clientLoginInfo.obtainBaseInfo().getStaffName());
        violationAccountSaveOrUpdateReqDTO.setCompanyId(clientLoginInfo.obtainBelongCompanyId());
        violationAccountSaveOrUpdateReqDTO.setCompanyCode(clientLoginInfo.obtainBelongCompanyCode());
        violationAccountSaveOrUpdateReqDTO.setCompanyName(clientLoginInfo.obtainBelongCompanyName());
        violationAccountSaveOrUpdateReqDTO.setLoginUserPhone(clientLoginInfo.obtainBaseInfo().getMobile());
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, violationAccountSaveOrUpdateReqDTO);
        String restUrl = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.VIOLATION_COMPANY_ACCOUNT_SAVE_OR_UPDATE_URL);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null);
    }
    @PostMapping(value = MrCarAssetRestCenter.VIOLATION_COMPANY_ACCOUNT_UPDATE_STATUS_URL_CLIENT)
    @RequestFunction(functionName = "违章管理-12123账号-启用禁用")
    @ApiOperation(value = "启用或禁用12123账号")
    public RestResponse updateAccountStatus(@RequestBody ViolationAccountUpdateStatusReqDTO violationAccountUpdateStatusReqDTO) {
        ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
        Map<String, Object> param = new HashMap<>();
        violationAccountUpdateStatusReqDTO.setLoginUserId(clientLoginInfo.obtainBaseInfo().getStaffId());
        violationAccountUpdateStatusReqDTO.setLoginUserName(clientLoginInfo.obtainBaseInfo().getStaffName());
        violationAccountUpdateStatusReqDTO.setLoginUserPhone(clientLoginInfo.obtainBaseInfo().getMobile());
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, violationAccountUpdateStatusReqDTO);
        String restUrl = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.VIOLATION_COMPANY_ACCOUNT_UPDATE_STATUS_URL);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null);
    }

    @PostMapping(value = MrCarAssetRestCenter.VIOLATION_COMPANY_ACCOUNT_LOG_LIST_URL_CLIENT)
    @RequestFunction(functionName = "违章管理-12123账号-账号日志")
    @ApiOperation(value = "账号日志")
    public RestResponse<PageDTO<ViolationAccountLogRespDTO>> getAccountLogList(@RequestBody ViolationAccountLogReqDTO violationAccountLogReqDTO) {
        Map<String, Object> param = new HashMap<>();
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, violationAccountLogReqDTO);
        String restUrl = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.VIOLATION_COMPANY_ACCOUNT_LOG_LIST_URL);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null, ViolationAccountLogRespDTO.class);
    }
    @PostMapping(value = "/client/violation/account/violationSyncStatusEnum")
    @RequestFunction(functionName = "违章同步状态枚举")
    @ApiOperation(value = "违章同步状态枚举")
    public RestResponse<List<DicKeyValueDTO>> violationSyncStatusEnum(@RequestBody CompanyPaidSearchReq companyPaidSearchReq) {
        List<DicKeyValueDTO> collect = Arrays.stream(ViolationSyncStatusEnum.values())
                .map(c -> new DicKeyValueDTO(c.getCode(), c.getDesc()))
                .collect(Collectors.toList());
        return RestResponse.success(collect);
    }
}
