package com.izu.mrcar.controller.listfield;

import com.google.common.collect.Maps;
import com.izu.config.dto.*;
import com.izu.consts.ConfigURI;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.config.RestUrlConfig;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;


@Slf4j
@RestController
public class FieldConfigDetailController {

    @PostMapping(ConfigURI.FIELD_CONFIG_DETAIL)
    @RequestFunction(functionName = "列表字段配置-详情")
    @ApiOperation(value = "列表字段配置详情")
    public RestResponse<FieldConfigDetailRespDTO> getDetail(@RequestBody FieldConfigDetailReq param) {
        String restUrl = RestUrlConfig.getConfig().getConfigCoreUrl() + ConfigURI.FIELD_CONFIG_DETAIL;
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        param.setCompanyId(loginBaseInfo.obtainBelongCompanyId());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null);
    }

    @PostMapping(ConfigURI.FIELD_CONFIG_SAVE)
    @RequestFunction(functionName = "列表字段配置-保存")
    @ApiOperation(value = "列表字段配置保存")
    public RestResponse<Void> saveConfig(@RequestBody FieldConfigDetailSaveReq param) {
        String restUrl = RestUrlConfig.getConfig().getConfigCoreUrl() + ConfigURI.FIELD_CONFIG_SAVE;
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        int customerId = loginBaseInfo.obtainBaseInfo().getStaffId();
        String customerName = loginBaseInfo.obtainBaseInfo().getStaffName();
        param.setUpdateId(customerId);
        param.setCreateId(customerId);
        param.setCreateName(customerName);
        param.setUpdateName(customerName);
        param.setCompanyId(loginBaseInfo.obtainBelongCompanyId());
        param.setCompanyName(loginBaseInfo.obtainBelongCompanyName());
        param.setCompanyCode(loginBaseInfo.obtainBelongCompanyCode());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null);
    }
}