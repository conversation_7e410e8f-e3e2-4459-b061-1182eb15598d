package com.izu.mrcar.controller.login;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.constants.MrcarUrl;
import com.izu.mrcar.common.enums.AuthStrategy;
import com.izu.mrcar.common.enums.ThirdpartyLoginTypeEnum;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.dto.login.DingBindMobileReqDTO;
import com.izu.mrcar.dto.login.LoginReqDTO;
import com.izu.mrcar.dto.login.WeChatBindMobileReqDTO;
import com.izu.mrcar.service.login.*;
import com.izu.mrcar.service.login.channel.WeChatLoginService;
import com.izu.mrcar.service.login.v2.thirdparty.dingtalk.DingTalkUserInfo;
import com.izu.mrcar.service.login.v2.thirdparty.dingtalk.DingtalkLoginClient;
import com.izu.mrcar.service.login.v2.thirdparty.wechat.WeChatAccessTokenResponse;
import com.izu.mrcar.service.login.v2.thirdparty.wechat.WeChatUserInfo;
import com.izu.mrcar.service.login.v2.thirdparty.wechat.WeChatWebsiteLoginClient;
import com.izu.mrcar.service.login.v2.token.MobileAccessibleToken;
import com.izu.mrcar.service.login.v2.token.MobilePasswordToken;
import com.izu.mrcar.service.login.v2.token.MobileValidationCodeToken;
import com.izu.mrcar.service.login.v2.token.ThirdpartyMobileToken;
import com.izu.mrcar.service.user.LoginServiceV2;
import com.izu.mrcar.shiro.exception.CustomAuthenticationException;
import com.izu.mrcar.shiro.exception.VerificationCodeErrorException;
import com.izu.mrcar.shiro.exception.VerificationCodeInvalidException;
import com.izu.mrcar.shiro.session.WebSessionUtil;
import com.izu.mrcar.utils.DateUtil;
import com.izu.mrcar.utils.IpAddr;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.config.errcode.UserErrorCode;
import com.izu.user.dto.account.DingAuthorizationInfoDTO;
import com.izu.user.dto.account.DingUnBindReqDTO;
import com.izu.user.dto.account.WeChatAuthorizationInfoDTO;
import com.izu.user.dto.account.WeChatUnBindReqDTO;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.dto.staff.pc.LoginSession;
import com.izu.user.dto.staff.pc.WeChatBindInfoDTO;
import com.izu.user.enums.LoginSystemEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.AccountException;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.authc.IncorrectCredentialsException;
import org.apache.shiro.subject.Subject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Api(tags = "账号登录")
@RestController
public class AccountLoginController {

    private static final Logger logger = LoggerFactory.getLogger(AccountLoginController.class);

    private static final UserRestLocator LOCATOR = new UserRestLocator();

    @Autowired
    private AuthApiServiceFactory authApiServiceFactory;
    @Autowired
    private LoginInfoCacheService loginInfoCacheService;

    @Autowired
    private AccountLoginService accountLoginService;
    @Autowired
    private WeChatLoginService weChatLoginService;

    @Autowired
    private DingtalkLoginClient dingtalkClient;

    @Autowired
    private WeChatWebsiteLoginClient wechatLoginClient;

    @Autowired
    private AccountLockService accountLockService;

    @Autowired
    private LoginServiceV2 loginServiceV2;

    /**
     * 用户登录
     */
    @PostMapping(MrcarUrl.ACCOUNT_LOGIN)
    @RequestFunction(functionName = "用户登录")
    @ApiOperation("用户登录")
    @SuppressWarnings("unchecked")
    public RestResponse<LoginBaseInfo> login(@RequestBody @Validated LoginReqDTO reqDTO)
            throws IOException {
        AuthenticationToken token;
        // 特殊处理微信的登录逻辑
        if (Objects.equals(AuthStrategy.WECHAT.getStrategy(),
                reqDTO.getAuthStrategy())) {
            // 取access_token
            WeChatAccessTokenResponse response =
                    wechatLoginClient.getAccessTokenByCode(reqDTO.getThirdToken());
            // 微信没有返回正确的token, 本次扫码已经失效
            if (StringUtils.isBlank(response.getAccessToken())) {
                return RestResponse.fail(ErrorCode.LOGIN_WECHAT_INVALID);
            }
            // 根据openid查询用户信息
            WeChatAuthorizationInfoDTO authInfo = findAuthInfoByOpenId(response.getOpenid());
            if (authInfo != null) {
                // 用于之前绑定过该微信
                token = new ThirdpartyMobileToken(authInfo.getMobile(),
                        authInfo.getOpenid(), ThirdpartyLoginTypeEnum.WECHAT);
            } else {
                // 第一次登录
                // 获取用户的信息
                WeChatUserInfo userInfo =
                        wechatLoginClient.getUserInfoByOpenId(response.getAccessToken(), response.getOpenid());
                WeChatAuthorizationInfoDTO dto =
                        BeanUtil.copyObject(userInfo, WeChatAuthorizationInfoDTO.class);
                // 得到本系统的登录相关token
                String sessionToken = generateWeChatLoginSession(dto);
                return RestResponse.create(
                        UserErrorCode.LOGIN_UNBIND_MOBILE,
                        "该账号尚未绑定手机号",
                        false,
                        sessionToken);
            }
        }else if(Objects.equals(AuthStrategy.PASSWORD.getStrategy(), reqDTO.getAuthStrategy())){
            //密码登录需要校验账号是否锁定
            boolean lock = accountLockService.isLock(reqDTO.getMobile());
            if (lock) {
                return RestResponse.fail(ErrorCode.ACCOUNT_LOCKED, accountLockService.getLockedTime());
            }
            token =  buildToken(reqDTO);
        } else if (Objects.equals(AuthStrategy.DING_TALK.getStrategy(), reqDTO.getAuthStrategy())) {
                // todo dingding
            // 取access_token
            DingTalkUserInfo userInfo = dingtalkClient.getUserInfoByAuthCode(reqDTO.getThirdToken());
            String unionId = userInfo.getUnionId();
            // 钉钉没有返回正确的token, 本次扫码已经失效
            logger.info("从钉钉获取用用户信息 userInfo:{}", userInfo);

            if (StringUtils.isBlank(unionId)) {
                return RestResponse.fail(ErrorCode.LOGIN_WECHAT_INVALID);
            }
            // 根据unionId查询用户信息
            DingAuthorizationInfoDTO authInfo = findAuthInfoByUnionId(unionId);
            if (authInfo != null) {
                // 用于之前绑定过该钉钉
                token = new ThirdpartyMobileToken(authInfo.getMobile(),
                        authInfo.getUnionid(), ThirdpartyLoginTypeEnum.DINGTALK);
            } else {
                // 第一次登录
                // 获取用户的信息
                DingAuthorizationInfoDTO dto = new DingAuthorizationInfoDTO();
                dto.setUnionid(unionId);
                dto.setOpenid(userInfo.getOpenId());
                dto.setMobile(userInfo.getMobile());
                dto.setNickname(userInfo.getNick());
                dto.setHeadimgurl(userInfo.getAvatarUrl());
                // 得到本系统的登录相关token
                String sessionToken = generateDingLoginSession(dto);
                return RestResponse.create(
                        UserErrorCode.LOGIN_UNBIND_MOBILE,
                        "该账号尚未绑定手机号",
                        false,
                        sessionToken);
            }
        } else {
            // 构建token
            token = buildToken(reqDTO);
        }
        return doLogin(token);
    }

    /**
     * 微信发送验证码
     */
    @PostMapping(MrcarUrl.ACCOUNT_WECHAT_SEND_VERIFICATION_CODE)
    @RequestFunction(functionName = "微信用户绑定发送验证码")
    @ApiOperation("微信用户绑定发送验证码")
    @SuppressWarnings("unchecked")
    public RestResponse<String> sendWeChatMobileVerificationCode(
            @RequestBody @Validated WeChatBindMobileReqDTO reqDTO) {
        // 发送验证码
        String restUrl = LOCATOR.getRestUrl(UserUrlCenter.WECHAT_SEND_VERIFICATION_CODE);
        Map<String, Object> params = new HashMap<>();
        params.put("token", reqDTO.getToken());
        params.put("mobile", reqDTO.getMobile());
        return RestClient.requestForObject(
                BaseHttpClient.HttpMethod.POST,
                restUrl,
                params,
                null,
                String.class);
    }

    /**
     * 钉钉发送验证码
     */
    @PostMapping(MrcarUrl.ACCOUNT_DING_SEND_VERIFICATION_CODE)
    @RequestFunction(functionName = "钉钉用户绑定发送验证码")
    @ApiOperation("钉钉用户绑定发送验证码")
    @SuppressWarnings("unchecked")
    public RestResponse<String> sendDingMobileVerificationCode(
            @RequestBody @Validated WeChatBindMobileReqDTO reqDTO) {
        // 发送验证码
        String restUrl = LOCATOR.getRestUrl(UserUrlCenter.DING_SEND_VERIFICATION_CODE);
        Map<String, Object> params = new HashMap<>();
        params.put("token", reqDTO.getToken());
        params.put("mobile", reqDTO.getMobile());
        return RestClient.requestForObject(
                BaseHttpClient.HttpMethod.POST,
                restUrl,
                params,
                null,
                String.class);
    }

    /**
     * 微信用户首次绑定手机号
     */
    @PostMapping(MrcarUrl.ACCOUNT_WECHAT_BIND_MOBILE)
    @RequestFunction(functionName = "微信用户首次登录绑定手机号")
    @ApiOperation("微信用户首次登录绑定手机号")
    @SuppressWarnings("unchecked")
    public RestResponse<LoginBaseInfo> loginByBindMobile(@RequestBody @Validated WeChatBindMobileReqDTO reqDTO) {
        // 绑定账号, 并清除上下文
        String restUrl = LOCATOR.getRestUrl(UserUrlCenter.WECHAT_BIND_AUTHORIZATION);
        Map<String, Object> params = new HashMap<>();
        params.put("token", reqDTO.getToken());
        params.put("verifyCode", reqDTO.getVerifyCode());
        params.put("mobile", reqDTO.getMobile());
        RestResponse<WeChatAuthorizationInfoDTO> response = RestClient.requestForObject(
                BaseHttpClient.HttpMethod.POST,
                restUrl,
                params,
                null,
                WeChatAuthorizationInfoDTO.class);
        if (!response.isSuccess()) {
            return RestResponse.fail(response.getCode(), response.getMsg());
        }
        WeChatAuthorizationInfoDTO info = response.getData();
        AuthenticationToken token = new ThirdpartyMobileToken(info.getMobile(),
                info.getOpenid(), ThirdpartyLoginTypeEnum.WECHAT);
        return doLogin(token);
    }


    /**
     * 钉钉用户首次绑定手机号
     */
    @PostMapping(MrcarUrl.ACCOUNT_DING_BIND_MOBILE)
    @RequestFunction(functionName = "钉钉用户首次登录绑定手机号")
    @ApiOperation("钉钉用户首次登录绑定手机号")
    @SuppressWarnings("unchecked")
    public RestResponse<LoginBaseInfo> dingLoginByBindMobile(@RequestBody @Validated DingBindMobileReqDTO reqDTO) {
        // 绑定账号, 并清除上下文
        String restUrl = LOCATOR.getRestUrl(UserUrlCenter.DING_BIND_AUTHORIZATION);
        Map<String, Object> params = new HashMap<>();
        params.put("token", reqDTO.getToken());
        params.put("verifyCode", reqDTO.getVerifyCode());
        params.put("mobile", reqDTO.getMobile());
        RestResponse<DingAuthorizationInfoDTO> response = RestClient.requestForObject(
                BaseHttpClient.HttpMethod.POST,
                restUrl,
                params,
                null,
                DingAuthorizationInfoDTO.class);
        if (!response.isSuccess()) {
            return RestResponse.fail(response.getCode(), response.getMsg());
        }
        DingAuthorizationInfoDTO info = response.getData();
        AuthenticationToken token = new ThirdpartyMobileToken(info.getMobile(),
                info.getOpenid(), ThirdpartyLoginTypeEnum.DINGTALK);
        return doLogin(token);
    }


    /**
     * 用户登出
     */
    @GetMapping(MrcarUrl.ACCOUNT_LOGOUT)
    @RequestFunction(functionName = "用户登出")
    @ApiOperation("用户登出")
    public RestResponse<?> logout() {
        LoginSession session = WebSessionUtil.getLoginSession();
        Subject subject = SecurityUtils.getSubject();
        if (subject.isAuthenticated()) {
            subject.logout();
        }
        loginInfoCacheService.clearCacheLoginInfo(session);
        return RestResponse.success(null);
    }

    /**
     * 快捷跳转系统
     */
    @PostMapping(MrcarUrl.ACCOUNT_QUICK_CHANGE_SYSTEM)
    @RequestFunction(functionName = "快捷跳转系统")
    @ApiOperation("快捷跳转系统")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "systemType", value = "跳转目标系统, 1-运营平台;2-客户平台",
                    required = true, paramType = "form")})
    public RestResponse<LoginBaseInfo> quickChangeSystem(
            @Verify(param = "systemType", rule = "required") Byte systemType) {
        LoginSession session = WebSessionUtil.getLoginSession();
        // 检查目标跳转合法性
        if (Objects.equals(systemType, session.getSystemType())) {
            if (Objects.equals(systemType, (byte)1)) {
                throw ExceptionFactory.createRestException(ErrorCode.UNABLE_TO_PROVIDER_SIDE);
            } else {
                throw ExceptionFactory.createRestException(ErrorCode.UNABLE_TO_CLIENT_SIDE);
            }
        }
        // 构造token
        MobileAccessibleToken token =
                new MobileAccessibleToken(session.getMobile(), LoginSystemEnum.getBySys(systemType));
        return doLogin(token);
    }


    /**
     * 发送手机验证码
     */
    @PostMapping(MrcarUrl.ACCOUNT_SEND_VERIFICATION_CODE)
    @RequestFunction(functionName = "获取短信验证码")
    @ApiOperation("获取短信验证码")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "mobileNum", value = "手机号码", required = true, paramType = "form")})
    public RestResponse<?> sendVerificationCode(
            @Verify(param = "mobileNum", rule = "required|mobile") String mobileNum) {
        // 先从运营端检查用户
        AuthApiService service = authApiServiceFactory.getService(LoginSystemEnum.PROVIDER);
        CustomAuthenticationException exception;
        try {
            service.checkPhone(mobileNum);
            return service.sendLoginVerifyCode(mobileNum);
        } catch (CustomAuthenticationException e) {
            // 没有生效的验证码
            exception = e;
        }
        // 尝试用户账号
        service = authApiServiceFactory.getService(LoginSystemEnum.CLIENT);
        try {
            service.checkPhone(mobileNum);
            return service.sendLoginVerifyCode(mobileNum);
        } catch (CustomAuthenticationException e) {
            // 客户端账号不存在
            if (e.getErrCode() == ErrorCode.CLIENT_PHONE_NOT_EXIST) {
                // 两端账号都不存在
                if (exception.getErrCode() == ErrorCode.PROVIDER_PHONE_NOT_EXIST) {
                    return RestResponse.fail(e.getErrCode());
                } else {
                    return RestResponse.fail(exception.getErrCode());
                }
            } else {
                // 抛出异常信息
                return RestResponse.fail(e.getErrCode());
            }
        }
    }

    /**
     * 获取登录信息
     */
    @GetMapping(MrcarUrl.ACCOUNT_GET_LOGIN_INFO)
    @RequestFunction(functionName = "获取登录信息, 会根据登录系统不同进行返回")
    @ApiOperation("获取登录信息")
    @SuppressWarnings("unchecked")
    public RestResponse<LoginBaseInfo> getLoginInfo() {
        return RestResponse.success(LoginSessionUtil.getBaseLoginInfo());
    }


    @ApiOperation("个人中心，微信解绑账号")
    @RequestFunction(functionName = "个人中心，微信解绑账号")
    @PostMapping(UserUrlCenter.WECHAT_UNBIND)
    public RestResponse<Boolean> unbind(@RequestBody WeChatUnBindReqDTO reqDTO, HttpServletRequest request) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        reqDTO.setMobile(baseLoginInfo.obtainBaseInfo().getMobile());
        reqDTO.setUnbindOperatorIp(IpAddr.getIpAddress(request));
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.WECHAT_UNBIND);
        HashMap<String, Object> param = new HashMap<>(2);
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null);
    }


    @ApiOperation("个人中心，钉钉解绑账号")
    @RequestFunction(functionName = "个人中心，钉钉解绑账号")
    @PostMapping(UserUrlCenter.DING_UNBIND)
    public RestResponse<Boolean> dingUnbind(@RequestBody DingUnBindReqDTO reqDTO, HttpServletRequest request) {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        reqDTO.setMobile(baseLoginInfo.obtainBaseInfo().getMobile());
        reqDTO.setUnbindOperatorIp(IpAddr.getIpAddress(request));
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.DING_UNBIND);
        HashMap<String, Object> param = new HashMap<>(2);
        param.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null);
    }





    @ApiOperation("个人中心，微信绑定账号")
    @RequestFunction(functionName = "个人中心，微信绑定账号")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "thirdToken", value = "微信的token", required = true, paramType = "form")})
    @PostMapping(UserUrlCenter.WECHAT_BIND)
    public RestResponse<Boolean> bind(@Verify(param = "thirdToken", rule = "required") String thirdToken) {
        // 取access_token
        try {
            WeChatAccessTokenResponse response =
                    wechatLoginClient.getAccessTokenByCode(thirdToken);
            // 微信没有返回正确的token, 本次扫码已经失效
            if (StringUtils.isBlank(response.getAccessToken())) {
                return RestResponse.fail(ErrorCode.LOGIN_WECHAT_INVALID);
            }
            // 根据openid查询用户信息
            WeChatUserInfo userInfo =
                    wechatLoginClient.getUserInfoByOpenId(response.getAccessToken(), response.getOpenid());
            WeChatAuthorizationInfoDTO authInfo =
                    BeanUtil.copyObject(userInfo, WeChatAuthorizationInfoDTO.class);
            if (authInfo == null) {
                logger.error("微信绑定账号, openid:{} 不存在", response.getOpenid());
                return RestResponse.fail(ErrorCode.LOGIN_WECHAT_INVALID);
            }
            LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
            authInfo.setMobile(baseLoginInfo.obtainBaseInfo().getMobile());
            String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.WECHAT_BIND);
            HashMap<String, Object> param = new HashMap<>(2);
            param.put(BaseHttpClient.POSTBODY_MAP_KEY, authInfo);
            return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null);
        } catch (IOException e) {
            logger.error("微信扫码绑定失败", e);
            return RestResponse.fail(ErrorCode.LOGIN_WECHAT_INVALID);
        }
    }


    @ApiOperation("个人中心，钉钉绑定账号")
    @RequestFunction(functionName = "个人中心,钉钉绑定账号")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "thirdToken", value = "钉钉扫码的token", required = true, paramType = "form")})
    @PostMapping(UserUrlCenter.DING_BIND)
    public RestResponse<Boolean> dingBind(@Verify(param = "thirdToken", rule = "required") String thirdToken) {
        // 取access_token
        try {
            DingTalkUserInfo userInfo = dingtalkClient.getUserInfoByAuthCode(thirdToken);
            String unionId = userInfo.getUnionId();
            DingAuthorizationInfoDTO authInfo =
                    BeanUtil.copyObject(userInfo, DingAuthorizationInfoDTO.class);
            if (authInfo == null) {
                logger.error("钉钉绑定账号, unionId:{} 不存在", unionId);
                return RestResponse.fail(ErrorCode.LOGIN_WECHAT_INVALID);
            }
            LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
            authInfo.setMobile(baseLoginInfo.obtainBaseInfo().getMobile());
            String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.DING_BIND);
            HashMap<String, Object> param = new HashMap<>(2);
            param.put(BaseHttpClient.POSTBODY_MAP_KEY, authInfo);
            return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null);
        } catch (IOException e) {
            logger.error("钉钉扫码绑定失败", e);
            return RestResponse.fail(ErrorCode.LOGIN_WECHAT_INVALID);
        }
    }


    @ApiOperation("个人中心，查询绑定的微信账号信息")
    @RequestFunction(functionName = "个人中心，查询绑定的微信账号信息")
    @GetMapping(UserUrlCenter.GET_WECHAT_BIND_INFO)
    public RestResponse<WeChatBindInfoDTO> getWeChatBindInfo() {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.GET_WECHAT_BIND_INFO);
        HashMap<String, Object> param = new HashMap<>(2);
        param.put("mobile", baseLoginInfo.obtainBaseInfo().getMobile());
        return RestClient.requestInside(BaseHttpClient.HttpMethod.GET, restUrl, param, null);
    }

    @ApiOperation("个人中心，查询绑定的钉钉账号信息")
    @RequestFunction(functionName = "个人中心，查询绑定的钉钉账号信息")
    @GetMapping(UserUrlCenter.GET_DING_BIND_INFO)
    public RestResponse<WeChatBindInfoDTO> getDingBindInfo() {
        LoginBaseInfo baseLoginInfo = LoginSessionUtil.getBaseLoginInfo();
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.GET_DING_BIND_INFO);
        HashMap<String, Object> param = new HashMap<>(2);
        param.put("mobile", baseLoginInfo.obtainBaseInfo().getMobile());
        return RestClient.requestInside(BaseHttpClient.HttpMethod.GET, restUrl, param, null);
    }



    @SuppressWarnings("unchecked")
    private RestResponse<LoginBaseInfo> doLogin(AuthenticationToken token) {
        // 获取当前认证人
        Subject subject = SecurityUtils.getSubject();
        // 判断当前用户是否登录
        if (subject.isAuthenticated()) {
            subject.logout();
        }
        // 尝试登录
        try {
            subject.login(token);
        } catch (AccountException e) {
            return RestResponse.fail(ErrorCode.CLIENT_PHONE_NOT_EXIST);
        } catch (IncorrectCredentialsException e) {
            String errorMessage = accountLockService.recordError(token.getPrincipal().toString());
            return RestResponse.create(ErrorCode.PASSWORD_ERROR, errorMessage, Boolean.FALSE, null);
        } catch (VerificationCodeErrorException e) {
            return RestResponse.fail(UserErrorCode.MSG_CODE_WRONG);
        } catch (VerificationCodeInvalidException e) {
            return RestResponse.fail(UserErrorCode.MSG_CODE_INVALID);
        } catch (CustomAuthenticationException e) {
            return RestResponse.create(e.getErrCode(), e.getErrMsg(), Boolean.FALSE, null);
        } catch (Exception e) {
            logger.error("登录异常", e);
            return RestResponse.fail(RestErrorCode.HTTP_SYSTEM_ERROR);
        }
        accountLockService.unLock(token.getPrincipal().toString());
        LoginBaseInfo loginBaseInfo =  LoginSessionUtil.getBaseLoginInfo();
        //更新最后一次登录时间
        if(Objects.equals(loginBaseInfo.getSystemType(),LoginSystemEnum.CLIENT.getSys())){
            //更新最后一次登录时间
            AccountBaseInfo accountBaseInfo = loginBaseInfo.obtainBaseInfo();
            loginServiceV2.updateLoginTime(accountBaseInfo.getStaffId(), DateUtil.format(new Date(), DateUtil.TIME_FORMAT));
        }

        return RestResponse.success(loginBaseInfo);
    }

    // 根据不同的登录方式, 构建认证使用的token对象
    private AuthenticationToken buildToken(LoginReqDTO reqDTO) throws IOException {
        switch (reqDTO.getAuthStrategy()) {
            case 1:
                // 手机验证码登录
                return new MobileValidationCodeToken(reqDTO.getMobile(), reqDTO.getVerifyCode());
            case 2:
                // 用户名明码登录
                return new MobilePasswordToken(reqDTO.getMobile(), reqDTO.getPassword());
            case 3:
                // 钉钉登录
            {
                DingTalkUserInfo userInfo
                        = dingtalkClient.getUserInfoByAuthCode(reqDTO.getThirdToken());
                return new ThirdpartyMobileToken(userInfo.getMobile(),
                        userInfo.getOpenId(), ThirdpartyLoginTypeEnum.DINGTALK);
            }
            default:
                break;
        }
        return null;
    }

    // ------------- 微信登录相关逻辑  -----------------------

    @SuppressWarnings("unchecked")
    private WeChatAuthorizationInfoDTO findAuthInfoByOpenId(String openid) {
        String restUrl = LOCATOR.getRestUrl(UserUrlCenter.WECHAT_QUERY_AUTHORIZATION);
        Map<String, Object> params = new HashMap<>();
        params.put("openid", openid);
        RestResponse<WeChatAuthorizationInfoDTO> response = RestClient.requestForObject(
                BaseHttpClient.HttpMethod.POST,
                restUrl,
                params,
                null,
                WeChatAuthorizationInfoDTO.class);
        return response.getData();
    }

    @SuppressWarnings("unchecked")
    private String generateWeChatLoginSession(WeChatAuthorizationInfoDTO reqDTO) {
        String restUrl = LOCATOR.getRestUrl(UserUrlCenter.WECHAT_SAVE_AUTHORIZATION_CONTEXT);
        JSONObject json = JSON.parseObject(JSON.toJSONString(reqDTO));
        Map<String, Object> params = json.getInnerMap();
        RestResponse<String> response = RestClient.requestForObject(
                BaseHttpClient.HttpMethod.POSTBODY,
                restUrl,
                params,
                null,
                String.class);
        return response.getData();
    }




    private DingAuthorizationInfoDTO findAuthInfoByUnionId(String unionId) {
        String restUrl = LOCATOR.getRestUrl(UserUrlCenter.DING_QUERY_AUTHORIZATION);
        Map<String, Object> params = new HashMap<>();
        params.put("unionId", unionId);
        RestResponse<DingAuthorizationInfoDTO> response = RestClient.requestForObject(
                BaseHttpClient.HttpMethod.POST,
                restUrl,
                params,
                null,
                DingAuthorizationInfoDTO.class);
        return response.getData();
    }


    private String generateDingLoginSession(DingAuthorizationInfoDTO reqDTO) {
        String restUrl = LOCATOR.getRestUrl(UserUrlCenter.DING_SAVE_AUTHORIZATION_CONTEXT);
        JSONObject json = JSON.parseObject(JSON.toJSONString(reqDTO));
        Map<String, Object> params = json.getInnerMap();
        RestResponse<String> response = RestClient.requestForObject(
                BaseHttpClient.HttpMethod.POSTBODY,
                restUrl,
                params,
                null,
                String.class);
        return response.getData();
    }

}
