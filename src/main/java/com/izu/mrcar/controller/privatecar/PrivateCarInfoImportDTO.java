package com.izu.mrcar.controller.privatecar;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
* Description:车辆导入列表头
* @Author:Hxc
* @Date:2023/7/21 4:41 PM
*/
@Data
public class PrivateCarInfoImportDTO {

    @ExcelProperty(value = "员工手机号",index = 0)
    private String mobile;

    @ExcelProperty(value = "车牌号",index = 1)
    private String vehicleLicense;

    @ExcelProperty(value = "车架号",index = 2)
    private String vehicleVin;

    @ExcelProperty(value = "车辆品牌 ",index = 3)
    private String vehicleBrand;

    @ExcelProperty(value = "车辆型号 ",index = 4)
    private String vehicleModel;

    @ExcelProperty(value = "车辆颜色 ",index = 5)
    private String vehicleColor;

    @ExcelProperty(value = "行驶证注册日期（格式：yyyy-MM-dd） ",index = 6)
    private String registerDate;

    @ExcelProperty(value = "发动机编号（不必填） ",index = 7)
    private String engineNum;

    @ExcelProperty(value = "所属城市 ",index = 8)
    private String belongCityName;

    @ExcelProperty(value = "是否隐私 ",index = 9)
    private String privacyFlag;
}
