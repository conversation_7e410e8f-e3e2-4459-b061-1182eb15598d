package com.izu.mrcar.controller.privatecar;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import com.google.common.collect.Maps;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.consts.MrcarAssetRestMsgCenter;
import com.izu.asset.consts.VehicleEnum;
import com.izu.asset.dto.CarBrandDTO;
import com.izu.asset.dto.CarImportDTO;
import com.izu.asset.dto.CarModelDTO;
import com.izu.asset.dto.PrivateCarVehicleDTO;
import com.izu.asset.dto.param.PrivateCarQueryDTO;
import com.izu.asset.dto.param.PrivateCarSaveDTO;
import com.izu.config.MrCarConfigRestLocator;
import com.izu.config.dto.CityDicDTO;
import com.izu.config.dto.CompanyBussinessDTO;
import com.izu.config.dto.EnterpriseConfigDTO;
import com.izu.config.dto.PrivateCarPriceSaveDTO;
import com.izu.consts.ConfigURI;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.controller.device.DeviceGpsController;
import com.izu.mrcar.controller.export.PrivateCarExport;
import com.izu.mrcar.service.asset.CarModelService;
import com.izu.mrcar.shiro.session.WebSessionUtil;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.dto.LoginUser;
import com.izu.mrcar.utils.UploadCommon;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.enums.perm.ClientDataPermTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.net.URLEncoder;

/**
 * <AUTHOR> dongxiya
 * @create 2022/11/18 14:24
 */
@Slf4j
@RestController
@Api(tags = "私车公用")
public class PrivateCarController {

    private final MrCarConfigRestLocator configRestLocator = new MrCarConfigRestLocator();
    private final MrCarAssetRestLocator assetRestLocator = new MrCarAssetRestLocator();

    @Autowired
    private CarModelService carModelService;

    @PostMapping(value = MrcarAssetRestMsgCenter.PRIVATECAR_GET_VEHICLE_PAGE)
    @ApiOperation("私车列表")
    public RestResponse<PageDTO<PrivateCarVehicleDTO>> listForPage(@RequestBody PrivateCarQueryDTO dto) {
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        Integer companyId = loginBaseInfo.obtainBelongCompanyId();
        dto.setCompanyId(companyId);
        dto.setSelfOwned(VehicleEnum.SelfOwned.PRIVATECAR.getValue());
        dto.setStaffId(loginBaseInfo.obtainBaseInfo().getStaffId());
        dto.setDataPermType(loginBaseInfo.obtainSimpleDataPerm().getDataPermType());
        if(loginBaseInfo.obtainSimpleDataPerm().getDataPermType().equals(ClientDataPermTypeEnum.ASSIGN_CITY.getType())){
            dto.setDataCodeSet(loginBaseInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet());
        }else if(loginBaseInfo.obtainSimpleDataPerm().getDataPermType().equals(ClientDataPermTypeEnum.ASSIGN_DEPT.getType())){
            //指定部门 先查到人 再查人的车
            Set<String> structIds = loginBaseInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.ID).getDataCodeSet();
            if(CollectionUtils.isNotEmpty(structIds)){
                final HashMap<String, Object> param = Maps.newHashMapWithExpectedSize(1);
                param.put(BaseHttpClient.POSTBODY_MAP_KEY, structIds);
                final String restUrl = new UserRestLocator().getRestUrl("/userManage/getCustomerByStructIds");
                final RestResponse response = RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null, Integer.class);
                if (response!=null && response.isSuccess()) {
                    List<Integer> customerIds = (List<Integer>) response.getData();
                    if(CollectionUtils.isNotEmpty(customerIds)) {
                        dto.setDataCodeSet(customerIds.stream().map(String::valueOf).collect(Collectors.toSet()));
                    }
                }
            }
        }
        Map<String, Object> paraMap = new HashMap<>(1);
        paraMap.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, assetRestLocator.getRestUrl(MrcarAssetRestMsgCenter.PRIVATECAR_GET_VEHICLE_PAGE), paraMap, null, PrivateCarVehicleDTO.class);
    }

    @PostMapping(MrcarAssetRestMsgCenter.PRIVATECAR_SAVE_VEHICLE)
    @ApiOperation("新增/编辑员工车辆")
    public RestResponse saveVehicle(@RequestBody PrivateCarSaveDTO dto) {
        try {
            LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
            Integer companyId = loginBaseInfo.obtainBelongCompanyId();
            dto.setVehicleResource(((byte) 2));
            dto.setCompanyId(companyId);
            dto.setSelfOwned(VehicleEnum.SelfOwned.PRIVATECAR.getValue());
            dto.setOperateId(loginBaseInfo.obtainBaseInfo().getStaffId());
            dto.setOperateName(loginBaseInfo.obtainBaseInfo().getStaffName());
            String restUrl = assetRestLocator.getRestUrl(MrcarAssetRestMsgCenter.PRIVATECAR_SAVE_VEHICLE);
            Map<String, Object> paraMap = new HashMap<>(1);
            paraMap.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paraMap, null, Object.class);
        } catch (Exception e) {
            log.error("新增/编辑员工车辆异常...", e);
            return RestResponse.fail(RestErrorCode.SYSTEM_EXCEPTION);
        }
    }

    @PostMapping(value = MrcarAssetRestMsgCenter.PRIVATECAR_BATCH_SAVE_VEHICLE)
    @ApiOperation("私车列表导入")
    public RestResponse batchInsert(HttpServletRequest request) {
        try {
            LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
            List<List<Object>> upload = UploadCommon.upload(request, 1, -1);
            if (upload == null ) {
                return RestResponse.create(1208, "未按照模板上传，请下载导入模板后重新上传", false, null);
            }
            if(upload.size() == 0){
                return RestResponse.create(1209, "文件内容为空，上传失败。", false, null);
            }
            List<CarBrandDTO> carBrandDTOS = carModelService.getCarBrandDicV2();
            List<CarModelDTO> carModelDTOS = carModelService.getCarModelDicV2();
            List<CityDicDTO> cityDicDTOList = carModelService.getCityDic();
            List<CarImportDTO> carImportDTOs = new ArrayList<>();
            for (List<Object> obj : upload) {
                if (obj == null) {
                    continue;
                }
                CarImportDTO carImportDTO = new CarImportDTO();
                carImportDTO.setMobile(DeviceGpsController.getString(obj, 1));
                carImportDTO.setVehicleLicense(DeviceGpsController.getString(obj, 2));
                carImportDTO.setVehicleVin(DeviceGpsController.getString(obj, 3));
                String vehicleModel = DeviceGpsController.getString(obj, 5);
                CarModelDTO carModelDTO = null;
                if(StringUtils.isNotBlank(vehicleModel)){
                    carImportDTO.setVehicleModel(vehicleModel);
                    carModelDTO = carModelDTOS.stream().filter(o->o.getModelName().equals(vehicleModel)).findFirst().orElse(null);
                    if(carModelDTO!=null) {
                        carImportDTO.setVehicleModelCode(carModelDTO.getModelCode());
                        carImportDTO.setVehicleModel(carModelDTO.getModelName());
                    }
                }
                String vehicleBrand = DeviceGpsController.getString(obj, 4);
                if(StringUtils.isNotBlank(vehicleBrand)){
                    carImportDTO.setVehicleBrand(vehicleBrand);
                    CarBrandDTO carBrandDTO = null;
                    if(carModelDTO != null){
                        CarModelDTO finalCarModelDTO = carModelDTO;
                        carBrandDTO = carBrandDTOS.stream().filter(o -> o.getBrandName().equals(vehicleBrand)
                        && o.getBrandId().equals(finalCarModelDTO.getBrandId())).findFirst().orElse(null);
                    }
                    if(Objects.isNull(carBrandDTO)){
                        carBrandDTO = carBrandDTOS.stream().filter(o->o.getBrandName().equals(vehicleBrand)).findFirst().orElse(null);
                    }
                    if(carBrandDTO !=null) {
                        carImportDTO.setVehicleBrandCode(carBrandDTO.getBrandCode());
                        carImportDTO.setVehicleBrand(carBrandDTO.getBrandName());
                    }
                }

                carImportDTO.setVehicleColor(DeviceGpsController.getString(obj, 6));
                carImportDTO.setRegisterDate(DeviceGpsController.getString(obj, 7));
                carImportDTO.setEngineNum(DeviceGpsController.getString(obj, 8));
                String belongCityName = DeviceGpsController.getString(obj, 9);
                if(StringUtils.isNotBlank(DeviceGpsController.getString(obj,10))){
                    String privacyFlagName = DeviceGpsController.getString(obj, 10);
                    if(Objects.equals(privacyFlagName,"否")){
                        carImportDTO.setPrivacyFlag(Byte.valueOf("0"));
                    } else {
                        carImportDTO.setPrivacyFlag(Byte.valueOf("1"));
                    }
                }

                if(StringUtils.isNotBlank(belongCityName)){
                    carImportDTO.setBelongCityName(belongCityName);
                    CityDicDTO cityDicDTO = cityDicDTOList.stream().filter(o->o.getCityName().equals(belongCityName)).findFirst().orElse(null);
                    if(cityDicDTO!=null){
                        carImportDTO.setBelongCityCode(cityDicDTO.getCityCode().toString());
                    }
                }
                carImportDTOs.add(carImportDTO);
            }
            Map<String, Object> paramMap = new HashMap<>(5);
            paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, carImportDTOs);
            paramMap.put("createId", loginBaseInfo.obtainBaseInfo().getStaffId());
            paramMap.put("createName", loginBaseInfo.obtainBaseInfo().getStaffName());
            paramMap.put("companyId", loginBaseInfo.obtainBelongCompanyId());
            if(loginBaseInfo.obtainSimpleDataPerm().getDataPermType().equals(ClientDataPermTypeEnum.ASSIGN_CITY.getType())){
                paramMap.put("cityDataScope", loginBaseInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet());
            }
            String restUrl = assetRestLocator.getRestUrl(MrcarAssetRestMsgCenter.PRIVATECAR_BATCH_SAVE_VEHICLE);
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, Object.class);
        } catch (Exception e) {
            log.error("私车导入异常,e:{}", e);
            return RestResponse.create(1208, "未按照模板上传，请下载导入模板后重新上传", false, null);
        }
    }

    @PostMapping(value = MrcarAssetRestMsgCenter.PRIVATECAR_GET_VEHICLE_PAGE_EXPORT)
    @ApiOperation("私车列表导出")
    public RestResponse export(@RequestBody PrivateCarQueryDTO dto, HttpServletResponse response) {
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        Integer companyId = loginBaseInfo.obtainBelongCompanyId();
        dto.setDataPermType(loginBaseInfo.obtainSimpleDataPerm().getDataPermType());
        if(loginBaseInfo.obtainSimpleDataPerm().getDataPermType().equals(ClientDataPermTypeEnum.ASSIGN_CITY.getType())){
            dto.setDataCodeSet(loginBaseInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet());
        }else if(loginBaseInfo.obtainSimpleDataPerm().getDataPermType().equals(ClientDataPermTypeEnum.ASSIGN_DEPT.getType())){
            dto.setDataCodeSet(loginBaseInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.ID).getDataCodeSet());
        }
        dto.setCompanyId(companyId);
        dto.setSelfOwned(VehicleEnum.SelfOwned.PRIVATECAR.getValue());
        dto.setPageNum(1);
        dto.setPageSize(200);
        Map<String, Object> paraMap = new HashMap<>(1);
        paraMap.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        String restUrl = assetRestLocator.getRestUrl(MrcarAssetRestMsgCenter.PRIVATECAR_GET_VEHICLE_PAGE);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paraMap, null, PrivateCarVehicleDTO.class);
        if (restResponse.isSuccess()) {
            PageDTO pageDto = (PageDTO) restResponse.getData();
            List<PrivateCarVehicleDTO> dtos = new ArrayList<>((int) pageDto.getTotal());
            dtos.addAll(pageDto.getResult());
            int pages = pageDto.getPages();
            while (dto.getPageNum() <= pages) {
                dto.setPageNum(dto.getPageNum() + 1);
                restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paraMap, null, PrivateCarVehicleDTO.class);
                if (!restResponse.isSuccess()) {
                    continue;
                }
                pageDto = (PageDTO) restResponse.getData();
                dtos.addAll(pageDto.getResult());
            }
            List<PrivateCarExport> collect = dtos.stream().map(
                    carInfoDTO -> {
                        PrivateCarExport carExport = BeanUtil.copyObject(carInfoDTO, PrivateCarExport.class);
                        return carExport;
                    }
            ).collect(Collectors.toList());
            Workbook workbook = ExcelExportUtil.exportExcel(new ExportParams(), PrivateCarExport.class, collect);
            String fileName = "员工车辆信息_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
            workbook.setSheetName(0, fileName);
            try (OutputStream outputStream = response.getOutputStream()) {
                response.setContentType("application/vnd.ms-excel;charset=utf-8");
                fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
                response.setHeader("Content-disposition", "attachment;filename*=utf-8''"  + fileName + ".xls");
                workbook.write(outputStream);
            } catch (Exception e) {
                log.error("导出Excel异常");
            }
        } else {

        }
        return null;
    }

    @PostMapping(ConfigURI.PRIVATECAR_SAVE_PRICE)
    @ApiOperation("私车公用保存价格配置")
    public RestResponse<PrivateCarPriceSaveDTO> savePrice(@RequestBody PrivateCarPriceSaveDTO dto) {
        try {
            LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
            Integer companyId = loginBaseInfo.obtainBelongCompanyId();
            dto.setCompanyId(companyId);
            dto.setCreateId(loginBaseInfo.obtainBaseInfo().getStaffId());
            Map<String, Object> paraMap = new HashMap<>(1);
            paraMap.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
            String restUrl = configRestLocator.getRestUrl(ConfigURI.PRIVATECAR_SAVE_PRICE);
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paraMap, null, PrivateCarPriceSaveDTO.class);
        } catch (Exception e) {
            log.error("私车公用保存价格配置...", e);
            return RestResponse.fail(RestErrorCode.SYSTEM_EXCEPTION);
        }
    }

    @PostMapping(ConfigURI.PRIVATECAR_GET_PRICE)
    @ApiOperation("获取私车公用价格配置")
    public RestResponse<PrivateCarPriceSaveDTO> getPrice() {
        try {
            PrivateCarPriceSaveDTO dto = new PrivateCarPriceSaveDTO();
            LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
            Integer companyId = loginBaseInfo.obtainBelongCompanyId();
            dto.setCompanyId(companyId);
            Map<String, Object> paraMap = new HashMap<>();
            paraMap.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
            String restUrl = configRestLocator.getRestUrl(ConfigURI.PRIVATECAR_GET_PRICE);
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paraMap, null, PrivateCarPriceSaveDTO.class);
        } catch (Exception e) {
            log.error("获取私车公用价格配置...", e);
            return RestResponse.fail(RestErrorCode.SYSTEM_EXCEPTION);
        }
    }

    @PostMapping(ConfigURI.CONFIG_ENTERPRISE_SAVE)
    @ApiOperation("保存企业配置")
    public RestResponse saveEnterpriseConfig(@RequestBody EnterpriseConfigDTO dto) {
        try {
            LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
            String restUrl = configRestLocator.getRestUrl(ConfigURI.CONFIG_ENTERPRISE_SAVE);
            dto.setLoginName(loginBaseInfo.obtainBaseInfo().getStaffName());
            dto.setLoginId(loginBaseInfo.obtainBaseInfo().getStaffId());
            Map<String, Object> paraMap = new HashMap<>(1);
            paraMap.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paraMap, null, EnterpriseConfigDTO.class);
        } catch (Exception e) {
            log.error("保存企业配置异常...", e);
            return RestResponse.fail(RestErrorCode.SYSTEM_EXCEPTION);
        }
    }

    @PostMapping(ConfigURI.CONFIG_ENTERPRISE_GET)
    @ApiOperation("获取企业配置")
    public RestResponse<EnterpriseConfigDTO> getEnterpriseConfig(@RequestBody EnterpriseConfigDTO dto) {
        try {
            Map<String, Object> paraMap = new HashMap<>(1);
            paraMap.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
            String restUrl = configRestLocator.getRestUrl(ConfigURI.CONFIG_ENTERPRISE_GET);
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paraMap, null, EnterpriseConfigDTO.class);
        } catch (Exception e) {
            log.error("获取企业配置异常...", e);
            return RestResponse.fail(RestErrorCode.SYSTEM_EXCEPTION);
        }
    }

    @PostMapping(ConfigURI.CONFIG_COMPANY_BUSINESS_GET_BY_BUSINESS_CODE)
    @ApiOperation("根据businessCode获取企是否开通该配置")
    @ApiImplicitParam(name = "businessCode",value = "业务编码，(16下单备注必填)",required = true)
    public RestResponse getByCompanyId(
            @Verify(param = "businessCode", rule = "required") Integer businessCode
    ) {
        Map<String,Object> paraMap = new HashMap<>(2);
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        Integer companyId = loginBaseInfo.obtainBelongCompanyId();
        paraMap.put("companyId", companyId);
        paraMap.put("businessCode", businessCode);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, new MrCarConfigRestLocator().getRestUrl(ConfigURI.CONFIG_COMPANY_BUSINESS_GET_BY_BUSINESS_CODE), paraMap,null);
    }
}
