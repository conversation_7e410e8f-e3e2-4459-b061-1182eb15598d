package com.izu.mrcar.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 公共常用方法
 */
public abstract class AbstractBaseController {
    /**
     * 封装请求参数，返回Map 方便透传
     **/
    protected Map<String, Object> getRequestParameters(final HttpServletRequest request) {
        Map<String, String[]> requestParameterMapMap = request.getParameterMap();
        HashMap<String, Object> resultMap = new HashMap<>();
        for (Map.Entry<String, String[]> entry : requestParameterMapMap.entrySet()) {
            String key = entry.getKey();
            String[] value = entry.getValue();
            if (key == null || key.length() == 0 || value == null || value.length == 0) {
                continue;
            }
            resultMap.put(key.trim(), StringUtils.join(value, ','));
        }
        return resultMap;
    }

    protected String getRestUrl(String url) {
        return "";
    }

    /**
     * 添加当前登录人信息
     */
    protected Map<String, Object> loginUserMap() {
        return null;
    }

    public Map<String, Object> httpParams(HttpServletRequest request, BaseHttpClient.HttpMethod method, Map<String, Object> others) throws IOException {
        Map<String, Object> httpParams = new HashMap<>();

        Map<String, String[]> parameterMap = request.getParameterMap();
        for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
            String key = entry.getKey();
            String[] value = entry.getValue();
            if (value == null) continue;
            if (value.length == 1) {
                httpParams.put(key, value[0]);
            } else {
                httpParams.put(key, Arrays.asList(value));
            }
        }

        // 当前登录人信息
        Map<String, Object> userMap = loginUserMap();
        if (!CollectionUtils.isEmpty(userMap)) {
            httpParams.putAll(userMap);
        }

        // json数据
        String contentType = Optional.ofNullable(request.getContentType()).map(StringUtils::trim).map(String::toLowerCase).orElse(null);
        if (BaseHttpClient.HttpMethod.POSTBODY == method) {
            String body = "{}";
            if (contentType != null && contentType.contains(MediaType.APPLICATION_JSON_VALUE)) {
                body = JSON.parseObject(request.getInputStream(), String.class);
            }
            body = StringUtils.defaultIfBlank(body, "{}");
            if (JSON.isValidObject(body)) {
                JSONObject bodyObject = JSONObject.parseObject(body);
                if (bodyObject != null) {
                    if (!CollectionUtils.isEmpty(userMap)) {
                        bodyObject.putAll(userMap);
                    }
                    if (!CollectionUtils.isEmpty(others)) {
                        bodyObject.putAll(others);
                    }
                    body = JSON.toJSONString(bodyObject);
                }
            }
            httpParams.put(BaseHttpClient.POSTBODY_MAP_KEY, StringUtils.defaultIfBlank(body, ""));
        }

        // 其它信息
        if (!CollectionUtils.isEmpty(others)) {
            httpParams.putAll(others);
        }
        return httpParams;
    }

    // ================================================================ 请求 =============================================================================
    public RestResponse get(String url, HttpServletRequest request, Map<String, String> httpHeaders, Map<String, Object> others) throws IOException {
        return request(BaseHttpClient.HttpMethod.GET, url, request, httpHeaders, others);
    }

    @Deprecated
    public RestResponse post(String url, HttpServletRequest request, Map<String, String> httpHeaders, Map<String, Object> others) throws IOException {
        return request(BaseHttpClient.HttpMethod.POST, url, request, httpHeaders, others);
    }

    @Deprecated
    public RestResponse postBody(String url, HttpServletRequest request, Map<String, String> httpHeaders, Map<String, Object> others) throws IOException {
        return request(BaseHttpClient.HttpMethod.POSTBODY, url, request, httpHeaders, others);
    }

    private RestResponse request(BaseHttpClient.HttpMethod method, String url, HttpServletRequest request, Map<String, String> httpHeaders, Map<String, Object> others) throws IOException {
        return RestClient.requestInside(method, getRestUrl(url), httpParams(request, method, others), httpHeaders);
    }

    protected Map<String,Object> getRequestParameters(final JSONObject jsonObject){
        return jsonObject.entrySet().stream().collect(Collectors.toMap(c -> c.getKey(), c -> c.getValue()));
    }
}