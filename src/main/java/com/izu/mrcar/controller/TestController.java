package com.izu.mrcar.controller;

import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.service.user.ClearUserLoginInfoService;
import com.izu.user.enums.LoginSystemEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date : 2023/3/3
 */
@RestController
public class TestController {

    @Autowired
    private ClearUserLoginInfoService clearUserLoginInfoService;


    @RequestMapping("/clear/perm")
    public RestResponse clear(String phone,Byte loginSystem){
        final LoginSystemEnum bySys = LoginSystemEnum.getBySys(loginSystem);
        clearUserLoginInfoService.clearLoginInfoAndAuthorPerm(phone,bySys);
        return RestResponse.success(null);
    }

}
