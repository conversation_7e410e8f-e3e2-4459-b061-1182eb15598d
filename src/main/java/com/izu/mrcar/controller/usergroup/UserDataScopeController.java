package com.izu.mrcar.controller.usergroup;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.izu.framework.doc.JrdApiDoc;
import com.izu.framework.doc.JrdDoc;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.shiro.session.WebSessionUtil;
import com.izu.user.dto.LoginUser;
import com.izu.mrcar.common.config.RestUrlConfig;

@RestController
@RequestMapping("/userDataScope")
@JrdDoc(name = "用户数据权限查询")
public class UserDataScopeController {
	private static final Logger logger = LoggerFactory.getLogger(UserDataScopeController.class);

	@RequestMapping("/getCityCodesOfUser")
    @JrdApiDoc(simpleDesc="查询数据权限的城市",detailDesc="多车监控使用",
            author="贾朝阳", resDataClass=Map.class, resDataDesc="")
    public RestResponse getCityCodesOfUser(){
		String restUrl = RestUrlConfig.getConfig().getConfigCoreUrl() + "/city/selectCityInfoByCityCode";
		// 获取当前登录用户信息
		LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
		logger.info("【获取当前登录用户的信息】"+ JSON.toJSONString(loginBaseInfo));
		Map<String,Object> paraMap = new HashMap<>();
//		todo 数据权限
//		paraMap.put("cityCodeSet", loginUser.getCityDataScope());
		RestResponse responseCity = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Map.class);
		List<Map> cityList = new ArrayList<Map>();
		
		
		Map<String,Object> paraMapFocus = new HashMap<>();
		paraMapFocus.put("focusStatus", 1);
//		todo 数据权限
//		paraMapFocus.put("dataScope", loginUser.getCityDataScope());
		paraMapFocus.put("companyId", loginBaseInfo.obtainBelongCompanyId());
		paraMapFocus.put("customerId", loginBaseInfo.obtainBaseInfo().getStaffId());
		paraMapFocus.put("pageNo", 1);
		paraMapFocus.put("pageSize", 100);
		RestResponse response = RestClient.requestForPage(BaseHttpClient.HttpMethod.POST, RestUrlConfig.getConfig().getAssetCoreUrl() + "/carLocation/listForPage", paraMapFocus, null, Map.class);
		logger.info("返回结果：{}",JSON.toJSONString(response));
		if(response.isSuccess()&&response.getData()!=null){
			PageDTO page = (PageDTO) response.getData();
			List<Map> list = (List<Map>) page.getResult();
			if(list.size()>0){
				Map<String, String> focus=new HashMap<String, String>();
				focus.put("cityCode", "0");
				focus.put("cityName", "我的关注");
				cityList.add(focus);
			}
		}
		if(responseCity.getData()!=null){
			cityList.addAll((List<Map>) responseCity.getData()) ;
		}
		return RestResponse.success(cityList);
	}
	
}
