package com.izu.mrcar.controller.usergroup;

import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.izu.framework.doc.JrdApiDoc;
import com.izu.framework.doc.JrdDoc;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.config.RestUrlConfig;

@RestController
@RequestMapping("/companyManage")
@JrdDoc(name = "企业信息")
public class CompanyManageNewController {
	private static final Logger logger = LoggerFactory.getLogger(CompanyManageNewController.class);
	
	@RequestMapping("/getCompanyList")
    @JrdApiDoc(simpleDesc="查询企业列表信息",detailDesc="下拉框使用，限制条数50",
            author="贾朝阳", resDataClass=Map.class, resDataDesc="")
    public RestResponse getCompanyList(@RequestBody JSONObject restObject){
        Map<String, Object> parames = restObject.getInnerMap();
        Integer pageNum = restObject.getInteger("pageNum");
        Integer pageSize = restObject.getInteger("pageSize");
        parames.put("pageNum", pageNum==null?1:pageNum);
		parames.put("pageSize", pageSize==null?50:pageSize);
        String url= RestUrlConfig.getConfig().getUserCoreUrl()+"/companyManage/getCompanyList.json";
        Object result = null;
        RestResponse restResponse=RestClient.requestForPage(BaseHttpClient.HttpMethod.POST, url, parames, null, Map.class);
        if (restResponse != null && restResponse .isSuccess()){
            PageDTO pageDto = (PageDTO) restResponse.getData();
            if (pageDto != null){
                result = pageDto.getResult();
            }
        }
        return RestResponse.success(result);
    }
}
