package com.izu.mrcar.controller.maintain.booking;

import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.maintain.booking.*;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.maintain.ClientMaintainDataPermUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
* @Description: 客户端-维保预约接口
* @author: hxc
* @Date: 2023/3/30
**/
@RestController
@Api(tags = "维保管理")
public class ClientMaintainBookingController {

    private final MrCarAssetRestLocator mrCarAssetRestLocator = new MrCarAssetRestLocator();

    @PostMapping(MrCarAssetRestCenter.CLIENT_MAINTAIN_BOOKING_GET_PAGE_LIST)
    @RequestFunction(functionName = "维保预约单分页列表-客户端")
    @ApiOperation(value = "维保预约单分页列表（客户端）",notes = "作者：贺新春")
    public RestResponse<PageDTO<MaintainBookingOrderClientPageRespDTO>> getPageList(@RequestBody MaintainBookingOrderClientPageReqDTO param){

        param.setCompanyCodes(ClientMaintainDataPermUtil.getDatePermCompanyScope(param.getCompanyCodes()));
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CLIENT_MAINTAIN_BOOKING_GET_PAGE_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, MaintainBookingOrderClientPageRespDTO.class);
    }

    @PostMapping(MrCarAssetRestCenter.CLIENT_MAINTAIN_BOOKING_EXPORT)
    @RequestFunction(functionName = "维保预约单导出-客户端")
    @ApiOperation(value = "维保预约单导出（客户端）",notes = "作者：贺新春")
    @ExportExcelWeb(fileName = ExportExcelConstants.FILE_NAME_MAINTAIN_BOOKING_ORDER, filePath = ExportExcelConstants.FILE_PATH,
            sheet = ExportExcelConstants.SHEET_NAME_MAINTAIN_BOOKING_ORDER, c = ClientMaintainBookingOrderExportDTO.class)
    public PageDTO<ClientMaintainBookingOrderExportDTO> exportForClient(@RequestBody MaintainBookingOrderClientPageReqDTO param, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response) {

        Set<String> companyScope = ClientMaintainDataPermUtil.getDatePermCompanyScope(param.getCompanyCodes());
        param.setCompanyCodes(companyScope);
        izuEasyExcelSession.setUserName(LoginSessionUtil.getClientLoginInfo().getBaseInfo().getStaffName());
        param.setPage(izuEasyExcelSession.getPageNo());
        param.setPageSize(ExportExcelConstants.PAGE_SIZE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CLIENT_MAINTAIN_BOOKING_GET_PAGE_LIST);
        RestResponse<PageDTO<MaintainBookingOrderClientPageRespDTO>> restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null,MaintainBookingOrderClientPageRespDTO.class);
        if(restResponse != null && restResponse.isSuccess()){
            PageDTO<MaintainBookingOrderClientPageRespDTO> data = restResponse.getData();
            if (data.getTotal()>10000){
                throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
            }
            List<ClientMaintainBookingOrderExportDTO> exportDTOList = BeanUtil.copyList(data.getResult(), ClientMaintainBookingOrderExportDTO.class);
            return new PageDTO(data.getPage(), data.getPageSize(), data.getTotal(), exportDTOList);
        }
        return null;
    }
}
