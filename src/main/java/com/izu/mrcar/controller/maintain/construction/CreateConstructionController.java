package com.izu.mrcar.controller.maintain.construction;

import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.maintain.construction.MaintainConstructionCreationReqDTO;
import com.izu.asset.dto.maintain.workHour.MaintainPriceDTO;
import com.izu.asset.dto.maintain.workHour.WorkUnitQueryForPCDTO;
import com.izu.asset.dto.vehicle.req.VehicleForConstructionReqDTO;
import com.izu.asset.dto.vehicle.resp.VehicleForConstructionRespDTO;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.dto.maintain.GarageForConstructionReqDTO;
import com.izu.user.dto.maintain.GarageForConstructionRespDTO;
import com.izu.user.dto.maintain.UserForConstructionReqDTO;
import com.izu.user.dto.maintain.UserForConstructionRespDTO;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@Api(tags = "维保单补录相关接口")
public class CreateConstructionController {
    private final MrCarAssetRestLocator mrCarAssetRestLocator = new MrCarAssetRestLocator();
    private final UserRestLocator userRestLocator = new UserRestLocator();
    @PostMapping("/car/getVehicleForConstruction")
    @RequestFunction(functionName = "维保补录-选择车辆-客户端")
    @ApiOperation(value = "维保补录-选择车辆（客户端）", notes = "作者：丁伟兵")
    public RestResponse<List<VehicleForConstructionRespDTO>> getVehicleForConstruction(@RequestBody VehicleForConstructionReqDTO param) {
        ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
        param.setLoginCompanyId(clientLoginInfo.getClientCompany().getCompanyId());
        String url = mrCarAssetRestLocator.getRestUrl("/car/getVehicleForConstruction");
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, VehicleForConstructionRespDTO.class);
    }
    @PostMapping(UserUrlCenter.CLIENT_GARAGE_SEARCH_BY_NAME)
    @RequestFunction(functionName = "维保补录-选择维修厂-客户端")
    @ApiOperation(value = "维保补录-选择维修厂（客户端）", notes = "作者：丁伟兵")
    public RestResponse<List<GarageForConstructionRespDTO>> getGarageByName(@RequestBody GarageForConstructionReqDTO param) {
        ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
        param.setLoginCompanyId(clientLoginInfo.getClientCompany().getCompanyId());
        String url = userRestLocator.getRestUrl(UserUrlCenter.CLIENT_GARAGE_SEARCH_BY_NAME);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, GarageForConstructionRespDTO.class);
    }
    @PostMapping(UserUrlCenter.CLIENT_USER_SEARCH_BY_NAME)
    @RequestFunction(functionName = "维保补录-选择员工-客户端")
    @ApiOperation(value = "维保补录-选择员工（客户端）", notes = "作者：丁伟兵")
    public RestResponse<List<UserForConstructionRespDTO>> getUserInfoForConstruction(@RequestBody UserForConstructionReqDTO param) {
        ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
        param.setLoginCompanyId(clientLoginInfo.getClientCompany().getCompanyId());
        String url = userRestLocator.getRestUrl(UserUrlCenter.CLIENT_USER_SEARCH_BY_NAME);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, UserForConstructionRespDTO.class);
    }
    @PostMapping(MrCarAssetRestCenter.MAINTAIN_CONSTRUCTION_GET_WORK_INFO_FOR_PC)
    @RequestFunction(functionName = "维保补录-选择标准工时-客户端")
    @ApiOperation(value = "维保补录-选择标准工时-客户端",notes = "作者：丁伟兵")
    public RestResponse<MaintainPriceDTO> getBookingInfo(@RequestBody WorkUnitQueryForPCDTO param){
        ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
        param.setLoginCompanyCode(clientLoginInfo.getClientCompany().getCompanyCode());
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.MAINTAIN_CONSTRUCTION_GET_WORK_INFO_FOR_PC);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }
    @PostMapping(MrCarAssetRestCenter.CLIENT_COMPANY_MAINTAIN_CREATION)
    @RequestFunction(functionName = "维保补录-创建接口-客户端")
    @ApiOperation(value = "维保补录-创建接口-客户端",notes = "作者：丁伟兵")
    public RestResponse<Boolean> maintainCreation(@RequestBody MaintainConstructionCreationReqDTO param){
        ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
        param.setLoginCompanyCode(clientLoginInfo.getClientCompany().getCompanyCode());
        param.setLoginCompanyId(clientLoginInfo.getClientCompany().getCompanyId());
        param.setLoginCompanyName(clientLoginInfo.getClientCompany().getCompanyName());
        param.setLoginUserId(clientLoginInfo.getBaseInfo().getStaffId());
        param.setLoginUserCode(clientLoginInfo.getBaseInfo().getStaffCode());
        param.setLoginUserName(clientLoginInfo.getBaseInfo().getStaffName());
        param.setLoginUserPhone(clientLoginInfo.getBaseInfo().getMobile());
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CLIENT_COMPANY_MAINTAIN_CREATION);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }
}
