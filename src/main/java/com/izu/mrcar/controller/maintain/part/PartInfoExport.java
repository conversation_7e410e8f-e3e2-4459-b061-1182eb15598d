package com.izu.mrcar.controller.maintain.part;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import izu.org.apache.poi.ss.usermodel.BorderStyle;
import izu.org.apache.poi.ss.usermodel.HorizontalAlignment;
import lombok.Data;

import java.util.Date;

@HeadStyle(
        fillForegroundColor = 22,
        fillBackgroundColor = 22
)
@ColumnWidth(20)
@ContentStyle(
        horizontalAlignment = HorizontalAlignment.CENTER,
        borderBottom = BorderStyle.THIN,
        borderLeft = BorderStyle.THIN,
        borderRight = BorderStyle.THIN,
        borderTop = BorderStyle.THIN,
        wrapped = true
)
@Data
public class PartInfoExport {
    @ExcelProperty(
            value = {"配件名称"},
            order = 1
    )
    private String partName;
    @ExcelProperty(
            value = {"配件编码"},
            order = 2
    )
    private String partNo;
    @ExcelProperty(
            value = {"配件分类"},
            order = 3
    )
    private String categoryName;
    @ExcelProperty(
            value = {"单位"},
            order = 4
    )
    private String partUnit;
    @ExcelProperty(
            value = {"配件状态"},
            order = 5
    )
    private String stateStr;

    @ExcelProperty(
            value = {"创建人"},
            order = 6
    )
    private String createrName;
    @ExcelProperty(
            value = {"创建时间"},
            order = 7
    )
    private Date createTime;
    @ExcelProperty(
            value = {"修改人"},
            order = 8
    )
    private String updaterName;
    @ExcelProperty(
            value = {"修改时间"},
            order = 9
    )
    private Date updateTime;
}
