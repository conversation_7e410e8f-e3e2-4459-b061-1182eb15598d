package com.izu.mrcar.controller.maintain.workhours;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: 工时基数导入
 * @date 2023/5/22 1:38
 */
@Data
public class WorkBaseImportDTO {

    /**
     * 维修项目名称 repair_item_name
     */
    @ExcelProperty(index = 0,value = "维修名称")
    private String repairItemName;

    /**
     * 维修组名称 repair_type_name
     */
    @ExcelProperty(index = 1,value = "维修组名称")
    private String repairTypeName;

    /**
     * 工时费 repair_labor_amount
     */
    @ExcelProperty(index = 2,value = "工时基数")
    private BigDecimal repairLaborAmount;

    /**
     * 是否常用项目（1:是 0:否） is_frequently
     */
    @ExcelProperty(index = 3,value = "是否常用")
    private String isFrequently;

    /**
     * 是否必选（1:是 0:否） is_required
     */
    @ExcelProperty(index = 4,value = "是否必选")
    private String isRequired;

    /**
     * 必选生效日期 valid_sdate
     */
    @ExcelProperty(index = 5,value = "生效日期")
    private Date validSdate;

    /**
     * 必选失效日期 valid_edate
     */
    @ExcelProperty(index = 6,value = "失效日期")
    private Date validEdate;

    /**
     * 状态（1：启用 4：禁用） state
     */
    @ExcelProperty(index = 7,value = "状态")
    private String stateName;

}
