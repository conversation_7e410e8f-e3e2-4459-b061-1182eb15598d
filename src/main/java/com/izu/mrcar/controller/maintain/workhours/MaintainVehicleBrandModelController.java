package com.izu.mrcar.controller.maintain.workhours;

import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.maintain.workHour.MaintainVehicleBrandModelDTO;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description: 维保车辆品牌车型
 * @author: hxc
 * @create: 2023-04-27 13:26
 **/
@RestController
@Api(tags = "维保管理")
public class MaintainVehicleBrandModelController {

    private final MrCarAssetRestLocator mrCarAssetRestLocator = new MrCarAssetRestLocator();

    @GetMapping(MrCarAssetRestCenter.MAINTAIN_WORK_BASE_GET_VEHICLE_BRAND_MODEL)
    @RequestFunction(functionName = "获取维保车辆类型品牌车型")
    @ApiOperation(value = "获取维保车辆类型品牌车型",notes = "作者：贺新春")
    @ApiImplicitParams({@ApiImplicitParam(name = "type",value = "类型（1车辆类型 2车辆品牌 3车辆厂牌 4车型）",required = true, paramType = "form"),
            @ApiImplicitParam(name = "code", value = "车辆类型/车辆品牌/车辆厂牌的code", paramType = "form")
    })
    public RestResponse<List<MaintainVehicleBrandModelDTO>> getVehicleType(@Verify(param = "type", rule = "required") Byte type,
                                                                           @Verify(param = "code", rule = "") String code){
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.MAINTAIN_WORK_BASE_GET_VEHICLE_BRAND_MODEL);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("type",type);
        paramMap.put("code",code);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.GET, url, paramMap, null);
    }
}
