package com.izu.mrcar.controller.maintain.garage;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.google.common.collect.Sets;
import izu.org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import izu.org.apache.poi.ss.usermodel.Sheet;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/10/25 17:30
 */
public class DbCarExcelSheetWriteHandler implements SheetWriteHandler {

    // 设置100列column
    private static final Integer COLUMN = 8;

    private Set<Integer> formatColumnList = Sets.newHashSet(6, 7);

    public DbCarExcelSheetWriteHandler(Set<Integer> formatColumnList) {
        this.formatColumnList = formatColumnList;
    }

    @Override
    public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {

    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        for (int i = 0; i < COLUMN; i++) {
            if(formatColumnList.contains(i)){
                // 设置为文本格式
                Sheet sxssfSheet = writeSheetHolder.getSheet();
                CellStyle cellStyle = writeWorkbookHolder.getCachedWorkbook().createCellStyle();
                // 49为文本格式
                cellStyle.setDataFormat((short) 49);
                // i为列，一整列设置为文本格式
                sxssfSheet.setDefaultColumnStyle(i, cellStyle);
            }
        }
    }
}
