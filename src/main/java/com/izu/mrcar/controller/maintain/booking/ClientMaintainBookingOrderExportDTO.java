package com.izu.mrcar.controller.maintain.booking;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import izu.org.apache.poi.ss.usermodel.BorderStyle;
import izu.org.apache.poi.ss.usermodel.HorizontalAlignment;
import lombok.Data;
import java.util.Date;

@HeadStyle(
        fillForegroundColor = 22,
        fillBackgroundColor = 22
)
@ContentStyle(
        horizontalAlignment = HorizontalAlignment.CENTER,
        borderBottom = BorderStyle.THIN,
        borderLeft = BorderStyle.THIN,
        borderRight = BorderStyle.THIN,
        borderTop = BorderStyle.THIN,
        wrapped = true
)

@ApiModel
@Data
public class ClientMaintainBookingOrderExportDTO {

    @ApiModelProperty(value = "预约单号",example = "YY2023040400001")
    @ColumnWidth(value = 20)
    @ExcelProperty(value = "预约单号")
    private String orderNo;

    @ApiModelProperty(value = "维修厂名称",example = "XXX维修厂")
    @ColumnWidth(value = 30)
    @ExcelProperty(value = "维修厂名称")
    private String garageName;

    @ApiModelProperty(value = "预约状态",example = "预约中")
    @ColumnWidth(value = 15)
    @ExcelProperty(value = "预约状态")
    private String orderStateStr;

    @ApiModelProperty(value = "故障说明",example = "车坏了")
    @ColumnWidth(value = 100)
    @ExcelProperty(value = "故障说明")
    private String failureDescription;

    @ApiModelProperty(value = "预约失败原因",example = "用户取消")
    @ColumnWidth(value = 100)
    @ExcelProperty(value = "预约失败原因")
    private String bookingFailureReason;

    @ApiModelProperty(value = "预约人",example = "张三")
    @ColumnWidth(value = 15)
    @ExcelProperty(value = "预约人")
    private String createrName;

    @ApiModelProperty(value = "预约人电话",example = "13000000000")
    @ColumnWidth(value = 20)
    @ExcelProperty(value = "预约人电话")
    private String bookingPhone;

    @ApiModelProperty(value = "预约时间",example = "2023-04-04 00:00:00")
    @ColumnWidth(value = 20)
    @ExcelProperty(value = "预约时间")
    private Date bookingTime;

    @ApiModelProperty(value = "预约类型",example = "维修及保养")
    @ColumnWidth(value = 15)
    @ExcelProperty(value = "预约类型")
    private String orderTypeStr;

    @ApiModelProperty(value = "是否到厂",example = "是")
    @ColumnWidth(value = 15)
    @ExcelProperty(value = "是否到厂")
    private String factoryInStr;

    @ApiModelProperty(value = "施工单号",example = "WB2023040400001")
    @ColumnWidth(value = 20)
    @ExcelProperty(value = "施工单号")
    private String constructionNo;

    @ApiModelProperty(value = "车辆品牌",example = "奥迪")
    @ColumnWidth(value = 20)
    @ExcelProperty(value = "车辆品牌")
    private String vehicleBrandName;

    @ApiModelProperty(value = "车系",example = "A6")
    @ColumnWidth(value = 20)
    @ExcelProperty(value = "车系")
    private String vehicleModelName;

    @ApiModelProperty(value = "维保费用承担方",example = "首汽租赁有限责任公司")
    @ColumnWidth(value = 30)
    @ExcelProperty(value = "维保费用承担方")
    private String undertakerCostCompanyName;

    @ApiModelProperty(value = "车牌号",example = "京A12345")
    @ColumnWidth(value = 20)
    @ExcelProperty(value = "车牌号")
    private String vehicleLicense;

    @ApiModelProperty(value = "接单/拒单人",example = "张三")
    @ColumnWidth(value = 20)
    @ExcelProperty(value = "接单/拒单人")
    private String receiverName;

    @ApiModelProperty(value = "接单/拒单时间",example = "2023-04-04 00:00:00")
    @ColumnWidth(value = 20)
    @ExcelProperty(value = "接单/拒单时间")
    private Date receiveTime;

    @ApiModelProperty(value = "企业名称",example = "xxx公司")
    @ColumnWidth(value = 30)
    @ExcelProperty(value = "企业名称")
    private String companyName;

    @ApiModelProperty(value = "创建时间",example = "2023-04-04 00:00:00")
    @ColumnWidth(value = 20)
    @ExcelProperty(value = "创建时间")
    private Date createTime;


}
