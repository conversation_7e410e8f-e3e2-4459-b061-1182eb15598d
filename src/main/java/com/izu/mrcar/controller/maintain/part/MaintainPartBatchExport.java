package com.izu.mrcar.controller.maintain.part;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import io.swagger.annotations.ApiModelProperty;
import izu.org.apache.poi.ss.usermodel.BorderStyle;
import izu.org.apache.poi.ss.usermodel.HorizontalAlignment;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@HeadStyle(
        fillForegroundColor = 22,
        fillBackgroundColor = 22
)
@ColumnWidth(20)
@ContentStyle(
        horizontalAlignment = HorizontalAlignment.CENTER,
        borderBottom = BorderStyle.THIN,
        borderLeft = BorderStyle.THIN,
        borderRight = BorderStyle.THIN,
        borderTop = BorderStyle.THIN,
        wrapped = true
)

@Data
public class MaintainPartBatchExport {

    @ExcelProperty(
            value = {"配件名称"},
            order = 1
    )
    private String partName;
    @ExcelProperty(
            value = {"配件编号"},
            order = 2
    )
    private String partNo;
    @ExcelProperty(
            value = {"入库数量"},
            order = 3
    )
    private Integer storageQuantity;
    @ExcelProperty(
            value = {"入库单价"},
            order = 4
    )
    private BigDecimal storageUnitPrice;
    @ExcelProperty(
            value = {"入库金额"},
            order = 5
    )
    private BigDecimal storageTotalPrice;
}
