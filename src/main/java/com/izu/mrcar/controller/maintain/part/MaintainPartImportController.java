package com.izu.mrcar.controller.maintain.part;

import com.alibaba.fastjson.JSON;
import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.maintain.part.input.client.MaintainPartCategoryInputDTO;
import com.izu.asset.dto.maintain.part.input.client.MaintainPartCreateDTO;
import com.izu.asset.dto.maintain.part.output.client.MaintainPartCategoryDTO;
import com.izu.excel.annotation.ImportExcel;
import com.izu.excel.upload.ExcelImportBase;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.controller.AbstractExcelUploadController;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.ImportErrorDTO;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@Api(tags = "维保管理")
public class MaintainPartImportController extends AbstractExcelUploadController<MaintainPartCreateDTO, AbstractExcelUploadController.ExcelUploadResult>{

    private static final Logger logger = LoggerFactory.getLogger(MaintainPartImportController.class);

    private  static final String COMMA = "；";

    @PostMapping("/download/partTemplate")
    @ApiOperation("导入配件模板下载")
    @RequestFunction(functionName = "导入配件模板下载")
    public void download(HttpServletRequest request, HttpServletResponse response) {
        String templatePath = "template/part_template.xlsx";
        String fileName = "配件导入模板.xlsx";
        Resource resource = new ClassPathResource(templatePath);
        try (InputStream is = resource.getInputStream(); XSSFWorkbook workbook = new XSSFWorkbook(is)) {
            ZipSecureFile.setMinInflateRatio(-1.0d);
            fileName = URLEncoder.encode(fileName, "UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename*=utf-8''" + fileName);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=gbk");
            response.setCharacterEncoding("gbk");
            XSSFSheet carModelSheet = workbook.getSheetAt(1);
            setCarModelList(carModelSheet);
            workbook.write(response.getOutputStream());
        }  catch (IOException e) {
            logger.error("配件导入模板下载失败", e);
        }
    }

    public void setCarModelList(XSSFSheet sheet) {
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        MaintainPartCategoryInputDTO param = new MaintainPartCategoryInputDTO();
        param.setPage(1);
        param.setPageSize(10000);
        param.setLoginCompanyCode(loginBaseInfo.obtainBelongCompanyCode());
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_CATEGORY_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, MaintainPartCategoryDTO.class);
        List<MaintainPartCategoryDTO> resList= new ArrayList<>();
        if(restResponse != null && restResponse.isSuccess()){
            PageDTO pageDTO =(PageDTO) restResponse.getData();
            resList = (List<MaintainPartCategoryDTO>)pageDTO.getResult() ;
        }
        if(CollectionUtils.isNotEmpty(resList)){
            for (int i = 0; i < resList.size(); i++) {
                MaintainPartCategoryDTO maintainPartCategoryDTO = resList.get(i);
                XSSFRow row = sheet.getRow(i + 1);
                if (row == null) {
                    row = sheet.createRow(i + 1);
                }
                XSSFCell cell0 = row.createCell(0);
                cell0.setCellValue(maintainPartCategoryDTO.getCategoryName());
            }
        }
    }

    @PostMapping(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_IMPORT_SAVE)
    @RequestFunction(functionName = "批量导入配件信息")
    @ApiOperation(value = "批量导入配件信息",notes = "作者：mapp")
    @ApiImplicitParam(name = "excelUrl", value = "文件路径", required = true)
    public RestResponse batchInsert(String excelUrl,
            HttpServletRequest request,
            HttpServletResponse response) {
        ExcelUploadResult eur = new ExcelUploadResult();
        return this.start(excelUrl, request, response, eur,16);
    }
    @Override
    protected List<Integer> getResolveSheetIndexes() {
        return Arrays.asList(1);
    }

    @Override
    protected String getColumnMappingConfigFile(HttpServletRequest request, HttpServletResponse response) {
        return null;
    }

    @Override
    protected List<MaintainPartCreateDTO> convertStringDataToObject(List<Map<Integer, String>> rowDatas, ExcelUploadResult excelUploadResult) {
        List<Map<Integer, String>> collect = rowDatas.stream().filter(row -> !checkFieldAllNull(row)).collect(Collectors.toList());
        return collect.stream().map( row ->{
            MaintainPartCreateDTO dto = new MaintainPartCreateDTO();
            dto.setPartName(row.get(0));
            dto.setCategoryName(row.get(1));
            dto.setPartUnit(row.get(2));
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    protected List<ExcelUploadRowError> checkExcelData(List<MaintainPartCreateDTO> rowDatas, HttpServletRequest request, HttpServletResponse response, ExcelUploadResult excelUploadResult) {
        List<ExcelUploadRowError> errorList = new ArrayList<>();
        int rowNum = 2;
        for (int i = 0; i < rowDatas.size(); i++) {
            MaintainPartCreateDTO importDto = rowDatas.get(i);
            StringBuffer error = new StringBuffer();
            if (StringUtils.isBlank(importDto.getPartName())) {
                error.append("配件名称不能为空").append(COMMA);
            }
            if (StringUtils.isBlank(importDto.getCategoryName())) {
                error.append("配件分类不能为空").append(COMMA);
            }
            String errorStr = error.toString();
            if (StringUtils.isNotBlank(errorStr)) {
                if (errorStr.lastIndexOf(COMMA) + 1 == errorStr.length()) {
                    errorStr = errorStr.substring(0, errorStr.length() - 1);
                }
                ExcelUploadRowError rowError = new ExcelUploadRowError(rowNum, errorStr);
                errorList.add(rowError);
            }
            rowNum += 1;
        }
        return errorList;
    }
    private boolean checkFieldAllNull(Object obj) {
        Field[] declaredFields = obj.getClass().getDeclaredFields();
        boolean flag = true;
        if(obj instanceof Map){
            Map<Integer,String> map = (Map) obj;
            for(Map.Entry entry : map.entrySet()){
                if (!ObjectUtils.isEmpty(entry.getValue())) {
                    flag = false;
                    break;
                }
            }
            return flag;
        }
        for (Field field : declaredFields) {
            field.setAccessible(true);
            Object o = null;
            try {
                o = field.get(obj);
            } catch (IllegalAccessException e) {
                logger.error("校验对象属性值异常");
            }
            if (!ObjectUtils.isEmpty(o)) {
                flag = false;
                break;
            }
        }
        return flag;
    }

    @Override
    protected boolean beforePersist(List<MaintainPartCreateDTO> rowDatas, HttpServletRequest request, HttpServletResponse response, ExcelUploadResult excelUploadResult) {
        return true;
    }

    @Override
    protected RestResponse executePersist(List<MaintainPartCreateDTO> rowDatas, HttpServletRequest request, HttpServletResponse response, ExcelUploadResult excelUploadResult) {
        ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
        Map<String, Object> paramMap = new HashMap<>(3);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, rowDatas);
        paramMap.put("operateId", clientLoginInfo.getBaseInfo().getStaffId());
        paramMap.put("operateName", clientLoginInfo.getBaseInfo().getStaffName());
        paramMap.put("loginCompanyCode", clientLoginInfo.getClientCompany().getCompanyCode());
        String restUrl = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_IMPORT_SAVE);
        //String restUrl = "http://localhost:8086/" + MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_IMPORT_SAVE;
        RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, ImportErrorDTO.class);
        logger.info("【配件导入batchInsert】响应response={}", JSON.toJSONString(restResponse));
        if (restResponse != null && restResponse.isSuccess()) {
            List<ExcelUploadRowError> errorList = new ArrayList<>();
            @SuppressWarnings({ "unchecked", "rawtypes" })
            List<ImportErrorDTO> list = (List) restResponse.getData();
            if(!CollectionUtils.isEmpty(list)){
                list.forEach(error ->{
                    ExcelUploadRowError excelUploadRowError = new ExcelUploadRowError(error.getRowNum(),error.getReason());
                    errorList.add(excelUploadRowError);
                });
                excelUploadResult.setCheckErrorMessages(errorList);
                excelUploadResult.setResolvedRows(rowDatas.size());
                excelUploadResult.setPersistSuccessRows(0);
                excelUploadResult.setPersistFailedRows(rowDatas.size());
                excelUploadResult.setErrorMessage("数据未通过校验，请确认后重新上传");
                return this.fail(null, request, response, excelUploadResult);
            }
            excelUploadResult.setPersistSuccessRows(rowDatas.size());
            return this.success(null, request, response, excelUploadResult);
        } else {
            if(restResponse != null){
                excelUploadResult.setErrorMessage(restResponse.getMsg());
            }
            return this.fail(null, request, response, excelUploadResult);
        }
    }

}
