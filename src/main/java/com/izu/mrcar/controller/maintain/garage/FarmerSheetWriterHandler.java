package com.izu.mrcar.controller.maintain.garage;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.izu.asset.consts.maintain.garage.ConstructionInvoiceRate;
import izu.org.apache.poi.ss.usermodel.DataValidation;
import izu.org.apache.poi.ss.usermodel.DataValidationConstraint;
import izu.org.apache.poi.ss.usermodel.DataValidationHelper;
import izu.org.apache.poi.ss.usermodel.Sheet;
import izu.org.apache.poi.ss.util.CellRangeAddressList;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023/5/8 20:32
 */
public class FarmerSheetWriterHandler implements SheetWriteHandler {

    //所属省份城市 1
    private String[] province;

    //发票类型 6
    private String[] invoiceType={"增值税专用发票","增值税普通发票"};

    //发表开具类型 7
    private String[] drawInvoiceType={"纸质发票","电子发票"};

    //支持维保 9
    private String[] supportMaintenance={"否","是"};
    //支持事故 10
    private String[] supportAccident={"否","是"};
    //准修车辆 11
    private String[] carType={"油车","电车","电车和油车"};
    //营业开始时间  12
    private String[] businessBegin={"06:00","07:00","08:00","09:00","10:00","11:00","12:00","13:00","14:00","15:00","16:00","17:00","18:00","19:00","20:00","21:00","22:00","23:00"};
    //营业结束时间  13
    private String[] businessEnd={"06:00","07:00","08:00","09:00","10:00","11:00","12:00","13:00","14:00","15:00","16:00","17:00","18:00","19:00","20:00","21:00","22:00","23:00"};
    //维修厂资质  19
    private String[] garageAptitude={"一类","二类","三类","4S店"};
    //服务范围  23
    private String[] serviceScope={"全部","仅本企业"};

    //基数
    private String[] workBaseTemplate;
    //系数
    private String[] workFactorTemplate;

    public FarmerSheetWriterHandler(String[] province, String[] workBaseTemplate, String[] workFactorTemplate) {
        this.province = province;
        this.workBaseTemplate = workBaseTemplate;
        this.workFactorTemplate = workFactorTemplate;
    }

    @Override
    public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        String[] province1={"云南-普洱-53-5308","云南-临沧-53-5309","云南-楚雄彝族自治州-53-5323","云南-红河哈尼族彝族自治州-53-5325","云南-文山壮族苗族自治州-53-5326","云南-西双版纳傣族自治州-53-5328","云南-大理白族自治州-53-5329","云南-德宏傣族景颇族自治州-53-5331","云南-怒江傈僳族自治州-53-5333"};
        String[] province2={"北京-北京-11-1101","天津-天津-12-1201","河北-石家庄-13-1301","河北-唐山-13-1302","河北-秦皇岛-13-1303","河北-邯郸-13-1304","河北-邢台-13-1305","河北-保定-13-1306","河北-张家口-13-1307","河北-承德-13-1308","河北-沧州-13-1309","河北-廊坊-13-1310","河北-衡水-13-1311","山西-太原-14-1401","山西-大同-14-1402","山西-阳泉-14-1403","山西-长治-14-1404","山西-晋城-14-1405","山西-朔州-14-1406","山西-晋中-14-1407","山西-运城-14-1408","山西-忻州-14-1409","山西-临汾-14-1410","山西-吕梁-14-1411","内蒙古-呼和浩特-15-1501","内蒙古-包头-15-1502","内蒙古-乌海-15-1503","内蒙古-赤峰-15-1504","内蒙古-通辽-15-1505","内蒙古-鄂尔多斯-15-1506","内蒙古-呼伦贝尔-15-1507","内蒙古-巴彦淖尔-15-1508","内蒙古-乌兰察布-15-1509","内蒙古-兴安盟-15-1522","内蒙古-锡林郭勒盟-15-1525","内蒙古-阿拉善盟-15-1529","辽宁-沈阳-21-2101","辽宁-大连-21-2102","辽宁-鞍山-21-2103","辽宁-抚顺-21-2104","辽宁-本溪-21-2105","辽宁-丹东-21-2106","辽宁-锦州-21-2107","辽宁-营口-21-2108","辽宁-阜新-21-2109","辽宁-辽阳-21-2110","辽宁-盘锦-21-2111","辽宁-铁岭-21-2112","辽宁-朝阳-21-2113","辽宁-葫芦岛-21-2114","吉林-长春-22-2201","吉林-吉林-22-2202","吉林-四平-22-2203","吉林-辽源-22-2204","吉林-通化-22-2205","吉林-白山-22-2206","吉林-松原-22-2207","吉林-白城-22-2208","吉林-延边朝鲜族自治州-22-2224","黑龙江-哈尔滨-23-2301","黑龙江-齐齐哈尔-23-2302","黑龙江-鸡西-23-2303","黑龙江-鹤岗-23-2304","黑龙江-双鸭山-23-2305","黑龙江-大庆-23-2306","黑龙江-伊春-23-2307","黑龙江-佳木斯-23-2308","黑龙江-七台河-23-2309","黑龙江-牡丹江-23-2310","黑龙江-黑河-23-2311","黑龙江-绥化-23-2312","黑龙江-大兴安岭地区-23-2327","上海-上海-31-3101","江苏-南京-32-3201","江苏-无锡-32-3202","江苏-徐州-32-3203","江苏-常州-32-3204","江苏-苏州-32-3205","江苏-南通-32-3206","江苏-连云港-32-3207","江苏-淮安-32-3208","江苏-盐城-32-3209","江苏-扬州-32-3210","江苏-镇江-32-3211","江苏-泰州-32-3212","江苏-宿迁-32-3213","浙江-杭州-33-3301","浙江-宁波-33-3302","浙江-温州-33-3303","浙江-嘉兴-33-3304","浙江-湖州-33-3305","浙江-绍兴-33-3306","浙江-金华-33-3307","浙江-衢州-33-3308","浙江-舟山-33-3309","浙江-台州-33-3310","浙江-丽水-33-3311","安徽-合肥-34-3401","安徽-芜湖-34-3402","安徽-蚌埠-34-3403","安徽-淮南-34-3404","安徽-马鞍山-34-3405","安徽-淮北-34-3406","安徽-铜陵-34-3407","安徽-安庆-34-3408","安徽-黄山-34-3410","安徽-滁州-34-3411","安徽-阜阳-34-3412","安徽-宿州-34-3413","安徽-六安-34-3415","安徽-亳州-34-3416","安徽-池州-34-3417","安徽-宣城-34-3418","福建-福州-35-3501","福建-厦门-35-3502","福建-莆田-35-3503","福建-三明-35-3504","福建-泉州-35-3505","福建-漳州-35-3506","福建-南平-35-3507","福建-龙岩-35-3508","福建-宁德-35-3509","江西-南昌-36-3601","江西-景德镇-36-3602","江西-萍乡-36-3603","江西-九江-36-3604","江西-新余-36-3605","江西-鹰潭-36-3606","江西-赣州-36-3607","江西-吉安-36-3608","江西-宜春-36-3609","江西-抚州-36-3610","江西-上饶-36-3611","山东-济南-37-3701","山东-青岛-37-3702","山东-淄博-37-3703","山东-枣庄-37-3704","山东-东营-37-3705","山东-烟台-37-3706","山东-潍坊-37-3707","山东-济宁-37-3708","山东-泰安-37-3709","山东-威海-37-3710","山东-日照-37-3711","山东-临沂-37-3713","山东-德州-37-3714","山东-聊城-37-3715","山东-滨州-37-3716","山东-菏泽-37-3717","河南-郑州-41-4101","河南-开封-41-4102","河南-洛阳-41-4103","河南-平顶山-41-4104","河南-安阳-41-4105","河南-鹤壁-41-4106","河南-新乡-41-4107","河南-焦作-41-4108","河南-濮阳-41-4109","河南-许昌-41-4110","河南-漯河-41-4111","河南-三门峡-41-4112","河南-南阳-41-4113","河南-商丘-41-4114","河南-信阳-41-4115","河南-周口-41-4116","河南-驻马店-41-4117","河南-济源-41-419001","湖北-武汉-42-4201","湖北-黄石-42-4202","湖北-十堰-42-4203","湖北-宜昌-42-4205","湖北-襄阳-42-4206","湖北-鄂州-42-4207","湖北-荆门-42-4208","湖北-孝感-42-4209","湖北-荆州-42-4210","湖北-黄冈-42-4211","湖北-咸宁-42-4212","湖北-随州-42-4213","湖北-恩施-42-4228","湖北-仙桃-42-429004","湖北-潜江-42-429005","湖北-天门-42-429006","湖北-神农架林区-42-429021","湖南-长沙-43-4301","湖南-株洲-43-4302","湖南-湘潭-43-4303","湖南-衡阳-43-4304","湖南-邵阳-43-4305","湖南-岳阳-43-4306","湖南-常德-43-4307","湖南-张家界-43-4308","湖南-益阳-43-4309","湖南-郴州-43-4310","湖南-永州-43-4311","湖南-怀化-43-4312","湖南-娄底-43-4313","湖南-湘西土家族苗族自治州-43-4331","广东-广州-44-4401","广东-韶关-44-4402","广东-深圳-44-4403","广东-汕头-44-4405","广东-江门-44-4407","广东-湛江-44-4408","广东-茂名-44-4409","广东-肇庆-44-4412","广东-惠州-44-4413","广东-梅州-44-4414","广东-汕尾-44-4415","广东-河源-44-4416","广东-阳江-44-4417","广东-清远-44-4418","广东-东莞-44-4419","广东-中山-44-4420","广东-潮州-44-4451","广东-揭阳-44-4452","广东-云浮-44-4453","广东-珠海-44-4404","广东-佛山-44-4406","广西-南宁-45-4501","广西-柳州-45-4502","广西-桂林-45-4503","广西-梧州-45-4504","广西-北海-45-4505","广西-防城港-45-4506","广西-钦州-45-4507","广西-贵港-45-4508","广西-玉林-45-4509","广西-百色-45-4510","广西-贺州-45-4511","广西-河池-45-4512","广西-来宾-45-4513","广西-崇左-45-4514","海南-海口-46-4601","海南-三亚-46-4602","海南-五指山-46-469001","海南-琼海-46-469002","海南-文昌-46-469005","海南-万宁-46-469006","海南-东方-46-469007","海南-定安县-46-469021","海南-屯昌县-46-469022","海南-澄迈县-46-469023","海南-临高县-46-469024","海南-白沙黎族自治县-46-469025","海南-昌江黎族自治县-46-469026","海南-乐东黎族自治县-46-469027","海南-陵水黎族自治县-46-469028","海南-保亭黎族苗族自治县-46-469029","海南-琼中黎族苗族自治县-46-469030","重庆-重庆-50-5001","四川-成都-51-5101","四川-自贡-51-5103","四川-攀枝花-51-5104","四川-泸州-51-5105","四川-德阳-51-5106","四川-绵阳-51-5107","四川-广元-51-5108","四川-遂宁-51-5109","四川-内江-51-5110","四川-乐山-51-5111","四川-南充-51-5113","四川-眉山-51-5114","四川-宜宾-51-5115","四川-广安-51-5116","四川-达州-51-5117","四川-雅安-51-5118","四川-巴中-51-5119","四川-资阳-51-5120","四川-阿坝藏族羌族自治州-51-5132","四川-甘孜藏族自治州-51-5133","四川-凉山彝族自治州-51-5134","贵州-贵阳-52-5201","贵州-六盘水-52-5202","贵州-遵义-52-5203","贵州-安顺-52-5204","贵州-毕节-52-5205","贵州-铜仁-52-5206","贵州-黔西南布依族苗族自治州-52-5223","贵州-黔东南苗族侗族自治州-52-5226","贵州-黔南布依族苗族自治州-52-5227","云南-昆明-53-5301","云南-曲靖-53-5303","云南-玉溪-53-5304","云南-保山-53-5305","云南-昭通-53-5306","云南-丽江-53-5307","云南-普洱-53-5308","云南-临沧-53-5309","云南-楚雄彝族自治州-53-5323","云南-红河哈尼族彝族自治州-53-5325","云南-文山壮族苗族自治州-53-5326","云南-西双版纳傣族自治州-53-5328","云南-大理白族自治州-53-5329","云南-德宏傣族景颇族自治州-53-5331","云南-怒江傈僳族自治州-53-5333","云南-迪庆-53-5334","西藏-拉萨-54-5401","西藏-日喀则-54-5402","西藏-昌都-54-5403","西藏-林芝-54-5404","西藏-阿里地区-54-5425","西藏-山南-54-5405","西藏-那曲-54-5406","陕西-西安-61-6101","陕西-铜川-61-6102","陕西-宝鸡-61-6103","陕西-咸阳-61-6104","陕西-渭南-61-6105","陕西-延安-61-6106","陕西-汉中-61-6107","陕西-榆林-61-6108","陕西-安康-61-6109","陕西-商洛-61-6110","甘肃-兰州-62-6201","甘肃-嘉峪关-62-6202","甘肃-白银-62-6204","甘肃-天水-62-6205","甘肃-武威-62-6206","甘肃-张掖-62-6207","甘肃-平凉-62-6208","甘肃-酒泉-62-6209","甘肃-庆阳-62-6210","甘肃-定西-62-6211","甘肃-陇南-62-6212","甘肃-临夏回族自治州-62-6229","甘肃-甘南藏族自治州-62-6230","甘肃-金昌-62-6203","青海-西宁-63-6301","青海-海东-63-6302","青海-海北藏族自治州-63-6322","青海-黄南藏族自治州-63-6323","青海-海南藏族自治州-63-6325","青海-果洛藏族自治州-63-6326","青海-玉树藏族自治州-63-6327","青海-海西蒙古族藏族自治州-63-6328","宁夏-银川-64-6401"};
        String[] province3={"北京-北京-11-1101","天津-天津-12-1201","河北-石家庄-13-1301","河北-唐山-13-1302","河北-秦皇岛-13-1303","河北-邯郸-13-1304","河北-邢台-13-1305","河北-保定-13-1306","河北-张家口-13-1307","河北-承德-13-1308","河北-沧州-13-1309","河北-廊坊-13-1310","河北-衡水-13-1311","山西-太原-14-1401","山西-大同-14-1402","山西-阳泉-14-1403","山西-长治-14-1404","山西-晋城-14-1405","山西-朔州-14-1406","山西-晋中-14-1407","山西-运城-14-1408","山西-忻州-14-1409","山西-临汾-14-1410","山西-吕梁-14-1411","内蒙古-呼和浩特-15-1501","内蒙古-包头-15-1502","内蒙古-乌海-15-1503","内蒙古-赤峰-15-1504","内蒙古-通辽-15-1505","内蒙古-鄂尔多斯-15-1506","内蒙古-呼伦贝尔-15-1507","内蒙古-巴彦淖尔-15-1508","内蒙古-乌兰察布-15-1509","内蒙古-兴安盟-15-1522","内蒙古-锡林郭勒盟-15-1525","内蒙古-阿拉善盟-15-1529","辽宁-沈阳-21-2101","辽宁-大连-21-2102","辽宁-鞍山-21-2103","辽宁-抚顺-21-2104","辽宁-本溪-21-2105","辽宁-丹东-21-2106","辽宁-锦州-21-2107","辽宁-营口-21-2108","辽宁-阜新-21-2109","辽宁-辽阳-21-2110","辽宁-盘锦-21-2111","辽宁-铁岭-21-2112","辽宁-朝阳-21-2113","辽宁-葫芦岛-21-2114","吉林-长春-22-2201","吉林-吉林-22-2202","吉林-四平-22-2203","吉林-辽源-22-2204","吉林-通化-22-2205","吉林-白山-22-2206","吉林-松原-22-2207","吉林-白城-22-2208","吉林-延边朝鲜族自治州-22-2224","黑龙江-哈尔滨-23-2301","黑龙江-齐齐哈尔-23-2302","黑龙江-鸡西-23-2303","黑龙江-鹤岗-23-2304","黑龙江-双鸭山-23-2305","黑龙江-大庆-23-2306","黑龙江-伊春-23-2307","黑龙江-佳木斯-23-2308","黑龙江-七台河-23-2309","黑龙江-牡丹江-23-2310","黑龙江-黑河-23-2311","黑龙江-绥化-23-2312","黑龙江-大兴安岭地区-23-2327","上海-上海-31-3101","江苏-南京-32-3201","江苏-无锡-32-3202","江苏-徐州-32-3203","江苏-常州-32-3204","江苏-苏州-32-3205","江苏-南通-32-3206","江苏-连云港-32-3207","江苏-淮安-32-3208","江苏-盐城-32-3209","江苏-扬州-32-3210","江苏-镇江-32-3211","江苏-泰州-32-3212","江苏-宿迁-32-3213","浙江-杭州-33-3301","浙江-宁波-33-3302","浙江-温州-33-3303","浙江-嘉兴-33-3304","浙江-湖州-33-3305","浙江-绍兴-33-3306","浙江-金华-33-3307","浙江-衢州-33-3308","浙江-舟山-33-3309","浙江-台州-33-3310","浙江-丽水-33-3311","安徽-合肥-34-3401","安徽-芜湖-34-3402","安徽-蚌埠-34-3403","安徽-淮南-34-3404","安徽-马鞍山-34-3405","安徽-淮北-34-3406","安徽-铜陵-34-3407","安徽-安庆-34-3408","安徽-黄山-34-3410","安徽-滁州-34-3411","安徽-阜阳-34-3412","安徽-宿州-34-3413","安徽-六安-34-3415","安徽-亳州-34-3416","安徽-池州-34-3417","安徽-宣城-34-3418","福建-福州-35-3501","福建-厦门-35-3502","福建-莆田-35-3503","福建-三明-35-3504","福建-泉州-35-3505","福建-漳州-35-3506","福建-南平-35-3507","福建-龙岩-35-3508","福建-宁德-35-3509","江西-南昌-36-3601","江西-景德镇-36-3602","江西-萍乡-36-3603","江西-九江-36-3604","江西-新余-36-3605","江西-鹰潭-36-3606","江西-赣州-36-3607","江西-吉安-36-3608","江西-宜春-36-3609","江西-抚州-36-3610","江西-上饶-36-3611","山东-济南-37-3701","山东-青岛-37-3702","山东-淄博-37-3703","山东-枣庄-37-3704","山东-东营-37-3705","山东-烟台-37-3706","山东-潍坊-37-3707","山东-济宁-37-3708","山东-泰安-37-3709","山东-威海-37-3710","山东-日照-37-3711","山东-临沂-37-3713","山东-德州-37-3714","山东-聊城-37-3715","山东-滨州-37-3716","山东-菏泽-37-3717","河南-郑州-41-4101","河南-开封-41-4102","河南-洛阳-41-4103","河南-平顶山-41-4104","河南-安阳-41-4105","河南-鹤壁-41-4106","河南-新乡-41-4107","河南-焦作-41-4108","河南-濮阳-41-4109","河南-许昌-41-4110","河南-漯河-41-4111","河南-三门峡-41-4112","河南-南阳-41-4113","河南-商丘-41-4114","河南-信阳-41-4115","河南-周口-41-4116","河南-驻马店-41-4117","河南-济源-41-419001","湖北-武汉-42-4201","湖北-黄石-42-4202","湖北-十堰-42-4203","湖北-宜昌-42-4205","湖北-襄阳-42-4206","湖北-鄂州-42-4207","湖北-荆门-42-4208","湖北-孝感-42-4209","湖北-荆州-42-4210","湖北-黄冈-42-4211","湖北-咸宁-42-4212","湖北-随州-42-4213","湖北-恩施-42-4228","湖北-仙桃-42-429004","湖北-潜江-42-429005","湖北-天门-42-429006","湖北-神农架林区-42-429021","湖南-长沙-43-4301","湖南-株洲-43-4302","湖南-湘潭-43-4303","湖南-衡阳-43-4304","湖南-邵阳-43-4305","湖南-岳阳-43-4306","湖南-常德-43-4307","湖南-张家界-43-4308","湖南-益阳-43-4309","湖南-郴州-43-4310","湖南-永州-43-4311","湖南-怀化-43-4312","湖南-娄底-43-4313","湖南-湘西土家族苗族自治州-43-4331","广东-广州-44-4401","广东-韶关-44-4402","广东-深圳-44-4403","广东-汕头-44-4405","广东-江门-44-4407","广东-湛江-44-4408","广东-茂名-44-4409","广东-肇庆-44-4412","广东-惠州-44-4413","广东-梅州-44-4414","广东-汕尾-44-4415","广东-河源-44-4416","广东-阳江-44-4417","广东-清远-44-4418","广东-东莞-44-4419","广东-中山-44-4420","广东-潮州-44-4451","广东-揭阳-44-4452","广东-云浮-44-4453","广东-珠海-44-4404","广东-佛山-44-4406","广西-南宁-45-4501","广西-柳州-45-4502","广西-桂林-45-4503","广西-梧州-45-4504","广西-北海-45-4505","广西-防城港-45-4506","广西-钦州-45-4507","广西-贵港-45-4508","广西-玉林-45-4509","广西-百色-45-4510","广西-贺州-45-4511","广西-河池-45-4512","广西-来宾-45-4513","广西-崇左-45-4514","海南-海口-46-4601","海南-三亚-46-4602","海南-五指山-46-469001","海南-琼海-46-469002","海南-文昌-46-469005","海南-万宁-46-469006","海南-东方-46-469007","海南-定安县-46-469021","海南-屯昌县-46-469022","海南-澄迈县-46-469023","海南-临高县-46-469024","海南-白沙黎族自治县-46-469025","海南-昌江黎族自治县-46-469026","海南-乐东黎族自治县-46-469027","海南-陵水黎族自治县-46-469028","海南-保亭黎族苗族自治县-46-469029","海南-琼中黎族苗族自治县-46-469030","重庆-重庆-50-5001","四川-成都-51-5101","四川-自贡-51-5103","四川-攀枝花-51-5104","四川-泸州-51-5105","四川-德阳-51-5106","四川-绵阳-51-5107","四川-广元-51-5108","四川-遂宁-51-5109","四川-内江-51-5110","四川-乐山-51-5111","四川-南充-51-5113","四川-眉山-51-5114","四川-宜宾-51-5115","四川-广安-51-5116","四川-达州-51-5117","四川-雅安-51-5118","四川-巴中-51-5119","四川-资阳-51-5120","四川-阿坝藏族羌族自治州-51-5132","四川-甘孜藏族自治州-51-5133","四川-凉山彝族自治州-51-5134","贵州-贵阳-52-5201","贵州-六盘水-52-5202","贵州-遵义-52-5203","贵州-安顺-52-5204","贵州-毕节-52-5205","贵州-铜仁-52-5206","贵州-黔西南布依族苗族自治州-52-5223","贵州-黔东南苗族侗族自治州-52-5226","贵州-黔南布依族苗族自治州-52-5227","云南-昆明-53-5301","云南-曲靖-53-5303","云南-玉溪-53-5304","云南-保山-53-5305","云南-昭通-53-5306","云南-丽江-53-5307","云南-普洱-53-5308","云南-临沧-53-5309","云南-楚雄彝族自治州-53-5323","云南-红河哈尼族彝族自治州-53-5325","云南-文山壮族苗族自治州-53-5326","云南-西双版纳傣族自治州-53-5328","云南-大理白族自治州-53-5329","云南-德宏傣族景颇族自治州-53-5331","云南-怒江傈僳族自治州-53-5333","云南-迪庆-53-5334","西藏-拉萨-54-5401","西藏-日喀则-54-5402","西藏-昌都-54-5403","西藏-林芝-54-5404","西藏-阿里地区-54-5425","西藏-山南-54-5405","西藏-那曲-54-5406","陕西-西安-61-6101","陕西-铜川-61-6102","陕西-宝鸡-61-6103","陕西-咸阳-61-6104","陕西-渭南-61-6105","陕西-延安-61-6106","陕西-汉中-61-6107","陕西-榆林-61-6108","陕西-安康-61-6109","陕西-商洛-61-6110","甘肃-兰州-62-6201","甘肃-嘉峪关-62-6202","甘肃-白银-62-6204","甘肃-天水-62-6205","甘肃-武威-62-6206","甘肃-张掖-62-6207","甘肃-平凉-62-6208","甘肃-酒泉-62-6209","甘肃-庆阳-62-6210","甘肃-定西-62-6211","甘肃-陇南-62-6212","甘肃-临夏回族自治州-62-6229","甘肃-甘南藏族自治州-62-6230","甘肃-金昌-62-6203","青海-西宁-63-6301","青海-海东-63-6302","青海-海北藏族自治州-63-6322","青海-黄南藏族自治州-63-6323","青海-海南藏族自治州-63-6325","青海-果洛藏族自治州-63-6326","青海-玉树藏族自治州-63-6327","青海-海西蒙古族藏族自治州-63-6328","宁夏-银川-64-6401","宁夏-石嘴山-64-6402","宁夏-吴忠-64-6403","宁夏-固原-64-6404","宁夏-中卫-64-6405","新疆-乌鲁木齐-65-6501","新疆-克拉玛依-65-6502","新疆-吐鲁番-65-6504","新疆-昌吉回族自治州-65-6523","新疆-博尔塔拉-65-6527","新疆-巴音郭楞-65-6528","新疆-阿克苏-65-6529","新疆-克孜勒苏柯尔克孜自治州-65-6530","新疆-喀什-65-6531","新疆-和田-65-6532","新疆-伊犁哈萨克自治州-65-6540","新疆-塔城-65-6542","新疆-阿勒泰-65-6543","新疆-石河子-65-659001","新疆-阿拉尔-65-659002","新疆-图木舒克-65-659003","新疆-五家渠-65-659004","新疆-哈密-65-6505","香港-香港-81-81","澳门-澳门-82-82"};
        String[] province4={"北京-北京-11-1101","天津-天津-12-1201","河北-石家庄-13-1301","河北-唐山-13-1302","河北-秦皇岛-13-1303","河北-邯郸-13-1304","河北-邢台-13-1305","河北-保定-13-1306","河北-张家口-13-1307","河北-承德-13-1308","河北-沧州-13-1309","河北-廊坊-13-1310","河北-衡水-13-1311"};
        //税率 8
        String[] taxRate=Arrays.asList(ConstructionInvoiceRate.values()).stream().map(ConstructionInvoiceRate::getName).toArray(String[]::new);
        //获取工作簿
        Sheet sheet = writeSheetHolder.getSheet();
        ///开始设置下拉框
        DataValidationHelper helper = sheet.getDataValidationHelper();
        //定义一个map key是需要添加下拉框的列的index value是下拉框数据
        Map<Integer, String[]> mapProvince = new HashMap<>();
        mapProvince.put(1,province4);
        //设置下拉框
        for (Map.Entry<Integer, String[]> entry : mapProvince.entrySet()) {
            /*起始行、终止行、起始列、终止列  起始行为1即表示表头不设置**/
            CellRangeAddressList addressList = new CellRangeAddressList(1, 65535, entry.getKey(), entry.getKey());

            /*设置下拉框数据**/
            DataValidationConstraint constraint = helper.createExplicitListConstraint(entry.getValue());
            DataValidation dataValidation = helper.createValidation(constraint, addressList);

            // 阻止输入非下拉选项的值
            dataValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
            dataValidation.setShowErrorBox(true);
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.createErrorBox("提示", "请选择下拉选项中的内容");
            sheet.addValidationData(dataValidation);
        }
        Map<Integer, String[]> mapInvoiceType = new HashMap<>();
        mapInvoiceType.put(5,invoiceType);
        //设置下拉框
        for (Map.Entry<Integer, String[]> entry : mapInvoiceType.entrySet()) {
            /*起始行、终止行、起始列、终止列  起始行为1即表示表头不设置**/
            CellRangeAddressList addressList = new CellRangeAddressList(1, 65535, entry.getKey(), entry.getKey());

            /*设置下拉框数据**/
            DataValidationConstraint constraint = helper.createExplicitListConstraint(entry.getValue());
            DataValidation dataValidation = helper.createValidation(constraint, addressList);

            // 阻止输入非下拉选项的值
            dataValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
            dataValidation.setShowErrorBox(true);
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.createErrorBox("提示", "请选择下拉选项中的内容");
            sheet.addValidationData(dataValidation);
        }
        Map<Integer, String[]> mapDrawInvoiceType = new HashMap<>();
        mapDrawInvoiceType.put(6,drawInvoiceType);
        //设置下拉框
        for (Map.Entry<Integer, String[]> entry : mapDrawInvoiceType.entrySet()) {
            /*起始行、终止行、起始列、终止列  起始行为1即表示表头不设置**/
            CellRangeAddressList addressList = new CellRangeAddressList(1, 65535, entry.getKey(), entry.getKey());

            /*设置下拉框数据**/
            DataValidationConstraint constraint = helper.createExplicitListConstraint(entry.getValue());
            DataValidation dataValidation = helper.createValidation(constraint, addressList);

            // 阻止输入非下拉选项的值
            dataValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
            dataValidation.setShowErrorBox(true);
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.createErrorBox("提示", "请选择下拉选项中的内容");
            sheet.addValidationData(dataValidation);
        }
        Map<Integer, String[]> mapTaxRate = new HashMap<>();
        mapTaxRate.put(7,taxRate);
        //设置下拉框
        for (Map.Entry<Integer, String[]> entry : mapTaxRate.entrySet()) {
            /*起始行、终止行、起始列、终止列  起始行为1即表示表头不设置**/
            CellRangeAddressList addressList = new CellRangeAddressList(1, 65535, entry.getKey(), entry.getKey());

            /*设置下拉框数据**/
            DataValidationConstraint constraint = helper.createExplicitListConstraint(entry.getValue());
            DataValidation dataValidation = helper.createValidation(constraint, addressList);

            // 阻止输入非下拉选项的值
            dataValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
            dataValidation.setShowErrorBox(true);
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.createErrorBox("提示", "请选择下拉选项中的内容");
            sheet.addValidationData(dataValidation);
        }
        Map<Integer, String[]> mapSupportMaintenance = new HashMap<>();
        mapSupportMaintenance.put(8,supportMaintenance);
        //设置下拉框
        for (Map.Entry<Integer, String[]> entry : mapSupportMaintenance.entrySet()) {
            /*起始行、终止行、起始列、终止列  起始行为1即表示表头不设置**/
            CellRangeAddressList addressList = new CellRangeAddressList(1, 65535, entry.getKey(), entry.getKey());

            /*设置下拉框数据**/
            DataValidationConstraint constraint = helper.createExplicitListConstraint(entry.getValue());
            DataValidation dataValidation = helper.createValidation(constraint, addressList);

            // 阻止输入非下拉选项的值
            dataValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
            dataValidation.setShowErrorBox(true);
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.createErrorBox("提示", "请选择下拉选项中的内容");
            sheet.addValidationData(dataValidation);
        }
        Map<Integer, String[]> mapSupportAccident = new HashMap<>();
        mapSupportAccident.put(9,supportAccident);
        //设置下拉框
        for (Map.Entry<Integer, String[]> entry : mapSupportAccident.entrySet()) {
            /*起始行、终止行、起始列、终止列  起始行为1即表示表头不设置**/
            CellRangeAddressList addressList = new CellRangeAddressList(1, 65535, entry.getKey(), entry.getKey());

            /*设置下拉框数据**/
            DataValidationConstraint constraint = helper.createExplicitListConstraint(entry.getValue());
            DataValidation dataValidation = helper.createValidation(constraint, addressList);

            // 阻止输入非下拉选项的值
            dataValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
            dataValidation.setShowErrorBox(true);
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.createErrorBox("提示", "请选择下拉选项中的内容");
            sheet.addValidationData(dataValidation);
        }

        Map<Integer, String[]> mapCarType = new HashMap<>();
        mapCarType.put(10,carType);
        //设置下拉框
        for (Map.Entry<Integer, String[]> entry : mapCarType.entrySet()) {
            /*起始行、终止行、起始列、终止列  起始行为1即表示表头不设置**/
            CellRangeAddressList addressList = new CellRangeAddressList(1, 65535, entry.getKey(), entry.getKey());

            /*设置下拉框数据**/
            DataValidationConstraint constraint = helper.createExplicitListConstraint(entry.getValue());
            DataValidation dataValidation = helper.createValidation(constraint, addressList);

            // 阻止输入非下拉选项的值
            dataValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
            dataValidation.setShowErrorBox(true);
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.createErrorBox("提示", "请选择下拉选项中的内容");
            sheet.addValidationData(dataValidation);
        }
        Map<Integer, String[]> mapBusinessBegin = new HashMap<>();
        mapBusinessBegin.put(11,businessBegin);
        //设置下拉框
        for (Map.Entry<Integer, String[]> entry : mapBusinessBegin.entrySet()) {
            /*起始行、终止行、起始列、终止列  起始行为1即表示表头不设置**/
            CellRangeAddressList addressList = new CellRangeAddressList(1, 65535, entry.getKey(), entry.getKey());

            /*设置下拉框数据**/
            DataValidationConstraint constraint = helper.createExplicitListConstraint(entry.getValue());
            DataValidation dataValidation = helper.createValidation(constraint, addressList);

            // 阻止输入非下拉选项的值
            dataValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
            dataValidation.setShowErrorBox(true);
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.createErrorBox("提示", "请选择下拉选项中的内容");
            sheet.addValidationData(dataValidation);
        }
        Map<Integer, String[]> mapBusinessEnd = new HashMap<>();
        mapBusinessEnd.put(12,businessEnd);
        //设置下拉框
        for (Map.Entry<Integer, String[]> entry : mapBusinessEnd.entrySet()) {
            /*起始行、终止行、起始列、终止列  起始行为1即表示表头不设置**/
            CellRangeAddressList addressList = new CellRangeAddressList(1, 65535, entry.getKey(), entry.getKey());

            /*设置下拉框数据**/
            DataValidationConstraint constraint = helper.createExplicitListConstraint(entry.getValue());
            DataValidation dataValidation = helper.createValidation(constraint, addressList);

            // 阻止输入非下拉选项的值
            dataValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
            dataValidation.setShowErrorBox(true);
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.createErrorBox("提示", "请选择下拉选项中的内容");
            sheet.addValidationData(dataValidation);
        }


        Map<Integer, String[]> mapDropDown = new HashMap<>();
        mapDropDown.put(14,workBaseTemplate);
        //设置下拉框
        for (Map.Entry<Integer, String[]> entry : mapDropDown.entrySet()) {
            /*起始行、终止行、起始列、终止列  起始行为1即表示表头不设置**/
            CellRangeAddressList addressList = new CellRangeAddressList(1, 65535, entry.getKey(), entry.getKey());

            /*设置下拉框数据**/
            DataValidationConstraint constraint = helper.createExplicitListConstraint(entry.getValue());
            DataValidation dataValidation = helper.createValidation(constraint, addressList);

            // 阻止输入非下拉选项的值
            dataValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
            dataValidation.setShowErrorBox(true);
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.createErrorBox("提示", "请选择下拉选项中的内容");
            sheet.addValidationData(dataValidation);
        }
        Map<Integer, String[]> mapDropDown2 = new HashMap<>();
        mapDropDown2.put(15,workFactorTemplate);
        //设置下拉框
        for (Map.Entry<Integer, String[]> entry : mapDropDown2.entrySet()) {
            /*起始行、终止行、起始列、终止列  起始行为1即表示表头不设置**/
            CellRangeAddressList addressList = new CellRangeAddressList(1, 65535, entry.getKey(), entry.getKey());

            /*设置下拉框数据**/
            DataValidationConstraint constraint = helper.createExplicitListConstraint(entry.getValue());
            DataValidation dataValidation = helper.createValidation(constraint, addressList);

            // 阻止输入非下拉选项的值
            dataValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
            dataValidation.setShowErrorBox(true);
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.createErrorBox("提示", "请选择下拉选项中的内容");
            sheet.addValidationData(dataValidation);
        }
        Map<Integer, String[]> mapGarageAptitude = new HashMap<>();
        mapGarageAptitude.put(18,garageAptitude);
        //设置下拉框
        for (Map.Entry<Integer, String[]> entry : mapGarageAptitude.entrySet()) {
            /*起始行、终止行、起始列、终止列  起始行为1即表示表头不设置**/
            CellRangeAddressList addressList = new CellRangeAddressList(1, 65535, entry.getKey(), entry.getKey());

            /*设置下拉框数据**/
            DataValidationConstraint constraint = helper.createExplicitListConstraint(entry.getValue());
            DataValidation dataValidation = helper.createValidation(constraint, addressList);

            // 阻止输入非下拉选项的值
            dataValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
            dataValidation.setShowErrorBox(true);
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.createErrorBox("提示", "请选择下拉选项中的内容");
            sheet.addValidationData(dataValidation);
        }
        Map<Integer, String[]> mapServiceScope = new HashMap<>();
        mapServiceScope.put(22,serviceScope);
        //设置下拉框
        for (Map.Entry<Integer, String[]> entry : mapServiceScope.entrySet()) {
            /*起始行、终止行、起始列、终止列  起始行为1即表示表头不设置**/
            CellRangeAddressList addressList = new CellRangeAddressList(1, 65535, entry.getKey(), entry.getKey());

            /*设置下拉框数据**/
            DataValidationConstraint constraint = helper.createExplicitListConstraint(entry.getValue());
            DataValidation dataValidation = helper.createValidation(constraint, addressList);

            // 阻止输入非下拉选项的值
            dataValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
            dataValidation.setShowErrorBox(true);
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.createErrorBox("提示", "请选择下拉选项中的内容");
            sheet.addValidationData(dataValidation);
        }
    }


}
