package com.izu.mrcar.controller.maintain.part;

import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.maintain.part.input.client.MaintainPartInventoryInputDTO;
import com.izu.asset.dto.maintain.part.input.garage.MaintainGaragePartInventoryInputDTO;
import com.izu.asset.dto.maintain.part.output.client.MaintainAllPartInventoryListDTO;
import com.izu.asset.dto.maintain.part.output.client.MaintainPartInventoryListDTO;
import com.izu.asset.dto.maintain.part.output.garage.MaintainGarageAllPartInventoryListDTO;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.maintain.GarageMaintainDataPermUtil;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;

@RestController
@Api(tags = "维保管理")
public class MaintainPartInventoryController {

    public static final String FILE_PATH = "/data/logs/mrcar/tmp";

    @PostMapping(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_INVENTORY_LIST)
    @RequestFunction(functionName = "配件库存列表-客户端")
    @ApiOperation(value = "配件库存列表（客户端）",notes = "作者：mapp")
    public RestResponse<PageDTO<MaintainAllPartInventoryListDTO>> getClientList(@RequestBody MaintainPartInventoryInputDTO param){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_INVENTORY_LIST);
        param.setLoginCompanyCode(loginBaseInfo.obtainBelongCompanyCode());
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, MaintainAllPartInventoryListDTO.class);
    }

    @PostMapping(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_INVENTORY_ONE_LIST)
    @RequestFunction(functionName = "单个配件库存批次列表-客户端")
    @ApiOperation(value = "单个配件库存批次列表（客户端）",notes = "作者：mapp")
    public RestResponse<PageDTO<MaintainPartInventoryListDTO>> getClientOnePartList(@RequestBody MaintainPartInventoryInputDTO param){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_INVENTORY_ONE_LIST);
        param.setLoginCompanyCode(loginBaseInfo.obtainBelongCompanyCode());
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, MaintainPartInventoryListDTO.class);
    }

    @PostMapping(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_INVENTORY_LIST_EXPORT)
    @RequestFunction(functionName = "配件库存列表导出(客户端)")
    @ApiOperation(value = "配件库存列表导出（客户端）")
    @ExportExcelWeb(fileName = ExportExcelConstants.FILE_NAME_MAINTAIN_PART_INVENTORY,filePath = ExportExcelConstants.FILE_PATH,
            sheet = ExportExcelConstants.SHEET_NAME_MAINTAIN_PART_INVENTORY, c =PartInventoryExport.class)
    public PageDTO exportPartInfo(@RequestBody MaintainPartInventoryInputDTO param, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        String userName = loginBaseInfo.obtainBaseInfo().getStaffName();
        param.setPage(izuEasyExcelSession.getPageNo());
        izuEasyExcelSession.setUserName(userName);
        param.setPageSize(10000);
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_INVENTORY_LIST);
        param.setLoginCompanyCode(loginBaseInfo.obtainBelongCompanyCode());
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, PartInventoryExport.class);
        if(restResponse!=null && restResponse.isSuccess()){
            PageDTO<PartInventoryExport> pageDTO = (PageDTO) restResponse.getData();
            if(pageDTO.getTotal() > 10000){
                throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
            }
            return pageDTO;
        }
        return null;
    }

    @PostMapping(MrCarAssetRestCenter.GARAGE_MAINTAIN_PART_INVENTORY_LIST)
    @RequestFunction(functionName = "配件库存列表-维修厂端")
    @ApiOperation(value = "配件库存列表（维修厂端）",notes = "作者：mapp")
    public RestResponse<PageDTO<MaintainGarageAllPartInventoryListDTO>> getGarageList(@RequestBody MaintainGaragePartInventoryInputDTO param){
        param.setCompanyCodes(GarageMaintainDataPermUtil.getDatePermGarageScope(param.getCompanyCodes()));
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.GARAGE_MAINTAIN_PART_INVENTORY_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, MaintainGarageAllPartInventoryListDTO.class);
    }

    @PostMapping(MrCarAssetRestCenter.GARAGE_MAINTAIN_PART_INVENTORY_ONE_LIST)
    @RequestFunction(functionName = "单个配件库存批次列表-维修厂端")
    @ApiOperation(value = "单个配件库存批次列表（维修厂端）",notes = "作者：mapp")
    public RestResponse<PageDTO<MaintainPartInventoryListDTO>> getGarageOnePartList(@RequestBody MaintainPartInventoryInputDTO param){
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.GARAGE_MAINTAIN_PART_INVENTORY_ONE_LIST);
        param.setCompanyCodes(GarageMaintainDataPermUtil.getDatePermGarageScope(param.getCompanyCodes()));
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, MaintainPartInventoryListDTO.class);
    }

    @PostMapping(MrCarAssetRestCenter.GARAGE_MAINTAIN_PART_INVENTORY_LIST_EXPORT)
    @RequestFunction(functionName = "配件库存列表导出(维修厂端)")
    @ApiOperation(value = "配件库存列表导出（客户端）")
    @ExportExcelWeb(fileName = ExportExcelConstants.FILE_NAME_MAINTAIN_PART_INVENTORY,filePath = ExportExcelConstants.FILE_PATH,
            sheet = ExportExcelConstants.SHEET_NAME_MAINTAIN_PART_INVENTORY, c =PartInventoryExport.class)
    public PageDTO exportGaragePartInfo(@RequestBody MaintainGaragePartInventoryInputDTO param, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        String userName = loginBaseInfo.obtainBaseInfo().getStaffName();
        param.setPage(izuEasyExcelSession.getPageNo());
        izuEasyExcelSession.setUserName(userName);
        param.setPageSize(10000);
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.GARAGE_MAINTAIN_PART_INVENTORY_LIST);
        param.setCompanyCodes(GarageMaintainDataPermUtil.getDatePermGarageScope(param.getCompanyCodes()));
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, PartInventoryExport.class);
        if(restResponse!=null && restResponse.isSuccess()){
            PageDTO<PartInventoryExport> pageDTO = (PageDTO) restResponse.getData();
            if(pageDTO.getTotal() > 10000){
                throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
            }
            return pageDTO;
        }
        return null;
    }
}
