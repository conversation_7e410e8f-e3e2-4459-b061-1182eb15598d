package com.izu.mrcar.controller.maintain.workhours;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.izu.asset.consts.maintain.workhours.MaintainGroupEnum;
import izu.org.apache.poi.ss.usermodel.DataValidation;
import izu.org.apache.poi.ss.usermodel.DataValidationConstraint;
import izu.org.apache.poi.ss.usermodel.DataValidationHelper;
import izu.org.apache.poi.ss.usermodel.Sheet;
import izu.org.apache.poi.ss.util.CellRangeAddressList;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 工时下拉列表
 * @date 2023/5/9 21:40
 */
public class WorkBaseSheetWriterHandler implements SheetWriteHandler {
    @Override
    public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {

    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {

        String[] group= Arrays.asList(MaintainGroupEnum.values()).stream().map(MaintainGroupEnum::getMessage).toArray(String[]::new);
        String[] oftenUse={"否","是"};
        String[] status={"启用","禁用"};
        //获取工作簿
        Sheet sheet = writeSheetHolder.getSheet();
        ///开始设置下拉框
        DataValidationHelper helper = sheet.getDataValidationHelper();
        //定义一个map key是需要添加下拉框的列的index value是下拉框数据
        Map<Integer, String[]> mapGroup = new HashMap<>();
        mapGroup.put(1,group);
        //设置下拉框
        for (Map.Entry<Integer, String[]> entry : mapGroup.entrySet()) {
            /*起始行、终止行、起始列、终止列  起始行为1即表示表头不设置**/
            CellRangeAddressList addressList = new CellRangeAddressList(1, 65535, entry.getKey(), entry.getKey());

            /*设置下拉框数据**/
            DataValidationConstraint constraint = helper.createExplicitListConstraint(entry.getValue());
            DataValidation dataValidation = helper.createValidation(constraint, addressList);

            // 阻止输入非下拉选项的值
            dataValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
            dataValidation.setShowErrorBox(true);
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.createErrorBox("提示", "请选择下拉选项中的内容");
            sheet.addValidationData(dataValidation);
        }

        Map<Integer, String[]> mapOftenUse = new HashMap<>();
        mapOftenUse.put(3,oftenUse);
        //设置下拉框
        for (Map.Entry<Integer, String[]> entry : mapOftenUse.entrySet()) {
            /*起始行、终止行、起始列、终止列  起始行为1即表示表头不设置**/
            CellRangeAddressList addressList = new CellRangeAddressList(1, 65535, entry.getKey(), entry.getKey());

            /*设置下拉框数据**/
            DataValidationConstraint constraint = helper.createExplicitListConstraint(entry.getValue());
            DataValidation dataValidation = helper.createValidation(constraint, addressList);

            // 阻止输入非下拉选项的值
            dataValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
            dataValidation.setShowErrorBox(true);
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.createErrorBox("提示", "请选择下拉选项中的内容");
            sheet.addValidationData(dataValidation);
        }

        Map<Integer, String[]> mapSelect = new HashMap<>();
        mapSelect.put(4,oftenUse);
        //设置下拉框
        for (Map.Entry<Integer, String[]> entry : mapSelect.entrySet()) {
            /*起始行、终止行、起始列、终止列  起始行为1即表示表头不设置**/
            CellRangeAddressList addressList = new CellRangeAddressList(1, 65535, entry.getKey(), entry.getKey());

            /*设置下拉框数据**/
            DataValidationConstraint constraint = helper.createExplicitListConstraint(entry.getValue());
            DataValidation dataValidation = helper.createValidation(constraint, addressList);

            // 阻止输入非下拉选项的值
            dataValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
            dataValidation.setShowErrorBox(true);
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.createErrorBox("提示", "请选择下拉选项中的内容");
            sheet.addValidationData(dataValidation);
        }

        Map<Integer, String[]> mapStatus = new HashMap<>();
        mapStatus.put(7,status);
        //设置下拉框
        for (Map.Entry<Integer, String[]> entry : mapStatus.entrySet()) {
            /*起始行、终止行、起始列、终止列  起始行为1即表示表头不设置**/
            CellRangeAddressList addressList = new CellRangeAddressList(1, 65535, entry.getKey(), entry.getKey());

            /*设置下拉框数据**/
            DataValidationConstraint constraint = helper.createExplicitListConstraint(entry.getValue());
            DataValidation dataValidation = helper.createValidation(constraint, addressList);

            // 阻止输入非下拉选项的值
            dataValidation.setErrorStyle(DataValidation.ErrorStyle.STOP);
            dataValidation.setShowErrorBox(true);
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.createErrorBox("提示", "请选择下拉选项中的内容");
            sheet.addValidationData(dataValidation);
        }
    }
}
