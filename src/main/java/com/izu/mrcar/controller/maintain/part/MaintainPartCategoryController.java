package com.izu.mrcar.controller.maintain.part;

import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.maintain.part.input.client.MaintainPartCategoryCreateDTO;
import com.izu.asset.dto.maintain.part.input.client.MaintainPartCategoryInputDTO;
import com.izu.asset.dto.maintain.part.output.client.MaintainPartCategoryDTO;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@Api(tags = "维保管理")
public class MaintainPartCategoryController {

    @PostMapping(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_CATEGORY_LIST)
    @RequestFunction(functionName = "配件分类-客户端")
    @ApiOperation(value = "配件分类（客户端）",notes = "作者：mapp")
    public RestResponse<PageDTO<MaintainPartCategoryDTO>> getList(@RequestBody MaintainPartCategoryInputDTO param){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_CATEGORY_LIST);
        param.setLoginCompanyCode(loginBaseInfo.obtainBelongCompanyCode());
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, MaintainPartCategoryDTO.class);
    }

    @PostMapping(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_CATEGORY_SAVE)
    @RequestFunction(functionName = "新增、编辑配件分类-客户端")
    @ApiOperation(value = "新增、编辑配件分类（客户端）",notes = "作者：mapp")
    public RestResponse save(@RequestBody MaintainPartCategoryCreateDTO param){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_CATEGORY_SAVE);
        param.setLoginCompanyCode(loginBaseInfo.obtainBelongCompanyCode());
        param.setLoginUserId(loginBaseInfo.obtainBaseInfo().getStaffId());
        param.setLoginUserName(loginBaseInfo.obtainBaseInfo().getStaffName());
        param.setLoginUserPhone(loginBaseInfo.obtainBaseInfo().getMobile());
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, Boolean.class);
    }

    @PostMapping(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_CATEGORY_DELETE)
    @RequestFunction(functionName = "删除配件分类-客户端")
    @ApiOperation(value = "删除配件分类（客户端）",notes = "作者：mapp")
    @ApiImplicitParam(name = "categoryId", value = "分类id", required = true)
    public RestResponse delete(Integer categoryId){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_CATEGORY_DELETE);
        Map<String,Object> param = new HashMap<>();
        param.put("categoryId",categoryId);
        param.put("companyCode",loginBaseInfo.obtainBelongCompanyCode());
        param.put("operateId",loginBaseInfo.obtainBaseInfo().getStaffId());
        param.put("operateName",loginBaseInfo.obtainBaseInfo().getStaffName());
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, url, param, null, Boolean.class);
    }
}
