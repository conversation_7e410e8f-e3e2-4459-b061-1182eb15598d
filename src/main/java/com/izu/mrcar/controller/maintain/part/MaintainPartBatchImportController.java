package com.izu.mrcar.controller.maintain.part;

import com.alibaba.fastjson.JSON;
import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.maintain.part.input.client.ImportErrorListDTO;
import com.izu.asset.dto.maintain.part.input.client.MaintainPartBatchCreateDTO;
import com.izu.asset.dto.maintain.part.input.client.MaintainPartBatchImportRespDTO;
import com.izu.asset.dto.maintain.part.input.client.MaintainPartInventoryDTO;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.SequenceUtil;
import com.izu.framework.web.util.excel.ExcelTool;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.cache.SessionRedisSentinelCache;
import com.izu.mrcar.controller.AbstractExcelUploadController;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.ImportErrorDTO;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@Api(tags = "维保管理")
public class MaintainPartBatchImportController extends AbstractExcelUploadController<MaintainPartInventoryDTO, AbstractExcelUploadController.ExcelUploadResult>{

    private static final Logger logger = LoggerFactory.getLogger(MaintainPartBatchImportController.class);

    private  static final String COMMA = "；";
    private static final String  PARTLOGTAG = "【配件库存导入】- ";
    private Class<MaintainPartInventoryDTO> partRowClazz;//每一行所对应的数据对象类型
    @Autowired
    private SessionRedisSentinelCache sessionRedisSentinelCache;
    private static final int       EXCEL_CHECK_ERRORS_CACHE_TIMEOUT = 6*60*60;//缓存时效

    @PostMapping("/batchInventory/uploadFile")
    @ApiOperation("批次导入库存文件返回数据")
    @RequestFunction(functionName = "批次导入库存文件返回数据")
    @ApiImplicitParam(name = "excelUrl", value = "文件路径", required = true)
    public RestResponse uploadFile(String excelUrl,HttpServletRequest request,
                                   HttpServletResponse response){
        ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
        AbstractExcelUploadController.ExcelUploadResult excelUploadResult = new AbstractExcelUploadController.ExcelUploadResult();
        //1 参数校验
        if(StringUtils.isBlank(excelUrl)) {
            excelUploadResult.setErrorMessage("路径为空,上传失败。");
            return fail(excelUploadResult,null);
        }
        //2 读取数据流
        final InputStream in = getPartExcelInputStream(excelUrl);
        if( in==null ) {
            excelUploadResult.setErrorMessage("解析文件失败。");
            return fail(excelUploadResult,null);
        }
        //3 解析成文本数据
        final String excelFileName  = excelUrl.substring(excelUrl.lastIndexOf("/")+1);//文件名
        final String excelType         = excelFileName.substring( excelFileName.lastIndexOf(".")+1 ).toLowerCase();//扩展名
        List<Map<Integer,String>> rowStringDatas = this.getExcelData(in, excelType, this.getResolveSheetIndexes());
        if( rowStringDatas==null || rowStringDatas.size()==0 ) {
            excelUploadResult.setErrorMessage("文件内容为空,上传失败。");
            return  fail(excelUploadResult,null);
        }else if(rowStringDatas.size() > 200){
            excelUploadResult.setErrorMessage("文件行数超过200条,上传失败。");
            return  fail(excelUploadResult,null);
        }
        //4 转换成对象数据
        List<MaintainPartInventoryDTO> rowDatas = new ArrayList<>();
        String columnPropertyConfigFile = this.getColumnMappingConfigFile(request, response);
        if( StringUtils.isNotBlank(columnPropertyConfigFile) ) {
            rowDatas = ExcelTool.inportMap2Object(rowStringDatas, this.partRowClazz , columnPropertyConfigFile);
        }else {
            rowDatas = this.convertStringDataToObject(rowStringDatas, null);
        }
        if( rowDatas==null || rowDatas.size()==0 ) {
            excelUploadResult.setErrorMessage("文件内容为空,上传失败。");
            return  fail(excelUploadResult,null);
        }
        //5 数据校验
        //String url = "http://localhost:8086/"+ MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_BATCH_CHECK_PART_INFO;
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_BATCH_CHECK_PART_INFO);
        Map<String, Object> paramMap = new HashMap<>(1);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, rowDatas);
        paramMap.put("loginCompanyCode", clientLoginInfo.getClientCompany().getCompanyCode());
        RestResponse restResponse = RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, MaintainPartBatchImportRespDTO.class);
        if (restResponse != null && restResponse.isSuccess()) {
            List<AbstractExcelUploadController.ExcelUploadRowError> errorList = new ArrayList<>();
            MaintainPartBatchImportRespDTO restDto = (MaintainPartBatchImportRespDTO) restResponse.getData();
            MaintainPartInveImportRespDTO resultDto = new MaintainPartInveImportRespDTO();
            List<ImportErrorListDTO> list = restDto.getImportErrorDTOList();
            List<MaintainPartInventoryDTO>  newDataList = restDto.getMaintainPartInventoryDTOList();
            resultDto.setInventoryDTOList(newDataList);
            resultDto.setTotalPartNum(restDto.getTotalPartNum());
            resultDto.setTotalSuccessNum(restDto.getTotalSuccessNum());
            resultDto.setTotalSuccessPrice(restDto.getTotalSuccessPrice());
            if(!CollectionUtils.isEmpty(list)){
                list.forEach(error ->{
                    ExcelUploadRowError excelUploadRowError = new ExcelUploadRowError(error.getRowNum(),error.getReason());
                    errorList.add(excelUploadRowError);
                });
                excelUploadResult.setCheckErrorMessages(errorList);
                excelUploadResult.setResolvedRows(rowDatas.size());
                excelUploadResult.setPersistSuccessRows(0);
                excelUploadResult.setPersistFailedRows(rowDatas.size());
                excelUploadResult.setErrorMessage("数据未全部通过校验，请确认后重新上传");
                resultDto.setExcelUploadResult(excelUploadResult);
                return fail( excelUploadResult,resultDto);
            }
            return RestResponse.success(resultDto);
        }
        return restResponse;
    }

    //响应失败
    protected RestResponse fail(AbstractExcelUploadController.ExcelUploadResult excelUploadResult,MaintainPartInveImportRespDTO resultDto) {
        //缓存每行的校验错误信息
        if( excelUploadResult.getCheckErrorMessages()!=null && excelUploadResult.getCheckErrorMessages().size()>0 ) {
            long checkErrorId = SequenceUtil.generate();
            AbstractExcelUploadController.ExcelUploadRowError[] errors = new AbstractExcelUploadController.ExcelUploadRowError[excelUploadResult.getCheckErrorMessages().size()];
            excelUploadResult.getCheckErrorMessages().toArray(errors);
            sessionRedisSentinelCache.set(AbstractExcelUploadController.EXCEL_CHECK_ERRORS_CACHE_KEY+checkErrorId , errors, EXCEL_CHECK_ERRORS_CACHE_TIMEOUT);
            excelUploadResult.setCheckErrorDetailUrl("/showexcelerror?checkErrorId="+checkErrorId );
            logger.info("配件库存数据校验存在错误数据，写入缓存成功。checkErrorId="+ checkErrorId );
        }
        RestResponse returnResp = RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        returnResp.setMsg(excelUploadResult.getErrorMessage());
        returnResp.setData(resultDto);
        return returnResp;
    }

    //解析输入流，返回文本数据
    private List<Map<Integer,String>> getExcelData( final InputStream excelInputStream, final String excelType, final List<Integer> specifiedSheetIndexs){
        List<Map<Integer, String>> rowDatas = new ArrayList<Map<Integer,String>>(1);
        try{
            rowDatas = ExcelTool.inport(excelInputStream, excelType, specifiedSheetIndexs);
            logger.info(PARTLOGTAG+"解析文件，返回文本数据，解析出的行数="+ rowDatas.size() );
            return rowDatas;
        }catch(Exception ex ) {
            logger.error(PARTLOGTAG+"解析文件异常！", ex );
            return new ArrayList<Map<Integer,String>>(1);
        }
    }

    //通过excelUrl获得输入流
    private InputStream getPartExcelInputStream( final String excelUrl){
        if( excelUrl.toLowerCase().startsWith("http") ) {//--->>>URL地址
            final String baseDir            = System.getProperty("java.io.tmpdir") + File.separator + "upload";
            final String excelFileName  = excelUrl.substring(excelUrl.lastIndexOf("/")+1);
            final String storeFilePath   = baseDir + File.separator + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) +"_"+ excelFileName;
            logger.info(PARTLOGTAG+"上传文件名称="+ excelFileName );
            logger.info(PARTLOGTAG+"存储保存路径="+ storeFilePath );
            Map<String,String> httpHeader = new HashMap<String,String>(2);
            httpHeader.put("Referer", "https://prd-third.izuche.com");//有防盗链机制，必须加上Referer
            boolean downloadOK =  BaseHttpClient.download(BaseHttpClient.HttpMethod.GET, excelUrl, null, httpHeader, storeFilePath);
            if( downloadOK ) {
                logger.info(PARTLOGTAG+"文件下载成功" );
                File storeFile = new File(storeFilePath);
                try {
                    return new FileInputStream(storeFile);
                } catch (FileNotFoundException e) {
                    logger.error(PARTLOGTAG+"文件下载失败", e );
                    return null;
                }
            }else {
                logger.error(PARTLOGTAG+"文件下载失败" );
                return null;
            }
        }else if( excelUrl.toLowerCase().startsWith("file:") ) {//--->>>本地文件路径，方便开发调试
            final String storeFilePath   = excelUrl.substring(5);
            logger.info(PARTLOGTAG+"存储保存路径="+ storeFilePath );
            if( !storeFilePath.toLowerCase().endsWith("xls") && !storeFilePath.toLowerCase().endsWith("xlsx") ) {
                logger.error(PARTLOGTAG+"不是Excel文件");
                return null;
            }
            File storeFile = new File(storeFilePath);
            if( storeFile.exists()==false ) {
                logger.error(PARTLOGTAG+"文件不存在");
                return null;
            }
            if( storeFile.isFile()==false ) {
                logger.error(PARTLOGTAG+"文件不存在");
                return null;
            }
            try {
                return new FileInputStream(storeFile);
            } catch (FileNotFoundException e) {
                logger.error(PARTLOGTAG+"文件不存在", e );
                return null;
            }
        }else {
            logger.error(PARTLOGTAG+"不支持此类型的URL！");
            return null;
        }
    }

    @PostMapping("/batchInventory/batchInsert")
    @ApiOperation("批次导入库存文件并保存")
    @RequestFunction(functionName = "批次导入库存文件并保存")
    @ApiImplicitParam(name = "excelUrl", value = "文件路径", required = true)
    public RestResponse batchInsert(String excelUrl,
                                    HttpServletRequest request,
                                    HttpServletResponse response) {
        ExcelUploadResult eur = new ExcelUploadResult();
        return this.start(excelUrl, request, response, eur,16);
    }

    @Override
    protected List<Integer> getResolveSheetIndexes() {
        return Arrays.asList(1);
    }

    @Override
    protected String getColumnMappingConfigFile(HttpServletRequest request, HttpServletResponse response) {
        return null;
    }

    @Override
    protected List<MaintainPartInventoryDTO> convertStringDataToObject(List<Map<Integer, String>> rowDatas, ExcelUploadResult excelUploadResult) {
        List<Map<Integer, String>> collect = rowDatas.stream().filter(row -> !checkFieldAllNull(row)).collect(Collectors.toList());
        return collect.stream().map( row ->{
            MaintainPartInventoryDTO dto = new MaintainPartInventoryDTO();
            dto.setPartName(row.get(0));
            dto.setPartNo(row.get(1));
            int num = (row.get(2) == null || "".equals(row.get(2).toString())) ? 0 : Integer.parseInt(row.get(2)) ;
            BigDecimal unitPrice = new BigDecimal(0);
            if(!Objects.isNull(row.get(3)) && !"".equals(row.get(2).toString())){
                unitPrice = new BigDecimal(row.get(3));
            }
            dto.setStorageQuantity(num);
            dto.setStorageUnitPrice(unitPrice.setScale(2,RoundingMode.HALF_UP));
            dto.setStorageTotalPrice(unitPrice.multiply(new BigDecimal(num)).setScale(2, RoundingMode.HALF_UP));
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    protected List<ExcelUploadRowError> checkExcelData(List<MaintainPartInventoryDTO> rowDatas, HttpServletRequest request, HttpServletResponse response, ExcelUploadResult excelUploadResult) {
        List<ExcelUploadRowError> errorList = new ArrayList<>();
        int rowNum = 2;
        for (int i = 0; i < rowDatas.size(); i++) {
            MaintainPartInventoryDTO importDto = rowDatas.get(i);
            StringBuffer error = new StringBuffer();
            if (StringUtils.isBlank(importDto.getPartName())) {
                error.append("配件名称不能为空").append(COMMA);
            }
            if (StringUtils.isBlank(importDto.getCategoryName())) {
                error.append("配件分类不能为空").append(COMMA);
            }
            String errorStr = error.toString();
            if (StringUtils.isNotBlank(errorStr)) {
                if (errorStr.lastIndexOf(COMMA) + 1 == errorStr.length()) {
                    errorStr = errorStr.substring(0, errorStr.length() - 1);
                }
                ExcelUploadRowError rowError = new ExcelUploadRowError(rowNum, errorStr);
                errorList.add(rowError);
            }
            rowNum += 1;
        }
        return errorList;
    }
    private boolean checkFieldAllNull(Object obj) {
        Field[] declaredFields = obj.getClass().getDeclaredFields();
        boolean flag = true;
        if(obj instanceof Map){
            Map<Integer,String> map = (Map) obj;
            for(Map.Entry entry : map.entrySet()){
                if (!ObjectUtils.isEmpty(entry.getValue())) {
                    flag = false;
                    break;
                }
            }
            return flag;
        }
        for (Field field : declaredFields) {
            field.setAccessible(true);
            Object o = null;
            try {
                o = field.get(obj);
            } catch (IllegalAccessException e) {
                logger.error("校验对象属性值异常");
            }
            if (!ObjectUtils.isEmpty(o)) {
                flag = false;
                break;
            }
        }
        return flag;
    }

    @Override
    protected boolean beforePersist(List<MaintainPartInventoryDTO> rowDatas, HttpServletRequest request, HttpServletResponse response, ExcelUploadResult excelUploadResult) {
        return true;
    }

    @Override
    protected RestResponse executePersist(List<MaintainPartInventoryDTO> rowDatas, HttpServletRequest request, HttpServletResponse response, ExcelUploadResult excelUploadResult) {
        ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
        MaintainPartBatchCreateDTO param = new MaintainPartBatchCreateDTO();

        Map<String, Object> paramMap = new HashMap<>(3);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, rowDatas);
        paramMap.put("operateId", clientLoginInfo.getBaseInfo().getStaffId());
        paramMap.put("operateName", clientLoginInfo.getBaseInfo().getStaffName());
        paramMap.put("loginCompanyCode", clientLoginInfo.getClientCompany().getCompanyCode());
        String restUrl = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_BATCH_SAVE);
        RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, ImportErrorDTO.class);
        logger.info("【配件导入batchInsert】响应response={}", JSON.toJSONString(restResponse));
        if (restResponse != null && restResponse.isSuccess()) {
            List<ExcelUploadRowError> errorList = new ArrayList<>();
            @SuppressWarnings({ "unchecked", "rawtypes" })
            List<ImportErrorDTO> list = (List) restResponse.getData();
            if(!CollectionUtils.isEmpty(list)){
                list.forEach(error ->{
                    ExcelUploadRowError excelUploadRowError = new ExcelUploadRowError(error.getRowNum(),error.getReason());
                    errorList.add(excelUploadRowError);
                });
                excelUploadResult.setCheckErrorMessages(errorList);
                excelUploadResult.setResolvedRows(rowDatas.size());
                excelUploadResult.setPersistSuccessRows(0);
                excelUploadResult.setPersistFailedRows(rowDatas.size());
                excelUploadResult.setErrorMessage("数据未通过校验，请确认后重新上传");
                return this.fail(null, request, response, excelUploadResult);
            }
            excelUploadResult.setPersistSuccessRows(rowDatas.size());
            return this.success(null, request, response, excelUploadResult);
        } else {
            if(restResponse != null){
                excelUploadResult.setErrorMessage(restResponse.getMsg());
            }
            return this.fail(null, request, response, excelUploadResult);
        }
    }

}
