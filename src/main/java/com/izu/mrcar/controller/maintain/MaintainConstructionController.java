package com.izu.mrcar.controller.maintain;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.CarInfoDTO;
import com.izu.asset.dto.param.PrivateCarQueryDTO;
import com.izu.asset.dto.param.VehicleSerialNoReqQTO;
import com.izu.asset.dto.vehicle.CarInfoBaseDTO;
import com.izu.asset.dto.vehicle.MrcarConstructionDTO;
import com.izu.asset.dto.vehicle.MrcarConstructionDetailPCDTO;
import com.izu.asset.dto.vehicle.MrcarMaintainConstructionDTO;
import com.izu.carasset.CarAssetRestLocator;
import com.izu.carasset.CarAssetRestUrl;
import com.izu.carasset.dto.output.ConstructionDTO;
import com.izu.carasset.dto.output.ConstructionDetailDTO;
import com.izu.carasset.dto.output.maintain.MaintainOperationRecordDTO;
import com.izu.carasset.dto.output.maintain.PCCreateInfoDTO;
import com.izu.carasset.enums.*;
import com.izu.carasset.util.StringUtils;
import com.izu.framework.doc.JrdApiDoc;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.excel.Column;
import com.izu.framework.web.util.excel.ExSheet;
import com.izu.framework.web.util.excel.ExcelTool;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.DataPermUtil;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.util.ObjectTransferUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/maintain/construction")
@Slf4j
@Deprecated
public class MaintainConstructionController {

    @Value("${mrcar-asset-core.host.url}")
    private String ASSET_HOST_URL;

    /**
     * 查询该企业下所有的首汽车辆编号集合，用于查看综合平台的维保和保险等信息
     * @return
     */
    public List<CarInfoDTO> selectVehicleSerialNoListByCompanyId(VehicleSerialNoReqQTO dto) {
        List<CarInfoDTO> carLicenseList = null;
        DataPermUtil.putDataPerm(dto);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, dto);
        RestResponse response = RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, ASSET_HOST_URL+"/car/selectVehicleSerialNoListByCompanyId", paramMap, null, CarInfoDTO.class);
        if(null!=response && response.isSuccess()){
            if(null!=response.getData()){
                carLicenseList = (List<CarInfoDTO>) response.getData();
            }
        }
        return carLicenseList;
    }

    @RequestMapping(value = "list")
    @RequestFunction(functionName = "维保工单列表")
    public RestResponse list(@RequestBody Map<String, Object> param) {
        String searchWithDataPermission = param.get("searchWithDataPermission") == null ? "1" : String.valueOf(param.get("searchWithDataPermission"));
        Integer permitTypeOfBussCodes = param.get("permitTypeOfBussCodes") == null ? 0 : Integer.parseInt(param.get("permitTypeOfBussCodes").toString());
        String listType = StringUtils.isBlank(param.get("listType")) ? "1,2,5" : String.valueOf(param.get("listType"));
        Set<Byte> constructionTypeStr = new HashSet<>();
        String[] listStr = listType.split(",");
        for (String s : listStr) {
            constructionTypeStr.add(Byte.valueOf(s));
        }
        param.put("constructionTypeStr", constructionTypeStr);
        log.info("【list】searchWithDataPermission:{},permitTypeOfBussCodes:{}", searchWithDataPermission, permitTypeOfBussCodes);
        if ("1".equals(searchWithDataPermission)) {
            this.autosetDataPermissionForQueryVehicleList(param, permitTypeOfBussCodes);
        }
        //mrcar数据权限范围
        VehicleSerialNoReqQTO dto = new VehicleSerialNoReqQTO().setVehicleLicense(param.get("vehicleLicense").toString());
        if (StringUtils.isNotBlank(param.get("structId"))) {
            dto.setStructId((Integer) param.get("structId"));
        }
        if (StringUtils.isNotBlank(param.get("cityCode"))) {
            dto.setCityCode(String.valueOf(param.get("cityCode")));
        }
        if (StringUtils.isNotBlank(param.get("companyId"))) {
            dto.setCompanyId((Integer)(param.get("companyId")));
        }
        if (StringUtils.isNotBlank(param.get("deptId"))) {
            dto.setDeptId((Integer)(param.get("deptId")));
        }
        dto.setLoginCompanyId(LoginSessionUtil.getBaseLoginInfo().obtainBelongCompanyId());
        List<CarInfoDTO> carInfoDTOs = selectVehicleSerialNoListByCompanyId(dto);
        if(CollectionUtil.isNotEmpty(carInfoDTOs)){
            Map<String, CarInfoDTO> carInfoMap = carInfoDTOs.stream().collect(Collectors.toMap(CarInfoDTO::getVehicleLicense, e -> e,(e1,e2) -> e1));
            List<String> vehicleSerialNoList = carInfoDTOs.stream().map(CarInfoDTO::getVehicleSerialNoNewSystem).collect(Collectors.toList());
            param.put("vehicleSerialNoList", vehicleSerialNoList);
            param.put("pageType",1);
            param.put("isPageQuery",1);
            log.info("【list】请求参数：paramMap = {}", JSON.toJSONString(param));
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
            String restUrl = new CarAssetRestLocator().getRestUrl(CarAssetRestUrl.MAINTAIN_CONSTRUCTION_LIST);
            RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, MrcarConstructionDTO.class);
            log.info("【list】响应结果：response = {}", JSON.toJSONString(restResponse));
            if (restResponse.getCode() == RestErrorCode.SUCCESS && Objects.nonNull(restResponse.getData())) {
                PageDTO pageDTO = (PageDTO) restResponse.getData();
                List<MrcarConstructionDTO> constructionDTOS = pageDTO.getResult();
                constructionDTOS.forEach(e -> {
                    setButtonShows(e);
                    CarInfoDTO carInfoDTO = carInfoMap.get(e.getVehicleLicense());
                    if (carInfoDTO != null) {
                        e.setBelongCityName(carInfoDTO.getBelongCityName());
                        e.setStructName(carInfoDTO.getStructName());
                    }
                });
            }
            return restResponse;
        }else {
            return RestResponse.success(new PageDTO(1, 10, 0, Collections.emptyList()));
        }

    }

    public void autosetDataPermissionForQueryVehicleList(final Map<String, Object> condition, final Integer permitTypeOfBussCodes) {
        log.info("数据权限：设置数据权限，permitTypeOfBussCodes:{}", permitTypeOfBussCodes);
        if (condition == null) {
            return;
        }
       /* SSOLoginUser ssoLoginUser = WebSessionUtil.getCurrentLoginUser();
        if (ssoLoginUser.getAssetDataPermissionScope() == null) {
            log.info("数据权限：当前登录账号没有设置数据权限范围，需要联系管理员进行配置（自动设置车辆ID为最大值，以确保查不出任何车辆）");
            condition.put("vehicleId", Integer.MAX_VALUE);
            return;
        } else if (ssoLoginUser.getAssetDataPermissionScope().byteValue() == 1 || ssoLoginUser.getAssetDataPermissionScope().byteValue() == 2) {//1:查看自己的, 2:查看部门的 （采用一样的逻辑来限制数据权限）
            *//***********************************************一、限制 机动车所有人、使用人*******************************************//*
            final Set<String> assetCodes = ssoLoginUser.getAssetDataPermissionAssetCode();
            if (assetCodes == null || assetCodes.size() == 0) {
                log.info("数据权限：当前登录账号没有分配资产组织机构编码，需要联系管理员进行配置（自动设置车辆ID为最大值，以确保查不出任何车辆）");
                condition.put("vehicleId", Integer.MAX_VALUE);
                return;
            }
            log.info("数据权限：当前登录账号能查看的资产组织机构编码=" + assetCodes.toString());//当有值时（说明已经给当前登录账号分配好了，这是正常情况）
            if (permitTypeOfBussCodes == null || permitTypeOfBussCodes.intValue() == 0) {//限定为：机动车所有人或机动车使用人
                condition.put("belongOrOperateBussCodes", assetCodes);
            } else if (permitTypeOfBussCodes.intValue() == 1) {//限定为：机动车所有人
                condition.put("belongBussCodes", assetCodes);
            } else if (permitTypeOfBussCodes.intValue() == 2) {//限定为：机动车使用人
                condition.put("operateBussCodes", assetCodes);
            } else {//传入其它，默认限定为：机动车所有人或机动车使用人
                condition.put("belongOrOperateBussCodes", assetCodes);
            }
        } else if (ssoLoginUser.getAssetDataPermissionScope().byteValue() == 3) {//为 3则查看全部
            //查看全部则不限制
            return;
        } else {
            log.info("数据权限：无效的数据权限范围，需要联系管理员进行调整（自动设置车辆ID为最大值，以确保查不出任何车辆）");
            condition.put("vehicleId", Integer.MAX_VALUE);
            return;
        }*/
    }


    /**
     * 设置按钮是否展示
     *
     * @param constructionDTO 请求参数
     */
    private void setButtonShows(ConstructionDTO constructionDTO) {
        constructionDTO.setShowEditButton(Boolean.FALSE);
        constructionDTO.setShowViewButton(Boolean.TRUE);
        constructionDTO.setShowCancelButton(Boolean.FALSE);
        constructionDTO.setShowSubmitApprovalButton(Boolean.FALSE);
        Byte constructionStatusAll=constructionDTO.getConstructionStatusAll();
        Byte paymentStatus=constructionDTO.getPaymentStatus();
        constructionDTO.setConstructionStatusAllStr(ConstructionStatusAllEnum.getByCode(constructionStatusAll).getName());
        constructionDTO.setPaymentStatusStr(ConstructionPaymentStatusEnum.getByCode(paymentStatus).getName());
        if (!constructionStatusAll.equals(ConstructionStatusAllEnum.MATERIALS_SUBMITTED.getCode())) {
            return;
        }
        if (constructionDTO.getConstructionSource() == ConstructionSourceEnum.SHOUQI.getCode()) {
//            if (constructionDTO.getConstructionStatus() == ConstructionStatusEnum.HAVE_REPAIR.getCode()
//                    && (constructionDTO.getApprovalStatus() == ConstructionApprovalStatusEnum.REFUSE.getCode()
//                    || constructionDTO.getApprovalStatus() == ConstructionApprovalStatusEnum.UNCOMMIT.getCode())) {
//                constructionDTO.setShowEditButton(Boolean.TRUE);
//            }
//            if (constructionDTO.getConstructionStatus() == ConstructionStatusEnum.HAVE_REPAIR.getCode()
//                    && (constructionDTO.getApprovalStatus() == ConstructionApprovalStatusEnum.REFUSE.getCode()
//                    || constructionDTO.getApprovalStatus() == ConstructionApprovalStatusEnum.UNCOMMIT.getCode()
//                    || constructionDTO.getApprovalStatus() == ConstructionApprovalStatusEnum.PASS.getCode())
//                    && (constructionDTO.getPaymentStatus() == ConstructionPaymentStatusEnum.CANNOT_COMMIT.getCode()
//                    || constructionDTO.getPaymentStatus() == ConstructionPaymentStatusEnum.WAIT_PAY.getCode())) {
//                constructionDTO.setShowCancelButton(Boolean.TRUE);
//            }
//            if (constructionDTO.getConstructionStatus() == ConstructionStatusEnum.HAVE_REPAIR.getCode()
//                    && (constructionDTO.getApprovalStatus() == ConstructionApprovalStatusEnum.REFUSE.getCode()
//                    || constructionDTO.getApprovalStatus() == ConstructionApprovalStatusEnum.UNCOMMIT.getCode()
//                    || constructionDTO.getApprovalStatus() == ConstructionApprovalStatusEnum.REFUSE.getCode())
//                    && (constructionDTO.getPaymentStatus() == ConstructionPaymentStatusEnum.CANNOT_COMMIT.getCode())) {
//                constructionDTO.setShowSubmitApprovalButton(Boolean.TRUE);
//            }
            constructionDTO.setShowReimbButton(paymentStatus.equals(ConstructionPaymentStatusEnum.WAIT_PAY.getCode())||paymentStatus.equals(ConstructionPaymentStatusEnum.HAVE_PAYED.getCode())?Boolean.TRUE:Boolean.FALSE);
            constructionDTO.setShowRelaunchReimbButton(paymentStatus.equals(ConstructionPaymentStatusEnum.PAY_REJECT.getCode()) || paymentStatus.equals(ConstructionPaymentStatusEnum.PAY_ABOLISH.getCode())?Boolean.TRUE:Boolean.FALSE);
        } else if (constructionDTO.getConstructionSource() == ConstructionSourceEnum.COOPERATOR.getCode() || constructionDTO.getConstructionSource() == ConstructionSourceEnum.THIRD.getCode()) {
/*            SSOLoginUser ssoLoginUser = WebSessionUtil.getCurrentLoginUser();
            if (Objects.nonNull(ssoLoginUser) && ssoLoginUser.getAssetDataPermissionScope() != null) {
                log.info("当前登录人权限获取assetDataPermissionScope=" + ssoLoginUser.getAssetDataPermissionScope());
                if (ssoLoginUser.getAssetDataPermissionScope() == 3) {
                    constructionDTO.setShowReimbButton(paymentStatus.equals(ConstructionPaymentStatusEnum.WAIT_PAY.getCode())||paymentStatus.equals(ConstructionPaymentStatusEnum.HAVE_PAYED.getCode()) ? Boolean.TRUE : Boolean.FALSE);
                    constructionDTO.setShowRelaunchReimbButton(paymentStatus.equals(ConstructionPaymentStatusEnum.PAY_REJECT.getCode()) || paymentStatus.equals(ConstructionPaymentStatusEnum.PAY_ABOLISH.getCode())? Boolean.TRUE : Boolean.FALSE);
                    return;
                }
                final Set<String> assetCodes = ssoLoginUser.getAssetDataPermissionAssetCode();
                log.info("当前登录人权限获取assetCodes=" + JSON.toJSONString(assetCodes));
                if (assetCodes != null && assetCodes.size() > 0 && assetCodes.contains(constructionDTO.getOperateBussCode())) {
                    constructionDTO.setShowReimbButton(paymentStatus.equals(ConstructionPaymentStatusEnum.WAIT_PAY.getCode())||paymentStatus.equals(ConstructionPaymentStatusEnum.HAVE_PAYED.getCode()) ? Boolean.TRUE : Boolean.FALSE);
                    constructionDTO.setShowRelaunchReimbButton(paymentStatus.equals(ConstructionPaymentStatusEnum.PAY_REJECT.getCode()) || paymentStatus.equals(ConstructionPaymentStatusEnum.PAY_ABOLISH.getCode())? Boolean.TRUE : Boolean.FALSE);
                }
            }*/
        }
    }


    @GetMapping(value = "view")
    @RequestFunction(functionName = "维保工单详情")
    public RestResponse view(@RequestParam("constructionNo") String constructionNo) {
        String restUrl = new CarAssetRestLocator().getRestUrl(CarAssetRestUrl.MAINTAIN_CONSTRUCTION_VIEW);

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("constructionNo", constructionNo);
        log.info("【view】请求参数：paramMap = {}", JSON.toJSONString(paramMap));
        RestResponse restResponse = RestClient.requestForObject(BaseHttpClient.HttpMethod.GET, restUrl, paramMap, null, MrcarConstructionDetailPCDTO.class);
        log.info("【view】响应结果：response = {}", JSON.toJSONString(restResponse));
        if (restResponse.isSuccess()) {
            MrcarConstructionDetailPCDTO constructionDetailPCDTO = ObjectTransferUtil.cast(restResponse.getData());
            if (constructionDetailPCDTO != null) {
                PCCreateInfoDTO pcCreateInfoDTO = constructionDetailPCDTO.getPcCreateInfoDTO();
                if (pcCreateInfoDTO != null) {
                    paramMap = Maps.newHashMapWithExpectedSize(1);
                    PrivateCarQueryDTO privateCarQueryDTO = new PrivateCarQueryDTO();
                    privateCarQueryDTO.setVehicleLicense(pcCreateInfoDTO.getVehicleLicense());
                    paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, privateCarQueryDTO);
                    RestResponse restResponse1 = RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.CAR_INFO_GET_BY_VEHICLE_VIN), paramMap, null, CarInfoBaseDTO.class);
                    if (restResponse1.isSuccess()) {
                        CarInfoBaseDTO carInfoBaseDTO = ObjectTransferUtil.cast(restResponse1.getData());
                        if (carInfoBaseDTO != null) {
                            constructionDetailPCDTO.setBelongCityName(carInfoBaseDTO.getBelongCityName());
                            constructionDetailPCDTO.setStructName(carInfoBaseDTO.getStructName());
                        }
                    }
                }
            }
        }
        return restResponse;
    }


    @GetMapping(value = "queryDetail")
    @RequestFunction(functionName = "维保工单详情")
    public RestResponse queryDetail(@RequestParam("constructionNo") String constructionNo) {
        String restUrl = new CarAssetRestLocator().getRestUrl(CarAssetRestUrl.MAINTAIN_CONSTRUCTION_DETAIL);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("constructionNo", constructionNo);
        log.info("【queryDetail】请求参数：paramMap = {}", JSON.toJSONString(paramMap));
        RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.GET, restUrl, paramMap, null, ConstructionDetailDTO.class);
        log.info("【queryDetail】响应结果：response = {}", JSON.toJSONString(restResponse));
        return restResponse;
    }


    /**
     * @param request
     * @param response
     * @return
     * <AUTHOR>
     * 导出付施工单列表信息
     */
    @RequestMapping(value = "/exportConstructionList", method = RequestMethod.POST)
    @RequestFunction(functionName = "维保工单导出")
    public RestResponse exportConstructionList(@RequestBody Map<String, Object> param, HttpServletRequest request, HttpServletResponse response) {
        String searchWithDataPermission = param.get("searchWithDataPermission") == null ? "1" : String.valueOf(param.get("searchWithDataPermission"));
        Integer permitTypeOfBussCodes = param.get("permitTypeOfBussCodes") == null ? 0 : Integer.parseInt(param.get("permitTypeOfBussCodes").toString());
        log.info("【export】searchWithDataPermission:{},permitTypeOfBussCodes:{}", searchWithDataPermission, permitTypeOfBussCodes);
        if ("1".equals(searchWithDataPermission)) {
            this.autosetDataPermissionForQueryVehicleList(param, permitTypeOfBussCodes);
        }

        String listType = StringUtils.isBlank(param.get("listType")) ? "1,2,5" : String.valueOf(param.get("listType"));
        Set<Byte> constructionTypeStr = new HashSet<>();
        String[] listStr = listType.split(",");

        for (String s: listStr) {
            constructionTypeStr.add(Byte.valueOf(s));
        }
        param.put("constructionTypeStr",constructionTypeStr);
        List<MrcarMaintainConstructionDTO> list = new ArrayList<>();
        VehicleSerialNoReqQTO dto = new VehicleSerialNoReqQTO().setVehicleLicense(param.get("vehicleLicense").toString());
        if (StringUtils.isNotBlank(param.get("structId"))) {
            dto.setStructId((Integer) param.get("structId"));
        }
        if (StringUtils.isNotBlank(param.get("cityCode"))) {
            dto.setCityCode(String.valueOf(param.get("cityCode")));
        }
        List<CarInfoDTO> carInfoDTOs = selectVehicleSerialNoListByCompanyId(dto);
        if(CollectionUtil.isNotEmpty(carInfoDTOs)){
            Map<String, CarInfoDTO> carInfoMap = carInfoDTOs.stream().collect(Collectors.toMap(CarInfoDTO::getVehicleLicense, e -> e,(e1,e2) -> e1));
            List<String> vehicleSerialNoList = carInfoDTOs.stream().map(CarInfoDTO::getVehicleSerialNoNewSystem).collect(Collectors.toList());
            param.put("vehicleSerialNoList", vehicleSerialNoList);
            log.info("【export】请求参数：param = {}", JSON.toJSONString(param));
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
            String restUrl = new CarAssetRestLocator().getRestUrl(CarAssetRestUrl.EXPORT_CONSTRUCTION_LIST);
            RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null, MrcarMaintainConstructionDTO.class);
            if (restResponse != null && restResponse.isSuccess()) {
                list = (List<MrcarMaintainConstructionDTO>) restResponse.getData();
                list.forEach(e -> {
                    CarInfoDTO carInfoDTO = carInfoMap.get(e.getVehicleLicense());
                    if (carInfoDTO != null) {
                        e.setBelongCityName(carInfoDTO.getBelongCityName());
                        e.setStructName(carInfoDTO.getStructName());
                    }
                });
            }else {

            }
        }else {

        }
        final List<ExSheet> exSheets = new ArrayList<ExSheet>();
        ExSheet sheet1 = null;
        if(listType.equals(ConstructionTypeEnum.Accident_Repair.getCode().toString())){
            sheet1 = this.makeSGConstructionSheetnew(list);
        }else {
            sheet1 = this.makeWBConstructionSheetnew(list);
        }
        exSheets.add(sheet1);
        try {
            //B1 根据车辆数量，选择合适的Excel文件类型。（注：采用POI生成不同类型的Excel时，其性能不一样。xls：生成速度快，但文件比较大；xlsx：生成速度慢，但文件比较小）
            String excelType = "xls";
            if (list.size() < 3000) {
                excelType = "xlsx";
            }
            //B2 生成文件名(需要对不同浏览器进行兼容，尤其是Firefox)
            String filename = "";
            if(listType.equals(ConstructionTypeEnum.Accident_Repair.getCode().toString())) {
                filename = "事故施工单_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + "." + excelType;
            }else{
                filename = "维保施工单_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + "." + excelType;
            }
            String excelFilenameCn = filename;
            String ua = request.getHeader("User-Agent");
            if (StringUtils.isEmpty(ua)) {//如果没有UA，则采用英文文件名
                filename = "CheckReduce" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + "." + excelType;
            } else if (ua.toLowerCase().indexOf("firefox") != -1) {//UA是Firefox
                filename = new String(filename.getBytes("UTF8"), "ISO8859-1");
            } else {//UA是IE、Chrome或其它
                filename = URLEncoder.encode(filename, "UTF8");
            }
            response.setHeader("Content-Disposition", "attachment; filename=\"" + filename + "\"");
            response.setHeader("Download-Filename", URLEncoder.encode(excelFilenameCn, "UTF8") );//这是和前端约定的自定义响应头
            if ("xls".equalsIgnoreCase(excelType)) {
                response.setContentType("application/vnd.ms-excel;charset=gbk");
            } else if ("xlsx".equalsIgnoreCase(excelType)) {
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=gbk");
            }
            response.setCharacterEncoding("gbk");
            //B3 输出EXCEL
            ExcelTool.export(exSheets, response.getOutputStream(), excelType);
            return null;
        } catch (Exception e) {
            log.error("导出施工单Excel异常！", e);
            return null;
        }

    }


    private ExSheet makeWBConstructionSheetnew(final List<MrcarMaintainConstructionDTO> list) {
        final List<Column> columnModes = new ArrayList<Column>(150);
        columnModes.add(new Column("constructionNo", "维保单号", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("vehicleLicense", "车牌号", (short) 2800, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("constructionType", "维保类型", (short) 2000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("constructionStatus", "工单状态", (short) 2500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("constructionCityName", "维修城市", (short) 2000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("belongCityName", "车辆所在城市", (short) 2000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("structName", "车辆所属部门", (short) 2000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("constructionGarageName", "维修厂名称", (short) 9000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("constructionInTime", "到厂时间", (short) 4500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("constructionOutTime", "出厂时间", (short) 4500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        final List<Map<String, String>> rowDatas = new ArrayList<Map<String, String>>(list.size() + 1);
        final SimpleDateFormat yyyyMMddHHmmss_sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        final SimpleDateFormat yyyyMMdd_sdf = new SimpleDateFormat("yyyy-MM-dd");
        if(CollectionUtils.isNotEmpty(list)) {
            for (MrcarMaintainConstructionDTO construction : list) {
                final Map<String, String> rowdata = new HashMap<String, String>(columnModes.size() * 2);
                rowdata.put("constructionNo", construction.getConstructionNo());
                rowdata.put("vehicleLicense", construction.getVehicleLicense());
                rowdata.put("constructionType", ConstructionTypeEnum.getNameByCode(construction.getConstructionType()));
                rowdata.put("constructionStatus", ConstructionStatusAllEnum.getByCode(construction.getConstructionStatusAll()).getName());
                rowdata.put("constructionCityName", construction.getConstructionCityName());
                rowdata.put("belongCityName", construction.getBelongCityName());
                rowdata.put("structName", construction.getStructName());
                rowdata.put("constructionGarageName", construction.getConstructionGarageName());
                rowdata.put("constructionInTime", StringUtils.isBlank(construction.getConstructionInTime()) ? "" : yyyyMMddHHmmss_sdf.format(construction.getConstructionInTime()));
                rowdata.put("constructionOutTime", StringUtils.isBlank(construction.getConstructionOutTime()) ? "" : yyyyMMddHHmmss_sdf.format(construction.getConstructionOutTime()));
                rowDatas.add(rowdata);
            }
        }
        StringBuffer sheetName = new StringBuffer("资产-维保施工单");
        return new ExSheet(columnModes, rowDatas, sheetName.toString());
    }

    private ExSheet makeSGConstructionSheetnew(final List<MrcarMaintainConstructionDTO> list) {
        final List<Column> columnModes = new ArrayList<Column>(150);
        columnModes.add(new Column("constructionNo", "事故单号", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("constructionStatus", "工单状态", (short) 2500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("paymentStatus", "支付状态", (short) 2000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("vehicleLicense", "车牌号", (short) 2800, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("vehicleVin", "车架号", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("vehicleSerialNo", "车辆编号", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("vehicleBrandName", "车辆品牌", (short) 2000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("vehicleModelName", "车型", (short) 2000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("operateBussName", "机动车使用人", (short) 8000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("leasePurposeName", "租赁用途", (short) 2000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("belongStoreName", "短租门店", (short) 6000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("vehicleRegisterTime", "登记日期", (short) 3000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("mileage", "创建时里程", (short) 2000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("createTime", "创建时间", (short) 4500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("reportRepairName", "送修人", (short) 2000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("reportRepairPhone", "送修人电话", (short) 2800, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("receptionFactoryInMileage", "到厂时里程", (short) 2000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("constructionInTime", "到厂时间", (short) 4500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("trafficInsuranceReportNo", "交强险报案号", (short) 4000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("commercialConstructionReportNo", "商业险报案号", (short) 6000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("constructionCityName", "维修城市", (short) 2000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("constructionSupplierType", "维修供应商", (short) 2000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("constructionGarageName", "维修厂名称", (short) 6000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("constructionDetail", "维修名称", (short) 12000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("contructionDiscountFeeAmount", "定损金额", (short) 2000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("accidentMatFee", "代垫金额", (short) 2000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("constructionEndMileage", "完成时里程", (short) 2000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("constructionEndTime", "维修完成时间", (short) 4500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("receptionFactoryOutName", "出厂接车人姓名", (short) 2000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("receptionFactoryOutPhone", "出厂接车人电话", (short) 2800, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("materialsSubmited", "材料已提交", (short) 2000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("receptionFactoryOutMileage", "出厂时里程", (short) 2000, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        columnModes.add(new Column("constructionOutTime", "出厂时间", (short) 4500, CellStyle.ALIGN_CENTER, CellStyle.VERTICAL_CENTER, Cell.CELL_TYPE_STRING, "0", HSSFColor.WHITE.index, HSSFColor.BLACK.index));
        final List<Map<String, String>> rowDatas = new ArrayList<Map<String, String>>(list.size() + 1);
        final SimpleDateFormat yyyyMMddHHmmss_sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        final SimpleDateFormat yyyyMMdd_sdf = new SimpleDateFormat("yyyy-MM-dd");
        if(CollectionUtils.isNotEmpty(list)) {
            for (MrcarMaintainConstructionDTO construction : list) {
                final Map<String, String> rowdata = new HashMap<String, String>(columnModes.size() * 2);
                rowdata.put("constructionNo", construction.getConstructionNo());
                rowdata.put("constructionStatus", ConstructionStatusAllEnum.getByCode(construction.getConstructionStatusAll()).getName());
                rowdata.put("paymentStatus", ConstructionPaymentStatusEnum.getByCode(construction.getPaymentStatus()).getName());
                rowdata.put("vehicleLicense", construction.getVehicleLicense());
                rowdata.put("vehicleVin", construction.getVehicleVin());
                rowdata.put("vehicleSerialNo", construction.getVehicleSerialNo());
                rowdata.put("vehicleBrandName", construction.getVehicleBrandName());
                rowdata.put("vehicleModelName", construction.getVehicleModelName());
                rowdata.put("operateBussName", construction.getOperateBussName());
                rowdata.put("leasePurposeName", construction.getLeasePurposeName());
                if(construction.getLeasePurpose().equals(LeasePurposeEnum.SHORT_REST.getCode())){
                    rowdata.put("belongStoreName", construction.getBelongStoreName());
                }else{
                    rowdata.put("belongStoreName", "");
                }
                rowdata.put("vehicleRegisterTime", StringUtils.isBlank(construction.getVehicleRegisterTime()) ? "" : yyyyMMdd_sdf.format(construction.getVehicleRegisterTime()));
                rowdata.put("mileage", construction.getMileage().toString());
                rowdata.put("createTime", StringUtils.isBlank(construction.getCreateTime()) ? "" : yyyyMMddHHmmss_sdf.format(construction.getCreateTime()));
                rowdata.put("reportRepairName", construction.getReportRepairName());
                rowdata.put("reportRepairPhone", construction.getReportRepairPhone());
                rowdata.put("receptionFactoryInMileage", construction.getReceptionFactoryInMileage().toString());
                rowdata.put("constructionInTime", StringUtils.isBlank(construction.getConstructionInTime()) ? "" : yyyyMMddHHmmss_sdf.format(construction.getConstructionInTime()));
                rowdata.put("trafficInsuranceReportNo", construction.getTrafficInsuranceReportNo());
                rowdata.put("commercialConstructionReportNo", construction.getCommercialConstructionReportNo());
                rowdata.put("constructionCityName", construction.getConstructionCityName());
                rowdata.put("constructionSupplierType", MaintainSupplierEnum.getNameByCode(construction.getConstructionSupplierType()));
                rowdata.put("constructionGarageName", construction.getConstructionGarageName());
                rowdata.put("constructionDetail", construction.getConstructionDetail());
                rowdata.put("contructionDiscountFeeAmount", construction.getContructionDiscountFeeAmount().setScale(2, RoundingMode.DOWN) + "");
                rowdata.put("accidentMatFee", construction.getAccidentMatFee().setScale(2, RoundingMode.DOWN) + "");
                rowdata.put("constructionEndMileage", construction.getConstructionEndMileage().toString());
                rowdata.put("constructionEndTime", StringUtils.isBlank(construction.getConstructionEndTime()) ? "" : yyyyMMddHHmmss_sdf.format(construction.getConstructionEndTime()));
                rowdata.put("receptionFactoryOutName", construction.getReceptionFactoryOutName());
                rowdata.put("receptionFactoryOutPhone", construction.getReceptionFactoryOutPhone());
                rowdata.put("materialsSubmited", construction.getMaterialsSubmited().equals((byte)1) ? "是" : "否");
                rowdata.put("receptionFactoryOutMileage", construction.getReceptionFactoryOutMileage().toString());
                rowdata.put("constructionOutTime", StringUtils.isBlank(construction.getConstructionOutTime()) ? "" : yyyyMMddHHmmss_sdf.format(construction.getConstructionOutTime()));
                rowDatas.add(rowdata);
            }
        }
        StringBuffer sheetName = new StringBuffer("资产-事故施工单");
        return new ExSheet(columnModes, rowDatas, sheetName.toString());
    }


    @GetMapping(value = "/getStatusList")
    @JrdApiDoc(simpleDesc = "获取维保工单的状态枚举列表", author = "ty", resDataClass = JSONObject.class)
    public RestResponse getStatusList(@RequestParam("busType") Byte busType) {
        String restUrl = new CarAssetRestLocator().getRestUrl(CarAssetRestUrl.MAINTAIN_CONSTRUCTION_GET_STATUS_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("busType", busType);
        log.info("【获取施工单状态码集合列表】请求参数：paramMap = {}", JSON.toJSONString(paramMap));
        RestResponse restResponse = RestClient.requestForObject(BaseHttpClient.HttpMethod.GET, restUrl, paramMap, null, Object.class);
        log.info("【获取施工单状态码集合列表】响应结果：response = {}", JSON.toJSONString(restResponse));
        return restResponse;
    }

    @RequestMapping(value = "/oprationRecord/queryRecords")
    @JrdApiDoc(simpleDesc = "施工单操作记录查询", author = "贺新春", resDataClass = MaintainOperationRecordDTO.class)
    public RestResponse queryRecords(@Verify(param = "constructionNo", rule = "required") @RequestParam("constructionNo") String constructionNo) {
        Map<String, Object> httpParams = new HashMap<>();
        httpParams.put("constructionNo",constructionNo);
        String restUrl = new CarAssetRestLocator().getRestUrl(CarAssetRestUrl.MAINTAIN_OPRATION_RECORD);
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, httpParams, null,MaintainOperationRecordDTO.class);
    }

}
