package com.izu.mrcar.controller.maintain.evaluation;

import com.google.common.collect.Sets;
import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.common.BaseDTO;
import com.izu.asset.dto.maintain.evaluation.MaintainEvaluationClientPageReqDTO;
import com.izu.asset.dto.maintain.evaluation.MaintainEvaluationClientPageRespDTO;
import com.izu.asset.dto.maintain.evaluation.MaintainEvaluationLabelRespDTO;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
* @Description: 客户端-维保评价接口
* @author: hxc
* @Date: 2023/3/30
**/
@RestController
@Api(tags = "维保管理")
public class ClientMaintainEvaluationController {

    private final MrCarAssetRestLocator mrCarAssetRestLocator = new MrCarAssetRestLocator();

    @PostMapping(MrCarAssetRestCenter.CLIENT_MAINTAIN_EVALUATION_GET_PAGE_LIST)
    @RequestFunction(functionName = "维修评价分页列表-客户端")
    @ApiOperation(value = "维修评价分页列表（客户端）",notes = "作者：贺新春")
    public RestResponse<PageDTO<MaintainEvaluationClientPageRespDTO>> getPageList(@RequestBody MaintainEvaluationClientPageReqDTO param){

        param.setLoginCompanyCode(LoginSessionUtil.getClientLoginInfo().obtainBelongCompanyCode());
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CLIENT_MAINTAIN_EVALUATION_GET_PAGE_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, MaintainEvaluationClientPageRespDTO.class);
    }

    @PostMapping(MrCarAssetRestCenter.CLIENT_MAINTAIN_EVALUATION_EXPORT)
    @RequestFunction(functionName = "维修评价导出-客户端")
    @ApiOperation(value = "维修评价导出（客户端）",notes = "作者：贺新春")
    @ExportExcelWeb(fileName = ExportExcelConstants.FILE_NAME_MAINTAIN_EVALUATION, filePath = ExportExcelConstants.FILE_PATH,
            sheet = ExportExcelConstants.SHEET_NAME_MAINTAIN_EVALUATION, c = ClientMaintainEvaluationExportDTO.class)
    public PageDTO<ClientMaintainEvaluationExportDTO> exportForClient(@RequestBody MaintainEvaluationClientPageReqDTO param, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response) {

        ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
        param.setLoginCompanyCode(clientLoginInfo.obtainBelongCompanyCode());
        izuEasyExcelSession.setUserName(clientLoginInfo.getBaseInfo().getStaffName());
        param.setPage(izuEasyExcelSession.getPageNo());
        param.setPageSize(ExportExcelConstants.PAGE_SIZE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CLIENT_MAINTAIN_EVALUATION_GET_PAGE_LIST);
        RestResponse<PageDTO<MaintainEvaluationClientPageRespDTO>> restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null,MaintainEvaluationClientPageRespDTO.class);
        if(restResponse != null && restResponse.isSuccess()){
            PageDTO<MaintainEvaluationClientPageRespDTO> data = restResponse.getData();
            if (data.getTotal()>10000){
                throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
            }
            List<ClientMaintainEvaluationExportDTO> exportDTOList = BeanUtil.copyList(data.getResult(), ClientMaintainEvaluationExportDTO.class);
            return new PageDTO(data.getPage(), data.getPageSize(), data.getTotal(), exportDTOList);
        }
        return null;
    }

    @PostMapping(MrCarAssetRestCenter.CLIENT_MAINTAIN_EVALUATION_GET_LABEL)
    @RequestFunction(functionName = "获取评价标签-客户端")
    @ApiOperation(value = "获取评价标签（客户端）",notes = "作者：贺新春")
    public RestResponse<MaintainEvaluationLabelRespDTO> getEvaluationLabel(){
        BaseDTO param = new BaseDTO();
        param.setCompanyCodes(Sets.newHashSet(LoginSessionUtil.getClientLoginInfo().getClientCompany().getCompanyCode()));
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CLIENT_MAINTAIN_EVALUATION_GET_LABEL);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null);
    }
}
