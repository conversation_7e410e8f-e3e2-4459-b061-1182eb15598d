package com.izu.mrcar.controller.maintain.part;

import com.izu.asset.dto.maintain.part.input.client.MaintainPartInventoryDTO;
import com.izu.mrcar.controller.AbstractExcelUploadController;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class MaintainPartInveImportRespDTO {

    private AbstractExcelUploadController.ExcelUploadResult excelUploadResult;

    private List<MaintainPartInventoryDTO> inventoryDTOList;

    private Integer totalPartNum;
    private Integer totalSuccessNum;
    private BigDecimal totalSuccessPrice;
}
