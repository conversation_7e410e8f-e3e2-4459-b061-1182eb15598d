package com.izu.mrcar.controller.maintain.workhours;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.alibaba.fastjson.JSONObject;
import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.carasset.dto.output.VehicleBrandResolvedDTO;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import izu.org.apache.poi.hssf.usermodel.HSSFDataValidation;
import izu.org.apache.poi.ss.usermodel.*;
import izu.org.apache.poi.ss.util.CellRangeAddressList;

import izu.org.apache.poi.hssf.usermodel.DVConstraint;
import izu.org.apache.poi.hssf.usermodel.HSSFWorkbook;

import java.util.List;
import java.util.Map;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023/5/26 18:47
 */
public class SelectDataSheetWriteHandlerV2 implements SheetWriteHandler {
    private Map<Integer, List<String>> selectMap;

    private char[] alphabet = new char[]{'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L',
            'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'};

    public SelectDataSheetWriteHandlerV2() {
    }

    @Override
    public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {


        // 需要设置下拉框的sheet页
        Sheet curSheet = writeSheetHolder.getSheet();
        //DataValidationHelper helper = curSheet.getDataValidationHelper();
        String dictSheetName = "车型参考表";
        Workbook workbook = writeWorkbookHolder.getWorkbook();
        // 数据字典的sheet页
        // 数据字典的sheet页
        Sheet dictSheet = workbook.createSheet(dictSheetName);
        Row row = dictSheet.createRow((short)0);
        // Create a cell and put a value in it.
        row.createCell(0).setCellValue("车辆类型名称");
        // Or do it on one line.
        row.createCell(1).setCellValue("车辆品牌名称");
        row.createCell(2).setCellValue("车辆厂牌名称");
        row.createCell(3).setCellValue("车型编码");
        row.createCell(4).setCellValue("车型名称");

        //String data="[{\"manufacturerCode\":\"010101\",\"manufacturerName\":\"一汽奥迪\",\"vehicleBrandCode\":\"0101\",\"vehicleBrandName\":\"奥迪\",\"vehicleCategoryCode\":\"01\",\"vehicleCategoryName\":\"小轿车\",\"vehicleModelCode\":\"010101010\",\"vehicleModelName\":\"156(进口)\"},{\"manufacturerCode\":\"010501\",\"manufacturerName\":\"北京奔驰\",\"vehicleBrandCode\":\"0105\",\"vehicleBrandName\":\"奔驰\",\"vehicleCategoryCode\":\"01\",\"vehicleCategoryName\":\"小轿车\",\"vehicleModelCode\":\"010501012\",\"vehicleModelName\":\"156(进口)\"},{\"manufacturerCode\":\"020101\",\"manufacturerName\":\"广汽本田\",\"vehicleBrandCode\":\"0201\",\"vehicleBrandName\":\"本田\",\"vehicleCategoryCode\":\"02\",\"vehicleCategoryName\":\"旅行车\",\"vehicleModelCode\":\"020101002\",\"vehicleModelName\":\"156(进口)\"},{\"manufacturerCode\":\"016001\",\"manufacturerName\":\"阿尔法·罗密欧\",\"vehicleBrandCode\":\"0160\",\"vehicleBrandName\":\"阿尔法·罗密欧\",\"vehicleCategoryCode\":\"01\",\"vehicleCategoryName\":\"小轿车\",\"vehicleModelCode\":\"016001002\",\"vehicleModelName\":\"156(进口)\"},{\"manufacturerCode\":\"010101\",\"manufacturerName\":\"一汽奥迪\",\"vehicleBrandCode\":\"0101\",\"vehicleBrandName\":\"奥迪\",\"vehicleCategoryCode\":\"01\",\"vehicleCategoryName\":\"小轿车\",\"vehicleModelCode\":\"010101012\",\"vehicleModelName\":\"156(进口).\"},{\"manufacturerCode\":\"020301\",\"manufacturerName\":\"福建奔驰\",\"vehicleBrandCode\":\"0203\",\"vehicleBrandName\":\"奔驰\",\"vehicleCategoryCode\":\"02\",\"vehicleCategoryName\":\"旅行车\",\"vehicleModelCode\":\"020301007\",\"vehicleModelName\":\"166(进口)\"}]";
        List<VehicleBrandResolvedDTO> vehicleBrandResolvedDTOs=new ArrayList<>();
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.MAINTAIN_WORK_BASE_GET_VEHICLE_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.GET, url, paramMap, null,VehicleBrandResolvedDTO.class);
        if(Objects.nonNull(restResponse.getData())){
            vehicleBrandResolvedDTOs=(List<VehicleBrandResolvedDTO>)restResponse.getData();
        }
    //    vehicleBrandResolvedDTOs = JSONObject.parseArray(data, VehicleBrandResolvedDTO.class);

        for(int i=0;i<vehicleBrandResolvedDTOs.size();i++){
            VehicleBrandResolvedDTO element=vehicleBrandResolvedDTOs.get(i);
            Row currentRow = dictSheet.createRow((short)(i+1));
            currentRow.createCell(0).setCellValue(element.getVehicleCategoryName());
            // Or do it on one line.
            currentRow.createCell(1).setCellValue(element.getVehicleBrandName());
            currentRow.createCell(2).setCellValue(element.getManufacturerName());
            currentRow.createCell(3).setCellValue(element.getVehicleModelCode());
            currentRow.createCell(4).setCellValue(element.getVehicleModelName());
        }
//        // 隐藏数据字典的sheet页
       // workbook.setSheetHidden(workbook.getSheetIndex(curSheet), true);
        //dictSheet.ad
        addAutoMatchValidationToSheet(workbook,curSheet,buildHeadList(vehicleBrandResolvedDTOs),'A','B',1,200);
    }

    /**
     * 将数字列转化成为字母列
     * @param num
     * @author: CUI
     * @date: 2022-05-27 9:12
     * @return: java.lang.String
     */
    private String getExcelColumn(int num) {
        String column = "";
        int len = alphabet.length - 1;
        int first = num / len;
        int second = num % len;
        if (num <= len) {
            column = alphabet[num] + "";
        } else {
            column = alphabet[first - 1] + "";
            if (second == 0) {
                column = column + alphabet[len] + "";
            } else {
                column = column + alphabet[second - 1] + "";
            }
        }
        return column;
    }

    /**
     * 给sheet页，添加下拉列表
     *
     * @param workbook    excel文件，用于添加Name
     * @param targetSheet 级联列表所在sheet页
     * @param options     级联数据 ['百度','阿里巴巴']
     * @param column      下拉列表所在列 从'A'开始
     * @param fromRow     下拉限制开始行
     * @param endRow      下拉限制结束行
     */
    public static void addValidationToSheet(Workbook workbook, Sheet targetSheet, Object[] options, char column, int fromRow, int endRow) {
        String hiddenSheetName = "sheet" + workbook.getNumberOfSheets();
        Sheet optionsSheet = workbook.createSheet(hiddenSheetName);
        String nameName = column + "_parent";

        int rowIndex = 0;
        for (Object option : options) {
            int columnIndex = 0;
            Row row = optionsSheet.createRow(rowIndex++);
            Cell cell = row.createCell(columnIndex++);
            cell.setCellValue(option.toString());
        }

        createName(workbook, nameName, hiddenSheetName + "!$A$1:$A$" + options.length);

        DVConstraint constraint = DVConstraint.createFormulaListConstraint(nameName);
        CellRangeAddressList regions = new CellRangeAddressList(fromRow, endRow, (int) column - 'A', (int) column - 'A');
        targetSheet.addValidationData(new HSSFDataValidation(regions, constraint));

        workbook.setSheetHidden(workbook.getSheetIndex(optionsSheet), true);
    }

    /**
     * 给sheet页  添加级联下拉列表
     *
     * @param workbook    excel
     * @param targetSheet sheet页
     * @param options     要添加的下拉列表内容  ， keys 是下拉列表1中的内容，每个Map.Entry.Value 是对应的级联下拉列表内容
     * @param keyColumn   下拉列表1位置
     * @param valueColumn 级联下拉列表位置
     * @param fromRow     级联限制开始行
     * @param endRow      级联限制结束行
     */
    public static void addValidationToSheet(Workbook workbook, Sheet targetSheet, Map<String, List<String>> options, char keyColumn, char valueColumn, int fromRow, int endRow) {
        String hiddenSheetName = "sheet" + workbook.getNumberOfSheets();
        Sheet hiddenSheet = workbook.createSheet(hiddenSheetName);
        List<String> firstLevelItems = new ArrayList<>();

        int rowIndex = 0;
        for (Map.Entry<String, List<String>> entry : options.entrySet()) {
            String parent = formatNameName(entry.getKey());
            firstLevelItems.add(parent);
            List<String> children = entry.getValue();

            int columnIndex = 0;
            Row row = hiddenSheet.createRow(rowIndex++);
            Cell cell = null;

            for (String child : children) {
                cell = row.createCell(columnIndex++);
                cell.setCellValue(child);
            }

            char lastChildrenColumn = (char) ((int) 'A' + children.size() - 1);
            createName(workbook, parent, String.format(hiddenSheetName + "!$A$%s:$%s$%s", rowIndex, lastChildrenColumn, rowIndex));

            DVConstraint constraint = DVConstraint.createFormulaListConstraint("INDIRECT($" + keyColumn + "1)");
            CellRangeAddressList regions = new CellRangeAddressList(fromRow, endRow, valueColumn - 'A', valueColumn - 'A');
            targetSheet.addValidationData(new HSSFDataValidation(regions, constraint));
        }

        addValidationToSheet(workbook, targetSheet, firstLevelItems.toArray(), keyColumn, fromRow, endRow);

    }

    /**
     * 根据用户在keyColumn选择的key, 自动填充value到valueColumn
     *
     * @param workbook    excel
     * @param targetSheet sheet页
     * @param keyValues   匹配关系 {'百度','www.baidu.com'},{'淘宝','www.taobao.com'}
     * @param keyColumn   要匹配的列（例如 网站中文名称）
     * @param valueColumn 匹配到的内容列（例如 网址）
     * @param fromRow     下拉限制开始行
     * @param endRow      下拉限制结束行
     */
    public static void addAutoMatchValidationToSheet(Workbook workbook, Sheet targetSheet, Map<String, String> keyValues, char keyColumn, char valueColumn, int fromRow, int endRow) {
        String hiddenSheetName = "sheet" + workbook.getNumberOfSheets();
        Sheet hiddenSheet = workbook.createSheet(hiddenSheetName);

        // init the search region(A and B columns in hiddenSheet)
        int rowIndex = 0;
        for (Map.Entry<String, String> kv : keyValues.entrySet()) {
            Row totalSheetRow = hiddenSheet.createRow(rowIndex++);

            Cell cell = totalSheetRow.createCell(0);
            cell.setCellValue(kv.getKey());

            cell = totalSheetRow.createCell(1);
            cell.setCellValue(kv.getValue());
        }

        for (int i = fromRow; i <= endRow; i++) {
            Row totalSheetRow = targetSheet.getRow(i);
            if (totalSheetRow == null) {
                totalSheetRow = targetSheet.createRow(i);
            }

            Cell cell = totalSheetRow.getCell((int) valueColumn - 'A');
            if (cell == null) {
                cell = totalSheetRow.createCell((int) valueColumn - 'A');
            }

            String keyCell = String.valueOf(keyColumn) + (i + 1);
            String formula = String.format("IF(ISNA(VLOOKUP(%s,%s!A:B,2,0)),\"\",VLOOKUP(%s,%s!A:B,2,0))", keyCell, hiddenSheetName, keyCell, hiddenSheetName);

            cell.setCellFormula(formula);
        }
        workbook.setSheetHidden(workbook.getSheetIndex(hiddenSheet), true);
        // init the keyColumn as comboList
        addValidationToSheet(workbook, targetSheet, keyValues.keySet().toArray(), keyColumn, fromRow, endRow);
    }

    private static Name createName(Workbook workbook, String nameName, String formula) {
        Name name = workbook.createName();
        name.setNameName(nameName);
        name.setRefersToFormula(formula);
        return name;
    }

    /**
     * 隐藏excel中的sheet页
     *
     * @param workbook
     * @param start    需要隐藏的 sheet开始索引
     */
    private static void hideTempDataSheet(HSSFWorkbook workbook, int start) {
        for (int i = start; i < workbook.getNumberOfSheets(); i++) {
            workbook.setSheetHidden(i, true);
        }
    }

    /**
     * 不可数字开头
     *
     * @param name
     * @return
     */
    static String formatNameName(String name) {
        name = name.replaceAll(" ", "").replaceAll("-", "_").replaceAll(":", ".");
        if (Character.isDigit(name.charAt(0))) {
            name = "_" + name;
        }

        return name;
    }

    private Map<String, String> buildHeadList(List<VehicleBrandResolvedDTO> vehicleBrandResolvedDTOs){
        Map<String, String> selectMap = new HashMap<>();
        selectMap=vehicleBrandResolvedDTOs.stream().collect(Collectors.toMap(VehicleBrandResolvedDTO::getVehicleModelCode,VehicleBrandResolvedDTO::getVehicleModelName,(k,v)->k,HashMap::new));
        return selectMap;
    }
}
