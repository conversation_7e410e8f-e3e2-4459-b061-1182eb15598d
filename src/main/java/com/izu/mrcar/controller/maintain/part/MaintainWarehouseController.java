package com.izu.mrcar.controller.maintain.part;

import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.maintain.part.input.client.MaintainPartWarehouseInputDTO;
import com.izu.asset.dto.maintain.part.input.garage.MaintainGaragePartWarehouseInputDTO;
import com.izu.asset.dto.maintain.part.output.client.MaintainPartWarehouseListDTO;
import com.izu.asset.dto.maintain.part.output.garage.MaintainGaragePartWarehouseListDTO;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.maintain.GarageMaintainDataPermUtil;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@Api(tags = "维保管理")
public class MaintainWarehouseController {

    @PostMapping(MrCarAssetRestCenter.CLIENT_MAINTAIN_WAREHOUSE_LIST)
    @RequestFunction(functionName = "总库存列表-客户端")
    @ApiOperation(value = "总库存列表（客户端）",notes = "作者：mapp")
    public RestResponse<PageDTO<MaintainPartWarehouseListDTO>> getClientList(@RequestBody MaintainPartWarehouseInputDTO param){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.CLIENT_MAINTAIN_WAREHOUSE_LIST);
        param.setLoginCompanyCode(loginBaseInfo.obtainBelongCompanyCode());
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, MaintainPartWarehouseListDTO.class);
    }

    @PostMapping(MrCarAssetRestCenter.GARAGE_MAINTAIN_WAREHOUSE_LIST)
    @RequestFunction(functionName = "总库存列表-维修厂端")
    @ApiOperation(value = "总库存列表（维修厂端）",notes = "作者：mapp")
    public RestResponse<PageDTO<MaintainGaragePartWarehouseListDTO>> getGarageList(@RequestBody MaintainGaragePartWarehouseInputDTO param){
        param.setCompanyCodes(GarageMaintainDataPermUtil.getDatePermGarageScope(param.getCompanyCodes()));
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.GARAGE_MAINTAIN_WAREHOUSE_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, MaintainGaragePartWarehouseListDTO.class);
    }

}
