package com.izu.mrcar.controller.maintain.workhours;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: 工时系数导出
 * @date 2023/5/6 19:46
 */
@Data
@ColumnWidth(20)
public class WorkFactorExportDTO {

    /**
     * 工时系数模板ID template_id
     */
    @ExcelIgnore
    private Integer templateId;

    /**
     * 车辆分类编码 vehicle_category_code
     */
    @ExcelIgnore
    private String vehicleCategoryCode;

    /**
     * 车辆分类名称 vehicle_category_name
     */
    @ExcelProperty(value = "车辆类型",index = 1)
    private String vehicleCategoryName;

    /**
     * 车辆品牌代码 vehicle_brand_code
     */
    @ExcelIgnore
    private String vehicleBrandCode;

    /**
     * 车辆品牌名称 vehicle_brand_name
     */
    @ExcelProperty(value = "车辆品牌",index = 2)
    private String vehicleBrandName;

    /**
     * 车辆厂商代码 manufacturer_code
     */
    @ExcelIgnore
    private String manufacturerCode;

    /**
     * 车辆厂商名称 manufacturer_name
     */
    @ExcelProperty(value = "车辆厂商",index = 3)
    private String manufacturerName;

    /**
     * 车辆车型代码 vehicle_model_code
     */
    @ExcelIgnore
    private String vehicleModelCode;

    /**
     * 车辆车型名称 vehicle_model_name
     */
    @ExcelProperty(value = "车型",index = 4)
    private String vehicleModelName;

    /**
     * 工时系数编号 factor_no
     */
    @ExcelProperty(value = "工时系数编号",index = 0)
    private String factorNo;

    /**
     * 工时系数 factor_value
     */
    @ExcelProperty(value = "工时系数",index = 5)
    private BigDecimal factorValue;

    /**
     * 状态(1：启用 4：禁用) factor_state
     */
    @ExcelIgnore
    private Byte factorState;
    @ExcelProperty(value = "状态",index = 6)
    private String factorStateName;
    @ExcelIgnore
    private Integer createrId;

    @ExcelProperty(value = "创建人",index = 7)
    private String createrName;

    @ExcelProperty(value = "创建时间",index = 8)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ExcelIgnore
    private Integer updaterId;

    @ExcelProperty(value = "修改人",index = 9)
    private String updaterName;


    @ExcelProperty(value = "修改时间",index = 10)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    public void setFactorState(Byte factorState) {
        this.factorState = factorState;
        this.factorStateName = Objects.equals(factorState,new Byte("1"))?"启用":"禁用";
    }
}
