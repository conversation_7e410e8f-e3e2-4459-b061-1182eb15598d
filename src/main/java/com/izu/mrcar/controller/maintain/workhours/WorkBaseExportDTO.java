package com.izu.mrcar.controller.maintain.workhours;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: 工时基数导出
 * @date 2023/5/6 19:46
 */
@Data
@ColumnWidth(20)
public class WorkBaseExportDTO {

    /**
     * 工时基数模板ID template_id
     */
    @ExcelIgnore
    private Integer templateId;

    /**
     * 维修组code repair_type_code
     */
    @ExcelIgnore
    private String repairTypeCode;

    /**
     * 维修项目编号 repair_item_no
     */
    @ExcelProperty(value = "工时基数编号",index = 0)
    private String repairItemNo;

    /**
     * 维修项目名称 repair_item_name
     */
    @ExcelProperty(value = "维修名称",index = 1)
    private String repairItemName;

    /**
     * 维修组名称 repair_type_name
     */
    @ExcelProperty(value = "维修组",index = 2)
    private String repairTypeName;


    /**
     * 工时费 repair_labor_amount
     */
    @ExcelProperty(value = "工时基数（元）",index = 3)
    private BigDecimal repairLaborAmount;

    /**
     * 是否常用项目（1:是 0:否） is_frequently
     */
    @ExcelIgnore
    private Boolean isFrequently;

    @ExcelProperty(value = "是否常用",index = 4)
    private String isFrequentlyName;

    /**
     * 是否必选（1:是 0:否） is_required
     */
    @ExcelIgnore
    private Boolean isRequired;
    @ExcelProperty(value = "是否必选",index = 5)
    private String isRequiredName;

    /**
     * 必选生效日期 valid_sdate
     */
    @DateTimeFormat("yyyy-MM-dd")
    @ExcelProperty(value = "必选生效日期",index = 6)
    private Date validSdate;

    /**
     * 必选失效日期 valid_edate
     */
    @ExcelProperty(value = "必选失效日期",index = 7)
    @DateTimeFormat("yyyy-MM-dd")
    private Date validEdate;

    /**
     * 状态（1：启用 4：禁用） state
     */

    @ExcelIgnore
    private Byte state;

    @ExcelProperty(value = "状态",index = 8)
    private String stateName;

    /**
     * 施工类型code（1:维修 2:保养 3:事故维修 4:救援 100:其它 construction_type_code
     */
    @ExcelIgnore
    private Byte constructionTypeCode;

    /**
     * '施工类型名称 construction_type_name
     */
    @ExcelIgnore
    private String constructionTypeName;

    @ExcelIgnore
    private Integer updaterId;

    @ExcelProperty(value = "修改人",index = 9)
    private String updaterName;

    @ExcelProperty(value = "修改时间",index = 10)
    @DateTimeFormat("yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    public void setState(Byte state) {
        this.state = state;
        this.stateName = Objects.equals(state,new Byte("1"))?"启用":"禁用";
    }

    public void setFrequently(Boolean isFrequently) {
        this.isFrequently = isFrequently;
        this.isFrequentlyName=Objects.equals(isFrequently,true)?"是":"否";
    }

    public void setRequired(Boolean isRequired) {
        this.isRequired = isRequired;
        this.isRequiredName=Objects.equals(isRequired,true)?"是":"否";
    }
}
