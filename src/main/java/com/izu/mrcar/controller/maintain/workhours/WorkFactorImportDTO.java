package com.izu.mrcar.controller.maintain.workhours;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: 系统导入对象
 * @date 2023/5/30 9:32
 */
@Data
public class WorkFactorImportDTO {


    /**
     * 车辆车型代码 vehicle_model_code
     */
    @ExcelProperty(value = "车型编码",index = 0)
    private String vehicleModelCode;

    /**
     * 车辆车型名称 vehicle_model_name
     */
    @ExcelProperty(value = "车型名称",index = 1)
    private String vehicleModelName;

    /**
     * 工时系数 factor_value
     */
    @ExcelProperty(value = "工时系数",index = 2)
    private BigDecimal factorValue;
}
