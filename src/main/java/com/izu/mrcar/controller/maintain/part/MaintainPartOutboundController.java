package com.izu.mrcar.controller.maintain.part;

import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.maintain.part.input.client.MaintainPartOutboundInputDTO;
import com.izu.asset.dto.maintain.part.output.client.MaintainPartOutboundDetailDTO;
import com.izu.asset.dto.maintain.part.output.client.MaintainPartOutboundListDTO;
import com.izu.asset.dto.maintain.part.output.garage.MaintainGaragePartOutboundListDTO;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.maintain.GarageMaintainDataPermUtil;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@Api(tags = "维保管理")
public class MaintainPartOutboundController {

    @PostMapping(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_OUTBOUND_LIST)
    @RequestFunction(functionName = "出库记录列表-客户端")
    @ApiOperation(value = "出库记录列表（客户端）",notes = "作者：mapp")
    public RestResponse<PageDTO<MaintainPartOutboundListDTO>> getClientList(@RequestBody MaintainPartOutboundInputDTO param){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_OUTBOUND_LIST);
        param.setLoginCompanyCode(loginBaseInfo.obtainBelongCompanyCode());
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, MaintainPartOutboundListDTO.class);
    }

    @PostMapping(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_OUTBOUND_DETAIL)
    @RequestFunction(functionName = "出库记录明细-客户端")
    @ApiOperation(value = "出库记录明细（客户端）",notes = "作者：mapp")
    @ApiImplicitParam(name = "outboundNo", value = "出库单号", required = true)
    public RestResponse<MaintainPartOutboundDetailDTO> getClientDetail(String outboundNo){
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_OUTBOUND_DETAIL);
        Map<String,Object> param = new HashMap<>();
        param.put("outboundNo",outboundNo);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, url, param, null, MaintainPartOutboundDetailDTO.class);
    }

    @PostMapping(MrCarAssetRestCenter.GARAGE_MAINTAIN_PART_OUTBOUND_LIST)
    @RequestFunction(functionName = "出库记录列表-维修厂端")
    @ApiOperation(value = "出库记录列表（维修厂端）",notes = "作者：mapp")
    public RestResponse<PageDTO<MaintainGaragePartOutboundListDTO>> getGarageList(@RequestBody MaintainPartOutboundInputDTO param){
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.GARAGE_MAINTAIN_PART_OUTBOUND_LIST);
        param.setCompanyCodes(GarageMaintainDataPermUtil.getDatePermGarageScope(param.getCompanyCodes()));
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, MaintainGaragePartOutboundListDTO.class);
    }

    @PostMapping(MrCarAssetRestCenter.GARAGE_MAINTAIN_PART_OUTBOUND_DETAIL)
    @RequestFunction(functionName = "出库记录明细-维修厂端")
    @ApiOperation(value = "出库记录明细（维修厂端）",notes = "作者：mapp")
    @ApiImplicitParam(name = "outboundNo", value = "出库单号", required = true)
    public RestResponse<MaintainPartOutboundDetailDTO> getGarageDetail(String outboundNo){
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_OUTBOUND_DETAIL);
        Map<String,Object> param = new HashMap<>();
        param.put("outboundNo",outboundNo);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, url, param, null, MaintainPartOutboundDetailDTO.class);
    }

    @PostMapping(MrCarAssetRestCenter.MAINTAIN_OUTBOUND_DETAIL_PART_LIST_EXPORT)
    @RequestFunction(functionName = "出库详情中配件列表信息导出")
    @ApiOperation(value = "出库详情中配件列表信息导出")
    @ApiImplicitParam(name = "outboundNo", value = "出库单号", required = true)
    @ExportExcelWeb(fileName = ExportExcelConstants.FILE_NAME_MAINTAIN_OUTBOUND_PART_INFO,filePath = ExportExcelConstants.FILE_PATH,
            sheet = ExportExcelConstants.SHEET_NAME_MAINTAIN_OUTBOUND_PART_INFO, c =MaintainPartOutboundExport.class)
    public PageDTO<MaintainPartOutboundExport> exportPartInfo(String outboundNo, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response){
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_OUTBOUND_DETAIL);
        Map<String, Object> param = new HashMap<>();
        param.put("outboundNo",outboundNo);
        RestResponse restResponse = RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, url, param, null, MaintainPartOutboundDetailDTO.class);
        List<MaintainPartOutboundExport> excelList = new ArrayList<>();
        if(restResponse!=null && restResponse.isSuccess()){
            MaintainPartOutboundDetailDTO restDto =(MaintainPartOutboundDetailDTO) restResponse.getData();
            if(restDto != null && !CollectionUtils.isEmpty(restDto.getDetailListDTOS())){
                if(restDto.getDetailListDTOS().size() > 10000){
                    throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
                }
                excelList = BeanUtil.copyList(restDto.getDetailListDTOS(),MaintainPartOutboundExport.class);
            }
        }
        return new PageDTO<>(1,10000,excelList.size(),excelList);
    }

}
