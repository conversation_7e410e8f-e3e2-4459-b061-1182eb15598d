package com.izu.mrcar.controller.maintain.evaluation;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import izu.org.apache.poi.ss.usermodel.BorderStyle;
import izu.org.apache.poi.ss.usermodel.HorizontalAlignment;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;

@HeadStyle(
        fillForegroundColor = 22,
        fillBackgroundColor = 22
)
@ContentStyle(
        horizontalAlignment = HorizontalAlignment.CENTER,
        borderBottom = BorderStyle.THIN,
        borderLeft = BorderStyle.THIN,
        borderRight = BorderStyle.THIN,
        borderTop = BorderStyle.THIN,
        wrapped = true
)

@ApiModel
@Data
public class ClientMaintainEvaluationExportDTO {

    @ApiModelProperty(value = "施工类型",example = "事故")
    @ColumnWidth(value = 15)
    @ExcelProperty(value = "施工类型")
    private String constructionTypeStr;

    @ApiModelProperty(value = "维修厂名称",example = "xxx维修厂")
    @ColumnWidth(value = 30)
    @ExcelProperty(value = "维修厂名称")
    private String garageName;

    @ApiModelProperty(value = "平均分",example = "4.5")
    @ColumnWidth(value = 10)
    @ExcelProperty(value = "平均分")
    private BigDecimal averageScore;

    @ApiModelProperty(value = "评价明细",example = "环境：1 服务：2 技术：5")
    @ColumnWidth(value = 35)
    @ExcelProperty(value = "评价明细")
    private String  evaluationDetail;

    @ApiModelProperty(value = "满意标签",example = "主动引导,维修专业")
    @ColumnWidth(value = 100)
    @ExcelProperty(value = "满意标签")
    private String satisfiedLabel;

    @ApiModelProperty(value = "不满意标签",example = "停车难")
    @ColumnWidth(value = 100)
    @ExcelProperty(value = "不满意标签")
    private String dissatisfiedLabel;

    @ApiModelProperty(value = "评语",example = "服务超级棒")
    @ColumnWidth(value = 100)
    @ExcelProperty(value = "评语")
    private String remark;

    @ApiModelProperty(value = "追评内容",example = "没修好")
    @ColumnWidth(value = 100)
    @ExcelProperty(value = "追评内容")
    private String addRemark;

    @ApiModelProperty(value = "追评时间",example = "2023-03-29 10:00:00")
    @ColumnWidth(value = 20)
    @ExcelProperty(value = "追评时间")
    private Date updateTime;

    @ApiModelProperty(value = "评价时间",example = "2023-03-29 09:00:00")
    @ColumnWidth(value = 20)
    @ExcelProperty(value = "评价时间")
    private Date createTime;
}
