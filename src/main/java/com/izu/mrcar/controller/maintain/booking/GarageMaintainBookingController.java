package com.izu.mrcar.controller.maintain.booking;

import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.maintain.booking.MaintainBookingOrderClientPageRespDTO;
import com.izu.asset.dto.maintain.booking.MaintainBookingOrderGaragePageReqDTO;
import com.izu.asset.dto.maintain.booking.MaintainBookingOrderGaragePageRespDTO;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.maintain.GarageMaintainDataPermUtil;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
* @Description: 维修厂端-维保预约接口
* @author: hxc
* @Date: 2023/3/30
**/
@RestController
@Api(tags = "维保管理")
public class GarageMaintainBookingController {

    private final MrCarAssetRestLocator mrCarAssetRestLocator = new MrCarAssetRestLocator();

    @PostMapping(MrCarAssetRestCenter.CLIENT_GARAGE_MAINTAIN_BOOKING_GET_PAGE_LIST)
    @RequestFunction(functionName = "维保预约单分页列表-维修厂端")
    @ApiOperation(value = "维保预约单分页列表（维修厂端）",notes = "作者：贺新春")
    public RestResponse<PageDTO<MaintainBookingOrderGaragePageRespDTO>> getPageList(@RequestBody MaintainBookingOrderGaragePageReqDTO param){

        param.setGarageNos(GarageMaintainDataPermUtil.getDatePermGarageScope(param.getGarageNos()));
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CLIENT_GARAGE_MAINTAIN_BOOKING_GET_PAGE_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, MaintainBookingOrderGaragePageRespDTO.class);
    }

    @PostMapping(MrCarAssetRestCenter.CLIENT_GARAGE_MAINTAIN_BOOKING_EXPORT)
    @RequestFunction(functionName = "维保预约单导出-维修厂端")
    @ApiOperation(value = "维保预约单导出（维修厂端）",notes = "作者：贺新春")
    @ExportExcelWeb(fileName = ExportExcelConstants.FILE_NAME_MAINTAIN_BOOKING_ORDER, filePath = ExportExcelConstants.FILE_PATH,
            sheet = ExportExcelConstants.SHEET_NAME_MAINTAIN_BOOKING_ORDER, c = GarageMaintainBookingOrderExportDTO.class)
    public PageDTO<GarageMaintainBookingOrderExportDTO> exportForGarage(@RequestBody MaintainBookingOrderGaragePageReqDTO param, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response){

        Set<String> datePermGarageScope = GarageMaintainDataPermUtil.getDatePermGarageScope(param.getGarageNos());
        param.setGarageNos(datePermGarageScope);
        ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
        izuEasyExcelSession.setUserName(clientLoginInfo.getBaseInfo().getStaffName());
        param.setPage(izuEasyExcelSession.getPageNo());
        param.setPageSize(ExportExcelConstants.PAGE_SIZE);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        String url = mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.CLIENT_GARAGE_MAINTAIN_BOOKING_GET_PAGE_LIST);
        RestResponse<PageDTO<MaintainBookingOrderClientPageRespDTO>> restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null,MaintainBookingOrderGaragePageRespDTO.class);
        if(restResponse != null && restResponse.isSuccess()){
            PageDTO<MaintainBookingOrderClientPageRespDTO> data = restResponse.getData();
            if (data.getTotal()>10000){
                throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
            }
            List<GarageMaintainBookingOrderExportDTO> exportDTOList = BeanUtil.copyList(data.getResult(), GarageMaintainBookingOrderExportDTO.class);
            return new PageDTO(data.getPage(), data.getPageSize(), data.getTotal(), exportDTOList);
        }
        return null;
    }
}
