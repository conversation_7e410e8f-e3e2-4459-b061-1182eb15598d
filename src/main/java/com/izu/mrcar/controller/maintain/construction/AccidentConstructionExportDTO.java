package com.izu.mrcar.controller.maintain.construction;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("维保/事故施工单")
public class AccidentConstructionExportDTO {

    @ExcelProperty(value = "事故单号")
    @ColumnWidth(value = 20)
    private String constructionNo;

    @ExcelProperty(value = "工单状态")
    @ColumnWidth(value = 20)
    private String constructionStatusAllStr;

    @ExcelProperty(value = "车牌号")
    @ColumnWidth(value = 20)
    private String vehicleLicense;

    @ExcelProperty(value = "车架号")
    @ColumnWidth(value = 20)
    private String vehicleVin;

    @ExcelProperty(value = "车辆归属")
    @ColumnWidth(value = 20)
    private String selfOwnedStr;

    @ExcelProperty(value = "车辆品牌")
    @ColumnWidth(value = 20)
    private String vehicleBrandName;

    @ExcelProperty(value = "车型")
    @ColumnWidth(value = 20)
    private String vehicleModelName;

    @ExcelProperty(value = "创建时间")
    @ColumnWidth(value = 20)
    private Date createTime;

    @ExcelProperty(value = "送修人")
    @ColumnWidth(value = 20)
    private String reportRepairName;

    @ExcelProperty(value = "送修人电话")
    @ColumnWidth(value = 20)
    private String reportRepairPhone;

    @ExcelProperty(value = "到厂时里程")
    @ColumnWidth(value = 20)
    private Integer receptionFactoryInMileage;

    @ExcelProperty(value = "到厂时间")
    @ColumnWidth(value = 20)
    private Date constructionInTime;

    @ExcelProperty(value = "交强险报案号")
    @ColumnWidth(value = 20)
    private String trafficInsuranceReportNo;

    @ExcelProperty(value = "商业险报案号")
    @ColumnWidth(value = 20)
    private String commercialConstructionReportNo;

    @ExcelProperty(value = "维修城市")
    @ColumnWidth(value = 20)
    private String constructionCityName;

    @ExcelProperty(value = "维修供应商")
    @ColumnWidth(value = 20)
    private String constructionSupplierTypeStr;

    @ExcelProperty(value = "维修厂名称")
    @ColumnWidth(value = 20)
    private String constructionGarageName;

    @ExcelProperty(value = "维修名称")
    @ColumnWidth(value = 60)
    private String constructionDetail;

    @ExcelProperty(value = "定损金额(元)")
    @ColumnWidth(value = 20)
    private BigDecimal contructionDiscountFeeAmount;

    @ExcelProperty(value = "代垫金额（元）")
    @ColumnWidth(value = 20)
    private BigDecimal accidentMatFee;

    @ExcelProperty(value = "完成时里程")
    @ColumnWidth(value = 20)
    private Integer constructionEndMileage;

    @ExcelProperty(value = "维修完成时间")
    @ColumnWidth(value = 20)
    private Date constructionEndTime;

    @ExcelProperty(value = "出厂接车人姓名")
    @ColumnWidth(value = 20)
    private String receptionFactoryOutName;

    @ExcelProperty(value = "出厂接车人电话")
    @ColumnWidth(value = 20)
    private String receptionFactoryOutPhone;

    @ExcelProperty(value = "材料是否已提交")
    @ColumnWidth(value = 20)
    private String materialsSubmitedStr;

    @ExcelProperty(value = "出厂时间")
    @ColumnWidth(value = 20)
    private Date constructionOutTime;

    @ExcelProperty(value = "客户企业")
    @ColumnWidth(value = 20)
    private String companyName;

    @ExcelProperty(value = "支付方式")
    @ColumnWidth(value = 20)
    private String paymentTypeStr;

    @ExcelProperty(value = "是否托管审批")
    @ColumnWidth(value = 20)
    private String isHostingStr;
}

