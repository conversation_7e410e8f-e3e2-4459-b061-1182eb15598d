package com.izu.mrcar.controller.maintain.part;

import com.alibaba.fastjson.JSON;
import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.maintain.part.input.client.MaintainPartBatchCreateDTO;
import com.izu.asset.dto.maintain.part.input.client.MaintainPartBatchInputDTO;
import com.izu.asset.dto.maintain.part.input.client.MaintainPartCategoryInputDTO;
import com.izu.asset.dto.maintain.part.input.garage.MaintainGaragePartBatchInputDTO;
import com.izu.asset.dto.maintain.part.output.client.*;
import com.izu.asset.dto.maintain.part.output.garage.MaintainGaragePartBatchListDTO;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.framework.web.util.SequenceUtil;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.cache.SessionRedisSentinelCache;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.controller.AbstractExcelUploadController;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.mrcar.utils.maintain.GarageMaintainDataPermUtil;
import com.izu.user.dto.ImportErrorDTO;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@Api(tags = "维保管理")
public class MaintainPartBatchController {

    private static final Logger logger = LoggerFactory.getLogger(MaintainPartBatchController.class);
    @Autowired
    private SessionRedisSentinelCache sessionRedisSentinelCache;
    private static final int       EXCEL_CHECK_ERRORS_CACHE_TIMEOUT = 6*60*60;//缓存时效

    @PostMapping(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_BATCH_LIST)
    @RequestFunction(functionName = "入库记录列表-客户端")
    @ApiOperation(value = "入库记录列表（客户端）",notes = "作者：mapp")
    public RestResponse<PageDTO<MaintainPartBatchListDTO>> getCilentList(@RequestBody MaintainPartBatchInputDTO param){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_BATCH_LIST);
        param.setLoginCompanyCode(loginBaseInfo.obtainBelongCompanyCode());
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, MaintainPartBatchListDTO.class);
    }

    @PostMapping(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_BATCH_DETAIL)
    @RequestFunction(functionName = "入库记录明细-客户端")
    @ApiOperation(value = "入库记录明细（客户端）",notes = "作者：mapp")
    @ApiImplicitParam(name = "batchNo", value = "批次编号", required = true)
    public RestResponse<MaintainPartBatchDetailDTO> getClientDetail(String batchNo){
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_BATCH_DETAIL);
        Map<String,Object> param = new HashMap<>();
        param.put("batchNo",batchNo);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, url, param, null, MaintainPartBatchDetailDTO.class);
    }

    @PostMapping(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_BATCH_SAVE)
    @RequestFunction(functionName = "入库新建保存")
    @ApiOperation(value = "入库新建保存",notes = "作者：mapp")
    public RestResponse save(@RequestBody MaintainPartBatchCreateDTO param) {
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_BATCH_SAVE);
        param.setLoginCompanyCode(loginBaseInfo.obtainBelongCompanyCode());
        param.setLoginCompanyName(loginBaseInfo.obtainBelongCompanyName());
        param.setLoginUserId(loginBaseInfo.obtainBaseInfo().getStaffId());
        param.setLoginUserName(loginBaseInfo.obtainBaseInfo().getStaffName());
        param.setLoginUserPhone(loginBaseInfo.obtainBaseInfo().getMobile());
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, Boolean.class);
    }

    @PostMapping(MrCarAssetRestCenter.GARAGE_MAINTAIN_PART_BATCH_LIST)
    @RequestFunction(functionName = "入库记录列表-维修厂端")
    @ApiOperation(value = "入库记录列表（维修厂端）",notes = "作者：mapp")
    public RestResponse<PageDTO<MaintainGaragePartBatchListDTO>> getGarageList(@RequestBody MaintainGaragePartBatchInputDTO param){
        param.setCompanyCodes(GarageMaintainDataPermUtil.getDatePermGarageScope(param.getCompanyCodes()));
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.GARAGE_MAINTAIN_PART_BATCH_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, MaintainGaragePartBatchListDTO.class);
    }

    @PostMapping(MrCarAssetRestCenter.GARAGE_MAINTAIN_PART_BATCH_DETAIL)
    @RequestFunction(functionName = "入库记录明细-维修厂端")
    @ApiOperation(value = "入库记录明细（维修厂端）",notes = "作者：mapp")
    @ApiImplicitParam(name = "batchNo", value = "批次编号", required = true)
    public RestResponse<MaintainPartBatchDetailDTO> getGarageDetail(String batchNo){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_BATCH_DETAIL);
        Map<String,Object> param = new HashMap<>();
        param.put("batchNo",batchNo);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, url, param, null, MaintainPartBatchDetailDTO.class);
    }

    @PostMapping(MrCarAssetRestCenter.MAINTAIN_BATCH_DETAIL_PART_LIST_EXPORT)
    @RequestFunction(functionName = "入库详情中配件列表信息导出")
    @ApiOperation(value = "入库详情中配件列表信息导出")
    @ApiImplicitParam(name = "batchNo", value = "批次编号", required = true)
    @ExportExcelWeb(fileName = ExportExcelConstants.FILE_NAME_MAINTAIN_BATCH_PART_INFO,filePath = ExportExcelConstants.FILE_PATH,
            sheet = ExportExcelConstants.SHEET_NAME_MAINTAIN_BATCH_PART_INFO, c =MaintainPartBatchExport.class)
    public PageDTO<MaintainPartBatchExport> exportPartInfo(String batchNo, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response){
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_BATCH_DETAIL);
        Map<String, Object> param = new HashMap<>();
        param.put("batchNo",batchNo);
        RestResponse restResponse = RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, url, param, null, MaintainPartBatchDetailDTO.class);
        List<MaintainPartBatchExport> excelList = new ArrayList<>();
        if(restResponse!=null && restResponse.isSuccess()){
            MaintainPartBatchDetailDTO maintainPartBatchDetailDTO = (MaintainPartBatchDetailDTO) restResponse.getData();
            if(maintainPartBatchDetailDTO != null) {
                if(CollectionUtils.isNotEmpty(maintainPartBatchDetailDTO.getMaintainPartInventoryListDTOList()) &&
                        maintainPartBatchDetailDTO.getMaintainPartInventoryListDTOList().size() > 10000){
                    throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
                }
                List<MaintainPartInventoryListDTO> restList = maintainPartBatchDetailDTO.getMaintainPartInventoryListDTOList();
                excelList = BeanUtil.copyList(restList,MaintainPartBatchExport.class);
            }
        }
        return new PageDTO<>(1,10000,excelList.size(),excelList);
    }

    @PostMapping("/download/partInventoryTemplate")
    @ApiOperation("导入配件库存模板下载")
    @RequestFunction(functionName = "导入配件库存模板下载")
    public void download(HttpServletRequest request, HttpServletResponse response) {
        String templatePath = "template/part_inventory_template.xlsx";
        String fileName = "配件库存导入模板.xlsx";
        Resource resource = new ClassPathResource(templatePath);
        try (InputStream is = resource.getInputStream(); XSSFWorkbook workbook = new XSSFWorkbook(is)) {
            ZipSecureFile.setMinInflateRatio(-1.0d);
            fileName = URLEncoder.encode(fileName, "UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename*=utf-8''" + fileName);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=gbk");
            response.setCharacterEncoding("gbk");
            XSSFSheet carModelSheet = workbook.getSheetAt(1);
            setCarModelList(carModelSheet);
            workbook.write(response.getOutputStream());
        }  catch (IOException e) {
            logger.error("配件库存导入模板下载失败", e);
        }
    }

    public void setCarModelList(XSSFSheet sheet) {
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        MaintainPartCategoryInputDTO param = new MaintainPartCategoryInputDTO();
        param.setPage(1);
        param.setPageSize(Integer.MAX_VALUE);
        param.setLoginCompanyCode(loginBaseInfo.obtainBelongCompanyCode());
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_LIST);
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, MaintainPartListDTO.class);
        List<MaintainPartListDTO> resList= new ArrayList<>();
        if(restResponse != null && restResponse.isSuccess()){
            PageDTO pageDTO = (PageDTO)restResponse.getData();
            resList = (List<MaintainPartListDTO>)pageDTO.getResult() ;
        }
        if(CollectionUtils.isNotEmpty(resList)){
            for (int i = 0; i < resList.size(); i++) {
                MaintainPartListDTO MaintainPartListDTO = resList.get(i);
                XSSFRow row = sheet.getRow(i + 1);
                if (row == null) {
                    row = sheet.createRow(i + 1);
                }
                XSSFCell cell0 = row.createCell(0);
                cell0.setCellValue(MaintainPartListDTO.getPartName());
                XSSFCell cell1 = row.createCell(1);
                cell1.setCellValue(MaintainPartListDTO.getPartNo());
            }
        }
    }
}
