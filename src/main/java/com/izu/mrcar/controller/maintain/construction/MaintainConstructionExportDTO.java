package com.izu.mrcar.controller.maintain.construction;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel("维保/事故施工单")
public class MaintainConstructionExportDTO {

    @ExcelProperty(value = "维保单号")
    @ColumnWidth(value = 20)
    private String constructionNo;

    @ExcelProperty(value = "工单状态")
    @ColumnWidth(value = 20)
    private String constructionStatusAllStr;

    @ExcelProperty(value = "车牌号")
    @ColumnWidth(value = 20)
    private String vehicleLicense;

    @ExcelProperty(value = "车架号")
    @ColumnWidth(value = 20)
    private String vehicleVin;

    @ExcelProperty(value = "维保费用承担方")
    @ColumnWidth(value = 20)
    private String undertakerCostCompanyName;

    @ExcelProperty(value = "车辆品牌")
    @ColumnWidth(value = 10)
    private String vehicleBrandName;

    @ExcelProperty(value = "车型")
    @ColumnWidth(value = 10)
    private String vehicleModelName;

    @ExcelProperty(value = "施工类型")
    @ColumnWidth(value = 20)
    private String constructionTypeStr;

    @ExcelProperty(value = "故障主诉")
    @ColumnWidth(value = 20)
    private String reportDescription;

    @ExcelProperty(value = "送修人")
    @ColumnWidth(value = 20)
    private String reportRepairName;

    @ExcelProperty(value = "送修人电话")
    @ColumnWidth(value = 20)
    private String reportRepairPhone;

    @ExcelProperty(value = "到厂里程")
    @ColumnWidth(value = 20)
    private Integer receptionFactoryInMileage;

    @ExcelProperty(value = "到厂时间")
    @ColumnWidth(value = 20)
    private Date constructionInTime;

    @ExcelProperty(value = "维修城市")
    @ColumnWidth(value = 20)
    private String constructionCityName;

    @ExcelProperty(value = "维修供应商")
    @ColumnWidth(value = 20)
    private String constructionSupplierTypeStr;

    @ExcelProperty(value = "维修厂名称")
    @ColumnWidth(value = 20)
    private String constructionGarageName;

    @ExcelProperty(value = "发票类型")
    @ColumnWidth(value = 20)
    private String contructionInvoiceTypeStr;

    @ExcelProperty(value = "税率")
    private BigDecimal contructionInvoiceRate;

    @ExcelProperty(value = "维修明细")
    @ColumnWidth(value = 60)
    private String constructionDetail;

//    @ExcelProperty(value = "实收工时费(元)")
//    @ColumnWidth(value = 20)
//    private BigDecimal workTimeAmount;
//
//    @ExcelProperty(value = "实收材料费（元）")
//    @ColumnWidth(value = 20)
//    private BigDecimal materialAmount;
//
//    @ExcelProperty(value = "实收合计（元）")
//    @ColumnWidth(value = 20)
//    private BigDecimal contructionTotalFeeAmount;
//
//    @ExcelProperty(value = "维修总价（元）")
//    @ColumnWidth(value = 20)
//    private BigDecimal repairTotalAmount;
//
//    @ExcelProperty(value = "不含税总价（元）")
//    @ColumnWidth(value = 20)
//    private BigDecimal contructionAfterTaxFeeAmount;

    @ExcelProperty(value = "实收工时费(元)")
    @ColumnWidth(value = 20)
    private String workTimeAmountStr;

    @ExcelProperty(value = "实收材料费（元）")
    @ColumnWidth(value = 20)
    private String materialAmountStr;

    @ExcelProperty(value = "实收合计（元）")
    @ColumnWidth(value = 20)
    private String contructionTotalFeeAmountStr;

    @ExcelProperty(value = "维修总价（元）")
    @ColumnWidth(value = 20)
    private String repairTotalAmountStr;

    @ExcelProperty(value = "不含税总价（元）")
    @ColumnWidth(value = 20)
    private String contructionAfterTaxFeeAmountStr;

    @ExcelProperty(value = "维修开始时间")
    @ColumnWidth(value = 20)
    private Date constructionStartTime;

    @ExcelProperty(value = "维修完成时间")
    @ColumnWidth(value = 20)
    private Date constructionEndTime;

    @ExcelProperty(value = "出厂时里程")
    @ColumnWidth(value = 20)
    private Integer receptionFactoryOutMileage;

    @ExcelProperty(value = "出厂时间")
    @ColumnWidth(value = 20)
    private Date constructionOutTime;

    @ExcelProperty(value = "客户企业")
    @ColumnWidth(value = 20)
    private String companyName;

    @ExcelProperty(value = "支付方式")
    @ColumnWidth(value = 20)
    private String paymentTypeStr;

    @ExcelProperty(value = "是否托管审批")
    @ColumnWidth(value = 20)
    private String isHostingStr;

    @ExcelProperty(value = "建单方式")
    @ColumnWidth(value = 20)
    private String creationTypeStr;

    @ExcelProperty(value = "单据来源")
    @ColumnWidth(value = 20)
    private String constructionSourceStr;
}

