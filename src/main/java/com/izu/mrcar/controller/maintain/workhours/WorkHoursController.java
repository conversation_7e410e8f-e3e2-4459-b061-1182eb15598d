package com.izu.mrcar.controller.maintain.workhours;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.alibaba.excel.write.style.row.SimpleRowHeightStyleStrategy;
import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.consts.maintain.workhours.MaintainGroupEnum;
import com.izu.asset.dto.common.BaseDTO;
import com.izu.asset.dto.maintain.workHour.*;
import com.izu.asset.errcode.MrCarAssetErrorCode;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.errorcode.ErrorCode;
import com.izu.mrcar.utils.excel.SelectDataSheetWriteHandler;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.*;

/**
 * <AUTHOR>
 * @description: 工时
 * @date 2023/4/23 17:46
 */
@RestController
@Api(tags = "维保管理")
public class WorkHoursController {

    private final MrCarAssetRestLocator mrCarAssetRestLocator = new MrCarAssetRestLocator();
    @PostMapping(MrCarAssetRestCenter.WORK_HOURS_TEMPLATE_LIST)
    @ApiOperation(value = "查询工时模板下拉列表",notes = "作者:郝彬杰")
    public RestResponse<MaintainWorkTemplateDTO> getWorkTemplateList(@RequestBody MaintainWorkTemplateReqDTO maintainWorkTemplateReqDTO){
        Map<String, Object> paramMap = new HashMap<>();
        maintainWorkTemplateReqDTO.setState(new Byte("1"));
        ClientLoginInfo clientLoginInfo= LoginSessionUtil.getClientLoginInfo();
        maintainWorkTemplateReqDTO.setCompanyCode(clientLoginInfo.getClientCompany().getCompanyCode());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, maintainWorkTemplateReqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY,  mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.WORK_HOURS_TEMPLATE_LIST), paramMap, null);
    }

    @PostMapping(MrCarAssetRestCenter.WORK_BASE_LIST)
    @ApiOperation(value = "合作关系-工时基数",notes = "作者：郝彬杰")
    public RestResponse<PageDTO<WorkBaseDTO>> getWorkBaseDetail(@RequestBody WorkTemplateQueryDTO workTemplateQueryDTO){
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, workTemplateQueryDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY,  mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.WORK_BASE_LIST), paramMap, null);
    }

    @PostMapping(MrCarAssetRestCenter.WORK_FACTOR_LIST)
    @ApiOperation(value = "合作关系-工时系数",notes = "作者：郝彬杰")
    public RestResponse<PageDTO<WorkFactorDTO>> getWorkFactorDetail(@RequestBody WorkTemplateQueryDTO workTemplateQueryDTO){
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, workTemplateQueryDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY,  mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.WORK_FACTOR_LIST), paramMap, null);
    }

    //创建编辑工时模板
    @PostMapping(MrCarAssetRestCenter.WORK_HOURS_TEMPLATE_SAVE)
    @ApiOperation(value = "保存工时模板",notes = "作者：郝彬杰")
    public RestResponse saveWorkTemplate(@RequestBody MaintainWorkTemplateSaveDTO maintainWorkTemplateSaveDTO){
        Map<String, Object> paramMap = new HashMap<>();
        buildLoginInfo(maintainWorkTemplateSaveDTO);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, maintainWorkTemplateSaveDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY,  mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.WORK_HOURS_TEMPLATE_SAVE), paramMap, null);
    }

    @PostMapping(MrCarAssetRestCenter.WORK_HOURS_TEMPLATE_DELETE)
    @ApiOperation(value = "删除工时模板",notes = "作者：郝彬杰")
    public RestResponse deleteWorkTemplate(@RequestBody MaintainWorkTemplateSaveDTO maintainWorkTemplateSaveDTO){
        Map<String, Object> paramMap = new HashMap<>();
        buildLoginInfo(maintainWorkTemplateSaveDTO);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, maintainWorkTemplateSaveDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY,  mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.WORK_HOURS_TEMPLATE_DELETE), paramMap, null);
    }
    //查询模板列表
    @ApiOperation(value = "查询工时模板列表",notes = "作者：郝彬杰")
    @PostMapping(MrCarAssetRestCenter.WORK_HOURS_TEMPLATE_QUERY)
    public RestResponse<MaintainWorkTemplateDTO> getWorkTemplate(@RequestBody MaintainWorkTemplateReqDTO maintainWorkTemplateReqDTO){
        Map<String, Object> paramMap = new HashMap<>();
        buildLoginInfo(maintainWorkTemplateReqDTO);
        maintainWorkTemplateReqDTO.setState((byte)1);
        maintainWorkTemplateReqDTO.setCompanyCode(maintainWorkTemplateReqDTO.getLoginCompanyCode());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, maintainWorkTemplateReqDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY,  mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.WORK_HOURS_TEMPLATE_QUERY), paramMap, null);

    }
    //工时系数保存 查询 详情 启用
    @PostMapping(MrCarAssetRestCenter.WORK_HOURS_BASE_SAVE)
    @ApiOperation(value = "保存工时基数",notes = "作者：郝彬杰")
    public RestResponse saveWorkBase(@RequestBody WorkBaseSaveDTO workBaseSaveDTO){
        Map<String, Object> paramMap = new HashMap<>();
        buildLoginInfo(workBaseSaveDTO);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, workBaseSaveDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY,  mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.WORK_HOURS_BASE_SAVE), paramMap, null);

    }
    @PostMapping(MrCarAssetRestCenter.WORK_HOURS_BASE_LIST)
    @ApiOperation(value = "查询工时基数列表",notes = "作者：郝彬杰")
    public RestResponse<WorkBaseDTO> getWorkBase(@RequestBody WorkBaseQueryDTO workBaseQueryDTO){
        Map<String, Object> paramMap = new HashMap<>();
        buildLoginInfo(workBaseQueryDTO);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, workBaseQueryDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY,  mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.WORK_HOURS_BASE_LIST), paramMap, null);
    }
    @PostMapping(MrCarAssetRestCenter.WORK_HOURS_BASE_DETAIL)
    @ApiOperation(value = "查询工时基数详情",notes = "作者：郝彬杰")
    public RestResponse<MaintainWorkBaseConfigDTO> getWorkBaseDetail(Integer templateId){
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("templateId", templateId);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST,  mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.WORK_HOURS_BASE_DETAIL), paramMap, null);
    }
    @PostMapping(MrCarAssetRestCenter.WORK_HOURS_BASE_STATUS)
    @ApiOperation(value = "启用禁用工时基数",notes = "作者：郝彬杰")
    public RestResponse editWorkBase(@RequestBody WorkBaseSaveDTO workBaseSaveDTO){
        Map<String, Object> paramMap = new HashMap<>();
        buildLoginInfo(workBaseSaveDTO);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, workBaseSaveDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY,  mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.WORK_HOURS_BASE_STATUS), paramMap, null);

    }

    //工时基数保存 查询 详情 启用

    @PostMapping(MrCarAssetRestCenter.WORK_HOURS_FACTOR_SAVE)
    @ApiOperation(value = "保存工时系数",notes = "作者：郝彬杰")
    public RestResponse saveWorkFactor(@RequestBody WorkFactorSaveDTO workFactorSaveDTO){
        Map<String, Object> paramMap = new HashMap<>();
        buildLoginInfo(workFactorSaveDTO);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, workFactorSaveDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY,  mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.WORK_HOURS_FACTOR_SAVE), paramMap, null);

    }

    @PostMapping(MrCarAssetRestCenter.WORK_HOURS_FACTOR_LIST)
    @ApiOperation(value = "查询工时系数列表",notes = "作者：郝彬杰")
    public RestResponse<WorkFactorDTO> getWorkFactor(@RequestBody WorkFactorQueryDTO workFactorQueryDTO){
        Map<String, Object> paramMap = new HashMap<>();
        buildLoginInfo(workFactorQueryDTO);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, workFactorQueryDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY,  mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.WORK_HOURS_FACTOR_LIST), paramMap, null);

    }

    @PostMapping(MrCarAssetRestCenter.WORK_HOURS_FACTOR_DETAIL)
    @ApiOperation(value = "查询工时系数详情",notes = "作者：郝彬杰")
    public RestResponse<MaintainWorkFactorConfigDTO> getWorkFactorDetail(Integer templateId){
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("templateId", templateId);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POST,  mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.WORK_HOURS_FACTOR_DETAIL), paramMap, null);
    }

    @PostMapping(MrCarAssetRestCenter.WORK_HOURS_FACTOR_STATUS)
    @ApiOperation(value = "启用禁用工时系数",notes = "作者：郝彬杰")
    public RestResponse editWorkFactor(@RequestBody WorkFactorSaveDTO workFactorSaveDTO){

        Map<String, Object> paramMap = new HashMap<>();
        buildLoginInfo(workFactorSaveDTO);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, workFactorSaveDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY,  mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.WORK_HOURS_FACTOR_STATUS), paramMap, null);

    }

    private void buildLoginInfo(BaseDTO baseDTO){
        ClientLoginInfo clientLoginInfo= LoginSessionUtil.getClientLoginInfo();
        baseDTO.setLoginUserId(clientLoginInfo.getBaseInfo().getStaffId());
        baseDTO.setLoginUserName(clientLoginInfo.getBaseInfo().getStaffName());
        baseDTO.setLoginCompanyCode(clientLoginInfo.getClientCompany().getCompanyCode());
        baseDTO.setLoginCompanyName(clientLoginInfo.getClientCompany().getCompanyName());
    }


    @RequestMapping(MrCarAssetRestCenter.WORK_HOURS_REPAIR_NAME)
    @ApiOperation(value = "查询维修名称",notes = "作者：郝彬杰")
    public RestResponse<WorkBaseDTO> getWorkRepairName( ){
        WorkBaseQueryDTO workBaseQueryDTO=new WorkBaseQueryDTO();
        Map<String, Object> paramMap = new HashMap<>();
        ClientLoginInfo clientLoginInfo=LoginSessionUtil.getClientLoginInfo();
        String companyCode=clientLoginInfo.getClientCompany().getCompanyCode();
        workBaseQueryDTO.setLoginCompanyCode(companyCode);
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY,workBaseQueryDTO);
        //String companyCode="FUS2023010100001";
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY,  mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.WORK_HOURS_REPAIR_NAME), paramMap, null);
    }

    @PostMapping(MrCarAssetRestCenter.WORK_FACTOR_EXPORT)
    @ApiOperation(value = "导出工时系数",notes = "作者：郝彬杰")
    @ExportExcelWeb(fileName = "导出工时系数数据.xlsx",filePath = "/data/logs/izubackground/tmp",sheet = "导出工时系数数据",
            c = WorkFactorExportDTO.class
    )
    public PageDTO exportWorkFactor(@RequestBody WorkFactorQueryDTO workFactorQueryDTO, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response){

        Map<String, Object> paramMap = new HashMap<>();
        workFactorQueryDTO.setPageSize(10000);
        ClientLoginInfo clientLoginInfo=LoginSessionUtil.getClientLoginInfo();
        izuEasyExcelSession.setUserName(clientLoginInfo.getBaseInfo().getStaffName());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, workFactorQueryDTO);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.WORK_HOURS_FACTOR_LIST), paramMap, null, WorkFactorExportDTO.class);
        if(restResponse!=null && restResponse.isSuccess()) {
            PageDTO pageDTO = (PageDTO) restResponse.getData();
            if (pageDTO.getTotal()>10000){
                throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
            }
            return pageDTO;
        }else{
            return null;
        }

    }

    @PostMapping(MrCarAssetRestCenter.WORK_FACTOR_IMPORT)
    @ApiOperation(value = "导入工时系数列表",notes = "作者：郝彬杰")
    public RestResponse importWorkFactor( WorkHourImportDTO workHourImportDTO){

        Map<String, Object> paramMap = new HashMap<>();
        ClientLoginInfo clientLoginInfo=LoginSessionUtil.getClientLoginInfo();
        workHourImportDTO.setLoginUserId(clientLoginInfo.getBaseInfo().getStaffId());
        workHourImportDTO.setLoginUserName(clientLoginInfo.getBaseInfo().getStaffName());
        workHourImportDTO.setLoginCompanyCode(clientLoginInfo.getClientCompany().getCompanyCode());
        workHourImportDTO.setLoginCompanyName(clientLoginInfo.getClientCompany().getCompanyName());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, workHourImportDTO);
        RestResponse restResponse= RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY,  mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.WORK_FACTOR_IMPORT), paramMap, null);

        if(Objects.equals(restResponse.getCode(),500)){
           return RestResponse.create(MrCarAssetErrorCode.BUSSINESS_SUBMIT_APPROVAL_ERROR,"导入失败请检查数据",false,null);
        }
        return restResponse;
    }


    @PostMapping(MrCarAssetRestCenter.WORK_BASE_EXPORT)
    @ApiOperation(value = "导出工时基数列表",notes = "作者：郝彬杰")
    @ExportExcelWeb(fileName = "工时基数数据.xlsx",filePath = "/data/logs/izubackground/tmp",sheet = "工时基数数据",
            c = WorkBaseExportDTO.class
    )
    public PageDTO exportWorkBase(@RequestBody WorkBaseQueryDTO workBaseQueryDTO,IzuEasyExcelSession izuEasyExcelSession,HttpServletRequest request, HttpServletResponse response){
        Map<String, Object> paramMap = new HashMap<>();
        workBaseQueryDTO.setPageSize(10000);
        ClientLoginInfo clientLoginInfo=LoginSessionUtil.getClientLoginInfo();
        izuEasyExcelSession.setUserName(clientLoginInfo.getBaseInfo().getStaffName());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, workBaseQueryDTO);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.WORK_HOURS_BASE_LIST), paramMap, null, WorkBaseExportDTO.class);
        if(restResponse!=null && restResponse.isSuccess()) {
            PageDTO pageDTO = (PageDTO) restResponse.getData();
            if (pageDTO.getTotal()>10000){
                throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
            }
            return pageDTO;
        }else{
            return null;
        }
    }

    @PostMapping(MrCarAssetRestCenter.WORK_BASE_IMPORT)
    @ApiOperation(value = "导入工时基数",notes = "作者：郝彬杰")
    public RestResponse importWorkBase(WorkHourImportDTO workHourImportDTO){
        Map<String, Object> paramMap = new HashMap<>();
        ClientLoginInfo clientLoginInfo=LoginSessionUtil.getClientLoginInfo();
        workHourImportDTO.setLoginUserId(clientLoginInfo.getBaseInfo().getStaffId());
        workHourImportDTO.setLoginUserName(clientLoginInfo.getBaseInfo().getStaffName());
        workHourImportDTO.setLoginCompanyCode(clientLoginInfo.getClientCompany().getCompanyCode());
        workHourImportDTO.setLoginCompanyName(clientLoginInfo.getClientCompany().getCompanyName());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, workHourImportDTO);
        return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY,  mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.WORK_BASE_IMPORT), paramMap, null);
    }



    @GetMapping(MrCarAssetRestCenter.WORK_BASE_DOWNLOAD)
    @RequestFunction(functionName = "下载工时基数模板")
    @ApiOperation("下载工时基数模板")
    public void downloadBaseMould(HttpServletRequest request, HttpServletResponse response) throws Exception {

        //导出模板名称
        String fileName = "工时基数模板excel";
        response.setContentType("application/msexcel");
        response.setCharacterEncoding("utf-8");
        //这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        String fileNameEncode = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition",  "attachment;filename*=utf-8''"  + fileNameEncode + ExcelTypeEnum.XLSX.getValue());
        List<WorkBaseImportDTO> list=new ArrayList<>();
        //设置下拉框内容
        Map<Integer, List<String>> selectMap = buildBaseHeadList();
        EasyExcelFactory.write(response.getOutputStream())
                .registerWriteHandler(new SelectDataSheetWriteHandler(selectMap))//设置字典
                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(20))//设置列宽度
                .registerWriteHandler(new SimpleRowHeightStyleStrategy((short) 28, (short) 18))//设置行高度
                .head(WorkBaseImportDTO.class)//此处对应的是实体类
                .excelType(ExcelTypeEnum.XLSX)//设置导出格式为xls后缀
                .sheet("工时基数模板")
                .doWrite(list);
    }

    private Map<Integer, List<String>> buildBaseHeadList(){
        String[] group= Arrays.asList(MaintainGroupEnum.values()).stream().map(MaintainGroupEnum::getMessage).toArray(String[]::new);
        String[] oftenUse={"否","是"};
        String[] status={"启用","禁用"};
        Map<Integer, List<String>> selectMap = new HashMap<>();
        List<String> list2 = Arrays.asList(group);
        selectMap.put(1, list2);

        selectMap.put(3, Arrays.asList(oftenUse));

        selectMap.put(4, Arrays.asList(oftenUse));

        selectMap.put(7, Arrays.asList(status));
        return selectMap;
    }

    @GetMapping(MrCarAssetRestCenter.WORK_FACTOR_DOWNLOAD)
    @RequestFunction(functionName = "下载工时系数模板")
    @ApiOperation("下载工时系数模板")
    public void downloadFactorMould(HttpServletRequest request, HttpServletResponse response) throws Exception {

        //导出模板名称
        String fileName = "导入工时系数模板";
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        //这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
        String fileNameEncode = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''"  + fileNameEncode + ExcelTypeEnum.XLS.getValue());
        List<WorkFactorImportDTO> list=new ArrayList<>();
        EasyExcelFactory.write(response.getOutputStream())
                .registerWriteHandler(new SelectDataSheetWriteHandlerV2())//设置字典
                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(30))//设置列宽度
                .registerWriteHandler(new SimpleRowHeightStyleStrategy((short) 28, (short) 18))//设置行高度
                .head(WorkFactorImportDTO.class)//此处对应的是实体类
                .excelType(ExcelTypeEnum.XLS)//设置导出格式为xls后缀
                .sheet("工时系数")
                .doWrite(list);

    }


    @PostMapping(MrCarAssetRestCenter.GARAGE_WORK_BASE_EXPORT)
    @ApiOperation(value = "导出工时基数列表",notes = "作者：郝彬杰")
    @ExportExcelWeb(fileName = "工时基数数据.xlsx",filePath = "/data/logs/excel/tmp",sheet = "工时基数数据",
            c = WorkBaseExportDTO.class
    )
    public PageDTO exportWorkBase(@RequestBody WorkTemplateQueryDTO workBaseQueryDTO, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response){
        Map<String, Object> paramMap = new HashMap<>();
        workBaseQueryDTO.setPageSize(10000);
        ClientLoginInfo clientLoginInfo=LoginSessionUtil.getClientLoginInfo();
        izuEasyExcelSession.setUserName(clientLoginInfo.getBaseInfo().getStaffName());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, workBaseQueryDTO);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.WORK_BASE_LIST), paramMap, null, WorkBaseExportDTO.class);
        if(restResponse!=null && restResponse.isSuccess()) {
            PageDTO pageDTO = (PageDTO) restResponse.getData();
            if (pageDTO.getTotal()>10000){
                throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
            }
            return pageDTO;
        }else{
            return null;
        }
    }

    @PostMapping(MrCarAssetRestCenter.GARAGE_WORK_FACTOR_EXPORT)
    @ApiOperation(value = "导出工时系数",notes = "作者：郝彬杰")
    @ExportExcelWeb(fileName = "导出工时系数数据.xlsx",filePath = "/data/logs/excel/tmp",sheet = "导出工时系数数据",
            c = WorkFactorExportDTO.class
    )
    public PageDTO exportWorkFactor(@RequestBody WorkTemplateQueryDTO workFactorQueryDTO, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response){

        Map<String, Object> paramMap = new HashMap<>();
        workFactorQueryDTO.setPageSize(10000);
        ClientLoginInfo clientLoginInfo=LoginSessionUtil.getClientLoginInfo();
        izuEasyExcelSession.setUserName(clientLoginInfo.getBaseInfo().getStaffName());
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, workFactorQueryDTO);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, mrCarAssetRestLocator.getRestUrl(MrCarAssetRestCenter.WORK_FACTOR_LIST), paramMap, null, WorkFactorExportDTO.class);
        if(restResponse!=null && restResponse.isSuccess()) {
            PageDTO pageDTO = (PageDTO) restResponse.getData();
            if (pageDTO.getTotal()>10000){
                throw ExceptionFactory.createRestException(ErrorCode.EXPORT_LIMITED_ERROR);
            }
            return pageDTO;
        }else{
            return null;
        }

    }

}
