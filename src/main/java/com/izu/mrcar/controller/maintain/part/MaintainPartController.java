package com.izu.mrcar.controller.maintain.part;

import com.izu.asset.MrCarAssetRestCenter;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.dto.maintain.part.input.client.MaintainPartCreateDTO;
import com.izu.asset.dto.maintain.part.input.client.MaintainPartInputDTO;
import com.izu.asset.dto.maintain.part.output.client.MaintainPartListDTO;
import com.izu.excel.annotation.ExportExcelWeb;
import com.izu.excel.dto.IzuEasyExcelSession;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.enums.role.SystemTypeEnum;
import io.swagger.annotations.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.izu.mrcar.common.constants.ExportExcelConstants;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@Api(tags = "维保管理")
public class MaintainPartController{

    private static final Logger logger = LoggerFactory.getLogger(MaintainPartController.class);

    public static final String FILE_PATH = "/data/logs/mrcar/tmp";

    @PostMapping(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_LIST)
    @RequestFunction(functionName = "配件管理列表-客户端")
    @ApiOperation(value = "配件管理列表（客户端）",notes = "作者：mapp")
    public RestResponse<PageDTO<MaintainPartListDTO>> getList(@RequestBody MaintainPartInputDTO param){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_LIST);
        param.setLoginCompanyCode(loginBaseInfo.obtainBelongCompanyCode());
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, MaintainPartListDTO.class);
    }

    @PostMapping(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_DROPDOWN_LIST)
    @RequestFunction(functionName = "配件下拉框列表")
    @ApiOperation(value = "配件下拉框列表",notes = "作者：mapp")
    public RestResponse getList(String companyCode){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_DROPDOWN_LIST);
        Map<String, Object> param = new HashMap<>();
        if(loginBaseInfo != null && loginBaseInfo.getSystemType().equals(SystemTypeEnum.CUSTOMER.getCode())) {
            param.put("companyCode", loginBaseInfo.obtainBelongCompanyCode());
        }else{
            param.put("companyCode", "");
        }
        return RestClient.requestForList(BaseHttpClient.HttpMethod.POST, url, param, null, Map.class);
    }

    @PostMapping(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_SAVE)
    @RequestFunction(functionName = "新增、编辑配件-客户端")
    @ApiOperation(value = "新增、编辑配件（客户端）",notes = "作者：mapp")
    public RestResponse save(@RequestBody MaintainPartCreateDTO param){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_SAVE);
        param.setLoginCompanyCode(loginBaseInfo.obtainBelongCompanyCode());
        param.setLoginUserId(loginBaseInfo.obtainBaseInfo().getStaffId());
        param.setLoginUserName(loginBaseInfo.obtainBaseInfo().getStaffName());
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, Boolean.class);
    }

    @PostMapping(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_ENABLE)
    @RequestFunction(functionName = "启用、禁用配件-客户端")
    @ApiOperation(value = "启用、禁用配件（客户端）",notes = "作者：mapp")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "partId", value = "配件id", required = true),
            @ApiImplicitParam(name = "state", value = "状态 4：禁用  1：启用", required = true),
    })
    public RestResponse enable(Integer partId,byte state){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_ENABLE);
        Map<String,Object> param = new HashMap<>();
        param.put("partId",partId);
        param.put("state",state);
        param.put("operateId",loginBaseInfo.obtainBaseInfo().getStaffId());
        param.put("operateName",loginBaseInfo.obtainBaseInfo().getStaffName());
        return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, url, param, null, Boolean.class);
    }

    @PostMapping(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_LIST_EXPORT)
    @RequestFunction(functionName = "配件列表导出（客户端）")
    @ApiOperation(value = "配件列表导出（客户端）")
    @ExportExcelWeb(fileName = ExportExcelConstants.FILE_NAME_MAINTAIN_PART,filePath = ExportExcelConstants.FILE_PATH,
            sheet = ExportExcelConstants.SHEET_NAME_MAINTAIN_PART, c =PartInfoExport.class)
    public PageDTO<PartInfoExport> exportPartInfo(@RequestBody MaintainPartInputDTO param, IzuEasyExcelSession izuEasyExcelSession, HttpServletRequest request, HttpServletResponse response){
        LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
        String userName = loginBaseInfo.obtainBaseInfo().getStaffName();
        param.setPage(izuEasyExcelSession.getPageNo());
        izuEasyExcelSession.setUserName(userName);
        param.setPageSize(10000);
        String url = new MrCarAssetRestLocator().getRestUrl(MrCarAssetRestCenter.CLIENT_MAINTAIN_PART_LIST);
        param.setLoginCompanyCode(loginBaseInfo.obtainBelongCompanyCode());
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put(BaseHttpClient.POSTBODY_MAP_KEY, param);
        RestResponse restResponse = RestClient.requestForPage(BaseHttpClient.HttpMethod.POSTBODY, url, paramMap, null, MaintainPartListDTO.class);
        if(restResponse!=null && restResponse.isSuccess()){
            PageDTO<MaintainPartListDTO> pageDTO = (PageDTO) restResponse.getData();
            List<PartInfoExport> exportList = new ArrayList<>();
            if(pageDTO != null) {
                List<MaintainPartListDTO> listDTOS = pageDTO.getResult();
                exportList = BeanUtil.copyList(listDTOS,PartInfoExport.class);
            }
            return new PageDTO<>(param.getPage(),param.getPageSize(),exportList.size(),exportList);
        }
        return null;
    }
}
