package com.izu.mrcar.controller.schedule.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashSet;
import java.util.Set;

/**
 * Created by hp on 2019/9/6.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ScheduleVo {

    private Byte scheduleStatus;

    private Byte dispatcherStatus;

    private Byte subScheduleStatus;

    private Byte orderType;

    private Byte serviceCode;

    private String orderStime;

    private String orderEtime;

    private Integer pageNo;

    private Integer pageSize;

    private String orderApplyNo;

    private String orderNo;

    /**
     * 总调度单id schedule_id
     */
    private Long scheduleId;

    /**
     * 对应的申请单id order_apply_id
     */
    private Long orderApplyId;

    /**
     * 对应的申请单编码 order_apply_code
     */
    private String orderApplyCode;

    /**
     * 企业id company_id
     */
    private Long companyId;

    private Integer customerId;

    private Set<Object> roleTypes=new HashSet<Object>();

    /**
     * 乘车人姓名 passenger_name
     */
    private String passengerName;

    /**
     * 乘车人手机号码 passenger_phone
     */
    private String passengerPhone;

    private String bookingOrderStime;

    private String bookingOrderEtime;

    private String bookingEstimateStime;

    private String bookingEstimateEtime;

    private Boolean bookingOrderTimeIsAsc;

    private Boolean bookingOrderETimeIsAsc;

    private Boolean createTimeIsAsc;

    private Integer passengerStructId;
}
