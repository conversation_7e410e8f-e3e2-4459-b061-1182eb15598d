package com.izu.mrcar.controller.schedule.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * Created by hp on 2019/9/8.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DriverVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer driverId;

    private Long scheduleId;

    private Integer vehicleId;

    private Long companyId;

    private String driverName;

    private String driverMobile;

    private String licenseType;

    private String orderApplyNo;

    private List<String> orderNoList;

    private Integer pageNo;

    private Integer pageSize;
    
    private Integer motorcadeId;
}
