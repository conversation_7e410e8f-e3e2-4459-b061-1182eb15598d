package com.izu.mrcar.controller.schedule;

import com.alibaba.fastjson.JSONObject;
import com.izu.asset.dto.CarDriverDispatchDTO;
import com.izu.config.MrCarConfigRestLocator;
import com.izu.dispatch.DispatchDTO;
import com.izu.dispatch.ScheduleDTO;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.controller.base.UserBaseController;
import com.izu.mrcar.controller.schedule.bean.*;
import com.izu.mrcar.controller.schedule.bean.ScheduleVo;
import com.izu.mrcar.controller.schedule.bean.VehicleVo;
import com.izu.mrcar.order.dto.dispatching.*;
import com.izu.mrcar.shiro.session.WebSessionUtil;
import com.izu.mrcar.order.MrcarOrderRestLocator;
import com.izu.mrcar.order.MrcarOrderRestMsgCenter;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.dto.LoginUser;
import com.izu.mrcar.common.constants.Constant;
import com.izu.mrcar.common.constants.ConstantCode;
import com.izu.user.dto.driver.DispatchForDriverReqDTO;
import com.izu.user.dto.staff.pc.AccountBaseInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.enums.perm.ClientDataPermTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.util.*;
import java.util.stream.Collectors;
import static com.izu.mrcar.controller.order.OrderController.ORDER_APPLY_CANCEL;

@RestController
@RequestMapping("/scheduleCenter")
@Api(tags = "行程调度")
public class ScheduleCenterController extends UserBaseController {

	private static final Logger logger = LoggerFactory.getLogger(ScheduleCenterController.class);
	/**
	 * 调度
	 * @return
	 * @throws Exception
	 */
	@PostMapping(value = "/schedule", produces = "application/json;charset=UTF-8")
	@ApiOperation("内部用车-立即调度")
	public RestResponse<Boolean> schedule(@RequestBody ScheduleCarDriverDTO scheduleCarDriverDTO) {

		final LoginBaseInfo loginInfo = LoginSessionUtil.getBaseLoginInfo();

		AccountBaseInfo accountBaseInfo = loginInfo.obtainBaseInfo();

		scheduleCarDriverDTO.setLoginUserId(accountBaseInfo.getStaffId());

		scheduleCarDriverDTO.setLoginUserName(accountBaseInfo.getStaffName());

		scheduleCarDriverDTO.setLoginUserMobile(accountBaseInfo.getMobile());

		scheduleCarDriverDTO.setLoginCompanyId(loginInfo.obtainBelongCompanyId());

		scheduleCarDriverDTO.setLoginCompanyName(loginInfo.obtainBelongCompanyName());

		final String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.INTERNAL_ORDER_DISPATCH);

		HashMap<String, Object> restParam = new HashMap<>();

		restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, scheduleCarDriverDTO);

		return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
	}

	/**
	 * 调度中心总调度列表接口
	 * @param
	 * @param
	 * @return
	 * @throws Exception
	 * <AUTHOR> 2019年08月06日
	 */
	@PostMapping("/listtrunkdispatcher")
	//@RequiresPermissions(value = "main_dispatch_order")
	public RestResponse listTrunkDispatcher(@RequestBody ScheduleListReqDTO schedule) {
		final LoginBaseInfo loginInfo = LoginSessionUtil.getBaseLoginInfo();
		schedule.setCompanyId(Long.valueOf(loginInfo.obtainBelongCompanyId()));
		schedule.setLoginId(loginInfo.obtainBaseInfo().getStaffId());
		schedule.setDataPermType(loginInfo.obtainSimpleDataPerm().getDataPermType());
		schedule.setDataPermCodeSet(loginInfo.obtainSimpleDataPerm().getDataCodeSet());
		if (null == schedule.getPageNo()) {
			schedule.setPageNo(ConstantCode.PAGE_NO);
		}
		if (null == schedule.getPageSize()) {
			schedule.setPageSize(ConstantCode.PAGE_SIZE);
		}
		final String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.LIST_SCHEDULE_PAGE);
		final HashMap<String, Object> paramMap = new HashMap<String, Object>() {{
			put(BaseHttpClient.POSTBODY_MAP_KEY, schedule);
		}};
		return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, paramMap, null);
	}

	@RequestMapping("/getScheduleOrder")
	public RestResponse getScheduleOrder(@RequestBody Map<String, Object> params){
		LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
		final String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.GET_SCHEDULE_DETAIL_FOR_APP);
		params.put("loginCompanyId",loginBaseInfo.obtainBelongCompanyId());
		//TODO 数据权限
//		params.put("cityDataScope",loginUser.getCityDataScope());
		RestResponse restResponse = RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, params, null);
		return restResponse;
	}

    /**
     * 订单时间范围内各部门可用车辆数接口
     * @param
     * @param
     * @return
     * <AUTHOR> 2019年08月06日
     */
    @PostMapping("/listAvailableVehicle")
    public RestResponse listAvailableVehicle(@RequestBody ScheduleVo scheduleVo) {
		LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
		scheduleVo.setCompanyId(Long.valueOf(loginBaseInfo.obtainBelongCompanyId()));
		final String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.LIST_AVAILABLE_VEHICLE);
		Map<String, Object> paramMap = new HashMap<>();
		paramMap.put("orderStime", scheduleVo.getOrderStime());
		paramMap.put("orderEtime", scheduleVo.getOrderEtime());
		paramMap.put("companyId", scheduleVo.getCompanyId());
		paramMap.put("scheduleId", scheduleVo.getScheduleId());
		return RestClient.requestForList(BaseHttpClient.HttpMethod.GET, restUrl, paramMap, null, HashMap.class);
    }


	/**
	 * 调度中心总调度列表详情接口
	 * @param
	 * @param
	 * @return
	 * <AUTHOR>
	 * 2019年08月07日
	 */
	@PostMapping("/getscheduleorder")
	@ApiOperation("调度中心总调度列表详情接口")
	public RestResponse<ScheduleInfoDTO> getScheduleOrder(@RequestBody ScheduleDTO scheduleDTO) {
		final String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.GET_SCHEDULE_DETAIL);
		LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
		Map<String, Object> paraMap = new HashMap<>();
		paraMap.put("scheduleId", scheduleDTO.getScheduleId());
		paraMap.put("subScheduleId", scheduleDTO.getSubScheduleId());
		paraMap.put("orderApplyNo", scheduleDTO.getOrderApplyNo());
		paraMap.put("loginCompanyId",loginBaseInfo.obtainBelongCompanyId());
		return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, ScheduleInfoDTO.class);
	}


	/**
	 * 调度中心子调度列表详情接口
	 * @param
	 * @param
	 * @return
	 * @throws Exception
	 * <AUTHOR>
	 * 2019年08月07日
	 */
	@PostMapping("/getsubscheduleorder")
	public RestResponse getSubScheduleOrder(@RequestBody ScheduleDTO scheduleDTO) {
		return this.getScheduleOrder(scheduleDTO);
	}

    /**
     * 指派时部门列表接口，查询企业下所有的部门
     * 2019年08月06日
     */
    @PostMapping("/listStructDispatcher")
    public RestResponse listStructDispatcher(@RequestBody Map<String,Object> paraMap) {
		LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
		final String restUrl = new UserRestLocator().getRestUrl("/struct/getCompanyStructList");
		paraMap.put("companyId", loginBaseInfo.obtainBelongCompanyId());
		return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null);
    }

    /**
     * 指派、调度时车辆类型以及数量信息
     * @param
     * @param
     * @return
     * <AUTHOR> 2019年08月06日
     */
    @RequestMapping("/getScheduleVehicle")
    public RestResponse getScheduleVehicle(@RequestBody SubScheduleVo subScheduleVo) {
		LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
		subScheduleVo.setCompanyId(Long.valueOf(loginBaseInfo.obtainBelongCompanyId()));
		String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.GET_SCHEDULE_VEHICLE);
		Map<String, Object> paraMap = new HashMap<>();
		paraMap.put("scheduleId", subScheduleVo.getScheduleId());
		paraMap.put("subScheduleId", subScheduleVo.getSubScheduleId());
		return RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Map.class);
	}


	@PostMapping("/checkVehicleUsed")
	public RestResponse checkVehicleUsed(@RequestBody VehicleVo vehicleVo) {
		LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
		String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.CHECK_VEHICLE_IS_USABLE);
		Map<String, Object> paraMap = new HashMap<>();
		paraMap.put("companyId", loginBaseInfo.obtainBelongCompanyId());
		paraMap.put("scheduleId", vehicleVo.getScheduleId());
		paraMap.put("vehicleId", vehicleVo.getVehicleId());
//		TODO 数据权限
//		paraMap.put("permissions", loginUser.getCityDataScope());
		return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null);
	}

	@PostMapping("/checkDriverUsed")
	public RestResponse checkDriverUsed(@RequestBody VehicleVo vehicleVo) {
		LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
		String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.CHECK_DRIVER_IS_USABLE);
		Map<String, Object> paraMap = new HashMap<>();
		paraMap.put("companyId", loginBaseInfo.obtainBelongCompanyId());
		paraMap.put("scheduleId", vehicleVo.getScheduleId());
		paraMap.put("driverId", vehicleVo.getDriverId());
//		todo 数据权限
//		paraMap.put("permissions", loginUser.getCityDataScope());
		return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null);
	}

	/**
	 * 司机列表
	 * @param
	 * @return
	 * <AUTHOR>
	 * 2018年11月29日
	 */
	@ApiOperation("内部用车-调度可选择司机列表")
	@PostMapping("/listDriver")
	public RestResponse<PageDTO<CarDriverDispatchDTO>> listDriver(@RequestBody DispatchForDriverReqDTO reqDTO) {
		reqDTO.setIsFromPC(Boolean.TRUE);
		return postBodyWithLogin("/driver/querySchedulableDriverList", reqDTO);
	}


	/**
	 * 策略列表
	 * @param
	 * @return
	 * <AUTHOR>
	 * 2018年11月29日
	 */
	@PostMapping("/listPolicy")
	public RestResponse listPolicy(@RequestBody PricePolicyVo pricePolicy) {
		LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
		//启用状态下的策略
		String restUrl =new MrCarConfigRestLocator().getRestUrl("/policy/querySchedulePolicyList");
		Map<String, Object> paraMap = new HashMap<>();
		paraMap.put("pageNo", pricePolicy.getPageNo());
		paraMap.put("pageSize", pricePolicy.getPageSize());
		paraMap.put("carLevelId", pricePolicy.getCarLevelId());
		paraMap.put("companyId", loginBaseInfo.obtainBelongCompanyId());
		if(Objects.equals(pricePolicy.getInternalSupplierFlag(),1)){
			paraMap.put("companyId", pricePolicy.getCompanyId());
		}
		paraMap.put("policyId", pricePolicy.getPolicyId());
		return RestClient.requestForPage(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, PricePolicyVo.class);
	}

	/**
	 * 订单申请取消
	 *
	 * @param restObject
	 * @return
	 */
	@RequestMapping(value = "/orderApplyCancel")
	@ResponseBody
	@RequiresPermissions(value = {"Cancel_Dispatcher","sop_bussiness_dispatch_cancel"},logical = Logical.OR)
	public RestResponse orderApplyCancel(@RequestBody JSONObject restObject) {
		LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
		if (loginBaseInfo == null) {
			//用户未登录
			return RestResponse.fail(Constant.CUSTOMER_NOT_LOGIN);
		}
		final String restUrl = new MrcarOrderRestLocator().getRestUrl(ORDER_APPLY_CANCEL);
		restObject.put("cancelSourceType", "1");
		restObject.put("cancelUserId", 0);
		restObject.put("cancelUserName", loginBaseInfo.obtainBaseInfo().getStaffName());
		try {
			return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, restObject.getInnerMap(), null, Boolean.class);
		} catch (Exception e) {
			return RestResponse.fail(Constant.SYS_ERROR);
		}

	}

	/**
	 * 内部用车订单-分配供应商
	 */
	@PostMapping(MrcarOrderRestMsgCenter.INTERNAL_ORDER_ASSIGN_SUPPLIER)
	public RestResponse assignSupplier(@RequestBody @Validated AssignSupplierDTO reqDTO) {
		final LoginBaseInfo loginInfo = LoginSessionUtil.getBaseLoginInfo();

		AccountBaseInfo accountBaseInfo = loginInfo.obtainBaseInfo();
		reqDTO.setLoginUserId(accountBaseInfo.getStaffId());

		reqDTO.setLoginUserName(accountBaseInfo.getStaffName());

		reqDTO.setLoginUserMobile(accountBaseInfo.getMobile());

		reqDTO.setLoginCompanyId(loginInfo.obtainBelongCompanyId());

		reqDTO.setLoginCompanyName(loginInfo.obtainBelongCompanyName());

		String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.INTERNAL_ORDER_ASSIGN_SUPPLIER);
		HashMap<String, Object> restParam = new HashMap<>();
		restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
		return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
	}
	/**
	 * 内部用车订单-调度退回
	 */
	@PostMapping(MrcarOrderRestMsgCenter.INTERNAL_ORDER_RETURN)
	public RestResponse dispatchReturn(@RequestBody @Validated DispatchReturnDTO reqDTO) {
		final LoginBaseInfo loginInfo = LoginSessionUtil.getBaseLoginInfo();

		AccountBaseInfo accountBaseInfo = loginInfo.obtainBaseInfo();
		reqDTO.setLoginUserId(accountBaseInfo.getStaffId());

		reqDTO.setLoginUserName(accountBaseInfo.getStaffName());

		reqDTO.setLoginUserMobile(accountBaseInfo.getMobile());

		reqDTO.setLoginCompanyId(loginInfo.obtainBelongCompanyId());

		reqDTO.setLoginCompanyName(loginInfo.obtainBelongCompanyName());
		String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.INTERNAL_ORDER_RETURN);
		HashMap<String, Object> restParam = new HashMap<>();
		restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
		return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
	}
	/**
	 * 内部用车订单-调度退回
	 */
	@PostMapping(MrcarOrderRestMsgCenter.INTERNAL_ORDER_CLEAN_SCHEDULE)
	public RestResponse cleanSchedule(@RequestBody @Validated CleanScheduleDTO reqDTO) {
		final LoginBaseInfo loginInfo = LoginSessionUtil.getBaseLoginInfo();

		AccountBaseInfo accountBaseInfo = loginInfo.obtainBaseInfo();
		reqDTO.setLoginUserId(accountBaseInfo.getStaffId());
		reqDTO.setLoginUserName(accountBaseInfo.getStaffName());
		reqDTO.setLoginUserMobile(accountBaseInfo.getMobile());
		reqDTO.setLoginCompanyId(loginInfo.obtainBelongCompanyId());
		reqDTO.setLoginCompanyName(loginInfo.obtainBelongCompanyName());
		String restUrl = new MrcarOrderRestLocator().getRestUrl(MrcarOrderRestMsgCenter.INTERNAL_ORDER_CLEAN_SCHEDULE);
		HashMap<String, Object> restParam = new HashMap<>();
		restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
		return RestClient.requestInside(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null);
	}


}
