package com.izu.mrcar.controller.schedule.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Created by hp on 2019/9/7.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PricePolicyVo implements Serializable{

    private static final long serialVersionUID = 1L;

    private Integer pageNo;

    private Integer pageSize;

    private Integer carLevelId;

    private String carLevelName;

    private Integer policyStatus;

    private Integer chargeType;

    private Integer policyType;

    private Integer policyId;

    private String policyName;

    private BigDecimal policyPrice;

    private Long companyId;

    //是否供应商端 默认否
    private Integer internalSupplierFlag=0;
}
