package com.izu.mrcar.controller.schedule.bean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Set;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Schedule implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer company_id;

    private Integer customer_id;

    private Integer user_id;
    
    private Integer order_id;

    private Set<Object> roleTypes;

    //对应的订单车辆级别
    private Integer vehicle_type;

    //开始时间
    private String order_stime;

    //结束时间
    private String order_etime;

    //总调度单状态(全部:0; 调度成功:1; 调度中:2; 取消：3；默认为0 )
    private Integer schedule_status;

    //子调度单状态(全部:0; 调度成功:1; 调度中:2; 取消：3；默认为0 )
    private Integer sub_schedule_status;


    //指派状态（全部：0； 已指派：1； 未指派：2 默认为0 ）
    private Integer dispatcher_status;

    //当前页码；不传默认为1
    private	Integer page_no;

    //每页显示记录数，不传默认为10
    private Integer page_size;

}
