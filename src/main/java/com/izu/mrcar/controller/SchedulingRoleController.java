package com.izu.mrcar.controller;

import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.config.RestUrlConfig;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@RestController
@RequestMapping("/schedulingRole")
public class SchedulingRoleController {
    private static final Logger logger = LoggerFactory.getLogger(SchedulingRoleController.class);

    @Autowired
    private RestUrlConfig restUrlConfig;

    @RequestMapping(value = "/listForPage")
    public RestResponse listForPage(@RequestBody Map<String,Object> paraMap, HttpServletRequest request){
        String restUrl = restUrlConfig.getConfigCoreUrl() + "schedulingRole/listForPage";

        try {
            LoginBaseInfo clientLoginInfo = LoginSessionUtil.getBaseLoginInfo();
            int companyId = clientLoginInfo.obtainBelongCompanyId();
            paraMap.put("companyId", companyId);
            return RestClient.requestForPage(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Map.class);
        } catch(Exception e) {
            logger.error("listForPage Exception：", e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }

    @RequestMapping(value = "/add")
    @RequestFunction(functionName = "新增排班规则")
    public RestResponse add(@RequestBody Map<String,Object> paraMap, HttpServletRequest request){
        String restUrl = restUrlConfig.getConfigCoreUrl() + "schedulingRole/add";

        try {
            LoginBaseInfo clientLoginInfo = LoginSessionUtil.getBaseLoginInfo();
            int companyId = clientLoginInfo.obtainBelongCompanyId();
            paraMap.put("companyId", companyId);
            paraMap.put("createId", clientLoginInfo.obtainBaseInfo().getStaffId());
            paraMap.put("createName", clientLoginInfo.obtainBaseInfo().getStaffName());
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Object.class);
        } catch(Exception e) {
            logger.error("add Exception：", e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }

    @RequestMapping(value = "/update")
    @RequestFunction(functionName = "编辑排班规则")
    public RestResponse update(@RequestBody Map<String,Object> paraMap, HttpServletRequest request){
        String restUrl = restUrlConfig.getConfigCoreUrl() + "schedulingRole/update";

        try {
            LoginBaseInfo clientLoginInfo = LoginSessionUtil.getBaseLoginInfo();
            int companyId = clientLoginInfo.obtainBelongCompanyId();
            paraMap.put("companyId", companyId);
            paraMap.put("updateId", clientLoginInfo.obtainBaseInfo().getStaffId());
            paraMap.put("updateName", clientLoginInfo.obtainBaseInfo().getStaffName());
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Object.class);
        } catch(Exception e) {
            logger.error("update Exception：", e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }

    @RequestMapping(value = "/delete")
    @RequestFunction(functionName = "删除排班规则")
    public RestResponse delete(@RequestBody Map<String,Object> paraMap, HttpServletRequest request){
        String restUrl = restUrlConfig.getConfigCoreUrl() + "schedulingRole/delete";

        try {
            LoginBaseInfo clientLoginInfo = LoginSessionUtil.getBaseLoginInfo();
            int companyId = clientLoginInfo.obtainBelongCompanyId();
            paraMap.put("companyId", companyId);
            paraMap.put("updateId", clientLoginInfo.obtainBaseInfo().getStaffId());
            paraMap.put("updateName", clientLoginInfo.obtainBaseInfo().getStaffName());
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Object.class);
        } catch(Exception e) {
            logger.error("delete Exception：", e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }

    @RequestMapping(value = "/updateStatus")
    public RestResponse updateStatus(@RequestBody Map<String,Object> paraMap, HttpServletRequest request){
        String restUrl = restUrlConfig.getConfigCoreUrl() + "schedulingRole/updateStatus";

        try {
            LoginBaseInfo clientLoginInfo = LoginSessionUtil.getBaseLoginInfo();
            int companyId = clientLoginInfo.obtainBelongCompanyId();
            paraMap.put("companyId", companyId);
            paraMap.put("updateId", clientLoginInfo.obtainBaseInfo().getStaffId());
            paraMap.put("updateName", clientLoginInfo.obtainBaseInfo().getStaffName());
            return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Object.class);
        } catch(Exception e) {
            logger.error("updateStatus Exception：", e);
            return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
        }
    }
}
