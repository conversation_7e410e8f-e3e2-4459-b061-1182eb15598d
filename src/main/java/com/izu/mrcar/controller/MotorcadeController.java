package com.izu.mrcar.controller;

import com.alibaba.fastjson.JSON;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.input.MotorcadeSaveDTO;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.enums.perm.ClientDataPermTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Sets;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/motorcade")
@Api(tags = "交易运营-企业管理")
public class MotorcadeController {
	private static final Logger logger = LoggerFactory.getLogger(MotorcadeController.class);

	@Value("${mrcar-user-core.host.url}")
	private String USER_HOST_URL;

	@RequestMapping(value = "/listForPage")
	@RequiresPermissions(value ="car_team")
	@RequestFunction(functionName = "车队列表")
    public RestResponse listForPage(@RequestBody Map<String,Object> paraMap, HttpServletRequest request){
		String restUrl = USER_HOST_URL + "motorcade/listForPage";
    	
		try {
//			获取企业id
			final ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
			Integer companyId = clientLoginInfo.obtainBelongCompanyId();
			paraMap.put("companyId", companyId);
			Byte dataType = clientLoginInfo.obtainSimpleDataPerm().getDataPermType();
			if(dataType.equals(ClientDataPermTypeEnum.SELF_COMPANY.getType())){
				if(null!=paraMap.get("companyIds")){
					List<Integer> companyIds = (List<Integer>) paraMap.get("companyIds");
					if(CollectionUtils.isNotEmpty(companyIds)){
						paraMap.put("companyIds",companyIds.stream().map(e->String.valueOf(e)).collect(Collectors.joining(",")));
					}else{
						paraMap.put("companyIds",String.join(",",clientLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.ID).getDataCodeSet()));
					}
				}else {
					paraMap.put("companyIds",String.join(",",clientLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.ID).getDataCodeSet()));
				}
			}else {
				paraMap.put("companyIds",companyId);
			}
			paraMap.put("permissions",String.join(",",clientLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet()));
			paraMap.put("dataPermType",dataType);
			paraMap.put("loginUserId",clientLoginInfo.getBaseInfo().getStaffId());
			return RestClient.requestForPage(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Map.class);
		} catch(Exception e) {
			logger.error("listForPage Exception：", e);
			return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
		}    	
    }

	public static Set<String> getDatePermCompanyScope(List<Integer> companyCodes){
		ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
		ClientDataPermTypeEnum dataPermTypeEnum = ClientDataPermTypeEnum.getByType(clientLoginInfo.obtainDataPerm().getDataPermType());
		Set<String> companyScope = Sets.newHashSet(clientLoginInfo.obtainBelongCompanyCode());
		switch (dataPermTypeEnum){
			case SELF_COMPANY:// 本企业
				companyScope = clientLoginInfo.obtainSimpleDataPerm(LoginBaseInfo.GetDataPermType.CODE).getDataCodeSet();
				break;
			case ASSIGN_DEPT:// 指定部门
				break;
			case ASSIGN_CITY:// 指定城市
				break;
			case ONESELF:// 本人
				break;
		}
		if (companyCodes != null && !companyCodes.isEmpty()){
			if (companyScope.retainAll(companyCodes) && companyScope.isEmpty()){
				return Sets.newHashSet("-1");
			}
		}
		return companyScope;
	}
    
    @RequestMapping(value = "/listDriverForMotorcade")
	@RequestFunction(functionName = "车队司机列表")
    public RestResponse listDriverForMotorcade(@RequestBody Map<String,Object> paraMap, HttpServletRequest request){
		String restUrl = USER_HOST_URL + "motorcade/listDriverForMotorcade";
		try {
//			获取企业id
			final ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
			if (clientLoginInfo == null){
				return RestResponse.fail(RestErrorCode.HTTP_UNAUTHORIZED);
			}
			Integer companyId = clientLoginInfo.obtainBelongCompanyId();
			paraMap.put("companyId", companyId);
			return RestClient.requestForPage(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Map.class);
		} catch(Exception e) {
			logger.error("listDriverForMotorcade Exception：", e);
			return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
		}       	
    }    
    
    @PostMapping(value = "/create")
	@RequiresPermissions(value = "car_team_add")
	@RequestFunction(functionName = "新增车队")
	@ApiOperation(value = "新增车队")
	public RestResponse create(@RequestBody MotorcadeSaveDTO saveDTO, HttpServletRequest request){
		String restUrl = USER_HOST_URL + "motorcade/create";
		try {
	//		获取企业id及用户信息
			final ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
			if (clientLoginInfo == null){
				return RestResponse.fail(RestErrorCode.HTTP_UNAUTHORIZED);
			}
			Integer companyId = clientLoginInfo.obtainBelongCompanyId();
			Integer customerId = clientLoginInfo.getBaseInfo().getStaffId();
			String customerName = clientLoginInfo.getBaseInfo().getStaffName();
			saveDTO.setCompanyId(companyId);
			saveDTO.setOperateId(customerId);
			saveDTO.setOperateName(customerName);
			Map<String, Object> param = new HashMap<>();
			param.put(BaseHttpClient.POSTBODY_MAP_KEY, saveDTO);
			return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null, Map.class);
		} catch(Exception e) {
			logger.error("create Exception：", e);
			return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
		}       	
    }
    
    @RequestMapping(value = "/getById")
	@RequestFunction(functionName = "车队详情")
    public RestResponse getById(@RequestBody Map<String,Object> paraMap, HttpServletRequest request){
		String restUrl = USER_HOST_URL + "motorcade/getById";
		try {
	//		获取企业id及用户信息
			final ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
			if (clientLoginInfo == null){
				return RestResponse.fail(RestErrorCode.HTTP_UNAUTHORIZED);
			}
			Integer companyId = clientLoginInfo.obtainBelongCompanyId();
			paraMap.put("companyId", companyId);
			return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Map.class);
		} catch(Exception e) {
			logger.error("getById Exception：", e);
			return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
		}
    }
    
    @PostMapping(value = "/update")
	@RequiresPermissions(value="car_team_modify")
	@RequestFunction(functionName = "编辑车队")
	@ApiOperation(value = "编辑车队")
	public RestResponse update(@RequestBody MotorcadeSaveDTO saveDTO, HttpServletRequest request){
		String restUrl = USER_HOST_URL + "motorcade/update";
		try {
	//		获取企业id及用户信息
			final ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
			if (clientLoginInfo == null){
				return RestResponse.fail(RestErrorCode.HTTP_UNAUTHORIZED);
			}
			Integer companyId = clientLoginInfo.obtainBelongCompanyId();
			String customerName = clientLoginInfo.getBaseInfo().getStaffName();
			Integer customerId = clientLoginInfo.getBaseInfo().getStaffId();
			saveDTO.setCompanyId(companyId);
			saveDTO.setOperateId(customerId);
			saveDTO.setOperateName(customerName);
			Map<String, Object> param = new HashMap<>();
			param.put(BaseHttpClient.POSTBODY_MAP_KEY, saveDTO);
			return RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, param, null, Map.class);
		} catch(Exception e) {
			logger.error("getById Exception：", e);
			return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
		}    	
    }

	/**
	 * 启用/停用车队
	 * @param paraMap
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/updStatus")
	@RequiresPermissions(value="scheduling_status")
	@RequestFunction(functionName = "编辑车队状态")
	public RestResponse updStatus(@RequestBody Map<String,Object> paraMap, HttpServletRequest request){
		String restUrl = USER_HOST_URL + "motorcade/updStatus";
		try {
			//		获取企业id及用户信息
			final ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
			if (clientLoginInfo == null){
				return RestResponse.fail(RestErrorCode.HTTP_UNAUTHORIZED);
			}
			Integer companyId = clientLoginInfo.obtainBelongCompanyId();
			String customerName = clientLoginInfo.getBaseInfo().getStaffName();
			Integer customerId = clientLoginInfo.getBaseInfo().getStaffId();
			paraMap.put("companyId", companyId);
			paraMap.put("operateId", customerId);
			paraMap.put("operateName", customerName);
			return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Map.class);
		} catch(Exception e) {
			logger.error("getById Exception：", e);
			return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
		}
	}
    
    @RequestMapping(value = "/delete")
	@RequiresPermissions(value = "car_team_disable")
	@RequestFunction(functionName = "删除车队")
    public RestResponse delete(@RequestBody Map<String,Object> paraMap, HttpServletRequest request){
		String restUrl = USER_HOST_URL + "motorcade/delete";
		try {
	//		获取企业id及用户信息
			final ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
			if (clientLoginInfo == null){
				return RestResponse.fail(RestErrorCode.HTTP_UNAUTHORIZED);
			}
			Integer companyId = clientLoginInfo.obtainBelongCompanyId();
			String customerName = clientLoginInfo.getBaseInfo().getStaffName();
			Integer customerId = clientLoginInfo.getBaseInfo().getStaffId();
			paraMap.put("companyId", companyId);
			paraMap.put("operateId", customerId);
			paraMap.put("operateName", customerName);
			return RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paraMap, null, Map.class);
		} catch(Exception e) {
			logger.error("getById Exception：", e);
			return RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
		}		
    }     
}
