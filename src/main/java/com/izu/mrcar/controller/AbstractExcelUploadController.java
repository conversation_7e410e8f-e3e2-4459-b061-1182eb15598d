package com.izu.mrcar.controller;

import com.alibaba.excel.EasyExcel;
import com.izu.excel.util.IzuEasyExcel;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.SequenceUtil;
import com.izu.framework.web.util.excel.ExcelTool;
import com.izu.mrcar.common.cache.SessionRedisSentinelCache;
import com.izu.mrcar.common.excel.NoModelDataListener;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**针对于Excel数据文件上传的通用处理<br>
 *  ROW : Excel中每一行所对应的数据对象 <br>
 *  EUR   : 处理的响应结果，可继承ExcelUploadResult扩展自定义的返回属性<br>
 * **/
public abstract class AbstractExcelUploadController<ROW, EUR extends AbstractExcelUploadController.ExcelUploadResult> extends AbstractExcelDownloadController{
    private static final Logger logger = LoggerFactory.getLogger(AbstractExcelUploadController.class);
    public  static final  String  EXCEL_CHECK_ERRORS_CACHE_KEY          = "aizu_excel_check_error_";//缓存前缀
    private static final int       EXCEL_CHECK_ERRORS_CACHE_TIMEOUT = 6*60*60;//缓存时效
    private static final String  LOGTAG = "【Excel文件上传共通处理】- ";
	@Autowired
	private SessionRedisSentinelCache sessionRedisSentinelCache;


    private Class<ROW> rowClazz;//每一行所对应的数据对象类型
    @SuppressWarnings("unchecked")
	public AbstractExcelUploadController() {
		super();
        Type superClass = this.getClass().getGenericSuperclass();
        Type rowType = ((ParameterizedType) superClass).getActualTypeArguments()[0];
        if (rowType instanceof ParameterizedType) {
            this.rowClazz = (Class<ROW>) ((ParameterizedType) rowType).getRawType();
        } else {
            this.rowClazz = (Class<ROW>) rowType;
        }
    }
	
    /**
     * Excel文件上传结果
     */
	@Data
	public static class ExcelUploadResult {
		private String errorMessage;          //整体的错误提示信息
		private long   fileSize;                     //文件大小(单位：字节)
		
		private int      resolvedRows;          //解析成功的数据行数量
		private int      checkErrorRows;       //校验失败的数据行数量
		private int      persistSuccessRows; //执久化成功的数据行数量
		private int      persistFailedRows;    //执久化失败的数据行数量

		private List<ExcelUploadRowError> checkErrorMessages = new ArrayList<ExcelUploadRowError>(1);//校验失败的每一行的错误信息
		private String  checkErrorDetailUrl     = "";  //校验失败的每一行的错误信息，导出这些每行错误信息的URL
	}
    /**
     * Excel文件每一行的校验结果
     */
	@Data
	public static class ExcelUploadRowError implements Serializable{
		private Integer rowNum;        //行号
		private String   errorMessage;//此行的错误提示信息
		public ExcelUploadRowError(Integer rowNum, String errorMessage) {
			super();
			this.rowNum = rowNum;
			this.errorMessage = errorMessage;
		}
	}

	private HttpServletRequest request;

	public HttpServletRequest getRequest() {
		return request;
	}

	/**Excel文件上传的入口**/
	public RestResponse start(final String excelUrl, final HttpServletRequest request, final HttpServletResponse response, final EUR eur ,final Integer columnCount) {
		this.request = request;
		logger.info(LOGTAG+"文件URL= " + excelUrl );
		//1 参数校验
		if( StringUtils.isBlank(excelUrl)) {
			eur.setErrorMessage("文件上传失败。");
			return this.fail(excelUrl, request, response, eur);
		}
		//2 读取数据流
		final InputStream in = this.getExcelInputStream(excelUrl, eur);
		if( in==null ) {
			eur.setErrorMessage("文件上传失败。");
			return this.fail(excelUrl, request, response, eur);
		}
		//3 解析成文本数据
//		final String excelFileName  = excelUrl.substring(excelUrl.lastIndexOf("/")+1);//文件名
//		final String excelType         = excelFileName.substring( excelFileName.lastIndexOf(".")+1 ).toLowerCase();//扩展名
//		List<Map<Integer,String>> rowStringDatas = this.resolveStringDataFromInputStream(in, excelType, this.getResolveSheetIndexes(), eur );
		NoModelDataListener noModelDataListener = new NoModelDataListener();
		EasyExcel.read(in, noModelDataListener).sheet().doRead();

		Map<Integer, String> headMap = noModelDataListener.getHeadMap();
		checkHeadMap(headMap);
		List<Map<Integer, String>> rowStringDatas = noModelDataListener.getCachedDataList();
		if( rowStringDatas==null || rowStringDatas.size()==0 ) {
			eur.setErrorMessage("文件内容为空，上传失败。");
			return  this.fail(excelUrl, request, response, eur);
		}
/*		if(rowStringDatas.get(0).size()!=columnCount){
			eur.setErrorMessage("文件模板错误，上传失败。");
			return  this.fail(excelUrl, request, response, eur);
		}*/
		//4 转换成对象数据
		List<ROW> rowDatas = new ArrayList<ROW>(1);
		String columnPropertyConfigFile = this.getColumnMappingConfigFile(request, response);
		if( StringUtils.isNotBlank(columnPropertyConfigFile) ) {
			rowDatas = ExcelTool.inportMap2Object(rowStringDatas, this.rowClazz , columnPropertyConfigFile);
		}else {
			rowDatas = this.convertStringDataToObject(rowStringDatas, eur);
		}
		if( rowDatas==null || rowDatas.size()==0 ) {
			eur.setErrorMessage("文件内容为空，上传失败。");
			return  this.fail(excelUrl, request, response, eur);
		}
		//5 数据校验
		List<ExcelUploadRowError> checkErrorMessages = this.checkExcelData(rowDatas, request, response, eur);
		if( checkErrorMessages!=null && checkErrorMessages.size()>0 ) {
			eur.setCheckErrorRows(checkErrorMessages.size());
			eur.setCheckErrorMessages(checkErrorMessages.stream().sorted(Comparator.nullsLast(Comparator.comparing(ExcelUploadRowError::getRowNum))).collect(Collectors.toList()) );
			eur.setErrorMessage("文件内容错误，上传失败。");
			return  this.fail(excelUrl, request, response, eur);
		}
		//6 执久化之前的动作
		if( this.beforePersist(rowDatas, request, response, eur)==false ) {
			eur.setErrorMessage("文件上传失败。");
			return this.fail(excelUrl, request, response, eur);
		}
		//7 执久化动作
		this.request = null;
		return this.executePersist(rowDatas, request, response, eur);
		
//		//最后响应
//		if( eur.getResolvedRows()==eur.getPersistSuccessRows() ) {
//			eur.setErrorMessage("文件上传成功。");
//			return this.success(excelUrl, request, response, eur);
//		}else {
//			eur.setErrorMessage("文件上传失败。");
//			return this.fail(excelUrl, request, response, eur);
//		}
	}
	
	//通过excelUrl获得输入流
	private InputStream getExcelInputStream( final String excelUrl, final EUR eur ){
		if( excelUrl.toLowerCase().startsWith("http") ) {//--->>>URL地址
			final String baseDir            = System.getProperty("java.io.tmpdir") + File.separator + "upload";
			final String excelFileName  = excelUrl.substring(excelUrl.lastIndexOf("/")+1);
			final String storeFilePath   = baseDir + File.separator + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) +"_"+ excelFileName;
			logger.info(LOGTAG+"上传文件名称="+ excelFileName );
			logger.info(LOGTAG+"存储保存路径="+ storeFilePath );
			Map<String,String> httpHeader = new HashMap<String,String>(2);
			httpHeader.put("Referer", "https://prd-third.izuche.com");//有防盗链机制，必须加上Referer
			boolean downloadOK =  BaseHttpClient.download(BaseHttpClient.HttpMethod.GET, excelUrl, null, httpHeader, storeFilePath);
			if( downloadOK ) {
				logger.info(LOGTAG+"文件下载成功" );
				File storeFile = new File(storeFilePath);
				eur.setFileSize( storeFile.length() );
				try {
					return new FileInputStream(storeFile);
				} catch (FileNotFoundException e) {
					logger.error(LOGTAG+"文件下载失败", e );
					return null;
				}
			}else {
				logger.error(LOGTAG+"文件下载失败" );
				return null;
			}
		}else if( excelUrl.toLowerCase().startsWith("file:") ) {//--->>>本地文件路径，方便开发调试
			final String storeFilePath   = excelUrl.substring(5);
			logger.info(LOGTAG+"存储保存路径="+ storeFilePath );
			if( !storeFilePath.toLowerCase().endsWith("xls") && !storeFilePath.toLowerCase().endsWith("xlsx") ) {
				logger.error(LOGTAG+"不是Excel文件");
				return null;
			}
			File storeFile = new File(storeFilePath);
			if( storeFile.exists()==false ) {
				logger.error(LOGTAG+"文件不存在");
				return null;
			}
			if( storeFile.isFile()==false ) {
				logger.error(LOGTAG+"文件不存在");
				return null;
			}
			eur.setFileSize( storeFile.length() );
			try {
				return new FileInputStream(storeFile);
			} catch (FileNotFoundException e) {
				logger.error(LOGTAG+"文件不存在", e );
				return null;
			}
		}else {
			logger.error(LOGTAG+"不支持此类型的URL！");
			return null;
		}
	}
	
	//解析输入流，返回文本数据
	private List<Map<Integer,String>> resolveStringDataFromInputStream( final InputStream excelInputStream, final String excelType, final List<Integer> specifiedSheetIndexs , final EUR eur){
		List<Map<Integer, String>> rowDatas = new ArrayList<Map<Integer,String>>(1);
		try{
			rowDatas = ExcelTool.inport(excelInputStream, excelType, specifiedSheetIndexs);
			if( rowDatas!=null ) {
				eur.setResolvedRows( rowDatas.size() );
			}
			logger.info(LOGTAG+"解析文件，返回文本数据，解析出的行数="+ rowDatas.size() );
			return rowDatas;
		}catch(Exception ex ) {
			logger.error(LOGTAG+"解析文件异常！", ex );
			return new ArrayList<Map<Integer,String>>(1);
		}
	}
	
	/**读取Sheet的序号（从1开始，依此类推）**/
	protected abstract List<Integer> getResolveSheetIndexes();
	/** 解析得到的文本数据，执行数据转换，生成对象列表（方式一：基于属性配置文件的默认实现，配置文件中标识了列序与属性之间的映射关系） **/
	protected abstract String getColumnMappingConfigFile(final HttpServletRequest request, final HttpServletResponse response );
	/** 解析得到的文本数据，执行数据转换，生成对象列表（方式二：自定义实现）**/
	protected abstract List<ROW> convertStringDataToObject( final List<Map<Integer,String>> rowDatas, final EUR eur);
	/** 校验数据的合法性（校验不通过时，返回每行的错误信息） **/
	protected abstract List<ExcelUploadRowError> checkExcelData(final List<ROW> rowDatas, final HttpServletRequest request, final HttpServletResponse response, final EUR eur );
	/** 执久化Excel数据之前 **/
	protected abstract boolean beforePersist(final List<ROW> rowDatas, final HttpServletRequest request, final HttpServletResponse response, final EUR eur );
	/** 执久化Excel数据 **/
	protected abstract RestResponse executePersist(final List<ROW> rowDatas, final HttpServletRequest request, final HttpServletResponse response, final EUR eur );
	
	//响应成功
	protected RestResponse success(final String excelUrl, final HttpServletRequest request, final HttpServletResponse response, final EUR eur ) {
		return RestResponse.success( eur );
	}
	//响应失败
	protected RestResponse fail(final String excelUrl, final HttpServletRequest request, final HttpServletResponse response, final EUR eur ) {
		//缓存每行的校验错误信息
		if( eur.getCheckErrorMessages()!=null && eur.getCheckErrorMessages().size()>0 ) {
			long checkErrorId = SequenceUtil.generate();
			ExcelUploadRowError[] errors = new ExcelUploadRowError[eur.getCheckErrorMessages().size()];
			eur.getCheckErrorMessages().toArray(errors);
			sessionRedisSentinelCache.set(EXCEL_CHECK_ERRORS_CACHE_KEY+checkErrorId , errors, EXCEL_CHECK_ERRORS_CACHE_TIMEOUT);
			eur.setCheckErrorDetailUrl("/showexcelerror?checkErrorId="+checkErrorId );
			logger.info(LOGTAG+"数据校验存在错误数据，写入缓存成功。checkErrorId="+ checkErrorId );
		}
		RestResponse returnResp = RestResponse.fail(RestErrorCode.UNKNOWN_ERROR);
		returnResp.setData( eur );
		returnResp.setMsg( eur.getErrorMessage() );
		return returnResp;
	}


	protected  void checkHeadMap(Map<Integer, String> headMap){};
}