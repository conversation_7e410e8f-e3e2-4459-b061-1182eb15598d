package com.izu.mrcar.controller.permission;

import com.alibaba.excel.EasyExcel;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.constants.MrcarUrl;
import com.izu.mrcar.controller.AbstractExcelUploadController;
import com.izu.mrcar.service.user.ClientUserManageImportStaffService;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.CustomerImportDTO;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.enums.role.SystemTypeEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date : 2023/2/18
 */
@Api(tags = "权限管理")
@RestController
@Slf4j
public class ClientUserManageImportStaffController {

    @Autowired
    private ClientUserManageImportStaffService importStaffService;


    @ApiOperation(value = "下载用户导入模版")
    @RequestFunction(functionName = "权限管理-用户列表-下载用户导入模版")
    @PostMapping(MrcarUrl.CLIENT_STAFF_DOWNLOAD_IMPORT_TEMPLATE)
    @Deprecated
    public RestResponse downloadImportTemplate(HttpServletRequest request, HttpServletResponse response) {
        final LoginBaseInfo loginInfo = LoginSessionUtil.getBaseLoginInfo();
        final Integer companyId = loginInfo.obtainBelongCompanyId();
        importStaffService.downloadImportTemplate(request, response, companyId, SystemTypeEnum.CUSTOMER.getCode());
        return null;
    }

    /**
     * 新增需求: 调整用户导入模板
     */
    @ApiOperation(value = "下载用户导入模版V2")
    @RequestFunction(functionName = "权限管理-用户列表-下载用户导入模版")
    @PostMapping(MrcarUrl.CLIENT_STAFF_DOWNLOAD_IMPORT_TEMPLATE_V2)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "vendor", value = "模板类型(excel或wps)", required = true, paramType = "form")
    })
    public ResponseEntity<Resource> downloadImportTemplateV2(@Verify(param = "vendor", rule = "required") String vendor)
            throws IOException {
        // 查询公司权限
        Integer companyId = LoginSessionUtil.getBaseLoginInfo().obtainBelongCompanyId();
        return importStaffService.downloadImportTemplateV2(companyId, SystemTypeEnum.CUSTOMER.getCode(), vendor);
    }

    @ApiOperation(value = "导入员工")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "file", value = "文件名称", required = true, paramType = "form")
    })
    @PostMapping(MrcarUrl.CLIENT_STAFF_MANAGE_IMPORT)
    public RestResponse importStaff(@Verify(param = "excelUrl", rule = "required") String excelUrl) {
        return importStaffService.importStaffForCustomer(getExcelInputStream(excelUrl));
    }

    private InputStream getExcelInputStream(final String excelUrl){
        String LOGTAG="importStaff";
        if( excelUrl.toLowerCase().startsWith("http") ) {//--->>>URL地址
            final String baseDir            = System.getProperty("java.io.tmpdir") + File.separator + "upload";
            final String excelFileName  = excelUrl.substring(excelUrl.lastIndexOf("/")+1);
            final String storeFilePath   = baseDir + File.separator + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) +"_"+ excelFileName;
            log.info(LOGTAG+"上传文件名称="+ excelFileName );
            log.info(LOGTAG+"存储保存路径="+ storeFilePath );
            Map<String,String> httpHeader = new HashMap<String,String>(2);
            httpHeader.put("Referer", "https://prd-third.izuche.com");//有防盗链机制，必须加上Referer
            boolean downloadOK =  BaseHttpClient.download(BaseHttpClient.HttpMethod.GET, excelUrl, null, httpHeader, storeFilePath);
            if( downloadOK ) {
                log.info(LOGTAG+"文件下载成功" );
                File storeFile = new File(storeFilePath);
                try {
                    return new FileInputStream(storeFile);
                } catch (FileNotFoundException e) {
                    log.error(LOGTAG+"文件下载失败", e );
                    return null;
                }
            }else {
                log.error(LOGTAG+"文件下载失败" );
                return null;
            }
        }else if( excelUrl.toLowerCase().startsWith("file:") ) {//--->>>本地文件路径，方便开发调试
            final String storeFilePath   = excelUrl.substring(5);
            log.info(LOGTAG+"存储保存路径="+ storeFilePath );
            if( !storeFilePath.toLowerCase().endsWith("xls") && !storeFilePath.toLowerCase().endsWith("xlsx") ) {
                log.error(LOGTAG+"不是Excel文件");
                return null;
            }
            File storeFile = new File(storeFilePath);
            if( storeFile.exists()==false ) {
                log.error(LOGTAG+"文件不存在");
                return null;
            }
            if( storeFile.isFile()==false ) {
                log.error(LOGTAG+"文件不存在");
                return null;
            }
            try {
                return new FileInputStream(storeFile);
            } catch (FileNotFoundException e) {
                log.error(LOGTAG+"文件不存在", e );
                return null;
            }
        }else {
            log.error(LOGTAG+"不支持此类型的URL！");
            return null;
        }
    }




}
