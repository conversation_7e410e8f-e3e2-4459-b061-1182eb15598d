package com.izu.mrcar.controller.permission;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.izu.bi.common.dto.v3.CzOrderInfoDTO;
import com.izu.bi.common.dto.v3.CzOverdueIncarOrderParamVo;
import com.izu.excel.annotation.ExportCsv;
import com.izu.excel.download.AbstractIzuWebCsv;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.validate.verify.Verify;
import com.izu.mrcar.common.annotation.RequestFunction;
import com.izu.mrcar.common.constants.MrcarUrl;
import com.izu.mrcar.controller.base.UserBaseController;
import com.izu.mrcar.service.user.SessionClearService;
import com.izu.mrcar.utils.DateUtil;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.config.UserRestLocator;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.user.dto.customer.*;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import com.izu.user.enums.LoginSystemEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 客户端-权限管理-员工管理
 *
 * <AUTHOR>
 * @date : 2023/2/6
 */
@Api(tags = "权限管理")
@RestController
@Slf4j
public class ClientUserManageController extends UserBaseController implements AbstractIzuWebCsv {

    @Autowired
    private SessionClearService sessionClearService;

    @ApiOperation(value = "获取用户列表")
    @RequestFunction(functionName = "权限管理-用户列表")
    @RequiresPermissions(value = "userAccountList")
    @PostMapping(UserUrlCenter.CLIENT_GET_STAFF_LIST)
    public RestResponse<CustomerManageRespDTO> customerList(@RequestBody CustomerManageReqDTO customerManageReqDTO) {
        return super.postBodyWithLogin(UserUrlCenter.CLIENT_GET_STAFF_LIST, customerManageReqDTO);
    }


    @ApiOperation(value = "导出用户列表")
    @RequestFunction(functionName = "权限管理-导出用户列表")
    @RequiresPermissions(value = "user_account_export")
    @PostMapping(MrcarUrl.CLIENT_STAFF_MANAGE_EXPORT)
    @ExportCsv(fileName = "员工导出列表.csv",
            filePath = "/data/logs/mrcar/tmp",
            head = "用户中文名,用户英文名,用户手机号,邮箱,所属部门,公司名称,职位,员工工号,角色,数据权限,修改人,修改时间,用户状态")
    public void export(@RequestBody CustomerManageReqDTO customerManageReqDTO, HttpServletResponse response) {
        final Map<String, Object> innerMap = JSON.parseObject(JSON.toJSONString(customerManageReqDTO)).getInnerMap();
        final LoginBaseInfo loginInfo = LoginSessionUtil.getBaseLoginInfo();
        this.webRepeatWriteCsv(response, innerMap, loginInfo.obtainBaseInfo().getStaffName());
    }


    @ApiOperation(value = "新增客户端员工")
    @RequestFunction(functionName = "用户列表新增员工")
    @PostMapping(UserUrlCenter.CLIENT_SAVE_CUSTOMER)
    public RestResponse saveCustomer(@RequestBody CustomerSaveReqDTO reqDTO) {
        reqDTO.setCompanyId(LoginSessionUtil.getBaseLoginInfo().obtainBelongCompanyId());
        return super.postBodyWithLogin(UserUrlCenter.CLIENT_SAVE_CUSTOMER, reqDTO);
    }


    @ApiOperation(value = "修改客户端员工")
    @RequestFunction(functionName = "修改客户端员工")
    @PostMapping(UserUrlCenter.CLIENT_STAFF_MANAGE_UPDATE)
    public RestResponse updateCustomer(@RequestBody CustomerUpdateReqDTO reqDTO) {
        reqDTO.setCompanyId(LoginSessionUtil.getBaseLoginInfo().obtainBelongCompanyId());
        //获取修改之前的手机号
        HashMap<String, Object> phoneRestParam = new HashMap<>();
        phoneRestParam.put("userIds", reqDTO.getCustomerId());
        List<String> phones = super.postForList(UserUrlCenter.GET_PHONE_BY_USER_ID, phoneRestParam, String.class);
        String phoneOld = null;
        if (CollectionUtils.isNotEmpty(phones)) {
            phoneOld = phones.get(0);
        }
        //判断是否修改了手机号，如果修改了手机号移除登录的会话
        boolean removeSession = StringUtils.isNotBlank(phoneOld) && !phoneOld.equals(reqDTO.getMobile());
        RestResponse restResponse = super.postBodyWithLogin(UserUrlCenter.CLIENT_STAFF_MANAGE_UPDATE, reqDTO);
        if (restResponse.isSuccess() && removeSession) {
            //修改用户数据成功并且需要本次修改了手机号，移除被修改人的会话信息
            sessionClearService.removeLoginSession(phoneOld, LoginSystemEnum.CLIENT);
        }
        return restResponse;
    }


    @ApiOperation(value = "新建员工页面-获取数据权限和角色列表")
    @RequestFunction(functionName = "新建页面-获取数据权限和角色列表")
    @PostMapping(UserUrlCenter.CLIENT_STAFF_ROLE_AND_PERM_FOR_CREATE)
    public RestResponse<StaffPermAndRoleRespDTO> getRoleAndPermForCreate() {
        final LoginBaseInfo loginInfo = LoginSessionUtil.getBaseLoginInfo();
        final Integer companyId = loginInfo.obtainBelongCompanyId();
        final HashMap<String, Object> restParam = Maps.newHashMapWithExpectedSize(1);
        restParam.put("companyId", companyId);
        return super.post(UserUrlCenter.CLIENT_STAFF_ROLE_AND_PERM_FOR_CREATE, restParam);
    }


    @ApiOperation(value = "新建员工页面-获取用户拥有的数据权限和角色列表")
    @RequestFunction(functionName = "新建页面-获取用户拥有的数据权限和角色列表")
    @PostMapping(UserUrlCenter.CLIENT_STAFF_ROLE_AND_PERM_FOR_CREATE2)
    public RestResponse<StaffPermAndRoleRespDTO> getRoleAndPermForCreate2() {
        final LoginBaseInfo loginInfo = LoginSessionUtil.getBaseLoginInfo();
        final Integer companyId = loginInfo.obtainBelongCompanyId();
        final HashMap<String, Object> restParam = new HashMap<String, Object>() {{
            put("companyId", companyId);
            put("customerId", loginInfo.obtainBaseInfo().getStaffId());
        }};
        return super.post(UserUrlCenter.CLIENT_STAFF_ROLE_AND_PERM_FOR_CREATE2, restParam);
    }

    @ApiOperation(value = "员工详情-编辑回显")
    @RequestFunction(functionName = "员工详情-编辑回显")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "customerId", value = "员工ID", required = true, paramType = "form")
    })
    @PostMapping(UserUrlCenter.CLIENT_STAFF_MANAGE_DETAIL_FOR_UPDATE)
    public RestResponse<CustomerDetailRespDTO> getCustomerDetailForUpdate(@Verify(param = "customerId", rule = "required") Integer customerId) {
        final HashMap<String, Object> restParam = Maps.newHashMapWithExpectedSize(1);
        restParam.put("customerId", customerId);
        return super.post(UserUrlCenter.CLIENT_STAFF_MANAGE_DETAIL_FOR_UPDATE, restParam);
    }


    @ApiOperation(value = "员工详情-编辑回显new")
    @RequestFunction(functionName = "员工详情-编辑回显new")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "customerId", value = "员工ID", required = true, paramType = "form"),
    })
    @PostMapping(UserUrlCenter.CLIENT_STAFF_MANAGE_DETAIL_FOR_UPDATE2)
    public RestResponse getCustomerDetailForUpdate2(@Verify(param = "customerId", rule = "required") Integer customerId) {
        ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
        Integer loginCustomerId = clientLoginInfo.getBaseInfo().getStaffId();

        final HashMap<String, Object> restParam = new HashMap<String, Object>() {{
            put("customerId", customerId);
            put("loginCustomerId", loginCustomerId);
        }};
        return super.post(UserUrlCenter.CLIENT_STAFF_MANAGE_DETAIL_FOR_UPDATE2, restParam);
    }





    @ApiOperation(value = "员工详情")
    @RequestFunction(functionName = "员工详情")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "customerId", value = "员工ID", required = true, paramType = "form")
    })
    @PostMapping(UserUrlCenter.CLIENT_STAFF_MANAGE_DETAIL)
    public RestResponse<ClientLoginInfo> getCustomerDetail(@Verify(param = "customerId", rule = "required") Integer customerId){
        final HashMap<String, Object> restParam = Maps.newHashMapWithExpectedSize(1);
        restParam.put("customerId", customerId);

        return super.post(UserUrlCenter.CLIENT_STAFF_MANAGE_DETAIL, restParam);
    }


    @ApiOperation(value = "员工禁用")
    @RequestFunction(functionName = "员工禁用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "customerId", value = "员工ID", required = true, paramType = "form")
    })
    @PostMapping(UserUrlCenter.CLIENT_STAFF_MANAGE_DISABLE)
    public RestResponse disable(@Verify(param = "customerId", rule = "required|min(1)") Integer customerId) {
        final HashMap<String, Object> restParam = Maps.newHashMapWithExpectedSize(1);
        restParam.put("customerId", customerId);
        return super.postBodyWithLogin(UserUrlCenter.CLIENT_STAFF_MANAGE_DISABLE, restParam);
    }

    @ApiOperation(value = "员工启用")
    @RequestFunction(functionName = "员工启用")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "customerId", value = "员工ID", required = true, paramType = "form")
    })
    @PostMapping(UserUrlCenter.CLIENT_STAFF_MANAGE_ENABLE)
    public RestResponse enableUser(@Verify(param = "customerId", rule = "required|min(1)") Integer customerId) {
        final HashMap<String, Object> restParam = Maps.newHashMapWithExpectedSize(1);
        restParam.put("customerId", customerId);
        return super.postBodyWithLogin(UserUrlCenter.CLIENT_STAFF_MANAGE_ENABLE, restParam);
    }


    @ApiOperation(value = "员工删除")
    @RequestFunction(functionName = "员工删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "customerId", value = "员工ID", required = true, paramType = "form")
    })
    @PostMapping(UserUrlCenter.CLIENT_STAFF_MANAGE_DELETE)
    public RestResponse deleteUser(@Verify(param = "customerId", rule = "required|min(1)") Integer customerId) {
        final HashMap<String, Object> restParam = new HashMap<String, Object>() {{
            put("customerId", customerId);

            put("loginUserId", 1);
            put("loginUserName", "");
        }};
        return super.postBodyWithLogin(UserUrlCenter.CLIENT_STAFF_MANAGE_DELETE, restParam);
    }


    @ApiOperation(value = "获取登录人所在公司的所有员工下拉框")
    @RequestFunction(functionName = "获取登录人所在公司的所有员工下拉框")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "customerName", value = "员工名称-模糊搜索", required = true, paramType = "form"),
            @ApiImplicitParam(name = "customerStatus", value = "员工状态;1-启用;4-停用", required = true, paramType = "form"),
            @ApiImplicitParam(name = "companyId", value = "公司ID,不传时，默认是取当前登录公司，传了值取该公司", required = false, paramType = "form"),
    })
    @GetMapping(UserUrlCenter.CLIENT_STAFF_MANAGE_GET_ALL_CUSTOMER)
    public RestResponse getAllCustomer(Byte customerStatus, String customerName,Integer companyId) {
        final Integer loginCompanyId = LoginSessionUtil.getBaseLoginInfo().obtainBelongCompanyId();
        HashMap<String, Object> restParam = new HashMap<String, Object>();
        if(companyId ==null){
            restParam.put("companyId", loginCompanyId);
        }else{
            restParam.put("companyId", companyId);
        }
        restParam.put("customerName", customerName);
        restParam.put("customerStatus", customerStatus);
        return post(UserUrlCenter.CLIENT_STAFF_MANAGE_GET_ALL_CUSTOMER, restParam);
    }


    @Override
    public PageDTO getDataByPage(Map<String, Object> map, int pageNo, int pageSize) {
        CustomerManageReqDTO param = JSON.parseObject(JSON.toJSONString(map), CustomerManageReqDTO.class);
        param.setPage(pageNo);
        param.setPageSize(pageSize);
        final RestResponse response = super.postBodyWithLoginForPage(UserUrlCenter.CLIENT_GET_STAFF_LIST, param, CustomerManageRespDTO.class);
        if (!response.isSuccess()) {
            log.error("调用user获取用户列表失败:参数:{} 相应:{}", map, JSON.toJSONString(response));
            throw ExceptionFactory.createRestException(RestErrorCode.HTTP_UNAUTHORIZED);
        }
        return (PageDTO) response.getData();
    }

    @Override
    public List<String> buildRecordData(PageDTO pageDTO) {
        List<CustomerManageRespDTO> resultList = pageDTO.getResult();
        List<String> result = resultList.stream().map(c -> {
            StringBuilder sb = new StringBuilder();
            String spit = ",";
            String tab = "\t";
            //员工中文名,用户英文名,用户手机号,邮箱,所属部门,公司名称,职位,员工工号,角色,数据权限,修改人,修改时间,员工状态
            sb.append(tab + Optional.ofNullable(c.getCustomerName()).orElse("")).append(spit);
            sb.append(tab + Optional.ofNullable(c.getEnglishName()).orElse("")).append(spit);
            sb.append(tab + Optional.ofNullable(c.getMobile()).orElse("")).append(spit);
            sb.append(tab + Optional.ofNullable(c.getEmail()).orElse("")).append(spit);
            sb.append(tab + Optional.ofNullable(c.getStructName()).orElse("")).append(spit);
            sb.append(tab + Optional.ofNullable(c.getCompanyName()).orElse("")).append(spit);
            sb.append(tab + Optional.ofNullable(c.getPosition()).orElse("")).append(spit);
            sb.append(tab + Optional.ofNullable(c.getCustomerNo()).orElse("")).append(spit);
            sb.append(tab + (StringUtils.isNotBlank(c.getRoleNames()) ? c.getRoleNames().replace(",", " ") : "")).append(spit);
            sb.append(tab + (StringUtils.isNotBlank(c.getDataPermTypeName()) ? c.getDataPermTypeName().replace(",", " ") : "")).append(spit);
            sb.append(tab + Optional.ofNullable(c.getUpdateName()).orElse("")).append(spit);
            sb.append(tab + c.getUpdateDate() == null ? "" : DateUtil.format(c.getUpdateDate(), DateUtil.TIME_FORMAT)).append(spit);
            sb.append(tab + Optional.ofNullable(c.getCustomerStatusDesc()).orElse("")).append(spit);
            return sb.toString();
        }).collect(Collectors.toList());
        return result;
    }
}
