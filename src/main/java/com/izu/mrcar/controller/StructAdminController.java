package com.izu.mrcar.controller;

import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.common.constants.ConstantCode;
import com.izu.mrcar.utils.LoginSessionUtil;
import com.izu.user.dto.StructNodeDTO;
import com.izu.user.dto.staff.pc.ClientLoginInfo;
import com.izu.user.dto.staff.pc.LoginBaseInfo;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping(value="/structAdmin")
public class StructAdminController {
	private static final Logger logger = LoggerFactory.getLogger(StructAdminController.class);

	@Value("${mrcar-user-core.host.url}")
	private String USER_HOST_URL;
	
	@Value("${mrcar-asset-core.host.url}")
	private String ASSET_HOST_URL;	
	
	private static final String COUNT_CAR_URL = "car/countCarForStruct";
	
	private static final String COUNT_CUSTOMER_URL = "userManage/countCustomerByStruct";
	
	private static final String COUNT_DRIVER_URL = "driver/countDriverForStruct";
	private static final String QUERY_DRIVER_LIST = "driver/selectDriverForStruct";

	private static final String QUERY_STRUCT_URL = "struct/getAllStruct";

	private static final String QUERY_STRUCT_FOR_DOWNLOAD = "struct/getAllStructForDownLoad";
	private static final String BATCH_INSERT_STRUCT = "struct/batchInsertStruct";
	private static final String UPDATE_STRUCT = "struct/updateStruct";
	private static final String DELETE_STRUCT = "struct/deleteStruct";
	private static final String QUERY_CUSTOMER_LIST = "userManage/queryCustomerForList";

	@RequestMapping(value="/countStructAsset",method=RequestMethod.POST)
	public RestResponse insert(@RequestBody Map<String,Object> paraMap,HttpServletRequest request) {
		try {
			ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
//			paraMap.put("structId", loginUser.getStructIds().get(0));
			paraMap.put("companyId", clientLoginInfo.getClientCompany().getCompanyId());
			RestResponse respCar = RestClient.requestInside(BaseHttpClient.HttpMethod.POST, ASSET_HOST_URL + COUNT_CAR_URL, paraMap, null);
			RestResponse respCustomer = RestClient.requestInside(BaseHttpClient.HttpMethod.POST, USER_HOST_URL + COUNT_CUSTOMER_URL, paraMap, null);
			RestResponse respDriver = RestClient.requestInside(BaseHttpClient.HttpMethod.POST, USER_HOST_URL + COUNT_DRIVER_URL, paraMap, null);
			Map<String, Object> data = new HashMap<String, Object>();
			data.put("carCount", respCar.getData());
			data.put("customerCount", respCustomer.getData());
			data.put("driverCount", respDriver.getData());
			return RestResponse.success(data);
		} catch(Exception e) {
			logger.error("查询部门人员、车辆、司机数量异常", e);
			return RestResponse.fail(RestErrorCode.SYSTEM_EXCEPTION);
		}
	}

    @RequestMapping(value="/queryDriverForList",method=RequestMethod.POST)
    public RestResponse queryDriverForList(@RequestBody Map<String,Object> paraMap,HttpServletRequest request) {
        try {
			ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
            paraMap.put("companyId", clientLoginInfo.getClientCompany().getCompanyId());
			paraMap.put("structIdStr",paraMap.get("structId"));
			RestResponse restDriverList= RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POST, USER_HOST_URL + QUERY_DRIVER_LIST, paraMap, null, RestResponse.class);
            RestResponse respCar = RestClient.requestInside(BaseHttpClient.HttpMethod.POST, ASSET_HOST_URL + COUNT_CAR_URL, paraMap, null);
            RestResponse respCustomer = RestClient.requestInside(BaseHttpClient.HttpMethod.POST, USER_HOST_URL + COUNT_CUSTOMER_URL, paraMap, null);
            RestResponse respDriver = RestClient.requestInside(BaseHttpClient.HttpMethod.POST, USER_HOST_URL + COUNT_DRIVER_URL, paraMap, null);
            Map<String, Object> data = new HashMap<String, Object>();
            data.put("driverList",restDriverList.getData());
            data.put("carCount", respCar.getData()==null?0:respCar.getData());
            data.put("customerCount", respCustomer.getData()==null?0:respCustomer.getData());
            data.put("driverCount", respDriver.getData()==null?0:respDriver.getData());
            return RestResponse.success(data);
        } catch(Exception e) {
            logger.error("查询部门人员、车辆、司机数量异常{}", e);
            return RestResponse.fail(RestErrorCode.SYSTEM_EXCEPTION);
        }
    }

	@RequestMapping(value="/queryCustomerForList",method=RequestMethod.POST)
	public RestResponse queryCustomerForList(@RequestBody Map<String,Object> paraMap,HttpServletRequest request) {
		try {
			ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
			paraMap.put("companyId", clientLoginInfo.getClientCompany().getCompanyId());
			paraMap.put("structIdStr",paraMap.get("structId"));
			RestResponse restCustomerList= RestClient.requestThirdApi(BaseHttpClient.HttpMethod.POST, USER_HOST_URL + QUERY_CUSTOMER_LIST, paraMap, null, RestResponse.class);
			RestResponse respCar = RestClient.requestInside(BaseHttpClient.HttpMethod.POST, ASSET_HOST_URL + COUNT_CAR_URL, paraMap, null);
			RestResponse respCustomer = RestClient.requestInside(BaseHttpClient.HttpMethod.POST, USER_HOST_URL + COUNT_CUSTOMER_URL, paraMap, null);
			RestResponse respDriver = RestClient.requestInside(BaseHttpClient.HttpMethod.POST, USER_HOST_URL + COUNT_DRIVER_URL, paraMap, null);
			Map<String, Object> data = new HashMap<String, Object>();
			data.put("customerList",restCustomerList.getData());
			data.put("carCount", respCar.getData()==null?0:respCar.getData());
			data.put("customerCount", respCustomer.getData()==null?0:respCustomer.getData());
			data.put("driverCount", respDriver.getData()==null?0:respDriver.getData());
			return RestResponse.success(data);
		} catch(Exception e) {
			logger.error("查询部门人员、车辆、司机数量异常{}", e);
			return RestResponse.fail(RestErrorCode.SYSTEM_EXCEPTION);
		}
	}

	@RequestMapping(value="/getAllStruct",method=RequestMethod.POST)
	public RestResponse getAllStruct(@RequestBody Map<String,Object> paraMap,HttpServletRequest request) {
		try {
			ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
			paraMap.put("companyId", clientLoginInfo.getClientCompany().getCompanyId());
			return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, USER_HOST_URL + QUERY_STRUCT_URL, paraMap, null);
		} catch(Exception e) {
			logger.error("查询公司所有部门树形结构异常{}", e);
			return RestResponse.fail(RestErrorCode.SYSTEM_EXCEPTION);
		}
	}
	
	@RequestMapping(value="/getStructByParas",method=RequestMethod.POST)
	public RestResponse getStructByParas(@RequestBody Map<String,Object> paraMap,HttpServletRequest request) {
		try {
			return RestResponse.success(0);
		} catch(Exception e) {
			logger.error("按城市编码查询公司部门信息异常{}", e);
			return RestResponse.fail(RestErrorCode.SYSTEM_EXCEPTION);
		}
	}	

	@RequestMapping(value="/batchInsertStruct",method=RequestMethod.POST)
	public RestResponse batchInsertStruct(@RequestBody Map<String,Object> paraMap,HttpServletRequest request) {
		try {
			ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
			paraMap.put("companyId", clientLoginInfo.getClientCompany().getCompanyId());
			return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, USER_HOST_URL + BATCH_INSERT_STRUCT, paraMap, null);
		} catch(Exception e) {
			logger.error("查询公司所有部门树形结构异常{}", e);
			return RestResponse.fail(RestErrorCode.SYSTEM_EXCEPTION);
		}
	}

	@RequestMapping(value="/updateStruct",method=RequestMethod.POST)
	public RestResponse updateStruct(@RequestBody Map<String,Object> paraMap,HttpServletRequest request) {
		try {
			ClientLoginInfo clientLoginInfo = LoginSessionUtil.getClientLoginInfo();
			paraMap.put("companyId", clientLoginInfo.getClientCompany().getCompanyId());
			return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, USER_HOST_URL + UPDATE_STRUCT, paraMap, null);
		} catch(Exception e) {
			logger.error("查询公司所有部门树形结构异常{}", e);
			return RestResponse.fail(RestErrorCode.SYSTEM_EXCEPTION);
		}
	}

	@RequestMapping(value="/deleteStruct",method=RequestMethod.POST)
	public RestResponse deleteStruct(@RequestBody Map<String,Object> paraMap,HttpServletRequest request) {
		try {
			LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
			paraMap.put("companyId", loginBaseInfo.obtainBelongCompanyId());
			paraMap.put("loginUserId", loginBaseInfo.obtainBaseInfo().getStaffId());
			paraMap.put("loginUserName", loginBaseInfo.obtainBaseInfo().getStaffName());

			return RestClient.requestInside(BaseHttpClient.HttpMethod.POST, USER_HOST_URL + DELETE_STRUCT, paraMap, null);
		} catch(Exception e) {
			logger.error("查询公司所有部门树形结构异常{}", e);
			return RestResponse.fail(RestErrorCode.SYSTEM_EXCEPTION);
		}
	}

	@RequestMapping(value = "/downLoadStructList", method = RequestMethod.POST)
	public void downloadStructList(HttpServletRequest request, HttpServletResponse response){
		LoginBaseInfo loginBaseInfo = LoginSessionUtil.getBaseLoginInfo();
		int companyId = loginBaseInfo.obtainBelongCompanyId();
		//获取企业名称
		String companyName = loginBaseInfo.obtainBelongCompanyName();
		try(XSSFWorkbook wb = new XSSFWorkbook()) {
			//查询该企业下的部门列表信息
			List<StructNodeDTO> structNodeDTOList = null;
			Map<String,Object> paraMap = new HashMap<>();
			paraMap.put("companyId", companyId);
			RestResponse restResponse= RestClient.requestForList(BaseHttpClient.HttpMethod.POST, USER_HOST_URL + QUERY_STRUCT_FOR_DOWNLOAD, paraMap, null,StructNodeDTO.class);
			if(restResponse.isSuccess()){
				structNodeDTOList = (List<StructNodeDTO>) restResponse.getData();
			}
			response.setContentType("application/vnd.ms-excel");
			response.setHeader("Content-disposition","attachment;filename*=utf-8''"  + URLEncoder.encode("部门-批量导出"+ ".xls", "UTF-8"));
			ServletOutputStream out = response.getOutputStream();
			BufferedOutputStream buffOut = null;
			XSSFSheet sheet = wb.createSheet("部门");
			XSSFRow row = sheet.createRow(0);
			row.createCell(0).setCellValue("部门名称");
			row.createCell(1).setCellValue("部门编号");
			row.createCell(2).setCellValue("上级部门名称");
			row.createCell(3).setCellValue("上级部门编号");
			if(structNodeDTOList == null || structNodeDTOList.size() == 0){
				//异常情况，企业下没有部门信息,则设置一个默认部门
				row = sheet.createRow(1);
				row.createCell(0).setCellValue("默认部门");
				row.createCell(1).setCellValue(ConstantCode.STRUCT_DEFAULT_NO);
				row.createCell(2).setCellValue(companyName);
			}else{
				int i = 0;
				for( i= 0;i<structNodeDTOList.size();i++){
					StructNodeDTO structNodeDTO = structNodeDTOList.get(i);
					StructNodeDTO parentStruct = null;
					row = sheet.createRow(i+1);

					//查找当前部门的父级部门信息
					if(null != structNodeDTO.getParentId() && structNodeDTO.getParentId() !=-1) {
						Optional<StructNodeDTO> parentStructList = structNodeDTOList.stream().filter(struct->struct.getStructId().equals(structNodeDTO.getParentId())).findFirst();
						if(parentStructList.isPresent()){
							parentStruct = parentStructList.get();
						}
					}
					row.createCell(0).setCellValue(structNodeDTO.getStructName() != null ? structNodeDTO.getStructName() : "");
					row.createCell(1).setCellValue(structNodeDTO.getStructNo() !=null ? structNodeDTO.getStructNo() : "");
					//判断上级部门是否为空
					if(parentStruct ==null){
						row.createCell(2).setCellValue("");
						row.createCell(3).setCellValue("");
					}else{
						row.createCell(2).setCellValue(parentStruct.getStructName()!=null ? parentStruct.getStructName() : "");
						row.createCell(3).setCellValue(parentStruct.getStructNo() != null ? parentStruct.getStructNo() : "");
					}
					// parent_id 为-1时，赋值为企业名称
					if(null != structNodeDTO.getParentId() && structNodeDTO.getParentId() ==-1){
						row.createCell(2).setCellValue(companyName);
					}
				}
			}
			buffOut = new BufferedOutputStream(out);
			wb.write(buffOut);
		} catch (Exception e) {
			logger.error("【部门管理】导出部门信息downLoadStructList error", e);
		}
	}


}
