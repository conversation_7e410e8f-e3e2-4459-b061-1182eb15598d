package com.izu.order.entity.mrcar;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class OrderInfoExample {
    /**
     * order_info
     */
    protected String orderByClause;

    /**
     * order_info
     */
    protected boolean distinct;

    /**
     * order_info
     */
    protected List<Criteria> oredCriteria;

    public OrderInfoExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNull() {
            addCriterion("order_no is null");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNotNull() {
            addCriterion("order_no is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNoEqualTo(String value) {
            addCriterion("order_no =", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotEqualTo(String value) {
            addCriterion("order_no <>", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThan(String value) {
            addCriterion("order_no >", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThanOrEqualTo(String value) {
            addCriterion("order_no >=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThan(String value) {
            addCriterion("order_no <", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThanOrEqualTo(String value) {
            addCriterion("order_no <=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLike(String value) {
            addCriterion("order_no like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotLike(String value) {
            addCriterion("order_no not like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoIn(List<String> values) {
            addCriterion("order_no in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotIn(List<String> values) {
            addCriterion("order_no not in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoBetween(String value1, String value2) {
            addCriterion("order_no between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotBetween(String value1, String value2) {
            addCriterion("order_no not between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderApplyNoIsNull() {
            addCriterion("order_apply_no is null");
            return (Criteria) this;
        }

        public Criteria andOrderApplyNoIsNotNull() {
            addCriterion("order_apply_no is not null");
            return (Criteria) this;
        }

        public Criteria andOrderApplyNoEqualTo(String value) {
            addCriterion("order_apply_no =", value, "orderApplyNo");
            return (Criteria) this;
        }

        public Criteria andOrderApplyNoNotEqualTo(String value) {
            addCriterion("order_apply_no <>", value, "orderApplyNo");
            return (Criteria) this;
        }

        public Criteria andOrderApplyNoGreaterThan(String value) {
            addCriterion("order_apply_no >", value, "orderApplyNo");
            return (Criteria) this;
        }

        public Criteria andOrderApplyNoGreaterThanOrEqualTo(String value) {
            addCriterion("order_apply_no >=", value, "orderApplyNo");
            return (Criteria) this;
        }

        public Criteria andOrderApplyNoLessThan(String value) {
            addCriterion("order_apply_no <", value, "orderApplyNo");
            return (Criteria) this;
        }

        public Criteria andOrderApplyNoLessThanOrEqualTo(String value) {
            addCriterion("order_apply_no <=", value, "orderApplyNo");
            return (Criteria) this;
        }

        public Criteria andOrderApplyNoLike(String value) {
            addCriterion("order_apply_no like", value, "orderApplyNo");
            return (Criteria) this;
        }

        public Criteria andOrderApplyNoNotLike(String value) {
            addCriterion("order_apply_no not like", value, "orderApplyNo");
            return (Criteria) this;
        }

        public Criteria andOrderApplyNoIn(List<String> values) {
            addCriterion("order_apply_no in", values, "orderApplyNo");
            return (Criteria) this;
        }

        public Criteria andOrderApplyNoNotIn(List<String> values) {
            addCriterion("order_apply_no not in", values, "orderApplyNo");
            return (Criteria) this;
        }

        public Criteria andOrderApplyNoBetween(String value1, String value2) {
            addCriterion("order_apply_no between", value1, value2, "orderApplyNo");
            return (Criteria) this;
        }

        public Criteria andOrderApplyNoNotBetween(String value1, String value2) {
            addCriterion("order_apply_no not between", value1, value2, "orderApplyNo");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Integer value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Integer value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Integer value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Integer value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Integer value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Integer> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Integer> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Integer value1, Integer value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Integer value1, Integer value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andStructIdIsNull() {
            addCriterion("struct_id is null");
            return (Criteria) this;
        }

        public Criteria andStructIdIsNotNull() {
            addCriterion("struct_id is not null");
            return (Criteria) this;
        }

        public Criteria andStructIdEqualTo(Integer value) {
            addCriterion("struct_id =", value, "structId");
            return (Criteria) this;
        }

        public Criteria andStructIdNotEqualTo(Integer value) {
            addCriterion("struct_id <>", value, "structId");
            return (Criteria) this;
        }

        public Criteria andStructIdGreaterThan(Integer value) {
            addCriterion("struct_id >", value, "structId");
            return (Criteria) this;
        }

        public Criteria andStructIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("struct_id >=", value, "structId");
            return (Criteria) this;
        }

        public Criteria andStructIdLessThan(Integer value) {
            addCriterion("struct_id <", value, "structId");
            return (Criteria) this;
        }

        public Criteria andStructIdLessThanOrEqualTo(Integer value) {
            addCriterion("struct_id <=", value, "structId");
            return (Criteria) this;
        }

        public Criteria andStructIdIn(List<Integer> values) {
            addCriterion("struct_id in", values, "structId");
            return (Criteria) this;
        }

        public Criteria andStructIdNotIn(List<Integer> values) {
            addCriterion("struct_id not in", values, "structId");
            return (Criteria) this;
        }

        public Criteria andStructIdBetween(Integer value1, Integer value2) {
            addCriterion("struct_id between", value1, value2, "structId");
            return (Criteria) this;
        }

        public Criteria andStructIdNotBetween(Integer value1, Integer value2) {
            addCriterion("struct_id not between", value1, value2, "structId");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIsNull() {
            addCriterion("order_type is null");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIsNotNull() {
            addCriterion("order_type is not null");
            return (Criteria) this;
        }

        public Criteria andOrderTypeEqualTo(Byte value) {
            addCriterion("order_type =", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotEqualTo(Byte value) {
            addCriterion("order_type <>", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeGreaterThan(Byte value) {
            addCriterion("order_type >", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("order_type >=", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeLessThan(Byte value) {
            addCriterion("order_type <", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeLessThanOrEqualTo(Byte value) {
            addCriterion("order_type <=", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIn(List<Byte> values) {
            addCriterion("order_type in", values, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotIn(List<Byte> values) {
            addCriterion("order_type not in", values, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeBetween(Byte value1, Byte value2) {
            addCriterion("order_type between", value1, value2, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("order_type not between", value1, value2, "orderType");
            return (Criteria) this;
        }

        public Criteria andServiceCodeIsNull() {
            addCriterion("service_code is null");
            return (Criteria) this;
        }

        public Criteria andServiceCodeIsNotNull() {
            addCriterion("service_code is not null");
            return (Criteria) this;
        }

        public Criteria andServiceCodeEqualTo(String value) {
            addCriterion("service_code =", value, "serviceCode");
            return (Criteria) this;
        }

        public Criteria andServiceCodeNotEqualTo(String value) {
            addCriterion("service_code <>", value, "serviceCode");
            return (Criteria) this;
        }

        public Criteria andServiceCodeGreaterThan(String value) {
            addCriterion("service_code >", value, "serviceCode");
            return (Criteria) this;
        }

        public Criteria andServiceCodeGreaterThanOrEqualTo(String value) {
            addCriterion("service_code >=", value, "serviceCode");
            return (Criteria) this;
        }

        public Criteria andServiceCodeLessThan(String value) {
            addCriterion("service_code <", value, "serviceCode");
            return (Criteria) this;
        }

        public Criteria andServiceCodeLessThanOrEqualTo(String value) {
            addCriterion("service_code <=", value, "serviceCode");
            return (Criteria) this;
        }

        public Criteria andServiceCodeLike(String value) {
            addCriterion("service_code like", value, "serviceCode");
            return (Criteria) this;
        }

        public Criteria andServiceCodeNotLike(String value) {
            addCriterion("service_code not like", value, "serviceCode");
            return (Criteria) this;
        }

        public Criteria andServiceCodeIn(List<String> values) {
            addCriterion("service_code in", values, "serviceCode");
            return (Criteria) this;
        }

        public Criteria andServiceCodeNotIn(List<String> values) {
            addCriterion("service_code not in", values, "serviceCode");
            return (Criteria) this;
        }

        public Criteria andServiceCodeBetween(String value1, String value2) {
            addCriterion("service_code between", value1, value2, "serviceCode");
            return (Criteria) this;
        }

        public Criteria andServiceCodeNotBetween(String value1, String value2) {
            addCriterion("service_code not between", value1, value2, "serviceCode");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNull() {
            addCriterion("customer_id is null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNotNull() {
            addCriterion("customer_id is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdEqualTo(Integer value) {
            addCriterion("customer_id =", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotEqualTo(Integer value) {
            addCriterion("customer_id <>", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThan(Integer value) {
            addCriterion("customer_id >", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("customer_id >=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThan(Integer value) {
            addCriterion("customer_id <", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThanOrEqualTo(Integer value) {
            addCriterion("customer_id <=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIn(List<Integer> values) {
            addCriterion("customer_id in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotIn(List<Integer> values) {
            addCriterion("customer_id not in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdBetween(Integer value1, Integer value2) {
            addCriterion("customer_id between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotBetween(Integer value1, Integer value2) {
            addCriterion("customer_id not between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNull() {
            addCriterion("customer_name is null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNotNull() {
            addCriterion("customer_name is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameEqualTo(String value) {
            addCriterion("customer_name =", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotEqualTo(String value) {
            addCriterion("customer_name <>", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThan(String value) {
            addCriterion("customer_name >", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThanOrEqualTo(String value) {
            addCriterion("customer_name >=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThan(String value) {
            addCriterion("customer_name <", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThanOrEqualTo(String value) {
            addCriterion("customer_name <=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLike(String value) {
            addCriterion("customer_name like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotLike(String value) {
            addCriterion("customer_name not like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIn(List<String> values) {
            addCriterion("customer_name in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotIn(List<String> values) {
            addCriterion("customer_name not in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameBetween(String value1, String value2) {
            addCriterion("customer_name between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotBetween(String value1, String value2) {
            addCriterion("customer_name not between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileIsNull() {
            addCriterion("customer_mobile is null");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileIsNotNull() {
            addCriterion("customer_mobile is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileEqualTo(String value) {
            addCriterion("customer_mobile =", value, "customerMobile");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileNotEqualTo(String value) {
            addCriterion("customer_mobile <>", value, "customerMobile");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileGreaterThan(String value) {
            addCriterion("customer_mobile >", value, "customerMobile");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileGreaterThanOrEqualTo(String value) {
            addCriterion("customer_mobile >=", value, "customerMobile");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileLessThan(String value) {
            addCriterion("customer_mobile <", value, "customerMobile");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileLessThanOrEqualTo(String value) {
            addCriterion("customer_mobile <=", value, "customerMobile");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileLike(String value) {
            addCriterion("customer_mobile like", value, "customerMobile");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileNotLike(String value) {
            addCriterion("customer_mobile not like", value, "customerMobile");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileIn(List<String> values) {
            addCriterion("customer_mobile in", values, "customerMobile");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileNotIn(List<String> values) {
            addCriterion("customer_mobile not in", values, "customerMobile");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileBetween(String value1, String value2) {
            addCriterion("customer_mobile between", value1, value2, "customerMobile");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileNotBetween(String value1, String value2) {
            addCriterion("customer_mobile not between", value1, value2, "customerMobile");
            return (Criteria) this;
        }

        public Criteria andHimselfIsNull() {
            addCriterion("himself is null");
            return (Criteria) this;
        }

        public Criteria andHimselfIsNotNull() {
            addCriterion("himself is not null");
            return (Criteria) this;
        }

        public Criteria andHimselfEqualTo(Boolean value) {
            addCriterion("himself =", value, "himself");
            return (Criteria) this;
        }

        public Criteria andHimselfNotEqualTo(Boolean value) {
            addCriterion("himself <>", value, "himself");
            return (Criteria) this;
        }

        public Criteria andHimselfGreaterThan(Boolean value) {
            addCriterion("himself >", value, "himself");
            return (Criteria) this;
        }

        public Criteria andHimselfGreaterThanOrEqualTo(Boolean value) {
            addCriterion("himself >=", value, "himself");
            return (Criteria) this;
        }

        public Criteria andHimselfLessThan(Boolean value) {
            addCriterion("himself <", value, "himself");
            return (Criteria) this;
        }

        public Criteria andHimselfLessThanOrEqualTo(Boolean value) {
            addCriterion("himself <=", value, "himself");
            return (Criteria) this;
        }

        public Criteria andHimselfIn(List<Boolean> values) {
            addCriterion("himself in", values, "himself");
            return (Criteria) this;
        }

        public Criteria andHimselfNotIn(List<Boolean> values) {
            addCriterion("himself not in", values, "himself");
            return (Criteria) this;
        }

        public Criteria andHimselfBetween(Boolean value1, Boolean value2) {
            addCriterion("himself between", value1, value2, "himself");
            return (Criteria) this;
        }

        public Criteria andHimselfNotBetween(Boolean value1, Boolean value2) {
            addCriterion("himself not between", value1, value2, "himself");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserIdIsNull() {
            addCriterion("booking_passenger_user_id is null");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserIdIsNotNull() {
            addCriterion("booking_passenger_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserIdEqualTo(Long value) {
            addCriterion("booking_passenger_user_id =", value, "bookingPassengerUserId");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserIdNotEqualTo(Long value) {
            addCriterion("booking_passenger_user_id <>", value, "bookingPassengerUserId");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserIdGreaterThan(Long value) {
            addCriterion("booking_passenger_user_id >", value, "bookingPassengerUserId");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserIdGreaterThanOrEqualTo(Long value) {
            addCriterion("booking_passenger_user_id >=", value, "bookingPassengerUserId");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserIdLessThan(Long value) {
            addCriterion("booking_passenger_user_id <", value, "bookingPassengerUserId");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserIdLessThanOrEqualTo(Long value) {
            addCriterion("booking_passenger_user_id <=", value, "bookingPassengerUserId");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserIdIn(List<Long> values) {
            addCriterion("booking_passenger_user_id in", values, "bookingPassengerUserId");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserIdNotIn(List<Long> values) {
            addCriterion("booking_passenger_user_id not in", values, "bookingPassengerUserId");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserIdBetween(Long value1, Long value2) {
            addCriterion("booking_passenger_user_id between", value1, value2, "bookingPassengerUserId");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserIdNotBetween(Long value1, Long value2) {
            addCriterion("booking_passenger_user_id not between", value1, value2, "bookingPassengerUserId");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserNameIsNull() {
            addCriterion("booking_passenger_user_name is null");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserNameIsNotNull() {
            addCriterion("booking_passenger_user_name is not null");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserNameEqualTo(String value) {
            addCriterion("booking_passenger_user_name =", value, "bookingPassengerUserName");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserNameNotEqualTo(String value) {
            addCriterion("booking_passenger_user_name <>", value, "bookingPassengerUserName");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserNameGreaterThan(String value) {
            addCriterion("booking_passenger_user_name >", value, "bookingPassengerUserName");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserNameGreaterThanOrEqualTo(String value) {
            addCriterion("booking_passenger_user_name >=", value, "bookingPassengerUserName");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserNameLessThan(String value) {
            addCriterion("booking_passenger_user_name <", value, "bookingPassengerUserName");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserNameLessThanOrEqualTo(String value) {
            addCriterion("booking_passenger_user_name <=", value, "bookingPassengerUserName");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserNameLike(String value) {
            addCriterion("booking_passenger_user_name like", value, "bookingPassengerUserName");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserNameNotLike(String value) {
            addCriterion("booking_passenger_user_name not like", value, "bookingPassengerUserName");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserNameIn(List<String> values) {
            addCriterion("booking_passenger_user_name in", values, "bookingPassengerUserName");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserNameNotIn(List<String> values) {
            addCriterion("booking_passenger_user_name not in", values, "bookingPassengerUserName");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserNameBetween(String value1, String value2) {
            addCriterion("booking_passenger_user_name between", value1, value2, "bookingPassengerUserName");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserNameNotBetween(String value1, String value2) {
            addCriterion("booking_passenger_user_name not between", value1, value2, "bookingPassengerUserName");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserPhoneIsNull() {
            addCriterion("booking_passenger_user_phone is null");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserPhoneIsNotNull() {
            addCriterion("booking_passenger_user_phone is not null");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserPhoneEqualTo(String value) {
            addCriterion("booking_passenger_user_phone =", value, "bookingPassengerUserPhone");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserPhoneNotEqualTo(String value) {
            addCriterion("booking_passenger_user_phone <>", value, "bookingPassengerUserPhone");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserPhoneGreaterThan(String value) {
            addCriterion("booking_passenger_user_phone >", value, "bookingPassengerUserPhone");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("booking_passenger_user_phone >=", value, "bookingPassengerUserPhone");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserPhoneLessThan(String value) {
            addCriterion("booking_passenger_user_phone <", value, "bookingPassengerUserPhone");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserPhoneLessThanOrEqualTo(String value) {
            addCriterion("booking_passenger_user_phone <=", value, "bookingPassengerUserPhone");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserPhoneLike(String value) {
            addCriterion("booking_passenger_user_phone like", value, "bookingPassengerUserPhone");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserPhoneNotLike(String value) {
            addCriterion("booking_passenger_user_phone not like", value, "bookingPassengerUserPhone");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserPhoneIn(List<String> values) {
            addCriterion("booking_passenger_user_phone in", values, "bookingPassengerUserPhone");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserPhoneNotIn(List<String> values) {
            addCriterion("booking_passenger_user_phone not in", values, "bookingPassengerUserPhone");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserPhoneBetween(String value1, String value2) {
            addCriterion("booking_passenger_user_phone between", value1, value2, "bookingPassengerUserPhone");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerUserPhoneNotBetween(String value1, String value2) {
            addCriterion("booking_passenger_user_phone not between", value1, value2, "bookingPassengerUserPhone");
            return (Criteria) this;
        }

        public Criteria andBookingOrderStimeIsNull() {
            addCriterion("booking_order_stime is null");
            return (Criteria) this;
        }

        public Criteria andBookingOrderStimeIsNotNull() {
            addCriterion("booking_order_stime is not null");
            return (Criteria) this;
        }

        public Criteria andBookingOrderStimeEqualTo(Date value) {
            addCriterion("booking_order_stime =", value, "bookingOrderStime");
            return (Criteria) this;
        }

        public Criteria andBookingOrderStimeNotEqualTo(Date value) {
            addCriterion("booking_order_stime <>", value, "bookingOrderStime");
            return (Criteria) this;
        }

        public Criteria andBookingOrderStimeGreaterThan(Date value) {
            addCriterion("booking_order_stime >", value, "bookingOrderStime");
            return (Criteria) this;
        }

        public Criteria andBookingOrderStimeGreaterThanOrEqualTo(Date value) {
            addCriterion("booking_order_stime >=", value, "bookingOrderStime");
            return (Criteria) this;
        }

        public Criteria andBookingOrderStimeLessThan(Date value) {
            addCriterion("booking_order_stime <", value, "bookingOrderStime");
            return (Criteria) this;
        }

        public Criteria andBookingOrderStimeLessThanOrEqualTo(Date value) {
            addCriterion("booking_order_stime <=", value, "bookingOrderStime");
            return (Criteria) this;
        }

        public Criteria andBookingOrderStimeIn(List<Date> values) {
            addCriterion("booking_order_stime in", values, "bookingOrderStime");
            return (Criteria) this;
        }

        public Criteria andBookingOrderStimeNotIn(List<Date> values) {
            addCriterion("booking_order_stime not in", values, "bookingOrderStime");
            return (Criteria) this;
        }

        public Criteria andBookingOrderStimeBetween(Date value1, Date value2) {
            addCriterion("booking_order_stime between", value1, value2, "bookingOrderStime");
            return (Criteria) this;
        }

        public Criteria andBookingOrderStimeNotBetween(Date value1, Date value2) {
            addCriterion("booking_order_stime not between", value1, value2, "bookingOrderStime");
            return (Criteria) this;
        }

        public Criteria andBookingOrderEtimeIsNull() {
            addCriterion("booking_order_etime is null");
            return (Criteria) this;
        }

        public Criteria andBookingOrderEtimeIsNotNull() {
            addCriterion("booking_order_etime is not null");
            return (Criteria) this;
        }

        public Criteria andBookingOrderEtimeEqualTo(Date value) {
            addCriterion("booking_order_etime =", value, "bookingOrderEtime");
            return (Criteria) this;
        }

        public Criteria andBookingOrderEtimeNotEqualTo(Date value) {
            addCriterion("booking_order_etime <>", value, "bookingOrderEtime");
            return (Criteria) this;
        }

        public Criteria andBookingOrderEtimeGreaterThan(Date value) {
            addCriterion("booking_order_etime >", value, "bookingOrderEtime");
            return (Criteria) this;
        }

        public Criteria andBookingOrderEtimeGreaterThanOrEqualTo(Date value) {
            addCriterion("booking_order_etime >=", value, "bookingOrderEtime");
            return (Criteria) this;
        }

        public Criteria andBookingOrderEtimeLessThan(Date value) {
            addCriterion("booking_order_etime <", value, "bookingOrderEtime");
            return (Criteria) this;
        }

        public Criteria andBookingOrderEtimeLessThanOrEqualTo(Date value) {
            addCriterion("booking_order_etime <=", value, "bookingOrderEtime");
            return (Criteria) this;
        }

        public Criteria andBookingOrderEtimeIn(List<Date> values) {
            addCriterion("booking_order_etime in", values, "bookingOrderEtime");
            return (Criteria) this;
        }

        public Criteria andBookingOrderEtimeNotIn(List<Date> values) {
            addCriterion("booking_order_etime not in", values, "bookingOrderEtime");
            return (Criteria) this;
        }

        public Criteria andBookingOrderEtimeBetween(Date value1, Date value2) {
            addCriterion("booking_order_etime between", value1, value2, "bookingOrderEtime");
            return (Criteria) this;
        }

        public Criteria andBookingOrderEtimeNotBetween(Date value1, Date value2) {
            addCriterion("booking_order_etime not between", value1, value2, "bookingOrderEtime");
            return (Criteria) this;
        }

        public Criteria andChannelOrderCodeIsNull() {
            addCriterion("channel_order_code is null");
            return (Criteria) this;
        }

        public Criteria andChannelOrderCodeIsNotNull() {
            addCriterion("channel_order_code is not null");
            return (Criteria) this;
        }

        public Criteria andChannelOrderCodeEqualTo(String value) {
            addCriterion("channel_order_code =", value, "channelOrderCode");
            return (Criteria) this;
        }

        public Criteria andChannelOrderCodeNotEqualTo(String value) {
            addCriterion("channel_order_code <>", value, "channelOrderCode");
            return (Criteria) this;
        }

        public Criteria andChannelOrderCodeGreaterThan(String value) {
            addCriterion("channel_order_code >", value, "channelOrderCode");
            return (Criteria) this;
        }

        public Criteria andChannelOrderCodeGreaterThanOrEqualTo(String value) {
            addCriterion("channel_order_code >=", value, "channelOrderCode");
            return (Criteria) this;
        }

        public Criteria andChannelOrderCodeLessThan(String value) {
            addCriterion("channel_order_code <", value, "channelOrderCode");
            return (Criteria) this;
        }

        public Criteria andChannelOrderCodeLessThanOrEqualTo(String value) {
            addCriterion("channel_order_code <=", value, "channelOrderCode");
            return (Criteria) this;
        }

        public Criteria andChannelOrderCodeLike(String value) {
            addCriterion("channel_order_code like", value, "channelOrderCode");
            return (Criteria) this;
        }

        public Criteria andChannelOrderCodeNotLike(String value) {
            addCriterion("channel_order_code not like", value, "channelOrderCode");
            return (Criteria) this;
        }

        public Criteria andChannelOrderCodeIn(List<String> values) {
            addCriterion("channel_order_code in", values, "channelOrderCode");
            return (Criteria) this;
        }

        public Criteria andChannelOrderCodeNotIn(List<String> values) {
            addCriterion("channel_order_code not in", values, "channelOrderCode");
            return (Criteria) this;
        }

        public Criteria andChannelOrderCodeBetween(String value1, String value2) {
            addCriterion("channel_order_code between", value1, value2, "channelOrderCode");
            return (Criteria) this;
        }

        public Criteria andChannelOrderCodeNotBetween(String value1, String value2) {
            addCriterion("channel_order_code not between", value1, value2, "channelOrderCode");
            return (Criteria) this;
        }

        public Criteria andBookingStartLongAddrIsNull() {
            addCriterion("booking_start_long_addr is null");
            return (Criteria) this;
        }

        public Criteria andBookingStartLongAddrIsNotNull() {
            addCriterion("booking_start_long_addr is not null");
            return (Criteria) this;
        }

        public Criteria andBookingStartLongAddrEqualTo(String value) {
            addCriterion("booking_start_long_addr =", value, "bookingStartLongAddr");
            return (Criteria) this;
        }

        public Criteria andBookingStartLongAddrNotEqualTo(String value) {
            addCriterion("booking_start_long_addr <>", value, "bookingStartLongAddr");
            return (Criteria) this;
        }

        public Criteria andBookingStartLongAddrGreaterThan(String value) {
            addCriterion("booking_start_long_addr >", value, "bookingStartLongAddr");
            return (Criteria) this;
        }

        public Criteria andBookingStartLongAddrGreaterThanOrEqualTo(String value) {
            addCriterion("booking_start_long_addr >=", value, "bookingStartLongAddr");
            return (Criteria) this;
        }

        public Criteria andBookingStartLongAddrLessThan(String value) {
            addCriterion("booking_start_long_addr <", value, "bookingStartLongAddr");
            return (Criteria) this;
        }

        public Criteria andBookingStartLongAddrLessThanOrEqualTo(String value) {
            addCriterion("booking_start_long_addr <=", value, "bookingStartLongAddr");
            return (Criteria) this;
        }

        public Criteria andBookingStartLongAddrLike(String value) {
            addCriterion("booking_start_long_addr like", value, "bookingStartLongAddr");
            return (Criteria) this;
        }

        public Criteria andBookingStartLongAddrNotLike(String value) {
            addCriterion("booking_start_long_addr not like", value, "bookingStartLongAddr");
            return (Criteria) this;
        }

        public Criteria andBookingStartLongAddrIn(List<String> values) {
            addCriterion("booking_start_long_addr in", values, "bookingStartLongAddr");
            return (Criteria) this;
        }

        public Criteria andBookingStartLongAddrNotIn(List<String> values) {
            addCriterion("booking_start_long_addr not in", values, "bookingStartLongAddr");
            return (Criteria) this;
        }

        public Criteria andBookingStartLongAddrBetween(String value1, String value2) {
            addCriterion("booking_start_long_addr between", value1, value2, "bookingStartLongAddr");
            return (Criteria) this;
        }

        public Criteria andBookingStartLongAddrNotBetween(String value1, String value2) {
            addCriterion("booking_start_long_addr not between", value1, value2, "bookingStartLongAddr");
            return (Criteria) this;
        }

        public Criteria andBookingStartShortAddrIsNull() {
            addCriterion("booking_start_short_addr is null");
            return (Criteria) this;
        }

        public Criteria andBookingStartShortAddrIsNotNull() {
            addCriterion("booking_start_short_addr is not null");
            return (Criteria) this;
        }

        public Criteria andBookingStartShortAddrEqualTo(String value) {
            addCriterion("booking_start_short_addr =", value, "bookingStartShortAddr");
            return (Criteria) this;
        }

        public Criteria andBookingStartShortAddrNotEqualTo(String value) {
            addCriterion("booking_start_short_addr <>", value, "bookingStartShortAddr");
            return (Criteria) this;
        }

        public Criteria andBookingStartShortAddrGreaterThan(String value) {
            addCriterion("booking_start_short_addr >", value, "bookingStartShortAddr");
            return (Criteria) this;
        }

        public Criteria andBookingStartShortAddrGreaterThanOrEqualTo(String value) {
            addCriterion("booking_start_short_addr >=", value, "bookingStartShortAddr");
            return (Criteria) this;
        }

        public Criteria andBookingStartShortAddrLessThan(String value) {
            addCriterion("booking_start_short_addr <", value, "bookingStartShortAddr");
            return (Criteria) this;
        }

        public Criteria andBookingStartShortAddrLessThanOrEqualTo(String value) {
            addCriterion("booking_start_short_addr <=", value, "bookingStartShortAddr");
            return (Criteria) this;
        }

        public Criteria andBookingStartShortAddrLike(String value) {
            addCriterion("booking_start_short_addr like", value, "bookingStartShortAddr");
            return (Criteria) this;
        }

        public Criteria andBookingStartShortAddrNotLike(String value) {
            addCriterion("booking_start_short_addr not like", value, "bookingStartShortAddr");
            return (Criteria) this;
        }

        public Criteria andBookingStartShortAddrIn(List<String> values) {
            addCriterion("booking_start_short_addr in", values, "bookingStartShortAddr");
            return (Criteria) this;
        }

        public Criteria andBookingStartShortAddrNotIn(List<String> values) {
            addCriterion("booking_start_short_addr not in", values, "bookingStartShortAddr");
            return (Criteria) this;
        }

        public Criteria andBookingStartShortAddrBetween(String value1, String value2) {
            addCriterion("booking_start_short_addr between", value1, value2, "bookingStartShortAddr");
            return (Criteria) this;
        }

        public Criteria andBookingStartShortAddrNotBetween(String value1, String value2) {
            addCriterion("booking_start_short_addr not between", value1, value2, "bookingStartShortAddr");
            return (Criteria) this;
        }

        public Criteria andBookingStartPointIsNull() {
            addCriterion("booking_start_point is null");
            return (Criteria) this;
        }

        public Criteria andBookingStartPointIsNotNull() {
            addCriterion("booking_start_point is not null");
            return (Criteria) this;
        }

        public Criteria andBookingStartPointEqualTo(String value) {
            addCriterion("booking_start_point =", value, "bookingStartPoint");
            return (Criteria) this;
        }

        public Criteria andBookingStartPointNotEqualTo(String value) {
            addCriterion("booking_start_point <>", value, "bookingStartPoint");
            return (Criteria) this;
        }

        public Criteria andBookingStartPointGreaterThan(String value) {
            addCriterion("booking_start_point >", value, "bookingStartPoint");
            return (Criteria) this;
        }

        public Criteria andBookingStartPointGreaterThanOrEqualTo(String value) {
            addCriterion("booking_start_point >=", value, "bookingStartPoint");
            return (Criteria) this;
        }

        public Criteria andBookingStartPointLessThan(String value) {
            addCriterion("booking_start_point <", value, "bookingStartPoint");
            return (Criteria) this;
        }

        public Criteria andBookingStartPointLessThanOrEqualTo(String value) {
            addCriterion("booking_start_point <=", value, "bookingStartPoint");
            return (Criteria) this;
        }

        public Criteria andBookingStartPointLike(String value) {
            addCriterion("booking_start_point like", value, "bookingStartPoint");
            return (Criteria) this;
        }

        public Criteria andBookingStartPointNotLike(String value) {
            addCriterion("booking_start_point not like", value, "bookingStartPoint");
            return (Criteria) this;
        }

        public Criteria andBookingStartPointIn(List<String> values) {
            addCriterion("booking_start_point in", values, "bookingStartPoint");
            return (Criteria) this;
        }

        public Criteria andBookingStartPointNotIn(List<String> values) {
            addCriterion("booking_start_point not in", values, "bookingStartPoint");
            return (Criteria) this;
        }

        public Criteria andBookingStartPointBetween(String value1, String value2) {
            addCriterion("booking_start_point between", value1, value2, "bookingStartPoint");
            return (Criteria) this;
        }

        public Criteria andBookingStartPointNotBetween(String value1, String value2) {
            addCriterion("booking_start_point not between", value1, value2, "bookingStartPoint");
            return (Criteria) this;
        }

        public Criteria andBookingEndLongAddrIsNull() {
            addCriterion("booking_end_long_addr is null");
            return (Criteria) this;
        }

        public Criteria andBookingEndLongAddrIsNotNull() {
            addCriterion("booking_end_long_addr is not null");
            return (Criteria) this;
        }

        public Criteria andBookingEndLongAddrEqualTo(String value) {
            addCriterion("booking_end_long_addr =", value, "bookingEndLongAddr");
            return (Criteria) this;
        }

        public Criteria andBookingEndLongAddrNotEqualTo(String value) {
            addCriterion("booking_end_long_addr <>", value, "bookingEndLongAddr");
            return (Criteria) this;
        }

        public Criteria andBookingEndLongAddrGreaterThan(String value) {
            addCriterion("booking_end_long_addr >", value, "bookingEndLongAddr");
            return (Criteria) this;
        }

        public Criteria andBookingEndLongAddrGreaterThanOrEqualTo(String value) {
            addCriterion("booking_end_long_addr >=", value, "bookingEndLongAddr");
            return (Criteria) this;
        }

        public Criteria andBookingEndLongAddrLessThan(String value) {
            addCriterion("booking_end_long_addr <", value, "bookingEndLongAddr");
            return (Criteria) this;
        }

        public Criteria andBookingEndLongAddrLessThanOrEqualTo(String value) {
            addCriterion("booking_end_long_addr <=", value, "bookingEndLongAddr");
            return (Criteria) this;
        }

        public Criteria andBookingEndLongAddrLike(String value) {
            addCriterion("booking_end_long_addr like", value, "bookingEndLongAddr");
            return (Criteria) this;
        }

        public Criteria andBookingEndLongAddrNotLike(String value) {
            addCriterion("booking_end_long_addr not like", value, "bookingEndLongAddr");
            return (Criteria) this;
        }

        public Criteria andBookingEndLongAddrIn(List<String> values) {
            addCriterion("booking_end_long_addr in", values, "bookingEndLongAddr");
            return (Criteria) this;
        }

        public Criteria andBookingEndLongAddrNotIn(List<String> values) {
            addCriterion("booking_end_long_addr not in", values, "bookingEndLongAddr");
            return (Criteria) this;
        }

        public Criteria andBookingEndLongAddrBetween(String value1, String value2) {
            addCriterion("booking_end_long_addr between", value1, value2, "bookingEndLongAddr");
            return (Criteria) this;
        }

        public Criteria andBookingEndLongAddrNotBetween(String value1, String value2) {
            addCriterion("booking_end_long_addr not between", value1, value2, "bookingEndLongAddr");
            return (Criteria) this;
        }

        public Criteria andBookingEndShortAddrIsNull() {
            addCriterion("booking_end_short_addr is null");
            return (Criteria) this;
        }

        public Criteria andBookingEndShortAddrIsNotNull() {
            addCriterion("booking_end_short_addr is not null");
            return (Criteria) this;
        }

        public Criteria andBookingEndShortAddrEqualTo(String value) {
            addCriterion("booking_end_short_addr =", value, "bookingEndShortAddr");
            return (Criteria) this;
        }

        public Criteria andBookingEndShortAddrNotEqualTo(String value) {
            addCriterion("booking_end_short_addr <>", value, "bookingEndShortAddr");
            return (Criteria) this;
        }

        public Criteria andBookingEndShortAddrGreaterThan(String value) {
            addCriterion("booking_end_short_addr >", value, "bookingEndShortAddr");
            return (Criteria) this;
        }

        public Criteria andBookingEndShortAddrGreaterThanOrEqualTo(String value) {
            addCriterion("booking_end_short_addr >=", value, "bookingEndShortAddr");
            return (Criteria) this;
        }

        public Criteria andBookingEndShortAddrLessThan(String value) {
            addCriterion("booking_end_short_addr <", value, "bookingEndShortAddr");
            return (Criteria) this;
        }

        public Criteria andBookingEndShortAddrLessThanOrEqualTo(String value) {
            addCriterion("booking_end_short_addr <=", value, "bookingEndShortAddr");
            return (Criteria) this;
        }

        public Criteria andBookingEndShortAddrLike(String value) {
            addCriterion("booking_end_short_addr like", value, "bookingEndShortAddr");
            return (Criteria) this;
        }

        public Criteria andBookingEndShortAddrNotLike(String value) {
            addCriterion("booking_end_short_addr not like", value, "bookingEndShortAddr");
            return (Criteria) this;
        }

        public Criteria andBookingEndShortAddrIn(List<String> values) {
            addCriterion("booking_end_short_addr in", values, "bookingEndShortAddr");
            return (Criteria) this;
        }

        public Criteria andBookingEndShortAddrNotIn(List<String> values) {
            addCriterion("booking_end_short_addr not in", values, "bookingEndShortAddr");
            return (Criteria) this;
        }

        public Criteria andBookingEndShortAddrBetween(String value1, String value2) {
            addCriterion("booking_end_short_addr between", value1, value2, "bookingEndShortAddr");
            return (Criteria) this;
        }

        public Criteria andBookingEndShortAddrNotBetween(String value1, String value2) {
            addCriterion("booking_end_short_addr not between", value1, value2, "bookingEndShortAddr");
            return (Criteria) this;
        }

        public Criteria andBookingEndPointIsNull() {
            addCriterion("booking_end_point is null");
            return (Criteria) this;
        }

        public Criteria andBookingEndPointIsNotNull() {
            addCriterion("booking_end_point is not null");
            return (Criteria) this;
        }

        public Criteria andBookingEndPointEqualTo(String value) {
            addCriterion("booking_end_point =", value, "bookingEndPoint");
            return (Criteria) this;
        }

        public Criteria andBookingEndPointNotEqualTo(String value) {
            addCriterion("booking_end_point <>", value, "bookingEndPoint");
            return (Criteria) this;
        }

        public Criteria andBookingEndPointGreaterThan(String value) {
            addCriterion("booking_end_point >", value, "bookingEndPoint");
            return (Criteria) this;
        }

        public Criteria andBookingEndPointGreaterThanOrEqualTo(String value) {
            addCriterion("booking_end_point >=", value, "bookingEndPoint");
            return (Criteria) this;
        }

        public Criteria andBookingEndPointLessThan(String value) {
            addCriterion("booking_end_point <", value, "bookingEndPoint");
            return (Criteria) this;
        }

        public Criteria andBookingEndPointLessThanOrEqualTo(String value) {
            addCriterion("booking_end_point <=", value, "bookingEndPoint");
            return (Criteria) this;
        }

        public Criteria andBookingEndPointLike(String value) {
            addCriterion("booking_end_point like", value, "bookingEndPoint");
            return (Criteria) this;
        }

        public Criteria andBookingEndPointNotLike(String value) {
            addCriterion("booking_end_point not like", value, "bookingEndPoint");
            return (Criteria) this;
        }

        public Criteria andBookingEndPointIn(List<String> values) {
            addCriterion("booking_end_point in", values, "bookingEndPoint");
            return (Criteria) this;
        }

        public Criteria andBookingEndPointNotIn(List<String> values) {
            addCriterion("booking_end_point not in", values, "bookingEndPoint");
            return (Criteria) this;
        }

        public Criteria andBookingEndPointBetween(String value1, String value2) {
            addCriterion("booking_end_point between", value1, value2, "bookingEndPoint");
            return (Criteria) this;
        }

        public Criteria andBookingEndPointNotBetween(String value1, String value2) {
            addCriterion("booking_end_point not between", value1, value2, "bookingEndPoint");
            return (Criteria) this;
        }

        public Criteria andStartCityCodeIsNull() {
            addCriterion("start_city_code is null");
            return (Criteria) this;
        }

        public Criteria andStartCityCodeIsNotNull() {
            addCriterion("start_city_code is not null");
            return (Criteria) this;
        }

        public Criteria andStartCityCodeEqualTo(Integer value) {
            addCriterion("start_city_code =", value, "startCityCode");
            return (Criteria) this;
        }

        public Criteria andStartCityCodeNotEqualTo(Integer value) {
            addCriterion("start_city_code <>", value, "startCityCode");
            return (Criteria) this;
        }

        public Criteria andStartCityCodeGreaterThan(Integer value) {
            addCriterion("start_city_code >", value, "startCityCode");
            return (Criteria) this;
        }

        public Criteria andStartCityCodeGreaterThanOrEqualTo(Integer value) {
            addCriterion("start_city_code >=", value, "startCityCode");
            return (Criteria) this;
        }

        public Criteria andStartCityCodeLessThan(Integer value) {
            addCriterion("start_city_code <", value, "startCityCode");
            return (Criteria) this;
        }

        public Criteria andStartCityCodeLessThanOrEqualTo(Integer value) {
            addCriterion("start_city_code <=", value, "startCityCode");
            return (Criteria) this;
        }

        public Criteria andStartCityCodeIn(List<Integer> values) {
            addCriterion("start_city_code in", values, "startCityCode");
            return (Criteria) this;
        }

        public Criteria andStartCityCodeNotIn(List<Integer> values) {
            addCriterion("start_city_code not in", values, "startCityCode");
            return (Criteria) this;
        }

        public Criteria andStartCityCodeBetween(Integer value1, Integer value2) {
            addCriterion("start_city_code between", value1, value2, "startCityCode");
            return (Criteria) this;
        }

        public Criteria andStartCityCodeNotBetween(Integer value1, Integer value2) {
            addCriterion("start_city_code not between", value1, value2, "startCityCode");
            return (Criteria) this;
        }

        public Criteria andStartCityNameIsNull() {
            addCriterion("start_city_name is null");
            return (Criteria) this;
        }

        public Criteria andStartCityNameIsNotNull() {
            addCriterion("start_city_name is not null");
            return (Criteria) this;
        }

        public Criteria andStartCityNameEqualTo(String value) {
            addCriterion("start_city_name =", value, "startCityName");
            return (Criteria) this;
        }

        public Criteria andStartCityNameNotEqualTo(String value) {
            addCriterion("start_city_name <>", value, "startCityName");
            return (Criteria) this;
        }

        public Criteria andStartCityNameGreaterThan(String value) {
            addCriterion("start_city_name >", value, "startCityName");
            return (Criteria) this;
        }

        public Criteria andStartCityNameGreaterThanOrEqualTo(String value) {
            addCriterion("start_city_name >=", value, "startCityName");
            return (Criteria) this;
        }

        public Criteria andStartCityNameLessThan(String value) {
            addCriterion("start_city_name <", value, "startCityName");
            return (Criteria) this;
        }

        public Criteria andStartCityNameLessThanOrEqualTo(String value) {
            addCriterion("start_city_name <=", value, "startCityName");
            return (Criteria) this;
        }

        public Criteria andStartCityNameLike(String value) {
            addCriterion("start_city_name like", value, "startCityName");
            return (Criteria) this;
        }

        public Criteria andStartCityNameNotLike(String value) {
            addCriterion("start_city_name not like", value, "startCityName");
            return (Criteria) this;
        }

        public Criteria andStartCityNameIn(List<String> values) {
            addCriterion("start_city_name in", values, "startCityName");
            return (Criteria) this;
        }

        public Criteria andStartCityNameNotIn(List<String> values) {
            addCriterion("start_city_name not in", values, "startCityName");
            return (Criteria) this;
        }

        public Criteria andStartCityNameBetween(String value1, String value2) {
            addCriterion("start_city_name between", value1, value2, "startCityName");
            return (Criteria) this;
        }

        public Criteria andStartCityNameNotBetween(String value1, String value2) {
            addCriterion("start_city_name not between", value1, value2, "startCityName");
            return (Criteria) this;
        }

        public Criteria andEndCityCodeIsNull() {
            addCriterion("end_city_code is null");
            return (Criteria) this;
        }

        public Criteria andEndCityCodeIsNotNull() {
            addCriterion("end_city_code is not null");
            return (Criteria) this;
        }

        public Criteria andEndCityCodeEqualTo(Integer value) {
            addCriterion("end_city_code =", value, "endCityCode");
            return (Criteria) this;
        }

        public Criteria andEndCityCodeNotEqualTo(Integer value) {
            addCriterion("end_city_code <>", value, "endCityCode");
            return (Criteria) this;
        }

        public Criteria andEndCityCodeGreaterThan(Integer value) {
            addCriterion("end_city_code >", value, "endCityCode");
            return (Criteria) this;
        }

        public Criteria andEndCityCodeGreaterThanOrEqualTo(Integer value) {
            addCriterion("end_city_code >=", value, "endCityCode");
            return (Criteria) this;
        }

        public Criteria andEndCityCodeLessThan(Integer value) {
            addCriterion("end_city_code <", value, "endCityCode");
            return (Criteria) this;
        }

        public Criteria andEndCityCodeLessThanOrEqualTo(Integer value) {
            addCriterion("end_city_code <=", value, "endCityCode");
            return (Criteria) this;
        }

        public Criteria andEndCityCodeIn(List<Integer> values) {
            addCriterion("end_city_code in", values, "endCityCode");
            return (Criteria) this;
        }

        public Criteria andEndCityCodeNotIn(List<Integer> values) {
            addCriterion("end_city_code not in", values, "endCityCode");
            return (Criteria) this;
        }

        public Criteria andEndCityCodeBetween(Integer value1, Integer value2) {
            addCriterion("end_city_code between", value1, value2, "endCityCode");
            return (Criteria) this;
        }

        public Criteria andEndCityCodeNotBetween(Integer value1, Integer value2) {
            addCriterion("end_city_code not between", value1, value2, "endCityCode");
            return (Criteria) this;
        }

        public Criteria andEndCityNameIsNull() {
            addCriterion("end_city_name is null");
            return (Criteria) this;
        }

        public Criteria andEndCityNameIsNotNull() {
            addCriterion("end_city_name is not null");
            return (Criteria) this;
        }

        public Criteria andEndCityNameEqualTo(String value) {
            addCriterion("end_city_name =", value, "endCityName");
            return (Criteria) this;
        }

        public Criteria andEndCityNameNotEqualTo(String value) {
            addCriterion("end_city_name <>", value, "endCityName");
            return (Criteria) this;
        }

        public Criteria andEndCityNameGreaterThan(String value) {
            addCriterion("end_city_name >", value, "endCityName");
            return (Criteria) this;
        }

        public Criteria andEndCityNameGreaterThanOrEqualTo(String value) {
            addCriterion("end_city_name >=", value, "endCityName");
            return (Criteria) this;
        }

        public Criteria andEndCityNameLessThan(String value) {
            addCriterion("end_city_name <", value, "endCityName");
            return (Criteria) this;
        }

        public Criteria andEndCityNameLessThanOrEqualTo(String value) {
            addCriterion("end_city_name <=", value, "endCityName");
            return (Criteria) this;
        }

        public Criteria andEndCityNameLike(String value) {
            addCriterion("end_city_name like", value, "endCityName");
            return (Criteria) this;
        }

        public Criteria andEndCityNameNotLike(String value) {
            addCriterion("end_city_name not like", value, "endCityName");
            return (Criteria) this;
        }

        public Criteria andEndCityNameIn(List<String> values) {
            addCriterion("end_city_name in", values, "endCityName");
            return (Criteria) this;
        }

        public Criteria andEndCityNameNotIn(List<String> values) {
            addCriterion("end_city_name not in", values, "endCityName");
            return (Criteria) this;
        }

        public Criteria andEndCityNameBetween(String value1, String value2) {
            addCriterion("end_city_name between", value1, value2, "endCityName");
            return (Criteria) this;
        }

        public Criteria andEndCityNameNotBetween(String value1, String value2) {
            addCriterion("end_city_name not between", value1, value2, "endCityName");
            return (Criteria) this;
        }

        public Criteria andBookingCarlevelIdIsNull() {
            addCriterion("booking_carlevel_id is null");
            return (Criteria) this;
        }

        public Criteria andBookingCarlevelIdIsNotNull() {
            addCriterion("booking_carlevel_id is not null");
            return (Criteria) this;
        }

        public Criteria andBookingCarlevelIdEqualTo(Byte value) {
            addCriterion("booking_carlevel_id =", value, "bookingCarlevelId");
            return (Criteria) this;
        }

        public Criteria andBookingCarlevelIdNotEqualTo(Byte value) {
            addCriterion("booking_carlevel_id <>", value, "bookingCarlevelId");
            return (Criteria) this;
        }

        public Criteria andBookingCarlevelIdGreaterThan(Byte value) {
            addCriterion("booking_carlevel_id >", value, "bookingCarlevelId");
            return (Criteria) this;
        }

        public Criteria andBookingCarlevelIdGreaterThanOrEqualTo(Byte value) {
            addCriterion("booking_carlevel_id >=", value, "bookingCarlevelId");
            return (Criteria) this;
        }

        public Criteria andBookingCarlevelIdLessThan(Byte value) {
            addCriterion("booking_carlevel_id <", value, "bookingCarlevelId");
            return (Criteria) this;
        }

        public Criteria andBookingCarlevelIdLessThanOrEqualTo(Byte value) {
            addCriterion("booking_carlevel_id <=", value, "bookingCarlevelId");
            return (Criteria) this;
        }

        public Criteria andBookingCarlevelIdIn(List<Byte> values) {
            addCriterion("booking_carlevel_id in", values, "bookingCarlevelId");
            return (Criteria) this;
        }

        public Criteria andBookingCarlevelIdNotIn(List<Byte> values) {
            addCriterion("booking_carlevel_id not in", values, "bookingCarlevelId");
            return (Criteria) this;
        }

        public Criteria andBookingCarlevelIdBetween(Byte value1, Byte value2) {
            addCriterion("booking_carlevel_id between", value1, value2, "bookingCarlevelId");
            return (Criteria) this;
        }

        public Criteria andBookingCarlevelIdNotBetween(Byte value1, Byte value2) {
            addCriterion("booking_carlevel_id not between", value1, value2, "bookingCarlevelId");
            return (Criteria) this;
        }

        public Criteria andBookingCarlevelNameIsNull() {
            addCriterion("booking_carlevel_name is null");
            return (Criteria) this;
        }

        public Criteria andBookingCarlevelNameIsNotNull() {
            addCriterion("booking_carlevel_name is not null");
            return (Criteria) this;
        }

        public Criteria andBookingCarlevelNameEqualTo(String value) {
            addCriterion("booking_carlevel_name =", value, "bookingCarlevelName");
            return (Criteria) this;
        }

        public Criteria andBookingCarlevelNameNotEqualTo(String value) {
            addCriterion("booking_carlevel_name <>", value, "bookingCarlevelName");
            return (Criteria) this;
        }

        public Criteria andBookingCarlevelNameGreaterThan(String value) {
            addCriterion("booking_carlevel_name >", value, "bookingCarlevelName");
            return (Criteria) this;
        }

        public Criteria andBookingCarlevelNameGreaterThanOrEqualTo(String value) {
            addCriterion("booking_carlevel_name >=", value, "bookingCarlevelName");
            return (Criteria) this;
        }

        public Criteria andBookingCarlevelNameLessThan(String value) {
            addCriterion("booking_carlevel_name <", value, "bookingCarlevelName");
            return (Criteria) this;
        }

        public Criteria andBookingCarlevelNameLessThanOrEqualTo(String value) {
            addCriterion("booking_carlevel_name <=", value, "bookingCarlevelName");
            return (Criteria) this;
        }

        public Criteria andBookingCarlevelNameLike(String value) {
            addCriterion("booking_carlevel_name like", value, "bookingCarlevelName");
            return (Criteria) this;
        }

        public Criteria andBookingCarlevelNameNotLike(String value) {
            addCriterion("booking_carlevel_name not like", value, "bookingCarlevelName");
            return (Criteria) this;
        }

        public Criteria andBookingCarlevelNameIn(List<String> values) {
            addCriterion("booking_carlevel_name in", values, "bookingCarlevelName");
            return (Criteria) this;
        }

        public Criteria andBookingCarlevelNameNotIn(List<String> values) {
            addCriterion("booking_carlevel_name not in", values, "bookingCarlevelName");
            return (Criteria) this;
        }

        public Criteria andBookingCarlevelNameBetween(String value1, String value2) {
            addCriterion("booking_carlevel_name between", value1, value2, "bookingCarlevelName");
            return (Criteria) this;
        }

        public Criteria andBookingCarlevelNameNotBetween(String value1, String value2) {
            addCriterion("booking_carlevel_name not between", value1, value2, "bookingCarlevelName");
            return (Criteria) this;
        }

        public Criteria andOrderCancellationTypeIsNull() {
            addCriterion("order_cancellation_type is null");
            return (Criteria) this;
        }

        public Criteria andOrderCancellationTypeIsNotNull() {
            addCriterion("order_cancellation_type is not null");
            return (Criteria) this;
        }

        public Criteria andOrderCancellationTypeEqualTo(Byte value) {
            addCriterion("order_cancellation_type =", value, "orderCancellationType");
            return (Criteria) this;
        }

        public Criteria andOrderCancellationTypeNotEqualTo(Byte value) {
            addCriterion("order_cancellation_type <>", value, "orderCancellationType");
            return (Criteria) this;
        }

        public Criteria andOrderCancellationTypeGreaterThan(Byte value) {
            addCriterion("order_cancellation_type >", value, "orderCancellationType");
            return (Criteria) this;
        }

        public Criteria andOrderCancellationTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("order_cancellation_type >=", value, "orderCancellationType");
            return (Criteria) this;
        }

        public Criteria andOrderCancellationTypeLessThan(Byte value) {
            addCriterion("order_cancellation_type <", value, "orderCancellationType");
            return (Criteria) this;
        }

        public Criteria andOrderCancellationTypeLessThanOrEqualTo(Byte value) {
            addCriterion("order_cancellation_type <=", value, "orderCancellationType");
            return (Criteria) this;
        }

        public Criteria andOrderCancellationTypeIn(List<Byte> values) {
            addCriterion("order_cancellation_type in", values, "orderCancellationType");
            return (Criteria) this;
        }

        public Criteria andOrderCancellationTypeNotIn(List<Byte> values) {
            addCriterion("order_cancellation_type not in", values, "orderCancellationType");
            return (Criteria) this;
        }

        public Criteria andOrderCancellationTypeBetween(Byte value1, Byte value2) {
            addCriterion("order_cancellation_type between", value1, value2, "orderCancellationType");
            return (Criteria) this;
        }

        public Criteria andOrderCancellationTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("order_cancellation_type not between", value1, value2, "orderCancellationType");
            return (Criteria) this;
        }

        public Criteria andAssignCarCanAbleIsNull() {
            addCriterion("assign_car_can_able is null");
            return (Criteria) this;
        }

        public Criteria andAssignCarCanAbleIsNotNull() {
            addCriterion("assign_car_can_able is not null");
            return (Criteria) this;
        }

        public Criteria andAssignCarCanAbleEqualTo(Boolean value) {
            addCriterion("assign_car_can_able =", value, "assignCarCanAble");
            return (Criteria) this;
        }

        public Criteria andAssignCarCanAbleNotEqualTo(Boolean value) {
            addCriterion("assign_car_can_able <>", value, "assignCarCanAble");
            return (Criteria) this;
        }

        public Criteria andAssignCarCanAbleGreaterThan(Boolean value) {
            addCriterion("assign_car_can_able >", value, "assignCarCanAble");
            return (Criteria) this;
        }

        public Criteria andAssignCarCanAbleGreaterThanOrEqualTo(Boolean value) {
            addCriterion("assign_car_can_able >=", value, "assignCarCanAble");
            return (Criteria) this;
        }

        public Criteria andAssignCarCanAbleLessThan(Boolean value) {
            addCriterion("assign_car_can_able <", value, "assignCarCanAble");
            return (Criteria) this;
        }

        public Criteria andAssignCarCanAbleLessThanOrEqualTo(Boolean value) {
            addCriterion("assign_car_can_able <=", value, "assignCarCanAble");
            return (Criteria) this;
        }

        public Criteria andAssignCarCanAbleIn(List<Boolean> values) {
            addCriterion("assign_car_can_able in", values, "assignCarCanAble");
            return (Criteria) this;
        }

        public Criteria andAssignCarCanAbleNotIn(List<Boolean> values) {
            addCriterion("assign_car_can_able not in", values, "assignCarCanAble");
            return (Criteria) this;
        }

        public Criteria andAssignCarCanAbleBetween(Boolean value1, Boolean value2) {
            addCriterion("assign_car_can_able between", value1, value2, "assignCarCanAble");
            return (Criteria) this;
        }

        public Criteria andAssignCarCanAbleNotBetween(Boolean value1, Boolean value2) {
            addCriterion("assign_car_can_able not between", value1, value2, "assignCarCanAble");
            return (Criteria) this;
        }

        public Criteria andAssignCarStatusIsNull() {
            addCriterion("assign_car_status is null");
            return (Criteria) this;
        }

        public Criteria andAssignCarStatusIsNotNull() {
            addCriterion("assign_car_status is not null");
            return (Criteria) this;
        }

        public Criteria andAssignCarStatusEqualTo(Byte value) {
            addCriterion("assign_car_status =", value, "assignCarStatus");
            return (Criteria) this;
        }

        public Criteria andAssignCarStatusNotEqualTo(Byte value) {
            addCriterion("assign_car_status <>", value, "assignCarStatus");
            return (Criteria) this;
        }

        public Criteria andAssignCarStatusGreaterThan(Byte value) {
            addCriterion("assign_car_status >", value, "assignCarStatus");
            return (Criteria) this;
        }

        public Criteria andAssignCarStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("assign_car_status >=", value, "assignCarStatus");
            return (Criteria) this;
        }

        public Criteria andAssignCarStatusLessThan(Byte value) {
            addCriterion("assign_car_status <", value, "assignCarStatus");
            return (Criteria) this;
        }

        public Criteria andAssignCarStatusLessThanOrEqualTo(Byte value) {
            addCriterion("assign_car_status <=", value, "assignCarStatus");
            return (Criteria) this;
        }

        public Criteria andAssignCarStatusIn(List<Byte> values) {
            addCriterion("assign_car_status in", values, "assignCarStatus");
            return (Criteria) this;
        }

        public Criteria andAssignCarStatusNotIn(List<Byte> values) {
            addCriterion("assign_car_status not in", values, "assignCarStatus");
            return (Criteria) this;
        }

        public Criteria andAssignCarStatusBetween(Byte value1, Byte value2) {
            addCriterion("assign_car_status between", value1, value2, "assignCarStatus");
            return (Criteria) this;
        }

        public Criteria andAssignCarStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("assign_car_status not between", value1, value2, "assignCarStatus");
            return (Criteria) this;
        }

        public Criteria andAssignCarApplyTimeIsNull() {
            addCriterion("assign_car_apply_time is null");
            return (Criteria) this;
        }

        public Criteria andAssignCarApplyTimeIsNotNull() {
            addCriterion("assign_car_apply_time is not null");
            return (Criteria) this;
        }

        public Criteria andAssignCarApplyTimeEqualTo(Date value) {
            addCriterion("assign_car_apply_time =", value, "assignCarApplyTime");
            return (Criteria) this;
        }

        public Criteria andAssignCarApplyTimeNotEqualTo(Date value) {
            addCriterion("assign_car_apply_time <>", value, "assignCarApplyTime");
            return (Criteria) this;
        }

        public Criteria andAssignCarApplyTimeGreaterThan(Date value) {
            addCriterion("assign_car_apply_time >", value, "assignCarApplyTime");
            return (Criteria) this;
        }

        public Criteria andAssignCarApplyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("assign_car_apply_time >=", value, "assignCarApplyTime");
            return (Criteria) this;
        }

        public Criteria andAssignCarApplyTimeLessThan(Date value) {
            addCriterion("assign_car_apply_time <", value, "assignCarApplyTime");
            return (Criteria) this;
        }

        public Criteria andAssignCarApplyTimeLessThanOrEqualTo(Date value) {
            addCriterion("assign_car_apply_time <=", value, "assignCarApplyTime");
            return (Criteria) this;
        }

        public Criteria andAssignCarApplyTimeIn(List<Date> values) {
            addCriterion("assign_car_apply_time in", values, "assignCarApplyTime");
            return (Criteria) this;
        }

        public Criteria andAssignCarApplyTimeNotIn(List<Date> values) {
            addCriterion("assign_car_apply_time not in", values, "assignCarApplyTime");
            return (Criteria) this;
        }

        public Criteria andAssignCarApplyTimeBetween(Date value1, Date value2) {
            addCriterion("assign_car_apply_time between", value1, value2, "assignCarApplyTime");
            return (Criteria) this;
        }

        public Criteria andAssignCarApplyTimeNotBetween(Date value1, Date value2) {
            addCriterion("assign_car_apply_time not between", value1, value2, "assignCarApplyTime");
            return (Criteria) this;
        }

        public Criteria andAssignCarApplyCountIsNull() {
            addCriterion("assign_car_apply_count is null");
            return (Criteria) this;
        }

        public Criteria andAssignCarApplyCountIsNotNull() {
            addCriterion("assign_car_apply_count is not null");
            return (Criteria) this;
        }

        public Criteria andAssignCarApplyCountEqualTo(Short value) {
            addCriterion("assign_car_apply_count =", value, "assignCarApplyCount");
            return (Criteria) this;
        }

        public Criteria andAssignCarApplyCountNotEqualTo(Short value) {
            addCriterion("assign_car_apply_count <>", value, "assignCarApplyCount");
            return (Criteria) this;
        }

        public Criteria andAssignCarApplyCountGreaterThan(Short value) {
            addCriterion("assign_car_apply_count >", value, "assignCarApplyCount");
            return (Criteria) this;
        }

        public Criteria andAssignCarApplyCountGreaterThanOrEqualTo(Short value) {
            addCriterion("assign_car_apply_count >=", value, "assignCarApplyCount");
            return (Criteria) this;
        }

        public Criteria andAssignCarApplyCountLessThan(Short value) {
            addCriterion("assign_car_apply_count <", value, "assignCarApplyCount");
            return (Criteria) this;
        }

        public Criteria andAssignCarApplyCountLessThanOrEqualTo(Short value) {
            addCriterion("assign_car_apply_count <=", value, "assignCarApplyCount");
            return (Criteria) this;
        }

        public Criteria andAssignCarApplyCountIn(List<Short> values) {
            addCriterion("assign_car_apply_count in", values, "assignCarApplyCount");
            return (Criteria) this;
        }

        public Criteria andAssignCarApplyCountNotIn(List<Short> values) {
            addCriterion("assign_car_apply_count not in", values, "assignCarApplyCount");
            return (Criteria) this;
        }

        public Criteria andAssignCarApplyCountBetween(Short value1, Short value2) {
            addCriterion("assign_car_apply_count between", value1, value2, "assignCarApplyCount");
            return (Criteria) this;
        }

        public Criteria andAssignCarApplyCountNotBetween(Short value1, Short value2) {
            addCriterion("assign_car_apply_count not between", value1, value2, "assignCarApplyCount");
            return (Criteria) this;
        }

        public Criteria andAssignCarSuccessTimeIsNull() {
            addCriterion("assign_car_success_time is null");
            return (Criteria) this;
        }

        public Criteria andAssignCarSuccessTimeIsNotNull() {
            addCriterion("assign_car_success_time is not null");
            return (Criteria) this;
        }

        public Criteria andAssignCarSuccessTimeEqualTo(Date value) {
            addCriterion("assign_car_success_time =", value, "assignCarSuccessTime");
            return (Criteria) this;
        }

        public Criteria andAssignCarSuccessTimeNotEqualTo(Date value) {
            addCriterion("assign_car_success_time <>", value, "assignCarSuccessTime");
            return (Criteria) this;
        }

        public Criteria andAssignCarSuccessTimeGreaterThan(Date value) {
            addCriterion("assign_car_success_time >", value, "assignCarSuccessTime");
            return (Criteria) this;
        }

        public Criteria andAssignCarSuccessTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("assign_car_success_time >=", value, "assignCarSuccessTime");
            return (Criteria) this;
        }

        public Criteria andAssignCarSuccessTimeLessThan(Date value) {
            addCriterion("assign_car_success_time <", value, "assignCarSuccessTime");
            return (Criteria) this;
        }

        public Criteria andAssignCarSuccessTimeLessThanOrEqualTo(Date value) {
            addCriterion("assign_car_success_time <=", value, "assignCarSuccessTime");
            return (Criteria) this;
        }

        public Criteria andAssignCarSuccessTimeIn(List<Date> values) {
            addCriterion("assign_car_success_time in", values, "assignCarSuccessTime");
            return (Criteria) this;
        }

        public Criteria andAssignCarSuccessTimeNotIn(List<Date> values) {
            addCriterion("assign_car_success_time not in", values, "assignCarSuccessTime");
            return (Criteria) this;
        }

        public Criteria andAssignCarSuccessTimeBetween(Date value1, Date value2) {
            addCriterion("assign_car_success_time between", value1, value2, "assignCarSuccessTime");
            return (Criteria) this;
        }

        public Criteria andAssignCarSuccessTimeNotBetween(Date value1, Date value2) {
            addCriterion("assign_car_success_time not between", value1, value2, "assignCarSuccessTime");
            return (Criteria) this;
        }

        public Criteria andAssignCarFailureTimeIsNull() {
            addCriterion("assign_car_failure_time is null");
            return (Criteria) this;
        }

        public Criteria andAssignCarFailureTimeIsNotNull() {
            addCriterion("assign_car_failure_time is not null");
            return (Criteria) this;
        }

        public Criteria andAssignCarFailureTimeEqualTo(Date value) {
            addCriterion("assign_car_failure_time =", value, "assignCarFailureTime");
            return (Criteria) this;
        }

        public Criteria andAssignCarFailureTimeNotEqualTo(Date value) {
            addCriterion("assign_car_failure_time <>", value, "assignCarFailureTime");
            return (Criteria) this;
        }

        public Criteria andAssignCarFailureTimeGreaterThan(Date value) {
            addCriterion("assign_car_failure_time >", value, "assignCarFailureTime");
            return (Criteria) this;
        }

        public Criteria andAssignCarFailureTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("assign_car_failure_time >=", value, "assignCarFailureTime");
            return (Criteria) this;
        }

        public Criteria andAssignCarFailureTimeLessThan(Date value) {
            addCriterion("assign_car_failure_time <", value, "assignCarFailureTime");
            return (Criteria) this;
        }

        public Criteria andAssignCarFailureTimeLessThanOrEqualTo(Date value) {
            addCriterion("assign_car_failure_time <=", value, "assignCarFailureTime");
            return (Criteria) this;
        }

        public Criteria andAssignCarFailureTimeIn(List<Date> values) {
            addCriterion("assign_car_failure_time in", values, "assignCarFailureTime");
            return (Criteria) this;
        }

        public Criteria andAssignCarFailureTimeNotIn(List<Date> values) {
            addCriterion("assign_car_failure_time not in", values, "assignCarFailureTime");
            return (Criteria) this;
        }

        public Criteria andAssignCarFailureTimeBetween(Date value1, Date value2) {
            addCriterion("assign_car_failure_time between", value1, value2, "assignCarFailureTime");
            return (Criteria) this;
        }

        public Criteria andAssignCarFailureTimeNotBetween(Date value1, Date value2) {
            addCriterion("assign_car_failure_time not between", value1, value2, "assignCarFailureTime");
            return (Criteria) this;
        }

        public Criteria andAssignCarlevelIdIsNull() {
            addCriterion("assign_carlevel_id is null");
            return (Criteria) this;
        }

        public Criteria andAssignCarlevelIdIsNotNull() {
            addCriterion("assign_carlevel_id is not null");
            return (Criteria) this;
        }

        public Criteria andAssignCarlevelIdEqualTo(Byte value) {
            addCriterion("assign_carlevel_id =", value, "assignCarlevelId");
            return (Criteria) this;
        }

        public Criteria andAssignCarlevelIdNotEqualTo(Byte value) {
            addCriterion("assign_carlevel_id <>", value, "assignCarlevelId");
            return (Criteria) this;
        }

        public Criteria andAssignCarlevelIdGreaterThan(Byte value) {
            addCriterion("assign_carlevel_id >", value, "assignCarlevelId");
            return (Criteria) this;
        }

        public Criteria andAssignCarlevelIdGreaterThanOrEqualTo(Byte value) {
            addCriterion("assign_carlevel_id >=", value, "assignCarlevelId");
            return (Criteria) this;
        }

        public Criteria andAssignCarlevelIdLessThan(Byte value) {
            addCriterion("assign_carlevel_id <", value, "assignCarlevelId");
            return (Criteria) this;
        }

        public Criteria andAssignCarlevelIdLessThanOrEqualTo(Byte value) {
            addCriterion("assign_carlevel_id <=", value, "assignCarlevelId");
            return (Criteria) this;
        }

        public Criteria andAssignCarlevelIdIn(List<Byte> values) {
            addCriterion("assign_carlevel_id in", values, "assignCarlevelId");
            return (Criteria) this;
        }

        public Criteria andAssignCarlevelIdNotIn(List<Byte> values) {
            addCriterion("assign_carlevel_id not in", values, "assignCarlevelId");
            return (Criteria) this;
        }

        public Criteria andAssignCarlevelIdBetween(Byte value1, Byte value2) {
            addCriterion("assign_carlevel_id between", value1, value2, "assignCarlevelId");
            return (Criteria) this;
        }

        public Criteria andAssignCarlevelIdNotBetween(Byte value1, Byte value2) {
            addCriterion("assign_carlevel_id not between", value1, value2, "assignCarlevelId");
            return (Criteria) this;
        }

        public Criteria andAssignCarlevelNameIsNull() {
            addCriterion("assign_carlevel_name is null");
            return (Criteria) this;
        }

        public Criteria andAssignCarlevelNameIsNotNull() {
            addCriterion("assign_carlevel_name is not null");
            return (Criteria) this;
        }

        public Criteria andAssignCarlevelNameEqualTo(String value) {
            addCriterion("assign_carlevel_name =", value, "assignCarlevelName");
            return (Criteria) this;
        }

        public Criteria andAssignCarlevelNameNotEqualTo(String value) {
            addCriterion("assign_carlevel_name <>", value, "assignCarlevelName");
            return (Criteria) this;
        }

        public Criteria andAssignCarlevelNameGreaterThan(String value) {
            addCriterion("assign_carlevel_name >", value, "assignCarlevelName");
            return (Criteria) this;
        }

        public Criteria andAssignCarlevelNameGreaterThanOrEqualTo(String value) {
            addCriterion("assign_carlevel_name >=", value, "assignCarlevelName");
            return (Criteria) this;
        }

        public Criteria andAssignCarlevelNameLessThan(String value) {
            addCriterion("assign_carlevel_name <", value, "assignCarlevelName");
            return (Criteria) this;
        }

        public Criteria andAssignCarlevelNameLessThanOrEqualTo(String value) {
            addCriterion("assign_carlevel_name <=", value, "assignCarlevelName");
            return (Criteria) this;
        }

        public Criteria andAssignCarlevelNameLike(String value) {
            addCriterion("assign_carlevel_name like", value, "assignCarlevelName");
            return (Criteria) this;
        }

        public Criteria andAssignCarlevelNameNotLike(String value) {
            addCriterion("assign_carlevel_name not like", value, "assignCarlevelName");
            return (Criteria) this;
        }

        public Criteria andAssignCarlevelNameIn(List<String> values) {
            addCriterion("assign_carlevel_name in", values, "assignCarlevelName");
            return (Criteria) this;
        }

        public Criteria andAssignCarlevelNameNotIn(List<String> values) {
            addCriterion("assign_carlevel_name not in", values, "assignCarlevelName");
            return (Criteria) this;
        }

        public Criteria andAssignCarlevelNameBetween(String value1, String value2) {
            addCriterion("assign_carlevel_name between", value1, value2, "assignCarlevelName");
            return (Criteria) this;
        }

        public Criteria andAssignCarlevelNameNotBetween(String value1, String value2) {
            addCriterion("assign_carlevel_name not between", value1, value2, "assignCarlevelName");
            return (Criteria) this;
        }

        public Criteria andAssignCarIdIsNull() {
            addCriterion("assign_car_id is null");
            return (Criteria) this;
        }

        public Criteria andAssignCarIdIsNotNull() {
            addCriterion("assign_car_id is not null");
            return (Criteria) this;
        }

        public Criteria andAssignCarIdEqualTo(Long value) {
            addCriterion("assign_car_id =", value, "assignCarId");
            return (Criteria) this;
        }

        public Criteria andAssignCarIdNotEqualTo(Long value) {
            addCriterion("assign_car_id <>", value, "assignCarId");
            return (Criteria) this;
        }

        public Criteria andAssignCarIdGreaterThan(Long value) {
            addCriterion("assign_car_id >", value, "assignCarId");
            return (Criteria) this;
        }

        public Criteria andAssignCarIdGreaterThanOrEqualTo(Long value) {
            addCriterion("assign_car_id >=", value, "assignCarId");
            return (Criteria) this;
        }

        public Criteria andAssignCarIdLessThan(Long value) {
            addCriterion("assign_car_id <", value, "assignCarId");
            return (Criteria) this;
        }

        public Criteria andAssignCarIdLessThanOrEqualTo(Long value) {
            addCriterion("assign_car_id <=", value, "assignCarId");
            return (Criteria) this;
        }

        public Criteria andAssignCarIdIn(List<Long> values) {
            addCriterion("assign_car_id in", values, "assignCarId");
            return (Criteria) this;
        }

        public Criteria andAssignCarIdNotIn(List<Long> values) {
            addCriterion("assign_car_id not in", values, "assignCarId");
            return (Criteria) this;
        }

        public Criteria andAssignCarIdBetween(Long value1, Long value2) {
            addCriterion("assign_car_id between", value1, value2, "assignCarId");
            return (Criteria) this;
        }

        public Criteria andAssignCarIdNotBetween(Long value1, Long value2) {
            addCriterion("assign_car_id not between", value1, value2, "assignCarId");
            return (Criteria) this;
        }

        public Criteria andAssignCarLicenseIsNull() {
            addCriterion("assign_car_license is null");
            return (Criteria) this;
        }

        public Criteria andAssignCarLicenseIsNotNull() {
            addCriterion("assign_car_license is not null");
            return (Criteria) this;
        }

        public Criteria andAssignCarLicenseEqualTo(String value) {
            addCriterion("assign_car_license =", value, "assignCarLicense");
            return (Criteria) this;
        }

        public Criteria andAssignCarLicenseNotEqualTo(String value) {
            addCriterion("assign_car_license <>", value, "assignCarLicense");
            return (Criteria) this;
        }

        public Criteria andAssignCarLicenseGreaterThan(String value) {
            addCriterion("assign_car_license >", value, "assignCarLicense");
            return (Criteria) this;
        }

        public Criteria andAssignCarLicenseGreaterThanOrEqualTo(String value) {
            addCriterion("assign_car_license >=", value, "assignCarLicense");
            return (Criteria) this;
        }

        public Criteria andAssignCarLicenseLessThan(String value) {
            addCriterion("assign_car_license <", value, "assignCarLicense");
            return (Criteria) this;
        }

        public Criteria andAssignCarLicenseLessThanOrEqualTo(String value) {
            addCriterion("assign_car_license <=", value, "assignCarLicense");
            return (Criteria) this;
        }

        public Criteria andAssignCarLicenseLike(String value) {
            addCriterion("assign_car_license like", value, "assignCarLicense");
            return (Criteria) this;
        }

        public Criteria andAssignCarLicenseNotLike(String value) {
            addCriterion("assign_car_license not like", value, "assignCarLicense");
            return (Criteria) this;
        }

        public Criteria andAssignCarLicenseIn(List<String> values) {
            addCriterion("assign_car_license in", values, "assignCarLicense");
            return (Criteria) this;
        }

        public Criteria andAssignCarLicenseNotIn(List<String> values) {
            addCriterion("assign_car_license not in", values, "assignCarLicense");
            return (Criteria) this;
        }

        public Criteria andAssignCarLicenseBetween(String value1, String value2) {
            addCriterion("assign_car_license between", value1, value2, "assignCarLicense");
            return (Criteria) this;
        }

        public Criteria andAssignCarLicenseNotBetween(String value1, String value2) {
            addCriterion("assign_car_license not between", value1, value2, "assignCarLicense");
            return (Criteria) this;
        }

        public Criteria andAssignDriverIdIsNull() {
            addCriterion("assign_driver_id is null");
            return (Criteria) this;
        }

        public Criteria andAssignDriverIdIsNotNull() {
            addCriterion("assign_driver_id is not null");
            return (Criteria) this;
        }

        public Criteria andAssignDriverIdEqualTo(Long value) {
            addCriterion("assign_driver_id =", value, "assignDriverId");
            return (Criteria) this;
        }

        public Criteria andAssignDriverIdNotEqualTo(Long value) {
            addCriterion("assign_driver_id <>", value, "assignDriverId");
            return (Criteria) this;
        }

        public Criteria andAssignDriverIdGreaterThan(Long value) {
            addCriterion("assign_driver_id >", value, "assignDriverId");
            return (Criteria) this;
        }

        public Criteria andAssignDriverIdGreaterThanOrEqualTo(Long value) {
            addCriterion("assign_driver_id >=", value, "assignDriverId");
            return (Criteria) this;
        }

        public Criteria andAssignDriverIdLessThan(Long value) {
            addCriterion("assign_driver_id <", value, "assignDriverId");
            return (Criteria) this;
        }

        public Criteria andAssignDriverIdLessThanOrEqualTo(Long value) {
            addCriterion("assign_driver_id <=", value, "assignDriverId");
            return (Criteria) this;
        }

        public Criteria andAssignDriverIdIn(List<Long> values) {
            addCriterion("assign_driver_id in", values, "assignDriverId");
            return (Criteria) this;
        }

        public Criteria andAssignDriverIdNotIn(List<Long> values) {
            addCriterion("assign_driver_id not in", values, "assignDriverId");
            return (Criteria) this;
        }

        public Criteria andAssignDriverIdBetween(Long value1, Long value2) {
            addCriterion("assign_driver_id between", value1, value2, "assignDriverId");
            return (Criteria) this;
        }

        public Criteria andAssignDriverIdNotBetween(Long value1, Long value2) {
            addCriterion("assign_driver_id not between", value1, value2, "assignDriverId");
            return (Criteria) this;
        }

        public Criteria andAssignDriverNameIsNull() {
            addCriterion("assign_driver_name is null");
            return (Criteria) this;
        }

        public Criteria andAssignDriverNameIsNotNull() {
            addCriterion("assign_driver_name is not null");
            return (Criteria) this;
        }

        public Criteria andAssignDriverNameEqualTo(String value) {
            addCriterion("assign_driver_name =", value, "assignDriverName");
            return (Criteria) this;
        }

        public Criteria andAssignDriverNameNotEqualTo(String value) {
            addCriterion("assign_driver_name <>", value, "assignDriverName");
            return (Criteria) this;
        }

        public Criteria andAssignDriverNameGreaterThan(String value) {
            addCriterion("assign_driver_name >", value, "assignDriverName");
            return (Criteria) this;
        }

        public Criteria andAssignDriverNameGreaterThanOrEqualTo(String value) {
            addCriterion("assign_driver_name >=", value, "assignDriverName");
            return (Criteria) this;
        }

        public Criteria andAssignDriverNameLessThan(String value) {
            addCriterion("assign_driver_name <", value, "assignDriverName");
            return (Criteria) this;
        }

        public Criteria andAssignDriverNameLessThanOrEqualTo(String value) {
            addCriterion("assign_driver_name <=", value, "assignDriverName");
            return (Criteria) this;
        }

        public Criteria andAssignDriverNameLike(String value) {
            addCriterion("assign_driver_name like", value, "assignDriverName");
            return (Criteria) this;
        }

        public Criteria andAssignDriverNameNotLike(String value) {
            addCriterion("assign_driver_name not like", value, "assignDriverName");
            return (Criteria) this;
        }

        public Criteria andAssignDriverNameIn(List<String> values) {
            addCriterion("assign_driver_name in", values, "assignDriverName");
            return (Criteria) this;
        }

        public Criteria andAssignDriverNameNotIn(List<String> values) {
            addCriterion("assign_driver_name not in", values, "assignDriverName");
            return (Criteria) this;
        }

        public Criteria andAssignDriverNameBetween(String value1, String value2) {
            addCriterion("assign_driver_name between", value1, value2, "assignDriverName");
            return (Criteria) this;
        }

        public Criteria andAssignDriverNameNotBetween(String value1, String value2) {
            addCriterion("assign_driver_name not between", value1, value2, "assignDriverName");
            return (Criteria) this;
        }

        public Criteria andAssignDriverPhoneIsNull() {
            addCriterion("assign_driver_phone is null");
            return (Criteria) this;
        }

        public Criteria andAssignDriverPhoneIsNotNull() {
            addCriterion("assign_driver_phone is not null");
            return (Criteria) this;
        }

        public Criteria andAssignDriverPhoneEqualTo(String value) {
            addCriterion("assign_driver_phone =", value, "assignDriverPhone");
            return (Criteria) this;
        }

        public Criteria andAssignDriverPhoneNotEqualTo(String value) {
            addCriterion("assign_driver_phone <>", value, "assignDriverPhone");
            return (Criteria) this;
        }

        public Criteria andAssignDriverPhoneGreaterThan(String value) {
            addCriterion("assign_driver_phone >", value, "assignDriverPhone");
            return (Criteria) this;
        }

        public Criteria andAssignDriverPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("assign_driver_phone >=", value, "assignDriverPhone");
            return (Criteria) this;
        }

        public Criteria andAssignDriverPhoneLessThan(String value) {
            addCriterion("assign_driver_phone <", value, "assignDriverPhone");
            return (Criteria) this;
        }

        public Criteria andAssignDriverPhoneLessThanOrEqualTo(String value) {
            addCriterion("assign_driver_phone <=", value, "assignDriverPhone");
            return (Criteria) this;
        }

        public Criteria andAssignDriverPhoneLike(String value) {
            addCriterion("assign_driver_phone like", value, "assignDriverPhone");
            return (Criteria) this;
        }

        public Criteria andAssignDriverPhoneNotLike(String value) {
            addCriterion("assign_driver_phone not like", value, "assignDriverPhone");
            return (Criteria) this;
        }

        public Criteria andAssignDriverPhoneIn(List<String> values) {
            addCriterion("assign_driver_phone in", values, "assignDriverPhone");
            return (Criteria) this;
        }

        public Criteria andAssignDriverPhoneNotIn(List<String> values) {
            addCriterion("assign_driver_phone not in", values, "assignDriverPhone");
            return (Criteria) this;
        }

        public Criteria andAssignDriverPhoneBetween(String value1, String value2) {
            addCriterion("assign_driver_phone between", value1, value2, "assignDriverPhone");
            return (Criteria) this;
        }

        public Criteria andAssignDriverPhoneNotBetween(String value1, String value2) {
            addCriterion("assign_driver_phone not between", value1, value2, "assignDriverPhone");
            return (Criteria) this;
        }

        public Criteria andAssignDriverCityCodeIsNull() {
            addCriterion("assign_driver_city_code is null");
            return (Criteria) this;
        }

        public Criteria andAssignDriverCityCodeIsNotNull() {
            addCriterion("assign_driver_city_code is not null");
            return (Criteria) this;
        }

        public Criteria andAssignDriverCityCodeEqualTo(Integer value) {
            addCriterion("assign_driver_city_code =", value, "assignDriverCityCode");
            return (Criteria) this;
        }

        public Criteria andAssignDriverCityCodeNotEqualTo(Integer value) {
            addCriterion("assign_driver_city_code <>", value, "assignDriverCityCode");
            return (Criteria) this;
        }

        public Criteria andAssignDriverCityCodeGreaterThan(Integer value) {
            addCriterion("assign_driver_city_code >", value, "assignDriverCityCode");
            return (Criteria) this;
        }

        public Criteria andAssignDriverCityCodeGreaterThanOrEqualTo(Integer value) {
            addCriterion("assign_driver_city_code >=", value, "assignDriverCityCode");
            return (Criteria) this;
        }

        public Criteria andAssignDriverCityCodeLessThan(Integer value) {
            addCriterion("assign_driver_city_code <", value, "assignDriverCityCode");
            return (Criteria) this;
        }

        public Criteria andAssignDriverCityCodeLessThanOrEqualTo(Integer value) {
            addCriterion("assign_driver_city_code <=", value, "assignDriverCityCode");
            return (Criteria) this;
        }

        public Criteria andAssignDriverCityCodeIn(List<Integer> values) {
            addCriterion("assign_driver_city_code in", values, "assignDriverCityCode");
            return (Criteria) this;
        }

        public Criteria andAssignDriverCityCodeNotIn(List<Integer> values) {
            addCriterion("assign_driver_city_code not in", values, "assignDriverCityCode");
            return (Criteria) this;
        }

        public Criteria andAssignDriverCityCodeBetween(Integer value1, Integer value2) {
            addCriterion("assign_driver_city_code between", value1, value2, "assignDriverCityCode");
            return (Criteria) this;
        }

        public Criteria andAssignDriverCityCodeNotBetween(Integer value1, Integer value2) {
            addCriterion("assign_driver_city_code not between", value1, value2, "assignDriverCityCode");
            return (Criteria) this;
        }

        public Criteria andAssignDriverCityNameIsNull() {
            addCriterion("assign_driver_city_name is null");
            return (Criteria) this;
        }

        public Criteria andAssignDriverCityNameIsNotNull() {
            addCriterion("assign_driver_city_name is not null");
            return (Criteria) this;
        }

        public Criteria andAssignDriverCityNameEqualTo(String value) {
            addCriterion("assign_driver_city_name =", value, "assignDriverCityName");
            return (Criteria) this;
        }

        public Criteria andAssignDriverCityNameNotEqualTo(String value) {
            addCriterion("assign_driver_city_name <>", value, "assignDriverCityName");
            return (Criteria) this;
        }

        public Criteria andAssignDriverCityNameGreaterThan(String value) {
            addCriterion("assign_driver_city_name >", value, "assignDriverCityName");
            return (Criteria) this;
        }

        public Criteria andAssignDriverCityNameGreaterThanOrEqualTo(String value) {
            addCriterion("assign_driver_city_name >=", value, "assignDriverCityName");
            return (Criteria) this;
        }

        public Criteria andAssignDriverCityNameLessThan(String value) {
            addCriterion("assign_driver_city_name <", value, "assignDriverCityName");
            return (Criteria) this;
        }

        public Criteria andAssignDriverCityNameLessThanOrEqualTo(String value) {
            addCriterion("assign_driver_city_name <=", value, "assignDriverCityName");
            return (Criteria) this;
        }

        public Criteria andAssignDriverCityNameLike(String value) {
            addCriterion("assign_driver_city_name like", value, "assignDriverCityName");
            return (Criteria) this;
        }

        public Criteria andAssignDriverCityNameNotLike(String value) {
            addCriterion("assign_driver_city_name not like", value, "assignDriverCityName");
            return (Criteria) this;
        }

        public Criteria andAssignDriverCityNameIn(List<String> values) {
            addCriterion("assign_driver_city_name in", values, "assignDriverCityName");
            return (Criteria) this;
        }

        public Criteria andAssignDriverCityNameNotIn(List<String> values) {
            addCriterion("assign_driver_city_name not in", values, "assignDriverCityName");
            return (Criteria) this;
        }

        public Criteria andAssignDriverCityNameBetween(String value1, String value2) {
            addCriterion("assign_driver_city_name between", value1, value2, "assignDriverCityName");
            return (Criteria) this;
        }

        public Criteria andAssignDriverCityNameNotBetween(String value1, String value2) {
            addCriterion("assign_driver_city_name not between", value1, value2, "assignDriverCityName");
            return (Criteria) this;
        }

        public Criteria andAssignCarmodelIdIsNull() {
            addCriterion("assign_carmodel_id is null");
            return (Criteria) this;
        }

        public Criteria andAssignCarmodelIdIsNotNull() {
            addCriterion("assign_carmodel_id is not null");
            return (Criteria) this;
        }

        public Criteria andAssignCarmodelIdEqualTo(Integer value) {
            addCriterion("assign_carmodel_id =", value, "assignCarmodelId");
            return (Criteria) this;
        }

        public Criteria andAssignCarmodelIdNotEqualTo(Integer value) {
            addCriterion("assign_carmodel_id <>", value, "assignCarmodelId");
            return (Criteria) this;
        }

        public Criteria andAssignCarmodelIdGreaterThan(Integer value) {
            addCriterion("assign_carmodel_id >", value, "assignCarmodelId");
            return (Criteria) this;
        }

        public Criteria andAssignCarmodelIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("assign_carmodel_id >=", value, "assignCarmodelId");
            return (Criteria) this;
        }

        public Criteria andAssignCarmodelIdLessThan(Integer value) {
            addCriterion("assign_carmodel_id <", value, "assignCarmodelId");
            return (Criteria) this;
        }

        public Criteria andAssignCarmodelIdLessThanOrEqualTo(Integer value) {
            addCriterion("assign_carmodel_id <=", value, "assignCarmodelId");
            return (Criteria) this;
        }

        public Criteria andAssignCarmodelIdIn(List<Integer> values) {
            addCriterion("assign_carmodel_id in", values, "assignCarmodelId");
            return (Criteria) this;
        }

        public Criteria andAssignCarmodelIdNotIn(List<Integer> values) {
            addCriterion("assign_carmodel_id not in", values, "assignCarmodelId");
            return (Criteria) this;
        }

        public Criteria andAssignCarmodelIdBetween(Integer value1, Integer value2) {
            addCriterion("assign_carmodel_id between", value1, value2, "assignCarmodelId");
            return (Criteria) this;
        }

        public Criteria andAssignCarmodelIdNotBetween(Integer value1, Integer value2) {
            addCriterion("assign_carmodel_id not between", value1, value2, "assignCarmodelId");
            return (Criteria) this;
        }

        public Criteria andAssignCarmodelNameIsNull() {
            addCriterion("assign_carmodel_name is null");
            return (Criteria) this;
        }

        public Criteria andAssignCarmodelNameIsNotNull() {
            addCriterion("assign_carmodel_name is not null");
            return (Criteria) this;
        }

        public Criteria andAssignCarmodelNameEqualTo(String value) {
            addCriterion("assign_carmodel_name =", value, "assignCarmodelName");
            return (Criteria) this;
        }

        public Criteria andAssignCarmodelNameNotEqualTo(String value) {
            addCriterion("assign_carmodel_name <>", value, "assignCarmodelName");
            return (Criteria) this;
        }

        public Criteria andAssignCarmodelNameGreaterThan(String value) {
            addCriterion("assign_carmodel_name >", value, "assignCarmodelName");
            return (Criteria) this;
        }

        public Criteria andAssignCarmodelNameGreaterThanOrEqualTo(String value) {
            addCriterion("assign_carmodel_name >=", value, "assignCarmodelName");
            return (Criteria) this;
        }

        public Criteria andAssignCarmodelNameLessThan(String value) {
            addCriterion("assign_carmodel_name <", value, "assignCarmodelName");
            return (Criteria) this;
        }

        public Criteria andAssignCarmodelNameLessThanOrEqualTo(String value) {
            addCriterion("assign_carmodel_name <=", value, "assignCarmodelName");
            return (Criteria) this;
        }

        public Criteria andAssignCarmodelNameLike(String value) {
            addCriterion("assign_carmodel_name like", value, "assignCarmodelName");
            return (Criteria) this;
        }

        public Criteria andAssignCarmodelNameNotLike(String value) {
            addCriterion("assign_carmodel_name not like", value, "assignCarmodelName");
            return (Criteria) this;
        }

        public Criteria andAssignCarmodelNameIn(List<String> values) {
            addCriterion("assign_carmodel_name in", values, "assignCarmodelName");
            return (Criteria) this;
        }

        public Criteria andAssignCarmodelNameNotIn(List<String> values) {
            addCriterion("assign_carmodel_name not in", values, "assignCarmodelName");
            return (Criteria) this;
        }

        public Criteria andAssignCarmodelNameBetween(String value1, String value2) {
            addCriterion("assign_carmodel_name between", value1, value2, "assignCarmodelName");
            return (Criteria) this;
        }

        public Criteria andAssignCarmodelNameNotBetween(String value1, String value2) {
            addCriterion("assign_carmodel_name not between", value1, value2, "assignCarmodelName");
            return (Criteria) this;
        }

        public Criteria andOrderCancelNoIsNull() {
            addCriterion("order_cancel_no is null");
            return (Criteria) this;
        }

        public Criteria andOrderCancelNoIsNotNull() {
            addCriterion("order_cancel_no is not null");
            return (Criteria) this;
        }

        public Criteria andOrderCancelNoEqualTo(String value) {
            addCriterion("order_cancel_no =", value, "orderCancelNo");
            return (Criteria) this;
        }

        public Criteria andOrderCancelNoNotEqualTo(String value) {
            addCriterion("order_cancel_no <>", value, "orderCancelNo");
            return (Criteria) this;
        }

        public Criteria andOrderCancelNoGreaterThan(String value) {
            addCriterion("order_cancel_no >", value, "orderCancelNo");
            return (Criteria) this;
        }

        public Criteria andOrderCancelNoGreaterThanOrEqualTo(String value) {
            addCriterion("order_cancel_no >=", value, "orderCancelNo");
            return (Criteria) this;
        }

        public Criteria andOrderCancelNoLessThan(String value) {
            addCriterion("order_cancel_no <", value, "orderCancelNo");
            return (Criteria) this;
        }

        public Criteria andOrderCancelNoLessThanOrEqualTo(String value) {
            addCriterion("order_cancel_no <=", value, "orderCancelNo");
            return (Criteria) this;
        }

        public Criteria andOrderCancelNoLike(String value) {
            addCriterion("order_cancel_no like", value, "orderCancelNo");
            return (Criteria) this;
        }

        public Criteria andOrderCancelNoNotLike(String value) {
            addCriterion("order_cancel_no not like", value, "orderCancelNo");
            return (Criteria) this;
        }

        public Criteria andOrderCancelNoIn(List<String> values) {
            addCriterion("order_cancel_no in", values, "orderCancelNo");
            return (Criteria) this;
        }

        public Criteria andOrderCancelNoNotIn(List<String> values) {
            addCriterion("order_cancel_no not in", values, "orderCancelNo");
            return (Criteria) this;
        }

        public Criteria andOrderCancelNoBetween(String value1, String value2) {
            addCriterion("order_cancel_no between", value1, value2, "orderCancelNo");
            return (Criteria) this;
        }

        public Criteria andOrderCancelNoNotBetween(String value1, String value2) {
            addCriterion("order_cancel_no not between", value1, value2, "orderCancelNo");
            return (Criteria) this;
        }

        public Criteria andOrderCancelTimeIsNull() {
            addCriterion("order_cancel_time is null");
            return (Criteria) this;
        }

        public Criteria andOrderCancelTimeIsNotNull() {
            addCriterion("order_cancel_time is not null");
            return (Criteria) this;
        }

        public Criteria andOrderCancelTimeEqualTo(Date value) {
            addCriterion("order_cancel_time =", value, "orderCancelTime");
            return (Criteria) this;
        }

        public Criteria andOrderCancelTimeNotEqualTo(Date value) {
            addCriterion("order_cancel_time <>", value, "orderCancelTime");
            return (Criteria) this;
        }

        public Criteria andOrderCancelTimeGreaterThan(Date value) {
            addCriterion("order_cancel_time >", value, "orderCancelTime");
            return (Criteria) this;
        }

        public Criteria andOrderCancelTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("order_cancel_time >=", value, "orderCancelTime");
            return (Criteria) this;
        }

        public Criteria andOrderCancelTimeLessThan(Date value) {
            addCriterion("order_cancel_time <", value, "orderCancelTime");
            return (Criteria) this;
        }

        public Criteria andOrderCancelTimeLessThanOrEqualTo(Date value) {
            addCriterion("order_cancel_time <=", value, "orderCancelTime");
            return (Criteria) this;
        }

        public Criteria andOrderCancelTimeIn(List<Date> values) {
            addCriterion("order_cancel_time in", values, "orderCancelTime");
            return (Criteria) this;
        }

        public Criteria andOrderCancelTimeNotIn(List<Date> values) {
            addCriterion("order_cancel_time not in", values, "orderCancelTime");
            return (Criteria) this;
        }

        public Criteria andOrderCancelTimeBetween(Date value1, Date value2) {
            addCriterion("order_cancel_time between", value1, value2, "orderCancelTime");
            return (Criteria) this;
        }

        public Criteria andOrderCancelTimeNotBetween(Date value1, Date value2) {
            addCriterion("order_cancel_time not between", value1, value2, "orderCancelTime");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitedIsNull() {
            addCriterion("appraise_submited is null");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitedIsNotNull() {
            addCriterion("appraise_submited is not null");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitedEqualTo(Boolean value) {
            addCriterion("appraise_submited =", value, "appraiseSubmited");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitedNotEqualTo(Boolean value) {
            addCriterion("appraise_submited <>", value, "appraiseSubmited");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitedGreaterThan(Boolean value) {
            addCriterion("appraise_submited >", value, "appraiseSubmited");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("appraise_submited >=", value, "appraiseSubmited");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitedLessThan(Boolean value) {
            addCriterion("appraise_submited <", value, "appraiseSubmited");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitedLessThanOrEqualTo(Boolean value) {
            addCriterion("appraise_submited <=", value, "appraiseSubmited");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitedIn(List<Boolean> values) {
            addCriterion("appraise_submited in", values, "appraiseSubmited");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitedNotIn(List<Boolean> values) {
            addCriterion("appraise_submited not in", values, "appraiseSubmited");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitedBetween(Boolean value1, Boolean value2) {
            addCriterion("appraise_submited between", value1, value2, "appraiseSubmited");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("appraise_submited not between", value1, value2, "appraiseSubmited");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitTimeIsNull() {
            addCriterion("appraise_submit_time is null");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitTimeIsNotNull() {
            addCriterion("appraise_submit_time is not null");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitTimeEqualTo(Date value) {
            addCriterion("appraise_submit_time =", value, "appraiseSubmitTime");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitTimeNotEqualTo(Date value) {
            addCriterion("appraise_submit_time <>", value, "appraiseSubmitTime");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitTimeGreaterThan(Date value) {
            addCriterion("appraise_submit_time >", value, "appraiseSubmitTime");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("appraise_submit_time >=", value, "appraiseSubmitTime");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitTimeLessThan(Date value) {
            addCriterion("appraise_submit_time <", value, "appraiseSubmitTime");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitTimeLessThanOrEqualTo(Date value) {
            addCriterion("appraise_submit_time <=", value, "appraiseSubmitTime");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitTimeIn(List<Date> values) {
            addCriterion("appraise_submit_time in", values, "appraiseSubmitTime");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitTimeNotIn(List<Date> values) {
            addCriterion("appraise_submit_time not in", values, "appraiseSubmitTime");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitTimeBetween(Date value1, Date value2) {
            addCriterion("appraise_submit_time between", value1, value2, "appraiseSubmitTime");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitTimeNotBetween(Date value1, Date value2) {
            addCriterion("appraise_submit_time not between", value1, value2, "appraiseSubmitTime");
            return (Criteria) this;
        }

        public Criteria andTrafficTypeIsNull() {
            addCriterion("traffic_type is null");
            return (Criteria) this;
        }

        public Criteria andTrafficTypeIsNotNull() {
            addCriterion("traffic_type is not null");
            return (Criteria) this;
        }

        public Criteria andTrafficTypeEqualTo(Byte value) {
            addCriterion("traffic_type =", value, "trafficType");
            return (Criteria) this;
        }

        public Criteria andTrafficTypeNotEqualTo(Byte value) {
            addCriterion("traffic_type <>", value, "trafficType");
            return (Criteria) this;
        }

        public Criteria andTrafficTypeGreaterThan(Byte value) {
            addCriterion("traffic_type >", value, "trafficType");
            return (Criteria) this;
        }

        public Criteria andTrafficTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("traffic_type >=", value, "trafficType");
            return (Criteria) this;
        }

        public Criteria andTrafficTypeLessThan(Byte value) {
            addCriterion("traffic_type <", value, "trafficType");
            return (Criteria) this;
        }

        public Criteria andTrafficTypeLessThanOrEqualTo(Byte value) {
            addCriterion("traffic_type <=", value, "trafficType");
            return (Criteria) this;
        }

        public Criteria andTrafficTypeIn(List<Byte> values) {
            addCriterion("traffic_type in", values, "trafficType");
            return (Criteria) this;
        }

        public Criteria andTrafficTypeNotIn(List<Byte> values) {
            addCriterion("traffic_type not in", values, "trafficType");
            return (Criteria) this;
        }

        public Criteria andTrafficTypeBetween(Byte value1, Byte value2) {
            addCriterion("traffic_type between", value1, value2, "trafficType");
            return (Criteria) this;
        }

        public Criteria andTrafficTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("traffic_type not between", value1, value2, "trafficType");
            return (Criteria) this;
        }

        public Criteria andTrafficNumberIsNull() {
            addCriterion("traffic_number is null");
            return (Criteria) this;
        }

        public Criteria andTrafficNumberIsNotNull() {
            addCriterion("traffic_number is not null");
            return (Criteria) this;
        }

        public Criteria andTrafficNumberEqualTo(String value) {
            addCriterion("traffic_number =", value, "trafficNumber");
            return (Criteria) this;
        }

        public Criteria andTrafficNumberNotEqualTo(String value) {
            addCriterion("traffic_number <>", value, "trafficNumber");
            return (Criteria) this;
        }

        public Criteria andTrafficNumberGreaterThan(String value) {
            addCriterion("traffic_number >", value, "trafficNumber");
            return (Criteria) this;
        }

        public Criteria andTrafficNumberGreaterThanOrEqualTo(String value) {
            addCriterion("traffic_number >=", value, "trafficNumber");
            return (Criteria) this;
        }

        public Criteria andTrafficNumberLessThan(String value) {
            addCriterion("traffic_number <", value, "trafficNumber");
            return (Criteria) this;
        }

        public Criteria andTrafficNumberLessThanOrEqualTo(String value) {
            addCriterion("traffic_number <=", value, "trafficNumber");
            return (Criteria) this;
        }

        public Criteria andTrafficNumberLike(String value) {
            addCriterion("traffic_number like", value, "trafficNumber");
            return (Criteria) this;
        }

        public Criteria andTrafficNumberNotLike(String value) {
            addCriterion("traffic_number not like", value, "trafficNumber");
            return (Criteria) this;
        }

        public Criteria andTrafficNumberIn(List<String> values) {
            addCriterion("traffic_number in", values, "trafficNumber");
            return (Criteria) this;
        }

        public Criteria andTrafficNumberNotIn(List<String> values) {
            addCriterion("traffic_number not in", values, "trafficNumber");
            return (Criteria) this;
        }

        public Criteria andTrafficNumberBetween(String value1, String value2) {
            addCriterion("traffic_number between", value1, value2, "trafficNumber");
            return (Criteria) this;
        }

        public Criteria andTrafficNumberNotBetween(String value1, String value2) {
            addCriterion("traffic_number not between", value1, value2, "trafficNumber");
            return (Criteria) this;
        }

        public Criteria andFactStartLongAddrIsNull() {
            addCriterion("fact_start_long_addr is null");
            return (Criteria) this;
        }

        public Criteria andFactStartLongAddrIsNotNull() {
            addCriterion("fact_start_long_addr is not null");
            return (Criteria) this;
        }

        public Criteria andFactStartLongAddrEqualTo(String value) {
            addCriterion("fact_start_long_addr =", value, "factStartLongAddr");
            return (Criteria) this;
        }

        public Criteria andFactStartLongAddrNotEqualTo(String value) {
            addCriterion("fact_start_long_addr <>", value, "factStartLongAddr");
            return (Criteria) this;
        }

        public Criteria andFactStartLongAddrGreaterThan(String value) {
            addCriterion("fact_start_long_addr >", value, "factStartLongAddr");
            return (Criteria) this;
        }

        public Criteria andFactStartLongAddrGreaterThanOrEqualTo(String value) {
            addCriterion("fact_start_long_addr >=", value, "factStartLongAddr");
            return (Criteria) this;
        }

        public Criteria andFactStartLongAddrLessThan(String value) {
            addCriterion("fact_start_long_addr <", value, "factStartLongAddr");
            return (Criteria) this;
        }

        public Criteria andFactStartLongAddrLessThanOrEqualTo(String value) {
            addCriterion("fact_start_long_addr <=", value, "factStartLongAddr");
            return (Criteria) this;
        }

        public Criteria andFactStartLongAddrLike(String value) {
            addCriterion("fact_start_long_addr like", value, "factStartLongAddr");
            return (Criteria) this;
        }

        public Criteria andFactStartLongAddrNotLike(String value) {
            addCriterion("fact_start_long_addr not like", value, "factStartLongAddr");
            return (Criteria) this;
        }

        public Criteria andFactStartLongAddrIn(List<String> values) {
            addCriterion("fact_start_long_addr in", values, "factStartLongAddr");
            return (Criteria) this;
        }

        public Criteria andFactStartLongAddrNotIn(List<String> values) {
            addCriterion("fact_start_long_addr not in", values, "factStartLongAddr");
            return (Criteria) this;
        }

        public Criteria andFactStartLongAddrBetween(String value1, String value2) {
            addCriterion("fact_start_long_addr between", value1, value2, "factStartLongAddr");
            return (Criteria) this;
        }

        public Criteria andFactStartLongAddrNotBetween(String value1, String value2) {
            addCriterion("fact_start_long_addr not between", value1, value2, "factStartLongAddr");
            return (Criteria) this;
        }

        public Criteria andFactStartShortAddrIsNull() {
            addCriterion("fact_start_short_addr is null");
            return (Criteria) this;
        }

        public Criteria andFactStartShortAddrIsNotNull() {
            addCriterion("fact_start_short_addr is not null");
            return (Criteria) this;
        }

        public Criteria andFactStartShortAddrEqualTo(String value) {
            addCriterion("fact_start_short_addr =", value, "factStartShortAddr");
            return (Criteria) this;
        }

        public Criteria andFactStartShortAddrNotEqualTo(String value) {
            addCriterion("fact_start_short_addr <>", value, "factStartShortAddr");
            return (Criteria) this;
        }

        public Criteria andFactStartShortAddrGreaterThan(String value) {
            addCriterion("fact_start_short_addr >", value, "factStartShortAddr");
            return (Criteria) this;
        }

        public Criteria andFactStartShortAddrGreaterThanOrEqualTo(String value) {
            addCriterion("fact_start_short_addr >=", value, "factStartShortAddr");
            return (Criteria) this;
        }

        public Criteria andFactStartShortAddrLessThan(String value) {
            addCriterion("fact_start_short_addr <", value, "factStartShortAddr");
            return (Criteria) this;
        }

        public Criteria andFactStartShortAddrLessThanOrEqualTo(String value) {
            addCriterion("fact_start_short_addr <=", value, "factStartShortAddr");
            return (Criteria) this;
        }

        public Criteria andFactStartShortAddrLike(String value) {
            addCriterion("fact_start_short_addr like", value, "factStartShortAddr");
            return (Criteria) this;
        }

        public Criteria andFactStartShortAddrNotLike(String value) {
            addCriterion("fact_start_short_addr not like", value, "factStartShortAddr");
            return (Criteria) this;
        }

        public Criteria andFactStartShortAddrIn(List<String> values) {
            addCriterion("fact_start_short_addr in", values, "factStartShortAddr");
            return (Criteria) this;
        }

        public Criteria andFactStartShortAddrNotIn(List<String> values) {
            addCriterion("fact_start_short_addr not in", values, "factStartShortAddr");
            return (Criteria) this;
        }

        public Criteria andFactStartShortAddrBetween(String value1, String value2) {
            addCriterion("fact_start_short_addr between", value1, value2, "factStartShortAddr");
            return (Criteria) this;
        }

        public Criteria andFactStartShortAddrNotBetween(String value1, String value2) {
            addCriterion("fact_start_short_addr not between", value1, value2, "factStartShortAddr");
            return (Criteria) this;
        }

        public Criteria andFactStartPointIsNull() {
            addCriterion("fact_start_point is null");
            return (Criteria) this;
        }

        public Criteria andFactStartPointIsNotNull() {
            addCriterion("fact_start_point is not null");
            return (Criteria) this;
        }

        public Criteria andFactStartPointEqualTo(String value) {
            addCriterion("fact_start_point =", value, "factStartPoint");
            return (Criteria) this;
        }

        public Criteria andFactStartPointNotEqualTo(String value) {
            addCriterion("fact_start_point <>", value, "factStartPoint");
            return (Criteria) this;
        }

        public Criteria andFactStartPointGreaterThan(String value) {
            addCriterion("fact_start_point >", value, "factStartPoint");
            return (Criteria) this;
        }

        public Criteria andFactStartPointGreaterThanOrEqualTo(String value) {
            addCriterion("fact_start_point >=", value, "factStartPoint");
            return (Criteria) this;
        }

        public Criteria andFactStartPointLessThan(String value) {
            addCriterion("fact_start_point <", value, "factStartPoint");
            return (Criteria) this;
        }

        public Criteria andFactStartPointLessThanOrEqualTo(String value) {
            addCriterion("fact_start_point <=", value, "factStartPoint");
            return (Criteria) this;
        }

        public Criteria andFactStartPointLike(String value) {
            addCriterion("fact_start_point like", value, "factStartPoint");
            return (Criteria) this;
        }

        public Criteria andFactStartPointNotLike(String value) {
            addCriterion("fact_start_point not like", value, "factStartPoint");
            return (Criteria) this;
        }

        public Criteria andFactStartPointIn(List<String> values) {
            addCriterion("fact_start_point in", values, "factStartPoint");
            return (Criteria) this;
        }

        public Criteria andFactStartPointNotIn(List<String> values) {
            addCriterion("fact_start_point not in", values, "factStartPoint");
            return (Criteria) this;
        }

        public Criteria andFactStartPointBetween(String value1, String value2) {
            addCriterion("fact_start_point between", value1, value2, "factStartPoint");
            return (Criteria) this;
        }

        public Criteria andFactStartPointNotBetween(String value1, String value2) {
            addCriterion("fact_start_point not between", value1, value2, "factStartPoint");
            return (Criteria) this;
        }

        public Criteria andFactEndLongAddrIsNull() {
            addCriterion("fact_end_long_addr is null");
            return (Criteria) this;
        }

        public Criteria andFactEndLongAddrIsNotNull() {
            addCriterion("fact_end_long_addr is not null");
            return (Criteria) this;
        }

        public Criteria andFactEndLongAddrEqualTo(String value) {
            addCriterion("fact_end_long_addr =", value, "factEndLongAddr");
            return (Criteria) this;
        }

        public Criteria andFactEndLongAddrNotEqualTo(String value) {
            addCriterion("fact_end_long_addr <>", value, "factEndLongAddr");
            return (Criteria) this;
        }

        public Criteria andFactEndLongAddrGreaterThan(String value) {
            addCriterion("fact_end_long_addr >", value, "factEndLongAddr");
            return (Criteria) this;
        }

        public Criteria andFactEndLongAddrGreaterThanOrEqualTo(String value) {
            addCriterion("fact_end_long_addr >=", value, "factEndLongAddr");
            return (Criteria) this;
        }

        public Criteria andFactEndLongAddrLessThan(String value) {
            addCriterion("fact_end_long_addr <", value, "factEndLongAddr");
            return (Criteria) this;
        }

        public Criteria andFactEndLongAddrLessThanOrEqualTo(String value) {
            addCriterion("fact_end_long_addr <=", value, "factEndLongAddr");
            return (Criteria) this;
        }

        public Criteria andFactEndLongAddrLike(String value) {
            addCriterion("fact_end_long_addr like", value, "factEndLongAddr");
            return (Criteria) this;
        }

        public Criteria andFactEndLongAddrNotLike(String value) {
            addCriterion("fact_end_long_addr not like", value, "factEndLongAddr");
            return (Criteria) this;
        }

        public Criteria andFactEndLongAddrIn(List<String> values) {
            addCriterion("fact_end_long_addr in", values, "factEndLongAddr");
            return (Criteria) this;
        }

        public Criteria andFactEndLongAddrNotIn(List<String> values) {
            addCriterion("fact_end_long_addr not in", values, "factEndLongAddr");
            return (Criteria) this;
        }

        public Criteria andFactEndLongAddrBetween(String value1, String value2) {
            addCriterion("fact_end_long_addr between", value1, value2, "factEndLongAddr");
            return (Criteria) this;
        }

        public Criteria andFactEndLongAddrNotBetween(String value1, String value2) {
            addCriterion("fact_end_long_addr not between", value1, value2, "factEndLongAddr");
            return (Criteria) this;
        }

        public Criteria andFactEndShortAddrIsNull() {
            addCriterion("fact_end_short_addr is null");
            return (Criteria) this;
        }

        public Criteria andFactEndShortAddrIsNotNull() {
            addCriterion("fact_end_short_addr is not null");
            return (Criteria) this;
        }

        public Criteria andFactEndShortAddrEqualTo(String value) {
            addCriterion("fact_end_short_addr =", value, "factEndShortAddr");
            return (Criteria) this;
        }

        public Criteria andFactEndShortAddrNotEqualTo(String value) {
            addCriterion("fact_end_short_addr <>", value, "factEndShortAddr");
            return (Criteria) this;
        }

        public Criteria andFactEndShortAddrGreaterThan(String value) {
            addCriterion("fact_end_short_addr >", value, "factEndShortAddr");
            return (Criteria) this;
        }

        public Criteria andFactEndShortAddrGreaterThanOrEqualTo(String value) {
            addCriterion("fact_end_short_addr >=", value, "factEndShortAddr");
            return (Criteria) this;
        }

        public Criteria andFactEndShortAddrLessThan(String value) {
            addCriterion("fact_end_short_addr <", value, "factEndShortAddr");
            return (Criteria) this;
        }

        public Criteria andFactEndShortAddrLessThanOrEqualTo(String value) {
            addCriterion("fact_end_short_addr <=", value, "factEndShortAddr");
            return (Criteria) this;
        }

        public Criteria andFactEndShortAddrLike(String value) {
            addCriterion("fact_end_short_addr like", value, "factEndShortAddr");
            return (Criteria) this;
        }

        public Criteria andFactEndShortAddrNotLike(String value) {
            addCriterion("fact_end_short_addr not like", value, "factEndShortAddr");
            return (Criteria) this;
        }

        public Criteria andFactEndShortAddrIn(List<String> values) {
            addCriterion("fact_end_short_addr in", values, "factEndShortAddr");
            return (Criteria) this;
        }

        public Criteria andFactEndShortAddrNotIn(List<String> values) {
            addCriterion("fact_end_short_addr not in", values, "factEndShortAddr");
            return (Criteria) this;
        }

        public Criteria andFactEndShortAddrBetween(String value1, String value2) {
            addCriterion("fact_end_short_addr between", value1, value2, "factEndShortAddr");
            return (Criteria) this;
        }

        public Criteria andFactEndShortAddrNotBetween(String value1, String value2) {
            addCriterion("fact_end_short_addr not between", value1, value2, "factEndShortAddr");
            return (Criteria) this;
        }

        public Criteria andFactEndPointIsNull() {
            addCriterion("fact_end_point is null");
            return (Criteria) this;
        }

        public Criteria andFactEndPointIsNotNull() {
            addCriterion("fact_end_point is not null");
            return (Criteria) this;
        }

        public Criteria andFactEndPointEqualTo(String value) {
            addCriterion("fact_end_point =", value, "factEndPoint");
            return (Criteria) this;
        }

        public Criteria andFactEndPointNotEqualTo(String value) {
            addCriterion("fact_end_point <>", value, "factEndPoint");
            return (Criteria) this;
        }

        public Criteria andFactEndPointGreaterThan(String value) {
            addCriterion("fact_end_point >", value, "factEndPoint");
            return (Criteria) this;
        }

        public Criteria andFactEndPointGreaterThanOrEqualTo(String value) {
            addCriterion("fact_end_point >=", value, "factEndPoint");
            return (Criteria) this;
        }

        public Criteria andFactEndPointLessThan(String value) {
            addCriterion("fact_end_point <", value, "factEndPoint");
            return (Criteria) this;
        }

        public Criteria andFactEndPointLessThanOrEqualTo(String value) {
            addCriterion("fact_end_point <=", value, "factEndPoint");
            return (Criteria) this;
        }

        public Criteria andFactEndPointLike(String value) {
            addCriterion("fact_end_point like", value, "factEndPoint");
            return (Criteria) this;
        }

        public Criteria andFactEndPointNotLike(String value) {
            addCriterion("fact_end_point not like", value, "factEndPoint");
            return (Criteria) this;
        }

        public Criteria andFactEndPointIn(List<String> values) {
            addCriterion("fact_end_point in", values, "factEndPoint");
            return (Criteria) this;
        }

        public Criteria andFactEndPointNotIn(List<String> values) {
            addCriterion("fact_end_point not in", values, "factEndPoint");
            return (Criteria) this;
        }

        public Criteria andFactEndPointBetween(String value1, String value2) {
            addCriterion("fact_end_point between", value1, value2, "factEndPoint");
            return (Criteria) this;
        }

        public Criteria andFactEndPointNotBetween(String value1, String value2) {
            addCriterion("fact_end_point not between", value1, value2, "factEndPoint");
            return (Criteria) this;
        }

        public Criteria andFactStartDateIsNull() {
            addCriterion("fact_start_date is null");
            return (Criteria) this;
        }

        public Criteria andFactStartDateIsNotNull() {
            addCriterion("fact_start_date is not null");
            return (Criteria) this;
        }

        public Criteria andFactStartDateEqualTo(Date value) {
            addCriterion("fact_start_date =", value, "factStartDate");
            return (Criteria) this;
        }

        public Criteria andFactStartDateNotEqualTo(Date value) {
            addCriterion("fact_start_date <>", value, "factStartDate");
            return (Criteria) this;
        }

        public Criteria andFactStartDateGreaterThan(Date value) {
            addCriterion("fact_start_date >", value, "factStartDate");
            return (Criteria) this;
        }

        public Criteria andFactStartDateGreaterThanOrEqualTo(Date value) {
            addCriterion("fact_start_date >=", value, "factStartDate");
            return (Criteria) this;
        }

        public Criteria andFactStartDateLessThan(Date value) {
            addCriterion("fact_start_date <", value, "factStartDate");
            return (Criteria) this;
        }

        public Criteria andFactStartDateLessThanOrEqualTo(Date value) {
            addCriterion("fact_start_date <=", value, "factStartDate");
            return (Criteria) this;
        }

        public Criteria andFactStartDateIn(List<Date> values) {
            addCriterion("fact_start_date in", values, "factStartDate");
            return (Criteria) this;
        }

        public Criteria andFactStartDateNotIn(List<Date> values) {
            addCriterion("fact_start_date not in", values, "factStartDate");
            return (Criteria) this;
        }

        public Criteria andFactStartDateBetween(Date value1, Date value2) {
            addCriterion("fact_start_date between", value1, value2, "factStartDate");
            return (Criteria) this;
        }

        public Criteria andFactStartDateNotBetween(Date value1, Date value2) {
            addCriterion("fact_start_date not between", value1, value2, "factStartDate");
            return (Criteria) this;
        }

        public Criteria andFactEndDateIsNull() {
            addCriterion("fact_end_date is null");
            return (Criteria) this;
        }

        public Criteria andFactEndDateIsNotNull() {
            addCriterion("fact_end_date is not null");
            return (Criteria) this;
        }

        public Criteria andFactEndDateEqualTo(Date value) {
            addCriterion("fact_end_date =", value, "factEndDate");
            return (Criteria) this;
        }

        public Criteria andFactEndDateNotEqualTo(Date value) {
            addCriterion("fact_end_date <>", value, "factEndDate");
            return (Criteria) this;
        }

        public Criteria andFactEndDateGreaterThan(Date value) {
            addCriterion("fact_end_date >", value, "factEndDate");
            return (Criteria) this;
        }

        public Criteria andFactEndDateGreaterThanOrEqualTo(Date value) {
            addCriterion("fact_end_date >=", value, "factEndDate");
            return (Criteria) this;
        }

        public Criteria andFactEndDateLessThan(Date value) {
            addCriterion("fact_end_date <", value, "factEndDate");
            return (Criteria) this;
        }

        public Criteria andFactEndDateLessThanOrEqualTo(Date value) {
            addCriterion("fact_end_date <=", value, "factEndDate");
            return (Criteria) this;
        }

        public Criteria andFactEndDateIn(List<Date> values) {
            addCriterion("fact_end_date in", values, "factEndDate");
            return (Criteria) this;
        }

        public Criteria andFactEndDateNotIn(List<Date> values) {
            addCriterion("fact_end_date not in", values, "factEndDate");
            return (Criteria) this;
        }

        public Criteria andFactEndDateBetween(Date value1, Date value2) {
            addCriterion("fact_end_date between", value1, value2, "factEndDate");
            return (Criteria) this;
        }

        public Criteria andFactEndDateNotBetween(Date value1, Date value2) {
            addCriterion("fact_end_date not between", value1, value2, "factEndDate");
            return (Criteria) this;
        }

        public Criteria andDriverStartTimeIsNull() {
            addCriterion("driver_start_time is null");
            return (Criteria) this;
        }

        public Criteria andDriverStartTimeIsNotNull() {
            addCriterion("driver_start_time is not null");
            return (Criteria) this;
        }

        public Criteria andDriverStartTimeEqualTo(Date value) {
            addCriterion("driver_start_time =", value, "driverStartTime");
            return (Criteria) this;
        }

        public Criteria andDriverStartTimeNotEqualTo(Date value) {
            addCriterion("driver_start_time <>", value, "driverStartTime");
            return (Criteria) this;
        }

        public Criteria andDriverStartTimeGreaterThan(Date value) {
            addCriterion("driver_start_time >", value, "driverStartTime");
            return (Criteria) this;
        }

        public Criteria andDriverStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("driver_start_time >=", value, "driverStartTime");
            return (Criteria) this;
        }

        public Criteria andDriverStartTimeLessThan(Date value) {
            addCriterion("driver_start_time <", value, "driverStartTime");
            return (Criteria) this;
        }

        public Criteria andDriverStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("driver_start_time <=", value, "driverStartTime");
            return (Criteria) this;
        }

        public Criteria andDriverStartTimeIn(List<Date> values) {
            addCriterion("driver_start_time in", values, "driverStartTime");
            return (Criteria) this;
        }

        public Criteria andDriverStartTimeNotIn(List<Date> values) {
            addCriterion("driver_start_time not in", values, "driverStartTime");
            return (Criteria) this;
        }

        public Criteria andDriverStartTimeBetween(Date value1, Date value2) {
            addCriterion("driver_start_time between", value1, value2, "driverStartTime");
            return (Criteria) this;
        }

        public Criteria andDriverStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("driver_start_time not between", value1, value2, "driverStartTime");
            return (Criteria) this;
        }

        public Criteria andDriverArriveTimeIsNull() {
            addCriterion("driver_arrive_time is null");
            return (Criteria) this;
        }

        public Criteria andDriverArriveTimeIsNotNull() {
            addCriterion("driver_arrive_time is not null");
            return (Criteria) this;
        }

        public Criteria andDriverArriveTimeEqualTo(Date value) {
            addCriterion("driver_arrive_time =", value, "driverArriveTime");
            return (Criteria) this;
        }

        public Criteria andDriverArriveTimeNotEqualTo(Date value) {
            addCriterion("driver_arrive_time <>", value, "driverArriveTime");
            return (Criteria) this;
        }

        public Criteria andDriverArriveTimeGreaterThan(Date value) {
            addCriterion("driver_arrive_time >", value, "driverArriveTime");
            return (Criteria) this;
        }

        public Criteria andDriverArriveTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("driver_arrive_time >=", value, "driverArriveTime");
            return (Criteria) this;
        }

        public Criteria andDriverArriveTimeLessThan(Date value) {
            addCriterion("driver_arrive_time <", value, "driverArriveTime");
            return (Criteria) this;
        }

        public Criteria andDriverArriveTimeLessThanOrEqualTo(Date value) {
            addCriterion("driver_arrive_time <=", value, "driverArriveTime");
            return (Criteria) this;
        }

        public Criteria andDriverArriveTimeIn(List<Date> values) {
            addCriterion("driver_arrive_time in", values, "driverArriveTime");
            return (Criteria) this;
        }

        public Criteria andDriverArriveTimeNotIn(List<Date> values) {
            addCriterion("driver_arrive_time not in", values, "driverArriveTime");
            return (Criteria) this;
        }

        public Criteria andDriverArriveTimeBetween(Date value1, Date value2) {
            addCriterion("driver_arrive_time between", value1, value2, "driverArriveTime");
            return (Criteria) this;
        }

        public Criteria andDriverArriveTimeNotBetween(Date value1, Date value2) {
            addCriterion("driver_arrive_time not between", value1, value2, "driverArriveTime");
            return (Criteria) this;
        }

        public Criteria andGetOnTimeIsNull() {
            addCriterion("get_on_time is null");
            return (Criteria) this;
        }

        public Criteria andGetOnTimeIsNotNull() {
            addCriterion("get_on_time is not null");
            return (Criteria) this;
        }

        public Criteria andGetOnTimeEqualTo(Date value) {
            addCriterion("get_on_time =", value, "getOnTime");
            return (Criteria) this;
        }

        public Criteria andGetOnTimeNotEqualTo(Date value) {
            addCriterion("get_on_time <>", value, "getOnTime");
            return (Criteria) this;
        }

        public Criteria andGetOnTimeGreaterThan(Date value) {
            addCriterion("get_on_time >", value, "getOnTime");
            return (Criteria) this;
        }

        public Criteria andGetOnTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("get_on_time >=", value, "getOnTime");
            return (Criteria) this;
        }

        public Criteria andGetOnTimeLessThan(Date value) {
            addCriterion("get_on_time <", value, "getOnTime");
            return (Criteria) this;
        }

        public Criteria andGetOnTimeLessThanOrEqualTo(Date value) {
            addCriterion("get_on_time <=", value, "getOnTime");
            return (Criteria) this;
        }

        public Criteria andGetOnTimeIn(List<Date> values) {
            addCriterion("get_on_time in", values, "getOnTime");
            return (Criteria) this;
        }

        public Criteria andGetOnTimeNotIn(List<Date> values) {
            addCriterion("get_on_time not in", values, "getOnTime");
            return (Criteria) this;
        }

        public Criteria andGetOnTimeBetween(Date value1, Date value2) {
            addCriterion("get_on_time between", value1, value2, "getOnTime");
            return (Criteria) this;
        }

        public Criteria andGetOnTimeNotBetween(Date value1, Date value2) {
            addCriterion("get_on_time not between", value1, value2, "getOnTime");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIsNull() {
            addCriterion("order_status is null");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIsNotNull() {
            addCriterion("order_status is not null");
            return (Criteria) this;
        }

        public Criteria andOrderStatusEqualTo(Short value) {
            addCriterion("order_status =", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotEqualTo(Short value) {
            addCriterion("order_status <>", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusGreaterThan(Short value) {
            addCriterion("order_status >", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusGreaterThanOrEqualTo(Short value) {
            addCriterion("order_status >=", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusLessThan(Short value) {
            addCriterion("order_status <", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusLessThanOrEqualTo(Short value) {
            addCriterion("order_status <=", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIn(List<Short> values) {
            addCriterion("order_status in", values, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotIn(List<Short> values) {
            addCriterion("order_status not in", values, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusBetween(Short value1, Short value2) {
            addCriterion("order_status between", value1, value2, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotBetween(Short value1, Short value2) {
            addCriterion("order_status not between", value1, value2, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderDetailIsNull() {
            addCriterion("order_detail is null");
            return (Criteria) this;
        }

        public Criteria andOrderDetailIsNotNull() {
            addCriterion("order_detail is not null");
            return (Criteria) this;
        }

        public Criteria andOrderDetailEqualTo(String value) {
            addCriterion("order_detail =", value, "orderDetail");
            return (Criteria) this;
        }

        public Criteria andOrderDetailNotEqualTo(String value) {
            addCriterion("order_detail <>", value, "orderDetail");
            return (Criteria) this;
        }

        public Criteria andOrderDetailGreaterThan(String value) {
            addCriterion("order_detail >", value, "orderDetail");
            return (Criteria) this;
        }

        public Criteria andOrderDetailGreaterThanOrEqualTo(String value) {
            addCriterion("order_detail >=", value, "orderDetail");
            return (Criteria) this;
        }

        public Criteria andOrderDetailLessThan(String value) {
            addCriterion("order_detail <", value, "orderDetail");
            return (Criteria) this;
        }

        public Criteria andOrderDetailLessThanOrEqualTo(String value) {
            addCriterion("order_detail <=", value, "orderDetail");
            return (Criteria) this;
        }

        public Criteria andOrderDetailLike(String value) {
            addCriterion("order_detail like", value, "orderDetail");
            return (Criteria) this;
        }

        public Criteria andOrderDetailNotLike(String value) {
            addCriterion("order_detail not like", value, "orderDetail");
            return (Criteria) this;
        }

        public Criteria andOrderDetailIn(List<String> values) {
            addCriterion("order_detail in", values, "orderDetail");
            return (Criteria) this;
        }

        public Criteria andOrderDetailNotIn(List<String> values) {
            addCriterion("order_detail not in", values, "orderDetail");
            return (Criteria) this;
        }

        public Criteria andOrderDetailBetween(String value1, String value2) {
            addCriterion("order_detail between", value1, value2, "orderDetail");
            return (Criteria) this;
        }

        public Criteria andOrderDetailNotBetween(String value1, String value2) {
            addCriterion("order_detail not between", value1, value2, "orderDetail");
            return (Criteria) this;
        }

        public Criteria andMileageSourceIsNull() {
            addCriterion("mileage_source is null");
            return (Criteria) this;
        }

        public Criteria andMileageSourceIsNotNull() {
            addCriterion("mileage_source is not null");
            return (Criteria) this;
        }

        public Criteria andMileageSourceEqualTo(Byte value) {
            addCriterion("mileage_source =", value, "mileageSource");
            return (Criteria) this;
        }

        public Criteria andMileageSourceNotEqualTo(Byte value) {
            addCriterion("mileage_source <>", value, "mileageSource");
            return (Criteria) this;
        }

        public Criteria andMileageSourceGreaterThan(Byte value) {
            addCriterion("mileage_source >", value, "mileageSource");
            return (Criteria) this;
        }

        public Criteria andMileageSourceGreaterThanOrEqualTo(Byte value) {
            addCriterion("mileage_source >=", value, "mileageSource");
            return (Criteria) this;
        }

        public Criteria andMileageSourceLessThan(Byte value) {
            addCriterion("mileage_source <", value, "mileageSource");
            return (Criteria) this;
        }

        public Criteria andMileageSourceLessThanOrEqualTo(Byte value) {
            addCriterion("mileage_source <=", value, "mileageSource");
            return (Criteria) this;
        }

        public Criteria andMileageSourceIn(List<Byte> values) {
            addCriterion("mileage_source in", values, "mileageSource");
            return (Criteria) this;
        }

        public Criteria andMileageSourceNotIn(List<Byte> values) {
            addCriterion("mileage_source not in", values, "mileageSource");
            return (Criteria) this;
        }

        public Criteria andMileageSourceBetween(Byte value1, Byte value2) {
            addCriterion("mileage_source between", value1, value2, "mileageSource");
            return (Criteria) this;
        }

        public Criteria andMileageSourceNotBetween(Byte value1, Byte value2) {
            addCriterion("mileage_source not between", value1, value2, "mileageSource");
            return (Criteria) this;
        }

        public Criteria andTripMileageIsNull() {
            addCriterion("trip_mileage is null");
            return (Criteria) this;
        }

        public Criteria andTripMileageIsNotNull() {
            addCriterion("trip_mileage is not null");
            return (Criteria) this;
        }

        public Criteria andTripMileageEqualTo(BigDecimal value) {
            addCriterion("trip_mileage =", value, "tripMileage");
            return (Criteria) this;
        }

        public Criteria andTripMileageNotEqualTo(BigDecimal value) {
            addCriterion("trip_mileage <>", value, "tripMileage");
            return (Criteria) this;
        }

        public Criteria andTripMileageGreaterThan(BigDecimal value) {
            addCriterion("trip_mileage >", value, "tripMileage");
            return (Criteria) this;
        }

        public Criteria andTripMileageGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("trip_mileage >=", value, "tripMileage");
            return (Criteria) this;
        }

        public Criteria andTripMileageLessThan(BigDecimal value) {
            addCriterion("trip_mileage <", value, "tripMileage");
            return (Criteria) this;
        }

        public Criteria andTripMileageLessThanOrEqualTo(BigDecimal value) {
            addCriterion("trip_mileage <=", value, "tripMileage");
            return (Criteria) this;
        }

        public Criteria andTripMileageIn(List<BigDecimal> values) {
            addCriterion("trip_mileage in", values, "tripMileage");
            return (Criteria) this;
        }

        public Criteria andTripMileageNotIn(List<BigDecimal> values) {
            addCriterion("trip_mileage not in", values, "tripMileage");
            return (Criteria) this;
        }

        public Criteria andTripMileageBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("trip_mileage between", value1, value2, "tripMileage");
            return (Criteria) this;
        }

        public Criteria andTripMileageNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("trip_mileage not between", value1, value2, "tripMileage");
            return (Criteria) this;
        }

        public Criteria andMotorcadeIdIsNull() {
            addCriterion("motorcade_id is null");
            return (Criteria) this;
        }

        public Criteria andMotorcadeIdIsNotNull() {
            addCriterion("motorcade_id is not null");
            return (Criteria) this;
        }

        public Criteria andMotorcadeIdEqualTo(Integer value) {
            addCriterion("motorcade_id =", value, "motorcadeId");
            return (Criteria) this;
        }

        public Criteria andMotorcadeIdNotEqualTo(Integer value) {
            addCriterion("motorcade_id <>", value, "motorcadeId");
            return (Criteria) this;
        }

        public Criteria andMotorcadeIdGreaterThan(Integer value) {
            addCriterion("motorcade_id >", value, "motorcadeId");
            return (Criteria) this;
        }

        public Criteria andMotorcadeIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("motorcade_id >=", value, "motorcadeId");
            return (Criteria) this;
        }

        public Criteria andMotorcadeIdLessThan(Integer value) {
            addCriterion("motorcade_id <", value, "motorcadeId");
            return (Criteria) this;
        }

        public Criteria andMotorcadeIdLessThanOrEqualTo(Integer value) {
            addCriterion("motorcade_id <=", value, "motorcadeId");
            return (Criteria) this;
        }

        public Criteria andMotorcadeIdIn(List<Integer> values) {
            addCriterion("motorcade_id in", values, "motorcadeId");
            return (Criteria) this;
        }

        public Criteria andMotorcadeIdNotIn(List<Integer> values) {
            addCriterion("motorcade_id not in", values, "motorcadeId");
            return (Criteria) this;
        }

        public Criteria andMotorcadeIdBetween(Integer value1, Integer value2) {
            addCriterion("motorcade_id between", value1, value2, "motorcadeId");
            return (Criteria) this;
        }

        public Criteria andMotorcadeIdNotBetween(Integer value1, Integer value2) {
            addCriterion("motorcade_id not between", value1, value2, "motorcadeId");
            return (Criteria) this;
        }

        public Criteria andMotorcadeNameIsNull() {
            addCriterion("motorcade_name is null");
            return (Criteria) this;
        }

        public Criteria andMotorcadeNameIsNotNull() {
            addCriterion("motorcade_name is not null");
            return (Criteria) this;
        }

        public Criteria andMotorcadeNameEqualTo(String value) {
            addCriterion("motorcade_name =", value, "motorcadeName");
            return (Criteria) this;
        }

        public Criteria andMotorcadeNameNotEqualTo(String value) {
            addCriterion("motorcade_name <>", value, "motorcadeName");
            return (Criteria) this;
        }

        public Criteria andMotorcadeNameGreaterThan(String value) {
            addCriterion("motorcade_name >", value, "motorcadeName");
            return (Criteria) this;
        }

        public Criteria andMotorcadeNameGreaterThanOrEqualTo(String value) {
            addCriterion("motorcade_name >=", value, "motorcadeName");
            return (Criteria) this;
        }

        public Criteria andMotorcadeNameLessThan(String value) {
            addCriterion("motorcade_name <", value, "motorcadeName");
            return (Criteria) this;
        }

        public Criteria andMotorcadeNameLessThanOrEqualTo(String value) {
            addCriterion("motorcade_name <=", value, "motorcadeName");
            return (Criteria) this;
        }

        public Criteria andMotorcadeNameLike(String value) {
            addCriterion("motorcade_name like", value, "motorcadeName");
            return (Criteria) this;
        }

        public Criteria andMotorcadeNameNotLike(String value) {
            addCriterion("motorcade_name not like", value, "motorcadeName");
            return (Criteria) this;
        }

        public Criteria andMotorcadeNameIn(List<String> values) {
            addCriterion("motorcade_name in", values, "motorcadeName");
            return (Criteria) this;
        }

        public Criteria andMotorcadeNameNotIn(List<String> values) {
            addCriterion("motorcade_name not in", values, "motorcadeName");
            return (Criteria) this;
        }

        public Criteria andMotorcadeNameBetween(String value1, String value2) {
            addCriterion("motorcade_name between", value1, value2, "motorcadeName");
            return (Criteria) this;
        }

        public Criteria andMotorcadeNameNotBetween(String value1, String value2) {
            addCriterion("motorcade_name not between", value1, value2, "motorcadeName");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyIdIsNull() {
            addCriterion("customer_company_id is null");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyIdIsNotNull() {
            addCriterion("customer_company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyIdEqualTo(Integer value) {
            addCriterion("customer_company_id =", value, "customerCompanyId");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyIdNotEqualTo(Integer value) {
            addCriterion("customer_company_id <>", value, "customerCompanyId");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyIdGreaterThan(Integer value) {
            addCriterion("customer_company_id >", value, "customerCompanyId");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("customer_company_id >=", value, "customerCompanyId");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyIdLessThan(Integer value) {
            addCriterion("customer_company_id <", value, "customerCompanyId");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyIdLessThanOrEqualTo(Integer value) {
            addCriterion("customer_company_id <=", value, "customerCompanyId");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyIdIn(List<Integer> values) {
            addCriterion("customer_company_id in", values, "customerCompanyId");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyIdNotIn(List<Integer> values) {
            addCriterion("customer_company_id not in", values, "customerCompanyId");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyIdBetween(Integer value1, Integer value2) {
            addCriterion("customer_company_id between", value1, value2, "customerCompanyId");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyIdNotBetween(Integer value1, Integer value2) {
            addCriterion("customer_company_id not between", value1, value2, "customerCompanyId");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyCodeIsNull() {
            addCriterion("customer_company_code is null");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyCodeIsNotNull() {
            addCriterion("customer_company_code is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyCodeEqualTo(String value) {
            addCriterion("customer_company_code =", value, "customerCompanyCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyCodeNotEqualTo(String value) {
            addCriterion("customer_company_code <>", value, "customerCompanyCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyCodeGreaterThan(String value) {
            addCriterion("customer_company_code >", value, "customerCompanyCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("customer_company_code >=", value, "customerCompanyCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyCodeLessThan(String value) {
            addCriterion("customer_company_code <", value, "customerCompanyCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("customer_company_code <=", value, "customerCompanyCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyCodeLike(String value) {
            addCriterion("customer_company_code like", value, "customerCompanyCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyCodeNotLike(String value) {
            addCriterion("customer_company_code not like", value, "customerCompanyCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyCodeIn(List<String> values) {
            addCriterion("customer_company_code in", values, "customerCompanyCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyCodeNotIn(List<String> values) {
            addCriterion("customer_company_code not in", values, "customerCompanyCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyCodeBetween(String value1, String value2) {
            addCriterion("customer_company_code between", value1, value2, "customerCompanyCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("customer_company_code not between", value1, value2, "customerCompanyCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyNameIsNull() {
            addCriterion("customer_company_name is null");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyNameIsNotNull() {
            addCriterion("customer_company_name is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyNameEqualTo(String value) {
            addCriterion("customer_company_name =", value, "customerCompanyName");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyNameNotEqualTo(String value) {
            addCriterion("customer_company_name <>", value, "customerCompanyName");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyNameGreaterThan(String value) {
            addCriterion("customer_company_name >", value, "customerCompanyName");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyNameGreaterThanOrEqualTo(String value) {
            addCriterion("customer_company_name >=", value, "customerCompanyName");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyNameLessThan(String value) {
            addCriterion("customer_company_name <", value, "customerCompanyName");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyNameLessThanOrEqualTo(String value) {
            addCriterion("customer_company_name <=", value, "customerCompanyName");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyNameLike(String value) {
            addCriterion("customer_company_name like", value, "customerCompanyName");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyNameNotLike(String value) {
            addCriterion("customer_company_name not like", value, "customerCompanyName");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyNameIn(List<String> values) {
            addCriterion("customer_company_name in", values, "customerCompanyName");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyNameNotIn(List<String> values) {
            addCriterion("customer_company_name not in", values, "customerCompanyName");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyNameBetween(String value1, String value2) {
            addCriterion("customer_company_name between", value1, value2, "customerCompanyName");
            return (Criteria) this;
        }

        public Criteria andCustomerCompanyNameNotBetween(String value1, String value2) {
            addCriterion("customer_company_name not between", value1, value2, "customerCompanyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIsNull() {
            addCriterion("company_name is null");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIsNotNull() {
            addCriterion("company_name is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyNameEqualTo(String value) {
            addCriterion("company_name =", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotEqualTo(String value) {
            addCriterion("company_name <>", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameGreaterThan(String value) {
            addCriterion("company_name >", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameGreaterThanOrEqualTo(String value) {
            addCriterion("company_name >=", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLessThan(String value) {
            addCriterion("company_name <", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLessThanOrEqualTo(String value) {
            addCriterion("company_name <=", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLike(String value) {
            addCriterion("company_name like", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotLike(String value) {
            addCriterion("company_name not like", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIn(List<String> values) {
            addCriterion("company_name in", values, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotIn(List<String> values) {
            addCriterion("company_name not in", values, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameBetween(String value1, String value2) {
            addCriterion("company_name between", value1, value2, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotBetween(String value1, String value2) {
            addCriterion("company_name not between", value1, value2, "companyName");
            return (Criteria) this;
        }

        public Criteria andFixedPriceValidIsNull() {
            addCriterion("fixed_price_valid is null");
            return (Criteria) this;
        }

        public Criteria andFixedPriceValidIsNotNull() {
            addCriterion("fixed_price_valid is not null");
            return (Criteria) this;
        }

        public Criteria andFixedPriceValidEqualTo(Boolean value) {
            addCriterion("fixed_price_valid =", value, "fixedPriceValid");
            return (Criteria) this;
        }

        public Criteria andFixedPriceValidNotEqualTo(Boolean value) {
            addCriterion("fixed_price_valid <>", value, "fixedPriceValid");
            return (Criteria) this;
        }

        public Criteria andFixedPriceValidGreaterThan(Boolean value) {
            addCriterion("fixed_price_valid >", value, "fixedPriceValid");
            return (Criteria) this;
        }

        public Criteria andFixedPriceValidGreaterThanOrEqualTo(Boolean value) {
            addCriterion("fixed_price_valid >=", value, "fixedPriceValid");
            return (Criteria) this;
        }

        public Criteria andFixedPriceValidLessThan(Boolean value) {
            addCriterion("fixed_price_valid <", value, "fixedPriceValid");
            return (Criteria) this;
        }

        public Criteria andFixedPriceValidLessThanOrEqualTo(Boolean value) {
            addCriterion("fixed_price_valid <=", value, "fixedPriceValid");
            return (Criteria) this;
        }

        public Criteria andFixedPriceValidIn(List<Boolean> values) {
            addCriterion("fixed_price_valid in", values, "fixedPriceValid");
            return (Criteria) this;
        }

        public Criteria andFixedPriceValidNotIn(List<Boolean> values) {
            addCriterion("fixed_price_valid not in", values, "fixedPriceValid");
            return (Criteria) this;
        }

        public Criteria andFixedPriceValidBetween(Boolean value1, Boolean value2) {
            addCriterion("fixed_price_valid between", value1, value2, "fixedPriceValid");
            return (Criteria) this;
        }

        public Criteria andFixedPriceValidNotBetween(Boolean value1, Boolean value2) {
            addCriterion("fixed_price_valid not between", value1, value2, "fixedPriceValid");
            return (Criteria) this;
        }

        public Criteria andFixedPriceIsNull() {
            addCriterion("fixed_price is null");
            return (Criteria) this;
        }

        public Criteria andFixedPriceIsNotNull() {
            addCriterion("fixed_price is not null");
            return (Criteria) this;
        }

        public Criteria andFixedPriceEqualTo(BigDecimal value) {
            addCriterion("fixed_price =", value, "fixedPrice");
            return (Criteria) this;
        }

        public Criteria andFixedPriceNotEqualTo(BigDecimal value) {
            addCriterion("fixed_price <>", value, "fixedPrice");
            return (Criteria) this;
        }

        public Criteria andFixedPriceGreaterThan(BigDecimal value) {
            addCriterion("fixed_price >", value, "fixedPrice");
            return (Criteria) this;
        }

        public Criteria andFixedPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("fixed_price >=", value, "fixedPrice");
            return (Criteria) this;
        }

        public Criteria andFixedPriceLessThan(BigDecimal value) {
            addCriterion("fixed_price <", value, "fixedPrice");
            return (Criteria) this;
        }

        public Criteria andFixedPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("fixed_price <=", value, "fixedPrice");
            return (Criteria) this;
        }

        public Criteria andFixedPriceIn(List<BigDecimal> values) {
            addCriterion("fixed_price in", values, "fixedPrice");
            return (Criteria) this;
        }

        public Criteria andFixedPriceNotIn(List<BigDecimal> values) {
            addCriterion("fixed_price not in", values, "fixedPrice");
            return (Criteria) this;
        }

        public Criteria andFixedPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("fixed_price between", value1, value2, "fixedPrice");
            return (Criteria) this;
        }

        public Criteria andFixedPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("fixed_price not between", value1, value2, "fixedPrice");
            return (Criteria) this;
        }

        public Criteria andStructNameIsNull() {
            addCriterion("struct_name is null");
            return (Criteria) this;
        }

        public Criteria andStructNameIsNotNull() {
            addCriterion("struct_name is not null");
            return (Criteria) this;
        }

        public Criteria andStructNameEqualTo(String value) {
            addCriterion("struct_name =", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameNotEqualTo(String value) {
            addCriterion("struct_name <>", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameGreaterThan(String value) {
            addCriterion("struct_name >", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameGreaterThanOrEqualTo(String value) {
            addCriterion("struct_name >=", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameLessThan(String value) {
            addCriterion("struct_name <", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameLessThanOrEqualTo(String value) {
            addCriterion("struct_name <=", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameLike(String value) {
            addCriterion("struct_name like", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameNotLike(String value) {
            addCriterion("struct_name not like", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameIn(List<String> values) {
            addCriterion("struct_name in", values, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameNotIn(List<String> values) {
            addCriterion("struct_name not in", values, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameBetween(String value1, String value2) {
            addCriterion("struct_name between", value1, value2, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameNotBetween(String value1, String value2) {
            addCriterion("struct_name not between", value1, value2, "structName");
            return (Criteria) this;
        }

        public Criteria andPassengerStructIdIsNull() {
            addCriterion("passenger_struct_id is null");
            return (Criteria) this;
        }

        public Criteria andPassengerStructIdIsNotNull() {
            addCriterion("passenger_struct_id is not null");
            return (Criteria) this;
        }

        public Criteria andPassengerStructIdEqualTo(Integer value) {
            addCriterion("passenger_struct_id =", value, "passengerStructId");
            return (Criteria) this;
        }

        public Criteria andPassengerStructIdNotEqualTo(Integer value) {
            addCriterion("passenger_struct_id <>", value, "passengerStructId");
            return (Criteria) this;
        }

        public Criteria andPassengerStructIdGreaterThan(Integer value) {
            addCriterion("passenger_struct_id >", value, "passengerStructId");
            return (Criteria) this;
        }

        public Criteria andPassengerStructIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("passenger_struct_id >=", value, "passengerStructId");
            return (Criteria) this;
        }

        public Criteria andPassengerStructIdLessThan(Integer value) {
            addCriterion("passenger_struct_id <", value, "passengerStructId");
            return (Criteria) this;
        }

        public Criteria andPassengerStructIdLessThanOrEqualTo(Integer value) {
            addCriterion("passenger_struct_id <=", value, "passengerStructId");
            return (Criteria) this;
        }

        public Criteria andPassengerStructIdIn(List<Integer> values) {
            addCriterion("passenger_struct_id in", values, "passengerStructId");
            return (Criteria) this;
        }

        public Criteria andPassengerStructIdNotIn(List<Integer> values) {
            addCriterion("passenger_struct_id not in", values, "passengerStructId");
            return (Criteria) this;
        }

        public Criteria andPassengerStructIdBetween(Integer value1, Integer value2) {
            addCriterion("passenger_struct_id between", value1, value2, "passengerStructId");
            return (Criteria) this;
        }

        public Criteria andPassengerStructIdNotBetween(Integer value1, Integer value2) {
            addCriterion("passenger_struct_id not between", value1, value2, "passengerStructId");
            return (Criteria) this;
        }

        public Criteria andPassengerStructNameIsNull() {
            addCriterion("passenger_struct_name is null");
            return (Criteria) this;
        }

        public Criteria andPassengerStructNameIsNotNull() {
            addCriterion("passenger_struct_name is not null");
            return (Criteria) this;
        }

        public Criteria andPassengerStructNameEqualTo(String value) {
            addCriterion("passenger_struct_name =", value, "passengerStructName");
            return (Criteria) this;
        }

        public Criteria andPassengerStructNameNotEqualTo(String value) {
            addCriterion("passenger_struct_name <>", value, "passengerStructName");
            return (Criteria) this;
        }

        public Criteria andPassengerStructNameGreaterThan(String value) {
            addCriterion("passenger_struct_name >", value, "passengerStructName");
            return (Criteria) this;
        }

        public Criteria andPassengerStructNameGreaterThanOrEqualTo(String value) {
            addCriterion("passenger_struct_name >=", value, "passengerStructName");
            return (Criteria) this;
        }

        public Criteria andPassengerStructNameLessThan(String value) {
            addCriterion("passenger_struct_name <", value, "passengerStructName");
            return (Criteria) this;
        }

        public Criteria andPassengerStructNameLessThanOrEqualTo(String value) {
            addCriterion("passenger_struct_name <=", value, "passengerStructName");
            return (Criteria) this;
        }

        public Criteria andPassengerStructNameLike(String value) {
            addCriterion("passenger_struct_name like", value, "passengerStructName");
            return (Criteria) this;
        }

        public Criteria andPassengerStructNameNotLike(String value) {
            addCriterion("passenger_struct_name not like", value, "passengerStructName");
            return (Criteria) this;
        }

        public Criteria andPassengerStructNameIn(List<String> values) {
            addCriterion("passenger_struct_name in", values, "passengerStructName");
            return (Criteria) this;
        }

        public Criteria andPassengerStructNameNotIn(List<String> values) {
            addCriterion("passenger_struct_name not in", values, "passengerStructName");
            return (Criteria) this;
        }

        public Criteria andPassengerStructNameBetween(String value1, String value2) {
            addCriterion("passenger_struct_name between", value1, value2, "passengerStructName");
            return (Criteria) this;
        }

        public Criteria andPassengerStructNameNotBetween(String value1, String value2) {
            addCriterion("passenger_struct_name not between", value1, value2, "passengerStructName");
            return (Criteria) this;
        }

        public Criteria andUserExceptionMarkIsNull() {
            addCriterion("user_exception_mark is null");
            return (Criteria) this;
        }

        public Criteria andUserExceptionMarkIsNotNull() {
            addCriterion("user_exception_mark is not null");
            return (Criteria) this;
        }

        public Criteria andUserExceptionMarkEqualTo(String value) {
            addCriterion("user_exception_mark =", value, "userExceptionMark");
            return (Criteria) this;
        }

        public Criteria andUserExceptionMarkNotEqualTo(String value) {
            addCriterion("user_exception_mark <>", value, "userExceptionMark");
            return (Criteria) this;
        }

        public Criteria andUserExceptionMarkGreaterThan(String value) {
            addCriterion("user_exception_mark >", value, "userExceptionMark");
            return (Criteria) this;
        }

        public Criteria andUserExceptionMarkGreaterThanOrEqualTo(String value) {
            addCriterion("user_exception_mark >=", value, "userExceptionMark");
            return (Criteria) this;
        }

        public Criteria andUserExceptionMarkLessThan(String value) {
            addCriterion("user_exception_mark <", value, "userExceptionMark");
            return (Criteria) this;
        }

        public Criteria andUserExceptionMarkLessThanOrEqualTo(String value) {
            addCriterion("user_exception_mark <=", value, "userExceptionMark");
            return (Criteria) this;
        }

        public Criteria andUserExceptionMarkLike(String value) {
            addCriterion("user_exception_mark like", value, "userExceptionMark");
            return (Criteria) this;
        }

        public Criteria andUserExceptionMarkNotLike(String value) {
            addCriterion("user_exception_mark not like", value, "userExceptionMark");
            return (Criteria) this;
        }

        public Criteria andUserExceptionMarkIn(List<String> values) {
            addCriterion("user_exception_mark in", values, "userExceptionMark");
            return (Criteria) this;
        }

        public Criteria andUserExceptionMarkNotIn(List<String> values) {
            addCriterion("user_exception_mark not in", values, "userExceptionMark");
            return (Criteria) this;
        }

        public Criteria andUserExceptionMarkBetween(String value1, String value2) {
            addCriterion("user_exception_mark between", value1, value2, "userExceptionMark");
            return (Criteria) this;
        }

        public Criteria andUserExceptionMarkNotBetween(String value1, String value2) {
            addCriterion("user_exception_mark not between", value1, value2, "userExceptionMark");
            return (Criteria) this;
        }

        public Criteria andMileageStatTypeIsNull() {
            addCriterion("mileage_stat_type is null");
            return (Criteria) this;
        }

        public Criteria andMileageStatTypeIsNotNull() {
            addCriterion("mileage_stat_type is not null");
            return (Criteria) this;
        }

        public Criteria andMileageStatTypeEqualTo(Byte value) {
            addCriterion("mileage_stat_type =", value, "mileageStatType");
            return (Criteria) this;
        }

        public Criteria andMileageStatTypeNotEqualTo(Byte value) {
            addCriterion("mileage_stat_type <>", value, "mileageStatType");
            return (Criteria) this;
        }

        public Criteria andMileageStatTypeGreaterThan(Byte value) {
            addCriterion("mileage_stat_type >", value, "mileageStatType");
            return (Criteria) this;
        }

        public Criteria andMileageStatTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("mileage_stat_type >=", value, "mileageStatType");
            return (Criteria) this;
        }

        public Criteria andMileageStatTypeLessThan(Byte value) {
            addCriterion("mileage_stat_type <", value, "mileageStatType");
            return (Criteria) this;
        }

        public Criteria andMileageStatTypeLessThanOrEqualTo(Byte value) {
            addCriterion("mileage_stat_type <=", value, "mileageStatType");
            return (Criteria) this;
        }

        public Criteria andMileageStatTypeIn(List<Byte> values) {
            addCriterion("mileage_stat_type in", values, "mileageStatType");
            return (Criteria) this;
        }

        public Criteria andMileageStatTypeNotIn(List<Byte> values) {
            addCriterion("mileage_stat_type not in", values, "mileageStatType");
            return (Criteria) this;
        }

        public Criteria andMileageStatTypeBetween(Byte value1, Byte value2) {
            addCriterion("mileage_stat_type between", value1, value2, "mileageStatType");
            return (Criteria) this;
        }

        public Criteria andMileageStatTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("mileage_stat_type not between", value1, value2, "mileageStatType");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNull() {
            addCriterion("deleted is null");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNotNull() {
            addCriterion("deleted is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedEqualTo(Boolean value) {
            addCriterion("deleted =", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotEqualTo(Boolean value) {
            addCriterion("deleted <>", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThan(Boolean value) {
            addCriterion("deleted >", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted >=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThan(Boolean value) {
            addCriterion("deleted <", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted <=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedIn(List<Boolean> values) {
            addCriterion("deleted in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotIn(List<Boolean> values) {
            addCriterion("deleted not in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted not between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andVehiclePrivacyFlagIsNull() {
            addCriterion("vehicle_privacy_flag is null");
            return (Criteria) this;
        }

        public Criteria andVehiclePrivacyFlagIsNotNull() {
            addCriterion("vehicle_privacy_flag is not null");
            return (Criteria) this;
        }

        public Criteria andVehiclePrivacyFlagEqualTo(Byte value) {
            addCriterion("vehicle_privacy_flag =", value, "vehiclePrivacyFlag");
            return (Criteria) this;
        }

        public Criteria andVehiclePrivacyFlagNotEqualTo(Byte value) {
            addCriterion("vehicle_privacy_flag <>", value, "vehiclePrivacyFlag");
            return (Criteria) this;
        }

        public Criteria andVehiclePrivacyFlagGreaterThan(Byte value) {
            addCriterion("vehicle_privacy_flag >", value, "vehiclePrivacyFlag");
            return (Criteria) this;
        }

        public Criteria andVehiclePrivacyFlagGreaterThanOrEqualTo(Byte value) {
            addCriterion("vehicle_privacy_flag >=", value, "vehiclePrivacyFlag");
            return (Criteria) this;
        }

        public Criteria andVehiclePrivacyFlagLessThan(Byte value) {
            addCriterion("vehicle_privacy_flag <", value, "vehiclePrivacyFlag");
            return (Criteria) this;
        }

        public Criteria andVehiclePrivacyFlagLessThanOrEqualTo(Byte value) {
            addCriterion("vehicle_privacy_flag <=", value, "vehiclePrivacyFlag");
            return (Criteria) this;
        }

        public Criteria andVehiclePrivacyFlagIn(List<Byte> values) {
            addCriterion("vehicle_privacy_flag in", values, "vehiclePrivacyFlag");
            return (Criteria) this;
        }

        public Criteria andVehiclePrivacyFlagNotIn(List<Byte> values) {
            addCriterion("vehicle_privacy_flag not in", values, "vehiclePrivacyFlag");
            return (Criteria) this;
        }

        public Criteria andVehiclePrivacyFlagBetween(Byte value1, Byte value2) {
            addCriterion("vehicle_privacy_flag between", value1, value2, "vehiclePrivacyFlag");
            return (Criteria) this;
        }

        public Criteria andVehiclePrivacyFlagNotBetween(Byte value1, Byte value2) {
            addCriterion("vehicle_privacy_flag not between", value1, value2, "vehiclePrivacyFlag");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyIdIsNull() {
            addCriterion("vehicle_company_id is null");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyIdIsNotNull() {
            addCriterion("vehicle_company_id is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyIdEqualTo(Integer value) {
            addCriterion("vehicle_company_id =", value, "vehicleCompanyId");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyIdNotEqualTo(Integer value) {
            addCriterion("vehicle_company_id <>", value, "vehicleCompanyId");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyIdGreaterThan(Integer value) {
            addCriterion("vehicle_company_id >", value, "vehicleCompanyId");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("vehicle_company_id >=", value, "vehicleCompanyId");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyIdLessThan(Integer value) {
            addCriterion("vehicle_company_id <", value, "vehicleCompanyId");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyIdLessThanOrEqualTo(Integer value) {
            addCriterion("vehicle_company_id <=", value, "vehicleCompanyId");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyIdIn(List<Integer> values) {
            addCriterion("vehicle_company_id in", values, "vehicleCompanyId");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyIdNotIn(List<Integer> values) {
            addCriterion("vehicle_company_id not in", values, "vehicleCompanyId");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyIdBetween(Integer value1, Integer value2) {
            addCriterion("vehicle_company_id between", value1, value2, "vehicleCompanyId");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyIdNotBetween(Integer value1, Integer value2) {
            addCriterion("vehicle_company_id not between", value1, value2, "vehicleCompanyId");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyNameIsNull() {
            addCriterion("vehicle_company_name is null");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyNameIsNotNull() {
            addCriterion("vehicle_company_name is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyNameEqualTo(String value) {
            addCriterion("vehicle_company_name =", value, "vehicleCompanyName");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyNameNotEqualTo(String value) {
            addCriterion("vehicle_company_name <>", value, "vehicleCompanyName");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyNameGreaterThan(String value) {
            addCriterion("vehicle_company_name >", value, "vehicleCompanyName");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyNameGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_company_name >=", value, "vehicleCompanyName");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyNameLessThan(String value) {
            addCriterion("vehicle_company_name <", value, "vehicleCompanyName");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyNameLessThanOrEqualTo(String value) {
            addCriterion("vehicle_company_name <=", value, "vehicleCompanyName");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyNameLike(String value) {
            addCriterion("vehicle_company_name like", value, "vehicleCompanyName");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyNameNotLike(String value) {
            addCriterion("vehicle_company_name not like", value, "vehicleCompanyName");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyNameIn(List<String> values) {
            addCriterion("vehicle_company_name in", values, "vehicleCompanyName");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyNameNotIn(List<String> values) {
            addCriterion("vehicle_company_name not in", values, "vehicleCompanyName");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyNameBetween(String value1, String value2) {
            addCriterion("vehicle_company_name between", value1, value2, "vehicleCompanyName");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyNameNotBetween(String value1, String value2) {
            addCriterion("vehicle_company_name not between", value1, value2, "vehicleCompanyName");
            return (Criteria) this;
        }

        public Criteria andDriverCompanyIdIsNull() {
            addCriterion("driver_company_id is null");
            return (Criteria) this;
        }

        public Criteria andDriverCompanyIdIsNotNull() {
            addCriterion("driver_company_id is not null");
            return (Criteria) this;
        }

        public Criteria andDriverCompanyIdEqualTo(Integer value) {
            addCriterion("driver_company_id =", value, "driverCompanyId");
            return (Criteria) this;
        }

        public Criteria andDriverCompanyIdNotEqualTo(Integer value) {
            addCriterion("driver_company_id <>", value, "driverCompanyId");
            return (Criteria) this;
        }

        public Criteria andDriverCompanyIdGreaterThan(Integer value) {
            addCriterion("driver_company_id >", value, "driverCompanyId");
            return (Criteria) this;
        }

        public Criteria andDriverCompanyIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("driver_company_id >=", value, "driverCompanyId");
            return (Criteria) this;
        }

        public Criteria andDriverCompanyIdLessThan(Integer value) {
            addCriterion("driver_company_id <", value, "driverCompanyId");
            return (Criteria) this;
        }

        public Criteria andDriverCompanyIdLessThanOrEqualTo(Integer value) {
            addCriterion("driver_company_id <=", value, "driverCompanyId");
            return (Criteria) this;
        }

        public Criteria andDriverCompanyIdIn(List<Integer> values) {
            addCriterion("driver_company_id in", values, "driverCompanyId");
            return (Criteria) this;
        }

        public Criteria andDriverCompanyIdNotIn(List<Integer> values) {
            addCriterion("driver_company_id not in", values, "driverCompanyId");
            return (Criteria) this;
        }

        public Criteria andDriverCompanyIdBetween(Integer value1, Integer value2) {
            addCriterion("driver_company_id between", value1, value2, "driverCompanyId");
            return (Criteria) this;
        }

        public Criteria andDriverCompanyIdNotBetween(Integer value1, Integer value2) {
            addCriterion("driver_company_id not between", value1, value2, "driverCompanyId");
            return (Criteria) this;
        }

        public Criteria andDriverCompanyNameIsNull() {
            addCriterion("driver_company_name is null");
            return (Criteria) this;
        }

        public Criteria andDriverCompanyNameIsNotNull() {
            addCriterion("driver_company_name is not null");
            return (Criteria) this;
        }

        public Criteria andDriverCompanyNameEqualTo(String value) {
            addCriterion("driver_company_name =", value, "driverCompanyName");
            return (Criteria) this;
        }

        public Criteria andDriverCompanyNameNotEqualTo(String value) {
            addCriterion("driver_company_name <>", value, "driverCompanyName");
            return (Criteria) this;
        }

        public Criteria andDriverCompanyNameGreaterThan(String value) {
            addCriterion("driver_company_name >", value, "driverCompanyName");
            return (Criteria) this;
        }

        public Criteria andDriverCompanyNameGreaterThanOrEqualTo(String value) {
            addCriterion("driver_company_name >=", value, "driverCompanyName");
            return (Criteria) this;
        }

        public Criteria andDriverCompanyNameLessThan(String value) {
            addCriterion("driver_company_name <", value, "driverCompanyName");
            return (Criteria) this;
        }

        public Criteria andDriverCompanyNameLessThanOrEqualTo(String value) {
            addCriterion("driver_company_name <=", value, "driverCompanyName");
            return (Criteria) this;
        }

        public Criteria andDriverCompanyNameLike(String value) {
            addCriterion("driver_company_name like", value, "driverCompanyName");
            return (Criteria) this;
        }

        public Criteria andDriverCompanyNameNotLike(String value) {
            addCriterion("driver_company_name not like", value, "driverCompanyName");
            return (Criteria) this;
        }

        public Criteria andDriverCompanyNameIn(List<String> values) {
            addCriterion("driver_company_name in", values, "driverCompanyName");
            return (Criteria) this;
        }

        public Criteria andDriverCompanyNameNotIn(List<String> values) {
            addCriterion("driver_company_name not in", values, "driverCompanyName");
            return (Criteria) this;
        }

        public Criteria andDriverCompanyNameBetween(String value1, String value2) {
            addCriterion("driver_company_name between", value1, value2, "driverCompanyName");
            return (Criteria) this;
        }

        public Criteria andDriverCompanyNameNotBetween(String value1, String value2) {
            addCriterion("driver_company_name not between", value1, value2, "driverCompanyName");
            return (Criteria) this;
        }

        public Criteria andThirdOrderNoIsNull() {
            addCriterion("third_order_no is null");
            return (Criteria) this;
        }

        public Criteria andThirdOrderNoIsNotNull() {
            addCriterion("third_order_no is not null");
            return (Criteria) this;
        }

        public Criteria andThirdOrderNoEqualTo(String value) {
            addCriterion("third_order_no =", value, "thirdOrderNo");
            return (Criteria) this;
        }

        public Criteria andThirdOrderNoNotEqualTo(String value) {
            addCriterion("third_order_no <>", value, "thirdOrderNo");
            return (Criteria) this;
        }

        public Criteria andThirdOrderNoGreaterThan(String value) {
            addCriterion("third_order_no >", value, "thirdOrderNo");
            return (Criteria) this;
        }

        public Criteria andThirdOrderNoGreaterThanOrEqualTo(String value) {
            addCriterion("third_order_no >=", value, "thirdOrderNo");
            return (Criteria) this;
        }

        public Criteria andThirdOrderNoLessThan(String value) {
            addCriterion("third_order_no <", value, "thirdOrderNo");
            return (Criteria) this;
        }

        public Criteria andThirdOrderNoLessThanOrEqualTo(String value) {
            addCriterion("third_order_no <=", value, "thirdOrderNo");
            return (Criteria) this;
        }

        public Criteria andThirdOrderNoLike(String value) {
            addCriterion("third_order_no like", value, "thirdOrderNo");
            return (Criteria) this;
        }

        public Criteria andThirdOrderNoNotLike(String value) {
            addCriterion("third_order_no not like", value, "thirdOrderNo");
            return (Criteria) this;
        }

        public Criteria andThirdOrderNoIn(List<String> values) {
            addCriterion("third_order_no in", values, "thirdOrderNo");
            return (Criteria) this;
        }

        public Criteria andThirdOrderNoNotIn(List<String> values) {
            addCriterion("third_order_no not in", values, "thirdOrderNo");
            return (Criteria) this;
        }

        public Criteria andThirdOrderNoBetween(String value1, String value2) {
            addCriterion("third_order_no between", value1, value2, "thirdOrderNo");
            return (Criteria) this;
        }

        public Criteria andThirdOrderNoNotBetween(String value1, String value2) {
            addCriterion("third_order_no not between", value1, value2, "thirdOrderNo");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}