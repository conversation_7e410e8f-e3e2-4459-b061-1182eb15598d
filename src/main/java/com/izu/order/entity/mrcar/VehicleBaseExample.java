package com.izu.order.entity.mrcar;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class VehicleBaseExample {
    /**
     * order_vehicle_base
     */
    protected String orderByClause;

    /**
     * order_vehicle_base
     */
    protected boolean distinct;

    /**
     * order_vehicle_base
     */
    protected List<Criteria> oredCriteria;

    public VehicleBaseExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andVehicleIdIsNull() {
            addCriterion("vehicle_id is null");
            return (Criteria) this;
        }

        public Criteria andVehicleIdIsNotNull() {
            addCriterion("vehicle_id is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleIdEqualTo(Long value) {
            addCriterion("vehicle_id =", value, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdNotEqualTo(Long value) {
            addCriterion("vehicle_id <>", value, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdGreaterThan(Long value) {
            addCriterion("vehicle_id >", value, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdGreaterThanOrEqualTo(Long value) {
            addCriterion("vehicle_id >=", value, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdLessThan(Long value) {
            addCriterion("vehicle_id <", value, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdLessThanOrEqualTo(Long value) {
            addCriterion("vehicle_id <=", value, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdIn(List<Long> values) {
            addCriterion("vehicle_id in", values, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdNotIn(List<Long> values) {
            addCriterion("vehicle_id not in", values, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdBetween(Long value1, Long value2) {
            addCriterion("vehicle_id between", value1, value2, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdNotBetween(Long value1, Long value2) {
            addCriterion("vehicle_id not between", value1, value2, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNull() {
            addCriterion("create_date is null");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNotNull() {
            addCriterion("create_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreateDateEqualTo(Date value) {
            addCriterion("create_date =", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotEqualTo(Date value) {
            addCriterion("create_date <>", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThan(Date value) {
            addCriterion("create_date >", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThanOrEqualTo(Date value) {
            addCriterion("create_date >=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThan(Date value) {
            addCriterion("create_date <", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThanOrEqualTo(Date value) {
            addCriterion("create_date <=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateIn(List<Date> values) {
            addCriterion("create_date in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotIn(List<Date> values) {
            addCriterion("create_date not in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateBetween(Date value1, Date value2) {
            addCriterion("create_date between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotBetween(Date value1, Date value2) {
            addCriterion("create_date not between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateIsNull() {
            addCriterion("update_date is null");
            return (Criteria) this;
        }

        public Criteria andUpdateDateIsNotNull() {
            addCriterion("update_date is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateDateEqualTo(Date value) {
            addCriterion("update_date =", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateNotEqualTo(Date value) {
            addCriterion("update_date <>", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateGreaterThan(Date value) {
            addCriterion("update_date >", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateGreaterThanOrEqualTo(Date value) {
            addCriterion("update_date >=", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateLessThan(Date value) {
            addCriterion("update_date <", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateLessThanOrEqualTo(Date value) {
            addCriterion("update_date <=", value, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateIn(List<Date> values) {
            addCriterion("update_date in", values, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateNotIn(List<Date> values) {
            addCriterion("update_date not in", values, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateBetween(Date value1, Date value2) {
            addCriterion("update_date between", value1, value2, "updateDate");
            return (Criteria) this;
        }

        public Criteria andUpdateDateNotBetween(Date value1, Date value2) {
            addCriterion("update_date not between", value1, value2, "updateDate");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseIsNull() {
            addCriterion("vehicle_license is null");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseIsNotNull() {
            addCriterion("vehicle_license is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseEqualTo(String value) {
            addCriterion("vehicle_license =", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseNotEqualTo(String value) {
            addCriterion("vehicle_license <>", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseGreaterThan(String value) {
            addCriterion("vehicle_license >", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_license >=", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseLessThan(String value) {
            addCriterion("vehicle_license <", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseLessThanOrEqualTo(String value) {
            addCriterion("vehicle_license <=", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseLike(String value) {
            addCriterion("vehicle_license like", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseNotLike(String value) {
            addCriterion("vehicle_license not like", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseIn(List<String> values) {
            addCriterion("vehicle_license in", values, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseNotIn(List<String> values) {
            addCriterion("vehicle_license not in", values, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseBetween(String value1, String value2) {
            addCriterion("vehicle_license between", value1, value2, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseNotBetween(String value1, String value2) {
            addCriterion("vehicle_license not between", value1, value2, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleVinIsNull() {
            addCriterion("vehicle_vin is null");
            return (Criteria) this;
        }

        public Criteria andVehicleVinIsNotNull() {
            addCriterion("vehicle_vin is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleVinEqualTo(String value) {
            addCriterion("vehicle_vin =", value, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinNotEqualTo(String value) {
            addCriterion("vehicle_vin <>", value, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinGreaterThan(String value) {
            addCriterion("vehicle_vin >", value, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_vin >=", value, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinLessThan(String value) {
            addCriterion("vehicle_vin <", value, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinLessThanOrEqualTo(String value) {
            addCriterion("vehicle_vin <=", value, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinLike(String value) {
            addCriterion("vehicle_vin like", value, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinNotLike(String value) {
            addCriterion("vehicle_vin not like", value, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinIn(List<String> values) {
            addCriterion("vehicle_vin in", values, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinNotIn(List<String> values) {
            addCriterion("vehicle_vin not in", values, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinBetween(String value1, String value2) {
            addCriterion("vehicle_vin between", value1, value2, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinNotBetween(String value1, String value2) {
            addCriterion("vehicle_vin not between", value1, value2, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandIdIsNull() {
            addCriterion("vehicle_brand_id is null");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandIdIsNotNull() {
            addCriterion("vehicle_brand_id is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandIdEqualTo(Integer value) {
            addCriterion("vehicle_brand_id =", value, "vehicleBrandId");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandIdNotEqualTo(Integer value) {
            addCriterion("vehicle_brand_id <>", value, "vehicleBrandId");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandIdGreaterThan(Integer value) {
            addCriterion("vehicle_brand_id >", value, "vehicleBrandId");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("vehicle_brand_id >=", value, "vehicleBrandId");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandIdLessThan(Integer value) {
            addCriterion("vehicle_brand_id <", value, "vehicleBrandId");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandIdLessThanOrEqualTo(Integer value) {
            addCriterion("vehicle_brand_id <=", value, "vehicleBrandId");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandIdIn(List<Integer> values) {
            addCriterion("vehicle_brand_id in", values, "vehicleBrandId");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandIdNotIn(List<Integer> values) {
            addCriterion("vehicle_brand_id not in", values, "vehicleBrandId");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandIdBetween(Integer value1, Integer value2) {
            addCriterion("vehicle_brand_id between", value1, value2, "vehicleBrandId");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandIdNotBetween(Integer value1, Integer value2) {
            addCriterion("vehicle_brand_id not between", value1, value2, "vehicleBrandId");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeIsNull() {
            addCriterion("vehicle_brand_code is null");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeIsNotNull() {
            addCriterion("vehicle_brand_code is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeEqualTo(String value) {
            addCriterion("vehicle_brand_code =", value, "vehicleBrandCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeNotEqualTo(String value) {
            addCriterion("vehicle_brand_code <>", value, "vehicleBrandCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeGreaterThan(String value) {
            addCriterion("vehicle_brand_code >", value, "vehicleBrandCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_brand_code >=", value, "vehicleBrandCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeLessThan(String value) {
            addCriterion("vehicle_brand_code <", value, "vehicleBrandCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeLessThanOrEqualTo(String value) {
            addCriterion("vehicle_brand_code <=", value, "vehicleBrandCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeLike(String value) {
            addCriterion("vehicle_brand_code like", value, "vehicleBrandCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeNotLike(String value) {
            addCriterion("vehicle_brand_code not like", value, "vehicleBrandCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeIn(List<String> values) {
            addCriterion("vehicle_brand_code in", values, "vehicleBrandCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeNotIn(List<String> values) {
            addCriterion("vehicle_brand_code not in", values, "vehicleBrandCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeBetween(String value1, String value2) {
            addCriterion("vehicle_brand_code between", value1, value2, "vehicleBrandCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeNotBetween(String value1, String value2) {
            addCriterion("vehicle_brand_code not between", value1, value2, "vehicleBrandCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandIsNull() {
            addCriterion("vehicle_brand is null");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandIsNotNull() {
            addCriterion("vehicle_brand is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandEqualTo(String value) {
            addCriterion("vehicle_brand =", value, "vehicleBrand");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandNotEqualTo(String value) {
            addCriterion("vehicle_brand <>", value, "vehicleBrand");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandGreaterThan(String value) {
            addCriterion("vehicle_brand >", value, "vehicleBrand");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_brand >=", value, "vehicleBrand");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandLessThan(String value) {
            addCriterion("vehicle_brand <", value, "vehicleBrand");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandLessThanOrEqualTo(String value) {
            addCriterion("vehicle_brand <=", value, "vehicleBrand");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandLike(String value) {
            addCriterion("vehicle_brand like", value, "vehicleBrand");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandNotLike(String value) {
            addCriterion("vehicle_brand not like", value, "vehicleBrand");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandIn(List<String> values) {
            addCriterion("vehicle_brand in", values, "vehicleBrand");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandNotIn(List<String> values) {
            addCriterion("vehicle_brand not in", values, "vehicleBrand");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandBetween(String value1, String value2) {
            addCriterion("vehicle_brand between", value1, value2, "vehicleBrand");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandNotBetween(String value1, String value2) {
            addCriterion("vehicle_brand not between", value1, value2, "vehicleBrand");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIdIsNull() {
            addCriterion("vehicle_model_id is null");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIdIsNotNull() {
            addCriterion("vehicle_model_id is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIdEqualTo(Integer value) {
            addCriterion("vehicle_model_id =", value, "vehicleModelId");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIdNotEqualTo(Integer value) {
            addCriterion("vehicle_model_id <>", value, "vehicleModelId");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIdGreaterThan(Integer value) {
            addCriterion("vehicle_model_id >", value, "vehicleModelId");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("vehicle_model_id >=", value, "vehicleModelId");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIdLessThan(Integer value) {
            addCriterion("vehicle_model_id <", value, "vehicleModelId");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIdLessThanOrEqualTo(Integer value) {
            addCriterion("vehicle_model_id <=", value, "vehicleModelId");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIdIn(List<Integer> values) {
            addCriterion("vehicle_model_id in", values, "vehicleModelId");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIdNotIn(List<Integer> values) {
            addCriterion("vehicle_model_id not in", values, "vehicleModelId");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIdBetween(Integer value1, Integer value2) {
            addCriterion("vehicle_model_id between", value1, value2, "vehicleModelId");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIdNotBetween(Integer value1, Integer value2) {
            addCriterion("vehicle_model_id not between", value1, value2, "vehicleModelId");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeIsNull() {
            addCriterion("vehicle_model_code is null");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeIsNotNull() {
            addCriterion("vehicle_model_code is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeEqualTo(String value) {
            addCriterion("vehicle_model_code =", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeNotEqualTo(String value) {
            addCriterion("vehicle_model_code <>", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeGreaterThan(String value) {
            addCriterion("vehicle_model_code >", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_model_code >=", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeLessThan(String value) {
            addCriterion("vehicle_model_code <", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeLessThanOrEqualTo(String value) {
            addCriterion("vehicle_model_code <=", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeLike(String value) {
            addCriterion("vehicle_model_code like", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeNotLike(String value) {
            addCriterion("vehicle_model_code not like", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeIn(List<String> values) {
            addCriterion("vehicle_model_code in", values, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeNotIn(List<String> values) {
            addCriterion("vehicle_model_code not in", values, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeBetween(String value1, String value2) {
            addCriterion("vehicle_model_code between", value1, value2, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeNotBetween(String value1, String value2) {
            addCriterion("vehicle_model_code not between", value1, value2, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIsNull() {
            addCriterion("vehicle_model is null");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIsNotNull() {
            addCriterion("vehicle_model is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleModelEqualTo(String value) {
            addCriterion("vehicle_model =", value, "vehicleModel");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNotEqualTo(String value) {
            addCriterion("vehicle_model <>", value, "vehicleModel");
            return (Criteria) this;
        }

        public Criteria andVehicleModelGreaterThan(String value) {
            addCriterion("vehicle_model >", value, "vehicleModel");
            return (Criteria) this;
        }

        public Criteria andVehicleModelGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_model >=", value, "vehicleModel");
            return (Criteria) this;
        }

        public Criteria andVehicleModelLessThan(String value) {
            addCriterion("vehicle_model <", value, "vehicleModel");
            return (Criteria) this;
        }

        public Criteria andVehicleModelLessThanOrEqualTo(String value) {
            addCriterion("vehicle_model <=", value, "vehicleModel");
            return (Criteria) this;
        }

        public Criteria andVehicleModelLike(String value) {
            addCriterion("vehicle_model like", value, "vehicleModel");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNotLike(String value) {
            addCriterion("vehicle_model not like", value, "vehicleModel");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIn(List<String> values) {
            addCriterion("vehicle_model in", values, "vehicleModel");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNotIn(List<String> values) {
            addCriterion("vehicle_model not in", values, "vehicleModel");
            return (Criteria) this;
        }

        public Criteria andVehicleModelBetween(String value1, String value2) {
            addCriterion("vehicle_model between", value1, value2, "vehicleModel");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNotBetween(String value1, String value2) {
            addCriterion("vehicle_model not between", value1, value2, "vehicleModel");
            return (Criteria) this;
        }

        public Criteria andEngineNumIsNull() {
            addCriterion("engine_num is null");
            return (Criteria) this;
        }

        public Criteria andEngineNumIsNotNull() {
            addCriterion("engine_num is not null");
            return (Criteria) this;
        }

        public Criteria andEngineNumEqualTo(String value) {
            addCriterion("engine_num =", value, "engineNum");
            return (Criteria) this;
        }

        public Criteria andEngineNumNotEqualTo(String value) {
            addCriterion("engine_num <>", value, "engineNum");
            return (Criteria) this;
        }

        public Criteria andEngineNumGreaterThan(String value) {
            addCriterion("engine_num >", value, "engineNum");
            return (Criteria) this;
        }

        public Criteria andEngineNumGreaterThanOrEqualTo(String value) {
            addCriterion("engine_num >=", value, "engineNum");
            return (Criteria) this;
        }

        public Criteria andEngineNumLessThan(String value) {
            addCriterion("engine_num <", value, "engineNum");
            return (Criteria) this;
        }

        public Criteria andEngineNumLessThanOrEqualTo(String value) {
            addCriterion("engine_num <=", value, "engineNum");
            return (Criteria) this;
        }

        public Criteria andEngineNumLike(String value) {
            addCriterion("engine_num like", value, "engineNum");
            return (Criteria) this;
        }

        public Criteria andEngineNumNotLike(String value) {
            addCriterion("engine_num not like", value, "engineNum");
            return (Criteria) this;
        }

        public Criteria andEngineNumIn(List<String> values) {
            addCriterion("engine_num in", values, "engineNum");
            return (Criteria) this;
        }

        public Criteria andEngineNumNotIn(List<String> values) {
            addCriterion("engine_num not in", values, "engineNum");
            return (Criteria) this;
        }

        public Criteria andEngineNumBetween(String value1, String value2) {
            addCriterion("engine_num between", value1, value2, "engineNum");
            return (Criteria) this;
        }

        public Criteria andEngineNumNotBetween(String value1, String value2) {
            addCriterion("engine_num not between", value1, value2, "engineNum");
            return (Criteria) this;
        }

        public Criteria andVehicleColorIsNull() {
            addCriterion("vehicle_color is null");
            return (Criteria) this;
        }

        public Criteria andVehicleColorIsNotNull() {
            addCriterion("vehicle_color is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleColorEqualTo(String value) {
            addCriterion("vehicle_color =", value, "vehicleColor");
            return (Criteria) this;
        }

        public Criteria andVehicleColorNotEqualTo(String value) {
            addCriterion("vehicle_color <>", value, "vehicleColor");
            return (Criteria) this;
        }

        public Criteria andVehicleColorGreaterThan(String value) {
            addCriterion("vehicle_color >", value, "vehicleColor");
            return (Criteria) this;
        }

        public Criteria andVehicleColorGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_color >=", value, "vehicleColor");
            return (Criteria) this;
        }

        public Criteria andVehicleColorLessThan(String value) {
            addCriterion("vehicle_color <", value, "vehicleColor");
            return (Criteria) this;
        }

        public Criteria andVehicleColorLessThanOrEqualTo(String value) {
            addCriterion("vehicle_color <=", value, "vehicleColor");
            return (Criteria) this;
        }

        public Criteria andVehicleColorLike(String value) {
            addCriterion("vehicle_color like", value, "vehicleColor");
            return (Criteria) this;
        }

        public Criteria andVehicleColorNotLike(String value) {
            addCriterion("vehicle_color not like", value, "vehicleColor");
            return (Criteria) this;
        }

        public Criteria andVehicleColorIn(List<String> values) {
            addCriterion("vehicle_color in", values, "vehicleColor");
            return (Criteria) this;
        }

        public Criteria andVehicleColorNotIn(List<String> values) {
            addCriterion("vehicle_color not in", values, "vehicleColor");
            return (Criteria) this;
        }

        public Criteria andVehicleColorBetween(String value1, String value2) {
            addCriterion("vehicle_color between", value1, value2, "vehicleColor");
            return (Criteria) this;
        }

        public Criteria andVehicleColorNotBetween(String value1, String value2) {
            addCriterion("vehicle_color not between", value1, value2, "vehicleColor");
            return (Criteria) this;
        }

        public Criteria andLicenseColorIsNull() {
            addCriterion("license_color is null");
            return (Criteria) this;
        }

        public Criteria andLicenseColorIsNotNull() {
            addCriterion("license_color is not null");
            return (Criteria) this;
        }

        public Criteria andLicenseColorEqualTo(String value) {
            addCriterion("license_color =", value, "licenseColor");
            return (Criteria) this;
        }

        public Criteria andLicenseColorNotEqualTo(String value) {
            addCriterion("license_color <>", value, "licenseColor");
            return (Criteria) this;
        }

        public Criteria andLicenseColorGreaterThan(String value) {
            addCriterion("license_color >", value, "licenseColor");
            return (Criteria) this;
        }

        public Criteria andLicenseColorGreaterThanOrEqualTo(String value) {
            addCriterion("license_color >=", value, "licenseColor");
            return (Criteria) this;
        }

        public Criteria andLicenseColorLessThan(String value) {
            addCriterion("license_color <", value, "licenseColor");
            return (Criteria) this;
        }

        public Criteria andLicenseColorLessThanOrEqualTo(String value) {
            addCriterion("license_color <=", value, "licenseColor");
            return (Criteria) this;
        }

        public Criteria andLicenseColorLike(String value) {
            addCriterion("license_color like", value, "licenseColor");
            return (Criteria) this;
        }

        public Criteria andLicenseColorNotLike(String value) {
            addCriterion("license_color not like", value, "licenseColor");
            return (Criteria) this;
        }

        public Criteria andLicenseColorIn(List<String> values) {
            addCriterion("license_color in", values, "licenseColor");
            return (Criteria) this;
        }

        public Criteria andLicenseColorNotIn(List<String> values) {
            addCriterion("license_color not in", values, "licenseColor");
            return (Criteria) this;
        }

        public Criteria andLicenseColorBetween(String value1, String value2) {
            addCriterion("license_color between", value1, value2, "licenseColor");
            return (Criteria) this;
        }

        public Criteria andLicenseColorNotBetween(String value1, String value2) {
            addCriterion("license_color not between", value1, value2, "licenseColor");
            return (Criteria) this;
        }

        public Criteria andPurchaseDateIsNull() {
            addCriterion("purchase_date is null");
            return (Criteria) this;
        }

        public Criteria andPurchaseDateIsNotNull() {
            addCriterion("purchase_date is not null");
            return (Criteria) this;
        }

        public Criteria andPurchaseDateEqualTo(String value) {
            addCriterion("purchase_date =", value, "purchaseDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseDateNotEqualTo(String value) {
            addCriterion("purchase_date <>", value, "purchaseDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseDateGreaterThan(String value) {
            addCriterion("purchase_date >", value, "purchaseDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseDateGreaterThanOrEqualTo(String value) {
            addCriterion("purchase_date >=", value, "purchaseDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseDateLessThan(String value) {
            addCriterion("purchase_date <", value, "purchaseDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseDateLessThanOrEqualTo(String value) {
            addCriterion("purchase_date <=", value, "purchaseDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseDateLike(String value) {
            addCriterion("purchase_date like", value, "purchaseDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseDateNotLike(String value) {
            addCriterion("purchase_date not like", value, "purchaseDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseDateIn(List<String> values) {
            addCriterion("purchase_date in", values, "purchaseDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseDateNotIn(List<String> values) {
            addCriterion("purchase_date not in", values, "purchaseDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseDateBetween(String value1, String value2) {
            addCriterion("purchase_date between", value1, value2, "purchaseDate");
            return (Criteria) this;
        }

        public Criteria andPurchaseDateNotBetween(String value1, String value2) {
            addCriterion("purchase_date not between", value1, value2, "purchaseDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateIsNull() {
            addCriterion("register_date is null");
            return (Criteria) this;
        }

        public Criteria andRegisterDateIsNotNull() {
            addCriterion("register_date is not null");
            return (Criteria) this;
        }

        public Criteria andRegisterDateEqualTo(String value) {
            addCriterion("register_date =", value, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateNotEqualTo(String value) {
            addCriterion("register_date <>", value, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateGreaterThan(String value) {
            addCriterion("register_date >", value, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateGreaterThanOrEqualTo(String value) {
            addCriterion("register_date >=", value, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateLessThan(String value) {
            addCriterion("register_date <", value, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateLessThanOrEqualTo(String value) {
            addCriterion("register_date <=", value, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateLike(String value) {
            addCriterion("register_date like", value, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateNotLike(String value) {
            addCriterion("register_date not like", value, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateIn(List<String> values) {
            addCriterion("register_date in", values, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateNotIn(List<String> values) {
            addCriterion("register_date not in", values, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateBetween(String value1, String value2) {
            addCriterion("register_date between", value1, value2, "registerDate");
            return (Criteria) this;
        }

        public Criteria andRegisterDateNotBetween(String value1, String value2) {
            addCriterion("register_date not between", value1, value2, "registerDate");
            return (Criteria) this;
        }

        public Criteria andDrivingLicenseUrlIsNull() {
            addCriterion("driving_license_url is null");
            return (Criteria) this;
        }

        public Criteria andDrivingLicenseUrlIsNotNull() {
            addCriterion("driving_license_url is not null");
            return (Criteria) this;
        }

        public Criteria andDrivingLicenseUrlEqualTo(String value) {
            addCriterion("driving_license_url =", value, "drivingLicenseUrl");
            return (Criteria) this;
        }

        public Criteria andDrivingLicenseUrlNotEqualTo(String value) {
            addCriterion("driving_license_url <>", value, "drivingLicenseUrl");
            return (Criteria) this;
        }

        public Criteria andDrivingLicenseUrlGreaterThan(String value) {
            addCriterion("driving_license_url >", value, "drivingLicenseUrl");
            return (Criteria) this;
        }

        public Criteria andDrivingLicenseUrlGreaterThanOrEqualTo(String value) {
            addCriterion("driving_license_url >=", value, "drivingLicenseUrl");
            return (Criteria) this;
        }

        public Criteria andDrivingLicenseUrlLessThan(String value) {
            addCriterion("driving_license_url <", value, "drivingLicenseUrl");
            return (Criteria) this;
        }

        public Criteria andDrivingLicenseUrlLessThanOrEqualTo(String value) {
            addCriterion("driving_license_url <=", value, "drivingLicenseUrl");
            return (Criteria) this;
        }

        public Criteria andDrivingLicenseUrlLike(String value) {
            addCriterion("driving_license_url like", value, "drivingLicenseUrl");
            return (Criteria) this;
        }

        public Criteria andDrivingLicenseUrlNotLike(String value) {
            addCriterion("driving_license_url not like", value, "drivingLicenseUrl");
            return (Criteria) this;
        }

        public Criteria andDrivingLicenseUrlIn(List<String> values) {
            addCriterion("driving_license_url in", values, "drivingLicenseUrl");
            return (Criteria) this;
        }

        public Criteria andDrivingLicenseUrlNotIn(List<String> values) {
            addCriterion("driving_license_url not in", values, "drivingLicenseUrl");
            return (Criteria) this;
        }

        public Criteria andDrivingLicenseUrlBetween(String value1, String value2) {
            addCriterion("driving_license_url between", value1, value2, "drivingLicenseUrl");
            return (Criteria) this;
        }

        public Criteria andDrivingLicenseUrlNotBetween(String value1, String value2) {
            addCriterion("driving_license_url not between", value1, value2, "drivingLicenseUrl");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeIsNull() {
            addCriterion("vehicle_type is null");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeIsNotNull() {
            addCriterion("vehicle_type is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeEqualTo(Byte value) {
            addCriterion("vehicle_type =", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeNotEqualTo(Byte value) {
            addCriterion("vehicle_type <>", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeGreaterThan(Byte value) {
            addCriterion("vehicle_type >", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("vehicle_type >=", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeLessThan(Byte value) {
            addCriterion("vehicle_type <", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeLessThanOrEqualTo(Byte value) {
            addCriterion("vehicle_type <=", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeIn(List<Byte> values) {
            addCriterion("vehicle_type in", values, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeNotIn(List<Byte> values) {
            addCriterion("vehicle_type not in", values, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeBetween(Byte value1, Byte value2) {
            addCriterion("vehicle_type between", value1, value2, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("vehicle_type not between", value1, value2, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeIsNull() {
            addCriterion("belong_city_code is null");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeIsNotNull() {
            addCriterion("belong_city_code is not null");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeEqualTo(String value) {
            addCriterion("belong_city_code =", value, "belongCityCode");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeNotEqualTo(String value) {
            addCriterion("belong_city_code <>", value, "belongCityCode");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeGreaterThan(String value) {
            addCriterion("belong_city_code >", value, "belongCityCode");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("belong_city_code >=", value, "belongCityCode");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeLessThan(String value) {
            addCriterion("belong_city_code <", value, "belongCityCode");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeLessThanOrEqualTo(String value) {
            addCriterion("belong_city_code <=", value, "belongCityCode");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeLike(String value) {
            addCriterion("belong_city_code like", value, "belongCityCode");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeNotLike(String value) {
            addCriterion("belong_city_code not like", value, "belongCityCode");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeIn(List<String> values) {
            addCriterion("belong_city_code in", values, "belongCityCode");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeNotIn(List<String> values) {
            addCriterion("belong_city_code not in", values, "belongCityCode");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeBetween(String value1, String value2) {
            addCriterion("belong_city_code between", value1, value2, "belongCityCode");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeNotBetween(String value1, String value2) {
            addCriterion("belong_city_code not between", value1, value2, "belongCityCode");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameIsNull() {
            addCriterion("belong_city_name is null");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameIsNotNull() {
            addCriterion("belong_city_name is not null");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameEqualTo(String value) {
            addCriterion("belong_city_name =", value, "belongCityName");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameNotEqualTo(String value) {
            addCriterion("belong_city_name <>", value, "belongCityName");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameGreaterThan(String value) {
            addCriterion("belong_city_name >", value, "belongCityName");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameGreaterThanOrEqualTo(String value) {
            addCriterion("belong_city_name >=", value, "belongCityName");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameLessThan(String value) {
            addCriterion("belong_city_name <", value, "belongCityName");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameLessThanOrEqualTo(String value) {
            addCriterion("belong_city_name <=", value, "belongCityName");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameLike(String value) {
            addCriterion("belong_city_name like", value, "belongCityName");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameNotLike(String value) {
            addCriterion("belong_city_name not like", value, "belongCityName");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameIn(List<String> values) {
            addCriterion("belong_city_name in", values, "belongCityName");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameNotIn(List<String> values) {
            addCriterion("belong_city_name not in", values, "belongCityName");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameBetween(String value1, String value2) {
            addCriterion("belong_city_name between", value1, value2, "belongCityName");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameNotBetween(String value1, String value2) {
            addCriterion("belong_city_name not between", value1, value2, "belongCityName");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Integer value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Integer value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Integer value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Integer value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Integer value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Integer> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Integer> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Integer value1, Integer value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Integer value1, Integer value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andStructIdIsNull() {
            addCriterion("struct_id is null");
            return (Criteria) this;
        }

        public Criteria andStructIdIsNotNull() {
            addCriterion("struct_id is not null");
            return (Criteria) this;
        }

        public Criteria andStructIdEqualTo(Integer value) {
            addCriterion("struct_id =", value, "structId");
            return (Criteria) this;
        }

        public Criteria andStructIdNotEqualTo(Integer value) {
            addCriterion("struct_id <>", value, "structId");
            return (Criteria) this;
        }

        public Criteria andStructIdGreaterThan(Integer value) {
            addCriterion("struct_id >", value, "structId");
            return (Criteria) this;
        }

        public Criteria andStructIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("struct_id >=", value, "structId");
            return (Criteria) this;
        }

        public Criteria andStructIdLessThan(Integer value) {
            addCriterion("struct_id <", value, "structId");
            return (Criteria) this;
        }

        public Criteria andStructIdLessThanOrEqualTo(Integer value) {
            addCriterion("struct_id <=", value, "structId");
            return (Criteria) this;
        }

        public Criteria andStructIdIn(List<Integer> values) {
            addCriterion("struct_id in", values, "structId");
            return (Criteria) this;
        }

        public Criteria andStructIdNotIn(List<Integer> values) {
            addCriterion("struct_id not in", values, "structId");
            return (Criteria) this;
        }

        public Criteria andStructIdBetween(Integer value1, Integer value2) {
            addCriterion("struct_id between", value1, value2, "structId");
            return (Criteria) this;
        }

        public Criteria andStructIdNotBetween(Integer value1, Integer value2) {
            addCriterion("struct_id not between", value1, value2, "structId");
            return (Criteria) this;
        }

        public Criteria andStructNameIsNull() {
            addCriterion("struct_name is null");
            return (Criteria) this;
        }

        public Criteria andStructNameIsNotNull() {
            addCriterion("struct_name is not null");
            return (Criteria) this;
        }

        public Criteria andStructNameEqualTo(String value) {
            addCriterion("struct_name =", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameNotEqualTo(String value) {
            addCriterion("struct_name <>", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameGreaterThan(String value) {
            addCriterion("struct_name >", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameGreaterThanOrEqualTo(String value) {
            addCriterion("struct_name >=", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameLessThan(String value) {
            addCriterion("struct_name <", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameLessThanOrEqualTo(String value) {
            addCriterion("struct_name <=", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameLike(String value) {
            addCriterion("struct_name like", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameNotLike(String value) {
            addCriterion("struct_name not like", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameIn(List<String> values) {
            addCriterion("struct_name in", values, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameNotIn(List<String> values) {
            addCriterion("struct_name not in", values, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameBetween(String value1, String value2) {
            addCriterion("struct_name between", value1, value2, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameNotBetween(String value1, String value2) {
            addCriterion("struct_name not between", value1, value2, "structName");
            return (Criteria) this;
        }

        public Criteria andSelfOwnedIsNull() {
            addCriterion("self_owned is null");
            return (Criteria) this;
        }

        public Criteria andSelfOwnedIsNotNull() {
            addCriterion("self_owned is not null");
            return (Criteria) this;
        }

        public Criteria andSelfOwnedEqualTo(Byte value) {
            addCriterion("self_owned =", value, "selfOwned");
            return (Criteria) this;
        }

        public Criteria andSelfOwnedNotEqualTo(Byte value) {
            addCriterion("self_owned <>", value, "selfOwned");
            return (Criteria) this;
        }

        public Criteria andSelfOwnedGreaterThan(Byte value) {
            addCriterion("self_owned >", value, "selfOwned");
            return (Criteria) this;
        }

        public Criteria andSelfOwnedGreaterThanOrEqualTo(Byte value) {
            addCriterion("self_owned >=", value, "selfOwned");
            return (Criteria) this;
        }

        public Criteria andSelfOwnedLessThan(Byte value) {
            addCriterion("self_owned <", value, "selfOwned");
            return (Criteria) this;
        }

        public Criteria andSelfOwnedLessThanOrEqualTo(Byte value) {
            addCriterion("self_owned <=", value, "selfOwned");
            return (Criteria) this;
        }

        public Criteria andSelfOwnedIn(List<Byte> values) {
            addCriterion("self_owned in", values, "selfOwned");
            return (Criteria) this;
        }

        public Criteria andSelfOwnedNotIn(List<Byte> values) {
            addCriterion("self_owned not in", values, "selfOwned");
            return (Criteria) this;
        }

        public Criteria andSelfOwnedBetween(Byte value1, Byte value2) {
            addCriterion("self_owned between", value1, value2, "selfOwned");
            return (Criteria) this;
        }

        public Criteria andSelfOwnedNotBetween(Byte value1, Byte value2) {
            addCriterion("self_owned not between", value1, value2, "selfOwned");
            return (Criteria) this;
        }

        public Criteria andWarnTypeIsNull() {
            addCriterion("warn_type is null");
            return (Criteria) this;
        }

        public Criteria andWarnTypeIsNotNull() {
            addCriterion("warn_type is not null");
            return (Criteria) this;
        }

        public Criteria andWarnTypeEqualTo(String value) {
            addCriterion("warn_type =", value, "warnType");
            return (Criteria) this;
        }

        public Criteria andWarnTypeNotEqualTo(String value) {
            addCriterion("warn_type <>", value, "warnType");
            return (Criteria) this;
        }

        public Criteria andWarnTypeGreaterThan(String value) {
            addCriterion("warn_type >", value, "warnType");
            return (Criteria) this;
        }

        public Criteria andWarnTypeGreaterThanOrEqualTo(String value) {
            addCriterion("warn_type >=", value, "warnType");
            return (Criteria) this;
        }

        public Criteria andWarnTypeLessThan(String value) {
            addCriterion("warn_type <", value, "warnType");
            return (Criteria) this;
        }

        public Criteria andWarnTypeLessThanOrEqualTo(String value) {
            addCriterion("warn_type <=", value, "warnType");
            return (Criteria) this;
        }

        public Criteria andWarnTypeLike(String value) {
            addCriterion("warn_type like", value, "warnType");
            return (Criteria) this;
        }

        public Criteria andWarnTypeNotLike(String value) {
            addCriterion("warn_type not like", value, "warnType");
            return (Criteria) this;
        }

        public Criteria andWarnTypeIn(List<String> values) {
            addCriterion("warn_type in", values, "warnType");
            return (Criteria) this;
        }

        public Criteria andWarnTypeNotIn(List<String> values) {
            addCriterion("warn_type not in", values, "warnType");
            return (Criteria) this;
        }

        public Criteria andWarnTypeBetween(String value1, String value2) {
            addCriterion("warn_type between", value1, value2, "warnType");
            return (Criteria) this;
        }

        public Criteria andWarnTypeNotBetween(String value1, String value2) {
            addCriterion("warn_type not between", value1, value2, "warnType");
            return (Criteria) this;
        }

        public Criteria andIsMrcarIsNull() {
            addCriterion("is_mrcar is null");
            return (Criteria) this;
        }

        public Criteria andIsMrcarIsNotNull() {
            addCriterion("is_mrcar is not null");
            return (Criteria) this;
        }

        public Criteria andIsMrcarEqualTo(Byte value) {
            addCriterion("is_mrcar =", value, "isMrcar");
            return (Criteria) this;
        }

        public Criteria andIsMrcarNotEqualTo(Byte value) {
            addCriterion("is_mrcar <>", value, "isMrcar");
            return (Criteria) this;
        }

        public Criteria andIsMrcarGreaterThan(Byte value) {
            addCriterion("is_mrcar >", value, "isMrcar");
            return (Criteria) this;
        }

        public Criteria andIsMrcarGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_mrcar >=", value, "isMrcar");
            return (Criteria) this;
        }

        public Criteria andIsMrcarLessThan(Byte value) {
            addCriterion("is_mrcar <", value, "isMrcar");
            return (Criteria) this;
        }

        public Criteria andIsMrcarLessThanOrEqualTo(Byte value) {
            addCriterion("is_mrcar <=", value, "isMrcar");
            return (Criteria) this;
        }

        public Criteria andIsMrcarIn(List<Byte> values) {
            addCriterion("is_mrcar in", values, "isMrcar");
            return (Criteria) this;
        }

        public Criteria andIsMrcarNotIn(List<Byte> values) {
            addCriterion("is_mrcar not in", values, "isMrcar");
            return (Criteria) this;
        }

        public Criteria andIsMrcarBetween(Byte value1, Byte value2) {
            addCriterion("is_mrcar between", value1, value2, "isMrcar");
            return (Criteria) this;
        }

        public Criteria andIsMrcarNotBetween(Byte value1, Byte value2) {
            addCriterion("is_mrcar not between", value1, value2, "isMrcar");
            return (Criteria) this;
        }

        public Criteria andWorkingStatusIsNull() {
            addCriterion("working_status is null");
            return (Criteria) this;
        }

        public Criteria andWorkingStatusIsNotNull() {
            addCriterion("working_status is not null");
            return (Criteria) this;
        }

        public Criteria andWorkingStatusEqualTo(Byte value) {
            addCriterion("working_status =", value, "workingStatus");
            return (Criteria) this;
        }

        public Criteria andWorkingStatusNotEqualTo(Byte value) {
            addCriterion("working_status <>", value, "workingStatus");
            return (Criteria) this;
        }

        public Criteria andWorkingStatusGreaterThan(Byte value) {
            addCriterion("working_status >", value, "workingStatus");
            return (Criteria) this;
        }

        public Criteria andWorkingStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("working_status >=", value, "workingStatus");
            return (Criteria) this;
        }

        public Criteria andWorkingStatusLessThan(Byte value) {
            addCriterion("working_status <", value, "workingStatus");
            return (Criteria) this;
        }

        public Criteria andWorkingStatusLessThanOrEqualTo(Byte value) {
            addCriterion("working_status <=", value, "workingStatus");
            return (Criteria) this;
        }

        public Criteria andWorkingStatusIn(List<Byte> values) {
            addCriterion("working_status in", values, "workingStatus");
            return (Criteria) this;
        }

        public Criteria andWorkingStatusNotIn(List<Byte> values) {
            addCriterion("working_status not in", values, "workingStatus");
            return (Criteria) this;
        }

        public Criteria andWorkingStatusBetween(Byte value1, Byte value2) {
            addCriterion("working_status between", value1, value2, "workingStatus");
            return (Criteria) this;
        }

        public Criteria andWorkingStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("working_status not between", value1, value2, "workingStatus");
            return (Criteria) this;
        }

        public Criteria andOperationStatusIsNull() {
            addCriterion("operation_status is null");
            return (Criteria) this;
        }

        public Criteria andOperationStatusIsNotNull() {
            addCriterion("operation_status is not null");
            return (Criteria) this;
        }

        public Criteria andOperationStatusEqualTo(Byte value) {
            addCriterion("operation_status =", value, "operationStatus");
            return (Criteria) this;
        }

        public Criteria andOperationStatusNotEqualTo(Byte value) {
            addCriterion("operation_status <>", value, "operationStatus");
            return (Criteria) this;
        }

        public Criteria andOperationStatusGreaterThan(Byte value) {
            addCriterion("operation_status >", value, "operationStatus");
            return (Criteria) this;
        }

        public Criteria andOperationStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("operation_status >=", value, "operationStatus");
            return (Criteria) this;
        }

        public Criteria andOperationStatusLessThan(Byte value) {
            addCriterion("operation_status <", value, "operationStatus");
            return (Criteria) this;
        }

        public Criteria andOperationStatusLessThanOrEqualTo(Byte value) {
            addCriterion("operation_status <=", value, "operationStatus");
            return (Criteria) this;
        }

        public Criteria andOperationStatusIn(List<Byte> values) {
            addCriterion("operation_status in", values, "operationStatus");
            return (Criteria) this;
        }

        public Criteria andOperationStatusNotIn(List<Byte> values) {
            addCriterion("operation_status not in", values, "operationStatus");
            return (Criteria) this;
        }

        public Criteria andOperationStatusBetween(Byte value1, Byte value2) {
            addCriterion("operation_status between", value1, value2, "operationStatus");
            return (Criteria) this;
        }

        public Criteria andOperationStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("operation_status not between", value1, value2, "operationStatus");
            return (Criteria) this;
        }

        public Criteria andLastInspectDateIsNull() {
            addCriterion("last_inspect_date is null");
            return (Criteria) this;
        }

        public Criteria andLastInspectDateIsNotNull() {
            addCriterion("last_inspect_date is not null");
            return (Criteria) this;
        }

        public Criteria andLastInspectDateEqualTo(String value) {
            addCriterion("last_inspect_date =", value, "lastInspectDate");
            return (Criteria) this;
        }

        public Criteria andLastInspectDateNotEqualTo(String value) {
            addCriterion("last_inspect_date <>", value, "lastInspectDate");
            return (Criteria) this;
        }

        public Criteria andLastInspectDateGreaterThan(String value) {
            addCriterion("last_inspect_date >", value, "lastInspectDate");
            return (Criteria) this;
        }

        public Criteria andLastInspectDateGreaterThanOrEqualTo(String value) {
            addCriterion("last_inspect_date >=", value, "lastInspectDate");
            return (Criteria) this;
        }

        public Criteria andLastInspectDateLessThan(String value) {
            addCriterion("last_inspect_date <", value, "lastInspectDate");
            return (Criteria) this;
        }

        public Criteria andLastInspectDateLessThanOrEqualTo(String value) {
            addCriterion("last_inspect_date <=", value, "lastInspectDate");
            return (Criteria) this;
        }

        public Criteria andLastInspectDateLike(String value) {
            addCriterion("last_inspect_date like", value, "lastInspectDate");
            return (Criteria) this;
        }

        public Criteria andLastInspectDateNotLike(String value) {
            addCriterion("last_inspect_date not like", value, "lastInspectDate");
            return (Criteria) this;
        }

        public Criteria andLastInspectDateIn(List<String> values) {
            addCriterion("last_inspect_date in", values, "lastInspectDate");
            return (Criteria) this;
        }

        public Criteria andLastInspectDateNotIn(List<String> values) {
            addCriterion("last_inspect_date not in", values, "lastInspectDate");
            return (Criteria) this;
        }

        public Criteria andLastInspectDateBetween(String value1, String value2) {
            addCriterion("last_inspect_date between", value1, value2, "lastInspectDate");
            return (Criteria) this;
        }

        public Criteria andLastInspectDateNotBetween(String value1, String value2) {
            addCriterion("last_inspect_date not between", value1, value2, "lastInspectDate");
            return (Criteria) this;
        }

        public Criteria andVehicleStatusIsNull() {
            addCriterion("vehicle_status is null");
            return (Criteria) this;
        }

        public Criteria andVehicleStatusIsNotNull() {
            addCriterion("vehicle_status is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleStatusEqualTo(Byte value) {
            addCriterion("vehicle_status =", value, "vehicleStatus");
            return (Criteria) this;
        }

        public Criteria andVehicleStatusNotEqualTo(Byte value) {
            addCriterion("vehicle_status <>", value, "vehicleStatus");
            return (Criteria) this;
        }

        public Criteria andVehicleStatusGreaterThan(Byte value) {
            addCriterion("vehicle_status >", value, "vehicleStatus");
            return (Criteria) this;
        }

        public Criteria andVehicleStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("vehicle_status >=", value, "vehicleStatus");
            return (Criteria) this;
        }

        public Criteria andVehicleStatusLessThan(Byte value) {
            addCriterion("vehicle_status <", value, "vehicleStatus");
            return (Criteria) this;
        }

        public Criteria andVehicleStatusLessThanOrEqualTo(Byte value) {
            addCriterion("vehicle_status <=", value, "vehicleStatus");
            return (Criteria) this;
        }

        public Criteria andVehicleStatusIn(List<Byte> values) {
            addCriterion("vehicle_status in", values, "vehicleStatus");
            return (Criteria) this;
        }

        public Criteria andVehicleStatusNotIn(List<Byte> values) {
            addCriterion("vehicle_status not in", values, "vehicleStatus");
            return (Criteria) this;
        }

        public Criteria andVehicleStatusBetween(Byte value1, Byte value2) {
            addCriterion("vehicle_status between", value1, value2, "vehicleStatus");
            return (Criteria) this;
        }

        public Criteria andVehicleStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("vehicle_status not between", value1, value2, "vehicleStatus");
            return (Criteria) this;
        }

        public Criteria andOwnCompanyIdIsNull() {
            addCriterion("own_company_id is null");
            return (Criteria) this;
        }

        public Criteria andOwnCompanyIdIsNotNull() {
            addCriterion("own_company_id is not null");
            return (Criteria) this;
        }

        public Criteria andOwnCompanyIdEqualTo(Integer value) {
            addCriterion("own_company_id =", value, "ownCompanyId");
            return (Criteria) this;
        }

        public Criteria andOwnCompanyIdNotEqualTo(Integer value) {
            addCriterion("own_company_id <>", value, "ownCompanyId");
            return (Criteria) this;
        }

        public Criteria andOwnCompanyIdGreaterThan(Integer value) {
            addCriterion("own_company_id >", value, "ownCompanyId");
            return (Criteria) this;
        }

        public Criteria andOwnCompanyIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("own_company_id >=", value, "ownCompanyId");
            return (Criteria) this;
        }

        public Criteria andOwnCompanyIdLessThan(Integer value) {
            addCriterion("own_company_id <", value, "ownCompanyId");
            return (Criteria) this;
        }

        public Criteria andOwnCompanyIdLessThanOrEqualTo(Integer value) {
            addCriterion("own_company_id <=", value, "ownCompanyId");
            return (Criteria) this;
        }

        public Criteria andOwnCompanyIdIn(List<Integer> values) {
            addCriterion("own_company_id in", values, "ownCompanyId");
            return (Criteria) this;
        }

        public Criteria andOwnCompanyIdNotIn(List<Integer> values) {
            addCriterion("own_company_id not in", values, "ownCompanyId");
            return (Criteria) this;
        }

        public Criteria andOwnCompanyIdBetween(Integer value1, Integer value2) {
            addCriterion("own_company_id between", value1, value2, "ownCompanyId");
            return (Criteria) this;
        }

        public Criteria andOwnCompanyIdNotBetween(Integer value1, Integer value2) {
            addCriterion("own_company_id not between", value1, value2, "ownCompanyId");
            return (Criteria) this;
        }

        public Criteria andOwnStructIdIsNull() {
            addCriterion("own_struct_id is null");
            return (Criteria) this;
        }

        public Criteria andOwnStructIdIsNotNull() {
            addCriterion("own_struct_id is not null");
            return (Criteria) this;
        }

        public Criteria andOwnStructIdEqualTo(Integer value) {
            addCriterion("own_struct_id =", value, "ownStructId");
            return (Criteria) this;
        }

        public Criteria andOwnStructIdNotEqualTo(Integer value) {
            addCriterion("own_struct_id <>", value, "ownStructId");
            return (Criteria) this;
        }

        public Criteria andOwnStructIdGreaterThan(Integer value) {
            addCriterion("own_struct_id >", value, "ownStructId");
            return (Criteria) this;
        }

        public Criteria andOwnStructIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("own_struct_id >=", value, "ownStructId");
            return (Criteria) this;
        }

        public Criteria andOwnStructIdLessThan(Integer value) {
            addCriterion("own_struct_id <", value, "ownStructId");
            return (Criteria) this;
        }

        public Criteria andOwnStructIdLessThanOrEqualTo(Integer value) {
            addCriterion("own_struct_id <=", value, "ownStructId");
            return (Criteria) this;
        }

        public Criteria andOwnStructIdIn(List<Integer> values) {
            addCriterion("own_struct_id in", values, "ownStructId");
            return (Criteria) this;
        }

        public Criteria andOwnStructIdNotIn(List<Integer> values) {
            addCriterion("own_struct_id not in", values, "ownStructId");
            return (Criteria) this;
        }

        public Criteria andOwnStructIdBetween(Integer value1, Integer value2) {
            addCriterion("own_struct_id between", value1, value2, "ownStructId");
            return (Criteria) this;
        }

        public Criteria andOwnStructIdNotBetween(Integer value1, Integer value2) {
            addCriterion("own_struct_id not between", value1, value2, "ownStructId");
            return (Criteria) this;
        }

        public Criteria andOwnStructNameIsNull() {
            addCriterion("own_struct_name is null");
            return (Criteria) this;
        }

        public Criteria andOwnStructNameIsNotNull() {
            addCriterion("own_struct_name is not null");
            return (Criteria) this;
        }

        public Criteria andOwnStructNameEqualTo(String value) {
            addCriterion("own_struct_name =", value, "ownStructName");
            return (Criteria) this;
        }

        public Criteria andOwnStructNameNotEqualTo(String value) {
            addCriterion("own_struct_name <>", value, "ownStructName");
            return (Criteria) this;
        }

        public Criteria andOwnStructNameGreaterThan(String value) {
            addCriterion("own_struct_name >", value, "ownStructName");
            return (Criteria) this;
        }

        public Criteria andOwnStructNameGreaterThanOrEqualTo(String value) {
            addCriterion("own_struct_name >=", value, "ownStructName");
            return (Criteria) this;
        }

        public Criteria andOwnStructNameLessThan(String value) {
            addCriterion("own_struct_name <", value, "ownStructName");
            return (Criteria) this;
        }

        public Criteria andOwnStructNameLessThanOrEqualTo(String value) {
            addCriterion("own_struct_name <=", value, "ownStructName");
            return (Criteria) this;
        }

        public Criteria andOwnStructNameLike(String value) {
            addCriterion("own_struct_name like", value, "ownStructName");
            return (Criteria) this;
        }

        public Criteria andOwnStructNameNotLike(String value) {
            addCriterion("own_struct_name not like", value, "ownStructName");
            return (Criteria) this;
        }

        public Criteria andOwnStructNameIn(List<String> values) {
            addCriterion("own_struct_name in", values, "ownStructName");
            return (Criteria) this;
        }

        public Criteria andOwnStructNameNotIn(List<String> values) {
            addCriterion("own_struct_name not in", values, "ownStructName");
            return (Criteria) this;
        }

        public Criteria andOwnStructNameBetween(String value1, String value2) {
            addCriterion("own_struct_name between", value1, value2, "ownStructName");
            return (Criteria) this;
        }

        public Criteria andOwnStructNameNotBetween(String value1, String value2) {
            addCriterion("own_struct_name not between", value1, value2, "ownStructName");
            return (Criteria) this;
        }

        public Criteria andOwnBrandIdIsNull() {
            addCriterion("own_brand_id is null");
            return (Criteria) this;
        }

        public Criteria andOwnBrandIdIsNotNull() {
            addCriterion("own_brand_id is not null");
            return (Criteria) this;
        }

        public Criteria andOwnBrandIdEqualTo(Integer value) {
            addCriterion("own_brand_id =", value, "ownBrandId");
            return (Criteria) this;
        }

        public Criteria andOwnBrandIdNotEqualTo(Integer value) {
            addCriterion("own_brand_id <>", value, "ownBrandId");
            return (Criteria) this;
        }

        public Criteria andOwnBrandIdGreaterThan(Integer value) {
            addCriterion("own_brand_id >", value, "ownBrandId");
            return (Criteria) this;
        }

        public Criteria andOwnBrandIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("own_brand_id >=", value, "ownBrandId");
            return (Criteria) this;
        }

        public Criteria andOwnBrandIdLessThan(Integer value) {
            addCriterion("own_brand_id <", value, "ownBrandId");
            return (Criteria) this;
        }

        public Criteria andOwnBrandIdLessThanOrEqualTo(Integer value) {
            addCriterion("own_brand_id <=", value, "ownBrandId");
            return (Criteria) this;
        }

        public Criteria andOwnBrandIdIn(List<Integer> values) {
            addCriterion("own_brand_id in", values, "ownBrandId");
            return (Criteria) this;
        }

        public Criteria andOwnBrandIdNotIn(List<Integer> values) {
            addCriterion("own_brand_id not in", values, "ownBrandId");
            return (Criteria) this;
        }

        public Criteria andOwnBrandIdBetween(Integer value1, Integer value2) {
            addCriterion("own_brand_id between", value1, value2, "ownBrandId");
            return (Criteria) this;
        }

        public Criteria andOwnBrandIdNotBetween(Integer value1, Integer value2) {
            addCriterion("own_brand_id not between", value1, value2, "ownBrandId");
            return (Criteria) this;
        }

        public Criteria andOwnBrandCodeIsNull() {
            addCriterion("own_brand_code is null");
            return (Criteria) this;
        }

        public Criteria andOwnBrandCodeIsNotNull() {
            addCriterion("own_brand_code is not null");
            return (Criteria) this;
        }

        public Criteria andOwnBrandCodeEqualTo(String value) {
            addCriterion("own_brand_code =", value, "ownBrandCode");
            return (Criteria) this;
        }

        public Criteria andOwnBrandCodeNotEqualTo(String value) {
            addCriterion("own_brand_code <>", value, "ownBrandCode");
            return (Criteria) this;
        }

        public Criteria andOwnBrandCodeGreaterThan(String value) {
            addCriterion("own_brand_code >", value, "ownBrandCode");
            return (Criteria) this;
        }

        public Criteria andOwnBrandCodeGreaterThanOrEqualTo(String value) {
            addCriterion("own_brand_code >=", value, "ownBrandCode");
            return (Criteria) this;
        }

        public Criteria andOwnBrandCodeLessThan(String value) {
            addCriterion("own_brand_code <", value, "ownBrandCode");
            return (Criteria) this;
        }

        public Criteria andOwnBrandCodeLessThanOrEqualTo(String value) {
            addCriterion("own_brand_code <=", value, "ownBrandCode");
            return (Criteria) this;
        }

        public Criteria andOwnBrandCodeLike(String value) {
            addCriterion("own_brand_code like", value, "ownBrandCode");
            return (Criteria) this;
        }

        public Criteria andOwnBrandCodeNotLike(String value) {
            addCriterion("own_brand_code not like", value, "ownBrandCode");
            return (Criteria) this;
        }

        public Criteria andOwnBrandCodeIn(List<String> values) {
            addCriterion("own_brand_code in", values, "ownBrandCode");
            return (Criteria) this;
        }

        public Criteria andOwnBrandCodeNotIn(List<String> values) {
            addCriterion("own_brand_code not in", values, "ownBrandCode");
            return (Criteria) this;
        }

        public Criteria andOwnBrandCodeBetween(String value1, String value2) {
            addCriterion("own_brand_code between", value1, value2, "ownBrandCode");
            return (Criteria) this;
        }

        public Criteria andOwnBrandCodeNotBetween(String value1, String value2) {
            addCriterion("own_brand_code not between", value1, value2, "ownBrandCode");
            return (Criteria) this;
        }

        public Criteria andOwnBrandNameIsNull() {
            addCriterion("own_brand_name is null");
            return (Criteria) this;
        }

        public Criteria andOwnBrandNameIsNotNull() {
            addCriterion("own_brand_name is not null");
            return (Criteria) this;
        }

        public Criteria andOwnBrandNameEqualTo(String value) {
            addCriterion("own_brand_name =", value, "ownBrandName");
            return (Criteria) this;
        }

        public Criteria andOwnBrandNameNotEqualTo(String value) {
            addCriterion("own_brand_name <>", value, "ownBrandName");
            return (Criteria) this;
        }

        public Criteria andOwnBrandNameGreaterThan(String value) {
            addCriterion("own_brand_name >", value, "ownBrandName");
            return (Criteria) this;
        }

        public Criteria andOwnBrandNameGreaterThanOrEqualTo(String value) {
            addCriterion("own_brand_name >=", value, "ownBrandName");
            return (Criteria) this;
        }

        public Criteria andOwnBrandNameLessThan(String value) {
            addCriterion("own_brand_name <", value, "ownBrandName");
            return (Criteria) this;
        }

        public Criteria andOwnBrandNameLessThanOrEqualTo(String value) {
            addCriterion("own_brand_name <=", value, "ownBrandName");
            return (Criteria) this;
        }

        public Criteria andOwnBrandNameLike(String value) {
            addCriterion("own_brand_name like", value, "ownBrandName");
            return (Criteria) this;
        }

        public Criteria andOwnBrandNameNotLike(String value) {
            addCriterion("own_brand_name not like", value, "ownBrandName");
            return (Criteria) this;
        }

        public Criteria andOwnBrandNameIn(List<String> values) {
            addCriterion("own_brand_name in", values, "ownBrandName");
            return (Criteria) this;
        }

        public Criteria andOwnBrandNameNotIn(List<String> values) {
            addCriterion("own_brand_name not in", values, "ownBrandName");
            return (Criteria) this;
        }

        public Criteria andOwnBrandNameBetween(String value1, String value2) {
            addCriterion("own_brand_name between", value1, value2, "ownBrandName");
            return (Criteria) this;
        }

        public Criteria andOwnBrandNameNotBetween(String value1, String value2) {
            addCriterion("own_brand_name not between", value1, value2, "ownBrandName");
            return (Criteria) this;
        }

        public Criteria andOwnModelIdIsNull() {
            addCriterion("own_model_id is null");
            return (Criteria) this;
        }

        public Criteria andOwnModelIdIsNotNull() {
            addCriterion("own_model_id is not null");
            return (Criteria) this;
        }

        public Criteria andOwnModelIdEqualTo(Integer value) {
            addCriterion("own_model_id =", value, "ownModelId");
            return (Criteria) this;
        }

        public Criteria andOwnModelIdNotEqualTo(Integer value) {
            addCriterion("own_model_id <>", value, "ownModelId");
            return (Criteria) this;
        }

        public Criteria andOwnModelIdGreaterThan(Integer value) {
            addCriterion("own_model_id >", value, "ownModelId");
            return (Criteria) this;
        }

        public Criteria andOwnModelIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("own_model_id >=", value, "ownModelId");
            return (Criteria) this;
        }

        public Criteria andOwnModelIdLessThan(Integer value) {
            addCriterion("own_model_id <", value, "ownModelId");
            return (Criteria) this;
        }

        public Criteria andOwnModelIdLessThanOrEqualTo(Integer value) {
            addCriterion("own_model_id <=", value, "ownModelId");
            return (Criteria) this;
        }

        public Criteria andOwnModelIdIn(List<Integer> values) {
            addCriterion("own_model_id in", values, "ownModelId");
            return (Criteria) this;
        }

        public Criteria andOwnModelIdNotIn(List<Integer> values) {
            addCriterion("own_model_id not in", values, "ownModelId");
            return (Criteria) this;
        }

        public Criteria andOwnModelIdBetween(Integer value1, Integer value2) {
            addCriterion("own_model_id between", value1, value2, "ownModelId");
            return (Criteria) this;
        }

        public Criteria andOwnModelIdNotBetween(Integer value1, Integer value2) {
            addCriterion("own_model_id not between", value1, value2, "ownModelId");
            return (Criteria) this;
        }

        public Criteria andOwnModelCodeIsNull() {
            addCriterion("own_model_code is null");
            return (Criteria) this;
        }

        public Criteria andOwnModelCodeIsNotNull() {
            addCriterion("own_model_code is not null");
            return (Criteria) this;
        }

        public Criteria andOwnModelCodeEqualTo(String value) {
            addCriterion("own_model_code =", value, "ownModelCode");
            return (Criteria) this;
        }

        public Criteria andOwnModelCodeNotEqualTo(String value) {
            addCriterion("own_model_code <>", value, "ownModelCode");
            return (Criteria) this;
        }

        public Criteria andOwnModelCodeGreaterThan(String value) {
            addCriterion("own_model_code >", value, "ownModelCode");
            return (Criteria) this;
        }

        public Criteria andOwnModelCodeGreaterThanOrEqualTo(String value) {
            addCriterion("own_model_code >=", value, "ownModelCode");
            return (Criteria) this;
        }

        public Criteria andOwnModelCodeLessThan(String value) {
            addCriterion("own_model_code <", value, "ownModelCode");
            return (Criteria) this;
        }

        public Criteria andOwnModelCodeLessThanOrEqualTo(String value) {
            addCriterion("own_model_code <=", value, "ownModelCode");
            return (Criteria) this;
        }

        public Criteria andOwnModelCodeLike(String value) {
            addCriterion("own_model_code like", value, "ownModelCode");
            return (Criteria) this;
        }

        public Criteria andOwnModelCodeNotLike(String value) {
            addCriterion("own_model_code not like", value, "ownModelCode");
            return (Criteria) this;
        }

        public Criteria andOwnModelCodeIn(List<String> values) {
            addCriterion("own_model_code in", values, "ownModelCode");
            return (Criteria) this;
        }

        public Criteria andOwnModelCodeNotIn(List<String> values) {
            addCriterion("own_model_code not in", values, "ownModelCode");
            return (Criteria) this;
        }

        public Criteria andOwnModelCodeBetween(String value1, String value2) {
            addCriterion("own_model_code between", value1, value2, "ownModelCode");
            return (Criteria) this;
        }

        public Criteria andOwnModelCodeNotBetween(String value1, String value2) {
            addCriterion("own_model_code not between", value1, value2, "ownModelCode");
            return (Criteria) this;
        }

        public Criteria andOwnModelNameIsNull() {
            addCriterion("own_model_name is null");
            return (Criteria) this;
        }

        public Criteria andOwnModelNameIsNotNull() {
            addCriterion("own_model_name is not null");
            return (Criteria) this;
        }

        public Criteria andOwnModelNameEqualTo(String value) {
            addCriterion("own_model_name =", value, "ownModelName");
            return (Criteria) this;
        }

        public Criteria andOwnModelNameNotEqualTo(String value) {
            addCriterion("own_model_name <>", value, "ownModelName");
            return (Criteria) this;
        }

        public Criteria andOwnModelNameGreaterThan(String value) {
            addCriterion("own_model_name >", value, "ownModelName");
            return (Criteria) this;
        }

        public Criteria andOwnModelNameGreaterThanOrEqualTo(String value) {
            addCriterion("own_model_name >=", value, "ownModelName");
            return (Criteria) this;
        }

        public Criteria andOwnModelNameLessThan(String value) {
            addCriterion("own_model_name <", value, "ownModelName");
            return (Criteria) this;
        }

        public Criteria andOwnModelNameLessThanOrEqualTo(String value) {
            addCriterion("own_model_name <=", value, "ownModelName");
            return (Criteria) this;
        }

        public Criteria andOwnModelNameLike(String value) {
            addCriterion("own_model_name like", value, "ownModelName");
            return (Criteria) this;
        }

        public Criteria andOwnModelNameNotLike(String value) {
            addCriterion("own_model_name not like", value, "ownModelName");
            return (Criteria) this;
        }

        public Criteria andOwnModelNameIn(List<String> values) {
            addCriterion("own_model_name in", values, "ownModelName");
            return (Criteria) this;
        }

        public Criteria andOwnModelNameNotIn(List<String> values) {
            addCriterion("own_model_name not in", values, "ownModelName");
            return (Criteria) this;
        }

        public Criteria andOwnModelNameBetween(String value1, String value2) {
            addCriterion("own_model_name between", value1, value2, "ownModelName");
            return (Criteria) this;
        }

        public Criteria andOwnModelNameNotBetween(String value1, String value2) {
            addCriterion("own_model_name not between", value1, value2, "ownModelName");
            return (Criteria) this;
        }

        public Criteria andVehicleAgeIsNull() {
            addCriterion("vehicle_age is null");
            return (Criteria) this;
        }

        public Criteria andVehicleAgeIsNotNull() {
            addCriterion("vehicle_age is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleAgeEqualTo(Short value) {
            addCriterion("vehicle_age =", value, "vehicleAge");
            return (Criteria) this;
        }

        public Criteria andVehicleAgeNotEqualTo(Short value) {
            addCriterion("vehicle_age <>", value, "vehicleAge");
            return (Criteria) this;
        }

        public Criteria andVehicleAgeGreaterThan(Short value) {
            addCriterion("vehicle_age >", value, "vehicleAge");
            return (Criteria) this;
        }

        public Criteria andVehicleAgeGreaterThanOrEqualTo(Short value) {
            addCriterion("vehicle_age >=", value, "vehicleAge");
            return (Criteria) this;
        }

        public Criteria andVehicleAgeLessThan(Short value) {
            addCriterion("vehicle_age <", value, "vehicleAge");
            return (Criteria) this;
        }

        public Criteria andVehicleAgeLessThanOrEqualTo(Short value) {
            addCriterion("vehicle_age <=", value, "vehicleAge");
            return (Criteria) this;
        }

        public Criteria andVehicleAgeIn(List<Short> values) {
            addCriterion("vehicle_age in", values, "vehicleAge");
            return (Criteria) this;
        }

        public Criteria andVehicleAgeNotIn(List<Short> values) {
            addCriterion("vehicle_age not in", values, "vehicleAge");
            return (Criteria) this;
        }

        public Criteria andVehicleAgeBetween(Short value1, Short value2) {
            addCriterion("vehicle_age between", value1, value2, "vehicleAge");
            return (Criteria) this;
        }

        public Criteria andVehicleAgeNotBetween(Short value1, Short value2) {
            addCriterion("vehicle_age not between", value1, value2, "vehicleAge");
            return (Criteria) this;
        }

        public Criteria andWheelbaseIsNull() {
            addCriterion("wheelbase is null");
            return (Criteria) this;
        }

        public Criteria andWheelbaseIsNotNull() {
            addCriterion("wheelbase is not null");
            return (Criteria) this;
        }

        public Criteria andWheelbaseEqualTo(String value) {
            addCriterion("wheelbase =", value, "wheelbase");
            return (Criteria) this;
        }

        public Criteria andWheelbaseNotEqualTo(String value) {
            addCriterion("wheelbase <>", value, "wheelbase");
            return (Criteria) this;
        }

        public Criteria andWheelbaseGreaterThan(String value) {
            addCriterion("wheelbase >", value, "wheelbase");
            return (Criteria) this;
        }

        public Criteria andWheelbaseGreaterThanOrEqualTo(String value) {
            addCriterion("wheelbase >=", value, "wheelbase");
            return (Criteria) this;
        }

        public Criteria andWheelbaseLessThan(String value) {
            addCriterion("wheelbase <", value, "wheelbase");
            return (Criteria) this;
        }

        public Criteria andWheelbaseLessThanOrEqualTo(String value) {
            addCriterion("wheelbase <=", value, "wheelbase");
            return (Criteria) this;
        }

        public Criteria andWheelbaseLike(String value) {
            addCriterion("wheelbase like", value, "wheelbase");
            return (Criteria) this;
        }

        public Criteria andWheelbaseNotLike(String value) {
            addCriterion("wheelbase not like", value, "wheelbase");
            return (Criteria) this;
        }

        public Criteria andWheelbaseIn(List<String> values) {
            addCriterion("wheelbase in", values, "wheelbase");
            return (Criteria) this;
        }

        public Criteria andWheelbaseNotIn(List<String> values) {
            addCriterion("wheelbase not in", values, "wheelbase");
            return (Criteria) this;
        }

        public Criteria andWheelbaseBetween(String value1, String value2) {
            addCriterion("wheelbase between", value1, value2, "wheelbase");
            return (Criteria) this;
        }

        public Criteria andWheelbaseNotBetween(String value1, String value2) {
            addCriterion("wheelbase not between", value1, value2, "wheelbase");
            return (Criteria) this;
        }

        public Criteria andVehicleResourceIsNull() {
            addCriterion("vehicle_resource is null");
            return (Criteria) this;
        }

        public Criteria andVehicleResourceIsNotNull() {
            addCriterion("vehicle_resource is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleResourceEqualTo(Byte value) {
            addCriterion("vehicle_resource =", value, "vehicleResource");
            return (Criteria) this;
        }

        public Criteria andVehicleResourceNotEqualTo(Byte value) {
            addCriterion("vehicle_resource <>", value, "vehicleResource");
            return (Criteria) this;
        }

        public Criteria andVehicleResourceGreaterThan(Byte value) {
            addCriterion("vehicle_resource >", value, "vehicleResource");
            return (Criteria) this;
        }

        public Criteria andVehicleResourceGreaterThanOrEqualTo(Byte value) {
            addCriterion("vehicle_resource >=", value, "vehicleResource");
            return (Criteria) this;
        }

        public Criteria andVehicleResourceLessThan(Byte value) {
            addCriterion("vehicle_resource <", value, "vehicleResource");
            return (Criteria) this;
        }

        public Criteria andVehicleResourceLessThanOrEqualTo(Byte value) {
            addCriterion("vehicle_resource <=", value, "vehicleResource");
            return (Criteria) this;
        }

        public Criteria andVehicleResourceIn(List<Byte> values) {
            addCriterion("vehicle_resource in", values, "vehicleResource");
            return (Criteria) this;
        }

        public Criteria andVehicleResourceNotIn(List<Byte> values) {
            addCriterion("vehicle_resource not in", values, "vehicleResource");
            return (Criteria) this;
        }

        public Criteria andVehicleResourceBetween(Byte value1, Byte value2) {
            addCriterion("vehicle_resource between", value1, value2, "vehicleResource");
            return (Criteria) this;
        }

        public Criteria andVehicleResourceNotBetween(Byte value1, Byte value2) {
            addCriterion("vehicle_resource not between", value1, value2, "vehicleResource");
            return (Criteria) this;
        }

        public Criteria andCreterIdIsNull() {
            addCriterion("creter_id is null");
            return (Criteria) this;
        }

        public Criteria andCreterIdIsNotNull() {
            addCriterion("creter_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreterIdEqualTo(Integer value) {
            addCriterion("creter_id =", value, "creterId");
            return (Criteria) this;
        }

        public Criteria andCreterIdNotEqualTo(Integer value) {
            addCriterion("creter_id <>", value, "creterId");
            return (Criteria) this;
        }

        public Criteria andCreterIdGreaterThan(Integer value) {
            addCriterion("creter_id >", value, "creterId");
            return (Criteria) this;
        }

        public Criteria andCreterIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("creter_id >=", value, "creterId");
            return (Criteria) this;
        }

        public Criteria andCreterIdLessThan(Integer value) {
            addCriterion("creter_id <", value, "creterId");
            return (Criteria) this;
        }

        public Criteria andCreterIdLessThanOrEqualTo(Integer value) {
            addCriterion("creter_id <=", value, "creterId");
            return (Criteria) this;
        }

        public Criteria andCreterIdIn(List<Integer> values) {
            addCriterion("creter_id in", values, "creterId");
            return (Criteria) this;
        }

        public Criteria andCreterIdNotIn(List<Integer> values) {
            addCriterion("creter_id not in", values, "creterId");
            return (Criteria) this;
        }

        public Criteria andCreterIdBetween(Integer value1, Integer value2) {
            addCriterion("creter_id between", value1, value2, "creterId");
            return (Criteria) this;
        }

        public Criteria andCreterIdNotBetween(Integer value1, Integer value2) {
            addCriterion("creter_id not between", value1, value2, "creterId");
            return (Criteria) this;
        }

        public Criteria andCreterNameIsNull() {
            addCriterion("creter_name is null");
            return (Criteria) this;
        }

        public Criteria andCreterNameIsNotNull() {
            addCriterion("creter_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreterNameEqualTo(String value) {
            addCriterion("creter_name =", value, "creterName");
            return (Criteria) this;
        }

        public Criteria andCreterNameNotEqualTo(String value) {
            addCriterion("creter_name <>", value, "creterName");
            return (Criteria) this;
        }

        public Criteria andCreterNameGreaterThan(String value) {
            addCriterion("creter_name >", value, "creterName");
            return (Criteria) this;
        }

        public Criteria andCreterNameGreaterThanOrEqualTo(String value) {
            addCriterion("creter_name >=", value, "creterName");
            return (Criteria) this;
        }

        public Criteria andCreterNameLessThan(String value) {
            addCriterion("creter_name <", value, "creterName");
            return (Criteria) this;
        }

        public Criteria andCreterNameLessThanOrEqualTo(String value) {
            addCriterion("creter_name <=", value, "creterName");
            return (Criteria) this;
        }

        public Criteria andCreterNameLike(String value) {
            addCriterion("creter_name like", value, "creterName");
            return (Criteria) this;
        }

        public Criteria andCreterNameNotLike(String value) {
            addCriterion("creter_name not like", value, "creterName");
            return (Criteria) this;
        }

        public Criteria andCreterNameIn(List<String> values) {
            addCriterion("creter_name in", values, "creterName");
            return (Criteria) this;
        }

        public Criteria andCreterNameNotIn(List<String> values) {
            addCriterion("creter_name not in", values, "creterName");
            return (Criteria) this;
        }

        public Criteria andCreterNameBetween(String value1, String value2) {
            addCriterion("creter_name between", value1, value2, "creterName");
            return (Criteria) this;
        }

        public Criteria andCreterNameNotBetween(String value1, String value2) {
            addCriterion("creter_name not between", value1, value2, "creterName");
            return (Criteria) this;
        }

        public Criteria andUpdateIdIsNull() {
            addCriterion("update_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdateIdIsNotNull() {
            addCriterion("update_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateIdEqualTo(Integer value) {
            addCriterion("update_id =", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdNotEqualTo(Integer value) {
            addCriterion("update_id <>", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdGreaterThan(Integer value) {
            addCriterion("update_id >", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("update_id >=", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdLessThan(Integer value) {
            addCriterion("update_id <", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdLessThanOrEqualTo(Integer value) {
            addCriterion("update_id <=", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdIn(List<Integer> values) {
            addCriterion("update_id in", values, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdNotIn(List<Integer> values) {
            addCriterion("update_id not in", values, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdBetween(Integer value1, Integer value2) {
            addCriterion("update_id between", value1, value2, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdNotBetween(Integer value1, Integer value2) {
            addCriterion("update_id not between", value1, value2, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateNameIsNull() {
            addCriterion("update_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdateNameIsNotNull() {
            addCriterion("update_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateNameEqualTo(String value) {
            addCriterion("update_name =", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotEqualTo(String value) {
            addCriterion("update_name <>", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameGreaterThan(String value) {
            addCriterion("update_name >", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameGreaterThanOrEqualTo(String value) {
            addCriterion("update_name >=", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameLessThan(String value) {
            addCriterion("update_name <", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameLessThanOrEqualTo(String value) {
            addCriterion("update_name <=", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameLike(String value) {
            addCriterion("update_name like", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotLike(String value) {
            addCriterion("update_name not like", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameIn(List<String> values) {
            addCriterion("update_name in", values, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotIn(List<String> values) {
            addCriterion("update_name not in", values, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameBetween(String value1, String value2) {
            addCriterion("update_name between", value1, value2, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotBetween(String value1, String value2) {
            addCriterion("update_name not between", value1, value2, "updateName");
            return (Criteria) this;
        }

        public Criteria andFuelTypeIsNull() {
            addCriterion("fuel_type is null");
            return (Criteria) this;
        }

        public Criteria andFuelTypeIsNotNull() {
            addCriterion("fuel_type is not null");
            return (Criteria) this;
        }

        public Criteria andFuelTypeEqualTo(Byte value) {
            addCriterion("fuel_type =", value, "fuelType");
            return (Criteria) this;
        }

        public Criteria andFuelTypeNotEqualTo(Byte value) {
            addCriterion("fuel_type <>", value, "fuelType");
            return (Criteria) this;
        }

        public Criteria andFuelTypeGreaterThan(Byte value) {
            addCriterion("fuel_type >", value, "fuelType");
            return (Criteria) this;
        }

        public Criteria andFuelTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("fuel_type >=", value, "fuelType");
            return (Criteria) this;
        }

        public Criteria andFuelTypeLessThan(Byte value) {
            addCriterion("fuel_type <", value, "fuelType");
            return (Criteria) this;
        }

        public Criteria andFuelTypeLessThanOrEqualTo(Byte value) {
            addCriterion("fuel_type <=", value, "fuelType");
            return (Criteria) this;
        }

        public Criteria andFuelTypeIn(List<Byte> values) {
            addCriterion("fuel_type in", values, "fuelType");
            return (Criteria) this;
        }

        public Criteria andFuelTypeNotIn(List<Byte> values) {
            addCriterion("fuel_type not in", values, "fuelType");
            return (Criteria) this;
        }

        public Criteria andFuelTypeBetween(Byte value1, Byte value2) {
            addCriterion("fuel_type between", value1, value2, "fuelType");
            return (Criteria) this;
        }

        public Criteria andFuelTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("fuel_type not between", value1, value2, "fuelType");
            return (Criteria) this;
        }

        public Criteria andFuelNoIsNull() {
            addCriterion("fuel_no is null");
            return (Criteria) this;
        }

        public Criteria andFuelNoIsNotNull() {
            addCriterion("fuel_no is not null");
            return (Criteria) this;
        }

        public Criteria andFuelNoEqualTo(String value) {
            addCriterion("fuel_no =", value, "fuelNo");
            return (Criteria) this;
        }

        public Criteria andFuelNoNotEqualTo(String value) {
            addCriterion("fuel_no <>", value, "fuelNo");
            return (Criteria) this;
        }

        public Criteria andFuelNoGreaterThan(String value) {
            addCriterion("fuel_no >", value, "fuelNo");
            return (Criteria) this;
        }

        public Criteria andFuelNoGreaterThanOrEqualTo(String value) {
            addCriterion("fuel_no >=", value, "fuelNo");
            return (Criteria) this;
        }

        public Criteria andFuelNoLessThan(String value) {
            addCriterion("fuel_no <", value, "fuelNo");
            return (Criteria) this;
        }

        public Criteria andFuelNoLessThanOrEqualTo(String value) {
            addCriterion("fuel_no <=", value, "fuelNo");
            return (Criteria) this;
        }

        public Criteria andFuelNoLike(String value) {
            addCriterion("fuel_no like", value, "fuelNo");
            return (Criteria) this;
        }

        public Criteria andFuelNoNotLike(String value) {
            addCriterion("fuel_no not like", value, "fuelNo");
            return (Criteria) this;
        }

        public Criteria andFuelNoIn(List<String> values) {
            addCriterion("fuel_no in", values, "fuelNo");
            return (Criteria) this;
        }

        public Criteria andFuelNoNotIn(List<String> values) {
            addCriterion("fuel_no not in", values, "fuelNo");
            return (Criteria) this;
        }

        public Criteria andFuelNoBetween(String value1, String value2) {
            addCriterion("fuel_no between", value1, value2, "fuelNo");
            return (Criteria) this;
        }

        public Criteria andFuelNoNotBetween(String value1, String value2) {
            addCriterion("fuel_no not between", value1, value2, "fuelNo");
            return (Criteria) this;
        }

        public Criteria andVehicleOutputVolumeIsNull() {
            addCriterion("vehicle_output_volume is null");
            return (Criteria) this;
        }

        public Criteria andVehicleOutputVolumeIsNotNull() {
            addCriterion("vehicle_output_volume is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleOutputVolumeEqualTo(String value) {
            addCriterion("vehicle_output_volume =", value, "vehicleOutputVolume");
            return (Criteria) this;
        }

        public Criteria andVehicleOutputVolumeNotEqualTo(String value) {
            addCriterion("vehicle_output_volume <>", value, "vehicleOutputVolume");
            return (Criteria) this;
        }

        public Criteria andVehicleOutputVolumeGreaterThan(String value) {
            addCriterion("vehicle_output_volume >", value, "vehicleOutputVolume");
            return (Criteria) this;
        }

        public Criteria andVehicleOutputVolumeGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_output_volume >=", value, "vehicleOutputVolume");
            return (Criteria) this;
        }

        public Criteria andVehicleOutputVolumeLessThan(String value) {
            addCriterion("vehicle_output_volume <", value, "vehicleOutputVolume");
            return (Criteria) this;
        }

        public Criteria andVehicleOutputVolumeLessThanOrEqualTo(String value) {
            addCriterion("vehicle_output_volume <=", value, "vehicleOutputVolume");
            return (Criteria) this;
        }

        public Criteria andVehicleOutputVolumeLike(String value) {
            addCriterion("vehicle_output_volume like", value, "vehicleOutputVolume");
            return (Criteria) this;
        }

        public Criteria andVehicleOutputVolumeNotLike(String value) {
            addCriterion("vehicle_output_volume not like", value, "vehicleOutputVolume");
            return (Criteria) this;
        }

        public Criteria andVehicleOutputVolumeIn(List<String> values) {
            addCriterion("vehicle_output_volume in", values, "vehicleOutputVolume");
            return (Criteria) this;
        }

        public Criteria andVehicleOutputVolumeNotIn(List<String> values) {
            addCriterion("vehicle_output_volume not in", values, "vehicleOutputVolume");
            return (Criteria) this;
        }

        public Criteria andVehicleOutputVolumeBetween(String value1, String value2) {
            addCriterion("vehicle_output_volume between", value1, value2, "vehicleOutputVolume");
            return (Criteria) this;
        }

        public Criteria andVehicleOutputVolumeNotBetween(String value1, String value2) {
            addCriterion("vehicle_output_volume not between", value1, value2, "vehicleOutputVolume");
            return (Criteria) this;
        }

        public Criteria andOutputVolumeUnitIsNull() {
            addCriterion("output_volume_unit is null");
            return (Criteria) this;
        }

        public Criteria andOutputVolumeUnitIsNotNull() {
            addCriterion("output_volume_unit is not null");
            return (Criteria) this;
        }

        public Criteria andOutputVolumeUnitEqualTo(String value) {
            addCriterion("output_volume_unit =", value, "outputVolumeUnit");
            return (Criteria) this;
        }

        public Criteria andOutputVolumeUnitNotEqualTo(String value) {
            addCriterion("output_volume_unit <>", value, "outputVolumeUnit");
            return (Criteria) this;
        }

        public Criteria andOutputVolumeUnitGreaterThan(String value) {
            addCriterion("output_volume_unit >", value, "outputVolumeUnit");
            return (Criteria) this;
        }

        public Criteria andOutputVolumeUnitGreaterThanOrEqualTo(String value) {
            addCriterion("output_volume_unit >=", value, "outputVolumeUnit");
            return (Criteria) this;
        }

        public Criteria andOutputVolumeUnitLessThan(String value) {
            addCriterion("output_volume_unit <", value, "outputVolumeUnit");
            return (Criteria) this;
        }

        public Criteria andOutputVolumeUnitLessThanOrEqualTo(String value) {
            addCriterion("output_volume_unit <=", value, "outputVolumeUnit");
            return (Criteria) this;
        }

        public Criteria andOutputVolumeUnitLike(String value) {
            addCriterion("output_volume_unit like", value, "outputVolumeUnit");
            return (Criteria) this;
        }

        public Criteria andOutputVolumeUnitNotLike(String value) {
            addCriterion("output_volume_unit not like", value, "outputVolumeUnit");
            return (Criteria) this;
        }

        public Criteria andOutputVolumeUnitIn(List<String> values) {
            addCriterion("output_volume_unit in", values, "outputVolumeUnit");
            return (Criteria) this;
        }

        public Criteria andOutputVolumeUnitNotIn(List<String> values) {
            addCriterion("output_volume_unit not in", values, "outputVolumeUnit");
            return (Criteria) this;
        }

        public Criteria andOutputVolumeUnitBetween(String value1, String value2) {
            addCriterion("output_volume_unit between", value1, value2, "outputVolumeUnit");
            return (Criteria) this;
        }

        public Criteria andOutputVolumeUnitNotBetween(String value1, String value2) {
            addCriterion("output_volume_unit not between", value1, value2, "outputVolumeUnit");
            return (Criteria) this;
        }

        public Criteria andEngineRateIsNull() {
            addCriterion("engine_rate is null");
            return (Criteria) this;
        }

        public Criteria andEngineRateIsNotNull() {
            addCriterion("engine_rate is not null");
            return (Criteria) this;
        }

        public Criteria andEngineRateEqualTo(String value) {
            addCriterion("engine_rate =", value, "engineRate");
            return (Criteria) this;
        }

        public Criteria andEngineRateNotEqualTo(String value) {
            addCriterion("engine_rate <>", value, "engineRate");
            return (Criteria) this;
        }

        public Criteria andEngineRateGreaterThan(String value) {
            addCriterion("engine_rate >", value, "engineRate");
            return (Criteria) this;
        }

        public Criteria andEngineRateGreaterThanOrEqualTo(String value) {
            addCriterion("engine_rate >=", value, "engineRate");
            return (Criteria) this;
        }

        public Criteria andEngineRateLessThan(String value) {
            addCriterion("engine_rate <", value, "engineRate");
            return (Criteria) this;
        }

        public Criteria andEngineRateLessThanOrEqualTo(String value) {
            addCriterion("engine_rate <=", value, "engineRate");
            return (Criteria) this;
        }

        public Criteria andEngineRateLike(String value) {
            addCriterion("engine_rate like", value, "engineRate");
            return (Criteria) this;
        }

        public Criteria andEngineRateNotLike(String value) {
            addCriterion("engine_rate not like", value, "engineRate");
            return (Criteria) this;
        }

        public Criteria andEngineRateIn(List<String> values) {
            addCriterion("engine_rate in", values, "engineRate");
            return (Criteria) this;
        }

        public Criteria andEngineRateNotIn(List<String> values) {
            addCriterion("engine_rate not in", values, "engineRate");
            return (Criteria) this;
        }

        public Criteria andEngineRateBetween(String value1, String value2) {
            addCriterion("engine_rate between", value1, value2, "engineRate");
            return (Criteria) this;
        }

        public Criteria andEngineRateNotBetween(String value1, String value2) {
            addCriterion("engine_rate not between", value1, value2, "engineRate");
            return (Criteria) this;
        }

        public Criteria andSeatCountIsNull() {
            addCriterion("seat_count is null");
            return (Criteria) this;
        }

        public Criteria andSeatCountIsNotNull() {
            addCriterion("seat_count is not null");
            return (Criteria) this;
        }

        public Criteria andSeatCountEqualTo(Byte value) {
            addCriterion("seat_count =", value, "seatCount");
            return (Criteria) this;
        }

        public Criteria andSeatCountNotEqualTo(Byte value) {
            addCriterion("seat_count <>", value, "seatCount");
            return (Criteria) this;
        }

        public Criteria andSeatCountGreaterThan(Byte value) {
            addCriterion("seat_count >", value, "seatCount");
            return (Criteria) this;
        }

        public Criteria andSeatCountGreaterThanOrEqualTo(Byte value) {
            addCriterion("seat_count >=", value, "seatCount");
            return (Criteria) this;
        }

        public Criteria andSeatCountLessThan(Byte value) {
            addCriterion("seat_count <", value, "seatCount");
            return (Criteria) this;
        }

        public Criteria andSeatCountLessThanOrEqualTo(Byte value) {
            addCriterion("seat_count <=", value, "seatCount");
            return (Criteria) this;
        }

        public Criteria andSeatCountIn(List<Byte> values) {
            addCriterion("seat_count in", values, "seatCount");
            return (Criteria) this;
        }

        public Criteria andSeatCountNotIn(List<Byte> values) {
            addCriterion("seat_count not in", values, "seatCount");
            return (Criteria) this;
        }

        public Criteria andSeatCountBetween(Byte value1, Byte value2) {
            addCriterion("seat_count between", value1, value2, "seatCount");
            return (Criteria) this;
        }

        public Criteria andSeatCountNotBetween(Byte value1, Byte value2) {
            addCriterion("seat_count not between", value1, value2, "seatCount");
            return (Criteria) this;
        }

        public Criteria andMaintainMileageIsNull() {
            addCriterion("maintain_mileage is null");
            return (Criteria) this;
        }

        public Criteria andMaintainMileageIsNotNull() {
            addCriterion("maintain_mileage is not null");
            return (Criteria) this;
        }

        public Criteria andMaintainMileageEqualTo(Integer value) {
            addCriterion("maintain_mileage =", value, "maintainMileage");
            return (Criteria) this;
        }

        public Criteria andMaintainMileageNotEqualTo(Integer value) {
            addCriterion("maintain_mileage <>", value, "maintainMileage");
            return (Criteria) this;
        }

        public Criteria andMaintainMileageGreaterThan(Integer value) {
            addCriterion("maintain_mileage >", value, "maintainMileage");
            return (Criteria) this;
        }

        public Criteria andMaintainMileageGreaterThanOrEqualTo(Integer value) {
            addCriterion("maintain_mileage >=", value, "maintainMileage");
            return (Criteria) this;
        }

        public Criteria andMaintainMileageLessThan(Integer value) {
            addCriterion("maintain_mileage <", value, "maintainMileage");
            return (Criteria) this;
        }

        public Criteria andMaintainMileageLessThanOrEqualTo(Integer value) {
            addCriterion("maintain_mileage <=", value, "maintainMileage");
            return (Criteria) this;
        }

        public Criteria andMaintainMileageIn(List<Integer> values) {
            addCriterion("maintain_mileage in", values, "maintainMileage");
            return (Criteria) this;
        }

        public Criteria andMaintainMileageNotIn(List<Integer> values) {
            addCriterion("maintain_mileage not in", values, "maintainMileage");
            return (Criteria) this;
        }

        public Criteria andMaintainMileageBetween(Integer value1, Integer value2) {
            addCriterion("maintain_mileage between", value1, value2, "maintainMileage");
            return (Criteria) this;
        }

        public Criteria andMaintainMileageNotBetween(Integer value1, Integer value2) {
            addCriterion("maintain_mileage not between", value1, value2, "maintainMileage");
            return (Criteria) this;
        }

        public Criteria andMaintainMonthIsNull() {
            addCriterion("maintain_month is null");
            return (Criteria) this;
        }

        public Criteria andMaintainMonthIsNotNull() {
            addCriterion("maintain_month is not null");
            return (Criteria) this;
        }

        public Criteria andMaintainMonthEqualTo(Byte value) {
            addCriterion("maintain_month =", value, "maintainMonth");
            return (Criteria) this;
        }

        public Criteria andMaintainMonthNotEqualTo(Byte value) {
            addCriterion("maintain_month <>", value, "maintainMonth");
            return (Criteria) this;
        }

        public Criteria andMaintainMonthGreaterThan(Byte value) {
            addCriterion("maintain_month >", value, "maintainMonth");
            return (Criteria) this;
        }

        public Criteria andMaintainMonthGreaterThanOrEqualTo(Byte value) {
            addCriterion("maintain_month >=", value, "maintainMonth");
            return (Criteria) this;
        }

        public Criteria andMaintainMonthLessThan(Byte value) {
            addCriterion("maintain_month <", value, "maintainMonth");
            return (Criteria) this;
        }

        public Criteria andMaintainMonthLessThanOrEqualTo(Byte value) {
            addCriterion("maintain_month <=", value, "maintainMonth");
            return (Criteria) this;
        }

        public Criteria andMaintainMonthIn(List<Byte> values) {
            addCriterion("maintain_month in", values, "maintainMonth");
            return (Criteria) this;
        }

        public Criteria andMaintainMonthNotIn(List<Byte> values) {
            addCriterion("maintain_month not in", values, "maintainMonth");
            return (Criteria) this;
        }

        public Criteria andMaintainMonthBetween(Byte value1, Byte value2) {
            addCriterion("maintain_month between", value1, value2, "maintainMonth");
            return (Criteria) this;
        }

        public Criteria andMaintainMonthNotBetween(Byte value1, Byte value2) {
            addCriterion("maintain_month not between", value1, value2, "maintainMonth");
            return (Criteria) this;
        }

        public Criteria andDriveTypeIsNull() {
            addCriterion("drive_type is null");
            return (Criteria) this;
        }

        public Criteria andDriveTypeIsNotNull() {
            addCriterion("drive_type is not null");
            return (Criteria) this;
        }

        public Criteria andDriveTypeEqualTo(Byte value) {
            addCriterion("drive_type =", value, "driveType");
            return (Criteria) this;
        }

        public Criteria andDriveTypeNotEqualTo(Byte value) {
            addCriterion("drive_type <>", value, "driveType");
            return (Criteria) this;
        }

        public Criteria andDriveTypeGreaterThan(Byte value) {
            addCriterion("drive_type >", value, "driveType");
            return (Criteria) this;
        }

        public Criteria andDriveTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("drive_type >=", value, "driveType");
            return (Criteria) this;
        }

        public Criteria andDriveTypeLessThan(Byte value) {
            addCriterion("drive_type <", value, "driveType");
            return (Criteria) this;
        }

        public Criteria andDriveTypeLessThanOrEqualTo(Byte value) {
            addCriterion("drive_type <=", value, "driveType");
            return (Criteria) this;
        }

        public Criteria andDriveTypeIn(List<Byte> values) {
            addCriterion("drive_type in", values, "driveType");
            return (Criteria) this;
        }

        public Criteria andDriveTypeNotIn(List<Byte> values) {
            addCriterion("drive_type not in", values, "driveType");
            return (Criteria) this;
        }

        public Criteria andDriveTypeBetween(Byte value1, Byte value2) {
            addCriterion("drive_type between", value1, value2, "driveType");
            return (Criteria) this;
        }

        public Criteria andDriveTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("drive_type not between", value1, value2, "driveType");
            return (Criteria) this;
        }

        public Criteria andVehiclePicUrlIsNull() {
            addCriterion("vehicle_pic_url is null");
            return (Criteria) this;
        }

        public Criteria andVehiclePicUrlIsNotNull() {
            addCriterion("vehicle_pic_url is not null");
            return (Criteria) this;
        }

        public Criteria andVehiclePicUrlEqualTo(String value) {
            addCriterion("vehicle_pic_url =", value, "vehiclePicUrl");
            return (Criteria) this;
        }

        public Criteria andVehiclePicUrlNotEqualTo(String value) {
            addCriterion("vehicle_pic_url <>", value, "vehiclePicUrl");
            return (Criteria) this;
        }

        public Criteria andVehiclePicUrlGreaterThan(String value) {
            addCriterion("vehicle_pic_url >", value, "vehiclePicUrl");
            return (Criteria) this;
        }

        public Criteria andVehiclePicUrlGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_pic_url >=", value, "vehiclePicUrl");
            return (Criteria) this;
        }

        public Criteria andVehiclePicUrlLessThan(String value) {
            addCriterion("vehicle_pic_url <", value, "vehiclePicUrl");
            return (Criteria) this;
        }

        public Criteria andVehiclePicUrlLessThanOrEqualTo(String value) {
            addCriterion("vehicle_pic_url <=", value, "vehiclePicUrl");
            return (Criteria) this;
        }

        public Criteria andVehiclePicUrlLike(String value) {
            addCriterion("vehicle_pic_url like", value, "vehiclePicUrl");
            return (Criteria) this;
        }

        public Criteria andVehiclePicUrlNotLike(String value) {
            addCriterion("vehicle_pic_url not like", value, "vehiclePicUrl");
            return (Criteria) this;
        }

        public Criteria andVehiclePicUrlIn(List<String> values) {
            addCriterion("vehicle_pic_url in", values, "vehiclePicUrl");
            return (Criteria) this;
        }

        public Criteria andVehiclePicUrlNotIn(List<String> values) {
            addCriterion("vehicle_pic_url not in", values, "vehiclePicUrl");
            return (Criteria) this;
        }

        public Criteria andVehiclePicUrlBetween(String value1, String value2) {
            addCriterion("vehicle_pic_url between", value1, value2, "vehiclePicUrl");
            return (Criteria) this;
        }

        public Criteria andVehiclePicUrlNotBetween(String value1, String value2) {
            addCriterion("vehicle_pic_url not between", value1, value2, "vehiclePicUrl");
            return (Criteria) this;
        }

        public Criteria andStructCodeIsNull() {
            addCriterion("struct_code is null");
            return (Criteria) this;
        }

        public Criteria andStructCodeIsNotNull() {
            addCriterion("struct_code is not null");
            return (Criteria) this;
        }

        public Criteria andStructCodeEqualTo(String value) {
            addCriterion("struct_code =", value, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeNotEqualTo(String value) {
            addCriterion("struct_code <>", value, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeGreaterThan(String value) {
            addCriterion("struct_code >", value, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeGreaterThanOrEqualTo(String value) {
            addCriterion("struct_code >=", value, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeLessThan(String value) {
            addCriterion("struct_code <", value, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeLessThanOrEqualTo(String value) {
            addCriterion("struct_code <=", value, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeLike(String value) {
            addCriterion("struct_code like", value, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeNotLike(String value) {
            addCriterion("struct_code not like", value, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeIn(List<String> values) {
            addCriterion("struct_code in", values, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeNotIn(List<String> values) {
            addCriterion("struct_code not in", values, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeBetween(String value1, String value2) {
            addCriterion("struct_code between", value1, value2, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeNotBetween(String value1, String value2) {
            addCriterion("struct_code not between", value1, value2, "structCode");
            return (Criteria) this;
        }

        public Criteria andBindWiredGpsIsNull() {
            addCriterion("bind_wired_gps is null");
            return (Criteria) this;
        }

        public Criteria andBindWiredGpsIsNotNull() {
            addCriterion("bind_wired_gps is not null");
            return (Criteria) this;
        }

        public Criteria andBindWiredGpsEqualTo(Byte value) {
            addCriterion("bind_wired_gps =", value, "bindWiredGps");
            return (Criteria) this;
        }

        public Criteria andBindWiredGpsNotEqualTo(Byte value) {
            addCriterion("bind_wired_gps <>", value, "bindWiredGps");
            return (Criteria) this;
        }

        public Criteria andBindWiredGpsGreaterThan(Byte value) {
            addCriterion("bind_wired_gps >", value, "bindWiredGps");
            return (Criteria) this;
        }

        public Criteria andBindWiredGpsGreaterThanOrEqualTo(Byte value) {
            addCriterion("bind_wired_gps >=", value, "bindWiredGps");
            return (Criteria) this;
        }

        public Criteria andBindWiredGpsLessThan(Byte value) {
            addCriterion("bind_wired_gps <", value, "bindWiredGps");
            return (Criteria) this;
        }

        public Criteria andBindWiredGpsLessThanOrEqualTo(Byte value) {
            addCriterion("bind_wired_gps <=", value, "bindWiredGps");
            return (Criteria) this;
        }

        public Criteria andBindWiredGpsIn(List<Byte> values) {
            addCriterion("bind_wired_gps in", values, "bindWiredGps");
            return (Criteria) this;
        }

        public Criteria andBindWiredGpsNotIn(List<Byte> values) {
            addCriterion("bind_wired_gps not in", values, "bindWiredGps");
            return (Criteria) this;
        }

        public Criteria andBindWiredGpsBetween(Byte value1, Byte value2) {
            addCriterion("bind_wired_gps between", value1, value2, "bindWiredGps");
            return (Criteria) this;
        }

        public Criteria andBindWiredGpsNotBetween(Byte value1, Byte value2) {
            addCriterion("bind_wired_gps not between", value1, value2, "bindWiredGps");
            return (Criteria) this;
        }

        public Criteria andWiredGpsNoIsNull() {
            addCriterion("wired_gps_no is null");
            return (Criteria) this;
        }

        public Criteria andWiredGpsNoIsNotNull() {
            addCriterion("wired_gps_no is not null");
            return (Criteria) this;
        }

        public Criteria andWiredGpsNoEqualTo(String value) {
            addCriterion("wired_gps_no =", value, "wiredGpsNo");
            return (Criteria) this;
        }

        public Criteria andWiredGpsNoNotEqualTo(String value) {
            addCriterion("wired_gps_no <>", value, "wiredGpsNo");
            return (Criteria) this;
        }

        public Criteria andWiredGpsNoGreaterThan(String value) {
            addCriterion("wired_gps_no >", value, "wiredGpsNo");
            return (Criteria) this;
        }

        public Criteria andWiredGpsNoGreaterThanOrEqualTo(String value) {
            addCriterion("wired_gps_no >=", value, "wiredGpsNo");
            return (Criteria) this;
        }

        public Criteria andWiredGpsNoLessThan(String value) {
            addCriterion("wired_gps_no <", value, "wiredGpsNo");
            return (Criteria) this;
        }

        public Criteria andWiredGpsNoLessThanOrEqualTo(String value) {
            addCriterion("wired_gps_no <=", value, "wiredGpsNo");
            return (Criteria) this;
        }

        public Criteria andWiredGpsNoLike(String value) {
            addCriterion("wired_gps_no like", value, "wiredGpsNo");
            return (Criteria) this;
        }

        public Criteria andWiredGpsNoNotLike(String value) {
            addCriterion("wired_gps_no not like", value, "wiredGpsNo");
            return (Criteria) this;
        }

        public Criteria andWiredGpsNoIn(List<String> values) {
            addCriterion("wired_gps_no in", values, "wiredGpsNo");
            return (Criteria) this;
        }

        public Criteria andWiredGpsNoNotIn(List<String> values) {
            addCriterion("wired_gps_no not in", values, "wiredGpsNo");
            return (Criteria) this;
        }

        public Criteria andWiredGpsNoBetween(String value1, String value2) {
            addCriterion("wired_gps_no between", value1, value2, "wiredGpsNo");
            return (Criteria) this;
        }

        public Criteria andWiredGpsNoNotBetween(String value1, String value2) {
            addCriterion("wired_gps_no not between", value1, value2, "wiredGpsNo");
            return (Criteria) this;
        }

        public Criteria andWiredSimNoIsNull() {
            addCriterion("wired_sim_no is null");
            return (Criteria) this;
        }

        public Criteria andWiredSimNoIsNotNull() {
            addCriterion("wired_sim_no is not null");
            return (Criteria) this;
        }

        public Criteria andWiredSimNoEqualTo(String value) {
            addCriterion("wired_sim_no =", value, "wiredSimNo");
            return (Criteria) this;
        }

        public Criteria andWiredSimNoNotEqualTo(String value) {
            addCriterion("wired_sim_no <>", value, "wiredSimNo");
            return (Criteria) this;
        }

        public Criteria andWiredSimNoGreaterThan(String value) {
            addCriterion("wired_sim_no >", value, "wiredSimNo");
            return (Criteria) this;
        }

        public Criteria andWiredSimNoGreaterThanOrEqualTo(String value) {
            addCriterion("wired_sim_no >=", value, "wiredSimNo");
            return (Criteria) this;
        }

        public Criteria andWiredSimNoLessThan(String value) {
            addCriterion("wired_sim_no <", value, "wiredSimNo");
            return (Criteria) this;
        }

        public Criteria andWiredSimNoLessThanOrEqualTo(String value) {
            addCriterion("wired_sim_no <=", value, "wiredSimNo");
            return (Criteria) this;
        }

        public Criteria andWiredSimNoLike(String value) {
            addCriterion("wired_sim_no like", value, "wiredSimNo");
            return (Criteria) this;
        }

        public Criteria andWiredSimNoNotLike(String value) {
            addCriterion("wired_sim_no not like", value, "wiredSimNo");
            return (Criteria) this;
        }

        public Criteria andWiredSimNoIn(List<String> values) {
            addCriterion("wired_sim_no in", values, "wiredSimNo");
            return (Criteria) this;
        }

        public Criteria andWiredSimNoNotIn(List<String> values) {
            addCriterion("wired_sim_no not in", values, "wiredSimNo");
            return (Criteria) this;
        }

        public Criteria andWiredSimNoBetween(String value1, String value2) {
            addCriterion("wired_sim_no between", value1, value2, "wiredSimNo");
            return (Criteria) this;
        }

        public Criteria andWiredSimNoNotBetween(String value1, String value2) {
            addCriterion("wired_sim_no not between", value1, value2, "wiredSimNo");
            return (Criteria) this;
        }

        public Criteria andWiredBindTimeIsNull() {
            addCriterion("wired_bind_time is null");
            return (Criteria) this;
        }

        public Criteria andWiredBindTimeIsNotNull() {
            addCriterion("wired_bind_time is not null");
            return (Criteria) this;
        }

        public Criteria andWiredBindTimeEqualTo(String value) {
            addCriterion("wired_bind_time =", value, "wiredBindTime");
            return (Criteria) this;
        }

        public Criteria andWiredBindTimeNotEqualTo(String value) {
            addCriterion("wired_bind_time <>", value, "wiredBindTime");
            return (Criteria) this;
        }

        public Criteria andWiredBindTimeGreaterThan(String value) {
            addCriterion("wired_bind_time >", value, "wiredBindTime");
            return (Criteria) this;
        }

        public Criteria andWiredBindTimeGreaterThanOrEqualTo(String value) {
            addCriterion("wired_bind_time >=", value, "wiredBindTime");
            return (Criteria) this;
        }

        public Criteria andWiredBindTimeLessThan(String value) {
            addCriterion("wired_bind_time <", value, "wiredBindTime");
            return (Criteria) this;
        }

        public Criteria andWiredBindTimeLessThanOrEqualTo(String value) {
            addCriterion("wired_bind_time <=", value, "wiredBindTime");
            return (Criteria) this;
        }

        public Criteria andWiredBindTimeLike(String value) {
            addCriterion("wired_bind_time like", value, "wiredBindTime");
            return (Criteria) this;
        }

        public Criteria andWiredBindTimeNotLike(String value) {
            addCriterion("wired_bind_time not like", value, "wiredBindTime");
            return (Criteria) this;
        }

        public Criteria andWiredBindTimeIn(List<String> values) {
            addCriterion("wired_bind_time in", values, "wiredBindTime");
            return (Criteria) this;
        }

        public Criteria andWiredBindTimeNotIn(List<String> values) {
            addCriterion("wired_bind_time not in", values, "wiredBindTime");
            return (Criteria) this;
        }

        public Criteria andWiredBindTimeBetween(String value1, String value2) {
            addCriterion("wired_bind_time between", value1, value2, "wiredBindTime");
            return (Criteria) this;
        }

        public Criteria andWiredBindTimeNotBetween(String value1, String value2) {
            addCriterion("wired_bind_time not between", value1, value2, "wiredBindTime");
            return (Criteria) this;
        }

        public Criteria andWiredUnbindTimeIsNull() {
            addCriterion("wired_unbind_time is null");
            return (Criteria) this;
        }

        public Criteria andWiredUnbindTimeIsNotNull() {
            addCriterion("wired_unbind_time is not null");
            return (Criteria) this;
        }

        public Criteria andWiredUnbindTimeEqualTo(String value) {
            addCriterion("wired_unbind_time =", value, "wiredUnbindTime");
            return (Criteria) this;
        }

        public Criteria andWiredUnbindTimeNotEqualTo(String value) {
            addCriterion("wired_unbind_time <>", value, "wiredUnbindTime");
            return (Criteria) this;
        }

        public Criteria andWiredUnbindTimeGreaterThan(String value) {
            addCriterion("wired_unbind_time >", value, "wiredUnbindTime");
            return (Criteria) this;
        }

        public Criteria andWiredUnbindTimeGreaterThanOrEqualTo(String value) {
            addCriterion("wired_unbind_time >=", value, "wiredUnbindTime");
            return (Criteria) this;
        }

        public Criteria andWiredUnbindTimeLessThan(String value) {
            addCriterion("wired_unbind_time <", value, "wiredUnbindTime");
            return (Criteria) this;
        }

        public Criteria andWiredUnbindTimeLessThanOrEqualTo(String value) {
            addCriterion("wired_unbind_time <=", value, "wiredUnbindTime");
            return (Criteria) this;
        }

        public Criteria andWiredUnbindTimeLike(String value) {
            addCriterion("wired_unbind_time like", value, "wiredUnbindTime");
            return (Criteria) this;
        }

        public Criteria andWiredUnbindTimeNotLike(String value) {
            addCriterion("wired_unbind_time not like", value, "wiredUnbindTime");
            return (Criteria) this;
        }

        public Criteria andWiredUnbindTimeIn(List<String> values) {
            addCriterion("wired_unbind_time in", values, "wiredUnbindTime");
            return (Criteria) this;
        }

        public Criteria andWiredUnbindTimeNotIn(List<String> values) {
            addCriterion("wired_unbind_time not in", values, "wiredUnbindTime");
            return (Criteria) this;
        }

        public Criteria andWiredUnbindTimeBetween(String value1, String value2) {
            addCriterion("wired_unbind_time between", value1, value2, "wiredUnbindTime");
            return (Criteria) this;
        }

        public Criteria andWiredUnbindTimeNotBetween(String value1, String value2) {
            addCriterion("wired_unbind_time not between", value1, value2, "wiredUnbindTime");
            return (Criteria) this;
        }

        public Criteria andBindWifiGpsIsNull() {
            addCriterion("bind_wifi_gps is null");
            return (Criteria) this;
        }

        public Criteria andBindWifiGpsIsNotNull() {
            addCriterion("bind_wifi_gps is not null");
            return (Criteria) this;
        }

        public Criteria andBindWifiGpsEqualTo(Byte value) {
            addCriterion("bind_wifi_gps =", value, "bindWifiGps");
            return (Criteria) this;
        }

        public Criteria andBindWifiGpsNotEqualTo(Byte value) {
            addCriterion("bind_wifi_gps <>", value, "bindWifiGps");
            return (Criteria) this;
        }

        public Criteria andBindWifiGpsGreaterThan(Byte value) {
            addCriterion("bind_wifi_gps >", value, "bindWifiGps");
            return (Criteria) this;
        }

        public Criteria andBindWifiGpsGreaterThanOrEqualTo(Byte value) {
            addCriterion("bind_wifi_gps >=", value, "bindWifiGps");
            return (Criteria) this;
        }

        public Criteria andBindWifiGpsLessThan(Byte value) {
            addCriterion("bind_wifi_gps <", value, "bindWifiGps");
            return (Criteria) this;
        }

        public Criteria andBindWifiGpsLessThanOrEqualTo(Byte value) {
            addCriterion("bind_wifi_gps <=", value, "bindWifiGps");
            return (Criteria) this;
        }

        public Criteria andBindWifiGpsIn(List<Byte> values) {
            addCriterion("bind_wifi_gps in", values, "bindWifiGps");
            return (Criteria) this;
        }

        public Criteria andBindWifiGpsNotIn(List<Byte> values) {
            addCriterion("bind_wifi_gps not in", values, "bindWifiGps");
            return (Criteria) this;
        }

        public Criteria andBindWifiGpsBetween(Byte value1, Byte value2) {
            addCriterion("bind_wifi_gps between", value1, value2, "bindWifiGps");
            return (Criteria) this;
        }

        public Criteria andBindWifiGpsNotBetween(Byte value1, Byte value2) {
            addCriterion("bind_wifi_gps not between", value1, value2, "bindWifiGps");
            return (Criteria) this;
        }

        public Criteria andWifiGpsNoIsNull() {
            addCriterion("wifi_gps_no is null");
            return (Criteria) this;
        }

        public Criteria andWifiGpsNoIsNotNull() {
            addCriterion("wifi_gps_no is not null");
            return (Criteria) this;
        }

        public Criteria andWifiGpsNoEqualTo(String value) {
            addCriterion("wifi_gps_no =", value, "wifiGpsNo");
            return (Criteria) this;
        }

        public Criteria andWifiGpsNoNotEqualTo(String value) {
            addCriterion("wifi_gps_no <>", value, "wifiGpsNo");
            return (Criteria) this;
        }

        public Criteria andWifiGpsNoGreaterThan(String value) {
            addCriterion("wifi_gps_no >", value, "wifiGpsNo");
            return (Criteria) this;
        }

        public Criteria andWifiGpsNoGreaterThanOrEqualTo(String value) {
            addCriterion("wifi_gps_no >=", value, "wifiGpsNo");
            return (Criteria) this;
        }

        public Criteria andWifiGpsNoLessThan(String value) {
            addCriterion("wifi_gps_no <", value, "wifiGpsNo");
            return (Criteria) this;
        }

        public Criteria andWifiGpsNoLessThanOrEqualTo(String value) {
            addCriterion("wifi_gps_no <=", value, "wifiGpsNo");
            return (Criteria) this;
        }

        public Criteria andWifiGpsNoLike(String value) {
            addCriterion("wifi_gps_no like", value, "wifiGpsNo");
            return (Criteria) this;
        }

        public Criteria andWifiGpsNoNotLike(String value) {
            addCriterion("wifi_gps_no not like", value, "wifiGpsNo");
            return (Criteria) this;
        }

        public Criteria andWifiGpsNoIn(List<String> values) {
            addCriterion("wifi_gps_no in", values, "wifiGpsNo");
            return (Criteria) this;
        }

        public Criteria andWifiGpsNoNotIn(List<String> values) {
            addCriterion("wifi_gps_no not in", values, "wifiGpsNo");
            return (Criteria) this;
        }

        public Criteria andWifiGpsNoBetween(String value1, String value2) {
            addCriterion("wifi_gps_no between", value1, value2, "wifiGpsNo");
            return (Criteria) this;
        }

        public Criteria andWifiGpsNoNotBetween(String value1, String value2) {
            addCriterion("wifi_gps_no not between", value1, value2, "wifiGpsNo");
            return (Criteria) this;
        }

        public Criteria andWifiSimNoIsNull() {
            addCriterion("wifi_sim_no is null");
            return (Criteria) this;
        }

        public Criteria andWifiSimNoIsNotNull() {
            addCriterion("wifi_sim_no is not null");
            return (Criteria) this;
        }

        public Criteria andWifiSimNoEqualTo(String value) {
            addCriterion("wifi_sim_no =", value, "wifiSimNo");
            return (Criteria) this;
        }

        public Criteria andWifiSimNoNotEqualTo(String value) {
            addCriterion("wifi_sim_no <>", value, "wifiSimNo");
            return (Criteria) this;
        }

        public Criteria andWifiSimNoGreaterThan(String value) {
            addCriterion("wifi_sim_no >", value, "wifiSimNo");
            return (Criteria) this;
        }

        public Criteria andWifiSimNoGreaterThanOrEqualTo(String value) {
            addCriterion("wifi_sim_no >=", value, "wifiSimNo");
            return (Criteria) this;
        }

        public Criteria andWifiSimNoLessThan(String value) {
            addCriterion("wifi_sim_no <", value, "wifiSimNo");
            return (Criteria) this;
        }

        public Criteria andWifiSimNoLessThanOrEqualTo(String value) {
            addCriterion("wifi_sim_no <=", value, "wifiSimNo");
            return (Criteria) this;
        }

        public Criteria andWifiSimNoLike(String value) {
            addCriterion("wifi_sim_no like", value, "wifiSimNo");
            return (Criteria) this;
        }

        public Criteria andWifiSimNoNotLike(String value) {
            addCriterion("wifi_sim_no not like", value, "wifiSimNo");
            return (Criteria) this;
        }

        public Criteria andWifiSimNoIn(List<String> values) {
            addCriterion("wifi_sim_no in", values, "wifiSimNo");
            return (Criteria) this;
        }

        public Criteria andWifiSimNoNotIn(List<String> values) {
            addCriterion("wifi_sim_no not in", values, "wifiSimNo");
            return (Criteria) this;
        }

        public Criteria andWifiSimNoBetween(String value1, String value2) {
            addCriterion("wifi_sim_no between", value1, value2, "wifiSimNo");
            return (Criteria) this;
        }

        public Criteria andWifiSimNoNotBetween(String value1, String value2) {
            addCriterion("wifi_sim_no not between", value1, value2, "wifiSimNo");
            return (Criteria) this;
        }

        public Criteria andWifiBindTimeIsNull() {
            addCriterion("wifi_bind_time is null");
            return (Criteria) this;
        }

        public Criteria andWifiBindTimeIsNotNull() {
            addCriterion("wifi_bind_time is not null");
            return (Criteria) this;
        }

        public Criteria andWifiBindTimeEqualTo(String value) {
            addCriterion("wifi_bind_time =", value, "wifiBindTime");
            return (Criteria) this;
        }

        public Criteria andWifiBindTimeNotEqualTo(String value) {
            addCriterion("wifi_bind_time <>", value, "wifiBindTime");
            return (Criteria) this;
        }

        public Criteria andWifiBindTimeGreaterThan(String value) {
            addCriterion("wifi_bind_time >", value, "wifiBindTime");
            return (Criteria) this;
        }

        public Criteria andWifiBindTimeGreaterThanOrEqualTo(String value) {
            addCriterion("wifi_bind_time >=", value, "wifiBindTime");
            return (Criteria) this;
        }

        public Criteria andWifiBindTimeLessThan(String value) {
            addCriterion("wifi_bind_time <", value, "wifiBindTime");
            return (Criteria) this;
        }

        public Criteria andWifiBindTimeLessThanOrEqualTo(String value) {
            addCriterion("wifi_bind_time <=", value, "wifiBindTime");
            return (Criteria) this;
        }

        public Criteria andWifiBindTimeLike(String value) {
            addCriterion("wifi_bind_time like", value, "wifiBindTime");
            return (Criteria) this;
        }

        public Criteria andWifiBindTimeNotLike(String value) {
            addCriterion("wifi_bind_time not like", value, "wifiBindTime");
            return (Criteria) this;
        }

        public Criteria andWifiBindTimeIn(List<String> values) {
            addCriterion("wifi_bind_time in", values, "wifiBindTime");
            return (Criteria) this;
        }

        public Criteria andWifiBindTimeNotIn(List<String> values) {
            addCriterion("wifi_bind_time not in", values, "wifiBindTime");
            return (Criteria) this;
        }

        public Criteria andWifiBindTimeBetween(String value1, String value2) {
            addCriterion("wifi_bind_time between", value1, value2, "wifiBindTime");
            return (Criteria) this;
        }

        public Criteria andWifiBindTimeNotBetween(String value1, String value2) {
            addCriterion("wifi_bind_time not between", value1, value2, "wifiBindTime");
            return (Criteria) this;
        }

        public Criteria andWifiUnbindTimeIsNull() {
            addCriterion("wifi_unbind_time is null");
            return (Criteria) this;
        }

        public Criteria andWifiUnbindTimeIsNotNull() {
            addCriterion("wifi_unbind_time is not null");
            return (Criteria) this;
        }

        public Criteria andWifiUnbindTimeEqualTo(String value) {
            addCriterion("wifi_unbind_time =", value, "wifiUnbindTime");
            return (Criteria) this;
        }

        public Criteria andWifiUnbindTimeNotEqualTo(String value) {
            addCriterion("wifi_unbind_time <>", value, "wifiUnbindTime");
            return (Criteria) this;
        }

        public Criteria andWifiUnbindTimeGreaterThan(String value) {
            addCriterion("wifi_unbind_time >", value, "wifiUnbindTime");
            return (Criteria) this;
        }

        public Criteria andWifiUnbindTimeGreaterThanOrEqualTo(String value) {
            addCriterion("wifi_unbind_time >=", value, "wifiUnbindTime");
            return (Criteria) this;
        }

        public Criteria andWifiUnbindTimeLessThan(String value) {
            addCriterion("wifi_unbind_time <", value, "wifiUnbindTime");
            return (Criteria) this;
        }

        public Criteria andWifiUnbindTimeLessThanOrEqualTo(String value) {
            addCriterion("wifi_unbind_time <=", value, "wifiUnbindTime");
            return (Criteria) this;
        }

        public Criteria andWifiUnbindTimeLike(String value) {
            addCriterion("wifi_unbind_time like", value, "wifiUnbindTime");
            return (Criteria) this;
        }

        public Criteria andWifiUnbindTimeNotLike(String value) {
            addCriterion("wifi_unbind_time not like", value, "wifiUnbindTime");
            return (Criteria) this;
        }

        public Criteria andWifiUnbindTimeIn(List<String> values) {
            addCriterion("wifi_unbind_time in", values, "wifiUnbindTime");
            return (Criteria) this;
        }

        public Criteria andWifiUnbindTimeNotIn(List<String> values) {
            addCriterion("wifi_unbind_time not in", values, "wifiUnbindTime");
            return (Criteria) this;
        }

        public Criteria andWifiUnbindTimeBetween(String value1, String value2) {
            addCriterion("wifi_unbind_time between", value1, value2, "wifiUnbindTime");
            return (Criteria) this;
        }

        public Criteria andWifiUnbindTimeNotBetween(String value1, String value2) {
            addCriterion("wifi_unbind_time not between", value1, value2, "wifiUnbindTime");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongedSysIsNull() {
            addCriterion("vehicle_belonged_sys is null");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongedSysIsNotNull() {
            addCriterion("vehicle_belonged_sys is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongedSysEqualTo(String value) {
            addCriterion("vehicle_belonged_sys =", value, "vehicleBelongedSys");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongedSysNotEqualTo(String value) {
            addCriterion("vehicle_belonged_sys <>", value, "vehicleBelongedSys");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongedSysGreaterThan(String value) {
            addCriterion("vehicle_belonged_sys >", value, "vehicleBelongedSys");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongedSysGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_belonged_sys >=", value, "vehicleBelongedSys");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongedSysLessThan(String value) {
            addCriterion("vehicle_belonged_sys <", value, "vehicleBelongedSys");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongedSysLessThanOrEqualTo(String value) {
            addCriterion("vehicle_belonged_sys <=", value, "vehicleBelongedSys");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongedSysLike(String value) {
            addCriterion("vehicle_belonged_sys like", value, "vehicleBelongedSys");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongedSysNotLike(String value) {
            addCriterion("vehicle_belonged_sys not like", value, "vehicleBelongedSys");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongedSysIn(List<String> values) {
            addCriterion("vehicle_belonged_sys in", values, "vehicleBelongedSys");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongedSysNotIn(List<String> values) {
            addCriterion("vehicle_belonged_sys not in", values, "vehicleBelongedSys");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongedSysBetween(String value1, String value2) {
            addCriterion("vehicle_belonged_sys between", value1, value2, "vehicleBelongedSys");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongedSysNotBetween(String value1, String value2) {
            addCriterion("vehicle_belonged_sys not between", value1, value2, "vehicleBelongedSys");
            return (Criteria) this;
        }

        public Criteria andBelongStructNameIsNull() {
            addCriterion("belong_struct_name is null");
            return (Criteria) this;
        }

        public Criteria andBelongStructNameIsNotNull() {
            addCriterion("belong_struct_name is not null");
            return (Criteria) this;
        }

        public Criteria andBelongStructNameEqualTo(String value) {
            addCriterion("belong_struct_name =", value, "belongStructName");
            return (Criteria) this;
        }

        public Criteria andBelongStructNameNotEqualTo(String value) {
            addCriterion("belong_struct_name <>", value, "belongStructName");
            return (Criteria) this;
        }

        public Criteria andBelongStructNameGreaterThan(String value) {
            addCriterion("belong_struct_name >", value, "belongStructName");
            return (Criteria) this;
        }

        public Criteria andBelongStructNameGreaterThanOrEqualTo(String value) {
            addCriterion("belong_struct_name >=", value, "belongStructName");
            return (Criteria) this;
        }

        public Criteria andBelongStructNameLessThan(String value) {
            addCriterion("belong_struct_name <", value, "belongStructName");
            return (Criteria) this;
        }

        public Criteria andBelongStructNameLessThanOrEqualTo(String value) {
            addCriterion("belong_struct_name <=", value, "belongStructName");
            return (Criteria) this;
        }

        public Criteria andBelongStructNameLike(String value) {
            addCriterion("belong_struct_name like", value, "belongStructName");
            return (Criteria) this;
        }

        public Criteria andBelongStructNameNotLike(String value) {
            addCriterion("belong_struct_name not like", value, "belongStructName");
            return (Criteria) this;
        }

        public Criteria andBelongStructNameIn(List<String> values) {
            addCriterion("belong_struct_name in", values, "belongStructName");
            return (Criteria) this;
        }

        public Criteria andBelongStructNameNotIn(List<String> values) {
            addCriterion("belong_struct_name not in", values, "belongStructName");
            return (Criteria) this;
        }

        public Criteria andBelongStructNameBetween(String value1, String value2) {
            addCriterion("belong_struct_name between", value1, value2, "belongStructName");
            return (Criteria) this;
        }

        public Criteria andBelongStructNameNotBetween(String value1, String value2) {
            addCriterion("belong_struct_name not between", value1, value2, "belongStructName");
            return (Criteria) this;
        }

        public Criteria andBelongStructCodeIsNull() {
            addCriterion("belong_struct_code is null");
            return (Criteria) this;
        }

        public Criteria andBelongStructCodeIsNotNull() {
            addCriterion("belong_struct_code is not null");
            return (Criteria) this;
        }

        public Criteria andBelongStructCodeEqualTo(String value) {
            addCriterion("belong_struct_code =", value, "belongStructCode");
            return (Criteria) this;
        }

        public Criteria andBelongStructCodeNotEqualTo(String value) {
            addCriterion("belong_struct_code <>", value, "belongStructCode");
            return (Criteria) this;
        }

        public Criteria andBelongStructCodeGreaterThan(String value) {
            addCriterion("belong_struct_code >", value, "belongStructCode");
            return (Criteria) this;
        }

        public Criteria andBelongStructCodeGreaterThanOrEqualTo(String value) {
            addCriterion("belong_struct_code >=", value, "belongStructCode");
            return (Criteria) this;
        }

        public Criteria andBelongStructCodeLessThan(String value) {
            addCriterion("belong_struct_code <", value, "belongStructCode");
            return (Criteria) this;
        }

        public Criteria andBelongStructCodeLessThanOrEqualTo(String value) {
            addCriterion("belong_struct_code <=", value, "belongStructCode");
            return (Criteria) this;
        }

        public Criteria andBelongStructCodeLike(String value) {
            addCriterion("belong_struct_code like", value, "belongStructCode");
            return (Criteria) this;
        }

        public Criteria andBelongStructCodeNotLike(String value) {
            addCriterion("belong_struct_code not like", value, "belongStructCode");
            return (Criteria) this;
        }

        public Criteria andBelongStructCodeIn(List<String> values) {
            addCriterion("belong_struct_code in", values, "belongStructCode");
            return (Criteria) this;
        }

        public Criteria andBelongStructCodeNotIn(List<String> values) {
            addCriterion("belong_struct_code not in", values, "belongStructCode");
            return (Criteria) this;
        }

        public Criteria andBelongStructCodeBetween(String value1, String value2) {
            addCriterion("belong_struct_code between", value1, value2, "belongStructCode");
            return (Criteria) this;
        }

        public Criteria andBelongStructCodeNotBetween(String value1, String value2) {
            addCriterion("belong_struct_code not between", value1, value2, "belongStructCode");
            return (Criteria) this;
        }

        public Criteria andOperateStructNameIsNull() {
            addCriterion("operate_struct_name is null");
            return (Criteria) this;
        }

        public Criteria andOperateStructNameIsNotNull() {
            addCriterion("operate_struct_name is not null");
            return (Criteria) this;
        }

        public Criteria andOperateStructNameEqualTo(String value) {
            addCriterion("operate_struct_name =", value, "operateStructName");
            return (Criteria) this;
        }

        public Criteria andOperateStructNameNotEqualTo(String value) {
            addCriterion("operate_struct_name <>", value, "operateStructName");
            return (Criteria) this;
        }

        public Criteria andOperateStructNameGreaterThan(String value) {
            addCriterion("operate_struct_name >", value, "operateStructName");
            return (Criteria) this;
        }

        public Criteria andOperateStructNameGreaterThanOrEqualTo(String value) {
            addCriterion("operate_struct_name >=", value, "operateStructName");
            return (Criteria) this;
        }

        public Criteria andOperateStructNameLessThan(String value) {
            addCriterion("operate_struct_name <", value, "operateStructName");
            return (Criteria) this;
        }

        public Criteria andOperateStructNameLessThanOrEqualTo(String value) {
            addCriterion("operate_struct_name <=", value, "operateStructName");
            return (Criteria) this;
        }

        public Criteria andOperateStructNameLike(String value) {
            addCriterion("operate_struct_name like", value, "operateStructName");
            return (Criteria) this;
        }

        public Criteria andOperateStructNameNotLike(String value) {
            addCriterion("operate_struct_name not like", value, "operateStructName");
            return (Criteria) this;
        }

        public Criteria andOperateStructNameIn(List<String> values) {
            addCriterion("operate_struct_name in", values, "operateStructName");
            return (Criteria) this;
        }

        public Criteria andOperateStructNameNotIn(List<String> values) {
            addCriterion("operate_struct_name not in", values, "operateStructName");
            return (Criteria) this;
        }

        public Criteria andOperateStructNameBetween(String value1, String value2) {
            addCriterion("operate_struct_name between", value1, value2, "operateStructName");
            return (Criteria) this;
        }

        public Criteria andOperateStructNameNotBetween(String value1, String value2) {
            addCriterion("operate_struct_name not between", value1, value2, "operateStructName");
            return (Criteria) this;
        }

        public Criteria andOperateStructCodeIsNull() {
            addCriterion("operate_struct_code is null");
            return (Criteria) this;
        }

        public Criteria andOperateStructCodeIsNotNull() {
            addCriterion("operate_struct_code is not null");
            return (Criteria) this;
        }

        public Criteria andOperateStructCodeEqualTo(String value) {
            addCriterion("operate_struct_code =", value, "operateStructCode");
            return (Criteria) this;
        }

        public Criteria andOperateStructCodeNotEqualTo(String value) {
            addCriterion("operate_struct_code <>", value, "operateStructCode");
            return (Criteria) this;
        }

        public Criteria andOperateStructCodeGreaterThan(String value) {
            addCriterion("operate_struct_code >", value, "operateStructCode");
            return (Criteria) this;
        }

        public Criteria andOperateStructCodeGreaterThanOrEqualTo(String value) {
            addCriterion("operate_struct_code >=", value, "operateStructCode");
            return (Criteria) this;
        }

        public Criteria andOperateStructCodeLessThan(String value) {
            addCriterion("operate_struct_code <", value, "operateStructCode");
            return (Criteria) this;
        }

        public Criteria andOperateStructCodeLessThanOrEqualTo(String value) {
            addCriterion("operate_struct_code <=", value, "operateStructCode");
            return (Criteria) this;
        }

        public Criteria andOperateStructCodeLike(String value) {
            addCriterion("operate_struct_code like", value, "operateStructCode");
            return (Criteria) this;
        }

        public Criteria andOperateStructCodeNotLike(String value) {
            addCriterion("operate_struct_code not like", value, "operateStructCode");
            return (Criteria) this;
        }

        public Criteria andOperateStructCodeIn(List<String> values) {
            addCriterion("operate_struct_code in", values, "operateStructCode");
            return (Criteria) this;
        }

        public Criteria andOperateStructCodeNotIn(List<String> values) {
            addCriterion("operate_struct_code not in", values, "operateStructCode");
            return (Criteria) this;
        }

        public Criteria andOperateStructCodeBetween(String value1, String value2) {
            addCriterion("operate_struct_code between", value1, value2, "operateStructCode");
            return (Criteria) this;
        }

        public Criteria andOperateStructCodeNotBetween(String value1, String value2) {
            addCriterion("operate_struct_code not between", value1, value2, "operateStructCode");
            return (Criteria) this;
        }

        public Criteria andOperateBussNameIsNull() {
            addCriterion("operate_buss_name is null");
            return (Criteria) this;
        }

        public Criteria andOperateBussNameIsNotNull() {
            addCriterion("operate_buss_name is not null");
            return (Criteria) this;
        }

        public Criteria andOperateBussNameEqualTo(String value) {
            addCriterion("operate_buss_name =", value, "operateBussName");
            return (Criteria) this;
        }

        public Criteria andOperateBussNameNotEqualTo(String value) {
            addCriterion("operate_buss_name <>", value, "operateBussName");
            return (Criteria) this;
        }

        public Criteria andOperateBussNameGreaterThan(String value) {
            addCriterion("operate_buss_name >", value, "operateBussName");
            return (Criteria) this;
        }

        public Criteria andOperateBussNameGreaterThanOrEqualTo(String value) {
            addCriterion("operate_buss_name >=", value, "operateBussName");
            return (Criteria) this;
        }

        public Criteria andOperateBussNameLessThan(String value) {
            addCriterion("operate_buss_name <", value, "operateBussName");
            return (Criteria) this;
        }

        public Criteria andOperateBussNameLessThanOrEqualTo(String value) {
            addCriterion("operate_buss_name <=", value, "operateBussName");
            return (Criteria) this;
        }

        public Criteria andOperateBussNameLike(String value) {
            addCriterion("operate_buss_name like", value, "operateBussName");
            return (Criteria) this;
        }

        public Criteria andOperateBussNameNotLike(String value) {
            addCriterion("operate_buss_name not like", value, "operateBussName");
            return (Criteria) this;
        }

        public Criteria andOperateBussNameIn(List<String> values) {
            addCriterion("operate_buss_name in", values, "operateBussName");
            return (Criteria) this;
        }

        public Criteria andOperateBussNameNotIn(List<String> values) {
            addCriterion("operate_buss_name not in", values, "operateBussName");
            return (Criteria) this;
        }

        public Criteria andOperateBussNameBetween(String value1, String value2) {
            addCriterion("operate_buss_name between", value1, value2, "operateBussName");
            return (Criteria) this;
        }

        public Criteria andOperateBussNameNotBetween(String value1, String value2) {
            addCriterion("operate_buss_name not between", value1, value2, "operateBussName");
            return (Criteria) this;
        }

        public Criteria andOperateBussCodeIsNull() {
            addCriterion("operate_buss_code is null");
            return (Criteria) this;
        }

        public Criteria andOperateBussCodeIsNotNull() {
            addCriterion("operate_buss_code is not null");
            return (Criteria) this;
        }

        public Criteria andOperateBussCodeEqualTo(String value) {
            addCriterion("operate_buss_code =", value, "operateBussCode");
            return (Criteria) this;
        }

        public Criteria andOperateBussCodeNotEqualTo(String value) {
            addCriterion("operate_buss_code <>", value, "operateBussCode");
            return (Criteria) this;
        }

        public Criteria andOperateBussCodeGreaterThan(String value) {
            addCriterion("operate_buss_code >", value, "operateBussCode");
            return (Criteria) this;
        }

        public Criteria andOperateBussCodeGreaterThanOrEqualTo(String value) {
            addCriterion("operate_buss_code >=", value, "operateBussCode");
            return (Criteria) this;
        }

        public Criteria andOperateBussCodeLessThan(String value) {
            addCriterion("operate_buss_code <", value, "operateBussCode");
            return (Criteria) this;
        }

        public Criteria andOperateBussCodeLessThanOrEqualTo(String value) {
            addCriterion("operate_buss_code <=", value, "operateBussCode");
            return (Criteria) this;
        }

        public Criteria andOperateBussCodeLike(String value) {
            addCriterion("operate_buss_code like", value, "operateBussCode");
            return (Criteria) this;
        }

        public Criteria andOperateBussCodeNotLike(String value) {
            addCriterion("operate_buss_code not like", value, "operateBussCode");
            return (Criteria) this;
        }

        public Criteria andOperateBussCodeIn(List<String> values) {
            addCriterion("operate_buss_code in", values, "operateBussCode");
            return (Criteria) this;
        }

        public Criteria andOperateBussCodeNotIn(List<String> values) {
            addCriterion("operate_buss_code not in", values, "operateBussCode");
            return (Criteria) this;
        }

        public Criteria andOperateBussCodeBetween(String value1, String value2) {
            addCriterion("operate_buss_code between", value1, value2, "operateBussCode");
            return (Criteria) this;
        }

        public Criteria andOperateBussCodeNotBetween(String value1, String value2) {
            addCriterion("operate_buss_code not between", value1, value2, "operateBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleMileageIsNull() {
            addCriterion("vehicle_mileage is null");
            return (Criteria) this;
        }

        public Criteria andVehicleMileageIsNotNull() {
            addCriterion("vehicle_mileage is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleMileageEqualTo(BigDecimal value) {
            addCriterion("vehicle_mileage =", value, "vehicleMileage");
            return (Criteria) this;
        }

        public Criteria andVehicleMileageNotEqualTo(BigDecimal value) {
            addCriterion("vehicle_mileage <>", value, "vehicleMileage");
            return (Criteria) this;
        }

        public Criteria andVehicleMileageGreaterThan(BigDecimal value) {
            addCriterion("vehicle_mileage >", value, "vehicleMileage");
            return (Criteria) this;
        }

        public Criteria andVehicleMileageGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("vehicle_mileage >=", value, "vehicleMileage");
            return (Criteria) this;
        }

        public Criteria andVehicleMileageLessThan(BigDecimal value) {
            addCriterion("vehicle_mileage <", value, "vehicleMileage");
            return (Criteria) this;
        }

        public Criteria andVehicleMileageLessThanOrEqualTo(BigDecimal value) {
            addCriterion("vehicle_mileage <=", value, "vehicleMileage");
            return (Criteria) this;
        }

        public Criteria andVehicleMileageIn(List<BigDecimal> values) {
            addCriterion("vehicle_mileage in", values, "vehicleMileage");
            return (Criteria) this;
        }

        public Criteria andVehicleMileageNotIn(List<BigDecimal> values) {
            addCriterion("vehicle_mileage not in", values, "vehicleMileage");
            return (Criteria) this;
        }

        public Criteria andVehicleMileageBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("vehicle_mileage between", value1, value2, "vehicleMileage");
            return (Criteria) this;
        }

        public Criteria andVehicleMileageNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("vehicle_mileage not between", value1, value2, "vehicleMileage");
            return (Criteria) this;
        }

        public Criteria andOccupyStaffIdIsNull() {
            addCriterion("occupy_staff_id is null");
            return (Criteria) this;
        }

        public Criteria andOccupyStaffIdIsNotNull() {
            addCriterion("occupy_staff_id is not null");
            return (Criteria) this;
        }

        public Criteria andOccupyStaffIdEqualTo(Integer value) {
            addCriterion("occupy_staff_id =", value, "occupyStaffId");
            return (Criteria) this;
        }

        public Criteria andOccupyStaffIdNotEqualTo(Integer value) {
            addCriterion("occupy_staff_id <>", value, "occupyStaffId");
            return (Criteria) this;
        }

        public Criteria andOccupyStaffIdGreaterThan(Integer value) {
            addCriterion("occupy_staff_id >", value, "occupyStaffId");
            return (Criteria) this;
        }

        public Criteria andOccupyStaffIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("occupy_staff_id >=", value, "occupyStaffId");
            return (Criteria) this;
        }

        public Criteria andOccupyStaffIdLessThan(Integer value) {
            addCriterion("occupy_staff_id <", value, "occupyStaffId");
            return (Criteria) this;
        }

        public Criteria andOccupyStaffIdLessThanOrEqualTo(Integer value) {
            addCriterion("occupy_staff_id <=", value, "occupyStaffId");
            return (Criteria) this;
        }

        public Criteria andOccupyStaffIdIn(List<Integer> values) {
            addCriterion("occupy_staff_id in", values, "occupyStaffId");
            return (Criteria) this;
        }

        public Criteria andOccupyStaffIdNotIn(List<Integer> values) {
            addCriterion("occupy_staff_id not in", values, "occupyStaffId");
            return (Criteria) this;
        }

        public Criteria andOccupyStaffIdBetween(Integer value1, Integer value2) {
            addCriterion("occupy_staff_id between", value1, value2, "occupyStaffId");
            return (Criteria) this;
        }

        public Criteria andOccupyStaffIdNotBetween(Integer value1, Integer value2) {
            addCriterion("occupy_staff_id not between", value1, value2, "occupyStaffId");
            return (Criteria) this;
        }

        public Criteria andOccupyStaffNameIsNull() {
            addCriterion("occupy_staff_name is null");
            return (Criteria) this;
        }

        public Criteria andOccupyStaffNameIsNotNull() {
            addCriterion("occupy_staff_name is not null");
            return (Criteria) this;
        }

        public Criteria andOccupyStaffNameEqualTo(String value) {
            addCriterion("occupy_staff_name =", value, "occupyStaffName");
            return (Criteria) this;
        }

        public Criteria andOccupyStaffNameNotEqualTo(String value) {
            addCriterion("occupy_staff_name <>", value, "occupyStaffName");
            return (Criteria) this;
        }

        public Criteria andOccupyStaffNameGreaterThan(String value) {
            addCriterion("occupy_staff_name >", value, "occupyStaffName");
            return (Criteria) this;
        }

        public Criteria andOccupyStaffNameGreaterThanOrEqualTo(String value) {
            addCriterion("occupy_staff_name >=", value, "occupyStaffName");
            return (Criteria) this;
        }

        public Criteria andOccupyStaffNameLessThan(String value) {
            addCriterion("occupy_staff_name <", value, "occupyStaffName");
            return (Criteria) this;
        }

        public Criteria andOccupyStaffNameLessThanOrEqualTo(String value) {
            addCriterion("occupy_staff_name <=", value, "occupyStaffName");
            return (Criteria) this;
        }

        public Criteria andOccupyStaffNameLike(String value) {
            addCriterion("occupy_staff_name like", value, "occupyStaffName");
            return (Criteria) this;
        }

        public Criteria andOccupyStaffNameNotLike(String value) {
            addCriterion("occupy_staff_name not like", value, "occupyStaffName");
            return (Criteria) this;
        }

        public Criteria andOccupyStaffNameIn(List<String> values) {
            addCriterion("occupy_staff_name in", values, "occupyStaffName");
            return (Criteria) this;
        }

        public Criteria andOccupyStaffNameNotIn(List<String> values) {
            addCriterion("occupy_staff_name not in", values, "occupyStaffName");
            return (Criteria) this;
        }

        public Criteria andOccupyStaffNameBetween(String value1, String value2) {
            addCriterion("occupy_staff_name between", value1, value2, "occupyStaffName");
            return (Criteria) this;
        }

        public Criteria andOccupyStaffNameNotBetween(String value1, String value2) {
            addCriterion("occupy_staff_name not between", value1, value2, "occupyStaffName");
            return (Criteria) this;
        }

        public Criteria andOccupyTimeIsNull() {
            addCriterion("occupy_time is null");
            return (Criteria) this;
        }

        public Criteria andOccupyTimeIsNotNull() {
            addCriterion("occupy_time is not null");
            return (Criteria) this;
        }

        public Criteria andOccupyTimeEqualTo(Date value) {
            addCriterion("occupy_time =", value, "occupyTime");
            return (Criteria) this;
        }

        public Criteria andOccupyTimeNotEqualTo(Date value) {
            addCriterion("occupy_time <>", value, "occupyTime");
            return (Criteria) this;
        }

        public Criteria andOccupyTimeGreaterThan(Date value) {
            addCriterion("occupy_time >", value, "occupyTime");
            return (Criteria) this;
        }

        public Criteria andOccupyTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("occupy_time >=", value, "occupyTime");
            return (Criteria) this;
        }

        public Criteria andOccupyTimeLessThan(Date value) {
            addCriterion("occupy_time <", value, "occupyTime");
            return (Criteria) this;
        }

        public Criteria andOccupyTimeLessThanOrEqualTo(Date value) {
            addCriterion("occupy_time <=", value, "occupyTime");
            return (Criteria) this;
        }

        public Criteria andOccupyTimeIn(List<Date> values) {
            addCriterion("occupy_time in", values, "occupyTime");
            return (Criteria) this;
        }

        public Criteria andOccupyTimeNotIn(List<Date> values) {
            addCriterion("occupy_time not in", values, "occupyTime");
            return (Criteria) this;
        }

        public Criteria andOccupyTimeBetween(Date value1, Date value2) {
            addCriterion("occupy_time between", value1, value2, "occupyTime");
            return (Criteria) this;
        }

        public Criteria andOccupyTimeNotBetween(Date value1, Date value2) {
            addCriterion("occupy_time not between", value1, value2, "occupyTime");
            return (Criteria) this;
        }

        public Criteria andVehicleUsageIsNull() {
            addCriterion("vehicle_usage is null");
            return (Criteria) this;
        }

        public Criteria andVehicleUsageIsNotNull() {
            addCriterion("vehicle_usage is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleUsageEqualTo(String value) {
            addCriterion("vehicle_usage =", value, "vehicleUsage");
            return (Criteria) this;
        }

        public Criteria andVehicleUsageNotEqualTo(String value) {
            addCriterion("vehicle_usage <>", value, "vehicleUsage");
            return (Criteria) this;
        }

        public Criteria andVehicleUsageGreaterThan(String value) {
            addCriterion("vehicle_usage >", value, "vehicleUsage");
            return (Criteria) this;
        }

        public Criteria andVehicleUsageGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_usage >=", value, "vehicleUsage");
            return (Criteria) this;
        }

        public Criteria andVehicleUsageLessThan(String value) {
            addCriterion("vehicle_usage <", value, "vehicleUsage");
            return (Criteria) this;
        }

        public Criteria andVehicleUsageLessThanOrEqualTo(String value) {
            addCriterion("vehicle_usage <=", value, "vehicleUsage");
            return (Criteria) this;
        }

        public Criteria andVehicleUsageLike(String value) {
            addCriterion("vehicle_usage like", value, "vehicleUsage");
            return (Criteria) this;
        }

        public Criteria andVehicleUsageNotLike(String value) {
            addCriterion("vehicle_usage not like", value, "vehicleUsage");
            return (Criteria) this;
        }

        public Criteria andVehicleUsageIn(List<String> values) {
            addCriterion("vehicle_usage in", values, "vehicleUsage");
            return (Criteria) this;
        }

        public Criteria andVehicleUsageNotIn(List<String> values) {
            addCriterion("vehicle_usage not in", values, "vehicleUsage");
            return (Criteria) this;
        }

        public Criteria andVehicleUsageBetween(String value1, String value2) {
            addCriterion("vehicle_usage between", value1, value2, "vehicleUsage");
            return (Criteria) this;
        }

        public Criteria andVehicleUsageNotBetween(String value1, String value2) {
            addCriterion("vehicle_usage not between", value1, value2, "vehicleUsage");
            return (Criteria) this;
        }

        public Criteria andVehicleIdNewSystemIsNull() {
            addCriterion("vehicle_id_new_system is null");
            return (Criteria) this;
        }

        public Criteria andVehicleIdNewSystemIsNotNull() {
            addCriterion("vehicle_id_new_system is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleIdNewSystemEqualTo(Integer value) {
            addCriterion("vehicle_id_new_system =", value, "vehicleIdNewSystem");
            return (Criteria) this;
        }

        public Criteria andVehicleIdNewSystemNotEqualTo(Integer value) {
            addCriterion("vehicle_id_new_system <>", value, "vehicleIdNewSystem");
            return (Criteria) this;
        }

        public Criteria andVehicleIdNewSystemGreaterThan(Integer value) {
            addCriterion("vehicle_id_new_system >", value, "vehicleIdNewSystem");
            return (Criteria) this;
        }

        public Criteria andVehicleIdNewSystemGreaterThanOrEqualTo(Integer value) {
            addCriterion("vehicle_id_new_system >=", value, "vehicleIdNewSystem");
            return (Criteria) this;
        }

        public Criteria andVehicleIdNewSystemLessThan(Integer value) {
            addCriterion("vehicle_id_new_system <", value, "vehicleIdNewSystem");
            return (Criteria) this;
        }

        public Criteria andVehicleIdNewSystemLessThanOrEqualTo(Integer value) {
            addCriterion("vehicle_id_new_system <=", value, "vehicleIdNewSystem");
            return (Criteria) this;
        }

        public Criteria andVehicleIdNewSystemIn(List<Integer> values) {
            addCriterion("vehicle_id_new_system in", values, "vehicleIdNewSystem");
            return (Criteria) this;
        }

        public Criteria andVehicleIdNewSystemNotIn(List<Integer> values) {
            addCriterion("vehicle_id_new_system not in", values, "vehicleIdNewSystem");
            return (Criteria) this;
        }

        public Criteria andVehicleIdNewSystemBetween(Integer value1, Integer value2) {
            addCriterion("vehicle_id_new_system between", value1, value2, "vehicleIdNewSystem");
            return (Criteria) this;
        }

        public Criteria andVehicleIdNewSystemNotBetween(Integer value1, Integer value2) {
            addCriterion("vehicle_id_new_system not between", value1, value2, "vehicleIdNewSystem");
            return (Criteria) this;
        }

        public Criteria andVehicleSerialNoNewSystemIsNull() {
            addCriterion("vehicle_serial_no_new_system is null");
            return (Criteria) this;
        }

        public Criteria andVehicleSerialNoNewSystemIsNotNull() {
            addCriterion("vehicle_serial_no_new_system is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleSerialNoNewSystemEqualTo(String value) {
            addCriterion("vehicle_serial_no_new_system =", value, "vehicleSerialNoNewSystem");
            return (Criteria) this;
        }

        public Criteria andVehicleSerialNoNewSystemNotEqualTo(String value) {
            addCriterion("vehicle_serial_no_new_system <>", value, "vehicleSerialNoNewSystem");
            return (Criteria) this;
        }

        public Criteria andVehicleSerialNoNewSystemGreaterThan(String value) {
            addCriterion("vehicle_serial_no_new_system >", value, "vehicleSerialNoNewSystem");
            return (Criteria) this;
        }

        public Criteria andVehicleSerialNoNewSystemGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_serial_no_new_system >=", value, "vehicleSerialNoNewSystem");
            return (Criteria) this;
        }

        public Criteria andVehicleSerialNoNewSystemLessThan(String value) {
            addCriterion("vehicle_serial_no_new_system <", value, "vehicleSerialNoNewSystem");
            return (Criteria) this;
        }

        public Criteria andVehicleSerialNoNewSystemLessThanOrEqualTo(String value) {
            addCriterion("vehicle_serial_no_new_system <=", value, "vehicleSerialNoNewSystem");
            return (Criteria) this;
        }

        public Criteria andVehicleSerialNoNewSystemLike(String value) {
            addCriterion("vehicle_serial_no_new_system like", value, "vehicleSerialNoNewSystem");
            return (Criteria) this;
        }

        public Criteria andVehicleSerialNoNewSystemNotLike(String value) {
            addCriterion("vehicle_serial_no_new_system not like", value, "vehicleSerialNoNewSystem");
            return (Criteria) this;
        }

        public Criteria andVehicleSerialNoNewSystemIn(List<String> values) {
            addCriterion("vehicle_serial_no_new_system in", values, "vehicleSerialNoNewSystem");
            return (Criteria) this;
        }

        public Criteria andVehicleSerialNoNewSystemNotIn(List<String> values) {
            addCriterion("vehicle_serial_no_new_system not in", values, "vehicleSerialNoNewSystem");
            return (Criteria) this;
        }

        public Criteria andVehicleSerialNoNewSystemBetween(String value1, String value2) {
            addCriterion("vehicle_serial_no_new_system between", value1, value2, "vehicleSerialNoNewSystem");
            return (Criteria) this;
        }

        public Criteria andVehicleSerialNoNewSystemNotBetween(String value1, String value2) {
            addCriterion("vehicle_serial_no_new_system not between", value1, value2, "vehicleSerialNoNewSystem");
            return (Criteria) this;
        }

        public Criteria andAssetCityCodeIsNull() {
            addCriterion("asset_city_code is null");
            return (Criteria) this;
        }

        public Criteria andAssetCityCodeIsNotNull() {
            addCriterion("asset_city_code is not null");
            return (Criteria) this;
        }

        public Criteria andAssetCityCodeEqualTo(String value) {
            addCriterion("asset_city_code =", value, "assetCityCode");
            return (Criteria) this;
        }

        public Criteria andAssetCityCodeNotEqualTo(String value) {
            addCriterion("asset_city_code <>", value, "assetCityCode");
            return (Criteria) this;
        }

        public Criteria andAssetCityCodeGreaterThan(String value) {
            addCriterion("asset_city_code >", value, "assetCityCode");
            return (Criteria) this;
        }

        public Criteria andAssetCityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("asset_city_code >=", value, "assetCityCode");
            return (Criteria) this;
        }

        public Criteria andAssetCityCodeLessThan(String value) {
            addCriterion("asset_city_code <", value, "assetCityCode");
            return (Criteria) this;
        }

        public Criteria andAssetCityCodeLessThanOrEqualTo(String value) {
            addCriterion("asset_city_code <=", value, "assetCityCode");
            return (Criteria) this;
        }

        public Criteria andAssetCityCodeLike(String value) {
            addCriterion("asset_city_code like", value, "assetCityCode");
            return (Criteria) this;
        }

        public Criteria andAssetCityCodeNotLike(String value) {
            addCriterion("asset_city_code not like", value, "assetCityCode");
            return (Criteria) this;
        }

        public Criteria andAssetCityCodeIn(List<String> values) {
            addCriterion("asset_city_code in", values, "assetCityCode");
            return (Criteria) this;
        }

        public Criteria andAssetCityCodeNotIn(List<String> values) {
            addCriterion("asset_city_code not in", values, "assetCityCode");
            return (Criteria) this;
        }

        public Criteria andAssetCityCodeBetween(String value1, String value2) {
            addCriterion("asset_city_code between", value1, value2, "assetCityCode");
            return (Criteria) this;
        }

        public Criteria andAssetCityCodeNotBetween(String value1, String value2) {
            addCriterion("asset_city_code not between", value1, value2, "assetCityCode");
            return (Criteria) this;
        }

        public Criteria andAssetCityNameIsNull() {
            addCriterion("asset_city_name is null");
            return (Criteria) this;
        }

        public Criteria andAssetCityNameIsNotNull() {
            addCriterion("asset_city_name is not null");
            return (Criteria) this;
        }

        public Criteria andAssetCityNameEqualTo(String value) {
            addCriterion("asset_city_name =", value, "assetCityName");
            return (Criteria) this;
        }

        public Criteria andAssetCityNameNotEqualTo(String value) {
            addCriterion("asset_city_name <>", value, "assetCityName");
            return (Criteria) this;
        }

        public Criteria andAssetCityNameGreaterThan(String value) {
            addCriterion("asset_city_name >", value, "assetCityName");
            return (Criteria) this;
        }

        public Criteria andAssetCityNameGreaterThanOrEqualTo(String value) {
            addCriterion("asset_city_name >=", value, "assetCityName");
            return (Criteria) this;
        }

        public Criteria andAssetCityNameLessThan(String value) {
            addCriterion("asset_city_name <", value, "assetCityName");
            return (Criteria) this;
        }

        public Criteria andAssetCityNameLessThanOrEqualTo(String value) {
            addCriterion("asset_city_name <=", value, "assetCityName");
            return (Criteria) this;
        }

        public Criteria andAssetCityNameLike(String value) {
            addCriterion("asset_city_name like", value, "assetCityName");
            return (Criteria) this;
        }

        public Criteria andAssetCityNameNotLike(String value) {
            addCriterion("asset_city_name not like", value, "assetCityName");
            return (Criteria) this;
        }

        public Criteria andAssetCityNameIn(List<String> values) {
            addCriterion("asset_city_name in", values, "assetCityName");
            return (Criteria) this;
        }

        public Criteria andAssetCityNameNotIn(List<String> values) {
            addCriterion("asset_city_name not in", values, "assetCityName");
            return (Criteria) this;
        }

        public Criteria andAssetCityNameBetween(String value1, String value2) {
            addCriterion("asset_city_name between", value1, value2, "assetCityName");
            return (Criteria) this;
        }

        public Criteria andAssetCityNameNotBetween(String value1, String value2) {
            addCriterion("asset_city_name not between", value1, value2, "assetCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleStatusLevelOneIsNull() {
            addCriterion("vehicle_status_level_one is null");
            return (Criteria) this;
        }

        public Criteria andVehicleStatusLevelOneIsNotNull() {
            addCriterion("vehicle_status_level_one is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleStatusLevelOneEqualTo(Byte value) {
            addCriterion("vehicle_status_level_one =", value, "vehicleStatusLevelOne");
            return (Criteria) this;
        }

        public Criteria andVehicleStatusLevelOneNotEqualTo(Byte value) {
            addCriterion("vehicle_status_level_one <>", value, "vehicleStatusLevelOne");
            return (Criteria) this;
        }

        public Criteria andVehicleStatusLevelOneGreaterThan(Byte value) {
            addCriterion("vehicle_status_level_one >", value, "vehicleStatusLevelOne");
            return (Criteria) this;
        }

        public Criteria andVehicleStatusLevelOneGreaterThanOrEqualTo(Byte value) {
            addCriterion("vehicle_status_level_one >=", value, "vehicleStatusLevelOne");
            return (Criteria) this;
        }

        public Criteria andVehicleStatusLevelOneLessThan(Byte value) {
            addCriterion("vehicle_status_level_one <", value, "vehicleStatusLevelOne");
            return (Criteria) this;
        }

        public Criteria andVehicleStatusLevelOneLessThanOrEqualTo(Byte value) {
            addCriterion("vehicle_status_level_one <=", value, "vehicleStatusLevelOne");
            return (Criteria) this;
        }

        public Criteria andVehicleStatusLevelOneIn(List<Byte> values) {
            addCriterion("vehicle_status_level_one in", values, "vehicleStatusLevelOne");
            return (Criteria) this;
        }

        public Criteria andVehicleStatusLevelOneNotIn(List<Byte> values) {
            addCriterion("vehicle_status_level_one not in", values, "vehicleStatusLevelOne");
            return (Criteria) this;
        }

        public Criteria andVehicleStatusLevelOneBetween(Byte value1, Byte value2) {
            addCriterion("vehicle_status_level_one between", value1, value2, "vehicleStatusLevelOne");
            return (Criteria) this;
        }

        public Criteria andVehicleStatusLevelOneNotBetween(Byte value1, Byte value2) {
            addCriterion("vehicle_status_level_one not between", value1, value2, "vehicleStatusLevelOne");
            return (Criteria) this;
        }

        public Criteria andVehicleStatusLevelTwoIsNull() {
            addCriterion("vehicle_status_level_two is null");
            return (Criteria) this;
        }

        public Criteria andVehicleStatusLevelTwoIsNotNull() {
            addCriterion("vehicle_status_level_two is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleStatusLevelTwoEqualTo(Byte value) {
            addCriterion("vehicle_status_level_two =", value, "vehicleStatusLevelTwo");
            return (Criteria) this;
        }

        public Criteria andVehicleStatusLevelTwoNotEqualTo(Byte value) {
            addCriterion("vehicle_status_level_two <>", value, "vehicleStatusLevelTwo");
            return (Criteria) this;
        }

        public Criteria andVehicleStatusLevelTwoGreaterThan(Byte value) {
            addCriterion("vehicle_status_level_two >", value, "vehicleStatusLevelTwo");
            return (Criteria) this;
        }

        public Criteria andVehicleStatusLevelTwoGreaterThanOrEqualTo(Byte value) {
            addCriterion("vehicle_status_level_two >=", value, "vehicleStatusLevelTwo");
            return (Criteria) this;
        }

        public Criteria andVehicleStatusLevelTwoLessThan(Byte value) {
            addCriterion("vehicle_status_level_two <", value, "vehicleStatusLevelTwo");
            return (Criteria) this;
        }

        public Criteria andVehicleStatusLevelTwoLessThanOrEqualTo(Byte value) {
            addCriterion("vehicle_status_level_two <=", value, "vehicleStatusLevelTwo");
            return (Criteria) this;
        }

        public Criteria andVehicleStatusLevelTwoIn(List<Byte> values) {
            addCriterion("vehicle_status_level_two in", values, "vehicleStatusLevelTwo");
            return (Criteria) this;
        }

        public Criteria andVehicleStatusLevelTwoNotIn(List<Byte> values) {
            addCriterion("vehicle_status_level_two not in", values, "vehicleStatusLevelTwo");
            return (Criteria) this;
        }

        public Criteria andVehicleStatusLevelTwoBetween(Byte value1, Byte value2) {
            addCriterion("vehicle_status_level_two between", value1, value2, "vehicleStatusLevelTwo");
            return (Criteria) this;
        }

        public Criteria andVehicleStatusLevelTwoNotBetween(Byte value1, Byte value2) {
            addCriterion("vehicle_status_level_two not between", value1, value2, "vehicleStatusLevelTwo");
            return (Criteria) this;
        }

        public Criteria andLeaseLicenseBelongOrgNameIsNull() {
            addCriterion("lease_license_belong_org_name is null");
            return (Criteria) this;
        }

        public Criteria andLeaseLicenseBelongOrgNameIsNotNull() {
            addCriterion("lease_license_belong_org_name is not null");
            return (Criteria) this;
        }

        public Criteria andLeaseLicenseBelongOrgNameEqualTo(String value) {
            addCriterion("lease_license_belong_org_name =", value, "leaseLicenseBelongOrgName");
            return (Criteria) this;
        }

        public Criteria andLeaseLicenseBelongOrgNameNotEqualTo(String value) {
            addCriterion("lease_license_belong_org_name <>", value, "leaseLicenseBelongOrgName");
            return (Criteria) this;
        }

        public Criteria andLeaseLicenseBelongOrgNameGreaterThan(String value) {
            addCriterion("lease_license_belong_org_name >", value, "leaseLicenseBelongOrgName");
            return (Criteria) this;
        }

        public Criteria andLeaseLicenseBelongOrgNameGreaterThanOrEqualTo(String value) {
            addCriterion("lease_license_belong_org_name >=", value, "leaseLicenseBelongOrgName");
            return (Criteria) this;
        }

        public Criteria andLeaseLicenseBelongOrgNameLessThan(String value) {
            addCriterion("lease_license_belong_org_name <", value, "leaseLicenseBelongOrgName");
            return (Criteria) this;
        }

        public Criteria andLeaseLicenseBelongOrgNameLessThanOrEqualTo(String value) {
            addCriterion("lease_license_belong_org_name <=", value, "leaseLicenseBelongOrgName");
            return (Criteria) this;
        }

        public Criteria andLeaseLicenseBelongOrgNameLike(String value) {
            addCriterion("lease_license_belong_org_name like", value, "leaseLicenseBelongOrgName");
            return (Criteria) this;
        }

        public Criteria andLeaseLicenseBelongOrgNameNotLike(String value) {
            addCriterion("lease_license_belong_org_name not like", value, "leaseLicenseBelongOrgName");
            return (Criteria) this;
        }

        public Criteria andLeaseLicenseBelongOrgNameIn(List<String> values) {
            addCriterion("lease_license_belong_org_name in", values, "leaseLicenseBelongOrgName");
            return (Criteria) this;
        }

        public Criteria andLeaseLicenseBelongOrgNameNotIn(List<String> values) {
            addCriterion("lease_license_belong_org_name not in", values, "leaseLicenseBelongOrgName");
            return (Criteria) this;
        }

        public Criteria andLeaseLicenseBelongOrgNameBetween(String value1, String value2) {
            addCriterion("lease_license_belong_org_name between", value1, value2, "leaseLicenseBelongOrgName");
            return (Criteria) this;
        }

        public Criteria andLeaseLicenseBelongOrgNameNotBetween(String value1, String value2) {
            addCriterion("lease_license_belong_org_name not between", value1, value2, "leaseLicenseBelongOrgName");
            return (Criteria) this;
        }

        public Criteria andBelongBussCodeIsNull() {
            addCriterion("belong_buss_code is null");
            return (Criteria) this;
        }

        public Criteria andBelongBussCodeIsNotNull() {
            addCriterion("belong_buss_code is not null");
            return (Criteria) this;
        }

        public Criteria andBelongBussCodeEqualTo(String value) {
            addCriterion("belong_buss_code =", value, "belongBussCode");
            return (Criteria) this;
        }

        public Criteria andBelongBussCodeNotEqualTo(String value) {
            addCriterion("belong_buss_code <>", value, "belongBussCode");
            return (Criteria) this;
        }

        public Criteria andBelongBussCodeGreaterThan(String value) {
            addCriterion("belong_buss_code >", value, "belongBussCode");
            return (Criteria) this;
        }

        public Criteria andBelongBussCodeGreaterThanOrEqualTo(String value) {
            addCriterion("belong_buss_code >=", value, "belongBussCode");
            return (Criteria) this;
        }

        public Criteria andBelongBussCodeLessThan(String value) {
            addCriterion("belong_buss_code <", value, "belongBussCode");
            return (Criteria) this;
        }

        public Criteria andBelongBussCodeLessThanOrEqualTo(String value) {
            addCriterion("belong_buss_code <=", value, "belongBussCode");
            return (Criteria) this;
        }

        public Criteria andBelongBussCodeLike(String value) {
            addCriterion("belong_buss_code like", value, "belongBussCode");
            return (Criteria) this;
        }

        public Criteria andBelongBussCodeNotLike(String value) {
            addCriterion("belong_buss_code not like", value, "belongBussCode");
            return (Criteria) this;
        }

        public Criteria andBelongBussCodeIn(List<String> values) {
            addCriterion("belong_buss_code in", values, "belongBussCode");
            return (Criteria) this;
        }

        public Criteria andBelongBussCodeNotIn(List<String> values) {
            addCriterion("belong_buss_code not in", values, "belongBussCode");
            return (Criteria) this;
        }

        public Criteria andBelongBussCodeBetween(String value1, String value2) {
            addCriterion("belong_buss_code between", value1, value2, "belongBussCode");
            return (Criteria) this;
        }

        public Criteria andBelongBussCodeNotBetween(String value1, String value2) {
            addCriterion("belong_buss_code not between", value1, value2, "belongBussCode");
            return (Criteria) this;
        }

        public Criteria andBelongBussNameIsNull() {
            addCriterion("belong_buss_name is null");
            return (Criteria) this;
        }

        public Criteria andBelongBussNameIsNotNull() {
            addCriterion("belong_buss_name is not null");
            return (Criteria) this;
        }

        public Criteria andBelongBussNameEqualTo(String value) {
            addCriterion("belong_buss_name =", value, "belongBussName");
            return (Criteria) this;
        }

        public Criteria andBelongBussNameNotEqualTo(String value) {
            addCriterion("belong_buss_name <>", value, "belongBussName");
            return (Criteria) this;
        }

        public Criteria andBelongBussNameGreaterThan(String value) {
            addCriterion("belong_buss_name >", value, "belongBussName");
            return (Criteria) this;
        }

        public Criteria andBelongBussNameGreaterThanOrEqualTo(String value) {
            addCriterion("belong_buss_name >=", value, "belongBussName");
            return (Criteria) this;
        }

        public Criteria andBelongBussNameLessThan(String value) {
            addCriterion("belong_buss_name <", value, "belongBussName");
            return (Criteria) this;
        }

        public Criteria andBelongBussNameLessThanOrEqualTo(String value) {
            addCriterion("belong_buss_name <=", value, "belongBussName");
            return (Criteria) this;
        }

        public Criteria andBelongBussNameLike(String value) {
            addCriterion("belong_buss_name like", value, "belongBussName");
            return (Criteria) this;
        }

        public Criteria andBelongBussNameNotLike(String value) {
            addCriterion("belong_buss_name not like", value, "belongBussName");
            return (Criteria) this;
        }

        public Criteria andBelongBussNameIn(List<String> values) {
            addCriterion("belong_buss_name in", values, "belongBussName");
            return (Criteria) this;
        }

        public Criteria andBelongBussNameNotIn(List<String> values) {
            addCriterion("belong_buss_name not in", values, "belongBussName");
            return (Criteria) this;
        }

        public Criteria andBelongBussNameBetween(String value1, String value2) {
            addCriterion("belong_buss_name between", value1, value2, "belongBussName");
            return (Criteria) this;
        }

        public Criteria andBelongBussNameNotBetween(String value1, String value2) {
            addCriterion("belong_buss_name not between", value1, value2, "belongBussName");
            return (Criteria) this;
        }

        public Criteria andMobileIsNull() {
            addCriterion("mobile is null");
            return (Criteria) this;
        }

        public Criteria andMobileIsNotNull() {
            addCriterion("mobile is not null");
            return (Criteria) this;
        }

        public Criteria andMobileEqualTo(String value) {
            addCriterion("mobile =", value, "mobile");
            return (Criteria) this;
        }

        public Criteria andMobileNotEqualTo(String value) {
            addCriterion("mobile <>", value, "mobile");
            return (Criteria) this;
        }

        public Criteria andMobileGreaterThan(String value) {
            addCriterion("mobile >", value, "mobile");
            return (Criteria) this;
        }

        public Criteria andMobileGreaterThanOrEqualTo(String value) {
            addCriterion("mobile >=", value, "mobile");
            return (Criteria) this;
        }

        public Criteria andMobileLessThan(String value) {
            addCriterion("mobile <", value, "mobile");
            return (Criteria) this;
        }

        public Criteria andMobileLessThanOrEqualTo(String value) {
            addCriterion("mobile <=", value, "mobile");
            return (Criteria) this;
        }

        public Criteria andMobileLike(String value) {
            addCriterion("mobile like", value, "mobile");
            return (Criteria) this;
        }

        public Criteria andMobileNotLike(String value) {
            addCriterion("mobile not like", value, "mobile");
            return (Criteria) this;
        }

        public Criteria andMobileIn(List<String> values) {
            addCriterion("mobile in", values, "mobile");
            return (Criteria) this;
        }

        public Criteria andMobileNotIn(List<String> values) {
            addCriterion("mobile not in", values, "mobile");
            return (Criteria) this;
        }

        public Criteria andMobileBetween(String value1, String value2) {
            addCriterion("mobile between", value1, value2, "mobile");
            return (Criteria) this;
        }

        public Criteria andMobileNotBetween(String value1, String value2) {
            addCriterion("mobile not between", value1, value2, "mobile");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}