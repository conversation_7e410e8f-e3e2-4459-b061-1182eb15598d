package com.izu.order.entity.mrcar;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class GovPublicCarOrderExample {
    /**
     * gov_public_car_order
     */
    protected String orderByClause;

    /**
     * gov_public_car_order
     */
    protected boolean distinct;

    /**
     * gov_public_car_order
     */
    protected List<Criteria> oredCriteria;

    /**
     *
     * @mbg.generated
     */
    public GovPublicCarOrderExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * gov_public_car_order null
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("order_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("order_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(Long value) {
            addCriterion("order_id =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(Long value) {
            addCriterion("order_id <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(Long value) {
            addCriterion("order_id >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(Long value) {
            addCriterion("order_id >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(Long value) {
            addCriterion("order_id <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(Long value) {
            addCriterion("order_id <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<Long> values) {
            addCriterion("order_id in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<Long> values) {
            addCriterion("order_id not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(Long value1, Long value2) {
            addCriterion("order_id between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(Long value1, Long value2) {
            addCriterion("order_id not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNull() {
            addCriterion("order_no is null");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNotNull() {
            addCriterion("order_no is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNoEqualTo(String value) {
            addCriterion("order_no =", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotEqualTo(String value) {
            addCriterion("order_no <>", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThan(String value) {
            addCriterion("order_no >", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThanOrEqualTo(String value) {
            addCriterion("order_no >=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThan(String value) {
            addCriterion("order_no <", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThanOrEqualTo(String value) {
            addCriterion("order_no <=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLike(String value) {
            addCriterion("order_no like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotLike(String value) {
            addCriterion("order_no not like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoIn(List<String> values) {
            addCriterion("order_no in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotIn(List<String> values) {
            addCriterion("order_no not in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoBetween(String value1, String value2) {
            addCriterion("order_no between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotBetween(String value1, String value2) {
            addCriterion("order_no not between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIsNull() {
            addCriterion("order_type is null");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIsNotNull() {
            addCriterion("order_type is not null");
            return (Criteria) this;
        }

        public Criteria andOrderTypeEqualTo(Integer value) {
            addCriterion("order_type =", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotEqualTo(Integer value) {
            addCriterion("order_type <>", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeGreaterThan(Integer value) {
            addCriterion("order_type >", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_type >=", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeLessThan(Integer value) {
            addCriterion("order_type <", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeLessThanOrEqualTo(Integer value) {
            addCriterion("order_type <=", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIn(List<Integer> values) {
            addCriterion("order_type in", values, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotIn(List<Integer> values) {
            addCriterion("order_type not in", values, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeBetween(Integer value1, Integer value2) {
            addCriterion("order_type between", value1, value2, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("order_type not between", value1, value2, "orderType");
            return (Criteria) this;
        }

        public Criteria andVehicleIdIsNull() {
            addCriterion("vehicle_id is null");
            return (Criteria) this;
        }

        public Criteria andVehicleIdIsNotNull() {
            addCriterion("vehicle_id is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleIdEqualTo(Integer value) {
            addCriterion("vehicle_id =", value, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdNotEqualTo(Integer value) {
            addCriterion("vehicle_id <>", value, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdGreaterThan(Integer value) {
            addCriterion("vehicle_id >", value, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("vehicle_id >=", value, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdLessThan(Integer value) {
            addCriterion("vehicle_id <", value, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdLessThanOrEqualTo(Integer value) {
            addCriterion("vehicle_id <=", value, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdIn(List<Integer> values) {
            addCriterion("vehicle_id in", values, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdNotIn(List<Integer> values) {
            addCriterion("vehicle_id not in", values, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdBetween(Integer value1, Integer value2) {
            addCriterion("vehicle_id between", value1, value2, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdNotBetween(Integer value1, Integer value2) {
            addCriterion("vehicle_id not between", value1, value2, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseIsNull() {
            addCriterion("vehicle_license is null");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseIsNotNull() {
            addCriterion("vehicle_license is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseEqualTo(String value) {
            addCriterion("vehicle_license =", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseNotEqualTo(String value) {
            addCriterion("vehicle_license <>", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseGreaterThan(String value) {
            addCriterion("vehicle_license >", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_license >=", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseLessThan(String value) {
            addCriterion("vehicle_license <", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseLessThanOrEqualTo(String value) {
            addCriterion("vehicle_license <=", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseLike(String value) {
            addCriterion("vehicle_license like", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseNotLike(String value) {
            addCriterion("vehicle_license not like", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseIn(List<String> values) {
            addCriterion("vehicle_license in", values, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseNotIn(List<String> values) {
            addCriterion("vehicle_license not in", values, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseBetween(String value1, String value2) {
            addCriterion("vehicle_license between", value1, value2, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseNotBetween(String value1, String value2) {
            addCriterion("vehicle_license not between", value1, value2, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleVinIsNull() {
            addCriterion("vehicle_vin is null");
            return (Criteria) this;
        }

        public Criteria andVehicleVinIsNotNull() {
            addCriterion("vehicle_vin is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleVinEqualTo(String value) {
            addCriterion("vehicle_vin =", value, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinNotEqualTo(String value) {
            addCriterion("vehicle_vin <>", value, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinGreaterThan(String value) {
            addCriterion("vehicle_vin >", value, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_vin >=", value, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinLessThan(String value) {
            addCriterion("vehicle_vin <", value, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinLessThanOrEqualTo(String value) {
            addCriterion("vehicle_vin <=", value, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinLike(String value) {
            addCriterion("vehicle_vin like", value, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinNotLike(String value) {
            addCriterion("vehicle_vin not like", value, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinIn(List<String> values) {
            addCriterion("vehicle_vin in", values, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinNotIn(List<String> values) {
            addCriterion("vehicle_vin not in", values, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinBetween(String value1, String value2) {
            addCriterion("vehicle_vin between", value1, value2, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinNotBetween(String value1, String value2) {
            addCriterion("vehicle_vin not between", value1, value2, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andOrderStructIdIsNull() {
            addCriterion("order_struct_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderStructIdIsNotNull() {
            addCriterion("order_struct_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderStructIdEqualTo(Integer value) {
            addCriterion("order_struct_id =", value, "orderStructId");
            return (Criteria) this;
        }

        public Criteria andOrderStructIdNotEqualTo(Integer value) {
            addCriterion("order_struct_id <>", value, "orderStructId");
            return (Criteria) this;
        }

        public Criteria andOrderStructIdGreaterThan(Integer value) {
            addCriterion("order_struct_id >", value, "orderStructId");
            return (Criteria) this;
        }

        public Criteria andOrderStructIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_struct_id >=", value, "orderStructId");
            return (Criteria) this;
        }

        public Criteria andOrderStructIdLessThan(Integer value) {
            addCriterion("order_struct_id <", value, "orderStructId");
            return (Criteria) this;
        }

        public Criteria andOrderStructIdLessThanOrEqualTo(Integer value) {
            addCriterion("order_struct_id <=", value, "orderStructId");
            return (Criteria) this;
        }

        public Criteria andOrderStructIdIn(List<Integer> values) {
            addCriterion("order_struct_id in", values, "orderStructId");
            return (Criteria) this;
        }

        public Criteria andOrderStructIdNotIn(List<Integer> values) {
            addCriterion("order_struct_id not in", values, "orderStructId");
            return (Criteria) this;
        }

        public Criteria andOrderStructIdBetween(Integer value1, Integer value2) {
            addCriterion("order_struct_id between", value1, value2, "orderStructId");
            return (Criteria) this;
        }

        public Criteria andOrderStructIdNotBetween(Integer value1, Integer value2) {
            addCriterion("order_struct_id not between", value1, value2, "orderStructId");
            return (Criteria) this;
        }

        public Criteria andOrderStructCodeIsNull() {
            addCriterion("order_struct_code is null");
            return (Criteria) this;
        }

        public Criteria andOrderStructCodeIsNotNull() {
            addCriterion("order_struct_code is not null");
            return (Criteria) this;
        }

        public Criteria andOrderStructCodeEqualTo(String value) {
            addCriterion("order_struct_code =", value, "orderStructCode");
            return (Criteria) this;
        }

        public Criteria andOrderStructCodeNotEqualTo(String value) {
            addCriterion("order_struct_code <>", value, "orderStructCode");
            return (Criteria) this;
        }

        public Criteria andOrderStructCodeGreaterThan(String value) {
            addCriterion("order_struct_code >", value, "orderStructCode");
            return (Criteria) this;
        }

        public Criteria andOrderStructCodeGreaterThanOrEqualTo(String value) {
            addCriterion("order_struct_code >=", value, "orderStructCode");
            return (Criteria) this;
        }

        public Criteria andOrderStructCodeLessThan(String value) {
            addCriterion("order_struct_code <", value, "orderStructCode");
            return (Criteria) this;
        }

        public Criteria andOrderStructCodeLessThanOrEqualTo(String value) {
            addCriterion("order_struct_code <=", value, "orderStructCode");
            return (Criteria) this;
        }

        public Criteria andOrderStructCodeLike(String value) {
            addCriterion("order_struct_code like", value, "orderStructCode");
            return (Criteria) this;
        }

        public Criteria andOrderStructCodeNotLike(String value) {
            addCriterion("order_struct_code not like", value, "orderStructCode");
            return (Criteria) this;
        }

        public Criteria andOrderStructCodeIn(List<String> values) {
            addCriterion("order_struct_code in", values, "orderStructCode");
            return (Criteria) this;
        }

        public Criteria andOrderStructCodeNotIn(List<String> values) {
            addCriterion("order_struct_code not in", values, "orderStructCode");
            return (Criteria) this;
        }

        public Criteria andOrderStructCodeBetween(String value1, String value2) {
            addCriterion("order_struct_code between", value1, value2, "orderStructCode");
            return (Criteria) this;
        }

        public Criteria andOrderStructCodeNotBetween(String value1, String value2) {
            addCriterion("order_struct_code not between", value1, value2, "orderStructCode");
            return (Criteria) this;
        }

        public Criteria andOrderStructNameIsNull() {
            addCriterion("order_struct_name is null");
            return (Criteria) this;
        }

        public Criteria andOrderStructNameIsNotNull() {
            addCriterion("order_struct_name is not null");
            return (Criteria) this;
        }

        public Criteria andOrderStructNameEqualTo(String value) {
            addCriterion("order_struct_name =", value, "orderStructName");
            return (Criteria) this;
        }

        public Criteria andOrderStructNameNotEqualTo(String value) {
            addCriterion("order_struct_name <>", value, "orderStructName");
            return (Criteria) this;
        }

        public Criteria andOrderStructNameGreaterThan(String value) {
            addCriterion("order_struct_name >", value, "orderStructName");
            return (Criteria) this;
        }

        public Criteria andOrderStructNameGreaterThanOrEqualTo(String value) {
            addCriterion("order_struct_name >=", value, "orderStructName");
            return (Criteria) this;
        }

        public Criteria andOrderStructNameLessThan(String value) {
            addCriterion("order_struct_name <", value, "orderStructName");
            return (Criteria) this;
        }

        public Criteria andOrderStructNameLessThanOrEqualTo(String value) {
            addCriterion("order_struct_name <=", value, "orderStructName");
            return (Criteria) this;
        }

        public Criteria andOrderStructNameLike(String value) {
            addCriterion("order_struct_name like", value, "orderStructName");
            return (Criteria) this;
        }

        public Criteria andOrderStructNameNotLike(String value) {
            addCriterion("order_struct_name not like", value, "orderStructName");
            return (Criteria) this;
        }

        public Criteria andOrderStructNameIn(List<String> values) {
            addCriterion("order_struct_name in", values, "orderStructName");
            return (Criteria) this;
        }

        public Criteria andOrderStructNameNotIn(List<String> values) {
            addCriterion("order_struct_name not in", values, "orderStructName");
            return (Criteria) this;
        }

        public Criteria andOrderStructNameBetween(String value1, String value2) {
            addCriterion("order_struct_name between", value1, value2, "orderStructName");
            return (Criteria) this;
        }

        public Criteria andOrderStructNameNotBetween(String value1, String value2) {
            addCriterion("order_struct_name not between", value1, value2, "orderStructName");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIsNull() {
            addCriterion("order_status is null");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIsNotNull() {
            addCriterion("order_status is not null");
            return (Criteria) this;
        }

        public Criteria andOrderStatusEqualTo(Byte value) {
            addCriterion("order_status =", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotEqualTo(Byte value) {
            addCriterion("order_status <>", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusGreaterThan(Byte value) {
            addCriterion("order_status >", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("order_status >=", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusLessThan(Byte value) {
            addCriterion("order_status <", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusLessThanOrEqualTo(Byte value) {
            addCriterion("order_status <=", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIn(List<Byte> values) {
            addCriterion("order_status in", values, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotIn(List<Byte> values) {
            addCriterion("order_status not in", values, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusBetween(Byte value1, Byte value2) {
            addCriterion("order_status between", value1, value2, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("order_status not between", value1, value2, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andApprovalStatusIsNull() {
            addCriterion("approval_status is null");
            return (Criteria) this;
        }

        public Criteria andApprovalStatusIsNotNull() {
            addCriterion("approval_status is not null");
            return (Criteria) this;
        }

        public Criteria andApprovalStatusEqualTo(Byte value) {
            addCriterion("approval_status =", value, "approvalStatus");
            return (Criteria) this;
        }

        public Criteria andApprovalStatusNotEqualTo(Byte value) {
            addCriterion("approval_status <>", value, "approvalStatus");
            return (Criteria) this;
        }

        public Criteria andApprovalStatusGreaterThan(Byte value) {
            addCriterion("approval_status >", value, "approvalStatus");
            return (Criteria) this;
        }

        public Criteria andApprovalStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("approval_status >=", value, "approvalStatus");
            return (Criteria) this;
        }

        public Criteria andApprovalStatusLessThan(Byte value) {
            addCriterion("approval_status <", value, "approvalStatus");
            return (Criteria) this;
        }

        public Criteria andApprovalStatusLessThanOrEqualTo(Byte value) {
            addCriterion("approval_status <=", value, "approvalStatus");
            return (Criteria) this;
        }

        public Criteria andApprovalStatusIn(List<Byte> values) {
            addCriterion("approval_status in", values, "approvalStatus");
            return (Criteria) this;
        }

        public Criteria andApprovalStatusNotIn(List<Byte> values) {
            addCriterion("approval_status not in", values, "approvalStatus");
            return (Criteria) this;
        }

        public Criteria andApprovalStatusBetween(Byte value1, Byte value2) {
            addCriterion("approval_status between", value1, value2, "approvalStatus");
            return (Criteria) this;
        }

        public Criteria andApprovalStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("approval_status not between", value1, value2, "approvalStatus");
            return (Criteria) this;
        }

        public Criteria andApprovalIdIsNull() {
            addCriterion("approval_id is null");
            return (Criteria) this;
        }

        public Criteria andApprovalIdIsNotNull() {
            addCriterion("approval_id is not null");
            return (Criteria) this;
        }

        public Criteria andApprovalIdEqualTo(String value) {
            addCriterion("approval_id =", value, "approvalId");
            return (Criteria) this;
        }

        public Criteria andApprovalIdNotEqualTo(String value) {
            addCriterion("approval_id <>", value, "approvalId");
            return (Criteria) this;
        }

        public Criteria andApprovalIdGreaterThan(String value) {
            addCriterion("approval_id >", value, "approvalId");
            return (Criteria) this;
        }

        public Criteria andApprovalIdGreaterThanOrEqualTo(String value) {
            addCriterion("approval_id >=", value, "approvalId");
            return (Criteria) this;
        }

        public Criteria andApprovalIdLessThan(String value) {
            addCriterion("approval_id <", value, "approvalId");
            return (Criteria) this;
        }

        public Criteria andApprovalIdLessThanOrEqualTo(String value) {
            addCriterion("approval_id <=", value, "approvalId");
            return (Criteria) this;
        }

        public Criteria andApprovalIdLike(String value) {
            addCriterion("approval_id like", value, "approvalId");
            return (Criteria) this;
        }

        public Criteria andApprovalIdNotLike(String value) {
            addCriterion("approval_id not like", value, "approvalId");
            return (Criteria) this;
        }

        public Criteria andApprovalIdIn(List<String> values) {
            addCriterion("approval_id in", values, "approvalId");
            return (Criteria) this;
        }

        public Criteria andApprovalIdNotIn(List<String> values) {
            addCriterion("approval_id not in", values, "approvalId");
            return (Criteria) this;
        }

        public Criteria andApprovalIdBetween(String value1, String value2) {
            addCriterion("approval_id between", value1, value2, "approvalId");
            return (Criteria) this;
        }

        public Criteria andApprovalIdNotBetween(String value1, String value2) {
            addCriterion("approval_id not between", value1, value2, "approvalId");
            return (Criteria) this;
        }

        public Criteria andApprovalCompletedTimeIsNull() {
            addCriterion("approval_completed_time is null");
            return (Criteria) this;
        }

        public Criteria andApprovalCompletedTimeIsNotNull() {
            addCriterion("approval_completed_time is not null");
            return (Criteria) this;
        }

        public Criteria andApprovalCompletedTimeEqualTo(Date value) {
            addCriterion("approval_completed_time =", value, "approvalCompletedTime");
            return (Criteria) this;
        }

        public Criteria andApprovalCompletedTimeNotEqualTo(Date value) {
            addCriterion("approval_completed_time <>", value, "approvalCompletedTime");
            return (Criteria) this;
        }

        public Criteria andApprovalCompletedTimeGreaterThan(Date value) {
            addCriterion("approval_completed_time >", value, "approvalCompletedTime");
            return (Criteria) this;
        }

        public Criteria andApprovalCompletedTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("approval_completed_time >=", value, "approvalCompletedTime");
            return (Criteria) this;
        }

        public Criteria andApprovalCompletedTimeLessThan(Date value) {
            addCriterion("approval_completed_time <", value, "approvalCompletedTime");
            return (Criteria) this;
        }

        public Criteria andApprovalCompletedTimeLessThanOrEqualTo(Date value) {
            addCriterion("approval_completed_time <=", value, "approvalCompletedTime");
            return (Criteria) this;
        }

        public Criteria andApprovalCompletedTimeIn(List<Date> values) {
            addCriterion("approval_completed_time in", values, "approvalCompletedTime");
            return (Criteria) this;
        }

        public Criteria andApprovalCompletedTimeNotIn(List<Date> values) {
            addCriterion("approval_completed_time not in", values, "approvalCompletedTime");
            return (Criteria) this;
        }

        public Criteria andApprovalCompletedTimeBetween(Date value1, Date value2) {
            addCriterion("approval_completed_time between", value1, value2, "approvalCompletedTime");
            return (Criteria) this;
        }

        public Criteria andApprovalCompletedTimeNotBetween(Date value1, Date value2) {
            addCriterion("approval_completed_time not between", value1, value2, "approvalCompletedTime");
            return (Criteria) this;
        }

        public Criteria andApprovalWithdrawalTypeIsNull() {
            addCriterion("approval_withdrawal_type is null");
            return (Criteria) this;
        }

        public Criteria andApprovalWithdrawalTypeIsNotNull() {
            addCriterion("approval_withdrawal_type is not null");
            return (Criteria) this;
        }

        public Criteria andApprovalWithdrawalTypeEqualTo(Byte value) {
            addCriterion("approval_withdrawal_type =", value, "approvalWithdrawalType");
            return (Criteria) this;
        }

        public Criteria andApprovalWithdrawalTypeNotEqualTo(Byte value) {
            addCriterion("approval_withdrawal_type <>", value, "approvalWithdrawalType");
            return (Criteria) this;
        }

        public Criteria andApprovalWithdrawalTypeGreaterThan(Byte value) {
            addCriterion("approval_withdrawal_type >", value, "approvalWithdrawalType");
            return (Criteria) this;
        }

        public Criteria andApprovalWithdrawalTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("approval_withdrawal_type >=", value, "approvalWithdrawalType");
            return (Criteria) this;
        }

        public Criteria andApprovalWithdrawalTypeLessThan(Byte value) {
            addCriterion("approval_withdrawal_type <", value, "approvalWithdrawalType");
            return (Criteria) this;
        }

        public Criteria andApprovalWithdrawalTypeLessThanOrEqualTo(Byte value) {
            addCriterion("approval_withdrawal_type <=", value, "approvalWithdrawalType");
            return (Criteria) this;
        }

        public Criteria andApprovalWithdrawalTypeIn(List<Byte> values) {
            addCriterion("approval_withdrawal_type in", values, "approvalWithdrawalType");
            return (Criteria) this;
        }

        public Criteria andApprovalWithdrawalTypeNotIn(List<Byte> values) {
            addCriterion("approval_withdrawal_type not in", values, "approvalWithdrawalType");
            return (Criteria) this;
        }

        public Criteria andApprovalWithdrawalTypeBetween(Byte value1, Byte value2) {
            addCriterion("approval_withdrawal_type between", value1, value2, "approvalWithdrawalType");
            return (Criteria) this;
        }

        public Criteria andApprovalWithdrawalTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("approval_withdrawal_type not between", value1, value2, "approvalWithdrawalType");
            return (Criteria) this;
        }

        public Criteria andCarUseReasonIsNull() {
            addCriterion("car_use_reason is null");
            return (Criteria) this;
        }

        public Criteria andCarUseReasonIsNotNull() {
            addCriterion("car_use_reason is not null");
            return (Criteria) this;
        }

        public Criteria andCarUseReasonEqualTo(String value) {
            addCriterion("car_use_reason =", value, "carUseReason");
            return (Criteria) this;
        }

        public Criteria andCarUseReasonNotEqualTo(String value) {
            addCriterion("car_use_reason <>", value, "carUseReason");
            return (Criteria) this;
        }

        public Criteria andCarUseReasonGreaterThan(String value) {
            addCriterion("car_use_reason >", value, "carUseReason");
            return (Criteria) this;
        }

        public Criteria andCarUseReasonGreaterThanOrEqualTo(String value) {
            addCriterion("car_use_reason >=", value, "carUseReason");
            return (Criteria) this;
        }

        public Criteria andCarUseReasonLessThan(String value) {
            addCriterion("car_use_reason <", value, "carUseReason");
            return (Criteria) this;
        }

        public Criteria andCarUseReasonLessThanOrEqualTo(String value) {
            addCriterion("car_use_reason <=", value, "carUseReason");
            return (Criteria) this;
        }

        public Criteria andCarUseReasonLike(String value) {
            addCriterion("car_use_reason like", value, "carUseReason");
            return (Criteria) this;
        }

        public Criteria andCarUseReasonNotLike(String value) {
            addCriterion("car_use_reason not like", value, "carUseReason");
            return (Criteria) this;
        }

        public Criteria andCarUseReasonIn(List<String> values) {
            addCriterion("car_use_reason in", values, "carUseReason");
            return (Criteria) this;
        }

        public Criteria andCarUseReasonNotIn(List<String> values) {
            addCriterion("car_use_reason not in", values, "carUseReason");
            return (Criteria) this;
        }

        public Criteria andCarUseReasonBetween(String value1, String value2) {
            addCriterion("car_use_reason between", value1, value2, "carUseReason");
            return (Criteria) this;
        }

        public Criteria andCarUseReasonNotBetween(String value1, String value2) {
            addCriterion("car_use_reason not between", value1, value2, "carUseReason");
            return (Criteria) this;
        }

        public Criteria andOrderUserMemoIsNull() {
            addCriterion("order_user_memo is null");
            return (Criteria) this;
        }

        public Criteria andOrderUserMemoIsNotNull() {
            addCriterion("order_user_memo is not null");
            return (Criteria) this;
        }

        public Criteria andOrderUserMemoEqualTo(String value) {
            addCriterion("order_user_memo =", value, "orderUserMemo");
            return (Criteria) this;
        }

        public Criteria andOrderUserMemoNotEqualTo(String value) {
            addCriterion("order_user_memo <>", value, "orderUserMemo");
            return (Criteria) this;
        }

        public Criteria andOrderUserMemoGreaterThan(String value) {
            addCriterion("order_user_memo >", value, "orderUserMemo");
            return (Criteria) this;
        }

        public Criteria andOrderUserMemoGreaterThanOrEqualTo(String value) {
            addCriterion("order_user_memo >=", value, "orderUserMemo");
            return (Criteria) this;
        }

        public Criteria andOrderUserMemoLessThan(String value) {
            addCriterion("order_user_memo <", value, "orderUserMemo");
            return (Criteria) this;
        }

        public Criteria andOrderUserMemoLessThanOrEqualTo(String value) {
            addCriterion("order_user_memo <=", value, "orderUserMemo");
            return (Criteria) this;
        }

        public Criteria andOrderUserMemoLike(String value) {
            addCriterion("order_user_memo like", value, "orderUserMemo");
            return (Criteria) this;
        }

        public Criteria andOrderUserMemoNotLike(String value) {
            addCriterion("order_user_memo not like", value, "orderUserMemo");
            return (Criteria) this;
        }

        public Criteria andOrderUserMemoIn(List<String> values) {
            addCriterion("order_user_memo in", values, "orderUserMemo");
            return (Criteria) this;
        }

        public Criteria andOrderUserMemoNotIn(List<String> values) {
            addCriterion("order_user_memo not in", values, "orderUserMemo");
            return (Criteria) this;
        }

        public Criteria andOrderUserMemoBetween(String value1, String value2) {
            addCriterion("order_user_memo between", value1, value2, "orderUserMemo");
            return (Criteria) this;
        }

        public Criteria andOrderUserMemoNotBetween(String value1, String value2) {
            addCriterion("order_user_memo not between", value1, value2, "orderUserMemo");
            return (Criteria) this;
        }

        public Criteria andExpectedPickupTimeIsNull() {
            addCriterion("expected_pickup_time is null");
            return (Criteria) this;
        }

        public Criteria andExpectedPickupTimeIsNotNull() {
            addCriterion("expected_pickup_time is not null");
            return (Criteria) this;
        }

        public Criteria andExpectedPickupTimeEqualTo(Date value) {
            addCriterion("expected_pickup_time =", value, "expectedPickupTime");
            return (Criteria) this;
        }

        public Criteria andExpectedPickupTimeNotEqualTo(Date value) {
            addCriterion("expected_pickup_time <>", value, "expectedPickupTime");
            return (Criteria) this;
        }

        public Criteria andExpectedPickupTimeGreaterThan(Date value) {
            addCriterion("expected_pickup_time >", value, "expectedPickupTime");
            return (Criteria) this;
        }

        public Criteria andExpectedPickupTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("expected_pickup_time >=", value, "expectedPickupTime");
            return (Criteria) this;
        }

        public Criteria andExpectedPickupTimeLessThan(Date value) {
            addCriterion("expected_pickup_time <", value, "expectedPickupTime");
            return (Criteria) this;
        }

        public Criteria andExpectedPickupTimeLessThanOrEqualTo(Date value) {
            addCriterion("expected_pickup_time <=", value, "expectedPickupTime");
            return (Criteria) this;
        }

        public Criteria andExpectedPickupTimeIn(List<Date> values) {
            addCriterion("expected_pickup_time in", values, "expectedPickupTime");
            return (Criteria) this;
        }

        public Criteria andExpectedPickupTimeNotIn(List<Date> values) {
            addCriterion("expected_pickup_time not in", values, "expectedPickupTime");
            return (Criteria) this;
        }

        public Criteria andExpectedPickupTimeBetween(Date value1, Date value2) {
            addCriterion("expected_pickup_time between", value1, value2, "expectedPickupTime");
            return (Criteria) this;
        }

        public Criteria andExpectedPickupTimeNotBetween(Date value1, Date value2) {
            addCriterion("expected_pickup_time not between", value1, value2, "expectedPickupTime");
            return (Criteria) this;
        }

        public Criteria andExpectedReturnTimeIsNull() {
            addCriterion("expected_return_time is null");
            return (Criteria) this;
        }

        public Criteria andExpectedReturnTimeIsNotNull() {
            addCriterion("expected_return_time is not null");
            return (Criteria) this;
        }

        public Criteria andExpectedReturnTimeEqualTo(Date value) {
            addCriterion("expected_return_time =", value, "expectedReturnTime");
            return (Criteria) this;
        }

        public Criteria andExpectedReturnTimeNotEqualTo(Date value) {
            addCriterion("expected_return_time <>", value, "expectedReturnTime");
            return (Criteria) this;
        }

        public Criteria andExpectedReturnTimeGreaterThan(Date value) {
            addCriterion("expected_return_time >", value, "expectedReturnTime");
            return (Criteria) this;
        }

        public Criteria andExpectedReturnTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("expected_return_time >=", value, "expectedReturnTime");
            return (Criteria) this;
        }

        public Criteria andExpectedReturnTimeLessThan(Date value) {
            addCriterion("expected_return_time <", value, "expectedReturnTime");
            return (Criteria) this;
        }

        public Criteria andExpectedReturnTimeLessThanOrEqualTo(Date value) {
            addCriterion("expected_return_time <=", value, "expectedReturnTime");
            return (Criteria) this;
        }

        public Criteria andExpectedReturnTimeIn(List<Date> values) {
            addCriterion("expected_return_time in", values, "expectedReturnTime");
            return (Criteria) this;
        }

        public Criteria andExpectedReturnTimeNotIn(List<Date> values) {
            addCriterion("expected_return_time not in", values, "expectedReturnTime");
            return (Criteria) this;
        }

        public Criteria andExpectedReturnTimeBetween(Date value1, Date value2) {
            addCriterion("expected_return_time between", value1, value2, "expectedReturnTime");
            return (Criteria) this;
        }

        public Criteria andExpectedReturnTimeNotBetween(Date value1, Date value2) {
            addCriterion("expected_return_time not between", value1, value2, "expectedReturnTime");
            return (Criteria) this;
        }

        public Criteria andOrderStartTimeIsNull() {
            addCriterion("order_start_time is null");
            return (Criteria) this;
        }

        public Criteria andOrderStartTimeIsNotNull() {
            addCriterion("order_start_time is not null");
            return (Criteria) this;
        }

        public Criteria andOrderStartTimeEqualTo(Date value) {
            addCriterion("order_start_time =", value, "orderStartTime");
            return (Criteria) this;
        }

        public Criteria andOrderStartTimeNotEqualTo(Date value) {
            addCriterion("order_start_time <>", value, "orderStartTime");
            return (Criteria) this;
        }

        public Criteria andOrderStartTimeGreaterThan(Date value) {
            addCriterion("order_start_time >", value, "orderStartTime");
            return (Criteria) this;
        }

        public Criteria andOrderStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("order_start_time >=", value, "orderStartTime");
            return (Criteria) this;
        }

        public Criteria andOrderStartTimeLessThan(Date value) {
            addCriterion("order_start_time <", value, "orderStartTime");
            return (Criteria) this;
        }

        public Criteria andOrderStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("order_start_time <=", value, "orderStartTime");
            return (Criteria) this;
        }

        public Criteria andOrderStartTimeIn(List<Date> values) {
            addCriterion("order_start_time in", values, "orderStartTime");
            return (Criteria) this;
        }

        public Criteria andOrderStartTimeNotIn(List<Date> values) {
            addCriterion("order_start_time not in", values, "orderStartTime");
            return (Criteria) this;
        }

        public Criteria andOrderStartTimeBetween(Date value1, Date value2) {
            addCriterion("order_start_time between", value1, value2, "orderStartTime");
            return (Criteria) this;
        }

        public Criteria andOrderStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("order_start_time not between", value1, value2, "orderStartTime");
            return (Criteria) this;
        }

        public Criteria andOrderEndTimeIsNull() {
            addCriterion("order_end_time is null");
            return (Criteria) this;
        }

        public Criteria andOrderEndTimeIsNotNull() {
            addCriterion("order_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andOrderEndTimeEqualTo(Date value) {
            addCriterion("order_end_time =", value, "orderEndTime");
            return (Criteria) this;
        }

        public Criteria andOrderEndTimeNotEqualTo(Date value) {
            addCriterion("order_end_time <>", value, "orderEndTime");
            return (Criteria) this;
        }

        public Criteria andOrderEndTimeGreaterThan(Date value) {
            addCriterion("order_end_time >", value, "orderEndTime");
            return (Criteria) this;
        }

        public Criteria andOrderEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("order_end_time >=", value, "orderEndTime");
            return (Criteria) this;
        }

        public Criteria andOrderEndTimeLessThan(Date value) {
            addCriterion("order_end_time <", value, "orderEndTime");
            return (Criteria) this;
        }

        public Criteria andOrderEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("order_end_time <=", value, "orderEndTime");
            return (Criteria) this;
        }

        public Criteria andOrderEndTimeIn(List<Date> values) {
            addCriterion("order_end_time in", values, "orderEndTime");
            return (Criteria) this;
        }

        public Criteria andOrderEndTimeNotIn(List<Date> values) {
            addCriterion("order_end_time not in", values, "orderEndTime");
            return (Criteria) this;
        }

        public Criteria andOrderEndTimeBetween(Date value1, Date value2) {
            addCriterion("order_end_time between", value1, value2, "orderEndTime");
            return (Criteria) this;
        }

        public Criteria andOrderEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("order_end_time not between", value1, value2, "orderEndTime");
            return (Criteria) this;
        }

        public Criteria andPickupLotExitTimeIsNull() {
            addCriterion("pickup_lot_exit_time is null");
            return (Criteria) this;
        }

        public Criteria andPickupLotExitTimeIsNotNull() {
            addCriterion("pickup_lot_exit_time is not null");
            return (Criteria) this;
        }

        public Criteria andPickupLotExitTimeEqualTo(Date value) {
            addCriterion("pickup_lot_exit_time =", value, "pickupLotExitTime");
            return (Criteria) this;
        }

        public Criteria andPickupLotExitTimeNotEqualTo(Date value) {
            addCriterion("pickup_lot_exit_time <>", value, "pickupLotExitTime");
            return (Criteria) this;
        }

        public Criteria andPickupLotExitTimeGreaterThan(Date value) {
            addCriterion("pickup_lot_exit_time >", value, "pickupLotExitTime");
            return (Criteria) this;
        }

        public Criteria andPickupLotExitTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("pickup_lot_exit_time >=", value, "pickupLotExitTime");
            return (Criteria) this;
        }

        public Criteria andPickupLotExitTimeLessThan(Date value) {
            addCriterion("pickup_lot_exit_time <", value, "pickupLotExitTime");
            return (Criteria) this;
        }

        public Criteria andPickupLotExitTimeLessThanOrEqualTo(Date value) {
            addCriterion("pickup_lot_exit_time <=", value, "pickupLotExitTime");
            return (Criteria) this;
        }

        public Criteria andPickupLotExitTimeIn(List<Date> values) {
            addCriterion("pickup_lot_exit_time in", values, "pickupLotExitTime");
            return (Criteria) this;
        }

        public Criteria andPickupLotExitTimeNotIn(List<Date> values) {
            addCriterion("pickup_lot_exit_time not in", values, "pickupLotExitTime");
            return (Criteria) this;
        }

        public Criteria andPickupLotExitTimeBetween(Date value1, Date value2) {
            addCriterion("pickup_lot_exit_time between", value1, value2, "pickupLotExitTime");
            return (Criteria) this;
        }

        public Criteria andPickupLotExitTimeNotBetween(Date value1, Date value2) {
            addCriterion("pickup_lot_exit_time not between", value1, value2, "pickupLotExitTime");
            return (Criteria) this;
        }

        public Criteria andReturnLotEntryTimeIsNull() {
            addCriterion("return_lot_entry_time is null");
            return (Criteria) this;
        }

        public Criteria andReturnLotEntryTimeIsNotNull() {
            addCriterion("return_lot_entry_time is not null");
            return (Criteria) this;
        }

        public Criteria andReturnLotEntryTimeEqualTo(Date value) {
            addCriterion("return_lot_entry_time =", value, "returnLotEntryTime");
            return (Criteria) this;
        }

        public Criteria andReturnLotEntryTimeNotEqualTo(Date value) {
            addCriterion("return_lot_entry_time <>", value, "returnLotEntryTime");
            return (Criteria) this;
        }

        public Criteria andReturnLotEntryTimeGreaterThan(Date value) {
            addCriterion("return_lot_entry_time >", value, "returnLotEntryTime");
            return (Criteria) this;
        }

        public Criteria andReturnLotEntryTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("return_lot_entry_time >=", value, "returnLotEntryTime");
            return (Criteria) this;
        }

        public Criteria andReturnLotEntryTimeLessThan(Date value) {
            addCriterion("return_lot_entry_time <", value, "returnLotEntryTime");
            return (Criteria) this;
        }

        public Criteria andReturnLotEntryTimeLessThanOrEqualTo(Date value) {
            addCriterion("return_lot_entry_time <=", value, "returnLotEntryTime");
            return (Criteria) this;
        }

        public Criteria andReturnLotEntryTimeIn(List<Date> values) {
            addCriterion("return_lot_entry_time in", values, "returnLotEntryTime");
            return (Criteria) this;
        }

        public Criteria andReturnLotEntryTimeNotIn(List<Date> values) {
            addCriterion("return_lot_entry_time not in", values, "returnLotEntryTime");
            return (Criteria) this;
        }

        public Criteria andReturnLotEntryTimeBetween(Date value1, Date value2) {
            addCriterion("return_lot_entry_time between", value1, value2, "returnLotEntryTime");
            return (Criteria) this;
        }

        public Criteria andReturnLotEntryTimeNotBetween(Date value1, Date value2) {
            addCriterion("return_lot_entry_time not between", value1, value2, "returnLotEntryTime");
            return (Criteria) this;
        }

        public Criteria andVerifierTimeIsNull() {
            addCriterion("verifier_time is null");
            return (Criteria) this;
        }

        public Criteria andVerifierTimeIsNotNull() {
            addCriterion("verifier_time is not null");
            return (Criteria) this;
        }

        public Criteria andVerifierTimeEqualTo(Date value) {
            addCriterion("verifier_time =", value, "verifierTime");
            return (Criteria) this;
        }

        public Criteria andVerifierTimeNotEqualTo(Date value) {
            addCriterion("verifier_time <>", value, "verifierTime");
            return (Criteria) this;
        }

        public Criteria andVerifierTimeGreaterThan(Date value) {
            addCriterion("verifier_time >", value, "verifierTime");
            return (Criteria) this;
        }

        public Criteria andVerifierTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("verifier_time >=", value, "verifierTime");
            return (Criteria) this;
        }

        public Criteria andVerifierTimeLessThan(Date value) {
            addCriterion("verifier_time <", value, "verifierTime");
            return (Criteria) this;
        }

        public Criteria andVerifierTimeLessThanOrEqualTo(Date value) {
            addCriterion("verifier_time <=", value, "verifierTime");
            return (Criteria) this;
        }

        public Criteria andVerifierTimeIn(List<Date> values) {
            addCriterion("verifier_time in", values, "verifierTime");
            return (Criteria) this;
        }

        public Criteria andVerifierTimeNotIn(List<Date> values) {
            addCriterion("verifier_time not in", values, "verifierTime");
            return (Criteria) this;
        }

        public Criteria andVerifierTimeBetween(Date value1, Date value2) {
            addCriterion("verifier_time between", value1, value2, "verifierTime");
            return (Criteria) this;
        }

        public Criteria andVerifierTimeNotBetween(Date value1, Date value2) {
            addCriterion("verifier_time not between", value1, value2, "verifierTime");
            return (Criteria) this;
        }

        public Criteria andCancellationTimeIsNull() {
            addCriterion("cancellation_time is null");
            return (Criteria) this;
        }

        public Criteria andCancellationTimeIsNotNull() {
            addCriterion("cancellation_time is not null");
            return (Criteria) this;
        }

        public Criteria andCancellationTimeEqualTo(Date value) {
            addCriterion("cancellation_time =", value, "cancellationTime");
            return (Criteria) this;
        }

        public Criteria andCancellationTimeNotEqualTo(Date value) {
            addCriterion("cancellation_time <>", value, "cancellationTime");
            return (Criteria) this;
        }

        public Criteria andCancellationTimeGreaterThan(Date value) {
            addCriterion("cancellation_time >", value, "cancellationTime");
            return (Criteria) this;
        }

        public Criteria andCancellationTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("cancellation_time >=", value, "cancellationTime");
            return (Criteria) this;
        }

        public Criteria andCancellationTimeLessThan(Date value) {
            addCriterion("cancellation_time <", value, "cancellationTime");
            return (Criteria) this;
        }

        public Criteria andCancellationTimeLessThanOrEqualTo(Date value) {
            addCriterion("cancellation_time <=", value, "cancellationTime");
            return (Criteria) this;
        }

        public Criteria andCancellationTimeIn(List<Date> values) {
            addCriterion("cancellation_time in", values, "cancellationTime");
            return (Criteria) this;
        }

        public Criteria andCancellationTimeNotIn(List<Date> values) {
            addCriterion("cancellation_time not in", values, "cancellationTime");
            return (Criteria) this;
        }

        public Criteria andCancellationTimeBetween(Date value1, Date value2) {
            addCriterion("cancellation_time between", value1, value2, "cancellationTime");
            return (Criteria) this;
        }

        public Criteria andCancellationTimeNotBetween(Date value1, Date value2) {
            addCriterion("cancellation_time not between", value1, value2, "cancellationTime");
            return (Criteria) this;
        }

        public Criteria andTotalMileageIsNull() {
            addCriterion("total_mileage is null");
            return (Criteria) this;
        }

        public Criteria andTotalMileageIsNotNull() {
            addCriterion("total_mileage is not null");
            return (Criteria) this;
        }

        public Criteria andTotalMileageEqualTo(BigDecimal value) {
            addCriterion("total_mileage =", value, "totalMileage");
            return (Criteria) this;
        }

        public Criteria andTotalMileageNotEqualTo(BigDecimal value) {
            addCriterion("total_mileage <>", value, "totalMileage");
            return (Criteria) this;
        }

        public Criteria andTotalMileageGreaterThan(BigDecimal value) {
            addCriterion("total_mileage >", value, "totalMileage");
            return (Criteria) this;
        }

        public Criteria andTotalMileageGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("total_mileage >=", value, "totalMileage");
            return (Criteria) this;
        }

        public Criteria andTotalMileageLessThan(BigDecimal value) {
            addCriterion("total_mileage <", value, "totalMileage");
            return (Criteria) this;
        }

        public Criteria andTotalMileageLessThanOrEqualTo(BigDecimal value) {
            addCriterion("total_mileage <=", value, "totalMileage");
            return (Criteria) this;
        }

        public Criteria andTotalMileageIn(List<BigDecimal> values) {
            addCriterion("total_mileage in", values, "totalMileage");
            return (Criteria) this;
        }

        public Criteria andTotalMileageNotIn(List<BigDecimal> values) {
            addCriterion("total_mileage not in", values, "totalMileage");
            return (Criteria) this;
        }

        public Criteria andTotalMileageBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_mileage between", value1, value2, "totalMileage");
            return (Criteria) this;
        }

        public Criteria andTotalMileageNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("total_mileage not between", value1, value2, "totalMileage");
            return (Criteria) this;
        }

        public Criteria andOutInFenceTotalMileageIsNull() {
            addCriterion("out_in_fence_total_mileage is null");
            return (Criteria) this;
        }

        public Criteria andOutInFenceTotalMileageIsNotNull() {
            addCriterion("out_in_fence_total_mileage is not null");
            return (Criteria) this;
        }

        public Criteria andOutInFenceTotalMileageEqualTo(BigDecimal value) {
            addCriterion("out_in_fence_total_mileage =", value, "outInFenceTotalMileage");
            return (Criteria) this;
        }

        public Criteria andOutInFenceTotalMileageNotEqualTo(BigDecimal value) {
            addCriterion("out_in_fence_total_mileage <>", value, "outInFenceTotalMileage");
            return (Criteria) this;
        }

        public Criteria andOutInFenceTotalMileageGreaterThan(BigDecimal value) {
            addCriterion("out_in_fence_total_mileage >", value, "outInFenceTotalMileage");
            return (Criteria) this;
        }

        public Criteria andOutInFenceTotalMileageGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("out_in_fence_total_mileage >=", value, "outInFenceTotalMileage");
            return (Criteria) this;
        }

        public Criteria andOutInFenceTotalMileageLessThan(BigDecimal value) {
            addCriterion("out_in_fence_total_mileage <", value, "outInFenceTotalMileage");
            return (Criteria) this;
        }

        public Criteria andOutInFenceTotalMileageLessThanOrEqualTo(BigDecimal value) {
            addCriterion("out_in_fence_total_mileage <=", value, "outInFenceTotalMileage");
            return (Criteria) this;
        }

        public Criteria andOutInFenceTotalMileageIn(List<BigDecimal> values) {
            addCriterion("out_in_fence_total_mileage in", values, "outInFenceTotalMileage");
            return (Criteria) this;
        }

        public Criteria andOutInFenceTotalMileageNotIn(List<BigDecimal> values) {
            addCriterion("out_in_fence_total_mileage not in", values, "outInFenceTotalMileage");
            return (Criteria) this;
        }

        public Criteria andOutInFenceTotalMileageBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("out_in_fence_total_mileage between", value1, value2, "outInFenceTotalMileage");
            return (Criteria) this;
        }

        public Criteria andOutInFenceTotalMileageNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("out_in_fence_total_mileage not between", value1, value2, "outInFenceTotalMileage");
            return (Criteria) this;
        }

        public Criteria andUpdateIdIsNull() {
            addCriterion("update_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdateIdIsNotNull() {
            addCriterion("update_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateIdEqualTo(Integer value) {
            addCriterion("update_id =", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdNotEqualTo(Integer value) {
            addCriterion("update_id <>", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdGreaterThan(Integer value) {
            addCriterion("update_id >", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("update_id >=", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdLessThan(Integer value) {
            addCriterion("update_id <", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdLessThanOrEqualTo(Integer value) {
            addCriterion("update_id <=", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdIn(List<Integer> values) {
            addCriterion("update_id in", values, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdNotIn(List<Integer> values) {
            addCriterion("update_id not in", values, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdBetween(Integer value1, Integer value2) {
            addCriterion("update_id between", value1, value2, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdNotBetween(Integer value1, Integer value2) {
            addCriterion("update_id not between", value1, value2, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateNameIsNull() {
            addCriterion("update_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdateNameIsNotNull() {
            addCriterion("update_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateNameEqualTo(String value) {
            addCriterion("update_name =", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotEqualTo(String value) {
            addCriterion("update_name <>", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameGreaterThan(String value) {
            addCriterion("update_name >", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameGreaterThanOrEqualTo(String value) {
            addCriterion("update_name >=", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameLessThan(String value) {
            addCriterion("update_name <", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameLessThanOrEqualTo(String value) {
            addCriterion("update_name <=", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameLike(String value) {
            addCriterion("update_name like", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotLike(String value) {
            addCriterion("update_name not like", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameIn(List<String> values) {
            addCriterion("update_name in", values, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotIn(List<String> values) {
            addCriterion("update_name not in", values, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameBetween(String value1, String value2) {
            addCriterion("update_name between", value1, value2, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotBetween(String value1, String value2) {
            addCriterion("update_name not between", value1, value2, "updateName");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andDeploymentModeIsNull() {
            addCriterion("deployment_mode is null");
            return (Criteria) this;
        }

        public Criteria andDeploymentModeIsNotNull() {
            addCriterion("deployment_mode is not null");
            return (Criteria) this;
        }

        public Criteria andDeploymentModeEqualTo(Byte value) {
            addCriterion("deployment_mode =", value, "deploymentMode");
            return (Criteria) this;
        }

        public Criteria andDeploymentModeNotEqualTo(Byte value) {
            addCriterion("deployment_mode <>", value, "deploymentMode");
            return (Criteria) this;
        }

        public Criteria andDeploymentModeGreaterThan(Byte value) {
            addCriterion("deployment_mode >", value, "deploymentMode");
            return (Criteria) this;
        }

        public Criteria andDeploymentModeGreaterThanOrEqualTo(Byte value) {
            addCriterion("deployment_mode >=", value, "deploymentMode");
            return (Criteria) this;
        }

        public Criteria andDeploymentModeLessThan(Byte value) {
            addCriterion("deployment_mode <", value, "deploymentMode");
            return (Criteria) this;
        }

        public Criteria andDeploymentModeLessThanOrEqualTo(Byte value) {
            addCriterion("deployment_mode <=", value, "deploymentMode");
            return (Criteria) this;
        }

        public Criteria andDeploymentModeIn(List<Byte> values) {
            addCriterion("deployment_mode in", values, "deploymentMode");
            return (Criteria) this;
        }

        public Criteria andDeploymentModeNotIn(List<Byte> values) {
            addCriterion("deployment_mode not in", values, "deploymentMode");
            return (Criteria) this;
        }

        public Criteria andDeploymentModeBetween(Byte value1, Byte value2) {
            addCriterion("deployment_mode between", value1, value2, "deploymentMode");
            return (Criteria) this;
        }

        public Criteria andDeploymentModeNotBetween(Byte value1, Byte value2) {
            addCriterion("deployment_mode not between", value1, value2, "deploymentMode");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Integer value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Integer value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Integer value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Integer value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Integer value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Integer> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Integer> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Integer value1, Integer value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Integer value1, Integer value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNull() {
            addCriterion("company_code is null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNotNull() {
            addCriterion("company_code is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeEqualTo(String value) {
            addCriterion("company_code =", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotEqualTo(String value) {
            addCriterion("company_code <>", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThan(String value) {
            addCriterion("company_code >", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("company_code >=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThan(String value) {
            addCriterion("company_code <", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("company_code <=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLike(String value) {
            addCriterion("company_code like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotLike(String value) {
            addCriterion("company_code not like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIn(List<String> values) {
            addCriterion("company_code in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotIn(List<String> values) {
            addCriterion("company_code not in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeBetween(String value1, String value2) {
            addCriterion("company_code between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("company_code not between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIsNull() {
            addCriterion("company_name is null");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIsNotNull() {
            addCriterion("company_name is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyNameEqualTo(String value) {
            addCriterion("company_name =", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotEqualTo(String value) {
            addCriterion("company_name <>", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameGreaterThan(String value) {
            addCriterion("company_name >", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameGreaterThanOrEqualTo(String value) {
            addCriterion("company_name >=", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLessThan(String value) {
            addCriterion("company_name <", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLessThanOrEqualTo(String value) {
            addCriterion("company_name <=", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLike(String value) {
            addCriterion("company_name like", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotLike(String value) {
            addCriterion("company_name not like", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIn(List<String> values) {
            addCriterion("company_name in", values, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotIn(List<String> values) {
            addCriterion("company_name not in", values, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameBetween(String value1, String value2) {
            addCriterion("company_name between", value1, value2, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotBetween(String value1, String value2) {
            addCriterion("company_name not between", value1, value2, "companyName");
            return (Criteria) this;
        }

        public Criteria andIsShowIsNull() {
            addCriterion("is_show is null");
            return (Criteria) this;
        }

        public Criteria andIsShowIsNotNull() {
            addCriterion("is_show is not null");
            return (Criteria) this;
        }

        public Criteria andIsShowEqualTo(Byte value) {
            addCriterion("is_show =", value, "isShow");
            return (Criteria) this;
        }

        public Criteria andIsShowNotEqualTo(Byte value) {
            addCriterion("is_show <>", value, "isShow");
            return (Criteria) this;
        }

        public Criteria andIsShowGreaterThan(Byte value) {
            addCriterion("is_show >", value, "isShow");
            return (Criteria) this;
        }

        public Criteria andIsShowGreaterThanOrEqualTo(Byte value) {
            addCriterion("is_show >=", value, "isShow");
            return (Criteria) this;
        }

        public Criteria andIsShowLessThan(Byte value) {
            addCriterion("is_show <", value, "isShow");
            return (Criteria) this;
        }

        public Criteria andIsShowLessThanOrEqualTo(Byte value) {
            addCriterion("is_show <=", value, "isShow");
            return (Criteria) this;
        }

        public Criteria andIsShowIn(List<Byte> values) {
            addCriterion("is_show in", values, "isShow");
            return (Criteria) this;
        }

        public Criteria andIsShowNotIn(List<Byte> values) {
            addCriterion("is_show not in", values, "isShow");
            return (Criteria) this;
        }

        public Criteria andIsShowBetween(Byte value1, Byte value2) {
            addCriterion("is_show between", value1, value2, "isShow");
            return (Criteria) this;
        }

        public Criteria andIsShowNotBetween(Byte value1, Byte value2) {
            addCriterion("is_show not between", value1, value2, "isShow");
            return (Criteria) this;
        }

        public Criteria andVerifyStatusIsNull() {
            addCriterion("verify_status is null");
            return (Criteria) this;
        }

        public Criteria andVerifyStatusIsNotNull() {
            addCriterion("verify_status is not null");
            return (Criteria) this;
        }

        public Criteria andVerifyStatusEqualTo(Integer value) {
            addCriterion("verify_status =", value, "verifyStatus");
            return (Criteria) this;
        }

        public Criteria andVerifyStatusNotEqualTo(Integer value) {
            addCriterion("verify_status <>", value, "verifyStatus");
            return (Criteria) this;
        }

        public Criteria andVerifyStatusGreaterThan(Integer value) {
            addCriterion("verify_status >", value, "verifyStatus");
            return (Criteria) this;
        }

        public Criteria andVerifyStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("verify_status >=", value, "verifyStatus");
            return (Criteria) this;
        }

        public Criteria andVerifyStatusLessThan(Integer value) {
            addCriterion("verify_status <", value, "verifyStatus");
            return (Criteria) this;
        }

        public Criteria andVerifyStatusLessThanOrEqualTo(Integer value) {
            addCriterion("verify_status <=", value, "verifyStatus");
            return (Criteria) this;
        }

        public Criteria andVerifyStatusIn(List<Integer> values) {
            addCriterion("verify_status in", values, "verifyStatus");
            return (Criteria) this;
        }

        public Criteria andVerifyStatusNotIn(List<Integer> values) {
            addCriterion("verify_status not in", values, "verifyStatus");
            return (Criteria) this;
        }

        public Criteria andVerifyStatusBetween(Integer value1, Integer value2) {
            addCriterion("verify_status between", value1, value2, "verifyStatus");
            return (Criteria) this;
        }

        public Criteria andVerifyStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("verify_status not between", value1, value2, "verifyStatus");
            return (Criteria) this;
        }
    }

    /**
     * gov_public_car_order
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * gov_public_car_order null
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}