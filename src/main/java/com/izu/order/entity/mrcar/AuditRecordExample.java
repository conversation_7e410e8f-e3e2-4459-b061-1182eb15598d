package com.izu.order.entity.mrcar;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class AuditRecordExample {
    /**
     * so_audit_record
     */
    protected String orderByClause;

    /**
     * so_audit_record
     */
    protected boolean distinct;

    /**
     * so_audit_record
     */
    protected List<Criteria> oredCriteria;

    /**
     *
     * @mbg.generated
     */
    public AuditRecordExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * so_audit_record null
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andAuditRecordIdIsNull() {
            addCriterion("audit_record_id is null");
            return (Criteria) this;
        }

        public Criteria andAuditRecordIdIsNotNull() {
            addCriterion("audit_record_id is not null");
            return (Criteria) this;
        }

        public Criteria andAuditRecordIdEqualTo(Integer value) {
            addCriterion("audit_record_id =", value, "auditRecordId");
            return (Criteria) this;
        }

        public Criteria andAuditRecordIdNotEqualTo(Integer value) {
            addCriterion("audit_record_id <>", value, "auditRecordId");
            return (Criteria) this;
        }

        public Criteria andAuditRecordIdGreaterThan(Integer value) {
            addCriterion("audit_record_id >", value, "auditRecordId");
            return (Criteria) this;
        }

        public Criteria andAuditRecordIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("audit_record_id >=", value, "auditRecordId");
            return (Criteria) this;
        }

        public Criteria andAuditRecordIdLessThan(Integer value) {
            addCriterion("audit_record_id <", value, "auditRecordId");
            return (Criteria) this;
        }

        public Criteria andAuditRecordIdLessThanOrEqualTo(Integer value) {
            addCriterion("audit_record_id <=", value, "auditRecordId");
            return (Criteria) this;
        }

        public Criteria andAuditRecordIdIn(List<Integer> values) {
            addCriterion("audit_record_id in", values, "auditRecordId");
            return (Criteria) this;
        }

        public Criteria andAuditRecordIdNotIn(List<Integer> values) {
            addCriterion("audit_record_id not in", values, "auditRecordId");
            return (Criteria) this;
        }

        public Criteria andAuditRecordIdBetween(Integer value1, Integer value2) {
            addCriterion("audit_record_id between", value1, value2, "auditRecordId");
            return (Criteria) this;
        }

        public Criteria andAuditRecordIdNotBetween(Integer value1, Integer value2) {
            addCriterion("audit_record_id not between", value1, value2, "auditRecordId");
            return (Criteria) this;
        }

        public Criteria andAuditRecordNumIsNull() {
            addCriterion("audit_record_num is null");
            return (Criteria) this;
        }

        public Criteria andAuditRecordNumIsNotNull() {
            addCriterion("audit_record_num is not null");
            return (Criteria) this;
        }

        public Criteria andAuditRecordNumEqualTo(String value) {
            addCriterion("audit_record_num =", value, "auditRecordNum");
            return (Criteria) this;
        }

        public Criteria andAuditRecordNumNotEqualTo(String value) {
            addCriterion("audit_record_num <>", value, "auditRecordNum");
            return (Criteria) this;
        }

        public Criteria andAuditRecordNumGreaterThan(String value) {
            addCriterion("audit_record_num >", value, "auditRecordNum");
            return (Criteria) this;
        }

        public Criteria andAuditRecordNumGreaterThanOrEqualTo(String value) {
            addCriterion("audit_record_num >=", value, "auditRecordNum");
            return (Criteria) this;
        }

        public Criteria andAuditRecordNumLessThan(String value) {
            addCriterion("audit_record_num <", value, "auditRecordNum");
            return (Criteria) this;
        }

        public Criteria andAuditRecordNumLessThanOrEqualTo(String value) {
            addCriterion("audit_record_num <=", value, "auditRecordNum");
            return (Criteria) this;
        }

        public Criteria andAuditRecordNumLike(String value) {
            addCriterion("audit_record_num like", value, "auditRecordNum");
            return (Criteria) this;
        }

        public Criteria andAuditRecordNumNotLike(String value) {
            addCriterion("audit_record_num not like", value, "auditRecordNum");
            return (Criteria) this;
        }

        public Criteria andAuditRecordNumIn(List<String> values) {
            addCriterion("audit_record_num in", values, "auditRecordNum");
            return (Criteria) this;
        }

        public Criteria andAuditRecordNumNotIn(List<String> values) {
            addCriterion("audit_record_num not in", values, "auditRecordNum");
            return (Criteria) this;
        }

        public Criteria andAuditRecordNumBetween(String value1, String value2) {
            addCriterion("audit_record_num between", value1, value2, "auditRecordNum");
            return (Criteria) this;
        }

        public Criteria andAuditRecordNumNotBetween(String value1, String value2) {
            addCriterion("audit_record_num not between", value1, value2, "auditRecordNum");
            return (Criteria) this;
        }

        public Criteria andAuditTypeIsNull() {
            addCriterion("audit_type is null");
            return (Criteria) this;
        }

        public Criteria andAuditTypeIsNotNull() {
            addCriterion("audit_type is not null");
            return (Criteria) this;
        }

        public Criteria andAuditTypeEqualTo(Integer value) {
            addCriterion("audit_type =", value, "auditType");
            return (Criteria) this;
        }

        public Criteria andAuditTypeNotEqualTo(Integer value) {
            addCriterion("audit_type <>", value, "auditType");
            return (Criteria) this;
        }

        public Criteria andAuditTypeGreaterThan(Integer value) {
            addCriterion("audit_type >", value, "auditType");
            return (Criteria) this;
        }

        public Criteria andAuditTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("audit_type >=", value, "auditType");
            return (Criteria) this;
        }

        public Criteria andAuditTypeLessThan(Integer value) {
            addCriterion("audit_type <", value, "auditType");
            return (Criteria) this;
        }

        public Criteria andAuditTypeLessThanOrEqualTo(Integer value) {
            addCriterion("audit_type <=", value, "auditType");
            return (Criteria) this;
        }

        public Criteria andAuditTypeIn(List<Integer> values) {
            addCriterion("audit_type in", values, "auditType");
            return (Criteria) this;
        }

        public Criteria andAuditTypeNotIn(List<Integer> values) {
            addCriterion("audit_type not in", values, "auditType");
            return (Criteria) this;
        }

        public Criteria andAuditTypeBetween(Integer value1, Integer value2) {
            addCriterion("audit_type between", value1, value2, "auditType");
            return (Criteria) this;
        }

        public Criteria andAuditTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("audit_type not between", value1, value2, "auditType");
            return (Criteria) this;
        }

        public Criteria andDemandOrderNumIsNull() {
            addCriterion("demand_order_num is null");
            return (Criteria) this;
        }

        public Criteria andDemandOrderNumIsNotNull() {
            addCriterion("demand_order_num is not null");
            return (Criteria) this;
        }

        public Criteria andDemandOrderNumEqualTo(String value) {
            addCriterion("demand_order_num =", value, "demandOrderNum");
            return (Criteria) this;
        }

        public Criteria andDemandOrderNumNotEqualTo(String value) {
            addCriterion("demand_order_num <>", value, "demandOrderNum");
            return (Criteria) this;
        }

        public Criteria andDemandOrderNumGreaterThan(String value) {
            addCriterion("demand_order_num >", value, "demandOrderNum");
            return (Criteria) this;
        }

        public Criteria andDemandOrderNumGreaterThanOrEqualTo(String value) {
            addCriterion("demand_order_num >=", value, "demandOrderNum");
            return (Criteria) this;
        }

        public Criteria andDemandOrderNumLessThan(String value) {
            addCriterion("demand_order_num <", value, "demandOrderNum");
            return (Criteria) this;
        }

        public Criteria andDemandOrderNumLessThanOrEqualTo(String value) {
            addCriterion("demand_order_num <=", value, "demandOrderNum");
            return (Criteria) this;
        }

        public Criteria andDemandOrderNumLike(String value) {
            addCriterion("demand_order_num like", value, "demandOrderNum");
            return (Criteria) this;
        }

        public Criteria andDemandOrderNumNotLike(String value) {
            addCriterion("demand_order_num not like", value, "demandOrderNum");
            return (Criteria) this;
        }

        public Criteria andDemandOrderNumIn(List<String> values) {
            addCriterion("demand_order_num in", values, "demandOrderNum");
            return (Criteria) this;
        }

        public Criteria andDemandOrderNumNotIn(List<String> values) {
            addCriterion("demand_order_num not in", values, "demandOrderNum");
            return (Criteria) this;
        }

        public Criteria andDemandOrderNumBetween(String value1, String value2) {
            addCriterion("demand_order_num between", value1, value2, "demandOrderNum");
            return (Criteria) this;
        }

        public Criteria andDemandOrderNumNotBetween(String value1, String value2) {
            addCriterion("demand_order_num not between", value1, value2, "demandOrderNum");
            return (Criteria) this;
        }

        public Criteria andCustomerOrderNumIsNull() {
            addCriterion("customer_order_num is null");
            return (Criteria) this;
        }

        public Criteria andCustomerOrderNumIsNotNull() {
            addCriterion("customer_order_num is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerOrderNumEqualTo(String value) {
            addCriterion("customer_order_num =", value, "customerOrderNum");
            return (Criteria) this;
        }

        public Criteria andCustomerOrderNumNotEqualTo(String value) {
            addCriterion("customer_order_num <>", value, "customerOrderNum");
            return (Criteria) this;
        }

        public Criteria andCustomerOrderNumGreaterThan(String value) {
            addCriterion("customer_order_num >", value, "customerOrderNum");
            return (Criteria) this;
        }

        public Criteria andCustomerOrderNumGreaterThanOrEqualTo(String value) {
            addCriterion("customer_order_num >=", value, "customerOrderNum");
            return (Criteria) this;
        }

        public Criteria andCustomerOrderNumLessThan(String value) {
            addCriterion("customer_order_num <", value, "customerOrderNum");
            return (Criteria) this;
        }

        public Criteria andCustomerOrderNumLessThanOrEqualTo(String value) {
            addCriterion("customer_order_num <=", value, "customerOrderNum");
            return (Criteria) this;
        }

        public Criteria andCustomerOrderNumLike(String value) {
            addCriterion("customer_order_num like", value, "customerOrderNum");
            return (Criteria) this;
        }

        public Criteria andCustomerOrderNumNotLike(String value) {
            addCriterion("customer_order_num not like", value, "customerOrderNum");
            return (Criteria) this;
        }

        public Criteria andCustomerOrderNumIn(List<String> values) {
            addCriterion("customer_order_num in", values, "customerOrderNum");
            return (Criteria) this;
        }

        public Criteria andCustomerOrderNumNotIn(List<String> values) {
            addCriterion("customer_order_num not in", values, "customerOrderNum");
            return (Criteria) this;
        }

        public Criteria andCustomerOrderNumBetween(String value1, String value2) {
            addCriterion("customer_order_num between", value1, value2, "customerOrderNum");
            return (Criteria) this;
        }

        public Criteria andCustomerOrderNumNotBetween(String value1, String value2) {
            addCriterion("customer_order_num not between", value1, value2, "customerOrderNum");
            return (Criteria) this;
        }

        public Criteria andSupplierOrderNumIsNull() {
            addCriterion("supplier_order_num is null");
            return (Criteria) this;
        }

        public Criteria andSupplierOrderNumIsNotNull() {
            addCriterion("supplier_order_num is not null");
            return (Criteria) this;
        }

        public Criteria andSupplierOrderNumEqualTo(String value) {
            addCriterion("supplier_order_num =", value, "supplierOrderNum");
            return (Criteria) this;
        }

        public Criteria andSupplierOrderNumNotEqualTo(String value) {
            addCriterion("supplier_order_num <>", value, "supplierOrderNum");
            return (Criteria) this;
        }

        public Criteria andSupplierOrderNumGreaterThan(String value) {
            addCriterion("supplier_order_num >", value, "supplierOrderNum");
            return (Criteria) this;
        }

        public Criteria andSupplierOrderNumGreaterThanOrEqualTo(String value) {
            addCriterion("supplier_order_num >=", value, "supplierOrderNum");
            return (Criteria) this;
        }

        public Criteria andSupplierOrderNumLessThan(String value) {
            addCriterion("supplier_order_num <", value, "supplierOrderNum");
            return (Criteria) this;
        }

        public Criteria andSupplierOrderNumLessThanOrEqualTo(String value) {
            addCriterion("supplier_order_num <=", value, "supplierOrderNum");
            return (Criteria) this;
        }

        public Criteria andSupplierOrderNumLike(String value) {
            addCriterion("supplier_order_num like", value, "supplierOrderNum");
            return (Criteria) this;
        }

        public Criteria andSupplierOrderNumNotLike(String value) {
            addCriterion("supplier_order_num not like", value, "supplierOrderNum");
            return (Criteria) this;
        }

        public Criteria andSupplierOrderNumIn(List<String> values) {
            addCriterion("supplier_order_num in", values, "supplierOrderNum");
            return (Criteria) this;
        }

        public Criteria andSupplierOrderNumNotIn(List<String> values) {
            addCriterion("supplier_order_num not in", values, "supplierOrderNum");
            return (Criteria) this;
        }

        public Criteria andSupplierOrderNumBetween(String value1, String value2) {
            addCriterion("supplier_order_num between", value1, value2, "supplierOrderNum");
            return (Criteria) this;
        }

        public Criteria andSupplierOrderNumNotBetween(String value1, String value2) {
            addCriterion("supplier_order_num not between", value1, value2, "supplierOrderNum");
            return (Criteria) this;
        }

        public Criteria andSupplierNameIsNull() {
            addCriterion("supplier_name is null");
            return (Criteria) this;
        }

        public Criteria andSupplierNameIsNotNull() {
            addCriterion("supplier_name is not null");
            return (Criteria) this;
        }

        public Criteria andSupplierNameEqualTo(String value) {
            addCriterion("supplier_name =", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameNotEqualTo(String value) {
            addCriterion("supplier_name <>", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameGreaterThan(String value) {
            addCriterion("supplier_name >", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameGreaterThanOrEqualTo(String value) {
            addCriterion("supplier_name >=", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameLessThan(String value) {
            addCriterion("supplier_name <", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameLessThanOrEqualTo(String value) {
            addCriterion("supplier_name <=", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameLike(String value) {
            addCriterion("supplier_name like", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameNotLike(String value) {
            addCriterion("supplier_name not like", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameIn(List<String> values) {
            addCriterion("supplier_name in", values, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameNotIn(List<String> values) {
            addCriterion("supplier_name not in", values, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameBetween(String value1, String value2) {
            addCriterion("supplier_name between", value1, value2, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameNotBetween(String value1, String value2) {
            addCriterion("supplier_name not between", value1, value2, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierTypeIsNull() {
            addCriterion("supplier_type is null");
            return (Criteria) this;
        }

        public Criteria andSupplierTypeIsNotNull() {
            addCriterion("supplier_type is not null");
            return (Criteria) this;
        }

        public Criteria andSupplierTypeEqualTo(Integer value) {
            addCriterion("supplier_type =", value, "supplierType");
            return (Criteria) this;
        }

        public Criteria andSupplierTypeNotEqualTo(Integer value) {
            addCriterion("supplier_type <>", value, "supplierType");
            return (Criteria) this;
        }

        public Criteria andSupplierTypeGreaterThan(Integer value) {
            addCriterion("supplier_type >", value, "supplierType");
            return (Criteria) this;
        }

        public Criteria andSupplierTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("supplier_type >=", value, "supplierType");
            return (Criteria) this;
        }

        public Criteria andSupplierTypeLessThan(Integer value) {
            addCriterion("supplier_type <", value, "supplierType");
            return (Criteria) this;
        }

        public Criteria andSupplierTypeLessThanOrEqualTo(Integer value) {
            addCriterion("supplier_type <=", value, "supplierType");
            return (Criteria) this;
        }

        public Criteria andSupplierTypeIn(List<Integer> values) {
            addCriterion("supplier_type in", values, "supplierType");
            return (Criteria) this;
        }

        public Criteria andSupplierTypeNotIn(List<Integer> values) {
            addCriterion("supplier_type not in", values, "supplierType");
            return (Criteria) this;
        }

        public Criteria andSupplierTypeBetween(Integer value1, Integer value2) {
            addCriterion("supplier_type between", value1, value2, "supplierType");
            return (Criteria) this;
        }

        public Criteria andSupplierTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("supplier_type not between", value1, value2, "supplierType");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeIsNull() {
            addCriterion("supplier_code is null");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeIsNotNull() {
            addCriterion("supplier_code is not null");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeEqualTo(String value) {
            addCriterion("supplier_code =", value, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeNotEqualTo(String value) {
            addCriterion("supplier_code <>", value, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeGreaterThan(String value) {
            addCriterion("supplier_code >", value, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeGreaterThanOrEqualTo(String value) {
            addCriterion("supplier_code >=", value, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeLessThan(String value) {
            addCriterion("supplier_code <", value, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeLessThanOrEqualTo(String value) {
            addCriterion("supplier_code <=", value, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeLike(String value) {
            addCriterion("supplier_code like", value, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeNotLike(String value) {
            addCriterion("supplier_code not like", value, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeIn(List<String> values) {
            addCriterion("supplier_code in", values, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeNotIn(List<String> values) {
            addCriterion("supplier_code not in", values, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeBetween(String value1, String value2) {
            addCriterion("supplier_code between", value1, value2, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeNotBetween(String value1, String value2) {
            addCriterion("supplier_code not between", value1, value2, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andAuditStatusIsNull() {
            addCriterion("audit_status is null");
            return (Criteria) this;
        }

        public Criteria andAuditStatusIsNotNull() {
            addCriterion("audit_status is not null");
            return (Criteria) this;
        }

        public Criteria andAuditStatusEqualTo(Integer value) {
            addCriterion("audit_status =", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusNotEqualTo(Integer value) {
            addCriterion("audit_status <>", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusGreaterThan(Integer value) {
            addCriterion("audit_status >", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("audit_status >=", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusLessThan(Integer value) {
            addCriterion("audit_status <", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusLessThanOrEqualTo(Integer value) {
            addCriterion("audit_status <=", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusIn(List<Integer> values) {
            addCriterion("audit_status in", values, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusNotIn(List<Integer> values) {
            addCriterion("audit_status not in", values, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusBetween(Integer value1, Integer value2) {
            addCriterion("audit_status between", value1, value2, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("audit_status not between", value1, value2, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditTimeIsNull() {
            addCriterion("audit_time is null");
            return (Criteria) this;
        }

        public Criteria andAuditTimeIsNotNull() {
            addCriterion("audit_time is not null");
            return (Criteria) this;
        }

        public Criteria andAuditTimeEqualTo(Date value) {
            addCriterion("audit_time =", value, "auditTime");
            return (Criteria) this;
        }

        public Criteria andAuditTimeNotEqualTo(Date value) {
            addCriterion("audit_time <>", value, "auditTime");
            return (Criteria) this;
        }

        public Criteria andAuditTimeGreaterThan(Date value) {
            addCriterion("audit_time >", value, "auditTime");
            return (Criteria) this;
        }

        public Criteria andAuditTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("audit_time >=", value, "auditTime");
            return (Criteria) this;
        }

        public Criteria andAuditTimeLessThan(Date value) {
            addCriterion("audit_time <", value, "auditTime");
            return (Criteria) this;
        }

        public Criteria andAuditTimeLessThanOrEqualTo(Date value) {
            addCriterion("audit_time <=", value, "auditTime");
            return (Criteria) this;
        }

        public Criteria andAuditTimeIn(List<Date> values) {
            addCriterion("audit_time in", values, "auditTime");
            return (Criteria) this;
        }

        public Criteria andAuditTimeNotIn(List<Date> values) {
            addCriterion("audit_time not in", values, "auditTime");
            return (Criteria) this;
        }

        public Criteria andAuditTimeBetween(Date value1, Date value2) {
            addCriterion("audit_time between", value1, value2, "auditTime");
            return (Criteria) this;
        }

        public Criteria andAuditTimeNotBetween(Date value1, Date value2) {
            addCriterion("audit_time not between", value1, value2, "auditTime");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerDeptCodeIsNull() {
            addCriterion("order_owner_dept_code is null");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerDeptCodeIsNotNull() {
            addCriterion("order_owner_dept_code is not null");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerDeptCodeEqualTo(String value) {
            addCriterion("order_owner_dept_code =", value, "orderOwnerDeptCode");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerDeptCodeNotEqualTo(String value) {
            addCriterion("order_owner_dept_code <>", value, "orderOwnerDeptCode");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerDeptCodeGreaterThan(String value) {
            addCriterion("order_owner_dept_code >", value, "orderOwnerDeptCode");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerDeptCodeGreaterThanOrEqualTo(String value) {
            addCriterion("order_owner_dept_code >=", value, "orderOwnerDeptCode");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerDeptCodeLessThan(String value) {
            addCriterion("order_owner_dept_code <", value, "orderOwnerDeptCode");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerDeptCodeLessThanOrEqualTo(String value) {
            addCriterion("order_owner_dept_code <=", value, "orderOwnerDeptCode");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerDeptCodeLike(String value) {
            addCriterion("order_owner_dept_code like", value, "orderOwnerDeptCode");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerDeptCodeNotLike(String value) {
            addCriterion("order_owner_dept_code not like", value, "orderOwnerDeptCode");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerDeptCodeIn(List<String> values) {
            addCriterion("order_owner_dept_code in", values, "orderOwnerDeptCode");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerDeptCodeNotIn(List<String> values) {
            addCriterion("order_owner_dept_code not in", values, "orderOwnerDeptCode");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerDeptCodeBetween(String value1, String value2) {
            addCriterion("order_owner_dept_code between", value1, value2, "orderOwnerDeptCode");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerDeptCodeNotBetween(String value1, String value2) {
            addCriterion("order_owner_dept_code not between", value1, value2, "orderOwnerDeptCode");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerDeptNameIsNull() {
            addCriterion("order_owner_dept_name is null");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerDeptNameIsNotNull() {
            addCriterion("order_owner_dept_name is not null");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerDeptNameEqualTo(String value) {
            addCriterion("order_owner_dept_name =", value, "orderOwnerDeptName");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerDeptNameNotEqualTo(String value) {
            addCriterion("order_owner_dept_name <>", value, "orderOwnerDeptName");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerDeptNameGreaterThan(String value) {
            addCriterion("order_owner_dept_name >", value, "orderOwnerDeptName");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerDeptNameGreaterThanOrEqualTo(String value) {
            addCriterion("order_owner_dept_name >=", value, "orderOwnerDeptName");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerDeptNameLessThan(String value) {
            addCriterion("order_owner_dept_name <", value, "orderOwnerDeptName");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerDeptNameLessThanOrEqualTo(String value) {
            addCriterion("order_owner_dept_name <=", value, "orderOwnerDeptName");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerDeptNameLike(String value) {
            addCriterion("order_owner_dept_name like", value, "orderOwnerDeptName");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerDeptNameNotLike(String value) {
            addCriterion("order_owner_dept_name not like", value, "orderOwnerDeptName");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerDeptNameIn(List<String> values) {
            addCriterion("order_owner_dept_name in", values, "orderOwnerDeptName");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerDeptNameNotIn(List<String> values) {
            addCriterion("order_owner_dept_name not in", values, "orderOwnerDeptName");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerDeptNameBetween(String value1, String value2) {
            addCriterion("order_owner_dept_name between", value1, value2, "orderOwnerDeptName");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerDeptNameNotBetween(String value1, String value2) {
            addCriterion("order_owner_dept_name not between", value1, value2, "orderOwnerDeptName");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerCompanyNameIsNull() {
            addCriterion("order_owner_company_name is null");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerCompanyNameIsNotNull() {
            addCriterion("order_owner_company_name is not null");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerCompanyNameEqualTo(String value) {
            addCriterion("order_owner_company_name =", value, "orderOwnerCompanyName");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerCompanyNameNotEqualTo(String value) {
            addCriterion("order_owner_company_name <>", value, "orderOwnerCompanyName");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerCompanyNameGreaterThan(String value) {
            addCriterion("order_owner_company_name >", value, "orderOwnerCompanyName");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerCompanyNameGreaterThanOrEqualTo(String value) {
            addCriterion("order_owner_company_name >=", value, "orderOwnerCompanyName");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerCompanyNameLessThan(String value) {
            addCriterion("order_owner_company_name <", value, "orderOwnerCompanyName");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerCompanyNameLessThanOrEqualTo(String value) {
            addCriterion("order_owner_company_name <=", value, "orderOwnerCompanyName");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerCompanyNameLike(String value) {
            addCriterion("order_owner_company_name like", value, "orderOwnerCompanyName");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerCompanyNameNotLike(String value) {
            addCriterion("order_owner_company_name not like", value, "orderOwnerCompanyName");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerCompanyNameIn(List<String> values) {
            addCriterion("order_owner_company_name in", values, "orderOwnerCompanyName");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerCompanyNameNotIn(List<String> values) {
            addCriterion("order_owner_company_name not in", values, "orderOwnerCompanyName");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerCompanyNameBetween(String value1, String value2) {
            addCriterion("order_owner_company_name between", value1, value2, "orderOwnerCompanyName");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerCompanyNameNotBetween(String value1, String value2) {
            addCriterion("order_owner_company_name not between", value1, value2, "orderOwnerCompanyName");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerCompanyIdIsNull() {
            addCriterion("order_owner_company_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerCompanyIdIsNotNull() {
            addCriterion("order_owner_company_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerCompanyIdEqualTo(Integer value) {
            addCriterion("order_owner_company_id =", value, "orderOwnerCompanyId");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerCompanyIdNotEqualTo(Integer value) {
            addCriterion("order_owner_company_id <>", value, "orderOwnerCompanyId");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerCompanyIdGreaterThan(Integer value) {
            addCriterion("order_owner_company_id >", value, "orderOwnerCompanyId");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerCompanyIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_owner_company_id >=", value, "orderOwnerCompanyId");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerCompanyIdLessThan(Integer value) {
            addCriterion("order_owner_company_id <", value, "orderOwnerCompanyId");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerCompanyIdLessThanOrEqualTo(Integer value) {
            addCriterion("order_owner_company_id <=", value, "orderOwnerCompanyId");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerCompanyIdIn(List<Integer> values) {
            addCriterion("order_owner_company_id in", values, "orderOwnerCompanyId");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerCompanyIdNotIn(List<Integer> values) {
            addCriterion("order_owner_company_id not in", values, "orderOwnerCompanyId");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerCompanyIdBetween(Integer value1, Integer value2) {
            addCriterion("order_owner_company_id between", value1, value2, "orderOwnerCompanyId");
            return (Criteria) this;
        }

        public Criteria andOrderOwnerCompanyIdNotBetween(Integer value1, Integer value2) {
            addCriterion("order_owner_company_id not between", value1, value2, "orderOwnerCompanyId");
            return (Criteria) this;
        }

        public Criteria andRejectReasonIsNull() {
            addCriterion("reject_reason is null");
            return (Criteria) this;
        }

        public Criteria andRejectReasonIsNotNull() {
            addCriterion("reject_reason is not null");
            return (Criteria) this;
        }

        public Criteria andRejectReasonEqualTo(String value) {
            addCriterion("reject_reason =", value, "rejectReason");
            return (Criteria) this;
        }

        public Criteria andRejectReasonNotEqualTo(String value) {
            addCriterion("reject_reason <>", value, "rejectReason");
            return (Criteria) this;
        }

        public Criteria andRejectReasonGreaterThan(String value) {
            addCriterion("reject_reason >", value, "rejectReason");
            return (Criteria) this;
        }

        public Criteria andRejectReasonGreaterThanOrEqualTo(String value) {
            addCriterion("reject_reason >=", value, "rejectReason");
            return (Criteria) this;
        }

        public Criteria andRejectReasonLessThan(String value) {
            addCriterion("reject_reason <", value, "rejectReason");
            return (Criteria) this;
        }

        public Criteria andRejectReasonLessThanOrEqualTo(String value) {
            addCriterion("reject_reason <=", value, "rejectReason");
            return (Criteria) this;
        }

        public Criteria andRejectReasonLike(String value) {
            addCriterion("reject_reason like", value, "rejectReason");
            return (Criteria) this;
        }

        public Criteria andRejectReasonNotLike(String value) {
            addCriterion("reject_reason not like", value, "rejectReason");
            return (Criteria) this;
        }

        public Criteria andRejectReasonIn(List<String> values) {
            addCriterion("reject_reason in", values, "rejectReason");
            return (Criteria) this;
        }

        public Criteria andRejectReasonNotIn(List<String> values) {
            addCriterion("reject_reason not in", values, "rejectReason");
            return (Criteria) this;
        }

        public Criteria andRejectReasonBetween(String value1, String value2) {
            addCriterion("reject_reason between", value1, value2, "rejectReason");
            return (Criteria) this;
        }

        public Criteria andRejectReasonNotBetween(String value1, String value2) {
            addCriterion("reject_reason not between", value1, value2, "rejectReason");
            return (Criteria) this;
        }

        public Criteria andCreateIdIsNull() {
            addCriterion("create_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateIdIsNotNull() {
            addCriterion("create_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateIdEqualTo(Integer value) {
            addCriterion("create_id =", value, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateIdNotEqualTo(Integer value) {
            addCriterion("create_id <>", value, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateIdGreaterThan(Integer value) {
            addCriterion("create_id >", value, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("create_id >=", value, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateIdLessThan(Integer value) {
            addCriterion("create_id <", value, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateIdLessThanOrEqualTo(Integer value) {
            addCriterion("create_id <=", value, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateIdIn(List<Integer> values) {
            addCriterion("create_id in", values, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateIdNotIn(List<Integer> values) {
            addCriterion("create_id not in", values, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateIdBetween(Integer value1, Integer value2) {
            addCriterion("create_id between", value1, value2, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateIdNotBetween(Integer value1, Integer value2) {
            addCriterion("create_id not between", value1, value2, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateNameIsNull() {
            addCriterion("create_name is null");
            return (Criteria) this;
        }

        public Criteria andCreateNameIsNotNull() {
            addCriterion("create_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreateNameEqualTo(String value) {
            addCriterion("create_name =", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameNotEqualTo(String value) {
            addCriterion("create_name <>", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameGreaterThan(String value) {
            addCriterion("create_name >", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameGreaterThanOrEqualTo(String value) {
            addCriterion("create_name >=", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameLessThan(String value) {
            addCriterion("create_name <", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameLessThanOrEqualTo(String value) {
            addCriterion("create_name <=", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameLike(String value) {
            addCriterion("create_name like", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameNotLike(String value) {
            addCriterion("create_name not like", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameIn(List<String> values) {
            addCriterion("create_name in", values, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameNotIn(List<String> values) {
            addCriterion("create_name not in", values, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameBetween(String value1, String value2) {
            addCriterion("create_name between", value1, value2, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameNotBetween(String value1, String value2) {
            addCriterion("create_name not between", value1, value2, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateIdIsNull() {
            addCriterion("update_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdateIdIsNotNull() {
            addCriterion("update_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateIdEqualTo(Integer value) {
            addCriterion("update_id =", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdNotEqualTo(Integer value) {
            addCriterion("update_id <>", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdGreaterThan(Integer value) {
            addCriterion("update_id >", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("update_id >=", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdLessThan(Integer value) {
            addCriterion("update_id <", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdLessThanOrEqualTo(Integer value) {
            addCriterion("update_id <=", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdIn(List<Integer> values) {
            addCriterion("update_id in", values, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdNotIn(List<Integer> values) {
            addCriterion("update_id not in", values, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdBetween(Integer value1, Integer value2) {
            addCriterion("update_id between", value1, value2, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdNotBetween(Integer value1, Integer value2) {
            addCriterion("update_id not between", value1, value2, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateNameIsNull() {
            addCriterion("update_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdateNameIsNotNull() {
            addCriterion("update_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateNameEqualTo(String value) {
            addCriterion("update_name =", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotEqualTo(String value) {
            addCriterion("update_name <>", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameGreaterThan(String value) {
            addCriterion("update_name >", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameGreaterThanOrEqualTo(String value) {
            addCriterion("update_name >=", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameLessThan(String value) {
            addCriterion("update_name <", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameLessThanOrEqualTo(String value) {
            addCriterion("update_name <=", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameLike(String value) {
            addCriterion("update_name like", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotLike(String value) {
            addCriterion("update_name not like", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameIn(List<String> values) {
            addCriterion("update_name in", values, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotIn(List<String> values) {
            addCriterion("update_name not in", values, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameBetween(String value1, String value2) {
            addCriterion("update_name between", value1, value2, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotBetween(String value1, String value2) {
            addCriterion("update_name not between", value1, value2, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    /**
     * so_audit_record
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * so_audit_record null
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}