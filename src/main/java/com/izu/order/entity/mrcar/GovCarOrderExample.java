package com.izu.order.entity.mrcar;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class GovCarOrderExample {
    /**
     * gov_car_order
     */
    protected String orderByClause;

    /**
     * gov_car_order
     */
    protected boolean distinct;

    /**
     * gov_car_order
     */
    protected List<Criteria> oredCriteria;

    /**
     *
     * @mbg.generated
     */
    public GovCarOrderExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * gov_car_order null
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andOrderNoIsNull() {
            addCriterion("order_no is null");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNotNull() {
            addCriterion("order_no is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNoEqualTo(String value) {
            addCriterion("order_no =", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotEqualTo(String value) {
            addCriterion("order_no <>", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThan(String value) {
            addCriterion("order_no >", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThanOrEqualTo(String value) {
            addCriterion("order_no >=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThan(String value) {
            addCriterion("order_no <", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThanOrEqualTo(String value) {
            addCriterion("order_no <=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLike(String value) {
            addCriterion("order_no like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotLike(String value) {
            addCriterion("order_no not like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoIn(List<String> values) {
            addCriterion("order_no in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotIn(List<String> values) {
            addCriterion("order_no not in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoBetween(String value1, String value2) {
            addCriterion("order_no between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotBetween(String value1, String value2) {
            addCriterion("order_no not between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andCreateIdIsNull() {
            addCriterion("create_id is null");
            return (Criteria) this;
        }

        public Criteria andCreateIdIsNotNull() {
            addCriterion("create_id is not null");
            return (Criteria) this;
        }

        public Criteria andCreateIdEqualTo(Integer value) {
            addCriterion("create_id =", value, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateIdNotEqualTo(Integer value) {
            addCriterion("create_id <>", value, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateIdGreaterThan(Integer value) {
            addCriterion("create_id >", value, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("create_id >=", value, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateIdLessThan(Integer value) {
            addCriterion("create_id <", value, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateIdLessThanOrEqualTo(Integer value) {
            addCriterion("create_id <=", value, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateIdIn(List<Integer> values) {
            addCriterion("create_id in", values, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateIdNotIn(List<Integer> values) {
            addCriterion("create_id not in", values, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateIdBetween(Integer value1, Integer value2) {
            addCriterion("create_id between", value1, value2, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateIdNotBetween(Integer value1, Integer value2) {
            addCriterion("create_id not between", value1, value2, "createId");
            return (Criteria) this;
        }

        public Criteria andCreateNameIsNull() {
            addCriterion("create_name is null");
            return (Criteria) this;
        }

        public Criteria andCreateNameIsNotNull() {
            addCriterion("create_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreateNameEqualTo(String value) {
            addCriterion("create_name =", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameNotEqualTo(String value) {
            addCriterion("create_name <>", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameGreaterThan(String value) {
            addCriterion("create_name >", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameGreaterThanOrEqualTo(String value) {
            addCriterion("create_name >=", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameLessThan(String value) {
            addCriterion("create_name <", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameLessThanOrEqualTo(String value) {
            addCriterion("create_name <=", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameLike(String value) {
            addCriterion("create_name like", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameNotLike(String value) {
            addCriterion("create_name not like", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameIn(List<String> values) {
            addCriterion("create_name in", values, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameNotIn(List<String> values) {
            addCriterion("create_name not in", values, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameBetween(String value1, String value2) {
            addCriterion("create_name between", value1, value2, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameNotBetween(String value1, String value2) {
            addCriterion("create_name not between", value1, value2, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateIdIsNull() {
            addCriterion("update_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdateIdIsNotNull() {
            addCriterion("update_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateIdEqualTo(Integer value) {
            addCriterion("update_id =", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdNotEqualTo(Integer value) {
            addCriterion("update_id <>", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdGreaterThan(Integer value) {
            addCriterion("update_id >", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("update_id >=", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdLessThan(Integer value) {
            addCriterion("update_id <", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdLessThanOrEqualTo(Integer value) {
            addCriterion("update_id <=", value, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdIn(List<Integer> values) {
            addCriterion("update_id in", values, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdNotIn(List<Integer> values) {
            addCriterion("update_id not in", values, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdBetween(Integer value1, Integer value2) {
            addCriterion("update_id between", value1, value2, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateIdNotBetween(Integer value1, Integer value2) {
            addCriterion("update_id not between", value1, value2, "updateId");
            return (Criteria) this;
        }

        public Criteria andUpdateNameIsNull() {
            addCriterion("update_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdateNameIsNotNull() {
            addCriterion("update_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateNameEqualTo(String value) {
            addCriterion("update_name =", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotEqualTo(String value) {
            addCriterion("update_name <>", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameGreaterThan(String value) {
            addCriterion("update_name >", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameGreaterThanOrEqualTo(String value) {
            addCriterion("update_name >=", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameLessThan(String value) {
            addCriterion("update_name <", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameLessThanOrEqualTo(String value) {
            addCriterion("update_name <=", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameLike(String value) {
            addCriterion("update_name like", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotLike(String value) {
            addCriterion("update_name not like", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameIn(List<String> values) {
            addCriterion("update_name in", values, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotIn(List<String> values) {
            addCriterion("update_name not in", values, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameBetween(String value1, String value2) {
            addCriterion("update_name between", value1, value2, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotBetween(String value1, String value2) {
            addCriterion("update_name not between", value1, value2, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andOrderDeviceSourceIsNull() {
            addCriterion("order_device_source is null");
            return (Criteria) this;
        }

        public Criteria andOrderDeviceSourceIsNotNull() {
            addCriterion("order_device_source is not null");
            return (Criteria) this;
        }

        public Criteria andOrderDeviceSourceEqualTo(String value) {
            addCriterion("order_device_source =", value, "orderDeviceSource");
            return (Criteria) this;
        }

        public Criteria andOrderDeviceSourceNotEqualTo(String value) {
            addCriterion("order_device_source <>", value, "orderDeviceSource");
            return (Criteria) this;
        }

        public Criteria andOrderDeviceSourceGreaterThan(String value) {
            addCriterion("order_device_source >", value, "orderDeviceSource");
            return (Criteria) this;
        }

        public Criteria andOrderDeviceSourceGreaterThanOrEqualTo(String value) {
            addCriterion("order_device_source >=", value, "orderDeviceSource");
            return (Criteria) this;
        }

        public Criteria andOrderDeviceSourceLessThan(String value) {
            addCriterion("order_device_source <", value, "orderDeviceSource");
            return (Criteria) this;
        }

        public Criteria andOrderDeviceSourceLessThanOrEqualTo(String value) {
            addCriterion("order_device_source <=", value, "orderDeviceSource");
            return (Criteria) this;
        }

        public Criteria andOrderDeviceSourceLike(String value) {
            addCriterion("order_device_source like", value, "orderDeviceSource");
            return (Criteria) this;
        }

        public Criteria andOrderDeviceSourceNotLike(String value) {
            addCriterion("order_device_source not like", value, "orderDeviceSource");
            return (Criteria) this;
        }

        public Criteria andOrderDeviceSourceIn(List<String> values) {
            addCriterion("order_device_source in", values, "orderDeviceSource");
            return (Criteria) this;
        }

        public Criteria andOrderDeviceSourceNotIn(List<String> values) {
            addCriterion("order_device_source not in", values, "orderDeviceSource");
            return (Criteria) this;
        }

        public Criteria andOrderDeviceSourceBetween(String value1, String value2) {
            addCriterion("order_device_source between", value1, value2, "orderDeviceSource");
            return (Criteria) this;
        }

        public Criteria andOrderDeviceSourceNotBetween(String value1, String value2) {
            addCriterion("order_device_source not between", value1, value2, "orderDeviceSource");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNull() {
            addCriterion("company_code is null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNotNull() {
            addCriterion("company_code is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeEqualTo(String value) {
            addCriterion("company_code =", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotEqualTo(String value) {
            addCriterion("company_code <>", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThan(String value) {
            addCriterion("company_code >", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("company_code >=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThan(String value) {
            addCriterion("company_code <", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("company_code <=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLike(String value) {
            addCriterion("company_code like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotLike(String value) {
            addCriterion("company_code not like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIn(List<String> values) {
            addCriterion("company_code in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotIn(List<String> values) {
            addCriterion("company_code not in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeBetween(String value1, String value2) {
            addCriterion("company_code between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("company_code not between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIsNull() {
            addCriterion("company_name is null");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIsNotNull() {
            addCriterion("company_name is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyNameEqualTo(String value) {
            addCriterion("company_name =", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotEqualTo(String value) {
            addCriterion("company_name <>", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameGreaterThan(String value) {
            addCriterion("company_name >", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameGreaterThanOrEqualTo(String value) {
            addCriterion("company_name >=", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLessThan(String value) {
            addCriterion("company_name <", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLessThanOrEqualTo(String value) {
            addCriterion("company_name <=", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLike(String value) {
            addCriterion("company_name like", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotLike(String value) {
            addCriterion("company_name not like", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIn(List<String> values) {
            addCriterion("company_name in", values, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotIn(List<String> values) {
            addCriterion("company_name not in", values, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameBetween(String value1, String value2) {
            addCriterion("company_name between", value1, value2, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotBetween(String value1, String value2) {
            addCriterion("company_name not between", value1, value2, "companyName");
            return (Criteria) this;
        }

        public Criteria andStructIdIsNull() {
            addCriterion("struct_id is null");
            return (Criteria) this;
        }

        public Criteria andStructIdIsNotNull() {
            addCriterion("struct_id is not null");
            return (Criteria) this;
        }

        public Criteria andStructIdEqualTo(Integer value) {
            addCriterion("struct_id =", value, "structId");
            return (Criteria) this;
        }

        public Criteria andStructIdNotEqualTo(Integer value) {
            addCriterion("struct_id <>", value, "structId");
            return (Criteria) this;
        }

        public Criteria andStructIdGreaterThan(Integer value) {
            addCriterion("struct_id >", value, "structId");
            return (Criteria) this;
        }

        public Criteria andStructIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("struct_id >=", value, "structId");
            return (Criteria) this;
        }

        public Criteria andStructIdLessThan(Integer value) {
            addCriterion("struct_id <", value, "structId");
            return (Criteria) this;
        }

        public Criteria andStructIdLessThanOrEqualTo(Integer value) {
            addCriterion("struct_id <=", value, "structId");
            return (Criteria) this;
        }

        public Criteria andStructIdIn(List<Integer> values) {
            addCriterion("struct_id in", values, "structId");
            return (Criteria) this;
        }

        public Criteria andStructIdNotIn(List<Integer> values) {
            addCriterion("struct_id not in", values, "structId");
            return (Criteria) this;
        }

        public Criteria andStructIdBetween(Integer value1, Integer value2) {
            addCriterion("struct_id between", value1, value2, "structId");
            return (Criteria) this;
        }

        public Criteria andStructIdNotBetween(Integer value1, Integer value2) {
            addCriterion("struct_id not between", value1, value2, "structId");
            return (Criteria) this;
        }

        public Criteria andStructCodeIsNull() {
            addCriterion("struct_code is null");
            return (Criteria) this;
        }

        public Criteria andStructCodeIsNotNull() {
            addCriterion("struct_code is not null");
            return (Criteria) this;
        }

        public Criteria andStructCodeEqualTo(String value) {
            addCriterion("struct_code =", value, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeNotEqualTo(String value) {
            addCriterion("struct_code <>", value, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeGreaterThan(String value) {
            addCriterion("struct_code >", value, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeGreaterThanOrEqualTo(String value) {
            addCriterion("struct_code >=", value, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeLessThan(String value) {
            addCriterion("struct_code <", value, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeLessThanOrEqualTo(String value) {
            addCriterion("struct_code <=", value, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeLike(String value) {
            addCriterion("struct_code like", value, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeNotLike(String value) {
            addCriterion("struct_code not like", value, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeIn(List<String> values) {
            addCriterion("struct_code in", values, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeNotIn(List<String> values) {
            addCriterion("struct_code not in", values, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeBetween(String value1, String value2) {
            addCriterion("struct_code between", value1, value2, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructCodeNotBetween(String value1, String value2) {
            addCriterion("struct_code not between", value1, value2, "structCode");
            return (Criteria) this;
        }

        public Criteria andStructNameIsNull() {
            addCriterion("struct_name is null");
            return (Criteria) this;
        }

        public Criteria andStructNameIsNotNull() {
            addCriterion("struct_name is not null");
            return (Criteria) this;
        }

        public Criteria andStructNameEqualTo(String value) {
            addCriterion("struct_name =", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameNotEqualTo(String value) {
            addCriterion("struct_name <>", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameGreaterThan(String value) {
            addCriterion("struct_name >", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameGreaterThanOrEqualTo(String value) {
            addCriterion("struct_name >=", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameLessThan(String value) {
            addCriterion("struct_name <", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameLessThanOrEqualTo(String value) {
            addCriterion("struct_name <=", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameLike(String value) {
            addCriterion("struct_name like", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameNotLike(String value) {
            addCriterion("struct_name not like", value, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameIn(List<String> values) {
            addCriterion("struct_name in", values, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameNotIn(List<String> values) {
            addCriterion("struct_name not in", values, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameBetween(String value1, String value2) {
            addCriterion("struct_name between", value1, value2, "structName");
            return (Criteria) this;
        }

        public Criteria andStructNameNotBetween(String value1, String value2) {
            addCriterion("struct_name not between", value1, value2, "structName");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNull() {
            addCriterion("customer_id is null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNotNull() {
            addCriterion("customer_id is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdEqualTo(Integer value) {
            addCriterion("customer_id =", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotEqualTo(Integer value) {
            addCriterion("customer_id <>", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThan(Integer value) {
            addCriterion("customer_id >", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("customer_id >=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThan(Integer value) {
            addCriterion("customer_id <", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThanOrEqualTo(Integer value) {
            addCriterion("customer_id <=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIn(List<Integer> values) {
            addCriterion("customer_id in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotIn(List<Integer> values) {
            addCriterion("customer_id not in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdBetween(Integer value1, Integer value2) {
            addCriterion("customer_id between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotBetween(Integer value1, Integer value2) {
            addCriterion("customer_id not between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNull() {
            addCriterion("customer_name is null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNotNull() {
            addCriterion("customer_name is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameEqualTo(String value) {
            addCriterion("customer_name =", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotEqualTo(String value) {
            addCriterion("customer_name <>", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThan(String value) {
            addCriterion("customer_name >", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThanOrEqualTo(String value) {
            addCriterion("customer_name >=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThan(String value) {
            addCriterion("customer_name <", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThanOrEqualTo(String value) {
            addCriterion("customer_name <=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLike(String value) {
            addCriterion("customer_name like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotLike(String value) {
            addCriterion("customer_name not like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIn(List<String> values) {
            addCriterion("customer_name in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotIn(List<String> values) {
            addCriterion("customer_name not in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameBetween(String value1, String value2) {
            addCriterion("customer_name between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotBetween(String value1, String value2) {
            addCriterion("customer_name not between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileIsNull() {
            addCriterion("customer_mobile is null");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileIsNotNull() {
            addCriterion("customer_mobile is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileEqualTo(String value) {
            addCriterion("customer_mobile =", value, "customerMobile");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileNotEqualTo(String value) {
            addCriterion("customer_mobile <>", value, "customerMobile");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileGreaterThan(String value) {
            addCriterion("customer_mobile >", value, "customerMobile");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileGreaterThanOrEqualTo(String value) {
            addCriterion("customer_mobile >=", value, "customerMobile");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileLessThan(String value) {
            addCriterion("customer_mobile <", value, "customerMobile");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileLessThanOrEqualTo(String value) {
            addCriterion("customer_mobile <=", value, "customerMobile");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileLike(String value) {
            addCriterion("customer_mobile like", value, "customerMobile");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileNotLike(String value) {
            addCriterion("customer_mobile not like", value, "customerMobile");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileIn(List<String> values) {
            addCriterion("customer_mobile in", values, "customerMobile");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileNotIn(List<String> values) {
            addCriterion("customer_mobile not in", values, "customerMobile");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileBetween(String value1, String value2) {
            addCriterion("customer_mobile between", value1, value2, "customerMobile");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileNotBetween(String value1, String value2) {
            addCriterion("customer_mobile not between", value1, value2, "customerMobile");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIsNull() {
            addCriterion("order_status is null");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIsNotNull() {
            addCriterion("order_status is not null");
            return (Criteria) this;
        }

        public Criteria andOrderStatusEqualTo(Byte value) {
            addCriterion("order_status =", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotEqualTo(Byte value) {
            addCriterion("order_status <>", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusGreaterThan(Byte value) {
            addCriterion("order_status >", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("order_status >=", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusLessThan(Byte value) {
            addCriterion("order_status <", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusLessThanOrEqualTo(Byte value) {
            addCriterion("order_status <=", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIn(List<Byte> values) {
            addCriterion("order_status in", values, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotIn(List<Byte> values) {
            addCriterion("order_status not in", values, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusBetween(Byte value1, Byte value2) {
            addCriterion("order_status between", value1, value2, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("order_status not between", value1, value2, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderSettleStatusIsNull() {
            addCriterion("order_settle_status is null");
            return (Criteria) this;
        }

        public Criteria andOrderSettleStatusIsNotNull() {
            addCriterion("order_settle_status is not null");
            return (Criteria) this;
        }

        public Criteria andOrderSettleStatusEqualTo(Byte value) {
            addCriterion("order_settle_status =", value, "orderSettleStatus");
            return (Criteria) this;
        }

        public Criteria andOrderSettleStatusNotEqualTo(Byte value) {
            addCriterion("order_settle_status <>", value, "orderSettleStatus");
            return (Criteria) this;
        }

        public Criteria andOrderSettleStatusGreaterThan(Byte value) {
            addCriterion("order_settle_status >", value, "orderSettleStatus");
            return (Criteria) this;
        }

        public Criteria andOrderSettleStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("order_settle_status >=", value, "orderSettleStatus");
            return (Criteria) this;
        }

        public Criteria andOrderSettleStatusLessThan(Byte value) {
            addCriterion("order_settle_status <", value, "orderSettleStatus");
            return (Criteria) this;
        }

        public Criteria andOrderSettleStatusLessThanOrEqualTo(Byte value) {
            addCriterion("order_settle_status <=", value, "orderSettleStatus");
            return (Criteria) this;
        }

        public Criteria andOrderSettleStatusIn(List<Byte> values) {
            addCriterion("order_settle_status in", values, "orderSettleStatus");
            return (Criteria) this;
        }

        public Criteria andOrderSettleStatusNotIn(List<Byte> values) {
            addCriterion("order_settle_status not in", values, "orderSettleStatus");
            return (Criteria) this;
        }

        public Criteria andOrderSettleStatusBetween(Byte value1, Byte value2) {
            addCriterion("order_settle_status between", value1, value2, "orderSettleStatus");
            return (Criteria) this;
        }

        public Criteria andOrderSettleStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("order_settle_status not between", value1, value2, "orderSettleStatus");
            return (Criteria) this;
        }

        public Criteria andVehicleIdIsNull() {
            addCriterion("vehicle_id is null");
            return (Criteria) this;
        }

        public Criteria andVehicleIdIsNotNull() {
            addCriterion("vehicle_id is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleIdEqualTo(Long value) {
            addCriterion("vehicle_id =", value, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdNotEqualTo(Long value) {
            addCriterion("vehicle_id <>", value, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdGreaterThan(Long value) {
            addCriterion("vehicle_id >", value, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdGreaterThanOrEqualTo(Long value) {
            addCriterion("vehicle_id >=", value, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdLessThan(Long value) {
            addCriterion("vehicle_id <", value, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdLessThanOrEqualTo(Long value) {
            addCriterion("vehicle_id <=", value, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdIn(List<Long> values) {
            addCriterion("vehicle_id in", values, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdNotIn(List<Long> values) {
            addCriterion("vehicle_id not in", values, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdBetween(Long value1, Long value2) {
            addCriterion("vehicle_id between", value1, value2, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdNotBetween(Long value1, Long value2) {
            addCriterion("vehicle_id not between", value1, value2, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleSerialNoIsNull() {
            addCriterion("vehicle_serial_no is null");
            return (Criteria) this;
        }

        public Criteria andVehicleSerialNoIsNotNull() {
            addCriterion("vehicle_serial_no is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleSerialNoEqualTo(String value) {
            addCriterion("vehicle_serial_no =", value, "vehicleSerialNo");
            return (Criteria) this;
        }

        public Criteria andVehicleSerialNoNotEqualTo(String value) {
            addCriterion("vehicle_serial_no <>", value, "vehicleSerialNo");
            return (Criteria) this;
        }

        public Criteria andVehicleSerialNoGreaterThan(String value) {
            addCriterion("vehicle_serial_no >", value, "vehicleSerialNo");
            return (Criteria) this;
        }

        public Criteria andVehicleSerialNoGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_serial_no >=", value, "vehicleSerialNo");
            return (Criteria) this;
        }

        public Criteria andVehicleSerialNoLessThan(String value) {
            addCriterion("vehicle_serial_no <", value, "vehicleSerialNo");
            return (Criteria) this;
        }

        public Criteria andVehicleSerialNoLessThanOrEqualTo(String value) {
            addCriterion("vehicle_serial_no <=", value, "vehicleSerialNo");
            return (Criteria) this;
        }

        public Criteria andVehicleSerialNoLike(String value) {
            addCriterion("vehicle_serial_no like", value, "vehicleSerialNo");
            return (Criteria) this;
        }

        public Criteria andVehicleSerialNoNotLike(String value) {
            addCriterion("vehicle_serial_no not like", value, "vehicleSerialNo");
            return (Criteria) this;
        }

        public Criteria andVehicleSerialNoIn(List<String> values) {
            addCriterion("vehicle_serial_no in", values, "vehicleSerialNo");
            return (Criteria) this;
        }

        public Criteria andVehicleSerialNoNotIn(List<String> values) {
            addCriterion("vehicle_serial_no not in", values, "vehicleSerialNo");
            return (Criteria) this;
        }

        public Criteria andVehicleSerialNoBetween(String value1, String value2) {
            addCriterion("vehicle_serial_no between", value1, value2, "vehicleSerialNo");
            return (Criteria) this;
        }

        public Criteria andVehicleSerialNoNotBetween(String value1, String value2) {
            addCriterion("vehicle_serial_no not between", value1, value2, "vehicleSerialNo");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseIsNull() {
            addCriterion("vehicle_license is null");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseIsNotNull() {
            addCriterion("vehicle_license is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseEqualTo(String value) {
            addCriterion("vehicle_license =", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseNotEqualTo(String value) {
            addCriterion("vehicle_license <>", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseGreaterThan(String value) {
            addCriterion("vehicle_license >", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_license >=", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseLessThan(String value) {
            addCriterion("vehicle_license <", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseLessThanOrEqualTo(String value) {
            addCriterion("vehicle_license <=", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseLike(String value) {
            addCriterion("vehicle_license like", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseNotLike(String value) {
            addCriterion("vehicle_license not like", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseIn(List<String> values) {
            addCriterion("vehicle_license in", values, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseNotIn(List<String> values) {
            addCriterion("vehicle_license not in", values, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseBetween(String value1, String value2) {
            addCriterion("vehicle_license between", value1, value2, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseNotBetween(String value1, String value2) {
            addCriterion("vehicle_license not between", value1, value2, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleVinIsNull() {
            addCriterion("vehicle_vin is null");
            return (Criteria) this;
        }

        public Criteria andVehicleVinIsNotNull() {
            addCriterion("vehicle_vin is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleVinEqualTo(String value) {
            addCriterion("vehicle_vin =", value, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinNotEqualTo(String value) {
            addCriterion("vehicle_vin <>", value, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinGreaterThan(String value) {
            addCriterion("vehicle_vin >", value, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_vin >=", value, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinLessThan(String value) {
            addCriterion("vehicle_vin <", value, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinLessThanOrEqualTo(String value) {
            addCriterion("vehicle_vin <=", value, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinLike(String value) {
            addCriterion("vehicle_vin like", value, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinNotLike(String value) {
            addCriterion("vehicle_vin not like", value, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinIn(List<String> values) {
            addCriterion("vehicle_vin in", values, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinNotIn(List<String> values) {
            addCriterion("vehicle_vin not in", values, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinBetween(String value1, String value2) {
            addCriterion("vehicle_vin between", value1, value2, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinNotBetween(String value1, String value2) {
            addCriterion("vehicle_vin not between", value1, value2, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeIsNull() {
            addCriterion("vehicle_brand_code is null");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeIsNotNull() {
            addCriterion("vehicle_brand_code is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeEqualTo(String value) {
            addCriterion("vehicle_brand_code =", value, "vehicleBrandCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeNotEqualTo(String value) {
            addCriterion("vehicle_brand_code <>", value, "vehicleBrandCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeGreaterThan(String value) {
            addCriterion("vehicle_brand_code >", value, "vehicleBrandCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_brand_code >=", value, "vehicleBrandCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeLessThan(String value) {
            addCriterion("vehicle_brand_code <", value, "vehicleBrandCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeLessThanOrEqualTo(String value) {
            addCriterion("vehicle_brand_code <=", value, "vehicleBrandCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeLike(String value) {
            addCriterion("vehicle_brand_code like", value, "vehicleBrandCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeNotLike(String value) {
            addCriterion("vehicle_brand_code not like", value, "vehicleBrandCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeIn(List<String> values) {
            addCriterion("vehicle_brand_code in", values, "vehicleBrandCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeNotIn(List<String> values) {
            addCriterion("vehicle_brand_code not in", values, "vehicleBrandCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeBetween(String value1, String value2) {
            addCriterion("vehicle_brand_code between", value1, value2, "vehicleBrandCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeNotBetween(String value1, String value2) {
            addCriterion("vehicle_brand_code not between", value1, value2, "vehicleBrandCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandNameIsNull() {
            addCriterion("vehicle_brand_name is null");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandNameIsNotNull() {
            addCriterion("vehicle_brand_name is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandNameEqualTo(String value) {
            addCriterion("vehicle_brand_name =", value, "vehicleBrandName");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandNameNotEqualTo(String value) {
            addCriterion("vehicle_brand_name <>", value, "vehicleBrandName");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandNameGreaterThan(String value) {
            addCriterion("vehicle_brand_name >", value, "vehicleBrandName");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandNameGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_brand_name >=", value, "vehicleBrandName");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandNameLessThan(String value) {
            addCriterion("vehicle_brand_name <", value, "vehicleBrandName");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandNameLessThanOrEqualTo(String value) {
            addCriterion("vehicle_brand_name <=", value, "vehicleBrandName");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandNameLike(String value) {
            addCriterion("vehicle_brand_name like", value, "vehicleBrandName");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandNameNotLike(String value) {
            addCriterion("vehicle_brand_name not like", value, "vehicleBrandName");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandNameIn(List<String> values) {
            addCriterion("vehicle_brand_name in", values, "vehicleBrandName");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandNameNotIn(List<String> values) {
            addCriterion("vehicle_brand_name not in", values, "vehicleBrandName");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandNameBetween(String value1, String value2) {
            addCriterion("vehicle_brand_name between", value1, value2, "vehicleBrandName");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandNameNotBetween(String value1, String value2) {
            addCriterion("vehicle_brand_name not between", value1, value2, "vehicleBrandName");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeIsNull() {
            addCriterion("vehicle_model_code is null");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeIsNotNull() {
            addCriterion("vehicle_model_code is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeEqualTo(String value) {
            addCriterion("vehicle_model_code =", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeNotEqualTo(String value) {
            addCriterion("vehicle_model_code <>", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeGreaterThan(String value) {
            addCriterion("vehicle_model_code >", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_model_code >=", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeLessThan(String value) {
            addCriterion("vehicle_model_code <", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeLessThanOrEqualTo(String value) {
            addCriterion("vehicle_model_code <=", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeLike(String value) {
            addCriterion("vehicle_model_code like", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeNotLike(String value) {
            addCriterion("vehicle_model_code not like", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeIn(List<String> values) {
            addCriterion("vehicle_model_code in", values, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeNotIn(List<String> values) {
            addCriterion("vehicle_model_code not in", values, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeBetween(String value1, String value2) {
            addCriterion("vehicle_model_code between", value1, value2, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeNotBetween(String value1, String value2) {
            addCriterion("vehicle_model_code not between", value1, value2, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameIsNull() {
            addCriterion("vehicle_model_name is null");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameIsNotNull() {
            addCriterion("vehicle_model_name is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameEqualTo(String value) {
            addCriterion("vehicle_model_name =", value, "vehicleModelName");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameNotEqualTo(String value) {
            addCriterion("vehicle_model_name <>", value, "vehicleModelName");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameGreaterThan(String value) {
            addCriterion("vehicle_model_name >", value, "vehicleModelName");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_model_name >=", value, "vehicleModelName");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameLessThan(String value) {
            addCriterion("vehicle_model_name <", value, "vehicleModelName");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameLessThanOrEqualTo(String value) {
            addCriterion("vehicle_model_name <=", value, "vehicleModelName");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameLike(String value) {
            addCriterion("vehicle_model_name like", value, "vehicleModelName");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameNotLike(String value) {
            addCriterion("vehicle_model_name not like", value, "vehicleModelName");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameIn(List<String> values) {
            addCriterion("vehicle_model_name in", values, "vehicleModelName");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameNotIn(List<String> values) {
            addCriterion("vehicle_model_name not in", values, "vehicleModelName");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameBetween(String value1, String value2) {
            addCriterion("vehicle_model_name between", value1, value2, "vehicleModelName");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameNotBetween(String value1, String value2) {
            addCriterion("vehicle_model_name not between", value1, value2, "vehicleModelName");
            return (Criteria) this;
        }

        public Criteria andVehicleStructIdIsNull() {
            addCriterion("vehicle_struct_id is null");
            return (Criteria) this;
        }

        public Criteria andVehicleStructIdIsNotNull() {
            addCriterion("vehicle_struct_id is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleStructIdEqualTo(Integer value) {
            addCriterion("vehicle_struct_id =", value, "vehicleStructId");
            return (Criteria) this;
        }

        public Criteria andVehicleStructIdNotEqualTo(Integer value) {
            addCriterion("vehicle_struct_id <>", value, "vehicleStructId");
            return (Criteria) this;
        }

        public Criteria andVehicleStructIdGreaterThan(Integer value) {
            addCriterion("vehicle_struct_id >", value, "vehicleStructId");
            return (Criteria) this;
        }

        public Criteria andVehicleStructIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("vehicle_struct_id >=", value, "vehicleStructId");
            return (Criteria) this;
        }

        public Criteria andVehicleStructIdLessThan(Integer value) {
            addCriterion("vehicle_struct_id <", value, "vehicleStructId");
            return (Criteria) this;
        }

        public Criteria andVehicleStructIdLessThanOrEqualTo(Integer value) {
            addCriterion("vehicle_struct_id <=", value, "vehicleStructId");
            return (Criteria) this;
        }

        public Criteria andVehicleStructIdIn(List<Integer> values) {
            addCriterion("vehicle_struct_id in", values, "vehicleStructId");
            return (Criteria) this;
        }

        public Criteria andVehicleStructIdNotIn(List<Integer> values) {
            addCriterion("vehicle_struct_id not in", values, "vehicleStructId");
            return (Criteria) this;
        }

        public Criteria andVehicleStructIdBetween(Integer value1, Integer value2) {
            addCriterion("vehicle_struct_id between", value1, value2, "vehicleStructId");
            return (Criteria) this;
        }

        public Criteria andVehicleStructIdNotBetween(Integer value1, Integer value2) {
            addCriterion("vehicle_struct_id not between", value1, value2, "vehicleStructId");
            return (Criteria) this;
        }

        public Criteria andVehicleStructCodeIsNull() {
            addCriterion("vehicle_struct_code is null");
            return (Criteria) this;
        }

        public Criteria andVehicleStructCodeIsNotNull() {
            addCriterion("vehicle_struct_code is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleStructCodeEqualTo(String value) {
            addCriterion("vehicle_struct_code =", value, "vehicleStructCode");
            return (Criteria) this;
        }

        public Criteria andVehicleStructCodeNotEqualTo(String value) {
            addCriterion("vehicle_struct_code <>", value, "vehicleStructCode");
            return (Criteria) this;
        }

        public Criteria andVehicleStructCodeGreaterThan(String value) {
            addCriterion("vehicle_struct_code >", value, "vehicleStructCode");
            return (Criteria) this;
        }

        public Criteria andVehicleStructCodeGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_struct_code >=", value, "vehicleStructCode");
            return (Criteria) this;
        }

        public Criteria andVehicleStructCodeLessThan(String value) {
            addCriterion("vehicle_struct_code <", value, "vehicleStructCode");
            return (Criteria) this;
        }

        public Criteria andVehicleStructCodeLessThanOrEqualTo(String value) {
            addCriterion("vehicle_struct_code <=", value, "vehicleStructCode");
            return (Criteria) this;
        }

        public Criteria andVehicleStructCodeLike(String value) {
            addCriterion("vehicle_struct_code like", value, "vehicleStructCode");
            return (Criteria) this;
        }

        public Criteria andVehicleStructCodeNotLike(String value) {
            addCriterion("vehicle_struct_code not like", value, "vehicleStructCode");
            return (Criteria) this;
        }

        public Criteria andVehicleStructCodeIn(List<String> values) {
            addCriterion("vehicle_struct_code in", values, "vehicleStructCode");
            return (Criteria) this;
        }

        public Criteria andVehicleStructCodeNotIn(List<String> values) {
            addCriterion("vehicle_struct_code not in", values, "vehicleStructCode");
            return (Criteria) this;
        }

        public Criteria andVehicleStructCodeBetween(String value1, String value2) {
            addCriterion("vehicle_struct_code between", value1, value2, "vehicleStructCode");
            return (Criteria) this;
        }

        public Criteria andVehicleStructCodeNotBetween(String value1, String value2) {
            addCriterion("vehicle_struct_code not between", value1, value2, "vehicleStructCode");
            return (Criteria) this;
        }

        public Criteria andVehicleStructNameIsNull() {
            addCriterion("vehicle_struct_name is null");
            return (Criteria) this;
        }

        public Criteria andVehicleStructNameIsNotNull() {
            addCriterion("vehicle_struct_name is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleStructNameEqualTo(String value) {
            addCriterion("vehicle_struct_name =", value, "vehicleStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleStructNameNotEqualTo(String value) {
            addCriterion("vehicle_struct_name <>", value, "vehicleStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleStructNameGreaterThan(String value) {
            addCriterion("vehicle_struct_name >", value, "vehicleStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleStructNameGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_struct_name >=", value, "vehicleStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleStructNameLessThan(String value) {
            addCriterion("vehicle_struct_name <", value, "vehicleStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleStructNameLessThanOrEqualTo(String value) {
            addCriterion("vehicle_struct_name <=", value, "vehicleStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleStructNameLike(String value) {
            addCriterion("vehicle_struct_name like", value, "vehicleStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleStructNameNotLike(String value) {
            addCriterion("vehicle_struct_name not like", value, "vehicleStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleStructNameIn(List<String> values) {
            addCriterion("vehicle_struct_name in", values, "vehicleStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleStructNameNotIn(List<String> values) {
            addCriterion("vehicle_struct_name not in", values, "vehicleStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleStructNameBetween(String value1, String value2) {
            addCriterion("vehicle_struct_name between", value1, value2, "vehicleStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleStructNameNotBetween(String value1, String value2) {
            addCriterion("vehicle_struct_name not between", value1, value2, "vehicleStructName");
            return (Criteria) this;
        }

        public Criteria andVehiclePicUrlIsNull() {
            addCriterion("vehicle_pic_url is null");
            return (Criteria) this;
        }

        public Criteria andVehiclePicUrlIsNotNull() {
            addCriterion("vehicle_pic_url is not null");
            return (Criteria) this;
        }

        public Criteria andVehiclePicUrlEqualTo(String value) {
            addCriterion("vehicle_pic_url =", value, "vehiclePicUrl");
            return (Criteria) this;
        }

        public Criteria andVehiclePicUrlNotEqualTo(String value) {
            addCriterion("vehicle_pic_url <>", value, "vehiclePicUrl");
            return (Criteria) this;
        }

        public Criteria andVehiclePicUrlGreaterThan(String value) {
            addCriterion("vehicle_pic_url >", value, "vehiclePicUrl");
            return (Criteria) this;
        }

        public Criteria andVehiclePicUrlGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_pic_url >=", value, "vehiclePicUrl");
            return (Criteria) this;
        }

        public Criteria andVehiclePicUrlLessThan(String value) {
            addCriterion("vehicle_pic_url <", value, "vehiclePicUrl");
            return (Criteria) this;
        }

        public Criteria andVehiclePicUrlLessThanOrEqualTo(String value) {
            addCriterion("vehicle_pic_url <=", value, "vehiclePicUrl");
            return (Criteria) this;
        }

        public Criteria andVehiclePicUrlLike(String value) {
            addCriterion("vehicle_pic_url like", value, "vehiclePicUrl");
            return (Criteria) this;
        }

        public Criteria andVehiclePicUrlNotLike(String value) {
            addCriterion("vehicle_pic_url not like", value, "vehiclePicUrl");
            return (Criteria) this;
        }

        public Criteria andVehiclePicUrlIn(List<String> values) {
            addCriterion("vehicle_pic_url in", values, "vehiclePicUrl");
            return (Criteria) this;
        }

        public Criteria andVehiclePicUrlNotIn(List<String> values) {
            addCriterion("vehicle_pic_url not in", values, "vehiclePicUrl");
            return (Criteria) this;
        }

        public Criteria andVehiclePicUrlBetween(String value1, String value2) {
            addCriterion("vehicle_pic_url between", value1, value2, "vehiclePicUrl");
            return (Criteria) this;
        }

        public Criteria andVehiclePicUrlNotBetween(String value1, String value2) {
            addCriterion("vehicle_pic_url not between", value1, value2, "vehiclePicUrl");
            return (Criteria) this;
        }

        public Criteria andOrderUserMemoIsNull() {
            addCriterion("order_user_memo is null");
            return (Criteria) this;
        }

        public Criteria andOrderUserMemoIsNotNull() {
            addCriterion("order_user_memo is not null");
            return (Criteria) this;
        }

        public Criteria andOrderUserMemoEqualTo(String value) {
            addCriterion("order_user_memo =", value, "orderUserMemo");
            return (Criteria) this;
        }

        public Criteria andOrderUserMemoNotEqualTo(String value) {
            addCriterion("order_user_memo <>", value, "orderUserMemo");
            return (Criteria) this;
        }

        public Criteria andOrderUserMemoGreaterThan(String value) {
            addCriterion("order_user_memo >", value, "orderUserMemo");
            return (Criteria) this;
        }

        public Criteria andOrderUserMemoGreaterThanOrEqualTo(String value) {
            addCriterion("order_user_memo >=", value, "orderUserMemo");
            return (Criteria) this;
        }

        public Criteria andOrderUserMemoLessThan(String value) {
            addCriterion("order_user_memo <", value, "orderUserMemo");
            return (Criteria) this;
        }

        public Criteria andOrderUserMemoLessThanOrEqualTo(String value) {
            addCriterion("order_user_memo <=", value, "orderUserMemo");
            return (Criteria) this;
        }

        public Criteria andOrderUserMemoLike(String value) {
            addCriterion("order_user_memo like", value, "orderUserMemo");
            return (Criteria) this;
        }

        public Criteria andOrderUserMemoNotLike(String value) {
            addCriterion("order_user_memo not like", value, "orderUserMemo");
            return (Criteria) this;
        }

        public Criteria andOrderUserMemoIn(List<String> values) {
            addCriterion("order_user_memo in", values, "orderUserMemo");
            return (Criteria) this;
        }

        public Criteria andOrderUserMemoNotIn(List<String> values) {
            addCriterion("order_user_memo not in", values, "orderUserMemo");
            return (Criteria) this;
        }

        public Criteria andOrderUserMemoBetween(String value1, String value2) {
            addCriterion("order_user_memo between", value1, value2, "orderUserMemo");
            return (Criteria) this;
        }

        public Criteria andOrderUserMemoNotBetween(String value1, String value2) {
            addCriterion("order_user_memo not between", value1, value2, "orderUserMemo");
            return (Criteria) this;
        }

        public Criteria andInitialFenceIdIsNull() {
            addCriterion("initial_fence_id is null");
            return (Criteria) this;
        }

        public Criteria andInitialFenceIdIsNotNull() {
            addCriterion("initial_fence_id is not null");
            return (Criteria) this;
        }

        public Criteria andInitialFenceIdEqualTo(Integer value) {
            addCriterion("initial_fence_id =", value, "initialFenceId");
            return (Criteria) this;
        }

        public Criteria andInitialFenceIdNotEqualTo(Integer value) {
            addCriterion("initial_fence_id <>", value, "initialFenceId");
            return (Criteria) this;
        }

        public Criteria andInitialFenceIdGreaterThan(Integer value) {
            addCriterion("initial_fence_id >", value, "initialFenceId");
            return (Criteria) this;
        }

        public Criteria andInitialFenceIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("initial_fence_id >=", value, "initialFenceId");
            return (Criteria) this;
        }

        public Criteria andInitialFenceIdLessThan(Integer value) {
            addCriterion("initial_fence_id <", value, "initialFenceId");
            return (Criteria) this;
        }

        public Criteria andInitialFenceIdLessThanOrEqualTo(Integer value) {
            addCriterion("initial_fence_id <=", value, "initialFenceId");
            return (Criteria) this;
        }

        public Criteria andInitialFenceIdIn(List<Integer> values) {
            addCriterion("initial_fence_id in", values, "initialFenceId");
            return (Criteria) this;
        }

        public Criteria andInitialFenceIdNotIn(List<Integer> values) {
            addCriterion("initial_fence_id not in", values, "initialFenceId");
            return (Criteria) this;
        }

        public Criteria andInitialFenceIdBetween(Integer value1, Integer value2) {
            addCriterion("initial_fence_id between", value1, value2, "initialFenceId");
            return (Criteria) this;
        }

        public Criteria andInitialFenceIdNotBetween(Integer value1, Integer value2) {
            addCriterion("initial_fence_id not between", value1, value2, "initialFenceId");
            return (Criteria) this;
        }

        public Criteria andInitialLatitudeIsNull() {
            addCriterion("initial_latitude is null");
            return (Criteria) this;
        }

        public Criteria andInitialLatitudeIsNotNull() {
            addCriterion("initial_latitude is not null");
            return (Criteria) this;
        }

        public Criteria andInitialLatitudeEqualTo(BigDecimal value) {
            addCriterion("initial_latitude =", value, "initialLatitude");
            return (Criteria) this;
        }

        public Criteria andInitialLatitudeNotEqualTo(BigDecimal value) {
            addCriterion("initial_latitude <>", value, "initialLatitude");
            return (Criteria) this;
        }

        public Criteria andInitialLatitudeGreaterThan(BigDecimal value) {
            addCriterion("initial_latitude >", value, "initialLatitude");
            return (Criteria) this;
        }

        public Criteria andInitialLatitudeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("initial_latitude >=", value, "initialLatitude");
            return (Criteria) this;
        }

        public Criteria andInitialLatitudeLessThan(BigDecimal value) {
            addCriterion("initial_latitude <", value, "initialLatitude");
            return (Criteria) this;
        }

        public Criteria andInitialLatitudeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("initial_latitude <=", value, "initialLatitude");
            return (Criteria) this;
        }

        public Criteria andInitialLatitudeIn(List<BigDecimal> values) {
            addCriterion("initial_latitude in", values, "initialLatitude");
            return (Criteria) this;
        }

        public Criteria andInitialLatitudeNotIn(List<BigDecimal> values) {
            addCriterion("initial_latitude not in", values, "initialLatitude");
            return (Criteria) this;
        }

        public Criteria andInitialLatitudeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("initial_latitude between", value1, value2, "initialLatitude");
            return (Criteria) this;
        }

        public Criteria andInitialLatitudeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("initial_latitude not between", value1, value2, "initialLatitude");
            return (Criteria) this;
        }

        public Criteria andInitialLongitudeIsNull() {
            addCriterion("initial_longitude is null");
            return (Criteria) this;
        }

        public Criteria andInitialLongitudeIsNotNull() {
            addCriterion("initial_longitude is not null");
            return (Criteria) this;
        }

        public Criteria andInitialLongitudeEqualTo(BigDecimal value) {
            addCriterion("initial_longitude =", value, "initialLongitude");
            return (Criteria) this;
        }

        public Criteria andInitialLongitudeNotEqualTo(BigDecimal value) {
            addCriterion("initial_longitude <>", value, "initialLongitude");
            return (Criteria) this;
        }

        public Criteria andInitialLongitudeGreaterThan(BigDecimal value) {
            addCriterion("initial_longitude >", value, "initialLongitude");
            return (Criteria) this;
        }

        public Criteria andInitialLongitudeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("initial_longitude >=", value, "initialLongitude");
            return (Criteria) this;
        }

        public Criteria andInitialLongitudeLessThan(BigDecimal value) {
            addCriterion("initial_longitude <", value, "initialLongitude");
            return (Criteria) this;
        }

        public Criteria andInitialLongitudeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("initial_longitude <=", value, "initialLongitude");
            return (Criteria) this;
        }

        public Criteria andInitialLongitudeIn(List<BigDecimal> values) {
            addCriterion("initial_longitude in", values, "initialLongitude");
            return (Criteria) this;
        }

        public Criteria andInitialLongitudeNotIn(List<BigDecimal> values) {
            addCriterion("initial_longitude not in", values, "initialLongitude");
            return (Criteria) this;
        }

        public Criteria andInitialLongitudeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("initial_longitude between", value1, value2, "initialLongitude");
            return (Criteria) this;
        }

        public Criteria andInitialLongitudeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("initial_longitude not between", value1, value2, "initialLongitude");
            return (Criteria) this;
        }

        public Criteria andInitialLocationIsNull() {
            addCriterion("initial_location is null");
            return (Criteria) this;
        }

        public Criteria andInitialLocationIsNotNull() {
            addCriterion("initial_location is not null");
            return (Criteria) this;
        }

        public Criteria andInitialLocationEqualTo(String value) {
            addCriterion("initial_location =", value, "initialLocation");
            return (Criteria) this;
        }

        public Criteria andInitialLocationNotEqualTo(String value) {
            addCriterion("initial_location <>", value, "initialLocation");
            return (Criteria) this;
        }

        public Criteria andInitialLocationGreaterThan(String value) {
            addCriterion("initial_location >", value, "initialLocation");
            return (Criteria) this;
        }

        public Criteria andInitialLocationGreaterThanOrEqualTo(String value) {
            addCriterion("initial_location >=", value, "initialLocation");
            return (Criteria) this;
        }

        public Criteria andInitialLocationLessThan(String value) {
            addCriterion("initial_location <", value, "initialLocation");
            return (Criteria) this;
        }

        public Criteria andInitialLocationLessThanOrEqualTo(String value) {
            addCriterion("initial_location <=", value, "initialLocation");
            return (Criteria) this;
        }

        public Criteria andInitialLocationLike(String value) {
            addCriterion("initial_location like", value, "initialLocation");
            return (Criteria) this;
        }

        public Criteria andInitialLocationNotLike(String value) {
            addCriterion("initial_location not like", value, "initialLocation");
            return (Criteria) this;
        }

        public Criteria andInitialLocationIn(List<String> values) {
            addCriterion("initial_location in", values, "initialLocation");
            return (Criteria) this;
        }

        public Criteria andInitialLocationNotIn(List<String> values) {
            addCriterion("initial_location not in", values, "initialLocation");
            return (Criteria) this;
        }

        public Criteria andInitialLocationBetween(String value1, String value2) {
            addCriterion("initial_location between", value1, value2, "initialLocation");
            return (Criteria) this;
        }

        public Criteria andInitialLocationNotBetween(String value1, String value2) {
            addCriterion("initial_location not between", value1, value2, "initialLocation");
            return (Criteria) this;
        }

        public Criteria andReturnFenceIdIsNull() {
            addCriterion("return_fence_id is null");
            return (Criteria) this;
        }

        public Criteria andReturnFenceIdIsNotNull() {
            addCriterion("return_fence_id is not null");
            return (Criteria) this;
        }

        public Criteria andReturnFenceIdEqualTo(Integer value) {
            addCriterion("return_fence_id =", value, "returnFenceId");
            return (Criteria) this;
        }

        public Criteria andReturnFenceIdNotEqualTo(Integer value) {
            addCriterion("return_fence_id <>", value, "returnFenceId");
            return (Criteria) this;
        }

        public Criteria andReturnFenceIdGreaterThan(Integer value) {
            addCriterion("return_fence_id >", value, "returnFenceId");
            return (Criteria) this;
        }

        public Criteria andReturnFenceIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("return_fence_id >=", value, "returnFenceId");
            return (Criteria) this;
        }

        public Criteria andReturnFenceIdLessThan(Integer value) {
            addCriterion("return_fence_id <", value, "returnFenceId");
            return (Criteria) this;
        }

        public Criteria andReturnFenceIdLessThanOrEqualTo(Integer value) {
            addCriterion("return_fence_id <=", value, "returnFenceId");
            return (Criteria) this;
        }

        public Criteria andReturnFenceIdIn(List<Integer> values) {
            addCriterion("return_fence_id in", values, "returnFenceId");
            return (Criteria) this;
        }

        public Criteria andReturnFenceIdNotIn(List<Integer> values) {
            addCriterion("return_fence_id not in", values, "returnFenceId");
            return (Criteria) this;
        }

        public Criteria andReturnFenceIdBetween(Integer value1, Integer value2) {
            addCriterion("return_fence_id between", value1, value2, "returnFenceId");
            return (Criteria) this;
        }

        public Criteria andReturnFenceIdNotBetween(Integer value1, Integer value2) {
            addCriterion("return_fence_id not between", value1, value2, "returnFenceId");
            return (Criteria) this;
        }

        public Criteria andReturnLatitudeIsNull() {
            addCriterion("return_latitude is null");
            return (Criteria) this;
        }

        public Criteria andReturnLatitudeIsNotNull() {
            addCriterion("return_latitude is not null");
            return (Criteria) this;
        }

        public Criteria andReturnLatitudeEqualTo(BigDecimal value) {
            addCriterion("return_latitude =", value, "returnLatitude");
            return (Criteria) this;
        }

        public Criteria andReturnLatitudeNotEqualTo(BigDecimal value) {
            addCriterion("return_latitude <>", value, "returnLatitude");
            return (Criteria) this;
        }

        public Criteria andReturnLatitudeGreaterThan(BigDecimal value) {
            addCriterion("return_latitude >", value, "returnLatitude");
            return (Criteria) this;
        }

        public Criteria andReturnLatitudeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("return_latitude >=", value, "returnLatitude");
            return (Criteria) this;
        }

        public Criteria andReturnLatitudeLessThan(BigDecimal value) {
            addCriterion("return_latitude <", value, "returnLatitude");
            return (Criteria) this;
        }

        public Criteria andReturnLatitudeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("return_latitude <=", value, "returnLatitude");
            return (Criteria) this;
        }

        public Criteria andReturnLatitudeIn(List<BigDecimal> values) {
            addCriterion("return_latitude in", values, "returnLatitude");
            return (Criteria) this;
        }

        public Criteria andReturnLatitudeNotIn(List<BigDecimal> values) {
            addCriterion("return_latitude not in", values, "returnLatitude");
            return (Criteria) this;
        }

        public Criteria andReturnLatitudeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("return_latitude between", value1, value2, "returnLatitude");
            return (Criteria) this;
        }

        public Criteria andReturnLatitudeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("return_latitude not between", value1, value2, "returnLatitude");
            return (Criteria) this;
        }

        public Criteria andReturnLongitudeIsNull() {
            addCriterion("return_longitude is null");
            return (Criteria) this;
        }

        public Criteria andReturnLongitudeIsNotNull() {
            addCriterion("return_longitude is not null");
            return (Criteria) this;
        }

        public Criteria andReturnLongitudeEqualTo(BigDecimal value) {
            addCriterion("return_longitude =", value, "returnLongitude");
            return (Criteria) this;
        }

        public Criteria andReturnLongitudeNotEqualTo(BigDecimal value) {
            addCriterion("return_longitude <>", value, "returnLongitude");
            return (Criteria) this;
        }

        public Criteria andReturnLongitudeGreaterThan(BigDecimal value) {
            addCriterion("return_longitude >", value, "returnLongitude");
            return (Criteria) this;
        }

        public Criteria andReturnLongitudeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("return_longitude >=", value, "returnLongitude");
            return (Criteria) this;
        }

        public Criteria andReturnLongitudeLessThan(BigDecimal value) {
            addCriterion("return_longitude <", value, "returnLongitude");
            return (Criteria) this;
        }

        public Criteria andReturnLongitudeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("return_longitude <=", value, "returnLongitude");
            return (Criteria) this;
        }

        public Criteria andReturnLongitudeIn(List<BigDecimal> values) {
            addCriterion("return_longitude in", values, "returnLongitude");
            return (Criteria) this;
        }

        public Criteria andReturnLongitudeNotIn(List<BigDecimal> values) {
            addCriterion("return_longitude not in", values, "returnLongitude");
            return (Criteria) this;
        }

        public Criteria andReturnLongitudeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("return_longitude between", value1, value2, "returnLongitude");
            return (Criteria) this;
        }

        public Criteria andReturnLongitudeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("return_longitude not between", value1, value2, "returnLongitude");
            return (Criteria) this;
        }

        public Criteria andReturnLocationIsNull() {
            addCriterion("return_location is null");
            return (Criteria) this;
        }

        public Criteria andReturnLocationIsNotNull() {
            addCriterion("return_location is not null");
            return (Criteria) this;
        }

        public Criteria andReturnLocationEqualTo(String value) {
            addCriterion("return_location =", value, "returnLocation");
            return (Criteria) this;
        }

        public Criteria andReturnLocationNotEqualTo(String value) {
            addCriterion("return_location <>", value, "returnLocation");
            return (Criteria) this;
        }

        public Criteria andReturnLocationGreaterThan(String value) {
            addCriterion("return_location >", value, "returnLocation");
            return (Criteria) this;
        }

        public Criteria andReturnLocationGreaterThanOrEqualTo(String value) {
            addCriterion("return_location >=", value, "returnLocation");
            return (Criteria) this;
        }

        public Criteria andReturnLocationLessThan(String value) {
            addCriterion("return_location <", value, "returnLocation");
            return (Criteria) this;
        }

        public Criteria andReturnLocationLessThanOrEqualTo(String value) {
            addCriterion("return_location <=", value, "returnLocation");
            return (Criteria) this;
        }

        public Criteria andReturnLocationLike(String value) {
            addCriterion("return_location like", value, "returnLocation");
            return (Criteria) this;
        }

        public Criteria andReturnLocationNotLike(String value) {
            addCriterion("return_location not like", value, "returnLocation");
            return (Criteria) this;
        }

        public Criteria andReturnLocationIn(List<String> values) {
            addCriterion("return_location in", values, "returnLocation");
            return (Criteria) this;
        }

        public Criteria andReturnLocationNotIn(List<String> values) {
            addCriterion("return_location not in", values, "returnLocation");
            return (Criteria) this;
        }

        public Criteria andReturnLocationBetween(String value1, String value2) {
            addCriterion("return_location between", value1, value2, "returnLocation");
            return (Criteria) this;
        }

        public Criteria andReturnLocationNotBetween(String value1, String value2) {
            addCriterion("return_location not between", value1, value2, "returnLocation");
            return (Criteria) this;
        }

        public Criteria andOrderPaymentTimeIsNull() {
            addCriterion("order_payment_time is null");
            return (Criteria) this;
        }

        public Criteria andOrderPaymentTimeIsNotNull() {
            addCriterion("order_payment_time is not null");
            return (Criteria) this;
        }

        public Criteria andOrderPaymentTimeEqualTo(Date value) {
            addCriterion("order_payment_time =", value, "orderPaymentTime");
            return (Criteria) this;
        }

        public Criteria andOrderPaymentTimeNotEqualTo(Date value) {
            addCriterion("order_payment_time <>", value, "orderPaymentTime");
            return (Criteria) this;
        }

        public Criteria andOrderPaymentTimeGreaterThan(Date value) {
            addCriterion("order_payment_time >", value, "orderPaymentTime");
            return (Criteria) this;
        }

        public Criteria andOrderPaymentTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("order_payment_time >=", value, "orderPaymentTime");
            return (Criteria) this;
        }

        public Criteria andOrderPaymentTimeLessThan(Date value) {
            addCriterion("order_payment_time <", value, "orderPaymentTime");
            return (Criteria) this;
        }

        public Criteria andOrderPaymentTimeLessThanOrEqualTo(Date value) {
            addCriterion("order_payment_time <=", value, "orderPaymentTime");
            return (Criteria) this;
        }

        public Criteria andOrderPaymentTimeIn(List<Date> values) {
            addCriterion("order_payment_time in", values, "orderPaymentTime");
            return (Criteria) this;
        }

        public Criteria andOrderPaymentTimeNotIn(List<Date> values) {
            addCriterion("order_payment_time not in", values, "orderPaymentTime");
            return (Criteria) this;
        }

        public Criteria andOrderPaymentTimeBetween(Date value1, Date value2) {
            addCriterion("order_payment_time between", value1, value2, "orderPaymentTime");
            return (Criteria) this;
        }

        public Criteria andOrderPaymentTimeNotBetween(Date value1, Date value2) {
            addCriterion("order_payment_time not between", value1, value2, "orderPaymentTime");
            return (Criteria) this;
        }

        public Criteria andConfirmTimeIsNull() {
            addCriterion("confirm_time is null");
            return (Criteria) this;
        }

        public Criteria andConfirmTimeIsNotNull() {
            addCriterion("confirm_time is not null");
            return (Criteria) this;
        }

        public Criteria andConfirmTimeEqualTo(Date value) {
            addCriterion("confirm_time =", value, "confirmTime");
            return (Criteria) this;
        }

        public Criteria andConfirmTimeNotEqualTo(Date value) {
            addCriterion("confirm_time <>", value, "confirmTime");
            return (Criteria) this;
        }

        public Criteria andConfirmTimeGreaterThan(Date value) {
            addCriterion("confirm_time >", value, "confirmTime");
            return (Criteria) this;
        }

        public Criteria andConfirmTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("confirm_time >=", value, "confirmTime");
            return (Criteria) this;
        }

        public Criteria andConfirmTimeLessThan(Date value) {
            addCriterion("confirm_time <", value, "confirmTime");
            return (Criteria) this;
        }

        public Criteria andConfirmTimeLessThanOrEqualTo(Date value) {
            addCriterion("confirm_time <=", value, "confirmTime");
            return (Criteria) this;
        }

        public Criteria andConfirmTimeIn(List<Date> values) {
            addCriterion("confirm_time in", values, "confirmTime");
            return (Criteria) this;
        }

        public Criteria andConfirmTimeNotIn(List<Date> values) {
            addCriterion("confirm_time not in", values, "confirmTime");
            return (Criteria) this;
        }

        public Criteria andConfirmTimeBetween(Date value1, Date value2) {
            addCriterion("confirm_time between", value1, value2, "confirmTime");
            return (Criteria) this;
        }

        public Criteria andConfirmTimeNotBetween(Date value1, Date value2) {
            addCriterion("confirm_time not between", value1, value2, "confirmTime");
            return (Criteria) this;
        }

        public Criteria andExpectedPickupTimeIsNull() {
            addCriterion("expected_pickup_time is null");
            return (Criteria) this;
        }

        public Criteria andExpectedPickupTimeIsNotNull() {
            addCriterion("expected_pickup_time is not null");
            return (Criteria) this;
        }

        public Criteria andExpectedPickupTimeEqualTo(Date value) {
            addCriterion("expected_pickup_time =", value, "expectedPickupTime");
            return (Criteria) this;
        }

        public Criteria andExpectedPickupTimeNotEqualTo(Date value) {
            addCriterion("expected_pickup_time <>", value, "expectedPickupTime");
            return (Criteria) this;
        }

        public Criteria andExpectedPickupTimeGreaterThan(Date value) {
            addCriterion("expected_pickup_time >", value, "expectedPickupTime");
            return (Criteria) this;
        }

        public Criteria andExpectedPickupTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("expected_pickup_time >=", value, "expectedPickupTime");
            return (Criteria) this;
        }

        public Criteria andExpectedPickupTimeLessThan(Date value) {
            addCriterion("expected_pickup_time <", value, "expectedPickupTime");
            return (Criteria) this;
        }

        public Criteria andExpectedPickupTimeLessThanOrEqualTo(Date value) {
            addCriterion("expected_pickup_time <=", value, "expectedPickupTime");
            return (Criteria) this;
        }

        public Criteria andExpectedPickupTimeIn(List<Date> values) {
            addCriterion("expected_pickup_time in", values, "expectedPickupTime");
            return (Criteria) this;
        }

        public Criteria andExpectedPickupTimeNotIn(List<Date> values) {
            addCriterion("expected_pickup_time not in", values, "expectedPickupTime");
            return (Criteria) this;
        }

        public Criteria andExpectedPickupTimeBetween(Date value1, Date value2) {
            addCriterion("expected_pickup_time between", value1, value2, "expectedPickupTime");
            return (Criteria) this;
        }

        public Criteria andExpectedPickupTimeNotBetween(Date value1, Date value2) {
            addCriterion("expected_pickup_time not between", value1, value2, "expectedPickupTime");
            return (Criteria) this;
        }

        public Criteria andExpectedReturnTimeIsNull() {
            addCriterion("expected_return_time is null");
            return (Criteria) this;
        }

        public Criteria andExpectedReturnTimeIsNotNull() {
            addCriterion("expected_return_time is not null");
            return (Criteria) this;
        }

        public Criteria andExpectedReturnTimeEqualTo(Date value) {
            addCriterion("expected_return_time =", value, "expectedReturnTime");
            return (Criteria) this;
        }

        public Criteria andExpectedReturnTimeNotEqualTo(Date value) {
            addCriterion("expected_return_time <>", value, "expectedReturnTime");
            return (Criteria) this;
        }

        public Criteria andExpectedReturnTimeGreaterThan(Date value) {
            addCriterion("expected_return_time >", value, "expectedReturnTime");
            return (Criteria) this;
        }

        public Criteria andExpectedReturnTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("expected_return_time >=", value, "expectedReturnTime");
            return (Criteria) this;
        }

        public Criteria andExpectedReturnTimeLessThan(Date value) {
            addCriterion("expected_return_time <", value, "expectedReturnTime");
            return (Criteria) this;
        }

        public Criteria andExpectedReturnTimeLessThanOrEqualTo(Date value) {
            addCriterion("expected_return_time <=", value, "expectedReturnTime");
            return (Criteria) this;
        }

        public Criteria andExpectedReturnTimeIn(List<Date> values) {
            addCriterion("expected_return_time in", values, "expectedReturnTime");
            return (Criteria) this;
        }

        public Criteria andExpectedReturnTimeNotIn(List<Date> values) {
            addCriterion("expected_return_time not in", values, "expectedReturnTime");
            return (Criteria) this;
        }

        public Criteria andExpectedReturnTimeBetween(Date value1, Date value2) {
            addCriterion("expected_return_time between", value1, value2, "expectedReturnTime");
            return (Criteria) this;
        }

        public Criteria andExpectedReturnTimeNotBetween(Date value1, Date value2) {
            addCriterion("expected_return_time not between", value1, value2, "expectedReturnTime");
            return (Criteria) this;
        }

        public Criteria andOperationPickupTimeIsNull() {
            addCriterion("operation_pickup_time is null");
            return (Criteria) this;
        }

        public Criteria andOperationPickupTimeIsNotNull() {
            addCriterion("operation_pickup_time is not null");
            return (Criteria) this;
        }

        public Criteria andOperationPickupTimeEqualTo(Date value) {
            addCriterion("operation_pickup_time =", value, "operationPickupTime");
            return (Criteria) this;
        }

        public Criteria andOperationPickupTimeNotEqualTo(Date value) {
            addCriterion("operation_pickup_time <>", value, "operationPickupTime");
            return (Criteria) this;
        }

        public Criteria andOperationPickupTimeGreaterThan(Date value) {
            addCriterion("operation_pickup_time >", value, "operationPickupTime");
            return (Criteria) this;
        }

        public Criteria andOperationPickupTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("operation_pickup_time >=", value, "operationPickupTime");
            return (Criteria) this;
        }

        public Criteria andOperationPickupTimeLessThan(Date value) {
            addCriterion("operation_pickup_time <", value, "operationPickupTime");
            return (Criteria) this;
        }

        public Criteria andOperationPickupTimeLessThanOrEqualTo(Date value) {
            addCriterion("operation_pickup_time <=", value, "operationPickupTime");
            return (Criteria) this;
        }

        public Criteria andOperationPickupTimeIn(List<Date> values) {
            addCriterion("operation_pickup_time in", values, "operationPickupTime");
            return (Criteria) this;
        }

        public Criteria andOperationPickupTimeNotIn(List<Date> values) {
            addCriterion("operation_pickup_time not in", values, "operationPickupTime");
            return (Criteria) this;
        }

        public Criteria andOperationPickupTimeBetween(Date value1, Date value2) {
            addCriterion("operation_pickup_time between", value1, value2, "operationPickupTime");
            return (Criteria) this;
        }

        public Criteria andOperationPickupTimeNotBetween(Date value1, Date value2) {
            addCriterion("operation_pickup_time not between", value1, value2, "operationPickupTime");
            return (Criteria) this;
        }

        public Criteria andActualPickupTimeIsNull() {
            addCriterion("actual_pickup_time is null");
            return (Criteria) this;
        }

        public Criteria andActualPickupTimeIsNotNull() {
            addCriterion("actual_pickup_time is not null");
            return (Criteria) this;
        }

        public Criteria andActualPickupTimeEqualTo(Date value) {
            addCriterion("actual_pickup_time =", value, "actualPickupTime");
            return (Criteria) this;
        }

        public Criteria andActualPickupTimeNotEqualTo(Date value) {
            addCriterion("actual_pickup_time <>", value, "actualPickupTime");
            return (Criteria) this;
        }

        public Criteria andActualPickupTimeGreaterThan(Date value) {
            addCriterion("actual_pickup_time >", value, "actualPickupTime");
            return (Criteria) this;
        }

        public Criteria andActualPickupTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("actual_pickup_time >=", value, "actualPickupTime");
            return (Criteria) this;
        }

        public Criteria andActualPickupTimeLessThan(Date value) {
            addCriterion("actual_pickup_time <", value, "actualPickupTime");
            return (Criteria) this;
        }

        public Criteria andActualPickupTimeLessThanOrEqualTo(Date value) {
            addCriterion("actual_pickup_time <=", value, "actualPickupTime");
            return (Criteria) this;
        }

        public Criteria andActualPickupTimeIn(List<Date> values) {
            addCriterion("actual_pickup_time in", values, "actualPickupTime");
            return (Criteria) this;
        }

        public Criteria andActualPickupTimeNotIn(List<Date> values) {
            addCriterion("actual_pickup_time not in", values, "actualPickupTime");
            return (Criteria) this;
        }

        public Criteria andActualPickupTimeBetween(Date value1, Date value2) {
            addCriterion("actual_pickup_time between", value1, value2, "actualPickupTime");
            return (Criteria) this;
        }

        public Criteria andActualPickupTimeNotBetween(Date value1, Date value2) {
            addCriterion("actual_pickup_time not between", value1, value2, "actualPickupTime");
            return (Criteria) this;
        }

        public Criteria andPickupLotExitTimeIsNull() {
            addCriterion("pickup_lot_exit_time is null");
            return (Criteria) this;
        }

        public Criteria andPickupLotExitTimeIsNotNull() {
            addCriterion("pickup_lot_exit_time is not null");
            return (Criteria) this;
        }

        public Criteria andPickupLotExitTimeEqualTo(Date value) {
            addCriterion("pickup_lot_exit_time =", value, "pickupLotExitTime");
            return (Criteria) this;
        }

        public Criteria andPickupLotExitTimeNotEqualTo(Date value) {
            addCriterion("pickup_lot_exit_time <>", value, "pickupLotExitTime");
            return (Criteria) this;
        }

        public Criteria andPickupLotExitTimeGreaterThan(Date value) {
            addCriterion("pickup_lot_exit_time >", value, "pickupLotExitTime");
            return (Criteria) this;
        }

        public Criteria andPickupLotExitTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("pickup_lot_exit_time >=", value, "pickupLotExitTime");
            return (Criteria) this;
        }

        public Criteria andPickupLotExitTimeLessThan(Date value) {
            addCriterion("pickup_lot_exit_time <", value, "pickupLotExitTime");
            return (Criteria) this;
        }

        public Criteria andPickupLotExitTimeLessThanOrEqualTo(Date value) {
            addCriterion("pickup_lot_exit_time <=", value, "pickupLotExitTime");
            return (Criteria) this;
        }

        public Criteria andPickupLotExitTimeIn(List<Date> values) {
            addCriterion("pickup_lot_exit_time in", values, "pickupLotExitTime");
            return (Criteria) this;
        }

        public Criteria andPickupLotExitTimeNotIn(List<Date> values) {
            addCriterion("pickup_lot_exit_time not in", values, "pickupLotExitTime");
            return (Criteria) this;
        }

        public Criteria andPickupLotExitTimeBetween(Date value1, Date value2) {
            addCriterion("pickup_lot_exit_time between", value1, value2, "pickupLotExitTime");
            return (Criteria) this;
        }

        public Criteria andPickupLotExitTimeNotBetween(Date value1, Date value2) {
            addCriterion("pickup_lot_exit_time not between", value1, value2, "pickupLotExitTime");
            return (Criteria) this;
        }

        public Criteria andOperationReturnTimeIsNull() {
            addCriterion("operation_return_time is null");
            return (Criteria) this;
        }

        public Criteria andOperationReturnTimeIsNotNull() {
            addCriterion("operation_return_time is not null");
            return (Criteria) this;
        }

        public Criteria andOperationReturnTimeEqualTo(Date value) {
            addCriterion("operation_return_time =", value, "operationReturnTime");
            return (Criteria) this;
        }

        public Criteria andOperationReturnTimeNotEqualTo(Date value) {
            addCriterion("operation_return_time <>", value, "operationReturnTime");
            return (Criteria) this;
        }

        public Criteria andOperationReturnTimeGreaterThan(Date value) {
            addCriterion("operation_return_time >", value, "operationReturnTime");
            return (Criteria) this;
        }

        public Criteria andOperationReturnTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("operation_return_time >=", value, "operationReturnTime");
            return (Criteria) this;
        }

        public Criteria andOperationReturnTimeLessThan(Date value) {
            addCriterion("operation_return_time <", value, "operationReturnTime");
            return (Criteria) this;
        }

        public Criteria andOperationReturnTimeLessThanOrEqualTo(Date value) {
            addCriterion("operation_return_time <=", value, "operationReturnTime");
            return (Criteria) this;
        }

        public Criteria andOperationReturnTimeIn(List<Date> values) {
            addCriterion("operation_return_time in", values, "operationReturnTime");
            return (Criteria) this;
        }

        public Criteria andOperationReturnTimeNotIn(List<Date> values) {
            addCriterion("operation_return_time not in", values, "operationReturnTime");
            return (Criteria) this;
        }

        public Criteria andOperationReturnTimeBetween(Date value1, Date value2) {
            addCriterion("operation_return_time between", value1, value2, "operationReturnTime");
            return (Criteria) this;
        }

        public Criteria andOperationReturnTimeNotBetween(Date value1, Date value2) {
            addCriterion("operation_return_time not between", value1, value2, "operationReturnTime");
            return (Criteria) this;
        }

        public Criteria andActualReturnTimeIsNull() {
            addCriterion("actual_return_time is null");
            return (Criteria) this;
        }

        public Criteria andActualReturnTimeIsNotNull() {
            addCriterion("actual_return_time is not null");
            return (Criteria) this;
        }

        public Criteria andActualReturnTimeEqualTo(Date value) {
            addCriterion("actual_return_time =", value, "actualReturnTime");
            return (Criteria) this;
        }

        public Criteria andActualReturnTimeNotEqualTo(Date value) {
            addCriterion("actual_return_time <>", value, "actualReturnTime");
            return (Criteria) this;
        }

        public Criteria andActualReturnTimeGreaterThan(Date value) {
            addCriterion("actual_return_time >", value, "actualReturnTime");
            return (Criteria) this;
        }

        public Criteria andActualReturnTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("actual_return_time >=", value, "actualReturnTime");
            return (Criteria) this;
        }

        public Criteria andActualReturnTimeLessThan(Date value) {
            addCriterion("actual_return_time <", value, "actualReturnTime");
            return (Criteria) this;
        }

        public Criteria andActualReturnTimeLessThanOrEqualTo(Date value) {
            addCriterion("actual_return_time <=", value, "actualReturnTime");
            return (Criteria) this;
        }

        public Criteria andActualReturnTimeIn(List<Date> values) {
            addCriterion("actual_return_time in", values, "actualReturnTime");
            return (Criteria) this;
        }

        public Criteria andActualReturnTimeNotIn(List<Date> values) {
            addCriterion("actual_return_time not in", values, "actualReturnTime");
            return (Criteria) this;
        }

        public Criteria andActualReturnTimeBetween(Date value1, Date value2) {
            addCriterion("actual_return_time between", value1, value2, "actualReturnTime");
            return (Criteria) this;
        }

        public Criteria andActualReturnTimeNotBetween(Date value1, Date value2) {
            addCriterion("actual_return_time not between", value1, value2, "actualReturnTime");
            return (Criteria) this;
        }

        public Criteria andReturnLotEntryTimeIsNull() {
            addCriterion("return_lot_entry_time is null");
            return (Criteria) this;
        }

        public Criteria andReturnLotEntryTimeIsNotNull() {
            addCriterion("return_lot_entry_time is not null");
            return (Criteria) this;
        }

        public Criteria andReturnLotEntryTimeEqualTo(Date value) {
            addCriterion("return_lot_entry_time =", value, "returnLotEntryTime");
            return (Criteria) this;
        }

        public Criteria andReturnLotEntryTimeNotEqualTo(Date value) {
            addCriterion("return_lot_entry_time <>", value, "returnLotEntryTime");
            return (Criteria) this;
        }

        public Criteria andReturnLotEntryTimeGreaterThan(Date value) {
            addCriterion("return_lot_entry_time >", value, "returnLotEntryTime");
            return (Criteria) this;
        }

        public Criteria andReturnLotEntryTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("return_lot_entry_time >=", value, "returnLotEntryTime");
            return (Criteria) this;
        }

        public Criteria andReturnLotEntryTimeLessThan(Date value) {
            addCriterion("return_lot_entry_time <", value, "returnLotEntryTime");
            return (Criteria) this;
        }

        public Criteria andReturnLotEntryTimeLessThanOrEqualTo(Date value) {
            addCriterion("return_lot_entry_time <=", value, "returnLotEntryTime");
            return (Criteria) this;
        }

        public Criteria andReturnLotEntryTimeIn(List<Date> values) {
            addCriterion("return_lot_entry_time in", values, "returnLotEntryTime");
            return (Criteria) this;
        }

        public Criteria andReturnLotEntryTimeNotIn(List<Date> values) {
            addCriterion("return_lot_entry_time not in", values, "returnLotEntryTime");
            return (Criteria) this;
        }

        public Criteria andReturnLotEntryTimeBetween(Date value1, Date value2) {
            addCriterion("return_lot_entry_time between", value1, value2, "returnLotEntryTime");
            return (Criteria) this;
        }

        public Criteria andReturnLotEntryTimeNotBetween(Date value1, Date value2) {
            addCriterion("return_lot_entry_time not between", value1, value2, "returnLotEntryTime");
            return (Criteria) this;
        }

        public Criteria andOrderSettleTimeIsNull() {
            addCriterion("order_settle_time is null");
            return (Criteria) this;
        }

        public Criteria andOrderSettleTimeIsNotNull() {
            addCriterion("order_settle_time is not null");
            return (Criteria) this;
        }

        public Criteria andOrderSettleTimeEqualTo(Date value) {
            addCriterion("order_settle_time =", value, "orderSettleTime");
            return (Criteria) this;
        }

        public Criteria andOrderSettleTimeNotEqualTo(Date value) {
            addCriterion("order_settle_time <>", value, "orderSettleTime");
            return (Criteria) this;
        }

        public Criteria andOrderSettleTimeGreaterThan(Date value) {
            addCriterion("order_settle_time >", value, "orderSettleTime");
            return (Criteria) this;
        }

        public Criteria andOrderSettleTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("order_settle_time >=", value, "orderSettleTime");
            return (Criteria) this;
        }

        public Criteria andOrderSettleTimeLessThan(Date value) {
            addCriterion("order_settle_time <", value, "orderSettleTime");
            return (Criteria) this;
        }

        public Criteria andOrderSettleTimeLessThanOrEqualTo(Date value) {
            addCriterion("order_settle_time <=", value, "orderSettleTime");
            return (Criteria) this;
        }

        public Criteria andOrderSettleTimeIn(List<Date> values) {
            addCriterion("order_settle_time in", values, "orderSettleTime");
            return (Criteria) this;
        }

        public Criteria andOrderSettleTimeNotIn(List<Date> values) {
            addCriterion("order_settle_time not in", values, "orderSettleTime");
            return (Criteria) this;
        }

        public Criteria andOrderSettleTimeBetween(Date value1, Date value2) {
            addCriterion("order_settle_time between", value1, value2, "orderSettleTime");
            return (Criteria) this;
        }

        public Criteria andOrderSettleTimeNotBetween(Date value1, Date value2) {
            addCriterion("order_settle_time not between", value1, value2, "orderSettleTime");
            return (Criteria) this;
        }

        public Criteria andRentalHoursIsNull() {
            addCriterion("rental_hours is null");
            return (Criteria) this;
        }

        public Criteria andRentalHoursIsNotNull() {
            addCriterion("rental_hours is not null");
            return (Criteria) this;
        }

        public Criteria andRentalHoursEqualTo(Integer value) {
            addCriterion("rental_hours =", value, "rentalHours");
            return (Criteria) this;
        }

        public Criteria andRentalHoursNotEqualTo(Integer value) {
            addCriterion("rental_hours <>", value, "rentalHours");
            return (Criteria) this;
        }

        public Criteria andRentalHoursGreaterThan(Integer value) {
            addCriterion("rental_hours >", value, "rentalHours");
            return (Criteria) this;
        }

        public Criteria andRentalHoursGreaterThanOrEqualTo(Integer value) {
            addCriterion("rental_hours >=", value, "rentalHours");
            return (Criteria) this;
        }

        public Criteria andRentalHoursLessThan(Integer value) {
            addCriterion("rental_hours <", value, "rentalHours");
            return (Criteria) this;
        }

        public Criteria andRentalHoursLessThanOrEqualTo(Integer value) {
            addCriterion("rental_hours <=", value, "rentalHours");
            return (Criteria) this;
        }

        public Criteria andRentalHoursIn(List<Integer> values) {
            addCriterion("rental_hours in", values, "rentalHours");
            return (Criteria) this;
        }

        public Criteria andRentalHoursNotIn(List<Integer> values) {
            addCriterion("rental_hours not in", values, "rentalHours");
            return (Criteria) this;
        }

        public Criteria andRentalHoursBetween(Integer value1, Integer value2) {
            addCriterion("rental_hours between", value1, value2, "rentalHours");
            return (Criteria) this;
        }

        public Criteria andRentalHoursNotBetween(Integer value1, Integer value2) {
            addCriterion("rental_hours not between", value1, value2, "rentalHours");
            return (Criteria) this;
        }

        public Criteria andOrderCancellationTimeIsNull() {
            addCriterion("order_cancellation_time is null");
            return (Criteria) this;
        }

        public Criteria andOrderCancellationTimeIsNotNull() {
            addCriterion("order_cancellation_time is not null");
            return (Criteria) this;
        }

        public Criteria andOrderCancellationTimeEqualTo(Date value) {
            addCriterion("order_cancellation_time =", value, "orderCancellationTime");
            return (Criteria) this;
        }

        public Criteria andOrderCancellationTimeNotEqualTo(Date value) {
            addCriterion("order_cancellation_time <>", value, "orderCancellationTime");
            return (Criteria) this;
        }

        public Criteria andOrderCancellationTimeGreaterThan(Date value) {
            addCriterion("order_cancellation_time >", value, "orderCancellationTime");
            return (Criteria) this;
        }

        public Criteria andOrderCancellationTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("order_cancellation_time >=", value, "orderCancellationTime");
            return (Criteria) this;
        }

        public Criteria andOrderCancellationTimeLessThan(Date value) {
            addCriterion("order_cancellation_time <", value, "orderCancellationTime");
            return (Criteria) this;
        }

        public Criteria andOrderCancellationTimeLessThanOrEqualTo(Date value) {
            addCriterion("order_cancellation_time <=", value, "orderCancellationTime");
            return (Criteria) this;
        }

        public Criteria andOrderCancellationTimeIn(List<Date> values) {
            addCriterion("order_cancellation_time in", values, "orderCancellationTime");
            return (Criteria) this;
        }

        public Criteria andOrderCancellationTimeNotIn(List<Date> values) {
            addCriterion("order_cancellation_time not in", values, "orderCancellationTime");
            return (Criteria) this;
        }

        public Criteria andOrderCancellationTimeBetween(Date value1, Date value2) {
            addCriterion("order_cancellation_time between", value1, value2, "orderCancellationTime");
            return (Criteria) this;
        }

        public Criteria andOrderCancellationTimeNotBetween(Date value1, Date value2) {
            addCriterion("order_cancellation_time not between", value1, value2, "orderCancellationTime");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitedIsNull() {
            addCriterion("appraise_submited is null");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitedIsNotNull() {
            addCriterion("appraise_submited is not null");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitedEqualTo(Boolean value) {
            addCriterion("appraise_submited =", value, "appraiseSubmited");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitedNotEqualTo(Boolean value) {
            addCriterion("appraise_submited <>", value, "appraiseSubmited");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitedGreaterThan(Boolean value) {
            addCriterion("appraise_submited >", value, "appraiseSubmited");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("appraise_submited >=", value, "appraiseSubmited");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitedLessThan(Boolean value) {
            addCriterion("appraise_submited <", value, "appraiseSubmited");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitedLessThanOrEqualTo(Boolean value) {
            addCriterion("appraise_submited <=", value, "appraiseSubmited");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitedIn(List<Boolean> values) {
            addCriterion("appraise_submited in", values, "appraiseSubmited");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitedNotIn(List<Boolean> values) {
            addCriterion("appraise_submited not in", values, "appraiseSubmited");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitedBetween(Boolean value1, Boolean value2) {
            addCriterion("appraise_submited between", value1, value2, "appraiseSubmited");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("appraise_submited not between", value1, value2, "appraiseSubmited");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitTimeIsNull() {
            addCriterion("appraise_submit_time is null");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitTimeIsNotNull() {
            addCriterion("appraise_submit_time is not null");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitTimeEqualTo(Date value) {
            addCriterion("appraise_submit_time =", value, "appraiseSubmitTime");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitTimeNotEqualTo(Date value) {
            addCriterion("appraise_submit_time <>", value, "appraiseSubmitTime");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitTimeGreaterThan(Date value) {
            addCriterion("appraise_submit_time >", value, "appraiseSubmitTime");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("appraise_submit_time >=", value, "appraiseSubmitTime");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitTimeLessThan(Date value) {
            addCriterion("appraise_submit_time <", value, "appraiseSubmitTime");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitTimeLessThanOrEqualTo(Date value) {
            addCriterion("appraise_submit_time <=", value, "appraiseSubmitTime");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitTimeIn(List<Date> values) {
            addCriterion("appraise_submit_time in", values, "appraiseSubmitTime");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitTimeNotIn(List<Date> values) {
            addCriterion("appraise_submit_time not in", values, "appraiseSubmitTime");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitTimeBetween(Date value1, Date value2) {
            addCriterion("appraise_submit_time between", value1, value2, "appraiseSubmitTime");
            return (Criteria) this;
        }

        public Criteria andAppraiseSubmitTimeNotBetween(Date value1, Date value2) {
            addCriterion("appraise_submit_time not between", value1, value2, "appraiseSubmitTime");
            return (Criteria) this;
        }

        public Criteria andReturnFenceNameIsNull() {
            addCriterion("return_fence_name is null");
            return (Criteria) this;
        }

        public Criteria andReturnFenceNameIsNotNull() {
            addCriterion("return_fence_name is not null");
            return (Criteria) this;
        }

        public Criteria andReturnFenceNameEqualTo(String value) {
            addCriterion("return_fence_name =", value, "returnFenceName");
            return (Criteria) this;
        }

        public Criteria andReturnFenceNameNotEqualTo(String value) {
            addCriterion("return_fence_name <>", value, "returnFenceName");
            return (Criteria) this;
        }

        public Criteria andReturnFenceNameGreaterThan(String value) {
            addCriterion("return_fence_name >", value, "returnFenceName");
            return (Criteria) this;
        }

        public Criteria andReturnFenceNameGreaterThanOrEqualTo(String value) {
            addCriterion("return_fence_name >=", value, "returnFenceName");
            return (Criteria) this;
        }

        public Criteria andReturnFenceNameLessThan(String value) {
            addCriterion("return_fence_name <", value, "returnFenceName");
            return (Criteria) this;
        }

        public Criteria andReturnFenceNameLessThanOrEqualTo(String value) {
            addCriterion("return_fence_name <=", value, "returnFenceName");
            return (Criteria) this;
        }

        public Criteria andReturnFenceNameLike(String value) {
            addCriterion("return_fence_name like", value, "returnFenceName");
            return (Criteria) this;
        }

        public Criteria andReturnFenceNameNotLike(String value) {
            addCriterion("return_fence_name not like", value, "returnFenceName");
            return (Criteria) this;
        }

        public Criteria andReturnFenceNameIn(List<String> values) {
            addCriterion("return_fence_name in", values, "returnFenceName");
            return (Criteria) this;
        }

        public Criteria andReturnFenceNameNotIn(List<String> values) {
            addCriterion("return_fence_name not in", values, "returnFenceName");
            return (Criteria) this;
        }

        public Criteria andReturnFenceNameBetween(String value1, String value2) {
            addCriterion("return_fence_name between", value1, value2, "returnFenceName");
            return (Criteria) this;
        }

        public Criteria andReturnFenceNameNotBetween(String value1, String value2) {
            addCriterion("return_fence_name not between", value1, value2, "returnFenceName");
            return (Criteria) this;
        }

        public Criteria andInitialFenceNameIsNull() {
            addCriterion("initial_fence_name is null");
            return (Criteria) this;
        }

        public Criteria andInitialFenceNameIsNotNull() {
            addCriterion("initial_fence_name is not null");
            return (Criteria) this;
        }

        public Criteria andInitialFenceNameEqualTo(String value) {
            addCriterion("initial_fence_name =", value, "initialFenceName");
            return (Criteria) this;
        }

        public Criteria andInitialFenceNameNotEqualTo(String value) {
            addCriterion("initial_fence_name <>", value, "initialFenceName");
            return (Criteria) this;
        }

        public Criteria andInitialFenceNameGreaterThan(String value) {
            addCriterion("initial_fence_name >", value, "initialFenceName");
            return (Criteria) this;
        }

        public Criteria andInitialFenceNameGreaterThanOrEqualTo(String value) {
            addCriterion("initial_fence_name >=", value, "initialFenceName");
            return (Criteria) this;
        }

        public Criteria andInitialFenceNameLessThan(String value) {
            addCriterion("initial_fence_name <", value, "initialFenceName");
            return (Criteria) this;
        }

        public Criteria andInitialFenceNameLessThanOrEqualTo(String value) {
            addCriterion("initial_fence_name <=", value, "initialFenceName");
            return (Criteria) this;
        }

        public Criteria andInitialFenceNameLike(String value) {
            addCriterion("initial_fence_name like", value, "initialFenceName");
            return (Criteria) this;
        }

        public Criteria andInitialFenceNameNotLike(String value) {
            addCriterion("initial_fence_name not like", value, "initialFenceName");
            return (Criteria) this;
        }

        public Criteria andInitialFenceNameIn(List<String> values) {
            addCriterion("initial_fence_name in", values, "initialFenceName");
            return (Criteria) this;
        }

        public Criteria andInitialFenceNameNotIn(List<String> values) {
            addCriterion("initial_fence_name not in", values, "initialFenceName");
            return (Criteria) this;
        }

        public Criteria andInitialFenceNameBetween(String value1, String value2) {
            addCriterion("initial_fence_name between", value1, value2, "initialFenceName");
            return (Criteria) this;
        }

        public Criteria andInitialFenceNameNotBetween(String value1, String value2) {
            addCriterion("initial_fence_name not between", value1, value2, "initialFenceName");
            return (Criteria) this;
        }
    }

    /**
     * gov_car_order
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * gov_car_order null
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}