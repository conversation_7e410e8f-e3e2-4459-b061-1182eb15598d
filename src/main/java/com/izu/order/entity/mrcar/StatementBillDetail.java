package com.izu.order.entity.mrcar;

import java.math.BigDecimal;
import java.util.Date;

/**
 *  （表名：so_statement_bill_detail）
 * <br>
 * <br>
 * 【重要提示】：<br>
 * &nbsp;&nbsp;此类通过 Mybatis Generator 逆向生成，禁止手动修改！<br>（因项目需求持续性会发生变更，当调整数据表字段时，需要再重新逆向生成此类）
 **/
public class StatementBillDetail {
    /** 自增主键 **/
    private Long statementBillDetailId;

    /** 订单账单号 根据账单类型不同 可区分为收入订单账单号 支出订单账单号 **/
    private String statementBillDetailNum;

    /** 账单号 根据账单类型不同 可区分为收入账单号 支出账单号 **/
    private String statementBillNum;

    /** 客户订单号 **/
    private String customerOrderNum;

    /** 供应商订单号（逗号分隔） **/
    private String supplierOrderNum;

    /** 账单类型 1 收入账单（客户账单） 2 支出账单（供应商账单）与bill保持一致 **/
    private Integer billType;

    /** 1 车辆  2 司机 3 车辆+司机 **/
    private Integer detailType;

    /** 0 为系统生成 -1 为当前调整账单 正数留待后续记录调整次数 **/
    private Integer modifyCount;

    /** 企业客户id **/
    private Integer companyCustomerId;

    /** 企业客户名称 **/
    private String companyCustomerName;

    /** 分公司编码 **/
    private String subsidiaryCompanyCode;

    /** 组织机构名称 **/
    private String subsidiaryCompanyName;

    /** 支出账单特有字段 服务客户id **/
    private Integer serviceCompanyCustomerId;

    /** 支出账单特有字段 服务客户名称 **/
    private String serviceCompanyCustomerName;

    /** 车级ID **/
    private Integer levelId;

    /** 车级名称 **/
    private String levelName;

    /** 车价区间 **/
    private String vehiclePriceRange;

    /** 计费开始时间 **/
    private Date startTime;

    /** 计费结束时间 **/
    private Date endTime;

    /** 实际计费结束时间 **/
    private Date actualEndTime;

    /** 账单开始时间 **/
    private Date billStartTime;

    /** 账单结束时间 **/
    private Date billEndTime;

    /** 车辆ID（明细类型包含车辆有值） **/
    private Long vehicleId;

    /** 车牌号码（明细类型包含车辆有值） **/
    private String vehicleLicense;

    /** 车架号（明细类型包含车辆有值） **/
    private String vehicleVin;

    /** 车辆使用天数 （明细类型包含车辆有值） **/
    private Integer vehicleUseDays;

    /** 车辆日租金（明细类型包含车辆有值） **/
    private BigDecimal vehicleDailyRentAmount;

    /** 车辆来源 1 首汽 2 供应商 **/
    private Integer vehicleSource;

    /** 车辆所属名称 如果是首汽 分公司code 供应商 具体供应商id **/
    private String vehicleBelongKey;

    /** 车辆所属名称 如果是首汽 分公司 供应商 具体供应商信息 **/
    private String vehicleBelongName;

    /** 司机ID **/
    private Integer driverId;

    /** 司机姓名 如果明细类型包含司机 则有值 **/
    private String driverName;

    /** 司机手机号 如果明细类型包含司机则有值 **/
    private String driverMobile;

    /** 司机使用天数 如果明细类型包含司机则有值 **/
    private Integer driverUseDays;

    /** 司机日租金 如果明细类型包含司机则有值 **/
    private BigDecimal driverDailyRentAmount;

    /** 司机来源 1 首汽 2 供应商 **/
    private Integer driverSource;

    /** 司机所属名称 如果是首汽 分公司code 供应商 具体供应商id **/
    private String driverBelongKey;

    /** 司机所属名称 如果是首汽 分公司 供应商 具体供应商信息 **/
    private String driverBelongName;

    /** 供应商车辆日租金 **/
    private BigDecimal supplierVehicleDailyRentAmount;

    /** 供应商司机日租金 **/
    private BigDecimal supplierDriverDailyRentAmount;

    /** 总租金 **/
    private BigDecimal totalAmount;

    /** 创建人ID **/
    private Integer createId;

    /** 创建人姓名 **/
    private String createName;

    /** 创建时间 **/
    private Date createTime;

    /** 修改人ID **/
    private Integer updateId;

    /** 修改人姓名 **/
    private String updateName;

    /** 修改时间 **/
    private Date updateTime;

    /** 推送状态,目前只有收入账单使用这个字段 0待推送 1推送成功 2推送失败 **/
    private Integer settleStatus;

    /** 自增主键 **/
    public Long getStatementBillDetailId() {
        return statementBillDetailId;
    }

    /** 自增主键 **/
    public void setStatementBillDetailId(Long statementBillDetailId) {
        this.statementBillDetailId = statementBillDetailId;
    }

    /** 订单账单号 根据账单类型不同 可区分为收入订单账单号 支出订单账单号 **/
    public String getStatementBillDetailNum() {
        return statementBillDetailNum;
    }

    /** 订单账单号 根据账单类型不同 可区分为收入订单账单号 支出订单账单号 **/
    public void setStatementBillDetailNum(String statementBillDetailNum) {
        this.statementBillDetailNum = statementBillDetailNum == null ? null : statementBillDetailNum.trim();
    }

    /** 账单号 根据账单类型不同 可区分为收入账单号 支出账单号 **/
    public String getStatementBillNum() {
        return statementBillNum;
    }

    /** 账单号 根据账单类型不同 可区分为收入账单号 支出账单号 **/
    public void setStatementBillNum(String statementBillNum) {
        this.statementBillNum = statementBillNum == null ? null : statementBillNum.trim();
    }

    /** 客户订单号 **/
    public String getCustomerOrderNum() {
        return customerOrderNum;
    }

    /** 客户订单号 **/
    public void setCustomerOrderNum(String customerOrderNum) {
        this.customerOrderNum = customerOrderNum == null ? null : customerOrderNum.trim();
    }

    /** 供应商订单号（逗号分隔） **/
    public String getSupplierOrderNum() {
        return supplierOrderNum;
    }

    /** 供应商订单号（逗号分隔） **/
    public void setSupplierOrderNum(String supplierOrderNum) {
        this.supplierOrderNum = supplierOrderNum == null ? null : supplierOrderNum.trim();
    }

    /** 账单类型 1 收入账单（客户账单） 2 支出账单（供应商账单）与bill保持一致 **/
    public Integer getBillType() {
        return billType;
    }

    /** 账单类型 1 收入账单（客户账单） 2 支出账单（供应商账单）与bill保持一致 **/
    public void setBillType(Integer billType) {
        this.billType = billType;
    }

    /** 1 车辆  2 司机 3 车辆+司机 **/
    public Integer getDetailType() {
        return detailType;
    }

    /** 1 车辆  2 司机 3 车辆+司机 **/
    public void setDetailType(Integer detailType) {
        this.detailType = detailType;
    }

    /** 0 为系统生成 -1 为当前调整账单 正数留待后续记录调整次数 **/
    public Integer getModifyCount() {
        return modifyCount;
    }

    /** 0 为系统生成 -1 为当前调整账单 正数留待后续记录调整次数 **/
    public void setModifyCount(Integer modifyCount) {
        this.modifyCount = modifyCount;
    }

    /** 企业客户id **/
    public Integer getCompanyCustomerId() {
        return companyCustomerId;
    }

    /** 企业客户id **/
    public void setCompanyCustomerId(Integer companyCustomerId) {
        this.companyCustomerId = companyCustomerId;
    }

    /** 企业客户名称 **/
    public String getCompanyCustomerName() {
        return companyCustomerName;
    }

    /** 企业客户名称 **/
    public void setCompanyCustomerName(String companyCustomerName) {
        this.companyCustomerName = companyCustomerName == null ? null : companyCustomerName.trim();
    }

    /** 分公司编码 **/
    public String getSubsidiaryCompanyCode() {
        return subsidiaryCompanyCode;
    }

    /** 分公司编码 **/
    public void setSubsidiaryCompanyCode(String subsidiaryCompanyCode) {
        this.subsidiaryCompanyCode = subsidiaryCompanyCode == null ? null : subsidiaryCompanyCode.trim();
    }

    /** 组织机构名称 **/
    public String getSubsidiaryCompanyName() {
        return subsidiaryCompanyName;
    }

    /** 组织机构名称 **/
    public void setSubsidiaryCompanyName(String subsidiaryCompanyName) {
        this.subsidiaryCompanyName = subsidiaryCompanyName == null ? null : subsidiaryCompanyName.trim();
    }

    /** 支出账单特有字段 服务客户id **/
    public Integer getServiceCompanyCustomerId() {
        return serviceCompanyCustomerId;
    }

    /** 支出账单特有字段 服务客户id **/
    public void setServiceCompanyCustomerId(Integer serviceCompanyCustomerId) {
        this.serviceCompanyCustomerId = serviceCompanyCustomerId;
    }

    /** 支出账单特有字段 服务客户名称 **/
    public String getServiceCompanyCustomerName() {
        return serviceCompanyCustomerName;
    }

    /** 支出账单特有字段 服务客户名称 **/
    public void setServiceCompanyCustomerName(String serviceCompanyCustomerName) {
        this.serviceCompanyCustomerName = serviceCompanyCustomerName == null ? null : serviceCompanyCustomerName.trim();
    }

    /** 车级ID **/
    public Integer getLevelId() {
        return levelId;
    }

    /** 车级ID **/
    public void setLevelId(Integer levelId) {
        this.levelId = levelId;
    }

    /** 车级名称 **/
    public String getLevelName() {
        return levelName;
    }

    /** 车级名称 **/
    public void setLevelName(String levelName) {
        this.levelName = levelName == null ? null : levelName.trim();
    }

    /** 车价区间 **/
    public String getVehiclePriceRange() {
        return vehiclePriceRange;
    }

    /** 车价区间 **/
    public void setVehiclePriceRange(String vehiclePriceRange) {
        this.vehiclePriceRange = vehiclePriceRange == null ? null : vehiclePriceRange.trim();
    }

    /** 计费开始时间 **/
    public Date getStartTime() {
        return startTime;
    }

    /** 计费开始时间 **/
    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    /** 计费结束时间 **/
    public Date getEndTime() {
        return endTime;
    }

    /** 计费结束时间 **/
    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    /** 实际计费结束时间 **/
    public Date getActualEndTime() {
        return actualEndTime;
    }

    /** 实际计费结束时间 **/
    public void setActualEndTime(Date actualEndTime) {
        this.actualEndTime = actualEndTime;
    }

    /** 账单开始时间 **/
    public Date getBillStartTime() {
        return billStartTime;
    }

    /** 账单开始时间 **/
    public void setBillStartTime(Date billStartTime) {
        this.billStartTime = billStartTime;
    }

    /** 账单结束时间 **/
    public Date getBillEndTime() {
        return billEndTime;
    }

    /** 账单结束时间 **/
    public void setBillEndTime(Date billEndTime) {
        this.billEndTime = billEndTime;
    }

    /** 车辆ID（明细类型包含车辆有值） **/
    public Long getVehicleId() {
        return vehicleId;
    }

    /** 车辆ID（明细类型包含车辆有值） **/
    public void setVehicleId(Long vehicleId) {
        this.vehicleId = vehicleId;
    }

    /** 车牌号码（明细类型包含车辆有值） **/
    public String getVehicleLicense() {
        return vehicleLicense;
    }

    /** 车牌号码（明细类型包含车辆有值） **/
    public void setVehicleLicense(String vehicleLicense) {
        this.vehicleLicense = vehicleLicense == null ? null : vehicleLicense.trim();
    }

    /** 车架号（明细类型包含车辆有值） **/
    public String getVehicleVin() {
        return vehicleVin;
    }

    /** 车架号（明细类型包含车辆有值） **/
    public void setVehicleVin(String vehicleVin) {
        this.vehicleVin = vehicleVin == null ? null : vehicleVin.trim();
    }

    /** 车辆使用天数 （明细类型包含车辆有值） **/
    public Integer getVehicleUseDays() {
        return vehicleUseDays;
    }

    /** 车辆使用天数 （明细类型包含车辆有值） **/
    public void setVehicleUseDays(Integer vehicleUseDays) {
        this.vehicleUseDays = vehicleUseDays;
    }

    /** 车辆日租金（明细类型包含车辆有值） **/
    public BigDecimal getVehicleDailyRentAmount() {
        return vehicleDailyRentAmount;
    }

    /** 车辆日租金（明细类型包含车辆有值） **/
    public void setVehicleDailyRentAmount(BigDecimal vehicleDailyRentAmount) {
        this.vehicleDailyRentAmount = vehicleDailyRentAmount;
    }

    /** 车辆来源 1 首汽 2 供应商 **/
    public Integer getVehicleSource() {
        return vehicleSource;
    }

    /** 车辆来源 1 首汽 2 供应商 **/
    public void setVehicleSource(Integer vehicleSource) {
        this.vehicleSource = vehicleSource;
    }

    /** 车辆所属名称 如果是首汽 分公司code 供应商 具体供应商id **/
    public String getVehicleBelongKey() {
        return vehicleBelongKey;
    }

    /** 车辆所属名称 如果是首汽 分公司code 供应商 具体供应商id **/
    public void setVehicleBelongKey(String vehicleBelongKey) {
        this.vehicleBelongKey = vehicleBelongKey == null ? null : vehicleBelongKey.trim();
    }

    /** 车辆所属名称 如果是首汽 分公司 供应商 具体供应商信息 **/
    public String getVehicleBelongName() {
        return vehicleBelongName;
    }

    /** 车辆所属名称 如果是首汽 分公司 供应商 具体供应商信息 **/
    public void setVehicleBelongName(String vehicleBelongName) {
        this.vehicleBelongName = vehicleBelongName == null ? null : vehicleBelongName.trim();
    }

    /** 司机ID **/
    public Integer getDriverId() {
        return driverId;
    }

    /** 司机ID **/
    public void setDriverId(Integer driverId) {
        this.driverId = driverId;
    }

    /** 司机姓名 如果明细类型包含司机 则有值 **/
    public String getDriverName() {
        return driverName;
    }

    /** 司机姓名 如果明细类型包含司机 则有值 **/
    public void setDriverName(String driverName) {
        this.driverName = driverName == null ? null : driverName.trim();
    }

    /** 司机手机号 如果明细类型包含司机则有值 **/
    public String getDriverMobile() {
        return driverMobile;
    }

    /** 司机手机号 如果明细类型包含司机则有值 **/
    public void setDriverMobile(String driverMobile) {
        this.driverMobile = driverMobile == null ? null : driverMobile.trim();
    }

    /** 司机使用天数 如果明细类型包含司机则有值 **/
    public Integer getDriverUseDays() {
        return driverUseDays;
    }

    /** 司机使用天数 如果明细类型包含司机则有值 **/
    public void setDriverUseDays(Integer driverUseDays) {
        this.driverUseDays = driverUseDays;
    }

    /** 司机日租金 如果明细类型包含司机则有值 **/
    public BigDecimal getDriverDailyRentAmount() {
        return driverDailyRentAmount;
    }

    /** 司机日租金 如果明细类型包含司机则有值 **/
    public void setDriverDailyRentAmount(BigDecimal driverDailyRentAmount) {
        this.driverDailyRentAmount = driverDailyRentAmount;
    }

    /** 司机来源 1 首汽 2 供应商 **/
    public Integer getDriverSource() {
        return driverSource;
    }

    /** 司机来源 1 首汽 2 供应商 **/
    public void setDriverSource(Integer driverSource) {
        this.driverSource = driverSource;
    }

    /** 司机所属名称 如果是首汽 分公司code 供应商 具体供应商id **/
    public String getDriverBelongKey() {
        return driverBelongKey;
    }

    /** 司机所属名称 如果是首汽 分公司code 供应商 具体供应商id **/
    public void setDriverBelongKey(String driverBelongKey) {
        this.driverBelongKey = driverBelongKey == null ? null : driverBelongKey.trim();
    }

    /** 司机所属名称 如果是首汽 分公司 供应商 具体供应商信息 **/
    public String getDriverBelongName() {
        return driverBelongName;
    }

    /** 司机所属名称 如果是首汽 分公司 供应商 具体供应商信息 **/
    public void setDriverBelongName(String driverBelongName) {
        this.driverBelongName = driverBelongName == null ? null : driverBelongName.trim();
    }

    /** 供应商车辆日租金 **/
    public BigDecimal getSupplierVehicleDailyRentAmount() {
        return supplierVehicleDailyRentAmount;
    }

    /** 供应商车辆日租金 **/
    public void setSupplierVehicleDailyRentAmount(BigDecimal supplierVehicleDailyRentAmount) {
        this.supplierVehicleDailyRentAmount = supplierVehicleDailyRentAmount;
    }

    /** 供应商司机日租金 **/
    public BigDecimal getSupplierDriverDailyRentAmount() {
        return supplierDriverDailyRentAmount;
    }

    /** 供应商司机日租金 **/
    public void setSupplierDriverDailyRentAmount(BigDecimal supplierDriverDailyRentAmount) {
        this.supplierDriverDailyRentAmount = supplierDriverDailyRentAmount;
    }

    /** 总租金 **/
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    /** 总租金 **/
    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    /** 创建人ID **/
    public Integer getCreateId() {
        return createId;
    }

    /** 创建人ID **/
    public void setCreateId(Integer createId) {
        this.createId = createId;
    }

    /** 创建人姓名 **/
    public String getCreateName() {
        return createName;
    }

    /** 创建人姓名 **/
    public void setCreateName(String createName) {
        this.createName = createName == null ? null : createName.trim();
    }

    /** 创建时间 **/
    public Date getCreateTime() {
        return createTime;
    }

    /** 创建时间 **/
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /** 修改人ID **/
    public Integer getUpdateId() {
        return updateId;
    }

    /** 修改人ID **/
    public void setUpdateId(Integer updateId) {
        this.updateId = updateId;
    }

    /** 修改人姓名 **/
    public String getUpdateName() {
        return updateName;
    }

    /** 修改人姓名 **/
    public void setUpdateName(String updateName) {
        this.updateName = updateName == null ? null : updateName.trim();
    }

    /** 修改时间 **/
    public Date getUpdateTime() {
        return updateTime;
    }

    /** 修改时间 **/
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /** 推送状态,目前只有收入账单使用这个字段 0待推送 1推送成功 2推送失败 **/
    public Integer getSettleStatus() {
        return settleStatus;
    }

    /** 推送状态,目前只有收入账单使用这个字段 0待推送 1推送成功 2推送失败 **/
    public void setSettleStatus(Integer settleStatus) {
        this.settleStatus = settleStatus;
    }
}