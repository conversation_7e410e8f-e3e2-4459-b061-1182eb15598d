package com.izu.order.entity.mrcar;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class BusinessOrderAdditionRecordExample {
    /**
     * business_order_addition_record
     */
    protected String orderByClause;

    /**
     * business_order_addition_record
     */
    protected boolean distinct;

    /**
     * business_order_addition_record
     */
    protected List<Criteria> oredCriteria;

    /**
     *
     * @mbg.generated
     */
    public BusinessOrderAdditionRecordExample() {
        oredCriteria = new ArrayList<>();
    }

    /**
     *
     * @mbg.generated
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     *
     * @mbg.generated
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     *
     * @mbg.generated
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     *
     * @mbg.generated
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     *
     * @mbg.generated
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     *
     * @mbg.generated
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     *
     * @mbg.generated
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     *
     * @mbg.generated
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     *
     * @mbg.generated
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     *
     * @mbg.generated
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * business_order_addition_record null
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andRecordIdIsNull() {
            addCriterion("record_id is null");
            return (Criteria) this;
        }

        public Criteria andRecordIdIsNotNull() {
            addCriterion("record_id is not null");
            return (Criteria) this;
        }

        public Criteria andRecordIdEqualTo(Integer value) {
            addCriterion("record_id =", value, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdNotEqualTo(Integer value) {
            addCriterion("record_id <>", value, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdGreaterThan(Integer value) {
            addCriterion("record_id >", value, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("record_id >=", value, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdLessThan(Integer value) {
            addCriterion("record_id <", value, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdLessThanOrEqualTo(Integer value) {
            addCriterion("record_id <=", value, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdIn(List<Integer> values) {
            addCriterion("record_id in", values, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdNotIn(List<Integer> values) {
            addCriterion("record_id not in", values, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdBetween(Integer value1, Integer value2) {
            addCriterion("record_id between", value1, value2, "recordId");
            return (Criteria) this;
        }

        public Criteria andRecordIdNotBetween(Integer value1, Integer value2) {
            addCriterion("record_id not between", value1, value2, "recordId");
            return (Criteria) this;
        }

        public Criteria andOrderCodeIsNull() {
            addCriterion("order_code is null");
            return (Criteria) this;
        }

        public Criteria andOrderCodeIsNotNull() {
            addCriterion("order_code is not null");
            return (Criteria) this;
        }

        public Criteria andOrderCodeEqualTo(String value) {
            addCriterion("order_code =", value, "orderCode");
            return (Criteria) this;
        }

        public Criteria andOrderCodeNotEqualTo(String value) {
            addCriterion("order_code <>", value, "orderCode");
            return (Criteria) this;
        }

        public Criteria andOrderCodeGreaterThan(String value) {
            addCriterion("order_code >", value, "orderCode");
            return (Criteria) this;
        }

        public Criteria andOrderCodeGreaterThanOrEqualTo(String value) {
            addCriterion("order_code >=", value, "orderCode");
            return (Criteria) this;
        }

        public Criteria andOrderCodeLessThan(String value) {
            addCriterion("order_code <", value, "orderCode");
            return (Criteria) this;
        }

        public Criteria andOrderCodeLessThanOrEqualTo(String value) {
            addCriterion("order_code <=", value, "orderCode");
            return (Criteria) this;
        }

        public Criteria andOrderCodeLike(String value) {
            addCriterion("order_code like", value, "orderCode");
            return (Criteria) this;
        }

        public Criteria andOrderCodeNotLike(String value) {
            addCriterion("order_code not like", value, "orderCode");
            return (Criteria) this;
        }

        public Criteria andOrderCodeIn(List<String> values) {
            addCriterion("order_code in", values, "orderCode");
            return (Criteria) this;
        }

        public Criteria andOrderCodeNotIn(List<String> values) {
            addCriterion("order_code not in", values, "orderCode");
            return (Criteria) this;
        }

        public Criteria andOrderCodeBetween(String value1, String value2) {
            addCriterion("order_code between", value1, value2, "orderCode");
            return (Criteria) this;
        }

        public Criteria andOrderCodeNotBetween(String value1, String value2) {
            addCriterion("order_code not between", value1, value2, "orderCode");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIsNull() {
            addCriterion("order_status is null");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIsNotNull() {
            addCriterion("order_status is not null");
            return (Criteria) this;
        }

        public Criteria andOrderStatusEqualTo(Integer value) {
            addCriterion("order_status =", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotEqualTo(Integer value) {
            addCriterion("order_status <>", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusGreaterThan(Integer value) {
            addCriterion("order_status >", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_status >=", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusLessThan(Integer value) {
            addCriterion("order_status <", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusLessThanOrEqualTo(Integer value) {
            addCriterion("order_status <=", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIn(List<Integer> values) {
            addCriterion("order_status in", values, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotIn(List<Integer> values) {
            addCriterion("order_status not in", values, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusBetween(Integer value1, Integer value2) {
            addCriterion("order_status between", value1, value2, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("order_status not between", value1, value2, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeIsNull() {
            addCriterion("customer_code is null");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeIsNotNull() {
            addCriterion("customer_code is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeEqualTo(String value) {
            addCriterion("customer_code =", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotEqualTo(String value) {
            addCriterion("customer_code <>", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeGreaterThan(String value) {
            addCriterion("customer_code >", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeGreaterThanOrEqualTo(String value) {
            addCriterion("customer_code >=", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeLessThan(String value) {
            addCriterion("customer_code <", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeLessThanOrEqualTo(String value) {
            addCriterion("customer_code <=", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeLike(String value) {
            addCriterion("customer_code like", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotLike(String value) {
            addCriterion("customer_code not like", value, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeIn(List<String> values) {
            addCriterion("customer_code in", values, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotIn(List<String> values) {
            addCriterion("customer_code not in", values, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeBetween(String value1, String value2) {
            addCriterion("customer_code between", value1, value2, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerCodeNotBetween(String value1, String value2) {
            addCriterion("customer_code not between", value1, value2, "customerCode");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNull() {
            addCriterion("customer_name is null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNotNull() {
            addCriterion("customer_name is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameEqualTo(String value) {
            addCriterion("customer_name =", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotEqualTo(String value) {
            addCriterion("customer_name <>", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThan(String value) {
            addCriterion("customer_name >", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThanOrEqualTo(String value) {
            addCriterion("customer_name >=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThan(String value) {
            addCriterion("customer_name <", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThanOrEqualTo(String value) {
            addCriterion("customer_name <=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLike(String value) {
            addCriterion("customer_name like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotLike(String value) {
            addCriterion("customer_name not like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIn(List<String> values) {
            addCriterion("customer_name in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotIn(List<String> values) {
            addCriterion("customer_name not in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameBetween(String value1, String value2) {
            addCriterion("customer_name between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotBetween(String value1, String value2) {
            addCriterion("customer_name not between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andOurSocialCreditCodeIsNull() {
            addCriterion("our_social_credit_code is null");
            return (Criteria) this;
        }

        public Criteria andOurSocialCreditCodeIsNotNull() {
            addCriterion("our_social_credit_code is not null");
            return (Criteria) this;
        }

        public Criteria andOurSocialCreditCodeEqualTo(String value) {
            addCriterion("our_social_credit_code =", value, "ourSocialCreditCode");
            return (Criteria) this;
        }

        public Criteria andOurSocialCreditCodeNotEqualTo(String value) {
            addCriterion("our_social_credit_code <>", value, "ourSocialCreditCode");
            return (Criteria) this;
        }

        public Criteria andOurSocialCreditCodeGreaterThan(String value) {
            addCriterion("our_social_credit_code >", value, "ourSocialCreditCode");
            return (Criteria) this;
        }

        public Criteria andOurSocialCreditCodeGreaterThanOrEqualTo(String value) {
            addCriterion("our_social_credit_code >=", value, "ourSocialCreditCode");
            return (Criteria) this;
        }

        public Criteria andOurSocialCreditCodeLessThan(String value) {
            addCriterion("our_social_credit_code <", value, "ourSocialCreditCode");
            return (Criteria) this;
        }

        public Criteria andOurSocialCreditCodeLessThanOrEqualTo(String value) {
            addCriterion("our_social_credit_code <=", value, "ourSocialCreditCode");
            return (Criteria) this;
        }

        public Criteria andOurSocialCreditCodeLike(String value) {
            addCriterion("our_social_credit_code like", value, "ourSocialCreditCode");
            return (Criteria) this;
        }

        public Criteria andOurSocialCreditCodeNotLike(String value) {
            addCriterion("our_social_credit_code not like", value, "ourSocialCreditCode");
            return (Criteria) this;
        }

        public Criteria andOurSocialCreditCodeIn(List<String> values) {
            addCriterion("our_social_credit_code in", values, "ourSocialCreditCode");
            return (Criteria) this;
        }

        public Criteria andOurSocialCreditCodeNotIn(List<String> values) {
            addCriterion("our_social_credit_code not in", values, "ourSocialCreditCode");
            return (Criteria) this;
        }

        public Criteria andOurSocialCreditCodeBetween(String value1, String value2) {
            addCriterion("our_social_credit_code between", value1, value2, "ourSocialCreditCode");
            return (Criteria) this;
        }

        public Criteria andOurSocialCreditCodeNotBetween(String value1, String value2) {
            addCriterion("our_social_credit_code not between", value1, value2, "ourSocialCreditCode");
            return (Criteria) this;
        }

        public Criteria andOurContractingEntityIsNull() {
            addCriterion("our_contracting_entity is null");
            return (Criteria) this;
        }

        public Criteria andOurContractingEntityIsNotNull() {
            addCriterion("our_contracting_entity is not null");
            return (Criteria) this;
        }

        public Criteria andOurContractingEntityEqualTo(String value) {
            addCriterion("our_contracting_entity =", value, "ourContractingEntity");
            return (Criteria) this;
        }

        public Criteria andOurContractingEntityNotEqualTo(String value) {
            addCriterion("our_contracting_entity <>", value, "ourContractingEntity");
            return (Criteria) this;
        }

        public Criteria andOurContractingEntityGreaterThan(String value) {
            addCriterion("our_contracting_entity >", value, "ourContractingEntity");
            return (Criteria) this;
        }

        public Criteria andOurContractingEntityGreaterThanOrEqualTo(String value) {
            addCriterion("our_contracting_entity >=", value, "ourContractingEntity");
            return (Criteria) this;
        }

        public Criteria andOurContractingEntityLessThan(String value) {
            addCriterion("our_contracting_entity <", value, "ourContractingEntity");
            return (Criteria) this;
        }

        public Criteria andOurContractingEntityLessThanOrEqualTo(String value) {
            addCriterion("our_contracting_entity <=", value, "ourContractingEntity");
            return (Criteria) this;
        }

        public Criteria andOurContractingEntityLike(String value) {
            addCriterion("our_contracting_entity like", value, "ourContractingEntity");
            return (Criteria) this;
        }

        public Criteria andOurContractingEntityNotLike(String value) {
            addCriterion("our_contracting_entity not like", value, "ourContractingEntity");
            return (Criteria) this;
        }

        public Criteria andOurContractingEntityIn(List<String> values) {
            addCriterion("our_contracting_entity in", values, "ourContractingEntity");
            return (Criteria) this;
        }

        public Criteria andOurContractingEntityNotIn(List<String> values) {
            addCriterion("our_contracting_entity not in", values, "ourContractingEntity");
            return (Criteria) this;
        }

        public Criteria andOurContractingEntityBetween(String value1, String value2) {
            addCriterion("our_contracting_entity between", value1, value2, "ourContractingEntity");
            return (Criteria) this;
        }

        public Criteria andOurContractingEntityNotBetween(String value1, String value2) {
            addCriterion("our_contracting_entity not between", value1, value2, "ourContractingEntity");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseIsNull() {
            addCriterion("vehicle_license is null");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseIsNotNull() {
            addCriterion("vehicle_license is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseEqualTo(String value) {
            addCriterion("vehicle_license =", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseNotEqualTo(String value) {
            addCriterion("vehicle_license <>", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseGreaterThan(String value) {
            addCriterion("vehicle_license >", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_license >=", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseLessThan(String value) {
            addCriterion("vehicle_license <", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseLessThanOrEqualTo(String value) {
            addCriterion("vehicle_license <=", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseLike(String value) {
            addCriterion("vehicle_license like", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseNotLike(String value) {
            addCriterion("vehicle_license not like", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseIn(List<String> values) {
            addCriterion("vehicle_license in", values, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseNotIn(List<String> values) {
            addCriterion("vehicle_license not in", values, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseBetween(String value1, String value2) {
            addCriterion("vehicle_license between", value1, value2, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseNotBetween(String value1, String value2) {
            addCriterion("vehicle_license not between", value1, value2, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameIsNull() {
            addCriterion("vehicle_model_name is null");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameIsNotNull() {
            addCriterion("vehicle_model_name is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameEqualTo(String value) {
            addCriterion("vehicle_model_name =", value, "vehicleModelName");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameNotEqualTo(String value) {
            addCriterion("vehicle_model_name <>", value, "vehicleModelName");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameGreaterThan(String value) {
            addCriterion("vehicle_model_name >", value, "vehicleModelName");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_model_name >=", value, "vehicleModelName");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameLessThan(String value) {
            addCriterion("vehicle_model_name <", value, "vehicleModelName");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameLessThanOrEqualTo(String value) {
            addCriterion("vehicle_model_name <=", value, "vehicleModelName");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameLike(String value) {
            addCriterion("vehicle_model_name like", value, "vehicleModelName");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameNotLike(String value) {
            addCriterion("vehicle_model_name not like", value, "vehicleModelName");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameIn(List<String> values) {
            addCriterion("vehicle_model_name in", values, "vehicleModelName");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameNotIn(List<String> values) {
            addCriterion("vehicle_model_name not in", values, "vehicleModelName");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameBetween(String value1, String value2) {
            addCriterion("vehicle_model_name between", value1, value2, "vehicleModelName");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNameNotBetween(String value1, String value2) {
            addCriterion("vehicle_model_name not between", value1, value2, "vehicleModelName");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeIsNull() {
            addCriterion("vehicle_model_code is null");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeIsNotNull() {
            addCriterion("vehicle_model_code is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeEqualTo(String value) {
            addCriterion("vehicle_model_code =", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeNotEqualTo(String value) {
            addCriterion("vehicle_model_code <>", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeGreaterThan(String value) {
            addCriterion("vehicle_model_code >", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_model_code >=", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeLessThan(String value) {
            addCriterion("vehicle_model_code <", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeLessThanOrEqualTo(String value) {
            addCriterion("vehicle_model_code <=", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeLike(String value) {
            addCriterion("vehicle_model_code like", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeNotLike(String value) {
            addCriterion("vehicle_model_code not like", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeIn(List<String> values) {
            addCriterion("vehicle_model_code in", values, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeNotIn(List<String> values) {
            addCriterion("vehicle_model_code not in", values, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeBetween(String value1, String value2) {
            addCriterion("vehicle_model_code between", value1, value2, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeNotBetween(String value1, String value2) {
            addCriterion("vehicle_model_code not between", value1, value2, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussCodeIsNull() {
            addCriterion("vehicle_operate_buss_code is null");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussCodeIsNotNull() {
            addCriterion("vehicle_operate_buss_code is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussCodeEqualTo(String value) {
            addCriterion("vehicle_operate_buss_code =", value, "vehicleOperateBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussCodeNotEqualTo(String value) {
            addCriterion("vehicle_operate_buss_code <>", value, "vehicleOperateBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussCodeGreaterThan(String value) {
            addCriterion("vehicle_operate_buss_code >", value, "vehicleOperateBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussCodeGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_operate_buss_code >=", value, "vehicleOperateBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussCodeLessThan(String value) {
            addCriterion("vehicle_operate_buss_code <", value, "vehicleOperateBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussCodeLessThanOrEqualTo(String value) {
            addCriterion("vehicle_operate_buss_code <=", value, "vehicleOperateBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussCodeLike(String value) {
            addCriterion("vehicle_operate_buss_code like", value, "vehicleOperateBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussCodeNotLike(String value) {
            addCriterion("vehicle_operate_buss_code not like", value, "vehicleOperateBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussCodeIn(List<String> values) {
            addCriterion("vehicle_operate_buss_code in", values, "vehicleOperateBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussCodeNotIn(List<String> values) {
            addCriterion("vehicle_operate_buss_code not in", values, "vehicleOperateBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussCodeBetween(String value1, String value2) {
            addCriterion("vehicle_operate_buss_code between", value1, value2, "vehicleOperateBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussCodeNotBetween(String value1, String value2) {
            addCriterion("vehicle_operate_buss_code not between", value1, value2, "vehicleOperateBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateCityCodeIsNull() {
            addCriterion("vehicle_operate_city_code is null");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateCityCodeIsNotNull() {
            addCriterion("vehicle_operate_city_code is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateCityCodeEqualTo(String value) {
            addCriterion("vehicle_operate_city_code =", value, "vehicleOperateCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateCityCodeNotEqualTo(String value) {
            addCriterion("vehicle_operate_city_code <>", value, "vehicleOperateCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateCityCodeGreaterThan(String value) {
            addCriterion("vehicle_operate_city_code >", value, "vehicleOperateCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateCityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_operate_city_code >=", value, "vehicleOperateCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateCityCodeLessThan(String value) {
            addCriterion("vehicle_operate_city_code <", value, "vehicleOperateCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateCityCodeLessThanOrEqualTo(String value) {
            addCriterion("vehicle_operate_city_code <=", value, "vehicleOperateCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateCityCodeLike(String value) {
            addCriterion("vehicle_operate_city_code like", value, "vehicleOperateCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateCityCodeNotLike(String value) {
            addCriterion("vehicle_operate_city_code not like", value, "vehicleOperateCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateCityCodeIn(List<String> values) {
            addCriterion("vehicle_operate_city_code in", values, "vehicleOperateCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateCityCodeNotIn(List<String> values) {
            addCriterion("vehicle_operate_city_code not in", values, "vehicleOperateCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateCityCodeBetween(String value1, String value2) {
            addCriterion("vehicle_operate_city_code between", value1, value2, "vehicleOperateCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateCityCodeNotBetween(String value1, String value2) {
            addCriterion("vehicle_operate_city_code not between", value1, value2, "vehicleOperateCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateCityNameIsNull() {
            addCriterion("vehicle_operate_city_name is null");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateCityNameIsNotNull() {
            addCriterion("vehicle_operate_city_name is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateCityNameEqualTo(String value) {
            addCriterion("vehicle_operate_city_name =", value, "vehicleOperateCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateCityNameNotEqualTo(String value) {
            addCriterion("vehicle_operate_city_name <>", value, "vehicleOperateCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateCityNameGreaterThan(String value) {
            addCriterion("vehicle_operate_city_name >", value, "vehicleOperateCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateCityNameGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_operate_city_name >=", value, "vehicleOperateCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateCityNameLessThan(String value) {
            addCriterion("vehicle_operate_city_name <", value, "vehicleOperateCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateCityNameLessThanOrEqualTo(String value) {
            addCriterion("vehicle_operate_city_name <=", value, "vehicleOperateCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateCityNameLike(String value) {
            addCriterion("vehicle_operate_city_name like", value, "vehicleOperateCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateCityNameNotLike(String value) {
            addCriterion("vehicle_operate_city_name not like", value, "vehicleOperateCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateCityNameIn(List<String> values) {
            addCriterion("vehicle_operate_city_name in", values, "vehicleOperateCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateCityNameNotIn(List<String> values) {
            addCriterion("vehicle_operate_city_name not in", values, "vehicleOperateCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateCityNameBetween(String value1, String value2) {
            addCriterion("vehicle_operate_city_name between", value1, value2, "vehicleOperateCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateCityNameNotBetween(String value1, String value2) {
            addCriterion("vehicle_operate_city_name not between", value1, value2, "vehicleOperateCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussNameIsNull() {
            addCriterion("vehicle_operate_buss_name is null");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussNameIsNotNull() {
            addCriterion("vehicle_operate_buss_name is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussNameEqualTo(String value) {
            addCriterion("vehicle_operate_buss_name =", value, "vehicleOperateBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussNameNotEqualTo(String value) {
            addCriterion("vehicle_operate_buss_name <>", value, "vehicleOperateBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussNameGreaterThan(String value) {
            addCriterion("vehicle_operate_buss_name >", value, "vehicleOperateBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussNameGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_operate_buss_name >=", value, "vehicleOperateBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussNameLessThan(String value) {
            addCriterion("vehicle_operate_buss_name <", value, "vehicleOperateBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussNameLessThanOrEqualTo(String value) {
            addCriterion("vehicle_operate_buss_name <=", value, "vehicleOperateBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussNameLike(String value) {
            addCriterion("vehicle_operate_buss_name like", value, "vehicleOperateBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussNameNotLike(String value) {
            addCriterion("vehicle_operate_buss_name not like", value, "vehicleOperateBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussNameIn(List<String> values) {
            addCriterion("vehicle_operate_buss_name in", values, "vehicleOperateBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussNameNotIn(List<String> values) {
            addCriterion("vehicle_operate_buss_name not in", values, "vehicleOperateBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussNameBetween(String value1, String value2) {
            addCriterion("vehicle_operate_buss_name between", value1, value2, "vehicleOperateBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussNameNotBetween(String value1, String value2) {
            addCriterion("vehicle_operate_buss_name not between", value1, value2, "vehicleOperateBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussCodeIsNull() {
            addCriterion("vehicle_belong_buss_code is null");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussCodeIsNotNull() {
            addCriterion("vehicle_belong_buss_code is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussCodeEqualTo(String value) {
            addCriterion("vehicle_belong_buss_code =", value, "vehicleBelongBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussCodeNotEqualTo(String value) {
            addCriterion("vehicle_belong_buss_code <>", value, "vehicleBelongBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussCodeGreaterThan(String value) {
            addCriterion("vehicle_belong_buss_code >", value, "vehicleBelongBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussCodeGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_belong_buss_code >=", value, "vehicleBelongBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussCodeLessThan(String value) {
            addCriterion("vehicle_belong_buss_code <", value, "vehicleBelongBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussCodeLessThanOrEqualTo(String value) {
            addCriterion("vehicle_belong_buss_code <=", value, "vehicleBelongBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussCodeLike(String value) {
            addCriterion("vehicle_belong_buss_code like", value, "vehicleBelongBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussCodeNotLike(String value) {
            addCriterion("vehicle_belong_buss_code not like", value, "vehicleBelongBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussCodeIn(List<String> values) {
            addCriterion("vehicle_belong_buss_code in", values, "vehicleBelongBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussCodeNotIn(List<String> values) {
            addCriterion("vehicle_belong_buss_code not in", values, "vehicleBelongBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussCodeBetween(String value1, String value2) {
            addCriterion("vehicle_belong_buss_code between", value1, value2, "vehicleBelongBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussCodeNotBetween(String value1, String value2) {
            addCriterion("vehicle_belong_buss_code not between", value1, value2, "vehicleBelongBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussNameIsNull() {
            addCriterion("vehicle_belong_buss_name is null");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussNameIsNotNull() {
            addCriterion("vehicle_belong_buss_name is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussNameEqualTo(String value) {
            addCriterion("vehicle_belong_buss_name =", value, "vehicleBelongBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussNameNotEqualTo(String value) {
            addCriterion("vehicle_belong_buss_name <>", value, "vehicleBelongBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussNameGreaterThan(String value) {
            addCriterion("vehicle_belong_buss_name >", value, "vehicleBelongBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussNameGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_belong_buss_name >=", value, "vehicleBelongBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussNameLessThan(String value) {
            addCriterion("vehicle_belong_buss_name <", value, "vehicleBelongBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussNameLessThanOrEqualTo(String value) {
            addCriterion("vehicle_belong_buss_name <=", value, "vehicleBelongBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussNameLike(String value) {
            addCriterion("vehicle_belong_buss_name like", value, "vehicleBelongBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussNameNotLike(String value) {
            addCriterion("vehicle_belong_buss_name not like", value, "vehicleBelongBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussNameIn(List<String> values) {
            addCriterion("vehicle_belong_buss_name in", values, "vehicleBelongBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussNameNotIn(List<String> values) {
            addCriterion("vehicle_belong_buss_name not in", values, "vehicleBelongBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussNameBetween(String value1, String value2) {
            addCriterion("vehicle_belong_buss_name between", value1, value2, "vehicleBelongBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussNameNotBetween(String value1, String value2) {
            addCriterion("vehicle_belong_buss_name not between", value1, value2, "vehicleBelongBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleSelfOwnedIsNull() {
            addCriterion("vehicle_self_owned is null");
            return (Criteria) this;
        }

        public Criteria andVehicleSelfOwnedIsNotNull() {
            addCriterion("vehicle_self_owned is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleSelfOwnedEqualTo(Byte value) {
            addCriterion("vehicle_self_owned =", value, "vehicleSelfOwned");
            return (Criteria) this;
        }

        public Criteria andVehicleSelfOwnedNotEqualTo(Byte value) {
            addCriterion("vehicle_self_owned <>", value, "vehicleSelfOwned");
            return (Criteria) this;
        }

        public Criteria andVehicleSelfOwnedGreaterThan(Byte value) {
            addCriterion("vehicle_self_owned >", value, "vehicleSelfOwned");
            return (Criteria) this;
        }

        public Criteria andVehicleSelfOwnedGreaterThanOrEqualTo(Byte value) {
            addCriterion("vehicle_self_owned >=", value, "vehicleSelfOwned");
            return (Criteria) this;
        }

        public Criteria andVehicleSelfOwnedLessThan(Byte value) {
            addCriterion("vehicle_self_owned <", value, "vehicleSelfOwned");
            return (Criteria) this;
        }

        public Criteria andVehicleSelfOwnedLessThanOrEqualTo(Byte value) {
            addCriterion("vehicle_self_owned <=", value, "vehicleSelfOwned");
            return (Criteria) this;
        }

        public Criteria andVehicleSelfOwnedIn(List<Byte> values) {
            addCriterion("vehicle_self_owned in", values, "vehicleSelfOwned");
            return (Criteria) this;
        }

        public Criteria andVehicleSelfOwnedNotIn(List<Byte> values) {
            addCriterion("vehicle_self_owned not in", values, "vehicleSelfOwned");
            return (Criteria) this;
        }

        public Criteria andVehicleSelfOwnedBetween(Byte value1, Byte value2) {
            addCriterion("vehicle_self_owned between", value1, value2, "vehicleSelfOwned");
            return (Criteria) this;
        }

        public Criteria andVehicleSelfOwnedNotBetween(Byte value1, Byte value2) {
            addCriterion("vehicle_self_owned not between", value1, value2, "vehicleSelfOwned");
            return (Criteria) this;
        }

        public Criteria andDriverNameIsNull() {
            addCriterion("driver_name is null");
            return (Criteria) this;
        }

        public Criteria andDriverNameIsNotNull() {
            addCriterion("driver_name is not null");
            return (Criteria) this;
        }

        public Criteria andDriverNameEqualTo(String value) {
            addCriterion("driver_name =", value, "driverName");
            return (Criteria) this;
        }

        public Criteria andDriverNameNotEqualTo(String value) {
            addCriterion("driver_name <>", value, "driverName");
            return (Criteria) this;
        }

        public Criteria andDriverNameGreaterThan(String value) {
            addCriterion("driver_name >", value, "driverName");
            return (Criteria) this;
        }

        public Criteria andDriverNameGreaterThanOrEqualTo(String value) {
            addCriterion("driver_name >=", value, "driverName");
            return (Criteria) this;
        }

        public Criteria andDriverNameLessThan(String value) {
            addCriterion("driver_name <", value, "driverName");
            return (Criteria) this;
        }

        public Criteria andDriverNameLessThanOrEqualTo(String value) {
            addCriterion("driver_name <=", value, "driverName");
            return (Criteria) this;
        }

        public Criteria andDriverNameLike(String value) {
            addCriterion("driver_name like", value, "driverName");
            return (Criteria) this;
        }

        public Criteria andDriverNameNotLike(String value) {
            addCriterion("driver_name not like", value, "driverName");
            return (Criteria) this;
        }

        public Criteria andDriverNameIn(List<String> values) {
            addCriterion("driver_name in", values, "driverName");
            return (Criteria) this;
        }

        public Criteria andDriverNameNotIn(List<String> values) {
            addCriterion("driver_name not in", values, "driverName");
            return (Criteria) this;
        }

        public Criteria andDriverNameBetween(String value1, String value2) {
            addCriterion("driver_name between", value1, value2, "driverName");
            return (Criteria) this;
        }

        public Criteria andDriverNameNotBetween(String value1, String value2) {
            addCriterion("driver_name not between", value1, value2, "driverName");
            return (Criteria) this;
        }

        public Criteria andDriverMobileIsNull() {
            addCriterion("driver_mobile is null");
            return (Criteria) this;
        }

        public Criteria andDriverMobileIsNotNull() {
            addCriterion("driver_mobile is not null");
            return (Criteria) this;
        }

        public Criteria andDriverMobileEqualTo(String value) {
            addCriterion("driver_mobile =", value, "driverMobile");
            return (Criteria) this;
        }

        public Criteria andDriverMobileNotEqualTo(String value) {
            addCriterion("driver_mobile <>", value, "driverMobile");
            return (Criteria) this;
        }

        public Criteria andDriverMobileGreaterThan(String value) {
            addCriterion("driver_mobile >", value, "driverMobile");
            return (Criteria) this;
        }

        public Criteria andDriverMobileGreaterThanOrEqualTo(String value) {
            addCriterion("driver_mobile >=", value, "driverMobile");
            return (Criteria) this;
        }

        public Criteria andDriverMobileLessThan(String value) {
            addCriterion("driver_mobile <", value, "driverMobile");
            return (Criteria) this;
        }

        public Criteria andDriverMobileLessThanOrEqualTo(String value) {
            addCriterion("driver_mobile <=", value, "driverMobile");
            return (Criteria) this;
        }

        public Criteria andDriverMobileLike(String value) {
            addCriterion("driver_mobile like", value, "driverMobile");
            return (Criteria) this;
        }

        public Criteria andDriverMobileNotLike(String value) {
            addCriterion("driver_mobile not like", value, "driverMobile");
            return (Criteria) this;
        }

        public Criteria andDriverMobileIn(List<String> values) {
            addCriterion("driver_mobile in", values, "driverMobile");
            return (Criteria) this;
        }

        public Criteria andDriverMobileNotIn(List<String> values) {
            addCriterion("driver_mobile not in", values, "driverMobile");
            return (Criteria) this;
        }

        public Criteria andDriverMobileBetween(String value1, String value2) {
            addCriterion("driver_mobile between", value1, value2, "driverMobile");
            return (Criteria) this;
        }

        public Criteria andDriverMobileNotBetween(String value1, String value2) {
            addCriterion("driver_mobile not between", value1, value2, "driverMobile");
            return (Criteria) this;
        }

        public Criteria andStartDateIsNull() {
            addCriterion("start_date is null");
            return (Criteria) this;
        }

        public Criteria andStartDateIsNotNull() {
            addCriterion("start_date is not null");
            return (Criteria) this;
        }

        public Criteria andStartDateEqualTo(Date value) {
            addCriterion("start_date =", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotEqualTo(Date value) {
            addCriterion("start_date <>", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateGreaterThan(Date value) {
            addCriterion("start_date >", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateGreaterThanOrEqualTo(Date value) {
            addCriterion("start_date >=", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateLessThan(Date value) {
            addCriterion("start_date <", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateLessThanOrEqualTo(Date value) {
            addCriterion("start_date <=", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateIn(List<Date> values) {
            addCriterion("start_date in", values, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotIn(List<Date> values) {
            addCriterion("start_date not in", values, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateBetween(Date value1, Date value2) {
            addCriterion("start_date between", value1, value2, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotBetween(Date value1, Date value2) {
            addCriterion("start_date not between", value1, value2, "startDate");
            return (Criteria) this;
        }

        public Criteria andEndDateIsNull() {
            addCriterion("end_date is null");
            return (Criteria) this;
        }

        public Criteria andEndDateIsNotNull() {
            addCriterion("end_date is not null");
            return (Criteria) this;
        }

        public Criteria andEndDateEqualTo(Date value) {
            addCriterion("end_date =", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotEqualTo(Date value) {
            addCriterion("end_date <>", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateGreaterThan(Date value) {
            addCriterion("end_date >", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateGreaterThanOrEqualTo(Date value) {
            addCriterion("end_date >=", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateLessThan(Date value) {
            addCriterion("end_date <", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateLessThanOrEqualTo(Date value) {
            addCriterion("end_date <=", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateIn(List<Date> values) {
            addCriterion("end_date in", values, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotIn(List<Date> values) {
            addCriterion("end_date not in", values, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateBetween(Date value1, Date value2) {
            addCriterion("end_date between", value1, value2, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotBetween(Date value1, Date value2) {
            addCriterion("end_date not between", value1, value2, "endDate");
            return (Criteria) this;
        }

        public Criteria andAmountIsNull() {
            addCriterion("amount is null");
            return (Criteria) this;
        }

        public Criteria andAmountIsNotNull() {
            addCriterion("amount is not null");
            return (Criteria) this;
        }

        public Criteria andAmountEqualTo(BigDecimal value) {
            addCriterion("amount =", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotEqualTo(BigDecimal value) {
            addCriterion("amount <>", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountGreaterThan(BigDecimal value) {
            addCriterion("amount >", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("amount >=", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountLessThan(BigDecimal value) {
            addCriterion("amount <", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("amount <=", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountIn(List<BigDecimal> values) {
            addCriterion("amount in", values, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotIn(List<BigDecimal> values) {
            addCriterion("amount not in", values, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("amount between", value1, value2, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("amount not between", value1, value2, "amount");
            return (Criteria) this;
        }

        public Criteria andTaxRateIsNull() {
            addCriterion("tax_rate is null");
            return (Criteria) this;
        }

        public Criteria andTaxRateIsNotNull() {
            addCriterion("tax_rate is not null");
            return (Criteria) this;
        }

        public Criteria andTaxRateEqualTo(BigDecimal value) {
            addCriterion("tax_rate =", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateNotEqualTo(BigDecimal value) {
            addCriterion("tax_rate <>", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateGreaterThan(BigDecimal value) {
            addCriterion("tax_rate >", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("tax_rate >=", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateLessThan(BigDecimal value) {
            addCriterion("tax_rate <", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("tax_rate <=", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateIn(List<BigDecimal> values) {
            addCriterion("tax_rate in", values, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateNotIn(List<BigDecimal> values) {
            addCriterion("tax_rate not in", values, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("tax_rate between", value1, value2, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("tax_rate not between", value1, value2, "taxRate");
            return (Criteria) this;
        }

        public Criteria andPaymentDaysIsNull() {
            addCriterion("payment_days is null");
            return (Criteria) this;
        }

        public Criteria andPaymentDaysIsNotNull() {
            addCriterion("payment_days is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentDaysEqualTo(Integer value) {
            addCriterion("payment_days =", value, "paymentDays");
            return (Criteria) this;
        }

        public Criteria andPaymentDaysNotEqualTo(Integer value) {
            addCriterion("payment_days <>", value, "paymentDays");
            return (Criteria) this;
        }

        public Criteria andPaymentDaysGreaterThan(Integer value) {
            addCriterion("payment_days >", value, "paymentDays");
            return (Criteria) this;
        }

        public Criteria andPaymentDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("payment_days >=", value, "paymentDays");
            return (Criteria) this;
        }

        public Criteria andPaymentDaysLessThan(Integer value) {
            addCriterion("payment_days <", value, "paymentDays");
            return (Criteria) this;
        }

        public Criteria andPaymentDaysLessThanOrEqualTo(Integer value) {
            addCriterion("payment_days <=", value, "paymentDays");
            return (Criteria) this;
        }

        public Criteria andPaymentDaysIn(List<Integer> values) {
            addCriterion("payment_days in", values, "paymentDays");
            return (Criteria) this;
        }

        public Criteria andPaymentDaysNotIn(List<Integer> values) {
            addCriterion("payment_days not in", values, "paymentDays");
            return (Criteria) this;
        }

        public Criteria andPaymentDaysBetween(Integer value1, Integer value2) {
            addCriterion("payment_days between", value1, value2, "paymentDays");
            return (Criteria) this;
        }

        public Criteria andPaymentDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("payment_days not between", value1, value2, "paymentDays");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeIsNull() {
            addCriterion("supplier_code is null");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeIsNotNull() {
            addCriterion("supplier_code is not null");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeEqualTo(String value) {
            addCriterion("supplier_code =", value, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeNotEqualTo(String value) {
            addCriterion("supplier_code <>", value, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeGreaterThan(String value) {
            addCriterion("supplier_code >", value, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeGreaterThanOrEqualTo(String value) {
            addCriterion("supplier_code >=", value, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeLessThan(String value) {
            addCriterion("supplier_code <", value, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeLessThanOrEqualTo(String value) {
            addCriterion("supplier_code <=", value, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeLike(String value) {
            addCriterion("supplier_code like", value, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeNotLike(String value) {
            addCriterion("supplier_code not like", value, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeIn(List<String> values) {
            addCriterion("supplier_code in", values, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeNotIn(List<String> values) {
            addCriterion("supplier_code not in", values, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeBetween(String value1, String value2) {
            addCriterion("supplier_code between", value1, value2, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierCodeNotBetween(String value1, String value2) {
            addCriterion("supplier_code not between", value1, value2, "supplierCode");
            return (Criteria) this;
        }

        public Criteria andSupplierNameIsNull() {
            addCriterion("supplier_name is null");
            return (Criteria) this;
        }

        public Criteria andSupplierNameIsNotNull() {
            addCriterion("supplier_name is not null");
            return (Criteria) this;
        }

        public Criteria andSupplierNameEqualTo(String value) {
            addCriterion("supplier_name =", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameNotEqualTo(String value) {
            addCriterion("supplier_name <>", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameGreaterThan(String value) {
            addCriterion("supplier_name >", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameGreaterThanOrEqualTo(String value) {
            addCriterion("supplier_name >=", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameLessThan(String value) {
            addCriterion("supplier_name <", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameLessThanOrEqualTo(String value) {
            addCriterion("supplier_name <=", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameLike(String value) {
            addCriterion("supplier_name like", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameNotLike(String value) {
            addCriterion("supplier_name not like", value, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameIn(List<String> values) {
            addCriterion("supplier_name in", values, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameNotIn(List<String> values) {
            addCriterion("supplier_name not in", values, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameBetween(String value1, String value2) {
            addCriterion("supplier_name between", value1, value2, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierNameNotBetween(String value1, String value2) {
            addCriterion("supplier_name not between", value1, value2, "supplierName");
            return (Criteria) this;
        }

        public Criteria andSupplierPayableAmountIsNull() {
            addCriterion("supplier_payable_amount is null");
            return (Criteria) this;
        }

        public Criteria andSupplierPayableAmountIsNotNull() {
            addCriterion("supplier_payable_amount is not null");
            return (Criteria) this;
        }

        public Criteria andSupplierPayableAmountEqualTo(BigDecimal value) {
            addCriterion("supplier_payable_amount =", value, "supplierPayableAmount");
            return (Criteria) this;
        }

        public Criteria andSupplierPayableAmountNotEqualTo(BigDecimal value) {
            addCriterion("supplier_payable_amount <>", value, "supplierPayableAmount");
            return (Criteria) this;
        }

        public Criteria andSupplierPayableAmountGreaterThan(BigDecimal value) {
            addCriterion("supplier_payable_amount >", value, "supplierPayableAmount");
            return (Criteria) this;
        }

        public Criteria andSupplierPayableAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("supplier_payable_amount >=", value, "supplierPayableAmount");
            return (Criteria) this;
        }

        public Criteria andSupplierPayableAmountLessThan(BigDecimal value) {
            addCriterion("supplier_payable_amount <", value, "supplierPayableAmount");
            return (Criteria) this;
        }

        public Criteria andSupplierPayableAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("supplier_payable_amount <=", value, "supplierPayableAmount");
            return (Criteria) this;
        }

        public Criteria andSupplierPayableAmountIn(List<BigDecimal> values) {
            addCriterion("supplier_payable_amount in", values, "supplierPayableAmount");
            return (Criteria) this;
        }

        public Criteria andSupplierPayableAmountNotIn(List<BigDecimal> values) {
            addCriterion("supplier_payable_amount not in", values, "supplierPayableAmount");
            return (Criteria) this;
        }

        public Criteria andSupplierPayableAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("supplier_payable_amount between", value1, value2, "supplierPayableAmount");
            return (Criteria) this;
        }

        public Criteria andSupplierPayableAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("supplier_payable_amount not between", value1, value2, "supplierPayableAmount");
            return (Criteria) this;
        }

        public Criteria andVehicleProviderIsNull() {
            addCriterion("vehicle_provider is null");
            return (Criteria) this;
        }

        public Criteria andVehicleProviderIsNotNull() {
            addCriterion("vehicle_provider is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleProviderEqualTo(Byte value) {
            addCriterion("vehicle_provider =", value, "vehicleProvider");
            return (Criteria) this;
        }

        public Criteria andVehicleProviderNotEqualTo(Byte value) {
            addCriterion("vehicle_provider <>", value, "vehicleProvider");
            return (Criteria) this;
        }

        public Criteria andVehicleProviderGreaterThan(Byte value) {
            addCriterion("vehicle_provider >", value, "vehicleProvider");
            return (Criteria) this;
        }

        public Criteria andVehicleProviderGreaterThanOrEqualTo(Byte value) {
            addCriterion("vehicle_provider >=", value, "vehicleProvider");
            return (Criteria) this;
        }

        public Criteria andVehicleProviderLessThan(Byte value) {
            addCriterion("vehicle_provider <", value, "vehicleProvider");
            return (Criteria) this;
        }

        public Criteria andVehicleProviderLessThanOrEqualTo(Byte value) {
            addCriterion("vehicle_provider <=", value, "vehicleProvider");
            return (Criteria) this;
        }

        public Criteria andVehicleProviderIn(List<Byte> values) {
            addCriterion("vehicle_provider in", values, "vehicleProvider");
            return (Criteria) this;
        }

        public Criteria andVehicleProviderNotIn(List<Byte> values) {
            addCriterion("vehicle_provider not in", values, "vehicleProvider");
            return (Criteria) this;
        }

        public Criteria andVehicleProviderBetween(Byte value1, Byte value2) {
            addCriterion("vehicle_provider between", value1, value2, "vehicleProvider");
            return (Criteria) this;
        }

        public Criteria andVehicleProviderNotBetween(Byte value1, Byte value2) {
            addCriterion("vehicle_provider not between", value1, value2, "vehicleProvider");
            return (Criteria) this;
        }

        public Criteria andDriverProviderIsNull() {
            addCriterion("driver_provider is null");
            return (Criteria) this;
        }

        public Criteria andDriverProviderIsNotNull() {
            addCriterion("driver_provider is not null");
            return (Criteria) this;
        }

        public Criteria andDriverProviderEqualTo(Byte value) {
            addCriterion("driver_provider =", value, "driverProvider");
            return (Criteria) this;
        }

        public Criteria andDriverProviderNotEqualTo(Byte value) {
            addCriterion("driver_provider <>", value, "driverProvider");
            return (Criteria) this;
        }

        public Criteria andDriverProviderGreaterThan(Byte value) {
            addCriterion("driver_provider >", value, "driverProvider");
            return (Criteria) this;
        }

        public Criteria andDriverProviderGreaterThanOrEqualTo(Byte value) {
            addCriterion("driver_provider >=", value, "driverProvider");
            return (Criteria) this;
        }

        public Criteria andDriverProviderLessThan(Byte value) {
            addCriterion("driver_provider <", value, "driverProvider");
            return (Criteria) this;
        }

        public Criteria andDriverProviderLessThanOrEqualTo(Byte value) {
            addCriterion("driver_provider <=", value, "driverProvider");
            return (Criteria) this;
        }

        public Criteria andDriverProviderIn(List<Byte> values) {
            addCriterion("driver_provider in", values, "driverProvider");
            return (Criteria) this;
        }

        public Criteria andDriverProviderNotIn(List<Byte> values) {
            addCriterion("driver_provider not in", values, "driverProvider");
            return (Criteria) this;
        }

        public Criteria andDriverProviderBetween(Byte value1, Byte value2) {
            addCriterion("driver_provider between", value1, value2, "driverProvider");
            return (Criteria) this;
        }

        public Criteria andDriverProviderNotBetween(Byte value1, Byte value2) {
            addCriterion("driver_provider not between", value1, value2, "driverProvider");
            return (Criteria) this;
        }

        public Criteria andCreateCodeIsNull() {
            addCriterion("create_code is null");
            return (Criteria) this;
        }

        public Criteria andCreateCodeIsNotNull() {
            addCriterion("create_code is not null");
            return (Criteria) this;
        }

        public Criteria andCreateCodeEqualTo(String value) {
            addCriterion("create_code =", value, "createCode");
            return (Criteria) this;
        }

        public Criteria andCreateCodeNotEqualTo(String value) {
            addCriterion("create_code <>", value, "createCode");
            return (Criteria) this;
        }

        public Criteria andCreateCodeGreaterThan(String value) {
            addCriterion("create_code >", value, "createCode");
            return (Criteria) this;
        }

        public Criteria andCreateCodeGreaterThanOrEqualTo(String value) {
            addCriterion("create_code >=", value, "createCode");
            return (Criteria) this;
        }

        public Criteria andCreateCodeLessThan(String value) {
            addCriterion("create_code <", value, "createCode");
            return (Criteria) this;
        }

        public Criteria andCreateCodeLessThanOrEqualTo(String value) {
            addCriterion("create_code <=", value, "createCode");
            return (Criteria) this;
        }

        public Criteria andCreateCodeLike(String value) {
            addCriterion("create_code like", value, "createCode");
            return (Criteria) this;
        }

        public Criteria andCreateCodeNotLike(String value) {
            addCriterion("create_code not like", value, "createCode");
            return (Criteria) this;
        }

        public Criteria andCreateCodeIn(List<String> values) {
            addCriterion("create_code in", values, "createCode");
            return (Criteria) this;
        }

        public Criteria andCreateCodeNotIn(List<String> values) {
            addCriterion("create_code not in", values, "createCode");
            return (Criteria) this;
        }

        public Criteria andCreateCodeBetween(String value1, String value2) {
            addCriterion("create_code between", value1, value2, "createCode");
            return (Criteria) this;
        }

        public Criteria andCreateCodeNotBetween(String value1, String value2) {
            addCriterion("create_code not between", value1, value2, "createCode");
            return (Criteria) this;
        }

        public Criteria andCreateNameIsNull() {
            addCriterion("create_name is null");
            return (Criteria) this;
        }

        public Criteria andCreateNameIsNotNull() {
            addCriterion("create_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreateNameEqualTo(String value) {
            addCriterion("create_name =", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameNotEqualTo(String value) {
            addCriterion("create_name <>", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameGreaterThan(String value) {
            addCriterion("create_name >", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameGreaterThanOrEqualTo(String value) {
            addCriterion("create_name >=", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameLessThan(String value) {
            addCriterion("create_name <", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameLessThanOrEqualTo(String value) {
            addCriterion("create_name <=", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameLike(String value) {
            addCriterion("create_name like", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameNotLike(String value) {
            addCriterion("create_name not like", value, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameIn(List<String> values) {
            addCriterion("create_name in", values, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameNotIn(List<String> values) {
            addCriterion("create_name not in", values, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameBetween(String value1, String value2) {
            addCriterion("create_name between", value1, value2, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateNameNotBetween(String value1, String value2) {
            addCriterion("create_name not between", value1, value2, "createName");
            return (Criteria) this;
        }

        public Criteria andCreateStructCodeIsNull() {
            addCriterion("create_struct_code is null");
            return (Criteria) this;
        }

        public Criteria andCreateStructCodeIsNotNull() {
            addCriterion("create_struct_code is not null");
            return (Criteria) this;
        }

        public Criteria andCreateStructCodeEqualTo(String value) {
            addCriterion("create_struct_code =", value, "createStructCode");
            return (Criteria) this;
        }

        public Criteria andCreateStructCodeNotEqualTo(String value) {
            addCriterion("create_struct_code <>", value, "createStructCode");
            return (Criteria) this;
        }

        public Criteria andCreateStructCodeGreaterThan(String value) {
            addCriterion("create_struct_code >", value, "createStructCode");
            return (Criteria) this;
        }

        public Criteria andCreateStructCodeGreaterThanOrEqualTo(String value) {
            addCriterion("create_struct_code >=", value, "createStructCode");
            return (Criteria) this;
        }

        public Criteria andCreateStructCodeLessThan(String value) {
            addCriterion("create_struct_code <", value, "createStructCode");
            return (Criteria) this;
        }

        public Criteria andCreateStructCodeLessThanOrEqualTo(String value) {
            addCriterion("create_struct_code <=", value, "createStructCode");
            return (Criteria) this;
        }

        public Criteria andCreateStructCodeLike(String value) {
            addCriterion("create_struct_code like", value, "createStructCode");
            return (Criteria) this;
        }

        public Criteria andCreateStructCodeNotLike(String value) {
            addCriterion("create_struct_code not like", value, "createStructCode");
            return (Criteria) this;
        }

        public Criteria andCreateStructCodeIn(List<String> values) {
            addCriterion("create_struct_code in", values, "createStructCode");
            return (Criteria) this;
        }

        public Criteria andCreateStructCodeNotIn(List<String> values) {
            addCriterion("create_struct_code not in", values, "createStructCode");
            return (Criteria) this;
        }

        public Criteria andCreateStructCodeBetween(String value1, String value2) {
            addCriterion("create_struct_code between", value1, value2, "createStructCode");
            return (Criteria) this;
        }

        public Criteria andCreateStructCodeNotBetween(String value1, String value2) {
            addCriterion("create_struct_code not between", value1, value2, "createStructCode");
            return (Criteria) this;
        }

        public Criteria andCreateStructNameIsNull() {
            addCriterion("create_struct_name is null");
            return (Criteria) this;
        }

        public Criteria andCreateStructNameIsNotNull() {
            addCriterion("create_struct_name is not null");
            return (Criteria) this;
        }

        public Criteria andCreateStructNameEqualTo(String value) {
            addCriterion("create_struct_name =", value, "createStructName");
            return (Criteria) this;
        }

        public Criteria andCreateStructNameNotEqualTo(String value) {
            addCriterion("create_struct_name <>", value, "createStructName");
            return (Criteria) this;
        }

        public Criteria andCreateStructNameGreaterThan(String value) {
            addCriterion("create_struct_name >", value, "createStructName");
            return (Criteria) this;
        }

        public Criteria andCreateStructNameGreaterThanOrEqualTo(String value) {
            addCriterion("create_struct_name >=", value, "createStructName");
            return (Criteria) this;
        }

        public Criteria andCreateStructNameLessThan(String value) {
            addCriterion("create_struct_name <", value, "createStructName");
            return (Criteria) this;
        }

        public Criteria andCreateStructNameLessThanOrEqualTo(String value) {
            addCriterion("create_struct_name <=", value, "createStructName");
            return (Criteria) this;
        }

        public Criteria andCreateStructNameLike(String value) {
            addCriterion("create_struct_name like", value, "createStructName");
            return (Criteria) this;
        }

        public Criteria andCreateStructNameNotLike(String value) {
            addCriterion("create_struct_name not like", value, "createStructName");
            return (Criteria) this;
        }

        public Criteria andCreateStructNameIn(List<String> values) {
            addCriterion("create_struct_name in", values, "createStructName");
            return (Criteria) this;
        }

        public Criteria andCreateStructNameNotIn(List<String> values) {
            addCriterion("create_struct_name not in", values, "createStructName");
            return (Criteria) this;
        }

        public Criteria andCreateStructNameBetween(String value1, String value2) {
            addCriterion("create_struct_name between", value1, value2, "createStructName");
            return (Criteria) this;
        }

        public Criteria andCreateStructNameNotBetween(String value1, String value2) {
            addCriterion("create_struct_name not between", value1, value2, "createStructName");
            return (Criteria) this;
        }

        public Criteria andUpdateCodeIsNull() {
            addCriterion("update_code is null");
            return (Criteria) this;
        }

        public Criteria andUpdateCodeIsNotNull() {
            addCriterion("update_code is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateCodeEqualTo(String value) {
            addCriterion("update_code =", value, "updateCode");
            return (Criteria) this;
        }

        public Criteria andUpdateCodeNotEqualTo(String value) {
            addCriterion("update_code <>", value, "updateCode");
            return (Criteria) this;
        }

        public Criteria andUpdateCodeGreaterThan(String value) {
            addCriterion("update_code >", value, "updateCode");
            return (Criteria) this;
        }

        public Criteria andUpdateCodeGreaterThanOrEqualTo(String value) {
            addCriterion("update_code >=", value, "updateCode");
            return (Criteria) this;
        }

        public Criteria andUpdateCodeLessThan(String value) {
            addCriterion("update_code <", value, "updateCode");
            return (Criteria) this;
        }

        public Criteria andUpdateCodeLessThanOrEqualTo(String value) {
            addCriterion("update_code <=", value, "updateCode");
            return (Criteria) this;
        }

        public Criteria andUpdateCodeLike(String value) {
            addCriterion("update_code like", value, "updateCode");
            return (Criteria) this;
        }

        public Criteria andUpdateCodeNotLike(String value) {
            addCriterion("update_code not like", value, "updateCode");
            return (Criteria) this;
        }

        public Criteria andUpdateCodeIn(List<String> values) {
            addCriterion("update_code in", values, "updateCode");
            return (Criteria) this;
        }

        public Criteria andUpdateCodeNotIn(List<String> values) {
            addCriterion("update_code not in", values, "updateCode");
            return (Criteria) this;
        }

        public Criteria andUpdateCodeBetween(String value1, String value2) {
            addCriterion("update_code between", value1, value2, "updateCode");
            return (Criteria) this;
        }

        public Criteria andUpdateCodeNotBetween(String value1, String value2) {
            addCriterion("update_code not between", value1, value2, "updateCode");
            return (Criteria) this;
        }

        public Criteria andUpdateNameIsNull() {
            addCriterion("update_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdateNameIsNotNull() {
            addCriterion("update_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateNameEqualTo(String value) {
            addCriterion("update_name =", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotEqualTo(String value) {
            addCriterion("update_name <>", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameGreaterThan(String value) {
            addCriterion("update_name >", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameGreaterThanOrEqualTo(String value) {
            addCriterion("update_name >=", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameLessThan(String value) {
            addCriterion("update_name <", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameLessThanOrEqualTo(String value) {
            addCriterion("update_name <=", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameLike(String value) {
            addCriterion("update_name like", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotLike(String value) {
            addCriterion("update_name not like", value, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameIn(List<String> values) {
            addCriterion("update_name in", values, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotIn(List<String> values) {
            addCriterion("update_name not in", values, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameBetween(String value1, String value2) {
            addCriterion("update_name between", value1, value2, "updateName");
            return (Criteria) this;
        }

        public Criteria andUpdateNameNotBetween(String value1, String value2) {
            addCriterion("update_name not between", value1, value2, "updateName");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andSupplierRateIsNull() {
            addCriterion("supplier_rate is null");
            return (Criteria) this;
        }

        public Criteria andSupplierRateIsNotNull() {
            addCriterion("supplier_rate is not null");
            return (Criteria) this;
        }

        public Criteria andSupplierRateEqualTo(BigDecimal value) {
            addCriterion("supplier_rate =", value, "supplierRate");
            return (Criteria) this;
        }

        public Criteria andSupplierRateNotEqualTo(BigDecimal value) {
            addCriterion("supplier_rate <>", value, "supplierRate");
            return (Criteria) this;
        }

        public Criteria andSupplierRateGreaterThan(BigDecimal value) {
            addCriterion("supplier_rate >", value, "supplierRate");
            return (Criteria) this;
        }

        public Criteria andSupplierRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("supplier_rate >=", value, "supplierRate");
            return (Criteria) this;
        }

        public Criteria andSupplierRateLessThan(BigDecimal value) {
            addCriterion("supplier_rate <", value, "supplierRate");
            return (Criteria) this;
        }

        public Criteria andSupplierRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("supplier_rate <=", value, "supplierRate");
            return (Criteria) this;
        }

        public Criteria andSupplierRateIn(List<BigDecimal> values) {
            addCriterion("supplier_rate in", values, "supplierRate");
            return (Criteria) this;
        }

        public Criteria andSupplierRateNotIn(List<BigDecimal> values) {
            addCriterion("supplier_rate not in", values, "supplierRate");
            return (Criteria) this;
        }

        public Criteria andSupplierRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("supplier_rate between", value1, value2, "supplierRate");
            return (Criteria) this;
        }

        public Criteria andSupplierRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("supplier_rate not between", value1, value2, "supplierRate");
            return (Criteria) this;
        }
    }

    /**
     * business_order_addition_record
     */
    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    /**
     * business_order_addition_record null
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}