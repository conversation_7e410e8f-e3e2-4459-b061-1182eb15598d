package com.izu.order.entity.mrcar;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TemporalSharedVehicleRecordExample {
    /**
     * t_temporal_shared_vehicle_record
     */
    protected String orderByClause;

    /**
     * t_temporal_shared_vehicle_record
     */
    protected boolean distinct;

    /**
     * t_temporal_shared_vehicle_record
     */
    protected List<Criteria> oredCriteria;

    public TemporalSharedVehicleRecordExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andWarnSnIsNull() {
            addCriterion("warn_sn is null");
            return (Criteria) this;
        }

        public Criteria andWarnSnIsNotNull() {
            addCriterion("warn_sn is not null");
            return (Criteria) this;
        }

        public Criteria andWarnSnEqualTo(String value) {
            addCriterion("warn_sn =", value, "warnSn");
            return (Criteria) this;
        }

        public Criteria andWarnSnNotEqualTo(String value) {
            addCriterion("warn_sn <>", value, "warnSn");
            return (Criteria) this;
        }

        public Criteria andWarnSnGreaterThan(String value) {
            addCriterion("warn_sn >", value, "warnSn");
            return (Criteria) this;
        }

        public Criteria andWarnSnGreaterThanOrEqualTo(String value) {
            addCriterion("warn_sn >=", value, "warnSn");
            return (Criteria) this;
        }

        public Criteria andWarnSnLessThan(String value) {
            addCriterion("warn_sn <", value, "warnSn");
            return (Criteria) this;
        }

        public Criteria andWarnSnLessThanOrEqualTo(String value) {
            addCriterion("warn_sn <=", value, "warnSn");
            return (Criteria) this;
        }

        public Criteria andWarnSnLike(String value) {
            addCriterion("warn_sn like", value, "warnSn");
            return (Criteria) this;
        }

        public Criteria andWarnSnNotLike(String value) {
            addCriterion("warn_sn not like", value, "warnSn");
            return (Criteria) this;
        }

        public Criteria andWarnSnIn(List<String> values) {
            addCriterion("warn_sn in", values, "warnSn");
            return (Criteria) this;
        }

        public Criteria andWarnSnNotIn(List<String> values) {
            addCriterion("warn_sn not in", values, "warnSn");
            return (Criteria) this;
        }

        public Criteria andWarnSnBetween(String value1, String value2) {
            addCriterion("warn_sn between", value1, value2, "warnSn");
            return (Criteria) this;
        }

        public Criteria andWarnSnNotBetween(String value1, String value2) {
            addCriterion("warn_sn not between", value1, value2, "warnSn");
            return (Criteria) this;
        }

        public Criteria andWarnIdIsNull() {
            addCriterion("warn_id is null");
            return (Criteria) this;
        }

        public Criteria andWarnIdIsNotNull() {
            addCriterion("warn_id is not null");
            return (Criteria) this;
        }

        public Criteria andWarnIdEqualTo(Long value) {
            addCriterion("warn_id =", value, "warnId");
            return (Criteria) this;
        }

        public Criteria andWarnIdNotEqualTo(Long value) {
            addCriterion("warn_id <>", value, "warnId");
            return (Criteria) this;
        }

        public Criteria andWarnIdGreaterThan(Long value) {
            addCriterion("warn_id >", value, "warnId");
            return (Criteria) this;
        }

        public Criteria andWarnIdGreaterThanOrEqualTo(Long value) {
            addCriterion("warn_id >=", value, "warnId");
            return (Criteria) this;
        }

        public Criteria andWarnIdLessThan(Long value) {
            addCriterion("warn_id <", value, "warnId");
            return (Criteria) this;
        }

        public Criteria andWarnIdLessThanOrEqualTo(Long value) {
            addCriterion("warn_id <=", value, "warnId");
            return (Criteria) this;
        }

        public Criteria andWarnIdIn(List<Long> values) {
            addCriterion("warn_id in", values, "warnId");
            return (Criteria) this;
        }

        public Criteria andWarnIdNotIn(List<Long> values) {
            addCriterion("warn_id not in", values, "warnId");
            return (Criteria) this;
        }

        public Criteria andWarnIdBetween(Long value1, Long value2) {
            addCriterion("warn_id between", value1, value2, "warnId");
            return (Criteria) this;
        }

        public Criteria andWarnIdNotBetween(Long value1, Long value2) {
            addCriterion("warn_id not between", value1, value2, "warnId");
            return (Criteria) this;
        }

        public Criteria andRecordStatusIsNull() {
            addCriterion("record_status is null");
            return (Criteria) this;
        }

        public Criteria andRecordStatusIsNotNull() {
            addCriterion("record_status is not null");
            return (Criteria) this;
        }

        public Criteria andRecordStatusEqualTo(Byte value) {
            addCriterion("record_status =", value, "recordStatus");
            return (Criteria) this;
        }

        public Criteria andRecordStatusNotEqualTo(Byte value) {
            addCriterion("record_status <>", value, "recordStatus");
            return (Criteria) this;
        }

        public Criteria andRecordStatusGreaterThan(Byte value) {
            addCriterion("record_status >", value, "recordStatus");
            return (Criteria) this;
        }

        public Criteria andRecordStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("record_status >=", value, "recordStatus");
            return (Criteria) this;
        }

        public Criteria andRecordStatusLessThan(Byte value) {
            addCriterion("record_status <", value, "recordStatus");
            return (Criteria) this;
        }

        public Criteria andRecordStatusLessThanOrEqualTo(Byte value) {
            addCriterion("record_status <=", value, "recordStatus");
            return (Criteria) this;
        }

        public Criteria andRecordStatusIn(List<Byte> values) {
            addCriterion("record_status in", values, "recordStatus");
            return (Criteria) this;
        }

        public Criteria andRecordStatusNotIn(List<Byte> values) {
            addCriterion("record_status not in", values, "recordStatus");
            return (Criteria) this;
        }

        public Criteria andRecordStatusBetween(Byte value1, Byte value2) {
            addCriterion("record_status between", value1, value2, "recordStatus");
            return (Criteria) this;
        }

        public Criteria andRecordStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("record_status not between", value1, value2, "recordStatus");
            return (Criteria) this;
        }

        public Criteria andWarnStartTimeIsNull() {
            addCriterion("warn_start_time is null");
            return (Criteria) this;
        }

        public Criteria andWarnStartTimeIsNotNull() {
            addCriterion("warn_start_time is not null");
            return (Criteria) this;
        }

        public Criteria andWarnStartTimeEqualTo(Date value) {
            addCriterion("warn_start_time =", value, "warnStartTime");
            return (Criteria) this;
        }

        public Criteria andWarnStartTimeNotEqualTo(Date value) {
            addCriterion("warn_start_time <>", value, "warnStartTime");
            return (Criteria) this;
        }

        public Criteria andWarnStartTimeGreaterThan(Date value) {
            addCriterion("warn_start_time >", value, "warnStartTime");
            return (Criteria) this;
        }

        public Criteria andWarnStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("warn_start_time >=", value, "warnStartTime");
            return (Criteria) this;
        }

        public Criteria andWarnStartTimeLessThan(Date value) {
            addCriterion("warn_start_time <", value, "warnStartTime");
            return (Criteria) this;
        }

        public Criteria andWarnStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("warn_start_time <=", value, "warnStartTime");
            return (Criteria) this;
        }

        public Criteria andWarnStartTimeIn(List<Date> values) {
            addCriterion("warn_start_time in", values, "warnStartTime");
            return (Criteria) this;
        }

        public Criteria andWarnStartTimeNotIn(List<Date> values) {
            addCriterion("warn_start_time not in", values, "warnStartTime");
            return (Criteria) this;
        }

        public Criteria andWarnStartTimeBetween(Date value1, Date value2) {
            addCriterion("warn_start_time between", value1, value2, "warnStartTime");
            return (Criteria) this;
        }

        public Criteria andWarnStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("warn_start_time not between", value1, value2, "warnStartTime");
            return (Criteria) this;
        }

        public Criteria andWarnEndTimeIsNull() {
            addCriterion("warn_end_time is null");
            return (Criteria) this;
        }

        public Criteria andWarnEndTimeIsNotNull() {
            addCriterion("warn_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andWarnEndTimeEqualTo(Date value) {
            addCriterion("warn_end_time =", value, "warnEndTime");
            return (Criteria) this;
        }

        public Criteria andWarnEndTimeNotEqualTo(Date value) {
            addCriterion("warn_end_time <>", value, "warnEndTime");
            return (Criteria) this;
        }

        public Criteria andWarnEndTimeGreaterThan(Date value) {
            addCriterion("warn_end_time >", value, "warnEndTime");
            return (Criteria) this;
        }

        public Criteria andWarnEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("warn_end_time >=", value, "warnEndTime");
            return (Criteria) this;
        }

        public Criteria andWarnEndTimeLessThan(Date value) {
            addCriterion("warn_end_time <", value, "warnEndTime");
            return (Criteria) this;
        }

        public Criteria andWarnEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("warn_end_time <=", value, "warnEndTime");
            return (Criteria) this;
        }

        public Criteria andWarnEndTimeIn(List<Date> values) {
            addCriterion("warn_end_time in", values, "warnEndTime");
            return (Criteria) this;
        }

        public Criteria andWarnEndTimeNotIn(List<Date> values) {
            addCriterion("warn_end_time not in", values, "warnEndTime");
            return (Criteria) this;
        }

        public Criteria andWarnEndTimeBetween(Date value1, Date value2) {
            addCriterion("warn_end_time between", value1, value2, "warnEndTime");
            return (Criteria) this;
        }

        public Criteria andWarnEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("warn_end_time not between", value1, value2, "warnEndTime");
            return (Criteria) this;
        }

        public Criteria andWarnStartLatitudeIsNull() {
            addCriterion("warn_start_latitude is null");
            return (Criteria) this;
        }

        public Criteria andWarnStartLatitudeIsNotNull() {
            addCriterion("warn_start_latitude is not null");
            return (Criteria) this;
        }

        public Criteria andWarnStartLatitudeEqualTo(BigDecimal value) {
            addCriterion("warn_start_latitude =", value, "warnStartLatitude");
            return (Criteria) this;
        }

        public Criteria andWarnStartLatitudeNotEqualTo(BigDecimal value) {
            addCriterion("warn_start_latitude <>", value, "warnStartLatitude");
            return (Criteria) this;
        }

        public Criteria andWarnStartLatitudeGreaterThan(BigDecimal value) {
            addCriterion("warn_start_latitude >", value, "warnStartLatitude");
            return (Criteria) this;
        }

        public Criteria andWarnStartLatitudeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("warn_start_latitude >=", value, "warnStartLatitude");
            return (Criteria) this;
        }

        public Criteria andWarnStartLatitudeLessThan(BigDecimal value) {
            addCriterion("warn_start_latitude <", value, "warnStartLatitude");
            return (Criteria) this;
        }

        public Criteria andWarnStartLatitudeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("warn_start_latitude <=", value, "warnStartLatitude");
            return (Criteria) this;
        }

        public Criteria andWarnStartLatitudeIn(List<BigDecimal> values) {
            addCriterion("warn_start_latitude in", values, "warnStartLatitude");
            return (Criteria) this;
        }

        public Criteria andWarnStartLatitudeNotIn(List<BigDecimal> values) {
            addCriterion("warn_start_latitude not in", values, "warnStartLatitude");
            return (Criteria) this;
        }

        public Criteria andWarnStartLatitudeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("warn_start_latitude between", value1, value2, "warnStartLatitude");
            return (Criteria) this;
        }

        public Criteria andWarnStartLatitudeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("warn_start_latitude not between", value1, value2, "warnStartLatitude");
            return (Criteria) this;
        }

        public Criteria andWarnStartLongitudeIsNull() {
            addCriterion("warn_start_longitude is null");
            return (Criteria) this;
        }

        public Criteria andWarnStartLongitudeIsNotNull() {
            addCriterion("warn_start_longitude is not null");
            return (Criteria) this;
        }

        public Criteria andWarnStartLongitudeEqualTo(BigDecimal value) {
            addCriterion("warn_start_longitude =", value, "warnStartLongitude");
            return (Criteria) this;
        }

        public Criteria andWarnStartLongitudeNotEqualTo(BigDecimal value) {
            addCriterion("warn_start_longitude <>", value, "warnStartLongitude");
            return (Criteria) this;
        }

        public Criteria andWarnStartLongitudeGreaterThan(BigDecimal value) {
            addCriterion("warn_start_longitude >", value, "warnStartLongitude");
            return (Criteria) this;
        }

        public Criteria andWarnStartLongitudeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("warn_start_longitude >=", value, "warnStartLongitude");
            return (Criteria) this;
        }

        public Criteria andWarnStartLongitudeLessThan(BigDecimal value) {
            addCriterion("warn_start_longitude <", value, "warnStartLongitude");
            return (Criteria) this;
        }

        public Criteria andWarnStartLongitudeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("warn_start_longitude <=", value, "warnStartLongitude");
            return (Criteria) this;
        }

        public Criteria andWarnStartLongitudeIn(List<BigDecimal> values) {
            addCriterion("warn_start_longitude in", values, "warnStartLongitude");
            return (Criteria) this;
        }

        public Criteria andWarnStartLongitudeNotIn(List<BigDecimal> values) {
            addCriterion("warn_start_longitude not in", values, "warnStartLongitude");
            return (Criteria) this;
        }

        public Criteria andWarnStartLongitudeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("warn_start_longitude between", value1, value2, "warnStartLongitude");
            return (Criteria) this;
        }

        public Criteria andWarnStartLongitudeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("warn_start_longitude not between", value1, value2, "warnStartLongitude");
            return (Criteria) this;
        }

        public Criteria andWarnEndLatitudeIsNull() {
            addCriterion("warn_end_latitude is null");
            return (Criteria) this;
        }

        public Criteria andWarnEndLatitudeIsNotNull() {
            addCriterion("warn_end_latitude is not null");
            return (Criteria) this;
        }

        public Criteria andWarnEndLatitudeEqualTo(BigDecimal value) {
            addCriterion("warn_end_latitude =", value, "warnEndLatitude");
            return (Criteria) this;
        }

        public Criteria andWarnEndLatitudeNotEqualTo(BigDecimal value) {
            addCriterion("warn_end_latitude <>", value, "warnEndLatitude");
            return (Criteria) this;
        }

        public Criteria andWarnEndLatitudeGreaterThan(BigDecimal value) {
            addCriterion("warn_end_latitude >", value, "warnEndLatitude");
            return (Criteria) this;
        }

        public Criteria andWarnEndLatitudeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("warn_end_latitude >=", value, "warnEndLatitude");
            return (Criteria) this;
        }

        public Criteria andWarnEndLatitudeLessThan(BigDecimal value) {
            addCriterion("warn_end_latitude <", value, "warnEndLatitude");
            return (Criteria) this;
        }

        public Criteria andWarnEndLatitudeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("warn_end_latitude <=", value, "warnEndLatitude");
            return (Criteria) this;
        }

        public Criteria andWarnEndLatitudeIn(List<BigDecimal> values) {
            addCriterion("warn_end_latitude in", values, "warnEndLatitude");
            return (Criteria) this;
        }

        public Criteria andWarnEndLatitudeNotIn(List<BigDecimal> values) {
            addCriterion("warn_end_latitude not in", values, "warnEndLatitude");
            return (Criteria) this;
        }

        public Criteria andWarnEndLatitudeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("warn_end_latitude between", value1, value2, "warnEndLatitude");
            return (Criteria) this;
        }

        public Criteria andWarnEndLatitudeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("warn_end_latitude not between", value1, value2, "warnEndLatitude");
            return (Criteria) this;
        }

        public Criteria andWarnEndLongitudeIsNull() {
            addCriterion("warn_end_longitude is null");
            return (Criteria) this;
        }

        public Criteria andWarnEndLongitudeIsNotNull() {
            addCriterion("warn_end_longitude is not null");
            return (Criteria) this;
        }

        public Criteria andWarnEndLongitudeEqualTo(BigDecimal value) {
            addCriterion("warn_end_longitude =", value, "warnEndLongitude");
            return (Criteria) this;
        }

        public Criteria andWarnEndLongitudeNotEqualTo(BigDecimal value) {
            addCriterion("warn_end_longitude <>", value, "warnEndLongitude");
            return (Criteria) this;
        }

        public Criteria andWarnEndLongitudeGreaterThan(BigDecimal value) {
            addCriterion("warn_end_longitude >", value, "warnEndLongitude");
            return (Criteria) this;
        }

        public Criteria andWarnEndLongitudeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("warn_end_longitude >=", value, "warnEndLongitude");
            return (Criteria) this;
        }

        public Criteria andWarnEndLongitudeLessThan(BigDecimal value) {
            addCriterion("warn_end_longitude <", value, "warnEndLongitude");
            return (Criteria) this;
        }

        public Criteria andWarnEndLongitudeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("warn_end_longitude <=", value, "warnEndLongitude");
            return (Criteria) this;
        }

        public Criteria andWarnEndLongitudeIn(List<BigDecimal> values) {
            addCriterion("warn_end_longitude in", values, "warnEndLongitude");
            return (Criteria) this;
        }

        public Criteria andWarnEndLongitudeNotIn(List<BigDecimal> values) {
            addCriterion("warn_end_longitude not in", values, "warnEndLongitude");
            return (Criteria) this;
        }

        public Criteria andWarnEndLongitudeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("warn_end_longitude between", value1, value2, "warnEndLongitude");
            return (Criteria) this;
        }

        public Criteria andWarnEndLongitudeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("warn_end_longitude not between", value1, value2, "warnEndLongitude");
            return (Criteria) this;
        }

        public Criteria andDurationIsNull() {
            addCriterion("duration is null");
            return (Criteria) this;
        }

        public Criteria andDurationIsNotNull() {
            addCriterion("duration is not null");
            return (Criteria) this;
        }

        public Criteria andDurationEqualTo(Integer value) {
            addCriterion("duration =", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationNotEqualTo(Integer value) {
            addCriterion("duration <>", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationGreaterThan(Integer value) {
            addCriterion("duration >", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationGreaterThanOrEqualTo(Integer value) {
            addCriterion("duration >=", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationLessThan(Integer value) {
            addCriterion("duration <", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationLessThanOrEqualTo(Integer value) {
            addCriterion("duration <=", value, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationIn(List<Integer> values) {
            addCriterion("duration in", values, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationNotIn(List<Integer> values) {
            addCriterion("duration not in", values, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationBetween(Integer value1, Integer value2) {
            addCriterion("duration between", value1, value2, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationNotBetween(Integer value1, Integer value2) {
            addCriterion("duration not between", value1, value2, "duration");
            return (Criteria) this;
        }

        public Criteria andDurationHourIsNull() {
            addCriterion("duration_hour is null");
            return (Criteria) this;
        }

        public Criteria andDurationHourIsNotNull() {
            addCriterion("duration_hour is not null");
            return (Criteria) this;
        }

        public Criteria andDurationHourEqualTo(BigDecimal value) {
            addCriterion("duration_hour =", value, "durationHour");
            return (Criteria) this;
        }

        public Criteria andDurationHourNotEqualTo(BigDecimal value) {
            addCriterion("duration_hour <>", value, "durationHour");
            return (Criteria) this;
        }

        public Criteria andDurationHourGreaterThan(BigDecimal value) {
            addCriterion("duration_hour >", value, "durationHour");
            return (Criteria) this;
        }

        public Criteria andDurationHourGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("duration_hour >=", value, "durationHour");
            return (Criteria) this;
        }

        public Criteria andDurationHourLessThan(BigDecimal value) {
            addCriterion("duration_hour <", value, "durationHour");
            return (Criteria) this;
        }

        public Criteria andDurationHourLessThanOrEqualTo(BigDecimal value) {
            addCriterion("duration_hour <=", value, "durationHour");
            return (Criteria) this;
        }

        public Criteria andDurationHourIn(List<BigDecimal> values) {
            addCriterion("duration_hour in", values, "durationHour");
            return (Criteria) this;
        }

        public Criteria andDurationHourNotIn(List<BigDecimal> values) {
            addCriterion("duration_hour not in", values, "durationHour");
            return (Criteria) this;
        }

        public Criteria andDurationHourBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("duration_hour between", value1, value2, "durationHour");
            return (Criteria) this;
        }

        public Criteria andDurationHourNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("duration_hour not between", value1, value2, "durationHour");
            return (Criteria) this;
        }

        public Criteria andTripMileageIsNull() {
            addCriterion("trip_mileage is null");
            return (Criteria) this;
        }

        public Criteria andTripMileageIsNotNull() {
            addCriterion("trip_mileage is not null");
            return (Criteria) this;
        }

        public Criteria andTripMileageEqualTo(BigDecimal value) {
            addCriterion("trip_mileage =", value, "tripMileage");
            return (Criteria) this;
        }

        public Criteria andTripMileageNotEqualTo(BigDecimal value) {
            addCriterion("trip_mileage <>", value, "tripMileage");
            return (Criteria) this;
        }

        public Criteria andTripMileageGreaterThan(BigDecimal value) {
            addCriterion("trip_mileage >", value, "tripMileage");
            return (Criteria) this;
        }

        public Criteria andTripMileageGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("trip_mileage >=", value, "tripMileage");
            return (Criteria) this;
        }

        public Criteria andTripMileageLessThan(BigDecimal value) {
            addCriterion("trip_mileage <", value, "tripMileage");
            return (Criteria) this;
        }

        public Criteria andTripMileageLessThanOrEqualTo(BigDecimal value) {
            addCriterion("trip_mileage <=", value, "tripMileage");
            return (Criteria) this;
        }

        public Criteria andTripMileageIn(List<BigDecimal> values) {
            addCriterion("trip_mileage in", values, "tripMileage");
            return (Criteria) this;
        }

        public Criteria andTripMileageNotIn(List<BigDecimal> values) {
            addCriterion("trip_mileage not in", values, "tripMileage");
            return (Criteria) this;
        }

        public Criteria andTripMileageBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("trip_mileage between", value1, value2, "tripMileage");
            return (Criteria) this;
        }

        public Criteria andTripMileageNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("trip_mileage not between", value1, value2, "tripMileage");
            return (Criteria) this;
        }

        public Criteria andOrderAssociateStatusIsNull() {
            addCriterion("order_associate_status is null");
            return (Criteria) this;
        }

        public Criteria andOrderAssociateStatusIsNotNull() {
            addCriterion("order_associate_status is not null");
            return (Criteria) this;
        }

        public Criteria andOrderAssociateStatusEqualTo(Byte value) {
            addCriterion("order_associate_status =", value, "orderAssociateStatus");
            return (Criteria) this;
        }

        public Criteria andOrderAssociateStatusNotEqualTo(Byte value) {
            addCriterion("order_associate_status <>", value, "orderAssociateStatus");
            return (Criteria) this;
        }

        public Criteria andOrderAssociateStatusGreaterThan(Byte value) {
            addCriterion("order_associate_status >", value, "orderAssociateStatus");
            return (Criteria) this;
        }

        public Criteria andOrderAssociateStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("order_associate_status >=", value, "orderAssociateStatus");
            return (Criteria) this;
        }

        public Criteria andOrderAssociateStatusLessThan(Byte value) {
            addCriterion("order_associate_status <", value, "orderAssociateStatus");
            return (Criteria) this;
        }

        public Criteria andOrderAssociateStatusLessThanOrEqualTo(Byte value) {
            addCriterion("order_associate_status <=", value, "orderAssociateStatus");
            return (Criteria) this;
        }

        public Criteria andOrderAssociateStatusIn(List<Byte> values) {
            addCriterion("order_associate_status in", values, "orderAssociateStatus");
            return (Criteria) this;
        }

        public Criteria andOrderAssociateStatusNotIn(List<Byte> values) {
            addCriterion("order_associate_status not in", values, "orderAssociateStatus");
            return (Criteria) this;
        }

        public Criteria andOrderAssociateStatusBetween(Byte value1, Byte value2) {
            addCriterion("order_associate_status between", value1, value2, "orderAssociateStatus");
            return (Criteria) this;
        }

        public Criteria andOrderAssociateStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("order_associate_status not between", value1, value2, "orderAssociateStatus");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIsNull() {
            addCriterion("update_user_id is null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIsNotNull() {
            addCriterion("update_user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdEqualTo(Integer value) {
            addCriterion("update_user_id =", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotEqualTo(Integer value) {
            addCriterion("update_user_id <>", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdGreaterThan(Integer value) {
            addCriterion("update_user_id >", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("update_user_id >=", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLessThan(Integer value) {
            addCriterion("update_user_id <", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdLessThanOrEqualTo(Integer value) {
            addCriterion("update_user_id <=", value, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdIn(List<Integer> values) {
            addCriterion("update_user_id in", values, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotIn(List<Integer> values) {
            addCriterion("update_user_id not in", values, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdBetween(Integer value1, Integer value2) {
            addCriterion("update_user_id between", value1, value2, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserIdNotBetween(Integer value1, Integer value2) {
            addCriterion("update_user_id not between", value1, value2, "updateUserId");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNameIsNull() {
            addCriterion("update_user_name is null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNameIsNotNull() {
            addCriterion("update_user_name is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNameEqualTo(String value) {
            addCriterion("update_user_name =", value, "updateUserName");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNameNotEqualTo(String value) {
            addCriterion("update_user_name <>", value, "updateUserName");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNameGreaterThan(String value) {
            addCriterion("update_user_name >", value, "updateUserName");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNameGreaterThanOrEqualTo(String value) {
            addCriterion("update_user_name >=", value, "updateUserName");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNameLessThan(String value) {
            addCriterion("update_user_name <", value, "updateUserName");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNameLessThanOrEqualTo(String value) {
            addCriterion("update_user_name <=", value, "updateUserName");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNameLike(String value) {
            addCriterion("update_user_name like", value, "updateUserName");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNameNotLike(String value) {
            addCriterion("update_user_name not like", value, "updateUserName");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNameIn(List<String> values) {
            addCriterion("update_user_name in", values, "updateUserName");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNameNotIn(List<String> values) {
            addCriterion("update_user_name not in", values, "updateUserName");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNameBetween(String value1, String value2) {
            addCriterion("update_user_name between", value1, value2, "updateUserName");
            return (Criteria) this;
        }

        public Criteria andUpdateUserNameNotBetween(String value1, String value2) {
            addCriterion("update_user_name not between", value1, value2, "updateUserName");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkIsNull() {
            addCriterion("update_remark is null");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkIsNotNull() {
            addCriterion("update_remark is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkEqualTo(String value) {
            addCriterion("update_remark =", value, "updateRemark");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkNotEqualTo(String value) {
            addCriterion("update_remark <>", value, "updateRemark");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkGreaterThan(String value) {
            addCriterion("update_remark >", value, "updateRemark");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("update_remark >=", value, "updateRemark");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkLessThan(String value) {
            addCriterion("update_remark <", value, "updateRemark");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkLessThanOrEqualTo(String value) {
            addCriterion("update_remark <=", value, "updateRemark");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkLike(String value) {
            addCriterion("update_remark like", value, "updateRemark");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkNotLike(String value) {
            addCriterion("update_remark not like", value, "updateRemark");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkIn(List<String> values) {
            addCriterion("update_remark in", values, "updateRemark");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkNotIn(List<String> values) {
            addCriterion("update_remark not in", values, "updateRemark");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkBetween(String value1, String value2) {
            addCriterion("update_remark between", value1, value2, "updateRemark");
            return (Criteria) this;
        }

        public Criteria andUpdateRemarkNotBetween(String value1, String value2) {
            addCriterion("update_remark not between", value1, value2, "updateRemark");
            return (Criteria) this;
        }

        public Criteria andVehicleIdIsNull() {
            addCriterion("vehicle_id is null");
            return (Criteria) this;
        }

        public Criteria andVehicleIdIsNotNull() {
            addCriterion("vehicle_id is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleIdEqualTo(Long value) {
            addCriterion("vehicle_id =", value, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdNotEqualTo(Long value) {
            addCriterion("vehicle_id <>", value, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdGreaterThan(Long value) {
            addCriterion("vehicle_id >", value, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdGreaterThanOrEqualTo(Long value) {
            addCriterion("vehicle_id >=", value, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdLessThan(Long value) {
            addCriterion("vehicle_id <", value, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdLessThanOrEqualTo(Long value) {
            addCriterion("vehicle_id <=", value, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdIn(List<Long> values) {
            addCriterion("vehicle_id in", values, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdNotIn(List<Long> values) {
            addCriterion("vehicle_id not in", values, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdBetween(Long value1, Long value2) {
            addCriterion("vehicle_id between", value1, value2, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleIdNotBetween(Long value1, Long value2) {
            addCriterion("vehicle_id not between", value1, value2, "vehicleId");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseIsNull() {
            addCriterion("vehicle_license is null");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseIsNotNull() {
            addCriterion("vehicle_license is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseEqualTo(String value) {
            addCriterion("vehicle_license =", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseNotEqualTo(String value) {
            addCriterion("vehicle_license <>", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseGreaterThan(String value) {
            addCriterion("vehicle_license >", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_license >=", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseLessThan(String value) {
            addCriterion("vehicle_license <", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseLessThanOrEqualTo(String value) {
            addCriterion("vehicle_license <=", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseLike(String value) {
            addCriterion("vehicle_license like", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseNotLike(String value) {
            addCriterion("vehicle_license not like", value, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseIn(List<String> values) {
            addCriterion("vehicle_license in", values, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseNotIn(List<String> values) {
            addCriterion("vehicle_license not in", values, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseBetween(String value1, String value2) {
            addCriterion("vehicle_license between", value1, value2, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleLicenseNotBetween(String value1, String value2) {
            addCriterion("vehicle_license not between", value1, value2, "vehicleLicense");
            return (Criteria) this;
        }

        public Criteria andVehicleVinIsNull() {
            addCriterion("vehicle_vin is null");
            return (Criteria) this;
        }

        public Criteria andVehicleVinIsNotNull() {
            addCriterion("vehicle_vin is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleVinEqualTo(String value) {
            addCriterion("vehicle_vin =", value, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinNotEqualTo(String value) {
            addCriterion("vehicle_vin <>", value, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinGreaterThan(String value) {
            addCriterion("vehicle_vin >", value, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_vin >=", value, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinLessThan(String value) {
            addCriterion("vehicle_vin <", value, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinLessThanOrEqualTo(String value) {
            addCriterion("vehicle_vin <=", value, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinLike(String value) {
            addCriterion("vehicle_vin like", value, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinNotLike(String value) {
            addCriterion("vehicle_vin not like", value, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinIn(List<String> values) {
            addCriterion("vehicle_vin in", values, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinNotIn(List<String> values) {
            addCriterion("vehicle_vin not in", values, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinBetween(String value1, String value2) {
            addCriterion("vehicle_vin between", value1, value2, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleVinNotBetween(String value1, String value2) {
            addCriterion("vehicle_vin not between", value1, value2, "vehicleVin");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandIdIsNull() {
            addCriterion("vehicle_brand_id is null");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandIdIsNotNull() {
            addCriterion("vehicle_brand_id is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandIdEqualTo(Integer value) {
            addCriterion("vehicle_brand_id =", value, "vehicleBrandId");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandIdNotEqualTo(Integer value) {
            addCriterion("vehicle_brand_id <>", value, "vehicleBrandId");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandIdGreaterThan(Integer value) {
            addCriterion("vehicle_brand_id >", value, "vehicleBrandId");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("vehicle_brand_id >=", value, "vehicleBrandId");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandIdLessThan(Integer value) {
            addCriterion("vehicle_brand_id <", value, "vehicleBrandId");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandIdLessThanOrEqualTo(Integer value) {
            addCriterion("vehicle_brand_id <=", value, "vehicleBrandId");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandIdIn(List<Integer> values) {
            addCriterion("vehicle_brand_id in", values, "vehicleBrandId");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandIdNotIn(List<Integer> values) {
            addCriterion("vehicle_brand_id not in", values, "vehicleBrandId");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandIdBetween(Integer value1, Integer value2) {
            addCriterion("vehicle_brand_id between", value1, value2, "vehicleBrandId");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandIdNotBetween(Integer value1, Integer value2) {
            addCriterion("vehicle_brand_id not between", value1, value2, "vehicleBrandId");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeIsNull() {
            addCriterion("vehicle_brand_code is null");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeIsNotNull() {
            addCriterion("vehicle_brand_code is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeEqualTo(String value) {
            addCriterion("vehicle_brand_code =", value, "vehicleBrandCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeNotEqualTo(String value) {
            addCriterion("vehicle_brand_code <>", value, "vehicleBrandCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeGreaterThan(String value) {
            addCriterion("vehicle_brand_code >", value, "vehicleBrandCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_brand_code >=", value, "vehicleBrandCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeLessThan(String value) {
            addCriterion("vehicle_brand_code <", value, "vehicleBrandCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeLessThanOrEqualTo(String value) {
            addCriterion("vehicle_brand_code <=", value, "vehicleBrandCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeLike(String value) {
            addCriterion("vehicle_brand_code like", value, "vehicleBrandCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeNotLike(String value) {
            addCriterion("vehicle_brand_code not like", value, "vehicleBrandCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeIn(List<String> values) {
            addCriterion("vehicle_brand_code in", values, "vehicleBrandCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeNotIn(List<String> values) {
            addCriterion("vehicle_brand_code not in", values, "vehicleBrandCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeBetween(String value1, String value2) {
            addCriterion("vehicle_brand_code between", value1, value2, "vehicleBrandCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandCodeNotBetween(String value1, String value2) {
            addCriterion("vehicle_brand_code not between", value1, value2, "vehicleBrandCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandIsNull() {
            addCriterion("vehicle_brand is null");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandIsNotNull() {
            addCriterion("vehicle_brand is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandEqualTo(String value) {
            addCriterion("vehicle_brand =", value, "vehicleBrand");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandNotEqualTo(String value) {
            addCriterion("vehicle_brand <>", value, "vehicleBrand");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandGreaterThan(String value) {
            addCriterion("vehicle_brand >", value, "vehicleBrand");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_brand >=", value, "vehicleBrand");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandLessThan(String value) {
            addCriterion("vehicle_brand <", value, "vehicleBrand");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandLessThanOrEqualTo(String value) {
            addCriterion("vehicle_brand <=", value, "vehicleBrand");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandLike(String value) {
            addCriterion("vehicle_brand like", value, "vehicleBrand");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandNotLike(String value) {
            addCriterion("vehicle_brand not like", value, "vehicleBrand");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandIn(List<String> values) {
            addCriterion("vehicle_brand in", values, "vehicleBrand");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandNotIn(List<String> values) {
            addCriterion("vehicle_brand not in", values, "vehicleBrand");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandBetween(String value1, String value2) {
            addCriterion("vehicle_brand between", value1, value2, "vehicleBrand");
            return (Criteria) this;
        }

        public Criteria andVehicleBrandNotBetween(String value1, String value2) {
            addCriterion("vehicle_brand not between", value1, value2, "vehicleBrand");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIdIsNull() {
            addCriterion("vehicle_model_id is null");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIdIsNotNull() {
            addCriterion("vehicle_model_id is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIdEqualTo(Integer value) {
            addCriterion("vehicle_model_id =", value, "vehicleModelId");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIdNotEqualTo(Integer value) {
            addCriterion("vehicle_model_id <>", value, "vehicleModelId");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIdGreaterThan(Integer value) {
            addCriterion("vehicle_model_id >", value, "vehicleModelId");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("vehicle_model_id >=", value, "vehicleModelId");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIdLessThan(Integer value) {
            addCriterion("vehicle_model_id <", value, "vehicleModelId");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIdLessThanOrEqualTo(Integer value) {
            addCriterion("vehicle_model_id <=", value, "vehicleModelId");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIdIn(List<Integer> values) {
            addCriterion("vehicle_model_id in", values, "vehicleModelId");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIdNotIn(List<Integer> values) {
            addCriterion("vehicle_model_id not in", values, "vehicleModelId");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIdBetween(Integer value1, Integer value2) {
            addCriterion("vehicle_model_id between", value1, value2, "vehicleModelId");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIdNotBetween(Integer value1, Integer value2) {
            addCriterion("vehicle_model_id not between", value1, value2, "vehicleModelId");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeIsNull() {
            addCriterion("vehicle_model_code is null");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeIsNotNull() {
            addCriterion("vehicle_model_code is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeEqualTo(String value) {
            addCriterion("vehicle_model_code =", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeNotEqualTo(String value) {
            addCriterion("vehicle_model_code <>", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeGreaterThan(String value) {
            addCriterion("vehicle_model_code >", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_model_code >=", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeLessThan(String value) {
            addCriterion("vehicle_model_code <", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeLessThanOrEqualTo(String value) {
            addCriterion("vehicle_model_code <=", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeLike(String value) {
            addCriterion("vehicle_model_code like", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeNotLike(String value) {
            addCriterion("vehicle_model_code not like", value, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeIn(List<String> values) {
            addCriterion("vehicle_model_code in", values, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeNotIn(List<String> values) {
            addCriterion("vehicle_model_code not in", values, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeBetween(String value1, String value2) {
            addCriterion("vehicle_model_code between", value1, value2, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelCodeNotBetween(String value1, String value2) {
            addCriterion("vehicle_model_code not between", value1, value2, "vehicleModelCode");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIsNull() {
            addCriterion("vehicle_model is null");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIsNotNull() {
            addCriterion("vehicle_model is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleModelEqualTo(String value) {
            addCriterion("vehicle_model =", value, "vehicleModel");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNotEqualTo(String value) {
            addCriterion("vehicle_model <>", value, "vehicleModel");
            return (Criteria) this;
        }

        public Criteria andVehicleModelGreaterThan(String value) {
            addCriterion("vehicle_model >", value, "vehicleModel");
            return (Criteria) this;
        }

        public Criteria andVehicleModelGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_model >=", value, "vehicleModel");
            return (Criteria) this;
        }

        public Criteria andVehicleModelLessThan(String value) {
            addCriterion("vehicle_model <", value, "vehicleModel");
            return (Criteria) this;
        }

        public Criteria andVehicleModelLessThanOrEqualTo(String value) {
            addCriterion("vehicle_model <=", value, "vehicleModel");
            return (Criteria) this;
        }

        public Criteria andVehicleModelLike(String value) {
            addCriterion("vehicle_model like", value, "vehicleModel");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNotLike(String value) {
            addCriterion("vehicle_model not like", value, "vehicleModel");
            return (Criteria) this;
        }

        public Criteria andVehicleModelIn(List<String> values) {
            addCriterion("vehicle_model in", values, "vehicleModel");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNotIn(List<String> values) {
            addCriterion("vehicle_model not in", values, "vehicleModel");
            return (Criteria) this;
        }

        public Criteria andVehicleModelBetween(String value1, String value2) {
            addCriterion("vehicle_model between", value1, value2, "vehicleModel");
            return (Criteria) this;
        }

        public Criteria andVehicleModelNotBetween(String value1, String value2) {
            addCriterion("vehicle_model not between", value1, value2, "vehicleModel");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeIsNull() {
            addCriterion("vehicle_type is null");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeIsNotNull() {
            addCriterion("vehicle_type is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeEqualTo(Byte value) {
            addCriterion("vehicle_type =", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeNotEqualTo(Byte value) {
            addCriterion("vehicle_type <>", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeGreaterThan(Byte value) {
            addCriterion("vehicle_type >", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("vehicle_type >=", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeLessThan(Byte value) {
            addCriterion("vehicle_type <", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeLessThanOrEqualTo(Byte value) {
            addCriterion("vehicle_type <=", value, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeIn(List<Byte> values) {
            addCriterion("vehicle_type in", values, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeNotIn(List<Byte> values) {
            addCriterion("vehicle_type not in", values, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeBetween(Byte value1, Byte value2) {
            addCriterion("vehicle_type between", value1, value2, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("vehicle_type not between", value1, value2, "vehicleType");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongCityCodeIsNull() {
            addCriterion("vehicle_belong_city_code is null");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongCityCodeIsNotNull() {
            addCriterion("vehicle_belong_city_code is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongCityCodeEqualTo(String value) {
            addCriterion("vehicle_belong_city_code =", value, "vehicleBelongCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongCityCodeNotEqualTo(String value) {
            addCriterion("vehicle_belong_city_code <>", value, "vehicleBelongCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongCityCodeGreaterThan(String value) {
            addCriterion("vehicle_belong_city_code >", value, "vehicleBelongCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongCityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_belong_city_code >=", value, "vehicleBelongCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongCityCodeLessThan(String value) {
            addCriterion("vehicle_belong_city_code <", value, "vehicleBelongCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongCityCodeLessThanOrEqualTo(String value) {
            addCriterion("vehicle_belong_city_code <=", value, "vehicleBelongCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongCityCodeLike(String value) {
            addCriterion("vehicle_belong_city_code like", value, "vehicleBelongCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongCityCodeNotLike(String value) {
            addCriterion("vehicle_belong_city_code not like", value, "vehicleBelongCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongCityCodeIn(List<String> values) {
            addCriterion("vehicle_belong_city_code in", values, "vehicleBelongCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongCityCodeNotIn(List<String> values) {
            addCriterion("vehicle_belong_city_code not in", values, "vehicleBelongCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongCityCodeBetween(String value1, String value2) {
            addCriterion("vehicle_belong_city_code between", value1, value2, "vehicleBelongCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongCityCodeNotBetween(String value1, String value2) {
            addCriterion("vehicle_belong_city_code not between", value1, value2, "vehicleBelongCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongCityNameIsNull() {
            addCriterion("vehicle_belong_city_name is null");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongCityNameIsNotNull() {
            addCriterion("vehicle_belong_city_name is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongCityNameEqualTo(String value) {
            addCriterion("vehicle_belong_city_name =", value, "vehicleBelongCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongCityNameNotEqualTo(String value) {
            addCriterion("vehicle_belong_city_name <>", value, "vehicleBelongCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongCityNameGreaterThan(String value) {
            addCriterion("vehicle_belong_city_name >", value, "vehicleBelongCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongCityNameGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_belong_city_name >=", value, "vehicleBelongCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongCityNameLessThan(String value) {
            addCriterion("vehicle_belong_city_name <", value, "vehicleBelongCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongCityNameLessThanOrEqualTo(String value) {
            addCriterion("vehicle_belong_city_name <=", value, "vehicleBelongCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongCityNameLike(String value) {
            addCriterion("vehicle_belong_city_name like", value, "vehicleBelongCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongCityNameNotLike(String value) {
            addCriterion("vehicle_belong_city_name not like", value, "vehicleBelongCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongCityNameIn(List<String> values) {
            addCriterion("vehicle_belong_city_name in", values, "vehicleBelongCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongCityNameNotIn(List<String> values) {
            addCriterion("vehicle_belong_city_name not in", values, "vehicleBelongCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongCityNameBetween(String value1, String value2) {
            addCriterion("vehicle_belong_city_name between", value1, value2, "vehicleBelongCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongCityNameNotBetween(String value1, String value2) {
            addCriterion("vehicle_belong_city_name not between", value1, value2, "vehicleBelongCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyIdIsNull() {
            addCriterion("vehicle_company_id is null");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyIdIsNotNull() {
            addCriterion("vehicle_company_id is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyIdEqualTo(Integer value) {
            addCriterion("vehicle_company_id =", value, "vehicleCompanyId");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyIdNotEqualTo(Integer value) {
            addCriterion("vehicle_company_id <>", value, "vehicleCompanyId");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyIdGreaterThan(Integer value) {
            addCriterion("vehicle_company_id >", value, "vehicleCompanyId");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("vehicle_company_id >=", value, "vehicleCompanyId");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyIdLessThan(Integer value) {
            addCriterion("vehicle_company_id <", value, "vehicleCompanyId");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyIdLessThanOrEqualTo(Integer value) {
            addCriterion("vehicle_company_id <=", value, "vehicleCompanyId");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyIdIn(List<Integer> values) {
            addCriterion("vehicle_company_id in", values, "vehicleCompanyId");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyIdNotIn(List<Integer> values) {
            addCriterion("vehicle_company_id not in", values, "vehicleCompanyId");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyIdBetween(Integer value1, Integer value2) {
            addCriterion("vehicle_company_id between", value1, value2, "vehicleCompanyId");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyIdNotBetween(Integer value1, Integer value2) {
            addCriterion("vehicle_company_id not between", value1, value2, "vehicleCompanyId");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyNameIsNull() {
            addCriterion("vehicle_company_name is null");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyNameIsNotNull() {
            addCriterion("vehicle_company_name is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyNameEqualTo(String value) {
            addCriterion("vehicle_company_name =", value, "vehicleCompanyName");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyNameNotEqualTo(String value) {
            addCriterion("vehicle_company_name <>", value, "vehicleCompanyName");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyNameGreaterThan(String value) {
            addCriterion("vehicle_company_name >", value, "vehicleCompanyName");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyNameGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_company_name >=", value, "vehicleCompanyName");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyNameLessThan(String value) {
            addCriterion("vehicle_company_name <", value, "vehicleCompanyName");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyNameLessThanOrEqualTo(String value) {
            addCriterion("vehicle_company_name <=", value, "vehicleCompanyName");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyNameLike(String value) {
            addCriterion("vehicle_company_name like", value, "vehicleCompanyName");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyNameNotLike(String value) {
            addCriterion("vehicle_company_name not like", value, "vehicleCompanyName");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyNameIn(List<String> values) {
            addCriterion("vehicle_company_name in", values, "vehicleCompanyName");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyNameNotIn(List<String> values) {
            addCriterion("vehicle_company_name not in", values, "vehicleCompanyName");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyNameBetween(String value1, String value2) {
            addCriterion("vehicle_company_name between", value1, value2, "vehicleCompanyName");
            return (Criteria) this;
        }

        public Criteria andVehicleCompanyNameNotBetween(String value1, String value2) {
            addCriterion("vehicle_company_name not between", value1, value2, "vehicleCompanyName");
            return (Criteria) this;
        }

        public Criteria andVehicleStructIdIsNull() {
            addCriterion("vehicle_struct_id is null");
            return (Criteria) this;
        }

        public Criteria andVehicleStructIdIsNotNull() {
            addCriterion("vehicle_struct_id is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleStructIdEqualTo(Integer value) {
            addCriterion("vehicle_struct_id =", value, "vehicleStructId");
            return (Criteria) this;
        }

        public Criteria andVehicleStructIdNotEqualTo(Integer value) {
            addCriterion("vehicle_struct_id <>", value, "vehicleStructId");
            return (Criteria) this;
        }

        public Criteria andVehicleStructIdGreaterThan(Integer value) {
            addCriterion("vehicle_struct_id >", value, "vehicleStructId");
            return (Criteria) this;
        }

        public Criteria andVehicleStructIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("vehicle_struct_id >=", value, "vehicleStructId");
            return (Criteria) this;
        }

        public Criteria andVehicleStructIdLessThan(Integer value) {
            addCriterion("vehicle_struct_id <", value, "vehicleStructId");
            return (Criteria) this;
        }

        public Criteria andVehicleStructIdLessThanOrEqualTo(Integer value) {
            addCriterion("vehicle_struct_id <=", value, "vehicleStructId");
            return (Criteria) this;
        }

        public Criteria andVehicleStructIdIn(List<Integer> values) {
            addCriterion("vehicle_struct_id in", values, "vehicleStructId");
            return (Criteria) this;
        }

        public Criteria andVehicleStructIdNotIn(List<Integer> values) {
            addCriterion("vehicle_struct_id not in", values, "vehicleStructId");
            return (Criteria) this;
        }

        public Criteria andVehicleStructIdBetween(Integer value1, Integer value2) {
            addCriterion("vehicle_struct_id between", value1, value2, "vehicleStructId");
            return (Criteria) this;
        }

        public Criteria andVehicleStructIdNotBetween(Integer value1, Integer value2) {
            addCriterion("vehicle_struct_id not between", value1, value2, "vehicleStructId");
            return (Criteria) this;
        }

        public Criteria andVehicleStructNameIsNull() {
            addCriterion("vehicle_struct_name is null");
            return (Criteria) this;
        }

        public Criteria andVehicleStructNameIsNotNull() {
            addCriterion("vehicle_struct_name is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleStructNameEqualTo(String value) {
            addCriterion("vehicle_struct_name =", value, "vehicleStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleStructNameNotEqualTo(String value) {
            addCriterion("vehicle_struct_name <>", value, "vehicleStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleStructNameGreaterThan(String value) {
            addCriterion("vehicle_struct_name >", value, "vehicleStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleStructNameGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_struct_name >=", value, "vehicleStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleStructNameLessThan(String value) {
            addCriterion("vehicle_struct_name <", value, "vehicleStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleStructNameLessThanOrEqualTo(String value) {
            addCriterion("vehicle_struct_name <=", value, "vehicleStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleStructNameLike(String value) {
            addCriterion("vehicle_struct_name like", value, "vehicleStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleStructNameNotLike(String value) {
            addCriterion("vehicle_struct_name not like", value, "vehicleStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleStructNameIn(List<String> values) {
            addCriterion("vehicle_struct_name in", values, "vehicleStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleStructNameNotIn(List<String> values) {
            addCriterion("vehicle_struct_name not in", values, "vehicleStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleStructNameBetween(String value1, String value2) {
            addCriterion("vehicle_struct_name between", value1, value2, "vehicleStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleStructNameNotBetween(String value1, String value2) {
            addCriterion("vehicle_struct_name not between", value1, value2, "vehicleStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleSelfOwnedIsNull() {
            addCriterion("vehicle_self_owned is null");
            return (Criteria) this;
        }

        public Criteria andVehicleSelfOwnedIsNotNull() {
            addCriterion("vehicle_self_owned is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleSelfOwnedEqualTo(Byte value) {
            addCriterion("vehicle_self_owned =", value, "vehicleSelfOwned");
            return (Criteria) this;
        }

        public Criteria andVehicleSelfOwnedNotEqualTo(Byte value) {
            addCriterion("vehicle_self_owned <>", value, "vehicleSelfOwned");
            return (Criteria) this;
        }

        public Criteria andVehicleSelfOwnedGreaterThan(Byte value) {
            addCriterion("vehicle_self_owned >", value, "vehicleSelfOwned");
            return (Criteria) this;
        }

        public Criteria andVehicleSelfOwnedGreaterThanOrEqualTo(Byte value) {
            addCriterion("vehicle_self_owned >=", value, "vehicleSelfOwned");
            return (Criteria) this;
        }

        public Criteria andVehicleSelfOwnedLessThan(Byte value) {
            addCriterion("vehicle_self_owned <", value, "vehicleSelfOwned");
            return (Criteria) this;
        }

        public Criteria andVehicleSelfOwnedLessThanOrEqualTo(Byte value) {
            addCriterion("vehicle_self_owned <=", value, "vehicleSelfOwned");
            return (Criteria) this;
        }

        public Criteria andVehicleSelfOwnedIn(List<Byte> values) {
            addCriterion("vehicle_self_owned in", values, "vehicleSelfOwned");
            return (Criteria) this;
        }

        public Criteria andVehicleSelfOwnedNotIn(List<Byte> values) {
            addCriterion("vehicle_self_owned not in", values, "vehicleSelfOwned");
            return (Criteria) this;
        }

        public Criteria andVehicleSelfOwnedBetween(Byte value1, Byte value2) {
            addCriterion("vehicle_self_owned between", value1, value2, "vehicleSelfOwned");
            return (Criteria) this;
        }

        public Criteria andVehicleSelfOwnedNotBetween(Byte value1, Byte value2) {
            addCriterion("vehicle_self_owned not between", value1, value2, "vehicleSelfOwned");
            return (Criteria) this;
        }

        public Criteria andVehicleWorkingStatusIsNull() {
            addCriterion("vehicle_working_status is null");
            return (Criteria) this;
        }

        public Criteria andVehicleWorkingStatusIsNotNull() {
            addCriterion("vehicle_working_status is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleWorkingStatusEqualTo(Byte value) {
            addCriterion("vehicle_working_status =", value, "vehicleWorkingStatus");
            return (Criteria) this;
        }

        public Criteria andVehicleWorkingStatusNotEqualTo(Byte value) {
            addCriterion("vehicle_working_status <>", value, "vehicleWorkingStatus");
            return (Criteria) this;
        }

        public Criteria andVehicleWorkingStatusGreaterThan(Byte value) {
            addCriterion("vehicle_working_status >", value, "vehicleWorkingStatus");
            return (Criteria) this;
        }

        public Criteria andVehicleWorkingStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("vehicle_working_status >=", value, "vehicleWorkingStatus");
            return (Criteria) this;
        }

        public Criteria andVehicleWorkingStatusLessThan(Byte value) {
            addCriterion("vehicle_working_status <", value, "vehicleWorkingStatus");
            return (Criteria) this;
        }

        public Criteria andVehicleWorkingStatusLessThanOrEqualTo(Byte value) {
            addCriterion("vehicle_working_status <=", value, "vehicleWorkingStatus");
            return (Criteria) this;
        }

        public Criteria andVehicleWorkingStatusIn(List<Byte> values) {
            addCriterion("vehicle_working_status in", values, "vehicleWorkingStatus");
            return (Criteria) this;
        }

        public Criteria andVehicleWorkingStatusNotIn(List<Byte> values) {
            addCriterion("vehicle_working_status not in", values, "vehicleWorkingStatus");
            return (Criteria) this;
        }

        public Criteria andVehicleWorkingStatusBetween(Byte value1, Byte value2) {
            addCriterion("vehicle_working_status between", value1, value2, "vehicleWorkingStatus");
            return (Criteria) this;
        }

        public Criteria andVehicleWorkingStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("vehicle_working_status not between", value1, value2, "vehicleWorkingStatus");
            return (Criteria) this;
        }

        public Criteria andVehicleOwnStructIdIsNull() {
            addCriterion("vehicle_own_struct_id is null");
            return (Criteria) this;
        }

        public Criteria andVehicleOwnStructIdIsNotNull() {
            addCriterion("vehicle_own_struct_id is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleOwnStructIdEqualTo(Integer value) {
            addCriterion("vehicle_own_struct_id =", value, "vehicleOwnStructId");
            return (Criteria) this;
        }

        public Criteria andVehicleOwnStructIdNotEqualTo(Integer value) {
            addCriterion("vehicle_own_struct_id <>", value, "vehicleOwnStructId");
            return (Criteria) this;
        }

        public Criteria andVehicleOwnStructIdGreaterThan(Integer value) {
            addCriterion("vehicle_own_struct_id >", value, "vehicleOwnStructId");
            return (Criteria) this;
        }

        public Criteria andVehicleOwnStructIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("vehicle_own_struct_id >=", value, "vehicleOwnStructId");
            return (Criteria) this;
        }

        public Criteria andVehicleOwnStructIdLessThan(Integer value) {
            addCriterion("vehicle_own_struct_id <", value, "vehicleOwnStructId");
            return (Criteria) this;
        }

        public Criteria andVehicleOwnStructIdLessThanOrEqualTo(Integer value) {
            addCriterion("vehicle_own_struct_id <=", value, "vehicleOwnStructId");
            return (Criteria) this;
        }

        public Criteria andVehicleOwnStructIdIn(List<Integer> values) {
            addCriterion("vehicle_own_struct_id in", values, "vehicleOwnStructId");
            return (Criteria) this;
        }

        public Criteria andVehicleOwnStructIdNotIn(List<Integer> values) {
            addCriterion("vehicle_own_struct_id not in", values, "vehicleOwnStructId");
            return (Criteria) this;
        }

        public Criteria andVehicleOwnStructIdBetween(Integer value1, Integer value2) {
            addCriterion("vehicle_own_struct_id between", value1, value2, "vehicleOwnStructId");
            return (Criteria) this;
        }

        public Criteria andVehicleOwnStructIdNotBetween(Integer value1, Integer value2) {
            addCriterion("vehicle_own_struct_id not between", value1, value2, "vehicleOwnStructId");
            return (Criteria) this;
        }

        public Criteria andVehicleOwnStructNameIsNull() {
            addCriterion("vehicle_own_struct_name is null");
            return (Criteria) this;
        }

        public Criteria andVehicleOwnStructNameIsNotNull() {
            addCriterion("vehicle_own_struct_name is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleOwnStructNameEqualTo(String value) {
            addCriterion("vehicle_own_struct_name =", value, "vehicleOwnStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleOwnStructNameNotEqualTo(String value) {
            addCriterion("vehicle_own_struct_name <>", value, "vehicleOwnStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleOwnStructNameGreaterThan(String value) {
            addCriterion("vehicle_own_struct_name >", value, "vehicleOwnStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleOwnStructNameGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_own_struct_name >=", value, "vehicleOwnStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleOwnStructNameLessThan(String value) {
            addCriterion("vehicle_own_struct_name <", value, "vehicleOwnStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleOwnStructNameLessThanOrEqualTo(String value) {
            addCriterion("vehicle_own_struct_name <=", value, "vehicleOwnStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleOwnStructNameLike(String value) {
            addCriterion("vehicle_own_struct_name like", value, "vehicleOwnStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleOwnStructNameNotLike(String value) {
            addCriterion("vehicle_own_struct_name not like", value, "vehicleOwnStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleOwnStructNameIn(List<String> values) {
            addCriterion("vehicle_own_struct_name in", values, "vehicleOwnStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleOwnStructNameNotIn(List<String> values) {
            addCriterion("vehicle_own_struct_name not in", values, "vehicleOwnStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleOwnStructNameBetween(String value1, String value2) {
            addCriterion("vehicle_own_struct_name between", value1, value2, "vehicleOwnStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleOwnStructNameNotBetween(String value1, String value2) {
            addCriterion("vehicle_own_struct_name not between", value1, value2, "vehicleOwnStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongStructNameIsNull() {
            addCriterion("vehicle_belong_struct_name is null");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongStructNameIsNotNull() {
            addCriterion("vehicle_belong_struct_name is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongStructNameEqualTo(String value) {
            addCriterion("vehicle_belong_struct_name =", value, "vehicleBelongStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongStructNameNotEqualTo(String value) {
            addCriterion("vehicle_belong_struct_name <>", value, "vehicleBelongStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongStructNameGreaterThan(String value) {
            addCriterion("vehicle_belong_struct_name >", value, "vehicleBelongStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongStructNameGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_belong_struct_name >=", value, "vehicleBelongStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongStructNameLessThan(String value) {
            addCriterion("vehicle_belong_struct_name <", value, "vehicleBelongStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongStructNameLessThanOrEqualTo(String value) {
            addCriterion("vehicle_belong_struct_name <=", value, "vehicleBelongStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongStructNameLike(String value) {
            addCriterion("vehicle_belong_struct_name like", value, "vehicleBelongStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongStructNameNotLike(String value) {
            addCriterion("vehicle_belong_struct_name not like", value, "vehicleBelongStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongStructNameIn(List<String> values) {
            addCriterion("vehicle_belong_struct_name in", values, "vehicleBelongStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongStructNameNotIn(List<String> values) {
            addCriterion("vehicle_belong_struct_name not in", values, "vehicleBelongStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongStructNameBetween(String value1, String value2) {
            addCriterion("vehicle_belong_struct_name between", value1, value2, "vehicleBelongStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongStructNameNotBetween(String value1, String value2) {
            addCriterion("vehicle_belong_struct_name not between", value1, value2, "vehicleBelongStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongStructCodeIsNull() {
            addCriterion("vehicle_belong_struct_code is null");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongStructCodeIsNotNull() {
            addCriterion("vehicle_belong_struct_code is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongStructCodeEqualTo(String value) {
            addCriterion("vehicle_belong_struct_code =", value, "vehicleBelongStructCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongStructCodeNotEqualTo(String value) {
            addCriterion("vehicle_belong_struct_code <>", value, "vehicleBelongStructCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongStructCodeGreaterThan(String value) {
            addCriterion("vehicle_belong_struct_code >", value, "vehicleBelongStructCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongStructCodeGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_belong_struct_code >=", value, "vehicleBelongStructCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongStructCodeLessThan(String value) {
            addCriterion("vehicle_belong_struct_code <", value, "vehicleBelongStructCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongStructCodeLessThanOrEqualTo(String value) {
            addCriterion("vehicle_belong_struct_code <=", value, "vehicleBelongStructCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongStructCodeLike(String value) {
            addCriterion("vehicle_belong_struct_code like", value, "vehicleBelongStructCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongStructCodeNotLike(String value) {
            addCriterion("vehicle_belong_struct_code not like", value, "vehicleBelongStructCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongStructCodeIn(List<String> values) {
            addCriterion("vehicle_belong_struct_code in", values, "vehicleBelongStructCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongStructCodeNotIn(List<String> values) {
            addCriterion("vehicle_belong_struct_code not in", values, "vehicleBelongStructCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongStructCodeBetween(String value1, String value2) {
            addCriterion("vehicle_belong_struct_code between", value1, value2, "vehicleBelongStructCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongStructCodeNotBetween(String value1, String value2) {
            addCriterion("vehicle_belong_struct_code not between", value1, value2, "vehicleBelongStructCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussCodeIsNull() {
            addCriterion("vehicle_belong_buss_code is null");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussCodeIsNotNull() {
            addCriterion("vehicle_belong_buss_code is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussCodeEqualTo(String value) {
            addCriterion("vehicle_belong_buss_code =", value, "vehicleBelongBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussCodeNotEqualTo(String value) {
            addCriterion("vehicle_belong_buss_code <>", value, "vehicleBelongBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussCodeGreaterThan(String value) {
            addCriterion("vehicle_belong_buss_code >", value, "vehicleBelongBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussCodeGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_belong_buss_code >=", value, "vehicleBelongBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussCodeLessThan(String value) {
            addCriterion("vehicle_belong_buss_code <", value, "vehicleBelongBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussCodeLessThanOrEqualTo(String value) {
            addCriterion("vehicle_belong_buss_code <=", value, "vehicleBelongBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussCodeLike(String value) {
            addCriterion("vehicle_belong_buss_code like", value, "vehicleBelongBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussCodeNotLike(String value) {
            addCriterion("vehicle_belong_buss_code not like", value, "vehicleBelongBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussCodeIn(List<String> values) {
            addCriterion("vehicle_belong_buss_code in", values, "vehicleBelongBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussCodeNotIn(List<String> values) {
            addCriterion("vehicle_belong_buss_code not in", values, "vehicleBelongBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussCodeBetween(String value1, String value2) {
            addCriterion("vehicle_belong_buss_code between", value1, value2, "vehicleBelongBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussCodeNotBetween(String value1, String value2) {
            addCriterion("vehicle_belong_buss_code not between", value1, value2, "vehicleBelongBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussNameIsNull() {
            addCriterion("vehicle_belong_buss_name is null");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussNameIsNotNull() {
            addCriterion("vehicle_belong_buss_name is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussNameEqualTo(String value) {
            addCriterion("vehicle_belong_buss_name =", value, "vehicleBelongBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussNameNotEqualTo(String value) {
            addCriterion("vehicle_belong_buss_name <>", value, "vehicleBelongBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussNameGreaterThan(String value) {
            addCriterion("vehicle_belong_buss_name >", value, "vehicleBelongBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussNameGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_belong_buss_name >=", value, "vehicleBelongBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussNameLessThan(String value) {
            addCriterion("vehicle_belong_buss_name <", value, "vehicleBelongBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussNameLessThanOrEqualTo(String value) {
            addCriterion("vehicle_belong_buss_name <=", value, "vehicleBelongBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussNameLike(String value) {
            addCriterion("vehicle_belong_buss_name like", value, "vehicleBelongBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussNameNotLike(String value) {
            addCriterion("vehicle_belong_buss_name not like", value, "vehicleBelongBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussNameIn(List<String> values) {
            addCriterion("vehicle_belong_buss_name in", values, "vehicleBelongBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussNameNotIn(List<String> values) {
            addCriterion("vehicle_belong_buss_name not in", values, "vehicleBelongBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussNameBetween(String value1, String value2) {
            addCriterion("vehicle_belong_buss_name between", value1, value2, "vehicleBelongBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleBelongBussNameNotBetween(String value1, String value2) {
            addCriterion("vehicle_belong_buss_name not between", value1, value2, "vehicleBelongBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateStructNameIsNull() {
            addCriterion("vehicle_operate_struct_name is null");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateStructNameIsNotNull() {
            addCriterion("vehicle_operate_struct_name is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateStructNameEqualTo(String value) {
            addCriterion("vehicle_operate_struct_name =", value, "vehicleOperateStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateStructNameNotEqualTo(String value) {
            addCriterion("vehicle_operate_struct_name <>", value, "vehicleOperateStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateStructNameGreaterThan(String value) {
            addCriterion("vehicle_operate_struct_name >", value, "vehicleOperateStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateStructNameGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_operate_struct_name >=", value, "vehicleOperateStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateStructNameLessThan(String value) {
            addCriterion("vehicle_operate_struct_name <", value, "vehicleOperateStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateStructNameLessThanOrEqualTo(String value) {
            addCriterion("vehicle_operate_struct_name <=", value, "vehicleOperateStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateStructNameLike(String value) {
            addCriterion("vehicle_operate_struct_name like", value, "vehicleOperateStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateStructNameNotLike(String value) {
            addCriterion("vehicle_operate_struct_name not like", value, "vehicleOperateStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateStructNameIn(List<String> values) {
            addCriterion("vehicle_operate_struct_name in", values, "vehicleOperateStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateStructNameNotIn(List<String> values) {
            addCriterion("vehicle_operate_struct_name not in", values, "vehicleOperateStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateStructNameBetween(String value1, String value2) {
            addCriterion("vehicle_operate_struct_name between", value1, value2, "vehicleOperateStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateStructNameNotBetween(String value1, String value2) {
            addCriterion("vehicle_operate_struct_name not between", value1, value2, "vehicleOperateStructName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateStructCodeIsNull() {
            addCriterion("vehicle_operate_struct_code is null");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateStructCodeIsNotNull() {
            addCriterion("vehicle_operate_struct_code is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateStructCodeEqualTo(String value) {
            addCriterion("vehicle_operate_struct_code =", value, "vehicleOperateStructCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateStructCodeNotEqualTo(String value) {
            addCriterion("vehicle_operate_struct_code <>", value, "vehicleOperateStructCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateStructCodeGreaterThan(String value) {
            addCriterion("vehicle_operate_struct_code >", value, "vehicleOperateStructCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateStructCodeGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_operate_struct_code >=", value, "vehicleOperateStructCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateStructCodeLessThan(String value) {
            addCriterion("vehicle_operate_struct_code <", value, "vehicleOperateStructCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateStructCodeLessThanOrEqualTo(String value) {
            addCriterion("vehicle_operate_struct_code <=", value, "vehicleOperateStructCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateStructCodeLike(String value) {
            addCriterion("vehicle_operate_struct_code like", value, "vehicleOperateStructCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateStructCodeNotLike(String value) {
            addCriterion("vehicle_operate_struct_code not like", value, "vehicleOperateStructCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateStructCodeIn(List<String> values) {
            addCriterion("vehicle_operate_struct_code in", values, "vehicleOperateStructCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateStructCodeNotIn(List<String> values) {
            addCriterion("vehicle_operate_struct_code not in", values, "vehicleOperateStructCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateStructCodeBetween(String value1, String value2) {
            addCriterion("vehicle_operate_struct_code between", value1, value2, "vehicleOperateStructCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateStructCodeNotBetween(String value1, String value2) {
            addCriterion("vehicle_operate_struct_code not between", value1, value2, "vehicleOperateStructCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussNameIsNull() {
            addCriterion("vehicle_operate_buss_name is null");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussNameIsNotNull() {
            addCriterion("vehicle_operate_buss_name is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussNameEqualTo(String value) {
            addCriterion("vehicle_operate_buss_name =", value, "vehicleOperateBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussNameNotEqualTo(String value) {
            addCriterion("vehicle_operate_buss_name <>", value, "vehicleOperateBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussNameGreaterThan(String value) {
            addCriterion("vehicle_operate_buss_name >", value, "vehicleOperateBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussNameGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_operate_buss_name >=", value, "vehicleOperateBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussNameLessThan(String value) {
            addCriterion("vehicle_operate_buss_name <", value, "vehicleOperateBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussNameLessThanOrEqualTo(String value) {
            addCriterion("vehicle_operate_buss_name <=", value, "vehicleOperateBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussNameLike(String value) {
            addCriterion("vehicle_operate_buss_name like", value, "vehicleOperateBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussNameNotLike(String value) {
            addCriterion("vehicle_operate_buss_name not like", value, "vehicleOperateBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussNameIn(List<String> values) {
            addCriterion("vehicle_operate_buss_name in", values, "vehicleOperateBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussNameNotIn(List<String> values) {
            addCriterion("vehicle_operate_buss_name not in", values, "vehicleOperateBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussNameBetween(String value1, String value2) {
            addCriterion("vehicle_operate_buss_name between", value1, value2, "vehicleOperateBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussNameNotBetween(String value1, String value2) {
            addCriterion("vehicle_operate_buss_name not between", value1, value2, "vehicleOperateBussName");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussCodeIsNull() {
            addCriterion("vehicle_operate_buss_code is null");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussCodeIsNotNull() {
            addCriterion("vehicle_operate_buss_code is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussCodeEqualTo(String value) {
            addCriterion("vehicle_operate_buss_code =", value, "vehicleOperateBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussCodeNotEqualTo(String value) {
            addCriterion("vehicle_operate_buss_code <>", value, "vehicleOperateBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussCodeGreaterThan(String value) {
            addCriterion("vehicle_operate_buss_code >", value, "vehicleOperateBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussCodeGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_operate_buss_code >=", value, "vehicleOperateBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussCodeLessThan(String value) {
            addCriterion("vehicle_operate_buss_code <", value, "vehicleOperateBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussCodeLessThanOrEqualTo(String value) {
            addCriterion("vehicle_operate_buss_code <=", value, "vehicleOperateBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussCodeLike(String value) {
            addCriterion("vehicle_operate_buss_code like", value, "vehicleOperateBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussCodeNotLike(String value) {
            addCriterion("vehicle_operate_buss_code not like", value, "vehicleOperateBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussCodeIn(List<String> values) {
            addCriterion("vehicle_operate_buss_code in", values, "vehicleOperateBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussCodeNotIn(List<String> values) {
            addCriterion("vehicle_operate_buss_code not in", values, "vehicleOperateBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussCodeBetween(String value1, String value2) {
            addCriterion("vehicle_operate_buss_code between", value1, value2, "vehicleOperateBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleOperateBussCodeNotBetween(String value1, String value2) {
            addCriterion("vehicle_operate_buss_code not between", value1, value2, "vehicleOperateBussCode");
            return (Criteria) this;
        }

        public Criteria andVehicleCreatorIdIsNull() {
            addCriterion("vehicle_creator_id is null");
            return (Criteria) this;
        }

        public Criteria andVehicleCreatorIdIsNotNull() {
            addCriterion("vehicle_creator_id is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleCreatorIdEqualTo(Integer value) {
            addCriterion("vehicle_creator_id =", value, "vehicleCreatorId");
            return (Criteria) this;
        }

        public Criteria andVehicleCreatorIdNotEqualTo(Integer value) {
            addCriterion("vehicle_creator_id <>", value, "vehicleCreatorId");
            return (Criteria) this;
        }

        public Criteria andVehicleCreatorIdGreaterThan(Integer value) {
            addCriterion("vehicle_creator_id >", value, "vehicleCreatorId");
            return (Criteria) this;
        }

        public Criteria andVehicleCreatorIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("vehicle_creator_id >=", value, "vehicleCreatorId");
            return (Criteria) this;
        }

        public Criteria andVehicleCreatorIdLessThan(Integer value) {
            addCriterion("vehicle_creator_id <", value, "vehicleCreatorId");
            return (Criteria) this;
        }

        public Criteria andVehicleCreatorIdLessThanOrEqualTo(Integer value) {
            addCriterion("vehicle_creator_id <=", value, "vehicleCreatorId");
            return (Criteria) this;
        }

        public Criteria andVehicleCreatorIdIn(List<Integer> values) {
            addCriterion("vehicle_creator_id in", values, "vehicleCreatorId");
            return (Criteria) this;
        }

        public Criteria andVehicleCreatorIdNotIn(List<Integer> values) {
            addCriterion("vehicle_creator_id not in", values, "vehicleCreatorId");
            return (Criteria) this;
        }

        public Criteria andVehicleCreatorIdBetween(Integer value1, Integer value2) {
            addCriterion("vehicle_creator_id between", value1, value2, "vehicleCreatorId");
            return (Criteria) this;
        }

        public Criteria andVehicleCreatorIdNotBetween(Integer value1, Integer value2) {
            addCriterion("vehicle_creator_id not between", value1, value2, "vehicleCreatorId");
            return (Criteria) this;
        }

        public Criteria andVehicleCreatorNameIsNull() {
            addCriterion("vehicle_creator_name is null");
            return (Criteria) this;
        }

        public Criteria andVehicleCreatorNameIsNotNull() {
            addCriterion("vehicle_creator_name is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleCreatorNameEqualTo(String value) {
            addCriterion("vehicle_creator_name =", value, "vehicleCreatorName");
            return (Criteria) this;
        }

        public Criteria andVehicleCreatorNameNotEqualTo(String value) {
            addCriterion("vehicle_creator_name <>", value, "vehicleCreatorName");
            return (Criteria) this;
        }

        public Criteria andVehicleCreatorNameGreaterThan(String value) {
            addCriterion("vehicle_creator_name >", value, "vehicleCreatorName");
            return (Criteria) this;
        }

        public Criteria andVehicleCreatorNameGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_creator_name >=", value, "vehicleCreatorName");
            return (Criteria) this;
        }

        public Criteria andVehicleCreatorNameLessThan(String value) {
            addCriterion("vehicle_creator_name <", value, "vehicleCreatorName");
            return (Criteria) this;
        }

        public Criteria andVehicleCreatorNameLessThanOrEqualTo(String value) {
            addCriterion("vehicle_creator_name <=", value, "vehicleCreatorName");
            return (Criteria) this;
        }

        public Criteria andVehicleCreatorNameLike(String value) {
            addCriterion("vehicle_creator_name like", value, "vehicleCreatorName");
            return (Criteria) this;
        }

        public Criteria andVehicleCreatorNameNotLike(String value) {
            addCriterion("vehicle_creator_name not like", value, "vehicleCreatorName");
            return (Criteria) this;
        }

        public Criteria andVehicleCreatorNameIn(List<String> values) {
            addCriterion("vehicle_creator_name in", values, "vehicleCreatorName");
            return (Criteria) this;
        }

        public Criteria andVehicleCreatorNameNotIn(List<String> values) {
            addCriterion("vehicle_creator_name not in", values, "vehicleCreatorName");
            return (Criteria) this;
        }

        public Criteria andVehicleCreatorNameBetween(String value1, String value2) {
            addCriterion("vehicle_creator_name between", value1, value2, "vehicleCreatorName");
            return (Criteria) this;
        }

        public Criteria andVehicleCreatorNameNotBetween(String value1, String value2) {
            addCriterion("vehicle_creator_name not between", value1, value2, "vehicleCreatorName");
            return (Criteria) this;
        }

        public Criteria andVehicleAssetCityCodeIsNull() {
            addCriterion("vehicle_asset_city_code is null");
            return (Criteria) this;
        }

        public Criteria andVehicleAssetCityCodeIsNotNull() {
            addCriterion("vehicle_asset_city_code is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleAssetCityCodeEqualTo(String value) {
            addCriterion("vehicle_asset_city_code =", value, "vehicleAssetCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleAssetCityCodeNotEqualTo(String value) {
            addCriterion("vehicle_asset_city_code <>", value, "vehicleAssetCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleAssetCityCodeGreaterThan(String value) {
            addCriterion("vehicle_asset_city_code >", value, "vehicleAssetCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleAssetCityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_asset_city_code >=", value, "vehicleAssetCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleAssetCityCodeLessThan(String value) {
            addCriterion("vehicle_asset_city_code <", value, "vehicleAssetCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleAssetCityCodeLessThanOrEqualTo(String value) {
            addCriterion("vehicle_asset_city_code <=", value, "vehicleAssetCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleAssetCityCodeLike(String value) {
            addCriterion("vehicle_asset_city_code like", value, "vehicleAssetCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleAssetCityCodeNotLike(String value) {
            addCriterion("vehicle_asset_city_code not like", value, "vehicleAssetCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleAssetCityCodeIn(List<String> values) {
            addCriterion("vehicle_asset_city_code in", values, "vehicleAssetCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleAssetCityCodeNotIn(List<String> values) {
            addCriterion("vehicle_asset_city_code not in", values, "vehicleAssetCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleAssetCityCodeBetween(String value1, String value2) {
            addCriterion("vehicle_asset_city_code between", value1, value2, "vehicleAssetCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleAssetCityCodeNotBetween(String value1, String value2) {
            addCriterion("vehicle_asset_city_code not between", value1, value2, "vehicleAssetCityCode");
            return (Criteria) this;
        }

        public Criteria andVehicleAssetCityNameIsNull() {
            addCriterion("vehicle_asset_city_name is null");
            return (Criteria) this;
        }

        public Criteria andVehicleAssetCityNameIsNotNull() {
            addCriterion("vehicle_asset_city_name is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleAssetCityNameEqualTo(String value) {
            addCriterion("vehicle_asset_city_name =", value, "vehicleAssetCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleAssetCityNameNotEqualTo(String value) {
            addCriterion("vehicle_asset_city_name <>", value, "vehicleAssetCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleAssetCityNameGreaterThan(String value) {
            addCriterion("vehicle_asset_city_name >", value, "vehicleAssetCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleAssetCityNameGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_asset_city_name >=", value, "vehicleAssetCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleAssetCityNameLessThan(String value) {
            addCriterion("vehicle_asset_city_name <", value, "vehicleAssetCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleAssetCityNameLessThanOrEqualTo(String value) {
            addCriterion("vehicle_asset_city_name <=", value, "vehicleAssetCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleAssetCityNameLike(String value) {
            addCriterion("vehicle_asset_city_name like", value, "vehicleAssetCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleAssetCityNameNotLike(String value) {
            addCriterion("vehicle_asset_city_name not like", value, "vehicleAssetCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleAssetCityNameIn(List<String> values) {
            addCriterion("vehicle_asset_city_name in", values, "vehicleAssetCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleAssetCityNameNotIn(List<String> values) {
            addCriterion("vehicle_asset_city_name not in", values, "vehicleAssetCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleAssetCityNameBetween(String value1, String value2) {
            addCriterion("vehicle_asset_city_name between", value1, value2, "vehicleAssetCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleAssetCityNameNotBetween(String value1, String value2) {
            addCriterion("vehicle_asset_city_name not between", value1, value2, "vehicleAssetCityName");
            return (Criteria) this;
        }

        public Criteria andVehicleSimNoIsNull() {
            addCriterion("vehicle_sim_no is null");
            return (Criteria) this;
        }

        public Criteria andVehicleSimNoIsNotNull() {
            addCriterion("vehicle_sim_no is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleSimNoEqualTo(String value) {
            addCriterion("vehicle_sim_no =", value, "vehicleSimNo");
            return (Criteria) this;
        }

        public Criteria andVehicleSimNoNotEqualTo(String value) {
            addCriterion("vehicle_sim_no <>", value, "vehicleSimNo");
            return (Criteria) this;
        }

        public Criteria andVehicleSimNoGreaterThan(String value) {
            addCriterion("vehicle_sim_no >", value, "vehicleSimNo");
            return (Criteria) this;
        }

        public Criteria andVehicleSimNoGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_sim_no >=", value, "vehicleSimNo");
            return (Criteria) this;
        }

        public Criteria andVehicleSimNoLessThan(String value) {
            addCriterion("vehicle_sim_no <", value, "vehicleSimNo");
            return (Criteria) this;
        }

        public Criteria andVehicleSimNoLessThanOrEqualTo(String value) {
            addCriterion("vehicle_sim_no <=", value, "vehicleSimNo");
            return (Criteria) this;
        }

        public Criteria andVehicleSimNoLike(String value) {
            addCriterion("vehicle_sim_no like", value, "vehicleSimNo");
            return (Criteria) this;
        }

        public Criteria andVehicleSimNoNotLike(String value) {
            addCriterion("vehicle_sim_no not like", value, "vehicleSimNo");
            return (Criteria) this;
        }

        public Criteria andVehicleSimNoIn(List<String> values) {
            addCriterion("vehicle_sim_no in", values, "vehicleSimNo");
            return (Criteria) this;
        }

        public Criteria andVehicleSimNoNotIn(List<String> values) {
            addCriterion("vehicle_sim_no not in", values, "vehicleSimNo");
            return (Criteria) this;
        }

        public Criteria andVehicleSimNoBetween(String value1, String value2) {
            addCriterion("vehicle_sim_no between", value1, value2, "vehicleSimNo");
            return (Criteria) this;
        }

        public Criteria andVehicleSimNoNotBetween(String value1, String value2) {
            addCriterion("vehicle_sim_no not between", value1, value2, "vehicleSimNo");
            return (Criteria) this;
        }

        public Criteria andVehicleDeviceNoIsNull() {
            addCriterion("vehicle_device_no is null");
            return (Criteria) this;
        }

        public Criteria andVehicleDeviceNoIsNotNull() {
            addCriterion("vehicle_device_no is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleDeviceNoEqualTo(String value) {
            addCriterion("vehicle_device_no =", value, "vehicleDeviceNo");
            return (Criteria) this;
        }

        public Criteria andVehicleDeviceNoNotEqualTo(String value) {
            addCriterion("vehicle_device_no <>", value, "vehicleDeviceNo");
            return (Criteria) this;
        }

        public Criteria andVehicleDeviceNoGreaterThan(String value) {
            addCriterion("vehicle_device_no >", value, "vehicleDeviceNo");
            return (Criteria) this;
        }

        public Criteria andVehicleDeviceNoGreaterThanOrEqualTo(String value) {
            addCriterion("vehicle_device_no >=", value, "vehicleDeviceNo");
            return (Criteria) this;
        }

        public Criteria andVehicleDeviceNoLessThan(String value) {
            addCriterion("vehicle_device_no <", value, "vehicleDeviceNo");
            return (Criteria) this;
        }

        public Criteria andVehicleDeviceNoLessThanOrEqualTo(String value) {
            addCriterion("vehicle_device_no <=", value, "vehicleDeviceNo");
            return (Criteria) this;
        }

        public Criteria andVehicleDeviceNoLike(String value) {
            addCriterion("vehicle_device_no like", value, "vehicleDeviceNo");
            return (Criteria) this;
        }

        public Criteria andVehicleDeviceNoNotLike(String value) {
            addCriterion("vehicle_device_no not like", value, "vehicleDeviceNo");
            return (Criteria) this;
        }

        public Criteria andVehicleDeviceNoIn(List<String> values) {
            addCriterion("vehicle_device_no in", values, "vehicleDeviceNo");
            return (Criteria) this;
        }

        public Criteria andVehicleDeviceNoNotIn(List<String> values) {
            addCriterion("vehicle_device_no not in", values, "vehicleDeviceNo");
            return (Criteria) this;
        }

        public Criteria andVehicleDeviceNoBetween(String value1, String value2) {
            addCriterion("vehicle_device_no between", value1, value2, "vehicleDeviceNo");
            return (Criteria) this;
        }

        public Criteria andVehicleDeviceNoNotBetween(String value1, String value2) {
            addCriterion("vehicle_device_no not between", value1, value2, "vehicleDeviceNo");
            return (Criteria) this;
        }

        public Criteria andVehicleDeviceTypeIsNull() {
            addCriterion("vehicle_device_type is null");
            return (Criteria) this;
        }

        public Criteria andVehicleDeviceTypeIsNotNull() {
            addCriterion("vehicle_device_type is not null");
            return (Criteria) this;
        }

        public Criteria andVehicleDeviceTypeEqualTo(Byte value) {
            addCriterion("vehicle_device_type =", value, "vehicleDeviceType");
            return (Criteria) this;
        }

        public Criteria andVehicleDeviceTypeNotEqualTo(Byte value) {
            addCriterion("vehicle_device_type <>", value, "vehicleDeviceType");
            return (Criteria) this;
        }

        public Criteria andVehicleDeviceTypeGreaterThan(Byte value) {
            addCriterion("vehicle_device_type >", value, "vehicleDeviceType");
            return (Criteria) this;
        }

        public Criteria andVehicleDeviceTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("vehicle_device_type >=", value, "vehicleDeviceType");
            return (Criteria) this;
        }

        public Criteria andVehicleDeviceTypeLessThan(Byte value) {
            addCriterion("vehicle_device_type <", value, "vehicleDeviceType");
            return (Criteria) this;
        }

        public Criteria andVehicleDeviceTypeLessThanOrEqualTo(Byte value) {
            addCriterion("vehicle_device_type <=", value, "vehicleDeviceType");
            return (Criteria) this;
        }

        public Criteria andVehicleDeviceTypeIn(List<Byte> values) {
            addCriterion("vehicle_device_type in", values, "vehicleDeviceType");
            return (Criteria) this;
        }

        public Criteria andVehicleDeviceTypeNotIn(List<Byte> values) {
            addCriterion("vehicle_device_type not in", values, "vehicleDeviceType");
            return (Criteria) this;
        }

        public Criteria andVehicleDeviceTypeBetween(Byte value1, Byte value2) {
            addCriterion("vehicle_device_type between", value1, value2, "vehicleDeviceType");
            return (Criteria) this;
        }

        public Criteria andVehicleDeviceTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("vehicle_device_type not between", value1, value2, "vehicleDeviceType");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNull() {
            addCriterion("order_no is null");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNotNull() {
            addCriterion("order_no is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNoEqualTo(String value) {
            addCriterion("order_no =", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotEqualTo(String value) {
            addCriterion("order_no <>", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThan(String value) {
            addCriterion("order_no >", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThanOrEqualTo(String value) {
            addCriterion("order_no >=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThan(String value) {
            addCriterion("order_no <", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThanOrEqualTo(String value) {
            addCriterion("order_no <=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLike(String value) {
            addCriterion("order_no like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotLike(String value) {
            addCriterion("order_no not like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoIn(List<String> values) {
            addCriterion("order_no in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotIn(List<String> values) {
            addCriterion("order_no not in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoBetween(String value1, String value2) {
            addCriterion("order_no between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotBetween(String value1, String value2) {
            addCriterion("order_no not between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderApplyNoIsNull() {
            addCriterion("order_apply_no is null");
            return (Criteria) this;
        }

        public Criteria andOrderApplyNoIsNotNull() {
            addCriterion("order_apply_no is not null");
            return (Criteria) this;
        }

        public Criteria andOrderApplyNoEqualTo(String value) {
            addCriterion("order_apply_no =", value, "orderApplyNo");
            return (Criteria) this;
        }

        public Criteria andOrderApplyNoNotEqualTo(String value) {
            addCriterion("order_apply_no <>", value, "orderApplyNo");
            return (Criteria) this;
        }

        public Criteria andOrderApplyNoGreaterThan(String value) {
            addCriterion("order_apply_no >", value, "orderApplyNo");
            return (Criteria) this;
        }

        public Criteria andOrderApplyNoGreaterThanOrEqualTo(String value) {
            addCriterion("order_apply_no >=", value, "orderApplyNo");
            return (Criteria) this;
        }

        public Criteria andOrderApplyNoLessThan(String value) {
            addCriterion("order_apply_no <", value, "orderApplyNo");
            return (Criteria) this;
        }

        public Criteria andOrderApplyNoLessThanOrEqualTo(String value) {
            addCriterion("order_apply_no <=", value, "orderApplyNo");
            return (Criteria) this;
        }

        public Criteria andOrderApplyNoLike(String value) {
            addCriterion("order_apply_no like", value, "orderApplyNo");
            return (Criteria) this;
        }

        public Criteria andOrderApplyNoNotLike(String value) {
            addCriterion("order_apply_no not like", value, "orderApplyNo");
            return (Criteria) this;
        }

        public Criteria andOrderApplyNoIn(List<String> values) {
            addCriterion("order_apply_no in", values, "orderApplyNo");
            return (Criteria) this;
        }

        public Criteria andOrderApplyNoNotIn(List<String> values) {
            addCriterion("order_apply_no not in", values, "orderApplyNo");
            return (Criteria) this;
        }

        public Criteria andOrderApplyNoBetween(String value1, String value2) {
            addCriterion("order_apply_no between", value1, value2, "orderApplyNo");
            return (Criteria) this;
        }

        public Criteria andOrderApplyNoNotBetween(String value1, String value2) {
            addCriterion("order_apply_no not between", value1, value2, "orderApplyNo");
            return (Criteria) this;
        }

        public Criteria andOrderCompanyIdIsNull() {
            addCriterion("order_company_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderCompanyIdIsNotNull() {
            addCriterion("order_company_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderCompanyIdEqualTo(Integer value) {
            addCriterion("order_company_id =", value, "orderCompanyId");
            return (Criteria) this;
        }

        public Criteria andOrderCompanyIdNotEqualTo(Integer value) {
            addCriterion("order_company_id <>", value, "orderCompanyId");
            return (Criteria) this;
        }

        public Criteria andOrderCompanyIdGreaterThan(Integer value) {
            addCriterion("order_company_id >", value, "orderCompanyId");
            return (Criteria) this;
        }

        public Criteria andOrderCompanyIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_company_id >=", value, "orderCompanyId");
            return (Criteria) this;
        }

        public Criteria andOrderCompanyIdLessThan(Integer value) {
            addCriterion("order_company_id <", value, "orderCompanyId");
            return (Criteria) this;
        }

        public Criteria andOrderCompanyIdLessThanOrEqualTo(Integer value) {
            addCriterion("order_company_id <=", value, "orderCompanyId");
            return (Criteria) this;
        }

        public Criteria andOrderCompanyIdIn(List<Integer> values) {
            addCriterion("order_company_id in", values, "orderCompanyId");
            return (Criteria) this;
        }

        public Criteria andOrderCompanyIdNotIn(List<Integer> values) {
            addCriterion("order_company_id not in", values, "orderCompanyId");
            return (Criteria) this;
        }

        public Criteria andOrderCompanyIdBetween(Integer value1, Integer value2) {
            addCriterion("order_company_id between", value1, value2, "orderCompanyId");
            return (Criteria) this;
        }

        public Criteria andOrderCompanyIdNotBetween(Integer value1, Integer value2) {
            addCriterion("order_company_id not between", value1, value2, "orderCompanyId");
            return (Criteria) this;
        }

        public Criteria andOrderStructIdIsNull() {
            addCriterion("order_struct_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderStructIdIsNotNull() {
            addCriterion("order_struct_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderStructIdEqualTo(Integer value) {
            addCriterion("order_struct_id =", value, "orderStructId");
            return (Criteria) this;
        }

        public Criteria andOrderStructIdNotEqualTo(Integer value) {
            addCriterion("order_struct_id <>", value, "orderStructId");
            return (Criteria) this;
        }

        public Criteria andOrderStructIdGreaterThan(Integer value) {
            addCriterion("order_struct_id >", value, "orderStructId");
            return (Criteria) this;
        }

        public Criteria andOrderStructIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_struct_id >=", value, "orderStructId");
            return (Criteria) this;
        }

        public Criteria andOrderStructIdLessThan(Integer value) {
            addCriterion("order_struct_id <", value, "orderStructId");
            return (Criteria) this;
        }

        public Criteria andOrderStructIdLessThanOrEqualTo(Integer value) {
            addCriterion("order_struct_id <=", value, "orderStructId");
            return (Criteria) this;
        }

        public Criteria andOrderStructIdIn(List<Integer> values) {
            addCriterion("order_struct_id in", values, "orderStructId");
            return (Criteria) this;
        }

        public Criteria andOrderStructIdNotIn(List<Integer> values) {
            addCriterion("order_struct_id not in", values, "orderStructId");
            return (Criteria) this;
        }

        public Criteria andOrderStructIdBetween(Integer value1, Integer value2) {
            addCriterion("order_struct_id between", value1, value2, "orderStructId");
            return (Criteria) this;
        }

        public Criteria andOrderStructIdNotBetween(Integer value1, Integer value2) {
            addCriterion("order_struct_id not between", value1, value2, "orderStructId");
            return (Criteria) this;
        }

        public Criteria andOrderStructNameIsNull() {
            addCriterion("order_struct_name is null");
            return (Criteria) this;
        }

        public Criteria andOrderStructNameIsNotNull() {
            addCriterion("order_struct_name is not null");
            return (Criteria) this;
        }

        public Criteria andOrderStructNameEqualTo(String value) {
            addCriterion("order_struct_name =", value, "orderStructName");
            return (Criteria) this;
        }

        public Criteria andOrderStructNameNotEqualTo(String value) {
            addCriterion("order_struct_name <>", value, "orderStructName");
            return (Criteria) this;
        }

        public Criteria andOrderStructNameGreaterThan(String value) {
            addCriterion("order_struct_name >", value, "orderStructName");
            return (Criteria) this;
        }

        public Criteria andOrderStructNameGreaterThanOrEqualTo(String value) {
            addCriterion("order_struct_name >=", value, "orderStructName");
            return (Criteria) this;
        }

        public Criteria andOrderStructNameLessThan(String value) {
            addCriterion("order_struct_name <", value, "orderStructName");
            return (Criteria) this;
        }

        public Criteria andOrderStructNameLessThanOrEqualTo(String value) {
            addCriterion("order_struct_name <=", value, "orderStructName");
            return (Criteria) this;
        }

        public Criteria andOrderStructNameLike(String value) {
            addCriterion("order_struct_name like", value, "orderStructName");
            return (Criteria) this;
        }

        public Criteria andOrderStructNameNotLike(String value) {
            addCriterion("order_struct_name not like", value, "orderStructName");
            return (Criteria) this;
        }

        public Criteria andOrderStructNameIn(List<String> values) {
            addCriterion("order_struct_name in", values, "orderStructName");
            return (Criteria) this;
        }

        public Criteria andOrderStructNameNotIn(List<String> values) {
            addCriterion("order_struct_name not in", values, "orderStructName");
            return (Criteria) this;
        }

        public Criteria andOrderStructNameBetween(String value1, String value2) {
            addCriterion("order_struct_name between", value1, value2, "orderStructName");
            return (Criteria) this;
        }

        public Criteria andOrderStructNameNotBetween(String value1, String value2) {
            addCriterion("order_struct_name not between", value1, value2, "orderStructName");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIsNull() {
            addCriterion("order_type is null");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIsNotNull() {
            addCriterion("order_type is not null");
            return (Criteria) this;
        }

        public Criteria andOrderTypeEqualTo(Byte value) {
            addCriterion("order_type =", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotEqualTo(Byte value) {
            addCriterion("order_type <>", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeGreaterThan(Byte value) {
            addCriterion("order_type >", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("order_type >=", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeLessThan(Byte value) {
            addCriterion("order_type <", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeLessThanOrEqualTo(Byte value) {
            addCriterion("order_type <=", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIn(List<Byte> values) {
            addCriterion("order_type in", values, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotIn(List<Byte> values) {
            addCriterion("order_type not in", values, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeBetween(Byte value1, Byte value2) {
            addCriterion("order_type between", value1, value2, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("order_type not between", value1, value2, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerIdIsNull() {
            addCriterion("order_customer_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerIdIsNotNull() {
            addCriterion("order_customer_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerIdEqualTo(Integer value) {
            addCriterion("order_customer_id =", value, "orderCustomerId");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerIdNotEqualTo(Integer value) {
            addCriterion("order_customer_id <>", value, "orderCustomerId");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerIdGreaterThan(Integer value) {
            addCriterion("order_customer_id >", value, "orderCustomerId");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_customer_id >=", value, "orderCustomerId");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerIdLessThan(Integer value) {
            addCriterion("order_customer_id <", value, "orderCustomerId");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerIdLessThanOrEqualTo(Integer value) {
            addCriterion("order_customer_id <=", value, "orderCustomerId");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerIdIn(List<Integer> values) {
            addCriterion("order_customer_id in", values, "orderCustomerId");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerIdNotIn(List<Integer> values) {
            addCriterion("order_customer_id not in", values, "orderCustomerId");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerIdBetween(Integer value1, Integer value2) {
            addCriterion("order_customer_id between", value1, value2, "orderCustomerId");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerIdNotBetween(Integer value1, Integer value2) {
            addCriterion("order_customer_id not between", value1, value2, "orderCustomerId");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerNameIsNull() {
            addCriterion("order_customer_name is null");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerNameIsNotNull() {
            addCriterion("order_customer_name is not null");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerNameEqualTo(String value) {
            addCriterion("order_customer_name =", value, "orderCustomerName");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerNameNotEqualTo(String value) {
            addCriterion("order_customer_name <>", value, "orderCustomerName");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerNameGreaterThan(String value) {
            addCriterion("order_customer_name >", value, "orderCustomerName");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerNameGreaterThanOrEqualTo(String value) {
            addCriterion("order_customer_name >=", value, "orderCustomerName");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerNameLessThan(String value) {
            addCriterion("order_customer_name <", value, "orderCustomerName");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerNameLessThanOrEqualTo(String value) {
            addCriterion("order_customer_name <=", value, "orderCustomerName");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerNameLike(String value) {
            addCriterion("order_customer_name like", value, "orderCustomerName");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerNameNotLike(String value) {
            addCriterion("order_customer_name not like", value, "orderCustomerName");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerNameIn(List<String> values) {
            addCriterion("order_customer_name in", values, "orderCustomerName");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerNameNotIn(List<String> values) {
            addCriterion("order_customer_name not in", values, "orderCustomerName");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerNameBetween(String value1, String value2) {
            addCriterion("order_customer_name between", value1, value2, "orderCustomerName");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerNameNotBetween(String value1, String value2) {
            addCriterion("order_customer_name not between", value1, value2, "orderCustomerName");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerMobileIsNull() {
            addCriterion("order_customer_mobile is null");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerMobileIsNotNull() {
            addCriterion("order_customer_mobile is not null");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerMobileEqualTo(String value) {
            addCriterion("order_customer_mobile =", value, "orderCustomerMobile");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerMobileNotEqualTo(String value) {
            addCriterion("order_customer_mobile <>", value, "orderCustomerMobile");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerMobileGreaterThan(String value) {
            addCriterion("order_customer_mobile >", value, "orderCustomerMobile");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerMobileGreaterThanOrEqualTo(String value) {
            addCriterion("order_customer_mobile >=", value, "orderCustomerMobile");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerMobileLessThan(String value) {
            addCriterion("order_customer_mobile <", value, "orderCustomerMobile");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerMobileLessThanOrEqualTo(String value) {
            addCriterion("order_customer_mobile <=", value, "orderCustomerMobile");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerMobileLike(String value) {
            addCriterion("order_customer_mobile like", value, "orderCustomerMobile");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerMobileNotLike(String value) {
            addCriterion("order_customer_mobile not like", value, "orderCustomerMobile");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerMobileIn(List<String> values) {
            addCriterion("order_customer_mobile in", values, "orderCustomerMobile");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerMobileNotIn(List<String> values) {
            addCriterion("order_customer_mobile not in", values, "orderCustomerMobile");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerMobileBetween(String value1, String value2) {
            addCriterion("order_customer_mobile between", value1, value2, "orderCustomerMobile");
            return (Criteria) this;
        }

        public Criteria andOrderCustomerMobileNotBetween(String value1, String value2) {
            addCriterion("order_customer_mobile not between", value1, value2, "orderCustomerMobile");
            return (Criteria) this;
        }

        public Criteria andFenceIdIsNull() {
            addCriterion("fence_id is null");
            return (Criteria) this;
        }

        public Criteria andFenceIdIsNotNull() {
            addCriterion("fence_id is not null");
            return (Criteria) this;
        }

        public Criteria andFenceIdEqualTo(Integer value) {
            addCriterion("fence_id =", value, "fenceId");
            return (Criteria) this;
        }

        public Criteria andFenceIdNotEqualTo(Integer value) {
            addCriterion("fence_id <>", value, "fenceId");
            return (Criteria) this;
        }

        public Criteria andFenceIdGreaterThan(Integer value) {
            addCriterion("fence_id >", value, "fenceId");
            return (Criteria) this;
        }

        public Criteria andFenceIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("fence_id >=", value, "fenceId");
            return (Criteria) this;
        }

        public Criteria andFenceIdLessThan(Integer value) {
            addCriterion("fence_id <", value, "fenceId");
            return (Criteria) this;
        }

        public Criteria andFenceIdLessThanOrEqualTo(Integer value) {
            addCriterion("fence_id <=", value, "fenceId");
            return (Criteria) this;
        }

        public Criteria andFenceIdIn(List<Integer> values) {
            addCriterion("fence_id in", values, "fenceId");
            return (Criteria) this;
        }

        public Criteria andFenceIdNotIn(List<Integer> values) {
            addCriterion("fence_id not in", values, "fenceId");
            return (Criteria) this;
        }

        public Criteria andFenceIdBetween(Integer value1, Integer value2) {
            addCriterion("fence_id between", value1, value2, "fenceId");
            return (Criteria) this;
        }

        public Criteria andFenceIdNotBetween(Integer value1, Integer value2) {
            addCriterion("fence_id not between", value1, value2, "fenceId");
            return (Criteria) this;
        }

        public Criteria andFenceNameIsNull() {
            addCriterion("fence_name is null");
            return (Criteria) this;
        }

        public Criteria andFenceNameIsNotNull() {
            addCriterion("fence_name is not null");
            return (Criteria) this;
        }

        public Criteria andFenceNameEqualTo(String value) {
            addCriterion("fence_name =", value, "fenceName");
            return (Criteria) this;
        }

        public Criteria andFenceNameNotEqualTo(String value) {
            addCriterion("fence_name <>", value, "fenceName");
            return (Criteria) this;
        }

        public Criteria andFenceNameGreaterThan(String value) {
            addCriterion("fence_name >", value, "fenceName");
            return (Criteria) this;
        }

        public Criteria andFenceNameGreaterThanOrEqualTo(String value) {
            addCriterion("fence_name >=", value, "fenceName");
            return (Criteria) this;
        }

        public Criteria andFenceNameLessThan(String value) {
            addCriterion("fence_name <", value, "fenceName");
            return (Criteria) this;
        }

        public Criteria andFenceNameLessThanOrEqualTo(String value) {
            addCriterion("fence_name <=", value, "fenceName");
            return (Criteria) this;
        }

        public Criteria andFenceNameLike(String value) {
            addCriterion("fence_name like", value, "fenceName");
            return (Criteria) this;
        }

        public Criteria andFenceNameNotLike(String value) {
            addCriterion("fence_name not like", value, "fenceName");
            return (Criteria) this;
        }

        public Criteria andFenceNameIn(List<String> values) {
            addCriterion("fence_name in", values, "fenceName");
            return (Criteria) this;
        }

        public Criteria andFenceNameNotIn(List<String> values) {
            addCriterion("fence_name not in", values, "fenceName");
            return (Criteria) this;
        }

        public Criteria andFenceNameBetween(String value1, String value2) {
            addCriterion("fence_name between", value1, value2, "fenceName");
            return (Criteria) this;
        }

        public Criteria andFenceNameNotBetween(String value1, String value2) {
            addCriterion("fence_name not between", value1, value2, "fenceName");
            return (Criteria) this;
        }

        public Criteria andFenceCityCodeIsNull() {
            addCriterion("fence_city_code is null");
            return (Criteria) this;
        }

        public Criteria andFenceCityCodeIsNotNull() {
            addCriterion("fence_city_code is not null");
            return (Criteria) this;
        }

        public Criteria andFenceCityCodeEqualTo(Integer value) {
            addCriterion("fence_city_code =", value, "fenceCityCode");
            return (Criteria) this;
        }

        public Criteria andFenceCityCodeNotEqualTo(Integer value) {
            addCriterion("fence_city_code <>", value, "fenceCityCode");
            return (Criteria) this;
        }

        public Criteria andFenceCityCodeGreaterThan(Integer value) {
            addCriterion("fence_city_code >", value, "fenceCityCode");
            return (Criteria) this;
        }

        public Criteria andFenceCityCodeGreaterThanOrEqualTo(Integer value) {
            addCriterion("fence_city_code >=", value, "fenceCityCode");
            return (Criteria) this;
        }

        public Criteria andFenceCityCodeLessThan(Integer value) {
            addCriterion("fence_city_code <", value, "fenceCityCode");
            return (Criteria) this;
        }

        public Criteria andFenceCityCodeLessThanOrEqualTo(Integer value) {
            addCriterion("fence_city_code <=", value, "fenceCityCode");
            return (Criteria) this;
        }

        public Criteria andFenceCityCodeIn(List<Integer> values) {
            addCriterion("fence_city_code in", values, "fenceCityCode");
            return (Criteria) this;
        }

        public Criteria andFenceCityCodeNotIn(List<Integer> values) {
            addCriterion("fence_city_code not in", values, "fenceCityCode");
            return (Criteria) this;
        }

        public Criteria andFenceCityCodeBetween(Integer value1, Integer value2) {
            addCriterion("fence_city_code between", value1, value2, "fenceCityCode");
            return (Criteria) this;
        }

        public Criteria andFenceCityCodeNotBetween(Integer value1, Integer value2) {
            addCriterion("fence_city_code not between", value1, value2, "fenceCityCode");
            return (Criteria) this;
        }

        public Criteria andFenceCityNameIsNull() {
            addCriterion("fence_city_name is null");
            return (Criteria) this;
        }

        public Criteria andFenceCityNameIsNotNull() {
            addCriterion("fence_city_name is not null");
            return (Criteria) this;
        }

        public Criteria andFenceCityNameEqualTo(String value) {
            addCriterion("fence_city_name =", value, "fenceCityName");
            return (Criteria) this;
        }

        public Criteria andFenceCityNameNotEqualTo(String value) {
            addCriterion("fence_city_name <>", value, "fenceCityName");
            return (Criteria) this;
        }

        public Criteria andFenceCityNameGreaterThan(String value) {
            addCriterion("fence_city_name >", value, "fenceCityName");
            return (Criteria) this;
        }

        public Criteria andFenceCityNameGreaterThanOrEqualTo(String value) {
            addCriterion("fence_city_name >=", value, "fenceCityName");
            return (Criteria) this;
        }

        public Criteria andFenceCityNameLessThan(String value) {
            addCriterion("fence_city_name <", value, "fenceCityName");
            return (Criteria) this;
        }

        public Criteria andFenceCityNameLessThanOrEqualTo(String value) {
            addCriterion("fence_city_name <=", value, "fenceCityName");
            return (Criteria) this;
        }

        public Criteria andFenceCityNameLike(String value) {
            addCriterion("fence_city_name like", value, "fenceCityName");
            return (Criteria) this;
        }

        public Criteria andFenceCityNameNotLike(String value) {
            addCriterion("fence_city_name not like", value, "fenceCityName");
            return (Criteria) this;
        }

        public Criteria andFenceCityNameIn(List<String> values) {
            addCriterion("fence_city_name in", values, "fenceCityName");
            return (Criteria) this;
        }

        public Criteria andFenceCityNameNotIn(List<String> values) {
            addCriterion("fence_city_name not in", values, "fenceCityName");
            return (Criteria) this;
        }

        public Criteria andFenceCityNameBetween(String value1, String value2) {
            addCriterion("fence_city_name between", value1, value2, "fenceCityName");
            return (Criteria) this;
        }

        public Criteria andFenceCityNameNotBetween(String value1, String value2) {
            addCriterion("fence_city_name not between", value1, value2, "fenceCityName");
            return (Criteria) this;
        }

        public Criteria andFenceWarnTypeIsNull() {
            addCriterion("fence_warn_type is null");
            return (Criteria) this;
        }

        public Criteria andFenceWarnTypeIsNotNull() {
            addCriterion("fence_warn_type is not null");
            return (Criteria) this;
        }

        public Criteria andFenceWarnTypeEqualTo(Byte value) {
            addCriterion("fence_warn_type =", value, "fenceWarnType");
            return (Criteria) this;
        }

        public Criteria andFenceWarnTypeNotEqualTo(Byte value) {
            addCriterion("fence_warn_type <>", value, "fenceWarnType");
            return (Criteria) this;
        }

        public Criteria andFenceWarnTypeGreaterThan(Byte value) {
            addCriterion("fence_warn_type >", value, "fenceWarnType");
            return (Criteria) this;
        }

        public Criteria andFenceWarnTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("fence_warn_type >=", value, "fenceWarnType");
            return (Criteria) this;
        }

        public Criteria andFenceWarnTypeLessThan(Byte value) {
            addCriterion("fence_warn_type <", value, "fenceWarnType");
            return (Criteria) this;
        }

        public Criteria andFenceWarnTypeLessThanOrEqualTo(Byte value) {
            addCriterion("fence_warn_type <=", value, "fenceWarnType");
            return (Criteria) this;
        }

        public Criteria andFenceWarnTypeIn(List<Byte> values) {
            addCriterion("fence_warn_type in", values, "fenceWarnType");
            return (Criteria) this;
        }

        public Criteria andFenceWarnTypeNotIn(List<Byte> values) {
            addCriterion("fence_warn_type not in", values, "fenceWarnType");
            return (Criteria) this;
        }

        public Criteria andFenceWarnTypeBetween(Byte value1, Byte value2) {
            addCriterion("fence_warn_type between", value1, value2, "fenceWarnType");
            return (Criteria) this;
        }

        public Criteria andFenceWarnTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("fence_warn_type not between", value1, value2, "fenceWarnType");
            return (Criteria) this;
        }

        public Criteria andFenceAddressIsNull() {
            addCriterion("fence_address is null");
            return (Criteria) this;
        }

        public Criteria andFenceAddressIsNotNull() {
            addCriterion("fence_address is not null");
            return (Criteria) this;
        }

        public Criteria andFenceAddressEqualTo(String value) {
            addCriterion("fence_address =", value, "fenceAddress");
            return (Criteria) this;
        }

        public Criteria andFenceAddressNotEqualTo(String value) {
            addCriterion("fence_address <>", value, "fenceAddress");
            return (Criteria) this;
        }

        public Criteria andFenceAddressGreaterThan(String value) {
            addCriterion("fence_address >", value, "fenceAddress");
            return (Criteria) this;
        }

        public Criteria andFenceAddressGreaterThanOrEqualTo(String value) {
            addCriterion("fence_address >=", value, "fenceAddress");
            return (Criteria) this;
        }

        public Criteria andFenceAddressLessThan(String value) {
            addCriterion("fence_address <", value, "fenceAddress");
            return (Criteria) this;
        }

        public Criteria andFenceAddressLessThanOrEqualTo(String value) {
            addCriterion("fence_address <=", value, "fenceAddress");
            return (Criteria) this;
        }

        public Criteria andFenceAddressLike(String value) {
            addCriterion("fence_address like", value, "fenceAddress");
            return (Criteria) this;
        }

        public Criteria andFenceAddressNotLike(String value) {
            addCriterion("fence_address not like", value, "fenceAddress");
            return (Criteria) this;
        }

        public Criteria andFenceAddressIn(List<String> values) {
            addCriterion("fence_address in", values, "fenceAddress");
            return (Criteria) this;
        }

        public Criteria andFenceAddressNotIn(List<String> values) {
            addCriterion("fence_address not in", values, "fenceAddress");
            return (Criteria) this;
        }

        public Criteria andFenceAddressBetween(String value1, String value2) {
            addCriterion("fence_address between", value1, value2, "fenceAddress");
            return (Criteria) this;
        }

        public Criteria andFenceAddressNotBetween(String value1, String value2) {
            addCriterion("fence_address not between", value1, value2, "fenceAddress");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}