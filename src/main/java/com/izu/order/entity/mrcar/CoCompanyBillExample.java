package com.izu.order.entity.mrcar;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class CoCompanyBillExample {
    /**
     * co_company_bill
     */
    protected String orderByClause;

    /**
     * co_company_bill
     */
    protected boolean distinct;

    /**
     * co_company_bill
     */
    protected List<Criteria> oredCriteria;

    public CoCompanyBillExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andCompanyBillIdIsNull() {
            addCriterion("company_bill_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyBillIdIsNotNull() {
            addCriterion("company_bill_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyBillIdEqualTo(Integer value) {
            addCriterion("company_bill_id =", value, "companyBillId");
            return (Criteria) this;
        }

        public Criteria andCompanyBillIdNotEqualTo(Integer value) {
            addCriterion("company_bill_id <>", value, "companyBillId");
            return (Criteria) this;
        }

        public Criteria andCompanyBillIdGreaterThan(Integer value) {
            addCriterion("company_bill_id >", value, "companyBillId");
            return (Criteria) this;
        }

        public Criteria andCompanyBillIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("company_bill_id >=", value, "companyBillId");
            return (Criteria) this;
        }

        public Criteria andCompanyBillIdLessThan(Integer value) {
            addCriterion("company_bill_id <", value, "companyBillId");
            return (Criteria) this;
        }

        public Criteria andCompanyBillIdLessThanOrEqualTo(Integer value) {
            addCriterion("company_bill_id <=", value, "companyBillId");
            return (Criteria) this;
        }

        public Criteria andCompanyBillIdIn(List<Integer> values) {
            addCriterion("company_bill_id in", values, "companyBillId");
            return (Criteria) this;
        }

        public Criteria andCompanyBillIdNotIn(List<Integer> values) {
            addCriterion("company_bill_id not in", values, "companyBillId");
            return (Criteria) this;
        }

        public Criteria andCompanyBillIdBetween(Integer value1, Integer value2) {
            addCriterion("company_bill_id between", value1, value2, "companyBillId");
            return (Criteria) this;
        }

        public Criteria andCompanyBillIdNotBetween(Integer value1, Integer value2) {
            addCriterion("company_bill_id not between", value1, value2, "companyBillId");
            return (Criteria) this;
        }

        public Criteria andCompanyBillNoIsNull() {
            addCriterion("company_bill_no is null");
            return (Criteria) this;
        }

        public Criteria andCompanyBillNoIsNotNull() {
            addCriterion("company_bill_no is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyBillNoEqualTo(String value) {
            addCriterion("company_bill_no =", value, "companyBillNo");
            return (Criteria) this;
        }

        public Criteria andCompanyBillNoNotEqualTo(String value) {
            addCriterion("company_bill_no <>", value, "companyBillNo");
            return (Criteria) this;
        }

        public Criteria andCompanyBillNoGreaterThan(String value) {
            addCriterion("company_bill_no >", value, "companyBillNo");
            return (Criteria) this;
        }

        public Criteria andCompanyBillNoGreaterThanOrEqualTo(String value) {
            addCriterion("company_bill_no >=", value, "companyBillNo");
            return (Criteria) this;
        }

        public Criteria andCompanyBillNoLessThan(String value) {
            addCriterion("company_bill_no <", value, "companyBillNo");
            return (Criteria) this;
        }

        public Criteria andCompanyBillNoLessThanOrEqualTo(String value) {
            addCriterion("company_bill_no <=", value, "companyBillNo");
            return (Criteria) this;
        }

        public Criteria andCompanyBillNoLike(String value) {
            addCriterion("company_bill_no like", value, "companyBillNo");
            return (Criteria) this;
        }

        public Criteria andCompanyBillNoNotLike(String value) {
            addCriterion("company_bill_no not like", value, "companyBillNo");
            return (Criteria) this;
        }

        public Criteria andCompanyBillNoIn(List<String> values) {
            addCriterion("company_bill_no in", values, "companyBillNo");
            return (Criteria) this;
        }

        public Criteria andCompanyBillNoNotIn(List<String> values) {
            addCriterion("company_bill_no not in", values, "companyBillNo");
            return (Criteria) this;
        }

        public Criteria andCompanyBillNoBetween(String value1, String value2) {
            addCriterion("company_bill_no between", value1, value2, "companyBillNo");
            return (Criteria) this;
        }

        public Criteria andCompanyBillNoNotBetween(String value1, String value2) {
            addCriterion("company_bill_no not between", value1, value2, "companyBillNo");
            return (Criteria) this;
        }

        public Criteria andBillStatusIsNull() {
            addCriterion("bill_status is null");
            return (Criteria) this;
        }

        public Criteria andBillStatusIsNotNull() {
            addCriterion("bill_status is not null");
            return (Criteria) this;
        }

        public Criteria andBillStatusEqualTo(Integer value) {
            addCriterion("bill_status =", value, "billStatus");
            return (Criteria) this;
        }

        public Criteria andBillStatusNotEqualTo(Integer value) {
            addCriterion("bill_status <>", value, "billStatus");
            return (Criteria) this;
        }

        public Criteria andBillStatusGreaterThan(Integer value) {
            addCriterion("bill_status >", value, "billStatus");
            return (Criteria) this;
        }

        public Criteria andBillStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("bill_status >=", value, "billStatus");
            return (Criteria) this;
        }

        public Criteria andBillStatusLessThan(Integer value) {
            addCriterion("bill_status <", value, "billStatus");
            return (Criteria) this;
        }

        public Criteria andBillStatusLessThanOrEqualTo(Integer value) {
            addCriterion("bill_status <=", value, "billStatus");
            return (Criteria) this;
        }

        public Criteria andBillStatusIn(List<Integer> values) {
            addCriterion("bill_status in", values, "billStatus");
            return (Criteria) this;
        }

        public Criteria andBillStatusNotIn(List<Integer> values) {
            addCriterion("bill_status not in", values, "billStatus");
            return (Criteria) this;
        }

        public Criteria andBillStatusBetween(Integer value1, Integer value2) {
            addCriterion("bill_status between", value1, value2, "billStatus");
            return (Criteria) this;
        }

        public Criteria andBillStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("bill_status not between", value1, value2, "billStatus");
            return (Criteria) this;
        }

        public Criteria andBillStartDateIsNull() {
            addCriterion("bill_start_date is null");
            return (Criteria) this;
        }

        public Criteria andBillStartDateIsNotNull() {
            addCriterion("bill_start_date is not null");
            return (Criteria) this;
        }

        public Criteria andBillStartDateEqualTo(Date value) {
            addCriterionForJDBCDate("bill_start_date =", value, "billStartDate");
            return (Criteria) this;
        }

        public Criteria andBillStartDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("bill_start_date <>", value, "billStartDate");
            return (Criteria) this;
        }

        public Criteria andBillStartDateGreaterThan(Date value) {
            addCriterionForJDBCDate("bill_start_date >", value, "billStartDate");
            return (Criteria) this;
        }

        public Criteria andBillStartDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("bill_start_date >=", value, "billStartDate");
            return (Criteria) this;
        }

        public Criteria andBillStartDateLessThan(Date value) {
            addCriterionForJDBCDate("bill_start_date <", value, "billStartDate");
            return (Criteria) this;
        }

        public Criteria andBillStartDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("bill_start_date <=", value, "billStartDate");
            return (Criteria) this;
        }

        public Criteria andBillStartDateIn(List<Date> values) {
            addCriterionForJDBCDate("bill_start_date in", values, "billStartDate");
            return (Criteria) this;
        }

        public Criteria andBillStartDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("bill_start_date not in", values, "billStartDate");
            return (Criteria) this;
        }

        public Criteria andBillStartDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("bill_start_date between", value1, value2, "billStartDate");
            return (Criteria) this;
        }

        public Criteria andBillStartDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("bill_start_date not between", value1, value2, "billStartDate");
            return (Criteria) this;
        }

        public Criteria andBillEndDateIsNull() {
            addCriterion("bill_end_date is null");
            return (Criteria) this;
        }

        public Criteria andBillEndDateIsNotNull() {
            addCriterion("bill_end_date is not null");
            return (Criteria) this;
        }

        public Criteria andBillEndDateEqualTo(Date value) {
            addCriterionForJDBCDate("bill_end_date =", value, "billEndDate");
            return (Criteria) this;
        }

        public Criteria andBillEndDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("bill_end_date <>", value, "billEndDate");
            return (Criteria) this;
        }

        public Criteria andBillEndDateGreaterThan(Date value) {
            addCriterionForJDBCDate("bill_end_date >", value, "billEndDate");
            return (Criteria) this;
        }

        public Criteria andBillEndDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("bill_end_date >=", value, "billEndDate");
            return (Criteria) this;
        }

        public Criteria andBillEndDateLessThan(Date value) {
            addCriterionForJDBCDate("bill_end_date <", value, "billEndDate");
            return (Criteria) this;
        }

        public Criteria andBillEndDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("bill_end_date <=", value, "billEndDate");
            return (Criteria) this;
        }

        public Criteria andBillEndDateIn(List<Date> values) {
            addCriterionForJDBCDate("bill_end_date in", values, "billEndDate");
            return (Criteria) this;
        }

        public Criteria andBillEndDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("bill_end_date not in", values, "billEndDate");
            return (Criteria) this;
        }

        public Criteria andBillEndDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("bill_end_date between", value1, value2, "billEndDate");
            return (Criteria) this;
        }

        public Criteria andBillEndDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("bill_end_date not between", value1, value2, "billEndDate");
            return (Criteria) this;
        }

        public Criteria andBillDateIsNull() {
            addCriterion("bill_date is null");
            return (Criteria) this;
        }

        public Criteria andBillDateIsNotNull() {
            addCriterion("bill_date is not null");
            return (Criteria) this;
        }

        public Criteria andBillDateEqualTo(Date value) {
            addCriterionForJDBCDate("bill_date =", value, "billDate");
            return (Criteria) this;
        }

        public Criteria andBillDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("bill_date <>", value, "billDate");
            return (Criteria) this;
        }

        public Criteria andBillDateGreaterThan(Date value) {
            addCriterionForJDBCDate("bill_date >", value, "billDate");
            return (Criteria) this;
        }

        public Criteria andBillDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("bill_date >=", value, "billDate");
            return (Criteria) this;
        }

        public Criteria andBillDateLessThan(Date value) {
            addCriterionForJDBCDate("bill_date <", value, "billDate");
            return (Criteria) this;
        }

        public Criteria andBillDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("bill_date <=", value, "billDate");
            return (Criteria) this;
        }

        public Criteria andBillDateIn(List<Date> values) {
            addCriterionForJDBCDate("bill_date in", values, "billDate");
            return (Criteria) this;
        }

        public Criteria andBillDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("bill_date not in", values, "billDate");
            return (Criteria) this;
        }

        public Criteria andBillDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("bill_date between", value1, value2, "billDate");
            return (Criteria) this;
        }

        public Criteria andBillDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("bill_date not between", value1, value2, "billDate");
            return (Criteria) this;
        }

        public Criteria andBillAmountIsNull() {
            addCriterion("bill_amount is null");
            return (Criteria) this;
        }

        public Criteria andBillAmountIsNotNull() {
            addCriterion("bill_amount is not null");
            return (Criteria) this;
        }

        public Criteria andBillAmountEqualTo(BigDecimal value) {
            addCriterion("bill_amount =", value, "billAmount");
            return (Criteria) this;
        }

        public Criteria andBillAmountNotEqualTo(BigDecimal value) {
            addCriterion("bill_amount <>", value, "billAmount");
            return (Criteria) this;
        }

        public Criteria andBillAmountGreaterThan(BigDecimal value) {
            addCriterion("bill_amount >", value, "billAmount");
            return (Criteria) this;
        }

        public Criteria andBillAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("bill_amount >=", value, "billAmount");
            return (Criteria) this;
        }

        public Criteria andBillAmountLessThan(BigDecimal value) {
            addCriterion("bill_amount <", value, "billAmount");
            return (Criteria) this;
        }

        public Criteria andBillAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("bill_amount <=", value, "billAmount");
            return (Criteria) this;
        }

        public Criteria andBillAmountIn(List<BigDecimal> values) {
            addCriterion("bill_amount in", values, "billAmount");
            return (Criteria) this;
        }

        public Criteria andBillAmountNotIn(List<BigDecimal> values) {
            addCriterion("bill_amount not in", values, "billAmount");
            return (Criteria) this;
        }

        public Criteria andBillAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("bill_amount between", value1, value2, "billAmount");
            return (Criteria) this;
        }

        public Criteria andBillAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("bill_amount not between", value1, value2, "billAmount");
            return (Criteria) this;
        }

        public Criteria andCarCountIsNull() {
            addCriterion("car_count is null");
            return (Criteria) this;
        }

        public Criteria andCarCountIsNotNull() {
            addCriterion("car_count is not null");
            return (Criteria) this;
        }

        public Criteria andCarCountEqualTo(Integer value) {
            addCriterion("car_count =", value, "carCount");
            return (Criteria) this;
        }

        public Criteria andCarCountNotEqualTo(Integer value) {
            addCriterion("car_count <>", value, "carCount");
            return (Criteria) this;
        }

        public Criteria andCarCountGreaterThan(Integer value) {
            addCriterion("car_count >", value, "carCount");
            return (Criteria) this;
        }

        public Criteria andCarCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("car_count >=", value, "carCount");
            return (Criteria) this;
        }

        public Criteria andCarCountLessThan(Integer value) {
            addCriterion("car_count <", value, "carCount");
            return (Criteria) this;
        }

        public Criteria andCarCountLessThanOrEqualTo(Integer value) {
            addCriterion("car_count <=", value, "carCount");
            return (Criteria) this;
        }

        public Criteria andCarCountIn(List<Integer> values) {
            addCriterion("car_count in", values, "carCount");
            return (Criteria) this;
        }

        public Criteria andCarCountNotIn(List<Integer> values) {
            addCriterion("car_count not in", values, "carCount");
            return (Criteria) this;
        }

        public Criteria andCarCountBetween(Integer value1, Integer value2) {
            addCriterion("car_count between", value1, value2, "carCount");
            return (Criteria) this;
        }

        public Criteria andCarCountNotBetween(Integer value1, Integer value2) {
            addCriterion("car_count not between", value1, value2, "carCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountIsNull() {
            addCriterion("order_count is null");
            return (Criteria) this;
        }

        public Criteria andOrderCountIsNotNull() {
            addCriterion("order_count is not null");
            return (Criteria) this;
        }

        public Criteria andOrderCountEqualTo(Integer value) {
            addCriterion("order_count =", value, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountNotEqualTo(Integer value) {
            addCriterion("order_count <>", value, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountGreaterThan(Integer value) {
            addCriterion("order_count >", value, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_count >=", value, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountLessThan(Integer value) {
            addCriterion("order_count <", value, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountLessThanOrEqualTo(Integer value) {
            addCriterion("order_count <=", value, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountIn(List<Integer> values) {
            addCriterion("order_count in", values, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountNotIn(List<Integer> values) {
            addCriterion("order_count not in", values, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountBetween(Integer value1, Integer value2) {
            addCriterion("order_count between", value1, value2, "orderCount");
            return (Criteria) this;
        }

        public Criteria andOrderCountNotBetween(Integer value1, Integer value2) {
            addCriterion("order_count not between", value1, value2, "orderCount");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Integer value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Integer value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Integer value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Integer value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Integer value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Integer> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Integer> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Integer value1, Integer value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Integer value1, Integer value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNull() {
            addCriterion("company_code is null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNotNull() {
            addCriterion("company_code is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeEqualTo(String value) {
            addCriterion("company_code =", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotEqualTo(String value) {
            addCriterion("company_code <>", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThan(String value) {
            addCriterion("company_code >", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("company_code >=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThan(String value) {
            addCriterion("company_code <", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("company_code <=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLike(String value) {
            addCriterion("company_code like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotLike(String value) {
            addCriterion("company_code not like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIn(List<String> values) {
            addCriterion("company_code in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotIn(List<String> values) {
            addCriterion("company_code not in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeBetween(String value1, String value2) {
            addCriterion("company_code between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("company_code not between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIsNull() {
            addCriterion("company_name is null");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIsNotNull() {
            addCriterion("company_name is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyNameEqualTo(String value) {
            addCriterion("company_name =", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotEqualTo(String value) {
            addCriterion("company_name <>", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameGreaterThan(String value) {
            addCriterion("company_name >", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameGreaterThanOrEqualTo(String value) {
            addCriterion("company_name >=", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLessThan(String value) {
            addCriterion("company_name <", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLessThanOrEqualTo(String value) {
            addCriterion("company_name <=", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameLike(String value) {
            addCriterion("company_name like", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotLike(String value) {
            addCriterion("company_name not like", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIn(List<String> values) {
            addCriterion("company_name in", values, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotIn(List<String> values) {
            addCriterion("company_name not in", values, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameBetween(String value1, String value2) {
            addCriterion("company_name between", value1, value2, "companyName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameNotBetween(String value1, String value2) {
            addCriterion("company_name not between", value1, value2, "companyName");
            return (Criteria) this;
        }

        public Criteria andSignStructCodeIsNull() {
            addCriterion("sign_struct_code is null");
            return (Criteria) this;
        }

        public Criteria andSignStructCodeIsNotNull() {
            addCriterion("sign_struct_code is not null");
            return (Criteria) this;
        }

        public Criteria andSignStructCodeEqualTo(String value) {
            addCriterion("sign_struct_code =", value, "signStructCode");
            return (Criteria) this;
        }

        public Criteria andSignStructCodeNotEqualTo(String value) {
            addCriterion("sign_struct_code <>", value, "signStructCode");
            return (Criteria) this;
        }

        public Criteria andSignStructCodeGreaterThan(String value) {
            addCriterion("sign_struct_code >", value, "signStructCode");
            return (Criteria) this;
        }

        public Criteria andSignStructCodeGreaterThanOrEqualTo(String value) {
            addCriterion("sign_struct_code >=", value, "signStructCode");
            return (Criteria) this;
        }

        public Criteria andSignStructCodeLessThan(String value) {
            addCriterion("sign_struct_code <", value, "signStructCode");
            return (Criteria) this;
        }

        public Criteria andSignStructCodeLessThanOrEqualTo(String value) {
            addCriterion("sign_struct_code <=", value, "signStructCode");
            return (Criteria) this;
        }

        public Criteria andSignStructCodeLike(String value) {
            addCriterion("sign_struct_code like", value, "signStructCode");
            return (Criteria) this;
        }

        public Criteria andSignStructCodeNotLike(String value) {
            addCriterion("sign_struct_code not like", value, "signStructCode");
            return (Criteria) this;
        }

        public Criteria andSignStructCodeIn(List<String> values) {
            addCriterion("sign_struct_code in", values, "signStructCode");
            return (Criteria) this;
        }

        public Criteria andSignStructCodeNotIn(List<String> values) {
            addCriterion("sign_struct_code not in", values, "signStructCode");
            return (Criteria) this;
        }

        public Criteria andSignStructCodeBetween(String value1, String value2) {
            addCriterion("sign_struct_code between", value1, value2, "signStructCode");
            return (Criteria) this;
        }

        public Criteria andSignStructCodeNotBetween(String value1, String value2) {
            addCriterion("sign_struct_code not between", value1, value2, "signStructCode");
            return (Criteria) this;
        }

        public Criteria andSignStructNameIsNull() {
            addCriterion("sign_struct_name is null");
            return (Criteria) this;
        }

        public Criteria andSignStructNameIsNotNull() {
            addCriterion("sign_struct_name is not null");
            return (Criteria) this;
        }

        public Criteria andSignStructNameEqualTo(String value) {
            addCriterion("sign_struct_name =", value, "signStructName");
            return (Criteria) this;
        }

        public Criteria andSignStructNameNotEqualTo(String value) {
            addCriterion("sign_struct_name <>", value, "signStructName");
            return (Criteria) this;
        }

        public Criteria andSignStructNameGreaterThan(String value) {
            addCriterion("sign_struct_name >", value, "signStructName");
            return (Criteria) this;
        }

        public Criteria andSignStructNameGreaterThanOrEqualTo(String value) {
            addCriterion("sign_struct_name >=", value, "signStructName");
            return (Criteria) this;
        }

        public Criteria andSignStructNameLessThan(String value) {
            addCriterion("sign_struct_name <", value, "signStructName");
            return (Criteria) this;
        }

        public Criteria andSignStructNameLessThanOrEqualTo(String value) {
            addCriterion("sign_struct_name <=", value, "signStructName");
            return (Criteria) this;
        }

        public Criteria andSignStructNameLike(String value) {
            addCriterion("sign_struct_name like", value, "signStructName");
            return (Criteria) this;
        }

        public Criteria andSignStructNameNotLike(String value) {
            addCriterion("sign_struct_name not like", value, "signStructName");
            return (Criteria) this;
        }

        public Criteria andSignStructNameIn(List<String> values) {
            addCriterion("sign_struct_name in", values, "signStructName");
            return (Criteria) this;
        }

        public Criteria andSignStructNameNotIn(List<String> values) {
            addCriterion("sign_struct_name not in", values, "signStructName");
            return (Criteria) this;
        }

        public Criteria andSignStructNameBetween(String value1, String value2) {
            addCriterion("sign_struct_name between", value1, value2, "signStructName");
            return (Criteria) this;
        }

        public Criteria andSignStructNameNotBetween(String value1, String value2) {
            addCriterion("sign_struct_name not between", value1, value2, "signStructName");
            return (Criteria) this;
        }

        public Criteria andTaxRateIsNull() {
            addCriterion("tax_rate is null");
            return (Criteria) this;
        }

        public Criteria andTaxRateIsNotNull() {
            addCriterion("tax_rate is not null");
            return (Criteria) this;
        }

        public Criteria andTaxRateEqualTo(BigDecimal value) {
            addCriterion("tax_rate =", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateNotEqualTo(BigDecimal value) {
            addCriterion("tax_rate <>", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateGreaterThan(BigDecimal value) {
            addCriterion("tax_rate >", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("tax_rate >=", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateLessThan(BigDecimal value) {
            addCriterion("tax_rate <", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateLessThanOrEqualTo(BigDecimal value) {
            addCriterion("tax_rate <=", value, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateIn(List<BigDecimal> values) {
            addCriterion("tax_rate in", values, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateNotIn(List<BigDecimal> values) {
            addCriterion("tax_rate not in", values, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("tax_rate between", value1, value2, "taxRate");
            return (Criteria) this;
        }

        public Criteria andTaxRateNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("tax_rate not between", value1, value2, "taxRate");
            return (Criteria) this;
        }

        public Criteria andOverdueDaysIsNull() {
            addCriterion("overdue_days is null");
            return (Criteria) this;
        }

        public Criteria andOverdueDaysIsNotNull() {
            addCriterion("overdue_days is not null");
            return (Criteria) this;
        }

        public Criteria andOverdueDaysEqualTo(Integer value) {
            addCriterion("overdue_days =", value, "overdueDays");
            return (Criteria) this;
        }

        public Criteria andOverdueDaysNotEqualTo(Integer value) {
            addCriterion("overdue_days <>", value, "overdueDays");
            return (Criteria) this;
        }

        public Criteria andOverdueDaysGreaterThan(Integer value) {
            addCriterion("overdue_days >", value, "overdueDays");
            return (Criteria) this;
        }

        public Criteria andOverdueDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("overdue_days >=", value, "overdueDays");
            return (Criteria) this;
        }

        public Criteria andOverdueDaysLessThan(Integer value) {
            addCriterion("overdue_days <", value, "overdueDays");
            return (Criteria) this;
        }

        public Criteria andOverdueDaysLessThanOrEqualTo(Integer value) {
            addCriterion("overdue_days <=", value, "overdueDays");
            return (Criteria) this;
        }

        public Criteria andOverdueDaysIn(List<Integer> values) {
            addCriterion("overdue_days in", values, "overdueDays");
            return (Criteria) this;
        }

        public Criteria andOverdueDaysNotIn(List<Integer> values) {
            addCriterion("overdue_days not in", values, "overdueDays");
            return (Criteria) this;
        }

        public Criteria andOverdueDaysBetween(Integer value1, Integer value2) {
            addCriterion("overdue_days between", value1, value2, "overdueDays");
            return (Criteria) this;
        }

        public Criteria andOverdueDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("overdue_days not between", value1, value2, "overdueDays");
            return (Criteria) this;
        }

        public Criteria andBelongStructIdIsNull() {
            addCriterion("belong_struct_id is null");
            return (Criteria) this;
        }

        public Criteria andBelongStructIdIsNotNull() {
            addCriterion("belong_struct_id is not null");
            return (Criteria) this;
        }

        public Criteria andBelongStructIdEqualTo(Integer value) {
            addCriterion("belong_struct_id =", value, "belongStructId");
            return (Criteria) this;
        }

        public Criteria andBelongStructIdNotEqualTo(Integer value) {
            addCriterion("belong_struct_id <>", value, "belongStructId");
            return (Criteria) this;
        }

        public Criteria andBelongStructIdGreaterThan(Integer value) {
            addCriterion("belong_struct_id >", value, "belongStructId");
            return (Criteria) this;
        }

        public Criteria andBelongStructIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("belong_struct_id >=", value, "belongStructId");
            return (Criteria) this;
        }

        public Criteria andBelongStructIdLessThan(Integer value) {
            addCriterion("belong_struct_id <", value, "belongStructId");
            return (Criteria) this;
        }

        public Criteria andBelongStructIdLessThanOrEqualTo(Integer value) {
            addCriterion("belong_struct_id <=", value, "belongStructId");
            return (Criteria) this;
        }

        public Criteria andBelongStructIdIn(List<Integer> values) {
            addCriterion("belong_struct_id in", values, "belongStructId");
            return (Criteria) this;
        }

        public Criteria andBelongStructIdNotIn(List<Integer> values) {
            addCriterion("belong_struct_id not in", values, "belongStructId");
            return (Criteria) this;
        }

        public Criteria andBelongStructIdBetween(Integer value1, Integer value2) {
            addCriterion("belong_struct_id between", value1, value2, "belongStructId");
            return (Criteria) this;
        }

        public Criteria andBelongStructIdNotBetween(Integer value1, Integer value2) {
            addCriterion("belong_struct_id not between", value1, value2, "belongStructId");
            return (Criteria) this;
        }

        public Criteria andBelongStructCodeIsNull() {
            addCriterion("belong_struct_code is null");
            return (Criteria) this;
        }

        public Criteria andBelongStructCodeIsNotNull() {
            addCriterion("belong_struct_code is not null");
            return (Criteria) this;
        }

        public Criteria andBelongStructCodeEqualTo(String value) {
            addCriterion("belong_struct_code =", value, "belongStructCode");
            return (Criteria) this;
        }

        public Criteria andBelongStructCodeNotEqualTo(String value) {
            addCriterion("belong_struct_code <>", value, "belongStructCode");
            return (Criteria) this;
        }

        public Criteria andBelongStructCodeGreaterThan(String value) {
            addCriterion("belong_struct_code >", value, "belongStructCode");
            return (Criteria) this;
        }

        public Criteria andBelongStructCodeGreaterThanOrEqualTo(String value) {
            addCriterion("belong_struct_code >=", value, "belongStructCode");
            return (Criteria) this;
        }

        public Criteria andBelongStructCodeLessThan(String value) {
            addCriterion("belong_struct_code <", value, "belongStructCode");
            return (Criteria) this;
        }

        public Criteria andBelongStructCodeLessThanOrEqualTo(String value) {
            addCriterion("belong_struct_code <=", value, "belongStructCode");
            return (Criteria) this;
        }

        public Criteria andBelongStructCodeLike(String value) {
            addCriterion("belong_struct_code like", value, "belongStructCode");
            return (Criteria) this;
        }

        public Criteria andBelongStructCodeNotLike(String value) {
            addCriterion("belong_struct_code not like", value, "belongStructCode");
            return (Criteria) this;
        }

        public Criteria andBelongStructCodeIn(List<String> values) {
            addCriterion("belong_struct_code in", values, "belongStructCode");
            return (Criteria) this;
        }

        public Criteria andBelongStructCodeNotIn(List<String> values) {
            addCriterion("belong_struct_code not in", values, "belongStructCode");
            return (Criteria) this;
        }

        public Criteria andBelongStructCodeBetween(String value1, String value2) {
            addCriterion("belong_struct_code between", value1, value2, "belongStructCode");
            return (Criteria) this;
        }

        public Criteria andBelongStructCodeNotBetween(String value1, String value2) {
            addCriterion("belong_struct_code not between", value1, value2, "belongStructCode");
            return (Criteria) this;
        }

        public Criteria andBelongStructNameIsNull() {
            addCriterion("belong_struct_name is null");
            return (Criteria) this;
        }

        public Criteria andBelongStructNameIsNotNull() {
            addCriterion("belong_struct_name is not null");
            return (Criteria) this;
        }

        public Criteria andBelongStructNameEqualTo(String value) {
            addCriterion("belong_struct_name =", value, "belongStructName");
            return (Criteria) this;
        }

        public Criteria andBelongStructNameNotEqualTo(String value) {
            addCriterion("belong_struct_name <>", value, "belongStructName");
            return (Criteria) this;
        }

        public Criteria andBelongStructNameGreaterThan(String value) {
            addCriterion("belong_struct_name >", value, "belongStructName");
            return (Criteria) this;
        }

        public Criteria andBelongStructNameGreaterThanOrEqualTo(String value) {
            addCriterion("belong_struct_name >=", value, "belongStructName");
            return (Criteria) this;
        }

        public Criteria andBelongStructNameLessThan(String value) {
            addCriterion("belong_struct_name <", value, "belongStructName");
            return (Criteria) this;
        }

        public Criteria andBelongStructNameLessThanOrEqualTo(String value) {
            addCriterion("belong_struct_name <=", value, "belongStructName");
            return (Criteria) this;
        }

        public Criteria andBelongStructNameLike(String value) {
            addCriterion("belong_struct_name like", value, "belongStructName");
            return (Criteria) this;
        }

        public Criteria andBelongStructNameNotLike(String value) {
            addCriterion("belong_struct_name not like", value, "belongStructName");
            return (Criteria) this;
        }

        public Criteria andBelongStructNameIn(List<String> values) {
            addCriterion("belong_struct_name in", values, "belongStructName");
            return (Criteria) this;
        }

        public Criteria andBelongStructNameNotIn(List<String> values) {
            addCriterion("belong_struct_name not in", values, "belongStructName");
            return (Criteria) this;
        }

        public Criteria andBelongStructNameBetween(String value1, String value2) {
            addCriterion("belong_struct_name between", value1, value2, "belongStructName");
            return (Criteria) this;
        }

        public Criteria andBelongStructNameNotBetween(String value1, String value2) {
            addCriterion("belong_struct_name not between", value1, value2, "belongStructName");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeIsNull() {
            addCriterion("belong_city_code is null");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeIsNotNull() {
            addCriterion("belong_city_code is not null");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeEqualTo(String value) {
            addCriterion("belong_city_code =", value, "belongCityCode");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeNotEqualTo(String value) {
            addCriterion("belong_city_code <>", value, "belongCityCode");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeGreaterThan(String value) {
            addCriterion("belong_city_code >", value, "belongCityCode");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeGreaterThanOrEqualTo(String value) {
            addCriterion("belong_city_code >=", value, "belongCityCode");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeLessThan(String value) {
            addCriterion("belong_city_code <", value, "belongCityCode");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeLessThanOrEqualTo(String value) {
            addCriterion("belong_city_code <=", value, "belongCityCode");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeLike(String value) {
            addCriterion("belong_city_code like", value, "belongCityCode");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeNotLike(String value) {
            addCriterion("belong_city_code not like", value, "belongCityCode");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeIn(List<String> values) {
            addCriterion("belong_city_code in", values, "belongCityCode");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeNotIn(List<String> values) {
            addCriterion("belong_city_code not in", values, "belongCityCode");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeBetween(String value1, String value2) {
            addCriterion("belong_city_code between", value1, value2, "belongCityCode");
            return (Criteria) this;
        }

        public Criteria andBelongCityCodeNotBetween(String value1, String value2) {
            addCriterion("belong_city_code not between", value1, value2, "belongCityCode");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameIsNull() {
            addCriterion("belong_city_name is null");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameIsNotNull() {
            addCriterion("belong_city_name is not null");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameEqualTo(String value) {
            addCriterion("belong_city_name =", value, "belongCityName");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameNotEqualTo(String value) {
            addCriterion("belong_city_name <>", value, "belongCityName");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameGreaterThan(String value) {
            addCriterion("belong_city_name >", value, "belongCityName");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameGreaterThanOrEqualTo(String value) {
            addCriterion("belong_city_name >=", value, "belongCityName");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameLessThan(String value) {
            addCriterion("belong_city_name <", value, "belongCityName");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameLessThanOrEqualTo(String value) {
            addCriterion("belong_city_name <=", value, "belongCityName");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameLike(String value) {
            addCriterion("belong_city_name like", value, "belongCityName");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameNotLike(String value) {
            addCriterion("belong_city_name not like", value, "belongCityName");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameIn(List<String> values) {
            addCriterion("belong_city_name in", values, "belongCityName");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameNotIn(List<String> values) {
            addCriterion("belong_city_name not in", values, "belongCityName");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameBetween(String value1, String value2) {
            addCriterion("belong_city_name between", value1, value2, "belongCityName");
            return (Criteria) this;
        }

        public Criteria andBelongCityNameNotBetween(String value1, String value2) {
            addCriterion("belong_city_name not between", value1, value2, "belongCityName");
            return (Criteria) this;
        }

        public Criteria andBillConfirmIdIsNull() {
            addCriterion("bill_confirm_id is null");
            return (Criteria) this;
        }

        public Criteria andBillConfirmIdIsNotNull() {
            addCriterion("bill_confirm_id is not null");
            return (Criteria) this;
        }

        public Criteria andBillConfirmIdEqualTo(Integer value) {
            addCriterion("bill_confirm_id =", value, "billConfirmId");
            return (Criteria) this;
        }

        public Criteria andBillConfirmIdNotEqualTo(Integer value) {
            addCriterion("bill_confirm_id <>", value, "billConfirmId");
            return (Criteria) this;
        }

        public Criteria andBillConfirmIdGreaterThan(Integer value) {
            addCriterion("bill_confirm_id >", value, "billConfirmId");
            return (Criteria) this;
        }

        public Criteria andBillConfirmIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("bill_confirm_id >=", value, "billConfirmId");
            return (Criteria) this;
        }

        public Criteria andBillConfirmIdLessThan(Integer value) {
            addCriterion("bill_confirm_id <", value, "billConfirmId");
            return (Criteria) this;
        }

        public Criteria andBillConfirmIdLessThanOrEqualTo(Integer value) {
            addCriterion("bill_confirm_id <=", value, "billConfirmId");
            return (Criteria) this;
        }

        public Criteria andBillConfirmIdIn(List<Integer> values) {
            addCriterion("bill_confirm_id in", values, "billConfirmId");
            return (Criteria) this;
        }

        public Criteria andBillConfirmIdNotIn(List<Integer> values) {
            addCriterion("bill_confirm_id not in", values, "billConfirmId");
            return (Criteria) this;
        }

        public Criteria andBillConfirmIdBetween(Integer value1, Integer value2) {
            addCriterion("bill_confirm_id between", value1, value2, "billConfirmId");
            return (Criteria) this;
        }

        public Criteria andBillConfirmIdNotBetween(Integer value1, Integer value2) {
            addCriterion("bill_confirm_id not between", value1, value2, "billConfirmId");
            return (Criteria) this;
        }

        public Criteria andBillConfirmNameIsNull() {
            addCriterion("bill_confirm_name is null");
            return (Criteria) this;
        }

        public Criteria andBillConfirmNameIsNotNull() {
            addCriterion("bill_confirm_name is not null");
            return (Criteria) this;
        }

        public Criteria andBillConfirmNameEqualTo(String value) {
            addCriterion("bill_confirm_name =", value, "billConfirmName");
            return (Criteria) this;
        }

        public Criteria andBillConfirmNameNotEqualTo(String value) {
            addCriterion("bill_confirm_name <>", value, "billConfirmName");
            return (Criteria) this;
        }

        public Criteria andBillConfirmNameGreaterThan(String value) {
            addCriterion("bill_confirm_name >", value, "billConfirmName");
            return (Criteria) this;
        }

        public Criteria andBillConfirmNameGreaterThanOrEqualTo(String value) {
            addCriterion("bill_confirm_name >=", value, "billConfirmName");
            return (Criteria) this;
        }

        public Criteria andBillConfirmNameLessThan(String value) {
            addCriterion("bill_confirm_name <", value, "billConfirmName");
            return (Criteria) this;
        }

        public Criteria andBillConfirmNameLessThanOrEqualTo(String value) {
            addCriterion("bill_confirm_name <=", value, "billConfirmName");
            return (Criteria) this;
        }

        public Criteria andBillConfirmNameLike(String value) {
            addCriterion("bill_confirm_name like", value, "billConfirmName");
            return (Criteria) this;
        }

        public Criteria andBillConfirmNameNotLike(String value) {
            addCriterion("bill_confirm_name not like", value, "billConfirmName");
            return (Criteria) this;
        }

        public Criteria andBillConfirmNameIn(List<String> values) {
            addCriterion("bill_confirm_name in", values, "billConfirmName");
            return (Criteria) this;
        }

        public Criteria andBillConfirmNameNotIn(List<String> values) {
            addCriterion("bill_confirm_name not in", values, "billConfirmName");
            return (Criteria) this;
        }

        public Criteria andBillConfirmNameBetween(String value1, String value2) {
            addCriterion("bill_confirm_name between", value1, value2, "billConfirmName");
            return (Criteria) this;
        }

        public Criteria andBillConfirmNameNotBetween(String value1, String value2) {
            addCriterion("bill_confirm_name not between", value1, value2, "billConfirmName");
            return (Criteria) this;
        }

        public Criteria andBillConfirmTimeIsNull() {
            addCriterion("bill_confirm_time is null");
            return (Criteria) this;
        }

        public Criteria andBillConfirmTimeIsNotNull() {
            addCriterion("bill_confirm_time is not null");
            return (Criteria) this;
        }

        public Criteria andBillConfirmTimeEqualTo(Date value) {
            addCriterion("bill_confirm_time =", value, "billConfirmTime");
            return (Criteria) this;
        }

        public Criteria andBillConfirmTimeNotEqualTo(Date value) {
            addCriterion("bill_confirm_time <>", value, "billConfirmTime");
            return (Criteria) this;
        }

        public Criteria andBillConfirmTimeGreaterThan(Date value) {
            addCriterion("bill_confirm_time >", value, "billConfirmTime");
            return (Criteria) this;
        }

        public Criteria andBillConfirmTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("bill_confirm_time >=", value, "billConfirmTime");
            return (Criteria) this;
        }

        public Criteria andBillConfirmTimeLessThan(Date value) {
            addCriterion("bill_confirm_time <", value, "billConfirmTime");
            return (Criteria) this;
        }

        public Criteria andBillConfirmTimeLessThanOrEqualTo(Date value) {
            addCriterion("bill_confirm_time <=", value, "billConfirmTime");
            return (Criteria) this;
        }

        public Criteria andBillConfirmTimeIn(List<Date> values) {
            addCriterion("bill_confirm_time in", values, "billConfirmTime");
            return (Criteria) this;
        }

        public Criteria andBillConfirmTimeNotIn(List<Date> values) {
            addCriterion("bill_confirm_time not in", values, "billConfirmTime");
            return (Criteria) this;
        }

        public Criteria andBillConfirmTimeBetween(Date value1, Date value2) {
            addCriterion("bill_confirm_time between", value1, value2, "billConfirmTime");
            return (Criteria) this;
        }

        public Criteria andBillConfirmTimeNotBetween(Date value1, Date value2) {
            addCriterion("bill_confirm_time not between", value1, value2, "billConfirmTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}