package com.izu.order.entity.mrcar;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class BusinessRentalOrderExample {
    /**
     * business_rental_order
     */
    protected String orderByClause;

    /**
     * business_rental_order
     */
    protected boolean distinct;

    /**
     * business_rental_order
     */
    protected List<Criteria> oredCriteria;

    public BusinessRentalOrderExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNull() {
            addCriterion("order_no is null");
            return (Criteria) this;
        }

        public Criteria andOrderNoIsNotNull() {
            addCriterion("order_no is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNoEqualTo(String value) {
            addCriterion("order_no =", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotEqualTo(String value) {
            addCriterion("order_no <>", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThan(String value) {
            addCriterion("order_no >", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoGreaterThanOrEqualTo(String value) {
            addCriterion("order_no >=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThan(String value) {
            addCriterion("order_no <", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLessThanOrEqualTo(String value) {
            addCriterion("order_no <=", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoLike(String value) {
            addCriterion("order_no like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotLike(String value) {
            addCriterion("order_no not like", value, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoIn(List<String> values) {
            addCriterion("order_no in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotIn(List<String> values) {
            addCriterion("order_no not in", values, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoBetween(String value1, String value2) {
            addCriterion("order_no between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderNoNotBetween(String value1, String value2) {
            addCriterion("order_no not between", value1, value2, "orderNo");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIsNull() {
            addCriterion("order_type is null");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIsNotNull() {
            addCriterion("order_type is not null");
            return (Criteria) this;
        }

        public Criteria andOrderTypeEqualTo(Byte value) {
            addCriterion("order_type =", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotEqualTo(Byte value) {
            addCriterion("order_type <>", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeGreaterThan(Byte value) {
            addCriterion("order_type >", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeGreaterThanOrEqualTo(Byte value) {
            addCriterion("order_type >=", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeLessThan(Byte value) {
            addCriterion("order_type <", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeLessThanOrEqualTo(Byte value) {
            addCriterion("order_type <=", value, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeIn(List<Byte> values) {
            addCriterion("order_type in", values, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotIn(List<Byte> values) {
            addCriterion("order_type not in", values, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeBetween(Byte value1, Byte value2) {
            addCriterion("order_type between", value1, value2, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderTypeNotBetween(Byte value1, Byte value2) {
            addCriterion("order_type not between", value1, value2, "orderType");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIsNull() {
            addCriterion("order_status is null");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIsNotNull() {
            addCriterion("order_status is not null");
            return (Criteria) this;
        }

        public Criteria andOrderStatusEqualTo(Byte value) {
            addCriterion("order_status =", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotEqualTo(Byte value) {
            addCriterion("order_status <>", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusGreaterThan(Byte value) {
            addCriterion("order_status >", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusGreaterThanOrEqualTo(Byte value) {
            addCriterion("order_status >=", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusLessThan(Byte value) {
            addCriterion("order_status <", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusLessThanOrEqualTo(Byte value) {
            addCriterion("order_status <=", value, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusIn(List<Byte> values) {
            addCriterion("order_status in", values, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotIn(List<Byte> values) {
            addCriterion("order_status not in", values, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusBetween(Byte value1, Byte value2) {
            addCriterion("order_status between", value1, value2, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderStatusNotBetween(Byte value1, Byte value2) {
            addCriterion("order_status not between", value1, value2, "orderStatus");
            return (Criteria) this;
        }

        public Criteria andOrderSourceIsNull() {
            addCriterion("order_source is null");
            return (Criteria) this;
        }

        public Criteria andOrderSourceIsNotNull() {
            addCriterion("order_source is not null");
            return (Criteria) this;
        }

        public Criteria andOrderSourceEqualTo(Byte value) {
            addCriterion("order_source =", value, "orderSource");
            return (Criteria) this;
        }

        public Criteria andOrderSourceNotEqualTo(Byte value) {
            addCriterion("order_source <>", value, "orderSource");
            return (Criteria) this;
        }

        public Criteria andOrderSourceGreaterThan(Byte value) {
            addCriterion("order_source >", value, "orderSource");
            return (Criteria) this;
        }

        public Criteria andOrderSourceGreaterThanOrEqualTo(Byte value) {
            addCriterion("order_source >=", value, "orderSource");
            return (Criteria) this;
        }

        public Criteria andOrderSourceLessThan(Byte value) {
            addCriterion("order_source <", value, "orderSource");
            return (Criteria) this;
        }

        public Criteria andOrderSourceLessThanOrEqualTo(Byte value) {
            addCriterion("order_source <=", value, "orderSource");
            return (Criteria) this;
        }

        public Criteria andOrderSourceIn(List<Byte> values) {
            addCriterion("order_source in", values, "orderSource");
            return (Criteria) this;
        }

        public Criteria andOrderSourceNotIn(List<Byte> values) {
            addCriterion("order_source not in", values, "orderSource");
            return (Criteria) this;
        }

        public Criteria andOrderSourceBetween(Byte value1, Byte value2) {
            addCriterion("order_source between", value1, value2, "orderSource");
            return (Criteria) this;
        }

        public Criteria andOrderSourceNotBetween(Byte value1, Byte value2) {
            addCriterion("order_source not between", value1, value2, "orderSource");
            return (Criteria) this;
        }

        public Criteria andChannelOrderCodeIsNull() {
            addCriterion("channel_order_code is null");
            return (Criteria) this;
        }

        public Criteria andChannelOrderCodeIsNotNull() {
            addCriterion("channel_order_code is not null");
            return (Criteria) this;
        }

        public Criteria andChannelOrderCodeEqualTo(String value) {
            addCriterion("channel_order_code =", value, "channelOrderCode");
            return (Criteria) this;
        }

        public Criteria andChannelOrderCodeNotEqualTo(String value) {
            addCriterion("channel_order_code <>", value, "channelOrderCode");
            return (Criteria) this;
        }

        public Criteria andChannelOrderCodeGreaterThan(String value) {
            addCriterion("channel_order_code >", value, "channelOrderCode");
            return (Criteria) this;
        }

        public Criteria andChannelOrderCodeGreaterThanOrEqualTo(String value) {
            addCriterion("channel_order_code >=", value, "channelOrderCode");
            return (Criteria) this;
        }

        public Criteria andChannelOrderCodeLessThan(String value) {
            addCriterion("channel_order_code <", value, "channelOrderCode");
            return (Criteria) this;
        }

        public Criteria andChannelOrderCodeLessThanOrEqualTo(String value) {
            addCriterion("channel_order_code <=", value, "channelOrderCode");
            return (Criteria) this;
        }

        public Criteria andChannelOrderCodeLike(String value) {
            addCriterion("channel_order_code like", value, "channelOrderCode");
            return (Criteria) this;
        }

        public Criteria andChannelOrderCodeNotLike(String value) {
            addCriterion("channel_order_code not like", value, "channelOrderCode");
            return (Criteria) this;
        }

        public Criteria andChannelOrderCodeIn(List<String> values) {
            addCriterion("channel_order_code in", values, "channelOrderCode");
            return (Criteria) this;
        }

        public Criteria andChannelOrderCodeNotIn(List<String> values) {
            addCriterion("channel_order_code not in", values, "channelOrderCode");
            return (Criteria) this;
        }

        public Criteria andChannelOrderCodeBetween(String value1, String value2) {
            addCriterion("channel_order_code between", value1, value2, "channelOrderCode");
            return (Criteria) this;
        }

        public Criteria andChannelOrderCodeNotBetween(String value1, String value2) {
            addCriterion("channel_order_code not between", value1, value2, "channelOrderCode");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(Date value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(Date value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(Date value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(Date value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(Date value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<Date> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<Date> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(Date value1, Date value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(Date value1, Date value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Integer value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Integer value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Integer value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Integer value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Integer value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Integer> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Integer> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Integer value1, Integer value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Integer value1, Integer value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNull() {
            addCriterion("customer_id is null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNotNull() {
            addCriterion("customer_id is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdEqualTo(Integer value) {
            addCriterion("customer_id =", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotEqualTo(Integer value) {
            addCriterion("customer_id <>", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThan(Integer value) {
            addCriterion("customer_id >", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("customer_id >=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThan(Integer value) {
            addCriterion("customer_id <", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThanOrEqualTo(Integer value) {
            addCriterion("customer_id <=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIn(List<Integer> values) {
            addCriterion("customer_id in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotIn(List<Integer> values) {
            addCriterion("customer_id not in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdBetween(Integer value1, Integer value2) {
            addCriterion("customer_id between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotBetween(Integer value1, Integer value2) {
            addCriterion("customer_id not between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNull() {
            addCriterion("customer_name is null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIsNotNull() {
            addCriterion("customer_name is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerNameEqualTo(String value) {
            addCriterion("customer_name =", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotEqualTo(String value) {
            addCriterion("customer_name <>", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThan(String value) {
            addCriterion("customer_name >", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameGreaterThanOrEqualTo(String value) {
            addCriterion("customer_name >=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThan(String value) {
            addCriterion("customer_name <", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLessThanOrEqualTo(String value) {
            addCriterion("customer_name <=", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameLike(String value) {
            addCriterion("customer_name like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotLike(String value) {
            addCriterion("customer_name not like", value, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameIn(List<String> values) {
            addCriterion("customer_name in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotIn(List<String> values) {
            addCriterion("customer_name not in", values, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameBetween(String value1, String value2) {
            addCriterion("customer_name between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerNameNotBetween(String value1, String value2) {
            addCriterion("customer_name not between", value1, value2, "customerName");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileIsNull() {
            addCriterion("customer_mobile is null");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileIsNotNull() {
            addCriterion("customer_mobile is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileEqualTo(String value) {
            addCriterion("customer_mobile =", value, "customerMobile");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileNotEqualTo(String value) {
            addCriterion("customer_mobile <>", value, "customerMobile");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileGreaterThan(String value) {
            addCriterion("customer_mobile >", value, "customerMobile");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileGreaterThanOrEqualTo(String value) {
            addCriterion("customer_mobile >=", value, "customerMobile");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileLessThan(String value) {
            addCriterion("customer_mobile <", value, "customerMobile");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileLessThanOrEqualTo(String value) {
            addCriterion("customer_mobile <=", value, "customerMobile");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileLike(String value) {
            addCriterion("customer_mobile like", value, "customerMobile");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileNotLike(String value) {
            addCriterion("customer_mobile not like", value, "customerMobile");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileIn(List<String> values) {
            addCriterion("customer_mobile in", values, "customerMobile");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileNotIn(List<String> values) {
            addCriterion("customer_mobile not in", values, "customerMobile");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileBetween(String value1, String value2) {
            addCriterion("customer_mobile between", value1, value2, "customerMobile");
            return (Criteria) this;
        }

        public Criteria andCustomerMobileNotBetween(String value1, String value2) {
            addCriterion("customer_mobile not between", value1, value2, "customerMobile");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerNameIsNull() {
            addCriterion("booking_passenger_name is null");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerNameIsNotNull() {
            addCriterion("booking_passenger_name is not null");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerNameEqualTo(String value) {
            addCriterion("booking_passenger_name =", value, "bookingPassengerName");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerNameNotEqualTo(String value) {
            addCriterion("booking_passenger_name <>", value, "bookingPassengerName");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerNameGreaterThan(String value) {
            addCriterion("booking_passenger_name >", value, "bookingPassengerName");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerNameGreaterThanOrEqualTo(String value) {
            addCriterion("booking_passenger_name >=", value, "bookingPassengerName");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerNameLessThan(String value) {
            addCriterion("booking_passenger_name <", value, "bookingPassengerName");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerNameLessThanOrEqualTo(String value) {
            addCriterion("booking_passenger_name <=", value, "bookingPassengerName");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerNameLike(String value) {
            addCriterion("booking_passenger_name like", value, "bookingPassengerName");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerNameNotLike(String value) {
            addCriterion("booking_passenger_name not like", value, "bookingPassengerName");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerNameIn(List<String> values) {
            addCriterion("booking_passenger_name in", values, "bookingPassengerName");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerNameNotIn(List<String> values) {
            addCriterion("booking_passenger_name not in", values, "bookingPassengerName");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerNameBetween(String value1, String value2) {
            addCriterion("booking_passenger_name between", value1, value2, "bookingPassengerName");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerNameNotBetween(String value1, String value2) {
            addCriterion("booking_passenger_name not between", value1, value2, "bookingPassengerName");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerPhoneIsNull() {
            addCriterion("booking_passenger_phone is null");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerPhoneIsNotNull() {
            addCriterion("booking_passenger_phone is not null");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerPhoneEqualTo(String value) {
            addCriterion("booking_passenger_phone =", value, "bookingPassengerPhone");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerPhoneNotEqualTo(String value) {
            addCriterion("booking_passenger_phone <>", value, "bookingPassengerPhone");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerPhoneGreaterThan(String value) {
            addCriterion("booking_passenger_phone >", value, "bookingPassengerPhone");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("booking_passenger_phone >=", value, "bookingPassengerPhone");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerPhoneLessThan(String value) {
            addCriterion("booking_passenger_phone <", value, "bookingPassengerPhone");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerPhoneLessThanOrEqualTo(String value) {
            addCriterion("booking_passenger_phone <=", value, "bookingPassengerPhone");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerPhoneLike(String value) {
            addCriterion("booking_passenger_phone like", value, "bookingPassengerPhone");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerPhoneNotLike(String value) {
            addCriterion("booking_passenger_phone not like", value, "bookingPassengerPhone");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerPhoneIn(List<String> values) {
            addCriterion("booking_passenger_phone in", values, "bookingPassengerPhone");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerPhoneNotIn(List<String> values) {
            addCriterion("booking_passenger_phone not in", values, "bookingPassengerPhone");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerPhoneBetween(String value1, String value2) {
            addCriterion("booking_passenger_phone between", value1, value2, "bookingPassengerPhone");
            return (Criteria) this;
        }

        public Criteria andBookingPassengerPhoneNotBetween(String value1, String value2) {
            addCriterion("booking_passenger_phone not between", value1, value2, "bookingPassengerPhone");
            return (Criteria) this;
        }

        public Criteria andBookingStartTimeIsNull() {
            addCriterion("booking_start_time is null");
            return (Criteria) this;
        }

        public Criteria andBookingStartTimeIsNotNull() {
            addCriterion("booking_start_time is not null");
            return (Criteria) this;
        }

        public Criteria andBookingStartTimeEqualTo(Date value) {
            addCriterion("booking_start_time =", value, "bookingStartTime");
            return (Criteria) this;
        }

        public Criteria andBookingStartTimeNotEqualTo(Date value) {
            addCriterion("booking_start_time <>", value, "bookingStartTime");
            return (Criteria) this;
        }

        public Criteria andBookingStartTimeGreaterThan(Date value) {
            addCriterion("booking_start_time >", value, "bookingStartTime");
            return (Criteria) this;
        }

        public Criteria andBookingStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("booking_start_time >=", value, "bookingStartTime");
            return (Criteria) this;
        }

        public Criteria andBookingStartTimeLessThan(Date value) {
            addCriterion("booking_start_time <", value, "bookingStartTime");
            return (Criteria) this;
        }

        public Criteria andBookingStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("booking_start_time <=", value, "bookingStartTime");
            return (Criteria) this;
        }

        public Criteria andBookingStartTimeIn(List<Date> values) {
            addCriterion("booking_start_time in", values, "bookingStartTime");
            return (Criteria) this;
        }

        public Criteria andBookingStartTimeNotIn(List<Date> values) {
            addCriterion("booking_start_time not in", values, "bookingStartTime");
            return (Criteria) this;
        }

        public Criteria andBookingStartTimeBetween(Date value1, Date value2) {
            addCriterion("booking_start_time between", value1, value2, "bookingStartTime");
            return (Criteria) this;
        }

        public Criteria andBookingStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("booking_start_time not between", value1, value2, "bookingStartTime");
            return (Criteria) this;
        }

        public Criteria andBookingEndTimeIsNull() {
            addCriterion("booking_end_time is null");
            return (Criteria) this;
        }

        public Criteria andBookingEndTimeIsNotNull() {
            addCriterion("booking_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andBookingEndTimeEqualTo(Date value) {
            addCriterion("booking_end_time =", value, "bookingEndTime");
            return (Criteria) this;
        }

        public Criteria andBookingEndTimeNotEqualTo(Date value) {
            addCriterion("booking_end_time <>", value, "bookingEndTime");
            return (Criteria) this;
        }

        public Criteria andBookingEndTimeGreaterThan(Date value) {
            addCriterion("booking_end_time >", value, "bookingEndTime");
            return (Criteria) this;
        }

        public Criteria andBookingEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("booking_end_time >=", value, "bookingEndTime");
            return (Criteria) this;
        }

        public Criteria andBookingEndTimeLessThan(Date value) {
            addCriterion("booking_end_time <", value, "bookingEndTime");
            return (Criteria) this;
        }

        public Criteria andBookingEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("booking_end_time <=", value, "bookingEndTime");
            return (Criteria) this;
        }

        public Criteria andBookingEndTimeIn(List<Date> values) {
            addCriterion("booking_end_time in", values, "bookingEndTime");
            return (Criteria) this;
        }

        public Criteria andBookingEndTimeNotIn(List<Date> values) {
            addCriterion("booking_end_time not in", values, "bookingEndTime");
            return (Criteria) this;
        }

        public Criteria andBookingEndTimeBetween(Date value1, Date value2) {
            addCriterion("booking_end_time between", value1, value2, "bookingEndTime");
            return (Criteria) this;
        }

        public Criteria andBookingEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("booking_end_time not between", value1, value2, "bookingEndTime");
            return (Criteria) this;
        }

        public Criteria andBookingStartFullAddressIsNull() {
            addCriterion("booking_start_full_address is null");
            return (Criteria) this;
        }

        public Criteria andBookingStartFullAddressIsNotNull() {
            addCriterion("booking_start_full_address is not null");
            return (Criteria) this;
        }

        public Criteria andBookingStartFullAddressEqualTo(String value) {
            addCriterion("booking_start_full_address =", value, "bookingStartFullAddress");
            return (Criteria) this;
        }

        public Criteria andBookingStartFullAddressNotEqualTo(String value) {
            addCriterion("booking_start_full_address <>", value, "bookingStartFullAddress");
            return (Criteria) this;
        }

        public Criteria andBookingStartFullAddressGreaterThan(String value) {
            addCriterion("booking_start_full_address >", value, "bookingStartFullAddress");
            return (Criteria) this;
        }

        public Criteria andBookingStartFullAddressGreaterThanOrEqualTo(String value) {
            addCriterion("booking_start_full_address >=", value, "bookingStartFullAddress");
            return (Criteria) this;
        }

        public Criteria andBookingStartFullAddressLessThan(String value) {
            addCriterion("booking_start_full_address <", value, "bookingStartFullAddress");
            return (Criteria) this;
        }

        public Criteria andBookingStartFullAddressLessThanOrEqualTo(String value) {
            addCriterion("booking_start_full_address <=", value, "bookingStartFullAddress");
            return (Criteria) this;
        }

        public Criteria andBookingStartFullAddressLike(String value) {
            addCriterion("booking_start_full_address like", value, "bookingStartFullAddress");
            return (Criteria) this;
        }

        public Criteria andBookingStartFullAddressNotLike(String value) {
            addCriterion("booking_start_full_address not like", value, "bookingStartFullAddress");
            return (Criteria) this;
        }

        public Criteria andBookingStartFullAddressIn(List<String> values) {
            addCriterion("booking_start_full_address in", values, "bookingStartFullAddress");
            return (Criteria) this;
        }

        public Criteria andBookingStartFullAddressNotIn(List<String> values) {
            addCriterion("booking_start_full_address not in", values, "bookingStartFullAddress");
            return (Criteria) this;
        }

        public Criteria andBookingStartFullAddressBetween(String value1, String value2) {
            addCriterion("booking_start_full_address between", value1, value2, "bookingStartFullAddress");
            return (Criteria) this;
        }

        public Criteria andBookingStartFullAddressNotBetween(String value1, String value2) {
            addCriterion("booking_start_full_address not between", value1, value2, "bookingStartFullAddress");
            return (Criteria) this;
        }

        public Criteria andBookingStartShortAddressIsNull() {
            addCriterion("booking_start_short_address is null");
            return (Criteria) this;
        }

        public Criteria andBookingStartShortAddressIsNotNull() {
            addCriterion("booking_start_short_address is not null");
            return (Criteria) this;
        }

        public Criteria andBookingStartShortAddressEqualTo(String value) {
            addCriterion("booking_start_short_address =", value, "bookingStartShortAddress");
            return (Criteria) this;
        }

        public Criteria andBookingStartShortAddressNotEqualTo(String value) {
            addCriterion("booking_start_short_address <>", value, "bookingStartShortAddress");
            return (Criteria) this;
        }

        public Criteria andBookingStartShortAddressGreaterThan(String value) {
            addCriterion("booking_start_short_address >", value, "bookingStartShortAddress");
            return (Criteria) this;
        }

        public Criteria andBookingStartShortAddressGreaterThanOrEqualTo(String value) {
            addCriterion("booking_start_short_address >=", value, "bookingStartShortAddress");
            return (Criteria) this;
        }

        public Criteria andBookingStartShortAddressLessThan(String value) {
            addCriterion("booking_start_short_address <", value, "bookingStartShortAddress");
            return (Criteria) this;
        }

        public Criteria andBookingStartShortAddressLessThanOrEqualTo(String value) {
            addCriterion("booking_start_short_address <=", value, "bookingStartShortAddress");
            return (Criteria) this;
        }

        public Criteria andBookingStartShortAddressLike(String value) {
            addCriterion("booking_start_short_address like", value, "bookingStartShortAddress");
            return (Criteria) this;
        }

        public Criteria andBookingStartShortAddressNotLike(String value) {
            addCriterion("booking_start_short_address not like", value, "bookingStartShortAddress");
            return (Criteria) this;
        }

        public Criteria andBookingStartShortAddressIn(List<String> values) {
            addCriterion("booking_start_short_address in", values, "bookingStartShortAddress");
            return (Criteria) this;
        }

        public Criteria andBookingStartShortAddressNotIn(List<String> values) {
            addCriterion("booking_start_short_address not in", values, "bookingStartShortAddress");
            return (Criteria) this;
        }

        public Criteria andBookingStartShortAddressBetween(String value1, String value2) {
            addCriterion("booking_start_short_address between", value1, value2, "bookingStartShortAddress");
            return (Criteria) this;
        }

        public Criteria andBookingStartShortAddressNotBetween(String value1, String value2) {
            addCriterion("booking_start_short_address not between", value1, value2, "bookingStartShortAddress");
            return (Criteria) this;
        }

        public Criteria andBookingStartLatitudeIsNull() {
            addCriterion("booking_start_latitude is null");
            return (Criteria) this;
        }

        public Criteria andBookingStartLatitudeIsNotNull() {
            addCriterion("booking_start_latitude is not null");
            return (Criteria) this;
        }

        public Criteria andBookingStartLatitudeEqualTo(BigDecimal value) {
            addCriterion("booking_start_latitude =", value, "bookingStartLatitude");
            return (Criteria) this;
        }

        public Criteria andBookingStartLatitudeNotEqualTo(BigDecimal value) {
            addCriterion("booking_start_latitude <>", value, "bookingStartLatitude");
            return (Criteria) this;
        }

        public Criteria andBookingStartLatitudeGreaterThan(BigDecimal value) {
            addCriterion("booking_start_latitude >", value, "bookingStartLatitude");
            return (Criteria) this;
        }

        public Criteria andBookingStartLatitudeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("booking_start_latitude >=", value, "bookingStartLatitude");
            return (Criteria) this;
        }

        public Criteria andBookingStartLatitudeLessThan(BigDecimal value) {
            addCriterion("booking_start_latitude <", value, "bookingStartLatitude");
            return (Criteria) this;
        }

        public Criteria andBookingStartLatitudeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("booking_start_latitude <=", value, "bookingStartLatitude");
            return (Criteria) this;
        }

        public Criteria andBookingStartLatitudeIn(List<BigDecimal> values) {
            addCriterion("booking_start_latitude in", values, "bookingStartLatitude");
            return (Criteria) this;
        }

        public Criteria andBookingStartLatitudeNotIn(List<BigDecimal> values) {
            addCriterion("booking_start_latitude not in", values, "bookingStartLatitude");
            return (Criteria) this;
        }

        public Criteria andBookingStartLatitudeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("booking_start_latitude between", value1, value2, "bookingStartLatitude");
            return (Criteria) this;
        }

        public Criteria andBookingStartLatitudeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("booking_start_latitude not between", value1, value2, "bookingStartLatitude");
            return (Criteria) this;
        }

        public Criteria andBookingStartLongitudeIsNull() {
            addCriterion("booking_start_longitude is null");
            return (Criteria) this;
        }

        public Criteria andBookingStartLongitudeIsNotNull() {
            addCriterion("booking_start_longitude is not null");
            return (Criteria) this;
        }

        public Criteria andBookingStartLongitudeEqualTo(BigDecimal value) {
            addCriterion("booking_start_longitude =", value, "bookingStartLongitude");
            return (Criteria) this;
        }

        public Criteria andBookingStartLongitudeNotEqualTo(BigDecimal value) {
            addCriterion("booking_start_longitude <>", value, "bookingStartLongitude");
            return (Criteria) this;
        }

        public Criteria andBookingStartLongitudeGreaterThan(BigDecimal value) {
            addCriterion("booking_start_longitude >", value, "bookingStartLongitude");
            return (Criteria) this;
        }

        public Criteria andBookingStartLongitudeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("booking_start_longitude >=", value, "bookingStartLongitude");
            return (Criteria) this;
        }

        public Criteria andBookingStartLongitudeLessThan(BigDecimal value) {
            addCriterion("booking_start_longitude <", value, "bookingStartLongitude");
            return (Criteria) this;
        }

        public Criteria andBookingStartLongitudeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("booking_start_longitude <=", value, "bookingStartLongitude");
            return (Criteria) this;
        }

        public Criteria andBookingStartLongitudeIn(List<BigDecimal> values) {
            addCriterion("booking_start_longitude in", values, "bookingStartLongitude");
            return (Criteria) this;
        }

        public Criteria andBookingStartLongitudeNotIn(List<BigDecimal> values) {
            addCriterion("booking_start_longitude not in", values, "bookingStartLongitude");
            return (Criteria) this;
        }

        public Criteria andBookingStartLongitudeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("booking_start_longitude between", value1, value2, "bookingStartLongitude");
            return (Criteria) this;
        }

        public Criteria andBookingStartLongitudeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("booking_start_longitude not between", value1, value2, "bookingStartLongitude");
            return (Criteria) this;
        }

        public Criteria andBookingEndFullAddressIsNull() {
            addCriterion("booking_end_full_address is null");
            return (Criteria) this;
        }

        public Criteria andBookingEndFullAddressIsNotNull() {
            addCriterion("booking_end_full_address is not null");
            return (Criteria) this;
        }

        public Criteria andBookingEndFullAddressEqualTo(String value) {
            addCriterion("booking_end_full_address =", value, "bookingEndFullAddress");
            return (Criteria) this;
        }

        public Criteria andBookingEndFullAddressNotEqualTo(String value) {
            addCriterion("booking_end_full_address <>", value, "bookingEndFullAddress");
            return (Criteria) this;
        }

        public Criteria andBookingEndFullAddressGreaterThan(String value) {
            addCriterion("booking_end_full_address >", value, "bookingEndFullAddress");
            return (Criteria) this;
        }

        public Criteria andBookingEndFullAddressGreaterThanOrEqualTo(String value) {
            addCriterion("booking_end_full_address >=", value, "bookingEndFullAddress");
            return (Criteria) this;
        }

        public Criteria andBookingEndFullAddressLessThan(String value) {
            addCriterion("booking_end_full_address <", value, "bookingEndFullAddress");
            return (Criteria) this;
        }

        public Criteria andBookingEndFullAddressLessThanOrEqualTo(String value) {
            addCriterion("booking_end_full_address <=", value, "bookingEndFullAddress");
            return (Criteria) this;
        }

        public Criteria andBookingEndFullAddressLike(String value) {
            addCriterion("booking_end_full_address like", value, "bookingEndFullAddress");
            return (Criteria) this;
        }

        public Criteria andBookingEndFullAddressNotLike(String value) {
            addCriterion("booking_end_full_address not like", value, "bookingEndFullAddress");
            return (Criteria) this;
        }

        public Criteria andBookingEndFullAddressIn(List<String> values) {
            addCriterion("booking_end_full_address in", values, "bookingEndFullAddress");
            return (Criteria) this;
        }

        public Criteria andBookingEndFullAddressNotIn(List<String> values) {
            addCriterion("booking_end_full_address not in", values, "bookingEndFullAddress");
            return (Criteria) this;
        }

        public Criteria andBookingEndFullAddressBetween(String value1, String value2) {
            addCriterion("booking_end_full_address between", value1, value2, "bookingEndFullAddress");
            return (Criteria) this;
        }

        public Criteria andBookingEndFullAddressNotBetween(String value1, String value2) {
            addCriterion("booking_end_full_address not between", value1, value2, "bookingEndFullAddress");
            return (Criteria) this;
        }

        public Criteria andBookingEndShortAddressIsNull() {
            addCriterion("booking_end_short_address is null");
            return (Criteria) this;
        }

        public Criteria andBookingEndShortAddressIsNotNull() {
            addCriterion("booking_end_short_address is not null");
            return (Criteria) this;
        }

        public Criteria andBookingEndShortAddressEqualTo(String value) {
            addCriterion("booking_end_short_address =", value, "bookingEndShortAddress");
            return (Criteria) this;
        }

        public Criteria andBookingEndShortAddressNotEqualTo(String value) {
            addCriterion("booking_end_short_address <>", value, "bookingEndShortAddress");
            return (Criteria) this;
        }

        public Criteria andBookingEndShortAddressGreaterThan(String value) {
            addCriterion("booking_end_short_address >", value, "bookingEndShortAddress");
            return (Criteria) this;
        }

        public Criteria andBookingEndShortAddressGreaterThanOrEqualTo(String value) {
            addCriterion("booking_end_short_address >=", value, "bookingEndShortAddress");
            return (Criteria) this;
        }

        public Criteria andBookingEndShortAddressLessThan(String value) {
            addCriterion("booking_end_short_address <", value, "bookingEndShortAddress");
            return (Criteria) this;
        }

        public Criteria andBookingEndShortAddressLessThanOrEqualTo(String value) {
            addCriterion("booking_end_short_address <=", value, "bookingEndShortAddress");
            return (Criteria) this;
        }

        public Criteria andBookingEndShortAddressLike(String value) {
            addCriterion("booking_end_short_address like", value, "bookingEndShortAddress");
            return (Criteria) this;
        }

        public Criteria andBookingEndShortAddressNotLike(String value) {
            addCriterion("booking_end_short_address not like", value, "bookingEndShortAddress");
            return (Criteria) this;
        }

        public Criteria andBookingEndShortAddressIn(List<String> values) {
            addCriterion("booking_end_short_address in", values, "bookingEndShortAddress");
            return (Criteria) this;
        }

        public Criteria andBookingEndShortAddressNotIn(List<String> values) {
            addCriterion("booking_end_short_address not in", values, "bookingEndShortAddress");
            return (Criteria) this;
        }

        public Criteria andBookingEndShortAddressBetween(String value1, String value2) {
            addCriterion("booking_end_short_address between", value1, value2, "bookingEndShortAddress");
            return (Criteria) this;
        }

        public Criteria andBookingEndShortAddressNotBetween(String value1, String value2) {
            addCriterion("booking_end_short_address not between", value1, value2, "bookingEndShortAddress");
            return (Criteria) this;
        }

        public Criteria andBookingEndLatitudeIsNull() {
            addCriterion("booking_end_latitude is null");
            return (Criteria) this;
        }

        public Criteria andBookingEndLatitudeIsNotNull() {
            addCriterion("booking_end_latitude is not null");
            return (Criteria) this;
        }

        public Criteria andBookingEndLatitudeEqualTo(BigDecimal value) {
            addCriterion("booking_end_latitude =", value, "bookingEndLatitude");
            return (Criteria) this;
        }

        public Criteria andBookingEndLatitudeNotEqualTo(BigDecimal value) {
            addCriterion("booking_end_latitude <>", value, "bookingEndLatitude");
            return (Criteria) this;
        }

        public Criteria andBookingEndLatitudeGreaterThan(BigDecimal value) {
            addCriterion("booking_end_latitude >", value, "bookingEndLatitude");
            return (Criteria) this;
        }

        public Criteria andBookingEndLatitudeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("booking_end_latitude >=", value, "bookingEndLatitude");
            return (Criteria) this;
        }

        public Criteria andBookingEndLatitudeLessThan(BigDecimal value) {
            addCriterion("booking_end_latitude <", value, "bookingEndLatitude");
            return (Criteria) this;
        }

        public Criteria andBookingEndLatitudeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("booking_end_latitude <=", value, "bookingEndLatitude");
            return (Criteria) this;
        }

        public Criteria andBookingEndLatitudeIn(List<BigDecimal> values) {
            addCriterion("booking_end_latitude in", values, "bookingEndLatitude");
            return (Criteria) this;
        }

        public Criteria andBookingEndLatitudeNotIn(List<BigDecimal> values) {
            addCriterion("booking_end_latitude not in", values, "bookingEndLatitude");
            return (Criteria) this;
        }

        public Criteria andBookingEndLatitudeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("booking_end_latitude between", value1, value2, "bookingEndLatitude");
            return (Criteria) this;
        }

        public Criteria andBookingEndLatitudeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("booking_end_latitude not between", value1, value2, "bookingEndLatitude");
            return (Criteria) this;
        }

        public Criteria andBookingEndLongitudeIsNull() {
            addCriterion("booking_end_longitude is null");
            return (Criteria) this;
        }

        public Criteria andBookingEndLongitudeIsNotNull() {
            addCriterion("booking_end_longitude is not null");
            return (Criteria) this;
        }

        public Criteria andBookingEndLongitudeEqualTo(BigDecimal value) {
            addCriterion("booking_end_longitude =", value, "bookingEndLongitude");
            return (Criteria) this;
        }

        public Criteria andBookingEndLongitudeNotEqualTo(BigDecimal value) {
            addCriterion("booking_end_longitude <>", value, "bookingEndLongitude");
            return (Criteria) this;
        }

        public Criteria andBookingEndLongitudeGreaterThan(BigDecimal value) {
            addCriterion("booking_end_longitude >", value, "bookingEndLongitude");
            return (Criteria) this;
        }

        public Criteria andBookingEndLongitudeGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("booking_end_longitude >=", value, "bookingEndLongitude");
            return (Criteria) this;
        }

        public Criteria andBookingEndLongitudeLessThan(BigDecimal value) {
            addCriterion("booking_end_longitude <", value, "bookingEndLongitude");
            return (Criteria) this;
        }

        public Criteria andBookingEndLongitudeLessThanOrEqualTo(BigDecimal value) {
            addCriterion("booking_end_longitude <=", value, "bookingEndLongitude");
            return (Criteria) this;
        }

        public Criteria andBookingEndLongitudeIn(List<BigDecimal> values) {
            addCriterion("booking_end_longitude in", values, "bookingEndLongitude");
            return (Criteria) this;
        }

        public Criteria andBookingEndLongitudeNotIn(List<BigDecimal> values) {
            addCriterion("booking_end_longitude not in", values, "bookingEndLongitude");
            return (Criteria) this;
        }

        public Criteria andBookingEndLongitudeBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("booking_end_longitude between", value1, value2, "bookingEndLongitude");
            return (Criteria) this;
        }

        public Criteria andBookingEndLongitudeNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("booking_end_longitude not between", value1, value2, "bookingEndLongitude");
            return (Criteria) this;
        }

        public Criteria andStartCityCodeIsNull() {
            addCriterion("start_city_code is null");
            return (Criteria) this;
        }

        public Criteria andStartCityCodeIsNotNull() {
            addCriterion("start_city_code is not null");
            return (Criteria) this;
        }

        public Criteria andStartCityCodeEqualTo(Integer value) {
            addCriterion("start_city_code =", value, "startCityCode");
            return (Criteria) this;
        }

        public Criteria andStartCityCodeNotEqualTo(Integer value) {
            addCriterion("start_city_code <>", value, "startCityCode");
            return (Criteria) this;
        }

        public Criteria andStartCityCodeGreaterThan(Integer value) {
            addCriterion("start_city_code >", value, "startCityCode");
            return (Criteria) this;
        }

        public Criteria andStartCityCodeGreaterThanOrEqualTo(Integer value) {
            addCriterion("start_city_code >=", value, "startCityCode");
            return (Criteria) this;
        }

        public Criteria andStartCityCodeLessThan(Integer value) {
            addCriterion("start_city_code <", value, "startCityCode");
            return (Criteria) this;
        }

        public Criteria andStartCityCodeLessThanOrEqualTo(Integer value) {
            addCriterion("start_city_code <=", value, "startCityCode");
            return (Criteria) this;
        }

        public Criteria andStartCityCodeIn(List<Integer> values) {
            addCriterion("start_city_code in", values, "startCityCode");
            return (Criteria) this;
        }

        public Criteria andStartCityCodeNotIn(List<Integer> values) {
            addCriterion("start_city_code not in", values, "startCityCode");
            return (Criteria) this;
        }

        public Criteria andStartCityCodeBetween(Integer value1, Integer value2) {
            addCriterion("start_city_code between", value1, value2, "startCityCode");
            return (Criteria) this;
        }

        public Criteria andStartCityCodeNotBetween(Integer value1, Integer value2) {
            addCriterion("start_city_code not between", value1, value2, "startCityCode");
            return (Criteria) this;
        }

        public Criteria andStartCityNameIsNull() {
            addCriterion("start_city_name is null");
            return (Criteria) this;
        }

        public Criteria andStartCityNameIsNotNull() {
            addCriterion("start_city_name is not null");
            return (Criteria) this;
        }

        public Criteria andStartCityNameEqualTo(String value) {
            addCriterion("start_city_name =", value, "startCityName");
            return (Criteria) this;
        }

        public Criteria andStartCityNameNotEqualTo(String value) {
            addCriterion("start_city_name <>", value, "startCityName");
            return (Criteria) this;
        }

        public Criteria andStartCityNameGreaterThan(String value) {
            addCriterion("start_city_name >", value, "startCityName");
            return (Criteria) this;
        }

        public Criteria andStartCityNameGreaterThanOrEqualTo(String value) {
            addCriterion("start_city_name >=", value, "startCityName");
            return (Criteria) this;
        }

        public Criteria andStartCityNameLessThan(String value) {
            addCriterion("start_city_name <", value, "startCityName");
            return (Criteria) this;
        }

        public Criteria andStartCityNameLessThanOrEqualTo(String value) {
            addCriterion("start_city_name <=", value, "startCityName");
            return (Criteria) this;
        }

        public Criteria andStartCityNameLike(String value) {
            addCriterion("start_city_name like", value, "startCityName");
            return (Criteria) this;
        }

        public Criteria andStartCityNameNotLike(String value) {
            addCriterion("start_city_name not like", value, "startCityName");
            return (Criteria) this;
        }

        public Criteria andStartCityNameIn(List<String> values) {
            addCriterion("start_city_name in", values, "startCityName");
            return (Criteria) this;
        }

        public Criteria andStartCityNameNotIn(List<String> values) {
            addCriterion("start_city_name not in", values, "startCityName");
            return (Criteria) this;
        }

        public Criteria andStartCityNameBetween(String value1, String value2) {
            addCriterion("start_city_name between", value1, value2, "startCityName");
            return (Criteria) this;
        }

        public Criteria andStartCityNameNotBetween(String value1, String value2) {
            addCriterion("start_city_name not between", value1, value2, "startCityName");
            return (Criteria) this;
        }

        public Criteria andEndCityCodeIsNull() {
            addCriterion("end_city_code is null");
            return (Criteria) this;
        }

        public Criteria andEndCityCodeIsNotNull() {
            addCriterion("end_city_code is not null");
            return (Criteria) this;
        }

        public Criteria andEndCityCodeEqualTo(Integer value) {
            addCriterion("end_city_code =", value, "endCityCode");
            return (Criteria) this;
        }

        public Criteria andEndCityCodeNotEqualTo(Integer value) {
            addCriterion("end_city_code <>", value, "endCityCode");
            return (Criteria) this;
        }

        public Criteria andEndCityCodeGreaterThan(Integer value) {
            addCriterion("end_city_code >", value, "endCityCode");
            return (Criteria) this;
        }

        public Criteria andEndCityCodeGreaterThanOrEqualTo(Integer value) {
            addCriterion("end_city_code >=", value, "endCityCode");
            return (Criteria) this;
        }

        public Criteria andEndCityCodeLessThan(Integer value) {
            addCriterion("end_city_code <", value, "endCityCode");
            return (Criteria) this;
        }

        public Criteria andEndCityCodeLessThanOrEqualTo(Integer value) {
            addCriterion("end_city_code <=", value, "endCityCode");
            return (Criteria) this;
        }

        public Criteria andEndCityCodeIn(List<Integer> values) {
            addCriterion("end_city_code in", values, "endCityCode");
            return (Criteria) this;
        }

        public Criteria andEndCityCodeNotIn(List<Integer> values) {
            addCriterion("end_city_code not in", values, "endCityCode");
            return (Criteria) this;
        }

        public Criteria andEndCityCodeBetween(Integer value1, Integer value2) {
            addCriterion("end_city_code between", value1, value2, "endCityCode");
            return (Criteria) this;
        }

        public Criteria andEndCityCodeNotBetween(Integer value1, Integer value2) {
            addCriterion("end_city_code not between", value1, value2, "endCityCode");
            return (Criteria) this;
        }

        public Criteria andEndCityNameIsNull() {
            addCriterion("end_city_name is null");
            return (Criteria) this;
        }

        public Criteria andEndCityNameIsNotNull() {
            addCriterion("end_city_name is not null");
            return (Criteria) this;
        }

        public Criteria andEndCityNameEqualTo(String value) {
            addCriterion("end_city_name =", value, "endCityName");
            return (Criteria) this;
        }

        public Criteria andEndCityNameNotEqualTo(String value) {
            addCriterion("end_city_name <>", value, "endCityName");
            return (Criteria) this;
        }

        public Criteria andEndCityNameGreaterThan(String value) {
            addCriterion("end_city_name >", value, "endCityName");
            return (Criteria) this;
        }

        public Criteria andEndCityNameGreaterThanOrEqualTo(String value) {
            addCriterion("end_city_name >=", value, "endCityName");
            return (Criteria) this;
        }

        public Criteria andEndCityNameLessThan(String value) {
            addCriterion("end_city_name <", value, "endCityName");
            return (Criteria) this;
        }

        public Criteria andEndCityNameLessThanOrEqualTo(String value) {
            addCriterion("end_city_name <=", value, "endCityName");
            return (Criteria) this;
        }

        public Criteria andEndCityNameLike(String value) {
            addCriterion("end_city_name like", value, "endCityName");
            return (Criteria) this;
        }

        public Criteria andEndCityNameNotLike(String value) {
            addCriterion("end_city_name not like", value, "endCityName");
            return (Criteria) this;
        }

        public Criteria andEndCityNameIn(List<String> values) {
            addCriterion("end_city_name in", values, "endCityName");
            return (Criteria) this;
        }

        public Criteria andEndCityNameNotIn(List<String> values) {
            addCriterion("end_city_name not in", values, "endCityName");
            return (Criteria) this;
        }

        public Criteria andEndCityNameBetween(String value1, String value2) {
            addCriterion("end_city_name between", value1, value2, "endCityName");
            return (Criteria) this;
        }

        public Criteria andEndCityNameNotBetween(String value1, String value2) {
            addCriterion("end_city_name not between", value1, value2, "endCityName");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNull() {
            addCriterion("deleted is null");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNotNull() {
            addCriterion("deleted is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedEqualTo(Byte value) {
            addCriterion("deleted =", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotEqualTo(Byte value) {
            addCriterion("deleted <>", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThan(Byte value) {
            addCriterion("deleted >", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThanOrEqualTo(Byte value) {
            addCriterion("deleted >=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThan(Byte value) {
            addCriterion("deleted <", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThanOrEqualTo(Byte value) {
            addCriterion("deleted <=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedIn(List<Byte> values) {
            addCriterion("deleted in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotIn(List<Byte> values) {
            addCriterion("deleted not in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedBetween(Byte value1, Byte value2) {
            addCriterion("deleted between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotBetween(Byte value1, Byte value2) {
            addCriterion("deleted not between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}