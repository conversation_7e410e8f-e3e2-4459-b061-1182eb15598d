package com.izu.order.entity.mrcar;

import java.math.BigDecimal;
import java.util.Date;

public class BusinessSupplierExpenditure {
    /**
     * 主键id id
     */
    private Long id;

    /**
     * 支出单号 expenditure_no
     */
    private String expenditureNo;

    /**
     * 供应商编码 supplier_code
     */
    private String supplierCode;

    /**
     * 供应商名称 supplier_name
     */
    private String supplierName;

    /**
     * 客户账单应收总计 should_pay_total_amount
     */
    private BigDecimal shouldPayTotalAmount;

    /**
     * 客户账单应收总计不含税 should_pay_total_amount_no_rate
     */
    private BigDecimal shouldPayTotalAmountNoRate;

    /**
     * 应支金额 supplier_amount_total
     */
    private BigDecimal supplierAmountTotal;

    /**
     * 应支金额不含税 supplier_amount_total_no_rate
     */
    private BigDecimal supplierAmountTotalNoRate;

    /**
     * 供应商税点 supplier_rate
     */
    private BigDecimal supplierRate;

    /**
     * 子订单数量 order_count
     */
    private Integer orderCount;

    /**
     * 供应商发票类型 1普票 2专票 suppler_invoice_type
     */
    private Integer supplerInvoiceType;

    /**
     * 支出单状态 0:废弃,1:已保存,2:审批中,3:审批驳回,4:已推送共享 expenditure_status
     */
    private Byte expenditureStatus;

    /**
     * 收益率 return_rate
     */
    private BigDecimal returnRate;

    /**
     * 机动车使用人编码 vehicle_user_code
     */
    private String vehicleUserCode;

    /**
     * 机动车使用人名称 vehicle_user_name
     */
    private String vehicleUserName;

    /**
     * 事项说明 item_description
     */
    private String itemDescription;

    /**
     * 审批建议 approval_suggestion
     */
    private String approvalSuggestion;

    /**
     * 创建人id creator_id
     */
    private Integer creatorId;

    /**
     * 创建人名称 creator_name
     */
    private String creatorName;

    /**
     * 创建人手机号 creator_phone
     */
    private String creatorPhone;

    /**
     * 创建人部门编码 creator_dept_code
     */
    private String creatorDeptCode;

    /**
     * 创建人部门 creator_dept_name
     */
    private String creatorDeptName;

    /**
     * 创建时间 create_time
     */
    private Date createTime;

    /**
     * 修改人id update_id
     */
    private Integer updateId;

    /**
     * 修改人名称 update_name
     */
    private String updateName;

    /**
     * 修改人电话 update_phone
     */
    private String updatePhone;

    /**
     * 修改人部门编码 update_dept_code
     */
    private String updateDeptCode;

    /**
     * 修改人部门 update_dept_name
     */
    private String updateDeptName;

    /**
     * 修改时间 update_time
     */
    private Date updateTime;

    /**
     * 推送共享返回结果 push_result
     */
    private String pushResult;

    /**
     * 主键id
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @return id 
     */
    public Long getId() {
        return id;
    }

    /**
     * 主键id
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @param id 
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 支出单号
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @return expenditure_no 
     */
    public String getExpenditureNo() {
        return expenditureNo;
    }

    /**
     * 支出单号
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @param expenditureNo 
     */
    public void setExpenditureNo(String expenditureNo) {
        this.expenditureNo = expenditureNo == null ? null : expenditureNo.trim();
    }

    /**
     * 供应商编码
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @return supplier_code 
     */
    public String getSupplierCode() {
        return supplierCode;
    }

    /**
     * 供应商编码
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @param supplierCode 
     */
    public void setSupplierCode(String supplierCode) {
        this.supplierCode = supplierCode == null ? null : supplierCode.trim();
    }

    /**
     * 供应商名称
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @return supplier_name 
     */
    public String getSupplierName() {
        return supplierName;
    }

    /**
     * 供应商名称
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @param supplierName 
     */
    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName == null ? null : supplierName.trim();
    }

    /**
     * 客户账单应收总计
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @return should_pay_total_amount 
     */
    public BigDecimal getShouldPayTotalAmount() {
        return shouldPayTotalAmount;
    }

    /**
     * 客户账单应收总计
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @param shouldPayTotalAmount 
     */
    public void setShouldPayTotalAmount(BigDecimal shouldPayTotalAmount) {
        this.shouldPayTotalAmount = shouldPayTotalAmount;
    }

    /**
     * 客户账单应收总计不含税
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @return should_pay_total_amount_no_rate 
     */
    public BigDecimal getShouldPayTotalAmountNoRate() {
        return shouldPayTotalAmountNoRate;
    }

    /**
     * 客户账单应收总计不含税
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @param shouldPayTotalAmountNoRate 
     */
    public void setShouldPayTotalAmountNoRate(BigDecimal shouldPayTotalAmountNoRate) {
        this.shouldPayTotalAmountNoRate = shouldPayTotalAmountNoRate;
    }

    /**
     * 应支金额
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @return supplier_amount_total 
     */
    public BigDecimal getSupplierAmountTotal() {
        return supplierAmountTotal;
    }

    /**
     * 应支金额
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @param supplierAmountTotal 
     */
    public void setSupplierAmountTotal(BigDecimal supplierAmountTotal) {
        this.supplierAmountTotal = supplierAmountTotal;
    }

    /**
     * 应支金额不含税
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @return supplier_amount_total_no_rate 
     */
    public BigDecimal getSupplierAmountTotalNoRate() {
        return supplierAmountTotalNoRate;
    }

    /**
     * 应支金额不含税
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @param supplierAmountTotalNoRate 
     */
    public void setSupplierAmountTotalNoRate(BigDecimal supplierAmountTotalNoRate) {
        this.supplierAmountTotalNoRate = supplierAmountTotalNoRate;
    }

    /**
     * 供应商税点
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @return supplier_rate 
     */
    public BigDecimal getSupplierRate() {
        return supplierRate;
    }

    /**
     * 供应商税点
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @param supplierRate 
     */
    public void setSupplierRate(BigDecimal supplierRate) {
        this.supplierRate = supplierRate;
    }

    /**
     * 子订单数量
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @return order_count 
     */
    public Integer getOrderCount() {
        return orderCount;
    }

    /**
     * 子订单数量
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @param orderCount 
     */
    public void setOrderCount(Integer orderCount) {
        this.orderCount = orderCount;
    }

    /**
     * 供应商发票类型 1普票 2专票
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @return suppler_invoice_type 
     */
    public Integer getSupplerInvoiceType() {
        return supplerInvoiceType;
    }

    /**
     * 供应商发票类型 1普票 2专票
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @param supplerInvoiceType 
     */
    public void setSupplerInvoiceType(Integer supplerInvoiceType) {
        this.supplerInvoiceType = supplerInvoiceType;
    }

    /**
     * 支出单状态 0:废弃,1:已保存,2:审批中,3:审批驳回,4:已推送共享
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @return expenditure_status 
     */
    public Byte getExpenditureStatus() {
        return expenditureStatus;
    }

    /**
     * 支出单状态 0:废弃,1:已保存,2:审批中,3:审批驳回,4:已推送共享
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @param expenditureStatus 
     */
    public void setExpenditureStatus(Byte expenditureStatus) {
        this.expenditureStatus = expenditureStatus;
    }

    /**
     * 收益率
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @return return_rate 
     */
    public BigDecimal getReturnRate() {
        return returnRate;
    }

    /**
     * 收益率
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @param returnRate 
     */
    public void setReturnRate(BigDecimal returnRate) {
        this.returnRate = returnRate;
    }

    /**
     * 机动车使用人编码
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @return vehicle_user_code 
     */
    public String getVehicleUserCode() {
        return vehicleUserCode;
    }

    /**
     * 机动车使用人编码
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @param vehicleUserCode 
     */
    public void setVehicleUserCode(String vehicleUserCode) {
        this.vehicleUserCode = vehicleUserCode == null ? null : vehicleUserCode.trim();
    }

    /**
     * 机动车使用人名称
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @return vehicle_user_name 
     */
    public String getVehicleUserName() {
        return vehicleUserName;
    }

    /**
     * 机动车使用人名称
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @param vehicleUserName 
     */
    public void setVehicleUserName(String vehicleUserName) {
        this.vehicleUserName = vehicleUserName == null ? null : vehicleUserName.trim();
    }

    /**
     * 事项说明
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @return item_description 
     */
    public String getItemDescription() {
        return itemDescription;
    }

    /**
     * 事项说明
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @param itemDescription 
     */
    public void setItemDescription(String itemDescription) {
        this.itemDescription = itemDescription == null ? null : itemDescription.trim();
    }

    /**
     * 审批建议
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @return approval_suggestion 
     */
    public String getApprovalSuggestion() {
        return approvalSuggestion;
    }

    /**
     * 审批建议
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @param approvalSuggestion 
     */
    public void setApprovalSuggestion(String approvalSuggestion) {
        this.approvalSuggestion = approvalSuggestion == null ? null : approvalSuggestion.trim();
    }

    /**
     * 创建人id
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @return creator_id 
     */
    public Integer getCreatorId() {
        return creatorId;
    }

    /**
     * 创建人id
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @param creatorId 
     */
    public void setCreatorId(Integer creatorId) {
        this.creatorId = creatorId;
    }

    /**
     * 创建人名称
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @return creator_name 
     */
    public String getCreatorName() {
        return creatorName;
    }

    /**
     * 创建人名称
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @param creatorName 
     */
    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName == null ? null : creatorName.trim();
    }

    /**
     * 创建人手机号
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @return creator_phone 
     */
    public String getCreatorPhone() {
        return creatorPhone;
    }

    /**
     * 创建人手机号
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @param creatorPhone 
     */
    public void setCreatorPhone(String creatorPhone) {
        this.creatorPhone = creatorPhone == null ? null : creatorPhone.trim();
    }

    /**
     * 创建人部门编码
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @return creator_dept_code 
     */
    public String getCreatorDeptCode() {
        return creatorDeptCode;
    }

    /**
     * 创建人部门编码
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @param creatorDeptCode 
     */
    public void setCreatorDeptCode(String creatorDeptCode) {
        this.creatorDeptCode = creatorDeptCode == null ? null : creatorDeptCode.trim();
    }

    /**
     * 创建人部门
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @return creator_dept_name 
     */
    public String getCreatorDeptName() {
        return creatorDeptName;
    }

    /**
     * 创建人部门
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @param creatorDeptName 
     */
    public void setCreatorDeptName(String creatorDeptName) {
        this.creatorDeptName = creatorDeptName == null ? null : creatorDeptName.trim();
    }

    /**
     * 创建时间
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @return create_time 
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 创建时间
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @param createTime 
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 修改人id
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @return update_id 
     */
    public Integer getUpdateId() {
        return updateId;
    }

    /**
     * 修改人id
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @param updateId 
     */
    public void setUpdateId(Integer updateId) {
        this.updateId = updateId;
    }

    /**
     * 修改人名称
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @return update_name 
     */
    public String getUpdateName() {
        return updateName;
    }

    /**
     * 修改人名称
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @param updateName 
     */
    public void setUpdateName(String updateName) {
        this.updateName = updateName == null ? null : updateName.trim();
    }

    /**
     * 修改人电话
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @return update_phone 
     */
    public String getUpdatePhone() {
        return updatePhone;
    }

    /**
     * 修改人电话
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @param updatePhone 
     */
    public void setUpdatePhone(String updatePhone) {
        this.updatePhone = updatePhone == null ? null : updatePhone.trim();
    }

    /**
     * 修改人部门编码
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @return update_dept_code 
     */
    public String getUpdateDeptCode() {
        return updateDeptCode;
    }

    /**
     * 修改人部门编码
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @param updateDeptCode 
     */
    public void setUpdateDeptCode(String updateDeptCode) {
        this.updateDeptCode = updateDeptCode == null ? null : updateDeptCode.trim();
    }

    /**
     * 修改人部门
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @return update_dept_name 
     */
    public String getUpdateDeptName() {
        return updateDeptName;
    }

    /**
     * 修改人部门
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @param updateDeptName 
     */
    public void setUpdateDeptName(String updateDeptName) {
        this.updateDeptName = updateDeptName == null ? null : updateDeptName.trim();
    }

    /**
     * 修改时间
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @return update_time 
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 修改时间
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @param updateTime 
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 推送共享返回结果
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @return push_result 
     */
    public String getPushResult() {
        return pushResult;
    }

    /**
     * 推送共享返回结果
     * <AUTHOR>
     * @date 2025-05-16 16:30:56
     * @param pushResult 
     */
    public void setPushResult(String pushResult) {
        this.pushResult = pushResult == null ? null : pushResult.trim();
    }
}