package com.izu.order.service.process;

import com.izu.mrcar.order.consts.AddressTypeEnum;
import com.izu.mrcar.order.consts.OrderEnum;
import com.izu.order.entity.mrcar.*;
import lombok.extern.slf4j.Slf4j;
import mapper.mrcar.ex.OrderApplyDestinationExMapper;
import mapper.mrcar.ex.OrderInfoExMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 刷数线程任务类---刷数地址明细表
 * @date 2024/4/29 9:28
 */
@Slf4j
public class ProcessOrderApplyDestinationThread implements Runnable {

    private final List<OrderApply> list;
    private final OrderApplyDestinationExMapper orderApplyDestinationExMapper;

    private final OrderInfoExMapper orderInfoExMapper;

    public ProcessOrderApplyDestinationThread(List<OrderApply> list, OrderApplyDestinationExMapper orderApplyDestinationExMapper, OrderInfoExMapper orderInfoExMapper) {
        this.list = list;
        this.orderApplyDestinationExMapper = orderApplyDestinationExMapper;
        this.orderInfoExMapper = orderInfoExMapper;
    }

    @Override
    public void run() {
        for (OrderApply orderApply : list) {
            //用车申请编号
            String orderApplyNo = orderApply.getOrderApplyNo();
            //新的订单不用做刷数
            OrderApplyDestinationExample orderApplyDestinationExampleCheck = new OrderApplyDestinationExample();
            orderApplyDestinationExampleCheck.createCriteria().andOrderApplyNoEqualTo(orderApplyNo).andTripTypeEqualTo(OrderEnum.TripType.SUB.getType());
            List<OrderApplyDestination> orderApplyDestinationsCheck = orderApplyDestinationExMapper.selectByExample(orderApplyDestinationExampleCheck);
            //有子行程类型的数据,证明是新功能产生的,不需要进行填数补充
            if (!CollectionUtils.isEmpty(orderApplyDestinationsCheck)) {
                log.info("订单编号:{} 新功能产生的订单,不需要进行刷数", orderApplyNo);
                continue;
            }

            String bookingStartPoint = orderApply.getBookingStartPoint();
            String bookingEndPoint = orderApply.getBookingEndPoint();
            //根据用车申请编号查询order_apply_destination表中的行程信息
            OrderApplyDestinationExample orderApplyDestinationExample = new OrderApplyDestinationExample();
            orderApplyDestinationExample.createCriteria().andOrderApplyNoEqualTo(orderApplyNo).andTripTypeEqualTo(OrderEnum.TripType.MAIN.getType());
            orderApplyDestinationExample.setOrderByClause("sort asc");
            List<OrderApplyDestination> orderApplyDestinations = orderApplyDestinationExMapper.selectByExample(orderApplyDestinationExample);
            //插入开始
            OrderApplyDestination orderApplyDestinationStart = new OrderApplyDestination();
            orderApplyDestinationStart.setAddressType(AddressTypeEnum.START_POINT.getType());
            orderApplyDestinationStart.setTripType(OrderEnum.TripType.MAIN.getType());
            orderApplyDestinationStart.setOrderApplyNo(orderApplyNo);
            orderApplyDestinationStart.setOrderNo("");
            orderApplyDestinationStart.setBookingEndLongAddr(orderApply.getBookingStartLongAddr());
            orderApplyDestinationStart.setBookingEndShortAddr(orderApply.getBookingStartShortAddr());
            orderApplyDestinationStart.setBookingEndPoint(orderApply.getBookingStartPoint());
            orderApplyDestinationStart.setBookingEndCityCode(orderApply.getBookingStartCityCode().toString());
            orderApplyDestinationStart.setBookingEndCityName(orderApply.getBookingStartCityName());
            orderApplyDestinationStart.setSort(0);
            orderApplyDestinationStart.setCreateTime(orderApply.getCreateTime());
            orderApplyDestinationStart.setUpdateTime(orderApply.getCreateTime());
            orderApplyDestinationStart.setEndAddressLabel(orderApply.getStartAddressLabel());
            orderApplyDestinationExMapper.insertSelective(orderApplyDestinationStart);
            //如果有数据
            int sort = 1;
            if (!CollectionUtils.isEmpty(orderApplyDestinations)) {
                //获取行程信息
                for (OrderApplyDestination orderApplyDestination : orderApplyDestinations) {
                    //去掉开始和(主行程不为空结束)的行程
                    if (bookingStartPoint.equals(orderApplyDestination.getBookingEndPoint()) || (StringUtils.isNotBlank(bookingEndPoint) && bookingEndPoint.equals(orderApplyDestination.getBookingEndPoint()))) {
                        //删除开始和结束的行程
                        orderApplyDestinationExMapper.deleteByPrimaryKey(orderApplyDestination.getId());
                        log.info("订单编号:{} 删除行程信息:{}", orderApplyNo, orderApplyDestination);
                        continue;
                    }
                    //设置为途径点
                    orderApplyDestination.setAddressType(AddressTypeEnum.WAY_POINT.getType());
                    orderApplyDestination.setSort(sort);
                    sort++;
                    orderApplyDestinationExMapper.updateByPrimaryKeySelective(orderApplyDestination);
                }

                //剩余的就是主行程的途径点，将原来的list添加开始和结束
                //插入结束 ---如果目的地存在才会进行插入
                if (StringUtils.isNotBlank(orderApply.getBookingEndPoint())) {
                    OrderApplyDestination orderApplyDestinationEnd = new OrderApplyDestination();
                    orderApplyDestinationEnd.setAddressType(AddressTypeEnum.DESTINATION_POINT.getType());
                    orderApplyDestinationEnd.setTripType(OrderEnum.TripType.MAIN.getType());
                    orderApplyDestinationEnd.setOrderApplyNo(orderApplyNo);
                    orderApplyDestinationEnd.setOrderNo("");
                    orderApplyDestinationEnd.setBookingEndLongAddr(orderApply.getBookingEndLongAddr());
                    orderApplyDestinationEnd.setBookingEndShortAddr(orderApply.getBookingEndShortAddr());
                    orderApplyDestinationEnd.setBookingEndPoint(orderApply.getBookingEndPoint());
                    orderApplyDestinationEnd.setBookingEndCityCode(orderApply.getBookingEndCityCode().toString());
                    orderApplyDestinationEnd.setBookingEndCityName(orderApply.getBookingEndCityName());
                    orderApplyDestinationEnd.setSort(sort + 1);
                    orderApplyDestinationEnd.setCreateTime(orderApply.getCreateTime());
                    orderApplyDestinationEnd.setUpdateTime(orderApply.getCreateTime());
                    orderApplyDestinationEnd.setEndAddressLabel(orderApply.getEndAddressLabel());
                    orderApplyDestinationExMapper.insertSelective(orderApplyDestinationEnd);
                }
            } else {
                //插入结束 排序为1 兼容没有途径点的情况 ---如果目的地存在才会进行插入
                if (StringUtils.isNotBlank(orderApply.getBookingEndPoint())) {
                    OrderApplyDestination orderApplyDestinationEnd = new OrderApplyDestination();
                    orderApplyDestinationEnd.setAddressType(AddressTypeEnum.DESTINATION_POINT.getType());
                    orderApplyDestinationEnd.setTripType(OrderEnum.TripType.MAIN.getType());
                    orderApplyDestinationEnd.setOrderApplyNo(orderApplyNo);
                    orderApplyDestinationEnd.setOrderNo("");
                    orderApplyDestinationEnd.setBookingEndLongAddr(orderApply.getBookingEndLongAddr());
                    orderApplyDestinationEnd.setBookingEndShortAddr(orderApply.getBookingEndShortAddr());
                    orderApplyDestinationEnd.setBookingEndPoint(orderApply.getBookingEndPoint());
                    orderApplyDestinationEnd.setBookingEndCityCode(orderApply.getBookingEndCityCode().toString());
                    orderApplyDestinationEnd.setBookingEndCityName(orderApply.getBookingEndCityName());
                    orderApplyDestinationEnd.setSort(sort);
                    orderApplyDestinationEnd.setCreateTime(orderApply.getCreateTime());
                    orderApplyDestinationEnd.setUpdateTime(orderApply.getCreateTime());
                    orderApplyDestinationEnd.setEndAddressLabel(orderApply.getEndAddressLabel());
                    orderApplyDestinationExMapper.insertSelective(orderApplyDestinationEnd);
                }
            }
            //查询有多少条order_info的数据
            OrderInfoExample orderInfoExample = new OrderInfoExample();
            orderInfoExample.createCriteria().andOrderApplyNoEqualTo(orderApplyNo);
            List<OrderInfo> orderInfos = orderInfoExMapper.selectByExample(orderInfoExample);
            if (!CollectionUtils.isEmpty(orderInfos)) {
                //查询主行程类型的行程信息
                OrderApplyDestinationExample orderApplyDestinationExampleSearch = new OrderApplyDestinationExample();
                orderApplyDestinationExampleSearch.createCriteria().andOrderApplyNoEqualTo(orderApplyNo).andTripTypeEqualTo(OrderEnum.TripType.MAIN.getType());
                List<OrderApplyDestination> orderApplyDestinationsSearchList = orderApplyDestinationExMapper.selectByExample(orderApplyDestinationExampleSearch);
                List<OrderApplyDestination> addOrderApplyDestinationList = new ArrayList<>();
                //构建子行程插入数据
                for (OrderInfo orderInfo : orderInfos) {
                    List<OrderApplyDestination> addOrderApplyDestination = orderApplyDestinationsSearchList.stream().map(orderApplyDestination -> {
                        orderApplyDestination.setId(null);
                        orderApplyDestination.setOrderNo(orderInfo.getOrderNo());
                        orderApplyDestination.setTripType(OrderEnum.TripType.SUB.getType());
                        return orderApplyDestination;
                    }).collect(Collectors.toList());
                    addOrderApplyDestinationList.addAll(addOrderApplyDestination);
                }
                //批量插入子行程类型的形成明细信息
                orderApplyDestinationExMapper.batchInsertList(addOrderApplyDestinationList);
            }
        }
        log.info("当前线程{}，执行刷新行程订单目的地表完毕", Thread.currentThread().getName());
    }
}
