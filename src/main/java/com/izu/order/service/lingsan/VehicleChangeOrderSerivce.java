package com.izu.order.service.lingsan;


import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import com.google.common.collect.Lists;
import com.izu.asset.errcode.MrCarAssetErrorCode;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.mrcar.order.consts.lingsan.*;
import com.izu.mrcar.order.dto.lingsan.supplier.input.OrderSupplierQueryDTO;
import com.izu.mrcar.order.dto.lingsan.vehicleChangeOrder.*;
import com.izu.mrcar.order.dto.lingsan.vehicleChangeOrder.req.VehicleChangeOrderReqDTO;
import com.izu.mrcar.order.dto.lingsan.vehicleChangeOrder.res.VehicleChangeOrderPageAppResDTO;
import com.izu.mrcar.order.errcode.MrCarOrderErrorCode;
import com.izu.order.entity.mrcar.OrderCustomer;
import com.izu.order.entity.mrcar.OrderSupplier;
import com.izu.order.entity.mrcar.VehicleChangeOrder;
import com.izu.order.util.DateUtil;
import com.izu.order.util.SequenceGenerator;
import com.izu.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import mapper.mrcar.ex.OrderCustomerExMapper;
import mapper.mrcar.ex.OrderSupplierExMapper;
import mapper.mrcar.ex.VehicleChangeOrderExMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Program: mrcar-order-core
 * @Description: 零散用车-换车单
 * @Author: inT
 * @Create: 2023-01-16
 */
@Slf4j
@Service
public class VehicleChangeOrderSerivce {

    @Autowired
    private VehicleChangeOrderExMapper changeOrderExMapper;

    @Autowired
    private OrderCustomerExMapper orderCustomerExMapper;

    @Autowired
    private CustomerOrderStatusService customerOrderStatusService;

    @Autowired
    private CustomerOrderSearchService customerOrderSearchService;

    @Autowired
    private OrderSupplierService orderSupplierService;

    @Autowired
    private CustomerOrderService customerOrderService;

    @Value("${env.name}")
    private String env_name;

    private static final String DEFAULT_DATE_STR = "1970-01-01 00:00:00";


    /**
     * 为完成的换车单
     * @param customerOrderNum
     * @return
     */
    public VehicleChangeOrderDTO getCurrentChangeOrder(String customerOrderNum) {
        // 当前订单已完成的最新换车单
        List<VehicleChangeOrder> vehicleChangeOrders = changeOrderExMapper.listCurrentChangeOrders(customerOrderNum, VehicleChangeOrderStatusEnum.COMPLETED.getStatus());
        // 存在换车单，取最新换车单
        if (CollectionUtils.isNotEmpty(vehicleChangeOrders)) {
            VehicleChangeOrder vehicleChangeOrder = vehicleChangeOrders.get(0);
            return BeanUtil.copyObject(vehicleChangeOrder,VehicleChangeOrderDTO.class);
        }
        return new VehicleChangeOrderDTO();
    }

    /**
     * 当前车辆和司机现状
     * @param customer
     * @return
     */
    public VehicleChangeOrderDTO getVehicleAndDriverInfoInit(OrderCustomer customer) {
        VehicleChangeOrderDTO dto = new VehicleChangeOrderDTO();
        // 取订单信息
        dto.setLevelName(customer.getLevelName());
        dto.setVehiclePriceRange(customer.getVehiclePriceRange());
        dto.setVehicleLicense(customer.getVehicleLicense());
        dto.setDriverName(customer.getDriverName());
        return dto;
    }

    /**
     * 派送中的换车单
     * @param customerOrderNum
     * @return
     */
    public VehicleChangeOrderDTO getDispatchingChangeOrder(String customerOrderNum) {
        List<Integer> status = new ArrayList<>();
        status.add(VehicleChangeOrderStatusEnum.DISPATCHING.getStatus());
        List<VehicleChangeOrder> changeingOrders = changeOrderExMapper.getChangeingOrder(customerOrderNum, status);
        if (CollectionUtils.isEmpty(changeingOrders)) {
            return null;
        }
        VehicleChangeOrderDTO vehicleChangeOrderDTO = BeanUtil.copyObject(changeingOrders.get(0), VehicleChangeOrderDTO.class);
        log.info("派送中的换车单:{}，还车类型:{}",vehicleChangeOrderDTO.getVehicleChangeOrderNum(),vehicleChangeOrderDTO.getChangeVehicleDemandType());
        return vehicleChangeOrderDTO;
    }

    /**
     * 获取进行中的换车单信息
     * @param customerOrderNum
     * @return
     */
    public VehicleChangeOrderDTO getChangeingOrder(String customerOrderNum) {
        List<Integer> status = new ArrayList<>();
        status.add(VehicleChangeOrderStatusEnum.CONFIRMATION_BEFORE_DELIVERY.getStatus());
        status.add(VehicleChangeOrderStatusEnum.WAITING_DELIVERY.getStatus());
        status.add(VehicleChangeOrderStatusEnum.DISPATCHING.getStatus());
        status.add(VehicleChangeOrderStatusEnum.TO_BE_CHANGED.getStatus());
        status.add(VehicleChangeOrderStatusEnum.DISPATCH_RETURN.getStatus());
        List<VehicleChangeOrder> changeingOrders = changeOrderExMapper.getChangeingOrder(customerOrderNum, status);
        // 首次发起换车申请，不存在历史换车单
        if (CollectionUtils.isEmpty(changeingOrders)) {
            return null;
        }
        VehicleChangeOrder changeingOrder = changeingOrders.get(0);
        VehicleChangeOrderDTO vehicleChangeOrderDTO = BeanUtil.copyObject(changeingOrder, VehicleChangeOrderDTO.class);
        log.info("订单号:{},进行中的换车单单号:{}",customerOrderNum,changeingOrder.getVehicleChangeOrderNum());
        return vehicleChangeOrderDTO;
    }



    /**
     * 换车单取消操作
     * @param dto
     * @param historyOrder
     */
    @SuppressWarnings("all")
    @Transactional(propagation = Propagation.REQUIRED,rollbackFor = Exception.class)
    public void cancelChangeOrder(VehicleChangeOrderDTO dto,VehicleChangeOrderDTO historyOrder) {
        // 更新换车单状态 换车中 -> 已取消
        VehicleChangeOrder changeOrder = changeOrderExMapper.getChangeOrderByChangeOrderNum(historyOrder.getVehicleChangeOrderNum());
        changeOrder.setUpdateId(dto.getLoginUser().getOperateUserId());
        changeOrder.setUpdateName(dto.getLoginUser().getOperateUserName());
        changeOrder.setCancelTime(new Date());
        changeOrder.setVehicleChangeOrderStatus(VehicleChangeOrderStatusEnum.ORDER_CANCELED.getStatus());
        int update = changeOrderExMapper.updateByPrimaryKeySelective(changeOrder);
        log.info("换车单取消操作结果:{},换车单号:{}",update,changeOrder.getVehicleChangeOrderNum());
        // 还原源单信息
        VehicleChangeOrder sourceChangeOrder = changeOrderExMapper.getChangeOrderByChangeOrderNum(changeOrder.getPreviousChangeOrderNum());
        if (ChangeDemandEnum.ORIGINAL_ORDER_INFORMATION.getType().equals(sourceChangeOrder.getChangeVehicleDemandType())) {
            sourceChangeOrder.setUpdateId(dto.getLoginUser().getOperateUserId());
            sourceChangeOrder.setUpdateName(dto.getLoginUser().getOperateUserName());
            sourceChangeOrder.setCompletionTime(DateUtils.getDayDate(DEFAULT_DATE_STR,DateUtils.YYYY_MM_DD_HH_mm_DD));
            sourceChangeOrder.setVehicleChangeOrderStatus(VehicleChangeOrderStatusEnum.ORDER_CANCELED.getStatus());
            int update1 = changeOrderExMapper.updateByPrimaryKeySelective(sourceChangeOrder);
            log.info("换车单_源单_取消操作结果:{},源单换车单号:{}",update1,sourceChangeOrder.getVehicleChangeOrderNum());
        }
        // 更新客户订单 换车中 -》使用中
        OrderCustomer customerOrder = customerOrderSearchService.getOrderCustomerByOrderNum(changeOrder.getCustomerOrderNum());
        RestResponse<Integer> response = customerOrderStatusService.inUse(customerOrder, dto.getLoginUser().getOperateUserId(), dto.getLoginUser().getOperateUserName());
        log.info("更新客户订单结果:{}", JSONObject.toJSONString(response));
        if (response == null || response.getCode() != 0) {
            throw new RuntimeException("客户订单更新状态失败");
        }
        // 更新供应商订单 换车中 -》使用中
        // 换车单父单
        VehicleChangeOrder order = changeOrderExMapper.getChangeOrderByChangeOrderNum(changeOrder.getPreviousChangeOrderNum());
        String[] split = order.getSupplierOrderNum().split(",");
        String supplierNums = ChangeDemandEnum.CHANGE_VEHICLE.getType().equals(changeOrder.getChangeVehicleDemandType()) ? split[0] :
                ChangeDemandEnum.CHANGE_DRIVER.getType().equals(changeOrder.getChangeVehicleDemandType()) ? split[1] :
                        ChangeDemandEnum.TRAIN_CHANGE_AND_DRIVER.getType().equals(changeOrder.getChangeVehicleDemandType()) ? order.getSupplierOrderNum() : null;
        // 新增司机，供应商车辆订单状态不变
        if (ChangeDemandEnum.ADD_DRIVER.getType().equals(changeOrder.getChangeVehicleDemandType())) {
            // 新增司机--还原订单、需求单信息
            customerOrderService.cancelChangeOrder(customerOrder,dto.getLoginUser().getOperateUserId(), dto.getLoginUser().getOperateUserName());
            return;
        }
        if (StringUtils.isBlank(supplierNums)) {
            log.info("供应商订单号不能为空");
            throw ExceptionFactory.createRestException(MrCarOrderErrorCode.AUDIT_SUPPLIER_ORDER_NUM_NULL_ERROR);
        }
        RestResponse restResponse = orderSupplierService.updateStatusBySupplierOrderNum(changeOrder.getCustomerOrderNum(),supplierNums, OrderStatusEnum.CHANGING_TRAINS_OR_DRIVERS.getStatus(),
                OrderStatusEnum.IN_USE.getStatus(), dto.getLoginUser().getOperateUserId(), dto.getLoginUser().getOperateUserName());
        if (restResponse == null || restResponse.getCode() != 0) {
            throw ExceptionFactory.createRestException(MrCarOrderErrorCode.RETURN_SAVE_ERROR);
        }
    }

    /**
     * 换车单分页列表查询
     * @param reqDTO
     * @return
     */
    @SuppressWarnings("all")
    public PageDTO listPageChangeOrders(VehicleChangeOrderReqDTO reqDTO) {
        log.info("当前环境:{}",env_name);
        if ("dev".equals(env_name)) {
            reqDTO.setLoginUser(null);
        }
        PageInfo<VehicleChangeOrderPageDTO> pageInfo = PageHelper.startPage(reqDTO.getPage(), reqDTO.getPageSize()).doSelectPageInfo(() -> changeOrderExMapper.listPageChangeOders(reqDTO));
        List<VehicleChangeOrderPageDTO> list = pageInfo.getList();
        if (CollectionUtils.isEmpty(list)) {
           return new PageDTO(pageInfo.getPageNum(), pageInfo.getPageSize(), pageInfo.getTotal(), pageInfo.getList());
        }
        list.stream().forEach(t -> {
            t.setChangeVehicleDemandTypeDesc(ChangeDemandEnum.getTypeDesc(t.getChangeVehicleDemandType()));
            t.setVehicleChangeOrderStatusDesc(VehicleChangeOrderStatusEnum.getVehicleChangeOrderStatusDesc(t.getVehicleChangeOrderStatus()));
        });
        return new PageDTO(pageInfo.getPageNum(), pageInfo.getPageSize(), pageInfo.getTotal(), pageInfo.getList());



    }


    /**
     * 换车单详情 - web
     * @param vehicleChangeOrderNum
     * @return
     */
    @SuppressWarnings("all")
    public VehicleChangeOrderDetailDTO getChangeOrderDetail(String vehicleChangeOrderNum) {
        VehicleChangeOrder order = changeOrderExMapper.getChangeOrderByChangeOrderNum(vehicleChangeOrderNum);
        if (order == null) {
            log.info("不存在的换车单单号:{}",vehicleChangeOrderNum);
            return null;
        }
        VehicleChangeOrderDetailDTO detailDTO = BeanUtil.copyObject(order, VehicleChangeOrderDetailDTO.class);

        VehicleChangeOrder history = changeOrderExMapper.getChangeOrderByChangeOrderNum(order.getPreviousChangeOrderNum());
        // 更换前车辆和司机信息
        detailDTO.setPreviousVehicleAndDriverInfos(Lists.newArrayList(getVehicleAndDriverInfo(history)));
        // 更换后车辆和司机信息
        if (!(VehicleChangeOrderStatusEnum.ORDER_CANCELED.getStatus().equals(order.getVehicleChangeOrderStatus())
                || VehicleChangeOrderStatusEnum.ORDER_CANCELED.getStatus().equals(order.getVehicleChangeOrderStatus()))) {
            detailDTO.setCurrentVehicleAndDriverInfos(Lists.newArrayList(getVehicleAndDriverInfo(order)));
        }
        detailDTO.setChangeVehicleDemandTypeDesc(ChangeDemandEnum.getTypeDesc(detailDTO.getChangeVehicleDemandType()));
        detailDTO.setVehicleChangeOrderStatusDesc(VehicleChangeOrderStatusEnum.getVehicleChangeOrderStatusDesc(detailDTO.getVehicleChangeOrderStatus()));
        detailDTO.setCreateTime(DateUtil.format(order.getCreateTime(),DateUtil.TIME_FORMAT));
        detailDTO.setExpectUseTime(DateUtil.format(order.getExpectUseTime(),DateUtil.DATE_FORMAT));
        if (order.getConfirmDeliveryTime() != null) {
            detailDTO.setConfirmDeliveryTime(DateUtil.format(order.getConfirmDeliveryTime(),DateUtil.TIME_FORMAT));
        }
        if (order.getCancelTime() != null) {
            detailDTO.setCancelTime(DateUtil.format(order.getCancelTime(),DateUtil.TIME_FORMAT));
        }

        return detailDTO;
    }

    private VehicleAndDriverInfoDTO getVehicleAndDriverInfo(VehicleChangeOrder changeOrder) {
        return BeanUtil.copyObject(changeOrder, VehicleAndDriverInfoDTO.class);
    }


    /**
     * 根据换车单号查询换车单信息
     * @param vehicleChangeOrderNum
     * @return
     */
    public VehicleChangeOrderDTO getChangeOrderByChangeOrderNum(String vehicleChangeOrderNum) {
        VehicleChangeOrder history = changeOrderExMapper.getChangeOrderByChangeOrderNum(vehicleChangeOrderNum);
        VehicleChangeOrderDTO vehicleChangeOrderDTO = BeanUtil.copyObject(history, VehicleChangeOrderDTO.class);
        return vehicleChangeOrderDTO;
    }

    /**
     * 换车单列表查询，不分页
     * @param reqDTO
     * @return
     */
    public List<VehicleChangeOrderPageDTO> listChangeOrders(VehicleChangeOrderReqDTO reqDTO) {
        if (StringUtils.isBlank(reqDTO.getCustomerOrderNum())) {
            return null;
        }
        List<VehicleChangeOrderPageDTO> dtos = changeOrderExMapper.listPageChangeOders(reqDTO);
        dtos.stream().forEach(t -> {
            t.setChangeVehicleDemandTypeDesc(ChangeDemandEnum.getTypeDesc(t.getChangeVehicleDemandType()));
            t.setVehicleChangeOrderStatusDesc(VehicleChangeOrderStatusEnum.getVehicleChangeOrderStatusDesc(t.getVehicleChangeOrderStatus()));
        });
        return dtos;
    }

    /**
     * 换车单基础信息查询
     * @param reqDTO
     * @return
     */
    public List<VehicleChangeOrderBaseDTO> listVehicleChangeOrderBaseDTOs(VehicleChangeOrderReqDTO reqDTO) {
        if (StringUtils.isBlank(reqDTO.getCustomerOrderNum())) {
            return null;
        }
        List<VehicleChangeOrderPageDTO> dtos = changeOrderExMapper.listPageChangeOders(reqDTO);
        List<VehicleChangeOrderBaseDTO> vehicleChangeOrderBaseDTOS = BeanUtil.copyList(dtos, VehicleChangeOrderBaseDTO.class);

        return vehicleChangeOrderBaseDTOS;
    }

    /**
     * 更新换车单状态
     * 换车单状态看{@link VehicleChangeOrderStatusEnum}
     * @param customerOrderNum
     * @param oldStatus
     * @param targetStatus
     * @param updateName
     * @param updateId
     * @param supplierOrderNums 供应商订单号
     * @return
     */
    public RestResponse updateVehicleChangeOrderStatus(String customerOrderNum, Integer oldStatus, Integer targetStatus,
                                                       String updateName, Integer updateId,String supplierOrderNums) {
        log.info("更新换车单状态，订单号:{},原状态:{},目标状态:{},updateName:{},supplierOrderNums:{}",
                customerOrderNum,oldStatus,targetStatus,updateName,updateId,supplierOrderNums);
        if (VehicleChangeOrderStatusEnum.getVehicleChangeOrderStatusDesc(targetStatus) == null) {
            return RestResponse.fail(-1,"不存在的目标状态");
        }
        ArrayList<Integer> status = new ArrayList<>(2);
        status.add(oldStatus);
        List<VehicleChangeOrder> changeingOrders = changeOrderExMapper.getChangeingOrder(customerOrderNum, status);
        if (CollectionUtils.isNotEmpty(changeingOrders)) {
            changeingOrders.stream().forEach(changeingOrder -> {
                if (changeingOrder != null) {
                    changeingOrder.setVehicleChangeOrderStatus(targetStatus);
                    int i = changeOrderExMapper.updateByChangeOrderNum(changeingOrder);
                    log.info("更新换车单状态成功,换车单号:{},i：{}",changeingOrder.getVehicleChangeOrderNum(),i);
                }
            });
        }
        return RestResponse.success(true);
    }

    /**
     * 使用记录查询
     * @param customerOrderNum
     * @return
     */
    @SuppressWarnings("all")
    public List<VehicleUseRecordDTO> listUseRecords(String customerOrderNum) {
        return doQuerylistUseRecords(customerOrderNum);
    }

    /**
     * 获取供应商名称
     * @param supplierNums
     * @return
     */
    private Map<Integer, String> getSupplierName(String supplierNums) {
        Map<Integer, String> hashMap = new HashMap<>(4);
        List<String> collect = Arrays.stream(supplierNums.split(",")).collect(Collectors.toList());
        List<OrderSupplier> orderSuppliers = orderSupplierService.listSupplierOrders(collect);
        Map<Integer, List<OrderSupplier>> listMap = orderSuppliers.stream().collect(Collectors.groupingBy(OrderSupplier::getOrderType));
        if (listMap == null) {
            return hashMap;
        }
        if (CollectionUtils.isNotEmpty(listMap.get(OrderSupplierEnum.SupplierOrderType.VEHICLE.getCode()))) {
            String companyName = listMap.get(OrderSupplierEnum.SupplierOrderType.VEHICLE.getCode()).get(0).getCompanyName();
            hashMap.put(OrderSupplierEnum.SupplierOrderType.VEHICLE.getCode(),companyName);
        }
        if (CollectionUtils.isNotEmpty(listMap.get(OrderSupplierEnum.SupplierOrderType.DRIVER.getCode()))) {
            String companyName = listMap.get(OrderSupplierEnum.SupplierOrderType.DRIVER.getCode()).get(0).getCompanyName();
            hashMap.put(OrderSupplierEnum.SupplierOrderType.DRIVER.getCode(),companyName);
        }
        return hashMap;
    }

    private VehicleUseRecordDTO installVehicleUseRecordDTO(boolean person ,String name, Date startTime, Date endTime, String companyName) {
        VehicleUseRecordDTO useRecordDTO = new VehicleUseRecordDTO();
        name = person ? name + " (司机)" : name;
        useRecordDTO.setVehicleOrDriver(name);
        useRecordDTO.setConfirmDeliveryTime(DateUtils.getDateFormat(startTime,DateUtils.YYYY_MM_DD_HH_mm_DD));
        if (endTime != null) {
            useRecordDTO.setReturnTime(DateUtils.getDateFormat(endTime,DateUtils.YYYY_MM_DD));
        }
        useRecordDTO.setCompanyName(companyName);
        return useRecordDTO;
    }

    public List<VehicleChangeOrder> queryCompletedVehicleChangeOrder(String customerOrderNum) {
        return changeOrderExMapper.selectByCustomerOrderNumAndStatus(customerOrderNum, VehicleChangeOrderStatusEnum.COMPLETED.getStatus());
    }

    /**
     * 换车单详情 - app
     * @param vehicleChangeOrderNum
     * @return
     */
    @SuppressWarnings("all")
    public VehicleChangeOrderDetailAppDTO getChangeOrderDetail4App(String vehicleChangeOrderNum) {
        VehicleChangeOrder order = changeOrderExMapper.getChangeOrderByChangeOrderNum(vehicleChangeOrderNum);
        if (order == null) {
            log.info("不存在的换车单单号:{}",vehicleChangeOrderNum);
            return null;
        }
        VehicleChangeOrderDetailAppDTO detailDTO = BeanUtil.copyObject(order, VehicleChangeOrderDetailAppDTO.class);
        VehicleChangeOrder history = changeOrderExMapper.getChangeOrderByChangeOrderNum(order.getPreviousChangeOrderNum());
        // 更换前车辆和司机信息
        detailDTO.setPreviousVehicleAndDriverInfo(getVehicleAndDriverInfo(history));
        // 更换后车辆和司机信息
        if (!VehicleChangeOrderStatusEnum.ORDER_CANCELED.getStatus().equals(order.getVehicleChangeOrderStatus())) {
            detailDTO.setCurrentVehicleAndDriverInfo(getVehicleAndDriverInfo(order));
        }
        // 更换前后车辆和司机信息
        detailDTO.setChangeVehicleDemandTypeDesc(ChangeDemandEnum.getTypeDesc(detailDTO.getChangeVehicleDemandType()));
        detailDTO.setVehicleChangeOrderStatusDesc(VehicleChangeOrderStatusEnum.getVehicleChangeOrderStatusDesc(detailDTO.getVehicleChangeOrderStatus()));
        detailDTO.setCreateTime(DateUtils.getDateFormat(order.getCreateTime(),DateUtil.TIME_FORMAT));
        detailDTO.setExpectUseTime(DateUtils.getDateFormat(order.getExpectUseTime(),DateUtil.DATE_FORMAT));
        if (order.getConfirmDeliveryTime() != null) {
            detailDTO.setConfirmDeliveryTime(DateUtil.format(order.getConfirmDeliveryTime(),DateUtil.TIME_FORMAT));
        }
        if (order.getCancelTime() != null) {
            detailDTO.setCancelTime(DateUtil.format(order.getCancelTime(),DateUtil.TIME_FORMAT));
        }
        return detailDTO;
    }

    /**
     * 换车单详情，换车前后信息-app
     * @param order
     * @param historyOrder
     * @return
     * @throws Exception
     */
    private List<VehicleChangeCompareDTO> getVehicleAndDriverInfo4App(VehicleChangeOrder order, VehicleChangeOrder historyOrder) {
        ArrayList<VehicleChangeCompareDTO> compareDTOS = new ArrayList<>();
        try {
            Class<? extends VehicleChangeOrder> orderClass = order.getClass();
            Class<? extends VehicleChangeOrder> historyOrderClass = historyOrder.getClass();

            for (VehicleAndDriverInfoEnum infoEnum : VehicleAndDriverInfoEnum.values()) {
                VehicleChangeCompareDTO compareDTO = new VehicleChangeCompareDTO();
                compareDTO.setKeyName(infoEnum.getTypeDesc());
                // 更新前信息
                Field declaredField = historyOrderClass.getDeclaredField(infoEnum.getAttributeName());
                declaredField.setAccessible(true);
                Object beforeValue = declaredField.get(historyOrder);
                compareDTO.setBeforeValue(beforeValue.toString());
                // 更新后信息
                declaredField = orderClass.getDeclaredField(infoEnum.getAttributeName());
                declaredField.setAccessible(true);
                Object nowValue = declaredField.get(order);
                compareDTO.setNowValue(nowValue.toString());
                compareDTOS.add(compareDTO);
            }
        } catch (Exception e) {
            log.error("换车单详情获取异常:" + e.getMessage(),e);
            throw ExceptionFactory.createRestException(MrCarOrderErrorCode.VEHICLE_QUERY_DETAIL_ERROR);
        }

        return compareDTOS;

    }

    /**
     * 根据订单号，查询最后一个换车单
     * @param customerOrderNum
     * @return
     */
    public VehicleChangeOrder getLastOneChangeOrder(String customerOrderNum) {
        if (StringUtils.isBlank(customerOrderNum)) {
            return null;
        }
        return changeOrderExMapper.getLastOneChangeOrder(customerOrderNum);
    }

    /**
     * 更新换车单信息
     * @param changeOrder
     * @return
     */
    public int updateByPrimaryKeySelective(VehicleChangeOrder changeOrder) {
        return changeOrderExMapper.updateByPrimaryKeySelective(changeOrder);
    }

    /**
     * 使用记录
     * @param customerOrderNum
     * @return
     */
    @SuppressWarnings("all")
    public List<VehicleUseRecordDTO> doQuerylistUseRecords(String customerOrderNum) {
        ArrayList<VehicleUseRecordDTO> vehicleUseRecordDTOS = new ArrayList<>();
        // 换车单记录
        ArrayList<Integer> status = new ArrayList<>();
        status.add(VehicleChangeOrderStatusEnum.COMPLETED.getStatus());
        List<VehicleChangeOrder> changeingOrders = changeOrderExMapper.getChangeingOrder(customerOrderNum, status);
        // 不存在换车单
        OrderCustomer orderCustomer = orderCustomerExMapper.selectByOrderNum(customerOrderNum);
        if (CollectionUtils.isEmpty(changeingOrders)) {
            List<OrderSupplier> orderSuppliers = orderSupplierService.listOrderSuppliers(customerOrderNum, null, null);
            Map<Integer, List<OrderSupplier>> listMap = orderSuppliers.stream().collect(Collectors.groupingBy(OrderSupplier::getOrderType));
            if (orderCustomer.getActualUseTime() != null) {
                if (StringUtils.isNotBlank(orderCustomer.getVehicleVin())) {
                    String companyName = listMap.get(OrderSupplierEnum.SupplierOrderType.VEHICLE.getCode()).stream().
                            sorted(Comparator.comparing(OrderSupplier::getOrderSupplierId)).collect(Collectors.toList()).get(0).getCompanyName();
                    VehicleUseRecordDTO vehicleUseRecordDTO = installVehicleUseRecordDTO(false,orderCustomer.getVehicleLicense(), orderCustomer.getActualUseTime(), orderCustomer.getActualReturnTime(),companyName);
                    vehicleUseRecordDTOS.add(vehicleUseRecordDTO);

                }
                if (StringUtils.isNotBlank(orderCustomer.getDriverName())) {
                    String companyName = listMap.get(OrderSupplierEnum.SupplierOrderType.DRIVER.getCode()).stream().
                            sorted(Comparator.comparing(OrderSupplier::getOrderSupplierId)).collect(Collectors.toList()).get(0).getCompanyName();
                    VehicleUseRecordDTO vehicleUseRecordDTO = installVehicleUseRecordDTO(true,orderCustomer.getDriverName(), orderCustomer.getActualUseTime(), orderCustomer.getActualReturnTime(),companyName);
                    vehicleUseRecordDTOS.add(vehicleUseRecordDTO);
                }
            }
        } else {
            // 升序
            List<String> supplierNums = changeingOrders.stream().sorted(Comparator.comparing(VehicleChangeOrder::getVehicleChangeOrderId))
                    .map(VehicleChangeOrder::getSupplierOrderNum).collect(Collectors.toList());
            List<String> supplierNumss = new ArrayList<>();
            // 存在换车单
            supplierNums.stream().forEach(s -> {
                List<String> collect = Arrays.stream(s.split(",")).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    if (StringUtils.isNotBlank(collect.get(0))
                            && !supplierNumss.contains(StringUtils.isNotBlank(collect.get(0)))) {
                        supplierNumss.add(collect.get(0));
                    }
                    if (collect.size() > 1 && StringUtils.isNotBlank(collect.get(1))
                            && !supplierNumss.contains(StringUtils.isNotBlank(collect.get(1)))) {
                        supplierNumss.add(collect.get(1));
                    }
                }
            });
            List<OrderSupplier> orderSuppliers = orderSupplierService.listSupplierOrders(supplierNumss);
            orderSuppliers.stream().forEach(orderSupplier -> {
                if (OrderSupplierEnum.SupplierOrderType.VEHICLE.getCode().equals(orderSupplier.getOrderType())) {
                    VehicleUseRecordDTO vehicleUseRecordDTO = installVehicleUseRecordDTO(false,orderSupplier.getVehicleLicense(), orderSupplier.getActualUseTime(), orderSupplier.getActualReturnTime(),orderSupplier.getCompanyName());
                    vehicleUseRecordDTOS.add(vehicleUseRecordDTO);
                }
                if (OrderSupplierEnum.SupplierOrderType.DRIVER.getCode().equals(orderSupplier.getOrderType())) {
                    VehicleUseRecordDTO vehicleUseRecordDTO = installVehicleUseRecordDTO(true,orderSupplier.getDriverName(), orderSupplier.getActualUseTime(), orderSupplier.getActualReturnTime(),orderSupplier.getCompanyName());
                    vehicleUseRecordDTOS.add(vehicleUseRecordDTO);
                }
            });
        }
        return vehicleUseRecordDTOS;
    }


}
