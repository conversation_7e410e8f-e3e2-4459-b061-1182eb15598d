package com.izu.order.service.lingsan.push;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.izu.business.dto.msgcenter.input.PushMessageInputDTO;
import com.izu.business.util.PushMessageUtil;
import com.izu.mrcar.order.consts.lingsan.AuditConstants;
import com.izu.mrcar.order.consts.lingsan.OrderSupplierEnum;
import com.izu.mrcar.order.dto.lingsan.audit.AuditBaseDTO;
import com.izu.notify.enums.MsgModelEnum;
import com.izu.order.entity.mrcar.AuditRecord;
import com.izu.order.entity.mrcar.OrderCustomer;
import com.izu.order.entity.mrcar.OrderSupplier;
import com.izu.order.service.lingsan.CustomerOrderSearchService;
import com.izu.order.service.lingsan.OrderSupplierService;
import com.izu.order.util.MessageSendUtils;
import com.izu.user.dto.order.CompanyAdminRespDTO;
import com.izu.user.restApi.OrderUserApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Program: mrcar-order-core
 * @Description: 审核消息推送
 * @Author: inT
 * @Create: 2023-02-16
 */
@Slf4j
@Service
@SuppressWarnings("all")
public class AuditMessagePushService {

    @Autowired
    private CustomerOrderSearchService customerOrderSearchService;

    @Autowired
    private OrderSupplierService orderSupplierService;

    /**
     * 用车前审核结果推送
     * @param record
     * @param auditBaseDTO
     * 审核通过
     * 订单号{order}，车牌号{carLicense}即将送达，有问题请于送车人联系,送车人姓名{DeliverName}，送车人手机号{DeliverCell}
     * 目标用户:下单人和用车人
     *
     * 审核驳回
     * 订单号{order}，车辆送达资料被驳回，请重新提交资料
     * 目标用户:供车方-管理员
     */
    @Async
    public void sendMsgPush4AuditBeforeUseVehicle(AuditRecord record, AuditBaseDTO auditBaseDTO) {
        log.info("消息推送开始...");
        if (record == null || auditBaseDTO == null) {
            return;
        }
        OrderCustomer customer = customerOrderSearchService.getOrderCustomerByOrderNum(record.getCustomerOrderNum());
        List<String> collect = Arrays.stream(record.getSupplierOrderNum().split(",")).collect(Collectors.toList());
        List<OrderSupplier> orderSuppliers = orderSupplierService.listSupplierOrders(collect);
        orderSuppliers = orderSuppliers.stream().filter(orderSupplier -> OrderSupplierEnum.SupplierOrderType.VEHICLE.getCode().equals(orderSupplier.getOrderType())).collect(Collectors.toList());
        log.info("collect：{}",collect);
        if (CollectionUtils.isEmpty(orderSuppliers)) {
            log.info("推送条件不符合，不推送");
            return;
        }
        OrderSupplier orderSupplier = orderSuppliers.get(0);
        Map<String,String> paramMap = new HashMap<>(8);// 消息体参数

        // 审核通过
        if (AuditConstants.AuditStatusEnum.APPROVED.getAuditStatus().equals(auditBaseDTO.getAuditStatus())) {
            // 用车人
            String customerPhone = customer.getCustomerPhone();
            HashSet<String> phones = Sets.newHashSet(customerPhone);
            // 下单人
            Integer companyId = customer.getCompanyId();
            CompanyAdminRespDTO companyAdmin = OrderUserApi.getCompanyAdmin(companyId);
            if (companyAdmin == null) {
                return;
            }
            String mobile = companyAdmin.getLinkerMobile();
            phones.add(mobile);
            paramMap.put(PushMessageUtil.MSG_PARAM_LINGSAN_ORDER,record.getCustomerOrderNum());
            paramMap.put(PushMessageUtil.MSG_PARAM_LINGSAN_CAR_LICENSE,orderSupplier.getVehicleLicense());// 车牌号
            paramMap.put(PushMessageUtil.MSG_PARAM_LINGSAN_DELIVER_NAME,orderSupplier.getDeliveryUseName()); // 送车人
            paramMap.put(PushMessageUtil.MSG_PARAM_LINGSAN_DELIVER_CELL,orderSupplier.getDeliveryUsePhone()); // 送车人手机号
            doSendPush(MsgModelEnum.SceneCodeEnum.MR_LING_SAN_RETURN_DATA_COMPLETED,paramMap,phones,customer.getCompanyId());
        } else {
            if (!OrderSupplierEnum.SupplierType.THIRD_PARTY.getCode().equals(orderSupplier.getCompanyType())) {
                return;
            }
            Set<String> phones = listPhonesBySupplierOrders(orderSuppliers);
            paramMap.put(PushMessageUtil.MSG_PARAM_LINGSAN_ORDER,record.getSupplierOrderNum());
            doSendPush(MsgModelEnum.SceneCodeEnum.MR_LING_SAN_REJECT_ARRIVE_DATA,paramMap,phones,customer.getCompanyId());
        }
        log.info("消息推送完成...");
    }

    /**
     * 审核驳回
     * 订单号{order}派送前资料已被驳回，请及时查看
     * 目标用户：供车方-管理员
     * @param record
     * @param auditBaseDTO
     */
    @Async
    public void sendMsgPush4AuditDeparture(AuditRecord record, AuditBaseDTO auditBaseDTO) {
        log.info("消息推送开始...");
        if (record == null || auditBaseDTO == null) {
            return;
        }
        // 审核通过
        if (AuditConstants.AuditStatusEnum.APPROVED.getAuditStatus().equals(auditBaseDTO.getAuditStatus())) {
            return;
        }
        // 供应商订单号
        List<String> supplierNums = Arrays.stream(record.getSupplierOrderNum().split(",")).filter(num -> StringUtils.isNotBlank(num)).collect(Collectors.toList());
        List<OrderSupplier> orderSuppliers = orderSupplierService.listSupplierOrders(supplierNums);
        // 获取三方订单
        List<OrderSupplier> collect = orderSuppliers.stream().filter(orderSupplier -> {
            if (OrderSupplierEnum.SupplierType.THIRD_PARTY.getCode().equals(orderSupplier.getCompanyType())) {
                return true;
            }
            return false;
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            log.info("推送条件不满足，不推送");
            return;
        }
        supplierNums = orderSuppliers.stream().map(OrderSupplier::getOrderSupplierNum).filter(num -> StringUtils.isNotBlank(num)).collect(Collectors.toList());
        String join = org.apache.commons.lang.StringUtils.join(supplierNums, ",");
        Set<String> phones = listPhonesBySupplierOrders(collect);
        Map<String,String> paramMap = new HashMap<>(2);// 消息体参数
        paramMap.put(PushMessageUtil.MSG_PARAM_LINGSAN_ORDER,join);
        doSendPush(MsgModelEnum.SceneCodeEnum.MR_LING_SAN_REJECT_DISPATCH_DATA,paramMap,phones,Integer.valueOf(collect.get(0).getCompanyCode()));
        log.info("消息推送完成...");
    }

    /**
     * 还车待确认
     * 场景：客户-还车待确认
     * 内容：订单号{order}，已还车，请确认
     * 目标客户：供车方-管理员
     */
    @Async
    public void sendMsgPush4CustomerReturnVehicle(List<OrderSupplier> orderSuppliers) {
        log.info("消息推送开始...");
        if (CollectionUtils.isEmpty(orderSuppliers)) {
            return;
        }
        // 车辆订单
        orderSuppliers = orderSuppliers.stream().filter(orderSupplier -> {
            if (OrderSupplierEnum.SupplierOrderType.VEHICLE.getCode().equals(orderSupplier.getOrderType())
                    && OrderSupplierEnum.SupplierType.THIRD_PARTY.getCode().equals(orderSupplier.getCompanyType())) {
                return true;
            }
            return false;
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderSuppliers)) {
            log.info("推送条件不满足，不推送");
            return;
        }
        Set<String> phones = listPhonesBySupplierOrders(orderSuppliers);
        Map<String,String> paramMap = new HashMap<>(2);// 消息体参数
        paramMap.put(PushMessageUtil.MSG_PARAM_LINGSAN_ORDER,orderSuppliers.get(0).getOrderSupplierNum());
        doSendPush(MsgModelEnum.SceneCodeEnum.MR_LING_SAN_RETURN_CAR,paramMap,phones,Integer.valueOf(orderSuppliers.get(0).getCompanyCode()));
        log.info("消息推送完成...");
    }

    /**
     * 场景：零散-订单已完成-还车待回传资料
     * 内容：订单号{order}，已完成，未回传资料，请确认
     * 目标客户：供车方-管理员
     *///
    @Async
    public void sendMsgPush4OrderComplete(List<OrderSupplier> orderSuppliers) {
        log.info("消息推送开始...");
        if (CollectionUtils.isEmpty(orderSuppliers)) {
            return;
        }
        List<OrderSupplier> collect = orderSuppliers.stream().filter(orderSupplier -> {
            if (OrderSupplierEnum.SupplierOrderType.VEHICLE.getCode().equals(orderSupplier.getOrderType())
                    && OrderSupplierEnum.SupplierType.THIRD_PARTY.getCode().equals(orderSupplier.getCompanyType())) {
                return true;
            }
            return false;
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            log.info("不符合推送条件，不推送");
            return;
        }
        // 给三方供应商推送消息
        Map<String,String> paramMap = new HashMap<>(2);// 消息体参数
        collect.stream().forEach(orderSupplier -> {
            CompanyAdminRespDTO companyAdmin = OrderUserApi.getCompanyAdmin(Integer.valueOf(orderSupplier.getCompanyCode()));
            if (companyAdmin != null) {
                HashSet<String> phones = Sets.newHashSet(companyAdmin.getLinkerMobile());
                paramMap.put(PushMessageUtil.MSG_PARAM_LINGSAN_ORDER,orderSupplier.getOrderSupplierNum());
                doSendPush(MsgModelEnum.SceneCodeEnum.MR_LING_SAN_REQUEST_RETURN_DATA,paramMap,phones,Integer.valueOf(orderSupplier.getCompanyCode()));
            }
        });
        log.info("消息推送完成...");
    }

    /**
     * 场景：零散-订单完成-还车资料被驳回
     * 内容：订单{order}，已完成，还车资料被驳回，请重新提交资料
     * 目标客户：供车方-管理员
     */
    @Async
    public void sendMsgPush4CompleteApprovedReject(AuditRecord auditRecord) {
        log.info("消息推送开始...");
        if (auditRecord == null) {
            return;
        }
        List<String> supplierOrderNums = Arrays.stream(auditRecord.getSupplierOrderNum().split(",")).collect(Collectors.toList());
        List<OrderSupplier> orderSuppliers = orderSupplierService.listSupplierOrders(supplierOrderNums);
        List<OrderSupplier> suppliers = orderSuppliers.stream().filter(supplierOrder -> {
            if (OrderSupplierEnum.SupplierOrderType.VEHICLE.getCode().equals(supplierOrder.getOrderType())
                    && OrderSupplierEnum.SupplierType.THIRD_PARTY.getCode().equals(supplierOrder.getCompanyType())) {
                return true;
            }
            return false;
        }).collect(Collectors.toList());
        Set<String> phones = listPhonesBySupplierOrders(suppliers);
        Map<String,String> paramMap = new HashMap<>(2);// 消息体参数
        paramMap.put(PushMessageUtil.MSG_PARAM_LINGSAN_ORDER,supplierOrderNums.get(0));
        doSendPush(MsgModelEnum.SceneCodeEnum.MR_LING_SAN_REJECT_RETURN_DATA,paramMap,phones,Integer.valueOf(suppliers.get(0).getCompanyCode()));
        log.info("消息推送结束...");
    }


    private Set<String> listPhonesBySupplierOrders(List<OrderSupplier> orderSuppliers) {
        Set<String> phones = new HashSet<>();
        if (CollectionUtils.isEmpty(orderSuppliers)) {
            return phones;
        }
        for (OrderSupplier supplier : orderSuppliers) {
            log.info("supplier-conpanyCode：{}",supplier.getCompanyCode());
            CompanyAdminRespDTO companyAdmin = OrderUserApi.getCompanyAdmin(Integer.valueOf(supplier.getCompanyCode()));
            if (companyAdmin == null) {
                continue;
            }
            log.info("supplier-conpanyCode,响应：{}",JSONObject.toJSONString(companyAdmin));
            phones.add(companyAdmin.getLinkerMobile());
        }
        return phones;

    }

    /**
     * 推送pushmsg
     * @param sceneCodeEnum
     * @param paramMap
     * @param phones
     * @param companyId
     */
    private void doSendPush(MsgModelEnum.SceneCodeEnum sceneCodeEnum, Map<String,String> paramMap, Set<String> phones, Integer companyId) {
        if (CollectionUtils.isEmpty(phones)) {
            log.info("手机号为空，不推送:{}",sceneCodeEnum);
            return;
        }
        PushMessageInputDTO pushMessageInputDTO = PushMessageInputDTO.builder()
                .sceneCodeEnum(sceneCodeEnum)
                .paramMap(paramMap)
                .scenePhoneSet(phones)
                .companyId(companyId)
                .build();
        log.info("消息推送内容:{}", JSONObject.toJSONString(pushMessageInputDTO));
        MessageSendUtils.pushMessage(pushMessageInputDTO);
    }



}
