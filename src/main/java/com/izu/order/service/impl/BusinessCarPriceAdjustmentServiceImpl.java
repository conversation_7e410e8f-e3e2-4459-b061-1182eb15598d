package com.izu.order.service.impl;

import com.alibaba.fastjson.JSON;
import com.izu.asset.dto.CarInfoDTO;
import com.izu.carasset.CarAssetRestLocator;
import com.izu.carasset.dto.output.StructAssetDTO;
import com.izu.framework.cache.MemoryCache;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.mrcar.order.DiscountTypeEnum;
import com.izu.mrcar.order.consts.*;
import com.izu.mrcar.order.dto.OrderSimpleQueryDTO;
import com.izu.mrcar.order.dto.businessOrder.BillAdjustPriceLogDTO;
import com.izu.mrcar.order.dto.mrcar.BillAttachDTO;
import com.izu.mrcar.order.dto.mrcar.FileDTO;
import com.izu.mrcar.order.dto.mrcar.OrderPriceAdjustDTO;
import com.izu.mrcar.order.dto.mrcar.OrderPriceDTO;
import com.izu.mrcar.order.dto.order.SettlementCommitReqDTO;
import com.izu.mrcar.order.dto.provider.input.BillAttachInputDTO;
import com.izu.mrcar.order.dto.provider.input.BillOrderInputDTO;
import com.izu.mrcar.order.dto.provider.input.OrderModifyInputDTO;
import com.izu.mrcar.order.errcode.MrCarOrderErrorCode;
import com.izu.order.entity.mrcar.*;
import com.izu.order.rpc.AssetRpc;
import com.izu.order.rpc.UserRpc;
import com.izu.order.service.BusinessAttachmentService;
import com.izu.order.service.BusinessCarPriceAdjustmentService;
import com.izu.order.service.OrderAmountService;
import com.izu.order.service.OrderTimeStreamService;
import com.izu.user.config.UserRestLocator;
import com.izu.user.dto.CompanyInfoDTO;
import com.izu.user.dto.company.CompanyDTO;
import com.izu.user.dto.company.CrmCustomerDTO;
import com.izu.user.restApi.UserApi;
import mapper.mrcar.BillAdjustPriceLogMapper;
import mapper.mrcar.BusinessPushOperationLogMapper;
import mapper.mrcar.ex.*;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on  2023/12/15 11:53
 */
@Service
public class BusinessCarPriceAdjustmentServiceImpl implements BusinessCarPriceAdjustmentService {

    private static final Logger logger = LoggerFactory.getLogger(BusinessCarPriceAdjustmentServiceImpl.class);

    @Autowired
    private OrderApplyExMapper orderApplyExMapper;

    @Autowired
    private OrderBusinessExtendExMapper orderBusinessExtendExMapper;

    @Autowired
    private OrderInfoExMapper orderInfoExMapper;

    @Autowired
    private BillAttachExMapper billAttachExMapper;

    @Autowired
    private BillOrderExMapper billOrderExMapper;

    @Autowired
    private BillExMapper billExMapper;

    @Autowired
    private BusinessAttachmentService businessAttachmentService;

    @Autowired
    private BillAdjustPriceLogMapper billAdjustPriceLogMapper;

    @Autowired
    private OrderTimeStreamService orderTimeStreamService;

    @Autowired
    private OrderCertificateExMapper orderCertificateExMapper;

    @Autowired
    private VehicleBaseExMapper vehicleBaseExMapper;




    @Autowired
    private OrderAmountService amountService;


    @Autowired
    private MrcarAttachmentExMapper attachmentExMapper;

    @Autowired
    private OrderAmountService orderAmountService;

    @Autowired
    private BusinessPushOperationLogMapper businessPushOperationLogMapper;


    @Override
    public OrderPriceDTO getOrderPrice(String orderApplyNo) {
        // 查询订单申请信息
        OrderApply orderApply = orderApplyExMapper.selectOneByOrderApplyNo(orderApplyNo);
        if (orderApply == null) {
            logger.warn("订单申请不存在:{},无法获取调价详情", orderApplyNo);
            throw ExceptionFactory.createRestException(MrCarOrderErrorCode.QUERY_ORDER_APPLY_NOT_EXISTS);
        }
        OrderPriceDTO priceDTO = new OrderPriceDTO();
        priceDTO.setIsRollBack(Boolean.FALSE);
        //查询当前行程单是否被退回过
        //将商务车推送综合日志状态置为已退回
        BusinessPushOperationLogExample businessPushOperationLogExample = new BusinessPushOperationLogExample();
        businessPushOperationLogExample.createCriteria().andOrderApplyNoEqualTo(orderApplyNo);
        List<BusinessPushOperationLog> businessPushOperationLogList = businessPushOperationLogMapper.selectByExample(businessPushOperationLogExample);
        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(businessPushOperationLogList)
        && businessPushOperationLogList.get(0).getPushStatus().equals(BusinessPushStatusEnum.RETURNED.getCode())){
            priceDTO.setIsRollBack(Boolean.TRUE);
        }
        //是否是一口价
        priceDTO.setFixedPriceValid(orderApply.getFixedPriceValid());
        //设置实际用车企业
        priceDTO.setCustomerCompanyId(orderApply.getCustomerCompanyId());
        priceDTO.setCustomerCompanyName(orderApply.getCustomerCompanyName());
        //预约订单开始时间
        priceDTO.setBookingOrderStime(orderApply.getBookingOrderStime());
        //预约订单结束时间
        priceDTO.setBookingOrderEtime(orderApply.getBookingOrderEtime());
        //开始地址
        priceDTO.setBookingStartLongAddr(orderApply.getBookingStartLongAddr());
        priceDTO.setBookingStartShortAddr(orderApply.getBookingStartShortAddr());

        priceDTO.setServiceCode(orderApply.getServiceCode());

        priceDTO.setServiceName(OrderEnum.OrderServiceType.getTextByValue(orderApply.getServiceCode()));

        //查询子行程信息
        List<OrderInfo> orderInfoList = orderInfoExMapper.selectCompletedOrders(orderApplyNo);
        List<OrderPriceAdjustDTO> orderPriceAdjustDTOS = orderInfoList.stream()
                .map(order -> createOrderPriceDTO(order, orderApply.getFixedPriceValid()))
                .collect(Collectors.toList());
        priceDTO.setSubOrderInfoList(orderPriceAdjustDTOS);

        Bill bill = billExMapper.selectByOrderApplyNo(orderApplyNo);
        if (Boolean.TRUE.equals(orderApply.getFixedPriceValid())) {
            priceDTO.setTotalAmount(getBigDecimal(bill.getFixedPrice()));
        } else {
            priceDTO.setTotalAmount(getBigDecimal(bill.getTotalAmount()));
        }
        priceDTO.setShouldpayAmount(getBigDecimal(bill.getShouldpayAmount()));
        BigDecimal totalReduction = orderPriceAdjustDTOS.stream()
                .map(a -> a.getReductionAmount().add(a.getDeductionAmount()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        priceDTO.setReductionAmount(totalReduction);
        priceDTO.setOtherAmount(getBigDecimal(bill.getOtherAmount()));
        List<FileDTO> modifyFiles = businessAttachmentService.getFileInfos(bill.getBillNo(), AttachmentTypeEnum.ORDER_PRICE);
        priceDTO.setModifyFiles(modifyFiles);
        priceDTO.setBusinessBeforeSettlementRespDTO(orderAmountService.getInfoBeforeSettlement(orderApplyNo));
        //查询账期相关字段
        OrderApply orderApply1 = orderApplyExMapper.selectByPrimaryKey(orderApply.getId());
        priceDTO.getBusinessBeforeSettlementRespDTO().setLastSelectedSettleDays(orderApply1.getSettleDays());
        priceDTO.getBusinessBeforeSettlementRespDTO().setLastSelectedSettleCompanyCode(orderApply1.getSettleCompanyCode());
        priceDTO.getBusinessBeforeSettlementRespDTO().setLastSelectedSettleCompanyName(orderApply1.getSettleCompanyName());
        priceDTO.getBusinessBeforeSettlementRespDTO().setLastSelectedSettleTax(orderApply1.getSettleTax());
        priceDTO.getBusinessBeforeSettlementRespDTO().setLastSelectedSettleTaxStr(formatTaxRate(orderApply1.getSettleTax()));
        return priceDTO;
    }
    public static String formatTaxRate(BigDecimal rate) {
        // 处理 null 值
        if (rate == null) {
            return null;
        }

        // 处理 0 值
        if (BigDecimal.ZERO.compareTo(rate) == 0) {
            return "0%";
        }

        // 乘以100并转换为百分比字符串
        BigDecimal percentage = rate.multiply(new BigDecimal("100"));

        // 去除末尾无意义的0和小数点（如5.00变为5，0.50变为0.5）
        percentage = percentage.stripTrailingZeros();

        // 如果小数部分为0，则显示整数形式
        if (percentage.scale() <= 0) {
            return percentage.toPlainString() + "%";
        }

        // 否则保留原有小数位数（最多4位）
        return percentage.setScale(Math.min(percentage.scale(), 4), RoundingMode.HALF_UP).toPlainString() + "%";
    }

    /**
     * 预处理一下参数
     */
    private void preHandleParam(OrderModifyInputDTO orderModifyInputDTO) {
        //基本费、超时费、超里程费、抵扣金额和减免金额，后台无法保存成功
        List<BillOrderInputDTO> subModify = orderModifyInputDTO.getSubModify();
        subModify.forEach(o -> {
            o.setFixedPrice(Optional.ofNullable(o.getFixedPrice()).orElse(BigDecimal.ZERO));
            o.setTotalValue(Optional.ofNullable(o.getTotalValue()).orElse(BigDecimal.ZERO));
            o.setBaseFee(Optional.ofNullable(o.getBaseFee()).orElse(BigDecimal.ZERO));
            o.setOverTimeFee(Optional.ofNullable(o.getOverTimeFee()).orElse(BigDecimal.ZERO));
            o.setOverMileageFee(Optional.ofNullable(o.getOverMileageFee()).orElse(BigDecimal.ZERO));
            o.setDeductionAmount(Optional.ofNullable(o.getDeductionAmount()).orElse(BigDecimal.ZERO));
            o.setReductionAmount(Optional.ofNullable(o.getReductionAmount()).orElse(BigDecimal.ZERO));
            o.setNightServiceFee(Optional.ofNullable(o.getNightServiceFee()).orElse(BigDecimal.ZERO));
        });
    }


    @Override
    @Transactional
    public void adjustPrice(OrderModifyInputDTO orderModifyInputDTO) {
        //如果是调价并推送,综合无此客户,直接报错
        if (orderModifyInputDTO.getIsSettlement()) {
            CompanyDTO company = UserRpc.getCompanyById(orderModifyInputDTO.getCustomerCompanyId());
            if (Objects.isNull(company)) {
                throw ExceptionFactory.createRestException(MrCarOrderErrorCode.NO_COMPANY);
            }
            CrmCustomerDTO crmCompanyByCode = UserRpc.getCrmCompanyByCode(company.getCustomerCode());
            if (Objects.isNull(crmCompanyByCode)) {
                throw ExceptionFactory.createRestException(MrCarOrderErrorCode.CRM_NO_COMPANY);
            }
        }

        //基本费、超时费、超里程费、抵扣金额和减免金额，后台无法保存成功
        String orderApplyNo = orderModifyInputDTO.getOrderApplyNo();
        OrderApply orderApply = orderApplyExMapper.selectOneByOrderApplyNo(orderApplyNo);
        Short orderStatus = orderApply.getOrderStatus();
        if (checkAdjustPrice(orderApply)) {
            logger.warn("订单调价：当前订单申请状态未处于待确认、已驳回、待结算。不能进行调价.orderApplyNo={},orderStatus={}",
                    orderApplyNo, orderStatus);
            throw ExceptionFactory.createRestException(MrCarOrderErrorCode.CAN_NOT_PRICE_ORDER_STATUS);
        }
        //预处理一下参数
        preHandleParam(orderModifyInputDTO);
        //校验前端传入的金额
        checkAmount(orderModifyInputDTO, orderApply.getFixedPriceValid());
        //校验一下调价并结算情况下，结算的参数是否齐全
        checkSettleParams(orderModifyInputDTO);
        //校验并设置机动车所有人
        checkCarOwner(orderModifyInputDTO);
        //校验权益卡
        checkBenefit(orderApply, orderModifyInputDTO);


        List<BillOrderInputDTO> subModify = orderModifyInputDTO.getSubModify();
        if (orderApply.getFixedPriceValid()) {
            for (BillOrderInputDTO billOrderInputDTO : subModify) {
                updateSubBillForFixed(billOrderInputDTO,orderModifyInputDTO);
                updateCarOwner(billOrderInputDTO);
            }
        } else {
            for (BillOrderInputDTO billOrderInputDTO : subModify) {
                updateSubBill(billOrderInputDTO, orderModifyInputDTO);
                updateCarOwner(billOrderInputDTO);
            }
        }
        Bill bill = billExMapper.selectByOrderApplyNo(orderApplyNo);
        Long billId = bill.getId();
        fillBill(orderModifyInputDTO, billId, orderApply.getFixedPriceValid());
        updateOrderApply(orderApply,orderModifyInputDTO);
        //插入附件信息
        if (!CollectionUtils.isEmpty(orderModifyInputDTO.getFileList())) {
            businessAttachmentService.saveAttachment(Math.toIntExact(billId), bill.getBillNo(),
                    AttachmentTypeEnum.ORDER_PRICE, orderModifyInputDTO.getFileList());
        }
        //调价并结算，执行结算逻辑
        if(orderModifyInputDTO.getIsSettlement()){
            SettlementCommitReqDTO settlementCommitReqDTO = BeanUtil.copyObject(orderModifyInputDTO, SettlementCommitReqDTO.class);
            //调价页面已经改过了，无需在推送账单里面，再次更新机动车使用人的字段。
            settlementCommitReqDTO.setOrderVehicleOperatorList(new ArrayList<>());
            RestResponse restResponse = amountService.orderSettlementV2(settlementCommitReqDTO);
            if(!restResponse.isSuccess()){
                //结算失败，抛出异常回滚事务
                logger.error("调价成功但是结算失败，失败原因：{}", JSON.toJSONString(restResponse));
                throw ExceptionFactory.createRestException(restResponse.getCode());
            }
        }
        //更新结算信息
        updateSettleInfo(orderModifyInputDTO, orderApply, orderApplyNo);

    }

    private void updateSettleInfo(OrderModifyInputDTO orderModifyInputDTO, OrderApply orderApply, String orderApplyNo) {
        OrderApply order = new OrderApply();
        order.setId(orderApply.getId());
        order.setSettleDays(orderModifyInputDTO.getSettleDays());
        order.setSettleCompanyCode(orderModifyInputDTO.getSettleCompanyCode());
        order.setSettleCompanyName(orderModifyInputDTO.getSettleCompanyName());
        order.setSettleTax(orderModifyInputDTO.getSettleTax());
        // 更新订单状态
        orderApplyExMapper.updateByPrimaryKeySelective(order);

        //删除附件
        List<MrcarAttachment> attachmentList = attachmentExMapper.findByApplyId(orderApply.getId(), AttachmentTypeEnum.ORDER_SETTLE.value());
        if (!CollectionUtils.isEmpty(attachmentList)) {
            attachmentList.stream().forEach(attachment ->
                    attachmentExMapper.deleteByPrimaryKey(attachment.getId())
            );
        }

        // 插入合同附件
        List<MrcarAttachment> attachments = orderModifyInputDTO.getSettleFiles().stream().map(fileDTO -> {
            MrcarAttachment attachment = new MrcarAttachment();
            attachment.setBusId(orderApply.getId());
            attachment.setBusNo(orderApplyNo);
            attachment.setBusType(AttachmentTypeEnum.ORDER_SETTLE.value());
            attachment.setFileName(fileDTO.getFileName());
            attachment.setFileRealName(fileDTO.getFileName());
            attachment.setFileUrl(fileDTO.getFileUrl());
            return attachment;
        }).collect(Collectors.toList());

        attachmentExMapper.batchInsert(attachments);
    }

    @Override
    public List<BillAdjustPriceLogDTO> queryBillAdjustPriceLogList(OrderSimpleQueryDTO orderSimpleQueryDTO) {
        //根据子行程编码获取调价记录
        BillAdjustPriceLogExample billAdjustPriceLogExample = new BillAdjustPriceLogExample();
        billAdjustPriceLogExample.createCriteria()
                .andOrderNoEqualTo(orderSimpleQueryDTO.getOrderNo());
        List<BillAdjustPriceLog> billAdjustPriceLogs = billAdjustPriceLogMapper.selectByExample(billAdjustPriceLogExample);
        return BeanUtil.copyList(billAdjustPriceLogs, BillAdjustPriceLogDTO.class);
    }

    //校验一下机动车所有人
    private void checkCarOwner(OrderModifyInputDTO orderModifyInputDTO) {
        List<BillOrderInputDTO> subModify = orderModifyInputDTO.getSubModify();
        for (BillOrderInputDTO billOrderInputDTO : subModify) {
            OrderBusinessExtend orderBusinessExtendFromCache = getOrderBusinessExtendFromCache(billOrderInputDTO.getOrderNo());
            Byte vehicleProvider = orderBusinessExtendFromCache.getVehicleProvider();
            //如果都是三方服务，机动车所有人=机动车使用人
            if (vehicleProvider == VehicleProviderEnum.THIRD_PARTY.getValue()) {
                billOrderInputDTO.setBelongBussCode(billOrderInputDTO.getOperateBussCode());
                billOrderInputDTO.setBelongBussName(billOrderInputDTO.getOperateBussName());
            } else {
                //首汽车辆取车辆上的所有人编码
                //查询订单
                OrderInfo orderInfo = orderInfoExMapper.selectOneByOrderNo(billOrderInputDTO.getOrderNo());
                Long assignCarId = orderInfo.getAssignCarId();
                if(assignCarId!=null){
                    //查车辆
                    CarInfoDTO carInfoById = AssetRpc.getCarInfoById(assignCarId);
                    if(carInfoById!=null){
                        billOrderInputDTO.setBelongBussCode(StringUtils.defaultString(carInfoById.getBelongBussCode()));
                        billOrderInputDTO.setBelongBussName(StringUtils.defaultString(carInfoById.getBelongBussName()));
                    }
                }

            }
        }
    }

    private void checkBenefit(OrderApply orderApply, OrderModifyInputDTO orderModifyInputDTO) {
        List<BillOrderInputDTO> subModify = orderModifyInputDTO.getSubModify();
        for (BillOrderInputDTO billOrderInputDTO : subModify) {
            OrderBusinessExtend orderBusinessExtendFromCache = getOrderBusinessExtendFromCache(billOrderInputDTO.getOrderNo());
            if (orderBusinessExtendFromCache.getBenefitCard() && (!Objects.equals(orderApply.getCustomerCompanyId(), orderModifyInputDTO.getCustomerCompanyId())
                    || !Objects.equals(orderApply.getBookingOrderStime(), orderModifyInputDTO.getBookingOrderStime())
                    || !Objects.equals(orderApply.getBookingOrderEtime(), orderModifyInputDTO.getBookingOrderEtime()))) {
                throw ExceptionFactory.createRestException(MrCarOrderErrorCode.CAN_NOT_UPDATE_CUSTOMER_COMPANY);
            }
        }
    }

    private void checkSettleParams(OrderModifyInputDTO orderModifyInputDTO) {
//        if (!orderModifyInputDTO.getIsSettlement()) {
//            //非调价并结算场景不需要下面的校验
//            return;
//        }
        //账期、签约主体、税率、合同附件不能为空
        if (orderModifyInputDTO.getSettleDays() == null) {
            throw ExceptionFactory.createRestException(MrCarOrderErrorCode.PARAMETER_REQUIRED, "账期");
        }
        if (StringUtils.isBlank(orderModifyInputDTO.getSettleCompanyCode())) {
            throw ExceptionFactory.createRestException(MrCarOrderErrorCode.PARAMETER_REQUIRED, "签约主体编码");
        }
        if (StringUtils.isBlank(orderModifyInputDTO.getSettleCompanyName())) {
            throw ExceptionFactory.createRestException(MrCarOrderErrorCode.PARAMETER_REQUIRED, "签约主体名称");
        }
        if (orderModifyInputDTO.getSettleTax() == null) {
            throw ExceptionFactory.createRestException(MrCarOrderErrorCode.PARAMETER_REQUIRED, "税率");
        }
        if (CollectionUtils.isEmpty(orderModifyInputDTO.getSettleFiles())) {
            throw ExceptionFactory.createRestException(MrCarOrderErrorCode.PARAMETER_REQUIRED, "结算合同附件");
        }
    }


    private boolean checkAdjustPrice(OrderApply orderApply) {
        // 订单状态为：订单待确认、已驳回、待结算，可进行调价
        Short orderStatus = orderApply.getOrderStatus();
        return orderStatus != OrderEnum.OrderApplyStatus.WAIT_CONFIRM.value()
                && orderStatus != OrderEnum.OrderApplyStatus.REJECT.value()
                && orderStatus != OrderEnum.OrderApplyStatus.WAIT_ACCOUNT.value();
    }

    private void checkAmount(OrderModifyInputDTO orderModifyInputDTO, Boolean fixedPriceValid) {
        List<BillOrderInputDTO> subModify = orderModifyInputDTO.getSubModify();
        //当前用的减免比率
        int discountProportion = UserApi.getDiscountProportion(orderModifyInputDTO.getLoginUserId());
        if (fixedPriceValid) {
            for (BillOrderInputDTO billOrderInputDTO : subModify) {
                checkAmountForSubOrderForFixed(billOrderInputDTO, discountProportion);
            }
        } else {
            for (BillOrderInputDTO billOrderInputDTO : subModify) {
                checkAmountForSubOrder(billOrderInputDTO, discountProportion);
            }
        }
      //判断当前行程是否存在三方服务，如果存在，应支出金额不能为空
        for (BillOrderInputDTO billOrderInputDTO : subModify) {
            //获取服务的提供方
            OrderBusinessExtend extend = getOrderBusinessExtendFromCache(billOrderInputDTO.getOrderNo());
            Byte driverProvider = extend.getDriverProvider();
            Byte vehicleProvider = extend.getVehicleProvider();
            //存在三方服务，应该支出金额不能为空
            if(DriverProviderEnum.THIRD_PARTY.getValue()== driverProvider
                    || VehicleProviderEnum.THIRD_PARTY.getValue()==vehicleProvider){
                if(billOrderInputDTO.getSupplierPayableAmount() == null){
                    throw ExceptionFactory.createRestException(MrCarOrderErrorCode.SUPPLIER_PAYABLE_AMOUNT_IS_NULL);
                }
            }
        }
    }

    private OrderBusinessExtend getOrderBusinessExtendFromCache(String orderNo) {
        String cacheKey = "businessAdjustPrice:"+orderNo;
        OrderBusinessExtend extend = (OrderBusinessExtend) MemoryCache.get(cacheKey);
        if (extend == null) {
            OrderBusinessExtendExample extendExample = new OrderBusinessExtendExample();
            extendExample.createCriteria()
                    .andOrderNoEqualTo(orderNo);
            List<OrderBusinessExtend> extendList = orderBusinessExtendExMapper.selectByExampleWithBLOBs(extendExample);
            extend = extendList.get(0);
            MemoryCache.set(cacheKey, extend, 10 * 1000);
        }
        return extend;
    }

    /**
     * 一口价金额的参数校验
     */
    private void checkAmountForSubOrderForFixed(BillOrderInputDTO billOrderInputDTO, int discountProportion) {
        //先校验一口价 - 抵扣-减免 = 应收金额
        BigDecimal calcReceivableValue = billOrderInputDTO.getFixedPrice()
                .subtract(billOrderInputDTO.getDeductionAmount())
                .subtract(billOrderInputDTO.getReductionAmount());
        BigDecimal receivableValue = billOrderInputDTO.getReceivableValue();
        if (calcReceivableValue.compareTo(receivableValue) != 0) {
            //应收金额计算错误
            throw ExceptionFactory.createRestException(MrCarOrderErrorCode.RECEIPT_MONEY_ERROR);
        }
        //看减免是否超过额度，最大减免金额 = 一口价 - 折扣
        this.checkReductionPercentage(billOrderInputDTO.getFixedPrice(), billOrderInputDTO, discountProportion);
    }

    private void checkAmountForSubOrder(BillOrderInputDTO billOrderInputDTO, int discountProportion) {
        //司机代垫费
        BigDecimal driverBillAmount = BigDecimal.ZERO;
        List<BillAttachInputDTO> driverBehalfPay = billOrderInputDTO.getDriverBehalfPay();
        if (!CollectionUtils.isEmpty(driverBehalfPay)) {
            driverBillAmount = driverBehalfPay.stream()
                    .map(BillAttachInputDTO::getFeeAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        //合计=基本费+超时费(商务车二期改版之后不存在基础时长和基础里程费)
        // +超里程费+司机代垫费+夜间服务费，不支持修改
        BigDecimal calTotalValue = billOrderInputDTO
                .getBaseFee()
                .add(billOrderInputDTO.getOverTimeFee())
                .add(billOrderInputDTO.getOverMileageFee())
                .add(driverBillAmount)
                .add(billOrderInputDTO.getNightServiceFee());
        if (calTotalValue.compareTo(billOrderInputDTO.getTotalValue()) != 0) {
            logger.error("调价合计计算错误，子行程编号:{},计算的合计:{},页面的合计:{}",
                    billOrderInputDTO.getOrderNo(), calTotalValue, billOrderInputDTO.getTotalValue());
            throw ExceptionFactory.createRestException(MrCarOrderErrorCode.PRICE_CALCULATE_ERROR);
        }
        //校验一下应收金额 合计-抵扣-减免
        BigDecimal calShouldPay = calTotalValue
                .subtract(billOrderInputDTO.getReductionAmount())
                .subtract(billOrderInputDTO.getDeductionAmount());
        if (calShouldPay.compareTo(billOrderInputDTO.getReceivableValue()) != 0) {
            logger.error("调价应收金额计算错误，子行程编号:{},计算的应收金额:{},页面的应收金额:{}",
                    billOrderInputDTO.getOrderNo(), calShouldPay, billOrderInputDTO.getReceivableValue());
        }
        //抵扣金额 最大等于合计金额
        if (billOrderInputDTO.getDeductionAmount().compareTo(calTotalValue) > 0) {
            logger.error("调价抵扣金额不能大于合计金额,子行程编号:{},抵扣金额:{},合计金额:{}",
                    billOrderInputDTO.getOrderNo(), billOrderInputDTO.getDeductionAmount(), calTotalValue);
            throw ExceptionFactory.createRestException(MrCarOrderErrorCode.DISCOUNT_MONEY_ERROR);
        }
        //看减免是否超过额度，最大减免金额 = 合计 - 折扣
        this.checkReductionPercentage(calTotalValue, billOrderInputDTO, discountProportion);
    }

    /**
     * 计算见面额度是否超限制
     */
    private void checkReductionPercentage(BigDecimal totalAmount, BillOrderInputDTO billOrderInputDTO, int discountProportion) {
        BigDecimal maxDeduction = totalAmount.subtract(billOrderInputDTO.getDeductionAmount());
        //折扣百分比
        BigDecimal reductionPercentage = new BigDecimal(discountProportion)
                .divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        BigDecimal multiply = maxDeduction.multiply(reductionPercentage);
        if (billOrderInputDTO.getReductionAmount().compareTo(multiply) > 0) {
            logger.error("调价减免金额不能大于最大减免金额,子行程编号:{},减免金额:{},最大减免金额:{},减免比率:{}",
                    billOrderInputDTO.getOrderNo(), billOrderInputDTO.getReductionAmount(), multiply, reductionPercentage);
            throw ExceptionFactory.createRestException(MrCarOrderErrorCode.ORDER_ADJUST_FAILED);
        }
    }

    private void updateSubBillForFixed(BillOrderInputDTO billOrderInputDTO,OrderModifyInputDTO orderModifyInputDTO) {
        //获取子行程账单
        String orderNo = billOrderInputDTO.getOrderNo();
        BillOrder billOrder = billOrderExMapper.selectByOrderNo(orderNo);
        if (billOrder == null) {
            return;
        }
        BillOrder updateBillOrder = new BillOrder();
        updateBillOrder.setId(billOrder.getId());
        updateBillOrder.setFixedPrice(billOrderInputDTO.getFixedPrice());
        //应收
        updateBillOrder.setShouldpayAmount(billOrderInputDTO.getReceivableValue());
        updateBillOrder.setDiscountType(billOrderInputDTO.getDiscountType());
        updateBillOrder.setPriceExplain(billOrderInputDTO.getPriceExplain());
        updateBillOrder.setDeductionAmount(billOrderInputDTO.getDeductionAmount());
        updateBillOrder.setReductionAmount(billOrderInputDTO.getReductionAmount());
        //调价改版之后没有其他金额
        updateBillOrder.setOtherAmount(BigDecimal.ZERO);
        updateBillOrder.setUpdateTime(new Date());
        //修改应支供应商金额
        updateBillOrder.setSupplierPayableAmount(billOrderInputDTO.getSupplierPayableAmount());
        billOrderExMapper.updateByPrimaryKeySelective(updateBillOrder);
        //修改子行程的一口价金额
        OrderInfo orderInfo = orderInfoExMapper.selectOneByOrderNo(orderNo);
        OrderInfo orderInfoUpdate = new OrderInfo();
        orderInfoUpdate.setId(orderInfo.getId());
        orderInfoUpdate.setFixedPrice(billOrderInputDTO.getFixedPrice());
        orderInfoExMapper.updateByPrimaryKeySelective(orderInfoUpdate);
        //记录子行程调价记录
        BillAdjustPriceLog billAdjustPriceLog = new BillAdjustPriceLog();
        billAdjustPriceLog.setOrderNo(orderNo);
        billAdjustPriceLog.setOrderApplyNo(orderModifyInputDTO.getOrderApplyNo());
        billAdjustPriceLog.setBeforeShouldPayAmount(billOrder.getShouldpayAmount());
        billAdjustPriceLog.setAfterShouldPayAmount(updateBillOrder.getShouldpayAmount());
        billAdjustPriceLog.setCreterId(orderModifyInputDTO.getLoginUserId());
        billAdjustPriceLog.setCreterName(orderModifyInputDTO.getLoginUserName());
        billAdjustPriceLog.setCreterPhone(orderModifyInputDTO.getLoginUserMobile());
        billAdjustPriceLog.setPriceExplain(billOrderInputDTO.getPriceExplain());
        billAdjustPriceLog.setCreateDate(new Date());
        billAdjustPriceLogMapper.insertSelective(billAdjustPriceLog);

        //返空里程费
        List<BillAttachInputDTO> driverBehalfPay = billOrderInputDTO.getDriverBehalfPay();
        BillAttachInputDTO emptyMileageFee = getEmptyMileageFee(driverBehalfPay);
        if (emptyMileageFee != null) {
            if(emptyMileageFee.getFeeAmount()!=null){
                updateBillOrder.setReturnEmptyFee(emptyMileageFee.getFeeAmount());
            }else{
                updateBillOrder.setReturnEmptyFee(BigDecimal.ZERO);
            }
        }
        //调价改版之后没有其他金额
        updateBillOrder.setOtherAmount(BigDecimal.ZERO);
        updateBillOrder.setUpdateTime(new Date());
        updateBillOrder.setDeductionAmount(billOrderInputDTO.getDeductionAmount());
        billOrderExMapper.updateByPrimaryKeySelective(updateBillOrder);
        fillDriverBehalfPay(driverBehalfPay, orderModifyInputDTO.getOrderApplyNo(), orderNo, billOrder.getBillNo());
    }

    private void updateCarOwner(BillOrderInputDTO billOrderInputDTO) {
        OrderBusinessExtend orderBusinessExtend = orderBusinessExtendExMapper.selectByOrderNo(billOrderInputDTO.getOrderNo());
        if (orderBusinessExtend == null) {
            return;
        }
        OrderBusinessExtend update = new OrderBusinessExtend();
        update.setId(orderBusinessExtend.getId());
        update.setBelongBussName(billOrderInputDTO.getBelongBussName());
        update.setBelongBussCode(billOrderInputDTO.getBelongBussCode());
        update.setOperateBussCode(billOrderInputDTO.getOperateBussCode());
        update.setOperateBussName(billOrderInputDTO.getOperateBussName());
        update.setSupplierRate(billOrderInputDTO.getSupplierRate());
        orderBusinessExtendExMapper.updateByPrimaryKeySelective(update);
    }

    /**
     * 更新子行程账单
     */
    private void updateSubBill(BillOrderInputDTO billOrderInputDTO,OrderModifyInputDTO orderModifyInputDTO) {
        //获取子行程账单
        String orderApplyNo = orderModifyInputDTO.getOrderApplyNo();
        String orderNo = billOrderInputDTO.getOrderNo();
        BillOrder billOrder = billOrderExMapper.selectByOrderNo(orderNo);
        if (billOrder == null) {
            return;
        }
        BillOrder updateBillOrder = new BillOrder();
        updateBillOrder.setId(billOrder.getId());
        //合计金额
        updateBillOrder.setTotalAmount(billOrderInputDTO.getTotalValue());
        //应收
        updateBillOrder.setShouldpayAmount(billOrderInputDTO.getReceivableValue());
        updateBillOrder.setBaseFee(billOrderInputDTO.getBaseFee());
        updateBillOrder.setTotalTimeFee(getBigDecimal(billOrderInputDTO.getOverTimeFee()));
        updateBillOrder.setTotalMileageFee(billOrderInputDTO.getOverMileageFee());
        updateBillOrder.setOverMileageFee(billOrderInputDTO.getOverMileageFee());
        updateBillOrder.setOverTimeFee(billOrderInputDTO.getOverTimeFee());
        updateBillOrder.setReductionAmount(billOrderInputDTO.getReductionAmount());
        updateBillOrder.setDiscountType(billOrderInputDTO.getDiscountType());
        updateBillOrder.setPriceExplain(billOrderInputDTO.getPriceExplain());
        updateBillOrder.setNightServiceFee(billOrderInputDTO.getNightServiceFee());
        updateBillOrder.setSupplierPayableAmount(billOrderInputDTO.getSupplierPayableAmount());
        //返空里程费
        List<BillAttachInputDTO> driverBehalfPay = billOrderInputDTO.getDriverBehalfPay();
        BillAttachInputDTO emptyMileageFee = getEmptyMileageFee(driverBehalfPay);
        if (emptyMileageFee != null) {
            if(emptyMileageFee.getFeeAmount()!=null){
                updateBillOrder.setReturnEmptyFee(emptyMileageFee.getFeeAmount());
            }else{
                updateBillOrder.setReturnEmptyFee(BigDecimal.ZERO);
            }
        }
        //调价改版之后没有其他金额
        updateBillOrder.setOtherAmount(BigDecimal.ZERO);
        updateBillOrder.setUpdateTime(new Date());
        updateBillOrder.setDeductionAmount(billOrderInputDTO.getDeductionAmount());
        billOrderExMapper.updateByPrimaryKeySelective(updateBillOrder);
        fillDriverBehalfPay(driverBehalfPay, orderApplyNo, orderNo, billOrder.getBillNo());

        //记录子行程调价记录
        BillAdjustPriceLog billAdjustPriceLog = new BillAdjustPriceLog();
        billAdjustPriceLog.setOrderNo(orderNo);
        billAdjustPriceLog.setOrderApplyNo(orderApplyNo);
        billAdjustPriceLog.setBeforeShouldPayAmount(billOrder.getShouldpayAmount());
        billAdjustPriceLog.setAfterShouldPayAmount(updateBillOrder.getShouldpayAmount());
        billAdjustPriceLog.setCreterId(orderModifyInputDTO.getLoginUserId());
        billAdjustPriceLog.setCreterName(orderModifyInputDTO.getLoginUserName());
        billAdjustPriceLog.setCreterPhone(orderModifyInputDTO.getLoginUserMobile());
        billAdjustPriceLog.setPriceExplain(billOrderInputDTO.getPriceExplain());
        billAdjustPriceLog.setCreateDate(new Date());
        billAdjustPriceLogMapper.insertSelective(billAdjustPriceLog);
    }

    /**
     * 填充司机补录信息
     */
    private void fillDriverBehalfPay(List<BillAttachInputDTO> driverBehalfPay,
                                     String orderApplyNo, String orderNo, String billNo) {
        if (CollectionUtils.isEmpty(driverBehalfPay)) {
            return;
        }
        driverBehalfPay.stream()
                .filter(d -> !isEmptyMileageFee(d))
                .forEach(d -> this.doFillDriverBehalfPay(d, orderApplyNo, orderNo, billNo));
    }

    private void doFillDriverBehalfPay(BillAttachInputDTO attachInputDTO,
                                       String orderApplyNo, String orderNo, String billNo) {
        BillAttach billAttach = new BillAttach();
        billAttach.setOrderApplyNo(orderApplyNo);
        billAttach.setOrderNo(orderNo);
        billAttach.setBillNo(billNo);
        billAttach.setAttachCode(attachInputDTO.getAttachCode());
        billAttach.setFeeAmount(attachInputDTO.getFeeAmount());
        billAttach.setRemarks(attachInputDTO.getRemarks());
        List<BillAttach> billAttaches = billAttachExMapper.selectByOrderNo(orderNo);
        Optional<BillAttach> optional = billAttaches.stream()
                .filter(e -> e.getAttachCode().equals(attachInputDTO.getAttachCode())).findAny();
        if (optional.isPresent()) {
            BillAttach e = optional.get();
            billAttach.setId(e.getId());
            //此处需要判断前端不传金额的情况即删除了此项金额
            if(attachInputDTO.getFeeAmount()==null){
                billAttachExMapper.deleteByPrimaryKey(e.getId());
            }else{
                billAttachExMapper.updateByPrimaryKeySelective(billAttach);
            }
        } else {
            billAttachExMapper.insertSelective(billAttach);
        }
    }


    private BillAttachInputDTO getEmptyMileageFee(List<BillAttachInputDTO> driverBehalfPay) {
        if (CollectionUtils.isEmpty(driverBehalfPay)) {
            return null;
        }
        return driverBehalfPay.stream().filter(this::isEmptyMileageFee).findAny().orElse(null);
    }

    private boolean isEmptyMileageFee(BillAttachInputDTO driverBehalfPay) {
        return BillConst.BillAttachType.EMPTY_MILEAGE_FEE.value() == driverBehalfPay.getAttachCode();
    }


    private void fillBill(OrderModifyInputDTO orderModifyInputDTO, Long billId, Boolean fixedPriceValid) {
        // 更新主申请的账单
        String priceExplain = orderModifyInputDTO.getPriceExplain();
        Integer loginUserId = orderModifyInputDTO.getLoginUserId();
        String loginName = orderModifyInputDTO.getLoginUserName();
        String loginPhone= orderModifyInputDTO.getLoginUserMobile();
        String orderApplyNo = orderModifyInputDTO.getOrderApplyNo();
        List<BillOrderInputDTO> subModify = orderModifyInputDTO.getSubModify();

        Bill updateRecord = new Bill();
        updateRecord.setId(billId);
        //总金额
        BigDecimal totalAmount = subModify.stream().map(BillOrderInputDTO::getTotalValue).reduce(BigDecimal.ZERO, BigDecimal::add);
        updateRecord.setTotalAmount(totalAmount);
        if (fixedPriceValid) {
            updateRecord.setFixedPrice(subModify.stream().map(BillOrderInputDTO::getFixedPrice).reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        //应收金额
        BigDecimal shouldPayAmount = subModify.stream().map(BillOrderInputDTO::getReceivableValue).reduce(BigDecimal.ZERO, BigDecimal::add);
        updateRecord.setShouldpayAmount(shouldPayAmount);
        //抵扣金额
        BigDecimal deductionAmountTotal = subModify.stream().map(BillOrderInputDTO::getDeductionAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        updateRecord.setCouponDeductAmount(deductionAmountTotal);
        //减免金额
        BigDecimal reductionAmount = subModify.stream().map(BillOrderInputDTO::getReductionAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        updateRecord.setReductionAmount(reductionAmount);
        //老数据待结算单的商务车订单已经去除，现在新的商务车订单待结算没有自他金额字段
        updateRecord.setOtherAmount(BigDecimal.ZERO);
        updateRecord.setPriceExplain(priceExplain);
        updateRecord.setAdjustPriceUserId(loginUserId);
        updateRecord.setAdjustPriceUserName(loginName);
        updateRecord.setAdjustPriceTime(new Date());
        billExMapper.updateByPrimaryKeySelective(updateRecord);
        //如果是一口价更新完账单表之后更新一下主行程里面的一口价总金额
        if (fixedPriceValid) {
            OrderApply orderApply = orderApplyExMapper.selectOneByOrderApplyNo(orderApplyNo);
            OrderApply orderApplyUpdateRecord = new OrderApply();
            orderApplyUpdateRecord.setId(orderApply.getId());
            orderApplyUpdateRecord.setFixedPrice(updateRecord.getFixedPrice());
            orderApplyExMapper.updateByPrimaryKeySelective(orderApplyUpdateRecord);
        }
        // 插入调价记录信息
        BillAdjustPriceLog billAdjustPriceLog = new BillAdjustPriceLog();
        billAdjustPriceLog.setCreterId(loginUserId);
        billAdjustPriceLog.setCreterName(loginName);
        billAdjustPriceLog.setCreterPhone(loginPhone);
        billAdjustPriceLog.setOrderApplyNo(orderApplyNo);
        billAdjustPriceLog.setOtherAmount(BigDecimal.ZERO);
        billAdjustPriceLog.setReductionAmount(reductionAmount);
        billAdjustPriceLog.setPriceExplain(priceExplain);
        billAdjustPriceLogMapper.insertSelective(billAdjustPriceLog);
    }


    private void updateOrderApply(OrderApply orderApply,OrderModifyInputDTO modifyInputDTO) {
        // 更新订单申请单状态
        short updateOrderStatus = OrderEnum.OrderApplyStatus.WAIT_ACCOUNT.value();
        // 订单联系人手机号
        String contactMobile = orderApply.getContactMobile();
        if (StringUtils.isNotBlank(contactMobile)) {
            // 有联系人，订单状态变为待确认状态
            updateOrderStatus = OrderEnum.OrderApplyStatus.WAIT_CONFIRM.value();
        }
        OrderApply order = new OrderApply();
        OrderInfo orderInfo = new OrderInfo();
        order.setId(orderApply.getId());
        order.setOrderStatus(updateOrderStatus);
        if(Objects.nonNull(modifyInputDTO.getBookingOrderStime())){
            orderInfo.setBookingOrderStime(modifyInputDTO.getBookingOrderStime());
            order.setBookingOrderStime(modifyInputDTO.getBookingOrderStime());
        }
        if(Objects.nonNull(modifyInputDTO.getBookingOrderEtime())){
            orderInfo.setBookingOrderEtime(modifyInputDTO.getBookingOrderEtime());
            order.setBookingOrderEtime(modifyInputDTO.getBookingOrderEtime());
        }
        if(Objects.nonNull(modifyInputDTO.getCustomerCompanyId())){
            order.setCustomerCompanyId(modifyInputDTO.getCustomerCompanyId());
            orderInfo.setCustomerCompanyId(modifyInputDTO.getCustomerCompanyId());
        }
        if(StringUtils.isNotEmpty(modifyInputDTO.getCustomerCompanyCode())){
            order.setCustomerCompanyCode(modifyInputDTO.getCustomerCompanyCode());
            orderInfo.setCustomerCompanyCode(modifyInputDTO.getCustomerCompanyCode());
        }
        if(StringUtils.isNotEmpty(modifyInputDTO.getCustomerCompanyName())){
            order.setCustomerCompanyName(modifyInputDTO.getCustomerCompanyName());
            orderInfo.setCustomerCompanyName(modifyInputDTO.getCustomerCompanyName());
        }

        // 更新订单状态
        orderApplyExMapper.updateByPrimaryKeySelective(order);

        //更新子行程数据
        OrderInfoExample orderInfoExample = new OrderInfoExample();
        orderInfoExample.createCriteria().andOrderApplyNoEqualTo(orderApply.getOrderApplyNo());
        orderInfoExMapper.updateByExampleSelective(orderInfo,orderInfoExample);

        // 插入订单时间流
        orderTimeStreamService.createOrderTimeStream(orderApply.getOrderApplyNo(), updateOrderStatus,modifyInputDTO.getLoginUserId(),modifyInputDTO.getLoginUserName());
    }

    private OrderPriceAdjustDTO createOrderPriceDTO(OrderInfo orderInfo, Boolean fixedPriceValid) {
        //获取车辆机动车使用人和所有人
        OrderPriceAdjustDTO adjustDTO = new OrderPriceAdjustDTO();
        //设置是否是一口价
        adjustDTO.setFixedPriceValid(fixedPriceValid);
        buildPriceAdjustFromOrder(orderInfo, adjustDTO);
        buildPriceAdjustFromExtend(orderInfo, adjustDTO);
        buildPriceAdjustFromBillOrder(orderInfo, adjustDTO);
        buildPriceAdjustFromAttach(orderInfo, adjustDTO);
        //设置司机上传附件
        buildPriceAdjustFromOrderCertificate(orderInfo, adjustDTO);
        return adjustDTO;
    }

    private void buildPriceAdjustFromOrderCertificate(OrderInfo orderInfo, OrderPriceAdjustDTO adjustDTO) {
        // 查询账单凭证信息
        List<String> certificateList = orderCertificateExMapper.selectByOrderNo(Arrays.asList(orderInfo.getOrderNo()));
        if (certificateList != null && certificateList.size() > 0){
            List<FileDTO> fileDTOList = certificateList.stream().map(certificate -> {
                FileDTO fileDTO = new FileDTO();
                fileDTO.setFileUrl(certificate);
                return fileDTO;
            }).collect(Collectors.toList());
            adjustDTO.setDriverUploadFiles(fileDTOList);
        }
    }

    private BigDecimal getBigDecimal(BigDecimal bigDecimal) {
        return Optional.ofNullable(bigDecimal).orElse(BigDecimal.ZERO);
    }

    private void buildPriceAdjustFromAttach(OrderInfo orderInfo, OrderPriceAdjustDTO infoDTO) {
        //一口价金额不加载这些字段
//        if (Boolean.TRUE.equals(infoDTO.getFixedPriceValid())) {
//            return;
//        }
        List<BillAttachDTO> billAttachDTOS = getEmptyBillAttachDTOList();
        List<BillAttach> billAttaches = billAttachExMapper.selectByOrderNo(orderInfo.getOrderNo());
        //转换为map
        Map<Byte, BillAttach> map = billAttaches.stream().collect(Collectors.toMap(BillAttach::getAttachCode,
                Function.identity(), (f1, f2) -> f2));
        billAttachDTOS.forEach(a -> {
            //返空里程费需要单独处理一下，返空里程费本是在 bill_order表存储，此处需要将其放入司机代垫
            if (BillConst.BillAttachType.EMPTY_MILEAGE_FEE.value() == a.getAttachCode()) {
                a.setFeeAmount(infoDTO.getReturnEmptyFee().signum() == 0 ? null : infoDTO.getReturnEmptyFee());
            } else if (BillConst.BillAttachType.REMARK.value() == a.getAttachCode()) {
                BillAttach billAttach = map.get(a.getAttachCode());
                if (billAttach != null) {
                    a.setRemarks(billAttach.getRemarks());
                }
            } else {
                BillAttach billAttach = map.get(a.getAttachCode());
                if (billAttach != null) {
                    //此处feeAmount可以为空
                    BigDecimal feeAmount = billAttach.getFeeAmount();
                    a.setFeeAmount(feeAmount.signum() == 0 ? null : feeAmount);
                }
            }
        });
        BigDecimal reduce = billAttachDTOS.stream()
                .map(BillAttachDTO::getFeeAmount).filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        //司机代垫费总额
        infoDTO.setDriverBillAmount(reduce.signum() == 0 ? null : reduce);
        infoDTO.setBillAttachDTOList(billAttachDTOS);
    }

    private void buildPriceAdjustFromExtend(OrderInfo orderInfo, OrderPriceAdjustDTO infoDTO) {
        OrderBusinessExtend orderBusinessExtend = orderBusinessExtendExMapper.selectByOrderNo(orderInfo.getOrderNo());
        if (orderBusinessExtend == null) {
            infoDTO.setServiceProviderType(ServiceProviderEnum.VEHICLE_DRIVER.getType());
        } else {
            //获取一下车辆和司机服务提供方
            Byte vehicleProvider = orderBusinessExtend.getVehicleProvider();
            Byte driverProvider = orderBusinessExtend.getDriverProvider();
            ServiceProviderEnum serviceProviderEnum = ServiceProviderEnum.parse(vehicleProvider, driverProvider);
            infoDTO.setServiceProviderType(serviceProviderEnum.getType());
            infoDTO.setServiceProviderDesc(serviceProviderEnum.getDesc());
            //获取一下供应商编码和供应商名称
            String supplierProviderCode = orderBusinessExtend.getSupplierProviderCode();
            infoDTO.setSupplierCode(supplierProviderCode);
            if (StringUtils.isNotBlank(supplierProviderCode)) {
                String supplierName = restForSupplierName(supplierProviderCode);
                infoDTO.setSupplierName(supplierName);
            }else {
                infoDTO.setSupplierName("无需选择");
            }
            //设置机动车所有人和机动车使用人
            infoDTO.setBelongBussName(orderBusinessExtend.getBelongBussName());
            infoDTO.setBelongBussCode(orderBusinessExtend.getBelongBussCode());
            infoDTO.setOperateBussName(orderBusinessExtend.getOperateBussName());
            infoDTO.setOperateBussCode(orderBusinessExtend.getOperateBussCode());
            infoDTO.setSupplierRate(stripTrailingZeros(orderBusinessExtend.getSupplierRate()));
        }
        //机动车所有人为空则查询车辆表的机动车所有人
        if(StringUtils.isBlank(infoDTO.getBelongBussCode())||StringUtils.isBlank(infoDTO.getBelongBussName())){
            if(StringUtils.isNotBlank(orderInfo.getAssignCarLicense())){
                VehicleBaseExample vehicleBaseExample = new VehicleBaseExample();
                vehicleBaseExample.createCriteria().andVehicleLicenseEqualTo(orderInfo.getAssignCarLicense()).andVehicleStatusEqualTo((byte)1);
                List<VehicleBase> vehicleBases = vehicleBaseExMapper.selectByExample(vehicleBaseExample);
                if(!CollectionUtils.isEmpty(vehicleBases)){
                    VehicleBase vehicleBase = vehicleBases.get(0);
                    infoDTO.setBelongBussName(vehicleBase.getBelongBussName());
                    infoDTO.setBelongBussCode(vehicleBase.getBelongBussCode());
                }
            }

        }
        //机动车所有人为空则查询车辆表的机动车使用人
        if(StringUtils.isBlank(infoDTO.getOperateBussCode())||StringUtils.isBlank(infoDTO.getOperateBussName())){
            String customerCompanyCode = orderInfo.getCustomerCompanyCode();
            String structByCustomerCode = UserRpc.getStructByCustomerCode(customerCompanyCode);
            infoDTO.setOperateBussCode(structByCustomerCode);
            HashMap<String, Object> param = new HashMap<>();
            param.put("structCodes", structByCustomerCode);
            //资产组织机构状态 1：有效、4、无效
            param.put("assetState", 1);
            String restUrl = new CarAssetRestLocator().getRestUrl("/asset/v1/getPCStructAssetList.json");
            RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, param, null, StructAssetDTO.class);
            if (restResponse.isSuccess()) {
                List<StructAssetDTO> list = (List<StructAssetDTO>) restResponse.getData();
                if(!CollectionUtils.isEmpty(list)){
                    infoDTO.setOperateBussName(list.get(0).getStructName());
                }

            }

        }
    }
    public static BigDecimal stripTrailingZeros(BigDecimal value) {
        if (value == null) {
            return null;
        }
        return value.stripTrailingZeros();
    }

    private List<BillAttachDTO> getEmptyBillAttachDTOList() {
        List<BillConst.BillAttachType> attachPrices = BillConst.BillAttachType.getAttachPrices();
        return attachPrices.stream().map(e -> new BillAttachDTO(e.value(), e.text())).collect(Collectors.toList());
    }


    private String restForSupplierName(String supplierCode) {
        String restUrl = new UserRestLocator().getRestUrl("/companyManage/getCompanyByCustomerCode");
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("customerCode", supplierCode);
        RestResponse restResponse = RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, hashMap, null, CompanyInfoDTO.class);
        if (!restResponse.isSuccess()) {
            return "";
        }
        CompanyInfoDTO companyInfoDTO = (CompanyInfoDTO) restResponse.getData();
        if (Objects.isNull(companyInfoDTO)) {
            return "";
        }
        return companyInfoDTO.getCompanyName();
    }



    private void buildPriceAdjustFromOrder(OrderInfo orderInfo, OrderPriceAdjustDTO infoDTO) {
        infoDTO.setOrderNo(orderInfo.getOrderNo());
        infoDTO.setAssignCarLicense(orderInfo.getAssignCarLicense());
        infoDTO.setAssignDriverName(orderInfo.getAssignDriverName());
        infoDTO.setAssignDriverPhone(orderInfo.getAssignDriverPhone());
    }

    private void buildPriceAdjustFromBillOrder(OrderInfo orderInfo, OrderPriceAdjustDTO infoDTO) {
        BillOrder billOrder = billOrderExMapper.selectByOrderNo(orderInfo.getOrderNo());
        if (Objects.isNull(billOrder)) {
            return;
        }
        infoDTO.setId(billOrder.getId());
        infoDTO.setDiscountType(billOrder.getDiscountType());
        infoDTO.setDiscountTypeName(DiscountTypeEnum.getDiscountType(billOrder.getDiscountType()).getDesc());
        infoDTO.setReductionAmount(getBigDecimal(billOrder.getReductionAmount()));
        infoDTO.setPriceExplain(billOrder.getPriceExplain());
        infoDTO.setDeductionAmount(getBigDecimal(billOrder.getDeductionAmount()));
        infoDTO.setReceivableValue(getBigDecimal(billOrder.getShouldpayAmount()));
        //设置一下供应商应该支出的金额
        infoDTO.setSupplierPayableAmount(getBigDecimal(billOrder.getSupplierPayableAmount()));
//        if (Boolean.TRUE.equals(infoDTO.getFixedPriceValid())) {
            infoDTO.setFixedPrice(getBigDecimal(billOrder.getFixedPrice()));
//        } else {
            infoDTO.setBaseFee(getBigDecimal(billOrder.getBaseFee()));
            infoDTO.setOverMileageFee(billOrder.getOverMileageFee());
            infoDTO.setOverTimeFee(billOrder.getOverTimeFee());
            infoDTO.setReturnEmptyFee(getBigDecimal(billOrder.getReturnEmptyFee()));
            infoDTO.setOverTime(billOrder.getOverTime().stripTrailingZeros());
            infoDTO.setOverMileage(billOrder.getOverMileage().stripTrailingZeros());
            //合计
            infoDTO.setTotalValue(getBigDecimal(billOrder.getTotalAmount()));
//        }
    }
}
