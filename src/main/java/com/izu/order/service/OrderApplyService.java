package com.izu.order.service;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.izu.asset.dto.CarInfoDTO;
import com.izu.asset.dto.CarLevelDTO;
import com.izu.carasset.dto.output.WorkdayHolidayDTO;
import com.izu.config.dto.PricePolicyDTO;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.mrcar.order.consts.AddressTypeEnum;
import com.izu.mrcar.order.consts.AttachmentTypeEnum;
import com.izu.mrcar.order.consts.OrderConst;
import com.izu.mrcar.order.consts.OrderEnum;
import com.izu.mrcar.order.consts.OrderEnum.OrderType;
import com.izu.mrcar.order.dto.mrcar.FileDTO;
import com.izu.mrcar.order.dto.mrcar.MyOrderInfoListDTO;
import com.izu.mrcar.order.dto.mrcar.OrderApplyDTO;
import com.izu.mrcar.order.dto.mrcar.OrderApplyVehicleImgDTO;
import com.izu.mrcar.order.dto.mrcar.param.OrderApplyQueryParam;
import com.izu.mrcar.order.dto.order.OrderApplyListForAppReqDTO;
import com.izu.mrcar.order.dto.order.OrderApplyListReqDTO;
import com.izu.mrcar.order.dto.order.OrderApplyListRespDTO;
import com.izu.mrcar.order.errcode.MrCarOrderErrorCode;
import com.izu.mrcar.order.util.MrCarOrderDateUtil;
import com.izu.mrcar.workflow.common.constants.enums.ModelEnum;
import com.izu.mrcar.workflow.common.dto.instance.ApplyStartDTO;
import com.izu.mrcar.workflow.common.dto.instance.ApplyStartSwitchDTO;
import com.izu.mrcar.workflow.common.utils.WorkflowClient;
import com.izu.order.common.RestLocators;
import com.izu.order.constants.RestMsgCenter;
import com.izu.order.entity.mrcar.*;
import com.izu.order.rpc.ApproveRestfulApi;
import com.izu.order.rpc.AssetRpc;
import com.izu.order.rpc.MrcarConfigRestfulApi;
import com.izu.order.rpc.UserRpc;
import com.izu.order.service.*;
import com.izu.order.util.DateUtil;
import com.izu.order.util.SequenceGenerator;
import com.izu.user.dto.company.CompanyDTO;
import com.izu.user.entity.Company;
import com.izu.user.enums.LanguageEnum;
import com.izu.user.enums.LoginSystemEnum;
import mapper.mrcar.ex.*;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description: 订单业务处理类
 * @author: hxc
 * @create: 2019-09-06 15:58
 **/
@Service
public class OrderApplyService {

    private static final Logger log = LoggerFactory.getLogger(OrderApplyService.class);

    @Autowired
    private SequenceGenerator sequenceGenerator;

    @Autowired
    private OrderApplyVehicleExMapper orderApplyVehicleExMapper;

    @Autowired
    private OrderApplyExMapper orderApplyExMapper;

    @Autowired
    private OrderTimeStreamService orderTimeStreamService;
    @Autowired
    private OrderApplyVehicleService orderApplyVehicleService;
    @Autowired
    private ApprovalService approvalService;
    @Autowired
    private BillExMapper billExMapper;
    @Autowired
    private OrderInfoExMapper orderInfoExMapper;

    @Autowired
    private MrcarAttachmentExMapper attachmentExMapper;

    @Autowired
    private OrderApplyDestinationExMapper orderApplyDestinationExMapper;

    @Value("${order.make.early.interval.time}")
    private int makeOrderEarlyIntervalTime;
    @Value("${bussiness.chedui.companyid}")
    private int cheduiId;

    @NacosValue(value = "${v2.order.startDate}", autoRefreshed = true)
    private String v2DateStr;


    @Autowired
    private OrderPriceSnapshotService orderPriceSnapshotService;

    @Autowired
    private OrderEvaluationService orderEvaluationService;

    @NacosValue(value = "${pdf.export.company.id}",autoRefreshed = true)
    private String pdfExportCompanyId;


    @Transactional(rollbackFor=Exception.class)
    public Map<String, Object> createOrderSelfHelp(OrderApply orderApplyNew,String bookingOrderStime,CarInfoDTO carInfoDTO) {
        Map<String, Object> result = new HashMap<String, Object>();
        List<OrderApply> orderApplyList = new ArrayList<OrderApply>();
        String[] bookingOrderStimeArra = bookingOrderStime.split(",");
        // 下单时间
        final Date createOrderDate = new Date();
        for(int i=0;i<bookingOrderStimeArra.length;i++) {
            OrderApply orderApply = BeanUtil.copyObject(orderApplyNew, OrderApply.class);
            Date stimeDate = MrCarOrderDateUtil.parse(bookingOrderStimeArra[i], MrCarOrderDateUtil.FMT_YYYYMMDDHHMMSS);
            orderApply.setBookingOrderStime(stimeDate);


            String orderApplyPrefix = orderApply.getOrderType().equals(OrderEnum.OrderType.INTERNAL_ORDER.value()) ? OrderConst.ORDER_NO_INTERNAL_PREFIX:OrderConst.ORDER_NO_MOTORCADE_PREFIX;
            // 生成一个新订单申请单号
            final String orderApplyNo = sequenceGenerator.generate(createOrderDate, orderApplyPrefix);
            log.info("生成新订单申请单号=" + orderApplyNo);
            orderApply.setOrderApplyNo(orderApplyNo);
            // 预定车辆总数
            orderApply.setBookingVehicleTotalCount(1);
            // 默认订单状态待出发
            orderApply.setOrderStatus(OrderEnum.OrderApplyStatus.DISPATCHED.value());
            // 默认审批通过
            orderApply.setApprovalStatus(OrderEnum.OrderApprovalStatus.APPROVEL_PASS.value());
            orderApply.setAuditFlag((byte)0);
            //========================判断是否需要审批,目前只有内部用车需要此判断。
            ApplyStartSwitchDTO applyStartSwitchDTO = new ApplyStartSwitchDTO();
            applyStartSwitchDTO.setCompanyId(orderApply.getCompanyId());
            applyStartSwitchDTO.setBusinessType(ModelEnum.BusinessTypeDefaultEnum.ORDER_SELF.getCode());
            Boolean isAuth =  WorkflowClient.isActive(applyStartSwitchDTO);
            log.info("查询是否需要审批"+ "参数： {} 返回结果： {}",JSON.toJSONString(applyStartSwitchDTO),isAuth);
            if(isAuth){
                // 订单状态待审核
                orderApply.setOrderStatus(OrderEnum.OrderApplyStatus.WAIT_AUTH.value());
                orderApply.setApprovalStatus(OrderEnum.OrderApprovalStatus.APPROVEL_WAIT.value());
                orderApply.setAuditFlag((byte)1);
            }
            // 创建订单申请
            orderApplyExMapper.insertSelective(orderApply);

            // ---------------订单申请成功创建，订单生成------------------------
            OrderInfo orderInfo = BeanUtil.copyObject(orderApply, OrderInfo.class);
            orderInfo.setStartCityCode(orderApply.getBookingStartCityCode());
            if (isAuth){
                orderInfo.setOrderStatus(OrderEnum.OrderStatus.UNKNOWN.value());
            }else {
                orderInfo.setOrderStatus(OrderEnum.OrderStatus.DISPATCHED.value());
            }
            List<OrderInfo> listOrderInfo=new ArrayList<>();
            List<String> listOrderId=new ArrayList<>();
            for(int orderIndex=0;orderApply.getBookingVehicleTotalCount()>orderIndex;orderIndex++){
                // 生成一个订单号
                final String orderNo = sequenceGenerator.generate(createOrderDate, OrderConst.ORDER_NO_PREFIX);
                log.info( "生成新子行程号=" + orderNo);
                orderInfo.setOrderNo(orderNo);
                orderInfo.setAssignCarId(carInfoDTO.getVehicleId());
                orderInfo.setAssignCarLicense(carInfoDTO.getVehicleLicense());
                orderInfo.setAssignCarmodelId(carInfoDTO.getVehicleModelId());
                orderInfo.setAssignCarmodelName(carInfoDTO.getVehicleModel());
                orderInfo.setAssignDriverId(orderApplyNew.getBookingPassengerUserId());
                orderInfo.setAssignDriverName(orderApplyNew.getBookingPassengerUserName());
                orderInfo.setAssignDriverPhone(orderApplyNew.getBookingPassengerUserPhone());
                orderInfo.setBookingCarlevelId(carInfoDTO.getVehicleType());
                orderInfo.setAssignCarlevelId(carInfoDTO.getVehicleType());
                //调用mrcar-asset获取字典表中的车辆级别名称
                CarLevelDTO levelById = AssetRpc.getLevelById(Integer.valueOf(carInfoDTO.getVehicleType()));
                String levelName = StringUtils.EMPTY ;
                if(levelById!=null){
                    levelName = levelById.getLevelName();
                }
                orderInfo.setBookingCarlevelName(levelName);
                orderInfo.setAssignCarlevelName(levelName);
                listOrderId.add(orderNo);
                listOrderInfo.add(BeanUtil.copyObject(orderInfo,OrderInfo.class));
                //快照价格策略
                saveOrderPriceSnapshot(orderInfo);
            }
            orderInfoExMapper.insertSelectiveBatch(listOrderInfo);
            if(!isAuth){
                // 插入订单申请时间流
                orderTimeStreamService.createOrderTimeStream(orderApplyNo,OrderEnum.OrderApplyStatus.DISPATCHED.value(),orderApply.getCustomerId(),orderApply.getCustomerName());
            }else{
                // 需要审批
                ApplyStartDTO applyStartDTO = new ApplyStartDTO();
                applyStartDTO.setLoginUserId(orderApply.getCustomerId());
                applyStartDTO.setLoginUserName(orderApply.getCustomerName());
                applyStartDTO.setLoginCompanyId(orderApply.getCompanyId());
                applyStartDTO.setLoginCompanyName(orderApply.getCompanyName());
                applyStartDTO.setLoginDeptId(orderApply.getStructId());
                applyStartDTO.setLoginDeptName(orderApply.getStructName());
                applyStartDTO.setBusinessType(ModelEnum.BusinessTypeDefaultEnum.ORDER_SELF.getCode());
                applyStartDTO.setBusinessNo(orderApply.getOrderApplyNo());
                Map<String,Object> variables = new HashMap<>();
                variables.put("sTime",orderApply.getBookingOrderStime());
                variables.put("eTime",orderApply.getBookingOrderEtime());
                variables.put("cityOut", orderApply.getCityOut() != null && orderApply.getCityOut());
                // 查询是否节假日
                WorkdayHolidayDTO workdayholiday = ApproveRestfulApi.workdayholiday(orderApply.getBookingOrderStime());
                boolean isHoliday = workdayholiday != null && workdayholiday.getIsHoliday();
                variables.put("isHoliday",isHoliday);
                // 设置部门id
                variables.put("deptId", orderApply.getStructId());
                variables.put("applyRemark",orderApply.getOrderDetail());
                // 流程实例变量设置
                applyStartDTO.setVariables(JSON.toJSONString(variables));
                final RestResponse response = WorkflowClient.applyStart(applyStartDTO);
                if (response.isSuccess() && response.getData()!=null){
                    orderApply.setProcessInstanceId(response.getData().toString());
                    OrderApply applyUpdate = new OrderApply();
                    applyUpdate.setId(orderApply.getId());
                    applyUpdate.setProcessInstanceId(response.getData().toString());
                    orderApplyExMapper.updateByPrimaryKeySelective(applyUpdate);
                }else{
                    log.info("提交审批失败，response={}", JSON.toJSONString(response));
                    throw ExceptionFactory.createRestException(response.getMsg());
                }
                // 插入订单申请时间流
                orderTimeStreamService.createOrderTimeStream(orderApplyNo,OrderEnum.OrderApplyStatus.WAIT_AUTH.value(),orderApply.getCustomerId(),orderApply.getCustomerName());
            }
            orderApplyList.add(orderApply);
            result.put("auditFlag", isAuth);
        }
        result.put("orderApplyList", orderApplyList);
        return result;
    }

    /**
     * 自助取还订单生成价格策略
     * @param orderInfo
     */
    protected void saveOrderPriceSnapshot(OrderInfo orderInfo) {
        orderPriceSnapshotService.createInternalOrderPriceSnapshot(Collections.singletonList(orderInfo));
    }


    /**
    * @Description: 计算订单车辆总数
    * @author: hxc
    * @Date: 2019/9/6
    **/
    public Integer vehicleCount(String vehicleDetail){

        if (StringUtils.isBlank(vehicleDetail)){
            return 1;
        }
        Integer vehicleCount = 0;
        String[] vehicleItems = vehicleDetail.split(",");
        if (vehicleItems != null && vehicleItems.length > 0) {
            for (String vehicleItem : vehicleItems) {
                final String[] parts = vehicleItem.split("@");
                if (parts != null && parts.length > 0) {
                    if (parts.length == 2) {
                        vehicleCount += Integer.valueOf(parts[1]);
                    }
                }
            }
        }
        return vehicleCount;
    }

    /**
    * @Description: 解析预定车级车辆信息
    * @author: hxc
    * @Date: 2019/9/6
    **/
    public List<OrderApplyVehicle> paseVehicle(String vehicleDetail,String orderApplyNo){

        if (StringUtils.isBlank(vehicleDetail) || StringUtils.isBlank(orderApplyNo)){
            return null;
        }
        List<OrderApplyVehicle> resultList = new ArrayList<>();
        String[] vehicleItems = vehicleDetail.split(",");
        if (vehicleItems != null && vehicleItems.length > 0) {
            for (String vehicleItem : vehicleItems) {
                final String[] parts = vehicleItem.split("@");
                if (parts != null && parts.length > 0) {
                    if (parts.length == 2) {
                        OrderApplyVehicle orderVehicle = new OrderApplyVehicle();
                        Byte levelId = Byte.valueOf(parts[0]);
                        orderVehicle.setBookingCarlevelId(levelId);
                        String levelName = StringUtils.EMPTY ;
                        //调用mrcar-asset获取字典表中的车辆级别名称
                        CarLevelDTO levelById = AssetRpc.getLevelById(Integer.valueOf(levelId));
                        if(levelById!=null){
                            levelName = levelById.getLevelName();
                        }
                        orderVehicle.setBookingCarlevelName(levelName);
                        orderVehicle.setBookingVehicleCount(Byte.valueOf(parts[1]));
                        orderVehicle.setOrderApplyNo(orderApplyNo);
                        resultList.add(orderVehicle);
                    }
                }
            }
        }
        return resultList;
    }

    /**
    * @Description: 查询订单申请信息通过申请单号
    * @author: hxc
    * @Date: 2019/9/7
    **/
    public OrderApply queryByOrderApplyNo(String orderApplyNo) {
        return orderApplyExMapper.selectOneByOrderApplyNo(orderApplyNo);
    }



    /**
    * @Description: 分页查询订单申请列表通过查询条件
    * @author: hxc
    * @Date: 2019/9/7
    **/
    public PageDTO queryOrderApplyPageList(Integer page, Integer pageSize, Byte orderType, String customerName, String createTimeStart, String createTimeEnd,
                                           Short orderStatus, Short applyStatus, String[] permissionArr, Integer customerId, Integer companyId, Integer userId, boolean isReceiveOrder) {

        int total;
        List<OrderApply> orderApplyList;
        Page<?> p = PageHelper.startPage(page, pageSize, true);
        try {
            if (null!=userId) {
                //申请列表
                orderApplyList = orderApplyExMapper.selectApprovalPageList(orderType,customerName,createTimeStart,createTimeEnd,applyStatus,permissionArr,customerId,companyId,userId);
            } else {
                //订单管理列表，接单列表，只与数据权限有关
                orderApplyList = orderApplyExMapper.selectPageList(orderType,customerName,createTimeStart,createTimeEnd,orderStatus,permissionArr,customerId,companyId,userId,isReceiveOrder);
            }
            total = (int)p.getTotal();
        }finally {
            PageHelper.clearPage();
        }
        if(orderApplyList==null || orderApplyList.isEmpty()) {
            return new PageDTO( page, pageSize, total ,  Collections.emptyList() );
        }
        List<OrderApplyDTO> orderApplyDTOS = BeanUtil.copyList(orderApplyList, OrderApplyDTO.class);
        // 填充订单应收金额
        for (OrderApplyDTO orderApplyDTO:orderApplyDTOS) {
            if (null!=userId && orderApplyDTO.getCustomerId().equals(userId)) {
                orderApplyDTO.setSelfOrder(1);
            }
                // 查询订单应收金额
            Bill bill = billExMapper.selectByOrderApplyNo(orderApplyDTO.getOrderApplyNo());
            if(bill!=null){
               BigDecimal shouldpayAmount = bill.getShouldpayAmount();
               orderApplyDTO.setShouldpayAmount(shouldpayAmount);
            }
            //查询公司名称
            if(orderApplyDTO.getCompanyId()!=null&&orderApplyDTO.getCompanyId()>0){
                final CompanyDTO company = UserRpc.getCompanyById(orderApplyDTO.getCompanyId());
                if(company!=null){
                    orderApplyDTO.setCompanyName(company.getCompanyName());
                }
            }
        }
        return new PageDTO(page,pageSize,total,orderApplyDTOS);
    }


    /**
    * @Description: 分页查询订单申请列表，提供给APP用户中心展示列表
    * @author: hxc
    * @Date: 2019/9/11
    **/
    public PageDTO queryOrderApplyPageListForAPPUserCenter(OrderApplyQueryParam orderApplyQueryParam) {

        int total =0;
        int page=orderApplyQueryParam.getPageNum();
        int pageSize=orderApplyQueryParam.getPageSize();
        String lang=orderApplyQueryParam.getLanguage();
        List<OrderApply> orderApplyList = null;
        Page<?> p = PageHelper.startPage(page, pageSize, true);
        try {
            orderApplyList = orderApplyExMapper.selectPageListForAPP(orderApplyQueryParam);
            total = (int)p.getTotal();
        }finally {
            PageHelper.clearPage();
        }
        if(orderApplyList==null || orderApplyList.size()==0) {
            PageDTO pageDto = new PageDTO( page, pageSize, total ,  new ArrayList<>() );
            return pageDto;
        }
        List<OrderApplyDTO> orderApplyDTOS = BeanUtil.copyList(orderApplyList, OrderApplyDTO.class);
        Map<String, Map<String, Object>> levelMap = getCarLevelMap();
        List<String> orderApplyNos=orderApplyDTOS.stream().map(OrderApplyDTO::getOrderApplyNo).collect(Collectors.toList());
        List<OrderApplyVehicle> orderApplyVehicleLists = orderApplyVehicleService.queryListByOrderApplyNo(orderApplyNos);
        Map<String,List<OrderApplyVehicle>> orderApplyVehicleMap=orderApplyVehicleLists.stream().collect(Collectors.groupingBy(OrderApplyVehicle::getOrderApplyNo));
        List<OrderInfo> orderInfoLists=  orderInfoExMapper.selectListByOrderApplyNos(orderApplyNos);
        Map<String,List<OrderInfo>> orderInfoMap=orderInfoLists.stream().collect(Collectors.groupingBy(OrderInfo::getOrderApplyNo));
        orderApplyDTOS.forEach(x -> {
            x.setServiceName(OrderEnum.OrderServiceType.getByValue(x.getServiceCode()).text());
            if (x.getOrderStatus()>= OrderEnum.OrderApplyStatus.END_TRIP.value() && !x.getAppraiseSubmited() && x.getOrderStatus()!=OrderEnum.OrderApplyStatus.CANCEL.value()) {
                x.setIsAppraised(0);
            } else {
                x.setIsAppraised(1);
            }
            // 填充预定车辆信息
            List<OrderApplyVehicle> orderApplyVehicleList = orderApplyVehicleMap.get(x.getOrderApplyNo());
            if (orderApplyVehicleList != null && orderApplyVehicleList.size() > 0 ){
                List<OrderApplyVehicleImgDTO> imgDTOS = BeanUtil.copyList(orderApplyVehicleList, OrderApplyVehicleImgDTO.class);
                if(null!=imgDTOS && !imgDTOS.isEmpty()){
                    for (OrderApplyVehicleImgDTO imgDTO : imgDTOS) {
                        imgDTO.setVehicleImageOff(levelMap.get(imgDTO.getBookingCarlevelId().toString()).get("vehicleImageOff").toString());
                        imgDTO.setVehicleImageOn(levelMap.get(imgDTO.getBookingCarlevelId().toString()).get("vehicleImageOn").toString());
                        imgDTO.setVehicleRoundOff(levelMap.get(imgDTO.getBookingCarlevelId().toString()).get("vehicleRoundOff").toString());
                        imgDTO.setVehicleRoundOn(levelMap.get(imgDTO.getBookingCarlevelId().toString()).get("vehicleRoundOn").toString());
                    }
                }
                x.setOrderApplyVehicleImgDTOList(imgDTOS);
            }
            //app列表 展示按钮逻辑
            //再次下单逻辑
            x.setCancelOrder(Boolean.FALSE);
            x.setImmediateTrip(Boolean.FALSE);
            x.setStartNavigation(Boolean.FALSE);
            x.setFinishTrip(Boolean.FALSE);
            x.setAgainOrder(Boolean.FALSE);
            x.setRevertOrder(Boolean.FALSE);
            if(Objects.equals(OrderEnum.OrderType.INTERNAL_ORDER.value(),x.getOrderType())){
                 if(Objects.equals(OrderEnum.OrderServiceType.ONCE.value(),x.getServiceCode()) || Objects.equals(OrderEnum.OrderServiceType.ONE_DAY_RENT.value(),x.getServiceCode())){
                     x.setAgainOrder(Boolean.TRUE);
                 }
            }
            //是否展示pdf导出按钮 乐途和丽江展示
            if(StringUtils.isNotBlank(pdfExportCompanyId)){
                List<Integer> pdfCompanyIds= Arrays.stream(pdfExportCompanyId.split(",")).map(Integer::valueOf).collect(Collectors.toList());
                if(Objects.equals(OrderEnum.OrderType.INTERNAL_ORDER.value(),x.getOrderType())&&pdfCompanyIds.contains(x.getCustomerCompanyId())){
                    x.setShowPdfBtn(true);
                }
            }

            //私车公用
            if(Objects.equals(OrderEnum.OrderType.PRIVATE_ORDER.value(),x.getOrderType())){
                if(Objects.equals(OrderEnum.OrderStatus.DISPATCHED.value(),x.getOrderStatus())){
                    x.setCancelOrder(Boolean.TRUE);
                    x.setImmediateTrip(Boolean.TRUE);
                }
                if(Objects.equals(OrderEnum.OrderStatus.DURING_TRIP.value(),x.getOrderStatus())){
                    x.setStartNavigation(Boolean.TRUE);
                    x.setFinishTrip(Boolean.TRUE);
                }
                if(Objects.equals(OrderEnum.OrderStatus.COMPLETED.value(),x.getOrderStatus())){
                    x.setAgainOrder(Boolean.TRUE);
                }
                //自助取还
            }else if(Objects.equals(OrderEnum.OrderType.INTERNAL_ORDER.value(),x.getOrderType()) &&
                    Objects.equals(OrderEnum.OrderServiceType.SELF_HELP_RENT.value(),x.getServiceCode())){
                //版本 113 之前的自助取还列表不允许展示取消和撤回按钮
                final int versionLimit = 113;
                Integer appVersion = orderApplyQueryParam.getAppVersion();
                boolean versionCondition = appVersion != null && appVersion >= versionLimit;
                if(versionCondition && Objects.equals(OrderEnum.OrderStatus.DISPATCHED.value(),x.getOrderStatus())){
                    x.setCancelOrder(Boolean.TRUE);
                }
                if(versionCondition && Objects.equals(OrderEnum.OrderApplyStatus.WAIT_AUTH.value(),x.getOrderStatus())){
                    x.setRevertOrder(Boolean.TRUE);
                }
            }
            //子行程编码
            List<OrderInfo> orderInfoList=  orderInfoMap.get(x.getOrderApplyNo());
            if(CollectionUtil.isNotEmpty(orderInfoList)){
                final OrderInfo orderInfo = orderInfoList.get(0);
                x.setOrderNo(orderInfo.getOrderNo());
                x.setAssignCarLicense(orderInfo.getAssignCarLicense());
            }

            if(Objects.equals(LanguageEnum.EN.getCode(),lang)){
                OrderEnum.OrderApplyStatus orderApplyStatus=OrderEnum.OrderApplyStatus.getByValue(x.getOrderStatus());
                x.setOrderStatusName(Objects.nonNull(orderApplyStatus)?orderApplyStatus.textEn():"");
                String serviceCode=x.getServiceCode();
                if(StringUtils.isNotBlank(serviceCode)){
                    OrderEnum.OrderServiceType orderServiceType= OrderEnum.OrderServiceType.getByValue(serviceCode);
                    x.setServiceName(Objects.isNull(orderServiceType)?"":orderServiceType.getEnName());
                }
                Byte orderType=x.getOrderType();
                if(Objects.nonNull(orderType)){
                    OrderEnum.OrderTypeForApp orderTypeForApp= OrderEnum.OrderTypeForApp.getByValue(orderType);
                    x.setOrderTypeName(Objects.nonNull(orderTypeForApp)?orderTypeForApp.textEn():"");
                }

            }
        });
        //log.info("【APP查询我的行程列表】返回结果："+JSON.toJSONString(orderApplyDTOS));
        return new PageDTO(page,pageSize,total,orderApplyDTOS);
    }


    public Map<String, Map<String, Object>> getCarLevelMap() {
        String restUrl = RestLocators.asset().getRestUrl("/leve/getCarLevelList");
        Map<String,Object> paramMap = new HashMap<>();
        paramMap.put("usableRange",2);
        RestResponse restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, paramMap, null, Map.class);
        if (!restResponse.isSuccess()) {
            log.error("查询资产服务所有车辆级别列表失败,参数：{}，返回结果：{}", JSONObject.toJSONString(paramMap), JSON.toJSONString(restResponse));
            return null;
        }
        List<Map> vehicleList = JSONArray.parseArray(JSONObject.toJSONString(restResponse.getData()), Map.class);
        Map<String, Map<String, Object>> levelMap = new HashMap<>();
        vehicleList.forEach(x->{levelMap.put(x.get("levelId").toString(),x);});
        return levelMap;
    }
    /**
     * 分页查询行程列表，提供APP工作台行程列表
     * @return
     */
    public PageDTO selectAllPageListForAPP(OrderApplyListForAppReqDTO reqDTO) {
        //当前登录人是运营端人员,只能查看商务用车订单
        final PageInfo<Object> pageInfo = PageHelper.startPage(reqDTO.getPageNum(), reqDTO.getPageSize())
                .doSelectPageInfo(() -> orderApplyExMapper.selectAllPageListForAPP(reqDTO));
        if (pageInfo.getList().isEmpty()) {
            PageDTO pageDto = new PageDTO(pageInfo.getPageNum(), pageInfo.getPageSize(), pageInfo.getTotal(), pageInfo.getList());
            return pageDto;
        }
        List<OrderApplyDTO> orderApplyDTOS = BeanUtil.copyList(pageInfo.getList(), OrderApplyDTO.class);
        Map<String, Map<String, Object>> levelMap = getCarLevelMap();
        orderApplyDTOS.forEach(x -> {
            x.setServiceName(OrderEnum.OrderServiceType.getByValue(x.getServiceCode()).text());
            if (x.getOrderStatus() >= OrderEnum.OrderApplyStatus.END_TRIP.value() && !x.getAppraiseSubmited() && x.getOrderStatus() != OrderEnum.OrderApplyStatus.CANCEL.value()) {
                x.setIsAppraised(0);
            } else {
                x.setIsAppraised(1);
            }
            // 填充预定车辆信息
            List<OrderApplyVehicle> orderApplyVehicleList = orderApplyVehicleService.queryByOrderApplyNo(x.getOrderApplyNo());
            if (orderApplyVehicleList != null && orderApplyVehicleList.size() > 0) {
                List<OrderApplyVehicleImgDTO> imgDTOS = BeanUtil.copyList(orderApplyVehicleList, OrderApplyVehicleImgDTO.class);
                if (null != imgDTOS && !imgDTOS.isEmpty()) {
                    for (OrderApplyVehicleImgDTO imgDTO : imgDTOS) {
                        imgDTO.setVehicleImageOff(levelMap.get(imgDTO.getBookingCarlevelId().toString()).get("vehicleImageOff").toString());
                        imgDTO.setVehicleImageOn(levelMap.get(imgDTO.getBookingCarlevelId().toString()).get("vehicleImageOn").toString());
                        imgDTO.setVehicleRoundOff(levelMap.get(imgDTO.getBookingCarlevelId().toString()).get("vehicleRoundOff").toString());
                        imgDTO.setVehicleRoundOn(levelMap.get(imgDTO.getBookingCarlevelId().toString()).get("vehicleRoundOn").toString());
                    }
                }
                x.setOrderApplyVehicleImgDTOList(imgDTOS);
            }
            // 私车公用 h5子行程详情使用，返回子行程详情
            if (Objects.equals(OrderType.PRIVATE_ORDER.value(), x.getOrderType())) {
                List<String> orderNos = orderInfoExMapper.listOrderNoByApplyNo(x.getOrderApplyNo());
                if (CollectionUtil.isNotEmpty(orderNos)) {
                    x.setOrderNo(orderNos.stream().findFirst().get());
                }

            }
        });
        log.info("【APP查询行程列表】查询成功！");
        return new PageDTO(pageInfo.getPageNum(), pageInfo.getPageSize(), pageInfo.getTotal(), orderApplyDTOS);
    }

    public boolean lockCoupon(Integer couponId, String orderApplyNo) {
        // 释放优惠券
        Map<String,Object> params=new HashMap<>();
        params.put("couponId",couponId);
        params.put("orderApplyNo",orderApplyNo);
        String restUrl = RestLocators.coupon().getRestUrl(RestMsgCenter.LOCK_COUPON);
        RestResponse restResponse = RestClient.requestInside(BaseHttpClient.HttpMethod.POST, restUrl, params, null);
        log.info("调用coupons锁定优惠券 条件：{},结果：{} ",  params.toString(),JSON.toJSONString(restResponse));
        return (boolean) restResponse.getData();
    }

    /**
     * @Description: 查询订单申请列表，提供给APP用户中心调度列表
     * @author: lt
     * @Date: 2019/11/13
     **/
    public List<OrderApplyDTO> queryOrderApplyListForSchedule(List<String> orderApplyNoList) {
        if (CollectionUtils.isEmpty(orderApplyNoList)) {
            return new ArrayList<>();
        }
        List<OrderApplyDTO> orderApplyDTOList = new ArrayList<>();
        List<OrderApplyVehicleImgDTO> orderApplyVehicleImgDTOList = new ArrayList<>();
        List<OrderApply> orderApplyList = orderApplyExMapper.selectListByOrderApplyNoList(orderApplyNoList);
        orderApplyDTOList = BeanUtil.copyList(orderApplyList, OrderApplyDTO.class);
        List<OrderApplyVehicle> list = orderApplyVehicleExMapper.selectByOrderApplyNoList(orderApplyNoList);
        orderApplyVehicleImgDTOList = BeanUtil.copyList(list, OrderApplyVehicleImgDTO.class);
        Map<String, Map<String, Object>> levelMap = approvalService.getCarLevelMap();
        for (OrderApplyDTO orderApplyDTO:orderApplyDTOList){
            List<OrderApplyVehicleImgDTO> list1 = new ArrayList<>();
            Iterator<OrderApplyVehicleImgDTO> iterator = orderApplyVehicleImgDTOList.iterator();
            while (iterator.hasNext()){
                OrderApplyVehicleImgDTO orderApplyVehicleImgDTO = iterator.next();
                if (orderApplyVehicleImgDTO.getOrderApplyNo().equals(orderApplyDTO.getOrderApplyNo())) {
                    orderApplyVehicleImgDTO.setVehicleImageOff(levelMap.get(orderApplyVehicleImgDTO.getBookingCarlevelId().toString()).get("vehicleImageOff").toString());
                    orderApplyVehicleImgDTO.setVehicleImageOn(levelMap.get(orderApplyVehicleImgDTO.getBookingCarlevelId().toString()).get("vehicleImageOn").toString());
                    orderApplyVehicleImgDTO.setVehicleRoundOff(levelMap.get(orderApplyVehicleImgDTO.getBookingCarlevelId().toString()).get("vehicleRoundOff").toString());
                    orderApplyVehicleImgDTO.setVehicleRoundOn(levelMap.get(orderApplyVehicleImgDTO.getBookingCarlevelId().toString()).get("vehicleRoundOn").toString());
                    list1.add(orderApplyVehicleImgDTO);
                    iterator.remove();
                }
            }
            orderApplyDTO.setOrderApplyVehicleImgDTOList(list1);
            //查询实际用车人企业信息
            final CompanyDTO company = UserRpc.getCompanyById(orderApplyDTO.getCustomerCompanyId());
            if(company!=null){
                orderApplyDTO.setCustomerCompanyName(company.getCompanyName());
            }
        }
        return orderApplyDTOList;
    }

    public List<OrderApplyDTO> queryOrderApplyListForSchedulePage(List<String> orderApplyNoList){
        if (CollectionUtils.isEmpty(orderApplyNoList)) {
            return new ArrayList<>();
        }
        List<OrderApply> orderApplyList = orderApplyExMapper.selectListByOrderApplyNoList(orderApplyNoList);
        return BeanUtil.copyList(orderApplyList, OrderApplyDTO.class);
    }

    public int queryCountOfInProgress(Integer companyId,Integer customerId){
        return orderApplyExMapper.queryCountOfInProgress(customerId,companyId);
    }

    /**
     * 获取最早的下单时间
     *
     * @param orderType
     * @param loginSystemType
     * @return
     */
    public String getEarliestOrderDate(Byte orderType, Byte loginSystemType) {
        //当前登陆人是运营端用户，最早下单之间就是当前时间
        if (LoginSystemEnum.PROVIDER.getSys().equals(loginSystemType)) {
            return DateUtil.getTimeString(new Date());
        }
        //商务用车订单，最早下单时间就是当前时间
        if (OrderType.MOTORCADE_ORDER.value().equals(orderType)) {
            return DateUtil.getTimeString(new Date());
        }
        //除上述两种情况之外，其余情况都是当前时间加上 makeOrderEarlyIntervalTime 分钟。
        Date makeOrderDate = DateUtil.addDate(new Date(), Calendar.MINUTE, makeOrderEarlyIntervalTime);
        return DateUtil.getTimeString(makeOrderDate);
    }

    public Map getOrderTime(Byte orderType,Integer loginCompanyId){
        Map resultMap = new HashMap();
        Date currentDate = new Date();

        if(!orderType.equals(OrderEnum.OrderType.MOTORCADE_ORDER.value())||cheduiId==loginCompanyId){
            //内部用车或者是商务车队企业人员下单，走原本分钟向上取整逻辑(前端做取整)
            resultMap.put("earliestOrderDate",DateUtil.getTimeString(currentDate));
        }else {
            //其他情况，一律走配置的时间规则
            Date makeOrderDate = DateUtil.addDate(currentDate, Calendar.MINUTE, makeOrderEarlyIntervalTime);
            resultMap.put("earliestOrderDate",DateUtil.getTimeString(makeOrderDate));
        }
        return resultMap;
    }



    /**
    * @Description: 分页查询行程列表
    * @author: hxc
    * @Date: 2019/12/26
    **/
    public PageDTO queryOrderApplyPageList(OrderApplyListReqDTO queryDTO) {
        if(null!=queryDTO.getOnlySelf() && queryDTO.getOnlySelf()){
            //我的行程不走数据权限
            queryDTO.setDataPermType(null);
        }
        if(CollUtil.isEmpty(queryDTO.getOrderTypeList()) && queryDTO.getPort()==1 && Objects.equals(queryDTO.getInternalSupplierFlag(),0)){
            return new PageDTO(queryDTO.getPage(), queryDTO.getPageSize(), 0L, null);
        }
        final PageInfo<OrderApplyListRespDTO> pageInfo = PageHelper.startPage(queryDTO.getPage(), queryDTO.getPageSize())
                .doSelectPageInfo(() -> orderApplyExMapper.selectOrderApplyPageList(queryDTO));
        List<OrderApplyListRespDTO> orderApplyList = pageInfo.getList();
        if (orderApplyList.isEmpty()) {
            return new PageDTO(pageInfo.getPageNum(), pageInfo.getPageSize(), pageInfo.getTotal(), pageInfo.getList());
        }
        //获取主行程id集合
        List<String> orderApplyNos = orderApplyList.stream().map(OrderApplyListRespDTO::getOrderApplyNo).collect(Collectors.toList());
        //查询私车的子行程集合 一对一
        List<OrderInfo> orderInfoList = orderInfoExMapper.selectByOrderApplyNos(orderApplyNos);
        //key->主行程id value->子行程id
        Map<String, OrderInfo> orderInfoMap = orderInfoList.stream()
                .collect(Collectors.toMap(OrderInfo::getOrderApplyNo, info -> info, (a, b) -> b));

        orderApplyList.forEach(orderApply -> {
            if (queryDTO.getLoginId() != null && queryDTO.getLoginId().equals(orderApply.getCustomerId())) {
                orderApply.setSelfOrder(1);
            }
            //商务用车区分版本
            final boolean versionFlag = OrderType.MOTORCADE_ORDER.value().equals(orderApply.getOrderType()) && StringUtils.isNotBlank(v2DateStr) && orderApply.getCreateTime().compareTo(DateUtil.parseDate(v2DateStr)) >= 0;
            orderApply.setVersion(versionFlag ? 2 : 1);
            //审批迁移之后，审批按钮在行程列表不展示
            orderApply.setIsShowApproveButton(false);
            //设置枚举值
            orderApply.setOrderTypeName(OrderType.getByValue(orderApply.getOrderType().byteValue()).text());
            if (!Objects.isNull(orderApply.getOrderStatus()) && OrderEnum.OrderApplyStatus.getByValue(orderApply.getOrderStatus().shortValue()) != null) {
                orderApply.setOrderStatusName(OrderEnum.OrderApplyStatus.getByValue(orderApply.getOrderStatus().shortValue()).text());
            } else {
                orderApply.setOrderStatusName(StringUtils.EMPTY);
            }
            orderApply.setServiceName(OrderEnum.OrderServiceType.getByValue(orderApply.getServiceCode()).text());
            //设置子行程id
            if (Objects.equals(OrderType.PRIVATE_ORDER.value(), orderApply.getOrderType())) {
                OrderInfo orderInfo=orderInfoMap.get(orderApply.getOrderApplyNo());
                if(Objects.nonNull(orderInfo)){
                    orderApply.setOrderNo(orderInfo.getOrderNo());
                    orderApply.setMileageStatType(orderInfo.getMileageStatType());
                }
            }
            //商务车是否展示取消按钮
            if (Objects.equals(OrderType.MOTORCADE_ORDER.value(), orderApply.getOrderType())) {
                Boolean cancelStatus = OrderEnum.OrderApplyStatus.DISPATCHING.value() == orderApply.getOrderStatus()
                        || OrderEnum.OrderApplyStatus.DISPATCHED.value() == orderApply.getOrderStatus()
                        || OrderEnum.OrderStatus.WAIT_AUTH.value() == orderApply.getOrderStatus();
                orderApply.setCancelButtonDisplay(cancelStatus);
            } else {
                Boolean cancelStatus = OrderEnum.OrderStatus.DISPATCHING.value() == orderApply.getOrderStatus()
                        || OrderEnum.OrderStatus.DISPATCHED.value() == orderApply.getOrderStatus();
                Boolean cancelIdentity = orderApply.getCustomerId().intValue() == queryDTO.getLoginId();
                if (OrderType.PRIVATE_ORDER.value().equals(orderApply.getOrderType())) {
                    cancelIdentity = cancelIdentity || orderApply.getBookingPassengerUserId().intValue() == queryDTO.getLoginId();
                }
                orderApply.setCancelButtonDisplay(cancelStatus && cancelIdentity);
            }

            //是否展示pdf导出按钮 乐途和丽江展示
            if(StringUtils.isNotBlank(pdfExportCompanyId)) {
                List<Integer> pdfCompanyIds = Arrays.stream(pdfExportCompanyId.split(",")).map(Integer::valueOf).collect(Collectors.toList());
                if (Objects.equals(OrderEnum.OrderType.INTERNAL_ORDER.value(), orderApply.getOrderType()) && pdfCompanyIds.contains(orderApply.getCustomerCompanyId())) {
                    orderApply.setShowPdfBtn(true);
                }
            }
        });
        return new PageDTO(pageInfo.getPageNum(), pageInfo.getPageSize(), pageInfo.getTotal(), pageInfo.getList());
    }

    public List<FileDTO> getSettleFiles(Integer id) {
        List<MrcarAttachment> attachments = attachmentExMapper.findByApplyId(id, AttachmentTypeEnum.ORDER_SETTLE.value());
        if (attachments == null || attachments.isEmpty()){
            return Collections.emptyList();
        }
        return attachments.stream().map(mrcarAttachment -> BeanUtil.copyObject(mrcarAttachment, FileDTO.class)).collect(Collectors.toList());
    }

    public List<FileDTO> getModifyFiles(Integer id) {
        List<MrcarAttachment> attachments = attachmentExMapper.findByApplyId(id, AttachmentTypeEnum.ORDER_PRICE.value());
        if (attachments == null || attachments.isEmpty()){
            return Collections.emptyList();
        }
        return attachments.stream().map(mrcarAttachment -> BeanUtil.copyObject(mrcarAttachment, FileDTO.class)).collect(Collectors.toList());
    }

    /**
     * @Description: 查询我的行程 准备废弃
     **/
    @Deprecated
    public PageDTO queryOrderApplyPageListForAPPUserCenterV2(OrderApplyQueryParam orderApplyQueryParam) {

        int total =0;
        int page=orderApplyQueryParam.getPageNum();
        int pageSize=orderApplyQueryParam.getPageSize();
        String lang=orderApplyQueryParam.getLanguage();
        List<OrderApply> orderApplyList = null;
        Page<?> p = PageHelper.startPage(page, pageSize, true);
        try {
            orderApplyList = orderApplyExMapper.selectPageListForAPP(orderApplyQueryParam);
            total = (int)p.getTotal();
        }finally {
            PageHelper.clearPage();
        }
        if(orderApplyList==null || orderApplyList.size()==0) {
            PageDTO pageDto = new PageDTO( page, pageSize, total ,  new ArrayList<>() );
            return pageDto;
        }
        List<OrderApplyDTO> orderApplyDTOS = BeanUtil.copyList(orderApplyList, OrderApplyDTO.class);
        Map<String, Map<String, Object>> levelMap = getCarLevelMap();
        List<String> orderApplyNos=orderApplyDTOS.stream().map(OrderApplyDTO::getOrderApplyNo).collect(Collectors.toList());
        List<OrderApplyVehicle> orderApplyVehicleLists = orderApplyVehicleService.queryListByOrderApplyNo(orderApplyNos);
        Map<String,List<OrderApplyVehicle>> orderApplyVehicleMap=orderApplyVehicleLists.stream().collect(Collectors.groupingBy(OrderApplyVehicle::getOrderApplyNo));
        List<OrderInfo> orderInfoLists=  orderInfoExMapper.selectListByOrderApplyNos(orderApplyNos);
        Map<String,List<OrderInfo>> orderInfoMap=orderInfoLists.stream().collect(Collectors.groupingBy(OrderInfo::getOrderApplyNo));
        orderApplyDTOS.forEach(x -> {
            x.setServiceName(OrderEnum.OrderServiceType.getByValue(x.getServiceCode()).text());
            if (x.getOrderStatus()>= OrderEnum.OrderApplyStatus.END_TRIP.value() && !x.getAppraiseSubmited() && x.getOrderStatus()!=OrderEnum.OrderApplyStatus.CANCEL.value()) {
                x.setIsAppraised(0);
            } else {
                x.setIsAppraised(1);
            }
            // 填充预定车辆信息
            List<OrderApplyVehicle> orderApplyVehicleList = orderApplyVehicleMap.get(x.getOrderApplyNo());
            if (orderApplyVehicleList != null && orderApplyVehicleList.size() > 0 ){
                List<OrderApplyVehicleImgDTO> imgDTOS = BeanUtil.copyList(orderApplyVehicleList, OrderApplyVehicleImgDTO.class);
                if(null!=imgDTOS && !imgDTOS.isEmpty()){
                    for (OrderApplyVehicleImgDTO imgDTO : imgDTOS) {
                        imgDTO.setVehicleImageOff(levelMap.get(imgDTO.getBookingCarlevelId().toString()).get("vehicleImageOff").toString());
                        imgDTO.setVehicleImageOn(levelMap.get(imgDTO.getBookingCarlevelId().toString()).get("vehicleImageOn").toString());
                        imgDTO.setVehicleRoundOff(levelMap.get(imgDTO.getBookingCarlevelId().toString()).get("vehicleRoundOff").toString());
                        imgDTO.setVehicleRoundOn(levelMap.get(imgDTO.getBookingCarlevelId().toString()).get("vehicleRoundOn").toString());
                    }
                }
                x.setOrderApplyVehicleImgDTOList(imgDTOS);
            }
            //app列表 展示按钮逻辑
            //再次下单逻辑
            x.setCancelOrder(Boolean.FALSE);
            x.setImmediateTrip(Boolean.FALSE);
            x.setStartNavigation(Boolean.FALSE);
            x.setFinishTrip(Boolean.FALSE);
            x.setAgainOrder(Boolean.FALSE);
            x.setRevertOrder(Boolean.FALSE);
            if(Objects.equals(OrderEnum.OrderType.INTERNAL_ORDER.value(),x.getOrderType())){
                if(Objects.equals(OrderEnum.OrderServiceType.ONCE.value(),x.getServiceCode()) || Objects.equals(OrderEnum.OrderServiceType.ONE_DAY_RENT.value(),x.getServiceCode())){
                    x.setAgainOrder(Boolean.TRUE);
                }
            }
            //私车公用
            if(Objects.equals(OrderEnum.OrderType.PRIVATE_ORDER.value(),x.getOrderType())){
                if(Objects.equals(OrderEnum.OrderStatus.DISPATCHED.value(),x.getOrderStatus())){
                    x.setCancelOrder(Boolean.TRUE);
                    x.setImmediateTrip(Boolean.TRUE);
                }
                if(Objects.equals(OrderEnum.OrderStatus.DURING_TRIP.value(),x.getOrderStatus())){
                    x.setStartNavigation(Boolean.TRUE);
                    x.setFinishTrip(Boolean.TRUE);
                }
                if(Objects.equals(OrderEnum.OrderStatus.COMPLETED.value(),x.getOrderStatus())){
                    x.setAgainOrder(Boolean.TRUE);
                }
                //自助取还
            }else if(Objects.equals(OrderEnum.OrderType.INTERNAL_ORDER.value(),x.getOrderType()) &&
                    Objects.equals(OrderEnum.OrderServiceType.SELF_HELP_RENT.value(),x.getServiceCode())){
                //版本 113 之前的自助取还列表不允许展示取消和撤回按钮
                final int versionLimit = 113;
                Integer appVersion = orderApplyQueryParam.getAppVersion();
                boolean versionCondition = appVersion != null && appVersion >= versionLimit;
                if(versionCondition && Objects.equals(OrderEnum.OrderStatus.DISPATCHED.value(),x.getOrderStatus())){
                    x.setCancelOrder(Boolean.TRUE);
                }
                if(versionCondition && Objects.equals(OrderEnum.OrderApplyStatus.WAIT_AUTH.value(),x.getOrderStatus())){
                    x.setRevertOrder(Boolean.TRUE);
                }
            }
            //子行程编码
            List<OrderInfo> orderInfoList=  orderInfoMap.get(x.getOrderApplyNo());
            if(CollectionUtil.isNotEmpty(orderInfoList)){
                final OrderInfo orderInfo = orderInfoList.get(0);
                x.setOrderNo(orderInfo.getOrderNo());
                x.setAssignCarLicense(orderInfo.getAssignCarLicense());
            }
            if(Objects.equals(LanguageEnum.EN.getCode(),lang)){
                OrderEnum.OrderApplyStatus orderApplyStatus=OrderEnum.OrderApplyStatus.getByValue(x.getOrderStatus());
                x.setOrderStatusName(Objects.nonNull(orderApplyStatus)?orderApplyStatus.textEn():"");
                String serviceCode=x.getServiceCode();
                if(StringUtils.isNotBlank(serviceCode)){
                    OrderEnum.OrderServiceType orderServiceType= OrderEnum.OrderServiceType.getByValue(serviceCode);
                    x.setServiceName(Objects.isNull(orderServiceType)?"":orderServiceType.getEnName());
                }
                Byte orderType=x.getOrderType();
                if(Objects.nonNull(orderType)){
                    OrderEnum.OrderTypeForApp orderTypeForApp= OrderEnum.OrderTypeForApp.getByValue(orderType);
                    x.setOrderTypeName(Objects.nonNull(orderTypeForApp)?orderTypeForApp.textEn():"");
                }

            }
        });
        //log.info("【APP查询我的行程列表】返回结果："+JSON.toJSONString(orderApplyDTOS));
        return new PageDTO(page,pageSize,total,orderApplyDTOS);
    }

    public PageDTO queryMyOrderInfoList(OrderApplyQueryParam orderApplyQueryParam) {
        int total;
        int page = orderApplyQueryParam.getPageNum();
        int pageSize = orderApplyQueryParam.getPageSize();
        String lang = orderApplyQueryParam.getLanguage();
        List<OrderInfo> orderInfoList ;
        Page<?> p = PageHelper.startPage(page, pageSize, true);
        try {
            orderInfoList = orderInfoExMapper.selectMyOrderInfoForBussinessList(orderApplyQueryParam);
            total = (int) p.getTotal();
        } finally {
            PageHelper.clearPage();
        }
        if (CollUtil.isEmpty(orderInfoList)) {
            PageDTO pageDto = new PageDTO(page, pageSize, total, new ArrayList<>());
            return pageDto;
        }
        List<MyOrderInfoListDTO> myOrderInfoList=BeanUtil.copyList(orderInfoList,MyOrderInfoListDTO.class);
        myOrderInfoList.forEach(x -> {
            x.setServiceName(OrderEnum.OrderServiceType.getTextByValue(x.getServiceCode()));
            x.setOrderTypeName(OrderEnum.OrderType.getTextByType(x.getOrderType()));
            x.setOrderStatusName(OrderEnum.OrderStatus.getTextByValue(x.getOrderStatus()));
            if (x.getOrderStatus()>= OrderEnum.OrderApplyStatus.END_TRIP.value() && !x.getAppraiseSubmited() && x.getOrderStatus()!=OrderEnum.OrderApplyStatus.CANCEL.value()) {
                x.setIsAppraised(0);
            } else {
                x.setIsAppraised(1);
            }
            x.setBookingOrderEtimeApp(DateUtil.format(x.getBookingOrderEtime(), DateUtil.TIMESTAMP_CHINA_APP_ENGLISH));
            x.setBookingOrderStimeApp(DateUtil.format(x.getBookingOrderStime(),  DateUtil.TIMESTAMP_CHINA_APP_ENGLISH));
            //app列表 展示按钮逻辑
            showButton(x);
            if(Objects.equals(LanguageEnum.EN.getCode(),lang)){
                OrderEnum.OrderApplyStatus orderApplyStatus=OrderEnum.OrderApplyStatus.getByValue(x.getOrderStatus());
                x.setOrderStatusName(Objects.nonNull(orderApplyStatus)?orderApplyStatus.textEn():"");
                String serviceCode=x.getServiceCode();
                if(StringUtils.isNotBlank(serviceCode)){
                    OrderEnum.OrderServiceType orderServiceType= OrderEnum.OrderServiceType.getByValue(serviceCode);
                    x.setServiceName(Objects.isNull(orderServiceType)?"":orderServiceType.getEnName());
                }
                Byte orderType=x.getOrderType();
                if(Objects.nonNull(orderType)){
                    OrderEnum.OrderTypeForApp orderTypeForApp= OrderEnum.OrderTypeForApp.getByValue(orderType);
                    x.setOrderTypeName(Objects.nonNull(orderTypeForApp)?orderTypeForApp.textEn():"");
                }
            }

            //如果是商务车的话，添加订单评价地址
            if(Objects.equals(OrderType.MOTORCADE_ORDER.value(),x.getOrderType())){
                String orderEvaluationUrl = orderEvaluationService.getOrderEvaluationUrl(x.getOrderNo());
                x.setEvaluateUrl(orderEvaluationUrl);
            }
        });
        return new PageDTO(page,pageSize,total,myOrderInfoList);

    }

    private void showButton(MyOrderInfoListDTO x){
        //app列表 展示按钮逻辑
        //再次下单逻辑
        x.setCancelOrder(Boolean.FALSE);
        x.setImmediateTrip(Boolean.FALSE);
        x.setStartNavigation(Boolean.FALSE);
        x.setFinishTrip(Boolean.FALSE);
        x.setAgainOrder(Boolean.FALSE);
        x.setRevertOrder(Boolean.FALSE);
        if(Objects.equals(OrderEnum.OrderType.INTERNAL_ORDER.value(),x.getOrderType())){
            if(Objects.equals(OrderEnum.OrderServiceType.ONCE.value(),x.getServiceCode()) || Objects.equals(OrderEnum.OrderServiceType.ONE_DAY_RENT.value(),x.getServiceCode())){
                x.setAgainOrder(Boolean.TRUE);
            }
        }
        //私车公用
        if(Objects.equals(OrderEnum.OrderType.PRIVATE_ORDER.value(),x.getOrderType())){
            //待出发
            if(Objects.equals(OrderEnum.OrderStatus.DISPATCHED.value(),x.getOrderStatus())){
                x.setCancelOrder(Boolean.TRUE);
                x.setImmediateTrip(Boolean.TRUE);
            }
            //行程中
            if(Objects.equals(OrderEnum.OrderStatus.DURING_TRIP.value(),x.getOrderStatus())){
                x.setStartNavigation(Boolean.TRUE);
                x.setFinishTrip(Boolean.TRUE);
            }
            //已完成
            if(Objects.equals(OrderEnum.OrderStatus.COMPLETED.value(),x.getOrderStatus())){
                x.setAgainOrder(Boolean.TRUE);
            }
            //自助取还
        }else if(Objects.equals(OrderEnum.OrderType.INTERNAL_ORDER.value(),x.getOrderType()) &&
                Objects.equals(OrderEnum.OrderServiceType.SELF_HELP_RENT.value(),x.getServiceCode())){

            if( Objects.equals(OrderEnum.OrderStatus.DISPATCHED.value(),x.getOrderStatus())){
                x.setCancelOrder(Boolean.TRUE);
            }
            OrderApply orderApply = orderApplyExMapper.selectOneByOrderApplyNo(x.getOrderApplyNo());
            if(Objects.equals(OrderEnum.OrderApplyStatus.WAIT_AUTH.value(),orderApply.getOrderStatus())){
                x.setRevertOrder(Boolean.TRUE);
            }
        }else if(Objects.equals(OrderEnum.OrderType.MOTORCADE_ORDER.value(),x.getOrderType())){
            x.setAgainOrder(Boolean.TRUE);
        }

    }

    public void buildDestinations(String destinations, OrderApply orderApply) {
        //如果是多目的地，创建多目的地信息
        List<OrderApplyDestination> listD = new ArrayList<>();
        //起点也需要加入目的地表
        OrderApplyDestination startDestination = new OrderApplyDestination();
        startDestination.setBookingEndLongAddr(orderApply.getBookingStartLongAddr());
        startDestination.setBookingEndShortAddr(orderApply.getBookingStartShortAddr());
        startDestination.setBookingEndPoint(orderApply.getBookingStartPoint());
        startDestination.setBookingEndCityCode(orderApply.getBookingStartCityCode() != null ? orderApply.getBookingStartCityCode().toString() : StringUtils.EMPTY);
        startDestination.setBookingEndCityName(orderApply.getBookingStartCityName());
        startDestination.setSort(0);
        startDestination.setAddressType(AddressTypeEnum.START_POINT.getType());
        listD.add(startDestination);

        List<OrderApplyDestination> destinationsReqList = new ArrayList<>();
        if (StringUtils.isNotBlank(destinations)) {
            destinationsReqList = JSON.parseArray(destinations, OrderApplyDestination.class);
        }
        for (int i = 0; i < destinationsReqList.size(); i++) {
            OrderApplyDestination orderApplyDestination = destinationsReqList.get(i);
            if (i == destinationsReqList.size() - 1) {
                orderApplyDestination.setAddressType(AddressTypeEnum.DESTINATION_POINT.getType());
            } else {
                orderApplyDestination.setAddressType(AddressTypeEnum.WAY_POINT.getType());
            }
            orderApplyDestination.setSort(i + 1);
            listD.add(orderApplyDestination);
        }
        listD.forEach(e -> {
            e.setOrderApplyNo(orderApply.getOrderApplyNo());
            if (StringUtils.isBlank(e.getBookingEndCityCode())) {
                e.setBookingEndCityCode(StringUtils.EMPTY);
            }
            e.setTripType(OrderEnum.TripType.MAIN.getType());
            orderApplyDestinationExMapper.insertSelective(e);
        });
    }
}
