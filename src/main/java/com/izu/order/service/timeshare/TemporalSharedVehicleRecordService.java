package com.izu.order.service.timeshare;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.izu.carasset.CarAssetRestLocator;
import com.izu.carasset.CarAssetRestUrl;
import com.izu.carasset.dto.output.WorkdayHolidayDTO;
import com.izu.framework.web.exception.RestErrorException;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.iot.dto.BdTravelDTO;
import com.izu.lbs.common.LbsRestLocator;
import com.izu.mrcar.config.enums.ServiceDictionary;
import com.izu.mrcar.iot.config.IotRestLocator;
import com.izu.mrcar.iot.dto.CarWarnFenceDTO;
import com.izu.mrcar.iot.dto.location.CarLocationTrailReqDTO;
import com.izu.mrcar.iot.dto.warn.fence.TemporalSharedVehicleMessageDTO;
import com.izu.mrcar.iot.iotEnum.WarnType;
import com.izu.mrcar.order.consts.OrderEnum;
import com.izu.mrcar.order.consts.TemporalSharedVehicleRecordEnum;
import com.izu.mrcar.order.dto.mrcar.OrderApplyDestinationDTO;
import com.izu.mrcar.order.dto.mrcar.OrderApplyPassengerDTO;
import com.izu.mrcar.order.dto.temporalSharedVehicle.DataPermDTO;
import com.izu.mrcar.order.dto.temporalSharedVehicle.TemporalSharedVehicleRecordDTO;
import com.izu.mrcar.order.dto.temporalSharedVehicle.TemporalSharedVehicleRecordDetailReqDTO;
import com.izu.mrcar.order.dto.temporalSharedVehicle.TemporalSharedVehicleRecordPageReqDTO;
import com.izu.mrcar.order.dto.temporalSharedVehicle.TemporalSharedVehicleRecordQueryOrderReqDTO;
import com.izu.mrcar.order.dto.temporalSharedVehicle.TemporalSharedVehicleRecordQueryOrderResDTO;
import com.izu.mrcar.order.errcode.MrCarOrderErrorCode;
import com.izu.order.entity.mrcar.*;
import com.izu.order.rpc.AssetRpc;
import com.izu.respcode.CouponErrorCode;
import com.izu.user.enums.LoginSystemEnum;
import com.izu.user.restApi.CompanyApi;
import com.izu.user.util.DateTimeUtils;
import com.izu.user.util.ObjectTransferUtil;
import lombok.extern.slf4j.Slf4j;
import mapper.mrcar.ex.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.izu.mrcar.order.consts.TemporalSharedVehicleRecordEnum.OrderAssociateStatusEnum.ASSOCIATED;
import static com.izu.mrcar.order.consts.TemporalSharedVehicleRecordEnum.OrderAssociateStatusEnum.UNASSOCIATED;
import static com.izu.mrcar.order.consts.TemporalSharedVehicleRecordEnum.RecordStatusEnum.COMPLETED;
import static com.izu.mrcar.order.consts.TemporalSharedVehicleRecordEnum.RecordStatusEnum.INVALID;
import static com.izu.mrcar.order.consts.TemporalSharedVehicleRecordEnum.RecordStatusEnum.IN_PROGRESS;

/**
 * <AUTHOR> dongxiya 2024/1/27 15:28
 */
@Slf4j
@Service
public class TemporalSharedVehicleRecordService {

    private static final Logger logger = LoggerFactory.getLogger(TemporalSharedVehicleRecordService.class);


    // --------------------- public method ----------------------

    /**
     * 分页查询-pc
     */
    public RestResponse page(TemporalSharedVehicleRecordPageReqDTO dto) {
        if (Objects.nonNull(dto.getDurationBegin())) {
            int durationMin = dto.getDurationBegin().multiply(new BigDecimal(60)).setScale(0, RoundingMode.HALF_UP).intValue();
            dto.setDurationBeginMaxTime(new Date(new Date().getTime() - TimeUnit.MINUTES.toMillis(durationMin)));
        }
        if (Objects.nonNull(dto.getDurationEnd())) {
            int durationMax = dto.getDurationEnd().multiply(new BigDecimal(60)).setScale(0, RoundingMode.HALF_UP).intValue();
            dto.setDurationBeginMinTime(new Date(new Date().getTime() - TimeUnit.MINUTES.toMillis(durationMax)));
        }
        PageDTO<TemporalSharedVehicleRecordDTO> page = ObjectTransferUtil.cast(ObjectTransferUtil.page(dto.getPage(), dto.getPageSize(),
                Objects.equals(LoginSystemEnum.PROVIDER.getSys(), dto.getSystemType()) ? () -> recordMapper.queryPageProvider(dto) : () -> recordMapper.queryPage(dto)));
        // 关联其他信息
        if (CollectionUtils.isNotEmpty(page.getResult())) {
            final Map<String, String> carLevelList = AssetRpc.getAllCarLevelList();
            page.getResult().forEach(e -> {
                if (Objects.equals(LoginSystemEnum.PROVIDER.getSys(), dto.getSystemType())) {
                    if (Objects.equals(IN_PROGRESS.getCode(), e.getRecordStatus())) {
                        e.setButtonTypes("3");
                    } else if (Objects.equals(COMPLETED.getCode(), e.getRecordStatus())) {
                        e.setButtonTypes("2,3");
                    }
                }
                formatData(e, carLevelList);
            });
        }
        // 订单信息
        Set<String> orderNos = page.getResult().stream()
                .map(TemporalSharedVehicleRecordDTO::getOrderNo)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        if (!orderNos.isEmpty()) {
            // 查询订单详情
            List<OrderInfo> orderInfos = orderInfoMapper.queryByOrderNos(new ArrayList<>(orderNos));
            Map<String, OrderInfo> orderInfoMap = orderInfos.stream()
                    .collect(Collectors.toMap(OrderInfo::getOrderNo, Function.identity()));
            // 查询乘车人
            Set<String> orderApplyNos = orderInfos.stream().map(OrderInfo::getOrderApplyNo).collect(Collectors.toSet());
            List<OrderApplyPassengerDTO> orderApplyPassengers = orderApplyPassengerMapper.selectPassengersByOrderApplyNo(new ArrayList<>(orderApplyNos));
            Map<String, List<OrderApplyPassengerDTO>> passengerMap =
                    orderApplyPassengers.stream()
                            .collect(Collectors.groupingBy(OrderApplyPassengerDTO::getOrderApplyNo, Collectors.toList()));
            // 查询目的地
            List<OrderApplyDestination> orderApplyDestinations =
                    orderApplyDestinationMapper.selectDestinationsByOrderApplyListAndType(new ArrayList<>(orderApplyNos), Lists.newArrayList(1, 2, 3));
            Map<String, List<OrderApplyDestination>> destinationMap =
                    orderApplyDestinations.stream()
                            .collect(Collectors.groupingBy(OrderApplyDestination::getOrderApplyNo, Collectors.toList()));
            page.getResult().forEach(s -> {
                if (StringUtils.isBlank(s.getOrderNo())
                        || !orderInfoMap.containsKey(s.getOrderNo())) {
                    return;
                }
                OrderInfo orderInfo = orderInfoMap.get(s.getOrderNo());
                // 订单
                s.setOrderBookingStartTime(orderInfo.getBookingOrderStime());
                s.setOrderBookingEndTime(orderInfo.getBookingOrderEtime());
                s.setOrderDriverId(orderInfo.getAssignDriverId());
                s.setOrderDriverName(orderInfo.getAssignDriverName());
                s.setOrderDriverPhone(orderInfo.getAssignDriverPhone());
                s.setOrderDetail(orderInfo.getOrderDetail());
                // 乘车人
                List<OrderApplyPassengerDTO> passengers = passengerMap.get(orderInfo.getOrderApplyNo());
                if (Objects.nonNull(passengers) && !passengers.isEmpty()) {
                    // fix fastjson bug
                    s.setPassengers(passengers.stream().map(r -> {
                        OrderApplyPassengerDTO d = new OrderApplyPassengerDTO();
                        BeanUtils.copyProperties(r, d);
                        return d;
                    }).toArray(OrderApplyPassengerDTO[]::new));
                }
                // 特殊处理自助取还
                if (Objects.equals(orderInfo.getOrderType(), OrderEnum.OrderType.INTERNAL_ORDER.value()) &&
                        Objects.equals(orderInfo.getServiceCode(), OrderEnum.OrderServiceType.SELF_HELP_RENT.value())) {
                    OrderApplyPassengerDTO passenger = new OrderApplyPassengerDTO();
                    passenger.setBookingPassengerUserName(orderInfo.getBookingPassengerUserName());
                    passenger.setBookingPassengerUserId(orderInfo.getBookingPassengerUserId().intValue());
                    passenger.setBookingPassengerUserPhone(orderInfo.getBookingPassengerUserPhone());
                    s.setPassengers(new OrderApplyPassengerDTO[] { passenger });
                }
                // 目的地
                List<OrderApplyDestination> destinations = destinationMap.get(orderInfo.getOrderApplyNo());
                if (Objects.nonNull(destinations) && !destinations.isEmpty()) {
                    s.setDestinations(destinations.stream().map(r -> {
                        OrderApplyDestinationDTO d = new OrderApplyDestinationDTO();
                        BeanUtils.copyProperties(r, d);
                        return d;
                    }).toArray(OrderApplyDestinationDTO[]::new));
                }
            });
        }
        return RestResponse.success(page);
    }

    /**
     * 废除
     */
    @Transactional(rollbackFor = Exception.class)
    public RestResponse inValid(TemporalSharedVehicleRecordDetailReqDTO dto) {
        // 校验
        TemporalSharedVehicleRecord temporalSharedVehicleRecord = queryById(dto.getId());
        if (Objects.equals(TemporalSharedVehicleRecordEnum.RecordStatusEnum.INVALID.getCode(), temporalSharedVehicleRecord.getRecordStatus())) {
            return RestResponse.fail(MrCarOrderErrorCode.DISPATCH_NOTICE_USER_MESSAGE, "当前状态不允许操作");
        }
        if (StringUtils.isBlank(dto.getUpdateRemark())) {
            return RestResponse.fail(MrCarOrderErrorCode.DISPATCH_NOTICE_USER_MESSAGE, "备注不能为空");
        }
        // 修改用车记录表
        TemporalSharedVehicleRecord update = new TemporalSharedVehicleRecord();
        update.setId(dto.getId());
        update.setUpdateUserId(dto.getStaffId());
        update.setUpdateUserName(dto.getStaffName());
        update.setUpdateRemark(dto.getUpdateRemark());
        recordMapper.inValid(dto);
        // 级联修改日统计表
        usageMapper.updateRecordStatus(INVALID.getCode(), temporalSharedVehicleRecord.getWarnSn());
        // 记录统计变化
        triggerRecordModification(temporalSharedVehicleRecord);
        return RestResponse.success(true);
    }
    /**
     * 详情
     */
    public RestResponse detail(TemporalSharedVehicleRecordDetailReqDTO dto) {
        TemporalSharedVehicleRecord temporalSharedVehicleRecord = queryById(dto.getId());
        TemporalSharedVehicleRecordDTO temporalSharedVehicleRecordDTO = BeanUtil.copyObject(temporalSharedVehicleRecord, TemporalSharedVehicleRecordDTO.class);
        final Map<String, String> carLevelList = AssetRpc.getAllCarLevelList();
        formatData(temporalSharedVehicleRecordDTO, carLevelList);
        //添加设备ID SIM_NO 设备类型
        String restUrl = new IotRestLocator().getRestUrl("/warn/getWarnFenceById");
        HashMap<String, Object> param = new HashMap<>();
        param.put("id", temporalSharedVehicleRecord.getWarnId());
        RestResponse restResponse = RestClient.requestForObject(BaseHttpClient.HttpMethod.POST,
                restUrl, param, null, CarWarnFenceDTO.class);
        if(restResponse.isSuccess() && restResponse.getData() != null){
            CarWarnFenceDTO carWarnFenceDTO = (CarWarnFenceDTO) restResponse.getData();
            temporalSharedVehicleRecordDTO.setVehicleDeviceNo(carWarnFenceDTO.getDeviceNo());
            temporalSharedVehicleRecordDTO.setVehicleDeviceType(carWarnFenceDTO.getDeviceType());
            temporalSharedVehicleRecordDTO.setVehicleSimNo(carWarnFenceDTO.getSimNo());
        }
        return RestResponse.success(temporalSharedVehicleRecordDTO);
    }

    /**
     * 关联申请单查询
     */
    public RestResponse queryOrder(TemporalSharedVehicleRecordQueryOrderReqDTO dto) {
        TemporalSharedVehicleRecord temporalSharedVehicleRecord = queryById(dto.getId());
        dto.setVehicleLicense(temporalSharedVehicleRecord.getVehicleLicense());
        dto.setVehicleCompanyId(temporalSharedVehicleRecord.getVehicleCompanyId());
        PageDTO<OrderInfo> page = ObjectTransferUtil.cast(ObjectTransferUtil.page(dto.getPage(), dto.getPageSize(), () -> orderInfoMapper.queryTemporalSharedVehicleRecordOrders(dto)));
        PageDTO<TemporalSharedVehicleRecordQueryOrderResDTO> result = ObjectTransferUtil.cast(BeanUtil.copyObject(page, PageDTO.class));

        if (CollectionUtils.isNotEmpty(page.getResult())) {
            result.setResult(page.getResult().stream().map(e -> {
                TemporalSharedVehicleRecordQueryOrderResDTO recordQueryOrderResDTO = BeanUtil.copyObject(e, TemporalSharedVehicleRecordQueryOrderResDTO.class);
                OrderEnum.OrderTypeForApp orderType = OrderEnum.OrderTypeForApp.getByValue(e.getOrderType());
                recordQueryOrderResDTO.setOrderTypeName(Objects.nonNull(orderType) ? orderType.text() : "");
                OrderEnum.OrderStatus orderStatus = OrderEnum.OrderStatus.getByValue(e.getOrderStatus());
                recordQueryOrderResDTO.setOrderStatusName(Objects.nonNull(orderStatus) ? orderStatus.text() : "");
                if (Objects.equals(temporalSharedVehicleRecord.getOrderAssociateStatus(), ASSOCIATED.getCode())
                        && Objects.equals(temporalSharedVehicleRecord.getOrderNo(), e.getOrderNo())) {
                    recordQueryOrderResDTO.setChecked(true);
                }
                return recordQueryOrderResDTO;
            }).collect(Collectors.toList()));
        }
        return RestResponse.success(result);
    }

    /**
     * 申请单关联
     */
    @Transactional(rollbackFor = Exception.class)
    public RestResponse associationOrder(TemporalSharedVehicleRecordDetailReqDTO dto) {
        TemporalSharedVehicleRecord record = queryById(dto.getId());
        if (Objects.equals(TemporalSharedVehicleRecordEnum.RecordStatusEnum.INVALID.getCode(), record.getRecordStatus())) {
            return RestResponse.fail(MrCarOrderErrorCode.DISPATCH_NOTICE_USER_MESSAGE, "当前状态不允许操作");
        }
        if (StringUtils.isNotBlank(dto.getOrderNo())) {
            OrderInfo orderInfo = orderInfoMapper.selectOneByOrderNo(dto.getOrderNo());
            doLinkOrderInfo(record, orderInfo, dto);
        } else {
            doUnlinkOrderInfo(record, dto);
        }
        return RestResponse.success(1);
    }

    /**
     * 接收处理来自Iot的报警围栏的事件, 生成分时用车的记录信息.
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean onWarnFenceEvent(TemporalSharedVehicleMessageDTO dto) {
        return dto.getEventType() == 1 ?
                doStartTemporalSharedVehicleEvent(dto) : doStopTemporalSharedVehicleEvent(dto);
    }


    /**
     * 内部用车结束行程时, 尝试关联用车记录
     */
    @Transactional
    public void onEndTravel(OrderInfo order) {
        // 仅针对内部用车
        if (order.getOrderType() != 1) {
            return;
        }
        // 查询所有可能会关联的用车记录
        // 1. 车牌号相同
        // 2. 没有关联过子订单
        TemporalSharedVehicleRecordExample cond = new TemporalSharedVehicleRecordExample();
        TemporalSharedVehicleRecordExample.Criteria criteria =
                cond.createCriteria()
                        .andVehicleLicenseNotEqualTo(order.getAssignCarLicense())
                        .andOrderAssociateStatusEqualTo(UNASSOCIATED.getCode())
                        .andRecordStatusEqualTo(COMPLETED.getCode());
        String serviceCode = order.getServiceCode();
        // 单程、整日、多日
        if (Objects.equals(serviceCode, String.valueOf(ServiceDictionary.SINGLE_TIME.getValue()))
                || Objects.equals(serviceCode, String.valueOf(ServiceDictionary.DAY.getValue()))
                || Objects.equals(serviceCode, String.valueOf(ServiceDictionary.DAYS.getValue()))) {
            criteria.andWarnStartTimeBetween(order.getBookingOrderStime(), order.getBookingOrderEtime());
        } else if (Objects.equals(serviceCode, String.valueOf(ServiceDictionary.SELF_HELP_RENT.getValue()))) {
            // 自助取还
            criteria.andWarnStartTimeBetween(order.getCreateTime(), order.getFactEndDate());
        } else {
            return;
        }
        // 所有可能会触发关联的用车记录
        List<TemporalSharedVehicleRecord> records = recordMapper.selectByExample(cond);
        // 根据用车记录, 反查订单
        // 考虑一般情况下, 存在满足条件的用车记录单数量不会很多, 简化处理, 采用循环更新的方式进行
        for (TemporalSharedVehicleRecord record : records) {
            List<OrderInfo> orders = findAllCandidateApplyOrder(record);
            if (orders.size() == 1) {
                // 恰好能够匹配当前的子行程
                OrderInfo orderInfo = orders.get(0);
                // 更新用车记录
                populateOrderInfo(record, orderInfo);
                record.setOrderAssociateStatus(ASSOCIATED.getCode()); // 已关联
                record.setUpdateTime(new Date());
                record.setUpdateUserId(0);
                record.setUpdateUserName("system");
                recordMapper.updateByPrimaryKeySelective(record);
            }
        }
    }

    // 关联子行程数据
    public boolean doLinkOrderInfo(TemporalSharedVehicleRecord record,
                                   OrderInfo orderInfo,
                                   DataPermDTO permDTO) {
        if (Objects.isNull(orderInfo)) {
            return false;
        }
        // 子行程车牌号 = 用车记录车牌号
        // 申请人企业 = 用车记录中的车辆所在企业
        if (!Objects.equals(record.getVehicleLicense(), orderInfo.getAssignCarLicense())
                || !Objects.equals(record.getVehicleCompanyId(), orderInfo.getCompanyId())) {
            return false;
        }
        // 如果
        if (Objects.equals(record.getOrderNo(), orderInfo.getOrderNo())) {
            return false;
        }
        // 更新订单信息
        populateOrderInfo(record, orderInfo);

        record.setOrderAssociateStatus(ASSOCIATED.getCode()); // 2-已关联
        record.setUpdateTime(new Date());
        record.setUpdateUserId(permDTO.getStaffId());
        record.setUpdateUserName(permDTO.getStaffName());

        return recordMapper.updateByPrimaryKeySelective(record) > 0;
    }

    // 取消关联子行程数据
    public boolean doUnlinkOrderInfo(TemporalSharedVehicleRecord record,
                                     DataPermDTO permDTO) {
        if (Objects.equals(record.getOrderAssociateStatus(),
                UNASSOCIATED.getCode())) {
            return false;
        }

        clearOrderInfo(record);
        record.setOrderAssociateStatus(UNASSOCIATED.getCode()); // 2-已关联
        record.setUpdateTime(new Date());
        record.setUpdateUserId(permDTO.getStaffId());
        record.setUpdateUserName(permDTO.getStaffName());

        return recordMapper.updateByPrimaryKey(record) > 0;
    }

    // -------------- fields ---------------------

    @Autowired
    private TemporalSharedVehicleRecordExMapper recordMapper;
    @Autowired
    private TemporalSharedVehicleDailyUsageExMapper usageMapper;
    @Autowired
    private VehicleBaseExMapper vehicleMapper;
    @Autowired
    private OrderInfoExMapper orderInfoMapper;
    @Autowired
    private TemporalSharedVehicleStatHelperService helperService;
    @Autowired
    private OrderApplyPassengerExMapper orderApplyPassengerMapper;
    @Autowired
    private OrderApplyDestinationExMapper orderApplyDestinationMapper;

    // ----------------- private methods --------------------

    private void formatData(TemporalSharedVehicleRecordDTO dto, Map<String, String> carLevelList) {
        dto.setDurationHour(!Objects.equals(IN_PROGRESS.getCode(), dto.getRecordStatus()) ? dto.getDurationHour() : divide(new BigDecimal(dto.getDuration()), (BigDecimal.valueOf(60))));
        dto.setRecordStatusName(TemporalSharedVehicleRecordEnum.RecordStatusEnum.getValueByCode(dto.getRecordStatus()));
        dto.setOrderAssociateStatusName(TemporalSharedVehicleRecordEnum.OrderAssociateStatusEnum.getValueByCode(dto.getOrderAssociateStatus()));
        OrderEnum.OrderTypeForApp orderType = OrderEnum.OrderTypeForApp.getByValue(dto.getOrderType());
        dto.setOrderTypeName(Objects.nonNull(orderType) ? orderType.text() : "");
        WarnType warnType = WarnType.getWarnTypeEnum(dto.getFenceWarnType());
        dto.setFenceWarnTypeName(warnType.getValueStr());
        dto.setVehicleTypeName(carLevelList.get(dto.getVehicleType() + ""));
    }

    private TemporalSharedVehicleRecord queryById(Long id) {
        TemporalSharedVehicleRecord temporalSharedVehicleRecord = recordMapper.selectByPrimaryKey(id);
        if (Objects.isNull(temporalSharedVehicleRecord)) {
            throw new RestErrorException("数据不存在", CouponErrorCode.TEMPLATE_ERROR);
        }
        return temporalSharedVehicleRecord;
    }

    private boolean doStartTemporalSharedVehicleEvent(TemporalSharedVehicleMessageDTO dto) {
        // 检查幂等
        TemporalSharedVehicleRecord record = recordMapper.findOneByWarnSn(dto.getWarnSn());
        if (Objects.nonNull(record)) {
            return false;
        }
        record = new TemporalSharedVehicleRecord();
        // 报警信息
        record.setWarnSn(dto.getWarnSn()); // 报警编号
        record.setWarnId(dto.getWarnId()); // 报警表ID
        record.setRecordStatus(IN_PROGRESS.getCode()); // 单据状态 1-进行中
        record.setWarnStartTime(dto.getWarnStartTime()); // 开始时间
        record.setWarnStartLatitude(dto.getStartLatitude());
        record.setWarnStartLongitude(dto.getStartLongitude());
        record.setOrderAssociateStatus(UNASSOCIATED.getCode()); // 关联状态 1-未关联
        record.setCreateTime(new Date());
        record.setUpdateUserId(0);
        record.setUpdateUserName("system");
        record.setUpdateTime(new Date());
        // 车辆信息
        VehicleBase vehicleInfo = vehicleMapper.selectByPrimaryKey(dto.getVehicleId());
        if (Objects.isNull(vehicleInfo)) {
            logger.warn("未能匹配到车辆信息, warn_sn : {}, vehicle_id : {}", dto.getWarnSn(), dto.getVehicleId());
            return false;
        }
        populateVehicleInfo(record, vehicleInfo);
        // 围栏信息
        record.setFenceId(dto.getFenceId());
        record.setFenceName(dto.getFenceName());
        record.setFenceCityCode(dto.getFenceCityCode());
        record.setFenceCityName(dto.getFenceCityName());
        record.setFenceWarnType(dto.getFenceWarnType().byteValue());
        record.setFenceAddress(dto.getFenceAddress());

        return recordMapper.insertSelective(record) > 0;
    }

    private boolean doStopTemporalSharedVehicleEvent(TemporalSharedVehicleMessageDTO dto) {
        TemporalSharedVehicleRecord record = recordMapper.findOneByWarnSn(dto.getWarnSn());
        if (Objects.isNull(record)) {
            return false;
        }
        // 幂等校验
        if (record.getRecordStatus() == 2
                || Objects.nonNull(record.getWarnEndTime())) {
            return false;
        }
        // 报警信息
        if (Objects.equals(record.getRecordStatus(), IN_PROGRESS.getCode())) {
            record.setRecordStatus(COMPLETED.getCode());
        }
        record.setWarnEndTime(dto.getWarnEndTime()); // 报警结束时间
        record.setWarnEndLatitude(dto.getEndLatitude());
        record.setWarnEndLongitude(dto.getEndLongitude());
        record.setDuration(dto.getDuration()); // 持续时间
        record.setDurationHour(divide(new BigDecimal(dto.getDuration()), (BigDecimal.valueOf(60)))); // 持续时间(小时)
        record.setTripMileage(dto.getTripMileage()); // 里程
        record.setUpdateTime(new Date());
        record.setUpdateUserId(0);
        record.setUpdateUserName("system");
        // 重新更新车辆信息
        VehicleBase vehicleInfo = vehicleMapper.selectByPrimaryKey(dto.getVehicleId());
        if (Objects.nonNull(vehicleInfo)) {
            populateVehicleInfo(record, vehicleInfo);
        }
        // 如果是已被废除的用车记录, 仅更新主表信息, 不触发更新单日明细和匹配行为
        if (Objects.equals(record.getRecordStatus(), COMPLETED.getCode())) {
            // 按天拆分记录
            List<TemporalSharedVehicleDailyUsage> usages = splitDailyUsage(record);
            // 匹配子行程单
            List<OrderInfo> orders = findAllCandidateApplyOrder(record);
            if (orders.size() == 1) {
                // 恰好能够匹配当前的子行程
                OrderInfo order = orders.get(0);
                populateOrderInfo(record, order);
                record.setOrderAssociateStatus(ASSOCIATED.getCode()); // 已关联
            } else {
                record.setOrderAssociateStatus(UNASSOCIATED.getCode()); // 未关联
                if (!orders.isEmpty()) {
                    record.setOrderAssociateRemark("存在多条满足条件的申请单: " +
                            orders.stream().map(OrderInfo::getOrderNo).collect(Collectors.joining(",")));
                }
            }
            // 批量插入按天统计记录
            usageMapper.batchInsertDailyUsage(usages);
        }
        // 更新用车记录表
        recordMapper.updateByPrimaryKeySelective(record);
        // 触发重新统计的需求
        triggerRecordModification(record);
        return true;
    }

    private List<TemporalSharedVehicleDailyUsage> splitDailyUsage(TemporalSharedVehicleRecord record) {
        List<TemporalSharedVehicleDailyUsage> result = new ArrayList<>();

        Instant start = record.getWarnStartTime().toInstant();
        Instant end = record.getWarnEndTime().toInstant();

        LocalDate curDay = start.atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate endDay = end.atZone(ZoneId.systemDefault()).toLocalDate();
        LocalTime curTime = start.atZone(ZoneId.systemDefault()).toLocalTime();
        LocalTime endTime = end.atZone(ZoneId.systemDefault()).toLocalTime();
        // 节假日信息
        Map<LocalDate, WorkdayHolidayDTO> holidays =
                queryForHolidays(curDay, endDay).stream()
                        .collect(Collectors.toMap(
                                entry -> entry.getCalendarDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate(),
                                Function.identity()));

        int cumulativeDuration = 0;
        BigDecimal cumulativeTripMileage = BigDecimal.ZERO;

        while (!curDay.isAfter(endDay)) {

            TemporalSharedVehicleDailyUsage usage = new TemporalSharedVehicleDailyUsage();
            usage.setWarnSn(record.getWarnSn());
            usage.setDayTime(Date.from(curDay.atStartOfDay(ZoneId.systemDefault()).toInstant()));
            usage.setDayType((byte) (holidays.get(curDay).getIsHoliday() ? 2 : 1));
            usage.setVehicleId(record.getVehicleId());
            usage.setVehicleLicense(record.getVehicleLicense());
            usage.setVehicleVin(record.getVehicleVin());
            usage.setVehicleBelongCityCode(record.getVehicleBelongCityCode());
            usage.setVehicleBelongCityName(record.getVehicleBelongCityName());
            usage.setVehicleCompanyId(record.getVehicleCompanyId());
            usage.setVehicleCompanyName(record.getVehicleCompanyName());
            usage.setVehicleStructId(record.getVehicleStructId());
            usage.setVehicleStructName(record.getVehicleStructName());
            usage.setVehicleBrandId(record.getVehicleBrandId());
            usage.setVehicleBrandCode(record.getVehicleBrandCode());
            usage.setVehicleBrandName(record.getVehicleBrand());
            usage.setVehicleCreatorId(record.getVehicleCreatorId());
            usage.setVehicleCreatorName(record.getVehicleCreatorName());
            usage.setRecordStatus(record.getRecordStatus());

            if (curDay.isEqual(endDay)) {
                // 最后一天
                usage.setDailyStartTime(Date.from(curDay.atTime(curTime).atZone(ZoneId.systemDefault()).toInstant()));
                usage.setDailyEndTime(Date.from(endDay.atTime(endTime).atZone(ZoneId.systemDefault()).toInstant()));
                // 计算时长和里程数
                usage.setDailyDuration(record.getDuration() - cumulativeDuration);
                usage.setDailyTripMileage(record.getTripMileage().subtract(cumulativeTripMileage)
                        .setScale(2, RoundingMode.UNNECESSARY));
            } else {
                usage.setDailyStartTime(Date.from(curDay.atTime(curTime).atZone(ZoneId.systemDefault()).toInstant()));
                usage.setDailyEndTime(Date.from(curDay.atTime(LocalTime.of(23, 59, 59)).atZone(ZoneId.systemDefault()).toInstant()));
                // 里程数
                BigDecimal tripMileage = computeVehicleMileage(record.getVehicleLicense(),
                        record.getVehicleVin(),
                        record.getVehicleDeviceNo(),
                        String.valueOf(record.getVehicleDeviceType()),
                        usage.getDailyStartTime(),
                        usage.getDailyEndTime());
                // 时长
                int duration = computeDuration(usage.getDailyStartTime(), usage.getDailyEndTime());
                usage.setDailyDuration(duration);
                usage.setDailyTripMileage(tripMileage);

                cumulativeDuration += duration;
                cumulativeTripMileage = cumulativeTripMileage.add(tripMileage);
            }

            curDay = curDay.plusDays(1);
            curTime = LocalTime.MIDNIGHT;

            result.add(usage);
        }

        return result;
    }

    private void populateVehicleInfo(TemporalSharedVehicleRecord record, VehicleBase vehicleInfo) {
        record.setVehicleId(vehicleInfo.getVehicleId());
        record.setVehicleLicense(vehicleInfo.getVehicleLicense());
        record.setVehicleVin(vehicleInfo.getVehicleVin());
        record.setVehicleBrandId(vehicleInfo.getVehicleBrandId());
        record.setVehicleBrandCode(vehicleInfo.getVehicleBrandCode());
        record.setVehicleBrand(vehicleInfo.getVehicleBrand());
        record.setVehicleModelId(vehicleInfo.getVehicleBrandId());
        record.setVehicleModelCode(vehicleInfo.getVehicleModelCode());
        record.setVehicleModel(vehicleInfo.getVehicleModel());
        record.setVehicleType(vehicleInfo.getVehicleType());
        record.setVehicleBelongCityCode(vehicleInfo.getBelongCityCode());
        record.setVehicleBelongCityName(vehicleInfo.getBelongCityName());
        record.setVehicleCompanyId(vehicleInfo.getCompanyId());
        // 查询企业名称
        record.setVehicleCompanyName(CompanyApi.getCompanyById(vehicleInfo.getCompanyId()).getCompanyName());
        record.setVehicleStructId(vehicleInfo.getStructId());
        record.setVehicleStructName(vehicleInfo.getStructName());
        record.setVehicleSelfOwned(vehicleInfo.getSelfOwned());
        record.setVehicleWorkingStatus(vehicleInfo.getWorkingStatus());
        record.setVehicleOwnStructId(vehicleInfo.getOwnStructId());
        record.setVehicleOwnStructName(vehicleInfo.getOwnStructName());
        record.setVehicleBelongStructCode(vehicleInfo.getBelongStructCode());
        record.setVehicleBelongStructName(vehicleInfo.getBelongStructName());
        record.setVehicleBelongBussCode(vehicleInfo.getBelongBussCode());
        record.setVehicleBelongBussName(vehicleInfo.getBelongBussName());
        record.setVehicleOperateBussCode(vehicleInfo.getOperateBussCode());
        record.setVehicleOperateBussName(vehicleInfo.getOperateBussName());
        record.setVehicleOperateStructCode(vehicleInfo.getOperateStructCode());
        record.setVehicleOperateStructName(vehicleInfo.getOperateStructName());
        record.setVehicleCreatorId(vehicleInfo.getCreterId());
        record.setVehicleCreatorName(vehicleInfo.getCreterName());
        record.setVehicleAssetCityCode(vehicleInfo.getAssetCityCode());
        record.setVehicleAssetCityName(vehicleInfo.getAssetCityName());
    }

    private void populateOrderInfo(TemporalSharedVehicleRecord record, OrderInfo orderInfo) {
        record.setOrderNo(orderInfo.getOrderNo());
        record.setOrderApplyNo(orderInfo.getOrderApplyNo());
        record.setOrderCompanyId(orderInfo.getCompanyId());
        record.setOrderStructId(orderInfo.getStructId());
        record.setOrderStructName(orderInfo.getStructName());
        record.setOrderType(orderInfo.getOrderType());
        record.setOrderCustomerId(orderInfo.getCustomerId());
        record.setOrderCustomerName(orderInfo.getCustomerName());
        record.setOrderCustomerMobile(orderInfo.getCustomerMobile());
    }

    private void clearOrderInfo(TemporalSharedVehicleRecord record) {
        record.setOrderNo(null);
        record.setOrderApplyNo(null);
        record.setOrderCompanyId(null);
        record.setOrderStructId(null);
        record.setOrderStructName(null);
        record.setOrderType(null);
        record.setOrderCustomerId(null);
        record.setOrderCustomerName(null);
        record.setOrderCustomerMobile(null);
    }

    // 查找所有符合条件的候选申请单记录
    private List<OrderInfo> findAllCandidateApplyOrder(TemporalSharedVehicleRecord record) {
        List<OrderInfo> candidates = new ArrayList<>();
        // 内部用车
        // 1. 单程、整日、多日
        // 检验范围：行程的【预约开始时间】和【预约结束时间】
        // 行程状态：行程中、行程已结束、账单待确认、账单已驳回、已完成
        // 调度车辆 = 用车记录车牌号
        OrderInfoExample cond1 = new OrderInfoExample();
        cond1.createCriteria()
                .andOrderTypeEqualTo((byte) 1)
                .andServiceCodeIn(
                        Lists.newArrayList(
                                String.valueOf(ServiceDictionary.SINGLE_TIME.getValue()),
                                String.valueOf(ServiceDictionary.DAY.getValue()),
                                String.valueOf(ServiceDictionary.DAYS.getValue())))
                .andOrderStatusIn(
                        Lists.newArrayList(
                                com.izu.dispatch.consts.OrderEnum.OrderStatus.DRIVER_START.value(),
                                com.izu.dispatch.consts.OrderEnum.OrderStatus.DRIVER_ARRIVE.value(),
                                com.izu.dispatch.consts.OrderEnum.OrderStatus.DURING_TRIP.value(),
                                com.izu.dispatch.consts.OrderEnum.OrderStatus.END_TRIP.value(),
                                com.izu.dispatch.consts.OrderEnum.OrderStatus.WAIT_CONFIRM.value(),
                                com.izu.dispatch.consts.OrderEnum.OrderStatus.REJECT.value(),
                                com.izu.dispatch.consts.OrderEnum.OrderStatus.WAIT_ACCOUNT.value(),
                                com.izu.dispatch.consts.OrderEnum.OrderStatus.COMPLETED.value()))
                .andAssignCarLicenseEqualTo(record.getVehicleLicense())
                .andBookingOrderStimeLessThanOrEqualTo(record.getWarnStartTime())
                .andBookingOrderEtimeGreaterThanOrEqualTo(record.getWarnStartTime());
        candidates.addAll(orderInfoMapper.selectByExample(cond1));
        // 2. 自助取还
        // 检验范围：行程的【创建时间 - 完成时间】
        // 行程状态：行程中
        // 车辆=用车记录车牌号
        OrderInfoExample cond2 = new OrderInfoExample();
        cond2.createCriteria()
                .andOrderTypeEqualTo((byte) 1)
                .andServiceCodeIn(
                        Lists.newArrayList(
                                String.valueOf(ServiceDictionary.SELF_HELP_RENT.getValue())))
                .andOrderStatusIn(
                        Lists.newArrayList(
                                com.izu.dispatch.consts.OrderEnum.OrderStatus.DURING_TRIP.value()))
                .andAssignCarLicenseEqualTo(record.getVehicleLicense())
                .andCreateTimeLessThanOrEqualTo(record.getWarnStartTime());
        candidates.addAll(orderInfoMapper.selectByExample(cond2));
        // 已完成
        OrderInfoExample cond3 = new OrderInfoExample();
        cond3.createCriteria()
                .andOrderTypeEqualTo((byte) 1)
                .andServiceCodeIn(
                        Lists.newArrayList(
                                String.valueOf(ServiceDictionary.SELF_HELP_RENT.getValue())))
                .andOrderStatusIn(
                        Lists.newArrayList(
                                com.izu.dispatch.consts.OrderEnum.OrderStatus.END_TRIP.value(),
                                com.izu.dispatch.consts.OrderEnum.OrderStatus.COMPLETED.value()))
                .andAssignCarLicenseEqualTo(record.getVehicleLicense())
                .andCreateTimeLessThanOrEqualTo(record.getWarnStartTime())
                .andFactEndDateGreaterThanOrEqualTo(record.getWarnStartTime());
        candidates.addAll(orderInfoMapper.selectByExample(cond3));
        log.info("查询所有的候选订单, order_no : {}", candidates.stream().map(OrderInfo::getOrderNo).collect(Collectors.toList()));
        return candidates;
    }

    private void triggerRecordModification(TemporalSharedVehicleRecord record) {
        // 忽略未结束的记录
        if (record.getWarnEndTime() == null) {
            return;
        }
        LocalDate curDay = record.getWarnStartTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate endDay = record.getWarnEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        Set<LocalDate> affectedDays = new HashSet<>();
        while (!curDay.isAfter(endDay)) {
            affectedDays.add(curDay);
            curDay = curDay.plusDays(1);
        }
        helperService.updateAffectedDay(LocalDate.now(), affectedDays);
    }

    // ---------------- utility methods ----------------

    public static BigDecimal divide(BigDecimal num1, BigDecimal num2) {
        return num1.divide(num2, 2, RoundingMode.HALF_UP);
    }

    private List<WorkdayHolidayDTO> queryForHolidays(LocalDate startDay, LocalDate endDay) {
        String restUrl = new CarAssetRestLocator().getRestUrl(CarAssetRestUrl.WORKDAY_HOLIDAY_QUERY_BY_RANGE);

        Map<String, Object> params =  new HashMap<String, Object>();
        params.put("beginDate", startDay.format(DateTimeFormatter.BASIC_ISO_DATE.withZone(ZoneId.systemDefault())));
        params.put("endDate", endDay.format(DateTimeFormatter.BASIC_ISO_DATE.withZone(ZoneId.systemDefault())));

        @SuppressWarnings("unchecked")
        RestResponse<List<WorkdayHolidayDTO>> response =
                RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, params, null, WorkdayHolidayDTO.class);
        return response.getData();
    }

    private BigDecimal computeVehicleMileage(String carNo,
                                             String vin,
                                             String deviceId,
                                             String deviceType,
                                             Date startTime,
                                             Date endTime) {
        SimpleDateFormat format = new SimpleDateFormat(DateTimeUtils.DEFAULT_DATE_TIME_FORMAT);

        CarLocationTrailReqDTO reqDTO = new CarLocationTrailReqDTO();
        reqDTO.setBeginCreateDate(format.format(startTime));
        reqDTO.setEndCreateDate(format.format(endTime));
        reqDTO.setCarNo(carNo);
        reqDTO.setCoordinate("BAIDU");
        reqDTO.setDeviceId(deviceId);
        reqDTO.setDeviceType(deviceType);
        reqDTO.setVehicleVin(vin);
        reqDTO.setDistinct(true);
        reqDTO.setStopInfo(true);

        String restUrl = new LbsRestLocator().getRestUrl("/car/location/trail/show/detail");
        Map<String, Object> params = JSON.parseObject(JSON.toJSONString(reqDTO));

        @SuppressWarnings("unchecked")
        RestResponse<BdTravelDTO> response = RestClient.requestForObject(
                BaseHttpClient.HttpMethod.POST, restUrl, params, null, BdTravelDTO.class);

        BdTravelDTO trail = response.getData();

        return (trail != null && trail.getDistance() != null) ? trail.getDistance() : BigDecimal.ZERO;
    }

    private int computeDuration(Date startTime,
                                Date endTime) {
        LocalDateTime start = startTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        LocalDateTime end = endTime.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        return (int) Duration.between(start, end).toMinutes();
    }

}
