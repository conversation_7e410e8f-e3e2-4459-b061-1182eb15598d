package com.izu.order.service.bus;

import com.izu.mrcar.order.dto.common.DrivingRoutePlanDTO;
import com.izu.mrcar.order.dto.common.GeoPoint;
import com.izu.mrcar.order.dto.mrcar.bus.BusOrderRouteSnapshotDTO;
import com.izu.order.entity.mongo.BusOrderDrivingRoutePlan;
import com.izu.order.util.BaiduMapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.FindAndReplaceOptions;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class BusOrderBaseService {

    @Autowired
    private BaiduMapService baiduMapService;
    @Autowired
    private MongoTemplate template;

    /**
     * 规划路径
     */
    public DrivingRoutePlanDTO planRoute(List<BusOrderRouteSnapshotDTO> stations,
                                         Integer routeType) {
        // 起始站
        BusOrderRouteSnapshotDTO startStation =
                stations.stream()
                        .filter(s -> Objects.equals(s.getStationType(), 0))
                        .findFirst()
                        .orElse(null);
        if (Objects.isNull(startStation)) {
            return null;
        }
        // 终点站
        BusOrderRouteSnapshotDTO endStation =
                stations.stream()
                        .filter(s -> Objects.equals(s.getStationType(), 2))
                        .findFirst()
                        .orElse(null);
        if (Objects.isNull(endStation)) {
            return null;
        }
        // 途径点
        List<BusOrderRouteSnapshotDTO> intermediateStations =
                stations.stream()
                        .filter(s -> Objects.equals(s.getStationType(), 1))
                        .collect(Collectors.toList());
        Integer tactics = 0;
        // 0-时间最短  2-距离最短
        if (Objects.equals(routeType, 1)) {
            tactics = 2;
        }

        // 规划
        GeoPoint origin = new GeoPoint();
        origin.setLatitude(startStation.getLatitude().toPlainString());
        origin.setLongitude(startStation.getLongitude().toPlainString());

        GeoPoint destination = new GeoPoint();
        destination.setLatitude(endStation.getLatitude().toPlainString());
        destination.setLongitude(endStation.getLongitude().toPlainString());

        List<GeoPoint> waypoints =
                intermediateStations.stream().map(s -> {
                    GeoPoint point = new GeoPoint();
                    point.setLatitude(s.getLatitude().toPlainString());
                    point.setLongitude(s.getLongitude().toPlainString());
                    return point;
                }).collect(Collectors.toList());

        try {
            return this.baiduMapService.drivingPlan(
                    origin,
                    destination,
                    null,
                    null,
                    waypoints,
                    tactics);
        } catch (IOException e) {
            return null;
        }
    }

    /**
     * 新增或更新路径规划
     */
    public void addOrUpdateRoutePlan(BusOrderDrivingRoutePlan record) {
        template.findAndReplace(
                Query.query(Criteria.where("orderNo").is(record.getOrderNo())),
                record,
                FindAndReplaceOptions.options().upsert());
    }

    /**
     * 根据订单查询历史规划信息
     */
    public BusOrderDrivingRoutePlan queryRoutePlanByOrderNo(String orderNo) {
        return this.template.findOne(
                Query.query(Criteria.where("orderNo").is(orderNo)),
                BusOrderDrivingRoutePlan.class);
    }

}
