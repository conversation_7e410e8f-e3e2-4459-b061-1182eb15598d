package com.izu.order.service.publicGovcar.handle;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.izu.asset.consts.VehicleEnum;
import com.izu.asset.dto.ModifyVehicleWorkingStatusReqDTO;
import com.izu.asset.restApi.order.OrderAssetRestApi;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.order.consts.FeedbackTypeEnum;
import com.izu.mrcar.order.consts.QuestionSourceEnum;
import com.izu.mrcar.order.consts.QuestionTypeEnum;
import com.izu.mrcar.order.consts.RequestSourceEnum;
import com.izu.mrcar.order.consts.govPublicCar.GovPublicCarOrderOperationTypeEnum;
import com.izu.mrcar.order.consts.govPublicCar.GovPublicCarOrderStatusEnum;
import com.izu.mrcar.order.consts.govPublicCar.GovPublicCarOrderUserTypeEnum;
import com.izu.mrcar.order.dto.order.OrderQuestionSubmitDTO;
import com.izu.mrcar.order.dto.publicGovcar.AppPublicGovCarCreateOrderReqDTO;
import com.izu.mrcar.workflow.common.dto.instance.BpmProcessInstanceCancelReqDTO;
import com.izu.mrcar.workflow.common.utils.WorkflowClient;
import com.izu.order.common.RestLocators;
import com.izu.order.entity.mrcar.GovPublicCarOrder;
import com.izu.order.entity.mrcar.GovPublicCarOrderAddressInfo;
import com.izu.order.entity.mrcar.GovPublicCarOrderAddressInfoExample;
import com.izu.order.entity.mrcar.GovPublicCarOrderExample;
import com.izu.order.entity.mrcar.GovPublicCarOrderUserInfo;
import com.izu.order.entity.mrcar.OrderOperationLog;
import com.izu.order.service.OrderQuestionService;
import com.izu.order.service.device.DeviceOperationService;
import com.izu.order.service.publicGovcar.handle.model.PublicGovCarOrderModifyTripHandleDTO;
import com.izu.order.service.publicGovcar.handle.model.PublicGovOrderHandleContext;
import com.izu.order.service.publicGovcar.handle.model.PublicGovOrderHandleDTO;
import lombok.extern.slf4j.Slf4j;
import mapper.mrcar.OrderOperationLogMapper;
import mapper.mrcar.ex.GovPublicCarOrderAddressInfoExMapper;
import mapper.mrcar.ex.GovPublicCarOrderExMapper;
import mapper.mrcar.ex.GovPublicCarOrderUserInfoExMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/8/18 10:31
 */
@Slf4j
public abstract class AbstractPublicGovCarOrderHandle implements PublicGovCarOrderHandle{


    @Resource
    protected TransactionTemplate transactionTemplate ;

    @Resource
    protected GovPublicCarOrderExMapper govPublicCarOrderExMapper;

    @Resource
    protected OrderOperationLogMapper orderOperationLogMapper;

    @Resource
    protected OrderQuestionService orderQuestionService;
    @Resource
    protected GovPublicCarOrderUserInfoExMapper orderUserInfoMapper;
    @Resource
    protected DeviceOperationService deviceOperationService;
    @Resource
    protected GovPublicCarOrderAddressInfoExMapper addressInfoMapper;


    @PostConstruct
    public void init() {
        PublicGovCarOrderHandleFactory.register(getOrderOptCode(), this);
    }

    @Override
    public boolean handle(PublicGovOrderHandleDTO handleDto) {
        PublicGovOrderHandleContext context = new PublicGovOrderHandleContext();
        context.setOrderNo(handleDto.getOrderNo());
        beforeHandle(handleDto, context);
        if(Objects.nonNull(context.getContinueNextStep()) && !context.getContinueNextStep()){
            return true;
        }
        doHandle(handleDto, context);
        afterHandle(handleDto, context);
        return true;
    }



    // 前置处理
    public void beforeHandle(PublicGovOrderHandleDTO handleDto, PublicGovOrderHandleContext context){};

    // 真正执行
    public void doHandle(PublicGovOrderHandleDTO handleDto, PublicGovOrderHandleContext context){};

    // 后置处理
    public void afterHandle(PublicGovOrderHandleDTO handleDto, PublicGovOrderHandleContext context){};

    public GovPublicCarOrder getOrder(String orderNo, Integer companyId, String companyCode){

        GovPublicCarOrderExample example = new GovPublicCarOrderExample();
        GovPublicCarOrderExample.Criteria criteria = example.createCriteria().andOrderNoEqualTo(orderNo);
        if(Objects.nonNull(companyId)){
            criteria.andCompanyIdEqualTo(companyId);
        }
        if(StringUtils.isNotBlank(companyCode)){
            criteria.andCompanyCodeEqualTo(companyCode);
        }

        List<GovPublicCarOrder> govCarOrderList = govPublicCarOrderExMapper.selectByExample(example);
        if(CollectionUtils.isEmpty(govCarOrderList)){
            return null;
        }
        return govCarOrderList.get(0);
    }


    public Boolean updateOrderStatus(GovPublicCarOrder govPublicCarOrder, Byte oldOrderStatus){

        GovPublicCarOrderExample example = new GovPublicCarOrderExample();
        example.createCriteria()
                .andOrderNoEqualTo(govPublicCarOrder.getOrderNo())
                .andOrderStatusEqualTo(oldOrderStatus);

        return govPublicCarOrderExMapper.updateByExampleSelective(govPublicCarOrder, example) > 0;
    }

    public Boolean addOrderOperationLog(PublicGovOrderHandleDTO handleDto,
                                        PublicGovOrderHandleContext context,
                                        Date updateDate,
                                        Byte orderStatus,
                                        GovPublicCarOrderOperationTypeEnum operationTypeEnum){

        OrderOperationLog orderOperationLog = new OrderOperationLog();
        orderOperationLog.setOrderId(Integer.parseInt(context.getGovPublicCarOrder().getOrderId() + ""));
        orderOperationLog.setOrderNo(context.getGovPublicCarOrder().getOrderNo());
        orderOperationLog.setOperatorId(Long.parseLong(handleDto.getOptUserId() + ""));
        orderOperationLog.setOperatorCode(handleDto.getOptUserCode());
        orderOperationLog.setOperatorName(handleDto.getOptUserName());
        orderOperationLog.setOperationType(operationTypeEnum.getCode());
        orderOperationLog.setOperationDescription(operationTypeEnum.getName());
        orderOperationLog.setOperationTime(updateDate);
        orderOperationLog.setOrderStatus(Integer.parseInt("" + orderStatus));
        orderOperationLog.setOrderStatusName(GovPublicCarOrderStatusEnum.getName(orderStatus));
        orderOperationLog.setOperatorMobile("");
        if(StringUtils.isNotBlank(handleDto.getOptUserMobile())){
            orderOperationLog.setOperatorMobile(handleDto.getOptUserMobile());
        }
        return orderOperationLogMapper.insertSelective(orderOperationLog) > 0;
    }


    protected GovPublicCarOrderAddressInfo buildAddressInfo(PublicGovCarOrderModifyTripHandleDTO param, PublicGovOrderHandleContext context) {

//        if(Objects.isNull(param.getEstimatedDestinationLatitude())
//        || Objects.isNull(param.getEstimatedDestinationLocation())
//        || StringUtils.isNotBlank(param.getEstimatedDestinationShortLocation())
//        || StringUtils.isNotBlank(param.getEstimatedDestinationLocation())){
//
//        }
        boolean addressModify = false;
        // 只有目标地址修改
        GovPublicCarOrderAddressInfo govPublicCarOrderAddressInfo = new GovPublicCarOrderAddressInfo();
        if(Objects.nonNull(param.getEstimatedDestinationLatitude()) && Objects.nonNull(param.getEstimatedDestinationLongitude())){
            addressModify = true;
            govPublicCarOrderAddressInfo.setEstimatedDestinationLatitude(param.getEstimatedDestinationLatitude());
            govPublicCarOrderAddressInfo.setEstimatedDestinationLongitude(param.getEstimatedDestinationLongitude());
        }
        if(StringUtils.isNotBlank(param.getEstimatedDestinationLocation())){
            addressModify = true;
            govPublicCarOrderAddressInfo.setEstimatedDestinationLocation(param.getEstimatedDestinationLocation());
        }
        if(StringUtils.isNotBlank(param.getEstimatedDestinationShortLocation())){
            addressModify = true;
            govPublicCarOrderAddressInfo.setEstimatedDestinationShortLocation(param.getEstimatedDestinationShortLocation());
        }

        if(Objects.nonNull(param.getEstimatedDepartureLatitude()) && Objects.nonNull(param.getEstimatedDepartureLongitude())){
            addressModify = true;
            govPublicCarOrderAddressInfo.setEstimatedDepartureLatitude(param.getEstimatedDepartureLatitude());
            govPublicCarOrderAddressInfo.setEstimatedDepartureLongitude(param.getEstimatedDepartureLongitude());
        }
        if(StringUtils.isNotBlank(param.getEstimatedDepartureLocation())){
            addressModify = true;
            govPublicCarOrderAddressInfo.setEstimatedDepartureLocation(param.getEstimatedDepartureLocation());
        }
        if(StringUtils.isNotBlank(param.getEstimatedDepartureShortLocation())){
            addressModify = true;
            govPublicCarOrderAddressInfo.setEstimatedDepartureShortLocation(param.getEstimatedDepartureShortLocation());
        }

        if(addressModify){
            govPublicCarOrderAddressInfo.setOrderNo(context.getOrderNo());
            govPublicCarOrderAddressInfo.setCreateTime(context.getUpdateDate());
            govPublicCarOrderAddressInfo.setUpdateTime(context.getUpdateDate());
            return govPublicCarOrderAddressInfo;
        }
        return null;
    }


    protected GovPublicCarOrderUserInfo buildDriverUserInfo(PublicGovCarOrderModifyTripHandleDTO param,
                                                            PublicGovOrderHandleDTO handleDTO,
                                                            PublicGovOrderHandleContext context) {
        if(Objects.isNull(param.getDriverId())
                || StringUtils.isBlank(param.getDriverCode())){
            return null;
        }
        GovPublicCarOrderUserInfo driverUserInfo = new GovPublicCarOrderUserInfo();
        driverUserInfo.setOrderNo(handleDTO.getOrderNo());
        driverUserInfo.setUserType(GovPublicCarOrderUserTypeEnum.DRIVER.getCode());
        driverUserInfo.setUserId(param.getDriverId());
        driverUserInfo.setUserCode(param.getDriverCode());
        driverUserInfo.setUserName(param.getDriverName());
        driverUserInfo.setUserMobile(param.getDriverMobile());
        driverUserInfo.setStructId(param.getDriverStructId());
        driverUserInfo.setStructCode(param.getDriverStructCode());
        driverUserInfo.setStructName(param.getDriverStructName());
        driverUserInfo.setCompanyCode(handleDTO.getCompanyCode());
        driverUserInfo.setCompanyName("");
        if(StringUtils.isNotBlank(handleDTO.getCompanyName())){
            driverUserInfo.setCompanyName(handleDTO.getCompanyName());
        }
        driverUserInfo.setCreateTime(context.getUpdateDate());
        driverUserInfo.setUpdateTime(context.getUpdateDate());
        return driverUserInfo;
    }


    protected List<GovPublicCarOrderUserInfo> buildPassengersUserInfo(PublicGovCarOrderModifyTripHandleDTO param,
                                         PublicGovOrderHandleDTO handleDTO,
                                         PublicGovOrderHandleContext context) {
        List<AppPublicGovCarCreateOrderReqDTO.PassengersInfo> passengersInfoList = param.getPassengersInfoList();
        if (CollectionUtils.isNotEmpty(passengersInfoList)) {
            List<GovPublicCarOrderUserInfo> list = new ArrayList<>(passengersInfoList.size());
            for (int i = 0; i < passengersInfoList.size(); i++) {
                AppPublicGovCarCreateOrderReqDTO.PassengersInfo passengersInfo = passengersInfoList.get(i);
                GovPublicCarOrderUserInfo govPublicCarOrderUserInfo = new GovPublicCarOrderUserInfo();
                govPublicCarOrderUserInfo.setOrderNo(handleDTO.getOrderNo());
                if (i == 0) {
                    govPublicCarOrderUserInfo.setUserType(GovPublicCarOrderUserTypeEnum.PRIMARY_RIDER.getCode());
                } else {
                    govPublicCarOrderUserInfo.setUserType(GovPublicCarOrderUserTypeEnum.SECONDARY_RIDER.getCode());
                }
                govPublicCarOrderUserInfo.setUserId(passengersInfo.getPassengerId());
                govPublicCarOrderUserInfo.setUserCode(passengersInfo.getPassengerCode());
                govPublicCarOrderUserInfo.setUserName(passengersInfo.getPassengerName());
                govPublicCarOrderUserInfo.setUserMobile(passengersInfo.getPassengerMobile());
                govPublicCarOrderUserInfo.setStructId(passengersInfo.getPassengerStructId());
                govPublicCarOrderUserInfo.setStructCode(passengersInfo.getPassengerStructCode());
                govPublicCarOrderUserInfo.setStructName(passengersInfo.getPassengerStructName());
                govPublicCarOrderUserInfo.setCompanyCode(handleDTO.getCompanyCode());
                govPublicCarOrderUserInfo.setCompanyName("");
                if(StringUtils.isNotBlank(handleDTO.getCompanyName())){
                    govPublicCarOrderUserInfo.setCompanyName(handleDTO.getCompanyName());
                }
                govPublicCarOrderUserInfo.setCreateTime(context.getUpdateDate());
                govPublicCarOrderUserInfo.setUpdateTime(context.getUpdateDate());
                list.add(govPublicCarOrderUserInfo);
            }
            return list;
        }
        return Lists.newArrayList();
    }

	protected Boolean addOrderQuestion(PublicGovOrderHandleDTO handleDTO,
                                       PublicGovOrderHandleContext context,
                                       String questionDescription,
                                       QuestionTypeEnum questionType,
                                       QuestionSourceEnum questionSource,
                                       FeedbackTypeEnum feedbackType) {
        OrderQuestionSubmitDTO question = new OrderQuestionSubmitDTO();
        question.setQuestionDescription(questionDescription);
        question.setQuestionSource(questionSource.getType());
        question.setBusinessNo(context.getOrderNo());
        question.setQuestionType(String.valueOf(questionType.getType()));
        question.setFeedbackType(feedbackType.getType());

        question.setLoginUserId(handleDTO.getOptUserId());
        question.setLoginCode(handleDTO.getOptUserCode());
        question.setLoginUserName(handleDTO.getOptUserName());
        question.setLoginUserMobile(handleDTO.getOptUserMobile());
        question.setLoginCompanyId(handleDTO.getCompanyId());
        question.setLoginCompanyCode(handleDTO.getCompanyCode());
        question.setLoginCompanyName(handleDTO.getCompanyName());
        question.setCustomerCode(handleDTO.getOptUserCode());
        question.setStructId(handleDTO.getStructId());
        question.setLoginStructCode(handleDTO.getStructCode());
        question.setStructName(handleDTO.getStructName());

        return this.orderQuestionService.saveQuestion(question);
    }



    protected GovPublicCarOrderUserInfo buildOptUserInfo(GovPublicCarOrderUserTypeEnum govPublicCarOrderUserTypeEnum,
                                                            PublicGovOrderHandleDTO handleDTO,
                                                            PublicGovOrderHandleContext context) {
        GovPublicCarOrderUserInfo userInfo = new GovPublicCarOrderUserInfo();
        userInfo.setOrderNo(handleDTO.getOrderNo());
        userInfo.setUserType(govPublicCarOrderUserTypeEnum.getCode());
        userInfo.setUserId(handleDTO.getOptUserId());
        userInfo.setUserCode(handleDTO.getOptUserCode());
        userInfo.setUserName(handleDTO.getOptUserName());
        userInfo.setUserMobile("");
        if(StringUtils.isNotBlank(handleDTO.getOptUserMobile())){
            userInfo.setUserMobile(handleDTO.getOptUserMobile());
        }
        userInfo.setStructId(handleDTO.getStructId());
        userInfo.setStructCode("");
        if(StringUtils.isNotBlank(handleDTO.getStructCode())){
            userInfo.setStructCode(handleDTO.getStructCode());
        }
        userInfo.setStructName("");
        if(StringUtils.isNotBlank(handleDTO.getStructName())){
            userInfo.setStructName(handleDTO.getStructName());
        }
        userInfo.setCompanyCode(handleDTO.getCompanyCode());
        userInfo.setCompanyName("");
        if(StringUtils.isNotBlank(handleDTO.getCompanyName())){
            userInfo.setCompanyName(handleDTO.getCompanyName());
        }
        // 记录真实的操作人公司
//        if(Objects.equals(handleDTO.getRequestSourceEnum(), RequestSourceEnum.PROVIDER)){
//            userInfo.setCompanyCode("");
//            if(StringUtils.isNotBlank(handleDTO.getOrderCompanyCode())){
//                userInfo.setCompanyCode(handleDTO.getOrderCompanyCode());
//            }
//            userInfo.setCompanyName("");
//            if(StringUtils.isNotBlank(handleDTO.getCompanyName())){
//                userInfo.setCompanyName(handleDTO.getCompanyName());
//            }
//        }
        userInfo.setCreateTime(context.getUpdateDate());
        userInfo.setUpdateTime(context.getUpdateDate());
        return userInfo;
    }



    protected void approvalCancel(PublicGovOrderHandleDTO handleDTO, PublicGovOrderHandleContext context) {
        if(StringUtils.isBlank(context.getGovPublicCarOrder().getApprovalId())){
            return;
        }
        GovPublicCarOrder govPublicCarOrder = context.getGovPublicCarOrder();
        //调用取消撤回审批接口
        BpmProcessInstanceCancelReqDTO reqDTO = new BpmProcessInstanceCancelReqDTO();
        reqDTO.setId(context.getGovPublicCarOrder().getApprovalId());
        reqDTO.setReason("发起人取消");
        reqDTO.setIsBusiness(Boolean.TRUE);
        reqDTO.setLoginUserId(0);
        reqDTO.setLoginUserName("系统");
        reqDTO.setLoginCompanyId(0);
        reqDTO.setLoginCompanyName("系统");
        log.info("orderNo:{} approveId {} 审批撤回", govPublicCarOrder.getOrderNo(), govPublicCarOrder.getApprovalId());
        RestResponse response = WorkflowClient.applyCancel(reqDTO);
        if (response.isSuccess() && response.getData() != null) {
        } else {
            // 这里是否要做补偿机制 TODO
            log.warn("orderNo:{} approveId {} 审批撤回失败 {}",
                    govPublicCarOrder.getOrderNo(), govPublicCarOrder.getApprovalId(), JSON.toJSONString(response));
        }
    }


    protected void releaseVehicle(Integer vehicleId){
        // 进行车辆释放
        ModifyVehicleWorkingStatusReqDTO modifyVehicleWorkingStatusReqDTO = new ModifyVehicleWorkingStatusReqDTO();
        modifyVehicleWorkingStatusReqDTO.setVehicleId(vehicleId.longValue());
        modifyVehicleWorkingStatusReqDTO.setWorkingStatus(VehicleEnum.WorkingStatus.NO_SERVICE.getValue());
        modifyVehicleWorkingStatusReqDTO.setChannel("order");
        OrderAssetRestApi.modifyVehicleWorkingStatus(modifyVehicleWorkingStatusReqDTO);
    }

    protected void occupyVehicle(Integer vehicleId, Integer occupyStaffId, String occupyStaffName, Date occupyTime){
        // 进行车辆释放
        ModifyVehicleWorkingStatusReqDTO modifyVehicleWorkingStatusReqDTO = new ModifyVehicleWorkingStatusReqDTO();
        modifyVehicleWorkingStatusReqDTO.setVehicleId(vehicleId.longValue());
        modifyVehicleWorkingStatusReqDTO.setWorkingStatus(VehicleEnum.WorkingStatus.IN_SERVICE.getValue());
        modifyVehicleWorkingStatusReqDTO.setOccupyTime(occupyTime);
        modifyVehicleWorkingStatusReqDTO.setOccupyStaffId(occupyStaffId);
        modifyVehicleWorkingStatusReqDTO.setOccupyStaffName(occupyStaffName);
        modifyVehicleWorkingStatusReqDTO.setChannel("order");
        OrderAssetRestApi.modifyVehicleWorkingStatus(modifyVehicleWorkingStatusReqDTO);
    }


    protected BigDecimal computeVehicleMileage(PublicGovOrderHandleContext context){
        GovPublicCarOrderAddressInfoExample addressInfoExample = new GovPublicCarOrderAddressInfoExample();
        GovPublicCarOrder order = context.getGovPublicCarOrder();
        addressInfoExample.createCriteria().andOrderNoEqualTo(order.getOrderNo())
                .andCompanyCodeEqualTo(order.getCompanyCode());

        List<GovPublicCarOrderAddressInfo> addressInfoList = addressInfoMapper.selectByExample(addressInfoExample);
        if(CollectionUtils.isEmpty(addressInfoList)){
            return BigDecimal.ZERO;
        }
        GovPublicCarOrderAddressInfo addressInfo = addressInfoList.get(0);
        if(Objects.isNull(addressInfo.getDeviceId()) || Objects.isNull(addressInfo.getDeviceType())){
            return BigDecimal.ZERO;
        }
        return deviceOperationService.computeVehicleMileage(order.getVehicleLicense(), order.getVehicleVin(),
                addressInfo.getDeviceId(), addressInfo.getDeviceType(), order.getOrderStartTime(), context.getUpdateDate());
    }

    protected void doCloseWarnRecord(PublicGovOrderHandleDTO handleDto,
                                   PublicGovOrderHandleContext context) {
        // 查询地址记录
        GovPublicCarOrderAddressInfoExample cond = new GovPublicCarOrderAddressInfoExample();
        cond.createCriteria().andOrderNoEqualTo(handleDto.getOrderNo());
        List<GovPublicCarOrderAddressInfo> addressInfoList = addressInfoMapper.selectByExample(cond);
        if (addressInfoList.isEmpty()) {
            return;
        }
        GovPublicCarOrderAddressInfo addressInfo = addressInfoList.get(0);
        // 报警编号
        String warnSn = addressInfo.getAlarmCode();
        // 车架号
        String vehicleVin = context.getGovPublicCarOrder().getVehicleVin();

        Map<String, Object> params = new HashMap<>();
        params.put("warnSn", warnSn);
        params.put("vehicleVin", vehicleVin);

        String restUrl = RestLocators.iot().getRestUrl("/warn/gov/forceEndWarn");
        RestResponse<?> response =
                RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, params, null, Object.class);
    }

}
