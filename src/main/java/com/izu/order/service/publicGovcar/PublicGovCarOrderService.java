package com.izu.order.service.publicGovcar;
import com.izu.business.util.PushMessageUtil;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.HashSet;

import com.alibaba.excel.util.DateUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.izu.asset.MrCarAssetRestLocator;
import com.izu.asset.consts.DeploymentModeEnum;
import com.izu.asset.consts.VehicleEnum;
import com.izu.asset.dto.ModifyVehicleWorkingStatusReqDTO;
import com.izu.asset.dto.publicGovCar.PublicGovCarInfoDTO;
import com.izu.asset.dto.publicGovCar.req.AppGetPublicGovCarVehicleReqDTO;
import com.izu.asset.dto.vehicle.CarInfoBaseDTO;
import com.izu.asset.dto.vehicle.CarInfoDetailDTO;
import com.izu.asset.dto.vehicle.CarInfoQueryConditionDTO;
import com.izu.asset.restApi.order.OrderAssetRestApi;
import com.izu.business.dto.msgcenter.input.PushMessageInputDTO;
import com.izu.config.dto.GovCompanyConfigRespDTO;
import com.izu.framework.mail.MailMessage;
import com.izu.framework.mail.MailUtils;
import com.izu.framework.web.exception.ExceptionFactory;
import com.izu.framework.web.exception.RestErrorException;
import com.izu.framework.web.rest.client.BaseHttpClient;
import com.izu.framework.web.rest.client.RestClient;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestErrorCode;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.framework.web.util.VelocityUtil;
import com.izu.mrcar.config.enums.IsShowEnum;
import com.izu.mrcar.iot.config.IotRestLocator;
import com.izu.mrcar.iot.dto.CarGpsFenceDTO;
import com.izu.mrcar.iot.dto.GpsVehicleDTO;
import com.izu.mrcar.iot.dto.device.ListVehicleDeviceInfoReqDTO;
import com.izu.mrcar.iot.dto.device.VehicleDeviceInfoDTO;
import com.izu.mrcar.iot.dto.iot.OfficialVehicleInFenceMsgDTO;
import com.izu.mrcar.iot.dto.iot.OfficialVehicleOutFenceMsgDTO;
import com.izu.mrcar.iot.dto.warn.OfficialVehicleWarnRecordDTO;
import com.izu.mrcar.iot.dto.warn.VehicleRealtimeStatusDTO;
import com.izu.mrcar.iot.restApi.MrCarIotRestApi;
import com.izu.mrcar.iot.utils.OSSUtils;
import com.izu.mrcar.order.ExportDTO;
import com.izu.mrcar.order.ExportPDFDTO;
import com.izu.mrcar.order.consts.CompanyTemplateEnum;
import com.izu.mrcar.order.consts.OrderConst;
import com.izu.mrcar.order.consts.govPublicCar.*;
import com.izu.mrcar.order.dto.publicGovcar.*;
import com.izu.mrcar.order.dto.publicGovcar.app.req.ExportDispatchOrderReqDTO;
import com.izu.mrcar.order.dto.publicGovcar.app.req.ExportDispatchOrderToMailReqDTO;
import com.izu.mrcar.order.errcode.MrCarOrderErrorCode;
import com.izu.mrcar.order.util.LocalDateTimeUtils;
import com.izu.mrcar.workflow.common.constants.enums.ModelEnum;
import com.izu.mrcar.workflow.common.dto.instance.ApplyStartDTO;
import com.izu.mrcar.workflow.common.dto.instance.ApplyStartSwitchDTO;
import com.izu.mrcar.workflow.common.dto.instance.BpmMessageSendApproveResultDTO;
import com.izu.mrcar.workflow.common.dto.instance.BpmProcessInstanceCancelReqDTO;
import com.izu.mrcar.workflow.common.enums.ErrorCodeConstants;
import com.izu.mrcar.workflow.common.enums.task.BpmProcessInstanceResultEnum;
import com.izu.mrcar.workflow.common.utils.BpmFormExportUtil;
import com.izu.mrcar.workflow.common.utils.PdfUtil;
import com.izu.mrcar.workflow.common.utils.WorkflowClient;
import com.izu.notify.enums.MsgModelEnum;
import com.izu.order.config.ExecutorConfig;
import com.izu.order.entity.mrcar.*;
import com.izu.order.rpc.ConfigRpc;
import com.izu.order.rpc.UserRpc;
import com.izu.order.service.device.DeviceOperationService;
import com.izu.order.util.DateUtil;
import com.izu.order.util.MessageSendUtils;
import com.izu.order.util.PdfUtils;
import com.izu.order.util.SequenceGenerator;
import com.izu.third.enums.OSSBucketEnum;
import com.izu.user.config.UserRestLocator;
import com.izu.user.config.consts.UserUrlCenter;
import com.izu.order.util.*;
import com.izu.user.dto.CompanyDepartmentBranchDTO;
import com.izu.user.dto.CompanyDepartmentDTO;
import com.izu.user.dto.TimeRentDispatcherDTO;
import com.izu.user.dto.company.CompanyDTO;
import com.izu.user.dto.customer.CustomerSimpleDTO;
import com.izu.user.dto.CustomerDTO;
import com.izu.user.enums.perm.ClientDataPermTypeEnum;
import com.izu.user.enums.perm.ProviderDataPermTypeEnum;
import com.izu.user.enums.role.SystemTypeEnum;
import com.izu.user.restApi.CompanyApi;
import com.izu.user.restApi.CompanyDeptApi;
import com.izu.user.restApi.UserApi;
import lombok.extern.slf4j.Slf4j;
import mapper.mrcar.GovPublicCarOrderTaskMapper;
import mapper.mrcar.OrderOperationLogMapper;
import mapper.mrcar.ex.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.velocity.VelocityContext;
import com.izu.user.restApi.OrderUserApi;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/8/17 8:51
 */
@Service
@Slf4j
public class PublicGovCarOrderService {

    /**
     * 判断车机是否离线的最大时间差
     * 48小时
     */
    public static final long DEVICE_REPORT_TIME_LIMIT = 48 * 60 * 60 * 1000L;

    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private static final int MAX_EMAIL_LIST_SIZE = 5;

    private static final String PDF_FILE_PATH = "/data/logs/tmp/";
//    private static final String PDF_FILE_PATH = "/Users/<USER>/tmp/";

    @Resource
    private GovPublicCarOrderExMapper orderExMapper;

    @Resource
    private GovPublicCarOrderUserInfoExMapper orderUserInfoMapper;

    @Resource
    private GovPublicCarOrderVehicleInfoExMapper vehicleInfoMapper;


    @Resource
    private GovPublicCarOrderAddressInfoExMapper addressInfoMapper;

    @Autowired
    private DeviceOperationService deviceOperationService;

    @Autowired
    private SequenceGenerator sequenceGenerator;

    @Resource
    private OrderOperationLogMapper orderOperationLogMapper;


    @Resource
    private GovPublicCarOrderSubDailyExMapper subDailyMapper;


    @NacosValue(value = "${gov.public.order.detail.tips:实际用车时间以操作开始结束的时间计算,如果您未操作开始就驶出围栏,系统将以驶出围栏时间计为开始时间。}", autoRefreshed = true)
    private String tips;
    @Resource(name = ExecutorConfig.ORDER_POOL)
    private ExecutorService orderExecutorService;
    @Resource
    private GovPublicCarOrderTaskMapper govPublicCarOrderTaskMapper;

    private void checkCreateOrderReq(AppPublicGovCarCreateOrderReqDTO reqDTO){
        GovCompanyConfigRespDTO respDTO = ConfigRpc.getGovCompanyCarConfig(reqDTO.getLoginCompanyId());
        if(Objects.isNull(respDTO)){
            return;
        }
        Integer vehicleUsageDays = respDTO.getVehicleUsageDays();
        if(Objects.isNull(vehicleUsageDays) || vehicleUsageDays < 1){
            return;
        }
        Date expectedReturnTime = reqDTO.getExpectedReturnTime();
        Date maxReturnTime = DateUtil.addDay(reqDTO.getExpectedPickupTime(), vehicleUsageDays);
        log.info("expectedReturnTime:{},maxReturnTime:{}", expectedReturnTime.getTime(), maxReturnTime.getTime());
        if(maxReturnTime.getTime() < expectedReturnTime.getTime()){
            throw ExceptionFactory.createRestException(MrCarOrderErrorCode.PUBLIC_GOV_ORDER_EXCEED_MAX_AVAILABLE_DAYS);
        }
    }
    /**
     * app 下单接口
     *
     * @param param
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public RestResponse<AppPublicGovCarCreateOrderRespDTO> createOrder(AppPublicGovCarCreateOrderReqDTO param) {
        AppPublicGovCarCreateOrderRespDTO respDTO = new AppPublicGovCarCreateOrderRespDTO();
        checkCreateOrderReq(param);
        //查询车辆信息
        CarInfoDetailDTO carInfoDetail = OrderAssetRestApi.getVehicleInfoByVehicleId(param.getVehicleId().longValue());
        log.info("查询车辆信息完成");
        //校验是否存在异常下单的情况
        RestResponse checkResult = checkCreateOrder(carInfoDetail);
        if (checkResult != null) {
            return checkResult;
        }
        //校验车辆是否发生调拨
        if(!carInfoDetail.getCompanyId().equals(param.getLoginCompanyId())){
            return RestResponse.fail(MrCarOrderErrorCode.VEHICLE_NOT_EXISTED_COMPANY_ERROR);
        }
        if (GovPublicCarDeploymentModeEnum.POINT_PLACEMENT.getCode().equals(param.getDeploymentMode())){
            CompanyDepartmentBranchDTO allDeptBranchList = UserApi.getAllDeptBranchList(param.getPassengersInfoList().get(0).getPassengerStructId());
            CompanyDepartmentBranchDTO allDeptBranchListVehicle = UserApi.getAllDeptBranchList(carInfoDetail.getStructId());
            if(allDeptBranchList.getSonDepartment()==null
                    ||allDeptBranchListVehicle.getSonDepartment()==null
            ||!allDeptBranchList.getSonDepartment().get(0).getId().equals(allDeptBranchListVehicle.getSonDepartment().get(0).getId())){

                return RestResponse.fail(MrCarOrderErrorCode.VEHICLE_NOT_EXISTED_STRUCT_ERROR);
            }
        }
        //校验车辆是否在围栏内
        VehicleBase vehicleBase = new VehicleBase();
        vehicleBase.setVehicleVin(carInfoDetail.getVehicleVin());
        VehicleRealtimeStatusDTO vehicleRealtimeStatusDTO = deviceOperationService.checkVehicleStatus(vehicleBase);
        log.info("查询车机完成");
        //实体类为空或者最近一次报点信息为空，都属于车机离线
        if (vehicleRealtimeStatusDTO == null || vehicleRealtimeStatusDTO.getDeviceStatus() == null) {
            return RestResponse.fail(MrCarOrderErrorCode.VEHICLE_DEVICE_OFFLINE);
        }
        //最近一次车机上传报点时间小于当前时间别且查48小时，则认为车机离线
        if (vehicleRealtimeStatusDTO.getDeviceStatus().getReportTime().getTime() < System.currentTimeMillis()
                && (System.currentTimeMillis() - vehicleRealtimeStatusDTO.getDeviceStatus().getReportTime().getTime() > DEVICE_REPORT_TIME_LIMIT)) {
            return RestResponse.fail(MrCarOrderErrorCode.VEHICLE_DEVICE_OFFLINE);
        }
        //校验设备是否在围栏内
        if (!vehicleRealtimeStatusDTO.getInFence()) {
            return RestResponse.fail(MrCarOrderErrorCode.VEHICLE_LEAVE_FENCE_ERROR);
        }
        //构建订单信息
        param.setVehicleStructId(carInfoDetail.getStructId());
        param.setVehicleVin(carInfoDetail.getVehicleVin());
        param.setVehicleLicense(carInfoDetail.getVehicleLicense());
        param.setInitialLatitude(vehicleRealtimeStatusDTO.getDeviceStatus().getLatBaidu());
        param.setInitialLongitude(vehicleRealtimeStatusDTO.getDeviceStatus().getLngBaidu());
        param.setInitialLocation(vehicleRealtimeStatusDTO.getAddress());
        param.setInitialFenceId(vehicleRealtimeStatusDTO.getNearestFence().getSnapId());
        param.setDeploymentMode(carInfoDetail.getDeploymentMode());
        GovPublicCarOrder govPublicCarOrder = buildOrderInfoAndInsert(param);
        respDTO.setOrderNo(govPublicCarOrder.getOrderNo());
        log.info("校验完成");
        //构建订单车辆表信息
        GovPublicCarOrderVehicleInfo govPublicCarOrderVehicleInfo = buildOrderVehicleInfo(param, govPublicCarOrder, carInfoDetail);
        log.info("订单插入完成");
        //构建地址相关信息
        buildAddressInfo(param, govPublicCarOrder,vehicleRealtimeStatusDTO);
        log.info("地址插入完成");
        //构建乘车人、司机等信息
        List<GovPublicCarOrderUserInfo> userInfos = buildOrderUserInfo(param, govPublicCarOrder);
        log.info("人员信息插入完成");
        //构建操作日志信息
        buildOrderOperationLogInfo(param, govPublicCarOrder);
        log.info("操作日志完成");
        //审批相关操作
        doApprove(param, govPublicCarOrder,respDTO);
        log.info("审批操作完成");
        //修改车态
        ModifyVehicleWorkingStatusReqDTO modifyVehicleWorkingStatusReqDTO = new ModifyVehicleWorkingStatusReqDTO();
        modifyVehicleWorkingStatusReqDTO.setVehicleId(govPublicCarOrder.getVehicleId().longValue());
        modifyVehicleWorkingStatusReqDTO.setWorkingStatus(VehicleEnum.WorkingStatus.IN_SERVICE.getValue());
        modifyVehicleWorkingStatusReqDTO.setChannel("order");
        List<GovPublicCarOrderUserInfo> mainPassengers =
                userInfos.stream().filter(user -> user.getUserType() == GovPublicCarOrderUserTypeEnum.PRIMARY_RIDER.getCode()).collect(Collectors.toList());
        modifyVehicleWorkingStatusReqDTO.setOccupyStaffId(mainPassengers.get(0).getUserId());
        modifyVehicleWorkingStatusReqDTO.setOccupyStaffName(mainPassengers.get(0).getUserName());
        modifyVehicleWorkingStatusReqDTO.setOccupyTime(new Date());
        OrderAssetRestApi.modifyVehicleWorkingStatus(modifyVehicleWorkingStatusReqDTO);
        log.info("修改车态操作完成");
        //下单成功发送消息
        sendOrderCreateMessage(govPublicCarOrder, govPublicCarOrderVehicleInfo, userInfos);
        log.info("发送消息完成");
        return RestResponse.success(respDTO);
    }

    //审批处理
    private void doApprove(AppPublicGovCarCreateOrderReqDTO param, GovPublicCarOrder govPublicCarOrder, AppPublicGovCarCreateOrderRespDTO respDTO) {
        respDTO.setOrderApprovalStatusStr(GovPublicCarApproveStatusCompleteEnum.PENDING_APPROVAL.getName());
        //查看审批设置是否激活
        ApplyStartSwitchDTO applyStartSwitchDTO = new ApplyStartSwitchDTO();
        applyStartSwitchDTO.setCompanyId(param.getLoginCompanyId());
        applyStartSwitchDTO.setBusinessType(ModelEnum.BusinessTypeDefaultEnum.PUBLIC_GOV_CAR_ORDER_APPLY.getCode());
        Boolean active = WorkflowClient.isActive(applyStartSwitchDTO);
        //如果开启
        if (active) {
            //发起审批
            ApplyStartDTO applyStartDTO = new ApplyStartDTO();
            applyStartDTO.setLoginUserId(param.getLoginUserId());
            applyStartDTO.setLoginUserName(param.getLoginUserName());
            applyStartDTO.setLoginCompanyId(param.getLoginCompanyId());
            applyStartDTO.setLoginCompanyName(param.getLoginCompanyName());
            applyStartDTO.setLoginDeptId(param.getStructId());
            applyStartDTO.setLoginDeptName(param.getStructName());
            applyStartDTO.setBusinessType(ModelEnum.BusinessTypeDefaultEnum.PUBLIC_GOV_CAR_ORDER_APPLY.getCode());
            applyStartDTO.setBusinessNo(govPublicCarOrder.getOrderNo());
            Map<String, Object> variables = new HashMap<>();
            variables.put("applyRemark",govPublicCarOrder.getCarUseReason());
            variables.put("deploymentMode",govPublicCarOrder.getDeploymentMode());
            variables.put("deptId",param.getStructId());
            // 流程实例变量设置
            applyStartDTO.setVariables(JSON.toJSONString(variables));
            final RestResponse response = WorkflowClient.applyStart(applyStartDTO);
            if (response.isSuccess() && response.getData() != null) {
                GovPublicCarOrder govPublicCarOrderUpdate = new GovPublicCarOrder();
                govPublicCarOrderUpdate.setOrderId(govPublicCarOrder.getOrderId());
                govPublicCarOrderUpdate.setApprovalId(response.getData().toString());
                govPublicCarOrderUpdate.setApprovalStatus(GovPublicCarApproveStatusEnum.PENDING.getCode());
                orderExMapper.updateByPrimaryKeySelective(govPublicCarOrderUpdate);
            } else {
                log.info("提交审批失败，response={}", JSON.toJSONString(response));
                //透传审批的消息
                throw ExceptionFactory.createRestException(response.getMsg());
            }
        }else{
            respDTO.setOrderApprovalStatusStr(GovPublicCarApproveStatusCompleteEnum.PENDING_DEPARTURE.getName());
        }
    }

    /**
     * 构建操作日志信息
     *
     * @param param
     * @param govPublicCarOrder
     */
    private void buildOrderOperationLogInfo(AppPublicGovCarCreateOrderReqDTO param, GovPublicCarOrder govPublicCarOrder) {
        OrderOperationLog orderOperationLog = new OrderOperationLog();
        orderOperationLog.setOrderId(Integer.valueOf(govPublicCarOrder.getOrderId() + ""));
        orderOperationLog.setOrderNo(govPublicCarOrder.getOrderNo());
        orderOperationLog.setOperationType(GovPublicCarOrderOperationTypeEnum.CREATE.getCode());
        orderOperationLog.setOperationDescription(GovPublicCarOrderOperationTypeEnum.CREATE.getName());
        orderOperationLog.setOperationTime(govPublicCarOrder.getCreateTime());
        orderOperationLog.setOperatorMobile(param.getLoginUserMobile());
        orderOperationLog.setOrderStatus(GovPublicCarOrderStatusEnum.PENDING_DEPARTURE.getCode().intValue());
        orderOperationLog.setOrderStatusName(GovPublicCarOrderStatusEnum.PENDING_DEPARTURE.getName());
        orderOperationLog.setOperatorId(param.getLoginUserId().longValue());
        orderOperationLog.setOperatorCode(param.getLoginCode());
        orderOperationLog.setOperatorName(param.getLoginUserName());
        orderOperationLogMapper.insertSelective(orderOperationLog);
    }

    /**
     * 构建乘车人、司机等信息
     *
     * @param param
     * @param govPublicCarOrder
     */
    private List<GovPublicCarOrderUserInfo> buildOrderUserInfo(AppPublicGovCarCreateOrderReqDTO param, GovPublicCarOrder govPublicCarOrder) {
        List<GovPublicCarOrderUserInfo> govPublicCarOrderUserInfoList = Lists.newArrayList();
        //构建下单人
        buildCreateOrderUserInfo(param, govPublicCarOrder, govPublicCarOrderUserInfoList);
        //构建乘车人信息
        buildPassengersUserInfo(param, govPublicCarOrder, govPublicCarOrderUserInfoList);
        //构建司机信息
        buildDriverUserInfo(param, govPublicCarOrder, govPublicCarOrderUserInfoList);
        //批量插入人员信息
        orderUserInfoMapper.batchInsert(govPublicCarOrderUserInfoList);
        return govPublicCarOrderUserInfoList;
    }

    /**
     * 构建司机信息
     *
     * @param param
     * @param govPublicCarOrder
     * @param govPublicCarOrderUserInfoList
     */
    private void buildDriverUserInfo(AppPublicGovCarCreateOrderReqDTO param, GovPublicCarOrder govPublicCarOrder, List<GovPublicCarOrderUserInfo> govPublicCarOrderUserInfoList) {
        GovPublicCarOrderUserInfo driverUserInfo = new GovPublicCarOrderUserInfo();
        driverUserInfo.setOrderNo(govPublicCarOrder.getOrderNo());
        driverUserInfo.setUserType(GovPublicCarOrderUserTypeEnum.DRIVER.getCode());
        driverUserInfo.setUserId(param.getDriverId());
        driverUserInfo.setUserCode(param.getDriverCode());
        driverUserInfo.setUserName(param.getDriverName());
        driverUserInfo.setUserMobile(param.getDriverMobile());
        driverUserInfo.setStructId(param.getDriverStructId());
        driverUserInfo.setStructCode(param.getDriverStructCode());
        driverUserInfo.setStructName(param.getDriverStructName());
        driverUserInfo.setCompanyCode(param.getLoginCompanyCode());
        driverUserInfo.setCompanyName(param.getLoginCompanyName());
        driverUserInfo.setCreateTime(govPublicCarOrder.getCreateTime());
        driverUserInfo.setUpdateTime(govPublicCarOrder.getCreateTime());
        govPublicCarOrderUserInfoList.add(driverUserInfo);
    }

    /**
     * 乘车人相关信息
     *
     * @param param
     * @param govPublicCarOrder
     * @param govPublicCarOrderUserInfoList
     */
    private void buildPassengersUserInfo(AppPublicGovCarCreateOrderReqDTO param, GovPublicCarOrder govPublicCarOrder, List<GovPublicCarOrderUserInfo> govPublicCarOrderUserInfoList) {
        List<AppPublicGovCarCreateOrderReqDTO.PassengersInfo> passengersInfoList = param.getPassengersInfoList();
        if (CollectionUtils.isNotEmpty(passengersInfoList)) {
            for (int i = 0; i < passengersInfoList.size(); i++) {
                AppPublicGovCarCreateOrderReqDTO.PassengersInfo passengersInfo = passengersInfoList.get(i);
                GovPublicCarOrderUserInfo govPublicCarOrderUserInfo = new GovPublicCarOrderUserInfo();
                govPublicCarOrderUserInfo.setOrderNo(govPublicCarOrder.getOrderNo());
                if (i == 0) {
                    govPublicCarOrderUserInfo.setUserType(GovPublicCarOrderUserTypeEnum.PRIMARY_RIDER.getCode());
                } else {
                    govPublicCarOrderUserInfo.setUserType(GovPublicCarOrderUserTypeEnum.SECONDARY_RIDER.getCode());
                }
                govPublicCarOrderUserInfo.setUserId(passengersInfo.getPassengerId());
                govPublicCarOrderUserInfo.setUserCode(passengersInfo.getPassengerCode());
                govPublicCarOrderUserInfo.setUserName(passengersInfo.getPassengerName());
                govPublicCarOrderUserInfo.setUserMobile(passengersInfo.getPassengerMobile());
                govPublicCarOrderUserInfo.setStructId(passengersInfo.getPassengerStructId());
                govPublicCarOrderUserInfo.setStructCode(passengersInfo.getPassengerStructCode());
                govPublicCarOrderUserInfo.setStructName(passengersInfo.getPassengerStructName());
                govPublicCarOrderUserInfo.setCompanyCode(param.getLoginCompanyCode());
                govPublicCarOrderUserInfo.setCompanyName(param.getLoginCompanyName());
                govPublicCarOrderUserInfo.setCreateTime(govPublicCarOrder.getCreateTime());
                govPublicCarOrderUserInfo.setUpdateTime(govPublicCarOrder.getCreateTime());
                govPublicCarOrderUserInfoList.add(govPublicCarOrderUserInfo);
            }
        }
    }

    /**
     * 下单人
     *
     * @param param
     * @param govPublicCarOrder
     * @param govPublicCarOrderUserInfoList
     */
    private void buildCreateOrderUserInfo(AppPublicGovCarCreateOrderReqDTO param, GovPublicCarOrder govPublicCarOrder, List<GovPublicCarOrderUserInfo> govPublicCarOrderUserInfoList) {
        GovPublicCarOrderUserInfo govPublicCarOrderUserInfo = new GovPublicCarOrderUserInfo();
        govPublicCarOrderUserInfo.setOrderNo(govPublicCarOrder.getOrderNo());
        govPublicCarOrderUserInfo.setUserType(GovPublicCarOrderUserTypeEnum.ORDER_PLACER.getCode());
        govPublicCarOrderUserInfo.setUserId(param.getLoginUserId());
        govPublicCarOrderUserInfo.setUserCode(param.getLoginCode());
        govPublicCarOrderUserInfo.setUserName(param.getLoginUserName());
        govPublicCarOrderUserInfo.setUserMobile(param.getLoginUserMobile());
        govPublicCarOrderUserInfo.setStructId(param.getStructId());
        //部门为空的情况
        if(StringUtils.isEmpty(param.getLoginStructCode())){
            //根据部门id查询部门code
            List<CompanyDepartmentDTO> departmentListByIds = UserApi.getDepartmentListByIds(param.getStructId().toString());
            govPublicCarOrderUserInfo.setStructCode(departmentListByIds.get(0).getDepartmentCode());
        }else {
            govPublicCarOrderUserInfo.setStructCode(param.getLoginStructCode());
        }
        govPublicCarOrderUserInfo.setStructName(param.getStructName());
        govPublicCarOrderUserInfo.setCompanyCode(param.getLoginCompanyCode());
        govPublicCarOrderUserInfo.setCompanyName(param.getLoginCompanyName());
        govPublicCarOrderUserInfo.setCreateTime(govPublicCarOrder.getCreateTime());
        govPublicCarOrderUserInfo.setUpdateTime(govPublicCarOrder.getCreateTime());
        govPublicCarOrderUserInfoList.add(govPublicCarOrderUserInfo);
    }

    /**
     * 构建地址相关信息
     *
     * @param param
     * @param govPublicCarOrder
     * @param vehicleRealtimeStatusDTO
     */
    private void buildAddressInfo(AppPublicGovCarCreateOrderReqDTO param, GovPublicCarOrder govPublicCarOrder, VehicleRealtimeStatusDTO vehicleRealtimeStatusDTO) {
        GovPublicCarOrderAddressInfo govPublicCarOrderAddressInfo = BeanUtil.copyObject(param, GovPublicCarOrderAddressInfo.class);
        govPublicCarOrderAddressInfo.setOrderNo(govPublicCarOrder.getOrderNo());
        govPublicCarOrderAddressInfo.setCreateTime(govPublicCarOrder.getCreateTime());
        govPublicCarOrderAddressInfo.setUpdateTime(govPublicCarOrder.getCreateTime());
        govPublicCarOrderAddressInfo.setCompanyCode(govPublicCarOrder.getCompanyCode());
        govPublicCarOrderAddressInfo.setDeviceId(vehicleRealtimeStatusDTO.getDeviceStatus().getDeviceId());
        govPublicCarOrderAddressInfo.setDeviceType(vehicleRealtimeStatusDTO.getDeviceStatus().getDeviceType());
        govPublicCarOrderAddressInfo.setInitialLatitude(vehicleRealtimeStatusDTO.getDeviceStatus().getLatBaidu());
        govPublicCarOrderAddressInfo.setInitialLongitude(vehicleRealtimeStatusDTO.getDeviceStatus().getLngBaidu());
        govPublicCarOrderAddressInfo.setInitialLocation(vehicleRealtimeStatusDTO.getAddress());
        govPublicCarOrderAddressInfo.setInitialFenceId(vehicleRealtimeStatusDTO.getNearestFence().getSnapId());
        addressInfoMapper.insertSelective(govPublicCarOrderAddressInfo);
    }

    /**
     * 构建订单车辆表信息
     *
     * @param param
     * @param govPublicCarOrder
     * @param carInfoDetail
     */
    private GovPublicCarOrderVehicleInfo buildOrderVehicleInfo(AppPublicGovCarCreateOrderReqDTO param, GovPublicCarOrder govPublicCarOrder, CarInfoDetailDTO carInfoDetail) {
        GovPublicCarOrderVehicleInfo govPublicCarOrderVehicleInfo = BeanUtil.copyObject(param, GovPublicCarOrderVehicleInfo.class);
        govPublicCarOrderVehicleInfo.setOrderNo(govPublicCarOrder.getOrderNo());
        if (carInfoDetail != null) {
            govPublicCarOrderVehicleInfo.setNoKeyUseCar(carInfoDetail.getNoKeyUseCar());
            govPublicCarOrderVehicleInfo.setVehicleBrandCode(carInfoDetail.getVehicleBrandCode());
            govPublicCarOrderVehicleInfo.setVehicleBrandName(carInfoDetail.getVehicleBrand());
            govPublicCarOrderVehicleInfo.setVehicleModelCode(carInfoDetail.getVehicleModelCode());
            govPublicCarOrderVehicleInfo.setVehicleModelName(carInfoDetail.getVehicleModel());
            govPublicCarOrderVehicleInfo.setVehicleStructId(carInfoDetail.getStructId());
            List<CompanyDepartmentDTO> departmentListByIds = UserApi.getDepartmentListByIds(carInfoDetail.getStructId().toString());
            if (CollectionUtils.isNotEmpty(departmentListByIds)){
                govPublicCarOrderVehicleInfo.setVehicleStructCode(departmentListByIds.get(0).getDepartmentCode());
                govPublicCarOrderVehicleInfo.setVehicleStructName(departmentListByIds.get(0).getDepartmentName());
            }
            govPublicCarOrderVehicleInfo.setVehicleCityCode(carInfoDetail.getBelongCityCode());
            govPublicCarOrderVehicleInfo.setVehicleCityName(carInfoDetail.getBelongCityName());
            govPublicCarOrderVehicleInfo.setVehicleLicense(carInfoDetail.getVehicleLicense());
            govPublicCarOrderVehicleInfo.setVehicleVin(carInfoDetail.getVehicleVin());
            if(StringUtils.isBlank(govPublicCarOrderVehicleInfo.getVehiclePicUrl())
                    && StringUtils.isNotBlank(carInfoDetail.getVehiclePicUrl())){
                govPublicCarOrderVehicleInfo.setVehiclePicUrl(carInfoDetail.getVehiclePicUrl());
            }
            govPublicCarOrderVehicleInfo.setBelongBussCode(carInfoDetail.getBelongBussCode());
            govPublicCarOrderVehicleInfo.setOperateBussCode(carInfoDetail.getOperateBussCode());
        }
        govPublicCarOrderVehicleInfo.setCreateTime(govPublicCarOrder.getCreateTime());
        govPublicCarOrderVehicleInfo.setCompanyCode(govPublicCarOrder.getCompanyCode());
        govPublicCarOrderVehicleInfo.setUpdateTime(govPublicCarOrder.getCreateTime());
        vehicleInfoMapper.insertSelective(govPublicCarOrderVehicleInfo);
        return govPublicCarOrderVehicleInfo;
    }

    /**
     * 构建订单信息
     *
     * @param param
     */
    private GovPublicCarOrder buildOrderInfoAndInsert(AppPublicGovCarCreateOrderReqDTO param) {
        GovPublicCarOrder govPublicCarOrder = BeanUtil.copyObject(param, GovPublicCarOrder.class);
        String orderNo = sequenceGenerator.generate(new Date(), OrderConst.PUBLIC_GOV_CAR_ORDER_PREFIX);
        govPublicCarOrder.setOrderNo(orderNo);
        govPublicCarOrder.setOrderType(GovPublicCarOrderTypeEnum.PUBLIC_USE_CAR_TYPE.getCode());
        //计算订单所属部门
        calculateOrderStruct(param, govPublicCarOrder);
        govPublicCarOrder.setOrderStatus(GovPublicCarOrderStatusEnum.PENDING_DEPARTURE.getCode());
        govPublicCarOrder.setApprovalStatus(GovPublicCarApproveStatusEnum.PASS.getCode());
        Date now = new Date();
        govPublicCarOrder.setCreateTime(now);
        govPublicCarOrder.setUpdateTime(now);
        govPublicCarOrder.setUpdateId(param.getLoginUserId());
        govPublicCarOrder.setUpdateName(param.getLoginUserName());
        govPublicCarOrder.setCompanyId(param.getLoginCompanyId());
        govPublicCarOrder.setCompanyCode(param.getLoginCompanyCode());
        govPublicCarOrder.setCompanyName(param.getLoginCompanyName());
        orderExMapper.insertSelective(govPublicCarOrder);
        return govPublicCarOrder;
    }

    /**
     * 计算乘车人部门
     *
     * @param param
     * @param govPublicCarOrder
     */
    private void calculateOrderStruct(AppPublicGovCarCreateOrderReqDTO param, GovPublicCarOrder govPublicCarOrder) {
        //平台共享车取主城车人的顶级部门，定点投放取车的顶级部门
        if (GovPublicCarDeploymentModeEnum.SHARED_PLATFORM.getCode().equals(param.getDeploymentMode())) {
            CompanyDepartmentBranchDTO allDeptBranchList = UserApi.getAllDeptBranchList(param.getPassengersInfoList().get(0).getPassengerStructId());
            if (allDeptBranchList != null) {
                if (CollectionUtils.isNotEmpty(allDeptBranchList.getSonDepartment())) {
                    govPublicCarOrder.setOrderStructId(allDeptBranchList.getSonDepartment().get(0).getId());
                    govPublicCarOrder.setOrderStructName(allDeptBranchList.getSonDepartment().get(0).getDepartmentName());
                    govPublicCarOrder.setOrderStructCode(allDeptBranchList.getSonDepartment().get(0).getDepartmentCode());
                } else {
                    govPublicCarOrder.setOrderStructId(allDeptBranchList.getParentDepartment().getId());
                    govPublicCarOrder.setOrderStructName(allDeptBranchList.getParentDepartment().getDepartmentName());
                    govPublicCarOrder.setOrderStructCode(allDeptBranchList.getParentDepartment().getDepartmentCode());
                }
            }
        } else if (GovPublicCarDeploymentModeEnum.POINT_PLACEMENT.getCode().equals(param.getDeploymentMode())) {
            CompanyDepartmentBranchDTO allDeptBranchList = UserApi.getAllDeptBranchList(param.getVehicleStructId());
            if (allDeptBranchList != null) {
                if (CollectionUtils.isNotEmpty(allDeptBranchList.getSonDepartment())) {
                    govPublicCarOrder.setOrderStructId(allDeptBranchList.getSonDepartment().get(0).getId());
                    govPublicCarOrder.setOrderStructName(allDeptBranchList.getSonDepartment().get(0).getDepartmentName());
                    govPublicCarOrder.setOrderStructCode(allDeptBranchList.getSonDepartment().get(0).getDepartmentCode());
                } else {
                    govPublicCarOrder.setOrderStructId(allDeptBranchList.getParentDepartment().getId());
                    govPublicCarOrder.setOrderStructName(allDeptBranchList.getParentDepartment().getDepartmentName());
                    govPublicCarOrder.setOrderStructCode(allDeptBranchList.getParentDepartment().getDepartmentCode());
                }
            }
        }
    }

    /**
     * 校验下单是否有异常场景
     *
     * @param carInfoDetail
     * @param carInfoDetail
     * @return
     */
    @Nullable
    private RestResponse checkCreateOrder(CarInfoDetailDTO carInfoDetail) {
        //校验车态
        if(carInfoDetail==null){
            return RestResponse.fail(MrCarOrderErrorCode.VEHICLE_NOT_EXIST_ERROR);
        }
        if(VehicleEnum.WorkingStatus.IN_SERVICE.getValue().equals(carInfoDetail.getWorkingStatus())){
            return RestResponse.fail(MrCarOrderErrorCode.VEHICLE_IN_USE_ERROR);
        }
        if(VehicleEnum.WorkingStatus.THE_OPERATING.getValue().equals(carInfoDetail.getWorkingStatus())){
            return RestResponse.fail(MrCarOrderErrorCode.VEHICLE_NOT_OPERATIONS_ERROR);
        }
        //校验该车辆是否存在进行中的订单
        GovPublicCarOrderExample govPublicCarOrderExample = new GovPublicCarOrderExample();
        govPublicCarOrderExample.createCriteria().andVehicleVinEqualTo(carInfoDetail.getVehicleVin()).andOrderStatusLessThanOrEqualTo(GovPublicCarOrderStatusEnum.IN_USE.getCode());
        List<GovPublicCarOrder> govPublicCarOrders = orderExMapper.selectByExample(govPublicCarOrderExample);
        if (CollectionUtils.isNotEmpty(govPublicCarOrders)) {
            return RestResponse.fail(MrCarOrderErrorCode.VEHICLE_EXIST_ORDER_ERROR);
        }
        return null;
    }


    public PageDTO getList(PublicGovCarOrderQueryDTO param) {
        boolean createSelect = StringUtils.isNotEmpty(param.getCreateCompanyCode()) || StringUtils.isNotEmpty(param.getCreateStructCode()) || StringUtils.isNotEmpty(param.getCreateUserName()) || StringUtils.isNotEmpty(param.getCreateUserMobile()) || CollectionUtils.isNotEmpty(param.getCompanyCodes());
        boolean passengerSelect = StringUtils.isNotEmpty(param.getPassengerUserName()) || StringUtils.isNotEmpty(param.getPassengerUserMobile());
        boolean driver = StringUtils.isNotEmpty(param.getDriverName()) || StringUtils.isNotEmpty(param.getDriverMobile());
        //处理下单人/用车人/司机的筛选项/数据权限
        if (createSelect || passengerSelect || driver) {
            List<String> orderNoList = handlerCarUserInfoParam(param, createSelect, passengerSelect, driver);
            if (CollectionUtils.isEmpty(orderNoList)) {
                return new PageDTO(param.getPage(), param.getPageSize(), 0, Collections.emptyList());
            }
            param.setOrderNoList(orderNoList);
        }
        setDataPerm(param);
        //分页查询订单列表
        PageInfo<GovPublicCarOrder> pageInfo = PageHelper.startPage(param.getPage(), param.getPageSize()).doSelectPageInfo(() ->
                orderExMapper.getListByParam(param));
        if (pageInfo.getList().isEmpty()) {
            return new PageDTO(param.getPage(), param.getPageSize(), pageInfo.getTotal(), Collections.emptyList());
        }
        List<PublicGovCarOrderRespDTO> resultList = BeanUtil.copyList(pageInfo.getList(), PublicGovCarOrderRespDTO.class);

        List<String> orderNos = pageInfo.getList().stream().map(GovPublicCarOrder::getOrderNo).collect(Collectors.toList());

        //查询人员信息
        GovPublicCarOrderUserInfoExample userInfoExample = new GovPublicCarOrderUserInfoExample();
        userInfoExample.createCriteria().andOrderNoIn(orderNos);
        List<GovPublicCarOrderUserInfo> userInfoList = orderUserInfoMapper.selectByExample(userInfoExample);
        Map<String, List<GovPublicCarOrderUserInfo>> userInfoMap = userInfoList.stream().collect(Collectors.groupingBy(GovPublicCarOrderUserInfo::getOrderNo));

        //查询车辆信息
        GovPublicCarOrderVehicleInfoExample vehicleInfoExample = new GovPublicCarOrderVehicleInfoExample();
        vehicleInfoExample.createCriteria().andOrderNoIn(orderNos);
        List<GovPublicCarOrderVehicleInfo> vehicleInfoList = vehicleInfoMapper.selectByExample(vehicleInfoExample);
        Map<String, GovPublicCarOrderVehicleInfo> vehicleInfoMap = vehicleInfoList.stream().collect(Collectors.toMap(GovPublicCarOrderVehicleInfo::getOrderNo, Function.identity(), (a, b) -> b));

        //查询地址信息
        GovPublicCarOrderAddressInfoExample addressInfoExample = new GovPublicCarOrderAddressInfoExample();
        addressInfoExample.createCriteria().andOrderNoIn(orderNos);
        List<GovPublicCarOrderAddressInfo> addressList = addressInfoMapper.selectByExample(addressInfoExample);
        Map<String, GovPublicCarOrderAddressInfo> addressInfoMap = addressList.stream().collect(Collectors.toMap(GovPublicCarOrderAddressInfo::getOrderNo, Function.identity(), (a, b) -> b));

        resultList.stream().forEach(result -> {
            result.setOrderTypeStr(GovPublicCarOrderTypeEnum.getName(result.getOrderType()));
            result.setApprovalStatusStr(GovPublicCarApproveStatusEnum.getName(result.getApprovalStatus()));
            result.setOrderStatusStr(GovPublicCarOrderStatusEnum.getName(result.getOrderStatus()));
            //费用归属部门置空
            result.setOrderStructName(null);
            //下单人默认值system
            result.setCreateUserName("system");
            //组装下单人信息
            List<GovPublicCarOrderUserInfo> orderUserInfoList = userInfoMap.get(result.getOrderNo());
            if (CollectionUtils.isNotEmpty(orderUserInfoList)) {
                GovPublicCarOrderUserInfo createOrderUserInfo = orderUserInfoList.stream().filter(user -> Objects.equals(user.getUserType(), GovPublicCarOrderUserTypeEnum.ORDER_PLACER.getCode())).findFirst().orElse(null);
                if (Objects.nonNull(createOrderUserInfo)) {
                    result.setCreateCompanyName(createOrderUserInfo.getCompanyName());
                    result.setCreateStructName(createOrderUserInfo.getStructName());
                    result.setCreateUserName(createOrderUserInfo.getUserName());
                }
                //组装主用车人信息
                GovPublicCarOrderUserInfo passengerOrderUserInfo = orderUserInfoList.stream().filter(user -> Objects.equals(user.getUserType(), GovPublicCarOrderUserTypeEnum.PRIMARY_RIDER.getCode())).findFirst().orElse(null);
                if (Objects.nonNull(passengerOrderUserInfo)) {
                    result.setPassengerUserName(passengerOrderUserInfo.getUserName());
                    //费用归属部门取用车人部门
                    result.setOrderStructName(passengerOrderUserInfo.getStructName());
                    // 用车人部门
                    result.setVehicleUsageStructName(passengerOrderUserInfo.getStructName());
                }

                //组装司机信息
                GovPublicCarOrderUserInfo driverOrderUserInfo = orderUserInfoList.stream().filter(user -> Objects.equals(user.getUserType(), GovPublicCarOrderUserTypeEnum.DRIVER.getCode())).findFirst().orElse(null);
                if (Objects.nonNull(driverOrderUserInfo)) {
                    result.setDriverName(driverOrderUserInfo.getUserName());
                }
            }

            //组装车辆信息
            GovPublicCarOrderVehicleInfo vehicleInfo = vehicleInfoMap.get(result.getOrderNo());
            if (Objects.nonNull(vehicleInfo)) {
                result.setDeploymentMode(Integer.valueOf(vehicleInfo.getDeploymentMode()));
                result.setDeploymentModeStr(DeploymentModeEnum.getNameByCode(vehicleInfo.getDeploymentMode()));
                result.setVehicleModelName(vehicleInfo.getVehicleModelName());
                result.setVehicleStructName(vehicleInfo.getVehicleStructName());
            }

            //组装地址信息
            GovPublicCarOrderAddressInfo addressInfo = addressInfoMap.get(result.getOrderNo());
            if (Objects.nonNull(addressInfo)) {
                result.setEstimatedDepartureLocation(addressInfo.getEstimatedDepartureShortLocation());
                result.setEstimatedDestinationLocation(addressInfo.getEstimatedDestinationShortLocation());
                result.setAlarmCode(addressInfo.getAlarmCode());
                result.setStartFenceId(addressInfo.getStartFenceId());
                result.setReturnFenceId(addressInfo.getReturnFenceId());
            }

            //控制强制结束按钮
            if (Objects.equals(result.getOrderStatus(), GovPublicCarOrderStatusEnum.IN_USE.getCode())) {
                result.setShowForceFinishButton(IsShowEnum.SHOW.getCode());
            } else {
                result.setShowForceFinishButton(IsShowEnum.NO_SHOW.getCode());
            }

            if (Objects.equals(result.getOrderStatus(), GovPublicCarOrderStatusEnum.PENDING_DEPARTURE.getCode())) {
                result.setShowCancelButton(IsShowEnum.SHOW.getCode());
            } else {
                result.setShowCancelButton(IsShowEnum.NO_SHOW.getCode());
            }

            //控制派车单按钮
            result.setShowDispatchButton(IsShowEnum.SHOW.getCode());
            // 将时间差转换为小时
            if (Objects.nonNull(result.getOrderStartTime()) && Objects.nonNull(result.getOrderEndTime())) {
                result.setUserTime((calculateDifferenceInHoursAsString(result.getOrderStartTime(), result.getOrderEndTime())));
            }
            // 计算出入栏总时长
            if(Objects.nonNull(result.getPickupLotExitTime()) && Objects.nonNull(result.getReturnLotEntryTime())){
                result.setOutInFenceUseTime(calculateDifferenceInHoursAsString(result.getPickupLotExitTime(), result.getReturnLotEntryTime()));
            }
            if(Objects.nonNull(result.getOutInFenceTotalMileage())){
                result.setOutInFenceTotalMileage(result.getOutInFenceTotalMileage());
            }
        });
        // 计算出入围栏总时长，上面的循环是兜底逻辑
        Map<String, String> outInFenceUseTimeMap = calculateOutInFenceUseTime(resultList.stream().map(PublicGovCarOrderRespDTO::getOrderNo).collect(Collectors.toList()));
        resultList.forEach(order -> {
            String outInFenceInUseTimeStr = outInFenceUseTimeMap.get(order.getOrderNo());
            if(StringUtils.isNotBlank(outInFenceInUseTimeStr)){
                order.setOutInFenceUseTime(outInFenceInUseTimeStr);
            }
        });
        return new PageDTO(param.getPage(), param.getPageSize(), pageInfo.getTotal(), resultList);
    }


    public PageDTO getListForApp(PublicGovCarOrderQueryDTO param) {
        //处理展示状态
        handleOrderApprovalStatus(param);
        setDataPerm(param);
        //分页查询订单列表
        PageInfo<GovPublicCarOrder> pageInfo = PageHelper.startPage(param.getPage(), param.getPageSize()).doSelectPageInfo(() ->
                orderExMapper.getListByParamForApp(param));
        if (pageInfo.getList().isEmpty()) {
            return new PageDTO(param.getPage(), param.getPageSize(), pageInfo.getTotal(), Collections.emptyList());
        }

        Map<String, GovPublicCarOrder> orderMap = pageInfo.getList().stream().collect(Collectors.toMap(GovPublicCarOrder::getOrderNo, Function.identity()));

        List<AppPublicGovCarOrderRespDTO> resultList = BeanUtil.copyList(pageInfo.getList(), AppPublicGovCarOrderRespDTO.class);

        List<String> orderNos = pageInfo.getList().stream().map(GovPublicCarOrder::getOrderNo).collect(Collectors.toList());

        //查询人员信息
        GovPublicCarOrderUserInfoExample userInfoExample = new GovPublicCarOrderUserInfoExample();
        userInfoExample.createCriteria().andOrderNoIn(orderNos);
        List<GovPublicCarOrderUserInfo> userInfoList = orderUserInfoMapper.selectByExample(userInfoExample);
        Map<String, List<GovPublicCarOrderUserInfo>> userInfoMap = userInfoList.stream().collect(Collectors.groupingBy(GovPublicCarOrderUserInfo::getOrderNo));

        //查询车辆信息
        GovPublicCarOrderVehicleInfoExample vehicleInfoExample = new GovPublicCarOrderVehicleInfoExample();
        vehicleInfoExample.createCriteria().andOrderNoIn(orderNos);
        List<GovPublicCarOrderVehicleInfo> vehicleInfoList = vehicleInfoMapper.selectByExample(vehicleInfoExample);
        Map<String, GovPublicCarOrderVehicleInfo> vehicleInfoMap = vehicleInfoList.stream().collect(Collectors.toMap(GovPublicCarOrderVehicleInfo::getOrderNo, Function.identity(), (a, b) -> b));

        //查询地址信息
        GovPublicCarOrderAddressInfoExample addressInfoExample = new GovPublicCarOrderAddressInfoExample();
        addressInfoExample.createCriteria().andOrderNoIn(orderNos);
        List<GovPublicCarOrderAddressInfo> addressList = addressInfoMapper.selectByExample(addressInfoExample);
        Map<String, GovPublicCarOrderAddressInfo> addressInfoMap = addressList.stream().collect(Collectors.toMap(GovPublicCarOrderAddressInfo::getOrderNo, Function.identity(), (a, b) -> b));


        resultList.stream().forEach(result -> {
            result.setApprovalStatusStr(GovPublicCarApproveStatusEnum.getName(result.getApprovalStatus()));
            result.setOrderStatusStr(GovPublicCarOrderStatusEnum.getName(result.getOrderStatus()));
            result.setOrderTypeStr(GovPublicCarOrderTypeEnum.getName(result.getOrderType()));

            //公务用车 待审批，审批撤回，审批驳回  展示审批状态
            if (Objects.equals(result.getOrderType(), GovPublicCarOrderTypeEnum.PUBLIC_USE_CAR_TYPE.getCode())) {
                result.setOrderApprovalStatusStr(GovPublicOrderUtil.getOrderApprovalStatus(result.getOrderStatus(), result.getApprovalStatus()));
            }else {
                result.setOrderApprovalStatusStr(result.getOrderStatusStr());
            }
            result.setOrderApprovalStatus(GovPublicCarApproveStatusCompleteEnum.getCode(result.getOrderApprovalStatusStr()));

            //组装下单人信息
            List<GovPublicCarOrderUserInfo> orderUserInfoList = userInfoMap.get(result.getOrderNo());
            if (CollectionUtils.isNotEmpty(orderUserInfoList)) {
                GovPublicCarOrderUserInfo createOrderUserInfo = orderUserInfoList.stream().filter(user -> Objects.equals(user.getUserType(), GovPublicCarOrderUserTypeEnum.ORDER_PLACER.getCode())).findFirst().orElse(null);
                if (Objects.nonNull(createOrderUserInfo)) {
                    result.setCreateUserInfo(createOrderUserInfo.getUserName() + "/" + createOrderUserInfo.getStructName());
                }
                //组装主用车人信息
                GovPublicCarOrderUserInfo passengerOrderUserInfo = orderUserInfoList.stream().filter(user -> Objects.equals(user.getUserType(), GovPublicCarOrderUserTypeEnum.PRIMARY_RIDER.getCode())).findFirst().orElse(null);
                if (Objects.nonNull(passengerOrderUserInfo)) {
                    result.setPassengerUserInfo(passengerOrderUserInfo.getUserName() + "·" + passengerOrderUserInfo.getUserMobile());
                }

                //组装司机信息
                GovPublicCarOrderUserInfo driverOrderUserInfo = orderUserInfoList.stream().filter(user -> Objects.equals(user.getUserType(), GovPublicCarOrderUserTypeEnum.DRIVER.getCode())).findFirst().orElse(null);
                if (Objects.nonNull(driverOrderUserInfo)) {
                    result.setDriverInfo(driverOrderUserInfo.getUserName() + "·" + driverOrderUserInfo.getUserMobile());
                }
            }

            //组装车辆信息
            GovPublicCarOrderVehicleInfo vehicleInfo = vehicleInfoMap.get(result.getOrderNo());
            if (Objects.nonNull(vehicleInfo)) {
                result.setDeploymentMode(Integer.valueOf(vehicleInfo.getDeploymentMode()));
                result.setDeploymentModeStr(DeploymentModeEnum.getNameByCode(vehicleInfo.getDeploymentMode()));
                result.setVehicleModelName(vehicleInfo.getVehicleModelName());
                result.setVehicleBrandName(vehicleInfo.getVehicleBrandName());
                result.setVehicleStructName(vehicleInfo.getVehicleStructName());
            }

            //组装地址信息
            GovPublicCarOrderAddressInfo addressInfo = addressInfoMap.get(result.getOrderNo());
            if (Objects.nonNull(addressInfo)) {
                result.setEstimatedDepartureLocation(addressInfo.getEstimatedDepartureShortLocation());
                result.setEstimatedDestinationLocation(addressInfo.getEstimatedDestinationShortLocation());
            }

            //组装预约用车实际
            GovPublicCarOrder govPublicCarOrder = orderMap.get(result.getOrderNo());
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd HH:mm");
            if (Objects.equals(govPublicCarOrder.getOrderType(), GovPublicCarOrderTypeEnum.PUBLIC_USE_CAR_TYPE.getCode())) {
                if (Objects.nonNull(govPublicCarOrder.getExpectedPickupTime()) && Objects.nonNull(govPublicCarOrder.getExpectedReturnTime())) {
                    // 格式化日期
                    result.setExpectedPickupTime(dateFormat.format(govPublicCarOrder.getExpectedPickupTime()));
                    result.setExpectedReturnTime(dateFormat.format(govPublicCarOrder.getExpectedReturnTime()));
                }
            }

            if (Objects.equals(govPublicCarOrder.getOrderType(), GovPublicCarOrderTypeEnum.NO_TASK_USE_CAR_TYPE.getCode())) {
                if (Objects.nonNull(govPublicCarOrder.getOrderStartTime())) {
                    result.setExpectedPickupTime(dateFormat.format(govPublicCarOrder.getOrderStartTime()));
                }
                if (Objects.nonNull(govPublicCarOrder.getOrderEndTime())) {
                    result.setExpectedReturnTime(dateFormat.format(govPublicCarOrder.getOrderEndTime()));
                }
            }

            //组装跳转修改行程所需参数
            result.setModifyTravelDTO(buildModifyTravelParam(govPublicCarOrder, orderUserInfoList, vehicleInfo, addressInfo));
        });
        if(Objects.equals(param.getEntryType(), GovPublicCarEntryTypeEnum.NO_TASK_ORDER.getCode())){
            resultList.forEach(order -> {
//                List<Byte> noHandleOrderStatusList =
//                        Lists.newArrayList(GovPublicCarOrderStatusEnum.IN_USE.getCode(), GovPublicCarOrderStatusEnum.PENDING_VERIFICATION.getCode());
                order.setNoTaskOrderHandleStatus(Objects.equals(order.getVerifyStatus(), GovPublicCarVerifyStatusEnum.VERIFIED.getCode()));
            });
        }
        return new PageDTO(param.getPage(), param.getPageSize(), pageInfo.getTotal(), resultList);
    }

    private AppPublicGovCarOrderModifyTravelDTO buildModifyTravelParam(GovPublicCarOrder govPublicCarOrder, List<GovPublicCarOrderUserInfo> orderUserInfoList, GovPublicCarOrderVehicleInfo vehicleInfo, GovPublicCarOrderAddressInfo addressInfo) {

        AppPublicGovCarOrderModifyTravelDTO modifyTravelDTO = new AppPublicGovCarOrderModifyTravelDTO();
        modifyTravelDTO.setCarUseReason(govPublicCarOrder.getCarUseReason());
        modifyTravelDTO.setExpectedPickupTime(govPublicCarOrder.getExpectedPickupTime());
        modifyTravelDTO.setExpectedReturnTime(govPublicCarOrder.getExpectedReturnTime());
        modifyTravelDTO.setOrderUserMemo(govPublicCarOrder.getOrderUserMemo());
        //组装司机信息
        if(CollectionUtils.isNotEmpty(orderUserInfoList)) {
            GovPublicCarOrderUserInfo driverOrderUserInfo = orderUserInfoList.stream().filter(user -> Objects.equals(user.getUserType(), GovPublicCarOrderUserTypeEnum.DRIVER.getCode())).findFirst().orElse(null);
            if (Objects.nonNull(driverOrderUserInfo)) {
                modifyTravelDTO.setDriverCode(driverOrderUserInfo.getUserCode());
                modifyTravelDTO.setDriverId(driverOrderUserInfo.getUserId());
                modifyTravelDTO.setDriverMobile(driverOrderUserInfo.getUserMobile());
                modifyTravelDTO.setDriverName(driverOrderUserInfo.getUserName());
                modifyTravelDTO.setDriverStructCode(driverOrderUserInfo.getStructCode());
                modifyTravelDTO.setDriverStructId(driverOrderUserInfo.getStructId());
                modifyTravelDTO.setDriverStructName(driverOrderUserInfo.getStructName());
            }
        }

        if (Objects.nonNull(addressInfo)) {
            modifyTravelDTO.setEstimatedDepartureLocation(addressInfo.getEstimatedDepartureLocation());
            modifyTravelDTO.setEstimatedDepartureLatitude(addressInfo.getEstimatedDepartureLatitude());
            modifyTravelDTO.setEstimatedDepartureLongitude(addressInfo.getEstimatedDepartureLongitude());
            modifyTravelDTO.setEstimatedDepartureShortLocation(addressInfo.getEstimatedDepartureShortLocation());
            modifyTravelDTO.setEstimatedDestinationLatitude(addressInfo.getEstimatedDestinationLatitude());
            modifyTravelDTO.setEstimatedDestinationLongitude(addressInfo.getEstimatedDestinationLongitude());
            modifyTravelDTO.setEstimatedDestinationLocation(addressInfo.getEstimatedDestinationLocation());
            modifyTravelDTO.setEstimatedDestinationShortLocation(addressInfo.getEstimatedDestinationShortLocation());
            modifyTravelDTO.setInitialFenceId(addressInfo.getInitialFenceId());
            modifyTravelDTO.setInitialLatitude(addressInfo.getInitialLatitude());
            modifyTravelDTO.setInitialLocation(addressInfo.getInitialLocation());
            modifyTravelDTO.setInitialLongitude(addressInfo.getInitialLongitude());
        }

        if (Objects.nonNull(vehicleInfo)) {
            modifyTravelDTO.setVehicleBrandName(vehicleInfo.getVehicleBrandName());
            modifyTravelDTO.setVehicleId(vehicleInfo.getVehicleId());
            modifyTravelDTO.setVehicleLicense(vehicleInfo.getVehicleLicense());
            modifyTravelDTO.setVehicleModelName(vehicleInfo.getVehicleModelName());
            modifyTravelDTO.setDeploymentMod(vehicleInfo.getDeploymentMode());
        }

        if(CollectionUtils.isNotEmpty(orderUserInfoList)) {
            List<GovPublicCarOrderUserInfo> passengersInfoList = orderUserInfoList.stream().filter(user -> Objects.equals(user.getUserType(), GovPublicCarOrderUserTypeEnum.PRIMARY_RIDER.getCode()) || Objects.equals(user.getUserType(), GovPublicCarOrderUserTypeEnum.SECONDARY_RIDER.getCode())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(passengersInfoList)) {
                List<AppPublicGovCarOrderPassengersInfoRespDTO> passengersInfoResponseList = passengersInfoList.stream().map(passenger -> {
                    AppPublicGovCarOrderPassengersInfoRespDTO passengersInfo = new AppPublicGovCarOrderPassengersInfoRespDTO();
                    passengersInfo.setPassengerCode(passenger.getUserCode());
                    passengersInfo.setPassengerId(passenger.getUserId());
                    passengersInfo.setPassengerMobile(passenger.getUserMobile());
                    passengersInfo.setPassengerName(passenger.getUserName());
                    passengersInfo.setPassengerStructName(passenger.getStructName());
                    passengersInfo.setPassengerStructId(passenger.getStructId());
                    passengersInfo.setPassengerStructCode(passenger.getStructCode());
                    return passengersInfo;
                }).collect(Collectors.toList());
                modifyTravelDTO.setPassengersInfoList(passengersInfoResponseList);
            }
        }
        return modifyTravelDTO;
    }


    private void handleOrderApprovalStatus(PublicGovCarOrderQueryDTO param) {
        if (Objects.isNull(param) || Objects.isNull(param.getOrderApprovalStatus())) {
            return;
        }
        switch (param.getOrderApprovalStatus()) {
            case 7:
                param.setOrderStatusList(Lists.newArrayList(GovPublicCarOrderStatusEnum.PENDING_DEPARTURE.getCode()));
                break;
            case 8:
                param.setOrderStatusList(Lists.newArrayList(GovPublicCarOrderStatusEnum.IN_USE.getCode()));
                break;
            case 9:
                param.setOrderStatusList(Lists.newArrayList(GovPublicCarOrderStatusEnum.PENDING_VERIFICATION.getCode(), GovPublicCarOrderStatusEnum.IN_USE.getCode()));
                param.setVerifyStatus(GovPublicCarVerifyStatusEnum.UNVERIFIED.getCode());
                break;
            case 10:
                param.setOrderStatusList(Lists.newArrayList(GovPublicCarOrderStatusEnum.COMPLETED.getCode()));
                break;
            case 11:
                param.setOrderStatusList(Lists.newArrayList(GovPublicCarOrderStatusEnum.CANCELED.getCode()));
                break;
        }
    }


    public static String calculateDifferenceInHoursAsString(Date date1, Date date2) {
        // 计算两个日期之间的时间差，单位为毫秒
        double diffInMillies = Math.abs(date2.getTime() - date1.getTime());

        // 将时间差转换为小时
        double diffInHours = diffInMillies / (1000 * 60 * 60);

        // 如果大于或等于1小时，直接格式化为小时
        return String.format("%.2f",diffInHours);
    }

    private List<String> handlerCarUserInfoParam(PublicGovCarOrderQueryDTO param, boolean createSelect, boolean passengerSelect, boolean driverSelect) {

        List<String> createUserInfoList = Lists.newArrayList();
        List<String> passengerUserInfoList = Lists.newArrayList();
        List<String> driverUserInfoList = Lists.newArrayList();


        //处理下单人
        if (createSelect) {
            GovPublicCarOrderUserInfoExample govPublicCarOrderUserInfoExample = new GovPublicCarOrderUserInfoExample();
            GovPublicCarOrderUserInfoExample.Criteria criteria = govPublicCarOrderUserInfoExample.createCriteria()
                    .andUserTypeEqualTo(GovPublicCarOrderUserTypeEnum.ORDER_PLACER.getCode());
            if (StringUtils.isNotEmpty(param.getCreateCompanyCode())) {
                criteria.andCompanyCodeEqualTo(param.getCreateCompanyCode());
            }
            if (CollectionUtils.isNotEmpty(param.getCompanyCodes())) {
                criteria.andCompanyCodeIn(param.getCompanyCodes());
            }
            if (StringUtils.isNotEmpty(param.getCreateStructCode())) {
                criteria.andStructCodeEqualTo(param.getCreateStructCode());
            }
            if (StringUtils.isNotEmpty(param.getCreateUserName())) {
                criteria.andUserNameEqualTo(param.getCreateUserName());
            }
            if (StringUtils.isNotEmpty(param.getCreateUserMobile())) {
                criteria.andUserMobileEqualTo(param.getCreateUserMobile());
            }
            createUserInfoList = orderUserInfoMapper.selectByExample(govPublicCarOrderUserInfoExample).stream().map(GovPublicCarOrderUserInfo::getOrderNo).collect(Collectors.toList());
        }

        //处理用车人
        if (passengerSelect) {
            GovPublicCarOrderUserInfoExample govPublicCarOrderUserInfoExample = new GovPublicCarOrderUserInfoExample();
            GovPublicCarOrderUserInfoExample.Criteria criteria = govPublicCarOrderUserInfoExample.createCriteria()
                    .andUserTypeIn(Arrays.asList(GovPublicCarOrderUserTypeEnum.PRIMARY_RIDER.getCode(), GovPublicCarOrderUserTypeEnum.SECONDARY_RIDER.getCode()));
            if (StringUtils.isNotEmpty(param.getPassengerUserName())) {
                criteria.andUserNameEqualTo(param.getPassengerUserName());
            }
            if (StringUtils.isNotEmpty(param.getPassengerUserMobile())) {
                criteria.andUserMobileEqualTo(param.getPassengerUserMobile());
            }
            passengerUserInfoList = orderUserInfoMapper.selectByExample(govPublicCarOrderUserInfoExample).stream().map(GovPublicCarOrderUserInfo::getOrderNo).collect(Collectors.toList());
        }

        //处理司机
        if (driverSelect) {
            GovPublicCarOrderUserInfoExample govPublicCarOrderUserInfoExample = new GovPublicCarOrderUserInfoExample();
            GovPublicCarOrderUserInfoExample.Criteria criteria = govPublicCarOrderUserInfoExample.createCriteria()
                    .andUserTypeEqualTo(GovPublicCarOrderUserTypeEnum.DRIVER.getCode());
            if (StringUtils.isNotEmpty(param.getDriverName())) {
                criteria.andUserNameEqualTo(param.getDriverName());
            }
            if (StringUtils.isNotEmpty(param.getDriverMobile())) {
                criteria.andUserMobileEqualTo(param.getDriverMobile());
            }
            driverUserInfoList = orderUserInfoMapper.selectByExample(govPublicCarOrderUserInfoExample).stream().map(GovPublicCarOrderUserInfo::getOrderNo).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(createUserInfoList) && CollectionUtils.isEmpty(passengerUserInfoList) && CollectionUtils.isEmpty(driverUserInfoList)) {
            return Lists.newArrayList();
        }


        //合并list
        List<List<String>> lists = Lists.newArrayList();
        if (createSelect) {
            lists.add(createUserInfoList);
        }

        if (passengerSelect) {
            lists.add(passengerUserInfoList);
        }

        if (driverSelect) {
            lists.add(driverUserInfoList);
        }

        //多个list取交集
        List<String> mixList = lists.stream()
                .reduce((list1, list2) -> {
                    // 使用retainAll来找到两个列表的交集，并返回新列表
                    list1.retainAll(list2);
                    return list1;
                })
                .orElse(null);// 如果没有列表或者所有列表都是空的，则返回null或者可以选择返回一个空列表

        return mixList;
    }

    /**
     * 处理审批相关操作
     *
     * @param bpmMessageSendApproveResultDTO
     */
    @Transactional(rollbackFor = Exception.class)
    public void consumeMessage(BpmMessageSendApproveResultDTO bpmMessageSendApproveResultDTO) {
        String orderNo = bpmMessageSendApproveResultDTO.getBusinessNo();
        List<GovPublicCarOrder> govPublicCarOrders = getGovPublicCarOrders(orderNo);
        if (CollectionUtils.isEmpty(govPublicCarOrders)) {
            log.info("公务用车订单接收审批流消息，找不到相关订单：{}", orderNo);
            return;
        }
        //根据订单号查询订单
        GovPublicCarOrder govPublicCarOrder = govPublicCarOrders.get(0);
        //老单子已经是撤回状态则消息丢弃
        if(GovPublicCarOrderStatusEnum.CANCELED.getCode().equals(govPublicCarOrder.getOrderStatus())){
            log.info("公务用车订单接收审批流消息，订单已经被取消：{}",orderNo);
            return;
        }
        BpmProcessInstanceResultEnum bpmProcessInstanceResultEnum = BpmProcessInstanceResultEnum.getEnum(bpmMessageSendApproveResultDTO.getResult());
        GovPublicCarOrder govPublicCarOrderUpdate = new GovPublicCarOrder();
        govPublicCarOrderUpdate.setOrderId(govPublicCarOrder.getOrderId());
        govPublicCarOrderUpdate.setUpdateTime(bpmMessageSendApproveResultDTO.getApproverTime());
        govPublicCarOrderUpdate.setApprovalCompletedTime(bpmMessageSendApproveResultDTO.getApproverTime());
        switch (bpmProcessInstanceResultEnum) {
            //审批通过
            case APPROVE:
                //修改订单状态为通过
                govPublicCarOrderUpdate.setApprovalStatus(GovPublicCarApproveStatusEnum.PASS.getCode());
                outOperateLogApprove(bpmMessageSendApproveResultDTO.getApproverTime(), govPublicCarOrder,GovPublicCarOrderOperationTypeEnum.APPROVAL_PASS,GovPublicCarOrderStatusEnum.PENDING_DEPARTURE,bpmMessageSendApproveResultDTO);
                break;
            //驳回
            case REJECT:
                //修改订单审批状态为驳回，订单状态为已取消，并释放车态
                govPublicCarOrderUpdate.setApprovalStatus(GovPublicCarApproveStatusEnum.TURN_DOWN.getCode());
                govPublicCarOrderUpdate.setOrderStatus(GovPublicCarOrderStatusEnum.CANCELED.getCode());
                outOperateLogApprove(bpmMessageSendApproveResultDTO.getApproverTime(), govPublicCarOrder,GovPublicCarOrderOperationTypeEnum.APPROVAL_REJECTED,GovPublicCarOrderStatusEnum.CANCELED,bpmMessageSendApproveResultDTO);
                //释放车态
                OrderAssetRestApi.updateVehicleWorkingStatus(govPublicCarOrder.getVehicleId().longValue(),"",VehicleEnum.WorkingStatus.NO_SERVICE.getValue(),"order");
                break;
            case CANCEL:
                //修改订单审批状态为取消，订单状态为已取消，并释放车态
                govPublicCarOrderUpdate.setApprovalStatus(GovPublicCarApproveStatusEnum.CANCEL.getCode());
                govPublicCarOrderUpdate.setOrderStatus(GovPublicCarOrderStatusEnum.CANCELED.getCode());
                govPublicCarOrderUpdate.setCancellationTime(bpmMessageSendApproveResultDTO.getApproverTime());
                outOperateLogApprove(bpmMessageSendApproveResultDTO.getApproverTime(), govPublicCarOrder,GovPublicCarOrderOperationTypeEnum.APPROVAL_WITHDRAW,GovPublicCarOrderStatusEnum.CANCELED,bpmMessageSendApproveResultDTO);
                //释放车态
                OrderAssetRestApi.updateVehicleWorkingStatus(govPublicCarOrder.getVehicleId().longValue(),"",VehicleEnum.WorkingStatus.NO_SERVICE.getValue(),"order");
                break;
            case BACK:
                //修改订单审批状态为，订单状态为已取消，并释放车态
                govPublicCarOrderUpdate.setApprovalStatus(GovPublicCarApproveStatusEnum.WITHDRAW.getCode());
                govPublicCarOrderUpdate.setOrderStatus(GovPublicCarOrderStatusEnum.CANCELED.getCode());
                govPublicCarOrderUpdate.setApprovalWithdrawalType(ApprovalWithdrawalTypeEnum.PERSON.getType());
                outOperateLogApprove(bpmMessageSendApproveResultDTO.getApproverTime(), govPublicCarOrder,GovPublicCarOrderOperationTypeEnum.APPROVAL_WITHDRAW,GovPublicCarOrderStatusEnum.CANCELED,bpmMessageSendApproveResultDTO);
                //释放车态
                OrderAssetRestApi.updateVehicleWorkingStatus(govPublicCarOrder.getVehicleId().longValue(),"",VehicleEnum.WorkingStatus.NO_SERVICE.getValue(),"order");
                break;
            default:
                log.info("公务用车订单接收审批流消息，审批结果未知：{}", bpmMessageSendApproveResultDTO);
        }
        //更新订单表
        orderExMapper.updateByPrimaryKeySelective(govPublicCarOrderUpdate);
    }

    private List<GovPublicCarOrder> getGovPublicCarOrders(String orderNo) {
        GovPublicCarOrderExample govPublicCarOrderExample = new GovPublicCarOrderExample();
        govPublicCarOrderExample.createCriteria().andOrderNoEqualTo(orderNo);
        return orderExMapper.selectByExample(govPublicCarOrderExample);
    }


    public RestResponse getDetail(String orderNo) {
        PublicGovCarOrderDetailInfoRespDTO publicGovCarOrderDetailInfoRespDTO = new PublicGovCarOrderDetailInfoRespDTO();

        //查询订单主表
        List<GovPublicCarOrder> govPublicCarOrders = getGovPublicCarOrders(orderNo);
        if (CollectionUtils.isEmpty(govPublicCarOrders)) {
            return RestResponse.create(RestErrorCode.SYSTEM_EXCEPTION, "订单不存在", false, null);
        }
        GovPublicCarOrder govPublicCarOrder = govPublicCarOrders.stream().findFirst().orElse(null);
        PublicGovCarOrderInfoDTO orderInfoDTO = new PublicGovCarOrderInfoDTO();
        BeanUtils.copyProperties(govPublicCarOrder, orderInfoDTO);


        //查询用车信息
        GovPublicCarOrderVehicleInfo vehicleInfo = getVehicleInfo(orderNo);
        if (Objects.isNull(vehicleInfo)) {
            return RestResponse.create(RestErrorCode.SYSTEM_EXCEPTION, "车辆信息不存在", false, null);
        }
        PublicGovVehicleInfoDTO vehicleInfoDto = new PublicGovVehicleInfoDTO();
        BeanUtils.copyProperties(vehicleInfo, vehicleInfoDto);
        // 计算出入栏总时长
        if(Objects.nonNull(govPublicCarOrder.getPickupLotExitTime()) && Objects.nonNull(govPublicCarOrder.getReturnLotEntryTime())){
            vehicleInfoDto.setOutInFenceUseTime(calculateDifferenceInHoursAsString(govPublicCarOrder.getPickupLotExitTime(), govPublicCarOrder.getReturnLotEntryTime()));
        }
        Map<String, String> outInFenceUseTimeMap = calculateOutInFenceUseTime(Lists.newArrayList(govPublicCarOrder.getOrderNo()));
        String outInFenceInUseTimeStr = outInFenceUseTimeMap.get(govPublicCarOrder.getOrderNo());
        if(StringUtils.isNotBlank(outInFenceInUseTimeStr)){
            vehicleInfoDto.setOutInFenceUseTime(outInFenceInUseTimeStr);
        }
        if(Objects.nonNull(govPublicCarOrder.getOutInFenceTotalMileage())){
            vehicleInfoDto.setOutInFenceTotalMileage(govPublicCarOrder.getOutInFenceTotalMileage());
        }

        //查询人员信息
        List<GovPublicCarOrderUserInfo> userInfoList = getUserInfo(orderNo);
        GovPublicCarOrderUserInfo mainUser = userInfoList.stream().filter(userInfo -> Objects.equals(userInfo.getUserType(), GovPublicCarOrderUserTypeEnum.PRIMARY_RIDER.getCode())).findFirst().orElse(null);

        //查询地址信息
        GovPublicCarOrderAddressInfo addressInfo = getAddressInfo(orderNo);

        //订单基本信息
        handlerUserInfo(govPublicCarOrder, orderInfoDTO, userInfoList, addressInfo);

        //订单车辆信息
        handlerVehicleInfo(govPublicCarOrder, vehicleInfoDto, userInfoList, vehicleInfo, addressInfo);

        //填充订单基础信息
        publicGovCarOrderDetailInfoRespDTO.setOrderInfo(orderInfoDTO);

        //填充车辆信息
        publicGovCarOrderDetailInfoRespDTO.setVehicleInfo(vehicleInfoDto);

        //填充日志数据  后续要干掉 TODO
//        publicGovCarOrderDetailInfoRespDTO.setOperationLogList(queryOperationLog(orderNo));

        //填充单日用车信息
        publicGovCarOrderDetailInfoRespDTO.setOrderSubDailyList(queryDailyInfo(govPublicCarOrder, vehicleInfo, mainUser));

        // 查询审批单信息
        if(StringUtils.isNotBlank(govPublicCarOrder.getApprovalId())){
            publicGovCarOrderDetailInfoRespDTO.setProcessInstanceId(govPublicCarOrder.getApprovalId());
        }
//        orderExecutorService
        CompletableFuture<Boolean> warnRecordTask = CompletableFuture.supplyAsync(() -> {
            //根据报警编号查询报警信息
            OfficialVehicleWarnRecordDTO warnRecord = queryWarnRecord(addressInfo);
            if (Objects.nonNull(warnRecord)) {
                publicGovCarOrderDetailInfoRespDTO.setWarnRecord(warnRecord);
                //报警编号(无任务订单,无审批才会展示)
                if (Objects.equals(govPublicCarOrder.getOrderType(), GovPublicCarOrderTypeEnum.NO_TASK_USE_CAR_TYPE.getCode()) &&
                        Objects.equals(govPublicCarOrder.getApprovalStatus(), GovPublicCarApproveStatusEnum.NO_APPROVAL.getCode())) {
                    vehicleInfoDto.setAlarmTypeStr(warnRecord.getWarnTypeStr());
                }
            } else {
                //兼容正常下单 车辆未出栏 正常展示轨迹
                OfficialVehicleWarnRecordDTO officialVehicleWarn = new OfficialVehicleWarnRecordDTO();
                officialVehicleWarn.setDeviceNo(addressInfo.getDeviceId());
                officialVehicleWarn.setVehicleVin(govPublicCarOrder.getVehicleVin());
                officialVehicleWarn.setVehicleLicense(govPublicCarOrder.getVehicleLicense());
                officialVehicleWarn.setDeviceType(addressInfo.getDeviceType());
                publicGovCarOrderDetailInfoRespDTO.setWarnRecord(officialVehicleWarn);
            }
            return true;
        }, orderExecutorService);

        CompletableFuture<Boolean> gpsFenceTask = CompletableFuture.supplyAsync(() -> {
            //pc端 都查询围栏快照 且需要兼容只有出栏 没有入栏的逻辑
            if (Objects.nonNull(addressInfo)) {
                String snapIds;
                if (Objects.nonNull(addressInfo.getStartFenceId()) && addressInfo.getStartFenceId() > 0) {
                    snapIds = String.valueOf(addressInfo.getStartFenceId());
                    if (Objects.nonNull(addressInfo.getReturnFenceId()) && addressInfo.getReturnFenceId() > 0) {
                        snapIds = snapIds + "," + addressInfo.getReturnFenceId();
                    }
                } else {
                    snapIds = String.valueOf(addressInfo.getInitialFenceId());
                }
                List<CarGpsFenceDTO> gpsFenceDTOList = queryBySnapIds(snapIds);
                if (CollectionUtils.isNotEmpty(gpsFenceDTOList)) {
                    publicGovCarOrderDetailInfoRespDTO.setGpsFenceList(gpsFenceDTOList);
                }
            }
            return true;
        }, orderExecutorService);
        CompletableFuture<Boolean> deviceTask = CompletableFuture.supplyAsync(() -> {
            // 设置绑定设备信息
            if(StringUtils.isNotBlank(govPublicCarOrder.getVehicleVin())){
                setDeviceList(publicGovCarOrderDetailInfoRespDTO, govPublicCarOrder, addressInfo);
            }
            return true;
        }, orderExecutorService);
        CompletableFuture.allOf(warnRecordTask, gpsFenceTask, deviceTask).join();
        return RestResponse.success(publicGovCarOrderDetailInfoRespDTO);
    }

    private Map<String, String> calculateOutInFenceUseTime(List<String> orderNoList){
        List<Integer> optTypeList = Lists.newArrayList(GovPublicCarOrderOperationTypeEnum.END_TRIP.getCode(),
                GovPublicCarOrderOperationTypeEnum.FORCE_END_TRIP.getCode(),
                GovPublicCarOrderOperationTypeEnum.OUT_FENCE.getCode(),
                GovPublicCarOrderOperationTypeEnum.IN_FENCE.getCode());
        OrderOperationLogExample operationLogExample = new OrderOperationLogExample();
        operationLogExample.createCriteria().andOrderNoIn(orderNoList).andOperationTypeIn(optTypeList);
        List<OrderOperationLog> orderOperationLogs = orderOperationLogMapper.selectByExample(operationLogExample);
        if (CollectionUtils.isEmpty(orderOperationLogs)) {
            return Maps.newHashMap();
        }
        // 根据orderNo 转map
        Map<String, List<OrderOperationLog>> operationLogMap = orderOperationLogs.stream().collect(Collectors.groupingBy(OrderOperationLog::getOrderNo));
        Map<String, String> resultMap = Maps.newHashMap();
        operationLogMap.forEach((orderNo, logs) -> {
            logs.sort(Comparator.comparing(OrderOperationLog::getOperationTime));
            Stack<OrderOperationLog> logStack = new Stack<>();
            long outInFenceUseTime = 0L;
            for(OrderOperationLog log : logs){
                if(Objects.equals(log.getOperationType(), GovPublicCarOrderOperationTypeEnum.OUT_FENCE.getCode())){
                    while(!logStack.isEmpty()){
                        logStack.pop();
                    }
                    logStack.push(log);
                }else if(Objects.equals(log.getOperationType(), GovPublicCarOrderOperationTypeEnum.IN_FENCE.getCode())){
                    if(logStack.isEmpty()){
                        continue;
                    }
                    OrderOperationLog outFenceLog = logStack.pop();
                    outInFenceUseTime = outInFenceUseTime + Math.abs(log.getOperationTime().getTime() - outFenceLog.getOperationTime().getTime());
                }else if(Objects.equals(log.getOperationType(), GovPublicCarOrderOperationTypeEnum.END_TRIP.getCode())
                    || Objects.equals(log.getOperationType(), GovPublicCarOrderOperationTypeEnum.FORCE_END_TRIP.getCode())){
                    if(logStack.isEmpty()){
                        continue;
                    }
                    OrderOperationLog tmpLog = null;
                    while(!logStack.isEmpty()){
                        tmpLog = logStack.pop();
                    }
                    if(Objects.nonNull(tmpLog) && Objects.equals(tmpLog.getOperationType(), GovPublicCarOrderOperationTypeEnum.OUT_FENCE.getCode())){
                        outInFenceUseTime = outInFenceUseTime + Math.abs(log.getOperationTime().getTime() - tmpLog.getOperationTime().getTime());
                    }
                }
            }
            if(outInFenceUseTime > 0){
                // 将时间差转换为小时
                BigDecimal diffInHours = new BigDecimal(outInFenceUseTime).divide(new BigDecimal(1000 * 60 * 60), 2, BigDecimal.ROUND_HALF_DOWN);
                // 如果大于或等于1小时，直接格式化为小时
                resultMap.put(orderNo, diffInHours.toString());
            }
        });
        return resultMap;
    }


    private void setDeviceList(PublicGovCarOrderDetailInfoRespDTO respDTO, GovPublicCarOrder govPublicCarOrder, GovPublicCarOrderAddressInfo addressInfo){
        if(Objects.isNull(addressInfo) || StringUtils.isBlank(addressInfo.getDeviceId())){
            return;
        }
        ListVehicleDeviceInfoReqDTO reqDTO = new ListVehicleDeviceInfoReqDTO();
        reqDTO.setCompanyId(govPublicCarOrder.getCompanyId());
        reqDTO.setVehicleVin(govPublicCarOrder.getVehicleVin());
        reqDTO.setSimNo(addressInfo.getDeviceId());
        List<VehicleDeviceInfoDTO> gpsVehicleList =
                MrCarIotRestApi.listVehicleDeviceInfo(reqDTO);
        if(CollectionUtils.isEmpty(gpsVehicleList)){
            return;
        }
        respDTO.setDeviceList(BeanUtil.copyList(gpsVehicleList, VehicleBindDeviceInfoDTO.class));
    }


   public void  handlerUserInfo(GovPublicCarOrder govPublicCarOrder,PublicGovCarOrderInfoDTO orderInfoDTO,List<GovPublicCarOrderUserInfo> userInfoList,GovPublicCarOrderAddressInfo addressInfo){
       orderInfoDTO.setOrderTypeStr(GovPublicCarOrderTypeEnum.getName(govPublicCarOrder.getOrderType()));
       orderInfoDTO.setApprovalStatusStr(GovPublicCarApproveStatusEnum.getName(govPublicCarOrder.getApprovalStatus()));
       orderInfoDTO.setOrderStatusStr(GovPublicCarOrderStatusEnum.getName(govPublicCarOrder.getOrderStatus()));


       //下单人
       GovPublicCarOrderUserInfo createUser = userInfoList.stream().filter(userInfo -> Objects.equals(userInfo.getUserType(), GovPublicCarOrderUserTypeEnum.ORDER_PLACER.getCode())).findFirst().orElse(null);
       if (Objects.nonNull(createUser)) {
           orderInfoDTO.setCreateUserName(createUser.getUserName() + createUser.getUserMobile());
           orderInfoDTO.setCreateCompanyName(createUser.getCompanyName());
           orderInfoDTO.setCreateTime(createUser.getCreateTime());
           orderInfoDTO.setCreateStructName(createUser.getStructName());
       }else {
           orderInfoDTO.setCreateUserName("system");
       }


       //用车人信息
       List<GovPublicCarOrderUserInfo> passengerInfoList = userInfoList.stream().filter(userInfo -> Objects.equals(userInfo.getUserType(), GovPublicCarOrderUserTypeEnum.PRIMARY_RIDER.getCode()) || Objects.equals(userInfo.getUserType(), GovPublicCarOrderUserTypeEnum.SECONDARY_RIDER.getCode())).collect(Collectors.toList());
       if(CollectionUtils.isNotEmpty(passengerInfoList)){
           List<String> passengerUserList = passengerInfoList.stream().map(passenger ->
                   passenger.getUserName() + " " + passenger.getUserMobile()
           ).collect(Collectors.toList());
           List<String> passengerStructList = passengerInfoList.stream().map(passenger -> passenger.getStructName()).collect(Collectors.toList());
           orderInfoDTO.setPassengerUserList(passengerUserList);
           orderInfoDTO.setPassengerStructList(passengerStructList);
       }

       if (Objects.nonNull(addressInfo)) {
           orderInfoDTO.setEstimatedDepartureLocation(addressInfo.getEstimatedDepartureShortLocation());
           orderInfoDTO.setEstimatedDestinationLocation(addressInfo.getEstimatedDestinationShortLocation());
       }
   }


  public void handlerVehicleInfo(GovPublicCarOrder govPublicCarOrder,PublicGovVehicleInfoDTO vehicleInfoDto,List<GovPublicCarOrderUserInfo> userInfoList,GovPublicCarOrderVehicleInfo vehicleInfo,GovPublicCarOrderAddressInfo addressInfo){
      //司机
      GovPublicCarOrderUserInfo driver = userInfoList.stream().filter(userInfo -> Objects.equals(userInfo.getUserType(), GovPublicCarOrderUserTypeEnum.DRIVER.getCode())).findFirst().orElse(null);
      if (Objects.nonNull(driver)) {
          vehicleInfoDto.setDriverName(driver.getUserName() + driver.getUserMobile());
      }

      //订单开始操作人
      //如果是已取消 置为null
      if (Objects.equals(govPublicCarOrder.getOrderStatus(), GovPublicCarOrderStatusEnum.CANCELED.getCode())) {
          vehicleInfoDto.setOrderStartOperator(null);
      } else {
          GovPublicCarOrderUserInfo startOperator = userInfoList.stream().filter(userInfo -> Objects.equals(userInfo.getUserType(), GovPublicCarOrderUserTypeEnum.ORDER_START_OPERATOR.getCode())).findFirst().orElse(null);
          if (Objects.nonNull(startOperator)) {
              vehicleInfoDto.setOrderStartOperator(startOperator.getUserName() + startOperator.getUserMobile());
          } else {
              OrderOperationLogExample operationLogExample = new OrderOperationLogExample();
              operationLogExample.createCriteria().andOrderNoEqualTo(govPublicCarOrder.getOrderNo()).andOperationTypeEqualTo(GovPublicCarOrderOperationTypeEnum.OUT_FENCE.getCode());
              List<OrderOperationLog> orderOperationLogs = orderOperationLogMapper.selectByExample(operationLogExample);
              if (CollectionUtils.isNotEmpty(orderOperationLogs)) {
                  vehicleInfoDto.setOrderStartOperator("system");
              } else {
                  vehicleInfoDto.setOrderStartOperator(null);
              }
          }
      }

      //订单结束操作人
      //如果是已取消 置为null
      GovPublicCarOrderUserInfo forceEndOperator = userInfoList.stream().filter(userInfo -> Objects.equals(userInfo.getUserType(), GovPublicCarOrderUserTypeEnum.ORDER_FORCE_END_OPERATOR.getCode())).findFirst().orElse(null);
      if (Objects.equals(govPublicCarOrder.getOrderStatus(), GovPublicCarOrderStatusEnum.CANCELED.getCode())) {
          vehicleInfoDto.setOrderEndOperator(null);
      } else {
          //无任务用车取强制结束人 -> 日志驶入围栏 -> null
          if (Objects.equals(govPublicCarOrder.getOrderType(), GovPublicCarOrderTypeEnum.NO_TASK_USE_CAR_TYPE.getCode())) {
              if (Objects.nonNull(forceEndOperator)) {
                  vehicleInfoDto.setOrderEndOperator(forceEndOperator.getUserName() + forceEndOperator.getUserMobile());
              } else {
                  OrderOperationLogExample operationLogExample = new OrderOperationLogExample();
                  operationLogExample.createCriteria().andOrderNoEqualTo(govPublicCarOrder.getOrderNo()).andOperationTypeEqualTo(GovPublicCarOrderOperationTypeEnum.IN_FENCE.getCode());
                  List<OrderOperationLog> orderOperationLogs = orderOperationLogMapper.selectByExample(operationLogExample);
                  if (CollectionUtils.isNotEmpty(orderOperationLogs)) {
                      vehicleInfoDto.setOrderEndOperator("system");
                  } else {
                      vehicleInfoDto.setOrderEndOperator(null);
                  }
              }
          } else {
              //有任务订单判断状态 取正常结束操作人 -> 强制结束操作人
              GovPublicCarOrderUserInfo endOperator = userInfoList.stream().filter(userInfo -> Objects.equals(userInfo.getUserType(), GovPublicCarOrderUserTypeEnum.ORDER_END_OPERATOR.getCode())).findFirst().orElse(null);
              if (Objects.nonNull(endOperator)) {
                  vehicleInfoDto.setOrderEndOperator(endOperator.getUserName() + endOperator.getUserMobile());
              } else {
                  if (Objects.nonNull(forceEndOperator)) {
                      vehicleInfoDto.setOrderEndOperator(forceEndOperator.getUserName() + forceEndOperator.getUserMobile());
                  } else {
                      vehicleInfoDto.setOrderEndOperator(null);
                  }
              }
          }
      }

      //投放模式
      vehicleInfoDto.setDeploymentMode(vehicleInfo.getDeploymentMode());
      vehicleInfoDto.setDeploymentModeStr(GovPublicCarDeploymentModeEnum.getName(vehicleInfo.getDeploymentMode()));

      //品牌车型
      vehicleInfoDto.setVehicleModelName(vehicleInfo.getVehicleBrandName() + vehicleInfo.getVehicleModelName());

      //实际开始实际/实际结束实际
      vehicleInfoDto.setOrderStartTime(govPublicCarOrder.getOrderStartTime());
      vehicleInfoDto.setOrderEndTime(govPublicCarOrder.getOrderEndTime());

      if (Objects.nonNull(govPublicCarOrder.getOrderEndTime()) && Objects.nonNull(govPublicCarOrder.getOrderStartTime())) {
          //用车总时长
          vehicleInfoDto.setUserTime((calculateDifferenceInHoursAsString(govPublicCarOrder.getOrderStartTime(), govPublicCarOrder.getOrderEndTime())));
      }

      //用车总里程
      vehicleInfoDto.setTotalMileage(govPublicCarOrder.getTotalMileage());

      //报警编号(无任务订单,无审批才会展示)
      if (Objects.equals(govPublicCarOrder.getOrderType(),GovPublicCarOrderTypeEnum.NO_TASK_USE_CAR_TYPE.getCode()) &&
              Objects.equals(govPublicCarOrder.getApprovalStatus(),GovPublicCarApproveStatusEnum.NO_APPROVAL.getCode()) &&
              Objects.nonNull(addressInfo)) {
          vehicleInfoDto.setAlarmCode(addressInfo.getAlarmCode());
      }
  }

    private OfficialVehicleWarnRecordDTO queryWarnRecord(GovPublicCarOrderAddressInfo addressInfo) {
        if (Objects.isNull(addressInfo) || StringUtils.isEmpty(addressInfo.getAlarmCode())) {
            return null;
        }
        String restUrl = new IotRestLocator().getRestUrl("/warn/getNoticeRecordBySn");
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put("warnSn", addressInfo.getAlarmCode());
        //调用获取围栏信息
        RestResponse response = RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paramMap, null, OfficialVehicleWarnRecordDTO.class);
        if (!response.isSuccess()) {
            log.error("调用Mr.car获取报警信息失败:{}", JSON.toJSONString(response));
        }
        if (Objects.isNull(response) || Objects.isNull(response.getData())) {
            return null;
        }
        return (OfficialVehicleWarnRecordDTO) response.getData();
    }

    private List<CarGpsFenceDTO> queryBySnapIds(String snapIds) {
        if(StringUtils.isEmpty(snapIds)){
            return Lists.newArrayList();
        }
        //根据这个接口查询轨迹
        String restUrl = new IotRestLocator().getRestUrl("/fence/queryBySnapIds");
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put("snapIds", snapIds);
        RestResponse response = RestClient.requestForList(BaseHttpClient.HttpMethod.POST, restUrl, paramMap, null, CarGpsFenceDTO.class);
        if (!response.isSuccess()) {
            log.error("调用Mr.car获取报围栏信息失败:{}", JSON.toJSONString(response));
        }
        if (Objects.isNull(response) || Objects.isNull(response.getData())) {
            return Lists.newArrayList();
        }
        List<CarGpsFenceDTO> gpsFenceList = (List<CarGpsFenceDTO>) response.getData();
        return gpsFenceList.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }


    private List<PublicGovCarOrderSubDailyDTO> queryDailyInfo(GovPublicCarOrder govPublicCarOrder, GovPublicCarOrderVehicleInfo vehicleInfo, GovPublicCarOrderUserInfo mainUser) {
        GovPublicCarOrderSubDailyExample subDailyExample = new GovPublicCarOrderSubDailyExample();
        subDailyExample.createCriteria().andOrderNoEqualTo(govPublicCarOrder.getOrderNo());
        List<GovPublicCarOrderSubDaily> subDailyList = subDailyMapper.selectByExample(subDailyExample);
        if (CollectionUtils.isEmpty(subDailyList)) {
            return Lists.newArrayList();
        }

        return subDailyList.stream().map(subDaily -> {
            PublicGovCarOrderSubDailyDTO subDailyDTO = new PublicGovCarOrderSubDailyDTO();
            subDailyDTO.setOrderNo(subDaily.getOrderNo());
            subDailyDTO.setVehicleLicense(govPublicCarOrder.getVehicleLicense());
            subDailyDTO.setVehicleStructName(vehicleInfo.getVehicleStructName());
            subDailyDTO.setPassengerStructName(Objects.nonNull(mainUser) ? mainUser.getStructName() : null);
            subDailyDTO.setStatDate(DateUtil.format(subDaily.getStatDate(), DateUtil.DATE_FORMAT));
            subDailyDTO.setExpectedPickupTime(DateUtil.format(subDaily.getExpectedPickupTime(), DateUtil.HOUR_TIME_FORMAT_2));
            subDailyDTO.setActualReturnTime(DateUtil.format(subDaily.getActualReturnTime(), DateUtil.HOUR_TIME_FORMAT_2));
            subDailyDTO.setFeeStructName(govPublicCarOrder.getOrderStructName());
            subDailyDTO.setTotalMileage(subDaily.getTotalMileage());
            subDailyDTO.setUserTime((Objects.nonNull(subDaily.getActualReturnTime()) && Objects.nonNull(subDaily.getActualReturnTime())) ? (calculateDifferenceInHoursAsString(subDaily.getExpectedPickupTime(), subDaily.getActualReturnTime())) : null);
            return subDailyDTO;
        }).collect(Collectors.toList());

    }


    //查询操作日志
    public List<PublicGovCarOrderOperationLogDTO> queryOperationLog(String orderNo) {
        //查询操作日志
        OrderOperationLogExample operationLogExample = new OrderOperationLogExample();
        operationLogExample.createCriteria().andOrderNoEqualTo(orderNo);
        List<OrderOperationLog> orderOperationLogs = orderOperationLogMapper.selectByExample(operationLogExample);
        if (CollectionUtils.isEmpty(orderOperationLogs)) {
            return Lists.newArrayList();
        }

        List<PublicGovCarOrderOperationLogDTO> operationLogDTOList = orderOperationLogs.stream().map(operationLog -> {
            PublicGovCarOrderOperationLogDTO operationLogDTO = new PublicGovCarOrderOperationLogDTO();
            operationLogDTO.setOperationTime(operationLog.getOperationTime());
            operationLogDTO.setId(operationLog.getId());
            operationLogDTO.setOperation(GovPublicCarOrderOperationTypeEnum.getName(operationLog.getOperationType()));
            operationLogDTO.setOrderStatusStr(operationLog.getOrderStatusName());
            operationLogDTO.setOperatorName(operationLog.getOperatorName() + operationLog.getOperatorMobile());
            return operationLogDTO;
        }).collect(Collectors.toList());

        //根据操作时间倒叙
        operationLogDTOList = operationLogDTOList.stream().sorted(Comparator.comparing(PublicGovCarOrderOperationLogDTO::getId).reversed()).collect(Collectors.toList());
        return operationLogDTOList;

    }


    public RestResponse getDetailForApp(String orderNo) {
        //查询订单主表
        List<GovPublicCarOrder> govPublicCarOrders = getGovPublicCarOrders(orderNo);
        if(CollectionUtils.isEmpty(govPublicCarOrders)){
            return RestResponse.create(RestErrorCode.SYSTEM_EXCEPTION, "订单不存在", false, null);
        }
        GovPublicCarOrder govPublicCarOrder = govPublicCarOrders.stream().findFirst().orElse(null);

        AppPublicGovCarOrderDetailRespDTO orderDetailRespDTO = new AppPublicGovCarOrderDetailRespDTO();
        BeanUtils.copyProperties(govPublicCarOrder, orderDetailRespDTO);


        //查询用车信息
        GovPublicCarOrderVehicleInfo vehicleInfo = getVehicleInfo(orderNo);
        if (Objects.isNull(vehicleInfo)) {
            return RestResponse.create(RestErrorCode.SYSTEM_EXCEPTION, "车辆信息不存在", false, null);
        }
        BeanUtils.copyProperties(vehicleInfo, orderDetailRespDTO);

        //查询人员信息
        List<GovPublicCarOrderUserInfo> userInfoList = getUserInfo(orderNo);

        //查询地址信息
        GovPublicCarOrderAddressInfo addressInfo = getAddressInfo(orderNo);

        //填充订单基础信息
        handlerOrderInfoForApp(govPublicCarOrder, orderDetailRespDTO, addressInfo);

        //填充车辆信息
        handlerVehicleInfoForApp(vehicleInfo, orderDetailRespDTO);

        //填充用户信息
        handlerUserInfoForApp(userInfoList, orderDetailRespDTO);

        //填充说明文案
        orderDetailRespDTO.setTips(tips);

        //填充围栏信息
        CarGpsFenceDTO gpsFence = queryBySnapIds(String.valueOf(addressInfo.getInitialFenceId())).stream().findFirst().orElse(null);
        if (Objects.nonNull(gpsFence)) {
            orderDetailRespDTO.setFenceName(gpsFence.getFenceName());
        }

        //根据报警编号查询报警信息
        OfficialVehicleWarnRecordDTO officialVehicleWarnRecord = queryWarnRecord(addressInfo);
        if (Objects.isNull(officialVehicleWarnRecord)) {
            //兼容正常下单 车辆未出栏 正常展示轨迹
            OfficialVehicleWarnRecordDTO officialVehicleWarn = new OfficialVehicleWarnRecordDTO();
            officialVehicleWarn.setDeviceNo(addressInfo.getDeviceId());
            officialVehicleWarn.setVehicleVin(govPublicCarOrder.getVehicleVin());
            officialVehicleWarn.setVehicleLicense(govPublicCarOrder.getVehicleLicense());
            officialVehicleWarn.setDeviceType(addressInfo.getDeviceType());
            orderDetailRespDTO.setWarnRecordDTO(officialVehicleWarn);
        } else {
            orderDetailRespDTO.setWarnRecordDTO(officialVehicleWarnRecord);
        }

        orderDetailRespDTO.setShowSnapshotFence(IsShowEnum.NO_SHOW.getCode());
        orderDetailRespDTO.setShowRealFence(IsShowEnum.NO_SHOW.getCode());
        orderDetailRespDTO.setShowTrail(IsShowEnum.NO_SHOW.getCode());
        orderDetailRespDTO.setShowVehiclePoi(IsShowEnum.NO_SHOW.getCode());

        //已取消不展示围栏等信息
        if (!Objects.equals(govPublicCarOrder.getOrderStatus(), GovPublicCarOrderStatusEnum.CANCELED.getCode())) {
            //如果是待出发或者用车中 展示实时围栏信息
            if (Objects.equals(govPublicCarOrder.getOrderStatus(), GovPublicCarOrderStatusEnum.PENDING_DEPARTURE.getCode()) || Objects.equals(govPublicCarOrder.getOrderStatus(), GovPublicCarOrderStatusEnum.IN_USE.getCode())) {
                orderDetailRespDTO.setVehicleRealtimeStatusDTO(queryVehicleRealTimeStatus(vehicleInfo.getVehicleVin()));
                orderDetailRespDTO.setShowRealFence(IsShowEnum.SHOW.getCode());
                orderDetailRespDTO.setShowVehiclePoi(IsShowEnum.SHOW.getCode());
            } else {
                //待核实 已完成 查询围栏快照
                if (Objects.nonNull(addressInfo) ) {
                    String snapIds;
                    if(Objects.nonNull(addressInfo.getStartFenceId()) && addressInfo.getStartFenceId() > 0) {
                        snapIds = String.valueOf(addressInfo.getStartFenceId());
                        if (Objects.nonNull(addressInfo.getReturnFenceId()) && addressInfo.getReturnFenceId() > 0) {
                            snapIds = snapIds + "," + addressInfo.getReturnFenceId();
                        }
                    }else {
                        snapIds = String.valueOf(addressInfo.getInitialFenceId());
                    }
                    List<CarGpsFenceDTO> gpsFenceDTOList = queryBySnapIds(snapIds);
                    if (CollectionUtils.isNotEmpty(gpsFenceDTOList)) {
                        orderDetailRespDTO.setGpsFenceList(gpsFenceDTOList);
                        orderDetailRespDTO.setShowSnapshotFence(IsShowEnum.SHOW.getCode());
                    }
                }
                orderDetailRespDTO.setShowTrail(IsShowEnum.SHOW.getCode());
            }
        }

        //组装跳转修改行程所需参数
        orderDetailRespDTO.setModifyTravelDTO(buildModifyTravelParam(govPublicCarOrder, userInfoList, vehicleInfo, addressInfo));
        return RestResponse.success(orderDetailRespDTO);
    }

    private VehicleRealtimeStatusDTO queryVehicleRealTimeStatus(String vehicleVin) {
        if (StringUtils.isEmpty(vehicleVin)) {
            return null;
        }
        String restUrl = new IotRestLocator().getRestUrl("/fence/checkVehicleStatus");
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(1);
        paramMap.put("vehicleVin", vehicleVin);
        RestResponse response = RestClient.requestForObject(BaseHttpClient.HttpMethod.POST, restUrl, paramMap, null, VehicleRealtimeStatusDTO.class);
        if (!response.isSuccess()) {
            log.error("根据车架号查询附近围栏信息:{}", JSON.toJSONString(response));
        }
        if (Objects.isNull(response) || Objects.isNull(response.getData())) {
            return null;
        }
        return (VehicleRealtimeStatusDTO) response.getData();
    }

    private void handlerUserInfoForApp(List<GovPublicCarOrderUserInfo> userInfoList, AppPublicGovCarOrderDetailRespDTO orderDetailRespDTO) {
        //下单人
        GovPublicCarOrderUserInfo createUser = userInfoList.stream().filter(userInfo -> Objects.equals(userInfo.getUserType(), GovPublicCarOrderUserTypeEnum.ORDER_PLACER.getCode())).findFirst().orElse(null);
        if (Objects.nonNull(createUser)) {
            orderDetailRespDTO.setCreateUserInfo(createUser.getUserName() + "/" + createUser.getStructName() + "/" + createUser.getUserMobile());
        }else {
            orderDetailRespDTO.setCreateUserInfo("system");

        }

        //用车人
        List<GovPublicCarOrderUserInfo> passengerUserList = userInfoList.stream().filter(userInfo -> Objects.equals(userInfo.getUserType(), GovPublicCarOrderUserTypeEnum.PRIMARY_RIDER.getCode()) || Objects.equals(userInfo.getUserType(), GovPublicCarOrderUserTypeEnum.SECONDARY_RIDER.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(passengerUserList)) {
            List<String> passengerList = passengerUserList.stream().map(passenger ->
                    passenger.getUserName() + "/" + passenger.getStructName() + "/" + passenger.getUserMobile()
            ).collect(Collectors.toList());
            orderDetailRespDTO.setPassengerUserInfo(passengerList);
        }

        //司机
        GovPublicCarOrderUserInfo driver = userInfoList.stream().filter(userInfo -> Objects.equals(userInfo.getUserType(), GovPublicCarOrderUserTypeEnum.DRIVER.getCode())).findFirst().orElse(null);
        if (Objects.nonNull(driver)) {
            orderDetailRespDTO.setDriverInfo(driver.getUserName() + "/" + driver.getStructName() + "/" + driver.getUserMobile());
        }
    }

    private void handlerVehicleInfoForApp(GovPublicCarOrderVehicleInfo vehicleInfo, AppPublicGovCarOrderDetailRespDTO orderDetailRespDTO) {
        //填充车辆图片
        orderDetailRespDTO.setVehicleImgUrl(vehicleInfo.getVehiclePicUrl());
        orderDetailRespDTO.setVehicleModelName(vehicleInfo.getVehicleBrandName() + " " + vehicleInfo.getVehicleModelName());
    }

    private void handlerOrderInfoForApp(GovPublicCarOrder govPublicCarOrder, AppPublicGovCarOrderDetailRespDTO orderDetailRespDTO, GovPublicCarOrderAddressInfo addressInfo) {
        orderDetailRespDTO.setOrderTypeStr(GovPublicCarOrderTypeEnum.getName(govPublicCarOrder.getOrderType()));
        orderDetailRespDTO.setApprovalStatusStr(GovPublicCarApproveStatusEnum.getName(govPublicCarOrder.getApprovalStatus()));
        orderDetailRespDTO.setOrderStatusStr(GovPublicCarOrderStatusEnum.getName(govPublicCarOrder.getOrderStatus()));
        // 无任务订单状态计算
        if(Objects.equals(govPublicCarOrder.getOrderType(), GovPublicCarOrderTypeEnum.NO_TASK_USE_CAR_TYPE.getCode())
                && Objects.equals(govPublicCarOrder.getOrderStatus(), GovPublicCarOrderStatusEnum.IN_USE.getCode())
                &&  Objects.equals(govPublicCarOrder.getVerifyStatus(), GovPublicCarVerifyStatusEnum.UNVERIFIED.getCode())){
            orderDetailRespDTO.setOrderStatus(GovPublicCarOrderStatusEnum.PENDING_VERIFICATION.getCode());
            orderDetailRespDTO.setOrderStatusStr(GovPublicCarOrderStatusEnum.PENDING_VERIFICATION.getName());
        }
        //公务用车 待审批，审批撤回，审批驳回  展示审批状态
        if (Objects.equals(govPublicCarOrder.getOrderType(), GovPublicCarOrderTypeEnum.PUBLIC_USE_CAR_TYPE.getCode())) {
            orderDetailRespDTO.setOrderApprovalStatusStr(GovPublicOrderUtil.getOrderApprovalStatus(govPublicCarOrder.getOrderStatus(), govPublicCarOrder.getApprovalStatus()));
        }else {
            orderDetailRespDTO.setOrderApprovalStatusStr(orderDetailRespDTO.getOrderStatusStr());
        }
        orderDetailRespDTO.setOrderApprovalStatus(GovPublicCarApproveStatusCompleteEnum.getCode(orderDetailRespDTO.getOrderApprovalStatusStr()));

        if (Objects.nonNull(govPublicCarOrder.getOrderStartTime()) && Objects.nonNull(govPublicCarOrder.getOrderEndTime())) {
            if (govPublicCarOrder.getOrderStartTime().after(new Date(0)) && govPublicCarOrder.getOrderEndTime().after(new Date(0))) {
                List<String> userTimeList = getUserTimeList(govPublicCarOrder.getOrderStartTime(), govPublicCarOrder.getOrderEndTime());
                orderDetailRespDTO.setUserTimeList(userTimeList);
            }
        }

        if (Objects.nonNull(addressInfo)) {
            orderDetailRespDTO.setEstimatedDepartureLocation(Objects.equals(govPublicCarOrder.getOrderType(), GovPublicCarOrderTypeEnum.PUBLIC_USE_CAR_TYPE.getCode()) ? addressInfo.getEstimatedDepartureShortLocation() : addressInfo.getActualDepartureShortLocation());
            orderDetailRespDTO.setEstimatedDestinationLocation(Objects.equals(govPublicCarOrder.getOrderType(), GovPublicCarOrderTypeEnum.PUBLIC_USE_CAR_TYPE.getCode()) ? addressInfo.getEstimatedDestinationShortLocation() : addressInfo.getActualDestinationShortLocation());
        }

        SimpleDateFormat dateFormat = new SimpleDateFormat("MM月dd日 HH:mm");
        if (Objects.equals(govPublicCarOrder.getOrderType(), GovPublicCarOrderTypeEnum.PUBLIC_USE_CAR_TYPE.getCode())) {
            if (Objects.nonNull(govPublicCarOrder.getExpectedPickupTime()) && Objects.nonNull(govPublicCarOrder.getExpectedReturnTime())) {
                // 格式化日期
                orderDetailRespDTO.setExpectedPickupTime(dateFormat.format(govPublicCarOrder.getExpectedPickupTime()));
                orderDetailRespDTO.setExpectedReturnTime(dateFormat.format(govPublicCarOrder.getExpectedReturnTime()));
            }
        }

        if (Objects.equals(govPublicCarOrder.getOrderType(), GovPublicCarOrderTypeEnum.NO_TASK_USE_CAR_TYPE.getCode())) {
            if (Objects.nonNull(govPublicCarOrder.getOrderStartTime())) {
                orderDetailRespDTO.setExpectedPickupTime(dateFormat.format(govPublicCarOrder.getOrderStartTime()));
            }
            if (Objects.nonNull(govPublicCarOrder.getOrderEndTime())) {
                orderDetailRespDTO.setExpectedReturnTime(dateFormat.format(govPublicCarOrder.getOrderEndTime()));
            }
        }
    }

    // 计算单个时间对的分钟差，并返回封装后的结果列表
    public static List<String> getUserTimeList(Date startTime, Date endTime) {

        LocalDateTime startLocalDateTime = convertToLocalDateTime(startTime);
        LocalDateTime endLocalDateTime = convertToLocalDateTime(endTime);

        List<String> resultList = new ArrayList<>();

        // 如果开始时间和结束时间在同一天，直接计算分钟差
        if (startLocalDateTime.toLocalDate().equals(endLocalDateTime.toLocalDate())) {
            // 计算时间差（以秒为单位）
            long minutes = ChronoUnit.MINUTES.between(startLocalDateTime, endLocalDateTime);
            resultList.add(startLocalDateTime.format(formatter) + " " + minutes + "分钟");
        } else {
            // 第一个时间段：从开始时间到当天的午夜
            LocalDateTime midnightStart = startLocalDateTime.toLocalDate().atStartOfDay().plusDays(1);
            long minutesFirstPart = ChronoUnit.MINUTES.between(startLocalDateTime, midnightStart);
            resultList.add(startLocalDateTime.format(formatter) + " " + minutesFirstPart + "分钟");

            // 中间的每个整天时间段：从午夜到第二天午夜
            LocalDateTime currentStart = midnightStart;
            while (currentStart.isBefore(endLocalDateTime.toLocalDate().atStartOfDay())) {
                LocalDateTime nextMidnight = currentStart.plusDays(1);
                long minutesFullDay = ChronoUnit.MINUTES.between(currentStart, nextMidnight);
                resultList.add(currentStart.format(formatter) + " " + minutesFullDay + "分钟");
                currentStart = nextMidnight;
            }

            // 最后一个时间段：从最后一个午夜到结束时间
            long minutesLastPart = ChronoUnit.MINUTES.between(currentStart, endLocalDateTime);
            resultList.add(currentStart.format(formatter) + " " + minutesLastPart + "分钟");
        }
        return resultList;
    }

    // 将 Date 转换为 LocalDateTime
    private static LocalDateTime convertToLocalDateTime(Date date) {
        return LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
    }

    /**
     * 车辆进入围栏
     *
     * @param officialVehicleInFenceMsgDTO 公务车辆入栏消息
     */
    @Transactional(rollbackFor = Exception.class)
    public void inFence(OfficialVehicleInFenceMsgDTO officialVehicleInFenceMsgDTO) {
        //根据车架号获取是否存在相关公务用车订单
        String vehicleVin = officialVehicleInFenceMsgDTO.getVehicleVin();
        List<GovPublicCarOrder> orderListByVin = getOrderListByVin(vehicleVin);
        if(CollectionUtils.isEmpty(orderListByVin)){
            log.info("公务用车-对公查询不到该车架号的相关订单,车架号：{}",vehicleVin);
            return;
        }
        GovPublicCarOrder govPublicCarOrder = orderListByVin.get(0);
        //订单状态
        Byte orderStatus = govPublicCarOrder.getOrderStatus();
        //只有最近一条单子为用车中的单子才可以进行车辆进入围栏操作
        if (!orderStatus.equals(GovPublicCarOrderStatusEnum.IN_USE.getCode())){
            log.info("最近单子状态不为用车中，不用进行消费，车架号：{}",vehicleVin);
            return;
        }
        //订单类型
        Integer orderType = govPublicCarOrder.getOrderType();
        Date now = new Date();
        //无任务用车单
        if(GovPublicCarOrderTypeEnum.NO_TASK_USE_CAR_TYPE.getCode().equals(orderType)){
            //修改订单状态未待核实
            GovPublicCarOrder updateOrder = new GovPublicCarOrder();
            updateOrder.setOrderId(govPublicCarOrder.getOrderId());
            GovPublicCarOrderStatusEnum orStatusEnu = GovPublicCarOrderStatusEnum.PENDING_VERIFICATION;
            if(Objects.equals(govPublicCarOrder.getVerifyStatus(), GovPublicCarVerifyStatusEnum.VERIFIED.getCode())){
                orStatusEnu = GovPublicCarOrderStatusEnum.COMPLETED;
            }
            updateOrder.setOrderStatus(orStatusEnu.getCode());
            updateOrder.setOrderEndTime(officialVehicleInFenceMsgDTO.getInFenceTime());
            updateOrder.setReturnLotEntryTime(officialVehicleInFenceMsgDTO.getInFenceTime());
            //无任务订单入栏的时候要去计算总里程
            GovPublicCarOrderAddressInfo govPublicCarOrderAddressInfo = getOrderVehicleSimNo(govPublicCarOrder.getOrderNo(), govPublicCarOrder.getCompanyCode());
            String deviceId = "";
            Byte deviceType = null;
            if(govPublicCarOrderAddressInfo!=null){
                deviceId = govPublicCarOrderAddressInfo.getDeviceId();
                deviceType = govPublicCarOrderAddressInfo.getDeviceType();
            }else{
                deviceId = officialVehicleInFenceMsgDTO.getDeviceNo();
                deviceType = officialVehicleInFenceMsgDTO.getDeviceType();
            }
            BigDecimal mileage = deviceOperationService.computeVehicleMileage(govPublicCarOrder.getVehicleLicense(), govPublicCarOrder.getVehicleVin(), deviceId, deviceType, govPublicCarOrder.getOrderStartTime(), officialVehicleInFenceMsgDTO.getInFenceTime());
            updateOrder.setTotalMileage(mileage);
            updateOrder.setOutInFenceTotalMileage(mileage);
            updateOrder.setUpdateTime(now);
            //阈值判断，此类订单将会在入栏之后隐藏掉
            long intervalMinutes = (officialVehicleInFenceMsgDTO.getInFenceTime().getTime() - govPublicCarOrder.getOrderStartTime().getTime()) / 60 / 1000;
            if(intervalMinutes <= officialVehicleInFenceMsgDTO.getEffectiveDuration()){
                updateOrder.setIsShow((byte)0);
            }
            orderExMapper.updateByPrimaryKeySelective(updateOrder);
            //修改地址信息
            GovPublicCarOrderAddressInfo updateAddress = getGovPublicCarOrderAddressInfo(officialVehicleInFenceMsgDTO, now);
            GovPublicCarOrderAddressInfoExample addressInfoExample = new GovPublicCarOrderAddressInfoExample();
            addressInfoExample.createCriteria().andOrderNoEqualTo(govPublicCarOrder.getOrderNo());
            addressInfoMapper.updateByExampleSelective(updateAddress,addressInfoExample);
            //生成日志信息
            outOperateLog(officialVehicleInFenceMsgDTO.getInFenceTime(), govPublicCarOrder,GovPublicCarOrderOperationTypeEnum.IN_FENCE, orStatusEnu);
            //释放车态
            OrderAssetRestApi.updateVehicleWorkingStatus(govPublicCarOrder.getVehicleId().longValue(),"",VehicleEnum.WorkingStatus.NO_SERVICE.getValue(),"order");
            //无任务订单变成待核实，需要给车辆所属部门的负责人发送埋点消息
            //大于阈值的情况下才会发送消息
//            if(Objects.equals(orStatusEnu, GovPublicCarOrderStatusEnum.PENDING_VERIFICATION) && intervalMinutes > officialVehicleInFenceMsgDTO.getEffectiveDuration()){
//                sendOrderPendingVerificationMessage(govPublicCarOrder);
//            }
        }else {
            //更新订单信息
            GovPublicCarOrder updateOrder = new GovPublicCarOrder();
            //入栏的时候要去计算出入围栏总里程
            if(Objects.nonNull(govPublicCarOrder.getPickupLotExitTime()) &&
                    !LocalDateTimeUtils.isDefaultDate(govPublicCarOrder.getPickupLotExitTime())){
                GovPublicCarOrderAddressInfo govPublicCarOrderAddressInfo = getOrderVehicleSimNo(govPublicCarOrder.getOrderNo(), govPublicCarOrder.getCompanyCode());
                String deviceId = "";
                Byte deviceType = null;
                if(govPublicCarOrderAddressInfo!=null){
                    deviceId = govPublicCarOrderAddressInfo.getDeviceId();
                    deviceType = govPublicCarOrderAddressInfo.getDeviceType();
                }else{
                    deviceId = officialVehicleInFenceMsgDTO.getDeviceNo();
                    deviceType = officialVehicleInFenceMsgDTO.getDeviceType();
                }
                BigDecimal mileage = deviceOperationService.computeVehicleMileage(
                        govPublicCarOrder.getVehicleLicense(),
                        govPublicCarOrder.getVehicleVin(),
                        deviceId,
                        deviceType,
                        govPublicCarOrder.getPickupLotExitTime(),
                        officialVehicleInFenceMsgDTO.getInFenceTime());
                updateOrder.setOutInFenceTotalMileage(mileage);
            }
            updateOrder.setOrderId(govPublicCarOrder.getOrderId());
            updateOrder.setReturnLotEntryTime(officialVehicleInFenceMsgDTO.getInFenceTime());
            orderExMapper.updateByPrimaryKeySelective(updateOrder);
            //更新地址信息
            GovPublicCarOrderAddressInfo updateAddress = new GovPublicCarOrderAddressInfo();
            updateAddress.setReturnFenceId(officialVehicleInFenceMsgDTO.getFenceSnapId());
            updateAddress.setReturnLatitude(officialVehicleInFenceMsgDTO.getLatitude());
            updateAddress.setReturnLongitude(officialVehicleInFenceMsgDTO.getLongitude());
            updateAddress.setReturnLocation(officialVehicleInFenceMsgDTO.getAddress());
            GovPublicCarOrderAddressInfoExample addressExample = new GovPublicCarOrderAddressInfoExample();
            addressExample.createCriteria().andOrderNoEqualTo(govPublicCarOrder.getOrderNo());
            addressInfoMapper.updateByExampleSelective(updateAddress,addressExample);
            outOperateLog(officialVehicleInFenceMsgDTO.getInFenceTime(), govPublicCarOrder,GovPublicCarOrderOperationTypeEnum.IN_FENCE,GovPublicCarOrderStatusEnum.getByValue(govPublicCarOrder.getOrderStatus()));
        }

    }

    private GovPublicCarOrderAddressInfo getOrderVehicleSimNo(String orderNo, String companyCode){
        GovPublicCarOrderAddressInfoExample addressQryExample = new GovPublicCarOrderAddressInfoExample();
        addressQryExample.createCriteria().andOrderNoEqualTo(orderNo).andCompanyCodeEqualTo(companyCode);
        List<GovPublicCarOrderAddressInfo> addressInfoList = addressInfoMapper.selectByExample(addressQryExample);
        if(CollectionUtils.isNotEmpty(addressInfoList)){
            return addressInfoList.get(0);
        }
        return null;
    }

    private @NotNull GovPublicCarOrderAddressInfo getGovPublicCarOrderAddressInfo(OfficialVehicleInFenceMsgDTO officialVehicleInFenceMsgDTO, Date now) {
        GovPublicCarOrderAddressInfo updateAddress = new GovPublicCarOrderAddressInfo();
        updateAddress.setEstimatedDestinationLocation(officialVehicleInFenceMsgDTO.getAddress());
        updateAddress.setEstimatedDestinationLatitude(officialVehicleInFenceMsgDTO.getLatitude());
        updateAddress.setEstimatedDestinationLongitude(officialVehicleInFenceMsgDTO.getLongitude());
        updateAddress.setReturnFenceId(officialVehicleInFenceMsgDTO.getFenceSnapId());
        updateAddress.setReturnLatitude(officialVehicleInFenceMsgDTO.getLatitude());
        updateAddress.setReturnLongitude(officialVehicleInFenceMsgDTO.getLongitude());
        updateAddress.setReturnLocation(officialVehicleInFenceMsgDTO.getAddress());
        updateAddress.setUpdateTime(now);
        updateAddress.setActualDestinationShortLocation(officialVehicleInFenceMsgDTO.getAddress());
        updateAddress.setEstimatedDestinationShortLocation(officialVehicleInFenceMsgDTO.getAddress());
        updateAddress.setActualDestinationLocation(officialVehicleInFenceMsgDTO.getAddress());
        updateAddress.setActualDestinationLatitude(officialVehicleInFenceMsgDTO.getLatitude());
        updateAddress.setActualDestinationLongitude(officialVehicleInFenceMsgDTO.getLongitude());
        return updateAddress;
    }

    /**
     * 车辆出围栏
     * @param officialVehicleOutFenceMsgDTO
     */
    @Transactional(rollbackFor = Exception.class)
    public void outFence(OfficialVehicleOutFenceMsgDTO officialVehicleOutFenceMsgDTO) {
        //根据车架号获取是否存在相关公务用车订单
        String vehicleVin = officialVehicleOutFenceMsgDTO.getVehicleVin();
        List<GovPublicCarOrder> orderListByVin = getOrderListByVin(vehicleVin);
        Date now = new Date();
        //查询出相关订单出来
        if(CollectionUtils.isNotEmpty(orderListByVin)){
            //分析订单的第一条数据
            GovPublicCarOrder govPublicCarOrder = orderListByVin.get(0);
            //单子的审批状态
            Byte approvalStatus = govPublicCarOrder.getApprovalStatus();
            //单子的订单状态
            Byte orderStatus = govPublicCarOrder.getOrderStatus();
            //订单状态为待出发&审批状态为待审批的，
            // 将已有订单的订单类型变为无任务用车，订单状态修改为用车中，审批状态修改为审批撤回，并调用审批执行系统撤回操作
            if(GovPublicCarOrderStatusEnum.PENDING_DEPARTURE.getCode().equals(orderStatus)&&GovPublicCarApproveStatusEnum.PENDING.getCode().equals(approvalStatus)){
                updateCaseOne(officialVehicleOutFenceMsgDTO, now, govPublicCarOrder);

                outOperateLog(officialVehicleOutFenceMsgDTO.getOutFenceTime(), govPublicCarOrder,GovPublicCarOrderOperationTypeEnum.OUT_FENCE,GovPublicCarOrderStatusEnum.IN_USE);
                //用车中状态时&&出栏时间为空，更新下出栏时间即可（正常出栏操作）
            }else if(GovPublicCarOrderStatusEnum.IN_USE.getCode().equals(orderStatus)&&govPublicCarOrder.getPickupLotExitTime()==null){
                updateCaseTwo(officialVehicleOutFenceMsgDTO, govPublicCarOrder);
                outOperateLog(officialVehicleOutFenceMsgDTO.getOutFenceTime(), govPublicCarOrder,GovPublicCarOrderOperationTypeEnum.OUT_FENCE,GovPublicCarOrderStatusEnum.IN_USE);
                //待出发&&审批状态为审批通过，更新下出栏时间并修改订单状态为用车中
            }else if(GovPublicCarOrderStatusEnum.IN_USE.getCode().equals(orderStatus)&&govPublicCarOrder.getPickupLotExitTime()!=null){
                outOperateLog(officialVehicleOutFenceMsgDTO.getOutFenceTime(), govPublicCarOrder,GovPublicCarOrderOperationTypeEnum.OUT_FENCE,GovPublicCarOrderStatusEnum.IN_USE);
                //待出发&&审批状态为审批通过，更新下出栏时间并修改订单状态为用车中
            }else if(GovPublicCarOrderStatusEnum.PENDING_DEPARTURE.getCode().equals(orderStatus)&&GovPublicCarApproveStatusEnum.PASS.getCode().equals(approvalStatus)){
                updateCaseThree(officialVehicleOutFenceMsgDTO, now, govPublicCarOrder);
                outOperateLog(officialVehicleOutFenceMsgDTO.getOutFenceTime(), govPublicCarOrder,GovPublicCarOrderOperationTypeEnum.OUT_FENCE,GovPublicCarOrderStatusEnum.IN_USE);
                //查询出单子的状态为完成、已取消、待核实时，都需要重新生成无任务订单
            }else if(GovPublicCarOrderStatusEnum.COMPLETED.getCode().equals(orderStatus)||GovPublicCarOrderStatusEnum.CANCELED.getCode().equals(orderStatus)||GovPublicCarOrderStatusEnum.PENDING_VERIFICATION.getCode().equals(orderStatus)){
                generateNoTaskOrder(officialVehicleOutFenceMsgDTO, vehicleVin, officialVehicleOutFenceMsgDTO.getOutFenceTime());

            }
        //查询不到的情况直接生成无任务订单&&占用车态等
        }else{
            generateNoTaskOrder(officialVehicleOutFenceMsgDTO, vehicleVin, officialVehicleOutFenceMsgDTO.getOutFenceTime());
        }
    }

    private void outOperateLog(Date out, GovPublicCarOrder govPublicCarOrder,GovPublicCarOrderOperationTypeEnum operationTypeEnum,GovPublicCarOrderStatusEnum statusEnum) {
        OrderOperationLog orderOperationLogOut = new OrderOperationLog();
        orderOperationLogOut.setOrderId(Integer.valueOf(govPublicCarOrder.getOrderId()+""));
        orderOperationLogOut.setOrderNo(govPublicCarOrder.getOrderNo());
        orderOperationLogOut.setOperationType(operationTypeEnum.getCode());
        orderOperationLogOut.setOperationDescription(operationTypeEnum.getName());
        orderOperationLogOut.setOperationTime(out);
        orderOperationLogOut.setOrderStatus(statusEnum.getCode().intValue());
        orderOperationLogOut.setOrderStatusName(statusEnum.getName());
        orderOperationLogOut.setOperatorId(0L);
        orderOperationLogOut.setOperatorCode("");
        orderOperationLogOut.setOperatorName("系统");
        orderOperationLogOut.setOperatorMobile("");
        orderOperationLogMapper.insertSelective(orderOperationLogOut);
    }
    private void outOperateLogApprove(Date out, GovPublicCarOrder govPublicCarOrder, GovPublicCarOrderOperationTypeEnum operationTypeEnum, GovPublicCarOrderStatusEnum statusEnum, BpmMessageSendApproveResultDTO bpmMessageSendApproveResultDTO) {
        OrderOperationLog orderOperationLogOut = new OrderOperationLog();
        orderOperationLogOut.setOrderId(Integer.valueOf(govPublicCarOrder.getOrderId()+""));
        orderOperationLogOut.setOrderNo(govPublicCarOrder.getOrderNo());
        orderOperationLogOut.setOperationType(operationTypeEnum.getCode());
        orderOperationLogOut.setOperationDescription(operationTypeEnum.getName());
        orderOperationLogOut.setOperationTime(out);
        orderOperationLogOut.setOrderStatus(statusEnum.getCode().intValue());
        orderOperationLogOut.setOrderStatusName(statusEnum.getName());
        Integer approverId = bpmMessageSendApproveResultDTO.getApproverId();
        CustomerDTO customerById = OrderUserApi.getCustomerById(Integer.valueOf(approverId+""));
        orderOperationLogOut.setOperatorId(approverId.longValue());
        if(customerById!=null){
            orderOperationLogOut.setOperatorCode(customerById.getCustomerCode());
            orderOperationLogOut.setOperatorName(customerById.getCustomerName());
            orderOperationLogOut.setOperatorMobile(customerById.getMobile());
        }else{
            orderOperationLogOut.setOperatorId(0L);
            orderOperationLogOut.setOperatorCode("");
            orderOperationLogOut.setOperatorName("系统");
            orderOperationLogOut.setOperatorMobile("");
        }
        orderOperationLogMapper.insertSelective(orderOperationLogOut);
    }

    /**
     * 生成无任务订单
     * @param officialVehicleOutFenceMsgDTO
     * @param vehicleVin
     * @param now
     */
    private void generateNoTaskOrder(OfficialVehicleOutFenceMsgDTO officialVehicleOutFenceMsgDTO, String vehicleVin, Date now) {
        //生成订单表信息
        GovPublicCarOrder order = new GovPublicCarOrder();
        String orderNo = sequenceGenerator.generate(new Date(), OrderConst.PUBLIC_GOV_CAR_ORDER_PREFIX);
        order.setOrderNo(orderNo);
        order.setOrderType(GovPublicCarOrderTypeEnum.NO_TASK_USE_CAR_TYPE.getCode());
        order.setVerifyStatus(GovPublicCarVerifyStatusEnum.UNVERIFIED.getCode());
        CarInfoQueryConditionDTO carInfoQueryConditionDTO = new CarInfoQueryConditionDTO();
        carInfoQueryConditionDTO.setVehicleVin(vehicleVin);
        List<CarInfoBaseDTO> carInfo = OrderAssetRestApi.getCarInfo(carInfoQueryConditionDTO);
        if(CollectionUtils.isNotEmpty(carInfo)){
            CarInfoBaseDTO carInfoBaseDTO = carInfo.get(0);
            //如果车辆属于暂停运营状态，此时不需要生成无任务订单
            if(carInfoBaseDTO.getWorkingStatus().equals(VehicleEnum.WorkingStatus.THE_OPERATING.getValue())){
                log.info("车辆目前状态处于暂停运营，不需要生成无任务订单");
                return;
            }
            order.setVehicleId(Integer.valueOf(carInfoBaseDTO.getVehicleId()+""));
            order.setVehicleLicense(carInfoBaseDTO.getVehicleLicense());
            order.setVehicleVin(carInfoBaseDTO.getVehicleVin());
            order.setOrderStructId(carInfoBaseDTO.getStructId());
            List<CompanyDepartmentDTO> departmentListByIds = UserApi.getDepartmentListByIds(carInfoBaseDTO.getStructId().toString());
            if (CollectionUtils.isNotEmpty(departmentListByIds)){
                order.setOrderStructCode(departmentListByIds.get(0).getDepartmentCode());
                order.setOrderStructName(departmentListByIds.get(0).getDepartmentName());
            }
            order.setCompanyId(carInfoBaseDTO.getCompanyId());
            order.setCompanyName(carInfoBaseDTO.getCompanyName());
            //查询企业code
            CompanyDTO companyById = CompanyApi.getCompanyById(carInfoBaseDTO.getCompanyId());
            order.setCompanyCode(companyById.getCustomerCode());
            AppGetPublicGovCarVehicleReqDTO reqDTO = new AppGetPublicGovCarVehicleReqDTO();
            reqDTO.setCompanyCode(companyById.getCustomerCode());
            reqDTO.setCompanyId(order.getCompanyId());
            reqDTO.setVehicleVin(vehicleVin);
            //查询车辆模式
            String restUrl = new MrCarAssetRestLocator().getRestUrl("/publicGovCar/getPublicGovCarVehicle");
            Map<String, Object> restParam = new HashMap<>();
            restParam.put(BaseHttpClient.POSTBODY_MAP_KEY, reqDTO);
            RestResponse restResponse = RestClient.requestForObject(BaseHttpClient.HttpMethod.POSTBODY, restUrl, restParam, null, PublicGovCarInfoDTO.class);
            if(restResponse.isSuccess()){
                PublicGovCarInfoDTO publicGovCarInfoDTO = (PublicGovCarInfoDTO) restResponse.getData();
                order.setDeploymentMode(publicGovCarInfoDTO.getDeploymentMode());
            }
        }
        order.setOrderStatus(GovPublicCarOrderStatusEnum.IN_USE.getCode());
        order.setApprovalStatus(GovPublicCarApproveStatusEnum.NO_APPROVAL.getCode());
        order.setApprovalId("");
        order.setOrderStartTime(officialVehicleOutFenceMsgDTO.getOutFenceTime());
        order.setPickupLotExitTime(officialVehicleOutFenceMsgDTO.getOutFenceTime());
        order.setCreateTime(now);
        order.setUpdateTime(now);
        //插入订单详情
        orderExMapper.insertSelective(order);
        //生成地址信息
        GovPublicCarOrderAddressInfo addressInfo = new GovPublicCarOrderAddressInfo();
        addressInfo.setOrderNo(order.getOrderNo());
        addressInfo.setCompanyCode(order.getCompanyCode());
        addressInfo.setInitialFenceId(officialVehicleOutFenceMsgDTO.getFenceSnapId());
        addressInfo.setInitialLatitude(officialVehicleOutFenceMsgDTO.getLatitude());
        addressInfo.setInitialLongitude(officialVehicleOutFenceMsgDTO.getLongitude());
        addressInfo.setInitialLocation(officialVehicleOutFenceMsgDTO.getAddress());
        addressInfo.setStartFenceId(officialVehicleOutFenceMsgDTO.getFenceSnapId());
        addressInfo.setStartLatitude(officialVehicleOutFenceMsgDTO.getLatitude());
        addressInfo.setStartLongitude(officialVehicleOutFenceMsgDTO.getLongitude());
        addressInfo.setStartLocation(officialVehicleOutFenceMsgDTO.getAddress());
        addressInfo.setAlarmCode(officialVehicleOutFenceMsgDTO.getWarnSn());
        addressInfo.setCreateTime(now);
        addressInfo.setUpdateTime(now);
        addressInfo.setActualDepartureShortLocation(officialVehicleOutFenceMsgDTO.getAddress());
        addressInfo.setEstimatedDepartureShortLocation(officialVehicleOutFenceMsgDTO.getAddress());
        addressInfo.setActualDepartureLocation(officialVehicleOutFenceMsgDTO.getAddress());
        addressInfo.setActualDepartureLatitude(officialVehicleOutFenceMsgDTO.getLatitude());
        addressInfo.setActualDepartureLongitude(officialVehicleOutFenceMsgDTO.getLongitude());
        // 这里deviceId取错了，保留deviceId取deviceNo的逻辑，因为deviceNo和simNo很多请款下相同，新逻辑上线后线上积压的消息可能没有simNo
        addressInfo.setDeviceId(officialVehicleOutFenceMsgDTO.getDeviceNo());
        if(StringUtils.isNotBlank(officialVehicleOutFenceMsgDTO.getSimNo())){
            addressInfo.setDeviceId(officialVehicleOutFenceMsgDTO.getSimNo());
        }
        addressInfo.setDeviceType(officialVehicleOutFenceMsgDTO.getDeviceType());
        addressInfo.setEstimatedDepartureLocation(officialVehicleOutFenceMsgDTO.getAddress());
        addressInfo.setEstimatedDepartureLatitude(officialVehicleOutFenceMsgDTO.getLatitude());
        addressInfo.setEstimatedDepartureLongitude(officialVehicleOutFenceMsgDTO.getLongitude());
        //插入地址信息
        addressInfoMapper.insertSelective(addressInfo);
        //生成车辆信息
        GovPublicCarOrderVehicleInfo govPublicCarOrderVehicleInfo = new GovPublicCarOrderVehicleInfo();
        govPublicCarOrderVehicleInfo.setOrderNo(order.getOrderNo());
        govPublicCarOrderVehicleInfo.setCompanyCode(order.getCompanyCode());
        govPublicCarOrderVehicleInfo.setVehicleId(order.getVehicleId());
        govPublicCarOrderVehicleInfo.setVehicleLicense(order.getVehicleLicense());
        govPublicCarOrderVehicleInfo.setVehicleVin(order.getVehicleVin());
        govPublicCarOrderVehicleInfo.setDeploymentMode(order.getDeploymentMode());
        if(CollectionUtils.isNotEmpty(carInfo)){
            CarInfoBaseDTO carInfoBaseDTO = carInfo.get(0);
            govPublicCarOrderVehicleInfo.setNoKeyUseCar(carInfoBaseDTO.getNoKeyUseCar());
            govPublicCarOrderVehicleInfo.setVehicleBrandCode(carInfoBaseDTO.getVehicleBrandCode());
            govPublicCarOrderVehicleInfo.setVehicleBrandName(carInfoBaseDTO.getVehicleBrand());
            govPublicCarOrderVehicleInfo.setVehicleModelCode(carInfoBaseDTO.getVehicleModelCode());
            govPublicCarOrderVehicleInfo.setVehicleModelName(carInfoBaseDTO.getVehicleModel());
            govPublicCarOrderVehicleInfo.setVehicleStructId(carInfoBaseDTO.getStructId());
            List<CompanyDepartmentDTO> departmentListByIds = UserApi.getDepartmentListByIds(carInfoBaseDTO.getStructId().toString());
            if (CollectionUtils.isNotEmpty(departmentListByIds)){
                govPublicCarOrderVehicleInfo.setVehicleStructCode(departmentListByIds.get(0).getDepartmentCode());
                govPublicCarOrderVehicleInfo.setVehicleStructName(departmentListByIds.get(0).getDepartmentName());
            }
            govPublicCarOrderVehicleInfo.setVehicleCityCode(carInfoBaseDTO.getBelongCityCode());
            govPublicCarOrderVehicleInfo.setVehicleCityName(carInfoBaseDTO.getBelongCityName());
            if(StringUtils.isBlank(govPublicCarOrderVehicleInfo.getVehiclePicUrl())
                    && StringUtils.isNotBlank(carInfoBaseDTO.getVehiclePicUrl())){
                govPublicCarOrderVehicleInfo.setVehiclePicUrl(carInfoBaseDTO.getVehiclePicUrl());
            }
            govPublicCarOrderVehicleInfo.setOperateBussCode(carInfoBaseDTO.getOperateBussCode());
            govPublicCarOrderVehicleInfo.setBelongBussCode(carInfoBaseDTO.getBelongBussCode());
        }
//                govPublicCarOrderVehicleInfo.setVehiclePicUrl();

        govPublicCarOrderVehicleInfo.setCreateTime(now);
        govPublicCarOrderVehicleInfo.setUpdateTime(now);
        //插入车辆信息
        vehicleInfoMapper.insertSelective(govPublicCarOrderVehicleInfo);
        outOperateLog(officialVehicleOutFenceMsgDTO.getOutFenceTime(), order,GovPublicCarOrderOperationTypeEnum.CREATE,GovPublicCarOrderStatusEnum.IN_USE);
        //修改车辆状态为占用
        OrderAssetRestApi.updateVehicleWorkingStatus(order.getVehicleId().longValue(),"",VehicleEnum.WorkingStatus.IN_SERVICE.getValue(),"order");
        outOperateLog(officialVehicleOutFenceMsgDTO.getOutFenceTime(), order,GovPublicCarOrderOperationTypeEnum.OUT_FENCE,GovPublicCarOrderStatusEnum.IN_USE);
        // 发送埋点信息
        sendOrderPendingVerificationMessageProcess(order, officialVehicleOutFenceMsgDTO);
    }



    public void sendOrderPendingVerificationMessageProcess(GovPublicCarOrder govPublicCarOrder,
                                                            OfficialVehicleOutFenceMsgDTO officialVehicleOutFenceMsgDTO) {
        if(Objects.isNull(officialVehicleOutFenceMsgDTO.getEffectiveDuration()) || officialVehicleOutFenceMsgDTO.getEffectiveDuration() < 1){
            sendOrderPendingVerificationMessage(govPublicCarOrder);
            return;
        }
        GovPublicCarOrderTask orderTask = new GovPublicCarOrderTask();
        orderTask.setOrderNo(govPublicCarOrder.getOrderNo());
        orderTask.setCompanyId(govPublicCarOrder.getCompanyId());
        orderTask.setCompanyCode(govPublicCarOrder.getCompanyCode());
        orderTask.setTaskType(GovPublicCarOrderTaskTypeEnum.NO_TASK_ORDER_SEND_MSG.getCode());
        orderTask.setCreateTime(new Date());
        orderTask.setTaskStatus(GovPublicCarOrderTaskStatusEnum.UN_EXECUTED.getCode());
        orderTask.setExecutionTime(DateUtil.addDateMinutB(orderTask.getCreateTime(), officialVehicleOutFenceMsgDTO.getEffectiveDuration()));
        govPublicCarOrderTaskMapper.insertSelective(orderTask);

    }

    private void updateCaseThree(OfficialVehicleOutFenceMsgDTO officialVehicleOutFenceMsgDTO, Date now, GovPublicCarOrder govPublicCarOrder) {
        //更新订单信息
        GovPublicCarOrder updateOrder = new GovPublicCarOrder();
        updateOrder.setOrderId(govPublicCarOrder.getOrderId());
        updateOrder.setOrderStatus(GovPublicCarOrderStatusEnum.IN_USE.getCode());
        updateOrder.setOrderStartTime(officialVehicleOutFenceMsgDTO.getOutFenceTime());
        updateOrder.setPickupLotExitTime(officialVehicleOutFenceMsgDTO.getOutFenceTime());
        orderExMapper.updateByPrimaryKeySelective(updateOrder);
        //更新地址信息
        GovPublicCarOrderAddressInfo updateAddress = new GovPublicCarOrderAddressInfo();
        updateAddress.setStartFenceId(officialVehicleOutFenceMsgDTO.getFenceSnapId());
        updateAddress.setStartLatitude(officialVehicleOutFenceMsgDTO.getLatitude());
        updateAddress.setStartLongitude(officialVehicleOutFenceMsgDTO.getLongitude());
        updateAddress.setStartLocation(officialVehicleOutFenceMsgDTO.getAddress());
        updateAddress.setAlarmCode(officialVehicleOutFenceMsgDTO.getWarnSn());
        updateAddress.setActualDepartureShortLocation(officialVehicleOutFenceMsgDTO.getAddress());
        updateAddress.setActualDepartureLatitude(officialVehicleOutFenceMsgDTO.getLatitude());
        updateAddress.setActualDepartureLongitude(officialVehicleOutFenceMsgDTO.getLongitude());
        GovPublicCarOrderAddressInfoExample addressExample = new GovPublicCarOrderAddressInfoExample();
        addressExample.createCriteria().andOrderNoEqualTo(govPublicCarOrder.getOrderNo());
        addressInfoMapper.updateByExampleSelective(updateAddress,addressExample);
        outOperateLog(officialVehicleOutFenceMsgDTO.getOutFenceTime(), govPublicCarOrder,GovPublicCarOrderOperationTypeEnum.START_TRIP,GovPublicCarOrderStatusEnum.IN_USE);
    }

    private void updateCaseTwo(OfficialVehicleOutFenceMsgDTO officialVehicleOutFenceMsgDTO, GovPublicCarOrder govPublicCarOrder) {
        //更新订单信息
        GovPublicCarOrder updateOrder = new GovPublicCarOrder();
        updateOrder.setOrderId(govPublicCarOrder.getOrderId());
        updateOrder.setPickupLotExitTime(officialVehicleOutFenceMsgDTO.getOutFenceTime());
        orderExMapper.updateByPrimaryKeySelective(updateOrder);
        //更新地址信息
        GovPublicCarOrderAddressInfo updateAddress = new GovPublicCarOrderAddressInfo();
        updateAddress.setStartFenceId(officialVehicleOutFenceMsgDTO.getFenceSnapId());
        updateAddress.setStartLatitude(officialVehicleOutFenceMsgDTO.getLatitude());
        updateAddress.setStartLongitude(officialVehicleOutFenceMsgDTO.getLongitude());
        updateAddress.setStartLocation(officialVehicleOutFenceMsgDTO.getAddress());
        updateAddress.setAlarmCode(officialVehicleOutFenceMsgDTO.getWarnSn());
        GovPublicCarOrderAddressInfoExample addressExample = new GovPublicCarOrderAddressInfoExample();
        addressExample.createCriteria().andOrderNoEqualTo(govPublicCarOrder.getOrderNo());
        addressInfoMapper.updateByExampleSelective(updateAddress,addressExample);
    }

    private void updateCaseOne(OfficialVehicleOutFenceMsgDTO officialVehicleOutFenceMsgDTO, Date now, GovPublicCarOrder govPublicCarOrder) {
        //更新订单信息
        GovPublicCarOrder updateOrder = new GovPublicCarOrder();
        updateOrder.setOrderId(govPublicCarOrder.getOrderId());
        updateOrder.setOrderType(GovPublicCarOrderTypeEnum.NO_TASK_USE_CAR_TYPE.getCode());
        updateOrder.setVerifyStatus(GovPublicCarVerifyStatusEnum.UNVERIFIED.getCode());
        updateOrder.setOrderStatus(GovPublicCarOrderStatusEnum.IN_USE.getCode());
        updateOrder.setApprovalStatus(GovPublicCarApproveStatusEnum.WITHDRAW.getCode());
        updateOrder.setApprovalCompletedTime(officialVehicleOutFenceMsgDTO.getOutFenceTime());
        updateOrder.setApprovalWithdrawalType(ApprovalWithdrawalTypeEnum.SYSTEM.getType());
        updateOrder.setPickupLotExitTime(officialVehicleOutFenceMsgDTO.getOutFenceTime());
        updateOrder.setOrderStartTime(officialVehicleOutFenceMsgDTO.getOutFenceTime());
        orderExMapper.updateByPrimaryKeySelective(updateOrder);
        //更新地址信息
        GovPublicCarOrderAddressInfo updateAddress = new GovPublicCarOrderAddressInfo();
        updateAddress.setStartFenceId(officialVehicleOutFenceMsgDTO.getFenceSnapId());
        updateAddress.setStartLatitude(officialVehicleOutFenceMsgDTO.getLatitude());
        updateAddress.setStartLongitude(officialVehicleOutFenceMsgDTO.getLongitude());
        updateAddress.setStartLocation(officialVehicleOutFenceMsgDTO.getAddress());
        updateAddress.setAlarmCode(officialVehicleOutFenceMsgDTO.getWarnSn());
        updateAddress.setActualDepartureShortLocation(officialVehicleOutFenceMsgDTO.getAddress());
        updateAddress.setActualDepartureLatitude(officialVehicleOutFenceMsgDTO.getLatitude());
        updateAddress.setActualDepartureLongitude(officialVehicleOutFenceMsgDTO.getLongitude());
        GovPublicCarOrderAddressInfoExample addressExample = new GovPublicCarOrderAddressInfoExample();
        addressExample.createCriteria().andOrderNoEqualTo(govPublicCarOrder.getOrderNo());
        addressInfoMapper.updateByExampleSelective(updateAddress,addressExample);
        //插入订单操作日志
        outOperateLog(officialVehicleOutFenceMsgDTO.getOutFenceTime(),govPublicCarOrder,GovPublicCarOrderOperationTypeEnum.UPDATE_ORDER_TYPE,GovPublicCarOrderStatusEnum.IN_USE);
        //调用取消撤回审批接口
        BpmProcessInstanceCancelReqDTO reqDTO = new BpmProcessInstanceCancelReqDTO();
        reqDTO.setId(govPublicCarOrder.getApprovalId());
        reqDTO.setReason("待审批状态驶出围栏");
        reqDTO.setIsBusiness(Boolean.TRUE);
        reqDTO.setLoginUserId(0);
        reqDTO.setLoginUserName("系统");
        reqDTO.setLoginCompanyId(0);
        reqDTO.setLoginCompanyName("系统");
        log.info("公务用车待审批状态驶出围栏，撤销审批流请求参数：{}", JSON.toJSONString(reqDTO));
        RestResponse response = WorkflowClient.applyCancel(reqDTO);
        log.info("公务用车待审批状态驶出围栏，请求结果{}", JSON.toJSONString(response));
        outOperateLog(officialVehicleOutFenceMsgDTO.getOutFenceTime(),govPublicCarOrder,GovPublicCarOrderOperationTypeEnum.APPROVAL_WITHDRAW,GovPublicCarOrderStatusEnum.IN_USE);
        // 发送埋点消息
        updateOrder.setCompanyId(govPublicCarOrder.getCompanyId());
        updateOrder.setCompanyCode(govPublicCarOrder.getCompanyCode());
        updateOrder.setVehicleVin(govPublicCarOrder.getVehicleVin());
        updateOrder.setVehicleLicense(govPublicCarOrder.getVehicleLicense());
        updateOrder.setOrderNo(govPublicCarOrder.getOrderNo());
        // 发送核实埋点信息处理
        sendOrderPendingVerificationMessageProcess(updateOrder, officialVehicleOutFenceMsgDTO);
    }

    /**
     * 根据车架号获取订单数据（按更新时间倒排）
     * @param vehicleVin
     * @return
     */
    private List<GovPublicCarOrder> getOrderListByVin(String vehicleVin) {
        GovPublicCarOrderExample example = new GovPublicCarOrderExample();
        example.createCriteria().andVehicleVinEqualTo(vehicleVin);
        example.setOrderByClause("create_time desc");
        List<GovPublicCarOrder> govPublicCarOrders = orderExMapper.selectByExample(example);
        return govPublicCarOrders;
    }

    /**
     * 根据订单号查询订单审批详情接口
     * @param orderNo
     * @return
     */
    public PublicGovCarOrderDetailRespDTO getApproveDetail(String orderNo) {
        PublicGovCarOrderDetailRespDTO publicGovCarOrderDetailRespDTO = new PublicGovCarOrderDetailRespDTO();
        //获取订单详情
        List<GovPublicCarOrder> govPublicCarOrders = getGovPublicCarOrders(orderNo);
        if(CollectionUtils.isEmpty(govPublicCarOrders)){
            return publicGovCarOrderDetailRespDTO;
        }
        GovPublicCarOrder govPublicCarOrder = govPublicCarOrders.get(0);
        publicGovCarOrderDetailRespDTO.setOrderNo(govPublicCarOrder.getOrderNo());
        publicGovCarOrderDetailRespDTO.setVehicleUsageStr(VehicleEnum.VehicleUsageEnum.OFFICIAL_BUSINESS_USE.getName());
        publicGovCarOrderDetailRespDTO.setVehicleUsage(VehicleEnum.VehicleUsageEnum.OFFICIAL_BUSINESS_USE.getCode()+"");
        publicGovCarOrderDetailRespDTO.setCreateTime(govPublicCarOrder.getCreateTime());

        publicGovCarOrderDetailRespDTO.setExpectedPickupTime(govPublicCarOrder.getExpectedPickupTime());
        publicGovCarOrderDetailRespDTO.setExpectedReturnTime(govPublicCarOrder.getExpectedReturnTime());
        publicGovCarOrderDetailRespDTO.setCarUseReason(govPublicCarOrder.getCarUseReason());
        publicGovCarOrderDetailRespDTO.setOrderUserMemo(govPublicCarOrder.getOrderUserMemo());
        publicGovCarOrderDetailRespDTO.setVehicleLicense(govPublicCarOrder.getVehicleLicense());
        //查询地址信息
        GovPublicCarOrderAddressInfo addressInfo = getAddressInfo(govPublicCarOrder.getOrderNo());
        if(addressInfo!=null){
            publicGovCarOrderDetailRespDTO.setEstimatedDepartureLocation(addressInfo.getEstimatedDepartureShortLocation());
            publicGovCarOrderDetailRespDTO.setEstimatedDestinationLocation(addressInfo.getEstimatedDestinationShortLocation());
        }
        //查询车辆信息
        GovPublicCarOrderVehicleInfo vehicleInfo = getVehicleInfo(govPublicCarOrder.getOrderNo());
        if(vehicleInfo!=null){
            publicGovCarOrderDetailRespDTO.setVehicleBrandName(vehicleInfo.getVehicleBrandName());
            publicGovCarOrderDetailRespDTO.setVehicleModelName(vehicleInfo.getVehicleModelName());
        }

        //查询人员相关信息
        //获取下单人
        List<GovPublicCarOrderUserInfo> userInfoList = getUserInfo(govPublicCarOrder.getOrderNo(),GovPublicCarOrderUserTypeEnum.ORDER_PLACER);
        if(CollectionUtils.isNotEmpty(userInfoList)){
            publicGovCarOrderDetailRespDTO.setCreateUserName(userInfoList.get(0).getUserName());
            publicGovCarOrderDetailRespDTO.setCreateUserPhone(userInfoList.get(0).getUserMobile());
            publicGovCarOrderDetailRespDTO.setCreateUserStructName(userInfoList.get(0).getStructName());
        }
        //获取司机
        List<GovPublicCarOrderUserInfo> driverInfoList = getUserInfo(govPublicCarOrder.getOrderNo(),GovPublicCarOrderUserTypeEnum.DRIVER);
        if(CollectionUtils.isNotEmpty(driverInfoList)){
            publicGovCarOrderDetailRespDTO.setDriverName(driverInfoList.get(0).getUserName());
            publicGovCarOrderDetailRespDTO.setDriverMobile(driverInfoList.get(0).getUserMobile());
            publicGovCarOrderDetailRespDTO.setDriverStructName(driverInfoList.get(0).getStructName());
        }
        //获取乘车人
        List<PublicGovCarOrderDetailRespDTO.PassengersInfo> allPassengerInfoList = new ArrayList<>();
        List<GovPublicCarOrderUserInfo> passengerMainInfo = getUserInfo(govPublicCarOrder.getOrderNo(),GovPublicCarOrderUserTypeEnum.PRIMARY_RIDER);
        if(CollectionUtils.isNotEmpty(passengerMainInfo)){
            publicGovCarOrderDetailRespDTO.setUseVehicleName(passengerMainInfo.get(0).getUserName());
            publicGovCarOrderDetailRespDTO.setUseVehicleMobile(passengerMainInfo.get(0).getUserMobile());
            publicGovCarOrderDetailRespDTO.setUseVehicleStructName(passengerMainInfo.get(0).getStructName());
        }
        List<GovPublicCarOrderUserInfo> passengerSecondInfos = getUserInfo(govPublicCarOrder.getOrderNo(),GovPublicCarOrderUserTypeEnum.SECONDARY_RIDER);
        allPassengerInfoList.addAll(dealUserInfos(passengerMainInfo));
        allPassengerInfoList.addAll(dealUserInfos(passengerSecondInfos));
        publicGovCarOrderDetailRespDTO.setPassengersInfoList(allPassengerInfoList);
        return publicGovCarOrderDetailRespDTO;
    }

    /**
     * 转化实体类为乘车人
     * @param passengerMainInfo
     * @return
     */
    private List<PublicGovCarOrderDetailRespDTO.PassengersInfo> dealUserInfos(List<GovPublicCarOrderUserInfo> passengerMainInfo) {
        if(CollectionUtils.isEmpty(passengerMainInfo)){
            return new ArrayList<>();
        }
        return passengerMainInfo.stream().map(item -> {
            PublicGovCarOrderDetailRespDTO.PassengersInfo passengerInfo = new PublicGovCarOrderDetailRespDTO.PassengersInfo();
            passengerInfo.setPassengerName(item.getUserName());
            passengerInfo.setPassengerMobile(item.getUserMobile());
            passengerInfo.setPassengerStructName(item.getStructName());
            return passengerInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 根据订单号获取订单人员信息
     * @param orderNo
     * @return
     */
    private List<GovPublicCarOrderUserInfo> getUserInfo(String orderNo,GovPublicCarOrderUserTypeEnum userTypeEnum) {
        GovPublicCarOrderUserInfoExample userInfoExample = new GovPublicCarOrderUserInfoExample();
        userInfoExample.createCriteria().andOrderNoEqualTo(orderNo).andUserTypeEqualTo(userTypeEnum.getCode());
        return orderUserInfoMapper.selectByExample(userInfoExample);
    }

    private List<GovPublicCarOrderUserInfo> getUserInfo(String orderNo) {
        GovPublicCarOrderUserInfoExample userInfoExample = new GovPublicCarOrderUserInfoExample();
        userInfoExample.createCriteria().andOrderNoEqualTo(orderNo);
        return orderUserInfoMapper.selectByExample(userInfoExample);
    }

    /**
     * 根据订单号获取订单车辆信息
     * @param orderNo
     * @return
     */
    private GovPublicCarOrderVehicleInfo getVehicleInfo(String orderNo) {
        GovPublicCarOrderVehicleInfoExample vehicleInfoExample = new GovPublicCarOrderVehicleInfoExample();
        vehicleInfoExample.createCriteria().andOrderNoEqualTo(orderNo);
        List<GovPublicCarOrderVehicleInfo> govPublicCarOrderVehicleInfos = vehicleInfoMapper.selectByExample(vehicleInfoExample);
        if(CollectionUtils.isNotEmpty(govPublicCarOrderVehicleInfos)){
            return govPublicCarOrderVehicleInfos.get(0);
        }
        return null;
    }

    /**
     * 根据订单号获取订单地址信息
     * @param orderNo
     * @return
     */
    private GovPublicCarOrderAddressInfo getAddressInfo(String orderNo) {
        GovPublicCarOrderAddressInfoExample addressInfoExample = new GovPublicCarOrderAddressInfoExample();
        addressInfoExample.createCriteria().andOrderNoEqualTo(orderNo);
        List<GovPublicCarOrderAddressInfo> govPublicCarOrderAddressInfos = addressInfoMapper.selectByExample(addressInfoExample);
        if(CollectionUtils.isNotEmpty(govPublicCarOrderAddressInfos)){
            return govPublicCarOrderAddressInfos.get(0);
        }
        return null;
    }
    //设置数据权限
    private void setDataPerm(PublicGovCarOrderQueryDTO param) {
        if (Objects.equals(param.getSystemType(), SystemTypeEnum.CUSTOMER.getCode())) {
            GovPublicCarOrderUserInfoExample userExample = new GovPublicCarOrderUserInfoExample();
            userExample.createCriteria().andUserCodeEqualTo(param.getStaffCode()).andUserTypeIn(Lists.newArrayList(
                    GovPublicCarOrderUserTypeEnum.DRIVER.getCode(),
                    GovPublicCarOrderUserTypeEnum.SECONDARY_RIDER.getCode(),
                    GovPublicCarOrderUserTypeEnum.PRIMARY_RIDER.getCode(),
                    GovPublicCarOrderUserTypeEnum.ORDER_PLACER.getCode()));
            List<String> userOrderList = orderUserInfoMapper.selectOrderNoByExample(userExample);
            if (CollectionUtils.isEmpty(userOrderList)) {
                userOrderList.add("-1");
            }
            //APP我的订单 数据权限: 本人为乘车人、下单人、司机
            if (Objects.nonNull(param.getEntryType()) && Objects.equals(param.getEntryType(), 2)) {
                //根据订单号匹配，数据权限指为本人
                param.setPermOrderList(userOrderList.stream().collect(Collectors.toSet()));
                param.setDataPermType(ClientDataPermTypeEnum.ONESELF.getType());
            } else {
                //订单管理和无任务订单走一样的
                //公务车订单列表数据权限
                if (Objects.equals(param.getDataPermType(), ClientDataPermTypeEnum.ASSIGN_DEPT.getType())) {
                    //车辆部门 == 指定部门
                    List<Integer> structIds = Optional.ofNullable(param.getDataCodeSet()).orElse(Sets.newHashSet("-1"))
                            .stream().map(Integer::parseInt).collect(Collectors.toList());
                    GovPublicCarOrderVehicleInfoExample vehicleInfoExample = new GovPublicCarOrderVehicleInfoExample();
                    vehicleInfoExample.createCriteria().andVehicleStructIdIn(structIds);
                    List<String> deptVehicleOrderList = vehicleInfoMapper.selectOrderNoByExample(vehicleInfoExample);

                    //下单人 用户 司机部门 == 指定部门
                    GovPublicCarOrderUserInfoExample userDeptExample = new GovPublicCarOrderUserInfoExample();
                    userDeptExample.createCriteria().andStructIdIn(structIds).andUserTypeIn(Lists.newArrayList(
                            GovPublicCarOrderUserTypeEnum.DRIVER.getCode(),
                            GovPublicCarOrderUserTypeEnum.SECONDARY_RIDER.getCode(),
                            GovPublicCarOrderUserTypeEnum.PRIMARY_RIDER.getCode(),
                            GovPublicCarOrderUserTypeEnum.ORDER_PLACER.getCode()));
                    List<String> deptUerInfoOrderList= orderUserInfoMapper.selectOrderNoByExample(userDeptExample);

                    deptVehicleOrderList.addAll(deptUerInfoOrderList);
                    deptVehicleOrderList.addAll(userOrderList);
                    param.setPermOrderList(deptVehicleOrderList.stream().collect(Collectors.toSet()));
                } else if (Objects.equals(param.getDataPermType(), ClientDataPermTypeEnum.ASSIGN_CITY.getType())) {
                    List<String> cityCodes = Optional.ofNullable(param.getDataCodeSet()).orElse(Sets.newHashSet("-1"))
                            .stream().collect(Collectors.toList());
                    GovPublicCarOrderVehicleInfoExample vehicleInfoExample = new GovPublicCarOrderVehicleInfoExample();
                    vehicleInfoExample.createCriteria().andVehicleCityCodeIn(cityCodes);
                    List<String> cityOrderList = vehicleInfoMapper.selectOrderNoByExample(vehicleInfoExample);
                    cityOrderList.addAll(userOrderList);
                    param.setPermOrderList(cityOrderList.stream().collect(Collectors.toSet()));
                } else if (Objects.equals(param.getDataPermType(), ClientDataPermTypeEnum.ONESELF.getType())) {
                    param.setPermOrderList(userOrderList.stream().collect(Collectors.toSet()));
                } else if (Objects.nonNull(param.getEntryType()) && Objects.equals(param.getDataPermType(), ClientDataPermTypeEnum.SELF_COMPANY.getType())) {
                    //APP 无任务订单和订单管理 本企业不支持子企业，只保留本企业
                    param.setDataCodeSet(Sets.newHashSet(String.valueOf(param.getCompanyId())));
                }
            }
        } else if (Objects.equals(param.getSystemType(), SystemTypeEnum.PROVIDER.getCode())) {
            if (Objects.equals(param.getDataPermType(), ProviderDataPermTypeEnum.SELF_DEPT.getType()) ||
                    Objects.equals(param.getDataPermType(), ProviderDataPermTypeEnum.ASSIGN_DEPT.getType())) {
                List<String> assetOrderList = Lists.newArrayList();
                if (CollectionUtils.isNotEmpty(param.getDataCodeSet())) {
                    GovPublicCarOrderVehicleInfoExample vehicleInfoExample = new GovPublicCarOrderVehicleInfoExample();
                    vehicleInfoExample.createCriteria().andOperateBussCodeIn(Lists.newArrayList(param.getDataCodeSet()));
                    final GovPublicCarOrderVehicleInfoExample.Criteria criteria1 = vehicleInfoExample.createCriteria()
                            .andBelongBussCodeIn(Lists.newArrayList(param.getDataCodeSet()));
                    vehicleInfoExample.or(criteria1);
                    assetOrderList = vehicleInfoMapper.selectOrderNoByExample(vehicleInfoExample);
                }
                if (CollectionUtils.isEmpty(assetOrderList)) {
                    assetOrderList = Lists.newArrayList("-1");
                }
                param.setPermOrderList(Sets.newHashSet(assetOrderList));
            }
        }
    }

    /**
     * 导出派车单
     */
    public ExportPDFDTO exportDeliveryPdf(String orderNo) {
        byte[] pdfByteArr = getDeliveryPdfPdfByteArr(orderNo);
        return doRenderDeliveryPdf(pdfByteArr);
    }

    private byte[] getDeliveryPdfPdfByteArr(String orderNo) {
        // 查询订单主表
        GovPublicCarOrderExample orderExample = new GovPublicCarOrderExample();
        orderExample.createCriteria().andOrderNoEqualTo(orderNo);
        GovPublicCarOrder govPublicCarOrder = orderExMapper.selectByExample(orderExample).stream().findFirst().orElse(null);
        if (Objects.isNull(govPublicCarOrder)) {
            throw ExceptionFactory.createRestException(MrCarOrderErrorCode.QUERY_ORDER_NOT_EXISTS);
        }

        // 查询用车信息
        GovPublicCarOrderVehicleInfoExample vehicleInfoExample = new GovPublicCarOrderVehicleInfoExample();
        vehicleInfoExample.createCriteria().andOrderNoEqualTo(orderNo);
        GovPublicCarOrderVehicleInfo vehicleInfo = vehicleInfoMapper.selectByExample(vehicleInfoExample).stream().findFirst().orElse(null);
        if (Objects.isNull(vehicleInfo)) {
            throw ExceptionFactory.createRestException((MrCarOrderErrorCode.VEHICLE_NOT_EXIST_ERROR));
        }

        // 查询人员信息
        GovPublicCarOrderUserInfoExample userInfoExample = new GovPublicCarOrderUserInfoExample();
        userInfoExample.createCriteria().andOrderNoEqualTo(orderNo);
        List<GovPublicCarOrderUserInfo> userInfoList = orderUserInfoMapper.selectByExample(userInfoExample);

        // 查询地址信息
        GovPublicCarOrderAddressInfoExample addressInfoExample = new GovPublicCarOrderAddressInfoExample();
        addressInfoExample.createCriteria().andOrderNoEqualTo(orderNo);
        GovPublicCarOrderAddressInfo addressInfo = addressInfoMapper.selectByExample(addressInfoExample).stream().findFirst().orElse(null);

        String templatePDF= CompanyTemplateEnum.LI_JIANG_TEMPLATE.getTemplateName();
        List<Map<String, Object>> dataMapList = getPDFData(govPublicCarOrder,vehicleInfo,userInfoList,addressInfo,templatePDF);
        return PdfUtils.createMorePageByTemplate(dataMapList, templatePDF, true);
    }


    private ExportPDFDTO doRenderDeliveryPdf(byte[] pdfByteArr) {
        String filePath = OSSUtils.uploadFileV2("派车单", pdfByteArr,"pdf");
        if(StringUtils.isBlank(filePath)){
            throw ExceptionFactory.createRestException(MrCarOrderErrorCode.CAR_LOCK_RETRYABLE_FAIL,"文件导出失败");
        }
        String[] fileSplits=filePath.split("\\/");
        String fileName=fileSplits[fileSplits.length-1];
        ExportPDFDTO fileDto = new ExportPDFDTO();
        fileDto.setMimeType("application/pdf");
        fileDto.setFileUrl(filePath);
        fileDto.setFileName(fileName);
        fileDto.setFileSize((long) (pdfByteArr.length));
        return fileDto;
    }

    private List<Map<String, Object>> getPDFData(GovPublicCarOrder govPublicCarOrder,
                                                 GovPublicCarOrderVehicleInfo vehicleInfo,
                                                 List<GovPublicCarOrderUserInfo> userInfoList,
                                                 GovPublicCarOrderAddressInfo addressInfo,String templatePDF) {
        List<Map<String, Object>> result=new ArrayList<>();

        Map<String, Object> dataMap = new HashMap<>();
        //丽江
        int idx = 2;
        //用车单位
        dataMap.put("fill_" + (++idx), govPublicCarOrder.getOrderStructName());
        String orderCreaterName = userInfoList.stream().filter(e -> Objects.equals(e.getUserType(), GovPublicCarOrderUserTypeEnum.ORDER_PLACER.getCode()))
                .map(e -> e.getUserName()+"("+e.getUserMobile()+" "+e.getStructName()+ ")").collect(Collectors.joining("\n"));
        //下单人
        dataMap.put("fill_" + (++idx), orderCreaterName);
        //乘车人 5
        String passengers = "";
            passengers = userInfoList.stream()
                    .filter(e -> Objects.equals(e.getUserType(), GovPublicCarOrderUserTypeEnum.PRIMARY_RIDER.getCode())||Objects.equals(e.getUserType(), GovPublicCarOrderUserTypeEnum.SECONDARY_RIDER.getCode()))
                    .map(e -> e.getUserName() +" "+ e.getUserMobile() + "(" +e.getStructName()+ ")")
                    .collect(Collectors.joining("\n"));

        dataMap.put("fill_" + (++idx), passengers);
        //用车类型 6
        dataMap.put("fill_" + (++idx),GovPublicCarOrderTypeEnum.getName(govPublicCarOrder.getOrderType()));
        //用车时间 7
        dataMap.put("fill_" + (++idx), DateUtils.format(govPublicCarOrder.getExpectedPickupTime(),"yyyy-MM-dd HH:mm") + "-" + DateUtils.format(govPublicCarOrder.getExpectedReturnTime(),"yyyy-MM-dd HH:mm"));
        //用车人数 8
        long passengerNum = userInfoList.stream()
                .filter(e -> Objects.equals(e.getUserType(), GovPublicCarOrderUserTypeEnum.PRIMARY_RIDER.getCode())||Objects.equals(e.getUserType(), GovPublicCarOrderUserTypeEnum.SECONDARY_RIDER.getCode())).count();
        dataMap.put("fill_" + (++idx), passengerNum);
        //航班/列车号 9
        dataMap.put("fill_" + (++idx), "");
        //出发地 10
        dataMap.put("fill_" + (++idx), addressInfo.getEstimatedDepartureShortLocation());
        // 目的地 11

        String address = addressInfo.getEstimatedDestinationShortLocation();
        dataMap.put("fill_" + (++idx), address);
        // 用车事由
        String carUseReason = govPublicCarOrder.getCarUseReason();
        if(StringUtils.isBlank(carUseReason)){
            carUseReason = "";
        }
        dataMap.put("fill_" + (++idx), carUseReason);
        //用车备注 12
        dataMap.put("fill_" + (++idx), govPublicCarOrder.getOrderUserMemo());
        // 司机 13
        String driverName=userInfoList.stream().filter(e -> Objects.equals(e.getUserType(), GovPublicCarOrderUserTypeEnum.DRIVER.getCode())).map(e -> e.getUserName()+"("+e.getUserMobile()+")").collect(Collectors.joining(","));
        dataMap.put("fill_" + (++idx), driverName);
        // 车型 14
        dataMap.put("fill_" + (++idx), vehicleInfo.getVehicleModelName());
        // 车牌号 15
        dataMap.put("fill_" + (++idx), vehicleInfo.getVehicleLicense());
        // 开始里程 16
//        BigDecimal start= new BigDecimal("111");
//        if(start != null && BigDecimal.ZERO.compareTo(start) != 0){
//            dataMap.put("fill_16", start);
//        }

        // 结束里程 17
//        BigDecimal end=new BigDecimal("111");
//        if(end != null && BigDecimal.ZERO.compareTo(end) != 0){
//            dataMap.put("fill_17",end );
//        }
        // 开始里程 16
        dataMap.put("fill_" + (++idx), "");
        // 结束里程 17
        dataMap.put("fill_" + (++idx), "");
        // 行驶里程 18
        BigDecimal tripMileage = govPublicCarOrder.getTotalMileage();
        String fillKey = "fill_" + (++idx);
        dataMap.put(fillKey, "");
        if( tripMileage != null && BigDecimal.ZERO.compareTo(tripMileage) != 0){
            dataMap.put(fillKey, govPublicCarOrder.getTotalMileage());
        }
        // 实际出发时间 19
        dataMap.put("fill_" + (++idx), DateUtils.format(govPublicCarOrder.getOrderStartTime(),"yyyy-MM-dd HH:mm"));
        // 实际结束时间 20
        dataMap.put("fill_" + (++idx), DateUtils.format(govPublicCarOrder.getOrderEndTime(),"yyyy-MM-dd HH:mm"));
        // 总时长 21
        fillKey = "fill_" + (++idx);
        dataMap.put(fillKey, "");
        if (Objects.nonNull(govPublicCarOrder.getOrderEndTime()) && Objects.nonNull(govPublicCarOrder.getOrderStartTime())) {
            dataMap.put(fillKey , DateUtil.getHourMinSec(govPublicCarOrder.getOrderEndTime(), govPublicCarOrder.getOrderStartTime()));
        }

        OrderOperationLogExample operationLogExample = new OrderOperationLogExample();
        operationLogExample.createCriteria().andOrderNoEqualTo(govPublicCarOrder.getOrderNo()).andOperationTypeEqualTo(GovPublicCarOrderOperationTypeEnum.APPROVAL_PASS.getCode());
        List<OrderOperationLog> orderOperationLogs = orderOperationLogMapper.selectByExample(operationLogExample);
        // 用车审批人 22
        fillKey = "fill_" + (++idx);
        dataMap.put(fillKey, "");
        if(CollectionUtils.isNotEmpty(orderOperationLogs)&&orderOperationLogs.get(0)!=null){
            if(StringUtils.isNotBlank(orderOperationLogs.get(0).getOperatorMobile())){
                dataMap.put(fillKey, orderOperationLogs.get(0).getOperatorName()+ "(" + orderOperationLogs.get(0).getOperatorMobile() + ")");
            }else{
                dataMap.put(fillKey, orderOperationLogs.get(0).getOperatorName());
            }
        }

        //平台车才发送下单调度消息
        fillKey = "fill_" + (++idx);
        dataMap.put(fillKey, "");
        if (Objects.equals(vehicleInfo.getDeploymentMode(), DeploymentModeEnum.PLATFORM.getValue())) {
            //获取调度员手机号
            List<TimeRentDispatcherDTO> dispatchers = UserRpc.getDispatchers(vehicleInfo.getVehicleStructId(),
                    govPublicCarOrder.getCompanyId());
            // 调度人 23
            if(CollectionUtils.isNotEmpty(dispatchers)){
                String dispatcherName = dispatchers.stream().map(e -> e.getDispatcherName() + "(" + e.getDispatcherMobile() + ")").collect(Collectors.joining(","));
                dataMap.put(fillKey, dispatcherName);
            }
        }
        // 路桥费 24
        // 加油费 25
        // 充电费 26
        // 停车费 27
        // 行驶费 28
        // 劳务费 29
        // 加班费 30
        // 洗车费 31
        // 餐饮费 32
        // 伙食补助费 33
        // 住宿费 34
        // 差旅费 35
        // 其他费 36
        // 其他费说明 37
        // 费用标准 38
        // 总费用 39
        // 调度员 40
        // 审核员 41
        // 驾驶员签字 42
        // 客户签字 43
        result.add(dataMap);

        return result;
    }




    /**
     * 获取车辆所在部门下拥有调度员角色的人员手机号
     * 平台车的车辆所在部门就是部门的根节点
     *
     * @param vehicleStructId 车辆所属部门ID
     * @param orderCompanyId  订单所属公司ID
     * @return 调度人员手机号列表
     */
    private List<String> getDispatchers(Integer vehicleStructId, Integer orderCompanyId) {
        String restUrl = new UserRestLocator().getRestUrl(UserUrlCenter.PUBLIC_GOV_CAR_GET_DISPATCH_PHONE);
        HashMap<String, Object> param = new HashMap<>();
        param.put("structId", vehicleStructId);
        param.put("companyId", orderCompanyId);
        @SuppressWarnings("unchecked")
        RestResponse<List<TimeRentDispatcherDTO>> restResponse = RestClient.requestForList(BaseHttpClient.HttpMethod.GET,
                restUrl, param, null, TimeRentDispatcherDTO.class);
        if (!restResponse.isSuccess()) {
            log.error("获取车辆所在部门下拥有调度员角色的人员手机号失败，返回信息：{}", restResponse.getMsg());
            return Collections.emptyList();
        }
        List<String> dispatcherMobiles = restResponse.getData().stream().map(TimeRentDispatcherDTO::getDispatcherMobile).collect(Collectors.toList());
        return dispatcherMobiles;
    }

    private void sendOrderCreateMessage(GovPublicCarOrder govPublicCarOrder,
                                        GovPublicCarOrderVehicleInfo govPublicCarOrderVehicleInfo
            , List<GovPublicCarOrderUserInfo> userInfos) {
        //平台车才发送下单调度消息
        if (!Objects.equals(govPublicCarOrderVehicleInfo.getDeploymentMode(), DeploymentModeEnum.PLATFORM.getValue())) {
            log.warn("不是平台车，不发送下单调度消息，车牌号：{},订单号:{}"
                    , govPublicCarOrderVehicleInfo.getVehicleLicense(), govPublicCarOrderVehicleInfo.getOrderNo());
            return;
        }
        //获取调度员手机号
        List<String> dispatchers = getDispatchers(govPublicCarOrderVehicleInfo.getVehicleStructId(),
                govPublicCarOrder.getCompanyId());
        if (CollectionUtils.isEmpty(dispatchers)) {
            log.warn("订单号:{} 车牌号:{} 车辆所属部门:{} 没有调度员信息，无需发送公务车调度埋点消息",
                    govPublicCarOrder.getOrderNo(),
                    govPublicCarOrderVehicleInfo.getVehicleLicense(), govPublicCarOrderVehicleInfo.getVehicleStructName());
            return;
        }
        //车牌号
        String licenseNumber = govPublicCarOrderVehicleInfo.getVehicleLicense();
        //下单人信息
        Optional<GovPublicCarOrderUserInfo> carOrderUserInfoOpt = userInfos.stream()
                .filter(e -> Objects.equals(e.getUserType(), GovPublicCarOrderUserTypeEnum.ORDER_PLACER.getCode()))
                .findAny();
        if (!carOrderUserInfoOpt.isPresent()) {
            log.warn("下单人信息不存在,不发送下单调度消息,车牌号：{}", licenseNumber);
            return;
        }
        //获取司机信息
        Optional<GovPublicCarOrderUserInfo> driverInfoOpt = userInfos.stream()
                .filter(e -> Objects.equals(e.getUserType(), GovPublicCarOrderUserTypeEnum.DRIVER.getCode()))
                .findAny();
        if (!driverInfoOpt.isPresent()) {
            log.warn("司机信息不存在,不发送下单调度消息,车牌号：{}", licenseNumber);
            return;
        }

        GovPublicCarOrderUserInfo carOrderUserInfo = carOrderUserInfoOpt.get();
        //创建人部门
        String creatorDepartment = carOrderUserInfo.getStructName();
        //创建人
        String creatorName = carOrderUserInfo.getUserName();
        //订单所属的公司ID
        Integer companyId = govPublicCarOrder.getCompanyId();
        //开始结束时间
        String startEndTime = formatSendDate(govPublicCarOrder);
        //订单号
        String orderNo = govPublicCarOrder.getOrderNo();

        Map<String, Object> extraMsg = new HashMap<>();
        extraMsg.put("orderNo", orderNo);
        String orderApprovalStatus = GovPublicOrderUtil
                .getOrderApprovalStatus(govPublicCarOrder.getOrderStatus(), govPublicCarOrder.getApprovalStatus());
        extraMsg.put("orderApprovalStatusStr", orderApprovalStatus);
        Map<String, String> paramMap = new HashMap<>();// 消息体参数
        PushMessageInputDTO pushMessageInputDTO = new PushMessageInputDTO();
        paramMap.put(PushMessageUtil.MSG_PARAM_VEHICLE_LICENSE, licenseNumber);

        paramMap.put(PushMessageUtil.MSG_PARAM_DRIVER_NAME, driverInfoOpt.get().getUserName());


        paramMap.put(PushMessageUtil.MSG_PARAM_APPLY_CREATOR_DEPARTMENT, creatorDepartment);
        paramMap.put(PushMessageUtil.MSG_PARAM_CREATOR_NAME, creatorName);
        paramMap.put(PushMessageUtil.MSG_PARAM_START_END_TIME, startEndTime);
        

        pushMessageInputDTO.setParamMap(paramMap);
        pushMessageInputDTO.setExtraMsg(extraMsg);
        pushMessageInputDTO.setScenePhoneSet(new HashSet<>(dispatchers));
        pushMessageInputDTO.setCompanyId(companyId);
        pushMessageInputDTO.setSceneCodeEnum(MsgModelEnum.SceneCodeEnum.TIME_RENT_PLACE_ORDER_NOTIFY_DISPACHER);
        MessageSendUtils.pushMessage(pushMessageInputDTO);
    }

    private String formatSendDate(GovPublicCarOrder govPublicCarOrder) {
         return  DateUtil.format(govPublicCarOrder.getExpectedPickupTime(), DateUtil.TIME_FORMAT_2)
                + "至" + DateUtil.format(govPublicCarOrder.getExpectedReturnTime(), DateUtil.TIME_FORMAT_2);
    }

    //获取部门负责人的手机号列表
    private List<String> getLeaderMobile(Integer vehicleStructId) {
        try {
            List<CustomerSimpleDTO> deptLeaderByDeptId = CompanyDeptApi.getDeptLeaderByDeptId(vehicleStructId);
            return deptLeaderByDeptId.stream().map(CustomerSimpleDTO::getMobile).collect(Collectors.toList());
        } catch (RestErrorException e) {
            log.error("获取部门负责人的手机号列表失败，车辆所属部门ID：{} e:", vehicleStructId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 发送订单待核实的消息
     * @param govPublicCarOrder 订单信息
     */
    public void sendOrderPendingVerificationMessage(GovPublicCarOrder govPublicCarOrder) {
        String startTime = DateUtil.format(govPublicCarOrder.getOrderStartTime(), DateUtil.TIME_FORMAT_2);
        String licenseNumber = govPublicCarOrder.getVehicleLicense();
        Integer companyId = govPublicCarOrder.getCompanyId();
        String orderNo = govPublicCarOrder.getOrderNo();
        //获取车辆所属部门的部门负责人
        GovPublicCarOrderVehicleInfoExample vehicleInfoExample = new GovPublicCarOrderVehicleInfoExample();
        vehicleInfoExample.createCriteria().andOrderNoEqualTo(govPublicCarOrder.getOrderNo());
        List<GovPublicCarOrderVehicleInfo> vehicleInfoList = vehicleInfoMapper
                .selectByExample(vehicleInfoExample);
        if (CollectionUtils.isEmpty(vehicleInfoList)) {
            log.warn("订单号:{} 车牌号:{} 没有车辆信息，无需发送订单核实消息", orderNo, licenseNumber);
            return;
        }
        GovPublicCarOrderVehicleInfo govPublicCarOrderVehicleInfo = vehicleInfoList.get(0);
        Integer vehicleStructId = govPublicCarOrderVehicleInfo.getVehicleStructId();
        List<String> leaderMobile = this.getLeaderMobile(vehicleStructId);
        if (leaderMobile == null || leaderMobile.isEmpty()) {
            log.warn("订单号:{} 车牌号:{} 部门负责人手机号为空，无需发送订单核实消息", orderNo, licenseNumber);
            return;
        }
        Set<String> scenePhoneSet = new HashSet<>(leaderMobile);

        Map<String, Object> extraMsg = new HashMap<>();
        extraMsg.put("orderNo", orderNo);
        String orderApprovalStatus = GovPublicOrderUtil
                .getOrderApprovalStatus(govPublicCarOrder.getOrderStatus(), govPublicCarOrder.getApprovalStatus());
        extraMsg.put("orderApprovalStatusStr", orderApprovalStatus);

        PushMessageInputDTO pushMessageInputDTO = new PushMessageInputDTO();
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put(PushMessageUtil.MSG_PARAM_VEHICLE_LICENSE, licenseNumber);
        paramMap.put(PushMessageUtil.MSG_PARAM_START_TIME, startTime);

        pushMessageInputDTO.setParamMap(paramMap);
        pushMessageInputDTO.setExtraMsg(extraMsg);
        pushMessageInputDTO.setScenePhoneSet(scenePhoneSet);
        pushMessageInputDTO.setCompanyId(companyId);
        pushMessageInputDTO.setSceneCodeEnum(MsgModelEnum.SceneCodeEnum.TIME_RENT_NOTASK_ORDER_CREATE_SUCCESS);
        MessageSendUtils.pushMessage(pushMessageInputDTO);
    }

    //发送车辆取消或者修改的消息
    public void sendVehicleCancelOrModifyMessage(String orderNo,MsgModelEnum.SceneCodeEnum sceneCodeEnum) {
        log.info("sendOrderCancellationMessage");

        GovPublicCarOrderExample orderExample = new GovPublicCarOrderExample();
        orderExample.createCriteria().andOrderNoEqualTo(orderNo);
        List<GovPublicCarOrder> govPublicCarOrders = orderExMapper.selectByExample(orderExample);

        if (CollectionUtils.isEmpty(govPublicCarOrders)) {
            log.error("govPublicCarOrders is empty");
            return;
        }

        GovPublicCarOrder govPublicCarOrder = govPublicCarOrders.get(0);
        String startEndTime = formatSendDate(govPublicCarOrder);
        String licenseNumber = govPublicCarOrder.getVehicleLicense();
        Integer companyId = govPublicCarOrder.getCompanyId();

        GovPublicCarOrderUserInfoExample userInfoExample = new GovPublicCarOrderUserInfoExample();
        userInfoExample.createCriteria().andOrderNoEqualTo(orderNo);
        List<GovPublicCarOrderUserInfo> govPublicCarOrderUserInfos = orderUserInfoMapper.selectByExample(userInfoExample);

        if (CollectionUtils.isEmpty(govPublicCarOrderUserInfos)) {
            log.error("govPublicCarOrderUserInfos is empty");
            return;
        }

        Optional<GovPublicCarOrderUserInfo> orderPlacerInfo = govPublicCarOrderUserInfos.stream()
                .filter(userInfo -> Objects.equals(userInfo.getUserType(), GovPublicCarOrderUserTypeEnum.ORDER_PLACER.getCode()))
                .findFirst();

        if (!orderPlacerInfo.isPresent()) {
            log.error("orderPlacerInfo is empty");
            return;
        }

        String orderCreatePhone = orderPlacerInfo.get().getUserMobile();
        String driverName = govPublicCarOrderUserInfos.stream()
                .filter(userInfo -> Objects.equals(userInfo.getUserType(), GovPublicCarOrderUserTypeEnum.DRIVER.getCode()))
                .map(GovPublicCarOrderUserInfo::getUserName)
                .findFirst()
                .orElse("");

        Set<String> scenePhoneSet = new HashSet<>();
        scenePhoneSet.add(orderCreatePhone);

        Map<String, Object> extraMsg = new HashMap<>();
        extraMsg.put("orderNo", orderNo);
        String orderApprovalStatus = GovPublicOrderUtil
                .getOrderApprovalStatus(govPublicCarOrder.getOrderStatus(), govPublicCarOrder.getApprovalStatus());
        extraMsg.put("orderApprovalStatusStr", orderApprovalStatus);

        PushMessageInputDTO pushMessageInputDTO = new PushMessageInputDTO();
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put(PushMessageUtil.MSG_PARAM_VEHICLE_LICENSE, licenseNumber);
        paramMap.put(PushMessageUtil.MSG_PARAM_DRIVER_NAME, driverName);
        paramMap.put(PushMessageUtil.MSG_PARAM_START_END_TIME, startEndTime);

        pushMessageInputDTO.setParamMap(paramMap);
        pushMessageInputDTO.setExtraMsg(extraMsg);
        pushMessageInputDTO.setScenePhoneSet(scenePhoneSet);
        pushMessageInputDTO.setCompanyId(companyId);
        pushMessageInputDTO.setSceneCodeEnum(sceneCodeEnum);
        MessageSendUtils.pushMessage(pushMessageInputDTO);
    }


    /**
     * 发送订单取消埋点消息
     * @param orderNo 订单号
     */
    public void sendOrderCancellationMessage(String orderNo) {
        this.sendVehicleCancelOrModifyMessage(orderNo, MsgModelEnum.SceneCodeEnum.TIME_RENT_ORDER_CANCEL_SUCCESS);
    }

    /**
     * 发送订单修改埋点消息
     */
    public void sendOrderModificationMessage(String orderNo) {
        this.sendVehicleCancelOrModifyMessage(orderNo, MsgModelEnum.SceneCodeEnum.TIME_RENT_ORDER_MODIFY_SUCCESS);
    }

    public Boolean exportDispatchOrderToMail(ExportDispatchOrderToMailReqDTO reqDTO) {
        List<String> emails = reqDTO.getEmails();
        emails = emails.stream().distinct().collect(Collectors.toList());
        if(emails.size() > MAX_EMAIL_LIST_SIZE){
            throw ExceptionFactory.createRestException(ErrorCodeConstants.MAIL_LIST_EXCEED_LIMIT.getCode(), MAX_EMAIL_LIST_SIZE);
        }
        File file = null;
        try {
            byte[] pdfByteArr = getDeliveryPdfPdfByteArr(reqDTO.getOrderNo());
            file = new File(PDF_FILE_PATH);
            if (!file.exists()) {
                file.mkdir();
            }
            String fileName = "派车单" + DateUtil.format(new Date(), "yyyyMMddHHmmss");
            String path = PDF_FILE_PATH + fileName + ".pdf";
            file = new File(path);
            FileOutputStream fos = new FileOutputStream(file);
            fos.write(pdfByteArr);
            fos.close();
            VelocityContext context = new VelocityContext();
            final String templateName = "mail_templates/exportPubGovCarDispatchOrder.vm";
            final String htmlBody = VelocityUtil.merge(templateName, context);
            //1.2 创建消息体
            String trackId = "" + System.currentTimeMillis();
            String subject = "【" + fileName + "】"; //标题
            List<File> attachmentFiles = Lists.newArrayList(file);//邮件附件
            final MailMessage mailMessage =
                    new MailMessage(trackId, subject, htmlBody, reqDTO.getEmails(),null,null,null, attachmentFiles);
            return MailUtils.send(mailMessage);
        } catch (Exception e) {
            log.error("exportDispatchOrderToMail exception", e);
            return false;
        }finally {
            if(file != null){
                file.delete();
            }
        }
    }

    public List<ExportDTO> exportDispatchOrder(ExportDispatchOrderReqDTO reqDTO) {
        ExportDTO exportDTO = new ExportDTO();
        try {
            File pdfOutput = Files.createTempFile(null, ".pdf").toFile();
            File imageOutPut = Files.createTempFile(null, ".png").toFile();
            try {
                byte[] pdfByteArr = getDeliveryPdfPdfByteArr(reqDTO.getOrderNo());
                FileOutputStream fos = new FileOutputStream(pdfOutput);
                fos.write(pdfByteArr);
                fos.close();
                String fileName = "派车单" + DateUtil.format(new Date(), "yyyyMMddHHmmss") + ".png";
                PdfUtil.pdf2Image(pdfOutput, imageOutPut.getAbsolutePath(), 128);
                String fileUrl = BpmFormExportUtil.uploadToOss(imageOutPut, OSSBucketEnum.FILE, "mrcarOrder", fileName);
                exportDTO.setFileName(fileName);
                exportDTO.setFileUrl(fileUrl);
            } catch (IOException e) {
                log.error("orderNo:{} 导出派车单异常", reqDTO.getOrderNo(), e);
            } finally {
                pdfOutput.delete();
                imageOutPut.delete();
            }
        } catch (IOException e) {
            log.error("orderNo:{} 导出派车单异常", reqDTO.getOrderNo(), e);
        }
        return Lists.newArrayList(exportDTO);
    }
}
