package com.izu.order.service.provider;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.izu.mrcar.order.consts.BusinessPushStatusEnum;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.mrcar.order.consts.BusinessAdditionOrderStatusEnum;
import com.izu.mrcar.order.consts.ExpenseStatusEnum;
import com.izu.mrcar.order.consts.OrderChannelSourceEnum;
import com.izu.mrcar.order.consts.OrderEnum;
import com.izu.mrcar.order.consts.OrderEnum.OrderType;
import com.izu.mrcar.order.dto.mrcar.OrderApplyDestinationDTO;
import com.izu.mrcar.order.dto.provider.input.OrderApplyOpeInputDTO;
import com.izu.mrcar.order.dto.provider.output.OrderApplyOpeOutputDTO;
import com.izu.mrcar.order.errcode.MrCarOrderErrorCode;
import com.izu.order.entity.mrcar.*;
import com.izu.order.rpc.UserRpc;
import com.izu.order.util.DateUtil;
import com.izu.user.enums.perm.ProviderDataPermTypeEnum;
import mapper.mrcar.BusinessOrderAdditionRecordMapper;
import mapper.mrcar.BusinessPushOperationLogMapper;
import mapper.mrcar.ex.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class OrderApplyProviderService {
    @NacosValue(value = "${v2.order.startDate}", autoRefreshed = true)
    private String v2DateStr;
    @Autowired
    private OrderApplyExMapper orderApplyExMapper;
    @Autowired
    private OrderInfoExMapper orderInfoExMapper;
    @Autowired
    private OrderBusinessExtendExMapper orderBusinessExtendExMapper;
    @Autowired
    private BillExMapper billExMapper;
    @Autowired
    private OrderApplyDestinationExMapper orderApplyDestinationExMapper;
    @Autowired
    private BillOrderExMapper billOrderExMapper;
    @Autowired
    private BusinessOrderAdditionRecordMapper orderAdditionRecordMapper;

    @Autowired
    private BusinessPushOperationLogMapper businessPushOperationLogMapper;

    /**
     * 分页查询行程列表（运营端）
     *
     * @param queryDTO
     * @return
     */
    public PageDTO getList(OrderApplyOpeInputDTO queryDTO) {
        //运营端 所属部门和执行部门权限需要特殊处理，需要将部门ID转换成其下维护的所有公司ID
        if (ProviderDataPermTypeEnum.ASSIGN_DEPT.getType().equals(queryDTO.getDataPermType())
                || ProviderDataPermTypeEnum.SELF_DEPT.getType().equals(queryDTO.getDataPermType())
        ) {
            //查询部门下的公司ID
            if (CollectionUtils.isNotEmpty(queryDTO.getDataPermCodeSet())) {
                final List<Integer> companyIds = UserRpc.getCompanyIdUnderDept(new ArrayList<>(queryDTO.getDataPermCodeSet()));
                if (companyIds.isEmpty()) {
                    queryDTO.setDataPermCodeSet(new HashSet<String>() {
                        {
                            this.add("-1");
                        }
                    });
                } else {
                    queryDTO.setDataPermCodeSet(new HashSet<>(companyIds.stream().map(String::valueOf).collect(Collectors.toSet())));
                }
            }
        }
        final PageInfo<OrderApplyOpeOutputDTO> pageInfo = PageHelper.startPage(queryDTO.getPage(), queryDTO.getPageSize())
                .doSelectPageInfo(() -> orderApplyExMapper.selectOrderApplyProviderPageList(queryDTO));
        List<OrderApplyOpeOutputDTO> orderApplyList = pageInfo.getList();
        if (orderApplyList.isEmpty()) {
            return new PageDTO(pageInfo.getPageNum(), pageInfo.getPageSize(), pageInfo.getTotal(), pageInfo.getList());
        }
        //获取主行程id集合
        List<String> orderApplyNos = orderApplyList.stream().map(order -> order.getOrderApplyNo()).collect(Collectors.toList());
        //查询子行程集合
        List<OrderInfo> orderInfoList = orderInfoExMapper.selectByOrderApplyNos(orderApplyNos);
        //key->主行程id value->子行程id
        Map<String, String> orderInfoMap = orderInfoList.stream()
                .collect(Collectors.toMap(info -> info.getOrderApplyNo(), info -> info.getOrderNo(), (a, b) -> b));

        orderApplyList.stream().forEach(orderApply -> {
            if (queryDTO.getLoginId() != null && queryDTO.getLoginId().equals(orderApply.getCustomerId())) {
                orderApply.setSelfOrder(1);
            }
            //商务用车区分版本
            final boolean versionFlag = OrderType.MOTORCADE_ORDER.value().equals(orderApply.getOrderType()) && StringUtils.isNotBlank(v2DateStr) && orderApply.getCreateTime().compareTo(DateUtil.parseDate(v2DateStr)) >= 0;
            orderApply.setVersion(versionFlag ? 2 : 1);
            //审批迁移之后，审批按钮在行程列表不展示
            orderApply.setIsShowApproveButton(false);
            //设置枚举值
            orderApply.setOrderTypeName(OrderType.getByValue(orderApply.getOrderType().byteValue()).text());
            if (!Objects.isNull(orderApply.getOrderStatus()) && OrderEnum.OrderApplyStatus.getByValue(orderApply.getOrderStatus().shortValue()) != null) {
                orderApply.setOrderStatusName(OrderEnum.OrderApplyStatus.getByValue(orderApply.getOrderStatus().shortValue()).text());
            } else {
                orderApply.setOrderStatusName(StringUtils.EMPTY);
            }
            orderApply.setServiceName(OrderEnum.OrderServiceType.getByValue(orderApply.getServiceCode()).text());
            //设置子行程id
            if (Objects.equals(OrderType.PRIVATE_ORDER.value(), orderApply.getOrderType())) {
                orderApply.setOrderNo(orderInfoMap.get(orderApply.getOrderApplyNo()));
            }

            if (Objects.equals(OrderType.MOTORCADE_ORDER.value(), orderApply.getOrderType())) {
                //如果是商务车,只需要校验订单状态
                boolean cancelStatus = OrderEnum.OrderApplyStatus.DISPATCHING.value() == orderApply.getOrderStatus()
                        || OrderEnum.OrderApplyStatus.DISPATCHED.value() == orderApply.getOrderStatus()
                        || OrderEnum.OrderApplyStatus.WAIT_ACCOUNT.value() == orderApply.getOrderStatus();
                orderApply.setCancelButtonDisplay(cancelStatus);
            } else {
                boolean cancelStatus = OrderEnum.OrderApplyStatus.DISPATCHING.value() == orderApply.getOrderStatus()
                        || OrderEnum.OrderApplyStatus.DISPATCHED.value() == orderApply.getOrderStatus();
                boolean cancelIdentity = orderApply.getCustomerId().intValue() == queryDTO.getLoginId();
                if (OrderType.PRIVATE_ORDER.value().equals(orderApply.getOrderType())) {
                    cancelIdentity = cancelIdentity || orderApply.getBookingPassengerUserId().intValue() == queryDTO.getLoginId();
                }
                orderApply.setCancelButtonDisplay(cancelStatus && cancelIdentity);
            }

        });
        return new PageDTO(pageInfo.getPageNum(), pageInfo.getPageSize(), pageInfo.getTotal(), pageInfo.getList());
    }


    @Transactional(rollbackFor = Exception.class)
    public RestResponse rollback(String orderApplyNo) {
        OrderApply orderApply = orderApplyExMapper.selectOneByOrderApplyNo(orderApplyNo);
        if (Objects.isNull(orderApply)) {
            return RestResponse.fail(MrCarOrderErrorCode.ORDER_COMMON_ERROR, "退回失败,数据异常,请数据调整申请");
        }

        //查询子行程集合
        List<OrderInfo> orderInfos = orderInfoExMapper.selectByApplyNo(orderApplyNo);
        if (CollectionUtils.isEmpty(orderInfos)) {
            return RestResponse.fail(MrCarOrderErrorCode.ORDER_COMMON_ERROR, "退回失败,数据异常,查询不到子行程");
        }

        //orderInfo对应一个extend
        List<OrderBusinessExtend> orderBusinessExtends = orderBusinessExtendExMapper.selectByOrderApplyNo(orderApplyNo);
        if (CollectionUtils.isEmpty(orderBusinessExtends)) {
            return RestResponse.fail(MrCarOrderErrorCode.ORDER_COMMON_ERROR, "退回失败,商务用车子订单数据异常");
        }

        boolean anyMatch = orderBusinessExtends
                .stream()
                .anyMatch(extend -> extend.getExpenseStatus().equals(ExpenseStatusEnum.EXPENSEING.getType())
                        || extend.getExpenseStatus().equals(ExpenseStatusEnum.EXPENSE_FINISH.getType())
                        || extend.getExpenseStatus().equals(ExpenseStatusEnum.EXPENDITURE_FINISH.getType()));

        if (anyMatch) {
            return RestResponse.fail(MrCarOrderErrorCode.ORDER_COMMON_ERROR, "退回失败,存在支出");
        }

        if (Objects.equals(orderApply.getOrderChannelSource(), OrderChannelSourceEnum.PC_ORDER_FILL.getDesc())) {
            //修改补录单状态
            BusinessOrderAdditionRecordExample orderAdditionRecordExample = new BusinessOrderAdditionRecordExample();
            orderAdditionRecordExample.createCriteria().andOrderCodeEqualTo(orderApplyNo);
            List<BusinessOrderAdditionRecord> businessOrderAdditionRecords = orderAdditionRecordMapper.selectByExample(orderAdditionRecordExample);
            if (CollectionUtils.isEmpty(businessOrderAdditionRecords)) {
                return RestResponse.fail(MrCarOrderErrorCode.ORDER_COMMON_ERROR, "退回失败,数据异常,补录单不存在");
            }
            orderAdditionRecordExample.createCriteria().andRecordIdIn(businessOrderAdditionRecords.stream().map(BusinessOrderAdditionRecord::getRecordId).collect(Collectors.toList()));
            BusinessOrderAdditionRecord orderAdditionRecord = new BusinessOrderAdditionRecord();
            orderAdditionRecord.setOrderStatus(BusinessAdditionOrderStatusEnum.SAVED.getStatus());
            orderAdditionRecordMapper.updateByExampleSelective(orderAdditionRecord, orderAdditionRecordExample);
            //删除主行程数据
            orderApplyExMapper.deleteByPrimaryKey(orderApply.getId());
            //删除子行程数据
            OrderInfoExample orderInfoExample = new OrderInfoExample();
            orderInfoExample.createCriteria().andIdIn(orderInfos.stream().map(OrderInfo::getId).collect(Collectors.toList()));
            orderInfoExMapper.deleteByExample(orderInfoExample);
            //删除拓展表
            OrderBusinessExtendExample orderBusinessExtendExample = new OrderBusinessExtendExample();
            orderBusinessExtendExample.createCriteria().andIdIn(orderBusinessExtends.stream().map(OrderBusinessExtend::getId).collect(Collectors.toList()));
            orderBusinessExtendExMapper.deleteByExample(orderBusinessExtendExample);
            //删除账单
            Bill bill = billExMapper.selectByOrderApplyNo(orderApplyNo);
            if (Objects.nonNull(bill)) {
                billExMapper.deleteByPrimaryKey(bill.getId());
            }
            //删除主行程订单目的地表
            List<OrderApplyDestination> applyDestinations = orderApplyDestinationExMapper.getDestinationListByApplyNo(orderApplyNo);
            if (CollectionUtils.isNotEmpty(applyDestinations)) {
                OrderApplyDestinationExample destinationExample = new OrderApplyDestinationExample();
                destinationExample.createCriteria().andIdIn(applyDestinations.stream().map(OrderApplyDestination::getId).collect(Collectors.toList()));
                orderApplyDestinationExMapper.deleteByExample(destinationExample);
            }
            //删除子行程订单目的地表
            List<OrderApplyDestination> orderDestinationList = orderApplyDestinationExMapper.getListByOrderNo(orderApplyNo);
            if (CollectionUtils.isNotEmpty(orderDestinationList)) {
                OrderApplyDestinationExample destinationExample = new OrderApplyDestinationExample();
                List<Integer> collect = orderDestinationList.stream().map(OrderApplyDestination::getId).collect(Collectors.toList());
                destinationExample.createCriteria().andIdIn(collect);
                orderApplyDestinationExMapper.deleteByExample(destinationExample);
            }

            //删除bill_order表数据
            List<BillOrder> billOrders = billOrderExMapper.selectByOrderApplyNo(orderApplyNo);
            if (CollectionUtils.isNotEmpty(billOrders)) {
                List<Long> collect = billOrders.stream().map(BillOrder::getId).collect(Collectors.toList());
                BillOrderExample billOrderExample = new BillOrderExample();
                billOrderExample.createCriteria().andIdIn(collect);
                billOrderExMapper.deleteByExample(billOrderExample);
            }

        } else {
            //更新主行程状态为:待推送账单
            OrderApply order = new OrderApply();
            order.setOrderStatus(OrderEnum.OrderApplyStatus.WAIT_ACCOUNT.value());
            order.setId(orderApply.getId());
            orderApplyExMapper.updateByPrimaryKeySelective(order);

            //子行程支出状态 由待支出 改为 待行程结束
            List<Long> waitExpenseIdList = orderBusinessExtends.stream().filter(extend -> extend.getExpenseStatus().equals(ExpenseStatusEnum.WAIT_EXPENSE.getType())).map(OrderBusinessExtend::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(waitExpenseIdList)) {
                OrderBusinessExtendExample orderBusinessExtendExample = new OrderBusinessExtendExample();
                orderBusinessExtendExample.createCriteria().andIdIn(waitExpenseIdList);
                OrderBusinessExtend orderBusinessExtend = new OrderBusinessExtend();
                orderBusinessExtend.setExpenseStatus(ExpenseStatusEnum.TRIP_NOT_FINISH.getType());
                orderBusinessExtendExMapper.updateByExampleSelective(orderBusinessExtend, orderBusinessExtendExample);
            }
        }

        //将商务车推送综合日志状态置为已退回
        BusinessPushOperationLogExample businessPushOperationLogExample = new BusinessPushOperationLogExample();
        businessPushOperationLogExample.createCriteria().andOrderApplyNoEqualTo(orderApplyNo);
        List<BusinessPushOperationLog> businessPushOperationLogList = businessPushOperationLogMapper.selectByExample(businessPushOperationLogExample);
        if(CollectionUtils.isNotEmpty(businessPushOperationLogList)){
            BusinessPushOperationLog businessPushOperationLog = new BusinessPushOperationLog();
            businessPushOperationLog.setPushStatus(BusinessPushStatusEnum.RETURNED.getCode());
            businessPushOperationLog.setId(businessPushOperationLogList.get(0).getId());
            businessPushOperationLogMapper.updateByPrimaryKeySelective(businessPushOperationLog);
        }
        return RestResponse.success(true);
    }

}
