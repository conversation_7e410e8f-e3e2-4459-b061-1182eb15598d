package com.izu.order.controller.govcar;

import cn.hutool.core.collection.CollectionUtil;
import com.izu.framework.locker.DistributionRedisLock;
import com.izu.framework.web.rest.response.PageDTO;
import com.izu.framework.web.rest.response.RestResponse;
import com.izu.framework.web.util.BeanUtil;
import com.izu.mrcar.order.consts.govCar.*;
import com.izu.mrcar.order.dto.govcar.GovCarCheckRecordDTO;
import com.izu.mrcar.order.dto.govcar.order.*;
import com.izu.mrcar.order.errcode.MrCarOrderErrorCode;
import com.izu.order.entity.mrcar.*;
import com.izu.order.service.govCar.*;
import com.izu.order.util.DateUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.izu.mrcar.order.MrcarOrderRestMsgCenter.*;

/**
 * <AUTHOR>
 * @date 2024/8/7 16:42
 */
@Slf4j
@Api(tags = "公务员用车-订单")
@RestController
public class GovCarOrderController {

    @Resource
    private GovCarOrderService govCarOrderService;

    @Resource
    private GovCarOrderOperationService orderOperationService;

    @Resource
    private GovCarOrderExtendService orderExtendService;

    @Resource
    private GovCarGainBackService govCarGainBackService;

    @Resource
    private GovCarOrderContractService govCarOrderContractService;

    @Resource
    private GovCarCheckRecordService govCarCheckRecordService;

    @Resource
    private GovCarOrderCancellationService govCarOrderCancellationService;

    private final DecimalFormat decimalFormat = new DecimalFormat("#,##0.00");

    public static final String FORMAT_STR = "(%s * %s)";

    public static final String LOCK_PRE = "GOV_CAR_ORDER_LOCK_%s";

    @PostMapping(GOV_CAR_ORDER_PAGE)
    @ApiOperation(value = "分页查询订单信息")
    public RestResponse<PageDTO<GovCarOrderListInfoDTO>> queryOrderList(@RequestBody GovCarOrderQueryParams params) {
        if (params.getPageNum() == null) {
            params.setPageNum(1);
        }
        if (params.getPageSize() == null) {
            params.setPageSize(10);
        }
        if (StringUtils.isNotBlank(params.getCreateTimeStart())) {
            params.setCreateTimeStart(params.getCreateTimeStart() + " 00:00:00");
        }
        if (StringUtils.isNotBlank(params.getCreateTimeEnd())) {
            params.setCreateTimeEnd(params.getCreateTimeEnd() + " 23:59:59");
        }
        PageDTO<GovCarOrderListInfoDTO> result = govCarOrderService.queryOrderList(params);
        return RestResponse.success(result);
    }

    @PostMapping(GOV_CAR_ORDER_CANCEL)
    @ApiOperation(value = "取消订单")
    public RestResponse<Void> cancelOrder(@RequestBody GovCarOrderCancelParams params) {
        String orderNo = params.getOrderNo();
        if (StringUtils.isBlank(orderNo)) {
            return RestResponse.fail(MrCarOrderErrorCode.ORDER_COMMON_ERROR, "订单编号不能为空");
        }
        DistributionRedisLock lock = DistributionRedisLock.newLock(String.format(LOCK_PRE, orderNo));
        try {
            if (!lock.lock(5)) {
                return RestResponse.fail(MrCarOrderErrorCode.ORDER_COMMON_ERROR, "取消失败,请稍后重试");
            }
            boolean result = orderOperationService.cancelOrder(params);
            if (!result) {
                return RestResponse.fail(MrCarOrderErrorCode.ORDER_COMMON_ERROR, "订单取消失败,请稍后再试");
            }
            return RestResponse.success(null);
        } finally {
            lock.unLock();
        }
    }

    @PostMapping(GOV_CAR_ORDER_CANCEL_PRE_CHECK)
    @ApiOperation(value = "订单车辆是否驶出围栏")
    public RestResponse<Boolean> cancelPre(@RequestBody GovCarOrderDetailParam params) {
        String orderNo = params.getOrderNo();
        if (StringUtils.isBlank(orderNo)) {
            return RestResponse.fail(MrCarOrderErrorCode.ORDER_COMMON_ERROR, "订单编号不能为空");
        }
        // 查询验车单信息
        return orderOperationService.cancelPre(params);
    }

    @PostMapping(GOV_CAR_ORDER_FORCE_RETURN)
    @ApiOperation(value = "强制还车")
    public RestResponse<Void> forceReturn(@RequestBody GovCarOrderForceReturnParams params) {
        String orderNo = params.getOrderNo();
        if (StringUtils.isBlank(orderNo)) {
            return RestResponse.fail(MrCarOrderErrorCode.ORDER_COMMON_ERROR, "订单编号不能为空");
        }
        DistributionRedisLock lock = DistributionRedisLock.newLock(String.format(LOCK_PRE, orderNo));
        try {
            if (!lock.lock(5)) {
                return RestResponse.fail(MrCarOrderErrorCode.ORDER_COMMON_ERROR, "强制还车失败,请稍后重试");
            }
            GovCarOrder orderInfo = govCarOrderService.queryOrder(orderNo);
            if (orderInfo == null) {
                return RestResponse.fail(MrCarOrderErrorCode.ORDER_COMMON_ERROR, "订单信息不存在");
            }
            if (!GovCarOrderEnums.OrderStatus.AWAITING_RETURN.getStatus().equals(orderInfo.getOrderStatus())) {
                return RestResponse.fail(MrCarOrderErrorCode.ORDER_COMMON_ERROR, "当前订单状态不支持还车操作");
            }
            govCarGainBackService.forceBack(params);
            return RestResponse.success(null);
        } finally {
            lock.unLock();
        }
    }

    @PostMapping(GOV_CAR_ORDER_ADJUSTMENT)
    @ApiOperation(value = "订单调价")
    public RestResponse<Void> adjustment(@RequestBody GovCarOrderAdjustmentParams params) {
        String orderNo = params.getOrderNo();
        if (StringUtils.isBlank(orderNo)) {
            return RestResponse.fail(MrCarOrderErrorCode.ORDER_COMMON_ERROR, "订单编号不能为空");
        }
        if (CollectionUtil.isEmpty(params.getAdjustItems())) {
            return RestResponse.fail(MrCarOrderErrorCode.ORDER_COMMON_ERROR, "调价明细不能为空");
        }
        DistributionRedisLock lock = DistributionRedisLock.newLock(String.format(LOCK_PRE, orderNo));
        try {
            if (!lock.lock(5)) {
                return RestResponse.fail(MrCarOrderErrorCode.ORDER_COMMON_ERROR, "订单调价失败,请稍后重试");
            }
            return orderOperationService.adjustment(params);
        } finally {
            lock.unLock();
        }
    }

    @PostMapping(GOV_CAR_ORDER_ADJUSTMENT_CONFIG)
    @ApiOperation(value = "订单调价项")
    public RestResponse<List<GovCarOrderAdjustmentItem>> adjustmentConfig(@RequestBody GovCarOrderAdjustmentParams params) {
        if (StringUtils.isBlank(params.getOrderNo())) {
            return RestResponse.fail(MrCarOrderErrorCode.ORDER_COMMON_ERROR, "订单编号不能为空");
        }
        return orderOperationService.adjustmentConfig(params);
    }

    @PostMapping(GOV_CAR_ORDER_DETAIL)
    @ApiOperation(value = "订单详情")
    public RestResponse<GovCarOrderDetailInfoDTO> detail(@RequestBody GovCarOrderDetailParam params) {
        String orderNo = params.getOrderNo();
        if (StringUtils.isBlank(orderNo)) {
            return RestResponse.fail(MrCarOrderErrorCode.ORDER_COMMON_ERROR, "订单编号不能为空");
        }
        GovCarOrder govCarOrder = govCarOrderService.queryOrder(orderNo);
        if (govCarOrder == null) {
            return RestResponse.fail(MrCarOrderErrorCode.ORDER_COMMON_ERROR, "订单详情不存在");
        }
        GovCarOrderDetailInfoDTO detail = BeanUtil.copyObject(govCarOrder, GovCarOrderDetailInfoDTO.class);
        if (GovCarOrderEnums.OrderStatus.CANCELLED.getStatus().equals(govCarOrder.getOrderStatus())) {
            // 查询取消记录,设置取消原因
            GovCarOrderCancellation govCarOrderCancellation = govCarOrderCancellationService.queryByOrderNoAndType(orderNo, GovCarOrderBillEnums.BillType.RENT_BILL.getType());
            String cancellationReason = Optional.ofNullable(govCarOrderCancellation).map(GovCarOrderCancellation::getCancellationReason).orElse(StringUtils.EMPTY);
            Date cancellationTime = Optional.ofNullable(govCarOrderCancellation).map(GovCarOrderCancellation::getCancellationTime).orElse(null);
            detail.setOrderCancelTime(DateUtil.formatDateTime(cancellationTime));
            detail.setCancellationReason(cancellationReason);
        }
        Date expectedPickupTime = govCarOrder.getExpectedPickupTime();
        Date expectedReturnTime = govCarOrder.getExpectedReturnTime();
        // 计算预计用车天数
        String expectedUseDays = DateUtil.calculateDaysDifference(expectedPickupTime, expectedReturnTime) + "";
        detail.setExpectedUseDays(expectedUseDays);
        // 计算实际用车天数
        Integer rentalHours = govCarOrder.getRentalHours();
        String actualUseDays = StringUtils.EMPTY;
        if (rentalHours > 0) {
            int total = (rentalHours / 24) + (rentalHours % 24 > 0 ? 1 : 0);
            actualUseDays = actualUseDays + total;
        }
        detail.setShowAdjustButton(GovCarOrderEnums.OrderStatus.RETURN_PENDING_SETTLEMENT.getStatus().equals(govCarOrder.getOrderStatus()) && GovCarOrderEnums.OrderSettleStatus.WAIT_SETTLE.getStatus().equals(govCarOrder.getOrderSettleStatus()));
        detail.setActualUseDays(actualUseDays);

        // 查询取还车记录
        GovCarGainBackRecord gainBackRecord = govCarGainBackService.queryByOrder(orderNo);
        GovCarOrderCheckRecord checkRecord = new GovCarOrderCheckRecord();
        checkRecord.setCheckRecordNo(StringUtils.EMPTY);
        checkRecord.setContractUrl(StringUtils.EMPTY);
        if (gainBackRecord != null){
            detail.setCheckStartTime(DateUtil.formatDateTime(gainBackRecord.getGainCheckStartTime()));
        }else {
            detail.setCheckStartTime(StringUtils.EMPTY);
        }
        if (gainBackRecord != null && gainBackRecord.getGainBackStatus() >= GovCarGainBackRecordStatusEnum.GAINED.getCode()) {
            checkRecord.setCheckRecordNo(gainBackRecord.getGainBackNo());
            GovCarOrderContract orderContract = govCarOrderContractService.queryByOrder(orderNo, GovCarOrderContractEnums.TypeEnum.GAIN_CAR.getCode());
            checkRecord.setContractUrl(orderContract.getContractUrl());
            if (gainBackRecord.getBackForce()){
                // 如果强制还车, 查询还车验车单
                GovCarCheckRecordDTO backRecord = govCarCheckRecordService.queryCheckRecord(gainBackRecord.getGainBackNo(), GovCarCheckRecordTypeEnum.BACK_CAR.getCode());
                if (backRecord != null){
                    detail.setForceReturnName(backRecord.getCheckUserName());
                    detail.setForceReturnReason(backRecord.getMemo());
                    detail.setForceReturnTime(DateUtil.formatDateTime(backRecord.getCheckEndTime()));
                }
            }
        }

        detail.setPickupCheck(checkRecord);
        // 部门需要设置成车辆的部门
        detail.setStructName(govCarOrder.getVehicleStructName());
        detail.setCreateTime(DateUtil.formatDateTime(govCarOrder.getCreateTime()));
        detail.setActualPickupTime(DateUtil.formatDateTime(govCarOrder.getActualPickupTime()));
        detail.setActualReturnTime(DateUtil.formatDateTime(govCarOrder.getActualReturnTime()));
        detail.setExpectedPickupTime(DateUtil.formatDateTime(govCarOrder.getExpectedPickupTime()));
        detail.setExpectedReturnTime(DateUtil.formatDateTime(govCarOrder.getExpectedReturnTime()));
        detail.setOrderStatusStr(GovCarOrderEnums.OrderStatus.getName(govCarOrder.getOrderStatus()));
        detail.setOrderSettleStatusStr(GovCarOrderEnums.OrderSettleStatus.getName(govCarOrder.getOrderSettleStatus()));
        detail.setPickupLotExitTime(DateUtil.formatDateTime(govCarOrder.getPickupLotExitTime()));
        detail.setReturnLotEntryTime(DateUtil.formatDateTime(govCarOrder.getReturnLotEntryTime()));
        detail.setOrderSettleTime(DateUtil.formatDateTime(govCarOrder.getOrderSettleTime()));
        detail.setOrderPaymentTime(DateUtil.formatDateTime(govCarOrder.getOrderPaymentTime()));
        // 查询账单生成 账单明细与支付记录
        List<GovCarOrderBill> govCarOrderBills = orderExtendService.queryBillByOrderNo(orderNo, GovCarOrderBillEnums.BillType.RENT_BILL.getType());
        // 已支付记录
        BigDecimal reductionAmount = BigDecimal.ZERO;
        Set<String> reductionAmountNotes = new HashSet<>();
        BigDecimal otherAmount = BigDecimal.ZERO;
        Set<String> otherAmountNotes = new HashSet<>();
        if (CollectionUtil.isNotEmpty(govCarOrderBills)) {
            // 计算已支付金额
            List<GovCarOrderPayRecordDTO> payRecords = new ArrayList<>();
            BigDecimal pay = BigDecimal.ZERO;
            BigDecimal pendingAmount = BigDecimal.ZERO;
            BigDecimal billTotal = BigDecimal.ZERO;
            BigDecimal refundTotal = BigDecimal.ZERO;
            for (GovCarOrderBill bill : govCarOrderBills) {
                // 计算已支付金额 生成支付记录
                if (GovCarOrderBillEnums.BillStatus.PAID.getStatus().equals(bill.getBillStatus())
                        || GovCarOrderBillEnums.BillStatus.REFUNDING.getStatus().equals(bill.getBillStatus())
                        || GovCarOrderBillEnums.BillStatus.REFUNDED.getStatus().equals(bill.getBillStatus())) {
                    GovCarOrderPayRecordDTO recordDTO = new GovCarOrderPayRecordDTO();
                    recordDTO.setPayAmount(decimalFormat.format(bill.getPayedAmount()));
                    BigDecimal actualAmount = bill.getPayedAmount().add(bill.getRefundAmount());
                    recordDTO.setActualAmount(decimalFormat.format(actualAmount));
                    recordDTO.setPayNo(bill.getPayTransNo());
                    recordDTO.setPayTime(DateUtil.formatDateTime(bill.getPayedTime()));
                    recordDTO.setPayWay(Optional.ofNullable(GovCarOrderBillEnums.PayedType.getName(bill.getPayedType())).orElse(StringUtils.EMPTY));
                    payRecords.add(recordDTO);
                    pay = pay.add(bill.getPayedAmount());
                    refundTotal = refundTotal.add(bill.getRefundAmount());
                }
                // 计算待支付金额
                if (GovCarOrderBillEnums.BillStatus.PENDING.getStatus().equals(bill.getBillStatus())) {
                    pendingAmount = pendingAmount.add(bill.getBillAmount());
                }
                // 计算账单总额
                billTotal = billTotal.add(bill.getBillAmount());
            }

            detail.setPayAmount(decimalFormat.format(pay));
            detail.setPayRecordList(payRecords);
            List<String> billNos = govCarOrderBills.stream().map(GovCarOrderBill::getBillNo).collect(Collectors.toList());
            List<GovCarOrderBillDetail> govCarOrderBillDetails = orderExtendService.queryBillDetailByBillNos(billNos);
            // 所有子项加起来是应收金额
            BigDecimal itemTotal = BigDecimal.ZERO;
            for (GovCarOrderBillDetail item : govCarOrderBillDetails) {
                itemTotal = itemTotal.add(item.getTotalAmount());
                if (GovCarOrderBillEnums.DetailType.ADDITIONAL_FEE.getType().equals(item.getDetailType())) {
                    otherAmount = otherAmount.add(item.getTotalAmount());
                    if (StringUtils.isNotBlank(item.getFeeNotes())) {
                        otherAmountNotes.add(item.getFeeNotes());
                    }
                }
                if (GovCarOrderBillEnums.DetailType.DISCOUNT_FEE.getType().equals(item.getDetailType())) {
                    reductionAmount = reductionAmount.add(item.getTotalAmount().abs());
                    if (StringUtils.isNotBlank(item.getFeeNotes())) {
                        reductionAmountNotes.add(item.getFeeNotes());
                    }

                }
            }
            if (GovCarOrderEnums.OrderStatus.RETURN_PENDING_SETTLEMENT.getStatus().equals(govCarOrder.getOrderStatus())) {
                detail.setPendingAmount(decimalFormat.format(pendingAmount));
                detail.setTotalAmount(decimalFormat.format(itemTotal));
                BigDecimal refundAmount = billTotal.subtract(itemTotal);
                detail.setRefundAmount(decimalFormat.format(refundAmount));
            }else if(GovCarOrderEnums.OrderStatus.COMPLETED.getStatus().equals(govCarOrder.getOrderStatus())){
                detail.setTotalAmount(decimalFormat.format(itemTotal));
                detail.setPendingAmount(StringUtils.EMPTY);
                detail.setRefundAmount(decimalFormat.format(refundTotal.abs()));
            } else {
                // 产品规定未到待结算不展示
                detail.setPendingAmount(StringUtils.EMPTY);
                detail.setTotalAmount(StringUtils.EMPTY);
                detail.setRefundAmount(StringUtils.EMPTY);
            }
            detail.setOtherAmount(decimalFormat.format(otherAmount));
            detail.setReductionAmount(decimalFormat.format(reductionAmount));
            detail.setOtherAmountNotes(String.join(",", otherAmountNotes));
            detail.setReductionAmountNotes(String.join(",", reductionAmountNotes));
            // 按照费用项分组
            List<GovCarOrderFeeItem> feeItemList = govCarOrderBillDetails.stream()
                    .collect(Collectors.groupingBy(GovCarOrderBillDetail::getPriceCode))
                    .values().stream().map(list -> {
                                GovCarOrderFeeItem feeItem = new GovCarOrderFeeItem();
                                // 已支付金额
                                BigDecimal payAmount = BigDecimal.ZERO;
                                // 减免金额
                                BigDecimal itemReductionAmount = BigDecimal.ZERO;
                                // 增加费用金额
                                BigDecimal extendAmount = BigDecimal.ZERO;
                                // 已退款金额
                                BigDecimal refundAmount = BigDecimal.ZERO;
                                List<String> element = new ArrayList<>();
                                for (GovCarOrderBillDetail item : list) {
                                    feeItem.setPriceCode(item.getPriceCode());
                                    feeItem.setPriceName(item.getPriceName());
                                    switch (item.getDetailType()) {
                                        case 0:
                                            // 把单位的0 去除
                                            element.add(String.format(FORMAT_STR, decimalFormat.format(item.getPriceAmount()), item.getQuantity().stripTrailingZeros().toString()));
                                            payAmount = payAmount.add(item.getTotalAmount());
                                            break;
                                        case 1:
                                            extendAmount = extendAmount.add(item.getTotalAmount());
                                            break;
                                        case 2:
                                            itemReductionAmount = itemReductionAmount.add(item.getTotalAmount().abs());
                                            break;
                                        case 3:
                                            refundAmount = refundAmount.add(item.getTotalAmount().abs());
                                            break;
                                    }
                                }
                                // 总额
                                BigDecimal innerTotalAmount = payAmount.add(extendAmount).subtract(itemReductionAmount).subtract(refundAmount);
                                feeItem.setTotalAmount(decimalFormat.format(innerTotalAmount));
                                feeItem.setReductionAmount(decimalFormat.format(itemReductionAmount));
                                feeItem.setExtendAmount(decimalFormat.format(extendAmount));
                                feeItem.setRefundAmount(decimalFormat.format(refundAmount));
                                feeItem.setPriceAmount(String.join("+", element));
                                return feeItem;
                            }
                    ).collect(Collectors.toList());
            detail.setFeeItems(feeItemList);
        } else {
            // 没有账单不展示
            detail.setPayRecordList(Collections.emptyList());
            detail.setFeeItems(Collections.emptyList());
            detail.setTotalAmount(StringUtils.EMPTY);
            detail.setPayAmount(StringUtils.EMPTY);
            detail.setPendingAmount(StringUtils.EMPTY);
            detail.setRefundAmount(StringUtils.EMPTY);
        }
        // 查询验车单信息
        return RestResponse.success(detail);
    }

    @PostMapping(GOV_CAR_ORDER_GET_NO_FINISH_BY_VEHICLE_VIN)
    @ApiOperation(value = "根据车架号查询订单信息")
    public RestResponse<GovCarOrderInfoDTO> getNoFinishOrderByVehicleVin(@RequestBody GovCarOrderGetNoFinishByVehicleVinReqDTO reqDTO) {
        GovCarOrderInfoDTO result = govCarOrderService.getNoFinishOrderByVehicleVin(reqDTO);
        return RestResponse.success(result);
    }
}
