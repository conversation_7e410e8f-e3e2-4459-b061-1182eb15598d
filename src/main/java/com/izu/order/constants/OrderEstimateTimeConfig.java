package com.izu.order.constants;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class OrderEstimateTimeConfig implements InitializingBean{
	@Value("${ONE_WAY_SERVICE}")
    private Integer ONE_WAY_SERVICE;
	@Value("${HALF_DAY_RENT}")
    private Integer HALF_DAY_RENT;
	
	@Value("${ONE_DAY_RENT}")
    private Integer ONE_DAY_RENT;
	
	@Value("${AIRPORT_PICKUP}")
    private Integer AIRPORT_PICKUP;
	
	@Value("${AIRPORT_DROPOFF}")
    private Integer AIRPORT_DROPOFF;
	
	@Value("${STATION_PICKUP}")
    private Integer STATION_PICKUP;
	
	@Value("${STATION_DROPOFF}")
    private Integer STATION_DROPOFF;
	
	@Value("${ONCE}")
    private Integer ONCE;
	
	/** 配置对象, 单例一个 */
	private static OrderEstimateTimeConfig config = new OrderEstimateTimeConfig();
	@Override
	public void afterPropertiesSet() throws Exception {
		config = this;
        
	}
	
	public static OrderEstimateTimeConfig getConfig() {
		return config;
	}

	public Integer getONE_WAY_SERVICE() {
		return ONE_WAY_SERVICE;
	}
	public void setONE_WAY_SERVICE(Integer oNE_WAY_SERVICE) {
		ONE_WAY_SERVICE = oNE_WAY_SERVICE;
	}

	public Integer getHALF_DAY_RENT() {
		return HALF_DAY_RENT;
	}

	public void setHALF_DAY_RENT(Integer hALF_DAY_RENT) {
		HALF_DAY_RENT = hALF_DAY_RENT;
	}

	public Integer getONE_DAY_RENT() {
		return ONE_DAY_RENT;
	}

	public void setONE_DAY_RENT(Integer oNE_DAY_RENT) {
		ONE_DAY_RENT = oNE_DAY_RENT;
	}

	public Integer getAIRPORT_PICKUP() {
		return AIRPORT_PICKUP;
	}

	public void setAIRPORT_PICKUP(Integer aIRPORT_PICKUP) {
		AIRPORT_PICKUP = aIRPORT_PICKUP;
	}

	public Integer getAIRPORT_DROPOFF() {
		return AIRPORT_DROPOFF;
	}

	public void setAIRPORT_DROPOFF(Integer aIRPORT_DROPOFF) {
		AIRPORT_DROPOFF = aIRPORT_DROPOFF;
	}

	public Integer getSTATION_PICKUP() {
		return STATION_PICKUP;
	}

	public void setSTATION_PICKUP(Integer sTATION_PICKUP) {
		STATION_PICKUP = sTATION_PICKUP;
	}

	public Integer getSTATION_DROPOFF() {
		return STATION_DROPOFF;
	}

	public void setSTATION_DROPOFF(Integer sTATION_DROPOFF) {
		STATION_DROPOFF = sTATION_DROPOFF;
	}

	public Integer getONCE() {
		return ONCE;
	}

	public void setONCE(Integer oNCE) {
		ONCE = oNCE;
	}
	
	
}
