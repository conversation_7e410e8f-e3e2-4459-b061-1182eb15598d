package com.izu.order;

import com.alibaba.nacos.client.config.utils.SnapShotSwitch;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@MapperScan("mapper")
@SpringBootApplication(scanBasePackages = {"com.izu.order", "com.izu.framework"})
public class OrderApplication {

    public static void main(String[] args) {
        SnapShotSwitch.setIsSnapShot(false);
        SpringApplication.run(OrderApplication.class, args);
    }

}
