package mapper.mrcar;

import com.izu.order.entity.mrcar.BusinessSupplierExpenditureDetail;
import com.izu.order.entity.mrcar.BusinessSupplierExpenditureDetailExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BusinessSupplierExpenditureDetailMapper {
    /**
     *
     * @mbg.generated
     */
    long countByExample(BusinessSupplierExpenditureDetailExample example);

    /**
     *
     * @mbg.generated
     */
    int deleteByExample(BusinessSupplierExpenditureDetailExample example);

    /**
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     *
     * @mbg.generated
     */
    int insert(BusinessSupplierExpenditureDetail row);

    /**
     *
     * @mbg.generated
     */
    int insertSelective(BusinessSupplierExpenditureDetail row);

    /**
     *
     * @mbg.generated
     */
    List<BusinessSupplierExpenditureDetail> selectByExample(BusinessSupplierExpenditureDetailExample example);

    /**
     *
     * @mbg.generated
     */
    BusinessSupplierExpenditureDetail selectByPrimaryKey(Long id);

    /**
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("row") BusinessSupplierExpenditureDetail row, @Param("example") BusinessSupplierExpenditureDetailExample example);

    /**
     *
     * @mbg.generated
     */
    int updateByExample(@Param("row") BusinessSupplierExpenditureDetail row, @Param("example") BusinessSupplierExpenditureDetailExample example);

    /**
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(BusinessSupplierExpenditureDetail row);

    /**
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(BusinessSupplierExpenditureDetail row);
}