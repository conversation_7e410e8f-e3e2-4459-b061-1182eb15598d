package mapper.mrcar;

import com.izu.order.entity.mrcar.TemporalSharedVehicleStatistics;

public interface TemporalSharedVehicleStatisticsMapper {
    /**
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer id);

    /**
     *
     * @mbg.generated
     */
    int insert(TemporalSharedVehicleStatistics row);

    /**
     *
     * @mbg.generated
     */
    int insertSelective(TemporalSharedVehicleStatistics row);

    /**
     *
     * @mbg.generated
     */
    TemporalSharedVehicleStatistics selectByPrimaryKey(Integer id);

    /**
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(TemporalSharedVehicleStatistics row);

    /**
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(TemporalSharedVehicleStatistics row);
}