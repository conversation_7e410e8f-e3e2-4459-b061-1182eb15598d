<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:cache="http://www.springframework.org/schema/cache"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd
		http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache.xsd">

	<bean id="clearLoginInfoAndAuthorizingCustomer"  class="com.izu.framework.rocketmq.CommonRocketConsumer" destroy-method="destroy">
		<property name="nameServerGroup" value="mrcar"/><!-- RocketMQ命名服务器分组名 -->
		<property name="groupName" value="mrcar_clear_loginInfo_authorizing_customer"/><!-- 消费组名称,注意：相同的组名在同一个JVM中只能配置为一个 -->
		<property name="consumeFromWhere" value="CONSUME_FROM_LAST_OFFSET"/><!-- 消费起点 -->
		<property name="messageModel" value="BROADCASTING"/><!-- 消费模式 -->
		<property name="topic" value="mrcar_clear_loginInfo_authorizing"/><!-- 所消费的队列名 -->
		<property name="tags" value="*"/><!-- 所消费的队列名的订阅标签 -->
		<property name="messageBatchMaxSize" value="1"/><!-- 每次消费消息的最大数量 -->
		<property name="threads" value="1"/><!-- 并发消费线程数 -->
		<property name="messageListener"  ref="clearLoginInfoAndAuthorizingCustomerListener"/><!-- 消息处理实现类 -->
	</bean>

</beans>