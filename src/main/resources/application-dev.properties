environment.profile=dev_envrimont
port=8090




file.upload.dir=/usr/local/tomcat/webapps/mutifile/mrcar/
file.download.dir=/usr/local/tomcat/webapps/mutifile/mrcar/


#\u53D1\u77ED\u4FE1\u76F8\u5173\u914D\u7F6E
sms.host=http://hy.domsg.net:8090/domsg/smsSend.do
sms.username=100675
sms.password=sqzl620


map.trail.url=http://**************:8089/gwservice/location/getvehicletrail
list.trail.url=http://**************:8089/gwservice/location/downloadgpstrail
main.url=https://dev-web.imrcar.com/
#baidu.map.ak=78r5uLOa5iPY8kz8PfCD6RwRQblrQ3s1

#yingyan
yingyan.ak=TkTtaG3EnQIlegPzvgeC64lXT8Nesgdb
yingyan.serviceid=207308
yingyan.sk=BZtNu9N3wsf2BuYUpnaIOK5HcTGL1OqB
yingyan.host=http://yingyan.baidu.com

mrcar.vehicle.url=http://test-mrcar-api.izuche.com
# GPSç½å³æå¡
gateway.url=http://test-gpsgateway.izuche.com/gwservice
# GPSç½å³è¿ç§»åå°å
gateway.new.url=https://test-carnet.izuche.com/carnet-core

channel_code=fc05de9e
secretKey=68c28a1b-e6b0-4623-a3a3-0e1692d434d8

push.customer_id=139
push.customer_name=admin
push.company_id=1

download.trail.url=http://test-gpsgateway.izuche.com/gwservice/location/downloadgpstrail

motorcade.channel_code=shortrent
motorcade.secretKey=95E75F3C-7B6E-43DB-AA5B-299A607BC5D7
motorcade.url=http://test-chedui-order.izuche.com/

# config-core
mrcar-config-core.host.url=https://dev-config.imrcar.com/
# asset-core
mrcar-asset-core.host.url=https://dev-asset.imrcar.com/
# order-core
mrcar-order-core.host.url=https://dev-order.imrcar.com/
# coupon-core
mrcar-coupon-core.host.url=https://dev-coupon.imrcar.com/
# iot-core
mrcar-iot-core.host.url=https://dev-iot.imrcar.com/
# user-core
mrcar-user-core.host.url=https://dev-user.imrcar.com/
# business-core
mrcar-business-core.host.url=https://dev-business.imrcar.com/
# mrcar-app
mrcar-app.host.url=https://dev-app-api.imrcar.com/mrcarapp/
# chedui id
bussiness.chedui.companyid=1
# shouqi id
bussiness.main.companyid=1
env.name=dev
smsSwitch=false

#gps
gps.host.url=https://dev-carnet.izuche.com/carnet-core/

#third-api
third.host.url=https://dev-third.izuche.com/third-api/

# ç»å½éªè¯ç åé´æåå
login_white_list=***********,***********
# æ°ç³»ç»åç»å½ç¨æ·ä¿¡æ¯æ¥è¯¢æ¥å£
newSystem.userInfo.url=https://dev-izubackground.izuche.com/izubackground/thirdPartyUrlVerifySession.json

xxl.job.admin.addresses=https://dev-job-idc-admin.izuche.com/izu-job-idc-admin
xxl.job.executor.appname=mrcar
xxl.job.executor.ip=
xxl.job.executor.port=0
xxl.job.executor.logpath=/data/logs/mrcar/mrcar-job
xxl.job.executor.logretentiondays=30
xxl.job.accessToken=

# Swaggeréç½® æ¯å¦å¼å¯swagger
swagger.enabled=true
baidu.ak=
access.main.url=
baidu.yingyan.ak=
databig.download.trail.url=
databig.list.trail.url=
databig.map.trail.url=
baidu.yingyan.host=
baidu.yingyan.sk=
baidu.yingyan.serviceid=

config.sms.username=
config.sms.password=
config.sms.host=