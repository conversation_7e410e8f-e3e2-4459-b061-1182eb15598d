<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.MrCarBillingInfoMapper">
  <resultMap id="BaseResultMap" type="com.izu.business.entity.MrCarBillingInfo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="bill_code" jdbcType="VARCHAR" property="billCode" />
    <result column="order_id" jdbcType="INTEGER" property="orderId" />
    <result column="order_code" jdbcType="VARCHAR" property="orderCode" />
    <result column="customer_code" jdbcType="VARCHAR" property="customerCode" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="bill_type" jdbcType="TINYINT" property="billType" />
    <result column="billing_method" jdbcType="VARCHAR" property="billingMethod" />
    <result column="return_amount" jdbcType="DECIMAL" property="returnAmount" />
    <result column="bill_status" jdbcType="TINYINT" property="billStatus" />
    <result column="bill_amount" jdbcType="DECIMAL" property="billAmount" />
    <result column="repayment_status" jdbcType="INTEGER" property="repaymentStatus" />
    <result column="repayment_amount" jdbcType="DECIMAL" property="repaymentAmount" />
    <result column="bill_sdate" jdbcType="DATE" property="billSdate" />
    <result column="bill_edate" jdbcType="DATE" property="billEdate" />
    <result column="payment_cycle" jdbcType="VARCHAR" property="paymentCycle" />
    <result column="bill_period" jdbcType="INTEGER" property="billPeriod" />
    <result column="bill_total_period" jdbcType="INTEGER" property="billTotalPeriod" />
    <result column="order_period" jdbcType="INTEGER" property="orderPeriod" />
    <result column="order_total_period" jdbcType="INTEGER" property="orderTotalPeriod" />
    <result column="payment_due_date" jdbcType="DATE" property="paymentDueDate" />
    <result column="tax_rate" jdbcType="DECIMAL" property="taxRate" />
    <result column="contract_code" jdbcType="VARCHAR" property="contractCode" />
    <result column="sign_sales_id" jdbcType="VARCHAR" property="signSalesId" />
    <result column="sign_sales_name" jdbcType="VARCHAR" property="signSalesName" />
    <result column="finance_struct_code" jdbcType="VARCHAR" property="financeStructCode" />
    <result column="finance_struct_name" jdbcType="VARCHAR" property="financeStructName" />
    <result column="operate_code" jdbcType="VARCHAR" property="operateCode" />
    <result column="operate_name" jdbcType="VARCHAR" property="operateName" />
    <result column="unit_price" jdbcType="DECIMAL" property="unitPrice" />
    <result column="quantity" jdbcType="DECIMAL" property="quantity" />
    <result column="adjust_amount" jdbcType="DECIMAL" property="adjustAmount" />
    <result column="create_method" jdbcType="VARCHAR" property="createMethod" />
    <result column="bill_desc" jdbcType="VARCHAR" property="billDesc" />
    <result column="billing_date" jdbcType="DATE" property="billingDate" />
    <result column="billing_period_days" jdbcType="INTEGER" property="billingPeriodDays" />
    <result column="create_id" jdbcType="VARCHAR" property="createId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_id" jdbcType="VARCHAR" property="updateId" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="adjust_reason" jdbcType="VARCHAR" property="adjustReason" />
    <result column="contract_type" jdbcType="INTEGER" property="contractType" />
    <result column="contract_id" jdbcType="INTEGER" property="contractId" />
    <result column="maintain_id" jdbcType="VARCHAR" property="maintainId" />
    <result column="maintain_name" jdbcType="VARCHAR" property="maintainName" />
    <result column="payment_type" jdbcType="INTEGER" property="paymentType" />
    <result column="config_date" jdbcType="TIMESTAMP" property="configDate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, bill_code, order_id, order_code, customer_code, customer_name, bill_type, billing_method, 
    return_amount, bill_status, bill_amount, repayment_status, repayment_amount, bill_sdate, 
    bill_edate, payment_cycle, bill_period, bill_total_period, order_period, order_total_period, 
    payment_due_date, tax_rate, contract_code, sign_sales_id, sign_sales_name, finance_struct_code, 
    finance_struct_name, operate_code, operate_name, unit_price, quantity, adjust_amount, 
    create_method, bill_desc, billing_date, billing_period_days, create_id, create_name, 
    create_time, update_id, update_name, update_time, adjust_reason, contract_type, contract_id, 
    maintain_id, maintain_name, payment_type, config_date
  </sql>
  <select id="selectByExample" parameterType="com.izu.business.entity.MrCarBillingInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mrcar_billing_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mrcar_billing_info
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from mrcar_billing_info
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.business.entity.MrCarBillingInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mrcar_billing_info (bill_code, order_id, order_code, 
      customer_code, customer_name, bill_type, 
      billing_method, return_amount, bill_status, 
      bill_amount, repayment_status, repayment_amount, 
      bill_sdate, bill_edate, payment_cycle, 
      bill_period, bill_total_period, order_period, 
      order_total_period, payment_due_date, tax_rate, 
      contract_code, sign_sales_id, sign_sales_name, 
      finance_struct_code, finance_struct_name, operate_code, 
      operate_name, unit_price, quantity, 
      adjust_amount, create_method, bill_desc, 
      billing_date, billing_period_days, create_id, 
      create_name, create_time, update_id, 
      update_name, update_time, adjust_reason, 
      contract_type, contract_id, maintain_id, 
      maintain_name, payment_type, config_date
      )
    values (#{billCode,jdbcType=VARCHAR}, #{orderId,jdbcType=INTEGER}, #{orderCode,jdbcType=VARCHAR}, 
      #{customerCode,jdbcType=VARCHAR}, #{customerName,jdbcType=VARCHAR}, #{billType,jdbcType=TINYINT}, 
      #{billingMethod,jdbcType=VARCHAR}, #{returnAmount,jdbcType=DECIMAL}, #{billStatus,jdbcType=TINYINT}, 
      #{billAmount,jdbcType=DECIMAL}, #{repaymentStatus,jdbcType=INTEGER}, #{repaymentAmount,jdbcType=DECIMAL}, 
      #{billSdate,jdbcType=DATE}, #{billEdate,jdbcType=DATE}, #{paymentCycle,jdbcType=VARCHAR}, 
      #{billPeriod,jdbcType=INTEGER}, #{billTotalPeriod,jdbcType=INTEGER}, #{orderPeriod,jdbcType=INTEGER}, 
      #{orderTotalPeriod,jdbcType=INTEGER}, #{paymentDueDate,jdbcType=DATE}, #{taxRate,jdbcType=DECIMAL}, 
      #{contractCode,jdbcType=VARCHAR}, #{signSalesId,jdbcType=VARCHAR}, #{signSalesName,jdbcType=VARCHAR}, 
      #{financeStructCode,jdbcType=VARCHAR}, #{financeStructName,jdbcType=VARCHAR}, #{operateCode,jdbcType=VARCHAR}, 
      #{operateName,jdbcType=VARCHAR}, #{unitPrice,jdbcType=DECIMAL}, #{quantity,jdbcType=DECIMAL}, 
      #{adjustAmount,jdbcType=DECIMAL}, #{createMethod,jdbcType=VARCHAR}, #{billDesc,jdbcType=VARCHAR}, 
      #{billingDate,jdbcType=DATE}, #{billingPeriodDays,jdbcType=INTEGER}, #{createId,jdbcType=VARCHAR}, 
      #{createName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateId,jdbcType=VARCHAR}, 
      #{updateName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{adjustReason,jdbcType=VARCHAR}, 
      #{contractType,jdbcType=INTEGER}, #{contractId,jdbcType=INTEGER}, #{maintainId,jdbcType=VARCHAR}, 
      #{maintainName,jdbcType=VARCHAR}, #{paymentType,jdbcType=INTEGER}, #{configDate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.business.entity.MrCarBillingInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mrcar_billing_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="billCode != null">
        bill_code,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderCode != null">
        order_code,
      </if>
      <if test="customerCode != null">
        customer_code,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="billType != null">
        bill_type,
      </if>
      <if test="billingMethod != null">
        billing_method,
      </if>
      <if test="returnAmount != null">
        return_amount,
      </if>
      <if test="billStatus != null">
        bill_status,
      </if>
      <if test="billAmount != null">
        bill_amount,
      </if>
      <if test="repaymentStatus != null">
        repayment_status,
      </if>
      <if test="repaymentAmount != null">
        repayment_amount,
      </if>
      <if test="billSdate != null">
        bill_sdate,
      </if>
      <if test="billEdate != null">
        bill_edate,
      </if>
      <if test="paymentCycle != null">
        payment_cycle,
      </if>
      <if test="billPeriod != null">
        bill_period,
      </if>
      <if test="billTotalPeriod != null">
        bill_total_period,
      </if>
      <if test="orderPeriod != null">
        order_period,
      </if>
      <if test="orderTotalPeriod != null">
        order_total_period,
      </if>
      <if test="paymentDueDate != null">
        payment_due_date,
      </if>
      <if test="taxRate != null">
        tax_rate,
      </if>
      <if test="contractCode != null">
        contract_code,
      </if>
      <if test="signSalesId != null">
        sign_sales_id,
      </if>
      <if test="signSalesName != null">
        sign_sales_name,
      </if>
      <if test="financeStructCode != null">
        finance_struct_code,
      </if>
      <if test="financeStructName != null">
        finance_struct_name,
      </if>
      <if test="operateCode != null">
        operate_code,
      </if>
      <if test="operateName != null">
        operate_name,
      </if>
      <if test="unitPrice != null">
        unit_price,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="adjustAmount != null">
        adjust_amount,
      </if>
      <if test="createMethod != null">
        create_method,
      </if>
      <if test="billDesc != null">
        bill_desc,
      </if>
      <if test="billingDate != null">
        billing_date,
      </if>
      <if test="billingPeriodDays != null">
        billing_period_days,
      </if>
      <if test="createId != null">
        create_id,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateId != null">
        update_id,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="adjustReason != null">
        adjust_reason,
      </if>
      <if test="contractType != null">
        contract_type,
      </if>
      <if test="contractId != null">
        contract_id,
      </if>
      <if test="maintainId != null">
        maintain_id,
      </if>
      <if test="maintainName != null">
        maintain_name,
      </if>
      <if test="paymentType != null">
        payment_type,
      </if>
      <if test="configDate != null">
        config_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="billCode != null">
        #{billCode,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderCode != null">
        #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="customerCode != null">
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="billType != null">
        #{billType,jdbcType=TINYINT},
      </if>
      <if test="billingMethod != null">
        #{billingMethod,jdbcType=VARCHAR},
      </if>
      <if test="returnAmount != null">
        #{returnAmount,jdbcType=DECIMAL},
      </if>
      <if test="billStatus != null">
        #{billStatus,jdbcType=TINYINT},
      </if>
      <if test="billAmount != null">
        #{billAmount,jdbcType=DECIMAL},
      </if>
      <if test="repaymentStatus != null">
        #{repaymentStatus,jdbcType=INTEGER},
      </if>
      <if test="repaymentAmount != null">
        #{repaymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="billSdate != null">
        #{billSdate,jdbcType=DATE},
      </if>
      <if test="billEdate != null">
        #{billEdate,jdbcType=DATE},
      </if>
      <if test="paymentCycle != null">
        #{paymentCycle,jdbcType=VARCHAR},
      </if>
      <if test="billPeriod != null">
        #{billPeriod,jdbcType=INTEGER},
      </if>
      <if test="billTotalPeriod != null">
        #{billTotalPeriod,jdbcType=INTEGER},
      </if>
      <if test="orderPeriod != null">
        #{orderPeriod,jdbcType=INTEGER},
      </if>
      <if test="orderTotalPeriod != null">
        #{orderTotalPeriod,jdbcType=INTEGER},
      </if>
      <if test="paymentDueDate != null">
        #{paymentDueDate,jdbcType=DATE},
      </if>
      <if test="taxRate != null">
        #{taxRate,jdbcType=DECIMAL},
      </if>
      <if test="contractCode != null">
        #{contractCode,jdbcType=VARCHAR},
      </if>
      <if test="signSalesId != null">
        #{signSalesId,jdbcType=VARCHAR},
      </if>
      <if test="signSalesName != null">
        #{signSalesName,jdbcType=VARCHAR},
      </if>
      <if test="financeStructCode != null">
        #{financeStructCode,jdbcType=VARCHAR},
      </if>
      <if test="financeStructName != null">
        #{financeStructName,jdbcType=VARCHAR},
      </if>
      <if test="operateCode != null">
        #{operateCode,jdbcType=VARCHAR},
      </if>
      <if test="operateName != null">
        #{operateName,jdbcType=VARCHAR},
      </if>
      <if test="unitPrice != null">
        #{unitPrice,jdbcType=DECIMAL},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=DECIMAL},
      </if>
      <if test="adjustAmount != null">
        #{adjustAmount,jdbcType=DECIMAL},
      </if>
      <if test="createMethod != null">
        #{createMethod,jdbcType=VARCHAR},
      </if>
      <if test="billDesc != null">
        #{billDesc,jdbcType=VARCHAR},
      </if>
      <if test="billingDate != null">
        #{billingDate,jdbcType=DATE},
      </if>
      <if test="billingPeriodDays != null">
        #{billingPeriodDays,jdbcType=INTEGER},
      </if>
      <if test="createId != null">
        #{createId,jdbcType=VARCHAR},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        #{updateId,jdbcType=VARCHAR},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="adjustReason != null">
        #{adjustReason,jdbcType=VARCHAR},
      </if>
      <if test="contractType != null">
        #{contractType,jdbcType=INTEGER},
      </if>
      <if test="contractId != null">
        #{contractId,jdbcType=INTEGER},
      </if>
      <if test="maintainId != null">
        #{maintainId,jdbcType=VARCHAR},
      </if>
      <if test="maintainName != null">
        #{maintainName,jdbcType=VARCHAR},
      </if>
      <if test="paymentType != null">
        #{paymentType,jdbcType=INTEGER},
      </if>
      <if test="configDate != null">
        #{configDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.business.entity.MrCarBillingInfoExample" resultType="java.lang.Long">
    select count(*) from mrcar_billing_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mrcar_billing_info
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=INTEGER},
      </if>
      <if test="row.billCode != null">
        bill_code = #{row.billCode,jdbcType=VARCHAR},
      </if>
      <if test="row.orderId != null">
        order_id = #{row.orderId,jdbcType=INTEGER},
      </if>
      <if test="row.orderCode != null">
        order_code = #{row.orderCode,jdbcType=VARCHAR},
      </if>
      <if test="row.customerCode != null">
        customer_code = #{row.customerCode,jdbcType=VARCHAR},
      </if>
      <if test="row.customerName != null">
        customer_name = #{row.customerName,jdbcType=VARCHAR},
      </if>
      <if test="row.billType != null">
        bill_type = #{row.billType,jdbcType=TINYINT},
      </if>
      <if test="row.billingMethod != null">
        billing_method = #{row.billingMethod,jdbcType=VARCHAR},
      </if>
      <if test="row.returnAmount != null">
        return_amount = #{row.returnAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.billStatus != null">
        bill_status = #{row.billStatus,jdbcType=TINYINT},
      </if>
      <if test="row.billAmount != null">
        bill_amount = #{row.billAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.repaymentStatus != null">
        repayment_status = #{row.repaymentStatus,jdbcType=INTEGER},
      </if>
      <if test="row.repaymentAmount != null">
        repayment_amount = #{row.repaymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.billSdate != null">
        bill_sdate = #{row.billSdate,jdbcType=DATE},
      </if>
      <if test="row.billEdate != null">
        bill_edate = #{row.billEdate,jdbcType=DATE},
      </if>
      <if test="row.paymentCycle != null">
        payment_cycle = #{row.paymentCycle,jdbcType=VARCHAR},
      </if>
      <if test="row.billPeriod != null">
        bill_period = #{row.billPeriod,jdbcType=INTEGER},
      </if>
      <if test="row.billTotalPeriod != null">
        bill_total_period = #{row.billTotalPeriod,jdbcType=INTEGER},
      </if>
      <if test="row.orderPeriod != null">
        order_period = #{row.orderPeriod,jdbcType=INTEGER},
      </if>
      <if test="row.orderTotalPeriod != null">
        order_total_period = #{row.orderTotalPeriod,jdbcType=INTEGER},
      </if>
      <if test="row.paymentDueDate != null">
        payment_due_date = #{row.paymentDueDate,jdbcType=DATE},
      </if>
      <if test="row.taxRate != null">
        tax_rate = #{row.taxRate,jdbcType=DECIMAL},
      </if>
      <if test="row.contractCode != null">
        contract_code = #{row.contractCode,jdbcType=VARCHAR},
      </if>
      <if test="row.signSalesId != null">
        sign_sales_id = #{row.signSalesId,jdbcType=VARCHAR},
      </if>
      <if test="row.signSalesName != null">
        sign_sales_name = #{row.signSalesName,jdbcType=VARCHAR},
      </if>
      <if test="row.financeStructCode != null">
        finance_struct_code = #{row.financeStructCode,jdbcType=VARCHAR},
      </if>
      <if test="row.financeStructName != null">
        finance_struct_name = #{row.financeStructName,jdbcType=VARCHAR},
      </if>
      <if test="row.operateCode != null">
        operate_code = #{row.operateCode,jdbcType=VARCHAR},
      </if>
      <if test="row.operateName != null">
        operate_name = #{row.operateName,jdbcType=VARCHAR},
      </if>
      <if test="row.unitPrice != null">
        unit_price = #{row.unitPrice,jdbcType=DECIMAL},
      </if>
      <if test="row.quantity != null">
        quantity = #{row.quantity,jdbcType=DECIMAL},
      </if>
      <if test="row.adjustAmount != null">
        adjust_amount = #{row.adjustAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.createMethod != null">
        create_method = #{row.createMethod,jdbcType=VARCHAR},
      </if>
      <if test="row.billDesc != null">
        bill_desc = #{row.billDesc,jdbcType=VARCHAR},
      </if>
      <if test="row.billingDate != null">
        billing_date = #{row.billingDate,jdbcType=DATE},
      </if>
      <if test="row.billingPeriodDays != null">
        billing_period_days = #{row.billingPeriodDays,jdbcType=INTEGER},
      </if>
      <if test="row.createId != null">
        create_id = #{row.createId,jdbcType=VARCHAR},
      </if>
      <if test="row.createName != null">
        create_name = #{row.createName,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateId != null">
        update_id = #{row.updateId,jdbcType=VARCHAR},
      </if>
      <if test="row.updateName != null">
        update_name = #{row.updateName,jdbcType=VARCHAR},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.adjustReason != null">
        adjust_reason = #{row.adjustReason,jdbcType=VARCHAR},
      </if>
      <if test="row.contractType != null">
        contract_type = #{row.contractType,jdbcType=INTEGER},
      </if>
      <if test="row.contractId != null">
        contract_id = #{row.contractId,jdbcType=INTEGER},
      </if>
      <if test="row.maintainId != null">
        maintain_id = #{row.maintainId,jdbcType=VARCHAR},
      </if>
      <if test="row.maintainName != null">
        maintain_name = #{row.maintainName,jdbcType=VARCHAR},
      </if>
      <if test="row.paymentType != null">
        payment_type = #{row.paymentType,jdbcType=INTEGER},
      </if>
      <if test="row.configDate != null">
        config_date = #{row.configDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mrcar_billing_info
    set id = #{row.id,jdbcType=INTEGER},
      bill_code = #{row.billCode,jdbcType=VARCHAR},
      order_id = #{row.orderId,jdbcType=INTEGER},
      order_code = #{row.orderCode,jdbcType=VARCHAR},
      customer_code = #{row.customerCode,jdbcType=VARCHAR},
      customer_name = #{row.customerName,jdbcType=VARCHAR},
      bill_type = #{row.billType,jdbcType=TINYINT},
      billing_method = #{row.billingMethod,jdbcType=VARCHAR},
      return_amount = #{row.returnAmount,jdbcType=DECIMAL},
      bill_status = #{row.billStatus,jdbcType=TINYINT},
      bill_amount = #{row.billAmount,jdbcType=DECIMAL},
      repayment_status = #{row.repaymentStatus,jdbcType=INTEGER},
      repayment_amount = #{row.repaymentAmount,jdbcType=DECIMAL},
      bill_sdate = #{row.billSdate,jdbcType=DATE},
      bill_edate = #{row.billEdate,jdbcType=DATE},
      payment_cycle = #{row.paymentCycle,jdbcType=VARCHAR},
      bill_period = #{row.billPeriod,jdbcType=INTEGER},
      bill_total_period = #{row.billTotalPeriod,jdbcType=INTEGER},
      order_period = #{row.orderPeriod,jdbcType=INTEGER},
      order_total_period = #{row.orderTotalPeriod,jdbcType=INTEGER},
      payment_due_date = #{row.paymentDueDate,jdbcType=DATE},
      tax_rate = #{row.taxRate,jdbcType=DECIMAL},
      contract_code = #{row.contractCode,jdbcType=VARCHAR},
      sign_sales_id = #{row.signSalesId,jdbcType=VARCHAR},
      sign_sales_name = #{row.signSalesName,jdbcType=VARCHAR},
      finance_struct_code = #{row.financeStructCode,jdbcType=VARCHAR},
      finance_struct_name = #{row.financeStructName,jdbcType=VARCHAR},
      operate_code = #{row.operateCode,jdbcType=VARCHAR},
      operate_name = #{row.operateName,jdbcType=VARCHAR},
      unit_price = #{row.unitPrice,jdbcType=DECIMAL},
      quantity = #{row.quantity,jdbcType=DECIMAL},
      adjust_amount = #{row.adjustAmount,jdbcType=DECIMAL},
      create_method = #{row.createMethod,jdbcType=VARCHAR},
      bill_desc = #{row.billDesc,jdbcType=VARCHAR},
      billing_date = #{row.billingDate,jdbcType=DATE},
      billing_period_days = #{row.billingPeriodDays,jdbcType=INTEGER},
      create_id = #{row.createId,jdbcType=VARCHAR},
      create_name = #{row.createName,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_id = #{row.updateId,jdbcType=VARCHAR},
      update_name = #{row.updateName,jdbcType=VARCHAR},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      adjust_reason = #{row.adjustReason,jdbcType=VARCHAR},
      contract_type = #{row.contractType,jdbcType=INTEGER},
      contract_id = #{row.contractId,jdbcType=INTEGER},
      maintain_id = #{row.maintainId,jdbcType=VARCHAR},
      maintain_name = #{row.maintainName,jdbcType=VARCHAR},
      payment_type = #{row.paymentType,jdbcType=INTEGER},
      config_date = #{row.configDate,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.business.entity.MrCarBillingInfo">
    update mrcar_billing_info
    <set>
      <if test="billCode != null">
        bill_code = #{billCode,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderCode != null">
        order_code = #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="customerCode != null">
        customer_code = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null">
        customer_name = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="billType != null">
        bill_type = #{billType,jdbcType=TINYINT},
      </if>
      <if test="billingMethod != null">
        billing_method = #{billingMethod,jdbcType=VARCHAR},
      </if>
      <if test="returnAmount != null">
        return_amount = #{returnAmount,jdbcType=DECIMAL},
      </if>
      <if test="billStatus != null">
        bill_status = #{billStatus,jdbcType=TINYINT},
      </if>
      <if test="billAmount != null">
        bill_amount = #{billAmount,jdbcType=DECIMAL},
      </if>
      <if test="repaymentStatus != null">
        repayment_status = #{repaymentStatus,jdbcType=INTEGER},
      </if>
      <if test="repaymentAmount != null">
        repayment_amount = #{repaymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="billSdate != null">
        bill_sdate = #{billSdate,jdbcType=DATE},
      </if>
      <if test="billEdate != null">
        bill_edate = #{billEdate,jdbcType=DATE},
      </if>
      <if test="paymentCycle != null">
        payment_cycle = #{paymentCycle,jdbcType=VARCHAR},
      </if>
      <if test="billPeriod != null">
        bill_period = #{billPeriod,jdbcType=INTEGER},
      </if>
      <if test="billTotalPeriod != null">
        bill_total_period = #{billTotalPeriod,jdbcType=INTEGER},
      </if>
      <if test="orderPeriod != null">
        order_period = #{orderPeriod,jdbcType=INTEGER},
      </if>
      <if test="orderTotalPeriod != null">
        order_total_period = #{orderTotalPeriod,jdbcType=INTEGER},
      </if>
      <if test="paymentDueDate != null">
        payment_due_date = #{paymentDueDate,jdbcType=DATE},
      </if>
      <if test="taxRate != null">
        tax_rate = #{taxRate,jdbcType=DECIMAL},
      </if>
      <if test="contractCode != null">
        contract_code = #{contractCode,jdbcType=VARCHAR},
      </if>
      <if test="signSalesId != null">
        sign_sales_id = #{signSalesId,jdbcType=VARCHAR},
      </if>
      <if test="signSalesName != null">
        sign_sales_name = #{signSalesName,jdbcType=VARCHAR},
      </if>
      <if test="financeStructCode != null">
        finance_struct_code = #{financeStructCode,jdbcType=VARCHAR},
      </if>
      <if test="financeStructName != null">
        finance_struct_name = #{financeStructName,jdbcType=VARCHAR},
      </if>
      <if test="operateCode != null">
        operate_code = #{operateCode,jdbcType=VARCHAR},
      </if>
      <if test="operateName != null">
        operate_name = #{operateName,jdbcType=VARCHAR},
      </if>
      <if test="unitPrice != null">
        unit_price = #{unitPrice,jdbcType=DECIMAL},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=DECIMAL},
      </if>
      <if test="adjustAmount != null">
        adjust_amount = #{adjustAmount,jdbcType=DECIMAL},
      </if>
      <if test="createMethod != null">
        create_method = #{createMethod,jdbcType=VARCHAR},
      </if>
      <if test="billDesc != null">
        bill_desc = #{billDesc,jdbcType=VARCHAR},
      </if>
      <if test="billingDate != null">
        billing_date = #{billingDate,jdbcType=DATE},
      </if>
      <if test="billingPeriodDays != null">
        billing_period_days = #{billingPeriodDays,jdbcType=INTEGER},
      </if>
      <if test="createId != null">
        create_id = #{createId,jdbcType=VARCHAR},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        update_id = #{updateId,jdbcType=VARCHAR},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="adjustReason != null">
        adjust_reason = #{adjustReason,jdbcType=VARCHAR},
      </if>
      <if test="contractType != null">
        contract_type = #{contractType,jdbcType=INTEGER},
      </if>
      <if test="contractId != null">
        contract_id = #{contractId,jdbcType=INTEGER},
      </if>
      <if test="maintainId != null">
        maintain_id = #{maintainId,jdbcType=VARCHAR},
      </if>
      <if test="maintainName != null">
        maintain_name = #{maintainName,jdbcType=VARCHAR},
      </if>
      <if test="paymentType != null">
        payment_type = #{paymentType,jdbcType=INTEGER},
      </if>
      <if test="configDate != null">
        config_date = #{configDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.business.entity.MrCarBillingInfo">
    update mrcar_billing_info
    set bill_code = #{billCode,jdbcType=VARCHAR},
      order_id = #{orderId,jdbcType=INTEGER},
      order_code = #{orderCode,jdbcType=VARCHAR},
      customer_code = #{customerCode,jdbcType=VARCHAR},
      customer_name = #{customerName,jdbcType=VARCHAR},
      bill_type = #{billType,jdbcType=TINYINT},
      billing_method = #{billingMethod,jdbcType=VARCHAR},
      return_amount = #{returnAmount,jdbcType=DECIMAL},
      bill_status = #{billStatus,jdbcType=TINYINT},
      bill_amount = #{billAmount,jdbcType=DECIMAL},
      repayment_status = #{repaymentStatus,jdbcType=INTEGER},
      repayment_amount = #{repaymentAmount,jdbcType=DECIMAL},
      bill_sdate = #{billSdate,jdbcType=DATE},
      bill_edate = #{billEdate,jdbcType=DATE},
      payment_cycle = #{paymentCycle,jdbcType=VARCHAR},
      bill_period = #{billPeriod,jdbcType=INTEGER},
      bill_total_period = #{billTotalPeriod,jdbcType=INTEGER},
      order_period = #{orderPeriod,jdbcType=INTEGER},
      order_total_period = #{orderTotalPeriod,jdbcType=INTEGER},
      payment_due_date = #{paymentDueDate,jdbcType=DATE},
      tax_rate = #{taxRate,jdbcType=DECIMAL},
      contract_code = #{contractCode,jdbcType=VARCHAR},
      sign_sales_id = #{signSalesId,jdbcType=VARCHAR},
      sign_sales_name = #{signSalesName,jdbcType=VARCHAR},
      finance_struct_code = #{financeStructCode,jdbcType=VARCHAR},
      finance_struct_name = #{financeStructName,jdbcType=VARCHAR},
      operate_code = #{operateCode,jdbcType=VARCHAR},
      operate_name = #{operateName,jdbcType=VARCHAR},
      unit_price = #{unitPrice,jdbcType=DECIMAL},
      quantity = #{quantity,jdbcType=DECIMAL},
      adjust_amount = #{adjustAmount,jdbcType=DECIMAL},
      create_method = #{createMethod,jdbcType=VARCHAR},
      bill_desc = #{billDesc,jdbcType=VARCHAR},
      billing_date = #{billingDate,jdbcType=DATE},
      billing_period_days = #{billingPeriodDays,jdbcType=INTEGER},
      create_id = #{createId,jdbcType=VARCHAR},
      create_name = #{createName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_id = #{updateId,jdbcType=VARCHAR},
      update_name = #{updateName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      adjust_reason = #{adjustReason,jdbcType=VARCHAR},
      contract_type = #{contractType,jdbcType=INTEGER},
      contract_id = #{contractId,jdbcType=INTEGER},
      maintain_id = #{maintainId,jdbcType=VARCHAR},
      maintain_name = #{maintainName,jdbcType=VARCHAR},
      payment_type = #{paymentType,jdbcType=INTEGER},
      config_date = #{configDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>