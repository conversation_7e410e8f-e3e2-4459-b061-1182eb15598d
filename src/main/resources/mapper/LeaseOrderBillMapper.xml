<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.LeaseOrderBillMapper">
  <resultMap id="BaseResultMap" type="com.izu.business.entity.LeaseOrderBill">
    <id column="bill_id" jdbcType="INTEGER" property="billId" />
    <result column="bill_relate_code" jdbcType="VARCHAR" property="billRelateCode" />
    <result column="bill_code" jdbcType="VARCHAR" property="billCode" />
    <result column="customer_id" jdbcType="INTEGER" property="customerId" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="contract_id" jdbcType="INTEGER" property="contractId" />
    <result column="contract_code" jdbcType="VARCHAR" property="contractCode" />
    <result column="relet_contract_code" jdbcType="VARCHAR" property="reletContractCode" />
    <result column="attach_contract_code" jdbcType="VARCHAR" property="attachContractCode" />
    <result column="order_id" jdbcType="INTEGER" property="orderId" />
    <result column="order_code" jdbcType="VARCHAR" property="orderCode" />
    <result column="bill_type" jdbcType="TINYINT" property="billType" />
    <result column="adjust_total_amount" jdbcType="DECIMAL" property="adjustTotalAmount" />
    <result column="reduce_amount" jdbcType="DECIMAL" property="reduceAmount" />
    <result column="return_amount" jdbcType="DECIMAL" property="returnAmount" />
    <result column="bill_amount" jdbcType="DECIMAL" property="billAmount" />
    <result column="bill_real_amount" jdbcType="DECIMAL" property="billRealAmount" />
    <result column="total_bill_stage" jdbcType="INTEGER" property="totalBillStage" />
    <result column="bill_stage" jdbcType="INTEGER" property="billStage" />
    <result column="bill_date" jdbcType="DATE" property="billDate" />
    <result column="bill_sdate" jdbcType="DATE" property="billSdate" />
    <result column="bill_edate" jdbcType="DATE" property="billEdate" />
    <result column="real_bill_edate" jdbcType="DATE" property="realBillEdate" />
    <result column="bill_status" jdbcType="INTEGER" property="billStatus" />
    <result column="audit_status" jdbcType="INTEGER" property="auditStatus" />
    <result column="apply_id" jdbcType="VARCHAR" property="applyId" />
    <result column="valid" jdbcType="BIT" property="valid" />
    <result column="first_balance_time" jdbcType="DATE" property="firstBalanceTime" />
    <result column="create_id" jdbcType="VARCHAR" property="createId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Base_Column_List">
    bill_id, bill_relate_code, bill_code, customer_id, customer_name, contract_id, contract_code, 
    relet_contract_code, attach_contract_code, order_id, order_code, bill_type, adjust_total_amount, 
    reduce_amount, return_amount, bill_amount, bill_real_amount, total_bill_stage, bill_stage, 
    bill_date, bill_sdate, bill_edate, real_bill_edate, bill_status, audit_status, apply_id, 
    valid, first_balance_time, create_id, create_name, create_time, update_time, remark
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lease_order_bill
    where bill_id = #{billId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from lease_order_bill
    where bill_id = #{billId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.business.entity.LeaseOrderBill">
    <selectKey keyProperty="billId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lease_order_bill (bill_relate_code, bill_code, customer_id, 
      customer_name, contract_id, contract_code, 
      relet_contract_code, attach_contract_code, 
      order_id, order_code, bill_type, 
      adjust_total_amount, reduce_amount, return_amount, 
      bill_amount, bill_real_amount, total_bill_stage, 
      bill_stage, bill_date, bill_sdate, 
      bill_edate, real_bill_edate, bill_status, 
      audit_status, apply_id, valid, 
      first_balance_time, create_id, create_name, 
      create_time, update_time, remark
      )
    values (#{billRelateCode,jdbcType=VARCHAR}, #{billCode,jdbcType=VARCHAR}, #{customerId,jdbcType=INTEGER}, 
      #{customerName,jdbcType=VARCHAR}, #{contractId,jdbcType=INTEGER}, #{contractCode,jdbcType=VARCHAR}, 
      #{reletContractCode,jdbcType=VARCHAR}, #{attachContractCode,jdbcType=VARCHAR}, 
      #{orderId,jdbcType=INTEGER}, #{orderCode,jdbcType=VARCHAR}, #{billType,jdbcType=TINYINT}, 
      #{adjustTotalAmount,jdbcType=DECIMAL}, #{reduceAmount,jdbcType=DECIMAL}, #{returnAmount,jdbcType=DECIMAL}, 
      #{billAmount,jdbcType=DECIMAL}, #{billRealAmount,jdbcType=DECIMAL}, #{totalBillStage,jdbcType=INTEGER}, 
      #{billStage,jdbcType=INTEGER}, #{billDate,jdbcType=DATE}, #{billSdate,jdbcType=DATE}, 
      #{billEdate,jdbcType=DATE}, #{realBillEdate,jdbcType=DATE}, #{billStatus,jdbcType=INTEGER}, 
      #{auditStatus,jdbcType=INTEGER}, #{applyId,jdbcType=VARCHAR}, #{valid,jdbcType=BIT}, 
      #{firstBalanceTime,jdbcType=DATE}, #{createId,jdbcType=VARCHAR}, #{createName,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.business.entity.LeaseOrderBill">
    <selectKey keyProperty="billId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lease_order_bill
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="billRelateCode != null">
        bill_relate_code,
      </if>
      <if test="billCode != null">
        bill_code,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="contractId != null">
        contract_id,
      </if>
      <if test="contractCode != null">
        contract_code,
      </if>
      <if test="reletContractCode != null">
        relet_contract_code,
      </if>
      <if test="attachContractCode != null">
        attach_contract_code,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderCode != null">
        order_code,
      </if>
      <if test="billType != null">
        bill_type,
      </if>
      <if test="adjustTotalAmount != null">
        adjust_total_amount,
      </if>
      <if test="reduceAmount != null">
        reduce_amount,
      </if>
      <if test="returnAmount != null">
        return_amount,
      </if>
      <if test="billAmount != null">
        bill_amount,
      </if>
      <if test="billRealAmount != null">
        bill_real_amount,
      </if>
      <if test="totalBillStage != null">
        total_bill_stage,
      </if>
      <if test="billStage != null">
        bill_stage,
      </if>
      <if test="billDate != null">
        bill_date,
      </if>
      <if test="billSdate != null">
        bill_sdate,
      </if>
      <if test="billEdate != null">
        bill_edate,
      </if>
      <if test="realBillEdate != null">
        real_bill_edate,
      </if>
      <if test="billStatus != null">
        bill_status,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="applyId != null">
        apply_id,
      </if>
      <if test="valid != null">
        valid,
      </if>
      <if test="firstBalanceTime != null">
        first_balance_time,
      </if>
      <if test="createId != null">
        create_id,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="remark != null">
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="billRelateCode != null">
        #{billRelateCode,jdbcType=VARCHAR},
      </if>
      <if test="billCode != null">
        #{billCode,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=INTEGER},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="contractId != null">
        #{contractId,jdbcType=INTEGER},
      </if>
      <if test="contractCode != null">
        #{contractCode,jdbcType=VARCHAR},
      </if>
      <if test="reletContractCode != null">
        #{reletContractCode,jdbcType=VARCHAR},
      </if>
      <if test="attachContractCode != null">
        #{attachContractCode,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderCode != null">
        #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="billType != null">
        #{billType,jdbcType=TINYINT},
      </if>
      <if test="adjustTotalAmount != null">
        #{adjustTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="reduceAmount != null">
        #{reduceAmount,jdbcType=DECIMAL},
      </if>
      <if test="returnAmount != null">
        #{returnAmount,jdbcType=DECIMAL},
      </if>
      <if test="billAmount != null">
        #{billAmount,jdbcType=DECIMAL},
      </if>
      <if test="billRealAmount != null">
        #{billRealAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalBillStage != null">
        #{totalBillStage,jdbcType=INTEGER},
      </if>
      <if test="billStage != null">
        #{billStage,jdbcType=INTEGER},
      </if>
      <if test="billDate != null">
        #{billDate,jdbcType=DATE},
      </if>
      <if test="billSdate != null">
        #{billSdate,jdbcType=DATE},
      </if>
      <if test="billEdate != null">
        #{billEdate,jdbcType=DATE},
      </if>
      <if test="realBillEdate != null">
        #{realBillEdate,jdbcType=DATE},
      </if>
      <if test="billStatus != null">
        #{billStatus,jdbcType=INTEGER},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="applyId != null">
        #{applyId,jdbcType=VARCHAR},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=BIT},
      </if>
      <if test="firstBalanceTime != null">
        #{firstBalanceTime,jdbcType=DATE},
      </if>
      <if test="createId != null">
        #{createId,jdbcType=VARCHAR},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.business.entity.LeaseOrderBill">
    update lease_order_bill
    <set>
      <if test="billRelateCode != null">
        bill_relate_code = #{billRelateCode,jdbcType=VARCHAR},
      </if>
      <if test="billCode != null">
        bill_code = #{billCode,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=INTEGER},
      </if>
      <if test="customerName != null">
        customer_name = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="contractId != null">
        contract_id = #{contractId,jdbcType=INTEGER},
      </if>
      <if test="contractCode != null">
        contract_code = #{contractCode,jdbcType=VARCHAR},
      </if>
      <if test="reletContractCode != null">
        relet_contract_code = #{reletContractCode,jdbcType=VARCHAR},
      </if>
      <if test="attachContractCode != null">
        attach_contract_code = #{attachContractCode,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderCode != null">
        order_code = #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="billType != null">
        bill_type = #{billType,jdbcType=TINYINT},
      </if>
      <if test="adjustTotalAmount != null">
        adjust_total_amount = #{adjustTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="reduceAmount != null">
        reduce_amount = #{reduceAmount,jdbcType=DECIMAL},
      </if>
      <if test="returnAmount != null">
        return_amount = #{returnAmount,jdbcType=DECIMAL},
      </if>
      <if test="billAmount != null">
        bill_amount = #{billAmount,jdbcType=DECIMAL},
      </if>
      <if test="billRealAmount != null">
        bill_real_amount = #{billRealAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalBillStage != null">
        total_bill_stage = #{totalBillStage,jdbcType=INTEGER},
      </if>
      <if test="billStage != null">
        bill_stage = #{billStage,jdbcType=INTEGER},
      </if>
      <if test="billDate != null">
        bill_date = #{billDate,jdbcType=DATE},
      </if>
      <if test="billSdate != null">
        bill_sdate = #{billSdate,jdbcType=DATE},
      </if>
      <if test="billEdate != null">
        bill_edate = #{billEdate,jdbcType=DATE},
      </if>
      <if test="realBillEdate != null">
        real_bill_edate = #{realBillEdate,jdbcType=DATE},
      </if>
      <if test="billStatus != null">
        bill_status = #{billStatus,jdbcType=INTEGER},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="applyId != null">
        apply_id = #{applyId,jdbcType=VARCHAR},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=BIT},
      </if>
      <if test="firstBalanceTime != null">
        first_balance_time = #{firstBalanceTime,jdbcType=DATE},
      </if>
      <if test="createId != null">
        create_id = #{createId,jdbcType=VARCHAR},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where bill_id = #{billId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.business.entity.LeaseOrderBill">
    update lease_order_bill
    set bill_relate_code = #{billRelateCode,jdbcType=VARCHAR},
      bill_code = #{billCode,jdbcType=VARCHAR},
      customer_id = #{customerId,jdbcType=INTEGER},
      customer_name = #{customerName,jdbcType=VARCHAR},
      contract_id = #{contractId,jdbcType=INTEGER},
      contract_code = #{contractCode,jdbcType=VARCHAR},
      relet_contract_code = #{reletContractCode,jdbcType=VARCHAR},
      attach_contract_code = #{attachContractCode,jdbcType=VARCHAR},
      order_id = #{orderId,jdbcType=INTEGER},
      order_code = #{orderCode,jdbcType=VARCHAR},
      bill_type = #{billType,jdbcType=TINYINT},
      adjust_total_amount = #{adjustTotalAmount,jdbcType=DECIMAL},
      reduce_amount = #{reduceAmount,jdbcType=DECIMAL},
      return_amount = #{returnAmount,jdbcType=DECIMAL},
      bill_amount = #{billAmount,jdbcType=DECIMAL},
      bill_real_amount = #{billRealAmount,jdbcType=DECIMAL},
      total_bill_stage = #{totalBillStage,jdbcType=INTEGER},
      bill_stage = #{billStage,jdbcType=INTEGER},
      bill_date = #{billDate,jdbcType=DATE},
      bill_sdate = #{billSdate,jdbcType=DATE},
      bill_edate = #{billEdate,jdbcType=DATE},
      real_bill_edate = #{realBillEdate,jdbcType=DATE},
      bill_status = #{billStatus,jdbcType=INTEGER},
      audit_status = #{auditStatus,jdbcType=INTEGER},
      apply_id = #{applyId,jdbcType=VARCHAR},
      valid = #{valid,jdbcType=BIT},
      first_balance_time = #{firstBalanceTime,jdbcType=DATE},
      create_id = #{createId,jdbcType=VARCHAR},
      create_name = #{createName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR}
    where bill_id = #{billId,jdbcType=INTEGER}
  </update>
</mapper>