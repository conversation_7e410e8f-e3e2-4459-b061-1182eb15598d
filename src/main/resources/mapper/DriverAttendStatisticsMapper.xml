<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.DriverAttendStatisticsMapper">
  <resultMap id="BaseResultMap" type="com.izu.business.entity.DriverAttendStatistics">
    <id column="statistics_id" jdbcType="INTEGER" property="statisticsId" />
    <result column="driver_id" jdbcType="INTEGER" property="driverId" />
    <result column="driver_name" jdbcType="VARCHAR" property="driverName" />
    <result column="driver_cell" jdbcType="VARCHAR" property="driverCell" />
    <result column="motorcade_id" jdbcType="INTEGER" property="motorcadeId" />
    <result column="motorcade_name" jdbcType="VARCHAR" property="motorcadeName" />
    <result column="attendance_month" jdbcType="VARCHAR" property="attendanceMonth" />
    <result column="should_attend_count" jdbcType="INTEGER" property="shouldAttendCount" />
    <result column="fact_attend_count" jdbcType="INTEGER" property="factAttendCount" />
    <result column="late_minutes" jdbcType="INTEGER" property="lateMinutes" />
    <result column="late_count" jdbcType="INTEGER" property="lateCount" />
    <result column="early_minutes" jdbcType="INTEGER" property="earlyMinutes" />
    <result column="early_leave_count" jdbcType="INTEGER" property="earlyLeaveCount" />
    <result column="miss_sign_count" jdbcType="INTEGER" property="missSignCount" />
    <result column="absent_count" jdbcType="INTEGER" property="absentCount" />
    <result column="out_sign_count" jdbcType="INTEGER" property="outSignCount" />
    <result column="statistics_type" jdbcType="INTEGER" property="statisticsType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    statistics_id, driver_id, driver_name, driver_cell, motorcade_id, motorcade_name, 
    attendance_month, should_attend_count, fact_attend_count, late_minutes, late_count, 
    early_minutes, early_leave_count, miss_sign_count, absent_count, out_sign_count, 
    statistics_type, create_time, update_time, company_id
  </sql>
  <select id="selectByExample" parameterType="com.izu.business.entity.DriverAttendStatisticsExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from driver_attend_statistics
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from driver_attend_statistics
    where statistics_id = #{statisticsId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from driver_attend_statistics
    where statistics_id = #{statisticsId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.business.entity.DriverAttendStatisticsExample">
    delete from driver_attend_statistics
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.business.entity.DriverAttendStatistics">
    insert into driver_attend_statistics (statistics_id, driver_id, driver_name, 
      driver_cell, motorcade_id, motorcade_name, 
      attendance_month, should_attend_count, fact_attend_count, 
      late_minutes, late_count, early_minutes, 
      early_leave_count, miss_sign_count, absent_count, 
      out_sign_count, statistics_type, create_time, 
      update_time, company_id)
    values (#{statisticsId,jdbcType=INTEGER}, #{driverId,jdbcType=INTEGER}, #{driverName,jdbcType=VARCHAR}, 
      #{driverCell,jdbcType=VARCHAR}, #{motorcadeId,jdbcType=INTEGER}, #{motorcadeName,jdbcType=VARCHAR}, 
      #{attendanceMonth,jdbcType=VARCHAR}, #{shouldAttendCount,jdbcType=INTEGER}, #{factAttendCount,jdbcType=INTEGER}, 
      #{lateMinutes,jdbcType=INTEGER}, #{lateCount,jdbcType=INTEGER}, #{earlyMinutes,jdbcType=INTEGER}, 
      #{earlyLeaveCount,jdbcType=INTEGER}, #{missSignCount,jdbcType=INTEGER}, #{absentCount,jdbcType=INTEGER}, 
      #{outSignCount,jdbcType=INTEGER}, #{statisticsType,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{companyId,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.business.entity.DriverAttendStatistics">
    insert into driver_attend_statistics
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="statisticsId != null">
        statistics_id,
      </if>
      <if test="driverId != null">
        driver_id,
      </if>
      <if test="driverName != null">
        driver_name,
      </if>
      <if test="driverCell != null">
        driver_cell,
      </if>
      <if test="motorcadeId != null">
        motorcade_id,
      </if>
      <if test="motorcadeName != null">
        motorcade_name,
      </if>
      <if test="attendanceMonth != null">
        attendance_month,
      </if>
      <if test="shouldAttendCount != null">
        should_attend_count,
      </if>
      <if test="factAttendCount != null">
        fact_attend_count,
      </if>
      <if test="lateMinutes != null">
        late_minutes,
      </if>
      <if test="lateCount != null">
        late_count,
      </if>
      <if test="earlyMinutes != null">
        early_minutes,
      </if>
      <if test="earlyLeaveCount != null">
        early_leave_count,
      </if>
      <if test="missSignCount != null">
        miss_sign_count,
      </if>
      <if test="absentCount != null">
        absent_count,
      </if>
      <if test="outSignCount != null">
        out_sign_count,
      </if>
      <if test="statisticsType != null">
        statistics_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="statisticsId != null">
        #{statisticsId,jdbcType=INTEGER},
      </if>
      <if test="driverId != null">
        #{driverId,jdbcType=INTEGER},
      </if>
      <if test="driverName != null">
        #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="driverCell != null">
        #{driverCell,jdbcType=VARCHAR},
      </if>
      <if test="motorcadeId != null">
        #{motorcadeId,jdbcType=INTEGER},
      </if>
      <if test="motorcadeName != null">
        #{motorcadeName,jdbcType=VARCHAR},
      </if>
      <if test="attendanceMonth != null">
        #{attendanceMonth,jdbcType=VARCHAR},
      </if>
      <if test="shouldAttendCount != null">
        #{shouldAttendCount,jdbcType=INTEGER},
      </if>
      <if test="factAttendCount != null">
        #{factAttendCount,jdbcType=INTEGER},
      </if>
      <if test="lateMinutes != null">
        #{lateMinutes,jdbcType=INTEGER},
      </if>
      <if test="lateCount != null">
        #{lateCount,jdbcType=INTEGER},
      </if>
      <if test="earlyMinutes != null">
        #{earlyMinutes,jdbcType=INTEGER},
      </if>
      <if test="earlyLeaveCount != null">
        #{earlyLeaveCount,jdbcType=INTEGER},
      </if>
      <if test="missSignCount != null">
        #{missSignCount,jdbcType=INTEGER},
      </if>
      <if test="absentCount != null">
        #{absentCount,jdbcType=INTEGER},
      </if>
      <if test="outSignCount != null">
        #{outSignCount,jdbcType=INTEGER},
      </if>
      <if test="statisticsType != null">
        #{statisticsType,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.business.entity.DriverAttendStatisticsExample" resultType="java.lang.Long">
    select count(*) from driver_attend_statistics
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update driver_attend_statistics
    <set>
      <if test="record.statisticsId != null">
        statistics_id = #{record.statisticsId,jdbcType=INTEGER},
      </if>
      <if test="record.driverId != null">
        driver_id = #{record.driverId,jdbcType=INTEGER},
      </if>
      <if test="record.driverName != null">
        driver_name = #{record.driverName,jdbcType=VARCHAR},
      </if>
      <if test="record.driverCell != null">
        driver_cell = #{record.driverCell,jdbcType=VARCHAR},
      </if>
      <if test="record.motorcadeId != null">
        motorcade_id = #{record.motorcadeId,jdbcType=INTEGER},
      </if>
      <if test="record.motorcadeName != null">
        motorcade_name = #{record.motorcadeName,jdbcType=VARCHAR},
      </if>
      <if test="record.attendanceMonth != null">
        attendance_month = #{record.attendanceMonth,jdbcType=VARCHAR},
      </if>
      <if test="record.shouldAttendCount != null">
        should_attend_count = #{record.shouldAttendCount,jdbcType=INTEGER},
      </if>
      <if test="record.factAttendCount != null">
        fact_attend_count = #{record.factAttendCount,jdbcType=INTEGER},
      </if>
      <if test="record.lateMinutes != null">
        late_minutes = #{record.lateMinutes,jdbcType=INTEGER},
      </if>
      <if test="record.lateCount != null">
        late_count = #{record.lateCount,jdbcType=INTEGER},
      </if>
      <if test="record.earlyMinutes != null">
        early_minutes = #{record.earlyMinutes,jdbcType=INTEGER},
      </if>
      <if test="record.earlyLeaveCount != null">
        early_leave_count = #{record.earlyLeaveCount,jdbcType=INTEGER},
      </if>
      <if test="record.missSignCount != null">
        miss_sign_count = #{record.missSignCount,jdbcType=INTEGER},
      </if>
      <if test="record.absentCount != null">
        absent_count = #{record.absentCount,jdbcType=INTEGER},
      </if>
      <if test="record.outSignCount != null">
        out_sign_count = #{record.outSignCount,jdbcType=INTEGER},
      </if>
      <if test="record.statisticsType != null">
        statistics_type = #{record.statisticsType,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update driver_attend_statistics
    set statistics_id = #{record.statisticsId,jdbcType=INTEGER},
      driver_id = #{record.driverId,jdbcType=INTEGER},
      driver_name = #{record.driverName,jdbcType=VARCHAR},
      driver_cell = #{record.driverCell,jdbcType=VARCHAR},
      motorcade_id = #{record.motorcadeId,jdbcType=INTEGER},
      motorcade_name = #{record.motorcadeName,jdbcType=VARCHAR},
      attendance_month = #{record.attendanceMonth,jdbcType=VARCHAR},
      should_attend_count = #{record.shouldAttendCount,jdbcType=INTEGER},
      fact_attend_count = #{record.factAttendCount,jdbcType=INTEGER},
      late_minutes = #{record.lateMinutes,jdbcType=INTEGER},
      late_count = #{record.lateCount,jdbcType=INTEGER},
      early_minutes = #{record.earlyMinutes,jdbcType=INTEGER},
      early_leave_count = #{record.earlyLeaveCount,jdbcType=INTEGER},
      miss_sign_count = #{record.missSignCount,jdbcType=INTEGER},
      absent_count = #{record.absentCount,jdbcType=INTEGER},
      out_sign_count = #{record.outSignCount,jdbcType=INTEGER},
      statistics_type = #{record.statisticsType,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      company_id = #{record.companyId,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.business.entity.DriverAttendStatistics">
    update driver_attend_statistics
    <set>
      <if test="driverId != null">
        driver_id = #{driverId,jdbcType=INTEGER},
      </if>
      <if test="driverName != null">
        driver_name = #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="driverCell != null">
        driver_cell = #{driverCell,jdbcType=VARCHAR},
      </if>
      <if test="motorcadeId != null">
        motorcade_id = #{motorcadeId,jdbcType=INTEGER},
      </if>
      <if test="motorcadeName != null">
        motorcade_name = #{motorcadeName,jdbcType=VARCHAR},
      </if>
      <if test="attendanceMonth != null">
        attendance_month = #{attendanceMonth,jdbcType=VARCHAR},
      </if>
      <if test="shouldAttendCount != null">
        should_attend_count = #{shouldAttendCount,jdbcType=INTEGER},
      </if>
      <if test="factAttendCount != null">
        fact_attend_count = #{factAttendCount,jdbcType=INTEGER},
      </if>
      <if test="lateMinutes != null">
        late_minutes = #{lateMinutes,jdbcType=INTEGER},
      </if>
      <if test="lateCount != null">
        late_count = #{lateCount,jdbcType=INTEGER},
      </if>
      <if test="earlyMinutes != null">
        early_minutes = #{earlyMinutes,jdbcType=INTEGER},
      </if>
      <if test="earlyLeaveCount != null">
        early_leave_count = #{earlyLeaveCount,jdbcType=INTEGER},
      </if>
      <if test="missSignCount != null">
        miss_sign_count = #{missSignCount,jdbcType=INTEGER},
      </if>
      <if test="absentCount != null">
        absent_count = #{absentCount,jdbcType=INTEGER},
      </if>
      <if test="outSignCount != null">
        out_sign_count = #{outSignCount,jdbcType=INTEGER},
      </if>
      <if test="statisticsType != null">
        statistics_type = #{statisticsType,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=INTEGER},
      </if>
    </set>
    where statistics_id = #{statisticsId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.business.entity.DriverAttendStatistics">
    update driver_attend_statistics
    set driver_id = #{driverId,jdbcType=INTEGER},
      driver_name = #{driverName,jdbcType=VARCHAR},
      driver_cell = #{driverCell,jdbcType=VARCHAR},
      motorcade_id = #{motorcadeId,jdbcType=INTEGER},
      motorcade_name = #{motorcadeName,jdbcType=VARCHAR},
      attendance_month = #{attendanceMonth,jdbcType=VARCHAR},
      should_attend_count = #{shouldAttendCount,jdbcType=INTEGER},
      fact_attend_count = #{factAttendCount,jdbcType=INTEGER},
      late_minutes = #{lateMinutes,jdbcType=INTEGER},
      late_count = #{lateCount,jdbcType=INTEGER},
      early_minutes = #{earlyMinutes,jdbcType=INTEGER},
      early_leave_count = #{earlyLeaveCount,jdbcType=INTEGER},
      miss_sign_count = #{missSignCount,jdbcType=INTEGER},
      absent_count = #{absentCount,jdbcType=INTEGER},
      out_sign_count = #{outSignCount,jdbcType=INTEGER},
      statistics_type = #{statisticsType,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      company_id = #{companyId,jdbcType=INTEGER}
    where statistics_id = #{statisticsId,jdbcType=INTEGER}
  </update>
</mapper>