<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.LeaseContractVehicleMapper">
  <resultMap id="BaseResultMap" type="com.izu.business.entity.LeaseContractVehicle">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="contract_id" jdbcType="INTEGER" property="contractId" />
    <result column="contract_code" jdbcType="VARCHAR" property="contractCode" />
    <result column="contract_type" jdbcType="INTEGER" property="contractType" />
    <result column="buy_car_date" jdbcType="VARCHAR" property="buyCarDate" />
    <result column="vehicle_numbers" jdbcType="INTEGER" property="vehicleNumbers" />
    <result column="lease_period" jdbcType="INTEGER" property="leasePeriod" />
    <result column="month_rent" jdbcType="DECIMAL" property="monthRent" />
    <result column="deposit_amount" jdbcType="VARCHAR" property="depositAmount" />
    <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode" />
    <result column="vehicle_model_name" jdbcType="VARCHAR" property="vehicleModelName" />
    <result column="configpack_code" jdbcType="VARCHAR" property="configpackCode" />
    <result column="configpack_name" jdbcType="VARCHAR" property="configpackName" />
    <result column="vehicle_city_code" jdbcType="VARCHAR" property="vehicleCityCode" />
    <result column="vehicle_city_name" jdbcType="VARCHAR" property="vehicleCityName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, contract_id, contract_code, contract_type, buy_car_date, vehicle_numbers, lease_period, 
    month_rent, deposit_amount, vehicle_model_code, vehicle_model_name, configpack_code, 
    configpack_name, vehicle_city_code, vehicle_city_name
  </sql>
  <select id="selectByExample" parameterType="com.izu.business.entity.LeaseContractVehicleExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lease_contract_vehicle
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lease_contract_vehicle
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from lease_contract_vehicle
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.business.entity.LeaseContractVehicleExample">
    delete from lease_contract_vehicle
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.business.entity.LeaseContractVehicle">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lease_contract_vehicle (contract_id, contract_code, contract_type, 
      buy_car_date, vehicle_numbers, lease_period, 
      month_rent, deposit_amount, vehicle_model_code, 
      vehicle_model_name, configpack_code, configpack_name, 
      vehicle_city_code, vehicle_city_name)
    values (#{contractId,jdbcType=INTEGER}, #{contractCode,jdbcType=VARCHAR}, #{contractType,jdbcType=INTEGER}, 
      #{buyCarDate,jdbcType=VARCHAR}, #{vehicleNumbers,jdbcType=INTEGER}, #{leasePeriod,jdbcType=INTEGER}, 
      #{monthRent,jdbcType=DECIMAL}, #{depositAmount,jdbcType=VARCHAR}, #{vehicleModelCode,jdbcType=VARCHAR}, 
      #{vehicleModelName,jdbcType=VARCHAR}, #{configpackCode,jdbcType=VARCHAR}, #{configpackName,jdbcType=VARCHAR}, 
      #{vehicleCityCode,jdbcType=VARCHAR}, #{vehicleCityName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.business.entity.LeaseContractVehicle">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lease_contract_vehicle
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="contractId != null">
        contract_id,
      </if>
      <if test="contractCode != null">
        contract_code,
      </if>
      <if test="contractType != null">
        contract_type,
      </if>
      <if test="buyCarDate != null">
        buy_car_date,
      </if>
      <if test="vehicleNumbers != null">
        vehicle_numbers,
      </if>
      <if test="leasePeriod != null">
        lease_period,
      </if>
      <if test="monthRent != null">
        month_rent,
      </if>
      <if test="depositAmount != null">
        deposit_amount,
      </if>
      <if test="vehicleModelCode != null">
        vehicle_model_code,
      </if>
      <if test="vehicleModelName != null">
        vehicle_model_name,
      </if>
      <if test="configpackCode != null">
        configpack_code,
      </if>
      <if test="configpackName != null">
        configpack_name,
      </if>
      <if test="vehicleCityCode != null">
        vehicle_city_code,
      </if>
      <if test="vehicleCityName != null">
        vehicle_city_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="contractId != null">
        #{contractId,jdbcType=INTEGER},
      </if>
      <if test="contractCode != null">
        #{contractCode,jdbcType=VARCHAR},
      </if>
      <if test="contractType != null">
        #{contractType,jdbcType=INTEGER},
      </if>
      <if test="buyCarDate != null">
        #{buyCarDate,jdbcType=VARCHAR},
      </if>
      <if test="vehicleNumbers != null">
        #{vehicleNumbers,jdbcType=INTEGER},
      </if>
      <if test="leasePeriod != null">
        #{leasePeriod,jdbcType=INTEGER},
      </if>
      <if test="monthRent != null">
        #{monthRent,jdbcType=DECIMAL},
      </if>
      <if test="depositAmount != null">
        #{depositAmount,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelCode != null">
        #{vehicleModelCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelName != null">
        #{vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="configpackCode != null">
        #{configpackCode,jdbcType=VARCHAR},
      </if>
      <if test="configpackName != null">
        #{configpackName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleCityCode != null">
        #{vehicleCityCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleCityName != null">
        #{vehicleCityName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.business.entity.LeaseContractVehicleExample" resultType="java.lang.Long">
    select count(*) from lease_contract_vehicle
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lease_contract_vehicle
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.contractId != null">
        contract_id = #{record.contractId,jdbcType=INTEGER},
      </if>
      <if test="record.contractCode != null">
        contract_code = #{record.contractCode,jdbcType=VARCHAR},
      </if>
      <if test="record.contractType != null">
        contract_type = #{record.contractType,jdbcType=INTEGER},
      </if>
      <if test="record.buyCarDate != null">
        buy_car_date = #{record.buyCarDate,jdbcType=VARCHAR},
      </if>
      <if test="record.vehicleNumbers != null">
        vehicle_numbers = #{record.vehicleNumbers,jdbcType=INTEGER},
      </if>
      <if test="record.leasePeriod != null">
        lease_period = #{record.leasePeriod,jdbcType=INTEGER},
      </if>
      <if test="record.monthRent != null">
        month_rent = #{record.monthRent,jdbcType=DECIMAL},
      </if>
      <if test="record.depositAmount != null">
        deposit_amount = #{record.depositAmount,jdbcType=VARCHAR},
      </if>
      <if test="record.vehicleModelCode != null">
        vehicle_model_code = #{record.vehicleModelCode,jdbcType=VARCHAR},
      </if>
      <if test="record.vehicleModelName != null">
        vehicle_model_name = #{record.vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="record.configpackCode != null">
        configpack_code = #{record.configpackCode,jdbcType=VARCHAR},
      </if>
      <if test="record.configpackName != null">
        configpack_name = #{record.configpackName,jdbcType=VARCHAR},
      </if>
      <if test="record.vehicleCityCode != null">
        vehicle_city_code = #{record.vehicleCityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.vehicleCityName != null">
        vehicle_city_name = #{record.vehicleCityName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lease_contract_vehicle
    set id = #{record.id,jdbcType=INTEGER},
      contract_id = #{record.contractId,jdbcType=INTEGER},
      contract_code = #{record.contractCode,jdbcType=VARCHAR},
      contract_type = #{record.contractType,jdbcType=INTEGER},
      buy_car_date = #{record.buyCarDate,jdbcType=VARCHAR},
      vehicle_numbers = #{record.vehicleNumbers,jdbcType=INTEGER},
      lease_period = #{record.leasePeriod,jdbcType=INTEGER},
      month_rent = #{record.monthRent,jdbcType=DECIMAL},
      deposit_amount = #{record.depositAmount,jdbcType=VARCHAR},
      vehicle_model_code = #{record.vehicleModelCode,jdbcType=VARCHAR},
      vehicle_model_name = #{record.vehicleModelName,jdbcType=VARCHAR},
      configpack_code = #{record.configpackCode,jdbcType=VARCHAR},
      configpack_name = #{record.configpackName,jdbcType=VARCHAR},
      vehicle_city_code = #{record.vehicleCityCode,jdbcType=VARCHAR},
      vehicle_city_name = #{record.vehicleCityName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.business.entity.LeaseContractVehicle">
    update lease_contract_vehicle
    <set>
      <if test="contractId != null">
        contract_id = #{contractId,jdbcType=INTEGER},
      </if>
      <if test="contractCode != null">
        contract_code = #{contractCode,jdbcType=VARCHAR},
      </if>
      <if test="contractType != null">
        contract_type = #{contractType,jdbcType=INTEGER},
      </if>
      <if test="buyCarDate != null">
        buy_car_date = #{buyCarDate,jdbcType=VARCHAR},
      </if>
      <if test="vehicleNumbers != null">
        vehicle_numbers = #{vehicleNumbers,jdbcType=INTEGER},
      </if>
      <if test="leasePeriod != null">
        lease_period = #{leasePeriod,jdbcType=INTEGER},
      </if>
      <if test="monthRent != null">
        month_rent = #{monthRent,jdbcType=DECIMAL},
      </if>
      <if test="depositAmount != null">
        deposit_amount = #{depositAmount,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelCode != null">
        vehicle_model_code = #{vehicleModelCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelName != null">
        vehicle_model_name = #{vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="configpackCode != null">
        configpack_code = #{configpackCode,jdbcType=VARCHAR},
      </if>
      <if test="configpackName != null">
        configpack_name = #{configpackName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleCityCode != null">
        vehicle_city_code = #{vehicleCityCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleCityName != null">
        vehicle_city_name = #{vehicleCityName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.business.entity.LeaseContractVehicle">
    update lease_contract_vehicle
    set contract_id = #{contractId,jdbcType=INTEGER},
      contract_code = #{contractCode,jdbcType=VARCHAR},
      contract_type = #{contractType,jdbcType=INTEGER},
      buy_car_date = #{buyCarDate,jdbcType=VARCHAR},
      vehicle_numbers = #{vehicleNumbers,jdbcType=INTEGER},
      lease_period = #{leasePeriod,jdbcType=INTEGER},
      month_rent = #{monthRent,jdbcType=DECIMAL},
      deposit_amount = #{depositAmount,jdbcType=VARCHAR},
      vehicle_model_code = #{vehicleModelCode,jdbcType=VARCHAR},
      vehicle_model_name = #{vehicleModelName,jdbcType=VARCHAR},
      configpack_code = #{configpackCode,jdbcType=VARCHAR},
      configpack_name = #{configpackName,jdbcType=VARCHAR},
      vehicle_city_code = #{vehicleCityCode,jdbcType=VARCHAR},
      vehicle_city_name = #{vehicleCityName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>