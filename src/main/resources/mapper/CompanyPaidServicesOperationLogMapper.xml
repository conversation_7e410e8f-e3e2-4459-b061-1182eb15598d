<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.CompanyPaidServicesOperationLogMapper">
  <resultMap id="BaseResultMap" type="com.izu.user.entity.CompanyPaidServicesOperationLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="paid_service_code" jdbcType="VARCHAR" property="paidServiceCode" />
    <result column="operation_description" jdbcType="VARCHAR" property="operationDescription" />
    <result column="operation_time" jdbcType="TIMESTAMP" property="operationTime" />
    <result column="operator_id" jdbcType="INTEGER" property="operatorId" />
    <result column="operator_code" jdbcType="VARCHAR" property="operatorCode" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="operator_mobile" jdbcType="VARCHAR" property="operatorMobile" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, paid_service_code, operation_description, operation_time, operator_id, operator_code, 
    operator_name, operator_mobile, create_time, order_no
  </sql>
  <select id="selectByExample" parameterType="com.izu.user.entity.CompanyPaidServicesOperationLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from company_paid_services_operation_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from company_paid_services_operation_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from company_paid_services_operation_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.user.entity.CompanyPaidServicesOperationLogExample">
    delete from company_paid_services_operation_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.user.entity.CompanyPaidServicesOperationLog">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into company_paid_services_operation_log (paid_service_code, operation_description, 
      operation_time, operator_id, operator_code, 
      operator_name, operator_mobile, create_time, 
      order_no)
    values (#{paidServiceCode,jdbcType=VARCHAR}, #{operationDescription,jdbcType=VARCHAR}, 
      #{operationTime,jdbcType=TIMESTAMP}, #{operatorId,jdbcType=INTEGER}, #{operatorCode,jdbcType=VARCHAR}, 
      #{operatorName,jdbcType=VARCHAR}, #{operatorMobile,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{orderNo,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.user.entity.CompanyPaidServicesOperationLog">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into company_paid_services_operation_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="paidServiceCode != null">
        paid_service_code,
      </if>
      <if test="operationDescription != null">
        operation_description,
      </if>
      <if test="operationTime != null">
        operation_time,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="operatorCode != null">
        operator_code,
      </if>
      <if test="operatorName != null">
        operator_name,
      </if>
      <if test="operatorMobile != null">
        operator_mobile,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="paidServiceCode != null">
        #{paidServiceCode,jdbcType=VARCHAR},
      </if>
      <if test="operationDescription != null">
        #{operationDescription,jdbcType=VARCHAR},
      </if>
      <if test="operationTime != null">
        #{operationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=INTEGER},
      </if>
      <if test="operatorCode != null">
        #{operatorCode,jdbcType=VARCHAR},
      </if>
      <if test="operatorName != null">
        #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="operatorMobile != null">
        #{operatorMobile,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.user.entity.CompanyPaidServicesOperationLogExample" resultType="java.lang.Long">
    select count(*) from company_paid_services_operation_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update company_paid_services_operation_log
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.paidServiceCode != null">
        paid_service_code = #{row.paidServiceCode,jdbcType=VARCHAR},
      </if>
      <if test="row.operationDescription != null">
        operation_description = #{row.operationDescription,jdbcType=VARCHAR},
      </if>
      <if test="row.operationTime != null">
        operation_time = #{row.operationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.operatorId != null">
        operator_id = #{row.operatorId,jdbcType=INTEGER},
      </if>
      <if test="row.operatorCode != null">
        operator_code = #{row.operatorCode,jdbcType=VARCHAR},
      </if>
      <if test="row.operatorName != null">
        operator_name = #{row.operatorName,jdbcType=VARCHAR},
      </if>
      <if test="row.operatorMobile != null">
        operator_mobile = #{row.operatorMobile,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.orderNo != null">
        order_no = #{row.orderNo,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update company_paid_services_operation_log
    set id = #{row.id,jdbcType=BIGINT},
      paid_service_code = #{row.paidServiceCode,jdbcType=VARCHAR},
      operation_description = #{row.operationDescription,jdbcType=VARCHAR},
      operation_time = #{row.operationTime,jdbcType=TIMESTAMP},
      operator_id = #{row.operatorId,jdbcType=INTEGER},
      operator_code = #{row.operatorCode,jdbcType=VARCHAR},
      operator_name = #{row.operatorName,jdbcType=VARCHAR},
      operator_mobile = #{row.operatorMobile,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      order_no = #{row.orderNo,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.user.entity.CompanyPaidServicesOperationLog">
    update company_paid_services_operation_log
    <set>
      <if test="paidServiceCode != null">
        paid_service_code = #{paidServiceCode,jdbcType=VARCHAR},
      </if>
      <if test="operationDescription != null">
        operation_description = #{operationDescription,jdbcType=VARCHAR},
      </if>
      <if test="operationTime != null">
        operation_time = #{operationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=INTEGER},
      </if>
      <if test="operatorCode != null">
        operator_code = #{operatorCode,jdbcType=VARCHAR},
      </if>
      <if test="operatorName != null">
        operator_name = #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="operatorMobile != null">
        operator_mobile = #{operatorMobile,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.user.entity.CompanyPaidServicesOperationLog">
    update company_paid_services_operation_log
    set paid_service_code = #{paidServiceCode,jdbcType=VARCHAR},
      operation_description = #{operationDescription,jdbcType=VARCHAR},
      operation_time = #{operationTime,jdbcType=TIMESTAMP},
      operator_id = #{operatorId,jdbcType=INTEGER},
      operator_code = #{operatorCode,jdbcType=VARCHAR},
      operator_name = #{operatorName,jdbcType=VARCHAR},
      operator_mobile = #{operatorMobile,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      order_no = #{orderNo,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>