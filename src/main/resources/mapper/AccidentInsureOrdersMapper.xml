<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.AccidentInsureOrdersMapper">
  <resultMap id="BaseResultMap" type="com.izu.business.entity.AccidentInsureOrders">
    <id column="insure_id" jdbcType="BIGINT" property="insureId" />
    <result column="accident_no" jdbcType="VARCHAR" property="accidentNo" />
    <result column="insure_no" jdbcType="VARCHAR" property="insureNo" />
    <result column="report_number" jdbcType="VARCHAR" property="reportNumber" />
    <result column="order_status" jdbcType="TINYINT" property="orderStatus" />
    <result column="report_time" jdbcType="TIMESTAMP" property="reportTime" />
    <result column="vehicle_vin" jdbcType="VARCHAR" property="vehicleVin" />
    <result column="belong_buss_code" jdbcType="VARCHAR" property="belongBussCode" />
    <result column="belong_buss_name" jdbcType="VARCHAR" property="belongBussName" />
    <result column="operate_buss_code" jdbcType="VARCHAR" property="operateBussCode" />
    <result column="operate_buss_name" jdbcType="VARCHAR" property="operateBussName" />
    <result column="insure_company_code" jdbcType="VARCHAR" property="insureCompanyCode" />
    <result column="insure_company_name" jdbcType="VARCHAR" property="insureCompanyName" />
    <result column="create_id" jdbcType="INTEGER" property="createId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_dept_id" jdbcType="INTEGER" property="createDeptId" />
    <result column="create_dept_name" jdbcType="VARCHAR" property="createDeptName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_id" jdbcType="INTEGER" property="updateId" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    insure_id, accident_no, insure_no, report_number, order_status, report_time, vehicle_vin, 
    belong_buss_code, belong_buss_name, operate_buss_code, operate_buss_name, insure_company_code, 
    insure_company_name, create_id, create_name, create_dept_id, create_dept_name, create_time, 
    update_id, update_name, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from accident_insure_orders
    where insure_id = #{insureId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from accident_insure_orders
    where insure_id = #{insureId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.izu.business.entity.AccidentInsureOrders">
    <selectKey keyProperty="insureId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into accident_insure_orders (accident_no, insure_no, report_number, 
      order_status, report_time, vehicle_vin, 
      belong_buss_code, belong_buss_name, operate_buss_code, 
      operate_buss_name, insure_company_code, insure_company_name, 
      create_id, create_name, create_dept_id, 
      create_dept_name, create_time, update_id, 
      update_name, update_time)
    values (#{accidentNo,jdbcType=VARCHAR}, #{insureNo,jdbcType=VARCHAR}, #{reportNumber,jdbcType=VARCHAR}, 
      #{orderStatus,jdbcType=TINYINT}, #{reportTime,jdbcType=TIMESTAMP}, #{vehicleVin,jdbcType=VARCHAR}, 
      #{belongBussCode,jdbcType=VARCHAR}, #{belongBussName,jdbcType=VARCHAR}, #{operateBussCode,jdbcType=VARCHAR}, 
      #{operateBussName,jdbcType=VARCHAR}, #{insureCompanyCode,jdbcType=VARCHAR}, #{insureCompanyName,jdbcType=VARCHAR}, 
      #{createId,jdbcType=INTEGER}, #{createName,jdbcType=VARCHAR}, #{createDeptId,jdbcType=INTEGER}, 
      #{createDeptName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateId,jdbcType=INTEGER}, 
      #{updateName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.business.entity.AccidentInsureOrders">
    <selectKey keyProperty="insureId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into accident_insure_orders
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accidentNo != null">
        accident_no,
      </if>
      <if test="insureNo != null">
        insure_no,
      </if>
      <if test="reportNumber != null">
        report_number,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="reportTime != null">
        report_time,
      </if>
      <if test="vehicleVin != null">
        vehicle_vin,
      </if>
      <if test="belongBussCode != null">
        belong_buss_code,
      </if>
      <if test="belongBussName != null">
        belong_buss_name,
      </if>
      <if test="operateBussCode != null">
        operate_buss_code,
      </if>
      <if test="operateBussName != null">
        operate_buss_name,
      </if>
      <if test="insureCompanyCode != null">
        insure_company_code,
      </if>
      <if test="insureCompanyName != null">
        insure_company_name,
      </if>
      <if test="createId != null">
        create_id,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createDeptId != null">
        create_dept_id,
      </if>
      <if test="createDeptName != null">
        create_dept_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateId != null">
        update_id,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accidentNo != null">
        #{accidentNo,jdbcType=VARCHAR},
      </if>
      <if test="insureNo != null">
        #{insureNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNumber != null">
        #{reportNumber,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=TINYINT},
      </if>
      <if test="reportTime != null">
        #{reportTime,jdbcType=TIMESTAMP},
      </if>
      <if test="vehicleVin != null">
        #{vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="belongBussCode != null">
        #{belongBussCode,jdbcType=VARCHAR},
      </if>
      <if test="belongBussName != null">
        #{belongBussName,jdbcType=VARCHAR},
      </if>
      <if test="operateBussCode != null">
        #{operateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="operateBussName != null">
        #{operateBussName,jdbcType=VARCHAR},
      </if>
      <if test="insureCompanyCode != null">
        #{insureCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="insureCompanyName != null">
        #{insureCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="createId != null">
        #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createDeptId != null">
        #{createDeptId,jdbcType=INTEGER},
      </if>
      <if test="createDeptName != null">
        #{createDeptName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.business.entity.AccidentInsureOrders">
    update accident_insure_orders
    <set>
      <if test="accidentNo != null">
        accident_no = #{accidentNo,jdbcType=VARCHAR},
      </if>
      <if test="insureNo != null">
        insure_no = #{insureNo,jdbcType=VARCHAR},
      </if>
      <if test="reportNumber != null">
        report_number = #{reportNumber,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=TINYINT},
      </if>
      <if test="reportTime != null">
        report_time = #{reportTime,jdbcType=TIMESTAMP},
      </if>
      <if test="vehicleVin != null">
        vehicle_vin = #{vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="belongBussCode != null">
        belong_buss_code = #{belongBussCode,jdbcType=VARCHAR},
      </if>
      <if test="belongBussName != null">
        belong_buss_name = #{belongBussName,jdbcType=VARCHAR},
      </if>
      <if test="operateBussCode != null">
        operate_buss_code = #{operateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="operateBussName != null">
        operate_buss_name = #{operateBussName,jdbcType=VARCHAR},
      </if>
      <if test="insureCompanyCode != null">
        insure_company_code = #{insureCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="insureCompanyName != null">
        insure_company_name = #{insureCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="createId != null">
        create_id = #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createDeptId != null">
        create_dept_id = #{createDeptId,jdbcType=INTEGER},
      </if>
      <if test="createDeptName != null">
        create_dept_name = #{createDeptName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        update_id = #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where insure_id = #{insureId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.business.entity.AccidentInsureOrders">
    update accident_insure_orders
    set accident_no = #{accidentNo,jdbcType=VARCHAR},
      insure_no = #{insureNo,jdbcType=VARCHAR},
      report_number = #{reportNumber,jdbcType=VARCHAR},
      order_status = #{orderStatus,jdbcType=TINYINT},
      report_time = #{reportTime,jdbcType=TIMESTAMP},
      vehicle_vin = #{vehicleVin,jdbcType=VARCHAR},
      belong_buss_code = #{belongBussCode,jdbcType=VARCHAR},
      belong_buss_name = #{belongBussName,jdbcType=VARCHAR},
      operate_buss_code = #{operateBussCode,jdbcType=VARCHAR},
      operate_buss_name = #{operateBussName,jdbcType=VARCHAR},
      insure_company_code = #{insureCompanyCode,jdbcType=VARCHAR},
      insure_company_name = #{insureCompanyName,jdbcType=VARCHAR},
      create_id = #{createId,jdbcType=INTEGER},
      create_name = #{createName,jdbcType=VARCHAR},
      create_dept_id = #{createDeptId,jdbcType=INTEGER},
      create_dept_name = #{createDeptName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_id = #{updateId,jdbcType=INTEGER},
      update_name = #{updateName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where insure_id = #{insureId,jdbcType=BIGINT}
  </update>
</mapper>