<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.MotorcadeSchedulingDetailMapper">
  <resultMap id="BaseResultMap" type="com.izu.user.entity.MotorcadeSchedulingDetail">
    <!--@mbg.generated-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="motorcade_id" jdbcType="INTEGER" property="motorcadeId" />
    <result column="driver_id" jdbcType="INTEGER" property="driverId" />
    <result column="date_time" jdbcType="DATE" property="dateTime" />
    <result column="scheduling_no" jdbcType="VARCHAR" property="schedulingNo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_id" jdbcType="INTEGER" property="createId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="update_id" jdbcType="INTEGER" property="updateId" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, company_id, motorcade_id, driver_id, date_time, scheduling_no, create_time, create_id, 
    create_name, update_id, update_name, update_time, start_time, end_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from motorcade_scheduling_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from motorcade_scheduling_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.izu.user.entity.MotorcadeSchedulingDetail" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into motorcade_scheduling_detail (company_id, motorcade_id, driver_id, 
      date_time, scheduling_no, create_time, 
      create_id, create_name, update_id, 
      update_name, update_time, start_time, 
      end_time)
    values (#{companyId,jdbcType=INTEGER}, #{motorcadeId,jdbcType=INTEGER}, #{driverId,jdbcType=INTEGER}, 
      #{dateTime,jdbcType=DATE}, #{schedulingNo,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{createId,jdbcType=INTEGER}, #{createName,jdbcType=VARCHAR}, #{updateId,jdbcType=INTEGER}, 
      #{updateName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{startTime,jdbcType=TIMESTAMP}, 
      #{endTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.izu.user.entity.MotorcadeSchedulingDetail" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into motorcade_scheduling_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="motorcadeId != null">
        motorcade_id,
      </if>
      <if test="driverId != null">
        driver_id,
      </if>
      <if test="dateTime != null">
        date_time,
      </if>
      <if test="schedulingNo != null">
        scheduling_no,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createId != null">
        create_id,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="updateId != null">
        update_id,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="motorcadeId != null">
        #{motorcadeId,jdbcType=INTEGER},
      </if>
      <if test="driverId != null">
        #{driverId,jdbcType=INTEGER},
      </if>
      <if test="dateTime != null">
        #{dateTime,jdbcType=DATE},
      </if>
      <if test="schedulingNo != null">
        #{schedulingNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createId != null">
        #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="updateId != null">
        #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.user.entity.MotorcadeSchedulingDetail">
    <!--@mbg.generated-->
    update motorcade_scheduling_detail
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="motorcadeId != null">
        motorcade_id = #{motorcadeId,jdbcType=INTEGER},
      </if>
      <if test="driverId != null">
        driver_id = #{driverId,jdbcType=INTEGER},
      </if>
      <if test="dateTime != null">
        date_time = #{dateTime,jdbcType=DATE},
      </if>
      <if test="schedulingNo != null">
        scheduling_no = #{schedulingNo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createId != null">
        create_id = #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="updateId != null">
        update_id = #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.user.entity.MotorcadeSchedulingDetail">
    <!--@mbg.generated-->
    update motorcade_scheduling_detail
    set company_id = #{companyId,jdbcType=INTEGER},
      motorcade_id = #{motorcadeId,jdbcType=INTEGER},
      driver_id = #{driverId,jdbcType=INTEGER},
      date_time = #{dateTime,jdbcType=DATE},
      scheduling_no = #{schedulingNo,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_id = #{createId,jdbcType=INTEGER},
      create_name = #{createName,jdbcType=VARCHAR},
      update_id = #{updateId,jdbcType=INTEGER},
      update_name = #{updateName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      start_time = #{startTime,jdbcType=TIMESTAMP},
      end_time = #{endTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>