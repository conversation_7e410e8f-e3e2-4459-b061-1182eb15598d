<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.StatisGeneralMonthMapper">
  <resultMap id="BaseResultMap" type="com.izu.business.entity.StatisGeneralMonth">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="statis_date" jdbcType="VARCHAR" property="statisDate" />
    <result column="city_code" jdbcType="INTEGER" property="cityCode" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="receivable_amount" jdbcType="DECIMAL" property="receivableAmount" />
    <result column="preferential_amount" jdbcType="DECIMAL" property="preferentialAmount" />
    <result column="waiver_amount" jdbcType="DECIMAL" property="waiverAmount" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="create_order_num" jdbcType="INTEGER" property="createOrderNum" />
    <result column="completed_order_num" jdbcType="INTEGER" property="completedOrderNum" />
    <result column="rented_car_num" jdbcType="INTEGER" property="rentedCarNum" />
    <result column="total_car_num" jdbcType="INTEGER" property="totalCarNum" />
    <result column="rented_rate" jdbcType="DECIMAL" property="rentedRate" />
    <result column="total_mileage" jdbcType="DECIMAL" property="totalMileage" />
    <result column="effective_mileage" jdbcType="DECIMAL" property="effectiveMileage" />
    <result column="pick_up_station" jdbcType="INTEGER" property="pickUpStation" />
    <result column="pick_up_airport" jdbcType="INTEGER" property="pickUpAirport" />
    <result column="whole_day" jdbcType="INTEGER" property="wholeDay" />
    <result column="half_day" jdbcType="INTEGER" property="halfDay" />
    <result column="single_time" jdbcType="INTEGER" property="singleTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, statis_date, city_code, city_name, receivable_amount, preferential_amount, waiver_amount, 
    total_amount, create_order_num, completed_order_num, rented_car_num, total_car_num, 
    rented_rate, total_mileage, effective_mileage, pick_up_station, pick_up_airport, 
    whole_day, half_day, single_time, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from statis_general_month
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from statis_general_month
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.business.entity.StatisGeneralMonth">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into statis_general_month (statis_date, city_code, city_name, 
      receivable_amount, preferential_amount, waiver_amount, 
      total_amount, create_order_num, completed_order_num, 
      rented_car_num, total_car_num, rented_rate, 
      total_mileage, effective_mileage, pick_up_station, 
      pick_up_airport, whole_day, half_day, 
      single_time, create_time, update_time
      )
    values (#{statisDate,jdbcType=VARCHAR}, #{cityCode,jdbcType=INTEGER}, #{cityName,jdbcType=VARCHAR}, 
      #{receivableAmount,jdbcType=DECIMAL}, #{preferentialAmount,jdbcType=DECIMAL}, #{waiverAmount,jdbcType=DECIMAL}, 
      #{totalAmount,jdbcType=DECIMAL}, #{createOrderNum,jdbcType=INTEGER}, #{completedOrderNum,jdbcType=INTEGER}, 
      #{rentedCarNum,jdbcType=INTEGER}, #{totalCarNum,jdbcType=INTEGER}, #{rentedRate,jdbcType=DECIMAL}, 
      #{totalMileage,jdbcType=DECIMAL}, #{effectiveMileage,jdbcType=DECIMAL}, #{pickUpStation,jdbcType=INTEGER}, 
      #{pickUpAirport,jdbcType=INTEGER}, #{wholeDay,jdbcType=INTEGER}, #{halfDay,jdbcType=INTEGER}, 
      #{singleTime,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.business.entity.StatisGeneralMonth">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into statis_general_month
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="statisDate != null">
        statis_date,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="cityName != null">
        city_name,
      </if>
      <if test="receivableAmount != null">
        receivable_amount,
      </if>
      <if test="preferentialAmount != null">
        preferential_amount,
      </if>
      <if test="waiverAmount != null">
        waiver_amount,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="createOrderNum != null">
        create_order_num,
      </if>
      <if test="completedOrderNum != null">
        completed_order_num,
      </if>
      <if test="rentedCarNum != null">
        rented_car_num,
      </if>
      <if test="totalCarNum != null">
        total_car_num,
      </if>
      <if test="rentedRate != null">
        rented_rate,
      </if>
      <if test="totalMileage != null">
        total_mileage,
      </if>
      <if test="effectiveMileage != null">
        effective_mileage,
      </if>
      <if test="pickUpStation != null">
        pick_up_station,
      </if>
      <if test="pickUpAirport != null">
        pick_up_airport,
      </if>
      <if test="wholeDay != null">
        whole_day,
      </if>
      <if test="halfDay != null">
        half_day,
      </if>
      <if test="singleTime != null">
        single_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="statisDate != null">
        #{statisDate,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=INTEGER},
      </if>
      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="receivableAmount != null">
        #{receivableAmount,jdbcType=DECIMAL},
      </if>
      <if test="preferentialAmount != null">
        #{preferentialAmount,jdbcType=DECIMAL},
      </if>
      <if test="waiverAmount != null">
        #{waiverAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="createOrderNum != null">
        #{createOrderNum,jdbcType=INTEGER},
      </if>
      <if test="completedOrderNum != null">
        #{completedOrderNum,jdbcType=INTEGER},
      </if>
      <if test="rentedCarNum != null">
        #{rentedCarNum,jdbcType=INTEGER},
      </if>
      <if test="totalCarNum != null">
        #{totalCarNum,jdbcType=INTEGER},
      </if>
      <if test="rentedRate != null">
        #{rentedRate,jdbcType=DECIMAL},
      </if>
      <if test="totalMileage != null">
        #{totalMileage,jdbcType=DECIMAL},
      </if>
      <if test="effectiveMileage != null">
        #{effectiveMileage,jdbcType=DECIMAL},
      </if>
      <if test="pickUpStation != null">
        #{pickUpStation,jdbcType=INTEGER},
      </if>
      <if test="pickUpAirport != null">
        #{pickUpAirport,jdbcType=INTEGER},
      </if>
      <if test="wholeDay != null">
        #{wholeDay,jdbcType=INTEGER},
      </if>
      <if test="halfDay != null">
        #{halfDay,jdbcType=INTEGER},
      </if>
      <if test="singleTime != null">
        #{singleTime,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.business.entity.StatisGeneralMonth">
    update statis_general_month
    <set>
      <if test="statisDate != null">
        statis_date = #{statisDate,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        city_code = #{cityCode,jdbcType=INTEGER},
      </if>
      <if test="cityName != null">
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="receivableAmount != null">
        receivable_amount = #{receivableAmount,jdbcType=DECIMAL},
      </if>
      <if test="preferentialAmount != null">
        preferential_amount = #{preferentialAmount,jdbcType=DECIMAL},
      </if>
      <if test="waiverAmount != null">
        waiver_amount = #{waiverAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalAmount != null">
        total_amount = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="createOrderNum != null">
        create_order_num = #{createOrderNum,jdbcType=INTEGER},
      </if>
      <if test="completedOrderNum != null">
        completed_order_num = #{completedOrderNum,jdbcType=INTEGER},
      </if>
      <if test="rentedCarNum != null">
        rented_car_num = #{rentedCarNum,jdbcType=INTEGER},
      </if>
      <if test="totalCarNum != null">
        total_car_num = #{totalCarNum,jdbcType=INTEGER},
      </if>
      <if test="rentedRate != null">
        rented_rate = #{rentedRate,jdbcType=DECIMAL},
      </if>
      <if test="totalMileage != null">
        total_mileage = #{totalMileage,jdbcType=DECIMAL},
      </if>
      <if test="effectiveMileage != null">
        effective_mileage = #{effectiveMileage,jdbcType=DECIMAL},
      </if>
      <if test="pickUpStation != null">
        pick_up_station = #{pickUpStation,jdbcType=INTEGER},
      </if>
      <if test="pickUpAirport != null">
        pick_up_airport = #{pickUpAirport,jdbcType=INTEGER},
      </if>
      <if test="wholeDay != null">
        whole_day = #{wholeDay,jdbcType=INTEGER},
      </if>
      <if test="halfDay != null">
        half_day = #{halfDay,jdbcType=INTEGER},
      </if>
      <if test="singleTime != null">
        single_time = #{singleTime,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.business.entity.StatisGeneralMonth">
    update statis_general_month
    set statis_date = #{statisDate,jdbcType=VARCHAR},
      city_code = #{cityCode,jdbcType=INTEGER},
      city_name = #{cityName,jdbcType=VARCHAR},
      receivable_amount = #{receivableAmount,jdbcType=DECIMAL},
      preferential_amount = #{preferentialAmount,jdbcType=DECIMAL},
      waiver_amount = #{waiverAmount,jdbcType=DECIMAL},
      total_amount = #{totalAmount,jdbcType=DECIMAL},
      create_order_num = #{createOrderNum,jdbcType=INTEGER},
      completed_order_num = #{completedOrderNum,jdbcType=INTEGER},
      rented_car_num = #{rentedCarNum,jdbcType=INTEGER},
      total_car_num = #{totalCarNum,jdbcType=INTEGER},
      rented_rate = #{rentedRate,jdbcType=DECIMAL},
      total_mileage = #{totalMileage,jdbcType=DECIMAL},
      effective_mileage = #{effectiveMileage,jdbcType=DECIMAL},
      pick_up_station = #{pickUpStation,jdbcType=INTEGER},
      pick_up_airport = #{pickUpAirport,jdbcType=INTEGER},
      whole_day = #{wholeDay,jdbcType=INTEGER},
      half_day = #{halfDay,jdbcType=INTEGER},
      single_time = #{singleTime,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>