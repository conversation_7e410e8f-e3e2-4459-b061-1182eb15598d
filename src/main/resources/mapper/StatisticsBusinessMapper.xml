<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.StatisticsBusinessMapper">
  <resultMap id="BaseResultMap" type="com.izu.business.entity.StatisticsBusiness">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="statistics_date" jdbcType="DATE" property="statisticsDate" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="account_type" jdbcType="TINYINT" property="accountType" />
    <result column="company_status" jdbcType="TINYINT" property="companyStatus" />
    <result column="last_login_time" jdbcType="TIMESTAMP" property="lastLoginTime" />
    <result column="customer_count" jdbcType="INTEGER" property="customerCount" />
    <result column="driver_count" jdbcType="INTEGER" property="driverCount" />
    <result column="driver_shouqi_count" jdbcType="INTEGER" property="driverShouqiCount" />
    <result column="driver_ziyou_count" jdbcType="INTEGER" property="driverZiyouCount" />
    <result column="driver_business_count" jdbcType="INTEGER" property="driverBusinessCount" />
    <result column="driver_third_count" jdbcType="INTEGER" property="driverThirdCount" />
    <result column="vehicle_count" jdbcType="INTEGER" property="vehicleCount" />
    <result column="vehicle_shouqi_count" jdbcType="INTEGER" property="vehicleShouqiCount" />
    <result column="vehicle_ziyou_count" jdbcType="INTEGER" property="vehicleZiyouCount" />
    <result column="vehicle_third_count" jdbcType="INTEGER" property="vehicleThirdCount" />
    <result column="vehicle_staff_count" jdbcType="INTEGER" property="vehicleStaffCount" />
    <result column="order_apply_count" jdbcType="INTEGER" property="orderApplyCount" />
    <result column="order_apply_internal_count" jdbcType="INTEGER" property="orderApplyInternalCount" />
    <result column="order_apply_motorcade_count" jdbcType="INTEGER" property="orderApplyMotorcadeCount" />
    <result column="no_sleep_status" jdbcType="INTEGER" property="noSleepStatus" />
    <result column="private_order_count" jdbcType="INTEGER" property="privateOrderCount" />
    <result column="so_order_count" jdbcType="INTEGER" property="soOrderCount" />
    <result column="co_order_count" jdbcType="INTEGER" property="coOrderCount" />
    <result column="device_shouqi_count" jdbcType="INTEGER" property="deviceShouqiCount" />
    <result column="device_ziyou_count" jdbcType="INTEGER" property="deviceZiyouCount" />
    <result column="apply_count" jdbcType="INTEGER" property="applyCount" />
    <result column="maintenance_sq_count" jdbcType="INTEGER" property="maintenanceSqCount" />
    <result column="maintenance_company_count" jdbcType="INTEGER" property="maintenanceCompanyCount" />
    <result column="charge_order_count" jdbcType="INTEGER" property="chargeOrderCount" />
    <result column="violation_sq_count" jdbcType="INTEGER" property="violationSqCount" />
    <result column="violation_company_count" jdbcType="INTEGER" property="violationCompanyCount" />
    <result column="belong_dept_id" jdbcType="VARCHAR" property="belongDeptId" />
    <result column="belong_dept_name" jdbcType="VARCHAR" property="belongDeptName" />
    <result column="public_id" jdbcType="VARCHAR" property="publicId" />
    <result column="public_pond_name" jdbcType="VARCHAR" property="publicPondName" />
    <result column="company_attribute" jdbcType="INTEGER" property="companyAttribute" />
    <result column="account_open_time" jdbcType="TIMESTAMP" property="accountOpenTime" />
    <result column="device_wireless_count" jdbcType="INTEGER" property="deviceWirelessCount" />
    <result column="device_wired_count" jdbcType="INTEGER" property="deviceWiredCount" />
    <result column="device_obd_count" jdbcType="INTEGER" property="deviceObdCount" />
    <result column="device_video_count" jdbcType="INTEGER" property="deviceVideoCount" />
    <result column="contract_count" jdbcType="INTEGER" property="contractCount" />
    <result column="contract_amount" jdbcType="DECIMAL" property="contractAmount" />
    <result column="system_contract_count" jdbcType="INTEGER" property="systemContractCount" />
    <result column="system_contract_amount" jdbcType="DECIMAL" property="systemContractAmount" />
    <result column="login_day" jdbcType="INTEGER" property="loginDay" />
    <result column="login_staff_count" jdbcType="INTEGER" property="loginStaffCount" />
    <result column="open_account_method" jdbcType="TINYINT" property="openAccountMethod" />
    <result column="active_status" jdbcType="INTEGER" property="activeStatus" />
    <result column="company_create_time" jdbcType="TIMESTAMP" property="companyCreateTime" />
    <result column="gov_public_order_count" jdbcType="INTEGER" property="govPublicOrderCount" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.izu.business.entity.StatisticsBusiness">
    <result column="top_function" jdbcType="VARCHAR" property="topFunction" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, statistics_date, create_time, update_time, company_id, company_name, account_type, 
    company_status, last_login_time, customer_count, driver_count, driver_shouqi_count, 
    driver_ziyou_count, driver_business_count, driver_third_count, vehicle_count, vehicle_shouqi_count, 
    vehicle_ziyou_count, vehicle_third_count, vehicle_staff_count, order_apply_count, 
    order_apply_internal_count, order_apply_motorcade_count, no_sleep_status, private_order_count, 
    so_order_count, co_order_count, device_shouqi_count, device_ziyou_count, apply_count, 
    maintenance_sq_count, maintenance_company_count, charge_order_count, violation_sq_count, 
    violation_company_count, belong_dept_id, belong_dept_name, public_id, public_pond_name, 
    company_attribute, account_open_time, device_wireless_count, device_wired_count, 
    device_obd_count, device_video_count, contract_count, contract_amount, system_contract_count, 
    system_contract_amount, login_day, login_staff_count, open_account_method, active_status, 
    company_create_time, gov_public_order_count
  </sql>
  <sql id="Blob_Column_List">
    top_function
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.izu.business.entity.StatisticsBusinessExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from statistics_business
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.izu.business.entity.StatisticsBusinessExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from statistics_business
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from statistics_business
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from statistics_business
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.business.entity.StatisticsBusinessExample">
    delete from statistics_business
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.business.entity.StatisticsBusiness">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into statistics_business (statistics_date, create_time, update_time, 
      company_id, company_name, account_type, 
      company_status, last_login_time, customer_count, 
      driver_count, driver_shouqi_count, driver_ziyou_count, 
      driver_business_count, driver_third_count, 
      vehicle_count, vehicle_shouqi_count, vehicle_ziyou_count, 
      vehicle_third_count, vehicle_staff_count, order_apply_count, 
      order_apply_internal_count, order_apply_motorcade_count, 
      no_sleep_status, private_order_count, so_order_count, 
      co_order_count, device_shouqi_count, device_ziyou_count, 
      apply_count, maintenance_sq_count, maintenance_company_count, 
      charge_order_count, violation_sq_count, violation_company_count, 
      belong_dept_id, belong_dept_name, public_id, 
      public_pond_name, company_attribute, account_open_time, 
      device_wireless_count, device_wired_count, 
      device_obd_count, device_video_count, contract_count, 
      contract_amount, system_contract_count, system_contract_amount, 
      login_day, login_staff_count, open_account_method, 
      active_status, company_create_time, gov_public_order_count, 
      top_function)
    values (#{statisticsDate,jdbcType=DATE}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{companyId,jdbcType=INTEGER}, #{companyName,jdbcType=VARCHAR}, #{accountType,jdbcType=TINYINT}, 
      #{companyStatus,jdbcType=TINYINT}, #{lastLoginTime,jdbcType=TIMESTAMP}, #{customerCount,jdbcType=INTEGER}, 
      #{driverCount,jdbcType=INTEGER}, #{driverShouqiCount,jdbcType=INTEGER}, #{driverZiyouCount,jdbcType=INTEGER}, 
      #{driverBusinessCount,jdbcType=INTEGER}, #{driverThirdCount,jdbcType=INTEGER}, 
      #{vehicleCount,jdbcType=INTEGER}, #{vehicleShouqiCount,jdbcType=INTEGER}, #{vehicleZiyouCount,jdbcType=INTEGER}, 
      #{vehicleThirdCount,jdbcType=INTEGER}, #{vehicleStaffCount,jdbcType=INTEGER}, #{orderApplyCount,jdbcType=INTEGER}, 
      #{orderApplyInternalCount,jdbcType=INTEGER}, #{orderApplyMotorcadeCount,jdbcType=INTEGER}, 
      #{noSleepStatus,jdbcType=INTEGER}, #{privateOrderCount,jdbcType=INTEGER}, #{soOrderCount,jdbcType=INTEGER}, 
      #{coOrderCount,jdbcType=INTEGER}, #{deviceShouqiCount,jdbcType=INTEGER}, #{deviceZiyouCount,jdbcType=INTEGER}, 
      #{applyCount,jdbcType=INTEGER}, #{maintenanceSqCount,jdbcType=INTEGER}, #{maintenanceCompanyCount,jdbcType=INTEGER}, 
      #{chargeOrderCount,jdbcType=INTEGER}, #{violationSqCount,jdbcType=INTEGER}, #{violationCompanyCount,jdbcType=INTEGER}, 
      #{belongDeptId,jdbcType=VARCHAR}, #{belongDeptName,jdbcType=VARCHAR}, #{publicId,jdbcType=VARCHAR}, 
      #{publicPondName,jdbcType=VARCHAR}, #{companyAttribute,jdbcType=INTEGER}, #{accountOpenTime,jdbcType=TIMESTAMP}, 
      #{deviceWirelessCount,jdbcType=INTEGER}, #{deviceWiredCount,jdbcType=INTEGER}, 
      #{deviceObdCount,jdbcType=INTEGER}, #{deviceVideoCount,jdbcType=INTEGER}, #{contractCount,jdbcType=INTEGER}, 
      #{contractAmount,jdbcType=DECIMAL}, #{systemContractCount,jdbcType=INTEGER}, #{systemContractAmount,jdbcType=DECIMAL}, 
      #{loginDay,jdbcType=INTEGER}, #{loginStaffCount,jdbcType=INTEGER}, #{openAccountMethod,jdbcType=TINYINT}, 
      #{activeStatus,jdbcType=INTEGER}, #{companyCreateTime,jdbcType=TIMESTAMP}, #{govPublicOrderCount,jdbcType=INTEGER}, 
      #{topFunction,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.business.entity.StatisticsBusiness">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into statistics_business
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="statisticsDate != null">
        statistics_date,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="accountType != null">
        account_type,
      </if>
      <if test="companyStatus != null">
        company_status,
      </if>
      <if test="lastLoginTime != null">
        last_login_time,
      </if>
      <if test="customerCount != null">
        customer_count,
      </if>
      <if test="driverCount != null">
        driver_count,
      </if>
      <if test="driverShouqiCount != null">
        driver_shouqi_count,
      </if>
      <if test="driverZiyouCount != null">
        driver_ziyou_count,
      </if>
      <if test="driverBusinessCount != null">
        driver_business_count,
      </if>
      <if test="driverThirdCount != null">
        driver_third_count,
      </if>
      <if test="vehicleCount != null">
        vehicle_count,
      </if>
      <if test="vehicleShouqiCount != null">
        vehicle_shouqi_count,
      </if>
      <if test="vehicleZiyouCount != null">
        vehicle_ziyou_count,
      </if>
      <if test="vehicleThirdCount != null">
        vehicle_third_count,
      </if>
      <if test="vehicleStaffCount != null">
        vehicle_staff_count,
      </if>
      <if test="orderApplyCount != null">
        order_apply_count,
      </if>
      <if test="orderApplyInternalCount != null">
        order_apply_internal_count,
      </if>
      <if test="orderApplyMotorcadeCount != null">
        order_apply_motorcade_count,
      </if>
      <if test="noSleepStatus != null">
        no_sleep_status,
      </if>
      <if test="privateOrderCount != null">
        private_order_count,
      </if>
      <if test="soOrderCount != null">
        so_order_count,
      </if>
      <if test="coOrderCount != null">
        co_order_count,
      </if>
      <if test="deviceShouqiCount != null">
        device_shouqi_count,
      </if>
      <if test="deviceZiyouCount != null">
        device_ziyou_count,
      </if>
      <if test="applyCount != null">
        apply_count,
      </if>
      <if test="maintenanceSqCount != null">
        maintenance_sq_count,
      </if>
      <if test="maintenanceCompanyCount != null">
        maintenance_company_count,
      </if>
      <if test="chargeOrderCount != null">
        charge_order_count,
      </if>
      <if test="violationSqCount != null">
        violation_sq_count,
      </if>
      <if test="violationCompanyCount != null">
        violation_company_count,
      </if>
      <if test="belongDeptId != null">
        belong_dept_id,
      </if>
      <if test="belongDeptName != null">
        belong_dept_name,
      </if>
      <if test="publicId != null">
        public_id,
      </if>
      <if test="publicPondName != null">
        public_pond_name,
      </if>
      <if test="companyAttribute != null">
        company_attribute,
      </if>
      <if test="accountOpenTime != null">
        account_open_time,
      </if>
      <if test="deviceWirelessCount != null">
        device_wireless_count,
      </if>
      <if test="deviceWiredCount != null">
        device_wired_count,
      </if>
      <if test="deviceObdCount != null">
        device_obd_count,
      </if>
      <if test="deviceVideoCount != null">
        device_video_count,
      </if>
      <if test="contractCount != null">
        contract_count,
      </if>
      <if test="contractAmount != null">
        contract_amount,
      </if>
      <if test="systemContractCount != null">
        system_contract_count,
      </if>
      <if test="systemContractAmount != null">
        system_contract_amount,
      </if>
      <if test="loginDay != null">
        login_day,
      </if>
      <if test="loginStaffCount != null">
        login_staff_count,
      </if>
      <if test="openAccountMethod != null">
        open_account_method,
      </if>
      <if test="activeStatus != null">
        active_status,
      </if>
      <if test="companyCreateTime != null">
        company_create_time,
      </if>
      <if test="govPublicOrderCount != null">
        gov_public_order_count,
      </if>
      <if test="topFunction != null">
        top_function,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="statisticsDate != null">
        #{statisticsDate,jdbcType=DATE},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="accountType != null">
        #{accountType,jdbcType=TINYINT},
      </if>
      <if test="companyStatus != null">
        #{companyStatus,jdbcType=TINYINT},
      </if>
      <if test="lastLoginTime != null">
        #{lastLoginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customerCount != null">
        #{customerCount,jdbcType=INTEGER},
      </if>
      <if test="driverCount != null">
        #{driverCount,jdbcType=INTEGER},
      </if>
      <if test="driverShouqiCount != null">
        #{driverShouqiCount,jdbcType=INTEGER},
      </if>
      <if test="driverZiyouCount != null">
        #{driverZiyouCount,jdbcType=INTEGER},
      </if>
      <if test="driverBusinessCount != null">
        #{driverBusinessCount,jdbcType=INTEGER},
      </if>
      <if test="driverThirdCount != null">
        #{driverThirdCount,jdbcType=INTEGER},
      </if>
      <if test="vehicleCount != null">
        #{vehicleCount,jdbcType=INTEGER},
      </if>
      <if test="vehicleShouqiCount != null">
        #{vehicleShouqiCount,jdbcType=INTEGER},
      </if>
      <if test="vehicleZiyouCount != null">
        #{vehicleZiyouCount,jdbcType=INTEGER},
      </if>
      <if test="vehicleThirdCount != null">
        #{vehicleThirdCount,jdbcType=INTEGER},
      </if>
      <if test="vehicleStaffCount != null">
        #{vehicleStaffCount,jdbcType=INTEGER},
      </if>
      <if test="orderApplyCount != null">
        #{orderApplyCount,jdbcType=INTEGER},
      </if>
      <if test="orderApplyInternalCount != null">
        #{orderApplyInternalCount,jdbcType=INTEGER},
      </if>
      <if test="orderApplyMotorcadeCount != null">
        #{orderApplyMotorcadeCount,jdbcType=INTEGER},
      </if>
      <if test="noSleepStatus != null">
        #{noSleepStatus,jdbcType=INTEGER},
      </if>
      <if test="privateOrderCount != null">
        #{privateOrderCount,jdbcType=INTEGER},
      </if>
      <if test="soOrderCount != null">
        #{soOrderCount,jdbcType=INTEGER},
      </if>
      <if test="coOrderCount != null">
        #{coOrderCount,jdbcType=INTEGER},
      </if>
      <if test="deviceShouqiCount != null">
        #{deviceShouqiCount,jdbcType=INTEGER},
      </if>
      <if test="deviceZiyouCount != null">
        #{deviceZiyouCount,jdbcType=INTEGER},
      </if>
      <if test="applyCount != null">
        #{applyCount,jdbcType=INTEGER},
      </if>
      <if test="maintenanceSqCount != null">
        #{maintenanceSqCount,jdbcType=INTEGER},
      </if>
      <if test="maintenanceCompanyCount != null">
        #{maintenanceCompanyCount,jdbcType=INTEGER},
      </if>
      <if test="chargeOrderCount != null">
        #{chargeOrderCount,jdbcType=INTEGER},
      </if>
      <if test="violationSqCount != null">
        #{violationSqCount,jdbcType=INTEGER},
      </if>
      <if test="violationCompanyCount != null">
        #{violationCompanyCount,jdbcType=INTEGER},
      </if>
      <if test="belongDeptId != null">
        #{belongDeptId,jdbcType=VARCHAR},
      </if>
      <if test="belongDeptName != null">
        #{belongDeptName,jdbcType=VARCHAR},
      </if>
      <if test="publicId != null">
        #{publicId,jdbcType=VARCHAR},
      </if>
      <if test="publicPondName != null">
        #{publicPondName,jdbcType=VARCHAR},
      </if>
      <if test="companyAttribute != null">
        #{companyAttribute,jdbcType=INTEGER},
      </if>
      <if test="accountOpenTime != null">
        #{accountOpenTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deviceWirelessCount != null">
        #{deviceWirelessCount,jdbcType=INTEGER},
      </if>
      <if test="deviceWiredCount != null">
        #{deviceWiredCount,jdbcType=INTEGER},
      </if>
      <if test="deviceObdCount != null">
        #{deviceObdCount,jdbcType=INTEGER},
      </if>
      <if test="deviceVideoCount != null">
        #{deviceVideoCount,jdbcType=INTEGER},
      </if>
      <if test="contractCount != null">
        #{contractCount,jdbcType=INTEGER},
      </if>
      <if test="contractAmount != null">
        #{contractAmount,jdbcType=DECIMAL},
      </if>
      <if test="systemContractCount != null">
        #{systemContractCount,jdbcType=INTEGER},
      </if>
      <if test="systemContractAmount != null">
        #{systemContractAmount,jdbcType=DECIMAL},
      </if>
      <if test="loginDay != null">
        #{loginDay,jdbcType=INTEGER},
      </if>
      <if test="loginStaffCount != null">
        #{loginStaffCount,jdbcType=INTEGER},
      </if>
      <if test="openAccountMethod != null">
        #{openAccountMethod,jdbcType=TINYINT},
      </if>
      <if test="activeStatus != null">
        #{activeStatus,jdbcType=INTEGER},
      </if>
      <if test="companyCreateTime != null">
        #{companyCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="govPublicOrderCount != null">
        #{govPublicOrderCount,jdbcType=INTEGER},
      </if>
      <if test="topFunction != null">
        #{topFunction,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.business.entity.StatisticsBusinessExample" resultType="java.lang.Long">
    select count(*) from statistics_business
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update statistics_business
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=INTEGER},
      </if>
      <if test="row.statisticsDate != null">
        statistics_date = #{row.statisticsDate,jdbcType=DATE},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.companyId != null">
        company_id = #{row.companyId,jdbcType=INTEGER},
      </if>
      <if test="row.companyName != null">
        company_name = #{row.companyName,jdbcType=VARCHAR},
      </if>
      <if test="row.accountType != null">
        account_type = #{row.accountType,jdbcType=TINYINT},
      </if>
      <if test="row.companyStatus != null">
        company_status = #{row.companyStatus,jdbcType=TINYINT},
      </if>
      <if test="row.lastLoginTime != null">
        last_login_time = #{row.lastLoginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.customerCount != null">
        customer_count = #{row.customerCount,jdbcType=INTEGER},
      </if>
      <if test="row.driverCount != null">
        driver_count = #{row.driverCount,jdbcType=INTEGER},
      </if>
      <if test="row.driverShouqiCount != null">
        driver_shouqi_count = #{row.driverShouqiCount,jdbcType=INTEGER},
      </if>
      <if test="row.driverZiyouCount != null">
        driver_ziyou_count = #{row.driverZiyouCount,jdbcType=INTEGER},
      </if>
      <if test="row.driverBusinessCount != null">
        driver_business_count = #{row.driverBusinessCount,jdbcType=INTEGER},
      </if>
      <if test="row.driverThirdCount != null">
        driver_third_count = #{row.driverThirdCount,jdbcType=INTEGER},
      </if>
      <if test="row.vehicleCount != null">
        vehicle_count = #{row.vehicleCount,jdbcType=INTEGER},
      </if>
      <if test="row.vehicleShouqiCount != null">
        vehicle_shouqi_count = #{row.vehicleShouqiCount,jdbcType=INTEGER},
      </if>
      <if test="row.vehicleZiyouCount != null">
        vehicle_ziyou_count = #{row.vehicleZiyouCount,jdbcType=INTEGER},
      </if>
      <if test="row.vehicleThirdCount != null">
        vehicle_third_count = #{row.vehicleThirdCount,jdbcType=INTEGER},
      </if>
      <if test="row.vehicleStaffCount != null">
        vehicle_staff_count = #{row.vehicleStaffCount,jdbcType=INTEGER},
      </if>
      <if test="row.orderApplyCount != null">
        order_apply_count = #{row.orderApplyCount,jdbcType=INTEGER},
      </if>
      <if test="row.orderApplyInternalCount != null">
        order_apply_internal_count = #{row.orderApplyInternalCount,jdbcType=INTEGER},
      </if>
      <if test="row.orderApplyMotorcadeCount != null">
        order_apply_motorcade_count = #{row.orderApplyMotorcadeCount,jdbcType=INTEGER},
      </if>
      <if test="row.noSleepStatus != null">
        no_sleep_status = #{row.noSleepStatus,jdbcType=INTEGER},
      </if>
      <if test="row.privateOrderCount != null">
        private_order_count = #{row.privateOrderCount,jdbcType=INTEGER},
      </if>
      <if test="row.soOrderCount != null">
        so_order_count = #{row.soOrderCount,jdbcType=INTEGER},
      </if>
      <if test="row.coOrderCount != null">
        co_order_count = #{row.coOrderCount,jdbcType=INTEGER},
      </if>
      <if test="row.deviceShouqiCount != null">
        device_shouqi_count = #{row.deviceShouqiCount,jdbcType=INTEGER},
      </if>
      <if test="row.deviceZiyouCount != null">
        device_ziyou_count = #{row.deviceZiyouCount,jdbcType=INTEGER},
      </if>
      <if test="row.applyCount != null">
        apply_count = #{row.applyCount,jdbcType=INTEGER},
      </if>
      <if test="row.maintenanceSqCount != null">
        maintenance_sq_count = #{row.maintenanceSqCount,jdbcType=INTEGER},
      </if>
      <if test="row.maintenanceCompanyCount != null">
        maintenance_company_count = #{row.maintenanceCompanyCount,jdbcType=INTEGER},
      </if>
      <if test="row.chargeOrderCount != null">
        charge_order_count = #{row.chargeOrderCount,jdbcType=INTEGER},
      </if>
      <if test="row.violationSqCount != null">
        violation_sq_count = #{row.violationSqCount,jdbcType=INTEGER},
      </if>
      <if test="row.violationCompanyCount != null">
        violation_company_count = #{row.violationCompanyCount,jdbcType=INTEGER},
      </if>
      <if test="row.belongDeptId != null">
        belong_dept_id = #{row.belongDeptId,jdbcType=VARCHAR},
      </if>
      <if test="row.belongDeptName != null">
        belong_dept_name = #{row.belongDeptName,jdbcType=VARCHAR},
      </if>
      <if test="row.publicId != null">
        public_id = #{row.publicId,jdbcType=VARCHAR},
      </if>
      <if test="row.publicPondName != null">
        public_pond_name = #{row.publicPondName,jdbcType=VARCHAR},
      </if>
      <if test="row.companyAttribute != null">
        company_attribute = #{row.companyAttribute,jdbcType=INTEGER},
      </if>
      <if test="row.accountOpenTime != null">
        account_open_time = #{row.accountOpenTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.deviceWirelessCount != null">
        device_wireless_count = #{row.deviceWirelessCount,jdbcType=INTEGER},
      </if>
      <if test="row.deviceWiredCount != null">
        device_wired_count = #{row.deviceWiredCount,jdbcType=INTEGER},
      </if>
      <if test="row.deviceObdCount != null">
        device_obd_count = #{row.deviceObdCount,jdbcType=INTEGER},
      </if>
      <if test="row.deviceVideoCount != null">
        device_video_count = #{row.deviceVideoCount,jdbcType=INTEGER},
      </if>
      <if test="row.contractCount != null">
        contract_count = #{row.contractCount,jdbcType=INTEGER},
      </if>
      <if test="row.contractAmount != null">
        contract_amount = #{row.contractAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.systemContractCount != null">
        system_contract_count = #{row.systemContractCount,jdbcType=INTEGER},
      </if>
      <if test="row.systemContractAmount != null">
        system_contract_amount = #{row.systemContractAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.loginDay != null">
        login_day = #{row.loginDay,jdbcType=INTEGER},
      </if>
      <if test="row.loginStaffCount != null">
        login_staff_count = #{row.loginStaffCount,jdbcType=INTEGER},
      </if>
      <if test="row.openAccountMethod != null">
        open_account_method = #{row.openAccountMethod,jdbcType=TINYINT},
      </if>
      <if test="row.activeStatus != null">
        active_status = #{row.activeStatus,jdbcType=INTEGER},
      </if>
      <if test="row.companyCreateTime != null">
        company_create_time = #{row.companyCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.govPublicOrderCount != null">
        gov_public_order_count = #{row.govPublicOrderCount,jdbcType=INTEGER},
      </if>
      <if test="row.topFunction != null">
        top_function = #{row.topFunction,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update statistics_business
    set id = #{row.id,jdbcType=INTEGER},
      statistics_date = #{row.statisticsDate,jdbcType=DATE},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      company_id = #{row.companyId,jdbcType=INTEGER},
      company_name = #{row.companyName,jdbcType=VARCHAR},
      account_type = #{row.accountType,jdbcType=TINYINT},
      company_status = #{row.companyStatus,jdbcType=TINYINT},
      last_login_time = #{row.lastLoginTime,jdbcType=TIMESTAMP},
      customer_count = #{row.customerCount,jdbcType=INTEGER},
      driver_count = #{row.driverCount,jdbcType=INTEGER},
      driver_shouqi_count = #{row.driverShouqiCount,jdbcType=INTEGER},
      driver_ziyou_count = #{row.driverZiyouCount,jdbcType=INTEGER},
      driver_business_count = #{row.driverBusinessCount,jdbcType=INTEGER},
      driver_third_count = #{row.driverThirdCount,jdbcType=INTEGER},
      vehicle_count = #{row.vehicleCount,jdbcType=INTEGER},
      vehicle_shouqi_count = #{row.vehicleShouqiCount,jdbcType=INTEGER},
      vehicle_ziyou_count = #{row.vehicleZiyouCount,jdbcType=INTEGER},
      vehicle_third_count = #{row.vehicleThirdCount,jdbcType=INTEGER},
      vehicle_staff_count = #{row.vehicleStaffCount,jdbcType=INTEGER},
      order_apply_count = #{row.orderApplyCount,jdbcType=INTEGER},
      order_apply_internal_count = #{row.orderApplyInternalCount,jdbcType=INTEGER},
      order_apply_motorcade_count = #{row.orderApplyMotorcadeCount,jdbcType=INTEGER},
      no_sleep_status = #{row.noSleepStatus,jdbcType=INTEGER},
      private_order_count = #{row.privateOrderCount,jdbcType=INTEGER},
      so_order_count = #{row.soOrderCount,jdbcType=INTEGER},
      co_order_count = #{row.coOrderCount,jdbcType=INTEGER},
      device_shouqi_count = #{row.deviceShouqiCount,jdbcType=INTEGER},
      device_ziyou_count = #{row.deviceZiyouCount,jdbcType=INTEGER},
      apply_count = #{row.applyCount,jdbcType=INTEGER},
      maintenance_sq_count = #{row.maintenanceSqCount,jdbcType=INTEGER},
      maintenance_company_count = #{row.maintenanceCompanyCount,jdbcType=INTEGER},
      charge_order_count = #{row.chargeOrderCount,jdbcType=INTEGER},
      violation_sq_count = #{row.violationSqCount,jdbcType=INTEGER},
      violation_company_count = #{row.violationCompanyCount,jdbcType=INTEGER},
      belong_dept_id = #{row.belongDeptId,jdbcType=VARCHAR},
      belong_dept_name = #{row.belongDeptName,jdbcType=VARCHAR},
      public_id = #{row.publicId,jdbcType=VARCHAR},
      public_pond_name = #{row.publicPondName,jdbcType=VARCHAR},
      company_attribute = #{row.companyAttribute,jdbcType=INTEGER},
      account_open_time = #{row.accountOpenTime,jdbcType=TIMESTAMP},
      device_wireless_count = #{row.deviceWirelessCount,jdbcType=INTEGER},
      device_wired_count = #{row.deviceWiredCount,jdbcType=INTEGER},
      device_obd_count = #{row.deviceObdCount,jdbcType=INTEGER},
      device_video_count = #{row.deviceVideoCount,jdbcType=INTEGER},
      contract_count = #{row.contractCount,jdbcType=INTEGER},
      contract_amount = #{row.contractAmount,jdbcType=DECIMAL},
      system_contract_count = #{row.systemContractCount,jdbcType=INTEGER},
      system_contract_amount = #{row.systemContractAmount,jdbcType=DECIMAL},
      login_day = #{row.loginDay,jdbcType=INTEGER},
      login_staff_count = #{row.loginStaffCount,jdbcType=INTEGER},
      open_account_method = #{row.openAccountMethod,jdbcType=TINYINT},
      active_status = #{row.activeStatus,jdbcType=INTEGER},
      company_create_time = #{row.companyCreateTime,jdbcType=TIMESTAMP},
      gov_public_order_count = #{row.govPublicOrderCount,jdbcType=INTEGER},
      top_function = #{row.topFunction,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update statistics_business
    set id = #{row.id,jdbcType=INTEGER},
      statistics_date = #{row.statisticsDate,jdbcType=DATE},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      company_id = #{row.companyId,jdbcType=INTEGER},
      company_name = #{row.companyName,jdbcType=VARCHAR},
      account_type = #{row.accountType,jdbcType=TINYINT},
      company_status = #{row.companyStatus,jdbcType=TINYINT},
      last_login_time = #{row.lastLoginTime,jdbcType=TIMESTAMP},
      customer_count = #{row.customerCount,jdbcType=INTEGER},
      driver_count = #{row.driverCount,jdbcType=INTEGER},
      driver_shouqi_count = #{row.driverShouqiCount,jdbcType=INTEGER},
      driver_ziyou_count = #{row.driverZiyouCount,jdbcType=INTEGER},
      driver_business_count = #{row.driverBusinessCount,jdbcType=INTEGER},
      driver_third_count = #{row.driverThirdCount,jdbcType=INTEGER},
      vehicle_count = #{row.vehicleCount,jdbcType=INTEGER},
      vehicle_shouqi_count = #{row.vehicleShouqiCount,jdbcType=INTEGER},
      vehicle_ziyou_count = #{row.vehicleZiyouCount,jdbcType=INTEGER},
      vehicle_third_count = #{row.vehicleThirdCount,jdbcType=INTEGER},
      vehicle_staff_count = #{row.vehicleStaffCount,jdbcType=INTEGER},
      order_apply_count = #{row.orderApplyCount,jdbcType=INTEGER},
      order_apply_internal_count = #{row.orderApplyInternalCount,jdbcType=INTEGER},
      order_apply_motorcade_count = #{row.orderApplyMotorcadeCount,jdbcType=INTEGER},
      no_sleep_status = #{row.noSleepStatus,jdbcType=INTEGER},
      private_order_count = #{row.privateOrderCount,jdbcType=INTEGER},
      so_order_count = #{row.soOrderCount,jdbcType=INTEGER},
      co_order_count = #{row.coOrderCount,jdbcType=INTEGER},
      device_shouqi_count = #{row.deviceShouqiCount,jdbcType=INTEGER},
      device_ziyou_count = #{row.deviceZiyouCount,jdbcType=INTEGER},
      apply_count = #{row.applyCount,jdbcType=INTEGER},
      maintenance_sq_count = #{row.maintenanceSqCount,jdbcType=INTEGER},
      maintenance_company_count = #{row.maintenanceCompanyCount,jdbcType=INTEGER},
      charge_order_count = #{row.chargeOrderCount,jdbcType=INTEGER},
      violation_sq_count = #{row.violationSqCount,jdbcType=INTEGER},
      violation_company_count = #{row.violationCompanyCount,jdbcType=INTEGER},
      belong_dept_id = #{row.belongDeptId,jdbcType=VARCHAR},
      belong_dept_name = #{row.belongDeptName,jdbcType=VARCHAR},
      public_id = #{row.publicId,jdbcType=VARCHAR},
      public_pond_name = #{row.publicPondName,jdbcType=VARCHAR},
      company_attribute = #{row.companyAttribute,jdbcType=INTEGER},
      account_open_time = #{row.accountOpenTime,jdbcType=TIMESTAMP},
      device_wireless_count = #{row.deviceWirelessCount,jdbcType=INTEGER},
      device_wired_count = #{row.deviceWiredCount,jdbcType=INTEGER},
      device_obd_count = #{row.deviceObdCount,jdbcType=INTEGER},
      device_video_count = #{row.deviceVideoCount,jdbcType=INTEGER},
      contract_count = #{row.contractCount,jdbcType=INTEGER},
      contract_amount = #{row.contractAmount,jdbcType=DECIMAL},
      system_contract_count = #{row.systemContractCount,jdbcType=INTEGER},
      system_contract_amount = #{row.systemContractAmount,jdbcType=DECIMAL},
      login_day = #{row.loginDay,jdbcType=INTEGER},
      login_staff_count = #{row.loginStaffCount,jdbcType=INTEGER},
      open_account_method = #{row.openAccountMethod,jdbcType=TINYINT},
      active_status = #{row.activeStatus,jdbcType=INTEGER},
      company_create_time = #{row.companyCreateTime,jdbcType=TIMESTAMP},
      gov_public_order_count = #{row.govPublicOrderCount,jdbcType=INTEGER}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.business.entity.StatisticsBusiness">
    update statistics_business
    <set>
      <if test="statisticsDate != null">
        statistics_date = #{statisticsDate,jdbcType=DATE},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="accountType != null">
        account_type = #{accountType,jdbcType=TINYINT},
      </if>
      <if test="companyStatus != null">
        company_status = #{companyStatus,jdbcType=TINYINT},
      </if>
      <if test="lastLoginTime != null">
        last_login_time = #{lastLoginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customerCount != null">
        customer_count = #{customerCount,jdbcType=INTEGER},
      </if>
      <if test="driverCount != null">
        driver_count = #{driverCount,jdbcType=INTEGER},
      </if>
      <if test="driverShouqiCount != null">
        driver_shouqi_count = #{driverShouqiCount,jdbcType=INTEGER},
      </if>
      <if test="driverZiyouCount != null">
        driver_ziyou_count = #{driverZiyouCount,jdbcType=INTEGER},
      </if>
      <if test="driverBusinessCount != null">
        driver_business_count = #{driverBusinessCount,jdbcType=INTEGER},
      </if>
      <if test="driverThirdCount != null">
        driver_third_count = #{driverThirdCount,jdbcType=INTEGER},
      </if>
      <if test="vehicleCount != null">
        vehicle_count = #{vehicleCount,jdbcType=INTEGER},
      </if>
      <if test="vehicleShouqiCount != null">
        vehicle_shouqi_count = #{vehicleShouqiCount,jdbcType=INTEGER},
      </if>
      <if test="vehicleZiyouCount != null">
        vehicle_ziyou_count = #{vehicleZiyouCount,jdbcType=INTEGER},
      </if>
      <if test="vehicleThirdCount != null">
        vehicle_third_count = #{vehicleThirdCount,jdbcType=INTEGER},
      </if>
      <if test="vehicleStaffCount != null">
        vehicle_staff_count = #{vehicleStaffCount,jdbcType=INTEGER},
      </if>
      <if test="orderApplyCount != null">
        order_apply_count = #{orderApplyCount,jdbcType=INTEGER},
      </if>
      <if test="orderApplyInternalCount != null">
        order_apply_internal_count = #{orderApplyInternalCount,jdbcType=INTEGER},
      </if>
      <if test="orderApplyMotorcadeCount != null">
        order_apply_motorcade_count = #{orderApplyMotorcadeCount,jdbcType=INTEGER},
      </if>
      <if test="noSleepStatus != null">
        no_sleep_status = #{noSleepStatus,jdbcType=INTEGER},
      </if>
      <if test="privateOrderCount != null">
        private_order_count = #{privateOrderCount,jdbcType=INTEGER},
      </if>
      <if test="soOrderCount != null">
        so_order_count = #{soOrderCount,jdbcType=INTEGER},
      </if>
      <if test="coOrderCount != null">
        co_order_count = #{coOrderCount,jdbcType=INTEGER},
      </if>
      <if test="deviceShouqiCount != null">
        device_shouqi_count = #{deviceShouqiCount,jdbcType=INTEGER},
      </if>
      <if test="deviceZiyouCount != null">
        device_ziyou_count = #{deviceZiyouCount,jdbcType=INTEGER},
      </if>
      <if test="applyCount != null">
        apply_count = #{applyCount,jdbcType=INTEGER},
      </if>
      <if test="maintenanceSqCount != null">
        maintenance_sq_count = #{maintenanceSqCount,jdbcType=INTEGER},
      </if>
      <if test="maintenanceCompanyCount != null">
        maintenance_company_count = #{maintenanceCompanyCount,jdbcType=INTEGER},
      </if>
      <if test="chargeOrderCount != null">
        charge_order_count = #{chargeOrderCount,jdbcType=INTEGER},
      </if>
      <if test="violationSqCount != null">
        violation_sq_count = #{violationSqCount,jdbcType=INTEGER},
      </if>
      <if test="violationCompanyCount != null">
        violation_company_count = #{violationCompanyCount,jdbcType=INTEGER},
      </if>
      <if test="belongDeptId != null">
        belong_dept_id = #{belongDeptId,jdbcType=VARCHAR},
      </if>
      <if test="belongDeptName != null">
        belong_dept_name = #{belongDeptName,jdbcType=VARCHAR},
      </if>
      <if test="publicId != null">
        public_id = #{publicId,jdbcType=VARCHAR},
      </if>
      <if test="publicPondName != null">
        public_pond_name = #{publicPondName,jdbcType=VARCHAR},
      </if>
      <if test="companyAttribute != null">
        company_attribute = #{companyAttribute,jdbcType=INTEGER},
      </if>
      <if test="accountOpenTime != null">
        account_open_time = #{accountOpenTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deviceWirelessCount != null">
        device_wireless_count = #{deviceWirelessCount,jdbcType=INTEGER},
      </if>
      <if test="deviceWiredCount != null">
        device_wired_count = #{deviceWiredCount,jdbcType=INTEGER},
      </if>
      <if test="deviceObdCount != null">
        device_obd_count = #{deviceObdCount,jdbcType=INTEGER},
      </if>
      <if test="deviceVideoCount != null">
        device_video_count = #{deviceVideoCount,jdbcType=INTEGER},
      </if>
      <if test="contractCount != null">
        contract_count = #{contractCount,jdbcType=INTEGER},
      </if>
      <if test="contractAmount != null">
        contract_amount = #{contractAmount,jdbcType=DECIMAL},
      </if>
      <if test="systemContractCount != null">
        system_contract_count = #{systemContractCount,jdbcType=INTEGER},
      </if>
      <if test="systemContractAmount != null">
        system_contract_amount = #{systemContractAmount,jdbcType=DECIMAL},
      </if>
      <if test="loginDay != null">
        login_day = #{loginDay,jdbcType=INTEGER},
      </if>
      <if test="loginStaffCount != null">
        login_staff_count = #{loginStaffCount,jdbcType=INTEGER},
      </if>
      <if test="openAccountMethod != null">
        open_account_method = #{openAccountMethod,jdbcType=TINYINT},
      </if>
      <if test="activeStatus != null">
        active_status = #{activeStatus,jdbcType=INTEGER},
      </if>
      <if test="companyCreateTime != null">
        company_create_time = #{companyCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="govPublicOrderCount != null">
        gov_public_order_count = #{govPublicOrderCount,jdbcType=INTEGER},
      </if>
      <if test="topFunction != null">
        top_function = #{topFunction,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.izu.business.entity.StatisticsBusiness">
    update statistics_business
    set statistics_date = #{statisticsDate,jdbcType=DATE},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      company_id = #{companyId,jdbcType=INTEGER},
      company_name = #{companyName,jdbcType=VARCHAR},
      account_type = #{accountType,jdbcType=TINYINT},
      company_status = #{companyStatus,jdbcType=TINYINT},
      last_login_time = #{lastLoginTime,jdbcType=TIMESTAMP},
      customer_count = #{customerCount,jdbcType=INTEGER},
      driver_count = #{driverCount,jdbcType=INTEGER},
      driver_shouqi_count = #{driverShouqiCount,jdbcType=INTEGER},
      driver_ziyou_count = #{driverZiyouCount,jdbcType=INTEGER},
      driver_business_count = #{driverBusinessCount,jdbcType=INTEGER},
      driver_third_count = #{driverThirdCount,jdbcType=INTEGER},
      vehicle_count = #{vehicleCount,jdbcType=INTEGER},
      vehicle_shouqi_count = #{vehicleShouqiCount,jdbcType=INTEGER},
      vehicle_ziyou_count = #{vehicleZiyouCount,jdbcType=INTEGER},
      vehicle_third_count = #{vehicleThirdCount,jdbcType=INTEGER},
      vehicle_staff_count = #{vehicleStaffCount,jdbcType=INTEGER},
      order_apply_count = #{orderApplyCount,jdbcType=INTEGER},
      order_apply_internal_count = #{orderApplyInternalCount,jdbcType=INTEGER},
      order_apply_motorcade_count = #{orderApplyMotorcadeCount,jdbcType=INTEGER},
      no_sleep_status = #{noSleepStatus,jdbcType=INTEGER},
      private_order_count = #{privateOrderCount,jdbcType=INTEGER},
      so_order_count = #{soOrderCount,jdbcType=INTEGER},
      co_order_count = #{coOrderCount,jdbcType=INTEGER},
      device_shouqi_count = #{deviceShouqiCount,jdbcType=INTEGER},
      device_ziyou_count = #{deviceZiyouCount,jdbcType=INTEGER},
      apply_count = #{applyCount,jdbcType=INTEGER},
      maintenance_sq_count = #{maintenanceSqCount,jdbcType=INTEGER},
      maintenance_company_count = #{maintenanceCompanyCount,jdbcType=INTEGER},
      charge_order_count = #{chargeOrderCount,jdbcType=INTEGER},
      violation_sq_count = #{violationSqCount,jdbcType=INTEGER},
      violation_company_count = #{violationCompanyCount,jdbcType=INTEGER},
      belong_dept_id = #{belongDeptId,jdbcType=VARCHAR},
      belong_dept_name = #{belongDeptName,jdbcType=VARCHAR},
      public_id = #{publicId,jdbcType=VARCHAR},
      public_pond_name = #{publicPondName,jdbcType=VARCHAR},
      company_attribute = #{companyAttribute,jdbcType=INTEGER},
      account_open_time = #{accountOpenTime,jdbcType=TIMESTAMP},
      device_wireless_count = #{deviceWirelessCount,jdbcType=INTEGER},
      device_wired_count = #{deviceWiredCount,jdbcType=INTEGER},
      device_obd_count = #{deviceObdCount,jdbcType=INTEGER},
      device_video_count = #{deviceVideoCount,jdbcType=INTEGER},
      contract_count = #{contractCount,jdbcType=INTEGER},
      contract_amount = #{contractAmount,jdbcType=DECIMAL},
      system_contract_count = #{systemContractCount,jdbcType=INTEGER},
      system_contract_amount = #{systemContractAmount,jdbcType=DECIMAL},
      login_day = #{loginDay,jdbcType=INTEGER},
      login_staff_count = #{loginStaffCount,jdbcType=INTEGER},
      open_account_method = #{openAccountMethod,jdbcType=TINYINT},
      active_status = #{activeStatus,jdbcType=INTEGER},
      company_create_time = #{companyCreateTime,jdbcType=TIMESTAMP},
      gov_public_order_count = #{govPublicOrderCount,jdbcType=INTEGER},
      top_function = #{topFunction,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.business.entity.StatisticsBusiness">
    update statistics_business
    set statistics_date = #{statisticsDate,jdbcType=DATE},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      company_id = #{companyId,jdbcType=INTEGER},
      company_name = #{companyName,jdbcType=VARCHAR},
      account_type = #{accountType,jdbcType=TINYINT},
      company_status = #{companyStatus,jdbcType=TINYINT},
      last_login_time = #{lastLoginTime,jdbcType=TIMESTAMP},
      customer_count = #{customerCount,jdbcType=INTEGER},
      driver_count = #{driverCount,jdbcType=INTEGER},
      driver_shouqi_count = #{driverShouqiCount,jdbcType=INTEGER},
      driver_ziyou_count = #{driverZiyouCount,jdbcType=INTEGER},
      driver_business_count = #{driverBusinessCount,jdbcType=INTEGER},
      driver_third_count = #{driverThirdCount,jdbcType=INTEGER},
      vehicle_count = #{vehicleCount,jdbcType=INTEGER},
      vehicle_shouqi_count = #{vehicleShouqiCount,jdbcType=INTEGER},
      vehicle_ziyou_count = #{vehicleZiyouCount,jdbcType=INTEGER},
      vehicle_third_count = #{vehicleThirdCount,jdbcType=INTEGER},
      vehicle_staff_count = #{vehicleStaffCount,jdbcType=INTEGER},
      order_apply_count = #{orderApplyCount,jdbcType=INTEGER},
      order_apply_internal_count = #{orderApplyInternalCount,jdbcType=INTEGER},
      order_apply_motorcade_count = #{orderApplyMotorcadeCount,jdbcType=INTEGER},
      no_sleep_status = #{noSleepStatus,jdbcType=INTEGER},
      private_order_count = #{privateOrderCount,jdbcType=INTEGER},
      so_order_count = #{soOrderCount,jdbcType=INTEGER},
      co_order_count = #{coOrderCount,jdbcType=INTEGER},
      device_shouqi_count = #{deviceShouqiCount,jdbcType=INTEGER},
      device_ziyou_count = #{deviceZiyouCount,jdbcType=INTEGER},
      apply_count = #{applyCount,jdbcType=INTEGER},
      maintenance_sq_count = #{maintenanceSqCount,jdbcType=INTEGER},
      maintenance_company_count = #{maintenanceCompanyCount,jdbcType=INTEGER},
      charge_order_count = #{chargeOrderCount,jdbcType=INTEGER},
      violation_sq_count = #{violationSqCount,jdbcType=INTEGER},
      violation_company_count = #{violationCompanyCount,jdbcType=INTEGER},
      belong_dept_id = #{belongDeptId,jdbcType=VARCHAR},
      belong_dept_name = #{belongDeptName,jdbcType=VARCHAR},
      public_id = #{publicId,jdbcType=VARCHAR},
      public_pond_name = #{publicPondName,jdbcType=VARCHAR},
      company_attribute = #{companyAttribute,jdbcType=INTEGER},
      account_open_time = #{accountOpenTime,jdbcType=TIMESTAMP},
      device_wireless_count = #{deviceWirelessCount,jdbcType=INTEGER},
      device_wired_count = #{deviceWiredCount,jdbcType=INTEGER},
      device_obd_count = #{deviceObdCount,jdbcType=INTEGER},
      device_video_count = #{deviceVideoCount,jdbcType=INTEGER},
      contract_count = #{contractCount,jdbcType=INTEGER},
      contract_amount = #{contractAmount,jdbcType=DECIMAL},
      system_contract_count = #{systemContractCount,jdbcType=INTEGER},
      system_contract_amount = #{systemContractAmount,jdbcType=DECIMAL},
      login_day = #{loginDay,jdbcType=INTEGER},
      login_staff_count = #{loginStaffCount,jdbcType=INTEGER},
      open_account_method = #{openAccountMethod,jdbcType=TINYINT},
      active_status = #{activeStatus,jdbcType=INTEGER},
      company_create_time = #{companyCreateTime,jdbcType=TIMESTAMP},
      gov_public_order_count = #{govPublicOrderCount,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>