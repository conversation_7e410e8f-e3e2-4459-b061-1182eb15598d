<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.OrderCommentSubMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.OrderCommentSub">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="comment_id" jdbcType="INTEGER" property="commentId" />
    <result column="order_apply_no" jdbcType="VARCHAR" property="orderApplyNo" />
    <result column="evaluation_id" jdbcType="INTEGER" property="evaluationId" />
    <result column="evaluation_name" jdbcType="VARCHAR" property="evaluationName" />
    <result column="evaluation_describe" jdbcType="VARCHAR" property="evaluationDescribe" />
    <result column="score" jdbcType="INTEGER" property="score" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, comment_id, order_apply_no, evaluation_id, evaluation_name, evaluation_describe, 
    score, create_date, update_date
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_comment_sub
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from order_comment_sub
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.OrderCommentSub">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_comment_sub (comment_id, order_apply_no, evaluation_id, 
      evaluation_name, evaluation_describe, score, 
      create_date, update_date)
    values (#{commentId,jdbcType=INTEGER}, #{orderApplyNo,jdbcType=VARCHAR}, #{evaluationId,jdbcType=INTEGER}, 
      #{evaluationName,jdbcType=VARCHAR}, #{evaluationDescribe,jdbcType=VARCHAR}, #{score,jdbcType=INTEGER}, 
      #{createDate,jdbcType=TIMESTAMP}, #{updateDate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.OrderCommentSub">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_comment_sub
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="commentId != null">
        comment_id,
      </if>
      <if test="orderApplyNo != null">
        order_apply_no,
      </if>
      <if test="evaluationId != null">
        evaluation_id,
      </if>
      <if test="evaluationName != null">
        evaluation_name,
      </if>
      <if test="evaluationDescribe != null">
        evaluation_describe,
      </if>
      <if test="score != null">
        score,
      </if>
      <if test="createDate != null">
        create_date,
      </if>
      <if test="updateDate != null">
        update_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="commentId != null">
        #{commentId,jdbcType=INTEGER},
      </if>
      <if test="orderApplyNo != null">
        #{orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="evaluationId != null">
        #{evaluationId,jdbcType=INTEGER},
      </if>
      <if test="evaluationName != null">
        #{evaluationName,jdbcType=VARCHAR},
      </if>
      <if test="evaluationDescribe != null">
        #{evaluationDescribe,jdbcType=VARCHAR},
      </if>
      <if test="score != null">
        #{score,jdbcType=INTEGER},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDate != null">
        #{updateDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.OrderCommentSub">
    update order_comment_sub
    <set>
      <if test="commentId != null">
        comment_id = #{commentId,jdbcType=INTEGER},
      </if>
      <if test="orderApplyNo != null">
        order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="evaluationId != null">
        evaluation_id = #{evaluationId,jdbcType=INTEGER},
      </if>
      <if test="evaluationName != null">
        evaluation_name = #{evaluationName,jdbcType=VARCHAR},
      </if>
      <if test="evaluationDescribe != null">
        evaluation_describe = #{evaluationDescribe,jdbcType=VARCHAR},
      </if>
      <if test="score != null">
        score = #{score,jdbcType=INTEGER},
      </if>
      <if test="createDate != null">
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDate != null">
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.OrderCommentSub">
    update order_comment_sub
    set comment_id = #{commentId,jdbcType=INTEGER},
      order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      evaluation_id = #{evaluationId,jdbcType=INTEGER},
      evaluation_name = #{evaluationName,jdbcType=VARCHAR},
      evaluation_describe = #{evaluationDescribe,jdbcType=VARCHAR},
      score = #{score,jdbcType=INTEGER},
      create_date = #{createDate,jdbcType=TIMESTAMP},
      update_date = #{updateDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>