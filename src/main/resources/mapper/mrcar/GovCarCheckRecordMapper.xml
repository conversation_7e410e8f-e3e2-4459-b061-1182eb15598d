<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.GovCarCheckRecordMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.GovCarCheckRecord">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="check_record_no" jdbcType="VARCHAR" property="checkRecordNo" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="gain_back_no" jdbcType="VARCHAR" property="gainBackNo" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="vehicle_license" jdbcType="VARCHAR" property="vehicleLicense" />
    <result column="vehicle_vin" jdbcType="VARCHAR" property="vehicleVin" />
    <result column="vehicle_brand_code" jdbcType="VARCHAR" property="vehicleBrandCode" />
    <result column="vehicle_brand_name" jdbcType="VARCHAR" property="vehicleBrandName" />
    <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode" />
    <result column="vehicle_model_name" jdbcType="VARCHAR" property="vehicleModelName" />
    <result column="oil_type" jdbcType="TINYINT" property="oilType" />
    <result column="oil_quantity" jdbcType="TINYINT" property="oilQuantity" />
    <result column="power_quantity" jdbcType="TINYINT" property="powerQuantity" />
    <result column="mileage_quantity" jdbcType="INTEGER" property="mileageQuantity" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="check_type" jdbcType="TINYINT" property="checkType" />
    <result column="check_status" jdbcType="TINYINT" property="checkStatus" />
    <result column="check_user_id" jdbcType="INTEGER" property="checkUserId" />
    <result column="check_user_name" jdbcType="VARCHAR" property="checkUserName" />
    <result column="check_start_time" jdbcType="TIMESTAMP" property="checkStartTime" />
    <result column="check_end_time" jdbcType="TIMESTAMP" property="checkEndTime" />
    <result column="cancel_user_id" jdbcType="INTEGER" property="cancelUserId" />
    <result column="cancel_user_name" jdbcType="VARCHAR" property="cancelUserName" />
    <result column="cancel_time" jdbcType="TIMESTAMP" property="cancelTime" />
    <result column="cancel_reason" jdbcType="VARCHAR" property="cancelReason" />
    <result column="update_id" jdbcType="INTEGER" property="updateId" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, check_record_no, order_no, gain_back_no, vehicle_id, vehicle_license, vehicle_vin, 
    vehicle_brand_code, vehicle_brand_name, vehicle_model_code, vehicle_model_name, oil_type, 
    oil_quantity, power_quantity, mileage_quantity, memo, check_type, check_status, check_user_id, 
    check_user_name, check_start_time, check_end_time, cancel_user_id, cancel_user_name, 
    cancel_time, cancel_reason, update_id, update_name, update_time
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.GovCarCheckRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from gov_car_check_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from gov_car_check_record
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from gov_car_check_record
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.GovCarCheckRecord">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gov_car_check_record (check_record_no, order_no, gain_back_no, 
      vehicle_id, vehicle_license, vehicle_vin, 
      vehicle_brand_code, vehicle_brand_name, vehicle_model_code, 
      vehicle_model_name, oil_type, oil_quantity, 
      power_quantity, mileage_quantity, memo, 
      check_type, check_status, check_user_id, 
      check_user_name, check_start_time, check_end_time, 
      cancel_user_id, cancel_user_name, cancel_time, 
      cancel_reason, update_id, update_name, 
      update_time)
    values (#{checkRecordNo,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, #{gainBackNo,jdbcType=VARCHAR}, 
      #{vehicleId,jdbcType=BIGINT}, #{vehicleLicense,jdbcType=VARCHAR}, #{vehicleVin,jdbcType=VARCHAR}, 
      #{vehicleBrandCode,jdbcType=VARCHAR}, #{vehicleBrandName,jdbcType=VARCHAR}, #{vehicleModelCode,jdbcType=VARCHAR}, 
      #{vehicleModelName,jdbcType=VARCHAR}, #{oilType,jdbcType=TINYINT}, #{oilQuantity,jdbcType=TINYINT}, 
      #{powerQuantity,jdbcType=TINYINT}, #{mileageQuantity,jdbcType=INTEGER}, #{memo,jdbcType=VARCHAR}, 
      #{checkType,jdbcType=TINYINT}, #{checkStatus,jdbcType=TINYINT}, #{checkUserId,jdbcType=INTEGER}, 
      #{checkUserName,jdbcType=VARCHAR}, #{checkStartTime,jdbcType=TIMESTAMP}, #{checkEndTime,jdbcType=TIMESTAMP}, 
      #{cancelUserId,jdbcType=INTEGER}, #{cancelUserName,jdbcType=VARCHAR}, #{cancelTime,jdbcType=TIMESTAMP}, 
      #{cancelReason,jdbcType=VARCHAR}, #{updateId,jdbcType=INTEGER}, #{updateName,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.GovCarCheckRecord">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gov_car_check_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="checkRecordNo != null">
        check_record_no,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="gainBackNo != null">
        gain_back_no,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="vehicleLicense != null">
        vehicle_license,
      </if>
      <if test="vehicleVin != null">
        vehicle_vin,
      </if>
      <if test="vehicleBrandCode != null">
        vehicle_brand_code,
      </if>
      <if test="vehicleBrandName != null">
        vehicle_brand_name,
      </if>
      <if test="vehicleModelCode != null">
        vehicle_model_code,
      </if>
      <if test="vehicleModelName != null">
        vehicle_model_name,
      </if>
      <if test="oilType != null">
        oil_type,
      </if>
      <if test="oilQuantity != null">
        oil_quantity,
      </if>
      <if test="powerQuantity != null">
        power_quantity,
      </if>
      <if test="mileageQuantity != null">
        mileage_quantity,
      </if>
      <if test="memo != null">
        memo,
      </if>
      <if test="checkType != null">
        check_type,
      </if>
      <if test="checkStatus != null">
        check_status,
      </if>
      <if test="checkUserId != null">
        check_user_id,
      </if>
      <if test="checkUserName != null">
        check_user_name,
      </if>
      <if test="checkStartTime != null">
        check_start_time,
      </if>
      <if test="checkEndTime != null">
        check_end_time,
      </if>
      <if test="cancelUserId != null">
        cancel_user_id,
      </if>
      <if test="cancelUserName != null">
        cancel_user_name,
      </if>
      <if test="cancelTime != null">
        cancel_time,
      </if>
      <if test="cancelReason != null">
        cancel_reason,
      </if>
      <if test="updateId != null">
        update_id,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="checkRecordNo != null">
        #{checkRecordNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="gainBackNo != null">
        #{gainBackNo,jdbcType=VARCHAR},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleLicense != null">
        #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="vehicleVin != null">
        #{vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBrandCode != null">
        #{vehicleBrandCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBrandName != null">
        #{vehicleBrandName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelCode != null">
        #{vehicleModelCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelName != null">
        #{vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="oilType != null">
        #{oilType,jdbcType=TINYINT},
      </if>
      <if test="oilQuantity != null">
        #{oilQuantity,jdbcType=TINYINT},
      </if>
      <if test="powerQuantity != null">
        #{powerQuantity,jdbcType=TINYINT},
      </if>
      <if test="mileageQuantity != null">
        #{mileageQuantity,jdbcType=INTEGER},
      </if>
      <if test="memo != null">
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="checkType != null">
        #{checkType,jdbcType=TINYINT},
      </if>
      <if test="checkStatus != null">
        #{checkStatus,jdbcType=TINYINT},
      </if>
      <if test="checkUserId != null">
        #{checkUserId,jdbcType=INTEGER},
      </if>
      <if test="checkUserName != null">
        #{checkUserName,jdbcType=VARCHAR},
      </if>
      <if test="checkStartTime != null">
        #{checkStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checkEndTime != null">
        #{checkEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cancelUserId != null">
        #{cancelUserId,jdbcType=INTEGER},
      </if>
      <if test="cancelUserName != null">
        #{cancelUserName,jdbcType=VARCHAR},
      </if>
      <if test="cancelTime != null">
        #{cancelTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cancelReason != null">
        #{cancelReason,jdbcType=VARCHAR},
      </if>
      <if test="updateId != null">
        #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.GovCarCheckRecordExample" resultType="java.lang.Long">
    select count(*) from gov_car_check_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update gov_car_check_record
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=INTEGER},
      </if>
      <if test="row.checkRecordNo != null">
        check_record_no = #{row.checkRecordNo,jdbcType=VARCHAR},
      </if>
      <if test="row.orderNo != null">
        order_no = #{row.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="row.gainBackNo != null">
        gain_back_no = #{row.gainBackNo,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleId != null">
        vehicle_id = #{row.vehicleId,jdbcType=BIGINT},
      </if>
      <if test="row.vehicleLicense != null">
        vehicle_license = #{row.vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleVin != null">
        vehicle_vin = #{row.vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleBrandCode != null">
        vehicle_brand_code = #{row.vehicleBrandCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleBrandName != null">
        vehicle_brand_name = #{row.vehicleBrandName,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleModelCode != null">
        vehicle_model_code = #{row.vehicleModelCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleModelName != null">
        vehicle_model_name = #{row.vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="row.oilType != null">
        oil_type = #{row.oilType,jdbcType=TINYINT},
      </if>
      <if test="row.oilQuantity != null">
        oil_quantity = #{row.oilQuantity,jdbcType=TINYINT},
      </if>
      <if test="row.powerQuantity != null">
        power_quantity = #{row.powerQuantity,jdbcType=TINYINT},
      </if>
      <if test="row.mileageQuantity != null">
        mileage_quantity = #{row.mileageQuantity,jdbcType=INTEGER},
      </if>
      <if test="row.memo != null">
        memo = #{row.memo,jdbcType=VARCHAR},
      </if>
      <if test="row.checkType != null">
        check_type = #{row.checkType,jdbcType=TINYINT},
      </if>
      <if test="row.checkStatus != null">
        check_status = #{row.checkStatus,jdbcType=TINYINT},
      </if>
      <if test="row.checkUserId != null">
        check_user_id = #{row.checkUserId,jdbcType=INTEGER},
      </if>
      <if test="row.checkUserName != null">
        check_user_name = #{row.checkUserName,jdbcType=VARCHAR},
      </if>
      <if test="row.checkStartTime != null">
        check_start_time = #{row.checkStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.checkEndTime != null">
        check_end_time = #{row.checkEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.cancelUserId != null">
        cancel_user_id = #{row.cancelUserId,jdbcType=INTEGER},
      </if>
      <if test="row.cancelUserName != null">
        cancel_user_name = #{row.cancelUserName,jdbcType=VARCHAR},
      </if>
      <if test="row.cancelTime != null">
        cancel_time = #{row.cancelTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.cancelReason != null">
        cancel_reason = #{row.cancelReason,jdbcType=VARCHAR},
      </if>
      <if test="row.updateId != null">
        update_id = #{row.updateId,jdbcType=INTEGER},
      </if>
      <if test="row.updateName != null">
        update_name = #{row.updateName,jdbcType=VARCHAR},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update gov_car_check_record
    set id = #{row.id,jdbcType=INTEGER},
      check_record_no = #{row.checkRecordNo,jdbcType=VARCHAR},
      order_no = #{row.orderNo,jdbcType=VARCHAR},
      gain_back_no = #{row.gainBackNo,jdbcType=VARCHAR},
      vehicle_id = #{row.vehicleId,jdbcType=BIGINT},
      vehicle_license = #{row.vehicleLicense,jdbcType=VARCHAR},
      vehicle_vin = #{row.vehicleVin,jdbcType=VARCHAR},
      vehicle_brand_code = #{row.vehicleBrandCode,jdbcType=VARCHAR},
      vehicle_brand_name = #{row.vehicleBrandName,jdbcType=VARCHAR},
      vehicle_model_code = #{row.vehicleModelCode,jdbcType=VARCHAR},
      vehicle_model_name = #{row.vehicleModelName,jdbcType=VARCHAR},
      oil_type = #{row.oilType,jdbcType=TINYINT},
      oil_quantity = #{row.oilQuantity,jdbcType=TINYINT},
      power_quantity = #{row.powerQuantity,jdbcType=TINYINT},
      mileage_quantity = #{row.mileageQuantity,jdbcType=INTEGER},
      memo = #{row.memo,jdbcType=VARCHAR},
      check_type = #{row.checkType,jdbcType=TINYINT},
      check_status = #{row.checkStatus,jdbcType=TINYINT},
      check_user_id = #{row.checkUserId,jdbcType=INTEGER},
      check_user_name = #{row.checkUserName,jdbcType=VARCHAR},
      check_start_time = #{row.checkStartTime,jdbcType=TIMESTAMP},
      check_end_time = #{row.checkEndTime,jdbcType=TIMESTAMP},
      cancel_user_id = #{row.cancelUserId,jdbcType=INTEGER},
      cancel_user_name = #{row.cancelUserName,jdbcType=VARCHAR},
      cancel_time = #{row.cancelTime,jdbcType=TIMESTAMP},
      cancel_reason = #{row.cancelReason,jdbcType=VARCHAR},
      update_id = #{row.updateId,jdbcType=INTEGER},
      update_name = #{row.updateName,jdbcType=VARCHAR},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.GovCarCheckRecord">
    update gov_car_check_record
    <set>
      <if test="checkRecordNo != null">
        check_record_no = #{checkRecordNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="gainBackNo != null">
        gain_back_no = #{gainBackNo,jdbcType=VARCHAR},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleLicense != null">
        vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="vehicleVin != null">
        vehicle_vin = #{vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBrandCode != null">
        vehicle_brand_code = #{vehicleBrandCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBrandName != null">
        vehicle_brand_name = #{vehicleBrandName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelCode != null">
        vehicle_model_code = #{vehicleModelCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelName != null">
        vehicle_model_name = #{vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="oilType != null">
        oil_type = #{oilType,jdbcType=TINYINT},
      </if>
      <if test="oilQuantity != null">
        oil_quantity = #{oilQuantity,jdbcType=TINYINT},
      </if>
      <if test="powerQuantity != null">
        power_quantity = #{powerQuantity,jdbcType=TINYINT},
      </if>
      <if test="mileageQuantity != null">
        mileage_quantity = #{mileageQuantity,jdbcType=INTEGER},
      </if>
      <if test="memo != null">
        memo = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="checkType != null">
        check_type = #{checkType,jdbcType=TINYINT},
      </if>
      <if test="checkStatus != null">
        check_status = #{checkStatus,jdbcType=TINYINT},
      </if>
      <if test="checkUserId != null">
        check_user_id = #{checkUserId,jdbcType=INTEGER},
      </if>
      <if test="checkUserName != null">
        check_user_name = #{checkUserName,jdbcType=VARCHAR},
      </if>
      <if test="checkStartTime != null">
        check_start_time = #{checkStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="checkEndTime != null">
        check_end_time = #{checkEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cancelUserId != null">
        cancel_user_id = #{cancelUserId,jdbcType=INTEGER},
      </if>
      <if test="cancelUserName != null">
        cancel_user_name = #{cancelUserName,jdbcType=VARCHAR},
      </if>
      <if test="cancelTime != null">
        cancel_time = #{cancelTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cancelReason != null">
        cancel_reason = #{cancelReason,jdbcType=VARCHAR},
      </if>
      <if test="updateId != null">
        update_id = #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.GovCarCheckRecord">
    update gov_car_check_record
    set check_record_no = #{checkRecordNo,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      gain_back_no = #{gainBackNo,jdbcType=VARCHAR},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      vehicle_vin = #{vehicleVin,jdbcType=VARCHAR},
      vehicle_brand_code = #{vehicleBrandCode,jdbcType=VARCHAR},
      vehicle_brand_name = #{vehicleBrandName,jdbcType=VARCHAR},
      vehicle_model_code = #{vehicleModelCode,jdbcType=VARCHAR},
      vehicle_model_name = #{vehicleModelName,jdbcType=VARCHAR},
      oil_type = #{oilType,jdbcType=TINYINT},
      oil_quantity = #{oilQuantity,jdbcType=TINYINT},
      power_quantity = #{powerQuantity,jdbcType=TINYINT},
      mileage_quantity = #{mileageQuantity,jdbcType=INTEGER},
      memo = #{memo,jdbcType=VARCHAR},
      check_type = #{checkType,jdbcType=TINYINT},
      check_status = #{checkStatus,jdbcType=TINYINT},
      check_user_id = #{checkUserId,jdbcType=INTEGER},
      check_user_name = #{checkUserName,jdbcType=VARCHAR},
      check_start_time = #{checkStartTime,jdbcType=TIMESTAMP},
      check_end_time = #{checkEndTime,jdbcType=TIMESTAMP},
      cancel_user_id = #{cancelUserId,jdbcType=INTEGER},
      cancel_user_name = #{cancelUserName,jdbcType=VARCHAR},
      cancel_time = #{cancelTime,jdbcType=TIMESTAMP},
      cancel_reason = #{cancelReason,jdbcType=VARCHAR},
      update_id = #{updateId,jdbcType=INTEGER},
      update_name = #{updateName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>