<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ApplyPoolMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.ApplyPool">
    <id column="apply_id" jdbcType="INTEGER" property="applyId" />
    <result column="apply_type" jdbcType="TINYINT" property="applyType" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="create_city_code" jdbcType="INTEGER" property="createCityCode" />
    <result column="create_city_name" jdbcType="VARCHAR" property="createCityName" />
    <result column="relation_id" jdbcType="INTEGER" property="relationId" />
    <result column="relation_no" jdbcType="VARCHAR" property="relationNo" />
    <result column="apply_status_level_one" jdbcType="TINYINT" property="applyStatusLevelOne" />
    <result column="apply_status_level_two" jdbcType="TINYINT" property="applyStatusLevelTwo" />
    <result column="valid" jdbcType="BIT" property="valid" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="create_id" jdbcType="INTEGER" property="createId" />
    <result column="create_mobile" jdbcType="VARCHAR" property="createMobile" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="update_id" jdbcType="INTEGER" property="updateId" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="update_mobile" jdbcType="VARCHAR" property="updateMobile" />
    <result column="create_department_id" jdbcType="INTEGER" property="createDepartmentId" />
    <result column="create_department_name" jdbcType="VARCHAR" property="createDepartmentName" />
    <result column="update_department_id" jdbcType="INTEGER" property="updateDepartmentId" />
    <result column="update_department_name" jdbcType="VARCHAR" property="updateDepartmentName" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.izu.order.entity.mrcar.ApplyPool">
    <result column="param_a" jdbcType="LONGVARCHAR" property="paramA" />
    <result column="param_b" jdbcType="LONGVARCHAR" property="paramB" />
    <result column="param_c" jdbcType="LONGVARCHAR" property="paramC" />
    <result column="param_d" jdbcType="LONGVARCHAR" property="paramD" />
  </resultMap>
  <sql id="Base_Column_List">
    apply_id, apply_type, company_id, create_city_code, create_city_name, relation_id, 
    relation_no, apply_status_level_one, apply_status_level_two, valid, create_date, 
    update_date, create_id, create_mobile, create_name, update_id, update_name, update_mobile, 
    create_department_id, create_department_name, update_department_id, update_department_name
  </sql>
  <sql id="Blob_Column_List">
    param_a, param_b, param_c, param_d
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from apply_pool
    where apply_id = #{applyId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from apply_pool
    where apply_id = #{applyId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.ApplyPool">
    <selectKey keyProperty="applyId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into apply_pool (apply_type, company_id, create_city_code, 
      create_city_name, relation_id, relation_no, 
      apply_status_level_one, apply_status_level_two, 
      valid, create_date, update_date, 
      create_id, create_mobile, create_name, 
      update_id, update_name, update_mobile, 
      create_department_id, create_department_name, 
      update_department_id, update_department_name, 
      param_a, param_b, param_c, 
      param_d)
    values (#{applyType,jdbcType=TINYINT}, #{companyId,jdbcType=INTEGER}, #{createCityCode,jdbcType=INTEGER}, 
      #{createCityName,jdbcType=VARCHAR}, #{relationId,jdbcType=INTEGER}, #{relationNo,jdbcType=VARCHAR}, 
      #{applyStatusLevelOne,jdbcType=TINYINT}, #{applyStatusLevelTwo,jdbcType=TINYINT}, 
      #{valid,jdbcType=BIT}, #{createDate,jdbcType=TIMESTAMP}, #{updateDate,jdbcType=TIMESTAMP}, 
      #{createId,jdbcType=INTEGER}, #{createMobile,jdbcType=VARCHAR}, #{createName,jdbcType=VARCHAR}, 
      #{updateId,jdbcType=INTEGER}, #{updateName,jdbcType=VARCHAR}, #{updateMobile,jdbcType=VARCHAR}, 
      #{createDepartmentId,jdbcType=INTEGER}, #{createDepartmentName,jdbcType=VARCHAR}, 
      #{updateDepartmentId,jdbcType=INTEGER}, #{updateDepartmentName,jdbcType=VARCHAR}, 
      #{paramA,jdbcType=LONGVARCHAR}, #{paramB,jdbcType=LONGVARCHAR}, #{paramC,jdbcType=LONGVARCHAR}, 
      #{paramD,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.ApplyPool">
    <selectKey keyProperty="applyId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into apply_pool
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="applyType != null">
        apply_type,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="createCityCode != null">
        create_city_code,
      </if>
      <if test="createCityName != null">
        create_city_name,
      </if>
      <if test="relationId != null">
        relation_id,
      </if>
      <if test="relationNo != null">
        relation_no,
      </if>
      <if test="applyStatusLevelOne != null">
        apply_status_level_one,
      </if>
      <if test="applyStatusLevelTwo != null">
        apply_status_level_two,
      </if>
      <if test="valid != null">
        valid,
      </if>
      <if test="createDate != null">
        create_date,
      </if>
      <if test="updateDate != null">
        update_date,
      </if>
      <if test="createId != null">
        create_id,
      </if>
      <if test="createMobile != null">
        create_mobile,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="updateId != null">
        update_id,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="updateMobile != null">
        update_mobile,
      </if>
      <if test="createDepartmentId != null">
        create_department_id,
      </if>
      <if test="createDepartmentName != null">
        create_department_name,
      </if>
      <if test="updateDepartmentId != null">
        update_department_id,
      </if>
      <if test="updateDepartmentName != null">
        update_department_name,
      </if>
      <if test="paramA != null">
        param_a,
      </if>
      <if test="paramB != null">
        param_b,
      </if>
      <if test="paramC != null">
        param_c,
      </if>
      <if test="paramD != null">
        param_d,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="applyType != null">
        #{applyType,jdbcType=TINYINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="createCityCode != null">
        #{createCityCode,jdbcType=INTEGER},
      </if>
      <if test="createCityName != null">
        #{createCityName,jdbcType=VARCHAR},
      </if>
      <if test="relationId != null">
        #{relationId,jdbcType=INTEGER},
      </if>
      <if test="relationNo != null">
        #{relationNo,jdbcType=VARCHAR},
      </if>
      <if test="applyStatusLevelOne != null">
        #{applyStatusLevelOne,jdbcType=TINYINT},
      </if>
      <if test="applyStatusLevelTwo != null">
        #{applyStatusLevelTwo,jdbcType=TINYINT},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=BIT},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDate != null">
        #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createId != null">
        #{createId,jdbcType=INTEGER},
      </if>
      <if test="createMobile != null">
        #{createMobile,jdbcType=VARCHAR},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="updateId != null">
        #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateMobile != null">
        #{updateMobile,jdbcType=VARCHAR},
      </if>
      <if test="createDepartmentId != null">
        #{createDepartmentId,jdbcType=INTEGER},
      </if>
      <if test="createDepartmentName != null">
        #{createDepartmentName,jdbcType=VARCHAR},
      </if>
      <if test="updateDepartmentId != null">
        #{updateDepartmentId,jdbcType=INTEGER},
      </if>
      <if test="updateDepartmentName != null">
        #{updateDepartmentName,jdbcType=VARCHAR},
      </if>
      <if test="paramA != null">
        #{paramA,jdbcType=LONGVARCHAR},
      </if>
      <if test="paramB != null">
        #{paramB,jdbcType=LONGVARCHAR},
      </if>
      <if test="paramC != null">
        #{paramC,jdbcType=LONGVARCHAR},
      </if>
      <if test="paramD != null">
        #{paramD,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.ApplyPool">
    update apply_pool
    <set>
      <if test="applyType != null">
        apply_type = #{applyType,jdbcType=TINYINT},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="createCityCode != null">
        create_city_code = #{createCityCode,jdbcType=INTEGER},
      </if>
      <if test="createCityName != null">
        create_city_name = #{createCityName,jdbcType=VARCHAR},
      </if>
      <if test="relationId != null">
        relation_id = #{relationId,jdbcType=INTEGER},
      </if>
      <if test="relationNo != null">
        relation_no = #{relationNo,jdbcType=VARCHAR},
      </if>
      <if test="applyStatusLevelOne != null">
        apply_status_level_one = #{applyStatusLevelOne,jdbcType=TINYINT},
      </if>
      <if test="applyStatusLevelTwo != null">
        apply_status_level_two = #{applyStatusLevelTwo,jdbcType=TINYINT},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=BIT},
      </if>
      <if test="createDate != null">
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDate != null">
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createId != null">
        create_id = #{createId,jdbcType=INTEGER},
      </if>
      <if test="createMobile != null">
        create_mobile = #{createMobile,jdbcType=VARCHAR},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="updateId != null">
        update_id = #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateMobile != null">
        update_mobile = #{updateMobile,jdbcType=VARCHAR},
      </if>
      <if test="createDepartmentId != null">
        create_department_id = #{createDepartmentId,jdbcType=INTEGER},
      </if>
      <if test="createDepartmentName != null">
        create_department_name = #{createDepartmentName,jdbcType=VARCHAR},
      </if>
      <if test="updateDepartmentId != null">
        update_department_id = #{updateDepartmentId,jdbcType=INTEGER},
      </if>
      <if test="updateDepartmentName != null">
        update_department_name = #{updateDepartmentName,jdbcType=VARCHAR},
      </if>
      <if test="paramA != null">
        param_a = #{paramA,jdbcType=LONGVARCHAR},
      </if>
      <if test="paramB != null">
        param_b = #{paramB,jdbcType=LONGVARCHAR},
      </if>
      <if test="paramC != null">
        param_c = #{paramC,jdbcType=LONGVARCHAR},
      </if>
      <if test="paramD != null">
        param_d = #{paramD,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where apply_id = #{applyId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.izu.order.entity.mrcar.ApplyPool">
    update apply_pool
    set apply_type = #{applyType,jdbcType=TINYINT},
      company_id = #{companyId,jdbcType=INTEGER},
      create_city_code = #{createCityCode,jdbcType=INTEGER},
      create_city_name = #{createCityName,jdbcType=VARCHAR},
      relation_id = #{relationId,jdbcType=INTEGER},
      relation_no = #{relationNo,jdbcType=VARCHAR},
      apply_status_level_one = #{applyStatusLevelOne,jdbcType=TINYINT},
      apply_status_level_two = #{applyStatusLevelTwo,jdbcType=TINYINT},
      valid = #{valid,jdbcType=BIT},
      create_date = #{createDate,jdbcType=TIMESTAMP},
      update_date = #{updateDate,jdbcType=TIMESTAMP},
      create_id = #{createId,jdbcType=INTEGER},
      create_mobile = #{createMobile,jdbcType=VARCHAR},
      create_name = #{createName,jdbcType=VARCHAR},
      update_id = #{updateId,jdbcType=INTEGER},
      update_name = #{updateName,jdbcType=VARCHAR},
      update_mobile = #{updateMobile,jdbcType=VARCHAR},
      create_department_id = #{createDepartmentId,jdbcType=INTEGER},
      create_department_name = #{createDepartmentName,jdbcType=VARCHAR},
      update_department_id = #{updateDepartmentId,jdbcType=INTEGER},
      update_department_name = #{updateDepartmentName,jdbcType=VARCHAR},
      param_a = #{paramA,jdbcType=LONGVARCHAR},
      param_b = #{paramB,jdbcType=LONGVARCHAR},
      param_c = #{paramC,jdbcType=LONGVARCHAR},
      param_d = #{paramD,jdbcType=LONGVARCHAR}
    where apply_id = #{applyId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.ApplyPool">
    update apply_pool
    set apply_type = #{applyType,jdbcType=TINYINT},
      company_id = #{companyId,jdbcType=INTEGER},
      create_city_code = #{createCityCode,jdbcType=INTEGER},
      create_city_name = #{createCityName,jdbcType=VARCHAR},
      relation_id = #{relationId,jdbcType=INTEGER},
      relation_no = #{relationNo,jdbcType=VARCHAR},
      apply_status_level_one = #{applyStatusLevelOne,jdbcType=TINYINT},
      apply_status_level_two = #{applyStatusLevelTwo,jdbcType=TINYINT},
      valid = #{valid,jdbcType=BIT},
      create_date = #{createDate,jdbcType=TIMESTAMP},
      update_date = #{updateDate,jdbcType=TIMESTAMP},
      create_id = #{createId,jdbcType=INTEGER},
      create_mobile = #{createMobile,jdbcType=VARCHAR},
      create_name = #{createName,jdbcType=VARCHAR},
      update_id = #{updateId,jdbcType=INTEGER},
      update_name = #{updateName,jdbcType=VARCHAR},
      update_mobile = #{updateMobile,jdbcType=VARCHAR},
      create_department_id = #{createDepartmentId,jdbcType=INTEGER},
      create_department_name = #{createDepartmentName,jdbcType=VARCHAR},
      update_department_id = #{updateDepartmentId,jdbcType=INTEGER},
      update_department_name = #{updateDepartmentName,jdbcType=VARCHAR}
    where apply_id = #{applyId,jdbcType=INTEGER}
  </update>
</mapper>