<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.DemandOrderMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.DemandOrder">
    <id column="demand_order_id" jdbcType="INTEGER" property="demandOrderId" />
    <result column="create_id" jdbcType="INTEGER" property="createId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_id" jdbcType="INTEGER" property="updateId" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_tag" jdbcType="TINYINT" property="delTag" />
    <result column="demand_order_num" jdbcType="VARCHAR" property="demandOrderNum" />
    <result column="demand_order_status" jdbcType="TINYINT" property="demandOrderStatus" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="customer_phone" jdbcType="VARCHAR" property="customerPhone" />
    <result column="customer_city_code" jdbcType="VARCHAR" property="customerCityCode" />
    <result column="customer_city_name" jdbcType="VARCHAR" property="customerCityName" />
    <result column="customer_address" jdbcType="VARCHAR" property="customerAddress" />
    <result column="estimate_use_date" jdbcType="TIMESTAMP" property="estimateUseDate" />
    <result column="estimate_return_date" jdbcType="TIMESTAMP" property="estimateReturnDate" />
    <result column="hire_cycle" jdbcType="INTEGER" property="hireCycle" />
    <result column="hire_type" jdbcType="TINYINT" property="hireType" />
    <result column="estimate_amount" jdbcType="DECIMAL" property="estimateAmount" />
    <result column="other_explain" jdbcType="VARCHAR" property="otherExplain" />
    <result column="dispatch_order_time" jdbcType="TIMESTAMP" property="dispatchOrderTime" />
    <result column="contract_code" jdbcType="VARCHAR" property="contractCode" />
    <result column="order_source" jdbcType="INTEGER" property="orderSource" />
    <result column="return_address" jdbcType="VARCHAR" property="returnAddress" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    demand_order_id, create_id, create_name, create_time, update_id, update_name, update_time, 
    del_tag, demand_order_num, demand_order_status, customer_name, customer_phone, customer_city_code, 
    customer_city_name, customer_address, estimate_use_date, estimate_return_date, hire_cycle, 
    hire_type, estimate_amount, other_explain, dispatch_order_time, contract_code, order_source, 
    return_address
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.DemandOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from so_demand_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from so_demand_order
    where demand_order_id = #{demandOrderId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from so_demand_order
    where demand_order_id = #{demandOrderId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.order.entity.mrcar.DemandOrderExample">
    delete from so_demand_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.DemandOrder">
    <selectKey keyProperty="demandOrderId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into so_demand_order (create_id, create_name, create_time, 
      update_id, update_name, update_time, 
      del_tag, demand_order_num, demand_order_status, 
      customer_name, customer_phone, customer_city_code, 
      customer_city_name, customer_address, estimate_use_date, 
      estimate_return_date, hire_cycle, hire_type, 
      estimate_amount, other_explain, dispatch_order_time, 
      contract_code, order_source, return_address
      )
    values (#{createId,jdbcType=INTEGER}, #{createName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateId,jdbcType=INTEGER}, #{updateName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{delTag,jdbcType=TINYINT}, #{demandOrderNum,jdbcType=VARCHAR}, #{demandOrderStatus,jdbcType=TINYINT}, 
      #{customerName,jdbcType=VARCHAR}, #{customerPhone,jdbcType=VARCHAR}, #{customerCityCode,jdbcType=VARCHAR}, 
      #{customerCityName,jdbcType=VARCHAR}, #{customerAddress,jdbcType=VARCHAR}, #{estimateUseDate,jdbcType=TIMESTAMP}, 
      #{estimateReturnDate,jdbcType=TIMESTAMP}, #{hireCycle,jdbcType=INTEGER}, #{hireType,jdbcType=TINYINT}, 
      #{estimateAmount,jdbcType=DECIMAL}, #{otherExplain,jdbcType=VARCHAR}, #{dispatchOrderTime,jdbcType=TIMESTAMP}, 
      #{contractCode,jdbcType=VARCHAR}, #{orderSource,jdbcType=INTEGER}, #{returnAddress,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.DemandOrder">
    <selectKey keyProperty="demandOrderId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into so_demand_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createId != null">
        create_id,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateId != null">
        update_id,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="delTag != null">
        del_tag,
      </if>
      <if test="demandOrderNum != null">
        demand_order_num,
      </if>
      <if test="demandOrderStatus != null">
        demand_order_status,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="customerPhone != null">
        customer_phone,
      </if>
      <if test="customerCityCode != null">
        customer_city_code,
      </if>
      <if test="customerCityName != null">
        customer_city_name,
      </if>
      <if test="customerAddress != null">
        customer_address,
      </if>
      <if test="estimateUseDate != null">
        estimate_use_date,
      </if>
      <if test="estimateReturnDate != null">
        estimate_return_date,
      </if>
      <if test="hireCycle != null">
        hire_cycle,
      </if>
      <if test="hireType != null">
        hire_type,
      </if>
      <if test="estimateAmount != null">
        estimate_amount,
      </if>
      <if test="otherExplain != null">
        other_explain,
      </if>
      <if test="dispatchOrderTime != null">
        dispatch_order_time,
      </if>
      <if test="contractCode != null">
        contract_code,
      </if>
      <if test="orderSource != null">
        order_source,
      </if>
      <if test="returnAddress != null">
        return_address,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createId != null">
        #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delTag != null">
        #{delTag,jdbcType=TINYINT},
      </if>
      <if test="demandOrderNum != null">
        #{demandOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="demandOrderStatus != null">
        #{demandOrderStatus,jdbcType=TINYINT},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerPhone != null">
        #{customerPhone,jdbcType=VARCHAR},
      </if>
      <if test="customerCityCode != null">
        #{customerCityCode,jdbcType=VARCHAR},
      </if>
      <if test="customerCityName != null">
        #{customerCityName,jdbcType=VARCHAR},
      </if>
      <if test="customerAddress != null">
        #{customerAddress,jdbcType=VARCHAR},
      </if>
      <if test="estimateUseDate != null">
        #{estimateUseDate,jdbcType=TIMESTAMP},
      </if>
      <if test="estimateReturnDate != null">
        #{estimateReturnDate,jdbcType=TIMESTAMP},
      </if>
      <if test="hireCycle != null">
        #{hireCycle,jdbcType=INTEGER},
      </if>
      <if test="hireType != null">
        #{hireType,jdbcType=TINYINT},
      </if>
      <if test="estimateAmount != null">
        #{estimateAmount,jdbcType=DECIMAL},
      </if>
      <if test="otherExplain != null">
        #{otherExplain,jdbcType=VARCHAR},
      </if>
      <if test="dispatchOrderTime != null">
        #{dispatchOrderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contractCode != null">
        #{contractCode,jdbcType=VARCHAR},
      </if>
      <if test="orderSource != null">
        #{orderSource,jdbcType=INTEGER},
      </if>
      <if test="returnAddress != null">
        #{returnAddress,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.DemandOrderExample" resultType="java.lang.Long">
    select count(*) from so_demand_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update so_demand_order
    <set>
      <if test="row.demandOrderId != null">
        demand_order_id = #{row.demandOrderId,jdbcType=INTEGER},
      </if>
      <if test="row.createId != null">
        create_id = #{row.createId,jdbcType=INTEGER},
      </if>
      <if test="row.createName != null">
        create_name = #{row.createName,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateId != null">
        update_id = #{row.updateId,jdbcType=INTEGER},
      </if>
      <if test="row.updateName != null">
        update_name = #{row.updateName,jdbcType=VARCHAR},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.delTag != null">
        del_tag = #{row.delTag,jdbcType=TINYINT},
      </if>
      <if test="row.demandOrderNum != null">
        demand_order_num = #{row.demandOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="row.demandOrderStatus != null">
        demand_order_status = #{row.demandOrderStatus,jdbcType=TINYINT},
      </if>
      <if test="row.customerName != null">
        customer_name = #{row.customerName,jdbcType=VARCHAR},
      </if>
      <if test="row.customerPhone != null">
        customer_phone = #{row.customerPhone,jdbcType=VARCHAR},
      </if>
      <if test="row.customerCityCode != null">
        customer_city_code = #{row.customerCityCode,jdbcType=VARCHAR},
      </if>
      <if test="row.customerCityName != null">
        customer_city_name = #{row.customerCityName,jdbcType=VARCHAR},
      </if>
      <if test="row.customerAddress != null">
        customer_address = #{row.customerAddress,jdbcType=VARCHAR},
      </if>
      <if test="row.estimateUseDate != null">
        estimate_use_date = #{row.estimateUseDate,jdbcType=TIMESTAMP},
      </if>
      <if test="row.estimateReturnDate != null">
        estimate_return_date = #{row.estimateReturnDate,jdbcType=TIMESTAMP},
      </if>
      <if test="row.hireCycle != null">
        hire_cycle = #{row.hireCycle,jdbcType=INTEGER},
      </if>
      <if test="row.hireType != null">
        hire_type = #{row.hireType,jdbcType=TINYINT},
      </if>
      <if test="row.estimateAmount != null">
        estimate_amount = #{row.estimateAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.otherExplain != null">
        other_explain = #{row.otherExplain,jdbcType=VARCHAR},
      </if>
      <if test="row.dispatchOrderTime != null">
        dispatch_order_time = #{row.dispatchOrderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.contractCode != null">
        contract_code = #{row.contractCode,jdbcType=VARCHAR},
      </if>
      <if test="row.orderSource != null">
        order_source = #{row.orderSource,jdbcType=INTEGER},
      </if>
      <if test="row.returnAddress != null">
        return_address = #{row.returnAddress,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update so_demand_order
    set demand_order_id = #{row.demandOrderId,jdbcType=INTEGER},
      create_id = #{row.createId,jdbcType=INTEGER},
      create_name = #{row.createName,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_id = #{row.updateId,jdbcType=INTEGER},
      update_name = #{row.updateName,jdbcType=VARCHAR},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      del_tag = #{row.delTag,jdbcType=TINYINT},
      demand_order_num = #{row.demandOrderNum,jdbcType=VARCHAR},
      demand_order_status = #{row.demandOrderStatus,jdbcType=TINYINT},
      customer_name = #{row.customerName,jdbcType=VARCHAR},
      customer_phone = #{row.customerPhone,jdbcType=VARCHAR},
      customer_city_code = #{row.customerCityCode,jdbcType=VARCHAR},
      customer_city_name = #{row.customerCityName,jdbcType=VARCHAR},
      customer_address = #{row.customerAddress,jdbcType=VARCHAR},
      estimate_use_date = #{row.estimateUseDate,jdbcType=TIMESTAMP},
      estimate_return_date = #{row.estimateReturnDate,jdbcType=TIMESTAMP},
      hire_cycle = #{row.hireCycle,jdbcType=INTEGER},
      hire_type = #{row.hireType,jdbcType=TINYINT},
      estimate_amount = #{row.estimateAmount,jdbcType=DECIMAL},
      other_explain = #{row.otherExplain,jdbcType=VARCHAR},
      dispatch_order_time = #{row.dispatchOrderTime,jdbcType=TIMESTAMP},
      contract_code = #{row.contractCode,jdbcType=VARCHAR},
      order_source = #{row.orderSource,jdbcType=INTEGER},
      return_address = #{row.returnAddress,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.DemandOrder">
    update so_demand_order
    <set>
      <if test="createId != null">
        create_id = #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        update_id = #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delTag != null">
        del_tag = #{delTag,jdbcType=TINYINT},
      </if>
      <if test="demandOrderNum != null">
        demand_order_num = #{demandOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="demandOrderStatus != null">
        demand_order_status = #{demandOrderStatus,jdbcType=TINYINT},
      </if>
      <if test="customerName != null">
        customer_name = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerPhone != null">
        customer_phone = #{customerPhone,jdbcType=VARCHAR},
      </if>
      <if test="customerCityCode != null">
        customer_city_code = #{customerCityCode,jdbcType=VARCHAR},
      </if>
      <if test="customerCityName != null">
        customer_city_name = #{customerCityName,jdbcType=VARCHAR},
      </if>
      <if test="customerAddress != null">
        customer_address = #{customerAddress,jdbcType=VARCHAR},
      </if>
      <if test="estimateUseDate != null">
        estimate_use_date = #{estimateUseDate,jdbcType=TIMESTAMP},
      </if>
      <if test="estimateReturnDate != null">
        estimate_return_date = #{estimateReturnDate,jdbcType=TIMESTAMP},
      </if>
      <if test="hireCycle != null">
        hire_cycle = #{hireCycle,jdbcType=INTEGER},
      </if>
      <if test="hireType != null">
        hire_type = #{hireType,jdbcType=TINYINT},
      </if>
      <if test="estimateAmount != null">
        estimate_amount = #{estimateAmount,jdbcType=DECIMAL},
      </if>
      <if test="otherExplain != null">
        other_explain = #{otherExplain,jdbcType=VARCHAR},
      </if>
      <if test="dispatchOrderTime != null">
        dispatch_order_time = #{dispatchOrderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contractCode != null">
        contract_code = #{contractCode,jdbcType=VARCHAR},
      </if>
      <if test="orderSource != null">
        order_source = #{orderSource,jdbcType=INTEGER},
      </if>
      <if test="returnAddress != null">
        return_address = #{returnAddress,jdbcType=VARCHAR},
      </if>
    </set>
    where demand_order_id = #{demandOrderId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.DemandOrder">
    update so_demand_order
    set create_id = #{createId,jdbcType=INTEGER},
      create_name = #{createName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_id = #{updateId,jdbcType=INTEGER},
      update_name = #{updateName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      del_tag = #{delTag,jdbcType=TINYINT},
      demand_order_num = #{demandOrderNum,jdbcType=VARCHAR},
      demand_order_status = #{demandOrderStatus,jdbcType=TINYINT},
      customer_name = #{customerName,jdbcType=VARCHAR},
      customer_phone = #{customerPhone,jdbcType=VARCHAR},
      customer_city_code = #{customerCityCode,jdbcType=VARCHAR},
      customer_city_name = #{customerCityName,jdbcType=VARCHAR},
      customer_address = #{customerAddress,jdbcType=VARCHAR},
      estimate_use_date = #{estimateUseDate,jdbcType=TIMESTAMP},
      estimate_return_date = #{estimateReturnDate,jdbcType=TIMESTAMP},
      hire_cycle = #{hireCycle,jdbcType=INTEGER},
      hire_type = #{hireType,jdbcType=TINYINT},
      estimate_amount = #{estimateAmount,jdbcType=DECIMAL},
      other_explain = #{otherExplain,jdbcType=VARCHAR},
      dispatch_order_time = #{dispatchOrderTime,jdbcType=TIMESTAMP},
      contract_code = #{contractCode,jdbcType=VARCHAR},
      order_source = #{orderSource,jdbcType=INTEGER},
      return_address = #{returnAddress,jdbcType=VARCHAR}
    where demand_order_id = #{demandOrderId,jdbcType=INTEGER}
  </update>
</mapper>