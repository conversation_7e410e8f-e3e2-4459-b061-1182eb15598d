<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.CoOrderInfoDepositMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.CoOrderInfoDeposit">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="co_order_id" jdbcType="INTEGER" property="coOrderId" />
    <result column="order_num" jdbcType="VARCHAR" property="orderNum" />
    <result column="deposit_type" jdbcType="TINYINT" property="depositType" />
    <result column="deposit_amount" jdbcType="DECIMAL" property="depositAmount" />
    <result column="payment_method" jdbcType="TINYINT" property="paymentMethod" />
    <result column="payment_time" jdbcType="TIMESTAMP" property="paymentTime" />
    <result column="unfreeze_time" jdbcType="TIMESTAMP" property="unfreezeTime" />
    <result column="deduct_amount" jdbcType="DECIMAL" property="deductAmount" />
    <result column="deduct_status" jdbcType="TINYINT" property="deductStatus" />
    <result column="remaining_deposit" jdbcType="DECIMAL" property="remainingDeposit" />
    <result column="unfreeze_status" jdbcType="TINYINT" property="unfreezeStatus" />
    <result column="unfreeze_type" jdbcType="TINYINT" property="unfreezeType" />
    <result column="deduct_remark" jdbcType="VARCHAR" property="deductRemark" />
    <result column="unfreeze_remark" jdbcType="VARCHAR" property="unfreezeRemark" />
    <result column="unfreeze_amount" jdbcType="DECIMAL" property="unfreezeAmount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="serial_no" jdbcType="VARCHAR" property="serialNo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, co_order_id, order_num, deposit_type, deposit_amount, payment_method, payment_time, 
    unfreeze_time, deduct_amount, deduct_status, remaining_deposit, unfreeze_status, 
    unfreeze_type, deduct_remark, unfreeze_remark, unfreeze_amount, create_time, update_time, 
    serial_no
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.CoOrderInfoDepositExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from co_order_info_deposit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from co_order_info_deposit
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from co_order_info_deposit
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.CoOrderInfoDeposit">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into co_order_info_deposit (co_order_id, order_num, deposit_type, 
      deposit_amount, payment_method, payment_time, 
      unfreeze_time, deduct_amount, deduct_status, 
      remaining_deposit, unfreeze_status, unfreeze_type, 
      deduct_remark, unfreeze_remark, unfreeze_amount, 
      create_time, update_time, serial_no
      )
    values (#{coOrderId,jdbcType=INTEGER}, #{orderNum,jdbcType=VARCHAR}, #{depositType,jdbcType=TINYINT}, 
      #{depositAmount,jdbcType=DECIMAL}, #{paymentMethod,jdbcType=TINYINT}, #{paymentTime,jdbcType=TIMESTAMP}, 
      #{unfreezeTime,jdbcType=TIMESTAMP}, #{deductAmount,jdbcType=DECIMAL}, #{deductStatus,jdbcType=TINYINT}, 
      #{remainingDeposit,jdbcType=DECIMAL}, #{unfreezeStatus,jdbcType=TINYINT}, #{unfreezeType,jdbcType=TINYINT}, 
      #{deductRemark,jdbcType=VARCHAR}, #{unfreezeRemark,jdbcType=VARCHAR}, #{unfreezeAmount,jdbcType=DECIMAL}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{serialNo,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.CoOrderInfoDeposit">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into co_order_info_deposit
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="coOrderId != null">
        co_order_id,
      </if>
      <if test="orderNum != null">
        order_num,
      </if>
      <if test="depositType != null">
        deposit_type,
      </if>
      <if test="depositAmount != null">
        deposit_amount,
      </if>
      <if test="paymentMethod != null">
        payment_method,
      </if>
      <if test="paymentTime != null">
        payment_time,
      </if>
      <if test="unfreezeTime != null">
        unfreeze_time,
      </if>
      <if test="deductAmount != null">
        deduct_amount,
      </if>
      <if test="deductStatus != null">
        deduct_status,
      </if>
      <if test="remainingDeposit != null">
        remaining_deposit,
      </if>
      <if test="unfreezeStatus != null">
        unfreeze_status,
      </if>
      <if test="unfreezeType != null">
        unfreeze_type,
      </if>
      <if test="deductRemark != null">
        deduct_remark,
      </if>
      <if test="unfreezeRemark != null">
        unfreeze_remark,
      </if>
      <if test="unfreezeAmount != null">
        unfreeze_amount,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="serialNo != null">
        serial_no,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="coOrderId != null">
        #{coOrderId,jdbcType=INTEGER},
      </if>
      <if test="orderNum != null">
        #{orderNum,jdbcType=VARCHAR},
      </if>
      <if test="depositType != null">
        #{depositType,jdbcType=TINYINT},
      </if>
      <if test="depositAmount != null">
        #{depositAmount,jdbcType=DECIMAL},
      </if>
      <if test="paymentMethod != null">
        #{paymentMethod,jdbcType=TINYINT},
      </if>
      <if test="paymentTime != null">
        #{paymentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="unfreezeTime != null">
        #{unfreezeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deductAmount != null">
        #{deductAmount,jdbcType=DECIMAL},
      </if>
      <if test="deductStatus != null">
        #{deductStatus,jdbcType=TINYINT},
      </if>
      <if test="remainingDeposit != null">
        #{remainingDeposit,jdbcType=DECIMAL},
      </if>
      <if test="unfreezeStatus != null">
        #{unfreezeStatus,jdbcType=TINYINT},
      </if>
      <if test="unfreezeType != null">
        #{unfreezeType,jdbcType=TINYINT},
      </if>
      <if test="deductRemark != null">
        #{deductRemark,jdbcType=VARCHAR},
      </if>
      <if test="unfreezeRemark != null">
        #{unfreezeRemark,jdbcType=VARCHAR},
      </if>
      <if test="unfreezeAmount != null">
        #{unfreezeAmount,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="serialNo != null">
        #{serialNo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.CoOrderInfoDepositExample" resultType="java.lang.Long">
    select count(*) from co_order_info_deposit
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update co_order_info_deposit
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=INTEGER},
      </if>
      <if test="row.coOrderId != null">
        co_order_id = #{row.coOrderId,jdbcType=INTEGER},
      </if>
      <if test="row.orderNum != null">
        order_num = #{row.orderNum,jdbcType=VARCHAR},
      </if>
      <if test="row.depositType != null">
        deposit_type = #{row.depositType,jdbcType=TINYINT},
      </if>
      <if test="row.depositAmount != null">
        deposit_amount = #{row.depositAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.paymentMethod != null">
        payment_method = #{row.paymentMethod,jdbcType=TINYINT},
      </if>
      <if test="row.paymentTime != null">
        payment_time = #{row.paymentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.unfreezeTime != null">
        unfreeze_time = #{row.unfreezeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.deductAmount != null">
        deduct_amount = #{row.deductAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.deductStatus != null">
        deduct_status = #{row.deductStatus,jdbcType=TINYINT},
      </if>
      <if test="row.remainingDeposit != null">
        remaining_deposit = #{row.remainingDeposit,jdbcType=DECIMAL},
      </if>
      <if test="row.unfreezeStatus != null">
        unfreeze_status = #{row.unfreezeStatus,jdbcType=TINYINT},
      </if>
      <if test="row.unfreezeType != null">
        unfreeze_type = #{row.unfreezeType,jdbcType=TINYINT},
      </if>
      <if test="row.deductRemark != null">
        deduct_remark = #{row.deductRemark,jdbcType=VARCHAR},
      </if>
      <if test="row.unfreezeRemark != null">
        unfreeze_remark = #{row.unfreezeRemark,jdbcType=VARCHAR},
      </if>
      <if test="row.unfreezeAmount != null">
        unfreeze_amount = #{row.unfreezeAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.serialNo != null">
        serial_no = #{row.serialNo,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update co_order_info_deposit
    set id = #{row.id,jdbcType=INTEGER},
      co_order_id = #{row.coOrderId,jdbcType=INTEGER},
      order_num = #{row.orderNum,jdbcType=VARCHAR},
      deposit_type = #{row.depositType,jdbcType=TINYINT},
      deposit_amount = #{row.depositAmount,jdbcType=DECIMAL},
      payment_method = #{row.paymentMethod,jdbcType=TINYINT},
      payment_time = #{row.paymentTime,jdbcType=TIMESTAMP},
      unfreeze_time = #{row.unfreezeTime,jdbcType=TIMESTAMP},
      deduct_amount = #{row.deductAmount,jdbcType=DECIMAL},
      deduct_status = #{row.deductStatus,jdbcType=TINYINT},
      remaining_deposit = #{row.remainingDeposit,jdbcType=DECIMAL},
      unfreeze_status = #{row.unfreezeStatus,jdbcType=TINYINT},
      unfreeze_type = #{row.unfreezeType,jdbcType=TINYINT},
      deduct_remark = #{row.deductRemark,jdbcType=VARCHAR},
      unfreeze_remark = #{row.unfreezeRemark,jdbcType=VARCHAR},
      unfreeze_amount = #{row.unfreezeAmount,jdbcType=DECIMAL},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      serial_no = #{row.serialNo,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.CoOrderInfoDeposit">
    update co_order_info_deposit
    <set>
      <if test="coOrderId != null">
        co_order_id = #{coOrderId,jdbcType=INTEGER},
      </if>
      <if test="orderNum != null">
        order_num = #{orderNum,jdbcType=VARCHAR},
      </if>
      <if test="depositType != null">
        deposit_type = #{depositType,jdbcType=TINYINT},
      </if>
      <if test="depositAmount != null">
        deposit_amount = #{depositAmount,jdbcType=DECIMAL},
      </if>
      <if test="paymentMethod != null">
        payment_method = #{paymentMethod,jdbcType=TINYINT},
      </if>
      <if test="paymentTime != null">
        payment_time = #{paymentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="unfreezeTime != null">
        unfreeze_time = #{unfreezeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deductAmount != null">
        deduct_amount = #{deductAmount,jdbcType=DECIMAL},
      </if>
      <if test="deductStatus != null">
        deduct_status = #{deductStatus,jdbcType=TINYINT},
      </if>
      <if test="remainingDeposit != null">
        remaining_deposit = #{remainingDeposit,jdbcType=DECIMAL},
      </if>
      <if test="unfreezeStatus != null">
        unfreeze_status = #{unfreezeStatus,jdbcType=TINYINT},
      </if>
      <if test="unfreezeType != null">
        unfreeze_type = #{unfreezeType,jdbcType=TINYINT},
      </if>
      <if test="deductRemark != null">
        deduct_remark = #{deductRemark,jdbcType=VARCHAR},
      </if>
      <if test="unfreezeRemark != null">
        unfreeze_remark = #{unfreezeRemark,jdbcType=VARCHAR},
      </if>
      <if test="unfreezeAmount != null">
        unfreeze_amount = #{unfreezeAmount,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="serialNo != null">
        serial_no = #{serialNo,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.CoOrderInfoDeposit">
    update co_order_info_deposit
    set co_order_id = #{coOrderId,jdbcType=INTEGER},
      order_num = #{orderNum,jdbcType=VARCHAR},
      deposit_type = #{depositType,jdbcType=TINYINT},
      deposit_amount = #{depositAmount,jdbcType=DECIMAL},
      payment_method = #{paymentMethod,jdbcType=TINYINT},
      payment_time = #{paymentTime,jdbcType=TIMESTAMP},
      unfreeze_time = #{unfreezeTime,jdbcType=TIMESTAMP},
      deduct_amount = #{deductAmount,jdbcType=DECIMAL},
      deduct_status = #{deductStatus,jdbcType=TINYINT},
      remaining_deposit = #{remainingDeposit,jdbcType=DECIMAL},
      unfreeze_status = #{unfreezeStatus,jdbcType=TINYINT},
      unfreeze_type = #{unfreezeType,jdbcType=TINYINT},
      deduct_remark = #{deductRemark,jdbcType=VARCHAR},
      unfreeze_remark = #{unfreezeRemark,jdbcType=VARCHAR},
      unfreeze_amount = #{unfreezeAmount,jdbcType=DECIMAL},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      serial_no = #{serialNo,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>