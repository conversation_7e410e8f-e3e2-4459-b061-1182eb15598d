<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.OrderInternalExtendMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.OrderInternalExtend">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_apply_no" jdbcType="VARCHAR" property="orderApplyNo" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="vehicle_parallel_order" jdbcType="BIT" property="vehicleParallelOrder" />
    <result column="vehicle_parallel_order_no" jdbcType="VARCHAR" property="vehicleParallelOrderNo" />
    <result column="driver_parallel_order" jdbcType="BIT" property="driverParallelOrder" />
    <result column="driver_parallel_order_no" jdbcType="VARCHAR" property="driverParallelOrderNo" />
    <result column="order_termination_type" jdbcType="INTEGER" property="orderTerminationType" />
    <result column="sign_confirm_config_flag" jdbcType="TINYINT" property="signConfirmConfigFlag" />
    <result column="sign_confirm_url" jdbcType="VARCHAR" property="signConfirmUrl" />
    <result column="sign_confirm_date" jdbcType="TIMESTAMP" property="signConfirmDate" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_apply_no, order_no, vehicle_parallel_order, vehicle_parallel_order_no, 
    driver_parallel_order, driver_parallel_order_no, order_termination_type, sign_confirm_config_flag, 
    sign_confirm_url, sign_confirm_date, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.OrderInternalExtendExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from order_internal_extend
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_internal_extend
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from order_internal_extend
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.order.entity.mrcar.OrderInternalExtendExample">
    delete from order_internal_extend
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.OrderInternalExtend">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_internal_extend (order_apply_no, order_no, vehicle_parallel_order, 
      vehicle_parallel_order_no, driver_parallel_order, 
      driver_parallel_order_no, order_termination_type, 
      sign_confirm_config_flag, sign_confirm_url, 
      sign_confirm_date, create_time, update_time
      )
    values (#{orderApplyNo,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, #{vehicleParallelOrder,jdbcType=BIT}, 
      #{vehicleParallelOrderNo,jdbcType=VARCHAR}, #{driverParallelOrder,jdbcType=BIT}, 
      #{driverParallelOrderNo,jdbcType=VARCHAR}, #{orderTerminationType,jdbcType=INTEGER}, 
      #{signConfirmConfigFlag,jdbcType=TINYINT}, #{signConfirmUrl,jdbcType=VARCHAR}, 
      #{signConfirmDate,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.OrderInternalExtend">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_internal_extend
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderApplyNo != null">
        order_apply_no,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="vehicleParallelOrder != null">
        vehicle_parallel_order,
      </if>
      <if test="vehicleParallelOrderNo != null">
        vehicle_parallel_order_no,
      </if>
      <if test="driverParallelOrder != null">
        driver_parallel_order,
      </if>
      <if test="driverParallelOrderNo != null">
        driver_parallel_order_no,
      </if>
      <if test="orderTerminationType != null">
        order_termination_type,
      </if>
      <if test="signConfirmConfigFlag != null">
        sign_confirm_config_flag,
      </if>
      <if test="signConfirmUrl != null">
        sign_confirm_url,
      </if>
      <if test="signConfirmDate != null">
        sign_confirm_date,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderApplyNo != null">
        #{orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="vehicleParallelOrder != null">
        #{vehicleParallelOrder,jdbcType=BIT},
      </if>
      <if test="vehicleParallelOrderNo != null">
        #{vehicleParallelOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="driverParallelOrder != null">
        #{driverParallelOrder,jdbcType=BIT},
      </if>
      <if test="driverParallelOrderNo != null">
        #{driverParallelOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderTerminationType != null">
        #{orderTerminationType,jdbcType=INTEGER},
      </if>
      <if test="signConfirmConfigFlag != null">
        #{signConfirmConfigFlag,jdbcType=TINYINT},
      </if>
      <if test="signConfirmUrl != null">
        #{signConfirmUrl,jdbcType=VARCHAR},
      </if>
      <if test="signConfirmDate != null">
        #{signConfirmDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.OrderInternalExtendExample" resultType="java.lang.Long">
    select count(*) from order_internal_extend
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update order_internal_extend
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.orderApplyNo != null">
        order_apply_no = #{row.orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="row.orderNo != null">
        order_no = #{row.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleParallelOrder != null">
        vehicle_parallel_order = #{row.vehicleParallelOrder,jdbcType=BIT},
      </if>
      <if test="row.vehicleParallelOrderNo != null">
        vehicle_parallel_order_no = #{row.vehicleParallelOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="row.driverParallelOrder != null">
        driver_parallel_order = #{row.driverParallelOrder,jdbcType=BIT},
      </if>
      <if test="row.driverParallelOrderNo != null">
        driver_parallel_order_no = #{row.driverParallelOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="row.orderTerminationType != null">
        order_termination_type = #{row.orderTerminationType,jdbcType=INTEGER},
      </if>
      <if test="row.signConfirmConfigFlag != null">
        sign_confirm_config_flag = #{row.signConfirmConfigFlag,jdbcType=TINYINT},
      </if>
      <if test="row.signConfirmUrl != null">
        sign_confirm_url = #{row.signConfirmUrl,jdbcType=VARCHAR},
      </if>
      <if test="row.signConfirmDate != null">
        sign_confirm_date = #{row.signConfirmDate,jdbcType=TIMESTAMP},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update order_internal_extend
    set id = #{row.id,jdbcType=BIGINT},
      order_apply_no = #{row.orderApplyNo,jdbcType=VARCHAR},
      order_no = #{row.orderNo,jdbcType=VARCHAR},
      vehicle_parallel_order = #{row.vehicleParallelOrder,jdbcType=BIT},
      vehicle_parallel_order_no = #{row.vehicleParallelOrderNo,jdbcType=VARCHAR},
      driver_parallel_order = #{row.driverParallelOrder,jdbcType=BIT},
      driver_parallel_order_no = #{row.driverParallelOrderNo,jdbcType=VARCHAR},
      order_termination_type = #{row.orderTerminationType,jdbcType=INTEGER},
      sign_confirm_config_flag = #{row.signConfirmConfigFlag,jdbcType=TINYINT},
      sign_confirm_url = #{row.signConfirmUrl,jdbcType=VARCHAR},
      sign_confirm_date = #{row.signConfirmDate,jdbcType=TIMESTAMP},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.OrderInternalExtend">
    update order_internal_extend
    <set>
      <if test="orderApplyNo != null">
        order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="vehicleParallelOrder != null">
        vehicle_parallel_order = #{vehicleParallelOrder,jdbcType=BIT},
      </if>
      <if test="vehicleParallelOrderNo != null">
        vehicle_parallel_order_no = #{vehicleParallelOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="driverParallelOrder != null">
        driver_parallel_order = #{driverParallelOrder,jdbcType=BIT},
      </if>
      <if test="driverParallelOrderNo != null">
        driver_parallel_order_no = #{driverParallelOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderTerminationType != null">
        order_termination_type = #{orderTerminationType,jdbcType=INTEGER},
      </if>
      <if test="signConfirmConfigFlag != null">
        sign_confirm_config_flag = #{signConfirmConfigFlag,jdbcType=TINYINT},
      </if>
      <if test="signConfirmUrl != null">
        sign_confirm_url = #{signConfirmUrl,jdbcType=VARCHAR},
      </if>
      <if test="signConfirmDate != null">
        sign_confirm_date = #{signConfirmDate,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.OrderInternalExtend">
    update order_internal_extend
    set order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      vehicle_parallel_order = #{vehicleParallelOrder,jdbcType=BIT},
      vehicle_parallel_order_no = #{vehicleParallelOrderNo,jdbcType=VARCHAR},
      driver_parallel_order = #{driverParallelOrder,jdbcType=BIT},
      driver_parallel_order_no = #{driverParallelOrderNo,jdbcType=VARCHAR},
      order_termination_type = #{orderTerminationType,jdbcType=INTEGER},
      sign_confirm_config_flag = #{signConfirmConfigFlag,jdbcType=TINYINT},
      sign_confirm_url = #{signConfirmUrl,jdbcType=VARCHAR},
      sign_confirm_date = #{signConfirmDate,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>