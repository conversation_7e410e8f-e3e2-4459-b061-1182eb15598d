<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.BusOrderRouteSnapshotMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.BusOrderRouteSnapshot">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="route_id" jdbcType="INTEGER" property="routeId" />
    <result column="route_code" jdbcType="VARCHAR" property="routeCode" />
    <result column="station_name" jdbcType="VARCHAR" property="stationName" />
    <result column="station_address" jdbcType="VARCHAR" property="stationAddress" />
    <result column="latitude" jdbcType="DECIMAL" property="latitude" />
    <result column="longitude" jdbcType="DECIMAL" property="longitude" />
    <result column="station_type" jdbcType="INTEGER" property="stationType" />
    <result column="province_id" jdbcType="INTEGER" property="provinceId" />
    <result column="province_name" jdbcType="VARCHAR" property="provinceName" />
    <result column="city_code" jdbcType="INTEGER" property="cityCode" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_id" jdbcType="INTEGER" property="createId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_no, route_id, route_code, station_name, station_address, latitude, longitude, 
    station_type, province_id, province_name, city_code, city_name, create_time, create_id, 
    create_name, sort
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.BusOrderRouteSnapshotExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from bus_order_route_snapshot
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bus_order_route_snapshot
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from bus_order_route_snapshot
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.BusOrderRouteSnapshot">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into bus_order_route_snapshot (order_no, route_id, route_code, 
      station_name, station_address, latitude, 
      longitude, station_type, province_id, 
      province_name, city_code, city_name, 
      create_time, create_id, create_name, 
      sort)
    values (#{orderNo,jdbcType=VARCHAR}, #{routeId,jdbcType=INTEGER}, #{routeCode,jdbcType=VARCHAR}, 
      #{stationName,jdbcType=VARCHAR}, #{stationAddress,jdbcType=VARCHAR}, #{latitude,jdbcType=DECIMAL}, 
      #{longitude,jdbcType=DECIMAL}, #{stationType,jdbcType=INTEGER}, #{provinceId,jdbcType=INTEGER}, 
      #{provinceName,jdbcType=VARCHAR}, #{cityCode,jdbcType=INTEGER}, #{cityName,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{createId,jdbcType=INTEGER}, #{createName,jdbcType=VARCHAR}, 
      #{sort,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.BusOrderRouteSnapshot">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into bus_order_route_snapshot
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="routeId != null">
        route_id,
      </if>
      <if test="routeCode != null">
        route_code,
      </if>
      <if test="stationName != null">
        station_name,
      </if>
      <if test="stationAddress != null">
        station_address,
      </if>
      <if test="latitude != null">
        latitude,
      </if>
      <if test="longitude != null">
        longitude,
      </if>
      <if test="stationType != null">
        station_type,
      </if>
      <if test="provinceId != null">
        province_id,
      </if>
      <if test="provinceName != null">
        province_name,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="cityName != null">
        city_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createId != null">
        create_id,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="sort != null">
        sort,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="routeId != null">
        #{routeId,jdbcType=INTEGER},
      </if>
      <if test="routeCode != null">
        #{routeCode,jdbcType=VARCHAR},
      </if>
      <if test="stationName != null">
        #{stationName,jdbcType=VARCHAR},
      </if>
      <if test="stationAddress != null">
        #{stationAddress,jdbcType=VARCHAR},
      </if>
      <if test="latitude != null">
        #{latitude,jdbcType=DECIMAL},
      </if>
      <if test="longitude != null">
        #{longitude,jdbcType=DECIMAL},
      </if>
      <if test="stationType != null">
        #{stationType,jdbcType=INTEGER},
      </if>
      <if test="provinceId != null">
        #{provinceId,jdbcType=INTEGER},
      </if>
      <if test="provinceName != null">
        #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=INTEGER},
      </if>
      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createId != null">
        #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map">
    update bus_order_route_snapshot
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=INTEGER},
      </if>
      <if test="row.orderNo != null">
        order_no = #{row.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="row.routeId != null">
        route_id = #{row.routeId,jdbcType=INTEGER},
      </if>
      <if test="row.routeCode != null">
        route_code = #{row.routeCode,jdbcType=VARCHAR},
      </if>
      <if test="row.stationName != null">
        station_name = #{row.stationName,jdbcType=VARCHAR},
      </if>
      <if test="row.stationAddress != null">
        station_address = #{row.stationAddress,jdbcType=VARCHAR},
      </if>
      <if test="row.latitude != null">
        latitude = #{row.latitude,jdbcType=DECIMAL},
      </if>
      <if test="row.longitude != null">
        longitude = #{row.longitude,jdbcType=DECIMAL},
      </if>
      <if test="row.stationType != null">
        station_type = #{row.stationType,jdbcType=INTEGER},
      </if>
      <if test="row.provinceId != null">
        province_id = #{row.provinceId,jdbcType=INTEGER},
      </if>
      <if test="row.provinceName != null">
        province_name = #{row.provinceName,jdbcType=VARCHAR},
      </if>
      <if test="row.cityCode != null">
        city_code = #{row.cityCode,jdbcType=INTEGER},
      </if>
      <if test="row.cityName != null">
        city_name = #{row.cityName,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.createId != null">
        create_id = #{row.createId,jdbcType=INTEGER},
      </if>
      <if test="row.createName != null">
        create_name = #{row.createName,jdbcType=VARCHAR},
      </if>
      <if test="row.sort != null">
        sort = #{row.sort,jdbcType=INTEGER},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update bus_order_route_snapshot
    set id = #{row.id,jdbcType=INTEGER},
      order_no = #{row.orderNo,jdbcType=VARCHAR},
      route_id = #{row.routeId,jdbcType=INTEGER},
      route_code = #{row.routeCode,jdbcType=VARCHAR},
      station_name = #{row.stationName,jdbcType=VARCHAR},
      station_address = #{row.stationAddress,jdbcType=VARCHAR},
      latitude = #{row.latitude,jdbcType=DECIMAL},
      longitude = #{row.longitude,jdbcType=DECIMAL},
      station_type = #{row.stationType,jdbcType=INTEGER},
      province_id = #{row.provinceId,jdbcType=INTEGER},
      province_name = #{row.provinceName,jdbcType=VARCHAR},
      city_code = #{row.cityCode,jdbcType=INTEGER},
      city_name = #{row.cityName,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      create_id = #{row.createId,jdbcType=INTEGER},
      create_name = #{row.createName,jdbcType=VARCHAR},
      sort = #{row.sort,jdbcType=INTEGER}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.BusOrderRouteSnapshot">
    update bus_order_route_snapshot
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="routeId != null">
        route_id = #{routeId,jdbcType=INTEGER},
      </if>
      <if test="routeCode != null">
        route_code = #{routeCode,jdbcType=VARCHAR},
      </if>
      <if test="stationName != null">
        station_name = #{stationName,jdbcType=VARCHAR},
      </if>
      <if test="stationAddress != null">
        station_address = #{stationAddress,jdbcType=VARCHAR},
      </if>
      <if test="latitude != null">
        latitude = #{latitude,jdbcType=DECIMAL},
      </if>
      <if test="longitude != null">
        longitude = #{longitude,jdbcType=DECIMAL},
      </if>
      <if test="stationType != null">
        station_type = #{stationType,jdbcType=INTEGER},
      </if>
      <if test="provinceId != null">
        province_id = #{provinceId,jdbcType=INTEGER},
      </if>
      <if test="provinceName != null">
        province_name = #{provinceName,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        city_code = #{cityCode,jdbcType=INTEGER},
      </if>
      <if test="cityName != null">
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createId != null">
        create_id = #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.BusOrderRouteSnapshot">
    update bus_order_route_snapshot
    set order_no = #{orderNo,jdbcType=VARCHAR},
      route_id = #{routeId,jdbcType=INTEGER},
      route_code = #{routeCode,jdbcType=VARCHAR},
      station_name = #{stationName,jdbcType=VARCHAR},
      station_address = #{stationAddress,jdbcType=VARCHAR},
      latitude = #{latitude,jdbcType=DECIMAL},
      longitude = #{longitude,jdbcType=DECIMAL},
      station_type = #{stationType,jdbcType=INTEGER},
      province_id = #{provinceId,jdbcType=INTEGER},
      province_name = #{provinceName,jdbcType=VARCHAR},
      city_code = #{cityCode,jdbcType=INTEGER},
      city_name = #{cityName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_id = #{createId,jdbcType=INTEGER},
      create_name = #{createName,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>