<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.BillAdjustPriceLogMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.BillAdjustPriceLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_apply_no" jdbcType="VARCHAR" property="orderApplyNo" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="reduction_amount" jdbcType="DECIMAL" property="reductionAmount" />
    <result column="other_amount" jdbcType="DECIMAL" property="otherAmount" />
    <result column="price_explain" jdbcType="VARCHAR" property="priceExplain" />
    <result column="before_should_pay_amount" jdbcType="DECIMAL" property="beforeShouldPayAmount" />
    <result column="after_should_pay_amount" jdbcType="DECIMAL" property="afterShouldPayAmount" />
    <result column="creter_id" jdbcType="INTEGER" property="creterId" />
    <result column="creter_name" jdbcType="VARCHAR" property="creterName" />
    <result column="creter_phone" jdbcType="VARCHAR" property="creterPhone" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_apply_no, order_no, reduction_amount, other_amount, price_explain, before_should_pay_amount, 
    after_should_pay_amount, creter_id, creter_name, creter_phone, create_date
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.BillAdjustPriceLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from bill_adjust_price_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bill_adjust_price_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bill_adjust_price_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.order.entity.mrcar.BillAdjustPriceLogExample">
    delete from bill_adjust_price_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.BillAdjustPriceLog">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into bill_adjust_price_log (order_apply_no, order_no, reduction_amount, 
      other_amount, price_explain, before_should_pay_amount, 
      after_should_pay_amount, creter_id, creter_name, 
      creter_phone, create_date)
    values (#{orderApplyNo,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, #{reductionAmount,jdbcType=DECIMAL}, 
      #{otherAmount,jdbcType=DECIMAL}, #{priceExplain,jdbcType=VARCHAR}, #{beforeShouldPayAmount,jdbcType=DECIMAL}, 
      #{afterShouldPayAmount,jdbcType=DECIMAL}, #{creterId,jdbcType=INTEGER}, #{creterName,jdbcType=VARCHAR}, 
      #{creterPhone,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.BillAdjustPriceLog">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into bill_adjust_price_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderApplyNo != null">
        order_apply_no,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="reductionAmount != null">
        reduction_amount,
      </if>
      <if test="otherAmount != null">
        other_amount,
      </if>
      <if test="priceExplain != null">
        price_explain,
      </if>
      <if test="beforeShouldPayAmount != null">
        before_should_pay_amount,
      </if>
      <if test="afterShouldPayAmount != null">
        after_should_pay_amount,
      </if>
      <if test="creterId != null">
        creter_id,
      </if>
      <if test="creterName != null">
        creter_name,
      </if>
      <if test="creterPhone != null">
        creter_phone,
      </if>
      <if test="createDate != null">
        create_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderApplyNo != null">
        #{orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="reductionAmount != null">
        #{reductionAmount,jdbcType=DECIMAL},
      </if>
      <if test="otherAmount != null">
        #{otherAmount,jdbcType=DECIMAL},
      </if>
      <if test="priceExplain != null">
        #{priceExplain,jdbcType=VARCHAR},
      </if>
      <if test="beforeShouldPayAmount != null">
        #{beforeShouldPayAmount,jdbcType=DECIMAL},
      </if>
      <if test="afterShouldPayAmount != null">
        #{afterShouldPayAmount,jdbcType=DECIMAL},
      </if>
      <if test="creterId != null">
        #{creterId,jdbcType=INTEGER},
      </if>
      <if test="creterName != null">
        #{creterName,jdbcType=VARCHAR},
      </if>
      <if test="creterPhone != null">
        #{creterPhone,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.BillAdjustPriceLogExample" resultType="java.lang.Long">
    select count(*) from bill_adjust_price_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update bill_adjust_price_log
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.orderApplyNo != null">
        order_apply_no = #{row.orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="row.orderNo != null">
        order_no = #{row.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="row.reductionAmount != null">
        reduction_amount = #{row.reductionAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.otherAmount != null">
        other_amount = #{row.otherAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.priceExplain != null">
        price_explain = #{row.priceExplain,jdbcType=VARCHAR},
      </if>
      <if test="row.beforeShouldPayAmount != null">
        before_should_pay_amount = #{row.beforeShouldPayAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.afterShouldPayAmount != null">
        after_should_pay_amount = #{row.afterShouldPayAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.creterId != null">
        creter_id = #{row.creterId,jdbcType=INTEGER},
      </if>
      <if test="row.creterName != null">
        creter_name = #{row.creterName,jdbcType=VARCHAR},
      </if>
      <if test="row.creterPhone != null">
        creter_phone = #{row.creterPhone,jdbcType=VARCHAR},
      </if>
      <if test="row.createDate != null">
        create_date = #{row.createDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update bill_adjust_price_log
    set id = #{row.id,jdbcType=BIGINT},
      order_apply_no = #{row.orderApplyNo,jdbcType=VARCHAR},
      order_no = #{row.orderNo,jdbcType=VARCHAR},
      reduction_amount = #{row.reductionAmount,jdbcType=DECIMAL},
      other_amount = #{row.otherAmount,jdbcType=DECIMAL},
      price_explain = #{row.priceExplain,jdbcType=VARCHAR},
      before_should_pay_amount = #{row.beforeShouldPayAmount,jdbcType=DECIMAL},
      after_should_pay_amount = #{row.afterShouldPayAmount,jdbcType=DECIMAL},
      creter_id = #{row.creterId,jdbcType=INTEGER},
      creter_name = #{row.creterName,jdbcType=VARCHAR},
      creter_phone = #{row.creterPhone,jdbcType=VARCHAR},
      create_date = #{row.createDate,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.BillAdjustPriceLog">
    update bill_adjust_price_log
    <set>
      <if test="orderApplyNo != null">
        order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="reductionAmount != null">
        reduction_amount = #{reductionAmount,jdbcType=DECIMAL},
      </if>
      <if test="otherAmount != null">
        other_amount = #{otherAmount,jdbcType=DECIMAL},
      </if>
      <if test="priceExplain != null">
        price_explain = #{priceExplain,jdbcType=VARCHAR},
      </if>
      <if test="beforeShouldPayAmount != null">
        before_should_pay_amount = #{beforeShouldPayAmount,jdbcType=DECIMAL},
      </if>
      <if test="afterShouldPayAmount != null">
        after_should_pay_amount = #{afterShouldPayAmount,jdbcType=DECIMAL},
      </if>
      <if test="creterId != null">
        creter_id = #{creterId,jdbcType=INTEGER},
      </if>
      <if test="creterName != null">
        creter_name = #{creterName,jdbcType=VARCHAR},
      </if>
      <if test="creterPhone != null">
        creter_phone = #{creterPhone,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.BillAdjustPriceLog">
    update bill_adjust_price_log
    set order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      reduction_amount = #{reductionAmount,jdbcType=DECIMAL},
      other_amount = #{otherAmount,jdbcType=DECIMAL},
      price_explain = #{priceExplain,jdbcType=VARCHAR},
      before_should_pay_amount = #{beforeShouldPayAmount,jdbcType=DECIMAL},
      after_should_pay_amount = #{afterShouldPayAmount,jdbcType=DECIMAL},
      creter_id = #{creterId,jdbcType=INTEGER},
      creter_name = #{creterName,jdbcType=VARCHAR},
      creter_phone = #{creterPhone,jdbcType=VARCHAR},
      create_date = #{createDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>