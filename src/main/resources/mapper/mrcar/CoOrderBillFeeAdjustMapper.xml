<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.CoOrderBillFeeAdjustMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.CoOrderBillFeeAdjust">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_bill_id" jdbcType="INTEGER" property="orderBillId" />
    <result column="fee_code" jdbcType="VARCHAR" property="feeCode" />
    <result column="fee_name" jdbcType="VARCHAR" property="feeName" />
    <result column="before_fee_amount" jdbcType="DECIMAL" property="beforeFeeAmount" />
    <result column="after_fee_amount" jdbcType="DECIMAL" property="afterFeeAmount" />
    <result column="create_id" jdbcType="INTEGER" property="createId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_bill_id, fee_code, fee_name, before_fee_amount, after_fee_amount, create_id, 
    create_name, create_time
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.CoOrderBillFeeAdjustExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from co_order_bill_fee_adjust
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from co_order_bill_fee_adjust
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from co_order_bill_fee_adjust
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.CoOrderBillFeeAdjust">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into co_order_bill_fee_adjust (order_bill_id, fee_code, fee_name, 
      before_fee_amount, after_fee_amount, create_id, 
      create_name, create_time)
    values (#{orderBillId,jdbcType=INTEGER}, #{feeCode,jdbcType=VARCHAR}, #{feeName,jdbcType=VARCHAR}, 
      #{beforeFeeAmount,jdbcType=DECIMAL}, #{afterFeeAmount,jdbcType=DECIMAL}, #{createId,jdbcType=INTEGER}, 
      #{createName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.CoOrderBillFeeAdjust">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into co_order_bill_fee_adjust
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderBillId != null">
        order_bill_id,
      </if>
      <if test="feeCode != null">
        fee_code,
      </if>
      <if test="feeName != null">
        fee_name,
      </if>
      <if test="beforeFeeAmount != null">
        before_fee_amount,
      </if>
      <if test="afterFeeAmount != null">
        after_fee_amount,
      </if>
      <if test="createId != null">
        create_id,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderBillId != null">
        #{orderBillId,jdbcType=INTEGER},
      </if>
      <if test="feeCode != null">
        #{feeCode,jdbcType=VARCHAR},
      </if>
      <if test="feeName != null">
        #{feeName,jdbcType=VARCHAR},
      </if>
      <if test="beforeFeeAmount != null">
        #{beforeFeeAmount,jdbcType=DECIMAL},
      </if>
      <if test="afterFeeAmount != null">
        #{afterFeeAmount,jdbcType=DECIMAL},
      </if>
      <if test="createId != null">
        #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.CoOrderBillFeeAdjustExample" resultType="java.lang.Long">
    select count(*) from co_order_bill_fee_adjust
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.CoOrderBillFeeAdjust">
    update co_order_bill_fee_adjust
    <set>
      <if test="orderBillId != null">
        order_bill_id = #{orderBillId,jdbcType=INTEGER},
      </if>
      <if test="feeCode != null">
        fee_code = #{feeCode,jdbcType=VARCHAR},
      </if>
      <if test="feeName != null">
        fee_name = #{feeName,jdbcType=VARCHAR},
      </if>
      <if test="beforeFeeAmount != null">
        before_fee_amount = #{beforeFeeAmount,jdbcType=DECIMAL},
      </if>
      <if test="afterFeeAmount != null">
        after_fee_amount = #{afterFeeAmount,jdbcType=DECIMAL},
      </if>
      <if test="createId != null">
        create_id = #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.CoOrderBillFeeAdjust">
    update co_order_bill_fee_adjust
    set order_bill_id = #{orderBillId,jdbcType=INTEGER},
      fee_code = #{feeCode,jdbcType=VARCHAR},
      fee_name = #{feeName,jdbcType=VARCHAR},
      before_fee_amount = #{beforeFeeAmount,jdbcType=DECIMAL},
      after_fee_amount = #{afterFeeAmount,jdbcType=DECIMAL},
      create_id = #{createId,jdbcType=INTEGER},
      create_name = #{createName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>