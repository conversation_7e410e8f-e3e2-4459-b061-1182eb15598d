<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.OrderConfigSnapshotMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.OrderConfigSnapshot">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="co_order_id" jdbcType="INTEGER" property="coOrderId" />
    <result column="out_mile_price" jdbcType="DECIMAL" property="outMilePrice" />
    <result column="day_price" jdbcType="DECIMAL" property="dayPrice" />
    <result column="rent_rule_hour" jdbcType="INTEGER" property="rentRuleHour" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="pickup_delivery_fee" jdbcType="DECIMAL" property="pickupDeliveryFee" />
    <result column="billing_method" jdbcType="INTEGER" property="billingMethod" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, co_order_id, out_mile_price, day_price, rent_rule_hour, create_time, pickup_delivery_fee, 
    billing_method
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.OrderConfigSnapshotExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from co_order_config_snapshot
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from co_order_config_snapshot
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from co_order_config_snapshot
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.order.entity.mrcar.OrderConfigSnapshotExample">
    delete from co_order_config_snapshot
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.OrderConfigSnapshot">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into co_order_config_snapshot (co_order_id, out_mile_price, day_price, 
      rent_rule_hour, create_time, pickup_delivery_fee, 
      billing_method)
    values (#{coOrderId,jdbcType=INTEGER}, #{outMilePrice,jdbcType=DECIMAL}, #{dayPrice,jdbcType=DECIMAL}, 
      #{rentRuleHour,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{pickupDeliveryFee,jdbcType=DECIMAL}, 
      #{billingMethod,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.OrderConfigSnapshot">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into co_order_config_snapshot
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="coOrderId != null">
        co_order_id,
      </if>
      <if test="outMilePrice != null">
        out_mile_price,
      </if>
      <if test="dayPrice != null">
        day_price,
      </if>
      <if test="rentRuleHour != null">
        rent_rule_hour,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="pickupDeliveryFee != null">
        pickup_delivery_fee,
      </if>
      <if test="billingMethod != null">
        billing_method,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="coOrderId != null">
        #{coOrderId,jdbcType=INTEGER},
      </if>
      <if test="outMilePrice != null">
        #{outMilePrice,jdbcType=DECIMAL},
      </if>
      <if test="dayPrice != null">
        #{dayPrice,jdbcType=DECIMAL},
      </if>
      <if test="rentRuleHour != null">
        #{rentRuleHour,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="pickupDeliveryFee != null">
        #{pickupDeliveryFee,jdbcType=DECIMAL},
      </if>
      <if test="billingMethod != null">
        #{billingMethod,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.OrderConfigSnapshotExample" resultType="java.lang.Long">
    select count(*) from co_order_config_snapshot
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update co_order_config_snapshot
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=INTEGER},
      </if>
      <if test="row.coOrderId != null">
        co_order_id = #{row.coOrderId,jdbcType=INTEGER},
      </if>
      <if test="row.outMilePrice != null">
        out_mile_price = #{row.outMilePrice,jdbcType=DECIMAL},
      </if>
      <if test="row.dayPrice != null">
        day_price = #{row.dayPrice,jdbcType=DECIMAL},
      </if>
      <if test="row.rentRuleHour != null">
        rent_rule_hour = #{row.rentRuleHour,jdbcType=INTEGER},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.pickupDeliveryFee != null">
        pickup_delivery_fee = #{row.pickupDeliveryFee,jdbcType=DECIMAL},
      </if>
      <if test="row.billingMethod != null">
        billing_method = #{row.billingMethod,jdbcType=INTEGER},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update co_order_config_snapshot
    set id = #{row.id,jdbcType=INTEGER},
      co_order_id = #{row.coOrderId,jdbcType=INTEGER},
      out_mile_price = #{row.outMilePrice,jdbcType=DECIMAL},
      day_price = #{row.dayPrice,jdbcType=DECIMAL},
      rent_rule_hour = #{row.rentRuleHour,jdbcType=INTEGER},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      pickup_delivery_fee = #{row.pickupDeliveryFee,jdbcType=DECIMAL},
      billing_method = #{row.billingMethod,jdbcType=INTEGER}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.OrderConfigSnapshot">
    update co_order_config_snapshot
    <set>
      <if test="coOrderId != null">
        co_order_id = #{coOrderId,jdbcType=INTEGER},
      </if>
      <if test="outMilePrice != null">
        out_mile_price = #{outMilePrice,jdbcType=DECIMAL},
      </if>
      <if test="dayPrice != null">
        day_price = #{dayPrice,jdbcType=DECIMAL},
      </if>
      <if test="rentRuleHour != null">
        rent_rule_hour = #{rentRuleHour,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="pickupDeliveryFee != null">
        pickup_delivery_fee = #{pickupDeliveryFee,jdbcType=DECIMAL},
      </if>
      <if test="billingMethod != null">
        billing_method = #{billingMethod,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.OrderConfigSnapshot">
    update co_order_config_snapshot
    set co_order_id = #{coOrderId,jdbcType=INTEGER},
      out_mile_price = #{outMilePrice,jdbcType=DECIMAL},
      day_price = #{dayPrice,jdbcType=DECIMAL},
      rent_rule_hour = #{rentRuleHour,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      pickup_delivery_fee = #{pickupDeliveryFee,jdbcType=DECIMAL},
      billing_method = #{billingMethod,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>