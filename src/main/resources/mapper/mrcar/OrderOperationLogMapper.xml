<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.OrderOperationLogMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.OrderOperationLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="INTEGER" property="orderId" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="operation_type" jdbcType="INTEGER" property="operationType" />
    <result column="operation_description" jdbcType="VARCHAR" property="operationDescription" />
    <result column="operation_time" jdbcType="TIMESTAMP" property="operationTime" />
    <result column="order_status" jdbcType="INTEGER" property="orderStatus" />
    <result column="order_status_name" jdbcType="VARCHAR" property="orderStatusName" />
    <result column="operator_id" jdbcType="BIGINT" property="operatorId" />
    <result column="operator_code" jdbcType="VARCHAR" property="operatorCode" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="operator_mobile" jdbcType="VARCHAR" property="operatorMobile" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_id, order_no, operation_type, operation_description, operation_time, order_status, 
    order_status_name, operator_id, operator_code, operator_name, operator_mobile, create_time
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.OrderOperationLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from order_operation_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_operation_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from order_operation_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.order.entity.mrcar.OrderOperationLogExample">
    delete from order_operation_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.OrderOperationLog">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_operation_log (order_id, order_no, operation_type, 
      operation_description, operation_time, 
      order_status, order_status_name, operator_id, 
      operator_code, operator_name, operator_mobile, 
      create_time)
    values (#{orderId,jdbcType=INTEGER}, #{orderNo,jdbcType=VARCHAR}, #{operationType,jdbcType=INTEGER}, 
      #{operationDescription,jdbcType=VARCHAR}, #{operationTime,jdbcType=TIMESTAMP}, 
      #{orderStatus,jdbcType=INTEGER}, #{orderStatusName,jdbcType=VARCHAR}, #{operatorId,jdbcType=BIGINT}, 
      #{operatorCode,jdbcType=VARCHAR}, #{operatorName,jdbcType=VARCHAR}, #{operatorMobile,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.OrderOperationLog">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_operation_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="operationType != null">
        operation_type,
      </if>
      <if test="operationDescription != null">
        operation_description,
      </if>
      <if test="operationTime != null">
        operation_time,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="orderStatusName != null">
        order_status_name,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="operatorCode != null">
        operator_code,
      </if>
      <if test="operatorName != null">
        operator_name,
      </if>
      <if test="operatorMobile != null">
        operator_mobile,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="operationType != null">
        #{operationType,jdbcType=INTEGER},
      </if>
      <if test="operationDescription != null">
        #{operationDescription,jdbcType=VARCHAR},
      </if>
      <if test="operationTime != null">
        #{operationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="orderStatusName != null">
        #{orderStatusName,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=BIGINT},
      </if>
      <if test="operatorCode != null">
        #{operatorCode,jdbcType=VARCHAR},
      </if>
      <if test="operatorName != null">
        #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="operatorMobile != null">
        #{operatorMobile,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.OrderOperationLogExample" resultType="java.lang.Long">
    select count(*) from order_operation_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update order_operation_log
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.orderId != null">
        order_id = #{row.orderId,jdbcType=INTEGER},
      </if>
      <if test="row.orderNo != null">
        order_no = #{row.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="row.operationType != null">
        operation_type = #{row.operationType,jdbcType=INTEGER},
      </if>
      <if test="row.operationDescription != null">
        operation_description = #{row.operationDescription,jdbcType=VARCHAR},
      </if>
      <if test="row.operationTime != null">
        operation_time = #{row.operationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.orderStatus != null">
        order_status = #{row.orderStatus,jdbcType=INTEGER},
      </if>
      <if test="row.orderStatusName != null">
        order_status_name = #{row.orderStatusName,jdbcType=VARCHAR},
      </if>
      <if test="row.operatorId != null">
        operator_id = #{row.operatorId,jdbcType=BIGINT},
      </if>
      <if test="row.operatorCode != null">
        operator_code = #{row.operatorCode,jdbcType=VARCHAR},
      </if>
      <if test="row.operatorName != null">
        operator_name = #{row.operatorName,jdbcType=VARCHAR},
      </if>
      <if test="row.operatorMobile != null">
        operator_mobile = #{row.operatorMobile,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update order_operation_log
    set id = #{row.id,jdbcType=BIGINT},
      order_id = #{row.orderId,jdbcType=INTEGER},
      order_no = #{row.orderNo,jdbcType=VARCHAR},
      operation_type = #{row.operationType,jdbcType=INTEGER},
      operation_description = #{row.operationDescription,jdbcType=VARCHAR},
      operation_time = #{row.operationTime,jdbcType=TIMESTAMP},
      order_status = #{row.orderStatus,jdbcType=INTEGER},
      order_status_name = #{row.orderStatusName,jdbcType=VARCHAR},
      operator_id = #{row.operatorId,jdbcType=BIGINT},
      operator_code = #{row.operatorCode,jdbcType=VARCHAR},
      operator_name = #{row.operatorName,jdbcType=VARCHAR},
      operator_mobile = #{row.operatorMobile,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.OrderOperationLog">
    update order_operation_log
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="operationType != null">
        operation_type = #{operationType,jdbcType=INTEGER},
      </if>
      <if test="operationDescription != null">
        operation_description = #{operationDescription,jdbcType=VARCHAR},
      </if>
      <if test="operationTime != null">
        operation_time = #{operationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="orderStatusName != null">
        order_status_name = #{orderStatusName,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=BIGINT},
      </if>
      <if test="operatorCode != null">
        operator_code = #{operatorCode,jdbcType=VARCHAR},
      </if>
      <if test="operatorName != null">
        operator_name = #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="operatorMobile != null">
        operator_mobile = #{operatorMobile,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.OrderOperationLog">
    update order_operation_log
    set order_id = #{orderId,jdbcType=INTEGER},
      order_no = #{orderNo,jdbcType=VARCHAR},
      operation_type = #{operationType,jdbcType=INTEGER},
      operation_description = #{operationDescription,jdbcType=VARCHAR},
      operation_time = #{operationTime,jdbcType=TIMESTAMP},
      order_status = #{orderStatus,jdbcType=INTEGER},
      order_status_name = #{orderStatusName,jdbcType=VARCHAR},
      operator_id = #{operatorId,jdbcType=BIGINT},
      operator_code = #{operatorCode,jdbcType=VARCHAR},
      operator_name = #{operatorName,jdbcType=VARCHAR},
      operator_mobile = #{operatorMobile,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>