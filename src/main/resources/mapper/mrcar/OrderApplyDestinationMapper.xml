<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.OrderApplyDestinationMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.OrderApplyDestination">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="address_type" jdbcType="INTEGER" property="addressType" />
    <result column="trip_type" jdbcType="INTEGER" property="tripType" />
    <result column="order_apply_no" jdbcType="VARCHAR" property="orderApplyNo" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="booking_end_long_addr" jdbcType="VARCHAR" property="bookingEndLongAddr" />
    <result column="booking_end_short_addr" jdbcType="VARCHAR" property="bookingEndShortAddr" />
    <result column="booking_end_point" jdbcType="VARCHAR" property="bookingEndPoint" />
    <result column="booking_end_city_code" jdbcType="VARCHAR" property="bookingEndCityCode" />
    <result column="booking_end_city_name" jdbcType="VARCHAR" property="bookingEndCityName" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="end_address_label" jdbcType="VARCHAR" property="endAddressLabel" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, address_type, trip_type, order_apply_no, order_no, booking_end_long_addr, booking_end_short_addr, 
    booking_end_point, booking_end_city_code, booking_end_city_name, sort, create_time, 
    update_time, end_address_label
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.OrderApplyDestinationExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from order_apply_destination
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_apply_destination
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from order_apply_destination
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.order.entity.mrcar.OrderApplyDestinationExample">
    delete from order_apply_destination
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.OrderApplyDestination">
    insert into order_apply_destination (id, address_type, trip_type, 
      order_apply_no, order_no, booking_end_long_addr, 
      booking_end_short_addr, booking_end_point, booking_end_city_code, 
      booking_end_city_name, sort, create_time, 
      update_time, end_address_label)
    values (#{id,jdbcType=INTEGER}, #{addressType,jdbcType=INTEGER}, #{tripType,jdbcType=INTEGER}, 
      #{orderApplyNo,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, #{bookingEndLongAddr,jdbcType=VARCHAR}, 
      #{bookingEndShortAddr,jdbcType=VARCHAR}, #{bookingEndPoint,jdbcType=VARCHAR}, #{bookingEndCityCode,jdbcType=VARCHAR}, 
      #{bookingEndCityName,jdbcType=VARCHAR}, #{sort,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{endAddressLabel,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.OrderApplyDestination">
    insert into order_apply_destination
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="addressType != null">
        address_type,
      </if>
      <if test="tripType != null">
        trip_type,
      </if>
      <if test="orderApplyNo != null">
        order_apply_no,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="bookingEndLongAddr != null">
        booking_end_long_addr,
      </if>
      <if test="bookingEndShortAddr != null">
        booking_end_short_addr,
      </if>
      <if test="bookingEndPoint != null">
        booking_end_point,
      </if>
      <if test="bookingEndCityCode != null">
        booking_end_city_code,
      </if>
      <if test="bookingEndCityName != null">
        booking_end_city_name,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="endAddressLabel != null">
        end_address_label,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="addressType != null">
        #{addressType,jdbcType=INTEGER},
      </if>
      <if test="tripType != null">
        #{tripType,jdbcType=INTEGER},
      </if>
      <if test="orderApplyNo != null">
        #{orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="bookingEndLongAddr != null">
        #{bookingEndLongAddr,jdbcType=VARCHAR},
      </if>
      <if test="bookingEndShortAddr != null">
        #{bookingEndShortAddr,jdbcType=VARCHAR},
      </if>
      <if test="bookingEndPoint != null">
        #{bookingEndPoint,jdbcType=VARCHAR},
      </if>
      <if test="bookingEndCityCode != null">
        #{bookingEndCityCode,jdbcType=VARCHAR},
      </if>
      <if test="bookingEndCityName != null">
        #{bookingEndCityName,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endAddressLabel != null">
        #{endAddressLabel,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.OrderApplyDestinationExample" resultType="java.lang.Long">
    select count(*) from order_apply_destination
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update order_apply_destination
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=INTEGER},
      </if>
      <if test="row.addressType != null">
        address_type = #{row.addressType,jdbcType=INTEGER},
      </if>
      <if test="row.tripType != null">
        trip_type = #{row.tripType,jdbcType=INTEGER},
      </if>
      <if test="row.orderApplyNo != null">
        order_apply_no = #{row.orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="row.orderNo != null">
        order_no = #{row.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="row.bookingEndLongAddr != null">
        booking_end_long_addr = #{row.bookingEndLongAddr,jdbcType=VARCHAR},
      </if>
      <if test="row.bookingEndShortAddr != null">
        booking_end_short_addr = #{row.bookingEndShortAddr,jdbcType=VARCHAR},
      </if>
      <if test="row.bookingEndPoint != null">
        booking_end_point = #{row.bookingEndPoint,jdbcType=VARCHAR},
      </if>
      <if test="row.bookingEndCityCode != null">
        booking_end_city_code = #{row.bookingEndCityCode,jdbcType=VARCHAR},
      </if>
      <if test="row.bookingEndCityName != null">
        booking_end_city_name = #{row.bookingEndCityName,jdbcType=VARCHAR},
      </if>
      <if test="row.sort != null">
        sort = #{row.sort,jdbcType=INTEGER},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.endAddressLabel != null">
        end_address_label = #{row.endAddressLabel,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update order_apply_destination
    set id = #{row.id,jdbcType=INTEGER},
      address_type = #{row.addressType,jdbcType=INTEGER},
      trip_type = #{row.tripType,jdbcType=INTEGER},
      order_apply_no = #{row.orderApplyNo,jdbcType=VARCHAR},
      order_no = #{row.orderNo,jdbcType=VARCHAR},
      booking_end_long_addr = #{row.bookingEndLongAddr,jdbcType=VARCHAR},
      booking_end_short_addr = #{row.bookingEndShortAddr,jdbcType=VARCHAR},
      booking_end_point = #{row.bookingEndPoint,jdbcType=VARCHAR},
      booking_end_city_code = #{row.bookingEndCityCode,jdbcType=VARCHAR},
      booking_end_city_name = #{row.bookingEndCityName,jdbcType=VARCHAR},
      sort = #{row.sort,jdbcType=INTEGER},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      end_address_label = #{row.endAddressLabel,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.OrderApplyDestination">
    update order_apply_destination
    <set>
      <if test="addressType != null">
        address_type = #{addressType,jdbcType=INTEGER},
      </if>
      <if test="tripType != null">
        trip_type = #{tripType,jdbcType=INTEGER},
      </if>
      <if test="orderApplyNo != null">
        order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="bookingEndLongAddr != null">
        booking_end_long_addr = #{bookingEndLongAddr,jdbcType=VARCHAR},
      </if>
      <if test="bookingEndShortAddr != null">
        booking_end_short_addr = #{bookingEndShortAddr,jdbcType=VARCHAR},
      </if>
      <if test="bookingEndPoint != null">
        booking_end_point = #{bookingEndPoint,jdbcType=VARCHAR},
      </if>
      <if test="bookingEndCityCode != null">
        booking_end_city_code = #{bookingEndCityCode,jdbcType=VARCHAR},
      </if>
      <if test="bookingEndCityName != null">
        booking_end_city_name = #{bookingEndCityName,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endAddressLabel != null">
        end_address_label = #{endAddressLabel,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.OrderApplyDestination">
    update order_apply_destination
    set address_type = #{addressType,jdbcType=INTEGER},
      trip_type = #{tripType,jdbcType=INTEGER},
      order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      booking_end_long_addr = #{bookingEndLongAddr,jdbcType=VARCHAR},
      booking_end_short_addr = #{bookingEndShortAddr,jdbcType=VARCHAR},
      booking_end_point = #{bookingEndPoint,jdbcType=VARCHAR},
      booking_end_city_code = #{bookingEndCityCode,jdbcType=VARCHAR},
      booking_end_city_name = #{bookingEndCityName,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      end_address_label = #{endAddressLabel,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>