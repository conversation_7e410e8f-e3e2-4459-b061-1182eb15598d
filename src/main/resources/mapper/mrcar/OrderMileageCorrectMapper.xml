<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.OrderMileageCorrectMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.OrderMileageCorrect">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="correct_no" jdbcType="VARCHAR" property="correctNo" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="order_type" jdbcType="TINYINT" property="orderType" />
    <result column="service_code" jdbcType="VARCHAR" property="serviceCode" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="passenger_name" jdbcType="VARCHAR" property="passengerName" />
    <result column="passenger_phone" jdbcType="VARCHAR" property="passengerPhone" />
    <result column="assign_car_license" jdbcType="VARCHAR" property="assignCarLicense" />
    <result column="mileage_before" jdbcType="DECIMAL" property="mileageBefore" />
    <result column="mileage_source" jdbcType="TINYINT" property="mileageSource" />
    <result column="mileage_correct" jdbcType="DECIMAL" property="mileageCorrect" />
    <result column="fact_start_date" jdbcType="TIMESTAMP" property="factStartDate" />
    <result column="fact_end_date" jdbcType="TIMESTAMP" property="factEndDate" />
    <result column="deal_name" jdbcType="VARCHAR" property="dealName" />
    <result column="deal_time" jdbcType="TIMESTAMP" property="dealTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_name, create_time, update_name, update_time, correct_no, order_no, order_type, 
    service_code, status, company_code, company_name, passenger_name, passenger_phone, 
    assign_car_license, mileage_before, mileage_source, mileage_correct, fact_start_date, 
    fact_end_date, deal_name, deal_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_mileage_correct
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from order_mileage_correct
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.OrderMileageCorrect">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_mileage_correct (create_name, create_time, update_name, 
      update_time, correct_no, order_no, 
      order_type, service_code, status, 
      company_code, company_name, passenger_name, 
      passenger_phone, assign_car_license, mileage_before, 
      mileage_source, mileage_correct, fact_start_date, 
      fact_end_date, deal_name, deal_time
      )
    values (#{createName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateName,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{correctNo,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, 
      #{orderType,jdbcType=TINYINT}, #{serviceCode,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, 
      #{companyCode,jdbcType=VARCHAR}, #{companyName,jdbcType=VARCHAR}, #{passengerName,jdbcType=VARCHAR}, 
      #{passengerPhone,jdbcType=VARCHAR}, #{assignCarLicense,jdbcType=VARCHAR}, #{mileageBefore,jdbcType=DECIMAL}, 
      #{mileageSource,jdbcType=TINYINT}, #{mileageCorrect,jdbcType=DECIMAL}, #{factStartDate,jdbcType=TIMESTAMP}, 
      #{factEndDate,jdbcType=TIMESTAMP}, #{dealName,jdbcType=VARCHAR}, #{dealTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.OrderMileageCorrect">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_mileage_correct
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createName != null">
        create_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="correctNo != null">
        correct_no,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="serviceCode != null">
        service_code,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="passengerName != null">
        passenger_name,
      </if>
      <if test="passengerPhone != null">
        passenger_phone,
      </if>
      <if test="assignCarLicense != null">
        assign_car_license,
      </if>
      <if test="mileageBefore != null">
        mileage_before,
      </if>
      <if test="mileageSource != null">
        mileage_source,
      </if>
      <if test="mileageCorrect != null">
        mileage_correct,
      </if>
      <if test="factStartDate != null">
        fact_start_date,
      </if>
      <if test="factEndDate != null">
        fact_end_date,
      </if>
      <if test="dealName != null">
        deal_name,
      </if>
      <if test="dealTime != null">
        deal_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="correctNo != null">
        #{correctNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=TINYINT},
      </if>
      <if test="serviceCode != null">
        #{serviceCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="passengerName != null">
        #{passengerName,jdbcType=VARCHAR},
      </if>
      <if test="passengerPhone != null">
        #{passengerPhone,jdbcType=VARCHAR},
      </if>
      <if test="assignCarLicense != null">
        #{assignCarLicense,jdbcType=VARCHAR},
      </if>
      <if test="mileageBefore != null">
        #{mileageBefore,jdbcType=DECIMAL},
      </if>
      <if test="mileageSource != null">
        #{mileageSource,jdbcType=TINYINT},
      </if>
      <if test="mileageCorrect != null">
        #{mileageCorrect,jdbcType=DECIMAL},
      </if>
      <if test="factStartDate != null">
        #{factStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="factEndDate != null">
        #{factEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="dealName != null">
        #{dealName,jdbcType=VARCHAR},
      </if>
      <if test="dealTime != null">
        #{dealTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.OrderMileageCorrect">
    update order_mileage_correct
    <set>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="correctNo != null">
        correct_no = #{correctNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=TINYINT},
      </if>
      <if test="serviceCode != null">
        service_code = #{serviceCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="passengerName != null">
        passenger_name = #{passengerName,jdbcType=VARCHAR},
      </if>
      <if test="passengerPhone != null">
        passenger_phone = #{passengerPhone,jdbcType=VARCHAR},
      </if>
      <if test="assignCarLicense != null">
        assign_car_license = #{assignCarLicense,jdbcType=VARCHAR},
      </if>
      <if test="mileageBefore != null">
        mileage_before = #{mileageBefore,jdbcType=DECIMAL},
      </if>
      <if test="mileageSource != null">
        mileage_source = #{mileageSource,jdbcType=TINYINT},
      </if>
      <if test="mileageCorrect != null">
        mileage_correct = #{mileageCorrect,jdbcType=DECIMAL},
      </if>
      <if test="factStartDate != null">
        fact_start_date = #{factStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="factEndDate != null">
        fact_end_date = #{factEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="dealName != null">
        deal_name = #{dealName,jdbcType=VARCHAR},
      </if>
      <if test="dealTime != null">
        deal_time = #{dealTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.OrderMileageCorrect">
    update order_mileage_correct
    set create_name = #{createName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_name = #{updateName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      correct_no = #{correctNo,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      order_type = #{orderType,jdbcType=TINYINT},
      service_code = #{serviceCode,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      company_code = #{companyCode,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      passenger_name = #{passengerName,jdbcType=VARCHAR},
      passenger_phone = #{passengerPhone,jdbcType=VARCHAR},
      assign_car_license = #{assignCarLicense,jdbcType=VARCHAR},
      mileage_before = #{mileageBefore,jdbcType=DECIMAL},
      mileage_source = #{mileageSource,jdbcType=TINYINT},
      mileage_correct = #{mileageCorrect,jdbcType=DECIMAL},
      fact_start_date = #{factStartDate,jdbcType=TIMESTAMP},
      fact_end_date = #{factEndDate,jdbcType=TIMESTAMP},
      deal_name = #{dealName,jdbcType=VARCHAR},
      deal_time = #{dealTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>