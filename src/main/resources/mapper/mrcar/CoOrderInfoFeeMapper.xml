<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.CoOrderInfoFeeMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.CoOrderInfoFee">
    <id column="order_info_fee_id" jdbcType="INTEGER" property="orderInfoFeeId" />
    <result column="create_id" jdbcType="INTEGER" property="createId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_mobile" jdbcType="VARCHAR" property="createMobile" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="co_order_id" jdbcType="INTEGER" property="coOrderId" />
    <result column="order_num" jdbcType="VARCHAR" property="orderNum" />
    <result column="fee_code" jdbcType="VARCHAR" property="feeCode" />
    <result column="fee_name" jdbcType="VARCHAR" property="feeName" />
    <result column="fee_amount" jdbcType="DECIMAL" property="feeAmount" />
  </resultMap>
  <sql id="Base_Column_List">
    order_info_fee_id, create_id, create_name, create_mobile, create_time, co_order_id, 
    order_num, fee_code, fee_name, fee_amount
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from co_order_info_fee
    where order_info_fee_id = #{orderInfoFeeId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from co_order_info_fee
    where order_info_fee_id = #{orderInfoFeeId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.CoOrderInfoFee">
    <selectKey keyProperty="orderInfoFeeId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into co_order_info_fee (create_id, create_name, create_mobile, 
      create_time, co_order_id, order_num, 
      fee_code, fee_name, fee_amount
      )
    values (#{createId,jdbcType=INTEGER}, #{createName,jdbcType=VARCHAR}, #{createMobile,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{coOrderId,jdbcType=INTEGER}, #{orderNum,jdbcType=VARCHAR}, 
      #{feeCode,jdbcType=VARCHAR}, #{feeName,jdbcType=VARCHAR}, #{feeAmount,jdbcType=DECIMAL}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.CoOrderInfoFee">
    <selectKey keyProperty="orderInfoFeeId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into co_order_info_fee
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createId != null">
        create_id,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createMobile != null">
        create_mobile,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="coOrderId != null">
        co_order_id,
      </if>
      <if test="orderNum != null">
        order_num,
      </if>
      <if test="feeCode != null">
        fee_code,
      </if>
      <if test="feeName != null">
        fee_name,
      </if>
      <if test="feeAmount != null">
        fee_amount,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createId != null">
        #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createMobile != null">
        #{createMobile,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="coOrderId != null">
        #{coOrderId,jdbcType=INTEGER},
      </if>
      <if test="orderNum != null">
        #{orderNum,jdbcType=VARCHAR},
      </if>
      <if test="feeCode != null">
        #{feeCode,jdbcType=VARCHAR},
      </if>
      <if test="feeName != null">
        #{feeName,jdbcType=VARCHAR},
      </if>
      <if test="feeAmount != null">
        #{feeAmount,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.CoOrderInfoFee">
    update co_order_info_fee
    <set>
      <if test="createId != null">
        create_id = #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createMobile != null">
        create_mobile = #{createMobile,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="coOrderId != null">
        co_order_id = #{coOrderId,jdbcType=INTEGER},
      </if>
      <if test="orderNum != null">
        order_num = #{orderNum,jdbcType=VARCHAR},
      </if>
      <if test="feeCode != null">
        fee_code = #{feeCode,jdbcType=VARCHAR},
      </if>
      <if test="feeName != null">
        fee_name = #{feeName,jdbcType=VARCHAR},
      </if>
      <if test="feeAmount != null">
        fee_amount = #{feeAmount,jdbcType=DECIMAL},
      </if>
    </set>
    where order_info_fee_id = #{orderInfoFeeId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.CoOrderInfoFee">
    update co_order_info_fee
    set create_id = #{createId,jdbcType=INTEGER},
      create_name = #{createName,jdbcType=VARCHAR},
      create_mobile = #{createMobile,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      co_order_id = #{coOrderId,jdbcType=INTEGER},
      order_num = #{orderNum,jdbcType=VARCHAR},
      fee_code = #{feeCode,jdbcType=VARCHAR},
      fee_name = #{feeName,jdbcType=VARCHAR},
      fee_amount = #{feeAmount,jdbcType=DECIMAL}
    where order_info_fee_id = #{orderInfoFeeId,jdbcType=INTEGER}
  </update>
</mapper>