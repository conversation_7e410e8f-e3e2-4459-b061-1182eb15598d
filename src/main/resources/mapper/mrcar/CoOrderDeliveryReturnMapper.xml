<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.CoOrderDeliveryReturnMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.CoOrderDeliveryReturn">
    <id column="order_delivery_return_id" jdbcType="INTEGER" property="orderDeliveryReturnId" />
    <result column="create_id" jdbcType="INTEGER" property="createId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_mobile" jdbcType="VARCHAR" property="createMobile" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="co_order_id" jdbcType="INTEGER" property="coOrderId" />
    <result column="order_num" jdbcType="VARCHAR" property="orderNum" />
    <result column="business_type" jdbcType="TINYINT" property="businessType" />
    <result column="over_mileage_kilometer" jdbcType="DECIMAL" property="overMileageKilometer" />
    <result column="over_mileage_amount" jdbcType="DECIMAL" property="overMileageAmount" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Base_Column_List">
    order_delivery_return_id, create_id, create_name, create_mobile, create_time, co_order_id, 
    order_num, business_type, over_mileage_kilometer, over_mileage_amount, remark
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from co_order_delivery_return
    where order_delivery_return_id = #{orderDeliveryReturnId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from co_order_delivery_return
    where order_delivery_return_id = #{orderDeliveryReturnId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.CoOrderDeliveryReturn">
    <selectKey keyProperty="orderDeliveryReturnId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into co_order_delivery_return (create_id, create_name, create_mobile, 
      create_time, co_order_id, order_num, 
      business_type, over_mileage_kilometer, over_mileage_amount, 
      remark)
    values (#{createId,jdbcType=INTEGER}, #{createName,jdbcType=VARCHAR}, #{createMobile,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{coOrderId,jdbcType=INTEGER}, #{orderNum,jdbcType=VARCHAR}, 
      #{businessType,jdbcType=TINYINT}, #{overMileageKilometer,jdbcType=DECIMAL}, #{overMileageAmount,jdbcType=DECIMAL}, 
      #{remark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.CoOrderDeliveryReturn">
    <selectKey keyProperty="orderDeliveryReturnId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into co_order_delivery_return
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createId != null">
        create_id,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createMobile != null">
        create_mobile,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="coOrderId != null">
        co_order_id,
      </if>
      <if test="orderNum != null">
        order_num,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="overMileageKilometer != null">
        over_mileage_kilometer,
      </if>
      <if test="overMileageAmount != null">
        over_mileage_amount,
      </if>
      <if test="remark != null">
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createId != null">
        #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createMobile != null">
        #{createMobile,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="coOrderId != null">
        #{coOrderId,jdbcType=INTEGER},
      </if>
      <if test="orderNum != null">
        #{orderNum,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=TINYINT},
      </if>
      <if test="overMileageKilometer != null">
        #{overMileageKilometer,jdbcType=DECIMAL},
      </if>
      <if test="overMileageAmount != null">
        #{overMileageAmount,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.CoOrderDeliveryReturn">
    update co_order_delivery_return
    <set>
      <if test="createId != null">
        create_id = #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createMobile != null">
        create_mobile = #{createMobile,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="coOrderId != null">
        co_order_id = #{coOrderId,jdbcType=INTEGER},
      </if>
      <if test="orderNum != null">
        order_num = #{orderNum,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=TINYINT},
      </if>
      <if test="overMileageKilometer != null">
        over_mileage_kilometer = #{overMileageKilometer,jdbcType=DECIMAL},
      </if>
      <if test="overMileageAmount != null">
        over_mileage_amount = #{overMileageAmount,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where order_delivery_return_id = #{orderDeliveryReturnId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.CoOrderDeliveryReturn">
    update co_order_delivery_return
    set create_id = #{createId,jdbcType=INTEGER},
      create_name = #{createName,jdbcType=VARCHAR},
      create_mobile = #{createMobile,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      co_order_id = #{coOrderId,jdbcType=INTEGER},
      order_num = #{orderNum,jdbcType=VARCHAR},
      business_type = #{businessType,jdbcType=TINYINT},
      over_mileage_kilometer = #{overMileageKilometer,jdbcType=DECIMAL},
      over_mileage_amount = #{overMileageAmount,jdbcType=DECIMAL},
      remark = #{remark,jdbcType=VARCHAR}
    where order_delivery_return_id = #{orderDeliveryReturnId,jdbcType=INTEGER}
  </update>
</mapper>