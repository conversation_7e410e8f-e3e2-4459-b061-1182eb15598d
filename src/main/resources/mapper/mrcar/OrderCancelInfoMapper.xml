<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.OrderCancelInfoMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.OrderCancelInfo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="order_cancel_no" jdbcType="VARCHAR" property="orderCancelNo" />
    <result column="cancel_fee_type" jdbcType="TINYINT" property="cancelFeeType" />
    <result column="cancel_source_type" jdbcType="TINYINT" property="cancelSourceType" />
    <result column="cancel_user_id" jdbcType="BIGINT" property="cancelUserId" />
    <result column="cancel_user_name" jdbcType="VARCHAR" property="cancelUserName" />
    <result column="cancel_reason_id" jdbcType="TINYINT" property="cancelReasonId" />
    <result column="cancel_reason_text" jdbcType="VARCHAR" property="cancelReasonText" />
    <result column="cancel_memo" jdbcType="VARCHAR" property="cancelMemo" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, order_no, order_cancel_no, cancel_fee_type, cancel_source_type, 
    cancel_user_id, cancel_user_name, cancel_reason_id, cancel_reason_text, cancel_memo
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_cancel_info
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from order_cancel_info
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.OrderCancelInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_cancel_info (create_time, update_time, order_no, 
      order_cancel_no, cancel_fee_type, cancel_source_type, 
      cancel_user_id, cancel_user_name, cancel_reason_id, 
      cancel_reason_text, cancel_memo)
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{orderNo,jdbcType=VARCHAR}, 
      #{orderCancelNo,jdbcType=VARCHAR}, #{cancelFeeType,jdbcType=TINYINT}, #{cancelSourceType,jdbcType=TINYINT}, 
      #{cancelUserId,jdbcType=BIGINT}, #{cancelUserName,jdbcType=VARCHAR}, #{cancelReasonId,jdbcType=TINYINT}, 
      #{cancelReasonText,jdbcType=VARCHAR}, #{cancelMemo,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.OrderCancelInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_cancel_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="orderCancelNo != null">
        order_cancel_no,
      </if>
      <if test="cancelFeeType != null">
        cancel_fee_type,
      </if>
      <if test="cancelSourceType != null">
        cancel_source_type,
      </if>
      <if test="cancelUserId != null">
        cancel_user_id,
      </if>
      <if test="cancelUserName != null">
        cancel_user_name,
      </if>
      <if test="cancelReasonId != null">
        cancel_reason_id,
      </if>
      <if test="cancelReasonText != null">
        cancel_reason_text,
      </if>
      <if test="cancelMemo != null">
        cancel_memo,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderCancelNo != null">
        #{orderCancelNo,jdbcType=VARCHAR},
      </if>
      <if test="cancelFeeType != null">
        #{cancelFeeType,jdbcType=TINYINT},
      </if>
      <if test="cancelSourceType != null">
        #{cancelSourceType,jdbcType=TINYINT},
      </if>
      <if test="cancelUserId != null">
        #{cancelUserId,jdbcType=BIGINT},
      </if>
      <if test="cancelUserName != null">
        #{cancelUserName,jdbcType=VARCHAR},
      </if>
      <if test="cancelReasonId != null">
        #{cancelReasonId,jdbcType=TINYINT},
      </if>
      <if test="cancelReasonText != null">
        #{cancelReasonText,jdbcType=VARCHAR},
      </if>
      <if test="cancelMemo != null">
        #{cancelMemo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.OrderCancelInfo">
    update order_cancel_info
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderCancelNo != null">
        order_cancel_no = #{orderCancelNo,jdbcType=VARCHAR},
      </if>
      <if test="cancelFeeType != null">
        cancel_fee_type = #{cancelFeeType,jdbcType=TINYINT},
      </if>
      <if test="cancelSourceType != null">
        cancel_source_type = #{cancelSourceType,jdbcType=TINYINT},
      </if>
      <if test="cancelUserId != null">
        cancel_user_id = #{cancelUserId,jdbcType=BIGINT},
      </if>
      <if test="cancelUserName != null">
        cancel_user_name = #{cancelUserName,jdbcType=VARCHAR},
      </if>
      <if test="cancelReasonId != null">
        cancel_reason_id = #{cancelReasonId,jdbcType=TINYINT},
      </if>
      <if test="cancelReasonText != null">
        cancel_reason_text = #{cancelReasonText,jdbcType=VARCHAR},
      </if>
      <if test="cancelMemo != null">
        cancel_memo = #{cancelMemo,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.OrderCancelInfo">
    update order_cancel_info
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      order_no = #{orderNo,jdbcType=VARCHAR},
      order_cancel_no = #{orderCancelNo,jdbcType=VARCHAR},
      cancel_fee_type = #{cancelFeeType,jdbcType=TINYINT},
      cancel_source_type = #{cancelSourceType,jdbcType=TINYINT},
      cancel_user_id = #{cancelUserId,jdbcType=BIGINT},
      cancel_user_name = #{cancelUserName,jdbcType=VARCHAR},
      cancel_reason_id = #{cancelReasonId,jdbcType=TINYINT},
      cancel_reason_text = #{cancelReasonText,jdbcType=VARCHAR},
      cancel_memo = #{cancelMemo,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>