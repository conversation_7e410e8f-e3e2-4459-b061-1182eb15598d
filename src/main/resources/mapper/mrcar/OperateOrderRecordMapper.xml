<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.OperateOrderRecordMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.OperateOrderRecord">
    <id column="operate_order_record_id" jdbcType="INTEGER" property="operateOrderRecordId" />
    <result column="create_id" jdbcType="INTEGER" property="createId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="del_tag" jdbcType="TINYINT" property="delTag" />
    <result column="demand_order_num" jdbcType="VARCHAR" property="demandOrderNum" />
    <result column="customer_order_num" jdbcType="VARCHAR" property="customerOrderNum" />
    <result column="supplier_order_num" jdbcType="VARCHAR" property="supplierOrderNum" />
    <result column="operate_type" jdbcType="TINYINT" property="operateType" />
    <result column="operate_time" jdbcType="TIMESTAMP" property="operateTime" />
    <result column="operator_id" jdbcType="INTEGER" property="operatorId" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
  </resultMap>
  <sql id="Base_Column_List">
    operate_order_record_id, create_id, create_name, create_time, del_tag, demand_order_num, 
    customer_order_num, supplier_order_num, operate_type, operate_time, operator_id, 
    reason
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from so_operate_order_record
    where operate_order_record_id = #{operateOrderRecordId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from so_operate_order_record
    where operate_order_record_id = #{operateOrderRecordId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.OperateOrderRecord">
    insert into so_operate_order_record (operate_order_record_id, create_id, create_name, 
      create_time, del_tag, demand_order_num, 
      customer_order_num, supplier_order_num, operate_type, 
      operate_time, operator_id, reason
      )
    values (#{operateOrderRecordId,jdbcType=INTEGER}, #{createId,jdbcType=INTEGER}, #{createName,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{delTag,jdbcType=TINYINT}, #{demandOrderNum,jdbcType=VARCHAR}, 
      #{customerOrderNum,jdbcType=VARCHAR}, #{supplierOrderNum,jdbcType=VARCHAR}, #{operateType,jdbcType=TINYINT}, 
      #{operateTime,jdbcType=TIMESTAMP}, #{operatorId,jdbcType=INTEGER}, #{reason,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.OperateOrderRecord">
    insert into so_operate_order_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="operateOrderRecordId != null">
        operate_order_record_id,
      </if>
      <if test="createId != null">
        create_id,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="delTag != null">
        del_tag,
      </if>
      <if test="demandOrderNum != null">
        demand_order_num,
      </if>
      <if test="customerOrderNum != null">
        customer_order_num,
      </if>
      <if test="supplierOrderNum != null">
        supplier_order_num,
      </if>
      <if test="operateType != null">
        operate_type,
      </if>
      <if test="operateTime != null">
        operate_time,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="reason != null">
        reason,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="operateOrderRecordId != null">
        #{operateOrderRecordId,jdbcType=INTEGER},
      </if>
      <if test="createId != null">
        #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delTag != null">
        #{delTag,jdbcType=TINYINT},
      </if>
      <if test="demandOrderNum != null">
        #{demandOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="customerOrderNum != null">
        #{customerOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="supplierOrderNum != null">
        #{supplierOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="operateType != null">
        #{operateType,jdbcType=TINYINT},
      </if>
      <if test="operateTime != null">
        #{operateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=INTEGER},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.OperateOrderRecord">
    update so_operate_order_record
    <set>
      <if test="createId != null">
        create_id = #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delTag != null">
        del_tag = #{delTag,jdbcType=TINYINT},
      </if>
      <if test="demandOrderNum != null">
        demand_order_num = #{demandOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="customerOrderNum != null">
        customer_order_num = #{customerOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="supplierOrderNum != null">
        supplier_order_num = #{supplierOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="operateType != null">
        operate_type = #{operateType,jdbcType=TINYINT},
      </if>
      <if test="operateTime != null">
        operate_time = #{operateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=INTEGER},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
    </set>
    where operate_order_record_id = #{operateOrderRecordId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.OperateOrderRecord">
    update so_operate_order_record
    set create_id = #{createId,jdbcType=INTEGER},
      create_name = #{createName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      del_tag = #{delTag,jdbcType=TINYINT},
      demand_order_num = #{demandOrderNum,jdbcType=VARCHAR},
      customer_order_num = #{customerOrderNum,jdbcType=VARCHAR},
      supplier_order_num = #{supplierOrderNum,jdbcType=VARCHAR},
      operate_type = #{operateType,jdbcType=TINYINT},
      operate_time = #{operateTime,jdbcType=TIMESTAMP},
      operator_id = #{operatorId,jdbcType=INTEGER},
      reason = #{reason,jdbcType=VARCHAR}
    where operate_order_record_id = #{operateOrderRecordId,jdbcType=INTEGER}
  </update>
</mapper>