<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.BusOrderMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.BusOrder">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="order_status" jdbcType="INTEGER" property="orderStatus" />
    <result column="order_create_type" jdbcType="INTEGER" property="orderCreateType" />
    <result column="rule_id" jdbcType="INTEGER" property="ruleId" />
    <result column="rule_code" jdbcType="VARCHAR" property="ruleCode" />
    <result column="customer_id" jdbcType="INTEGER" property="customerId" />
    <result column="customer_code" jdbcType="VARCHAR" property="customerCode" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="contact_name" jdbcType="VARCHAR" property="contactName" />
    <result column="contact_mobile" jdbcType="VARCHAR" property="contactMobile" />
    <result column="business_type" jdbcType="INTEGER" property="businessType" />
    <result column="source_department_id" jdbcType="INTEGER" property="sourceDepartmentId" />
    <result column="source_department_code" jdbcType="VARCHAR" property="sourceDepartmentCode" />
    <result column="source_department_name" jdbcType="VARCHAR" property="sourceDepartmentName" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="department_id" jdbcType="INTEGER" property="departmentId" />
    <result column="department_code" jdbcType="VARCHAR" property="departmentCode" />
    <result column="department_name" jdbcType="VARCHAR" property="departmentName" />
    <result column="estimated_mileage" jdbcType="DECIMAL" property="estimatedMileage" />
    <result column="estimated_duration" jdbcType="INTEGER" property="estimatedDuration" />
    <result column="estimated_start_time" jdbcType="TIMESTAMP" property="estimatedStartTime" />
    <result column="estimated_end_time" jdbcType="TIMESTAMP" property="estimatedEndTime" />
    <result column="actual_start_time" jdbcType="TIMESTAMP" property="actualStartTime" />
    <result column="actual_end_time" jdbcType="TIMESTAMP" property="actualEndTime" />
    <result column="total_settlement_amount" jdbcType="DECIMAL" property="totalSettlementAmount" />
    <result column="passenger_name" jdbcType="VARCHAR" property="passengerName" />
    <result column="passenger_mobile" jdbcType="VARCHAR" property="passengerMobile" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="route_id" jdbcType="INTEGER" property="routeId" />
    <result column="route_code" jdbcType="VARCHAR" property="routeCode" />
    <result column="route_name" jdbcType="VARCHAR" property="routeName" />
    <result column="route_type" jdbcType="INTEGER" property="routeType" />
    <result column="dispatch_type" jdbcType="INTEGER" property="dispatchType" />
    <result column="vehicle_level_id" jdbcType="INTEGER" property="vehicleLevelId" />
    <result column="vehicle_level" jdbcType="VARCHAR" property="vehicleLevel" />
    <result column="vehicle_model_id" jdbcType="INTEGER" property="vehicleModelId" />
    <result column="vehicle_model" jdbcType="VARCHAR" property="vehicleModel" />
    <result column="assigned_vehicle_id" jdbcType="INTEGER" property="assignedVehicleId" />
    <result column="assigned_vehicle_license" jdbcType="VARCHAR" property="assignedVehicleLicense" />
    <result column="assigned_vehicle_vin" jdbcType="VARCHAR" property="assignedVehicleVin" />
    <result column="device_id" jdbcType="VARCHAR" property="deviceId" />
    <result column="device_type" jdbcType="INTEGER" property="deviceType" />
    <result column="assigned_driver_id" jdbcType="INTEGER" property="assignedDriverId" />
    <result column="assigned_driver_name" jdbcType="VARCHAR" property="assignedDriverName" />
    <result column="assigned_driver_mobile" jdbcType="VARCHAR" property="assignedDriverMobile" />
    <result column="user_count" jdbcType="INTEGER" property="userCount" />
    <result column="process_instance_Id" jdbcType="VARCHAR" property="processInstanceId" />
    <result column="create_id" jdbcType="INTEGER" property="createId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_mobile" jdbcType="VARCHAR" property="createMobile" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_id" jdbcType="INTEGER" property="updateId" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_status" jdbcType="INTEGER" property="delStatus" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_no, order_status, order_create_type, rule_id, rule_code, customer_id, customer_code, 
    customer_name, contact_name, contact_mobile, business_type, source_department_id, 
    source_department_code, source_department_name, company_id, company_code, company_name, 
    department_id, department_code, department_name, estimated_mileage, estimated_duration, 
    estimated_start_time, estimated_end_time, actual_start_time, actual_end_time, total_settlement_amount, 
    passenger_name, passenger_mobile, remark, route_id, route_code, route_name, route_type, 
    dispatch_type, vehicle_level_id, vehicle_level, vehicle_model_id, vehicle_model, 
    assigned_vehicle_id, assigned_vehicle_license, assigned_vehicle_vin, device_id, device_type, 
    assigned_driver_id, assigned_driver_name, assigned_driver_mobile, user_count, process_instance_Id, 
    create_id, create_name, create_mobile, create_time, update_id, update_name, update_time, 
    del_status
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.BusOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from bus_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bus_order
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from bus_order
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.BusOrder">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into bus_order (order_no, order_status, order_create_type, 
      rule_id, rule_code, customer_id, 
      customer_code, customer_name, contact_name, 
      contact_mobile, business_type, source_department_id, 
      source_department_code, source_department_name, 
      company_id, company_code, company_name, 
      department_id, department_code, department_name, 
      estimated_mileage, estimated_duration, estimated_start_time, 
      estimated_end_time, actual_start_time, 
      actual_end_time, total_settlement_amount, 
      passenger_name, passenger_mobile, remark, 
      route_id, route_code, route_name, 
      route_type, dispatch_type, vehicle_level_id, 
      vehicle_level, vehicle_model_id, vehicle_model, 
      assigned_vehicle_id, assigned_vehicle_license, 
      assigned_vehicle_vin, device_id, device_type, 
      assigned_driver_id, assigned_driver_name, assigned_driver_mobile, 
      user_count, process_instance_Id, create_id, 
      create_name, create_mobile, create_time, 
      update_id, update_name, update_time, 
      del_status)
    values (#{orderNo,jdbcType=VARCHAR}, #{orderStatus,jdbcType=INTEGER}, #{orderCreateType,jdbcType=INTEGER}, 
      #{ruleId,jdbcType=INTEGER}, #{ruleCode,jdbcType=VARCHAR}, #{customerId,jdbcType=INTEGER}, 
      #{customerCode,jdbcType=VARCHAR}, #{customerName,jdbcType=VARCHAR}, #{contactName,jdbcType=VARCHAR}, 
      #{contactMobile,jdbcType=VARCHAR}, #{businessType,jdbcType=INTEGER}, #{sourceDepartmentId,jdbcType=INTEGER}, 
      #{sourceDepartmentCode,jdbcType=VARCHAR}, #{sourceDepartmentName,jdbcType=VARCHAR}, 
      #{companyId,jdbcType=INTEGER}, #{companyCode,jdbcType=VARCHAR}, #{companyName,jdbcType=VARCHAR}, 
      #{departmentId,jdbcType=INTEGER}, #{departmentCode,jdbcType=VARCHAR}, #{departmentName,jdbcType=VARCHAR}, 
      #{estimatedMileage,jdbcType=DECIMAL}, #{estimatedDuration,jdbcType=INTEGER}, #{estimatedStartTime,jdbcType=TIMESTAMP}, 
      #{estimatedEndTime,jdbcType=TIMESTAMP}, #{actualStartTime,jdbcType=TIMESTAMP}, 
      #{actualEndTime,jdbcType=TIMESTAMP}, #{totalSettlementAmount,jdbcType=DECIMAL}, 
      #{passengerName,jdbcType=VARCHAR}, #{passengerMobile,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{routeId,jdbcType=INTEGER}, #{routeCode,jdbcType=VARCHAR}, #{routeName,jdbcType=VARCHAR}, 
      #{routeType,jdbcType=INTEGER}, #{dispatchType,jdbcType=INTEGER}, #{vehicleLevelId,jdbcType=INTEGER}, 
      #{vehicleLevel,jdbcType=VARCHAR}, #{vehicleModelId,jdbcType=INTEGER}, #{vehicleModel,jdbcType=VARCHAR}, 
      #{assignedVehicleId,jdbcType=INTEGER}, #{assignedVehicleLicense,jdbcType=VARCHAR}, 
      #{assignedVehicleVin,jdbcType=VARCHAR}, #{deviceId,jdbcType=VARCHAR}, #{deviceType,jdbcType=INTEGER}, 
      #{assignedDriverId,jdbcType=INTEGER}, #{assignedDriverName,jdbcType=VARCHAR}, #{assignedDriverMobile,jdbcType=VARCHAR}, 
      #{userCount,jdbcType=INTEGER}, #{processInstanceId,jdbcType=VARCHAR}, #{createId,jdbcType=INTEGER}, 
      #{createName,jdbcType=VARCHAR}, #{createMobile,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateId,jdbcType=INTEGER}, #{updateName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{delStatus,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.BusOrder">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into bus_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="orderCreateType != null">
        order_create_type,
      </if>
      <if test="ruleId != null">
        rule_id,
      </if>
      <if test="ruleCode != null">
        rule_code,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="customerCode != null">
        customer_code,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="contactName != null">
        contact_name,
      </if>
      <if test="contactMobile != null">
        contact_mobile,
      </if>
      <if test="businessType != null">
        business_type,
      </if>
      <if test="sourceDepartmentId != null">
        source_department_id,
      </if>
      <if test="sourceDepartmentCode != null">
        source_department_code,
      </if>
      <if test="sourceDepartmentName != null">
        source_department_name,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="departmentId != null">
        department_id,
      </if>
      <if test="departmentCode != null">
        department_code,
      </if>
      <if test="departmentName != null">
        department_name,
      </if>
      <if test="estimatedMileage != null">
        estimated_mileage,
      </if>
      <if test="estimatedDuration != null">
        estimated_duration,
      </if>
      <if test="estimatedStartTime != null">
        estimated_start_time,
      </if>
      <if test="estimatedEndTime != null">
        estimated_end_time,
      </if>
      <if test="actualStartTime != null">
        actual_start_time,
      </if>
      <if test="actualEndTime != null">
        actual_end_time,
      </if>
      <if test="totalSettlementAmount != null">
        total_settlement_amount,
      </if>
      <if test="passengerName != null">
        passenger_name,
      </if>
      <if test="passengerMobile != null">
        passenger_mobile,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="routeId != null">
        route_id,
      </if>
      <if test="routeCode != null">
        route_code,
      </if>
      <if test="routeName != null">
        route_name,
      </if>
      <if test="routeType != null">
        route_type,
      </if>
      <if test="dispatchType != null">
        dispatch_type,
      </if>
      <if test="vehicleLevelId != null">
        vehicle_level_id,
      </if>
      <if test="vehicleLevel != null">
        vehicle_level,
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id,
      </if>
      <if test="vehicleModel != null">
        vehicle_model,
      </if>
      <if test="assignedVehicleId != null">
        assigned_vehicle_id,
      </if>
      <if test="assignedVehicleLicense != null">
        assigned_vehicle_license,
      </if>
      <if test="assignedVehicleVin != null">
        assigned_vehicle_vin,
      </if>
      <if test="deviceId != null">
        device_id,
      </if>
      <if test="deviceType != null">
        device_type,
      </if>
      <if test="assignedDriverId != null">
        assigned_driver_id,
      </if>
      <if test="assignedDriverName != null">
        assigned_driver_name,
      </if>
      <if test="assignedDriverMobile != null">
        assigned_driver_mobile,
      </if>
      <if test="userCount != null">
        user_count,
      </if>
      <if test="processInstanceId != null">
        process_instance_Id,
      </if>
      <if test="createId != null">
        create_id,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createMobile != null">
        create_mobile,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateId != null">
        update_id,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="delStatus != null">
        del_status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="orderCreateType != null">
        #{orderCreateType,jdbcType=INTEGER},
      </if>
      <if test="ruleId != null">
        #{ruleId,jdbcType=INTEGER},
      </if>
      <if test="ruleCode != null">
        #{ruleCode,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=INTEGER},
      </if>
      <if test="customerCode != null">
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="contactName != null">
        #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactMobile != null">
        #{contactMobile,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=INTEGER},
      </if>
      <if test="sourceDepartmentId != null">
        #{sourceDepartmentId,jdbcType=INTEGER},
      </if>
      <if test="sourceDepartmentCode != null">
        #{sourceDepartmentCode,jdbcType=VARCHAR},
      </if>
      <if test="sourceDepartmentName != null">
        #{sourceDepartmentName,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="departmentId != null">
        #{departmentId,jdbcType=INTEGER},
      </if>
      <if test="departmentCode != null">
        #{departmentCode,jdbcType=VARCHAR},
      </if>
      <if test="departmentName != null">
        #{departmentName,jdbcType=VARCHAR},
      </if>
      <if test="estimatedMileage != null">
        #{estimatedMileage,jdbcType=DECIMAL},
      </if>
      <if test="estimatedDuration != null">
        #{estimatedDuration,jdbcType=INTEGER},
      </if>
      <if test="estimatedStartTime != null">
        #{estimatedStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="estimatedEndTime != null">
        #{estimatedEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="actualStartTime != null">
        #{actualStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="actualEndTime != null">
        #{actualEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="totalSettlementAmount != null">
        #{totalSettlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="passengerName != null">
        #{passengerName,jdbcType=VARCHAR},
      </if>
      <if test="passengerMobile != null">
        #{passengerMobile,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="routeId != null">
        #{routeId,jdbcType=INTEGER},
      </if>
      <if test="routeCode != null">
        #{routeCode,jdbcType=VARCHAR},
      </if>
      <if test="routeName != null">
        #{routeName,jdbcType=VARCHAR},
      </if>
      <if test="routeType != null">
        #{routeType,jdbcType=INTEGER},
      </if>
      <if test="dispatchType != null">
        #{dispatchType,jdbcType=INTEGER},
      </if>
      <if test="vehicleLevelId != null">
        #{vehicleLevelId,jdbcType=INTEGER},
      </if>
      <if test="vehicleLevel != null">
        #{vehicleLevel,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelId != null">
        #{vehicleModelId,jdbcType=INTEGER},
      </if>
      <if test="vehicleModel != null">
        #{vehicleModel,jdbcType=VARCHAR},
      </if>
      <if test="assignedVehicleId != null">
        #{assignedVehicleId,jdbcType=INTEGER},
      </if>
      <if test="assignedVehicleLicense != null">
        #{assignedVehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="assignedVehicleVin != null">
        #{assignedVehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="deviceId != null">
        #{deviceId,jdbcType=VARCHAR},
      </if>
      <if test="deviceType != null">
        #{deviceType,jdbcType=INTEGER},
      </if>
      <if test="assignedDriverId != null">
        #{assignedDriverId,jdbcType=INTEGER},
      </if>
      <if test="assignedDriverName != null">
        #{assignedDriverName,jdbcType=VARCHAR},
      </if>
      <if test="assignedDriverMobile != null">
        #{assignedDriverMobile,jdbcType=VARCHAR},
      </if>
      <if test="userCount != null">
        #{userCount,jdbcType=INTEGER},
      </if>
      <if test="processInstanceId != null">
        #{processInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="createId != null">
        #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createMobile != null">
        #{createMobile,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delStatus != null">
        #{delStatus,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map">
    update bus_order
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=INTEGER},
      </if>
      <if test="row.orderNo != null">
        order_no = #{row.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="row.orderStatus != null">
        order_status = #{row.orderStatus,jdbcType=INTEGER},
      </if>
      <if test="row.orderCreateType != null">
        order_create_type = #{row.orderCreateType,jdbcType=INTEGER},
      </if>
      <if test="row.ruleId != null">
        rule_id = #{row.ruleId,jdbcType=INTEGER},
      </if>
      <if test="row.ruleCode != null">
        rule_code = #{row.ruleCode,jdbcType=VARCHAR},
      </if>
      <if test="row.customerId != null">
        customer_id = #{row.customerId,jdbcType=INTEGER},
      </if>
      <if test="row.customerCode != null">
        customer_code = #{row.customerCode,jdbcType=VARCHAR},
      </if>
      <if test="row.customerName != null">
        customer_name = #{row.customerName,jdbcType=VARCHAR},
      </if>
      <if test="row.contactName != null">
        contact_name = #{row.contactName,jdbcType=VARCHAR},
      </if>
      <if test="row.contactMobile != null">
        contact_mobile = #{row.contactMobile,jdbcType=VARCHAR},
      </if>
      <if test="row.businessType != null">
        business_type = #{row.businessType,jdbcType=INTEGER},
      </if>
      <if test="row.sourceDepartmentId != null">
        source_department_id = #{row.sourceDepartmentId,jdbcType=INTEGER},
      </if>
      <if test="row.sourceDepartmentCode != null">
        source_department_code = #{row.sourceDepartmentCode,jdbcType=VARCHAR},
      </if>
      <if test="row.sourceDepartmentName != null">
        source_department_name = #{row.sourceDepartmentName,jdbcType=VARCHAR},
      </if>
      <if test="row.companyId != null">
        company_id = #{row.companyId,jdbcType=INTEGER},
      </if>
      <if test="row.companyCode != null">
        company_code = #{row.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="row.companyName != null">
        company_name = #{row.companyName,jdbcType=VARCHAR},
      </if>
      <if test="row.departmentId != null">
        department_id = #{row.departmentId,jdbcType=INTEGER},
      </if>
      <if test="row.departmentCode != null">
        department_code = #{row.departmentCode,jdbcType=VARCHAR},
      </if>
      <if test="row.departmentName != null">
        department_name = #{row.departmentName,jdbcType=VARCHAR},
      </if>
      <if test="row.estimatedMileage != null">
        estimated_mileage = #{row.estimatedMileage,jdbcType=DECIMAL},
      </if>
      <if test="row.estimatedDuration != null">
        estimated_duration = #{row.estimatedDuration,jdbcType=INTEGER},
      </if>
      <if test="row.estimatedStartTime != null">
        estimated_start_time = #{row.estimatedStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.estimatedEndTime != null">
        estimated_end_time = #{row.estimatedEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.actualStartTime != null">
        actual_start_time = #{row.actualStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.actualEndTime != null">
        actual_end_time = #{row.actualEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.totalSettlementAmount != null">
        total_settlement_amount = #{row.totalSettlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.passengerName != null">
        passenger_name = #{row.passengerName,jdbcType=VARCHAR},
      </if>
      <if test="row.passengerMobile != null">
        passenger_mobile = #{row.passengerMobile,jdbcType=VARCHAR},
      </if>
      <if test="row.remark != null">
        remark = #{row.remark,jdbcType=VARCHAR},
      </if>
      <if test="row.routeId != null">
        route_id = #{row.routeId,jdbcType=INTEGER},
      </if>
      <if test="row.routeCode != null">
        route_code = #{row.routeCode,jdbcType=VARCHAR},
      </if>
      <if test="row.routeName != null">
        route_name = #{row.routeName,jdbcType=VARCHAR},
      </if>
      <if test="row.routeType != null">
        route_type = #{row.routeType,jdbcType=INTEGER},
      </if>
      <if test="row.dispatchType != null">
        dispatch_type = #{row.dispatchType,jdbcType=INTEGER},
      </if>
      <if test="row.vehicleLevelId != null">
        vehicle_level_id = #{row.vehicleLevelId,jdbcType=INTEGER},
      </if>
      <if test="row.vehicleLevel != null">
        vehicle_level = #{row.vehicleLevel,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleModelId != null">
        vehicle_model_id = #{row.vehicleModelId,jdbcType=INTEGER},
      </if>
      <if test="row.vehicleModel != null">
        vehicle_model = #{row.vehicleModel,jdbcType=VARCHAR},
      </if>
      <if test="row.assignedVehicleId != null">
        assigned_vehicle_id = #{row.assignedVehicleId,jdbcType=INTEGER},
      </if>
      <if test="row.assignedVehicleLicense != null">
        assigned_vehicle_license = #{row.assignedVehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="row.assignedVehicleVin != null">
        assigned_vehicle_vin = #{row.assignedVehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="row.deviceId != null">
        device_id = #{row.deviceId,jdbcType=VARCHAR},
      </if>
      <if test="row.deviceType != null">
        device_type = #{row.deviceType,jdbcType=INTEGER},
      </if>
      <if test="row.assignedDriverId != null">
        assigned_driver_id = #{row.assignedDriverId,jdbcType=INTEGER},
      </if>
      <if test="row.assignedDriverName != null">
        assigned_driver_name = #{row.assignedDriverName,jdbcType=VARCHAR},
      </if>
      <if test="row.assignedDriverMobile != null">
        assigned_driver_mobile = #{row.assignedDriverMobile,jdbcType=VARCHAR},
      </if>
      <if test="row.userCount != null">
        user_count = #{row.userCount,jdbcType=INTEGER},
      </if>
      <if test="row.processInstanceId != null">
        process_instance_Id = #{row.processInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="row.createId != null">
        create_id = #{row.createId,jdbcType=INTEGER},
      </if>
      <if test="row.createName != null">
        create_name = #{row.createName,jdbcType=VARCHAR},
      </if>
      <if test="row.createMobile != null">
        create_mobile = #{row.createMobile,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateId != null">
        update_id = #{row.updateId,jdbcType=INTEGER},
      </if>
      <if test="row.updateName != null">
        update_name = #{row.updateName,jdbcType=VARCHAR},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.delStatus != null">
        del_status = #{row.delStatus,jdbcType=INTEGER},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update bus_order
    set id = #{row.id,jdbcType=INTEGER},
      order_no = #{row.orderNo,jdbcType=VARCHAR},
      order_status = #{row.orderStatus,jdbcType=INTEGER},
      order_create_type = #{row.orderCreateType,jdbcType=INTEGER},
      rule_id = #{row.ruleId,jdbcType=INTEGER},
      rule_code = #{row.ruleCode,jdbcType=VARCHAR},
      customer_id = #{row.customerId,jdbcType=INTEGER},
      customer_code = #{row.customerCode,jdbcType=VARCHAR},
      customer_name = #{row.customerName,jdbcType=VARCHAR},
      contact_name = #{row.contactName,jdbcType=VARCHAR},
      contact_mobile = #{row.contactMobile,jdbcType=VARCHAR},
      business_type = #{row.businessType,jdbcType=INTEGER},
      source_department_id = #{row.sourceDepartmentId,jdbcType=INTEGER},
      source_department_code = #{row.sourceDepartmentCode,jdbcType=VARCHAR},
      source_department_name = #{row.sourceDepartmentName,jdbcType=VARCHAR},
      company_id = #{row.companyId,jdbcType=INTEGER},
      company_code = #{row.companyCode,jdbcType=VARCHAR},
      company_name = #{row.companyName,jdbcType=VARCHAR},
      department_id = #{row.departmentId,jdbcType=INTEGER},
      department_code = #{row.departmentCode,jdbcType=VARCHAR},
      department_name = #{row.departmentName,jdbcType=VARCHAR},
      estimated_mileage = #{row.estimatedMileage,jdbcType=DECIMAL},
      estimated_duration = #{row.estimatedDuration,jdbcType=INTEGER},
      estimated_start_time = #{row.estimatedStartTime,jdbcType=TIMESTAMP},
      estimated_end_time = #{row.estimatedEndTime,jdbcType=TIMESTAMP},
      actual_start_time = #{row.actualStartTime,jdbcType=TIMESTAMP},
      actual_end_time = #{row.actualEndTime,jdbcType=TIMESTAMP},
      total_settlement_amount = #{row.totalSettlementAmount,jdbcType=DECIMAL},
      passenger_name = #{row.passengerName,jdbcType=VARCHAR},
      passenger_mobile = #{row.passengerMobile,jdbcType=VARCHAR},
      remark = #{row.remark,jdbcType=VARCHAR},
      route_id = #{row.routeId,jdbcType=INTEGER},
      route_code = #{row.routeCode,jdbcType=VARCHAR},
      route_name = #{row.routeName,jdbcType=VARCHAR},
      route_type = #{row.routeType,jdbcType=INTEGER},
      dispatch_type = #{row.dispatchType,jdbcType=INTEGER},
      vehicle_level_id = #{row.vehicleLevelId,jdbcType=INTEGER},
      vehicle_level = #{row.vehicleLevel,jdbcType=VARCHAR},
      vehicle_model_id = #{row.vehicleModelId,jdbcType=INTEGER},
      vehicle_model = #{row.vehicleModel,jdbcType=VARCHAR},
      assigned_vehicle_id = #{row.assignedVehicleId,jdbcType=INTEGER},
      assigned_vehicle_license = #{row.assignedVehicleLicense,jdbcType=VARCHAR},
      assigned_vehicle_vin = #{row.assignedVehicleVin,jdbcType=VARCHAR},
      device_id = #{row.deviceId,jdbcType=VARCHAR},
      device_type = #{row.deviceType,jdbcType=INTEGER},
      assigned_driver_id = #{row.assignedDriverId,jdbcType=INTEGER},
      assigned_driver_name = #{row.assignedDriverName,jdbcType=VARCHAR},
      assigned_driver_mobile = #{row.assignedDriverMobile,jdbcType=VARCHAR},
      user_count = #{row.userCount,jdbcType=INTEGER},
      process_instance_Id = #{row.processInstanceId,jdbcType=VARCHAR},
      create_id = #{row.createId,jdbcType=INTEGER},
      create_name = #{row.createName,jdbcType=VARCHAR},
      create_mobile = #{row.createMobile,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_id = #{row.updateId,jdbcType=INTEGER},
      update_name = #{row.updateName,jdbcType=VARCHAR},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      del_status = #{row.delStatus,jdbcType=INTEGER}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.BusOrder">
    update bus_order
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="orderCreateType != null">
        order_create_type = #{orderCreateType,jdbcType=INTEGER},
      </if>
      <if test="ruleId != null">
        rule_id = #{ruleId,jdbcType=INTEGER},
      </if>
      <if test="ruleCode != null">
        rule_code = #{ruleCode,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=INTEGER},
      </if>
      <if test="customerCode != null">
        customer_code = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null">
        customer_name = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="contactName != null">
        contact_name = #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactMobile != null">
        contact_mobile = #{contactMobile,jdbcType=VARCHAR},
      </if>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=INTEGER},
      </if>
      <if test="sourceDepartmentId != null">
        source_department_id = #{sourceDepartmentId,jdbcType=INTEGER},
      </if>
      <if test="sourceDepartmentCode != null">
        source_department_code = #{sourceDepartmentCode,jdbcType=VARCHAR},
      </if>
      <if test="sourceDepartmentName != null">
        source_department_name = #{sourceDepartmentName,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="departmentId != null">
        department_id = #{departmentId,jdbcType=INTEGER},
      </if>
      <if test="departmentCode != null">
        department_code = #{departmentCode,jdbcType=VARCHAR},
      </if>
      <if test="departmentName != null">
        department_name = #{departmentName,jdbcType=VARCHAR},
      </if>
      <if test="estimatedMileage != null">
        estimated_mileage = #{estimatedMileage,jdbcType=DECIMAL},
      </if>
      <if test="estimatedDuration != null">
        estimated_duration = #{estimatedDuration,jdbcType=INTEGER},
      </if>
      <if test="estimatedStartTime != null">
        estimated_start_time = #{estimatedStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="estimatedEndTime != null">
        estimated_end_time = #{estimatedEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="actualStartTime != null">
        actual_start_time = #{actualStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="actualEndTime != null">
        actual_end_time = #{actualEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="totalSettlementAmount != null">
        total_settlement_amount = #{totalSettlementAmount,jdbcType=DECIMAL},
      </if>
      <if test="passengerName != null">
        passenger_name = #{passengerName,jdbcType=VARCHAR},
      </if>
      <if test="passengerMobile != null">
        passenger_mobile = #{passengerMobile,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="routeId != null">
        route_id = #{routeId,jdbcType=INTEGER},
      </if>
      <if test="routeCode != null">
        route_code = #{routeCode,jdbcType=VARCHAR},
      </if>
      <if test="routeName != null">
        route_name = #{routeName,jdbcType=VARCHAR},
      </if>
      <if test="routeType != null">
        route_type = #{routeType,jdbcType=INTEGER},
      </if>
      <if test="dispatchType != null">
        dispatch_type = #{dispatchType,jdbcType=INTEGER},
      </if>
      <if test="vehicleLevelId != null">
        vehicle_level_id = #{vehicleLevelId,jdbcType=INTEGER},
      </if>
      <if test="vehicleLevel != null">
        vehicle_level = #{vehicleLevel,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id = #{vehicleModelId,jdbcType=INTEGER},
      </if>
      <if test="vehicleModel != null">
        vehicle_model = #{vehicleModel,jdbcType=VARCHAR},
      </if>
      <if test="assignedVehicleId != null">
        assigned_vehicle_id = #{assignedVehicleId,jdbcType=INTEGER},
      </if>
      <if test="assignedVehicleLicense != null">
        assigned_vehicle_license = #{assignedVehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="assignedVehicleVin != null">
        assigned_vehicle_vin = #{assignedVehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="deviceId != null">
        device_id = #{deviceId,jdbcType=VARCHAR},
      </if>
      <if test="deviceType != null">
        device_type = #{deviceType,jdbcType=INTEGER},
      </if>
      <if test="assignedDriverId != null">
        assigned_driver_id = #{assignedDriverId,jdbcType=INTEGER},
      </if>
      <if test="assignedDriverName != null">
        assigned_driver_name = #{assignedDriverName,jdbcType=VARCHAR},
      </if>
      <if test="assignedDriverMobile != null">
        assigned_driver_mobile = #{assignedDriverMobile,jdbcType=VARCHAR},
      </if>
      <if test="userCount != null">
        user_count = #{userCount,jdbcType=INTEGER},
      </if>
      <if test="processInstanceId != null">
        process_instance_Id = #{processInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="createId != null">
        create_id = #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createMobile != null">
        create_mobile = #{createMobile,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        update_id = #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delStatus != null">
        del_status = #{delStatus,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.BusOrder">
    update bus_order
    set order_no = #{orderNo,jdbcType=VARCHAR},
      order_status = #{orderStatus,jdbcType=INTEGER},
      order_create_type = #{orderCreateType,jdbcType=INTEGER},
      rule_id = #{ruleId,jdbcType=INTEGER},
      rule_code = #{ruleCode,jdbcType=VARCHAR},
      customer_id = #{customerId,jdbcType=INTEGER},
      customer_code = #{customerCode,jdbcType=VARCHAR},
      customer_name = #{customerName,jdbcType=VARCHAR},
      contact_name = #{contactName,jdbcType=VARCHAR},
      contact_mobile = #{contactMobile,jdbcType=VARCHAR},
      business_type = #{businessType,jdbcType=INTEGER},
      source_department_id = #{sourceDepartmentId,jdbcType=INTEGER},
      source_department_code = #{sourceDepartmentCode,jdbcType=VARCHAR},
      source_department_name = #{sourceDepartmentName,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=INTEGER},
      company_code = #{companyCode,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      department_id = #{departmentId,jdbcType=INTEGER},
      department_code = #{departmentCode,jdbcType=VARCHAR},
      department_name = #{departmentName,jdbcType=VARCHAR},
      estimated_mileage = #{estimatedMileage,jdbcType=DECIMAL},
      estimated_duration = #{estimatedDuration,jdbcType=INTEGER},
      estimated_start_time = #{estimatedStartTime,jdbcType=TIMESTAMP},
      estimated_end_time = #{estimatedEndTime,jdbcType=TIMESTAMP},
      actual_start_time = #{actualStartTime,jdbcType=TIMESTAMP},
      actual_end_time = #{actualEndTime,jdbcType=TIMESTAMP},
      total_settlement_amount = #{totalSettlementAmount,jdbcType=DECIMAL},
      passenger_name = #{passengerName,jdbcType=VARCHAR},
      passenger_mobile = #{passengerMobile,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      route_id = #{routeId,jdbcType=INTEGER},
      route_code = #{routeCode,jdbcType=VARCHAR},
      route_name = #{routeName,jdbcType=VARCHAR},
      route_type = #{routeType,jdbcType=INTEGER},
      dispatch_type = #{dispatchType,jdbcType=INTEGER},
      vehicle_level_id = #{vehicleLevelId,jdbcType=INTEGER},
      vehicle_level = #{vehicleLevel,jdbcType=VARCHAR},
      vehicle_model_id = #{vehicleModelId,jdbcType=INTEGER},
      vehicle_model = #{vehicleModel,jdbcType=VARCHAR},
      assigned_vehicle_id = #{assignedVehicleId,jdbcType=INTEGER},
      assigned_vehicle_license = #{assignedVehicleLicense,jdbcType=VARCHAR},
      assigned_vehicle_vin = #{assignedVehicleVin,jdbcType=VARCHAR},
      device_id = #{deviceId,jdbcType=VARCHAR},
      device_type = #{deviceType,jdbcType=INTEGER},
      assigned_driver_id = #{assignedDriverId,jdbcType=INTEGER},
      assigned_driver_name = #{assignedDriverName,jdbcType=VARCHAR},
      assigned_driver_mobile = #{assignedDriverMobile,jdbcType=VARCHAR},
      user_count = #{userCount,jdbcType=INTEGER},
      process_instance_Id = #{processInstanceId,jdbcType=VARCHAR},
      create_id = #{createId,jdbcType=INTEGER},
      create_name = #{createName,jdbcType=VARCHAR},
      create_mobile = #{createMobile,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_id = #{updateId,jdbcType=INTEGER},
      update_name = #{updateName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      del_status = #{delStatus,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>