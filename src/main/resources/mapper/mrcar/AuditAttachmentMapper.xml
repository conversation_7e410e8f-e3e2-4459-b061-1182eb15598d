<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.AuditAttachmentMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.AuditAttachment">
    <id column="audit_attachment_id" jdbcType="INTEGER" property="auditAttachmentId" />
    <result column="relate_business_type" jdbcType="INTEGER" property="relateBusinessType" />
    <result column="relate_business_code" jdbcType="VARCHAR" property="relateBusinessCode" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="file_url" jdbcType="VARCHAR" property="fileUrl" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    audit_attachment_id, relate_business_type, relate_business_code, file_name, file_url, 
    create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from so_audit_attachment
    where audit_attachment_id = #{auditAttachmentId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from so_audit_attachment
    where audit_attachment_id = #{auditAttachmentId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.AuditAttachment">
    <selectKey keyProperty="auditAttachmentId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into so_audit_attachment (relate_business_type, relate_business_code, 
      file_name, file_url, create_time
      )
    values (#{relateBusinessType,jdbcType=INTEGER}, #{relateBusinessCode,jdbcType=VARCHAR}, 
      #{fileName,jdbcType=VARCHAR}, #{fileUrl,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.AuditAttachment">
    <selectKey keyProperty="auditAttachmentId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into so_audit_attachment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="relateBusinessType != null">
        relate_business_type,
      </if>
      <if test="relateBusinessCode != null">
        relate_business_code,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="fileUrl != null">
        file_url,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="relateBusinessType != null">
        #{relateBusinessType,jdbcType=INTEGER},
      </if>
      <if test="relateBusinessCode != null">
        #{relateBusinessCode,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileUrl != null">
        #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.AuditAttachment">
    update so_audit_attachment
    <set>
      <if test="relateBusinessType != null">
        relate_business_type = #{relateBusinessType,jdbcType=INTEGER},
      </if>
      <if test="relateBusinessCode != null">
        relate_business_code = #{relateBusinessCode,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileUrl != null">
        file_url = #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where audit_attachment_id = #{auditAttachmentId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.AuditAttachment">
    update so_audit_attachment
    set relate_business_type = #{relateBusinessType,jdbcType=INTEGER},
      relate_business_code = #{relateBusinessCode,jdbcType=VARCHAR},
      file_name = #{fileName,jdbcType=VARCHAR},
      file_url = #{fileUrl,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where audit_attachment_id = #{auditAttachmentId,jdbcType=INTEGER}
  </update>
</mapper>