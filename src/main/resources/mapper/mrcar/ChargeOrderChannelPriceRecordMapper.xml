<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ChargeOrderChannelPriceRecordMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.ChargeOrderChannelPriceRecord">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_id" jdbcType="INTEGER" property="orderId" />
    <result column="order_code" jdbcType="VARCHAR" property="orderCode" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="detail_start_time" jdbcType="TIMESTAMP" property="detailStartTime" />
    <result column="detail_end_time" jdbcType="TIMESTAMP" property="detailEndTime" />
    <result column="detail_ele_price" jdbcType="DECIMAL" property="detailElePrice" />
    <result column="detail_service_price" jdbcType="DECIMAL" property="detailServicePrice" />
    <result column="detail_power" jdbcType="DECIMAL" property="detailPower" />
    <result column="detail_ele_amount" jdbcType="DECIMAL" property="detailEleAmount" />
    <result column="detail_service_amount" jdbcType="DECIMAL" property="detailServiceAmount" />
  </resultMap>
  <sql id="Base_Column_List">
    id, order_id, order_code, create_time, detail_start_time, detail_end_time, detail_ele_price, 
    detail_service_price, detail_power, detail_ele_amount, detail_service_amount
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from charge_order_channel_price_record
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from charge_order_channel_price_record
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.ChargeOrderChannelPriceRecord">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into charge_order_channel_price_record (order_id, order_code, create_time, 
      detail_start_time, detail_end_time, detail_ele_price, 
      detail_service_price, detail_power, detail_ele_amount, 
      detail_service_amount)
    values (#{orderId,jdbcType=INTEGER}, #{orderCode,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{detailStartTime,jdbcType=TIMESTAMP}, #{detailEndTime,jdbcType=TIMESTAMP}, #{detailElePrice,jdbcType=DECIMAL}, 
      #{detailServicePrice,jdbcType=DECIMAL}, #{detailPower,jdbcType=DECIMAL}, #{detailEleAmount,jdbcType=DECIMAL}, 
      #{detailServiceAmount,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.ChargeOrderChannelPriceRecord">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into charge_order_channel_price_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderCode != null">
        order_code,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="detailStartTime != null">
        detail_start_time,
      </if>
      <if test="detailEndTime != null">
        detail_end_time,
      </if>
      <if test="detailElePrice != null">
        detail_ele_price,
      </if>
      <if test="detailServicePrice != null">
        detail_service_price,
      </if>
      <if test="detailPower != null">
        detail_power,
      </if>
      <if test="detailEleAmount != null">
        detail_ele_amount,
      </if>
      <if test="detailServiceAmount != null">
        detail_service_amount,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderCode != null">
        #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="detailStartTime != null">
        #{detailStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="detailEndTime != null">
        #{detailEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="detailElePrice != null">
        #{detailElePrice,jdbcType=DECIMAL},
      </if>
      <if test="detailServicePrice != null">
        #{detailServicePrice,jdbcType=DECIMAL},
      </if>
      <if test="detailPower != null">
        #{detailPower,jdbcType=DECIMAL},
      </if>
      <if test="detailEleAmount != null">
        #{detailEleAmount,jdbcType=DECIMAL},
      </if>
      <if test="detailServiceAmount != null">
        #{detailServiceAmount,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.ChargeOrderChannelPriceRecord">
    update charge_order_channel_price_record
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderCode != null">
        order_code = #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="detailStartTime != null">
        detail_start_time = #{detailStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="detailEndTime != null">
        detail_end_time = #{detailEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="detailElePrice != null">
        detail_ele_price = #{detailElePrice,jdbcType=DECIMAL},
      </if>
      <if test="detailServicePrice != null">
        detail_service_price = #{detailServicePrice,jdbcType=DECIMAL},
      </if>
      <if test="detailPower != null">
        detail_power = #{detailPower,jdbcType=DECIMAL},
      </if>
      <if test="detailEleAmount != null">
        detail_ele_amount = #{detailEleAmount,jdbcType=DECIMAL},
      </if>
      <if test="detailServiceAmount != null">
        detail_service_amount = #{detailServiceAmount,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.ChargeOrderChannelPriceRecord">
    update charge_order_channel_price_record
    set order_id = #{orderId,jdbcType=INTEGER},
      order_code = #{orderCode,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      detail_start_time = #{detailStartTime,jdbcType=TIMESTAMP},
      detail_end_time = #{detailEndTime,jdbcType=TIMESTAMP},
      detail_ele_price = #{detailElePrice,jdbcType=DECIMAL},
      detail_service_price = #{detailServicePrice,jdbcType=DECIMAL},
      detail_power = #{detailPower,jdbcType=DECIMAL},
      detail_ele_amount = #{detailEleAmount,jdbcType=DECIMAL},
      detail_service_amount = #{detailServiceAmount,jdbcType=DECIMAL}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>