<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.TemporalSharedVehicleDailyUsageMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.TemporalSharedVehicleDailyUsage">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="warn_sn" jdbcType="VARCHAR" property="warnSn" />
    <result column="record_status" jdbcType="TINYINT" property="recordStatus" />
    <result column="day_time" jdbcType="DATE" property="dayTime" />
    <result column="day_type" jdbcType="TINYINT" property="dayType" />
    <result column="daily_start_time" jdbcType="TIMESTAMP" property="dailyStartTime" />
    <result column="daily_end_time" jdbcType="TIMESTAMP" property="dailyEndTime" />
    <result column="daily_duration" jdbcType="INTEGER" property="dailyDuration" />
    <result column="daily_trip_mileage" jdbcType="DECIMAL" property="dailyTripMileage" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="vehicle_license" jdbcType="VARCHAR" property="vehicleLicense" />
    <result column="vehicle_vin" jdbcType="VARCHAR" property="vehicleVin" />
    <result column="vehicle_belong_city_code" jdbcType="VARCHAR" property="vehicleBelongCityCode" />
    <result column="vehicle_belong_city_name" jdbcType="VARCHAR" property="vehicleBelongCityName" />
    <result column="vehicle_company_id" jdbcType="INTEGER" property="vehicleCompanyId" />
    <result column="vehicle_company_name" jdbcType="VARCHAR" property="vehicleCompanyName" />
    <result column="vehicle_struct_id" jdbcType="INTEGER" property="vehicleStructId" />
    <result column="vehicle_struct_name" jdbcType="VARCHAR" property="vehicleStructName" />
    <result column="vehicle_brand_id" jdbcType="INTEGER" property="vehicleBrandId" />
    <result column="vehicle_brand_code" jdbcType="VARCHAR" property="vehicleBrandCode" />
    <result column="vehicle_brand_name" jdbcType="VARCHAR" property="vehicleBrandName" />
    <result column="vehicle_creator_id" jdbcType="INTEGER" property="vehicleCreatorId" />
    <result column="vehicle_creator_name" jdbcType="VARCHAR" property="vehicleCreatorName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, warn_sn, record_status, day_time, day_type, daily_start_time, daily_end_time, 
    daily_duration, daily_trip_mileage, vehicle_id, vehicle_license, vehicle_vin, vehicle_belong_city_code, 
    vehicle_belong_city_name, vehicle_company_id, vehicle_company_name, vehicle_struct_id, 
    vehicle_struct_name, vehicle_brand_id, vehicle_brand_code, vehicle_brand_name, vehicle_creator_id, 
    vehicle_creator_name
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.TemporalSharedVehicleDailyUsageExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_temporal_shared_vehicle_daily_usage
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_temporal_shared_vehicle_daily_usage
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_temporal_shared_vehicle_daily_usage
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.order.entity.mrcar.TemporalSharedVehicleDailyUsageExample">
    delete from t_temporal_shared_vehicle_daily_usage
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.TemporalSharedVehicleDailyUsage">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into t_temporal_shared_vehicle_daily_usage (warn_sn, record_status, day_time, 
      day_type, daily_start_time, daily_end_time, 
      daily_duration, daily_trip_mileage, vehicle_id, 
      vehicle_license, vehicle_vin, vehicle_belong_city_code, 
      vehicle_belong_city_name, vehicle_company_id, 
      vehicle_company_name, vehicle_struct_id, vehicle_struct_name, 
      vehicle_brand_id, vehicle_brand_code, vehicle_brand_name, 
      vehicle_creator_id, vehicle_creator_name)
    values (#{warnSn,jdbcType=VARCHAR}, #{recordStatus,jdbcType=TINYINT}, #{dayTime,jdbcType=DATE}, 
      #{dayType,jdbcType=TINYINT}, #{dailyStartTime,jdbcType=TIMESTAMP}, #{dailyEndTime,jdbcType=TIMESTAMP}, 
      #{dailyDuration,jdbcType=INTEGER}, #{dailyTripMileage,jdbcType=DECIMAL}, #{vehicleId,jdbcType=BIGINT}, 
      #{vehicleLicense,jdbcType=VARCHAR}, #{vehicleVin,jdbcType=VARCHAR}, #{vehicleBelongCityCode,jdbcType=VARCHAR}, 
      #{vehicleBelongCityName,jdbcType=VARCHAR}, #{vehicleCompanyId,jdbcType=INTEGER}, 
      #{vehicleCompanyName,jdbcType=VARCHAR}, #{vehicleStructId,jdbcType=INTEGER}, #{vehicleStructName,jdbcType=VARCHAR}, 
      #{vehicleBrandId,jdbcType=INTEGER}, #{vehicleBrandCode,jdbcType=VARCHAR}, #{vehicleBrandName,jdbcType=VARCHAR}, 
      #{vehicleCreatorId,jdbcType=INTEGER}, #{vehicleCreatorName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.TemporalSharedVehicleDailyUsage">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into t_temporal_shared_vehicle_daily_usage
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="warnSn != null">
        warn_sn,
      </if>
      <if test="recordStatus != null">
        record_status,
      </if>
      <if test="dayTime != null">
        day_time,
      </if>
      <if test="dayType != null">
        day_type,
      </if>
      <if test="dailyStartTime != null">
        daily_start_time,
      </if>
      <if test="dailyEndTime != null">
        daily_end_time,
      </if>
      <if test="dailyDuration != null">
        daily_duration,
      </if>
      <if test="dailyTripMileage != null">
        daily_trip_mileage,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="vehicleLicense != null">
        vehicle_license,
      </if>
      <if test="vehicleVin != null">
        vehicle_vin,
      </if>
      <if test="vehicleBelongCityCode != null">
        vehicle_belong_city_code,
      </if>
      <if test="vehicleBelongCityName != null">
        vehicle_belong_city_name,
      </if>
      <if test="vehicleCompanyId != null">
        vehicle_company_id,
      </if>
      <if test="vehicleCompanyName != null">
        vehicle_company_name,
      </if>
      <if test="vehicleStructId != null">
        vehicle_struct_id,
      </if>
      <if test="vehicleStructName != null">
        vehicle_struct_name,
      </if>
      <if test="vehicleBrandId != null">
        vehicle_brand_id,
      </if>
      <if test="vehicleBrandCode != null">
        vehicle_brand_code,
      </if>
      <if test="vehicleBrandName != null">
        vehicle_brand_name,
      </if>
      <if test="vehicleCreatorId != null">
        vehicle_creator_id,
      </if>
      <if test="vehicleCreatorName != null">
        vehicle_creator_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="warnSn != null">
        #{warnSn,jdbcType=VARCHAR},
      </if>
      <if test="recordStatus != null">
        #{recordStatus,jdbcType=TINYINT},
      </if>
      <if test="dayTime != null">
        #{dayTime,jdbcType=DATE},
      </if>
      <if test="dayType != null">
        #{dayType,jdbcType=TINYINT},
      </if>
      <if test="dailyStartTime != null">
        #{dailyStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dailyEndTime != null">
        #{dailyEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dailyDuration != null">
        #{dailyDuration,jdbcType=INTEGER},
      </if>
      <if test="dailyTripMileage != null">
        #{dailyTripMileage,jdbcType=DECIMAL},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleLicense != null">
        #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="vehicleVin != null">
        #{vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBelongCityCode != null">
        #{vehicleBelongCityCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBelongCityName != null">
        #{vehicleBelongCityName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleCompanyId != null">
        #{vehicleCompanyId,jdbcType=INTEGER},
      </if>
      <if test="vehicleCompanyName != null">
        #{vehicleCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleStructId != null">
        #{vehicleStructId,jdbcType=INTEGER},
      </if>
      <if test="vehicleStructName != null">
        #{vehicleStructName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBrandId != null">
        #{vehicleBrandId,jdbcType=INTEGER},
      </if>
      <if test="vehicleBrandCode != null">
        #{vehicleBrandCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBrandName != null">
        #{vehicleBrandName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleCreatorId != null">
        #{vehicleCreatorId,jdbcType=INTEGER},
      </if>
      <if test="vehicleCreatorName != null">
        #{vehicleCreatorName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.TemporalSharedVehicleDailyUsageExample" resultType="java.lang.Long">
    select count(*) from t_temporal_shared_vehicle_daily_usage
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update t_temporal_shared_vehicle_daily_usage
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.warnSn != null">
        warn_sn = #{row.warnSn,jdbcType=VARCHAR},
      </if>
      <if test="row.recordStatus != null">
        record_status = #{row.recordStatus,jdbcType=TINYINT},
      </if>
      <if test="row.dayTime != null">
        day_time = #{row.dayTime,jdbcType=DATE},
      </if>
      <if test="row.dayType != null">
        day_type = #{row.dayType,jdbcType=TINYINT},
      </if>
      <if test="row.dailyStartTime != null">
        daily_start_time = #{row.dailyStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.dailyEndTime != null">
        daily_end_time = #{row.dailyEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.dailyDuration != null">
        daily_duration = #{row.dailyDuration,jdbcType=INTEGER},
      </if>
      <if test="row.dailyTripMileage != null">
        daily_trip_mileage = #{row.dailyTripMileage,jdbcType=DECIMAL},
      </if>
      <if test="row.vehicleId != null">
        vehicle_id = #{row.vehicleId,jdbcType=BIGINT},
      </if>
      <if test="row.vehicleLicense != null">
        vehicle_license = #{row.vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleVin != null">
        vehicle_vin = #{row.vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleBelongCityCode != null">
        vehicle_belong_city_code = #{row.vehicleBelongCityCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleBelongCityName != null">
        vehicle_belong_city_name = #{row.vehicleBelongCityName,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleCompanyId != null">
        vehicle_company_id = #{row.vehicleCompanyId,jdbcType=INTEGER},
      </if>
      <if test="row.vehicleCompanyName != null">
        vehicle_company_name = #{row.vehicleCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleStructId != null">
        vehicle_struct_id = #{row.vehicleStructId,jdbcType=INTEGER},
      </if>
      <if test="row.vehicleStructName != null">
        vehicle_struct_name = #{row.vehicleStructName,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleBrandId != null">
        vehicle_brand_id = #{row.vehicleBrandId,jdbcType=INTEGER},
      </if>
      <if test="row.vehicleBrandCode != null">
        vehicle_brand_code = #{row.vehicleBrandCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleBrandName != null">
        vehicle_brand_name = #{row.vehicleBrandName,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleCreatorId != null">
        vehicle_creator_id = #{row.vehicleCreatorId,jdbcType=INTEGER},
      </if>
      <if test="row.vehicleCreatorName != null">
        vehicle_creator_name = #{row.vehicleCreatorName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update t_temporal_shared_vehicle_daily_usage
    set id = #{row.id,jdbcType=BIGINT},
      warn_sn = #{row.warnSn,jdbcType=VARCHAR},
      record_status = #{row.recordStatus,jdbcType=TINYINT},
      day_time = #{row.dayTime,jdbcType=DATE},
      day_type = #{row.dayType,jdbcType=TINYINT},
      daily_start_time = #{row.dailyStartTime,jdbcType=TIMESTAMP},
      daily_end_time = #{row.dailyEndTime,jdbcType=TIMESTAMP},
      daily_duration = #{row.dailyDuration,jdbcType=INTEGER},
      daily_trip_mileage = #{row.dailyTripMileage,jdbcType=DECIMAL},
      vehicle_id = #{row.vehicleId,jdbcType=BIGINT},
      vehicle_license = #{row.vehicleLicense,jdbcType=VARCHAR},
      vehicle_vin = #{row.vehicleVin,jdbcType=VARCHAR},
      vehicle_belong_city_code = #{row.vehicleBelongCityCode,jdbcType=VARCHAR},
      vehicle_belong_city_name = #{row.vehicleBelongCityName,jdbcType=VARCHAR},
      vehicle_company_id = #{row.vehicleCompanyId,jdbcType=INTEGER},
      vehicle_company_name = #{row.vehicleCompanyName,jdbcType=VARCHAR},
      vehicle_struct_id = #{row.vehicleStructId,jdbcType=INTEGER},
      vehicle_struct_name = #{row.vehicleStructName,jdbcType=VARCHAR},
      vehicle_brand_id = #{row.vehicleBrandId,jdbcType=INTEGER},
      vehicle_brand_code = #{row.vehicleBrandCode,jdbcType=VARCHAR},
      vehicle_brand_name = #{row.vehicleBrandName,jdbcType=VARCHAR},
      vehicle_creator_id = #{row.vehicleCreatorId,jdbcType=INTEGER},
      vehicle_creator_name = #{row.vehicleCreatorName,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.TemporalSharedVehicleDailyUsage">
    update t_temporal_shared_vehicle_daily_usage
    <set>
      <if test="warnSn != null">
        warn_sn = #{warnSn,jdbcType=VARCHAR},
      </if>
      <if test="recordStatus != null">
        record_status = #{recordStatus,jdbcType=TINYINT},
      </if>
      <if test="dayTime != null">
        day_time = #{dayTime,jdbcType=DATE},
      </if>
      <if test="dayType != null">
        day_type = #{dayType,jdbcType=TINYINT},
      </if>
      <if test="dailyStartTime != null">
        daily_start_time = #{dailyStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dailyEndTime != null">
        daily_end_time = #{dailyEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dailyDuration != null">
        daily_duration = #{dailyDuration,jdbcType=INTEGER},
      </if>
      <if test="dailyTripMileage != null">
        daily_trip_mileage = #{dailyTripMileage,jdbcType=DECIMAL},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleLicense != null">
        vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="vehicleVin != null">
        vehicle_vin = #{vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBelongCityCode != null">
        vehicle_belong_city_code = #{vehicleBelongCityCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBelongCityName != null">
        vehicle_belong_city_name = #{vehicleBelongCityName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleCompanyId != null">
        vehicle_company_id = #{vehicleCompanyId,jdbcType=INTEGER},
      </if>
      <if test="vehicleCompanyName != null">
        vehicle_company_name = #{vehicleCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleStructId != null">
        vehicle_struct_id = #{vehicleStructId,jdbcType=INTEGER},
      </if>
      <if test="vehicleStructName != null">
        vehicle_struct_name = #{vehicleStructName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBrandId != null">
        vehicle_brand_id = #{vehicleBrandId,jdbcType=INTEGER},
      </if>
      <if test="vehicleBrandCode != null">
        vehicle_brand_code = #{vehicleBrandCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBrandName != null">
        vehicle_brand_name = #{vehicleBrandName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleCreatorId != null">
        vehicle_creator_id = #{vehicleCreatorId,jdbcType=INTEGER},
      </if>
      <if test="vehicleCreatorName != null">
        vehicle_creator_name = #{vehicleCreatorName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.TemporalSharedVehicleDailyUsage">
    update t_temporal_shared_vehicle_daily_usage
    set warn_sn = #{warnSn,jdbcType=VARCHAR},
      record_status = #{recordStatus,jdbcType=TINYINT},
      day_time = #{dayTime,jdbcType=DATE},
      day_type = #{dayType,jdbcType=TINYINT},
      daily_start_time = #{dailyStartTime,jdbcType=TIMESTAMP},
      daily_end_time = #{dailyEndTime,jdbcType=TIMESTAMP},
      daily_duration = #{dailyDuration,jdbcType=INTEGER},
      daily_trip_mileage = #{dailyTripMileage,jdbcType=DECIMAL},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      vehicle_vin = #{vehicleVin,jdbcType=VARCHAR},
      vehicle_belong_city_code = #{vehicleBelongCityCode,jdbcType=VARCHAR},
      vehicle_belong_city_name = #{vehicleBelongCityName,jdbcType=VARCHAR},
      vehicle_company_id = #{vehicleCompanyId,jdbcType=INTEGER},
      vehicle_company_name = #{vehicleCompanyName,jdbcType=VARCHAR},
      vehicle_struct_id = #{vehicleStructId,jdbcType=INTEGER},
      vehicle_struct_name = #{vehicleStructName,jdbcType=VARCHAR},
      vehicle_brand_id = #{vehicleBrandId,jdbcType=INTEGER},
      vehicle_brand_code = #{vehicleBrandCode,jdbcType=VARCHAR},
      vehicle_brand_name = #{vehicleBrandName,jdbcType=VARCHAR},
      vehicle_creator_id = #{vehicleCreatorId,jdbcType=INTEGER},
      vehicle_creator_name = #{vehicleCreatorName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>