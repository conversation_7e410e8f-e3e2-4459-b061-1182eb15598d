<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ScheduleMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.Schedule">
    <id column="schedule_id" jdbcType="BIGINT" property="scheduleId" />
    <result column="schedule_no" jdbcType="VARCHAR" property="scheduleNo" />
    <result column="order_apply_id" jdbcType="BIGINT" property="orderApplyId" />
    <result column="order_apply_no" jdbcType="VARCHAR" property="orderApplyNo" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="struct_id" jdbcType="INTEGER" property="structId" />
    <result column="schedule_status" jdbcType="TINYINT" property="scheduleStatus" />
    <result column="dispatcher_status" jdbcType="TINYINT" property="dispatcherStatus" />
    <result column="dispatcher_time" jdbcType="TIMESTAMP" property="dispatcherTime" />
    <result column="schedule_time" jdbcType="TIMESTAMP" property="scheduleTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="last_update_id" jdbcType="BIGINT" property="lastUpdateId" />
    <result column="last_update_name" jdbcType="VARCHAR" property="lastUpdateName" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
    <result column="order_type" jdbcType="TINYINT" property="orderType" />
    <result column="customer_id" jdbcType="INTEGER" property="customerId" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="customer_mobile" jdbcType="VARCHAR" property="customerMobile" />
    <result column="booking_order_stime" jdbcType="TIMESTAMP" property="bookingOrderStime" />
    <result column="booking_order_etime" jdbcType="TIMESTAMP" property="bookingOrderEtime" />
    <result column="booking_start_short_addr" jdbcType="VARCHAR" property="bookingStartShortAddr" />
    <result column="booking_end_short_addr" jdbcType="VARCHAR" property="bookingEndShortAddr" />
    <result column="booking_start_city_code" jdbcType="INTEGER" property="bookingStartCityCode" />
    <result column="booking_start_city_name" jdbcType="VARCHAR" property="bookingStartCityName" />
    <result column="booking_end_city_code" jdbcType="INTEGER" property="bookingEndCityCode" />
    <result column="booking_end_city_name" jdbcType="VARCHAR" property="bookingEndCityName" />
    <result column="booking_vehicle_total_count" jdbcType="INTEGER" property="bookingVehicleTotalCount" />
    <result column="service_code" jdbcType="VARCHAR" property="serviceCode" />
    <result column="passenger_name" jdbcType="VARCHAR" property="passengerName" />
    <result column="passenger_phone" jdbcType="VARCHAR" property="passengerPhone" />
    <result column="struct_name" jdbcType="VARCHAR" property="structName" />
    <result column="passenger_struct_id" jdbcType="INTEGER" property="passengerStructId" />
    <result column="passenger_struct_name" jdbcType="VARCHAR" property="passengerStructName" />
    <result column="supplier_id" jdbcType="INTEGER" property="supplierId" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="supplier_flag" jdbcType="INTEGER" property="supplierFlag" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    schedule_id, schedule_no, order_apply_id, order_apply_no, company_id, struct_id, 
    schedule_status, dispatcher_status, dispatcher_time, schedule_time, create_time, 
    last_update_id, last_update_name, last_update_time, order_type, customer_id, customer_name, 
    customer_mobile, booking_order_stime, booking_order_etime, booking_start_short_addr, 
    booking_end_short_addr, booking_start_city_code, booking_start_city_name, booking_end_city_code, 
    booking_end_city_name, booking_vehicle_total_count, service_code, passenger_name, 
    passenger_phone, struct_name, passenger_struct_id, passenger_struct_name, supplier_id, 
    supplier_name, supplier_flag
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.ScheduleExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from schedule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from schedule
    where schedule_id = #{scheduleId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from schedule
    where schedule_id = #{scheduleId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.order.entity.mrcar.ScheduleExample">
    delete from schedule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.Schedule">
    <selectKey keyProperty="scheduleId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into schedule (schedule_no, order_apply_id, order_apply_no, 
      company_id, struct_id, schedule_status, 
      dispatcher_status, dispatcher_time, schedule_time, 
      create_time, last_update_id, last_update_name, 
      last_update_time, order_type, customer_id, 
      customer_name, customer_mobile, booking_order_stime, 
      booking_order_etime, booking_start_short_addr, 
      booking_end_short_addr, booking_start_city_code, 
      booking_start_city_name, booking_end_city_code, 
      booking_end_city_name, booking_vehicle_total_count, 
      service_code, passenger_name, passenger_phone, 
      struct_name, passenger_struct_id, passenger_struct_name, 
      supplier_id, supplier_name, supplier_flag
      )
    values (#{scheduleNo,jdbcType=VARCHAR}, #{orderApplyId,jdbcType=BIGINT}, #{orderApplyNo,jdbcType=VARCHAR}, 
      #{companyId,jdbcType=BIGINT}, #{structId,jdbcType=INTEGER}, #{scheduleStatus,jdbcType=TINYINT}, 
      #{dispatcherStatus,jdbcType=TINYINT}, #{dispatcherTime,jdbcType=TIMESTAMP}, #{scheduleTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP}, #{lastUpdateId,jdbcType=BIGINT}, #{lastUpdateName,jdbcType=VARCHAR}, 
      #{lastUpdateTime,jdbcType=TIMESTAMP}, #{orderType,jdbcType=TINYINT}, #{customerId,jdbcType=INTEGER}, 
      #{customerName,jdbcType=VARCHAR}, #{customerMobile,jdbcType=VARCHAR}, #{bookingOrderStime,jdbcType=TIMESTAMP}, 
      #{bookingOrderEtime,jdbcType=TIMESTAMP}, #{bookingStartShortAddr,jdbcType=VARCHAR}, 
      #{bookingEndShortAddr,jdbcType=VARCHAR}, #{bookingStartCityCode,jdbcType=INTEGER}, 
      #{bookingStartCityName,jdbcType=VARCHAR}, #{bookingEndCityCode,jdbcType=INTEGER}, 
      #{bookingEndCityName,jdbcType=VARCHAR}, #{bookingVehicleTotalCount,jdbcType=INTEGER}, 
      #{serviceCode,jdbcType=VARCHAR}, #{passengerName,jdbcType=VARCHAR}, #{passengerPhone,jdbcType=VARCHAR}, 
      #{structName,jdbcType=VARCHAR}, #{passengerStructId,jdbcType=INTEGER}, #{passengerStructName,jdbcType=VARCHAR}, 
      #{supplierId,jdbcType=INTEGER}, #{supplierName,jdbcType=VARCHAR}, #{supplierFlag,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.Schedule">
    <selectKey keyProperty="scheduleId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into schedule
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="scheduleNo != null">
        schedule_no,
      </if>
      <if test="orderApplyId != null">
        order_apply_id,
      </if>
      <if test="orderApplyNo != null">
        order_apply_no,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="structId != null">
        struct_id,
      </if>
      <if test="scheduleStatus != null">
        schedule_status,
      </if>
      <if test="dispatcherStatus != null">
        dispatcher_status,
      </if>
      <if test="dispatcherTime != null">
        dispatcher_time,
      </if>
      <if test="scheduleTime != null">
        schedule_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="lastUpdateId != null">
        last_update_id,
      </if>
      <if test="lastUpdateName != null">
        last_update_name,
      </if>
      <if test="lastUpdateTime != null">
        last_update_time,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="customerMobile != null">
        customer_mobile,
      </if>
      <if test="bookingOrderStime != null">
        booking_order_stime,
      </if>
      <if test="bookingOrderEtime != null">
        booking_order_etime,
      </if>
      <if test="bookingStartShortAddr != null">
        booking_start_short_addr,
      </if>
      <if test="bookingEndShortAddr != null">
        booking_end_short_addr,
      </if>
      <if test="bookingStartCityCode != null">
        booking_start_city_code,
      </if>
      <if test="bookingStartCityName != null">
        booking_start_city_name,
      </if>
      <if test="bookingEndCityCode != null">
        booking_end_city_code,
      </if>
      <if test="bookingEndCityName != null">
        booking_end_city_name,
      </if>
      <if test="bookingVehicleTotalCount != null">
        booking_vehicle_total_count,
      </if>
      <if test="serviceCode != null">
        service_code,
      </if>
      <if test="passengerName != null">
        passenger_name,
      </if>
      <if test="passengerPhone != null">
        passenger_phone,
      </if>
      <if test="structName != null">
        struct_name,
      </if>
      <if test="passengerStructId != null">
        passenger_struct_id,
      </if>
      <if test="passengerStructName != null">
        passenger_struct_name,
      </if>
      <if test="supplierId != null">
        supplier_id,
      </if>
      <if test="supplierName != null">
        supplier_name,
      </if>
      <if test="supplierFlag != null">
        supplier_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="scheduleNo != null">
        #{scheduleNo,jdbcType=VARCHAR},
      </if>
      <if test="orderApplyId != null">
        #{orderApplyId,jdbcType=BIGINT},
      </if>
      <if test="orderApplyNo != null">
        #{orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="structId != null">
        #{structId,jdbcType=INTEGER},
      </if>
      <if test="scheduleStatus != null">
        #{scheduleStatus,jdbcType=TINYINT},
      </if>
      <if test="dispatcherStatus != null">
        #{dispatcherStatus,jdbcType=TINYINT},
      </if>
      <if test="dispatcherTime != null">
        #{dispatcherTime,jdbcType=TIMESTAMP},
      </if>
      <if test="scheduleTime != null">
        #{scheduleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateId != null">
        #{lastUpdateId,jdbcType=BIGINT},
      </if>
      <if test="lastUpdateName != null">
        #{lastUpdateName,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=TINYINT},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=INTEGER},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerMobile != null">
        #{customerMobile,jdbcType=VARCHAR},
      </if>
      <if test="bookingOrderStime != null">
        #{bookingOrderStime,jdbcType=TIMESTAMP},
      </if>
      <if test="bookingOrderEtime != null">
        #{bookingOrderEtime,jdbcType=TIMESTAMP},
      </if>
      <if test="bookingStartShortAddr != null">
        #{bookingStartShortAddr,jdbcType=VARCHAR},
      </if>
      <if test="bookingEndShortAddr != null">
        #{bookingEndShortAddr,jdbcType=VARCHAR},
      </if>
      <if test="bookingStartCityCode != null">
        #{bookingStartCityCode,jdbcType=INTEGER},
      </if>
      <if test="bookingStartCityName != null">
        #{bookingStartCityName,jdbcType=VARCHAR},
      </if>
      <if test="bookingEndCityCode != null">
        #{bookingEndCityCode,jdbcType=INTEGER},
      </if>
      <if test="bookingEndCityName != null">
        #{bookingEndCityName,jdbcType=VARCHAR},
      </if>
      <if test="bookingVehicleTotalCount != null">
        #{bookingVehicleTotalCount,jdbcType=INTEGER},
      </if>
      <if test="serviceCode != null">
        #{serviceCode,jdbcType=VARCHAR},
      </if>
      <if test="passengerName != null">
        #{passengerName,jdbcType=VARCHAR},
      </if>
      <if test="passengerPhone != null">
        #{passengerPhone,jdbcType=VARCHAR},
      </if>
      <if test="structName != null">
        #{structName,jdbcType=VARCHAR},
      </if>
      <if test="passengerStructId != null">
        #{passengerStructId,jdbcType=INTEGER},
      </if>
      <if test="passengerStructName != null">
        #{passengerStructName,jdbcType=VARCHAR},
      </if>
      <if test="supplierId != null">
        #{supplierId,jdbcType=INTEGER},
      </if>
      <if test="supplierName != null">
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="supplierFlag != null">
        #{supplierFlag,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.ScheduleExample" resultType="java.lang.Long">
    select count(*) from schedule
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update schedule
    <set>
      <if test="row.scheduleId != null">
        schedule_id = #{row.scheduleId,jdbcType=BIGINT},
      </if>
      <if test="row.scheduleNo != null">
        schedule_no = #{row.scheduleNo,jdbcType=VARCHAR},
      </if>
      <if test="row.orderApplyId != null">
        order_apply_id = #{row.orderApplyId,jdbcType=BIGINT},
      </if>
      <if test="row.orderApplyNo != null">
        order_apply_no = #{row.orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="row.companyId != null">
        company_id = #{row.companyId,jdbcType=BIGINT},
      </if>
      <if test="row.structId != null">
        struct_id = #{row.structId,jdbcType=INTEGER},
      </if>
      <if test="row.scheduleStatus != null">
        schedule_status = #{row.scheduleStatus,jdbcType=TINYINT},
      </if>
      <if test="row.dispatcherStatus != null">
        dispatcher_status = #{row.dispatcherStatus,jdbcType=TINYINT},
      </if>
      <if test="row.dispatcherTime != null">
        dispatcher_time = #{row.dispatcherTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.scheduleTime != null">
        schedule_time = #{row.scheduleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.lastUpdateId != null">
        last_update_id = #{row.lastUpdateId,jdbcType=BIGINT},
      </if>
      <if test="row.lastUpdateName != null">
        last_update_name = #{row.lastUpdateName,jdbcType=VARCHAR},
      </if>
      <if test="row.lastUpdateTime != null">
        last_update_time = #{row.lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.orderType != null">
        order_type = #{row.orderType,jdbcType=TINYINT},
      </if>
      <if test="row.customerId != null">
        customer_id = #{row.customerId,jdbcType=INTEGER},
      </if>
      <if test="row.customerName != null">
        customer_name = #{row.customerName,jdbcType=VARCHAR},
      </if>
      <if test="row.customerMobile != null">
        customer_mobile = #{row.customerMobile,jdbcType=VARCHAR},
      </if>
      <if test="row.bookingOrderStime != null">
        booking_order_stime = #{row.bookingOrderStime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.bookingOrderEtime != null">
        booking_order_etime = #{row.bookingOrderEtime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.bookingStartShortAddr != null">
        booking_start_short_addr = #{row.bookingStartShortAddr,jdbcType=VARCHAR},
      </if>
      <if test="row.bookingEndShortAddr != null">
        booking_end_short_addr = #{row.bookingEndShortAddr,jdbcType=VARCHAR},
      </if>
      <if test="row.bookingStartCityCode != null">
        booking_start_city_code = #{row.bookingStartCityCode,jdbcType=INTEGER},
      </if>
      <if test="row.bookingStartCityName != null">
        booking_start_city_name = #{row.bookingStartCityName,jdbcType=VARCHAR},
      </if>
      <if test="row.bookingEndCityCode != null">
        booking_end_city_code = #{row.bookingEndCityCode,jdbcType=INTEGER},
      </if>
      <if test="row.bookingEndCityName != null">
        booking_end_city_name = #{row.bookingEndCityName,jdbcType=VARCHAR},
      </if>
      <if test="row.bookingVehicleTotalCount != null">
        booking_vehicle_total_count = #{row.bookingVehicleTotalCount,jdbcType=INTEGER},
      </if>
      <if test="row.serviceCode != null">
        service_code = #{row.serviceCode,jdbcType=VARCHAR},
      </if>
      <if test="row.passengerName != null">
        passenger_name = #{row.passengerName,jdbcType=VARCHAR},
      </if>
      <if test="row.passengerPhone != null">
        passenger_phone = #{row.passengerPhone,jdbcType=VARCHAR},
      </if>
      <if test="row.structName != null">
        struct_name = #{row.structName,jdbcType=VARCHAR},
      </if>
      <if test="row.passengerStructId != null">
        passenger_struct_id = #{row.passengerStructId,jdbcType=INTEGER},
      </if>
      <if test="row.passengerStructName != null">
        passenger_struct_name = #{row.passengerStructName,jdbcType=VARCHAR},
      </if>
      <if test="row.supplierId != null">
        supplier_id = #{row.supplierId,jdbcType=INTEGER},
      </if>
      <if test="row.supplierName != null">
        supplier_name = #{row.supplierName,jdbcType=VARCHAR},
      </if>
      <if test="row.supplierFlag != null">
        supplier_flag = #{row.supplierFlag,jdbcType=INTEGER},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update schedule
    set schedule_id = #{row.scheduleId,jdbcType=BIGINT},
      schedule_no = #{row.scheduleNo,jdbcType=VARCHAR},
      order_apply_id = #{row.orderApplyId,jdbcType=BIGINT},
      order_apply_no = #{row.orderApplyNo,jdbcType=VARCHAR},
      company_id = #{row.companyId,jdbcType=BIGINT},
      struct_id = #{row.structId,jdbcType=INTEGER},
      schedule_status = #{row.scheduleStatus,jdbcType=TINYINT},
      dispatcher_status = #{row.dispatcherStatus,jdbcType=TINYINT},
      dispatcher_time = #{row.dispatcherTime,jdbcType=TIMESTAMP},
      schedule_time = #{row.scheduleTime,jdbcType=TIMESTAMP},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      last_update_id = #{row.lastUpdateId,jdbcType=BIGINT},
      last_update_name = #{row.lastUpdateName,jdbcType=VARCHAR},
      last_update_time = #{row.lastUpdateTime,jdbcType=TIMESTAMP},
      order_type = #{row.orderType,jdbcType=TINYINT},
      customer_id = #{row.customerId,jdbcType=INTEGER},
      customer_name = #{row.customerName,jdbcType=VARCHAR},
      customer_mobile = #{row.customerMobile,jdbcType=VARCHAR},
      booking_order_stime = #{row.bookingOrderStime,jdbcType=TIMESTAMP},
      booking_order_etime = #{row.bookingOrderEtime,jdbcType=TIMESTAMP},
      booking_start_short_addr = #{row.bookingStartShortAddr,jdbcType=VARCHAR},
      booking_end_short_addr = #{row.bookingEndShortAddr,jdbcType=VARCHAR},
      booking_start_city_code = #{row.bookingStartCityCode,jdbcType=INTEGER},
      booking_start_city_name = #{row.bookingStartCityName,jdbcType=VARCHAR},
      booking_end_city_code = #{row.bookingEndCityCode,jdbcType=INTEGER},
      booking_end_city_name = #{row.bookingEndCityName,jdbcType=VARCHAR},
      booking_vehicle_total_count = #{row.bookingVehicleTotalCount,jdbcType=INTEGER},
      service_code = #{row.serviceCode,jdbcType=VARCHAR},
      passenger_name = #{row.passengerName,jdbcType=VARCHAR},
      passenger_phone = #{row.passengerPhone,jdbcType=VARCHAR},
      struct_name = #{row.structName,jdbcType=VARCHAR},
      passenger_struct_id = #{row.passengerStructId,jdbcType=INTEGER},
      passenger_struct_name = #{row.passengerStructName,jdbcType=VARCHAR},
      supplier_id = #{row.supplierId,jdbcType=INTEGER},
      supplier_name = #{row.supplierName,jdbcType=VARCHAR},
      supplier_flag = #{row.supplierFlag,jdbcType=INTEGER}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.Schedule">
    update schedule
    <set>
      <if test="scheduleNo != null">
        schedule_no = #{scheduleNo,jdbcType=VARCHAR},
      </if>
      <if test="orderApplyId != null">
        order_apply_id = #{orderApplyId,jdbcType=BIGINT},
      </if>
      <if test="orderApplyNo != null">
        order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="structId != null">
        struct_id = #{structId,jdbcType=INTEGER},
      </if>
      <if test="scheduleStatus != null">
        schedule_status = #{scheduleStatus,jdbcType=TINYINT},
      </if>
      <if test="dispatcherStatus != null">
        dispatcher_status = #{dispatcherStatus,jdbcType=TINYINT},
      </if>
      <if test="dispatcherTime != null">
        dispatcher_time = #{dispatcherTime,jdbcType=TIMESTAMP},
      </if>
      <if test="scheduleTime != null">
        schedule_time = #{scheduleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateId != null">
        last_update_id = #{lastUpdateId,jdbcType=BIGINT},
      </if>
      <if test="lastUpdateName != null">
        last_update_name = #{lastUpdateName,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=TINYINT},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=INTEGER},
      </if>
      <if test="customerName != null">
        customer_name = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerMobile != null">
        customer_mobile = #{customerMobile,jdbcType=VARCHAR},
      </if>
      <if test="bookingOrderStime != null">
        booking_order_stime = #{bookingOrderStime,jdbcType=TIMESTAMP},
      </if>
      <if test="bookingOrderEtime != null">
        booking_order_etime = #{bookingOrderEtime,jdbcType=TIMESTAMP},
      </if>
      <if test="bookingStartShortAddr != null">
        booking_start_short_addr = #{bookingStartShortAddr,jdbcType=VARCHAR},
      </if>
      <if test="bookingEndShortAddr != null">
        booking_end_short_addr = #{bookingEndShortAddr,jdbcType=VARCHAR},
      </if>
      <if test="bookingStartCityCode != null">
        booking_start_city_code = #{bookingStartCityCode,jdbcType=INTEGER},
      </if>
      <if test="bookingStartCityName != null">
        booking_start_city_name = #{bookingStartCityName,jdbcType=VARCHAR},
      </if>
      <if test="bookingEndCityCode != null">
        booking_end_city_code = #{bookingEndCityCode,jdbcType=INTEGER},
      </if>
      <if test="bookingEndCityName != null">
        booking_end_city_name = #{bookingEndCityName,jdbcType=VARCHAR},
      </if>
      <if test="bookingVehicleTotalCount != null">
        booking_vehicle_total_count = #{bookingVehicleTotalCount,jdbcType=INTEGER},
      </if>
      <if test="serviceCode != null">
        service_code = #{serviceCode,jdbcType=VARCHAR},
      </if>
      <if test="passengerName != null">
        passenger_name = #{passengerName,jdbcType=VARCHAR},
      </if>
      <if test="passengerPhone != null">
        passenger_phone = #{passengerPhone,jdbcType=VARCHAR},
      </if>
      <if test="structName != null">
        struct_name = #{structName,jdbcType=VARCHAR},
      </if>
      <if test="passengerStructId != null">
        passenger_struct_id = #{passengerStructId,jdbcType=INTEGER},
      </if>
      <if test="passengerStructName != null">
        passenger_struct_name = #{passengerStructName,jdbcType=VARCHAR},
      </if>
      <if test="supplierId != null">
        supplier_id = #{supplierId,jdbcType=INTEGER},
      </if>
      <if test="supplierName != null">
        supplier_name = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="supplierFlag != null">
        supplier_flag = #{supplierFlag,jdbcType=INTEGER},
      </if>
    </set>
    where schedule_id = #{scheduleId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.Schedule">
    update schedule
    set schedule_no = #{scheduleNo,jdbcType=VARCHAR},
      order_apply_id = #{orderApplyId,jdbcType=BIGINT},
      order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=BIGINT},
      struct_id = #{structId,jdbcType=INTEGER},
      schedule_status = #{scheduleStatus,jdbcType=TINYINT},
      dispatcher_status = #{dispatcherStatus,jdbcType=TINYINT},
      dispatcher_time = #{dispatcherTime,jdbcType=TIMESTAMP},
      schedule_time = #{scheduleTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      last_update_id = #{lastUpdateId,jdbcType=BIGINT},
      last_update_name = #{lastUpdateName,jdbcType=VARCHAR},
      last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      order_type = #{orderType,jdbcType=TINYINT},
      customer_id = #{customerId,jdbcType=INTEGER},
      customer_name = #{customerName,jdbcType=VARCHAR},
      customer_mobile = #{customerMobile,jdbcType=VARCHAR},
      booking_order_stime = #{bookingOrderStime,jdbcType=TIMESTAMP},
      booking_order_etime = #{bookingOrderEtime,jdbcType=TIMESTAMP},
      booking_start_short_addr = #{bookingStartShortAddr,jdbcType=VARCHAR},
      booking_end_short_addr = #{bookingEndShortAddr,jdbcType=VARCHAR},
      booking_start_city_code = #{bookingStartCityCode,jdbcType=INTEGER},
      booking_start_city_name = #{bookingStartCityName,jdbcType=VARCHAR},
      booking_end_city_code = #{bookingEndCityCode,jdbcType=INTEGER},
      booking_end_city_name = #{bookingEndCityName,jdbcType=VARCHAR},
      booking_vehicle_total_count = #{bookingVehicleTotalCount,jdbcType=INTEGER},
      service_code = #{serviceCode,jdbcType=VARCHAR},
      passenger_name = #{passengerName,jdbcType=VARCHAR},
      passenger_phone = #{passengerPhone,jdbcType=VARCHAR},
      struct_name = #{structName,jdbcType=VARCHAR},
      passenger_struct_id = #{passengerStructId,jdbcType=INTEGER},
      passenger_struct_name = #{passengerStructName,jdbcType=VARCHAR},
      supplier_id = #{supplierId,jdbcType=INTEGER},
      supplier_name = #{supplierName,jdbcType=VARCHAR},
      supplier_flag = #{supplierFlag,jdbcType=INTEGER}
    where schedule_id = #{scheduleId,jdbcType=BIGINT}
  </update>
</mapper>