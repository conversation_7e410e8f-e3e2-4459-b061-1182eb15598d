<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.TemporalSharedVehicleStatisticsMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.TemporalSharedVehicleStatistics">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="day_time" jdbcType="DATE" property="dayTime" />
    <result column="day_type" jdbcType="TINYINT" property="dayType" />
    <result column="daily_duration" jdbcType="INTEGER" property="dailyDuration" />
    <result column="daily_trip_mileage" jdbcType="DECIMAL" property="dailyTripMileage" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="vehicle_license" jdbcType="VARCHAR" property="vehicleLicense" />
    <result column="vehicle_vin" jdbcType="VARCHAR" property="vehicleVin" />
    <result column="vehicle_belong_city_code" jdbcType="VARCHAR" property="vehicleBelongCityCode" />
    <result column="vehicle_belong_city_name" jdbcType="VARCHAR" property="vehicleBelongCityName" />
    <result column="vehicle_company_id" jdbcType="INTEGER" property="vehicleCompanyId" />
    <result column="vehicle_company_name" jdbcType="VARCHAR" property="vehicleCompanyName" />
    <result column="vehicle_struct_id" jdbcType="INTEGER" property="vehicleStructId" />
    <result column="vehicle_struct_name" jdbcType="VARCHAR" property="vehicleStructName" />
    <result column="vehicle_brand_id" jdbcType="INTEGER" property="vehicleBrandId" />
    <result column="vehicle_brand_code" jdbcType="VARCHAR" property="vehicleBrandCode" />
    <result column="vehicle_brand_name" jdbcType="VARCHAR" property="vehicleBrandName" />
    <result column="vehicle_creator_id" jdbcType="INTEGER" property="vehicleCreatorId" />
    <result column="vehicle_creator_name" jdbcType="VARCHAR" property="vehicleCreatorName" />
  </resultMap>
  <sql id="Base_Column_List">
    id, day_time, day_type, daily_duration, daily_trip_mileage, vehicle_id, vehicle_license,
    vehicle_vin, vehicle_belong_city_code, vehicle_belong_city_name, vehicle_company_id,
    vehicle_company_name, vehicle_struct_id, vehicle_struct_name, vehicle_brand_id, vehicle_brand_code,
    vehicle_brand_name, vehicle_creator_id, vehicle_creator_name
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_temporal_shared_vehicle_statistics
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from t_temporal_shared_vehicle_statistics
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.TemporalSharedVehicleStatistics">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into t_temporal_shared_vehicle_statistics (day_time, day_type, daily_duration,
    daily_trip_mileage, vehicle_id, vehicle_license,
    vehicle_vin, vehicle_belong_city_code, vehicle_belong_city_name,
    vehicle_company_id, vehicle_company_name, vehicle_struct_id,
    vehicle_struct_name, vehicle_brand_id, vehicle_brand_code,
    vehicle_brand_name, vehicle_creator_id, vehicle_creator_name
    )
    values (#{dayTime,jdbcType=DATE}, #{dayType,jdbcType=TINYINT}, #{dailyDuration,jdbcType=INTEGER},
    #{dailyTripMileage,jdbcType=DECIMAL}, #{vehicleId,jdbcType=BIGINT}, #{vehicleLicense,jdbcType=VARCHAR},
    #{vehicleVin,jdbcType=VARCHAR}, #{vehicleBelongCityCode,jdbcType=VARCHAR}, #{vehicleBelongCityName,jdbcType=VARCHAR},
    #{vehicleCompanyId,jdbcType=INTEGER}, #{vehicleCompanyName,jdbcType=VARCHAR}, #{vehicleStructId,jdbcType=INTEGER},
    #{vehicleStructName,jdbcType=VARCHAR}, #{vehicleBrandId,jdbcType=INTEGER}, #{vehicleBrandCode,jdbcType=VARCHAR},
    #{vehicleBrandName,jdbcType=VARCHAR}, #{vehicleCreatorId,jdbcType=INTEGER}, #{vehicleCreatorName,jdbcType=VARCHAR}
    )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.TemporalSharedVehicleStatistics">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into t_temporal_shared_vehicle_statistics
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="dayTime != null">
        day_time,
      </if>
      <if test="dayType != null">
        day_type,
      </if>
      <if test="dailyDuration != null">
        daily_duration,
      </if>
      <if test="dailyTripMileage != null">
        daily_trip_mileage,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="vehicleLicense != null">
        vehicle_license,
      </if>
      <if test="vehicleVin != null">
        vehicle_vin,
      </if>
      <if test="vehicleBelongCityCode != null">
        vehicle_belong_city_code,
      </if>
      <if test="vehicleBelongCityName != null">
        vehicle_belong_city_name,
      </if>
      <if test="vehicleCompanyId != null">
        vehicle_company_id,
      </if>
      <if test="vehicleCompanyName != null">
        vehicle_company_name,
      </if>
      <if test="vehicleStructId != null">
        vehicle_struct_id,
      </if>
      <if test="vehicleStructName != null">
        vehicle_struct_name,
      </if>
      <if test="vehicleBrandId != null">
        vehicle_brand_id,
      </if>
      <if test="vehicleBrandCode != null">
        vehicle_brand_code,
      </if>
      <if test="vehicleBrandName != null">
        vehicle_brand_name,
      </if>
      <if test="vehicleCreatorId != null">
        vehicle_creator_id,
      </if>
      <if test="vehicleCreatorName != null">
        vehicle_creator_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="dayTime != null">
        #{dayTime,jdbcType=DATE},
      </if>
      <if test="dayType != null">
        #{dayType,jdbcType=TINYINT},
      </if>
      <if test="dailyDuration != null">
        #{dailyDuration,jdbcType=INTEGER},
      </if>
      <if test="dailyTripMileage != null">
        #{dailyTripMileage,jdbcType=DECIMAL},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleLicense != null">
        #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="vehicleVin != null">
        #{vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBelongCityCode != null">
        #{vehicleBelongCityCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBelongCityName != null">
        #{vehicleBelongCityName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleCompanyId != null">
        #{vehicleCompanyId,jdbcType=INTEGER},
      </if>
      <if test="vehicleCompanyName != null">
        #{vehicleCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleStructId != null">
        #{vehicleStructId,jdbcType=INTEGER},
      </if>
      <if test="vehicleStructName != null">
        #{vehicleStructName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBrandId != null">
        #{vehicleBrandId,jdbcType=INTEGER},
      </if>
      <if test="vehicleBrandCode != null">
        #{vehicleBrandCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBrandName != null">
        #{vehicleBrandName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleCreatorId != null">
        #{vehicleCreatorId,jdbcType=INTEGER},
      </if>
      <if test="vehicleCreatorName != null">
        #{vehicleCreatorName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.TemporalSharedVehicleStatistics">
    update t_temporal_shared_vehicle_statistics
    <set>
      <if test="dayTime != null">
        day_time = #{dayTime,jdbcType=DATE},
      </if>
      <if test="dayType != null">
        day_type = #{dayType,jdbcType=TINYINT},
      </if>
      <if test="dailyDuration != null">
        daily_duration = #{dailyDuration,jdbcType=INTEGER},
      </if>
      <if test="dailyTripMileage != null">
        daily_trip_mileage = #{dailyTripMileage,jdbcType=DECIMAL},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleLicense != null">
        vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="vehicleVin != null">
        vehicle_vin = #{vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBelongCityCode != null">
        vehicle_belong_city_code = #{vehicleBelongCityCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBelongCityName != null">
        vehicle_belong_city_name = #{vehicleBelongCityName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleCompanyId != null">
        vehicle_company_id = #{vehicleCompanyId,jdbcType=INTEGER},
      </if>
      <if test="vehicleCompanyName != null">
        vehicle_company_name = #{vehicleCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleStructId != null">
        vehicle_struct_id = #{vehicleStructId,jdbcType=INTEGER},
      </if>
      <if test="vehicleStructName != null">
        vehicle_struct_name = #{vehicleStructName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBrandId != null">
        vehicle_brand_id = #{vehicleBrandId,jdbcType=INTEGER},
      </if>
      <if test="vehicleBrandCode != null">
        vehicle_brand_code = #{vehicleBrandCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBrandName != null">
        vehicle_brand_name = #{vehicleBrandName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleCreatorId != null">
        vehicle_creator_id = #{vehicleCreatorId,jdbcType=INTEGER},
      </if>
      <if test="vehicleCreatorName != null">
        vehicle_creator_name = #{vehicleCreatorName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.TemporalSharedVehicleStatistics">
    update t_temporal_shared_vehicle_statistics
    set day_time = #{dayTime,jdbcType=DATE},
      day_type = #{dayType,jdbcType=TINYINT},
      daily_duration = #{dailyDuration,jdbcType=INTEGER},
      daily_trip_mileage = #{dailyTripMileage,jdbcType=DECIMAL},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      vehicle_vin = #{vehicleVin,jdbcType=VARCHAR},
      vehicle_belong_city_code = #{vehicleBelongCityCode,jdbcType=VARCHAR},
      vehicle_belong_city_name = #{vehicleBelongCityName,jdbcType=VARCHAR},
      vehicle_company_id = #{vehicleCompanyId,jdbcType=INTEGER},
      vehicle_company_name = #{vehicleCompanyName,jdbcType=VARCHAR},
      vehicle_struct_id = #{vehicleStructId,jdbcType=INTEGER},
      vehicle_struct_name = #{vehicleStructName,jdbcType=VARCHAR},
      vehicle_brand_id = #{vehicleBrandId,jdbcType=INTEGER},
      vehicle_brand_code = #{vehicleBrandCode,jdbcType=VARCHAR},
      vehicle_brand_name = #{vehicleBrandName,jdbcType=VARCHAR},
      vehicle_creator_id = #{vehicleCreatorId,jdbcType=INTEGER},
      vehicle_creator_name = #{vehicleCreatorName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>