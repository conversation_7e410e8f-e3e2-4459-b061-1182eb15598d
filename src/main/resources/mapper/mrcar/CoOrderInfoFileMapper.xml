<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.CoOrderInfoFileMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.CoOrderInfoFile">
    <id column="order_info_file_id" jdbcType="INTEGER" property="orderInfoFileId" />
    <result column="create_id" jdbcType="INTEGER" property="createId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_mobile" jdbcType="VARCHAR" property="createMobile" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="co_order_id" jdbcType="INTEGER" property="coOrderId" />
    <result column="order_num" jdbcType="VARCHAR" property="orderNum" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="file_url" jdbcType="VARCHAR" property="fileUrl" />
    <result column="file_type" jdbcType="INTEGER" property="fileType" />
    <result column="valid" jdbcType="TINYINT" property="valid" />
  </resultMap>
  <sql id="Base_Column_List">
    order_info_file_id, create_id, create_name, create_mobile, create_time, update_time, 
    co_order_id, order_num, file_name, file_url, file_type, valid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from co_order_info_file
    where order_info_file_id = #{orderInfoFileId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from co_order_info_file
    where order_info_file_id = #{orderInfoFileId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.CoOrderInfoFile">
    <selectKey keyProperty="orderInfoFileId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into co_order_info_file (create_id, create_name, create_mobile, 
      create_time, update_time, co_order_id, 
      order_num, file_name, file_url, 
      file_type, valid)
    values (#{createId,jdbcType=INTEGER}, #{createName,jdbcType=VARCHAR}, #{createMobile,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{coOrderId,jdbcType=INTEGER}, 
      #{orderNum,jdbcType=VARCHAR}, #{fileName,jdbcType=VARCHAR}, #{fileUrl,jdbcType=VARCHAR}, 
      #{fileType,jdbcType=INTEGER}, #{valid,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.CoOrderInfoFile">
    <selectKey keyProperty="orderInfoFileId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into co_order_info_file
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createId != null">
        create_id,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createMobile != null">
        create_mobile,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="coOrderId != null">
        co_order_id,
      </if>
      <if test="orderNum != null">
        order_num,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="fileUrl != null">
        file_url,
      </if>
      <if test="fileType != null">
        file_type,
      </if>
      <if test="valid != null">
        valid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createId != null">
        #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createMobile != null">
        #{createMobile,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="coOrderId != null">
        #{coOrderId,jdbcType=INTEGER},
      </if>
      <if test="orderNum != null">
        #{orderNum,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileUrl != null">
        #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="fileType != null">
        #{fileType,jdbcType=INTEGER},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.CoOrderInfoFile">
    update co_order_info_file
    <set>
      <if test="createId != null">
        create_id = #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createMobile != null">
        create_mobile = #{createMobile,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="coOrderId != null">
        co_order_id = #{coOrderId,jdbcType=INTEGER},
      </if>
      <if test="orderNum != null">
        order_num = #{orderNum,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileUrl != null">
        file_url = #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="fileType != null">
        file_type = #{fileType,jdbcType=INTEGER},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=TINYINT},
      </if>
    </set>
    where order_info_file_id = #{orderInfoFileId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.CoOrderInfoFile">
    update co_order_info_file
    set create_id = #{createId,jdbcType=INTEGER},
      create_name = #{createName,jdbcType=VARCHAR},
      create_mobile = #{createMobile,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      co_order_id = #{coOrderId,jdbcType=INTEGER},
      order_num = #{orderNum,jdbcType=VARCHAR},
      file_name = #{fileName,jdbcType=VARCHAR},
      file_url = #{fileUrl,jdbcType=VARCHAR},
      file_type = #{fileType,jdbcType=INTEGER},
      valid = #{valid,jdbcType=TINYINT}
    where order_info_file_id = #{orderInfoFileId,jdbcType=INTEGER}
  </update>
</mapper>