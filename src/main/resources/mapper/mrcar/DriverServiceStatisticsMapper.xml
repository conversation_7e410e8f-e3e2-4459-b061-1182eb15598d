<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.DriverServiceStatisticsMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.DriverServiceStatistics">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="driver_id" jdbcType="INTEGER" property="driverId" />
    <result column="driver_name" jdbcType="VARCHAR" property="driverName" />
    <result column="driver_mobile" jdbcType="VARCHAR" property="driverMobile" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="total_time" jdbcType="DECIMAL" property="totalTime" />
    <result column="total_count" jdbcType="INTEGER" property="totalCount" />
    <result column="total_comment" jdbcType="DECIMAL" property="totalComment" />
    <result column="date_type" jdbcType="TINYINT" property="dateType" />
    <result column="year_date" jdbcType="VARCHAR" property="yearDate" />
    <result column="year_month_date" jdbcType="VARCHAR" property="yearMonthDate" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, driver_id, driver_name, driver_mobile, total_amount, total_time, total_count, 
    total_comment, date_type, year_date, year_month_date, create_date, update_date
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from driver_service_statistics
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from driver_service_statistics
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.DriverServiceStatistics">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into driver_service_statistics (driver_id, driver_name, driver_mobile, 
      total_amount, total_time, total_count, 
      total_comment, date_type, year_date, 
      year_month_date, create_date, update_date
      )
    values (#{driverId,jdbcType=INTEGER}, #{driverName,jdbcType=VARCHAR}, #{driverMobile,jdbcType=VARCHAR}, 
      #{totalAmount,jdbcType=DECIMAL}, #{totalTime,jdbcType=DECIMAL}, #{totalCount,jdbcType=INTEGER}, 
      #{totalComment,jdbcType=DECIMAL}, #{dateType,jdbcType=TINYINT}, #{yearDate,jdbcType=VARCHAR}, 
      #{yearMonthDate,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP}, #{updateDate,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.DriverServiceStatistics">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into driver_service_statistics
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="driverId != null">
        driver_id,
      </if>
      <if test="driverName != null">
        driver_name,
      </if>
      <if test="driverMobile != null">
        driver_mobile,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="totalTime != null">
        total_time,
      </if>
      <if test="totalCount != null">
        total_count,
      </if>
      <if test="totalComment != null">
        total_comment,
      </if>
      <if test="dateType != null">
        date_type,
      </if>
      <if test="yearDate != null">
        year_date,
      </if>
      <if test="yearMonthDate != null">
        year_month_date,
      </if>
      <if test="createDate != null">
        create_date,
      </if>
      <if test="updateDate != null">
        update_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="driverId != null">
        #{driverId,jdbcType=INTEGER},
      </if>
      <if test="driverName != null">
        #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="driverMobile != null">
        #{driverMobile,jdbcType=VARCHAR},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalTime != null">
        #{totalTime,jdbcType=DECIMAL},
      </if>
      <if test="totalCount != null">
        #{totalCount,jdbcType=INTEGER},
      </if>
      <if test="totalComment != null">
        #{totalComment,jdbcType=DECIMAL},
      </if>
      <if test="dateType != null">
        #{dateType,jdbcType=TINYINT},
      </if>
      <if test="yearDate != null">
        #{yearDate,jdbcType=VARCHAR},
      </if>
      <if test="yearMonthDate != null">
        #{yearMonthDate,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDate != null">
        #{updateDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.DriverServiceStatistics">
    update driver_service_statistics
    <set>
      <if test="driverId != null">
        driver_id = #{driverId,jdbcType=INTEGER},
      </if>
      <if test="driverName != null">
        driver_name = #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="driverMobile != null">
        driver_mobile = #{driverMobile,jdbcType=VARCHAR},
      </if>
      <if test="totalAmount != null">
        total_amount = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalTime != null">
        total_time = #{totalTime,jdbcType=DECIMAL},
      </if>
      <if test="totalCount != null">
        total_count = #{totalCount,jdbcType=INTEGER},
      </if>
      <if test="totalComment != null">
        total_comment = #{totalComment,jdbcType=DECIMAL},
      </if>
      <if test="dateType != null">
        date_type = #{dateType,jdbcType=TINYINT},
      </if>
      <if test="yearDate != null">
        year_date = #{yearDate,jdbcType=VARCHAR},
      </if>
      <if test="yearMonthDate != null">
        year_month_date = #{yearMonthDate,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDate != null">
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.DriverServiceStatistics">
    update driver_service_statistics
    set driver_id = #{driverId,jdbcType=INTEGER},
      driver_name = #{driverName,jdbcType=VARCHAR},
      driver_mobile = #{driverMobile,jdbcType=VARCHAR},
      total_amount = #{totalAmount,jdbcType=DECIMAL},
      total_time = #{totalTime,jdbcType=DECIMAL},
      total_count = #{totalCount,jdbcType=INTEGER},
      total_comment = #{totalComment,jdbcType=DECIMAL},
      date_type = #{dateType,jdbcType=TINYINT},
      year_date = #{yearDate,jdbcType=VARCHAR},
      year_month_date = #{yearMonthDate,jdbcType=VARCHAR},
      create_date = #{createDate,jdbcType=TIMESTAMP},
      update_date = #{updateDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>