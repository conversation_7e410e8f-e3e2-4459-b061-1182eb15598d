<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.GovPublicCarOrderVehicleInfoMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.GovPublicCarOrderVehicleInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="vehicle_id" jdbcType="INTEGER" property="vehicleId" />
    <result column="vehicle_license" jdbcType="VARCHAR" property="vehicleLicense" />
    <result column="vehicle_vin" jdbcType="VARCHAR" property="vehicleVin" />
    <result column="deployment_mode" jdbcType="TINYINT" property="deploymentMode" />
    <result column="no_key_use_car" jdbcType="TINYINT" property="noKeyUseCar" />
    <result column="vehicle_brand_code" jdbcType="VARCHAR" property="vehicleBrandCode" />
    <result column="vehicle_brand_name" jdbcType="VARCHAR" property="vehicleBrandName" />
    <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode" />
    <result column="vehicle_model_name" jdbcType="VARCHAR" property="vehicleModelName" />
    <result column="vehicle_pic_url" jdbcType="VARCHAR" property="vehiclePicUrl" />
    <result column="vehicle_struct_id" jdbcType="INTEGER" property="vehicleStructId" />
    <result column="vehicle_struct_code" jdbcType="VARCHAR" property="vehicleStructCode" />
    <result column="vehicle_struct_name" jdbcType="VARCHAR" property="vehicleStructName" />
    <result column="vehicle_city_code" jdbcType="VARCHAR" property="vehicleCityCode" />
    <result column="vehicle_city_name" jdbcType="VARCHAR" property="vehicleCityName" />
    <result column="operate_buss_code" jdbcType="VARCHAR" property="operateBussCode" />
    <result column="belong_buss_code" jdbcType="VARCHAR" property="belongBussCode" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_no, company_code, vehicle_id, vehicle_license, vehicle_vin, deployment_mode, 
    no_key_use_car, vehicle_brand_code, vehicle_brand_name, vehicle_model_code, vehicle_model_name, 
    vehicle_pic_url, vehicle_struct_id, vehicle_struct_code, vehicle_struct_name, vehicle_city_code, 
    vehicle_city_name, operate_buss_code, belong_buss_code, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.GovPublicCarOrderVehicleInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from gov_public_car_order_vehicle_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from gov_public_car_order_vehicle_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from gov_public_car_order_vehicle_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.order.entity.mrcar.GovPublicCarOrderVehicleInfoExample">
    delete from gov_public_car_order_vehicle_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.GovPublicCarOrderVehicleInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gov_public_car_order_vehicle_info (order_no, company_code, vehicle_id, 
      vehicle_license, vehicle_vin, deployment_mode, 
      no_key_use_car, vehicle_brand_code, vehicle_brand_name, 
      vehicle_model_code, vehicle_model_name, vehicle_pic_url, 
      vehicle_struct_id, vehicle_struct_code, vehicle_struct_name, 
      vehicle_city_code, vehicle_city_name, operate_buss_code, 
      belong_buss_code, create_time, update_time
      )
    values (#{orderNo,jdbcType=VARCHAR}, #{companyCode,jdbcType=VARCHAR}, #{vehicleId,jdbcType=INTEGER}, 
      #{vehicleLicense,jdbcType=VARCHAR}, #{vehicleVin,jdbcType=VARCHAR}, #{deploymentMode,jdbcType=TINYINT}, 
      #{noKeyUseCar,jdbcType=TINYINT}, #{vehicleBrandCode,jdbcType=VARCHAR}, #{vehicleBrandName,jdbcType=VARCHAR}, 
      #{vehicleModelCode,jdbcType=VARCHAR}, #{vehicleModelName,jdbcType=VARCHAR}, #{vehiclePicUrl,jdbcType=VARCHAR}, 
      #{vehicleStructId,jdbcType=INTEGER}, #{vehicleStructCode,jdbcType=VARCHAR}, #{vehicleStructName,jdbcType=VARCHAR}, 
      #{vehicleCityCode,jdbcType=VARCHAR}, #{vehicleCityName,jdbcType=VARCHAR}, #{operateBussCode,jdbcType=VARCHAR}, 
      #{belongBussCode,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.GovPublicCarOrderVehicleInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gov_public_car_order_vehicle_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="vehicleLicense != null">
        vehicle_license,
      </if>
      <if test="vehicleVin != null">
        vehicle_vin,
      </if>
      <if test="deploymentMode != null">
        deployment_mode,
      </if>
      <if test="noKeyUseCar != null">
        no_key_use_car,
      </if>
      <if test="vehicleBrandCode != null">
        vehicle_brand_code,
      </if>
      <if test="vehicleBrandName != null">
        vehicle_brand_name,
      </if>
      <if test="vehicleModelCode != null">
        vehicle_model_code,
      </if>
      <if test="vehicleModelName != null">
        vehicle_model_name,
      </if>
      <if test="vehiclePicUrl != null">
        vehicle_pic_url,
      </if>
      <if test="vehicleStructId != null">
        vehicle_struct_id,
      </if>
      <if test="vehicleStructCode != null">
        vehicle_struct_code,
      </if>
      <if test="vehicleStructName != null">
        vehicle_struct_name,
      </if>
      <if test="vehicleCityCode != null">
        vehicle_city_code,
      </if>
      <if test="vehicleCityName != null">
        vehicle_city_name,
      </if>
      <if test="operateBussCode != null">
        operate_buss_code,
      </if>
      <if test="belongBussCode != null">
        belong_buss_code,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=INTEGER},
      </if>
      <if test="vehicleLicense != null">
        #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="vehicleVin != null">
        #{vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="deploymentMode != null">
        #{deploymentMode,jdbcType=TINYINT},
      </if>
      <if test="noKeyUseCar != null">
        #{noKeyUseCar,jdbcType=TINYINT},
      </if>
      <if test="vehicleBrandCode != null">
        #{vehicleBrandCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBrandName != null">
        #{vehicleBrandName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelCode != null">
        #{vehicleModelCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelName != null">
        #{vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="vehiclePicUrl != null">
        #{vehiclePicUrl,jdbcType=VARCHAR},
      </if>
      <if test="vehicleStructId != null">
        #{vehicleStructId,jdbcType=INTEGER},
      </if>
      <if test="vehicleStructCode != null">
        #{vehicleStructCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleStructName != null">
        #{vehicleStructName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleCityCode != null">
        #{vehicleCityCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleCityName != null">
        #{vehicleCityName,jdbcType=VARCHAR},
      </if>
      <if test="operateBussCode != null">
        #{operateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="belongBussCode != null">
        #{belongBussCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.GovPublicCarOrderVehicleInfoExample" resultType="java.lang.Long">
    select count(*) from gov_public_car_order_vehicle_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update gov_public_car_order_vehicle_info
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.orderNo != null">
        order_no = #{row.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="row.companyCode != null">
        company_code = #{row.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleId != null">
        vehicle_id = #{row.vehicleId,jdbcType=INTEGER},
      </if>
      <if test="row.vehicleLicense != null">
        vehicle_license = #{row.vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleVin != null">
        vehicle_vin = #{row.vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="row.deploymentMode != null">
        deployment_mode = #{row.deploymentMode,jdbcType=TINYINT},
      </if>
      <if test="row.noKeyUseCar != null">
        no_key_use_car = #{row.noKeyUseCar,jdbcType=TINYINT},
      </if>
      <if test="row.vehicleBrandCode != null">
        vehicle_brand_code = #{row.vehicleBrandCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleBrandName != null">
        vehicle_brand_name = #{row.vehicleBrandName,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleModelCode != null">
        vehicle_model_code = #{row.vehicleModelCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleModelName != null">
        vehicle_model_name = #{row.vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="row.vehiclePicUrl != null">
        vehicle_pic_url = #{row.vehiclePicUrl,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleStructId != null">
        vehicle_struct_id = #{row.vehicleStructId,jdbcType=INTEGER},
      </if>
      <if test="row.vehicleStructCode != null">
        vehicle_struct_code = #{row.vehicleStructCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleStructName != null">
        vehicle_struct_name = #{row.vehicleStructName,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleCityCode != null">
        vehicle_city_code = #{row.vehicleCityCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleCityName != null">
        vehicle_city_name = #{row.vehicleCityName,jdbcType=VARCHAR},
      </if>
      <if test="row.operateBussCode != null">
        operate_buss_code = #{row.operateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="row.belongBussCode != null">
        belong_buss_code = #{row.belongBussCode,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update gov_public_car_order_vehicle_info
    set id = #{row.id,jdbcType=BIGINT},
      order_no = #{row.orderNo,jdbcType=VARCHAR},
      company_code = #{row.companyCode,jdbcType=VARCHAR},
      vehicle_id = #{row.vehicleId,jdbcType=INTEGER},
      vehicle_license = #{row.vehicleLicense,jdbcType=VARCHAR},
      vehicle_vin = #{row.vehicleVin,jdbcType=VARCHAR},
      deployment_mode = #{row.deploymentMode,jdbcType=TINYINT},
      no_key_use_car = #{row.noKeyUseCar,jdbcType=TINYINT},
      vehicle_brand_code = #{row.vehicleBrandCode,jdbcType=VARCHAR},
      vehicle_brand_name = #{row.vehicleBrandName,jdbcType=VARCHAR},
      vehicle_model_code = #{row.vehicleModelCode,jdbcType=VARCHAR},
      vehicle_model_name = #{row.vehicleModelName,jdbcType=VARCHAR},
      vehicle_pic_url = #{row.vehiclePicUrl,jdbcType=VARCHAR},
      vehicle_struct_id = #{row.vehicleStructId,jdbcType=INTEGER},
      vehicle_struct_code = #{row.vehicleStructCode,jdbcType=VARCHAR},
      vehicle_struct_name = #{row.vehicleStructName,jdbcType=VARCHAR},
      vehicle_city_code = #{row.vehicleCityCode,jdbcType=VARCHAR},
      vehicle_city_name = #{row.vehicleCityName,jdbcType=VARCHAR},
      operate_buss_code = #{row.operateBussCode,jdbcType=VARCHAR},
      belong_buss_code = #{row.belongBussCode,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.GovPublicCarOrderVehicleInfo">
    update gov_public_car_order_vehicle_info
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=INTEGER},
      </if>
      <if test="vehicleLicense != null">
        vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="vehicleVin != null">
        vehicle_vin = #{vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="deploymentMode != null">
        deployment_mode = #{deploymentMode,jdbcType=TINYINT},
      </if>
      <if test="noKeyUseCar != null">
        no_key_use_car = #{noKeyUseCar,jdbcType=TINYINT},
      </if>
      <if test="vehicleBrandCode != null">
        vehicle_brand_code = #{vehicleBrandCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBrandName != null">
        vehicle_brand_name = #{vehicleBrandName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelCode != null">
        vehicle_model_code = #{vehicleModelCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelName != null">
        vehicle_model_name = #{vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="vehiclePicUrl != null">
        vehicle_pic_url = #{vehiclePicUrl,jdbcType=VARCHAR},
      </if>
      <if test="vehicleStructId != null">
        vehicle_struct_id = #{vehicleStructId,jdbcType=INTEGER},
      </if>
      <if test="vehicleStructCode != null">
        vehicle_struct_code = #{vehicleStructCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleStructName != null">
        vehicle_struct_name = #{vehicleStructName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleCityCode != null">
        vehicle_city_code = #{vehicleCityCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleCityName != null">
        vehicle_city_name = #{vehicleCityName,jdbcType=VARCHAR},
      </if>
      <if test="operateBussCode != null">
        operate_buss_code = #{operateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="belongBussCode != null">
        belong_buss_code = #{belongBussCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.GovPublicCarOrderVehicleInfo">
    update gov_public_car_order_vehicle_info
    set order_no = #{orderNo,jdbcType=VARCHAR},
      company_code = #{companyCode,jdbcType=VARCHAR},
      vehicle_id = #{vehicleId,jdbcType=INTEGER},
      vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      vehicle_vin = #{vehicleVin,jdbcType=VARCHAR},
      deployment_mode = #{deploymentMode,jdbcType=TINYINT},
      no_key_use_car = #{noKeyUseCar,jdbcType=TINYINT},
      vehicle_brand_code = #{vehicleBrandCode,jdbcType=VARCHAR},
      vehicle_brand_name = #{vehicleBrandName,jdbcType=VARCHAR},
      vehicle_model_code = #{vehicleModelCode,jdbcType=VARCHAR},
      vehicle_model_name = #{vehicleModelName,jdbcType=VARCHAR},
      vehicle_pic_url = #{vehiclePicUrl,jdbcType=VARCHAR},
      vehicle_struct_id = #{vehicleStructId,jdbcType=INTEGER},
      vehicle_struct_code = #{vehicleStructCode,jdbcType=VARCHAR},
      vehicle_struct_name = #{vehicleStructName,jdbcType=VARCHAR},
      vehicle_city_code = #{vehicleCityCode,jdbcType=VARCHAR},
      vehicle_city_name = #{vehicleCityName,jdbcType=VARCHAR},
      operate_buss_code = #{operateBussCode,jdbcType=VARCHAR},
      belong_buss_code = #{belongBussCode,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>