<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.SequenceMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.Sequence">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="sequence_date" jdbcType="DATE" property="sequenceDate" />
    <result column="sequence_type" jdbcType="VARCHAR" property="sequenceType" />
    <result column="current_seq_number" jdbcType="INTEGER" property="currentSeqNumber" />
    <result column="update_version" jdbcType="VARCHAR" property="updateVersion" />
  </resultMap>
  <sql id="Base_Column_List">
    id, sequence_date, sequence_type, current_seq_number, update_version
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from sequence
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from sequence
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.Sequence">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sequence (sequence_date, sequence_type, current_seq_number, 
      update_version)
    values (#{sequenceDate,jdbcType=DATE}, #{sequenceType,jdbcType=VARCHAR}, #{currentSeqNumber,jdbcType=INTEGER}, 
      #{updateVersion,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.Sequence">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into sequence
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="sequenceDate != null">
        sequence_date,
      </if>
      <if test="sequenceType != null">
        sequence_type,
      </if>
      <if test="currentSeqNumber != null">
        current_seq_number,
      </if>
      <if test="updateVersion != null">
        update_version,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="sequenceDate != null">
        #{sequenceDate,jdbcType=DATE},
      </if>
      <if test="sequenceType != null">
        #{sequenceType,jdbcType=VARCHAR},
      </if>
      <if test="currentSeqNumber != null">
        #{currentSeqNumber,jdbcType=INTEGER},
      </if>
      <if test="updateVersion != null">
        #{updateVersion,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.Sequence">
    update sequence
    <set>
      <if test="sequenceDate != null">
        sequence_date = #{sequenceDate,jdbcType=DATE},
      </if>
      <if test="sequenceType != null">
        sequence_type = #{sequenceType,jdbcType=VARCHAR},
      </if>
      <if test="currentSeqNumber != null">
        current_seq_number = #{currentSeqNumber,jdbcType=INTEGER},
      </if>
      <if test="updateVersion != null">
        update_version = #{updateVersion,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.Sequence">
    update sequence
    set sequence_date = #{sequenceDate,jdbcType=DATE},
      sequence_type = #{sequenceType,jdbcType=VARCHAR},
      current_seq_number = #{currentSeqNumber,jdbcType=INTEGER},
      update_version = #{updateVersion,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>