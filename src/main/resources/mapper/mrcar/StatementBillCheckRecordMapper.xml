<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.StatementBillCheckRecordMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.StatementBillCheckRecord">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="income_bill_num" jdbcType="VARCHAR" property="incomeBillNum" />
    <result column="expenditure_bill_num" jdbcType="VARCHAR" property="expenditureBillNum" />
    <result column="check_result" jdbcType="INTEGER" property="checkResult" />
    <result column="company_customer_confirm_amount" jdbcType="DECIMAL" property="companyCustomerConfirmAmount" />
    <result column="reject_reason" jdbcType="VARCHAR" property="rejectReason" />
    <result column="file_list" jdbcType="CHAR" property="fileList" />
    <result column="role_type" jdbcType="INTEGER" property="roleType" />
    <result column="create_id" jdbcType="INTEGER" property="createId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, income_bill_num, expenditure_bill_num, check_result, company_customer_confirm_amount, 
    reject_reason, file_list, role_type, create_id, create_name, create_time
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.StatementBillCheckRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from so_statement_bill_check_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from so_statement_bill_check_record
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from so_statement_bill_check_record
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.StatementBillCheckRecord">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into so_statement_bill_check_record (income_bill_num, expenditure_bill_num, 
      check_result, company_customer_confirm_amount, 
      reject_reason, file_list, role_type, 
      create_id, create_name, create_time
      )
    values (#{incomeBillNum,jdbcType=VARCHAR}, #{expenditureBillNum,jdbcType=VARCHAR}, 
      #{checkResult,jdbcType=INTEGER}, #{companyCustomerConfirmAmount,jdbcType=DECIMAL}, 
      #{rejectReason,jdbcType=VARCHAR}, #{fileList,jdbcType=CHAR}, #{roleType,jdbcType=INTEGER}, 
      #{createId,jdbcType=INTEGER}, #{createName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.StatementBillCheckRecord">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into so_statement_bill_check_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="incomeBillNum != null">
        income_bill_num,
      </if>
      <if test="expenditureBillNum != null">
        expenditure_bill_num,
      </if>
      <if test="checkResult != null">
        check_result,
      </if>
      <if test="companyCustomerConfirmAmount != null">
        company_customer_confirm_amount,
      </if>
      <if test="rejectReason != null">
        reject_reason,
      </if>
      <if test="fileList != null">
        file_list,
      </if>
      <if test="roleType != null">
        role_type,
      </if>
      <if test="createId != null">
        create_id,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="incomeBillNum != null">
        #{incomeBillNum,jdbcType=VARCHAR},
      </if>
      <if test="expenditureBillNum != null">
        #{expenditureBillNum,jdbcType=VARCHAR},
      </if>
      <if test="checkResult != null">
        #{checkResult,jdbcType=INTEGER},
      </if>
      <if test="companyCustomerConfirmAmount != null">
        #{companyCustomerConfirmAmount,jdbcType=DECIMAL},
      </if>
      <if test="rejectReason != null">
        #{rejectReason,jdbcType=VARCHAR},
      </if>
      <if test="fileList != null">
        #{fileList,jdbcType=CHAR},
      </if>
      <if test="roleType != null">
        #{roleType,jdbcType=INTEGER},
      </if>
      <if test="createId != null">
        #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.StatementBillCheckRecordExample" resultType="java.lang.Long">
    select count(*) from so_statement_bill_check_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.StatementBillCheckRecord">
    update so_statement_bill_check_record
    <set>
      <if test="incomeBillNum != null">
        income_bill_num = #{incomeBillNum,jdbcType=VARCHAR},
      </if>
      <if test="expenditureBillNum != null">
        expenditure_bill_num = #{expenditureBillNum,jdbcType=VARCHAR},
      </if>
      <if test="checkResult != null">
        check_result = #{checkResult,jdbcType=INTEGER},
      </if>
      <if test="companyCustomerConfirmAmount != null">
        company_customer_confirm_amount = #{companyCustomerConfirmAmount,jdbcType=DECIMAL},
      </if>
      <if test="rejectReason != null">
        reject_reason = #{rejectReason,jdbcType=VARCHAR},
      </if>
      <if test="fileList != null">
        file_list = #{fileList,jdbcType=CHAR},
      </if>
      <if test="roleType != null">
        role_type = #{roleType,jdbcType=INTEGER},
      </if>
      <if test="createId != null">
        create_id = #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.StatementBillCheckRecord">
    update so_statement_bill_check_record
    set income_bill_num = #{incomeBillNum,jdbcType=VARCHAR},
      expenditure_bill_num = #{expenditureBillNum,jdbcType=VARCHAR},
      check_result = #{checkResult,jdbcType=INTEGER},
      company_customer_confirm_amount = #{companyCustomerConfirmAmount,jdbcType=DECIMAL},
      reject_reason = #{rejectReason,jdbcType=VARCHAR},
      file_list = #{fileList,jdbcType=CHAR},
      role_type = #{roleType,jdbcType=INTEGER},
      create_id = #{createId,jdbcType=INTEGER},
      create_name = #{createName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>