<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.BusinessRentalOrderMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.BusinessRentalOrder">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="order_type" jdbcType="TINYINT" property="orderType" />
    <result column="order_status" jdbcType="TINYINT" property="orderStatus" />
    <result column="order_source" jdbcType="TINYINT" property="orderSource" />
    <result column="channel_order_code" jdbcType="VARCHAR" property="channelOrderCode" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="customer_id" jdbcType="INTEGER" property="customerId" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="customer_mobile" jdbcType="VARCHAR" property="customerMobile" />
    <result column="booking_passenger_name" jdbcType="VARCHAR" property="bookingPassengerName" />
    <result column="booking_passenger_phone" jdbcType="VARCHAR" property="bookingPassengerPhone" />
    <result column="booking_start_time" jdbcType="TIMESTAMP" property="bookingStartTime" />
    <result column="booking_end_time" jdbcType="TIMESTAMP" property="bookingEndTime" />
    <result column="booking_start_full_address" jdbcType="VARCHAR" property="bookingStartFullAddress" />
    <result column="booking_start_short_address" jdbcType="VARCHAR" property="bookingStartShortAddress" />
    <result column="booking_start_latitude" jdbcType="DECIMAL" property="bookingStartLatitude" />
    <result column="booking_start_longitude" jdbcType="DECIMAL" property="bookingStartLongitude" />
    <result column="booking_end_full_address" jdbcType="VARCHAR" property="bookingEndFullAddress" />
    <result column="booking_end_short_address" jdbcType="VARCHAR" property="bookingEndShortAddress" />
    <result column="booking_end_latitude" jdbcType="DECIMAL" property="bookingEndLatitude" />
    <result column="booking_end_longitude" jdbcType="DECIMAL" property="bookingEndLongitude" />
    <result column="start_city_code" jdbcType="INTEGER" property="startCityCode" />
    <result column="start_city_name" jdbcType="VARCHAR" property="startCityName" />
    <result column="end_city_code" jdbcType="INTEGER" property="endCityCode" />
    <result column="end_city_name" jdbcType="VARCHAR" property="endCityName" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_no, order_type, order_status, order_source, channel_order_code, create_time, 
    update_time, company_id, customer_id, customer_name, customer_mobile, booking_passenger_name, 
    booking_passenger_phone, booking_start_time, booking_end_time, booking_start_full_address, 
    booking_start_short_address, booking_start_latitude, booking_start_longitude, booking_end_full_address, 
    booking_end_short_address, booking_end_latitude, booking_end_longitude, start_city_code, 
    start_city_name, end_city_code, end_city_name, deleted, remark
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.BusinessRentalOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from business_rental_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from business_rental_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from business_rental_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.order.entity.mrcar.BusinessRentalOrderExample">
    delete from business_rental_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.BusinessRentalOrder">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into business_rental_order (order_no, order_type, order_status, 
      order_source, channel_order_code, create_time, 
      update_time, company_id, customer_id, 
      customer_name, customer_mobile, booking_passenger_name, 
      booking_passenger_phone, booking_start_time, 
      booking_end_time, booking_start_full_address, 
      booking_start_short_address, booking_start_latitude, 
      booking_start_longitude, booking_end_full_address, 
      booking_end_short_address, booking_end_latitude, 
      booking_end_longitude, start_city_code, start_city_name, 
      end_city_code, end_city_name, deleted, 
      remark)
    values (#{orderNo,jdbcType=VARCHAR}, #{orderType,jdbcType=TINYINT}, #{orderStatus,jdbcType=TINYINT}, 
      #{orderSource,jdbcType=TINYINT}, #{channelOrderCode,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{companyId,jdbcType=INTEGER}, #{customerId,jdbcType=INTEGER}, 
      #{customerName,jdbcType=VARCHAR}, #{customerMobile,jdbcType=VARCHAR}, #{bookingPassengerName,jdbcType=VARCHAR}, 
      #{bookingPassengerPhone,jdbcType=VARCHAR}, #{bookingStartTime,jdbcType=TIMESTAMP}, 
      #{bookingEndTime,jdbcType=TIMESTAMP}, #{bookingStartFullAddress,jdbcType=VARCHAR}, 
      #{bookingStartShortAddress,jdbcType=VARCHAR}, #{bookingStartLatitude,jdbcType=DECIMAL}, 
      #{bookingStartLongitude,jdbcType=DECIMAL}, #{bookingEndFullAddress,jdbcType=VARCHAR}, 
      #{bookingEndShortAddress,jdbcType=VARCHAR}, #{bookingEndLatitude,jdbcType=DECIMAL}, 
      #{bookingEndLongitude,jdbcType=DECIMAL}, #{startCityCode,jdbcType=INTEGER}, #{startCityName,jdbcType=VARCHAR}, 
      #{endCityCode,jdbcType=INTEGER}, #{endCityName,jdbcType=VARCHAR}, #{deleted,jdbcType=TINYINT}, 
      #{remark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.BusinessRentalOrder">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into business_rental_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="orderSource != null">
        order_source,
      </if>
      <if test="channelOrderCode != null">
        channel_order_code,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="customerMobile != null">
        customer_mobile,
      </if>
      <if test="bookingPassengerName != null">
        booking_passenger_name,
      </if>
      <if test="bookingPassengerPhone != null">
        booking_passenger_phone,
      </if>
      <if test="bookingStartTime != null">
        booking_start_time,
      </if>
      <if test="bookingEndTime != null">
        booking_end_time,
      </if>
      <if test="bookingStartFullAddress != null">
        booking_start_full_address,
      </if>
      <if test="bookingStartShortAddress != null">
        booking_start_short_address,
      </if>
      <if test="bookingStartLatitude != null">
        booking_start_latitude,
      </if>
      <if test="bookingStartLongitude != null">
        booking_start_longitude,
      </if>
      <if test="bookingEndFullAddress != null">
        booking_end_full_address,
      </if>
      <if test="bookingEndShortAddress != null">
        booking_end_short_address,
      </if>
      <if test="bookingEndLatitude != null">
        booking_end_latitude,
      </if>
      <if test="bookingEndLongitude != null">
        booking_end_longitude,
      </if>
      <if test="startCityCode != null">
        start_city_code,
      </if>
      <if test="startCityName != null">
        start_city_name,
      </if>
      <if test="endCityCode != null">
        end_city_code,
      </if>
      <if test="endCityName != null">
        end_city_name,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="remark != null">
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=TINYINT},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=TINYINT},
      </if>
      <if test="orderSource != null">
        #{orderSource,jdbcType=TINYINT},
      </if>
      <if test="channelOrderCode != null">
        #{channelOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=INTEGER},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerMobile != null">
        #{customerMobile,jdbcType=VARCHAR},
      </if>
      <if test="bookingPassengerName != null">
        #{bookingPassengerName,jdbcType=VARCHAR},
      </if>
      <if test="bookingPassengerPhone != null">
        #{bookingPassengerPhone,jdbcType=VARCHAR},
      </if>
      <if test="bookingStartTime != null">
        #{bookingStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bookingEndTime != null">
        #{bookingEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bookingStartFullAddress != null">
        #{bookingStartFullAddress,jdbcType=VARCHAR},
      </if>
      <if test="bookingStartShortAddress != null">
        #{bookingStartShortAddress,jdbcType=VARCHAR},
      </if>
      <if test="bookingStartLatitude != null">
        #{bookingStartLatitude,jdbcType=DECIMAL},
      </if>
      <if test="bookingStartLongitude != null">
        #{bookingStartLongitude,jdbcType=DECIMAL},
      </if>
      <if test="bookingEndFullAddress != null">
        #{bookingEndFullAddress,jdbcType=VARCHAR},
      </if>
      <if test="bookingEndShortAddress != null">
        #{bookingEndShortAddress,jdbcType=VARCHAR},
      </if>
      <if test="bookingEndLatitude != null">
        #{bookingEndLatitude,jdbcType=DECIMAL},
      </if>
      <if test="bookingEndLongitude != null">
        #{bookingEndLongitude,jdbcType=DECIMAL},
      </if>
      <if test="startCityCode != null">
        #{startCityCode,jdbcType=INTEGER},
      </if>
      <if test="startCityName != null">
        #{startCityName,jdbcType=VARCHAR},
      </if>
      <if test="endCityCode != null">
        #{endCityCode,jdbcType=INTEGER},
      </if>
      <if test="endCityName != null">
        #{endCityName,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.BusinessRentalOrderExample" resultType="java.lang.Long">
    select count(*) from business_rental_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update business_rental_order
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.orderNo != null">
        order_no = #{row.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="row.orderType != null">
        order_type = #{row.orderType,jdbcType=TINYINT},
      </if>
      <if test="row.orderStatus != null">
        order_status = #{row.orderStatus,jdbcType=TINYINT},
      </if>
      <if test="row.orderSource != null">
        order_source = #{row.orderSource,jdbcType=TINYINT},
      </if>
      <if test="row.channelOrderCode != null">
        channel_order_code = #{row.channelOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.companyId != null">
        company_id = #{row.companyId,jdbcType=INTEGER},
      </if>
      <if test="row.customerId != null">
        customer_id = #{row.customerId,jdbcType=INTEGER},
      </if>
      <if test="row.customerName != null">
        customer_name = #{row.customerName,jdbcType=VARCHAR},
      </if>
      <if test="row.customerMobile != null">
        customer_mobile = #{row.customerMobile,jdbcType=VARCHAR},
      </if>
      <if test="row.bookingPassengerName != null">
        booking_passenger_name = #{row.bookingPassengerName,jdbcType=VARCHAR},
      </if>
      <if test="row.bookingPassengerPhone != null">
        booking_passenger_phone = #{row.bookingPassengerPhone,jdbcType=VARCHAR},
      </if>
      <if test="row.bookingStartTime != null">
        booking_start_time = #{row.bookingStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.bookingEndTime != null">
        booking_end_time = #{row.bookingEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.bookingStartFullAddress != null">
        booking_start_full_address = #{row.bookingStartFullAddress,jdbcType=VARCHAR},
      </if>
      <if test="row.bookingStartShortAddress != null">
        booking_start_short_address = #{row.bookingStartShortAddress,jdbcType=VARCHAR},
      </if>
      <if test="row.bookingStartLatitude != null">
        booking_start_latitude = #{row.bookingStartLatitude,jdbcType=DECIMAL},
      </if>
      <if test="row.bookingStartLongitude != null">
        booking_start_longitude = #{row.bookingStartLongitude,jdbcType=DECIMAL},
      </if>
      <if test="row.bookingEndFullAddress != null">
        booking_end_full_address = #{row.bookingEndFullAddress,jdbcType=VARCHAR},
      </if>
      <if test="row.bookingEndShortAddress != null">
        booking_end_short_address = #{row.bookingEndShortAddress,jdbcType=VARCHAR},
      </if>
      <if test="row.bookingEndLatitude != null">
        booking_end_latitude = #{row.bookingEndLatitude,jdbcType=DECIMAL},
      </if>
      <if test="row.bookingEndLongitude != null">
        booking_end_longitude = #{row.bookingEndLongitude,jdbcType=DECIMAL},
      </if>
      <if test="row.startCityCode != null">
        start_city_code = #{row.startCityCode,jdbcType=INTEGER},
      </if>
      <if test="row.startCityName != null">
        start_city_name = #{row.startCityName,jdbcType=VARCHAR},
      </if>
      <if test="row.endCityCode != null">
        end_city_code = #{row.endCityCode,jdbcType=INTEGER},
      </if>
      <if test="row.endCityName != null">
        end_city_name = #{row.endCityName,jdbcType=VARCHAR},
      </if>
      <if test="row.deleted != null">
        deleted = #{row.deleted,jdbcType=TINYINT},
      </if>
      <if test="row.remark != null">
        remark = #{row.remark,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update business_rental_order
    set id = #{row.id,jdbcType=BIGINT},
      order_no = #{row.orderNo,jdbcType=VARCHAR},
      order_type = #{row.orderType,jdbcType=TINYINT},
      order_status = #{row.orderStatus,jdbcType=TINYINT},
      order_source = #{row.orderSource,jdbcType=TINYINT},
      channel_order_code = #{row.channelOrderCode,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      company_id = #{row.companyId,jdbcType=INTEGER},
      customer_id = #{row.customerId,jdbcType=INTEGER},
      customer_name = #{row.customerName,jdbcType=VARCHAR},
      customer_mobile = #{row.customerMobile,jdbcType=VARCHAR},
      booking_passenger_name = #{row.bookingPassengerName,jdbcType=VARCHAR},
      booking_passenger_phone = #{row.bookingPassengerPhone,jdbcType=VARCHAR},
      booking_start_time = #{row.bookingStartTime,jdbcType=TIMESTAMP},
      booking_end_time = #{row.bookingEndTime,jdbcType=TIMESTAMP},
      booking_start_full_address = #{row.bookingStartFullAddress,jdbcType=VARCHAR},
      booking_start_short_address = #{row.bookingStartShortAddress,jdbcType=VARCHAR},
      booking_start_latitude = #{row.bookingStartLatitude,jdbcType=DECIMAL},
      booking_start_longitude = #{row.bookingStartLongitude,jdbcType=DECIMAL},
      booking_end_full_address = #{row.bookingEndFullAddress,jdbcType=VARCHAR},
      booking_end_short_address = #{row.bookingEndShortAddress,jdbcType=VARCHAR},
      booking_end_latitude = #{row.bookingEndLatitude,jdbcType=DECIMAL},
      booking_end_longitude = #{row.bookingEndLongitude,jdbcType=DECIMAL},
      start_city_code = #{row.startCityCode,jdbcType=INTEGER},
      start_city_name = #{row.startCityName,jdbcType=VARCHAR},
      end_city_code = #{row.endCityCode,jdbcType=INTEGER},
      end_city_name = #{row.endCityName,jdbcType=VARCHAR},
      deleted = #{row.deleted,jdbcType=TINYINT},
      remark = #{row.remark,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.BusinessRentalOrder">
    update business_rental_order
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=TINYINT},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=TINYINT},
      </if>
      <if test="orderSource != null">
        order_source = #{orderSource,jdbcType=TINYINT},
      </if>
      <if test="channelOrderCode != null">
        channel_order_code = #{channelOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=INTEGER},
      </if>
      <if test="customerName != null">
        customer_name = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerMobile != null">
        customer_mobile = #{customerMobile,jdbcType=VARCHAR},
      </if>
      <if test="bookingPassengerName != null">
        booking_passenger_name = #{bookingPassengerName,jdbcType=VARCHAR},
      </if>
      <if test="bookingPassengerPhone != null">
        booking_passenger_phone = #{bookingPassengerPhone,jdbcType=VARCHAR},
      </if>
      <if test="bookingStartTime != null">
        booking_start_time = #{bookingStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bookingEndTime != null">
        booking_end_time = #{bookingEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="bookingStartFullAddress != null">
        booking_start_full_address = #{bookingStartFullAddress,jdbcType=VARCHAR},
      </if>
      <if test="bookingStartShortAddress != null">
        booking_start_short_address = #{bookingStartShortAddress,jdbcType=VARCHAR},
      </if>
      <if test="bookingStartLatitude != null">
        booking_start_latitude = #{bookingStartLatitude,jdbcType=DECIMAL},
      </if>
      <if test="bookingStartLongitude != null">
        booking_start_longitude = #{bookingStartLongitude,jdbcType=DECIMAL},
      </if>
      <if test="bookingEndFullAddress != null">
        booking_end_full_address = #{bookingEndFullAddress,jdbcType=VARCHAR},
      </if>
      <if test="bookingEndShortAddress != null">
        booking_end_short_address = #{bookingEndShortAddress,jdbcType=VARCHAR},
      </if>
      <if test="bookingEndLatitude != null">
        booking_end_latitude = #{bookingEndLatitude,jdbcType=DECIMAL},
      </if>
      <if test="bookingEndLongitude != null">
        booking_end_longitude = #{bookingEndLongitude,jdbcType=DECIMAL},
      </if>
      <if test="startCityCode != null">
        start_city_code = #{startCityCode,jdbcType=INTEGER},
      </if>
      <if test="startCityName != null">
        start_city_name = #{startCityName,jdbcType=VARCHAR},
      </if>
      <if test="endCityCode != null">
        end_city_code = #{endCityCode,jdbcType=INTEGER},
      </if>
      <if test="endCityName != null">
        end_city_name = #{endCityName,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.BusinessRentalOrder">
    update business_rental_order
    set order_no = #{orderNo,jdbcType=VARCHAR},
      order_type = #{orderType,jdbcType=TINYINT},
      order_status = #{orderStatus,jdbcType=TINYINT},
      order_source = #{orderSource,jdbcType=TINYINT},
      channel_order_code = #{channelOrderCode,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      company_id = #{companyId,jdbcType=INTEGER},
      customer_id = #{customerId,jdbcType=INTEGER},
      customer_name = #{customerName,jdbcType=VARCHAR},
      customer_mobile = #{customerMobile,jdbcType=VARCHAR},
      booking_passenger_name = #{bookingPassengerName,jdbcType=VARCHAR},
      booking_passenger_phone = #{bookingPassengerPhone,jdbcType=VARCHAR},
      booking_start_time = #{bookingStartTime,jdbcType=TIMESTAMP},
      booking_end_time = #{bookingEndTime,jdbcType=TIMESTAMP},
      booking_start_full_address = #{bookingStartFullAddress,jdbcType=VARCHAR},
      booking_start_short_address = #{bookingStartShortAddress,jdbcType=VARCHAR},
      booking_start_latitude = #{bookingStartLatitude,jdbcType=DECIMAL},
      booking_start_longitude = #{bookingStartLongitude,jdbcType=DECIMAL},
      booking_end_full_address = #{bookingEndFullAddress,jdbcType=VARCHAR},
      booking_end_short_address = #{bookingEndShortAddress,jdbcType=VARCHAR},
      booking_end_latitude = #{bookingEndLatitude,jdbcType=DECIMAL},
      booking_end_longitude = #{bookingEndLongitude,jdbcType=DECIMAL},
      start_city_code = #{startCityCode,jdbcType=INTEGER},
      start_city_name = #{startCityName,jdbcType=VARCHAR},
      end_city_code = #{endCityCode,jdbcType=INTEGER},
      end_city_name = #{endCityName,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=TINYINT},
      remark = #{remark,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>