<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.OrderApprovalInfoMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.OrderApprovalInfo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="order_apply_no" jdbcType="VARCHAR" property="orderApplyNo" />
    <result column="approval_status" jdbcType="TINYINT" property="approvalStatus" />
    <result column="approval_user_id" jdbcType="BIGINT" property="approvalUserId" />
    <result column="approval_user_name" jdbcType="VARCHAR" property="approvalUserName" />
    <result column="approval_reason_text" jdbcType="VARCHAR" property="approvalReasonText" />
    <result column="approval_struct_id" jdbcType="INTEGER" property="approvalStructId" />
    <result column="approval_struct_name" jdbcType="VARCHAR" property="approvalStructName" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, order_apply_no, approval_status, approval_user_id, 
    approval_user_name, approval_reason_text, approval_struct_id, approval_struct_name
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_approval_info
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from order_approval_info
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.OrderApprovalInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_approval_info (create_time, update_time, order_apply_no, 
      approval_status, approval_user_id, approval_user_name, 
      approval_reason_text, approval_struct_id, approval_struct_name
      )
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{orderApplyNo,jdbcType=VARCHAR}, 
      #{approvalStatus,jdbcType=TINYINT}, #{approvalUserId,jdbcType=BIGINT}, #{approvalUserName,jdbcType=VARCHAR}, 
      #{approvalReasonText,jdbcType=VARCHAR}, #{approvalStructId,jdbcType=INTEGER}, #{approvalStructName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.OrderApprovalInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_approval_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="orderApplyNo != null">
        order_apply_no,
      </if>
      <if test="approvalStatus != null">
        approval_status,
      </if>
      <if test="approvalUserId != null">
        approval_user_id,
      </if>
      <if test="approvalUserName != null">
        approval_user_name,
      </if>
      <if test="approvalReasonText != null">
        approval_reason_text,
      </if>
      <if test="approvalStructId != null">
        approval_struct_id,
      </if>
      <if test="approvalStructName != null">
        approval_struct_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderApplyNo != null">
        #{orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="approvalStatus != null">
        #{approvalStatus,jdbcType=TINYINT},
      </if>
      <if test="approvalUserId != null">
        #{approvalUserId,jdbcType=BIGINT},
      </if>
      <if test="approvalUserName != null">
        #{approvalUserName,jdbcType=VARCHAR},
      </if>
      <if test="approvalReasonText != null">
        #{approvalReasonText,jdbcType=VARCHAR},
      </if>
      <if test="approvalStructId != null">
        #{approvalStructId,jdbcType=INTEGER},
      </if>
      <if test="approvalStructName != null">
        #{approvalStructName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.OrderApprovalInfo">
    update order_approval_info
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderApplyNo != null">
        order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="approvalStatus != null">
        approval_status = #{approvalStatus,jdbcType=TINYINT},
      </if>
      <if test="approvalUserId != null">
        approval_user_id = #{approvalUserId,jdbcType=BIGINT},
      </if>
      <if test="approvalUserName != null">
        approval_user_name = #{approvalUserName,jdbcType=VARCHAR},
      </if>
      <if test="approvalReasonText != null">
        approval_reason_text = #{approvalReasonText,jdbcType=VARCHAR},
      </if>
      <if test="approvalStructId != null">
        approval_struct_id = #{approvalStructId,jdbcType=INTEGER},
      </if>
      <if test="approvalStructName != null">
        approval_struct_name = #{approvalStructName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.OrderApprovalInfo">
    update order_approval_info
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      approval_status = #{approvalStatus,jdbcType=TINYINT},
      approval_user_id = #{approvalUserId,jdbcType=BIGINT},
      approval_user_name = #{approvalUserName,jdbcType=VARCHAR},
      approval_reason_text = #{approvalReasonText,jdbcType=VARCHAR},
      approval_struct_id = #{approvalStructId,jdbcType=INTEGER},
      approval_struct_name = #{approvalStructName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>