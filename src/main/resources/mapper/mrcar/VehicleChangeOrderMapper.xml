<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.VehicleChangeOrderMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.VehicleChangeOrder">
    <id column="vehicle_change_order_id" jdbcType="INTEGER" property="vehicleChangeOrderId" />
    <result column="vehicle_change_order_num" jdbcType="VARCHAR" property="vehicleChangeOrderNum" />
    <result column="demand_order_num" jdbcType="VARCHAR" property="demandOrderNum" />
    <result column="customer_order_num" jdbcType="VARCHAR" property="customerOrderNum" />
    <result column="previous_change_order_num" jdbcType="VARCHAR" property="previousChangeOrderNum" />
    <result column="supplier_order_num" jdbcType="VARCHAR" property="supplierOrderNum" />
    <result column="expect_use_time" jdbcType="TIMESTAMP" property="expectUseTime" />
    <result column="cancel_time" jdbcType="TIMESTAMP" property="cancelTime" />
    <result column="confirm_delivery_time" jdbcType="TIMESTAMP" property="confirmDeliveryTime" />
    <result column="completion_time" jdbcType="TIMESTAMP" property="completionTime" />
    <result column="change_vehicle_demand_type" jdbcType="INTEGER" property="changeVehicleDemandType" />
    <result column="replacement_instructions" jdbcType="VARCHAR" property="replacementInstructions" />
    <result column="vehicle_change_order_status" jdbcType="INTEGER" property="vehicleChangeOrderStatus" />
    <result column="level_id" jdbcType="INTEGER" property="levelId" />
    <result column="level_name" jdbcType="VARCHAR" property="levelName" />
    <result column="vehicle_price_range" jdbcType="VARCHAR" property="vehiclePriceRange" />
    <result column="vehicle_license" jdbcType="VARCHAR" property="vehicleLicense" />
    <result column="vehicle_vin" jdbcType="VARCHAR" property="vehicleVin" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="vehicle_supplier_name" jdbcType="VARCHAR" property="vehicleSupplierName" />
    <result column="vehicle_supplier_code" jdbcType="VARCHAR" property="vehicleSupplierCode" />
    <result column="vehicle_supplier_type" jdbcType="INTEGER" property="vehicleSupplierType" />
    <result column="driver_id" jdbcType="INTEGER" property="driverId" />
    <result column="driver_name" jdbcType="VARCHAR" property="driverName" />
    <result column="driver_mobile" jdbcType="VARCHAR" property="driverMobile" />
    <result column="driver_supplier_name" jdbcType="VARCHAR" property="driverSupplierName" />
    <result column="driver_supplier_code" jdbcType="VARCHAR" property="driverSupplierCode" />
    <result column="driver_supplier_type" jdbcType="INTEGER" property="driverSupplierType" />
    <result column="order_owner_dept_code" jdbcType="VARCHAR" property="orderOwnerDeptCode" />
    <result column="order_owner_dept_name" jdbcType="VARCHAR" property="orderOwnerDeptName" />
    <result column="order_owner_company_name" jdbcType="VARCHAR" property="orderOwnerCompanyName" />
    <result column="order_owner_company_id" jdbcType="INTEGER" property="orderOwnerCompanyId" />
    <result column="create_id" jdbcType="INTEGER" property="createId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_id" jdbcType="INTEGER" property="updateId" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    vehicle_change_order_id, vehicle_change_order_num, demand_order_num, customer_order_num, 
    previous_change_order_num, supplier_order_num, expect_use_time, cancel_time, confirm_delivery_time, 
    completion_time, change_vehicle_demand_type, replacement_instructions, vehicle_change_order_status, 
    level_id, level_name, vehicle_price_range, vehicle_license, vehicle_vin, vehicle_id, 
    vehicle_supplier_name, vehicle_supplier_code, vehicle_supplier_type, driver_id, driver_name, 
    driver_mobile, driver_supplier_name, driver_supplier_code, driver_supplier_type, 
    order_owner_dept_code, order_owner_dept_name, order_owner_company_name, order_owner_company_id, 
    create_id, create_name, create_time, update_id, update_name, update_time
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.VehicleChangeOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from so_vehicle_change_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from so_vehicle_change_order
    where vehicle_change_order_id = #{vehicleChangeOrderId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from so_vehicle_change_order
    where vehicle_change_order_id = #{vehicleChangeOrderId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.VehicleChangeOrder">
    <selectKey keyProperty="vehicleChangeOrderId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into so_vehicle_change_order (vehicle_change_order_num, demand_order_num, 
      customer_order_num, previous_change_order_num, 
      supplier_order_num, expect_use_time, cancel_time, 
      confirm_delivery_time, completion_time, 
      change_vehicle_demand_type, replacement_instructions, 
      vehicle_change_order_status, level_id, level_name, 
      vehicle_price_range, vehicle_license, vehicle_vin, 
      vehicle_id, vehicle_supplier_name, vehicle_supplier_code, 
      vehicle_supplier_type, driver_id, driver_name, 
      driver_mobile, driver_supplier_name, driver_supplier_code, 
      driver_supplier_type, order_owner_dept_code, 
      order_owner_dept_name, order_owner_company_name, 
      order_owner_company_id, create_id, create_name, 
      create_time, update_id, update_name, 
      update_time)
    values (#{vehicleChangeOrderNum,jdbcType=VARCHAR}, #{demandOrderNum,jdbcType=VARCHAR}, 
      #{customerOrderNum,jdbcType=VARCHAR}, #{previousChangeOrderNum,jdbcType=VARCHAR}, 
      #{supplierOrderNum,jdbcType=VARCHAR}, #{expectUseTime,jdbcType=TIMESTAMP}, #{cancelTime,jdbcType=TIMESTAMP}, 
      #{confirmDeliveryTime,jdbcType=TIMESTAMP}, #{completionTime,jdbcType=TIMESTAMP}, 
      #{changeVehicleDemandType,jdbcType=INTEGER}, #{replacementInstructions,jdbcType=VARCHAR}, 
      #{vehicleChangeOrderStatus,jdbcType=INTEGER}, #{levelId,jdbcType=INTEGER}, #{levelName,jdbcType=VARCHAR}, 
      #{vehiclePriceRange,jdbcType=VARCHAR}, #{vehicleLicense,jdbcType=VARCHAR}, #{vehicleVin,jdbcType=VARCHAR}, 
      #{vehicleId,jdbcType=BIGINT}, #{vehicleSupplierName,jdbcType=VARCHAR}, #{vehicleSupplierCode,jdbcType=VARCHAR}, 
      #{vehicleSupplierType,jdbcType=INTEGER}, #{driverId,jdbcType=INTEGER}, #{driverName,jdbcType=VARCHAR}, 
      #{driverMobile,jdbcType=VARCHAR}, #{driverSupplierName,jdbcType=VARCHAR}, #{driverSupplierCode,jdbcType=VARCHAR}, 
      #{driverSupplierType,jdbcType=INTEGER}, #{orderOwnerDeptCode,jdbcType=VARCHAR}, 
      #{orderOwnerDeptName,jdbcType=VARCHAR}, #{orderOwnerCompanyName,jdbcType=VARCHAR}, 
      #{orderOwnerCompanyId,jdbcType=INTEGER}, #{createId,jdbcType=INTEGER}, #{createName,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateId,jdbcType=INTEGER}, #{updateName,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.VehicleChangeOrder">
    <selectKey keyProperty="vehicleChangeOrderId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into so_vehicle_change_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="vehicleChangeOrderNum != null">
        vehicle_change_order_num,
      </if>
      <if test="demandOrderNum != null">
        demand_order_num,
      </if>
      <if test="customerOrderNum != null">
        customer_order_num,
      </if>
      <if test="previousChangeOrderNum != null">
        previous_change_order_num,
      </if>
      <if test="supplierOrderNum != null">
        supplier_order_num,
      </if>
      <if test="expectUseTime != null">
        expect_use_time,
      </if>
      <if test="cancelTime != null">
        cancel_time,
      </if>
      <if test="confirmDeliveryTime != null">
        confirm_delivery_time,
      </if>
      <if test="completionTime != null">
        completion_time,
      </if>
      <if test="changeVehicleDemandType != null">
        change_vehicle_demand_type,
      </if>
      <if test="replacementInstructions != null">
        replacement_instructions,
      </if>
      <if test="vehicleChangeOrderStatus != null">
        vehicle_change_order_status,
      </if>
      <if test="levelId != null">
        level_id,
      </if>
      <if test="levelName != null">
        level_name,
      </if>
      <if test="vehiclePriceRange != null">
        vehicle_price_range,
      </if>
      <if test="vehicleLicense != null">
        vehicle_license,
      </if>
      <if test="vehicleVin != null">
        vehicle_vin,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="vehicleSupplierName != null">
        vehicle_supplier_name,
      </if>
      <if test="vehicleSupplierCode != null">
        vehicle_supplier_code,
      </if>
      <if test="vehicleSupplierType != null">
        vehicle_supplier_type,
      </if>
      <if test="driverId != null">
        driver_id,
      </if>
      <if test="driverName != null">
        driver_name,
      </if>
      <if test="driverMobile != null">
        driver_mobile,
      </if>
      <if test="driverSupplierName != null">
        driver_supplier_name,
      </if>
      <if test="driverSupplierCode != null">
        driver_supplier_code,
      </if>
      <if test="driverSupplierType != null">
        driver_supplier_type,
      </if>
      <if test="orderOwnerDeptCode != null">
        order_owner_dept_code,
      </if>
      <if test="orderOwnerDeptName != null">
        order_owner_dept_name,
      </if>
      <if test="orderOwnerCompanyName != null">
        order_owner_company_name,
      </if>
      <if test="orderOwnerCompanyId != null">
        order_owner_company_id,
      </if>
      <if test="createId != null">
        create_id,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateId != null">
        update_id,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="vehicleChangeOrderNum != null">
        #{vehicleChangeOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="demandOrderNum != null">
        #{demandOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="customerOrderNum != null">
        #{customerOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="previousChangeOrderNum != null">
        #{previousChangeOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="supplierOrderNum != null">
        #{supplierOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="expectUseTime != null">
        #{expectUseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cancelTime != null">
        #{cancelTime,jdbcType=TIMESTAMP},
      </if>
      <if test="confirmDeliveryTime != null">
        #{confirmDeliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="completionTime != null">
        #{completionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="changeVehicleDemandType != null">
        #{changeVehicleDemandType,jdbcType=INTEGER},
      </if>
      <if test="replacementInstructions != null">
        #{replacementInstructions,jdbcType=VARCHAR},
      </if>
      <if test="vehicleChangeOrderStatus != null">
        #{vehicleChangeOrderStatus,jdbcType=INTEGER},
      </if>
      <if test="levelId != null">
        #{levelId,jdbcType=INTEGER},
      </if>
      <if test="levelName != null">
        #{levelName,jdbcType=VARCHAR},
      </if>
      <if test="vehiclePriceRange != null">
        #{vehiclePriceRange,jdbcType=VARCHAR},
      </if>
      <if test="vehicleLicense != null">
        #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="vehicleVin != null">
        #{vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleSupplierName != null">
        #{vehicleSupplierName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleSupplierCode != null">
        #{vehicleSupplierCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleSupplierType != null">
        #{vehicleSupplierType,jdbcType=INTEGER},
      </if>
      <if test="driverId != null">
        #{driverId,jdbcType=INTEGER},
      </if>
      <if test="driverName != null">
        #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="driverMobile != null">
        #{driverMobile,jdbcType=VARCHAR},
      </if>
      <if test="driverSupplierName != null">
        #{driverSupplierName,jdbcType=VARCHAR},
      </if>
      <if test="driverSupplierCode != null">
        #{driverSupplierCode,jdbcType=VARCHAR},
      </if>
      <if test="driverSupplierType != null">
        #{driverSupplierType,jdbcType=INTEGER},
      </if>
      <if test="orderOwnerDeptCode != null">
        #{orderOwnerDeptCode,jdbcType=VARCHAR},
      </if>
      <if test="orderOwnerDeptName != null">
        #{orderOwnerDeptName,jdbcType=VARCHAR},
      </if>
      <if test="orderOwnerCompanyName != null">
        #{orderOwnerCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="orderOwnerCompanyId != null">
        #{orderOwnerCompanyId,jdbcType=INTEGER},
      </if>
      <if test="createId != null">
        #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.VehicleChangeOrderExample" resultType="java.lang.Long">
    select count(*) from so_vehicle_change_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.VehicleChangeOrder">
    update so_vehicle_change_order
    <set>
      <if test="vehicleChangeOrderNum != null">
        vehicle_change_order_num = #{vehicleChangeOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="demandOrderNum != null">
        demand_order_num = #{demandOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="customerOrderNum != null">
        customer_order_num = #{customerOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="previousChangeOrderNum != null">
        previous_change_order_num = #{previousChangeOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="supplierOrderNum != null">
        supplier_order_num = #{supplierOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="expectUseTime != null">
        expect_use_time = #{expectUseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cancelTime != null">
        cancel_time = #{cancelTime,jdbcType=TIMESTAMP},
      </if>
      <if test="confirmDeliveryTime != null">
        confirm_delivery_time = #{confirmDeliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="completionTime != null">
        completion_time = #{completionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="changeVehicleDemandType != null">
        change_vehicle_demand_type = #{changeVehicleDemandType,jdbcType=INTEGER},
      </if>
      <if test="replacementInstructions != null">
        replacement_instructions = #{replacementInstructions,jdbcType=VARCHAR},
      </if>
      <if test="vehicleChangeOrderStatus != null">
        vehicle_change_order_status = #{vehicleChangeOrderStatus,jdbcType=INTEGER},
      </if>
      <if test="levelId != null">
        level_id = #{levelId,jdbcType=INTEGER},
      </if>
      <if test="levelName != null">
        level_name = #{levelName,jdbcType=VARCHAR},
      </if>
      <if test="vehiclePriceRange != null">
        vehicle_price_range = #{vehiclePriceRange,jdbcType=VARCHAR},
      </if>
      <if test="vehicleLicense != null">
        vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="vehicleVin != null">
        vehicle_vin = #{vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleSupplierName != null">
        vehicle_supplier_name = #{vehicleSupplierName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleSupplierCode != null">
        vehicle_supplier_code = #{vehicleSupplierCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleSupplierType != null">
        vehicle_supplier_type = #{vehicleSupplierType,jdbcType=INTEGER},
      </if>
      <if test="driverId != null">
        driver_id = #{driverId,jdbcType=INTEGER},
      </if>
      <if test="driverName != null">
        driver_name = #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="driverMobile != null">
        driver_mobile = #{driverMobile,jdbcType=VARCHAR},
      </if>
      <if test="driverSupplierName != null">
        driver_supplier_name = #{driverSupplierName,jdbcType=VARCHAR},
      </if>
      <if test="driverSupplierCode != null">
        driver_supplier_code = #{driverSupplierCode,jdbcType=VARCHAR},
      </if>
      <if test="driverSupplierType != null">
        driver_supplier_type = #{driverSupplierType,jdbcType=INTEGER},
      </if>
      <if test="orderOwnerDeptCode != null">
        order_owner_dept_code = #{orderOwnerDeptCode,jdbcType=VARCHAR},
      </if>
      <if test="orderOwnerDeptName != null">
        order_owner_dept_name = #{orderOwnerDeptName,jdbcType=VARCHAR},
      </if>
      <if test="orderOwnerCompanyName != null">
        order_owner_company_name = #{orderOwnerCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="orderOwnerCompanyId != null">
        order_owner_company_id = #{orderOwnerCompanyId,jdbcType=INTEGER},
      </if>
      <if test="createId != null">
        create_id = #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        update_id = #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where vehicle_change_order_id = #{vehicleChangeOrderId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.VehicleChangeOrder">
    update so_vehicle_change_order
    set vehicle_change_order_num = #{vehicleChangeOrderNum,jdbcType=VARCHAR},
      demand_order_num = #{demandOrderNum,jdbcType=VARCHAR},
      customer_order_num = #{customerOrderNum,jdbcType=VARCHAR},
      previous_change_order_num = #{previousChangeOrderNum,jdbcType=VARCHAR},
      supplier_order_num = #{supplierOrderNum,jdbcType=VARCHAR},
      expect_use_time = #{expectUseTime,jdbcType=TIMESTAMP},
      cancel_time = #{cancelTime,jdbcType=TIMESTAMP},
      confirm_delivery_time = #{confirmDeliveryTime,jdbcType=TIMESTAMP},
      completion_time = #{completionTime,jdbcType=TIMESTAMP},
      change_vehicle_demand_type = #{changeVehicleDemandType,jdbcType=INTEGER},
      replacement_instructions = #{replacementInstructions,jdbcType=VARCHAR},
      vehicle_change_order_status = #{vehicleChangeOrderStatus,jdbcType=INTEGER},
      level_id = #{levelId,jdbcType=INTEGER},
      level_name = #{levelName,jdbcType=VARCHAR},
      vehicle_price_range = #{vehiclePriceRange,jdbcType=VARCHAR},
      vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      vehicle_vin = #{vehicleVin,jdbcType=VARCHAR},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      vehicle_supplier_name = #{vehicleSupplierName,jdbcType=VARCHAR},
      vehicle_supplier_code = #{vehicleSupplierCode,jdbcType=VARCHAR},
      vehicle_supplier_type = #{vehicleSupplierType,jdbcType=INTEGER},
      driver_id = #{driverId,jdbcType=INTEGER},
      driver_name = #{driverName,jdbcType=VARCHAR},
      driver_mobile = #{driverMobile,jdbcType=VARCHAR},
      driver_supplier_name = #{driverSupplierName,jdbcType=VARCHAR},
      driver_supplier_code = #{driverSupplierCode,jdbcType=VARCHAR},
      driver_supplier_type = #{driverSupplierType,jdbcType=INTEGER},
      order_owner_dept_code = #{orderOwnerDeptCode,jdbcType=VARCHAR},
      order_owner_dept_name = #{orderOwnerDeptName,jdbcType=VARCHAR},
      order_owner_company_name = #{orderOwnerCompanyName,jdbcType=VARCHAR},
      order_owner_company_id = #{orderOwnerCompanyId,jdbcType=INTEGER},
      create_id = #{createId,jdbcType=INTEGER},
      create_name = #{createName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_id = #{updateId,jdbcType=INTEGER},
      update_name = #{updateName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where vehicle_change_order_id = #{vehicleChangeOrderId,jdbcType=INTEGER}
  </update>
</mapper>