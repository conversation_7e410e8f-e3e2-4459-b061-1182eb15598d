<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.DemandOrderAttachmentMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.DemandOrderAttachment">
    <id column="demand_order_attachment_id" jdbcType="INTEGER" property="demandOrderAttachmentId" />
    <result column="create_id" jdbcType="INTEGER" property="createId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="demand_order_num" jdbcType="VARCHAR" property="demandOrderNum" />
    <result column="attachment_list" jdbcType="CHAR" property="attachmentList" />
  </resultMap>
  <sql id="Base_Column_List">
    demand_order_attachment_id, create_id, create_name, create_time, demand_order_num, 
    attachment_list
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from so_demand_order_attachment
    where demand_order_attachment_id = #{demandOrderAttachmentId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from so_demand_order_attachment
    where demand_order_attachment_id = #{demandOrderAttachmentId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.DemandOrderAttachment">
    insert into so_demand_order_attachment (demand_order_attachment_id, create_id, 
      create_name, create_time, demand_order_num, 
      attachment_list)
    values (#{demandOrderAttachmentId,jdbcType=INTEGER}, #{createId,jdbcType=INTEGER}, 
      #{createName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{demandOrderNum,jdbcType=VARCHAR}, 
      #{attachmentList,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.DemandOrderAttachment">
    insert into so_demand_order_attachment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="demandOrderAttachmentId != null">
        demand_order_attachment_id,
      </if>
      <if test="createId != null">
        create_id,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="demandOrderNum != null">
        demand_order_num,
      </if>
      <if test="attachmentList != null">
        attachment_list,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="demandOrderAttachmentId != null">
        #{demandOrderAttachmentId,jdbcType=INTEGER},
      </if>
      <if test="createId != null">
        #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="demandOrderNum != null">
        #{demandOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="attachmentList != null">
        #{attachmentList,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.DemandOrderAttachment">
    update so_demand_order_attachment
    <set>
      <if test="createId != null">
        create_id = #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="demandOrderNum != null">
        demand_order_num = #{demandOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="attachmentList != null">
        attachment_list = #{attachmentList,jdbcType=CHAR},
      </if>
    </set>
    where demand_order_attachment_id = #{demandOrderAttachmentId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.DemandOrderAttachment">
    update so_demand_order_attachment
    set create_id = #{createId,jdbcType=INTEGER},
      create_name = #{createName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      demand_order_num = #{demandOrderNum,jdbcType=VARCHAR},
      attachment_list = #{attachmentList,jdbcType=CHAR}
    where demand_order_attachment_id = #{demandOrderAttachmentId,jdbcType=INTEGER}
  </update>
</mapper>