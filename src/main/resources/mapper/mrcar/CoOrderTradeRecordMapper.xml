<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.CoOrderTradeRecordMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.CoOrderTradeRecord">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="serial_no" jdbcType="VARCHAR" property="serialNo" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="bill_no" jdbcType="VARCHAR" property="billNo" />
    <result column="cashier_serial_no" jdbcType="VARCHAR" property="cashierSerialNo" />
    <result column="trade_type" jdbcType="INTEGER" property="tradeType" />
    <result column="trade_amount" jdbcType="DECIMAL" property="tradeAmount" />
    <result column="trade_channel" jdbcType="INTEGER" property="tradeChannel" />
    <result column="trade_start_time" jdbcType="TIMESTAMP" property="tradeStartTime" />
    <result column="trade_end_time" jdbcType="TIMESTAMP" property="tradeEndTime" />
    <result column="trade_status" jdbcType="INTEGER" property="tradeStatus" />
    <result column="trade_desc" jdbcType="VARCHAR" property="tradeDesc" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, serial_no, order_no, bill_no, cashier_serial_no, trade_type, trade_amount, trade_channel, 
    trade_start_time, trade_end_time, trade_status, trade_desc, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.CoOrderTradeRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from co_order_trade_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from co_order_trade_record
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from co_order_trade_record
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.CoOrderTradeRecord">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into co_order_trade_record (serial_no, order_no, bill_no, 
      cashier_serial_no, trade_type, trade_amount, 
      trade_channel, trade_start_time, trade_end_time, 
      trade_status, trade_desc, create_time, 
      update_time)
    values (#{serialNo,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, #{billNo,jdbcType=VARCHAR}, 
      #{cashierSerialNo,jdbcType=VARCHAR}, #{tradeType,jdbcType=INTEGER}, #{tradeAmount,jdbcType=DECIMAL}, 
      #{tradeChannel,jdbcType=INTEGER}, #{tradeStartTime,jdbcType=TIMESTAMP}, #{tradeEndTime,jdbcType=TIMESTAMP}, 
      #{tradeStatus,jdbcType=INTEGER}, #{tradeDesc,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.CoOrderTradeRecord">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into co_order_trade_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="serialNo != null">
        serial_no,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="billNo != null">
        bill_no,
      </if>
      <if test="cashierSerialNo != null">
        cashier_serial_no,
      </if>
      <if test="tradeType != null">
        trade_type,
      </if>
      <if test="tradeAmount != null">
        trade_amount,
      </if>
      <if test="tradeChannel != null">
        trade_channel,
      </if>
      <if test="tradeStartTime != null">
        trade_start_time,
      </if>
      <if test="tradeEndTime != null">
        trade_end_time,
      </if>
      <if test="tradeStatus != null">
        trade_status,
      </if>
      <if test="tradeDesc != null">
        trade_desc,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="serialNo != null">
        #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="billNo != null">
        #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="cashierSerialNo != null">
        #{cashierSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="tradeType != null">
        #{tradeType,jdbcType=INTEGER},
      </if>
      <if test="tradeAmount != null">
        #{tradeAmount,jdbcType=DECIMAL},
      </if>
      <if test="tradeChannel != null">
        #{tradeChannel,jdbcType=INTEGER},
      </if>
      <if test="tradeStartTime != null">
        #{tradeStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tradeEndTime != null">
        #{tradeEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tradeStatus != null">
        #{tradeStatus,jdbcType=INTEGER},
      </if>
      <if test="tradeDesc != null">
        #{tradeDesc,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.CoOrderTradeRecordExample" resultType="java.lang.Long">
    select count(*) from co_order_trade_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update co_order_trade_record
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=INTEGER},
      </if>
      <if test="row.serialNo != null">
        serial_no = #{row.serialNo,jdbcType=VARCHAR},
      </if>
      <if test="row.orderNo != null">
        order_no = #{row.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="row.billNo != null">
        bill_no = #{row.billNo,jdbcType=VARCHAR},
      </if>
      <if test="row.cashierSerialNo != null">
        cashier_serial_no = #{row.cashierSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="row.tradeType != null">
        trade_type = #{row.tradeType,jdbcType=INTEGER},
      </if>
      <if test="row.tradeAmount != null">
        trade_amount = #{row.tradeAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.tradeChannel != null">
        trade_channel = #{row.tradeChannel,jdbcType=INTEGER},
      </if>
      <if test="row.tradeStartTime != null">
        trade_start_time = #{row.tradeStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.tradeEndTime != null">
        trade_end_time = #{row.tradeEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.tradeStatus != null">
        trade_status = #{row.tradeStatus,jdbcType=INTEGER},
      </if>
      <if test="row.tradeDesc != null">
        trade_desc = #{row.tradeDesc,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update co_order_trade_record
    set id = #{row.id,jdbcType=INTEGER},
      serial_no = #{row.serialNo,jdbcType=VARCHAR},
      order_no = #{row.orderNo,jdbcType=VARCHAR},
      bill_no = #{row.billNo,jdbcType=VARCHAR},
      cashier_serial_no = #{row.cashierSerialNo,jdbcType=VARCHAR},
      trade_type = #{row.tradeType,jdbcType=INTEGER},
      trade_amount = #{row.tradeAmount,jdbcType=DECIMAL},
      trade_channel = #{row.tradeChannel,jdbcType=INTEGER},
      trade_start_time = #{row.tradeStartTime,jdbcType=TIMESTAMP},
      trade_end_time = #{row.tradeEndTime,jdbcType=TIMESTAMP},
      trade_status = #{row.tradeStatus,jdbcType=INTEGER},
      trade_desc = #{row.tradeDesc,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.CoOrderTradeRecord">
    update co_order_trade_record
    <set>
      <if test="serialNo != null">
        serial_no = #{serialNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="billNo != null">
        bill_no = #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="cashierSerialNo != null">
        cashier_serial_no = #{cashierSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="tradeType != null">
        trade_type = #{tradeType,jdbcType=INTEGER},
      </if>
      <if test="tradeAmount != null">
        trade_amount = #{tradeAmount,jdbcType=DECIMAL},
      </if>
      <if test="tradeChannel != null">
        trade_channel = #{tradeChannel,jdbcType=INTEGER},
      </if>
      <if test="tradeStartTime != null">
        trade_start_time = #{tradeStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tradeEndTime != null">
        trade_end_time = #{tradeEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tradeStatus != null">
        trade_status = #{tradeStatus,jdbcType=INTEGER},
      </if>
      <if test="tradeDesc != null">
        trade_desc = #{tradeDesc,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.CoOrderTradeRecord">
    update co_order_trade_record
    set serial_no = #{serialNo,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      bill_no = #{billNo,jdbcType=VARCHAR},
      cashier_serial_no = #{cashierSerialNo,jdbcType=VARCHAR},
      trade_type = #{tradeType,jdbcType=INTEGER},
      trade_amount = #{tradeAmount,jdbcType=DECIMAL},
      trade_channel = #{tradeChannel,jdbcType=INTEGER},
      trade_start_time = #{tradeStartTime,jdbcType=TIMESTAMP},
      trade_end_time = #{tradeEndTime,jdbcType=TIMESTAMP},
      trade_status = #{tradeStatus,jdbcType=INTEGER},
      trade_desc = #{tradeDesc,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>