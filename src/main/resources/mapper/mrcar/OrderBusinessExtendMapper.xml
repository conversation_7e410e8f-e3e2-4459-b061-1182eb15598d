<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.OrderBusinessExtendMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.OrderBusinessExtend">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_apply_no" jdbcType="VARCHAR" property="orderApplyNo" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="send_msg_passenger" jdbcType="BIT" property="sendMsgPassenger" />
    <result column="handy_dispatch" jdbcType="BIT" property="handyDispatch" />
    <result column="benefit_card" jdbcType="BIT" property="benefitCard" />
    <result column="estimate_amount" jdbcType="DECIMAL" property="estimateAmount" />
    <result column="comment_submit" jdbcType="TINYINT" property="commentSubmit" />
    <result column="card_secret" jdbcType="VARCHAR" property="cardSecret" />
    <result column="vehicle_provider" jdbcType="TINYINT" property="vehicleProvider" />
    <result column="driver_provider" jdbcType="TINYINT" property="driverProvider" />
    <result column="supplier_provider_code" jdbcType="VARCHAR" property="supplierProviderCode" />
    <result column="expense_status" jdbcType="TINYINT" property="expenseStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="belong_buss_code" jdbcType="VARCHAR" property="belongBussCode" />
    <result column="belong_buss_name" jdbcType="VARCHAR" property="belongBussName" />
    <result column="operate_buss_name" jdbcType="VARCHAR" property="operateBussName" />
    <result column="operate_buss_code" jdbcType="VARCHAR" property="operateBussCode" />
    <result column="supplier_rate" jdbcType="DECIMAL" property="supplierRate" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.izu.order.entity.mrcar.OrderBusinessExtend">
    <result column="price_snapshot" jdbcType="LONGVARCHAR" property="priceSnapshot" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_apply_no, order_no, send_msg_passenger, handy_dispatch, benefit_card, estimate_amount, 
    comment_submit, card_secret, vehicle_provider, driver_provider, supplier_provider_code, 
    expense_status, create_time, update_time, belong_buss_code, belong_buss_name, operate_buss_name, 
    operate_buss_code, supplier_rate
  </sql>
  <sql id="Blob_Column_List">
    price_snapshot
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.izu.order.entity.mrcar.OrderBusinessExtendExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from order_business_extend
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.OrderBusinessExtendExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from order_business_extend
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from order_business_extend
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from order_business_extend
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.order.entity.mrcar.OrderBusinessExtendExample">
    delete from order_business_extend
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.OrderBusinessExtend">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_business_extend (order_apply_no, order_no, send_msg_passenger, 
      handy_dispatch, benefit_card, estimate_amount, 
      comment_submit, card_secret, vehicle_provider, 
      driver_provider, supplier_provider_code, expense_status, 
      create_time, update_time, belong_buss_code, 
      belong_buss_name, operate_buss_name, operate_buss_code, 
      supplier_rate, price_snapshot)
    values (#{orderApplyNo,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, #{sendMsgPassenger,jdbcType=BIT}, 
      #{handyDispatch,jdbcType=BIT}, #{benefitCard,jdbcType=BIT}, #{estimateAmount,jdbcType=DECIMAL}, 
      #{commentSubmit,jdbcType=TINYINT}, #{cardSecret,jdbcType=VARCHAR}, #{vehicleProvider,jdbcType=TINYINT}, 
      #{driverProvider,jdbcType=TINYINT}, #{supplierProviderCode,jdbcType=VARCHAR}, #{expenseStatus,jdbcType=TINYINT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{belongBussCode,jdbcType=VARCHAR}, 
      #{belongBussName,jdbcType=VARCHAR}, #{operateBussName,jdbcType=VARCHAR}, #{operateBussCode,jdbcType=VARCHAR}, 
      #{supplierRate,jdbcType=DECIMAL}, #{priceSnapshot,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.OrderBusinessExtend">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_business_extend
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderApplyNo != null">
        order_apply_no,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="sendMsgPassenger != null">
        send_msg_passenger,
      </if>
      <if test="handyDispatch != null">
        handy_dispatch,
      </if>
      <if test="benefitCard != null">
        benefit_card,
      </if>
      <if test="estimateAmount != null">
        estimate_amount,
      </if>
      <if test="commentSubmit != null">
        comment_submit,
      </if>
      <if test="cardSecret != null">
        card_secret,
      </if>
      <if test="vehicleProvider != null">
        vehicle_provider,
      </if>
      <if test="driverProvider != null">
        driver_provider,
      </if>
      <if test="supplierProviderCode != null">
        supplier_provider_code,
      </if>
      <if test="expenseStatus != null">
        expense_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="belongBussCode != null">
        belong_buss_code,
      </if>
      <if test="belongBussName != null">
        belong_buss_name,
      </if>
      <if test="operateBussName != null">
        operate_buss_name,
      </if>
      <if test="operateBussCode != null">
        operate_buss_code,
      </if>
      <if test="supplierRate != null">
        supplier_rate,
      </if>
      <if test="priceSnapshot != null">
        price_snapshot,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderApplyNo != null">
        #{orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="sendMsgPassenger != null">
        #{sendMsgPassenger,jdbcType=BIT},
      </if>
      <if test="handyDispatch != null">
        #{handyDispatch,jdbcType=BIT},
      </if>
      <if test="benefitCard != null">
        #{benefitCard,jdbcType=BIT},
      </if>
      <if test="estimateAmount != null">
        #{estimateAmount,jdbcType=DECIMAL},
      </if>
      <if test="commentSubmit != null">
        #{commentSubmit,jdbcType=TINYINT},
      </if>
      <if test="cardSecret != null">
        #{cardSecret,jdbcType=VARCHAR},
      </if>
      <if test="vehicleProvider != null">
        #{vehicleProvider,jdbcType=TINYINT},
      </if>
      <if test="driverProvider != null">
        #{driverProvider,jdbcType=TINYINT},
      </if>
      <if test="supplierProviderCode != null">
        #{supplierProviderCode,jdbcType=VARCHAR},
      </if>
      <if test="expenseStatus != null">
        #{expenseStatus,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="belongBussCode != null">
        #{belongBussCode,jdbcType=VARCHAR},
      </if>
      <if test="belongBussName != null">
        #{belongBussName,jdbcType=VARCHAR},
      </if>
      <if test="operateBussName != null">
        #{operateBussName,jdbcType=VARCHAR},
      </if>
      <if test="operateBussCode != null">
        #{operateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierRate != null">
        #{supplierRate,jdbcType=DECIMAL},
      </if>
      <if test="priceSnapshot != null">
        #{priceSnapshot,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.OrderBusinessExtendExample" resultType="java.lang.Long">
    select count(*) from order_business_extend
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update order_business_extend
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.orderApplyNo != null">
        order_apply_no = #{row.orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="row.orderNo != null">
        order_no = #{row.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="row.sendMsgPassenger != null">
        send_msg_passenger = #{row.sendMsgPassenger,jdbcType=BIT},
      </if>
      <if test="row.handyDispatch != null">
        handy_dispatch = #{row.handyDispatch,jdbcType=BIT},
      </if>
      <if test="row.benefitCard != null">
        benefit_card = #{row.benefitCard,jdbcType=BIT},
      </if>
      <if test="row.estimateAmount != null">
        estimate_amount = #{row.estimateAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.commentSubmit != null">
        comment_submit = #{row.commentSubmit,jdbcType=TINYINT},
      </if>
      <if test="row.cardSecret != null">
        card_secret = #{row.cardSecret,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleProvider != null">
        vehicle_provider = #{row.vehicleProvider,jdbcType=TINYINT},
      </if>
      <if test="row.driverProvider != null">
        driver_provider = #{row.driverProvider,jdbcType=TINYINT},
      </if>
      <if test="row.supplierProviderCode != null">
        supplier_provider_code = #{row.supplierProviderCode,jdbcType=VARCHAR},
      </if>
      <if test="row.expenseStatus != null">
        expense_status = #{row.expenseStatus,jdbcType=TINYINT},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.belongBussCode != null">
        belong_buss_code = #{row.belongBussCode,jdbcType=VARCHAR},
      </if>
      <if test="row.belongBussName != null">
        belong_buss_name = #{row.belongBussName,jdbcType=VARCHAR},
      </if>
      <if test="row.operateBussName != null">
        operate_buss_name = #{row.operateBussName,jdbcType=VARCHAR},
      </if>
      <if test="row.operateBussCode != null">
        operate_buss_code = #{row.operateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="row.supplierRate != null">
        supplier_rate = #{row.supplierRate,jdbcType=DECIMAL},
      </if>
      <if test="row.priceSnapshot != null">
        price_snapshot = #{row.priceSnapshot,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update order_business_extend
    set id = #{row.id,jdbcType=BIGINT},
      order_apply_no = #{row.orderApplyNo,jdbcType=VARCHAR},
      order_no = #{row.orderNo,jdbcType=VARCHAR},
      send_msg_passenger = #{row.sendMsgPassenger,jdbcType=BIT},
      handy_dispatch = #{row.handyDispatch,jdbcType=BIT},
      benefit_card = #{row.benefitCard,jdbcType=BIT},
      estimate_amount = #{row.estimateAmount,jdbcType=DECIMAL},
      comment_submit = #{row.commentSubmit,jdbcType=TINYINT},
      card_secret = #{row.cardSecret,jdbcType=VARCHAR},
      vehicle_provider = #{row.vehicleProvider,jdbcType=TINYINT},
      driver_provider = #{row.driverProvider,jdbcType=TINYINT},
      supplier_provider_code = #{row.supplierProviderCode,jdbcType=VARCHAR},
      expense_status = #{row.expenseStatus,jdbcType=TINYINT},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      belong_buss_code = #{row.belongBussCode,jdbcType=VARCHAR},
      belong_buss_name = #{row.belongBussName,jdbcType=VARCHAR},
      operate_buss_name = #{row.operateBussName,jdbcType=VARCHAR},
      operate_buss_code = #{row.operateBussCode,jdbcType=VARCHAR},
      supplier_rate = #{row.supplierRate,jdbcType=DECIMAL},
      price_snapshot = #{row.priceSnapshot,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update order_business_extend
    set id = #{row.id,jdbcType=BIGINT},
      order_apply_no = #{row.orderApplyNo,jdbcType=VARCHAR},
      order_no = #{row.orderNo,jdbcType=VARCHAR},
      send_msg_passenger = #{row.sendMsgPassenger,jdbcType=BIT},
      handy_dispatch = #{row.handyDispatch,jdbcType=BIT},
      benefit_card = #{row.benefitCard,jdbcType=BIT},
      estimate_amount = #{row.estimateAmount,jdbcType=DECIMAL},
      comment_submit = #{row.commentSubmit,jdbcType=TINYINT},
      card_secret = #{row.cardSecret,jdbcType=VARCHAR},
      vehicle_provider = #{row.vehicleProvider,jdbcType=TINYINT},
      driver_provider = #{row.driverProvider,jdbcType=TINYINT},
      supplier_provider_code = #{row.supplierProviderCode,jdbcType=VARCHAR},
      expense_status = #{row.expenseStatus,jdbcType=TINYINT},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      belong_buss_code = #{row.belongBussCode,jdbcType=VARCHAR},
      belong_buss_name = #{row.belongBussName,jdbcType=VARCHAR},
      operate_buss_name = #{row.operateBussName,jdbcType=VARCHAR},
      operate_buss_code = #{row.operateBussCode,jdbcType=VARCHAR},
      supplier_rate = #{row.supplierRate,jdbcType=DECIMAL}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.OrderBusinessExtend">
    update order_business_extend
    <set>
      <if test="orderApplyNo != null">
        order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="sendMsgPassenger != null">
        send_msg_passenger = #{sendMsgPassenger,jdbcType=BIT},
      </if>
      <if test="handyDispatch != null">
        handy_dispatch = #{handyDispatch,jdbcType=BIT},
      </if>
      <if test="benefitCard != null">
        benefit_card = #{benefitCard,jdbcType=BIT},
      </if>
      <if test="estimateAmount != null">
        estimate_amount = #{estimateAmount,jdbcType=DECIMAL},
      </if>
      <if test="commentSubmit != null">
        comment_submit = #{commentSubmit,jdbcType=TINYINT},
      </if>
      <if test="cardSecret != null">
        card_secret = #{cardSecret,jdbcType=VARCHAR},
      </if>
      <if test="vehicleProvider != null">
        vehicle_provider = #{vehicleProvider,jdbcType=TINYINT},
      </if>
      <if test="driverProvider != null">
        driver_provider = #{driverProvider,jdbcType=TINYINT},
      </if>
      <if test="supplierProviderCode != null">
        supplier_provider_code = #{supplierProviderCode,jdbcType=VARCHAR},
      </if>
      <if test="expenseStatus != null">
        expense_status = #{expenseStatus,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="belongBussCode != null">
        belong_buss_code = #{belongBussCode,jdbcType=VARCHAR},
      </if>
      <if test="belongBussName != null">
        belong_buss_name = #{belongBussName,jdbcType=VARCHAR},
      </if>
      <if test="operateBussName != null">
        operate_buss_name = #{operateBussName,jdbcType=VARCHAR},
      </if>
      <if test="operateBussCode != null">
        operate_buss_code = #{operateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierRate != null">
        supplier_rate = #{supplierRate,jdbcType=DECIMAL},
      </if>
      <if test="priceSnapshot != null">
        price_snapshot = #{priceSnapshot,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.izu.order.entity.mrcar.OrderBusinessExtend">
    update order_business_extend
    set order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      send_msg_passenger = #{sendMsgPassenger,jdbcType=BIT},
      handy_dispatch = #{handyDispatch,jdbcType=BIT},
      benefit_card = #{benefitCard,jdbcType=BIT},
      estimate_amount = #{estimateAmount,jdbcType=DECIMAL},
      comment_submit = #{commentSubmit,jdbcType=TINYINT},
      card_secret = #{cardSecret,jdbcType=VARCHAR},
      vehicle_provider = #{vehicleProvider,jdbcType=TINYINT},
      driver_provider = #{driverProvider,jdbcType=TINYINT},
      supplier_provider_code = #{supplierProviderCode,jdbcType=VARCHAR},
      expense_status = #{expenseStatus,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      belong_buss_code = #{belongBussCode,jdbcType=VARCHAR},
      belong_buss_name = #{belongBussName,jdbcType=VARCHAR},
      operate_buss_name = #{operateBussName,jdbcType=VARCHAR},
      operate_buss_code = #{operateBussCode,jdbcType=VARCHAR},
      supplier_rate = #{supplierRate,jdbcType=DECIMAL},
      price_snapshot = #{priceSnapshot,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.OrderBusinessExtend">
    update order_business_extend
    set order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      send_msg_passenger = #{sendMsgPassenger,jdbcType=BIT},
      handy_dispatch = #{handyDispatch,jdbcType=BIT},
      benefit_card = #{benefitCard,jdbcType=BIT},
      estimate_amount = #{estimateAmount,jdbcType=DECIMAL},
      comment_submit = #{commentSubmit,jdbcType=TINYINT},
      card_secret = #{cardSecret,jdbcType=VARCHAR},
      vehicle_provider = #{vehicleProvider,jdbcType=TINYINT},
      driver_provider = #{driverProvider,jdbcType=TINYINT},
      supplier_provider_code = #{supplierProviderCode,jdbcType=VARCHAR},
      expense_status = #{expenseStatus,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      belong_buss_code = #{belongBussCode,jdbcType=VARCHAR},
      belong_buss_name = #{belongBussName,jdbcType=VARCHAR},
      operate_buss_name = #{operateBussName,jdbcType=VARCHAR},
      operate_buss_code = #{operateBussCode,jdbcType=VARCHAR},
      supplier_rate = #{supplierRate,jdbcType=DECIMAL}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>