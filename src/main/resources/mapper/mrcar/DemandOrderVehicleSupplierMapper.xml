<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.DemandOrderVehicleSupplierMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.DemandOrderVehicleSupplier">
    <id column="demand_order_vehicle_supplier_id" jdbcType="INTEGER" property="demandOrderVehicleSupplierId" />
    <result column="create_id" jdbcType="INTEGER" property="createId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_id" jdbcType="INTEGER" property="updateId" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_tag" jdbcType="TINYINT" property="delTag" />
    <result column="demand_order_num" jdbcType="VARCHAR" property="demandOrderNum" />
    <result column="level_id" jdbcType="INTEGER" property="levelId" />
    <result column="level_name" jdbcType="VARCHAR" property="levelName" />
    <result column="vehicle_price_range" jdbcType="VARCHAR" property="vehiclePriceRange" />
    <result column="vehicle_count" jdbcType="INTEGER" property="vehicleCount" />
    <result column="vehicle_unit_price" jdbcType="DECIMAL" property="vehicleUnitPrice" />
    <result column="driver_count" jdbcType="INTEGER" property="driverCount" />
    <result column="driver_unit_price" jdbcType="DECIMAL" property="driverUnitPrice" />
    <result column="unit_price_amount" jdbcType="DECIMAL" property="unitPriceAmount" />
    <result column="supplier_type" jdbcType="TINYINT" property="supplierType" />
    <result column="supplier_id" jdbcType="VARCHAR" property="supplierId" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="supplier_remark" jdbcType="VARCHAR" property="supplierRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    demand_order_vehicle_supplier_id, create_id, create_name, create_time, update_id, 
    update_name, update_time, del_tag, demand_order_num, level_id, level_name, vehicle_price_range, 
    vehicle_count, vehicle_unit_price, driver_count, driver_unit_price, unit_price_amount, 
    supplier_type, supplier_id, supplier_name, supplier_remark
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from so_demand_order_vehicle_supplier
    where demand_order_vehicle_supplier_id = #{demandOrderVehicleSupplierId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from so_demand_order_vehicle_supplier
    where demand_order_vehicle_supplier_id = #{demandOrderVehicleSupplierId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.DemandOrderVehicleSupplier">
    insert into so_demand_order_vehicle_supplier (demand_order_vehicle_supplier_id, create_id, 
      create_name, create_time, update_id, 
      update_name, update_time, del_tag, 
      demand_order_num, level_id, level_name, 
      vehicle_price_range, vehicle_count, vehicle_unit_price, 
      driver_count, driver_unit_price, unit_price_amount, 
      supplier_type, supplier_id, supplier_name, 
      supplier_remark)
    values (#{demandOrderVehicleSupplierId,jdbcType=INTEGER}, #{createId,jdbcType=INTEGER}, 
      #{createName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateId,jdbcType=INTEGER}, 
      #{updateName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{delTag,jdbcType=TINYINT}, 
      #{demandOrderNum,jdbcType=VARCHAR}, #{levelId,jdbcType=INTEGER}, #{levelName,jdbcType=VARCHAR}, 
      #{vehiclePriceRange,jdbcType=VARCHAR}, #{vehicleCount,jdbcType=INTEGER}, #{vehicleUnitPrice,jdbcType=DECIMAL}, 
      #{driverCount,jdbcType=INTEGER}, #{driverUnitPrice,jdbcType=DECIMAL}, #{unitPriceAmount,jdbcType=DECIMAL}, 
      #{supplierType,jdbcType=TINYINT}, #{supplierId,jdbcType=VARCHAR}, #{supplierName,jdbcType=VARCHAR},
      #{supplierRemark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.DemandOrderVehicleSupplier">
    insert into so_demand_order_vehicle_supplier
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="demandOrderVehicleSupplierId != null">
        demand_order_vehicle_supplier_id,
      </if>
      <if test="createId != null">
        create_id,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateId != null">
        update_id,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="delTag != null">
        del_tag,
      </if>
      <if test="demandOrderNum != null">
        demand_order_num,
      </if>
      <if test="levelId != null">
        level_id,
      </if>
      <if test="levelName != null">
        level_name,
      </if>
      <if test="vehiclePriceRange != null">
        vehicle_price_range,
      </if>
      <if test="vehicleCount != null">
        vehicle_count,
      </if>
      <if test="vehicleUnitPrice != null">
        vehicle_unit_price,
      </if>
      <if test="driverCount != null">
        driver_count,
      </if>
      <if test="driverUnitPrice != null">
        driver_unit_price,
      </if>
      <if test="unitPriceAmount != null">
        unit_price_amount,
      </if>
      <if test="supplierType != null">
        supplier_type,
      </if>
      <if test="supplierId != null">
        supplier_id,
      </if>
      <if test="supplierName != null">
        supplier_name,
      </if>
      <if test="supplierRemark != null">
        supplier_remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="demandOrderVehicleSupplierId != null">
        #{demandOrderVehicleSupplierId,jdbcType=INTEGER},
      </if>
      <if test="createId != null">
        #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delTag != null">
        #{delTag,jdbcType=TINYINT},
      </if>
      <if test="demandOrderNum != null">
        #{demandOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="levelId != null">
        #{levelId,jdbcType=INTEGER},
      </if>
      <if test="levelName != null">
        #{levelName,jdbcType=VARCHAR},
      </if>
      <if test="vehiclePriceRange != null">
        #{vehiclePriceRange,jdbcType=VARCHAR},
      </if>
      <if test="vehicleCount != null">
        #{vehicleCount,jdbcType=INTEGER},
      </if>
      <if test="vehicleUnitPrice != null">
        #{vehicleUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="driverCount != null">
        #{driverCount,jdbcType=INTEGER},
      </if>
      <if test="driverUnitPrice != null">
        #{driverUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="unitPriceAmount != null">
        #{unitPriceAmount,jdbcType=DECIMAL},
      </if>
      <if test="supplierType != null">
        #{supplierType,jdbcType=TINYINT},
      </if>
      <if test="supplierId != null">
        #{supplierId,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="supplierRemark != null">
        #{supplierRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.DemandOrderVehicleSupplier">
    update so_demand_order_vehicle_supplier
    <set>
      <if test="createId != null">
        create_id = #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        update_id = #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delTag != null">
        del_tag = #{delTag,jdbcType=TINYINT},
      </if>
      <if test="demandOrderNum != null">
        demand_order_num = #{demandOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="levelId != null">
        level_id = #{levelId,jdbcType=INTEGER},
      </if>
      <if test="levelName != null">
        level_name = #{levelName,jdbcType=VARCHAR},
      </if>
      <if test="vehiclePriceRange != null">
        vehicle_price_range = #{vehiclePriceRange,jdbcType=VARCHAR},
      </if>
      <if test="vehicleCount != null">
        vehicle_count = #{vehicleCount,jdbcType=INTEGER},
      </if>
      <if test="vehicleUnitPrice != null">
        vehicle_unit_price = #{vehicleUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="driverCount != null">
        driver_count = #{driverCount,jdbcType=INTEGER},
      </if>
      <if test="driverUnitPrice != null">
        driver_unit_price = #{driverUnitPrice,jdbcType=DECIMAL},
      </if>
      <if test="unitPriceAmount != null">
        unit_price_amount = #{unitPriceAmount,jdbcType=DECIMAL},
      </if>
      <if test="supplierType != null">
        supplier_type = #{supplierType,jdbcType=TINYINT},
      </if>
      <if test="supplierId != null">
        supplier_id = #{supplierId,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        supplier_name = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="supplierRemark != null">
        supplier_remark = #{supplierRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where demand_order_vehicle_supplier_id = #{demandOrderVehicleSupplierId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.DemandOrderVehicleSupplier">
    update so_demand_order_vehicle_supplier
    set create_id = #{createId,jdbcType=INTEGER},
      create_name = #{createName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_id = #{updateId,jdbcType=INTEGER},
      update_name = #{updateName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      del_tag = #{delTag,jdbcType=TINYINT},
      demand_order_num = #{demandOrderNum,jdbcType=VARCHAR},
      level_id = #{levelId,jdbcType=INTEGER},
      level_name = #{levelName,jdbcType=VARCHAR},
      vehicle_price_range = #{vehiclePriceRange,jdbcType=VARCHAR},
      vehicle_count = #{vehicleCount,jdbcType=INTEGER},
      vehicle_unit_price = #{vehicleUnitPrice,jdbcType=DECIMAL},
      driver_count = #{driverCount,jdbcType=INTEGER},
      driver_unit_price = #{driverUnitPrice,jdbcType=DECIMAL},
      unit_price_amount = #{unitPriceAmount,jdbcType=DECIMAL},
      supplier_type = #{supplierType,jdbcType=TINYINT},
      supplier_id = #{supplierId,jdbcType=VARCHAR},
      supplier_name = #{supplierName,jdbcType=VARCHAR},
      supplier_remark = #{supplierRemark,jdbcType=VARCHAR}
    where demand_order_vehicle_supplier_id = #{demandOrderVehicleSupplierId,jdbcType=INTEGER}
  </update>
</mapper>