<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.BusinessSupplierExpenditureDetailMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.BusinessSupplierExpenditureDetail">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="expenditure_no" jdbcType="VARCHAR" property="expenditureNo" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="service_code" jdbcType="VARCHAR" property="serviceCode" />
    <result column="vehicle_provider" jdbcType="TINYINT" property="vehicleProvider" />
    <result column="driver_provider" jdbcType="TINYINT" property="driverProvider" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="should_pay_amount" jdbcType="DECIMAL" property="shouldPayAmount" />
    <result column="customer_bill_tax_rate" jdbcType="DECIMAL" property="customerBillTaxRate" />
    <result column="should_pay_amount_no_rate" jdbcType="DECIMAL" property="shouldPayAmountNoRate" />
    <result column="supplier_amount" jdbcType="DECIMAL" property="supplierAmount" />
    <result column="supplier_amount_no_rate" jdbcType="DECIMAL" property="supplierAmountNoRate" />
    <result column="return_rate" jdbcType="DECIMAL" property="returnRate" />
    <result column="car_license" jdbcType="VARCHAR" property="carLicense" />
    <result column="driver_id" jdbcType="BIGINT" property="driverId" />
    <result column="driver_name" jdbcType="VARCHAR" property="driverName" />
    <result column="driver_phone" jdbcType="VARCHAR" property="driverPhone" />
    <result column="use_time" jdbcType="VARCHAR" property="useTime" />
    <result column="base_amount" jdbcType="DECIMAL" property="baseAmount" />
    <result column="over_time_amount" jdbcType="DECIMAL" property="overTimeAmount" />
    <result column="over_mileage_amount" jdbcType="DECIMAL" property="overMileageAmount" />
    <result column="other_amount" jdbcType="DECIMAL" property="otherAmount" />
    <result column="reduction_total_amount" jdbcType="DECIMAL" property="reductionTotalAmount" />
    <result column="customer_id" jdbcType="INTEGER" property="customerId" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="booking_passenger_user_id" jdbcType="BIGINT" property="bookingPassengerUserId" />
    <result column="booking_passenger_user_name" jdbcType="VARCHAR" property="bookingPassengerUserName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="supplier_rate" jdbcType="DECIMAL" property="supplierRate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, expenditure_no, order_no, service_code, vehicle_provider, driver_provider, company_id, 
    company_code, company_name, should_pay_amount, customer_bill_tax_rate, should_pay_amount_no_rate, 
    supplier_amount, supplier_amount_no_rate, return_rate, car_license, driver_id, driver_name, 
    driver_phone, use_time, base_amount, over_time_amount, over_mileage_amount, other_amount, 
    reduction_total_amount, customer_id, customer_name, booking_passenger_user_id, booking_passenger_user_name, 
    create_time, update_time, supplier_rate
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.BusinessSupplierExpenditureDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from business_supplier_expenditure_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from business_supplier_expenditure_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from business_supplier_expenditure_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.order.entity.mrcar.BusinessSupplierExpenditureDetailExample">
    delete from business_supplier_expenditure_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.BusinessSupplierExpenditureDetail">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into business_supplier_expenditure_detail (expenditure_no, order_no, service_code, 
      vehicle_provider, driver_provider, company_id, 
      company_code, company_name, should_pay_amount, 
      customer_bill_tax_rate, should_pay_amount_no_rate, 
      supplier_amount, supplier_amount_no_rate, return_rate, 
      car_license, driver_id, driver_name, 
      driver_phone, use_time, base_amount, 
      over_time_amount, over_mileage_amount, other_amount, 
      reduction_total_amount, customer_id, customer_name, 
      booking_passenger_user_id, booking_passenger_user_name, 
      create_time, update_time, supplier_rate
      )
    values (#{expenditureNo,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, #{serviceCode,jdbcType=VARCHAR}, 
      #{vehicleProvider,jdbcType=TINYINT}, #{driverProvider,jdbcType=TINYINT}, #{companyId,jdbcType=INTEGER}, 
      #{companyCode,jdbcType=VARCHAR}, #{companyName,jdbcType=VARCHAR}, #{shouldPayAmount,jdbcType=DECIMAL}, 
      #{customerBillTaxRate,jdbcType=DECIMAL}, #{shouldPayAmountNoRate,jdbcType=DECIMAL}, 
      #{supplierAmount,jdbcType=DECIMAL}, #{supplierAmountNoRate,jdbcType=DECIMAL}, #{returnRate,jdbcType=DECIMAL}, 
      #{carLicense,jdbcType=VARCHAR}, #{driverId,jdbcType=BIGINT}, #{driverName,jdbcType=VARCHAR}, 
      #{driverPhone,jdbcType=VARCHAR}, #{useTime,jdbcType=VARCHAR}, #{baseAmount,jdbcType=DECIMAL}, 
      #{overTimeAmount,jdbcType=DECIMAL}, #{overMileageAmount,jdbcType=DECIMAL}, #{otherAmount,jdbcType=DECIMAL}, 
      #{reductionTotalAmount,jdbcType=DECIMAL}, #{customerId,jdbcType=INTEGER}, #{customerName,jdbcType=VARCHAR}, 
      #{bookingPassengerUserId,jdbcType=BIGINT}, #{bookingPassengerUserName,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{supplierRate,jdbcType=DECIMAL}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.BusinessSupplierExpenditureDetail">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into business_supplier_expenditure_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="expenditureNo != null">
        expenditure_no,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="serviceCode != null">
        service_code,
      </if>
      <if test="vehicleProvider != null">
        vehicle_provider,
      </if>
      <if test="driverProvider != null">
        driver_provider,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="shouldPayAmount != null">
        should_pay_amount,
      </if>
      <if test="customerBillTaxRate != null">
        customer_bill_tax_rate,
      </if>
      <if test="shouldPayAmountNoRate != null">
        should_pay_amount_no_rate,
      </if>
      <if test="supplierAmount != null">
        supplier_amount,
      </if>
      <if test="supplierAmountNoRate != null">
        supplier_amount_no_rate,
      </if>
      <if test="returnRate != null">
        return_rate,
      </if>
      <if test="carLicense != null">
        car_license,
      </if>
      <if test="driverId != null">
        driver_id,
      </if>
      <if test="driverName != null">
        driver_name,
      </if>
      <if test="driverPhone != null">
        driver_phone,
      </if>
      <if test="useTime != null">
        use_time,
      </if>
      <if test="baseAmount != null">
        base_amount,
      </if>
      <if test="overTimeAmount != null">
        over_time_amount,
      </if>
      <if test="overMileageAmount != null">
        over_mileage_amount,
      </if>
      <if test="otherAmount != null">
        other_amount,
      </if>
      <if test="reductionTotalAmount != null">
        reduction_total_amount,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="bookingPassengerUserId != null">
        booking_passenger_user_id,
      </if>
      <if test="bookingPassengerUserName != null">
        booking_passenger_user_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="supplierRate != null">
        supplier_rate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="expenditureNo != null">
        #{expenditureNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="serviceCode != null">
        #{serviceCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleProvider != null">
        #{vehicleProvider,jdbcType=TINYINT},
      </if>
      <if test="driverProvider != null">
        #{driverProvider,jdbcType=TINYINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="shouldPayAmount != null">
        #{shouldPayAmount,jdbcType=DECIMAL},
      </if>
      <if test="customerBillTaxRate != null">
        #{customerBillTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="shouldPayAmountNoRate != null">
        #{shouldPayAmountNoRate,jdbcType=DECIMAL},
      </if>
      <if test="supplierAmount != null">
        #{supplierAmount,jdbcType=DECIMAL},
      </if>
      <if test="supplierAmountNoRate != null">
        #{supplierAmountNoRate,jdbcType=DECIMAL},
      </if>
      <if test="returnRate != null">
        #{returnRate,jdbcType=DECIMAL},
      </if>
      <if test="carLicense != null">
        #{carLicense,jdbcType=VARCHAR},
      </if>
      <if test="driverId != null">
        #{driverId,jdbcType=BIGINT},
      </if>
      <if test="driverName != null">
        #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="driverPhone != null">
        #{driverPhone,jdbcType=VARCHAR},
      </if>
      <if test="useTime != null">
        #{useTime,jdbcType=VARCHAR},
      </if>
      <if test="baseAmount != null">
        #{baseAmount,jdbcType=DECIMAL},
      </if>
      <if test="overTimeAmount != null">
        #{overTimeAmount,jdbcType=DECIMAL},
      </if>
      <if test="overMileageAmount != null">
        #{overMileageAmount,jdbcType=DECIMAL},
      </if>
      <if test="otherAmount != null">
        #{otherAmount,jdbcType=DECIMAL},
      </if>
      <if test="reductionTotalAmount != null">
        #{reductionTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=INTEGER},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="bookingPassengerUserId != null">
        #{bookingPassengerUserId,jdbcType=BIGINT},
      </if>
      <if test="bookingPassengerUserName != null">
        #{bookingPassengerUserName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="supplierRate != null">
        #{supplierRate,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.BusinessSupplierExpenditureDetailExample" resultType="java.lang.Long">
    select count(*) from business_supplier_expenditure_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update business_supplier_expenditure_detail
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.expenditureNo != null">
        expenditure_no = #{row.expenditureNo,jdbcType=VARCHAR},
      </if>
      <if test="row.orderNo != null">
        order_no = #{row.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="row.serviceCode != null">
        service_code = #{row.serviceCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleProvider != null">
        vehicle_provider = #{row.vehicleProvider,jdbcType=TINYINT},
      </if>
      <if test="row.driverProvider != null">
        driver_provider = #{row.driverProvider,jdbcType=TINYINT},
      </if>
      <if test="row.companyId != null">
        company_id = #{row.companyId,jdbcType=INTEGER},
      </if>
      <if test="row.companyCode != null">
        company_code = #{row.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="row.companyName != null">
        company_name = #{row.companyName,jdbcType=VARCHAR},
      </if>
      <if test="row.shouldPayAmount != null">
        should_pay_amount = #{row.shouldPayAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.customerBillTaxRate != null">
        customer_bill_tax_rate = #{row.customerBillTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="row.shouldPayAmountNoRate != null">
        should_pay_amount_no_rate = #{row.shouldPayAmountNoRate,jdbcType=DECIMAL},
      </if>
      <if test="row.supplierAmount != null">
        supplier_amount = #{row.supplierAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.supplierAmountNoRate != null">
        supplier_amount_no_rate = #{row.supplierAmountNoRate,jdbcType=DECIMAL},
      </if>
      <if test="row.returnRate != null">
        return_rate = #{row.returnRate,jdbcType=DECIMAL},
      </if>
      <if test="row.carLicense != null">
        car_license = #{row.carLicense,jdbcType=VARCHAR},
      </if>
      <if test="row.driverId != null">
        driver_id = #{row.driverId,jdbcType=BIGINT},
      </if>
      <if test="row.driverName != null">
        driver_name = #{row.driverName,jdbcType=VARCHAR},
      </if>
      <if test="row.driverPhone != null">
        driver_phone = #{row.driverPhone,jdbcType=VARCHAR},
      </if>
      <if test="row.useTime != null">
        use_time = #{row.useTime,jdbcType=VARCHAR},
      </if>
      <if test="row.baseAmount != null">
        base_amount = #{row.baseAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.overTimeAmount != null">
        over_time_amount = #{row.overTimeAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.overMileageAmount != null">
        over_mileage_amount = #{row.overMileageAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.otherAmount != null">
        other_amount = #{row.otherAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.reductionTotalAmount != null">
        reduction_total_amount = #{row.reductionTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.customerId != null">
        customer_id = #{row.customerId,jdbcType=INTEGER},
      </if>
      <if test="row.customerName != null">
        customer_name = #{row.customerName,jdbcType=VARCHAR},
      </if>
      <if test="row.bookingPassengerUserId != null">
        booking_passenger_user_id = #{row.bookingPassengerUserId,jdbcType=BIGINT},
      </if>
      <if test="row.bookingPassengerUserName != null">
        booking_passenger_user_name = #{row.bookingPassengerUserName,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.supplierRate != null">
        supplier_rate = #{row.supplierRate,jdbcType=DECIMAL},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update business_supplier_expenditure_detail
    set id = #{row.id,jdbcType=BIGINT},
      expenditure_no = #{row.expenditureNo,jdbcType=VARCHAR},
      order_no = #{row.orderNo,jdbcType=VARCHAR},
      service_code = #{row.serviceCode,jdbcType=VARCHAR},
      vehicle_provider = #{row.vehicleProvider,jdbcType=TINYINT},
      driver_provider = #{row.driverProvider,jdbcType=TINYINT},
      company_id = #{row.companyId,jdbcType=INTEGER},
      company_code = #{row.companyCode,jdbcType=VARCHAR},
      company_name = #{row.companyName,jdbcType=VARCHAR},
      should_pay_amount = #{row.shouldPayAmount,jdbcType=DECIMAL},
      customer_bill_tax_rate = #{row.customerBillTaxRate,jdbcType=DECIMAL},
      should_pay_amount_no_rate = #{row.shouldPayAmountNoRate,jdbcType=DECIMAL},
      supplier_amount = #{row.supplierAmount,jdbcType=DECIMAL},
      supplier_amount_no_rate = #{row.supplierAmountNoRate,jdbcType=DECIMAL},
      return_rate = #{row.returnRate,jdbcType=DECIMAL},
      car_license = #{row.carLicense,jdbcType=VARCHAR},
      driver_id = #{row.driverId,jdbcType=BIGINT},
      driver_name = #{row.driverName,jdbcType=VARCHAR},
      driver_phone = #{row.driverPhone,jdbcType=VARCHAR},
      use_time = #{row.useTime,jdbcType=VARCHAR},
      base_amount = #{row.baseAmount,jdbcType=DECIMAL},
      over_time_amount = #{row.overTimeAmount,jdbcType=DECIMAL},
      over_mileage_amount = #{row.overMileageAmount,jdbcType=DECIMAL},
      other_amount = #{row.otherAmount,jdbcType=DECIMAL},
      reduction_total_amount = #{row.reductionTotalAmount,jdbcType=DECIMAL},
      customer_id = #{row.customerId,jdbcType=INTEGER},
      customer_name = #{row.customerName,jdbcType=VARCHAR},
      booking_passenger_user_id = #{row.bookingPassengerUserId,jdbcType=BIGINT},
      booking_passenger_user_name = #{row.bookingPassengerUserName,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      supplier_rate = #{row.supplierRate,jdbcType=DECIMAL}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.BusinessSupplierExpenditureDetail">
    update business_supplier_expenditure_detail
    <set>
      <if test="expenditureNo != null">
        expenditure_no = #{expenditureNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="serviceCode != null">
        service_code = #{serviceCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleProvider != null">
        vehicle_provider = #{vehicleProvider,jdbcType=TINYINT},
      </if>
      <if test="driverProvider != null">
        driver_provider = #{driverProvider,jdbcType=TINYINT},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="shouldPayAmount != null">
        should_pay_amount = #{shouldPayAmount,jdbcType=DECIMAL},
      </if>
      <if test="customerBillTaxRate != null">
        customer_bill_tax_rate = #{customerBillTaxRate,jdbcType=DECIMAL},
      </if>
      <if test="shouldPayAmountNoRate != null">
        should_pay_amount_no_rate = #{shouldPayAmountNoRate,jdbcType=DECIMAL},
      </if>
      <if test="supplierAmount != null">
        supplier_amount = #{supplierAmount,jdbcType=DECIMAL},
      </if>
      <if test="supplierAmountNoRate != null">
        supplier_amount_no_rate = #{supplierAmountNoRate,jdbcType=DECIMAL},
      </if>
      <if test="returnRate != null">
        return_rate = #{returnRate,jdbcType=DECIMAL},
      </if>
      <if test="carLicense != null">
        car_license = #{carLicense,jdbcType=VARCHAR},
      </if>
      <if test="driverId != null">
        driver_id = #{driverId,jdbcType=BIGINT},
      </if>
      <if test="driverName != null">
        driver_name = #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="driverPhone != null">
        driver_phone = #{driverPhone,jdbcType=VARCHAR},
      </if>
      <if test="useTime != null">
        use_time = #{useTime,jdbcType=VARCHAR},
      </if>
      <if test="baseAmount != null">
        base_amount = #{baseAmount,jdbcType=DECIMAL},
      </if>
      <if test="overTimeAmount != null">
        over_time_amount = #{overTimeAmount,jdbcType=DECIMAL},
      </if>
      <if test="overMileageAmount != null">
        over_mileage_amount = #{overMileageAmount,jdbcType=DECIMAL},
      </if>
      <if test="otherAmount != null">
        other_amount = #{otherAmount,jdbcType=DECIMAL},
      </if>
      <if test="reductionTotalAmount != null">
        reduction_total_amount = #{reductionTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=INTEGER},
      </if>
      <if test="customerName != null">
        customer_name = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="bookingPassengerUserId != null">
        booking_passenger_user_id = #{bookingPassengerUserId,jdbcType=BIGINT},
      </if>
      <if test="bookingPassengerUserName != null">
        booking_passenger_user_name = #{bookingPassengerUserName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="supplierRate != null">
        supplier_rate = #{supplierRate,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.BusinessSupplierExpenditureDetail">
    update business_supplier_expenditure_detail
    set expenditure_no = #{expenditureNo,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      service_code = #{serviceCode,jdbcType=VARCHAR},
      vehicle_provider = #{vehicleProvider,jdbcType=TINYINT},
      driver_provider = #{driverProvider,jdbcType=TINYINT},
      company_id = #{companyId,jdbcType=INTEGER},
      company_code = #{companyCode,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      should_pay_amount = #{shouldPayAmount,jdbcType=DECIMAL},
      customer_bill_tax_rate = #{customerBillTaxRate,jdbcType=DECIMAL},
      should_pay_amount_no_rate = #{shouldPayAmountNoRate,jdbcType=DECIMAL},
      supplier_amount = #{supplierAmount,jdbcType=DECIMAL},
      supplier_amount_no_rate = #{supplierAmountNoRate,jdbcType=DECIMAL},
      return_rate = #{returnRate,jdbcType=DECIMAL},
      car_license = #{carLicense,jdbcType=VARCHAR},
      driver_id = #{driverId,jdbcType=BIGINT},
      driver_name = #{driverName,jdbcType=VARCHAR},
      driver_phone = #{driverPhone,jdbcType=VARCHAR},
      use_time = #{useTime,jdbcType=VARCHAR},
      base_amount = #{baseAmount,jdbcType=DECIMAL},
      over_time_amount = #{overTimeAmount,jdbcType=DECIMAL},
      over_mileage_amount = #{overMileageAmount,jdbcType=DECIMAL},
      other_amount = #{otherAmount,jdbcType=DECIMAL},
      reduction_total_amount = #{reductionTotalAmount,jdbcType=DECIMAL},
      customer_id = #{customerId,jdbcType=INTEGER},
      customer_name = #{customerName,jdbcType=VARCHAR},
      booking_passenger_user_id = #{bookingPassengerUserId,jdbcType=BIGINT},
      booking_passenger_user_name = #{bookingPassengerUserName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      supplier_rate = #{supplierRate,jdbcType=DECIMAL}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>