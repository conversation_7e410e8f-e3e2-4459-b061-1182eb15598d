<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ScheduleSubMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.ScheduleSub">
    <id column="sub_schedule_id" jdbcType="BIGINT" property="subScheduleId" />
    <result column="schedule_id" jdbcType="BIGINT" property="scheduleId" />
    <result column="order_apply_id" jdbcType="BIGINT" property="orderApplyId" />
    <result column="order_apply_no" jdbcType="VARCHAR" property="orderApplyNo" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="struct_id" jdbcType="BIGINT" property="structId" />
    <result column="sub_schedule_status" jdbcType="TINYINT" property="subScheduleStatus" />
    <result column="total_vehicle_count" jdbcType="INTEGER" property="totalVehicleCount" />
    <result column="valid" jdbcType="BIT" property="valid" />
    <result column="schedule_time" jdbcType="VARCHAR" property="scheduleTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="last_update_id" jdbcType="BIGINT" property="lastUpdateId" />
    <result column="last_update_name" jdbcType="VARCHAR" property="lastUpdateName" />
    <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    sub_schedule_id, schedule_id, order_apply_id, order_apply_no, company_id, struct_id, 
    sub_schedule_status, total_vehicle_count, valid, schedule_time, create_time, last_update_id, 
    last_update_name, last_update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from schedule_sub
    where sub_schedule_id = #{subScheduleId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from schedule_sub
    where sub_schedule_id = #{subScheduleId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.ScheduleSub">
    <selectKey keyProperty="subScheduleId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into schedule_sub (schedule_id, order_apply_id, order_apply_no, 
      company_id, struct_id, sub_schedule_status, 
      total_vehicle_count, valid, schedule_time, 
      create_time, last_update_id, last_update_name, 
      last_update_time)
    values (#{scheduleId,jdbcType=BIGINT}, #{orderApplyId,jdbcType=BIGINT}, #{orderApplyNo,jdbcType=VARCHAR}, 
      #{companyId,jdbcType=BIGINT}, #{structId,jdbcType=BIGINT}, #{subScheduleStatus,jdbcType=TINYINT}, 
      #{totalVehicleCount,jdbcType=INTEGER}, #{valid,jdbcType=BIT}, #{scheduleTime,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{lastUpdateId,jdbcType=BIGINT}, #{lastUpdateName,jdbcType=VARCHAR}, 
      #{lastUpdateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.ScheduleSub">
    <selectKey keyProperty="subScheduleId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into schedule_sub
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="scheduleId != null">
        schedule_id,
      </if>
      <if test="orderApplyId != null">
        order_apply_id,
      </if>
      <if test="orderApplyNo != null">
        order_apply_no,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="structId != null">
        struct_id,
      </if>
      <if test="subScheduleStatus != null">
        sub_schedule_status,
      </if>
      <if test="totalVehicleCount != null">
        total_vehicle_count,
      </if>
      <if test="valid != null">
        valid,
      </if>
      <if test="scheduleTime != null">
        schedule_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="lastUpdateId != null">
        last_update_id,
      </if>
      <if test="lastUpdateName != null">
        last_update_name,
      </if>
      <if test="lastUpdateTime != null">
        last_update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="scheduleId != null">
        #{scheduleId,jdbcType=BIGINT},
      </if>
      <if test="orderApplyId != null">
        #{orderApplyId,jdbcType=BIGINT},
      </if>
      <if test="orderApplyNo != null">
        #{orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="structId != null">
        #{structId,jdbcType=BIGINT},
      </if>
      <if test="subScheduleStatus != null">
        #{subScheduleStatus,jdbcType=TINYINT},
      </if>
      <if test="totalVehicleCount != null">
        #{totalVehicleCount,jdbcType=INTEGER},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=BIT},
      </if>
      <if test="scheduleTime != null">
        #{scheduleTime,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateId != null">
        #{lastUpdateId,jdbcType=BIGINT},
      </if>
      <if test="lastUpdateName != null">
        #{lastUpdateName,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.ScheduleSub">
    update schedule_sub
    <set>
      <if test="scheduleId != null">
        schedule_id = #{scheduleId,jdbcType=BIGINT},
      </if>
      <if test="orderApplyId != null">
        order_apply_id = #{orderApplyId,jdbcType=BIGINT},
      </if>
      <if test="orderApplyNo != null">
        order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="structId != null">
        struct_id = #{structId,jdbcType=BIGINT},
      </if>
      <if test="subScheduleStatus != null">
        sub_schedule_status = #{subScheduleStatus,jdbcType=TINYINT},
      </if>
      <if test="totalVehicleCount != null">
        total_vehicle_count = #{totalVehicleCount,jdbcType=INTEGER},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=BIT},
      </if>
      <if test="scheduleTime != null">
        schedule_time = #{scheduleTime,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdateId != null">
        last_update_id = #{lastUpdateId,jdbcType=BIGINT},
      </if>
      <if test="lastUpdateName != null">
        last_update_name = #{lastUpdateName,jdbcType=VARCHAR},
      </if>
      <if test="lastUpdateTime != null">
        last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where sub_schedule_id = #{subScheduleId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.ScheduleSub">
    update schedule_sub
    set schedule_id = #{scheduleId,jdbcType=BIGINT},
      order_apply_id = #{orderApplyId,jdbcType=BIGINT},
      order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=BIGINT},
      struct_id = #{structId,jdbcType=BIGINT},
      sub_schedule_status = #{subScheduleStatus,jdbcType=TINYINT},
      total_vehicle_count = #{totalVehicleCount,jdbcType=INTEGER},
      valid = #{valid,jdbcType=BIT},
      schedule_time = #{scheduleTime,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      last_update_id = #{lastUpdateId,jdbcType=BIGINT},
      last_update_name = #{lastUpdateName,jdbcType=VARCHAR},
      last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP}
    where sub_schedule_id = #{subScheduleId,jdbcType=BIGINT}
  </update>
</mapper>