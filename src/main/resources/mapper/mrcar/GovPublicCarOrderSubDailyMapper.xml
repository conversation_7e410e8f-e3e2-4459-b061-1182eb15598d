<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.GovPublicCarOrderSubDailyMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.GovPublicCarOrderSubDaily">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="stat_date" jdbcType="DATE" property="statDate" />
    <result column="expected_pickup_time" jdbcType="TIMESTAMP" property="expectedPickupTime" />
    <result column="actual_return_time" jdbcType="TIMESTAMP" property="actualReturnTime" />
    <result column="total_mileage" jdbcType="DECIMAL" property="totalMileage" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_no, stat_date, expected_pickup_time, actual_return_time, total_mileage, 
    create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.GovPublicCarOrderSubDailyExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from gov_public_car_order_sub_daily
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from gov_public_car_order_sub_daily
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from gov_public_car_order_sub_daily
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.order.entity.mrcar.GovPublicCarOrderSubDailyExample">
    delete from gov_public_car_order_sub_daily
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.GovPublicCarOrderSubDaily">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gov_public_car_order_sub_daily (order_no, stat_date, expected_pickup_time, 
      actual_return_time, total_mileage, create_time, 
      update_time)
    values (#{orderNo,jdbcType=VARCHAR}, #{statDate,jdbcType=DATE}, #{expectedPickupTime,jdbcType=TIMESTAMP}, 
      #{actualReturnTime,jdbcType=TIMESTAMP}, #{totalMileage,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.GovPublicCarOrderSubDaily">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gov_public_car_order_sub_daily
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="statDate != null">
        stat_date,
      </if>
      <if test="expectedPickupTime != null">
        expected_pickup_time,
      </if>
      <if test="actualReturnTime != null">
        actual_return_time,
      </if>
      <if test="totalMileage != null">
        total_mileage,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="statDate != null">
        #{statDate,jdbcType=DATE},
      </if>
      <if test="expectedPickupTime != null">
        #{expectedPickupTime,jdbcType=TIMESTAMP},
      </if>
      <if test="actualReturnTime != null">
        #{actualReturnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="totalMileage != null">
        #{totalMileage,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.GovPublicCarOrderSubDailyExample" resultType="java.lang.Long">
    select count(*) from gov_public_car_order_sub_daily
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update gov_public_car_order_sub_daily
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.orderNo != null">
        order_no = #{row.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="row.statDate != null">
        stat_date = #{row.statDate,jdbcType=DATE},
      </if>
      <if test="row.expectedPickupTime != null">
        expected_pickup_time = #{row.expectedPickupTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.actualReturnTime != null">
        actual_return_time = #{row.actualReturnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.totalMileage != null">
        total_mileage = #{row.totalMileage,jdbcType=DECIMAL},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update gov_public_car_order_sub_daily
    set id = #{row.id,jdbcType=BIGINT},
      order_no = #{row.orderNo,jdbcType=VARCHAR},
      stat_date = #{row.statDate,jdbcType=DATE},
      expected_pickup_time = #{row.expectedPickupTime,jdbcType=TIMESTAMP},
      actual_return_time = #{row.actualReturnTime,jdbcType=TIMESTAMP},
      total_mileage = #{row.totalMileage,jdbcType=DECIMAL},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.GovPublicCarOrderSubDaily">
    update gov_public_car_order_sub_daily
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="statDate != null">
        stat_date = #{statDate,jdbcType=DATE},
      </if>
      <if test="expectedPickupTime != null">
        expected_pickup_time = #{expectedPickupTime,jdbcType=TIMESTAMP},
      </if>
      <if test="actualReturnTime != null">
        actual_return_time = #{actualReturnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="totalMileage != null">
        total_mileage = #{totalMileage,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.GovPublicCarOrderSubDaily">
    update gov_public_car_order_sub_daily
    set order_no = #{orderNo,jdbcType=VARCHAR},
      stat_date = #{statDate,jdbcType=DATE},
      expected_pickup_time = #{expectedPickupTime,jdbcType=TIMESTAMP},
      actual_return_time = #{actualReturnTime,jdbcType=TIMESTAMP},
      total_mileage = #{totalMileage,jdbcType=DECIMAL},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>