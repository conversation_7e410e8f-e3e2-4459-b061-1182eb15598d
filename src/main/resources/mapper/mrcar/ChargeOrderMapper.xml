<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ChargeOrderMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.ChargeOrder">
    <id column="order_id" jdbcType="INTEGER" property="orderId" />
    <result column="order_code" jdbcType="VARCHAR" property="orderCode" />
    <result column="start_charge_seq" jdbcType="VARCHAR" property="startChargeSeq" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="vehicle_vin" jdbcType="VARCHAR" property="vehicleVin" />
    <result column="vehicle_license" jdbcType="VARCHAR" property="vehicleLicense" />
    <result column="enter_way" jdbcType="TINYINT" property="enterWay" />
    <result column="vehicle_license_url" jdbcType="VARCHAR" property="vehicleLicenseUrl" />
    <result column="license_check_time" jdbcType="TIMESTAMP" property="licenseCheckTime" />
    <result column="lease_order_owner_id" jdbcType="INTEGER" property="leaseOrderOwnerId" />
    <result column="lease_order_owner_name" jdbcType="VARCHAR" property="leaseOrderOwnerName" />
    <result column="charge_start_time" jdbcType="TIMESTAMP" property="chargeStartTime" />
    <result column="charge_end_time" jdbcType="TIMESTAMP" property="chargeEndTime" />
    <result column="order_end_time" jdbcType="TIMESTAMP" property="orderEndTime" />
    <result column="order_status" jdbcType="TINYINT" property="orderStatus" />
    <result column="charge_status" jdbcType="TINYINT" property="chargeStatus" />
    <result column="end_reason" jdbcType="VARCHAR" property="endReason" />
    <result column="end_way" jdbcType="TINYINT" property="endWay" />
    <result column="channel_id" jdbcType="INTEGER" property="channelId" />
    <result column="channel_name" jdbcType="VARCHAR" property="channelName" />
    <result column="operator_id" jdbcType="INTEGER" property="operatorId" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="charge_station_id" jdbcType="VARCHAR" property="chargeStationId" />
    <result column="charge_station_name" jdbcType="VARCHAR" property="chargeStationName" />
    <result column="station_city_code" jdbcType="VARCHAR" property="stationCityCode" />
    <result column="station_city_name" jdbcType="VARCHAR" property="stationCityName" />
    <result column="equipment_id" jdbcType="VARCHAR" property="equipmentId" />
    <result column="connector_id" jdbcType="VARCHAR" property="connectorId" />
    <result column="connector_name" jdbcType="VARCHAR" property="connectorName" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="user_phone" jdbcType="VARCHAR" property="userPhone" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="vehicle_operate_buss_code" jdbcType="VARCHAR" property="vehicleOperateBussCode" />
    <result column="vehicle_operate_buss_name" jdbcType="VARCHAR" property="vehicleOperateBussName" />
    <result column="vehicle_operate_city_code" jdbcType="VARCHAR" property="vehicleOperateCityCode" />
    <result column="vehicle_operate_city_name" jdbcType="VARCHAR" property="vehicleOperateCityName" />
    <result column="vehicle_belong_buss_code" jdbcType="VARCHAR" property="vehicleBelongBussCode" />
    <result column="vehicle_belong_buss_name" jdbcType="VARCHAR" property="vehicleBelongBussName" />
    <result column="vehicle_belong_buss_city_code" jdbcType="VARCHAR" property="vehicleBelongBussCityCode" />
    <result column="vehicle_belong_buss_city_name" jdbcType="VARCHAR" property="vehicleBelongBussCityName" />
    <result column="lease_contract_code" jdbcType="VARCHAR" property="leaseContractCode" />
    <result column="lease_order_code" jdbcType="VARCHAR" property="leaseOrderCode" />
    <result column="advance_dispatch_car_apply_no" jdbcType="VARCHAR" property="advanceDispatchCarApplyNo" />
    <result column="vehicle_operate_account_city_code" jdbcType="VARCHAR" property="vehicleOperateAccountCityCode" />
    <result column="vehicle_operate_account_city_name" jdbcType="VARCHAR" property="vehicleOperateAccountCityName" />
    <result column="vehicle_belong_account_city_code" jdbcType="VARCHAR" property="vehicleBelongAccountCityCode" />
    <result column="vehicle_belong_account_city_name" jdbcType="VARCHAR" property="vehicleBelongAccountCityName" />
    <result column="power_limit" jdbcType="TINYINT" property="powerLimit" />
    <result column="extend_amount_1" jdbcType="DECIMAL" property="extendAmount1" />
    <result column="extend_amount_2" jdbcType="DECIMAL" property="extendAmount2" />
    <result column="extend_text_1" jdbcType="VARCHAR" property="extendText1" />
    <result column="extend_text_2" jdbcType="VARCHAR" property="extendText2" />
    <result column="extend_int_1" jdbcType="INTEGER" property="extendInt1" />
    <result column="extend_int_2" jdbcType="INTEGER" property="extendInt2" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="signing_entity_credit_code" jdbcType="VARCHAR" property="signingEntityCreditCode" />
    <result column="signing_entity_financial_code" jdbcType="VARCHAR" property="signingEntityFinancialCode" />
    <result column="signing_entity_financial_name" jdbcType="VARCHAR" property="signingEntityFinancialName" />
    <result column="sales_entity_credit_code" jdbcType="VARCHAR" property="salesEntityCreditCode" />
    <result column="sales_entity_financial_code" jdbcType="VARCHAR" property="salesEntityFinancialCode" />
    <result column="sales_entity_financial_name" jdbcType="VARCHAR" property="salesEntityFinancialName" />
    <result column="paid_service_type" jdbcType="TINYINT" property="paidServiceType" />
    <result column="ident_code" jdbcType="VARCHAR" property="identCode" />
    <result column="connector_id" jdbcType="VARCHAR" property="connectorId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    order_id, order_code, start_charge_seq, create_time, update_time, vehicle_id, vehicle_vin,
    vehicle_license, enter_way, vehicle_license_url, license_check_time, lease_order_owner_id, 
    lease_order_owner_name, charge_start_time, charge_end_time, order_end_time, order_status, 
    charge_status, end_reason, end_way, channel_id, channel_name, operator_id, operator_name, 
    charge_station_id, charge_station_name, station_city_code, station_city_name, equipment_id, 
    connector_id, connector_name, user_id, user_name, user_phone, company_id, company_name, 
    vehicle_operate_buss_code, vehicle_operate_buss_name, vehicle_operate_city_code, 
    vehicle_operate_city_name, vehicle_belong_buss_code, vehicle_belong_buss_name, vehicle_belong_buss_city_code, 
    vehicle_belong_buss_city_name, lease_contract_code, lease_order_code, advance_dispatch_car_apply_no, 
    vehicle_operate_account_city_code, vehicle_operate_account_city_name, vehicle_belong_account_city_code, 
    vehicle_belong_account_city_name, power_limit, extend_amount_1, extend_amount_2, 
    extend_text_1, extend_text_2, extend_int_1, extend_int_2, is_delete, signing_entity_credit_code, 
    signing_entity_financial_code, signing_entity_financial_name, sales_entity_credit_code, 
    sales_entity_financial_code, sales_entity_financial_name, paid_service_type, ident_code
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.ChargeOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from charge_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from charge_order
    where order_id = #{orderId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from charge_order
    where order_id = #{orderId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.order.entity.mrcar.ChargeOrderExample">
    delete from charge_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.ChargeOrder">
    <selectKey keyProperty="orderId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into charge_order (order_code, start_charge_seq, create_time, 
      update_time, vehicle_id, vehicle_vin, 
      vehicle_license, enter_way, vehicle_license_url, 
      license_check_time, lease_order_owner_id, 
      lease_order_owner_name, charge_start_time, 
      charge_end_time, order_end_time, order_status, 
      charge_status, end_reason, end_way, 
      channel_id, channel_name, operator_id, 
      operator_name, charge_station_id, charge_station_name, 
      station_city_code, station_city_name, equipment_id, 
      connector_id, connector_name, user_id, 
      user_name, user_phone, company_id, 
      company_name, vehicle_operate_buss_code, vehicle_operate_buss_name, 
      vehicle_operate_city_code, vehicle_operate_city_name, 
      vehicle_belong_buss_code, vehicle_belong_buss_name, 
      vehicle_belong_buss_city_code, vehicle_belong_buss_city_name, 
      lease_contract_code, lease_order_code, advance_dispatch_car_apply_no, 
      vehicle_operate_account_city_code, vehicle_operate_account_city_name, 
      vehicle_belong_account_city_code, vehicle_belong_account_city_name, 
      power_limit, extend_amount_1, extend_amount_2, 
      extend_text_1, extend_text_2, extend_int_1, 
      extend_int_2, is_delete, signing_entity_credit_code, 
      signing_entity_financial_code, signing_entity_financial_name, 
      sales_entity_credit_code, sales_entity_financial_code, 
      sales_entity_financial_name, paid_service_type, 
      ident_code)
    values (#{orderCode,jdbcType=VARCHAR}, #{startChargeSeq,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{vehicleId,jdbcType=BIGINT}, #{vehicleVin,jdbcType=VARCHAR}, 
      #{vehicleLicense,jdbcType=VARCHAR}, #{enterWay,jdbcType=TINYINT}, #{vehicleLicenseUrl,jdbcType=VARCHAR}, 
      #{licenseCheckTime,jdbcType=TIMESTAMP}, #{leaseOrderOwnerId,jdbcType=INTEGER}, 
      #{leaseOrderOwnerName,jdbcType=VARCHAR}, #{chargeStartTime,jdbcType=TIMESTAMP}, 
      #{chargeEndTime,jdbcType=TIMESTAMP}, #{orderEndTime,jdbcType=TIMESTAMP}, #{orderStatus,jdbcType=TINYINT}, 
      #{chargeStatus,jdbcType=TINYINT}, #{endReason,jdbcType=VARCHAR}, #{endWay,jdbcType=TINYINT}, 
      #{channelId,jdbcType=INTEGER}, #{channelName,jdbcType=VARCHAR}, #{operatorId,jdbcType=INTEGER}, 
      #{operatorName,jdbcType=VARCHAR}, #{chargeStationId,jdbcType=VARCHAR}, #{chargeStationName,jdbcType=VARCHAR}, 
      #{stationCityCode,jdbcType=VARCHAR}, #{stationCityName,jdbcType=VARCHAR}, #{equipmentId,jdbcType=VARCHAR}, 
      #{connectorId,jdbcType=VARCHAR}, #{connectorName,jdbcType=VARCHAR}, #{userId,jdbcType=INTEGER}, 
      #{userName,jdbcType=VARCHAR}, #{userPhone,jdbcType=VARCHAR}, #{companyId,jdbcType=INTEGER}, 
      #{companyName,jdbcType=VARCHAR}, #{vehicleOperateBussCode,jdbcType=VARCHAR}, #{vehicleOperateBussName,jdbcType=VARCHAR}, 
      #{vehicleOperateCityCode,jdbcType=VARCHAR}, #{vehicleOperateCityName,jdbcType=VARCHAR}, 
      #{vehicleBelongBussCode,jdbcType=VARCHAR}, #{vehicleBelongBussName,jdbcType=VARCHAR}, 
      #{vehicleBelongBussCityCode,jdbcType=VARCHAR}, #{vehicleBelongBussCityName,jdbcType=VARCHAR}, 
      #{leaseContractCode,jdbcType=VARCHAR}, #{leaseOrderCode,jdbcType=VARCHAR}, #{advanceDispatchCarApplyNo,jdbcType=VARCHAR}, 
      #{vehicleOperateAccountCityCode,jdbcType=VARCHAR}, #{vehicleOperateAccountCityName,jdbcType=VARCHAR}, 
      #{vehicleBelongAccountCityCode,jdbcType=VARCHAR}, #{vehicleBelongAccountCityName,jdbcType=VARCHAR}, 
      #{powerLimit,jdbcType=TINYINT}, #{extendAmount1,jdbcType=DECIMAL}, #{extendAmount2,jdbcType=DECIMAL}, 
      #{extendText1,jdbcType=VARCHAR}, #{extendText2,jdbcType=VARCHAR}, #{extendInt1,jdbcType=INTEGER}, 
      #{extendInt2,jdbcType=INTEGER}, #{isDelete,jdbcType=TINYINT}, #{signingEntityCreditCode,jdbcType=VARCHAR}, 
      #{signingEntityFinancialCode,jdbcType=VARCHAR}, #{signingEntityFinancialName,jdbcType=VARCHAR}, 
      #{salesEntityCreditCode,jdbcType=VARCHAR}, #{salesEntityFinancialCode,jdbcType=VARCHAR}, 
      #{salesEntityFinancialName,jdbcType=VARCHAR}, #{paidServiceType,jdbcType=TINYINT}, 
      #{identCode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.ChargeOrder">
    <selectKey keyProperty="orderId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into charge_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderCode != null">
        order_code,
      </if>
      <if test="startChargeSeq != null">
        start_charge_seq,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="vehicleVin != null">
        vehicle_vin,
      </if>
      <if test="vehicleLicense != null">
        vehicle_license,
      </if>
      <if test="enterWay != null">
        enter_way,
      </if>
      <if test="vehicleLicenseUrl != null">
        vehicle_license_url,
      </if>
      <if test="licenseCheckTime != null">
        license_check_time,
      </if>
      <if test="leaseOrderOwnerId != null">
        lease_order_owner_id,
      </if>
      <if test="leaseOrderOwnerName != null">
        lease_order_owner_name,
      </if>
      <if test="chargeStartTime != null">
        charge_start_time,
      </if>
      <if test="chargeEndTime != null">
        charge_end_time,
      </if>
      <if test="orderEndTime != null">
        order_end_time,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="chargeStatus != null">
        charge_status,
      </if>
      <if test="endReason != null">
        end_reason,
      </if>
      <if test="endWay != null">
        end_way,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
      <if test="channelName != null">
        channel_name,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="operatorName != null">
        operator_name,
      </if>
      <if test="chargeStationId != null">
        charge_station_id,
      </if>
      <if test="chargeStationName != null">
        charge_station_name,
      </if>
      <if test="stationCityCode != null">
        station_city_code,
      </if>
      <if test="stationCityName != null">
        station_city_name,
      </if>
      <if test="equipmentId != null">
        equipment_id,
      </if>
      <if test="connectorId != null">
        connector_id,
      </if>
      <if test="connectorName != null">
        connector_name,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="userPhone != null">
        user_phone,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="vehicleOperateBussCode != null">
        vehicle_operate_buss_code,
      </if>
      <if test="vehicleOperateBussName != null">
        vehicle_operate_buss_name,
      </if>
      <if test="vehicleOperateCityCode != null">
        vehicle_operate_city_code,
      </if>
      <if test="vehicleOperateCityName != null">
        vehicle_operate_city_name,
      </if>
      <if test="vehicleBelongBussCode != null">
        vehicle_belong_buss_code,
      </if>
      <if test="vehicleBelongBussName != null">
        vehicle_belong_buss_name,
      </if>
      <if test="vehicleBelongBussCityCode != null">
        vehicle_belong_buss_city_code,
      </if>
      <if test="vehicleBelongBussCityName != null">
        vehicle_belong_buss_city_name,
      </if>
      <if test="leaseContractCode != null">
        lease_contract_code,
      </if>
      <if test="leaseOrderCode != null">
        lease_order_code,
      </if>
      <if test="advanceDispatchCarApplyNo != null">
        advance_dispatch_car_apply_no,
      </if>
      <if test="vehicleOperateAccountCityCode != null">
        vehicle_operate_account_city_code,
      </if>
      <if test="vehicleOperateAccountCityName != null">
        vehicle_operate_account_city_name,
      </if>
      <if test="vehicleBelongAccountCityCode != null">
        vehicle_belong_account_city_code,
      </if>
      <if test="vehicleBelongAccountCityName != null">
        vehicle_belong_account_city_name,
      </if>
      <if test="powerLimit != null">
        power_limit,
      </if>
      <if test="extendAmount1 != null">
        extend_amount_1,
      </if>
      <if test="extendAmount2 != null">
        extend_amount_2,
      </if>
      <if test="extendText1 != null">
        extend_text_1,
      </if>
      <if test="extendText2 != null">
        extend_text_2,
      </if>
      <if test="extendInt1 != null">
        extend_int_1,
      </if>
      <if test="extendInt2 != null">
        extend_int_2,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="signingEntityCreditCode != null">
        signing_entity_credit_code,
      </if>
      <if test="signingEntityFinancialCode != null">
        signing_entity_financial_code,
      </if>
      <if test="signingEntityFinancialName != null">
        signing_entity_financial_name,
      </if>
      <if test="salesEntityCreditCode != null">
        sales_entity_credit_code,
      </if>
      <if test="salesEntityFinancialCode != null">
        sales_entity_financial_code,
      </if>
      <if test="salesEntityFinancialName != null">
        sales_entity_financial_name,
      </if>
      <if test="paidServiceType != null">
        paid_service_type,
      </if>
      <if test="identCode != null">
        ident_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderCode != null">
        #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="startChargeSeq != null">
        #{startChargeSeq,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleVin != null">
        #{vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="vehicleLicense != null">
        #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="enterWay != null">
        #{enterWay,jdbcType=TINYINT},
      </if>
      <if test="vehicleLicenseUrl != null">
        #{vehicleLicenseUrl,jdbcType=VARCHAR},
      </if>
      <if test="licenseCheckTime != null">
        #{licenseCheckTime,jdbcType=TIMESTAMP},
      </if>
      <if test="leaseOrderOwnerId != null">
        #{leaseOrderOwnerId,jdbcType=INTEGER},
      </if>
      <if test="leaseOrderOwnerName != null">
        #{leaseOrderOwnerName,jdbcType=VARCHAR},
      </if>
      <if test="chargeStartTime != null">
        #{chargeStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="chargeEndTime != null">
        #{chargeEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderEndTime != null">
        #{orderEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=TINYINT},
      </if>
      <if test="chargeStatus != null">
        #{chargeStatus,jdbcType=TINYINT},
      </if>
      <if test="endReason != null">
        #{endReason,jdbcType=VARCHAR},
      </if>
      <if test="endWay != null">
        #{endWay,jdbcType=TINYINT},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=INTEGER},
      </if>
      <if test="channelName != null">
        #{channelName,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=INTEGER},
      </if>
      <if test="operatorName != null">
        #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="chargeStationId != null">
        #{chargeStationId,jdbcType=VARCHAR},
      </if>
      <if test="chargeStationName != null">
        #{chargeStationName,jdbcType=VARCHAR},
      </if>
      <if test="stationCityCode != null">
        #{stationCityCode,jdbcType=VARCHAR},
      </if>
      <if test="stationCityName != null">
        #{stationCityName,jdbcType=VARCHAR},
      </if>
      <if test="equipmentId != null">
        #{equipmentId,jdbcType=VARCHAR},
      </if>
      <if test="connectorId != null">
        #{connectorId,jdbcType=VARCHAR},
      </if>
      <if test="connectorName != null">
        #{connectorName,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="userPhone != null">
        #{userPhone,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleOperateBussCode != null">
        #{vehicleOperateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleOperateBussName != null">
        #{vehicleOperateBussName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleOperateCityCode != null">
        #{vehicleOperateCityCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleOperateCityName != null">
        #{vehicleOperateCityName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBelongBussCode != null">
        #{vehicleBelongBussCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBelongBussName != null">
        #{vehicleBelongBussName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBelongBussCityCode != null">
        #{vehicleBelongBussCityCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBelongBussCityName != null">
        #{vehicleBelongBussCityName,jdbcType=VARCHAR},
      </if>
      <if test="leaseContractCode != null">
        #{leaseContractCode,jdbcType=VARCHAR},
      </if>
      <if test="leaseOrderCode != null">
        #{leaseOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="advanceDispatchCarApplyNo != null">
        #{advanceDispatchCarApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="vehicleOperateAccountCityCode != null">
        #{vehicleOperateAccountCityCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleOperateAccountCityName != null">
        #{vehicleOperateAccountCityName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBelongAccountCityCode != null">
        #{vehicleBelongAccountCityCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBelongAccountCityName != null">
        #{vehicleBelongAccountCityName,jdbcType=VARCHAR},
      </if>
      <if test="powerLimit != null">
        #{powerLimit,jdbcType=TINYINT},
      </if>
      <if test="extendAmount1 != null">
        #{extendAmount1,jdbcType=DECIMAL},
      </if>
      <if test="extendAmount2 != null">
        #{extendAmount2,jdbcType=DECIMAL},
      </if>
      <if test="extendText1 != null">
        #{extendText1,jdbcType=VARCHAR},
      </if>
      <if test="extendText2 != null">
        #{extendText2,jdbcType=VARCHAR},
      </if>
      <if test="extendInt1 != null">
        #{extendInt1,jdbcType=INTEGER},
      </if>
      <if test="extendInt2 != null">
        #{extendInt2,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="signingEntityCreditCode != null">
        #{signingEntityCreditCode,jdbcType=VARCHAR},
      </if>
      <if test="signingEntityFinancialCode != null">
        #{signingEntityFinancialCode,jdbcType=VARCHAR},
      </if>
      <if test="signingEntityFinancialName != null">
        #{signingEntityFinancialName,jdbcType=VARCHAR},
      </if>
      <if test="salesEntityCreditCode != null">
        #{salesEntityCreditCode,jdbcType=VARCHAR},
      </if>
      <if test="salesEntityFinancialCode != null">
        #{salesEntityFinancialCode,jdbcType=VARCHAR},
      </if>
      <if test="salesEntityFinancialName != null">
        #{salesEntityFinancialName,jdbcType=VARCHAR},
      </if>
      <if test="paidServiceType != null">
        #{paidServiceType,jdbcType=TINYINT},
      </if>
      <if test="identCode != null">
        #{identCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.ChargeOrderExample" resultType="java.lang.Long">
    select count(*) from charge_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update charge_order
    <set>
      <if test="row.orderId != null">
        order_id = #{row.orderId,jdbcType=INTEGER},
      </if>
      <if test="row.orderCode != null">
        order_code = #{row.orderCode,jdbcType=VARCHAR},
      </if>
      <if test="row.startChargeSeq != null">
        start_charge_seq = #{row.startChargeSeq,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.vehicleId != null">
        vehicle_id = #{row.vehicleId,jdbcType=BIGINT},
      </if>
      <if test="row.vehicleVin != null">
        vehicle_vin = #{row.vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleLicense != null">
        vehicle_license = #{row.vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="row.enterWay != null">
        enter_way = #{row.enterWay,jdbcType=TINYINT},
      </if>
      <if test="row.vehicleLicenseUrl != null">
        vehicle_license_url = #{row.vehicleLicenseUrl,jdbcType=VARCHAR},
      </if>
      <if test="row.licenseCheckTime != null">
        license_check_time = #{row.licenseCheckTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.leaseOrderOwnerId != null">
        lease_order_owner_id = #{row.leaseOrderOwnerId,jdbcType=INTEGER},
      </if>
      <if test="row.leaseOrderOwnerName != null">
        lease_order_owner_name = #{row.leaseOrderOwnerName,jdbcType=VARCHAR},
      </if>
      <if test="row.chargeStartTime != null">
        charge_start_time = #{row.chargeStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.chargeEndTime != null">
        charge_end_time = #{row.chargeEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.orderEndTime != null">
        order_end_time = #{row.orderEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.orderStatus != null">
        order_status = #{row.orderStatus,jdbcType=TINYINT},
      </if>
      <if test="row.chargeStatus != null">
        charge_status = #{row.chargeStatus,jdbcType=TINYINT},
      </if>
      <if test="row.endReason != null">
        end_reason = #{row.endReason,jdbcType=VARCHAR},
      </if>
      <if test="row.endWay != null">
        end_way = #{row.endWay,jdbcType=TINYINT},
      </if>
      <if test="row.channelId != null">
        channel_id = #{row.channelId,jdbcType=INTEGER},
      </if>
      <if test="row.channelName != null">
        channel_name = #{row.channelName,jdbcType=VARCHAR},
      </if>
      <if test="row.operatorId != null">
        operator_id = #{row.operatorId,jdbcType=INTEGER},
      </if>
      <if test="row.operatorName != null">
        operator_name = #{row.operatorName,jdbcType=VARCHAR},
      </if>
      <if test="row.chargeStationId != null">
        charge_station_id = #{row.chargeStationId,jdbcType=VARCHAR},
      </if>
      <if test="row.chargeStationName != null">
        charge_station_name = #{row.chargeStationName,jdbcType=VARCHAR},
      </if>
      <if test="row.stationCityCode != null">
        station_city_code = #{row.stationCityCode,jdbcType=VARCHAR},
      </if>
      <if test="row.stationCityName != null">
        station_city_name = #{row.stationCityName,jdbcType=VARCHAR},
      </if>
      <if test="row.equipmentId != null">
        equipment_id = #{row.equipmentId,jdbcType=VARCHAR},
      </if>
      <if test="row.connectorId != null">
        connector_id = #{row.connectorId,jdbcType=VARCHAR},
      </if>
      <if test="row.connectorName != null">
        connector_name = #{row.connectorName,jdbcType=VARCHAR},
      </if>
      <if test="row.userId != null">
        user_id = #{row.userId,jdbcType=INTEGER},
      </if>
      <if test="row.userName != null">
        user_name = #{row.userName,jdbcType=VARCHAR},
      </if>
      <if test="row.userPhone != null">
        user_phone = #{row.userPhone,jdbcType=VARCHAR},
      </if>
      <if test="row.companyId != null">
        company_id = #{row.companyId,jdbcType=INTEGER},
      </if>
      <if test="row.companyName != null">
        company_name = #{row.companyName,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleOperateBussCode != null">
        vehicle_operate_buss_code = #{row.vehicleOperateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleOperateBussName != null">
        vehicle_operate_buss_name = #{row.vehicleOperateBussName,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleOperateCityCode != null">
        vehicle_operate_city_code = #{row.vehicleOperateCityCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleOperateCityName != null">
        vehicle_operate_city_name = #{row.vehicleOperateCityName,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleBelongBussCode != null">
        vehicle_belong_buss_code = #{row.vehicleBelongBussCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleBelongBussName != null">
        vehicle_belong_buss_name = #{row.vehicleBelongBussName,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleBelongBussCityCode != null">
        vehicle_belong_buss_city_code = #{row.vehicleBelongBussCityCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleBelongBussCityName != null">
        vehicle_belong_buss_city_name = #{row.vehicleBelongBussCityName,jdbcType=VARCHAR},
      </if>
      <if test="row.leaseContractCode != null">
        lease_contract_code = #{row.leaseContractCode,jdbcType=VARCHAR},
      </if>
      <if test="row.leaseOrderCode != null">
        lease_order_code = #{row.leaseOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="row.advanceDispatchCarApplyNo != null">
        advance_dispatch_car_apply_no = #{row.advanceDispatchCarApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleOperateAccountCityCode != null">
        vehicle_operate_account_city_code = #{row.vehicleOperateAccountCityCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleOperateAccountCityName != null">
        vehicle_operate_account_city_name = #{row.vehicleOperateAccountCityName,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleBelongAccountCityCode != null">
        vehicle_belong_account_city_code = #{row.vehicleBelongAccountCityCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleBelongAccountCityName != null">
        vehicle_belong_account_city_name = #{row.vehicleBelongAccountCityName,jdbcType=VARCHAR},
      </if>
      <if test="row.powerLimit != null">
        power_limit = #{row.powerLimit,jdbcType=TINYINT},
      </if>
      <if test="row.extendAmount1 != null">
        extend_amount_1 = #{row.extendAmount1,jdbcType=DECIMAL},
      </if>
      <if test="row.extendAmount2 != null">
        extend_amount_2 = #{row.extendAmount2,jdbcType=DECIMAL},
      </if>
      <if test="row.extendText1 != null">
        extend_text_1 = #{row.extendText1,jdbcType=VARCHAR},
      </if>
      <if test="row.extendText2 != null">
        extend_text_2 = #{row.extendText2,jdbcType=VARCHAR},
      </if>
      <if test="row.extendInt1 != null">
        extend_int_1 = #{row.extendInt1,jdbcType=INTEGER},
      </if>
      <if test="row.extendInt2 != null">
        extend_int_2 = #{row.extendInt2,jdbcType=INTEGER},
      </if>
      <if test="row.isDelete != null">
        is_delete = #{row.isDelete,jdbcType=TINYINT},
      </if>
      <if test="row.signingEntityCreditCode != null">
        signing_entity_credit_code = #{row.signingEntityCreditCode,jdbcType=VARCHAR},
      </if>
      <if test="row.signingEntityFinancialCode != null">
        signing_entity_financial_code = #{row.signingEntityFinancialCode,jdbcType=VARCHAR},
      </if>
      <if test="row.signingEntityFinancialName != null">
        signing_entity_financial_name = #{row.signingEntityFinancialName,jdbcType=VARCHAR},
      </if>
      <if test="row.salesEntityCreditCode != null">
        sales_entity_credit_code = #{row.salesEntityCreditCode,jdbcType=VARCHAR},
      </if>
      <if test="row.salesEntityFinancialCode != null">
        sales_entity_financial_code = #{row.salesEntityFinancialCode,jdbcType=VARCHAR},
      </if>
      <if test="row.salesEntityFinancialName != null">
        sales_entity_financial_name = #{row.salesEntityFinancialName,jdbcType=VARCHAR},
      </if>
      <if test="row.paidServiceType != null">
        paid_service_type = #{row.paidServiceType,jdbcType=TINYINT},
      </if>
      <if test="row.identCode != null">
        ident_code = #{row.identCode,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update charge_order
    set order_id = #{row.orderId,jdbcType=INTEGER},
      order_code = #{row.orderCode,jdbcType=VARCHAR},
      start_charge_seq = #{row.startChargeSeq,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      vehicle_id = #{row.vehicleId,jdbcType=BIGINT},
      vehicle_vin = #{row.vehicleVin,jdbcType=VARCHAR},
      vehicle_license = #{row.vehicleLicense,jdbcType=VARCHAR},
      enter_way = #{row.enterWay,jdbcType=TINYINT},
      vehicle_license_url = #{row.vehicleLicenseUrl,jdbcType=VARCHAR},
      license_check_time = #{row.licenseCheckTime,jdbcType=TIMESTAMP},
      lease_order_owner_id = #{row.leaseOrderOwnerId,jdbcType=INTEGER},
      lease_order_owner_name = #{row.leaseOrderOwnerName,jdbcType=VARCHAR},
      charge_start_time = #{row.chargeStartTime,jdbcType=TIMESTAMP},
      charge_end_time = #{row.chargeEndTime,jdbcType=TIMESTAMP},
      order_end_time = #{row.orderEndTime,jdbcType=TIMESTAMP},
      order_status = #{row.orderStatus,jdbcType=TINYINT},
      charge_status = #{row.chargeStatus,jdbcType=TINYINT},
      end_reason = #{row.endReason,jdbcType=VARCHAR},
      end_way = #{row.endWay,jdbcType=TINYINT},
      channel_id = #{row.channelId,jdbcType=INTEGER},
      channel_name = #{row.channelName,jdbcType=VARCHAR},
      operator_id = #{row.operatorId,jdbcType=INTEGER},
      operator_name = #{row.operatorName,jdbcType=VARCHAR},
      charge_station_id = #{row.chargeStationId,jdbcType=VARCHAR},
      charge_station_name = #{row.chargeStationName,jdbcType=VARCHAR},
      station_city_code = #{row.stationCityCode,jdbcType=VARCHAR},
      station_city_name = #{row.stationCityName,jdbcType=VARCHAR},
      equipment_id = #{row.equipmentId,jdbcType=VARCHAR},
      connector_id = #{row.connectorId,jdbcType=VARCHAR},
      connector_name = #{row.connectorName,jdbcType=VARCHAR},
      user_id = #{row.userId,jdbcType=INTEGER},
      user_name = #{row.userName,jdbcType=VARCHAR},
      user_phone = #{row.userPhone,jdbcType=VARCHAR},
      company_id = #{row.companyId,jdbcType=INTEGER},
      company_name = #{row.companyName,jdbcType=VARCHAR},
      vehicle_operate_buss_code = #{row.vehicleOperateBussCode,jdbcType=VARCHAR},
      vehicle_operate_buss_name = #{row.vehicleOperateBussName,jdbcType=VARCHAR},
      vehicle_operate_city_code = #{row.vehicleOperateCityCode,jdbcType=VARCHAR},
      vehicle_operate_city_name = #{row.vehicleOperateCityName,jdbcType=VARCHAR},
      vehicle_belong_buss_code = #{row.vehicleBelongBussCode,jdbcType=VARCHAR},
      vehicle_belong_buss_name = #{row.vehicleBelongBussName,jdbcType=VARCHAR},
      vehicle_belong_buss_city_code = #{row.vehicleBelongBussCityCode,jdbcType=VARCHAR},
      vehicle_belong_buss_city_name = #{row.vehicleBelongBussCityName,jdbcType=VARCHAR},
      lease_contract_code = #{row.leaseContractCode,jdbcType=VARCHAR},
      lease_order_code = #{row.leaseOrderCode,jdbcType=VARCHAR},
      advance_dispatch_car_apply_no = #{row.advanceDispatchCarApplyNo,jdbcType=VARCHAR},
      vehicle_operate_account_city_code = #{row.vehicleOperateAccountCityCode,jdbcType=VARCHAR},
      vehicle_operate_account_city_name = #{row.vehicleOperateAccountCityName,jdbcType=VARCHAR},
      vehicle_belong_account_city_code = #{row.vehicleBelongAccountCityCode,jdbcType=VARCHAR},
      vehicle_belong_account_city_name = #{row.vehicleBelongAccountCityName,jdbcType=VARCHAR},
      power_limit = #{row.powerLimit,jdbcType=TINYINT},
      extend_amount_1 = #{row.extendAmount1,jdbcType=DECIMAL},
      extend_amount_2 = #{row.extendAmount2,jdbcType=DECIMAL},
      extend_text_1 = #{row.extendText1,jdbcType=VARCHAR},
      extend_text_2 = #{row.extendText2,jdbcType=VARCHAR},
      extend_int_1 = #{row.extendInt1,jdbcType=INTEGER},
      extend_int_2 = #{row.extendInt2,jdbcType=INTEGER},
      is_delete = #{row.isDelete,jdbcType=TINYINT},
      signing_entity_credit_code = #{row.signingEntityCreditCode,jdbcType=VARCHAR},
      signing_entity_financial_code = #{row.signingEntityFinancialCode,jdbcType=VARCHAR},
      signing_entity_financial_name = #{row.signingEntityFinancialName,jdbcType=VARCHAR},
      sales_entity_credit_code = #{row.salesEntityCreditCode,jdbcType=VARCHAR},
      sales_entity_financial_code = #{row.salesEntityFinancialCode,jdbcType=VARCHAR},
      sales_entity_financial_name = #{row.salesEntityFinancialName,jdbcType=VARCHAR},
      paid_service_type = #{row.paidServiceType,jdbcType=TINYINT},
      ident_code = #{row.identCode,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.ChargeOrder">
    update charge_order
    <set>
      <if test="orderCode != null">
        order_code = #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="startChargeSeq != null">
        start_charge_seq = #{startChargeSeq,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleVin != null">
        vehicle_vin = #{vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="vehicleLicense != null">
        vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="enterWay != null">
        enter_way = #{enterWay,jdbcType=TINYINT},
      </if>
      <if test="vehicleLicenseUrl != null">
        vehicle_license_url = #{vehicleLicenseUrl,jdbcType=VARCHAR},
      </if>
      <if test="licenseCheckTime != null">
        license_check_time = #{licenseCheckTime,jdbcType=TIMESTAMP},
      </if>
      <if test="leaseOrderOwnerId != null">
        lease_order_owner_id = #{leaseOrderOwnerId,jdbcType=INTEGER},
      </if>
      <if test="leaseOrderOwnerName != null">
        lease_order_owner_name = #{leaseOrderOwnerName,jdbcType=VARCHAR},
      </if>
      <if test="chargeStartTime != null">
        charge_start_time = #{chargeStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="chargeEndTime != null">
        charge_end_time = #{chargeEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderEndTime != null">
        order_end_time = #{orderEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=TINYINT},
      </if>
      <if test="chargeStatus != null">
        charge_status = #{chargeStatus,jdbcType=TINYINT},
      </if>
      <if test="endReason != null">
        end_reason = #{endReason,jdbcType=VARCHAR},
      </if>
      <if test="endWay != null">
        end_way = #{endWay,jdbcType=TINYINT},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=INTEGER},
      </if>
      <if test="channelName != null">
        channel_name = #{channelName,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=INTEGER},
      </if>
      <if test="operatorName != null">
        operator_name = #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="chargeStationId != null">
        charge_station_id = #{chargeStationId,jdbcType=VARCHAR},
      </if>
      <if test="chargeStationName != null">
        charge_station_name = #{chargeStationName,jdbcType=VARCHAR},
      </if>
      <if test="stationCityCode != null">
        station_city_code = #{stationCityCode,jdbcType=VARCHAR},
      </if>
      <if test="stationCityName != null">
        station_city_name = #{stationCityName,jdbcType=VARCHAR},
      </if>
      <if test="equipmentId != null">
        equipment_id = #{equipmentId,jdbcType=VARCHAR},
      </if>
      <if test="connectorId != null">
        connector_id = #{connectorId,jdbcType=VARCHAR},
      </if>
      <if test="connectorName != null">
        connector_name = #{connectorName,jdbcType=VARCHAR},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="userName != null">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="userPhone != null">
        user_phone = #{userPhone,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleOperateBussCode != null">
        vehicle_operate_buss_code = #{vehicleOperateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleOperateBussName != null">
        vehicle_operate_buss_name = #{vehicleOperateBussName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleOperateCityCode != null">
        vehicle_operate_city_code = #{vehicleOperateCityCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleOperateCityName != null">
        vehicle_operate_city_name = #{vehicleOperateCityName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBelongBussCode != null">
        vehicle_belong_buss_code = #{vehicleBelongBussCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBelongBussName != null">
        vehicle_belong_buss_name = #{vehicleBelongBussName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBelongBussCityCode != null">
        vehicle_belong_buss_city_code = #{vehicleBelongBussCityCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBelongBussCityName != null">
        vehicle_belong_buss_city_name = #{vehicleBelongBussCityName,jdbcType=VARCHAR},
      </if>
      <if test="leaseContractCode != null">
        lease_contract_code = #{leaseContractCode,jdbcType=VARCHAR},
      </if>
      <if test="leaseOrderCode != null">
        lease_order_code = #{leaseOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="advanceDispatchCarApplyNo != null">
        advance_dispatch_car_apply_no = #{advanceDispatchCarApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="vehicleOperateAccountCityCode != null">
        vehicle_operate_account_city_code = #{vehicleOperateAccountCityCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleOperateAccountCityName != null">
        vehicle_operate_account_city_name = #{vehicleOperateAccountCityName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBelongAccountCityCode != null">
        vehicle_belong_account_city_code = #{vehicleBelongAccountCityCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBelongAccountCityName != null">
        vehicle_belong_account_city_name = #{vehicleBelongAccountCityName,jdbcType=VARCHAR},
      </if>
      <if test="powerLimit != null">
        power_limit = #{powerLimit,jdbcType=TINYINT},
      </if>
      <if test="extendAmount1 != null">
        extend_amount_1 = #{extendAmount1,jdbcType=DECIMAL},
      </if>
      <if test="extendAmount2 != null">
        extend_amount_2 = #{extendAmount2,jdbcType=DECIMAL},
      </if>
      <if test="extendText1 != null">
        extend_text_1 = #{extendText1,jdbcType=VARCHAR},
      </if>
      <if test="extendText2 != null">
        extend_text_2 = #{extendText2,jdbcType=VARCHAR},
      </if>
      <if test="extendInt1 != null">
        extend_int_1 = #{extendInt1,jdbcType=INTEGER},
      </if>
      <if test="extendInt2 != null">
        extend_int_2 = #{extendInt2,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="signingEntityCreditCode != null">
        signing_entity_credit_code = #{signingEntityCreditCode,jdbcType=VARCHAR},
      </if>
      <if test="signingEntityFinancialCode != null">
        signing_entity_financial_code = #{signingEntityFinancialCode,jdbcType=VARCHAR},
      </if>
      <if test="signingEntityFinancialName != null">
        signing_entity_financial_name = #{signingEntityFinancialName,jdbcType=VARCHAR},
      </if>
      <if test="salesEntityCreditCode != null">
        sales_entity_credit_code = #{salesEntityCreditCode,jdbcType=VARCHAR},
      </if>
      <if test="salesEntityFinancialCode != null">
        sales_entity_financial_code = #{salesEntityFinancialCode,jdbcType=VARCHAR},
      </if>
      <if test="salesEntityFinancialName != null">
        sales_entity_financial_name = #{salesEntityFinancialName,jdbcType=VARCHAR},
      </if>
      <if test="paidServiceType != null">
        paid_service_type = #{paidServiceType,jdbcType=TINYINT},
      </if>
      <if test="identCode != null">
        ident_code = #{identCode,jdbcType=VARCHAR},
      </if>
    </set>
    where order_id = #{orderId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.ChargeOrder">
    update charge_order
    set order_code = #{orderCode,jdbcType=VARCHAR},
      start_charge_seq = #{startChargeSeq,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      vehicle_vin = #{vehicleVin,jdbcType=VARCHAR},
      vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      enter_way = #{enterWay,jdbcType=TINYINT},
      vehicle_license_url = #{vehicleLicenseUrl,jdbcType=VARCHAR},
      license_check_time = #{licenseCheckTime,jdbcType=TIMESTAMP},
      lease_order_owner_id = #{leaseOrderOwnerId,jdbcType=INTEGER},
      lease_order_owner_name = #{leaseOrderOwnerName,jdbcType=VARCHAR},
      charge_start_time = #{chargeStartTime,jdbcType=TIMESTAMP},
      charge_end_time = #{chargeEndTime,jdbcType=TIMESTAMP},
      order_end_time = #{orderEndTime,jdbcType=TIMESTAMP},
      order_status = #{orderStatus,jdbcType=TINYINT},
      charge_status = #{chargeStatus,jdbcType=TINYINT},
      end_reason = #{endReason,jdbcType=VARCHAR},
      end_way = #{endWay,jdbcType=TINYINT},
      channel_id = #{channelId,jdbcType=INTEGER},
      channel_name = #{channelName,jdbcType=VARCHAR},
      operator_id = #{operatorId,jdbcType=INTEGER},
      operator_name = #{operatorName,jdbcType=VARCHAR},
      charge_station_id = #{chargeStationId,jdbcType=VARCHAR},
      charge_station_name = #{chargeStationName,jdbcType=VARCHAR},
      station_city_code = #{stationCityCode,jdbcType=VARCHAR},
      station_city_name = #{stationCityName,jdbcType=VARCHAR},
      equipment_id = #{equipmentId,jdbcType=VARCHAR},
      connector_id = #{connectorId,jdbcType=VARCHAR},
      connector_name = #{connectorName,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=INTEGER},
      user_name = #{userName,jdbcType=VARCHAR},
      user_phone = #{userPhone,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=INTEGER},
      company_name = #{companyName,jdbcType=VARCHAR},
      vehicle_operate_buss_code = #{vehicleOperateBussCode,jdbcType=VARCHAR},
      vehicle_operate_buss_name = #{vehicleOperateBussName,jdbcType=VARCHAR},
      vehicle_operate_city_code = #{vehicleOperateCityCode,jdbcType=VARCHAR},
      vehicle_operate_city_name = #{vehicleOperateCityName,jdbcType=VARCHAR},
      vehicle_belong_buss_code = #{vehicleBelongBussCode,jdbcType=VARCHAR},
      vehicle_belong_buss_name = #{vehicleBelongBussName,jdbcType=VARCHAR},
      vehicle_belong_buss_city_code = #{vehicleBelongBussCityCode,jdbcType=VARCHAR},
      vehicle_belong_buss_city_name = #{vehicleBelongBussCityName,jdbcType=VARCHAR},
      lease_contract_code = #{leaseContractCode,jdbcType=VARCHAR},
      lease_order_code = #{leaseOrderCode,jdbcType=VARCHAR},
      advance_dispatch_car_apply_no = #{advanceDispatchCarApplyNo,jdbcType=VARCHAR},
      vehicle_operate_account_city_code = #{vehicleOperateAccountCityCode,jdbcType=VARCHAR},
      vehicle_operate_account_city_name = #{vehicleOperateAccountCityName,jdbcType=VARCHAR},
      vehicle_belong_account_city_code = #{vehicleBelongAccountCityCode,jdbcType=VARCHAR},
      vehicle_belong_account_city_name = #{vehicleBelongAccountCityName,jdbcType=VARCHAR},
      power_limit = #{powerLimit,jdbcType=TINYINT},
      extend_amount_1 = #{extendAmount1,jdbcType=DECIMAL},
      extend_amount_2 = #{extendAmount2,jdbcType=DECIMAL},
      extend_text_1 = #{extendText1,jdbcType=VARCHAR},
      extend_text_2 = #{extendText2,jdbcType=VARCHAR},
      extend_int_1 = #{extendInt1,jdbcType=INTEGER},
      extend_int_2 = #{extendInt2,jdbcType=INTEGER},
      is_delete = #{isDelete,jdbcType=TINYINT},
      signing_entity_credit_code = #{signingEntityCreditCode,jdbcType=VARCHAR},
      signing_entity_financial_code = #{signingEntityFinancialCode,jdbcType=VARCHAR},
      signing_entity_financial_name = #{signingEntityFinancialName,jdbcType=VARCHAR},
      sales_entity_credit_code = #{salesEntityCreditCode,jdbcType=VARCHAR},
      sales_entity_financial_code = #{salesEntityFinancialCode,jdbcType=VARCHAR},
      sales_entity_financial_name = #{salesEntityFinancialName,jdbcType=VARCHAR},
      paid_service_type = #{paidServiceType,jdbcType=TINYINT},
      ident_code = #{identCode,jdbcType=VARCHAR}
    where order_id = #{orderId,jdbcType=INTEGER}
  </update>
</mapper>