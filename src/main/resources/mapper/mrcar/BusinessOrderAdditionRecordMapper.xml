<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.BusinessOrderAdditionRecordMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.BusinessOrderAdditionRecord">
    <id column="record_id" jdbcType="INTEGER" property="recordId" />
    <result column="order_code" jdbcType="VARCHAR" property="orderCode" />
    <result column="order_status" jdbcType="INTEGER" property="orderStatus" />
    <result column="customer_code" jdbcType="VARCHAR" property="customerCode" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="our_social_credit_code" jdbcType="VARCHAR" property="ourSocialCreditCode" />
    <result column="our_contracting_entity" jdbcType="VARCHAR" property="ourContractingEntity" />
    <result column="vehicle_license" jdbcType="VARCHAR" property="vehicleLicense" />
    <result column="vehicle_model_name" jdbcType="VARCHAR" property="vehicleModelName" />
    <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode" />
    <result column="vehicle_operate_buss_code" jdbcType="VARCHAR" property="vehicleOperateBussCode" />
    <result column="vehicle_operate_city_code" jdbcType="VARCHAR" property="vehicleOperateCityCode" />
    <result column="vehicle_operate_city_name" jdbcType="VARCHAR" property="vehicleOperateCityName" />
    <result column="vehicle_operate_buss_name" jdbcType="VARCHAR" property="vehicleOperateBussName" />
    <result column="vehicle_belong_buss_code" jdbcType="VARCHAR" property="vehicleBelongBussCode" />
    <result column="vehicle_belong_buss_name" jdbcType="VARCHAR" property="vehicleBelongBussName" />
    <result column="vehicle_self_owned" jdbcType="TINYINT" property="vehicleSelfOwned" />
    <result column="driver_name" jdbcType="VARCHAR" property="driverName" />
    <result column="driver_mobile" jdbcType="VARCHAR" property="driverMobile" />
    <result column="start_date" jdbcType="TIMESTAMP" property="startDate" />
    <result column="end_date" jdbcType="TIMESTAMP" property="endDate" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="tax_rate" jdbcType="DECIMAL" property="taxRate" />
    <result column="payment_days" jdbcType="INTEGER" property="paymentDays" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="supplier_payable_amount" jdbcType="DECIMAL" property="supplierPayableAmount" />
    <result column="vehicle_provider" jdbcType="TINYINT" property="vehicleProvider" />
    <result column="driver_provider" jdbcType="TINYINT" property="driverProvider" />
    <result column="create_code" jdbcType="VARCHAR" property="createCode" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_struct_code" jdbcType="VARCHAR" property="createStructCode" />
    <result column="create_struct_name" jdbcType="VARCHAR" property="createStructName" />
    <result column="update_code" jdbcType="VARCHAR" property="updateCode" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="supplier_rate" jdbcType="DECIMAL" property="supplierRate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    record_id, order_code, order_status, customer_code, customer_name, our_social_credit_code, 
    our_contracting_entity, vehicle_license, vehicle_model_name, vehicle_model_code, 
    vehicle_operate_buss_code, vehicle_operate_city_code, vehicle_operate_city_name, 
    vehicle_operate_buss_name, vehicle_belong_buss_code, vehicle_belong_buss_name, vehicle_self_owned, 
    driver_name, driver_mobile, start_date, end_date, amount, tax_rate, payment_days, 
    remark, supplier_code, supplier_name, supplier_payable_amount, vehicle_provider, 
    driver_provider, create_code, create_name, create_struct_code, create_struct_name, 
    update_code, update_name, create_time, update_time, supplier_rate
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.BusinessOrderAdditionRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from business_order_addition_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from business_order_addition_record
    where record_id = #{recordId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from business_order_addition_record
    where record_id = #{recordId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.order.entity.mrcar.BusinessOrderAdditionRecordExample">
    delete from business_order_addition_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.BusinessOrderAdditionRecord">
    insert into business_order_addition_record (record_id, order_code, order_status, 
      customer_code, customer_name, our_social_credit_code, 
      our_contracting_entity, vehicle_license, vehicle_model_name, 
      vehicle_model_code, vehicle_operate_buss_code, 
      vehicle_operate_city_code, vehicle_operate_city_name, 
      vehicle_operate_buss_name, vehicle_belong_buss_code, 
      vehicle_belong_buss_name, vehicle_self_owned, 
      driver_name, driver_mobile, start_date, 
      end_date, amount, tax_rate, 
      payment_days, remark, supplier_code, 
      supplier_name, supplier_payable_amount, vehicle_provider, 
      driver_provider, create_code, create_name, 
      create_struct_code, create_struct_name, update_code, 
      update_name, create_time, update_time, 
      supplier_rate)
    values (#{recordId,jdbcType=INTEGER}, #{orderCode,jdbcType=VARCHAR}, #{orderStatus,jdbcType=INTEGER}, 
      #{customerCode,jdbcType=VARCHAR}, #{customerName,jdbcType=VARCHAR}, #{ourSocialCreditCode,jdbcType=VARCHAR}, 
      #{ourContractingEntity,jdbcType=VARCHAR}, #{vehicleLicense,jdbcType=VARCHAR}, #{vehicleModelName,jdbcType=VARCHAR}, 
      #{vehicleModelCode,jdbcType=VARCHAR}, #{vehicleOperateBussCode,jdbcType=VARCHAR}, 
      #{vehicleOperateCityCode,jdbcType=VARCHAR}, #{vehicleOperateCityName,jdbcType=VARCHAR}, 
      #{vehicleOperateBussName,jdbcType=VARCHAR}, #{vehicleBelongBussCode,jdbcType=VARCHAR}, 
      #{vehicleBelongBussName,jdbcType=VARCHAR}, #{vehicleSelfOwned,jdbcType=TINYINT}, 
      #{driverName,jdbcType=VARCHAR}, #{driverMobile,jdbcType=VARCHAR}, #{startDate,jdbcType=TIMESTAMP}, 
      #{endDate,jdbcType=TIMESTAMP}, #{amount,jdbcType=DECIMAL}, #{taxRate,jdbcType=DECIMAL}, 
      #{paymentDays,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}, #{supplierCode,jdbcType=VARCHAR}, 
      #{supplierName,jdbcType=VARCHAR}, #{supplierPayableAmount,jdbcType=DECIMAL}, #{vehicleProvider,jdbcType=TINYINT}, 
      #{driverProvider,jdbcType=TINYINT}, #{createCode,jdbcType=VARCHAR}, #{createName,jdbcType=VARCHAR}, 
      #{createStructCode,jdbcType=VARCHAR}, #{createStructName,jdbcType=VARCHAR}, #{updateCode,jdbcType=VARCHAR}, 
      #{updateName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{supplierRate,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.BusinessOrderAdditionRecord">
    insert into business_order_addition_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="recordId != null">
        record_id,
      </if>
      <if test="orderCode != null">
        order_code,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="customerCode != null">
        customer_code,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="ourSocialCreditCode != null">
        our_social_credit_code,
      </if>
      <if test="ourContractingEntity != null">
        our_contracting_entity,
      </if>
      <if test="vehicleLicense != null">
        vehicle_license,
      </if>
      <if test="vehicleModelName != null">
        vehicle_model_name,
      </if>
      <if test="vehicleModelCode != null">
        vehicle_model_code,
      </if>
      <if test="vehicleOperateBussCode != null">
        vehicle_operate_buss_code,
      </if>
      <if test="vehicleOperateCityCode != null">
        vehicle_operate_city_code,
      </if>
      <if test="vehicleOperateCityName != null">
        vehicle_operate_city_name,
      </if>
      <if test="vehicleOperateBussName != null">
        vehicle_operate_buss_name,
      </if>
      <if test="vehicleBelongBussCode != null">
        vehicle_belong_buss_code,
      </if>
      <if test="vehicleBelongBussName != null">
        vehicle_belong_buss_name,
      </if>
      <if test="vehicleSelfOwned != null">
        vehicle_self_owned,
      </if>
      <if test="driverName != null">
        driver_name,
      </if>
      <if test="driverMobile != null">
        driver_mobile,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="taxRate != null">
        tax_rate,
      </if>
      <if test="paymentDays != null">
        payment_days,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="supplierCode != null">
        supplier_code,
      </if>
      <if test="supplierName != null">
        supplier_name,
      </if>
      <if test="supplierPayableAmount != null">
        supplier_payable_amount,
      </if>
      <if test="vehicleProvider != null">
        vehicle_provider,
      </if>
      <if test="driverProvider != null">
        driver_provider,
      </if>
      <if test="createCode != null">
        create_code,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createStructCode != null">
        create_struct_code,
      </if>
      <if test="createStructName != null">
        create_struct_name,
      </if>
      <if test="updateCode != null">
        update_code,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="supplierRate != null">
        supplier_rate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="recordId != null">
        #{recordId,jdbcType=INTEGER},
      </if>
      <if test="orderCode != null">
        #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="customerCode != null">
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="ourSocialCreditCode != null">
        #{ourSocialCreditCode,jdbcType=VARCHAR},
      </if>
      <if test="ourContractingEntity != null">
        #{ourContractingEntity,jdbcType=VARCHAR},
      </if>
      <if test="vehicleLicense != null">
        #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelName != null">
        #{vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelCode != null">
        #{vehicleModelCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleOperateBussCode != null">
        #{vehicleOperateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleOperateCityCode != null">
        #{vehicleOperateCityCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleOperateCityName != null">
        #{vehicleOperateCityName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleOperateBussName != null">
        #{vehicleOperateBussName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBelongBussCode != null">
        #{vehicleBelongBussCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBelongBussName != null">
        #{vehicleBelongBussName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleSelfOwned != null">
        #{vehicleSelfOwned,jdbcType=TINYINT},
      </if>
      <if test="driverName != null">
        #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="driverMobile != null">
        #{driverMobile,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="taxRate != null">
        #{taxRate,jdbcType=DECIMAL},
      </if>
      <if test="paymentDays != null">
        #{paymentDays,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="supplierCode != null">
        #{supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="supplierPayableAmount != null">
        #{supplierPayableAmount,jdbcType=DECIMAL},
      </if>
      <if test="vehicleProvider != null">
        #{vehicleProvider,jdbcType=TINYINT},
      </if>
      <if test="driverProvider != null">
        #{driverProvider,jdbcType=TINYINT},
      </if>
      <if test="createCode != null">
        #{createCode,jdbcType=VARCHAR},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createStructCode != null">
        #{createStructCode,jdbcType=VARCHAR},
      </if>
      <if test="createStructName != null">
        #{createStructName,jdbcType=VARCHAR},
      </if>
      <if test="updateCode != null">
        #{updateCode,jdbcType=VARCHAR},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="supplierRate != null">
        #{supplierRate,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.BusinessOrderAdditionRecordExample" resultType="java.lang.Long">
    select count(*) from business_order_addition_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update business_order_addition_record
    <set>
      <if test="row.recordId != null">
        record_id = #{row.recordId,jdbcType=INTEGER},
      </if>
      <if test="row.orderCode != null">
        order_code = #{row.orderCode,jdbcType=VARCHAR},
      </if>
      <if test="row.orderStatus != null">
        order_status = #{row.orderStatus,jdbcType=INTEGER},
      </if>
      <if test="row.customerCode != null">
        customer_code = #{row.customerCode,jdbcType=VARCHAR},
      </if>
      <if test="row.customerName != null">
        customer_name = #{row.customerName,jdbcType=VARCHAR},
      </if>
      <if test="row.ourSocialCreditCode != null">
        our_social_credit_code = #{row.ourSocialCreditCode,jdbcType=VARCHAR},
      </if>
      <if test="row.ourContractingEntity != null">
        our_contracting_entity = #{row.ourContractingEntity,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleLicense != null">
        vehicle_license = #{row.vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleModelName != null">
        vehicle_model_name = #{row.vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleModelCode != null">
        vehicle_model_code = #{row.vehicleModelCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleOperateBussCode != null">
        vehicle_operate_buss_code = #{row.vehicleOperateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleOperateCityCode != null">
        vehicle_operate_city_code = #{row.vehicleOperateCityCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleOperateCityName != null">
        vehicle_operate_city_name = #{row.vehicleOperateCityName,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleOperateBussName != null">
        vehicle_operate_buss_name = #{row.vehicleOperateBussName,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleBelongBussCode != null">
        vehicle_belong_buss_code = #{row.vehicleBelongBussCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleBelongBussName != null">
        vehicle_belong_buss_name = #{row.vehicleBelongBussName,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleSelfOwned != null">
        vehicle_self_owned = #{row.vehicleSelfOwned,jdbcType=TINYINT},
      </if>
      <if test="row.driverName != null">
        driver_name = #{row.driverName,jdbcType=VARCHAR},
      </if>
      <if test="row.driverMobile != null">
        driver_mobile = #{row.driverMobile,jdbcType=VARCHAR},
      </if>
      <if test="row.startDate != null">
        start_date = #{row.startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="row.endDate != null">
        end_date = #{row.endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="row.amount != null">
        amount = #{row.amount,jdbcType=DECIMAL},
      </if>
      <if test="row.taxRate != null">
        tax_rate = #{row.taxRate,jdbcType=DECIMAL},
      </if>
      <if test="row.paymentDays != null">
        payment_days = #{row.paymentDays,jdbcType=INTEGER},
      </if>
      <if test="row.remark != null">
        remark = #{row.remark,jdbcType=VARCHAR},
      </if>
      <if test="row.supplierCode != null">
        supplier_code = #{row.supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="row.supplierName != null">
        supplier_name = #{row.supplierName,jdbcType=VARCHAR},
      </if>
      <if test="row.supplierPayableAmount != null">
        supplier_payable_amount = #{row.supplierPayableAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.vehicleProvider != null">
        vehicle_provider = #{row.vehicleProvider,jdbcType=TINYINT},
      </if>
      <if test="row.driverProvider != null">
        driver_provider = #{row.driverProvider,jdbcType=TINYINT},
      </if>
      <if test="row.createCode != null">
        create_code = #{row.createCode,jdbcType=VARCHAR},
      </if>
      <if test="row.createName != null">
        create_name = #{row.createName,jdbcType=VARCHAR},
      </if>
      <if test="row.createStructCode != null">
        create_struct_code = #{row.createStructCode,jdbcType=VARCHAR},
      </if>
      <if test="row.createStructName != null">
        create_struct_name = #{row.createStructName,jdbcType=VARCHAR},
      </if>
      <if test="row.updateCode != null">
        update_code = #{row.updateCode,jdbcType=VARCHAR},
      </if>
      <if test="row.updateName != null">
        update_name = #{row.updateName,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.supplierRate != null">
        supplier_rate = #{row.supplierRate,jdbcType=DECIMAL},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update business_order_addition_record
    set record_id = #{row.recordId,jdbcType=INTEGER},
      order_code = #{row.orderCode,jdbcType=VARCHAR},
      order_status = #{row.orderStatus,jdbcType=INTEGER},
      customer_code = #{row.customerCode,jdbcType=VARCHAR},
      customer_name = #{row.customerName,jdbcType=VARCHAR},
      our_social_credit_code = #{row.ourSocialCreditCode,jdbcType=VARCHAR},
      our_contracting_entity = #{row.ourContractingEntity,jdbcType=VARCHAR},
      vehicle_license = #{row.vehicleLicense,jdbcType=VARCHAR},
      vehicle_model_name = #{row.vehicleModelName,jdbcType=VARCHAR},
      vehicle_model_code = #{row.vehicleModelCode,jdbcType=VARCHAR},
      vehicle_operate_buss_code = #{row.vehicleOperateBussCode,jdbcType=VARCHAR},
      vehicle_operate_city_code = #{row.vehicleOperateCityCode,jdbcType=VARCHAR},
      vehicle_operate_city_name = #{row.vehicleOperateCityName,jdbcType=VARCHAR},
      vehicle_operate_buss_name = #{row.vehicleOperateBussName,jdbcType=VARCHAR},
      vehicle_belong_buss_code = #{row.vehicleBelongBussCode,jdbcType=VARCHAR},
      vehicle_belong_buss_name = #{row.vehicleBelongBussName,jdbcType=VARCHAR},
      vehicle_self_owned = #{row.vehicleSelfOwned,jdbcType=TINYINT},
      driver_name = #{row.driverName,jdbcType=VARCHAR},
      driver_mobile = #{row.driverMobile,jdbcType=VARCHAR},
      start_date = #{row.startDate,jdbcType=TIMESTAMP},
      end_date = #{row.endDate,jdbcType=TIMESTAMP},
      amount = #{row.amount,jdbcType=DECIMAL},
      tax_rate = #{row.taxRate,jdbcType=DECIMAL},
      payment_days = #{row.paymentDays,jdbcType=INTEGER},
      remark = #{row.remark,jdbcType=VARCHAR},
      supplier_code = #{row.supplierCode,jdbcType=VARCHAR},
      supplier_name = #{row.supplierName,jdbcType=VARCHAR},
      supplier_payable_amount = #{row.supplierPayableAmount,jdbcType=DECIMAL},
      vehicle_provider = #{row.vehicleProvider,jdbcType=TINYINT},
      driver_provider = #{row.driverProvider,jdbcType=TINYINT},
      create_code = #{row.createCode,jdbcType=VARCHAR},
      create_name = #{row.createName,jdbcType=VARCHAR},
      create_struct_code = #{row.createStructCode,jdbcType=VARCHAR},
      create_struct_name = #{row.createStructName,jdbcType=VARCHAR},
      update_code = #{row.updateCode,jdbcType=VARCHAR},
      update_name = #{row.updateName,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      supplier_rate = #{row.supplierRate,jdbcType=DECIMAL}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.BusinessOrderAdditionRecord">
    update business_order_addition_record
    <set>
      <if test="orderCode != null">
        order_code = #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="customerCode != null">
        customer_code = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null">
        customer_name = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="ourSocialCreditCode != null">
        our_social_credit_code = #{ourSocialCreditCode,jdbcType=VARCHAR},
      </if>
      <if test="ourContractingEntity != null">
        our_contracting_entity = #{ourContractingEntity,jdbcType=VARCHAR},
      </if>
      <if test="vehicleLicense != null">
        vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelName != null">
        vehicle_model_name = #{vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelCode != null">
        vehicle_model_code = #{vehicleModelCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleOperateBussCode != null">
        vehicle_operate_buss_code = #{vehicleOperateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleOperateCityCode != null">
        vehicle_operate_city_code = #{vehicleOperateCityCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleOperateCityName != null">
        vehicle_operate_city_name = #{vehicleOperateCityName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleOperateBussName != null">
        vehicle_operate_buss_name = #{vehicleOperateBussName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBelongBussCode != null">
        vehicle_belong_buss_code = #{vehicleBelongBussCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBelongBussName != null">
        vehicle_belong_buss_name = #{vehicleBelongBussName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleSelfOwned != null">
        vehicle_self_owned = #{vehicleSelfOwned,jdbcType=TINYINT},
      </if>
      <if test="driverName != null">
        driver_name = #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="driverMobile != null">
        driver_mobile = #{driverMobile,jdbcType=VARCHAR},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=TIMESTAMP},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="taxRate != null">
        tax_rate = #{taxRate,jdbcType=DECIMAL},
      </if>
      <if test="paymentDays != null">
        payment_days = #{paymentDays,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="supplierCode != null">
        supplier_code = #{supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        supplier_name = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="supplierPayableAmount != null">
        supplier_payable_amount = #{supplierPayableAmount,jdbcType=DECIMAL},
      </if>
      <if test="vehicleProvider != null">
        vehicle_provider = #{vehicleProvider,jdbcType=TINYINT},
      </if>
      <if test="driverProvider != null">
        driver_provider = #{driverProvider,jdbcType=TINYINT},
      </if>
      <if test="createCode != null">
        create_code = #{createCode,jdbcType=VARCHAR},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createStructCode != null">
        create_struct_code = #{createStructCode,jdbcType=VARCHAR},
      </if>
      <if test="createStructName != null">
        create_struct_name = #{createStructName,jdbcType=VARCHAR},
      </if>
      <if test="updateCode != null">
        update_code = #{updateCode,jdbcType=VARCHAR},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="supplierRate != null">
        supplier_rate = #{supplierRate,jdbcType=DECIMAL},
      </if>
    </set>
    where record_id = #{recordId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.BusinessOrderAdditionRecord">
    update business_order_addition_record
    set order_code = #{orderCode,jdbcType=VARCHAR},
      order_status = #{orderStatus,jdbcType=INTEGER},
      customer_code = #{customerCode,jdbcType=VARCHAR},
      customer_name = #{customerName,jdbcType=VARCHAR},
      our_social_credit_code = #{ourSocialCreditCode,jdbcType=VARCHAR},
      our_contracting_entity = #{ourContractingEntity,jdbcType=VARCHAR},
      vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      vehicle_model_name = #{vehicleModelName,jdbcType=VARCHAR},
      vehicle_model_code = #{vehicleModelCode,jdbcType=VARCHAR},
      vehicle_operate_buss_code = #{vehicleOperateBussCode,jdbcType=VARCHAR},
      vehicle_operate_city_code = #{vehicleOperateCityCode,jdbcType=VARCHAR},
      vehicle_operate_city_name = #{vehicleOperateCityName,jdbcType=VARCHAR},
      vehicle_operate_buss_name = #{vehicleOperateBussName,jdbcType=VARCHAR},
      vehicle_belong_buss_code = #{vehicleBelongBussCode,jdbcType=VARCHAR},
      vehicle_belong_buss_name = #{vehicleBelongBussName,jdbcType=VARCHAR},
      vehicle_self_owned = #{vehicleSelfOwned,jdbcType=TINYINT},
      driver_name = #{driverName,jdbcType=VARCHAR},
      driver_mobile = #{driverMobile,jdbcType=VARCHAR},
      start_date = #{startDate,jdbcType=TIMESTAMP},
      end_date = #{endDate,jdbcType=TIMESTAMP},
      amount = #{amount,jdbcType=DECIMAL},
      tax_rate = #{taxRate,jdbcType=DECIMAL},
      payment_days = #{paymentDays,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      supplier_code = #{supplierCode,jdbcType=VARCHAR},
      supplier_name = #{supplierName,jdbcType=VARCHAR},
      supplier_payable_amount = #{supplierPayableAmount,jdbcType=DECIMAL},
      vehicle_provider = #{vehicleProvider,jdbcType=TINYINT},
      driver_provider = #{driverProvider,jdbcType=TINYINT},
      create_code = #{createCode,jdbcType=VARCHAR},
      create_name = #{createName,jdbcType=VARCHAR},
      create_struct_code = #{createStructCode,jdbcType=VARCHAR},
      create_struct_name = #{createStructName,jdbcType=VARCHAR},
      update_code = #{updateCode,jdbcType=VARCHAR},
      update_name = #{updateName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      supplier_rate = #{supplierRate,jdbcType=DECIMAL}
    where record_id = #{recordId,jdbcType=INTEGER}
  </update>
</mapper>