<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.CarLockLogMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.CarLockLog">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="operation_time" jdbcType="TIMESTAMP" property="operationTime" />
    <result column="customer_id" jdbcType="INTEGER" property="customerId" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="customer_location" jdbcType="VARCHAR" property="customerLocation" />
    <result column="car_location" jdbcType="VARCHAR" property="carLocation" />
    <result column="distance" jdbcType="INTEGER" property="distance" />
    <result column="car_id" jdbcType="INTEGER" property="carId" />
    <result column="vin" jdbcType="VARCHAR" property="vin" />
    <result column="car_num" jdbcType="VARCHAR" property="carNum" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
  </resultMap>
  <sql id="Base_Column_List">
    id, type, operation_time, customer_id, customer_name, customer_location, car_location, 
    distance, car_id, vin, car_num, status, order_no
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from car_lock_log
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from car_lock_log
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.CarLockLog">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into car_lock_log (type, operation_time, customer_id, 
      customer_name, customer_location, car_location, 
      distance, car_id, vin, 
      car_num, status, order_no
      )
    values (#{type,jdbcType=TINYINT}, #{operationTime,jdbcType=TIMESTAMP}, #{customerId,jdbcType=INTEGER}, 
      #{customerName,jdbcType=VARCHAR}, #{customerLocation,jdbcType=VARCHAR}, #{carLocation,jdbcType=VARCHAR}, 
      #{distance,jdbcType=INTEGER}, #{carId,jdbcType=INTEGER}, #{vin,jdbcType=VARCHAR}, 
      #{carNum,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{orderNo,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.CarLockLog">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into car_lock_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="type != null">
        type,
      </if>
      <if test="operationTime != null">
        operation_time,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="customerLocation != null">
        customer_location,
      </if>
      <if test="carLocation != null">
        car_location,
      </if>
      <if test="distance != null">
        distance,
      </if>
      <if test="carId != null">
        car_id,
      </if>
      <if test="vin != null">
        vin,
      </if>
      <if test="carNum != null">
        car_num,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="operationTime != null">
        #{operationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=INTEGER},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerLocation != null">
        #{customerLocation,jdbcType=VARCHAR},
      </if>
      <if test="carLocation != null">
        #{carLocation,jdbcType=VARCHAR},
      </if>
      <if test="distance != null">
        #{distance,jdbcType=INTEGER},
      </if>
      <if test="carId != null">
        #{carId,jdbcType=INTEGER},
      </if>
      <if test="vin != null">
        #{vin,jdbcType=VARCHAR},
      </if>
      <if test="carNum != null">
        #{carNum,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.CarLockLog">
    update car_lock_log
    <set>
      <if test="type != null">
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="operationTime != null">
        operation_time = #{operationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=INTEGER},
      </if>
      <if test="customerName != null">
        customer_name = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerLocation != null">
        customer_location = #{customerLocation,jdbcType=VARCHAR},
      </if>
      <if test="carLocation != null">
        car_location = #{carLocation,jdbcType=VARCHAR},
      </if>
      <if test="distance != null">
        distance = #{distance,jdbcType=INTEGER},
      </if>
      <if test="carId != null">
        car_id = #{carId,jdbcType=INTEGER},
      </if>
      <if test="vin != null">
        vin = #{vin,jdbcType=VARCHAR},
      </if>
      <if test="carNum != null">
        car_num = #{carNum,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.CarLockLog">
    update car_lock_log
    set type = #{type,jdbcType=TINYINT},
      operation_time = #{operationTime,jdbcType=TIMESTAMP},
      customer_id = #{customerId,jdbcType=INTEGER},
      customer_name = #{customerName,jdbcType=VARCHAR},
      customer_location = #{customerLocation,jdbcType=VARCHAR},
      car_location = #{carLocation,jdbcType=VARCHAR},
      distance = #{distance,jdbcType=INTEGER},
      car_id = #{carId,jdbcType=INTEGER},
      vin = #{vin,jdbcType=VARCHAR},
      car_num = #{carNum,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      order_no = #{orderNo,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>