<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.GovCarShortStoreVehicleMappingMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.GovCarShortStoreVehicleMapping">
    <id column="operate_buss_code" jdbcType="VARCHAR" property="operateBussCode" />
    <result column="operate_buss_name" jdbcType="VARCHAR" property="operateBussName" />
    <result column="admin_store_code" jdbcType="VARCHAR" property="adminStoreCode" />
    <result column="admin_store_name" jdbcType="VARCHAR" property="adminStoreName" />
    <result column="belong_store_code" jdbcType="VARCHAR" property="belongStoreCode" />
    <result column="belong_store_name" jdbcType="VARCHAR" property="belongStoreName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="notes" jdbcType="VARCHAR" property="notes" />
  </resultMap>
  <sql id="Base_Column_List">
    operate_buss_code, operate_buss_name, admin_store_code, admin_store_name, belong_store_code, 
    belong_store_name, create_time, update_time, notes
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from gov_car_short_store_vehicle_mapping
    where operate_buss_code = #{operateBussCode,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from gov_car_short_store_vehicle_mapping
    where operate_buss_code = #{operateBussCode,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.GovCarShortStoreVehicleMapping">
    insert into gov_car_short_store_vehicle_mapping (operate_buss_code, operate_buss_name, 
      admin_store_code, admin_store_name, belong_store_code, 
      belong_store_name, create_time, update_time, 
      notes)
    values (#{operateBussCode,jdbcType=VARCHAR}, #{operateBussName,jdbcType=VARCHAR}, 
      #{adminStoreCode,jdbcType=VARCHAR}, #{adminStoreName,jdbcType=VARCHAR}, #{belongStoreCode,jdbcType=VARCHAR}, 
      #{belongStoreName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{notes,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.GovCarShortStoreVehicleMapping">
    insert into gov_car_short_store_vehicle_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="operateBussCode != null">
        operate_buss_code,
      </if>
      <if test="operateBussName != null">
        operate_buss_name,
      </if>
      <if test="adminStoreCode != null">
        admin_store_code,
      </if>
      <if test="adminStoreName != null">
        admin_store_name,
      </if>
      <if test="belongStoreCode != null">
        belong_store_code,
      </if>
      <if test="belongStoreName != null">
        belong_store_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="notes != null">
        notes,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="operateBussCode != null">
        #{operateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="operateBussName != null">
        #{operateBussName,jdbcType=VARCHAR},
      </if>
      <if test="adminStoreCode != null">
        #{adminStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="adminStoreName != null">
        #{adminStoreName,jdbcType=VARCHAR},
      </if>
      <if test="belongStoreCode != null">
        #{belongStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="belongStoreName != null">
        #{belongStoreName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="notes != null">
        #{notes,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.GovCarShortStoreVehicleMapping">
    update gov_car_short_store_vehicle_mapping
    <set>
      <if test="operateBussName != null">
        operate_buss_name = #{operateBussName,jdbcType=VARCHAR},
      </if>
      <if test="adminStoreCode != null">
        admin_store_code = #{adminStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="adminStoreName != null">
        admin_store_name = #{adminStoreName,jdbcType=VARCHAR},
      </if>
      <if test="belongStoreCode != null">
        belong_store_code = #{belongStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="belongStoreName != null">
        belong_store_name = #{belongStoreName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="notes != null">
        notes = #{notes,jdbcType=VARCHAR},
      </if>
    </set>
    where operate_buss_code = #{operateBussCode,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.GovCarShortStoreVehicleMapping">
    update gov_car_short_store_vehicle_mapping
    set operate_buss_name = #{operateBussName,jdbcType=VARCHAR},
      admin_store_code = #{adminStoreCode,jdbcType=VARCHAR},
      admin_store_name = #{adminStoreName,jdbcType=VARCHAR},
      belong_store_code = #{belongStoreCode,jdbcType=VARCHAR},
      belong_store_name = #{belongStoreName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      notes = #{notes,jdbcType=VARCHAR}
    where operate_buss_code = #{operateBussCode,jdbcType=VARCHAR}
  </update>
</mapper>