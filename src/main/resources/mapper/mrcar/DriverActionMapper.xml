<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.DriverActionMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.DriverAction">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="driver_id" jdbcType="INTEGER" property="driverId" />
    <result column="action" jdbcType="INTEGER" property="action" />
    <result column="action_time" jdbcType="TIMESTAMP" property="actionTime" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="imei" jdbcType="VARCHAR" property="imei" />
    <result column="mobel_version" jdbcType="VARCHAR" property="mobelVersion" />
    <result column="sys_version" jdbcType="VARCHAR" property="sysVersion" />
    <result column="platform" jdbcType="VARCHAR" property="platform" />
    <result column="long_addr" jdbcType="VARCHAR" property="longAddr" />
    <result column="short_addr" jdbcType="VARCHAR" property="shortAddr" />
    <result column="point" jdbcType="VARCHAR" property="point" />
    <result column="start_city_code" jdbcType="INTEGER" property="startCityCode" />
  </resultMap>
  <sql id="Base_Column_List">
    id, driver_id, action, action_time, order_no, version, imei, mobel_version, sys_version, 
    platform, long_addr, short_addr, point, start_city_code
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from driver_action
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from driver_action
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.DriverAction">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into driver_action (driver_id, action, action_time, 
      order_no, version, imei, 
      mobel_version, sys_version, platform, 
      long_addr, short_addr, point, 
      start_city_code)
    values (#{driverId,jdbcType=INTEGER}, #{action,jdbcType=INTEGER}, #{actionTime,jdbcType=TIMESTAMP}, 
      #{orderNo,jdbcType=VARCHAR}, #{version,jdbcType=VARCHAR}, #{imei,jdbcType=VARCHAR}, 
      #{mobelVersion,jdbcType=VARCHAR}, #{sysVersion,jdbcType=VARCHAR}, #{platform,jdbcType=VARCHAR}, 
      #{longAddr,jdbcType=VARCHAR}, #{shortAddr,jdbcType=VARCHAR}, #{point,jdbcType=VARCHAR}, 
      #{startCityCode,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.DriverAction">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into driver_action
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="driverId != null">
        driver_id,
      </if>
      <if test="action != null">
        action,
      </if>
      <if test="actionTime != null">
        action_time,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="imei != null">
        imei,
      </if>
      <if test="mobelVersion != null">
        mobel_version,
      </if>
      <if test="sysVersion != null">
        sys_version,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="longAddr != null">
        long_addr,
      </if>
      <if test="shortAddr != null">
        short_addr,
      </if>
      <if test="point != null">
        point,
      </if>
      <if test="startCityCode != null">
        start_city_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="driverId != null">
        #{driverId,jdbcType=INTEGER},
      </if>
      <if test="action != null">
        #{action,jdbcType=INTEGER},
      </if>
      <if test="actionTime != null">
        #{actionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="imei != null">
        #{imei,jdbcType=VARCHAR},
      </if>
      <if test="mobelVersion != null">
        #{mobelVersion,jdbcType=VARCHAR},
      </if>
      <if test="sysVersion != null">
        #{sysVersion,jdbcType=VARCHAR},
      </if>
      <if test="platform != null">
        #{platform,jdbcType=VARCHAR},
      </if>
      <if test="longAddr != null">
        #{longAddr,jdbcType=VARCHAR},
      </if>
      <if test="shortAddr != null">
        #{shortAddr,jdbcType=VARCHAR},
      </if>
      <if test="point != null">
        #{point,jdbcType=VARCHAR},
      </if>
      <if test="startCityCode != null">
        #{startCityCode,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.DriverAction">
    update driver_action
    <set>
      <if test="driverId != null">
        driver_id = #{driverId,jdbcType=INTEGER},
      </if>
      <if test="action != null">
        action = #{action,jdbcType=INTEGER},
      </if>
      <if test="actionTime != null">
        action_time = #{actionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=VARCHAR},
      </if>
      <if test="imei != null">
        imei = #{imei,jdbcType=VARCHAR},
      </if>
      <if test="mobelVersion != null">
        mobel_version = #{mobelVersion,jdbcType=VARCHAR},
      </if>
      <if test="sysVersion != null">
        sys_version = #{sysVersion,jdbcType=VARCHAR},
      </if>
      <if test="platform != null">
        platform = #{platform,jdbcType=VARCHAR},
      </if>
      <if test="longAddr != null">
        long_addr = #{longAddr,jdbcType=VARCHAR},
      </if>
      <if test="shortAddr != null">
        short_addr = #{shortAddr,jdbcType=VARCHAR},
      </if>
      <if test="point != null">
        point = #{point,jdbcType=VARCHAR},
      </if>
      <if test="startCityCode != null">
        start_city_code = #{startCityCode,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.DriverAction">
    update driver_action
    set driver_id = #{driverId,jdbcType=INTEGER},
      action = #{action,jdbcType=INTEGER},
      action_time = #{actionTime,jdbcType=TIMESTAMP},
      order_no = #{orderNo,jdbcType=VARCHAR},
      version = #{version,jdbcType=VARCHAR},
      imei = #{imei,jdbcType=VARCHAR},
      mobel_version = #{mobelVersion,jdbcType=VARCHAR},
      sys_version = #{sysVersion,jdbcType=VARCHAR},
      platform = #{platform,jdbcType=VARCHAR},
      long_addr = #{longAddr,jdbcType=VARCHAR},
      short_addr = #{shortAddr,jdbcType=VARCHAR},
      point = #{point,jdbcType=VARCHAR},
      start_city_code = #{startCityCode,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>