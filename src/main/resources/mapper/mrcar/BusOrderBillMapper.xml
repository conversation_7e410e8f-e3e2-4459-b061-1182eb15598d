<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.BusOrderBillMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.BusOrderBill">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="actual_operating_mileage" jdbcType="DECIMAL" property="actualOperatingMileage" />
    <result column="actual_empty_mileage" jdbcType="DECIMAL" property="actualEmptyMileage" />
    <result column="actual_operating_duration" jdbcType="INTEGER" property="actualOperatingDuration" />
    <result column="actual_empty_duration" jdbcType="INTEGER" property="actualEmptyDuration" />
    <result column="parking_fee" jdbcType="DECIMAL" property="parkingFee" />
    <result column="toll_fee" jdbcType="DECIMAL" property="tollFee" />
    <result column="catering_fee" jdbcType="DECIMAL" property="cateringFee" />
    <result column="accommodation_fee" jdbcType="DECIMAL" property="accommodationFee" />
    <result column="other_fee" jdbcType="DECIMAL" property="otherFee" />
    <result column="order_fee" jdbcType="DECIMAL" property="orderFee" />
    <result column="timeout_fee" jdbcType="DECIMAL" property="timeoutFee" />
    <result column="over_mileage_fee" jdbcType="DECIMAL" property="overMileageFee" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="adjust_remark" jdbcType="VARCHAR" property="adjustRemark" />
    <result column="create_id" jdbcType="INTEGER" property="createId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="update_id" jdbcType="INTEGER" property="updateId" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_no, actual_operating_mileage, actual_empty_mileage, actual_operating_duration, 
    actual_empty_duration, parking_fee, toll_fee, catering_fee, accommodation_fee, other_fee, 
    order_fee, timeout_fee, over_mileage_fee, remark, adjust_remark, create_id, create_name, 
    update_id, update_name, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.BusOrderBillExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from bus_order_bill
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bus_order_bill
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from bus_order_bill
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.BusOrderBill">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into bus_order_bill (order_no, actual_operating_mileage, 
      actual_empty_mileage, actual_operating_duration, 
      actual_empty_duration, parking_fee, toll_fee, 
      catering_fee, accommodation_fee, other_fee, 
      order_fee, timeout_fee, over_mileage_fee, 
      remark, adjust_remark, create_id, 
      create_name, update_id, update_name, 
      create_time, update_time)
    values (#{orderNo,jdbcType=VARCHAR}, #{actualOperatingMileage,jdbcType=DECIMAL}, 
      #{actualEmptyMileage,jdbcType=DECIMAL}, #{actualOperatingDuration,jdbcType=INTEGER}, 
      #{actualEmptyDuration,jdbcType=INTEGER}, #{parkingFee,jdbcType=DECIMAL}, #{tollFee,jdbcType=DECIMAL}, 
      #{cateringFee,jdbcType=DECIMAL}, #{accommodationFee,jdbcType=DECIMAL}, #{otherFee,jdbcType=DECIMAL}, 
      #{orderFee,jdbcType=DECIMAL}, #{timeoutFee,jdbcType=DECIMAL}, #{overMileageFee,jdbcType=DECIMAL}, 
      #{remark,jdbcType=VARCHAR}, #{adjustRemark,jdbcType=VARCHAR}, #{createId,jdbcType=INTEGER}, 
      #{createName,jdbcType=VARCHAR}, #{updateId,jdbcType=INTEGER}, #{updateName,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.BusOrderBill">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into bus_order_bill
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="actualOperatingMileage != null">
        actual_operating_mileage,
      </if>
      <if test="actualEmptyMileage != null">
        actual_empty_mileage,
      </if>
      <if test="actualOperatingDuration != null">
        actual_operating_duration,
      </if>
      <if test="actualEmptyDuration != null">
        actual_empty_duration,
      </if>
      <if test="parkingFee != null">
        parking_fee,
      </if>
      <if test="tollFee != null">
        toll_fee,
      </if>
      <if test="cateringFee != null">
        catering_fee,
      </if>
      <if test="accommodationFee != null">
        accommodation_fee,
      </if>
      <if test="otherFee != null">
        other_fee,
      </if>
      <if test="orderFee != null">
        order_fee,
      </if>
      <if test="timeoutFee != null">
        timeout_fee,
      </if>
      <if test="overMileageFee != null">
        over_mileage_fee,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="adjustRemark != null">
        adjust_remark,
      </if>
      <if test="createId != null">
        create_id,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="updateId != null">
        update_id,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="actualOperatingMileage != null">
        #{actualOperatingMileage,jdbcType=DECIMAL},
      </if>
      <if test="actualEmptyMileage != null">
        #{actualEmptyMileage,jdbcType=DECIMAL},
      </if>
      <if test="actualOperatingDuration != null">
        #{actualOperatingDuration,jdbcType=INTEGER},
      </if>
      <if test="actualEmptyDuration != null">
        #{actualEmptyDuration,jdbcType=INTEGER},
      </if>
      <if test="parkingFee != null">
        #{parkingFee,jdbcType=DECIMAL},
      </if>
      <if test="tollFee != null">
        #{tollFee,jdbcType=DECIMAL},
      </if>
      <if test="cateringFee != null">
        #{cateringFee,jdbcType=DECIMAL},
      </if>
      <if test="accommodationFee != null">
        #{accommodationFee,jdbcType=DECIMAL},
      </if>
      <if test="otherFee != null">
        #{otherFee,jdbcType=DECIMAL},
      </if>
      <if test="orderFee != null">
        #{orderFee,jdbcType=DECIMAL},
      </if>
      <if test="timeoutFee != null">
        #{timeoutFee,jdbcType=DECIMAL},
      </if>
      <if test="overMileageFee != null">
        #{overMileageFee,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="adjustRemark != null">
        #{adjustRemark,jdbcType=VARCHAR},
      </if>
      <if test="createId != null">
        #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="updateId != null">
        #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByExampleSelective" parameterType="map">
    update bus_order_bill
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=INTEGER},
      </if>
      <if test="row.orderNo != null">
        order_no = #{row.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="row.actualOperatingMileage != null">
        actual_operating_mileage = #{row.actualOperatingMileage,jdbcType=DECIMAL},
      </if>
      <if test="row.actualEmptyMileage != null">
        actual_empty_mileage = #{row.actualEmptyMileage,jdbcType=DECIMAL},
      </if>
      <if test="row.actualOperatingDuration != null">
        actual_operating_duration = #{row.actualOperatingDuration,jdbcType=INTEGER},
      </if>
      <if test="row.actualEmptyDuration != null">
        actual_empty_duration = #{row.actualEmptyDuration,jdbcType=INTEGER},
      </if>
      <if test="row.parkingFee != null">
        parking_fee = #{row.parkingFee,jdbcType=DECIMAL},
      </if>
      <if test="row.tollFee != null">
        toll_fee = #{row.tollFee,jdbcType=DECIMAL},
      </if>
      <if test="row.cateringFee != null">
        catering_fee = #{row.cateringFee,jdbcType=DECIMAL},
      </if>
      <if test="row.accommodationFee != null">
        accommodation_fee = #{row.accommodationFee,jdbcType=DECIMAL},
      </if>
      <if test="row.otherFee != null">
        other_fee = #{row.otherFee,jdbcType=DECIMAL},
      </if>
      <if test="row.orderFee != null">
        order_fee = #{row.orderFee,jdbcType=DECIMAL},
      </if>
      <if test="row.timeoutFee != null">
        timeout_fee = #{row.timeoutFee,jdbcType=DECIMAL},
      </if>
      <if test="row.overMileageFee != null">
        over_mileage_fee = #{row.overMileageFee,jdbcType=DECIMAL},
      </if>
      <if test="row.remark != null">
        remark = #{row.remark,jdbcType=VARCHAR},
      </if>
      <if test="row.adjustRemark != null">
        adjust_remark = #{row.adjustRemark,jdbcType=VARCHAR},
      </if>
      <if test="row.createId != null">
        create_id = #{row.createId,jdbcType=INTEGER},
      </if>
      <if test="row.createName != null">
        create_name = #{row.createName,jdbcType=VARCHAR},
      </if>
      <if test="row.updateId != null">
        update_id = #{row.updateId,jdbcType=INTEGER},
      </if>
      <if test="row.updateName != null">
        update_name = #{row.updateName,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update bus_order_bill
    set id = #{row.id,jdbcType=INTEGER},
      order_no = #{row.orderNo,jdbcType=VARCHAR},
      actual_operating_mileage = #{row.actualOperatingMileage,jdbcType=DECIMAL},
      actual_empty_mileage = #{row.actualEmptyMileage,jdbcType=DECIMAL},
      actual_operating_duration = #{row.actualOperatingDuration,jdbcType=INTEGER},
      actual_empty_duration = #{row.actualEmptyDuration,jdbcType=INTEGER},
      parking_fee = #{row.parkingFee,jdbcType=DECIMAL},
      toll_fee = #{row.tollFee,jdbcType=DECIMAL},
      catering_fee = #{row.cateringFee,jdbcType=DECIMAL},
      accommodation_fee = #{row.accommodationFee,jdbcType=DECIMAL},
      other_fee = #{row.otherFee,jdbcType=DECIMAL},
      order_fee = #{row.orderFee,jdbcType=DECIMAL},
      timeout_fee = #{row.timeoutFee,jdbcType=DECIMAL},
      over_mileage_fee = #{row.overMileageFee,jdbcType=DECIMAL},
      remark = #{row.remark,jdbcType=VARCHAR},
      adjust_remark = #{row.adjustRemark,jdbcType=VARCHAR},
      create_id = #{row.createId,jdbcType=INTEGER},
      create_name = #{row.createName,jdbcType=VARCHAR},
      update_id = #{row.updateId,jdbcType=INTEGER},
      update_name = #{row.updateName,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.BusOrderBill">
    update bus_order_bill
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="actualOperatingMileage != null">
        actual_operating_mileage = #{actualOperatingMileage,jdbcType=DECIMAL},
      </if>
      <if test="actualEmptyMileage != null">
        actual_empty_mileage = #{actualEmptyMileage,jdbcType=DECIMAL},
      </if>
      <if test="actualOperatingDuration != null">
        actual_operating_duration = #{actualOperatingDuration,jdbcType=INTEGER},
      </if>
      <if test="actualEmptyDuration != null">
        actual_empty_duration = #{actualEmptyDuration,jdbcType=INTEGER},
      </if>
      <if test="parkingFee != null">
        parking_fee = #{parkingFee,jdbcType=DECIMAL},
      </if>
      <if test="tollFee != null">
        toll_fee = #{tollFee,jdbcType=DECIMAL},
      </if>
      <if test="cateringFee != null">
        catering_fee = #{cateringFee,jdbcType=DECIMAL},
      </if>
      <if test="accommodationFee != null">
        accommodation_fee = #{accommodationFee,jdbcType=DECIMAL},
      </if>
      <if test="otherFee != null">
        other_fee = #{otherFee,jdbcType=DECIMAL},
      </if>
      <if test="orderFee != null">
        order_fee = #{orderFee,jdbcType=DECIMAL},
      </if>
      <if test="timeoutFee != null">
        timeout_fee = #{timeoutFee,jdbcType=DECIMAL},
      </if>
      <if test="overMileageFee != null">
        over_mileage_fee = #{overMileageFee,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="adjustRemark != null">
        adjust_remark = #{adjustRemark,jdbcType=VARCHAR},
      </if>
      <if test="createId != null">
        create_id = #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="updateId != null">
        update_id = #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.BusOrderBill">
    update bus_order_bill
    set order_no = #{orderNo,jdbcType=VARCHAR},
      actual_operating_mileage = #{actualOperatingMileage,jdbcType=DECIMAL},
      actual_empty_mileage = #{actualEmptyMileage,jdbcType=DECIMAL},
      actual_operating_duration = #{actualOperatingDuration,jdbcType=INTEGER},
      actual_empty_duration = #{actualEmptyDuration,jdbcType=INTEGER},
      parking_fee = #{parkingFee,jdbcType=DECIMAL},
      toll_fee = #{tollFee,jdbcType=DECIMAL},
      catering_fee = #{cateringFee,jdbcType=DECIMAL},
      accommodation_fee = #{accommodationFee,jdbcType=DECIMAL},
      other_fee = #{otherFee,jdbcType=DECIMAL},
      order_fee = #{orderFee,jdbcType=DECIMAL},
      timeout_fee = #{timeoutFee,jdbcType=DECIMAL},
      over_mileage_fee = #{overMileageFee,jdbcType=DECIMAL},
      remark = #{remark,jdbcType=VARCHAR},
      adjust_remark = #{adjustRemark,jdbcType=VARCHAR},
      create_id = #{createId,jdbcType=INTEGER},
      create_name = #{createName,jdbcType=VARCHAR},
      update_id = #{updateId,jdbcType=INTEGER},
      update_name = #{updateName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>