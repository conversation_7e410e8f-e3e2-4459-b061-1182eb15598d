<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.CoOrderInfoMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.CoOrderInfo">
    <id column="co_order_id" jdbcType="INTEGER" property="coOrderId" />
    <result column="create_id" jdbcType="INTEGER" property="createId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_mobile" jdbcType="VARCHAR" property="createMobile" />
    <result column="create_company_id" jdbcType="VARCHAR" property="createCompanyId" />
    <result column="create_company_name" jdbcType="VARCHAR" property="createCompanyName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_id" jdbcType="INTEGER" property="updateId" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="update_mobile" jdbcType="VARCHAR" property="updateMobile" />
    <result column="update_company_id" jdbcType="VARCHAR" property="updateCompanyId" />
    <result column="update_company_name" jdbcType="VARCHAR" property="updateCompanyName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="del_tag" jdbcType="TINYINT" property="delTag" />
    <result column="order_num" jdbcType="VARCHAR" property="orderNum" />
    <result column="third_order_num" jdbcType="VARCHAR" property="thirdOrderNum" />
    <result column="order_status" jdbcType="INTEGER" property="orderStatus" />
    <result column="bill_status" jdbcType="INTEGER" property="billStatus" />
    <result column="order_source" jdbcType="TINYINT" property="orderSource" />
    <result column="actual_use_time" jdbcType="TIMESTAMP" property="actualUseTime" />
    <result column="actual_return_time" jdbcType="TIMESTAMP" property="actualReturnTime" />
    <result column="estimate_use_date" jdbcType="TIMESTAMP" property="estimateUseDate" />
    <result column="estimate_return_date" jdbcType="TIMESTAMP" property="estimateReturnDate" />
    <result column="actual_use_cycle" jdbcType="INTEGER" property="actualUseCycle" />
    <result column="estimate_use_cycle" jdbcType="INTEGER" property="estimateUseCycle" />
    <result column="order_type" jdbcType="TINYINT" property="orderType" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="customer_phone" jdbcType="VARCHAR" property="customerPhone" />
    <result column="customer_id_card" jdbcType="VARCHAR" property="customerIdCard" />
    <result column="estimate_use_city_code" jdbcType="VARCHAR" property="estimateUseCityCode" />
    <result column="estimate_use_city_name" jdbcType="VARCHAR" property="estimateUseCityName" />
    <result column="estimate_return_city_code" jdbcType="VARCHAR" property="estimateReturnCityCode" />
    <result column="estimate_return_city_name" jdbcType="VARCHAR" property="estimateReturnCityName" />
    <result column="estimate_use_address" jdbcType="VARCHAR" property="estimateUseAddress" />
    <result column="estimate_use_address_lat" jdbcType="VARCHAR" property="estimateUseAddressLat" />
    <result column="estimate_use_address_lon" jdbcType="VARCHAR" property="estimateUseAddressLon" />
    <result column="estimate_return_address" jdbcType="VARCHAR" property="estimateReturnAddress" />
    <result column="estimate_return_address_lat" jdbcType="VARCHAR" property="estimateReturnAddressLat" />
    <result column="estimate_return_address_lon" jdbcType="VARCHAR" property="estimateReturnAddressLon" />
    <result column="actual_use_city_code" jdbcType="VARCHAR" property="actualUseCityCode" />
    <result column="actual_use_city_name" jdbcType="VARCHAR" property="actualUseCityName" />
    <result column="actual_return_city_code" jdbcType="VARCHAR" property="actualReturnCityCode" />
    <result column="actual_return_city_name" jdbcType="VARCHAR" property="actualReturnCityName" />
    <result column="actual_use_address" jdbcType="VARCHAR" property="actualUseAddress" />
    <result column="actual_use_address_lat" jdbcType="VARCHAR" property="actualUseAddressLat" />
    <result column="actual_use_address_lon" jdbcType="VARCHAR" property="actualUseAddressLon" />
    <result column="actual_return_address" jdbcType="VARCHAR" property="actualReturnAddress" />
    <result column="actual_return_address_lat" jdbcType="VARCHAR" property="actualReturnAddressLat" />
    <result column="actual_return_address_lon" jdbcType="VARCHAR" property="actualReturnAddressLon" />
    <result column="delivery_person_id" jdbcType="INTEGER" property="deliveryPersonId" />
    <result column="delivery_person_name" jdbcType="VARCHAR" property="deliveryPersonName" />
    <result column="delivery_person_phone" jdbcType="VARCHAR" property="deliveryPersonPhone" />
    <result column="pick_person_id" jdbcType="INTEGER" property="pickPersonId" />
    <result column="pick_person_name" jdbcType="VARCHAR" property="pickPersonName" />
    <result column="pick_person_phone" jdbcType="VARCHAR" property="pickPersonPhone" />
    <result column="use_remark" jdbcType="VARCHAR" property="useRemark" />
    <result column="delivery_over_mileage_amount" jdbcType="DECIMAL" property="deliveryOverMileageAmount" />
    <result column="return_over_mileage_amount" jdbcType="DECIMAL" property="returnOverMileageAmount" />
    <result column="total_over_mileage_amount" jdbcType="DECIMAL" property="totalOverMileageAmount" />
    <result column="vehicle_rent_amount" jdbcType="DECIMAL" property="vehicleRentAmount" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="tax_total_amount" jdbcType="DECIMAL" property="taxTotalAmount" />
    <result column="original_total_amount" jdbcType="DECIMAL" property="originalTotalAmount" />
    <result column="vehicle_license" jdbcType="VARCHAR" property="vehicleLicense" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="vehicle_vin" jdbcType="VARCHAR" property="vehicleVin" />
    <result column="vehicle_serial_no_new_system" jdbcType="VARCHAR" property="vehicleSerialNoNewSystem" />
    <result column="vehicle_brand_code" jdbcType="VARCHAR" property="vehicleBrandCode" />
    <result column="vehicle_brand" jdbcType="VARCHAR" property="vehicleBrand" />
    <result column="brand" jdbcType="VARCHAR" property="brand" />
    <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode" />
    <result column="vehicle_model" jdbcType="VARCHAR" property="vehicleModel" />
    <result column="engine_num" jdbcType="VARCHAR" property="engineNum" />
    <result column="vehicle_color" jdbcType="VARCHAR" property="vehicleColor" />
    <result column="vehicle_company_id" jdbcType="VARCHAR" property="vehicleCompanyId" />
    <result column="vehicle_company_name" jdbcType="VARCHAR" property="vehicleCompanyName" />
    <result column="self_owned" jdbcType="TINYINT" property="selfOwned" />
    <result column="operate_buss_name" jdbcType="VARCHAR" property="operateBussName" />
    <result column="operate_buss_code" jdbcType="VARCHAR" property="operateBussCode" />
    <result column="belong_buss_code" jdbcType="VARCHAR" property="belongBussCode" />
    <result column="belong_buss_name" jdbcType="VARCHAR" property="belongBussName" />
    <result column="asset_city_code" jdbcType="VARCHAR" property="assetCityCode" />
    <result column="asset_city_name" jdbcType="VARCHAR" property="assetCityName" />
    <result column="struct_id" jdbcType="INTEGER" property="structId" />
    <result column="struct_code" jdbcType="VARCHAR" property="structCode" />
    <result column="struct_name" jdbcType="VARCHAR" property="structName" />
    <result column="belong_city_code" jdbcType="VARCHAR" property="belongCityCode" />
    <result column="belong_city_name" jdbcType="VARCHAR" property="belongCityName" />
    <result column="record_memo" jdbcType="VARCHAR" property="recordMemo" />
    <result column="update_memo" jdbcType="VARCHAR" property="updateMemo" />
    <result column="cancel_memo" jdbcType="VARCHAR" property="cancelMemo" />
    <result column="tax_type" jdbcType="INTEGER" property="taxType" />
    <result column="tax_rate" jdbcType="DECIMAL" property="taxRate" />
    <result column="deposit_flag" jdbcType="BIT" property="depositFlag" />
    <result column="deposit_free_type" jdbcType="TINYINT" property="depositFreeType" />
    <result column="deposit_auth_url" jdbcType="VARCHAR" property="depositAuthUrl" />
    <result column="deposit_auth_qr_code_url" jdbcType="VARCHAR" property="depositAuthQrCodeUrl" />
    <result column="vehicle_deposit" jdbcType="DECIMAL" property="vehicleDeposit" />
    <result column="violation_deposit" jdbcType="DECIMAL" property="violationDeposit" />
    <result column="order_preauth_timeout" jdbcType="TIMESTAMP" property="orderPreauthTimeout" />
    <result column="deposit_free_reason" jdbcType="VARCHAR" property="depositFreeReason" />
    <result column="car_level_id" jdbcType="INTEGER" property="carLevelId" />
    <result column="car_level_name" jdbcType="VARCHAR" property="carLevelName" />
    <result column="charge_type" jdbcType="VARCHAR" property="chargeType" />
    <result column="pickup_delivery_fee" jdbcType="DECIMAL" property="pickupDeliveryFee" />
    <result column="price_config_detail_id" jdbcType="INTEGER" property="priceConfigDetailId" />
    <result column="config_model_code" jdbcType="VARCHAR" property="configModelCode" />
    <result column="config_model_name" jdbcType="VARCHAR" property="configModelName" />
    <result column="config_brand_code" jdbcType="VARCHAR" property="configBrandCode" />
    <result column="config_brand_name" jdbcType="VARCHAR" property="configBrandName" />
    <result column="day_price" jdbcType="DECIMAL" property="dayPrice" />
    <result column="out_mile_price" jdbcType="DECIMAL" property="outMilePrice" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    co_order_id, create_id, create_name, create_mobile, create_company_id, create_company_name, 
    create_time, update_id, update_name, update_mobile, update_company_id, update_company_name, 
    update_time, del_tag, order_num, third_order_num, order_status, bill_status, order_source, 
    actual_use_time, actual_return_time, estimate_use_date, estimate_return_date, actual_use_cycle, 
    estimate_use_cycle, order_type, company_id, company_code, company_name, customer_name, 
    customer_phone, customer_id_card, estimate_use_city_code, estimate_use_city_name, 
    estimate_return_city_code, estimate_return_city_name, estimate_use_address, estimate_use_address_lat, 
    estimate_use_address_lon, estimate_return_address, estimate_return_address_lat, estimate_return_address_lon, 
    actual_use_city_code, actual_use_city_name, actual_return_city_code, actual_return_city_name, 
    actual_use_address, actual_use_address_lat, actual_use_address_lon, actual_return_address, 
    actual_return_address_lat, actual_return_address_lon, delivery_person_id, delivery_person_name, 
    delivery_person_phone, pick_person_id, pick_person_name, pick_person_phone, use_remark, 
    delivery_over_mileage_amount, return_over_mileage_amount, total_over_mileage_amount, 
    vehicle_rent_amount, total_amount, tax_total_amount, original_total_amount, vehicle_license, 
    vehicle_id, vehicle_vin, vehicle_serial_no_new_system, vehicle_brand_code, vehicle_brand, 
    brand, vehicle_model_code, vehicle_model, engine_num, vehicle_color, vehicle_company_id, 
    vehicle_company_name, self_owned, operate_buss_name, operate_buss_code, belong_buss_code, 
    belong_buss_name, asset_city_code, asset_city_name, struct_id, struct_code, struct_name, 
    belong_city_code, belong_city_name, record_memo, update_memo, cancel_memo, tax_type, 
    tax_rate, deposit_flag, deposit_free_type, deposit_auth_url, deposit_auth_qr_code_url, 
    vehicle_deposit, violation_deposit, order_preauth_timeout, deposit_free_reason, car_level_id, 
    car_level_name, charge_type, pickup_delivery_fee, price_config_detail_id, config_model_code, 
    config_model_name, config_brand_code, config_brand_name, day_price, out_mile_price
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.CoOrderInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from co_order_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from co_order_info
    where co_order_id = #{coOrderId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from co_order_info
    where co_order_id = #{coOrderId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.order.entity.mrcar.CoOrderInfoExample">
    delete from co_order_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.CoOrderInfo">
    <selectKey keyProperty="coOrderId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into co_order_info (create_id, create_name, create_mobile, 
      create_company_id, create_company_name, create_time, 
      update_id, update_name, update_mobile, 
      update_company_id, update_company_name, update_time, 
      del_tag, order_num, third_order_num, 
      order_status, bill_status, order_source, 
      actual_use_time, actual_return_time, estimate_use_date, 
      estimate_return_date, actual_use_cycle, estimate_use_cycle, 
      order_type, company_id, company_code, 
      company_name, customer_name, customer_phone, 
      customer_id_card, estimate_use_city_code, estimate_use_city_name, 
      estimate_return_city_code, estimate_return_city_name, 
      estimate_use_address, estimate_use_address_lat, 
      estimate_use_address_lon, estimate_return_address, 
      estimate_return_address_lat, estimate_return_address_lon, 
      actual_use_city_code, actual_use_city_name, actual_return_city_code, 
      actual_return_city_name, actual_use_address, 
      actual_use_address_lat, actual_use_address_lon, 
      actual_return_address, actual_return_address_lat, 
      actual_return_address_lon, delivery_person_id, 
      delivery_person_name, delivery_person_phone, 
      pick_person_id, pick_person_name, pick_person_phone, 
      use_remark, delivery_over_mileage_amount, return_over_mileage_amount, 
      total_over_mileage_amount, vehicle_rent_amount, 
      total_amount, tax_total_amount, original_total_amount, 
      vehicle_license, vehicle_id, vehicle_vin, 
      vehicle_serial_no_new_system, vehicle_brand_code, 
      vehicle_brand, brand, vehicle_model_code, 
      vehicle_model, engine_num, vehicle_color, 
      vehicle_company_id, vehicle_company_name, self_owned, 
      operate_buss_name, operate_buss_code, belong_buss_code, 
      belong_buss_name, asset_city_code, asset_city_name, 
      struct_id, struct_code, struct_name, 
      belong_city_code, belong_city_name, record_memo, 
      update_memo, cancel_memo, tax_type, 
      tax_rate, deposit_flag, deposit_free_type, 
      deposit_auth_url, deposit_auth_qr_code_url, vehicle_deposit, 
      violation_deposit, order_preauth_timeout, 
      deposit_free_reason, car_level_id, car_level_name, 
      charge_type, pickup_delivery_fee, price_config_detail_id, 
      config_model_code, config_model_name, config_brand_code, 
      config_brand_name, day_price, out_mile_price
      )
    values (#{createId,jdbcType=INTEGER}, #{createName,jdbcType=VARCHAR}, #{createMobile,jdbcType=VARCHAR}, 
      #{createCompanyId,jdbcType=VARCHAR}, #{createCompanyName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateId,jdbcType=INTEGER}, #{updateName,jdbcType=VARCHAR}, #{updateMobile,jdbcType=VARCHAR}, 
      #{updateCompanyId,jdbcType=VARCHAR}, #{updateCompanyName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{delTag,jdbcType=TINYINT}, #{orderNum,jdbcType=VARCHAR}, #{thirdOrderNum,jdbcType=VARCHAR}, 
      #{orderStatus,jdbcType=INTEGER}, #{billStatus,jdbcType=INTEGER}, #{orderSource,jdbcType=TINYINT}, 
      #{actualUseTime,jdbcType=TIMESTAMP}, #{actualReturnTime,jdbcType=TIMESTAMP}, #{estimateUseDate,jdbcType=TIMESTAMP}, 
      #{estimateReturnDate,jdbcType=TIMESTAMP}, #{actualUseCycle,jdbcType=INTEGER}, #{estimateUseCycle,jdbcType=INTEGER}, 
      #{orderType,jdbcType=TINYINT}, #{companyId,jdbcType=INTEGER}, #{companyCode,jdbcType=VARCHAR}, 
      #{companyName,jdbcType=VARCHAR}, #{customerName,jdbcType=VARCHAR}, #{customerPhone,jdbcType=VARCHAR}, 
      #{customerIdCard,jdbcType=VARCHAR}, #{estimateUseCityCode,jdbcType=VARCHAR}, #{estimateUseCityName,jdbcType=VARCHAR}, 
      #{estimateReturnCityCode,jdbcType=VARCHAR}, #{estimateReturnCityName,jdbcType=VARCHAR}, 
      #{estimateUseAddress,jdbcType=VARCHAR}, #{estimateUseAddressLat,jdbcType=VARCHAR}, 
      #{estimateUseAddressLon,jdbcType=VARCHAR}, #{estimateReturnAddress,jdbcType=VARCHAR}, 
      #{estimateReturnAddressLat,jdbcType=VARCHAR}, #{estimateReturnAddressLon,jdbcType=VARCHAR}, 
      #{actualUseCityCode,jdbcType=VARCHAR}, #{actualUseCityName,jdbcType=VARCHAR}, #{actualReturnCityCode,jdbcType=VARCHAR}, 
      #{actualReturnCityName,jdbcType=VARCHAR}, #{actualUseAddress,jdbcType=VARCHAR}, 
      #{actualUseAddressLat,jdbcType=VARCHAR}, #{actualUseAddressLon,jdbcType=VARCHAR}, 
      #{actualReturnAddress,jdbcType=VARCHAR}, #{actualReturnAddressLat,jdbcType=VARCHAR}, 
      #{actualReturnAddressLon,jdbcType=VARCHAR}, #{deliveryPersonId,jdbcType=INTEGER}, 
      #{deliveryPersonName,jdbcType=VARCHAR}, #{deliveryPersonPhone,jdbcType=VARCHAR}, 
      #{pickPersonId,jdbcType=INTEGER}, #{pickPersonName,jdbcType=VARCHAR}, #{pickPersonPhone,jdbcType=VARCHAR}, 
      #{useRemark,jdbcType=VARCHAR}, #{deliveryOverMileageAmount,jdbcType=DECIMAL}, #{returnOverMileageAmount,jdbcType=DECIMAL}, 
      #{totalOverMileageAmount,jdbcType=DECIMAL}, #{vehicleRentAmount,jdbcType=DECIMAL}, 
      #{totalAmount,jdbcType=DECIMAL}, #{taxTotalAmount,jdbcType=DECIMAL}, #{originalTotalAmount,jdbcType=DECIMAL}, 
      #{vehicleLicense,jdbcType=VARCHAR}, #{vehicleId,jdbcType=BIGINT}, #{vehicleVin,jdbcType=VARCHAR}, 
      #{vehicleSerialNoNewSystem,jdbcType=VARCHAR}, #{vehicleBrandCode,jdbcType=VARCHAR}, 
      #{vehicleBrand,jdbcType=VARCHAR}, #{brand,jdbcType=VARCHAR}, #{vehicleModelCode,jdbcType=VARCHAR}, 
      #{vehicleModel,jdbcType=VARCHAR}, #{engineNum,jdbcType=VARCHAR}, #{vehicleColor,jdbcType=VARCHAR}, 
      #{vehicleCompanyId,jdbcType=VARCHAR}, #{vehicleCompanyName,jdbcType=VARCHAR}, #{selfOwned,jdbcType=TINYINT}, 
      #{operateBussName,jdbcType=VARCHAR}, #{operateBussCode,jdbcType=VARCHAR}, #{belongBussCode,jdbcType=VARCHAR}, 
      #{belongBussName,jdbcType=VARCHAR}, #{assetCityCode,jdbcType=VARCHAR}, #{assetCityName,jdbcType=VARCHAR}, 
      #{structId,jdbcType=INTEGER}, #{structCode,jdbcType=VARCHAR}, #{structName,jdbcType=VARCHAR}, 
      #{belongCityCode,jdbcType=VARCHAR}, #{belongCityName,jdbcType=VARCHAR}, #{recordMemo,jdbcType=VARCHAR}, 
      #{updateMemo,jdbcType=VARCHAR}, #{cancelMemo,jdbcType=VARCHAR}, #{taxType,jdbcType=INTEGER}, 
      #{taxRate,jdbcType=DECIMAL}, #{depositFlag,jdbcType=BIT}, #{depositFreeType,jdbcType=TINYINT}, 
      #{depositAuthUrl,jdbcType=VARCHAR}, #{depositAuthQrCodeUrl,jdbcType=VARCHAR}, #{vehicleDeposit,jdbcType=DECIMAL}, 
      #{violationDeposit,jdbcType=DECIMAL}, #{orderPreauthTimeout,jdbcType=TIMESTAMP}, 
      #{depositFreeReason,jdbcType=VARCHAR}, #{carLevelId,jdbcType=INTEGER}, #{carLevelName,jdbcType=VARCHAR}, 
      #{chargeType,jdbcType=VARCHAR}, #{pickupDeliveryFee,jdbcType=DECIMAL}, #{priceConfigDetailId,jdbcType=INTEGER}, 
      #{configModelCode,jdbcType=VARCHAR}, #{configModelName,jdbcType=VARCHAR}, #{configBrandCode,jdbcType=VARCHAR}, 
      #{configBrandName,jdbcType=VARCHAR}, #{dayPrice,jdbcType=DECIMAL}, #{outMilePrice,jdbcType=DECIMAL}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.CoOrderInfo">
    <selectKey keyProperty="coOrderId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into co_order_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createId != null">
        create_id,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createMobile != null">
        create_mobile,
      </if>
      <if test="createCompanyId != null">
        create_company_id,
      </if>
      <if test="createCompanyName != null">
        create_company_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateId != null">
        update_id,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="updateMobile != null">
        update_mobile,
      </if>
      <if test="updateCompanyId != null">
        update_company_id,
      </if>
      <if test="updateCompanyName != null">
        update_company_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="delTag != null">
        del_tag,
      </if>
      <if test="orderNum != null">
        order_num,
      </if>
      <if test="thirdOrderNum != null">
        third_order_num,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="billStatus != null">
        bill_status,
      </if>
      <if test="orderSource != null">
        order_source,
      </if>
      <if test="actualUseTime != null">
        actual_use_time,
      </if>
      <if test="actualReturnTime != null">
        actual_return_time,
      </if>
      <if test="estimateUseDate != null">
        estimate_use_date,
      </if>
      <if test="estimateReturnDate != null">
        estimate_return_date,
      </if>
      <if test="actualUseCycle != null">
        actual_use_cycle,
      </if>
      <if test="estimateUseCycle != null">
        estimate_use_cycle,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="customerPhone != null">
        customer_phone,
      </if>
      <if test="customerIdCard != null">
        customer_id_card,
      </if>
      <if test="estimateUseCityCode != null">
        estimate_use_city_code,
      </if>
      <if test="estimateUseCityName != null">
        estimate_use_city_name,
      </if>
      <if test="estimateReturnCityCode != null">
        estimate_return_city_code,
      </if>
      <if test="estimateReturnCityName != null">
        estimate_return_city_name,
      </if>
      <if test="estimateUseAddress != null">
        estimate_use_address,
      </if>
      <if test="estimateUseAddressLat != null">
        estimate_use_address_lat,
      </if>
      <if test="estimateUseAddressLon != null">
        estimate_use_address_lon,
      </if>
      <if test="estimateReturnAddress != null">
        estimate_return_address,
      </if>
      <if test="estimateReturnAddressLat != null">
        estimate_return_address_lat,
      </if>
      <if test="estimateReturnAddressLon != null">
        estimate_return_address_lon,
      </if>
      <if test="actualUseCityCode != null">
        actual_use_city_code,
      </if>
      <if test="actualUseCityName != null">
        actual_use_city_name,
      </if>
      <if test="actualReturnCityCode != null">
        actual_return_city_code,
      </if>
      <if test="actualReturnCityName != null">
        actual_return_city_name,
      </if>
      <if test="actualUseAddress != null">
        actual_use_address,
      </if>
      <if test="actualUseAddressLat != null">
        actual_use_address_lat,
      </if>
      <if test="actualUseAddressLon != null">
        actual_use_address_lon,
      </if>
      <if test="actualReturnAddress != null">
        actual_return_address,
      </if>
      <if test="actualReturnAddressLat != null">
        actual_return_address_lat,
      </if>
      <if test="actualReturnAddressLon != null">
        actual_return_address_lon,
      </if>
      <if test="deliveryPersonId != null">
        delivery_person_id,
      </if>
      <if test="deliveryPersonName != null">
        delivery_person_name,
      </if>
      <if test="deliveryPersonPhone != null">
        delivery_person_phone,
      </if>
      <if test="pickPersonId != null">
        pick_person_id,
      </if>
      <if test="pickPersonName != null">
        pick_person_name,
      </if>
      <if test="pickPersonPhone != null">
        pick_person_phone,
      </if>
      <if test="useRemark != null">
        use_remark,
      </if>
      <if test="deliveryOverMileageAmount != null">
        delivery_over_mileage_amount,
      </if>
      <if test="returnOverMileageAmount != null">
        return_over_mileage_amount,
      </if>
      <if test="totalOverMileageAmount != null">
        total_over_mileage_amount,
      </if>
      <if test="vehicleRentAmount != null">
        vehicle_rent_amount,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="taxTotalAmount != null">
        tax_total_amount,
      </if>
      <if test="originalTotalAmount != null">
        original_total_amount,
      </if>
      <if test="vehicleLicense != null">
        vehicle_license,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="vehicleVin != null">
        vehicle_vin,
      </if>
      <if test="vehicleSerialNoNewSystem != null">
        vehicle_serial_no_new_system,
      </if>
      <if test="vehicleBrandCode != null">
        vehicle_brand_code,
      </if>
      <if test="vehicleBrand != null">
        vehicle_brand,
      </if>
      <if test="brand != null">
        brand,
      </if>
      <if test="vehicleModelCode != null">
        vehicle_model_code,
      </if>
      <if test="vehicleModel != null">
        vehicle_model,
      </if>
      <if test="engineNum != null">
        engine_num,
      </if>
      <if test="vehicleColor != null">
        vehicle_color,
      </if>
      <if test="vehicleCompanyId != null">
        vehicle_company_id,
      </if>
      <if test="vehicleCompanyName != null">
        vehicle_company_name,
      </if>
      <if test="selfOwned != null">
        self_owned,
      </if>
      <if test="operateBussName != null">
        operate_buss_name,
      </if>
      <if test="operateBussCode != null">
        operate_buss_code,
      </if>
      <if test="belongBussCode != null">
        belong_buss_code,
      </if>
      <if test="belongBussName != null">
        belong_buss_name,
      </if>
      <if test="assetCityCode != null">
        asset_city_code,
      </if>
      <if test="assetCityName != null">
        asset_city_name,
      </if>
      <if test="structId != null">
        struct_id,
      </if>
      <if test="structCode != null">
        struct_code,
      </if>
      <if test="structName != null">
        struct_name,
      </if>
      <if test="belongCityCode != null">
        belong_city_code,
      </if>
      <if test="belongCityName != null">
        belong_city_name,
      </if>
      <if test="recordMemo != null">
        record_memo,
      </if>
      <if test="updateMemo != null">
        update_memo,
      </if>
      <if test="cancelMemo != null">
        cancel_memo,
      </if>
      <if test="taxType != null">
        tax_type,
      </if>
      <if test="taxRate != null">
        tax_rate,
      </if>
      <if test="depositFlag != null">
        deposit_flag,
      </if>
      <if test="depositFreeType != null">
        deposit_free_type,
      </if>
      <if test="depositAuthUrl != null">
        deposit_auth_url,
      </if>
      <if test="depositAuthQrCodeUrl != null">
        deposit_auth_qr_code_url,
      </if>
      <if test="vehicleDeposit != null">
        vehicle_deposit,
      </if>
      <if test="violationDeposit != null">
        violation_deposit,
      </if>
      <if test="orderPreauthTimeout != null">
        order_preauth_timeout,
      </if>
      <if test="depositFreeReason != null">
        deposit_free_reason,
      </if>
      <if test="carLevelId != null">
        car_level_id,
      </if>
      <if test="carLevelName != null">
        car_level_name,
      </if>
      <if test="chargeType != null">
        charge_type,
      </if>
      <if test="pickupDeliveryFee != null">
        pickup_delivery_fee,
      </if>
      <if test="priceConfigDetailId != null">
        price_config_detail_id,
      </if>
      <if test="configModelCode != null">
        config_model_code,
      </if>
      <if test="configModelName != null">
        config_model_name,
      </if>
      <if test="configBrandCode != null">
        config_brand_code,
      </if>
      <if test="configBrandName != null">
        config_brand_name,
      </if>
      <if test="dayPrice != null">
        day_price,
      </if>
      <if test="outMilePrice != null">
        out_mile_price,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createId != null">
        #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createMobile != null">
        #{createMobile,jdbcType=VARCHAR},
      </if>
      <if test="createCompanyId != null">
        #{createCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="createCompanyName != null">
        #{createCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateMobile != null">
        #{updateMobile,jdbcType=VARCHAR},
      </if>
      <if test="updateCompanyId != null">
        #{updateCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="updateCompanyName != null">
        #{updateCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delTag != null">
        #{delTag,jdbcType=TINYINT},
      </if>
      <if test="orderNum != null">
        #{orderNum,jdbcType=VARCHAR},
      </if>
      <if test="thirdOrderNum != null">
        #{thirdOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="billStatus != null">
        #{billStatus,jdbcType=INTEGER},
      </if>
      <if test="orderSource != null">
        #{orderSource,jdbcType=TINYINT},
      </if>
      <if test="actualUseTime != null">
        #{actualUseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="actualReturnTime != null">
        #{actualReturnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="estimateUseDate != null">
        #{estimateUseDate,jdbcType=TIMESTAMP},
      </if>
      <if test="estimateReturnDate != null">
        #{estimateReturnDate,jdbcType=TIMESTAMP},
      </if>
      <if test="actualUseCycle != null">
        #{actualUseCycle,jdbcType=INTEGER},
      </if>
      <if test="estimateUseCycle != null">
        #{estimateUseCycle,jdbcType=INTEGER},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=TINYINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerPhone != null">
        #{customerPhone,jdbcType=VARCHAR},
      </if>
      <if test="customerIdCard != null">
        #{customerIdCard,jdbcType=VARCHAR},
      </if>
      <if test="estimateUseCityCode != null">
        #{estimateUseCityCode,jdbcType=VARCHAR},
      </if>
      <if test="estimateUseCityName != null">
        #{estimateUseCityName,jdbcType=VARCHAR},
      </if>
      <if test="estimateReturnCityCode != null">
        #{estimateReturnCityCode,jdbcType=VARCHAR},
      </if>
      <if test="estimateReturnCityName != null">
        #{estimateReturnCityName,jdbcType=VARCHAR},
      </if>
      <if test="estimateUseAddress != null">
        #{estimateUseAddress,jdbcType=VARCHAR},
      </if>
      <if test="estimateUseAddressLat != null">
        #{estimateUseAddressLat,jdbcType=VARCHAR},
      </if>
      <if test="estimateUseAddressLon != null">
        #{estimateUseAddressLon,jdbcType=VARCHAR},
      </if>
      <if test="estimateReturnAddress != null">
        #{estimateReturnAddress,jdbcType=VARCHAR},
      </if>
      <if test="estimateReturnAddressLat != null">
        #{estimateReturnAddressLat,jdbcType=VARCHAR},
      </if>
      <if test="estimateReturnAddressLon != null">
        #{estimateReturnAddressLon,jdbcType=VARCHAR},
      </if>
      <if test="actualUseCityCode != null">
        #{actualUseCityCode,jdbcType=VARCHAR},
      </if>
      <if test="actualUseCityName != null">
        #{actualUseCityName,jdbcType=VARCHAR},
      </if>
      <if test="actualReturnCityCode != null">
        #{actualReturnCityCode,jdbcType=VARCHAR},
      </if>
      <if test="actualReturnCityName != null">
        #{actualReturnCityName,jdbcType=VARCHAR},
      </if>
      <if test="actualUseAddress != null">
        #{actualUseAddress,jdbcType=VARCHAR},
      </if>
      <if test="actualUseAddressLat != null">
        #{actualUseAddressLat,jdbcType=VARCHAR},
      </if>
      <if test="actualUseAddressLon != null">
        #{actualUseAddressLon,jdbcType=VARCHAR},
      </if>
      <if test="actualReturnAddress != null">
        #{actualReturnAddress,jdbcType=VARCHAR},
      </if>
      <if test="actualReturnAddressLat != null">
        #{actualReturnAddressLat,jdbcType=VARCHAR},
      </if>
      <if test="actualReturnAddressLon != null">
        #{actualReturnAddressLon,jdbcType=VARCHAR},
      </if>
      <if test="deliveryPersonId != null">
        #{deliveryPersonId,jdbcType=INTEGER},
      </if>
      <if test="deliveryPersonName != null">
        #{deliveryPersonName,jdbcType=VARCHAR},
      </if>
      <if test="deliveryPersonPhone != null">
        #{deliveryPersonPhone,jdbcType=VARCHAR},
      </if>
      <if test="pickPersonId != null">
        #{pickPersonId,jdbcType=INTEGER},
      </if>
      <if test="pickPersonName != null">
        #{pickPersonName,jdbcType=VARCHAR},
      </if>
      <if test="pickPersonPhone != null">
        #{pickPersonPhone,jdbcType=VARCHAR},
      </if>
      <if test="useRemark != null">
        #{useRemark,jdbcType=VARCHAR},
      </if>
      <if test="deliveryOverMileageAmount != null">
        #{deliveryOverMileageAmount,jdbcType=DECIMAL},
      </if>
      <if test="returnOverMileageAmount != null">
        #{returnOverMileageAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalOverMileageAmount != null">
        #{totalOverMileageAmount,jdbcType=DECIMAL},
      </if>
      <if test="vehicleRentAmount != null">
        #{vehicleRentAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxTotalAmount != null">
        #{taxTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="originalTotalAmount != null">
        #{originalTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="vehicleLicense != null">
        #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleVin != null">
        #{vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="vehicleSerialNoNewSystem != null">
        #{vehicleSerialNoNewSystem,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBrandCode != null">
        #{vehicleBrandCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBrand != null">
        #{vehicleBrand,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        #{brand,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelCode != null">
        #{vehicleModelCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModel != null">
        #{vehicleModel,jdbcType=VARCHAR},
      </if>
      <if test="engineNum != null">
        #{engineNum,jdbcType=VARCHAR},
      </if>
      <if test="vehicleColor != null">
        #{vehicleColor,jdbcType=VARCHAR},
      </if>
      <if test="vehicleCompanyId != null">
        #{vehicleCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="vehicleCompanyName != null">
        #{vehicleCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="selfOwned != null">
        #{selfOwned,jdbcType=TINYINT},
      </if>
      <if test="operateBussName != null">
        #{operateBussName,jdbcType=VARCHAR},
      </if>
      <if test="operateBussCode != null">
        #{operateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="belongBussCode != null">
        #{belongBussCode,jdbcType=VARCHAR},
      </if>
      <if test="belongBussName != null">
        #{belongBussName,jdbcType=VARCHAR},
      </if>
      <if test="assetCityCode != null">
        #{assetCityCode,jdbcType=VARCHAR},
      </if>
      <if test="assetCityName != null">
        #{assetCityName,jdbcType=VARCHAR},
      </if>
      <if test="structId != null">
        #{structId,jdbcType=INTEGER},
      </if>
      <if test="structCode != null">
        #{structCode,jdbcType=VARCHAR},
      </if>
      <if test="structName != null">
        #{structName,jdbcType=VARCHAR},
      </if>
      <if test="belongCityCode != null">
        #{belongCityCode,jdbcType=VARCHAR},
      </if>
      <if test="belongCityName != null">
        #{belongCityName,jdbcType=VARCHAR},
      </if>
      <if test="recordMemo != null">
        #{recordMemo,jdbcType=VARCHAR},
      </if>
      <if test="updateMemo != null">
        #{updateMemo,jdbcType=VARCHAR},
      </if>
      <if test="cancelMemo != null">
        #{cancelMemo,jdbcType=VARCHAR},
      </if>
      <if test="taxType != null">
        #{taxType,jdbcType=INTEGER},
      </if>
      <if test="taxRate != null">
        #{taxRate,jdbcType=DECIMAL},
      </if>
      <if test="depositFlag != null">
        #{depositFlag,jdbcType=BIT},
      </if>
      <if test="depositFreeType != null">
        #{depositFreeType,jdbcType=TINYINT},
      </if>
      <if test="depositAuthUrl != null">
        #{depositAuthUrl,jdbcType=VARCHAR},
      </if>
      <if test="depositAuthQrCodeUrl != null">
        #{depositAuthQrCodeUrl,jdbcType=VARCHAR},
      </if>
      <if test="vehicleDeposit != null">
        #{vehicleDeposit,jdbcType=DECIMAL},
      </if>
      <if test="violationDeposit != null">
        #{violationDeposit,jdbcType=DECIMAL},
      </if>
      <if test="orderPreauthTimeout != null">
        #{orderPreauthTimeout,jdbcType=TIMESTAMP},
      </if>
      <if test="depositFreeReason != null">
        #{depositFreeReason,jdbcType=VARCHAR},
      </if>
      <if test="carLevelId != null">
        #{carLevelId,jdbcType=INTEGER},
      </if>
      <if test="carLevelName != null">
        #{carLevelName,jdbcType=VARCHAR},
      </if>
      <if test="chargeType != null">
        #{chargeType,jdbcType=VARCHAR},
      </if>
      <if test="pickupDeliveryFee != null">
        #{pickupDeliveryFee,jdbcType=DECIMAL},
      </if>
      <if test="priceConfigDetailId != null">
        #{priceConfigDetailId,jdbcType=INTEGER},
      </if>
      <if test="configModelCode != null">
        #{configModelCode,jdbcType=VARCHAR},
      </if>
      <if test="configModelName != null">
        #{configModelName,jdbcType=VARCHAR},
      </if>
      <if test="configBrandCode != null">
        #{configBrandCode,jdbcType=VARCHAR},
      </if>
      <if test="configBrandName != null">
        #{configBrandName,jdbcType=VARCHAR},
      </if>
      <if test="dayPrice != null">
        #{dayPrice,jdbcType=DECIMAL},
      </if>
      <if test="outMilePrice != null">
        #{outMilePrice,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.CoOrderInfoExample" resultType="java.lang.Long">
    select count(*) from co_order_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update co_order_info
    <set>
      <if test="row.coOrderId != null">
        co_order_id = #{row.coOrderId,jdbcType=INTEGER},
      </if>
      <if test="row.createId != null">
        create_id = #{row.createId,jdbcType=INTEGER},
      </if>
      <if test="row.createName != null">
        create_name = #{row.createName,jdbcType=VARCHAR},
      </if>
      <if test="row.createMobile != null">
        create_mobile = #{row.createMobile,jdbcType=VARCHAR},
      </if>
      <if test="row.createCompanyId != null">
        create_company_id = #{row.createCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="row.createCompanyName != null">
        create_company_name = #{row.createCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateId != null">
        update_id = #{row.updateId,jdbcType=INTEGER},
      </if>
      <if test="row.updateName != null">
        update_name = #{row.updateName,jdbcType=VARCHAR},
      </if>
      <if test="row.updateMobile != null">
        update_mobile = #{row.updateMobile,jdbcType=VARCHAR},
      </if>
      <if test="row.updateCompanyId != null">
        update_company_id = #{row.updateCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="row.updateCompanyName != null">
        update_company_name = #{row.updateCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.delTag != null">
        del_tag = #{row.delTag,jdbcType=TINYINT},
      </if>
      <if test="row.orderNum != null">
        order_num = #{row.orderNum,jdbcType=VARCHAR},
      </if>
      <if test="row.thirdOrderNum != null">
        third_order_num = #{row.thirdOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="row.orderStatus != null">
        order_status = #{row.orderStatus,jdbcType=INTEGER},
      </if>
      <if test="row.billStatus != null">
        bill_status = #{row.billStatus,jdbcType=INTEGER},
      </if>
      <if test="row.orderSource != null">
        order_source = #{row.orderSource,jdbcType=TINYINT},
      </if>
      <if test="row.actualUseTime != null">
        actual_use_time = #{row.actualUseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.actualReturnTime != null">
        actual_return_time = #{row.actualReturnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.estimateUseDate != null">
        estimate_use_date = #{row.estimateUseDate,jdbcType=TIMESTAMP},
      </if>
      <if test="row.estimateReturnDate != null">
        estimate_return_date = #{row.estimateReturnDate,jdbcType=TIMESTAMP},
      </if>
      <if test="row.actualUseCycle != null">
        actual_use_cycle = #{row.actualUseCycle,jdbcType=INTEGER},
      </if>
      <if test="row.estimateUseCycle != null">
        estimate_use_cycle = #{row.estimateUseCycle,jdbcType=INTEGER},
      </if>
      <if test="row.orderType != null">
        order_type = #{row.orderType,jdbcType=TINYINT},
      </if>
      <if test="row.companyId != null">
        company_id = #{row.companyId,jdbcType=INTEGER},
      </if>
      <if test="row.companyCode != null">
        company_code = #{row.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="row.companyName != null">
        company_name = #{row.companyName,jdbcType=VARCHAR},
      </if>
      <if test="row.customerName != null">
        customer_name = #{row.customerName,jdbcType=VARCHAR},
      </if>
      <if test="row.customerPhone != null">
        customer_phone = #{row.customerPhone,jdbcType=VARCHAR},
      </if>
      <if test="row.customerIdCard != null">
        customer_id_card = #{row.customerIdCard,jdbcType=VARCHAR},
      </if>
      <if test="row.estimateUseCityCode != null">
        estimate_use_city_code = #{row.estimateUseCityCode,jdbcType=VARCHAR},
      </if>
      <if test="row.estimateUseCityName != null">
        estimate_use_city_name = #{row.estimateUseCityName,jdbcType=VARCHAR},
      </if>
      <if test="row.estimateReturnCityCode != null">
        estimate_return_city_code = #{row.estimateReturnCityCode,jdbcType=VARCHAR},
      </if>
      <if test="row.estimateReturnCityName != null">
        estimate_return_city_name = #{row.estimateReturnCityName,jdbcType=VARCHAR},
      </if>
      <if test="row.estimateUseAddress != null">
        estimate_use_address = #{row.estimateUseAddress,jdbcType=VARCHAR},
      </if>
      <if test="row.estimateUseAddressLat != null">
        estimate_use_address_lat = #{row.estimateUseAddressLat,jdbcType=VARCHAR},
      </if>
      <if test="row.estimateUseAddressLon != null">
        estimate_use_address_lon = #{row.estimateUseAddressLon,jdbcType=VARCHAR},
      </if>
      <if test="row.estimateReturnAddress != null">
        estimate_return_address = #{row.estimateReturnAddress,jdbcType=VARCHAR},
      </if>
      <if test="row.estimateReturnAddressLat != null">
        estimate_return_address_lat = #{row.estimateReturnAddressLat,jdbcType=VARCHAR},
      </if>
      <if test="row.estimateReturnAddressLon != null">
        estimate_return_address_lon = #{row.estimateReturnAddressLon,jdbcType=VARCHAR},
      </if>
      <if test="row.actualUseCityCode != null">
        actual_use_city_code = #{row.actualUseCityCode,jdbcType=VARCHAR},
      </if>
      <if test="row.actualUseCityName != null">
        actual_use_city_name = #{row.actualUseCityName,jdbcType=VARCHAR},
      </if>
      <if test="row.actualReturnCityCode != null">
        actual_return_city_code = #{row.actualReturnCityCode,jdbcType=VARCHAR},
      </if>
      <if test="row.actualReturnCityName != null">
        actual_return_city_name = #{row.actualReturnCityName,jdbcType=VARCHAR},
      </if>
      <if test="row.actualUseAddress != null">
        actual_use_address = #{row.actualUseAddress,jdbcType=VARCHAR},
      </if>
      <if test="row.actualUseAddressLat != null">
        actual_use_address_lat = #{row.actualUseAddressLat,jdbcType=VARCHAR},
      </if>
      <if test="row.actualUseAddressLon != null">
        actual_use_address_lon = #{row.actualUseAddressLon,jdbcType=VARCHAR},
      </if>
      <if test="row.actualReturnAddress != null">
        actual_return_address = #{row.actualReturnAddress,jdbcType=VARCHAR},
      </if>
      <if test="row.actualReturnAddressLat != null">
        actual_return_address_lat = #{row.actualReturnAddressLat,jdbcType=VARCHAR},
      </if>
      <if test="row.actualReturnAddressLon != null">
        actual_return_address_lon = #{row.actualReturnAddressLon,jdbcType=VARCHAR},
      </if>
      <if test="row.deliveryPersonId != null">
        delivery_person_id = #{row.deliveryPersonId,jdbcType=INTEGER},
      </if>
      <if test="row.deliveryPersonName != null">
        delivery_person_name = #{row.deliveryPersonName,jdbcType=VARCHAR},
      </if>
      <if test="row.deliveryPersonPhone != null">
        delivery_person_phone = #{row.deliveryPersonPhone,jdbcType=VARCHAR},
      </if>
      <if test="row.pickPersonId != null">
        pick_person_id = #{row.pickPersonId,jdbcType=INTEGER},
      </if>
      <if test="row.pickPersonName != null">
        pick_person_name = #{row.pickPersonName,jdbcType=VARCHAR},
      </if>
      <if test="row.pickPersonPhone != null">
        pick_person_phone = #{row.pickPersonPhone,jdbcType=VARCHAR},
      </if>
      <if test="row.useRemark != null">
        use_remark = #{row.useRemark,jdbcType=VARCHAR},
      </if>
      <if test="row.deliveryOverMileageAmount != null">
        delivery_over_mileage_amount = #{row.deliveryOverMileageAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.returnOverMileageAmount != null">
        return_over_mileage_amount = #{row.returnOverMileageAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.totalOverMileageAmount != null">
        total_over_mileage_amount = #{row.totalOverMileageAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.vehicleRentAmount != null">
        vehicle_rent_amount = #{row.vehicleRentAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.totalAmount != null">
        total_amount = #{row.totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.taxTotalAmount != null">
        tax_total_amount = #{row.taxTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.originalTotalAmount != null">
        original_total_amount = #{row.originalTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.vehicleLicense != null">
        vehicle_license = #{row.vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleId != null">
        vehicle_id = #{row.vehicleId,jdbcType=BIGINT},
      </if>
      <if test="row.vehicleVin != null">
        vehicle_vin = #{row.vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleSerialNoNewSystem != null">
        vehicle_serial_no_new_system = #{row.vehicleSerialNoNewSystem,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleBrandCode != null">
        vehicle_brand_code = #{row.vehicleBrandCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleBrand != null">
        vehicle_brand = #{row.vehicleBrand,jdbcType=VARCHAR},
      </if>
      <if test="row.brand != null">
        brand = #{row.brand,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleModelCode != null">
        vehicle_model_code = #{row.vehicleModelCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleModel != null">
        vehicle_model = #{row.vehicleModel,jdbcType=VARCHAR},
      </if>
      <if test="row.engineNum != null">
        engine_num = #{row.engineNum,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleColor != null">
        vehicle_color = #{row.vehicleColor,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleCompanyId != null">
        vehicle_company_id = #{row.vehicleCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleCompanyName != null">
        vehicle_company_name = #{row.vehicleCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="row.selfOwned != null">
        self_owned = #{row.selfOwned,jdbcType=TINYINT},
      </if>
      <if test="row.operateBussName != null">
        operate_buss_name = #{row.operateBussName,jdbcType=VARCHAR},
      </if>
      <if test="row.operateBussCode != null">
        operate_buss_code = #{row.operateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="row.belongBussCode != null">
        belong_buss_code = #{row.belongBussCode,jdbcType=VARCHAR},
      </if>
      <if test="row.belongBussName != null">
        belong_buss_name = #{row.belongBussName,jdbcType=VARCHAR},
      </if>
      <if test="row.assetCityCode != null">
        asset_city_code = #{row.assetCityCode,jdbcType=VARCHAR},
      </if>
      <if test="row.assetCityName != null">
        asset_city_name = #{row.assetCityName,jdbcType=VARCHAR},
      </if>
      <if test="row.structId != null">
        struct_id = #{row.structId,jdbcType=INTEGER},
      </if>
      <if test="row.structCode != null">
        struct_code = #{row.structCode,jdbcType=VARCHAR},
      </if>
      <if test="row.structName != null">
        struct_name = #{row.structName,jdbcType=VARCHAR},
      </if>
      <if test="row.belongCityCode != null">
        belong_city_code = #{row.belongCityCode,jdbcType=VARCHAR},
      </if>
      <if test="row.belongCityName != null">
        belong_city_name = #{row.belongCityName,jdbcType=VARCHAR},
      </if>
      <if test="row.recordMemo != null">
        record_memo = #{row.recordMemo,jdbcType=VARCHAR},
      </if>
      <if test="row.updateMemo != null">
        update_memo = #{row.updateMemo,jdbcType=VARCHAR},
      </if>
      <if test="row.cancelMemo != null">
        cancel_memo = #{row.cancelMemo,jdbcType=VARCHAR},
      </if>
      <if test="row.taxType != null">
        tax_type = #{row.taxType,jdbcType=INTEGER},
      </if>
      <if test="row.taxRate != null">
        tax_rate = #{row.taxRate,jdbcType=DECIMAL},
      </if>
      <if test="row.depositFlag != null">
        deposit_flag = #{row.depositFlag,jdbcType=BIT},
      </if>
      <if test="row.depositFreeType != null">
        deposit_free_type = #{row.depositFreeType,jdbcType=TINYINT},
      </if>
      <if test="row.depositAuthUrl != null">
        deposit_auth_url = #{row.depositAuthUrl,jdbcType=VARCHAR},
      </if>
      <if test="row.depositAuthQrCodeUrl != null">
        deposit_auth_qr_code_url = #{row.depositAuthQrCodeUrl,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleDeposit != null">
        vehicle_deposit = #{row.vehicleDeposit,jdbcType=DECIMAL},
      </if>
      <if test="row.violationDeposit != null">
        violation_deposit = #{row.violationDeposit,jdbcType=DECIMAL},
      </if>
      <if test="row.orderPreauthTimeout != null">
        order_preauth_timeout = #{row.orderPreauthTimeout,jdbcType=TIMESTAMP},
      </if>
      <if test="row.depositFreeReason != null">
        deposit_free_reason = #{row.depositFreeReason,jdbcType=VARCHAR},
      </if>
      <if test="row.carLevelId != null">
        car_level_id = #{row.carLevelId,jdbcType=INTEGER},
      </if>
      <if test="row.carLevelName != null">
        car_level_name = #{row.carLevelName,jdbcType=VARCHAR},
      </if>
      <if test="row.chargeType != null">
        charge_type = #{row.chargeType,jdbcType=VARCHAR},
      </if>
      <if test="row.pickupDeliveryFee != null">
        pickup_delivery_fee = #{row.pickupDeliveryFee,jdbcType=DECIMAL},
      </if>
      <if test="row.priceConfigDetailId != null">
        price_config_detail_id = #{row.priceConfigDetailId,jdbcType=INTEGER},
      </if>
      <if test="row.configModelCode != null">
        config_model_code = #{row.configModelCode,jdbcType=VARCHAR},
      </if>
      <if test="row.configModelName != null">
        config_model_name = #{row.configModelName,jdbcType=VARCHAR},
      </if>
      <if test="row.configBrandCode != null">
        config_brand_code = #{row.configBrandCode,jdbcType=VARCHAR},
      </if>
      <if test="row.configBrandName != null">
        config_brand_name = #{row.configBrandName,jdbcType=VARCHAR},
      </if>
      <if test="row.dayPrice != null">
        day_price = #{row.dayPrice,jdbcType=DECIMAL},
      </if>
      <if test="row.outMilePrice != null">
        out_mile_price = #{row.outMilePrice,jdbcType=DECIMAL},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update co_order_info
    set co_order_id = #{row.coOrderId,jdbcType=INTEGER},
      create_id = #{row.createId,jdbcType=INTEGER},
      create_name = #{row.createName,jdbcType=VARCHAR},
      create_mobile = #{row.createMobile,jdbcType=VARCHAR},
      create_company_id = #{row.createCompanyId,jdbcType=VARCHAR},
      create_company_name = #{row.createCompanyName,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_id = #{row.updateId,jdbcType=INTEGER},
      update_name = #{row.updateName,jdbcType=VARCHAR},
      update_mobile = #{row.updateMobile,jdbcType=VARCHAR},
      update_company_id = #{row.updateCompanyId,jdbcType=VARCHAR},
      update_company_name = #{row.updateCompanyName,jdbcType=VARCHAR},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      del_tag = #{row.delTag,jdbcType=TINYINT},
      order_num = #{row.orderNum,jdbcType=VARCHAR},
      third_order_num = #{row.thirdOrderNum,jdbcType=VARCHAR},
      order_status = #{row.orderStatus,jdbcType=INTEGER},
      bill_status = #{row.billStatus,jdbcType=INTEGER},
      order_source = #{row.orderSource,jdbcType=TINYINT},
      actual_use_time = #{row.actualUseTime,jdbcType=TIMESTAMP},
      actual_return_time = #{row.actualReturnTime,jdbcType=TIMESTAMP},
      estimate_use_date = #{row.estimateUseDate,jdbcType=TIMESTAMP},
      estimate_return_date = #{row.estimateReturnDate,jdbcType=TIMESTAMP},
      actual_use_cycle = #{row.actualUseCycle,jdbcType=INTEGER},
      estimate_use_cycle = #{row.estimateUseCycle,jdbcType=INTEGER},
      order_type = #{row.orderType,jdbcType=TINYINT},
      company_id = #{row.companyId,jdbcType=INTEGER},
      company_code = #{row.companyCode,jdbcType=VARCHAR},
      company_name = #{row.companyName,jdbcType=VARCHAR},
      customer_name = #{row.customerName,jdbcType=VARCHAR},
      customer_phone = #{row.customerPhone,jdbcType=VARCHAR},
      customer_id_card = #{row.customerIdCard,jdbcType=VARCHAR},
      estimate_use_city_code = #{row.estimateUseCityCode,jdbcType=VARCHAR},
      estimate_use_city_name = #{row.estimateUseCityName,jdbcType=VARCHAR},
      estimate_return_city_code = #{row.estimateReturnCityCode,jdbcType=VARCHAR},
      estimate_return_city_name = #{row.estimateReturnCityName,jdbcType=VARCHAR},
      estimate_use_address = #{row.estimateUseAddress,jdbcType=VARCHAR},
      estimate_use_address_lat = #{row.estimateUseAddressLat,jdbcType=VARCHAR},
      estimate_use_address_lon = #{row.estimateUseAddressLon,jdbcType=VARCHAR},
      estimate_return_address = #{row.estimateReturnAddress,jdbcType=VARCHAR},
      estimate_return_address_lat = #{row.estimateReturnAddressLat,jdbcType=VARCHAR},
      estimate_return_address_lon = #{row.estimateReturnAddressLon,jdbcType=VARCHAR},
      actual_use_city_code = #{row.actualUseCityCode,jdbcType=VARCHAR},
      actual_use_city_name = #{row.actualUseCityName,jdbcType=VARCHAR},
      actual_return_city_code = #{row.actualReturnCityCode,jdbcType=VARCHAR},
      actual_return_city_name = #{row.actualReturnCityName,jdbcType=VARCHAR},
      actual_use_address = #{row.actualUseAddress,jdbcType=VARCHAR},
      actual_use_address_lat = #{row.actualUseAddressLat,jdbcType=VARCHAR},
      actual_use_address_lon = #{row.actualUseAddressLon,jdbcType=VARCHAR},
      actual_return_address = #{row.actualReturnAddress,jdbcType=VARCHAR},
      actual_return_address_lat = #{row.actualReturnAddressLat,jdbcType=VARCHAR},
      actual_return_address_lon = #{row.actualReturnAddressLon,jdbcType=VARCHAR},
      delivery_person_id = #{row.deliveryPersonId,jdbcType=INTEGER},
      delivery_person_name = #{row.deliveryPersonName,jdbcType=VARCHAR},
      delivery_person_phone = #{row.deliveryPersonPhone,jdbcType=VARCHAR},
      pick_person_id = #{row.pickPersonId,jdbcType=INTEGER},
      pick_person_name = #{row.pickPersonName,jdbcType=VARCHAR},
      pick_person_phone = #{row.pickPersonPhone,jdbcType=VARCHAR},
      use_remark = #{row.useRemark,jdbcType=VARCHAR},
      delivery_over_mileage_amount = #{row.deliveryOverMileageAmount,jdbcType=DECIMAL},
      return_over_mileage_amount = #{row.returnOverMileageAmount,jdbcType=DECIMAL},
      total_over_mileage_amount = #{row.totalOverMileageAmount,jdbcType=DECIMAL},
      vehicle_rent_amount = #{row.vehicleRentAmount,jdbcType=DECIMAL},
      total_amount = #{row.totalAmount,jdbcType=DECIMAL},
      tax_total_amount = #{row.taxTotalAmount,jdbcType=DECIMAL},
      original_total_amount = #{row.originalTotalAmount,jdbcType=DECIMAL},
      vehicle_license = #{row.vehicleLicense,jdbcType=VARCHAR},
      vehicle_id = #{row.vehicleId,jdbcType=BIGINT},
      vehicle_vin = #{row.vehicleVin,jdbcType=VARCHAR},
      vehicle_serial_no_new_system = #{row.vehicleSerialNoNewSystem,jdbcType=VARCHAR},
      vehicle_brand_code = #{row.vehicleBrandCode,jdbcType=VARCHAR},
      vehicle_brand = #{row.vehicleBrand,jdbcType=VARCHAR},
      brand = #{row.brand,jdbcType=VARCHAR},
      vehicle_model_code = #{row.vehicleModelCode,jdbcType=VARCHAR},
      vehicle_model = #{row.vehicleModel,jdbcType=VARCHAR},
      engine_num = #{row.engineNum,jdbcType=VARCHAR},
      vehicle_color = #{row.vehicleColor,jdbcType=VARCHAR},
      vehicle_company_id = #{row.vehicleCompanyId,jdbcType=VARCHAR},
      vehicle_company_name = #{row.vehicleCompanyName,jdbcType=VARCHAR},
      self_owned = #{row.selfOwned,jdbcType=TINYINT},
      operate_buss_name = #{row.operateBussName,jdbcType=VARCHAR},
      operate_buss_code = #{row.operateBussCode,jdbcType=VARCHAR},
      belong_buss_code = #{row.belongBussCode,jdbcType=VARCHAR},
      belong_buss_name = #{row.belongBussName,jdbcType=VARCHAR},
      asset_city_code = #{row.assetCityCode,jdbcType=VARCHAR},
      asset_city_name = #{row.assetCityName,jdbcType=VARCHAR},
      struct_id = #{row.structId,jdbcType=INTEGER},
      struct_code = #{row.structCode,jdbcType=VARCHAR},
      struct_name = #{row.structName,jdbcType=VARCHAR},
      belong_city_code = #{row.belongCityCode,jdbcType=VARCHAR},
      belong_city_name = #{row.belongCityName,jdbcType=VARCHAR},
      record_memo = #{row.recordMemo,jdbcType=VARCHAR},
      update_memo = #{row.updateMemo,jdbcType=VARCHAR},
      cancel_memo = #{row.cancelMemo,jdbcType=VARCHAR},
      tax_type = #{row.taxType,jdbcType=INTEGER},
      tax_rate = #{row.taxRate,jdbcType=DECIMAL},
      deposit_flag = #{row.depositFlag,jdbcType=BIT},
      deposit_free_type = #{row.depositFreeType,jdbcType=TINYINT},
      deposit_auth_url = #{row.depositAuthUrl,jdbcType=VARCHAR},
      deposit_auth_qr_code_url = #{row.depositAuthQrCodeUrl,jdbcType=VARCHAR},
      vehicle_deposit = #{row.vehicleDeposit,jdbcType=DECIMAL},
      violation_deposit = #{row.violationDeposit,jdbcType=DECIMAL},
      order_preauth_timeout = #{row.orderPreauthTimeout,jdbcType=TIMESTAMP},
      deposit_free_reason = #{row.depositFreeReason,jdbcType=VARCHAR},
      car_level_id = #{row.carLevelId,jdbcType=INTEGER},
      car_level_name = #{row.carLevelName,jdbcType=VARCHAR},
      charge_type = #{row.chargeType,jdbcType=VARCHAR},
      pickup_delivery_fee = #{row.pickupDeliveryFee,jdbcType=DECIMAL},
      price_config_detail_id = #{row.priceConfigDetailId,jdbcType=INTEGER},
      config_model_code = #{row.configModelCode,jdbcType=VARCHAR},
      config_model_name = #{row.configModelName,jdbcType=VARCHAR},
      config_brand_code = #{row.configBrandCode,jdbcType=VARCHAR},
      config_brand_name = #{row.configBrandName,jdbcType=VARCHAR},
      day_price = #{row.dayPrice,jdbcType=DECIMAL},
      out_mile_price = #{row.outMilePrice,jdbcType=DECIMAL}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.CoOrderInfo">
    update co_order_info
    <set>
      <if test="createId != null">
        create_id = #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createMobile != null">
        create_mobile = #{createMobile,jdbcType=VARCHAR},
      </if>
      <if test="createCompanyId != null">
        create_company_id = #{createCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="createCompanyName != null">
        create_company_name = #{createCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        update_id = #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateMobile != null">
        update_mobile = #{updateMobile,jdbcType=VARCHAR},
      </if>
      <if test="updateCompanyId != null">
        update_company_id = #{updateCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="updateCompanyName != null">
        update_company_name = #{updateCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delTag != null">
        del_tag = #{delTag,jdbcType=TINYINT},
      </if>
      <if test="orderNum != null">
        order_num = #{orderNum,jdbcType=VARCHAR},
      </if>
      <if test="thirdOrderNum != null">
        third_order_num = #{thirdOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="billStatus != null">
        bill_status = #{billStatus,jdbcType=INTEGER},
      </if>
      <if test="orderSource != null">
        order_source = #{orderSource,jdbcType=TINYINT},
      </if>
      <if test="actualUseTime != null">
        actual_use_time = #{actualUseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="actualReturnTime != null">
        actual_return_time = #{actualReturnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="estimateUseDate != null">
        estimate_use_date = #{estimateUseDate,jdbcType=TIMESTAMP},
      </if>
      <if test="estimateReturnDate != null">
        estimate_return_date = #{estimateReturnDate,jdbcType=TIMESTAMP},
      </if>
      <if test="actualUseCycle != null">
        actual_use_cycle = #{actualUseCycle,jdbcType=INTEGER},
      </if>
      <if test="estimateUseCycle != null">
        estimate_use_cycle = #{estimateUseCycle,jdbcType=INTEGER},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=TINYINT},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null">
        customer_name = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerPhone != null">
        customer_phone = #{customerPhone,jdbcType=VARCHAR},
      </if>
      <if test="customerIdCard != null">
        customer_id_card = #{customerIdCard,jdbcType=VARCHAR},
      </if>
      <if test="estimateUseCityCode != null">
        estimate_use_city_code = #{estimateUseCityCode,jdbcType=VARCHAR},
      </if>
      <if test="estimateUseCityName != null">
        estimate_use_city_name = #{estimateUseCityName,jdbcType=VARCHAR},
      </if>
      <if test="estimateReturnCityCode != null">
        estimate_return_city_code = #{estimateReturnCityCode,jdbcType=VARCHAR},
      </if>
      <if test="estimateReturnCityName != null">
        estimate_return_city_name = #{estimateReturnCityName,jdbcType=VARCHAR},
      </if>
      <if test="estimateUseAddress != null">
        estimate_use_address = #{estimateUseAddress,jdbcType=VARCHAR},
      </if>
      <if test="estimateUseAddressLat != null">
        estimate_use_address_lat = #{estimateUseAddressLat,jdbcType=VARCHAR},
      </if>
      <if test="estimateUseAddressLon != null">
        estimate_use_address_lon = #{estimateUseAddressLon,jdbcType=VARCHAR},
      </if>
      <if test="estimateReturnAddress != null">
        estimate_return_address = #{estimateReturnAddress,jdbcType=VARCHAR},
      </if>
      <if test="estimateReturnAddressLat != null">
        estimate_return_address_lat = #{estimateReturnAddressLat,jdbcType=VARCHAR},
      </if>
      <if test="estimateReturnAddressLon != null">
        estimate_return_address_lon = #{estimateReturnAddressLon,jdbcType=VARCHAR},
      </if>
      <if test="actualUseCityCode != null">
        actual_use_city_code = #{actualUseCityCode,jdbcType=VARCHAR},
      </if>
      <if test="actualUseCityName != null">
        actual_use_city_name = #{actualUseCityName,jdbcType=VARCHAR},
      </if>
      <if test="actualReturnCityCode != null">
        actual_return_city_code = #{actualReturnCityCode,jdbcType=VARCHAR},
      </if>
      <if test="actualReturnCityName != null">
        actual_return_city_name = #{actualReturnCityName,jdbcType=VARCHAR},
      </if>
      <if test="actualUseAddress != null">
        actual_use_address = #{actualUseAddress,jdbcType=VARCHAR},
      </if>
      <if test="actualUseAddressLat != null">
        actual_use_address_lat = #{actualUseAddressLat,jdbcType=VARCHAR},
      </if>
      <if test="actualUseAddressLon != null">
        actual_use_address_lon = #{actualUseAddressLon,jdbcType=VARCHAR},
      </if>
      <if test="actualReturnAddress != null">
        actual_return_address = #{actualReturnAddress,jdbcType=VARCHAR},
      </if>
      <if test="actualReturnAddressLat != null">
        actual_return_address_lat = #{actualReturnAddressLat,jdbcType=VARCHAR},
      </if>
      <if test="actualReturnAddressLon != null">
        actual_return_address_lon = #{actualReturnAddressLon,jdbcType=VARCHAR},
      </if>
      <if test="deliveryPersonId != null">
        delivery_person_id = #{deliveryPersonId,jdbcType=INTEGER},
      </if>
      <if test="deliveryPersonName != null">
        delivery_person_name = #{deliveryPersonName,jdbcType=VARCHAR},
      </if>
      <if test="deliveryPersonPhone != null">
        delivery_person_phone = #{deliveryPersonPhone,jdbcType=VARCHAR},
      </if>
      <if test="pickPersonId != null">
        pick_person_id = #{pickPersonId,jdbcType=INTEGER},
      </if>
      <if test="pickPersonName != null">
        pick_person_name = #{pickPersonName,jdbcType=VARCHAR},
      </if>
      <if test="pickPersonPhone != null">
        pick_person_phone = #{pickPersonPhone,jdbcType=VARCHAR},
      </if>
      <if test="useRemark != null">
        use_remark = #{useRemark,jdbcType=VARCHAR},
      </if>
      <if test="deliveryOverMileageAmount != null">
        delivery_over_mileage_amount = #{deliveryOverMileageAmount,jdbcType=DECIMAL},
      </if>
      <if test="returnOverMileageAmount != null">
        return_over_mileage_amount = #{returnOverMileageAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalOverMileageAmount != null">
        total_over_mileage_amount = #{totalOverMileageAmount,jdbcType=DECIMAL},
      </if>
      <if test="vehicleRentAmount != null">
        vehicle_rent_amount = #{vehicleRentAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalAmount != null">
        total_amount = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxTotalAmount != null">
        tax_total_amount = #{taxTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="originalTotalAmount != null">
        original_total_amount = #{originalTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="vehicleLicense != null">
        vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleVin != null">
        vehicle_vin = #{vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="vehicleSerialNoNewSystem != null">
        vehicle_serial_no_new_system = #{vehicleSerialNoNewSystem,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBrandCode != null">
        vehicle_brand_code = #{vehicleBrandCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBrand != null">
        vehicle_brand = #{vehicleBrand,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        brand = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelCode != null">
        vehicle_model_code = #{vehicleModelCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModel != null">
        vehicle_model = #{vehicleModel,jdbcType=VARCHAR},
      </if>
      <if test="engineNum != null">
        engine_num = #{engineNum,jdbcType=VARCHAR},
      </if>
      <if test="vehicleColor != null">
        vehicle_color = #{vehicleColor,jdbcType=VARCHAR},
      </if>
      <if test="vehicleCompanyId != null">
        vehicle_company_id = #{vehicleCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="vehicleCompanyName != null">
        vehicle_company_name = #{vehicleCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="selfOwned != null">
        self_owned = #{selfOwned,jdbcType=TINYINT},
      </if>
      <if test="operateBussName != null">
        operate_buss_name = #{operateBussName,jdbcType=VARCHAR},
      </if>
      <if test="operateBussCode != null">
        operate_buss_code = #{operateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="belongBussCode != null">
        belong_buss_code = #{belongBussCode,jdbcType=VARCHAR},
      </if>
      <if test="belongBussName != null">
        belong_buss_name = #{belongBussName,jdbcType=VARCHAR},
      </if>
      <if test="assetCityCode != null">
        asset_city_code = #{assetCityCode,jdbcType=VARCHAR},
      </if>
      <if test="assetCityName != null">
        asset_city_name = #{assetCityName,jdbcType=VARCHAR},
      </if>
      <if test="structId != null">
        struct_id = #{structId,jdbcType=INTEGER},
      </if>
      <if test="structCode != null">
        struct_code = #{structCode,jdbcType=VARCHAR},
      </if>
      <if test="structName != null">
        struct_name = #{structName,jdbcType=VARCHAR},
      </if>
      <if test="belongCityCode != null">
        belong_city_code = #{belongCityCode,jdbcType=VARCHAR},
      </if>
      <if test="belongCityName != null">
        belong_city_name = #{belongCityName,jdbcType=VARCHAR},
      </if>
      <if test="recordMemo != null">
        record_memo = #{recordMemo,jdbcType=VARCHAR},
      </if>
      <if test="updateMemo != null">
        update_memo = #{updateMemo,jdbcType=VARCHAR},
      </if>
      <if test="cancelMemo != null">
        cancel_memo = #{cancelMemo,jdbcType=VARCHAR},
      </if>
      <if test="taxType != null">
        tax_type = #{taxType,jdbcType=INTEGER},
      </if>
      <if test="taxRate != null">
        tax_rate = #{taxRate,jdbcType=DECIMAL},
      </if>
      <if test="depositFlag != null">
        deposit_flag = #{depositFlag,jdbcType=BIT},
      </if>
      <if test="depositFreeType != null">
        deposit_free_type = #{depositFreeType,jdbcType=TINYINT},
      </if>
      <if test="depositAuthUrl != null">
        deposit_auth_url = #{depositAuthUrl,jdbcType=VARCHAR},
      </if>
      <if test="depositAuthQrCodeUrl != null">
        deposit_auth_qr_code_url = #{depositAuthQrCodeUrl,jdbcType=VARCHAR},
      </if>
      <if test="vehicleDeposit != null">
        vehicle_deposit = #{vehicleDeposit,jdbcType=DECIMAL},
      </if>
      <if test="violationDeposit != null">
        violation_deposit = #{violationDeposit,jdbcType=DECIMAL},
      </if>
      <if test="orderPreauthTimeout != null">
        order_preauth_timeout = #{orderPreauthTimeout,jdbcType=TIMESTAMP},
      </if>
      <if test="depositFreeReason != null">
        deposit_free_reason = #{depositFreeReason,jdbcType=VARCHAR},
      </if>
      <if test="carLevelId != null">
        car_level_id = #{carLevelId,jdbcType=INTEGER},
      </if>
      <if test="carLevelName != null">
        car_level_name = #{carLevelName,jdbcType=VARCHAR},
      </if>
      <if test="chargeType != null">
        charge_type = #{chargeType,jdbcType=VARCHAR},
      </if>
      <if test="pickupDeliveryFee != null">
        pickup_delivery_fee = #{pickupDeliveryFee,jdbcType=DECIMAL},
      </if>
      <if test="priceConfigDetailId != null">
        price_config_detail_id = #{priceConfigDetailId,jdbcType=INTEGER},
      </if>
      <if test="configModelCode != null">
        config_model_code = #{configModelCode,jdbcType=VARCHAR},
      </if>
      <if test="configModelName != null">
        config_model_name = #{configModelName,jdbcType=VARCHAR},
      </if>
      <if test="configBrandCode != null">
        config_brand_code = #{configBrandCode,jdbcType=VARCHAR},
      </if>
      <if test="configBrandName != null">
        config_brand_name = #{configBrandName,jdbcType=VARCHAR},
      </if>
      <if test="dayPrice != null">
        day_price = #{dayPrice,jdbcType=DECIMAL},
      </if>
      <if test="outMilePrice != null">
        out_mile_price = #{outMilePrice,jdbcType=DECIMAL},
      </if>
    </set>
    where co_order_id = #{coOrderId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.CoOrderInfo">
    update co_order_info
    set create_id = #{createId,jdbcType=INTEGER},
      create_name = #{createName,jdbcType=VARCHAR},
      create_mobile = #{createMobile,jdbcType=VARCHAR},
      create_company_id = #{createCompanyId,jdbcType=VARCHAR},
      create_company_name = #{createCompanyName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_id = #{updateId,jdbcType=INTEGER},
      update_name = #{updateName,jdbcType=VARCHAR},
      update_mobile = #{updateMobile,jdbcType=VARCHAR},
      update_company_id = #{updateCompanyId,jdbcType=VARCHAR},
      update_company_name = #{updateCompanyName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      del_tag = #{delTag,jdbcType=TINYINT},
      order_num = #{orderNum,jdbcType=VARCHAR},
      third_order_num = #{thirdOrderNum,jdbcType=VARCHAR},
      order_status = #{orderStatus,jdbcType=INTEGER},
      bill_status = #{billStatus,jdbcType=INTEGER},
      order_source = #{orderSource,jdbcType=TINYINT},
      actual_use_time = #{actualUseTime,jdbcType=TIMESTAMP},
      actual_return_time = #{actualReturnTime,jdbcType=TIMESTAMP},
      estimate_use_date = #{estimateUseDate,jdbcType=TIMESTAMP},
      estimate_return_date = #{estimateReturnDate,jdbcType=TIMESTAMP},
      actual_use_cycle = #{actualUseCycle,jdbcType=INTEGER},
      estimate_use_cycle = #{estimateUseCycle,jdbcType=INTEGER},
      order_type = #{orderType,jdbcType=TINYINT},
      company_id = #{companyId,jdbcType=INTEGER},
      company_code = #{companyCode,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      customer_name = #{customerName,jdbcType=VARCHAR},
      customer_phone = #{customerPhone,jdbcType=VARCHAR},
      customer_id_card = #{customerIdCard,jdbcType=VARCHAR},
      estimate_use_city_code = #{estimateUseCityCode,jdbcType=VARCHAR},
      estimate_use_city_name = #{estimateUseCityName,jdbcType=VARCHAR},
      estimate_return_city_code = #{estimateReturnCityCode,jdbcType=VARCHAR},
      estimate_return_city_name = #{estimateReturnCityName,jdbcType=VARCHAR},
      estimate_use_address = #{estimateUseAddress,jdbcType=VARCHAR},
      estimate_use_address_lat = #{estimateUseAddressLat,jdbcType=VARCHAR},
      estimate_use_address_lon = #{estimateUseAddressLon,jdbcType=VARCHAR},
      estimate_return_address = #{estimateReturnAddress,jdbcType=VARCHAR},
      estimate_return_address_lat = #{estimateReturnAddressLat,jdbcType=VARCHAR},
      estimate_return_address_lon = #{estimateReturnAddressLon,jdbcType=VARCHAR},
      actual_use_city_code = #{actualUseCityCode,jdbcType=VARCHAR},
      actual_use_city_name = #{actualUseCityName,jdbcType=VARCHAR},
      actual_return_city_code = #{actualReturnCityCode,jdbcType=VARCHAR},
      actual_return_city_name = #{actualReturnCityName,jdbcType=VARCHAR},
      actual_use_address = #{actualUseAddress,jdbcType=VARCHAR},
      actual_use_address_lat = #{actualUseAddressLat,jdbcType=VARCHAR},
      actual_use_address_lon = #{actualUseAddressLon,jdbcType=VARCHAR},
      actual_return_address = #{actualReturnAddress,jdbcType=VARCHAR},
      actual_return_address_lat = #{actualReturnAddressLat,jdbcType=VARCHAR},
      actual_return_address_lon = #{actualReturnAddressLon,jdbcType=VARCHAR},
      delivery_person_id = #{deliveryPersonId,jdbcType=INTEGER},
      delivery_person_name = #{deliveryPersonName,jdbcType=VARCHAR},
      delivery_person_phone = #{deliveryPersonPhone,jdbcType=VARCHAR},
      pick_person_id = #{pickPersonId,jdbcType=INTEGER},
      pick_person_name = #{pickPersonName,jdbcType=VARCHAR},
      pick_person_phone = #{pickPersonPhone,jdbcType=VARCHAR},
      use_remark = #{useRemark,jdbcType=VARCHAR},
      delivery_over_mileage_amount = #{deliveryOverMileageAmount,jdbcType=DECIMAL},
      return_over_mileage_amount = #{returnOverMileageAmount,jdbcType=DECIMAL},
      total_over_mileage_amount = #{totalOverMileageAmount,jdbcType=DECIMAL},
      vehicle_rent_amount = #{vehicleRentAmount,jdbcType=DECIMAL},
      total_amount = #{totalAmount,jdbcType=DECIMAL},
      tax_total_amount = #{taxTotalAmount,jdbcType=DECIMAL},
      original_total_amount = #{originalTotalAmount,jdbcType=DECIMAL},
      vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      vehicle_vin = #{vehicleVin,jdbcType=VARCHAR},
      vehicle_serial_no_new_system = #{vehicleSerialNoNewSystem,jdbcType=VARCHAR},
      vehicle_brand_code = #{vehicleBrandCode,jdbcType=VARCHAR},
      vehicle_brand = #{vehicleBrand,jdbcType=VARCHAR},
      brand = #{brand,jdbcType=VARCHAR},
      vehicle_model_code = #{vehicleModelCode,jdbcType=VARCHAR},
      vehicle_model = #{vehicleModel,jdbcType=VARCHAR},
      engine_num = #{engineNum,jdbcType=VARCHAR},
      vehicle_color = #{vehicleColor,jdbcType=VARCHAR},
      vehicle_company_id = #{vehicleCompanyId,jdbcType=VARCHAR},
      vehicle_company_name = #{vehicleCompanyName,jdbcType=VARCHAR},
      self_owned = #{selfOwned,jdbcType=TINYINT},
      operate_buss_name = #{operateBussName,jdbcType=VARCHAR},
      operate_buss_code = #{operateBussCode,jdbcType=VARCHAR},
      belong_buss_code = #{belongBussCode,jdbcType=VARCHAR},
      belong_buss_name = #{belongBussName,jdbcType=VARCHAR},
      asset_city_code = #{assetCityCode,jdbcType=VARCHAR},
      asset_city_name = #{assetCityName,jdbcType=VARCHAR},
      struct_id = #{structId,jdbcType=INTEGER},
      struct_code = #{structCode,jdbcType=VARCHAR},
      struct_name = #{structName,jdbcType=VARCHAR},
      belong_city_code = #{belongCityCode,jdbcType=VARCHAR},
      belong_city_name = #{belongCityName,jdbcType=VARCHAR},
      record_memo = #{recordMemo,jdbcType=VARCHAR},
      update_memo = #{updateMemo,jdbcType=VARCHAR},
      cancel_memo = #{cancelMemo,jdbcType=VARCHAR},
      tax_type = #{taxType,jdbcType=INTEGER},
      tax_rate = #{taxRate,jdbcType=DECIMAL},
      deposit_flag = #{depositFlag,jdbcType=BIT},
      deposit_free_type = #{depositFreeType,jdbcType=TINYINT},
      deposit_auth_url = #{depositAuthUrl,jdbcType=VARCHAR},
      deposit_auth_qr_code_url = #{depositAuthQrCodeUrl,jdbcType=VARCHAR},
      vehicle_deposit = #{vehicleDeposit,jdbcType=DECIMAL},
      violation_deposit = #{violationDeposit,jdbcType=DECIMAL},
      order_preauth_timeout = #{orderPreauthTimeout,jdbcType=TIMESTAMP},
      deposit_free_reason = #{depositFreeReason,jdbcType=VARCHAR},
      car_level_id = #{carLevelId,jdbcType=INTEGER},
      car_level_name = #{carLevelName,jdbcType=VARCHAR},
      charge_type = #{chargeType,jdbcType=VARCHAR},
      pickup_delivery_fee = #{pickupDeliveryFee,jdbcType=DECIMAL},
      price_config_detail_id = #{priceConfigDetailId,jdbcType=INTEGER},
      config_model_code = #{configModelCode,jdbcType=VARCHAR},
      config_model_name = #{configModelName,jdbcType=VARCHAR},
      config_brand_code = #{configBrandCode,jdbcType=VARCHAR},
      config_brand_name = #{configBrandName,jdbcType=VARCHAR},
      day_price = #{dayPrice,jdbcType=DECIMAL},
      out_mile_price = #{outMilePrice,jdbcType=DECIMAL}
    where co_order_id = #{coOrderId,jdbcType=INTEGER}
  </update>
</mapper>