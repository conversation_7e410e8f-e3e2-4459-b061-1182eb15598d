<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.BillMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.Bill">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="bill_no" jdbcType="VARCHAR" property="billNo" />
    <result column="order_apply_no" jdbcType="VARCHAR" property="orderApplyNo" />
    <result column="fixed_price_valid" jdbcType="BIT" property="fixedPriceValid" />
    <result column="fixed_price" jdbcType="DECIMAL" property="fixedPrice" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="shouldpay_amount" jdbcType="DECIMAL" property="shouldpayAmount" />
    <result column="unpayed_amount" jdbcType="DECIMAL" property="unpayedAmount" />
    <result column="payed_amount" jdbcType="DECIMAL" property="payedAmount" />
    <result column="coupon_deduct_amount" jdbcType="DECIMAL" property="couponDeductAmount" />
    <result column="reduction_amount" jdbcType="DECIMAL" property="reductionAmount" />
    <result column="actual_income_amount" jdbcType="DECIMAL" property="actualIncomeAmount" />
    <result column="other_amount" jdbcType="DECIMAL" property="otherAmount" />
    <result column="price_explain" jdbcType="VARCHAR" property="priceExplain" />
    <result column="bill_status" jdbcType="TINYINT" property="billStatus" />
    <result column="fee_lock" jdbcType="BIT" property="feeLock" />
    <result column="adjust_price_user_id" jdbcType="INTEGER" property="adjustPriceUserId" />
    <result column="adjust_price_user_name" jdbcType="VARCHAR" property="adjustPriceUserName" />
    <result column="adjust_price_time" jdbcType="TIMESTAMP" property="adjustPriceTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, bill_no, order_apply_no, fixed_price_valid, fixed_price, 
    total_amount, shouldpay_amount, unpayed_amount, payed_amount, coupon_deduct_amount, 
    reduction_amount, actual_income_amount, other_amount, price_explain, bill_status, 
    fee_lock, adjust_price_user_id, adjust_price_user_name, adjust_price_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bill
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bill
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.Bill">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into bill (create_time, update_time, bill_no, 
      order_apply_no, fixed_price_valid, fixed_price, 
      total_amount, shouldpay_amount, unpayed_amount, 
      payed_amount, coupon_deduct_amount, reduction_amount, 
      actual_income_amount, other_amount, price_explain, 
      bill_status, fee_lock, adjust_price_user_id, 
      adjust_price_user_name, adjust_price_time)
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{billNo,jdbcType=VARCHAR}, 
      #{orderApplyNo,jdbcType=VARCHAR}, #{fixedPriceValid,jdbcType=BIT}, #{fixedPrice,jdbcType=DECIMAL}, 
      #{totalAmount,jdbcType=DECIMAL}, #{shouldpayAmount,jdbcType=DECIMAL}, #{unpayedAmount,jdbcType=DECIMAL}, 
      #{payedAmount,jdbcType=DECIMAL}, #{couponDeductAmount,jdbcType=DECIMAL}, #{reductionAmount,jdbcType=DECIMAL}, 
      #{actualIncomeAmount,jdbcType=DECIMAL}, #{otherAmount,jdbcType=DECIMAL}, #{priceExplain,jdbcType=VARCHAR}, 
      #{billStatus,jdbcType=TINYINT}, #{feeLock,jdbcType=BIT}, #{adjustPriceUserId,jdbcType=INTEGER}, 
      #{adjustPriceUserName,jdbcType=VARCHAR}, #{adjustPriceTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.Bill">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into bill
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="billNo != null">
        bill_no,
      </if>
      <if test="orderApplyNo != null">
        order_apply_no,
      </if>
      <if test="fixedPriceValid != null">
        fixed_price_valid,
      </if>
      <if test="fixedPrice != null">
        fixed_price,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="shouldpayAmount != null">
        shouldpay_amount,
      </if>
      <if test="unpayedAmount != null">
        unpayed_amount,
      </if>
      <if test="payedAmount != null">
        payed_amount,
      </if>
      <if test="couponDeductAmount != null">
        coupon_deduct_amount,
      </if>
      <if test="reductionAmount != null">
        reduction_amount,
      </if>
      <if test="actualIncomeAmount != null">
        actual_income_amount,
      </if>
      <if test="otherAmount != null">
        other_amount,
      </if>
      <if test="priceExplain != null">
        price_explain,
      </if>
      <if test="billStatus != null">
        bill_status,
      </if>
      <if test="feeLock != null">
        fee_lock,
      </if>
      <if test="adjustPriceUserId != null">
        adjust_price_user_id,
      </if>
      <if test="adjustPriceUserName != null">
        adjust_price_user_name,
      </if>
      <if test="adjustPriceTime != null">
        adjust_price_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="billNo != null">
        #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="orderApplyNo != null">
        #{orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="fixedPriceValid != null">
        #{fixedPriceValid,jdbcType=BIT},
      </if>
      <if test="fixedPrice != null">
        #{fixedPrice,jdbcType=DECIMAL},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="shouldpayAmount != null">
        #{shouldpayAmount,jdbcType=DECIMAL},
      </if>
      <if test="unpayedAmount != null">
        #{unpayedAmount,jdbcType=DECIMAL},
      </if>
      <if test="payedAmount != null">
        #{payedAmount,jdbcType=DECIMAL},
      </if>
      <if test="couponDeductAmount != null">
        #{couponDeductAmount,jdbcType=DECIMAL},
      </if>
      <if test="reductionAmount != null">
        #{reductionAmount,jdbcType=DECIMAL},
      </if>
      <if test="actualIncomeAmount != null">
        #{actualIncomeAmount,jdbcType=DECIMAL},
      </if>
      <if test="otherAmount != null">
        #{otherAmount,jdbcType=DECIMAL},
      </if>
      <if test="priceExplain != null">
        #{priceExplain,jdbcType=VARCHAR},
      </if>
      <if test="billStatus != null">
        #{billStatus,jdbcType=TINYINT},
      </if>
      <if test="feeLock != null">
        #{feeLock,jdbcType=BIT},
      </if>
      <if test="adjustPriceUserId != null">
        #{adjustPriceUserId,jdbcType=INTEGER},
      </if>
      <if test="adjustPriceUserName != null">
        #{adjustPriceUserName,jdbcType=VARCHAR},
      </if>
      <if test="adjustPriceTime != null">
        #{adjustPriceTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.Bill">
    update bill
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="billNo != null">
        bill_no = #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="orderApplyNo != null">
        order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="fixedPriceValid != null">
        fixed_price_valid = #{fixedPriceValid,jdbcType=BIT},
      </if>
      <if test="fixedPrice != null">
        fixed_price = #{fixedPrice,jdbcType=DECIMAL},
      </if>
      <if test="totalAmount != null">
        total_amount = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="shouldpayAmount != null">
        shouldpay_amount = #{shouldpayAmount,jdbcType=DECIMAL},
      </if>
      <if test="unpayedAmount != null">
        unpayed_amount = #{unpayedAmount,jdbcType=DECIMAL},
      </if>
      <if test="payedAmount != null">
        payed_amount = #{payedAmount,jdbcType=DECIMAL},
      </if>
      <if test="couponDeductAmount != null">
        coupon_deduct_amount = #{couponDeductAmount,jdbcType=DECIMAL},
      </if>
      <if test="reductionAmount != null">
        reduction_amount = #{reductionAmount,jdbcType=DECIMAL},
      </if>
      <if test="actualIncomeAmount != null">
        actual_income_amount = #{actualIncomeAmount,jdbcType=DECIMAL},
      </if>
      <if test="otherAmount != null">
        other_amount = #{otherAmount,jdbcType=DECIMAL},
      </if>
      <if test="priceExplain != null">
        price_explain = #{priceExplain,jdbcType=VARCHAR},
      </if>
      <if test="billStatus != null">
        bill_status = #{billStatus,jdbcType=TINYINT},
      </if>
      <if test="feeLock != null">
        fee_lock = #{feeLock,jdbcType=BIT},
      </if>
      <if test="adjustPriceUserId != null">
        adjust_price_user_id = #{adjustPriceUserId,jdbcType=INTEGER},
      </if>
      <if test="adjustPriceUserName != null">
        adjust_price_user_name = #{adjustPriceUserName,jdbcType=VARCHAR},
      </if>
      <if test="adjustPriceTime != null">
        adjust_price_time = #{adjustPriceTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.Bill">
    update bill
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      bill_no = #{billNo,jdbcType=VARCHAR},
      order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      fixed_price_valid = #{fixedPriceValid,jdbcType=BIT},
      fixed_price = #{fixedPrice,jdbcType=DECIMAL},
      total_amount = #{totalAmount,jdbcType=DECIMAL},
      shouldpay_amount = #{shouldpayAmount,jdbcType=DECIMAL},
      unpayed_amount = #{unpayedAmount,jdbcType=DECIMAL},
      payed_amount = #{payedAmount,jdbcType=DECIMAL},
      coupon_deduct_amount = #{couponDeductAmount,jdbcType=DECIMAL},
      reduction_amount = #{reductionAmount,jdbcType=DECIMAL},
      actual_income_amount = #{actualIncomeAmount,jdbcType=DECIMAL},
      other_amount = #{otherAmount,jdbcType=DECIMAL},
      price_explain = #{priceExplain,jdbcType=VARCHAR},
      bill_status = #{billStatus,jdbcType=TINYINT},
      fee_lock = #{feeLock,jdbcType=BIT},
      adjust_price_user_id = #{adjustPriceUserId,jdbcType=INTEGER},
      adjust_price_user_name = #{adjustPriceUserName,jdbcType=VARCHAR},
      adjust_price_time = #{adjustPriceTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>