<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.OrderCertificateMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.OrderCertificate">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="picture_url" jdbcType="VARCHAR" property="pictureUrl" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="source" jdbcType="INTEGER" property="source" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, order_no, picture_url, memo, source, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_certificate
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from order_certificate
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.OrderCertificate">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_certificate (order_no, picture_url, memo, 
      source, create_time)
    values (#{orderNo,jdbcType=VARCHAR}, #{pictureUrl,jdbcType=VARCHAR}, #{memo,jdbcType=VARCHAR}, 
      #{source,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.OrderCertificate">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_certificate
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="pictureUrl != null">
        picture_url,
      </if>
      <if test="memo != null">
        memo,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="pictureUrl != null">
        #{pictureUrl,jdbcType=VARCHAR},
      </if>
      <if test="memo != null">
        #{memo,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.OrderCertificate">
    update order_certificate
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="pictureUrl != null">
        picture_url = #{pictureUrl,jdbcType=VARCHAR},
      </if>
      <if test="memo != null">
        memo = #{memo,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.OrderCertificate">
    update order_certificate
    set order_no = #{orderNo,jdbcType=VARCHAR},
      picture_url = #{pictureUrl,jdbcType=VARCHAR},
      memo = #{memo,jdbcType=VARCHAR},
      source = #{source,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>