<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.GovPublicCarOrderMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.GovPublicCarOrder">
    <id column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="order_type" jdbcType="INTEGER" property="orderType" />
    <result column="vehicle_id" jdbcType="INTEGER" property="vehicleId" />
    <result column="vehicle_license" jdbcType="VARCHAR" property="vehicleLicense" />
    <result column="vehicle_vin" jdbcType="VARCHAR" property="vehicleVin" />
    <result column="order_struct_id" jdbcType="INTEGER" property="orderStructId" />
    <result column="order_struct_code" jdbcType="VARCHAR" property="orderStructCode" />
    <result column="order_struct_name" jdbcType="VARCHAR" property="orderStructName" />
    <result column="order_status" jdbcType="TINYINT" property="orderStatus" />
    <result column="approval_status" jdbcType="TINYINT" property="approvalStatus" />
    <result column="approval_id" jdbcType="VARCHAR" property="approvalId" />
    <result column="approval_completed_time" jdbcType="TIMESTAMP" property="approvalCompletedTime" />
    <result column="approval_withdrawal_type" jdbcType="TINYINT" property="approvalWithdrawalType" />
    <result column="car_use_reason" jdbcType="VARCHAR" property="carUseReason" />
    <result column="order_user_memo" jdbcType="VARCHAR" property="orderUserMemo" />
    <result column="expected_pickup_time" jdbcType="TIMESTAMP" property="expectedPickupTime" />
    <result column="expected_return_time" jdbcType="TIMESTAMP" property="expectedReturnTime" />
    <result column="order_start_time" jdbcType="TIMESTAMP" property="orderStartTime" />
    <result column="order_end_time" jdbcType="TIMESTAMP" property="orderEndTime" />
    <result column="pickup_lot_exit_time" jdbcType="TIMESTAMP" property="pickupLotExitTime" />
    <result column="return_lot_entry_time" jdbcType="TIMESTAMP" property="returnLotEntryTime" />
    <result column="verifier_time" jdbcType="TIMESTAMP" property="verifierTime" />
    <result column="cancellation_time" jdbcType="TIMESTAMP" property="cancellationTime" />
    <result column="total_mileage" jdbcType="DECIMAL" property="totalMileage" />
    <result column="out_in_fence_total_mileage" jdbcType="DECIMAL" property="outInFenceTotalMileage" />
    <result column="update_id" jdbcType="INTEGER" property="updateId" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deployment_mode" jdbcType="TINYINT" property="deploymentMode" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="is_show" jdbcType="TINYINT" property="isShow" />
    <result column="verify_status" jdbcType="INTEGER" property="verifyStatus" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    order_id, order_no, order_type, vehicle_id, vehicle_license, vehicle_vin, order_struct_id, 
    order_struct_code, order_struct_name, order_status, approval_status, approval_id, 
    approval_completed_time, approval_withdrawal_type, car_use_reason, order_user_memo, 
    expected_pickup_time, expected_return_time, order_start_time, order_end_time, pickup_lot_exit_time, 
    return_lot_entry_time, verifier_time, cancellation_time, total_mileage, out_in_fence_total_mileage, 
    update_id, update_name, create_time, update_time, deployment_mode, company_id, company_code, 
    company_name, is_show, verify_status
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.GovPublicCarOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from gov_public_car_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from gov_public_car_order
    where order_id = #{orderId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from gov_public_car_order
    where order_id = #{orderId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.order.entity.mrcar.GovPublicCarOrderExample">
    delete from gov_public_car_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.GovPublicCarOrder">
    <selectKey keyProperty="orderId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gov_public_car_order (order_no, order_type, vehicle_id, 
      vehicle_license, vehicle_vin, order_struct_id, 
      order_struct_code, order_struct_name, order_status, 
      approval_status, approval_id, approval_completed_time, 
      approval_withdrawal_type, car_use_reason, order_user_memo, 
      expected_pickup_time, expected_return_time, 
      order_start_time, order_end_time, pickup_lot_exit_time, 
      return_lot_entry_time, verifier_time, cancellation_time, 
      total_mileage, out_in_fence_total_mileage, update_id, 
      update_name, create_time, update_time, 
      deployment_mode, company_id, company_code, 
      company_name, is_show, verify_status
      )
    values (#{orderNo,jdbcType=VARCHAR}, #{orderType,jdbcType=INTEGER}, #{vehicleId,jdbcType=INTEGER}, 
      #{vehicleLicense,jdbcType=VARCHAR}, #{vehicleVin,jdbcType=VARCHAR}, #{orderStructId,jdbcType=INTEGER}, 
      #{orderStructCode,jdbcType=VARCHAR}, #{orderStructName,jdbcType=VARCHAR}, #{orderStatus,jdbcType=TINYINT}, 
      #{approvalStatus,jdbcType=TINYINT}, #{approvalId,jdbcType=VARCHAR}, #{approvalCompletedTime,jdbcType=TIMESTAMP}, 
      #{approvalWithdrawalType,jdbcType=TINYINT}, #{carUseReason,jdbcType=VARCHAR}, #{orderUserMemo,jdbcType=VARCHAR}, 
      #{expectedPickupTime,jdbcType=TIMESTAMP}, #{expectedReturnTime,jdbcType=TIMESTAMP}, 
      #{orderStartTime,jdbcType=TIMESTAMP}, #{orderEndTime,jdbcType=TIMESTAMP}, #{pickupLotExitTime,jdbcType=TIMESTAMP}, 
      #{returnLotEntryTime,jdbcType=TIMESTAMP}, #{verifierTime,jdbcType=TIMESTAMP}, #{cancellationTime,jdbcType=TIMESTAMP}, 
      #{totalMileage,jdbcType=DECIMAL}, #{outInFenceTotalMileage,jdbcType=DECIMAL}, #{updateId,jdbcType=INTEGER}, 
      #{updateName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{deploymentMode,jdbcType=TINYINT}, #{companyId,jdbcType=INTEGER}, #{companyCode,jdbcType=VARCHAR}, 
      #{companyName,jdbcType=VARCHAR}, #{isShow,jdbcType=TINYINT}, #{verifyStatus,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.GovPublicCarOrder">
    <selectKey keyProperty="orderId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gov_public_car_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="vehicleLicense != null">
        vehicle_license,
      </if>
      <if test="vehicleVin != null">
        vehicle_vin,
      </if>
      <if test="orderStructId != null">
        order_struct_id,
      </if>
      <if test="orderStructCode != null">
        order_struct_code,
      </if>
      <if test="orderStructName != null">
        order_struct_name,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="approvalStatus != null">
        approval_status,
      </if>
      <if test="approvalId != null">
        approval_id,
      </if>
      <if test="approvalCompletedTime != null">
        approval_completed_time,
      </if>
      <if test="approvalWithdrawalType != null">
        approval_withdrawal_type,
      </if>
      <if test="carUseReason != null">
        car_use_reason,
      </if>
      <if test="orderUserMemo != null">
        order_user_memo,
      </if>
      <if test="expectedPickupTime != null">
        expected_pickup_time,
      </if>
      <if test="expectedReturnTime != null">
        expected_return_time,
      </if>
      <if test="orderStartTime != null">
        order_start_time,
      </if>
      <if test="orderEndTime != null">
        order_end_time,
      </if>
      <if test="pickupLotExitTime != null">
        pickup_lot_exit_time,
      </if>
      <if test="returnLotEntryTime != null">
        return_lot_entry_time,
      </if>
      <if test="verifierTime != null">
        verifier_time,
      </if>
      <if test="cancellationTime != null">
        cancellation_time,
      </if>
      <if test="totalMileage != null">
        total_mileage,
      </if>
      <if test="outInFenceTotalMileage != null">
        out_in_fence_total_mileage,
      </if>
      <if test="updateId != null">
        update_id,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="deploymentMode != null">
        deployment_mode,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="isShow != null">
        is_show,
      </if>
      <if test="verifyStatus != null">
        verify_status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=INTEGER},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=INTEGER},
      </if>
      <if test="vehicleLicense != null">
        #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="vehicleVin != null">
        #{vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="orderStructId != null">
        #{orderStructId,jdbcType=INTEGER},
      </if>
      <if test="orderStructCode != null">
        #{orderStructCode,jdbcType=VARCHAR},
      </if>
      <if test="orderStructName != null">
        #{orderStructName,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=TINYINT},
      </if>
      <if test="approvalStatus != null">
        #{approvalStatus,jdbcType=TINYINT},
      </if>
      <if test="approvalId != null">
        #{approvalId,jdbcType=VARCHAR},
      </if>
      <if test="approvalCompletedTime != null">
        #{approvalCompletedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="approvalWithdrawalType != null">
        #{approvalWithdrawalType,jdbcType=TINYINT},
      </if>
      <if test="carUseReason != null">
        #{carUseReason,jdbcType=VARCHAR},
      </if>
      <if test="orderUserMemo != null">
        #{orderUserMemo,jdbcType=VARCHAR},
      </if>
      <if test="expectedPickupTime != null">
        #{expectedPickupTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expectedReturnTime != null">
        #{expectedReturnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderStartTime != null">
        #{orderStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderEndTime != null">
        #{orderEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="pickupLotExitTime != null">
        #{pickupLotExitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="returnLotEntryTime != null">
        #{returnLotEntryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="verifierTime != null">
        #{verifierTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cancellationTime != null">
        #{cancellationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="totalMileage != null">
        #{totalMileage,jdbcType=DECIMAL},
      </if>
      <if test="outInFenceTotalMileage != null">
        #{outInFenceTotalMileage,jdbcType=DECIMAL},
      </if>
      <if test="updateId != null">
        #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deploymentMode != null">
        #{deploymentMode,jdbcType=TINYINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="isShow != null">
        #{isShow,jdbcType=TINYINT},
      </if>
      <if test="verifyStatus != null">
        #{verifyStatus,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.GovPublicCarOrderExample" resultType="java.lang.Long">
    select count(*) from gov_public_car_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update gov_public_car_order
    <set>
      <if test="row.orderId != null">
        order_id = #{row.orderId,jdbcType=BIGINT},
      </if>
      <if test="row.orderNo != null">
        order_no = #{row.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="row.orderType != null">
        order_type = #{row.orderType,jdbcType=INTEGER},
      </if>
      <if test="row.vehicleId != null">
        vehicle_id = #{row.vehicleId,jdbcType=INTEGER},
      </if>
      <if test="row.vehicleLicense != null">
        vehicle_license = #{row.vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleVin != null">
        vehicle_vin = #{row.vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="row.orderStructId != null">
        order_struct_id = #{row.orderStructId,jdbcType=INTEGER},
      </if>
      <if test="row.orderStructCode != null">
        order_struct_code = #{row.orderStructCode,jdbcType=VARCHAR},
      </if>
      <if test="row.orderStructName != null">
        order_struct_name = #{row.orderStructName,jdbcType=VARCHAR},
      </if>
      <if test="row.orderStatus != null">
        order_status = #{row.orderStatus,jdbcType=TINYINT},
      </if>
      <if test="row.approvalStatus != null">
        approval_status = #{row.approvalStatus,jdbcType=TINYINT},
      </if>
      <if test="row.approvalId != null">
        approval_id = #{row.approvalId,jdbcType=VARCHAR},
      </if>
      <if test="row.approvalCompletedTime != null">
        approval_completed_time = #{row.approvalCompletedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.approvalWithdrawalType != null">
        approval_withdrawal_type = #{row.approvalWithdrawalType,jdbcType=TINYINT},
      </if>
      <if test="row.carUseReason != null">
        car_use_reason = #{row.carUseReason,jdbcType=VARCHAR},
      </if>
      <if test="row.orderUserMemo != null">
        order_user_memo = #{row.orderUserMemo,jdbcType=VARCHAR},
      </if>
      <if test="row.expectedPickupTime != null">
        expected_pickup_time = #{row.expectedPickupTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.expectedReturnTime != null">
        expected_return_time = #{row.expectedReturnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.orderStartTime != null">
        order_start_time = #{row.orderStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.orderEndTime != null">
        order_end_time = #{row.orderEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.pickupLotExitTime != null">
        pickup_lot_exit_time = #{row.pickupLotExitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.returnLotEntryTime != null">
        return_lot_entry_time = #{row.returnLotEntryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.verifierTime != null">
        verifier_time = #{row.verifierTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.cancellationTime != null">
        cancellation_time = #{row.cancellationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.totalMileage != null">
        total_mileage = #{row.totalMileage,jdbcType=DECIMAL},
      </if>
      <if test="row.outInFenceTotalMileage != null">
        out_in_fence_total_mileage = #{row.outInFenceTotalMileage,jdbcType=DECIMAL},
      </if>
      <if test="row.updateId != null">
        update_id = #{row.updateId,jdbcType=INTEGER},
      </if>
      <if test="row.updateName != null">
        update_name = #{row.updateName,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.deploymentMode != null">
        deployment_mode = #{row.deploymentMode,jdbcType=TINYINT},
      </if>
      <if test="row.companyId != null">
        company_id = #{row.companyId,jdbcType=INTEGER},
      </if>
      <if test="row.companyCode != null">
        company_code = #{row.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="row.companyName != null">
        company_name = #{row.companyName,jdbcType=VARCHAR},
      </if>
      <if test="row.isShow != null">
        is_show = #{row.isShow,jdbcType=TINYINT},
      </if>
      <if test="row.verifyStatus != null">
        verify_status = #{row.verifyStatus,jdbcType=INTEGER},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update gov_public_car_order
    set order_id = #{row.orderId,jdbcType=BIGINT},
      order_no = #{row.orderNo,jdbcType=VARCHAR},
      order_type = #{row.orderType,jdbcType=INTEGER},
      vehicle_id = #{row.vehicleId,jdbcType=INTEGER},
      vehicle_license = #{row.vehicleLicense,jdbcType=VARCHAR},
      vehicle_vin = #{row.vehicleVin,jdbcType=VARCHAR},
      order_struct_id = #{row.orderStructId,jdbcType=INTEGER},
      order_struct_code = #{row.orderStructCode,jdbcType=VARCHAR},
      order_struct_name = #{row.orderStructName,jdbcType=VARCHAR},
      order_status = #{row.orderStatus,jdbcType=TINYINT},
      approval_status = #{row.approvalStatus,jdbcType=TINYINT},
      approval_id = #{row.approvalId,jdbcType=VARCHAR},
      approval_completed_time = #{row.approvalCompletedTime,jdbcType=TIMESTAMP},
      approval_withdrawal_type = #{row.approvalWithdrawalType,jdbcType=TINYINT},
      car_use_reason = #{row.carUseReason,jdbcType=VARCHAR},
      order_user_memo = #{row.orderUserMemo,jdbcType=VARCHAR},
      expected_pickup_time = #{row.expectedPickupTime,jdbcType=TIMESTAMP},
      expected_return_time = #{row.expectedReturnTime,jdbcType=TIMESTAMP},
      order_start_time = #{row.orderStartTime,jdbcType=TIMESTAMP},
      order_end_time = #{row.orderEndTime,jdbcType=TIMESTAMP},
      pickup_lot_exit_time = #{row.pickupLotExitTime,jdbcType=TIMESTAMP},
      return_lot_entry_time = #{row.returnLotEntryTime,jdbcType=TIMESTAMP},
      verifier_time = #{row.verifierTime,jdbcType=TIMESTAMP},
      cancellation_time = #{row.cancellationTime,jdbcType=TIMESTAMP},
      total_mileage = #{row.totalMileage,jdbcType=DECIMAL},
      out_in_fence_total_mileage = #{row.outInFenceTotalMileage,jdbcType=DECIMAL},
      update_id = #{row.updateId,jdbcType=INTEGER},
      update_name = #{row.updateName,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      deployment_mode = #{row.deploymentMode,jdbcType=TINYINT},
      company_id = #{row.companyId,jdbcType=INTEGER},
      company_code = #{row.companyCode,jdbcType=VARCHAR},
      company_name = #{row.companyName,jdbcType=VARCHAR},
      is_show = #{row.isShow,jdbcType=TINYINT},
      verify_status = #{row.verifyStatus,jdbcType=INTEGER}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.GovPublicCarOrder">
    update gov_public_car_order
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=INTEGER},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=INTEGER},
      </if>
      <if test="vehicleLicense != null">
        vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="vehicleVin != null">
        vehicle_vin = #{vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="orderStructId != null">
        order_struct_id = #{orderStructId,jdbcType=INTEGER},
      </if>
      <if test="orderStructCode != null">
        order_struct_code = #{orderStructCode,jdbcType=VARCHAR},
      </if>
      <if test="orderStructName != null">
        order_struct_name = #{orderStructName,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=TINYINT},
      </if>
      <if test="approvalStatus != null">
        approval_status = #{approvalStatus,jdbcType=TINYINT},
      </if>
      <if test="approvalId != null">
        approval_id = #{approvalId,jdbcType=VARCHAR},
      </if>
      <if test="approvalCompletedTime != null">
        approval_completed_time = #{approvalCompletedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="approvalWithdrawalType != null">
        approval_withdrawal_type = #{approvalWithdrawalType,jdbcType=TINYINT},
      </if>
      <if test="carUseReason != null">
        car_use_reason = #{carUseReason,jdbcType=VARCHAR},
      </if>
      <if test="orderUserMemo != null">
        order_user_memo = #{orderUserMemo,jdbcType=VARCHAR},
      </if>
      <if test="expectedPickupTime != null">
        expected_pickup_time = #{expectedPickupTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expectedReturnTime != null">
        expected_return_time = #{expectedReturnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderStartTime != null">
        order_start_time = #{orderStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderEndTime != null">
        order_end_time = #{orderEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="pickupLotExitTime != null">
        pickup_lot_exit_time = #{pickupLotExitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="returnLotEntryTime != null">
        return_lot_entry_time = #{returnLotEntryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="verifierTime != null">
        verifier_time = #{verifierTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cancellationTime != null">
        cancellation_time = #{cancellationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="totalMileage != null">
        total_mileage = #{totalMileage,jdbcType=DECIMAL},
      </if>
      <if test="outInFenceTotalMileage != null">
        out_in_fence_total_mileage = #{outInFenceTotalMileage,jdbcType=DECIMAL},
      </if>
      <if test="updateId != null">
        update_id = #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deploymentMode != null">
        deployment_mode = #{deploymentMode,jdbcType=TINYINT},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="isShow != null">
        is_show = #{isShow,jdbcType=TINYINT},
      </if>
      <if test="verifyStatus != null">
        verify_status = #{verifyStatus,jdbcType=INTEGER},
      </if>
    </set>
    where order_id = #{orderId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.GovPublicCarOrder">
    update gov_public_car_order
    set order_no = #{orderNo,jdbcType=VARCHAR},
      order_type = #{orderType,jdbcType=INTEGER},
      vehicle_id = #{vehicleId,jdbcType=INTEGER},
      vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      vehicle_vin = #{vehicleVin,jdbcType=VARCHAR},
      order_struct_id = #{orderStructId,jdbcType=INTEGER},
      order_struct_code = #{orderStructCode,jdbcType=VARCHAR},
      order_struct_name = #{orderStructName,jdbcType=VARCHAR},
      order_status = #{orderStatus,jdbcType=TINYINT},
      approval_status = #{approvalStatus,jdbcType=TINYINT},
      approval_id = #{approvalId,jdbcType=VARCHAR},
      approval_completed_time = #{approvalCompletedTime,jdbcType=TIMESTAMP},
      approval_withdrawal_type = #{approvalWithdrawalType,jdbcType=TINYINT},
      car_use_reason = #{carUseReason,jdbcType=VARCHAR},
      order_user_memo = #{orderUserMemo,jdbcType=VARCHAR},
      expected_pickup_time = #{expectedPickupTime,jdbcType=TIMESTAMP},
      expected_return_time = #{expectedReturnTime,jdbcType=TIMESTAMP},
      order_start_time = #{orderStartTime,jdbcType=TIMESTAMP},
      order_end_time = #{orderEndTime,jdbcType=TIMESTAMP},
      pickup_lot_exit_time = #{pickupLotExitTime,jdbcType=TIMESTAMP},
      return_lot_entry_time = #{returnLotEntryTime,jdbcType=TIMESTAMP},
      verifier_time = #{verifierTime,jdbcType=TIMESTAMP},
      cancellation_time = #{cancellationTime,jdbcType=TIMESTAMP},
      total_mileage = #{totalMileage,jdbcType=DECIMAL},
      out_in_fence_total_mileage = #{outInFenceTotalMileage,jdbcType=DECIMAL},
      update_id = #{updateId,jdbcType=INTEGER},
      update_name = #{updateName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      deployment_mode = #{deploymentMode,jdbcType=TINYINT},
      company_id = #{companyId,jdbcType=INTEGER},
      company_code = #{companyCode,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      is_show = #{isShow,jdbcType=TINYINT},
      verify_status = #{verifyStatus,jdbcType=INTEGER}
    where order_id = #{orderId,jdbcType=BIGINT}
  </update>
</mapper>