<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.BillOrderMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.BillOrder">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="bill_order_no" jdbcType="VARCHAR" property="billOrderNo" />
    <result column="order_apply_no" jdbcType="VARCHAR" property="orderApplyNo" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="bill_no" jdbcType="VARCHAR" property="billNo" />
    <result column="policy_type" jdbcType="TINYINT" property="policyType" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="shouldpay_amount" jdbcType="DECIMAL" property="shouldpayAmount" />
    <result column="valuation_fee" jdbcType="DECIMAL" property="valuationFee" />
    <result column="policy_price" jdbcType="DECIMAL" property="policyPrice" />
    <result column="total_mileage" jdbcType="DECIMAL" property="totalMileage" />
    <result column="total_time" jdbcType="DECIMAL" property="totalTime" />
    <result column="total_time_fee" jdbcType="DECIMAL" property="totalTimeFee" />
    <result column="total_mileage_fee" jdbcType="DECIMAL" property="totalMileageFee" />
    <result column="base_fee" jdbcType="DECIMAL" property="baseFee" />
    <result column="base_mileage" jdbcType="DECIMAL" property="baseMileage" />
    <result column="base_mileage_fee" jdbcType="DECIMAL" property="baseMileageFee" />
    <result column="base_time" jdbcType="DECIMAL" property="baseTime" />
    <result column="base_time_fee" jdbcType="DECIMAL" property="baseTimeFee" />
    <result column="over_mileage" jdbcType="DECIMAL" property="overMileage" />
    <result column="over_mileage_fee" jdbcType="DECIMAL" property="overMileageFee" />
    <result column="over_time" jdbcType="DECIMAL" property="overTime" />
    <result column="over_time_fee" jdbcType="DECIMAL" property="overTimeFee" />
    <result column="attach_fee" jdbcType="DECIMAL" property="attachFee" />
    <result column="return_empty_fee" jdbcType="DECIMAL" property="returnEmptyFee" />
    <result column="return_empty_mileage" jdbcType="DECIMAL" property="returnEmptyMileage" />
    <result column="return_empty_price" jdbcType="DECIMAL" property="returnEmptyPrice" />
    <result column="night_service_fee" jdbcType="DECIMAL" property="nightServiceFee" />
    <result column="night_service_time" jdbcType="DECIMAL" property="nightServiceTime" />
    <result column="night_service_price" jdbcType="DECIMAL" property="nightServicePrice" />
    <result column="fixed_price_valid" jdbcType="TINYINT" property="fixedPriceValid" />
    <result column="fixed_price" jdbcType="DECIMAL" property="fixedPrice" />
    <result column="reduction_amount" jdbcType="DECIMAL" property="reductionAmount" />
    <result column="other_amount" jdbcType="DECIMAL" property="otherAmount" />
    <result column="price_explain" jdbcType="VARCHAR" property="priceExplain" />
    <result column="discount_type" jdbcType="TINYINT" property="discountType" />
    <result column="deduction_amount" jdbcType="DECIMAL" property="deductionAmount" />
    <result column="supplier_payable_amount" jdbcType="DECIMAL" property="supplierPayableAmount" />
    <result column="refund_amount_synthesis" jdbcType="DECIMAL" property="refundAmountSynthesis" />
    <result column="refund_status" jdbcType="TINYINT" property="refundStatus" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, create_time, update_time, bill_order_no, order_apply_no, order_no, bill_no, policy_type, 
    total_amount, shouldpay_amount, valuation_fee, policy_price, total_mileage, total_time, 
    total_time_fee, total_mileage_fee, base_fee, base_mileage, base_mileage_fee, base_time, 
    base_time_fee, over_mileage, over_mileage_fee, over_time, over_time_fee, attach_fee, 
    return_empty_fee, return_empty_mileage, return_empty_price, night_service_fee, night_service_time, 
    night_service_price, fixed_price_valid, fixed_price, reduction_amount, other_amount, 
    price_explain, discount_type, deduction_amount, supplier_payable_amount, refund_amount_synthesis, 
    refund_status
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.BillOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from bill_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from bill_order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bill_order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.order.entity.mrcar.BillOrderExample">
    delete from bill_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.BillOrder">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into bill_order (create_time, update_time, bill_order_no, 
      order_apply_no, order_no, bill_no, 
      policy_type, total_amount, shouldpay_amount, 
      valuation_fee, policy_price, total_mileage, 
      total_time, total_time_fee, total_mileage_fee, 
      base_fee, base_mileage, base_mileage_fee, 
      base_time, base_time_fee, over_mileage, 
      over_mileage_fee, over_time, over_time_fee, 
      attach_fee, return_empty_fee, return_empty_mileage, 
      return_empty_price, night_service_fee, night_service_time, 
      night_service_price, fixed_price_valid, fixed_price, 
      reduction_amount, other_amount, price_explain, 
      discount_type, deduction_amount, supplier_payable_amount, 
      refund_amount_synthesis, refund_status)
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{billOrderNo,jdbcType=VARCHAR}, 
      #{orderApplyNo,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, #{billNo,jdbcType=VARCHAR}, 
      #{policyType,jdbcType=TINYINT}, #{totalAmount,jdbcType=DECIMAL}, #{shouldpayAmount,jdbcType=DECIMAL}, 
      #{valuationFee,jdbcType=DECIMAL}, #{policyPrice,jdbcType=DECIMAL}, #{totalMileage,jdbcType=DECIMAL}, 
      #{totalTime,jdbcType=DECIMAL}, #{totalTimeFee,jdbcType=DECIMAL}, #{totalMileageFee,jdbcType=DECIMAL}, 
      #{baseFee,jdbcType=DECIMAL}, #{baseMileage,jdbcType=DECIMAL}, #{baseMileageFee,jdbcType=DECIMAL}, 
      #{baseTime,jdbcType=DECIMAL}, #{baseTimeFee,jdbcType=DECIMAL}, #{overMileage,jdbcType=DECIMAL}, 
      #{overMileageFee,jdbcType=DECIMAL}, #{overTime,jdbcType=DECIMAL}, #{overTimeFee,jdbcType=DECIMAL}, 
      #{attachFee,jdbcType=DECIMAL}, #{returnEmptyFee,jdbcType=DECIMAL}, #{returnEmptyMileage,jdbcType=DECIMAL}, 
      #{returnEmptyPrice,jdbcType=DECIMAL}, #{nightServiceFee,jdbcType=DECIMAL}, #{nightServiceTime,jdbcType=DECIMAL}, 
      #{nightServicePrice,jdbcType=DECIMAL}, #{fixedPriceValid,jdbcType=TINYINT}, #{fixedPrice,jdbcType=DECIMAL}, 
      #{reductionAmount,jdbcType=DECIMAL}, #{otherAmount,jdbcType=DECIMAL}, #{priceExplain,jdbcType=VARCHAR}, 
      #{discountType,jdbcType=TINYINT}, #{deductionAmount,jdbcType=DECIMAL}, #{supplierPayableAmount,jdbcType=DECIMAL}, 
      #{refundAmountSynthesis,jdbcType=DECIMAL}, #{refundStatus,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.BillOrder">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into bill_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="billOrderNo != null">
        bill_order_no,
      </if>
      <if test="orderApplyNo != null">
        order_apply_no,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="billNo != null">
        bill_no,
      </if>
      <if test="policyType != null">
        policy_type,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="shouldpayAmount != null">
        shouldpay_amount,
      </if>
      <if test="valuationFee != null">
        valuation_fee,
      </if>
      <if test="policyPrice != null">
        policy_price,
      </if>
      <if test="totalMileage != null">
        total_mileage,
      </if>
      <if test="totalTime != null">
        total_time,
      </if>
      <if test="totalTimeFee != null">
        total_time_fee,
      </if>
      <if test="totalMileageFee != null">
        total_mileage_fee,
      </if>
      <if test="baseFee != null">
        base_fee,
      </if>
      <if test="baseMileage != null">
        base_mileage,
      </if>
      <if test="baseMileageFee != null">
        base_mileage_fee,
      </if>
      <if test="baseTime != null">
        base_time,
      </if>
      <if test="baseTimeFee != null">
        base_time_fee,
      </if>
      <if test="overMileage != null">
        over_mileage,
      </if>
      <if test="overMileageFee != null">
        over_mileage_fee,
      </if>
      <if test="overTime != null">
        over_time,
      </if>
      <if test="overTimeFee != null">
        over_time_fee,
      </if>
      <if test="attachFee != null">
        attach_fee,
      </if>
      <if test="returnEmptyFee != null">
        return_empty_fee,
      </if>
      <if test="returnEmptyMileage != null">
        return_empty_mileage,
      </if>
      <if test="returnEmptyPrice != null">
        return_empty_price,
      </if>
      <if test="nightServiceFee != null">
        night_service_fee,
      </if>
      <if test="nightServiceTime != null">
        night_service_time,
      </if>
      <if test="nightServicePrice != null">
        night_service_price,
      </if>
      <if test="fixedPriceValid != null">
        fixed_price_valid,
      </if>
      <if test="fixedPrice != null">
        fixed_price,
      </if>
      <if test="reductionAmount != null">
        reduction_amount,
      </if>
      <if test="otherAmount != null">
        other_amount,
      </if>
      <if test="priceExplain != null">
        price_explain,
      </if>
      <if test="discountType != null">
        discount_type,
      </if>
      <if test="deductionAmount != null">
        deduction_amount,
      </if>
      <if test="supplierPayableAmount != null">
        supplier_payable_amount,
      </if>
      <if test="refundAmountSynthesis != null">
        refund_amount_synthesis,
      </if>
      <if test="refundStatus != null">
        refund_status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="billOrderNo != null">
        #{billOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderApplyNo != null">
        #{orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="billNo != null">
        #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="policyType != null">
        #{policyType,jdbcType=TINYINT},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="shouldpayAmount != null">
        #{shouldpayAmount,jdbcType=DECIMAL},
      </if>
      <if test="valuationFee != null">
        #{valuationFee,jdbcType=DECIMAL},
      </if>
      <if test="policyPrice != null">
        #{policyPrice,jdbcType=DECIMAL},
      </if>
      <if test="totalMileage != null">
        #{totalMileage,jdbcType=DECIMAL},
      </if>
      <if test="totalTime != null">
        #{totalTime,jdbcType=DECIMAL},
      </if>
      <if test="totalTimeFee != null">
        #{totalTimeFee,jdbcType=DECIMAL},
      </if>
      <if test="totalMileageFee != null">
        #{totalMileageFee,jdbcType=DECIMAL},
      </if>
      <if test="baseFee != null">
        #{baseFee,jdbcType=DECIMAL},
      </if>
      <if test="baseMileage != null">
        #{baseMileage,jdbcType=DECIMAL},
      </if>
      <if test="baseMileageFee != null">
        #{baseMileageFee,jdbcType=DECIMAL},
      </if>
      <if test="baseTime != null">
        #{baseTime,jdbcType=DECIMAL},
      </if>
      <if test="baseTimeFee != null">
        #{baseTimeFee,jdbcType=DECIMAL},
      </if>
      <if test="overMileage != null">
        #{overMileage,jdbcType=DECIMAL},
      </if>
      <if test="overMileageFee != null">
        #{overMileageFee,jdbcType=DECIMAL},
      </if>
      <if test="overTime != null">
        #{overTime,jdbcType=DECIMAL},
      </if>
      <if test="overTimeFee != null">
        #{overTimeFee,jdbcType=DECIMAL},
      </if>
      <if test="attachFee != null">
        #{attachFee,jdbcType=DECIMAL},
      </if>
      <if test="returnEmptyFee != null">
        #{returnEmptyFee,jdbcType=DECIMAL},
      </if>
      <if test="returnEmptyMileage != null">
        #{returnEmptyMileage,jdbcType=DECIMAL},
      </if>
      <if test="returnEmptyPrice != null">
        #{returnEmptyPrice,jdbcType=DECIMAL},
      </if>
      <if test="nightServiceFee != null">
        #{nightServiceFee,jdbcType=DECIMAL},
      </if>
      <if test="nightServiceTime != null">
        #{nightServiceTime,jdbcType=DECIMAL},
      </if>
      <if test="nightServicePrice != null">
        #{nightServicePrice,jdbcType=DECIMAL},
      </if>
      <if test="fixedPriceValid != null">
        #{fixedPriceValid,jdbcType=TINYINT},
      </if>
      <if test="fixedPrice != null">
        #{fixedPrice,jdbcType=DECIMAL},
      </if>
      <if test="reductionAmount != null">
        #{reductionAmount,jdbcType=DECIMAL},
      </if>
      <if test="otherAmount != null">
        #{otherAmount,jdbcType=DECIMAL},
      </if>
      <if test="priceExplain != null">
        #{priceExplain,jdbcType=VARCHAR},
      </if>
      <if test="discountType != null">
        #{discountType,jdbcType=TINYINT},
      </if>
      <if test="deductionAmount != null">
        #{deductionAmount,jdbcType=DECIMAL},
      </if>
      <if test="supplierPayableAmount != null">
        #{supplierPayableAmount,jdbcType=DECIMAL},
      </if>
      <if test="refundAmountSynthesis != null">
        #{refundAmountSynthesis,jdbcType=DECIMAL},
      </if>
      <if test="refundStatus != null">
        #{refundStatus,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.BillOrderExample" resultType="java.lang.Long">
    select count(*) from bill_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update bill_order
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.billOrderNo != null">
        bill_order_no = #{row.billOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="row.orderApplyNo != null">
        order_apply_no = #{row.orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="row.orderNo != null">
        order_no = #{row.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="row.billNo != null">
        bill_no = #{row.billNo,jdbcType=VARCHAR},
      </if>
      <if test="row.policyType != null">
        policy_type = #{row.policyType,jdbcType=TINYINT},
      </if>
      <if test="row.totalAmount != null">
        total_amount = #{row.totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.shouldpayAmount != null">
        shouldpay_amount = #{row.shouldpayAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.valuationFee != null">
        valuation_fee = #{row.valuationFee,jdbcType=DECIMAL},
      </if>
      <if test="row.policyPrice != null">
        policy_price = #{row.policyPrice,jdbcType=DECIMAL},
      </if>
      <if test="row.totalMileage != null">
        total_mileage = #{row.totalMileage,jdbcType=DECIMAL},
      </if>
      <if test="row.totalTime != null">
        total_time = #{row.totalTime,jdbcType=DECIMAL},
      </if>
      <if test="row.totalTimeFee != null">
        total_time_fee = #{row.totalTimeFee,jdbcType=DECIMAL},
      </if>
      <if test="row.totalMileageFee != null">
        total_mileage_fee = #{row.totalMileageFee,jdbcType=DECIMAL},
      </if>
      <if test="row.baseFee != null">
        base_fee = #{row.baseFee,jdbcType=DECIMAL},
      </if>
      <if test="row.baseMileage != null">
        base_mileage = #{row.baseMileage,jdbcType=DECIMAL},
      </if>
      <if test="row.baseMileageFee != null">
        base_mileage_fee = #{row.baseMileageFee,jdbcType=DECIMAL},
      </if>
      <if test="row.baseTime != null">
        base_time = #{row.baseTime,jdbcType=DECIMAL},
      </if>
      <if test="row.baseTimeFee != null">
        base_time_fee = #{row.baseTimeFee,jdbcType=DECIMAL},
      </if>
      <if test="row.overMileage != null">
        over_mileage = #{row.overMileage,jdbcType=DECIMAL},
      </if>
      <if test="row.overMileageFee != null">
        over_mileage_fee = #{row.overMileageFee,jdbcType=DECIMAL},
      </if>
      <if test="row.overTime != null">
        over_time = #{row.overTime,jdbcType=DECIMAL},
      </if>
      <if test="row.overTimeFee != null">
        over_time_fee = #{row.overTimeFee,jdbcType=DECIMAL},
      </if>
      <if test="row.attachFee != null">
        attach_fee = #{row.attachFee,jdbcType=DECIMAL},
      </if>
      <if test="row.returnEmptyFee != null">
        return_empty_fee = #{row.returnEmptyFee,jdbcType=DECIMAL},
      </if>
      <if test="row.returnEmptyMileage != null">
        return_empty_mileage = #{row.returnEmptyMileage,jdbcType=DECIMAL},
      </if>
      <if test="row.returnEmptyPrice != null">
        return_empty_price = #{row.returnEmptyPrice,jdbcType=DECIMAL},
      </if>
      <if test="row.nightServiceFee != null">
        night_service_fee = #{row.nightServiceFee,jdbcType=DECIMAL},
      </if>
      <if test="row.nightServiceTime != null">
        night_service_time = #{row.nightServiceTime,jdbcType=DECIMAL},
      </if>
      <if test="row.nightServicePrice != null">
        night_service_price = #{row.nightServicePrice,jdbcType=DECIMAL},
      </if>
      <if test="row.fixedPriceValid != null">
        fixed_price_valid = #{row.fixedPriceValid,jdbcType=TINYINT},
      </if>
      <if test="row.fixedPrice != null">
        fixed_price = #{row.fixedPrice,jdbcType=DECIMAL},
      </if>
      <if test="row.reductionAmount != null">
        reduction_amount = #{row.reductionAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.otherAmount != null">
        other_amount = #{row.otherAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.priceExplain != null">
        price_explain = #{row.priceExplain,jdbcType=VARCHAR},
      </if>
      <if test="row.discountType != null">
        discount_type = #{row.discountType,jdbcType=TINYINT},
      </if>
      <if test="row.deductionAmount != null">
        deduction_amount = #{row.deductionAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.supplierPayableAmount != null">
        supplier_payable_amount = #{row.supplierPayableAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.refundAmountSynthesis != null">
        refund_amount_synthesis = #{row.refundAmountSynthesis,jdbcType=DECIMAL},
      </if>
      <if test="row.refundStatus != null">
        refund_status = #{row.refundStatus,jdbcType=TINYINT},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update bill_order
    set id = #{row.id,jdbcType=BIGINT},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      bill_order_no = #{row.billOrderNo,jdbcType=VARCHAR},
      order_apply_no = #{row.orderApplyNo,jdbcType=VARCHAR},
      order_no = #{row.orderNo,jdbcType=VARCHAR},
      bill_no = #{row.billNo,jdbcType=VARCHAR},
      policy_type = #{row.policyType,jdbcType=TINYINT},
      total_amount = #{row.totalAmount,jdbcType=DECIMAL},
      shouldpay_amount = #{row.shouldpayAmount,jdbcType=DECIMAL},
      valuation_fee = #{row.valuationFee,jdbcType=DECIMAL},
      policy_price = #{row.policyPrice,jdbcType=DECIMAL},
      total_mileage = #{row.totalMileage,jdbcType=DECIMAL},
      total_time = #{row.totalTime,jdbcType=DECIMAL},
      total_time_fee = #{row.totalTimeFee,jdbcType=DECIMAL},
      total_mileage_fee = #{row.totalMileageFee,jdbcType=DECIMAL},
      base_fee = #{row.baseFee,jdbcType=DECIMAL},
      base_mileage = #{row.baseMileage,jdbcType=DECIMAL},
      base_mileage_fee = #{row.baseMileageFee,jdbcType=DECIMAL},
      base_time = #{row.baseTime,jdbcType=DECIMAL},
      base_time_fee = #{row.baseTimeFee,jdbcType=DECIMAL},
      over_mileage = #{row.overMileage,jdbcType=DECIMAL},
      over_mileage_fee = #{row.overMileageFee,jdbcType=DECIMAL},
      over_time = #{row.overTime,jdbcType=DECIMAL},
      over_time_fee = #{row.overTimeFee,jdbcType=DECIMAL},
      attach_fee = #{row.attachFee,jdbcType=DECIMAL},
      return_empty_fee = #{row.returnEmptyFee,jdbcType=DECIMAL},
      return_empty_mileage = #{row.returnEmptyMileage,jdbcType=DECIMAL},
      return_empty_price = #{row.returnEmptyPrice,jdbcType=DECIMAL},
      night_service_fee = #{row.nightServiceFee,jdbcType=DECIMAL},
      night_service_time = #{row.nightServiceTime,jdbcType=DECIMAL},
      night_service_price = #{row.nightServicePrice,jdbcType=DECIMAL},
      fixed_price_valid = #{row.fixedPriceValid,jdbcType=TINYINT},
      fixed_price = #{row.fixedPrice,jdbcType=DECIMAL},
      reduction_amount = #{row.reductionAmount,jdbcType=DECIMAL},
      other_amount = #{row.otherAmount,jdbcType=DECIMAL},
      price_explain = #{row.priceExplain,jdbcType=VARCHAR},
      discount_type = #{row.discountType,jdbcType=TINYINT},
      deduction_amount = #{row.deductionAmount,jdbcType=DECIMAL},
      supplier_payable_amount = #{row.supplierPayableAmount,jdbcType=DECIMAL},
      refund_amount_synthesis = #{row.refundAmountSynthesis,jdbcType=DECIMAL},
      refund_status = #{row.refundStatus,jdbcType=TINYINT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.BillOrder">
    update bill_order
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="billOrderNo != null">
        bill_order_no = #{billOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderApplyNo != null">
        order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="billNo != null">
        bill_no = #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="policyType != null">
        policy_type = #{policyType,jdbcType=TINYINT},
      </if>
      <if test="totalAmount != null">
        total_amount = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="shouldpayAmount != null">
        shouldpay_amount = #{shouldpayAmount,jdbcType=DECIMAL},
      </if>
      <if test="valuationFee != null">
        valuation_fee = #{valuationFee,jdbcType=DECIMAL},
      </if>
      <if test="policyPrice != null">
        policy_price = #{policyPrice,jdbcType=DECIMAL},
      </if>
      <if test="totalMileage != null">
        total_mileage = #{totalMileage,jdbcType=DECIMAL},
      </if>
      <if test="totalTime != null">
        total_time = #{totalTime,jdbcType=DECIMAL},
      </if>
      <if test="totalTimeFee != null">
        total_time_fee = #{totalTimeFee,jdbcType=DECIMAL},
      </if>
      <if test="totalMileageFee != null">
        total_mileage_fee = #{totalMileageFee,jdbcType=DECIMAL},
      </if>
      <if test="baseFee != null">
        base_fee = #{baseFee,jdbcType=DECIMAL},
      </if>
      <if test="baseMileage != null">
        base_mileage = #{baseMileage,jdbcType=DECIMAL},
      </if>
      <if test="baseMileageFee != null">
        base_mileage_fee = #{baseMileageFee,jdbcType=DECIMAL},
      </if>
      <if test="baseTime != null">
        base_time = #{baseTime,jdbcType=DECIMAL},
      </if>
      <if test="baseTimeFee != null">
        base_time_fee = #{baseTimeFee,jdbcType=DECIMAL},
      </if>
      <if test="overMileage != null">
        over_mileage = #{overMileage,jdbcType=DECIMAL},
      </if>
      <if test="overMileageFee != null">
        over_mileage_fee = #{overMileageFee,jdbcType=DECIMAL},
      </if>
      <if test="overTime != null">
        over_time = #{overTime,jdbcType=DECIMAL},
      </if>
      <if test="overTimeFee != null">
        over_time_fee = #{overTimeFee,jdbcType=DECIMAL},
      </if>
      <if test="attachFee != null">
        attach_fee = #{attachFee,jdbcType=DECIMAL},
      </if>
      <if test="returnEmptyFee != null">
        return_empty_fee = #{returnEmptyFee,jdbcType=DECIMAL},
      </if>
      <if test="returnEmptyMileage != null">
        return_empty_mileage = #{returnEmptyMileage,jdbcType=DECIMAL},
      </if>
      <if test="returnEmptyPrice != null">
        return_empty_price = #{returnEmptyPrice,jdbcType=DECIMAL},
      </if>
      <if test="nightServiceFee != null">
        night_service_fee = #{nightServiceFee,jdbcType=DECIMAL},
      </if>
      <if test="nightServiceTime != null">
        night_service_time = #{nightServiceTime,jdbcType=DECIMAL},
      </if>
      <if test="nightServicePrice != null">
        night_service_price = #{nightServicePrice,jdbcType=DECIMAL},
      </if>
      <if test="fixedPriceValid != null">
        fixed_price_valid = #{fixedPriceValid,jdbcType=TINYINT},
      </if>
      <if test="fixedPrice != null">
        fixed_price = #{fixedPrice,jdbcType=DECIMAL},
      </if>
      <if test="reductionAmount != null">
        reduction_amount = #{reductionAmount,jdbcType=DECIMAL},
      </if>
      <if test="otherAmount != null">
        other_amount = #{otherAmount,jdbcType=DECIMAL},
      </if>
      <if test="priceExplain != null">
        price_explain = #{priceExplain,jdbcType=VARCHAR},
      </if>
      <if test="discountType != null">
        discount_type = #{discountType,jdbcType=TINYINT},
      </if>
      <if test="deductionAmount != null">
        deduction_amount = #{deductionAmount,jdbcType=DECIMAL},
      </if>
      <if test="supplierPayableAmount != null">
        supplier_payable_amount = #{supplierPayableAmount,jdbcType=DECIMAL},
      </if>
      <if test="refundAmountSynthesis != null">
        refund_amount_synthesis = #{refundAmountSynthesis,jdbcType=DECIMAL},
      </if>
      <if test="refundStatus != null">
        refund_status = #{refundStatus,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.BillOrder">
    update bill_order
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      bill_order_no = #{billOrderNo,jdbcType=VARCHAR},
      order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      bill_no = #{billNo,jdbcType=VARCHAR},
      policy_type = #{policyType,jdbcType=TINYINT},
      total_amount = #{totalAmount,jdbcType=DECIMAL},
      shouldpay_amount = #{shouldpayAmount,jdbcType=DECIMAL},
      valuation_fee = #{valuationFee,jdbcType=DECIMAL},
      policy_price = #{policyPrice,jdbcType=DECIMAL},
      total_mileage = #{totalMileage,jdbcType=DECIMAL},
      total_time = #{totalTime,jdbcType=DECIMAL},
      total_time_fee = #{totalTimeFee,jdbcType=DECIMAL},
      total_mileage_fee = #{totalMileageFee,jdbcType=DECIMAL},
      base_fee = #{baseFee,jdbcType=DECIMAL},
      base_mileage = #{baseMileage,jdbcType=DECIMAL},
      base_mileage_fee = #{baseMileageFee,jdbcType=DECIMAL},
      base_time = #{baseTime,jdbcType=DECIMAL},
      base_time_fee = #{baseTimeFee,jdbcType=DECIMAL},
      over_mileage = #{overMileage,jdbcType=DECIMAL},
      over_mileage_fee = #{overMileageFee,jdbcType=DECIMAL},
      over_time = #{overTime,jdbcType=DECIMAL},
      over_time_fee = #{overTimeFee,jdbcType=DECIMAL},
      attach_fee = #{attachFee,jdbcType=DECIMAL},
      return_empty_fee = #{returnEmptyFee,jdbcType=DECIMAL},
      return_empty_mileage = #{returnEmptyMileage,jdbcType=DECIMAL},
      return_empty_price = #{returnEmptyPrice,jdbcType=DECIMAL},
      night_service_fee = #{nightServiceFee,jdbcType=DECIMAL},
      night_service_time = #{nightServiceTime,jdbcType=DECIMAL},
      night_service_price = #{nightServicePrice,jdbcType=DECIMAL},
      fixed_price_valid = #{fixedPriceValid,jdbcType=TINYINT},
      fixed_price = #{fixedPrice,jdbcType=DECIMAL},
      reduction_amount = #{reductionAmount,jdbcType=DECIMAL},
      other_amount = #{otherAmount,jdbcType=DECIMAL},
      price_explain = #{priceExplain,jdbcType=VARCHAR},
      discount_type = #{discountType,jdbcType=TINYINT},
      deduction_amount = #{deductionAmount,jdbcType=DECIMAL},
      supplier_payable_amount = #{supplierPayableAmount,jdbcType=DECIMAL},
      refund_amount_synthesis = #{refundAmountSynthesis,jdbcType=DECIMAL},
      refund_status = #{refundStatus,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>