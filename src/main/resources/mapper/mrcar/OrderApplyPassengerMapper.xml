<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.OrderApplyPassengerMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.OrderApplyPassenger">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_apply_no" jdbcType="VARCHAR" property="orderApplyNo" />
    <result column="booking_passenger_user_id" jdbcType="INTEGER" property="bookingPassengerUserId" />
    <result column="booking_passenger_user_name" jdbcType="VARCHAR" property="bookingPassengerUserName" />
    <result column="booking_passenger_user_phone" jdbcType="VARCHAR" property="bookingPassengerUserPhone" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="struct_id" jdbcType="INTEGER" property="structId" />
    <result column="struct_name" jdbcType="VARCHAR" property="structName" />
  </resultMap>
  <sql id="Base_Column_List">
    id, order_apply_no, booking_passenger_user_id, booking_passenger_user_name, booking_passenger_user_phone, 
    sort, create_time, update_time, struct_id, struct_name
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_apply_passenger
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from order_apply_passenger
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.OrderApplyPassenger">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_apply_passenger (order_apply_no, booking_passenger_user_id, 
      booking_passenger_user_name, booking_passenger_user_phone, 
      sort, create_time, update_time, 
      struct_id, struct_name)
    values (#{orderApplyNo,jdbcType=VARCHAR}, #{bookingPassengerUserId,jdbcType=INTEGER}, 
      #{bookingPassengerUserName,jdbcType=VARCHAR}, #{bookingPassengerUserPhone,jdbcType=VARCHAR}, 
      #{sort,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{structId,jdbcType=INTEGER}, #{structName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.OrderApplyPassenger">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_apply_passenger
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderApplyNo != null">
        order_apply_no,
      </if>
      <if test="bookingPassengerUserId != null">
        booking_passenger_user_id,
      </if>
      <if test="bookingPassengerUserName != null">
        booking_passenger_user_name,
      </if>
      <if test="bookingPassengerUserPhone != null">
        booking_passenger_user_phone,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="structId != null">
        struct_id,
      </if>
      <if test="structName != null">
        struct_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderApplyNo != null">
        #{orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="bookingPassengerUserId != null">
        #{bookingPassengerUserId,jdbcType=INTEGER},
      </if>
      <if test="bookingPassengerUserName != null">
        #{bookingPassengerUserName,jdbcType=VARCHAR},
      </if>
      <if test="bookingPassengerUserPhone != null">
        #{bookingPassengerUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="structId != null">
        #{structId,jdbcType=INTEGER},
      </if>
      <if test="structName != null">
        #{structName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.OrderApplyPassenger">
    update order_apply_passenger
    <set>
      <if test="orderApplyNo != null">
        order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="bookingPassengerUserId != null">
        booking_passenger_user_id = #{bookingPassengerUserId,jdbcType=INTEGER},
      </if>
      <if test="bookingPassengerUserName != null">
        booking_passenger_user_name = #{bookingPassengerUserName,jdbcType=VARCHAR},
      </if>
      <if test="bookingPassengerUserPhone != null">
        booking_passenger_user_phone = #{bookingPassengerUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="structId != null">
        struct_id = #{structId,jdbcType=INTEGER},
      </if>
      <if test="structName != null">
        struct_name = #{structName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.OrderApplyPassenger">
    update order_apply_passenger
    set order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      booking_passenger_user_id = #{bookingPassengerUserId,jdbcType=INTEGER},
      booking_passenger_user_name = #{bookingPassengerUserName,jdbcType=VARCHAR},
      booking_passenger_user_phone = #{bookingPassengerUserPhone,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      struct_id = #{structId,jdbcType=INTEGER},
      struct_name = #{structName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>