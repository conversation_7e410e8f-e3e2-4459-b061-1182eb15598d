<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.GovCarOrderBillDetailMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.GovCarOrderBillDetail">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="create_id" jdbcType="INTEGER" property="createId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_id" jdbcType="INTEGER" property="updateId" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="bill_no" jdbcType="VARCHAR" property="billNo" />
    <result column="price_code" jdbcType="VARCHAR" property="priceCode" />
    <result column="price_name" jdbcType="VARCHAR" property="priceName" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="price_amount" jdbcType="DECIMAL" property="priceAmount" />
    <result column="price_unit" jdbcType="VARCHAR" property="priceUnit" />
    <result column="quantity" jdbcType="DECIMAL" property="quantity" />
    <result column="fee_notes" jdbcType="VARCHAR" property="feeNotes" />
    <result column="detail_type" jdbcType="TINYINT" property="detailType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, create_id, create_name, create_time, update_id, update_name, update_time, order_no, 
    bill_no, price_code, price_name, total_amount, price_amount, price_unit, quantity, 
    fee_notes, detail_type
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.GovCarOrderBillDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from gov_car_order_bill_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from gov_car_order_bill_detail
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from gov_car_order_bill_detail
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.GovCarOrderBillDetail">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gov_car_order_bill_detail (create_id, create_name, create_time, 
      update_id, update_name, update_time, 
      order_no, bill_no, price_code, 
      price_name, total_amount, price_amount, 
      price_unit, quantity, fee_notes, 
      detail_type)
    values (#{createId,jdbcType=INTEGER}, #{createName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateId,jdbcType=INTEGER}, #{updateName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{orderNo,jdbcType=VARCHAR}, #{billNo,jdbcType=VARCHAR}, #{priceCode,jdbcType=VARCHAR}, 
      #{priceName,jdbcType=VARCHAR}, #{totalAmount,jdbcType=DECIMAL}, #{priceAmount,jdbcType=DECIMAL}, 
      #{priceUnit,jdbcType=VARCHAR}, #{quantity,jdbcType=DECIMAL}, #{feeNotes,jdbcType=VARCHAR}, 
      #{detailType,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.GovCarOrderBillDetail">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gov_car_order_bill_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createId != null">
        create_id,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateId != null">
        update_id,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="billNo != null">
        bill_no,
      </if>
      <if test="priceCode != null">
        price_code,
      </if>
      <if test="priceName != null">
        price_name,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="priceAmount != null">
        price_amount,
      </if>
      <if test="priceUnit != null">
        price_unit,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="feeNotes != null">
        fee_notes,
      </if>
      <if test="detailType != null">
        detail_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createId != null">
        #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="billNo != null">
        #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="priceCode != null">
        #{priceCode,jdbcType=VARCHAR},
      </if>
      <if test="priceName != null">
        #{priceName,jdbcType=VARCHAR},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="priceAmount != null">
        #{priceAmount,jdbcType=DECIMAL},
      </if>
      <if test="priceUnit != null">
        #{priceUnit,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=DECIMAL},
      </if>
      <if test="feeNotes != null">
        #{feeNotes,jdbcType=VARCHAR},
      </if>
      <if test="detailType != null">
        #{detailType,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.GovCarOrderBillDetailExample" resultType="java.lang.Long">
    select count(*) from gov_car_order_bill_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update gov_car_order_bill_detail
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=INTEGER},
      </if>
      <if test="row.createId != null">
        create_id = #{row.createId,jdbcType=INTEGER},
      </if>
      <if test="row.createName != null">
        create_name = #{row.createName,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateId != null">
        update_id = #{row.updateId,jdbcType=INTEGER},
      </if>
      <if test="row.updateName != null">
        update_name = #{row.updateName,jdbcType=VARCHAR},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.orderNo != null">
        order_no = #{row.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="row.billNo != null">
        bill_no = #{row.billNo,jdbcType=VARCHAR},
      </if>
      <if test="row.priceCode != null">
        price_code = #{row.priceCode,jdbcType=VARCHAR},
      </if>
      <if test="row.priceName != null">
        price_name = #{row.priceName,jdbcType=VARCHAR},
      </if>
      <if test="row.totalAmount != null">
        total_amount = #{row.totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.priceAmount != null">
        price_amount = #{row.priceAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.priceUnit != null">
        price_unit = #{row.priceUnit,jdbcType=VARCHAR},
      </if>
      <if test="row.quantity != null">
        quantity = #{row.quantity,jdbcType=DECIMAL},
      </if>
      <if test="row.feeNotes != null">
        fee_notes = #{row.feeNotes,jdbcType=VARCHAR},
      </if>
      <if test="row.detailType != null">
        detail_type = #{row.detailType,jdbcType=TINYINT},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update gov_car_order_bill_detail
    set id = #{row.id,jdbcType=INTEGER},
      create_id = #{row.createId,jdbcType=INTEGER},
      create_name = #{row.createName,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_id = #{row.updateId,jdbcType=INTEGER},
      update_name = #{row.updateName,jdbcType=VARCHAR},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      order_no = #{row.orderNo,jdbcType=VARCHAR},
      bill_no = #{row.billNo,jdbcType=VARCHAR},
      price_code = #{row.priceCode,jdbcType=VARCHAR},
      price_name = #{row.priceName,jdbcType=VARCHAR},
      total_amount = #{row.totalAmount,jdbcType=DECIMAL},
      price_amount = #{row.priceAmount,jdbcType=DECIMAL},
      price_unit = #{row.priceUnit,jdbcType=VARCHAR},
      quantity = #{row.quantity,jdbcType=DECIMAL},
      fee_notes = #{row.feeNotes,jdbcType=VARCHAR},
      detail_type = #{row.detailType,jdbcType=TINYINT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.GovCarOrderBillDetail">
    update gov_car_order_bill_detail
    <set>
      <if test="createId != null">
        create_id = #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        update_id = #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="billNo != null">
        bill_no = #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="priceCode != null">
        price_code = #{priceCode,jdbcType=VARCHAR},
      </if>
      <if test="priceName != null">
        price_name = #{priceName,jdbcType=VARCHAR},
      </if>
      <if test="totalAmount != null">
        total_amount = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="priceAmount != null">
        price_amount = #{priceAmount,jdbcType=DECIMAL},
      </if>
      <if test="priceUnit != null">
        price_unit = #{priceUnit,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=DECIMAL},
      </if>
      <if test="feeNotes != null">
        fee_notes = #{feeNotes,jdbcType=VARCHAR},
      </if>
      <if test="detailType != null">
        detail_type = #{detailType,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.GovCarOrderBillDetail">
    update gov_car_order_bill_detail
    set create_id = #{createId,jdbcType=INTEGER},
      create_name = #{createName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_id = #{updateId,jdbcType=INTEGER},
      update_name = #{updateName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      order_no = #{orderNo,jdbcType=VARCHAR},
      bill_no = #{billNo,jdbcType=VARCHAR},
      price_code = #{priceCode,jdbcType=VARCHAR},
      price_name = #{priceName,jdbcType=VARCHAR},
      total_amount = #{totalAmount,jdbcType=DECIMAL},
      price_amount = #{priceAmount,jdbcType=DECIMAL},
      price_unit = #{priceUnit,jdbcType=VARCHAR},
      quantity = #{quantity,jdbcType=DECIMAL},
      fee_notes = #{feeNotes,jdbcType=VARCHAR},
      detail_type = #{detailType,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>