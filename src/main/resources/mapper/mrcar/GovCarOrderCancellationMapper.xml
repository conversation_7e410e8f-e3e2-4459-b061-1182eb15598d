<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.GovCarOrderCancellationMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.GovCarOrderCancellation">
    <id column="cancellation_no" jdbcType="VARCHAR" property="cancellationNo" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="order_type" jdbcType="TINYINT" property="orderType" />
    <result column="cancellation_reason" jdbcType="VARCHAR" property="cancellationReason" />
    <result column="cancellation_time" jdbcType="TIMESTAMP" property="cancellationTime" />
    <result column="cancelled_by_id" jdbcType="INTEGER" property="cancelledById" />
    <result column="cancelled_by_name" jdbcType="VARCHAR" property="cancelledByName" />
    <result column="cancellation_type" jdbcType="TINYINT" property="cancellationType" />
    <result column="notes" jdbcType="VARCHAR" property="notes" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    cancellation_no, order_no, order_type, cancellation_reason, cancellation_time, cancelled_by_id, 
    cancelled_by_name, cancellation_type, notes
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.GovCarOrderCancellationExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from gov_car_order_cancellation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from gov_car_order_cancellation
    where cancellation_no = #{cancellationNo,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from gov_car_order_cancellation
    where cancellation_no = #{cancellationNo,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.GovCarOrderCancellation">
    insert into gov_car_order_cancellation (cancellation_no, order_no, order_type, 
      cancellation_reason, cancellation_time, 
      cancelled_by_id, cancelled_by_name, cancellation_type, 
      notes)
    values (#{cancellationNo,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, #{orderType,jdbcType=TINYINT}, 
      #{cancellationReason,jdbcType=VARCHAR}, #{cancellationTime,jdbcType=TIMESTAMP}, 
      #{cancelledById,jdbcType=INTEGER}, #{cancelledByName,jdbcType=VARCHAR}, #{cancellationType,jdbcType=TINYINT}, 
      #{notes,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.GovCarOrderCancellation">
    insert into gov_car_order_cancellation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="cancellationNo != null">
        cancellation_no,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="cancellationReason != null">
        cancellation_reason,
      </if>
      <if test="cancellationTime != null">
        cancellation_time,
      </if>
      <if test="cancelledById != null">
        cancelled_by_id,
      </if>
      <if test="cancelledByName != null">
        cancelled_by_name,
      </if>
      <if test="cancellationType != null">
        cancellation_type,
      </if>
      <if test="notes != null">
        notes,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="cancellationNo != null">
        #{cancellationNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=TINYINT},
      </if>
      <if test="cancellationReason != null">
        #{cancellationReason,jdbcType=VARCHAR},
      </if>
      <if test="cancellationTime != null">
        #{cancellationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cancelledById != null">
        #{cancelledById,jdbcType=INTEGER},
      </if>
      <if test="cancelledByName != null">
        #{cancelledByName,jdbcType=VARCHAR},
      </if>
      <if test="cancellationType != null">
        #{cancellationType,jdbcType=TINYINT},
      </if>
      <if test="notes != null">
        #{notes,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.GovCarOrderCancellationExample" resultType="java.lang.Long">
    select count(*) from gov_car_order_cancellation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update gov_car_order_cancellation
    <set>
      <if test="row.cancellationNo != null">
        cancellation_no = #{row.cancellationNo,jdbcType=VARCHAR},
      </if>
      <if test="row.orderNo != null">
        order_no = #{row.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="row.orderType != null">
        order_type = #{row.orderType,jdbcType=TINYINT},
      </if>
      <if test="row.cancellationReason != null">
        cancellation_reason = #{row.cancellationReason,jdbcType=VARCHAR},
      </if>
      <if test="row.cancellationTime != null">
        cancellation_time = #{row.cancellationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.cancelledById != null">
        cancelled_by_id = #{row.cancelledById,jdbcType=INTEGER},
      </if>
      <if test="row.cancelledByName != null">
        cancelled_by_name = #{row.cancelledByName,jdbcType=VARCHAR},
      </if>
      <if test="row.cancellationType != null">
        cancellation_type = #{row.cancellationType,jdbcType=TINYINT},
      </if>
      <if test="row.notes != null">
        notes = #{row.notes,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update gov_car_order_cancellation
    set cancellation_no = #{row.cancellationNo,jdbcType=VARCHAR},
      order_no = #{row.orderNo,jdbcType=VARCHAR},
      order_type = #{row.orderType,jdbcType=TINYINT},
      cancellation_reason = #{row.cancellationReason,jdbcType=VARCHAR},
      cancellation_time = #{row.cancellationTime,jdbcType=TIMESTAMP},
      cancelled_by_id = #{row.cancelledById,jdbcType=INTEGER},
      cancelled_by_name = #{row.cancelledByName,jdbcType=VARCHAR},
      cancellation_type = #{row.cancellationType,jdbcType=TINYINT},
      notes = #{row.notes,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.GovCarOrderCancellation">
    update gov_car_order_cancellation
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=TINYINT},
      </if>
      <if test="cancellationReason != null">
        cancellation_reason = #{cancellationReason,jdbcType=VARCHAR},
      </if>
      <if test="cancellationTime != null">
        cancellation_time = #{cancellationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cancelledById != null">
        cancelled_by_id = #{cancelledById,jdbcType=INTEGER},
      </if>
      <if test="cancelledByName != null">
        cancelled_by_name = #{cancelledByName,jdbcType=VARCHAR},
      </if>
      <if test="cancellationType != null">
        cancellation_type = #{cancellationType,jdbcType=TINYINT},
      </if>
      <if test="notes != null">
        notes = #{notes,jdbcType=VARCHAR},
      </if>
    </set>
    where cancellation_no = #{cancellationNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.GovCarOrderCancellation">
    update gov_car_order_cancellation
    set order_no = #{orderNo,jdbcType=VARCHAR},
      order_type = #{orderType,jdbcType=TINYINT},
      cancellation_reason = #{cancellationReason,jdbcType=VARCHAR},
      cancellation_time = #{cancellationTime,jdbcType=TIMESTAMP},
      cancelled_by_id = #{cancelledById,jdbcType=INTEGER},
      cancelled_by_name = #{cancelledByName,jdbcType=VARCHAR},
      cancellation_type = #{cancellationType,jdbcType=TINYINT},
      notes = #{notes,jdbcType=VARCHAR}
    where cancellation_no = #{cancellationNo,jdbcType=VARCHAR}
  </update>
</mapper>