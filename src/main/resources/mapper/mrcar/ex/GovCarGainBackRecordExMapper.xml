<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.GovCarGainBackRecordExMapper">

    <select id="queryByPage" resultType="com.izu.mrcar.order.dto.govcar.GovCarGainBackRecordPageDTO">
        select
        o.order_no orderNo, o.customer_name customerName, o.customer_mobile customerMobile,
        o.company_code companyCode, o.company_name companyName, o.vehicle_struct_code structCode, o.vehicle_struct_name structName,
        gb.gain_back_no gainBackNo, gb.vehicle_license vehicleLicense,
        g.oil_type gainOilType, g.oil_quantity gainOilQuantity, g.power_quantity gainPowerQuantity,
        g.mileage_quantity gainMileageQuantity, g.check_user_name gainCheckUserName, g.check_start_time
        gainCheckStartTime,
        g.check_end_time gainCheckEndTime,
        b.oil_type backOilType, b.oil_quantity backOilQuantity, b.power_quantity backPowerQuantity,
        b.mileage_quantity backMileageQuantity, b.check_user_name backCheckUserName, b.check_start_time
        backCheckStartTime,
        b.check_end_time backCheckEndTime
        from gov_car_gain_back_record gb
        left join gov_car_order o on o.order_no = gb.order_no
        left join gov_car_check_record g on gb.gain_back_no = g.gain_back_no and g.check_type = 10 and g.check_status = 10
        left join gov_car_check_record b on gb.gain_back_no = b.gain_back_no and b.check_type = 20 and b.check_status = 10
        <where>
            gb. gain_back_status >= 30
            <if test="orderNo != null and orderNo !='' ">
                and gb.order_no = #{orderNo}
            </if>
            <if test="customerId != null">
                and o.customer_id = #{customerId}
            </if>
            <if test="customerName != null and customerName !='' ">
                and o.customer_name = #{customerName}
            </if>
            <if test="customerMobile != null and customerMobile !='' ">
                and o.customer_mobile = #{customerMobile}
            </if>
            <if test="companyCode != null and companyCode !='' ">
                and o.company_code = #{companyCode}
            </if>
            <if test="structCode != null and structCode !='' ">
                and o.vehicle_struct_code = #{structCode}
            </if>
            <if test="gainBackNo != null and gainBackNo !='' ">
                and gb.gain_back_no = #{gainBackNo}
            </if>
            <if test="vehicleLicense != null and vehicleLicense !='' ">
                and gb.vehicle_license = #{vehicleLicense}
            </if>
            <if test="dataPermCodeSet != null and !dataPermCodeSet.isEmpty">
                and (o.vehicle_struct_code in
                <foreach collection="dataPermCodeSet" open="(" close=")" separator="," item="element">#{element}</foreach>
                or o.customer_id = #{loginUserId})
            </if>
        </where>
        order by gb.gain_start_time desc
    </select>
    <select id="queryByOrder" resultMap="mapper.mrcar.GovCarGainBackRecordMapper.BaseResultMap">
        select
        <include refid="mapper.mrcar.GovCarGainBackRecordMapper.Base_Column_List"/>
        from gov_car_gain_back_record
        where order_no = #{orderNo}
        order by gain_start_time desc limit 1
    </select>

    <select id="queryByGainBackNo" resultMap="mapper.mrcar.GovCarGainBackRecordMapper.BaseResultMap">
        select
        <include refid="mapper.mrcar.GovCarGainBackRecordMapper.Base_Column_List"/>
        from gov_car_gain_back_record
        where gain_back_no = #{gainBackNo}
    </select>
</mapper>