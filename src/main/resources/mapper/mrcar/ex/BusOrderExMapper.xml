<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.BusOrderExMapper">
    <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.BusOrder"
               extends="mapper.mrcar.BusOrderMapper.BaseResultMap">
    </resultMap>

    <select id="getListByParam" resultMap="BaseResultMap"
            parameterType="com.izu.mrcar.order.dto.mrcar.bus.req.BusOrderListQueryDTO">
        select
        <include refid="mapper.mrcar.BusOrderMapper.Base_Column_List"></include>
        from bus_order
        <where>
            del_status = 0
            <if test="companyIds != null and companyIds.size()>0">
                and company_id in
                <foreach collection="companyIds" open="(" separator="," close=")" item="companyId">
                    #{companyId}
                </foreach>
            </if>
            <if test="orderNo != null and orderNo !=''">
                and order_no = #{orderNo}
            </if>
            <if test="orderStatus != null and orderStatus.size()>0">
                and order_status in
                <foreach collection="orderStatus" open="(" separator="," close=")" item="orderStatus">
                    #{orderStatus}
                </foreach>
            </if>
            <if test="businessTypeList != null and businessTypeList.size()>0">
                and business_type in
                <foreach collection="businessTypeList" open="(" separator="," close=")" item="businessType">
                    #{businessType}
                </foreach>
            </if>
            <if test="customerId != null">
                and customer_id = #{customerId}
            </if>
            <if test="customerName != null and customerName !=''">
                and customer_Name like  concat('%', #{customerName}, '%')
            </if>
            <if test="businessType != null">
                and business_type = #{businessType}
            </if>
            <if test="assignedVehicleLicense != null and assignedVehicleLicense !=''">
                and assigned_vehicle_license like concat('%', #{assignedVehicleLicense}, '%')
            </if>
            <if test="assignedDriverName != null and assignedDriverName !=''">
                and assigned_driver_name like concat('%', #{assignedDriverName}, '%')
            </if>
            <if test="assignedDriverMobile != null and assignedDriverMobile !=''">
                and assigned_driver_mobile = #{assignedDriverMobile}
            </if>
            <if test="departmentId != null">
                and department_id = #{departmentId}
            </if>
            <if test="departmentCode != null and departmentCode !=''">
                and department_code = #{departmentCode}
            </if>
            <if test="estimatedStartDate != null  and estimatedStartDate!=''">
                and DATE(estimated_start_time) = #{estimatedStartDate}
            </if>
            <if test="estimatedStartTimeStart != null  and estimatedStartTimeStart!=''">
                and estimated_start_time <![CDATA[>=]]> #{estimatedStartTimeStart}
            </if>
            <if test="estimatedStartTimeEnd != null  and estimatedStartTimeEnd!=''">
                and estimated_start_time <![CDATA[<=]]> #{estimatedStartTimeEnd}
            </if>
            <if test="estimatedEndTimeStart != null  and estimatedEndTimeStart!=''">
                and estimated_end_time <![CDATA[>=]]> #{estimatedEndTimeStart}
            </if>
            <if test="estimatedEndTimeEnd != null  and estimatedEndTimeEnd!=''">
                and estimated_end_time <![CDATA[<=]]> #{estimatedEndTimeEnd}
            </if>
            <if test="createName != null and createName !=''">
                and create_name = #{createName}
            </if>
            <if test="createTimeStart != null  and createTimeStart!=''">
                and create_time <![CDATA[>=]]> #{createTimeStart}
            </if>
            <if test="createTimeEnd != null  and createTimeEnd!=''">
                and create_time <![CDATA[<=]]> #{createTimeEnd}
            </if>
            <if test="routeId != null">
                and route_id = #{routeId}
            </if>
            <if test="sourceDepartmentId != null">
                and source_department_id = #{sourceDepartmentId}
            </if>
            <if test="sourceDepartmentCode != null and sourceDepartmentCode !=''">
                and source_department_code = #{sourceDepartmentCode}
            </if>
            <if test="orderCreateType != null">
                and order_create_type = #{orderCreateType}
            </if>
            <if test="isReplace != null">
                <choose>
                    <when test="isReplace">
                        and order_create_type = 3
                    </when>
                    <when test="!isReplace">
                        and order_create_type in (1, 2)
                    </when>
                </choose>
            </if>
            <include refid="dataPerm"></include>
            <if test="busOrderEntry != null">
                <choose>
                    <when test="busOrderEntry == 1">
                        ORDER BY create_time DESC
                    </when>
                    <when test="busOrderEntry == 2 and (showStatus == 1 or showStatus == 7)">
                        ORDER BY estimated_start_time ASC
                    </when>
                    <when test="busOrderEntry == 2 and showStatus == 8">
                        ORDER BY estimated_start_time DESC
                    </when>
                    <when test="busOrderEntry == 3 and showStatus == 9">
                        ORDER BY
                        CASE order_status
                        WHEN 30 THEN 1
                        WHEN 40 THEN 2
                        WHEN 50 THEN 3
                        WHEN 60 THEN 4
                        WHEN 20 THEN 5
                        WHEN 70 THEN 6
                        ELSE 7
                        END ASC,
                        estimated_start_time ASC
                    </when>
                    <when test="busOrderEntry == 3 and showStatus == 10">
                        ORDER BY estimated_start_time DESC
                    </when>
                    <otherwise>
                        ORDER BY create_time ASC
                    </otherwise>
                </choose>
            </if>
            <if test="busOrderEntry == null">
                ORDER BY create_time DESC
            </if>
        </where>
    </select>


    <sql id="dataPerm">
        <!--运营端-->
        <choose>
            <when test="systemType==1 ">
                <choose>
                    <!--本人-->
                    <when test="dataPermType==7">
                        and company_code = -1
                    </when>
                </choose>
            </when>
            <!--客户端-->
            <when test="systemType==2 ">
                <choose>
                    <when test="dataPermType==1">
                        <if test="dataCodeSet != null and dataCodeSet.size()>0">
                            and company_id in
                            <foreach collection="dataCodeSet" item="dataPermItem" open="(" separator="," close=")">
                                #{dataPermItem}
                            </foreach>
                        </if>
                    </when>

                    <when test="dataPermType == 2">
                        <choose>
                            <when test="busOrderEntry != null and busOrderEntry == 3">
                                <if test="dataCodeSet != null and dataCodeSet.size()>0">
                                    and ((department_id in
                                    <foreach collection="dataCodeSet" item="department_id" open="(" separator=","
                                             close=")">
                                        #{department_id}
                                    </foreach>
                                    ) or (assigned_driver_id = #{driverId}))
                                </if>
                            </when>
                            <when test="busOrderEntry == null or busOrderEntry == 1 or busOrderEntry == 2">
                                <if test="dataCodeSet != null and dataCodeSet.size()>0">
                                    and ((department_id in
                                    <foreach collection="dataCodeSet" item="departmentId" open="(" separator=","
                                             close=")">
                                        #{departmentId}
                                    </foreach>
                                    ) or (create_id = #{staffId}))
                                </if>
                            </when>
                        </choose>
                    </when>
                    <when test="dataPermType == 3">
                        <choose>
                            <when test="busOrderEntry == null">
                                and company_id = #{companyId}
                            </when>
                        </choose>
                    </when>
                    <when test="dataPermType == 4">
                        <choose>
                            <when test="busOrderEntry != null and busOrderEntry == 3">
                                and assigned_driver_id = #{driverId}
                            </when>
                            <when test="busOrderEntry == null or busOrderEntry == 1 or busOrderEntry == 2">
                                and create_id = #{staffId}
                            </when>
                        </choose>
                    </when>
                </choose>
            </when>
            <otherwise>
                and order_no = -1
            </otherwise>
        </choose>
    </sql>


    <select id="selectByOrderNo" resultMap="BaseResultMap">
        select
        <include refid="mapper.mrcar.BusOrderMapper.Base_Column_List"></include>
        from bus_order
        where order_no = #{orderNo}
        and del_status = 0
    </select>

    <select id="selectLatestAutoCreationOrderByRuleId" resultMap="BaseResultMap">
        select
        <include refid="mapper.mrcar.BusOrderMapper.Base_Column_List"/>
        from bus_order
        where order_create_type = 1
        and rule_id = #{ruleId}
        order by estimated_start_time desc limit 1
    </select>

    <select id="selectLatestOrderByUserId" resultMap="BaseResultMap">
        select
        <include refid="mapper.mrcar.BusOrderMapper.Base_Column_List"/>
        from bus_order
        where create_id = #{userId}
        order by id desc limit 1
    </select>

</mapper>