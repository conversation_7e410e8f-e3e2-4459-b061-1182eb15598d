<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.BusOrderRouteSnapshotExMapper">
    <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.BusOrderRouteSnapshot" extends="mapper.mrcar.BusOrderRouteSnapshotMapper.BaseResultMap">
    </resultMap>
    <insert id="insertBatch">
        insert into bus_order_route_snapshot
            (order_no, route_id, route_code, station_name, station_address,
            latitude, longitude, station_type, province_id, province_name,
             city_code, city_name, create_id, create_name, sort)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.orderNo},
            #{item.routeId},
            #{item.routeCode},
            #{item.stationName},
            #{item.stationAddress},
            #{item.latitude},
            #{item.longitude},
            #{item.stationType},
            #{item.provinceId},
            #{item.provinceName},
            #{item.cityCode},
            #{item.cityName},
            #{item.createId},
            #{item.createName},
            #{item.sort}
            )
        </foreach>
    </insert>
    <delete id="deleteByOrderNo">
        delete from bus_order_route_snapshot where order_no = #{orderNo}
    </delete>

    <select id="selectByOrderNo" resultMap="BaseResultMap">
        select
            <include refid="mapper.mrcar.BusOrderRouteSnapshotMapper.Base_Column_List"/>
        from bus_order_route_snapshot where order_no = #{orderNo} order by sort asc
    </select>

</mapper>