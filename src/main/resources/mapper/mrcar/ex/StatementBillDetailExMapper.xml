<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.StatementBillDetailExMapper">
    <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.StatementBillDetail"
               extends="mapper.mrcar.StatementBillDetailMapper.BaseResultMap">
    </resultMap>

    <sql id="Base_Column_List">
        <include refid="mapper.mrcar.StatementBillDetailMapper.Base_Column_List"/>
    </sql>
    <update id="updateModifyCount">
        update so_statement_bill_detail set modify_count = #{modifyCount} where statement_bill_num = #{statementBillNum} and modify_count = -1
    </update>
    <update id="updateNewestModifyCount">
        update so_statement_bill_detail set modify_count = -1 where statement_bill_num = #{statementBillNum}
        and modify_count in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>
    <delete id="deleteByPrimaryKeyBatch">
        delete from so_statement_bill_detail
        where statement_bill_detail_id in
        <foreach collection="list" item="id" index="index" separator="," open="(" close=")">
            #{id}
        </foreach>
    </delete>
    <select id="selectByStatementBillNumAndModifyCount" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from so_statement_bill_detail where statement_bill_num = #{statementBillNum} and modify_count = #{modifyCount}
        order by statement_bill_detail_id
    </select>
    <select id="selectByStatementBillNumAndModifyCountList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from so_statement_bill_detail
        where statement_bill_num = #{statementBillNum}
        and modify_count in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
        order by statement_bill_detail_id
    </select>
    <select id="getMaxModifyCount" resultType="java.lang.Integer">
        select max(modify_count) from so_statement_bill_detail where statement_bill_num = #{statementBillNum}
    </select>
    <select id="getSystemBillDetail" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from so_statement_bill_detail
        <where>
            statement_bill_num = #{statementBillNum} and modify_count in
            <foreach collection="modifyCountList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
            <if test="statementBillDetailNum != null and statementBillDetailNum != ''">
                and statement_bill_detail_num = #{statementBillDetailNum}
            </if>
            <if test="customerOrderNum != null and customerOrderNum != ''">
                and customer_order_num = #{customerOrderNum}
            </if>
            <if test="detailType != null">
                and detail_type = #{detailType}
            </if>
            <if test="vehicleLicense != null and vehicleLicense != ''">
                and vehicle_license = #{vehicleLicense}
            </if>
            <if test="driverName != null and driverName != ''">
                and driver_name = #{driverName}
            </if>
            <if test="driverMobile != null and driverMobile != ''">
                and driver_mobile = #{driverMobile}
            </if>
            <if test="driverSource != null">
                and driver_source = #{driverSource}
            </if>
            <if test="driverBelongKey != null and driverBelongKey != ''">
                and driver_belong_key = #{driverBelongKey}
            </if>
            <if test="vehicleSource != null">
                and vehicle_source = #{vehicleSource}
            </if>
            <if test="vehicleBelongKey != null and vehicleBelongKey != ''">
                and vehicle_belong_key = #{vehicleBelongKey}
            </if>
            <!-- 筛选已核对数据 -->
            <if test="checked != null and checked == true">
                and modify_count = -3
            </if>
            <!-- 筛选未核对数据 -->
            <if test="checked != null and checked == false">
                and modify_count = -2
            </if>
            <!-- 筛选重复订单 -->
            <if test="duplicateOrder != null and duplicateOrder == true">
                and customer_order_num in (
                    select
                        customer_order_num
                    from
                        so_statement_bill_detail
                    where
                        statement_bill_num = #{statementBillNum}
                        and modify_count = 0
                    group by
                        customer_order_num
                    having
                        count(*) > 1
                )
            </if>
            <!-- 筛选不重复订单 -->
            <if test="duplicateOrder != null and duplicateOrder == false">
                and customer_order_num in (
                    select
                        customer_order_num
                    from
                        so_statement_bill_detail
                    where
                        statement_bill_num = #{statementBillNum}
                        and modify_count = 0
                    group by
                        customer_order_num
                    having
                        count(*) = 1
                )
            </if>
        </where>
        order by statement_bill_detail_id
    </select>
    <select id="selectTrueWhenAllPushSuccess" resultType="java.lang.Integer">
        select count(if(settle_status=1,1,NULL))=count(*) from so_statement_bill_detail
        where statement_bill_num = #{statementBillNum} and modify_count = #{modifyCount}
    </select>
    <select id="selectLastByStatementBillNumAndCustomerOrderNum" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from so_statement_bill_detail
        where statement_bill_num = #{statementBillNum} and customer_order_num = #{customerOrderNum}
        order by bill_end_time desc limit 1
    </select>
    <select id="selectIncomeAndExpenditureDetail" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from so_statement_bill_detail
        <where>
            <!-- 推送状态为已推送的 -->
            settle_status = 1
            <if test="statementBillDetailNum != null and statementBillDetailNum != ''">
                and statement_bill_detail_num = #{statementBillDetailNum}
            </if>
            <if test="customerOrderNum != null and customerOrderNum != ''">
                and customer_order_num = #{customerOrderNum}
            </if>
            <if test="billType != null">
                and bill_type = #{billType}
            </if>
            <if test="vehicleLicense != null and vehicleLicense != ''">
                and vehicle_license = #{vehicleLicense}
            </if>
            <if test="driverName != null and driverName != ''">
                and driver_name = #{driverName}
            </if>
            <if test="driverMobile != null and driverMobile != ''">
                and driver_mobile = #{driverMobile}
            </if>
            <if test="loginUser != null and loginUser.permType == 1 and loginUser.deptCodes != null and loginUser.deptCodes.size() > 0">
                and subsidiary_company_code in
                <foreach collection="loginUser.deptCodes" item="deptCode" open="(" close=")" separator=",">
                    #{deptCode}
                </foreach>
            </if>
        </where>
        order by statement_bill_detail_id desc
    </select>
</mapper>