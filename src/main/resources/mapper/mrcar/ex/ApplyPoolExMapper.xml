<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.ApplyPoolExMapper">

    <select id="selectApplyListByParam" resultMap="mapper.mrcar.ApplyPoolMapper.ResultMapWithBLOBs">
        select
        <include refid="mapper.mrcar.ApplyPoolMapper.Base_Column_List" />
        ,
        <include refid="mapper.mrcar.ApplyPoolMapper.Blob_Column_List" />
        from apply_pool
        <where>
            valid = TRUE
            <if test="userId != null">
                and create_id = #{userId}
            </if>
            <if test="userId == null">
                <if test="userMobile != null">
                    and create_mobile = #{userMobile}
                </if>
            </if>
            <if test="companyId != null">
                and company_id = #{companyId}
            </if>
            <if test="applyTypeList != null and applyTypeList.size() != 0">
                and apply_type IN
                <foreach collection="applyTypeList" open="(" close=")" separator="," item="applyType">
                    #{applyType}
                </foreach>
            </if>
            <if test="applyStatusList != null and applyStatusList.size() != 0">
                AND apply_status_level_one IN
                <foreach collection="applyStatusList" open="(" close=")" separator="," item="applyStatus">
                    #{applyStatus}
                </foreach>
            </if>
        </where>
        order by create_date desc
    </select>

    <select id="selectApproveListByParam" resultMap="mapper.mrcar.ApplyPoolMapper.ResultMapWithBLOBs">
        select
        <include refid="mapper.mrcar.ApplyPoolMapper.Base_Column_List" />
        ,
        <include refid="mapper.mrcar.ApplyPoolMapper.Blob_Column_List" />
        from apply_pool
        <where>
            valid = TRUE
            <if test="userId != null">
                <if test="permissionList != null and permissionList.size() > 0">
                    and (create_id = #{userId}
                    OR create_city_code in
                    <foreach collection="permissionList" open="(" close=")" separator="," item="cityCode">
                        #{cityCode}
                    </foreach>
                    )
                </if>
            </if>
            <if test="companyId != null">
                and company_id = #{companyId}
            </if>
            <if test="applyType != null">
                and apply_type = #{applyType}
            </if>
            <if test="applyStatus != null and applyStatus != 0">
                AND apply_status_level_one = #{applyStatus}
            </if>
            <if test="departmentIds != null and departmentIds != ''">
                AND (
                CASE WHEN apply_type = 1 THEN create_department_id in
                <foreach item="item" collection="departmentIds.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
                or create_department_id = 0
                else 1=1 end )
            </if>
        </where>
        order by create_date desc
    </select>

    <select id="countApproveForApp" resultType="java.lang.Integer">
        select count(1) from apply_pool
        <where>
            valid = TRUE
            <if test="userId != null">
                <if test="permissionList != null and permissionList.size() > 0">
                    and (create_id = #{userId}
                    OR create_city_code in
                    <foreach collection="permissionList" open="(" close=")" separator="," item="cityCode">
                        #{cityCode}
                    </foreach>
                    )
                </if>
            </if>
            <if test="companyId != null">
                and company_id = #{companyId}
            </if>
            <if test="applyStatus != null and applyStatus != 0">
                AND apply_status_level_one = #{applyStatus}
            </if>
            <if test="departmentIds != null and departmentIds != ''">
                AND (
                CASE WHEN apply_type = 1 THEN create_department_id in
                <foreach item="item" collection="departmentIds.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
                or create_department_id = 0
                else 1=1 end )
            </if>
        </where>
    </select>

    <select id="selectApproveByRelationId" resultMap="mapper.mrcar.ApplyPoolMapper.ResultMapWithBLOBs">
        select
        <include refid="mapper.mrcar.ApplyPoolMapper.Base_Column_List" />
        ,
        <include refid="mapper.mrcar.ApplyPoolMapper.Blob_Column_List" />
        from apply_pool
        WHERE relation_id = #{relationId} limit 1
    </select>

    <select id="selectApproveByRelationNo" resultMap="mapper.mrcar.ApplyPoolMapper.ResultMapWithBLOBs">
        select
        <include refid="mapper.mrcar.ApplyPoolMapper.Base_Column_List" />
        ,
        <include refid="mapper.mrcar.ApplyPoolMapper.Blob_Column_List" />
        from apply_pool
        WHERE relation_no = #{relationNo} limit 1
    </select>

    <select id="selectApproveCount" resultType="java.lang.Integer">
        select
        count(1)
        from apply_pool
        WHERE relation_no = #{relationNo}
    </select>

    <update id="timeOverApply">
        update apply_pool
        set apply_status_level_one = 5,
            update_id              = 0,
            update_name            = 'SYSTEM',
            update_date            = now()
        where relation_id = #{relationId} and apply_type = 2
    </update>

</mapper>