<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.GovCarOrderBillDetailExMapper">
    <insert id="insertBatchSelective" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            INSERT INTO gov_car_order_bill_detail
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.createId != null">
                    create_id,
                </if>
                <if test="item.createName != null">
                    create_name,
                </if>
                <if test="item.createTime != null">
                    create_time,
                </if>
                <if test="item.updateId != null">
                    update_id,
                </if>
                <if test="item.updateName != null">
                    update_name,
                </if>
                <if test="item.updateTime != null">
                    update_time,
                </if>
                <if test="item.orderNo != null">
                    order_no,
                </if>
                <if test="item.billNo != null">
                    bill_no,
                </if>
                <if test="item.priceCode != null">
                    price_code,
                </if>
                <if test="item.priceName != null">
                    price_name,
                </if>
                <if test="item.totalAmount != null">
                    total_amount,
                </if>
                <if test="item.priceAmount != null">
                    price_amount,
                </if>
                <if test="item.priceUnit != null">
                    price_unit,
                </if>
                <if test="item.quantity != null">
                    quantity,
                </if>
                <if test="item.feeNotes != null">
                    fee_notes,
                </if>
                <if test="item.detailType != null">
                    detail_type,
                </if>
            </trim>
            VALUES
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.createId != null">
                    #{item.createId,jdbcType=INTEGER},
                </if>
                <if test="item.createName != null">
                    #{item.createName,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.updateId != null">
                    #{item.updateId,jdbcType=INTEGER},
                </if>
                <if test="item.updateName != null">
                    #{item.updateName,jdbcType=VARCHAR},
                </if>
                <if test="item.updateTime != null">
                    #{item.updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.orderNo != null">
                    #{item.orderNo,jdbcType=VARCHAR},
                </if>
                <if test="item.billNo != null">
                    #{item.billNo,jdbcType=VARCHAR},
                </if>
                <if test="item.priceCode != null">
                    #{item.priceCode,jdbcType=VARCHAR},
                </if>
                <if test="item.priceName != null">
                    #{item.priceName,jdbcType=VARCHAR},
                </if>
                <if test="item.totalAmount != null">
                    #{item.totalAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.priceAmount != null">
                    #{item.priceAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.priceUnit != null">
                    #{item.priceUnit,jdbcType=VARCHAR},
                </if>
                <if test="item.quantity != null">
                    #{item.quantity,jdbcType=DECIMAL},
                </if>
                <if test="item.feeNotes != null">
                    #{item.feeNotes,jdbcType=VARCHAR},
                </if>
                <if test="item.detailType != null">
                    #{item.detailType,jdbcType=TINYINT},
                </if>
            </trim>
        </foreach>
    </insert>

    <select id="selectByBillNo" resultMap="mapper.mrcar.GovCarOrderBillDetailMapper.BaseResultMap">
        select
        <include refid="mapper.mrcar.GovCarOrderBillDetailMapper.Base_Column_List"/>
        from gov_car_order_bill_detail
        where bill_no = #{billNo}
    </select>

    <insert id="batchInsert">
        insert into gov_car_order_bill_detail (create_id, create_name, order_no, bill_no, price_code, price_name,
        total_amount, price_amount, quantity, fee_notes, detail_type) values
        <foreach collection="list" separator="," item="elem">
            (#{elem.createId}, #{elem.createName}, #{elem.orderNo}, #{elem.billNo},
            #{elem.priceCode}, #{elem.priceName}, #{elem.totalAmount}, #{elem.priceAmount},
            #{elem.quantity}, #{elem.feeNotes}, #{elem.detailType})
        </foreach>
    </insert>


    <select id="selectOrderPriceCodes" resultMap="mapper.mrcar.GovCarOrderBillDetailMapper.BaseResultMap">
        select price_code, price_name, sum(total_amount) total_amount
        from gov_car_order_bill_detail
        where order_no = #{orderNo}
        group by price_code;
    </select>

    <select id="selectBillPriceCodes" resultMap="mapper.mrcar.GovCarOrderBillDetailMapper.BaseResultMap">
        select price_code, price_name, sum(total_amount) total_amount
        from gov_car_order_bill_detail
        where bill_no = #{billNo}
        group by price_code;
    </select>

    <select id="selectBillDetailByBillNo" resultMap="mapper.mrcar.GovCarOrderBillDetailMapper.BaseResultMap">
        select
        <include refid="mapper.mrcar.GovCarOrderBillDetailMapper.Base_Column_List"/>
        from gov_car_order_bill_detail
        where bill_no = #{billNo};
    </select>

    <select id="selectByOrderNoAndDetailTypes" resultMap="mapper.mrcar.GovCarOrderBillDetailMapper.BaseResultMap">
        select
        <include refid="mapper.mrcar.GovCarOrderBillDetailMapper.Base_Column_List"/>
        from gov_car_order_bill_detail
        where order_no = #{orderNo}
        <if test="detailTypes != null and detailTypes.size()>0 ">
            and detail_type in
            <foreach collection="detailTypes" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>
