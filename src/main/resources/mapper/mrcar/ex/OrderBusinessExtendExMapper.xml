<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.OrderBusinessExtendExMapper">

  <select id="selectByOrderApplyNo" resultMap="mapper.mrcar.OrderBusinessExtendMapper.ResultMapWithBLOBs">
    select
    <include refid="mapper.mrcar.OrderBusinessExtendMapper.Base_Column_List"/>,
      <include refid="mapper.mrcar.OrderBusinessExtendMapper.Blob_Column_List"/>
    from order_business_extend
    where order_apply_no = #{orderApplyNo}
  </select>

  <select id="selectByOrderNo" resultMap="mapper.mrcar.OrderBusinessExtendMapper.BaseResultMap">
    select
    <include refid="mapper.mrcar.OrderBusinessExtendMapper.Base_Column_List"/>
    from order_business_extend
    where order_no = #{orderNo} limit 1
  </select>

  <update id="updateCommitByOrderNo">
    update order_business_extend set comment_submit =1 where order_apply_no = #{orderApplyNo}
  </update>

  <select id="selectByUseDate" resultType="com.izu.mrcar.order.dto.order.evaluation.OrderSendDTO">
      select a.order_apply_no orderApplyNo,
      b.order_no orderNo,
      b.customer_company_id customerCompanyId,
      b.booking_passenger_user_name bookingPassengerUserName,
      b.booking_passenger_user_phone bookingPassengerUserPhone from
      order_apply a inner join
      order_info b on a.order_apply_no=b.order_apply_no
      where b.booking_order_stime <![CDATA[ >= ]]> #{useDateStart} and
      b.booking_order_stime <![CDATA[ <= ]]> #{useDateEnd} and b.order_status = 90
      and a.appraise_submited = 0 and b.order_type=2
  </select>

    <insert id="batchInsertOrderBusinessExtend">

        INSERT INTO order_business_extend (order_apply_no, order_no,
        handy_dispatch, benefit_card, estimate_amount,
        comment_submit, card_secret,
        vehicle_provider, driver_provider, supplier_provider_code,belong_buss_code,belong_buss_name,operate_buss_name,operate_buss_code,
        expense_status, create_time, update_time,
        price_snapshot)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.orderApplyNo}, #{item.orderNo},
            #{item.handyDispatch}, #{item.benefitCard},
            #{item.estimateAmount},
            #{item.commentSubmit}, #{item.cardSecret},
            #{item.vehicleProvider}, #{item.driverProvider},
            #{item.supplierProviderCode},#{item.belongBussCode},#{item.belongBussName},#{item.operateBussName},#{item.operateBussCode},
            #{item.expenseStatus}, #{item.createTime},
            #{item.updateTime},
            #{item.priceSnapshot})
        </foreach>
    </insert>

    <select id="isApplyUseBenefitCard" resultType="java.lang.Boolean">
        select
        benefit_card
        from order_business_extend
        where order_apply_no = #{orderApplyNo} limit 1
    </select>

    <select id="selectByOrderNos" resultMap="mapper.mrcar.OrderBusinessExtendMapper.BaseResultMap">
        select
        <include refid="mapper.mrcar.OrderBusinessExtendMapper.Base_Column_List"/>
        from order_business_extend
        where order_no in
        <foreach collection="orderNos" open="(" close=")" separator="," item="orderNoItem">
            #{orderNoItem}
        </foreach>
    </select>

    <select id="selectByOrderApplyNos" resultMap="mapper.mrcar.OrderBusinessExtendMapper.BaseResultMap">
        select
        <include refid="mapper.mrcar.OrderBusinessExtendMapper.Base_Column_List"/>
        from order_business_extend
        where order_apply_no in
        <foreach collection="orderApplyNos" open="(" close=")" separator="," item="orderApplyNoItem">
            #{orderApplyNoItem}
        </foreach>
    </select>

    <select id="updatePriceSnapshot" resultType="java.lang.Integer">
        update order_business_extend set price_snapshot = #{priceSnapshot} where id = #{extendId}
    </select>
</mapper>