<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.TemporalSharedVehicleDailyUsageExMapper">

    <select id="getListByDateSet" resultType="com.izu.mrcar.order.dto.timeShare.DayStatisticsListDTO">
        SELECT
        day_time dayTime,
        day_type dayType,
        vehicle_company_id vehicleCompanyId,
        vehicle_company_name vehicleCompanyName,
        vehicle_struct_id vehicleStructId,
        vehicle_struct_name vehicleStructName,
        vehicle_belong_city_code vehicleBelongCityCode,
        vehicle_belong_city_name vehicleBelongCityName,
        vehicle_id vehicleId,
        vehicle_license vehicleLicense,
        vehicle_vin vehicleVin,
        vehicle_brand_id vehicleBrandId,
        vehicle_brand_code vehicleBrandCode,
        vehicle_brand_name vehicleBrandName,
          vehicle_creator_id vehicleCreatorId,
          vehicle_creator_name vehicleCreatorName,
        SUM( daily_duration ) dailyDuration,
        SUM( daily_trip_mileage ) dailyTripMileage
        FROM t_temporal_shared_vehicle_daily_usage
        where record_status = 2
        <if test="dateStr!=null and dateStr.size()>0 ">
            and day_time in
            <foreach collection="dateStr" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
            GROUP BY
            day_time,
            vehicle_company_id,
            vehicle_struct_id,
            vehicle_belong_city_code,
            vehicle_license,
            vehicle_vin
    </select>

    <insert id="batchInsertDailyUsage">
        insert into t_temporal_shared_vehicle_daily_usage
        (warn_sn, record_status, day_time, day_type, daily_start_time, daily_end_time, daily_duration, daily_trip_mileage,
         vehicle_id, vehicle_license, vehicle_vin, vehicle_belong_city_code, vehicle_belong_city_name, vehicle_company_id, vehicle_company_name,
         vehicle_struct_id, vehicle_struct_name, vehicle_brand_id, vehicle_brand_code, vehicle_brand_name, vehicle_creator_id, vehicle_creator_name)
        values
        <foreach collection="list" item="item" separator=",">
            ( #{item.warnSn}, #{item.recordStatus}, #{item.dayTime}, #{item.dayType}, #{item.dailyStartTime}, #{item.dailyEndTime},
              #{item.dailyDuration}, #{item.dailyTripMileage}, #{item.vehicleId}, #{item.vehicleLicense},
              #{item.vehicleVin}, #{item.vehicleBelongCityCode}, #{item.vehicleBelongCityName}, #{item.vehicleCompanyId}, #{item.vehicleCompanyName},
              #{item.vehicleStructId}, #{item.vehicleStructName}, #{item.vehicleBrandId}, #{item.vehicleBrandCode},
              #{item.vehicleBrandName}, #{item.vehicleCreatorId}, #{item.vehicleCreatorName} )
        </foreach>
    </insert>

    <update id="updateRecordStatus">
        update t_temporal_shared_vehicle_daily_usage
        set record_status = #{recordStatus}
        where warn_sn = #{warnSn}
    </update>

    <select id="pageSearch" resultMap="mapper.mrcar.TemporalSharedVehicleDailyUsageMapper.BaseResultMap">
        select
        <include refid="mapper.mrcar.TemporalSharedVehicleDailyUsageMapper.Base_Column_List"/>
        from t_temporal_shared_vehicle_daily_usage
        where record_status = 2
        <if test="vehicleCompanyId != null">
            and vehicle_company_id = #{vehicleCompanyId}
        </if>
        <if test="vehicleStructId != null">
            and vehicle_struct_id = #{vehicleStructId}
        </if>
        <if test="vehicleBelongCityCode != null and vehicleBelongCityCode != ''">
            and vehicle_belong_city_code = #{vehicleBelongCityCode}
        </if>
        <if test="vehicleVin != null and vehicleVin != ''">
            and vehicle_vin = #{vehicleVin}
        </if>
        <if test="vehicleLicense != null and vehicleLicense != ''">
            and vehicle_license like CONCAT('%', #{vehicleLicense},'%')
        </if>
        <if test="dayType != null">
            and day_type = #{dayType}
        </if>
        <if test="warnStartBeginTime != null">
            and daily_start_time <![CDATA[>=]]> #{warnStartBeginTime}
        </if>
        <if test="warnStartEndTime != null">
            and daily_end_time <![CDATA[<=]]> #{warnStartEndTime}
        </if>
        <if test="durationBeginInt != null">
            and daily_duration <![CDATA[>=]]> #{durationBeginInt}
        </if>
        <if test="durationEndInt != null">
            and daily_duration <![CDATA[<=]]> #{durationEndInt}
        </if>
        <if test="companyId != null ">
            and vehicle_company_id = #{companyId}
        </if>
        <if test="dataPermIsNotNull">
            <choose>
                <when test="systemType == 2">
                    and record_status in (1,2)
                    <choose>
                        <when test="dataPermType == 1">
                            and vehicle_company_id in
                            <foreach collection="dataCodeSet" item="item" separator="," open="(" close=")">
                                #{item}
                            </foreach>
                        </when>
                        <when test="dataPermType == 2">
                            and (vehicle_struct_id in
                            <foreach collection="dataCodeSet" item="item" separator="," open="(" close=")">
                                #{item}
                            </foreach>
                            or vehicle_creator_id = #{staffId})
                        </when>
                        <when test="dataPermType == 3">
                            and (vehicle_belong_city_code in
                            <foreach collection="dataCodeSet" item="item" separator="," open="(" close=")">
                                #{item}
                            </foreach>
                            or vehicle_creator_id = #{staffId})
                        </when>
                        <when test="dataPermType == 4">
                            and vehicle_creator_id = #{staffId}
                        </when>
                        <otherwise>
                            and 1 != 1
                        </otherwise>
                    </choose>
                </when>
                <otherwise>
                    and 1 != 1
                </otherwise>
            </choose>
        </if>

        <if test="sortField != null">
            <if test="sortField == 1 ">
                <if test="sortFieldToAsc == null or sortFieldToAsc == false">
                    order by daily_start_time desc
                </if>
                <if test="sortFieldToAsc == true">
                    order by daily_start_time asc
                </if>
            </if>
            <if test="sortField == 2 ">
                <if test="sortFieldToAsc == null or sortFieldToAsc == false">
                    order by daily_duration desc
                </if>
                <if test="sortFieldToAsc == true">
                    order by daily_duration asc
                </if>
            </if>
            <if test="sortField == 3 ">
                <if test="sortFieldToAsc == null or sortFieldToAsc == false">
                    order by daily_trip_mileage desc
                </if>
                <if test="sortFieldToAsc == true">
                    order by daily_trip_mileage asc
                </if>
            </if>
        </if>
        <if test="sortField == null">
            order by daily_start_time desc
        </if>

    </select>

    <select id="pageSearchByProvider" resultMap="mapper.mrcar.TemporalSharedVehicleDailyUsageMapper.BaseResultMap">
        select
        a.*
        from t_temporal_shared_vehicle_daily_usage a
        inner join order_vehicle_base b
        on a.vehicle_license = b.vehicle_license and b.vehicle_status = 1
        and a.record_status = 2
        <if test="vehicleCompanyId != null">
            and a.vehicle_company_id = #{vehicleCompanyId}
        </if>
        <if test="vehicleStructId != null">
            and a.vehicle_struct_id = #{vehicleStructId}
        </if>
        <if test="vehicleBelongCityCode != null and vehicleBelongCityCode != ''">
            and a.vehicle_belong_city_code = #{vehicleBelongCityCode}
        </if>
        <if test="vehicleVin != null and vehicleVin != ''">
            and a.vehicle_vin = #{vehicleVin}
        </if>
        <if test="vehicleLicense != null and vehicleLicense != ''">
            and a.vehicle_license like CONCAT('%', #{vehicleLicense},'%')
        </if>
        <if test="dayType != null">
            and a.day_type = #{dayType}
        </if>
        <if test="warnStartBeginTime != null">
            and a.daily_start_time <![CDATA[>=]]> #{warnStartBeginTime}
        </if>
        <if test="warnStartEndTime != null">
            and a.daily_end_time <![CDATA[<=]]> #{warnStartEndTime}
        </if>
        <if test="durationBeginInt != null">
            and a.daily_duration <![CDATA[>=]]> #{durationBeginInt}
        </if>
        <if test="durationEndInt != null">
            and a.daily_duration <![CDATA[<=]]> #{durationEndInt}
        </if>

        <if test="dataPermIsNotNull">
            <choose>
                <when test="systemType == 1">
                    <choose>
                        <when test="dataPermType == 1 or dataPermType == 2 or dataPermType == 3 or dataPermType == 4  or dataPermType == 5 or dataPermType == 7">
                            and (b.operate_struct_code in
                            <foreach collection="dataCodeSet" item="item" separator="," open="(" close=")">
                                #{item}
                            </foreach>
                            or b.belong_struct_code in
                            <foreach collection="dataCodeSet" item="item" separator="," open="(" close=")">
                                #{item}
                            </foreach>
                            )
                        </when>
                        <when test="dataPermType == 6">
                            and 1 = 1
                        </when>
                        <otherwise>
                            and 1 != 1
                        </otherwise>
                    </choose>
                </when>
                <otherwise>
                    and 1 != 1
                </otherwise>
            </choose>
        </if>

        <if test="sortField != null">
            <if test="sortField == 1 ">
                <if test="sortFieldToAsc == null or sortFieldToAsc == false">
                    order by a.daily_start_time desc
                </if>
                <if test="sortFieldToAsc == true">
                    order by a.daily_start_time asc
                </if>
            </if>
            <if test="sortField == 2 ">
                <if test="sortFieldToAsc == null or sortFieldToAsc == false">
                    order by a.daily_duration desc
                </if>
                <if test="sortFieldToAsc == true">
                    order by a.daily_duration asc
                </if>
            </if>
            <if test="sortField == 3 ">
                <if test="sortFieldToAsc == null or sortFieldToAsc == false">
                    order by a.daily_trip_mileage desc
                </if>
                <if test="sortFieldToAsc == true">
                    order by a.daily_trip_mileage asc
                </if>
            </if>
        </if>
        <if test="sortField == null">
            order by a.daily_start_time desc
        </if>

    </select>


</mapper>