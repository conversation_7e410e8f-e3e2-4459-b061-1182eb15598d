<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.TemporalSharedVehicleStatisticsExMapper">
  <select id="getListByParam" resultMap="mapper.mrcar.TemporalSharedVehicleStatisticsMapper.BaseResultMap">
    select
    <include refid="mapper.mrcar.TemporalSharedVehicleStatisticsMapper.Base_Column_List" />
    from t_temporal_shared_vehicle_statistics
    <where>
        <if test="loginCompanyId != null ">
            and vehicle_company_id = #{loginCompanyId}
        </if>
      <if test="dayTimeStart!=null and dayTimeStart!='' ">
        and day_time <![CDATA[>=]]> #{dayTimeStart}
      </if>
      <if test="dayTimeEnd!=null and dayTimeEnd!='' ">
        and day_time <![CDATA[<=]]> #{dayTimeEnd}
      </if>
      <if test="vehicleCompanyId!=null ">
        and vehicle_company_id = #{vehicleCompanyId}
      </if>
      <if test="vehicleStructId!=null ">
        and vehicle_struct_id = #{vehicleStructId}
      </if>
      <if test="vehicleBelongCityCode!=null and vehicleBelongCityCode!='' ">
        and vehicle_belong_city_code = #{vehicleBelongCityCode}
      </if>
      <if test="vehicleLicense!=null and vehicleLicense!='' ">
        and vehicle_license like CONCAT('%', #{vehicleLicense},'%')
      </if>
      <if test="vehicleVin!=null and vehicleVin!='' ">
        and vehicle_vin = #{vehicleVin}
      </if>
      <if test="dayType!=null ">
        and day_type = #{dayType}
      </if>
      <if test="durationMin!=null ">
        and daily_duration <![CDATA[>=]]> #{durationMin}
      </if>
      <if test="durationMax!=null ">
        and daily_duration <![CDATA[<=]]> #{durationMax}
      </if>
      <if test="dataPermType!=null ">
        <choose>
          <when test="dataPermType == 1">
            and vehicle_company_id in
            <foreach collection="dataPermCodeSet" item="item" separator="," open="(" close=")">
              #{item}
            </foreach>
          </when>
          <when test="dataPermType == 2">
            and (vehicle_struct_id in
            <foreach collection="dataPermCodeSet" item="item" separator="," open="(" close=")">
              #{item}
            </foreach>
            or vehicle_creator_id = #{loginUserId})
          </when>
          <when test="dataPermType == 3">
            and (vehicle_belong_city_code in
            <foreach collection="dataPermCodeSet" item="item" separator="," open="(" close=")">
              #{item}
            </foreach>
            or vehicle_creator_id = #{loginUserId})
          </when>
          <when test="dataPermType == 4">
            and vehicle_creator_id = #{loginUserId}
          </when>
          <otherwise>
            and 1 != 1
          </otherwise>
        </choose>
      </if>
    </where>
    <if test=" sortField == null ">
      order by day_time desc
    </if>
    <if test="sortField!=null and sortField == 1">
      <if test="sortFieldToAsc ==null or sortFieldToAsc == 0">
        order by day_time desc
      </if>
      <if test="sortFieldToAsc == 1">
        order by day_time asc
      </if>
    </if>
    <if test="sortField!=null and sortField == 2">
      <if test="sortFieldToAsc ==null or sortFieldToAsc == 0">
        order by daily_duration desc
      </if>
      <if test="sortFieldToAsc == 1">
        order by daily_duration asc
      </if>
    </if>
    <if test="sortField!=null and sortField == 3">
      <if test="sortFieldToAsc ==null or sortFieldToAsc == 0">
        order by daily_trip_mileage desc
      </if>
      <if test="sortFieldToAsc == 1">
        order by daily_trip_mileage asc
      </if>
    </if>
  </select>

    <select id="getProviderListByParam" resultMap="mapper.mrcar.TemporalSharedVehicleStatisticsMapper.BaseResultMap">
        select
        a.*
        from t_temporal_shared_vehicle_statistics a left join order_vehicle_base b
        on a.vehicle_id = b.vehicle_id
        <where>
            <if test="dayTimeStart!=null and dayTimeStart!='' ">
                and a.day_time <![CDATA[>=]]> #{dayTimeStart}
            </if>
            <if test="dayTimeEnd!=null and dayTimeEnd!='' ">
                and a.day_time <![CDATA[<=]]> #{dayTimeEnd}
            </if>
            <if test="vehicleCompanyId!=null ">
                and a.vehicle_company_id = #{vehicleCompanyId}
            </if>
            <if test="vehicleStructId!=null ">
                and a.vehicle_struct_id = #{vehicleStructId}
            </if>
            <if test="vehicleBelongCityCode!=null and vehicleBelongCityCode!='' ">
                and a.vehicle_belong_city_code = #{vehicleBelongCityCode}
            </if>
            <if test="vehicleLicense!=null and vehicleLicense!='' ">
                and a.vehicle_license like CONCAT('%', #{vehicleLicense},'%')
            </if>
            <if test="vehicleVin!=null and vehicleVin!='' ">
                and a.vehicle_vin = #{vehicleVin}
            </if>
            <if test="dayType!=null ">
                and a.day_type = #{dayType}
            </if>
            <if test="durationMin!=null ">
                and a.daily_duration <![CDATA[>=]]> #{durationMin}
            </if>
            <if test="durationMax!=null ">
                and a.daily_duration <![CDATA[<=]]> #{durationMax}
            </if>
            <if test="dataPermType!=null ">
                <choose>
                    <when test="dataPermType == 1 or dataPermType == 2 or dataPermType == 3 or dataPermType == 4  or dataPermType == 5 or dataPermType == 7">
                        and (b.operate_struct_code in
                        <foreach collection="dataPermCodeSet" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                        or b.belong_struct_code in
                        <foreach collection="dataPermCodeSet" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                        )
                    </when>
                    <when test="dataPermType == 6">
                        and 1 = 1
                    </when>
                    <otherwise>
                        and 1 != 1
                    </otherwise>
                </choose>
            </if>
        </where>
        <if test=" sortField == null ">
            order by day_time desc
        </if>
        <if test="sortField!=null and sortField == 1">
            <if test="sortFieldToAsc ==null or sortFieldToAsc == 0">
                order by day_time desc
            </if>
            <if test="sortFieldToAsc == 1">
                order by day_time asc
            </if>
        </if>
        <if test="sortField!=null and sortField == 2">
            <if test="sortFieldToAsc ==null or sortFieldToAsc == 0">
                order by daily_duration desc
            </if>
            <if test="sortFieldToAsc == 1">
                order by daily_duration asc
            </if>
        </if>
        <if test="sortField!=null and sortField == 3">
            <if test="sortFieldToAsc ==null or sortFieldToAsc == 0">
                order by daily_trip_mileage desc
            </if>
            <if test="sortFieldToAsc == 1">
                order by daily_trip_mileage asc
            </if>
        </if>
    </select>

  <select id="getListByParamForMonth" resultType="com.izu.mrcar.order.dto.timeShare.DayStatisticsListDTO">
    SELECT
    vehicle_company_id vehicleCompanyId,
    vehicle_company_name vehicleCompanyName,
    vehicle_struct_id vehicleStructId,
    vehicle_struct_name vehicleStructName,
    vehicle_belong_city_code vehicleBelongCityCode,
    vehicle_belong_city_name vehicleBelongCityName,
    vehicle_license vehicleLicense,
    vehicle_vin vehicleVin,
    vehicle_brand_code vehicleBrandCode,
    vehicle_brand_name vehicleBrandName,
    SUM( daily_duration ) dailyDuration,
    SUM( daily_trip_mileage ) dailyTripMileage
    FROM t_temporal_shared_vehicle_statistics
    <where>
        <if test="loginCompanyId != null ">
            and vehicle_company_id = #{loginCompanyId}
        </if>
        <if test="dayTimeStart!=null and dayTimeStart!='' ">
            and day_time <![CDATA[>=]]> #{dayTimeStart}
        </if>
        <if test="dayTimeEnd!=null and dayTimeEnd!='' ">
            and day_time <![CDATA[<=]]> #{dayTimeEnd}
        </if>
      <if test="vehicleLicense!=null and vehicleLicense!='' ">
        and vehicle_license like CONCAT('%', #{vehicleLicense},'%')
      </if>
      <if test="vehicleVin!=null and vehicleVin!='' ">
        and vehicle_vin = #{vehicleVin}
      </if>
      <if test="vehicleCompanyId!=null ">
        and vehicle_company_id = #{vehicleCompanyId}
      </if>
      <if test="vehicleStructId!=null ">
        and vehicle_struct_id = #{vehicleStructId}
      </if>
      <if test="vehicleBelongCityCode!=null and vehicleBelongCityCode!='' ">
        and vehicle_belong_city_code = #{vehicleBelongCityCode}
      </if>
        <if test="dataPermType!=null ">
            <choose>
                <when test="dataPermType == 1">
                    and vehicle_company_id in
                    <foreach collection="dataPermCodeSet" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </when>
                <when test="dataPermType == 2">
                    and (vehicle_struct_id in
                    <foreach collection="dataPermCodeSet" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                    or vehicle_creator_id = #{loginUserId})
                </when>
                <when test="dataPermType == 3">
                    and (vehicle_belong_city_code in
                    <foreach collection="dataPermCodeSet" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                    or vehicle_creator_id = #{loginUserId})
                </when>
                <when test="dataPermType == 4">
                    and vehicle_creator_id = #{loginUserId}
                </when>
                <otherwise>
                    and 1 != 1
                </otherwise>
            </choose>
        </if>
    </where>
    GROUP BY
    vehicle_company_id,
    vehicle_struct_id,
    vehicle_belong_city_code,
    vehicle_license,
    vehicle_vin
      <if test=" sortField == null ">
          order by daily_duration desc
      </if>
      <if test="sortField!=null and sortField == 2">
          <if test="sortFieldToAsc ==null or sortFieldToAsc == 0">
              order by daily_duration desc
          </if>
          <if test="sortFieldToAsc == 1">
              order by daily_duration asc
          </if>
      </if>
      <if test="sortField!=null and sortField == 3">
          <if test="sortFieldToAsc ==null or sortFieldToAsc == 0">
              order by daily_trip_mileage desc
          </if>
          <if test="sortFieldToAsc == 1">
              order by daily_trip_mileage asc
          </if>
      </if>
  </select>

    <select id="getProviderListByParamForMonth" resultType="com.izu.mrcar.order.dto.timeShare.DayStatisticsListDTO">
        SELECT
        b.company_id vehicleCompanyId,
        a.vehicle_company_name vehicleCompanyName,
        b.struct_id vehicleStructId,
        b.struct_name vehicleStructName,
        b.belong_city_code vehicleBelongCityCode,
        b.belong_city_name vehicleBelongCityName,
        b.vehicle_license vehicleLicense,
        b.vehicle_vin vehicleVin,
        b.vehicle_brand_id vehicleBrandId,
        b.vehicle_brand_code vehicleBrandCode,
        a.vehicle_brand_name vehicleBrandName,
        SUM( daily_duration ) dailyDuration,
        SUM( daily_trip_mileage ) dailyTripMileage
        FROM t_temporal_shared_vehicle_statistics a left join order_vehicle_base b on a.vehicle_id=b.vehicle_id
        <where>
            <if test="dayTimeStart!=null and dayTimeStart!='' ">
                and a.day_time <![CDATA[>=]]> #{dayTimeStart}
            </if>
            <if test="dayTimeEnd!=null and dayTimeEnd!='' ">
                and a.day_time <![CDATA[<=]]> #{dayTimeEnd}
            </if>
            <if test="vehicleLicense!=null and vehicleLicense!='' ">
                and a.vehicle_license like CONCAT('%', #{vehicleLicense},'%')
            </if>
            <if test="vehicleVin!=null and vehicleVin!='' ">
                and a.vehicle_vin = #{vehicleVin}
            </if>
            <if test="vehicleCompanyId!=null ">
                and b.company_id = #{vehicleCompanyId}
            </if>
            <if test="vehicleStructId!=null ">
                and b.struct_id = #{vehicleStructId}
            </if>
            <if test="vehicleBelongCityCode!=null and vehicleBelongCityCode!='' ">
                and b.belong_city_code = #{vehicleBelongCityCode}
            </if>
            <if test="dataPermType!=null ">
                <choose>
                    <when test="dataPermType == 1 or dataPermType == 2 or dataPermType == 3 or dataPermType == 4  or dataPermType == 5 or dataPermType == 7">
                        and (b.operate_struct_code in
                        <foreach collection="dataPermCodeSet" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                        or b.belong_struct_code in
                        <foreach collection="dataPermCodeSet" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                        )
                    </when>
                    <when test="dataPermType == 6">
                        and 1 = 1
                    </when>
                    <otherwise>
                        and 1 != 1
                    </otherwise>
                </choose>
            </if>
        </where>
        GROUP BY
        b.company_id,
        b.struct_id,
        b.belong_city_code,
        b.vehicle_license,
        b.vehicle_vin
        <if test=" sortField == null ">
            order by dailyDuration desc
        </if>
        <if test="sortField!=null and sortField == 2">
            <if test="sortFieldToAsc ==null or sortFieldToAsc == 0">
                order by dailyDuration desc
            </if>
            <if test="sortFieldToAsc == 1">
                order by dailyDuration asc
            </if>
        </if>
        <if test="sortField!=null and sortField == 3">
            <if test="sortFieldToAsc ==null or sortFieldToAsc == 0">
                order by dailyTripMileage desc
            </if>
            <if test="sortFieldToAsc == 1">
                order by dailyTripMileage asc
            </if>
        </if>
    </select>

  <delete id="deleteByDateSet">
    delete from t_temporal_shared_vehicle_statistics
    where
    <if test="dateStr!=null and dateStr.size()>0 ">
      day_time in
      <foreach collection="dateStr" open="(" close=")" separator="," item="item">
        #{item}
      </foreach>
    </if>
  </delete>

  <insert id="batchInsert">
    insert into t_temporal_shared_vehicle_statistics (day_time, day_type, daily_duration,
    daily_trip_mileage, vehicle_id, vehicle_license,
    vehicle_vin, vehicle_belong_city_code, vehicle_belong_city_name,
    vehicle_company_id, vehicle_company_name, vehicle_struct_id,
    vehicle_struct_name, vehicle_brand_id, vehicle_brand_code,
    vehicle_brand_name,vehicle_creator_id, vehicle_creator_name)
    values
    <foreach collection="list" item="item" separator=",">
     (#{item.dayTime,jdbcType=DATE}, #{item.dayType,jdbcType=TINYINT}, #{item.dailyDuration,jdbcType=INTEGER},
    #{item.dailyTripMileage,jdbcType=DECIMAL}, #{item.vehicleId,jdbcType=BIGINT}, #{item.vehicleLicense,jdbcType=VARCHAR},
    #{item.vehicleVin,jdbcType=VARCHAR}, #{item.vehicleBelongCityCode,jdbcType=VARCHAR}, #{item.vehicleBelongCityName,jdbcType=VARCHAR},
    #{item.vehicleCompanyId,jdbcType=INTEGER}, #{item.vehicleCompanyName,jdbcType=VARCHAR}, #{item.vehicleStructId,jdbcType=INTEGER},
    #{item.vehicleStructName,jdbcType=VARCHAR}, #{item.vehicleBrandId,jdbcType=INTEGER}, #{item.vehicleBrandCode,jdbcType=VARCHAR},
    #{item.vehicleBrandName,jdbcType=VARCHAR},#{item.vehicleCreatorId,jdbcType=INTEGER}, #{item.vehicleCreatorName,jdbcType=VARCHAR})

    </foreach>
  </insert>
</mapper>