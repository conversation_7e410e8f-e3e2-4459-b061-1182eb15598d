<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.GovPublicCarOrderSubDailyExMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.GovPublicCarOrderSubDaily"
             extends="mapper.mrcar.GovPublicCarOrderSubDailyMapper.BaseResultMap">
  </resultMap>
  <select id="selectAllTotalMileage" resultType="java.math.BigDecimal">
    select sum(total_mileage) from gov_public_car_order_sub_daily
    where order_no = #{orderNo}
  </select>

</mapper>