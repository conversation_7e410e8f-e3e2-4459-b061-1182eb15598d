<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.AppraisalTypeConfigExMapper">

  <resultMap id="ExtendResultMap" type="com.izu.mrcar.order.dto.mrcar.AppraisalTypeDTO">
    <id column="evaluation_id" jdbcType="INTEGER" property="evaluationId" />
    <result column="evaluation_name" jdbcType="VARCHAR" property="evaluationName" />
    <result column="evaluation_describe" jdbcType="VARCHAR" property="evaluationDescribe" />
    <result column="dissatisfied_label" jdbcType="VARCHAR" property="dissatisfiedLabel" />
  </resultMap>
  <select id="selectAllAppraisalType" resultMap="ExtendResultMap">
    select
    evaluation_id, evaluation_name, evaluation_describe, dissatisfied_label
    from appraisal_type_config
    where evaluation_status = 1
    <if test="orderType != null and orderType!='' ">
      and order_type = #{orderType}
    </if>
    order by update_time asc
  </select>

  <update id="updateByEvaluationId" parameterType="com.izu.order.entity.mrcar.AppraisalTypeConfig">
    update appraisal_type_config
    set evaluation_status = 2,update_user = #{updateUser,jdbcType=VARCHAR}
    where evaluation_id = #{evaluationId,jdbcType=INTEGER}
  </update>
  <select id="selectByParam" parameterType="com.izu.order.entity.mrcar.AppraisalTypeConfig" resultMap="mapper.mrcar.AppraisalTypeConfigMapper.BaseResultMap">
    select
    <include refid="mapper.mrcar.AppraisalTypeConfigMapper.Base_Column_List" />
    from appraisal_type_config
    <where>
      evaluation_status <![CDATA[!= ]]> 2
      <if test="evaluationName!='' and evaluationName!=null ">
        and evaluation_name like CONCAT('%', #{evaluationName,jdbcType=VARCHAR}, '%')
      </if>
      <if test="evaluationStatus!=null">
        and evaluation_status = #{evaluationStatus,jdbcType=TINYINT}
      </if>
    </where>
    order by update_time desc
  </select>
</mapper>