<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.GovPublicCarOrderTaskExMapper">


  <select id="traverseOrderInfoForClose" resultMap="mapper.mrcar.GovPublicCarOrderTaskMapper.BaseResultMap">
    SELECT <include refid="mapper.mrcar.GovPublicCarOrderTaskMapper.Base_Column_List" />
    from gov_public_car_order_task
    where id &gt; #{startId}
    and task_status = 0
    order by id
    limit #{limit}
  </select>
</mapper>