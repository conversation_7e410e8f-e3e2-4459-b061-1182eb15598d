<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.OrderInfoExMapper">
    <resultMap id="BaseResultMapDTO" type="com.izu.mrcar.order.dto.mrcar.OrderInfoDTO"
               extends="mapper.mrcar.OrderInfoMapper.BaseResultMap">
    </resultMap>
    <resultMap id="BusinessOrderInfoDTO" type="com.izu.order.entity.mrcar.BusinessOrderInfoDTO"
               extends="mapper.mrcar.OrderInfoMapper.BaseResultMap">
        <result property="driverProvider" column="driver_provider"/>
        <result property="vehicleProvider" column="vehicle_provider"/>
        <result property="expenditureStatus" column="expense_status"/>
        <result property="supplierProviderCode" column="supplier_provider_code"/>
    </resultMap>
    <sql id="Base_Column_ListDTO">
        <include refid="mapper.mrcar.OrderInfoMapper.Base_Column_List"/>
    </sql>
    <select id="selectOneByOrderNo" resultMap="mapper.mrcar.OrderInfoMapper.BaseResultMap">
        select
        <include refid="mapper.mrcar.OrderInfoMapper.Base_Column_List"/>
        from order_info
        where order_no = #{orderNo}
        limit 1
    </select>
    <select id="selectUnCancelListByApplyNo" resultMap="mapper.mrcar.OrderInfoMapper.BaseResultMap">
        select
        <include refid="mapper.mrcar.OrderInfoMapper.Base_Column_List"/>
        from order_info
        where order_apply_no = #{orderApplyNo} AND order_status!=100
    </select>
    <select id="selectCountUnCancelOrderByApplyNo" resultType="int">
        select
        count(1)
        from order_info
        where order_apply_no = #{orderApplyNo} AND order_status!=100
    </select>
    <select id="selectByOrderApplyNo" resultMap="mapper.mrcar.OrderInfoMapper.BaseResultMap">
        select
        <include refid="mapper.mrcar.OrderInfoMapper.Base_Column_List"/>
        from order_info
        where order_apply_no = #{orderApplyNo} order by fact_end_date desc
    </select>
    <select id="selectCompletedOrders" resultMap="mapper.mrcar.OrderInfoMapper.BaseResultMap">
        select
        <include refid="mapper.mrcar.OrderInfoMapper.Base_Column_List"/>
        from order_info
        where order_status=90 and order_apply_no = #{orderApplyNo} order by fact_end_date desc
    </select>
    <select id="selectDriverOrderList" resultMap="mapper.mrcar.OrderInfoMapper.BaseResultMap">
        select
        <include refid="mapper.mrcar.OrderInfoMapper.Base_Column_List"/>
        from order_info
        where assign_driver_id = #{driverId}
        and DATE_FORMAT(booking_order_stime,'%Y-%m-%d') = #{bookingOrderStimeStr}
        order by booking_order_stime asc
    </select>

    <select id="selectDriverHomePageOrderList" parameterType="com.izu.mrcar.order.dto.order.DriverAcceptOrderListReqDTO" resultMap="mapper.mrcar.OrderInfoMapper.BaseResultMap">
        select
        <include refid="mapper.mrcar.OrderInfoMapper.Base_Column_List"/>
        from order_info
        where
            <choose>
                <when test="systemType!=null and systemType==2">
                    (assign_driver_id = #{queryDriverId} or (assign_driver_id = #{queryDriverId} and company_id = #{loginCompanyId})
                    <if test="companyIdList != null and companyIdList.size()>0 ">
                        or company_id in
                        <foreach item="
                        item" collection="companyIdList" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    )
                </when>
                <otherwise>
                     assign_driver_id = #{queryDriverId}
                </otherwise>
            </choose>
        <choose>
            <when test="driverSourceType!=null and driverSourceType==4">
<!--                查询所有-->
            </when>
            <otherwise>
                <if test="systemType!=null">
                    <choose>
                        <when test="systemType == 1">
                            <!--如果登录的是运营端，只查询商务用车-->
                            and order_type = 2
                        </when>
                        <otherwise>
                            <!--登录的是客户端，查询内部用车-->
                            and order_type = 1
                        </otherwise>
                    </choose>
                </if>
            </otherwise>
        </choose>

        <if test="createTimeStart!=null">
            and create_time >= #{createTimeStart}
        </if>
        <if test="createTimeEnd!=null">
            and create_time &lt;=#{createTimeEnd}
        </if>
        <if test="orderStatus != null">
            <if test="orderStatus == 1">
                and order_status in (10,20,25,26)
            </if>
            <if test="orderStatus == 2">
                and order_status in (30,60)
            </if>
            <if test="orderStatus == 3">
                and order_status in (90)
            </if>
            <if test="orderStatus == 4">
                and order_status in (100)
            </if>
            <if test="orderStatus == 5">
                and order_status in (20,25,26,30,40,50,60)
            </if>
        </if>
        <if test="bookingOrderStimeSort!=null">
            order by  booking_order_stime
            <choose>
                <when test="bookingOrderStimeSort == 1">
                    asc
                </when>
                <otherwise>
                    desc
                </otherwise>
            </choose>
        </if>
    </select>

    <select id="selectUnCompleteOrderByApplyNo" resultType="int">
        select count(1)
        from order_info
        where order_apply_no = #{orderApplyNo}
          and order_status not in (90, 100);
    </select>

    <select id="selectUnFinishTripByApplyNo" resultType="int">
        select count(1)
        from order_info
        where order_apply_no = #{orderApplyNo}
          and order_status in (10, 20, 25, 26, 30)
    </select>
    <!-- 查询过期未提交订单费用补录的订单 -->
    <select id="selectExpireOrderBillAttach" resultMap="mapper.mrcar.OrderInfoMapper.BaseResultMap">
        select order_no, fact_end_date
        from order_info
        where order_status = 60
        limit #{page},#{limit}
    </select>
    <select id="selectOrderNosByOrderApplyNo" resultType="java.lang.String">
        select order_no as orderNo
        from order_info
        where order_apply_no = #{orderApplyNo}
          and order_status = 90
    </select>

    <insert id="insertSelectiveBatch" parameterType="com.izu.order.entity.mrcar.OrderInfo">
        <foreach collection="listOrderInfo" item="orderInfo" index="index">
            <if test="index==0">
                insert into order_info
                <trim prefix="(" suffix=")" suffixOverrides=",">
                    <if test="orderInfo.createTime != null">
                        create_time,
                    </if>
                    <if test="orderInfo.updateTime != null">
                        update_time,
                    </if>
                    <if test="orderInfo.orderNo != null">
                        order_no,
                    </if>
                    <if test="orderInfo.orderApplyNo != null">
                        order_apply_no,
                    </if>
                    <if test="orderInfo.companyId != null">
                        company_id,
                    </if>
                    <if test="orderInfo.structId != null">
                        struct_id,
                    </if>
                    <if test="orderInfo.orderType != null">
                        order_type,
                    </if>
                    <if test="orderInfo.serviceCode != null">
                        service_code,
                    </if>
                    <if test="orderInfo.customerId != null">
                        customer_id,
                    </if>
                    <if test="orderInfo.customerName != null">
                        customer_name,
                    </if>
                    <if test="orderInfo.customerMobile != null">
                        customer_mobile,
                    </if>
                    <if test="orderInfo.himself != null">
                        himself,
                    </if>
                    <if test="orderInfo.bookingPassengerUserId != null">
                        booking_passenger_user_id,
                    </if>
                    <if test="orderInfo.bookingPassengerUserName != null">
                        booking_passenger_user_name,
                    </if>
                    <if test="orderInfo.bookingPassengerUserPhone != null">
                        booking_passenger_user_phone,
                    </if>
                    <if test="orderInfo.bookingOrderStime != null">
                        booking_order_stime,
                    </if>
                    <if test="orderInfo.bookingOrderEtime != null">
                        booking_order_etime,
                    </if>
                    <if test="orderInfo.channelOrderCode != null">
                        channel_order_code,
                    </if>
                    <if test="orderInfo.bookingStartLongAddr != null">
                        booking_start_long_addr,
                    </if>
                    <if test="orderInfo.bookingStartShortAddr != null">
                        booking_start_short_addr,
                    </if>
                    <if test="orderInfo.bookingStartPoint != null">
                        booking_start_point,
                    </if>
                    <if test="orderInfo.bookingEndLongAddr != null">
                        booking_end_long_addr,
                    </if>
                    <if test="orderInfo.bookingEndShortAddr != null">
                        booking_end_short_addr,
                    </if>
                    <if test="orderInfo.bookingEndPoint != null">
                        booking_end_point,
                    </if>
                    <if test="orderInfo.startCityCode != null">
                        start_city_code,
                    </if>
                    <if test="orderInfo.startCityName != null">
                        start_city_name,
                    </if>
                    <if test="orderInfo.endCityCode != null">
                        end_city_code,
                    </if>
                    <if test="orderInfo.endCityName != null">
                        end_city_name,
                    </if>
                    <if test="orderInfo.bookingCarlevelId != null">
                        booking_carlevel_id,
                    </if>
                    <if test="orderInfo.bookingCarlevelName != null">
                        booking_carlevel_name,
                    </if>
                    <if test="orderInfo.orderCancellationType != null">
                        order_cancellation_type,
                    </if>
                    <if test="orderInfo.assignCarCanAble != null">
                        assign_car_can_able,
                    </if>
                    <if test="orderInfo.assignCarStatus != null">
                        assign_car_status,
                    </if>
                    <if test="orderInfo.assignCarApplyTime != null">
                        assign_car_apply_time,
                    </if>
                    <if test="orderInfo.assignCarApplyCount != null">
                        assign_car_apply_count,
                    </if>
                    <if test="orderInfo.assignCarSuccessTime != null">
                        assign_car_success_time,
                    </if>
                    <if test="orderInfo.assignCarFailureTime != null">
                        assign_car_failure_time,
                    </if>
                    <if test="orderInfo.assignCarlevelId != null">
                        assign_carlevel_id,
                    </if>
                    <if test="orderInfo.assignCarlevelName != null">
                        assign_carlevel_name,
                    </if>
                    <if test="orderInfo.assignCarId != null">
                        assign_car_id,
                    </if>
                    <if test="orderInfo.assignCarLicense != null">
                        assign_car_license,
                    </if>
                    <if test="orderInfo.assignDriverId != null">
                        assign_driver_id,
                    </if>
                    <if test="orderInfo.assignDriverName != null">
                        assign_driver_name,
                    </if>
                    <if test="orderInfo.assignDriverPhone != null">
                        assign_driver_phone,
                    </if>
                    <if test="orderInfo.assignDriverCityCode != null">
                        assign_driver_city_code,
                    </if>
                    <if test="orderInfo.assignDriverCityName != null">
                        assign_driver_city_name,
                    </if>
                    <if test="orderInfo.assignCarmodelId != null">
                        assign_carmodel_id,
                    </if>
                    <if test="orderInfo.assignCarmodelName != null">
                        assign_carmodel_name,
                    </if>
                    <if test="orderInfo.orderCancelNo != null">
                        order_cancel_no,
                    </if>
                    <if test="orderInfo.orderCancelTime != null">
                        order_cancel_time,
                    </if>
                    <if test="orderInfo.appraiseSubmited != null">
                        appraise_submited,
                    </if>
                    <if test="orderInfo.appraiseSubmitTime != null">
                        appraise_submit_time,
                    </if>
                    <if test="orderInfo.trafficType != null">
                        traffic_type,
                    </if>
                    <if test="orderInfo.trafficNumber != null">
                        traffic_number,
                    </if>
                    <if test="orderInfo.factStartLongAddr != null">
                        fact_start_long_addr,
                    </if>
                    <if test="orderInfo.factStartShortAddr != null">
                        fact_start_short_addr,
                    </if>
                    <if test="orderInfo.factStartPoint != null">
                        fact_start_point,
                    </if>
                    <if test="orderInfo.factEndLongAddr != null">
                        fact_end_long_addr,
                    </if>
                    <if test="orderInfo.factEndShortAddr != null">
                        fact_end_short_addr,
                    </if>
                    <if test="orderInfo.factEndPoint != null">
                        fact_end_point,
                    </if>
                    <if test="orderInfo.factStartDate != null">
                        fact_start_date,
                    </if>
                    <if test="orderInfo.factEndDate != null">
                        fact_end_date,
                    </if>
                    <if test="orderInfo.driverStartTime != null">
                        driver_start_time,
                    </if>
                    <if test="orderInfo.driverArriveTime != null">
                        driver_arrive_time,
                    </if>
                    <if test="orderInfo.getOnTime != null">
                        get_on_time,
                    </if>
                    <if test="orderInfo.orderStatus != null">
                        order_status,
                    </if>
                    <if test="orderInfo.orderDetail != null">
                        order_detail,
                    </if>
                    <if test="orderInfo.mileageSource != null">
                        mileage_source,
                    </if>
                    <if test="orderInfo.tripMileage != null">
                        trip_mileage,
                    </if>
                    <if test="orderInfo.motorcadeId != null">
                        motorcade_id,
                    </if>
                    <if test="orderInfo.motorcadeName != null">
                        motorcade_name,
                    </if>
                    <if test="orderInfo.customerCompanyId != null">
                        customer_company_id,
                    </if>
                    <if test="orderInfo.customerCompanyCode != null">
                        customer_company_code,
                    </if>
                    <if test="orderInfo.customerCompanyName != null">
                        customer_company_name,
                    </if>
                    <if test="orderInfo.companyName != null">
                        company_name,
                    </if>
                    <if test="orderInfo.structName != null">
                        struct_name,
                    </if>
                    <if test="orderInfo.passengerStructId != null">
                        passenger_struct_id,
                    </if>
                    <if test="orderInfo.passengerStructName != null">
                        passenger_struct_name,
                    </if>
                    <if test="orderInfo.fixedPriceValid !=null">
                        fixed_price_valid,
                    </if>
                    <if test="orderInfo.fixedPrice!=null">
                        fixed_price,
                    </if>
                </trim>
                values
            </if>
        </foreach>
        <foreach collection="listOrderInfo" item="orderInfo" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="orderInfo.createTime != null">
                    #{orderInfo.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="orderInfo.updateTime != null">
                    #{orderInfo.updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="orderInfo.orderNo != null">
                    #{orderInfo.orderNo,jdbcType=VARCHAR},
                </if>
                <if test="orderInfo.orderApplyNo != null">
                    #{orderInfo.orderApplyNo,jdbcType=VARCHAR},
                </if>
                <if test="orderInfo.companyId != null">
                    #{orderInfo.companyId,jdbcType=INTEGER},
                </if>
                <if test="orderInfo.structId != null">
                    #{orderInfo.structId,jdbcType=INTEGER},
                </if>
                <if test="orderInfo.orderType != null">
                    #{orderInfo.orderType,jdbcType=TINYINT},
                </if>
                <if test="orderInfo.serviceCode != null">
                    #{orderInfo.serviceCode,jdbcType=VARCHAR},
                </if>
                <if test="orderInfo.customerId != null">
                    #{orderInfo.customerId,jdbcType=INTEGER},
                </if>
                <if test="orderInfo.customerName != null">
                    #{orderInfo.customerName,jdbcType=VARCHAR},
                </if>
                <if test="orderInfo.customerMobile != null">
                    #{orderInfo.customerMobile,jdbcType=VARCHAR},
                </if>
                <if test="orderInfo.himself != null">
                    #{orderInfo.himself,jdbcType=BIT},
                </if>
                <if test="orderInfo.bookingPassengerUserId != null">
                    #{orderInfo.bookingPassengerUserId,jdbcType=BIGINT},
                </if>
                <if test="orderInfo.bookingPassengerUserName != null">
                    #{orderInfo.bookingPassengerUserName,jdbcType=VARCHAR},
                </if>
                <if test="orderInfo.bookingPassengerUserPhone != null">
                    #{orderInfo.bookingPassengerUserPhone,jdbcType=VARCHAR},
                </if>
                <if test="orderInfo.bookingOrderStime != null">
                    #{orderInfo.bookingOrderStime,jdbcType=TIMESTAMP},
                </if>
                <if test="orderInfo.bookingOrderEtime != null">
                    #{orderInfo.bookingOrderEtime,jdbcType=TIMESTAMP},
                </if>
                <if test="orderInfo.channelOrderCode != null">
                    #{orderInfo.channelOrderCode,jdbcType=VARCHAR},
                </if>
                <if test="orderInfo.bookingStartLongAddr != null">
                    #{orderInfo.bookingStartLongAddr,jdbcType=VARCHAR},
                </if>
                <if test="orderInfo.bookingStartShortAddr != null">
                    #{orderInfo.bookingStartShortAddr,jdbcType=VARCHAR},
                </if>
                <if test="orderInfo.bookingStartPoint != null">
                    #{orderInfo.bookingStartPoint,jdbcType=VARCHAR},
                </if>
                <if test="orderInfo.bookingEndLongAddr != null">
                    #{orderInfo.bookingEndLongAddr,jdbcType=VARCHAR},
                </if>
                <if test="orderInfo.bookingEndShortAddr != null">
                    #{orderInfo.bookingEndShortAddr,jdbcType=VARCHAR},
                </if>
                <if test="orderInfo.bookingEndPoint != null">
                    #{orderInfo.bookingEndPoint,jdbcType=VARCHAR},
                </if>
                <if test="orderInfo.startCityCode != null">
                    #{orderInfo.startCityCode,jdbcType=INTEGER},
                </if>
                <if test="orderInfo.startCityName != null">
                    #{orderInfo.startCityName,jdbcType=VARCHAR},
                </if>
                <if test="orderInfo.endCityCode != null">
                    #{orderInfo.endCityCode,jdbcType=INTEGER},
                </if>
                <if test="orderInfo.endCityName != null">
                    #{orderInfo.endCityName,jdbcType=VARCHAR},
                </if>
                <if test="orderInfo.bookingCarlevelId != null">
                    #{orderInfo.bookingCarlevelId,jdbcType=TINYINT},
                </if>
                <if test="orderInfo.bookingCarlevelName != null">
                    #{orderInfo.bookingCarlevelName,jdbcType=VARCHAR},
                </if>
                <if test="orderInfo.orderCancellationType != null">
                    #{orderInfo.orderCancellationType,jdbcType=TINYINT},
                </if>
                <if test="orderInfo.assignCarCanAble != null">
                    #{orderInfo.assignCarCanAble,jdbcType=BIT},
                </if>
                <if test="orderInfo.assignCarStatus != null">
                    #{orderInfo.assignCarStatus,jdbcType=TINYINT},
                </if>
                <if test="orderInfo.assignCarApplyTime != null">
                    #{orderInfo.assignCarApplyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="orderInfo.assignCarApplyCount != null">
                    #{orderInfo.assignCarApplyCount,jdbcType=SMALLINT},
                </if>
                <if test="orderInfo.assignCarSuccessTime != null">
                    #{orderInfo.assignCarSuccessTime,jdbcType=TIMESTAMP},
                </if>
                <if test="orderInfo.assignCarFailureTime != null">
                    #{orderInfo.assignCarFailureTime,jdbcType=TIMESTAMP},
                </if>
                <if test="orderInfo.assignCarlevelId != null">
                    #{orderInfo.assignCarlevelId,jdbcType=TINYINT},
                </if>
                <if test="orderInfo.assignCarlevelName != null">
                    #{orderInfo.assignCarlevelName,jdbcType=VARCHAR},
                </if>
                <if test="orderInfo.assignCarId != null">
                    #{orderInfo.assignCarId,jdbcType=BIGINT},
                </if>
                <if test="orderInfo.assignCarLicense != null">
                    #{orderInfo.assignCarLicense,jdbcType=VARCHAR},
                </if>
                <if test="orderInfo.assignDriverId != null">
                    #{orderInfo.assignDriverId,jdbcType=BIGINT},
                </if>
                <if test="orderInfo.assignDriverName != null">
                    #{orderInfo.assignDriverName,jdbcType=VARCHAR},
                </if>
                <if test="orderInfo.assignDriverPhone != null">
                    #{orderInfo.assignDriverPhone,jdbcType=VARCHAR},
                </if>
                <if test="orderInfo.assignDriverCityCode != null">
                    #{orderInfo.assignDriverCityCode,jdbcType=INTEGER},
                </if>
                <if test="orderInfo.assignDriverCityName != null">
                    #{orderInfo.assignDriverCityName,jdbcType=VARCHAR},
                </if>
                <if test="orderInfo.assignCarmodelId != null">
                    #{orderInfo.assignCarmodelId,jdbcType=INTEGER},
                </if>
                <if test="orderInfo.assignCarmodelName != null">
                    #{orderInfo.assignCarmodelName,jdbcType=VARCHAR},
                </if>
                <if test="orderInfo.orderCancelNo != null">
                    #{orderInfo.orderCancelNo,jdbcType=VARCHAR},
                </if>
                <if test="orderInfo.orderCancelTime != null">
                    #{orderInfo.orderCancelTime,jdbcType=TIMESTAMP},
                </if>
                <if test="orderInfo.appraiseSubmited != null">
                    #{orderInfo.appraiseSubmited,jdbcType=BIT},
                </if>
                <if test="orderInfo.appraiseSubmitTime != null">
                    #{orderInfo.appraiseSubmitTime,jdbcType=TIMESTAMP},
                </if>
                <if test="orderInfo.trafficType != null">
                    #{orderInfo.trafficType,jdbcType=TINYINT},
                </if>
                <if test="orderInfo.trafficNumber != null">
                    #{orderInfo.trafficNumber,jdbcType=VARCHAR},
                </if>
                <if test="orderInfo.factStartLongAddr != null">
                    #{orderInfo.factStartLongAddr,jdbcType=VARCHAR},
                </if>
                <if test="orderInfo.factStartShortAddr != null">
                    #{orderInfo.factStartShortAddr,jdbcType=VARCHAR},
                </if>
                <if test="orderInfo.factStartPoint != null">
                    #{orderInfo.factStartPoint,jdbcType=VARCHAR},
                </if>
                <if test="orderInfo.factEndLongAddr != null">
                    #{orderInfo.factEndLongAddr,jdbcType=VARCHAR},
                </if>
                <if test="orderInfo.factEndShortAddr != null">
                    #{orderInfo.factEndShortAddr,jdbcType=VARCHAR},
                </if>
                <if test="orderInfo.factEndPoint != null">
                    #{orderInfo.factEndPoint,jdbcType=VARCHAR},
                </if>
                <if test="orderInfo.factStartDate != null">
                    #{orderInfo.factStartDate,jdbcType=TIMESTAMP},
                </if>
                <if test="orderInfo.factEndDate != null">
                    #{orderInfo.factEndDate,jdbcType=TIMESTAMP},
                </if>
                <if test="orderInfo.driverStartTime != null">
                    #{orderInfo.driverStartTime,jdbcType=TIMESTAMP},
                </if>
                <if test="orderInfo.driverArriveTime != null">
                    #{orderInfo.driverArriveTime,jdbcType=TIMESTAMP},
                </if>
                <if test="orderInfo.getOnTime != null">
                    #{orderInfo.getOnTime,jdbcType=TIMESTAMP},
                </if>
                <if test="orderInfo.orderStatus != null">
                    #{orderInfo.orderStatus,jdbcType=SMALLINT},
                </if>
                <if test="orderInfo.orderDetail != null">
                    #{orderInfo.orderDetail,jdbcType=VARCHAR},
                </if>
                <if test="orderInfo.mileageSource != null">
                    #{orderInfo.mileageSource,jdbcType=TINYINT},
                </if>
                <if test="orderInfo.tripMileage != null">
                    #{orderInfo.tripMileage,jdbcType=DECIMAL},
                </if>
                <if test="orderInfo.motorcadeId != null">
                    #{orderInfo.motorcadeId,jdbcType=INTEGER},
                </if>
                <if test="orderInfo.motorcadeName != null">
                    #{orderInfo.motorcadeName,jdbcType=VARCHAR},
                </if>
                <if test="orderInfo.customerCompanyId != null">
                    #{orderInfo.customerCompanyId,jdbcType=INTEGER},
                </if>
                <if test="orderInfo.customerCompanyCode != null">
                    #{orderInfo.customerCompanyCode,jdbcType=VARCHAR},
                </if>
                <if test="orderInfo.customerCompanyName != null">
                    #{orderInfo.customerCompanyName,jdbcType=VARCHAR},
                </if>
                <if test="orderInfo.companyName != null">
                    #{orderInfo.companyName,jdbcType=VARCHAR},
                </if>
                <if test="orderInfo.structName != null">
                    #{orderInfo.structName,jdbcType=VARCHAR},
                </if>
                <if test="orderInfo.passengerStructId != null">
                    #{orderInfo.passengerStructId,jdbcType=INTEGER},
                </if>
                <if test="orderInfo.passengerStructName != null">
                    #{orderInfo.passengerStructName,jdbcType=VARCHAR},
                </if>
                <if test="orderInfo.fixedPriceValid !=null">
                    #{orderInfo.fixedPriceValid},
                </if>
                <if test="orderInfo.fixedPrice!=null">
                    #{orderInfo.fixedPrice},
                </if>
            </trim>
        </foreach>
    </insert>
    <resultMap id="BaseResultMap" type="com.izu.mrcar.order.dto.mrcar.OrderInfoExport">
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="order_apply_no" jdbcType="VARCHAR" property="orderApplyNo"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="channel_order_code" jdbcType="VARCHAR" property="channelOrderCode"/>
        <result column="order_type" jdbcType="TINYINT" property="orderType"/>
        <result column="service_code" jdbcType="VARCHAR" property="serviceCode"/>
        <result column="customer_name" jdbcType="VARCHAR" property="customerName"/>
        <result column="customer_mobile" jdbcType="VARCHAR" property="customerMobile"/>
        <result column="booking_passenger_user_name" jdbcType="VARCHAR" property="bookingPassengerUserName"/>
        <result column="booking_passenger_user_phone" jdbcType="VARCHAR" property="bookingPassengerUserPhone"/>
        <result column="contact_name" jdbcType="VARCHAR" property="contactName"/>
        <result column="contact_mobile" jdbcType="VARCHAR" property="contactMobile"/>
        <result column="booking_order_stime" jdbcType="TIMESTAMP" property="bookingOrderStime"/>
        <result column="booking_order_etime" jdbcType="TIMESTAMP" property="bookingOrderEtime"/>
        <result column="fact_end_date" jdbcType="TIMESTAMP" property="factEndDate"/>
        <result column="fact_start_date" jdbcType="TIMESTAMP" property="factStartDate"/>
        <result column="start_city_name" jdbcType="VARCHAR" property="startCityName"/>
        <result column="booking_start_short_addr" jdbcType="VARCHAR" property="bookingStartShortAddr"/>
        <result column="booking_end_city_name" jdbcType="VARCHAR" property="endCityName"/>
        <result column="booking_end_short_addr" jdbcType="VARCHAR" property="bookingEndShortAddr"/>
        <result column="assign_carlevel_name" jdbcType="VARCHAR" property="assignCarlevelName"/>
        <result column="assign_carmodel_name" jdbcType="VARCHAR" property="assignCarmodelName"/>
        <result column="vehicle_brand" jdbcType="VARCHAR" property="vehicleBrand"/>
        <result column="assign_car_license" jdbcType="VARCHAR" property="assignCarLicense"/>
        <result column="assign_driver_name" jdbcType="VARCHAR" property="assignDriverName"/>
        <result column="assign_driver_phone" jdbcType="VARCHAR" property="assignDriverPhone"/>
        <result column="total_amount" jdbcType="DECIMAL" property="totalAmount"/>
        <result column="order_status" jdbcType="TINYINT" property="orderStatus"/>
        <result column="apply_status" jdbcType="TINYINT" property="applyStatus"/>
        <result column="total_mileage" jdbcType="INTEGER" property="totalMileage"/>
        <result column="total_time" jdbcType="VARCHAR" property="totalTime"/>
        <result column="total_second" jdbcType="DECIMAL" property="totalSecond"/>
        <result column="base_fee" jdbcType="DECIMAL" property="baseFee"/>
        <result column="total_mileage_fee" jdbcType="DECIMAL" property="totalMileageFee"/>
        <result column="total_time_fee" jdbcType="DECIMAL" property="totalTimeFee"/>
        <result column="total_fee_amount" jdbcType="DECIMAL" property="totalFeeAmount"/>
        <result column="fee_amount" jdbcType="DECIMAL" property="feeAmount"/>
        <result column="fee_amount2" jdbcType="DECIMAL" property="feeAmount2"/>
        <result column="fee_amount3" jdbcType="DECIMAL" property="feeAmount3"/>
        <result column="fee_amount4" jdbcType="DECIMAL" property="feeAmount4"/>
        <result column="fee_amount5" jdbcType="DECIMAL" property="feeAmount5"/>
        <result column="return_empty_fee" jdbcType="DECIMAL" property="returnEmptyFee"/>
        <result column="fee_amount_remark" jdbcType="VARCHAR" property="feeAmountRemark"/>
        <result column="other_amount_sub" jdbcType="DECIMAL" property="otherAmountSub"/>
        <result column="reduction_amount_sub" jdbcType="DECIMAL" property="reductionAmountSub"/>
        <result column="other_amount" jdbcType="DECIMAL" property="otherAmount"/>
        <result column="reduction_amount" jdbcType="DECIMAL" property="reductionAmount"/>
        <result column="coupon_deduct_amount" jdbcType="DECIMAL" property="couponDeductAmount"/>
        <result column="all_total_amount" jdbcType="DECIMAL" property="allTotalAmount"/>
        <result column="company_name" jdbcType="VARCHAR" property="companyName"/>
        <result column="activity_name" jdbcType="VARCHAR" property="activityName"/>
        <result column="pay_type" jdbcType="TINYINT" property="payType"/>
        <result column="business_name" jdbcType="VARCHAR" property="businessName"/>
        <result column="fixed_price" jdbcType="VARCHAR" property="fixAmount"/>
        <result column="price_explain" jdbcType="VARCHAR" property="priceExplain"/>
        <result column="order_detail" jdbcType="VARCHAR" property="orderDetail"/>
        <result column="customer_company_name" jdbcType="VARCHAR" property="customerCompanyName"/>
        <result column="settle_company_name" jdbcType="VARCHAR" property="settleCompanyName"/>
        <result column="struct_name" jdbcType="VARCHAR" property="structName"/>
        <result column="passenger_struct_name" jdbcType="VARCHAR" property="passengerStructName"/>
        <result column="cancel_user_name" jdbcType="VARCHAR" property="cancelUserName" />
        <result column="audit_flag" jdbcType="TINYINT" property="auditFlag" />
        <result column="assign_car_id" jdbcType="BIGINT" property="assignCarId" />
        <result column="start_manual_mileage" jdbcType="DECIMAL" property="startManualMileage"/>
        <result column="end_manual_mileage" jdbcType="DECIMAL" property="endManualMileage"/>
    </resultMap>
    <select id="selectOrderNoList" resultMap="BaseResultMap">
        SELECT
        DISTINCT o.order_no,
        oa.audit_flag,
        oa.order_apply_no,
        oa.create_time,
        o.channel_order_code,
        o.order_detail,
        o.order_type,
        o.service_code,
        o.customer_id,
        o.customer_name,
        o.customer_mobile,
        o.booking_passenger_user_id,
        o.booking_passenger_user_name,
        o.booking_passenger_user_phone,
        oa.customer_no customerNo,
        oa.booking_passenger_customer_no bookingPassengerCustomerNo,
        oa.contact_name,
        oa.contact_mobile,
        oa.business_name,
        o.booking_order_stime,
        o.booking_order_etime,
        o.fact_end_date,
        o.fact_start_date,
        oa.booking_start_city_name start_city_name,
        o.booking_start_short_addr,
        o.booking_end_short_addr,
        oa.booking_end_city_name,
        o.assign_carlevel_name,
        o.assign_carmodel_name,
        v.vehicle_brand,
        o.assign_car_license,
        o.assign_driver_name,
        o.assign_driver_phone,
        o.order_status order_status,
        oa.order_status apply_status,
        b.total_mileage,
        b.total_time,
        b.total_time total_second,
        b.base_fee,
        b.total_mileage_fee,
        b.total_time_fee,
        b.attach_fee total_fee_amount ,
        b.other_amount other_amount_sub,
        b.reduction_amount reduction_amount_sub,
        a.fee_amount,
        a2.fee_amount fee_amount2,
        a3.fee_amount fee_amount3,
        a4.fee_amount fee_amount4,
        a5.fee_amount fee_amount5,
        a6.remarks fee_amount_remark,
        bi.total_amount total_amount,
        bi.fixed_price,
        bi.price_explain,
        bi.other_amount,
        bi.reduction_amount,
        bi.coupon_deduct_amount,
        bi.shouldpay_amount all_total_amount,
        oa.pay_type,
        o.company_name,
        '' as activity_name,
        o.customer_company_name,
        oa.settle_company_name,
        oa.struct_name struct_name,
        oa.passenger_struct_name passenger_struct_name,
        cancel.cancel_user_name,
        o.assign_car_id,
        oia.start_manual_mileage start_manual_mileage,
        oia.end_manual_mileage end_manual_mileage
        FROM
        order_apply oa
        LEFT JOIN order_info o ON o.order_apply_no = oa.order_apply_no
        LEFT JOIN order_vehicle_base v ON o.assign_car_id = v.vehicle_id
        LEFT JOIN bill_order b ON o.order_no = b.order_no
        LEFT JOIN bill_attach a ON o.order_no=a.order_no AND a.attach_code=2
        LEFT JOIN bill_attach a2 ON o.order_no=a2.order_no AND a2.attach_code=1
        LEFT JOIN bill_attach a3 ON o.order_no=a3.order_no AND a3.attach_code=3
        LEFT JOIN bill_attach a4 ON o.order_no=a4.order_no AND a4.attach_code=4
        LEFT JOIN bill_attach a5 ON o.order_no=a5.order_no AND a5.attach_code=5
        LEFT JOIN bill_attach a6 ON o.order_no=a6.order_no AND a6.attach_code=99
        LEFT JOIN bill bi ON bi.order_apply_no=oa.order_apply_no
        left join order_cancel_info cancel on o.order_cancel_no = cancel.order_cancel_no
        left join order_info_attach oia on o.order_no = oia.order_no
        <where>
            oa.deleted = 0
            <if test="orderApplyNo != null and orderApplyNo != ''">
               and oa.order_apply_no = #{orderApplyNo}
            </if>
            <if test="orderType != null ">
                and oa.order_type = #{orderType}
            </if>
            <if test="serviceCode != null and serviceCode !=''">
                and oa.service_code in
                <foreach item="item" collection="serviceCode.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="customerName != null and customerName != ''">
                and oa.customer_name = #{customerName}
            </if>
            <if test="companyId != null ">
                and oa.company_id = #{companyId}
            </if>
            <if test="bookingPassengerUserName != null and bookingPassengerUserName != ''">
                and oa.booking_passenger_user_name = #{bookingPassengerUserName}
            </if>
            <if test="customerCompanyId != null ">
                and oa.customer_company_id = #{customerCompanyId}
            </if>
            <if test="structId != null ">
                and oa.struct_id = #{structId}
            </if>
            <if test="passengerStructId != null ">
                and oa.passenger_struct_id = #{passengerStructId}
            </if>
            <if test="orderStatus != null and orderStatus!=''">
                and oa.order_status in
                <foreach item="item" collection="orderStatus.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="createTimeStart != null and createTimeStart != ''">
                and oa.create_time <![CDATA[>=]]> #{createTimeStart}
            </if>
            <if test="createTimeEnd != null and createTimeEnd != ''">
                and oa.create_time <![CDATA[<=]]> #{createTimeEnd}
            </if>
            <if test="bookingOrderStime != null and bookingOrderStime != ''">
                and oa.booking_order_stime <![CDATA[>=]]> #{bookingOrderStime}
            </if>
            <if test="bookingOrderEtime != null and bookingOrderEtime != ''">
                and oa.booking_order_stime <![CDATA[<=]]> #{bookingOrderEtime}
            </if>
            <if test="customerMobile != null and customerMobile != ''">
                and oa.customer_mobile = #{customerMobile}
            </if>
            <if test="bookingPassengerUserPhone != null and bookingPassengerUserPhone != ''">
                and oa.booking_passenger_user_phone = #{bookingPassengerUserPhone}
            </if>
<!--            <if test="permissions != null and permissions != ''">
                and (oa.booking_start_city_code in
                <foreach item="cityCode" collection="permissions.split(',')" open="(" separator="," close=")">
                    #{cityCode}
                </foreach>
                or oa.booking_end_city_code IN
                <foreach item="cityCode" collection="permissions.split(',')" open="(" separator="," close=")">
                    #{cityCode}
                </foreach>
                or oa.customer_id = #{loginId}
                )
            </if>-->
            <if test="permissions != null and permissions != ''">
                <!--指定部门 -->
                <if test="dataPermType!=null and dataPermType ==2">
                    and (oa.struct_id in
                    <foreach item="item" collection="permissions.split(',')" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    or oa.passenger_struct_id IN
                    <foreach item="item" collection="permissions.split(',')" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    or oa.customer_id = #{loginId}
                    )
                </if>
                <!--指定城市 -->
                <if test="dataPermType!=null and dataPermType ==3">
                    and (oa.booking_start_city_code in
                    <foreach item="cityCode" collection="permissions.split(',')" open="(" separator="," close=")">
                        #{cityCode}
                    </foreach>
                    or oa.booking_end_city_code IN
                    <foreach item="cityCode" collection="permissions.split(',')" open="(" separator="," close=")">
                        #{cityCode}
                    </foreach>
                    or oa.customer_id = #{loginId}
                    )
                </if>
            </if>
            <!--本人权限 -->
            <if test="dataPermType!=null and dataPermType ==4">
                and (oa.customer_id = #{loginId})
            </if>
<!--            <if test="loginCompanyId != null">
                and oa.company_id = #{loginCompanyId}
            </if>-->
            <if test="companyIds != null and companyIds != ''">
                and oa.company_id in
                <foreach item="item" collection="companyIds.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="auditFlag != null">
                and oa.audit_flag = #{auditFlag}
            </if>
            <if test="onlySelf != null and onlySelf == true">
                and ((oa.order_type in (1, 2) and oa.customer_id = #{loginId}) or (oa.order_type = 3 and oa.booking_passenger_user_id = #{loginId}))
            </if>
            /*and oa.order_status != 5*/
        </where>
        <choose>
            <when test="sort != null and sort != ''">
                order by ${sort}
            </when>
            <otherwise>
                order by create_time desc
            </otherwise>
        </choose>

    </select>

    <select id="selectProviderOrderNoList" resultMap="BaseResultMap">
        SELECT
        DISTINCT o.order_no,
        oa.audit_flag,
        oa.order_apply_no,
        oa.create_time,
        o.channel_order_code,
        o.order_detail,
        o.order_type,
        o.service_code,
        o.customer_name,
        o.customer_mobile,
        o.booking_passenger_user_name,
        o.booking_passenger_user_phone,
        oa.contact_name,
        oa.contact_mobile,
        oa.business_name,
        o.booking_order_stime,
        o.booking_order_etime,
        o.fact_end_date,
        o.fact_start_date,
        oa.booking_start_city_name start_city_name,
        o.booking_start_short_addr,
        o.booking_end_short_addr,
        oa.booking_end_city_name,
        o.assign_carlevel_name,
        o.assign_carmodel_name,
        v.vehicle_brand,
        o.assign_car_license,
        o.assign_driver_name,
        o.assign_driver_phone,
        o.order_status order_status,
        oa.order_status apply_status,
        b.total_mileage,
        b.total_time,
        b.total_time total_second,
        b.base_fee,
        b.total_mileage_fee,
        b.total_time_fee,
        b.attach_fee total_fee_amount ,
        b.other_amount other_amount_sub,
        b.reduction_amount reduction_amount_sub,
        a.fee_amount,
        a2.fee_amount fee_amount2,
        a3.fee_amount fee_amount3,
        a4.fee_amount fee_amount4,
        a5.fee_amount fee_amount5,
        b.return_empty_fee return_empty_fee,
        a6.remarks fee_amount_remark,
        bi.total_amount total_amount,
        bi.fixed_price,
        bi.price_explain,
        bi.other_amount,
        bi.reduction_amount,
        bi.coupon_deduct_amount,
        bi.shouldpay_amount all_total_amount,
        oa.pay_type,
        o.company_name,
        '' as activity_name,
        o.customer_company_name,
        oa.settle_company_name,
        oa.struct_name struct_name,
        oa.passenger_struct_name passenger_struct_name,
        cancel.cancel_user_name,
        o.assign_car_id,
        oia.start_manual_mileage as start_manual_mileage,
        oia.end_manual_mileage as end_manual_mileage
        FROM
        order_apply oa
        LEFT JOIN order_info o ON o.order_apply_no = oa.order_apply_no
        LEFT JOIN order_vehicle_base v ON o.assign_car_id = v.vehicle_id
        LEFT JOIN bill_order b ON o.order_no = b.order_no
        LEFT JOIN bill_attach a ON o.order_no=a.order_no AND a.attach_code=2
        LEFT JOIN bill_attach a2 ON o.order_no=a2.order_no AND a2.attach_code=1
        LEFT JOIN bill_attach a3 ON o.order_no=a3.order_no AND a3.attach_code=3
        LEFT JOIN bill_attach a4 ON o.order_no=a4.order_no AND a4.attach_code=4
        LEFT JOIN bill_attach a5 ON o.order_no=a5.order_no AND a5.attach_code=5
        LEFT JOIN bill_attach a6 ON o.order_no=a6.order_no AND a6.attach_code=99
        LEFT JOIN bill bi ON bi.order_apply_no=oa.order_apply_no
        left join order_cancel_info cancel on o.order_cancel_no = cancel.order_cancel_no
        left join order_info_attach oia on o.order_no = oia.order_no
        <where>
            oa.deleted=0
            <if test="orderApplyNo != null and orderApplyNo != ''">
               and oa.order_apply_no = #{orderApplyNo}
            </if>
            <if test="orderType != null ">
                and oa.order_type = #{orderType}
            </if>
            <if test="serviceCode != null and serviceCode !=''">
                and oa.service_code in
                <foreach item="item" collection="serviceCode.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="customerName != null and customerName != ''">
                and oa.customer_name = #{customerName}
            </if>
            <if test="companyId != null ">
                and oa.company_id = #{companyId}
            </if>
            <if test="bookingPassengerUserName != null and bookingPassengerUserName != ''">
                and oa.booking_passenger_user_name = #{bookingPassengerUserName}
            </if>
            <if test="customerCompanyId != null ">
                and oa.customer_company_id = #{customerCompanyId}
            </if>
            <if test="structId != null ">
                and oa.struct_id = #{structId}
            </if>
            <if test="passengerStructId != null ">
                and oa.passenger_struct_id = #{passengerStructId}
            </if>
            <if test="orderStatus != null and orderStatus!=''">
                and oa.order_status in
                <foreach item="item" collection="orderStatus.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="createTimeStart != null and createTimeStart != ''">
                and oa.create_time <![CDATA[>=]]> #{createTimeStart}
            </if>
            <if test="createTimeEnd != null and createTimeEnd != ''">
                and oa.create_time <![CDATA[<=]]> #{createTimeEnd}
            </if>
            <if test="bookingOrderStime != null and bookingOrderStime != ''">
                and oa.booking_order_stime <![CDATA[>=]]> #{bookingOrderStime}
            </if>
            <if test="bookingOrderEtime != null and bookingOrderEtime != ''">
                and oa.booking_order_stime <![CDATA[<=]]> #{bookingOrderEtime}
            </if>
            <if test="customerMobile != null and customerMobile != ''">
                and oa.customer_mobile = #{customerMobile}
            </if>
            <if test="bookingPassengerUserPhone != null and bookingPassengerUserPhone != ''">
                and oa.booking_passenger_user_phone = #{bookingPassengerUserPhone}
            </if>
            <!--<if test="loginCompanyId != null">-->
                <!--and oa.company_id = #{loginCompanyId}-->
            <!--</if>-->
            <if test="auditFlag != null">
                and oa.audit_flag = #{auditFlag}
            </if>
            <if test="onlySelf != null and onlySelf == true">
                and ((oa.order_type in (1, 2) and oa.customer_id = #{loginId}) or (oa.order_type = 3 and oa.booking_passenger_user_id = #{loginId}))
            </if>
            <if test="dataPermType !=null ">
                <!-- 负责合同  用户的数据权限的合同范围，匹配行程中车辆所属合同-->
                <if test="dataPermType == 1 ">
                    <!-- (商务用车列表 本人创建)-->
                    <choose>
                        <when test="orderType != null and orderType==2 ">
                            and oa.customer_id = #{loginId}
                        </when>
                        <otherwise>
                            <if test="dataPermCodeSet != null and dataPermCodeSet.size()>0 ">
                                and o.assign_car_license in
                                <foreach item="item" collection="dataPermCodeSet" open="(" separator="," close=")">
                                    #{item}
                                </foreach>
                            </if>
                        </otherwise>
                    </choose>
                </if>
                <!-- 负责客户/指定客户：用户的数据权限的客户名称，匹配行程的下单人企业 或者乘车人企业； -->
                <if test="dataPermType == 2 or  dataPermType == 3 ">
                    <if test="dataPermCodeSet != null and dataPermCodeSet.size()>0 ">
                        and (oa.company_id in
                        <foreach item="item" collection="dataPermCodeSet" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        or oa.customer_company_id in
                        <foreach item="item" collection="dataPermCodeSet" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        <if test=" loginId!=null">
                            or oa.customer_id = #{loginId}
                        </if>
                        )
                    </if>
                </if>
                <!-- 指定部门/所属部门：乘车人企业对应的CRM客户的所属部门 是登录用户的数据权限内的部门 -->
                <if test="dataPermType == 4 or dataPermType == 5 ">
                    <choose>
                        <when test="dataPermCodeSet != null and dataPermCodeSet.size()>0 ">
                        and (oa.customer_company_id in
                        <foreach item="item" collection="dataPermCodeSet" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        <if test=" loginId!=null">
                            or oa.customer_id = #{loginId}
                        </if>
                        )
                        </when>
                        <otherwise>
                            <if test=" loginId!=null">
                                and oa.customer_id = #{loginId}
                            </if>
                        </otherwise>
                    </choose>
                </if>
                <!-- 本人 下单人为本人-->
                <if test="dataPermType == 7">
                    <choose>
                        <when test="loginId!=null">
                            and oa.customer_id = #{loginId}
                        </when>
                        <otherwise>
                            and oa.id = -1
                        </otherwise>
                    </choose>
                </if>
            </if>
        </where>
        <choose>
            <when test="sort != null and sort != ''">
                order by ${sort}
            </when>
            <otherwise>
                order by create_time desc
            </otherwise>
        </choose>

    </select>


    <select id="existServiceOrder" resultMap="mapper.mrcar.OrderInfoMapper.BaseResultMap">
        select assign_car_license
        from order_info
        where assign_driver_id = #{driverId}
          and order_status in (25, 26, 30, 50)
        LIMIT 1
    </select>

    <select id="existServiceOrderByDriverIdOrCarId" resultMap="mapper.mrcar.OrderInfoMapper.BaseResultMap">
        select <include refid="mapper.mrcar.OrderInfoMapper.Base_Column_List"/>
        from order_info
        where (assign_driver_id = #{driverId} or assign_car_id = #{carId})
        and order_type in (1,2)
          and order_status in (25, 26, 30, 50)
        LIMIT 1
    </select>


    <select id="existServiceSelfOrderByDriverIdOrCarId" resultMap="mapper.mrcar.OrderInfoMapper.BaseResultMap">
        select <include refid="mapper.mrcar.OrderInfoMapper.Base_Column_List"/>
        from order_info
        where (assign_driver_id = #{driverId} or assign_car_id = #{carId})
        and order_type  = 1
        and service_code = 10
        and order_status in (25, 26, 30, 50)
        LIMIT 1
    </select>


    <select id="getOrderMileageSource" resultMap="mapper.mrcar.OrderInfoMapper.BaseResultMap">
        select mileage_source,
               fact_start_date,
               fact_end_date,
               order_no,
               assign_driver_id,
               assign_car_license,
               order_type,
               order_apply_no
        from order_info
        where order_no = #{orderNo}
    </select>

    <select id="checkUnDoOrderByParam" resultType="java.lang.Integer">
        select
        COUNT(1)
        from order_info
        <where>
            order_status <![CDATA[<]]> 40 and order_status != 0
            <if test="driverId != null">
                AND assign_driver_id = #{driverId}
            </if>
            <if test="vehicleId != null">
                AND assign_car_id = #{vehicleId}
            </if>
            <if test="bookingOrderStime != null and bookingOrderStime != ''">
                and booking_order_stime <![CDATA[>=]]> #{bookingOrderStime}
            </if>
            <if test="orderType!=null">
                and order_type = #{orderType}
            </if>
        </where>
    </select>

    <select id="selectOrderByDriverDay" resultMap="mapper.mrcar.OrderInfoMapper.BaseResultMap">
        select
        <include refid="mapper.mrcar.OrderInfoMapper.Base_Column_List"/>
        from order_info
        WHERE assign_driver_id=#{driverId} AND fact_end_date BETWEEN #{startTime} AND #{endTime}
    </select>
    <update id="updateByOrderNo">
        update order_info
        set order_status = #{orderStatus}
        where order_no = #{orderNo}
    </update>
    <!-- 查询子行程列表(客户端) -->
    <select id="queryOrderInfoList" resultMap="mapper.mrcar.OrderInfoMapper.BaseResultMap">
        select
        order_apply_no,
        order_no,
        customer_id,
        create_time,
        order_type,
        service_code,
        customer_name,
        booking_passenger_user_id,
        booking_passenger_user_name,
        booking_order_stime,
        assign_driver_name,
        assign_driver_phone,
        assign_car_id,
        assign_carlevel_name,
        assign_car_license,
        booking_start_short_addr,
        booking_end_short_addr,
        fact_start_short_addr,
        fact_end_short_addr,
        fact_start_date,
        fact_end_date,
        order_status,
        motorcade_id,
        motorcade_name,
        company_name,
        customer_company_id,
        customer_company_name,
        fixed_price_valid,
        fixed_price,
        himself,
        mileage_stat_type,
        company_id,
        struct_name,
        passenger_struct_name,
        vehicle_company_name,
        driver_company_name
        from order_info
        where deleted = 0 and ((order_status > 10 and order_type != 2) or order_type = 2)
        <if test="orderApplyNo != null and orderApplyNo != ''">
            and order_apply_no = #{orderApplyNo}
        </if>
        <if test="orderNo != null and orderNo != ''">
            and order_no = #{orderNo}
        </if>
        <if test="orderType != null ">
            and order_type = #{orderType}
        </if>
        <if test="orderTypeList != null and orderTypeList.size()>0 and port==1 and  internalSupplierFlag==0">
            and order_type in
            <foreach item="item" collection="orderTypeList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="serviceCode != null and serviceCode !=''">
            and service_code in
            <foreach item="item" collection="serviceCode.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="customerName != null and customerName != ''">
            and customer_name = #{customerName}
        </if>
        <if test="companyId != null ">
            and company_id = #{companyId}
        </if>
        <if test="bookingPassengerUserName != null and bookingPassengerUserName != ''">
            and booking_passenger_user_name = #{bookingPassengerUserName}
        </if>
        <if test="customerCompanyId != null ">
            and customer_company_id = #{customerCompanyId}
        </if>
        <if test="orderStatus != null and orderStatus!=''">
            and order_status in
            <foreach item="item" collection="orderStatus.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="assignDriverName != null and assignDriverName != ''">
            and assign_driver_name = #{assignDriverName}
        </if>
        <if test="assignDriverPhone != null and assignDriverPhone != ''">
            and assign_driver_phone LIKE CONCAT(#{assignDriverPhone}, '%')
        </if>
        <if test="assignCarLicense != null and assignCarLicense != ''">
            and assign_car_license = #{assignCarLicense}
        </if>
        <if test="bookingCarlevelId != null ">
            and assign_carlevel_id = #{bookingCarlevelId}
        </if>
        <if test="createTimeStart != null and createTimeStart != ''">
            and create_time <![CDATA[>=]]> #{createTimeStart}
        </if>
        <if test="createTimeEnd != null and createTimeEnd != ''">
            and create_time <![CDATA[<=]]> #{createTimeEnd}
        </if>
        <if test="fixedPriceValid != null ">
            and fixed_price_valid = #{fixedPriceValid}
        </if>
        <if test="bookingOrderStime != null and bookingOrderStime != ''">
            and booking_order_stime <![CDATA[>=]]> #{bookingOrderStime}
        </if>
        <if test="bookingOrderEtime != null and bookingOrderEtime != ''">
            and booking_order_stime <![CDATA[<=]]> #{bookingOrderEtime}
        </if>
        <if test="customerMobile != null and customerMobile != ''">
            and customer_mobile = #{customerMobile}
        </if>
        <if test="bookingPassengerUserPhone != null and bookingPassengerUserPhone != ''">
            and booking_passenger_user_phone = #{bookingPassengerUserPhone}
        </if>
        <if test="vehicleCompanyId != null ">
            and vehicle_company_id = #{vehicleCompanyId} and order_type =1
        </if>
        <if test="driverCompanyId != null ">
            and driver_company_id = #{driverCompanyId} and order_type =1
        </if>
        <if test="vehicleCompanyName != null and vehicleCompanyName!=''">
            and vehicle_company_name like CONCAT('%',#{vehicleCompanyName},'%') and order_type =1
        </if>
        <if test="driverCompanyName != null and driverCompanyName!=''">
            and driver_company_name like CONCAT('%',#{driverCompanyName},'%') and order_type =1
        </if>
        <if test="permissions != null and permissions != ''">
            and (start_city_code in
            <foreach item="cityCode" collection="permissions.split(',')" open="(" separator="," close=")">
                #{cityCode}
            </foreach>
            or end_city_code IN
            <foreach item="cityCode" collection="permissions.split(',')" open="(" separator="," close=")">
                #{cityCode}
            </foreach>
            )
        </if>

        <if test="motorcadeId != null">
            and motorcade_id = #{motorcadeId}
        </if>
        <if test="structId !=null">
            and (struct_id = #{structId}
            or passenger_struct_id = #{structId})
        </if>
        <if test="structIdList != null and structIdList.size() > 0">
            and (struct_id in
            <foreach collection="structIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
            or passenger_struct_id in
            <foreach collection="structIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
            )
        </if>
        <if test="companyIds != null and companyIds != ''">
            and customer_company_id in
            <foreach item="item" collection="companyIds.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="internalSupplierFlag ==0">
            <include refid="clientDataPerm"></include>
        </if>
        <if test="internalSupplierFlag ==1">
            and vehicle_company_id = #{loginCompanyId} and order_type =1 and driver_company_id = #{loginCompanyId}
        </if>
        order by create_time desc
    </select>
    <sql id="clientDataPerm">
        <!--本企业 -->
        <if test="dataPermType!=null and dataPermType ==1">
            and customer_company_id in
            <foreach item="customerCompanyIdItem" collection="dataPermCodeSet" open="(" separator="," close=")">
                #{customerCompanyIdItem}
            </foreach>
        </if>
        <!--指定部门 -->
        <if test="dataPermType!=null and dataPermType ==2">
            and (struct_id in
            <foreach item="item" collection="dataPermCodeSet" open="(" separator="," close=")">
                #{item}
            </foreach>
            or passenger_struct_id IN
            <foreach item="item" collection="dataPermCodeSet" open="(" separator="," close=")">
                #{item}
            </foreach>
            or customer_id = #{loginId}
            )
            and customer_company_id = #{loginCompanyId}
        </if>
        <!--指定城市 -->
        <if test="dataPermType!=null and dataPermType ==3">
            and (start_city_code in
            <foreach item="cityCode" collection="dataPermCodeSet" open="(" separator="," close=")">
                #{cityCode}
            </foreach>
            or end_city_code IN
            <foreach item="cityCode" collection="dataPermCodeSet" open="(" separator="," close=")">
                #{cityCode}
            </foreach>
            or customer_id = #{loginId}
            )
            and customer_company_id = #{loginCompanyId}
        </if>
        <!--本人权限 -->
        <if test="dataPermType!=null and dataPermType ==4">
            and (customer_id = #{loginId} or booking_passenger_user_id = #{loginId})
            and customer_company_id = #{loginCompanyId}
        </if>
    </sql>

    <!-- 查询子行程列表(运营端) -->
    <select id="queryOrderInfoProviderList" resultMap="mapper.mrcar.OrderInfoMapper.BaseResultMap">
        select
        <include refid="mapper.mrcar.OrderInfoMapper.Base_Column_List"/>
        from order_info
        where deleted = 0 and ((order_status > 10 and order_type != 2) or order_type = 2)
        <if test="orderApplyNo != null and orderApplyNo != ''">
            and order_apply_no = #{orderApplyNo}
        </if>
        <if test="orderNo != null and orderNo != ''">
            and order_no = #{orderNo}
        </if>
        <if test="orderType != null ">
            and order_type = #{orderType}
        </if>
        <if test="serviceCode != null and serviceCode !=''">
            and service_code in
            <foreach item="item" collection="serviceCode.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="customerName != null and customerName != ''">
            and customer_name = #{customerName}
        </if>
        <if test="companyId != null ">
            and company_id = #{companyId}
        </if>
        <if test="bookingPassengerUserName != null and bookingPassengerUserName != ''">
            and booking_passenger_user_name = #{bookingPassengerUserName}
        </if>
        <if test="customerCompanyId != null ">
            and customer_company_id = #{customerCompanyId}
        </if>
        <if test="orderStatus != null and orderStatus!=''">
            and order_status in
            <foreach item="item" collection="orderStatus.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="assignDriverName != null and assignDriverName != ''">
            and assign_driver_name = #{assignDriverName}
        </if>
        <if test="assignCarLicense != null and assignCarLicense != ''">
            and assign_car_license = #{assignCarLicense}
        </if>
        <if test="bookingCarlevelId != null ">
            and assign_carlevel_id = #{bookingCarlevelId}
        </if>
        <if test="createTimeStart != null and createTimeStart != ''">
            and create_time <![CDATA[>=]]> #{createTimeStart}
        </if>
        <if test="createTimeEnd != null and createTimeEnd != ''">
            and create_time <![CDATA[<=]]> #{createTimeEnd}
        </if>
        <if test="fixedPriceValid != null ">
            and fixed_price_valid = #{fixedPriceValid}
        </if>
        <if test="bookingOrderStime != null and bookingOrderStime != ''">
            and booking_order_stime <![CDATA[>=]]> #{bookingOrderStime}
        </if>
        <if test="bookingOrderEtime != null and bookingOrderEtime != ''">
            and booking_order_stime <![CDATA[<=]]> #{bookingOrderEtime}
        </if>
        <if test="customerMobile != null and customerMobile != ''">
            and customer_mobile = #{customerMobile}
        </if>
        <if test="bookingPassengerUserPhone != null and bookingPassengerUserPhone != ''">
            and booking_passenger_user_phone = #{bookingPassengerUserPhone}
        </if>
        <!--<if test="loginCompanyId != null">-->
            <!--and company_id = #{loginCompanyId}-->
        <!--</if>-->
        <if test="motorcadeId != null">
            and motorcade_id = #{motorcadeId}
        </if>
        <if test="vehicleCompanyId != null ">
            and vehicle_company_id = #{vehicleCompanyId} and order_type =1
        </if>
        <if test="driverCompanyId != null ">
            and driver_company_id = #{driverCompanyId} and order_type =1
        </if>
        <if test="vehicleCompanyName != null and vehicleCompanyName!=''">
            and vehicle_company_name like CONCAT('%',#{vehicleCompanyName},'%') and order_type =1
        </if>
        <if test="driverCompanyName != null and driverCompanyName!=''">
            and driver_company_name like CONCAT('%',#{driverCompanyName},'%') and order_type =1
        </if>
        <if test="dataPermType !=null ">
            <!-- 负责合同  用户的数据权限的合同范围，匹配行程中车辆所属合同-->
            <if test="dataPermType == 1 ">
                <if test="dataPermCodeSet != null and dataPermCodeSet.size()>0 ">
                    and assign_car_license in
                    <foreach item="item" collection="dataPermCodeSet" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            </if>
            <!-- 负责客户/指定客户：用户的数据权限的客户名称，匹配行程的下单人企业 或者乘车人企业； -->
            <if test="dataPermType == 2 or  dataPermType == 3 ">
                <if test="dataPermCodeSet != null and dataPermCodeSet.size()>0 ">
                    and (company_id in
                    <foreach item="item" collection="dataPermCodeSet" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    or customer_company_id in
                    <foreach item="item" collection="dataPermCodeSet" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    <if test="loginId!=null">
                    or customer_id = #{loginId}
                    </if>
                    )
                </if>
            </if>
            <!-- 指定部门/所属部门：乘车人企业对应的CRM客户的所属部门 是登录用户的数据权限内的部门 -->
            <if test="dataPermType == 4 or dataPermType == 5 ">
                <choose>
                    <when test="dataPermCodeSet != null and dataPermCodeSet.size()>0 ">
                        and (customer_company_id in
                        <foreach item="item" collection="dataPermCodeSet" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        <if test="loginId!=null">
                            or customer_id = #{loginId}
                        </if>
                        )
                    </when>
                    <otherwise>
                        <if test="loginId!=null">
                            and customer_id = #{loginId}
                        </if>
                    </otherwise>
                </choose>
            </if>
            <if test="dataPermType == 7">
                <choose>
                    <when test="loginId!=null">
                        and customer_id = #{loginId}
                    </when>
                    <otherwise>
                        and id = -1
                    </otherwise>
                </choose>
            </if>
        </if>
        order by create_time desc
    </select>
    <!-- 查询子行程列表(运营端) -->
    <select id="queryOrderInfoProviderListV2" resultMap="BusinessOrderInfoDTO">
        select
        oi.*,boar.vehicle_provider,boar.driver_provider,boar.expense_status,boar.supplier_provider_code
        from order_info oi
        left join order_business_extend boar on oi.order_no = boar.order_no
        where oi.deleted = 0 and ((oi.order_status > 10 and oi.order_type != 2) or oi.order_type = 2)
        <if test="orderApplyNo != null and orderApplyNo != ''">
            and oi.order_apply_no = #{orderApplyNo}
        </if>
        <if test="orderNo != null and orderNo != ''">
            and oi.order_no = #{orderNo}
        </if>
        <if test="orderType != null ">
            and oi.order_type = #{orderType}
        </if>
        <if test="serviceCode != null and serviceCode !=''">
            and oi.service_code in
            <foreach item="item" collection="serviceCode.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="customerName != null and customerName != ''">
            and oi.customer_name = #{customerName}
        </if>
        <if test="companyId != null ">
            and oi.company_id = #{companyId}
        </if>
        <if test="bookingPassengerUserName != null and bookingPassengerUserName != ''">
            and oi.booking_passenger_user_name = #{bookingPassengerUserName}
        </if>
        <if test="customerCompanyId != null ">
            and oi.customer_company_id = #{customerCompanyId}
        </if>
        <if test="orderStatus != null and orderStatus!=''">
            and oi.order_status in
            <foreach item="item" collection="orderStatus.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="assignDriverName != null and assignDriverName != ''">
            and oi.assign_driver_name = #{assignDriverName}
        </if>
        <if test="assignCarLicense != null and assignCarLicense != ''">
            and oi.assign_car_license = #{assignCarLicense}
        </if>
        <if test="bookingCarlevelId != null ">
            and oi.assign_carlevel_id = #{bookingCarlevelId}
        </if>
        <if test="createTimeStart != null and createTimeStart != ''">
            and oi.create_time <![CDATA[>=]]> #{createTimeStart}
        </if>
        <if test="createTimeEnd != null and createTimeEnd != ''">
            and oi.create_time <![CDATA[<=]]> #{createTimeEnd}
        </if>
        <if test="supplierCode != null and supplierCode != ''">
            and boar.supplier_provider_code = #{supplierCode}
        </if>
        <if test="fixedPriceValid != null ">
            and oi.fixed_price_valid = #{fixedPriceValid}
        </if>
        <if test="bookingOrderStime != null and bookingOrderStime != ''">
            and oi.booking_order_stime <![CDATA[>=]]> #{bookingOrderStime}
        </if>
        <if test="bookingOrderEtime != null and bookingOrderEtime != ''">
            and oi.booking_order_stime <![CDATA[<=]]> #{bookingOrderEtime}
        </if>
        <if test="customerMobile != null and customerMobile != ''">
            and oi.customer_mobile = #{customerMobile}
        </if>
        <if test="vehicleCompanyId != null ">
            and oi.vehicle_company_id = #{vehicleCompanyId} and oi.order_type =1
        </if>
        <if test="driverCompanyId != null ">
            and oi.driver_company_id = #{driverCompanyId} and oi.order_type =1
        </if>
        <if test="vehicleCompanyName != null and vehicleCompanyName!=''">
            and oi.vehicle_company_name like CONCAT('%',#{vehicleCompanyName},'%') and oi.order_type =1
        </if>
        <if test="driverCompanyName != null and driverCompanyName!=''">
            and oi.driver_company_name like CONCAT('%',#{driverCompanyName},'%') and oi.order_type =1
        </if>
        <if test="bookingPassengerUserPhone != null and bookingPassengerUserPhone != ''">
            and oi.booking_passenger_user_phone = #{bookingPassengerUserPhone}
        </if>
        <!--<if test="loginCompanyId != null">-->
            <!--and company_id = #{loginCompanyId}-->
        <!--</if>-->
        <if test="motorcadeId != null">
            and oi.motorcade_id = #{motorcadeId}
        </if>
        <if test="expenditureStatus != null">
            and boar.expense_status = #{expenditureStatus}
        </if>
        <if test="vehicleProvider != null">
            and boar.driver_provider = #{driverProvider} and boar.vehicle_provider = #{vehicleProvider}
        </if>
        <if test="dataPermType !=null ">
            <!-- 负责合同  用户的数据权限的合同范围，匹配行程中车辆所属合同-->
            <if test="dataPermType == 1 ">
                <if test="dataPermCodeSet != null and dataPermCodeSet.size()>0 ">
                    and oi.assign_car_license in
                    <foreach item="item" collection="dataPermCodeSet" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            </if>
            <!-- 负责客户/指定客户：用户的数据权限的客户名称，匹配行程的下单人企业 或者乘车人企业； -->
            <if test="dataPermType == 2 or  dataPermType == 3 ">
                <if test="dataPermCodeSet != null and dataPermCodeSet.size()>0 ">
                    and (oi.company_id in
                    <foreach item="item" collection="dataPermCodeSet" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    or oi.customer_company_id in
                    <foreach item="item" collection="dataPermCodeSet" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    <if test="loginId!=null">
                    or oi.customer_id = #{loginId}
                    </if>
                    )
                </if>
            </if>
            <!-- 指定部门/所属部门：乘车人企业对应的CRM客户的所属部门 是登录用户的数据权限内的部门 -->
            <if test="dataPermType == 4 or dataPermType == 5 ">
                <choose>
                    <when test="dataPermCodeSet != null and dataPermCodeSet.size()>0 ">
                        and (oi.customer_company_id in
                        <foreach item="item" collection="dataPermCodeSet" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        <if test="loginId!=null">
                            or oi.customer_id = #{loginId}
                        </if>
                        )
                    </when>
                    <otherwise>
                        <if test="loginId!=null">
                            and oi.customer_id = #{loginId}
                        </if>
                    </otherwise>
                </choose>
            </if>
            <if test="dataPermType == 7">
                <choose>
                    <when test="loginId!=null">
                        and oi.customer_id = #{loginId}
                    </when>
                    <otherwise>
                        and oi.id = -1
                    </otherwise>
                </choose>
            </if>
        </if>
        order by oi.create_time desc
    </select>

    <select id="queryOrderInfoListForScheduleCalendar" resultType="com.izu.mrcar.order.dto.mrcar.OrderInfoDTO">
        select
        o.order_no orderNo,
        o.customer_name customerName,
        o.booking_passenger_user_name bookingPassengerUserName,
        o.booking_order_stime bookingOrderStime,
        o.booking_order_etime bookingOrderEtime,
        o.assign_driver_name assignDriverName,
        o.assign_car_license assignCarLicense,
        o.booking_start_short_addr bookingStartShortAddr,
        o.booking_end_short_addr bookingEndShortAddr,
        t.vehicle_model as vehicleModel,
        t.vehicle_brand as vehicleBrand
        from order_info o left join order_vehicle_base t on o.assign_car_id = t.vehicle_id
        where o.order_status in(20,30)
        <if test="bookDate != null and bookDate !=''">
            and DATE(o.booking_order_stime) = #{bookDate}
        </if>
        <if test="customerName != null and customerName != ''">
            and o.customer_name = #{customerName}
        </if>
        <if test="bookingPassengerUserName != null and bookingPassengerUserName != ''">
            and o.booking_passenger_user_name = #{bookingPassengerUserName}
        </if>
        <if test="assignDriverName != null and assignDriverName != ''">
            and o.assign_driver_name = #{assignDriverName}
        </if>
        <if test="assignCarLicense != null and assignCarLicense != ''">
            and o.assign_car_license = #{assignCarLicense}
        </if>
        <if test="vehicleModelCode != null and vehicleModelCode != ''">
            and t.vehicle_model_code = #{vehicleModelCode}
        </if>
        <if test="vehicleBrandCode != null and vehicleBrandCode != ''">
            and t.vehicle_brand_code = #{vehicleBrandCode}
        </if>
        <if test="permissions != null and permissions != ''">
            and (o.start_city_code in
            <foreach item="cityCode" collection="permissions.split(',')" open="(" separator="," close=")">
                #{cityCode}
            </foreach>
            or o.end_city_code IN
            <foreach item="cityCode" collection="permissions.split(',')" open="(" separator="," close=")">
                #{cityCode}
            </foreach>
            or o.customer_id = #{loginId}
            )
        </if>
        <if test="loginCompanyId != null">
            and o.company_id = #{loginCompanyId}
        </if>
        order by o.create_time desc
    </select>

    <select id="getOrderInfoCountByDay" resultType="com.izu.mrcar.order.dto.mrcar.OrderInfoCountByDay">
        select
        DATE(booking_order_stime) as orderDate,
        count(1) as orderCount
        from order_info
        where order_status in(20,30)
        <if test="bookYear != null and bookYear !=''">
            and YEAR(booking_order_stime) = #{bookYear}
        </if>
        <if test="bookMonth != null and bookMonth !=''">
            and MONTH(booking_order_stime) = #{bookMonth}
        </if>
        <if test="permissions != null and permissions != ''">
            and (start_city_code in
            <foreach item="cityCode" collection="permissions.split(',')" open="(" separator="," close=")">
                #{cityCode}
            </foreach>
            or end_city_code IN
            <foreach item="cityCode" collection="permissions.split(',')" open="(" separator="," close=")">
                #{cityCode}
            </foreach>
            or customer_id = #{loginId}
            )
        </if>
        <if test="loginCompanyId != null">
            and company_id = #{loginCompanyId}
        </if>
        GROUP BY DATE(booking_order_stime)
    </select>

    <select id="queryProviderOrderInfoListForScheduleCalendar" resultType="com.izu.mrcar.order.dto.mrcar.OrderInfoDTO">
        select
        o.order_no orderNo,
        o.customer_name customerName,
        o.booking_passenger_user_name bookingPassengerUserName,
        o.booking_order_stime bookingOrderStime,
        o.booking_order_etime bookingOrderEtime,
        o.assign_driver_name assignDriverName,
        o.assign_car_license assignCarLicense,
        o.booking_start_short_addr bookingStartShortAddr,
        o.booking_end_short_addr bookingEndShortAddr,
        t.vehicle_model as vehicleModel,
        t.vehicle_brand as vehicleBrand
        from order_info o left join order_vehicle_base t on o.assign_car_id = t.vehicle_id
        where o.order_status in(20,30)
        <if test="bookDate != null and bookDate !=''">
            and DATE(o.booking_order_stime) = #{bookDate}
        </if>
        <if test="customerName != null and customerName != ''">
            and o.customer_name = #{customerName}
        </if>
        <if test="bookingPassengerUserName != null and bookingPassengerUserName != ''">
            and o.booking_passenger_user_name = #{bookingPassengerUserName}
        </if>
        <if test="assignDriverName != null and assignDriverName != ''">
            and o.assign_driver_name = #{assignDriverName}
        </if>
        <if test="assignCarLicense != null and assignCarLicense != ''">
            and o.assign_car_license = #{assignCarLicense}
        </if>
        <if test="vehicleModelCode != null and vehicleModelCode != ''">
            and t.vehicle_model_code = #{vehicleModelCode}
        </if>
        <if test="vehicleBrandCode != null and vehicleBrandCode != ''">
            and t.vehicle_brand_code = #{vehicleBrandCode}
        </if>
        <if test="dataPermType !=null ">
            <!-- 负责合同：空 -->
            <if test="dataPermType == 1 ">
                <if test="loginId!=null">
                  and o.customer_id = #{loginId}
                </if>
            </if>
            <!-- 负责客户/指定客户：用户的数据权限的客户名称，匹配行程的下单人企业 或者乘车人企业； -->
            <if test="dataPermType == 2 or  dataPermType == 3 ">
                <if test="dataPermCodeSet != null and dataPermCodeSet.size()>0 ">
                    and (o.company_id in
                    <foreach item="item" collection="dataPermCodeSet" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    or o.customer_company_id in
                    <foreach item="item" collection="dataPermCodeSet" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    <if test="loginId!=null">
                        or o.customer_id = #{loginId}
                    </if>
                    )
                </if>
            </if>
            <!-- 指定部门/所属部门：用户的数据权限的部门名称，匹配行程中车辆的部门（车辆信息中的运营组织机构名称）-->
            <if test="dataPermType == 4 or dataPermType == 5 ">
                <if test="dataPermCodeSet != null and dataPermCodeSet.size()>0 ">
                    and (o.customer_company_id in
                    <foreach item="item" collection="dataPermCodeSet" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    <if test="loginId!=null">
                        or o.customer_id = #{loginId}
                    </if>
                    )
                </if>
            </if>
            <if test="dataPermType == 7">
                <choose>
                    <when test="loginId!=null">
                        and o.customer_id = #{loginId}
                    </when>
                    <otherwise>
                        and o.id = -1
                    </otherwise>
                </choose>
            </if>
        </if>
        <!--<if test="loginCompanyId != null">-->
            <!--and o.company_id = #{loginCompanyId}-->
        <!--</if>-->
        order by o.create_time desc
    </select>

    <select id="getProviderOrderInfoCountByDay" resultType="com.izu.mrcar.order.dto.mrcar.OrderInfoCountByDay">
        select
        DATE(booking_order_stime) as orderDate,
        count(1) as orderCount
        from order_info
        where order_status in(20,30)
        <if test="bookYear != null and bookYear !=''">
            and YEAR(booking_order_stime) = #{bookYear}
        </if>
        <if test="bookMonth != null and bookMonth !=''">
            and MONTH(booking_order_stime) = #{bookMonth}
        </if>
        <if test="dataPermType !=null ">
            <!-- 负责合同：空 -->
            <if test="dataPermType == 1 ">
                <if test="loginId!=null">
                  and customer_id = #{loginId}
                </if>
            </if>
            <!-- 负责客户/指定客户：用户的数据权限的客户名称，匹配行程的下单人企业 或者乘车人企业； -->
            <if test="dataPermType == 2 or  dataPermType == 3 ">
                <if test="dataPermCodeSet != null and dataPermCodeSet.size()>0 ">
                    and (company_id in
                    <foreach item="item" collection="dataPermCodeSet" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    or customer_company_id in
                    <foreach item="item" collection="dataPermCodeSet" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    <if test="loginId!=null">
                        or customer_id = #{loginId}
                    </if>
                    )
                </if>
            </if>
            <!-- 指定部门/所属部门：用户的数据权限的部门名称，匹配行程中车辆的部门（车辆信息中的运营组织机构名称）-->
            <if test="dataPermType == 4 or dataPermType == 5 ">
                <if test="dataPermCodeSet != null and dataPermCodeSet.size()>0 ">
                    and (customer_company_id in
                    <foreach item="item" collection="dataPermCodeSet" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    <if test="loginId!=null">
                        or customer_id = #{loginId}
                    </if>
                    )
                </if>
            </if>
            <if test="dataPermType == 7">
                <choose>
                    <when test="loginId!=null">
                        and customer_id = #{loginId}
                    </when>
                    <otherwise>
                        and id = -1
                    </otherwise>
                </choose>
            </if>
        </if>
        <!--<if test="loginCompanyId != null">-->
            <!--and company_id = #{loginCompanyId}-->
        <!--</if>-->
        GROUP BY DATE(booking_order_stime)
    </select>

    <select id="selectFinishOrderByTime" resultMap="mapper.mrcar.OrderInfoMapper.BaseResultMap">
        select
        <include refid="mapper.mrcar.OrderInfoMapper.Base_Column_List"/>
        from order_info
        WHERE order_type=2 AND order_status BETWEEN 30 AND 90 AND create_time BETWEEN #{startTime} AND #{endTime}
    </select>

    <select id="queryViolationOrderInfo" resultMap="mapper.mrcar.OrderInfoMapper.BaseResultMap">
        select
            <include refid="mapper.mrcar.OrderInfoMapper.Base_Column_List"/>
        from order_info
        WHERE assign_car_license = #{vehicleLicense} and fact_start_date <![CDATA[<=]]> #{violationTime} and fact_end_date <![CDATA[>=]]> #{violationTime} limit 1
    </select>

    <select id="selectByOrderNos" resultMap="mapper.mrcar.OrderInfoMapper.BaseResultMap">
        select <include refid="mapper.mrcar.OrderInfoMapper.Base_Column_List"/>
            from order_info where order_type=3 and order_status= 20
        and order_no in
        <foreach collection="orderNos" open="(" separator="," close=")" item="orderNo">
         #{orderNo}
       </foreach>
    </select>

    <select id="selectByOrderApplyNos" resultMap="mapper.mrcar.OrderInfoMapper.BaseResultMap">
        select <include refid="mapper.mrcar.OrderInfoMapper.Base_Column_List"/>
        from order_info where deleted = 0 and order_type=3
        and order_apply_no in
        <foreach collection="orderApplyNos" open="(" separator="," close=")" item="orderApplyNo">
            #{orderApplyNo}
        </foreach>
    </select>

    <select id="listOrderNoByApplyNo" resultType="java.lang.String">
        select order_no from order_info where order_apply_no = #{orderApplyNo}
    </select>


    <select id="selectByApplyNo" resultMap="mapper.mrcar.OrderInfoMapper.BaseResultMap">
        select <include refid="mapper.mrcar.OrderInfoMapper.Base_Column_List"/>
        from order_info where order_apply_no = #{orderApplyNo}
    </select>

    <select id="getOrderType" resultType="java.lang.Byte">
        select order_type from order_info where order_no = #{orderNo} limit 1
    </select>

    <select id="getMileageStatType" resultType="java.lang.Byte">
        select mileage_stat_type from order_info where order_no = #{orderNo} limit 1
    </select>

    <select id="selectMinId" resultType="java.lang.Integer">
        select min(id) from order_info
    </select>

    <select id="selectMaxId" resultType="java.lang.Integer">
        select max(id) from order_info
    </select>

    <select id="selectOrderInfo" resultMap="mapper.mrcar.OrderInfoMapper.BaseResultMap">
        select <include refid="mapper.mrcar.OrderInfoMapper.Base_Column_List"/>
        from order_info where id >=#{minId} and id <![CDATA[<]]>#{maxId}
    </select>

    <select id="exportSonOrderList" resultMap="BaseResultMap">
        SELECT
        DISTINCT
        case when o.order_status > 10 then o.order_no else '' end order_no,
        oa.audit_flag,
        oa.order_apply_no,
        oa.create_time,
        o.channel_order_code,
        o.order_detail,
        o.order_type,
        o.service_code,
        o.customer_name,
        o.customer_mobile,
        o.booking_passenger_user_name,
        o.booking_passenger_user_phone,
        oa.contact_name,
        oa.contact_mobile,
        oa.business_name,
        o.booking_order_stime,
        o.booking_order_etime,
        o.fact_end_date,
        o.fact_start_date,
        oa.booking_start_city_name start_city_name,
        o.booking_start_short_addr,
        o.booking_end_short_addr,
        oa.booking_end_city_name,
        o.assign_carlevel_name,
        o.assign_carmodel_name,
        v.vehicle_brand,
        o.assign_car_license,
        o.assign_driver_name,
        o.assign_driver_phone,
        case when o.order_status > 10 then o.order_status else null end order_status,
        oa.order_status apply_status,
        b.total_mileage,
        b.total_time,
        b.total_time total_second,
        b.base_fee,
        b.total_mileage_fee,
        b.total_time_fee,
        b.attach_fee total_fee_amount ,
        b.other_amount other_amount_sub,
        b.reduction_amount reduction_amount_sub,
        a.fee_amount,
        a2.fee_amount fee_amount2,
        a3.fee_amount fee_amount3,
        a4.fee_amount fee_amount4,
        a5.fee_amount fee_amount5,
        bi.total_amount total_amount,
        bi.fixed_price,
        bi.price_explain,
        bi.other_amount,
        bi.reduction_amount,
        bi.coupon_deduct_amount,
        bi.shouldpay_amount all_total_amount,
        oa.pay_type,
        o.company_name,
        '' as activity_name,
        o.customer_company_name,
        oa.settle_company_name,
        oa.struct_name struct_name,
        oa.passenger_struct_name passenger_struct_name,
        cancel.cancel_user_name,
        o.assign_car_id
        FROM
        order_apply oa
        LEFT JOIN order_info o ON o.order_apply_no = oa.order_apply_no
        LEFT JOIN order_vehicle_base v ON o.assign_car_id = v.vehicle_id
        LEFT JOIN bill_order b ON o.order_no = b.order_no
        LEFT JOIN bill_attach a ON o.order_no=a.order_no AND a.attach_code=2
        LEFT JOIN bill_attach a2 ON o.order_no=a2.order_no AND a2.attach_code=1
        LEFT JOIN bill_attach a3 ON o.order_no=a3.order_no AND a3.attach_code=3
        LEFT JOIN bill_attach a4 ON o.order_no=a4.order_no AND a4.attach_code=4
        LEFT JOIN bill_attach a5 ON o.order_no=a5.order_no AND a5.attach_code=5
        LEFT JOIN bill bi ON bi.order_apply_no=oa.order_apply_no
        left join order_cancel_info cancel on o.order_cancel_no = cancel.order_cancel_no
        <where>
              o.order_status > 10
            <if test="orderApplyNo != null and orderApplyNo != ''">
              and oa.order_apply_no = #{orderApplyNo}
            </if>
            <if test="orderNo != null and orderNo != ''">
                and o.order_no = #{orderNo}
            </if>
            <if test="orderType != null ">
                and oa.order_type = #{orderType}
            </if>
            <if test="serviceCode != null and serviceCode !=''">
                and oa.service_code in
                <foreach item="item" collection="serviceCode.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="customerName != null and customerName != ''">
                and oa.customer_name = #{customerName}
            </if>
            <if test="companyId != null ">
                and oa.company_id = #{companyId}
            </if>
            <if test="bookingPassengerUserName != null and bookingPassengerUserName != ''">
                and oa.booking_passenger_user_name = #{bookingPassengerUserName}
            </if>
            <if test="customerCompanyId != null ">
                and oa.customer_company_id = #{customerCompanyId}
            </if>
            <if test="orderStatus != null and orderStatus!=''">
                and o.order_status in
                <foreach item="item" collection="orderStatus.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="createTimeStart != null and createTimeStart != ''">
                and oa.create_time <![CDATA[>=]]> #{createTimeStart}
            </if>
            <if test="createTimeEnd != null and createTimeEnd != ''">
                and oa.create_time <![CDATA[<=]]> #{createTimeEnd}
            </if>
            <if test="bookingOrderStime != null and bookingOrderStime != ''">
                and oa.booking_order_stime <![CDATA[>=]]> #{bookingOrderStime}
            </if>
            <if test="bookingOrderEtime != null and bookingOrderEtime != ''">
                and oa.booking_order_stime <![CDATA[<=]]> #{bookingOrderEtime}
            </if>
            <if test="customerMobile != null and customerMobile != ''">
                and oa.customer_mobile = #{customerMobile}
            </if>
            <if test="bookingPassengerUserPhone != null and bookingPassengerUserPhone != ''">
                and oa.booking_passenger_user_phone = #{bookingPassengerUserPhone}
            </if>
<!--            <if test="permissions != null and permissions != ''">
                and (o.start_city_code in
                <foreach item="cityCode" collection="permissions.split(',')" open="(" separator="," close=")">
                    #{cityCode}
                </foreach>
                or o.end_city_code IN
                <foreach item="cityCode" collection="permissions.split(',')" open="(" separator="," close=")">
                    #{cityCode}
                </foreach>
                or o.customer_id = #{loginId}
                )
            </if>-->
            <if test="permissions != null and permissions != ''">
                <!--指定部门 -->
                <if test="dataPermType!=null and dataPermType ==2">
                    and (oa.struct_id in
                    <foreach item="item" collection="permissions.split(',')" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    or oa.passenger_struct_id IN
                    <foreach item="item" collection="permissions.split(',')" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    or oa.customer_id = #{loginId}
                    )
                </if>
                <!--指定城市 -->
                <if test="dataPermType!=null and dataPermType ==3">
                    and (o.start_city_code in
                    <foreach item="cityCode" collection="permissions.split(',')" open="(" separator="," close=")">
                        #{cityCode}
                    </foreach>
                    or o.end_city_code IN
                    <foreach item="cityCode" collection="permissions.split(',')" open="(" separator="," close=")">
                        #{cityCode}
                    </foreach>
                    or oa.customer_id = #{loginId}
                    )
                </if>
            </if>
            <!--本人权限 -->
            <if test="dataPermType!=null and dataPermType ==4">
                and (oa.customer_id = #{loginId})
            </if>
<!--            <if test="loginCompanyId != null">
                and oa.company_id = #{loginCompanyId}
            </if>-->
            <if test="companyIds != null and companyIds != ''">
                and oa.company_id in
                <foreach item="item" collection="companyIds.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="assignDriverName != null and assignDriverName != ''">
                and o.assign_driver_name = #{assignDriverName}
            </if>
            <if test="bookingCarlevelId != null ">
                and o.assign_carlevel_id = #{bookingCarlevelId}
            </if>
            <if test="assignCarLicense != null and assignCarLicense != ''">
                and o.assign_car_license = #{assignCarLicense}
            </if>
            <if test="fixedPriceValid != null ">
                and o.fixed_price_valid = #{fixedPriceValid}
            </if>
            <if test="motorcadeId != null">
                and o.motorcade_id = #{motorcadeId}
            </if>
            /*and oa.order_status != 5*/
        </where>
        order by create_time desc

    </select>


    <select id="listExpiredPrivateOrder" resultType="java.lang.String">
        select order_no from order_info where order_type = 3 and order_status=20 and booking_order_etime &lt; now()
    </select>

    <select id="getOrderInService" resultType="com.izu.mrcar.order.dto.order.OrderInServiceDTO">
        select order_apply_no     as orderApplyNo,
               order_no           as orderNo,
               order_type         as orderType,
               service_code       as serviceCode,
               assign_car_license as assignCarLicense,
               assign_driver_id   as assignDriverId
        from order_info
        where assign_driver_phone = #{assignDriverPhone}
          and order_status = 30
          and service_code != 10
    </select>

    <select id="existInTravelsOrder" resultType="java.lang.Integer">
        select count(*)
        from order_info
        where assign_driver_phone = #{assignDriverPhone}
          and order_status in (25, 26, 30)
          and service_code != 10
    </select>

    <select id="existPrivateCarOrderInTravels" resultType="java.lang.Integer">
        select count(*)
        from order_info
        where assign_driver_phone = #{assignDriverPhone}
        and order_type = 3
        and order_status in (25, 26, 30)
    </select>


    <!-- 查询商务用车相关订单 -->
    <select id="listOrderByDriverTime" resultMap="mapper.mrcar.OrderInfoMapper.BaseResultMap">
        select
        <include refid="mapper.mrcar.OrderInfoMapper.Base_Column_List"/>
        from order_info
        WHERE
        order_type = 2 AND order_status > 10 and order_status != 100
        and driver_start_time  <![CDATA[ >= ]]> #{statisSTime,jdbcType=TIMESTAMP} AND driver_start_time <![CDATA[ < ]]> #{statisETime,jdbcType=TIMESTAMP}
    </select>

    <select id="selectEffectiveOrderNosByVehicle" resultType="com.izu.mrcar.order.dto.other.MotorcadeOrderInfoDTO">
        select o.order_no orderNo,v.vehicle_serial_no_new_system vehicleSerialNo, v.vehicle_vin vehicleVin
        from order_info o left join order_vehicle_base v on o.assign_car_id = v.vehicle_id
        where order_type =2 and order_status != 100
        <if test="vehicleSerialNo != null and vehicleSerialNo != ''">
            and v.vehicle_serial_no_new_system in
            <foreach item="item" collection="vehicleSerialNo.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="vehicleVin != null and vehicleVin != ''">
            and v.vehicle_vin in
            <foreach item="item" collection="vehicleVin.split(',')" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectWorkOrderNoByVehicleId" resultType="java.lang.String">
        select order_no
        from order_info
        WHERE assign_car_id = #{assignCarId}
        and (
        (
        (order_type = 3 or (order_type = 1 and service_code = '10')) and order_status!=100 and
        ((#{serviceDate} BETWEEN fact_start_date and fact_end_date) or
        (fact_end_date = '1970-01-01 00:00:00' and fact_start_date!='1970-01-01 00:00:00' and fact_start_date &lt;= #{serviceDate}))
        )
        or
        (
        #{serviceDate} BETWEEN driver_start_time and fact_end_date or
        (fact_end_date = '1970-01-01 00:00:00' and driver_start_time != '1970-01-01 00:00:00' and
        driver_start_time &lt;= #{serviceDate})
        )
        )
        limit 1
    </select>
    <select id="selectWorkOrderApplyNoByVehicleId" resultType="java.lang.String">
        select order_apply_no
        from order_info
        WHERE assign_car_id = #{assignCarId}
        <if test="serviceDate != null">
            and (#{serviceDate} BETWEEN fact_start_date and fact_end_date
            or (fact_end_date = '1970-01-01 00:00:00' and fact_start_date != '1970-01-01 00:00:00' and fact_start_date <![CDATA[ <= ]]> #{serviceDate}))
        </if>
        limit 1
    </select>
    <select id="selectOrderApplyNoByVehicleId" resultType="java.lang.String">
        select order_apply_no
        from order_info
        WHERE assign_car_id = #{assignCarId}
        and order_status between 10 and 50
        order by create_time desc
        limit 1
    </select>

    <select id="listOrderInfoGroupByVehicle" resultType="com.izu.mrcar.order.dto.iotOrder.OrderInfoStatResDTO">
        select o.assign_car_license vehicleLicense,v.vehicle_vin vehicleVin,
        v.belong_city_name cityName,count(o.order_no) orderCount,
        o.order_type orderType,sum( CASE WHEN b.fixed_price_valid = 1 THEN b.fixed_price ELSE b.shouldpay_amount END  ) totalAmount,
        sum(b.total_mileage) totalOrderMileage,sum(b.total_time) totalTime
        from order_info o LEFT JOIN bill_order b on o.order_no = b.order_no
        LEFT JOIN order_vehicle_base v on o.assign_car_id = v.vehicle_id
        where o.order_status = 90
        <if test="companyId != null ">
            <if test="companyId == 1 ">
                and o.order_type = 2
            </if>
            <if test="companyId != 1 ">
                and o.company_id = #{companyId}
            </if>
        </if>
        <if test="vehicleLicense != null and vehicleLicense != '' ">
            and o.assign_car_license = #{vehicleLicense}
        </if>
        <if test="cityCode != null and cityCode != '' ">
            and v.belong_city_code = #{cityCode}
        </if>
        <if test="orderType != null ">
            and o.order_type = #{orderType}
        </if>
        <if test="vehicleVinList != null and vehicleVinList.size() > 0">
            and v.vehicle_vin in
            <foreach collection="vehicleVinList" item="vehicleVin" index="index" separator="," open="(" close=")">
                #{vehicleVin}
            </foreach>
        </if>
        <if test="bookingOrderStime != null and bookingOrderStime != ''">
            and o.booking_order_stime <![CDATA[>=]]> #{bookingOrderStime}
        </if>
        <if test="bookingOrderEtime != null and bookingOrderEtime != ''">
            and o.booking_order_stime <![CDATA[<=]]> #{bookingOrderEtime}
        </if>
        GROUP BY o.assign_car_license
    </select>

    <select id="getOrderCountByTime" resultType="java.lang.Integer" parameterType="com.izu.mrcar.order.dto.order.OrderDataPanelQueryDTO">
        select count(*)
        from order_info
        WHERE deleted = 0 and ((order_status > 10 and order_type != 2) or order_type = 2)
        <if test="startDate !=null and startDate !=''">
            and booking_order_stime  <![CDATA[ >= ]]> #{startDate,jdbcType=TIMESTAMP}
        </if>

        <if test="endDate !=null and endDate !=''">
            AND booking_order_stime <![CDATA[ <= ]]> #{endDate,jdbcType=TIMESTAMP}
        </if>

        <if test="companyId != null">
            and customer_company_id = #{companyId}
        </if>
        <if test="deptId !=null">
            and (struct_id = #{deptId} or passenger_struct_id=#{deptId} )
        </if>
        <if test="structIdList != null and structIdList.size() > 0">
            and (struct_id in
            <foreach collection="structIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
            or passenger_struct_id in
            <foreach collection="structIdList" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
                )
        </if>
        <include refid="clientDataPerm"></include>
    </select>

    <select id="selectListByOrderApplyNos" resultMap="mapper.mrcar.OrderInfoMapper.BaseResultMap">
        select <include refid="mapper.mrcar.OrderInfoMapper.Base_Column_List"/>
        from order_info where order_apply_no in
        <foreach collection="orderApplyNos" open="(" separator="," close=")" item="orderApplyNo">
            #{orderApplyNo}
        </foreach>
    </select>

<!--    获取子行程列表导出 order_apply表中的扩展字段-->
    <select id="getOrderInfoExportApplyExtend" resultMap="mapper.mrcar.OrderApplyMapper.BaseResultMap">
        select
        oa.order_apply_no,
        oa.audit_flag,
        oa.order_apply_no,
        oa.create_time,
        oa.contact_name,
        oa.contact_mobile,
        oa.business_name,
        oa.booking_start_city_name,
        oa.booking_end_city_name,
        oa.pay_type,
        oa.settle_company_name,
        oa.struct_name struct_name,
        oa.passenger_struct_name ,
        oa.order_status apply_status,
        oa.order_status,
        oa.order_channel_source,
        oa.settle_tax
        from order_apply as oa
        where oa.order_apply_no in
        <foreach collection="orderApplyNos" item="orderApplyNo" separator="," open="(" close=")">
            #{orderApplyNo}
        </foreach>
    </select>

    <select id="getOrderInfoExportBillOrderExtend" resultMap="mapper.mrcar.BillOrderMapper.BaseResultMap">
        select
        b.order_no,
        b.total_mileage,
        b.total_time,
        b.total_time ,
        b.base_fee,
        b.total_mileage_fee,
        b.total_time_fee,
        b.attach_fee ,
        b.other_amount ,
        b.reduction_amount,
        b.refund_amount_synthesis,
        b.refund_status,
        b.return_empty_fee,
        b.deduction_amount,
        b.fixed_price,
        b.shouldpay_amount,
        b.over_mileage_fee,
        b.over_time_fee,
        b.supplier_payable_amount
        from bill_order as b
        where  b.order_no in
        <foreach collection="orderNos" item="orderNo" close=")" open="(" separator=",">
            #{orderNo}
        </foreach>
    </select>

    <select id="getOrderInfoExportBillExtend" resultMap="mapper.mrcar.BillMapper.BaseResultMap">
        select
        bi.order_apply_no,
        bi.total_amount ,
        bi.fixed_price,
        bi.price_explain,
        bi.other_amount,
        bi.reduction_amount,
        bi.coupon_deduct_amount,
        bi.shouldpay_amount
        from bill as bi
        where bi.order_apply_no in
        <foreach collection="orderApplyNos" item="orderApplyNo" separator="," open="(" close=")">
            #{orderApplyNo}
        </foreach>
    </select>
    <select id="getOrderInfoExportOrderCancelInfoExtend" resultMap="mapper.mrcar.OrderCancelInfoMapper.BaseResultMap">
        select
        cancel_user_name,order_cancel_no
        from order_cancel_info
        where order_cancel_no in
        <foreach collection="orderCancelInfoNoList" item="orderCancelNo" separator="," open="(" close=")">
            #{orderCancelNo}
        </foreach>
    </select>

    <select id="getOrderInfoExportBillAttachExtend" resultMap="mapper.mrcar.BillAttachMapper.BaseResultMap">
        select fee_amount, order_no, attach_code, remarks
        from bill_attach
        where order_no in
        <foreach collection="orderNos" item="orderNo" close=")" open="(" separator=",">
            #{orderNo}
        </foreach>
    </select>


    <select id="getOrderInfoExportCancelExtend" resultMap="mapper.mrcar.OrderCancelInfoMapper.BaseResultMap">
        select cancel_user_name,order_cancel_no,create_time from order_cancel_info
        where order_cancel_no in
        <foreach collection="orderCancelNos" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getOrderInfoExportVehicleExtend" resultMap="mapper.mrcar.VehicleBaseMapper.BaseResultMap">
        select vehicle_brand, vehicle_model, vehicle_id
        from order_vehicle_base
        where vehicle_id in
        <foreach collection="vehicleIds" separator="," open="(" close=")" item="id">
            #{id}
        </foreach>
    </select>

    <select id="getOrderInfoExportAttachExtend" resultMap="mapper.mrcar.OrderInfoAttachMapper.BaseResultMap">
        select start_manual_mileage, end_manual_mileage, order_no
        from order_info_attach
        where order_no in
        <foreach collection="orderNos" item="orderNo" close=")" open="(" separator=",">
            #{orderNo}
        </foreach>
    </select>
    <select id="getBusinessSupExportAttachExtend" resultType="com.izu.order.entity.mrcar.BusinessSupplierExpenditureDetailByExport">
        select  order_no as orderNo,bs.vehicle_user_name as vehicleOperateBussName,bsed.should_pay_amount as realShouldPayAmount,
        bsed.customer_bill_tax_rate as customerSettleTax,bsed.supplier_amount as expenseAmount,bs.suppler_invoice_type as supplerInvoiceTypeName,
        bs.supplier_rate as supplerSettleTax,bsed.return_rate as returnRate,bs.expenditure_status as expenseStatusName,bsed.expenditure_no as expenditureNo,
        bsed.supplier_rate as  supplerSettleDetailTax
        from business_supplier_expenditure_detail as bsed
        left join business_supplier_expenditure as bs
        on bs.expenditure_no = bsed.expenditure_no
        where bs.expenditure_status !=0 and bsed.order_no in
        <foreach collection="orderNos" item="orderNo" close=")" open="(" separator=",">
            #{orderNo}
        </foreach>
    </select>
    <select id="getOrderBusinessExtend" resultMap="mapper.mrcar.OrderBusinessExtendMapper.BaseResultMap">
        select
        <include refid="mapper.mrcar.OrderBusinessExtendMapper.Base_Column_List"/>
        from order_business_extend
        where order_no in
        <foreach collection="orderNos" item="orderNo" close=")" open="(" separator=",">
            #{orderNo}
        </foreach>
    </select>
    <select id="getOrderApplyDestinationExtend" resultMap="mapper.mrcar.OrderApplyDestinationMapper.BaseResultMap">
        select
        <include refid="mapper.mrcar.OrderApplyDestinationMapper.Base_Column_List"/>
        from order_apply_destination
        where order_no in
        <foreach collection="orderNos" item="orderNo" close=")" open="(" separator=",">
            #{orderNo}
        </foreach>
        and address_type in (2,3) and trip_type = 2
        order by address_type
    </select>
    <select id="getOrderInfoAttachExtend" resultMap="mapper.mrcar.OrderInfoAttachMapper.BaseResultMap">
        select
        start_manual_mileage,end_manual_mileage,order_no
        from order_info_attach
        where order_no in
        <foreach collection="orderNos" item="orderNo" close=")" open="(" separator=",">
            #{orderNo}
        </foreach>
    </select>
    <select id="getOrderApplyPassengerExtend" resultMap="mapper.mrcar.OrderApplyPassengerMapper.BaseResultMap">
        select
        <include refid="mapper.mrcar.OrderApplyPassengerMapper.Base_Column_List"/>
        from order_apply_passenger
        where order_apply_no in
        <foreach collection="orderApplyNos" item="orderApplyNo" close=")" open="(" separator=",">
            #{orderApplyNo}
        </foreach>
    </select>

    <select id="getOrderInfoByVehicle" resultMap="mapper.mrcar.OrderInfoMapper.BaseResultMap">
        select <include refid="mapper.mrcar.OrderInfoMapper.Base_Column_List"/>
        from order_info where order_status != 100
        <if test="vehicleId!=null ">
            and assign_car_id = #{vehicleId}
        </if>
        <if test="vehicleLicense!=null and vehicleLicense!='' ">
            and assign_car_license = #{vehicleLicense}
        </if>
        and driver_start_time <![CDATA[ > ]]> '1970-01-01 00:00:00' and driver_start_time <![CDATA[ <= ]]> #{nowDate}
        and (fact_end_date <![CDATA[ >= ]]> #{nowDate} or fact_end_date = '1970-01-01 00:00:00')
        order by id desc
    </select>

    <select id="getOrderInfoByVehicleIds" resultMap="mapper.mrcar.OrderInfoMapper.BaseResultMap">
        select
        <include refid="mapper.mrcar.OrderInfoMapper.Base_Column_List"/>
        from order_info
        <where>
            order_status != 100
            <if test="vehicleIds != null and vehicleIds !=''">
                and assign_car_id in
                <foreach item="item" collection="vehicleIds.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            and driver_start_time <![CDATA[ > ]]> '1970-01-01 00:00:00' and driver_start_time <![CDATA[ <= ]]>
            #{nowDate}
            and (fact_end_date <![CDATA[ >= ]]> #{nowDate} or fact_end_date = '1970-01-01 00:00:00')
        </where>
        order by id desc
    </select>

    <select id="getOrderInfoByDriverName" resultMap="mapper.mrcar.OrderInfoMapper.BaseResultMap">
        select <include refid="mapper.mrcar.OrderInfoMapper.Base_Column_List"/>
        from order_info
        where order_status != 100 and company_id = #{companyId}
        <if test="driverName!=null and driverName!='' ">
            and assign_driver_name like CONCAT('%', #{driverName},'%')
        </if>
        and driver_start_time <![CDATA[ > ]]> '1970-01-01 00:00:00' and driver_start_time <![CDATA[ <= ]]> #{nowDate}
        and (fact_end_date <![CDATA[ >= ]]> #{nowDate} or fact_end_date = '1970-01-01 00:00:00')
        order by id desc
    </select>
    <select id="queryByOrderNos" resultMap="mapper.mrcar.OrderInfoMapper.BaseResultMap">
        select <include refid="mapper.mrcar.OrderInfoMapper.Base_Column_List"/>
        from order_info where order_no in
        <foreach collection="orderNos" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
    </select>

    <update id="updateByOrderApplyNo" parameterType="com.izu.order.entity.mrcar.OrderInfo">
        update order_info
        <set>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>

            <if test="orderType != null">
                order_type = #{orderType,jdbcType=TINYINT},
            </if>
            <if test="serviceCode != null">
                service_code = #{serviceCode,jdbcType=VARCHAR},
            </if>
            <if test="bookingCarlevelId != null">
                booking_carlevel_id = #{bookingCarlevelId,jdbcType=TINYINT},
            </if>
            <if test="bookingCarlevelName != null">
                booking_carlevel_name = #{bookingCarlevelName,jdbcType=VARCHAR},
            </if>
            <if test="assignCarlevelId != null">
                assign_carlevel_id = #{assignCarlevelId,jdbcType=TINYINT},
            </if>
            <if test="assignCarlevelName != null">
                assign_carlevel_name = #{assignCarlevelName,jdbcType=VARCHAR},
            </if>
            <if test="assignCarId != null">
                assign_car_id = #{assignCarId,jdbcType=BIGINT},
            </if>
            <if test="assignCarLicense != null">
                assign_car_license = #{assignCarLicense,jdbcType=VARCHAR},
            </if>
            <if test="assignDriverId != null">
                assign_driver_id = #{assignDriverId,jdbcType=BIGINT},
            </if>
            <if test="assignDriverName != null">
                assign_driver_name = #{assignDriverName,jdbcType=VARCHAR},
            </if>
            <if test="assignDriverPhone != null">
                assign_driver_phone = #{assignDriverPhone,jdbcType=VARCHAR},
            </if>
            <if test="assignCarmodelId != null">
                assign_carmodel_id = #{assignCarmodelId,jdbcType=INTEGER},
            </if>
            <if test="assignCarmodelName != null">
                assign_carmodel_name = #{assignCarmodelName,jdbcType=VARCHAR},
            </if>
            <if test="orderStatus != null">
                order_status = #{orderStatus,jdbcType=SMALLINT},
            </if>
            <if test="motorcadeId != null">
                motorcade_id = #{motorcadeId,jdbcType=INTEGER},
            </if>
            <if test="motorcadeName != null">
                motorcade_name = #{motorcadeName,jdbcType=VARCHAR},
            </if>
        </set>
        where order_apply_no = #{orderApplyNo}
    </update>

    <select id="queryTemporalSharedVehicleRecordOrders" resultMap="mapper.mrcar.OrderInfoMapper.BaseResultMap" >
        select
         <include refid="mapper.mrcar.OrderInfoMapper.Base_Column_List"/>
         from order_info
        <where>
            customer_company_id = #{vehicleCompanyId} and order_status > 0
            <if test="vehicleLicense != null and vehicleLicense != '' ">
                and assign_car_license = #{vehicleLicense}
            </if>
            <if test="orderNo != null and orderNo != '' ">
                and order_no = #{orderNo}
            </if>
            <if test="orderStatus != null and orderStatus.size() > 0">
                and order_status in
                <foreach collection="orderStatus" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="bookingOrderStimeBegin != null">
                and booking_order_stime <![CDATA[>=]]> #{bookingOrderStimeBegin}
            </if>
            <if test="bookingOrderStimeEnd != null">
                and booking_order_stime <![CDATA[<=]]> #{bookingOrderStimeEnd}
            </if>
            <if test="orderCustomerName != null and orderCustomerName != '' ">
                and customer_name = #{orderCustomerName}
            </if>
            <if test="orderCustomerStructId != null">
                and struct_id = #{orderCustomerStructId}
            </if>
        </where>

    </select>
    <select id="selectOrderInfoByCompanyId"
            resultType="com.izu.mrcar.order.dto.provider.output.SubTripDetailForAddOutputDTO">
        select oi.order_no as orderNo,oi.customer_company_name as companyName,oi.customer_company_id as companyId,oi.customer_company_code as companyCode,
               oi.service_code as serviceCode,oi.assign_car_license as numberPlate,oi.assign_driver_id as driverId,oi.assign_driver_name as driverName,
               oi.assign_driver_phone as driverPhone,oi.start_city_name as startCity,oi.booking_order_stime as useVehicleTime,
               IFNULL(bo.supplier_payable_amount,0) as supplierAmount,IFNULL(bo.shouldpay_amount,0) as shouldPayAmount,IFNULL(bo.base_fee,0) as basicFee,
               IFNULL(bo.over_time_fee,0) as overtimeFee,IFNULL(bo.over_mileage_fee,0) as excessMileageFee,IFNULL(bo.reduction_amount,0) as deductionsTotalFee,
               IFNULL(bo.other_amount,0) as otherFee,oi.booking_passenger_user_name as passenger,oi.booking_passenger_user_phone as passengerMobile,
               oi.booking_passenger_user_id as bookingPassengerUserId,oi.customer_id as customerId,
               oi.customer_name as createUser,oi.customer_mobile as createUserMobile,oi.booking_start_short_addr as startAddress,
               obe.vehicle_provider as vehicleProvider,obe.driver_provider as driverProvider,IFNULL(oa.settle_tax,0) as customerBillTaxRate,bo.return_empty_fee as returnEmptyFee
        from order_info oi
        left join bill_order bo
        on oi.order_no = bo.order_no
        left join order_business_extend obe
        on oi.order_no = obe.order_no
        left join order_apply oa
        on oi.order_apply_no = oa.order_apply_no
        <where>
            <choose>
                <when test="tripType != null and tripType != '' ">
                    and oi.service_code = #{tripType}
                </when>
                <otherwise>
                    and oi.service_code in (2,3,4,5,6,7)
                </otherwise>
            </choose>
               <if test="createTimeStart != null and createTimeStart != ''">
                and oi.create_time <![CDATA[>=]]> #{createTimeStart}
            </if>
            <if test="createTimeEnd != null  and createTimeEnd != ''">
                and oi.create_time <![CDATA[<=]]>#{createTimeEnd}
            </if>
            <if test="useVehicleTimeStart != null and useVehicleTimeStart != ''">
                and oi.booking_order_stime <![CDATA[>=]]> #{useVehicleTimeStart}
            </if>
            <if test="useVehicleTimeEnd != null  and useVehicleTimeEnd != ''">
                and oi.booking_order_stime <![CDATA[<=]]>#{useVehicleTimeEnd}
            </if>
            <if test="numberPlate!='' and numberPlate !=null ">
                and oi.assign_car_license like CONCAT('%', #{numberPlate}, '%')
            </if>
            <if test="supplierName!='' and supplierName !=null ">
                and oi.customer_company_code = #{supplierName}
            </if>
            <if test="passenger!='' and passenger !=null ">
                and oi.booking_passenger_user_name like CONCAT('%',#{passenger},'%')
            </if>
            <if test="createUser!='' and createUser !=null ">
                and oi.customer_name  like  CONCAT('%',#{createUser},'%')
            </if>
            <if test="startCity!='' and startCity !=null ">
                and oi.start_city_code = #{startCity}
            </if>
            <if test="supplierProviderCode!='' and supplierProviderCode !=null ">
                and obe.supplier_provider_code = #{supplierProviderCode}
            </if>
            <if test="operateBussCode!='' and operateBussCode !=null ">
                and obe.operate_buss_code = #{operateBussCode}
            </if>
            <if test="orderNo!='' and orderNo !=null ">
                and oi.order_no = #{orderNo}
            </if>
            <if test="subItemNumberList != null and subItemNumberList.size() > 0">
                and oi.order_no not in
                <foreach collection="subItemNumberList" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
            and oi.deleted = 0 and oi.order_status = 90
--                查询待支出
            and obe.expense_status = 2
        </where>
        order by oi.id desc
    </select>

    <sql id="businessOrderListForApp">
        id,
        order_no,
        order_apply_no,
        order_type,
        booking_order_stime,
        booking_start_short_addr,
        booking_start_point,
        booking_end_short_addr,
        booking_end_point,
        order_status,
        service_code,
        appraise_submited,
        customer_name,
        booking_passenger_user_name,
        booking_passenger_user_phone,
        customer_company_name,
        booking_order_etime,
        passenger_struct_name,
        booking_passenger_user_id,
        customer_mobile,
        company_name
    </sql>

    <select id="selectMyOrderInfoForBussinessList" resultMap="mapper.mrcar.OrderInfoMapper.BaseResultMap" parameterType="com.izu.mrcar.order.dto.mrcar.param.OrderApplyQueryParam">
        select <include refid="businessOrderListForApp"/>,
        case order_status when 30 then 1 when 20 then 2 when 10 then 3 when 5 then 4 else 5 end sordFeild
        from order_info
        <where>
            and order_type = 2
            <if test="customerId != null">
                and  customer_id = #{customerId}
            </if>
            <if test="loginUserMobile != null">
                and (customer_mobile = #{loginUserMobile} or booking_passenger_user_phone = #{loginUserMobile} or order_apply_no in (
                select order_apply_no from order_apply_passenger where booking_passenger_user_phone = #{loginUserMobile}
                ))
            </if>
            <if test="companyId != null">
                and customer_company_id = #{companyId}
            </if>
            <if test="keyWord!=null and keyWord!='' ">
                and (customer_name  like CONCAT('%',#{keyWord,jdbcType=VARCHAR},'%') or booking_start_short_addr  like CONCAT('%',#{keyWord,jdbcType=VARCHAR},'%'))
            </if>
            <if test="orderStatusApp != null and orderStatusApp != ''">
                <if test="orderStatusApp ==2">
                    and order_status = 10
                </if>
                <if test="orderStatusApp ==3">
                    and order_status = 20
                </if>
                <if test="orderStatusApp ==4">
                    and order_status = 30
                </if>
                <if test="orderStatusApp ==5">
                    and appraise_submited = FALSE AND order_status IN (40,50,60,70,90)
                </if>
            </if>
            <if test="orderStatus != null and orderStatus!=''">
                and order_status in
                <foreach item="item" collection="orderStatus.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="serviceCodes != null and serviceCodes!=''">
                and service_code in
                <foreach item="item" collection="serviceCodes.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="createTimeStart != null and createTimeStart != ''">
                and DATE(create_time) <![CDATA[>=]]> #{createTimeStart}
            </if>
            <if test="createTimeEnd != null and createTimeEnd != ''">
                and DATE(create_time) <![CDATA[<=]]> #{createTimeEnd}
            </if>
            <if test="bookingOrderStime != null and bookingOrderStime != ''">
                and DATE(booking_order_stime) <![CDATA[>=]]> #{bookingOrderStime}
            </if>
            <if test="bookingOrderEtime != null and bookingOrderEtime != ''">
                and DATE(booking_order_stime) <![CDATA[<=]]> #{bookingOrderEtime}
            </if>
            <if test="bookingPassengerUserName != null and bookingPassengerUserName!=''">
                and booking_passenger_user_name = #{bookingPassengerUserName}
            </if>
            <if test="bookingPassengerUserPhone != null and bookingPassengerUserPhone!=''">
                and booking_passenger_user_phone = #{bookingPassengerUserPhone}
            </if>
            <if test="passengerStructId != null">
                and passenger_struct_id = #{passengerStructId}
            </if>
        </where>
        order by sordFeild asc,create_time desc
    </select>

    <!-- APP行程列表 客户端数据权限 -->
    <sql id="appDataPerm">

        <!-- 客户端 数据权限不存在不允许查看数据 -->
        <if test="dataPermType==null">
            and id = -1
        </if>

        <!-- 客户端 数据权限 本人：下单人是本人 -->
        <if test="dataPermType!=null and dataPermType==4">
            <choose>
                <when test="loginUserMobile!=null">
                    and (customer_mobile = #{loginUserMobile} or booking_passenger_user_phone = #{loginUserMobile} or order_apply_no in (
                    select order_apply_no from order_apply_passenger where booking_passenger_user_phone = #{loginUserMobile}
                    ))
                </when>
                <otherwise>
                    and id = -1
                </otherwise>
            </choose>
        </if>

        <!-- 客户端 数据权限 指定城市：出发地/目的地城市在用户数据权限-指定城市内 ∪ 下单人是本人的行程 -->
        <if test="dataPermType!=null and dataPermType==3">
            <!-- 指定城市并且没有配置城市不允许查看数据 -->
            <choose>
                <when test="dataPermCodeSet!=null and dataPermCodeSet.size()>0">
                    and (start_city_code in
                    <foreach item="cityCodeItem" collection="dataPermCodeSet" open="(" separator="," close=")">
                        #{cityCodeItem}
                    </foreach>
                    or end_city_code IN
                    <foreach item="cityCodeItem" collection="dataPermCodeSet" open="(" separator="," close=")">
                        #{cityCodeItem}
                    </foreach>
                    or customer_id = #{loginUserId}
                    )
                </when>
                <otherwise>
                    and id = -1
                </otherwise>
            </choose>
        </if>


        <!-- 客户端 数据权限 指定部门：出发地/目的地城市在用户数据权限-指定城市内 ∪ 下单人是本人的行程 -->
        <if test="dataPermType!=null and dataPermType==2">
            <choose>
                <when test="dataPermIdSet!=null and dataPermIdSet.size()>0">
                    and (struct_id in
                    <foreach item="deptIdItem" collection="dataPermIdSet" open="(" separator="," close=")">
                        #{deptIdItem}
                    </foreach>
                    or passenger_struct_id in
                    <foreach item="deptIdItem" collection="dataPermIdSet" open="(" separator="," close=")">
                        #{deptIdItem}
                    </foreach>
                    or customer_id = #{loginUserId}
                    )
                </when>
                <otherwise>
                    and id = -1
                </otherwise>
            </choose>
        </if>
    </sql>

    <select id="selectWorkStationOrderList" resultMap="mapper.mrcar.OrderInfoMapper.BaseResultMap" parameterType="com.izu.mrcar.order.dto.order.OrderInfoListForAppReqDTO">
        select <include refid="businessOrderListForApp"/>
        from order_info
        <where>
            <choose>
                <!-- 运营端 能查看的数据范围:运营端暂时不允许查询 -->
                <when test="loginSystemType!=null and loginSystemType==1">
                    and order_type = 2
                    and id = -1
                </when>
                <!-- 客户端 能查看所有类型的订单-->
                <otherwise>
                    <!-- 客户端 代垫司机没有被分派公司时，不允许查询行程列表 -->
                    <if test="loginCompanyId == null">
                        and id = -1
                    </if>
                    <!-- 客户端 只能查看本公司的行程数据 -->
                    <if test="loginCompanyId!=null">
                        and customer_company_id = #{loginCompanyId}
                    </if>
                    <!-- 引入客户端数据权限 -->
                    <include refid="appDataPerm"/>
                </otherwise>
            </choose>
            <if test="companyId != null">
                and company_id = #{companyId}
            </if>
            <if test="structId != null">
                and struct_id = #{structId}
            </if>
            <if test="customerName != null and customerName!=''">
                and customer_name = #{customerName}
            </if>
            <if test="customerMobile != null and customerMobile!=''">
                and customer_mobile = #{customerMobile}
            </if>
            <if test="orderStatus != null and orderStatus!=''">
                and order_status in
                <foreach item="item" collection="orderStatus.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="orderType != null">
                and order_type = #{orderType}
            </if>
            <if test="serviceCodes != null and serviceCodes!=''">
                and service_code in
                <foreach item="item" collection="serviceCodes.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="createTimeStart != null and createTimeStart != ''">
                and DATE(create_time) <![CDATA[>=]]> #{createTimeStart}
            </if>
            <if test="createTimeEnd != null and createTimeEnd != ''">
                and DATE(create_time) <![CDATA[<=]]> #{createTimeEnd}
            </if>
            <if test="bookingOrderStime != null and bookingOrderStime != ''">
                and DATE(booking_order_stime) <![CDATA[>=]]> #{bookingOrderStime}
            </if>
            <if test="bookingOrderEtime != null and bookingOrderEtime != ''">
                and DATE(booking_order_stime) <![CDATA[<=]]> #{bookingOrderEtime}
            </if>
            <if test="bookingPassengerUserName != null and bookingPassengerUserName!=''">
                and booking_passenger_user_name = #{bookingPassengerUserName}
            </if>
            <if test="bookingPassengerUserPhone != null and bookingPassengerUserPhone!=''">
                and booking_passenger_user_phone = #{bookingPassengerUserPhone}
            </if>
            <if test="customerCompanyId != null">
                and customer_company_id = #{customerCompanyId}
            </if>
            <if test="passengerStructId != null">
                and passenger_struct_id = #{passengerStructId}
            </if>
            <!-- app版本小于 90 不允许展示私车相关订单,只能展示内部用车和商务用车-->
            <if test="appVersion !=null and appVersion lt 90">
                and order_type in(1,2)
            </if>
        </where>
        <choose>
            <when test="bookingOrderStimeSort!=null and bookingOrderStimeSort==1">
                order by booking_order_stime asc
            </when>
            <when test="bookingOrderStimeSort!=null and bookingOrderStimeSort==2">
                order by booking_order_stime desc
            </when>
            <when test="createTimeSort!=null and createTimeSort==1">
                order by create_time asc
            </when>
            <when test="createTimeSort!=null and createTimeSort==2">
                order by create_time desc
            </when>
            <otherwise>
                order by create_time desc
            </otherwise>
        </choose>
    </select>
    <select id="checkCarStatus" resultType="java.lang.Long">
        SELECT assign_car_id FROM order_info WHERE
        booking_order_stime  <![CDATA[<=]]>  #{startTime}
        and booking_order_etime  <![CDATA[>=]]>  #{endTime}
        and order_status != 100
        and assign_car_id in
        <foreach item="item" collection="vehicleIdList" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY assign_car_id
    </select>

    <select id="selectOrderInfoByOrderApplyNo" resultMap="mapper.mrcar.OrderInfoMapper.BaseResultMap">
        select
        <include refid="mapper.mrcar.OrderInfoMapper.Base_Column_List"/>
        from order_info
        where order_apply_no = #{orderApplyNo}
        <if test="orderStatus !=null">
            and order_status=#{orderStatus}
        </if>
    </select>

    <select id="selectByCompanyIds" resultType="com.izu.business.dto.StatisticsBusinessDTO">
        select customer_company_id companyId,count(id) orderInfoCount,
               sum( CASE WHEN order_type = 1 THEN 1 ELSE 0 END ) orderApplyInternalCount,
               sum( CASE WHEN order_type = 3 THEN 1 ELSE 0 END ) privateOrderCount
        from order_info
        where order_type !=2 and order_status > 10 and customer_company_id in
        <foreach collection="companyIds" open="(" close=")" item="companyId" separator=",">
            #{companyId}
        </foreach>
        <if test="queryDate !=null and queryDate !=''">
            and DATE(create_time) = #{queryDate}
        </if>
        group by customer_company_id
    </select>


    <select id="selectMotorcadeByCompanyIds" resultType="com.izu.business.dto.StatisticsBusinessDTO">
        select customer_company_id companyId,count(id) orderApplyMotorcadeCount
        from order_info
        where order_type =2 and deleted = 0 and customer_company_id in
        <foreach collection="companyIds" open="(" close=")" item="companyId" separator=",">
            #{companyId}
        </foreach>
        <if test="queryDate !=null and queryDate !=''">
            and DATE(create_time) = #{queryDate}
        </if>
        group by customer_company_id
    </select>



    <select id="getOrderBusinessStatistics" resultType="com.izu.mrcar.order.dto.order.business.OrderBusinessStatisticsDTO">
        select
        sum( CASE WHEN order_type = 1 THEN 1 ELSE 0 END ) internalOrderCount,
        sum( CASE WHEN order_type = 2 THEN 1 ELSE 0 END ) motorcadeOrderCount,
        sum( CASE WHEN order_type = 3 THEN 1 ELSE 0 END ) privateOrderCount,
        count(distinct  case when order_type=1 then company_id end) internalCompanyCount,
        count(distinct  case when order_type=2 then customer_company_id end) motorcadeCompanyCount,
        count(distinct  case when order_type=3 then company_id end) privateCompanyCount
        from order_info
        where order_status not in (100) and date_format(create_time,'%Y-%m-%d') = #{statisticsDay}
    </select>
    <select id="selectMileageByBookingTime" resultType="com.izu.business.dto.bi.BiStatisticsMileage">
        SELECT
            DATE_FORMAT(booking_order_stime, '%Y-%m-%d') AS statDate,
            SUM(trip_mileage) AS totalMileage
        FROM
            order_info
        WHERE
            booking_order_etime BETWEEN #{startDate} AND #{endDate}
          AND assign_car_id = #{vehicleId} and order_type = 1 and company_id = #{companyId}
        GROUP BY
            DATE_FORMAT(booking_order_stime, '%Y-%m-%d')
        ORDER BY
            statDate;
    </select>
    <select id="statisticsMileageUpdate" resultType="com.izu.business.dto.bi.BiStatisticsMileage">
        SELECT
            DATE_FORMAT(booking_order_stime, '%Y-%m-%d') AS statDate,
            SUM(trip_mileage) AS totalMileage
        FROM
            order_info
        WHERE
            booking_order_stime BETWEEN #{startDate} AND #{endDate}
          AND assign_car_id = #{vehicleId} and order_type = 1 and company_id = #{companyId}
        GROUP BY
            DATE_FORMAT(booking_order_stime, '%Y-%m-%d')
        ORDER BY
            statDate
    </select>

    <select id="biStatistics"  parameterType="com.izu.mrcar.order.dto.order.BIStatisticsOrderFeeReqDTO"
            resultType="com.izu.mrcar.order.dto.order.OrderFeeRespDTO">
        select b.attach_code as attachCode,sum(b.fee_amount) as fee,
               date_format(a.booking_order_stime,'%Y-%m-%d') as statDate
        from order_info a
        inner join bill_attach b on a.order_no = b.order_no
        where a.order_type=1 and a.order_status=90
        and a.assign_car_license = #{vehicleLicense} and a.company_id=#{companyId}
        and b.attach_status=1
          <if test="factEndDateStart != null">
              and a.fact_end_date &gt;= #{factEndDateStart}
          </if>
        <if test="factEndDateEnd != null">
            and a.fact_end_date &lt; #{factEndDateEnd}
        </if>
        <if test="bookingOrderStimeStart != null">
            and a.booking_order_stime &gt;= #{bookingOrderStimeStart}
        </if>
        <if test="bookingOrderStimeEnd != null">
            and a.booking_order_stime &lt; #{bookingOrderStimeEnd}
        </if>
        group by attachCode,statDate
    </select>

    <select id="returnEmptyFeeStatistics"  parameterType="com.izu.mrcar.order.dto.order.BIStatisticsOrderFeeReqDTO"
            resultType="com.izu.mrcar.order.dto.order.OrderFeeRespDTO">
        select sum(b.return_empty_fee) as fee, date_format(a.booking_order_stime,'%Y-%m-%d') as statDate from order_info a
        inner join bill_order b on a.order_no = b.order_no
        where a.order_type=1 and a.order_status=90 and
        a.assign_car_license =#{vehicleLicense} and a.company_id=#{companyId}
        <if test="factEndDateStart != null">
            and a.fact_end_date &gt;= #{factEndDateStart}
        </if>
        <if test="factEndDateEnd != null">
            and a.fact_end_date &lt; #{factEndDateEnd}
        </if>
        <if test="bookingOrderStimeStart != null">
            and a.booking_order_stime &gt;= #{bookingOrderStimeStart}
        </if>
        <if test="bookingOrderStimeEnd != null">
            and a.booking_order_stime &lt; #{bookingOrderStimeEnd}
        </if>
        group by statDate
    </select>
    <select id="getTraceOrderByTime" resultType="com.izu.mrcar.order.dto.order.TraceOrderByTimeResp">
        SELECT
        order_no AS orderNo,
        assign_driver_name AS assignDriverName,
        assign_driver_phone AS assignDriverPhone,
        booking_passenger_user_name AS bookingPassengerUserName,
        booking_passenger_user_phone AS bookingPassengerUserPhone,
        fact_start_date AS factStartDate,
        fact_end_date AS factEndDate,
        fact_start_short_addr AS factStartShortAddr,
        fact_end_short_addr AS factEndShortAddr,
        trip_mileage AS tripMileage,
        TIMESTAMPDIFF(SECOND, fact_start_date, fact_end_date) AS durationSeconds
        FROM
        order_info
        WHERE
        order_type = 1
        AND order_status = 90
        AND company_id = #{companyId}
        AND assign_car_id = #{vehicleId}
        AND fact_start_date <![CDATA[<=]]> #{endDate}  -- inputEndDate
        AND fact_end_date >= #{beginDate}
    </select>


</mapper>
