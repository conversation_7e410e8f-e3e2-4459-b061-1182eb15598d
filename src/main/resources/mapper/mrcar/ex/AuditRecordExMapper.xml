<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.AuditRecordExMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.AuditRecord"
             extends="mapper.mrcar.AuditRecordMapper.BaseResultMap">

  </resultMap>
  <sql id="Base_Column_List">
    <include refid="mapper.mrcar.AuditRecordMapper.Base_Column_List"/>
  </sql>

  <select id="listAuditRecords" resultMap="BaseResultMap">
    select
      <include refid="Base_Column_List"/>
    from
      so_audit_record
    where
      customer_order_num = #{customerOrderNum}
      <if test="auditType != null">
        and audit_type = #{auditType}
      </if>
      <if test="supplierOrderNum != null and supplierOrderNum != '' ">
          and supplier_order_num like concat('%',#{supplierOrderNum},'%')
      </if>
  </select>

  <select id="getAuditRecordByRecordNum" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/> from so_audit_record where audit_record_num = #{auditRecordNum}
  </select>

  <select id="getAuditRecordByRecordNumForUpdate" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/> from so_audit_record where audit_record_num = #{auditRecordNum} for update
  </select>

  <select id="auditRecordPageList" resultType="com.izu.mrcar.order.dto.lingsan.audit.AuditDTO">
    select
      audit_record_num auditRecordNum,
      audit_type auditType,
      audit_time auditTime,
      audit_status auditStatus,
      customer_order_num customerOrderNum,
      supplier_order_num supplierOrderNum,
      supplier_name supplierName,
      supplier_type supplierType,
      order_owner_company_id orderOwnerCompanyId,
      order_owner_company_name orderOwnerCompanyName,
      create_time createTime,
      reject_reason rejectReason
    from
      so_audit_record
    <where>
      <if test="auditType != null ">
        and audit_type = #{auditType}
      </if>
      <if test="auditStatus != null ">
        and audit_status = #{auditStatus}
      </if>
      <if test="auditRecordNum != null and auditRecordNum != '' ">
        and audit_record_num = #{auditRecordNum}
      </if>
      <if test="customerOrderNum != null and customerOrderNum != '' ">
        and customer_order_num = #{customerOrderNum}
      </if>
      <if test="orderOwnerCompanyName != null and orderOwnerCompanyName != '' ">
        and order_owner_company_name = #{orderOwnerCompanyName}
      </if>
      <if test="supplierOrderNum != null and supplierOrderNum != '' ">
        and supplier_order_num like concat('%',#{supplierOrderNum},'%')
      </if>
      <if test="loginUser != null and loginUser.deptCodes != null and loginUser.deptCodes.size() > 0 ">
        and order_owner_dept_code in
        <foreach collection="loginUser.deptCodes" item="deptCode" open="(" separator="," close=")">
          #{deptCode}
        </foreach>
      </if>
    </where>
    order by audit_record_id desc
  </select>

  <select id="listAuditRecordsByCondition" resultMap="BaseResultMap">
    select
      <include refid="Base_Column_List"/>
    from
      so_audit_record
    <where>
      <if test="customerOrderNum != null and customerOrderNum != '' ">
        and customer_order_num = #{customerOrderNum}
      </if>
      <if test="auditType != null ">
        and audit_type = #{auditType}
      </if>
      <if test="auditStatuss != null ">
        and audit_status in
        <foreach collection="auditStatuss" item="auditStatus" open="(" separator="," close=")">
          #{auditStatus}
        </foreach>
      </if>
      <if test="supplierOrderNum != null and supplierOrderNum != '' ">
        and supplier_order_num like concat('%',#{supplierOrderNum},'%')
      </if>
    </where>
  </select>

  <select id="getLastAuditRecord" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from
    so_audit_record
    where
    customer_order_num = #{customerOrderNum}
    <if test="auditType != null">
      and audit_type = #{auditType}
    </if>
    <if test="supplierOrderNum != null and supplierOrderNum != '' ">
      and supplier_order_num like concat('%',#{supplierOrderNum},'%')
    </if>
    order by audit_record_id desc limit 1
  </select>

</mapper>