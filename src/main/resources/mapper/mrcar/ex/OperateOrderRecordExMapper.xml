<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.OperateOrderRecordExMapper">
    <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.OperateOrderRecord"
               extends="mapper.mrcar.OperateOrderRecordMapper.BaseResultMap" />
    <select id="selectByCustomerOrderNum" resultMap="mapper.mrcar.OperateOrderRecordMapper.BaseResultMap">
        select
        <include refid="mapper.mrcar.OperateOrderRecordMapper.Base_Column_List" />
        from so_operate_order_record
        where customer_order_num = #{customerOrderNum}
        <if test="relateBusinessType != null ">
            AND operate_type = #{relateBusinessType}
        </if>
    </select>
</mapper>