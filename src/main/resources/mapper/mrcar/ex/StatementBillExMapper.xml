<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.StatementBillExMapper">
    <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.StatementBill"
               extends="mapper.mrcar.StatementBillMapper.BaseResultMap">
    </resultMap>

    <sql id="Base_Column_List">
        <include refid="mapper.mrcar.StatementBillMapper.Base_Column_List"/>
    </sql>
    <select id="selectByStatementBillNum" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from so_statement_bill where statement_bill_num = #{statementBillNum} limit 1
    </select>
    <select id="selectBillList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from so_statement_bill
        <!-- 账单类型 1 收入账单（客户账单） 2 支出账单（供应商账单） -->
        where bill_type = 1
        <if test="createTimeStart != null">
            and create_time <![CDATA[>=]]>  #{createTimeStart}
        </if>
        <if test="createTimeEnd != null">
            and create_time <![CDATA[<=]]>  #{createTimeEnd}
        </if>
        <if test="billStatus != null">
            and bill_status = #{billStatus}
        </if>
        <if test="secondBillStatus != null">
            and second_bill_status = #{secondBillStatus}
        </if>
        <if test="settleStatus != null">
            and settle_status = #{settleStatus}
        </if>
        <if test="statementBillNum != null and statementBillNum != ''">
            and statement_bill_num =  #{statementBillNum}
        </if>
        <if test="companyCustomerId != null">
            and company_customer_id =  #{companyCustomerId}
        </if>
        <if test="customerOrderNum != null and customerOrderNum != ''">
            and exists(
                select 1 from so_statement_bill_detail d
                where d.statement_bill_num = so_statement_bill.statement_bill_num and d.modify_count = 0
                and d.customer_order_num = #{customerOrderNum}
            )
        </if>
        <if test="loginUser != null and loginUser.permType == 1 and loginUser.deptCodes != null and loginUser.deptCodes.size() > 0">
            and subsidiary_company_code in
            <foreach collection="loginUser.deptCodes" item="deptCode" open="(" close=")" separator=",">
                #{deptCode}
            </foreach>
        </if>
        <if test="loginUser != null and loginUser.permType == 2 and loginUser.companyId != null">
            and company_customer_id =  #{loginUser.companyId}
        </if>
        order by statement_bill_id desc
    </select>
    <select id="selectLastByCompanyCustomerId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from so_statement_bill where company_customer_id = #{companyCustomerId} order by bill_end_time desc limit 1
    </select>
    <select id="selectByCompanyCustomerIdAndBillStartTimeAndBillEndTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from so_statement_bill
        where company_customer_id = #{companyCustomerId}
          and bill_start_time = #{billStartTime}
          and bill_end_time = #{billEndTime}
        limit 1
    </select>
    <select id="selectExpenditurePageList" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from
            so_statement_bill
        where
            <!-- 账单类型 1 收入账单（客户账单） 2 支出账单（供应商账单） -->
            bill_type = 2
            <if test="statementBillNum != null and statementBillNum !=''">
                and statement_bill_num = #{statementBillNum}
            </if>
            <if test="createTimeStart != null">
                and create_time <![CDATA[ >= ]]> #{createTimeStart}
            </if>
            <if test="createTimeEnd != null">
                and create_time <![CDATA[ <= ]]> #{createTimeEnd}
            </if>
            <if test="billStatus != null">
                and bill_status = #{billStatus}
            </if>
            <if test="settleStatus != null">
                and settle_status = #{settleStatus}
            </if>
            <if test="relateBillNum != null and relateBillNum != ''">
                and relate_bill_num = #{relateBillNum}
            </if>
            <if test="companyCustomerId != null">
                and company_customer_id = #{companyCustomerId}
            </if>
            <if test="companyCustomerName != null and companyCustomerName != ''">
                and company_customer_name = #{companyCustomerName}
            </if>
            <if test="serviceCompanyCustomerId != null">
                and service_company_customer_id = #{serviceCompanyCustomerId}
            </if>
            <if test="serviceCompanyCustomerName != null and serviceCompanyCustomerName != ''">
                and service_company_customer_name = #{serviceCompanyCustomerName}
            </if>
            <if test="customerOrderNum != null and customerOrderNum != ''">
                and exists(
                    select
                        1
                    from
                        so_statement_bill_detail d
                    where
                        d.statement_bill_num = so_statement_bill.statement_bill_num
                        and d.customer_order_num = #{customerOrderNum}
                )
            </if>
            <if test="subsidiaryCompanyCodes != null and subsidiaryCompanyCodes != ''">
                and subsidiary_company_code in
                <foreach collection="subsidiaryCompanyCodes.split(',')" item="deptCode" open="(" close=")" separator=",">
                    #{deptCode}
                </foreach>
            </if>
        order by
            create_time desc,
            statement_bill_id desc
    </select>
    <select id="selectSupplierBillPageList" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List"/>
        from so_statement_bill
        where company_customer_id = #{companyCustomerId}
        and bill_type = 2
        <if test="billStatus != null">
            and bill_status = #{billStatus}
        </if>
        <if test="statementBillNum != null and statementBillNum != ''">
            and statement_bill_num = #{statementBillNum}
        </if>
        <if test="createTimeStart != null">
            and create_time <![CDATA[ >= ]]> #{createTimeStart}
        </if>
        <if test="createTimeEnd != null">
            and create_time <![CDATA[ <= ]]> #{createTimeEnd}
        </if>
        <if test="supplierOrderNum != null and supplierOrderNum != ''">
            and exists(
                select 1 from so_statement_bill_detail d
                where d.statement_bill_num = so_statement_bill.statement_bill_num
                and d.supplier_order_num = #{supplierOrderNum}
            )
        </if>
        order by statement_bill_id desc
    </select>
</mapper>