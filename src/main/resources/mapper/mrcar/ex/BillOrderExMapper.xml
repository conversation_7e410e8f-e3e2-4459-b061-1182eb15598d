<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.BillOrderExMapper">

    <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.BillOrder" extends="mapper.mrcar.BillOrderMapper.BaseResultMap"/>

  <select id="selectByOrderNo" resultMap="mapper.mrcar.BillOrderMapper.BaseResultMap">
    select 
    <include refid="mapper.mrcar.BillOrderMapper.Base_Column_List" />
    from bill_order
    where order_no = #{orderNo}
    limit 1
  </select>
  <select id="addAttachFee">
    update bill_order
    set total_amount = (total_amount + #{totalAmount}),
        shouldpay_amount = (shouldpay_amount + #{shouldpayAmount}),
        deduction_amount = (deduction_amount +#{deductionAmount})
    where id = #{id}
  </select>

    <select id="selectByOrderApplyNo" resultMap="mapper.mrcar.BillOrderMapper.BaseResultMap">
        select
        <include refid="mapper.mrcar.BillOrderMapper.Base_Column_List" />
        from bill_order
        where order_apply_no = #{applyNo}
    </select>


    <select id="getSettledBillsByBussCode" resultMap="BaseResultMap">
        SELECT
        b.order_apply_no ,
        b.order_no,
        b.bill_no,
        b.total_amount,
        b.shouldpay_amount,
        b.valuation_fee,
        b.fixed_price_valid,
        b.fixed_price,
        b.reduction_amount,
        b.other_amount
        FROM
        bill_order AS b
        INNER JOIN order_info AS info ON b.order_no = info.order_no
        LEFT JOIN order_apply AS apply ON info.order_apply_no = apply.order_apply_no
        LEFT JOIN order_vehicle_base AS t ON info.assign_car_id = t.vehicle_id
        <where>
            AND apply.order_type = 2
            AND apply.order_status = 90
            <if test="operateBussCode!=null and operateBussCode!=''">
                and t.operate_buss_code = #{operateBussCode}
            </if>
            <if test="statsStartTime != null and statsStartTime!=''">
                and apply.order_settle_time &gt;= #{statsStartTime}
            </if>
            <if test="statsEndTime!=null and statsEndTime!=''">
                and apply.order_settle_time &lt;= #{statsEndTime}
            </if>
        </where>
    </select>

    <select id="getCompletedBill" resultType="com.izu.business.dto.BillOrderStatisDTO">
        SELECT
        o.service_code as serviceCode,
        o.assign_car_id as assignCarId,
        o.assign_car_license as assignCarLicense,
        b.bill_order_no as billOrderNo,
        b.order_apply_no as orderApplyNo,
        b.order_no as orderNo,
        b.bill_no as billNo,
        b.policy_type as policyType,
        b.total_amount as totalAmount,
        b.shouldpay_amount as shouldpayAmount,
        b.valuation_fee as valuationFee,
        b.policy_price as policyPrice,
        b.total_mileage as totalMileage,
        b.total_time as totalTime,
        b.total_time_fee as totalTimeFee,
        b.total_mileage_fee as totalMileageFee,
        b.base_fee as baseFee,
        b.base_mileage as baseMileage ,
        b.base_mileage_fee as baseMileageFee,
        b.base_time as baseTime,
        b.base_time_fee as baseTimeFee,
        b.over_mileage as overMileage,
        b.over_mileage_fee as overMileageFee,
        b.over_time as overTime,
        b.over_time_fee as overTimeFee,
        b.attach_fee as attachFee,
        b.return_empty_fee as returnEmptyFee,
        b.return_empty_mileage as returnEmptyMileage,
        b.return_empty_price as returnEmptyPrice,
        b.night_service_fee as nightServiceFee,
        b.night_service_time as nightServiceTime,
        b.night_service_price as nightServicePrice,
        o.fixed_price_valid as fixedPriceValid,
        o.fixed_price as fixedPrice,
        d.coupon_id as couponId
        FROM
        order_info as o
        left join bill_order as b on o.order_no = b.order_no
        left join order_apply as d on o.order_apply_no = d.order_apply_no
        WHERE
        o.order_status >= 40
        AND o.order_type = 2
        AND o.order_status != 100
        AND d.order_status !=60
        <if test="cityCode!=null">
            and o.assign_driver_city_code = #{cityCode}
        </if>
        <if test="statsStartTime!=null and statsStartTime!=''">
            AND o.fact_end_date &gt;= #{statsStartTime}
        </if>
        <if test="statsEndTime!=null and statsEndTime!=''">
            AND o.fact_end_date &lt;= #{statsEndTime}
        </if>

    </select>


    <select id="getCompletedBillByBussCode" resultType="com.izu.business.dto.BillOrderStatisDTO">
        SELECT
        o.service_code as serviceCode,
        o.assign_car_id as assignCarId,
        o.assign_car_license as assignCarLicense,
        b.bill_order_no as billOrderNo,
        b.order_apply_no as orderApplyNo,
        b.order_no as orderNo,
        b.bill_no as billNo,
        b.policy_type as policyType,
        b.total_amount as totalAmount,
        b.shouldpay_amount as shouldpayAmount,
        b.valuation_fee as valuationFee,
        b.policy_price as policyPrice,
        b.total_mileage as totalMileage,
        b.total_time as totalTime,
        b.total_time_fee as totalTimeFee,
        b.total_mileage_fee as totalMileageFee,
        b.base_fee as baseFee,
        b.base_mileage as baseMileage ,
        b.base_mileage_fee as baseMileageFee,
        b.base_time as baseTime,
        b.base_time_fee as baseTimeFee,
        b.over_mileage as overMileage,
        b.over_mileage_fee as overMileageFee,
        b.over_time as overTime,
        b.over_time_fee as overTimeFee,
        b.attach_fee as attachFee,
        b.return_empty_fee as returnEmptyFee,
        b.return_empty_mileage as returnEmptyMileage,
        b.return_empty_price as returnEmptyPrice,
        b.night_service_fee as nightServiceFee,
        b.night_service_time as nightServiceTime,
        b.night_service_price as nightServicePrice,
        o.fixed_price_valid as fixedPriceValid,
        o.fixed_price as fixedPrice,
        d.coupon_id as couponId
        FROM
        order_info as o
        left join bill_order as b on o.order_no = b.order_no
        left join order_apply as d on o.order_apply_no = d.order_apply_no
        LEFT JOIN order_vehicle_base AS t ON o.assign_car_id = t.vehicle_id
        WHERE
        o.order_status >= 40
        AND o.order_type = 2
        AND o.order_status != 100
        AND d.order_status !=60
        <if test="operateBussCode!=null and operateBussCode!=''">
            and t.operate_buss_code = #{operateBussCode}
        </if>
        <if test="statsStartTime!=null and statsStartTime!=''">
            AND o.fact_end_date &gt;= #{statsStartTime}
        </if>
        <if test="statsEndTime!=null and statsEndTime!=''">
            AND o.fact_end_date &lt;= #{statsEndTime}
        </if>

    </select>

    <update id="updateSupplierPayableAmountByOrderNo">
        update bill_order
        set supplier_payable_amount = #{supplierPayableAmount}
        where order_no = #{orderNo}
    </update>

    <update id="updateOrderBillMileageAndTime">
        update bill_order
        set total_mileage = #{totalMileage},
        total_time = #{totalTime}
        where order_no = #{orderNo}
    </update>
    <update id="updateBillOrderRefund">
        <foreach collection="refundInfoList" item="item" separator=";">
            update
            bill_order
            set
            refund_status = #{item.refundStatus},
            refund_amount_synthesis = #{item.refundAmountSynthesis}
            where
                <if test="item.orderApplyNo!=null and item.orderApplyNo!=''">
                    order_apply_no = #{item.orderApplyNo}
                </if>
                <if test="item.billOrderNo!=null and item.billOrderNo!=''">
                    bill_order_no = #{item.billOrderNo}
                </if>
        </foreach>
    </update>
</mapper>