<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.CoOrderInfoFeeExMapper">

    <select id="list" resultMap="mapper.mrcar.CoOrderInfoFeeMapper.BaseResultMap">
        select
        <include refid="mapper.mrcar.CoOrderInfoFeeMapper.Base_Column_List"/>
        from co_order_info_fee
        where 1=1
        <if test="coOrderId != null">
            AND co_order_id = #{coOrderId}
        </if>
        <if test="orderNum != null and orderNum != '' ">
            AND order_num = #{orderNum}
        </if>
        <if test="orderNumList != null and orderNumList.size() != 0">
            AND order_num in
            <foreach collection="orderNumList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <insert id="batchInsert" parameterType="com.izu.order.entity.mrcar.CoOrderInfoFee">
        insert into co_order_info_fee (create_id, create_name, create_mobile,
        co_order_id, order_num,
        fee_code, fee_name, fee_amount
        ) values
        <foreach collection="orderInfoFees" item="element" separator=",">
            (#{element.createId}, #{element.createName}, #{element.createMobile}, #{element.coOrderId},
            #{element.orderNum},
            #{element.feeCode}, #{element.feeName}, #{element.feeAmount})
        </foreach>

    </insert>
    <select id="selectByCoOrderId" parameterType="java.lang.Integer" resultMap="mapper.mrcar.CoOrderInfoFeeMapper.BaseResultMap">
        select
        <include refid="mapper.mrcar.CoOrderInfoFeeMapper.Base_Column_List" />
        from co_order_info_fee
        where co_order_id = #{coOrderId,jdbcType=INTEGER}
    </select>
    <select id="selectByOrderNos" resultType="com.izu.order.entity.mrcar.CoOrderFeeSimpleInfo">
        SELECT order_num AS orderNum,
        -- 注意：若有新费用项添加，此处需修改
        SUM(IF(fee_code = '03', fee_amount, 0)) AS petrolAmount,
        SUM(IF(fee_code = '04', fee_amount, 0)) AS violateAmount,
        SUM(IF(fee_code = '05', fee_amount, 0)) AS pickUpAmount,
        SUM(IF(fee_code = '99', fee_amount, 0)) AS otherAmount
        FROM co_order_info_fee
        where order_num in
        <foreach collection="orderNums" item="orderNum" separator="," open="(" close=")">
            #{orderNum}
        </foreach>
        GROUP BY order_num
    </select>
</mapper>