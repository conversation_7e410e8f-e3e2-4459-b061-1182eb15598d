<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.BusinessOperationLogExMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.BusinessOperationLog" extends="mapper.mrcar.BusinessOperationLogMapper.BaseResultMap"/>
   <select id="getOperationListByExpenditureNo" resultType="com.izu.mrcar.order.dto.provider.output.OperationOutputDTO">
        select bol.operator_name as operatorName,bol.operation_time as operationTime,bol.operation_type_name as operationTypeName,
               bol.operation_field as approvalComments,bol.before_value as beforeValue,bol.after_value as afterValue
        from
        business_operation_log bol
        where expenditure_no = #{expenditureNo}
   </select>

</mapper>