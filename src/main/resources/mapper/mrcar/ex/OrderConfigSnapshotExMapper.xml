<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.OrderConfigSnapshotExMapper">


    <select id="selectByOrderId" resultMap="mapper.mrcar.OrderConfigSnapshotMapper.BaseResultMap">
        select <include refid="mapper.mrcar.OrderConfigSnapshotMapper.Base_Column_List"/>
        from co_order_config_snapshot
        where co_order_id = #{coOrderId}
    </select>

    <insert id="batchInsert">
        insert into co_order_config_snapshot
        (co_order_id, out_mile_price, day_price, rent_rule_hour, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.coOrderId,jdbcType=INTEGER}, #{item.outMilePrice,jdbcType=DECIMAL}, #{item.dayPrice,jdbcType=DECIMAL},
            #{item.rentRuleHour,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

</mapper>