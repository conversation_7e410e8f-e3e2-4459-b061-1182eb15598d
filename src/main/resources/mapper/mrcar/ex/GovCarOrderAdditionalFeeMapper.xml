<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.GovCarOrderAdditionalFeeMapper">

    <select id="selectPageListForApp" parameterType="com.izu.mrcar.order.dto.govcar.order.AppGovCarOrderAdditionalFeePageReqDTO"
            resultType="com.izu.mrcar.order.dto.govcar.order.AppGovCarOrderAdditionalFeePageRespDTO">
        select b.order_no as orderNo, o.vehicle_license as vehicleLicense,
        o.actual_pickup_time as actualPickupTime,b.bill_no as orderAdditionalFeeNo,
        b.bill_amount as billAmount, b.bill_status as orderAdditionalFeeStatus
        from gov_car_order_bill b left join gov_car_order o on b.order_no = o.order_no
        <where>
            b.bill_type = 2 and o.company_code = #{loginCompanyCode} and o.customer_id = #{loginUserId}
            <if test="orderAdditionalFeeNo != null and orderAdditionalFeeNo != ''">
                and b.bill_no = #{orderAdditionalFeeNo}
            </if>
            <if test="orderAdditionalFeeStatus != null">
                and b.bill_status = #{orderAdditionalFeeStatus}
            </if>
        </where>
        order by b.create_time desc
    </select>

    <select id="selectOrderAdditionalFeeDetailByBillNos"
            resultType="com.izu.mrcar.order.dto.govcar.bill.GovCarOrderBillDetailDTO">
        select
            price_name as priceName,
            price_unit as priceUnit,
            quantity,bill_no as billNo,
            price_amount as priceAmount,
            total_amount as totalAmount,
            fee_notes as feeNotes
        from gov_car_order_bill_detail
        where detail_type = 10 and bill_no in
            <foreach collection="billNos" item="billNo" open="(" separator="," close=")">
                #{billNo}
            </foreach>
    </select>

    <select id="selectOrderAdditionalFeeByBillNo"
            resultType="com.izu.mrcar.order.dto.govcar.order.GovCarOrderAdditionalFeeDetailAppDTO">
        select
            b.bill_no as orderAdditionalFeeNo,
            b.bill_status as orderAdditionalFeeStatus,
            b.bill_amount as billAmount,
            b.bill_notes as billNotes,
            b.create_time as createTime,
            b.payed_time as payedTime,
            b.order_no as orderNo,
            o.vehicle_license as vehicleLicense,
            o.actual_pickup_time as actualPickupTime,
            o.actual_return_time as actualReturnTime
        from gov_car_order o
                 inner join gov_car_order_bill b on o.order_no = b.order_no
        where b.bill_no = #{billNo}
    </select>

    <select id="selectOrderTotalAmount" resultType="java.math.BigDecimal">
        select sum(total_amount)
        from gov_car_order_bill_detail
        where order_no = #{orderNo} and detail_type != 10
    </select>

</mapper>
