<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.BusinessSupplierExpenditureExMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.BusinessSupplierExpenditure" extends="mapper.mrcar.BusinessSupplierExpenditureMapper.BaseResultMap"/>
  <select id="selectByParam" resultType="com.izu.mrcar.order.dto.provider.output.SupplierSpendOutputDTO">
    select
    bse.expenditure_no as expenditureNo,bse.supplier_name as supplierName,bse.expenditure_status as expenditureStatus,
    bse.should_pay_total_amount as customerBillTotal,bse.should_pay_total_amount_no_rate as customerBillTotalNoTax,
    bse.supplier_amount_total as shouldPayAmountTotal,bse.supplier_amount_total_no_rate as shouldPayAmountTotalNoTax,
    bse.supplier_rate as supplierTaxPoint,bse.return_rate as incomeRate,bse.suppler_invoice_type as supplierBillType,
    bse.order_count AS subTripCount,bse.vehicle_user_name as useVehicleName,bse.creator_name as createName,
    bse.create_time as createTime,bse.update_name as updateName,bse.update_time as updateTime,bse.approval_suggestion as approvalSuggestion,
    bse.push_result as pushResult,IFNULL(sum(bo.refund_amount_synthesis),0) AS returnAmountTotal
    from business_supplier_expenditure bse
    left join business_supplier_expenditure_detail bsed on bse.expenditure_no = bsed.expenditure_no
    left join  bill_order bo ON bsed.order_no = bo.order_no
    <where>
      <if test="supplierName!='' and supplierName !=null ">
        and bse.supplier_name like CONCAT('%', #{supplierName}, '%')
      </if>
      <if test="creatorCode!='' and creatorCode!=null">
        and bse.creator_id = #{creatorCode}
      </if>
      <if test="createTimeStart != '' and createTimeStart != null">
        and bse.create_time <![CDATA[>=]]> #{createTimeStart}
      </if>
      <if test="createTimeEnd != '' and createTimeEnd != null">
        and bse.create_time <![CDATA[<=]]>#{createTimeEnd}
      </if>
      <if test="expenditureNo!='' and expenditureNo !=null ">
        and bse.expenditure_no = #{expenditureNo}
      </if>
      <if test="expenditureStatus!='' and expenditureStatus!=null">
        and bse.expenditure_status = #{expenditureStatus}
      </if>
      <if test="vehicleUserCode!='' and vehicleUserCode!=null">
        and bse.vehicle_user_code = #{vehicleUserCode}
      </if>
      <if test="shouldPayAmountStart != '' and shouldPayAmountStart != null">
        and bse.supplier_amount_total <![CDATA[>=]]> #{shouldPayAmountStart}
      </if>
      <if test="shouldPayAmountEnd != '' and shouldPayAmountEnd != null">
        and bse.supplier_amount_total <![CDATA[<=]]>#{shouldPayAmountEnd}
      </if>
      <if test="orderNo != '' and orderNo != null">
        and bsed.order_no = #{orderNo}
      </if>
    </where>
    GROUP BY bse.expenditure_no
    order by (case when bse.expenditure_status=2 THEN 1 when bse.expenditure_status=3 THEN 2 when bse.expenditure_status=5 THEN 3 else 4 END),bse.update_time desc

  </select>
  <select id="exportSubSupplierSpendList" resultType="com.izu.mrcar.order.dto.provider.output.ExportSubTripDetailDTO">
    select
      bse.expenditure_no AS expenditureNo,
      bse.expenditure_status AS expenditureStatus,
      bsed.order_no AS orderNo,
      bsed.service_code AS serviceCode,
      bsed.vehicle_provider AS vehicleProvider,
      bsed.driver_provider AS driverProvider,
      bsed.company_name AS companyName,
      IFNULL(bsed.should_pay_amount, 0) AS shouldPayAmount,
      IFNULL(
      bsed.customer_bill_tax_rate,
      0
      ) AS customerBillTaxRate,
      IFNULL(
      bsed.should_pay_amount_no_rate,
      0
      ) AS shouldPayAmountNoRate,
      IFNULL(bsed.supplier_amount, 0) AS supplierAmount,
      IFNULL(
      bsed.supplier_amount_no_rate,
      0
      ) AS supplierAmountNoRate,
      IFNULL(bsed.return_rate, 0) AS incomeRate,
      bsed.car_license AS numberPlate,
      bsed.driver_name AS driverName,
      bsed.driver_phone AS driverPhone,
      bsed.use_time AS useVehicleTime,
      bsed.driver_id AS driverId,
      IFNULL(bsed.base_amount, 0) AS basicFee,
      IFNULL(bsed.over_time_amount, 0) AS overtimeFee,
      IFNULL(bsed.over_mileage_amount, 0) AS excessMileageFee,
      IFNULL(bsed.other_amount, 0) AS otherFee,
      IFNULL(
      bsed.reduction_total_amount,
      0
      ) AS deductionsTotalFee,
      bsed.company_id AS companyId,
      bsed.company_code AS companyCode,
      bsed.customer_id AS customerId,
      bsed.customer_name AS customerName,
      bsed.booking_passenger_user_id AS bookingPassengerUserId,
      bsed.booking_passenger_user_name AS bookingPassengerUserName,
      IFNULL(
      bo.refund_amount_synthesis,
      0
      ) AS returnAmount,
      oi.create_time as orderCreateTime,
      bpol.operation_time as pushBillTime
    FROM
    business_supplier_expenditure bse
    LEFT JOIN business_supplier_expenditure_detail bsed ON bse.expenditure_no = bsed.expenditure_no
    LEFT JOIN bill_order bo ON bsed.order_no = bo.order_no
    left join order_info oi on bsed.order_no = oi.order_no
    left join business_push_operation_log bpol on bpol.order_apply_no = oi.order_apply_no and bpol.push_status = 1
    <where>
      <if test="supplierName!='' and supplierName !=null ">
        and bse.supplier_name like CONCAT('%', #{supplierName}, '%')
      </if>
      <if test="creatorCode!='' and creatorCode!=null">
        and bse.creator_id = #{creatorCode}
      </if>
      <if test="createTimeStart != '' and createTimeStart != null">
        and bse.create_time <![CDATA[>=]]> #{createTimeStart}
      </if>
      <if test="createTimeEnd != '' and createTimeEnd != null">
        and bse.create_time <![CDATA[<=]]>#{createTimeEnd}
      </if>
      <if test="expenditureNo!='' and expenditureNo !=null ">
        and bse.expenditure_no = #{expenditureNo}
      </if>
      <if test="expenditureStatus!='' and expenditureStatus!=null">
        and bse.expenditure_status = #{expenditureStatus}
      </if>
      <if test="vehicleUserCode!='' and vehicleUserCode!=null">
        and bse.vehicle_user_code = #{vehicleUserCode}
      </if>
      <if test="shouldPayAmountStart != '' and shouldPayAmountStart != null">
        and bse.supplier_amount_total <![CDATA[>=]]> #{shouldPayAmountStart}
      </if>
      <if test="shouldPayAmountEnd != '' and shouldPayAmountEnd != null">
        and bse.supplier_amount_total <![CDATA[<=]]>#{shouldPayAmountEnd}
      </if>
      <if test="orderNo != '' and orderNo != null">
        and bsed.order_no = #{orderNo}
      </if>
    </where>
    order by (case when bse.expenditure_status=2 THEN 1 when bse.expenditure_status=3 THEN 2 when bse.expenditure_status=5 THEN 3
    else 4 END),bse.update_time desc
  </select>
    <select id="selectDetailByExpenditureNo" resultType="com.izu.mrcar.order.dto.provider.output.SupplierSpendDetailOutputDTO">
      select bse.supplier_name as supplierName,bse.supplier_amount_total as shouldPayAmountTotal,bse.should_pay_total_amount as customerBillTotal,
             bse.suppler_invoice_type as supplierBillType,bse.supplier_amount_total_no_rate as shouldPayAmountTotalNoTax,
             bse.should_pay_total_amount_no_rate as customerBillTotalNoTax,CONCAT(bse.creator_name,' ',bse.creator_phone) as createName,
             bse.supplier_rate as supplierTaxPoint,bse.return_rate as incomeRate,bse.creator_dept_name as createDepartment,
             bse.vehicle_user_name as useVehicleName,bse.approval_suggestion as approvalComments,bse.expenditure_status as expenditureStatus,
             bse.supplier_code as supplierCode,bse.vehicle_user_code as vehicleUserCode,bse.item_description as itemDescription,
             bse.creator_phone  as creatorPhone,bse.creator_id as creatorId,IFNULL(sum(bo.refund_amount_synthesis),0) AS returnAmountTotal
      from business_supplier_expenditure bse
             LEFT JOIN business_supplier_expenditure_detail bsed ON bse.expenditure_no = bsed.expenditure_no
             LEFT JOIN bill_order bo ON bsed.order_no = bo.order_no
      where bse.expenditure_no = #{expenditureNo}
      group by bse.expenditure_no
    </select>

  <update id="updateByExpenditureNo" parameterType="com.izu.mrcar.order.dto.provider.input.UpdateExpenditureStatusInputDTO">
    update business_supplier_expenditure
    <set>
      <if test="operationType != null">
        expenditure_status = #{operationType},
      </if>
      <if test="approvalSuggestion != null">
        approval_suggestion = #{approvalSuggestion},
      </if>
      <if test="loginUserId != null">
        update_id = #{loginUserId},
      </if>
      <if test="loginUserName != null">
        update_name = #{loginUserName},
      </if>
      <if test="loginUserMobile != null">
        update_phone = #{loginUserMobile},
      </if>
      <if test="loginDeptCode != null">
        update_dept_code = #{loginDeptCode},
      </if>
      <if test="structName != null">
        update_dept_name = #{loginDeptName},
      </if>
      <if test="pushResult != null">
        push_result = #{pushResult},
      </if>
    </set>
    where expenditure_no = #{expenditureNo}
  </update>
  <update id="updateAllByExpenditureNo" parameterType="com.izu.order.entity.mrcar.BusinessSupplierExpenditure">
    update business_supplier_expenditure
    <set>
      <if test="supplierCode != null">
        supplier_code = #{supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        supplier_name = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="shouldPayTotalAmount != null">
        should_pay_total_amount = #{shouldPayTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="shouldPayTotalAmountNoRate != null">
        should_pay_total_amount_no_rate = #{shouldPayTotalAmountNoRate,jdbcType=DECIMAL},
      </if>
      <if test="supplierAmountTotal != null">
        supplier_amount_total = #{supplierAmountTotal,jdbcType=DECIMAL},
      </if>
      <if test="supplierAmountTotalNoRate != null">
        supplier_amount_total_no_rate = #{supplierAmountTotalNoRate,jdbcType=DECIMAL},
      </if>
      <if test="supplierRate != null">
        supplier_rate = #{supplierRate,jdbcType=DECIMAL},
      </if>
      <if test="orderCount != null">
        order_count = #{orderCount,jdbcType=INTEGER},
      </if>
      <if test="supplerInvoiceType != null">
        suppler_invoice_type = #{supplerInvoiceType,jdbcType=INTEGER},
      </if>
      <if test="expenditureStatus != null">
        expenditure_status = #{expenditureStatus,jdbcType=TINYINT},
      </if>
      <if test="returnRate != null">
        return_rate = #{returnRate,jdbcType=DECIMAL},
      </if>
      <if test="vehicleUserCode != null">
        vehicle_user_code = #{vehicleUserCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleUserName != null">
        vehicle_user_name = #{vehicleUserName,jdbcType=VARCHAR},
      </if>
      <if test="itemDescription != null">
        item_description = #{itemDescription,jdbcType=VARCHAR},
      </if>
      <if test="approvalSuggestion != null">
        approval_suggestion = #{approvalSuggestion,jdbcType=VARCHAR},
      </if>
      <if test="updateId != null">
        update_id = #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updatePhone != null">
        update_phone = #{updatePhone,jdbcType=VARCHAR},
      </if>
      <if test="updateDeptCode != null">
        update_dept_code = #{updateDeptCode,jdbcType=VARCHAR},
      </if>
      <if test="updateDeptName != null">
        update_dept_name = #{updateDeptName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where expenditure_no = #{expenditureNo}
  </update>
  <update id="updateRateByExpenditureNo" parameterType="com.izu.order.entity.mrcar.BusinessSupplierExpenditure">
    update business_supplier_expenditure
    set supplier_rate = #{supplierRate}
    where expenditure_no = #{expenditureNo}
  </update>

</mapper>