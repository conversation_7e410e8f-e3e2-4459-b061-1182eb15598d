<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.ThirdApplyRecordMapper">
    <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.ThirdApplyRecord">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="receipt_no" jdbcType="VARCHAR" property="receiptNo"/>
        <result column="order_status" jdbcType="INTEGER" property="orderStatus"/>
        <result column="request_status" jdbcType="INTEGER" property="requestStatus"/>
        <result column="request_type" jdbcType="INTEGER" property="requestType"/>
        <result column="content_type" jdbcType="INTEGER" property="contentType"/>
        <result column="handle_status" jdbcType="INTEGER" property="handleStatus"/>
        <result column="request_count" jdbcType="INTEGER" property="requestCount"/>
        <result column="interface_code" jdbcType="VARCHAR" property="interfaceCode"/>
        <result column="request_url" jdbcType="VARCHAR" property="requestUrl"/>
        <result column="request_params" jdbcType="VARCHAR" property="requestParams"/>
        <result column="company_code" jdbcType="VARCHAR" property="companyCode"/>
        <result column="company_name" jdbcType="VARCHAR" property="companyName"/>
        <result column="apply_time" jdbcType="TIMESTAMP" property="applyTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        , order_no, receipt_no, order_status, request_status,request_type,content_type,request_count,handle_status,interface_code,request_url,request_params,company_code,company_name,apply_time
    </sql>


    <select id="getRetryRecord" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        third_apply_record
        where
        handle_status = 1 and request_count &lt; #{requestCount}
    </select>


    <select id="selectPageList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from third_apply_record
        where 1=1
        <if test="orderNo != null and orderNo != ''">
            AND order_no = #{orderNo}
        </if>
        <if test="orderStatus != null">
            AND order_status = #{orderStatus}
        </if>
        <if test="companyCode != null and companyCode != ''">
            AND company_code = #{companyCode}
        </if>
        <if test="interfaceCode != null and interfaceCode != ''">
            AND interface_code = #{interfaceCode}
        </if>
        <if test="requestStatus != null">
            AND request_status = #{requestStatus}
        </if>
        <if test="handleStatus != null">
            AND handle_status = #{handleStatus}
        </if>
        <if test="startTime != null and startTime!=''">
            and apply_time <![CDATA[>=]]> #{startTime}
        </if>
        <if test="endTime != null and endTime!='' ">
            and apply_time <![CDATA[<=]]> #{endTime}
        </if>
        order by apply_time desc
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from third_apply_record
        where id = #{id,jdbcType=INTEGER}
    </select>

    <select id="selectByReceiptNoAndInterface" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from third_apply_record
        where receipt_no = #{receiptNo,jdbcType=VARCHAR} and interface_code = #{interfaceCode,jdbcType=INTEGER} and
        handle_status = 1 and request_count &lt; #{requestCount}
    </select>

    <update id="updateHandleStatusById" parameterType="java.lang.Integer">
        update third_apply_record
        set handle_status = 2
        where id = #{id,jdbcType=INTEGER}
    </update>


    <insert id="insert" parameterType="com.izu.order.entity.mrcar.ThirdApplyRecord">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into third_apply_record (order_no, receipt_no,
        order_status,request_url,request_params,request_type,content_type,company_code,company_name,apply_time,
        request_status, handle_status, request_count,interface_code)
        values (#{orderNo,jdbcType=VARCHAR}, #{receiptNo,jdbcType=VARCHAR}, #{orderStatus,jdbcType=INTEGER},
        #{requestUrl,jdbcType=VARCHAR},#{requestParams,jdbcType=VARCHAR},#{requestType,jdbcType=INTEGER},#{contentType,jdbcType=INTEGER},
        #{companyCode,jdbcType=VARCHAR},#{companyName,jdbcType=VARCHAR},#{applyTime,jdbcType=TIMESTAMP},#{requestStatus,jdbcType=INTEGER},
        #{handleStatus,jdbcType=INTEGER},#{requestCount,jdbcType=INTEGER},#{interfaceCode,jdbcType=VARCHAR})
    </insert>


    <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.ThirdApplyRecord">
        update third_apply_record
        <set>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="receiptNo != null">
                receipt_no = #{receiptNo,jdbcType=VARCHAR},
            </if>
            <if test="orderStatus != null">
                order_status = #{orderStatus,jdbcType=INTEGER},
            </if>
            <if test="requestUrl != null">
                request_url = #{requestUrl,jdbcType=VARCHAR},
            </if>
            <if test="requestParams != null">
                request_params = #{requestParams,jdbcType=VARCHAR},
            </if>
            <if test="requestType != null">
                request_type = #{requestType,jdbcType=INTEGER},
            </if>
            <if test="contentType != null">
                content_type = #{contentType,jdbcType=INTEGER},
            </if>
            <if test="companyCode != null">
                company_code = #{companyCode,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null">
                company_name = #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="requestStatus != null">
                request_status = #{requestStatus,jdbcType=INTEGER},
            </if>
            <if test="handleStatus != null">
                handle_status = #{handleStatus,jdbcType=INTEGER},
            </if>
            <if test="requestCount != null">
                request_count = #{requestCount,jdbcType=INTEGER},
            </if>
            <if test="interfaceCode != null">
                interface_code = #{interfaceCode,jdbcType=INTEGER},
            </if>
            <if test="applyTime != null">
                apply_time = #{applyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

</mapper>