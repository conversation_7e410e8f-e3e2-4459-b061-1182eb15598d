<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.SequenceExMapper">
  <!-- 计数器累加1 -->
  <update id="increaseCounter">
	update sequence set current_seq_number=current_seq_number+1, update_version=#{updateVersion,jdbcType=VARCHAR}
	where sequence_date=#{sequenceDate,jdbcType=DATE} and sequence_type=#{sequenceType,jdbcType=VARCHAR}
  </update>
  
  <!-- 插入: 忽略唯一主键 -->
  <insert id="insertIgnoreSelective" parameterType="com.izu.order.entity.mrcar.Sequence">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert IGNORE into sequence
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="sequenceDate != null">
        sequence_date,
      </if>
      <if test="sequenceType != null">
        sequence_type,
      </if>
      <if test="currentSeqNumber != null">
        current_seq_number,
      </if>
      <if test="updateVersion != null">
        update_version,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="sequenceDate != null">
        #{sequenceDate,jdbcType=DATE},
      </if>
      <if test="sequenceType != null">
        #{sequenceType,jdbcType=VARCHAR},
      </if>
      <if test="currentSeqNumber != null">
        #{currentSeqNumber,jdbcType=INTEGER},
      </if>
      <if test="updateVersion != null">
        #{updateVersion,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <!-- 查询序列号 -->
  <select id="selectSequence" resultMap="mapper.mrcar.SequenceMapper.BaseResultMap">
    select 
    <include refid="mapper.mrcar.SequenceMapper.Base_Column_List" />
    from sequence
	where sequence_date=#{sequenceDate,jdbcType=DATE} and sequence_type=#{sequenceType,jdbcType=VARCHAR}
	limit 1
  </select>

</mapper>