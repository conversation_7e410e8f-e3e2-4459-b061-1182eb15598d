<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.GovCarIncomePushProcessExMapper">

    <update id="updatePushStatus">
        update gov_car_income_push_process
        set push_status = #{pushStatus},
            actual_push_time = now(),
            push_count = push_count + 1
        where push_id = #{pushId}
    </update>

    <select id="fetchPendingAndFailedPushRecords" resultType="com.izu.order.entity.mrcar.GovCarIncomePushProcess">
        select
            push_id as pushId,
            order_no as orderNo,
            push_data as pushData,
            bill_type as billType
        from gov_car_income_push_process
        where
            (push_status = 0 and push_count <![CDATA[ <= ]]> #{maxRetries}) -- 待推送的
           or
            (push_status = 2 and push_count <![CDATA[ <= ]]> #{maxRetries}) -- 推送失败但仍可重试的
        order by expect_push_time asc
    </select>

    <select id="queryOrderBill" resultType="com.izu.mrcar.order.dto.govcar.incomepush.GovCarOrderBillInfoDTO">
        SELECT
            b.bill_no AS billNo,
            b.order_no AS orderNo,
            b.bill_name AS billName,
            b.bill_type AS billType,
            b.payed_time AS payedTime,
            bd.price_code AS priceCode,
            bd.price_name AS priceName,
            SUM(bd.total_amount) AS totalAmount,  -- 计算总收入（包括支付和退款后的结果）
            bd.detail_type AS detailType,
            s.order_time AS orderTime,
            s.vehicle_serial_no AS vehicleSerialNo,
            s.vehicle_license AS vehicleLicense,
            s.operate_buss_code AS operateBussCode,
            s.operate_buss_name AS operateBussName,
            s.belong_buss_code AS belongBussCode,
            s.belong_buss_name AS belongBussName,
            s.admin_store_code AS adminStoreCode,
            s.admin_store_name AS adminStoreName,
            s.belong_store_code AS belongStoreCode,
            s.belong_store_name AS belongStoreName,
            s.customer_id AS customerId,
            s.customer_name AS customerName,
            s.customer_mobile AS customerMobile,
            s.lease_purpose as leasePurpose
        FROM
            gov_car_order_bill b
                INNER JOIN
            gov_car_order_bill_detail bd
            ON b.bill_no = bd.bill_no
                INNER JOIN
            gov_car_order_data_snapshot s
            ON b.order_no = s.order_no
        WHERE
            b.bill_valid = 1
            <if test="billNo != null and billNo != ''">
                and b.bill_no = #{billNo}
            </if>
          and b.bill_type = #{billType}
          and b.order_no = #{orderNo}
          and b.push_bill_income = 1
        GROUP BY
            bd.price_code, bd.price_name  -- 按照费用项名称分组
        ORDER BY
            b.order_no, bd.price_name
    </select>

</mapper>
