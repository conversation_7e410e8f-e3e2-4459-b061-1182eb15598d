<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.TemporalSharedVehicleRecordExMapper">
  <resultMap id="BaseResultMap" type="com.izu.mrcar.order.dto.temporalSharedVehicle.TemporalSharedVehicleRecordDTO" extends="mapper.mrcar.TemporalSharedVehicleRecordMapper.BaseResultMap">
    <result column="duration1" jdbcType="INTEGER" property="duration" />
  </resultMap>
  <sql id="Base_Column_List" >
    <include refid="mapper.mrcar.TemporalSharedVehicleRecordMapper.Base_Column_List"/>
  </sql>

  <select id="queryPageProvider" resultMap="BaseResultMap">
    select
    a.*,IF(a.record_status =1, TIMESTAMPDIFF(MINUTE, a.warn_start_time, NOW()),a.duration) duration1
    from t_temporal_shared_vehicle_record a,order_vehicle_base b
    <where>
      a.vehicle_license = b.vehicle_license and b.vehicle_status = 1
      <if test="warnSn != null and warnSn != ''">
        and warn_sn = #{warnSn}
      </if>
      <if test="vehicleCompanyId != null">
        and vehicle_company_id = #{vehicleCompanyId}
      </if>
      <if test="vehicleStructId != null">
        and vehicle_struct_id = #{vehicleStructId}
      </if>
      <if test="vehicleVin != null and vehicleVin != ''">
        and a.vehicle_vin = #{vehicleVin}
      </if>
      <if test="vehicleLicense != null and vehicleLicense != ''">
        and a.vehicle_license like CONCAT('%', #{vehicleLicense},'%')
      </if>
      <if test="recordStatuses != null and recordStatuses.size() > 0">
        and record_status in
        <foreach collection="recordStatuses" item="item" separator="," open="(" close=")">
          #{item}
        </foreach>
      </if>
      <if test="orderNo != null and orderNo != ''">
        and order_no = #{orderNo}
      </if>
      <if test="orderCustomerName != null and orderCustomerName != ''">
        and order_customer_name = #{orderCustomerName}
      </if>
      <if test="orderStructId != null">
        and order_struct_id = #{orderStructId}
      </if>
      <if test="orderAssociateStatus != null">
        and order_associate_status = #{orderAssociateStatus}
      </if>
      <if test="warnStartBeginTime != null">
        and warn_start_time <![CDATA[>=]]> #{warnStartBeginTime}
      </if>
      <if test="warnStartEndTime != null">
        and warn_start_time <![CDATA[<=]]> #{warnStartEndTime}
      </if>
      <if test="durationBegin != null">
        and ( ( duration_hour <![CDATA[>=]]> #{durationBegin} and record_status in (2,3) )
        or ( warn_start_time <![CDATA[<=]]> #{durationBeginMaxTime} and record_status = 1 ) )
      </if>
      <if test="durationEnd != null">
        and (( duration_hour <![CDATA[<=]]> #{durationEnd} and record_status in (2,3) )
        or ( warn_start_time <![CDATA[>=]]> #{durationBeginMinTime} and record_status = 1 ) )
      </if>

      <if test="dataPermIsNotNull">
        <choose>
          <when test="systemType == 1">
            <choose>
              <when test="dataPermType == 1 or dataPermType == 2 or dataPermType == 3 or dataPermType == 4  or dataPermType == 5 or dataPermType == 7">
                and (b.operate_struct_code in
                <foreach collection="dataCodeSet" item="item" separator="," open="(" close=")">
                  #{item}
                </foreach>
                or b.belong_struct_code in
                <foreach collection="dataCodeSet" item="item" separator="," open="(" close=")">
                  #{item}
                </foreach>
                )
              </when>
              <when test="dataPermType == 6">
                and 1 = 1
              </when>
              <otherwise>
                and 1 != 1
              </otherwise>
            </choose>
          </when>
          <otherwise>
            and 1 != 1
          </otherwise>
        </choose>
      </if>

      <if test="sortField != null">
        <if test="sortField == 1 ">
          <if test="sortFieldToAsc == null or sortFieldToAsc == false">
            order by warn_start_time desc
          </if>
          <if test="sortFieldToAsc == true">
            order by warn_start_time asc
          </if>
        </if>
        <if test="sortField == 2 ">
          <if test="sortFieldToAsc == null or sortFieldToAsc == false">
            order by duration1 desc
          </if>
          <if test="sortFieldToAsc == true">
            order by duration1 asc
          </if>
        </if>
        <if test="sortField == 3 ">
          <if test="sortFieldToAsc == null or sortFieldToAsc == false">
            order by trip_mileage desc
          </if>
          <if test="sortFieldToAsc == true">
            order by trip_mileage asc
          </if>
        </if>
      </if>
      <if test="sortField == null">
        order by warn_start_time desc
      </if>
    </where>
  </select>

  <select id="queryPage" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>,IF(record_status =1, TIMESTAMPDIFF(MINUTE, warn_start_time, NOW()),duration) duration1
    from t_temporal_shared_vehicle_record
    <where>
      <if test="warnSn != null and warnSn != ''">
        and warn_sn = #{warnSn}
      </if>
      <if test="vehicleCompanyId != null">
        and vehicle_company_id = #{vehicleCompanyId}
      </if>
      <if test="vehicleStructId != null">
        and vehicle_struct_id = #{vehicleStructId}
      </if>
      <if test="vehicleVin != null and vehicleVin != ''">
        and vehicle_vin = #{vehicleVin}
      </if>
      <if test="vehicleLicense != null and vehicleLicense != ''">
        and vehicle_license like CONCAT('%', #{vehicleLicense},'%')
      </if>
      <if test="recordStatuses != null and recordStatuses.size() > 0">
        and record_status in
        <foreach collection="recordStatuses" item="item" separator="," open="(" close=")">
          #{item}
        </foreach>
      </if>
      <if test="orderNo != null and orderNo != ''">
        and order_no = #{orderNo}
      </if>
      <if test="orderCustomerName != null and orderCustomerName != ''">
        and order_customer_name = #{orderCustomerName}
      </if>
      <if test="orderStructId != null">
        and order_struct_id = #{orderStructId}
      </if>
      <if test="orderAssociateStatus != null">
        and order_associate_status = #{orderAssociateStatus}
      </if>
      <if test="warnStartBeginTime != null">
        and warn_start_time <![CDATA[>=]]> #{warnStartBeginTime}
      </if>
      <if test="warnStartEndTime != null">
        and warn_start_time <![CDATA[<=]]> #{warnStartEndTime}
      </if>
      <if test="durationBegin != null">
        and ( ( duration_hour <![CDATA[>=]]> #{durationBegin} and record_status in (2,3) )
        or ( warn_start_time <![CDATA[<=]]> #{durationBeginMaxTime} and record_status = 1 ) )
      </if>
      <if test="durationEnd != null">
        and (( duration_hour <![CDATA[<=]]> #{durationEnd} and record_status in (2,3) )
        or ( warn_start_time <![CDATA[>=]]> #{durationBeginMinTime} and record_status = 1 ) )
      </if>
      and (vehicle_company_id = #{companyId}
      <if test="dataPermIsNotNull and systemType == 2 and dataPermType == 1">
        or vehicle_company_id in
        <foreach collection="dataCodeSet" item="item" separator="," open="(" close=")">
          #{item}
        </foreach>
      </if>
      )
      <if test="dataPermIsNotNull">
        <choose>
          <when test="systemType == 2">
            and record_status in (1,2)
            <choose>
              <when test="dataPermType == 1">
              </when>
              <when test="dataPermType == 2">
                and (vehicle_struct_id in
                <foreach collection="dataCodeSet" item="item" separator="," open="(" close=")">
                  #{item}
                </foreach>
                or vehicle_creator_id = #{staffId})
              </when>
              <when test="dataPermType == 3">
                and (vehicle_belong_city_code in
                <foreach collection="dataCodeSet" item="item" separator="," open="(" close=")">
                  #{item}
                </foreach>
                or vehicle_creator_id = #{staffId})
              </when>
              <when test="dataPermType == 4">
                and vehicle_creator_id = #{staffId}
              </when>
              <otherwise>
                and 1 != 1
              </otherwise>
            </choose>
          </when>
          <otherwise>
            and 1 != 1
          </otherwise>
        </choose>
      </if>

      <if test="sortField != null">
        <if test="sortField == 1 ">
          <if test="sortFieldToAsc == null or sortFieldToAsc == false">
            order by warn_start_time desc
          </if>
          <if test="sortFieldToAsc == true">
            order by warn_start_time asc
          </if>
        </if>
        <if test="sortField == 2 ">
          <if test="sortFieldToAsc == null or sortFieldToAsc == false">
            order by duration1 desc
          </if>
          <if test="sortFieldToAsc == true">
            order by duration1 asc
          </if>
        </if>
        <if test="sortField == 3 ">
          <if test="sortFieldToAsc == null or sortFieldToAsc == false">
            order by trip_mileage desc
          </if>
          <if test="sortFieldToAsc == true">
            order by trip_mileage asc
          </if>
        </if>
      </if>
      <if test="sortField == null">
        order by warn_start_time desc
      </if>
    </where>
  </select>

  <update id="inValid">
    update t_temporal_shared_vehicle_record
    set record_status = 3,
    update_user_id = #{staffId},
    update_user_name = #{staffName}
    <if test="updateRemark != null and updateRemark != ''">
      , update_remark = #{updateRemark}
    </if>
    where id = #{id}
  </update>
  
  <select id="findOneByWarnSn" resultMap="mapper.mrcar.TemporalSharedVehicleRecordMapper.BaseResultMap">
    select * from t_temporal_shared_vehicle_record where warn_sn = #{warnSn}
  </select>

</mapper>