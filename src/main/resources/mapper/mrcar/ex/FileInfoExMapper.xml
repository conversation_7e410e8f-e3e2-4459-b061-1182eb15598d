<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.FileInfoExMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.FileInfo" extends="mapper.mrcar.FileInfoMapper.BaseResultMap"/>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into file_info (file_type, relation_code, file_name,
    file_url, valid, create_time)
    values
    <foreach collection="list" item="fileInfo" index="index" separator=",">
      (
      #{fileInfo.fileType},
      #{fileInfo.relationCode},
      #{fileInfo.fileName},
      #{fileInfo.fileUrl},
      #{fileInfo.valid},
      #{fileInfo.createTime}
      )
    </foreach>
  </insert>
</mapper>