<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.BillExMapper">

    <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.Bill" extends="mapper.mrcar.BillMapper.BaseResultMap"/>

    <select id="selectByPrimaryKey" resultMap="mapper.mrcar.BillMapper.BaseResultMap">
        select
        <include refid="mapper.mrcar.BillMapper.Base_Column_List"/>
        from bill
        where order_apply_no = #{orderApplyNo}
    </select>

    <select id="selectByOrderApplyNo" resultMap="mapper.mrcar.BillMapper.BaseResultMap">
        select
        <include refid="mapper.mrcar.BillMapper.Base_Column_List"/>
        from bill
        where order_apply_no = #{orderApplyNo}
    </select>

    <update id="updateBillByBillOrder" parameterType="com.izu.order.entity.mrcar.BillOrder">
        update bill
        set total_amount     = (total_amount + #{totalAmount}),
            shouldpay_amount = (shouldpay_amount + #{shouldpayAmount})
        where order_apply_no = #{orderApplyNo}
    </update>

    <update id="updateBillDiscount" parameterType="com.izu.order.entity.mrcar.BillOrder">
        update bill
        set shouldpay_amount = 0,
            coupon_deduct_amount = (coupon_deduct_amount + #{totalAmount}  )
        where order_apply_no = #{orderApplyNo}
    </update>

    <update id="addAttachFee" parameterType="com.izu.order.entity.mrcar.Bill">
        update bill
        set total_amount = (total_amount + #{totalAmount}),
        shouldpay_amount = (shouldpay_amount+#{shouldpayAmount}),
        coupon_deduct_amount = (coupon_deduct_amount + #{couponDeductAmount} )
        where order_apply_no = #{orderApplyNo}
    </update>

    <update id="updateAttachFee">
        update bill
        set total_amount     = (<![CDATA[total_amount]]> + #{addFee}  ),
            shouldpay_amount = (<![CDATA[shouldpay_amount]]> + #{addFee}  )
        where order_apply_no = #{orderApplyNo}
    </update>

    <update id="updateAttachFeeWithCoupon">
        update bill
        set total_amount = (total_amount + #{addFee}),
            shouldpay_amount = 0,
            coupon_deduct_amount = (coupon_deduct_amount + #{addFee} )
        where order_apply_no = #{orderApplyNo}
    </update>

    <update id="readjustBill" parameterType="com.izu.order.entity.mrcar.Bill">
        update bill
        set shouldpay_amount = (shouldpay_amount - #{reductionAmount} + #{otherAmount}),
            other_amount = (#{otherAmount} + other_amount),
            reduction_amount = (#{reductionAmount} + reduction_amount),
            price_explain = #{priceExplain},
            adjust_price_user_id = #{adjustPriceUserId},
            adjust_price_user_name = #{adjustPriceUserName},
            adjust_price_time = #{adjustPriceTime}
        where id = #{id} and (shouldpay_amount - #{reductionAmount} + #{otherAmount}) >= 0
    </update>

    <update id="updateBillDiscountWithFixPrice">
        update bill
        set coupon_deduct_amount = fixed_price
        where id = #{id}
    </update>

    <update id="updateBillShouldPayAmountWithFixPrice">
        update bill
        set shouldpay_amount = fixed_price
        where id = #{id}
    </update>

    <select id="isSetFixPrice" resultType="boolean">
        select
            fixed_price_valid
        from bill
        WHERE order_apply_no = #{orderApplyNo}
    </select>

    <update id="updateA">
        update bill
          set coupon_deduct_amount = fixed_price
        where order_apply_no = #{orderApplyNo}
    </update>

    <update id="updateB">
        update bill
        set shouldpay_amount = fixed_price
        where order_apply_no = #{orderApplyNo}
    </update>

    <update id="abatementFixedPriceForUnFinishOrder">
        update bill
        set fixed_price = (fixed_price - #{fixedPrice})
        where order_apply_no = #{orderApplyNo}
    </update>
    <update id="abatementFixedPriceForCoupon">
        update bill
        set fixed_price = (fixed_price - #{fixedPrice}),
        coupon_deduct_amount = fixed_price
        where order_apply_no = #{orderApplyNo}
    </update>
    <update id="abatementFixedPriceForUnCoupon">
        update bill
        set fixed_price = (fixed_price - #{fixedPrice}),
        shouldpay_amount = fixed_price
        where order_apply_no = #{orderApplyNo}
    </update>

    <select id="getSettledBills" resultMap="BaseResultMap">
        SELECT
        DISTINCT bill.*
        FROM
        bill
        INNER JOIN order_info AS info ON bill.order_apply_no = info.order_apply_no
        INNER JOIN order_apply AS apply ON bill.order_apply_no = apply.order_apply_no
        AND info.order_type = 2
        AND apply.order_status = 90
        <if test="cityCode !=null">
            AND info.assign_driver_city_code = #{cityCode}
        </if>
        <if test="statsStartTime != null and statsStartTime!=''">
            and bill.update_time &gt;= #{statsStartTime}
        </if>
        <if test="statsEndTime!=null and statsEndTime!=''">
            and bill.update_time &lt;= #{statsEndTime}
        </if>
    </select>

    <update id="reduceAttachFee" parameterType="com.izu.order.entity.mrcar.Bill">
        update bill
        set total_amount = (total_amount - #{totalAmount}),
            shouldpay_amount = (shouldpay_amount - #{shouldpayAmount}),
            reduction_amount = (reduction_amount - #{reductionAmount}),
            coupon_deduct_amount = (coupon_deduct_amount - #{couponDeductAmount}),
            fixed_price = (fixed_price - #{fixedPrice})
        where order_apply_no = #{orderApplyNo}
    </update>
    <update id="clearAmount" parameterType="com.izu.order.entity.mrcar.Bill">
        update bill
        set total_amount = #{totalAmount},
            shouldpay_amount = #{shouldpayAmount},
            reduction_amount = #{reductionAmount},
            coupon_deduct_amount = #{couponDeductAmount},
            fixed_price = #{fixedPrice}
        where order_apply_no = #{orderApplyNo}
    </update>
</mapper>