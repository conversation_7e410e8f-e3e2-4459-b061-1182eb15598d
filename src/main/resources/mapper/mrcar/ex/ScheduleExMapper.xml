<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.ScheduleExMapper">
    <resultMap id="BaseResultMap" type="com.izu.mrcar.order.dto.dispatching.ScheduleDTO" extends="mapper.mrcar.ScheduleMapper.BaseResultMap">
        <result column="order_status" jdbcType="TINYINT" property="orderStatus" />
        <result column="passenger_struct_name" jdbcType="VARCHAR" property="passengerStructName" />
    </resultMap>
    <resultMap id="ProviderBaseResultMap" type="com.izu.mrcar.order.dto.provider.output.ScheduleListDTO" extends="mapper.mrcar.ScheduleMapper.BaseResultMap">
        <result column="order_status" jdbcType="TINYINT" property="orderStatus" />
        <result column="passenger_struct_name" jdbcType="VARCHAR" property="passengerStructName" />
    </resultMap>
    <resultMap id="AppBaseResultMap" type="com.izu.mrcar.order.dto.dispatching.AppScheduleListDTO" extends="mapper.mrcar.ScheduleMapper.BaseResultMap">
        <result column="passenger_struct_name" jdbcType="VARCHAR" property="passengerStructName" />
    </resultMap>
    <sql id="Base_Column_List">
        <include refid="mapper.mrcar.ScheduleMapper.Base_Column_List"/>
    </sql>
    <select id="selectScheduleListByParam" parameterType="com.izu.mrcar.order.dto.dispatching.ScheduleListReqDTO" resultMap="BaseResultMap">
        select
        schedule_id, s.order_apply_no, schedule_status, dispatcher_status, schedule_time, s.create_time, s.booking_order_stime, s.booking_order_etime,
        s.booking_start_short_addr, s.booking_end_short_addr, s.booking_vehicle_total_count, s.order_type, s.customer_name, s.customer_mobile, s.service_code,
        s.passenger_name,s.passenger_phone,s.passenger_struct_name,s.supplier_flag
        from schedule s
        <where>
            <if test="permissionList != null and permissionList.size() > 0" >
                AND (
                s.booking_start_city_code in
                <foreach collection="permissionList" open="(" close=")" separator="," item="cityCode">
                    #{cityCode}
                </foreach>
                or
                s.booking_end_city_code in
                <foreach collection="permissionList" open="(" close=")" separator="," item="cityCode">
                    #{cityCode}
                </foreach>
                )
            </if>
            <if test="internalSupplierFlag ==0">
                <if test="dataPermCodeSet != null and dataPermCodeSet.size() > 0" >
                    <!--指定部门 -->
                    <if test="dataPermType!=null and dataPermType ==2">
                        and (s.struct_id in
                        <foreach item="item" collection="dataPermCodeSet" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        or s.passenger_struct_id IN
                        <foreach item="item" collection="dataPermCodeSet" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        or s.customer_id = #{loginId}
                        )
                    </if>
                    <!--指定城市 -->
                    <if test="dataPermType!=null and dataPermType ==3">
                        AND (
                        s.booking_start_city_code in
                        <foreach collection="dataPermCodeSet" open="(" close=")" separator="," item="cityCode">
                            #{cityCode}
                        </foreach>
                        or
                        s.booking_end_city_code in
                        <foreach collection="dataPermCodeSet" open="(" close=")" separator="," item="cityCode">
                            #{cityCode}
                        </foreach>
                        or s.customer_id = #{loginId}
                        )
                    </if>
                </if>
                <!--本人权限 -->
                <if test="dataPermType!=null and dataPermType ==4">
                    and (s.customer_id = #{loginId})
                </if>
                <if test="companyId!=null ">
                    AND s.company_id=#{companyId}
                </if>
            </if>
            <if test="internalSupplierFlag==1">
                and s.supplier_id =#{companyId} and s.order_type=1
            </if>
            <if test="orderStime!=null and orderStime!=''">
                AND s.create_time <![CDATA[>=]]> #{orderStime}
            </if>
            <if test="orderEtime!=null and orderEtime!=''">
                AND s.create_time <![CDATA[<=]]> #{orderEtime}
            </if>
            <if test="bookingOrderStime!=null and bookingOrderStime!=''">
                AND s.booking_order_stime <![CDATA[>=]]> #{bookingOrderStime}
            </if>
            <if test="bookingOrderEtime!=null and bookingOrderEtime!=''">
                AND s.booking_order_stime <![CDATA[<=]]> #{bookingOrderEtime}
            </if>
            <if test="bookingEstimateStime!=null and bookingEstimateStime!=''">
                AND s.booking_order_etime <![CDATA[>=]]> #{bookingEstimateStime}
            </if>
            <if test="bookingEstimateEtime!=null and bookingEstimateEtime!=''">
                AND s.booking_order_etime <![CDATA[<=]]> #{bookingEstimateEtime}
            </if>
            <if test="passengerName!=null and passengerName!=''">
                AND s.passenger_name = #{passengerName}
            </if>
            <if test="passengerPhone!=null and passengerPhone!=''">
                AND s.passenger_phone = #{passengerPhone}
            </if>
            <if test="customerId!=null ">
                AND s.customer_id=#{customerId}
            </if>
            <if test="dispatcherStatus!=null and dispatcherStatus!=0">
                AND s.dispatcher_status=#{dispatcherStatus}
            </if>
            <if test="scheduleStatus!=null and scheduleStatus!=0">
                <choose>
                    <when test="scheduleStatus==1">
                        AND s.schedule_status IN(1,5,6)
                    </when>
                    <when test="scheduleStatus==3" >
                        and EXISTS (
                        SELECT 1
                        FROM order_apply oa
                        WHERE
                        oa.order_apply_no = s.order_apply_no
                        and
                        oa.order_status in(30,40,90,100)
                        )
                    </when>
                    <when test="scheduleStatus==2" >
                        and EXISTS (
                        SELECT 1
                        FROM order_apply oa
                        WHERE
                        oa.order_apply_no = s.order_apply_no
                        and
                        oa.order_status =20
                        )
                    </when>
                    <otherwise>
                        AND s.schedule_status= #{scheduleStatus}
                    </otherwise>
                </choose>
            </if>
            <if test="orderType!=null and orderType!=0">
                AND s.order_type=#{orderType}
            </if>
<!--            <if test="serviceCode!=null and serviceCode!=0">
                AND s.service_code=#{serviceCode}
            </if>-->
            <if test="serviceCodeStr != null and serviceCodeStr !=''">
                and s.service_code in
                <foreach item="item" collection="serviceCodeStr.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="orderApplyNo!=null and orderApplyNo != ''">
                AND s.order_apply_no=#{orderApplyNo}
            </if>
            <if test="passengerStructId!=null">
                AND s.passenger_struct_id=#{passengerStructId}
            </if>
        </where>
        <if test="bookingOrderTimeIsAsc!=null">
            <if test="bookingOrderTimeIsAsc == true">
                ORDER BY booking_order_stime ASC
            </if>
            <if test="bookingOrderTimeIsAsc == false">
                ORDER BY booking_order_stime DESC
            </if>
        </if>
        <if test="bookingOrderETimeIsAsc!=null">
            <if test="bookingOrderETimeIsAsc == true">
                ORDER BY booking_order_etime ASC
            </if>
            <if test="bookingOrderETimeIsAsc == false">
                ORDER BY booking_order_etime DESC
            </if>
        </if>
        <if test="createTimeIsAsc!=null">
            <if test="createTimeIsAsc == true">
                ORDER BY create_time ASC
            </if>
            <if test="createTimeIsAsc == false">
                ORDER BY create_time DESC
            </if>
        </if>
        <if test="bookingOrderTimeIsAsc == null and createTimeIsAsc == null and bookingOrderETimeIsAsc == null">
            ORDER BY schedule_status asc,booking_order_stime DESC
        </if>
    </select>

    <select id="selectScheduleProviderListByParam" parameterType="com.izu.mrcar.order.dto.provider.input.ScheduleListInputDTO" resultMap="ProviderBaseResultMap">
        select
        schedule_id, s.order_apply_no, schedule_status, dispatcher_status, schedule_time, s.create_time, s.booking_order_stime, s.booking_order_etime,
        s.booking_start_short_addr, s.booking_end_short_addr, s.booking_vehicle_total_count, s.order_type, s.customer_name, s.customer_mobile, s.service_code,
        s.passenger_name,s.passenger_phone,s.passenger_struct_name
        from schedule s
        left join order_apply oa on s.order_apply_no=oa.order_apply_no
        left join order_info o on s.order_apply_no=o.order_apply_no
        <where>
            <if test="orderStime!=null and orderStime!=''">
                s.create_time <![CDATA[>=]]> #{orderStime}
            </if>
            <if test="orderEtime!=null and orderEtime!=''">
                AND s.create_time <![CDATA[<=]]> #{orderEtime}
            </if>
            <if test="bookingOrderStime!=null and bookingOrderStime!=''">
                AND s.booking_order_stime <![CDATA[>=]]> #{bookingOrderStime}
            </if>
            <if test="bookingOrderEtime!=null and bookingOrderEtime!=''">
                AND s.booking_order_stime <![CDATA[<=]]> #{bookingOrderEtime}
            </if>
            <if test="bookingEstimateStime!=null and bookingEstimateStime!=''">
                AND s.booking_order_etime <![CDATA[>=]]> #{bookingEstimateStime}
            </if>
            <if test="bookingEstimateEtime!=null and bookingEstimateEtime!=''">
                AND s.booking_order_etime <![CDATA[<=]]> #{bookingEstimateEtime}
            </if>
            <if test="passengerName!=null and passengerName!=''">
                AND s.passenger_name = #{passengerName}
            </if>
            <if test="passengerPhone!=null and passengerPhone!=''">
                AND s.passenger_phone = #{passengerPhone}
            </if>
            <if test="customerId!=null ">
                AND s.customer_id=#{customerId}
            </if>
            <if test="customerName!=null ">
                AND s.customer_name like concat('%', #{customerName}, '%')
            </if>
            <if test="dispatcherStatus!=null and dispatcherStatus!=0">
                AND s.dispatcher_status=#{dispatcherStatus}
            </if>
            <if test="scheduleStatus!=null and scheduleStatus!=0">
                AND s.schedule_status=#{scheduleStatus}
            </if>
            <if test="orderType!=null and orderType!=0">
                AND s.order_type=#{orderType}
            </if>
            <if test="serviceCode!=null and serviceCode!=0">
                AND s.service_code=#{serviceCode}
            </if>
            <if test="orderApplyNo!=null and orderApplyNo != ''">
                AND s.order_apply_no=#{orderApplyNo}
            </if>
            <if test="passengerStructId!=null">
                AND s.passenger_struct_id=#{passengerStructId}
            </if>
            <if test="dataPermIsLimit">
                and (
                <if test="dataPermCityCodes!=null and dataPermCityCodes.size()>0">
                    s.booking_start_city_code in
                    <foreach collection="dataPermCityCodes" item="cityCodeItem" open="(" close=")" separator=",">
                        #{cityCodeItem}
                    </foreach>
                </if>
                <if test="(dataPermCityCodes!=null and dataPermCityCodes.size()>0) and (dataPermCompanyIds!=null and dataPermCompanyIds.size()>0)">
                    or
                </if>
                <if test="dataPermCompanyIds!=null and dataPermCompanyIds.size()>0">
                    oa.customer_company_id in
                    <foreach collection="dataPermCompanyIds" separator="," close=")" open="(" item="companyItem">
                        #{companyItem}
                    </foreach>
                </if>
                )
            </if>
        </where>
        group by s.order_apply_no
        <if test="bookingOrderTimeIsAsc!=null">
            <if test="bookingOrderTimeIsAsc == true">
                ORDER BY booking_order_stime ASC
            </if>
            <if test="bookingOrderTimeIsAsc == false">
                ORDER BY booking_order_stime DESC
            </if>
        </if>
        <if test="bookingOrderETimeIsAsc!=null">
            <if test="bookingOrderETimeIsAsc == true">
                ORDER BY booking_order_etime ASC
            </if>
            <if test="bookingOrderETimeIsAsc == false">
                ORDER BY booking_order_etime DESC
            </if>
        </if>
        <if test="createTimeIsAsc!=null">
            <if test="createTimeIsAsc == true">
                ORDER BY create_time ASC
            </if>
            <if test="createTimeIsAsc == false">
                ORDER BY create_time DESC
            </if>
        </if>
        <if test="bookingOrderTimeIsAsc == null and createTimeIsAsc == null and bookingOrderETimeIsAsc == null">
            ORDER BY booking_order_stime DESC
        </if>
    </select>

<!--    调度列表的数据权限-->
    <sql id="dataPermSQL">
        <if test="dataPermIsLimit">
            and (
            <if test="dataPermCityCodes!=null and dataPermCityCodes.size()>0">
                s.booking_start_city_code in
                <foreach collection="dataPermCityCodes" item="cityCodeItem" open="(" close=")" separator=",">
                    #{cityCodeItem}
                </foreach>
            </if>
            <if test="(dataPermCityCodes!=null and dataPermCityCodes.size()>0) and (dataPermCompanyIds!=null and dataPermCompanyIds.size()>0)">
                or
            </if>
            <if test="dataPermCompanyIds!=null and dataPermCompanyIds.size()>0">
                oa.customer_company_id in
                <foreach collection="dataPermCompanyIds" separator="," close=")" open="(" item="companyItem">
                    #{companyItem}
                </foreach>
            </if>
            )
        </if>
    </sql>


    <!-- APP调度列表 客户端数据权限 -->
    <sql id="clientDataPermDataPermScheduleListForApp">
        <!-- 客户端 数据权限 本人：下单人是本人的行程 -->
        <if test="dataPermType!=null and dataPermType==4">
            and a.customer_id = #{loginUserId}
        </if>

        <!-- 客户端 数据权限 指定城市：出发地/目的地城市在用户数据权限-指定城市内 ∪ 下单人是本人的行程 -->
        <if test="dataPermType!=null and dataPermType==3">
            <!-- 指定城市并且没有配置城市不允许查看数据 -->
            <choose>
                <when test="dataPermCodeSet!=null and dataPermCodeSet.size()>0">
                    and (a.booking_start_city_code in
                    <foreach item="cityCodeItem" collection="dataPermCodeSet" open="(" separator="," close=")">
                        #{cityCodeItem}
                    </foreach>
                    or a.booking_end_city_code IN
                    <foreach item="cityCodeItem" collection="dataPermCodeSet" open="(" separator="," close=")">
                        #{cityCodeItem}
                    </foreach>
                    or a.customer_id = #{loginUserId}
                    )
                </when>
                <otherwise>
                    and a.schedule_id = -1
                </otherwise>
            </choose>
        </if>


        <!-- 客户端 数据权限 指定部门：出发地/目的地城市在用户数据权限-指定城市内 ∪ 下单人是本人的行程 -->
        <if test="dataPermType!=null and dataPermType==2">
            <choose>
                <when test="dataPermIdSet!=null and dataPermIdSet.size()>0">
                    and (a.struct_id in
                    <foreach item="deptIdItem" collection="dataPermIdSet" open="(" separator="," close=")">
                        #{deptIdItem}
                    </foreach>
                    or a.passenger_struct_id in
                    <foreach item="deptIdItem" collection="dataPermIdSet" open="(" separator="," close=")">
                        #{deptIdItem}
                    </foreach>
                    or a.customer_id = #{loginUserId}
                    )
                </when>
                <otherwise>
                    and a.schedule_id = -1
                </otherwise>
            </choose>
        </if>
    </sql>

    <!--  APP调度列表 客户端数据权限 按照公司匹配的统一逻辑 -->
    <sql id="providerDataPermScheduleListForAppCompanyQuery">
        <choose>
            <when test="dataPermIdSet!=null and dataPermIdSet.size()>0">
                and (a.company_id in
                <foreach item="companyIdItem" collection="dataPermIdSet" open="(" separator="," close=")">
                    #{companyIdItem}
                </foreach>
                or
                b.customer_company_id in
                <foreach item="companyIdItem" collection="dataPermIdSet" open="(" separator="," close=")">
                    #{companyIdItem}
                </foreach>
                <if test="loginUserId!=null">
                    or a.customer_id = #{loginUserId}
                </if>
                )
            </when>
            <otherwise>
                <if test="loginUserId!=null">
                    and a.customer_id = #{loginUserId}
                </if>
            </otherwise>
        </choose>
    </sql>

<!--    APP调度列表 客户端数据权限-->
    <sql id="providerDataPermScheduleListForApp">
        <!-- 运营端 数据权限 本人：下单人是本人 -->
        <if test="dataPermType!=null and dataPermType==7">
            <choose>
                <when test="loginUserId!=null">
                    and a.customer_id = #{loginUserId}
                </when>
                <otherwise>
                    and a.schedule_id = -1
                </otherwise>
            </choose>
        </if>
        <!-- 运营端 数据权限 指定客户或负责客户：根据客户ID(公司)批量搜索 -->
        <if test="dataPermType!=null and (dataPermType==2 or dataPermType==3)">
          <include refid="providerDataPermScheduleListForAppCompanyQuery"/>
        </if>

        <!-- 运营端 数据权限 指定部门或者所属部门：根据t_company表部门对应的客户查询 -->
        <if test="dataPermType!=null and (dataPermType==4 or dataPermType==5)">
            <include refid="providerDataPermScheduleListForAppCompanyQuery"/>
        </if>
        <!-- 运营端 数据权限 负责合同：根据长租合同表中负责的合同对应的客户查询 -->
        <if test="dataPermType!=null and dataPermType==1 ">
            <if test="loginUserId!=null">
                and a.customer_id = #{loginUserId}
            </if>
        </if>
    </sql>

    <select id="selectScheduleInnerListForApp" parameterType="com.izu.mrcar.order.dto.dispatching.SchedulePageListReqDTO" resultMap="BaseResultMap">
        select
        a.schedule_id, a.order_apply_no, a.schedule_status, a.dispatcher_status, a.schedule_time, a.create_time,
        a.booking_order_stime, a.booking_order_etime,
        a.booking_start_short_addr, a.booking_end_short_addr, a.booking_vehicle_total_count, a.order_type,
        a.customer_name, a.customer_mobile,b.order_status,a.supplier_flag
        from schedule a left join order_apply b on a.order_apply_id = b.id
        <where>
            a.order_type !=2
            <if test="internalSupplierFlag ==0">
            <!-- 统一处理：数据权限不存在不允许查看数据 -->
            <if test="dataPermType==null">
                and a.schedule_id = -1
            </if>
            <!-- 客户端 代垫司机没有被分派公司时，不允许查询行程列表 -->
            <if test="loginCompanyId == null">
                and a.schedule_id = -1
            </if>
            <!-- 客户端 只能查看本公司的行程数据 -->
            <if test="loginCompanyId!=null">
                and a.company_id = #{loginCompanyId}
            </if>
            <!-- 引入客户端数据权限 -->
            <include refid="clientDataPermDataPermScheduleListForApp"/>
            </if>
            <if test="internalSupplierFlag ==1">
                and a.supplier_id =#{loginCompanyId} and a.order_type=1
            </if>
            <if test="scheduleStatus!=null and scheduleStatus!=0">
                <choose>
                    <when test="scheduleStatus==1">
                        <if test="countFlag ==1">
                            <if test="internalSupplierFlag ==0">
                                AND a.schedule_status IN(1,5)
                            </if>
                            <if test="internalSupplierFlag ==1">
                                AND a.schedule_status =6
                            </if>
                        </if>
                        <if test="countFlag ==0">
                            AND a.schedule_status IN(1,5,6)
                        </if>
                    </when>
                    <when test="scheduleStatus==3" >
                        and EXISTS (
                        SELECT 1
                        FROM order_apply oa
                        WHERE
                        oa.order_apply_no = a.order_apply_no
                        and
                        oa.order_status in(30,40,90,100)
                        )
                    </when>
                    <when test="scheduleStatus==2" >
                        and EXISTS (
                        SELECT 1
                        FROM order_apply oa
                        WHERE
                        oa.order_apply_no = a.order_apply_no
                        and
                        oa.order_status =20
                        )
                    </when>
                    <otherwise>
                        AND a.schedule_status= #{scheduleStatus}
                    </otherwise>
                </choose>
            </if>
            <if test="companyId!=null ">
                AND a.company_id=#{companyId}
            </if>
            <if test="structId != null">
                and a.struct_id = #{structId}
            </if>
            <if test="customerName != null and customerName!=''">
                and a.customer_name = #{customerName}
            </if>
            <if test="customerMobile != null and customerMobile!=''">
                and a.customer_mobile = #{customerMobile}
            </if>
            <if test="bookingPassengerUserName != null and bookingPassengerUserName!=''">
                and a.passenger_name = #{bookingPassengerUserName}
            </if>
            <if test="bookingPassengerUserPhone != null and bookingPassengerUserPhone!=''">
                and a.passenger_phone = #{bookingPassengerUserPhone}
            </if>
            <if test="customerCompanyId != null">
                and b.customer_company_id = #{customerCompanyId}
            </if>
            <if test="passengerStructId != null">
                and a.passenger_struct_id = #{passengerStructId}
            </if>
            <if test="createTimeStart != null and createTimeStart != ''">
                and DATE(a.create_time) <![CDATA[>=]]> #{createTimeStart}
            </if>
            <if test="createTimeEnd != null and createTimeEnd != ''">
                and DATE(a.create_time) <![CDATA[<=]]> #{createTimeEnd}
            </if>
            <if test="bookingOrderStime != null and bookingOrderStime != ''">
                and DATE(a.booking_order_stime) <![CDATA[>=]]> #{bookingOrderStime}
            </if>
            <if test="bookingOrderEtime != null and bookingOrderEtime != ''">
                and DATE(a.booking_order_stime) <![CDATA[<=]]> #{bookingOrderEtime}
            </if>
            AND a.dispatcher_status = 1
        </where>
        <choose>
            <when test="bookingOrderStimeSort!=null and bookingOrderStimeSort==1">
                order by a.booking_order_stime asc
            </when>
            <when test="bookingOrderStimeSort!=null and bookingOrderStimeSort==2">
                order by a.booking_order_stime desc
            </when>
            <when test="createTimeSort!=null and createTimeSort==1">
                order by a.create_time asc
            </when>
            <when test="createTimeSort!=null and createTimeSort==2">
                order by a.create_time desc
            </when>
            <otherwise>
                ORDER BY a.schedule_status asc, a.booking_order_stime asc,a.create_time DESC
            </otherwise>
        </choose>
    </select>








    <update id="updateByConditionSelective" parameterType="com.izu.order.entity.mrcar.Schedule">
        update schedule
        <set>
            <if test="scheduleStatus != null">
                schedule_status = #{scheduleStatus},
            </if>
            <if test="dispatcherStatus != null">
                dispatcher_status = #{dispatcherStatus},
            </if>
            <if test="dispatcherTime != null">
                dispatcher_time = #{dispatcherTime},
            </if>
            <if test="scheduleTime != null">
                schedule_time = #{scheduleTime},
            </if>
            <if test="lastUpdateId != null">
                last_update_id = #{lastUpdateId},
            </if>
            <if test="lastUpdateName != null">
                last_update_name = #{lastUpdateName},
            </if>
        </set>
        <where>
            <if test="scheduleId != null">
                schedule_id = #{scheduleId}
            </if>
            <if test="orderApplyNo != null">
                order_apply_no = #{orderApplyNo}
            </if>
        </where>
    </update>
    <update id="updateById" parameterType="map">
        update schedule
        set schedule_status = #{scheduleStatus},
            last_update_id = #{lastUpdateId},
            last_update_name = #{lastUpdateName},
            supplier_id=null,
            supplier_name=null
        where schedule_id = #{scheduleId}
    </update>

    <select id="getDispatcherId" resultType="java.lang.Integer">
        select last_update_id from schedule where order_apply_no = #{orderApplyNo} limit 1
    </select>

    <select id="selectScheduleBusinessList" parameterType="com.izu.mrcar.order.dto.dispatching.SchedulePageListReqDTO"
            resultMap="AppBaseResultMap">
        select
        a.schedule_id, a.order_apply_no, a.schedule_status, a.dispatcher_status, a.schedule_time, a.create_time,
        a.booking_start_short_addr, a.booking_end_short_addr, a.order_type,
        a.customer_name, a.customer_mobile,a.struct_name
        from schedule a left join order_apply b on a.order_apply_id = b.id
        <where>
            and a.order_type = 2
            <if test="scheduleStatus!=null and scheduleStatus!=0">
                AND a.schedule_status=#{scheduleStatus}
            </if>
            <if test="companyId!=null ">
                AND a.company_id=#{companyId}
            </if>
            <if test="structId != null">
                and a.struct_id = #{structId}
            </if>
            <if test="customerName != null and customerName!=''">
                and a.customer_name = #{customerName}
            </if>
            <if test="customerMobile != null and customerMobile!=''">
                and a.customer_mobile = #{customerMobile}
            </if>
            <if test="bookingPassengerUserName != null and bookingPassengerUserName!=''">
                and a.passenger_name = #{bookingPassengerUserName}
            </if>
            <if test="bookingPassengerUserPhone != null and bookingPassengerUserPhone!=''">
                and a.passenger_phone = #{bookingPassengerUserPhone}
            </if>
            <if test="customerCompanyId != null">
                and b.customer_company_id = #{customerCompanyId}
            </if>
            <if test="passengerStructId != null">
                and a.passenger_struct_id = #{passengerStructId}
            </if>
            <if test="createTimeStart != null and createTimeStart != ''">
                and DATE(a.create_time) <![CDATA[>=]]> #{createTimeStart}
            </if>
            <if test="createTimeEnd != null and createTimeEnd != ''">
                and DATE(a.create_time) <![CDATA[<=]]> #{createTimeEnd}
            </if>
            <if test="bookingOrderStime != null and bookingOrderStime != ''">
                and DATE(a.booking_order_stime) <![CDATA[>=]]> #{bookingOrderStime}
            </if>
            <if test="bookingOrderEtime != null and bookingOrderEtime != ''">
                and DATE(a.booking_order_stime) <![CDATA[<=]]> #{bookingOrderEtime}
            </if>
            AND a.dispatcher_status = 1
            <if test="dataPermIsLimit">
                and (
                <if test="dataPermCityCodes!=null and dataPermCityCodes.size()>0">
                    a.booking_start_city_code in
                    <foreach collection="dataPermCityCodes" item="cityCodeItem" open="(" close=")" separator=",">
                        #{cityCodeItem}
                    </foreach>
                </if>
                <if test="(dataPermCityCodes!=null and dataPermCityCodes.size()>0) and (dataPermCompanyIds!=null and dataPermCompanyIds.size()>0)">
                    or
                </if>
                <if test="dataPermCompanyIds!=null and dataPermCompanyIds.size()>0">
                    b.customer_company_id in
                    <foreach collection="dataPermCompanyIds" separator="," close=")" open="(" item="companyItem">
                        #{companyItem}
                    </foreach>
                </if>
                )
            </if>

        </where>
        <choose>
            <when test="bookingOrderStimeSort!=null and bookingOrderStimeSort==1">
                order by a.booking_order_stime asc
            </when>
            <when test="bookingOrderStimeSort!=null and bookingOrderStimeSort==2">
                order by a.booking_order_stime desc
            </when>
            <when test="createTimeSort!=null and createTimeSort==1">
                order by a.create_time asc
            </when>
            <when test="createTimeSort!=null and createTimeSort==2">
                order by a.create_time desc
            </when>
<!--            查询全部或者是查询已经调度的数据，默认按照创建时间倒叙 -->
            <when test="scheduleStatus == null || scheduleStatus == 2">
                order by a.create_time desc
            </when>
            <otherwise>
                ORDER BY a.booking_order_stime asc,a.create_time DESC
            </otherwise>
        </choose>
    </select>

    <select id="querySupplierNameByOrderApplyNo" resultType="java.lang.String">
        select
        supplier_name
        from schedule
        where
         order_apply_no = #{orderApplyNo} limit 1
    </select>

    <select id="queryByOrderApplyNo" resultMap="BaseResultMap">
        select
            schedule_id,
            supplier_id,
            supplier_name
        from schedule
        where
            order_apply_no = #{orderApplyNo} limit 1
    </select>

    <update id="updateByOrderApplyNo" parameterType="map">
        update schedule_flow
        <set>
            <if test="carLicense != null and carLicense!=''">
                car_license=#{carLicense},
            </if>
            <if test="carLevelName != null and carLevelName!=''">
                car_level_name=#{carLevelName},
            </if>
            <if test="carModel != null and carModel!=''">
                car_model=#{carModel},
            </if>
            <if test="carColor != null and carColor!=''">
                car_color=#{carColor},
            </if>
            <if test="driverName != null and driverName!=''">
                driver_name=#{driverName},
            </if>
            <if test="driverMobile != null and driverMobile!=''">
                driver_mobile=#{driverMobile},
            </if>
            <if test="carBrand != null and carBrand!=''">
                car_brand=#{carBrand},
            </if>
        </set>
        where order_apply_no = #{orderApplyNo}
    </update>

</mapper>