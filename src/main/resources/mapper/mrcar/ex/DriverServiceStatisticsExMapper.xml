<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.DriverServiceStatisticsExMapper">
  <select id="selectByParam" parameterType="com.izu.order.entity.mrcar.DriverServiceStatistics" resultMap="mapper.mrcar.DriverServiceStatisticsMapper.BaseResultMap">
    select
    <include refid="mapper.mrcar.DriverServiceStatisticsMapper.Base_Column_List" />
    from driver_service_statistics
    <where>
      <if test="driverId != null">
        AND driver_id = #{driverId}
      </if>
      <if test="driverName != null">
        AND driver_name = #{driverName}
      </if>
      <if test="driverMobile != null">
        AND driver_mobile = #{driverMobile}
      </if>
      <if test="dateType != null">
        AND date_type = #{dateType}
      </if>
      <if test="yearDate != null">
        AND year_date = #{yearDate}
      </if>
      <if test="yearMonthDate != null">
        AND year_month_date = #{yearMonthDate}
      </if>
      <if test="createDate != null">
        AND create_date = #{createDate}
      </if>
    </where>
    limit 1
  </select>

  <select id="selectByDriverIdAndTime" resultMap="mapper.mrcar.DriverServiceStatisticsMapper.BaseResultMap">
    select
    <include refid="mapper.mrcar.DriverServiceStatisticsMapper.Base_Column_List" />
    from driver_service_statistics
    WHERE driver_id = #{driverId} AND date_type = 1 AND create_date BETWEEN #{startTime} AND #{endTime}
  </select>

  <select id="selectByDriverIds" parameterType="com.izu.order.entity.mrcar.DriverServiceStatistics" resultMap="mapper.mrcar.DriverServiceStatisticsMapper.BaseResultMap">
    select
    <include refid="mapper.mrcar.DriverServiceStatisticsMapper.Base_Column_List" />
    from driver_service_statistics
    <where>
      <if test="driverIds != null">
        AND driver_id in
        <foreach collection="driverIds" item="driverId" open="(" close=")" separator=",">
        	#{driverId}
        </foreach>
      </if>
      <if test="dateType != null">
        AND date_type = #{dateType}
      </if>
    </where>
  </select>

  <select id="sortDriverIdByOrderNum" resultType="java.lang.Integer">
    select driver_id
    from driver_service_statistics
    where date_type = 4
    and driver_id in
    <foreach collection="driverIdList" separator="," open="(" close=")" item="driverIdItem">
      #{driverIdItem}
    </foreach>
    order by total_count desc
  </select>
</mapper>