<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.DemandOrderVehicleSupplierExMapper">
    <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.DemandOrderVehicleSupplier"
               extends="mapper.mrcar.DemandOrderVehicleSupplierMapper.BaseResultMap" />

    <select id="selectByDemandOrderNum" resultMap="BaseResultMap">
        select <include refid="mapper.mrcar.DemandOrderVehicleSupplierMapper.Base_Column_List"/>
        from so_demand_order_vehicle_supplier
        where demand_order_num = #{demandOrderNum,jdbcType=VARCHAR} and supplier_id = #{supplierId} and del_tag = 0
    </select>

</mapper>