<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.BusinessSupplierExpenditureDetailExMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.BusinessSupplierExpenditureDetail" extends="mapper.mrcar.BusinessSupplierExpenditureDetailMapper.BaseResultMap"/>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into business_supplier_expenditure_detail (expenditure_no, order_no, service_code,
    vehicle_provider, driver_provider, company_id,company_code,
    company_name, should_pay_amount, customer_bill_tax_rate,
    should_pay_amount_no_rate, supplier_amount,
    supplier_amount_no_rate, return_rate, car_license,
    driver_id, driver_name, driver_phone,
    use_time, base_amount, over_time_amount,
    over_mileage_amount, other_amount, reduction_total_amount,
    customer_id, customer_name, booking_passenger_user_id,
    booking_passenger_user_name, create_time,supplier_rate)
    values
    <foreach collection="list" item="expenditureDetail" index="index" separator=",">
      (
      #{expenditureDetail.expenditureNo},
      #{expenditureDetail.orderNo},
      #{expenditureDetail.serviceCode},
      #{expenditureDetail.vehicleProvider},
      #{expenditureDetail.driverProvider},
      #{expenditureDetail.companyId},
      #{expenditureDetail.companyCode},
      #{expenditureDetail.companyName},
      #{expenditureDetail.shouldPayAmount},
      #{expenditureDetail.customerBillTaxRate},
      #{expenditureDetail.shouldPayAmountNoRate},
      #{expenditureDetail.supplierAmount},
      #{expenditureDetail.supplierAmountNoRate},
      #{expenditureDetail.returnRate},
      #{expenditureDetail.carLicense},
      #{expenditureDetail.driverId},
      #{expenditureDetail.driverName},
      #{expenditureDetail.driverPhone},
      #{expenditureDetail.useTime},
      #{expenditureDetail.baseAmount},
      #{expenditureDetail.overTimeAmount},
      #{expenditureDetail.overMileageAmount},
      #{expenditureDetail.otherAmount},
      #{expenditureDetail.reductionTotalAmount},
      #{expenditureDetail.customerId},
      #{expenditureDetail.customerName},
      #{expenditureDetail.bookingPassengerUserId},
      #{expenditureDetail.bookingPassengerUserName},
      #{expenditureDetail.createTime},
      #{expenditureDetail.supplierRate}
      )
    </foreach>
  </insert>
    <delete id="deleteByExpenditureNo">
      delete from business_supplier_expenditure_detail
      where expenditure_no = #{expenditureNo}
    </delete>
    <select id="getSubTripDetailList" resultType="com.izu.mrcar.order.dto.provider.output.SubTripDetailOutputDTO">
    select bsed.order_no as orderNo,bsed.service_code as serviceCode,bsed.vehicle_provider as vehicleProvider,
      bsed.driver_provider as driverProvider,bsed.company_name as companyName,IFNULL(bsed.should_pay_amount, 0) as shouldPayAmount,
           IFNULL(bsed.customer_bill_tax_rate, 0) as customerBillTaxRate,IFNULL(bsed.should_pay_amount_no_rate, 0) as shouldPayAmountNoRate,
           IFNULL(bsed.supplier_amount, 0) as supplierAmount,IFNULL(bsed.supplier_amount_no_rate, 0) as supplierAmountNoRate,IFNULL(bsed.return_rate, 0) as incomeRate,
      bsed.car_license as numberPlate,bsed.driver_name as driverName,bsed.driver_phone as driverPhone,bsed.use_time as useVehicleTime,bsed.driver_id as driverId,
           IFNULL(bsed.base_amount, 0) as basicFee,IFNULL(bsed.over_time_amount, 0) as overtimeFee,IFNULL(bsed.over_mileage_amount, 0) as excessMileageFee,
           IFNULL(bsed.other_amount, 0) as otherFee,IFNULL(bsed.reduction_total_amount, 0) as deductionsTotalFee,bsed.company_id as companyId,bsed.company_code as companyCode,
      bsed.customer_id as customerId,bsed.customer_name as customerName,bsed.booking_passenger_user_id as bookingPassengerUserId,
      bsed.booking_passenger_user_name as bookingPassengerUserName,IFNULL(bo.refund_amount_synthesis,0) AS returnAmount,bsed.supplier_rate as supplierRate
    from
      business_supplier_expenditure_detail bsed
      LEFT JOIN bill_order bo ON bsed.order_no = bo.order_no
    <where>
      <if test="tripType != null and tripType != '' ">
        and service_code = #{tripType}
      </if>
      <if test="createTimeStart != null and createTimeStart != ''">
        and bo.create_time <![CDATA[>=]]> #{createTimeStart}
      </if>
      <if test="createTimeEnd != null  and createTimeEnd != ''">
        and bo.create_time <![CDATA[<=]]>#{createTimeEnd}
      </if>
      <if test="useVehicleTimeStart != null and useVehicleTimeStart != ''">
        and use_time <![CDATA[>=]]> #{useVehicleTimeStart}
      </if>
      <if test="useVehicleTimeEnd != null  and useVehicleTimeEnd != ''">
        and use_time <![CDATA[<=]]>#{useVehicleTimeEnd}
      </if>
      <if test="numberPlate!='' and numberPlate !=null ">
        and car_license like CONCAT('%', #{numberPlate}, '%')
      </if>
      <if test="passenger!='' and passenger !=null ">
        and booking_passenger_user_name like CONCAT('%',#{passenger},'%')
      </if>
      <if test="createUser!='' and createUser !=null ">
        and customer_name like CONCAT('%', #{createUser}, '%')
      </if>
      <if test="supplierName!='' and supplierName !=null ">
        and company_code = #{supplierName}
      </if>
      and expenditure_no = #{expenditureNo}
    </where>
  </select>
    <select id="selectOrderListByExpenditureNo" resultType="java.lang.String">
      select order_no
      from business_supplier_expenditure_detail
      where expenditure_no = #{expenditureNo}
    </select>
  <select id="selectCountByExNoAndProvider" resultType="java.lang.Integer">
    select count(1)
    from business_supplier_expenditure_detail
    where expenditure_no = #{expenditureNo} and
          ((vehicle_provider = 1 and driver_provider = 2) or (vehicle_provider = 2 and driver_provider = 1))
  </select>
  <select id="selectDetailByExpenditureNo"
          resultType="com.izu.mrcar.order.dto.provider.output.PushBusinessSupplierExpenditureDetail">
    select customer_id as customerId,order_no as orderNo,supplier_amount as supplierAmount,company_id as companyId,company_code as companyCode
    FROM business_supplier_expenditure_detail
    WHERE expenditure_no = #{expenditureNo}
  </select>
</mapper>