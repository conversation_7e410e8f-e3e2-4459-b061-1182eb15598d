<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.CoOrderTradeRecordExMapper">
    <select id="queryListByPage" parameterType="map" resultMap="mapper.mrcar.CoOrderTradeRecordMapper.BaseResultMap">
        select
        <include refid="mapper.mrcar.CoOrderTradeRecordMapper.Base_Column_List" />
        from co_order_trade_record
        <where>
            <if test="orderNo != null and orderNo != ''">
                and order_no like concat('%',#{orderNo},'%')
            </if>
            <if test="tradeType != null ">
                and trade_type = #{tradeType}
            </if>
            <if test="tradeChannel != null ">
                and trade_channel = #{tradeChannel}
            </if>
        </where>
        order by update_time desc
    </select>

    <select id="queryByOrderNoAndTradeStatusAndTradeType" parameterType="map"
            resultMap="mapper.mrcar.CoOrderTradeRecordMapper.BaseResultMap">
        select
        <include refid="mapper.mrcar.CoOrderTradeRecordMapper.Base_Column_List" />
        from co_order_trade_record
        where
        order_no= #{orderNum}
        and
        trade_status = #{tradeStatus}
        and
        trade_type = #{tradeType}
        order by update_time desc limit 1
    </select>
</mapper>