<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.GovPublicCarOrderUserInfoExMapper">
    <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.GovPublicCarOrderUserInfo"
               extends="mapper.mrcar.GovPublicCarOrderUserInfoMapper.BaseResultMap">
    </resultMap>

    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>

    <insert id="batchInsert">
        insert into gov_public_car_order_user_info (order_no, user_type, user_id,
        user_code, user_name, user_mobile,
        struct_id, struct_code, struct_name,
        company_code, company_name, create_time,
        update_time) values
        <foreach collection="govPublicCarOrderUserInfoList" separator="," item="item">
            (#{item.orderNo,jdbcType=VARCHAR}, #{item.userType,jdbcType=INTEGER}, #{item.userId,jdbcType=INTEGER},
            #{item.userCode,jdbcType=VARCHAR}, #{item.userName,jdbcType=VARCHAR}, #{item.userMobile,jdbcType=VARCHAR},
            #{item.structId,jdbcType=INTEGER}, #{item.structCode,jdbcType=VARCHAR}, #{item.structName,jdbcType=VARCHAR},
            #{item.companyCode,jdbcType=VARCHAR}, #{item.companyName,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>


    <select id="selectOrderNoByExample" parameterType="com.izu.order.entity.mrcar.GovPublicCarOrderUserInfoExample" resultType="java.lang.String">
        select
        order_no orderNo
        from gov_public_car_order_user_info
        <if test="_parameter != null">
            <include refid="Example_Where_Clause" />
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
    </select>

</mapper>