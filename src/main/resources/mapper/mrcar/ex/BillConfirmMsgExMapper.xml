<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.BillConfirmMsgExMapper">
  <select id="selectConfirmByOrderApplyNo" resultType="java.lang.Integer">
    select count(1)
    from bill_confirm_msg
    where order_apply_no = #{orderNo} and confirm_status = 0
  </select>
  <update id="updateByOrderApplyNo" >
    update bill_confirm_msg
    set confirm_status = 1
    where order_apply_no = #{orderApplyNo}
  </update>
  <select id="selectConfirmByOrderApplyNoAndPhone" resultType="java.lang.Integer">
    select count(1)
    from bill_confirm_msg
    where order_apply_no = #{orderNo} and contact_mobile = #{userPhone} and confirm_status = 1
  </select>
</mapper>