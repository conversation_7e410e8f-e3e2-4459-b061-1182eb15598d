<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.MrcarAttachmentExMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.MrcarAttachment">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="bus_id" jdbcType="INTEGER" property="busId" />
    <result column="bus_no" jdbcType="VARCHAR" property="busNo" />
    <result column="bus_type" jdbcType="TINYINT" property="busType" />
    <result column="file_url" jdbcType="VARCHAR" property="fileUrl" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="file_real_name" jdbcType="VARCHAR" property="fileRealName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="file_category" jdbcType="TINYINT" property="fileCategory" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="file_description" jdbcType="VARCHAR" property="fileDescription" />
  </resultMap>
  <sql id="Base_Column_List">
    id, bus_id, bus_no, bus_type, file_url, file_name, file_real_name, create_time, update_time, 
    file_category, status, file_description
  </sql>

  <insert id="batchInsert" parameterType="java.util.List">
    insert into mrcar_attachment (bus_id, bus_no, bus_type,
    file_url, file_name, file_real_name)
    values
    <foreach collection="listFile" item="attachment" index="index" separator=",">
      (
      #{attachment.busId},
      #{attachment.busNo},
      #{attachment.busType},
      #{attachment.fileUrl},
      #{attachment.fileName},
      #{attachment.fileRealName}
      )
    </foreach>
  </insert>

  <select id="findByApplyId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from mrcar_attachment where bus_id = #{id} and bus_type = #{busType}
  </select>

  <select id="findByOrderIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from mrcar_attachment where bus_id = #{id} and bus_type = #{busType}
  </select>

  <delete id="deleteByBusId">
    delete from mrcar_attachment
    where bus_id = #{id} and bus_type = #{busType}
  </delete>

  <select id="findByBusNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from mrcar_attachment
    where bus_no = #{busNo} and bus_type = #{busType}
  </select>
</mapper>