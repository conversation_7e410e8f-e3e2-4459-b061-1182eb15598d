<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.CoOrderInfoFileExMapper">

    <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.CoOrderInfoFile"
               extends="mapper.mrcar.CoOrderInfoFileMapper.BaseResultMap"/>

    <select id="list" resultMap="mapper.mrcar.CoOrderInfoFileMapper.BaseResultMap">
        select
        <include refid="mapper.mrcar.CoOrderInfoFileMapper.Base_Column_List"/>
        from co_order_info_file
        where valid = 1
        <if test="coOrderId != null">
            AND co_order_id = #{coOrderId}
        </if>
        <if test="orderNum != null and orderNum != ''">
            AND order_num = #{orderNum}
        </if>
        <if test="fileType != null">
            AND file_type = #{fileType}
        </if>

    </select>


    <insert id="batchInsert" parameterType="com.izu.order.entity.mrcar.CoOrderInfoFile">
        insert into co_order_info_file (create_id, create_name, create_mobile,
        co_order_id,order_num, file_name, file_url,
        file_type) values
        <foreach collection="files" item="element" separator=",">
            (#{element.createId}, #{element.createName}, #{element.createMobile}, #{element.coOrderId},
            #{element.orderNum},
            #{element.fileName}, #{element.fileUrl}, #{element.fileType})
        </foreach>
    </insert>

    <update id="batchUnValid">
        update co_order_info_file set valid = 0 where co_order_id = #{coOrderId} and file_type = #{type}
    </update>

</mapper>