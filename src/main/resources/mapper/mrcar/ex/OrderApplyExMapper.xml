<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.OrderApplyExMapper">


  <resultMap id="orderApplyPageMap" type="com.izu.mrcar.order.dto.order.OrderApplyListRespDTO" extends="mapper.mrcar.OrderApplyMapper.BaseResultMap">
    <result column="shouldpay_amount" jdbcType="DECIMAL" property="shouldpayAmount" />
    <result column="coupon_deduct_amount" jdbcType="DECIMAL" property="couponDeductAmount" />
    <result column="discount_amount" jdbcType="DECIMAL" property="discountAmount" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
  </resultMap>

  <resultMap id="orderApplyProviderPageMap" type="com.izu.mrcar.order.dto.provider.output.OrderApplyOpeOutputDTO" extends="mapper.mrcar.OrderApplyMapper.BaseResultMap">
    <result column="shouldpay_amount" jdbcType="DECIMAL" property="shouldpayAmount" />
    <result column="coupon_deduct_amount" jdbcType="DECIMAL" property="couponDeductAmount" />
    <result column="discount_amount" jdbcType="DECIMAL" property="discountAmount" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount"/>
  </resultMap>


  <!-- PC行程列表 -->
  <select id="selectOrderApplyPageList" parameterType="com.izu.mrcar.order.dto.order.OrderApplyListReqDTO" resultMap="orderApplyPageMap">
    select apply.order_apply_no,
    apply.create_time,
    apply.order_type,
    apply.service_code,
    apply.customer_name,
    apply.company_id,
    apply.struct_id,
    apply.booking_passenger_user_id,
    apply.booking_passenger_user_name,
    apply.customer_company_id,
    apply.customer_company_name,
    apply.booking_start_short_addr,
    apply.booking_start_long_addr,
    apply.booking_end_short_addr,
    apply.booking_end_long_addr,
    apply.booking_order_stime,
    apply.booking_order_etime,
    apply.booking_vehicle_total_count,
    apply.order_status,
    apply.company_name,
    apply.fixed_price_valid,
    b.fixed_price,
    apply.himself,
    apply.customer_id,
    apply.settle_days,
    apply.settle_company_code,
    apply.settle_company_name,
    apply.settle_tax,
    apply.struct_name,
    apply.passenger_struct_name,
    apply.audit_flag,
    apply.customer_no,
    apply.booking_passenger_customer_no,
    b.shouldpay_amount,
    b.coupon_deduct_amount,
    b.total_amount
    from order_apply as apply
    left join bill as b on apply.order_apply_no = b.order_apply_no
    <where>
       apply.deleted = 0
    <if test="orderApplyNo != null and orderApplyNo != ''">
      and apply.order_apply_no = #{orderApplyNo}
    </if>
    <if test="orderType != null ">
      and apply.order_type = #{orderType}
    </if>
    <if test="structId != null ">
      and apply.struct_id = #{structId}
    </if>
    <if test="passengerStructId != null ">
      and apply.passenger_struct_id = #{passengerStructId}
    </if>
    <if test="serviceCode != null and serviceCode !=''">
      and apply.service_code in
      <foreach item="item" collection="serviceCode.split(',')" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="customerName != null and customerName != ''">
      and apply.customer_name = #{customerName}
    </if>
    <if test="companyId != null ">
      and apply.company_id = #{companyId}
    </if>
    <if test="auditFlag != null ">
      and apply.audit_flag = #{auditFlag}
    </if>
    <if test="bookingPassengerUserName != null and bookingPassengerUserName != ''">
      and apply.booking_passenger_user_name = #{bookingPassengerUserName}
    </if>
    <if test="customerCompanyId != null ">
      and apply.customer_company_id = #{customerCompanyId}
    </if>
      <if test="vehicleCompanyId != null ">
        and EXISTS (
        SELECT 1
        FROM order_info oi
        WHERE
        oi.order_apply_no = apply.order_apply_no
        and
        oi.vehicle_company_id =#{vehicleCompanyId}
        and oi.order_type =1
        )
      </if>
      <if test="driverCompanyId != null ">
        and EXISTS (
        SELECT 1
        FROM order_info oi
        WHERE
        oi.order_apply_no = apply.order_apply_no
        and
        oi.driver_company_id =#{driverCompanyId}
        and oi.order_type =1
        )
      </if>
      <if test="vehicleCompanyName != null and vehicleCompanyName!=''">
        and EXISTS (
        SELECT 1
        FROM order_info oi
        WHERE
        oi.order_apply_no = apply.order_apply_no
        and
        oi.vehicle_company_name like CONCAT('%',#{vehicleCompanyName},'%')
        and oi.order_type =1
        )
      </if>
      <if test="driverCompanyName != null and driverCompanyName!=''">
        and EXISTS (
        SELECT 1
        FROM order_info oi
        WHERE
        oi.order_apply_no = apply.order_apply_no
        and
        oi.driver_company_name like CONCAT('%',#{driverCompanyName},'%')
        and oi.order_type =1
        )
      </if>
    <if test="orderStatus != null and orderStatus!=''">
      and apply.order_status in
      <foreach item="item" collection="orderStatus.split(',')" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="createTimeStart != null and createTimeStart != ''">
      and apply.create_time <![CDATA[>=]]> #{createTimeStart}
    </if>
      <if test="createTimeEnd != null and createTimeEnd != ''">
      and apply.create_time <![CDATA[<=]]> #{createTimeEnd}
    </if>
    <if test="bookingOrderStime != null and bookingOrderStime != ''">
      and apply.booking_order_stime <![CDATA[>=]]> #{bookingOrderStime}
    </if>
      <if test="bookingOrderEtime != null and bookingOrderEtime != ''">
      and apply.booking_order_stime <![CDATA[<=]]> #{bookingOrderEtime}
    </if>
      <if test="customerMobile != null and customerMobile != ''">
      and apply.customer_mobile = #{customerMobile}
    </if>
      <if test="bookingPassengerUserPhone != null and bookingPassengerUserPhone != ''">
        and apply.booking_passenger_user_phone = #{bookingPassengerUserPhone}
      </if>
      <if test="orderTypeList != null and orderTypeList.size()>0 and port==1 and  internalSupplierFlag ==0 ">
        and apply.order_type in
        <foreach item="item" collection="orderTypeList" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    <if test="permissions != null and permissions != ''">
      and (apply.booking_start_city_code in
      <foreach item="cityCode" collection="permissions.split(',')" open="(" separator="," close=")">
        #{cityCode}
      </foreach>
      or apply.booking_end_city_code IN
      <foreach item="cityCode" collection="permissions.split(',')" open="(" separator="," close=")">
        #{cityCode}
      </foreach>
      or apply.customer_id = #{loginId}
      )
    </if>
    <!--指定部门 -->
    <if test="internalSupplierFlag ==0">
      <if test="dataPermType!=null and dataPermType ==2">
        and (apply.struct_id in
        <foreach item="item" collection="dataPermCodeSet" open="(" separator="," close=")">
          #{item}
        </foreach>
        or apply.passenger_struct_id IN
        <foreach item="item" collection="dataPermCodeSet" open="(" separator="," close=")">
          #{item}
        </foreach>
        or apply.customer_id = #{loginId}
        )
      </if>
      <!--指定城市 -->
      <if test="dataPermType!=null and dataPermType ==3">
        and (apply.booking_start_city_code in
        <foreach item="cityCode" collection="dataPermCodeSet" open="(" separator="," close=")">
          #{cityCode}
        </foreach>
        or apply.booking_end_city_code IN
        <foreach item="cityCode" collection="dataPermCodeSet" open="(" separator="," close=")">
          #{cityCode}
        </foreach>
        or apply.customer_id = #{loginId}
        )
      </if>
      <!--本人权限 -->
      <if test="dataPermType!=null and dataPermType ==4">
        and apply.customer_id = #{loginId}
      </if>
      <if test="companyIds != null and companyIds != ''">
        and ((order_type != 2 and apply.company_id in
        <foreach item="item" collection="companyIds.split(',')" open="(" separator="," close=")">
          #{item}
        </foreach> ) or (order_type =2 and apply.customer_company_id in
        <foreach item="item" collection="companyIds.split(',')" open="(" separator="," close=")">
          #{item}
        </foreach> ))
      </if>
      <if test="onlySelf != null and onlySelf == true">
        and ((apply.order_type in (1, 2) and apply.customer_id = #{loginId}) or (apply.order_type = 3 and apply.booking_passenger_user_id = #{loginId}))
      </if>
    </if>
      <if test="internalSupplierFlag ==1">
        and EXISTS (
        SELECT 1
        FROM order_info oi
        WHERE
        oi.order_apply_no = apply.order_apply_no
        and
        oi.vehicle_company_id =#{loginCompanyId}
        and
        oi.driver_company_id =#{loginCompanyId}
        and
        oi.order_type =1
        )

    </if>
    </where>
    <choose>
      <when test="sort != null and sort != ''">
        order by apply.${sort}
      </when>
      <otherwise>
        order by apply.create_time desc
      </otherwise>
    </choose>
  </select>

  <!-- PC行程列表 -->
  <select id="selectOrderApplyProviderPageList" parameterType="com.izu.mrcar.order.dto.provider.output.OrderApplyOpeOutputDTO" resultMap="orderApplyProviderPageMap">
    select apply.order_apply_no,
    apply.create_time,
    apply.order_type,
    apply.service_code,
    apply.customer_name,
    apply.company_id,
    apply.struct_id,
    apply.booking_passenger_user_id,
    apply.booking_passenger_user_name,
    apply.customer_company_name,
    apply.booking_start_short_addr,
    apply.booking_start_long_addr,
    apply.booking_end_short_addr,
    apply.booking_end_long_addr,
    apply.booking_order_stime,
    apply.booking_order_etime,
    apply.booking_vehicle_total_count,
    apply.order_status,
    apply.company_name,
    apply.fixed_price_valid,
    b.fixed_price,
    apply.himself,
    apply.customer_id,
    apply.settle_days,
    apply.settle_company_code,
    apply.settle_company_name,
    apply.settle_tax,
    apply.struct_name,
    apply.passenger_struct_name,
    apply.audit_flag,
    apply.order_channel_source,
    b.shouldpay_amount,
    b.coupon_deduct_amount,
    b.total_amount
    from order_apply as apply
    left join bill as b on apply.order_apply_no = b.order_apply_no
    left join order_info c on apply.order_apply_no=c.order_apply_no
    left join business_supplier_expenditure_detail bsed on c.order_no=bsed.order_no
    where apply.deleted = 0
      <if test="orderApplyNo != null and orderApplyNo != ''">
        and apply.order_apply_no = #{orderApplyNo}
      </if>
      <if test="orderType != null ">
        and apply.order_type = #{orderType}
      </if>
      <if test="structId != null ">
        and apply.struct_id = #{structId}
      </if>
      <if test="passengerStructId != null ">
        and apply.passenger_struct_id = #{passengerStructId}
      </if>
      <if test="serviceCode != null and serviceCode !=''">
        and apply.service_code in
        <foreach item="item" collection="serviceCode.split(',')" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="vehicleCompanyId != null ">
        and EXISTS (
        SELECT 1
        FROM order_info oi
        WHERE
        oi.order_apply_no = apply.order_apply_no
        and
        oi.vehicle_company_id =#{vehicleCompanyId}
        and oi.order_type =1
        )
      </if>
      <if test="driverCompanyId != null ">
        and EXISTS (
        SELECT 1
        FROM order_info oi
        WHERE
        oi.order_apply_no = apply.order_apply_no
        and
        oi.driver_company_id =#{driverCompanyId}
        and oi.order_type =1
        )
      </if>
    <if test="vehicleCompanyName != null and vehicleCompanyName!=''">
      and EXISTS (
      SELECT 1
      FROM order_info oi
      WHERE
      oi.order_apply_no = apply.order_apply_no
      and
      oi.vehicle_company_name like CONCAT('%',#{vehicleCompanyName},'%')
      and oi.order_type =1
      )
    </if>
    <if test="driverCompanyName != null and driverCompanyName!=''">
      and EXISTS (
      SELECT 1
      FROM order_info oi
      WHERE
      oi.order_apply_no = apply.order_apply_no
      and
      oi.driver_company_name like CONCAT('%',#{driverCompanyName},'%')
      and oi.order_type =1
      )
    </if>
      <if test="customerName != null and customerName != ''">
        and apply.customer_name = #{customerName}
      </if>
      <if test="companyId != null ">
        and apply.company_id = #{companyId}
      </if>
      <if test="auditFlag != null ">
        and apply.audit_flag = #{auditFlag}
      </if>
      <if test="bookingPassengerUserName != null and bookingPassengerUserName != ''">
        and apply.booking_passenger_user_name = #{bookingPassengerUserName}
      </if>
      <if test="customerCompanyId != null ">
        and apply.customer_company_id = #{customerCompanyId}
      </if>
      <if test="orderStatus != null and orderStatus!=''">
        and apply.order_status in
        <foreach item="item" collection="orderStatus.split(',')" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="createTimeStart != null and createTimeStart != ''">
        and apply.create_time <![CDATA[>=]]> #{createTimeStart}
      </if>
      <if test="createTimeEnd != null and createTimeEnd != ''">
        and apply.create_time <![CDATA[<=]]> #{createTimeEnd}
      </if>
      <if test="bookingOrderStime != null and bookingOrderStime != ''">
        and apply.booking_order_stime <![CDATA[>=]]> #{bookingOrderStime}
      </if>
      <if test="bookingOrderEtime != null and bookingOrderEtime != ''">
        and apply.booking_order_stime <![CDATA[<=]]> #{bookingOrderEtime}
      </if>
      <if test="customerMobile != null and customerMobile != ''">
        and apply.customer_mobile = #{customerMobile}
      </if>
      <if test="bookingPassengerUserPhone != null and bookingPassengerUserPhone != ''">
        and apply.booking_passenger_user_phone = #{bookingPassengerUserPhone}
      </if>
      <!--<if test="loginCompanyId != null">-->
        <!--and apply.company_id = #{loginCompanyId}-->
      <!--</if>-->
      <if test="onlySelf != null and onlySelf == true">
        and ((apply.order_type in (1, 2) and apply.customer_id = #{loginId}) or (apply.order_type = 3 and apply.booking_passenger_user_id = #{loginId}))
      </if>
      <if test="expenditureNo !=null and expenditureNo != ''">
        and bsed.expenditure_no = #{expenditureNo}
      </if>
      <if test="orderChannelSource !=null and orderChannelSource != ''">
        and apply.order_channel_source = #{orderChannelSource}
      </if>

      <if test="dataPermType !=null ">
        <!-- 负责合同  用户的数据权限的合同范围，匹配行程中车辆所属合同 -->
        <if test="dataPermType == 1 ">
          <!-- (商务用车列表 本人创建)-->
          <choose>
            <when test="orderType != null and orderType==2 ">
              and apply.customer_id = #{loginId}
            </when>
            <otherwise>
              <if test="dataPermCodeSet != null and dataPermCodeSet.size()>0 ">
                and c.assign_car_license in
                <foreach item="item" collection="dataPermCodeSet" open="(" separator="," close=")">
                  #{item}
                </foreach>
              </if>
            </otherwise>
          </choose>
        </if>
        <!-- 负责客户/指定客户：用户的数据权限的客户名称，匹配行程的下单人企业 或者乘车人企业； -->
        <if test="dataPermType == 2 or  dataPermType == 3 ">
          <if test="dataPermCodeSet != null and dataPermCodeSet.size()>0 ">
            and (apply.company_id in
            <foreach item="item" collection="dataPermCodeSet" open="(" separator="," close=")">
              #{item}
            </foreach>
            or apply.customer_company_id in
            <foreach item="item" collection="dataPermCodeSet" open="(" separator="," close=")">
              #{item}
            </foreach>
            <if test="loginId!=null">
              or apply.customer_id = #{loginId}
            </if>
            )
          </if>
        </if>
        <!-- 指定部门/所属部门：乘车人企业对应的CRM客户的所属部门 是登录用户的数据权限内的部门 -->
        <if test="dataPermType == 4 or dataPermType == 5 ">
          <choose>
            <when test="dataPermCodeSet != null and dataPermCodeSet.size()>0 ">
              and (apply.customer_company_id in
              <foreach item="item" collection="dataPermCodeSet" open="(" separator="," close=")">
                #{item}
              </foreach>
              <if test="loginId!=null">
                or apply.customer_id = #{loginId}
              </if>
              )
            </when>
            <otherwise>
              <if test="loginId!=null">
                and apply.customer_id = #{loginId}
              </if>
            </otherwise>
          </choose>
        </if>
        <!-- 本人 下单人为本人-->
        <if test="dataPermType == 7">
          <choose>
            <when test="loginId!=null">
              and apply.customer_id = #{loginId}
            </when>
            <otherwise>
              and id = -1
            </otherwise>
          </choose>
        </if>
      </if>
      group by apply.order_apply_no
    <choose>
      <when test="sort != null and sort != ''">
        order by apply.${sort}
      </when>
      <otherwise>
        order by apply.create_time desc
      </otherwise>
    </choose>
  </select>

  <!--PC订单管理列表，只与数据权限有关，且只显示已审批通过的订单-->
  <select id="selectPageList" resultMap="mapper.mrcar.OrderApplyMapper.BaseResultMap">
    select
    <include refid="mapper.mrcar.OrderApplyMapper.Base_Column_List" />
    from order_apply
    <where>
      approval_status = 10
    <if test="orderType != null">
      and order_type = #{orderType}
    </if>
    <if test="customerName != null and customerName != ''">
      and customer_name = #{customerName}
    </if>
    <if test="orderStatus != null">
      and order_status = #{orderStatus}
    </if>
    <if test="createTimeStart != null and createTimeStart != ''">
      and create_time <![CDATA[>=]]> #{createTimeStart}
    </if>
      <if test="createTimeEnd != null and createTimeEnd != ''">
      and create_time <![CDATA[<=]]> #{createTimeEnd}
    </if>
    <if test="companyId != null">
      and company_id = #{companyId}
    </if>
    <if test="permissionArr != null">
      <if test="isReceiveOrder">
        and (booking_start_city_code in
        <foreach collection="permissionArr" item="cityCode" index="index" separator="," open="(" close=")">
          #{cityCode}
        </foreach>
        OR booking_end_city_code IN
        <foreach collection="permissionArr" item="cityCode" index="index" separator="," open="(" close=")">
          #{cityCode}
        </foreach>
        )
      </if>
      <if test="isReceiveOrder==false">
        and create_city_code in
        <foreach collection="permissionArr" item="cityCode" index="index" separator="," open="(" close=")">
          #{cityCode}
        </foreach>
      </if>
    </if>
    </where>
    order by create_time desc
  </select>
  <!--PC订单申请列表，与数据权限有关且与当前登录用户有关-->
  <select id="selectApprovalPageList" resultMap="mapper.mrcar.OrderApplyMapper.BaseResultMap">
    select
    <include refid="mapper.mrcar.OrderApplyMapper.Base_Column_List" />
    from order_apply
    <where>
    <if test="orderType != null">
      AND order_type = #{orderType}
    </if>
    <if test="customerName != null and customerName != ''">
      and customer_name = #{customerName}
    </if>
    <if test="applyStatus != null and applyStatus != 0">
      and approval_status = #{applyStatus}
    </if>
    <if test="createTimeStart != null and createTimeStart != ''">
      and create_time <![CDATA[>=]]> #{createTimeStart}
    </if>
      <if test="createTimeEnd != null and createTimeEnd != ''">
      and create_time <![CDATA[<=]]> #{createTimeEnd}
    </if>
      <if test="companyId != null">
        and company_id = #{companyId}
      </if>
    <if test="customerId != null">
      and customer_id = #{customerId}
    </if>
    <if test="userId != null">
      and (customer_id = #{userId}
      <if test="permissionArr != null">
        OR create_city_code in
        <foreach collection="permissionArr" item="cityCode" index="index" separator="," open="(" close=")">
          #{cityCode}
        </foreach>
      </if>
      )
    </if>
    </where>
    order by create_time desc
  </select>
  <!--APP个人行程列表，只能看到自己的订单-->
  <select id="selectPageListForAPP" resultMap="mapper.mrcar.OrderApplyMapper.BaseResultMap" parameterType="com.izu.mrcar.order.dto.mrcar.param.OrderApplyQueryParam">
    select order_apply_no,order_type,booking_order_stime,booking_start_short_addr,
    booking_start_point,booking_end_short_addr,booking_end_point,order_status,service_code,appraise_submited,
    customer_name,customer_mobile,booking_passenger_user_name,customer_company_name,company_name,booking_order_etime,passenger_struct_name,
    booking_passenger_user_id,case order_status when 30 then 1 when 20 then 2 when 10 then 3 when 5 then 4 else 5 end sordFeild,
    start_address_label,end_address_label,customer_company_id
    from order_apply
    <where>
    <if test="customerId != null">
      and  customer_id = #{customerId}
    </if>
      <if test="approval_status != null">
        and approval_status = #{approval_status}
      </if>
    <if test="userId != null">
      and ((order_type in (1, 2) and customer_id = #{userId}) or (order_type = 3 and booking_passenger_user_id = #{userId}))
    </if>
    <if test="companyId != null">
      and company_id = #{companyId}
    </if>
    <if test="keyWord!=null and keyWord!='' ">
        and (customer_name  like CONCAT('%',#{keyWord,jdbcType=VARCHAR},'%') or booking_start_short_addr  like CONCAT('%',#{keyWord,jdbcType=VARCHAR},'%'))
    </if>
    <if test="orderStatusApp != null and orderStatusApp != ''">
      <if test="orderStatusApp ==2">
        and order_status = 10
      </if>
      <if test="orderStatusApp ==3">
        and order_status = 20
      </if>
      <if test="orderStatusApp ==4">
        and order_status = 30
      </if>
      <if test="orderStatusApp ==5">
        and appraise_submited = FALSE AND order_status IN (40,50,60,70,90)
      </if>
    </if>
      <if test="orderStatus != null and orderStatus!=''">
        and order_status in
        <foreach item="item" collection="orderStatus.split(',')" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="orderType != null">
        and order_type = #{orderType}
      </if>
      <if test="serviceCodes != null and serviceCodes!=''">
        and service_code in
        <foreach item="item" collection="serviceCodes.split(',')" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="createTimeStart != null and createTimeStart != ''">
        and DATE(create_time) <![CDATA[>=]]> #{createTimeStart}
      </if>
      <if test="createTimeEnd != null and createTimeEnd != ''">
        and DATE(create_time) <![CDATA[<=]]> #{createTimeEnd}
      </if>
      <if test="bookingOrderStime != null and bookingOrderStime != ''">
        and DATE(booking_order_stime) <![CDATA[>=]]> #{bookingOrderStime}
      </if>
      <if test="bookingOrderEtime != null and bookingOrderEtime != ''">
        and DATE(booking_order_stime) <![CDATA[<=]]> #{bookingOrderEtime}
      </if>
      <if test="bookingPassengerUserName != null and bookingPassengerUserName!=''">
        and booking_passenger_user_name = #{bookingPassengerUserName}
      </if>
      <if test="bookingPassengerUserPhone != null and bookingPassengerUserPhone!=''">
        and booking_passenger_user_phone = #{bookingPassengerUserPhone}
      </if>
      <if test="customerCompanyId != null">
        and customer_company_id = #{customerCompanyId}
      </if>
      <if test="passengerStructId != null">
        and passenger_struct_id = #{passengerStructId}
      </if>
      <if test="orderTypes!=null and orderTypes!=''">
        and order_type in(1,2)
      </if>
    </where>
    order by sordFeild asc,create_time desc
  </select>

  <!-- APP行程列表 客户端数据权限 -->
  <sql id="clientDataPermPageListForApp">

    <!-- 客户端 数据权限不存在不允许查看数据 -->
    <if test="dataPermType==null">
      and id = -1
    </if>

    <!-- 客户端 数据权限 本人：下单人是本人 -->
    <if test="dataPermType!=null and dataPermType==4">
      <choose>
        <when test="loginUserId!=null">
          and customer_id = #{loginUserId}
        </when>
        <otherwise>
          and id = -1
        </otherwise>
      </choose>
    </if>

    <!-- 客户端 数据权限 指定城市：出发地/目的地城市在用户数据权限-指定城市内 ∪ 下单人是本人的行程 -->
    <if test="dataPermType!=null and dataPermType==3">
      <!-- 指定城市并且没有配置城市不允许查看数据 -->
      <choose>
        <when test="dataPermCodeSet!=null and dataPermCodeSet.size()>0">
          and (booking_start_city_code in
          <foreach item="cityCodeItem" collection="dataPermCodeSet" open="(" separator="," close=")">
            #{cityCodeItem}
          </foreach>
          or booking_end_city_code IN
          <foreach item="cityCodeItem" collection="dataPermCodeSet" open="(" separator="," close=")">
            #{cityCodeItem}
          </foreach>
          or customer_id = #{loginUserId}
          )
        </when>
        <otherwise>
          and id = -1
        </otherwise>
      </choose>
    </if>


    <!-- 客户端 数据权限 指定部门：出发地/目的地城市在用户数据权限-指定城市内 ∪ 下单人是本人的行程 -->
    <if test="dataPermType!=null and dataPermType==2">
      <choose>
        <when test="dataPermIdSet!=null and dataPermIdSet.size()>0">
          and (struct_id in
          <foreach item="deptIdItem" collection="dataPermIdSet" open="(" separator="," close=")">
            #{deptIdItem}
          </foreach>
          or passenger_struct_id in
          <foreach item="deptIdItem" collection="dataPermIdSet" open="(" separator="," close=")">
            #{deptIdItem}
          </foreach>
          or customer_id = #{loginUserId}
          )
        </when>
        <otherwise>
          and id = -1
        </otherwise>
      </choose>
    </if>
  </sql>

  <!--APP行程列表-->
  <select id="selectAllPageListForAPP" parameterType="com.izu.mrcar.order.dto.order.OrderApplyListForAppReqDTO" resultMap="mapper.mrcar.OrderApplyMapper.BaseResultMap">
    select order_apply_no,order_type,create_time,booking_order_stime,booking_start_short_addr,
    booking_start_point,booking_end_short_addr,booking_end_point,order_status,service_code,appraise_submited,
    customer_name,booking_passenger_user_name,customer_company_name,booking_order_etime,passenger_struct_name,
    booking_passenger_user_id,company_name,customer_mobile,booking_passenger_user_phone
    from order_apply
    <where>
      <choose>
        <!-- 运营端 能查看的数据范围:运营端暂时不允许查询 -->
        <when test="loginSystemType!=null and loginSystemType==1">
          and order_type = 2
          and id = -1
        </when>
        <!-- 客户端 能查看所有类型的订单-->
        <otherwise>
          <!-- 客户端 代垫司机没有被分派公司时，不允许查询行程列表 -->
          <if test="loginCompanyId == null">
            and id = -1
          </if>
          <!-- 客户端 只能查看本公司的行程数据 -->
          <if test="loginCompanyId!=null">
            and company_id = #{loginCompanyId}
          </if>
          <!-- 引入客户端数据权限 -->
         <include refid="clientDataPermPageListForApp"/>
        </otherwise>
      </choose>
      <if test="companyId != null">
        and company_id = #{companyId}
      </if>
      <if test="structId != null">
        and struct_id = #{structId}
      </if>
      <if test="customerName != null and customerName!=''">
        and customer_name = #{customerName}
      </if>
      <if test="customerMobile != null and customerMobile!=''">
        and customer_mobile = #{customerMobile}
      </if>
      <if test="orderStatus != null and orderStatus!=''">
        and order_status in
        <foreach item="item" collection="orderStatus.split(',')" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="orderType != null">
        and order_type = #{orderType}
      </if>
      <if test="serviceCodes != null and serviceCodes!=''">
        and service_code in
        <foreach item="item" collection="serviceCodes.split(',')" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="createTimeStart != null and createTimeStart != ''">
        and DATE(create_time) <![CDATA[>=]]> #{createTimeStart}
      </if>
      <if test="createTimeEnd != null and createTimeEnd != ''">
        and DATE(create_time) <![CDATA[<=]]> #{createTimeEnd}
      </if>
      <if test="bookingOrderStime != null and bookingOrderStime != ''">
        and DATE(booking_order_stime) <![CDATA[>=]]> #{bookingOrderStime}
      </if>
      <if test="bookingOrderEtime != null and bookingOrderEtime != ''">
        and DATE(booking_order_stime) <![CDATA[<=]]> #{bookingOrderEtime}
      </if>
      <if test="bookingPassengerUserName != null and bookingPassengerUserName!=''">
        and booking_passenger_user_name = #{bookingPassengerUserName}
      </if>
      <if test="bookingPassengerUserPhone != null and bookingPassengerUserPhone!=''">
        and booking_passenger_user_phone = #{bookingPassengerUserPhone}
      </if>
      <if test="customerCompanyId != null">
        and customer_company_id = #{customerCompanyId}
      </if>
      <if test="passengerStructId != null">
        and passenger_struct_id = #{passengerStructId}
      </if>
      <!-- app版本小于 90 不允许展示私车相关订单,只能展示内部用车和商务用车-->
      <if test="appVersion !=null and appVersion lt 90">
        and order_type in(1,2)
      </if>
    </where>
    <choose>
      <when test="bookingOrderStimeSort!=null and bookingOrderStimeSort==1">
        order by booking_order_stime asc
      </when>
      <when test="bookingOrderStimeSort!=null and bookingOrderStimeSort==2">
        order by booking_order_stime desc
      </when>
      <when test="createTimeSort!=null and createTimeSort==1">
        order by create_time asc
      </when>
      <when test="createTimeSort!=null and createTimeSort==2">
        order by create_time desc
      </when>
      <otherwise>
        order by create_time desc
      </otherwise>
    </choose>
  </select>
  <update id="updateOrderAppraiseSubmited">
    update order_apply
    set
      appraise_submited = #{appraiseSubmited,jdbcType=BIT},
      appraise_submit_time = #{appraiseSubmitTime,jdbcType=TIMESTAMP}
    where order_apply_no = #{orderApplyNo,jdbcType=VARCHAR}
  </update>

  <select id="selectOneByOrderApplyNo" resultMap="mapper.mrcar.OrderApplyMapper.BaseResultMap">
    select
    <include refid="mapper.mrcar.OrderApplyMapper.Base_Column_List"/>
    from order_apply
    where order_apply_no =#{orderApplyNo}
  </select>

  <select id="selectListByOrderApplyNoList" resultMap="mapper.mrcar.OrderApplyMapper.BaseResultMap">
    select
    <include refid="mapper.mrcar.OrderApplyMapper.Base_Column_List"/>
    from order_apply
    where order_apply_no IN
    <foreach collection="orderApplyNoList" item="orderApplyNo" index="index" separator="," open="(" close=")">
      #{orderApplyNo}
    </foreach>
  </select>
  <!--超时未调度订单申请列表 -->
  <select id="selectExpireOrderApply" resultMap="mapper.mrcar.OrderApplyMapper.BaseResultMap">
    select <include refid="mapper.mrcar.OrderApplyMapper.Base_Column_List"/>
    from order_apply
    where order_status = 10 and order_type != 2
    limit #{page},#{limit}
  </select>
  <!--自助取还待出发订单申请列表 -->
  <select id="selectExpireSelfHelpRentOrderApply" resultMap="mapper.mrcar.OrderApplyMapper.BaseResultMap">
    select id,order_apply_no,booking_order_stime,audit_flag
    from order_apply
    where order_status = 20 and service_code = 10
    limit #{page},#{limit}
  </select>
  <!--超时未审批订单申请列表 -->
  <select id="selectExpireOrderApplyBeforeApproval" resultMap="mapper.mrcar.OrderApplyMapper.BaseResultMap">
    select <include refid="mapper.mrcar.OrderApplyMapper.Base_Column_List"/>
    from order_apply
    <where>
      approval_status = 5 and order_type != 2
      <if test="companyId != null">
        AND company_id=#{companyId}
      </if>
    </where>
    limit #{page},#{limit}
  </select>

  <update id="updateOrderApplyStatus">
    update order_apply
      set order_status = #{orderStatus}
    where order_apply_no = #{orderApplyNo}
  </update>
  <!-- 批量取消订单申请 -->
  <update id="batchCancle" parameterType="java.util.List">
    update order_apply
    set order_status = 100,order_cancellation_type = 0,order_cancel_time = now()
    where order_apply_no in
    <foreach collection="list" item="orderApplyNo" index="index" separator="," open="(" close=")">
      #{orderApplyNo}
    </foreach>
  </update>

  <select id="isApplyUseCoupon" resultType="int">
    select count(*) from order_apply where order_apply_no = #{orderApplyNo} and coupon_id > 0;
  </select>
  <select id="queryCountOfInProgress" resultType="java.lang.Integer">
    select count(*) from order_apply
    where order_status IN (10,20,30,40,50)
    <if test="customerId != null">
    and customer_id = #{customerId}
    </if>
    <if test="companyId != null">
      and company_id = #{companyId}
    </if>
  </select>
  <update id="abatementFixedPrice">
    update order_apply
      set fixed_price = (fixed_price - #{fixedPrice})
    where order_apply_no = #{orderApplyNo}
  </update>


  <select id="countPrivateCarOrderInProgress" resultType="java.lang.Integer">
     select count(*) from order_apply where order_status in (20,30) and order_type=3 and booking_passenger_user_id=#{passengerUserId}
  </select>

  <select id="queryMyTripCount" resultType="java.lang.Integer">
    select count(*)
    from order_apply
    where order_status in (5,20,10,30)
      and ((order_type=3
      and booking_passenger_user_id=#{passengerUserId})
       or(
          order_type in(1,2)
        and customer_id=#{passengerUserId}))

  </select>

  <!--  行程报表接口迁移-->
  <select id="queryListOfCustomer" resultType="com.izu.mrcar.order.dto.businessOrder.ApplyOrderStatementCustomerDTO">
    select p.struct_name departmentName,p.customer_name customerName,p.customer_mobile customerMobile,
    p.company_id companyId,p.company_name companyName,p.order_type orderType,sum(CASE WHEN b.fixed_price_valid =1 THEN b.fixed_price ELSE b.shouldpay_amount END) totalAmount,count(DISTINCT(sub.applyNo)) orderCount,sum(sub.orderNo) vehicleCount,sum(sub.tm) totalMileage,sum(sub.tt) totalTime from
    order_apply p LEFT JOIN
    (
    select count(o.order_no) orderNo,o.order_apply_no applyNo,sum(b.total_mileage) tm,sum(b.total_time) tt from order_info o LEFT JOIN bill_order b on o.order_no = b.order_no
    where o.order_status = 90
    <if test="companyId != null ">
      <if test="companyId != 1 ">
        and ( o.company_id=#{companyId}
        <if test="dataPermIsNotNull">
          <choose>
            <when test="dataPermType == 1">
              or o.company_id in
              <foreach collection="dataCodeSet" item="item" separator="," open="(" close=")">
                #{item}
              </foreach>
            </when>
          </choose>
        </if>
        )
      </if>
    </if>
    group by o.order_apply_no
    ) sub on p.order_apply_no = sub.applyNo
    LEFT JOIN bill b ON p.order_apply_no = b.order_apply_no
    <where>
      p.order_status = 90
      <if test="customerName != null and customerName != '' ">
        and p.customer_name = #{customerName}
      </if>
      <if test="customerMobile != null and customerMobile != '' ">
        and p.customer_mobile = #{customerMobile}
      </if>
      <if test="companyId != null ">
        <if test="companyId == 1 ">
          and p.order_type = 2
        </if>
        <if test="companyId != 1 ">
          and ( p.company_id=#{companyId}
          <if test="dataPermIsNotNull">
            <choose>
              <when test="dataPermType == 1">
                or p.company_id in
                <foreach collection="dataCodeSet" item="item" separator="," open="(" close=")">
                  #{item}
                </foreach>
              </when>
            </choose>
          </if>
          )
        </if>
      </if>
      <if test="companyIds != null and companyIds.size() > 0 ">
        and p.company_id in
        <foreach item="item" collection="companyIds" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="customerCompanyId != null ">
        and p.company_id = #{customerCompanyId}
      </if>
      <if test="departmentId != null ">
        and p.struct_id = #{departmentId}
      </if>
      <if test="orderType != null ">
        and p.order_type = #{orderType}
      </if>
      <if test="bookingOrderStime != null and bookingOrderStime != ''">
        and p.booking_order_stime <![CDATA[>=]]> #{bookingOrderStime}
      </if>
      <if test="bookingOrderEtime != null and bookingOrderEtime != ''">
        and p.booking_order_stime <![CDATA[<=]]> #{bookingOrderEtime}
      </if>
    </where>
    GROUP BY p.customer_id
    <if test="sortField != null">
      <if test="sortField == 1 ">
        <if test="sortFieldToAsc == null or sortFieldToAsc == false">
          order by totalAmount desc
        </if>
        <if test="sortFieldToAsc == true">
          order by totalAmount asc
        </if>
      </if>
      <if test="sortField == 2 ">
        <if test="sortFieldToAsc == null or sortFieldToAsc == false">
          order by orderCount desc
        </if>
        <if test="sortFieldToAsc == true">
          order by orderCount asc
        </if>
      </if>
      <if test="sortField == 3 ">
        <if test="sortFieldToAsc == null or sortFieldToAsc == false">
          order by vehicleCount desc
        </if>
        <if test="sortFieldToAsc == true">
          order by vehicleCount asc
        </if>
      </if>
      <if test="sortField == 4 ">
        <if test="sortFieldToAsc == null or sortFieldToAsc == false">
          order by totalMileage desc
        </if>
        <if test="sortFieldToAsc == true">
          order by totalMileage asc
        </if>
      </if>
      <if test="sortField == 5 ">
        <if test="sortFieldToAsc == null or sortFieldToAsc == false">
          order by totalTime desc
        </if>
        <if test="sortFieldToAsc == true">
          order by totalTime asc
        </if>
      </if>
    </if>
    <if test="sortField == null">
      order by totalAmount desc
    </if>
  </select>

  <select id="queryListOfPassenger" resultType="com.izu.mrcar.order.dto.businessOrder.ApplyOrderStatementCustomerDTO">
    select p.passenger_struct_name departmentName,p.booking_passenger_user_name customerName,p.booking_passenger_user_phone customerMobile,
    p.customer_company_id companyId,p.customer_company_name companyName,p.order_type orderType,sum(CASE WHEN b.fixed_price_valid =1 THEN b.fixed_price ELSE b.shouldpay_amount END) totalAmount,count(DISTINCT(sub.applyNo)) orderCount,sum(sub.orderNo) vehicleCount,sum(sub.tm) totalMileage,sum(sub.tt) totalTime from
    order_apply p LEFT JOIN
    (
    select count(o.order_no) orderNo,o.order_apply_no applyNo,sum(b.total_mileage) tm,sum(b.total_time) tt from order_info o LEFT JOIN bill_order b on o.order_no = b.order_no
    where o.order_status = 90
    <if test="companyId != null ">
      <if test="companyId != 1 ">
        and ( o.company_id=#{companyId}
        <if test="dataPermIsNotNull">
          <choose>
            <when test="dataPermType == 1">
              or o.company_id in
              <foreach collection="dataCodeSet" item="item" separator="," open="(" close=")">
                #{item}
              </foreach>
            </when>
          </choose>
        </if>
        )
      </if>
    </if>
    group by o.order_apply_no
    ) sub ON p.order_apply_no = sub.applyNo
    LEFT JOIN bill b ON p.order_apply_no = b.order_apply_no
    <where>
      p.order_status = 90
      <if test="customerName != null and customerName != '' ">
        and p.booking_passenger_user_name = #{customerName}
      </if>
      <if test="customerMobile != null and customerMobile != '' ">
        and p.booking_passenger_user_phone = #{customerMobile}
      </if>
      <if test="companyId != null ">
        <if test="companyId == 1 ">
          and p.order_type = 2
        </if>
        <if test="companyId != 1 ">
          and ( p.company_id=#{companyId}
          <if test="dataPermIsNotNull">
            <choose>
              <when test="dataPermType == 1">
                or p.company_id in
                <foreach collection="dataCodeSet" item="item" separator="," open="(" close=")">
                  #{item}
                </foreach>
              </when>
            </choose>
          </if>
          )
        </if>
      </if>
      <if test="companyIds != null and companyIds.size() > 0 ">
        and p.customer_company_id in
        <foreach item="item" collection="companyIds" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="customerCompanyId != null ">
        and p.customer_company_id = #{customerCompanyId}
      </if>
      <if test="departmentId != null ">
        and p.passenger_struct_id = #{departmentId}
      </if>
      <if test="orderType != null ">
        and p.order_type = #{orderType}
      </if>
      <if test="bookingOrderStime != null and bookingOrderStime != ''">
        and p.booking_order_stime <![CDATA[>=]]> #{bookingOrderStime}
      </if>
      <if test="bookingOrderEtime != null and bookingOrderEtime != ''">
        and p.booking_order_stime <![CDATA[<=]]> #{bookingOrderEtime}
      </if>
    </where>
    GROUP BY p.booking_passenger_user_name,p.booking_passenger_user_phone,p.customer_company_id
    <if test="sortField != null">
      <if test="sortField == 1 ">
        <if test="sortFieldToAsc == null or sortFieldToAsc == false">
          order by totalAmount desc
        </if>
        <if test="sortFieldToAsc == true">
          order by totalAmount asc
        </if>
      </if>
      <if test="sortField == 2 ">
        <if test="sortFieldToAsc == null or sortFieldToAsc == false">
          order by orderCount desc
        </if>
        <if test="sortFieldToAsc == true">
          order by orderCount asc
        </if>
      </if>
      <if test="sortField == 3 ">
        <if test="sortFieldToAsc == null or sortFieldToAsc == false">
          order by vehicleCount desc
        </if>
        <if test="sortFieldToAsc == true">
          order by vehicleCount asc
        </if>
      </if>
      <if test="sortField == 4 ">
        <if test="sortFieldToAsc == null or sortFieldToAsc == false">
          order by totalMileage desc
        </if>
        <if test="sortFieldToAsc == true">
          order by totalMileage asc
        </if>
      </if>
      <if test="sortField == 5 ">
        <if test="sortFieldToAsc == null or sortFieldToAsc == false">
          order by totalTime desc
        </if>
        <if test="sortFieldToAsc == true">
          order by totalTime asc
        </if>
      </if>
    </if>
    <if test="sortField == null">
      order by totalAmount desc
    </if>
  </select>

  <select id="queryListOfVehicle" resultType="com.izu.mrcar.order.dto.businessOrder.ApplyOrderStatementVehicleDTO">
    select o.assign_car_license                                                                  vehicleLicense,
    v.belong_city_name                                                                    cityName,
    v.struct_name  structName,
    o.company_id                                                                          companyId,
    count(o.order_no)                                                                     orderCount,
    o.order_type                                                                          orderType,
    sum(CASE WHEN b.fixed_price_valid = 1 THEN b.fixed_price ELSE b.shouldpay_amount END) totalAmount,
    sum(o.trip_mileage)                                                                   totalOrderMileage,
    sum(case
    when b.id is not null then b.total_time
    when (o.fact_end_date != '1970-01-01 00:00:00' and o.fact_start_date != '1970-01-01 00:00:00')
    then TIMESTAMPDIFF(SECOND, o.fact_start_date, o.fact_end_date)
    else 0
    end)                                                                              totalTime
    from order_info o
    LEFT JOIN bill_order b on o.order_no = b.order_no
    LEFT JOIN order_vehicle_base v on o.assign_car_id = v.vehicle_id
    <where>
      o.order_status = 90
      and ( o.company_id=#{companyId}
      <if test="dataPermIsNotNull">
        <choose>
          <when test="dataPermType == 1">
            or o.company_id in
            <foreach collection="dataCodeSet" item="item" separator="," open="(" close=")">
              #{item}
            </foreach>
          </when>
          <when test="dataPermType == 2">
            and (o.struct_id in
            <foreach collection="dataCodeSet" item="item" separator="," open="(" close=")">
              #{item}
            </foreach>
            or o.customer_id = #{staffId}
            )
          </when>
          <when test="dataPermType == 3">
            and (o.start_city_code in
            <foreach collection="dataCodeSet" item="item" separator="," open="(" close=")">
              #{item}
            </foreach>
            or
            o.end_city_code in
            <foreach collection="dataCodeSet" item="item" separator="," open="(" close=")">
              #{item}
            </foreach>
            or o.customer_id = #{staffId}
            )
          </when>
        </choose>
      </if>
      )
      <if test="companyIds != null and companyIds.size() > 0 ">
        and o.company_id in
        <foreach item="item" collection="companyIds" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="vehicleLicense != null and vehicleLicense != '' ">
        and o.assign_car_license = #{vehicleLicense}
      </if>
      <if test="cityCode != null and cityCode != '' ">
        and v.belong_city_code = #{cityCode}
      </if>
      <if test="structId != null ">
        and v.struct_id = #{structId}
      </if>
      <if test="orderTypeList != null and orderTypeList.size() > 0">
        and o.order_type in
        <foreach collection="orderTypeList" item="item" index="index" separator="," open="(" close=")">
          #{item}
        </foreach>
      </if>
      <if test="bookingOrderStime != null and bookingOrderStime != ''">
        and o.booking_order_stime <![CDATA[>=]]> #{bookingOrderStime}
      </if>
      <if test="bookingOrderEtime != null and bookingOrderEtime != ''">
        and o.booking_order_stime <![CDATA[<=]]> #{bookingOrderEtime}
      </if>
    </where>
    GROUP BY o.assign_car_license
    <if test="sortField != null">
      <if test="sortField == 1 ">
        <if test="sortFieldToAsc == null or sortFieldToAsc == false">
          order by totalAmount desc
        </if>
        <if test="sortFieldToAsc == true">
          order by totalAmount asc
        </if>
      </if>
      <if test="sortField == 2 ">
        <if test="sortFieldToAsc == null or sortFieldToAsc == false">
          order by orderCount desc
        </if>
        <if test="sortFieldToAsc == true">
          order by orderCount asc
        </if>
      </if>
      <if test="sortField == 3 ">
        <if test="sortFieldToAsc == null or sortFieldToAsc == false">
          order by totalOrderMileage desc
        </if>
        <if test="sortFieldToAsc == true">
          order by totalOrderMileage asc
        </if>
      </if>
      <if test="sortField == 4 ">
        <if test="sortFieldToAsc == null or sortFieldToAsc == false">
          order by totalTime desc
        </if>
        <if test="sortFieldToAsc == true">
          order by totalTime asc
        </if>
      </if>
    </if>
    <if test="sortField == null">
      order by totalAmount desc
    </if>
  </select>

  <select id="queryListOfDriver" resultType="com.izu.mrcar.order.dto.businessOrder.ApplyOrderStatementDriverDTO">
    SELECT
    o.assign_driver_name assignDriverName,
    o.assign_driver_phone assignDriverPhone,
    o.motorcade_id motorcadeId,
    o.motorcade_name motorcadeName,
    o.order_type orderType,
    o.company_id companyId,
    sum( CASE WHEN b.fixed_price_valid = 1 THEN b.fixed_price ELSE b.shouldpay_amount END ) totalAmount,
    count(1) orderCount,
    sum(b.attach_fee) attachFee,
    sum( b.total_mileage ) totalMileage,
    sum( b.total_time ) totalTime
    FROM
    order_info o
    LEFT JOIN bill_order b ON o.order_no = b.order_no
    <where>
      o.order_status = 90
      <if test="assignDriverName != null and assignDriverName != '' ">
        and o.assign_driver_name = #{assignDriverName}
      </if>
      <if test="assignDriverPhone != null and assignDriverPhone != '' ">
        and o.assign_driver_phone = #{assignDriverPhone}
      </if>
      <if test="companyId != null ">
        <if test="companyId == 1 ">
          and o.order_type = 2
        </if>
        <if test="companyId != 1 ">
          and ( o.company_id=#{companyId}
          <if test="dataPermIsNotNull">
            <choose>
              <when test="dataPermType == 1">
                or o.company_id in
                <foreach collection="dataCodeSet" item="item" separator="," open="(" close=")">
                  #{item}
                </foreach>
              </when>
            </choose>
          </if>
          )
        </if>
      </if>
      <if test="companyIds != null and companyIds.size() > 0 ">
        and o.company_id in
        <foreach item="item" collection="companyIds" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="motorcadeId != null ">
        and o.motorcade_id = #{motorcadeId}
      </if>
      <if test="orderType != null ">
        and o.order_type = #{orderType}
      </if>
      <if test="bookingOrderStime != null and bookingOrderStime != ''">
        and o.booking_order_stime <![CDATA[>=]]> #{bookingOrderStime}
      </if>
      <if test="bookingOrderEtime != null and bookingOrderEtime != ''">
        and o.booking_order_stime <![CDATA[<=]]> #{bookingOrderEtime}
      </if>
    </where>
    GROUP BY o.assign_driver_id
    <if test="sortField != null">
      <if test="sortField == 1 ">
        <if test="sortFieldToAsc == null or sortFieldToAsc == false">
          order by totalAmount desc
        </if>
        <if test="sortFieldToAsc == true">
          order by totalAmount asc
        </if>
      </if>
      <if test="sortField == 2 ">
        <if test="sortFieldToAsc == null or sortFieldToAsc == false">
          order by orderCount desc
        </if>
        <if test="sortFieldToAsc == true">
          order by orderCount asc
        </if>
      </if>
      <if test="sortField == 3 ">
        <if test="sortFieldToAsc == null or sortFieldToAsc == false">
          order by attachFee desc
        </if>
        <if test="sortFieldToAsc == true">
          order by attachFee asc
        </if>
      </if>
      <if test="sortField == 4 ">
        <if test="sortFieldToAsc == null or sortFieldToAsc == false">
          order by totalMileage desc
        </if>
        <if test="sortFieldToAsc == true">
          order by totalMileage asc
        </if>
      </if>
      <if test="sortField == 5 ">
        <if test="sortFieldToAsc == null or sortFieldToAsc == false">
          order by totalTime desc
        </if>
        <if test="sortFieldToAsc == true">
          order by totalTime asc
        </if>
      </if>
    </if>
    <if test="sortField == null">
      order by totalAmount desc
    </if>
  </select>

  <select id="selectInternalByCompany" resultType="com.izu.business.dto.StatisticsBusinessDTO">
    select company_id companyId,count(id) orderApplyInternalCount
    from order_apply
    where order_status not in (8,9,100) and order_type = 1
    and company_id in
    <foreach collection="companyIds" open="(" close=")" item="companyId" separator=",">
      #{companyId}
    </foreach>
    group by company_id
  </select>


  <select id="selectMotorcadeByCompany" resultType="com.izu.business.dto.StatisticsBusinessDTO">
    select company_id companyId,count(id) orderApplyMotorcadeCount
    from order_apply
    where order_status not in (8,9,100) and order_type = 2
    and customer_company_id in
    <foreach collection="companyIds" open="(" close=")" item="companyId" separator=",">
      #{companyId}
    </foreach>
    group by company_id
  </select>

  <select id="getOrderMileageByDate" resultType="com.izu.mrcar.order.dto.businessOrder.OrderInfoMileageByDateDTO">
    select substr(o.booking_order_stime,1,10) as statDate,ifnull(sum(o.trip_mileage),0) as totalMileage
    from order_info o
    left join bill_order b on b.order_no=o.order_no
    where o.order_status=90
    and o.order_type in (1,2)
    <if test="companyId != null">
      and o.company_id = #{companyId}
    </if>
    <if test="deptId != null">
      and o.struct_id = #{deptId}
    </if>
      <if test="structIdList != null and structIdList.size() > 0">
        and o.struct_id in
        <foreach collection="structIdList" item="item" separator="," open="(" close=")">
          #{item}
        </foreach>
      </if>
    <if test="dataPermType != null and dataPermType == 1">
      <if test="dataPermIdSet != null and dataPermIdSet.size() > 0">
        and o.company_id in
        <foreach collection="dataPermIdSet" item="item" separator="," open="(" close=")">
          #{item}
        </foreach>
      </if>
    </if>
    <if test="dataPermType != null and dataPermType == 2">
      <if test="dataPermCodeSet != null and dataPermCodeSet.size() > 0">
        and (o.struct_id in
        <foreach collection="dataPermCodeSet" item="item" separator="," open="(" close=")">
          #{item}
        </foreach>
            or o.customer_id = #{loginUserId}
            )
      and o.company_id = #{loginCompanyId}
      </if>
    </if>
    <if test="dataPermType != null and dataPermType == 3">
      <if test="dataPermCodeSet != null and dataPermCodeSet.size() > 0">
        and (o.start_city_code in
        <foreach collection="dataPermCodeSet" item="item" separator="," open="(" close=")">
          #{item}
        </foreach>
                 or
        o.end_city_code in
        <foreach collection="dataPermCodeSet" item="item" separator="," open="(" close=")">
          #{item}
        </foreach>
            or o.customer_id = #{loginUserId}
        )
      and o.company_id = #{loginCompanyId}
      </if>
    </if>
    <if test="bookingOrderStime != null and bookingOrderStime != ''">
      and o.booking_order_stime <![CDATA[>=]]> #{bookingOrderStime}
    </if>
    <if test="bookingOrderEtime != null and bookingOrderEtime != ''">
      and o.booking_order_stime <![CDATA[<=]]> #{bookingOrderEtime}
    </if>
    group by statDate
  </select>
  <select id="getOrderTypeAndServiceType" resultType="com.izu.order.entity.mrcar.OrderApply">
    select order_type as orderType ,service_code as serviceCode from order_apply where order_apply_no = #{orderApplyNo}
  </select>

  <select id="checkUnOverOrderByParam" resultType="java.lang.String">
    select order_apply_no from order_apply
    <where>
      customer_id = #{customerId} and order_status not in (8,9,60,90,100)
      <if test="orderType!=null">
        and order_type = #{orderType}
      </if>
      <if test="serviceCode!=null and serviceCode!='' ">
        and service_code = #{serviceCode}
      </if>
    </where>
    limit 1
  </select>
  <select id="queryOrderApplyUseExport" resultMap="mapper.mrcar.OrderApplyMapper.BaseResultMap">
    select <include refid="mapper.mrcar.OrderApplyMapper.Base_Column_List"></include>
    from order_apply
    where
    deleted = 0
    <if test="orderApplyNo != null and orderApplyNo != ''">
      and order_apply_no = #{orderApplyNo}
    </if>
    <if test="orderChannelSource !=null and orderChannelSource != ''">
      and order_channel_source = #{orderChannelSource}
    </if>
    <if test="orderType != null ">
      and order_type = #{orderType}
    </if>
    <if test="vehicleCompanyId != null ">
      and EXISTS (
      SELECT 1
      FROM order_info oi
      WHERE
      oi.order_apply_no = order_apply.order_apply_no
      and
      oi.vehicle_company_id =#{vehicleCompanyId}
      and oi.order_type =1
      )
    </if>
    <if test="driverCompanyId != null ">
      and EXISTS (
      SELECT 1
      FROM order_info oi
      WHERE
      oi.order_apply_no = order_apply.order_apply_no
      and
      oi.driver_company_id =#{driverCompanyId}
      and oi.order_type =1
      )
    </if>
    <if test="vehicleCompanyName != null and vehicleCompanyName!=''">
      and EXISTS (
      SELECT 1
      FROM order_info oi
      WHERE
      oi.order_apply_no = order_apply.order_apply_no
      and
      oi.vehicle_company_name like CONCAT('%',#{vehicleCompanyName},'%')
      and oi.order_type =1
      )
    </if>
    <if test="driverCompanyName != null and driverCompanyName!=''">
      and EXISTS (
      SELECT 1
      FROM order_info oi
      WHERE
      oi.order_apply_no = order_apply.order_apply_no
      and
      oi.driver_company_name like CONCAT('%',#{driverCompanyName},'%')
      and oi.order_type =1
      )
    </if>
    <if test="structId != null ">
      and struct_id = #{structId}
    </if>
    <if test="passengerStructId != null ">
      and passenger_struct_id = #{passengerStructId}
    </if>
    <if test="serviceCode != null and serviceCode !=''">
      and service_code in
      <foreach item="item" collection="serviceCode.split(',')" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="customerName != null and customerName != ''">
      and customer_name = #{customerName}
    </if>
    <if test="companyId != null ">
      and company_id = #{companyId}
    </if>
    <if test="auditFlag != null ">
      and audit_flag = #{auditFlag}
    </if>
    <if test="bookingPassengerUserName != null and bookingPassengerUserName != ''">
      and booking_passenger_user_name = #{bookingPassengerUserName}
    </if>
    <if test="customerCompanyId != null ">
      and customer_company_id = #{customerCompanyId}
    </if>
    <if test="orderStatus != null and orderStatus!=''">
      and order_status in
      <foreach item="item" collection="orderStatus.split(',')" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="createTimeStart != null and createTimeStart != ''">
      and create_time <![CDATA[>=]]> #{createTimeStart}
    </if>
    <if test="createTimeEnd != null and createTimeEnd != ''">
      and create_time <![CDATA[<=]]> #{createTimeEnd}
    </if>
    <if test="bookingOrderStime != null and bookingOrderStime != ''">
      and booking_order_stime <![CDATA[>=]]> #{bookingOrderStime}
    </if>
    <if test="bookingOrderEtime != null and bookingOrderEtime != ''">
      and booking_order_stime <![CDATA[<=]]> #{bookingOrderEtime}
    </if>
    <if test="customerMobile != null and customerMobile != ''">
      and customer_mobile = #{customerMobile}
    </if>
    <if test="bookingPassengerUserPhone != null and bookingPassengerUserPhone != ''">
      and booking_passenger_user_phone = #{bookingPassengerUserPhone}
    </if>
    <!--<if test="loginCompanyId != null">-->
    <!--and company_id = #{loginCompanyId}-->
    <!--</if>-->
    <if test="onlySelf != null and onlySelf == true">
      and ((order_type in (1, 2) and customer_id = #{loginId}) or (order_type = 3 and booking_passenger_user_id = #{loginId}))
    </if>

    <if test="dataPermType !=null ">
      <!-- 负责合同  用户的数据权限的合同范围，匹配行程中车辆所属合同 -->
      <if test="dataPermType == 1 ">
        <!-- (商务用车列表 本人创建)-->
         and customer_id = #{loginId}
      </if>
      <!-- 负责客户/指定客户：用户的数据权限的客户名称，匹配行程的下单人企业 或者乘车人企业； -->
      <if test="dataPermType == 2 or  dataPermType == 3 ">
        <if test="dataPermCodeSet != null and dataPermCodeSet.size()>0 ">
          and (company_id in
          <foreach item="item" collection="dataPermCodeSet" open="(" separator="," close=")">
            #{item}
          </foreach>
          or customer_company_id in
          <foreach item="item" collection="dataPermCodeSet" open="(" separator="," close=")">
            #{item}
          </foreach>
          <if test="loginId!=null">
            or customer_id = #{loginId}
          </if>
          )
        </if>
      </if>
      <!-- 指定部门/所属部门：乘车人企业对应的CRM客户的所属部门 是登录用户的数据权限内的部门 -->
      <if test="dataPermType == 4 or dataPermType == 5 ">
        <choose>
          <when test="dataPermCodeSet != null and dataPermCodeSet.size()>0 ">
            and (customer_company_id in
            <foreach item="item" collection="dataPermCodeSet" open="(" separator="," close=")">
              #{item}
            </foreach>
            <if test="loginId!=null">
              or customer_id = #{loginId}
            </if>
            )
          </when>
          <otherwise>
            <if test="loginId!=null">
              and customer_id = #{loginId}
            </if>
          </otherwise>
        </choose>
      </if>
      <!-- 本人 下单人为本人-->
      <if test="dataPermType == 7">
        <choose>
          <when test="loginId!=null">
            and customer_id = #{loginId}
          </when>
          <otherwise>
            and id = -1
          </otherwise>
        </choose>
      </if>
    </if>
    <choose>
      <when test="sort != null and sort != ''">
        order by ${sort}
      </when>
      <otherwise>
        order by create_time desc
      </otherwise>
    </choose>
  </select>

  <select id="listClientOrder4Export" resultMap="mapper.mrcar.OrderApplyMapper.BaseResultMap">
    select <include refid="mapper.mrcar.OrderApplyMapper.Base_Column_List"></include>
    from order_apply
    where
    deleted = 0
    <if test="orderApplyNo != null and orderApplyNo != ''">
      and order_apply_no = #{orderApplyNo}
    </if>
    <if test="orderType != null ">
      and order_type = #{orderType}
    </if>
    <if test="orderTypeList != null and orderTypeList.size()>0 and port==1 and internalSupplierFlag ==0">
      and order_type in
      <foreach item="item" collection="orderTypeList" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="structId != null ">
      and struct_id = #{structId}
    </if>
    <if test="passengerStructId != null ">
      and passenger_struct_id = #{passengerStructId}
    </if>
    <if test="serviceCode != null and serviceCode !=''">
      and service_code in
      <foreach item="item" collection="serviceCode.split(',')" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="customerName != null and customerName != ''">
      and customer_name = #{customerName}
    </if>
    <if test="companyId != null ">
      and company_id = #{companyId}
    </if>
    <if test="auditFlag != null ">
      and audit_flag = #{auditFlag}
    </if>
    <if test="vehicleCompanyId != null ">
      and EXISTS (
      SELECT 1
      FROM order_info oi
      WHERE
      oi.order_apply_no = order_apply.order_apply_no
      and
      oi.vehicle_company_id =#{vehicleCompanyId}
      and oi.order_type =1
      )
    </if>
    <if test="driverCompanyId != null ">
      and EXISTS (
      SELECT 1
      FROM order_info oi
      WHERE
      oi.order_apply_no = order_apply.order_apply_no
      and
      oi.driver_company_id =#{driverCompanyId}
      and oi.order_type =1
      )
    </if>
    <if test="vehicleCompanyName != null and vehicleCompanyName!=''">
      and EXISTS (
      SELECT 1
      FROM order_info oi
      WHERE
      oi.order_apply_no = order_apply.order_apply_no
      and
      oi.vehicle_company_name like CONCAT('%',#{vehicleCompanyName},'%')
      and oi.order_type =1
      )
    </if>
    <if test="driverCompanyName != null and driverCompanyName!=''">
      and EXISTS (
      SELECT 1
      FROM order_info oi
      WHERE
      oi.order_apply_no = order_apply.order_apply_no
      and
      oi.driver_company_name like CONCAT('%',#{driverCompanyName},'%')
      and oi.order_type =1
      )
    </if>
    <if test="bookingPassengerUserName != null and bookingPassengerUserName != ''">
      and booking_passenger_user_name = #{bookingPassengerUserName}
    </if>
    <if test="customerCompanyId != null ">
      and customer_company_id = #{customerCompanyId}
    </if>
    <if test="orderStatus != null and orderStatus!=''">
      and order_status in
      <foreach item="item" collection="orderStatus.split(',')" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="createTimeStart != null and createTimeStart != ''">
      and create_time <![CDATA[>=]]> #{createTimeStart}
    </if>
    <if test="createTimeEnd != null and createTimeEnd != ''">
      and create_time <![CDATA[<=]]> #{createTimeEnd}
    </if>
    <if test="bookingOrderStime != null and bookingOrderStime != ''">
      and booking_order_stime <![CDATA[>=]]> #{bookingOrderStime}
    </if>
    <if test="bookingOrderEtime != null and bookingOrderEtime != ''">
      and booking_order_stime <![CDATA[<=]]> #{bookingOrderEtime}
    </if>
    <if test="customerMobile != null and customerMobile != ''">
      and customer_mobile = #{customerMobile}
    </if>
    <if test="bookingPassengerUserPhone != null and bookingPassengerUserPhone != ''">
      and booking_passenger_user_phone = #{bookingPassengerUserPhone}
    </if>
    <if test="onlySelf != null and onlySelf == true">
      and ((order_type in (1, 2) and customer_id = #{loginId}) or (order_type = 3 and booking_passenger_user_id = #{loginId}))
    </if>
    <if test="internalSupplierFlag != null and internalSupplierFlag ==0">
    <if test="permissions != null and permissions != ''">
      <!--指定部门 -->
      <if test="dataPermType!=null and dataPermType ==2">
        and (struct_id in
        <foreach item="item" collection="permissions.split(',')" open="(" separator="," close=")">
          #{item}
        </foreach>
        or passenger_struct_id IN
        <foreach item="item" collection="permissions.split(',')" open="(" separator="," close=")">
          #{item}
        </foreach>
        or customer_id = #{loginId}
        )
      </if>
      <!--指定城市 -->
      <if test="dataPermType!=null and dataPermType ==3">
        and (booking_start_city_code in
        <foreach item="cityCode" collection="permissions.split(',')" open="(" separator="," close=")">
          #{cityCode}
        </foreach>
        or booking_end_city_code IN
        <foreach item="cityCode" collection="permissions.split(',')" open="(" separator="," close=")">
          #{cityCode}
        </foreach>
        or customer_id = #{loginId}
        )
      </if>
    </if>
    <!--本人权限 -->
    <if test="dataPermType!=null and dataPermType ==4">
      and (customer_id = #{loginId})
    </if>
    </if>
    <if test="internalSupplierFlag != null and internalSupplierFlag ==1">
      and EXISTS (
      SELECT 1
      FROM order_info oi
      WHERE
      oi.order_apply_no = order_apply.order_apply_no
      and
      oi.vehicle_company_id =#{loginCompanyId}
      and
      oi.driver_company_id =#{loginCompanyId}
      and
      oi.order_type =1
      )
    </if>
    <if test="companyIds != null and companyIds != ''">
      and company_id in
      <foreach item="item" collection="companyIds.split(',')" open="(" separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="auditFlag != null">
      and audit_flag = #{auditFlag}
    </if>
    <choose>
      <when test="sort != null and sort != ''">
        order by ${sort}
      </when>
      <otherwise>
        order by create_time desc
      </otherwise>
    </choose>
  </select>


</mapper>