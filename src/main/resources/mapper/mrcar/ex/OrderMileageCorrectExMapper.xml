<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.OrderMileageCorrectExMapper">
  <select id="selectByList"  resultMap="mapper.mrcar.OrderMileageCorrectMapper.BaseResultMap">
    select
    <include refid="mapper.mrcar.OrderMileageCorrectMapper.Base_Column_List" />
    from order_mileage_correct
    <where>
      <if test="assignCarLicense!=null and assignCarLicense !=''">
        and assign_car_license = #{assignCarLicense}
      </if>

      <if test="operateTimeStart != null and operateTimeStart != ''">
        and deal_time <![CDATA[>=]]> #{operateTimeStart}
      </if>
      <if test="operateTimeEnd != null and operateTimeEnd != ''">
        and deal_time <![CDATA[<=]]> #{operateTimeEnd}
      </if>

      <if test="factStartDateS != null and factStartDateS != ''">
        and fact_start_date <![CDATA[>=]]> #{factStartDateS}
      </if>
      <if test="factStartDateE != null and factStartDateE != ''">
        and fact_start_date <![CDATA[<=]]> #{factStartDateE}
      </if>

      <if test="factEndDateS != null and factEndDateS != ''">
        and fact_end_date <![CDATA[>=]]> #{factEndDateS}
      </if>
      <if test="factEndDateE != null and factEndDateE != ''">
        and fact_end_date <![CDATA[<=]]> #{factEndDateE}
      </if>

      <if test="orderNo != null and orderNo != ''">
        and order_no = #{orderNo}
      </if>
      <if test="status != null">
        and status = #{status}
      </if>
      <if test="companyName != null and companyName != ''">
        and company_name like  CONCAT('%', #{companyName}, '%')
      </if>
      <if test="companyCode != null and companyCode != ''">
        and company_code = #{companyCode}
      </if>
      <if test="dealName != null and dealName != ''">
        and deal_name like  CONCAT('%', #{dealName}, '%')
      </if>
    </where>
    order by update_time desc
  </select>

  <update id="updateCorrectRecord" parameterType="com.izu.order.entity.mrcar.OrderMileageCorrect">
    update order_mileage_correct
    <set>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=TINYINT},
      </if>
      <if test="serviceCode != null">
        service_code = #{serviceCode,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
    </set>
    where correct_no = #{correctNo,jdbcType=VARCHAR}
  </update>
</mapper>