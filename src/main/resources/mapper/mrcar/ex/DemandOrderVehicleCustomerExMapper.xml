<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.DemandOrderVehicleCustomerExMapper">
    <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.DemandOrderVehicleCustomer"
               extends="mapper.mrcar.DemandOrderVehicleCustomerMapper.BaseResultMap" />

    <select id="selectByDemandOrderNum" resultMap="BaseResultMap">
        select <include refid="mapper.mrcar.DemandOrderVehicleCustomerMapper.Base_Column_List"/>
        from so_demand_order_vehicle_customer
        where demand_order_num = #{demandOrderNum,jdbcType=VARCHAR} and del_tag = 0
    </select>
</mapper>