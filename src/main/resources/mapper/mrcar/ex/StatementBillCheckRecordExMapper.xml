<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.StatementBillCheckRecordExMapper">
    <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.StatementBillCheckRecord"
               extends="mapper.mrcar.StatementBillCheckRecordMapper.BaseResultMap">
    </resultMap>

    <sql id="Base_Column_List">
        <include refid="mapper.mrcar.StatementBillCheckRecordMapper.Base_Column_List"/>
    </sql>
    <select id="selectByIncomeBillNum" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from so_statement_bill_check_record where income_bill_num=#{incomeBillNum} and check_result=#{checkResult}
    </select>
    <select id="selectByIncomeBillNumAndRoleType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from so_statement_bill_check_record
        where income_bill_num=#{incomeBillNum} and role_type=#{roleType}
        order by id desc limit 1
    </select>
    <select id="selectByIncomeBillNumAndRoleTypeAndCheckResult" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from so_statement_bill_check_record
        where income_bill_num=#{incomeBillNum} and role_type=#{roleType} and check_result=#{checkResult}
        order by id desc limit 1
    </select>
</mapper>