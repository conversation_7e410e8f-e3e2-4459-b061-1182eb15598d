<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.ScheduleSubVehicleExMapper">
    <resultMap id="BaseResultMap" type="com.izu.mrcar.order.dto.dispatching.SubScheduleVehicleDTO" extends="mapper.mrcar.ScheduleSubVehicleMapper.BaseResultMap">

    </resultMap>
    <sql id="Base_Column_List">
        <include refid="mapper.mrcar.ScheduleSubVehicleMapper.Base_Column_List"/>
    </sql>
    <select id="listVehicleInfoBySub" resultType="java.util.Map">
        select car_level_id AS carLevelId, car_level_name AS carLevelName, vehicle_count AS vehicleCount,
        vehicle_round_on AS vehicleImageOn, vehicle_round_off AS vehicleImageOff from schedule_sub_vehicle t1 LEFT JOIN car_level t2 ON level_id=car_level_id
        <where>
            <if test="subScheduleId!=null ">
               AND t1.valid=TRUE
            </if>
            <if test="subScheduleId!=null ">
               AND sub_schedule_id=#{subScheduleId,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="listVehicleBySubIdForApp" resultType="com.izu.mrcar.order.dto.dispatching.ScheduleVehicleImgDTO">
        select car_level_id AS bookingCarlevelId, car_level_name AS bookingCarlevelName, vehicle_count AS bookingVehicleCount
        from schedule_sub_vehicle
        <where>
               valid=TRUE
            <if test="subScheduleId!=null ">
               AND sub_schedule_id=#{subScheduleId}
            </if>
        </where>
    </select>

    <select id="listVehicleInfoByOrderNo" resultType="java.util.Map">
        select booking_carlevel_id AS carLevelId,
        booking_carlevel_name AS carLevelName,
        booking_vehicle_count AS vehicleCount,
        vehicle_round_on AS vehicleImageOn, vehicle_round_off AS vehicleImageOff
        from order_apply_vehicle LEFT JOIN car_level ON level_id=booking_carlevel_id
        WHERE order_apply_no=#{orderApplyNo}
    </select>

    <insert id="batchInsert">
        INSERT INTO schedule_sub_vehicle
        (sub_schedule_id,schedule_id,order_apply_id,order_apply_no,car_level_id,car_level_name,vehicle_count)
        VALUES
        <foreach collection="subScheduleVehicles" item="item" separator=",">
            (#{subScheduleId},#{item.scheduleId},#{item.orderApplyId},#{item.orderApplyNo},#{item.carLevelId},#{item.carLevelName},#{item.vehicleCount})
        </foreach>
    </insert>

    <update id="reduceVehicleCountByIdType">
        update schedule_sub_vehicle set vehicle_count=vehicle_count-1 WHERE sub_schedule_id = #{subScheduleId} AND car_level_id=#{carLevelId}
    </update>

</mapper>