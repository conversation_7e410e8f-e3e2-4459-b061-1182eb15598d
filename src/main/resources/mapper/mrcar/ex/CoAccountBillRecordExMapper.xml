<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.CoAccountBillRecordExMapper">
  <select id="selectLastedRecord" resultMap="mapper.mrcar.CoAccountBillRecordMapper.BaseResultMap">
    select 
    <include refid="mapper.mrcar.CoAccountBillRecordMapper.Base_Column_List" />
    from co_account_bill_record
    where company_id = #{companyId,jdbcType=INTEGER} and struct_code = #{structCode,jdbcType=VARCHAR} order by bill_end_date desc limit 1
  </select>
</mapper>