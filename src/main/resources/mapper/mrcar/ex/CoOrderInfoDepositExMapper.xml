<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.CoOrderInfoDepositExMapper">

    <select id="queryTimeoutOrderDeposit" parameterType="map"
            resultMap="mapper.mrcar.CoOrderInfoDepositMapper.BaseResultMap">
        select
        coid.*
        from co_order_info_deposit coid inner join co_order_info coi on coi.order_num  = coid.order_num
        where coi.order_status = #{orderStatus} and coid.unfreeze_time<![CDATA[<=]]> #{date} and coid.deposit_type=#{depositType}
        and coid.remaining_deposit>0 and coid.unfreeze_status not in (1,2)

    </select>

    <select id="queryTimeoutOrderDepositUnfreeze" resultMap="mapper.mrcar.CoOrderInfoDepositMapper.BaseResultMap">
        select
        coid.*
        from co_order_info_deposit coid inner join co_order_info coi on coi.order_num  = coid.order_num
        where  coid.remaining_deposit>0 and coid.unfreeze_status not in (1,2) and coi.order_status = 50 or coi.deposit_free_type !=0
    </select>
</mapper>