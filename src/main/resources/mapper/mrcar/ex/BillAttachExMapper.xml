<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ex.BillAttachExMapper">
    <select id="selectByOrderNo" resultMap="mapper.mrcar.BillAttachMapper.BaseResultMap">
        select
        <include refid="mapper.mrcar.BillAttachMapper.Base_Column_List" />
        from bill_attach
        where order_no = #{orderNo} order by attach_code asc
    </select>
    <insert id="insertBatch" parameterType="java.util.List">
        insert into bill_attach (bill_attach_no,
        order_apply_no, order_no, bill_no,
        attach_code, fee_amount,remarks)
        values
        <foreach collection="list" item="billAttach" index="index" separator=",">
            (
            #{billAttach.billAttachNo},
            #{billAttach.orderApplyNo},
            #{billAttach.orderNo},
            #{billAttach.billNo},
            #{billAttach.attachCode},
            #{billAttach.feeAmount},
            #{billAttach.remarks}
            )
        </foreach>
    </insert>

    <select id="getAdditionalRecording" resultMap="BaseResultMapEx">
        SELECT
        a.*
        FROM
        bill_attach AS a
        LEFT JOIN order_info AS o ON o.order_no = a.order_no
        WHERE
        o.fixed_price_valid = 0
        AND a.attach_status = 1
        AND o.order_type = 2
        <if test="cityCode!=null">
            AND o.assign_driver_city_code = #{cityCode}
        </if>
        <if test="statsStartTime!=null and statsStartTime!=''">
            AND a.create_time &gt;= #{statsStartTime}
        </if>
        <if test="statsEndTime!=null and statsEndTime!=''">
            AND a.create_time &lt;= #{statsEndTime}
        </if>
        group by a.order_no,attach_code
    </select>

    <select id="getAdditionalRecordingByBussCode" resultMap="BaseResultMapEx">
        SELECT
        a.*
        FROM
        bill_attach AS a
        LEFT JOIN order_info AS o ON o.order_no = a.order_no
        LEFT JOIN order_vehicle_base AS t ON o.assign_car_id = t.vehicle_id
        WHERE
        o.fixed_price_valid = 0
        AND a.attach_status = 1
        AND o.order_type = 2
        <if test="operateBussCode!=null and operateBussCode!=''">
            and t.operate_buss_code = #{operateBussCode}
        </if>
        <if test="statsStartTime!=null and statsStartTime!=''">
            AND a.create_time &gt;= #{statsStartTime}
        </if>
        <if test="statsEndTime!=null and statsEndTime!=''">
            AND a.create_time &lt;= #{statsEndTime}
        </if>
        group by a.order_no,attach_code
    </select>

    <resultMap id="BaseResultMapEx" type="com.izu.mrcar.order.dto.mrcar.BillAttachDTO">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="bill_attach_no" jdbcType="VARCHAR" property="billAttachNo" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="order_apply_no" jdbcType="VARCHAR" property="orderApplyNo" />
        <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
        <result column="bill_no" jdbcType="VARCHAR" property="billNo" />
        <result column="attach_code" jdbcType="TINYINT" property="attachCode" />
        <result column="fee_amount" jdbcType="DECIMAL" property="feeAmount" />
        <result column="attach_status" jdbcType="TINYINT" property="attachStatus" />
        <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    </resultMap>

    <select id="selectByOrderApplyNo" resultMap="mapper.mrcar.BillAttachMapper.BaseResultMap">
        select
        <include refid="mapper.mrcar.BillAttachMapper.Base_Column_List" />
        from bill_attach
        where order_apply_no = #{orderApplyNo} order by attach_code asc
    </select>
</mapper>