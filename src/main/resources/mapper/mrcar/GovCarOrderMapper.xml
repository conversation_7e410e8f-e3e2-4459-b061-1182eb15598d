<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.GovCarOrderMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.GovCarOrder">
    <id column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="create_id" jdbcType="INTEGER" property="createId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_id" jdbcType="INTEGER" property="updateId" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="order_device_source" jdbcType="VARCHAR" property="orderDeviceSource" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="struct_id" jdbcType="INTEGER" property="structId" />
    <result column="struct_code" jdbcType="VARCHAR" property="structCode" />
    <result column="struct_name" jdbcType="VARCHAR" property="structName" />
    <result column="customer_id" jdbcType="INTEGER" property="customerId" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="customer_mobile" jdbcType="VARCHAR" property="customerMobile" />
    <result column="order_status" jdbcType="TINYINT" property="orderStatus" />
    <result column="order_settle_status" jdbcType="TINYINT" property="orderSettleStatus" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="vehicle_serial_no" jdbcType="VARCHAR" property="vehicleSerialNo" />
    <result column="vehicle_license" jdbcType="VARCHAR" property="vehicleLicense" />
    <result column="vehicle_vin" jdbcType="VARCHAR" property="vehicleVin" />
    <result column="vehicle_brand_code" jdbcType="VARCHAR" property="vehicleBrandCode" />
    <result column="vehicle_brand_name" jdbcType="VARCHAR" property="vehicleBrandName" />
    <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode" />
    <result column="vehicle_model_name" jdbcType="VARCHAR" property="vehicleModelName" />
    <result column="vehicle_struct_id" jdbcType="INTEGER" property="vehicleStructId" />
    <result column="vehicle_struct_code" jdbcType="VARCHAR" property="vehicleStructCode" />
    <result column="vehicle_struct_name" jdbcType="VARCHAR" property="vehicleStructName" />
    <result column="vehicle_pic_url" jdbcType="VARCHAR" property="vehiclePicUrl" />
    <result column="order_user_memo" jdbcType="VARCHAR" property="orderUserMemo" />
    <result column="initial_fence_id" jdbcType="INTEGER" property="initialFenceId" />
    <result column="initial_latitude" jdbcType="DECIMAL" property="initialLatitude" />
    <result column="initial_longitude" jdbcType="DECIMAL" property="initialLongitude" />
    <result column="initial_location" jdbcType="VARCHAR" property="initialLocation" />
    <result column="return_fence_id" jdbcType="INTEGER" property="returnFenceId" />
    <result column="return_latitude" jdbcType="DECIMAL" property="returnLatitude" />
    <result column="return_longitude" jdbcType="DECIMAL" property="returnLongitude" />
    <result column="return_location" jdbcType="VARCHAR" property="returnLocation" />
    <result column="order_payment_time" jdbcType="TIMESTAMP" property="orderPaymentTime" />
    <result column="confirm_time" jdbcType="TIMESTAMP" property="confirmTime" />
    <result column="expected_pickup_time" jdbcType="TIMESTAMP" property="expectedPickupTime" />
    <result column="expected_return_time" jdbcType="TIMESTAMP" property="expectedReturnTime" />
    <result column="operation_pickup_time" jdbcType="TIMESTAMP" property="operationPickupTime" />
    <result column="actual_pickup_time" jdbcType="TIMESTAMP" property="actualPickupTime" />
    <result column="pickup_lot_exit_time" jdbcType="TIMESTAMP" property="pickupLotExitTime" />
    <result column="operation_return_time" jdbcType="TIMESTAMP" property="operationReturnTime" />
    <result column="actual_return_time" jdbcType="TIMESTAMP" property="actualReturnTime" />
    <result column="return_lot_entry_time" jdbcType="TIMESTAMP" property="returnLotEntryTime" />
    <result column="order_settle_time" jdbcType="TIMESTAMP" property="orderSettleTime" />
    <result column="rental_hours" jdbcType="INTEGER" property="rentalHours" />
    <result column="order_cancellation_time" jdbcType="TIMESTAMP" property="orderCancellationTime" />
    <result column="appraise_submited" jdbcType="BIT" property="appraiseSubmited" />
    <result column="appraise_submit_time" jdbcType="TIMESTAMP" property="appraiseSubmitTime" />
    <result column="return_fence_name" jdbcType="VARCHAR" property="returnFenceName" />
    <result column="initial_fence_name" jdbcType="VARCHAR" property="initialFenceName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    order_no, create_id, create_name, create_time, update_id, update_name, update_time, 
    order_device_source, company_code, company_name, struct_id, struct_code, struct_name, 
    customer_id, customer_name, customer_mobile, order_status, order_settle_status, vehicle_id, 
    vehicle_serial_no, vehicle_license, vehicle_vin, vehicle_brand_code, vehicle_brand_name, 
    vehicle_model_code, vehicle_model_name, vehicle_struct_id, vehicle_struct_code, vehicle_struct_name, 
    vehicle_pic_url, order_user_memo, initial_fence_id, initial_latitude, initial_longitude, 
    initial_location, return_fence_id, return_latitude, return_longitude, return_location, 
    order_payment_time, confirm_time, expected_pickup_time, expected_return_time, operation_pickup_time, 
    actual_pickup_time, pickup_lot_exit_time, operation_return_time, actual_return_time, 
    return_lot_entry_time, order_settle_time, rental_hours, order_cancellation_time, 
    appraise_submited, appraise_submit_time, return_fence_name, initial_fence_name
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.GovCarOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from gov_car_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from gov_car_order
    where order_no = #{orderNo,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from gov_car_order
    where order_no = #{orderNo,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.GovCarOrder">
    insert into gov_car_order (order_no, create_id, create_name, 
      create_time, update_id, update_name, 
      update_time, order_device_source, company_code, 
      company_name, struct_id, struct_code, 
      struct_name, customer_id, customer_name, 
      customer_mobile, order_status, order_settle_status, 
      vehicle_id, vehicle_serial_no, vehicle_license, 
      vehicle_vin, vehicle_brand_code, vehicle_brand_name, 
      vehicle_model_code, vehicle_model_name, vehicle_struct_id, 
      vehicle_struct_code, vehicle_struct_name, vehicle_pic_url, 
      order_user_memo, initial_fence_id, initial_latitude, 
      initial_longitude, initial_location, return_fence_id, 
      return_latitude, return_longitude, return_location, 
      order_payment_time, confirm_time, expected_pickup_time, 
      expected_return_time, operation_pickup_time, 
      actual_pickup_time, pickup_lot_exit_time, 
      operation_return_time, actual_return_time, 
      return_lot_entry_time, order_settle_time, 
      rental_hours, order_cancellation_time, appraise_submited, 
      appraise_submit_time, return_fence_name, 
      initial_fence_name)
    values (#{orderNo,jdbcType=VARCHAR}, #{createId,jdbcType=INTEGER}, #{createName,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateId,jdbcType=INTEGER}, #{updateName,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{orderDeviceSource,jdbcType=VARCHAR}, #{companyCode,jdbcType=VARCHAR}, 
      #{companyName,jdbcType=VARCHAR}, #{structId,jdbcType=INTEGER}, #{structCode,jdbcType=VARCHAR}, 
      #{structName,jdbcType=VARCHAR}, #{customerId,jdbcType=INTEGER}, #{customerName,jdbcType=VARCHAR}, 
      #{customerMobile,jdbcType=VARCHAR}, #{orderStatus,jdbcType=TINYINT}, #{orderSettleStatus,jdbcType=TINYINT}, 
      #{vehicleId,jdbcType=BIGINT}, #{vehicleSerialNo,jdbcType=VARCHAR}, #{vehicleLicense,jdbcType=VARCHAR}, 
      #{vehicleVin,jdbcType=VARCHAR}, #{vehicleBrandCode,jdbcType=VARCHAR}, #{vehicleBrandName,jdbcType=VARCHAR}, 
      #{vehicleModelCode,jdbcType=VARCHAR}, #{vehicleModelName,jdbcType=VARCHAR}, #{vehicleStructId,jdbcType=INTEGER}, 
      #{vehicleStructCode,jdbcType=VARCHAR}, #{vehicleStructName,jdbcType=VARCHAR}, #{vehiclePicUrl,jdbcType=VARCHAR}, 
      #{orderUserMemo,jdbcType=VARCHAR}, #{initialFenceId,jdbcType=INTEGER}, #{initialLatitude,jdbcType=DECIMAL}, 
      #{initialLongitude,jdbcType=DECIMAL}, #{initialLocation,jdbcType=VARCHAR}, #{returnFenceId,jdbcType=INTEGER}, 
      #{returnLatitude,jdbcType=DECIMAL}, #{returnLongitude,jdbcType=DECIMAL}, #{returnLocation,jdbcType=VARCHAR}, 
      #{orderPaymentTime,jdbcType=TIMESTAMP}, #{confirmTime,jdbcType=TIMESTAMP}, #{expectedPickupTime,jdbcType=TIMESTAMP}, 
      #{expectedReturnTime,jdbcType=TIMESTAMP}, #{operationPickupTime,jdbcType=TIMESTAMP}, 
      #{actualPickupTime,jdbcType=TIMESTAMP}, #{pickupLotExitTime,jdbcType=TIMESTAMP}, 
      #{operationReturnTime,jdbcType=TIMESTAMP}, #{actualReturnTime,jdbcType=TIMESTAMP}, 
      #{returnLotEntryTime,jdbcType=TIMESTAMP}, #{orderSettleTime,jdbcType=TIMESTAMP}, 
      #{rentalHours,jdbcType=INTEGER}, #{orderCancellationTime,jdbcType=TIMESTAMP}, #{appraiseSubmited,jdbcType=BIT}, 
      #{appraiseSubmitTime,jdbcType=TIMESTAMP}, #{returnFenceName,jdbcType=VARCHAR}, 
      #{initialFenceName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.GovCarOrder">
    insert into gov_car_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="createId != null">
        create_id,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateId != null">
        update_id,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="orderDeviceSource != null">
        order_device_source,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="structId != null">
        struct_id,
      </if>
      <if test="structCode != null">
        struct_code,
      </if>
      <if test="structName != null">
        struct_name,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="customerMobile != null">
        customer_mobile,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="orderSettleStatus != null">
        order_settle_status,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="vehicleSerialNo != null">
        vehicle_serial_no,
      </if>
      <if test="vehicleLicense != null">
        vehicle_license,
      </if>
      <if test="vehicleVin != null">
        vehicle_vin,
      </if>
      <if test="vehicleBrandCode != null">
        vehicle_brand_code,
      </if>
      <if test="vehicleBrandName != null">
        vehicle_brand_name,
      </if>
      <if test="vehicleModelCode != null">
        vehicle_model_code,
      </if>
      <if test="vehicleModelName != null">
        vehicle_model_name,
      </if>
      <if test="vehicleStructId != null">
        vehicle_struct_id,
      </if>
      <if test="vehicleStructCode != null">
        vehicle_struct_code,
      </if>
      <if test="vehicleStructName != null">
        vehicle_struct_name,
      </if>
      <if test="vehiclePicUrl != null">
        vehicle_pic_url,
      </if>
      <if test="orderUserMemo != null">
        order_user_memo,
      </if>
      <if test="initialFenceId != null">
        initial_fence_id,
      </if>
      <if test="initialLatitude != null">
        initial_latitude,
      </if>
      <if test="initialLongitude != null">
        initial_longitude,
      </if>
      <if test="initialLocation != null">
        initial_location,
      </if>
      <if test="returnFenceId != null">
        return_fence_id,
      </if>
      <if test="returnLatitude != null">
        return_latitude,
      </if>
      <if test="returnLongitude != null">
        return_longitude,
      </if>
      <if test="returnLocation != null">
        return_location,
      </if>
      <if test="orderPaymentTime != null">
        order_payment_time,
      </if>
      <if test="confirmTime != null">
        confirm_time,
      </if>
      <if test="expectedPickupTime != null">
        expected_pickup_time,
      </if>
      <if test="expectedReturnTime != null">
        expected_return_time,
      </if>
      <if test="operationPickupTime != null">
        operation_pickup_time,
      </if>
      <if test="actualPickupTime != null">
        actual_pickup_time,
      </if>
      <if test="pickupLotExitTime != null">
        pickup_lot_exit_time,
      </if>
      <if test="operationReturnTime != null">
        operation_return_time,
      </if>
      <if test="actualReturnTime != null">
        actual_return_time,
      </if>
      <if test="returnLotEntryTime != null">
        return_lot_entry_time,
      </if>
      <if test="orderSettleTime != null">
        order_settle_time,
      </if>
      <if test="rentalHours != null">
        rental_hours,
      </if>
      <if test="orderCancellationTime != null">
        order_cancellation_time,
      </if>
      <if test="appraiseSubmited != null">
        appraise_submited,
      </if>
      <if test="appraiseSubmitTime != null">
        appraise_submit_time,
      </if>
      <if test="returnFenceName != null">
        return_fence_name,
      </if>
      <if test="initialFenceName != null">
        initial_fence_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="createId != null">
        #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderDeviceSource != null">
        #{orderDeviceSource,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="structId != null">
        #{structId,jdbcType=INTEGER},
      </if>
      <if test="structCode != null">
        #{structCode,jdbcType=VARCHAR},
      </if>
      <if test="structName != null">
        #{structName,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=INTEGER},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerMobile != null">
        #{customerMobile,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=TINYINT},
      </if>
      <if test="orderSettleStatus != null">
        #{orderSettleStatus,jdbcType=TINYINT},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleSerialNo != null">
        #{vehicleSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="vehicleLicense != null">
        #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="vehicleVin != null">
        #{vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBrandCode != null">
        #{vehicleBrandCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBrandName != null">
        #{vehicleBrandName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelCode != null">
        #{vehicleModelCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelName != null">
        #{vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleStructId != null">
        #{vehicleStructId,jdbcType=INTEGER},
      </if>
      <if test="vehicleStructCode != null">
        #{vehicleStructCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleStructName != null">
        #{vehicleStructName,jdbcType=VARCHAR},
      </if>
      <if test="vehiclePicUrl != null">
        #{vehiclePicUrl,jdbcType=VARCHAR},
      </if>
      <if test="orderUserMemo != null">
        #{orderUserMemo,jdbcType=VARCHAR},
      </if>
      <if test="initialFenceId != null">
        #{initialFenceId,jdbcType=INTEGER},
      </if>
      <if test="initialLatitude != null">
        #{initialLatitude,jdbcType=DECIMAL},
      </if>
      <if test="initialLongitude != null">
        #{initialLongitude,jdbcType=DECIMAL},
      </if>
      <if test="initialLocation != null">
        #{initialLocation,jdbcType=VARCHAR},
      </if>
      <if test="returnFenceId != null">
        #{returnFenceId,jdbcType=INTEGER},
      </if>
      <if test="returnLatitude != null">
        #{returnLatitude,jdbcType=DECIMAL},
      </if>
      <if test="returnLongitude != null">
        #{returnLongitude,jdbcType=DECIMAL},
      </if>
      <if test="returnLocation != null">
        #{returnLocation,jdbcType=VARCHAR},
      </if>
      <if test="orderPaymentTime != null">
        #{orderPaymentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="confirmTime != null">
        #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expectedPickupTime != null">
        #{expectedPickupTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expectedReturnTime != null">
        #{expectedReturnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operationPickupTime != null">
        #{operationPickupTime,jdbcType=TIMESTAMP},
      </if>
      <if test="actualPickupTime != null">
        #{actualPickupTime,jdbcType=TIMESTAMP},
      </if>
      <if test="pickupLotExitTime != null">
        #{pickupLotExitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operationReturnTime != null">
        #{operationReturnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="actualReturnTime != null">
        #{actualReturnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="returnLotEntryTime != null">
        #{returnLotEntryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderSettleTime != null">
        #{orderSettleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="rentalHours != null">
        #{rentalHours,jdbcType=INTEGER},
      </if>
      <if test="orderCancellationTime != null">
        #{orderCancellationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="appraiseSubmited != null">
        #{appraiseSubmited,jdbcType=BIT},
      </if>
      <if test="appraiseSubmitTime != null">
        #{appraiseSubmitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="returnFenceName != null">
        #{returnFenceName,jdbcType=VARCHAR},
      </if>
      <if test="initialFenceName != null">
        #{initialFenceName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.GovCarOrderExample" resultType="java.lang.Long">
    select count(*) from gov_car_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update gov_car_order
    <set>
      <if test="row.orderNo != null">
        order_no = #{row.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="row.createId != null">
        create_id = #{row.createId,jdbcType=INTEGER},
      </if>
      <if test="row.createName != null">
        create_name = #{row.createName,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateId != null">
        update_id = #{row.updateId,jdbcType=INTEGER},
      </if>
      <if test="row.updateName != null">
        update_name = #{row.updateName,jdbcType=VARCHAR},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.orderDeviceSource != null">
        order_device_source = #{row.orderDeviceSource,jdbcType=VARCHAR},
      </if>
      <if test="row.companyCode != null">
        company_code = #{row.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="row.companyName != null">
        company_name = #{row.companyName,jdbcType=VARCHAR},
      </if>
      <if test="row.structId != null">
        struct_id = #{row.structId,jdbcType=INTEGER},
      </if>
      <if test="row.structCode != null">
        struct_code = #{row.structCode,jdbcType=VARCHAR},
      </if>
      <if test="row.structName != null">
        struct_name = #{row.structName,jdbcType=VARCHAR},
      </if>
      <if test="row.customerId != null">
        customer_id = #{row.customerId,jdbcType=INTEGER},
      </if>
      <if test="row.customerName != null">
        customer_name = #{row.customerName,jdbcType=VARCHAR},
      </if>
      <if test="row.customerMobile != null">
        customer_mobile = #{row.customerMobile,jdbcType=VARCHAR},
      </if>
      <if test="row.orderStatus != null">
        order_status = #{row.orderStatus,jdbcType=TINYINT},
      </if>
      <if test="row.orderSettleStatus != null">
        order_settle_status = #{row.orderSettleStatus,jdbcType=TINYINT},
      </if>
      <if test="row.vehicleId != null">
        vehicle_id = #{row.vehicleId,jdbcType=BIGINT},
      </if>
      <if test="row.vehicleSerialNo != null">
        vehicle_serial_no = #{row.vehicleSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleLicense != null">
        vehicle_license = #{row.vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleVin != null">
        vehicle_vin = #{row.vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleBrandCode != null">
        vehicle_brand_code = #{row.vehicleBrandCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleBrandName != null">
        vehicle_brand_name = #{row.vehicleBrandName,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleModelCode != null">
        vehicle_model_code = #{row.vehicleModelCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleModelName != null">
        vehicle_model_name = #{row.vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleStructId != null">
        vehicle_struct_id = #{row.vehicleStructId,jdbcType=INTEGER},
      </if>
      <if test="row.vehicleStructCode != null">
        vehicle_struct_code = #{row.vehicleStructCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleStructName != null">
        vehicle_struct_name = #{row.vehicleStructName,jdbcType=VARCHAR},
      </if>
      <if test="row.vehiclePicUrl != null">
        vehicle_pic_url = #{row.vehiclePicUrl,jdbcType=VARCHAR},
      </if>
      <if test="row.orderUserMemo != null">
        order_user_memo = #{row.orderUserMemo,jdbcType=VARCHAR},
      </if>
      <if test="row.initialFenceId != null">
        initial_fence_id = #{row.initialFenceId,jdbcType=INTEGER},
      </if>
      <if test="row.initialLatitude != null">
        initial_latitude = #{row.initialLatitude,jdbcType=DECIMAL},
      </if>
      <if test="row.initialLongitude != null">
        initial_longitude = #{row.initialLongitude,jdbcType=DECIMAL},
      </if>
      <if test="row.initialLocation != null">
        initial_location = #{row.initialLocation,jdbcType=VARCHAR},
      </if>
      <if test="row.returnFenceId != null">
        return_fence_id = #{row.returnFenceId,jdbcType=INTEGER},
      </if>
      <if test="row.returnLatitude != null">
        return_latitude = #{row.returnLatitude,jdbcType=DECIMAL},
      </if>
      <if test="row.returnLongitude != null">
        return_longitude = #{row.returnLongitude,jdbcType=DECIMAL},
      </if>
      <if test="row.returnLocation != null">
        return_location = #{row.returnLocation,jdbcType=VARCHAR},
      </if>
      <if test="row.orderPaymentTime != null">
        order_payment_time = #{row.orderPaymentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.confirmTime != null">
        confirm_time = #{row.confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.expectedPickupTime != null">
        expected_pickup_time = #{row.expectedPickupTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.expectedReturnTime != null">
        expected_return_time = #{row.expectedReturnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.operationPickupTime != null">
        operation_pickup_time = #{row.operationPickupTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.actualPickupTime != null">
        actual_pickup_time = #{row.actualPickupTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.pickupLotExitTime != null">
        pickup_lot_exit_time = #{row.pickupLotExitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.operationReturnTime != null">
        operation_return_time = #{row.operationReturnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.actualReturnTime != null">
        actual_return_time = #{row.actualReturnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.returnLotEntryTime != null">
        return_lot_entry_time = #{row.returnLotEntryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.orderSettleTime != null">
        order_settle_time = #{row.orderSettleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.rentalHours != null">
        rental_hours = #{row.rentalHours,jdbcType=INTEGER},
      </if>
      <if test="row.orderCancellationTime != null">
        order_cancellation_time = #{row.orderCancellationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.appraiseSubmited != null">
        appraise_submited = #{row.appraiseSubmited,jdbcType=BIT},
      </if>
      <if test="row.appraiseSubmitTime != null">
        appraise_submit_time = #{row.appraiseSubmitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.returnFenceName != null">
        return_fence_name = #{row.returnFenceName,jdbcType=VARCHAR},
      </if>
      <if test="row.initialFenceName != null">
        initial_fence_name = #{row.initialFenceName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update gov_car_order
    set order_no = #{row.orderNo,jdbcType=VARCHAR},
      create_id = #{row.createId,jdbcType=INTEGER},
      create_name = #{row.createName,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_id = #{row.updateId,jdbcType=INTEGER},
      update_name = #{row.updateName,jdbcType=VARCHAR},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      order_device_source = #{row.orderDeviceSource,jdbcType=VARCHAR},
      company_code = #{row.companyCode,jdbcType=VARCHAR},
      company_name = #{row.companyName,jdbcType=VARCHAR},
      struct_id = #{row.structId,jdbcType=INTEGER},
      struct_code = #{row.structCode,jdbcType=VARCHAR},
      struct_name = #{row.structName,jdbcType=VARCHAR},
      customer_id = #{row.customerId,jdbcType=INTEGER},
      customer_name = #{row.customerName,jdbcType=VARCHAR},
      customer_mobile = #{row.customerMobile,jdbcType=VARCHAR},
      order_status = #{row.orderStatus,jdbcType=TINYINT},
      order_settle_status = #{row.orderSettleStatus,jdbcType=TINYINT},
      vehicle_id = #{row.vehicleId,jdbcType=BIGINT},
      vehicle_serial_no = #{row.vehicleSerialNo,jdbcType=VARCHAR},
      vehicle_license = #{row.vehicleLicense,jdbcType=VARCHAR},
      vehicle_vin = #{row.vehicleVin,jdbcType=VARCHAR},
      vehicle_brand_code = #{row.vehicleBrandCode,jdbcType=VARCHAR},
      vehicle_brand_name = #{row.vehicleBrandName,jdbcType=VARCHAR},
      vehicle_model_code = #{row.vehicleModelCode,jdbcType=VARCHAR},
      vehicle_model_name = #{row.vehicleModelName,jdbcType=VARCHAR},
      vehicle_struct_id = #{row.vehicleStructId,jdbcType=INTEGER},
      vehicle_struct_code = #{row.vehicleStructCode,jdbcType=VARCHAR},
      vehicle_struct_name = #{row.vehicleStructName,jdbcType=VARCHAR},
      vehicle_pic_url = #{row.vehiclePicUrl,jdbcType=VARCHAR},
      order_user_memo = #{row.orderUserMemo,jdbcType=VARCHAR},
      initial_fence_id = #{row.initialFenceId,jdbcType=INTEGER},
      initial_latitude = #{row.initialLatitude,jdbcType=DECIMAL},
      initial_longitude = #{row.initialLongitude,jdbcType=DECIMAL},
      initial_location = #{row.initialLocation,jdbcType=VARCHAR},
      return_fence_id = #{row.returnFenceId,jdbcType=INTEGER},
      return_latitude = #{row.returnLatitude,jdbcType=DECIMAL},
      return_longitude = #{row.returnLongitude,jdbcType=DECIMAL},
      return_location = #{row.returnLocation,jdbcType=VARCHAR},
      order_payment_time = #{row.orderPaymentTime,jdbcType=TIMESTAMP},
      confirm_time = #{row.confirmTime,jdbcType=TIMESTAMP},
      expected_pickup_time = #{row.expectedPickupTime,jdbcType=TIMESTAMP},
      expected_return_time = #{row.expectedReturnTime,jdbcType=TIMESTAMP},
      operation_pickup_time = #{row.operationPickupTime,jdbcType=TIMESTAMP},
      actual_pickup_time = #{row.actualPickupTime,jdbcType=TIMESTAMP},
      pickup_lot_exit_time = #{row.pickupLotExitTime,jdbcType=TIMESTAMP},
      operation_return_time = #{row.operationReturnTime,jdbcType=TIMESTAMP},
      actual_return_time = #{row.actualReturnTime,jdbcType=TIMESTAMP},
      return_lot_entry_time = #{row.returnLotEntryTime,jdbcType=TIMESTAMP},
      order_settle_time = #{row.orderSettleTime,jdbcType=TIMESTAMP},
      rental_hours = #{row.rentalHours,jdbcType=INTEGER},
      order_cancellation_time = #{row.orderCancellationTime,jdbcType=TIMESTAMP},
      appraise_submited = #{row.appraiseSubmited,jdbcType=BIT},
      appraise_submit_time = #{row.appraiseSubmitTime,jdbcType=TIMESTAMP},
      return_fence_name = #{row.returnFenceName,jdbcType=VARCHAR},
      initial_fence_name = #{row.initialFenceName,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.GovCarOrder">
    update gov_car_order
    <set>
      <if test="createId != null">
        create_id = #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        update_id = #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderDeviceSource != null">
        order_device_source = #{orderDeviceSource,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="structId != null">
        struct_id = #{structId,jdbcType=INTEGER},
      </if>
      <if test="structCode != null">
        struct_code = #{structCode,jdbcType=VARCHAR},
      </if>
      <if test="structName != null">
        struct_name = #{structName,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=INTEGER},
      </if>
      <if test="customerName != null">
        customer_name = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerMobile != null">
        customer_mobile = #{customerMobile,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=TINYINT},
      </if>
      <if test="orderSettleStatus != null">
        order_settle_status = #{orderSettleStatus,jdbcType=TINYINT},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleSerialNo != null">
        vehicle_serial_no = #{vehicleSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="vehicleLicense != null">
        vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="vehicleVin != null">
        vehicle_vin = #{vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBrandCode != null">
        vehicle_brand_code = #{vehicleBrandCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBrandName != null">
        vehicle_brand_name = #{vehicleBrandName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelCode != null">
        vehicle_model_code = #{vehicleModelCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelName != null">
        vehicle_model_name = #{vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleStructId != null">
        vehicle_struct_id = #{vehicleStructId,jdbcType=INTEGER},
      </if>
      <if test="vehicleStructCode != null">
        vehicle_struct_code = #{vehicleStructCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleStructName != null">
        vehicle_struct_name = #{vehicleStructName,jdbcType=VARCHAR},
      </if>
      <if test="vehiclePicUrl != null">
        vehicle_pic_url = #{vehiclePicUrl,jdbcType=VARCHAR},
      </if>
      <if test="orderUserMemo != null">
        order_user_memo = #{orderUserMemo,jdbcType=VARCHAR},
      </if>
      <if test="initialFenceId != null">
        initial_fence_id = #{initialFenceId,jdbcType=INTEGER},
      </if>
      <if test="initialLatitude != null">
        initial_latitude = #{initialLatitude,jdbcType=DECIMAL},
      </if>
      <if test="initialLongitude != null">
        initial_longitude = #{initialLongitude,jdbcType=DECIMAL},
      </if>
      <if test="initialLocation != null">
        initial_location = #{initialLocation,jdbcType=VARCHAR},
      </if>
      <if test="returnFenceId != null">
        return_fence_id = #{returnFenceId,jdbcType=INTEGER},
      </if>
      <if test="returnLatitude != null">
        return_latitude = #{returnLatitude,jdbcType=DECIMAL},
      </if>
      <if test="returnLongitude != null">
        return_longitude = #{returnLongitude,jdbcType=DECIMAL},
      </if>
      <if test="returnLocation != null">
        return_location = #{returnLocation,jdbcType=VARCHAR},
      </if>
      <if test="orderPaymentTime != null">
        order_payment_time = #{orderPaymentTime,jdbcType=TIMESTAMP},
      </if>
      <if test="confirmTime != null">
        confirm_time = #{confirmTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expectedPickupTime != null">
        expected_pickup_time = #{expectedPickupTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expectedReturnTime != null">
        expected_return_time = #{expectedReturnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operationPickupTime != null">
        operation_pickup_time = #{operationPickupTime,jdbcType=TIMESTAMP},
      </if>
      <if test="actualPickupTime != null">
        actual_pickup_time = #{actualPickupTime,jdbcType=TIMESTAMP},
      </if>
      <if test="pickupLotExitTime != null">
        pickup_lot_exit_time = #{pickupLotExitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operationReturnTime != null">
        operation_return_time = #{operationReturnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="actualReturnTime != null">
        actual_return_time = #{actualReturnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="returnLotEntryTime != null">
        return_lot_entry_time = #{returnLotEntryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderSettleTime != null">
        order_settle_time = #{orderSettleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="rentalHours != null">
        rental_hours = #{rentalHours,jdbcType=INTEGER},
      </if>
      <if test="orderCancellationTime != null">
        order_cancellation_time = #{orderCancellationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="appraiseSubmited != null">
        appraise_submited = #{appraiseSubmited,jdbcType=BIT},
      </if>
      <if test="appraiseSubmitTime != null">
        appraise_submit_time = #{appraiseSubmitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="returnFenceName != null">
        return_fence_name = #{returnFenceName,jdbcType=VARCHAR},
      </if>
      <if test="initialFenceName != null">
        initial_fence_name = #{initialFenceName,jdbcType=VARCHAR},
      </if>
    </set>
    where order_no = #{orderNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.GovCarOrder">
    update gov_car_order
    set create_id = #{createId,jdbcType=INTEGER},
      create_name = #{createName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_id = #{updateId,jdbcType=INTEGER},
      update_name = #{updateName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      order_device_source = #{orderDeviceSource,jdbcType=VARCHAR},
      company_code = #{companyCode,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      struct_id = #{structId,jdbcType=INTEGER},
      struct_code = #{structCode,jdbcType=VARCHAR},
      struct_name = #{structName,jdbcType=VARCHAR},
      customer_id = #{customerId,jdbcType=INTEGER},
      customer_name = #{customerName,jdbcType=VARCHAR},
      customer_mobile = #{customerMobile,jdbcType=VARCHAR},
      order_status = #{orderStatus,jdbcType=TINYINT},
      order_settle_status = #{orderSettleStatus,jdbcType=TINYINT},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      vehicle_serial_no = #{vehicleSerialNo,jdbcType=VARCHAR},
      vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      vehicle_vin = #{vehicleVin,jdbcType=VARCHAR},
      vehicle_brand_code = #{vehicleBrandCode,jdbcType=VARCHAR},
      vehicle_brand_name = #{vehicleBrandName,jdbcType=VARCHAR},
      vehicle_model_code = #{vehicleModelCode,jdbcType=VARCHAR},
      vehicle_model_name = #{vehicleModelName,jdbcType=VARCHAR},
      vehicle_struct_id = #{vehicleStructId,jdbcType=INTEGER},
      vehicle_struct_code = #{vehicleStructCode,jdbcType=VARCHAR},
      vehicle_struct_name = #{vehicleStructName,jdbcType=VARCHAR},
      vehicle_pic_url = #{vehiclePicUrl,jdbcType=VARCHAR},
      order_user_memo = #{orderUserMemo,jdbcType=VARCHAR},
      initial_fence_id = #{initialFenceId,jdbcType=INTEGER},
      initial_latitude = #{initialLatitude,jdbcType=DECIMAL},
      initial_longitude = #{initialLongitude,jdbcType=DECIMAL},
      initial_location = #{initialLocation,jdbcType=VARCHAR},
      return_fence_id = #{returnFenceId,jdbcType=INTEGER},
      return_latitude = #{returnLatitude,jdbcType=DECIMAL},
      return_longitude = #{returnLongitude,jdbcType=DECIMAL},
      return_location = #{returnLocation,jdbcType=VARCHAR},
      order_payment_time = #{orderPaymentTime,jdbcType=TIMESTAMP},
      confirm_time = #{confirmTime,jdbcType=TIMESTAMP},
      expected_pickup_time = #{expectedPickupTime,jdbcType=TIMESTAMP},
      expected_return_time = #{expectedReturnTime,jdbcType=TIMESTAMP},
      operation_pickup_time = #{operationPickupTime,jdbcType=TIMESTAMP},
      actual_pickup_time = #{actualPickupTime,jdbcType=TIMESTAMP},
      pickup_lot_exit_time = #{pickupLotExitTime,jdbcType=TIMESTAMP},
      operation_return_time = #{operationReturnTime,jdbcType=TIMESTAMP},
      actual_return_time = #{actualReturnTime,jdbcType=TIMESTAMP},
      return_lot_entry_time = #{returnLotEntryTime,jdbcType=TIMESTAMP},
      order_settle_time = #{orderSettleTime,jdbcType=TIMESTAMP},
      rental_hours = #{rentalHours,jdbcType=INTEGER},
      order_cancellation_time = #{orderCancellationTime,jdbcType=TIMESTAMP},
      appraise_submited = #{appraiseSubmited,jdbcType=BIT},
      appraise_submit_time = #{appraiseSubmitTime,jdbcType=TIMESTAMP},
      return_fence_name = #{returnFenceName,jdbcType=VARCHAR},
      initial_fence_name = #{initialFenceName,jdbcType=VARCHAR}
    where order_no = #{orderNo,jdbcType=VARCHAR}
  </update>
</mapper>