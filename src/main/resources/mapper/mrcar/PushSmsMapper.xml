<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.PushSmsMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.PushSms">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="push_type" jdbcType="TINYINT" property="pushType" />
    <result column="receive_mobile" jdbcType="VARCHAR" property="receiveMobile" />
    <result column="send_content" jdbcType="VARCHAR" property="sendContent" />
    <result column="send_time" jdbcType="TIMESTAMP" property="sendTime" />
    <result column="send_status" jdbcType="TINYINT" property="sendStatus" />
    <result column="result_content" jdbcType="VARCHAR" property="resultContent" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, push_type, receive_mobile, send_content, send_time, 
    send_status, result_content
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from push_sms
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from push_sms
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.PushSms">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into push_sms (create_time, update_time, push_type, 
      receive_mobile, send_content, send_time, 
      send_status, result_content)
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{pushType,jdbcType=TINYINT}, 
      #{receiveMobile,jdbcType=VARCHAR}, #{sendContent,jdbcType=VARCHAR}, #{sendTime,jdbcType=TIMESTAMP}, 
      #{sendStatus,jdbcType=TINYINT}, #{resultContent,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.PushSms">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into push_sms
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="pushType != null">
        push_type,
      </if>
      <if test="receiveMobile != null">
        receive_mobile,
      </if>
      <if test="sendContent != null">
        send_content,
      </if>
      <if test="sendTime != null">
        send_time,
      </if>
      <if test="sendStatus != null">
        send_status,
      </if>
      <if test="resultContent != null">
        result_content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="pushType != null">
        #{pushType,jdbcType=TINYINT},
      </if>
      <if test="receiveMobile != null">
        #{receiveMobile,jdbcType=VARCHAR},
      </if>
      <if test="sendContent != null">
        #{sendContent,jdbcType=VARCHAR},
      </if>
      <if test="sendTime != null">
        #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sendStatus != null">
        #{sendStatus,jdbcType=TINYINT},
      </if>
      <if test="resultContent != null">
        #{resultContent,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.PushSms">
    update push_sms
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="pushType != null">
        push_type = #{pushType,jdbcType=TINYINT},
      </if>
      <if test="receiveMobile != null">
        receive_mobile = #{receiveMobile,jdbcType=VARCHAR},
      </if>
      <if test="sendContent != null">
        send_content = #{sendContent,jdbcType=VARCHAR},
      </if>
      <if test="sendTime != null">
        send_time = #{sendTime,jdbcType=TIMESTAMP},
      </if>
      <if test="sendStatus != null">
        send_status = #{sendStatus,jdbcType=TINYINT},
      </if>
      <if test="resultContent != null">
        result_content = #{resultContent,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.PushSms">
    update push_sms
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      push_type = #{pushType,jdbcType=TINYINT},
      receive_mobile = #{receiveMobile,jdbcType=VARCHAR},
      send_content = #{sendContent,jdbcType=VARCHAR},
      send_time = #{sendTime,jdbcType=TIMESTAMP},
      send_status = #{sendStatus,jdbcType=TINYINT},
      result_content = #{resultContent,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>