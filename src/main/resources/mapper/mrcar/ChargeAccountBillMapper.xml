<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ChargeAccountBillMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.ChargeAccountBill">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_id" jdbcType="INTEGER" property="orderId" />
    <result column="order_code" jdbcType="VARCHAR" property="orderCode" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="bill_date" jdbcType="DATE" property="billDate" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="operator_total_amount" jdbcType="DECIMAL" property="operatorTotalAmount" />
    <result column="operator_service_amount" jdbcType="DECIMAL" property="operatorServiceAmount" />
    <result column="total_ele_amount" jdbcType="DECIMAL" property="totalEleAmount" />
    <result column="service_amount" jdbcType="DECIMAL" property="serviceAmount" />
    <result column="total_power" jdbcType="DECIMAL" property="totalPower" />
    <result column="pay_way" jdbcType="TINYINT" property="payWay" />
    <result column="create_way" jdbcType="TINYINT" property="createWay" />
    <result column="extend_amount_1" jdbcType="DECIMAL" property="extendAmount1" />
    <result column="extend_amount_2" jdbcType="DECIMAL" property="extendAmount2" />
    <result column="extend_text_1" jdbcType="VARCHAR" property="extendText1" />
    <result column="extend_text_2" jdbcType="VARCHAR" property="extendText2" />
    <result column="extend_int_1" jdbcType="INTEGER" property="extendInt1" />
    <result column="extend_int_2" jdbcType="INTEGER" property="extendInt2" />
    <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
    <result column="expense_status" jdbcType="INTEGER" property="expenseStatus" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_id, order_code, create_time, update_time, bill_date, total_amount, operator_total_amount, 
    operator_service_amount, total_ele_amount, service_amount, total_power, pay_way, 
    create_way, extend_amount_1, extend_amount_2, extend_text_1, extend_text_2, extend_int_1, 
    extend_int_2, is_delete, expense_status
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.ChargeAccountBillExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from charge_account_bill
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from charge_account_bill
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from charge_account_bill
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.order.entity.mrcar.ChargeAccountBillExample">
    delete from charge_account_bill
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.ChargeAccountBill">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into charge_account_bill (order_id, order_code, create_time, 
      update_time, bill_date, total_amount, 
      operator_total_amount, operator_service_amount, 
      total_ele_amount, service_amount, total_power, 
      pay_way, create_way, extend_amount_1, 
      extend_amount_2, extend_text_1, extend_text_2, 
      extend_int_1, extend_int_2, is_delete, 
      expense_status)
    values (#{orderId,jdbcType=INTEGER}, #{orderCode,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{billDate,jdbcType=DATE}, #{totalAmount,jdbcType=DECIMAL}, 
      #{operatorTotalAmount,jdbcType=DECIMAL}, #{operatorServiceAmount,jdbcType=DECIMAL}, 
      #{totalEleAmount,jdbcType=DECIMAL}, #{serviceAmount,jdbcType=DECIMAL}, #{totalPower,jdbcType=DECIMAL}, 
      #{payWay,jdbcType=TINYINT}, #{createWay,jdbcType=TINYINT}, #{extendAmount1,jdbcType=DECIMAL}, 
      #{extendAmount2,jdbcType=DECIMAL}, #{extendText1,jdbcType=VARCHAR}, #{extendText2,jdbcType=VARCHAR}, 
      #{extendInt1,jdbcType=INTEGER}, #{extendInt2,jdbcType=INTEGER}, #{isDelete,jdbcType=TINYINT}, 
      #{expenseStatus,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.ChargeAccountBill">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into charge_account_bill
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderCode != null">
        order_code,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="billDate != null">
        bill_date,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="operatorTotalAmount != null">
        operator_total_amount,
      </if>
      <if test="operatorServiceAmount != null">
        operator_service_amount,
      </if>
      <if test="totalEleAmount != null">
        total_ele_amount,
      </if>
      <if test="serviceAmount != null">
        service_amount,
      </if>
      <if test="totalPower != null">
        total_power,
      </if>
      <if test="payWay != null">
        pay_way,
      </if>
      <if test="createWay != null">
        create_way,
      </if>
      <if test="extendAmount1 != null">
        extend_amount_1,
      </if>
      <if test="extendAmount2 != null">
        extend_amount_2,
      </if>
      <if test="extendText1 != null">
        extend_text_1,
      </if>
      <if test="extendText2 != null">
        extend_text_2,
      </if>
      <if test="extendInt1 != null">
        extend_int_1,
      </if>
      <if test="extendInt2 != null">
        extend_int_2,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="expenseStatus != null">
        expense_status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderCode != null">
        #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="billDate != null">
        #{billDate,jdbcType=DATE},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="operatorTotalAmount != null">
        #{operatorTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="operatorServiceAmount != null">
        #{operatorServiceAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalEleAmount != null">
        #{totalEleAmount,jdbcType=DECIMAL},
      </if>
      <if test="serviceAmount != null">
        #{serviceAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalPower != null">
        #{totalPower,jdbcType=DECIMAL},
      </if>
      <if test="payWay != null">
        #{payWay,jdbcType=TINYINT},
      </if>
      <if test="createWay != null">
        #{createWay,jdbcType=TINYINT},
      </if>
      <if test="extendAmount1 != null">
        #{extendAmount1,jdbcType=DECIMAL},
      </if>
      <if test="extendAmount2 != null">
        #{extendAmount2,jdbcType=DECIMAL},
      </if>
      <if test="extendText1 != null">
        #{extendText1,jdbcType=VARCHAR},
      </if>
      <if test="extendText2 != null">
        #{extendText2,jdbcType=VARCHAR},
      </if>
      <if test="extendInt1 != null">
        #{extendInt1,jdbcType=INTEGER},
      </if>
      <if test="extendInt2 != null">
        #{extendInt2,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="expenseStatus != null">
        #{expenseStatus,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.ChargeAccountBillExample" resultType="java.lang.Long">
    select count(*) from charge_account_bill
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update charge_account_bill
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=INTEGER},
      </if>
      <if test="row.orderId != null">
        order_id = #{row.orderId,jdbcType=INTEGER},
      </if>
      <if test="row.orderCode != null">
        order_code = #{row.orderCode,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.billDate != null">
        bill_date = #{row.billDate,jdbcType=DATE},
      </if>
      <if test="row.totalAmount != null">
        total_amount = #{row.totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.operatorTotalAmount != null">
        operator_total_amount = #{row.operatorTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.operatorServiceAmount != null">
        operator_service_amount = #{row.operatorServiceAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.totalEleAmount != null">
        total_ele_amount = #{row.totalEleAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.serviceAmount != null">
        service_amount = #{row.serviceAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.totalPower != null">
        total_power = #{row.totalPower,jdbcType=DECIMAL},
      </if>
      <if test="row.payWay != null">
        pay_way = #{row.payWay,jdbcType=TINYINT},
      </if>
      <if test="row.createWay != null">
        create_way = #{row.createWay,jdbcType=TINYINT},
      </if>
      <if test="row.extendAmount1 != null">
        extend_amount_1 = #{row.extendAmount1,jdbcType=DECIMAL},
      </if>
      <if test="row.extendAmount2 != null">
        extend_amount_2 = #{row.extendAmount2,jdbcType=DECIMAL},
      </if>
      <if test="row.extendText1 != null">
        extend_text_1 = #{row.extendText1,jdbcType=VARCHAR},
      </if>
      <if test="row.extendText2 != null">
        extend_text_2 = #{row.extendText2,jdbcType=VARCHAR},
      </if>
      <if test="row.extendInt1 != null">
        extend_int_1 = #{row.extendInt1,jdbcType=INTEGER},
      </if>
      <if test="row.extendInt2 != null">
        extend_int_2 = #{row.extendInt2,jdbcType=INTEGER},
      </if>
      <if test="row.isDelete != null">
        is_delete = #{row.isDelete,jdbcType=TINYINT},
      </if>
      <if test="row.expenseStatus != null">
        expense_status = #{row.expenseStatus,jdbcType=INTEGER},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update charge_account_bill
    set id = #{row.id,jdbcType=INTEGER},
      order_id = #{row.orderId,jdbcType=INTEGER},
      order_code = #{row.orderCode,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      bill_date = #{row.billDate,jdbcType=DATE},
      total_amount = #{row.totalAmount,jdbcType=DECIMAL},
      operator_total_amount = #{row.operatorTotalAmount,jdbcType=DECIMAL},
      operator_service_amount = #{row.operatorServiceAmount,jdbcType=DECIMAL},
      total_ele_amount = #{row.totalEleAmount,jdbcType=DECIMAL},
      service_amount = #{row.serviceAmount,jdbcType=DECIMAL},
      total_power = #{row.totalPower,jdbcType=DECIMAL},
      pay_way = #{row.payWay,jdbcType=TINYINT},
      create_way = #{row.createWay,jdbcType=TINYINT},
      extend_amount_1 = #{row.extendAmount1,jdbcType=DECIMAL},
      extend_amount_2 = #{row.extendAmount2,jdbcType=DECIMAL},
      extend_text_1 = #{row.extendText1,jdbcType=VARCHAR},
      extend_text_2 = #{row.extendText2,jdbcType=VARCHAR},
      extend_int_1 = #{row.extendInt1,jdbcType=INTEGER},
      extend_int_2 = #{row.extendInt2,jdbcType=INTEGER},
      is_delete = #{row.isDelete,jdbcType=TINYINT},
      expense_status = #{row.expenseStatus,jdbcType=INTEGER}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.ChargeAccountBill">
    update charge_account_bill
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderCode != null">
        order_code = #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="billDate != null">
        bill_date = #{billDate,jdbcType=DATE},
      </if>
      <if test="totalAmount != null">
        total_amount = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="operatorTotalAmount != null">
        operator_total_amount = #{operatorTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="operatorServiceAmount != null">
        operator_service_amount = #{operatorServiceAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalEleAmount != null">
        total_ele_amount = #{totalEleAmount,jdbcType=DECIMAL},
      </if>
      <if test="serviceAmount != null">
        service_amount = #{serviceAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalPower != null">
        total_power = #{totalPower,jdbcType=DECIMAL},
      </if>
      <if test="payWay != null">
        pay_way = #{payWay,jdbcType=TINYINT},
      </if>
      <if test="createWay != null">
        create_way = #{createWay,jdbcType=TINYINT},
      </if>
      <if test="extendAmount1 != null">
        extend_amount_1 = #{extendAmount1,jdbcType=DECIMAL},
      </if>
      <if test="extendAmount2 != null">
        extend_amount_2 = #{extendAmount2,jdbcType=DECIMAL},
      </if>
      <if test="extendText1 != null">
        extend_text_1 = #{extendText1,jdbcType=VARCHAR},
      </if>
      <if test="extendText2 != null">
        extend_text_2 = #{extendText2,jdbcType=VARCHAR},
      </if>
      <if test="extendInt1 != null">
        extend_int_1 = #{extendInt1,jdbcType=INTEGER},
      </if>
      <if test="extendInt2 != null">
        extend_int_2 = #{extendInt2,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        is_delete = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="expenseStatus != null">
        expense_status = #{expenseStatus,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.ChargeAccountBill">
    update charge_account_bill
    set order_id = #{orderId,jdbcType=INTEGER},
      order_code = #{orderCode,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      bill_date = #{billDate,jdbcType=DATE},
      total_amount = #{totalAmount,jdbcType=DECIMAL},
      operator_total_amount = #{operatorTotalAmount,jdbcType=DECIMAL},
      operator_service_amount = #{operatorServiceAmount,jdbcType=DECIMAL},
      total_ele_amount = #{totalEleAmount,jdbcType=DECIMAL},
      service_amount = #{serviceAmount,jdbcType=DECIMAL},
      total_power = #{totalPower,jdbcType=DECIMAL},
      pay_way = #{payWay,jdbcType=TINYINT},
      create_way = #{createWay,jdbcType=TINYINT},
      extend_amount_1 = #{extendAmount1,jdbcType=DECIMAL},
      extend_amount_2 = #{extendAmount2,jdbcType=DECIMAL},
      extend_text_1 = #{extendText1,jdbcType=VARCHAR},
      extend_text_2 = #{extendText2,jdbcType=VARCHAR},
      extend_int_1 = #{extendInt1,jdbcType=INTEGER},
      extend_int_2 = #{extendInt2,jdbcType=INTEGER},
      is_delete = #{isDelete,jdbcType=TINYINT},
      expense_status = #{expenseStatus,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>