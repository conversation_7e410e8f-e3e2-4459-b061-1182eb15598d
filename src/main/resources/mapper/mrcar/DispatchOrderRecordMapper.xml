<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.DispatchOrderRecordMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.DispatchOrderRecord">
    <id column="dispatch_order_record_id" jdbcType="INTEGER" property="dispatchOrderRecordId" />
    <result column="create_id" jdbcType="INTEGER" property="createId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="del_tag" jdbcType="TINYINT" property="delTag" />
    <result column="demand_order_num" jdbcType="VARCHAR" property="demandOrderNum" />
    <result column="customer_order_num" jdbcType="VARCHAR" property="customerOrderNum" />
    <result column="dispatch_order_time" jdbcType="TIMESTAMP" property="dispatchOrderTime" />
    <result column="take_order_type" jdbcType="TINYINT" property="takeOrderType" />
    <result column="take_order_company_name" jdbcType="VARCHAR" property="takeOrderCompanyName" />
    <result column="take_order_company_id" jdbcType="VARCHAR" property="takeOrderCompanyId" />
    <result column="dispatch_order_user_id" jdbcType="INTEGER" property="dispatchOrderUserId" />
    <result column="dispatch_type" jdbcType="TINYINT" property="dispatchType" />
    <result column="return_reason" jdbcType="VARCHAR" property="returnReason" />
  </resultMap>
  <sql id="Base_Column_List">
    dispatch_order_record_id, create_id, create_name, create_time, del_tag, demand_order_num, 
    customer_order_num, dispatch_order_time, take_order_type, take_order_company_name, 
    take_order_company_id, dispatch_order_user_id, dispatch_type, return_reason
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from so_dispatch_order_record
    where dispatch_order_record_id = #{dispatchOrderRecordId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from so_dispatch_order_record
    where dispatch_order_record_id = #{dispatchOrderRecordId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.DispatchOrderRecord">
    insert into so_dispatch_order_record (dispatch_order_record_id, create_id, 
      create_name, create_time, del_tag, 
      demand_order_num, customer_order_num, dispatch_order_time, 
      take_order_type, take_order_company_name, take_order_company_id, 
      dispatch_order_user_id, dispatch_type, return_reason
      )
    values (#{dispatchOrderRecordId,jdbcType=INTEGER}, #{createId,jdbcType=INTEGER}, 
      #{createName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{delTag,jdbcType=TINYINT}, 
      #{demandOrderNum,jdbcType=VARCHAR}, #{customerOrderNum,jdbcType=VARCHAR}, #{dispatchOrderTime,jdbcType=TIMESTAMP}, 
      #{takeOrderType,jdbcType=TINYINT}, #{takeOrderCompanyName,jdbcType=VARCHAR}, #{takeOrderCompanyId,jdbcType=VARCHAR}, 
      #{dispatchOrderUserId,jdbcType=INTEGER}, #{dispatchType,jdbcType=TINYINT}, #{returnReason,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.DispatchOrderRecord">
    insert into so_dispatch_order_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="dispatchOrderRecordId != null">
        dispatch_order_record_id,
      </if>
      <if test="createId != null">
        create_id,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="delTag != null">
        del_tag,
      </if>
      <if test="demandOrderNum != null">
        demand_order_num,
      </if>
      <if test="customerOrderNum != null">
        customer_order_num,
      </if>
      <if test="dispatchOrderTime != null">
        dispatch_order_time,
      </if>
      <if test="takeOrderType != null">
        take_order_type,
      </if>
      <if test="takeOrderCompanyName != null">
        take_order_company_name,
      </if>
      <if test="takeOrderCompanyId != null">
        take_order_company_id,
      </if>
      <if test="dispatchOrderUserId != null">
        dispatch_order_user_id,
      </if>
      <if test="dispatchType != null">
        dispatch_type,
      </if>
      <if test="returnReason != null">
        return_reason,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="dispatchOrderRecordId != null">
        #{dispatchOrderRecordId,jdbcType=INTEGER},
      </if>
      <if test="createId != null">
        #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delTag != null">
        #{delTag,jdbcType=TINYINT},
      </if>
      <if test="demandOrderNum != null">
        #{demandOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="customerOrderNum != null">
        #{customerOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="dispatchOrderTime != null">
        #{dispatchOrderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="takeOrderType != null">
        #{takeOrderType,jdbcType=TINYINT},
      </if>
      <if test="takeOrderCompanyName != null">
        #{takeOrderCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="takeOrderCompanyId != null">
        #{takeOrderCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="dispatchOrderUserId != null">
        #{dispatchOrderUserId,jdbcType=INTEGER},
      </if>
      <if test="dispatchType != null">
        #{dispatchType,jdbcType=TINYINT},
      </if>
      <if test="returnReason != null">
        #{returnReason,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.DispatchOrderRecord">
    update so_dispatch_order_record
    <set>
      <if test="createId != null">
        create_id = #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="delTag != null">
        del_tag = #{delTag,jdbcType=TINYINT},
      </if>
      <if test="demandOrderNum != null">
        demand_order_num = #{demandOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="customerOrderNum != null">
        customer_order_num = #{customerOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="dispatchOrderTime != null">
        dispatch_order_time = #{dispatchOrderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="takeOrderType != null">
        take_order_type = #{takeOrderType,jdbcType=TINYINT},
      </if>
      <if test="takeOrderCompanyName != null">
        take_order_company_name = #{takeOrderCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="takeOrderCompanyId != null">
        take_order_company_id = #{takeOrderCompanyId,jdbcType=VARCHAR},
      </if>
      <if test="dispatchOrderUserId != null">
        dispatch_order_user_id = #{dispatchOrderUserId,jdbcType=INTEGER},
      </if>
      <if test="dispatchType != null">
        dispatch_type = #{dispatchType,jdbcType=TINYINT},
      </if>
      <if test="returnReason != null">
        return_reason = #{returnReason,jdbcType=VARCHAR},
      </if>
    </set>
    where dispatch_order_record_id = #{dispatchOrderRecordId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.DispatchOrderRecord">
    update so_dispatch_order_record
    set create_id = #{createId,jdbcType=INTEGER},
      create_name = #{createName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      del_tag = #{delTag,jdbcType=TINYINT},
      demand_order_num = #{demandOrderNum,jdbcType=VARCHAR},
      customer_order_num = #{customerOrderNum,jdbcType=VARCHAR},
      dispatch_order_time = #{dispatchOrderTime,jdbcType=TIMESTAMP},
      take_order_type = #{takeOrderType,jdbcType=TINYINT},
      take_order_company_name = #{takeOrderCompanyName,jdbcType=VARCHAR},
      take_order_company_id = #{takeOrderCompanyId,jdbcType=VARCHAR},
      dispatch_order_user_id = #{dispatchOrderUserId,jdbcType=INTEGER},
      dispatch_type = #{dispatchType,jdbcType=TINYINT},
      return_reason = #{returnReason,jdbcType=VARCHAR}
    where dispatch_order_record_id = #{dispatchOrderRecordId,jdbcType=INTEGER}
  </update>
</mapper>