<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.CoAccountBillRecordMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.CoAccountBillRecord">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="struct_code" jdbcType="VARCHAR" property="structCode" />
    <result column="struct_name" jdbcType="VARCHAR" property="structName" />
    <result column="config_date_num" jdbcType="INTEGER" property="configDateNum" />
    <result column="bill_date" jdbcType="DATE" property="billDate" />
    <result column="bill_start_date" jdbcType="DATE" property="billStartDate" />
    <result column="bill_end_date" jdbcType="DATE" property="billEndDate" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, company_id, company_code, company_name, struct_code, struct_name, config_date_num, 
    bill_date, bill_start_date, bill_end_date, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.CoAccountBillRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from co_account_bill_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from co_account_bill_record
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from co_account_bill_record
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.CoAccountBillRecord">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into co_account_bill_record (company_id, company_code, company_name, 
      struct_code, struct_name, config_date_num, 
      bill_date, bill_start_date, bill_end_date, 
      create_time, update_time)
    values (#{companyId,jdbcType=INTEGER}, #{companyCode,jdbcType=VARCHAR}, #{companyName,jdbcType=VARCHAR}, 
      #{structCode,jdbcType=VARCHAR}, #{structName,jdbcType=VARCHAR}, #{configDateNum,jdbcType=INTEGER}, 
      #{billDate,jdbcType=DATE}, #{billStartDate,jdbcType=DATE}, #{billEndDate,jdbcType=DATE}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.CoAccountBillRecord">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into co_account_bill_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="structCode != null">
        struct_code,
      </if>
      <if test="structName != null">
        struct_name,
      </if>
      <if test="configDateNum != null">
        config_date_num,
      </if>
      <if test="billDate != null">
        bill_date,
      </if>
      <if test="billStartDate != null">
        bill_start_date,
      </if>
      <if test="billEndDate != null">
        bill_end_date,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="structCode != null">
        #{structCode,jdbcType=VARCHAR},
      </if>
      <if test="structName != null">
        #{structName,jdbcType=VARCHAR},
      </if>
      <if test="configDateNum != null">
        #{configDateNum,jdbcType=INTEGER},
      </if>
      <if test="billDate != null">
        #{billDate,jdbcType=DATE},
      </if>
      <if test="billStartDate != null">
        #{billStartDate,jdbcType=DATE},
      </if>
      <if test="billEndDate != null">
        #{billEndDate,jdbcType=DATE},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.CoAccountBillRecordExample" resultType="java.lang.Long">
    select count(*) from co_account_bill_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.CoAccountBillRecord">
    update co_account_bill_record
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="structCode != null">
        struct_code = #{structCode,jdbcType=VARCHAR},
      </if>
      <if test="structName != null">
        struct_name = #{structName,jdbcType=VARCHAR},
      </if>
      <if test="configDateNum != null">
        config_date_num = #{configDateNum,jdbcType=INTEGER},
      </if>
      <if test="billDate != null">
        bill_date = #{billDate,jdbcType=DATE},
      </if>
      <if test="billStartDate != null">
        bill_start_date = #{billStartDate,jdbcType=DATE},
      </if>
      <if test="billEndDate != null">
        bill_end_date = #{billEndDate,jdbcType=DATE},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.CoAccountBillRecord">
    update co_account_bill_record
    set company_id = #{companyId,jdbcType=INTEGER},
      company_code = #{companyCode,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      struct_code = #{structCode,jdbcType=VARCHAR},
      struct_name = #{structName,jdbcType=VARCHAR},
      config_date_num = #{configDateNum,jdbcType=INTEGER},
      bill_date = #{billDate,jdbcType=DATE},
      bill_start_date = #{billStartDate,jdbcType=DATE},
      bill_end_date = #{billEndDate,jdbcType=DATE},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>