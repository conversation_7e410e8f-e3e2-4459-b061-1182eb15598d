<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.TemporalSharedVehicleRecordMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.TemporalSharedVehicleRecord">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="warn_sn" jdbcType="VARCHAR" property="warnSn" />
    <result column="warn_id" jdbcType="BIGINT" property="warnId" />
    <result column="record_status" jdbcType="TINYINT" property="recordStatus" />
    <result column="warn_start_time" jdbcType="TIMESTAMP" property="warnStartTime" />
    <result column="warn_end_time" jdbcType="TIMESTAMP" property="warnEndTime" />
    <result column="warn_start_latitude" jdbcType="DECIMAL" property="warnStartLatitude" />
    <result column="warn_start_longitude" jdbcType="DECIMAL" property="warnStartLongitude" />
    <result column="warn_end_latitude" jdbcType="DECIMAL" property="warnEndLatitude" />
    <result column="warn_end_longitude" jdbcType="DECIMAL" property="warnEndLongitude" />
    <result column="duration" jdbcType="INTEGER" property="duration" />
    <result column="duration_hour" jdbcType="DECIMAL" property="durationHour" />
    <result column="trip_mileage" jdbcType="DECIMAL" property="tripMileage" />
    <result column="order_associate_status" jdbcType="TINYINT" property="orderAssociateStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user_id" jdbcType="INTEGER" property="updateUserId" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_remark" jdbcType="VARCHAR" property="updateRemark" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="vehicle_license" jdbcType="VARCHAR" property="vehicleLicense" />
    <result column="vehicle_vin" jdbcType="VARCHAR" property="vehicleVin" />
    <result column="vehicle_brand_id" jdbcType="INTEGER" property="vehicleBrandId" />
    <result column="vehicle_brand_code" jdbcType="VARCHAR" property="vehicleBrandCode" />
    <result column="vehicle_brand" jdbcType="VARCHAR" property="vehicleBrand" />
    <result column="vehicle_model_id" jdbcType="INTEGER" property="vehicleModelId" />
    <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode" />
    <result column="vehicle_model" jdbcType="VARCHAR" property="vehicleModel" />
    <result column="vehicle_type" jdbcType="TINYINT" property="vehicleType" />
    <result column="vehicle_belong_city_code" jdbcType="VARCHAR" property="vehicleBelongCityCode" />
    <result column="vehicle_belong_city_name" jdbcType="VARCHAR" property="vehicleBelongCityName" />
    <result column="vehicle_company_id" jdbcType="INTEGER" property="vehicleCompanyId" />
    <result column="vehicle_company_name" jdbcType="VARCHAR" property="vehicleCompanyName" />
    <result column="vehicle_struct_id" jdbcType="INTEGER" property="vehicleStructId" />
    <result column="vehicle_struct_name" jdbcType="VARCHAR" property="vehicleStructName" />
    <result column="vehicle_self_owned" jdbcType="TINYINT" property="vehicleSelfOwned" />
    <result column="vehicle_working_status" jdbcType="TINYINT" property="vehicleWorkingStatus" />
    <result column="vehicle_own_struct_id" jdbcType="INTEGER" property="vehicleOwnStructId" />
    <result column="vehicle_own_struct_name" jdbcType="VARCHAR" property="vehicleOwnStructName" />
    <result column="vehicle_belong_struct_name" jdbcType="VARCHAR" property="vehicleBelongStructName" />
    <result column="vehicle_belong_struct_code" jdbcType="VARCHAR" property="vehicleBelongStructCode" />
    <result column="vehicle_belong_buss_code" jdbcType="VARCHAR" property="vehicleBelongBussCode" />
    <result column="vehicle_belong_buss_name" jdbcType="VARCHAR" property="vehicleBelongBussName" />
    <result column="vehicle_operate_struct_name" jdbcType="VARCHAR" property="vehicleOperateStructName" />
    <result column="vehicle_operate_struct_code" jdbcType="VARCHAR" property="vehicleOperateStructCode" />
    <result column="vehicle_operate_buss_name" jdbcType="VARCHAR" property="vehicleOperateBussName" />
    <result column="vehicle_operate_buss_code" jdbcType="VARCHAR" property="vehicleOperateBussCode" />
    <result column="vehicle_creator_id" jdbcType="INTEGER" property="vehicleCreatorId" />
    <result column="vehicle_creator_name" jdbcType="VARCHAR" property="vehicleCreatorName" />
    <result column="vehicle_asset_city_code" jdbcType="VARCHAR" property="vehicleAssetCityCode" />
    <result column="vehicle_asset_city_name" jdbcType="VARCHAR" property="vehicleAssetCityName" />
    <result column="vehicle_sim_no" jdbcType="VARCHAR" property="vehicleSimNo" />
    <result column="vehicle_device_no" jdbcType="VARCHAR" property="vehicleDeviceNo" />
    <result column="vehicle_device_type" jdbcType="TINYINT" property="vehicleDeviceType" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="order_apply_no" jdbcType="VARCHAR" property="orderApplyNo" />
    <result column="order_company_id" jdbcType="INTEGER" property="orderCompanyId" />
    <result column="order_struct_id" jdbcType="INTEGER" property="orderStructId" />
    <result column="order_struct_name" jdbcType="VARCHAR" property="orderStructName" />
    <result column="order_type" jdbcType="TINYINT" property="orderType" />
    <result column="order_customer_id" jdbcType="INTEGER" property="orderCustomerId" />
    <result column="order_customer_name" jdbcType="VARCHAR" property="orderCustomerName" />
    <result column="order_customer_mobile" jdbcType="VARCHAR" property="orderCustomerMobile" />
    <result column="fence_id" jdbcType="INTEGER" property="fenceId" />
    <result column="fence_name" jdbcType="VARCHAR" property="fenceName" />
    <result column="fence_city_code" jdbcType="INTEGER" property="fenceCityCode" />
    <result column="fence_city_name" jdbcType="VARCHAR" property="fenceCityName" />
    <result column="fence_warn_type" jdbcType="TINYINT" property="fenceWarnType" />
    <result column="fence_address" jdbcType="VARCHAR" property="fenceAddress" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.izu.order.entity.mrcar.TemporalSharedVehicleRecord">
    <result column="order_associate_remark" jdbcType="LONGVARCHAR" property="orderAssociateRemark" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, warn_sn, warn_id, record_status, warn_start_time, warn_end_time, warn_start_latitude, 
    warn_start_longitude, warn_end_latitude, warn_end_longitude, duration, duration_hour, 
    trip_mileage, order_associate_status, create_time, update_user_id, update_user_name, 
    update_time, update_remark, vehicle_id, vehicle_license, vehicle_vin, vehicle_brand_id, 
    vehicle_brand_code, vehicle_brand, vehicle_model_id, vehicle_model_code, vehicle_model, 
    vehicle_type, vehicle_belong_city_code, vehicle_belong_city_name, vehicle_company_id, 
    vehicle_company_name, vehicle_struct_id, vehicle_struct_name, vehicle_self_owned, 
    vehicle_working_status, vehicle_own_struct_id, vehicle_own_struct_name, vehicle_belong_struct_name, 
    vehicle_belong_struct_code, vehicle_belong_buss_code, vehicle_belong_buss_name, vehicle_operate_struct_name, 
    vehicle_operate_struct_code, vehicle_operate_buss_name, vehicle_operate_buss_code, 
    vehicle_creator_id, vehicle_creator_name, vehicle_asset_city_code, vehicle_asset_city_name, 
    vehicle_sim_no, vehicle_device_no, vehicle_device_type, order_no, order_apply_no, 
    order_company_id, order_struct_id, order_struct_name, order_type, order_customer_id, 
    order_customer_name, order_customer_mobile, fence_id, fence_name, fence_city_code, 
    fence_city_name, fence_warn_type, fence_address
  </sql>
  <sql id="Blob_Column_List">
    order_associate_remark
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.izu.order.entity.mrcar.TemporalSharedVehicleRecordExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from t_temporal_shared_vehicle_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.TemporalSharedVehicleRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_temporal_shared_vehicle_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from t_temporal_shared_vehicle_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_temporal_shared_vehicle_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.order.entity.mrcar.TemporalSharedVehicleRecordExample">
    delete from t_temporal_shared_vehicle_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.TemporalSharedVehicleRecord">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into t_temporal_shared_vehicle_record (warn_sn, warn_id, record_status, 
      warn_start_time, warn_end_time, warn_start_latitude, 
      warn_start_longitude, warn_end_latitude, warn_end_longitude, 
      duration, duration_hour, trip_mileage, 
      order_associate_status, create_time, update_user_id, 
      update_user_name, update_time, update_remark, 
      vehicle_id, vehicle_license, vehicle_vin, 
      vehicle_brand_id, vehicle_brand_code, vehicle_brand, 
      vehicle_model_id, vehicle_model_code, vehicle_model, 
      vehicle_type, vehicle_belong_city_code, vehicle_belong_city_name, 
      vehicle_company_id, vehicle_company_name, vehicle_struct_id, 
      vehicle_struct_name, vehicle_self_owned, vehicle_working_status, 
      vehicle_own_struct_id, vehicle_own_struct_name, 
      vehicle_belong_struct_name, vehicle_belong_struct_code, 
      vehicle_belong_buss_code, vehicle_belong_buss_name, 
      vehicle_operate_struct_name, vehicle_operate_struct_code, 
      vehicle_operate_buss_name, vehicle_operate_buss_code, 
      vehicle_creator_id, vehicle_creator_name, vehicle_asset_city_code, 
      vehicle_asset_city_name, vehicle_sim_no, vehicle_device_no, 
      vehicle_device_type, order_no, order_apply_no, 
      order_company_id, order_struct_id, order_struct_name, 
      order_type, order_customer_id, order_customer_name, 
      order_customer_mobile, fence_id, fence_name, 
      fence_city_code, fence_city_name, fence_warn_type, 
      fence_address, order_associate_remark)
    values (#{warnSn,jdbcType=VARCHAR}, #{warnId,jdbcType=BIGINT}, #{recordStatus,jdbcType=TINYINT}, 
      #{warnStartTime,jdbcType=TIMESTAMP}, #{warnEndTime,jdbcType=TIMESTAMP}, #{warnStartLatitude,jdbcType=DECIMAL}, 
      #{warnStartLongitude,jdbcType=DECIMAL}, #{warnEndLatitude,jdbcType=DECIMAL}, #{warnEndLongitude,jdbcType=DECIMAL}, 
      #{duration,jdbcType=INTEGER}, #{durationHour,jdbcType=DECIMAL}, #{tripMileage,jdbcType=DECIMAL}, 
      #{orderAssociateStatus,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateUserId,jdbcType=INTEGER}, 
      #{updateUserName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updateRemark,jdbcType=VARCHAR}, 
      #{vehicleId,jdbcType=BIGINT}, #{vehicleLicense,jdbcType=VARCHAR}, #{vehicleVin,jdbcType=VARCHAR}, 
      #{vehicleBrandId,jdbcType=INTEGER}, #{vehicleBrandCode,jdbcType=VARCHAR}, #{vehicleBrand,jdbcType=VARCHAR}, 
      #{vehicleModelId,jdbcType=INTEGER}, #{vehicleModelCode,jdbcType=VARCHAR}, #{vehicleModel,jdbcType=VARCHAR}, 
      #{vehicleType,jdbcType=TINYINT}, #{vehicleBelongCityCode,jdbcType=VARCHAR}, #{vehicleBelongCityName,jdbcType=VARCHAR}, 
      #{vehicleCompanyId,jdbcType=INTEGER}, #{vehicleCompanyName,jdbcType=VARCHAR}, #{vehicleStructId,jdbcType=INTEGER}, 
      #{vehicleStructName,jdbcType=VARCHAR}, #{vehicleSelfOwned,jdbcType=TINYINT}, #{vehicleWorkingStatus,jdbcType=TINYINT}, 
      #{vehicleOwnStructId,jdbcType=INTEGER}, #{vehicleOwnStructName,jdbcType=VARCHAR}, 
      #{vehicleBelongStructName,jdbcType=VARCHAR}, #{vehicleBelongStructCode,jdbcType=VARCHAR}, 
      #{vehicleBelongBussCode,jdbcType=VARCHAR}, #{vehicleBelongBussName,jdbcType=VARCHAR}, 
      #{vehicleOperateStructName,jdbcType=VARCHAR}, #{vehicleOperateStructCode,jdbcType=VARCHAR}, 
      #{vehicleOperateBussName,jdbcType=VARCHAR}, #{vehicleOperateBussCode,jdbcType=VARCHAR}, 
      #{vehicleCreatorId,jdbcType=INTEGER}, #{vehicleCreatorName,jdbcType=VARCHAR}, #{vehicleAssetCityCode,jdbcType=VARCHAR}, 
      #{vehicleAssetCityName,jdbcType=VARCHAR}, #{vehicleSimNo,jdbcType=VARCHAR}, #{vehicleDeviceNo,jdbcType=VARCHAR}, 
      #{vehicleDeviceType,jdbcType=TINYINT}, #{orderNo,jdbcType=VARCHAR}, #{orderApplyNo,jdbcType=VARCHAR}, 
      #{orderCompanyId,jdbcType=INTEGER}, #{orderStructId,jdbcType=INTEGER}, #{orderStructName,jdbcType=VARCHAR}, 
      #{orderType,jdbcType=TINYINT}, #{orderCustomerId,jdbcType=INTEGER}, #{orderCustomerName,jdbcType=VARCHAR}, 
      #{orderCustomerMobile,jdbcType=VARCHAR}, #{fenceId,jdbcType=INTEGER}, #{fenceName,jdbcType=VARCHAR}, 
      #{fenceCityCode,jdbcType=INTEGER}, #{fenceCityName,jdbcType=VARCHAR}, #{fenceWarnType,jdbcType=TINYINT}, 
      #{fenceAddress,jdbcType=VARCHAR}, #{orderAssociateRemark,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.TemporalSharedVehicleRecord">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into t_temporal_shared_vehicle_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="warnSn != null">
        warn_sn,
      </if>
      <if test="warnId != null">
        warn_id,
      </if>
      <if test="recordStatus != null">
        record_status,
      </if>
      <if test="warnStartTime != null">
        warn_start_time,
      </if>
      <if test="warnEndTime != null">
        warn_end_time,
      </if>
      <if test="warnStartLatitude != null">
        warn_start_latitude,
      </if>
      <if test="warnStartLongitude != null">
        warn_start_longitude,
      </if>
      <if test="warnEndLatitude != null">
        warn_end_latitude,
      </if>
      <if test="warnEndLongitude != null">
        warn_end_longitude,
      </if>
      <if test="duration != null">
        duration,
      </if>
      <if test="durationHour != null">
        duration_hour,
      </if>
      <if test="tripMileage != null">
        trip_mileage,
      </if>
      <if test="orderAssociateStatus != null">
        order_associate_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateUserId != null">
        update_user_id,
      </if>
      <if test="updateUserName != null">
        update_user_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateRemark != null">
        update_remark,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="vehicleLicense != null">
        vehicle_license,
      </if>
      <if test="vehicleVin != null">
        vehicle_vin,
      </if>
      <if test="vehicleBrandId != null">
        vehicle_brand_id,
      </if>
      <if test="vehicleBrandCode != null">
        vehicle_brand_code,
      </if>
      <if test="vehicleBrand != null">
        vehicle_brand,
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id,
      </if>
      <if test="vehicleModelCode != null">
        vehicle_model_code,
      </if>
      <if test="vehicleModel != null">
        vehicle_model,
      </if>
      <if test="vehicleType != null">
        vehicle_type,
      </if>
      <if test="vehicleBelongCityCode != null">
        vehicle_belong_city_code,
      </if>
      <if test="vehicleBelongCityName != null">
        vehicle_belong_city_name,
      </if>
      <if test="vehicleCompanyId != null">
        vehicle_company_id,
      </if>
      <if test="vehicleCompanyName != null">
        vehicle_company_name,
      </if>
      <if test="vehicleStructId != null">
        vehicle_struct_id,
      </if>
      <if test="vehicleStructName != null">
        vehicle_struct_name,
      </if>
      <if test="vehicleSelfOwned != null">
        vehicle_self_owned,
      </if>
      <if test="vehicleWorkingStatus != null">
        vehicle_working_status,
      </if>
      <if test="vehicleOwnStructId != null">
        vehicle_own_struct_id,
      </if>
      <if test="vehicleOwnStructName != null">
        vehicle_own_struct_name,
      </if>
      <if test="vehicleBelongStructName != null">
        vehicle_belong_struct_name,
      </if>
      <if test="vehicleBelongStructCode != null">
        vehicle_belong_struct_code,
      </if>
      <if test="vehicleBelongBussCode != null">
        vehicle_belong_buss_code,
      </if>
      <if test="vehicleBelongBussName != null">
        vehicle_belong_buss_name,
      </if>
      <if test="vehicleOperateStructName != null">
        vehicle_operate_struct_name,
      </if>
      <if test="vehicleOperateStructCode != null">
        vehicle_operate_struct_code,
      </if>
      <if test="vehicleOperateBussName != null">
        vehicle_operate_buss_name,
      </if>
      <if test="vehicleOperateBussCode != null">
        vehicle_operate_buss_code,
      </if>
      <if test="vehicleCreatorId != null">
        vehicle_creator_id,
      </if>
      <if test="vehicleCreatorName != null">
        vehicle_creator_name,
      </if>
      <if test="vehicleAssetCityCode != null">
        vehicle_asset_city_code,
      </if>
      <if test="vehicleAssetCityName != null">
        vehicle_asset_city_name,
      </if>
      <if test="vehicleSimNo != null">
        vehicle_sim_no,
      </if>
      <if test="vehicleDeviceNo != null">
        vehicle_device_no,
      </if>
      <if test="vehicleDeviceType != null">
        vehicle_device_type,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="orderApplyNo != null">
        order_apply_no,
      </if>
      <if test="orderCompanyId != null">
        order_company_id,
      </if>
      <if test="orderStructId != null">
        order_struct_id,
      </if>
      <if test="orderStructName != null">
        order_struct_name,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="orderCustomerId != null">
        order_customer_id,
      </if>
      <if test="orderCustomerName != null">
        order_customer_name,
      </if>
      <if test="orderCustomerMobile != null">
        order_customer_mobile,
      </if>
      <if test="fenceId != null">
        fence_id,
      </if>
      <if test="fenceName != null">
        fence_name,
      </if>
      <if test="fenceCityCode != null">
        fence_city_code,
      </if>
      <if test="fenceCityName != null">
        fence_city_name,
      </if>
      <if test="fenceWarnType != null">
        fence_warn_type,
      </if>
      <if test="fenceAddress != null">
        fence_address,
      </if>
      <if test="orderAssociateRemark != null">
        order_associate_remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="warnSn != null">
        #{warnSn,jdbcType=VARCHAR},
      </if>
      <if test="warnId != null">
        #{warnId,jdbcType=BIGINT},
      </if>
      <if test="recordStatus != null">
        #{recordStatus,jdbcType=TINYINT},
      </if>
      <if test="warnStartTime != null">
        #{warnStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="warnEndTime != null">
        #{warnEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="warnStartLatitude != null">
        #{warnStartLatitude,jdbcType=DECIMAL},
      </if>
      <if test="warnStartLongitude != null">
        #{warnStartLongitude,jdbcType=DECIMAL},
      </if>
      <if test="warnEndLatitude != null">
        #{warnEndLatitude,jdbcType=DECIMAL},
      </if>
      <if test="warnEndLongitude != null">
        #{warnEndLongitude,jdbcType=DECIMAL},
      </if>
      <if test="duration != null">
        #{duration,jdbcType=INTEGER},
      </if>
      <if test="durationHour != null">
        #{durationHour,jdbcType=DECIMAL},
      </if>
      <if test="tripMileage != null">
        #{tripMileage,jdbcType=DECIMAL},
      </if>
      <if test="orderAssociateStatus != null">
        #{orderAssociateStatus,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null">
        #{updateUserId,jdbcType=INTEGER},
      </if>
      <if test="updateUserName != null">
        #{updateUserName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleLicense != null">
        #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="vehicleVin != null">
        #{vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBrandId != null">
        #{vehicleBrandId,jdbcType=INTEGER},
      </if>
      <if test="vehicleBrandCode != null">
        #{vehicleBrandCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBrand != null">
        #{vehicleBrand,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelId != null">
        #{vehicleModelId,jdbcType=INTEGER},
      </if>
      <if test="vehicleModelCode != null">
        #{vehicleModelCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModel != null">
        #{vehicleModel,jdbcType=VARCHAR},
      </if>
      <if test="vehicleType != null">
        #{vehicleType,jdbcType=TINYINT},
      </if>
      <if test="vehicleBelongCityCode != null">
        #{vehicleBelongCityCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBelongCityName != null">
        #{vehicleBelongCityName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleCompanyId != null">
        #{vehicleCompanyId,jdbcType=INTEGER},
      </if>
      <if test="vehicleCompanyName != null">
        #{vehicleCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleStructId != null">
        #{vehicleStructId,jdbcType=INTEGER},
      </if>
      <if test="vehicleStructName != null">
        #{vehicleStructName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleSelfOwned != null">
        #{vehicleSelfOwned,jdbcType=TINYINT},
      </if>
      <if test="vehicleWorkingStatus != null">
        #{vehicleWorkingStatus,jdbcType=TINYINT},
      </if>
      <if test="vehicleOwnStructId != null">
        #{vehicleOwnStructId,jdbcType=INTEGER},
      </if>
      <if test="vehicleOwnStructName != null">
        #{vehicleOwnStructName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBelongStructName != null">
        #{vehicleBelongStructName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBelongStructCode != null">
        #{vehicleBelongStructCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBelongBussCode != null">
        #{vehicleBelongBussCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBelongBussName != null">
        #{vehicleBelongBussName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleOperateStructName != null">
        #{vehicleOperateStructName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleOperateStructCode != null">
        #{vehicleOperateStructCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleOperateBussName != null">
        #{vehicleOperateBussName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleOperateBussCode != null">
        #{vehicleOperateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleCreatorId != null">
        #{vehicleCreatorId,jdbcType=INTEGER},
      </if>
      <if test="vehicleCreatorName != null">
        #{vehicleCreatorName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleAssetCityCode != null">
        #{vehicleAssetCityCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleAssetCityName != null">
        #{vehicleAssetCityName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleSimNo != null">
        #{vehicleSimNo,jdbcType=VARCHAR},
      </if>
      <if test="vehicleDeviceNo != null">
        #{vehicleDeviceNo,jdbcType=VARCHAR},
      </if>
      <if test="vehicleDeviceType != null">
        #{vehicleDeviceType,jdbcType=TINYINT},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderApplyNo != null">
        #{orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="orderCompanyId != null">
        #{orderCompanyId,jdbcType=INTEGER},
      </if>
      <if test="orderStructId != null">
        #{orderStructId,jdbcType=INTEGER},
      </if>
      <if test="orderStructName != null">
        #{orderStructName,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=TINYINT},
      </if>
      <if test="orderCustomerId != null">
        #{orderCustomerId,jdbcType=INTEGER},
      </if>
      <if test="orderCustomerName != null">
        #{orderCustomerName,jdbcType=VARCHAR},
      </if>
      <if test="orderCustomerMobile != null">
        #{orderCustomerMobile,jdbcType=VARCHAR},
      </if>
      <if test="fenceId != null">
        #{fenceId,jdbcType=INTEGER},
      </if>
      <if test="fenceName != null">
        #{fenceName,jdbcType=VARCHAR},
      </if>
      <if test="fenceCityCode != null">
        #{fenceCityCode,jdbcType=INTEGER},
      </if>
      <if test="fenceCityName != null">
        #{fenceCityName,jdbcType=VARCHAR},
      </if>
      <if test="fenceWarnType != null">
        #{fenceWarnType,jdbcType=TINYINT},
      </if>
      <if test="fenceAddress != null">
        #{fenceAddress,jdbcType=VARCHAR},
      </if>
      <if test="orderAssociateRemark != null">
        #{orderAssociateRemark,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.TemporalSharedVehicleRecordExample" resultType="java.lang.Long">
    select count(*) from t_temporal_shared_vehicle_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update t_temporal_shared_vehicle_record
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.warnSn != null">
        warn_sn = #{row.warnSn,jdbcType=VARCHAR},
      </if>
      <if test="row.warnId != null">
        warn_id = #{row.warnId,jdbcType=BIGINT},
      </if>
      <if test="row.recordStatus != null">
        record_status = #{row.recordStatus,jdbcType=TINYINT},
      </if>
      <if test="row.warnStartTime != null">
        warn_start_time = #{row.warnStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.warnEndTime != null">
        warn_end_time = #{row.warnEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.warnStartLatitude != null">
        warn_start_latitude = #{row.warnStartLatitude,jdbcType=DECIMAL},
      </if>
      <if test="row.warnStartLongitude != null">
        warn_start_longitude = #{row.warnStartLongitude,jdbcType=DECIMAL},
      </if>
      <if test="row.warnEndLatitude != null">
        warn_end_latitude = #{row.warnEndLatitude,jdbcType=DECIMAL},
      </if>
      <if test="row.warnEndLongitude != null">
        warn_end_longitude = #{row.warnEndLongitude,jdbcType=DECIMAL},
      </if>
      <if test="row.duration != null">
        duration = #{row.duration,jdbcType=INTEGER},
      </if>
      <if test="row.durationHour != null">
        duration_hour = #{row.durationHour,jdbcType=DECIMAL},
      </if>
      <if test="row.tripMileage != null">
        trip_mileage = #{row.tripMileage,jdbcType=DECIMAL},
      </if>
      <if test="row.orderAssociateStatus != null">
        order_associate_status = #{row.orderAssociateStatus,jdbcType=TINYINT},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateUserId != null">
        update_user_id = #{row.updateUserId,jdbcType=INTEGER},
      </if>
      <if test="row.updateUserName != null">
        update_user_name = #{row.updateUserName,jdbcType=VARCHAR},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateRemark != null">
        update_remark = #{row.updateRemark,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleId != null">
        vehicle_id = #{row.vehicleId,jdbcType=BIGINT},
      </if>
      <if test="row.vehicleLicense != null">
        vehicle_license = #{row.vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleVin != null">
        vehicle_vin = #{row.vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleBrandId != null">
        vehicle_brand_id = #{row.vehicleBrandId,jdbcType=INTEGER},
      </if>
      <if test="row.vehicleBrandCode != null">
        vehicle_brand_code = #{row.vehicleBrandCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleBrand != null">
        vehicle_brand = #{row.vehicleBrand,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleModelId != null">
        vehicle_model_id = #{row.vehicleModelId,jdbcType=INTEGER},
      </if>
      <if test="row.vehicleModelCode != null">
        vehicle_model_code = #{row.vehicleModelCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleModel != null">
        vehicle_model = #{row.vehicleModel,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleType != null">
        vehicle_type = #{row.vehicleType,jdbcType=TINYINT},
      </if>
      <if test="row.vehicleBelongCityCode != null">
        vehicle_belong_city_code = #{row.vehicleBelongCityCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleBelongCityName != null">
        vehicle_belong_city_name = #{row.vehicleBelongCityName,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleCompanyId != null">
        vehicle_company_id = #{row.vehicleCompanyId,jdbcType=INTEGER},
      </if>
      <if test="row.vehicleCompanyName != null">
        vehicle_company_name = #{row.vehicleCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleStructId != null">
        vehicle_struct_id = #{row.vehicleStructId,jdbcType=INTEGER},
      </if>
      <if test="row.vehicleStructName != null">
        vehicle_struct_name = #{row.vehicleStructName,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleSelfOwned != null">
        vehicle_self_owned = #{row.vehicleSelfOwned,jdbcType=TINYINT},
      </if>
      <if test="row.vehicleWorkingStatus != null">
        vehicle_working_status = #{row.vehicleWorkingStatus,jdbcType=TINYINT},
      </if>
      <if test="row.vehicleOwnStructId != null">
        vehicle_own_struct_id = #{row.vehicleOwnStructId,jdbcType=INTEGER},
      </if>
      <if test="row.vehicleOwnStructName != null">
        vehicle_own_struct_name = #{row.vehicleOwnStructName,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleBelongStructName != null">
        vehicle_belong_struct_name = #{row.vehicleBelongStructName,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleBelongStructCode != null">
        vehicle_belong_struct_code = #{row.vehicleBelongStructCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleBelongBussCode != null">
        vehicle_belong_buss_code = #{row.vehicleBelongBussCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleBelongBussName != null">
        vehicle_belong_buss_name = #{row.vehicleBelongBussName,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleOperateStructName != null">
        vehicle_operate_struct_name = #{row.vehicleOperateStructName,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleOperateStructCode != null">
        vehicle_operate_struct_code = #{row.vehicleOperateStructCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleOperateBussName != null">
        vehicle_operate_buss_name = #{row.vehicleOperateBussName,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleOperateBussCode != null">
        vehicle_operate_buss_code = #{row.vehicleOperateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleCreatorId != null">
        vehicle_creator_id = #{row.vehicleCreatorId,jdbcType=INTEGER},
      </if>
      <if test="row.vehicleCreatorName != null">
        vehicle_creator_name = #{row.vehicleCreatorName,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleAssetCityCode != null">
        vehicle_asset_city_code = #{row.vehicleAssetCityCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleAssetCityName != null">
        vehicle_asset_city_name = #{row.vehicleAssetCityName,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleSimNo != null">
        vehicle_sim_no = #{row.vehicleSimNo,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleDeviceNo != null">
        vehicle_device_no = #{row.vehicleDeviceNo,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleDeviceType != null">
        vehicle_device_type = #{row.vehicleDeviceType,jdbcType=TINYINT},
      </if>
      <if test="row.orderNo != null">
        order_no = #{row.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="row.orderApplyNo != null">
        order_apply_no = #{row.orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="row.orderCompanyId != null">
        order_company_id = #{row.orderCompanyId,jdbcType=INTEGER},
      </if>
      <if test="row.orderStructId != null">
        order_struct_id = #{row.orderStructId,jdbcType=INTEGER},
      </if>
      <if test="row.orderStructName != null">
        order_struct_name = #{row.orderStructName,jdbcType=VARCHAR},
      </if>
      <if test="row.orderType != null">
        order_type = #{row.orderType,jdbcType=TINYINT},
      </if>
      <if test="row.orderCustomerId != null">
        order_customer_id = #{row.orderCustomerId,jdbcType=INTEGER},
      </if>
      <if test="row.orderCustomerName != null">
        order_customer_name = #{row.orderCustomerName,jdbcType=VARCHAR},
      </if>
      <if test="row.orderCustomerMobile != null">
        order_customer_mobile = #{row.orderCustomerMobile,jdbcType=VARCHAR},
      </if>
      <if test="row.fenceId != null">
        fence_id = #{row.fenceId,jdbcType=INTEGER},
      </if>
      <if test="row.fenceName != null">
        fence_name = #{row.fenceName,jdbcType=VARCHAR},
      </if>
      <if test="row.fenceCityCode != null">
        fence_city_code = #{row.fenceCityCode,jdbcType=INTEGER},
      </if>
      <if test="row.fenceCityName != null">
        fence_city_name = #{row.fenceCityName,jdbcType=VARCHAR},
      </if>
      <if test="row.fenceWarnType != null">
        fence_warn_type = #{row.fenceWarnType,jdbcType=TINYINT},
      </if>
      <if test="row.fenceAddress != null">
        fence_address = #{row.fenceAddress,jdbcType=VARCHAR},
      </if>
      <if test="row.orderAssociateRemark != null">
        order_associate_remark = #{row.orderAssociateRemark,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update t_temporal_shared_vehicle_record
    set id = #{row.id,jdbcType=BIGINT},
      warn_sn = #{row.warnSn,jdbcType=VARCHAR},
      warn_id = #{row.warnId,jdbcType=BIGINT},
      record_status = #{row.recordStatus,jdbcType=TINYINT},
      warn_start_time = #{row.warnStartTime,jdbcType=TIMESTAMP},
      warn_end_time = #{row.warnEndTime,jdbcType=TIMESTAMP},
      warn_start_latitude = #{row.warnStartLatitude,jdbcType=DECIMAL},
      warn_start_longitude = #{row.warnStartLongitude,jdbcType=DECIMAL},
      warn_end_latitude = #{row.warnEndLatitude,jdbcType=DECIMAL},
      warn_end_longitude = #{row.warnEndLongitude,jdbcType=DECIMAL},
      duration = #{row.duration,jdbcType=INTEGER},
      duration_hour = #{row.durationHour,jdbcType=DECIMAL},
      trip_mileage = #{row.tripMileage,jdbcType=DECIMAL},
      order_associate_status = #{row.orderAssociateStatus,jdbcType=TINYINT},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_user_id = #{row.updateUserId,jdbcType=INTEGER},
      update_user_name = #{row.updateUserName,jdbcType=VARCHAR},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      update_remark = #{row.updateRemark,jdbcType=VARCHAR},
      vehicle_id = #{row.vehicleId,jdbcType=BIGINT},
      vehicle_license = #{row.vehicleLicense,jdbcType=VARCHAR},
      vehicle_vin = #{row.vehicleVin,jdbcType=VARCHAR},
      vehicle_brand_id = #{row.vehicleBrandId,jdbcType=INTEGER},
      vehicle_brand_code = #{row.vehicleBrandCode,jdbcType=VARCHAR},
      vehicle_brand = #{row.vehicleBrand,jdbcType=VARCHAR},
      vehicle_model_id = #{row.vehicleModelId,jdbcType=INTEGER},
      vehicle_model_code = #{row.vehicleModelCode,jdbcType=VARCHAR},
      vehicle_model = #{row.vehicleModel,jdbcType=VARCHAR},
      vehicle_type = #{row.vehicleType,jdbcType=TINYINT},
      vehicle_belong_city_code = #{row.vehicleBelongCityCode,jdbcType=VARCHAR},
      vehicle_belong_city_name = #{row.vehicleBelongCityName,jdbcType=VARCHAR},
      vehicle_company_id = #{row.vehicleCompanyId,jdbcType=INTEGER},
      vehicle_company_name = #{row.vehicleCompanyName,jdbcType=VARCHAR},
      vehicle_struct_id = #{row.vehicleStructId,jdbcType=INTEGER},
      vehicle_struct_name = #{row.vehicleStructName,jdbcType=VARCHAR},
      vehicle_self_owned = #{row.vehicleSelfOwned,jdbcType=TINYINT},
      vehicle_working_status = #{row.vehicleWorkingStatus,jdbcType=TINYINT},
      vehicle_own_struct_id = #{row.vehicleOwnStructId,jdbcType=INTEGER},
      vehicle_own_struct_name = #{row.vehicleOwnStructName,jdbcType=VARCHAR},
      vehicle_belong_struct_name = #{row.vehicleBelongStructName,jdbcType=VARCHAR},
      vehicle_belong_struct_code = #{row.vehicleBelongStructCode,jdbcType=VARCHAR},
      vehicle_belong_buss_code = #{row.vehicleBelongBussCode,jdbcType=VARCHAR},
      vehicle_belong_buss_name = #{row.vehicleBelongBussName,jdbcType=VARCHAR},
      vehicle_operate_struct_name = #{row.vehicleOperateStructName,jdbcType=VARCHAR},
      vehicle_operate_struct_code = #{row.vehicleOperateStructCode,jdbcType=VARCHAR},
      vehicle_operate_buss_name = #{row.vehicleOperateBussName,jdbcType=VARCHAR},
      vehicle_operate_buss_code = #{row.vehicleOperateBussCode,jdbcType=VARCHAR},
      vehicle_creator_id = #{row.vehicleCreatorId,jdbcType=INTEGER},
      vehicle_creator_name = #{row.vehicleCreatorName,jdbcType=VARCHAR},
      vehicle_asset_city_code = #{row.vehicleAssetCityCode,jdbcType=VARCHAR},
      vehicle_asset_city_name = #{row.vehicleAssetCityName,jdbcType=VARCHAR},
      vehicle_sim_no = #{row.vehicleSimNo,jdbcType=VARCHAR},
      vehicle_device_no = #{row.vehicleDeviceNo,jdbcType=VARCHAR},
      vehicle_device_type = #{row.vehicleDeviceType,jdbcType=TINYINT},
      order_no = #{row.orderNo,jdbcType=VARCHAR},
      order_apply_no = #{row.orderApplyNo,jdbcType=VARCHAR},
      order_company_id = #{row.orderCompanyId,jdbcType=INTEGER},
      order_struct_id = #{row.orderStructId,jdbcType=INTEGER},
      order_struct_name = #{row.orderStructName,jdbcType=VARCHAR},
      order_type = #{row.orderType,jdbcType=TINYINT},
      order_customer_id = #{row.orderCustomerId,jdbcType=INTEGER},
      order_customer_name = #{row.orderCustomerName,jdbcType=VARCHAR},
      order_customer_mobile = #{row.orderCustomerMobile,jdbcType=VARCHAR},
      fence_id = #{row.fenceId,jdbcType=INTEGER},
      fence_name = #{row.fenceName,jdbcType=VARCHAR},
      fence_city_code = #{row.fenceCityCode,jdbcType=INTEGER},
      fence_city_name = #{row.fenceCityName,jdbcType=VARCHAR},
      fence_warn_type = #{row.fenceWarnType,jdbcType=TINYINT},
      fence_address = #{row.fenceAddress,jdbcType=VARCHAR},
      order_associate_remark = #{row.orderAssociateRemark,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update t_temporal_shared_vehicle_record
    set id = #{row.id,jdbcType=BIGINT},
      warn_sn = #{row.warnSn,jdbcType=VARCHAR},
      warn_id = #{row.warnId,jdbcType=BIGINT},
      record_status = #{row.recordStatus,jdbcType=TINYINT},
      warn_start_time = #{row.warnStartTime,jdbcType=TIMESTAMP},
      warn_end_time = #{row.warnEndTime,jdbcType=TIMESTAMP},
      warn_start_latitude = #{row.warnStartLatitude,jdbcType=DECIMAL},
      warn_start_longitude = #{row.warnStartLongitude,jdbcType=DECIMAL},
      warn_end_latitude = #{row.warnEndLatitude,jdbcType=DECIMAL},
      warn_end_longitude = #{row.warnEndLongitude,jdbcType=DECIMAL},
      duration = #{row.duration,jdbcType=INTEGER},
      duration_hour = #{row.durationHour,jdbcType=DECIMAL},
      trip_mileage = #{row.tripMileage,jdbcType=DECIMAL},
      order_associate_status = #{row.orderAssociateStatus,jdbcType=TINYINT},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_user_id = #{row.updateUserId,jdbcType=INTEGER},
      update_user_name = #{row.updateUserName,jdbcType=VARCHAR},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      update_remark = #{row.updateRemark,jdbcType=VARCHAR},
      vehicle_id = #{row.vehicleId,jdbcType=BIGINT},
      vehicle_license = #{row.vehicleLicense,jdbcType=VARCHAR},
      vehicle_vin = #{row.vehicleVin,jdbcType=VARCHAR},
      vehicle_brand_id = #{row.vehicleBrandId,jdbcType=INTEGER},
      vehicle_brand_code = #{row.vehicleBrandCode,jdbcType=VARCHAR},
      vehicle_brand = #{row.vehicleBrand,jdbcType=VARCHAR},
      vehicle_model_id = #{row.vehicleModelId,jdbcType=INTEGER},
      vehicle_model_code = #{row.vehicleModelCode,jdbcType=VARCHAR},
      vehicle_model = #{row.vehicleModel,jdbcType=VARCHAR},
      vehicle_type = #{row.vehicleType,jdbcType=TINYINT},
      vehicle_belong_city_code = #{row.vehicleBelongCityCode,jdbcType=VARCHAR},
      vehicle_belong_city_name = #{row.vehicleBelongCityName,jdbcType=VARCHAR},
      vehicle_company_id = #{row.vehicleCompanyId,jdbcType=INTEGER},
      vehicle_company_name = #{row.vehicleCompanyName,jdbcType=VARCHAR},
      vehicle_struct_id = #{row.vehicleStructId,jdbcType=INTEGER},
      vehicle_struct_name = #{row.vehicleStructName,jdbcType=VARCHAR},
      vehicle_self_owned = #{row.vehicleSelfOwned,jdbcType=TINYINT},
      vehicle_working_status = #{row.vehicleWorkingStatus,jdbcType=TINYINT},
      vehicle_own_struct_id = #{row.vehicleOwnStructId,jdbcType=INTEGER},
      vehicle_own_struct_name = #{row.vehicleOwnStructName,jdbcType=VARCHAR},
      vehicle_belong_struct_name = #{row.vehicleBelongStructName,jdbcType=VARCHAR},
      vehicle_belong_struct_code = #{row.vehicleBelongStructCode,jdbcType=VARCHAR},
      vehicle_belong_buss_code = #{row.vehicleBelongBussCode,jdbcType=VARCHAR},
      vehicle_belong_buss_name = #{row.vehicleBelongBussName,jdbcType=VARCHAR},
      vehicle_operate_struct_name = #{row.vehicleOperateStructName,jdbcType=VARCHAR},
      vehicle_operate_struct_code = #{row.vehicleOperateStructCode,jdbcType=VARCHAR},
      vehicle_operate_buss_name = #{row.vehicleOperateBussName,jdbcType=VARCHAR},
      vehicle_operate_buss_code = #{row.vehicleOperateBussCode,jdbcType=VARCHAR},
      vehicle_creator_id = #{row.vehicleCreatorId,jdbcType=INTEGER},
      vehicle_creator_name = #{row.vehicleCreatorName,jdbcType=VARCHAR},
      vehicle_asset_city_code = #{row.vehicleAssetCityCode,jdbcType=VARCHAR},
      vehicle_asset_city_name = #{row.vehicleAssetCityName,jdbcType=VARCHAR},
      vehicle_sim_no = #{row.vehicleSimNo,jdbcType=VARCHAR},
      vehicle_device_no = #{row.vehicleDeviceNo,jdbcType=VARCHAR},
      vehicle_device_type = #{row.vehicleDeviceType,jdbcType=TINYINT},
      order_no = #{row.orderNo,jdbcType=VARCHAR},
      order_apply_no = #{row.orderApplyNo,jdbcType=VARCHAR},
      order_company_id = #{row.orderCompanyId,jdbcType=INTEGER},
      order_struct_id = #{row.orderStructId,jdbcType=INTEGER},
      order_struct_name = #{row.orderStructName,jdbcType=VARCHAR},
      order_type = #{row.orderType,jdbcType=TINYINT},
      order_customer_id = #{row.orderCustomerId,jdbcType=INTEGER},
      order_customer_name = #{row.orderCustomerName,jdbcType=VARCHAR},
      order_customer_mobile = #{row.orderCustomerMobile,jdbcType=VARCHAR},
      fence_id = #{row.fenceId,jdbcType=INTEGER},
      fence_name = #{row.fenceName,jdbcType=VARCHAR},
      fence_city_code = #{row.fenceCityCode,jdbcType=INTEGER},
      fence_city_name = #{row.fenceCityName,jdbcType=VARCHAR},
      fence_warn_type = #{row.fenceWarnType,jdbcType=TINYINT},
      fence_address = #{row.fenceAddress,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.TemporalSharedVehicleRecord">
    update t_temporal_shared_vehicle_record
    <set>
      <if test="warnSn != null">
        warn_sn = #{warnSn,jdbcType=VARCHAR},
      </if>
      <if test="warnId != null">
        warn_id = #{warnId,jdbcType=BIGINT},
      </if>
      <if test="recordStatus != null">
        record_status = #{recordStatus,jdbcType=TINYINT},
      </if>
      <if test="warnStartTime != null">
        warn_start_time = #{warnStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="warnEndTime != null">
        warn_end_time = #{warnEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="warnStartLatitude != null">
        warn_start_latitude = #{warnStartLatitude,jdbcType=DECIMAL},
      </if>
      <if test="warnStartLongitude != null">
        warn_start_longitude = #{warnStartLongitude,jdbcType=DECIMAL},
      </if>
      <if test="warnEndLatitude != null">
        warn_end_latitude = #{warnEndLatitude,jdbcType=DECIMAL},
      </if>
      <if test="warnEndLongitude != null">
        warn_end_longitude = #{warnEndLongitude,jdbcType=DECIMAL},
      </if>
      <if test="duration != null">
        duration = #{duration,jdbcType=INTEGER},
      </if>
      <if test="durationHour != null">
        duration_hour = #{durationHour,jdbcType=DECIMAL},
      </if>
      <if test="tripMileage != null">
        trip_mileage = #{tripMileage,jdbcType=DECIMAL},
      </if>
      <if test="orderAssociateStatus != null">
        order_associate_status = #{orderAssociateStatus,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateUserId != null">
        update_user_id = #{updateUserId,jdbcType=INTEGER},
      </if>
      <if test="updateUserName != null">
        update_user_name = #{updateUserName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateRemark != null">
        update_remark = #{updateRemark,jdbcType=VARCHAR},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleLicense != null">
        vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="vehicleVin != null">
        vehicle_vin = #{vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBrandId != null">
        vehicle_brand_id = #{vehicleBrandId,jdbcType=INTEGER},
      </if>
      <if test="vehicleBrandCode != null">
        vehicle_brand_code = #{vehicleBrandCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBrand != null">
        vehicle_brand = #{vehicleBrand,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id = #{vehicleModelId,jdbcType=INTEGER},
      </if>
      <if test="vehicleModelCode != null">
        vehicle_model_code = #{vehicleModelCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModel != null">
        vehicle_model = #{vehicleModel,jdbcType=VARCHAR},
      </if>
      <if test="vehicleType != null">
        vehicle_type = #{vehicleType,jdbcType=TINYINT},
      </if>
      <if test="vehicleBelongCityCode != null">
        vehicle_belong_city_code = #{vehicleBelongCityCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBelongCityName != null">
        vehicle_belong_city_name = #{vehicleBelongCityName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleCompanyId != null">
        vehicle_company_id = #{vehicleCompanyId,jdbcType=INTEGER},
      </if>
      <if test="vehicleCompanyName != null">
        vehicle_company_name = #{vehicleCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleStructId != null">
        vehicle_struct_id = #{vehicleStructId,jdbcType=INTEGER},
      </if>
      <if test="vehicleStructName != null">
        vehicle_struct_name = #{vehicleStructName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleSelfOwned != null">
        vehicle_self_owned = #{vehicleSelfOwned,jdbcType=TINYINT},
      </if>
      <if test="vehicleWorkingStatus != null">
        vehicle_working_status = #{vehicleWorkingStatus,jdbcType=TINYINT},
      </if>
      <if test="vehicleOwnStructId != null">
        vehicle_own_struct_id = #{vehicleOwnStructId,jdbcType=INTEGER},
      </if>
      <if test="vehicleOwnStructName != null">
        vehicle_own_struct_name = #{vehicleOwnStructName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBelongStructName != null">
        vehicle_belong_struct_name = #{vehicleBelongStructName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBelongStructCode != null">
        vehicle_belong_struct_code = #{vehicleBelongStructCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBelongBussCode != null">
        vehicle_belong_buss_code = #{vehicleBelongBussCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBelongBussName != null">
        vehicle_belong_buss_name = #{vehicleBelongBussName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleOperateStructName != null">
        vehicle_operate_struct_name = #{vehicleOperateStructName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleOperateStructCode != null">
        vehicle_operate_struct_code = #{vehicleOperateStructCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleOperateBussName != null">
        vehicle_operate_buss_name = #{vehicleOperateBussName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleOperateBussCode != null">
        vehicle_operate_buss_code = #{vehicleOperateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleCreatorId != null">
        vehicle_creator_id = #{vehicleCreatorId,jdbcType=INTEGER},
      </if>
      <if test="vehicleCreatorName != null">
        vehicle_creator_name = #{vehicleCreatorName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleAssetCityCode != null">
        vehicle_asset_city_code = #{vehicleAssetCityCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleAssetCityName != null">
        vehicle_asset_city_name = #{vehicleAssetCityName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleSimNo != null">
        vehicle_sim_no = #{vehicleSimNo,jdbcType=VARCHAR},
      </if>
      <if test="vehicleDeviceNo != null">
        vehicle_device_no = #{vehicleDeviceNo,jdbcType=VARCHAR},
      </if>
      <if test="vehicleDeviceType != null">
        vehicle_device_type = #{vehicleDeviceType,jdbcType=TINYINT},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderApplyNo != null">
        order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="orderCompanyId != null">
        order_company_id = #{orderCompanyId,jdbcType=INTEGER},
      </if>
      <if test="orderStructId != null">
        order_struct_id = #{orderStructId,jdbcType=INTEGER},
      </if>
      <if test="orderStructName != null">
        order_struct_name = #{orderStructName,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=TINYINT},
      </if>
      <if test="orderCustomerId != null">
        order_customer_id = #{orderCustomerId,jdbcType=INTEGER},
      </if>
      <if test="orderCustomerName != null">
        order_customer_name = #{orderCustomerName,jdbcType=VARCHAR},
      </if>
      <if test="orderCustomerMobile != null">
        order_customer_mobile = #{orderCustomerMobile,jdbcType=VARCHAR},
      </if>
      <if test="fenceId != null">
        fence_id = #{fenceId,jdbcType=INTEGER},
      </if>
      <if test="fenceName != null">
        fence_name = #{fenceName,jdbcType=VARCHAR},
      </if>
      <if test="fenceCityCode != null">
        fence_city_code = #{fenceCityCode,jdbcType=INTEGER},
      </if>
      <if test="fenceCityName != null">
        fence_city_name = #{fenceCityName,jdbcType=VARCHAR},
      </if>
      <if test="fenceWarnType != null">
        fence_warn_type = #{fenceWarnType,jdbcType=TINYINT},
      </if>
      <if test="fenceAddress != null">
        fence_address = #{fenceAddress,jdbcType=VARCHAR},
      </if>
      <if test="orderAssociateRemark != null">
        order_associate_remark = #{orderAssociateRemark,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.izu.order.entity.mrcar.TemporalSharedVehicleRecord">
    update t_temporal_shared_vehicle_record
    set warn_sn = #{warnSn,jdbcType=VARCHAR},
      warn_id = #{warnId,jdbcType=BIGINT},
      record_status = #{recordStatus,jdbcType=TINYINT},
      warn_start_time = #{warnStartTime,jdbcType=TIMESTAMP},
      warn_end_time = #{warnEndTime,jdbcType=TIMESTAMP},
      warn_start_latitude = #{warnStartLatitude,jdbcType=DECIMAL},
      warn_start_longitude = #{warnStartLongitude,jdbcType=DECIMAL},
      warn_end_latitude = #{warnEndLatitude,jdbcType=DECIMAL},
      warn_end_longitude = #{warnEndLongitude,jdbcType=DECIMAL},
      duration = #{duration,jdbcType=INTEGER},
      duration_hour = #{durationHour,jdbcType=DECIMAL},
      trip_mileage = #{tripMileage,jdbcType=DECIMAL},
      order_associate_status = #{orderAssociateStatus,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user_id = #{updateUserId,jdbcType=INTEGER},
      update_user_name = #{updateUserName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_remark = #{updateRemark,jdbcType=VARCHAR},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      vehicle_vin = #{vehicleVin,jdbcType=VARCHAR},
      vehicle_brand_id = #{vehicleBrandId,jdbcType=INTEGER},
      vehicle_brand_code = #{vehicleBrandCode,jdbcType=VARCHAR},
      vehicle_brand = #{vehicleBrand,jdbcType=VARCHAR},
      vehicle_model_id = #{vehicleModelId,jdbcType=INTEGER},
      vehicle_model_code = #{vehicleModelCode,jdbcType=VARCHAR},
      vehicle_model = #{vehicleModel,jdbcType=VARCHAR},
      vehicle_type = #{vehicleType,jdbcType=TINYINT},
      vehicle_belong_city_code = #{vehicleBelongCityCode,jdbcType=VARCHAR},
      vehicle_belong_city_name = #{vehicleBelongCityName,jdbcType=VARCHAR},
      vehicle_company_id = #{vehicleCompanyId,jdbcType=INTEGER},
      vehicle_company_name = #{vehicleCompanyName,jdbcType=VARCHAR},
      vehicle_struct_id = #{vehicleStructId,jdbcType=INTEGER},
      vehicle_struct_name = #{vehicleStructName,jdbcType=VARCHAR},
      vehicle_self_owned = #{vehicleSelfOwned,jdbcType=TINYINT},
      vehicle_working_status = #{vehicleWorkingStatus,jdbcType=TINYINT},
      vehicle_own_struct_id = #{vehicleOwnStructId,jdbcType=INTEGER},
      vehicle_own_struct_name = #{vehicleOwnStructName,jdbcType=VARCHAR},
      vehicle_belong_struct_name = #{vehicleBelongStructName,jdbcType=VARCHAR},
      vehicle_belong_struct_code = #{vehicleBelongStructCode,jdbcType=VARCHAR},
      vehicle_belong_buss_code = #{vehicleBelongBussCode,jdbcType=VARCHAR},
      vehicle_belong_buss_name = #{vehicleBelongBussName,jdbcType=VARCHAR},
      vehicle_operate_struct_name = #{vehicleOperateStructName,jdbcType=VARCHAR},
      vehicle_operate_struct_code = #{vehicleOperateStructCode,jdbcType=VARCHAR},
      vehicle_operate_buss_name = #{vehicleOperateBussName,jdbcType=VARCHAR},
      vehicle_operate_buss_code = #{vehicleOperateBussCode,jdbcType=VARCHAR},
      vehicle_creator_id = #{vehicleCreatorId,jdbcType=INTEGER},
      vehicle_creator_name = #{vehicleCreatorName,jdbcType=VARCHAR},
      vehicle_asset_city_code = #{vehicleAssetCityCode,jdbcType=VARCHAR},
      vehicle_asset_city_name = #{vehicleAssetCityName,jdbcType=VARCHAR},
      vehicle_sim_no = #{vehicleSimNo,jdbcType=VARCHAR},
      vehicle_device_no = #{vehicleDeviceNo,jdbcType=VARCHAR},
      vehicle_device_type = #{vehicleDeviceType,jdbcType=TINYINT},
      order_no = #{orderNo,jdbcType=VARCHAR},
      order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      order_company_id = #{orderCompanyId,jdbcType=INTEGER},
      order_struct_id = #{orderStructId,jdbcType=INTEGER},
      order_struct_name = #{orderStructName,jdbcType=VARCHAR},
      order_type = #{orderType,jdbcType=TINYINT},
      order_customer_id = #{orderCustomerId,jdbcType=INTEGER},
      order_customer_name = #{orderCustomerName,jdbcType=VARCHAR},
      order_customer_mobile = #{orderCustomerMobile,jdbcType=VARCHAR},
      fence_id = #{fenceId,jdbcType=INTEGER},
      fence_name = #{fenceName,jdbcType=VARCHAR},
      fence_city_code = #{fenceCityCode,jdbcType=INTEGER},
      fence_city_name = #{fenceCityName,jdbcType=VARCHAR},
      fence_warn_type = #{fenceWarnType,jdbcType=TINYINT},
      fence_address = #{fenceAddress,jdbcType=VARCHAR},
      order_associate_remark = #{orderAssociateRemark,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.TemporalSharedVehicleRecord">
    update t_temporal_shared_vehicle_record
    set warn_sn = #{warnSn,jdbcType=VARCHAR},
      warn_id = #{warnId,jdbcType=BIGINT},
      record_status = #{recordStatus,jdbcType=TINYINT},
      warn_start_time = #{warnStartTime,jdbcType=TIMESTAMP},
      warn_end_time = #{warnEndTime,jdbcType=TIMESTAMP},
      warn_start_latitude = #{warnStartLatitude,jdbcType=DECIMAL},
      warn_start_longitude = #{warnStartLongitude,jdbcType=DECIMAL},
      warn_end_latitude = #{warnEndLatitude,jdbcType=DECIMAL},
      warn_end_longitude = #{warnEndLongitude,jdbcType=DECIMAL},
      duration = #{duration,jdbcType=INTEGER},
      duration_hour = #{durationHour,jdbcType=DECIMAL},
      trip_mileage = #{tripMileage,jdbcType=DECIMAL},
      order_associate_status = #{orderAssociateStatus,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_user_id = #{updateUserId,jdbcType=INTEGER},
      update_user_name = #{updateUserName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_remark = #{updateRemark,jdbcType=VARCHAR},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      vehicle_vin = #{vehicleVin,jdbcType=VARCHAR},
      vehicle_brand_id = #{vehicleBrandId,jdbcType=INTEGER},
      vehicle_brand_code = #{vehicleBrandCode,jdbcType=VARCHAR},
      vehicle_brand = #{vehicleBrand,jdbcType=VARCHAR},
      vehicle_model_id = #{vehicleModelId,jdbcType=INTEGER},
      vehicle_model_code = #{vehicleModelCode,jdbcType=VARCHAR},
      vehicle_model = #{vehicleModel,jdbcType=VARCHAR},
      vehicle_type = #{vehicleType,jdbcType=TINYINT},
      vehicle_belong_city_code = #{vehicleBelongCityCode,jdbcType=VARCHAR},
      vehicle_belong_city_name = #{vehicleBelongCityName,jdbcType=VARCHAR},
      vehicle_company_id = #{vehicleCompanyId,jdbcType=INTEGER},
      vehicle_company_name = #{vehicleCompanyName,jdbcType=VARCHAR},
      vehicle_struct_id = #{vehicleStructId,jdbcType=INTEGER},
      vehicle_struct_name = #{vehicleStructName,jdbcType=VARCHAR},
      vehicle_self_owned = #{vehicleSelfOwned,jdbcType=TINYINT},
      vehicle_working_status = #{vehicleWorkingStatus,jdbcType=TINYINT},
      vehicle_own_struct_id = #{vehicleOwnStructId,jdbcType=INTEGER},
      vehicle_own_struct_name = #{vehicleOwnStructName,jdbcType=VARCHAR},
      vehicle_belong_struct_name = #{vehicleBelongStructName,jdbcType=VARCHAR},
      vehicle_belong_struct_code = #{vehicleBelongStructCode,jdbcType=VARCHAR},
      vehicle_belong_buss_code = #{vehicleBelongBussCode,jdbcType=VARCHAR},
      vehicle_belong_buss_name = #{vehicleBelongBussName,jdbcType=VARCHAR},
      vehicle_operate_struct_name = #{vehicleOperateStructName,jdbcType=VARCHAR},
      vehicle_operate_struct_code = #{vehicleOperateStructCode,jdbcType=VARCHAR},
      vehicle_operate_buss_name = #{vehicleOperateBussName,jdbcType=VARCHAR},
      vehicle_operate_buss_code = #{vehicleOperateBussCode,jdbcType=VARCHAR},
      vehicle_creator_id = #{vehicleCreatorId,jdbcType=INTEGER},
      vehicle_creator_name = #{vehicleCreatorName,jdbcType=VARCHAR},
      vehicle_asset_city_code = #{vehicleAssetCityCode,jdbcType=VARCHAR},
      vehicle_asset_city_name = #{vehicleAssetCityName,jdbcType=VARCHAR},
      vehicle_sim_no = #{vehicleSimNo,jdbcType=VARCHAR},
      vehicle_device_no = #{vehicleDeviceNo,jdbcType=VARCHAR},
      vehicle_device_type = #{vehicleDeviceType,jdbcType=TINYINT},
      order_no = #{orderNo,jdbcType=VARCHAR},
      order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      order_company_id = #{orderCompanyId,jdbcType=INTEGER},
      order_struct_id = #{orderStructId,jdbcType=INTEGER},
      order_struct_name = #{orderStructName,jdbcType=VARCHAR},
      order_type = #{orderType,jdbcType=TINYINT},
      order_customer_id = #{orderCustomerId,jdbcType=INTEGER},
      order_customer_name = #{orderCustomerName,jdbcType=VARCHAR},
      order_customer_mobile = #{orderCustomerMobile,jdbcType=VARCHAR},
      fence_id = #{fenceId,jdbcType=INTEGER},
      fence_name = #{fenceName,jdbcType=VARCHAR},
      fence_city_code = #{fenceCityCode,jdbcType=INTEGER},
      fence_city_name = #{fenceCityName,jdbcType=VARCHAR},
      fence_warn_type = #{fenceWarnType,jdbcType=TINYINT},
      fence_address = #{fenceAddress,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>