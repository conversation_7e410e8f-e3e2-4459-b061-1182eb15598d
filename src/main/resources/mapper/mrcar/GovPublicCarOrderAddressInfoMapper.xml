<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.GovPublicCarOrderAddressInfoMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.GovPublicCarOrderAddressInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="estimated_departure_location" jdbcType="VARCHAR" property="estimatedDepartureLocation" />
    <result column="estimated_destination_location" jdbcType="VARCHAR" property="estimatedDestinationLocation" />
    <result column="estimated_departure_latitude" jdbcType="DECIMAL" property="estimatedDepartureLatitude" />
    <result column="estimated_departure_longitude" jdbcType="DECIMAL" property="estimatedDepartureLongitude" />
    <result column="estimated_destination_latitude" jdbcType="DECIMAL" property="estimatedDestinationLatitude" />
    <result column="estimated_destination_longitude" jdbcType="DECIMAL" property="estimatedDestinationLongitude" />
    <result column="initial_fence_id" jdbcType="INTEGER" property="initialFenceId" />
    <result column="initial_latitude" jdbcType="DECIMAL" property="initialLatitude" />
    <result column="initial_longitude" jdbcType="DECIMAL" property="initialLongitude" />
    <result column="initial_location" jdbcType="VARCHAR" property="initialLocation" />
    <result column="start_fence_id" jdbcType="INTEGER" property="startFenceId" />
    <result column="start_latitude" jdbcType="DECIMAL" property="startLatitude" />
    <result column="start_longitude" jdbcType="DECIMAL" property="startLongitude" />
    <result column="start_location" jdbcType="VARCHAR" property="startLocation" />
    <result column="return_fence_id" jdbcType="INTEGER" property="returnFenceId" />
    <result column="return_latitude" jdbcType="DECIMAL" property="returnLatitude" />
    <result column="return_longitude" jdbcType="DECIMAL" property="returnLongitude" />
    <result column="return_location" jdbcType="VARCHAR" property="returnLocation" />
    <result column="alarm_code" jdbcType="VARCHAR" property="alarmCode" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="actual_departure_short_location" jdbcType="VARCHAR" property="actualDepartureShortLocation" />
    <result column="actual_destination_short_location" jdbcType="VARCHAR" property="actualDestinationShortLocation" />
    <result column="estimated_departure_short_location" jdbcType="VARCHAR" property="estimatedDepartureShortLocation" />
    <result column="estimated_destination_short_location" jdbcType="VARCHAR" property="estimatedDestinationShortLocation" />
    <result column="actual_departure_location" jdbcType="VARCHAR" property="actualDepartureLocation" />
    <result column="actual_destination_location" jdbcType="VARCHAR" property="actualDestinationLocation" />
    <result column="actual_departure_latitude" jdbcType="DECIMAL" property="actualDepartureLatitude" />
    <result column="actual_departure_longitude" jdbcType="DECIMAL" property="actualDepartureLongitude" />
    <result column="actual_destination_latitude" jdbcType="DECIMAL" property="actualDestinationLatitude" />
    <result column="actual_destination_longitude" jdbcType="DECIMAL" property="actualDestinationLongitude" />
    <result column="device_id" jdbcType="VARCHAR" property="deviceId" />
    <result column="device_type" jdbcType="TINYINT" property="deviceType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_no, company_code, estimated_departure_location, estimated_destination_location, 
    estimated_departure_latitude, estimated_departure_longitude, estimated_destination_latitude, 
    estimated_destination_longitude, initial_fence_id, initial_latitude, initial_longitude, 
    initial_location, start_fence_id, start_latitude, start_longitude, start_location, 
    return_fence_id, return_latitude, return_longitude, return_location, alarm_code, 
    create_time, update_time, actual_departure_short_location, actual_destination_short_location, 
    estimated_departure_short_location, estimated_destination_short_location, actual_departure_location, 
    actual_destination_location, actual_departure_latitude, actual_departure_longitude, 
    actual_destination_latitude, actual_destination_longitude, device_id, device_type
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.GovPublicCarOrderAddressInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from gov_public_car_order_address_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from gov_public_car_order_address_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from gov_public_car_order_address_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.order.entity.mrcar.GovPublicCarOrderAddressInfoExample">
    delete from gov_public_car_order_address_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.GovPublicCarOrderAddressInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gov_public_car_order_address_info (order_no, company_code, estimated_departure_location, 
      estimated_destination_location, estimated_departure_latitude, 
      estimated_departure_longitude, estimated_destination_latitude, 
      estimated_destination_longitude, initial_fence_id, 
      initial_latitude, initial_longitude, initial_location, 
      start_fence_id, start_latitude, start_longitude, 
      start_location, return_fence_id, return_latitude, 
      return_longitude, return_location, alarm_code, 
      create_time, update_time, actual_departure_short_location, 
      actual_destination_short_location, estimated_departure_short_location, 
      estimated_destination_short_location, actual_departure_location, 
      actual_destination_location, actual_departure_latitude, 
      actual_departure_longitude, actual_destination_latitude, 
      actual_destination_longitude, device_id, device_type
      )
    values (#{orderNo,jdbcType=VARCHAR}, #{companyCode,jdbcType=VARCHAR}, #{estimatedDepartureLocation,jdbcType=VARCHAR}, 
      #{estimatedDestinationLocation,jdbcType=VARCHAR}, #{estimatedDepartureLatitude,jdbcType=DECIMAL}, 
      #{estimatedDepartureLongitude,jdbcType=DECIMAL}, #{estimatedDestinationLatitude,jdbcType=DECIMAL}, 
      #{estimatedDestinationLongitude,jdbcType=DECIMAL}, #{initialFenceId,jdbcType=INTEGER}, 
      #{initialLatitude,jdbcType=DECIMAL}, #{initialLongitude,jdbcType=DECIMAL}, #{initialLocation,jdbcType=VARCHAR}, 
      #{startFenceId,jdbcType=INTEGER}, #{startLatitude,jdbcType=DECIMAL}, #{startLongitude,jdbcType=DECIMAL}, 
      #{startLocation,jdbcType=VARCHAR}, #{returnFenceId,jdbcType=INTEGER}, #{returnLatitude,jdbcType=DECIMAL}, 
      #{returnLongitude,jdbcType=DECIMAL}, #{returnLocation,jdbcType=VARCHAR}, #{alarmCode,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{actualDepartureShortLocation,jdbcType=VARCHAR}, 
      #{actualDestinationShortLocation,jdbcType=VARCHAR}, #{estimatedDepartureShortLocation,jdbcType=VARCHAR}, 
      #{estimatedDestinationShortLocation,jdbcType=VARCHAR}, #{actualDepartureLocation,jdbcType=VARCHAR}, 
      #{actualDestinationLocation,jdbcType=VARCHAR}, #{actualDepartureLatitude,jdbcType=DECIMAL}, 
      #{actualDepartureLongitude,jdbcType=DECIMAL}, #{actualDestinationLatitude,jdbcType=DECIMAL}, 
      #{actualDestinationLongitude,jdbcType=DECIMAL}, #{deviceId,jdbcType=VARCHAR}, #{deviceType,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.GovPublicCarOrderAddressInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gov_public_car_order_address_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="estimatedDepartureLocation != null">
        estimated_departure_location,
      </if>
      <if test="estimatedDestinationLocation != null">
        estimated_destination_location,
      </if>
      <if test="estimatedDepartureLatitude != null">
        estimated_departure_latitude,
      </if>
      <if test="estimatedDepartureLongitude != null">
        estimated_departure_longitude,
      </if>
      <if test="estimatedDestinationLatitude != null">
        estimated_destination_latitude,
      </if>
      <if test="estimatedDestinationLongitude != null">
        estimated_destination_longitude,
      </if>
      <if test="initialFenceId != null">
        initial_fence_id,
      </if>
      <if test="initialLatitude != null">
        initial_latitude,
      </if>
      <if test="initialLongitude != null">
        initial_longitude,
      </if>
      <if test="initialLocation != null">
        initial_location,
      </if>
      <if test="startFenceId != null">
        start_fence_id,
      </if>
      <if test="startLatitude != null">
        start_latitude,
      </if>
      <if test="startLongitude != null">
        start_longitude,
      </if>
      <if test="startLocation != null">
        start_location,
      </if>
      <if test="returnFenceId != null">
        return_fence_id,
      </if>
      <if test="returnLatitude != null">
        return_latitude,
      </if>
      <if test="returnLongitude != null">
        return_longitude,
      </if>
      <if test="returnLocation != null">
        return_location,
      </if>
      <if test="alarmCode != null">
        alarm_code,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="actualDepartureShortLocation != null">
        actual_departure_short_location,
      </if>
      <if test="actualDestinationShortLocation != null">
        actual_destination_short_location,
      </if>
      <if test="estimatedDepartureShortLocation != null">
        estimated_departure_short_location,
      </if>
      <if test="estimatedDestinationShortLocation != null">
        estimated_destination_short_location,
      </if>
      <if test="actualDepartureLocation != null">
        actual_departure_location,
      </if>
      <if test="actualDestinationLocation != null">
        actual_destination_location,
      </if>
      <if test="actualDepartureLatitude != null">
        actual_departure_latitude,
      </if>
      <if test="actualDepartureLongitude != null">
        actual_departure_longitude,
      </if>
      <if test="actualDestinationLatitude != null">
        actual_destination_latitude,
      </if>
      <if test="actualDestinationLongitude != null">
        actual_destination_longitude,
      </if>
      <if test="deviceId != null">
        device_id,
      </if>
      <if test="deviceType != null">
        device_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="estimatedDepartureLocation != null">
        #{estimatedDepartureLocation,jdbcType=VARCHAR},
      </if>
      <if test="estimatedDestinationLocation != null">
        #{estimatedDestinationLocation,jdbcType=VARCHAR},
      </if>
      <if test="estimatedDepartureLatitude != null">
        #{estimatedDepartureLatitude,jdbcType=DECIMAL},
      </if>
      <if test="estimatedDepartureLongitude != null">
        #{estimatedDepartureLongitude,jdbcType=DECIMAL},
      </if>
      <if test="estimatedDestinationLatitude != null">
        #{estimatedDestinationLatitude,jdbcType=DECIMAL},
      </if>
      <if test="estimatedDestinationLongitude != null">
        #{estimatedDestinationLongitude,jdbcType=DECIMAL},
      </if>
      <if test="initialFenceId != null">
        #{initialFenceId,jdbcType=INTEGER},
      </if>
      <if test="initialLatitude != null">
        #{initialLatitude,jdbcType=DECIMAL},
      </if>
      <if test="initialLongitude != null">
        #{initialLongitude,jdbcType=DECIMAL},
      </if>
      <if test="initialLocation != null">
        #{initialLocation,jdbcType=VARCHAR},
      </if>
      <if test="startFenceId != null">
        #{startFenceId,jdbcType=INTEGER},
      </if>
      <if test="startLatitude != null">
        #{startLatitude,jdbcType=DECIMAL},
      </if>
      <if test="startLongitude != null">
        #{startLongitude,jdbcType=DECIMAL},
      </if>
      <if test="startLocation != null">
        #{startLocation,jdbcType=VARCHAR},
      </if>
      <if test="returnFenceId != null">
        #{returnFenceId,jdbcType=INTEGER},
      </if>
      <if test="returnLatitude != null">
        #{returnLatitude,jdbcType=DECIMAL},
      </if>
      <if test="returnLongitude != null">
        #{returnLongitude,jdbcType=DECIMAL},
      </if>
      <if test="returnLocation != null">
        #{returnLocation,jdbcType=VARCHAR},
      </if>
      <if test="alarmCode != null">
        #{alarmCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="actualDepartureShortLocation != null">
        #{actualDepartureShortLocation,jdbcType=VARCHAR},
      </if>
      <if test="actualDestinationShortLocation != null">
        #{actualDestinationShortLocation,jdbcType=VARCHAR},
      </if>
      <if test="estimatedDepartureShortLocation != null">
        #{estimatedDepartureShortLocation,jdbcType=VARCHAR},
      </if>
      <if test="estimatedDestinationShortLocation != null">
        #{estimatedDestinationShortLocation,jdbcType=VARCHAR},
      </if>
      <if test="actualDepartureLocation != null">
        #{actualDepartureLocation,jdbcType=VARCHAR},
      </if>
      <if test="actualDestinationLocation != null">
        #{actualDestinationLocation,jdbcType=VARCHAR},
      </if>
      <if test="actualDepartureLatitude != null">
        #{actualDepartureLatitude,jdbcType=DECIMAL},
      </if>
      <if test="actualDepartureLongitude != null">
        #{actualDepartureLongitude,jdbcType=DECIMAL},
      </if>
      <if test="actualDestinationLatitude != null">
        #{actualDestinationLatitude,jdbcType=DECIMAL},
      </if>
      <if test="actualDestinationLongitude != null">
        #{actualDestinationLongitude,jdbcType=DECIMAL},
      </if>
      <if test="deviceId != null">
        #{deviceId,jdbcType=VARCHAR},
      </if>
      <if test="deviceType != null">
        #{deviceType,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.GovPublicCarOrderAddressInfoExample" resultType="java.lang.Long">
    select count(*) from gov_public_car_order_address_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update gov_public_car_order_address_info
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.orderNo != null">
        order_no = #{row.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="row.companyCode != null">
        company_code = #{row.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="row.estimatedDepartureLocation != null">
        estimated_departure_location = #{row.estimatedDepartureLocation,jdbcType=VARCHAR},
      </if>
      <if test="row.estimatedDestinationLocation != null">
        estimated_destination_location = #{row.estimatedDestinationLocation,jdbcType=VARCHAR},
      </if>
      <if test="row.estimatedDepartureLatitude != null">
        estimated_departure_latitude = #{row.estimatedDepartureLatitude,jdbcType=DECIMAL},
      </if>
      <if test="row.estimatedDepartureLongitude != null">
        estimated_departure_longitude = #{row.estimatedDepartureLongitude,jdbcType=DECIMAL},
      </if>
      <if test="row.estimatedDestinationLatitude != null">
        estimated_destination_latitude = #{row.estimatedDestinationLatitude,jdbcType=DECIMAL},
      </if>
      <if test="row.estimatedDestinationLongitude != null">
        estimated_destination_longitude = #{row.estimatedDestinationLongitude,jdbcType=DECIMAL},
      </if>
      <if test="row.initialFenceId != null">
        initial_fence_id = #{row.initialFenceId,jdbcType=INTEGER},
      </if>
      <if test="row.initialLatitude != null">
        initial_latitude = #{row.initialLatitude,jdbcType=DECIMAL},
      </if>
      <if test="row.initialLongitude != null">
        initial_longitude = #{row.initialLongitude,jdbcType=DECIMAL},
      </if>
      <if test="row.initialLocation != null">
        initial_location = #{row.initialLocation,jdbcType=VARCHAR},
      </if>
      <if test="row.startFenceId != null">
        start_fence_id = #{row.startFenceId,jdbcType=INTEGER},
      </if>
      <if test="row.startLatitude != null">
        start_latitude = #{row.startLatitude,jdbcType=DECIMAL},
      </if>
      <if test="row.startLongitude != null">
        start_longitude = #{row.startLongitude,jdbcType=DECIMAL},
      </if>
      <if test="row.startLocation != null">
        start_location = #{row.startLocation,jdbcType=VARCHAR},
      </if>
      <if test="row.returnFenceId != null">
        return_fence_id = #{row.returnFenceId,jdbcType=INTEGER},
      </if>
      <if test="row.returnLatitude != null">
        return_latitude = #{row.returnLatitude,jdbcType=DECIMAL},
      </if>
      <if test="row.returnLongitude != null">
        return_longitude = #{row.returnLongitude,jdbcType=DECIMAL},
      </if>
      <if test="row.returnLocation != null">
        return_location = #{row.returnLocation,jdbcType=VARCHAR},
      </if>
      <if test="row.alarmCode != null">
        alarm_code = #{row.alarmCode,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.actualDepartureShortLocation != null">
        actual_departure_short_location = #{row.actualDepartureShortLocation,jdbcType=VARCHAR},
      </if>
      <if test="row.actualDestinationShortLocation != null">
        actual_destination_short_location = #{row.actualDestinationShortLocation,jdbcType=VARCHAR},
      </if>
      <if test="row.estimatedDepartureShortLocation != null">
        estimated_departure_short_location = #{row.estimatedDepartureShortLocation,jdbcType=VARCHAR},
      </if>
      <if test="row.estimatedDestinationShortLocation != null">
        estimated_destination_short_location = #{row.estimatedDestinationShortLocation,jdbcType=VARCHAR},
      </if>
      <if test="row.actualDepartureLocation != null">
        actual_departure_location = #{row.actualDepartureLocation,jdbcType=VARCHAR},
      </if>
      <if test="row.actualDestinationLocation != null">
        actual_destination_location = #{row.actualDestinationLocation,jdbcType=VARCHAR},
      </if>
      <if test="row.actualDepartureLatitude != null">
        actual_departure_latitude = #{row.actualDepartureLatitude,jdbcType=DECIMAL},
      </if>
      <if test="row.actualDepartureLongitude != null">
        actual_departure_longitude = #{row.actualDepartureLongitude,jdbcType=DECIMAL},
      </if>
      <if test="row.actualDestinationLatitude != null">
        actual_destination_latitude = #{row.actualDestinationLatitude,jdbcType=DECIMAL},
      </if>
      <if test="row.actualDestinationLongitude != null">
        actual_destination_longitude = #{row.actualDestinationLongitude,jdbcType=DECIMAL},
      </if>
      <if test="row.deviceId != null">
        device_id = #{row.deviceId,jdbcType=VARCHAR},
      </if>
      <if test="row.deviceType != null">
        device_type = #{row.deviceType,jdbcType=TINYINT},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update gov_public_car_order_address_info
    set id = #{row.id,jdbcType=BIGINT},
      order_no = #{row.orderNo,jdbcType=VARCHAR},
      company_code = #{row.companyCode,jdbcType=VARCHAR},
      estimated_departure_location = #{row.estimatedDepartureLocation,jdbcType=VARCHAR},
      estimated_destination_location = #{row.estimatedDestinationLocation,jdbcType=VARCHAR},
      estimated_departure_latitude = #{row.estimatedDepartureLatitude,jdbcType=DECIMAL},
      estimated_departure_longitude = #{row.estimatedDepartureLongitude,jdbcType=DECIMAL},
      estimated_destination_latitude = #{row.estimatedDestinationLatitude,jdbcType=DECIMAL},
      estimated_destination_longitude = #{row.estimatedDestinationLongitude,jdbcType=DECIMAL},
      initial_fence_id = #{row.initialFenceId,jdbcType=INTEGER},
      initial_latitude = #{row.initialLatitude,jdbcType=DECIMAL},
      initial_longitude = #{row.initialLongitude,jdbcType=DECIMAL},
      initial_location = #{row.initialLocation,jdbcType=VARCHAR},
      start_fence_id = #{row.startFenceId,jdbcType=INTEGER},
      start_latitude = #{row.startLatitude,jdbcType=DECIMAL},
      start_longitude = #{row.startLongitude,jdbcType=DECIMAL},
      start_location = #{row.startLocation,jdbcType=VARCHAR},
      return_fence_id = #{row.returnFenceId,jdbcType=INTEGER},
      return_latitude = #{row.returnLatitude,jdbcType=DECIMAL},
      return_longitude = #{row.returnLongitude,jdbcType=DECIMAL},
      return_location = #{row.returnLocation,jdbcType=VARCHAR},
      alarm_code = #{row.alarmCode,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      actual_departure_short_location = #{row.actualDepartureShortLocation,jdbcType=VARCHAR},
      actual_destination_short_location = #{row.actualDestinationShortLocation,jdbcType=VARCHAR},
      estimated_departure_short_location = #{row.estimatedDepartureShortLocation,jdbcType=VARCHAR},
      estimated_destination_short_location = #{row.estimatedDestinationShortLocation,jdbcType=VARCHAR},
      actual_departure_location = #{row.actualDepartureLocation,jdbcType=VARCHAR},
      actual_destination_location = #{row.actualDestinationLocation,jdbcType=VARCHAR},
      actual_departure_latitude = #{row.actualDepartureLatitude,jdbcType=DECIMAL},
      actual_departure_longitude = #{row.actualDepartureLongitude,jdbcType=DECIMAL},
      actual_destination_latitude = #{row.actualDestinationLatitude,jdbcType=DECIMAL},
      actual_destination_longitude = #{row.actualDestinationLongitude,jdbcType=DECIMAL},
      device_id = #{row.deviceId,jdbcType=VARCHAR},
      device_type = #{row.deviceType,jdbcType=TINYINT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.GovPublicCarOrderAddressInfo">
    update gov_public_car_order_address_info
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="estimatedDepartureLocation != null">
        estimated_departure_location = #{estimatedDepartureLocation,jdbcType=VARCHAR},
      </if>
      <if test="estimatedDestinationLocation != null">
        estimated_destination_location = #{estimatedDestinationLocation,jdbcType=VARCHAR},
      </if>
      <if test="estimatedDepartureLatitude != null">
        estimated_departure_latitude = #{estimatedDepartureLatitude,jdbcType=DECIMAL},
      </if>
      <if test="estimatedDepartureLongitude != null">
        estimated_departure_longitude = #{estimatedDepartureLongitude,jdbcType=DECIMAL},
      </if>
      <if test="estimatedDestinationLatitude != null">
        estimated_destination_latitude = #{estimatedDestinationLatitude,jdbcType=DECIMAL},
      </if>
      <if test="estimatedDestinationLongitude != null">
        estimated_destination_longitude = #{estimatedDestinationLongitude,jdbcType=DECIMAL},
      </if>
      <if test="initialFenceId != null">
        initial_fence_id = #{initialFenceId,jdbcType=INTEGER},
      </if>
      <if test="initialLatitude != null">
        initial_latitude = #{initialLatitude,jdbcType=DECIMAL},
      </if>
      <if test="initialLongitude != null">
        initial_longitude = #{initialLongitude,jdbcType=DECIMAL},
      </if>
      <if test="initialLocation != null">
        initial_location = #{initialLocation,jdbcType=VARCHAR},
      </if>
      <if test="startFenceId != null">
        start_fence_id = #{startFenceId,jdbcType=INTEGER},
      </if>
      <if test="startLatitude != null">
        start_latitude = #{startLatitude,jdbcType=DECIMAL},
      </if>
      <if test="startLongitude != null">
        start_longitude = #{startLongitude,jdbcType=DECIMAL},
      </if>
      <if test="startLocation != null">
        start_location = #{startLocation,jdbcType=VARCHAR},
      </if>
      <if test="returnFenceId != null">
        return_fence_id = #{returnFenceId,jdbcType=INTEGER},
      </if>
      <if test="returnLatitude != null">
        return_latitude = #{returnLatitude,jdbcType=DECIMAL},
      </if>
      <if test="returnLongitude != null">
        return_longitude = #{returnLongitude,jdbcType=DECIMAL},
      </if>
      <if test="returnLocation != null">
        return_location = #{returnLocation,jdbcType=VARCHAR},
      </if>
      <if test="alarmCode != null">
        alarm_code = #{alarmCode,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="actualDepartureShortLocation != null">
        actual_departure_short_location = #{actualDepartureShortLocation,jdbcType=VARCHAR},
      </if>
      <if test="actualDestinationShortLocation != null">
        actual_destination_short_location = #{actualDestinationShortLocation,jdbcType=VARCHAR},
      </if>
      <if test="estimatedDepartureShortLocation != null">
        estimated_departure_short_location = #{estimatedDepartureShortLocation,jdbcType=VARCHAR},
      </if>
      <if test="estimatedDestinationShortLocation != null">
        estimated_destination_short_location = #{estimatedDestinationShortLocation,jdbcType=VARCHAR},
      </if>
      <if test="actualDepartureLocation != null">
        actual_departure_location = #{actualDepartureLocation,jdbcType=VARCHAR},
      </if>
      <if test="actualDestinationLocation != null">
        actual_destination_location = #{actualDestinationLocation,jdbcType=VARCHAR},
      </if>
      <if test="actualDepartureLatitude != null">
        actual_departure_latitude = #{actualDepartureLatitude,jdbcType=DECIMAL},
      </if>
      <if test="actualDepartureLongitude != null">
        actual_departure_longitude = #{actualDepartureLongitude,jdbcType=DECIMAL},
      </if>
      <if test="actualDestinationLatitude != null">
        actual_destination_latitude = #{actualDestinationLatitude,jdbcType=DECIMAL},
      </if>
      <if test="actualDestinationLongitude != null">
        actual_destination_longitude = #{actualDestinationLongitude,jdbcType=DECIMAL},
      </if>
      <if test="deviceId != null">
        device_id = #{deviceId,jdbcType=VARCHAR},
      </if>
      <if test="deviceType != null">
        device_type = #{deviceType,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.GovPublicCarOrderAddressInfo">
    update gov_public_car_order_address_info
    set order_no = #{orderNo,jdbcType=VARCHAR},
      company_code = #{companyCode,jdbcType=VARCHAR},
      estimated_departure_location = #{estimatedDepartureLocation,jdbcType=VARCHAR},
      estimated_destination_location = #{estimatedDestinationLocation,jdbcType=VARCHAR},
      estimated_departure_latitude = #{estimatedDepartureLatitude,jdbcType=DECIMAL},
      estimated_departure_longitude = #{estimatedDepartureLongitude,jdbcType=DECIMAL},
      estimated_destination_latitude = #{estimatedDestinationLatitude,jdbcType=DECIMAL},
      estimated_destination_longitude = #{estimatedDestinationLongitude,jdbcType=DECIMAL},
      initial_fence_id = #{initialFenceId,jdbcType=INTEGER},
      initial_latitude = #{initialLatitude,jdbcType=DECIMAL},
      initial_longitude = #{initialLongitude,jdbcType=DECIMAL},
      initial_location = #{initialLocation,jdbcType=VARCHAR},
      start_fence_id = #{startFenceId,jdbcType=INTEGER},
      start_latitude = #{startLatitude,jdbcType=DECIMAL},
      start_longitude = #{startLongitude,jdbcType=DECIMAL},
      start_location = #{startLocation,jdbcType=VARCHAR},
      return_fence_id = #{returnFenceId,jdbcType=INTEGER},
      return_latitude = #{returnLatitude,jdbcType=DECIMAL},
      return_longitude = #{returnLongitude,jdbcType=DECIMAL},
      return_location = #{returnLocation,jdbcType=VARCHAR},
      alarm_code = #{alarmCode,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      actual_departure_short_location = #{actualDepartureShortLocation,jdbcType=VARCHAR},
      actual_destination_short_location = #{actualDestinationShortLocation,jdbcType=VARCHAR},
      estimated_departure_short_location = #{estimatedDepartureShortLocation,jdbcType=VARCHAR},
      estimated_destination_short_location = #{estimatedDestinationShortLocation,jdbcType=VARCHAR},
      actual_departure_location = #{actualDepartureLocation,jdbcType=VARCHAR},
      actual_destination_location = #{actualDestinationLocation,jdbcType=VARCHAR},
      actual_departure_latitude = #{actualDepartureLatitude,jdbcType=DECIMAL},
      actual_departure_longitude = #{actualDepartureLongitude,jdbcType=DECIMAL},
      actual_destination_latitude = #{actualDestinationLatitude,jdbcType=DECIMAL},
      actual_destination_longitude = #{actualDestinationLongitude,jdbcType=DECIMAL},
      device_id = #{deviceId,jdbcType=VARCHAR},
      device_type = #{deviceType,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>