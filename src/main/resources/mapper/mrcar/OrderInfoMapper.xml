<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.OrderInfoMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.OrderInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="order_apply_no" jdbcType="VARCHAR" property="orderApplyNo" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="struct_id" jdbcType="INTEGER" property="structId" />
    <result column="order_type" jdbcType="TINYINT" property="orderType" />
    <result column="service_code" jdbcType="VARCHAR" property="serviceCode" />
    <result column="customer_id" jdbcType="INTEGER" property="customerId" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="customer_mobile" jdbcType="VARCHAR" property="customerMobile" />
    <result column="himself" jdbcType="BIT" property="himself" />
    <result column="booking_passenger_user_id" jdbcType="BIGINT" property="bookingPassengerUserId" />
    <result column="booking_passenger_user_name" jdbcType="VARCHAR" property="bookingPassengerUserName" />
    <result column="booking_passenger_user_phone" jdbcType="VARCHAR" property="bookingPassengerUserPhone" />
    <result column="booking_order_stime" jdbcType="TIMESTAMP" property="bookingOrderStime" />
    <result column="booking_order_etime" jdbcType="TIMESTAMP" property="bookingOrderEtime" />
    <result column="channel_order_code" jdbcType="VARCHAR" property="channelOrderCode" />
    <result column="booking_start_long_addr" jdbcType="VARCHAR" property="bookingStartLongAddr" />
    <result column="booking_start_short_addr" jdbcType="VARCHAR" property="bookingStartShortAddr" />
    <result column="booking_start_point" jdbcType="VARCHAR" property="bookingStartPoint" />
    <result column="booking_end_long_addr" jdbcType="VARCHAR" property="bookingEndLongAddr" />
    <result column="booking_end_short_addr" jdbcType="VARCHAR" property="bookingEndShortAddr" />
    <result column="booking_end_point" jdbcType="VARCHAR" property="bookingEndPoint" />
    <result column="start_city_code" jdbcType="INTEGER" property="startCityCode" />
    <result column="start_city_name" jdbcType="VARCHAR" property="startCityName" />
    <result column="end_city_code" jdbcType="INTEGER" property="endCityCode" />
    <result column="end_city_name" jdbcType="VARCHAR" property="endCityName" />
    <result column="booking_carlevel_id" jdbcType="TINYINT" property="bookingCarlevelId" />
    <result column="booking_carlevel_name" jdbcType="VARCHAR" property="bookingCarlevelName" />
    <result column="order_cancellation_type" jdbcType="TINYINT" property="orderCancellationType" />
    <result column="assign_car_can_able" jdbcType="BIT" property="assignCarCanAble" />
    <result column="assign_car_status" jdbcType="TINYINT" property="assignCarStatus" />
    <result column="assign_car_apply_time" jdbcType="TIMESTAMP" property="assignCarApplyTime" />
    <result column="assign_car_apply_count" jdbcType="SMALLINT" property="assignCarApplyCount" />
    <result column="assign_car_success_time" jdbcType="TIMESTAMP" property="assignCarSuccessTime" />
    <result column="assign_car_failure_time" jdbcType="TIMESTAMP" property="assignCarFailureTime" />
    <result column="assign_carlevel_id" jdbcType="TINYINT" property="assignCarlevelId" />
    <result column="assign_carlevel_name" jdbcType="VARCHAR" property="assignCarlevelName" />
    <result column="assign_car_id" jdbcType="BIGINT" property="assignCarId" />
    <result column="assign_car_license" jdbcType="VARCHAR" property="assignCarLicense" />
    <result column="assign_driver_id" jdbcType="BIGINT" property="assignDriverId" />
    <result column="assign_driver_name" jdbcType="VARCHAR" property="assignDriverName" />
    <result column="assign_driver_phone" jdbcType="VARCHAR" property="assignDriverPhone" />
    <result column="assign_driver_city_code" jdbcType="INTEGER" property="assignDriverCityCode" />
    <result column="assign_driver_city_name" jdbcType="VARCHAR" property="assignDriverCityName" />
    <result column="assign_carmodel_id" jdbcType="INTEGER" property="assignCarmodelId" />
    <result column="assign_carmodel_name" jdbcType="VARCHAR" property="assignCarmodelName" />
    <result column="order_cancel_no" jdbcType="VARCHAR" property="orderCancelNo" />
    <result column="order_cancel_time" jdbcType="TIMESTAMP" property="orderCancelTime" />
    <result column="appraise_submited" jdbcType="BIT" property="appraiseSubmited" />
    <result column="appraise_submit_time" jdbcType="TIMESTAMP" property="appraiseSubmitTime" />
    <result column="traffic_type" jdbcType="TINYINT" property="trafficType" />
    <result column="traffic_number" jdbcType="VARCHAR" property="trafficNumber" />
    <result column="fact_start_long_addr" jdbcType="VARCHAR" property="factStartLongAddr" />
    <result column="fact_start_short_addr" jdbcType="VARCHAR" property="factStartShortAddr" />
    <result column="fact_start_point" jdbcType="VARCHAR" property="factStartPoint" />
    <result column="fact_end_long_addr" jdbcType="VARCHAR" property="factEndLongAddr" />
    <result column="fact_end_short_addr" jdbcType="VARCHAR" property="factEndShortAddr" />
    <result column="fact_end_point" jdbcType="VARCHAR" property="factEndPoint" />
    <result column="fact_start_date" jdbcType="TIMESTAMP" property="factStartDate" />
    <result column="fact_end_date" jdbcType="TIMESTAMP" property="factEndDate" />
    <result column="driver_start_time" jdbcType="TIMESTAMP" property="driverStartTime" />
    <result column="driver_arrive_time" jdbcType="TIMESTAMP" property="driverArriveTime" />
    <result column="get_on_time" jdbcType="TIMESTAMP" property="getOnTime" />
    <result column="order_status" jdbcType="SMALLINT" property="orderStatus" />
    <result column="order_detail" jdbcType="VARCHAR" property="orderDetail" />
    <result column="mileage_source" jdbcType="TINYINT" property="mileageSource" />
    <result column="trip_mileage" jdbcType="DECIMAL" property="tripMileage" />
    <result column="motorcade_id" jdbcType="INTEGER" property="motorcadeId" />
    <result column="motorcade_name" jdbcType="VARCHAR" property="motorcadeName" />
    <result column="customer_company_id" jdbcType="INTEGER" property="customerCompanyId" />
    <result column="customer_company_code" jdbcType="VARCHAR" property="customerCompanyCode" />
    <result column="customer_company_name" jdbcType="VARCHAR" property="customerCompanyName" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="fixed_price_valid" jdbcType="BIT" property="fixedPriceValid" />
    <result column="fixed_price" jdbcType="DECIMAL" property="fixedPrice" />
    <result column="struct_name" jdbcType="VARCHAR" property="structName" />
    <result column="passenger_struct_id" jdbcType="INTEGER" property="passengerStructId" />
    <result column="passenger_struct_name" jdbcType="VARCHAR" property="passengerStructName" />
    <result column="user_exception_mark" jdbcType="VARCHAR" property="userExceptionMark" />
    <result column="mileage_stat_type" jdbcType="TINYINT" property="mileageStatType" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="vehicle_privacy_flag" jdbcType="TINYINT" property="vehiclePrivacyFlag" />
    <result column="vehicle_company_id" jdbcType="INTEGER" property="vehicleCompanyId" />
    <result column="vehicle_company_name" jdbcType="VARCHAR" property="vehicleCompanyName" />
    <result column="driver_company_id" jdbcType="INTEGER" property="driverCompanyId" />
    <result column="driver_company_name" jdbcType="VARCHAR" property="driverCompanyName" />
    <result column="third_order_no" jdbcType="VARCHAR" property="thirdOrderNo" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, create_time, update_time, order_no, order_apply_no, company_id, struct_id, order_type, 
    service_code, customer_id, customer_name, customer_mobile, himself, booking_passenger_user_id, 
    booking_passenger_user_name, booking_passenger_user_phone, booking_order_stime, booking_order_etime, 
    channel_order_code, booking_start_long_addr, booking_start_short_addr, booking_start_point, 
    booking_end_long_addr, booking_end_short_addr, booking_end_point, start_city_code, 
    start_city_name, end_city_code, end_city_name, booking_carlevel_id, booking_carlevel_name, 
    order_cancellation_type, assign_car_can_able, assign_car_status, assign_car_apply_time, 
    assign_car_apply_count, assign_car_success_time, assign_car_failure_time, assign_carlevel_id, 
    assign_carlevel_name, assign_car_id, assign_car_license, assign_driver_id, assign_driver_name, 
    assign_driver_phone, assign_driver_city_code, assign_driver_city_name, assign_carmodel_id, 
    assign_carmodel_name, order_cancel_no, order_cancel_time, appraise_submited, appraise_submit_time, 
    traffic_type, traffic_number, fact_start_long_addr, fact_start_short_addr, fact_start_point, 
    fact_end_long_addr, fact_end_short_addr, fact_end_point, fact_start_date, fact_end_date, 
    driver_start_time, driver_arrive_time, get_on_time, order_status, order_detail, mileage_source, 
    trip_mileage, motorcade_id, motorcade_name, customer_company_id, customer_company_code, 
    customer_company_name, company_name, fixed_price_valid, fixed_price, struct_name, 
    passenger_struct_id, passenger_struct_name, user_exception_mark, mileage_stat_type, 
    deleted, vehicle_privacy_flag, vehicle_company_id, vehicle_company_name, driver_company_id, 
    driver_company_name, third_order_no
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.OrderInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from order_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from order_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.order.entity.mrcar.OrderInfoExample">
    delete from order_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.OrderInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_info (create_time, update_time, order_no, 
      order_apply_no, company_id, struct_id, 
      order_type, service_code, customer_id, 
      customer_name, customer_mobile, himself, 
      booking_passenger_user_id, booking_passenger_user_name, 
      booking_passenger_user_phone, booking_order_stime, 
      booking_order_etime, channel_order_code, 
      booking_start_long_addr, booking_start_short_addr, 
      booking_start_point, booking_end_long_addr, 
      booking_end_short_addr, booking_end_point, start_city_code, 
      start_city_name, end_city_code, end_city_name, 
      booking_carlevel_id, booking_carlevel_name, 
      order_cancellation_type, assign_car_can_able, assign_car_status, 
      assign_car_apply_time, assign_car_apply_count, 
      assign_car_success_time, assign_car_failure_time, 
      assign_carlevel_id, assign_carlevel_name, assign_car_id, 
      assign_car_license, assign_driver_id, assign_driver_name, 
      assign_driver_phone, assign_driver_city_code, 
      assign_driver_city_name, assign_carmodel_id, 
      assign_carmodel_name, order_cancel_no, order_cancel_time, 
      appraise_submited, appraise_submit_time, traffic_type, 
      traffic_number, fact_start_long_addr, fact_start_short_addr, 
      fact_start_point, fact_end_long_addr, fact_end_short_addr, 
      fact_end_point, fact_start_date, fact_end_date, 
      driver_start_time, driver_arrive_time, 
      get_on_time, order_status, order_detail, 
      mileage_source, trip_mileage, motorcade_id, 
      motorcade_name, customer_company_id, customer_company_code, 
      customer_company_name, company_name, fixed_price_valid, 
      fixed_price, struct_name, passenger_struct_id, 
      passenger_struct_name, user_exception_mark, 
      mileage_stat_type, deleted, vehicle_privacy_flag, 
      vehicle_company_id, vehicle_company_name, driver_company_id, 
      driver_company_name, third_order_no)
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{orderNo,jdbcType=VARCHAR}, 
      #{orderApplyNo,jdbcType=VARCHAR}, #{companyId,jdbcType=INTEGER}, #{structId,jdbcType=INTEGER}, 
      #{orderType,jdbcType=TINYINT}, #{serviceCode,jdbcType=VARCHAR}, #{customerId,jdbcType=INTEGER}, 
      #{customerName,jdbcType=VARCHAR}, #{customerMobile,jdbcType=VARCHAR}, #{himself,jdbcType=BIT}, 
      #{bookingPassengerUserId,jdbcType=BIGINT}, #{bookingPassengerUserName,jdbcType=VARCHAR}, 
      #{bookingPassengerUserPhone,jdbcType=VARCHAR}, #{bookingOrderStime,jdbcType=TIMESTAMP}, 
      #{bookingOrderEtime,jdbcType=TIMESTAMP}, #{channelOrderCode,jdbcType=VARCHAR}, 
      #{bookingStartLongAddr,jdbcType=VARCHAR}, #{bookingStartShortAddr,jdbcType=VARCHAR}, 
      #{bookingStartPoint,jdbcType=VARCHAR}, #{bookingEndLongAddr,jdbcType=VARCHAR}, 
      #{bookingEndShortAddr,jdbcType=VARCHAR}, #{bookingEndPoint,jdbcType=VARCHAR}, #{startCityCode,jdbcType=INTEGER}, 
      #{startCityName,jdbcType=VARCHAR}, #{endCityCode,jdbcType=INTEGER}, #{endCityName,jdbcType=VARCHAR}, 
      #{bookingCarlevelId,jdbcType=TINYINT}, #{bookingCarlevelName,jdbcType=VARCHAR}, 
      #{orderCancellationType,jdbcType=TINYINT}, #{assignCarCanAble,jdbcType=BIT}, #{assignCarStatus,jdbcType=TINYINT}, 
      #{assignCarApplyTime,jdbcType=TIMESTAMP}, #{assignCarApplyCount,jdbcType=SMALLINT}, 
      #{assignCarSuccessTime,jdbcType=TIMESTAMP}, #{assignCarFailureTime,jdbcType=TIMESTAMP}, 
      #{assignCarlevelId,jdbcType=TINYINT}, #{assignCarlevelName,jdbcType=VARCHAR}, #{assignCarId,jdbcType=BIGINT}, 
      #{assignCarLicense,jdbcType=VARCHAR}, #{assignDriverId,jdbcType=BIGINT}, #{assignDriverName,jdbcType=VARCHAR}, 
      #{assignDriverPhone,jdbcType=VARCHAR}, #{assignDriverCityCode,jdbcType=INTEGER}, 
      #{assignDriverCityName,jdbcType=VARCHAR}, #{assignCarmodelId,jdbcType=INTEGER}, 
      #{assignCarmodelName,jdbcType=VARCHAR}, #{orderCancelNo,jdbcType=VARCHAR}, #{orderCancelTime,jdbcType=TIMESTAMP}, 
      #{appraiseSubmited,jdbcType=BIT}, #{appraiseSubmitTime,jdbcType=TIMESTAMP}, #{trafficType,jdbcType=TINYINT}, 
      #{trafficNumber,jdbcType=VARCHAR}, #{factStartLongAddr,jdbcType=VARCHAR}, #{factStartShortAddr,jdbcType=VARCHAR}, 
      #{factStartPoint,jdbcType=VARCHAR}, #{factEndLongAddr,jdbcType=VARCHAR}, #{factEndShortAddr,jdbcType=VARCHAR}, 
      #{factEndPoint,jdbcType=VARCHAR}, #{factStartDate,jdbcType=TIMESTAMP}, #{factEndDate,jdbcType=TIMESTAMP}, 
      #{driverStartTime,jdbcType=TIMESTAMP}, #{driverArriveTime,jdbcType=TIMESTAMP}, 
      #{getOnTime,jdbcType=TIMESTAMP}, #{orderStatus,jdbcType=SMALLINT}, #{orderDetail,jdbcType=VARCHAR}, 
      #{mileageSource,jdbcType=TINYINT}, #{tripMileage,jdbcType=DECIMAL}, #{motorcadeId,jdbcType=INTEGER}, 
      #{motorcadeName,jdbcType=VARCHAR}, #{customerCompanyId,jdbcType=INTEGER}, #{customerCompanyCode,jdbcType=VARCHAR}, 
      #{customerCompanyName,jdbcType=VARCHAR}, #{companyName,jdbcType=VARCHAR}, #{fixedPriceValid,jdbcType=BIT}, 
      #{fixedPrice,jdbcType=DECIMAL}, #{structName,jdbcType=VARCHAR}, #{passengerStructId,jdbcType=INTEGER}, 
      #{passengerStructName,jdbcType=VARCHAR}, #{userExceptionMark,jdbcType=VARCHAR}, 
      #{mileageStatType,jdbcType=TINYINT}, #{deleted,jdbcType=BIT}, #{vehiclePrivacyFlag,jdbcType=TINYINT}, 
      #{vehicleCompanyId,jdbcType=INTEGER}, #{vehicleCompanyName,jdbcType=VARCHAR}, #{driverCompanyId,jdbcType=INTEGER}, 
      #{driverCompanyName,jdbcType=VARCHAR}, #{thirdOrderNo,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.OrderInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="orderApplyNo != null">
        order_apply_no,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="structId != null">
        struct_id,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="serviceCode != null">
        service_code,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="customerMobile != null">
        customer_mobile,
      </if>
      <if test="himself != null">
        himself,
      </if>
      <if test="bookingPassengerUserId != null">
        booking_passenger_user_id,
      </if>
      <if test="bookingPassengerUserName != null">
        booking_passenger_user_name,
      </if>
      <if test="bookingPassengerUserPhone != null">
        booking_passenger_user_phone,
      </if>
      <if test="bookingOrderStime != null">
        booking_order_stime,
      </if>
      <if test="bookingOrderEtime != null">
        booking_order_etime,
      </if>
      <if test="channelOrderCode != null">
        channel_order_code,
      </if>
      <if test="bookingStartLongAddr != null">
        booking_start_long_addr,
      </if>
      <if test="bookingStartShortAddr != null">
        booking_start_short_addr,
      </if>
      <if test="bookingStartPoint != null">
        booking_start_point,
      </if>
      <if test="bookingEndLongAddr != null">
        booking_end_long_addr,
      </if>
      <if test="bookingEndShortAddr != null">
        booking_end_short_addr,
      </if>
      <if test="bookingEndPoint != null">
        booking_end_point,
      </if>
      <if test="startCityCode != null">
        start_city_code,
      </if>
      <if test="startCityName != null">
        start_city_name,
      </if>
      <if test="endCityCode != null">
        end_city_code,
      </if>
      <if test="endCityName != null">
        end_city_name,
      </if>
      <if test="bookingCarlevelId != null">
        booking_carlevel_id,
      </if>
      <if test="bookingCarlevelName != null">
        booking_carlevel_name,
      </if>
      <if test="orderCancellationType != null">
        order_cancellation_type,
      </if>
      <if test="assignCarCanAble != null">
        assign_car_can_able,
      </if>
      <if test="assignCarStatus != null">
        assign_car_status,
      </if>
      <if test="assignCarApplyTime != null">
        assign_car_apply_time,
      </if>
      <if test="assignCarApplyCount != null">
        assign_car_apply_count,
      </if>
      <if test="assignCarSuccessTime != null">
        assign_car_success_time,
      </if>
      <if test="assignCarFailureTime != null">
        assign_car_failure_time,
      </if>
      <if test="assignCarlevelId != null">
        assign_carlevel_id,
      </if>
      <if test="assignCarlevelName != null">
        assign_carlevel_name,
      </if>
      <if test="assignCarId != null">
        assign_car_id,
      </if>
      <if test="assignCarLicense != null">
        assign_car_license,
      </if>
      <if test="assignDriverId != null">
        assign_driver_id,
      </if>
      <if test="assignDriverName != null">
        assign_driver_name,
      </if>
      <if test="assignDriverPhone != null">
        assign_driver_phone,
      </if>
      <if test="assignDriverCityCode != null">
        assign_driver_city_code,
      </if>
      <if test="assignDriverCityName != null">
        assign_driver_city_name,
      </if>
      <if test="assignCarmodelId != null">
        assign_carmodel_id,
      </if>
      <if test="assignCarmodelName != null">
        assign_carmodel_name,
      </if>
      <if test="orderCancelNo != null">
        order_cancel_no,
      </if>
      <if test="orderCancelTime != null">
        order_cancel_time,
      </if>
      <if test="appraiseSubmited != null">
        appraise_submited,
      </if>
      <if test="appraiseSubmitTime != null">
        appraise_submit_time,
      </if>
      <if test="trafficType != null">
        traffic_type,
      </if>
      <if test="trafficNumber != null">
        traffic_number,
      </if>
      <if test="factStartLongAddr != null">
        fact_start_long_addr,
      </if>
      <if test="factStartShortAddr != null">
        fact_start_short_addr,
      </if>
      <if test="factStartPoint != null">
        fact_start_point,
      </if>
      <if test="factEndLongAddr != null">
        fact_end_long_addr,
      </if>
      <if test="factEndShortAddr != null">
        fact_end_short_addr,
      </if>
      <if test="factEndPoint != null">
        fact_end_point,
      </if>
      <if test="factStartDate != null">
        fact_start_date,
      </if>
      <if test="factEndDate != null">
        fact_end_date,
      </if>
      <if test="driverStartTime != null">
        driver_start_time,
      </if>
      <if test="driverArriveTime != null">
        driver_arrive_time,
      </if>
      <if test="getOnTime != null">
        get_on_time,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="orderDetail != null">
        order_detail,
      </if>
      <if test="mileageSource != null">
        mileage_source,
      </if>
      <if test="tripMileage != null">
        trip_mileage,
      </if>
      <if test="motorcadeId != null">
        motorcade_id,
      </if>
      <if test="motorcadeName != null">
        motorcade_name,
      </if>
      <if test="customerCompanyId != null">
        customer_company_id,
      </if>
      <if test="customerCompanyCode != null">
        customer_company_code,
      </if>
      <if test="customerCompanyName != null">
        customer_company_name,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="fixedPriceValid != null">
        fixed_price_valid,
      </if>
      <if test="fixedPrice != null">
        fixed_price,
      </if>
      <if test="structName != null">
        struct_name,
      </if>
      <if test="passengerStructId != null">
        passenger_struct_id,
      </if>
      <if test="passengerStructName != null">
        passenger_struct_name,
      </if>
      <if test="userExceptionMark != null">
        user_exception_mark,
      </if>
      <if test="mileageStatType != null">
        mileage_stat_type,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="vehiclePrivacyFlag != null">
        vehicle_privacy_flag,
      </if>
      <if test="vehicleCompanyId != null">
        vehicle_company_id,
      </if>
      <if test="vehicleCompanyName != null">
        vehicle_company_name,
      </if>
      <if test="driverCompanyId != null">
        driver_company_id,
      </if>
      <if test="driverCompanyName != null">
        driver_company_name,
      </if>
      <if test="thirdOrderNo != null">
        third_order_no,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderApplyNo != null">
        #{orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="structId != null">
        #{structId,jdbcType=INTEGER},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=TINYINT},
      </if>
      <if test="serviceCode != null">
        #{serviceCode,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=INTEGER},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerMobile != null">
        #{customerMobile,jdbcType=VARCHAR},
      </if>
      <if test="himself != null">
        #{himself,jdbcType=BIT},
      </if>
      <if test="bookingPassengerUserId != null">
        #{bookingPassengerUserId,jdbcType=BIGINT},
      </if>
      <if test="bookingPassengerUserName != null">
        #{bookingPassengerUserName,jdbcType=VARCHAR},
      </if>
      <if test="bookingPassengerUserPhone != null">
        #{bookingPassengerUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="bookingOrderStime != null">
        #{bookingOrderStime,jdbcType=TIMESTAMP},
      </if>
      <if test="bookingOrderEtime != null">
        #{bookingOrderEtime,jdbcType=TIMESTAMP},
      </if>
      <if test="channelOrderCode != null">
        #{channelOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="bookingStartLongAddr != null">
        #{bookingStartLongAddr,jdbcType=VARCHAR},
      </if>
      <if test="bookingStartShortAddr != null">
        #{bookingStartShortAddr,jdbcType=VARCHAR},
      </if>
      <if test="bookingStartPoint != null">
        #{bookingStartPoint,jdbcType=VARCHAR},
      </if>
      <if test="bookingEndLongAddr != null">
        #{bookingEndLongAddr,jdbcType=VARCHAR},
      </if>
      <if test="bookingEndShortAddr != null">
        #{bookingEndShortAddr,jdbcType=VARCHAR},
      </if>
      <if test="bookingEndPoint != null">
        #{bookingEndPoint,jdbcType=VARCHAR},
      </if>
      <if test="startCityCode != null">
        #{startCityCode,jdbcType=INTEGER},
      </if>
      <if test="startCityName != null">
        #{startCityName,jdbcType=VARCHAR},
      </if>
      <if test="endCityCode != null">
        #{endCityCode,jdbcType=INTEGER},
      </if>
      <if test="endCityName != null">
        #{endCityName,jdbcType=VARCHAR},
      </if>
      <if test="bookingCarlevelId != null">
        #{bookingCarlevelId,jdbcType=TINYINT},
      </if>
      <if test="bookingCarlevelName != null">
        #{bookingCarlevelName,jdbcType=VARCHAR},
      </if>
      <if test="orderCancellationType != null">
        #{orderCancellationType,jdbcType=TINYINT},
      </if>
      <if test="assignCarCanAble != null">
        #{assignCarCanAble,jdbcType=BIT},
      </if>
      <if test="assignCarStatus != null">
        #{assignCarStatus,jdbcType=TINYINT},
      </if>
      <if test="assignCarApplyTime != null">
        #{assignCarApplyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="assignCarApplyCount != null">
        #{assignCarApplyCount,jdbcType=SMALLINT},
      </if>
      <if test="assignCarSuccessTime != null">
        #{assignCarSuccessTime,jdbcType=TIMESTAMP},
      </if>
      <if test="assignCarFailureTime != null">
        #{assignCarFailureTime,jdbcType=TIMESTAMP},
      </if>
      <if test="assignCarlevelId != null">
        #{assignCarlevelId,jdbcType=TINYINT},
      </if>
      <if test="assignCarlevelName != null">
        #{assignCarlevelName,jdbcType=VARCHAR},
      </if>
      <if test="assignCarId != null">
        #{assignCarId,jdbcType=BIGINT},
      </if>
      <if test="assignCarLicense != null">
        #{assignCarLicense,jdbcType=VARCHAR},
      </if>
      <if test="assignDriverId != null">
        #{assignDriverId,jdbcType=BIGINT},
      </if>
      <if test="assignDriverName != null">
        #{assignDriverName,jdbcType=VARCHAR},
      </if>
      <if test="assignDriverPhone != null">
        #{assignDriverPhone,jdbcType=VARCHAR},
      </if>
      <if test="assignDriverCityCode != null">
        #{assignDriverCityCode,jdbcType=INTEGER},
      </if>
      <if test="assignDriverCityName != null">
        #{assignDriverCityName,jdbcType=VARCHAR},
      </if>
      <if test="assignCarmodelId != null">
        #{assignCarmodelId,jdbcType=INTEGER},
      </if>
      <if test="assignCarmodelName != null">
        #{assignCarmodelName,jdbcType=VARCHAR},
      </if>
      <if test="orderCancelNo != null">
        #{orderCancelNo,jdbcType=VARCHAR},
      </if>
      <if test="orderCancelTime != null">
        #{orderCancelTime,jdbcType=TIMESTAMP},
      </if>
      <if test="appraiseSubmited != null">
        #{appraiseSubmited,jdbcType=BIT},
      </if>
      <if test="appraiseSubmitTime != null">
        #{appraiseSubmitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="trafficType != null">
        #{trafficType,jdbcType=TINYINT},
      </if>
      <if test="trafficNumber != null">
        #{trafficNumber,jdbcType=VARCHAR},
      </if>
      <if test="factStartLongAddr != null">
        #{factStartLongAddr,jdbcType=VARCHAR},
      </if>
      <if test="factStartShortAddr != null">
        #{factStartShortAddr,jdbcType=VARCHAR},
      </if>
      <if test="factStartPoint != null">
        #{factStartPoint,jdbcType=VARCHAR},
      </if>
      <if test="factEndLongAddr != null">
        #{factEndLongAddr,jdbcType=VARCHAR},
      </if>
      <if test="factEndShortAddr != null">
        #{factEndShortAddr,jdbcType=VARCHAR},
      </if>
      <if test="factEndPoint != null">
        #{factEndPoint,jdbcType=VARCHAR},
      </if>
      <if test="factStartDate != null">
        #{factStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="factEndDate != null">
        #{factEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="driverStartTime != null">
        #{driverStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="driverArriveTime != null">
        #{driverArriveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="getOnTime != null">
        #{getOnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=SMALLINT},
      </if>
      <if test="orderDetail != null">
        #{orderDetail,jdbcType=VARCHAR},
      </if>
      <if test="mileageSource != null">
        #{mileageSource,jdbcType=TINYINT},
      </if>
      <if test="tripMileage != null">
        #{tripMileage,jdbcType=DECIMAL},
      </if>
      <if test="motorcadeId != null">
        #{motorcadeId,jdbcType=INTEGER},
      </if>
      <if test="motorcadeName != null">
        #{motorcadeName,jdbcType=VARCHAR},
      </if>
      <if test="customerCompanyId != null">
        #{customerCompanyId,jdbcType=INTEGER},
      </if>
      <if test="customerCompanyCode != null">
        #{customerCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="customerCompanyName != null">
        #{customerCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="fixedPriceValid != null">
        #{fixedPriceValid,jdbcType=BIT},
      </if>
      <if test="fixedPrice != null">
        #{fixedPrice,jdbcType=DECIMAL},
      </if>
      <if test="structName != null">
        #{structName,jdbcType=VARCHAR},
      </if>
      <if test="passengerStructId != null">
        #{passengerStructId,jdbcType=INTEGER},
      </if>
      <if test="passengerStructName != null">
        #{passengerStructName,jdbcType=VARCHAR},
      </if>
      <if test="userExceptionMark != null">
        #{userExceptionMark,jdbcType=VARCHAR},
      </if>
      <if test="mileageStatType != null">
        #{mileageStatType,jdbcType=TINYINT},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="vehiclePrivacyFlag != null">
        #{vehiclePrivacyFlag,jdbcType=TINYINT},
      </if>
      <if test="vehicleCompanyId != null">
        #{vehicleCompanyId,jdbcType=INTEGER},
      </if>
      <if test="vehicleCompanyName != null">
        #{vehicleCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="driverCompanyId != null">
        #{driverCompanyId,jdbcType=INTEGER},
      </if>
      <if test="driverCompanyName != null">
        #{driverCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="thirdOrderNo != null">
        #{thirdOrderNo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.OrderInfoExample" resultType="java.lang.Long">
    select count(*) from order_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update order_info
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.orderNo != null">
        order_no = #{row.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="row.orderApplyNo != null">
        order_apply_no = #{row.orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="row.companyId != null">
        company_id = #{row.companyId,jdbcType=INTEGER},
      </if>
      <if test="row.structId != null">
        struct_id = #{row.structId,jdbcType=INTEGER},
      </if>
      <if test="row.orderType != null">
        order_type = #{row.orderType,jdbcType=TINYINT},
      </if>
      <if test="row.serviceCode != null">
        service_code = #{row.serviceCode,jdbcType=VARCHAR},
      </if>
      <if test="row.customerId != null">
        customer_id = #{row.customerId,jdbcType=INTEGER},
      </if>
      <if test="row.customerName != null">
        customer_name = #{row.customerName,jdbcType=VARCHAR},
      </if>
      <if test="row.customerMobile != null">
        customer_mobile = #{row.customerMobile,jdbcType=VARCHAR},
      </if>
      <if test="row.himself != null">
        himself = #{row.himself,jdbcType=BIT},
      </if>
      <if test="row.bookingPassengerUserId != null">
        booking_passenger_user_id = #{row.bookingPassengerUserId,jdbcType=BIGINT},
      </if>
      <if test="row.bookingPassengerUserName != null">
        booking_passenger_user_name = #{row.bookingPassengerUserName,jdbcType=VARCHAR},
      </if>
      <if test="row.bookingPassengerUserPhone != null">
        booking_passenger_user_phone = #{row.bookingPassengerUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="row.bookingOrderStime != null">
        booking_order_stime = #{row.bookingOrderStime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.bookingOrderEtime != null">
        booking_order_etime = #{row.bookingOrderEtime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.channelOrderCode != null">
        channel_order_code = #{row.channelOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="row.bookingStartLongAddr != null">
        booking_start_long_addr = #{row.bookingStartLongAddr,jdbcType=VARCHAR},
      </if>
      <if test="row.bookingStartShortAddr != null">
        booking_start_short_addr = #{row.bookingStartShortAddr,jdbcType=VARCHAR},
      </if>
      <if test="row.bookingStartPoint != null">
        booking_start_point = #{row.bookingStartPoint,jdbcType=VARCHAR},
      </if>
      <if test="row.bookingEndLongAddr != null">
        booking_end_long_addr = #{row.bookingEndLongAddr,jdbcType=VARCHAR},
      </if>
      <if test="row.bookingEndShortAddr != null">
        booking_end_short_addr = #{row.bookingEndShortAddr,jdbcType=VARCHAR},
      </if>
      <if test="row.bookingEndPoint != null">
        booking_end_point = #{row.bookingEndPoint,jdbcType=VARCHAR},
      </if>
      <if test="row.startCityCode != null">
        start_city_code = #{row.startCityCode,jdbcType=INTEGER},
      </if>
      <if test="row.startCityName != null">
        start_city_name = #{row.startCityName,jdbcType=VARCHAR},
      </if>
      <if test="row.endCityCode != null">
        end_city_code = #{row.endCityCode,jdbcType=INTEGER},
      </if>
      <if test="row.endCityName != null">
        end_city_name = #{row.endCityName,jdbcType=VARCHAR},
      </if>
      <if test="row.bookingCarlevelId != null">
        booking_carlevel_id = #{row.bookingCarlevelId,jdbcType=TINYINT},
      </if>
      <if test="row.bookingCarlevelName != null">
        booking_carlevel_name = #{row.bookingCarlevelName,jdbcType=VARCHAR},
      </if>
      <if test="row.orderCancellationType != null">
        order_cancellation_type = #{row.orderCancellationType,jdbcType=TINYINT},
      </if>
      <if test="row.assignCarCanAble != null">
        assign_car_can_able = #{row.assignCarCanAble,jdbcType=BIT},
      </if>
      <if test="row.assignCarStatus != null">
        assign_car_status = #{row.assignCarStatus,jdbcType=TINYINT},
      </if>
      <if test="row.assignCarApplyTime != null">
        assign_car_apply_time = #{row.assignCarApplyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.assignCarApplyCount != null">
        assign_car_apply_count = #{row.assignCarApplyCount,jdbcType=SMALLINT},
      </if>
      <if test="row.assignCarSuccessTime != null">
        assign_car_success_time = #{row.assignCarSuccessTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.assignCarFailureTime != null">
        assign_car_failure_time = #{row.assignCarFailureTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.assignCarlevelId != null">
        assign_carlevel_id = #{row.assignCarlevelId,jdbcType=TINYINT},
      </if>
      <if test="row.assignCarlevelName != null">
        assign_carlevel_name = #{row.assignCarlevelName,jdbcType=VARCHAR},
      </if>
      <if test="row.assignCarId != null">
        assign_car_id = #{row.assignCarId,jdbcType=BIGINT},
      </if>
      <if test="row.assignCarLicense != null">
        assign_car_license = #{row.assignCarLicense,jdbcType=VARCHAR},
      </if>
      <if test="row.assignDriverId != null">
        assign_driver_id = #{row.assignDriverId,jdbcType=BIGINT},
      </if>
      <if test="row.assignDriverName != null">
        assign_driver_name = #{row.assignDriverName,jdbcType=VARCHAR},
      </if>
      <if test="row.assignDriverPhone != null">
        assign_driver_phone = #{row.assignDriverPhone,jdbcType=VARCHAR},
      </if>
      <if test="row.assignDriverCityCode != null">
        assign_driver_city_code = #{row.assignDriverCityCode,jdbcType=INTEGER},
      </if>
      <if test="row.assignDriverCityName != null">
        assign_driver_city_name = #{row.assignDriverCityName,jdbcType=VARCHAR},
      </if>
      <if test="row.assignCarmodelId != null">
        assign_carmodel_id = #{row.assignCarmodelId,jdbcType=INTEGER},
      </if>
      <if test="row.assignCarmodelName != null">
        assign_carmodel_name = #{row.assignCarmodelName,jdbcType=VARCHAR},
      </if>
      <if test="row.orderCancelNo != null">
        order_cancel_no = #{row.orderCancelNo,jdbcType=VARCHAR},
      </if>
      <if test="row.orderCancelTime != null">
        order_cancel_time = #{row.orderCancelTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.appraiseSubmited != null">
        appraise_submited = #{row.appraiseSubmited,jdbcType=BIT},
      </if>
      <if test="row.appraiseSubmitTime != null">
        appraise_submit_time = #{row.appraiseSubmitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.trafficType != null">
        traffic_type = #{row.trafficType,jdbcType=TINYINT},
      </if>
      <if test="row.trafficNumber != null">
        traffic_number = #{row.trafficNumber,jdbcType=VARCHAR},
      </if>
      <if test="row.factStartLongAddr != null">
        fact_start_long_addr = #{row.factStartLongAddr,jdbcType=VARCHAR},
      </if>
      <if test="row.factStartShortAddr != null">
        fact_start_short_addr = #{row.factStartShortAddr,jdbcType=VARCHAR},
      </if>
      <if test="row.factStartPoint != null">
        fact_start_point = #{row.factStartPoint,jdbcType=VARCHAR},
      </if>
      <if test="row.factEndLongAddr != null">
        fact_end_long_addr = #{row.factEndLongAddr,jdbcType=VARCHAR},
      </if>
      <if test="row.factEndShortAddr != null">
        fact_end_short_addr = #{row.factEndShortAddr,jdbcType=VARCHAR},
      </if>
      <if test="row.factEndPoint != null">
        fact_end_point = #{row.factEndPoint,jdbcType=VARCHAR},
      </if>
      <if test="row.factStartDate != null">
        fact_start_date = #{row.factStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="row.factEndDate != null">
        fact_end_date = #{row.factEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="row.driverStartTime != null">
        driver_start_time = #{row.driverStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.driverArriveTime != null">
        driver_arrive_time = #{row.driverArriveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.getOnTime != null">
        get_on_time = #{row.getOnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.orderStatus != null">
        order_status = #{row.orderStatus,jdbcType=SMALLINT},
      </if>
      <if test="row.orderDetail != null">
        order_detail = #{row.orderDetail,jdbcType=VARCHAR},
      </if>
      <if test="row.mileageSource != null">
        mileage_source = #{row.mileageSource,jdbcType=TINYINT},
      </if>
      <if test="row.tripMileage != null">
        trip_mileage = #{row.tripMileage,jdbcType=DECIMAL},
      </if>
      <if test="row.motorcadeId != null">
        motorcade_id = #{row.motorcadeId,jdbcType=INTEGER},
      </if>
      <if test="row.motorcadeName != null">
        motorcade_name = #{row.motorcadeName,jdbcType=VARCHAR},
      </if>
      <if test="row.customerCompanyId != null">
        customer_company_id = #{row.customerCompanyId,jdbcType=INTEGER},
      </if>
      <if test="row.customerCompanyCode != null">
        customer_company_code = #{row.customerCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="row.customerCompanyName != null">
        customer_company_name = #{row.customerCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="row.companyName != null">
        company_name = #{row.companyName,jdbcType=VARCHAR},
      </if>
      <if test="row.fixedPriceValid != null">
        fixed_price_valid = #{row.fixedPriceValid,jdbcType=BIT},
      </if>
      <if test="row.fixedPrice != null">
        fixed_price = #{row.fixedPrice,jdbcType=DECIMAL},
      </if>
      <if test="row.structName != null">
        struct_name = #{row.structName,jdbcType=VARCHAR},
      </if>
      <if test="row.passengerStructId != null">
        passenger_struct_id = #{row.passengerStructId,jdbcType=INTEGER},
      </if>
      <if test="row.passengerStructName != null">
        passenger_struct_name = #{row.passengerStructName,jdbcType=VARCHAR},
      </if>
      <if test="row.userExceptionMark != null">
        user_exception_mark = #{row.userExceptionMark,jdbcType=VARCHAR},
      </if>
      <if test="row.mileageStatType != null">
        mileage_stat_type = #{row.mileageStatType,jdbcType=TINYINT},
      </if>
      <if test="row.deleted != null">
        deleted = #{row.deleted,jdbcType=BIT},
      </if>
      <if test="row.vehiclePrivacyFlag != null">
        vehicle_privacy_flag = #{row.vehiclePrivacyFlag,jdbcType=TINYINT},
      </if>
      <if test="row.vehicleCompanyId != null">
        vehicle_company_id = #{row.vehicleCompanyId,jdbcType=INTEGER},
      </if>
      <if test="row.vehicleCompanyName != null">
        vehicle_company_name = #{row.vehicleCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="row.driverCompanyId != null">
        driver_company_id = #{row.driverCompanyId,jdbcType=INTEGER},
      </if>
      <if test="row.driverCompanyName != null">
        driver_company_name = #{row.driverCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="row.thirdOrderNo != null">
        third_order_no = #{row.thirdOrderNo,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update order_info
    set id = #{row.id,jdbcType=BIGINT},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      order_no = #{row.orderNo,jdbcType=VARCHAR},
      order_apply_no = #{row.orderApplyNo,jdbcType=VARCHAR},
      company_id = #{row.companyId,jdbcType=INTEGER},
      struct_id = #{row.structId,jdbcType=INTEGER},
      order_type = #{row.orderType,jdbcType=TINYINT},
      service_code = #{row.serviceCode,jdbcType=VARCHAR},
      customer_id = #{row.customerId,jdbcType=INTEGER},
      customer_name = #{row.customerName,jdbcType=VARCHAR},
      customer_mobile = #{row.customerMobile,jdbcType=VARCHAR},
      himself = #{row.himself,jdbcType=BIT},
      booking_passenger_user_id = #{row.bookingPassengerUserId,jdbcType=BIGINT},
      booking_passenger_user_name = #{row.bookingPassengerUserName,jdbcType=VARCHAR},
      booking_passenger_user_phone = #{row.bookingPassengerUserPhone,jdbcType=VARCHAR},
      booking_order_stime = #{row.bookingOrderStime,jdbcType=TIMESTAMP},
      booking_order_etime = #{row.bookingOrderEtime,jdbcType=TIMESTAMP},
      channel_order_code = #{row.channelOrderCode,jdbcType=VARCHAR},
      booking_start_long_addr = #{row.bookingStartLongAddr,jdbcType=VARCHAR},
      booking_start_short_addr = #{row.bookingStartShortAddr,jdbcType=VARCHAR},
      booking_start_point = #{row.bookingStartPoint,jdbcType=VARCHAR},
      booking_end_long_addr = #{row.bookingEndLongAddr,jdbcType=VARCHAR},
      booking_end_short_addr = #{row.bookingEndShortAddr,jdbcType=VARCHAR},
      booking_end_point = #{row.bookingEndPoint,jdbcType=VARCHAR},
      start_city_code = #{row.startCityCode,jdbcType=INTEGER},
      start_city_name = #{row.startCityName,jdbcType=VARCHAR},
      end_city_code = #{row.endCityCode,jdbcType=INTEGER},
      end_city_name = #{row.endCityName,jdbcType=VARCHAR},
      booking_carlevel_id = #{row.bookingCarlevelId,jdbcType=TINYINT},
      booking_carlevel_name = #{row.bookingCarlevelName,jdbcType=VARCHAR},
      order_cancellation_type = #{row.orderCancellationType,jdbcType=TINYINT},
      assign_car_can_able = #{row.assignCarCanAble,jdbcType=BIT},
      assign_car_status = #{row.assignCarStatus,jdbcType=TINYINT},
      assign_car_apply_time = #{row.assignCarApplyTime,jdbcType=TIMESTAMP},
      assign_car_apply_count = #{row.assignCarApplyCount,jdbcType=SMALLINT},
      assign_car_success_time = #{row.assignCarSuccessTime,jdbcType=TIMESTAMP},
      assign_car_failure_time = #{row.assignCarFailureTime,jdbcType=TIMESTAMP},
      assign_carlevel_id = #{row.assignCarlevelId,jdbcType=TINYINT},
      assign_carlevel_name = #{row.assignCarlevelName,jdbcType=VARCHAR},
      assign_car_id = #{row.assignCarId,jdbcType=BIGINT},
      assign_car_license = #{row.assignCarLicense,jdbcType=VARCHAR},
      assign_driver_id = #{row.assignDriverId,jdbcType=BIGINT},
      assign_driver_name = #{row.assignDriverName,jdbcType=VARCHAR},
      assign_driver_phone = #{row.assignDriverPhone,jdbcType=VARCHAR},
      assign_driver_city_code = #{row.assignDriverCityCode,jdbcType=INTEGER},
      assign_driver_city_name = #{row.assignDriverCityName,jdbcType=VARCHAR},
      assign_carmodel_id = #{row.assignCarmodelId,jdbcType=INTEGER},
      assign_carmodel_name = #{row.assignCarmodelName,jdbcType=VARCHAR},
      order_cancel_no = #{row.orderCancelNo,jdbcType=VARCHAR},
      order_cancel_time = #{row.orderCancelTime,jdbcType=TIMESTAMP},
      appraise_submited = #{row.appraiseSubmited,jdbcType=BIT},
      appraise_submit_time = #{row.appraiseSubmitTime,jdbcType=TIMESTAMP},
      traffic_type = #{row.trafficType,jdbcType=TINYINT},
      traffic_number = #{row.trafficNumber,jdbcType=VARCHAR},
      fact_start_long_addr = #{row.factStartLongAddr,jdbcType=VARCHAR},
      fact_start_short_addr = #{row.factStartShortAddr,jdbcType=VARCHAR},
      fact_start_point = #{row.factStartPoint,jdbcType=VARCHAR},
      fact_end_long_addr = #{row.factEndLongAddr,jdbcType=VARCHAR},
      fact_end_short_addr = #{row.factEndShortAddr,jdbcType=VARCHAR},
      fact_end_point = #{row.factEndPoint,jdbcType=VARCHAR},
      fact_start_date = #{row.factStartDate,jdbcType=TIMESTAMP},
      fact_end_date = #{row.factEndDate,jdbcType=TIMESTAMP},
      driver_start_time = #{row.driverStartTime,jdbcType=TIMESTAMP},
      driver_arrive_time = #{row.driverArriveTime,jdbcType=TIMESTAMP},
      get_on_time = #{row.getOnTime,jdbcType=TIMESTAMP},
      order_status = #{row.orderStatus,jdbcType=SMALLINT},
      order_detail = #{row.orderDetail,jdbcType=VARCHAR},
      mileage_source = #{row.mileageSource,jdbcType=TINYINT},
      trip_mileage = #{row.tripMileage,jdbcType=DECIMAL},
      motorcade_id = #{row.motorcadeId,jdbcType=INTEGER},
      motorcade_name = #{row.motorcadeName,jdbcType=VARCHAR},
      customer_company_id = #{row.customerCompanyId,jdbcType=INTEGER},
      customer_company_code = #{row.customerCompanyCode,jdbcType=VARCHAR},
      customer_company_name = #{row.customerCompanyName,jdbcType=VARCHAR},
      company_name = #{row.companyName,jdbcType=VARCHAR},
      fixed_price_valid = #{row.fixedPriceValid,jdbcType=BIT},
      fixed_price = #{row.fixedPrice,jdbcType=DECIMAL},
      struct_name = #{row.structName,jdbcType=VARCHAR},
      passenger_struct_id = #{row.passengerStructId,jdbcType=INTEGER},
      passenger_struct_name = #{row.passengerStructName,jdbcType=VARCHAR},
      user_exception_mark = #{row.userExceptionMark,jdbcType=VARCHAR},
      mileage_stat_type = #{row.mileageStatType,jdbcType=TINYINT},
      deleted = #{row.deleted,jdbcType=BIT},
      vehicle_privacy_flag = #{row.vehiclePrivacyFlag,jdbcType=TINYINT},
      vehicle_company_id = #{row.vehicleCompanyId,jdbcType=INTEGER},
      vehicle_company_name = #{row.vehicleCompanyName,jdbcType=VARCHAR},
      driver_company_id = #{row.driverCompanyId,jdbcType=INTEGER},
      driver_company_name = #{row.driverCompanyName,jdbcType=VARCHAR},
      third_order_no = #{row.thirdOrderNo,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.OrderInfo">
    update order_info
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderApplyNo != null">
        order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="structId != null">
        struct_id = #{structId,jdbcType=INTEGER},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=TINYINT},
      </if>
      <if test="serviceCode != null">
        service_code = #{serviceCode,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=INTEGER},
      </if>
      <if test="customerName != null">
        customer_name = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerMobile != null">
        customer_mobile = #{customerMobile,jdbcType=VARCHAR},
      </if>
      <if test="himself != null">
        himself = #{himself,jdbcType=BIT},
      </if>
      <if test="bookingPassengerUserId != null">
        booking_passenger_user_id = #{bookingPassengerUserId,jdbcType=BIGINT},
      </if>
      <if test="bookingPassengerUserName != null">
        booking_passenger_user_name = #{bookingPassengerUserName,jdbcType=VARCHAR},
      </if>
      <if test="bookingPassengerUserPhone != null">
        booking_passenger_user_phone = #{bookingPassengerUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="bookingOrderStime != null">
        booking_order_stime = #{bookingOrderStime,jdbcType=TIMESTAMP},
      </if>
      <if test="bookingOrderEtime != null">
        booking_order_etime = #{bookingOrderEtime,jdbcType=TIMESTAMP},
      </if>
      <if test="channelOrderCode != null">
        channel_order_code = #{channelOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="bookingStartLongAddr != null">
        booking_start_long_addr = #{bookingStartLongAddr,jdbcType=VARCHAR},
      </if>
      <if test="bookingStartShortAddr != null">
        booking_start_short_addr = #{bookingStartShortAddr,jdbcType=VARCHAR},
      </if>
      <if test="bookingStartPoint != null">
        booking_start_point = #{bookingStartPoint,jdbcType=VARCHAR},
      </if>
      <if test="bookingEndLongAddr != null">
        booking_end_long_addr = #{bookingEndLongAddr,jdbcType=VARCHAR},
      </if>
      <if test="bookingEndShortAddr != null">
        booking_end_short_addr = #{bookingEndShortAddr,jdbcType=VARCHAR},
      </if>
      <if test="bookingEndPoint != null">
        booking_end_point = #{bookingEndPoint,jdbcType=VARCHAR},
      </if>
      <if test="startCityCode != null">
        start_city_code = #{startCityCode,jdbcType=INTEGER},
      </if>
      <if test="startCityName != null">
        start_city_name = #{startCityName,jdbcType=VARCHAR},
      </if>
      <if test="endCityCode != null">
        end_city_code = #{endCityCode,jdbcType=INTEGER},
      </if>
      <if test="endCityName != null">
        end_city_name = #{endCityName,jdbcType=VARCHAR},
      </if>
      <if test="bookingCarlevelId != null">
        booking_carlevel_id = #{bookingCarlevelId,jdbcType=TINYINT},
      </if>
      <if test="bookingCarlevelName != null">
        booking_carlevel_name = #{bookingCarlevelName,jdbcType=VARCHAR},
      </if>
      <if test="orderCancellationType != null">
        order_cancellation_type = #{orderCancellationType,jdbcType=TINYINT},
      </if>
      <if test="assignCarCanAble != null">
        assign_car_can_able = #{assignCarCanAble,jdbcType=BIT},
      </if>
      <if test="assignCarStatus != null">
        assign_car_status = #{assignCarStatus,jdbcType=TINYINT},
      </if>
      <if test="assignCarApplyTime != null">
        assign_car_apply_time = #{assignCarApplyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="assignCarApplyCount != null">
        assign_car_apply_count = #{assignCarApplyCount,jdbcType=SMALLINT},
      </if>
      <if test="assignCarSuccessTime != null">
        assign_car_success_time = #{assignCarSuccessTime,jdbcType=TIMESTAMP},
      </if>
      <if test="assignCarFailureTime != null">
        assign_car_failure_time = #{assignCarFailureTime,jdbcType=TIMESTAMP},
      </if>
      <if test="assignCarlevelId != null">
        assign_carlevel_id = #{assignCarlevelId,jdbcType=TINYINT},
      </if>
      <if test="assignCarlevelName != null">
        assign_carlevel_name = #{assignCarlevelName,jdbcType=VARCHAR},
      </if>
      <if test="assignCarId != null">
        assign_car_id = #{assignCarId,jdbcType=BIGINT},
      </if>
      <if test="assignCarLicense != null">
        assign_car_license = #{assignCarLicense,jdbcType=VARCHAR},
      </if>
      <if test="assignDriverId != null">
        assign_driver_id = #{assignDriverId,jdbcType=BIGINT},
      </if>
      <if test="assignDriverName != null">
        assign_driver_name = #{assignDriverName,jdbcType=VARCHAR},
      </if>
      <if test="assignDriverPhone != null">
        assign_driver_phone = #{assignDriverPhone,jdbcType=VARCHAR},
      </if>
      <if test="assignDriverCityCode != null">
        assign_driver_city_code = #{assignDriverCityCode,jdbcType=INTEGER},
      </if>
      <if test="assignDriverCityName != null">
        assign_driver_city_name = #{assignDriverCityName,jdbcType=VARCHAR},
      </if>
      <if test="assignCarmodelId != null">
        assign_carmodel_id = #{assignCarmodelId,jdbcType=INTEGER},
      </if>
      <if test="assignCarmodelName != null">
        assign_carmodel_name = #{assignCarmodelName,jdbcType=VARCHAR},
      </if>
      <if test="orderCancelNo != null">
        order_cancel_no = #{orderCancelNo,jdbcType=VARCHAR},
      </if>
      <if test="orderCancelTime != null">
        order_cancel_time = #{orderCancelTime,jdbcType=TIMESTAMP},
      </if>
      <if test="appraiseSubmited != null">
        appraise_submited = #{appraiseSubmited,jdbcType=BIT},
      </if>
      <if test="appraiseSubmitTime != null">
        appraise_submit_time = #{appraiseSubmitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="trafficType != null">
        traffic_type = #{trafficType,jdbcType=TINYINT},
      </if>
      <if test="trafficNumber != null">
        traffic_number = #{trafficNumber,jdbcType=VARCHAR},
      </if>
      <if test="factStartLongAddr != null">
        fact_start_long_addr = #{factStartLongAddr,jdbcType=VARCHAR},
      </if>
      <if test="factStartShortAddr != null">
        fact_start_short_addr = #{factStartShortAddr,jdbcType=VARCHAR},
      </if>
      <if test="factStartPoint != null">
        fact_start_point = #{factStartPoint,jdbcType=VARCHAR},
      </if>
      <if test="factEndLongAddr != null">
        fact_end_long_addr = #{factEndLongAddr,jdbcType=VARCHAR},
      </if>
      <if test="factEndShortAddr != null">
        fact_end_short_addr = #{factEndShortAddr,jdbcType=VARCHAR},
      </if>
      <if test="factEndPoint != null">
        fact_end_point = #{factEndPoint,jdbcType=VARCHAR},
      </if>
      <if test="factStartDate != null">
        fact_start_date = #{factStartDate,jdbcType=TIMESTAMP},
      </if>
      <if test="factEndDate != null">
        fact_end_date = #{factEndDate,jdbcType=TIMESTAMP},
      </if>
      <if test="driverStartTime != null">
        driver_start_time = #{driverStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="driverArriveTime != null">
        driver_arrive_time = #{driverArriveTime,jdbcType=TIMESTAMP},
      </if>
      <if test="getOnTime != null">
        get_on_time = #{getOnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=SMALLINT},
      </if>
      <if test="orderDetail != null">
        order_detail = #{orderDetail,jdbcType=VARCHAR},
      </if>
      <if test="mileageSource != null">
        mileage_source = #{mileageSource,jdbcType=TINYINT},
      </if>
      <if test="tripMileage != null">
        trip_mileage = #{tripMileage,jdbcType=DECIMAL},
      </if>
      <if test="motorcadeId != null">
        motorcade_id = #{motorcadeId,jdbcType=INTEGER},
      </if>
      <if test="motorcadeName != null">
        motorcade_name = #{motorcadeName,jdbcType=VARCHAR},
      </if>
      <if test="customerCompanyId != null">
        customer_company_id = #{customerCompanyId,jdbcType=INTEGER},
      </if>
      <if test="customerCompanyCode != null">
        customer_company_code = #{customerCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="customerCompanyName != null">
        customer_company_name = #{customerCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="fixedPriceValid != null">
        fixed_price_valid = #{fixedPriceValid,jdbcType=BIT},
      </if>
      <if test="fixedPrice != null">
        fixed_price = #{fixedPrice,jdbcType=DECIMAL},
      </if>
      <if test="structName != null">
        struct_name = #{structName,jdbcType=VARCHAR},
      </if>
      <if test="passengerStructId != null">
        passenger_struct_id = #{passengerStructId,jdbcType=INTEGER},
      </if>
      <if test="passengerStructName != null">
        passenger_struct_name = #{passengerStructName,jdbcType=VARCHAR},
      </if>
      <if test="userExceptionMark != null">
        user_exception_mark = #{userExceptionMark,jdbcType=VARCHAR},
      </if>
      <if test="mileageStatType != null">
        mileage_stat_type = #{mileageStatType,jdbcType=TINYINT},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="vehiclePrivacyFlag != null">
        vehicle_privacy_flag = #{vehiclePrivacyFlag,jdbcType=TINYINT},
      </if>
      <if test="vehicleCompanyId != null">
        vehicle_company_id = #{vehicleCompanyId,jdbcType=INTEGER},
      </if>
      <if test="vehicleCompanyName != null">
        vehicle_company_name = #{vehicleCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="driverCompanyId != null">
        driver_company_id = #{driverCompanyId,jdbcType=INTEGER},
      </if>
      <if test="driverCompanyName != null">
        driver_company_name = #{driverCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="thirdOrderNo != null">
        third_order_no = #{thirdOrderNo,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.OrderInfo">
    update order_info
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      order_no = #{orderNo,jdbcType=VARCHAR},
      order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=INTEGER},
      struct_id = #{structId,jdbcType=INTEGER},
      order_type = #{orderType,jdbcType=TINYINT},
      service_code = #{serviceCode,jdbcType=VARCHAR},
      customer_id = #{customerId,jdbcType=INTEGER},
      customer_name = #{customerName,jdbcType=VARCHAR},
      customer_mobile = #{customerMobile,jdbcType=VARCHAR},
      himself = #{himself,jdbcType=BIT},
      booking_passenger_user_id = #{bookingPassengerUserId,jdbcType=BIGINT},
      booking_passenger_user_name = #{bookingPassengerUserName,jdbcType=VARCHAR},
      booking_passenger_user_phone = #{bookingPassengerUserPhone,jdbcType=VARCHAR},
      booking_order_stime = #{bookingOrderStime,jdbcType=TIMESTAMP},
      booking_order_etime = #{bookingOrderEtime,jdbcType=TIMESTAMP},
      channel_order_code = #{channelOrderCode,jdbcType=VARCHAR},
      booking_start_long_addr = #{bookingStartLongAddr,jdbcType=VARCHAR},
      booking_start_short_addr = #{bookingStartShortAddr,jdbcType=VARCHAR},
      booking_start_point = #{bookingStartPoint,jdbcType=VARCHAR},
      booking_end_long_addr = #{bookingEndLongAddr,jdbcType=VARCHAR},
      booking_end_short_addr = #{bookingEndShortAddr,jdbcType=VARCHAR},
      booking_end_point = #{bookingEndPoint,jdbcType=VARCHAR},
      start_city_code = #{startCityCode,jdbcType=INTEGER},
      start_city_name = #{startCityName,jdbcType=VARCHAR},
      end_city_code = #{endCityCode,jdbcType=INTEGER},
      end_city_name = #{endCityName,jdbcType=VARCHAR},
      booking_carlevel_id = #{bookingCarlevelId,jdbcType=TINYINT},
      booking_carlevel_name = #{bookingCarlevelName,jdbcType=VARCHAR},
      order_cancellation_type = #{orderCancellationType,jdbcType=TINYINT},
      assign_car_can_able = #{assignCarCanAble,jdbcType=BIT},
      assign_car_status = #{assignCarStatus,jdbcType=TINYINT},
      assign_car_apply_time = #{assignCarApplyTime,jdbcType=TIMESTAMP},
      assign_car_apply_count = #{assignCarApplyCount,jdbcType=SMALLINT},
      assign_car_success_time = #{assignCarSuccessTime,jdbcType=TIMESTAMP},
      assign_car_failure_time = #{assignCarFailureTime,jdbcType=TIMESTAMP},
      assign_carlevel_id = #{assignCarlevelId,jdbcType=TINYINT},
      assign_carlevel_name = #{assignCarlevelName,jdbcType=VARCHAR},
      assign_car_id = #{assignCarId,jdbcType=BIGINT},
      assign_car_license = #{assignCarLicense,jdbcType=VARCHAR},
      assign_driver_id = #{assignDriverId,jdbcType=BIGINT},
      assign_driver_name = #{assignDriverName,jdbcType=VARCHAR},
      assign_driver_phone = #{assignDriverPhone,jdbcType=VARCHAR},
      assign_driver_city_code = #{assignDriverCityCode,jdbcType=INTEGER},
      assign_driver_city_name = #{assignDriverCityName,jdbcType=VARCHAR},
      assign_carmodel_id = #{assignCarmodelId,jdbcType=INTEGER},
      assign_carmodel_name = #{assignCarmodelName,jdbcType=VARCHAR},
      order_cancel_no = #{orderCancelNo,jdbcType=VARCHAR},
      order_cancel_time = #{orderCancelTime,jdbcType=TIMESTAMP},
      appraise_submited = #{appraiseSubmited,jdbcType=BIT},
      appraise_submit_time = #{appraiseSubmitTime,jdbcType=TIMESTAMP},
      traffic_type = #{trafficType,jdbcType=TINYINT},
      traffic_number = #{trafficNumber,jdbcType=VARCHAR},
      fact_start_long_addr = #{factStartLongAddr,jdbcType=VARCHAR},
      fact_start_short_addr = #{factStartShortAddr,jdbcType=VARCHAR},
      fact_start_point = #{factStartPoint,jdbcType=VARCHAR},
      fact_end_long_addr = #{factEndLongAddr,jdbcType=VARCHAR},
      fact_end_short_addr = #{factEndShortAddr,jdbcType=VARCHAR},
      fact_end_point = #{factEndPoint,jdbcType=VARCHAR},
      fact_start_date = #{factStartDate,jdbcType=TIMESTAMP},
      fact_end_date = #{factEndDate,jdbcType=TIMESTAMP},
      driver_start_time = #{driverStartTime,jdbcType=TIMESTAMP},
      driver_arrive_time = #{driverArriveTime,jdbcType=TIMESTAMP},
      get_on_time = #{getOnTime,jdbcType=TIMESTAMP},
      order_status = #{orderStatus,jdbcType=SMALLINT},
      order_detail = #{orderDetail,jdbcType=VARCHAR},
      mileage_source = #{mileageSource,jdbcType=TINYINT},
      trip_mileage = #{tripMileage,jdbcType=DECIMAL},
      motorcade_id = #{motorcadeId,jdbcType=INTEGER},
      motorcade_name = #{motorcadeName,jdbcType=VARCHAR},
      customer_company_id = #{customerCompanyId,jdbcType=INTEGER},
      customer_company_code = #{customerCompanyCode,jdbcType=VARCHAR},
      customer_company_name = #{customerCompanyName,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      fixed_price_valid = #{fixedPriceValid,jdbcType=BIT},
      fixed_price = #{fixedPrice,jdbcType=DECIMAL},
      struct_name = #{structName,jdbcType=VARCHAR},
      passenger_struct_id = #{passengerStructId,jdbcType=INTEGER},
      passenger_struct_name = #{passengerStructName,jdbcType=VARCHAR},
      user_exception_mark = #{userExceptionMark,jdbcType=VARCHAR},
      mileage_stat_type = #{mileageStatType,jdbcType=TINYINT},
      deleted = #{deleted,jdbcType=BIT},
      vehicle_privacy_flag = #{vehiclePrivacyFlag,jdbcType=TINYINT},
      vehicle_company_id = #{vehicleCompanyId,jdbcType=INTEGER},
      vehicle_company_name = #{vehicleCompanyName,jdbcType=VARCHAR},
      driver_company_id = #{driverCompanyId,jdbcType=INTEGER},
      driver_company_name = #{driverCompanyName,jdbcType=VARCHAR},
      third_order_no = #{thirdOrderNo,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>