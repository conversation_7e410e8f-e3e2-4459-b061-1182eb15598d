<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.OrderSupplierResourceMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.OrderSupplierResource">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="relate_business_type" jdbcType="INTEGER" property="relateBusinessType" />
    <result column="relate_business_code" jdbcType="VARCHAR" property="relateBusinessCode" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="file_url" jdbcType="VARCHAR" property="fileUrl" />
    <result column="del_tag" jdbcType="INTEGER" property="delTag" />
    <result column="create_id" jdbcType="INTEGER" property="createId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, relate_business_type, relate_business_code, file_name, file_url, del_tag, create_id, 
    create_name, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from so_order_supplier_resource
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from so_order_supplier_resource
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.OrderSupplierResource">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into so_order_supplier_resource (relate_business_type, relate_business_code, 
      file_name, file_url, del_tag, 
      create_id, create_name, create_time
      )
    values (#{relateBusinessType,jdbcType=INTEGER}, #{relateBusinessCode,jdbcType=VARCHAR}, 
      #{fileName,jdbcType=VARCHAR}, #{fileUrl,jdbcType=VARCHAR}, #{delTag,jdbcType=INTEGER}, 
      #{createId,jdbcType=INTEGER}, #{createName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.OrderSupplierResource">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into so_order_supplier_resource
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="relateBusinessType != null">
        relate_business_type,
      </if>
      <if test="relateBusinessCode != null">
        relate_business_code,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="fileUrl != null">
        file_url,
      </if>
      <if test="delTag != null">
        del_tag,
      </if>
      <if test="createId != null">
        create_id,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="relateBusinessType != null">
        #{relateBusinessType,jdbcType=INTEGER},
      </if>
      <if test="relateBusinessCode != null">
        #{relateBusinessCode,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileUrl != null">
        #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="delTag != null">
        #{delTag,jdbcType=INTEGER},
      </if>
      <if test="createId != null">
        #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.OrderSupplierResource">
    update so_order_supplier_resource
    <set>
      <if test="relateBusinessType != null">
        relate_business_type = #{relateBusinessType,jdbcType=INTEGER},
      </if>
      <if test="relateBusinessCode != null">
        relate_business_code = #{relateBusinessCode,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileUrl != null">
        file_url = #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="delTag != null">
        del_tag = #{delTag,jdbcType=INTEGER},
      </if>
      <if test="createId != null">
        create_id = #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.OrderSupplierResource">
    update so_order_supplier_resource
    set relate_business_type = #{relateBusinessType,jdbcType=INTEGER},
      relate_business_code = #{relateBusinessCode,jdbcType=VARCHAR},
      file_name = #{fileName,jdbcType=VARCHAR},
      file_url = #{fileUrl,jdbcType=VARCHAR},
      del_tag = #{delTag,jdbcType=INTEGER},
      create_id = #{createId,jdbcType=INTEGER},
      create_name = #{createName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>