<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.OrderInfoAttachMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.OrderInfoAttach">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="order_apply_no" jdbcType="VARCHAR" property="orderApplyNo" />
    <result column="start_manual_mileage" jdbcType="DECIMAL" property="startManualMileage" />
    <result column="start_device_mileage" jdbcType="DECIMAL" property="startDeviceMileage" />
    <result column="start_device_date" jdbcType="TIMESTAMP" property="startDeviceDate" />
    <result column="end_manual_mileage" jdbcType="DECIMAL" property="endManualMileage" />
    <result column="end_device_mileage" jdbcType="DECIMAL" property="endDeviceMileage" />
    <result column="end_device_date" jdbcType="TIMESTAMP" property="endDeviceDate" />
    <result column="device_mileage" jdbcType="DECIMAL" property="deviceMileage" />
    <result column="baidu_Trace_mileage" jdbcType="DECIMAL" property="baiduTraceMileage" />
    <result column="vehicle_mileage" jdbcType="DECIMAL" property="vehicleMileage" />
    <result column="app_gps_mileage" jdbcType="DECIMAL" property="appGpsMileage" />
    <result column="car_gps_mileage" jdbcType="DECIMAL" property="carGpsMileage" />
    <result column="correct_type" jdbcType="TINYINT" property="correctType" />
    <result column="correct_status" jdbcType="TINYINT" property="correctStatus" />
    <result column="before_correct_mileage" jdbcType="DECIMAL" property="beforeCorrectMileage" />
  </resultMap>
  <sql id="Base_Column_List">
    id, order_no, order_apply_no, start_manual_mileage, start_device_mileage, start_device_date, 
    end_manual_mileage, end_device_mileage, end_device_date, device_mileage, baidu_Trace_mileage, 
    vehicle_mileage, app_gps_mileage, car_gps_mileage, correct_type, correct_status, 
    before_correct_mileage
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_info_attach
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from order_info_attach
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.OrderInfoAttach">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_info_attach (order_no, order_apply_no, start_manual_mileage, 
      start_device_mileage, start_device_date, 
      end_manual_mileage, end_device_mileage, end_device_date, 
      device_mileage, baidu_Trace_mileage, vehicle_mileage, 
      app_gps_mileage, car_gps_mileage, correct_type, 
      correct_status, before_correct_mileage)
    values (#{orderNo,jdbcType=VARCHAR}, #{orderApplyNo,jdbcType=VARCHAR}, #{startManualMileage,jdbcType=DECIMAL}, 
      #{startDeviceMileage,jdbcType=DECIMAL}, #{startDeviceDate,jdbcType=TIMESTAMP}, 
      #{endManualMileage,jdbcType=DECIMAL}, #{endDeviceMileage,jdbcType=DECIMAL}, #{endDeviceDate,jdbcType=TIMESTAMP}, 
      #{deviceMileage,jdbcType=DECIMAL}, #{baiduTraceMileage,jdbcType=DECIMAL}, #{vehicleMileage,jdbcType=DECIMAL}, 
      #{appGpsMileage,jdbcType=DECIMAL}, #{carGpsMileage,jdbcType=DECIMAL}, #{correctType,jdbcType=TINYINT}, 
      #{correctStatus,jdbcType=TINYINT}, #{beforeCorrectMileage,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.OrderInfoAttach">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_info_attach
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="orderApplyNo != null">
        order_apply_no,
      </if>
      <if test="startManualMileage != null">
        start_manual_mileage,
      </if>
      <if test="startDeviceMileage != null">
        start_device_mileage,
      </if>
      <if test="startDeviceDate != null">
        start_device_date,
      </if>
      <if test="endManualMileage != null">
        end_manual_mileage,
      </if>
      <if test="endDeviceMileage != null">
        end_device_mileage,
      </if>
      <if test="endDeviceDate != null">
        end_device_date,
      </if>
      <if test="deviceMileage != null">
        device_mileage,
      </if>
      <if test="baiduTraceMileage != null">
        baidu_Trace_mileage,
      </if>
      <if test="vehicleMileage != null">
        vehicle_mileage,
      </if>
      <if test="appGpsMileage != null">
        app_gps_mileage,
      </if>
      <if test="carGpsMileage != null">
        car_gps_mileage,
      </if>
      <if test="correctType != null">
        correct_type,
      </if>
      <if test="correctStatus != null">
        correct_status,
      </if>
      <if test="beforeCorrectMileage != null">
        before_correct_mileage,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderApplyNo != null">
        #{orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="startManualMileage != null">
        #{startManualMileage,jdbcType=DECIMAL},
      </if>
      <if test="startDeviceMileage != null">
        #{startDeviceMileage,jdbcType=DECIMAL},
      </if>
      <if test="startDeviceDate != null">
        #{startDeviceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endManualMileage != null">
        #{endManualMileage,jdbcType=DECIMAL},
      </if>
      <if test="endDeviceMileage != null">
        #{endDeviceMileage,jdbcType=DECIMAL},
      </if>
      <if test="endDeviceDate != null">
        #{endDeviceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="deviceMileage != null">
        #{deviceMileage,jdbcType=DECIMAL},
      </if>
      <if test="baiduTraceMileage != null">
        #{baiduTraceMileage,jdbcType=DECIMAL},
      </if>
      <if test="vehicleMileage != null">
        #{vehicleMileage,jdbcType=DECIMAL},
      </if>
      <if test="appGpsMileage != null">
        #{appGpsMileage,jdbcType=DECIMAL},
      </if>
      <if test="carGpsMileage != null">
        #{carGpsMileage,jdbcType=DECIMAL},
      </if>
      <if test="correctType != null">
        #{correctType,jdbcType=TINYINT},
      </if>
      <if test="correctStatus != null">
        #{correctStatus,jdbcType=TINYINT},
      </if>
      <if test="beforeCorrectMileage != null">
        #{beforeCorrectMileage,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.OrderInfoAttach">
    update order_info_attach
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderApplyNo != null">
        order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="startManualMileage != null">
        start_manual_mileage = #{startManualMileage,jdbcType=DECIMAL},
      </if>
      <if test="startDeviceMileage != null">
        start_device_mileage = #{startDeviceMileage,jdbcType=DECIMAL},
      </if>
      <if test="startDeviceDate != null">
        start_device_date = #{startDeviceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="endManualMileage != null">
        end_manual_mileage = #{endManualMileage,jdbcType=DECIMAL},
      </if>
      <if test="endDeviceMileage != null">
        end_device_mileage = #{endDeviceMileage,jdbcType=DECIMAL},
      </if>
      <if test="endDeviceDate != null">
        end_device_date = #{endDeviceDate,jdbcType=TIMESTAMP},
      </if>
      <if test="deviceMileage != null">
        device_mileage = #{deviceMileage,jdbcType=DECIMAL},
      </if>
      <if test="baiduTraceMileage != null">
        baidu_Trace_mileage = #{baiduTraceMileage,jdbcType=DECIMAL},
      </if>
      <if test="vehicleMileage != null">
        vehicle_mileage = #{vehicleMileage,jdbcType=DECIMAL},
      </if>
      <if test="appGpsMileage != null">
        app_gps_mileage = #{appGpsMileage,jdbcType=DECIMAL},
      </if>
      <if test="carGpsMileage != null">
        car_gps_mileage = #{carGpsMileage,jdbcType=DECIMAL},
      </if>
      <if test="correctType != null">
        correct_type = #{correctType,jdbcType=TINYINT},
      </if>
      <if test="correctStatus != null">
        correct_status = #{correctStatus,jdbcType=TINYINT},
      </if>
      <if test="beforeCorrectMileage != null">
        before_correct_mileage = #{beforeCorrectMileage,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.OrderInfoAttach">
    update order_info_attach
    set order_no = #{orderNo,jdbcType=VARCHAR},
      order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      start_manual_mileage = #{startManualMileage,jdbcType=DECIMAL},
      start_device_mileage = #{startDeviceMileage,jdbcType=DECIMAL},
      start_device_date = #{startDeviceDate,jdbcType=TIMESTAMP},
      end_manual_mileage = #{endManualMileage,jdbcType=DECIMAL},
      end_device_mileage = #{endDeviceMileage,jdbcType=DECIMAL},
      end_device_date = #{endDeviceDate,jdbcType=TIMESTAMP},
      device_mileage = #{deviceMileage,jdbcType=DECIMAL},
      baidu_Trace_mileage = #{baiduTraceMileage,jdbcType=DECIMAL},
      vehicle_mileage = #{vehicleMileage,jdbcType=DECIMAL},
      app_gps_mileage = #{appGpsMileage,jdbcType=DECIMAL},
      car_gps_mileage = #{carGpsMileage,jdbcType=DECIMAL},
      correct_type = #{correctType,jdbcType=TINYINT},
      correct_status = #{correctStatus,jdbcType=TINYINT},
      before_correct_mileage = #{beforeCorrectMileage,jdbcType=DECIMAL}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>