<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.VehicleBaseMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.VehicleBase">
    <id column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="vehicle_license" jdbcType="VARCHAR" property="vehicleLicense" />
    <result column="vehicle_vin" jdbcType="VARCHAR" property="vehicleVin" />
    <result column="vehicle_brand_id" jdbcType="INTEGER" property="vehicleBrandId" />
    <result column="vehicle_brand_code" jdbcType="VARCHAR" property="vehicleBrandCode" />
    <result column="vehicle_brand" jdbcType="VARCHAR" property="vehicleBrand" />
    <result column="vehicle_model_id" jdbcType="INTEGER" property="vehicleModelId" />
    <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode" />
    <result column="vehicle_model" jdbcType="VARCHAR" property="vehicleModel" />
    <result column="engine_num" jdbcType="VARCHAR" property="engineNum" />
    <result column="vehicle_color" jdbcType="VARCHAR" property="vehicleColor" />
    <result column="license_color" jdbcType="VARCHAR" property="licenseColor" />
    <result column="purchase_date" jdbcType="VARCHAR" property="purchaseDate" />
    <result column="register_date" jdbcType="VARCHAR" property="registerDate" />
    <result column="driving_license_url" jdbcType="VARCHAR" property="drivingLicenseUrl" />
    <result column="vehicle_type" jdbcType="TINYINT" property="vehicleType" />
    <result column="belong_city_code" jdbcType="VARCHAR" property="belongCityCode" />
    <result column="belong_city_name" jdbcType="VARCHAR" property="belongCityName" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="struct_id" jdbcType="INTEGER" property="structId" />
    <result column="struct_name" jdbcType="VARCHAR" property="structName" />
    <result column="self_owned" jdbcType="TINYINT" property="selfOwned" />
    <result column="warn_type" jdbcType="VARCHAR" property="warnType" />
    <result column="is_mrcar" jdbcType="TINYINT" property="isMrcar" />
    <result column="working_status" jdbcType="TINYINT" property="workingStatus" />
    <result column="operation_status" jdbcType="TINYINT" property="operationStatus" />
    <result column="last_inspect_date" jdbcType="VARCHAR" property="lastInspectDate" />
    <result column="vehicle_status" jdbcType="TINYINT" property="vehicleStatus" />
    <result column="own_company_id" jdbcType="INTEGER" property="ownCompanyId" />
    <result column="own_struct_id" jdbcType="INTEGER" property="ownStructId" />
    <result column="own_struct_name" jdbcType="VARCHAR" property="ownStructName" />
    <result column="own_brand_id" jdbcType="INTEGER" property="ownBrandId" />
    <result column="own_brand_code" jdbcType="VARCHAR" property="ownBrandCode" />
    <result column="own_brand_name" jdbcType="VARCHAR" property="ownBrandName" />
    <result column="own_model_id" jdbcType="INTEGER" property="ownModelId" />
    <result column="own_model_code" jdbcType="VARCHAR" property="ownModelCode" />
    <result column="own_model_name" jdbcType="VARCHAR" property="ownModelName" />
    <result column="vehicle_age" jdbcType="SMALLINT" property="vehicleAge" />
    <result column="wheelbase" jdbcType="VARCHAR" property="wheelbase" />
    <result column="vehicle_resource" jdbcType="TINYINT" property="vehicleResource" />
    <result column="creter_id" jdbcType="INTEGER" property="creterId" />
    <result column="creter_name" jdbcType="VARCHAR" property="creterName" />
    <result column="update_id" jdbcType="INTEGER" property="updateId" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="fuel_type" jdbcType="TINYINT" property="fuelType" />
    <result column="fuel_no" jdbcType="VARCHAR" property="fuelNo" />
    <result column="vehicle_output_volume" jdbcType="VARCHAR" property="vehicleOutputVolume" />
    <result column="output_volume_unit" jdbcType="CHAR" property="outputVolumeUnit" />
    <result column="engine_rate" jdbcType="VARCHAR" property="engineRate" />
    <result column="seat_count" jdbcType="TINYINT" property="seatCount" />
    <result column="maintain_mileage" jdbcType="INTEGER" property="maintainMileage" />
    <result column="maintain_month" jdbcType="TINYINT" property="maintainMonth" />
    <result column="drive_type" jdbcType="TINYINT" property="driveType" />
    <result column="vehicle_pic_url" jdbcType="VARCHAR" property="vehiclePicUrl" />
    <result column="struct_code" jdbcType="VARCHAR" property="structCode" />
    <result column="bind_wired_gps" jdbcType="TINYINT" property="bindWiredGps" />
    <result column="wired_gps_no" jdbcType="VARCHAR" property="wiredGpsNo" />
    <result column="wired_sim_no" jdbcType="VARCHAR" property="wiredSimNo" />
    <result column="wired_bind_time" jdbcType="VARCHAR" property="wiredBindTime" />
    <result column="wired_unbind_time" jdbcType="VARCHAR" property="wiredUnbindTime" />
    <result column="bind_wifi_gps" jdbcType="TINYINT" property="bindWifiGps" />
    <result column="wifi_gps_no" jdbcType="VARCHAR" property="wifiGpsNo" />
    <result column="wifi_sim_no" jdbcType="VARCHAR" property="wifiSimNo" />
    <result column="wifi_bind_time" jdbcType="VARCHAR" property="wifiBindTime" />
    <result column="wifi_unbind_time" jdbcType="VARCHAR" property="wifiUnbindTime" />
    <result column="vehicle_belonged_sys" jdbcType="VARCHAR" property="vehicleBelongedSys" />
    <result column="belong_struct_name" jdbcType="VARCHAR" property="belongStructName" />
    <result column="belong_struct_code" jdbcType="VARCHAR" property="belongStructCode" />
    <result column="operate_struct_name" jdbcType="VARCHAR" property="operateStructName" />
    <result column="operate_struct_code" jdbcType="VARCHAR" property="operateStructCode" />
    <result column="operate_buss_name" jdbcType="VARCHAR" property="operateBussName" />
    <result column="operate_buss_code" jdbcType="VARCHAR" property="operateBussCode" />
    <result column="vehicle_mileage" jdbcType="DECIMAL" property="vehicleMileage" />
    <result column="occupy_staff_id" jdbcType="INTEGER" property="occupyStaffId" />
    <result column="occupy_staff_name" jdbcType="VARCHAR" property="occupyStaffName" />
    <result column="occupy_time" jdbcType="TIMESTAMP" property="occupyTime" />
    <result column="vehicle_usage" jdbcType="VARCHAR" property="vehicleUsage" />
    <result column="vehicle_id_new_system" jdbcType="INTEGER" property="vehicleIdNewSystem" />
    <result column="vehicle_serial_no_new_system" jdbcType="VARCHAR" property="vehicleSerialNoNewSystem" />
    <result column="asset_city_code" jdbcType="VARCHAR" property="assetCityCode" />
    <result column="asset_city_name" jdbcType="VARCHAR" property="assetCityName" />
    <result column="vehicle_status_level_one" jdbcType="TINYINT" property="vehicleStatusLevelOne" />
    <result column="vehicle_status_level_two" jdbcType="TINYINT" property="vehicleStatusLevelTwo" />
    <result column="lease_license_belong_org_name" jdbcType="VARCHAR" property="leaseLicenseBelongOrgName" />
    <result column="belong_buss_code" jdbcType="VARCHAR" property="belongBussCode" />
    <result column="belong_buss_name" jdbcType="VARCHAR" property="belongBussName" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    vehicle_id, create_date, update_date, vehicle_license, vehicle_vin, vehicle_brand_id, 
    vehicle_brand_code, vehicle_brand, vehicle_model_id, vehicle_model_code, vehicle_model, 
    engine_num, vehicle_color, license_color, purchase_date, register_date, driving_license_url, 
    vehicle_type, belong_city_code, belong_city_name, company_id, struct_id, struct_name, 
    self_owned, warn_type, is_mrcar, working_status, operation_status, last_inspect_date, 
    vehicle_status, own_company_id, own_struct_id, own_struct_name, own_brand_id, own_brand_code, 
    own_brand_name, own_model_id, own_model_code, own_model_name, vehicle_age, wheelbase, 
    vehicle_resource, creter_id, creter_name, update_id, update_name, fuel_type, fuel_no, 
    vehicle_output_volume, output_volume_unit, engine_rate, seat_count, maintain_mileage, 
    maintain_month, drive_type, vehicle_pic_url, struct_code, bind_wired_gps, wired_gps_no, 
    wired_sim_no, wired_bind_time, wired_unbind_time, bind_wifi_gps, wifi_gps_no, wifi_sim_no, 
    wifi_bind_time, wifi_unbind_time, vehicle_belonged_sys, belong_struct_name, belong_struct_code, 
    operate_struct_name, operate_struct_code, operate_buss_name, operate_buss_code, vehicle_mileage, 
    occupy_staff_id, occupy_staff_name, occupy_time, vehicle_usage, vehicle_id_new_system, 
    vehicle_serial_no_new_system, asset_city_code, asset_city_name, vehicle_status_level_one, 
    vehicle_status_level_two, lease_license_belong_org_name, belong_buss_code, belong_buss_name, 
    mobile
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.VehicleBaseExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from order_vehicle_base
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_vehicle_base
    where vehicle_id = #{vehicleId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from order_vehicle_base
    where vehicle_id = #{vehicleId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.order.entity.mrcar.VehicleBaseExample">
    delete from order_vehicle_base
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.VehicleBase">
    <selectKey keyProperty="vehicleId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_vehicle_base (create_date, update_date, vehicle_license, 
      vehicle_vin, vehicle_brand_id, vehicle_brand_code, 
      vehicle_brand, vehicle_model_id, vehicle_model_code, 
      vehicle_model, engine_num, vehicle_color, 
      license_color, purchase_date, register_date, 
      driving_license_url, vehicle_type, belong_city_code, 
      belong_city_name, company_id, struct_id, 
      struct_name, self_owned, warn_type, 
      is_mrcar, working_status, operation_status, 
      last_inspect_date, vehicle_status, own_company_id, 
      own_struct_id, own_struct_name, own_brand_id, 
      own_brand_code, own_brand_name, own_model_id, 
      own_model_code, own_model_name, vehicle_age, 
      wheelbase, vehicle_resource, creter_id, 
      creter_name, update_id, update_name, 
      fuel_type, fuel_no, vehicle_output_volume, 
      output_volume_unit, engine_rate, seat_count, 
      maintain_mileage, maintain_month, drive_type, 
      vehicle_pic_url, struct_code, bind_wired_gps, 
      wired_gps_no, wired_sim_no, wired_bind_time, 
      wired_unbind_time, bind_wifi_gps, wifi_gps_no, 
      wifi_sim_no, wifi_bind_time, wifi_unbind_time, 
      vehicle_belonged_sys, belong_struct_name, belong_struct_code, 
      operate_struct_name, operate_struct_code, operate_buss_name, 
      operate_buss_code, vehicle_mileage, occupy_staff_id, 
      occupy_staff_name, occupy_time, vehicle_usage, 
      vehicle_id_new_system, vehicle_serial_no_new_system, 
      asset_city_code, asset_city_name, vehicle_status_level_one, 
      vehicle_status_level_two, lease_license_belong_org_name, 
      belong_buss_code, belong_buss_name, mobile
      )
    values (#{createDate,jdbcType=TIMESTAMP}, #{updateDate,jdbcType=TIMESTAMP}, #{vehicleLicense,jdbcType=VARCHAR}, 
      #{vehicleVin,jdbcType=VARCHAR}, #{vehicleBrandId,jdbcType=INTEGER}, #{vehicleBrandCode,jdbcType=VARCHAR}, 
      #{vehicleBrand,jdbcType=VARCHAR}, #{vehicleModelId,jdbcType=INTEGER}, #{vehicleModelCode,jdbcType=VARCHAR}, 
      #{vehicleModel,jdbcType=VARCHAR}, #{engineNum,jdbcType=VARCHAR}, #{vehicleColor,jdbcType=VARCHAR}, 
      #{licenseColor,jdbcType=VARCHAR}, #{purchaseDate,jdbcType=VARCHAR}, #{registerDate,jdbcType=VARCHAR}, 
      #{drivingLicenseUrl,jdbcType=VARCHAR}, #{vehicleType,jdbcType=TINYINT}, #{belongCityCode,jdbcType=VARCHAR}, 
      #{belongCityName,jdbcType=VARCHAR}, #{companyId,jdbcType=INTEGER}, #{structId,jdbcType=INTEGER}, 
      #{structName,jdbcType=VARCHAR}, #{selfOwned,jdbcType=TINYINT}, #{warnType,jdbcType=VARCHAR}, 
      #{isMrcar,jdbcType=TINYINT}, #{workingStatus,jdbcType=TINYINT}, #{operationStatus,jdbcType=TINYINT}, 
      #{lastInspectDate,jdbcType=VARCHAR}, #{vehicleStatus,jdbcType=TINYINT}, #{ownCompanyId,jdbcType=INTEGER}, 
      #{ownStructId,jdbcType=INTEGER}, #{ownStructName,jdbcType=VARCHAR}, #{ownBrandId,jdbcType=INTEGER}, 
      #{ownBrandCode,jdbcType=VARCHAR}, #{ownBrandName,jdbcType=VARCHAR}, #{ownModelId,jdbcType=INTEGER}, 
      #{ownModelCode,jdbcType=VARCHAR}, #{ownModelName,jdbcType=VARCHAR}, #{vehicleAge,jdbcType=SMALLINT}, 
      #{wheelbase,jdbcType=VARCHAR}, #{vehicleResource,jdbcType=TINYINT}, #{creterId,jdbcType=INTEGER}, 
      #{creterName,jdbcType=VARCHAR}, #{updateId,jdbcType=INTEGER}, #{updateName,jdbcType=VARCHAR}, 
      #{fuelType,jdbcType=TINYINT}, #{fuelNo,jdbcType=VARCHAR}, #{vehicleOutputVolume,jdbcType=VARCHAR}, 
      #{outputVolumeUnit,jdbcType=CHAR}, #{engineRate,jdbcType=VARCHAR}, #{seatCount,jdbcType=TINYINT}, 
      #{maintainMileage,jdbcType=INTEGER}, #{maintainMonth,jdbcType=TINYINT}, #{driveType,jdbcType=TINYINT}, 
      #{vehiclePicUrl,jdbcType=VARCHAR}, #{structCode,jdbcType=VARCHAR}, #{bindWiredGps,jdbcType=TINYINT}, 
      #{wiredGpsNo,jdbcType=VARCHAR}, #{wiredSimNo,jdbcType=VARCHAR}, #{wiredBindTime,jdbcType=VARCHAR}, 
      #{wiredUnbindTime,jdbcType=VARCHAR}, #{bindWifiGps,jdbcType=TINYINT}, #{wifiGpsNo,jdbcType=VARCHAR}, 
      #{wifiSimNo,jdbcType=VARCHAR}, #{wifiBindTime,jdbcType=VARCHAR}, #{wifiUnbindTime,jdbcType=VARCHAR}, 
      #{vehicleBelongedSys,jdbcType=VARCHAR}, #{belongStructName,jdbcType=VARCHAR}, #{belongStructCode,jdbcType=VARCHAR}, 
      #{operateStructName,jdbcType=VARCHAR}, #{operateStructCode,jdbcType=VARCHAR}, #{operateBussName,jdbcType=VARCHAR}, 
      #{operateBussCode,jdbcType=VARCHAR}, #{vehicleMileage,jdbcType=DECIMAL}, #{occupyStaffId,jdbcType=INTEGER}, 
      #{occupyStaffName,jdbcType=VARCHAR}, #{occupyTime,jdbcType=TIMESTAMP}, #{vehicleUsage,jdbcType=VARCHAR}, 
      #{vehicleIdNewSystem,jdbcType=INTEGER}, #{vehicleSerialNoNewSystem,jdbcType=VARCHAR}, 
      #{assetCityCode,jdbcType=VARCHAR}, #{assetCityName,jdbcType=VARCHAR}, #{vehicleStatusLevelOne,jdbcType=TINYINT}, 
      #{vehicleStatusLevelTwo,jdbcType=TINYINT}, #{leaseLicenseBelongOrgName,jdbcType=VARCHAR}, 
      #{belongBussCode,jdbcType=VARCHAR}, #{belongBussName,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.VehicleBase">
    <selectKey keyProperty="vehicleId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_vehicle_base
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createDate != null">
        create_date,
      </if>
      <if test="updateDate != null">
        update_date,
      </if>
      <if test="vehicleLicense != null">
        vehicle_license,
      </if>
      <if test="vehicleVin != null">
        vehicle_vin,
      </if>
      <if test="vehicleBrandId != null">
        vehicle_brand_id,
      </if>
      <if test="vehicleBrandCode != null">
        vehicle_brand_code,
      </if>
      <if test="vehicleBrand != null">
        vehicle_brand,
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id,
      </if>
      <if test="vehicleModelCode != null">
        vehicle_model_code,
      </if>
      <if test="vehicleModel != null">
        vehicle_model,
      </if>
      <if test="engineNum != null">
        engine_num,
      </if>
      <if test="vehicleColor != null">
        vehicle_color,
      </if>
      <if test="licenseColor != null">
        license_color,
      </if>
      <if test="purchaseDate != null">
        purchase_date,
      </if>
      <if test="registerDate != null">
        register_date,
      </if>
      <if test="drivingLicenseUrl != null">
        driving_license_url,
      </if>
      <if test="vehicleType != null">
        vehicle_type,
      </if>
      <if test="belongCityCode != null">
        belong_city_code,
      </if>
      <if test="belongCityName != null">
        belong_city_name,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="structId != null">
        struct_id,
      </if>
      <if test="structName != null">
        struct_name,
      </if>
      <if test="selfOwned != null">
        self_owned,
      </if>
      <if test="warnType != null">
        warn_type,
      </if>
      <if test="isMrcar != null">
        is_mrcar,
      </if>
      <if test="workingStatus != null">
        working_status,
      </if>
      <if test="operationStatus != null">
        operation_status,
      </if>
      <if test="lastInspectDate != null">
        last_inspect_date,
      </if>
      <if test="vehicleStatus != null">
        vehicle_status,
      </if>
      <if test="ownCompanyId != null">
        own_company_id,
      </if>
      <if test="ownStructId != null">
        own_struct_id,
      </if>
      <if test="ownStructName != null">
        own_struct_name,
      </if>
      <if test="ownBrandId != null">
        own_brand_id,
      </if>
      <if test="ownBrandCode != null">
        own_brand_code,
      </if>
      <if test="ownBrandName != null">
        own_brand_name,
      </if>
      <if test="ownModelId != null">
        own_model_id,
      </if>
      <if test="ownModelCode != null">
        own_model_code,
      </if>
      <if test="ownModelName != null">
        own_model_name,
      </if>
      <if test="vehicleAge != null">
        vehicle_age,
      </if>
      <if test="wheelbase != null">
        wheelbase,
      </if>
      <if test="vehicleResource != null">
        vehicle_resource,
      </if>
      <if test="creterId != null">
        creter_id,
      </if>
      <if test="creterName != null">
        creter_name,
      </if>
      <if test="updateId != null">
        update_id,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="fuelType != null">
        fuel_type,
      </if>
      <if test="fuelNo != null">
        fuel_no,
      </if>
      <if test="vehicleOutputVolume != null">
        vehicle_output_volume,
      </if>
      <if test="outputVolumeUnit != null">
        output_volume_unit,
      </if>
      <if test="engineRate != null">
        engine_rate,
      </if>
      <if test="seatCount != null">
        seat_count,
      </if>
      <if test="maintainMileage != null">
        maintain_mileage,
      </if>
      <if test="maintainMonth != null">
        maintain_month,
      </if>
      <if test="driveType != null">
        drive_type,
      </if>
      <if test="vehiclePicUrl != null">
        vehicle_pic_url,
      </if>
      <if test="structCode != null">
        struct_code,
      </if>
      <if test="bindWiredGps != null">
        bind_wired_gps,
      </if>
      <if test="wiredGpsNo != null">
        wired_gps_no,
      </if>
      <if test="wiredSimNo != null">
        wired_sim_no,
      </if>
      <if test="wiredBindTime != null">
        wired_bind_time,
      </if>
      <if test="wiredUnbindTime != null">
        wired_unbind_time,
      </if>
      <if test="bindWifiGps != null">
        bind_wifi_gps,
      </if>
      <if test="wifiGpsNo != null">
        wifi_gps_no,
      </if>
      <if test="wifiSimNo != null">
        wifi_sim_no,
      </if>
      <if test="wifiBindTime != null">
        wifi_bind_time,
      </if>
      <if test="wifiUnbindTime != null">
        wifi_unbind_time,
      </if>
      <if test="vehicleBelongedSys != null">
        vehicle_belonged_sys,
      </if>
      <if test="belongStructName != null">
        belong_struct_name,
      </if>
      <if test="belongStructCode != null">
        belong_struct_code,
      </if>
      <if test="operateStructName != null">
        operate_struct_name,
      </if>
      <if test="operateStructCode != null">
        operate_struct_code,
      </if>
      <if test="operateBussName != null">
        operate_buss_name,
      </if>
      <if test="operateBussCode != null">
        operate_buss_code,
      </if>
      <if test="vehicleMileage != null">
        vehicle_mileage,
      </if>
      <if test="occupyStaffId != null">
        occupy_staff_id,
      </if>
      <if test="occupyStaffName != null">
        occupy_staff_name,
      </if>
      <if test="occupyTime != null">
        occupy_time,
      </if>
      <if test="vehicleUsage != null">
        vehicle_usage,
      </if>
      <if test="vehicleIdNewSystem != null">
        vehicle_id_new_system,
      </if>
      <if test="vehicleSerialNoNewSystem != null">
        vehicle_serial_no_new_system,
      </if>
      <if test="assetCityCode != null">
        asset_city_code,
      </if>
      <if test="assetCityName != null">
        asset_city_name,
      </if>
      <if test="vehicleStatusLevelOne != null">
        vehicle_status_level_one,
      </if>
      <if test="vehicleStatusLevelTwo != null">
        vehicle_status_level_two,
      </if>
      <if test="leaseLicenseBelongOrgName != null">
        lease_license_belong_org_name,
      </if>
      <if test="belongBussCode != null">
        belong_buss_code,
      </if>
      <if test="belongBussName != null">
        belong_buss_name,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDate != null">
        #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="vehicleLicense != null">
        #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="vehicleVin != null">
        #{vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBrandId != null">
        #{vehicleBrandId,jdbcType=INTEGER},
      </if>
      <if test="vehicleBrandCode != null">
        #{vehicleBrandCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBrand != null">
        #{vehicleBrand,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelId != null">
        #{vehicleModelId,jdbcType=INTEGER},
      </if>
      <if test="vehicleModelCode != null">
        #{vehicleModelCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModel != null">
        #{vehicleModel,jdbcType=VARCHAR},
      </if>
      <if test="engineNum != null">
        #{engineNum,jdbcType=VARCHAR},
      </if>
      <if test="vehicleColor != null">
        #{vehicleColor,jdbcType=VARCHAR},
      </if>
      <if test="licenseColor != null">
        #{licenseColor,jdbcType=VARCHAR},
      </if>
      <if test="purchaseDate != null">
        #{purchaseDate,jdbcType=VARCHAR},
      </if>
      <if test="registerDate != null">
        #{registerDate,jdbcType=VARCHAR},
      </if>
      <if test="drivingLicenseUrl != null">
        #{drivingLicenseUrl,jdbcType=VARCHAR},
      </if>
      <if test="vehicleType != null">
        #{vehicleType,jdbcType=TINYINT},
      </if>
      <if test="belongCityCode != null">
        #{belongCityCode,jdbcType=VARCHAR},
      </if>
      <if test="belongCityName != null">
        #{belongCityName,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="structId != null">
        #{structId,jdbcType=INTEGER},
      </if>
      <if test="structName != null">
        #{structName,jdbcType=VARCHAR},
      </if>
      <if test="selfOwned != null">
        #{selfOwned,jdbcType=TINYINT},
      </if>
      <if test="warnType != null">
        #{warnType,jdbcType=VARCHAR},
      </if>
      <if test="isMrcar != null">
        #{isMrcar,jdbcType=TINYINT},
      </if>
      <if test="workingStatus != null">
        #{workingStatus,jdbcType=TINYINT},
      </if>
      <if test="operationStatus != null">
        #{operationStatus,jdbcType=TINYINT},
      </if>
      <if test="lastInspectDate != null">
        #{lastInspectDate,jdbcType=VARCHAR},
      </if>
      <if test="vehicleStatus != null">
        #{vehicleStatus,jdbcType=TINYINT},
      </if>
      <if test="ownCompanyId != null">
        #{ownCompanyId,jdbcType=INTEGER},
      </if>
      <if test="ownStructId != null">
        #{ownStructId,jdbcType=INTEGER},
      </if>
      <if test="ownStructName != null">
        #{ownStructName,jdbcType=VARCHAR},
      </if>
      <if test="ownBrandId != null">
        #{ownBrandId,jdbcType=INTEGER},
      </if>
      <if test="ownBrandCode != null">
        #{ownBrandCode,jdbcType=VARCHAR},
      </if>
      <if test="ownBrandName != null">
        #{ownBrandName,jdbcType=VARCHAR},
      </if>
      <if test="ownModelId != null">
        #{ownModelId,jdbcType=INTEGER},
      </if>
      <if test="ownModelCode != null">
        #{ownModelCode,jdbcType=VARCHAR},
      </if>
      <if test="ownModelName != null">
        #{ownModelName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleAge != null">
        #{vehicleAge,jdbcType=SMALLINT},
      </if>
      <if test="wheelbase != null">
        #{wheelbase,jdbcType=VARCHAR},
      </if>
      <if test="vehicleResource != null">
        #{vehicleResource,jdbcType=TINYINT},
      </if>
      <if test="creterId != null">
        #{creterId,jdbcType=INTEGER},
      </if>
      <if test="creterName != null">
        #{creterName,jdbcType=VARCHAR},
      </if>
      <if test="updateId != null">
        #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="fuelType != null">
        #{fuelType,jdbcType=TINYINT},
      </if>
      <if test="fuelNo != null">
        #{fuelNo,jdbcType=VARCHAR},
      </if>
      <if test="vehicleOutputVolume != null">
        #{vehicleOutputVolume,jdbcType=VARCHAR},
      </if>
      <if test="outputVolumeUnit != null">
        #{outputVolumeUnit,jdbcType=CHAR},
      </if>
      <if test="engineRate != null">
        #{engineRate,jdbcType=VARCHAR},
      </if>
      <if test="seatCount != null">
        #{seatCount,jdbcType=TINYINT},
      </if>
      <if test="maintainMileage != null">
        #{maintainMileage,jdbcType=INTEGER},
      </if>
      <if test="maintainMonth != null">
        #{maintainMonth,jdbcType=TINYINT},
      </if>
      <if test="driveType != null">
        #{driveType,jdbcType=TINYINT},
      </if>
      <if test="vehiclePicUrl != null">
        #{vehiclePicUrl,jdbcType=VARCHAR},
      </if>
      <if test="structCode != null">
        #{structCode,jdbcType=VARCHAR},
      </if>
      <if test="bindWiredGps != null">
        #{bindWiredGps,jdbcType=TINYINT},
      </if>
      <if test="wiredGpsNo != null">
        #{wiredGpsNo,jdbcType=VARCHAR},
      </if>
      <if test="wiredSimNo != null">
        #{wiredSimNo,jdbcType=VARCHAR},
      </if>
      <if test="wiredBindTime != null">
        #{wiredBindTime,jdbcType=VARCHAR},
      </if>
      <if test="wiredUnbindTime != null">
        #{wiredUnbindTime,jdbcType=VARCHAR},
      </if>
      <if test="bindWifiGps != null">
        #{bindWifiGps,jdbcType=TINYINT},
      </if>
      <if test="wifiGpsNo != null">
        #{wifiGpsNo,jdbcType=VARCHAR},
      </if>
      <if test="wifiSimNo != null">
        #{wifiSimNo,jdbcType=VARCHAR},
      </if>
      <if test="wifiBindTime != null">
        #{wifiBindTime,jdbcType=VARCHAR},
      </if>
      <if test="wifiUnbindTime != null">
        #{wifiUnbindTime,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBelongedSys != null">
        #{vehicleBelongedSys,jdbcType=VARCHAR},
      </if>
      <if test="belongStructName != null">
        #{belongStructName,jdbcType=VARCHAR},
      </if>
      <if test="belongStructCode != null">
        #{belongStructCode,jdbcType=VARCHAR},
      </if>
      <if test="operateStructName != null">
        #{operateStructName,jdbcType=VARCHAR},
      </if>
      <if test="operateStructCode != null">
        #{operateStructCode,jdbcType=VARCHAR},
      </if>
      <if test="operateBussName != null">
        #{operateBussName,jdbcType=VARCHAR},
      </if>
      <if test="operateBussCode != null">
        #{operateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleMileage != null">
        #{vehicleMileage,jdbcType=DECIMAL},
      </if>
      <if test="occupyStaffId != null">
        #{occupyStaffId,jdbcType=INTEGER},
      </if>
      <if test="occupyStaffName != null">
        #{occupyStaffName,jdbcType=VARCHAR},
      </if>
      <if test="occupyTime != null">
        #{occupyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="vehicleUsage != null">
        #{vehicleUsage,jdbcType=VARCHAR},
      </if>
      <if test="vehicleIdNewSystem != null">
        #{vehicleIdNewSystem,jdbcType=INTEGER},
      </if>
      <if test="vehicleSerialNoNewSystem != null">
        #{vehicleSerialNoNewSystem,jdbcType=VARCHAR},
      </if>
      <if test="assetCityCode != null">
        #{assetCityCode,jdbcType=VARCHAR},
      </if>
      <if test="assetCityName != null">
        #{assetCityName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleStatusLevelOne != null">
        #{vehicleStatusLevelOne,jdbcType=TINYINT},
      </if>
      <if test="vehicleStatusLevelTwo != null">
        #{vehicleStatusLevelTwo,jdbcType=TINYINT},
      </if>
      <if test="leaseLicenseBelongOrgName != null">
        #{leaseLicenseBelongOrgName,jdbcType=VARCHAR},
      </if>
      <if test="belongBussCode != null">
        #{belongBussCode,jdbcType=VARCHAR},
      </if>
      <if test="belongBussName != null">
        #{belongBussName,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.VehicleBaseExample" resultType="java.lang.Long">
    select count(*) from order_vehicle_base
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update order_vehicle_base
    <set>
      <if test="row.vehicleId != null">
        vehicle_id = #{row.vehicleId,jdbcType=BIGINT},
      </if>
      <if test="row.createDate != null">
        create_date = #{row.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateDate != null">
        update_date = #{row.updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="row.vehicleLicense != null">
        vehicle_license = #{row.vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleVin != null">
        vehicle_vin = #{row.vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleBrandId != null">
        vehicle_brand_id = #{row.vehicleBrandId,jdbcType=INTEGER},
      </if>
      <if test="row.vehicleBrandCode != null">
        vehicle_brand_code = #{row.vehicleBrandCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleBrand != null">
        vehicle_brand = #{row.vehicleBrand,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleModelId != null">
        vehicle_model_id = #{row.vehicleModelId,jdbcType=INTEGER},
      </if>
      <if test="row.vehicleModelCode != null">
        vehicle_model_code = #{row.vehicleModelCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleModel != null">
        vehicle_model = #{row.vehicleModel,jdbcType=VARCHAR},
      </if>
      <if test="row.engineNum != null">
        engine_num = #{row.engineNum,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleColor != null">
        vehicle_color = #{row.vehicleColor,jdbcType=VARCHAR},
      </if>
      <if test="row.licenseColor != null">
        license_color = #{row.licenseColor,jdbcType=VARCHAR},
      </if>
      <if test="row.purchaseDate != null">
        purchase_date = #{row.purchaseDate,jdbcType=VARCHAR},
      </if>
      <if test="row.registerDate != null">
        register_date = #{row.registerDate,jdbcType=VARCHAR},
      </if>
      <if test="row.drivingLicenseUrl != null">
        driving_license_url = #{row.drivingLicenseUrl,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleType != null">
        vehicle_type = #{row.vehicleType,jdbcType=TINYINT},
      </if>
      <if test="row.belongCityCode != null">
        belong_city_code = #{row.belongCityCode,jdbcType=VARCHAR},
      </if>
      <if test="row.belongCityName != null">
        belong_city_name = #{row.belongCityName,jdbcType=VARCHAR},
      </if>
      <if test="row.companyId != null">
        company_id = #{row.companyId,jdbcType=INTEGER},
      </if>
      <if test="row.structId != null">
        struct_id = #{row.structId,jdbcType=INTEGER},
      </if>
      <if test="row.structName != null">
        struct_name = #{row.structName,jdbcType=VARCHAR},
      </if>
      <if test="row.selfOwned != null">
        self_owned = #{row.selfOwned,jdbcType=TINYINT},
      </if>
      <if test="row.warnType != null">
        warn_type = #{row.warnType,jdbcType=VARCHAR},
      </if>
      <if test="row.isMrcar != null">
        is_mrcar = #{row.isMrcar,jdbcType=TINYINT},
      </if>
      <if test="row.workingStatus != null">
        working_status = #{row.workingStatus,jdbcType=TINYINT},
      </if>
      <if test="row.operationStatus != null">
        operation_status = #{row.operationStatus,jdbcType=TINYINT},
      </if>
      <if test="row.lastInspectDate != null">
        last_inspect_date = #{row.lastInspectDate,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleStatus != null">
        vehicle_status = #{row.vehicleStatus,jdbcType=TINYINT},
      </if>
      <if test="row.ownCompanyId != null">
        own_company_id = #{row.ownCompanyId,jdbcType=INTEGER},
      </if>
      <if test="row.ownStructId != null">
        own_struct_id = #{row.ownStructId,jdbcType=INTEGER},
      </if>
      <if test="row.ownStructName != null">
        own_struct_name = #{row.ownStructName,jdbcType=VARCHAR},
      </if>
      <if test="row.ownBrandId != null">
        own_brand_id = #{row.ownBrandId,jdbcType=INTEGER},
      </if>
      <if test="row.ownBrandCode != null">
        own_brand_code = #{row.ownBrandCode,jdbcType=VARCHAR},
      </if>
      <if test="row.ownBrandName != null">
        own_brand_name = #{row.ownBrandName,jdbcType=VARCHAR},
      </if>
      <if test="row.ownModelId != null">
        own_model_id = #{row.ownModelId,jdbcType=INTEGER},
      </if>
      <if test="row.ownModelCode != null">
        own_model_code = #{row.ownModelCode,jdbcType=VARCHAR},
      </if>
      <if test="row.ownModelName != null">
        own_model_name = #{row.ownModelName,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleAge != null">
        vehicle_age = #{row.vehicleAge,jdbcType=SMALLINT},
      </if>
      <if test="row.wheelbase != null">
        wheelbase = #{row.wheelbase,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleResource != null">
        vehicle_resource = #{row.vehicleResource,jdbcType=TINYINT},
      </if>
      <if test="row.creterId != null">
        creter_id = #{row.creterId,jdbcType=INTEGER},
      </if>
      <if test="row.creterName != null">
        creter_name = #{row.creterName,jdbcType=VARCHAR},
      </if>
      <if test="row.updateId != null">
        update_id = #{row.updateId,jdbcType=INTEGER},
      </if>
      <if test="row.updateName != null">
        update_name = #{row.updateName,jdbcType=VARCHAR},
      </if>
      <if test="row.fuelType != null">
        fuel_type = #{row.fuelType,jdbcType=TINYINT},
      </if>
      <if test="row.fuelNo != null">
        fuel_no = #{row.fuelNo,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleOutputVolume != null">
        vehicle_output_volume = #{row.vehicleOutputVolume,jdbcType=VARCHAR},
      </if>
      <if test="row.outputVolumeUnit != null">
        output_volume_unit = #{row.outputVolumeUnit,jdbcType=CHAR},
      </if>
      <if test="row.engineRate != null">
        engine_rate = #{row.engineRate,jdbcType=VARCHAR},
      </if>
      <if test="row.seatCount != null">
        seat_count = #{row.seatCount,jdbcType=TINYINT},
      </if>
      <if test="row.maintainMileage != null">
        maintain_mileage = #{row.maintainMileage,jdbcType=INTEGER},
      </if>
      <if test="row.maintainMonth != null">
        maintain_month = #{row.maintainMonth,jdbcType=TINYINT},
      </if>
      <if test="row.driveType != null">
        drive_type = #{row.driveType,jdbcType=TINYINT},
      </if>
      <if test="row.vehiclePicUrl != null">
        vehicle_pic_url = #{row.vehiclePicUrl,jdbcType=VARCHAR},
      </if>
      <if test="row.structCode != null">
        struct_code = #{row.structCode,jdbcType=VARCHAR},
      </if>
      <if test="row.bindWiredGps != null">
        bind_wired_gps = #{row.bindWiredGps,jdbcType=TINYINT},
      </if>
      <if test="row.wiredGpsNo != null">
        wired_gps_no = #{row.wiredGpsNo,jdbcType=VARCHAR},
      </if>
      <if test="row.wiredSimNo != null">
        wired_sim_no = #{row.wiredSimNo,jdbcType=VARCHAR},
      </if>
      <if test="row.wiredBindTime != null">
        wired_bind_time = #{row.wiredBindTime,jdbcType=VARCHAR},
      </if>
      <if test="row.wiredUnbindTime != null">
        wired_unbind_time = #{row.wiredUnbindTime,jdbcType=VARCHAR},
      </if>
      <if test="row.bindWifiGps != null">
        bind_wifi_gps = #{row.bindWifiGps,jdbcType=TINYINT},
      </if>
      <if test="row.wifiGpsNo != null">
        wifi_gps_no = #{row.wifiGpsNo,jdbcType=VARCHAR},
      </if>
      <if test="row.wifiSimNo != null">
        wifi_sim_no = #{row.wifiSimNo,jdbcType=VARCHAR},
      </if>
      <if test="row.wifiBindTime != null">
        wifi_bind_time = #{row.wifiBindTime,jdbcType=VARCHAR},
      </if>
      <if test="row.wifiUnbindTime != null">
        wifi_unbind_time = #{row.wifiUnbindTime,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleBelongedSys != null">
        vehicle_belonged_sys = #{row.vehicleBelongedSys,jdbcType=VARCHAR},
      </if>
      <if test="row.belongStructName != null">
        belong_struct_name = #{row.belongStructName,jdbcType=VARCHAR},
      </if>
      <if test="row.belongStructCode != null">
        belong_struct_code = #{row.belongStructCode,jdbcType=VARCHAR},
      </if>
      <if test="row.operateStructName != null">
        operate_struct_name = #{row.operateStructName,jdbcType=VARCHAR},
      </if>
      <if test="row.operateStructCode != null">
        operate_struct_code = #{row.operateStructCode,jdbcType=VARCHAR},
      </if>
      <if test="row.operateBussName != null">
        operate_buss_name = #{row.operateBussName,jdbcType=VARCHAR},
      </if>
      <if test="row.operateBussCode != null">
        operate_buss_code = #{row.operateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleMileage != null">
        vehicle_mileage = #{row.vehicleMileage,jdbcType=DECIMAL},
      </if>
      <if test="row.occupyStaffId != null">
        occupy_staff_id = #{row.occupyStaffId,jdbcType=INTEGER},
      </if>
      <if test="row.occupyStaffName != null">
        occupy_staff_name = #{row.occupyStaffName,jdbcType=VARCHAR},
      </if>
      <if test="row.occupyTime != null">
        occupy_time = #{row.occupyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.vehicleUsage != null">
        vehicle_usage = #{row.vehicleUsage,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleIdNewSystem != null">
        vehicle_id_new_system = #{row.vehicleIdNewSystem,jdbcType=INTEGER},
      </if>
      <if test="row.vehicleSerialNoNewSystem != null">
        vehicle_serial_no_new_system = #{row.vehicleSerialNoNewSystem,jdbcType=VARCHAR},
      </if>
      <if test="row.assetCityCode != null">
        asset_city_code = #{row.assetCityCode,jdbcType=VARCHAR},
      </if>
      <if test="row.assetCityName != null">
        asset_city_name = #{row.assetCityName,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleStatusLevelOne != null">
        vehicle_status_level_one = #{row.vehicleStatusLevelOne,jdbcType=TINYINT},
      </if>
      <if test="row.vehicleStatusLevelTwo != null">
        vehicle_status_level_two = #{row.vehicleStatusLevelTwo,jdbcType=TINYINT},
      </if>
      <if test="row.leaseLicenseBelongOrgName != null">
        lease_license_belong_org_name = #{row.leaseLicenseBelongOrgName,jdbcType=VARCHAR},
      </if>
      <if test="row.belongBussCode != null">
        belong_buss_code = #{row.belongBussCode,jdbcType=VARCHAR},
      </if>
      <if test="row.belongBussName != null">
        belong_buss_name = #{row.belongBussName,jdbcType=VARCHAR},
      </if>
      <if test="row.mobile != null">
        mobile = #{row.mobile,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update order_vehicle_base
    set vehicle_id = #{row.vehicleId,jdbcType=BIGINT},
      create_date = #{row.createDate,jdbcType=TIMESTAMP},
      update_date = #{row.updateDate,jdbcType=TIMESTAMP},
      vehicle_license = #{row.vehicleLicense,jdbcType=VARCHAR},
      vehicle_vin = #{row.vehicleVin,jdbcType=VARCHAR},
      vehicle_brand_id = #{row.vehicleBrandId,jdbcType=INTEGER},
      vehicle_brand_code = #{row.vehicleBrandCode,jdbcType=VARCHAR},
      vehicle_brand = #{row.vehicleBrand,jdbcType=VARCHAR},
      vehicle_model_id = #{row.vehicleModelId,jdbcType=INTEGER},
      vehicle_model_code = #{row.vehicleModelCode,jdbcType=VARCHAR},
      vehicle_model = #{row.vehicleModel,jdbcType=VARCHAR},
      engine_num = #{row.engineNum,jdbcType=VARCHAR},
      vehicle_color = #{row.vehicleColor,jdbcType=VARCHAR},
      license_color = #{row.licenseColor,jdbcType=VARCHAR},
      purchase_date = #{row.purchaseDate,jdbcType=VARCHAR},
      register_date = #{row.registerDate,jdbcType=VARCHAR},
      driving_license_url = #{row.drivingLicenseUrl,jdbcType=VARCHAR},
      vehicle_type = #{row.vehicleType,jdbcType=TINYINT},
      belong_city_code = #{row.belongCityCode,jdbcType=VARCHAR},
      belong_city_name = #{row.belongCityName,jdbcType=VARCHAR},
      company_id = #{row.companyId,jdbcType=INTEGER},
      struct_id = #{row.structId,jdbcType=INTEGER},
      struct_name = #{row.structName,jdbcType=VARCHAR},
      self_owned = #{row.selfOwned,jdbcType=TINYINT},
      warn_type = #{row.warnType,jdbcType=VARCHAR},
      is_mrcar = #{row.isMrcar,jdbcType=TINYINT},
      working_status = #{row.workingStatus,jdbcType=TINYINT},
      operation_status = #{row.operationStatus,jdbcType=TINYINT},
      last_inspect_date = #{row.lastInspectDate,jdbcType=VARCHAR},
      vehicle_status = #{row.vehicleStatus,jdbcType=TINYINT},
      own_company_id = #{row.ownCompanyId,jdbcType=INTEGER},
      own_struct_id = #{row.ownStructId,jdbcType=INTEGER},
      own_struct_name = #{row.ownStructName,jdbcType=VARCHAR},
      own_brand_id = #{row.ownBrandId,jdbcType=INTEGER},
      own_brand_code = #{row.ownBrandCode,jdbcType=VARCHAR},
      own_brand_name = #{row.ownBrandName,jdbcType=VARCHAR},
      own_model_id = #{row.ownModelId,jdbcType=INTEGER},
      own_model_code = #{row.ownModelCode,jdbcType=VARCHAR},
      own_model_name = #{row.ownModelName,jdbcType=VARCHAR},
      vehicle_age = #{row.vehicleAge,jdbcType=SMALLINT},
      wheelbase = #{row.wheelbase,jdbcType=VARCHAR},
      vehicle_resource = #{row.vehicleResource,jdbcType=TINYINT},
      creter_id = #{row.creterId,jdbcType=INTEGER},
      creter_name = #{row.creterName,jdbcType=VARCHAR},
      update_id = #{row.updateId,jdbcType=INTEGER},
      update_name = #{row.updateName,jdbcType=VARCHAR},
      fuel_type = #{row.fuelType,jdbcType=TINYINT},
      fuel_no = #{row.fuelNo,jdbcType=VARCHAR},
      vehicle_output_volume = #{row.vehicleOutputVolume,jdbcType=VARCHAR},
      output_volume_unit = #{row.outputVolumeUnit,jdbcType=CHAR},
      engine_rate = #{row.engineRate,jdbcType=VARCHAR},
      seat_count = #{row.seatCount,jdbcType=TINYINT},
      maintain_mileage = #{row.maintainMileage,jdbcType=INTEGER},
      maintain_month = #{row.maintainMonth,jdbcType=TINYINT},
      drive_type = #{row.driveType,jdbcType=TINYINT},
      vehicle_pic_url = #{row.vehiclePicUrl,jdbcType=VARCHAR},
      struct_code = #{row.structCode,jdbcType=VARCHAR},
      bind_wired_gps = #{row.bindWiredGps,jdbcType=TINYINT},
      wired_gps_no = #{row.wiredGpsNo,jdbcType=VARCHAR},
      wired_sim_no = #{row.wiredSimNo,jdbcType=VARCHAR},
      wired_bind_time = #{row.wiredBindTime,jdbcType=VARCHAR},
      wired_unbind_time = #{row.wiredUnbindTime,jdbcType=VARCHAR},
      bind_wifi_gps = #{row.bindWifiGps,jdbcType=TINYINT},
      wifi_gps_no = #{row.wifiGpsNo,jdbcType=VARCHAR},
      wifi_sim_no = #{row.wifiSimNo,jdbcType=VARCHAR},
      wifi_bind_time = #{row.wifiBindTime,jdbcType=VARCHAR},
      wifi_unbind_time = #{row.wifiUnbindTime,jdbcType=VARCHAR},
      vehicle_belonged_sys = #{row.vehicleBelongedSys,jdbcType=VARCHAR},
      belong_struct_name = #{row.belongStructName,jdbcType=VARCHAR},
      belong_struct_code = #{row.belongStructCode,jdbcType=VARCHAR},
      operate_struct_name = #{row.operateStructName,jdbcType=VARCHAR},
      operate_struct_code = #{row.operateStructCode,jdbcType=VARCHAR},
      operate_buss_name = #{row.operateBussName,jdbcType=VARCHAR},
      operate_buss_code = #{row.operateBussCode,jdbcType=VARCHAR},
      vehicle_mileage = #{row.vehicleMileage,jdbcType=DECIMAL},
      occupy_staff_id = #{row.occupyStaffId,jdbcType=INTEGER},
      occupy_staff_name = #{row.occupyStaffName,jdbcType=VARCHAR},
      occupy_time = #{row.occupyTime,jdbcType=TIMESTAMP},
      vehicle_usage = #{row.vehicleUsage,jdbcType=VARCHAR},
      vehicle_id_new_system = #{row.vehicleIdNewSystem,jdbcType=INTEGER},
      vehicle_serial_no_new_system = #{row.vehicleSerialNoNewSystem,jdbcType=VARCHAR},
      asset_city_code = #{row.assetCityCode,jdbcType=VARCHAR},
      asset_city_name = #{row.assetCityName,jdbcType=VARCHAR},
      vehicle_status_level_one = #{row.vehicleStatusLevelOne,jdbcType=TINYINT},
      vehicle_status_level_two = #{row.vehicleStatusLevelTwo,jdbcType=TINYINT},
      lease_license_belong_org_name = #{row.leaseLicenseBelongOrgName,jdbcType=VARCHAR},
      belong_buss_code = #{row.belongBussCode,jdbcType=VARCHAR},
      belong_buss_name = #{row.belongBussName,jdbcType=VARCHAR},
      mobile = #{row.mobile,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.VehicleBase">
    update order_vehicle_base
    <set>
      <if test="createDate != null">
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDate != null">
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="vehicleLicense != null">
        vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="vehicleVin != null">
        vehicle_vin = #{vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBrandId != null">
        vehicle_brand_id = #{vehicleBrandId,jdbcType=INTEGER},
      </if>
      <if test="vehicleBrandCode != null">
        vehicle_brand_code = #{vehicleBrandCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBrand != null">
        vehicle_brand = #{vehicleBrand,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelId != null">
        vehicle_model_id = #{vehicleModelId,jdbcType=INTEGER},
      </if>
      <if test="vehicleModelCode != null">
        vehicle_model_code = #{vehicleModelCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModel != null">
        vehicle_model = #{vehicleModel,jdbcType=VARCHAR},
      </if>
      <if test="engineNum != null">
        engine_num = #{engineNum,jdbcType=VARCHAR},
      </if>
      <if test="vehicleColor != null">
        vehicle_color = #{vehicleColor,jdbcType=VARCHAR},
      </if>
      <if test="licenseColor != null">
        license_color = #{licenseColor,jdbcType=VARCHAR},
      </if>
      <if test="purchaseDate != null">
        purchase_date = #{purchaseDate,jdbcType=VARCHAR},
      </if>
      <if test="registerDate != null">
        register_date = #{registerDate,jdbcType=VARCHAR},
      </if>
      <if test="drivingLicenseUrl != null">
        driving_license_url = #{drivingLicenseUrl,jdbcType=VARCHAR},
      </if>
      <if test="vehicleType != null">
        vehicle_type = #{vehicleType,jdbcType=TINYINT},
      </if>
      <if test="belongCityCode != null">
        belong_city_code = #{belongCityCode,jdbcType=VARCHAR},
      </if>
      <if test="belongCityName != null">
        belong_city_name = #{belongCityName,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="structId != null">
        struct_id = #{structId,jdbcType=INTEGER},
      </if>
      <if test="structName != null">
        struct_name = #{structName,jdbcType=VARCHAR},
      </if>
      <if test="selfOwned != null">
        self_owned = #{selfOwned,jdbcType=TINYINT},
      </if>
      <if test="warnType != null">
        warn_type = #{warnType,jdbcType=VARCHAR},
      </if>
      <if test="isMrcar != null">
        is_mrcar = #{isMrcar,jdbcType=TINYINT},
      </if>
      <if test="workingStatus != null">
        working_status = #{workingStatus,jdbcType=TINYINT},
      </if>
      <if test="operationStatus != null">
        operation_status = #{operationStatus,jdbcType=TINYINT},
      </if>
      <if test="lastInspectDate != null">
        last_inspect_date = #{lastInspectDate,jdbcType=VARCHAR},
      </if>
      <if test="vehicleStatus != null">
        vehicle_status = #{vehicleStatus,jdbcType=TINYINT},
      </if>
      <if test="ownCompanyId != null">
        own_company_id = #{ownCompanyId,jdbcType=INTEGER},
      </if>
      <if test="ownStructId != null">
        own_struct_id = #{ownStructId,jdbcType=INTEGER},
      </if>
      <if test="ownStructName != null">
        own_struct_name = #{ownStructName,jdbcType=VARCHAR},
      </if>
      <if test="ownBrandId != null">
        own_brand_id = #{ownBrandId,jdbcType=INTEGER},
      </if>
      <if test="ownBrandCode != null">
        own_brand_code = #{ownBrandCode,jdbcType=VARCHAR},
      </if>
      <if test="ownBrandName != null">
        own_brand_name = #{ownBrandName,jdbcType=VARCHAR},
      </if>
      <if test="ownModelId != null">
        own_model_id = #{ownModelId,jdbcType=INTEGER},
      </if>
      <if test="ownModelCode != null">
        own_model_code = #{ownModelCode,jdbcType=VARCHAR},
      </if>
      <if test="ownModelName != null">
        own_model_name = #{ownModelName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleAge != null">
        vehicle_age = #{vehicleAge,jdbcType=SMALLINT},
      </if>
      <if test="wheelbase != null">
        wheelbase = #{wheelbase,jdbcType=VARCHAR},
      </if>
      <if test="vehicleResource != null">
        vehicle_resource = #{vehicleResource,jdbcType=TINYINT},
      </if>
      <if test="creterId != null">
        creter_id = #{creterId,jdbcType=INTEGER},
      </if>
      <if test="creterName != null">
        creter_name = #{creterName,jdbcType=VARCHAR},
      </if>
      <if test="updateId != null">
        update_id = #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="fuelType != null">
        fuel_type = #{fuelType,jdbcType=TINYINT},
      </if>
      <if test="fuelNo != null">
        fuel_no = #{fuelNo,jdbcType=VARCHAR},
      </if>
      <if test="vehicleOutputVolume != null">
        vehicle_output_volume = #{vehicleOutputVolume,jdbcType=VARCHAR},
      </if>
      <if test="outputVolumeUnit != null">
        output_volume_unit = #{outputVolumeUnit,jdbcType=CHAR},
      </if>
      <if test="engineRate != null">
        engine_rate = #{engineRate,jdbcType=VARCHAR},
      </if>
      <if test="seatCount != null">
        seat_count = #{seatCount,jdbcType=TINYINT},
      </if>
      <if test="maintainMileage != null">
        maintain_mileage = #{maintainMileage,jdbcType=INTEGER},
      </if>
      <if test="maintainMonth != null">
        maintain_month = #{maintainMonth,jdbcType=TINYINT},
      </if>
      <if test="driveType != null">
        drive_type = #{driveType,jdbcType=TINYINT},
      </if>
      <if test="vehiclePicUrl != null">
        vehicle_pic_url = #{vehiclePicUrl,jdbcType=VARCHAR},
      </if>
      <if test="structCode != null">
        struct_code = #{structCode,jdbcType=VARCHAR},
      </if>
      <if test="bindWiredGps != null">
        bind_wired_gps = #{bindWiredGps,jdbcType=TINYINT},
      </if>
      <if test="wiredGpsNo != null">
        wired_gps_no = #{wiredGpsNo,jdbcType=VARCHAR},
      </if>
      <if test="wiredSimNo != null">
        wired_sim_no = #{wiredSimNo,jdbcType=VARCHAR},
      </if>
      <if test="wiredBindTime != null">
        wired_bind_time = #{wiredBindTime,jdbcType=VARCHAR},
      </if>
      <if test="wiredUnbindTime != null">
        wired_unbind_time = #{wiredUnbindTime,jdbcType=VARCHAR},
      </if>
      <if test="bindWifiGps != null">
        bind_wifi_gps = #{bindWifiGps,jdbcType=TINYINT},
      </if>
      <if test="wifiGpsNo != null">
        wifi_gps_no = #{wifiGpsNo,jdbcType=VARCHAR},
      </if>
      <if test="wifiSimNo != null">
        wifi_sim_no = #{wifiSimNo,jdbcType=VARCHAR},
      </if>
      <if test="wifiBindTime != null">
        wifi_bind_time = #{wifiBindTime,jdbcType=VARCHAR},
      </if>
      <if test="wifiUnbindTime != null">
        wifi_unbind_time = #{wifiUnbindTime,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBelongedSys != null">
        vehicle_belonged_sys = #{vehicleBelongedSys,jdbcType=VARCHAR},
      </if>
      <if test="belongStructName != null">
        belong_struct_name = #{belongStructName,jdbcType=VARCHAR},
      </if>
      <if test="belongStructCode != null">
        belong_struct_code = #{belongStructCode,jdbcType=VARCHAR},
      </if>
      <if test="operateStructName != null">
        operate_struct_name = #{operateStructName,jdbcType=VARCHAR},
      </if>
      <if test="operateStructCode != null">
        operate_struct_code = #{operateStructCode,jdbcType=VARCHAR},
      </if>
      <if test="operateBussName != null">
        operate_buss_name = #{operateBussName,jdbcType=VARCHAR},
      </if>
      <if test="operateBussCode != null">
        operate_buss_code = #{operateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleMileage != null">
        vehicle_mileage = #{vehicleMileage,jdbcType=DECIMAL},
      </if>
      <if test="occupyStaffId != null">
        occupy_staff_id = #{occupyStaffId,jdbcType=INTEGER},
      </if>
      <if test="occupyStaffName != null">
        occupy_staff_name = #{occupyStaffName,jdbcType=VARCHAR},
      </if>
      <if test="occupyTime != null">
        occupy_time = #{occupyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="vehicleUsage != null">
        vehicle_usage = #{vehicleUsage,jdbcType=VARCHAR},
      </if>
      <if test="vehicleIdNewSystem != null">
        vehicle_id_new_system = #{vehicleIdNewSystem,jdbcType=INTEGER},
      </if>
      <if test="vehicleSerialNoNewSystem != null">
        vehicle_serial_no_new_system = #{vehicleSerialNoNewSystem,jdbcType=VARCHAR},
      </if>
      <if test="assetCityCode != null">
        asset_city_code = #{assetCityCode,jdbcType=VARCHAR},
      </if>
      <if test="assetCityName != null">
        asset_city_name = #{assetCityName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleStatusLevelOne != null">
        vehicle_status_level_one = #{vehicleStatusLevelOne,jdbcType=TINYINT},
      </if>
      <if test="vehicleStatusLevelTwo != null">
        vehicle_status_level_two = #{vehicleStatusLevelTwo,jdbcType=TINYINT},
      </if>
      <if test="leaseLicenseBelongOrgName != null">
        lease_license_belong_org_name = #{leaseLicenseBelongOrgName,jdbcType=VARCHAR},
      </if>
      <if test="belongBussCode != null">
        belong_buss_code = #{belongBussCode,jdbcType=VARCHAR},
      </if>
      <if test="belongBussName != null">
        belong_buss_name = #{belongBussName,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
    </set>
    where vehicle_id = #{vehicleId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.VehicleBase">
    update order_vehicle_base
    set create_date = #{createDate,jdbcType=TIMESTAMP},
      update_date = #{updateDate,jdbcType=TIMESTAMP},
      vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      vehicle_vin = #{vehicleVin,jdbcType=VARCHAR},
      vehicle_brand_id = #{vehicleBrandId,jdbcType=INTEGER},
      vehicle_brand_code = #{vehicleBrandCode,jdbcType=VARCHAR},
      vehicle_brand = #{vehicleBrand,jdbcType=VARCHAR},
      vehicle_model_id = #{vehicleModelId,jdbcType=INTEGER},
      vehicle_model_code = #{vehicleModelCode,jdbcType=VARCHAR},
      vehicle_model = #{vehicleModel,jdbcType=VARCHAR},
      engine_num = #{engineNum,jdbcType=VARCHAR},
      vehicle_color = #{vehicleColor,jdbcType=VARCHAR},
      license_color = #{licenseColor,jdbcType=VARCHAR},
      purchase_date = #{purchaseDate,jdbcType=VARCHAR},
      register_date = #{registerDate,jdbcType=VARCHAR},
      driving_license_url = #{drivingLicenseUrl,jdbcType=VARCHAR},
      vehicle_type = #{vehicleType,jdbcType=TINYINT},
      belong_city_code = #{belongCityCode,jdbcType=VARCHAR},
      belong_city_name = #{belongCityName,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=INTEGER},
      struct_id = #{structId,jdbcType=INTEGER},
      struct_name = #{structName,jdbcType=VARCHAR},
      self_owned = #{selfOwned,jdbcType=TINYINT},
      warn_type = #{warnType,jdbcType=VARCHAR},
      is_mrcar = #{isMrcar,jdbcType=TINYINT},
      working_status = #{workingStatus,jdbcType=TINYINT},
      operation_status = #{operationStatus,jdbcType=TINYINT},
      last_inspect_date = #{lastInspectDate,jdbcType=VARCHAR},
      vehicle_status = #{vehicleStatus,jdbcType=TINYINT},
      own_company_id = #{ownCompanyId,jdbcType=INTEGER},
      own_struct_id = #{ownStructId,jdbcType=INTEGER},
      own_struct_name = #{ownStructName,jdbcType=VARCHAR},
      own_brand_id = #{ownBrandId,jdbcType=INTEGER},
      own_brand_code = #{ownBrandCode,jdbcType=VARCHAR},
      own_brand_name = #{ownBrandName,jdbcType=VARCHAR},
      own_model_id = #{ownModelId,jdbcType=INTEGER},
      own_model_code = #{ownModelCode,jdbcType=VARCHAR},
      own_model_name = #{ownModelName,jdbcType=VARCHAR},
      vehicle_age = #{vehicleAge,jdbcType=SMALLINT},
      wheelbase = #{wheelbase,jdbcType=VARCHAR},
      vehicle_resource = #{vehicleResource,jdbcType=TINYINT},
      creter_id = #{creterId,jdbcType=INTEGER},
      creter_name = #{creterName,jdbcType=VARCHAR},
      update_id = #{updateId,jdbcType=INTEGER},
      update_name = #{updateName,jdbcType=VARCHAR},
      fuel_type = #{fuelType,jdbcType=TINYINT},
      fuel_no = #{fuelNo,jdbcType=VARCHAR},
      vehicle_output_volume = #{vehicleOutputVolume,jdbcType=VARCHAR},
      output_volume_unit = #{outputVolumeUnit,jdbcType=CHAR},
      engine_rate = #{engineRate,jdbcType=VARCHAR},
      seat_count = #{seatCount,jdbcType=TINYINT},
      maintain_mileage = #{maintainMileage,jdbcType=INTEGER},
      maintain_month = #{maintainMonth,jdbcType=TINYINT},
      drive_type = #{driveType,jdbcType=TINYINT},
      vehicle_pic_url = #{vehiclePicUrl,jdbcType=VARCHAR},
      struct_code = #{structCode,jdbcType=VARCHAR},
      bind_wired_gps = #{bindWiredGps,jdbcType=TINYINT},
      wired_gps_no = #{wiredGpsNo,jdbcType=VARCHAR},
      wired_sim_no = #{wiredSimNo,jdbcType=VARCHAR},
      wired_bind_time = #{wiredBindTime,jdbcType=VARCHAR},
      wired_unbind_time = #{wiredUnbindTime,jdbcType=VARCHAR},
      bind_wifi_gps = #{bindWifiGps,jdbcType=TINYINT},
      wifi_gps_no = #{wifiGpsNo,jdbcType=VARCHAR},
      wifi_sim_no = #{wifiSimNo,jdbcType=VARCHAR},
      wifi_bind_time = #{wifiBindTime,jdbcType=VARCHAR},
      wifi_unbind_time = #{wifiUnbindTime,jdbcType=VARCHAR},
      vehicle_belonged_sys = #{vehicleBelongedSys,jdbcType=VARCHAR},
      belong_struct_name = #{belongStructName,jdbcType=VARCHAR},
      belong_struct_code = #{belongStructCode,jdbcType=VARCHAR},
      operate_struct_name = #{operateStructName,jdbcType=VARCHAR},
      operate_struct_code = #{operateStructCode,jdbcType=VARCHAR},
      operate_buss_name = #{operateBussName,jdbcType=VARCHAR},
      operate_buss_code = #{operateBussCode,jdbcType=VARCHAR},
      vehicle_mileage = #{vehicleMileage,jdbcType=DECIMAL},
      occupy_staff_id = #{occupyStaffId,jdbcType=INTEGER},
      occupy_staff_name = #{occupyStaffName,jdbcType=VARCHAR},
      occupy_time = #{occupyTime,jdbcType=TIMESTAMP},
      vehicle_usage = #{vehicleUsage,jdbcType=VARCHAR},
      vehicle_id_new_system = #{vehicleIdNewSystem,jdbcType=INTEGER},
      vehicle_serial_no_new_system = #{vehicleSerialNoNewSystem,jdbcType=VARCHAR},
      asset_city_code = #{assetCityCode,jdbcType=VARCHAR},
      asset_city_name = #{assetCityName,jdbcType=VARCHAR},
      vehicle_status_level_one = #{vehicleStatusLevelOne,jdbcType=TINYINT},
      vehicle_status_level_two = #{vehicleStatusLevelTwo,jdbcType=TINYINT},
      lease_license_belong_org_name = #{leaseLicenseBelongOrgName,jdbcType=VARCHAR},
      belong_buss_code = #{belongBussCode,jdbcType=VARCHAR},
      belong_buss_name = #{belongBussName,jdbcType=VARCHAR},
      mobile = #{mobile,jdbcType=VARCHAR}
    where vehicle_id = #{vehicleId,jdbcType=BIGINT}
  </update>
</mapper>