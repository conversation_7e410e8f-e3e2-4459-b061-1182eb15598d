<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.BusinessOperationLogMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.BusinessOperationLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="expenditure_no" jdbcType="VARCHAR" property="expenditureNo" />
    <result column="operator_id" jdbcType="INTEGER" property="operatorId" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="operation_time" jdbcType="TIMESTAMP" property="operationTime" />
    <result column="operation_type" jdbcType="TINYINT" property="operationType" />
    <result column="operation_type_name" jdbcType="VARCHAR" property="operationTypeName" />
    <result column="operation_field" jdbcType="VARCHAR" property="operationField" />
    <result column="before_value" jdbcType="VARCHAR" property="beforeValue" />
    <result column="after_value" jdbcType="VARCHAR" property="afterValue" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, expenditure_no, operator_id, operator_name, operation_time, operation_type, operation_type_name, 
    operation_field, before_value, after_value, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.BusinessOperationLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from business_operation_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from business_operation_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from business_operation_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.order.entity.mrcar.BusinessOperationLogExample">
    delete from business_operation_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.BusinessOperationLog">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into business_operation_log (expenditure_no, operator_id, operator_name, 
      operation_time, operation_type, operation_type_name, 
      operation_field, before_value, after_value, 
      create_time, update_time)
    values (#{expenditureNo,jdbcType=VARCHAR}, #{operatorId,jdbcType=INTEGER}, #{operatorName,jdbcType=VARCHAR}, 
      #{operationTime,jdbcType=TIMESTAMP}, #{operationType,jdbcType=TINYINT}, #{operationTypeName,jdbcType=VARCHAR}, 
      #{operationField,jdbcType=VARCHAR}, #{beforeValue,jdbcType=VARCHAR}, #{afterValue,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.BusinessOperationLog">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into business_operation_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="expenditureNo != null">
        expenditure_no,
      </if>
      <if test="operatorId != null">
        operator_id,
      </if>
      <if test="operatorName != null">
        operator_name,
      </if>
      <if test="operationTime != null">
        operation_time,
      </if>
      <if test="operationType != null">
        operation_type,
      </if>
      <if test="operationTypeName != null">
        operation_type_name,
      </if>
      <if test="operationField != null">
        operation_field,
      </if>
      <if test="beforeValue != null">
        before_value,
      </if>
      <if test="afterValue != null">
        after_value,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="expenditureNo != null">
        #{expenditureNo,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        #{operatorId,jdbcType=INTEGER},
      </if>
      <if test="operatorName != null">
        #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="operationTime != null">
        #{operationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operationType != null">
        #{operationType,jdbcType=TINYINT},
      </if>
      <if test="operationTypeName != null">
        #{operationTypeName,jdbcType=VARCHAR},
      </if>
      <if test="operationField != null">
        #{operationField,jdbcType=VARCHAR},
      </if>
      <if test="beforeValue != null">
        #{beforeValue,jdbcType=VARCHAR},
      </if>
      <if test="afterValue != null">
        #{afterValue,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.BusinessOperationLogExample" resultType="java.lang.Long">
    select count(*) from business_operation_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update business_operation_log
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.expenditureNo != null">
        expenditure_no = #{row.expenditureNo,jdbcType=VARCHAR},
      </if>
      <if test="row.operatorId != null">
        operator_id = #{row.operatorId,jdbcType=INTEGER},
      </if>
      <if test="row.operatorName != null">
        operator_name = #{row.operatorName,jdbcType=VARCHAR},
      </if>
      <if test="row.operationTime != null">
        operation_time = #{row.operationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.operationType != null">
        operation_type = #{row.operationType,jdbcType=TINYINT},
      </if>
      <if test="row.operationTypeName != null">
        operation_type_name = #{row.operationTypeName,jdbcType=VARCHAR},
      </if>
      <if test="row.operationField != null">
        operation_field = #{row.operationField,jdbcType=VARCHAR},
      </if>
      <if test="row.beforeValue != null">
        before_value = #{row.beforeValue,jdbcType=VARCHAR},
      </if>
      <if test="row.afterValue != null">
        after_value = #{row.afterValue,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update business_operation_log
    set id = #{row.id,jdbcType=BIGINT},
      expenditure_no = #{row.expenditureNo,jdbcType=VARCHAR},
      operator_id = #{row.operatorId,jdbcType=INTEGER},
      operator_name = #{row.operatorName,jdbcType=VARCHAR},
      operation_time = #{row.operationTime,jdbcType=TIMESTAMP},
      operation_type = #{row.operationType,jdbcType=TINYINT},
      operation_type_name = #{row.operationTypeName,jdbcType=VARCHAR},
      operation_field = #{row.operationField,jdbcType=VARCHAR},
      before_value = #{row.beforeValue,jdbcType=VARCHAR},
      after_value = #{row.afterValue,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.BusinessOperationLog">
    update business_operation_log
    <set>
      <if test="expenditureNo != null">
        expenditure_no = #{expenditureNo,jdbcType=VARCHAR},
      </if>
      <if test="operatorId != null">
        operator_id = #{operatorId,jdbcType=INTEGER},
      </if>
      <if test="operatorName != null">
        operator_name = #{operatorName,jdbcType=VARCHAR},
      </if>
      <if test="operationTime != null">
        operation_time = #{operationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="operationType != null">
        operation_type = #{operationType,jdbcType=TINYINT},
      </if>
      <if test="operationTypeName != null">
        operation_type_name = #{operationTypeName,jdbcType=VARCHAR},
      </if>
      <if test="operationField != null">
        operation_field = #{operationField,jdbcType=VARCHAR},
      </if>
      <if test="beforeValue != null">
        before_value = #{beforeValue,jdbcType=VARCHAR},
      </if>
      <if test="afterValue != null">
        after_value = #{afterValue,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.BusinessOperationLog">
    update business_operation_log
    set expenditure_no = #{expenditureNo,jdbcType=VARCHAR},
      operator_id = #{operatorId,jdbcType=INTEGER},
      operator_name = #{operatorName,jdbcType=VARCHAR},
      operation_time = #{operationTime,jdbcType=TIMESTAMP},
      operation_type = #{operationType,jdbcType=TINYINT},
      operation_type_name = #{operationTypeName,jdbcType=VARCHAR},
      operation_field = #{operationField,jdbcType=VARCHAR},
      before_value = #{beforeValue,jdbcType=VARCHAR},
      after_value = #{afterValue,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>