<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.GovCarOrderDataSnapshotMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.GovCarOrderDataSnapshot">
    <id column="snapshot_id" jdbcType="INTEGER" property="snapshotId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="order_time" jdbcType="TIMESTAMP" property="orderTime" />
    <result column="vehicle_serial_no" jdbcType="VARCHAR" property="vehicleSerialNo" />
    <result column="vehicle_license" jdbcType="VARCHAR" property="vehicleLicense" />
    <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode" />
    <result column="vehicle_model_name" jdbcType="VARCHAR" property="vehicleModelName" />
    <result column="operate_buss_code" jdbcType="VARCHAR" property="operateBussCode" />
    <result column="operate_buss_name" jdbcType="VARCHAR" property="operateBussName" />
    <result column="belong_buss_code" jdbcType="VARCHAR" property="belongBussCode" />
    <result column="belong_buss_name" jdbcType="VARCHAR" property="belongBussName" />
    <result column="admin_store_code" jdbcType="VARCHAR" property="adminStoreCode" />
    <result column="admin_store_name" jdbcType="VARCHAR" property="adminStoreName" />
    <result column="belong_store_code" jdbcType="VARCHAR" property="belongStoreCode" />
    <result column="belong_store_name" jdbcType="VARCHAR" property="belongStoreName" />
    <result column="lease_purpose" jdbcType="VARCHAR" property="leasePurpose" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="vehicle_struct_id" jdbcType="INTEGER" property="vehicleStructId" />
    <result column="vehicle_struct_code" jdbcType="VARCHAR" property="vehicleStructCode" />
    <result column="vehicle_struct_name" jdbcType="VARCHAR" property="vehicleStructName" />
    <result column="struct_id" jdbcType="INTEGER" property="structId" />
    <result column="struct_code" jdbcType="VARCHAR" property="structCode" />
    <result column="struct_name" jdbcType="VARCHAR" property="structName" />
    <result column="customer_id" jdbcType="INTEGER" property="customerId" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="customer_mobile" jdbcType="VARCHAR" property="customerMobile" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.izu.order.entity.mrcar.GovCarOrderDataSnapshot">
    <result column="price_config" jdbcType="LONGVARCHAR" property="priceConfig" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    snapshot_id, create_time, update_time, order_no, order_time, vehicle_serial_no, vehicle_license,
    vehicle_model_code, vehicle_model_name, operate_buss_code, operate_buss_name, belong_buss_code,
    belong_buss_name, admin_store_code, admin_store_name, belong_store_code, belong_store_name,
    lease_purpose, company_code, company_name, vehicle_struct_id, vehicle_struct_code,
    vehicle_struct_name, struct_id, struct_code, struct_name, customer_id, customer_name,
    customer_mobile
  </sql>
  <sql id="Blob_Column_List">
    price_config
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.izu.order.entity.mrcar.GovCarOrderDataSnapshotExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from gov_car_order_data_snapshot
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.GovCarOrderDataSnapshotExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from gov_car_order_data_snapshot
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from gov_car_order_data_snapshot
    where snapshot_id = #{snapshotId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from gov_car_order_data_snapshot
    where snapshot_id = #{snapshotId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.GovCarOrderDataSnapshot">
    <selectKey keyProperty="snapshotId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gov_car_order_data_snapshot (create_time, update_time, order_no,
      order_time, vehicle_serial_no, vehicle_license,
      vehicle_model_code, vehicle_model_name, operate_buss_code,
      operate_buss_name, belong_buss_code, belong_buss_name,
      admin_store_code, admin_store_name, belong_store_code,
      belong_store_name, lease_purpose, company_code,
      company_name, vehicle_struct_id, vehicle_struct_code,
      vehicle_struct_name, struct_id, struct_code,
      struct_name, customer_id, customer_name,
      customer_mobile, price_config)
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{orderNo,jdbcType=VARCHAR},
      #{orderTime,jdbcType=TIMESTAMP}, #{vehicleSerialNo,jdbcType=VARCHAR}, #{vehicleLicense,jdbcType=VARCHAR},
      #{vehicleModelCode,jdbcType=VARCHAR}, #{vehicleModelName,jdbcType=VARCHAR}, #{operateBussCode,jdbcType=VARCHAR},
      #{operateBussName,jdbcType=VARCHAR}, #{belongBussCode,jdbcType=VARCHAR}, #{belongBussName,jdbcType=VARCHAR},
      #{adminStoreCode,jdbcType=VARCHAR}, #{adminStoreName,jdbcType=VARCHAR}, #{belongStoreCode,jdbcType=VARCHAR},
      #{belongStoreName,jdbcType=VARCHAR}, #{leasePurpose,jdbcType=VARCHAR}, #{companyCode,jdbcType=VARCHAR},
      #{companyName,jdbcType=VARCHAR}, #{vehicleStructId,jdbcType=INTEGER}, #{vehicleStructCode,jdbcType=VARCHAR},
      #{vehicleStructName,jdbcType=VARCHAR}, #{structId,jdbcType=INTEGER}, #{structCode,jdbcType=VARCHAR},
      #{structName,jdbcType=VARCHAR}, #{customerId,jdbcType=INTEGER}, #{customerName,jdbcType=VARCHAR},
      #{customerMobile,jdbcType=VARCHAR}, #{priceConfig})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.GovCarOrderDataSnapshot">
    <selectKey keyProperty="snapshotId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gov_car_order_data_snapshot
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="orderTime != null">
        order_time,
      </if>
      <if test="vehicleSerialNo != null">
        vehicle_serial_no,
      </if>
      <if test="vehicleLicense != null">
        vehicle_license,
      </if>
      <if test="vehicleModelCode != null">
        vehicle_model_code,
      </if>
      <if test="vehicleModelName != null">
        vehicle_model_name,
      </if>
      <if test="operateBussCode != null">
        operate_buss_code,
      </if>
      <if test="operateBussName != null">
        operate_buss_name,
      </if>
      <if test="belongBussCode != null">
        belong_buss_code,
      </if>
      <if test="belongBussName != null">
        belong_buss_name,
      </if>
      <if test="adminStoreCode != null">
        admin_store_code,
      </if>
      <if test="adminStoreName != null">
        admin_store_name,
      </if>
      <if test="belongStoreCode != null">
        belong_store_code,
      </if>
      <if test="belongStoreName != null">
        belong_store_name,
      </if>
      <if test="leasePurpose != null">
        lease_purpose,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="vehicleStructId != null">
        vehicle_struct_id,
      </if>
      <if test="vehicleStructCode != null">
        vehicle_struct_code,
      </if>
      <if test="vehicleStructName != null">
        vehicle_struct_name,
      </if>
      <if test="structId != null">
        struct_id,
      </if>
      <if test="structCode != null">
        struct_code,
      </if>
      <if test="structName != null">
        struct_name,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="customerMobile != null">
        customer_mobile,
      </if>
      <if test="priceConfig != null">
        price_config,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderTime != null">
        #{orderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="vehicleSerialNo != null">
        #{vehicleSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="vehicleLicense != null">
        #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelCode != null">
        #{vehicleModelCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelName != null">
        #{vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="operateBussCode != null">
        #{operateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="operateBussName != null">
        #{operateBussName,jdbcType=VARCHAR},
      </if>
      <if test="belongBussCode != null">
        #{belongBussCode,jdbcType=VARCHAR},
      </if>
      <if test="belongBussName != null">
        #{belongBussName,jdbcType=VARCHAR},
      </if>
      <if test="adminStoreCode != null">
        #{adminStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="adminStoreName != null">
        #{adminStoreName,jdbcType=VARCHAR},
      </if>
      <if test="belongStoreCode != null">
        #{belongStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="belongStoreName != null">
        #{belongStoreName,jdbcType=VARCHAR},
      </if>
      <if test="leasePurpose != null">
        #{leasePurpose,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleStructId != null">
        #{vehicleStructId,jdbcType=INTEGER},
      </if>
      <if test="vehicleStructCode != null">
        #{vehicleStructCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleStructName != null">
        #{vehicleStructName,jdbcType=VARCHAR},
      </if>
      <if test="structId != null">
        #{structId,jdbcType=INTEGER},
      </if>
      <if test="structCode != null">
        #{structCode,jdbcType=VARCHAR},
      </if>
      <if test="structName != null">
        #{structName,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=INTEGER},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerMobile != null">
        #{customerMobile,jdbcType=VARCHAR},
      </if>
      <if test="priceConfig != null">
        #{priceConfig},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.GovCarOrderDataSnapshotExample" resultType="java.lang.Long">
    select count(*) from gov_car_order_data_snapshot
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update gov_car_order_data_snapshot
    <set>
      <if test="row.snapshotId != null">
        snapshot_id = #{row.snapshotId,jdbcType=INTEGER},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.orderNo != null">
        order_no = #{row.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="row.orderTime != null">
        order_time = #{row.orderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.vehicleSerialNo != null">
        vehicle_serial_no = #{row.vehicleSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleLicense != null">
        vehicle_license = #{row.vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleModelCode != null">
        vehicle_model_code = #{row.vehicleModelCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleModelName != null">
        vehicle_model_name = #{row.vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="row.operateBussCode != null">
        operate_buss_code = #{row.operateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="row.operateBussName != null">
        operate_buss_name = #{row.operateBussName,jdbcType=VARCHAR},
      </if>
      <if test="row.belongBussCode != null">
        belong_buss_code = #{row.belongBussCode,jdbcType=VARCHAR},
      </if>
      <if test="row.belongBussName != null">
        belong_buss_name = #{row.belongBussName,jdbcType=VARCHAR},
      </if>
      <if test="row.adminStoreCode != null">
        admin_store_code = #{row.adminStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="row.adminStoreName != null">
        admin_store_name = #{row.adminStoreName,jdbcType=VARCHAR},
      </if>
      <if test="row.belongStoreCode != null">
        belong_store_code = #{row.belongStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="row.belongStoreName != null">
        belong_store_name = #{row.belongStoreName,jdbcType=VARCHAR},
      </if>
      <if test="row.leasePurpose != null">
        lease_purpose = #{row.leasePurpose,jdbcType=VARCHAR},
      </if>
      <if test="row.companyCode != null">
        company_code = #{row.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="row.companyName != null">
        company_name = #{row.companyName,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleStructId != null">
        vehicle_struct_id = #{row.vehicleStructId,jdbcType=INTEGER},
      </if>
      <if test="row.vehicleStructCode != null">
        vehicle_struct_code = #{row.vehicleStructCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleStructName != null">
        vehicle_struct_name = #{row.vehicleStructName,jdbcType=VARCHAR},
      </if>
      <if test="row.structId != null">
        struct_id = #{row.structId,jdbcType=INTEGER},
      </if>
      <if test="row.structCode != null">
        struct_code = #{row.structCode,jdbcType=VARCHAR},
      </if>
      <if test="row.structName != null">
        struct_name = #{row.structName,jdbcType=VARCHAR},
      </if>
      <if test="row.customerId != null">
        customer_id = #{row.customerId,jdbcType=INTEGER},
      </if>
      <if test="row.customerName != null">
        customer_name = #{row.customerName,jdbcType=VARCHAR},
      </if>
      <if test="row.customerMobile != null">
        customer_mobile = #{row.customerMobile,jdbcType=VARCHAR},
      </if>
      <if test="row.priceConfig != null">
        price_config = #{row.priceConfig},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update gov_car_order_data_snapshot
    set snapshot_id = #{row.snapshotId,jdbcType=INTEGER},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      order_no = #{row.orderNo,jdbcType=VARCHAR},
      order_time = #{row.orderTime,jdbcType=TIMESTAMP},
      vehicle_serial_no = #{row.vehicleSerialNo,jdbcType=VARCHAR},
      vehicle_license = #{row.vehicleLicense,jdbcType=VARCHAR},
      vehicle_model_code = #{row.vehicleModelCode,jdbcType=VARCHAR},
      vehicle_model_name = #{row.vehicleModelName,jdbcType=VARCHAR},
      operate_buss_code = #{row.operateBussCode,jdbcType=VARCHAR},
      operate_buss_name = #{row.operateBussName,jdbcType=VARCHAR},
      belong_buss_code = #{row.belongBussCode,jdbcType=VARCHAR},
      belong_buss_name = #{row.belongBussName,jdbcType=VARCHAR},
      admin_store_code = #{row.adminStoreCode,jdbcType=VARCHAR},
      admin_store_name = #{row.adminStoreName,jdbcType=VARCHAR},
      belong_store_code = #{row.belongStoreCode,jdbcType=VARCHAR},
      belong_store_name = #{row.belongStoreName,jdbcType=VARCHAR},
      lease_purpose = #{row.leasePurpose,jdbcType=VARCHAR},
      company_code = #{row.companyCode,jdbcType=VARCHAR},
      company_name = #{row.companyName,jdbcType=VARCHAR},
      vehicle_struct_id = #{row.vehicleStructId,jdbcType=INTEGER},
      vehicle_struct_code = #{row.vehicleStructCode,jdbcType=VARCHAR},
      vehicle_struct_name = #{row.vehicleStructName,jdbcType=VARCHAR},
      struct_id = #{row.structId,jdbcType=INTEGER},
      struct_code = #{row.structCode,jdbcType=VARCHAR},
      struct_name = #{row.structName,jdbcType=VARCHAR},
      customer_id = #{row.customerId,jdbcType=INTEGER},
      customer_name = #{row.customerName,jdbcType=VARCHAR},
      customer_mobile = #{row.customerMobile,jdbcType=VARCHAR},
      price_config = #{row.priceConfig}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update gov_car_order_data_snapshot
    set snapshot_id = #{row.snapshotId,jdbcType=INTEGER},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      order_no = #{row.orderNo,jdbcType=VARCHAR},
      order_time = #{row.orderTime,jdbcType=TIMESTAMP},
      vehicle_serial_no = #{row.vehicleSerialNo,jdbcType=VARCHAR},
      vehicle_license = #{row.vehicleLicense,jdbcType=VARCHAR},
      vehicle_model_code = #{row.vehicleModelCode,jdbcType=VARCHAR},
      vehicle_model_name = #{row.vehicleModelName,jdbcType=VARCHAR},
      operate_buss_code = #{row.operateBussCode,jdbcType=VARCHAR},
      operate_buss_name = #{row.operateBussName,jdbcType=VARCHAR},
      belong_buss_code = #{row.belongBussCode,jdbcType=VARCHAR},
      belong_buss_name = #{row.belongBussName,jdbcType=VARCHAR},
      admin_store_code = #{row.adminStoreCode,jdbcType=VARCHAR},
      admin_store_name = #{row.adminStoreName,jdbcType=VARCHAR},
      belong_store_code = #{row.belongStoreCode,jdbcType=VARCHAR},
      belong_store_name = #{row.belongStoreName,jdbcType=VARCHAR},
      lease_purpose = #{row.leasePurpose,jdbcType=VARCHAR},
      company_code = #{row.companyCode,jdbcType=VARCHAR},
      company_name = #{row.companyName,jdbcType=VARCHAR},
      vehicle_struct_id = #{row.vehicleStructId,jdbcType=INTEGER},
      vehicle_struct_code = #{row.vehicleStructCode,jdbcType=VARCHAR},
      vehicle_struct_name = #{row.vehicleStructName,jdbcType=VARCHAR},
      struct_id = #{row.structId,jdbcType=INTEGER},
      struct_code = #{row.structCode,jdbcType=VARCHAR},
      struct_name = #{row.structName,jdbcType=VARCHAR},
      customer_id = #{row.customerId,jdbcType=INTEGER},
      customer_name = #{row.customerName,jdbcType=VARCHAR},
      customer_mobile = #{row.customerMobile,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.GovCarOrderDataSnapshot">
    update gov_car_order_data_snapshot
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderTime != null">
        order_time = #{orderTime,jdbcType=TIMESTAMP},
      </if>
      <if test="vehicleSerialNo != null">
        vehicle_serial_no = #{vehicleSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="vehicleLicense != null">
        vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelCode != null">
        vehicle_model_code = #{vehicleModelCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelName != null">
        vehicle_model_name = #{vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="operateBussCode != null">
        operate_buss_code = #{operateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="operateBussName != null">
        operate_buss_name = #{operateBussName,jdbcType=VARCHAR},
      </if>
      <if test="belongBussCode != null">
        belong_buss_code = #{belongBussCode,jdbcType=VARCHAR},
      </if>
      <if test="belongBussName != null">
        belong_buss_name = #{belongBussName,jdbcType=VARCHAR},
      </if>
      <if test="adminStoreCode != null">
        admin_store_code = #{adminStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="adminStoreName != null">
        admin_store_name = #{adminStoreName,jdbcType=VARCHAR},
      </if>
      <if test="belongStoreCode != null">
        belong_store_code = #{belongStoreCode,jdbcType=VARCHAR},
      </if>
      <if test="belongStoreName != null">
        belong_store_name = #{belongStoreName,jdbcType=VARCHAR},
      </if>
      <if test="leasePurpose != null">
        lease_purpose = #{leasePurpose,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleStructId != null">
        vehicle_struct_id = #{vehicleStructId,jdbcType=INTEGER},
      </if>
      <if test="vehicleStructCode != null">
        vehicle_struct_code = #{vehicleStructCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleStructName != null">
        vehicle_struct_name = #{vehicleStructName,jdbcType=VARCHAR},
      </if>
      <if test="structId != null">
        struct_id = #{structId,jdbcType=INTEGER},
      </if>
      <if test="structCode != null">
        struct_code = #{structCode,jdbcType=VARCHAR},
      </if>
      <if test="structName != null">
        struct_name = #{structName,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=INTEGER},
      </if>
      <if test="customerName != null">
        customer_name = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerMobile != null">
        customer_mobile = #{customerMobile,jdbcType=VARCHAR},
      </if>
      <if test="priceConfig != null">
        price_config = #{priceConfig},
      </if>
    </set>
    where snapshot_id = #{snapshotId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.izu.order.entity.mrcar.GovCarOrderDataSnapshot">
    update gov_car_order_data_snapshot
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      order_no = #{orderNo,jdbcType=VARCHAR},
      order_time = #{orderTime,jdbcType=TIMESTAMP},
      vehicle_serial_no = #{vehicleSerialNo,jdbcType=VARCHAR},
      vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      vehicle_model_code = #{vehicleModelCode,jdbcType=VARCHAR},
      vehicle_model_name = #{vehicleModelName,jdbcType=VARCHAR},
      operate_buss_code = #{operateBussCode,jdbcType=VARCHAR},
      operate_buss_name = #{operateBussName,jdbcType=VARCHAR},
      belong_buss_code = #{belongBussCode,jdbcType=VARCHAR},
      belong_buss_name = #{belongBussName,jdbcType=VARCHAR},
      admin_store_code = #{adminStoreCode,jdbcType=VARCHAR},
      admin_store_name = #{adminStoreName,jdbcType=VARCHAR},
      belong_store_code = #{belongStoreCode,jdbcType=VARCHAR},
      belong_store_name = #{belongStoreName,jdbcType=VARCHAR},
      lease_purpose = #{leasePurpose,jdbcType=VARCHAR},
      company_code = #{companyCode,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      vehicle_struct_id = #{vehicleStructId,jdbcType=INTEGER},
      vehicle_struct_code = #{vehicleStructCode,jdbcType=VARCHAR},
      vehicle_struct_name = #{vehicleStructName,jdbcType=VARCHAR},
      struct_id = #{structId,jdbcType=INTEGER},
      struct_code = #{structCode,jdbcType=VARCHAR},
      struct_name = #{structName,jdbcType=VARCHAR},
      customer_id = #{customerId,jdbcType=INTEGER},
      customer_name = #{customerName,jdbcType=VARCHAR},
      customer_mobile = #{customerMobile,jdbcType=VARCHAR},
      price_config = #{priceConfig}
    where snapshot_id = #{snapshotId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.GovCarOrderDataSnapshot">
    update gov_car_order_data_snapshot
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      order_no = #{orderNo,jdbcType=VARCHAR},
      order_time = #{orderTime,jdbcType=TIMESTAMP},
      vehicle_serial_no = #{vehicleSerialNo,jdbcType=VARCHAR},
      vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      vehicle_model_code = #{vehicleModelCode,jdbcType=VARCHAR},
      vehicle_model_name = #{vehicleModelName,jdbcType=VARCHAR},
      operate_buss_code = #{operateBussCode,jdbcType=VARCHAR},
      operate_buss_name = #{operateBussName,jdbcType=VARCHAR},
      belong_buss_code = #{belongBussCode,jdbcType=VARCHAR},
      belong_buss_name = #{belongBussName,jdbcType=VARCHAR},
      admin_store_code = #{adminStoreCode,jdbcType=VARCHAR},
      admin_store_name = #{adminStoreName,jdbcType=VARCHAR},
      belong_store_code = #{belongStoreCode,jdbcType=VARCHAR},
      belong_store_name = #{belongStoreName,jdbcType=VARCHAR},
      lease_purpose = #{leasePurpose,jdbcType=VARCHAR},
      company_code = #{companyCode,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      vehicle_struct_id = #{vehicleStructId,jdbcType=INTEGER},
      vehicle_struct_code = #{vehicleStructCode,jdbcType=VARCHAR},
      vehicle_struct_name = #{vehicleStructName,jdbcType=VARCHAR},
      struct_id = #{structId,jdbcType=INTEGER},
      struct_code = #{structCode,jdbcType=VARCHAR},
      struct_name = #{structName,jdbcType=VARCHAR},
      customer_id = #{customerId,jdbcType=INTEGER},
      customer_name = #{customerName,jdbcType=VARCHAR},
      customer_mobile = #{customerMobile,jdbcType=VARCHAR}
    where snapshot_id = #{snapshotId,jdbcType=INTEGER}
  </update>
</mapper>
