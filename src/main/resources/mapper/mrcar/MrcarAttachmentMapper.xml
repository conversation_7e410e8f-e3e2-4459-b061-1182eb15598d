<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.MrcarAttachmentMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.MrcarAttachment">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="bus_id" jdbcType="INTEGER" property="busId" />
    <result column="bus_no" jdbcType="VARCHAR" property="busNo" />
    <result column="bus_type" jdbcType="TINYINT" property="busType" />
    <result column="file_url" jdbcType="VARCHAR" property="fileUrl" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="file_real_name" jdbcType="VARCHAR" property="fileRealName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="file_category" jdbcType="TINYINT" property="fileCategory" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="file_description" jdbcType="VARCHAR" property="fileDescription" />
  </resultMap>
  <sql id="Base_Column_List">
    id, bus_id, bus_no, bus_type, file_url, file_name, file_real_name, create_time, update_time, 
    file_category, status, file_description
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mrcar_attachment
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from mrcar_attachment
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.MrcarAttachment">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mrcar_attachment (bus_id, bus_no, bus_type, 
      file_url, file_name, file_real_name, 
      create_time, update_time, file_category, 
      status, file_description)
    values (#{busId,jdbcType=INTEGER}, #{busNo,jdbcType=VARCHAR}, #{busType,jdbcType=TINYINT}, 
      #{fileUrl,jdbcType=VARCHAR}, #{fileName,jdbcType=VARCHAR}, #{fileRealName,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{fileCategory,jdbcType=TINYINT}, 
      #{status,jdbcType=TINYINT}, #{fileDescription,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.MrcarAttachment">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mrcar_attachment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="busId != null">
        bus_id,
      </if>
      <if test="busNo != null">
        bus_no,
      </if>
      <if test="busType != null">
        bus_type,
      </if>
      <if test="fileUrl != null">
        file_url,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="fileRealName != null">
        file_real_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="fileCategory != null">
        file_category,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="fileDescription != null">
        file_description,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="busId != null">
        #{busId,jdbcType=INTEGER},
      </if>
      <if test="busNo != null">
        #{busNo,jdbcType=VARCHAR},
      </if>
      <if test="busType != null">
        #{busType,jdbcType=TINYINT},
      </if>
      <if test="fileUrl != null">
        #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileRealName != null">
        #{fileRealName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="fileCategory != null">
        #{fileCategory,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="fileDescription != null">
        #{fileDescription,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.MrcarAttachment">
    update mrcar_attachment
    <set>
      <if test="busId != null">
        bus_id = #{busId,jdbcType=INTEGER},
      </if>
      <if test="busNo != null">
        bus_no = #{busNo,jdbcType=VARCHAR},
      </if>
      <if test="busType != null">
        bus_type = #{busType,jdbcType=TINYINT},
      </if>
      <if test="fileUrl != null">
        file_url = #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileRealName != null">
        file_real_name = #{fileRealName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="fileCategory != null">
        file_category = #{fileCategory,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="fileDescription != null">
        file_description = #{fileDescription,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.MrcarAttachment">
    update mrcar_attachment
    set bus_id = #{busId,jdbcType=INTEGER},
      bus_no = #{busNo,jdbcType=VARCHAR},
      bus_type = #{busType,jdbcType=TINYINT},
      file_url = #{fileUrl,jdbcType=VARCHAR},
      file_name = #{fileName,jdbcType=VARCHAR},
      file_real_name = #{fileRealName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      file_category = #{fileCategory,jdbcType=TINYINT},
      status = #{status,jdbcType=TINYINT},
      file_description = #{fileDescription,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>