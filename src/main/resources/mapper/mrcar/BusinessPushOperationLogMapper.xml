<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.BusinessPushOperationLogMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.BusinessPushOperationLog">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_apply_no" jdbcType="VARCHAR" property="orderApplyNo" />
    <result column="login_code" jdbcType="VARCHAR" property="loginCode" />
    <result column="login_name" jdbcType="VARCHAR" property="loginName" />
    <result column="operation_time" jdbcType="TIMESTAMP" property="operationTime" />
    <result column="push_type" jdbcType="TINYINT" property="pushType" />
    <result column="push_status" jdbcType="TINYINT" property="pushStatus" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.izu.order.entity.mrcar.BusinessPushOperationLog">
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_apply_no, login_code, login_name, operation_time, push_type, push_status
  </sql>
  <sql id="Blob_Column_List">
    content
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.izu.order.entity.mrcar.BusinessPushOperationLogExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from business_push_operation_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.BusinessPushOperationLogExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from business_push_operation_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from business_push_operation_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from business_push_operation_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.order.entity.mrcar.BusinessPushOperationLogExample">
    delete from business_push_operation_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.BusinessPushOperationLog">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into business_push_operation_log (order_apply_no, login_code, login_name, 
      operation_time, push_type, push_status, 
      content)
    values (#{orderApplyNo,jdbcType=VARCHAR}, #{loginCode,jdbcType=VARCHAR}, #{loginName,jdbcType=VARCHAR}, 
      #{operationTime,jdbcType=TIMESTAMP}, #{pushType,jdbcType=TINYINT}, #{pushStatus,jdbcType=TINYINT}, 
      #{content,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.BusinessPushOperationLog">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into business_push_operation_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderApplyNo != null">
        order_apply_no,
      </if>
      <if test="loginCode != null">
        login_code,
      </if>
      <if test="loginName != null">
        login_name,
      </if>
      <if test="operationTime != null">
        operation_time,
      </if>
      <if test="pushType != null">
        push_type,
      </if>
      <if test="pushStatus != null">
        push_status,
      </if>
      <if test="content != null">
        content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderApplyNo != null">
        #{orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="loginCode != null">
        #{loginCode,jdbcType=VARCHAR},
      </if>
      <if test="loginName != null">
        #{loginName,jdbcType=VARCHAR},
      </if>
      <if test="operationTime != null">
        #{operationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="pushType != null">
        #{pushType,jdbcType=TINYINT},
      </if>
      <if test="pushStatus != null">
        #{pushStatus,jdbcType=TINYINT},
      </if>
      <if test="content != null">
        #{content,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.BusinessPushOperationLogExample" resultType="java.lang.Long">
    select count(*) from business_push_operation_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update business_push_operation_log
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.orderApplyNo != null">
        order_apply_no = #{row.orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="row.loginCode != null">
        login_code = #{row.loginCode,jdbcType=VARCHAR},
      </if>
      <if test="row.loginName != null">
        login_name = #{row.loginName,jdbcType=VARCHAR},
      </if>
      <if test="row.operationTime != null">
        operation_time = #{row.operationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.pushType != null">
        push_type = #{row.pushType,jdbcType=TINYINT},
      </if>
      <if test="row.pushStatus != null">
        push_status = #{row.pushStatus,jdbcType=TINYINT},
      </if>
      <if test="row.content != null">
        content = #{row.content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update business_push_operation_log
    set id = #{row.id,jdbcType=BIGINT},
      order_apply_no = #{row.orderApplyNo,jdbcType=VARCHAR},
      login_code = #{row.loginCode,jdbcType=VARCHAR},
      login_name = #{row.loginName,jdbcType=VARCHAR},
      operation_time = #{row.operationTime,jdbcType=TIMESTAMP},
      push_type = #{row.pushType,jdbcType=TINYINT},
      push_status = #{row.pushStatus,jdbcType=TINYINT},
      content = #{row.content,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update business_push_operation_log
    set id = #{row.id,jdbcType=BIGINT},
      order_apply_no = #{row.orderApplyNo,jdbcType=VARCHAR},
      login_code = #{row.loginCode,jdbcType=VARCHAR},
      login_name = #{row.loginName,jdbcType=VARCHAR},
      operation_time = #{row.operationTime,jdbcType=TIMESTAMP},
      push_type = #{row.pushType,jdbcType=TINYINT},
      push_status = #{row.pushStatus,jdbcType=TINYINT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.BusinessPushOperationLog">
    update business_push_operation_log
    <set>
      <if test="orderApplyNo != null">
        order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="loginCode != null">
        login_code = #{loginCode,jdbcType=VARCHAR},
      </if>
      <if test="loginName != null">
        login_name = #{loginName,jdbcType=VARCHAR},
      </if>
      <if test="operationTime != null">
        operation_time = #{operationTime,jdbcType=TIMESTAMP},
      </if>
      <if test="pushType != null">
        push_type = #{pushType,jdbcType=TINYINT},
      </if>
      <if test="pushStatus != null">
        push_status = #{pushStatus,jdbcType=TINYINT},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.izu.order.entity.mrcar.BusinessPushOperationLog">
    update business_push_operation_log
    set order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      login_code = #{loginCode,jdbcType=VARCHAR},
      login_name = #{loginName,jdbcType=VARCHAR},
      operation_time = #{operationTime,jdbcType=TIMESTAMP},
      push_type = #{pushType,jdbcType=TINYINT},
      push_status = #{pushStatus,jdbcType=TINYINT},
      content = #{content,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.BusinessPushOperationLog">
    update business_push_operation_log
    set order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      login_code = #{loginCode,jdbcType=VARCHAR},
      login_name = #{loginName,jdbcType=VARCHAR},
      operation_time = #{operationTime,jdbcType=TIMESTAMP},
      push_type = #{pushType,jdbcType=TINYINT},
      push_status = #{pushStatus,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>