<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.FileInfoMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.FileInfo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="file_type" jdbcType="INTEGER" property="fileType" />
    <result column="relation_code" jdbcType="VARCHAR" property="relationCode" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="file_url" jdbcType="VARCHAR" property="fileUrl" />
    <result column="valid" jdbcType="BIT" property="valid" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, file_type, relation_code, file_name, file_url, valid, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.FileInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from file_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from file_info
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from file_info
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.order.entity.mrcar.FileInfoExample">
    delete from file_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.FileInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into file_info (file_type, relation_code, file_name, 
      file_url, valid, create_time, 
      update_time)
    values (#{fileType,jdbcType=INTEGER}, #{relationCode,jdbcType=VARCHAR}, #{fileName,jdbcType=VARCHAR}, 
      #{fileUrl,jdbcType=VARCHAR}, #{valid,jdbcType=BIT}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.FileInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into file_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="fileType != null">
        file_type,
      </if>
      <if test="relationCode != null">
        relation_code,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="fileUrl != null">
        file_url,
      </if>
      <if test="valid != null">
        valid,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="fileType != null">
        #{fileType,jdbcType=INTEGER},
      </if>
      <if test="relationCode != null">
        #{relationCode,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileUrl != null">
        #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.FileInfoExample" resultType="java.lang.Long">
    select count(*) from file_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update file_info
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.fileType != null">
        file_type = #{record.fileType,jdbcType=INTEGER},
      </if>
      <if test="record.relationCode != null">
        relation_code = #{record.relationCode,jdbcType=VARCHAR},
      </if>
      <if test="record.fileName != null">
        file_name = #{record.fileName,jdbcType=VARCHAR},
      </if>
      <if test="record.fileUrl != null">
        file_url = #{record.fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.valid != null">
        valid = #{record.valid,jdbcType=BIT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update file_info
    set id = #{record.id,jdbcType=INTEGER},
      file_type = #{record.fileType,jdbcType=INTEGER},
      relation_code = #{record.relationCode,jdbcType=VARCHAR},
      file_name = #{record.fileName,jdbcType=VARCHAR},
      file_url = #{record.fileUrl,jdbcType=VARCHAR},
      valid = #{record.valid,jdbcType=BIT},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.FileInfo">
    update file_info
    <set>
      <if test="fileType != null">
        file_type = #{fileType,jdbcType=INTEGER},
      </if>
      <if test="relationCode != null">
        relation_code = #{relationCode,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileUrl != null">
        file_url = #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.FileInfo">
    update file_info
    set file_type = #{fileType,jdbcType=INTEGER},
      relation_code = #{relationCode,jdbcType=VARCHAR},
      file_name = #{fileName,jdbcType=VARCHAR},
      file_url = #{fileUrl,jdbcType=VARCHAR},
      valid = #{valid,jdbcType=BIT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>