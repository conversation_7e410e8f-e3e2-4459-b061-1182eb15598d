<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.GovCarIncomePushProcessMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.GovCarIncomePushProcess">
    <id column="push_id" jdbcType="VARCHAR" property="pushId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="push_status" jdbcType="TINYINT" property="pushStatus" />
    <result column="push_count" jdbcType="TINYINT" property="pushCount" />
    <result column="bill_type" jdbcType="TINYINT" property="billType" />
    <result column="bill_status" jdbcType="TINYINT" property="billStatus" />
    <result column="expect_push_time" jdbcType="TIMESTAMP" property="expectPushTime" />
    <result column="actual_push_time" jdbcType="TIMESTAMP" property="actualPushTime" />
    <result column="valid" jdbcType="BIT" property="valid" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.izu.order.entity.mrcar.GovCarIncomePushProcess">
    <result column="push_data" jdbcType="LONGVARCHAR" property="pushData" />
  </resultMap>
  <sql id="Base_Column_List">
    push_id, create_time, update_time, order_no, push_status, push_count, bill_type,
    bill_status, expect_push_time, actual_push_time, valid
  </sql>
  <sql id="Blob_Column_List">
    push_data
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from gov_car_income_push_process
    where push_id = #{pushId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from gov_car_income_push_process
    where push_id = #{pushId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.GovCarIncomePushProcess">
    insert into gov_car_income_push_process (push_id, create_time, update_time,
      order_no, push_status, push_count,
      bill_type, bill_status, expect_push_time,
      actual_push_time, valid, push_data
      )
    values (#{pushId,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
      #{orderNo,jdbcType=VARCHAR}, #{pushStatus,jdbcType=TINYINT}, #{pushCount,jdbcType=TINYINT},
      #{billType,jdbcType=TINYINT}, #{billStatus,jdbcType=TINYINT}, #{expectPushTime,jdbcType=TIMESTAMP},
      #{actualPushTime,jdbcType=TIMESTAMP}, #{valid,jdbcType=BIT}, #{pushData}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.GovCarIncomePushProcess">
    insert into gov_car_income_push_process
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pushId != null">
        push_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="pushStatus != null">
        push_status,
      </if>
      <if test="pushCount != null">
        push_count,
      </if>
      <if test="billType != null">
        bill_type,
      </if>
      <if test="billStatus != null">
        bill_status,
      </if>
      <if test="expectPushTime != null">
        expect_push_time,
      </if>
      <if test="actualPushTime != null">
        actual_push_time,
      </if>
      <if test="valid != null">
        valid,
      </if>
      <if test="pushData != null">
        push_data,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pushId != null">
        #{pushId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="pushStatus != null">
        #{pushStatus,jdbcType=TINYINT},
      </if>
      <if test="pushCount != null">
        #{pushCount,jdbcType=TINYINT},
      </if>
      <if test="billType != null">
        #{billType,jdbcType=TINYINT},
      </if>
      <if test="billStatus != null">
        #{billStatus,jdbcType=TINYINT},
      </if>
      <if test="expectPushTime != null">
        #{expectPushTime,jdbcType=TIMESTAMP},
      </if>
      <if test="actualPushTime != null">
        #{actualPushTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        #{valid,jdbcType=BIT},
      </if>
      <if test="pushData != null">
        #{pushData},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.GovCarIncomePushProcess">
    update gov_car_income_push_process
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="pushStatus != null">
        push_status = #{pushStatus,jdbcType=TINYINT},
      </if>
      <if test="pushCount != null">
        push_count = #{pushCount,jdbcType=TINYINT},
      </if>
      <if test="billType != null">
        bill_type = #{billType,jdbcType=TINYINT},
      </if>
      <if test="billStatus != null">
        bill_status = #{billStatus,jdbcType=TINYINT},
      </if>
      <if test="expectPushTime != null">
        expect_push_time = #{expectPushTime,jdbcType=TIMESTAMP},
      </if>
      <if test="actualPushTime != null">
        actual_push_time = #{actualPushTime,jdbcType=TIMESTAMP},
      </if>
      <if test="valid != null">
        valid = #{valid,jdbcType=BIT},
      </if>
      <if test="pushData != null">
        push_data = #{pushData},
      </if>
    </set>
    where push_id = #{pushId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.izu.order.entity.mrcar.GovCarIncomePushProcess">
    update gov_car_income_push_process
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      order_no = #{orderNo,jdbcType=VARCHAR},
      push_status = #{pushStatus,jdbcType=TINYINT},
      push_count = #{pushCount,jdbcType=TINYINT},
      bill_type = #{billType,jdbcType=TINYINT},
      bill_status = #{billStatus,jdbcType=TINYINT},
      expect_push_time = #{expectPushTime,jdbcType=TIMESTAMP},
      actual_push_time = #{actualPushTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=BIT},
      push_data = #{pushData}
    where push_id = #{pushId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.GovCarIncomePushProcess">
    update gov_car_income_push_process
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      order_no = #{orderNo,jdbcType=VARCHAR},
      push_status = #{pushStatus,jdbcType=TINYINT},
      push_count = #{pushCount,jdbcType=TINYINT},
      bill_type = #{billType,jdbcType=TINYINT},
      bill_status = #{billStatus,jdbcType=TINYINT},
      expect_push_time = #{expectPushTime,jdbcType=TIMESTAMP},
      actual_push_time = #{actualPushTime,jdbcType=TIMESTAMP},
      valid = #{valid,jdbcType=BIT}
    where push_id = #{pushId,jdbcType=VARCHAR}
  </update>
</mapper>
