<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.CommonApplyMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.CommonApply">
    <id column="apply_id" jdbcType="BIGINT" property="applyId" />
    <result column="apply_type" jdbcType="INTEGER" property="applyType" />
    <result column="apply_business_code" jdbcType="VARCHAR" property="applyBusinessCode" />
    <result column="apply_suggestion" jdbcType="VARCHAR" property="applySuggestion" />
    <result column="approver_id" jdbcType="INTEGER" property="approverId" />
    <result column="approver_name" jdbcType="VARCHAR" property="approverName" />
    <result column="apply_time" jdbcType="TIMESTAMP" property="applyTime" />
    <result column="apply_status" jdbcType="INTEGER" property="applyStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    apply_id, apply_type, apply_business_code, apply_suggestion, approver_id, approver_name, 
    apply_time, apply_status, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.CommonApplyExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from common_apply
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from common_apply
    where apply_id = #{applyId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from common_apply
    where apply_id = #{applyId,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.order.entity.mrcar.CommonApplyExample">
    delete from common_apply
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.CommonApply">
    <selectKey keyProperty="applyId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into common_apply (apply_type, apply_business_code, apply_suggestion, 
      approver_id, approver_name, apply_time, 
      apply_status, create_time, update_time
      )
    values (#{applyType,jdbcType=INTEGER}, #{applyBusinessCode,jdbcType=VARCHAR}, #{applySuggestion,jdbcType=VARCHAR}, 
      #{approverId,jdbcType=INTEGER}, #{approverName,jdbcType=VARCHAR}, #{applyTime,jdbcType=TIMESTAMP}, 
      #{applyStatus,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.CommonApply">
    <selectKey keyProperty="applyId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into common_apply
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="applyType != null">
        apply_type,
      </if>
      <if test="applyBusinessCode != null">
        apply_business_code,
      </if>
      <if test="applySuggestion != null">
        apply_suggestion,
      </if>
      <if test="approverId != null">
        approver_id,
      </if>
      <if test="approverName != null">
        approver_name,
      </if>
      <if test="applyTime != null">
        apply_time,
      </if>
      <if test="applyStatus != null">
        apply_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="applyType != null">
        #{applyType,jdbcType=INTEGER},
      </if>
      <if test="applyBusinessCode != null">
        #{applyBusinessCode,jdbcType=VARCHAR},
      </if>
      <if test="applySuggestion != null">
        #{applySuggestion,jdbcType=VARCHAR},
      </if>
      <if test="approverId != null">
        #{approverId,jdbcType=INTEGER},
      </if>
      <if test="approverName != null">
        #{approverName,jdbcType=VARCHAR},
      </if>
      <if test="applyTime != null">
        #{applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="applyStatus != null">
        #{applyStatus,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.CommonApplyExample" resultType="java.lang.Long">
    select count(*) from common_apply
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update common_apply
    <set>
      <if test="row.applyId != null">
        apply_id = #{row.applyId,jdbcType=BIGINT},
      </if>
      <if test="row.applyType != null">
        apply_type = #{row.applyType,jdbcType=INTEGER},
      </if>
      <if test="row.applyBusinessCode != null">
        apply_business_code = #{row.applyBusinessCode,jdbcType=VARCHAR},
      </if>
      <if test="row.applySuggestion != null">
        apply_suggestion = #{row.applySuggestion,jdbcType=VARCHAR},
      </if>
      <if test="row.approverId != null">
        approver_id = #{row.approverId,jdbcType=INTEGER},
      </if>
      <if test="row.approverName != null">
        approver_name = #{row.approverName,jdbcType=VARCHAR},
      </if>
      <if test="row.applyTime != null">
        apply_time = #{row.applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.applyStatus != null">
        apply_status = #{row.applyStatus,jdbcType=INTEGER},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update common_apply
    set apply_id = #{row.applyId,jdbcType=BIGINT},
      apply_type = #{row.applyType,jdbcType=INTEGER},
      apply_business_code = #{row.applyBusinessCode,jdbcType=VARCHAR},
      apply_suggestion = #{row.applySuggestion,jdbcType=VARCHAR},
      approver_id = #{row.approverId,jdbcType=INTEGER},
      approver_name = #{row.approverName,jdbcType=VARCHAR},
      apply_time = #{row.applyTime,jdbcType=TIMESTAMP},
      apply_status = #{row.applyStatus,jdbcType=INTEGER},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.CommonApply">
    update common_apply
    <set>
      <if test="applyType != null">
        apply_type = #{applyType,jdbcType=INTEGER},
      </if>
      <if test="applyBusinessCode != null">
        apply_business_code = #{applyBusinessCode,jdbcType=VARCHAR},
      </if>
      <if test="applySuggestion != null">
        apply_suggestion = #{applySuggestion,jdbcType=VARCHAR},
      </if>
      <if test="approverId != null">
        approver_id = #{approverId,jdbcType=INTEGER},
      </if>
      <if test="approverName != null">
        approver_name = #{approverName,jdbcType=VARCHAR},
      </if>
      <if test="applyTime != null">
        apply_time = #{applyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="applyStatus != null">
        apply_status = #{applyStatus,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where apply_id = #{applyId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.CommonApply">
    update common_apply
    set apply_type = #{applyType,jdbcType=INTEGER},
      apply_business_code = #{applyBusinessCode,jdbcType=VARCHAR},
      apply_suggestion = #{applySuggestion,jdbcType=VARCHAR},
      approver_id = #{approverId,jdbcType=INTEGER},
      approver_name = #{approverName,jdbcType=VARCHAR},
      apply_time = #{applyTime,jdbcType=TIMESTAMP},
      apply_status = #{applyStatus,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where apply_id = #{applyId,jdbcType=BIGINT}
  </update>
</mapper>