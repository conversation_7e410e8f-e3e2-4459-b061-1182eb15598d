<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.ReturnVehicleRecordMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.ReturnVehicleRecord">
    <id column="return_vehicle_record_id" jdbcType="INTEGER" property="returnVehicleRecordId" />
    <result column="customer_order_num" jdbcType="VARCHAR" property="customerOrderNum" />
    <result column="supplier_order_num" jdbcType="VARCHAR" property="supplierOrderNum" />
    <result column="supplier_order_type" jdbcType="INTEGER" property="supplierOrderType" />
    <result column="audit_record_num" jdbcType="VARCHAR" property="auditRecordNum" />
    <result column="return_type" jdbcType="INTEGER" property="returnType" />
    <result column="return_time" jdbcType="TIMESTAMP" property="returnTime" />
    <result column="create_id" jdbcType="INTEGER" property="createId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    return_vehicle_record_id, customer_order_num, supplier_order_num, supplier_order_type, 
    audit_record_num, return_type, return_time, create_id, create_name, create_time
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.ReturnVehicleRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from so_return_vehicle_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from so_return_vehicle_record
    where return_vehicle_record_id = #{returnVehicleRecordId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from so_return_vehicle_record
    where return_vehicle_record_id = #{returnVehicleRecordId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.ReturnVehicleRecord">
    <selectKey keyProperty="returnVehicleRecordId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into so_return_vehicle_record (customer_order_num, supplier_order_num, 
      supplier_order_type, audit_record_num, return_type, 
      return_time, create_id, create_name, 
      create_time)
    values (#{customerOrderNum,jdbcType=VARCHAR}, #{supplierOrderNum,jdbcType=VARCHAR}, 
      #{supplierOrderType,jdbcType=INTEGER}, #{auditRecordNum,jdbcType=VARCHAR}, #{returnType,jdbcType=INTEGER}, 
      #{returnTime,jdbcType=TIMESTAMP}, #{createId,jdbcType=INTEGER}, #{createName,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.ReturnVehicleRecord">
    <selectKey keyProperty="returnVehicleRecordId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into so_return_vehicle_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="customerOrderNum != null">
        customer_order_num,
      </if>
      <if test="supplierOrderNum != null">
        supplier_order_num,
      </if>
      <if test="supplierOrderType != null">
        supplier_order_type,
      </if>
      <if test="auditRecordNum != null">
        audit_record_num,
      </if>
      <if test="returnType != null">
        return_type,
      </if>
      <if test="returnTime != null">
        return_time,
      </if>
      <if test="createId != null">
        create_id,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="customerOrderNum != null">
        #{customerOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="supplierOrderNum != null">
        #{supplierOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="supplierOrderType != null">
        #{supplierOrderType,jdbcType=INTEGER},
      </if>
      <if test="auditRecordNum != null">
        #{auditRecordNum,jdbcType=VARCHAR},
      </if>
      <if test="returnType != null">
        #{returnType,jdbcType=INTEGER},
      </if>
      <if test="returnTime != null">
        #{returnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createId != null">
        #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.ReturnVehicleRecordExample" resultType="java.lang.Long">
    select count(*) from so_return_vehicle_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.ReturnVehicleRecord">
    update so_return_vehicle_record
    <set>
      <if test="customerOrderNum != null">
        customer_order_num = #{customerOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="supplierOrderNum != null">
        supplier_order_num = #{supplierOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="supplierOrderType != null">
        supplier_order_type = #{supplierOrderType,jdbcType=INTEGER},
      </if>
      <if test="auditRecordNum != null">
        audit_record_num = #{auditRecordNum,jdbcType=VARCHAR},
      </if>
      <if test="returnType != null">
        return_type = #{returnType,jdbcType=INTEGER},
      </if>
      <if test="returnTime != null">
        return_time = #{returnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createId != null">
        create_id = #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where return_vehicle_record_id = #{returnVehicleRecordId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.ReturnVehicleRecord">
    update so_return_vehicle_record
    set customer_order_num = #{customerOrderNum,jdbcType=VARCHAR},
      supplier_order_num = #{supplierOrderNum,jdbcType=VARCHAR},
      supplier_order_type = #{supplierOrderType,jdbcType=INTEGER},
      audit_record_num = #{auditRecordNum,jdbcType=VARCHAR},
      return_type = #{returnType,jdbcType=INTEGER},
      return_time = #{returnTime,jdbcType=TIMESTAMP},
      create_id = #{createId,jdbcType=INTEGER},
      create_name = #{createName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where return_vehicle_record_id = #{returnVehicleRecordId,jdbcType=INTEGER}
  </update>
</mapper>