<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.BillAttachMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.BillAttach">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="bill_attach_no" jdbcType="VARCHAR" property="billAttachNo" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="order_apply_no" jdbcType="VARCHAR" property="orderApplyNo" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="bill_no" jdbcType="VARCHAR" property="billNo" />
    <result column="attach_code" jdbcType="TINYINT" property="attachCode" />
    <result column="fee_amount" jdbcType="DECIMAL" property="feeAmount" />
    <result column="attach_status" jdbcType="TINYINT" property="attachStatus" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, bill_attach_no, update_time, order_apply_no, order_no, bill_no,
    attach_code, fee_amount, attach_status, remarks
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from bill_attach
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from bill_attach
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.BillAttach">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into bill_attach (create_time, bill_attach_no, update_time,
    order_apply_no, order_no, bill_no,
    attach_code, fee_amount, attach_status,
    remarks)
    values (#{createTime,jdbcType=TIMESTAMP}, #{billAttachNo,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP},
    #{orderApplyNo,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, #{billNo,jdbcType=VARCHAR},
    #{attachCode,jdbcType=TINYINT}, #{feeAmount,jdbcType=DECIMAL}, #{attachStatus,jdbcType=TINYINT},
    #{remarks,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.BillAttach">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into bill_attach
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="billAttachNo != null">
        bill_attach_no,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="orderApplyNo != null">
        order_apply_no,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="billNo != null">
        bill_no,
      </if>
      <if test="attachCode != null">
        attach_code,
      </if>
      <if test="feeAmount != null">
        fee_amount,
      </if>
      <if test="attachStatus != null">
        attach_status,
      </if>
      <if test="remarks != null">
        remarks,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="billAttachNo != null">
        #{billAttachNo,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderApplyNo != null">
        #{orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="billNo != null">
        #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="attachCode != null">
        #{attachCode,jdbcType=TINYINT},
      </if>
      <if test="feeAmount != null">
        #{feeAmount,jdbcType=DECIMAL},
      </if>
      <if test="attachStatus != null">
        #{attachStatus,jdbcType=TINYINT},
      </if>
      <if test="remarks != null">
        #{remarks,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.BillAttach">
    update bill_attach
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="billAttachNo != null">
        bill_attach_no = #{billAttachNo,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderApplyNo != null">
        order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="billNo != null">
        bill_no = #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="attachCode != null">
        attach_code = #{attachCode,jdbcType=TINYINT},
      </if>
      <if test="feeAmount != null">
        fee_amount = #{feeAmount,jdbcType=DECIMAL},
      </if>
      <if test="attachStatus != null">
        attach_status = #{attachStatus,jdbcType=TINYINT},
      </if>
      <if test="remarks != null">
        remarks = #{remarks,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.BillAttach">
    update bill_attach
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      bill_attach_no = #{billAttachNo,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      bill_no = #{billNo,jdbcType=VARCHAR},
      attach_code = #{attachCode,jdbcType=TINYINT},
      fee_amount = #{feeAmount,jdbcType=DECIMAL},
      attach_status = #{attachStatus,jdbcType=TINYINT},
      remarks = #{remarks,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>