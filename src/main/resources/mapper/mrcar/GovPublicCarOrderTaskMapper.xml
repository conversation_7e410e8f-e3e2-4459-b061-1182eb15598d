<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.GovPublicCarOrderTaskMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.GovPublicCarOrderTask">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="task_type" jdbcType="INTEGER" property="taskType" />
    <result column="task_status" jdbcType="INTEGER" property="taskStatus" />
    <result column="execution_time" jdbcType="TIMESTAMP" property="executionTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_no, company_id, company_code, task_type, task_status, execution_time, create_time, 
    update_time
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.GovPublicCarOrderTaskExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from gov_public_car_order_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from gov_public_car_order_task
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from gov_public_car_order_task
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.order.entity.mrcar.GovPublicCarOrderTaskExample">
    delete from gov_public_car_order_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.GovPublicCarOrderTask">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gov_public_car_order_task (order_no, company_id, company_code, 
      task_type, task_status, execution_time, 
      create_time, update_time)
    values (#{orderNo,jdbcType=VARCHAR}, #{companyId,jdbcType=INTEGER}, #{companyCode,jdbcType=VARCHAR}, 
      #{taskType,jdbcType=INTEGER}, #{taskStatus,jdbcType=INTEGER}, #{executionTime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.GovPublicCarOrderTask">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gov_public_car_order_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="taskType != null">
        task_type,
      </if>
      <if test="taskStatus != null">
        task_status,
      </if>
      <if test="executionTime != null">
        execution_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="taskType != null">
        #{taskType,jdbcType=INTEGER},
      </if>
      <if test="taskStatus != null">
        #{taskStatus,jdbcType=INTEGER},
      </if>
      <if test="executionTime != null">
        #{executionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.GovPublicCarOrderTaskExample" resultType="java.lang.Long">
    select count(*) from gov_public_car_order_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update gov_public_car_order_task
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.orderNo != null">
        order_no = #{row.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="row.companyId != null">
        company_id = #{row.companyId,jdbcType=INTEGER},
      </if>
      <if test="row.companyCode != null">
        company_code = #{row.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="row.taskType != null">
        task_type = #{row.taskType,jdbcType=INTEGER},
      </if>
      <if test="row.taskStatus != null">
        task_status = #{row.taskStatus,jdbcType=INTEGER},
      </if>
      <if test="row.executionTime != null">
        execution_time = #{row.executionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update gov_public_car_order_task
    set id = #{row.id,jdbcType=BIGINT},
      order_no = #{row.orderNo,jdbcType=VARCHAR},
      company_id = #{row.companyId,jdbcType=INTEGER},
      company_code = #{row.companyCode,jdbcType=VARCHAR},
      task_type = #{row.taskType,jdbcType=INTEGER},
      task_status = #{row.taskStatus,jdbcType=INTEGER},
      execution_time = #{row.executionTime,jdbcType=TIMESTAMP},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.GovPublicCarOrderTask">
    update gov_public_car_order_task
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="taskType != null">
        task_type = #{taskType,jdbcType=INTEGER},
      </if>
      <if test="taskStatus != null">
        task_status = #{taskStatus,jdbcType=INTEGER},
      </if>
      <if test="executionTime != null">
        execution_time = #{executionTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.GovPublicCarOrderTask">
    update gov_public_car_order_task
    set order_no = #{orderNo,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=INTEGER},
      company_code = #{companyCode,jdbcType=VARCHAR},
      task_type = #{taskType,jdbcType=INTEGER},
      task_status = #{taskStatus,jdbcType=INTEGER},
      execution_time = #{executionTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>