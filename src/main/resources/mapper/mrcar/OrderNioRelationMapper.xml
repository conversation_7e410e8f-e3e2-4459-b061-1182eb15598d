<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.OrderNioRelationMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.OrderNioRelation">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="demand_order_num" jdbcType="VARCHAR" property="demandOrderNum" />
    <result column="agent_mobile" jdbcType="VARCHAR" property="agentMobile" />
    <result column="agent_real_name" jdbcType="VARCHAR" property="agentRealName" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="contact_type" jdbcType="VARCHAR" property="contactType" />
    <result column="out_side_order_no" jdbcType="VARCHAR" property="outSideOrderNo" />
    <result column="rent_city" jdbcType="VARCHAR" property="rentCity" />
    <result column="rent_time" jdbcType="VARCHAR" property="rentTime" />
    <result column="renter_id_no" jdbcType="VARCHAR" property="renterIdNo" />
    <result column="renter_memo" jdbcType="VARCHAR" property="renterMemo" />
    <result column="renter_mobile" jdbcType="VARCHAR" property="renterMobile" />
    <result column="renter_real_name" jdbcType="VARCHAR" property="renterRealName" />
    <result column="revert_time" jdbcType="VARCHAR" property="revertTime" />
    <result column="srv_get_addr" jdbcType="VARCHAR" property="srvGetAddr" />
    <result column="srv_get_lat" jdbcType="VARCHAR" property="srvGetLat" />
    <result column="srv_get_lon" jdbcType="VARCHAR" property="srvGetLon" />
    <result column="srv_return_addr" jdbcType="VARCHAR" property="srvReturnAddr" />
    <result column="srv_return_lat" jdbcType="VARCHAR" property="srvReturnLat" />
    <result column="srv_return_lon" jdbcType="VARCHAR" property="srvReturnLon" />
    <result column="steward_memo" jdbcType="VARCHAR" property="stewardMemo" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, demand_order_num, agent_mobile, agent_real_name, city_code, contact_type, out_side_order_no, 
    rent_city, rent_time, renter_id_no, renter_memo, renter_mobile, renter_real_name, 
    revert_time, srv_get_addr, srv_get_lat, srv_get_lon, srv_return_addr, srv_return_lat, 
    srv_return_lon, steward_memo, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from so_order_nio_relation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from so_order_nio_relation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.OrderNioRelation">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into so_order_nio_relation (demand_order_num, agent_mobile, agent_real_name, 
      city_code, contact_type, out_side_order_no, 
      rent_city, rent_time, renter_id_no, 
      renter_memo, renter_mobile, renter_real_name, 
      revert_time, srv_get_addr, srv_get_lat, 
      srv_get_lon, srv_return_addr, srv_return_lat, 
      srv_return_lon, steward_memo, create_time, 
      update_time)
    values (#{demandOrderNum,jdbcType=VARCHAR}, #{agentMobile,jdbcType=VARCHAR}, #{agentRealName,jdbcType=VARCHAR}, 
      #{cityCode,jdbcType=VARCHAR}, #{contactType,jdbcType=VARCHAR}, #{outSideOrderNo,jdbcType=VARCHAR}, 
      #{rentCity,jdbcType=VARCHAR}, #{rentTime,jdbcType=VARCHAR}, #{renterIdNo,jdbcType=VARCHAR}, 
      #{renterMemo,jdbcType=VARCHAR}, #{renterMobile,jdbcType=VARCHAR}, #{renterRealName,jdbcType=VARCHAR}, 
      #{revertTime,jdbcType=VARCHAR}, #{srvGetAddr,jdbcType=VARCHAR}, #{srvGetLat,jdbcType=VARCHAR}, 
      #{srvGetLon,jdbcType=VARCHAR}, #{srvReturnAddr,jdbcType=VARCHAR}, #{srvReturnLat,jdbcType=VARCHAR}, 
      #{srvReturnLon,jdbcType=VARCHAR}, #{stewardMemo,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.OrderNioRelation">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into so_order_nio_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="demandOrderNum != null">
        demand_order_num,
      </if>
      <if test="agentMobile != null">
        agent_mobile,
      </if>
      <if test="agentRealName != null">
        agent_real_name,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="contactType != null">
        contact_type,
      </if>
      <if test="outSideOrderNo != null">
        out_side_order_no,
      </if>
      <if test="rentCity != null">
        rent_city,
      </if>
      <if test="rentTime != null">
        rent_time,
      </if>
      <if test="renterIdNo != null">
        renter_id_no,
      </if>
      <if test="renterMemo != null">
        renter_memo,
      </if>
      <if test="renterMobile != null">
        renter_mobile,
      </if>
      <if test="renterRealName != null">
        renter_real_name,
      </if>
      <if test="revertTime != null">
        revert_time,
      </if>
      <if test="srvGetAddr != null">
        srv_get_addr,
      </if>
      <if test="srvGetLat != null">
        srv_get_lat,
      </if>
      <if test="srvGetLon != null">
        srv_get_lon,
      </if>
      <if test="srvReturnAddr != null">
        srv_return_addr,
      </if>
      <if test="srvReturnLat != null">
        srv_return_lat,
      </if>
      <if test="srvReturnLon != null">
        srv_return_lon,
      </if>
      <if test="stewardMemo != null">
        steward_memo,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="demandOrderNum != null">
        #{demandOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="agentMobile != null">
        #{agentMobile,jdbcType=VARCHAR},
      </if>
      <if test="agentRealName != null">
        #{agentRealName,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="contactType != null">
        #{contactType,jdbcType=VARCHAR},
      </if>
      <if test="outSideOrderNo != null">
        #{outSideOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="rentCity != null">
        #{rentCity,jdbcType=VARCHAR},
      </if>
      <if test="rentTime != null">
        #{rentTime,jdbcType=VARCHAR},
      </if>
      <if test="renterIdNo != null">
        #{renterIdNo,jdbcType=VARCHAR},
      </if>
      <if test="renterMemo != null">
        #{renterMemo,jdbcType=VARCHAR},
      </if>
      <if test="renterMobile != null">
        #{renterMobile,jdbcType=VARCHAR},
      </if>
      <if test="renterRealName != null">
        #{renterRealName,jdbcType=VARCHAR},
      </if>
      <if test="revertTime != null">
        #{revertTime,jdbcType=VARCHAR},
      </if>
      <if test="srvGetAddr != null">
        #{srvGetAddr,jdbcType=VARCHAR},
      </if>
      <if test="srvGetLat != null">
        #{srvGetLat,jdbcType=VARCHAR},
      </if>
      <if test="srvGetLon != null">
        #{srvGetLon,jdbcType=VARCHAR},
      </if>
      <if test="srvReturnAddr != null">
        #{srvReturnAddr,jdbcType=VARCHAR},
      </if>
      <if test="srvReturnLat != null">
        #{srvReturnLat,jdbcType=VARCHAR},
      </if>
      <if test="srvReturnLon != null">
        #{srvReturnLon,jdbcType=VARCHAR},
      </if>
      <if test="stewardMemo != null">
        #{stewardMemo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.OrderNioRelation">
    update so_order_nio_relation
    <set>
      <if test="demandOrderNum != null">
        demand_order_num = #{demandOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="agentMobile != null">
        agent_mobile = #{agentMobile,jdbcType=VARCHAR},
      </if>
      <if test="agentRealName != null">
        agent_real_name = #{agentRealName,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        city_code = #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="contactType != null">
        contact_type = #{contactType,jdbcType=VARCHAR},
      </if>
      <if test="outSideOrderNo != null">
        out_side_order_no = #{outSideOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="rentCity != null">
        rent_city = #{rentCity,jdbcType=VARCHAR},
      </if>
      <if test="rentTime != null">
        rent_time = #{rentTime,jdbcType=VARCHAR},
      </if>
      <if test="renterIdNo != null">
        renter_id_no = #{renterIdNo,jdbcType=VARCHAR},
      </if>
      <if test="renterMemo != null">
        renter_memo = #{renterMemo,jdbcType=VARCHAR},
      </if>
      <if test="renterMobile != null">
        renter_mobile = #{renterMobile,jdbcType=VARCHAR},
      </if>
      <if test="renterRealName != null">
        renter_real_name = #{renterRealName,jdbcType=VARCHAR},
      </if>
      <if test="revertTime != null">
        revert_time = #{revertTime,jdbcType=VARCHAR},
      </if>
      <if test="srvGetAddr != null">
        srv_get_addr = #{srvGetAddr,jdbcType=VARCHAR},
      </if>
      <if test="srvGetLat != null">
        srv_get_lat = #{srvGetLat,jdbcType=VARCHAR},
      </if>
      <if test="srvGetLon != null">
        srv_get_lon = #{srvGetLon,jdbcType=VARCHAR},
      </if>
      <if test="srvReturnAddr != null">
        srv_return_addr = #{srvReturnAddr,jdbcType=VARCHAR},
      </if>
      <if test="srvReturnLat != null">
        srv_return_lat = #{srvReturnLat,jdbcType=VARCHAR},
      </if>
      <if test="srvReturnLon != null">
        srv_return_lon = #{srvReturnLon,jdbcType=VARCHAR},
      </if>
      <if test="stewardMemo != null">
        steward_memo = #{stewardMemo,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.OrderNioRelation">
    update so_order_nio_relation
    set demand_order_num = #{demandOrderNum,jdbcType=VARCHAR},
      agent_mobile = #{agentMobile,jdbcType=VARCHAR},
      agent_real_name = #{agentRealName,jdbcType=VARCHAR},
      city_code = #{cityCode,jdbcType=VARCHAR},
      contact_type = #{contactType,jdbcType=VARCHAR},
      out_side_order_no = #{outSideOrderNo,jdbcType=VARCHAR},
      rent_city = #{rentCity,jdbcType=VARCHAR},
      rent_time = #{rentTime,jdbcType=VARCHAR},
      renter_id_no = #{renterIdNo,jdbcType=VARCHAR},
      renter_memo = #{renterMemo,jdbcType=VARCHAR},
      renter_mobile = #{renterMobile,jdbcType=VARCHAR},
      renter_real_name = #{renterRealName,jdbcType=VARCHAR},
      revert_time = #{revertTime,jdbcType=VARCHAR},
      srv_get_addr = #{srvGetAddr,jdbcType=VARCHAR},
      srv_get_lat = #{srvGetLat,jdbcType=VARCHAR},
      srv_get_lon = #{srvGetLon,jdbcType=VARCHAR},
      srv_return_addr = #{srvReturnAddr,jdbcType=VARCHAR},
      srv_return_lat = #{srvReturnLat,jdbcType=VARCHAR},
      srv_return_lon = #{srvReturnLon,jdbcType=VARCHAR},
      steward_memo = #{stewardMemo,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>