<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.AuditRecordMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.AuditRecord">
    <id column="audit_record_id" jdbcType="INTEGER" property="auditRecordId" />
    <result column="audit_record_num" jdbcType="VARCHAR" property="auditRecordNum" />
    <result column="audit_type" jdbcType="INTEGER" property="auditType" />
    <result column="demand_order_num" jdbcType="VARCHAR" property="demandOrderNum" />
    <result column="customer_order_num" jdbcType="VARCHAR" property="customerOrderNum" />
    <result column="supplier_order_num" jdbcType="VARCHAR" property="supplierOrderNum" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="supplier_type" jdbcType="INTEGER" property="supplierType" />
    <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode" />
    <result column="audit_status" jdbcType="INTEGER" property="auditStatus" />
    <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime" />
    <result column="order_owner_dept_code" jdbcType="VARCHAR" property="orderOwnerDeptCode" />
    <result column="order_owner_dept_name" jdbcType="VARCHAR" property="orderOwnerDeptName" />
    <result column="order_owner_company_name" jdbcType="VARCHAR" property="orderOwnerCompanyName" />
    <result column="order_owner_company_id" jdbcType="INTEGER" property="orderOwnerCompanyId" />
    <result column="reject_reason" jdbcType="VARCHAR" property="rejectReason" />
    <result column="create_id" jdbcType="INTEGER" property="createId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_id" jdbcType="INTEGER" property="updateId" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    audit_record_id, audit_record_num, audit_type, demand_order_num, customer_order_num, 
    supplier_order_num, supplier_name, supplier_type, supplier_code, audit_status, audit_time, 
    order_owner_dept_code, order_owner_dept_name, order_owner_company_name, order_owner_company_id, 
    reject_reason, create_id, create_name, create_time, update_id, update_name, update_time
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.AuditRecordExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from so_audit_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from so_audit_record
    where audit_record_id = #{auditRecordId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from so_audit_record
    where audit_record_id = #{auditRecordId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.AuditRecord">
    <selectKey keyProperty="auditRecordId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into so_audit_record (audit_record_num, audit_type, demand_order_num, 
      customer_order_num, supplier_order_num, supplier_name, 
      supplier_type, supplier_code, audit_status, 
      audit_time, order_owner_dept_code, order_owner_dept_name, 
      order_owner_company_name, order_owner_company_id, 
      reject_reason, create_id, create_name, 
      create_time, update_id, update_name, 
      update_time)
    values (#{auditRecordNum,jdbcType=VARCHAR}, #{auditType,jdbcType=INTEGER}, #{demandOrderNum,jdbcType=VARCHAR}, 
      #{customerOrderNum,jdbcType=VARCHAR}, #{supplierOrderNum,jdbcType=VARCHAR}, #{supplierName,jdbcType=VARCHAR}, 
      #{supplierType,jdbcType=INTEGER}, #{supplierCode,jdbcType=VARCHAR}, #{auditStatus,jdbcType=INTEGER}, 
      #{auditTime,jdbcType=TIMESTAMP}, #{orderOwnerDeptCode,jdbcType=VARCHAR}, #{orderOwnerDeptName,jdbcType=VARCHAR}, 
      #{orderOwnerCompanyName,jdbcType=VARCHAR}, #{orderOwnerCompanyId,jdbcType=INTEGER}, 
      #{rejectReason,jdbcType=VARCHAR}, #{createId,jdbcType=INTEGER}, #{createName,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateId,jdbcType=INTEGER}, #{updateName,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.AuditRecord">
    <selectKey keyProperty="auditRecordId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into so_audit_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="auditRecordNum != null">
        audit_record_num,
      </if>
      <if test="auditType != null">
        audit_type,
      </if>
      <if test="demandOrderNum != null">
        demand_order_num,
      </if>
      <if test="customerOrderNum != null">
        customer_order_num,
      </if>
      <if test="supplierOrderNum != null">
        supplier_order_num,
      </if>
      <if test="supplierName != null">
        supplier_name,
      </if>
      <if test="supplierType != null">
        supplier_type,
      </if>
      <if test="supplierCode != null">
        supplier_code,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="auditTime != null">
        audit_time,
      </if>
      <if test="orderOwnerDeptCode != null">
        order_owner_dept_code,
      </if>
      <if test="orderOwnerDeptName != null">
        order_owner_dept_name,
      </if>
      <if test="orderOwnerCompanyName != null">
        order_owner_company_name,
      </if>
      <if test="orderOwnerCompanyId != null">
        order_owner_company_id,
      </if>
      <if test="rejectReason != null">
        reject_reason,
      </if>
      <if test="createId != null">
        create_id,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateId != null">
        update_id,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="auditRecordNum != null">
        #{auditRecordNum,jdbcType=VARCHAR},
      </if>
      <if test="auditType != null">
        #{auditType,jdbcType=INTEGER},
      </if>
      <if test="demandOrderNum != null">
        #{demandOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="customerOrderNum != null">
        #{customerOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="supplierOrderNum != null">
        #{supplierOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="supplierType != null">
        #{supplierType,jdbcType=INTEGER},
      </if>
      <if test="supplierCode != null">
        #{supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="auditTime != null">
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderOwnerDeptCode != null">
        #{orderOwnerDeptCode,jdbcType=VARCHAR},
      </if>
      <if test="orderOwnerDeptName != null">
        #{orderOwnerDeptName,jdbcType=VARCHAR},
      </if>
      <if test="orderOwnerCompanyName != null">
        #{orderOwnerCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="orderOwnerCompanyId != null">
        #{orderOwnerCompanyId,jdbcType=INTEGER},
      </if>
      <if test="rejectReason != null">
        #{rejectReason,jdbcType=VARCHAR},
      </if>
      <if test="createId != null">
        #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.AuditRecordExample" resultType="java.lang.Long">
    select count(*) from so_audit_record
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.AuditRecord">
    update so_audit_record
    <set>
      <if test="auditRecordNum != null">
        audit_record_num = #{auditRecordNum,jdbcType=VARCHAR},
      </if>
      <if test="auditType != null">
        audit_type = #{auditType,jdbcType=INTEGER},
      </if>
      <if test="demandOrderNum != null">
        demand_order_num = #{demandOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="customerOrderNum != null">
        customer_order_num = #{customerOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="supplierOrderNum != null">
        supplier_order_num = #{supplierOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        supplier_name = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="supplierType != null">
        supplier_type = #{supplierType,jdbcType=INTEGER},
      </if>
      <if test="supplierCode != null">
        supplier_code = #{supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="auditTime != null">
        audit_time = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderOwnerDeptCode != null">
        order_owner_dept_code = #{orderOwnerDeptCode,jdbcType=VARCHAR},
      </if>
      <if test="orderOwnerDeptName != null">
        order_owner_dept_name = #{orderOwnerDeptName,jdbcType=VARCHAR},
      </if>
      <if test="orderOwnerCompanyName != null">
        order_owner_company_name = #{orderOwnerCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="orderOwnerCompanyId != null">
        order_owner_company_id = #{orderOwnerCompanyId,jdbcType=INTEGER},
      </if>
      <if test="rejectReason != null">
        reject_reason = #{rejectReason,jdbcType=VARCHAR},
      </if>
      <if test="createId != null">
        create_id = #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        update_id = #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where audit_record_id = #{auditRecordId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.AuditRecord">
    update so_audit_record
    set audit_record_num = #{auditRecordNum,jdbcType=VARCHAR},
      audit_type = #{auditType,jdbcType=INTEGER},
      demand_order_num = #{demandOrderNum,jdbcType=VARCHAR},
      customer_order_num = #{customerOrderNum,jdbcType=VARCHAR},
      supplier_order_num = #{supplierOrderNum,jdbcType=VARCHAR},
      supplier_name = #{supplierName,jdbcType=VARCHAR},
      supplier_type = #{supplierType,jdbcType=INTEGER},
      supplier_code = #{supplierCode,jdbcType=VARCHAR},
      audit_status = #{auditStatus,jdbcType=INTEGER},
      audit_time = #{auditTime,jdbcType=TIMESTAMP},
      order_owner_dept_code = #{orderOwnerDeptCode,jdbcType=VARCHAR},
      order_owner_dept_name = #{orderOwnerDeptName,jdbcType=VARCHAR},
      order_owner_company_name = #{orderOwnerCompanyName,jdbcType=VARCHAR},
      order_owner_company_id = #{orderOwnerCompanyId,jdbcType=INTEGER},
      reject_reason = #{rejectReason,jdbcType=VARCHAR},
      create_id = #{createId,jdbcType=INTEGER},
      create_name = #{createName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_id = #{updateId,jdbcType=INTEGER},
      update_name = #{updateName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where audit_record_id = #{auditRecordId,jdbcType=INTEGER}
  </update>
</mapper>