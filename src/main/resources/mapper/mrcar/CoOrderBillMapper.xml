<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.CoOrderBillMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.CoOrderBill">
    <id column="order_bill_id" jdbcType="INTEGER" property="orderBillId" />
    <result column="order_bill_no" jdbcType="VARCHAR" property="orderBillNo" />
    <result column="single_bill_no" jdbcType="VARCHAR" property="singleBillNo" />
    <result column="company_bill_no" jdbcType="VARCHAR" property="companyBillNo" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="third_order_no" jdbcType="VARCHAR" property="thirdOrderNo" />
    <result column="vehicle_license" jdbcType="VARCHAR" property="vehicleLicense" />
    <result column="vehicle_serial_no" jdbcType="VARCHAR" property="vehicleSerialNo" />
    <result column="vehicle_brand_code" jdbcType="VARCHAR" property="vehicleBrandCode" />
    <result column="vehicle_brand_name" jdbcType="VARCHAR" property="vehicleBrandName" />
    <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode" />
    <result column="vehicle_model_name" jdbcType="VARCHAR" property="vehicleModelName" />
    <result column="operate_buss_code" jdbcType="VARCHAR" property="operateBussCode" />
    <result column="operate_buss_name" jdbcType="VARCHAR" property="operateBussName" />
    <result column="belong_buss_code" jdbcType="VARCHAR" property="belongBussCode" />
    <result column="belong_buss_name" jdbcType="VARCHAR" property="belongBussName" />
    <result column="asset_city_code" jdbcType="VARCHAR" property="assetCityCode" />
    <result column="asset_city_name" jdbcType="VARCHAR" property="assetCityName" />
    <result column="actual_use_time" jdbcType="TIMESTAMP" property="actualUseTime" />
    <result column="actual_return_time" jdbcType="TIMESTAMP" property="actualReturnTime" />
    <result column="rent_days" jdbcType="INTEGER" property="rentDays" />
    <result column="bill_amount" jdbcType="DECIMAL" property="billAmount" />
    <result column="tax_bill_amount" jdbcType="DECIMAL" property="taxBillAmount" />
    <result column="origin_bill_amount" jdbcType="DECIMAL" property="originBillAmount" />
    <result column="adjust_reason" jdbcType="VARCHAR" property="adjustReason" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="vehicle_type" jdbcType="INTEGER" property="vehicleType" />
    <result column="belong_struct_id" jdbcType="INTEGER" property="belongStructId" />
    <result column="belong_struct_code" jdbcType="VARCHAR" property="belongStructCode" />
    <result column="belong_struct_name" jdbcType="VARCHAR" property="belongStructName" />
    <result column="belong_city_code" jdbcType="VARCHAR" property="belongCityCode" />
    <result column="belong_city_name" jdbcType="VARCHAR" property="belongCityName" />
    <result column="order_create_id" jdbcType="INTEGER" property="orderCreateId" />
    <result column="order_create_name" jdbcType="VARCHAR" property="orderCreateName" />
    <result column="bill_status" jdbcType="INTEGER" property="billStatus" />
    <result column="bill_start_date" jdbcType="DATE" property="billStartDate" />
    <result column="bill_end_date" jdbcType="DATE" property="billEndDate" />
    <result column="bill_date" jdbcType="DATE" property="billDate" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="billing_method" jdbcType="INTEGER" property="billingMethod" />
    <result column="sign_struct_code" jdbcType="VARCHAR" property="signStructCode" />
    <result column="sign_struct_name" jdbcType="VARCHAR" property="signStructName" />
    <result column="tax_rate" jdbcType="DECIMAL" property="taxRate" />
    <result column="overdue_days" jdbcType="INTEGER" property="overdueDays" />
    <result column="social_credit_code" jdbcType="VARCHAR" property="socialCreditCode" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    order_bill_id, order_bill_no, single_bill_no, company_bill_no, order_no, third_order_no, 
    vehicle_license, vehicle_serial_no, vehicle_brand_code, vehicle_brand_name, vehicle_model_code, 
    vehicle_model_name, operate_buss_code, operate_buss_name, belong_buss_code, belong_buss_name, 
    asset_city_code, asset_city_name, actual_use_time, actual_return_time, rent_days, 
    bill_amount, tax_bill_amount, origin_bill_amount, adjust_reason, company_id, company_code, 
    company_name, vehicle_type, belong_struct_id, belong_struct_code, belong_struct_name, 
    belong_city_code, belong_city_name, order_create_id, order_create_name, bill_status, 
    bill_start_date, bill_end_date, bill_date, create_time, update_time, billing_method, 
    sign_struct_code, sign_struct_name, tax_rate, overdue_days, social_credit_code
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.CoOrderBillExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from co_order_bill
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from co_order_bill
    where order_bill_id = #{orderBillId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from co_order_bill
    where order_bill_id = #{orderBillId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.order.entity.mrcar.CoOrderBillExample">
    delete from co_order_bill
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.CoOrderBill">
    <selectKey keyProperty="orderBillId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into co_order_bill (order_bill_no, single_bill_no, company_bill_no, 
      order_no, third_order_no, vehicle_license, 
      vehicle_serial_no, vehicle_brand_code, vehicle_brand_name, 
      vehicle_model_code, vehicle_model_name, operate_buss_code, 
      operate_buss_name, belong_buss_code, belong_buss_name, 
      asset_city_code, asset_city_name, actual_use_time, 
      actual_return_time, rent_days, bill_amount, 
      tax_bill_amount, origin_bill_amount, adjust_reason, 
      company_id, company_code, company_name, 
      vehicle_type, belong_struct_id, belong_struct_code, 
      belong_struct_name, belong_city_code, belong_city_name, 
      order_create_id, order_create_name, bill_status, 
      bill_start_date, bill_end_date, bill_date, 
      create_time, update_time, billing_method, 
      sign_struct_code, sign_struct_name, tax_rate, 
      overdue_days, social_credit_code)
    values (#{orderBillNo,jdbcType=VARCHAR}, #{singleBillNo,jdbcType=VARCHAR}, #{companyBillNo,jdbcType=VARCHAR}, 
      #{orderNo,jdbcType=VARCHAR}, #{thirdOrderNo,jdbcType=VARCHAR}, #{vehicleLicense,jdbcType=VARCHAR}, 
      #{vehicleSerialNo,jdbcType=VARCHAR}, #{vehicleBrandCode,jdbcType=VARCHAR}, #{vehicleBrandName,jdbcType=VARCHAR}, 
      #{vehicleModelCode,jdbcType=VARCHAR}, #{vehicleModelName,jdbcType=VARCHAR}, #{operateBussCode,jdbcType=VARCHAR}, 
      #{operateBussName,jdbcType=VARCHAR}, #{belongBussCode,jdbcType=VARCHAR}, #{belongBussName,jdbcType=VARCHAR}, 
      #{assetCityCode,jdbcType=VARCHAR}, #{assetCityName,jdbcType=VARCHAR}, #{actualUseTime,jdbcType=TIMESTAMP}, 
      #{actualReturnTime,jdbcType=TIMESTAMP}, #{rentDays,jdbcType=INTEGER}, #{billAmount,jdbcType=DECIMAL}, 
      #{taxBillAmount,jdbcType=DECIMAL}, #{originBillAmount,jdbcType=DECIMAL}, #{adjustReason,jdbcType=VARCHAR}, 
      #{companyId,jdbcType=INTEGER}, #{companyCode,jdbcType=VARCHAR}, #{companyName,jdbcType=VARCHAR}, 
      #{vehicleType,jdbcType=INTEGER}, #{belongStructId,jdbcType=INTEGER}, #{belongStructCode,jdbcType=VARCHAR}, 
      #{belongStructName,jdbcType=VARCHAR}, #{belongCityCode,jdbcType=VARCHAR}, #{belongCityName,jdbcType=VARCHAR}, 
      #{orderCreateId,jdbcType=INTEGER}, #{orderCreateName,jdbcType=VARCHAR}, #{billStatus,jdbcType=INTEGER}, 
      #{billStartDate,jdbcType=DATE}, #{billEndDate,jdbcType=DATE}, #{billDate,jdbcType=DATE}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{billingMethod,jdbcType=INTEGER}, 
      #{signStructCode,jdbcType=VARCHAR}, #{signStructName,jdbcType=VARCHAR}, #{taxRate,jdbcType=DECIMAL}, 
      #{overdueDays,jdbcType=INTEGER}, #{socialCreditCode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.CoOrderBill">
    <selectKey keyProperty="orderBillId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into co_order_bill
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderBillNo != null">
        order_bill_no,
      </if>
      <if test="singleBillNo != null">
        single_bill_no,
      </if>
      <if test="companyBillNo != null">
        company_bill_no,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="thirdOrderNo != null">
        third_order_no,
      </if>
      <if test="vehicleLicense != null">
        vehicle_license,
      </if>
      <if test="vehicleSerialNo != null">
        vehicle_serial_no,
      </if>
      <if test="vehicleBrandCode != null">
        vehicle_brand_code,
      </if>
      <if test="vehicleBrandName != null">
        vehicle_brand_name,
      </if>
      <if test="vehicleModelCode != null">
        vehicle_model_code,
      </if>
      <if test="vehicleModelName != null">
        vehicle_model_name,
      </if>
      <if test="operateBussCode != null">
        operate_buss_code,
      </if>
      <if test="operateBussName != null">
        operate_buss_name,
      </if>
      <if test="belongBussCode != null">
        belong_buss_code,
      </if>
      <if test="belongBussName != null">
        belong_buss_name,
      </if>
      <if test="assetCityCode != null">
        asset_city_code,
      </if>
      <if test="assetCityName != null">
        asset_city_name,
      </if>
      <if test="actualUseTime != null">
        actual_use_time,
      </if>
      <if test="actualReturnTime != null">
        actual_return_time,
      </if>
      <if test="rentDays != null">
        rent_days,
      </if>
      <if test="billAmount != null">
        bill_amount,
      </if>
      <if test="taxBillAmount != null">
        tax_bill_amount,
      </if>
      <if test="originBillAmount != null">
        origin_bill_amount,
      </if>
      <if test="adjustReason != null">
        adjust_reason,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="vehicleType != null">
        vehicle_type,
      </if>
      <if test="belongStructId != null">
        belong_struct_id,
      </if>
      <if test="belongStructCode != null">
        belong_struct_code,
      </if>
      <if test="belongStructName != null">
        belong_struct_name,
      </if>
      <if test="belongCityCode != null">
        belong_city_code,
      </if>
      <if test="belongCityName != null">
        belong_city_name,
      </if>
      <if test="orderCreateId != null">
        order_create_id,
      </if>
      <if test="orderCreateName != null">
        order_create_name,
      </if>
      <if test="billStatus != null">
        bill_status,
      </if>
      <if test="billStartDate != null">
        bill_start_date,
      </if>
      <if test="billEndDate != null">
        bill_end_date,
      </if>
      <if test="billDate != null">
        bill_date,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="billingMethod != null">
        billing_method,
      </if>
      <if test="signStructCode != null">
        sign_struct_code,
      </if>
      <if test="signStructName != null">
        sign_struct_name,
      </if>
      <if test="taxRate != null">
        tax_rate,
      </if>
      <if test="overdueDays != null">
        overdue_days,
      </if>
      <if test="socialCreditCode != null">
        social_credit_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderBillNo != null">
        #{orderBillNo,jdbcType=VARCHAR},
      </if>
      <if test="singleBillNo != null">
        #{singleBillNo,jdbcType=VARCHAR},
      </if>
      <if test="companyBillNo != null">
        #{companyBillNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="thirdOrderNo != null">
        #{thirdOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="vehicleLicense != null">
        #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="vehicleSerialNo != null">
        #{vehicleSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBrandCode != null">
        #{vehicleBrandCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBrandName != null">
        #{vehicleBrandName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelCode != null">
        #{vehicleModelCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelName != null">
        #{vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="operateBussCode != null">
        #{operateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="operateBussName != null">
        #{operateBussName,jdbcType=VARCHAR},
      </if>
      <if test="belongBussCode != null">
        #{belongBussCode,jdbcType=VARCHAR},
      </if>
      <if test="belongBussName != null">
        #{belongBussName,jdbcType=VARCHAR},
      </if>
      <if test="assetCityCode != null">
        #{assetCityCode,jdbcType=VARCHAR},
      </if>
      <if test="assetCityName != null">
        #{assetCityName,jdbcType=VARCHAR},
      </if>
      <if test="actualUseTime != null">
        #{actualUseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="actualReturnTime != null">
        #{actualReturnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="rentDays != null">
        #{rentDays,jdbcType=INTEGER},
      </if>
      <if test="billAmount != null">
        #{billAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxBillAmount != null">
        #{taxBillAmount,jdbcType=DECIMAL},
      </if>
      <if test="originBillAmount != null">
        #{originBillAmount,jdbcType=DECIMAL},
      </if>
      <if test="adjustReason != null">
        #{adjustReason,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleType != null">
        #{vehicleType,jdbcType=INTEGER},
      </if>
      <if test="belongStructId != null">
        #{belongStructId,jdbcType=INTEGER},
      </if>
      <if test="belongStructCode != null">
        #{belongStructCode,jdbcType=VARCHAR},
      </if>
      <if test="belongStructName != null">
        #{belongStructName,jdbcType=VARCHAR},
      </if>
      <if test="belongCityCode != null">
        #{belongCityCode,jdbcType=VARCHAR},
      </if>
      <if test="belongCityName != null">
        #{belongCityName,jdbcType=VARCHAR},
      </if>
      <if test="orderCreateId != null">
        #{orderCreateId,jdbcType=INTEGER},
      </if>
      <if test="orderCreateName != null">
        #{orderCreateName,jdbcType=VARCHAR},
      </if>
      <if test="billStatus != null">
        #{billStatus,jdbcType=INTEGER},
      </if>
      <if test="billStartDate != null">
        #{billStartDate,jdbcType=DATE},
      </if>
      <if test="billEndDate != null">
        #{billEndDate,jdbcType=DATE},
      </if>
      <if test="billDate != null">
        #{billDate,jdbcType=DATE},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="billingMethod != null">
        #{billingMethod,jdbcType=INTEGER},
      </if>
      <if test="signStructCode != null">
        #{signStructCode,jdbcType=VARCHAR},
      </if>
      <if test="signStructName != null">
        #{signStructName,jdbcType=VARCHAR},
      </if>
      <if test="taxRate != null">
        #{taxRate,jdbcType=DECIMAL},
      </if>
      <if test="overdueDays != null">
        #{overdueDays,jdbcType=INTEGER},
      </if>
      <if test="socialCreditCode != null">
        #{socialCreditCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.CoOrderBillExample" resultType="java.lang.Long">
    select count(*) from co_order_bill
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update co_order_bill
    <set>
      <if test="row.orderBillId != null">
        order_bill_id = #{row.orderBillId,jdbcType=INTEGER},
      </if>
      <if test="row.orderBillNo != null">
        order_bill_no = #{row.orderBillNo,jdbcType=VARCHAR},
      </if>
      <if test="row.singleBillNo != null">
        single_bill_no = #{row.singleBillNo,jdbcType=VARCHAR},
      </if>
      <if test="row.companyBillNo != null">
        company_bill_no = #{row.companyBillNo,jdbcType=VARCHAR},
      </if>
      <if test="row.orderNo != null">
        order_no = #{row.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="row.thirdOrderNo != null">
        third_order_no = #{row.thirdOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleLicense != null">
        vehicle_license = #{row.vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleSerialNo != null">
        vehicle_serial_no = #{row.vehicleSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleBrandCode != null">
        vehicle_brand_code = #{row.vehicleBrandCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleBrandName != null">
        vehicle_brand_name = #{row.vehicleBrandName,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleModelCode != null">
        vehicle_model_code = #{row.vehicleModelCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleModelName != null">
        vehicle_model_name = #{row.vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="row.operateBussCode != null">
        operate_buss_code = #{row.operateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="row.operateBussName != null">
        operate_buss_name = #{row.operateBussName,jdbcType=VARCHAR},
      </if>
      <if test="row.belongBussCode != null">
        belong_buss_code = #{row.belongBussCode,jdbcType=VARCHAR},
      </if>
      <if test="row.belongBussName != null">
        belong_buss_name = #{row.belongBussName,jdbcType=VARCHAR},
      </if>
      <if test="row.assetCityCode != null">
        asset_city_code = #{row.assetCityCode,jdbcType=VARCHAR},
      </if>
      <if test="row.assetCityName != null">
        asset_city_name = #{row.assetCityName,jdbcType=VARCHAR},
      </if>
      <if test="row.actualUseTime != null">
        actual_use_time = #{row.actualUseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.actualReturnTime != null">
        actual_return_time = #{row.actualReturnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.rentDays != null">
        rent_days = #{row.rentDays,jdbcType=INTEGER},
      </if>
      <if test="row.billAmount != null">
        bill_amount = #{row.billAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.taxBillAmount != null">
        tax_bill_amount = #{row.taxBillAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.originBillAmount != null">
        origin_bill_amount = #{row.originBillAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.adjustReason != null">
        adjust_reason = #{row.adjustReason,jdbcType=VARCHAR},
      </if>
      <if test="row.companyId != null">
        company_id = #{row.companyId,jdbcType=INTEGER},
      </if>
      <if test="row.companyCode != null">
        company_code = #{row.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="row.companyName != null">
        company_name = #{row.companyName,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleType != null">
        vehicle_type = #{row.vehicleType,jdbcType=INTEGER},
      </if>
      <if test="row.belongStructId != null">
        belong_struct_id = #{row.belongStructId,jdbcType=INTEGER},
      </if>
      <if test="row.belongStructCode != null">
        belong_struct_code = #{row.belongStructCode,jdbcType=VARCHAR},
      </if>
      <if test="row.belongStructName != null">
        belong_struct_name = #{row.belongStructName,jdbcType=VARCHAR},
      </if>
      <if test="row.belongCityCode != null">
        belong_city_code = #{row.belongCityCode,jdbcType=VARCHAR},
      </if>
      <if test="row.belongCityName != null">
        belong_city_name = #{row.belongCityName,jdbcType=VARCHAR},
      </if>
      <if test="row.orderCreateId != null">
        order_create_id = #{row.orderCreateId,jdbcType=INTEGER},
      </if>
      <if test="row.orderCreateName != null">
        order_create_name = #{row.orderCreateName,jdbcType=VARCHAR},
      </if>
      <if test="row.billStatus != null">
        bill_status = #{row.billStatus,jdbcType=INTEGER},
      </if>
      <if test="row.billStartDate != null">
        bill_start_date = #{row.billStartDate,jdbcType=DATE},
      </if>
      <if test="row.billEndDate != null">
        bill_end_date = #{row.billEndDate,jdbcType=DATE},
      </if>
      <if test="row.billDate != null">
        bill_date = #{row.billDate,jdbcType=DATE},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.billingMethod != null">
        billing_method = #{row.billingMethod,jdbcType=INTEGER},
      </if>
      <if test="row.signStructCode != null">
        sign_struct_code = #{row.signStructCode,jdbcType=VARCHAR},
      </if>
      <if test="row.signStructName != null">
        sign_struct_name = #{row.signStructName,jdbcType=VARCHAR},
      </if>
      <if test="row.taxRate != null">
        tax_rate = #{row.taxRate,jdbcType=DECIMAL},
      </if>
      <if test="row.overdueDays != null">
        overdue_days = #{row.overdueDays,jdbcType=INTEGER},
      </if>
      <if test="row.socialCreditCode != null">
        social_credit_code = #{row.socialCreditCode,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update co_order_bill
    set order_bill_id = #{row.orderBillId,jdbcType=INTEGER},
      order_bill_no = #{row.orderBillNo,jdbcType=VARCHAR},
      single_bill_no = #{row.singleBillNo,jdbcType=VARCHAR},
      company_bill_no = #{row.companyBillNo,jdbcType=VARCHAR},
      order_no = #{row.orderNo,jdbcType=VARCHAR},
      third_order_no = #{row.thirdOrderNo,jdbcType=VARCHAR},
      vehicle_license = #{row.vehicleLicense,jdbcType=VARCHAR},
      vehicle_serial_no = #{row.vehicleSerialNo,jdbcType=VARCHAR},
      vehicle_brand_code = #{row.vehicleBrandCode,jdbcType=VARCHAR},
      vehicle_brand_name = #{row.vehicleBrandName,jdbcType=VARCHAR},
      vehicle_model_code = #{row.vehicleModelCode,jdbcType=VARCHAR},
      vehicle_model_name = #{row.vehicleModelName,jdbcType=VARCHAR},
      operate_buss_code = #{row.operateBussCode,jdbcType=VARCHAR},
      operate_buss_name = #{row.operateBussName,jdbcType=VARCHAR},
      belong_buss_code = #{row.belongBussCode,jdbcType=VARCHAR},
      belong_buss_name = #{row.belongBussName,jdbcType=VARCHAR},
      asset_city_code = #{row.assetCityCode,jdbcType=VARCHAR},
      asset_city_name = #{row.assetCityName,jdbcType=VARCHAR},
      actual_use_time = #{row.actualUseTime,jdbcType=TIMESTAMP},
      actual_return_time = #{row.actualReturnTime,jdbcType=TIMESTAMP},
      rent_days = #{row.rentDays,jdbcType=INTEGER},
      bill_amount = #{row.billAmount,jdbcType=DECIMAL},
      tax_bill_amount = #{row.taxBillAmount,jdbcType=DECIMAL},
      origin_bill_amount = #{row.originBillAmount,jdbcType=DECIMAL},
      adjust_reason = #{row.adjustReason,jdbcType=VARCHAR},
      company_id = #{row.companyId,jdbcType=INTEGER},
      company_code = #{row.companyCode,jdbcType=VARCHAR},
      company_name = #{row.companyName,jdbcType=VARCHAR},
      vehicle_type = #{row.vehicleType,jdbcType=INTEGER},
      belong_struct_id = #{row.belongStructId,jdbcType=INTEGER},
      belong_struct_code = #{row.belongStructCode,jdbcType=VARCHAR},
      belong_struct_name = #{row.belongStructName,jdbcType=VARCHAR},
      belong_city_code = #{row.belongCityCode,jdbcType=VARCHAR},
      belong_city_name = #{row.belongCityName,jdbcType=VARCHAR},
      order_create_id = #{row.orderCreateId,jdbcType=INTEGER},
      order_create_name = #{row.orderCreateName,jdbcType=VARCHAR},
      bill_status = #{row.billStatus,jdbcType=INTEGER},
      bill_start_date = #{row.billStartDate,jdbcType=DATE},
      bill_end_date = #{row.billEndDate,jdbcType=DATE},
      bill_date = #{row.billDate,jdbcType=DATE},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      billing_method = #{row.billingMethod,jdbcType=INTEGER},
      sign_struct_code = #{row.signStructCode,jdbcType=VARCHAR},
      sign_struct_name = #{row.signStructName,jdbcType=VARCHAR},
      tax_rate = #{row.taxRate,jdbcType=DECIMAL},
      overdue_days = #{row.overdueDays,jdbcType=INTEGER},
      social_credit_code = #{row.socialCreditCode,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.CoOrderBill">
    update co_order_bill
    <set>
      <if test="orderBillNo != null">
        order_bill_no = #{orderBillNo,jdbcType=VARCHAR},
      </if>
      <if test="singleBillNo != null">
        single_bill_no = #{singleBillNo,jdbcType=VARCHAR},
      </if>
      <if test="companyBillNo != null">
        company_bill_no = #{companyBillNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="thirdOrderNo != null">
        third_order_no = #{thirdOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="vehicleLicense != null">
        vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="vehicleSerialNo != null">
        vehicle_serial_no = #{vehicleSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBrandCode != null">
        vehicle_brand_code = #{vehicleBrandCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBrandName != null">
        vehicle_brand_name = #{vehicleBrandName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelCode != null">
        vehicle_model_code = #{vehicleModelCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelName != null">
        vehicle_model_name = #{vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="operateBussCode != null">
        operate_buss_code = #{operateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="operateBussName != null">
        operate_buss_name = #{operateBussName,jdbcType=VARCHAR},
      </if>
      <if test="belongBussCode != null">
        belong_buss_code = #{belongBussCode,jdbcType=VARCHAR},
      </if>
      <if test="belongBussName != null">
        belong_buss_name = #{belongBussName,jdbcType=VARCHAR},
      </if>
      <if test="assetCityCode != null">
        asset_city_code = #{assetCityCode,jdbcType=VARCHAR},
      </if>
      <if test="assetCityName != null">
        asset_city_name = #{assetCityName,jdbcType=VARCHAR},
      </if>
      <if test="actualUseTime != null">
        actual_use_time = #{actualUseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="actualReturnTime != null">
        actual_return_time = #{actualReturnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="rentDays != null">
        rent_days = #{rentDays,jdbcType=INTEGER},
      </if>
      <if test="billAmount != null">
        bill_amount = #{billAmount,jdbcType=DECIMAL},
      </if>
      <if test="taxBillAmount != null">
        tax_bill_amount = #{taxBillAmount,jdbcType=DECIMAL},
      </if>
      <if test="originBillAmount != null">
        origin_bill_amount = #{originBillAmount,jdbcType=DECIMAL},
      </if>
      <if test="adjustReason != null">
        adjust_reason = #{adjustReason,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="vehicleType != null">
        vehicle_type = #{vehicleType,jdbcType=INTEGER},
      </if>
      <if test="belongStructId != null">
        belong_struct_id = #{belongStructId,jdbcType=INTEGER},
      </if>
      <if test="belongStructCode != null">
        belong_struct_code = #{belongStructCode,jdbcType=VARCHAR},
      </if>
      <if test="belongStructName != null">
        belong_struct_name = #{belongStructName,jdbcType=VARCHAR},
      </if>
      <if test="belongCityCode != null">
        belong_city_code = #{belongCityCode,jdbcType=VARCHAR},
      </if>
      <if test="belongCityName != null">
        belong_city_name = #{belongCityName,jdbcType=VARCHAR},
      </if>
      <if test="orderCreateId != null">
        order_create_id = #{orderCreateId,jdbcType=INTEGER},
      </if>
      <if test="orderCreateName != null">
        order_create_name = #{orderCreateName,jdbcType=VARCHAR},
      </if>
      <if test="billStatus != null">
        bill_status = #{billStatus,jdbcType=INTEGER},
      </if>
      <if test="billStartDate != null">
        bill_start_date = #{billStartDate,jdbcType=DATE},
      </if>
      <if test="billEndDate != null">
        bill_end_date = #{billEndDate,jdbcType=DATE},
      </if>
      <if test="billDate != null">
        bill_date = #{billDate,jdbcType=DATE},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="billingMethod != null">
        billing_method = #{billingMethod,jdbcType=INTEGER},
      </if>
      <if test="signStructCode != null">
        sign_struct_code = #{signStructCode,jdbcType=VARCHAR},
      </if>
      <if test="signStructName != null">
        sign_struct_name = #{signStructName,jdbcType=VARCHAR},
      </if>
      <if test="taxRate != null">
        tax_rate = #{taxRate,jdbcType=DECIMAL},
      </if>
      <if test="overdueDays != null">
        overdue_days = #{overdueDays,jdbcType=INTEGER},
      </if>
      <if test="socialCreditCode != null">
        social_credit_code = #{socialCreditCode,jdbcType=VARCHAR},
      </if>
    </set>
    where order_bill_id = #{orderBillId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.CoOrderBill">
    update co_order_bill
    set order_bill_no = #{orderBillNo,jdbcType=VARCHAR},
      single_bill_no = #{singleBillNo,jdbcType=VARCHAR},
      company_bill_no = #{companyBillNo,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      third_order_no = #{thirdOrderNo,jdbcType=VARCHAR},
      vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      vehicle_serial_no = #{vehicleSerialNo,jdbcType=VARCHAR},
      vehicle_brand_code = #{vehicleBrandCode,jdbcType=VARCHAR},
      vehicle_brand_name = #{vehicleBrandName,jdbcType=VARCHAR},
      vehicle_model_code = #{vehicleModelCode,jdbcType=VARCHAR},
      vehicle_model_name = #{vehicleModelName,jdbcType=VARCHAR},
      operate_buss_code = #{operateBussCode,jdbcType=VARCHAR},
      operate_buss_name = #{operateBussName,jdbcType=VARCHAR},
      belong_buss_code = #{belongBussCode,jdbcType=VARCHAR},
      belong_buss_name = #{belongBussName,jdbcType=VARCHAR},
      asset_city_code = #{assetCityCode,jdbcType=VARCHAR},
      asset_city_name = #{assetCityName,jdbcType=VARCHAR},
      actual_use_time = #{actualUseTime,jdbcType=TIMESTAMP},
      actual_return_time = #{actualReturnTime,jdbcType=TIMESTAMP},
      rent_days = #{rentDays,jdbcType=INTEGER},
      bill_amount = #{billAmount,jdbcType=DECIMAL},
      tax_bill_amount = #{taxBillAmount,jdbcType=DECIMAL},
      origin_bill_amount = #{originBillAmount,jdbcType=DECIMAL},
      adjust_reason = #{adjustReason,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=INTEGER},
      company_code = #{companyCode,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      vehicle_type = #{vehicleType,jdbcType=INTEGER},
      belong_struct_id = #{belongStructId,jdbcType=INTEGER},
      belong_struct_code = #{belongStructCode,jdbcType=VARCHAR},
      belong_struct_name = #{belongStructName,jdbcType=VARCHAR},
      belong_city_code = #{belongCityCode,jdbcType=VARCHAR},
      belong_city_name = #{belongCityName,jdbcType=VARCHAR},
      order_create_id = #{orderCreateId,jdbcType=INTEGER},
      order_create_name = #{orderCreateName,jdbcType=VARCHAR},
      bill_status = #{billStatus,jdbcType=INTEGER},
      bill_start_date = #{billStartDate,jdbcType=DATE},
      bill_end_date = #{billEndDate,jdbcType=DATE},
      bill_date = #{billDate,jdbcType=DATE},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      billing_method = #{billingMethod,jdbcType=INTEGER},
      sign_struct_code = #{signStructCode,jdbcType=VARCHAR},
      sign_struct_name = #{signStructName,jdbcType=VARCHAR},
      tax_rate = #{taxRate,jdbcType=DECIMAL},
      overdue_days = #{overdueDays,jdbcType=INTEGER},
      social_credit_code = #{socialCreditCode,jdbcType=VARCHAR}
    where order_bill_id = #{orderBillId,jdbcType=INTEGER}
  </update>
</mapper>