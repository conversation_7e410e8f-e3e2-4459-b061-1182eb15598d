<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.AppraisalTypeConfigMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.AppraisalTypeConfig">
    <id column="evaluation_id" jdbcType="INTEGER" property="evaluationId" />
    <result column="evaluation_name" jdbcType="VARCHAR" property="evaluationName" />
    <result column="evaluation_describe" jdbcType="VARCHAR" property="evaluationDescribe" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="evaluation_status" jdbcType="TINYINT" property="evaluationStatus" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="dissatisfied_label" jdbcType="VARCHAR" property="dissatisfiedLabel" />
    <result column="order_type" jdbcType="VARCHAR" property="orderType" />
  </resultMap>
  <sql id="Base_Column_List">
    evaluation_id, evaluation_name, evaluation_describe, sort, evaluation_status, create_user, 
    update_user, create_time, update_time, dissatisfied_label, order_type
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from appraisal_type_config
    where evaluation_id = #{evaluationId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from appraisal_type_config
    where evaluation_id = #{evaluationId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.AppraisalTypeConfig">
    <selectKey keyProperty="evaluationId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into appraisal_type_config (evaluation_name, evaluation_describe, 
      sort, evaluation_status, create_user, 
      update_user, create_time, update_time, 
      dissatisfied_label, order_type)
    values (#{evaluationName,jdbcType=VARCHAR}, #{evaluationDescribe,jdbcType=VARCHAR}, 
      #{sort,jdbcType=INTEGER}, #{evaluationStatus,jdbcType=TINYINT}, #{createUser,jdbcType=VARCHAR}, 
      #{updateUser,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{dissatisfiedLabel,jdbcType=VARCHAR}, #{orderType,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.AppraisalTypeConfig">
    <selectKey keyProperty="evaluationId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into appraisal_type_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="evaluationName != null">
        evaluation_name,
      </if>
      <if test="evaluationDescribe != null">
        evaluation_describe,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="evaluationStatus != null">
        evaluation_status,
      </if>
      <if test="createUser != null">
        create_user,
      </if>
      <if test="updateUser != null">
        update_user,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="dissatisfiedLabel != null">
        dissatisfied_label,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="evaluationName != null">
        #{evaluationName,jdbcType=VARCHAR},
      </if>
      <if test="evaluationDescribe != null">
        #{evaluationDescribe,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="evaluationStatus != null">
        #{evaluationStatus,jdbcType=TINYINT},
      </if>
      <if test="createUser != null">
        #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dissatisfiedLabel != null">
        #{dissatisfiedLabel,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.AppraisalTypeConfig">
    update appraisal_type_config
    <set>
      <if test="evaluationName != null">
        evaluation_name = #{evaluationName,jdbcType=VARCHAR},
      </if>
      <if test="evaluationDescribe != null">
        evaluation_describe = #{evaluationDescribe,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="evaluationStatus != null">
        evaluation_status = #{evaluationStatus,jdbcType=TINYINT},
      </if>
      <if test="createUser != null">
        create_user = #{createUser,jdbcType=VARCHAR},
      </if>
      <if test="updateUser != null">
        update_user = #{updateUser,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="dissatisfiedLabel != null">
        dissatisfied_label = #{dissatisfiedLabel,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=VARCHAR},
      </if>
    </set>
    where evaluation_id = #{evaluationId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.AppraisalTypeConfig">
    update appraisal_type_config
    set evaluation_name = #{evaluationName,jdbcType=VARCHAR},
      evaluation_describe = #{evaluationDescribe,jdbcType=VARCHAR},
      sort = #{sort,jdbcType=INTEGER},
      evaluation_status = #{evaluationStatus,jdbcType=TINYINT},
      create_user = #{createUser,jdbcType=VARCHAR},
      update_user = #{updateUser,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      dissatisfied_label = #{dissatisfiedLabel,jdbcType=VARCHAR},
      order_type = #{orderType,jdbcType=VARCHAR}
    where evaluation_id = #{evaluationId,jdbcType=INTEGER}
  </update>
</mapper>