<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.OrderApplyVehicleMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.OrderApplyVehicle">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_apply_no" jdbcType="VARCHAR" property="orderApplyNo" />
    <result column="booking_carlevel_id" jdbcType="TINYINT" property="bookingCarlevelId" />
    <result column="booking_carlevel_name" jdbcType="VARCHAR" property="bookingCarlevelName" />
    <result column="booking_vehicle_count" jdbcType="TINYINT" property="bookingVehicleCount" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="creter_id" jdbcType="INTEGER" property="creterId" />
    <result column="creter_name" jdbcType="VARCHAR" property="creterName" />
    <result column="update_id" jdbcType="INTEGER" property="updateId" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
  </resultMap>
  <sql id="Base_Column_List">
    id, order_apply_no, booking_carlevel_id, booking_carlevel_name, booking_vehicle_count, 
    create_date, update_date, creter_id, creter_name, update_id, update_name
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_apply_vehicle
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from order_apply_vehicle
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.OrderApplyVehicle">
    insert into order_apply_vehicle (id, order_apply_no, booking_carlevel_id, 
      booking_carlevel_name, booking_vehicle_count, 
      create_date, update_date, creter_id, 
      creter_name, update_id, update_name
      )
    values (#{id,jdbcType=BIGINT}, #{orderApplyNo,jdbcType=VARCHAR}, #{bookingCarlevelId,jdbcType=TINYINT}, 
      #{bookingCarlevelName,jdbcType=VARCHAR}, #{bookingVehicleCount,jdbcType=TINYINT}, 
      #{createDate,jdbcType=TIMESTAMP}, #{updateDate,jdbcType=TIMESTAMP}, #{creterId,jdbcType=INTEGER}, 
      #{creterName,jdbcType=VARCHAR}, #{updateId,jdbcType=INTEGER}, #{updateName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.OrderApplyVehicle">
    insert into order_apply_vehicle
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderApplyNo != null">
        order_apply_no,
      </if>
      <if test="bookingCarlevelId != null">
        booking_carlevel_id,
      </if>
      <if test="bookingCarlevelName != null">
        booking_carlevel_name,
      </if>
      <if test="bookingVehicleCount != null">
        booking_vehicle_count,
      </if>
      <if test="createDate != null">
        create_date,
      </if>
      <if test="updateDate != null">
        update_date,
      </if>
      <if test="creterId != null">
        creter_id,
      </if>
      <if test="creterName != null">
        creter_name,
      </if>
      <if test="updateId != null">
        update_id,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="orderApplyNo != null">
        #{orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="bookingCarlevelId != null">
        #{bookingCarlevelId,jdbcType=TINYINT},
      </if>
      <if test="bookingCarlevelName != null">
        #{bookingCarlevelName,jdbcType=VARCHAR},
      </if>
      <if test="bookingVehicleCount != null">
        #{bookingVehicleCount,jdbcType=TINYINT},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDate != null">
        #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="creterId != null">
        #{creterId,jdbcType=INTEGER},
      </if>
      <if test="creterName != null">
        #{creterName,jdbcType=VARCHAR},
      </if>
      <if test="updateId != null">
        #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.OrderApplyVehicle">
    update order_apply_vehicle
    <set>
      <if test="orderApplyNo != null">
        order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="bookingCarlevelId != null">
        booking_carlevel_id = #{bookingCarlevelId,jdbcType=TINYINT},
      </if>
      <if test="bookingCarlevelName != null">
        booking_carlevel_name = #{bookingCarlevelName,jdbcType=VARCHAR},
      </if>
      <if test="bookingVehicleCount != null">
        booking_vehicle_count = #{bookingVehicleCount,jdbcType=TINYINT},
      </if>
      <if test="createDate != null">
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDate != null">
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="creterId != null">
        creter_id = #{creterId,jdbcType=INTEGER},
      </if>
      <if test="creterName != null">
        creter_name = #{creterName,jdbcType=VARCHAR},
      </if>
      <if test="updateId != null">
        update_id = #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.OrderApplyVehicle">
    update order_apply_vehicle
    set order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      booking_carlevel_id = #{bookingCarlevelId,jdbcType=TINYINT},
      booking_carlevel_name = #{bookingCarlevelName,jdbcType=VARCHAR},
      booking_vehicle_count = #{bookingVehicleCount,jdbcType=TINYINT},
      create_date = #{createDate,jdbcType=TIMESTAMP},
      update_date = #{updateDate,jdbcType=TIMESTAMP},
      creter_id = #{creterId,jdbcType=INTEGER},
      creter_name = #{creterName,jdbcType=VARCHAR},
      update_id = #{updateId,jdbcType=INTEGER},
      update_name = #{updateName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>