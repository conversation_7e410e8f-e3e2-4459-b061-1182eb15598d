<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.StatementBillMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.StatementBill">
    <id column="statement_bill_id" jdbcType="INTEGER" property="statementBillId" />
    <result column="statement_bill_num" jdbcType="VARCHAR" property="statementBillNum" />
    <result column="bill_type" jdbcType="INTEGER" property="billType" />
    <result column="bill_amount" jdbcType="DECIMAL" property="billAmount" />
    <result column="company_customer_id" jdbcType="INTEGER" property="companyCustomerId" />
    <result column="company_customer_name" jdbcType="VARCHAR" property="companyCustomerName" />
    <result column="subsidiary_company_code" jdbcType="VARCHAR" property="subsidiaryCompanyCode" />
    <result column="subsidiary_company_name" jdbcType="VARCHAR" property="subsidiaryCompanyName" />
    <result column="bill_start_time" jdbcType="TIMESTAMP" property="billStartTime" />
    <result column="bill_end_time" jdbcType="TIMESTAMP" property="billEndTime" />
    <result column="vehicle_count" jdbcType="INTEGER" property="vehicleCount" />
    <result column="driver_count" jdbcType="INTEGER" property="driverCount" />
    <result column="bill_status" jdbcType="INTEGER" property="billStatus" />
    <result column="second_bill_status" jdbcType="INTEGER" property="secondBillStatus" />
    <result column="settle_status" jdbcType="INTEGER" property="settleStatus" />
    <result column="company_customer_confirm_amount" jdbcType="DECIMAL" property="companyCustomerConfirmAmount" />
    <result column="service_company_customer_id" jdbcType="INTEGER" property="serviceCompanyCustomerId" />
    <result column="service_company_customer_name" jdbcType="VARCHAR" property="serviceCompanyCustomerName" />
    <result column="relate_bill_num" jdbcType="VARCHAR" property="relateBillNum" />
    <result column="create_id" jdbcType="INTEGER" property="createId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_id" jdbcType="INTEGER" property="updateId" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    statement_bill_id, statement_bill_num, bill_type, bill_amount, company_customer_id, 
    company_customer_name, subsidiary_company_code, subsidiary_company_name, bill_start_time, 
    bill_end_time, vehicle_count, driver_count, bill_status, second_bill_status, settle_status, 
    company_customer_confirm_amount, service_company_customer_id, service_company_customer_name, 
    relate_bill_num, create_id, create_name, create_time, update_id, update_name, update_time
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.StatementBillExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from so_statement_bill
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from so_statement_bill
    where statement_bill_id = #{statementBillId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from so_statement_bill
    where statement_bill_id = #{statementBillId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.StatementBill">
    <selectKey keyProperty="statementBillId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into so_statement_bill (statement_bill_num, bill_type, bill_amount, 
      company_customer_id, company_customer_name, 
      subsidiary_company_code, subsidiary_company_name, 
      bill_start_time, bill_end_time, vehicle_count, 
      driver_count, bill_status, second_bill_status, 
      settle_status, company_customer_confirm_amount, 
      service_company_customer_id, service_company_customer_name, 
      relate_bill_num, create_id, create_name, 
      create_time, update_id, update_name, 
      update_time)
    values (#{statementBillNum,jdbcType=VARCHAR}, #{billType,jdbcType=INTEGER}, #{billAmount,jdbcType=DECIMAL}, 
      #{companyCustomerId,jdbcType=INTEGER}, #{companyCustomerName,jdbcType=VARCHAR}, 
      #{subsidiaryCompanyCode,jdbcType=VARCHAR}, #{subsidiaryCompanyName,jdbcType=VARCHAR}, 
      #{billStartTime,jdbcType=TIMESTAMP}, #{billEndTime,jdbcType=TIMESTAMP}, #{vehicleCount,jdbcType=INTEGER}, 
      #{driverCount,jdbcType=INTEGER}, #{billStatus,jdbcType=INTEGER}, #{secondBillStatus,jdbcType=INTEGER}, 
      #{settleStatus,jdbcType=INTEGER}, #{companyCustomerConfirmAmount,jdbcType=DECIMAL}, 
      #{serviceCompanyCustomerId,jdbcType=INTEGER}, #{serviceCompanyCustomerName,jdbcType=VARCHAR}, 
      #{relateBillNum,jdbcType=VARCHAR}, #{createId,jdbcType=INTEGER}, #{createName,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateId,jdbcType=INTEGER}, #{updateName,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.StatementBill">
    <selectKey keyProperty="statementBillId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into so_statement_bill
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="statementBillNum != null">
        statement_bill_num,
      </if>
      <if test="billType != null">
        bill_type,
      </if>
      <if test="billAmount != null">
        bill_amount,
      </if>
      <if test="companyCustomerId != null">
        company_customer_id,
      </if>
      <if test="companyCustomerName != null">
        company_customer_name,
      </if>
      <if test="subsidiaryCompanyCode != null">
        subsidiary_company_code,
      </if>
      <if test="subsidiaryCompanyName != null">
        subsidiary_company_name,
      </if>
      <if test="billStartTime != null">
        bill_start_time,
      </if>
      <if test="billEndTime != null">
        bill_end_time,
      </if>
      <if test="vehicleCount != null">
        vehicle_count,
      </if>
      <if test="driverCount != null">
        driver_count,
      </if>
      <if test="billStatus != null">
        bill_status,
      </if>
      <if test="secondBillStatus != null">
        second_bill_status,
      </if>
      <if test="settleStatus != null">
        settle_status,
      </if>
      <if test="companyCustomerConfirmAmount != null">
        company_customer_confirm_amount,
      </if>
      <if test="serviceCompanyCustomerId != null">
        service_company_customer_id,
      </if>
      <if test="serviceCompanyCustomerName != null">
        service_company_customer_name,
      </if>
      <if test="relateBillNum != null">
        relate_bill_num,
      </if>
      <if test="createId != null">
        create_id,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateId != null">
        update_id,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="statementBillNum != null">
        #{statementBillNum,jdbcType=VARCHAR},
      </if>
      <if test="billType != null">
        #{billType,jdbcType=INTEGER},
      </if>
      <if test="billAmount != null">
        #{billAmount,jdbcType=DECIMAL},
      </if>
      <if test="companyCustomerId != null">
        #{companyCustomerId,jdbcType=INTEGER},
      </if>
      <if test="companyCustomerName != null">
        #{companyCustomerName,jdbcType=VARCHAR},
      </if>
      <if test="subsidiaryCompanyCode != null">
        #{subsidiaryCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="subsidiaryCompanyName != null">
        #{subsidiaryCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="billStartTime != null">
        #{billStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="billEndTime != null">
        #{billEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="vehicleCount != null">
        #{vehicleCount,jdbcType=INTEGER},
      </if>
      <if test="driverCount != null">
        #{driverCount,jdbcType=INTEGER},
      </if>
      <if test="billStatus != null">
        #{billStatus,jdbcType=INTEGER},
      </if>
      <if test="secondBillStatus != null">
        #{secondBillStatus,jdbcType=INTEGER},
      </if>
      <if test="settleStatus != null">
        #{settleStatus,jdbcType=INTEGER},
      </if>
      <if test="companyCustomerConfirmAmount != null">
        #{companyCustomerConfirmAmount,jdbcType=DECIMAL},
      </if>
      <if test="serviceCompanyCustomerId != null">
        #{serviceCompanyCustomerId,jdbcType=INTEGER},
      </if>
      <if test="serviceCompanyCustomerName != null">
        #{serviceCompanyCustomerName,jdbcType=VARCHAR},
      </if>
      <if test="relateBillNum != null">
        #{relateBillNum,jdbcType=VARCHAR},
      </if>
      <if test="createId != null">
        #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.StatementBillExample" resultType="java.lang.Long">
    select count(*) from so_statement_bill
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.StatementBill">
    update so_statement_bill
    <set>
      <if test="statementBillNum != null">
        statement_bill_num = #{statementBillNum,jdbcType=VARCHAR},
      </if>
      <if test="billType != null">
        bill_type = #{billType,jdbcType=INTEGER},
      </if>
      <if test="billAmount != null">
        bill_amount = #{billAmount,jdbcType=DECIMAL},
      </if>
      <if test="companyCustomerId != null">
        company_customer_id = #{companyCustomerId,jdbcType=INTEGER},
      </if>
      <if test="companyCustomerName != null">
        company_customer_name = #{companyCustomerName,jdbcType=VARCHAR},
      </if>
      <if test="subsidiaryCompanyCode != null">
        subsidiary_company_code = #{subsidiaryCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="subsidiaryCompanyName != null">
        subsidiary_company_name = #{subsidiaryCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="billStartTime != null">
        bill_start_time = #{billStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="billEndTime != null">
        bill_end_time = #{billEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="vehicleCount != null">
        vehicle_count = #{vehicleCount,jdbcType=INTEGER},
      </if>
      <if test="driverCount != null">
        driver_count = #{driverCount,jdbcType=INTEGER},
      </if>
      <if test="billStatus != null">
        bill_status = #{billStatus,jdbcType=INTEGER},
      </if>
      <if test="secondBillStatus != null">
        second_bill_status = #{secondBillStatus,jdbcType=INTEGER},
      </if>
      <if test="settleStatus != null">
        settle_status = #{settleStatus,jdbcType=INTEGER},
      </if>
      <if test="companyCustomerConfirmAmount != null">
        company_customer_confirm_amount = #{companyCustomerConfirmAmount,jdbcType=DECIMAL},
      </if>
      <if test="serviceCompanyCustomerId != null">
        service_company_customer_id = #{serviceCompanyCustomerId,jdbcType=INTEGER},
      </if>
      <if test="serviceCompanyCustomerName != null">
        service_company_customer_name = #{serviceCompanyCustomerName,jdbcType=VARCHAR},
      </if>
      <if test="relateBillNum != null">
        relate_bill_num = #{relateBillNum,jdbcType=VARCHAR},
      </if>
      <if test="createId != null">
        create_id = #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        update_id = #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where statement_bill_id = #{statementBillId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.StatementBill">
    update so_statement_bill
    set statement_bill_num = #{statementBillNum,jdbcType=VARCHAR},
      bill_type = #{billType,jdbcType=INTEGER},
      bill_amount = #{billAmount,jdbcType=DECIMAL},
      company_customer_id = #{companyCustomerId,jdbcType=INTEGER},
      company_customer_name = #{companyCustomerName,jdbcType=VARCHAR},
      subsidiary_company_code = #{subsidiaryCompanyCode,jdbcType=VARCHAR},
      subsidiary_company_name = #{subsidiaryCompanyName,jdbcType=VARCHAR},
      bill_start_time = #{billStartTime,jdbcType=TIMESTAMP},
      bill_end_time = #{billEndTime,jdbcType=TIMESTAMP},
      vehicle_count = #{vehicleCount,jdbcType=INTEGER},
      driver_count = #{driverCount,jdbcType=INTEGER},
      bill_status = #{billStatus,jdbcType=INTEGER},
      second_bill_status = #{secondBillStatus,jdbcType=INTEGER},
      settle_status = #{settleStatus,jdbcType=INTEGER},
      company_customer_confirm_amount = #{companyCustomerConfirmAmount,jdbcType=DECIMAL},
      service_company_customer_id = #{serviceCompanyCustomerId,jdbcType=INTEGER},
      service_company_customer_name = #{serviceCompanyCustomerName,jdbcType=VARCHAR},
      relate_bill_num = #{relateBillNum,jdbcType=VARCHAR},
      create_id = #{createId,jdbcType=INTEGER},
      create_name = #{createName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_id = #{updateId,jdbcType=INTEGER},
      update_name = #{updateName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where statement_bill_id = #{statementBillId,jdbcType=INTEGER}
  </update>
</mapper>