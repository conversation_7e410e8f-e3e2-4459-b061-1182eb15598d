<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.OrderSupplierMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.OrderSupplier">
    <id column="order_supplier_id" jdbcType="INTEGER" property="orderSupplierId" />
    <result column="order_supplier_num" jdbcType="VARCHAR" property="orderSupplierNum" />
    <result column="order_dispatch_time" jdbcType="TIMESTAMP" property="orderDispatchTime" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="customer_phone" jdbcType="VARCHAR" property="customerPhone" />
    <result column="customer_city_code" jdbcType="VARCHAR" property="customerCityCode" />
    <result column="customer_city_name" jdbcType="VARCHAR" property="customerCityName" />
    <result column="customer_address" jdbcType="VARCHAR" property="customerAddress" />
    <result column="estimate_use_date" jdbcType="TIMESTAMP" property="estimateUseDate" />
    <result column="estimate_return_date" jdbcType="TIMESTAMP" property="estimateReturnDate" />
    <result column="estimate_hire_cycle" jdbcType="INTEGER" property="estimateHireCycle" />
    <result column="actual_use_time" jdbcType="TIMESTAMP" property="actualUseTime" />
    <result column="actual_return_time" jdbcType="TIMESTAMP" property="actualReturnTime" />
    <result column="actual_use_days" jdbcType="INTEGER" property="actualUseDays" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="vehicle_license" jdbcType="VARCHAR" property="vehicleLicense" />
    <result column="vehicle_vin" jdbcType="VARCHAR" property="vehicleVin" />
    <result column="power_type" jdbcType="INTEGER" property="powerType" />
    <result column="operate_buss_name" jdbcType="VARCHAR" property="operateBussName" />
    <result column="operate_buss_code" jdbcType="VARCHAR" property="operateBussCode" />
    <result column="belong_buss_code" jdbcType="VARCHAR" property="belongBussCode" />
    <result column="belong_buss_name" jdbcType="VARCHAR" property="belongBussName" />
    <result column="asset_city_code" jdbcType="VARCHAR" property="assetCityCode" />
    <result column="asset_city_name" jdbcType="VARCHAR" property="assetCityName" />
    <result column="driver_id" jdbcType="INTEGER" property="driverId" />
    <result column="driver_name" jdbcType="VARCHAR" property="driverName" />
    <result column="driver_mobile" jdbcType="VARCHAR" property="driverMobile" />
    <result column="daily_rent" jdbcType="DECIMAL" property="dailyRent" />
    <result column="demand_order_num" jdbcType="VARCHAR" property="demandOrderNum" />
    <result column="customer_order_num" jdbcType="VARCHAR" property="customerOrderNum" />
    <result column="order_type" jdbcType="INTEGER" property="orderType" />
    <result column="order_status" jdbcType="INTEGER" property="orderStatus" />
    <result column="delivery_use_name" jdbcType="VARCHAR" property="deliveryUseName" />
    <result column="delivery_use_phone" jdbcType="VARCHAR" property="deliveryUsePhone" />
    <result column="delivery_time" jdbcType="TIMESTAMP" property="deliveryTime" />
    <result column="company_type" jdbcType="INTEGER" property="companyType" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="change_flag" jdbcType="INTEGER" property="changeFlag" />
    <result column="completed_flag" jdbcType="INTEGER" property="completedFlag" />
    <result column="dispatching_flag" jdbcType="INTEGER" property="dispatchingFlag" />
    <result column="returned_flag" jdbcType="INTEGER" property="returnedFlag" />
    <result column="create_id" jdbcType="INTEGER" property="createId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_id" jdbcType="INTEGER" property="updateId" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="return_address" jdbcType="VARCHAR" property="returnAddress" />
  </resultMap>
  <sql id="Base_Column_List">
    order_supplier_id, order_supplier_num, order_dispatch_time, customer_name, customer_phone, 
    customer_city_code, customer_city_name, customer_address, estimate_use_date, estimate_return_date, 
    estimate_hire_cycle, actual_use_time, actual_return_time, actual_use_days, vehicle_id, 
    vehicle_license, vehicle_vin, power_type, operate_buss_name, operate_buss_code, belong_buss_code, 
    belong_buss_name, asset_city_code, asset_city_name, driver_id, driver_name, driver_mobile, 
    daily_rent, demand_order_num, customer_order_num, order_type, order_status, delivery_use_name, 
    delivery_use_phone, delivery_time, company_type, company_code, company_name, change_flag, 
    completed_flag, dispatching_flag, returned_flag, create_id, create_name, create_time, 
    update_id, update_name, update_time, return_address
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from so_order_supplier
    where order_supplier_id = #{orderSupplierId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from so_order_supplier
    where order_supplier_id = #{orderSupplierId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.OrderSupplier">
    insert into so_order_supplier (order_supplier_id, order_supplier_num, 
      order_dispatch_time, customer_name, customer_phone, 
      customer_city_code, customer_city_name, customer_address, 
      estimate_use_date, estimate_return_date, 
      estimate_hire_cycle, actual_use_time, actual_return_time, 
      actual_use_days, vehicle_id, vehicle_license, 
      vehicle_vin, power_type, operate_buss_name, 
      operate_buss_code, belong_buss_code, belong_buss_name, 
      asset_city_code, asset_city_name, driver_id, 
      driver_name, driver_mobile, daily_rent, 
      demand_order_num, customer_order_num, order_type, 
      order_status, delivery_use_name, delivery_use_phone, 
      delivery_time, company_type, company_code, 
      company_name, change_flag, completed_flag, 
      dispatching_flag, returned_flag, create_id, 
      create_name, create_time, update_id, 
      update_name, update_time, return_address
      )
    values (#{orderSupplierId,jdbcType=INTEGER}, #{orderSupplierNum,jdbcType=VARCHAR}, 
      #{orderDispatchTime,jdbcType=TIMESTAMP}, #{customerName,jdbcType=VARCHAR}, #{customerPhone,jdbcType=VARCHAR}, 
      #{customerCityCode,jdbcType=VARCHAR}, #{customerCityName,jdbcType=VARCHAR}, #{customerAddress,jdbcType=VARCHAR}, 
      #{estimateUseDate,jdbcType=TIMESTAMP}, #{estimateReturnDate,jdbcType=TIMESTAMP}, 
      #{estimateHireCycle,jdbcType=INTEGER}, #{actualUseTime,jdbcType=TIMESTAMP}, #{actualReturnTime,jdbcType=TIMESTAMP}, 
      #{actualUseDays,jdbcType=INTEGER}, #{vehicleId,jdbcType=BIGINT}, #{vehicleLicense,jdbcType=VARCHAR}, 
      #{vehicleVin,jdbcType=VARCHAR}, #{powerType,jdbcType=INTEGER}, #{operateBussName,jdbcType=VARCHAR}, 
      #{operateBussCode,jdbcType=VARCHAR}, #{belongBussCode,jdbcType=VARCHAR}, #{belongBussName,jdbcType=VARCHAR}, 
      #{assetCityCode,jdbcType=VARCHAR}, #{assetCityName,jdbcType=VARCHAR}, #{driverId,jdbcType=INTEGER}, 
      #{driverName,jdbcType=VARCHAR}, #{driverMobile,jdbcType=VARCHAR}, #{dailyRent,jdbcType=DECIMAL}, 
      #{demandOrderNum,jdbcType=VARCHAR}, #{customerOrderNum,jdbcType=VARCHAR}, #{orderType,jdbcType=INTEGER}, 
      #{orderStatus,jdbcType=INTEGER}, #{deliveryUseName,jdbcType=VARCHAR}, #{deliveryUsePhone,jdbcType=VARCHAR}, 
      #{deliveryTime,jdbcType=TIMESTAMP}, #{companyType,jdbcType=INTEGER}, #{companyCode,jdbcType=VARCHAR}, 
      #{companyName,jdbcType=VARCHAR}, #{changeFlag,jdbcType=INTEGER}, #{completedFlag,jdbcType=INTEGER}, 
      #{dispatchingFlag,jdbcType=INTEGER}, #{returnedFlag,jdbcType=INTEGER}, #{createId,jdbcType=INTEGER}, 
      #{createName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateId,jdbcType=INTEGER}, 
      #{updateName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{returnAddress,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.OrderSupplier">
    insert into so_order_supplier
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderSupplierId != null">
        order_supplier_id,
      </if>
      <if test="orderSupplierNum != null">
        order_supplier_num,
      </if>
      <if test="orderDispatchTime != null">
        order_dispatch_time,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="customerPhone != null">
        customer_phone,
      </if>
      <if test="customerCityCode != null">
        customer_city_code,
      </if>
      <if test="customerCityName != null">
        customer_city_name,
      </if>
      <if test="customerAddress != null">
        customer_address,
      </if>
      <if test="estimateUseDate != null">
        estimate_use_date,
      </if>
      <if test="estimateReturnDate != null">
        estimate_return_date,
      </if>
      <if test="estimateHireCycle != null">
        estimate_hire_cycle,
      </if>
      <if test="actualUseTime != null">
        actual_use_time,
      </if>
      <if test="actualReturnTime != null">
        actual_return_time,
      </if>
      <if test="actualUseDays != null">
        actual_use_days,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="vehicleLicense != null">
        vehicle_license,
      </if>
      <if test="vehicleVin != null">
        vehicle_vin,
      </if>
      <if test="powerType != null">
        power_type,
      </if>
      <if test="operateBussName != null">
        operate_buss_name,
      </if>
      <if test="operateBussCode != null">
        operate_buss_code,
      </if>
      <if test="belongBussCode != null">
        belong_buss_code,
      </if>
      <if test="belongBussName != null">
        belong_buss_name,
      </if>
      <if test="assetCityCode != null">
        asset_city_code,
      </if>
      <if test="assetCityName != null">
        asset_city_name,
      </if>
      <if test="driverId != null">
        driver_id,
      </if>
      <if test="driverName != null">
        driver_name,
      </if>
      <if test="driverMobile != null">
        driver_mobile,
      </if>
      <if test="dailyRent != null">
        daily_rent,
      </if>
      <if test="demandOrderNum != null">
        demand_order_num,
      </if>
      <if test="customerOrderNum != null">
        customer_order_num,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="deliveryUseName != null">
        delivery_use_name,
      </if>
      <if test="deliveryUsePhone != null">
        delivery_use_phone,
      </if>
      <if test="deliveryTime != null">
        delivery_time,
      </if>
      <if test="companyType != null">
        company_type,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="changeFlag != null">
        change_flag,
      </if>
      <if test="completedFlag != null">
        completed_flag,
      </if>
      <if test="dispatchingFlag != null">
        dispatching_flag,
      </if>
      <if test="returnedFlag != null">
        returned_flag,
      </if>
      <if test="createId != null">
        create_id,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateId != null">
        update_id,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="returnAddress != null">
        return_address,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderSupplierId != null">
        #{orderSupplierId,jdbcType=INTEGER},
      </if>
      <if test="orderSupplierNum != null">
        #{orderSupplierNum,jdbcType=VARCHAR},
      </if>
      <if test="orderDispatchTime != null">
        #{orderDispatchTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerPhone != null">
        #{customerPhone,jdbcType=VARCHAR},
      </if>
      <if test="customerCityCode != null">
        #{customerCityCode,jdbcType=VARCHAR},
      </if>
      <if test="customerCityName != null">
        #{customerCityName,jdbcType=VARCHAR},
      </if>
      <if test="customerAddress != null">
        #{customerAddress,jdbcType=VARCHAR},
      </if>
      <if test="estimateUseDate != null">
        #{estimateUseDate,jdbcType=TIMESTAMP},
      </if>
      <if test="estimateReturnDate != null">
        #{estimateReturnDate,jdbcType=TIMESTAMP},
      </if>
      <if test="estimateHireCycle != null">
        #{estimateHireCycle,jdbcType=INTEGER},
      </if>
      <if test="actualUseTime != null">
        #{actualUseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="actualReturnTime != null">
        #{actualReturnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="actualUseDays != null">
        #{actualUseDays,jdbcType=INTEGER},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleLicense != null">
        #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="vehicleVin != null">
        #{vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="powerType != null">
        #{powerType,jdbcType=INTEGER},
      </if>
      <if test="operateBussName != null">
        #{operateBussName,jdbcType=VARCHAR},
      </if>
      <if test="operateBussCode != null">
        #{operateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="belongBussCode != null">
        #{belongBussCode,jdbcType=VARCHAR},
      </if>
      <if test="belongBussName != null">
        #{belongBussName,jdbcType=VARCHAR},
      </if>
      <if test="assetCityCode != null">
        #{assetCityCode,jdbcType=VARCHAR},
      </if>
      <if test="assetCityName != null">
        #{assetCityName,jdbcType=VARCHAR},
      </if>
      <if test="driverId != null">
        #{driverId,jdbcType=INTEGER},
      </if>
      <if test="driverName != null">
        #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="driverMobile != null">
        #{driverMobile,jdbcType=VARCHAR},
      </if>
      <if test="dailyRent != null">
        #{dailyRent,jdbcType=DECIMAL},
      </if>
      <if test="demandOrderNum != null">
        #{demandOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="customerOrderNum != null">
        #{customerOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=INTEGER},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="deliveryUseName != null">
        #{deliveryUseName,jdbcType=VARCHAR},
      </if>
      <if test="deliveryUsePhone != null">
        #{deliveryUsePhone,jdbcType=VARCHAR},
      </if>
      <if test="deliveryTime != null">
        #{deliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyType != null">
        #{companyType,jdbcType=INTEGER},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="changeFlag != null">
        #{changeFlag,jdbcType=INTEGER},
      </if>
      <if test="completedFlag != null">
        #{completedFlag,jdbcType=INTEGER},
      </if>
      <if test="dispatchingFlag != null">
        #{dispatchingFlag,jdbcType=INTEGER},
      </if>
      <if test="returnedFlag != null">
        #{returnedFlag,jdbcType=INTEGER},
      </if>
      <if test="createId != null">
        #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="returnAddress != null">
        #{returnAddress,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.OrderSupplier">
    update so_order_supplier
    <set>
      <if test="orderSupplierNum != null">
        order_supplier_num = #{orderSupplierNum,jdbcType=VARCHAR},
      </if>
      <if test="orderDispatchTime != null">
        order_dispatch_time = #{orderDispatchTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customerName != null">
        customer_name = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerPhone != null">
        customer_phone = #{customerPhone,jdbcType=VARCHAR},
      </if>
      <if test="customerCityCode != null">
        customer_city_code = #{customerCityCode,jdbcType=VARCHAR},
      </if>
      <if test="customerCityName != null">
        customer_city_name = #{customerCityName,jdbcType=VARCHAR},
      </if>
      <if test="customerAddress != null">
        customer_address = #{customerAddress,jdbcType=VARCHAR},
      </if>
      <if test="estimateUseDate != null">
        estimate_use_date = #{estimateUseDate,jdbcType=TIMESTAMP},
      </if>
      <if test="estimateReturnDate != null">
        estimate_return_date = #{estimateReturnDate,jdbcType=TIMESTAMP},
      </if>
      <if test="estimateHireCycle != null">
        estimate_hire_cycle = #{estimateHireCycle,jdbcType=INTEGER},
      </if>
      <if test="actualUseTime != null">
        actual_use_time = #{actualUseTime,jdbcType=TIMESTAMP},
      </if>
      <if test="actualReturnTime != null">
        actual_return_time = #{actualReturnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="actualUseDays != null">
        actual_use_days = #{actualUseDays,jdbcType=INTEGER},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleLicense != null">
        vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="vehicleVin != null">
        vehicle_vin = #{vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="powerType != null">
        power_type = #{powerType,jdbcType=INTEGER},
      </if>
      <if test="operateBussName != null">
        operate_buss_name = #{operateBussName,jdbcType=VARCHAR},
      </if>
      <if test="operateBussCode != null">
        operate_buss_code = #{operateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="belongBussCode != null">
        belong_buss_code = #{belongBussCode,jdbcType=VARCHAR},
      </if>
      <if test="belongBussName != null">
        belong_buss_name = #{belongBussName,jdbcType=VARCHAR},
      </if>
      <if test="assetCityCode != null">
        asset_city_code = #{assetCityCode,jdbcType=VARCHAR},
      </if>
      <if test="assetCityName != null">
        asset_city_name = #{assetCityName,jdbcType=VARCHAR},
      </if>
      <if test="driverId != null">
        driver_id = #{driverId,jdbcType=INTEGER},
      </if>
      <if test="driverName != null">
        driver_name = #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="driverMobile != null">
        driver_mobile = #{driverMobile,jdbcType=VARCHAR},
      </if>
      <if test="dailyRent != null">
        daily_rent = #{dailyRent,jdbcType=DECIMAL},
      </if>
      <if test="demandOrderNum != null">
        demand_order_num = #{demandOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="customerOrderNum != null">
        customer_order_num = #{customerOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=INTEGER},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="deliveryUseName != null">
        delivery_use_name = #{deliveryUseName,jdbcType=VARCHAR},
      </if>
      <if test="deliveryUsePhone != null">
        delivery_use_phone = #{deliveryUsePhone,jdbcType=VARCHAR},
      </if>
      <if test="deliveryTime != null">
        delivery_time = #{deliveryTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyType != null">
        company_type = #{companyType,jdbcType=INTEGER},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="changeFlag != null">
        change_flag = #{changeFlag,jdbcType=INTEGER},
      </if>
      <if test="completedFlag != null">
        completed_flag = #{completedFlag,jdbcType=INTEGER},
      </if>
      <if test="dispatchingFlag != null">
        dispatching_flag = #{dispatchingFlag,jdbcType=INTEGER},
      </if>
      <if test="returnedFlag != null">
        returned_flag = #{returnedFlag,jdbcType=INTEGER},
      </if>
      <if test="createId != null">
        create_id = #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        update_id = #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="returnAddress != null">
        return_address = #{returnAddress,jdbcType=VARCHAR},
      </if>
    </set>
    where order_supplier_id = #{orderSupplierId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.OrderSupplier">
    update so_order_supplier
    set order_supplier_num = #{orderSupplierNum,jdbcType=VARCHAR},
      order_dispatch_time = #{orderDispatchTime,jdbcType=TIMESTAMP},
      customer_name = #{customerName,jdbcType=VARCHAR},
      customer_phone = #{customerPhone,jdbcType=VARCHAR},
      customer_city_code = #{customerCityCode,jdbcType=VARCHAR},
      customer_city_name = #{customerCityName,jdbcType=VARCHAR},
      customer_address = #{customerAddress,jdbcType=VARCHAR},
      estimate_use_date = #{estimateUseDate,jdbcType=TIMESTAMP},
      estimate_return_date = #{estimateReturnDate,jdbcType=TIMESTAMP},
      estimate_hire_cycle = #{estimateHireCycle,jdbcType=INTEGER},
      actual_use_time = #{actualUseTime,jdbcType=TIMESTAMP},
      actual_return_time = #{actualReturnTime,jdbcType=TIMESTAMP},
      actual_use_days = #{actualUseDays,jdbcType=INTEGER},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      vehicle_vin = #{vehicleVin,jdbcType=VARCHAR},
      power_type = #{powerType,jdbcType=INTEGER},
      operate_buss_name = #{operateBussName,jdbcType=VARCHAR},
      operate_buss_code = #{operateBussCode,jdbcType=VARCHAR},
      belong_buss_code = #{belongBussCode,jdbcType=VARCHAR},
      belong_buss_name = #{belongBussName,jdbcType=VARCHAR},
      asset_city_code = #{assetCityCode,jdbcType=VARCHAR},
      asset_city_name = #{assetCityName,jdbcType=VARCHAR},
      driver_id = #{driverId,jdbcType=INTEGER},
      driver_name = #{driverName,jdbcType=VARCHAR},
      driver_mobile = #{driverMobile,jdbcType=VARCHAR},
      daily_rent = #{dailyRent,jdbcType=DECIMAL},
      demand_order_num = #{demandOrderNum,jdbcType=VARCHAR},
      customer_order_num = #{customerOrderNum,jdbcType=VARCHAR},
      order_type = #{orderType,jdbcType=INTEGER},
      order_status = #{orderStatus,jdbcType=INTEGER},
      delivery_use_name = #{deliveryUseName,jdbcType=VARCHAR},
      delivery_use_phone = #{deliveryUsePhone,jdbcType=VARCHAR},
      delivery_time = #{deliveryTime,jdbcType=TIMESTAMP},
      company_type = #{companyType,jdbcType=INTEGER},
      company_code = #{companyCode,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      change_flag = #{changeFlag,jdbcType=INTEGER},
      completed_flag = #{completedFlag,jdbcType=INTEGER},
      dispatching_flag = #{dispatchingFlag,jdbcType=INTEGER},
      returned_flag = #{returnedFlag,jdbcType=INTEGER},
      create_id = #{createId,jdbcType=INTEGER},
      create_name = #{createName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_id = #{updateId,jdbcType=INTEGER},
      update_name = #{updateName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      return_address = #{returnAddress,jdbcType=VARCHAR}
    where order_supplier_id = #{orderSupplierId,jdbcType=INTEGER}
  </update>
</mapper>