<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.OrderApplyMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.OrderApply">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="order_apply_no" jdbcType="VARCHAR" property="orderApplyNo" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="struct_id" jdbcType="INTEGER" property="structId" />
    <result column="order_type" jdbcType="TINYINT" property="orderType" />
    <result column="service_code" jdbcType="VARCHAR" property="serviceCode" />
    <result column="customer_id" jdbcType="INTEGER" property="customerId" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="customer_mobile" jdbcType="VARCHAR" property="customerMobile" />
    <result column="customer_no" jdbcType="VARCHAR" property="customerNo" />
    <result column="business_id" jdbcType="INTEGER" property="businessId" />
    <result column="business_name" jdbcType="VARCHAR" property="businessName" />
    <result column="himself" jdbcType="BIT" property="himself" />
    <result column="self_driver" jdbcType="BIT" property="selfDriver" />
    <result column="booking_passenger_user_id" jdbcType="BIGINT" property="bookingPassengerUserId" />
    <result column="booking_passenger_user_name" jdbcType="VARCHAR" property="bookingPassengerUserName" />
    <result column="booking_passenger_user_phone" jdbcType="VARCHAR" property="bookingPassengerUserPhone" />
    <result column="booking_passenger_customer_no" jdbcType="VARCHAR" property="bookingPassengerCustomerNo" />
    <result column="booking_order_stime" jdbcType="TIMESTAMP" property="bookingOrderStime" />
    <result column="booking_order_etime" jdbcType="TIMESTAMP" property="bookingOrderEtime" />
    <result column="booking_start_long_addr" jdbcType="VARCHAR" property="bookingStartLongAddr" />
    <result column="booking_start_short_addr" jdbcType="VARCHAR" property="bookingStartShortAddr" />
    <result column="booking_start_point" jdbcType="VARCHAR" property="bookingStartPoint" />
    <result column="booking_end_long_addr" jdbcType="VARCHAR" property="bookingEndLongAddr" />
    <result column="booking_end_short_addr" jdbcType="VARCHAR" property="bookingEndShortAddr" />
    <result column="booking_end_point" jdbcType="VARCHAR" property="bookingEndPoint" />
    <result column="booking_start_city_code" jdbcType="INTEGER" property="bookingStartCityCode" />
    <result column="booking_start_city_name" jdbcType="VARCHAR" property="bookingStartCityName" />
    <result column="booking_end_city_code" jdbcType="INTEGER" property="bookingEndCityCode" />
    <result column="booking_end_city_name" jdbcType="VARCHAR" property="bookingEndCityName" />
    <result column="channel_order_code" jdbcType="VARCHAR" property="channelOrderCode" />
    <result column="booking_vehicle_total_count" jdbcType="INTEGER" property="bookingVehicleTotalCount" />
    <result column="pay_type" jdbcType="TINYINT" property="payType" />
    <result column="order_detail" jdbcType="VARCHAR" property="orderDetail" />
    <result column="contact_name" jdbcType="VARCHAR" property="contactName" />
    <result column="contact_mobile" jdbcType="VARCHAR" property="contactMobile" />
    <result column="coupon_id" jdbcType="BIGINT" property="couponId" />
    <result column="order_channel_source" jdbcType="VARCHAR" property="orderChannelSource" />
    <result column="order_status" jdbcType="SMALLINT" property="orderStatus" />
    <result column="approval_status" jdbcType="SMALLINT" property="approvalStatus" />
    <result column="order_cancellation_type" jdbcType="TINYINT" property="orderCancellationType" />
    <result column="order_cancel_no" jdbcType="VARCHAR" property="orderCancelNo" />
    <result column="order_cancel_time" jdbcType="TIMESTAMP" property="orderCancelTime" />
    <result column="order_settle_status" jdbcType="TINYINT" property="orderSettleStatus" />
    <result column="order_settle_no" jdbcType="VARCHAR" property="orderSettleNo" />
    <result column="fixed_price_valid" jdbcType="BIT" property="fixedPriceValid" />
    <result column="fixed_price" jdbcType="DECIMAL" property="fixedPrice" />
    <result column="order_settle_time" jdbcType="TIMESTAMP" property="orderSettleTime" />
    <result column="traffic_type" jdbcType="TINYINT" property="trafficType" />
    <result column="traffic_number" jdbcType="VARCHAR" property="trafficNumber" />
    <result column="appraise_submited" jdbcType="BIT" property="appraiseSubmited" />
    <result column="appraise_submit_time" jdbcType="TIMESTAMP" property="appraiseSubmitTime" />
    <result column="custome_city_code" jdbcType="VARCHAR" property="customeCityCode" />
    <result column="custome_city_name" jdbcType="VARCHAR" property="customeCityName" />
    <result column="create_city_code" jdbcType="VARCHAR" property="createCityCode" />
    <result column="create_city_name" jdbcType="VARCHAR" property="createCityName" />
    <result column="customer_company_id" jdbcType="INTEGER" property="customerCompanyId" />
    <result column="customer_company_code" jdbcType="VARCHAR" property="customerCompanyCode" />
    <result column="customer_company_name" jdbcType="VARCHAR" property="customerCompanyName" />
    <result column="audit_flag" jdbcType="TINYINT" property="auditFlag" />
    <result column="estimate_distance" jdbcType="INTEGER" property="estimateDistance" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="settle_days" jdbcType="INTEGER" property="settleDays" />
    <result column="settle_company_code" jdbcType="VARCHAR" property="settleCompanyCode" />
    <result column="settle_company_name" jdbcType="VARCHAR" property="settleCompanyName" />
    <result column="settle_tax" jdbcType="DECIMAL" property="settleTax" />
    <result column="booking_passenger_count" jdbcType="INTEGER" property="bookingPassengerCount" />
    <result column="struct_name" jdbcType="VARCHAR" property="structName" />
    <result column="passenger_struct_id" jdbcType="INTEGER" property="passengerStructId" />
    <result column="passenger_struct_name" jdbcType="VARCHAR" property="passengerStructName" />
    <result column="city_out" jdbcType="BIT" property="cityOut" />
    <result column="process_instance_id" jdbcType="VARCHAR" property="processInstanceId" />
    <result column="start_address_label" jdbcType="VARCHAR" property="startAddressLabel" />
    <result column="end_address_label" jdbcType="VARCHAR" property="endAddressLabel" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="use_car_reason" jdbcType="VARCHAR" property="useCarReason" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, create_time, update_time, order_apply_no, company_id, struct_id, order_type, 
    service_code, customer_id, customer_name, customer_mobile, customer_no, business_id, 
    business_name, himself, self_driver, booking_passenger_user_id, booking_passenger_user_name, 
    booking_passenger_user_phone, booking_passenger_customer_no, booking_order_stime, 
    booking_order_etime, booking_start_long_addr, booking_start_short_addr, booking_start_point, 
    booking_end_long_addr, booking_end_short_addr, booking_end_point, booking_start_city_code, 
    booking_start_city_name, booking_end_city_code, booking_end_city_name, channel_order_code, 
    booking_vehicle_total_count, pay_type, order_detail, contact_name, contact_mobile, 
    coupon_id, order_channel_source, order_status, approval_status, order_cancellation_type, 
    order_cancel_no, order_cancel_time, order_settle_status, order_settle_no, fixed_price_valid, 
    fixed_price, order_settle_time, traffic_type, traffic_number, appraise_submited, 
    appraise_submit_time, custome_city_code, custome_city_name, create_city_code, create_city_name, 
    customer_company_id, customer_company_code, customer_company_name, audit_flag, estimate_distance, 
    company_name, settle_days, settle_company_code, settle_company_name, settle_tax, 
    booking_passenger_count, struct_name, passenger_struct_id, passenger_struct_name, 
    city_out, process_instance_id, start_address_label, end_address_label, deleted, use_car_reason
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.OrderApplyExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from order_apply
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_apply
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from order_apply
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.order.entity.mrcar.OrderApplyExample">
    delete from order_apply
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.OrderApply">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_apply (create_time, update_time, order_apply_no, 
      company_id, struct_id, order_type, 
      service_code, customer_id, customer_name, 
      customer_mobile, customer_no, business_id, 
      business_name, himself, self_driver, 
      booking_passenger_user_id, booking_passenger_user_name, 
      booking_passenger_user_phone, booking_passenger_customer_no, 
      booking_order_stime, booking_order_etime, 
      booking_start_long_addr, booking_start_short_addr, 
      booking_start_point, booking_end_long_addr, 
      booking_end_short_addr, booking_end_point, booking_start_city_code, 
      booking_start_city_name, booking_end_city_code, 
      booking_end_city_name, channel_order_code, booking_vehicle_total_count, 
      pay_type, order_detail, contact_name, 
      contact_mobile, coupon_id, order_channel_source, 
      order_status, approval_status, order_cancellation_type, 
      order_cancel_no, order_cancel_time, order_settle_status, 
      order_settle_no, fixed_price_valid, fixed_price, 
      order_settle_time, traffic_type, traffic_number, 
      appraise_submited, appraise_submit_time, custome_city_code, 
      custome_city_name, create_city_code, create_city_name, 
      customer_company_id, customer_company_code, 
      customer_company_name, audit_flag, estimate_distance, 
      company_name, settle_days, settle_company_code, 
      settle_company_name, settle_tax, booking_passenger_count, 
      struct_name, passenger_struct_id, passenger_struct_name, 
      city_out, process_instance_id, start_address_label, 
      end_address_label, deleted, use_car_reason
      )
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{orderApplyNo,jdbcType=VARCHAR}, 
      #{companyId,jdbcType=INTEGER}, #{structId,jdbcType=INTEGER}, #{orderType,jdbcType=TINYINT}, 
      #{serviceCode,jdbcType=VARCHAR}, #{customerId,jdbcType=INTEGER}, #{customerName,jdbcType=VARCHAR}, 
      #{customerMobile,jdbcType=VARCHAR}, #{customerNo,jdbcType=VARCHAR}, #{businessId,jdbcType=INTEGER}, 
      #{businessName,jdbcType=VARCHAR}, #{himself,jdbcType=BIT}, #{selfDriver,jdbcType=BIT}, 
      #{bookingPassengerUserId,jdbcType=BIGINT}, #{bookingPassengerUserName,jdbcType=VARCHAR}, 
      #{bookingPassengerUserPhone,jdbcType=VARCHAR}, #{bookingPassengerCustomerNo,jdbcType=VARCHAR}, 
      #{bookingOrderStime,jdbcType=TIMESTAMP}, #{bookingOrderEtime,jdbcType=TIMESTAMP}, 
      #{bookingStartLongAddr,jdbcType=VARCHAR}, #{bookingStartShortAddr,jdbcType=VARCHAR}, 
      #{bookingStartPoint,jdbcType=VARCHAR}, #{bookingEndLongAddr,jdbcType=VARCHAR}, 
      #{bookingEndShortAddr,jdbcType=VARCHAR}, #{bookingEndPoint,jdbcType=VARCHAR}, #{bookingStartCityCode,jdbcType=INTEGER}, 
      #{bookingStartCityName,jdbcType=VARCHAR}, #{bookingEndCityCode,jdbcType=INTEGER}, 
      #{bookingEndCityName,jdbcType=VARCHAR}, #{channelOrderCode,jdbcType=VARCHAR}, #{bookingVehicleTotalCount,jdbcType=INTEGER}, 
      #{payType,jdbcType=TINYINT}, #{orderDetail,jdbcType=VARCHAR}, #{contactName,jdbcType=VARCHAR}, 
      #{contactMobile,jdbcType=VARCHAR}, #{couponId,jdbcType=BIGINT}, #{orderChannelSource,jdbcType=VARCHAR}, 
      #{orderStatus,jdbcType=SMALLINT}, #{approvalStatus,jdbcType=SMALLINT}, #{orderCancellationType,jdbcType=TINYINT}, 
      #{orderCancelNo,jdbcType=VARCHAR}, #{orderCancelTime,jdbcType=TIMESTAMP}, #{orderSettleStatus,jdbcType=TINYINT}, 
      #{orderSettleNo,jdbcType=VARCHAR}, #{fixedPriceValid,jdbcType=BIT}, #{fixedPrice,jdbcType=DECIMAL}, 
      #{orderSettleTime,jdbcType=TIMESTAMP}, #{trafficType,jdbcType=TINYINT}, #{trafficNumber,jdbcType=VARCHAR}, 
      #{appraiseSubmited,jdbcType=BIT}, #{appraiseSubmitTime,jdbcType=TIMESTAMP}, #{customeCityCode,jdbcType=VARCHAR}, 
      #{customeCityName,jdbcType=VARCHAR}, #{createCityCode,jdbcType=VARCHAR}, #{createCityName,jdbcType=VARCHAR}, 
      #{customerCompanyId,jdbcType=INTEGER}, #{customerCompanyCode,jdbcType=VARCHAR}, 
      #{customerCompanyName,jdbcType=VARCHAR}, #{auditFlag,jdbcType=TINYINT}, #{estimateDistance,jdbcType=INTEGER}, 
      #{companyName,jdbcType=VARCHAR}, #{settleDays,jdbcType=INTEGER}, #{settleCompanyCode,jdbcType=VARCHAR}, 
      #{settleCompanyName,jdbcType=VARCHAR}, #{settleTax,jdbcType=DECIMAL}, #{bookingPassengerCount,jdbcType=INTEGER}, 
      #{structName,jdbcType=VARCHAR}, #{passengerStructId,jdbcType=INTEGER}, #{passengerStructName,jdbcType=VARCHAR}, 
      #{cityOut,jdbcType=BIT}, #{processInstanceId,jdbcType=VARCHAR}, #{startAddressLabel,jdbcType=VARCHAR}, 
      #{endAddressLabel,jdbcType=VARCHAR}, #{deleted,jdbcType=BIT}, #{useCarReason,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.OrderApply">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_apply
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="orderApplyNo != null">
        order_apply_no,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="structId != null">
        struct_id,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="serviceCode != null">
        service_code,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="customerMobile != null">
        customer_mobile,
      </if>
      <if test="customerNo != null">
        customer_no,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="businessName != null">
        business_name,
      </if>
      <if test="himself != null">
        himself,
      </if>
      <if test="selfDriver != null">
        self_driver,
      </if>
      <if test="bookingPassengerUserId != null">
        booking_passenger_user_id,
      </if>
      <if test="bookingPassengerUserName != null">
        booking_passenger_user_name,
      </if>
      <if test="bookingPassengerUserPhone != null">
        booking_passenger_user_phone,
      </if>
      <if test="bookingPassengerCustomerNo != null">
        booking_passenger_customer_no,
      </if>
      <if test="bookingOrderStime != null">
        booking_order_stime,
      </if>
      <if test="bookingOrderEtime != null">
        booking_order_etime,
      </if>
      <if test="bookingStartLongAddr != null">
        booking_start_long_addr,
      </if>
      <if test="bookingStartShortAddr != null">
        booking_start_short_addr,
      </if>
      <if test="bookingStartPoint != null">
        booking_start_point,
      </if>
      <if test="bookingEndLongAddr != null">
        booking_end_long_addr,
      </if>
      <if test="bookingEndShortAddr != null">
        booking_end_short_addr,
      </if>
      <if test="bookingEndPoint != null">
        booking_end_point,
      </if>
      <if test="bookingStartCityCode != null">
        booking_start_city_code,
      </if>
      <if test="bookingStartCityName != null">
        booking_start_city_name,
      </if>
      <if test="bookingEndCityCode != null">
        booking_end_city_code,
      </if>
      <if test="bookingEndCityName != null">
        booking_end_city_name,
      </if>
      <if test="channelOrderCode != null">
        channel_order_code,
      </if>
      <if test="bookingVehicleTotalCount != null">
        booking_vehicle_total_count,
      </if>
      <if test="payType != null">
        pay_type,
      </if>
      <if test="orderDetail != null">
        order_detail,
      </if>
      <if test="contactName != null">
        contact_name,
      </if>
      <if test="contactMobile != null">
        contact_mobile,
      </if>
      <if test="couponId != null">
        coupon_id,
      </if>
      <if test="orderChannelSource != null">
        order_channel_source,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="approvalStatus != null">
        approval_status,
      </if>
      <if test="orderCancellationType != null">
        order_cancellation_type,
      </if>
      <if test="orderCancelNo != null">
        order_cancel_no,
      </if>
      <if test="orderCancelTime != null">
        order_cancel_time,
      </if>
      <if test="orderSettleStatus != null">
        order_settle_status,
      </if>
      <if test="orderSettleNo != null">
        order_settle_no,
      </if>
      <if test="fixedPriceValid != null">
        fixed_price_valid,
      </if>
      <if test="fixedPrice != null">
        fixed_price,
      </if>
      <if test="orderSettleTime != null">
        order_settle_time,
      </if>
      <if test="trafficType != null">
        traffic_type,
      </if>
      <if test="trafficNumber != null">
        traffic_number,
      </if>
      <if test="appraiseSubmited != null">
        appraise_submited,
      </if>
      <if test="appraiseSubmitTime != null">
        appraise_submit_time,
      </if>
      <if test="customeCityCode != null">
        custome_city_code,
      </if>
      <if test="customeCityName != null">
        custome_city_name,
      </if>
      <if test="createCityCode != null">
        create_city_code,
      </if>
      <if test="createCityName != null">
        create_city_name,
      </if>
      <if test="customerCompanyId != null">
        customer_company_id,
      </if>
      <if test="customerCompanyCode != null">
        customer_company_code,
      </if>
      <if test="customerCompanyName != null">
        customer_company_name,
      </if>
      <if test="auditFlag != null">
        audit_flag,
      </if>
      <if test="estimateDistance != null">
        estimate_distance,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="settleDays != null">
        settle_days,
      </if>
      <if test="settleCompanyCode != null">
        settle_company_code,
      </if>
      <if test="settleCompanyName != null">
        settle_company_name,
      </if>
      <if test="settleTax != null">
        settle_tax,
      </if>
      <if test="bookingPassengerCount != null">
        booking_passenger_count,
      </if>
      <if test="structName != null">
        struct_name,
      </if>
      <if test="passengerStructId != null">
        passenger_struct_id,
      </if>
      <if test="passengerStructName != null">
        passenger_struct_name,
      </if>
      <if test="cityOut != null">
        city_out,
      </if>
      <if test="processInstanceId != null">
        process_instance_id,
      </if>
      <if test="startAddressLabel != null">
        start_address_label,
      </if>
      <if test="endAddressLabel != null">
        end_address_label,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="useCarReason != null">
        use_car_reason,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderApplyNo != null">
        #{orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="structId != null">
        #{structId,jdbcType=INTEGER},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=TINYINT},
      </if>
      <if test="serviceCode != null">
        #{serviceCode,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=INTEGER},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerMobile != null">
        #{customerMobile,jdbcType=VARCHAR},
      </if>
      <if test="customerNo != null">
        #{customerNo,jdbcType=VARCHAR},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="businessName != null">
        #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="himself != null">
        #{himself,jdbcType=BIT},
      </if>
      <if test="selfDriver != null">
        #{selfDriver,jdbcType=BIT},
      </if>
      <if test="bookingPassengerUserId != null">
        #{bookingPassengerUserId,jdbcType=BIGINT},
      </if>
      <if test="bookingPassengerUserName != null">
        #{bookingPassengerUserName,jdbcType=VARCHAR},
      </if>
      <if test="bookingPassengerUserPhone != null">
        #{bookingPassengerUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="bookingPassengerCustomerNo != null">
        #{bookingPassengerCustomerNo,jdbcType=VARCHAR},
      </if>
      <if test="bookingOrderStime != null">
        #{bookingOrderStime,jdbcType=TIMESTAMP},
      </if>
      <if test="bookingOrderEtime != null">
        #{bookingOrderEtime,jdbcType=TIMESTAMP},
      </if>
      <if test="bookingStartLongAddr != null">
        #{bookingStartLongAddr,jdbcType=VARCHAR},
      </if>
      <if test="bookingStartShortAddr != null">
        #{bookingStartShortAddr,jdbcType=VARCHAR},
      </if>
      <if test="bookingStartPoint != null">
        #{bookingStartPoint,jdbcType=VARCHAR},
      </if>
      <if test="bookingEndLongAddr != null">
        #{bookingEndLongAddr,jdbcType=VARCHAR},
      </if>
      <if test="bookingEndShortAddr != null">
        #{bookingEndShortAddr,jdbcType=VARCHAR},
      </if>
      <if test="bookingEndPoint != null">
        #{bookingEndPoint,jdbcType=VARCHAR},
      </if>
      <if test="bookingStartCityCode != null">
        #{bookingStartCityCode,jdbcType=INTEGER},
      </if>
      <if test="bookingStartCityName != null">
        #{bookingStartCityName,jdbcType=VARCHAR},
      </if>
      <if test="bookingEndCityCode != null">
        #{bookingEndCityCode,jdbcType=INTEGER},
      </if>
      <if test="bookingEndCityName != null">
        #{bookingEndCityName,jdbcType=VARCHAR},
      </if>
      <if test="channelOrderCode != null">
        #{channelOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="bookingVehicleTotalCount != null">
        #{bookingVehicleTotalCount,jdbcType=INTEGER},
      </if>
      <if test="payType != null">
        #{payType,jdbcType=TINYINT},
      </if>
      <if test="orderDetail != null">
        #{orderDetail,jdbcType=VARCHAR},
      </if>
      <if test="contactName != null">
        #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactMobile != null">
        #{contactMobile,jdbcType=VARCHAR},
      </if>
      <if test="couponId != null">
        #{couponId,jdbcType=BIGINT},
      </if>
      <if test="orderChannelSource != null">
        #{orderChannelSource,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=SMALLINT},
      </if>
      <if test="approvalStatus != null">
        #{approvalStatus,jdbcType=SMALLINT},
      </if>
      <if test="orderCancellationType != null">
        #{orderCancellationType,jdbcType=TINYINT},
      </if>
      <if test="orderCancelNo != null">
        #{orderCancelNo,jdbcType=VARCHAR},
      </if>
      <if test="orderCancelTime != null">
        #{orderCancelTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderSettleStatus != null">
        #{orderSettleStatus,jdbcType=TINYINT},
      </if>
      <if test="orderSettleNo != null">
        #{orderSettleNo,jdbcType=VARCHAR},
      </if>
      <if test="fixedPriceValid != null">
        #{fixedPriceValid,jdbcType=BIT},
      </if>
      <if test="fixedPrice != null">
        #{fixedPrice,jdbcType=DECIMAL},
      </if>
      <if test="orderSettleTime != null">
        #{orderSettleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="trafficType != null">
        #{trafficType,jdbcType=TINYINT},
      </if>
      <if test="trafficNumber != null">
        #{trafficNumber,jdbcType=VARCHAR},
      </if>
      <if test="appraiseSubmited != null">
        #{appraiseSubmited,jdbcType=BIT},
      </if>
      <if test="appraiseSubmitTime != null">
        #{appraiseSubmitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customeCityCode != null">
        #{customeCityCode,jdbcType=VARCHAR},
      </if>
      <if test="customeCityName != null">
        #{customeCityName,jdbcType=VARCHAR},
      </if>
      <if test="createCityCode != null">
        #{createCityCode,jdbcType=VARCHAR},
      </if>
      <if test="createCityName != null">
        #{createCityName,jdbcType=VARCHAR},
      </if>
      <if test="customerCompanyId != null">
        #{customerCompanyId,jdbcType=INTEGER},
      </if>
      <if test="customerCompanyCode != null">
        #{customerCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="customerCompanyName != null">
        #{customerCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="auditFlag != null">
        #{auditFlag,jdbcType=TINYINT},
      </if>
      <if test="estimateDistance != null">
        #{estimateDistance,jdbcType=INTEGER},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="settleDays != null">
        #{settleDays,jdbcType=INTEGER},
      </if>
      <if test="settleCompanyCode != null">
        #{settleCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="settleCompanyName != null">
        #{settleCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="settleTax != null">
        #{settleTax,jdbcType=DECIMAL},
      </if>
      <if test="bookingPassengerCount != null">
        #{bookingPassengerCount,jdbcType=INTEGER},
      </if>
      <if test="structName != null">
        #{structName,jdbcType=VARCHAR},
      </if>
      <if test="passengerStructId != null">
        #{passengerStructId,jdbcType=INTEGER},
      </if>
      <if test="passengerStructName != null">
        #{passengerStructName,jdbcType=VARCHAR},
      </if>
      <if test="cityOut != null">
        #{cityOut,jdbcType=BIT},
      </if>
      <if test="processInstanceId != null">
        #{processInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="startAddressLabel != null">
        #{startAddressLabel,jdbcType=VARCHAR},
      </if>
      <if test="endAddressLabel != null">
        #{endAddressLabel,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="useCarReason != null">
        #{useCarReason,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.OrderApplyExample" resultType="java.lang.Long">
    select count(*) from order_apply
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update order_apply
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=INTEGER},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.orderApplyNo != null">
        order_apply_no = #{row.orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="row.companyId != null">
        company_id = #{row.companyId,jdbcType=INTEGER},
      </if>
      <if test="row.structId != null">
        struct_id = #{row.structId,jdbcType=INTEGER},
      </if>
      <if test="row.orderType != null">
        order_type = #{row.orderType,jdbcType=TINYINT},
      </if>
      <if test="row.serviceCode != null">
        service_code = #{row.serviceCode,jdbcType=VARCHAR},
      </if>
      <if test="row.customerId != null">
        customer_id = #{row.customerId,jdbcType=INTEGER},
      </if>
      <if test="row.customerName != null">
        customer_name = #{row.customerName,jdbcType=VARCHAR},
      </if>
      <if test="row.customerMobile != null">
        customer_mobile = #{row.customerMobile,jdbcType=VARCHAR},
      </if>
      <if test="row.customerNo != null">
        customer_no = #{row.customerNo,jdbcType=VARCHAR},
      </if>
      <if test="row.businessId != null">
        business_id = #{row.businessId,jdbcType=INTEGER},
      </if>
      <if test="row.businessName != null">
        business_name = #{row.businessName,jdbcType=VARCHAR},
      </if>
      <if test="row.himself != null">
        himself = #{row.himself,jdbcType=BIT},
      </if>
      <if test="row.selfDriver != null">
        self_driver = #{row.selfDriver,jdbcType=BIT},
      </if>
      <if test="row.bookingPassengerUserId != null">
        booking_passenger_user_id = #{row.bookingPassengerUserId,jdbcType=BIGINT},
      </if>
      <if test="row.bookingPassengerUserName != null">
        booking_passenger_user_name = #{row.bookingPassengerUserName,jdbcType=VARCHAR},
      </if>
      <if test="row.bookingPassengerUserPhone != null">
        booking_passenger_user_phone = #{row.bookingPassengerUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="row.bookingPassengerCustomerNo != null">
        booking_passenger_customer_no = #{row.bookingPassengerCustomerNo,jdbcType=VARCHAR},
      </if>
      <if test="row.bookingOrderStime != null">
        booking_order_stime = #{row.bookingOrderStime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.bookingOrderEtime != null">
        booking_order_etime = #{row.bookingOrderEtime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.bookingStartLongAddr != null">
        booking_start_long_addr = #{row.bookingStartLongAddr,jdbcType=VARCHAR},
      </if>
      <if test="row.bookingStartShortAddr != null">
        booking_start_short_addr = #{row.bookingStartShortAddr,jdbcType=VARCHAR},
      </if>
      <if test="row.bookingStartPoint != null">
        booking_start_point = #{row.bookingStartPoint,jdbcType=VARCHAR},
      </if>
      <if test="row.bookingEndLongAddr != null">
        booking_end_long_addr = #{row.bookingEndLongAddr,jdbcType=VARCHAR},
      </if>
      <if test="row.bookingEndShortAddr != null">
        booking_end_short_addr = #{row.bookingEndShortAddr,jdbcType=VARCHAR},
      </if>
      <if test="row.bookingEndPoint != null">
        booking_end_point = #{row.bookingEndPoint,jdbcType=VARCHAR},
      </if>
      <if test="row.bookingStartCityCode != null">
        booking_start_city_code = #{row.bookingStartCityCode,jdbcType=INTEGER},
      </if>
      <if test="row.bookingStartCityName != null">
        booking_start_city_name = #{row.bookingStartCityName,jdbcType=VARCHAR},
      </if>
      <if test="row.bookingEndCityCode != null">
        booking_end_city_code = #{row.bookingEndCityCode,jdbcType=INTEGER},
      </if>
      <if test="row.bookingEndCityName != null">
        booking_end_city_name = #{row.bookingEndCityName,jdbcType=VARCHAR},
      </if>
      <if test="row.channelOrderCode != null">
        channel_order_code = #{row.channelOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="row.bookingVehicleTotalCount != null">
        booking_vehicle_total_count = #{row.bookingVehicleTotalCount,jdbcType=INTEGER},
      </if>
      <if test="row.payType != null">
        pay_type = #{row.payType,jdbcType=TINYINT},
      </if>
      <if test="row.orderDetail != null">
        order_detail = #{row.orderDetail,jdbcType=VARCHAR},
      </if>
      <if test="row.contactName != null">
        contact_name = #{row.contactName,jdbcType=VARCHAR},
      </if>
      <if test="row.contactMobile != null">
        contact_mobile = #{row.contactMobile,jdbcType=VARCHAR},
      </if>
      <if test="row.couponId != null">
        coupon_id = #{row.couponId,jdbcType=BIGINT},
      </if>
      <if test="row.orderChannelSource != null">
        order_channel_source = #{row.orderChannelSource,jdbcType=VARCHAR},
      </if>
      <if test="row.orderStatus != null">
        order_status = #{row.orderStatus,jdbcType=SMALLINT},
      </if>
      <if test="row.approvalStatus != null">
        approval_status = #{row.approvalStatus,jdbcType=SMALLINT},
      </if>
      <if test="row.orderCancellationType != null">
        order_cancellation_type = #{row.orderCancellationType,jdbcType=TINYINT},
      </if>
      <if test="row.orderCancelNo != null">
        order_cancel_no = #{row.orderCancelNo,jdbcType=VARCHAR},
      </if>
      <if test="row.orderCancelTime != null">
        order_cancel_time = #{row.orderCancelTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.orderSettleStatus != null">
        order_settle_status = #{row.orderSettleStatus,jdbcType=TINYINT},
      </if>
      <if test="row.orderSettleNo != null">
        order_settle_no = #{row.orderSettleNo,jdbcType=VARCHAR},
      </if>
      <if test="row.fixedPriceValid != null">
        fixed_price_valid = #{row.fixedPriceValid,jdbcType=BIT},
      </if>
      <if test="row.fixedPrice != null">
        fixed_price = #{row.fixedPrice,jdbcType=DECIMAL},
      </if>
      <if test="row.orderSettleTime != null">
        order_settle_time = #{row.orderSettleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.trafficType != null">
        traffic_type = #{row.trafficType,jdbcType=TINYINT},
      </if>
      <if test="row.trafficNumber != null">
        traffic_number = #{row.trafficNumber,jdbcType=VARCHAR},
      </if>
      <if test="row.appraiseSubmited != null">
        appraise_submited = #{row.appraiseSubmited,jdbcType=BIT},
      </if>
      <if test="row.appraiseSubmitTime != null">
        appraise_submit_time = #{row.appraiseSubmitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.customeCityCode != null">
        custome_city_code = #{row.customeCityCode,jdbcType=VARCHAR},
      </if>
      <if test="row.customeCityName != null">
        custome_city_name = #{row.customeCityName,jdbcType=VARCHAR},
      </if>
      <if test="row.createCityCode != null">
        create_city_code = #{row.createCityCode,jdbcType=VARCHAR},
      </if>
      <if test="row.createCityName != null">
        create_city_name = #{row.createCityName,jdbcType=VARCHAR},
      </if>
      <if test="row.customerCompanyId != null">
        customer_company_id = #{row.customerCompanyId,jdbcType=INTEGER},
      </if>
      <if test="row.customerCompanyCode != null">
        customer_company_code = #{row.customerCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="row.customerCompanyName != null">
        customer_company_name = #{row.customerCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="row.auditFlag != null">
        audit_flag = #{row.auditFlag,jdbcType=TINYINT},
      </if>
      <if test="row.estimateDistance != null">
        estimate_distance = #{row.estimateDistance,jdbcType=INTEGER},
      </if>
      <if test="row.companyName != null">
        company_name = #{row.companyName,jdbcType=VARCHAR},
      </if>
      <if test="row.settleDays != null">
        settle_days = #{row.settleDays,jdbcType=INTEGER},
      </if>
      <if test="row.settleCompanyCode != null">
        settle_company_code = #{row.settleCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="row.settleCompanyName != null">
        settle_company_name = #{row.settleCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="row.settleTax != null">
        settle_tax = #{row.settleTax,jdbcType=DECIMAL},
      </if>
      <if test="row.bookingPassengerCount != null">
        booking_passenger_count = #{row.bookingPassengerCount,jdbcType=INTEGER},
      </if>
      <if test="row.structName != null">
        struct_name = #{row.structName,jdbcType=VARCHAR},
      </if>
      <if test="row.passengerStructId != null">
        passenger_struct_id = #{row.passengerStructId,jdbcType=INTEGER},
      </if>
      <if test="row.passengerStructName != null">
        passenger_struct_name = #{row.passengerStructName,jdbcType=VARCHAR},
      </if>
      <if test="row.cityOut != null">
        city_out = #{row.cityOut,jdbcType=BIT},
      </if>
      <if test="row.processInstanceId != null">
        process_instance_id = #{row.processInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="row.startAddressLabel != null">
        start_address_label = #{row.startAddressLabel,jdbcType=VARCHAR},
      </if>
      <if test="row.endAddressLabel != null">
        end_address_label = #{row.endAddressLabel,jdbcType=VARCHAR},
      </if>
      <if test="row.deleted != null">
        deleted = #{row.deleted,jdbcType=BIT},
      </if>
      <if test="row.useCarReason != null">
        use_car_reason = #{row.useCarReason,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update order_apply
    set id = #{row.id,jdbcType=INTEGER},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      order_apply_no = #{row.orderApplyNo,jdbcType=VARCHAR},
      company_id = #{row.companyId,jdbcType=INTEGER},
      struct_id = #{row.structId,jdbcType=INTEGER},
      order_type = #{row.orderType,jdbcType=TINYINT},
      service_code = #{row.serviceCode,jdbcType=VARCHAR},
      customer_id = #{row.customerId,jdbcType=INTEGER},
      customer_name = #{row.customerName,jdbcType=VARCHAR},
      customer_mobile = #{row.customerMobile,jdbcType=VARCHAR},
      customer_no = #{row.customerNo,jdbcType=VARCHAR},
      business_id = #{row.businessId,jdbcType=INTEGER},
      business_name = #{row.businessName,jdbcType=VARCHAR},
      himself = #{row.himself,jdbcType=BIT},
      self_driver = #{row.selfDriver,jdbcType=BIT},
      booking_passenger_user_id = #{row.bookingPassengerUserId,jdbcType=BIGINT},
      booking_passenger_user_name = #{row.bookingPassengerUserName,jdbcType=VARCHAR},
      booking_passenger_user_phone = #{row.bookingPassengerUserPhone,jdbcType=VARCHAR},
      booking_passenger_customer_no = #{row.bookingPassengerCustomerNo,jdbcType=VARCHAR},
      booking_order_stime = #{row.bookingOrderStime,jdbcType=TIMESTAMP},
      booking_order_etime = #{row.bookingOrderEtime,jdbcType=TIMESTAMP},
      booking_start_long_addr = #{row.bookingStartLongAddr,jdbcType=VARCHAR},
      booking_start_short_addr = #{row.bookingStartShortAddr,jdbcType=VARCHAR},
      booking_start_point = #{row.bookingStartPoint,jdbcType=VARCHAR},
      booking_end_long_addr = #{row.bookingEndLongAddr,jdbcType=VARCHAR},
      booking_end_short_addr = #{row.bookingEndShortAddr,jdbcType=VARCHAR},
      booking_end_point = #{row.bookingEndPoint,jdbcType=VARCHAR},
      booking_start_city_code = #{row.bookingStartCityCode,jdbcType=INTEGER},
      booking_start_city_name = #{row.bookingStartCityName,jdbcType=VARCHAR},
      booking_end_city_code = #{row.bookingEndCityCode,jdbcType=INTEGER},
      booking_end_city_name = #{row.bookingEndCityName,jdbcType=VARCHAR},
      channel_order_code = #{row.channelOrderCode,jdbcType=VARCHAR},
      booking_vehicle_total_count = #{row.bookingVehicleTotalCount,jdbcType=INTEGER},
      pay_type = #{row.payType,jdbcType=TINYINT},
      order_detail = #{row.orderDetail,jdbcType=VARCHAR},
      contact_name = #{row.contactName,jdbcType=VARCHAR},
      contact_mobile = #{row.contactMobile,jdbcType=VARCHAR},
      coupon_id = #{row.couponId,jdbcType=BIGINT},
      order_channel_source = #{row.orderChannelSource,jdbcType=VARCHAR},
      order_status = #{row.orderStatus,jdbcType=SMALLINT},
      approval_status = #{row.approvalStatus,jdbcType=SMALLINT},
      order_cancellation_type = #{row.orderCancellationType,jdbcType=TINYINT},
      order_cancel_no = #{row.orderCancelNo,jdbcType=VARCHAR},
      order_cancel_time = #{row.orderCancelTime,jdbcType=TIMESTAMP},
      order_settle_status = #{row.orderSettleStatus,jdbcType=TINYINT},
      order_settle_no = #{row.orderSettleNo,jdbcType=VARCHAR},
      fixed_price_valid = #{row.fixedPriceValid,jdbcType=BIT},
      fixed_price = #{row.fixedPrice,jdbcType=DECIMAL},
      order_settle_time = #{row.orderSettleTime,jdbcType=TIMESTAMP},
      traffic_type = #{row.trafficType,jdbcType=TINYINT},
      traffic_number = #{row.trafficNumber,jdbcType=VARCHAR},
      appraise_submited = #{row.appraiseSubmited,jdbcType=BIT},
      appraise_submit_time = #{row.appraiseSubmitTime,jdbcType=TIMESTAMP},
      custome_city_code = #{row.customeCityCode,jdbcType=VARCHAR},
      custome_city_name = #{row.customeCityName,jdbcType=VARCHAR},
      create_city_code = #{row.createCityCode,jdbcType=VARCHAR},
      create_city_name = #{row.createCityName,jdbcType=VARCHAR},
      customer_company_id = #{row.customerCompanyId,jdbcType=INTEGER},
      customer_company_code = #{row.customerCompanyCode,jdbcType=VARCHAR},
      customer_company_name = #{row.customerCompanyName,jdbcType=VARCHAR},
      audit_flag = #{row.auditFlag,jdbcType=TINYINT},
      estimate_distance = #{row.estimateDistance,jdbcType=INTEGER},
      company_name = #{row.companyName,jdbcType=VARCHAR},
      settle_days = #{row.settleDays,jdbcType=INTEGER},
      settle_company_code = #{row.settleCompanyCode,jdbcType=VARCHAR},
      settle_company_name = #{row.settleCompanyName,jdbcType=VARCHAR},
      settle_tax = #{row.settleTax,jdbcType=DECIMAL},
      booking_passenger_count = #{row.bookingPassengerCount,jdbcType=INTEGER},
      struct_name = #{row.structName,jdbcType=VARCHAR},
      passenger_struct_id = #{row.passengerStructId,jdbcType=INTEGER},
      passenger_struct_name = #{row.passengerStructName,jdbcType=VARCHAR},
      city_out = #{row.cityOut,jdbcType=BIT},
      process_instance_id = #{row.processInstanceId,jdbcType=VARCHAR},
      start_address_label = #{row.startAddressLabel,jdbcType=VARCHAR},
      end_address_label = #{row.endAddressLabel,jdbcType=VARCHAR},
      deleted = #{row.deleted,jdbcType=BIT},
      use_car_reason = #{row.useCarReason,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.OrderApply">
    update order_apply
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderApplyNo != null">
        order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="structId != null">
        struct_id = #{structId,jdbcType=INTEGER},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=TINYINT},
      </if>
      <if test="serviceCode != null">
        service_code = #{serviceCode,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=INTEGER},
      </if>
      <if test="customerName != null">
        customer_name = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="customerMobile != null">
        customer_mobile = #{customerMobile,jdbcType=VARCHAR},
      </if>
      <if test="customerNo != null">
        customer_no = #{customerNo,jdbcType=VARCHAR},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="businessName != null">
        business_name = #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="himself != null">
        himself = #{himself,jdbcType=BIT},
      </if>
      <if test="selfDriver != null">
        self_driver = #{selfDriver,jdbcType=BIT},
      </if>
      <if test="bookingPassengerUserId != null">
        booking_passenger_user_id = #{bookingPassengerUserId,jdbcType=BIGINT},
      </if>
      <if test="bookingPassengerUserName != null">
        booking_passenger_user_name = #{bookingPassengerUserName,jdbcType=VARCHAR},
      </if>
      <if test="bookingPassengerUserPhone != null">
        booking_passenger_user_phone = #{bookingPassengerUserPhone,jdbcType=VARCHAR},
      </if>
      <if test="bookingPassengerCustomerNo != null">
        booking_passenger_customer_no = #{bookingPassengerCustomerNo,jdbcType=VARCHAR},
      </if>
      <if test="bookingOrderStime != null">
        booking_order_stime = #{bookingOrderStime,jdbcType=TIMESTAMP},
      </if>
      <if test="bookingOrderEtime != null">
        booking_order_etime = #{bookingOrderEtime,jdbcType=TIMESTAMP},
      </if>
      <if test="bookingStartLongAddr != null">
        booking_start_long_addr = #{bookingStartLongAddr,jdbcType=VARCHAR},
      </if>
      <if test="bookingStartShortAddr != null">
        booking_start_short_addr = #{bookingStartShortAddr,jdbcType=VARCHAR},
      </if>
      <if test="bookingStartPoint != null">
        booking_start_point = #{bookingStartPoint,jdbcType=VARCHAR},
      </if>
      <if test="bookingEndLongAddr != null">
        booking_end_long_addr = #{bookingEndLongAddr,jdbcType=VARCHAR},
      </if>
      <if test="bookingEndShortAddr != null">
        booking_end_short_addr = #{bookingEndShortAddr,jdbcType=VARCHAR},
      </if>
      <if test="bookingEndPoint != null">
        booking_end_point = #{bookingEndPoint,jdbcType=VARCHAR},
      </if>
      <if test="bookingStartCityCode != null">
        booking_start_city_code = #{bookingStartCityCode,jdbcType=INTEGER},
      </if>
      <if test="bookingStartCityName != null">
        booking_start_city_name = #{bookingStartCityName,jdbcType=VARCHAR},
      </if>
      <if test="bookingEndCityCode != null">
        booking_end_city_code = #{bookingEndCityCode,jdbcType=INTEGER},
      </if>
      <if test="bookingEndCityName != null">
        booking_end_city_name = #{bookingEndCityName,jdbcType=VARCHAR},
      </if>
      <if test="channelOrderCode != null">
        channel_order_code = #{channelOrderCode,jdbcType=VARCHAR},
      </if>
      <if test="bookingVehicleTotalCount != null">
        booking_vehicle_total_count = #{bookingVehicleTotalCount,jdbcType=INTEGER},
      </if>
      <if test="payType != null">
        pay_type = #{payType,jdbcType=TINYINT},
      </if>
      <if test="orderDetail != null">
        order_detail = #{orderDetail,jdbcType=VARCHAR},
      </if>
      <if test="contactName != null">
        contact_name = #{contactName,jdbcType=VARCHAR},
      </if>
      <if test="contactMobile != null">
        contact_mobile = #{contactMobile,jdbcType=VARCHAR},
      </if>
      <if test="couponId != null">
        coupon_id = #{couponId,jdbcType=BIGINT},
      </if>
      <if test="orderChannelSource != null">
        order_channel_source = #{orderChannelSource,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=SMALLINT},
      </if>
      <if test="approvalStatus != null">
        approval_status = #{approvalStatus,jdbcType=SMALLINT},
      </if>
      <if test="orderCancellationType != null">
        order_cancellation_type = #{orderCancellationType,jdbcType=TINYINT},
      </if>
      <if test="orderCancelNo != null">
        order_cancel_no = #{orderCancelNo,jdbcType=VARCHAR},
      </if>
      <if test="orderCancelTime != null">
        order_cancel_time = #{orderCancelTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderSettleStatus != null">
        order_settle_status = #{orderSettleStatus,jdbcType=TINYINT},
      </if>
      <if test="orderSettleNo != null">
        order_settle_no = #{orderSettleNo,jdbcType=VARCHAR},
      </if>
      <if test="fixedPriceValid != null">
        fixed_price_valid = #{fixedPriceValid,jdbcType=BIT},
      </if>
      <if test="fixedPrice != null">
        fixed_price = #{fixedPrice,jdbcType=DECIMAL},
      </if>
      <if test="orderSettleTime != null">
        order_settle_time = #{orderSettleTime,jdbcType=TIMESTAMP},
      </if>
      <if test="trafficType != null">
        traffic_type = #{trafficType,jdbcType=TINYINT},
      </if>
      <if test="trafficNumber != null">
        traffic_number = #{trafficNumber,jdbcType=VARCHAR},
      </if>
      <if test="appraiseSubmited != null">
        appraise_submited = #{appraiseSubmited,jdbcType=BIT},
      </if>
      <if test="appraiseSubmitTime != null">
        appraise_submit_time = #{appraiseSubmitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customeCityCode != null">
        custome_city_code = #{customeCityCode,jdbcType=VARCHAR},
      </if>
      <if test="customeCityName != null">
        custome_city_name = #{customeCityName,jdbcType=VARCHAR},
      </if>
      <if test="createCityCode != null">
        create_city_code = #{createCityCode,jdbcType=VARCHAR},
      </if>
      <if test="createCityName != null">
        create_city_name = #{createCityName,jdbcType=VARCHAR},
      </if>
      <if test="customerCompanyId != null">
        customer_company_id = #{customerCompanyId,jdbcType=INTEGER},
      </if>
      <if test="customerCompanyCode != null">
        customer_company_code = #{customerCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="customerCompanyName != null">
        customer_company_name = #{customerCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="auditFlag != null">
        audit_flag = #{auditFlag,jdbcType=TINYINT},
      </if>
      <if test="estimateDistance != null">
        estimate_distance = #{estimateDistance,jdbcType=INTEGER},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="settleDays != null">
        settle_days = #{settleDays,jdbcType=INTEGER},
      </if>
      <if test="settleCompanyCode != null">
        settle_company_code = #{settleCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="settleCompanyName != null">
        settle_company_name = #{settleCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="settleTax != null">
        settle_tax = #{settleTax,jdbcType=DECIMAL},
      </if>
      <if test="bookingPassengerCount != null">
        booking_passenger_count = #{bookingPassengerCount,jdbcType=INTEGER},
      </if>
      <if test="structName != null">
        struct_name = #{structName,jdbcType=VARCHAR},
      </if>
      <if test="passengerStructId != null">
        passenger_struct_id = #{passengerStructId,jdbcType=INTEGER},
      </if>
      <if test="passengerStructName != null">
        passenger_struct_name = #{passengerStructName,jdbcType=VARCHAR},
      </if>
      <if test="cityOut != null">
        city_out = #{cityOut,jdbcType=BIT},
      </if>
      <if test="processInstanceId != null">
        process_instance_id = #{processInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="startAddressLabel != null">
        start_address_label = #{startAddressLabel,jdbcType=VARCHAR},
      </if>
      <if test="endAddressLabel != null">
        end_address_label = #{endAddressLabel,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="useCarReason != null">
        use_car_reason = #{useCarReason,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.OrderApply">
    update order_apply
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=INTEGER},
      struct_id = #{structId,jdbcType=INTEGER},
      order_type = #{orderType,jdbcType=TINYINT},
      service_code = #{serviceCode,jdbcType=VARCHAR},
      customer_id = #{customerId,jdbcType=INTEGER},
      customer_name = #{customerName,jdbcType=VARCHAR},
      customer_mobile = #{customerMobile,jdbcType=VARCHAR},
      customer_no = #{customerNo,jdbcType=VARCHAR},
      business_id = #{businessId,jdbcType=INTEGER},
      business_name = #{businessName,jdbcType=VARCHAR},
      himself = #{himself,jdbcType=BIT},
      self_driver = #{selfDriver,jdbcType=BIT},
      booking_passenger_user_id = #{bookingPassengerUserId,jdbcType=BIGINT},
      booking_passenger_user_name = #{bookingPassengerUserName,jdbcType=VARCHAR},
      booking_passenger_user_phone = #{bookingPassengerUserPhone,jdbcType=VARCHAR},
      booking_passenger_customer_no = #{bookingPassengerCustomerNo,jdbcType=VARCHAR},
      booking_order_stime = #{bookingOrderStime,jdbcType=TIMESTAMP},
      booking_order_etime = #{bookingOrderEtime,jdbcType=TIMESTAMP},
      booking_start_long_addr = #{bookingStartLongAddr,jdbcType=VARCHAR},
      booking_start_short_addr = #{bookingStartShortAddr,jdbcType=VARCHAR},
      booking_start_point = #{bookingStartPoint,jdbcType=VARCHAR},
      booking_end_long_addr = #{bookingEndLongAddr,jdbcType=VARCHAR},
      booking_end_short_addr = #{bookingEndShortAddr,jdbcType=VARCHAR},
      booking_end_point = #{bookingEndPoint,jdbcType=VARCHAR},
      booking_start_city_code = #{bookingStartCityCode,jdbcType=INTEGER},
      booking_start_city_name = #{bookingStartCityName,jdbcType=VARCHAR},
      booking_end_city_code = #{bookingEndCityCode,jdbcType=INTEGER},
      booking_end_city_name = #{bookingEndCityName,jdbcType=VARCHAR},
      channel_order_code = #{channelOrderCode,jdbcType=VARCHAR},
      booking_vehicle_total_count = #{bookingVehicleTotalCount,jdbcType=INTEGER},
      pay_type = #{payType,jdbcType=TINYINT},
      order_detail = #{orderDetail,jdbcType=VARCHAR},
      contact_name = #{contactName,jdbcType=VARCHAR},
      contact_mobile = #{contactMobile,jdbcType=VARCHAR},
      coupon_id = #{couponId,jdbcType=BIGINT},
      order_channel_source = #{orderChannelSource,jdbcType=VARCHAR},
      order_status = #{orderStatus,jdbcType=SMALLINT},
      approval_status = #{approvalStatus,jdbcType=SMALLINT},
      order_cancellation_type = #{orderCancellationType,jdbcType=TINYINT},
      order_cancel_no = #{orderCancelNo,jdbcType=VARCHAR},
      order_cancel_time = #{orderCancelTime,jdbcType=TIMESTAMP},
      order_settle_status = #{orderSettleStatus,jdbcType=TINYINT},
      order_settle_no = #{orderSettleNo,jdbcType=VARCHAR},
      fixed_price_valid = #{fixedPriceValid,jdbcType=BIT},
      fixed_price = #{fixedPrice,jdbcType=DECIMAL},
      order_settle_time = #{orderSettleTime,jdbcType=TIMESTAMP},
      traffic_type = #{trafficType,jdbcType=TINYINT},
      traffic_number = #{trafficNumber,jdbcType=VARCHAR},
      appraise_submited = #{appraiseSubmited,jdbcType=BIT},
      appraise_submit_time = #{appraiseSubmitTime,jdbcType=TIMESTAMP},
      custome_city_code = #{customeCityCode,jdbcType=VARCHAR},
      custome_city_name = #{customeCityName,jdbcType=VARCHAR},
      create_city_code = #{createCityCode,jdbcType=VARCHAR},
      create_city_name = #{createCityName,jdbcType=VARCHAR},
      customer_company_id = #{customerCompanyId,jdbcType=INTEGER},
      customer_company_code = #{customerCompanyCode,jdbcType=VARCHAR},
      customer_company_name = #{customerCompanyName,jdbcType=VARCHAR},
      audit_flag = #{auditFlag,jdbcType=TINYINT},
      estimate_distance = #{estimateDistance,jdbcType=INTEGER},
      company_name = #{companyName,jdbcType=VARCHAR},
      settle_days = #{settleDays,jdbcType=INTEGER},
      settle_company_code = #{settleCompanyCode,jdbcType=VARCHAR},
      settle_company_name = #{settleCompanyName,jdbcType=VARCHAR},
      settle_tax = #{settleTax,jdbcType=DECIMAL},
      booking_passenger_count = #{bookingPassengerCount,jdbcType=INTEGER},
      struct_name = #{structName,jdbcType=VARCHAR},
      passenger_struct_id = #{passengerStructId,jdbcType=INTEGER},
      passenger_struct_name = #{passengerStructName,jdbcType=VARCHAR},
      city_out = #{cityOut,jdbcType=BIT},
      process_instance_id = #{processInstanceId,jdbcType=VARCHAR},
      start_address_label = #{startAddressLabel,jdbcType=VARCHAR},
      end_address_label = #{endAddressLabel,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=BIT},
      use_car_reason = #{useCarReason,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>