<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.OrderPriceSnapshotMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.OrderPriceSnapshot">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="policy_id" jdbcType="INTEGER" property="policyId" />
    <result column="policy_name" jdbcType="VARCHAR" property="policyName" />
    <result column="fixed_price_valid" jdbcType="BIT" property="fixedPriceValid" />
    <result column="fixed_price" jdbcType="DECIMAL" property="fixedPrice" />
    <result column="policy_type" jdbcType="TINYINT" property="policyType" />
    <result column="charge_type" jdbcType="TINYINT" property="chargeType" />
    <result column="policy_type_price" jdbcType="TINYINT" property="policyTypePrice" />
    <result column="policy_price" jdbcType="DECIMAL" property="policyPrice" />
    <result column="base_fee_type" jdbcType="TINYINT" property="baseFeeType" />
    <result column="section_stime" jdbcType="TINYINT" property="sectionStime" />
    <result column="section_etime" jdbcType="TINYINT" property="sectionEtime" />
    <result column="base_fee" jdbcType="DECIMAL" property="baseFee" />
    <result column="base_mileage" jdbcType="DECIMAL" property="baseMileage" />
    <result column="base_time" jdbcType="DECIMAL" property="baseTime" />
    <result column="base_mileage_fee" jdbcType="DECIMAL" property="baseMileageFee" />
    <result column="base_time_fee" jdbcType="DECIMAL" property="baseTimeFee" />
    <result column="over_mileage_fee" jdbcType="DECIMAL" property="overMileageFee" />
    <result column="over_time_fee" jdbcType="DECIMAL" property="overTimeFee" />
    <result column="is_night_service_fee" jdbcType="TINYINT" property="isNightServiceFee" />
    <result column="night_service_stime" jdbcType="VARCHAR" property="nightServiceStime" />
    <result column="night_service_etime" jdbcType="VARCHAR" property="nightServiceEtime" />
    <result column="night_service_price" jdbcType="DECIMAL" property="nightServicePrice" />
    <result column="return_empty_price" jdbcType="DECIMAL" property="returnEmptyPrice" />
    <result column="is_return_empty_fee" jdbcType="TINYINT" property="isReturnEmptyFee" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, order_no, policy_id, policy_name, fixed_price_valid, 
    fixed_price, policy_type, charge_type, policy_type_price, policy_price, base_fee_type, 
    section_stime, section_etime, base_fee, base_mileage, base_time, base_mileage_fee, 
    base_time_fee, over_mileage_fee, over_time_fee, is_night_service_fee, night_service_stime, 
    night_service_etime, night_service_price, return_empty_price, is_return_empty_fee
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from order_price_snapshot
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from order_price_snapshot
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.OrderPriceSnapshot">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_price_snapshot (create_time, update_time, order_no, 
      policy_id, policy_name, fixed_price_valid, 
      fixed_price, policy_type, charge_type, 
      policy_type_price, policy_price, base_fee_type, 
      section_stime, section_etime, base_fee, 
      base_mileage, base_time, base_mileage_fee, 
      base_time_fee, over_mileage_fee, over_time_fee, 
      is_night_service_fee, night_service_stime, night_service_etime, 
      night_service_price, return_empty_price, is_return_empty_fee
      )
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{orderNo,jdbcType=VARCHAR}, 
      #{policyId,jdbcType=INTEGER}, #{policyName,jdbcType=VARCHAR}, #{fixedPriceValid,jdbcType=BIT}, 
      #{fixedPrice,jdbcType=DECIMAL}, #{policyType,jdbcType=TINYINT}, #{chargeType,jdbcType=TINYINT}, 
      #{policyTypePrice,jdbcType=TINYINT}, #{policyPrice,jdbcType=DECIMAL}, #{baseFeeType,jdbcType=TINYINT}, 
      #{sectionStime,jdbcType=TINYINT}, #{sectionEtime,jdbcType=TINYINT}, #{baseFee,jdbcType=DECIMAL}, 
      #{baseMileage,jdbcType=DECIMAL}, #{baseTime,jdbcType=DECIMAL}, #{baseMileageFee,jdbcType=DECIMAL}, 
      #{baseTimeFee,jdbcType=DECIMAL}, #{overMileageFee,jdbcType=DECIMAL}, #{overTimeFee,jdbcType=DECIMAL}, 
      #{isNightServiceFee,jdbcType=TINYINT}, #{nightServiceStime,jdbcType=VARCHAR}, #{nightServiceEtime,jdbcType=VARCHAR}, 
      #{nightServicePrice,jdbcType=DECIMAL}, #{returnEmptyPrice,jdbcType=DECIMAL}, #{isReturnEmptyFee,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.OrderPriceSnapshot">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into order_price_snapshot
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="policyId != null">
        policy_id,
      </if>
      <if test="policyName != null">
        policy_name,
      </if>
      <if test="fixedPriceValid != null">
        fixed_price_valid,
      </if>
      <if test="fixedPrice != null">
        fixed_price,
      </if>
      <if test="policyType != null">
        policy_type,
      </if>
      <if test="chargeType != null">
        charge_type,
      </if>
      <if test="policyTypePrice != null">
        policy_type_price,
      </if>
      <if test="policyPrice != null">
        policy_price,
      </if>
      <if test="baseFeeType != null">
        base_fee_type,
      </if>
      <if test="sectionStime != null">
        section_stime,
      </if>
      <if test="sectionEtime != null">
        section_etime,
      </if>
      <if test="baseFee != null">
        base_fee,
      </if>
      <if test="baseMileage != null">
        base_mileage,
      </if>
      <if test="baseTime != null">
        base_time,
      </if>
      <if test="baseMileageFee != null">
        base_mileage_fee,
      </if>
      <if test="baseTimeFee != null">
        base_time_fee,
      </if>
      <if test="overMileageFee != null">
        over_mileage_fee,
      </if>
      <if test="overTimeFee != null">
        over_time_fee,
      </if>
      <if test="isNightServiceFee != null">
        is_night_service_fee,
      </if>
      <if test="nightServiceStime != null">
        night_service_stime,
      </if>
      <if test="nightServiceEtime != null">
        night_service_etime,
      </if>
      <if test="nightServicePrice != null">
        night_service_price,
      </if>
      <if test="returnEmptyPrice != null">
        return_empty_price,
      </if>
      <if test="isReturnEmptyFee != null">
        is_return_empty_fee,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="policyId != null">
        #{policyId,jdbcType=INTEGER},
      </if>
      <if test="policyName != null">
        #{policyName,jdbcType=VARCHAR},
      </if>
      <if test="fixedPriceValid != null">
        #{fixedPriceValid,jdbcType=BIT},
      </if>
      <if test="fixedPrice != null">
        #{fixedPrice,jdbcType=DECIMAL},
      </if>
      <if test="policyType != null">
        #{policyType,jdbcType=TINYINT},
      </if>
      <if test="chargeType != null">
        #{chargeType,jdbcType=TINYINT},
      </if>
      <if test="policyTypePrice != null">
        #{policyTypePrice,jdbcType=TINYINT},
      </if>
      <if test="policyPrice != null">
        #{policyPrice,jdbcType=DECIMAL},
      </if>
      <if test="baseFeeType != null">
        #{baseFeeType,jdbcType=TINYINT},
      </if>
      <if test="sectionStime != null">
        #{sectionStime,jdbcType=TINYINT},
      </if>
      <if test="sectionEtime != null">
        #{sectionEtime,jdbcType=TINYINT},
      </if>
      <if test="baseFee != null">
        #{baseFee,jdbcType=DECIMAL},
      </if>
      <if test="baseMileage != null">
        #{baseMileage,jdbcType=DECIMAL},
      </if>
      <if test="baseTime != null">
        #{baseTime,jdbcType=DECIMAL},
      </if>
      <if test="baseMileageFee != null">
        #{baseMileageFee,jdbcType=DECIMAL},
      </if>
      <if test="baseTimeFee != null">
        #{baseTimeFee,jdbcType=DECIMAL},
      </if>
      <if test="overMileageFee != null">
        #{overMileageFee,jdbcType=DECIMAL},
      </if>
      <if test="overTimeFee != null">
        #{overTimeFee,jdbcType=DECIMAL},
      </if>
      <if test="isNightServiceFee != null">
        #{isNightServiceFee,jdbcType=TINYINT},
      </if>
      <if test="nightServiceStime != null">
        #{nightServiceStime,jdbcType=VARCHAR},
      </if>
      <if test="nightServiceEtime != null">
        #{nightServiceEtime,jdbcType=VARCHAR},
      </if>
      <if test="nightServicePrice != null">
        #{nightServicePrice,jdbcType=DECIMAL},
      </if>
      <if test="returnEmptyPrice != null">
        #{returnEmptyPrice,jdbcType=DECIMAL},
      </if>
      <if test="isReturnEmptyFee != null">
        #{isReturnEmptyFee,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.OrderPriceSnapshot">
    update order_price_snapshot
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="policyId != null">
        policy_id = #{policyId,jdbcType=INTEGER},
      </if>
      <if test="policyName != null">
        policy_name = #{policyName,jdbcType=VARCHAR},
      </if>
      <if test="fixedPriceValid != null">
        fixed_price_valid = #{fixedPriceValid,jdbcType=BIT},
      </if>
      <if test="fixedPrice != null">
        fixed_price = #{fixedPrice,jdbcType=DECIMAL},
      </if>
      <if test="policyType != null">
        policy_type = #{policyType,jdbcType=TINYINT},
      </if>
      <if test="chargeType != null">
        charge_type = #{chargeType,jdbcType=TINYINT},
      </if>
      <if test="policyTypePrice != null">
        policy_type_price = #{policyTypePrice,jdbcType=TINYINT},
      </if>
      <if test="policyPrice != null">
        policy_price = #{policyPrice,jdbcType=DECIMAL},
      </if>
      <if test="baseFeeType != null">
        base_fee_type = #{baseFeeType,jdbcType=TINYINT},
      </if>
      <if test="sectionStime != null">
        section_stime = #{sectionStime,jdbcType=TINYINT},
      </if>
      <if test="sectionEtime != null">
        section_etime = #{sectionEtime,jdbcType=TINYINT},
      </if>
      <if test="baseFee != null">
        base_fee = #{baseFee,jdbcType=DECIMAL},
      </if>
      <if test="baseMileage != null">
        base_mileage = #{baseMileage,jdbcType=DECIMAL},
      </if>
      <if test="baseTime != null">
        base_time = #{baseTime,jdbcType=DECIMAL},
      </if>
      <if test="baseMileageFee != null">
        base_mileage_fee = #{baseMileageFee,jdbcType=DECIMAL},
      </if>
      <if test="baseTimeFee != null">
        base_time_fee = #{baseTimeFee,jdbcType=DECIMAL},
      </if>
      <if test="overMileageFee != null">
        over_mileage_fee = #{overMileageFee,jdbcType=DECIMAL},
      </if>
      <if test="overTimeFee != null">
        over_time_fee = #{overTimeFee,jdbcType=DECIMAL},
      </if>
      <if test="isNightServiceFee != null">
        is_night_service_fee = #{isNightServiceFee,jdbcType=TINYINT},
      </if>
      <if test="nightServiceStime != null">
        night_service_stime = #{nightServiceStime,jdbcType=VARCHAR},
      </if>
      <if test="nightServiceEtime != null">
        night_service_etime = #{nightServiceEtime,jdbcType=VARCHAR},
      </if>
      <if test="nightServicePrice != null">
        night_service_price = #{nightServicePrice,jdbcType=DECIMAL},
      </if>
      <if test="returnEmptyPrice != null">
        return_empty_price = #{returnEmptyPrice,jdbcType=DECIMAL},
      </if>
      <if test="isReturnEmptyFee != null">
        is_return_empty_fee = #{isReturnEmptyFee,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.OrderPriceSnapshot">
    update order_price_snapshot
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      order_no = #{orderNo,jdbcType=VARCHAR},
      policy_id = #{policyId,jdbcType=INTEGER},
      policy_name = #{policyName,jdbcType=VARCHAR},
      fixed_price_valid = #{fixedPriceValid,jdbcType=BIT},
      fixed_price = #{fixedPrice,jdbcType=DECIMAL},
      policy_type = #{policyType,jdbcType=TINYINT},
      charge_type = #{chargeType,jdbcType=TINYINT},
      policy_type_price = #{policyTypePrice,jdbcType=TINYINT},
      policy_price = #{policyPrice,jdbcType=DECIMAL},
      base_fee_type = #{baseFeeType,jdbcType=TINYINT},
      section_stime = #{sectionStime,jdbcType=TINYINT},
      section_etime = #{sectionEtime,jdbcType=TINYINT},
      base_fee = #{baseFee,jdbcType=DECIMAL},
      base_mileage = #{baseMileage,jdbcType=DECIMAL},
      base_time = #{baseTime,jdbcType=DECIMAL},
      base_mileage_fee = #{baseMileageFee,jdbcType=DECIMAL},
      base_time_fee = #{baseTimeFee,jdbcType=DECIMAL},
      over_mileage_fee = #{overMileageFee,jdbcType=DECIMAL},
      over_time_fee = #{overTimeFee,jdbcType=DECIMAL},
      is_night_service_fee = #{isNightServiceFee,jdbcType=TINYINT},
      night_service_stime = #{nightServiceStime,jdbcType=VARCHAR},
      night_service_etime = #{nightServiceEtime,jdbcType=VARCHAR},
      night_service_price = #{nightServicePrice,jdbcType=DECIMAL},
      return_empty_price = #{returnEmptyPrice,jdbcType=DECIMAL},
      is_return_empty_fee = #{isReturnEmptyFee,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>