<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.GovCarOrderContractMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.GovCarOrderContract">
    <id column="contract_id" jdbcType="INTEGER" property="contractId" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="contract_no" jdbcType="VARCHAR" property="contractNo" />
    <result column="contract_type" jdbcType="TINYINT" property="contractType" />
    <result column="signer_status" jdbcType="TINYINT" property="signerStatus" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="user_mobile" jdbcType="VARCHAR" property="userMobile" />
    <result column="user_account" jdbcType="VARCHAR" property="userAccount" />
    <result column="branch_office_id" jdbcType="INTEGER" property="branchOfficeId" />
    <result column="branch_office_name" jdbcType="VARCHAR" property="branchOfficeName" />
    <result column="branch_office_account" jdbcType="VARCHAR" property="branchOfficeAccount" />
    <result column="contract_url" jdbcType="VARCHAR" property="contractUrl" />
    <result column="tid" jdbcType="VARCHAR" property="tid" />
    <result column="sign_time" jdbcType="TIMESTAMP" property="signTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    contract_id, order_no, contract_no, contract_type, signer_status, user_id, user_mobile, 
    user_account, branch_office_id, branch_office_name, branch_office_account, contract_url, 
    tid, sign_time, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.GovCarOrderContractExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from gov_car_order_contract
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from gov_car_order_contract
    where contract_id = #{contractId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from gov_car_order_contract
    where contract_id = #{contractId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.GovCarOrderContract">
    <selectKey keyProperty="contractId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gov_car_order_contract (order_no, contract_no, contract_type, 
      signer_status, user_id, user_mobile, 
      user_account, branch_office_id, branch_office_name, 
      branch_office_account, contract_url, tid, 
      sign_time, create_time, update_time
      )
    values (#{orderNo,jdbcType=VARCHAR}, #{contractNo,jdbcType=VARCHAR}, #{contractType,jdbcType=TINYINT}, 
      #{signerStatus,jdbcType=TINYINT}, #{userId,jdbcType=INTEGER}, #{userMobile,jdbcType=VARCHAR}, 
      #{userAccount,jdbcType=VARCHAR}, #{branchOfficeId,jdbcType=INTEGER}, #{branchOfficeName,jdbcType=VARCHAR}, 
      #{branchOfficeAccount,jdbcType=VARCHAR}, #{contractUrl,jdbcType=VARCHAR}, #{tid,jdbcType=VARCHAR}, 
      #{signTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.GovCarOrderContract">
    <selectKey keyProperty="contractId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into gov_car_order_contract
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="contractNo != null">
        contract_no,
      </if>
      <if test="contractType != null">
        contract_type,
      </if>
      <if test="signerStatus != null">
        signer_status,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="userMobile != null">
        user_mobile,
      </if>
      <if test="userAccount != null">
        user_account,
      </if>
      <if test="branchOfficeId != null">
        branch_office_id,
      </if>
      <if test="branchOfficeName != null">
        branch_office_name,
      </if>
      <if test="branchOfficeAccount != null">
        branch_office_account,
      </if>
      <if test="contractUrl != null">
        contract_url,
      </if>
      <if test="tid != null">
        tid,
      </if>
      <if test="signTime != null">
        sign_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="contractNo != null">
        #{contractNo,jdbcType=VARCHAR},
      </if>
      <if test="contractType != null">
        #{contractType,jdbcType=TINYINT},
      </if>
      <if test="signerStatus != null">
        #{signerStatus,jdbcType=TINYINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="userMobile != null">
        #{userMobile,jdbcType=VARCHAR},
      </if>
      <if test="userAccount != null">
        #{userAccount,jdbcType=VARCHAR},
      </if>
      <if test="branchOfficeId != null">
        #{branchOfficeId,jdbcType=INTEGER},
      </if>
      <if test="branchOfficeName != null">
        #{branchOfficeName,jdbcType=VARCHAR},
      </if>
      <if test="branchOfficeAccount != null">
        #{branchOfficeAccount,jdbcType=VARCHAR},
      </if>
      <if test="contractUrl != null">
        #{contractUrl,jdbcType=VARCHAR},
      </if>
      <if test="tid != null">
        #{tid,jdbcType=VARCHAR},
      </if>
      <if test="signTime != null">
        #{signTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.GovCarOrderContractExample" resultType="java.lang.Long">
    select count(*) from gov_car_order_contract
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update gov_car_order_contract
    <set>
      <if test="row.contractId != null">
        contract_id = #{row.contractId,jdbcType=INTEGER},
      </if>
      <if test="row.orderNo != null">
        order_no = #{row.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="row.contractNo != null">
        contract_no = #{row.contractNo,jdbcType=VARCHAR},
      </if>
      <if test="row.contractType != null">
        contract_type = #{row.contractType,jdbcType=TINYINT},
      </if>
      <if test="row.signerStatus != null">
        signer_status = #{row.signerStatus,jdbcType=TINYINT},
      </if>
      <if test="row.userId != null">
        user_id = #{row.userId,jdbcType=INTEGER},
      </if>
      <if test="row.userMobile != null">
        user_mobile = #{row.userMobile,jdbcType=VARCHAR},
      </if>
      <if test="row.userAccount != null">
        user_account = #{row.userAccount,jdbcType=VARCHAR},
      </if>
      <if test="row.branchOfficeId != null">
        branch_office_id = #{row.branchOfficeId,jdbcType=INTEGER},
      </if>
      <if test="row.branchOfficeName != null">
        branch_office_name = #{row.branchOfficeName,jdbcType=VARCHAR},
      </if>
      <if test="row.branchOfficeAccount != null">
        branch_office_account = #{row.branchOfficeAccount,jdbcType=VARCHAR},
      </if>
      <if test="row.contractUrl != null">
        contract_url = #{row.contractUrl,jdbcType=VARCHAR},
      </if>
      <if test="row.tid != null">
        tid = #{row.tid,jdbcType=VARCHAR},
      </if>
      <if test="row.signTime != null">
        sign_time = #{row.signTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update gov_car_order_contract
    set contract_id = #{row.contractId,jdbcType=INTEGER},
      order_no = #{row.orderNo,jdbcType=VARCHAR},
      contract_no = #{row.contractNo,jdbcType=VARCHAR},
      contract_type = #{row.contractType,jdbcType=TINYINT},
      signer_status = #{row.signerStatus,jdbcType=TINYINT},
      user_id = #{row.userId,jdbcType=INTEGER},
      user_mobile = #{row.userMobile,jdbcType=VARCHAR},
      user_account = #{row.userAccount,jdbcType=VARCHAR},
      branch_office_id = #{row.branchOfficeId,jdbcType=INTEGER},
      branch_office_name = #{row.branchOfficeName,jdbcType=VARCHAR},
      branch_office_account = #{row.branchOfficeAccount,jdbcType=VARCHAR},
      contract_url = #{row.contractUrl,jdbcType=VARCHAR},
      tid = #{row.tid,jdbcType=VARCHAR},
      sign_time = #{row.signTime,jdbcType=TIMESTAMP},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.GovCarOrderContract">
    update gov_car_order_contract
    <set>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="contractNo != null">
        contract_no = #{contractNo,jdbcType=VARCHAR},
      </if>
      <if test="contractType != null">
        contract_type = #{contractType,jdbcType=TINYINT},
      </if>
      <if test="signerStatus != null">
        signer_status = #{signerStatus,jdbcType=TINYINT},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="userMobile != null">
        user_mobile = #{userMobile,jdbcType=VARCHAR},
      </if>
      <if test="userAccount != null">
        user_account = #{userAccount,jdbcType=VARCHAR},
      </if>
      <if test="branchOfficeId != null">
        branch_office_id = #{branchOfficeId,jdbcType=INTEGER},
      </if>
      <if test="branchOfficeName != null">
        branch_office_name = #{branchOfficeName,jdbcType=VARCHAR},
      </if>
      <if test="branchOfficeAccount != null">
        branch_office_account = #{branchOfficeAccount,jdbcType=VARCHAR},
      </if>
      <if test="contractUrl != null">
        contract_url = #{contractUrl,jdbcType=VARCHAR},
      </if>
      <if test="tid != null">
        tid = #{tid,jdbcType=VARCHAR},
      </if>
      <if test="signTime != null">
        sign_time = #{signTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where contract_id = #{contractId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.GovCarOrderContract">
    update gov_car_order_contract
    set order_no = #{orderNo,jdbcType=VARCHAR},
      contract_no = #{contractNo,jdbcType=VARCHAR},
      contract_type = #{contractType,jdbcType=TINYINT},
      signer_status = #{signerStatus,jdbcType=TINYINT},
      user_id = #{userId,jdbcType=INTEGER},
      user_mobile = #{userMobile,jdbcType=VARCHAR},
      user_account = #{userAccount,jdbcType=VARCHAR},
      branch_office_id = #{branchOfficeId,jdbcType=INTEGER},
      branch_office_name = #{branchOfficeName,jdbcType=VARCHAR},
      branch_office_account = #{branchOfficeAccount,jdbcType=VARCHAR},
      contract_url = #{contractUrl,jdbcType=VARCHAR},
      tid = #{tid,jdbcType=VARCHAR},
      sign_time = #{signTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where contract_id = #{contractId,jdbcType=INTEGER}
  </update>
</mapper>