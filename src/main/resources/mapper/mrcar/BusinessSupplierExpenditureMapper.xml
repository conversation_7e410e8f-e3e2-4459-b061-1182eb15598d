<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.BusinessSupplierExpenditureMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.BusinessSupplierExpenditure">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="expenditure_no" jdbcType="VARCHAR" property="expenditureNo" />
    <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="should_pay_total_amount" jdbcType="DECIMAL" property="shouldPayTotalAmount" />
    <result column="should_pay_total_amount_no_rate" jdbcType="DECIMAL" property="shouldPayTotalAmountNoRate" />
    <result column="supplier_amount_total" jdbcType="DECIMAL" property="supplierAmountTotal" />
    <result column="supplier_amount_total_no_rate" jdbcType="DECIMAL" property="supplierAmountTotalNoRate" />
    <result column="supplier_rate" jdbcType="DECIMAL" property="supplierRate" />
    <result column="order_count" jdbcType="INTEGER" property="orderCount" />
    <result column="suppler_invoice_type" jdbcType="INTEGER" property="supplerInvoiceType" />
    <result column="expenditure_status" jdbcType="TINYINT" property="expenditureStatus" />
    <result column="return_rate" jdbcType="DECIMAL" property="returnRate" />
    <result column="vehicle_user_code" jdbcType="VARCHAR" property="vehicleUserCode" />
    <result column="vehicle_user_name" jdbcType="VARCHAR" property="vehicleUserName" />
    <result column="item_description" jdbcType="VARCHAR" property="itemDescription" />
    <result column="approval_suggestion" jdbcType="VARCHAR" property="approvalSuggestion" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="creator_name" jdbcType="VARCHAR" property="creatorName" />
    <result column="creator_phone" jdbcType="VARCHAR" property="creatorPhone" />
    <result column="creator_dept_code" jdbcType="VARCHAR" property="creatorDeptCode" />
    <result column="creator_dept_name" jdbcType="VARCHAR" property="creatorDeptName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_id" jdbcType="INTEGER" property="updateId" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="update_phone" jdbcType="VARCHAR" property="updatePhone" />
    <result column="update_dept_code" jdbcType="VARCHAR" property="updateDeptCode" />
    <result column="update_dept_name" jdbcType="VARCHAR" property="updateDeptName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="push_result" jdbcType="VARCHAR" property="pushResult" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, expenditure_no, supplier_code, supplier_name, should_pay_total_amount, should_pay_total_amount_no_rate, 
    supplier_amount_total, supplier_amount_total_no_rate, supplier_rate, order_count, 
    suppler_invoice_type, expenditure_status, return_rate, vehicle_user_code, vehicle_user_name, 
    item_description, approval_suggestion, creator_id, creator_name, creator_phone, creator_dept_code, 
    creator_dept_name, create_time, update_id, update_name, update_phone, update_dept_code, 
    update_dept_name, update_time, push_result
  </sql>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.BusinessSupplierExpenditureExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from business_supplier_expenditure
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from business_supplier_expenditure
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from business_supplier_expenditure
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.order.entity.mrcar.BusinessSupplierExpenditureExample">
    delete from business_supplier_expenditure
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.BusinessSupplierExpenditure">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into business_supplier_expenditure (expenditure_no, supplier_code, supplier_name, 
      should_pay_total_amount, should_pay_total_amount_no_rate, 
      supplier_amount_total, supplier_amount_total_no_rate, 
      supplier_rate, order_count, suppler_invoice_type, 
      expenditure_status, return_rate, vehicle_user_code, 
      vehicle_user_name, item_description, approval_suggestion, 
      creator_id, creator_name, creator_phone, 
      creator_dept_code, creator_dept_name, create_time, 
      update_id, update_name, update_phone, 
      update_dept_code, update_dept_name, update_time, 
      push_result)
    values (#{expenditureNo,jdbcType=VARCHAR}, #{supplierCode,jdbcType=VARCHAR}, #{supplierName,jdbcType=VARCHAR}, 
      #{shouldPayTotalAmount,jdbcType=DECIMAL}, #{shouldPayTotalAmountNoRate,jdbcType=DECIMAL}, 
      #{supplierAmountTotal,jdbcType=DECIMAL}, #{supplierAmountTotalNoRate,jdbcType=DECIMAL}, 
      #{supplierRate,jdbcType=DECIMAL}, #{orderCount,jdbcType=INTEGER}, #{supplerInvoiceType,jdbcType=INTEGER}, 
      #{expenditureStatus,jdbcType=TINYINT}, #{returnRate,jdbcType=DECIMAL}, #{vehicleUserCode,jdbcType=VARCHAR}, 
      #{vehicleUserName,jdbcType=VARCHAR}, #{itemDescription,jdbcType=VARCHAR}, #{approvalSuggestion,jdbcType=VARCHAR}, 
      #{creatorId,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{creatorPhone,jdbcType=VARCHAR}, 
      #{creatorDeptCode,jdbcType=VARCHAR}, #{creatorDeptName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateId,jdbcType=INTEGER}, #{updateName,jdbcType=VARCHAR}, #{updatePhone,jdbcType=VARCHAR}, 
      #{updateDeptCode,jdbcType=VARCHAR}, #{updateDeptName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{pushResult,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.BusinessSupplierExpenditure">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into business_supplier_expenditure
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="expenditureNo != null">
        expenditure_no,
      </if>
      <if test="supplierCode != null">
        supplier_code,
      </if>
      <if test="supplierName != null">
        supplier_name,
      </if>
      <if test="shouldPayTotalAmount != null">
        should_pay_total_amount,
      </if>
      <if test="shouldPayTotalAmountNoRate != null">
        should_pay_total_amount_no_rate,
      </if>
      <if test="supplierAmountTotal != null">
        supplier_amount_total,
      </if>
      <if test="supplierAmountTotalNoRate != null">
        supplier_amount_total_no_rate,
      </if>
      <if test="supplierRate != null">
        supplier_rate,
      </if>
      <if test="orderCount != null">
        order_count,
      </if>
      <if test="supplerInvoiceType != null">
        suppler_invoice_type,
      </if>
      <if test="expenditureStatus != null">
        expenditure_status,
      </if>
      <if test="returnRate != null">
        return_rate,
      </if>
      <if test="vehicleUserCode != null">
        vehicle_user_code,
      </if>
      <if test="vehicleUserName != null">
        vehicle_user_name,
      </if>
      <if test="itemDescription != null">
        item_description,
      </if>
      <if test="approvalSuggestion != null">
        approval_suggestion,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="creatorName != null">
        creator_name,
      </if>
      <if test="creatorPhone != null">
        creator_phone,
      </if>
      <if test="creatorDeptCode != null">
        creator_dept_code,
      </if>
      <if test="creatorDeptName != null">
        creator_dept_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateId != null">
        update_id,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="updatePhone != null">
        update_phone,
      </if>
      <if test="updateDeptCode != null">
        update_dept_code,
      </if>
      <if test="updateDeptName != null">
        update_dept_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="pushResult != null">
        push_result,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="expenditureNo != null">
        #{expenditureNo,jdbcType=VARCHAR},
      </if>
      <if test="supplierCode != null">
        #{supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="shouldPayTotalAmount != null">
        #{shouldPayTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="shouldPayTotalAmountNoRate != null">
        #{shouldPayTotalAmountNoRate,jdbcType=DECIMAL},
      </if>
      <if test="supplierAmountTotal != null">
        #{supplierAmountTotal,jdbcType=DECIMAL},
      </if>
      <if test="supplierAmountTotalNoRate != null">
        #{supplierAmountTotalNoRate,jdbcType=DECIMAL},
      </if>
      <if test="supplierRate != null">
        #{supplierRate,jdbcType=DECIMAL},
      </if>
      <if test="orderCount != null">
        #{orderCount,jdbcType=INTEGER},
      </if>
      <if test="supplerInvoiceType != null">
        #{supplerInvoiceType,jdbcType=INTEGER},
      </if>
      <if test="expenditureStatus != null">
        #{expenditureStatus,jdbcType=TINYINT},
      </if>
      <if test="returnRate != null">
        #{returnRate,jdbcType=DECIMAL},
      </if>
      <if test="vehicleUserCode != null">
        #{vehicleUserCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleUserName != null">
        #{vehicleUserName,jdbcType=VARCHAR},
      </if>
      <if test="itemDescription != null">
        #{itemDescription,jdbcType=VARCHAR},
      </if>
      <if test="approvalSuggestion != null">
        #{approvalSuggestion,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="creatorPhone != null">
        #{creatorPhone,jdbcType=VARCHAR},
      </if>
      <if test="creatorDeptCode != null">
        #{creatorDeptCode,jdbcType=VARCHAR},
      </if>
      <if test="creatorDeptName != null">
        #{creatorDeptName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updatePhone != null">
        #{updatePhone,jdbcType=VARCHAR},
      </if>
      <if test="updateDeptCode != null">
        #{updateDeptCode,jdbcType=VARCHAR},
      </if>
      <if test="updateDeptName != null">
        #{updateDeptName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="pushResult != null">
        #{pushResult,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.BusinessSupplierExpenditureExample" resultType="java.lang.Long">
    select count(*) from business_supplier_expenditure
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update business_supplier_expenditure
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.expenditureNo != null">
        expenditure_no = #{row.expenditureNo,jdbcType=VARCHAR},
      </if>
      <if test="row.supplierCode != null">
        supplier_code = #{row.supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="row.supplierName != null">
        supplier_name = #{row.supplierName,jdbcType=VARCHAR},
      </if>
      <if test="row.shouldPayTotalAmount != null">
        should_pay_total_amount = #{row.shouldPayTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.shouldPayTotalAmountNoRate != null">
        should_pay_total_amount_no_rate = #{row.shouldPayTotalAmountNoRate,jdbcType=DECIMAL},
      </if>
      <if test="row.supplierAmountTotal != null">
        supplier_amount_total = #{row.supplierAmountTotal,jdbcType=DECIMAL},
      </if>
      <if test="row.supplierAmountTotalNoRate != null">
        supplier_amount_total_no_rate = #{row.supplierAmountTotalNoRate,jdbcType=DECIMAL},
      </if>
      <if test="row.supplierRate != null">
        supplier_rate = #{row.supplierRate,jdbcType=DECIMAL},
      </if>
      <if test="row.orderCount != null">
        order_count = #{row.orderCount,jdbcType=INTEGER},
      </if>
      <if test="row.supplerInvoiceType != null">
        suppler_invoice_type = #{row.supplerInvoiceType,jdbcType=INTEGER},
      </if>
      <if test="row.expenditureStatus != null">
        expenditure_status = #{row.expenditureStatus,jdbcType=TINYINT},
      </if>
      <if test="row.returnRate != null">
        return_rate = #{row.returnRate,jdbcType=DECIMAL},
      </if>
      <if test="row.vehicleUserCode != null">
        vehicle_user_code = #{row.vehicleUserCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleUserName != null">
        vehicle_user_name = #{row.vehicleUserName,jdbcType=VARCHAR},
      </if>
      <if test="row.itemDescription != null">
        item_description = #{row.itemDescription,jdbcType=VARCHAR},
      </if>
      <if test="row.approvalSuggestion != null">
        approval_suggestion = #{row.approvalSuggestion,jdbcType=VARCHAR},
      </if>
      <if test="row.creatorId != null">
        creator_id = #{row.creatorId,jdbcType=INTEGER},
      </if>
      <if test="row.creatorName != null">
        creator_name = #{row.creatorName,jdbcType=VARCHAR},
      </if>
      <if test="row.creatorPhone != null">
        creator_phone = #{row.creatorPhone,jdbcType=VARCHAR},
      </if>
      <if test="row.creatorDeptCode != null">
        creator_dept_code = #{row.creatorDeptCode,jdbcType=VARCHAR},
      </if>
      <if test="row.creatorDeptName != null">
        creator_dept_name = #{row.creatorDeptName,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateId != null">
        update_id = #{row.updateId,jdbcType=INTEGER},
      </if>
      <if test="row.updateName != null">
        update_name = #{row.updateName,jdbcType=VARCHAR},
      </if>
      <if test="row.updatePhone != null">
        update_phone = #{row.updatePhone,jdbcType=VARCHAR},
      </if>
      <if test="row.updateDeptCode != null">
        update_dept_code = #{row.updateDeptCode,jdbcType=VARCHAR},
      </if>
      <if test="row.updateDeptName != null">
        update_dept_name = #{row.updateDeptName,jdbcType=VARCHAR},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.pushResult != null">
        push_result = #{row.pushResult,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update business_supplier_expenditure
    set id = #{row.id,jdbcType=BIGINT},
      expenditure_no = #{row.expenditureNo,jdbcType=VARCHAR},
      supplier_code = #{row.supplierCode,jdbcType=VARCHAR},
      supplier_name = #{row.supplierName,jdbcType=VARCHAR},
      should_pay_total_amount = #{row.shouldPayTotalAmount,jdbcType=DECIMAL},
      should_pay_total_amount_no_rate = #{row.shouldPayTotalAmountNoRate,jdbcType=DECIMAL},
      supplier_amount_total = #{row.supplierAmountTotal,jdbcType=DECIMAL},
      supplier_amount_total_no_rate = #{row.supplierAmountTotalNoRate,jdbcType=DECIMAL},
      supplier_rate = #{row.supplierRate,jdbcType=DECIMAL},
      order_count = #{row.orderCount,jdbcType=INTEGER},
      suppler_invoice_type = #{row.supplerInvoiceType,jdbcType=INTEGER},
      expenditure_status = #{row.expenditureStatus,jdbcType=TINYINT},
      return_rate = #{row.returnRate,jdbcType=DECIMAL},
      vehicle_user_code = #{row.vehicleUserCode,jdbcType=VARCHAR},
      vehicle_user_name = #{row.vehicleUserName,jdbcType=VARCHAR},
      item_description = #{row.itemDescription,jdbcType=VARCHAR},
      approval_suggestion = #{row.approvalSuggestion,jdbcType=VARCHAR},
      creator_id = #{row.creatorId,jdbcType=INTEGER},
      creator_name = #{row.creatorName,jdbcType=VARCHAR},
      creator_phone = #{row.creatorPhone,jdbcType=VARCHAR},
      creator_dept_code = #{row.creatorDeptCode,jdbcType=VARCHAR},
      creator_dept_name = #{row.creatorDeptName,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_id = #{row.updateId,jdbcType=INTEGER},
      update_name = #{row.updateName,jdbcType=VARCHAR},
      update_phone = #{row.updatePhone,jdbcType=VARCHAR},
      update_dept_code = #{row.updateDeptCode,jdbcType=VARCHAR},
      update_dept_name = #{row.updateDeptName,jdbcType=VARCHAR},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      push_result = #{row.pushResult,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.BusinessSupplierExpenditure">
    update business_supplier_expenditure
    <set>
      <if test="expenditureNo != null">
        expenditure_no = #{expenditureNo,jdbcType=VARCHAR},
      </if>
      <if test="supplierCode != null">
        supplier_code = #{supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        supplier_name = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="shouldPayTotalAmount != null">
        should_pay_total_amount = #{shouldPayTotalAmount,jdbcType=DECIMAL},
      </if>
      <if test="shouldPayTotalAmountNoRate != null">
        should_pay_total_amount_no_rate = #{shouldPayTotalAmountNoRate,jdbcType=DECIMAL},
      </if>
      <if test="supplierAmountTotal != null">
        supplier_amount_total = #{supplierAmountTotal,jdbcType=DECIMAL},
      </if>
      <if test="supplierAmountTotalNoRate != null">
        supplier_amount_total_no_rate = #{supplierAmountTotalNoRate,jdbcType=DECIMAL},
      </if>
      <if test="supplierRate != null">
        supplier_rate = #{supplierRate,jdbcType=DECIMAL},
      </if>
      <if test="orderCount != null">
        order_count = #{orderCount,jdbcType=INTEGER},
      </if>
      <if test="supplerInvoiceType != null">
        suppler_invoice_type = #{supplerInvoiceType,jdbcType=INTEGER},
      </if>
      <if test="expenditureStatus != null">
        expenditure_status = #{expenditureStatus,jdbcType=TINYINT},
      </if>
      <if test="returnRate != null">
        return_rate = #{returnRate,jdbcType=DECIMAL},
      </if>
      <if test="vehicleUserCode != null">
        vehicle_user_code = #{vehicleUserCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleUserName != null">
        vehicle_user_name = #{vehicleUserName,jdbcType=VARCHAR},
      </if>
      <if test="itemDescription != null">
        item_description = #{itemDescription,jdbcType=VARCHAR},
      </if>
      <if test="approvalSuggestion != null">
        approval_suggestion = #{approvalSuggestion,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        creator_name = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="creatorPhone != null">
        creator_phone = #{creatorPhone,jdbcType=VARCHAR},
      </if>
      <if test="creatorDeptCode != null">
        creator_dept_code = #{creatorDeptCode,jdbcType=VARCHAR},
      </if>
      <if test="creatorDeptName != null">
        creator_dept_name = #{creatorDeptName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        update_id = #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updatePhone != null">
        update_phone = #{updatePhone,jdbcType=VARCHAR},
      </if>
      <if test="updateDeptCode != null">
        update_dept_code = #{updateDeptCode,jdbcType=VARCHAR},
      </if>
      <if test="updateDeptName != null">
        update_dept_name = #{updateDeptName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="pushResult != null">
        push_result = #{pushResult,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.BusinessSupplierExpenditure">
    update business_supplier_expenditure
    set expenditure_no = #{expenditureNo,jdbcType=VARCHAR},
      supplier_code = #{supplierCode,jdbcType=VARCHAR},
      supplier_name = #{supplierName,jdbcType=VARCHAR},
      should_pay_total_amount = #{shouldPayTotalAmount,jdbcType=DECIMAL},
      should_pay_total_amount_no_rate = #{shouldPayTotalAmountNoRate,jdbcType=DECIMAL},
      supplier_amount_total = #{supplierAmountTotal,jdbcType=DECIMAL},
      supplier_amount_total_no_rate = #{supplierAmountTotalNoRate,jdbcType=DECIMAL},
      supplier_rate = #{supplierRate,jdbcType=DECIMAL},
      order_count = #{orderCount,jdbcType=INTEGER},
      suppler_invoice_type = #{supplerInvoiceType,jdbcType=INTEGER},
      expenditure_status = #{expenditureStatus,jdbcType=TINYINT},
      return_rate = #{returnRate,jdbcType=DECIMAL},
      vehicle_user_code = #{vehicleUserCode,jdbcType=VARCHAR},
      vehicle_user_name = #{vehicleUserName,jdbcType=VARCHAR},
      item_description = #{itemDescription,jdbcType=VARCHAR},
      approval_suggestion = #{approvalSuggestion,jdbcType=VARCHAR},
      creator_id = #{creatorId,jdbcType=INTEGER},
      creator_name = #{creatorName,jdbcType=VARCHAR},
      creator_phone = #{creatorPhone,jdbcType=VARCHAR},
      creator_dept_code = #{creatorDeptCode,jdbcType=VARCHAR},
      creator_dept_name = #{creatorDeptName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_id = #{updateId,jdbcType=INTEGER},
      update_name = #{updateName,jdbcType=VARCHAR},
      update_phone = #{updatePhone,jdbcType=VARCHAR},
      update_dept_code = #{updateDeptCode,jdbcType=VARCHAR},
      update_dept_name = #{updateDeptName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      push_result = #{pushResult,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>