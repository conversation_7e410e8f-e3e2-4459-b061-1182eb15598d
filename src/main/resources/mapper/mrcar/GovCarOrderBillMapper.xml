<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.mrcar.GovCarOrderBillMapper">
  <resultMap id="BaseResultMap" type="com.izu.order.entity.mrcar.GovCarOrderBill">
    <id column="bill_no" jdbcType="VARCHAR" property="billNo" />
    <result column="create_id" jdbcType="INTEGER" property="createId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_id" jdbcType="INTEGER" property="updateId" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="bill_name" jdbcType="VARCHAR" property="billName" />
    <result column="bill_type" jdbcType="TINYINT" property="billType" />
    <result column="bill_amount" jdbcType="DECIMAL" property="billAmount" />
    <result column="payed_amount" jdbcType="DECIMAL" property="payedAmount" />
    <result column="payed_type" jdbcType="TINYINT" property="payedType" />
    <result column="payed_time" jdbcType="TIMESTAMP" property="payedTime" />
    <result column="refund_amount" jdbcType="DECIMAL" property="refundAmount" />
    <result column="bill_valid" jdbcType="TINYINT" property="billValid" />
    <result column="bill_status" jdbcType="TINYINT" property="billStatus" />
    <result column="payments_account_code" jdbcType="VARCHAR" property="paymentsAccountCode" />
    <result column="push_bill_income" jdbcType="TINYINT" property="pushBillIncome" />
    <result column="pay_trans_no" jdbcType="VARCHAR" property="payTransNo" />
    <result column="refund_trans_no" jdbcType="VARCHAR" property="refundTransNo" />
    <result column="bill_notes" jdbcType="VARCHAR" property="billNotes" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.izu.order.entity.mrcar.GovCarOrderBill">
    <result column="payment_details" jdbcType="LONGVARCHAR" property="paymentDetails" />
  </resultMap>

  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    bill_no, create_id, create_name, create_time, update_id, update_name, update_time, 
    order_no, bill_name, bill_type, bill_amount, payed_amount, payed_type, payed_time, 
    refund_amount, bill_valid, bill_status, payments_account_code, push_bill_income, 
    pay_trans_no, refund_trans_no, bill_notes
  </sql>
  <sql id="Blob_Column_List">
    payment_details
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.izu.order.entity.mrcar.GovCarOrderBillExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from gov_car_order_bill
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.izu.order.entity.mrcar.GovCarOrderBillExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from gov_car_order_bill
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from gov_car_order_bill
    where bill_no = #{billNo,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from gov_car_order_bill
    where bill_no = #{billNo,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.izu.order.entity.mrcar.GovCarOrderBill">
    insert into gov_car_order_bill (bill_no, create_id, create_name, 
      create_time, update_id, update_name, 
      update_time, order_no, bill_name, 
      bill_type, bill_amount, payed_amount, 
      payed_type, payed_time, refund_amount, 
      bill_valid, bill_status, payments_account_code, 
      push_bill_income, pay_trans_no, refund_trans_no, 
      bill_notes, payment_details)
    values (#{billNo,jdbcType=VARCHAR}, #{createId,jdbcType=INTEGER}, #{createName,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateId,jdbcType=INTEGER}, #{updateName,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{orderNo,jdbcType=VARCHAR}, #{billName,jdbcType=VARCHAR}, 
      #{billType,jdbcType=TINYINT}, #{billAmount,jdbcType=DECIMAL}, #{payedAmount,jdbcType=DECIMAL}, 
      #{payedType,jdbcType=TINYINT}, #{payedTime,jdbcType=TIMESTAMP}, #{refundAmount,jdbcType=DECIMAL}, 
      #{billValid,jdbcType=TINYINT}, #{billStatus,jdbcType=TINYINT}, #{paymentsAccountCode,jdbcType=VARCHAR}, 
      #{pushBillIncome,jdbcType=TINYINT}, #{payTransNo,jdbcType=VARCHAR}, #{refundTransNo,jdbcType=VARCHAR}, 
      #{billNotes,jdbcType=VARCHAR}, #{paymentDetails,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.order.entity.mrcar.GovCarOrderBill">
    insert into gov_car_order_bill
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="billNo != null">
        bill_no,
      </if>
      <if test="createId != null">
        create_id,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateId != null">
        update_id,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="billName != null">
        bill_name,
      </if>
      <if test="billType != null">
        bill_type,
      </if>
      <if test="billAmount != null">
        bill_amount,
      </if>
      <if test="payedAmount != null">
        payed_amount,
      </if>
      <if test="payedType != null">
        payed_type,
      </if>
      <if test="payedTime != null">
        payed_time,
      </if>
      <if test="refundAmount != null">
        refund_amount,
      </if>
      <if test="billValid != null">
        bill_valid,
      </if>
      <if test="billStatus != null">
        bill_status,
      </if>
      <if test="paymentsAccountCode != null">
        payments_account_code,
      </if>
      <if test="pushBillIncome != null">
        push_bill_income,
      </if>
      <if test="payTransNo != null">
        pay_trans_no,
      </if>
      <if test="refundTransNo != null">
        refund_trans_no,
      </if>
      <if test="billNotes != null">
        bill_notes,
      </if>
      <if test="paymentDetails != null">
        payment_details,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="billNo != null">
        #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="createId != null">
        #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="billName != null">
        #{billName,jdbcType=VARCHAR},
      </if>
      <if test="billType != null">
        #{billType,jdbcType=TINYINT},
      </if>
      <if test="billAmount != null">
        #{billAmount,jdbcType=DECIMAL},
      </if>
      <if test="payedAmount != null">
        #{payedAmount,jdbcType=DECIMAL},
      </if>
      <if test="payedType != null">
        #{payedType,jdbcType=TINYINT},
      </if>
      <if test="payedTime != null">
        #{payedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="refundAmount != null">
        #{refundAmount,jdbcType=DECIMAL},
      </if>
      <if test="billValid != null">
        #{billValid,jdbcType=TINYINT},
      </if>
      <if test="billStatus != null">
        #{billStatus,jdbcType=TINYINT},
      </if>
      <if test="paymentsAccountCode != null">
        #{paymentsAccountCode,jdbcType=VARCHAR},
      </if>
      <if test="pushBillIncome != null">
        #{pushBillIncome,jdbcType=TINYINT},
      </if>
      <if test="payTransNo != null">
        #{payTransNo,jdbcType=VARCHAR},
      </if>
      <if test="refundTransNo != null">
        #{refundTransNo,jdbcType=VARCHAR},
      </if>
      <if test="billNotes != null">
        #{billNotes,jdbcType=VARCHAR},
      </if>
      <if test="paymentDetails != null">
        #{paymentDetails,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.order.entity.mrcar.GovCarOrderBillExample" resultType="java.lang.Long">
    select count(*) from gov_car_order_bill
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>

    <update id="updateByExampleSelective" parameterType="map">
    update gov_car_order_bill
    <set>
      <if test="row.billNo != null">
        bill_no = #{row.billNo,jdbcType=VARCHAR},
      </if>
      <if test="row.createId != null">
        create_id = #{row.createId,jdbcType=INTEGER},
      </if>
      <if test="row.createName != null">
        create_name = #{row.createName,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateId != null">
        update_id = #{row.updateId,jdbcType=INTEGER},
      </if>
      <if test="row.updateName != null">
        update_name = #{row.updateName,jdbcType=VARCHAR},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.orderNo != null">
        order_no = #{row.orderNo,jdbcType=VARCHAR},
      </if>
      <if test="row.billName != null">
        bill_name = #{row.billName,jdbcType=VARCHAR},
      </if>
      <if test="row.billType != null">
        bill_type = #{row.billType,jdbcType=TINYINT},
      </if>
      <if test="row.billAmount != null">
        bill_amount = #{row.billAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.payedAmount != null">
        payed_amount = #{row.payedAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.payedType != null">
        payed_type = #{row.payedType,jdbcType=TINYINT},
      </if>
      <if test="row.payedTime != null">
        payed_time = #{row.payedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.refundAmount != null">
        refund_amount = #{row.refundAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.billValid != null">
        bill_valid = #{row.billValid,jdbcType=TINYINT},
      </if>
      <if test="row.billStatus != null">
        bill_status = #{row.billStatus,jdbcType=TINYINT},
      </if>
      <if test="row.paymentsAccountCode != null">
        payments_account_code = #{row.paymentsAccountCode,jdbcType=VARCHAR},
      </if>
      <if test="row.pushBillIncome != null">
        push_bill_income = #{row.pushBillIncome,jdbcType=TINYINT},
      </if>
      <if test="row.payTransNo != null">
        pay_trans_no = #{row.payTransNo,jdbcType=VARCHAR},
      </if>
      <if test="row.refundTransNo != null">
        refund_trans_no = #{row.refundTransNo,jdbcType=VARCHAR},
      </if>
      <if test="row.billNotes != null">
        bill_notes = #{row.billNotes,jdbcType=VARCHAR},
      </if>
      <if test="row.paymentDetails != null">
        payment_details = #{row.paymentDetails,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update gov_car_order_bill
    set bill_no = #{row.billNo,jdbcType=VARCHAR},
      create_id = #{row.createId,jdbcType=INTEGER},
      create_name = #{row.createName,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_id = #{row.updateId,jdbcType=INTEGER},
      update_name = #{row.updateName,jdbcType=VARCHAR},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      order_no = #{row.orderNo,jdbcType=VARCHAR},
      bill_name = #{row.billName,jdbcType=VARCHAR},
      bill_type = #{row.billType,jdbcType=TINYINT},
      bill_amount = #{row.billAmount,jdbcType=DECIMAL},
      payed_amount = #{row.payedAmount,jdbcType=DECIMAL},
      payed_type = #{row.payedType,jdbcType=TINYINT},
      payed_time = #{row.payedTime,jdbcType=TIMESTAMP},
      refund_amount = #{row.refundAmount,jdbcType=DECIMAL},
      bill_valid = #{row.billValid,jdbcType=TINYINT},
      bill_status = #{row.billStatus,jdbcType=TINYINT},
      payments_account_code = #{row.paymentsAccountCode,jdbcType=VARCHAR},
      push_bill_income = #{row.pushBillIncome,jdbcType=TINYINT},
      pay_trans_no = #{row.payTransNo,jdbcType=VARCHAR},
      refund_trans_no = #{row.refundTransNo,jdbcType=VARCHAR},
      bill_notes = #{row.billNotes,jdbcType=VARCHAR},
      payment_details = #{row.paymentDetails,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update gov_car_order_bill
    set bill_no = #{row.billNo,jdbcType=VARCHAR},
      create_id = #{row.createId,jdbcType=INTEGER},
      create_name = #{row.createName,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_id = #{row.updateId,jdbcType=INTEGER},
      update_name = #{row.updateName,jdbcType=VARCHAR},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      order_no = #{row.orderNo,jdbcType=VARCHAR},
      bill_name = #{row.billName,jdbcType=VARCHAR},
      bill_type = #{row.billType,jdbcType=TINYINT},
      bill_amount = #{row.billAmount,jdbcType=DECIMAL},
      payed_amount = #{row.payedAmount,jdbcType=DECIMAL},
      payed_type = #{row.payedType,jdbcType=TINYINT},
      payed_time = #{row.payedTime,jdbcType=TIMESTAMP},
      refund_amount = #{row.refundAmount,jdbcType=DECIMAL},
      bill_valid = #{row.billValid,jdbcType=TINYINT},
      bill_status = #{row.billStatus,jdbcType=TINYINT},
      payments_account_code = #{row.paymentsAccountCode,jdbcType=VARCHAR},
      push_bill_income = #{row.pushBillIncome,jdbcType=TINYINT},
      pay_trans_no = #{row.payTransNo,jdbcType=VARCHAR},
      refund_trans_no = #{row.refundTransNo,jdbcType=VARCHAR},
      bill_notes = #{row.billNotes,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.order.entity.mrcar.GovCarOrderBill">
    update gov_car_order_bill
    <set>
      <if test="createId != null">
        create_id = #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        update_id = #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="billName != null">
        bill_name = #{billName,jdbcType=VARCHAR},
      </if>
      <if test="billType != null">
        bill_type = #{billType,jdbcType=TINYINT},
      </if>
      <if test="billAmount != null">
        bill_amount = #{billAmount,jdbcType=DECIMAL},
      </if>
      <if test="payedAmount != null">
        payed_amount = #{payedAmount,jdbcType=DECIMAL},
      </if>
      <if test="payedType != null">
        payed_type = #{payedType,jdbcType=TINYINT},
      </if>
      <if test="payedTime != null">
        payed_time = #{payedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="refundAmount != null">
        refund_amount = #{refundAmount,jdbcType=DECIMAL},
      </if>
      <if test="billValid != null">
        bill_valid = #{billValid,jdbcType=TINYINT},
      </if>
      <if test="billStatus != null">
        bill_status = #{billStatus,jdbcType=TINYINT},
      </if>
      <if test="paymentsAccountCode != null">
        payments_account_code = #{paymentsAccountCode,jdbcType=VARCHAR},
      </if>
      <if test="pushBillIncome != null">
        push_bill_income = #{pushBillIncome,jdbcType=TINYINT},
      </if>
      <if test="payTransNo != null">
        pay_trans_no = #{payTransNo,jdbcType=VARCHAR},
      </if>
      <if test="refundTransNo != null">
        refund_trans_no = #{refundTransNo,jdbcType=VARCHAR},
      </if>
      <if test="billNotes != null">
        bill_notes = #{billNotes,jdbcType=VARCHAR},
      </if>
      <if test="paymentDetails != null">
        payment_details = #{paymentDetails,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where bill_no = #{billNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.izu.order.entity.mrcar.GovCarOrderBill">
    update gov_car_order_bill
    set create_id = #{createId,jdbcType=INTEGER},
      create_name = #{createName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_id = #{updateId,jdbcType=INTEGER},
      update_name = #{updateName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      order_no = #{orderNo,jdbcType=VARCHAR},
      bill_name = #{billName,jdbcType=VARCHAR},
      bill_type = #{billType,jdbcType=TINYINT},
      bill_amount = #{billAmount,jdbcType=DECIMAL},
      payed_amount = #{payedAmount,jdbcType=DECIMAL},
      payed_type = #{payedType,jdbcType=TINYINT},
      payed_time = #{payedTime,jdbcType=TIMESTAMP},
      refund_amount = #{refundAmount,jdbcType=DECIMAL},
      bill_valid = #{billValid,jdbcType=TINYINT},
      bill_status = #{billStatus,jdbcType=TINYINT},
      payments_account_code = #{paymentsAccountCode,jdbcType=VARCHAR},
      push_bill_income = #{pushBillIncome,jdbcType=TINYINT},
      pay_trans_no = #{payTransNo,jdbcType=VARCHAR},
      refund_trans_no = #{refundTransNo,jdbcType=VARCHAR},
      bill_notes = #{billNotes,jdbcType=VARCHAR},
      payment_details = #{paymentDetails,jdbcType=LONGVARCHAR}
    where bill_no = #{billNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.order.entity.mrcar.GovCarOrderBill">
    update gov_car_order_bill
    set create_id = #{createId,jdbcType=INTEGER},
      create_name = #{createName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_id = #{updateId,jdbcType=INTEGER},
      update_name = #{updateName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      order_no = #{orderNo,jdbcType=VARCHAR},
      bill_name = #{billName,jdbcType=VARCHAR},
      bill_type = #{billType,jdbcType=TINYINT},
      bill_amount = #{billAmount,jdbcType=DECIMAL},
      payed_amount = #{payedAmount,jdbcType=DECIMAL},
      payed_type = #{payedType,jdbcType=TINYINT},
      payed_time = #{payedTime,jdbcType=TIMESTAMP},
      refund_amount = #{refundAmount,jdbcType=DECIMAL},
      bill_valid = #{billValid,jdbcType=TINYINT},
      bill_status = #{billStatus,jdbcType=TINYINT},
      payments_account_code = #{paymentsAccountCode,jdbcType=VARCHAR},
      push_bill_income = #{pushBillIncome,jdbcType=TINYINT},
      pay_trans_no = #{payTransNo,jdbcType=VARCHAR},
      refund_trans_no = #{refundTransNo,jdbcType=VARCHAR},
      bill_notes = #{billNotes,jdbcType=VARCHAR}
    where bill_no = #{billNo,jdbcType=VARCHAR}
  </update>
</mapper>