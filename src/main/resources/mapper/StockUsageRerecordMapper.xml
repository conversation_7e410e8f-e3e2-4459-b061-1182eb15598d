<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.StockUsageRerecordMapper">
  <resultMap id="BaseResultMap" type="com.izu.business.entity.StockUsageRerecord">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_type" jdbcType="TINYINT" property="businessType" />
    <result column="business_no" jdbcType="VARCHAR" property="businessNo" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="car_level_id" jdbcType="TINYINT" property="carLevelId" />
    <result column="vehicle_count" jdbcType="TINYINT" property="vehicleCount" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="vehicle_license" jdbcType="VARCHAR" property="vehicleLicense" />
    <result column="vehicle_vin" jdbcType="VARCHAR" property="vehicleVin" />
    <result column="driver_id" jdbcType="INTEGER" property="driverId" />
    <result column="driver_name" jdbcType="VARCHAR" property="driverName" />
    <result column="stime" jdbcType="TIMESTAMP" property="stime" />
    <result column="etime" jdbcType="TIMESTAMP" property="etime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, business_type, business_no, company_id, car_level_id, vehicle_count, vehicle_id, 
    vehicle_license, vehicle_vin, driver_id, driver_name, stime, etime, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from stock_usage_rerecord
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from stock_usage_rerecord
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.business.entity.StockUsageRerecord">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into stock_usage_rerecord (business_type, business_no, company_id, 
      car_level_id, vehicle_count, vehicle_id, 
      vehicle_license, vehicle_vin, driver_id, 
      driver_name, stime, etime, 
      create_time)
    values (#{businessType,jdbcType=TINYINT}, #{businessNo,jdbcType=VARCHAR}, #{companyId,jdbcType=INTEGER}, 
      #{carLevelId,jdbcType=TINYINT}, #{vehicleCount,jdbcType=TINYINT}, #{vehicleId,jdbcType=BIGINT}, 
      #{vehicleLicense,jdbcType=VARCHAR}, #{vehicleVin,jdbcType=VARCHAR}, #{driverId,jdbcType=INTEGER}, 
      #{driverName,jdbcType=VARCHAR}, #{stime,jdbcType=TIMESTAMP}, #{etime,jdbcType=TIMESTAMP}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.business.entity.StockUsageRerecord">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into stock_usage_rerecord
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessType != null">
        business_type,
      </if>
      <if test="businessNo != null">
        business_no,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="carLevelId != null">
        car_level_id,
      </if>
      <if test="vehicleCount != null">
        vehicle_count,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="vehicleLicense != null">
        vehicle_license,
      </if>
      <if test="vehicleVin != null">
        vehicle_vin,
      </if>
      <if test="driverId != null">
        driver_id,
      </if>
      <if test="driverName != null">
        driver_name,
      </if>
      <if test="stime != null">
        stime,
      </if>
      <if test="etime != null">
        etime,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessType != null">
        #{businessType,jdbcType=TINYINT},
      </if>
      <if test="businessNo != null">
        #{businessNo,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="carLevelId != null">
        #{carLevelId,jdbcType=TINYINT},
      </if>
      <if test="vehicleCount != null">
        #{vehicleCount,jdbcType=TINYINT},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleLicense != null">
        #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="vehicleVin != null">
        #{vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="driverId != null">
        #{driverId,jdbcType=INTEGER},
      </if>
      <if test="driverName != null">
        #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="stime != null">
        #{stime,jdbcType=TIMESTAMP},
      </if>
      <if test="etime != null">
        #{etime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.business.entity.StockUsageRerecord">
    update stock_usage_rerecord
    <set>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=TINYINT},
      </if>
      <if test="businessNo != null">
        business_no = #{businessNo,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="carLevelId != null">
        car_level_id = #{carLevelId,jdbcType=TINYINT},
      </if>
      <if test="vehicleCount != null">
        vehicle_count = #{vehicleCount,jdbcType=TINYINT},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleLicense != null">
        vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="vehicleVin != null">
        vehicle_vin = #{vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="driverId != null">
        driver_id = #{driverId,jdbcType=INTEGER},
      </if>
      <if test="driverName != null">
        driver_name = #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="stime != null">
        stime = #{stime,jdbcType=TIMESTAMP},
      </if>
      <if test="etime != null">
        etime = #{etime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.business.entity.StockUsageRerecord">
    update stock_usage_rerecord
    set business_type = #{businessType,jdbcType=TINYINT},
      business_no = #{businessNo,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=INTEGER},
      car_level_id = #{carLevelId,jdbcType=TINYINT},
      vehicle_count = #{vehicleCount,jdbcType=TINYINT},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      vehicle_vin = #{vehicleVin,jdbcType=VARCHAR},
      driver_id = #{driverId,jdbcType=INTEGER},
      driver_name = #{driverName,jdbcType=VARCHAR},
      stime = #{stime,jdbcType=TIMESTAMP},
      etime = #{etime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>