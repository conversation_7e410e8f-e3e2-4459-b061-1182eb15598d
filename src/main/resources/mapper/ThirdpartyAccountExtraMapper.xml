<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.ThirdpartyAccountExtraMapper">
  <resultMap id="BaseResultMap" type="com.izu.user.entity.ThirdpartyAccountExtra">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="customer_code" jdbcType="VARCHAR" property="customerCode" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="user_outer_id" jdbcType="VARCHAR" property="userOuterId" />
    <result column="channel_id" jdbcType="VARCHAR" property="channelId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, customer_code, customer_name, mobile, create_time, update_time, user_outer_id, 
    channel_id
  </sql>
  <select id="selectByExample" parameterType="com.izu.user.entity.ThirdpartyAccountExtraExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_thirdparty_account_extra
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_thirdparty_account_extra
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from t_thirdparty_account_extra
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.user.entity.ThirdpartyAccountExtraExample">
    delete from t_thirdparty_account_extra
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.user.entity.ThirdpartyAccountExtra">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into t_thirdparty_account_extra (customer_code, customer_name, mobile, 
      create_time, update_time, user_outer_id, 
      channel_id)
    values (#{customerCode,jdbcType=VARCHAR}, #{customerName,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{userOuterId,jdbcType=VARCHAR}, 
      #{channelId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.user.entity.ThirdpartyAccountExtra">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into t_thirdparty_account_extra
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="customerCode != null">
        customer_code,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="userOuterId != null">
        user_outer_id,
      </if>
      <if test="channelId != null">
        channel_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="customerCode != null">
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="userOuterId != null">
        #{userOuterId,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null">
        #{channelId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.user.entity.ThirdpartyAccountExtraExample" resultType="java.lang.Long">
    select count(*) from t_thirdparty_account_extra
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update t_thirdparty_account_extra
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=INTEGER},
      </if>
      <if test="row.customerCode != null">
        customer_code = #{row.customerCode,jdbcType=VARCHAR},
      </if>
      <if test="row.customerName != null">
        customer_name = #{row.customerName,jdbcType=VARCHAR},
      </if>
      <if test="row.mobile != null">
        mobile = #{row.mobile,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.userOuterId != null">
        user_outer_id = #{row.userOuterId,jdbcType=VARCHAR},
      </if>
      <if test="row.channelId != null">
        channel_id = #{row.channelId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update t_thirdparty_account_extra
    set id = #{row.id,jdbcType=INTEGER},
      customer_code = #{row.customerCode,jdbcType=VARCHAR},
      customer_name = #{row.customerName,jdbcType=VARCHAR},
      mobile = #{row.mobile,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      user_outer_id = #{row.userOuterId,jdbcType=VARCHAR},
      channel_id = #{row.channelId,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.user.entity.ThirdpartyAccountExtra">
    update t_thirdparty_account_extra
    <set>
      <if test="customerCode != null">
        customer_code = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null">
        customer_name = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="userOuterId != null">
        user_outer_id = #{userOuterId,jdbcType=VARCHAR},
      </if>
      <if test="channelId != null">
        channel_id = #{channelId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.user.entity.ThirdpartyAccountExtra">
    update t_thirdparty_account_extra
    set customer_code = #{customerCode,jdbcType=VARCHAR},
      customer_name = #{customerName,jdbcType=VARCHAR},
      mobile = #{mobile,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      user_outer_id = #{userOuterId,jdbcType=VARCHAR},
      channel_id = #{channelId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>