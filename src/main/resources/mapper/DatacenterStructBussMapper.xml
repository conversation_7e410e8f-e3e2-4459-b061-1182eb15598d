<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.DatacenterStructBussMapper">
  <resultMap id="BaseResultMap" type="com.izu.user.entity.DatacenterStructBuss">
    <id column="struct_id" jdbcType="INTEGER" property="structId" />
    <result column="struct_name" jdbcType="VARCHAR" property="structName" />
    <result column="struct_name_short" jdbcType="VARCHAR" property="structNameShort" />
    <result column="provider_code" jdbcType="VARCHAR" property="providerCode" />
    <result column="struct_parent_id_hierarchy" jdbcType="VARCHAR" property="structParentIdHierarchy" />
    <result column="parent_id" jdbcType="INTEGER" property="parentId" />
    <result column="ding_id" jdbcType="BIGINT" property="dingId" />
    <result column="ding_parent_id" jdbcType="BIGINT" property="dingParentId" />
    <result column="struct_code" jdbcType="VARCHAR" property="structCode" />
    <result column="struct_type" jdbcType="TINYINT" property="structType" />
    <result column="region_type" jdbcType="TINYINT" property="regionType" />
    <result column="belong_province_code" jdbcType="VARCHAR" property="belongProvinceCode" />
    <result column="belong_province_name" jdbcType="VARCHAR" property="belongProvinceName" />
    <result column="belong_city_code" jdbcType="VARCHAR" property="belongCityCode" />
    <result column="belong_city_name" jdbcType="VARCHAR" property="belongCityName" />
    <result column="account_set_city_code" jdbcType="VARCHAR" property="accountSetCityCode" />
    <result column="account_set_city_name" jdbcType="VARCHAR" property="accountSetCityName" />
    <result column="struct_addr" jdbcType="VARCHAR" property="structAddr" />
    <result column="operate_lease" jdbcType="TINYINT" property="operateLease" />
    <result column="orange_business" jdbcType="TINYINT" property="orangeBusiness" />
    <result column="belong_finance_id" jdbcType="VARCHAR" property="belongFinanceId" />
    <result column="struct_telno" jdbcType="VARCHAR" property="structTelno" />
    <result column="struct_state" jdbcType="TINYINT" property="structState" />
    <result column="buss_is_leaf" jdbcType="TINYINT" property="bussIsLeaf" />
    <result column="is_exist_ding" jdbcType="TINYINT" property="isExistDing" />
    <result column="asset_struct_id" jdbcType="VARCHAR" property="assetStructId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="creter_id" jdbcType="INTEGER" property="creterId" />
    <result column="creter_name" jdbcType="VARCHAR" property="creterName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_id" jdbcType="INTEGER" property="updateId" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    struct_id, struct_name, struct_name_short, provider_code, struct_parent_id_hierarchy, 
    parent_id, ding_id, ding_parent_id, struct_code, struct_type, region_type, belong_province_code, 
    belong_province_name, belong_city_code, belong_city_name, account_set_city_code, 
    account_set_city_name, struct_addr, operate_lease, orange_business, belong_finance_id, 
    struct_telno, struct_state, buss_is_leaf, is_exist_ding, asset_struct_id, remark, 
    creter_id, creter_name, create_time, update_time, update_id, update_name
  </sql>
  <select id="selectByExample" parameterType="com.izu.user.entity.DatacenterStructBussExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from datacenter_struct_buss
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from datacenter_struct_buss
    where struct_id = #{structId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from datacenter_struct_buss
    where struct_id = #{structId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.user.entity.DatacenterStructBussExample">
    delete from datacenter_struct_buss
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.user.entity.DatacenterStructBuss">
    <selectKey keyProperty="structId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into datacenter_struct_buss (struct_name, struct_name_short, provider_code, 
      struct_parent_id_hierarchy, parent_id, ding_id, 
      ding_parent_id, struct_code, struct_type, 
      region_type, belong_province_code, belong_province_name, 
      belong_city_code, belong_city_name, account_set_city_code, 
      account_set_city_name, struct_addr, operate_lease, 
      orange_business, belong_finance_id, struct_telno, 
      struct_state, buss_is_leaf, is_exist_ding, 
      asset_struct_id, remark, creter_id, 
      creter_name, create_time, update_time, 
      update_id, update_name)
    values (#{structName,jdbcType=VARCHAR}, #{structNameShort,jdbcType=VARCHAR}, #{providerCode,jdbcType=VARCHAR}, 
      #{structParentIdHierarchy,jdbcType=VARCHAR}, #{parentId,jdbcType=INTEGER}, #{dingId,jdbcType=BIGINT}, 
      #{dingParentId,jdbcType=BIGINT}, #{structCode,jdbcType=VARCHAR}, #{structType,jdbcType=TINYINT}, 
      #{regionType,jdbcType=TINYINT}, #{belongProvinceCode,jdbcType=VARCHAR}, #{belongProvinceName,jdbcType=VARCHAR}, 
      #{belongCityCode,jdbcType=VARCHAR}, #{belongCityName,jdbcType=VARCHAR}, #{accountSetCityCode,jdbcType=VARCHAR}, 
      #{accountSetCityName,jdbcType=VARCHAR}, #{structAddr,jdbcType=VARCHAR}, #{operateLease,jdbcType=TINYINT}, 
      #{orangeBusiness,jdbcType=TINYINT}, #{belongFinanceId,jdbcType=VARCHAR}, #{structTelno,jdbcType=VARCHAR}, 
      #{structState,jdbcType=TINYINT}, #{bussIsLeaf,jdbcType=TINYINT}, #{isExistDing,jdbcType=TINYINT}, 
      #{assetStructId,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{creterId,jdbcType=INTEGER}, 
      #{creterName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{updateId,jdbcType=INTEGER}, #{updateName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.user.entity.DatacenterStructBuss">
    <selectKey keyProperty="structId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into datacenter_struct_buss
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="structName != null">
        struct_name,
      </if>
      <if test="structNameShort != null">
        struct_name_short,
      </if>
      <if test="providerCode != null">
        provider_code,
      </if>
      <if test="structParentIdHierarchy != null">
        struct_parent_id_hierarchy,
      </if>
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="dingId != null">
        ding_id,
      </if>
      <if test="dingParentId != null">
        ding_parent_id,
      </if>
      <if test="structCode != null">
        struct_code,
      </if>
      <if test="structType != null">
        struct_type,
      </if>
      <if test="regionType != null">
        region_type,
      </if>
      <if test="belongProvinceCode != null">
        belong_province_code,
      </if>
      <if test="belongProvinceName != null">
        belong_province_name,
      </if>
      <if test="belongCityCode != null">
        belong_city_code,
      </if>
      <if test="belongCityName != null">
        belong_city_name,
      </if>
      <if test="accountSetCityCode != null">
        account_set_city_code,
      </if>
      <if test="accountSetCityName != null">
        account_set_city_name,
      </if>
      <if test="structAddr != null">
        struct_addr,
      </if>
      <if test="operateLease != null">
        operate_lease,
      </if>
      <if test="orangeBusiness != null">
        orange_business,
      </if>
      <if test="belongFinanceId != null">
        belong_finance_id,
      </if>
      <if test="structTelno != null">
        struct_telno,
      </if>
      <if test="structState != null">
        struct_state,
      </if>
      <if test="bussIsLeaf != null">
        buss_is_leaf,
      </if>
      <if test="isExistDing != null">
        is_exist_ding,
      </if>
      <if test="assetStructId != null">
        asset_struct_id,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="creterId != null">
        creter_id,
      </if>
      <if test="creterName != null">
        creter_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateId != null">
        update_id,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="structName != null">
        #{structName,jdbcType=VARCHAR},
      </if>
      <if test="structNameShort != null">
        #{structNameShort,jdbcType=VARCHAR},
      </if>
      <if test="providerCode != null">
        #{providerCode,jdbcType=VARCHAR},
      </if>
      <if test="structParentIdHierarchy != null">
        #{structParentIdHierarchy,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=INTEGER},
      </if>
      <if test="dingId != null">
        #{dingId,jdbcType=BIGINT},
      </if>
      <if test="dingParentId != null">
        #{dingParentId,jdbcType=BIGINT},
      </if>
      <if test="structCode != null">
        #{structCode,jdbcType=VARCHAR},
      </if>
      <if test="structType != null">
        #{structType,jdbcType=TINYINT},
      </if>
      <if test="regionType != null">
        #{regionType,jdbcType=TINYINT},
      </if>
      <if test="belongProvinceCode != null">
        #{belongProvinceCode,jdbcType=VARCHAR},
      </if>
      <if test="belongProvinceName != null">
        #{belongProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="belongCityCode != null">
        #{belongCityCode,jdbcType=VARCHAR},
      </if>
      <if test="belongCityName != null">
        #{belongCityName,jdbcType=VARCHAR},
      </if>
      <if test="accountSetCityCode != null">
        #{accountSetCityCode,jdbcType=VARCHAR},
      </if>
      <if test="accountSetCityName != null">
        #{accountSetCityName,jdbcType=VARCHAR},
      </if>
      <if test="structAddr != null">
        #{structAddr,jdbcType=VARCHAR},
      </if>
      <if test="operateLease != null">
        #{operateLease,jdbcType=TINYINT},
      </if>
      <if test="orangeBusiness != null">
        #{orangeBusiness,jdbcType=TINYINT},
      </if>
      <if test="belongFinanceId != null">
        #{belongFinanceId,jdbcType=VARCHAR},
      </if>
      <if test="structTelno != null">
        #{structTelno,jdbcType=VARCHAR},
      </if>
      <if test="structState != null">
        #{structState,jdbcType=TINYINT},
      </if>
      <if test="bussIsLeaf != null">
        #{bussIsLeaf,jdbcType=TINYINT},
      </if>
      <if test="isExistDing != null">
        #{isExistDing,jdbcType=TINYINT},
      </if>
      <if test="assetStructId != null">
        #{assetStructId,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creterId != null">
        #{creterId,jdbcType=INTEGER},
      </if>
      <if test="creterName != null">
        #{creterName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.user.entity.DatacenterStructBussExample" resultType="java.lang.Long">
    select count(*) from datacenter_struct_buss
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update datacenter_struct_buss
    <set>
      <if test="record.structId != null">
        struct_id = #{record.structId,jdbcType=INTEGER},
      </if>
      <if test="record.structName != null">
        struct_name = #{record.structName,jdbcType=VARCHAR},
      </if>
      <if test="record.structNameShort != null">
        struct_name_short = #{record.structNameShort,jdbcType=VARCHAR},
      </if>
      <if test="record.providerCode != null">
        provider_code = #{record.providerCode,jdbcType=VARCHAR},
      </if>
      <if test="record.structParentIdHierarchy != null">
        struct_parent_id_hierarchy = #{record.structParentIdHierarchy,jdbcType=VARCHAR},
      </if>
      <if test="record.parentId != null">
        parent_id = #{record.parentId,jdbcType=INTEGER},
      </if>
      <if test="record.dingId != null">
        ding_id = #{record.dingId,jdbcType=BIGINT},
      </if>
      <if test="record.dingParentId != null">
        ding_parent_id = #{record.dingParentId,jdbcType=BIGINT},
      </if>
      <if test="record.structCode != null">
        struct_code = #{record.structCode,jdbcType=VARCHAR},
      </if>
      <if test="record.structType != null">
        struct_type = #{record.structType,jdbcType=TINYINT},
      </if>
      <if test="record.regionType != null">
        region_type = #{record.regionType,jdbcType=TINYINT},
      </if>
      <if test="record.belongProvinceCode != null">
        belong_province_code = #{record.belongProvinceCode,jdbcType=VARCHAR},
      </if>
      <if test="record.belongProvinceName != null">
        belong_province_name = #{record.belongProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="record.belongCityCode != null">
        belong_city_code = #{record.belongCityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.belongCityName != null">
        belong_city_name = #{record.belongCityName,jdbcType=VARCHAR},
      </if>
      <if test="record.accountSetCityCode != null">
        account_set_city_code = #{record.accountSetCityCode,jdbcType=VARCHAR},
      </if>
      <if test="record.accountSetCityName != null">
        account_set_city_name = #{record.accountSetCityName,jdbcType=VARCHAR},
      </if>
      <if test="record.structAddr != null">
        struct_addr = #{record.structAddr,jdbcType=VARCHAR},
      </if>
      <if test="record.operateLease != null">
        operate_lease = #{record.operateLease,jdbcType=TINYINT},
      </if>
      <if test="record.orangeBusiness != null">
        orange_business = #{record.orangeBusiness,jdbcType=TINYINT},
      </if>
      <if test="record.belongFinanceId != null">
        belong_finance_id = #{record.belongFinanceId,jdbcType=VARCHAR},
      </if>
      <if test="record.structTelno != null">
        struct_telno = #{record.structTelno,jdbcType=VARCHAR},
      </if>
      <if test="record.structState != null">
        struct_state = #{record.structState,jdbcType=TINYINT},
      </if>
      <if test="record.bussIsLeaf != null">
        buss_is_leaf = #{record.bussIsLeaf,jdbcType=TINYINT},
      </if>
      <if test="record.isExistDing != null">
        is_exist_ding = #{record.isExistDing,jdbcType=TINYINT},
      </if>
      <if test="record.assetStructId != null">
        asset_struct_id = #{record.assetStructId,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.creterId != null">
        creter_id = #{record.creterId,jdbcType=INTEGER},
      </if>
      <if test="record.creterName != null">
        creter_name = #{record.creterName,jdbcType=VARCHAR},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateId != null">
        update_id = #{record.updateId,jdbcType=INTEGER},
      </if>
      <if test="record.updateName != null">
        update_name = #{record.updateName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update datacenter_struct_buss
    set struct_id = #{record.structId,jdbcType=INTEGER},
      struct_name = #{record.structName,jdbcType=VARCHAR},
      struct_name_short = #{record.structNameShort,jdbcType=VARCHAR},
      provider_code = #{record.providerCode,jdbcType=VARCHAR},
      struct_parent_id_hierarchy = #{record.structParentIdHierarchy,jdbcType=VARCHAR},
      parent_id = #{record.parentId,jdbcType=INTEGER},
      ding_id = #{record.dingId,jdbcType=BIGINT},
      ding_parent_id = #{record.dingParentId,jdbcType=BIGINT},
      struct_code = #{record.structCode,jdbcType=VARCHAR},
      struct_type = #{record.structType,jdbcType=TINYINT},
      region_type = #{record.regionType,jdbcType=TINYINT},
      belong_province_code = #{record.belongProvinceCode,jdbcType=VARCHAR},
      belong_province_name = #{record.belongProvinceName,jdbcType=VARCHAR},
      belong_city_code = #{record.belongCityCode,jdbcType=VARCHAR},
      belong_city_name = #{record.belongCityName,jdbcType=VARCHAR},
      account_set_city_code = #{record.accountSetCityCode,jdbcType=VARCHAR},
      account_set_city_name = #{record.accountSetCityName,jdbcType=VARCHAR},
      struct_addr = #{record.structAddr,jdbcType=VARCHAR},
      operate_lease = #{record.operateLease,jdbcType=TINYINT},
      orange_business = #{record.orangeBusiness,jdbcType=TINYINT},
      belong_finance_id = #{record.belongFinanceId,jdbcType=VARCHAR},
      struct_telno = #{record.structTelno,jdbcType=VARCHAR},
      struct_state = #{record.structState,jdbcType=TINYINT},
      buss_is_leaf = #{record.bussIsLeaf,jdbcType=TINYINT},
      is_exist_ding = #{record.isExistDing,jdbcType=TINYINT},
      asset_struct_id = #{record.assetStructId,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      creter_id = #{record.creterId,jdbcType=INTEGER},
      creter_name = #{record.creterName,jdbcType=VARCHAR},
      create_time = #{record.createTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      update_id = #{record.updateId,jdbcType=INTEGER},
      update_name = #{record.updateName,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.user.entity.DatacenterStructBuss">
    update datacenter_struct_buss
    <set>
      <if test="structName != null">
        struct_name = #{structName,jdbcType=VARCHAR},
      </if>
      <if test="structNameShort != null">
        struct_name_short = #{structNameShort,jdbcType=VARCHAR},
      </if>
      <if test="providerCode != null">
        provider_code = #{providerCode,jdbcType=VARCHAR},
      </if>
      <if test="structParentIdHierarchy != null">
        struct_parent_id_hierarchy = #{structParentIdHierarchy,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=INTEGER},
      </if>
      <if test="dingId != null">
        ding_id = #{dingId,jdbcType=BIGINT},
      </if>
      <if test="dingParentId != null">
        ding_parent_id = #{dingParentId,jdbcType=BIGINT},
      </if>
      <if test="structCode != null">
        struct_code = #{structCode,jdbcType=VARCHAR},
      </if>
      <if test="structType != null">
        struct_type = #{structType,jdbcType=TINYINT},
      </if>
      <if test="regionType != null">
        region_type = #{regionType,jdbcType=TINYINT},
      </if>
      <if test="belongProvinceCode != null">
        belong_province_code = #{belongProvinceCode,jdbcType=VARCHAR},
      </if>
      <if test="belongProvinceName != null">
        belong_province_name = #{belongProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="belongCityCode != null">
        belong_city_code = #{belongCityCode,jdbcType=VARCHAR},
      </if>
      <if test="belongCityName != null">
        belong_city_name = #{belongCityName,jdbcType=VARCHAR},
      </if>
      <if test="accountSetCityCode != null">
        account_set_city_code = #{accountSetCityCode,jdbcType=VARCHAR},
      </if>
      <if test="accountSetCityName != null">
        account_set_city_name = #{accountSetCityName,jdbcType=VARCHAR},
      </if>
      <if test="structAddr != null">
        struct_addr = #{structAddr,jdbcType=VARCHAR},
      </if>
      <if test="operateLease != null">
        operate_lease = #{operateLease,jdbcType=TINYINT},
      </if>
      <if test="orangeBusiness != null">
        orange_business = #{orangeBusiness,jdbcType=TINYINT},
      </if>
      <if test="belongFinanceId != null">
        belong_finance_id = #{belongFinanceId,jdbcType=VARCHAR},
      </if>
      <if test="structTelno != null">
        struct_telno = #{structTelno,jdbcType=VARCHAR},
      </if>
      <if test="structState != null">
        struct_state = #{structState,jdbcType=TINYINT},
      </if>
      <if test="bussIsLeaf != null">
        buss_is_leaf = #{bussIsLeaf,jdbcType=TINYINT},
      </if>
      <if test="isExistDing != null">
        is_exist_ding = #{isExistDing,jdbcType=TINYINT},
      </if>
      <if test="assetStructId != null">
        asset_struct_id = #{assetStructId,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="creterId != null">
        creter_id = #{creterId,jdbcType=INTEGER},
      </if>
      <if test="creterName != null">
        creter_name = #{creterName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        update_id = #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
    </set>
    where struct_id = #{structId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.user.entity.DatacenterStructBuss">
    update datacenter_struct_buss
    set struct_name = #{structName,jdbcType=VARCHAR},
      struct_name_short = #{structNameShort,jdbcType=VARCHAR},
      provider_code = #{providerCode,jdbcType=VARCHAR},
      struct_parent_id_hierarchy = #{structParentIdHierarchy,jdbcType=VARCHAR},
      parent_id = #{parentId,jdbcType=INTEGER},
      ding_id = #{dingId,jdbcType=BIGINT},
      ding_parent_id = #{dingParentId,jdbcType=BIGINT},
      struct_code = #{structCode,jdbcType=VARCHAR},
      struct_type = #{structType,jdbcType=TINYINT},
      region_type = #{regionType,jdbcType=TINYINT},
      belong_province_code = #{belongProvinceCode,jdbcType=VARCHAR},
      belong_province_name = #{belongProvinceName,jdbcType=VARCHAR},
      belong_city_code = #{belongCityCode,jdbcType=VARCHAR},
      belong_city_name = #{belongCityName,jdbcType=VARCHAR},
      account_set_city_code = #{accountSetCityCode,jdbcType=VARCHAR},
      account_set_city_name = #{accountSetCityName,jdbcType=VARCHAR},
      struct_addr = #{structAddr,jdbcType=VARCHAR},
      operate_lease = #{operateLease,jdbcType=TINYINT},
      orange_business = #{orangeBusiness,jdbcType=TINYINT},
      belong_finance_id = #{belongFinanceId,jdbcType=VARCHAR},
      struct_telno = #{structTelno,jdbcType=VARCHAR},
      struct_state = #{structState,jdbcType=TINYINT},
      buss_is_leaf = #{bussIsLeaf,jdbcType=TINYINT},
      is_exist_ding = #{isExistDing,jdbcType=TINYINT},
      asset_struct_id = #{assetStructId,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      creter_id = #{creterId,jdbcType=INTEGER},
      creter_name = #{creterName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_id = #{updateId,jdbcType=INTEGER},
      update_name = #{updateName,jdbcType=VARCHAR}
    where struct_id = #{structId,jdbcType=INTEGER}
  </update>
</mapper>