<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.StatisticsSystemViewMapper">
  <resultMap id="BaseResultMap" type="com.izu.business.entity.StatisticsSystemView">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="customer_pv" jdbcType="INTEGER" property="customerPv" />
    <result column="customer_uv" jdbcType="INTEGER" property="customerUv" />
    <result column="web_pv" jdbcType="INTEGER" property="webPv" />
    <result column="android_pv" jdbcType="INTEGER" property="androidPv" />
    <result column="ios_pv" jdbcType="INTEGER" property="iosPv" />
    <result column="web_uv" jdbcType="INTEGER" property="webUv" />
    <result column="android_uv" jdbcType="INTEGER" property="androidUv" />
    <result column="ios_uv" jdbcType="INTEGER" property="iosUv" />
    <result column="stat_date" jdbcType="DATE" property="statDate" />
    <result column="stat_week" jdbcType="INTEGER" property="statWeek" />
    <result column="stat_month" jdbcType="INTEGER" property="statMonth" />
    <result column="stat_season" jdbcType="INTEGER" property="statSeason" />
    <result column="stat_year" jdbcType="INTEGER" property="statYear" />
    <result column="internal_order_count" jdbcType="INTEGER" property="internalOrderCount" />
    <result column="motorcade_order_count" jdbcType="INTEGER" property="motorcadeOrderCount" />
    <result column="private_order_count" jdbcType="INTEGER" property="privateOrderCount" />
    <result column="so_order_count" jdbcType="INTEGER" property="soOrderCount" />
    <result column="co_order_count" jdbcType="INTEGER" property="coOrderCount" />
    <result column="gov_public_order_count" jdbcType="INTEGER" property="govPublicOrderCount" />
    <result column="staff_count" jdbcType="INTEGER" property="staffCount" />
    <result column="sq_vehicle_count" jdbcType="INTEGER" property="sqVehicleCount" />
    <result column="company_vehicle_count" jdbcType="INTEGER" property="companyVehicleCount" />
    <result column="third_vehicle_count" jdbcType="INTEGER" property="thirdVehicleCount" />
    <result column="staff_vehicle_count" jdbcType="INTEGER" property="staffVehicleCount" />
    <result column="sq_driver_count" jdbcType="INTEGER" property="sqDriverCount" />
    <result column="company_driver_count" jdbcType="INTEGER" property="companyDriverCount" />
    <result column="business_driver_count" jdbcType="INTEGER" property="businessDriverCount" />
    <result column="third_driver_count" jdbcType="INTEGER" property="thirdDriverCount" />
    <result column="sq_device_count" jdbcType="INTEGER" property="sqDeviceCount" />
    <result column="self_device_count" jdbcType="INTEGER" property="selfDeviceCount" />
    <result column="apply_count" jdbcType="INTEGER" property="applyCount" />
    <result column="sq_maintenance_count" jdbcType="INTEGER" property="sqMaintenanceCount" />
    <result column="company_maintenance_count" jdbcType="INTEGER" property="companyMaintenanceCount" />
    <result column="charge_order_count" jdbcType="INTEGER" property="chargeOrderCount" />
    <result column="sq_violation_count" jdbcType="INTEGER" property="sqViolationCount" />
    <result column="company_violation_count" jdbcType="INTEGER" property="companyViolationCount" />
    <result column="company_attribute" jdbcType="TINYINT" property="companyAttribute" />
    <result column="account_type" jdbcType="TINYINT" property="accountType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, create_time, update_time, company_id, company_code, company_name, customer_pv, 
    customer_uv, web_pv, android_pv, ios_pv, web_uv, android_uv, ios_uv, stat_date, stat_week, 
    stat_month, stat_season, stat_year, internal_order_count, motorcade_order_count, 
    private_order_count, so_order_count, co_order_count, gov_public_order_count, staff_count, 
    sq_vehicle_count, company_vehicle_count, third_vehicle_count, staff_vehicle_count, 
    sq_driver_count, company_driver_count, business_driver_count, third_driver_count, 
    sq_device_count, self_device_count, apply_count, sq_maintenance_count, company_maintenance_count, 
    charge_order_count, sq_violation_count, company_violation_count, company_attribute, 
    account_type
  </sql>
  <select id="selectByExample" parameterType="com.izu.business.entity.StatisticsSystemViewExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from statistics_system_view
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from statistics_system_view
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from statistics_system_view
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.business.entity.StatisticsSystemViewExample">
    delete from statistics_system_view
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.business.entity.StatisticsSystemView">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into statistics_system_view (create_time, update_time, company_id, 
      company_code, company_name, customer_pv, 
      customer_uv, web_pv, android_pv, 
      ios_pv, web_uv, android_uv, 
      ios_uv, stat_date, stat_week, 
      stat_month, stat_season, stat_year, 
      internal_order_count, motorcade_order_count, 
      private_order_count, so_order_count, co_order_count, 
      gov_public_order_count, staff_count, sq_vehicle_count, 
      company_vehicle_count, third_vehicle_count, 
      staff_vehicle_count, sq_driver_count, company_driver_count, 
      business_driver_count, third_driver_count, 
      sq_device_count, self_device_count, apply_count, 
      sq_maintenance_count, company_maintenance_count, 
      charge_order_count, sq_violation_count, company_violation_count, 
      company_attribute, account_type)
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{companyId,jdbcType=INTEGER}, 
      #{companyCode,jdbcType=VARCHAR}, #{companyName,jdbcType=VARCHAR}, #{customerPv,jdbcType=INTEGER}, 
      #{customerUv,jdbcType=INTEGER}, #{webPv,jdbcType=INTEGER}, #{androidPv,jdbcType=INTEGER}, 
      #{iosPv,jdbcType=INTEGER}, #{webUv,jdbcType=INTEGER}, #{androidUv,jdbcType=INTEGER}, 
      #{iosUv,jdbcType=INTEGER}, #{statDate,jdbcType=DATE}, #{statWeek,jdbcType=INTEGER}, 
      #{statMonth,jdbcType=INTEGER}, #{statSeason,jdbcType=INTEGER}, #{statYear,jdbcType=INTEGER}, 
      #{internalOrderCount,jdbcType=INTEGER}, #{motorcadeOrderCount,jdbcType=INTEGER}, 
      #{privateOrderCount,jdbcType=INTEGER}, #{soOrderCount,jdbcType=INTEGER}, #{coOrderCount,jdbcType=INTEGER}, 
      #{govPublicOrderCount,jdbcType=INTEGER}, #{staffCount,jdbcType=INTEGER}, #{sqVehicleCount,jdbcType=INTEGER}, 
      #{companyVehicleCount,jdbcType=INTEGER}, #{thirdVehicleCount,jdbcType=INTEGER}, 
      #{staffVehicleCount,jdbcType=INTEGER}, #{sqDriverCount,jdbcType=INTEGER}, #{companyDriverCount,jdbcType=INTEGER}, 
      #{businessDriverCount,jdbcType=INTEGER}, #{thirdDriverCount,jdbcType=INTEGER}, 
      #{sqDeviceCount,jdbcType=INTEGER}, #{selfDeviceCount,jdbcType=INTEGER}, #{applyCount,jdbcType=INTEGER}, 
      #{sqMaintenanceCount,jdbcType=INTEGER}, #{companyMaintenanceCount,jdbcType=INTEGER}, 
      #{chargeOrderCount,jdbcType=INTEGER}, #{sqViolationCount,jdbcType=INTEGER}, #{companyViolationCount,jdbcType=INTEGER}, 
      #{companyAttribute,jdbcType=TINYINT}, #{accountType,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.business.entity.StatisticsSystemView">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into statistics_system_view
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="customerPv != null">
        customer_pv,
      </if>
      <if test="customerUv != null">
        customer_uv,
      </if>
      <if test="webPv != null">
        web_pv,
      </if>
      <if test="androidPv != null">
        android_pv,
      </if>
      <if test="iosPv != null">
        ios_pv,
      </if>
      <if test="webUv != null">
        web_uv,
      </if>
      <if test="androidUv != null">
        android_uv,
      </if>
      <if test="iosUv != null">
        ios_uv,
      </if>
      <if test="statDate != null">
        stat_date,
      </if>
      <if test="statWeek != null">
        stat_week,
      </if>
      <if test="statMonth != null">
        stat_month,
      </if>
      <if test="statSeason != null">
        stat_season,
      </if>
      <if test="statYear != null">
        stat_year,
      </if>
      <if test="internalOrderCount != null">
        internal_order_count,
      </if>
      <if test="motorcadeOrderCount != null">
        motorcade_order_count,
      </if>
      <if test="privateOrderCount != null">
        private_order_count,
      </if>
      <if test="soOrderCount != null">
        so_order_count,
      </if>
      <if test="coOrderCount != null">
        co_order_count,
      </if>
      <if test="govPublicOrderCount != null">
        gov_public_order_count,
      </if>
      <if test="staffCount != null">
        staff_count,
      </if>
      <if test="sqVehicleCount != null">
        sq_vehicle_count,
      </if>
      <if test="companyVehicleCount != null">
        company_vehicle_count,
      </if>
      <if test="thirdVehicleCount != null">
        third_vehicle_count,
      </if>
      <if test="staffVehicleCount != null">
        staff_vehicle_count,
      </if>
      <if test="sqDriverCount != null">
        sq_driver_count,
      </if>
      <if test="companyDriverCount != null">
        company_driver_count,
      </if>
      <if test="businessDriverCount != null">
        business_driver_count,
      </if>
      <if test="thirdDriverCount != null">
        third_driver_count,
      </if>
      <if test="sqDeviceCount != null">
        sq_device_count,
      </if>
      <if test="selfDeviceCount != null">
        self_device_count,
      </if>
      <if test="applyCount != null">
        apply_count,
      </if>
      <if test="sqMaintenanceCount != null">
        sq_maintenance_count,
      </if>
      <if test="companyMaintenanceCount != null">
        company_maintenance_count,
      </if>
      <if test="chargeOrderCount != null">
        charge_order_count,
      </if>
      <if test="sqViolationCount != null">
        sq_violation_count,
      </if>
      <if test="companyViolationCount != null">
        company_violation_count,
      </if>
      <if test="companyAttribute != null">
        company_attribute,
      </if>
      <if test="accountType != null">
        account_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="customerPv != null">
        #{customerPv,jdbcType=INTEGER},
      </if>
      <if test="customerUv != null">
        #{customerUv,jdbcType=INTEGER},
      </if>
      <if test="webPv != null">
        #{webPv,jdbcType=INTEGER},
      </if>
      <if test="androidPv != null">
        #{androidPv,jdbcType=INTEGER},
      </if>
      <if test="iosPv != null">
        #{iosPv,jdbcType=INTEGER},
      </if>
      <if test="webUv != null">
        #{webUv,jdbcType=INTEGER},
      </if>
      <if test="androidUv != null">
        #{androidUv,jdbcType=INTEGER},
      </if>
      <if test="iosUv != null">
        #{iosUv,jdbcType=INTEGER},
      </if>
      <if test="statDate != null">
        #{statDate,jdbcType=DATE},
      </if>
      <if test="statWeek != null">
        #{statWeek,jdbcType=INTEGER},
      </if>
      <if test="statMonth != null">
        #{statMonth,jdbcType=INTEGER},
      </if>
      <if test="statSeason != null">
        #{statSeason,jdbcType=INTEGER},
      </if>
      <if test="statYear != null">
        #{statYear,jdbcType=INTEGER},
      </if>
      <if test="internalOrderCount != null">
        #{internalOrderCount,jdbcType=INTEGER},
      </if>
      <if test="motorcadeOrderCount != null">
        #{motorcadeOrderCount,jdbcType=INTEGER},
      </if>
      <if test="privateOrderCount != null">
        #{privateOrderCount,jdbcType=INTEGER},
      </if>
      <if test="soOrderCount != null">
        #{soOrderCount,jdbcType=INTEGER},
      </if>
      <if test="coOrderCount != null">
        #{coOrderCount,jdbcType=INTEGER},
      </if>
      <if test="govPublicOrderCount != null">
        #{govPublicOrderCount,jdbcType=INTEGER},
      </if>
      <if test="staffCount != null">
        #{staffCount,jdbcType=INTEGER},
      </if>
      <if test="sqVehicleCount != null">
        #{sqVehicleCount,jdbcType=INTEGER},
      </if>
      <if test="companyVehicleCount != null">
        #{companyVehicleCount,jdbcType=INTEGER},
      </if>
      <if test="thirdVehicleCount != null">
        #{thirdVehicleCount,jdbcType=INTEGER},
      </if>
      <if test="staffVehicleCount != null">
        #{staffVehicleCount,jdbcType=INTEGER},
      </if>
      <if test="sqDriverCount != null">
        #{sqDriverCount,jdbcType=INTEGER},
      </if>
      <if test="companyDriverCount != null">
        #{companyDriverCount,jdbcType=INTEGER},
      </if>
      <if test="businessDriverCount != null">
        #{businessDriverCount,jdbcType=INTEGER},
      </if>
      <if test="thirdDriverCount != null">
        #{thirdDriverCount,jdbcType=INTEGER},
      </if>
      <if test="sqDeviceCount != null">
        #{sqDeviceCount,jdbcType=INTEGER},
      </if>
      <if test="selfDeviceCount != null">
        #{selfDeviceCount,jdbcType=INTEGER},
      </if>
      <if test="applyCount != null">
        #{applyCount,jdbcType=INTEGER},
      </if>
      <if test="sqMaintenanceCount != null">
        #{sqMaintenanceCount,jdbcType=INTEGER},
      </if>
      <if test="companyMaintenanceCount != null">
        #{companyMaintenanceCount,jdbcType=INTEGER},
      </if>
      <if test="chargeOrderCount != null">
        #{chargeOrderCount,jdbcType=INTEGER},
      </if>
      <if test="sqViolationCount != null">
        #{sqViolationCount,jdbcType=INTEGER},
      </if>
      <if test="companyViolationCount != null">
        #{companyViolationCount,jdbcType=INTEGER},
      </if>
      <if test="companyAttribute != null">
        #{companyAttribute,jdbcType=TINYINT},
      </if>
      <if test="accountType != null">
        #{accountType,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.business.entity.StatisticsSystemViewExample" resultType="java.lang.Long">
    select count(*) from statistics_system_view
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update statistics_system_view
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.companyId != null">
        company_id = #{row.companyId,jdbcType=INTEGER},
      </if>
      <if test="row.companyCode != null">
        company_code = #{row.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="row.companyName != null">
        company_name = #{row.companyName,jdbcType=VARCHAR},
      </if>
      <if test="row.customerPv != null">
        customer_pv = #{row.customerPv,jdbcType=INTEGER},
      </if>
      <if test="row.customerUv != null">
        customer_uv = #{row.customerUv,jdbcType=INTEGER},
      </if>
      <if test="row.webPv != null">
        web_pv = #{row.webPv,jdbcType=INTEGER},
      </if>
      <if test="row.androidPv != null">
        android_pv = #{row.androidPv,jdbcType=INTEGER},
      </if>
      <if test="row.iosPv != null">
        ios_pv = #{row.iosPv,jdbcType=INTEGER},
      </if>
      <if test="row.webUv != null">
        web_uv = #{row.webUv,jdbcType=INTEGER},
      </if>
      <if test="row.androidUv != null">
        android_uv = #{row.androidUv,jdbcType=INTEGER},
      </if>
      <if test="row.iosUv != null">
        ios_uv = #{row.iosUv,jdbcType=INTEGER},
      </if>
      <if test="row.statDate != null">
        stat_date = #{row.statDate,jdbcType=DATE},
      </if>
      <if test="row.statWeek != null">
        stat_week = #{row.statWeek,jdbcType=INTEGER},
      </if>
      <if test="row.statMonth != null">
        stat_month = #{row.statMonth,jdbcType=INTEGER},
      </if>
      <if test="row.statSeason != null">
        stat_season = #{row.statSeason,jdbcType=INTEGER},
      </if>
      <if test="row.statYear != null">
        stat_year = #{row.statYear,jdbcType=INTEGER},
      </if>
      <if test="row.internalOrderCount != null">
        internal_order_count = #{row.internalOrderCount,jdbcType=INTEGER},
      </if>
      <if test="row.motorcadeOrderCount != null">
        motorcade_order_count = #{row.motorcadeOrderCount,jdbcType=INTEGER},
      </if>
      <if test="row.privateOrderCount != null">
        private_order_count = #{row.privateOrderCount,jdbcType=INTEGER},
      </if>
      <if test="row.soOrderCount != null">
        so_order_count = #{row.soOrderCount,jdbcType=INTEGER},
      </if>
      <if test="row.coOrderCount != null">
        co_order_count = #{row.coOrderCount,jdbcType=INTEGER},
      </if>
      <if test="row.govPublicOrderCount != null">
        gov_public_order_count = #{row.govPublicOrderCount,jdbcType=INTEGER},
      </if>
      <if test="row.staffCount != null">
        staff_count = #{row.staffCount,jdbcType=INTEGER},
      </if>
      <if test="row.sqVehicleCount != null">
        sq_vehicle_count = #{row.sqVehicleCount,jdbcType=INTEGER},
      </if>
      <if test="row.companyVehicleCount != null">
        company_vehicle_count = #{row.companyVehicleCount,jdbcType=INTEGER},
      </if>
      <if test="row.thirdVehicleCount != null">
        third_vehicle_count = #{row.thirdVehicleCount,jdbcType=INTEGER},
      </if>
      <if test="row.staffVehicleCount != null">
        staff_vehicle_count = #{row.staffVehicleCount,jdbcType=INTEGER},
      </if>
      <if test="row.sqDriverCount != null">
        sq_driver_count = #{row.sqDriverCount,jdbcType=INTEGER},
      </if>
      <if test="row.companyDriverCount != null">
        company_driver_count = #{row.companyDriverCount,jdbcType=INTEGER},
      </if>
      <if test="row.businessDriverCount != null">
        business_driver_count = #{row.businessDriverCount,jdbcType=INTEGER},
      </if>
      <if test="row.thirdDriverCount != null">
        third_driver_count = #{row.thirdDriverCount,jdbcType=INTEGER},
      </if>
      <if test="row.sqDeviceCount != null">
        sq_device_count = #{row.sqDeviceCount,jdbcType=INTEGER},
      </if>
      <if test="row.selfDeviceCount != null">
        self_device_count = #{row.selfDeviceCount,jdbcType=INTEGER},
      </if>
      <if test="row.applyCount != null">
        apply_count = #{row.applyCount,jdbcType=INTEGER},
      </if>
      <if test="row.sqMaintenanceCount != null">
        sq_maintenance_count = #{row.sqMaintenanceCount,jdbcType=INTEGER},
      </if>
      <if test="row.companyMaintenanceCount != null">
        company_maintenance_count = #{row.companyMaintenanceCount,jdbcType=INTEGER},
      </if>
      <if test="row.chargeOrderCount != null">
        charge_order_count = #{row.chargeOrderCount,jdbcType=INTEGER},
      </if>
      <if test="row.sqViolationCount != null">
        sq_violation_count = #{row.sqViolationCount,jdbcType=INTEGER},
      </if>
      <if test="row.companyViolationCount != null">
        company_violation_count = #{row.companyViolationCount,jdbcType=INTEGER},
      </if>
      <if test="row.companyAttribute != null">
        company_attribute = #{row.companyAttribute,jdbcType=TINYINT},
      </if>
      <if test="row.accountType != null">
        account_type = #{row.accountType,jdbcType=TINYINT},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update statistics_system_view
    set id = #{row.id,jdbcType=BIGINT},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      company_id = #{row.companyId,jdbcType=INTEGER},
      company_code = #{row.companyCode,jdbcType=VARCHAR},
      company_name = #{row.companyName,jdbcType=VARCHAR},
      customer_pv = #{row.customerPv,jdbcType=INTEGER},
      customer_uv = #{row.customerUv,jdbcType=INTEGER},
      web_pv = #{row.webPv,jdbcType=INTEGER},
      android_pv = #{row.androidPv,jdbcType=INTEGER},
      ios_pv = #{row.iosPv,jdbcType=INTEGER},
      web_uv = #{row.webUv,jdbcType=INTEGER},
      android_uv = #{row.androidUv,jdbcType=INTEGER},
      ios_uv = #{row.iosUv,jdbcType=INTEGER},
      stat_date = #{row.statDate,jdbcType=DATE},
      stat_week = #{row.statWeek,jdbcType=INTEGER},
      stat_month = #{row.statMonth,jdbcType=INTEGER},
      stat_season = #{row.statSeason,jdbcType=INTEGER},
      stat_year = #{row.statYear,jdbcType=INTEGER},
      internal_order_count = #{row.internalOrderCount,jdbcType=INTEGER},
      motorcade_order_count = #{row.motorcadeOrderCount,jdbcType=INTEGER},
      private_order_count = #{row.privateOrderCount,jdbcType=INTEGER},
      so_order_count = #{row.soOrderCount,jdbcType=INTEGER},
      co_order_count = #{row.coOrderCount,jdbcType=INTEGER},
      gov_public_order_count = #{row.govPublicOrderCount,jdbcType=INTEGER},
      staff_count = #{row.staffCount,jdbcType=INTEGER},
      sq_vehicle_count = #{row.sqVehicleCount,jdbcType=INTEGER},
      company_vehicle_count = #{row.companyVehicleCount,jdbcType=INTEGER},
      third_vehicle_count = #{row.thirdVehicleCount,jdbcType=INTEGER},
      staff_vehicle_count = #{row.staffVehicleCount,jdbcType=INTEGER},
      sq_driver_count = #{row.sqDriverCount,jdbcType=INTEGER},
      company_driver_count = #{row.companyDriverCount,jdbcType=INTEGER},
      business_driver_count = #{row.businessDriverCount,jdbcType=INTEGER},
      third_driver_count = #{row.thirdDriverCount,jdbcType=INTEGER},
      sq_device_count = #{row.sqDeviceCount,jdbcType=INTEGER},
      self_device_count = #{row.selfDeviceCount,jdbcType=INTEGER},
      apply_count = #{row.applyCount,jdbcType=INTEGER},
      sq_maintenance_count = #{row.sqMaintenanceCount,jdbcType=INTEGER},
      company_maintenance_count = #{row.companyMaintenanceCount,jdbcType=INTEGER},
      charge_order_count = #{row.chargeOrderCount,jdbcType=INTEGER},
      sq_violation_count = #{row.sqViolationCount,jdbcType=INTEGER},
      company_violation_count = #{row.companyViolationCount,jdbcType=INTEGER},
      company_attribute = #{row.companyAttribute,jdbcType=TINYINT},
      account_type = #{row.accountType,jdbcType=TINYINT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.business.entity.StatisticsSystemView">
    update statistics_system_view
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="customerPv != null">
        customer_pv = #{customerPv,jdbcType=INTEGER},
      </if>
      <if test="customerUv != null">
        customer_uv = #{customerUv,jdbcType=INTEGER},
      </if>
      <if test="webPv != null">
        web_pv = #{webPv,jdbcType=INTEGER},
      </if>
      <if test="androidPv != null">
        android_pv = #{androidPv,jdbcType=INTEGER},
      </if>
      <if test="iosPv != null">
        ios_pv = #{iosPv,jdbcType=INTEGER},
      </if>
      <if test="webUv != null">
        web_uv = #{webUv,jdbcType=INTEGER},
      </if>
      <if test="androidUv != null">
        android_uv = #{androidUv,jdbcType=INTEGER},
      </if>
      <if test="iosUv != null">
        ios_uv = #{iosUv,jdbcType=INTEGER},
      </if>
      <if test="statDate != null">
        stat_date = #{statDate,jdbcType=DATE},
      </if>
      <if test="statWeek != null">
        stat_week = #{statWeek,jdbcType=INTEGER},
      </if>
      <if test="statMonth != null">
        stat_month = #{statMonth,jdbcType=INTEGER},
      </if>
      <if test="statSeason != null">
        stat_season = #{statSeason,jdbcType=INTEGER},
      </if>
      <if test="statYear != null">
        stat_year = #{statYear,jdbcType=INTEGER},
      </if>
      <if test="internalOrderCount != null">
        internal_order_count = #{internalOrderCount,jdbcType=INTEGER},
      </if>
      <if test="motorcadeOrderCount != null">
        motorcade_order_count = #{motorcadeOrderCount,jdbcType=INTEGER},
      </if>
      <if test="privateOrderCount != null">
        private_order_count = #{privateOrderCount,jdbcType=INTEGER},
      </if>
      <if test="soOrderCount != null">
        so_order_count = #{soOrderCount,jdbcType=INTEGER},
      </if>
      <if test="coOrderCount != null">
        co_order_count = #{coOrderCount,jdbcType=INTEGER},
      </if>
      <if test="govPublicOrderCount != null">
        gov_public_order_count = #{govPublicOrderCount,jdbcType=INTEGER},
      </if>
      <if test="staffCount != null">
        staff_count = #{staffCount,jdbcType=INTEGER},
      </if>
      <if test="sqVehicleCount != null">
        sq_vehicle_count = #{sqVehicleCount,jdbcType=INTEGER},
      </if>
      <if test="companyVehicleCount != null">
        company_vehicle_count = #{companyVehicleCount,jdbcType=INTEGER},
      </if>
      <if test="thirdVehicleCount != null">
        third_vehicle_count = #{thirdVehicleCount,jdbcType=INTEGER},
      </if>
      <if test="staffVehicleCount != null">
        staff_vehicle_count = #{staffVehicleCount,jdbcType=INTEGER},
      </if>
      <if test="sqDriverCount != null">
        sq_driver_count = #{sqDriverCount,jdbcType=INTEGER},
      </if>
      <if test="companyDriverCount != null">
        company_driver_count = #{companyDriverCount,jdbcType=INTEGER},
      </if>
      <if test="businessDriverCount != null">
        business_driver_count = #{businessDriverCount,jdbcType=INTEGER},
      </if>
      <if test="thirdDriverCount != null">
        third_driver_count = #{thirdDriverCount,jdbcType=INTEGER},
      </if>
      <if test="sqDeviceCount != null">
        sq_device_count = #{sqDeviceCount,jdbcType=INTEGER},
      </if>
      <if test="selfDeviceCount != null">
        self_device_count = #{selfDeviceCount,jdbcType=INTEGER},
      </if>
      <if test="applyCount != null">
        apply_count = #{applyCount,jdbcType=INTEGER},
      </if>
      <if test="sqMaintenanceCount != null">
        sq_maintenance_count = #{sqMaintenanceCount,jdbcType=INTEGER},
      </if>
      <if test="companyMaintenanceCount != null">
        company_maintenance_count = #{companyMaintenanceCount,jdbcType=INTEGER},
      </if>
      <if test="chargeOrderCount != null">
        charge_order_count = #{chargeOrderCount,jdbcType=INTEGER},
      </if>
      <if test="sqViolationCount != null">
        sq_violation_count = #{sqViolationCount,jdbcType=INTEGER},
      </if>
      <if test="companyViolationCount != null">
        company_violation_count = #{companyViolationCount,jdbcType=INTEGER},
      </if>
      <if test="companyAttribute != null">
        company_attribute = #{companyAttribute,jdbcType=TINYINT},
      </if>
      <if test="accountType != null">
        account_type = #{accountType,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.business.entity.StatisticsSystemView">
    update statistics_system_view
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      company_id = #{companyId,jdbcType=INTEGER},
      company_code = #{companyCode,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      customer_pv = #{customerPv,jdbcType=INTEGER},
      customer_uv = #{customerUv,jdbcType=INTEGER},
      web_pv = #{webPv,jdbcType=INTEGER},
      android_pv = #{androidPv,jdbcType=INTEGER},
      ios_pv = #{iosPv,jdbcType=INTEGER},
      web_uv = #{webUv,jdbcType=INTEGER},
      android_uv = #{androidUv,jdbcType=INTEGER},
      ios_uv = #{iosUv,jdbcType=INTEGER},
      stat_date = #{statDate,jdbcType=DATE},
      stat_week = #{statWeek,jdbcType=INTEGER},
      stat_month = #{statMonth,jdbcType=INTEGER},
      stat_season = #{statSeason,jdbcType=INTEGER},
      stat_year = #{statYear,jdbcType=INTEGER},
      internal_order_count = #{internalOrderCount,jdbcType=INTEGER},
      motorcade_order_count = #{motorcadeOrderCount,jdbcType=INTEGER},
      private_order_count = #{privateOrderCount,jdbcType=INTEGER},
      so_order_count = #{soOrderCount,jdbcType=INTEGER},
      co_order_count = #{coOrderCount,jdbcType=INTEGER},
      gov_public_order_count = #{govPublicOrderCount,jdbcType=INTEGER},
      staff_count = #{staffCount,jdbcType=INTEGER},
      sq_vehicle_count = #{sqVehicleCount,jdbcType=INTEGER},
      company_vehicle_count = #{companyVehicleCount,jdbcType=INTEGER},
      third_vehicle_count = #{thirdVehicleCount,jdbcType=INTEGER},
      staff_vehicle_count = #{staffVehicleCount,jdbcType=INTEGER},
      sq_driver_count = #{sqDriverCount,jdbcType=INTEGER},
      company_driver_count = #{companyDriverCount,jdbcType=INTEGER},
      business_driver_count = #{businessDriverCount,jdbcType=INTEGER},
      third_driver_count = #{thirdDriverCount,jdbcType=INTEGER},
      sq_device_count = #{sqDeviceCount,jdbcType=INTEGER},
      self_device_count = #{selfDeviceCount,jdbcType=INTEGER},
      apply_count = #{applyCount,jdbcType=INTEGER},
      sq_maintenance_count = #{sqMaintenanceCount,jdbcType=INTEGER},
      company_maintenance_count = #{companyMaintenanceCount,jdbcType=INTEGER},
      charge_order_count = #{chargeOrderCount,jdbcType=INTEGER},
      sq_violation_count = #{sqViolationCount,jdbcType=INTEGER},
      company_violation_count = #{companyViolationCount,jdbcType=INTEGER},
      company_attribute = #{companyAttribute,jdbcType=TINYINT},
      account_type = #{accountType,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>