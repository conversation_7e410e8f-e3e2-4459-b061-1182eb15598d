<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.LeaseContractBussMapper">
  <resultMap id="BaseResultMap" type="com.izu.business.entity.LeaseContractBuss">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="buss_contract_id" jdbcType="INTEGER" property="bussContractId" />
    <result column="standard_contract" jdbcType="INTEGER" property="standardContract" />
    <result column="contract_code" jdbcType="VARCHAR" property="contractCode" />
    <result column="contract_type" jdbcType="INTEGER" property="contractType" />
    <result column="main_contract_code" jdbcType="VARCHAR" property="mainContractCode" />
    <result column="customer_id" jdbcType="INTEGER" property="customerId" />
    <result column="customer_code" jdbcType="VARCHAR" property="customerCode" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="lease_period" jdbcType="INTEGER" property="leasePeriod" />
    <result column="contract_sdate" jdbcType="DATE" property="contractSdate" />
    <result column="contract_edate" jdbcType="DATE" property="contractEdate" />
    <result column="pay_period" jdbcType="INTEGER" property="payPeriod" />
    <result column="contract_status" jdbcType="INTEGER" property="contractStatus" />
    <result column="buss_status" jdbcType="INTEGER" property="bussStatus" />
    <result column="total_rent" jdbcType="DECIMAL" property="totalRent" />
    <result column="total_deposit" jdbcType="DECIMAL" property="totalDeposit" />
    <result column="maintain_id" jdbcType="VARCHAR" property="maintainId" />
    <result column="maintain_name" jdbcType="VARCHAR" property="maintainName" />
    <result column="sign_sales_id" jdbcType="VARCHAR" property="signSalesId" />
    <result column="sign_sales_name" jdbcType="VARCHAR" property="signSalesName" />
    <result column="struct_business_license" jdbcType="VARCHAR" property="structBusinessLicense" />
    <result column="signing_subject_name" jdbcType="VARCHAR" property="signingSubjectName" />
    <result column="sign_date" jdbcType="DATE" property="signDate" />
    <result column="bookkeeping_method" jdbcType="TINYINT" property="bookkeepingMethod" />
    <result column="bill_day" jdbcType="TINYINT" property="billDay" />
    <result column="audit_status" jdbcType="INTEGER" property="auditStatus" />
    <result column="contract_sign_status" jdbcType="INTEGER" property="contractSignStatus" />
    <result column="struct_code" jdbcType="VARCHAR" property="structCode" />
    <result column="struct_name" jdbcType="VARCHAR" property="structName" />
    <result column="contract_tag_code" jdbcType="VARCHAR" property="contractTagCode" />
    <result column="contract_tag_name" jdbcType="VARCHAR" property="contractTagName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, buss_contract_id, standard_contract, contract_code, contract_type, main_contract_code, 
    customer_id, customer_code, customer_name, lease_period, contract_sdate, contract_edate, 
    pay_period, contract_status, buss_status, total_rent, total_deposit, maintain_id, 
    maintain_name, sign_sales_id, sign_sales_name, struct_business_license, signing_subject_name, 
    sign_date, bookkeeping_method, bill_day, audit_status, contract_sign_status, struct_code, 
    struct_name, contract_tag_code, contract_tag_name
  </sql>
  <select id="selectByExample" parameterType="com.izu.business.entity.LeaseContractBussExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lease_contract_buss
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lease_contract_buss
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from lease_contract_buss
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.business.entity.LeaseContractBussExample">
    delete from lease_contract_buss
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.business.entity.LeaseContractBuss">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lease_contract_buss (buss_contract_id, standard_contract, 
      contract_code, contract_type, main_contract_code, 
      customer_id, customer_code, customer_name, 
      lease_period, contract_sdate, contract_edate, 
      pay_period, contract_status, buss_status, 
      total_rent, total_deposit, maintain_id, 
      maintain_name, sign_sales_id, sign_sales_name, 
      struct_business_license, signing_subject_name, 
      sign_date, bookkeeping_method, bill_day, 
      audit_status, contract_sign_status, struct_code, 
      struct_name, contract_tag_code, contract_tag_name
      )
    values (#{bussContractId,jdbcType=INTEGER}, #{standardContract,jdbcType=INTEGER}, 
      #{contractCode,jdbcType=VARCHAR}, #{contractType,jdbcType=INTEGER}, #{mainContractCode,jdbcType=VARCHAR}, 
      #{customerId,jdbcType=INTEGER}, #{customerCode,jdbcType=VARCHAR}, #{customerName,jdbcType=VARCHAR}, 
      #{leasePeriod,jdbcType=INTEGER}, #{contractSdate,jdbcType=DATE}, #{contractEdate,jdbcType=DATE}, 
      #{payPeriod,jdbcType=INTEGER}, #{contractStatus,jdbcType=INTEGER}, #{bussStatus,jdbcType=INTEGER}, 
      #{totalRent,jdbcType=DECIMAL}, #{totalDeposit,jdbcType=DECIMAL}, #{maintainId,jdbcType=VARCHAR}, 
      #{maintainName,jdbcType=VARCHAR}, #{signSalesId,jdbcType=VARCHAR}, #{signSalesName,jdbcType=VARCHAR}, 
      #{structBusinessLicense,jdbcType=VARCHAR}, #{signingSubjectName,jdbcType=VARCHAR}, 
      #{signDate,jdbcType=DATE}, #{bookkeepingMethod,jdbcType=TINYINT}, #{billDay,jdbcType=TINYINT}, 
      #{auditStatus,jdbcType=INTEGER}, #{contractSignStatus,jdbcType=INTEGER}, #{structCode,jdbcType=VARCHAR}, 
      #{structName,jdbcType=VARCHAR}, #{contractTagCode,jdbcType=VARCHAR}, #{contractTagName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.business.entity.LeaseContractBuss">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lease_contract_buss
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bussContractId != null">
        buss_contract_id,
      </if>
      <if test="standardContract != null">
        standard_contract,
      </if>
      <if test="contractCode != null">
        contract_code,
      </if>
      <if test="contractType != null">
        contract_type,
      </if>
      <if test="mainContractCode != null">
        main_contract_code,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="customerCode != null">
        customer_code,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="leasePeriod != null">
        lease_period,
      </if>
      <if test="contractSdate != null">
        contract_sdate,
      </if>
      <if test="contractEdate != null">
        contract_edate,
      </if>
      <if test="payPeriod != null">
        pay_period,
      </if>
      <if test="contractStatus != null">
        contract_status,
      </if>
      <if test="bussStatus != null">
        buss_status,
      </if>
      <if test="totalRent != null">
        total_rent,
      </if>
      <if test="totalDeposit != null">
        total_deposit,
      </if>
      <if test="maintainId != null">
        maintain_id,
      </if>
      <if test="maintainName != null">
        maintain_name,
      </if>
      <if test="signSalesId != null">
        sign_sales_id,
      </if>
      <if test="signSalesName != null">
        sign_sales_name,
      </if>
      <if test="structBusinessLicense != null">
        struct_business_license,
      </if>
      <if test="signingSubjectName != null">
        signing_subject_name,
      </if>
      <if test="signDate != null">
        sign_date,
      </if>
      <if test="bookkeepingMethod != null">
        bookkeeping_method,
      </if>
      <if test="billDay != null">
        bill_day,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="contractSignStatus != null">
        contract_sign_status,
      </if>
      <if test="structCode != null">
        struct_code,
      </if>
      <if test="structName != null">
        struct_name,
      </if>
      <if test="contractTagCode != null">
        contract_tag_code,
      </if>
      <if test="contractTagName != null">
        contract_tag_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bussContractId != null">
        #{bussContractId,jdbcType=INTEGER},
      </if>
      <if test="standardContract != null">
        #{standardContract,jdbcType=INTEGER},
      </if>
      <if test="contractCode != null">
        #{contractCode,jdbcType=VARCHAR},
      </if>
      <if test="contractType != null">
        #{contractType,jdbcType=INTEGER},
      </if>
      <if test="mainContractCode != null">
        #{mainContractCode,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=INTEGER},
      </if>
      <if test="customerCode != null">
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="leasePeriod != null">
        #{leasePeriod,jdbcType=INTEGER},
      </if>
      <if test="contractSdate != null">
        #{contractSdate,jdbcType=DATE},
      </if>
      <if test="contractEdate != null">
        #{contractEdate,jdbcType=DATE},
      </if>
      <if test="payPeriod != null">
        #{payPeriod,jdbcType=INTEGER},
      </if>
      <if test="contractStatus != null">
        #{contractStatus,jdbcType=INTEGER},
      </if>
      <if test="bussStatus != null">
        #{bussStatus,jdbcType=INTEGER},
      </if>
      <if test="totalRent != null">
        #{totalRent,jdbcType=DECIMAL},
      </if>
      <if test="totalDeposit != null">
        #{totalDeposit,jdbcType=DECIMAL},
      </if>
      <if test="maintainId != null">
        #{maintainId,jdbcType=VARCHAR},
      </if>
      <if test="maintainName != null">
        #{maintainName,jdbcType=VARCHAR},
      </if>
      <if test="signSalesId != null">
        #{signSalesId,jdbcType=VARCHAR},
      </if>
      <if test="signSalesName != null">
        #{signSalesName,jdbcType=VARCHAR},
      </if>
      <if test="structBusinessLicense != null">
        #{structBusinessLicense,jdbcType=VARCHAR},
      </if>
      <if test="signingSubjectName != null">
        #{signingSubjectName,jdbcType=VARCHAR},
      </if>
      <if test="signDate != null">
        #{signDate,jdbcType=DATE},
      </if>
      <if test="bookkeepingMethod != null">
        #{bookkeepingMethod,jdbcType=TINYINT},
      </if>
      <if test="billDay != null">
        #{billDay,jdbcType=TINYINT},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="contractSignStatus != null">
        #{contractSignStatus,jdbcType=INTEGER},
      </if>
      <if test="structCode != null">
        #{structCode,jdbcType=VARCHAR},
      </if>
      <if test="structName != null">
        #{structName,jdbcType=VARCHAR},
      </if>
      <if test="contractTagCode != null">
        #{contractTagCode,jdbcType=VARCHAR},
      </if>
      <if test="contractTagName != null">
        #{contractTagName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.business.entity.LeaseContractBussExample" resultType="java.lang.Long">
    select count(*) from lease_contract_buss
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lease_contract_buss
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=INTEGER},
      </if>
      <if test="row.bussContractId != null">
        buss_contract_id = #{row.bussContractId,jdbcType=INTEGER},
      </if>
      <if test="row.standardContract != null">
        standard_contract = #{row.standardContract,jdbcType=INTEGER},
      </if>
      <if test="row.contractCode != null">
        contract_code = #{row.contractCode,jdbcType=VARCHAR},
      </if>
      <if test="row.contractType != null">
        contract_type = #{row.contractType,jdbcType=INTEGER},
      </if>
      <if test="row.mainContractCode != null">
        main_contract_code = #{row.mainContractCode,jdbcType=VARCHAR},
      </if>
      <if test="row.customerId != null">
        customer_id = #{row.customerId,jdbcType=INTEGER},
      </if>
      <if test="row.customerCode != null">
        customer_code = #{row.customerCode,jdbcType=VARCHAR},
      </if>
      <if test="row.customerName != null">
        customer_name = #{row.customerName,jdbcType=VARCHAR},
      </if>
      <if test="row.leasePeriod != null">
        lease_period = #{row.leasePeriod,jdbcType=INTEGER},
      </if>
      <if test="row.contractSdate != null">
        contract_sdate = #{row.contractSdate,jdbcType=DATE},
      </if>
      <if test="row.contractEdate != null">
        contract_edate = #{row.contractEdate,jdbcType=DATE},
      </if>
      <if test="row.payPeriod != null">
        pay_period = #{row.payPeriod,jdbcType=INTEGER},
      </if>
      <if test="row.contractStatus != null">
        contract_status = #{row.contractStatus,jdbcType=INTEGER},
      </if>
      <if test="row.bussStatus != null">
        buss_status = #{row.bussStatus,jdbcType=INTEGER},
      </if>
      <if test="row.totalRent != null">
        total_rent = #{row.totalRent,jdbcType=DECIMAL},
      </if>
      <if test="row.totalDeposit != null">
        total_deposit = #{row.totalDeposit,jdbcType=DECIMAL},
      </if>
      <if test="row.maintainId != null">
        maintain_id = #{row.maintainId,jdbcType=VARCHAR},
      </if>
      <if test="row.maintainName != null">
        maintain_name = #{row.maintainName,jdbcType=VARCHAR},
      </if>
      <if test="row.signSalesId != null">
        sign_sales_id = #{row.signSalesId,jdbcType=VARCHAR},
      </if>
      <if test="row.signSalesName != null">
        sign_sales_name = #{row.signSalesName,jdbcType=VARCHAR},
      </if>
      <if test="row.structBusinessLicense != null">
        struct_business_license = #{row.structBusinessLicense,jdbcType=VARCHAR},
      </if>
      <if test="row.signingSubjectName != null">
        signing_subject_name = #{row.signingSubjectName,jdbcType=VARCHAR},
      </if>
      <if test="row.signDate != null">
        sign_date = #{row.signDate,jdbcType=DATE},
      </if>
      <if test="row.bookkeepingMethod != null">
        bookkeeping_method = #{row.bookkeepingMethod,jdbcType=TINYINT},
      </if>
      <if test="row.billDay != null">
        bill_day = #{row.billDay,jdbcType=TINYINT},
      </if>
      <if test="row.auditStatus != null">
        audit_status = #{row.auditStatus,jdbcType=INTEGER},
      </if>
      <if test="row.contractSignStatus != null">
        contract_sign_status = #{row.contractSignStatus,jdbcType=INTEGER},
      </if>
      <if test="row.structCode != null">
        struct_code = #{row.structCode,jdbcType=VARCHAR},
      </if>
      <if test="row.structName != null">
        struct_name = #{row.structName,jdbcType=VARCHAR},
      </if>
      <if test="row.contractTagCode != null">
        contract_tag_code = #{row.contractTagCode,jdbcType=VARCHAR},
      </if>
      <if test="row.contractTagName != null">
        contract_tag_name = #{row.contractTagName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lease_contract_buss
    set id = #{row.id,jdbcType=INTEGER},
      buss_contract_id = #{row.bussContractId,jdbcType=INTEGER},
      standard_contract = #{row.standardContract,jdbcType=INTEGER},
      contract_code = #{row.contractCode,jdbcType=VARCHAR},
      contract_type = #{row.contractType,jdbcType=INTEGER},
      main_contract_code = #{row.mainContractCode,jdbcType=VARCHAR},
      customer_id = #{row.customerId,jdbcType=INTEGER},
      customer_code = #{row.customerCode,jdbcType=VARCHAR},
      customer_name = #{row.customerName,jdbcType=VARCHAR},
      lease_period = #{row.leasePeriod,jdbcType=INTEGER},
      contract_sdate = #{row.contractSdate,jdbcType=DATE},
      contract_edate = #{row.contractEdate,jdbcType=DATE},
      pay_period = #{row.payPeriod,jdbcType=INTEGER},
      contract_status = #{row.contractStatus,jdbcType=INTEGER},
      buss_status = #{row.bussStatus,jdbcType=INTEGER},
      total_rent = #{row.totalRent,jdbcType=DECIMAL},
      total_deposit = #{row.totalDeposit,jdbcType=DECIMAL},
      maintain_id = #{row.maintainId,jdbcType=VARCHAR},
      maintain_name = #{row.maintainName,jdbcType=VARCHAR},
      sign_sales_id = #{row.signSalesId,jdbcType=VARCHAR},
      sign_sales_name = #{row.signSalesName,jdbcType=VARCHAR},
      struct_business_license = #{row.structBusinessLicense,jdbcType=VARCHAR},
      signing_subject_name = #{row.signingSubjectName,jdbcType=VARCHAR},
      sign_date = #{row.signDate,jdbcType=DATE},
      bookkeeping_method = #{row.bookkeepingMethod,jdbcType=TINYINT},
      bill_day = #{row.billDay,jdbcType=TINYINT},
      audit_status = #{row.auditStatus,jdbcType=INTEGER},
      contract_sign_status = #{row.contractSignStatus,jdbcType=INTEGER},
      struct_code = #{row.structCode,jdbcType=VARCHAR},
      struct_name = #{row.structName,jdbcType=VARCHAR},
      contract_tag_code = #{row.contractTagCode,jdbcType=VARCHAR},
      contract_tag_name = #{row.contractTagName,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.business.entity.LeaseContractBuss">
    update lease_contract_buss
    <set>
      <if test="bussContractId != null">
        buss_contract_id = #{bussContractId,jdbcType=INTEGER},
      </if>
      <if test="standardContract != null">
        standard_contract = #{standardContract,jdbcType=INTEGER},
      </if>
      <if test="contractCode != null">
        contract_code = #{contractCode,jdbcType=VARCHAR},
      </if>
      <if test="contractType != null">
        contract_type = #{contractType,jdbcType=INTEGER},
      </if>
      <if test="mainContractCode != null">
        main_contract_code = #{mainContractCode,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=INTEGER},
      </if>
      <if test="customerCode != null">
        customer_code = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null">
        customer_name = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="leasePeriod != null">
        lease_period = #{leasePeriod,jdbcType=INTEGER},
      </if>
      <if test="contractSdate != null">
        contract_sdate = #{contractSdate,jdbcType=DATE},
      </if>
      <if test="contractEdate != null">
        contract_edate = #{contractEdate,jdbcType=DATE},
      </if>
      <if test="payPeriod != null">
        pay_period = #{payPeriod,jdbcType=INTEGER},
      </if>
      <if test="contractStatus != null">
        contract_status = #{contractStatus,jdbcType=INTEGER},
      </if>
      <if test="bussStatus != null">
        buss_status = #{bussStatus,jdbcType=INTEGER},
      </if>
      <if test="totalRent != null">
        total_rent = #{totalRent,jdbcType=DECIMAL},
      </if>
      <if test="totalDeposit != null">
        total_deposit = #{totalDeposit,jdbcType=DECIMAL},
      </if>
      <if test="maintainId != null">
        maintain_id = #{maintainId,jdbcType=VARCHAR},
      </if>
      <if test="maintainName != null">
        maintain_name = #{maintainName,jdbcType=VARCHAR},
      </if>
      <if test="signSalesId != null">
        sign_sales_id = #{signSalesId,jdbcType=VARCHAR},
      </if>
      <if test="signSalesName != null">
        sign_sales_name = #{signSalesName,jdbcType=VARCHAR},
      </if>
      <if test="structBusinessLicense != null">
        struct_business_license = #{structBusinessLicense,jdbcType=VARCHAR},
      </if>
      <if test="signingSubjectName != null">
        signing_subject_name = #{signingSubjectName,jdbcType=VARCHAR},
      </if>
      <if test="signDate != null">
        sign_date = #{signDate,jdbcType=DATE},
      </if>
      <if test="bookkeepingMethod != null">
        bookkeeping_method = #{bookkeepingMethod,jdbcType=TINYINT},
      </if>
      <if test="billDay != null">
        bill_day = #{billDay,jdbcType=TINYINT},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="contractSignStatus != null">
        contract_sign_status = #{contractSignStatus,jdbcType=INTEGER},
      </if>
      <if test="structCode != null">
        struct_code = #{structCode,jdbcType=VARCHAR},
      </if>
      <if test="structName != null">
        struct_name = #{structName,jdbcType=VARCHAR},
      </if>
      <if test="contractTagCode != null">
        contract_tag_code = #{contractTagCode,jdbcType=VARCHAR},
      </if>
      <if test="contractTagName != null">
        contract_tag_name = #{contractTagName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.business.entity.LeaseContractBuss">
    update lease_contract_buss
    set buss_contract_id = #{bussContractId,jdbcType=INTEGER},
      standard_contract = #{standardContract,jdbcType=INTEGER},
      contract_code = #{contractCode,jdbcType=VARCHAR},
      contract_type = #{contractType,jdbcType=INTEGER},
      main_contract_code = #{mainContractCode,jdbcType=VARCHAR},
      customer_id = #{customerId,jdbcType=INTEGER},
      customer_code = #{customerCode,jdbcType=VARCHAR},
      customer_name = #{customerName,jdbcType=VARCHAR},
      lease_period = #{leasePeriod,jdbcType=INTEGER},
      contract_sdate = #{contractSdate,jdbcType=DATE},
      contract_edate = #{contractEdate,jdbcType=DATE},
      pay_period = #{payPeriod,jdbcType=INTEGER},
      contract_status = #{contractStatus,jdbcType=INTEGER},
      buss_status = #{bussStatus,jdbcType=INTEGER},
      total_rent = #{totalRent,jdbcType=DECIMAL},
      total_deposit = #{totalDeposit,jdbcType=DECIMAL},
      maintain_id = #{maintainId,jdbcType=VARCHAR},
      maintain_name = #{maintainName,jdbcType=VARCHAR},
      sign_sales_id = #{signSalesId,jdbcType=VARCHAR},
      sign_sales_name = #{signSalesName,jdbcType=VARCHAR},
      struct_business_license = #{structBusinessLicense,jdbcType=VARCHAR},
      signing_subject_name = #{signingSubjectName,jdbcType=VARCHAR},
      sign_date = #{signDate,jdbcType=DATE},
      bookkeeping_method = #{bookkeepingMethod,jdbcType=TINYINT},
      bill_day = #{billDay,jdbcType=TINYINT},
      audit_status = #{auditStatus,jdbcType=INTEGER},
      contract_sign_status = #{contractSignStatus,jdbcType=INTEGER},
      struct_code = #{structCode,jdbcType=VARCHAR},
      struct_name = #{structName,jdbcType=VARCHAR},
      contract_tag_code = #{contractTagCode,jdbcType=VARCHAR},
      contract_tag_name = #{contractTagName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>