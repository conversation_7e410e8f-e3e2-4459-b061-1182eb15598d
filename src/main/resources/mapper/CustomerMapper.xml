<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.CustomerMapper">
  <resultMap id="BaseResultMap" type="com.izu.user.entity.Customer">
    <id column="customer_id" jdbcType="INTEGER" property="customerId" />
    <result column="customer_code" jdbcType="VARCHAR" property="customerCode" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="pinyin_name" jdbcType="VARCHAR" property="pinyinName" />
    <result column="english_name" jdbcType="VARCHAR" property="englishName" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="customer_no" jdbcType="VARCHAR" property="customerNo" />
    <result column="login_password" jdbcType="VARCHAR" property="loginPassword" />
    <result column="position" jdbcType="VARCHAR" property="position" />
    <result column="salt" jdbcType="VARCHAR" property="salt" />
    <result column="gender" jdbcType="TINYINT" property="gender" />
    <result column="customer_status" jdbcType="TINYINT" property="customerStatus" />
    <result column="disable_source" jdbcType="TINYINT" property="disableSource" />
    <result column="head_icon" jdbcType="VARCHAR" property="headIcon" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="provider_code" jdbcType="VARCHAR" property="providerCode" />
    <result column="data_perm_type" jdbcType="TINYINT" property="dataPermType" />
    <result column="struct_id" jdbcType="INTEGER" property="structId" />
    <result column="struct_name" jdbcType="VARCHAR" property="structName" />
    <result column="budget_over" jdbcType="TINYINT" property="budgetOver" />
    <result column="register_time" jdbcType="VARCHAR" property="registerTime" />
    <result column="join_time" jdbcType="VARCHAR" property="joinTime" />
    <result column="last_login_time" jdbcType="VARCHAR" property="lastLoginTime" />
    <result column="white_list" jdbcType="TINYINT" property="whiteList" />
    <result column="disccount_proportion" jdbcType="INTEGER" property="disccountProportion" />
    <result column="driver_id" jdbcType="INTEGER" property="driverId" />
    <result column="create_id" jdbcType="INTEGER" property="createId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="update_id" jdbcType="INTEGER" property="updateId" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="best_sign_account" jdbcType="VARCHAR" property="bestSignAccount" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    customer_id, customer_code, customer_name, pinyin_name, english_name, mobile, customer_no, 
    login_password, position, salt, gender, customer_status, disable_source, head_icon, 
    email, company_id, company_code, provider_code, data_perm_type, struct_id, struct_name, 
    budget_over, register_time, join_time, last_login_time, white_list, disccount_proportion, 
    driver_id, create_id, create_name, update_id, update_name, create_time, update_date, 
    best_sign_account
  </sql>
  <select id="selectByExample" parameterType="com.izu.user.entity.CustomerExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_customer
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_customer
    where customer_id = #{customerId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from t_customer
    where customer_id = #{customerId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.user.entity.Customer">
    <selectKey keyProperty="customerId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into t_customer (customer_code, customer_name, pinyin_name, 
      english_name, mobile, customer_no, 
      login_password, position, salt, 
      gender, customer_status, disable_source, 
      head_icon, email, company_id, 
      company_code, provider_code, data_perm_type, 
      struct_id, struct_name, budget_over, 
      register_time, join_time, last_login_time, 
      white_list, disccount_proportion, driver_id, 
      create_id, create_name, update_id, 
      update_name, create_time, update_date, 
      best_sign_account)
    values (#{customerCode,jdbcType=VARCHAR}, #{customerName,jdbcType=VARCHAR}, #{pinyinName,jdbcType=VARCHAR}, 
      #{englishName,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, #{customerNo,jdbcType=VARCHAR}, 
      #{loginPassword,jdbcType=VARCHAR}, #{position,jdbcType=VARCHAR}, #{salt,jdbcType=VARCHAR}, 
      #{gender,jdbcType=TINYINT}, #{customerStatus,jdbcType=TINYINT}, #{disableSource,jdbcType=TINYINT}, 
      #{headIcon,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, #{companyId,jdbcType=INTEGER}, 
      #{companyCode,jdbcType=VARCHAR}, #{providerCode,jdbcType=VARCHAR}, #{dataPermType,jdbcType=TINYINT}, 
      #{structId,jdbcType=INTEGER}, #{structName,jdbcType=VARCHAR}, #{budgetOver,jdbcType=TINYINT}, 
      #{registerTime,jdbcType=VARCHAR}, #{joinTime,jdbcType=VARCHAR}, #{lastLoginTime,jdbcType=VARCHAR}, 
      #{whiteList,jdbcType=TINYINT}, #{disccountProportion,jdbcType=INTEGER}, #{driverId,jdbcType=INTEGER}, 
      #{createId,jdbcType=INTEGER}, #{createName,jdbcType=VARCHAR}, #{updateId,jdbcType=INTEGER}, 
      #{updateName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateDate,jdbcType=TIMESTAMP}, 
      #{bestSignAccount,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.user.entity.Customer">
    <selectKey keyProperty="customerId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into t_customer
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="customerCode != null">
        customer_code,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="pinyinName != null">
        pinyin_name,
      </if>
      <if test="englishName != null">
        english_name,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="customerNo != null">
        customer_no,
      </if>
      <if test="loginPassword != null">
        login_password,
      </if>
      <if test="position != null">
        position,
      </if>
      <if test="salt != null">
        salt,
      </if>
      <if test="gender != null">
        gender,
      </if>
      <if test="customerStatus != null">
        customer_status,
      </if>
      <if test="disableSource != null">
        disable_source,
      </if>
      <if test="headIcon != null">
        head_icon,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="providerCode != null">
        provider_code,
      </if>
      <if test="dataPermType != null">
        data_perm_type,
      </if>
      <if test="structId != null">
        struct_id,
      </if>
      <if test="structName != null">
        struct_name,
      </if>
      <if test="budgetOver != null">
        budget_over,
      </if>
      <if test="registerTime != null">
        register_time,
      </if>
      <if test="joinTime != null">
        join_time,
      </if>
      <if test="lastLoginTime != null">
        last_login_time,
      </if>
      <if test="whiteList != null">
        white_list,
      </if>
      <if test="disccountProportion != null">
        disccount_proportion,
      </if>
      <if test="driverId != null">
        driver_id,
      </if>
      <if test="createId != null">
        create_id,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="updateId != null">
        update_id,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateDate != null">
        update_date,
      </if>
      <if test="bestSignAccount != null">
        best_sign_account,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="customerCode != null">
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="pinyinName != null">
        #{pinyinName,jdbcType=VARCHAR},
      </if>
      <if test="englishName != null">
        #{englishName,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="customerNo != null">
        #{customerNo,jdbcType=VARCHAR},
      </if>
      <if test="loginPassword != null">
        #{loginPassword,jdbcType=VARCHAR},
      </if>
      <if test="position != null">
        #{position,jdbcType=VARCHAR},
      </if>
      <if test="salt != null">
        #{salt,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=TINYINT},
      </if>
      <if test="customerStatus != null">
        #{customerStatus,jdbcType=TINYINT},
      </if>
      <if test="disableSource != null">
        #{disableSource,jdbcType=TINYINT},
      </if>
      <if test="headIcon != null">
        #{headIcon,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="providerCode != null">
        #{providerCode,jdbcType=VARCHAR},
      </if>
      <if test="dataPermType != null">
        #{dataPermType,jdbcType=TINYINT},
      </if>
      <if test="structId != null">
        #{structId,jdbcType=INTEGER},
      </if>
      <if test="structName != null">
        #{structName,jdbcType=VARCHAR},
      </if>
      <if test="budgetOver != null">
        #{budgetOver,jdbcType=TINYINT},
      </if>
      <if test="registerTime != null">
        #{registerTime,jdbcType=VARCHAR},
      </if>
      <if test="joinTime != null">
        #{joinTime,jdbcType=VARCHAR},
      </if>
      <if test="lastLoginTime != null">
        #{lastLoginTime,jdbcType=VARCHAR},
      </if>
      <if test="whiteList != null">
        #{whiteList,jdbcType=TINYINT},
      </if>
      <if test="disccountProportion != null">
        #{disccountProportion,jdbcType=INTEGER},
      </if>
      <if test="driverId != null">
        #{driverId,jdbcType=INTEGER},
      </if>
      <if test="createId != null">
        #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="updateId != null">
        #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDate != null">
        #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="bestSignAccount != null">
        #{bestSignAccount,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.user.entity.CustomerExample" resultType="java.lang.Long">
    select count(*) from t_customer
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update t_customer
    <set>
      <if test="row.customerId != null">
        customer_id = #{row.customerId,jdbcType=INTEGER},
      </if>
      <if test="row.customerCode != null">
        customer_code = #{row.customerCode,jdbcType=VARCHAR},
      </if>
      <if test="row.customerName != null">
        customer_name = #{row.customerName,jdbcType=VARCHAR},
      </if>
      <if test="row.pinyinName != null">
        pinyin_name = #{row.pinyinName,jdbcType=VARCHAR},
      </if>
      <if test="row.englishName != null">
        english_name = #{row.englishName,jdbcType=VARCHAR},
      </if>
      <if test="row.mobile != null">
        mobile = #{row.mobile,jdbcType=VARCHAR},
      </if>
      <if test="row.customerNo != null">
        customer_no = #{row.customerNo,jdbcType=VARCHAR},
      </if>
      <if test="row.loginPassword != null">
        login_password = #{row.loginPassword,jdbcType=VARCHAR},
      </if>
      <if test="row.position != null">
        position = #{row.position,jdbcType=VARCHAR},
      </if>
      <if test="row.salt != null">
        salt = #{row.salt,jdbcType=VARCHAR},
      </if>
      <if test="row.gender != null">
        gender = #{row.gender,jdbcType=TINYINT},
      </if>
      <if test="row.customerStatus != null">
        customer_status = #{row.customerStatus,jdbcType=TINYINT},
      </if>
      <if test="row.disableSource != null">
        disable_source = #{row.disableSource,jdbcType=TINYINT},
      </if>
      <if test="row.headIcon != null">
        head_icon = #{row.headIcon,jdbcType=VARCHAR},
      </if>
      <if test="row.email != null">
        email = #{row.email,jdbcType=VARCHAR},
      </if>
      <if test="row.companyId != null">
        company_id = #{row.companyId,jdbcType=INTEGER},
      </if>
      <if test="row.companyCode != null">
        company_code = #{row.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="row.providerCode != null">
        provider_code = #{row.providerCode,jdbcType=VARCHAR},
      </if>
      <if test="row.dataPermType != null">
        data_perm_type = #{row.dataPermType,jdbcType=TINYINT},
      </if>
      <if test="row.structId != null">
        struct_id = #{row.structId,jdbcType=INTEGER},
      </if>
      <if test="row.structName != null">
        struct_name = #{row.structName,jdbcType=VARCHAR},
      </if>
      <if test="row.budgetOver != null">
        budget_over = #{row.budgetOver,jdbcType=TINYINT},
      </if>
      <if test="row.registerTime != null">
        register_time = #{row.registerTime,jdbcType=VARCHAR},
      </if>
      <if test="row.joinTime != null">
        join_time = #{row.joinTime,jdbcType=VARCHAR},
      </if>
      <if test="row.lastLoginTime != null">
        last_login_time = #{row.lastLoginTime,jdbcType=VARCHAR},
      </if>
      <if test="row.whiteList != null">
        white_list = #{row.whiteList,jdbcType=TINYINT},
      </if>
      <if test="row.disccountProportion != null">
        disccount_proportion = #{row.disccountProportion,jdbcType=INTEGER},
      </if>
      <if test="row.driverId != null">
        driver_id = #{row.driverId,jdbcType=INTEGER},
      </if>
      <if test="row.createId != null">
        create_id = #{row.createId,jdbcType=INTEGER},
      </if>
      <if test="row.createName != null">
        create_name = #{row.createName,jdbcType=VARCHAR},
      </if>
      <if test="row.updateId != null">
        update_id = #{row.updateId,jdbcType=INTEGER},
      </if>
      <if test="row.updateName != null">
        update_name = #{row.updateName,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateDate != null">
        update_date = #{row.updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="row.bestSignAccount != null">
        best_sign_account = #{row.bestSignAccount,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update t_customer
    set customer_id = #{row.customerId,jdbcType=INTEGER},
      customer_code = #{row.customerCode,jdbcType=VARCHAR},
      customer_name = #{row.customerName,jdbcType=VARCHAR},
      pinyin_name = #{row.pinyinName,jdbcType=VARCHAR},
      english_name = #{row.englishName,jdbcType=VARCHAR},
      mobile = #{row.mobile,jdbcType=VARCHAR},
      customer_no = #{row.customerNo,jdbcType=VARCHAR},
      login_password = #{row.loginPassword,jdbcType=VARCHAR},
      position = #{row.position,jdbcType=VARCHAR},
      salt = #{row.salt,jdbcType=VARCHAR},
      gender = #{row.gender,jdbcType=TINYINT},
      customer_status = #{row.customerStatus,jdbcType=TINYINT},
      disable_source = #{row.disableSource,jdbcType=TINYINT},
      head_icon = #{row.headIcon,jdbcType=VARCHAR},
      email = #{row.email,jdbcType=VARCHAR},
      company_id = #{row.companyId,jdbcType=INTEGER},
      company_code = #{row.companyCode,jdbcType=VARCHAR},
      provider_code = #{row.providerCode,jdbcType=VARCHAR},
      data_perm_type = #{row.dataPermType,jdbcType=TINYINT},
      struct_id = #{row.structId,jdbcType=INTEGER},
      struct_name = #{row.structName,jdbcType=VARCHAR},
      budget_over = #{row.budgetOver,jdbcType=TINYINT},
      register_time = #{row.registerTime,jdbcType=VARCHAR},
      join_time = #{row.joinTime,jdbcType=VARCHAR},
      last_login_time = #{row.lastLoginTime,jdbcType=VARCHAR},
      white_list = #{row.whiteList,jdbcType=TINYINT},
      disccount_proportion = #{row.disccountProportion,jdbcType=INTEGER},
      driver_id = #{row.driverId,jdbcType=INTEGER},
      create_id = #{row.createId,jdbcType=INTEGER},
      create_name = #{row.createName,jdbcType=VARCHAR},
      update_id = #{row.updateId,jdbcType=INTEGER},
      update_name = #{row.updateName,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_date = #{row.updateDate,jdbcType=TIMESTAMP},
      best_sign_account = #{row.bestSignAccount,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.user.entity.Customer">
    update t_customer
    <set>
      <if test="customerCode != null">
        customer_code = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null">
        customer_name = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="pinyinName != null">
        pinyin_name = #{pinyinName,jdbcType=VARCHAR},
      </if>
      <if test="englishName != null">
        english_name = #{englishName,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="customerNo != null">
        customer_no = #{customerNo,jdbcType=VARCHAR},
      </if>
      <if test="loginPassword != null">
        login_password = #{loginPassword,jdbcType=VARCHAR},
      </if>
      <if test="position != null">
        position = #{position,jdbcType=VARCHAR},
      </if>
      <if test="salt != null">
        salt = #{salt,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        gender = #{gender,jdbcType=TINYINT},
      </if>
      <if test="customerStatus != null">
        customer_status = #{customerStatus,jdbcType=TINYINT},
      </if>
      <if test="disableSource != null">
        disable_source = #{disableSource,jdbcType=TINYINT},
      </if>
      <if test="headIcon != null">
        head_icon = #{headIcon,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="providerCode != null">
        provider_code = #{providerCode,jdbcType=VARCHAR},
      </if>
      <if test="dataPermType != null">
        data_perm_type = #{dataPermType,jdbcType=TINYINT},
      </if>
      <if test="structId != null">
        struct_id = #{structId,jdbcType=INTEGER},
      </if>
      <if test="structName != null">
        struct_name = #{structName,jdbcType=VARCHAR},
      </if>
      <if test="budgetOver != null">
        budget_over = #{budgetOver,jdbcType=TINYINT},
      </if>
      <if test="registerTime != null">
        register_time = #{registerTime,jdbcType=VARCHAR},
      </if>
      <if test="joinTime != null">
        join_time = #{joinTime,jdbcType=VARCHAR},
      </if>
      <if test="lastLoginTime != null">
        last_login_time = #{lastLoginTime,jdbcType=VARCHAR},
      </if>
      <if test="whiteList != null">
        white_list = #{whiteList,jdbcType=TINYINT},
      </if>
      <if test="disccountProportion != null">
        disccount_proportion = #{disccountProportion,jdbcType=INTEGER},
      </if>
      <if test="driverId != null">
        driver_id = #{driverId,jdbcType=INTEGER},
      </if>
      <if test="createId != null">
        create_id = #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="updateId != null">
        update_id = #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDate != null">
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
      <if test="bestSignAccount != null">
        best_sign_account = #{bestSignAccount,jdbcType=VARCHAR},
      </if>
    </set>
    where customer_id = #{customerId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.user.entity.Customer">
    update t_customer
    set customer_code = #{customerCode,jdbcType=VARCHAR},
      customer_name = #{customerName,jdbcType=VARCHAR},
      pinyin_name = #{pinyinName,jdbcType=VARCHAR},
      english_name = #{englishName,jdbcType=VARCHAR},
      mobile = #{mobile,jdbcType=VARCHAR},
      customer_no = #{customerNo,jdbcType=VARCHAR},
      login_password = #{loginPassword,jdbcType=VARCHAR},
      position = #{position,jdbcType=VARCHAR},
      salt = #{salt,jdbcType=VARCHAR},
      gender = #{gender,jdbcType=TINYINT},
      customer_status = #{customerStatus,jdbcType=TINYINT},
      disable_source = #{disableSource,jdbcType=TINYINT},
      head_icon = #{headIcon,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=INTEGER},
      company_code = #{companyCode,jdbcType=VARCHAR},
      provider_code = #{providerCode,jdbcType=VARCHAR},
      data_perm_type = #{dataPermType,jdbcType=TINYINT},
      struct_id = #{structId,jdbcType=INTEGER},
      struct_name = #{structName,jdbcType=VARCHAR},
      budget_over = #{budgetOver,jdbcType=TINYINT},
      register_time = #{registerTime,jdbcType=VARCHAR},
      join_time = #{joinTime,jdbcType=VARCHAR},
      last_login_time = #{lastLoginTime,jdbcType=VARCHAR},
      white_list = #{whiteList,jdbcType=TINYINT},
      disccount_proportion = #{disccountProportion,jdbcType=INTEGER},
      driver_id = #{driverId,jdbcType=INTEGER},
      create_id = #{createId,jdbcType=INTEGER},
      create_name = #{createName,jdbcType=VARCHAR},
      update_id = #{updateId,jdbcType=INTEGER},
      update_name = #{updateName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_date = #{updateDate,jdbcType=TIMESTAMP},
      best_sign_account = #{bestSignAccount,jdbcType=VARCHAR}
    where customer_id = #{customerId,jdbcType=INTEGER}
  </update>
</mapper>