<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.WeChatAuthorizationInfoMapper">
  <resultMap id="BaseResultMap" type="com.izu.user.entity.WeChatAuthorizationInfo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="openid" jdbcType="VARCHAR" property="openid" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="nickname" jdbcType="VARCHAR" property="nickname" />
    <result column="sex" jdbcType="TINYINT" property="sex" />
    <result column="headimgurl" jdbcType="VARCHAR" property="headimgurl" />
    <result column="unionid" jdbcType="VARCHAR" property="unionid" />
    <result column="bind_status" jdbcType="TINYINT" property="bindStatus" />
    <result column="unbind_operator_ip" jdbcType="VARCHAR" property="unbindOperatorIp" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, openid, mobile, nickname, sex, headimgurl, unionid, bind_status, unbind_operator_ip, 
    create_date, update_date
  </sql>
  <select id="selectByExample" parameterType="com.izu.user.entity.WeChatAuthorizationInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_wechat_authorization_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_wechat_authorization_info
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from t_wechat_authorization_info
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.user.entity.WeChatAuthorizationInfoExample">
    delete from t_wechat_authorization_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.user.entity.WeChatAuthorizationInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into t_wechat_authorization_info (openid, mobile, nickname, 
      sex, headimgurl, unionid, 
      bind_status, unbind_operator_ip, create_date, 
      update_date)
    values (#{openid,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, #{nickname,jdbcType=VARCHAR}, 
      #{sex,jdbcType=TINYINT}, #{headimgurl,jdbcType=VARCHAR}, #{unionid,jdbcType=VARCHAR}, 
      #{bindStatus,jdbcType=TINYINT}, #{unbindOperatorIp,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP}, 
      #{updateDate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.user.entity.WeChatAuthorizationInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into t_wechat_authorization_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="openid != null">
        openid,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="nickname != null">
        nickname,
      </if>
      <if test="sex != null">
        sex,
      </if>
      <if test="headimgurl != null">
        headimgurl,
      </if>
      <if test="unionid != null">
        unionid,
      </if>
      <if test="bindStatus != null">
        bind_status,
      </if>
      <if test="unbindOperatorIp != null">
        unbind_operator_ip,
      </if>
      <if test="createDate != null">
        create_date,
      </if>
      <if test="updateDate != null">
        update_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="openid != null">
        #{openid,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="nickname != null">
        #{nickname,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        #{sex,jdbcType=TINYINT},
      </if>
      <if test="headimgurl != null">
        #{headimgurl,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="bindStatus != null">
        #{bindStatus,jdbcType=TINYINT},
      </if>
      <if test="unbindOperatorIp != null">
        #{unbindOperatorIp,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDate != null">
        #{updateDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.user.entity.WeChatAuthorizationInfoExample" resultType="java.lang.Long">
    select count(*) from t_wechat_authorization_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update t_wechat_authorization_info
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=INTEGER},
      </if>
      <if test="row.openid != null">
        openid = #{row.openid,jdbcType=VARCHAR},
      </if>
      <if test="row.mobile != null">
        mobile = #{row.mobile,jdbcType=VARCHAR},
      </if>
      <if test="row.nickname != null">
        nickname = #{row.nickname,jdbcType=VARCHAR},
      </if>
      <if test="row.sex != null">
        sex = #{row.sex,jdbcType=TINYINT},
      </if>
      <if test="row.headimgurl != null">
        headimgurl = #{row.headimgurl,jdbcType=VARCHAR},
      </if>
      <if test="row.unionid != null">
        unionid = #{row.unionid,jdbcType=VARCHAR},
      </if>
      <if test="row.bindStatus != null">
        bind_status = #{row.bindStatus,jdbcType=TINYINT},
      </if>
      <if test="row.unbindOperatorIp != null">
        unbind_operator_ip = #{row.unbindOperatorIp,jdbcType=VARCHAR},
      </if>
      <if test="row.createDate != null">
        create_date = #{row.createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateDate != null">
        update_date = #{row.updateDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update t_wechat_authorization_info
    set id = #{row.id,jdbcType=INTEGER},
      openid = #{row.openid,jdbcType=VARCHAR},
      mobile = #{row.mobile,jdbcType=VARCHAR},
      nickname = #{row.nickname,jdbcType=VARCHAR},
      sex = #{row.sex,jdbcType=TINYINT},
      headimgurl = #{row.headimgurl,jdbcType=VARCHAR},
      unionid = #{row.unionid,jdbcType=VARCHAR},
      bind_status = #{row.bindStatus,jdbcType=TINYINT},
      unbind_operator_ip = #{row.unbindOperatorIp,jdbcType=VARCHAR},
      create_date = #{row.createDate,jdbcType=TIMESTAMP},
      update_date = #{row.updateDate,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.user.entity.WeChatAuthorizationInfo">
    update t_wechat_authorization_info
    <set>
      <if test="openid != null">
        openid = #{openid,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="nickname != null">
        nickname = #{nickname,jdbcType=VARCHAR},
      </if>
      <if test="sex != null">
        sex = #{sex,jdbcType=TINYINT},
      </if>
      <if test="headimgurl != null">
        headimgurl = #{headimgurl,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        unionid = #{unionid,jdbcType=VARCHAR},
      </if>
      <if test="bindStatus != null">
        bind_status = #{bindStatus,jdbcType=TINYINT},
      </if>
      <if test="unbindOperatorIp != null">
        unbind_operator_ip = #{unbindOperatorIp,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null">
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="updateDate != null">
        update_date = #{updateDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.user.entity.WeChatAuthorizationInfo">
    update t_wechat_authorization_info
    set openid = #{openid,jdbcType=VARCHAR},
      mobile = #{mobile,jdbcType=VARCHAR},
      nickname = #{nickname,jdbcType=VARCHAR},
      sex = #{sex,jdbcType=TINYINT},
      headimgurl = #{headimgurl,jdbcType=VARCHAR},
      unionid = #{unionid,jdbcType=VARCHAR},
      bind_status = #{bindStatus,jdbcType=TINYINT},
      unbind_operator_ip = #{unbindOperatorIp,jdbcType=VARCHAR},
      create_date = #{createDate,jdbcType=TIMESTAMP},
      update_date = #{updateDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>