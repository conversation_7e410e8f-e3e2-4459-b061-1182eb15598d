<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.SupplierSynchronizationConfigMapper">
  <resultMap id="BaseResultMap" type="com.izu.user.entity.SupplierSynchronizationConfig">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sync_type" jdbcType="VARCHAR" property="syncType" />
    <result column="account_enabled" jdbcType="BIT" property="accountEnabled" />
    <result column="pack_code" jdbcType="VARCHAR" property="packCode" />
    <result column="account_duration" jdbcType="INTEGER" property="accountDuration" />
    <result column="sync_disable_account" jdbcType="BIT" property="syncDisableAccount" />
    <result column="yn" jdbcType="BIT" property="yn" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, sync_type, account_enabled, pack_code, account_duration, sync_disable_account, 
    yn, create_time, update_time
  </sql>
  <select id="selectByExample" parameterType="com.izu.user.entity.SupplierSynchronizationConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from supplier_synchronization_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from supplier_synchronization_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from supplier_synchronization_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.user.entity.SupplierSynchronizationConfigExample">
    delete from supplier_synchronization_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.user.entity.SupplierSynchronizationConfig">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into supplier_synchronization_config (sync_type, account_enabled, pack_code, 
      account_duration, sync_disable_account, yn, 
      create_time, update_time)
    values (#{syncType,jdbcType=VARCHAR}, #{accountEnabled,jdbcType=BIT}, #{packCode,jdbcType=VARCHAR}, 
      #{accountDuration,jdbcType=INTEGER}, #{syncDisableAccount,jdbcType=BIT}, #{yn,jdbcType=BIT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.user.entity.SupplierSynchronizationConfig">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into supplier_synchronization_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="syncType != null">
        sync_type,
      </if>
      <if test="accountEnabled != null">
        account_enabled,
      </if>
      <if test="packCode != null">
        pack_code,
      </if>
      <if test="accountDuration != null">
        account_duration,
      </if>
      <if test="syncDisableAccount != null">
        sync_disable_account,
      </if>
      <if test="yn != null">
        yn,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="syncType != null">
        #{syncType,jdbcType=VARCHAR},
      </if>
      <if test="accountEnabled != null">
        #{accountEnabled,jdbcType=BIT},
      </if>
      <if test="packCode != null">
        #{packCode,jdbcType=VARCHAR},
      </if>
      <if test="accountDuration != null">
        #{accountDuration,jdbcType=INTEGER},
      </if>
      <if test="syncDisableAccount != null">
        #{syncDisableAccount,jdbcType=BIT},
      </if>
      <if test="yn != null">
        #{yn,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.user.entity.SupplierSynchronizationConfigExample" resultType="java.lang.Long">
    select count(*) from supplier_synchronization_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update supplier_synchronization_config
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.syncType != null">
        sync_type = #{row.syncType,jdbcType=VARCHAR},
      </if>
      <if test="row.accountEnabled != null">
        account_enabled = #{row.accountEnabled,jdbcType=BIT},
      </if>
      <if test="row.packCode != null">
        pack_code = #{row.packCode,jdbcType=VARCHAR},
      </if>
      <if test="row.accountDuration != null">
        account_duration = #{row.accountDuration,jdbcType=INTEGER},
      </if>
      <if test="row.syncDisableAccount != null">
        sync_disable_account = #{row.syncDisableAccount,jdbcType=BIT},
      </if>
      <if test="row.yn != null">
        yn = #{row.yn,jdbcType=BIT},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update supplier_synchronization_config
    set id = #{row.id,jdbcType=BIGINT},
      sync_type = #{row.syncType,jdbcType=VARCHAR},
      account_enabled = #{row.accountEnabled,jdbcType=BIT},
      pack_code = #{row.packCode,jdbcType=VARCHAR},
      account_duration = #{row.accountDuration,jdbcType=INTEGER},
      sync_disable_account = #{row.syncDisableAccount,jdbcType=BIT},
      yn = #{row.yn,jdbcType=BIT},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.user.entity.SupplierSynchronizationConfig">
    update supplier_synchronization_config
    <set>
      <if test="syncType != null">
        sync_type = #{syncType,jdbcType=VARCHAR},
      </if>
      <if test="accountEnabled != null">
        account_enabled = #{accountEnabled,jdbcType=BIT},
      </if>
      <if test="packCode != null">
        pack_code = #{packCode,jdbcType=VARCHAR},
      </if>
      <if test="accountDuration != null">
        account_duration = #{accountDuration,jdbcType=INTEGER},
      </if>
      <if test="syncDisableAccount != null">
        sync_disable_account = #{syncDisableAccount,jdbcType=BIT},
      </if>
      <if test="yn != null">
        yn = #{yn,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.user.entity.SupplierSynchronizationConfig">
    update supplier_synchronization_config
    set sync_type = #{syncType,jdbcType=VARCHAR},
      account_enabled = #{accountEnabled,jdbcType=BIT},
      pack_code = #{packCode,jdbcType=VARCHAR},
      account_duration = #{accountDuration,jdbcType=INTEGER},
      sync_disable_account = #{syncDisableAccount,jdbcType=BIT},
      yn = #{yn,jdbcType=BIT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>