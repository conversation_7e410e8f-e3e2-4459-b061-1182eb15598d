<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.ex.MaintainGarageCompanyRelationExMapper">
  <resultMap id="queryResultMap" type="com.izu.user.dto.maintain.GarageRelationDTO">
    <id column="relation_id" jdbcType="INTEGER" property="relationId" />
    <result column="garage_no" jdbcType="VARCHAR" property="garageNo" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="relation_company_id" jdbcType="INTEGER" property="relationCompanyId" />
    <result column="relation_company_code" jdbcType="VARCHAR" property="relationCompanyCode" />
    <result column="relation_company_name" jdbcType="VARCHAR" property="relationCompanyName" />
    <result column="relation_type" jdbcType="TINYINT" property="relationType" />
    <result column="relation_status" jdbcType="TINYINT" property="relationStatus" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_id" jdbcType="INTEGER" property="updateId" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
  </resultMap>
  <sql id="query_Column_List">
    relation_id, garage_no, company_code, relation_company_id, relation_company_code,
    relation_company_name, relation_type, relation_status,
    update_time, update_id, update_name
  </sql>
  <select id="selectRelationGarageNos" resultType="java.lang.String">
    select garage_no
    from maintain_garage_company_relation
    <where>
      relation_company_id = #{relationCompanyId}
      <if test="relationStatus != null">
        and relation_status = #{relationStatus}
      </if>
    </where>
  </select>
  <select id="selectRelationGarageByCondition" resultMap="mapper.MaintainGarageCompanyRelationMapper.BaseResultMap">
    select <include refid="mapper.MaintainGarageCompanyRelationMapper.Base_Column_List" />
    from maintain_garage_company_relation
    where relation_company_code=#{relationCompanyCode}
          and garage_no=#{garageNo} and relation_status=1 limit 1
  </select>

  <select id="getGarageRelationList" resultMap="mapper.MaintainGarageCompanyRelationMapper.BaseResultMap" parameterType="com.izu.user.dto.maintain.GarageRelationReqDTO">
    select <include refid="mapper.MaintainGarageCompanyRelationMapper.Base_Column_List" />
    from maintain_garage_company_relation
    <where>
      <if test="garageName !=null and garageName!=''">
        and garage_name like concat(#{garageName},'%')
      </if>
      <if test="garageNameItem !=null and garageNameItem!=''">
        and garage_name =#{garageNameItem}
      </if>
      <if test="relationCompanyName !=null and relationCompanyName!=''">
        and relation_company_name  like concat(#{relationCompanyName},'%')
      </if>
      <if test="relationCompanyCode !=null and relationCompanyCode!=''">
        and relation_company_code  = #{relationCompanyCode}
      </if>
      <if test="relationType != null">
        and relation_type = #{relationType,jdbcType=TINYINT}
      </if>
      <if test="relationTypeList!=null and relationTypeList.size()>0">
        and relation_type in
        <foreach collection="relationTypeList" open="(" separator="," close=")" item="item">
          #{item}
        </foreach>
      </if>
      <if test="garageNo !=null and garageNo!=''">
        and garage_no=#{garageNo}
      </if>
      <if test="relationStatus!=null">
        and relation_status=#{relationStatus}
      </if>
    </where>
  </select>
  <insert id="insertBatchGarageRelation">
    insert into maintain_garage_company_relation
    (garage_no, garage_name,relation_company_id,
    relation_company_code, relation_company_name,
    relation_type, create_time, create_id,
    create_name, update_time, update_id, update_name)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.garageNo},#{item.garageName},#{item.relationCompanyId},#{item.relationCompanyCode},#{item.relationCompanyName},#{item.relationType},
      #{item.createTime},#{item.createId},#{item.createName},#{item.updateTime},
      #{item.updateId},#{item.updateName})
    </foreach>
  </insert>

  <select id="existRelationGarage" resultMap="mapper.MaintainGarageCompanyRelationMapper.BaseResultMap">
    select <include refid="mapper.MaintainGarageCompanyRelationMapper.Base_Column_List" />
    from maintain_garage_company_relation where garage_no=#{garageNo} and relation_status=1 limit 1
  </select>
  <select id="selectRelationGarageList" resultMap="mapper.MaintainGarageCompanyRelationMapper.BaseResultMap">
    select <include refid="mapper.MaintainGarageCompanyRelationMapper.Base_Column_List" />
    from maintain_garage_company_relation
    where garage_no=#{garageNo}
    <if test="relationStatus != null">
      and relation_status = #{relationStatus}
    </if>
  </select>
  <select id="getBindGarageCompanyCode" resultType="String">
    select DISTINCT relation_company_code from maintain_garage_company_relation;
  </select>

  <select id="getGarageRelationCompanyList" resultMap="queryResultMap" parameterType="com.izu.user.dto.maintain.GarageRelationReqDTO">
    select <include refid="query_Column_List" />
    from maintain_garage_company_relation
    <where>
      <if test="garageNo !=null and garageNo!=''">
        and garage_no=#{garageNo}
      </if>
      <if test="relationCompanyName !=null and relationCompanyName!=''">
        and relation_company_name  like concat(#{relationCompanyName},'%')
      </if>
      <if test="garageName !=null and garageName!=''">
        and garage_name  like concat(#{garageName},'%')
      </if>
      <if test="relationCompanyCode !=null and relationCompanyCode!=''">
        and relation_company_code  = #{relationCompanyCode}
      </if>
      <if test="relationType != null">
        and relation_type = #{relationType,jdbcType=TINYINT}
      </if>
      <if test="garageCompanyCodes!=null and garageCompanyCodes.size()>0">
        and relation_company_code in
         <foreach collection="garageCompanyCodes" open="(" separator="," close=")" item="item">
           #{item}
         </foreach>
      </if>
      <if test="relationTypeList!=null and relationTypeList.size()>0">
        and relation_type in
         <foreach collection="relationTypeList" open="(" separator="," close=")" item="item">
           #{item}
         </foreach>
      </if>
      <if test="relationStatus!=null">
        and relation_status=#{relationStatus}
      </if>
      <if test="garageNos !=null and garageNos.size()>0">
        and garage_no in
        <foreach collection="garageNos" open="(" separator="," close=")" item="garageNoItem">
          #{garageNoItem}
        </foreach>
      </if>
      <if test="moreCompanyCodes !=null and moreCompanyCodes.size()>0">
        and garage_no in
        <foreach collection="moreCompanyCodes" open="(" separator="," close=")" item="moreCompanyCodeItem">
          #{moreCompanyCodeItem}
        </foreach>
      </if>
      <if test="dataCodeSet !=null and dataCodeSet.size()>0">
        and relation_company_code in
        <foreach collection="dataCodeSet" item="dataCodeItem" open="(" separator="," close=")" >
          #{dataCodeItem}
        </foreach>
      </if>
    </where>
    order by update_time desc
  </select>
  <select id="getGarageRelationCompanyListBySupplier" resultType="com.izu.user.dto.maintain.GarageRelationDTO" parameterType="com.izu.user.dto.maintain.GarageRelationReqDTO">
    select mgcr.relation_id as relationId, mgcr.garage_no as garageNo, mgcr.company_code as companyCode,
           mgcr.relation_company_id as relationCompanyId, mgcr.relation_company_code as relationCompanyCode,
    mgcr.relation_company_name as relationCompanyName , mgcr.relation_type as relationType , mgcr.relation_status as relationStatus,
    mgcr.update_time as updateTime, mgcr.update_id as updateId, mgcr.update_name as updateName,
    mg.garage_name as garageName, mg.garage_status as garageStatus, mg.garage_contactor_name as garageContactorName,
    mg.garage_contactor_phone as garageContactorPhone, mg.contract_begin_date as contractBeginDate,
    mg.contract_end_date as contractEndDate,mg.supplier_type as supplierType
    from maintain_garage_company_relation mgcr
    left join maintain_garage mg on mg.garage_no=mgcr.garage_no
    <where>
      <if test="garageNo !=null and garageNo!=''">
        and mgcr.garage_no=#{garageNo}
      </if>
      <if test="relationCompanyName !=null and relationCompanyName!=''">
        and mgcr.relation_company_name  like concat(#{relationCompanyName},'%')
      </if>
      <if test="garageName !=null and garageName!=''">
        and mgcr.garage_name  like concat(#{garageName},'%')
      </if>
      <if test="relationCompanyCode !=null and relationCompanyCode!=''">
        and mgcr.relation_company_code  = #{relationCompanyCode}
      </if>
      <if test="relationType != null">
        and mgcr.relation_type = #{relationType,jdbcType=TINYINT}
      </if>
      <if test="garageCompanyCodes!=null and garageCompanyCodes.size()>0">
        and mgcr.relation_company_code in
         <foreach collection="garageCompanyCodes" open="(" separator="," close=")" item="item">
           #{item}
         </foreach>
      </if>
      <if test="relationTypeList!=null and relationTypeList.size()>0">
        and mgcr.relation_type in
         <foreach collection="relationTypeList" open="(" separator="," close=")" item="item">
           #{item}
         </foreach>
      </if>
      <if test="relationStatus!=null">
        and mgcr.relation_status=#{relationStatus}
      </if>
      <if test="garageNos !=null and garageNos.size()>0">
        and mgcr.garage_no in
        <foreach collection="garageNos" open="(" separator="," close=")" item="garageNoItem">
          #{garageNoItem}
        </foreach>
      </if>
      <if test="moreCompanyCodes !=null and moreCompanyCodes.size()>0">
        and mgcr.garage_no in
        <foreach collection="moreCompanyCodes" open="(" separator="," close=")" item="moreCompanyCodeItem">
          #{moreCompanyCodeItem}
        </foreach>
      </if>
      <if test="dataCodeSet !=null and dataCodeSet.size()>0">
        and mgcr.relation_company_code in
        <foreach collection="dataCodeSet" item="dataCodeItem" open="(" separator="," close=")" >
          #{dataCodeItem}
        </foreach>
      </if>
      <if test="supplierType!=null">
        and mg.supplier_type=#{supplierType}
      </if>
    </where>
    order by mgcr.update_time desc
  </select>

  <select id="getRelationGarageList" resultType="String">
    select DISTINCT garage_no from maintain_garage_company_relation where relation_company_code = #{relationCompanyCode} and relation_status=1;
  </select>
  <select id="searchRelation" resultMap="mapper.MaintainGarageCompanyRelationMapper.BaseResultMap" parameterType="com.izu.user.dto.maintain.MaintainGarageSearchReqDTO">
    select <include refid="query_Column_List" />
    from maintain_garage_company_relation
    <where>
      <if test="garageNo !=null and garageNo!=''">
        and garage_no=#{garageNo} and relation_status=1
      </if>
      <if test="companyCode !=null and companyCode!=''">
        and relation_company_code  = #{companyCode}
      </if>
    </where>
  </select>
  <select id="getRemoveRelation" parameterType="com.izu.user.dto.maintain.MaintainGarageBatchRemoveReqDTO" resultType="java.lang.Integer">
    select mgcr.relation_id
    from maintain_garage_company_relation mgcr
    left join maintain_garage mg on mgcr.garage_no=mg.garage_no
    <where>
      <if test="companyCode !=null and companyCode!=''">
        and mgcr.relation_company_code = #{companyCode} and mgcr.relation_status=1
      </if>
      <if test="garageCityCode !=null and garageCityCode!=''">
        and mg.garage_city_code  = #{garageCityCode}
      </if>
      <if test="supplierTypeList!=null and supplierTypeList.size()>0">
        and mg.supplier_type in
        <foreach collection="supplierTypeList" open="(" separator="," close=")" item="item">
          #{item}
        </foreach>
      </if>
    </where>
  </select>
</mapper>
