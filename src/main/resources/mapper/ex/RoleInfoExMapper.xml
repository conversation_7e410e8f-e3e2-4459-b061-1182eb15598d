<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.ex.RoleInfoExMapper">
    <select id="listRoleByStaffCode" resultMap="mapper.RoleInfoMapper.BaseResultMap">
        select role.role_id,
               role.role_code,
               role.role_name,
               role.role_explain,
               role.valid,
               role.role_type,
               role.system_type,
               role.role_mark,
               role.company_code,
               role.company_name,
               role.provider_code,
               role.provider_name,
               role.create_source
        from t_role_info as role
                 inner join t_staff_role_relation tsrr on role.role_code = tsrr.role_code and role.valid = 1
        where tsrr.staff_code = #{staffCode};
    </select>

    <select id="listRoleByParamsForProvider" resultMap="mapper.RoleInfoMapper.BaseResultMap">
        select
        <include refid="mapper.RoleInfoMapper.Base_Column_List"/>
        from t_role_info
        <where>
            <if test="roleName != null and roleName!=''">
                and role_name like concat('%', #{roleName}, '%')
            </if>
            <if test="customerCompanyName != null and customerCompanyName!=''">
                and company_name like concat(#{customerCompanyName}, '%')
            </if>
            <if test="companyCode != null and companyCode!=''">
                and company_code = #{companyCode}
            </if>
            <if test="roleType != null">
                and role_type = #{roleType}
            </if>
            <if test="systemType != null">
                and system_type = #{systemType}
            </if>
            <if test="valid != null">
                and valid = #{valid}
            </if>
            <if test="roleCodeList != null and roleCodeList.size() != 0">
                and role_code in
                <foreach collection="roleCodeList" item="code" separator="," open="(" close=")">
                    #{code}
                </foreach>
            </if>
        </where>
        order by update_time desc
    </select>

    <select id="queryLikePrams" resultMap="mapper.RoleInfoMapper.BaseResultMap">
        select
        <include refid="mapper.RoleInfoMapper.Base_Column_List"/>
        from t_role_info
        <where>
            <if test="roleName != null and roleName!=''">
                and role_name like concat(#{roleName}, '%')
            </if>
        </where>
        order by role_type,update_time desc
    </select>

    <select id="queryLikePramsForUser" resultMap="mapper.RoleInfoMapper.BaseResultMap">
        select
        <include refid="mapper.RoleInfoMapper.Base_Column_List"/>
        from t_role_info
        <where>
            system_type = 2
            <if test="roleName != null and roleName!=''">
                and role_name like concat(#{roleName}, '%')
            </if>
            <if test="companyCode !=null and companyCode !='' ">
                and company_code = #{companyCode}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="queryLikePramsForCompany" resultMap="mapper.RoleInfoMapper.BaseResultMap">
        select
        <include refid="mapper.RoleInfoMapper.Base_Column_List"/>
        from t_role_info
        <where>
            (company_code = #{companyCode} or (system_type = 2 and role_type = 1))
            <if test="roleName != null and roleName!=''">
                and role_name like concat(#{roleName}, '%')
            </if>
        </where>
        order by role_type,update_time desc
    </select>

    <select id="listRoleByParamsForCompany" resultMap="mapper.RoleInfoMapper.BaseResultMap"
            parameterType="com.izu.user.dto.roleinfo.RoleInfoReqDTO">
        select
        <include refid="mapper.RoleInfoMapper.Base_Column_List"/>
        from t_role_info
        <where>
            <if test="roleName != null and roleName!=''">
                and role_name like concat(#{roleName}, '%')
            </if>
            <if test="roleType != null">
                and role_type = #{roleType}
            </if>
            <if test="valid != null">
                and valid = #{valid}
            </if>
            <if test="roleCodeList != null and roleCodeList.size() != 0">
                and role_code in
                <foreach collection="roleCodeList" item="code" separator="," open="(" close=")">
                    #{code}
                </foreach>
            </if>

            <if test="loginSystemType !=null and loginSystemType==2">
                <choose>
                    <when test="dataPermType == 1">
                        and (company_code in
                            <foreach collection="dataCodeSet" item="dataCodeItem" separator="," open="(" close=")">#{dataCodeItem}
                            </foreach>
                            or (system_type = 2 and role_type = 1 and valid=1) )
                    </when>
                    <when test="dataPermType == 2 or dataPermType == 3 or dataPermType == 4">
                        and create_id = #{loginUserId}
                        and (company_code =#{loginCompanyCode} or (system_type = 2 and role_type = 1 and valid=1))
                    </when>
                    <otherwise>
                        and company_code = -1
                    </otherwise>
                </choose>
            </if>
        </where>
        order by update_time desc
    </select>

    <select id="getRoleInfoByParams" resultMap="mapper.RoleInfoMapper.BaseResultMap">
        select
        <include refid="mapper.RoleInfoMapper.Base_Column_List"/>
        from t_role_info
        <where>
            valid = 1
            <if test="name != null and name!=''">
                and role_name = #{name}
            </if>
            <if test="systemType != null">
                and system_type = #{systemType}
            </if>
            <if test="roleType != null">
                and role_type = #{roleType}
            </if>
            <if test="companyCode != null and companyCode!=''">
                and company_code = #{companyCode}
            </if>
        </where>
    </select>

    <select id="getRoleInfoByCode" resultMap="mapper.RoleInfoMapper.BaseResultMap">
        select
        <include refid="mapper.RoleInfoMapper.Base_Column_List"/>
        from t_role_info
        where role_code = #{roleCode}
    </select>

    <select id="getRolePermCounter" resultType="com.izu.user.dto.roleinfo.RolePermCounterDTO">
        select r.role_code as roleCode, count(*) as permNums
        from t_role_perm_relation as r
        inner join manager_permission as m on r.perm_sys_code = m.perm_sys_code
        where m.valid = 1
        and r.role_code in
        <foreach collection="roleCodeList" item="roleCode" close=")" open="(" separator=",">
            #{roleCode}
        </foreach>
        group by r.role_code
    </select>
    
</mapper>