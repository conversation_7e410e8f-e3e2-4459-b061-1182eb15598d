<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.ex.DriverAllocationDetailExMapper">
    <resultMap id="BaseResultMap" type="com.izu.user.entity.DriverAllocationDetail" extends="mapper.DriverAllocationDetailMapper.BaseResultMap">
    </resultMap>


    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_driver_allocation_detail (allocation_id, driver_id, driver_mobile,
        office_status, working_status, labor_company_name,
        labor_company_id, belong_struct_code, belong_struct_name,
        source_company_id, source_company_code, source_company_name,
        source_department_name, source_department_id)
        VALUES
        <foreach collection="list" item="allocateDetail" separator=",">
            (#{allocateDetail.allocationId,jdbcType=INTEGER}, #{allocateDetail.driverId,jdbcType=INTEGER}, #{allocateDetail.driverMobile,jdbcType=VARCHAR},
            #{allocateDetail.officeStatus,jdbcType=TINYINT}, #{allocateDetail.workingStatus,jdbcType=TINYINT}, #{allocateDetail.laborCompanyName,jdbcType=VARCHAR},
            #{allocateDetail.laborCompanyId,jdbcType=INTEGER}, #{allocateDetail.belongStructCode,jdbcType=VARCHAR}, #{allocateDetail.belongStructName,jdbcType=VARCHAR},
            #{allocateDetail.sourceCompanyId,jdbcType=INTEGER}, #{allocateDetail.sourceCompanyCode,jdbcType=VARCHAR}, #{allocateDetail.sourceCompanyName,jdbcType=VARCHAR},
            #{allocateDetail.sourceDepartmentName,jdbcType=VARCHAR}, #{allocateDetail.sourceDepartmentId,jdbcType=INTEGER})
        </foreach>
    </insert>
</mapper>