<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.ex.DriverAllocationRecordExMapper">
    <resultMap id="queryResultMap" type="com.izu.user.entity.DriverAllocationRecord" extends="mapper.DriverAllocationRecordMapper.BaseResultMap">

    </resultMap>

    <select id="getAllocationList" parameterType="com.izu.user.dto.driver.DriverAllocationRecordListReqDTO" resultMap="queryResultMap">
        select <include refid="mapper.DriverAllocationRecordMapper.Base_Column_List"></include>
            from t_driver_allocation_record
        <where>
            <if test="allocationNo != null and allocationNo != ''">
                and allocation_no = #{allocationNo}
            </if>
            <if test="allocationMethod != null and allocationMethod != ''">
                and allocation_method = #{allocationMethod}
            </if>
            <if test="targetCompanyId != null and targetCompanyId != ''">
                and target_company_id = #{targetCompanyId}
            </if>

            <if test="createdId!= null ">
                and created_id = #{createdId}
            </if>
            <if test="createdName!= null ">
                and created_name like concat( #{createdName},'%')
            </if>
            <if test="createdTimeStart!= null ">
                and created_time <![CDATA[ >= ]]> #{createdTimeStart}
            </if>
            <if test="createdTimeEnd!= null ">
                and created_time <![CDATA[ <= ]]> #{createdTimeEnd}
            </if>
            <if test="driverId!= null ">
                and allocation_method = #{allocationMethod}
            </if>
            <if test="allocationIds!= null and allocationIds.size()>0 ">
                and allocation_no in
                <foreach collection="allocationIds" item="allocationIdItem" open="(" separator="," close=")">
                    #{allocationIdItem}
                </foreach>
            </if>
            <include refid="dataPerm"></include>
        </where>
        order by created_time desc


    </select>

    <sql id="dataPerm">

        <if test="loginSystemType == 2">
          <choose>
              <when test="dataPermType == 1 and dataPermType == 2 and dataPermType == 3 and dataPermType == 7">
                  and created_id = #{loginUserId}
              </when>
              <when test="dataPermType == 6">

              </when>
              <when test="dataPermType == 5 and dataPermType == 4">
                  and (allocation_id in (select allocation_id from t_driver_allocation_detail where
                   <foreach collection="dataCodeSet" item="dataCodeSetItem" open="(" close=")" separator=",">
                       #{dataCodeSetItem}
                   </foreach>
                  ) or created_id = #{loginUserId})
              </when>
              <otherwise>
                  and allocation_id=-1
              </otherwise>
          </choose>

        </if>
    </sql>

    <select id="allocationDetail" resultMap="queryResultMap">
        select <include refid="mapper.DriverAllocationRecordMapper.Base_Column_List"></include>
            from t_driver_allocation_record
            where allocation_no = #{allocationNo}
    </select>
</mapper>