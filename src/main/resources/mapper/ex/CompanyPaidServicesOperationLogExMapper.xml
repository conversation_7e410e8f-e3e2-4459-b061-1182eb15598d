<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.ex.CompanyPaidServicesOperationLogExMapper">

  <select id="listOperationLog" resultMap="mapper.CompanyPaidServicesOperationLogMapper.BaseResultMap">
    select <include refid="mapper.CompanyPaidServicesOperationLogMapper.Base_Column_List"></include>
        from company_paid_services_operation_log
    where paid_service_code = #{paidServiceCode}
    order by operation_time desc
  </select>
</mapper>