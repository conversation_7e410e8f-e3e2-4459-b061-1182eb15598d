<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.ex.MotorcadeSchedulingDetailExMapper">
    <select id="selectListByParam" resultMap="mapper.MotorcadeSchedulingDetailMapper.BaseResultMap">
        select
        <include refid="mapper.MotorcadeSchedulingDetailMapper.Base_Column_List"/>
        from motorcade_scheduling_detail
        where company_id=#{companyId}
        and motorcade_id=#{motorcadeId}
        and DATE_FORMAT(date_time,"%Y-%m-01")=DATE_FORMAT(#{dateTime},"%Y-%m-01")
    </select>
    <select id="selectDriverListByParam" resultMap="mapper.MotorcadeSchedulingDetailMapper.BaseResultMap">
        select
        <include refid="mapper.MotorcadeSchedulingDetailMapper.Base_Column_List"/>
        from motorcade_scheduling_detail
        where company_id=#{companyId}
        and motorcade_id=#{motorcadeId}
        and date_time=#{dateTime}
        and driver_id=#{driverId}
    </select>
    <select id="getDetailForDriver" resultType="map">
        select start_time, end_time
        from motorcade_scheduling_detail
        where driver_id = #{driverId}
          and DATE_FORMAT(date_time, "%Y-%m-01") = DATE_FORMAT(#{dateTime}, "%Y-%m-01")
        limit 1
    </select>

    <delete id="clearSetting">
        delete
        from motorcade_scheduling_detail
        where company_id = #{companyId}
          and motorcade_id = #{motorcadeId}
          and driver_id = #{driverId}
          and date_time <![CDATA[>=]]> #{start}
          and date_time <![CDATA[<=]]> #{end}
    </delete>

    <insert id="batchInsert">
        insert into motorcade_scheduling_detail
        (company_id, motorcade_id, driver_id,
        date_time, scheduling_no,
        create_id, create_name,
        start_time, end_time)
        values
        <foreach collection="details" separator="," item="detail">
            (#{detail.companyId} , #{detail.motorcadeId} , #{detail.driverId} , #{detail.dateTime} ,
            #{detail.schedulingNo} , #{detail.createId} , #{detail.createName}, #{detail.startTime} , #{detail.endTime}
            )
        </foreach>
    </insert>

    <resultMap id="AppSchedulingDTO" type="com.izu.config.dto.AppSchedulingDTO">
        <result column="start_time" jdbcType="VARCHAR" property="start"/>
        <result column="end_time" jdbcType="VARCHAR" property="end"/>
        <result column="date_time" jdbcType="VARCHAR" property="date"/>
    </resultMap>

    <select id="getSchedulingList" resultMap="AppSchedulingDTO">
        select DATE_FORMAT(start_time, "%H:%i")   start_time,
               DATE_FORMAT(end_time, "%H:%i")     end_time,
               DATE_FORMAT(date_time, "%Y/%m/%d") date_time
        from motorcade_scheduling_detail
        where driver_id = #{driverId}
          and motorcade_id = #{motorcadeId}
          and DATE_FORMAT(date_time, "%Y-%m") = DATE_FORMAT(#{date}, "%Y-%m")
        order by date_time asc
    </select>


    <select id="queryWorkDriverIds" resultType="java.util.Map">
        select driver_id driverId
        from motorcade_scheduling_detail
        where company_id = #{companyId}
        <if test="motorcadeId!=null">
            and motorcade_id = #{motorcadeId}
        </if>
        and start_time <![CDATA[<=]]> #{startDate}
        and end_time <![CDATA[>]]> #{startDate}
        <if test="endDate != null">
            and end_time <![CDATA[>=]]> #{endDate}
        </if>

    </select>

    <select id="getTwoDaySchedulingInfo" resultMap="mapper.MotorcadeSchedulingDetailMapper.BaseResultMap">
        select
        <include refid="mapper.MotorcadeSchedulingDetailMapper.Base_Column_List"/>
        from motorcade_scheduling_detail
        where driver_id = #{driverId}
        and motorcade_id = #{motorcadeId}
        and (date_time = #{today} or date_time = #{yesterday})
        order by date_time
    </select>
    <select id="getOneDaySchedulingInfo" resultMap="mapper.MotorcadeSchedulingDetailMapper.BaseResultMap">
        select
        <include refid="mapper.MotorcadeSchedulingDetailMapper.Base_Column_List"/>
        from motorcade_scheduling_detail
        where 
        driver_id in 
        <foreach collection="driverIds" item="driverId" open="(" close=")" separator=",">
        	#{driverId}
        </foreach>
        <if test="motorcadeId != null">
        	and motorcade_id = #{motorcadeId}
        </if>
        and date_time = #{today}
    </select>

    <select id="selectCountDetail" resultType="java.lang.Integer">
        select count(1)
        from motorcade_scheduling_detail
        where motorcade_id = #{motorcadeId}
          and driver_id = #{driverId}
          and date_time <![CDATA[>=]]> #{start}
    </select>

    <select id="getSchedulingDriver" resultMap="mapper.MotorcadeSchedulingDetailMapper.BaseResultMap">
        select
        <include refid="mapper.MotorcadeSchedulingDetailMapper.Base_Column_List"/>
        from motorcade_scheduling_detail where date_time = #{date}
        <if test="drivers != null and drivers.size() != 0">
            and motorcade_id in
            <foreach collection="drivers" open="(" close=")" item="mid" separator=",">
                #{mid.motorcadeId}
            </foreach>
            and driver_id in
            <foreach collection="drivers" open="(" close=")" item="mid" separator=",">
                #{mid.driverId}
            </foreach>
        </if>
    </select>

    <select id="getShouldAttendCount" resultType="java.lang.Integer">
        select
        count(1)
        from motorcade_scheduling_detail
        where DATE_FORMAT(date_time,'%Y-%m') = #{ymStr}
        and driver_id = #{driverId,jdbcType=INTEGER} and motorcade_id =#{motorcadeId}
    </select>
    <select id="getAllDriverIdOfAttend" resultType="java.lang.Integer">
        select
        distinct t1.driver_id
        from motorcade_scheduling_detail t1
        left join motorcade t2 on t1.motorcade_id = t2.motorcade_id
        left join t_driver t3 on t3.driver_id = t1.driver_id
        where t2.motorcade_status=1 and DATE_FORMAT(t1.date_time,'%Y-%m') = #{ymStr}
        and t3.driver_type = 1
  </select>

</mapper>