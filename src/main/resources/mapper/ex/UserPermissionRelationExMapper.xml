<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.ex.UserPermissionRelationExMapper">
  <resultMap id="BaseResultMap" type="com.izu.user.entity.UserPermissionRelation"  extends="mapper.UserPermissionRelationMapper.BaseResultMap">
  </resultMap>
  <sql id="Base_Column_List">
    <include refid="mapper.UserPermissionRelationMapper.Base_Column_List" />
  </sql>
<select id="selectStaffPermissionByStaffId"  resultMap="BaseResultMap"  >

</select>
  <select id="selectStaffPermissionIdsByStaffId"  resultType="java.lang.Integer"  >
        SELECT DISTINCT permission_id FROM user_permission_relation WHERE staff_id=#{staffId}
  </select>

  <select id="selectCustomerByPermissionId"  resultType="com.izu.user.entity.Customer"  >
    SELECT
    customer_id customerId,customer_name customerName,mobile
    FROM user_permission_relation t1 LEFT JOIN t_customer t2 ON t1.staff_id=t2.customer_id
    WHERE permission_id IN
    <foreach collection="permissionIds" item="permissionId" separator="," open="(" close=")">
      #{permissionId}
    </foreach>
    AND t2.company_id=#{companyId} AND t2.customer_status=1 GROUP BY customer_id
  </select>

  <delete id="clearUserPermissionIn">
    delete from user_permission_relation
    <where>
      <if test="userIdSet != null and userIdSet.size() != 0">
        staff_id in
        <foreach collection="userIdSet" item="uid" separator="," open="(" close=")">
          #{uid,jdbcType=INTEGER}
        </foreach>
      </if>
      <if test="permissions != null and permissions.size() != 0">
        and permission_id in
        <foreach collection="permissions" item="pid" separator="," open="(" close=")">
          #{pid,jdbcType=INTEGER}
        </foreach>
      </if>
    </where>
  </delete>

  <insert id="saveUserPermissions">
    insert into user_permission_relation (staff_id, permission_id) values
    <foreach collection="userIdSet" item="uid" separator=",">
      <foreach collection="permissions" separator="," item="pid">
        (#{uid,jdbcType=INTEGER} , #{pid,jdbcType=INTEGER})
      </foreach>
    </foreach>
  </insert>

  <update id="updateByStaffId" >
    update user_permission_relation
    set staff_id = #{newAdministratorId,jdbcType=INTEGER}
    where staff_id = #{oldAdministratorId,jdbcType=INTEGER}
  </update>

  <select id="listPhoneByPermId" resultType="java.lang.String">
    select mobile from user_permission_relation as upr inner join t_customer as c on upr.staff_id=c.customer_id where permission_id=#{permId} and c.customer_status=1 and mobile is not null and mobile!=''
  </select>

  <select id="listAllPermission"  resultMap="BaseResultMap"  >
    select permission_id,staff_id from user_permission_relation
    where staff_id in
    (select customer_id from t_customer where company_id=#{companyId} and customer_status=1)
    order by staff_id
  </select>
</mapper>