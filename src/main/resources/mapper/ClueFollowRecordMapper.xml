<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.ClueFollowRecordMapper">
  <resultMap id="BaseResultMap" type="com.izu.business.entity.ClueFollowRecord">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="clue_code" jdbcType="VARCHAR" property="clueCode" />
    <result column="follow_id" jdbcType="INTEGER" property="followId" />
    <result column="follow_name" jdbcType="VARCHAR" property="followName" />
    <result column="follow_time" jdbcType="VARCHAR" property="followTime" />
    <result column="follow_content" jdbcType="VARCHAR" property="followContent" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="customer_status" jdbcType="TINYINT" property="customerStatus" />
    <result column="contacts_status" jdbcType="TINYINT" property="contactsStatus" />
  </resultMap>
  <sql id="Base_Column_List">
    id, clue_code, follow_id, follow_name, follow_time, follow_content, create_time, 
    update_time, customer_status, contacts_status
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from clue_follow_record
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from clue_follow_record
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.business.entity.ClueFollowRecord">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into clue_follow_record (clue_code, follow_id, follow_name, 
      follow_time, follow_content, create_time, 
      update_time, customer_status, contacts_status
      )
    values (#{clueCode,jdbcType=VARCHAR}, #{followId,jdbcType=INTEGER}, #{followName,jdbcType=VARCHAR}, 
      #{followTime,jdbcType=VARCHAR}, #{followContent,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{customerStatus,jdbcType=TINYINT}, #{contactsStatus,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.business.entity.ClueFollowRecord">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into clue_follow_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="clueCode != null">
        clue_code,
      </if>
      <if test="followId != null">
        follow_id,
      </if>
      <if test="followName != null">
        follow_name,
      </if>
      <if test="followTime != null">
        follow_time,
      </if>
      <if test="followContent != null">
        follow_content,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="customerStatus != null">
        customer_status,
      </if>
      <if test="contactsStatus != null">
        contacts_status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="clueCode != null">
        #{clueCode,jdbcType=VARCHAR},
      </if>
      <if test="followId != null">
        #{followId,jdbcType=INTEGER},
      </if>
      <if test="followName != null">
        #{followName,jdbcType=VARCHAR},
      </if>
      <if test="followTime != null">
        #{followTime,jdbcType=VARCHAR},
      </if>
      <if test="followContent != null">
        #{followContent,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customerStatus != null">
        #{customerStatus,jdbcType=TINYINT},
      </if>
      <if test="contactsStatus != null">
        #{contactsStatus,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.business.entity.ClueFollowRecord">
    update clue_follow_record
    <set>
      <if test="clueCode != null">
        clue_code = #{clueCode,jdbcType=VARCHAR},
      </if>
      <if test="followId != null">
        follow_id = #{followId,jdbcType=INTEGER},
      </if>
      <if test="followName != null">
        follow_name = #{followName,jdbcType=VARCHAR},
      </if>
      <if test="followTime != null">
        follow_time = #{followTime,jdbcType=VARCHAR},
      </if>
      <if test="followContent != null">
        follow_content = #{followContent,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customerStatus != null">
        customer_status = #{customerStatus,jdbcType=TINYINT},
      </if>
      <if test="contactsStatus != null">
        contacts_status = #{contactsStatus,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.business.entity.ClueFollowRecord">
    update clue_follow_record
    set clue_code = #{clueCode,jdbcType=VARCHAR},
      follow_id = #{followId,jdbcType=INTEGER},
      follow_name = #{followName,jdbcType=VARCHAR},
      follow_time = #{followTime,jdbcType=VARCHAR},
      follow_content = #{followContent,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      customer_status = #{customerStatus,jdbcType=TINYINT},
      contacts_status = #{contactsStatus,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>