<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.PushSmsMapper">
  <resultMap id="BaseResultMap" type="com.izu.user.entity.PushSms">
    <id column="push_sms_id" jdbcType="BIGINT" property="pushSmsId" />
    <result column="push_type" jdbcType="TINYINT" property="pushType" />
    <result column="receive_mobile" jdbcType="VARCHAR" property="receiveMobile" />
    <result column="send_content" jdbcType="VARCHAR" property="sendContent" />
    <result column="send_time" jdbcType="VARCHAR" property="sendTime" />
    <result column="send_status" jdbcType="TINYINT" property="sendStatus" />
    <result column="result_content" jdbcType="VARCHAR" property="resultContent" />
  </resultMap>
  <sql id="Base_Column_List">
    push_sms_id, push_type, receive_mobile, send_content, send_time, send_status, result_content
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_push_sms
    where push_sms_id = #{pushSmsId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_push_sms
    where push_sms_id = #{pushSmsId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.izu.user.entity.PushSms">
    <selectKey keyProperty="pushSmsId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into t_push_sms (push_type, receive_mobile, send_content, 
      send_time, send_status, result_content
      )
    values (#{pushType,jdbcType=TINYINT}, #{receiveMobile,jdbcType=VARCHAR}, #{sendContent,jdbcType=VARCHAR}, 
      #{sendTime,jdbcType=VARCHAR}, #{sendStatus,jdbcType=TINYINT}, #{resultContent,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.user.entity.PushSms">
    <selectKey keyProperty="pushSmsId" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into t_push_sms
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pushType != null">
        push_type,
      </if>
      <if test="receiveMobile != null">
        receive_mobile,
      </if>
      <if test="sendContent != null">
        send_content,
      </if>
      <if test="sendTime != null">
        send_time,
      </if>
      <if test="sendStatus != null">
        send_status,
      </if>
      <if test="resultContent != null">
        result_content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pushType != null">
        #{pushType,jdbcType=TINYINT},
      </if>
      <if test="receiveMobile != null">
        #{receiveMobile,jdbcType=VARCHAR},
      </if>
      <if test="sendContent != null">
        #{sendContent,jdbcType=VARCHAR},
      </if>
      <if test="sendTime != null">
        #{sendTime,jdbcType=VARCHAR},
      </if>
      <if test="sendStatus != null">
        #{sendStatus,jdbcType=TINYINT},
      </if>
      <if test="resultContent != null">
        #{resultContent,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.user.entity.PushSms">
    update t_push_sms
    <set>
      <if test="pushType != null">
        push_type = #{pushType,jdbcType=TINYINT},
      </if>
      <if test="receiveMobile != null">
        receive_mobile = #{receiveMobile,jdbcType=VARCHAR},
      </if>
      <if test="sendContent != null">
        send_content = #{sendContent,jdbcType=VARCHAR},
      </if>
      <if test="sendTime != null">
        send_time = #{sendTime,jdbcType=VARCHAR},
      </if>
      <if test="sendStatus != null">
        send_status = #{sendStatus,jdbcType=TINYINT},
      </if>
      <if test="resultContent != null">
        result_content = #{resultContent,jdbcType=VARCHAR},
      </if>
    </set>
    where push_sms_id = #{pushSmsId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.user.entity.PushSms">
    update t_push_sms
    set push_type = #{pushType,jdbcType=TINYINT},
      receive_mobile = #{receiveMobile,jdbcType=VARCHAR},
      send_content = #{sendContent,jdbcType=VARCHAR},
      send_time = #{sendTime,jdbcType=VARCHAR},
      send_status = #{sendStatus,jdbcType=TINYINT},
      result_content = #{resultContent,jdbcType=VARCHAR}
    where push_sms_id = #{pushSmsId,jdbcType=BIGINT}
  </update>
</mapper>