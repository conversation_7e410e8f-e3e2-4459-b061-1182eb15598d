<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.StatisticsCustomerTrendMapper">
  <resultMap id="BaseResultMap" type="com.izu.business.entity.StatisticsCustomerTrend">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="statistics_date" jdbcType="DATE" property="statisticsDate" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="customer_valid" jdbcType="INTEGER" property="customerValid" />
    <result column="customer_invalid" jdbcType="INTEGER" property="customerInvalid" />
    <result column="customer_create" jdbcType="INTEGER" property="customerCreate" />
  </resultMap>
  <sql id="Base_Column_List">
    id, statistics_date, create_time, update_time, customer_valid, customer_invalid, 
    customer_create
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from statistics_customer_trend
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from statistics_customer_trend
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.business.entity.StatisticsCustomerTrend">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into statistics_customer_trend (statistics_date, create_time, update_time, 
      customer_valid, customer_invalid, customer_create
      )
    values (#{statisticsDate,jdbcType=DATE}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{customerValid,jdbcType=INTEGER}, #{customerInvalid,jdbcType=INTEGER}, #{customerCreate,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.business.entity.StatisticsCustomerTrend">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into statistics_customer_trend
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="statisticsDate != null">
        statistics_date,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="customerValid != null">
        customer_valid,
      </if>
      <if test="customerInvalid != null">
        customer_invalid,
      </if>
      <if test="customerCreate != null">
        customer_create,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="statisticsDate != null">
        #{statisticsDate,jdbcType=DATE},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customerValid != null">
        #{customerValid,jdbcType=INTEGER},
      </if>
      <if test="customerInvalid != null">
        #{customerInvalid,jdbcType=INTEGER},
      </if>
      <if test="customerCreate != null">
        #{customerCreate,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.business.entity.StatisticsCustomerTrend">
    update statistics_customer_trend
    <set>
      <if test="statisticsDate != null">
        statistics_date = #{statisticsDate,jdbcType=DATE},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customerValid != null">
        customer_valid = #{customerValid,jdbcType=INTEGER},
      </if>
      <if test="customerInvalid != null">
        customer_invalid = #{customerInvalid,jdbcType=INTEGER},
      </if>
      <if test="customerCreate != null">
        customer_create = #{customerCreate,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.business.entity.StatisticsCustomerTrend">
    update statistics_customer_trend
    set statistics_date = #{statisticsDate,jdbcType=DATE},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      customer_valid = #{customerValid,jdbcType=INTEGER},
      customer_invalid = #{customerInvalid,jdbcType=INTEGER},
      customer_create = #{customerCreate,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>