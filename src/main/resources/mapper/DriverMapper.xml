<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.DriverMapper">
  <resultMap id="BaseResultMap" type="com.izu.user.entity.Driver">
    <id column="driver_id" jdbcType="INTEGER" property="driverId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="driver_name" jdbcType="VARCHAR" property="driverName" />
    <result column="pinyin_name" jdbcType="VARCHAR" property="pinyinName" />
    <result column="driver_email" jdbcType="VARCHAR" property="driverEmail" />
    <result column="driver_mobile" jdbcType="VARCHAR" property="driverMobile" />
    <result column="gender" jdbcType="TINYINT" property="gender" />
    <result column="head_icon" jdbcType="VARCHAR" property="headIcon" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="political_status" jdbcType="TINYINT" property="politicalStatus" />
    <result column="education_status" jdbcType="TINYINT" property="educationStatus" />
    <result column="license_type" jdbcType="VARCHAR" property="licenseType" />
    <result column="license_no" jdbcType="VARCHAR" property="licenseNo" />
    <result column="verify_status" jdbcType="INTEGER" property="verifyStatus" />
    <result column="cert_name" jdbcType="VARCHAR" property="certName" />
    <result column="cert_no" jdbcType="VARCHAR" property="certNo" />
    <result column="brith_date" jdbcType="VARCHAR" property="brithDate" />
    <result column="term_of_validity" jdbcType="DATE" property="termOfValidity" />
    <result column="front_img_url" jdbcType="VARCHAR" property="frontImgUrl" />
    <result column="back_img_url" jdbcType="VARCHAR" property="backImgUrl" />
    <result column="lease_driver_id" jdbcType="INTEGER" property="leaseDriverId" />
    <result column="driving_licence_img_url" jdbcType="VARCHAR" property="drivingLicenceImgUrl" />
    <result column="driver_archives_no" jdbcType="VARCHAR" property="driverArchivesNo" />
    <result column="issuing_organ" jdbcType="VARCHAR" property="issuingOrgan" />
    <result column="arrive_time" jdbcType="VARCHAR" property="arriveTime" />
    <result column="driver_status" jdbcType="INTEGER" property="driverStatus" />
    <result column="office_status" jdbcType="TINYINT" property="officeStatus" />
    <result column="working_status" jdbcType="TINYINT" property="workingStatus" />
    <result column="driver_type" jdbcType="INTEGER" property="driverType" />
    <result column="driver_source_type" jdbcType="TINYINT" property="driverSourceType" />
    <result column="supplier_driver_line" jdbcType="VARCHAR" property="supplierDriverLine" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="customer_company_code" jdbcType="VARCHAR" property="customerCompanyCode" />
    <result column="customer_company_name" jdbcType="VARCHAR" property="customerCompanyName" />
    <result column="contract_code" jdbcType="VARCHAR" property="contractCode" />
    <result column="struct_id" jdbcType="INTEGER" property="structId" />
    <result column="struct_name" jdbcType="VARCHAR" property="structName" />
    <result column="join_time" jdbcType="VARCHAR" property="joinTime" />
    <result column="delete_time" jdbcType="VARCHAR" property="deleteTime" />
    <result column="last_login_time" jdbcType="VARCHAR" property="lastLoginTime" />
    <result column="bind_vehicle_id" jdbcType="INTEGER" property="bindVehicleId" />
    <result column="belong_struct_code" jdbcType="VARCHAR" property="belongStructCode" />
    <result column="belong_struct_name" jdbcType="VARCHAR" property="belongStructName" />
    <result column="labor_company_name" jdbcType="VARCHAR" property="laborCompanyName" />
    <result column="labor_company_id" jdbcType="INTEGER" property="laborCompanyId" />
    <result column="entry_date" jdbcType="VARCHAR" property="entryDate" />
    <result column="first_pickup_time" jdbcType="VARCHAR" property="firstPickupTime" />
    <result column="driver_a_date" jdbcType="VARCHAR" property="driverADate" />
    <result column="comment" jdbcType="VARCHAR" property="comment" />
    <result column="creter_id" jdbcType="INTEGER" property="creterId" />
    <result column="creter_name" jdbcType="VARCHAR" property="creterName" />
    <result column="update_id" jdbcType="INTEGER" property="updateId" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    driver_id, create_time, update_time, driver_name, pinyin_name, driver_email, driver_mobile, 
    gender, head_icon, address, political_status, education_status, license_type, license_no, 
    verify_status, cert_name, cert_no, brith_date, term_of_validity, front_img_url, back_img_url, 
    lease_driver_id, driving_licence_img_url, driver_archives_no, issuing_organ, arrive_time, 
    driver_status, office_status, working_status, driver_type, driver_source_type, supplier_driver_line, 
    company_id, customer_company_code, customer_company_name, contract_code, struct_id, 
    struct_name, join_time, delete_time, last_login_time, bind_vehicle_id, belong_struct_code, 
    belong_struct_name, labor_company_name, labor_company_id, entry_date, first_pickup_time, 
    driver_a_date, comment, creter_id, creter_name, update_id, update_name
  </sql>
  <select id="selectByExample" parameterType="com.izu.user.entity.DriverExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_driver
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_driver
    where driver_id = #{driverId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from t_driver
    where driver_id = #{driverId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.user.entity.DriverExample">
    delete from t_driver
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.user.entity.Driver">
    <selectKey keyProperty="driverId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into t_driver (create_time, update_time, driver_name, 
      pinyin_name, driver_email, driver_mobile, 
      gender, head_icon, address, 
      political_status, education_status, license_type, 
      license_no, verify_status, cert_name, 
      cert_no, brith_date, term_of_validity, 
      front_img_url, back_img_url, lease_driver_id, 
      driving_licence_img_url, driver_archives_no, 
      issuing_organ, arrive_time, driver_status, 
      office_status, working_status, driver_type, 
      driver_source_type, supplier_driver_line, company_id, 
      customer_company_code, customer_company_name, 
      contract_code, struct_id, struct_name, 
      join_time, delete_time, last_login_time, 
      bind_vehicle_id, belong_struct_code, belong_struct_name, 
      labor_company_name, labor_company_id, entry_date, 
      first_pickup_time, driver_a_date, comment, 
      creter_id, creter_name, update_id, 
      update_name)
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{driverName,jdbcType=VARCHAR}, 
      #{pinyinName,jdbcType=VARCHAR}, #{driverEmail,jdbcType=VARCHAR}, #{driverMobile,jdbcType=VARCHAR}, 
      #{gender,jdbcType=TINYINT}, #{headIcon,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, 
      #{politicalStatus,jdbcType=TINYINT}, #{educationStatus,jdbcType=TINYINT}, #{licenseType,jdbcType=VARCHAR}, 
      #{licenseNo,jdbcType=VARCHAR}, #{verifyStatus,jdbcType=INTEGER}, #{certName,jdbcType=VARCHAR}, 
      #{certNo,jdbcType=VARCHAR}, #{brithDate,jdbcType=VARCHAR}, #{termOfValidity,jdbcType=DATE}, 
      #{frontImgUrl,jdbcType=VARCHAR}, #{backImgUrl,jdbcType=VARCHAR}, #{leaseDriverId,jdbcType=INTEGER}, 
      #{drivingLicenceImgUrl,jdbcType=VARCHAR}, #{driverArchivesNo,jdbcType=VARCHAR}, 
      #{issuingOrgan,jdbcType=VARCHAR}, #{arriveTime,jdbcType=VARCHAR}, #{driverStatus,jdbcType=INTEGER}, 
      #{officeStatus,jdbcType=TINYINT}, #{workingStatus,jdbcType=TINYINT}, #{driverType,jdbcType=INTEGER}, 
      #{driverSourceType,jdbcType=TINYINT}, #{supplierDriverLine,jdbcType=VARCHAR}, #{companyId,jdbcType=INTEGER}, 
      #{customerCompanyCode,jdbcType=VARCHAR}, #{customerCompanyName,jdbcType=VARCHAR}, 
      #{contractCode,jdbcType=VARCHAR}, #{structId,jdbcType=INTEGER}, #{structName,jdbcType=VARCHAR}, 
      #{joinTime,jdbcType=VARCHAR}, #{deleteTime,jdbcType=VARCHAR}, #{lastLoginTime,jdbcType=VARCHAR}, 
      #{bindVehicleId,jdbcType=INTEGER}, #{belongStructCode,jdbcType=VARCHAR}, #{belongStructName,jdbcType=VARCHAR}, 
      #{laborCompanyName,jdbcType=VARCHAR}, #{laborCompanyId,jdbcType=INTEGER}, #{entryDate,jdbcType=VARCHAR}, 
      #{firstPickupTime,jdbcType=VARCHAR}, #{driverADate,jdbcType=VARCHAR}, #{comment,jdbcType=VARCHAR}, 
      #{creterId,jdbcType=INTEGER}, #{creterName,jdbcType=VARCHAR}, #{updateId,jdbcType=INTEGER}, 
      #{updateName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.user.entity.Driver">
    <selectKey keyProperty="driverId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into t_driver
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="driverName != null">
        driver_name,
      </if>
      <if test="pinyinName != null">
        pinyin_name,
      </if>
      <if test="driverEmail != null">
        driver_email,
      </if>
      <if test="driverMobile != null">
        driver_mobile,
      </if>
      <if test="gender != null">
        gender,
      </if>
      <if test="headIcon != null">
        head_icon,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="politicalStatus != null">
        political_status,
      </if>
      <if test="educationStatus != null">
        education_status,
      </if>
      <if test="licenseType != null">
        license_type,
      </if>
      <if test="licenseNo != null">
        license_no,
      </if>
      <if test="verifyStatus != null">
        verify_status,
      </if>
      <if test="certName != null">
        cert_name,
      </if>
      <if test="certNo != null">
        cert_no,
      </if>
      <if test="brithDate != null">
        brith_date,
      </if>
      <if test="termOfValidity != null">
        term_of_validity,
      </if>
      <if test="frontImgUrl != null">
        front_img_url,
      </if>
      <if test="backImgUrl != null">
        back_img_url,
      </if>
      <if test="leaseDriverId != null">
        lease_driver_id,
      </if>
      <if test="drivingLicenceImgUrl != null">
        driving_licence_img_url,
      </if>
      <if test="driverArchivesNo != null">
        driver_archives_no,
      </if>
      <if test="issuingOrgan != null">
        issuing_organ,
      </if>
      <if test="arriveTime != null">
        arrive_time,
      </if>
      <if test="driverStatus != null">
        driver_status,
      </if>
      <if test="officeStatus != null">
        office_status,
      </if>
      <if test="workingStatus != null">
        working_status,
      </if>
      <if test="driverType != null">
        driver_type,
      </if>
      <if test="driverSourceType != null">
        driver_source_type,
      </if>
      <if test="supplierDriverLine != null">
        supplier_driver_line,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="customerCompanyCode != null">
        customer_company_code,
      </if>
      <if test="customerCompanyName != null">
        customer_company_name,
      </if>
      <if test="contractCode != null">
        contract_code,
      </if>
      <if test="structId != null">
        struct_id,
      </if>
      <if test="structName != null">
        struct_name,
      </if>
      <if test="joinTime != null">
        join_time,
      </if>
      <if test="deleteTime != null">
        delete_time,
      </if>
      <if test="lastLoginTime != null">
        last_login_time,
      </if>
      <if test="bindVehicleId != null">
        bind_vehicle_id,
      </if>
      <if test="belongStructCode != null">
        belong_struct_code,
      </if>
      <if test="belongStructName != null">
        belong_struct_name,
      </if>
      <if test="laborCompanyName != null">
        labor_company_name,
      </if>
      <if test="laborCompanyId != null">
        labor_company_id,
      </if>
      <if test="entryDate != null">
        entry_date,
      </if>
      <if test="firstPickupTime != null">
        first_pickup_time,
      </if>
      <if test="driverADate != null">
        driver_a_date,
      </if>
      <if test="comment != null">
        comment,
      </if>
      <if test="creterId != null">
        creter_id,
      </if>
      <if test="creterName != null">
        creter_name,
      </if>
      <if test="updateId != null">
        update_id,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="driverName != null">
        #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="pinyinName != null">
        #{pinyinName,jdbcType=VARCHAR},
      </if>
      <if test="driverEmail != null">
        #{driverEmail,jdbcType=VARCHAR},
      </if>
      <if test="driverMobile != null">
        #{driverMobile,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=TINYINT},
      </if>
      <if test="headIcon != null">
        #{headIcon,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="politicalStatus != null">
        #{politicalStatus,jdbcType=TINYINT},
      </if>
      <if test="educationStatus != null">
        #{educationStatus,jdbcType=TINYINT},
      </if>
      <if test="licenseType != null">
        #{licenseType,jdbcType=VARCHAR},
      </if>
      <if test="licenseNo != null">
        #{licenseNo,jdbcType=VARCHAR},
      </if>
      <if test="verifyStatus != null">
        #{verifyStatus,jdbcType=INTEGER},
      </if>
      <if test="certName != null">
        #{certName,jdbcType=VARCHAR},
      </if>
      <if test="certNo != null">
        #{certNo,jdbcType=VARCHAR},
      </if>
      <if test="brithDate != null">
        #{brithDate,jdbcType=VARCHAR},
      </if>
      <if test="termOfValidity != null">
        #{termOfValidity,jdbcType=DATE},
      </if>
      <if test="frontImgUrl != null">
        #{frontImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="backImgUrl != null">
        #{backImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="leaseDriverId != null">
        #{leaseDriverId,jdbcType=INTEGER},
      </if>
      <if test="drivingLicenceImgUrl != null">
        #{drivingLicenceImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="driverArchivesNo != null">
        #{driverArchivesNo,jdbcType=VARCHAR},
      </if>
      <if test="issuingOrgan != null">
        #{issuingOrgan,jdbcType=VARCHAR},
      </if>
      <if test="arriveTime != null">
        #{arriveTime,jdbcType=VARCHAR},
      </if>
      <if test="driverStatus != null">
        #{driverStatus,jdbcType=INTEGER},
      </if>
      <if test="officeStatus != null">
        #{officeStatus,jdbcType=TINYINT},
      </if>
      <if test="workingStatus != null">
        #{workingStatus,jdbcType=TINYINT},
      </if>
      <if test="driverType != null">
        #{driverType,jdbcType=INTEGER},
      </if>
      <if test="driverSourceType != null">
        #{driverSourceType,jdbcType=TINYINT},
      </if>
      <if test="supplierDriverLine != null">
        #{supplierDriverLine,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="customerCompanyCode != null">
        #{customerCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="customerCompanyName != null">
        #{customerCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="contractCode != null">
        #{contractCode,jdbcType=VARCHAR},
      </if>
      <if test="structId != null">
        #{structId,jdbcType=INTEGER},
      </if>
      <if test="structName != null">
        #{structName,jdbcType=VARCHAR},
      </if>
      <if test="joinTime != null">
        #{joinTime,jdbcType=VARCHAR},
      </if>
      <if test="deleteTime != null">
        #{deleteTime,jdbcType=VARCHAR},
      </if>
      <if test="lastLoginTime != null">
        #{lastLoginTime,jdbcType=VARCHAR},
      </if>
      <if test="bindVehicleId != null">
        #{bindVehicleId,jdbcType=INTEGER},
      </if>
      <if test="belongStructCode != null">
        #{belongStructCode,jdbcType=VARCHAR},
      </if>
      <if test="belongStructName != null">
        #{belongStructName,jdbcType=VARCHAR},
      </if>
      <if test="laborCompanyName != null">
        #{laborCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="laborCompanyId != null">
        #{laborCompanyId,jdbcType=INTEGER},
      </if>
      <if test="entryDate != null">
        #{entryDate,jdbcType=VARCHAR},
      </if>
      <if test="firstPickupTime != null">
        #{firstPickupTime,jdbcType=VARCHAR},
      </if>
      <if test="driverADate != null">
        #{driverADate,jdbcType=VARCHAR},
      </if>
      <if test="comment != null">
        #{comment,jdbcType=VARCHAR},
      </if>
      <if test="creterId != null">
        #{creterId,jdbcType=INTEGER},
      </if>
      <if test="creterName != null">
        #{creterName,jdbcType=VARCHAR},
      </if>
      <if test="updateId != null">
        #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.user.entity.DriverExample" resultType="java.lang.Long">
    select count(*) from t_driver
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update t_driver
    <set>
      <if test="row.driverId != null">
        driver_id = #{row.driverId,jdbcType=INTEGER},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.driverName != null">
        driver_name = #{row.driverName,jdbcType=VARCHAR},
      </if>
      <if test="row.pinyinName != null">
        pinyin_name = #{row.pinyinName,jdbcType=VARCHAR},
      </if>
      <if test="row.driverEmail != null">
        driver_email = #{row.driverEmail,jdbcType=VARCHAR},
      </if>
      <if test="row.driverMobile != null">
        driver_mobile = #{row.driverMobile,jdbcType=VARCHAR},
      </if>
      <if test="row.gender != null">
        gender = #{row.gender,jdbcType=TINYINT},
      </if>
      <if test="row.headIcon != null">
        head_icon = #{row.headIcon,jdbcType=VARCHAR},
      </if>
      <if test="row.address != null">
        address = #{row.address,jdbcType=VARCHAR},
      </if>
      <if test="row.politicalStatus != null">
        political_status = #{row.politicalStatus,jdbcType=TINYINT},
      </if>
      <if test="row.educationStatus != null">
        education_status = #{row.educationStatus,jdbcType=TINYINT},
      </if>
      <if test="row.licenseType != null">
        license_type = #{row.licenseType,jdbcType=VARCHAR},
      </if>
      <if test="row.licenseNo != null">
        license_no = #{row.licenseNo,jdbcType=VARCHAR},
      </if>
      <if test="row.verifyStatus != null">
        verify_status = #{row.verifyStatus,jdbcType=INTEGER},
      </if>
      <if test="row.certName != null">
        cert_name = #{row.certName,jdbcType=VARCHAR},
      </if>
      <if test="row.certNo != null">
        cert_no = #{row.certNo,jdbcType=VARCHAR},
      </if>
      <if test="row.brithDate != null">
        brith_date = #{row.brithDate,jdbcType=VARCHAR},
      </if>
      <if test="row.termOfValidity != null">
        term_of_validity = #{row.termOfValidity,jdbcType=DATE},
      </if>
      <if test="row.frontImgUrl != null">
        front_img_url = #{row.frontImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="row.backImgUrl != null">
        back_img_url = #{row.backImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="row.leaseDriverId != null">
        lease_driver_id = #{row.leaseDriverId,jdbcType=INTEGER},
      </if>
      <if test="row.drivingLicenceImgUrl != null">
        driving_licence_img_url = #{row.drivingLicenceImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="row.driverArchivesNo != null">
        driver_archives_no = #{row.driverArchivesNo,jdbcType=VARCHAR},
      </if>
      <if test="row.issuingOrgan != null">
        issuing_organ = #{row.issuingOrgan,jdbcType=VARCHAR},
      </if>
      <if test="row.arriveTime != null">
        arrive_time = #{row.arriveTime,jdbcType=VARCHAR},
      </if>
      <if test="row.driverStatus != null">
        driver_status = #{row.driverStatus,jdbcType=INTEGER},
      </if>
      <if test="row.officeStatus != null">
        office_status = #{row.officeStatus,jdbcType=TINYINT},
      </if>
      <if test="row.workingStatus != null">
        working_status = #{row.workingStatus,jdbcType=TINYINT},
      </if>
      <if test="row.driverType != null">
        driver_type = #{row.driverType,jdbcType=INTEGER},
      </if>
      <if test="row.driverSourceType != null">
        driver_source_type = #{row.driverSourceType,jdbcType=TINYINT},
      </if>
      <if test="row.supplierDriverLine != null">
        supplier_driver_line = #{row.supplierDriverLine,jdbcType=VARCHAR},
      </if>
      <if test="row.companyId != null">
        company_id = #{row.companyId,jdbcType=INTEGER},
      </if>
      <if test="row.customerCompanyCode != null">
        customer_company_code = #{row.customerCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="row.customerCompanyName != null">
        customer_company_name = #{row.customerCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="row.contractCode != null">
        contract_code = #{row.contractCode,jdbcType=VARCHAR},
      </if>
      <if test="row.structId != null">
        struct_id = #{row.structId,jdbcType=INTEGER},
      </if>
      <if test="row.structName != null">
        struct_name = #{row.structName,jdbcType=VARCHAR},
      </if>
      <if test="row.joinTime != null">
        join_time = #{row.joinTime,jdbcType=VARCHAR},
      </if>
      <if test="row.deleteTime != null">
        delete_time = #{row.deleteTime,jdbcType=VARCHAR},
      </if>
      <if test="row.lastLoginTime != null">
        last_login_time = #{row.lastLoginTime,jdbcType=VARCHAR},
      </if>
      <if test="row.bindVehicleId != null">
        bind_vehicle_id = #{row.bindVehicleId,jdbcType=INTEGER},
      </if>
      <if test="row.belongStructCode != null">
        belong_struct_code = #{row.belongStructCode,jdbcType=VARCHAR},
      </if>
      <if test="row.belongStructName != null">
        belong_struct_name = #{row.belongStructName,jdbcType=VARCHAR},
      </if>
      <if test="row.laborCompanyName != null">
        labor_company_name = #{row.laborCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="row.laborCompanyId != null">
        labor_company_id = #{row.laborCompanyId,jdbcType=INTEGER},
      </if>
      <if test="row.entryDate != null">
        entry_date = #{row.entryDate,jdbcType=VARCHAR},
      </if>
      <if test="row.firstPickupTime != null">
        first_pickup_time = #{row.firstPickupTime,jdbcType=VARCHAR},
      </if>
      <if test="row.driverADate != null">
        driver_a_date = #{row.driverADate,jdbcType=VARCHAR},
      </if>
      <if test="row.comment != null">
        comment = #{row.comment,jdbcType=VARCHAR},
      </if>
      <if test="row.creterId != null">
        creter_id = #{row.creterId,jdbcType=INTEGER},
      </if>
      <if test="row.creterName != null">
        creter_name = #{row.creterName,jdbcType=VARCHAR},
      </if>
      <if test="row.updateId != null">
        update_id = #{row.updateId,jdbcType=INTEGER},
      </if>
      <if test="row.updateName != null">
        update_name = #{row.updateName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update t_driver
    set driver_id = #{row.driverId,jdbcType=INTEGER},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      driver_name = #{row.driverName,jdbcType=VARCHAR},
      pinyin_name = #{row.pinyinName,jdbcType=VARCHAR},
      driver_email = #{row.driverEmail,jdbcType=VARCHAR},
      driver_mobile = #{row.driverMobile,jdbcType=VARCHAR},
      gender = #{row.gender,jdbcType=TINYINT},
      head_icon = #{row.headIcon,jdbcType=VARCHAR},
      address = #{row.address,jdbcType=VARCHAR},
      political_status = #{row.politicalStatus,jdbcType=TINYINT},
      education_status = #{row.educationStatus,jdbcType=TINYINT},
      license_type = #{row.licenseType,jdbcType=VARCHAR},
      license_no = #{row.licenseNo,jdbcType=VARCHAR},
      verify_status = #{row.verifyStatus,jdbcType=INTEGER},
      cert_name = #{row.certName,jdbcType=VARCHAR},
      cert_no = #{row.certNo,jdbcType=VARCHAR},
      brith_date = #{row.brithDate,jdbcType=VARCHAR},
      term_of_validity = #{row.termOfValidity,jdbcType=DATE},
      front_img_url = #{row.frontImgUrl,jdbcType=VARCHAR},
      back_img_url = #{row.backImgUrl,jdbcType=VARCHAR},
      lease_driver_id = #{row.leaseDriverId,jdbcType=INTEGER},
      driving_licence_img_url = #{row.drivingLicenceImgUrl,jdbcType=VARCHAR},
      driver_archives_no = #{row.driverArchivesNo,jdbcType=VARCHAR},
      issuing_organ = #{row.issuingOrgan,jdbcType=VARCHAR},
      arrive_time = #{row.arriveTime,jdbcType=VARCHAR},
      driver_status = #{row.driverStatus,jdbcType=INTEGER},
      office_status = #{row.officeStatus,jdbcType=TINYINT},
      working_status = #{row.workingStatus,jdbcType=TINYINT},
      driver_type = #{row.driverType,jdbcType=INTEGER},
      driver_source_type = #{row.driverSourceType,jdbcType=TINYINT},
      supplier_driver_line = #{row.supplierDriverLine,jdbcType=VARCHAR},
      company_id = #{row.companyId,jdbcType=INTEGER},
      customer_company_code = #{row.customerCompanyCode,jdbcType=VARCHAR},
      customer_company_name = #{row.customerCompanyName,jdbcType=VARCHAR},
      contract_code = #{row.contractCode,jdbcType=VARCHAR},
      struct_id = #{row.structId,jdbcType=INTEGER},
      struct_name = #{row.structName,jdbcType=VARCHAR},
      join_time = #{row.joinTime,jdbcType=VARCHAR},
      delete_time = #{row.deleteTime,jdbcType=VARCHAR},
      last_login_time = #{row.lastLoginTime,jdbcType=VARCHAR},
      bind_vehicle_id = #{row.bindVehicleId,jdbcType=INTEGER},
      belong_struct_code = #{row.belongStructCode,jdbcType=VARCHAR},
      belong_struct_name = #{row.belongStructName,jdbcType=VARCHAR},
      labor_company_name = #{row.laborCompanyName,jdbcType=VARCHAR},
      labor_company_id = #{row.laborCompanyId,jdbcType=INTEGER},
      entry_date = #{row.entryDate,jdbcType=VARCHAR},
      first_pickup_time = #{row.firstPickupTime,jdbcType=VARCHAR},
      driver_a_date = #{row.driverADate,jdbcType=VARCHAR},
      comment = #{row.comment,jdbcType=VARCHAR},
      creter_id = #{row.creterId,jdbcType=INTEGER},
      creter_name = #{row.creterName,jdbcType=VARCHAR},
      update_id = #{row.updateId,jdbcType=INTEGER},
      update_name = #{row.updateName,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.user.entity.Driver">
    update t_driver
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="driverName != null">
        driver_name = #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="pinyinName != null">
        pinyin_name = #{pinyinName,jdbcType=VARCHAR},
      </if>
      <if test="driverEmail != null">
        driver_email = #{driverEmail,jdbcType=VARCHAR},
      </if>
      <if test="driverMobile != null">
        driver_mobile = #{driverMobile,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        gender = #{gender,jdbcType=TINYINT},
      </if>
      <if test="headIcon != null">
        head_icon = #{headIcon,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="politicalStatus != null">
        political_status = #{politicalStatus,jdbcType=TINYINT},
      </if>
      <if test="educationStatus != null">
        education_status = #{educationStatus,jdbcType=TINYINT},
      </if>
      <if test="licenseType != null">
        license_type = #{licenseType,jdbcType=VARCHAR},
      </if>
      <if test="licenseNo != null">
        license_no = #{licenseNo,jdbcType=VARCHAR},
      </if>
      <if test="verifyStatus != null">
        verify_status = #{verifyStatus,jdbcType=INTEGER},
      </if>
      <if test="certName != null">
        cert_name = #{certName,jdbcType=VARCHAR},
      </if>
      <if test="certNo != null">
        cert_no = #{certNo,jdbcType=VARCHAR},
      </if>
      <if test="brithDate != null">
        brith_date = #{brithDate,jdbcType=VARCHAR},
      </if>
      <if test="termOfValidity != null">
        term_of_validity = #{termOfValidity,jdbcType=DATE},
      </if>
      <if test="frontImgUrl != null">
        front_img_url = #{frontImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="backImgUrl != null">
        back_img_url = #{backImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="leaseDriverId != null">
        lease_driver_id = #{leaseDriverId,jdbcType=INTEGER},
      </if>
      <if test="drivingLicenceImgUrl != null">
        driving_licence_img_url = #{drivingLicenceImgUrl,jdbcType=VARCHAR},
      </if>
      <if test="driverArchivesNo != null">
        driver_archives_no = #{driverArchivesNo,jdbcType=VARCHAR},
      </if>
      <if test="issuingOrgan != null">
        issuing_organ = #{issuingOrgan,jdbcType=VARCHAR},
      </if>
      <if test="arriveTime != null">
        arrive_time = #{arriveTime,jdbcType=VARCHAR},
      </if>
      <if test="driverStatus != null">
        driver_status = #{driverStatus,jdbcType=INTEGER},
      </if>
      <if test="officeStatus != null">
        office_status = #{officeStatus,jdbcType=TINYINT},
      </if>
      <if test="workingStatus != null">
        working_status = #{workingStatus,jdbcType=TINYINT},
      </if>
      <if test="driverType != null">
        driver_type = #{driverType,jdbcType=INTEGER},
      </if>
      <if test="driverSourceType != null">
        driver_source_type = #{driverSourceType,jdbcType=TINYINT},
      </if>
      <if test="supplierDriverLine != null">
        supplier_driver_line = #{supplierDriverLine,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="customerCompanyCode != null">
        customer_company_code = #{customerCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="customerCompanyName != null">
        customer_company_name = #{customerCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="contractCode != null">
        contract_code = #{contractCode,jdbcType=VARCHAR},
      </if>
      <if test="structId != null">
        struct_id = #{structId,jdbcType=INTEGER},
      </if>
      <if test="structName != null">
        struct_name = #{structName,jdbcType=VARCHAR},
      </if>
      <if test="joinTime != null">
        join_time = #{joinTime,jdbcType=VARCHAR},
      </if>
      <if test="deleteTime != null">
        delete_time = #{deleteTime,jdbcType=VARCHAR},
      </if>
      <if test="lastLoginTime != null">
        last_login_time = #{lastLoginTime,jdbcType=VARCHAR},
      </if>
      <if test="bindVehicleId != null">
        bind_vehicle_id = #{bindVehicleId,jdbcType=INTEGER},
      </if>
      <if test="belongStructCode != null">
        belong_struct_code = #{belongStructCode,jdbcType=VARCHAR},
      </if>
      <if test="belongStructName != null">
        belong_struct_name = #{belongStructName,jdbcType=VARCHAR},
      </if>
      <if test="laborCompanyName != null">
        labor_company_name = #{laborCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="laborCompanyId != null">
        labor_company_id = #{laborCompanyId,jdbcType=INTEGER},
      </if>
      <if test="entryDate != null">
        entry_date = #{entryDate,jdbcType=VARCHAR},
      </if>
      <if test="firstPickupTime != null">
        first_pickup_time = #{firstPickupTime,jdbcType=VARCHAR},
      </if>
      <if test="driverADate != null">
        driver_a_date = #{driverADate,jdbcType=VARCHAR},
      </if>
      <if test="comment != null">
        comment = #{comment,jdbcType=VARCHAR},
      </if>
      <if test="creterId != null">
        creter_id = #{creterId,jdbcType=INTEGER},
      </if>
      <if test="creterName != null">
        creter_name = #{creterName,jdbcType=VARCHAR},
      </if>
      <if test="updateId != null">
        update_id = #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
    </set>
    where driver_id = #{driverId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.user.entity.Driver">
    update t_driver
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      driver_name = #{driverName,jdbcType=VARCHAR},
      pinyin_name = #{pinyinName,jdbcType=VARCHAR},
      driver_email = #{driverEmail,jdbcType=VARCHAR},
      driver_mobile = #{driverMobile,jdbcType=VARCHAR},
      gender = #{gender,jdbcType=TINYINT},
      head_icon = #{headIcon,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      political_status = #{politicalStatus,jdbcType=TINYINT},
      education_status = #{educationStatus,jdbcType=TINYINT},
      license_type = #{licenseType,jdbcType=VARCHAR},
      license_no = #{licenseNo,jdbcType=VARCHAR},
      verify_status = #{verifyStatus,jdbcType=INTEGER},
      cert_name = #{certName,jdbcType=VARCHAR},
      cert_no = #{certNo,jdbcType=VARCHAR},
      brith_date = #{brithDate,jdbcType=VARCHAR},
      term_of_validity = #{termOfValidity,jdbcType=DATE},
      front_img_url = #{frontImgUrl,jdbcType=VARCHAR},
      back_img_url = #{backImgUrl,jdbcType=VARCHAR},
      lease_driver_id = #{leaseDriverId,jdbcType=INTEGER},
      driving_licence_img_url = #{drivingLicenceImgUrl,jdbcType=VARCHAR},
      driver_archives_no = #{driverArchivesNo,jdbcType=VARCHAR},
      issuing_organ = #{issuingOrgan,jdbcType=VARCHAR},
      arrive_time = #{arriveTime,jdbcType=VARCHAR},
      driver_status = #{driverStatus,jdbcType=INTEGER},
      office_status = #{officeStatus,jdbcType=TINYINT},
      working_status = #{workingStatus,jdbcType=TINYINT},
      driver_type = #{driverType,jdbcType=INTEGER},
      driver_source_type = #{driverSourceType,jdbcType=TINYINT},
      supplier_driver_line = #{supplierDriverLine,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=INTEGER},
      customer_company_code = #{customerCompanyCode,jdbcType=VARCHAR},
      customer_company_name = #{customerCompanyName,jdbcType=VARCHAR},
      contract_code = #{contractCode,jdbcType=VARCHAR},
      struct_id = #{structId,jdbcType=INTEGER},
      struct_name = #{structName,jdbcType=VARCHAR},
      join_time = #{joinTime,jdbcType=VARCHAR},
      delete_time = #{deleteTime,jdbcType=VARCHAR},
      last_login_time = #{lastLoginTime,jdbcType=VARCHAR},
      bind_vehicle_id = #{bindVehicleId,jdbcType=INTEGER},
      belong_struct_code = #{belongStructCode,jdbcType=VARCHAR},
      belong_struct_name = #{belongStructName,jdbcType=VARCHAR},
      labor_company_name = #{laborCompanyName,jdbcType=VARCHAR},
      labor_company_id = #{laborCompanyId,jdbcType=INTEGER},
      entry_date = #{entryDate,jdbcType=VARCHAR},
      first_pickup_time = #{firstPickupTime,jdbcType=VARCHAR},
      driver_a_date = #{driverADate,jdbcType=VARCHAR},
      comment = #{comment,jdbcType=VARCHAR},
      creter_id = #{creterId,jdbcType=INTEGER},
      creter_name = #{creterName,jdbcType=VARCHAR},
      update_id = #{updateId,jdbcType=INTEGER},
      update_name = #{updateName,jdbcType=VARCHAR}
    where driver_id = #{driverId,jdbcType=INTEGER}
  </update>
</mapper>