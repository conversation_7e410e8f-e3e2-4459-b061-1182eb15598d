<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.MotorcadeDriverMapper">
  <resultMap id="BaseResultMap" type="com.izu.business.entity.MotorcadeDriver">
    <!--@mbg.generated-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="motorcade_id" jdbcType="INTEGER" property="motorcadeId" />
    <result column="driver_id" jdbcType="INTEGER" property="driverId" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="delete_time" jdbcType="TIMESTAMP" property="deleteTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, motorcade_id, driver_id, company_id, `status`, create_time, delete_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from motorcade_driver
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from motorcade_driver
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.izu.business.entity.MotorcadeDriver" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into motorcade_driver (motorcade_id, driver_id, company_id, 
      `status`, create_time, delete_time
      )
    values (#{motorcadeId,jdbcType=INTEGER}, #{driverId,jdbcType=INTEGER}, #{companyId,jdbcType=INTEGER}, 
      #{status,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, #{deleteTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.izu.business.entity.MotorcadeDriver" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into motorcade_driver
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="motorcadeId != null">
        motorcade_id,
      </if>
      <if test="driverId != null">
        driver_id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="deleteTime != null">
        delete_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="motorcadeId != null">
        #{motorcadeId,jdbcType=INTEGER},
      </if>
      <if test="driverId != null">
        #{driverId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleteTime != null">
        #{deleteTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.business.entity.MotorcadeDriver">
    <!--@mbg.generated-->
    update motorcade_driver
    <set>
      <if test="motorcadeId != null">
        motorcade_id = #{motorcadeId,jdbcType=INTEGER},
      </if>
      <if test="driverId != null">
        driver_id = #{driverId,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleteTime != null">
        delete_time = #{deleteTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.business.entity.MotorcadeDriver">
    <!--@mbg.generated-->
    update motorcade_driver
    set motorcade_id = #{motorcadeId,jdbcType=INTEGER},
      driver_id = #{driverId,jdbcType=INTEGER},
      company_id = #{companyId,jdbcType=INTEGER},
      `status` = #{status,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      delete_time = #{deleteTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>