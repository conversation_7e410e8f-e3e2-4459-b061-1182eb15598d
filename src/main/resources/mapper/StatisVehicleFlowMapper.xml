<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.StatisVehicleFlowMapper">
  <resultMap id="BaseResultMap" type="com.izu.business.entity.StatisVehicleFlow">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="static_date" jdbcType="VARCHAR" property="staticDate" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="vehicle_vin" jdbcType="VARCHAR" property="vehicleVin" />
    <result column="vehicle_license" jdbcType="VARCHAR" property="vehicleLicense" />
    <result column="vehicle_id_new_system" jdbcType="INTEGER" property="vehicleIdNewSystem" />
    <result column="vehicle_serial_no_new_system" jdbcType="VARCHAR" property="vehicleSerialNoNewSystem" />
    <result column="lease_purpose" jdbcType="VARCHAR" property="leasePurpose" />
    <result column="account_city_code" jdbcType="VARCHAR" property="accountCityCode" />
    <result column="account_city_name" jdbcType="VARCHAR" property="accountCityName" />
    <result column="operate_struct_code" jdbcType="VARCHAR" property="operateStructCode" />
    <result column="asset_struct_code" jdbcType="VARCHAR" property="assetStructCode" />
    <result column="asset_struct_name" jdbcType="VARCHAR" property="assetStructName" />
    <result column="belong_city_code" jdbcType="VARCHAR" property="belongCityCode" />
    <result column="belong_city_name" jdbcType="VARCHAR" property="belongCityName" />
    <result column="driver_city_code" jdbcType="VARCHAR" property="driverCityCode" />
    <result column="driver_city_name" jdbcType="VARCHAR" property="driverCityName" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="order_type" jdbcType="TINYINT" property="orderType" />
    <result column="service_code" jdbcType="VARCHAR" property="serviceCode" />
    <result column="rent_count" jdbcType="DECIMAL" property="rentCount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, static_date, vehicle_id, vehicle_vin, vehicle_license, vehicle_id_new_system, 
    vehicle_serial_no_new_system, lease_purpose, account_city_code, account_city_name, 
    operate_struct_code, asset_struct_code, asset_struct_name, belong_city_code, belong_city_name, 
    driver_city_code, driver_city_name, order_no, order_type, service_code, rent_count, 
    create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from statis_vehicle_flow
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from statis_vehicle_flow
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.izu.business.entity.StatisVehicleFlow">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into statis_vehicle_flow (static_date, vehicle_id, vehicle_vin, 
      vehicle_license, vehicle_id_new_system, vehicle_serial_no_new_system, 
      lease_purpose, account_city_code, account_city_name, 
      operate_struct_code, asset_struct_code, asset_struct_name, 
      belong_city_code, belong_city_name, driver_city_code, 
      driver_city_name, order_no, order_type, 
      service_code, rent_count, create_time, 
      update_time)
    values (#{staticDate,jdbcType=VARCHAR}, #{vehicleId,jdbcType=BIGINT}, #{vehicleVin,jdbcType=VARCHAR}, 
      #{vehicleLicense,jdbcType=VARCHAR}, #{vehicleIdNewSystem,jdbcType=INTEGER}, #{vehicleSerialNoNewSystem,jdbcType=VARCHAR}, 
      #{leasePurpose,jdbcType=VARCHAR}, #{accountCityCode,jdbcType=VARCHAR}, #{accountCityName,jdbcType=VARCHAR}, 
      #{operateStructCode,jdbcType=VARCHAR}, #{assetStructCode,jdbcType=VARCHAR}, #{assetStructName,jdbcType=VARCHAR}, 
      #{belongCityCode,jdbcType=VARCHAR}, #{belongCityName,jdbcType=VARCHAR}, #{driverCityCode,jdbcType=VARCHAR}, 
      #{driverCityName,jdbcType=VARCHAR}, #{orderNo,jdbcType=VARCHAR}, #{orderType,jdbcType=TINYINT}, 
      #{serviceCode,jdbcType=VARCHAR}, #{rentCount,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.business.entity.StatisVehicleFlow">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into statis_vehicle_flow
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="staticDate != null">
        static_date,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="vehicleVin != null">
        vehicle_vin,
      </if>
      <if test="vehicleLicense != null">
        vehicle_license,
      </if>
      <if test="vehicleIdNewSystem != null">
        vehicle_id_new_system,
      </if>
      <if test="vehicleSerialNoNewSystem != null">
        vehicle_serial_no_new_system,
      </if>
      <if test="leasePurpose != null">
        lease_purpose,
      </if>
      <if test="accountCityCode != null">
        account_city_code,
      </if>
      <if test="accountCityName != null">
        account_city_name,
      </if>
      <if test="operateStructCode != null">
        operate_struct_code,
      </if>
      <if test="assetStructCode != null">
        asset_struct_code,
      </if>
      <if test="assetStructName != null">
        asset_struct_name,
      </if>
      <if test="belongCityCode != null">
        belong_city_code,
      </if>
      <if test="belongCityName != null">
        belong_city_name,
      </if>
      <if test="driverCityCode != null">
        driver_city_code,
      </if>
      <if test="driverCityName != null">
        driver_city_name,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="serviceCode != null">
        service_code,
      </if>
      <if test="rentCount != null">
        rent_count,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="staticDate != null">
        #{staticDate,jdbcType=VARCHAR},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleVin != null">
        #{vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="vehicleLicense != null">
        #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="vehicleIdNewSystem != null">
        #{vehicleIdNewSystem,jdbcType=INTEGER},
      </if>
      <if test="vehicleSerialNoNewSystem != null">
        #{vehicleSerialNoNewSystem,jdbcType=VARCHAR},
      </if>
      <if test="leasePurpose != null">
        #{leasePurpose,jdbcType=VARCHAR},
      </if>
      <if test="accountCityCode != null">
        #{accountCityCode,jdbcType=VARCHAR},
      </if>
      <if test="accountCityName != null">
        #{accountCityName,jdbcType=VARCHAR},
      </if>
      <if test="operateStructCode != null">
        #{operateStructCode,jdbcType=VARCHAR},
      </if>
      <if test="assetStructCode != null">
        #{assetStructCode,jdbcType=VARCHAR},
      </if>
      <if test="assetStructName != null">
        #{assetStructName,jdbcType=VARCHAR},
      </if>
      <if test="belongCityCode != null">
        #{belongCityCode,jdbcType=VARCHAR},
      </if>
      <if test="belongCityName != null">
        #{belongCityName,jdbcType=VARCHAR},
      </if>
      <if test="driverCityCode != null">
        #{driverCityCode,jdbcType=VARCHAR},
      </if>
      <if test="driverCityName != null">
        #{driverCityName,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=TINYINT},
      </if>
      <if test="serviceCode != null">
        #{serviceCode,jdbcType=VARCHAR},
      </if>
      <if test="rentCount != null">
        #{rentCount,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.business.entity.StatisVehicleFlow">
    update statis_vehicle_flow
    <set>
      <if test="staticDate != null">
        static_date = #{staticDate,jdbcType=VARCHAR},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleVin != null">
        vehicle_vin = #{vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="vehicleLicense != null">
        vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="vehicleIdNewSystem != null">
        vehicle_id_new_system = #{vehicleIdNewSystem,jdbcType=INTEGER},
      </if>
      <if test="vehicleSerialNoNewSystem != null">
        vehicle_serial_no_new_system = #{vehicleSerialNoNewSystem,jdbcType=VARCHAR},
      </if>
      <if test="leasePurpose != null">
        lease_purpose = #{leasePurpose,jdbcType=VARCHAR},
      </if>
      <if test="accountCityCode != null">
        account_city_code = #{accountCityCode,jdbcType=VARCHAR},
      </if>
      <if test="accountCityName != null">
        account_city_name = #{accountCityName,jdbcType=VARCHAR},
      </if>
      <if test="operateStructCode != null">
        operate_struct_code = #{operateStructCode,jdbcType=VARCHAR},
      </if>
      <if test="assetStructCode != null">
        asset_struct_code = #{assetStructCode,jdbcType=VARCHAR},
      </if>
      <if test="assetStructName != null">
        asset_struct_name = #{assetStructName,jdbcType=VARCHAR},
      </if>
      <if test="belongCityCode != null">
        belong_city_code = #{belongCityCode,jdbcType=VARCHAR},
      </if>
      <if test="belongCityName != null">
        belong_city_name = #{belongCityName,jdbcType=VARCHAR},
      </if>
      <if test="driverCityCode != null">
        driver_city_code = #{driverCityCode,jdbcType=VARCHAR},
      </if>
      <if test="driverCityName != null">
        driver_city_name = #{driverCityName,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=TINYINT},
      </if>
      <if test="serviceCode != null">
        service_code = #{serviceCode,jdbcType=VARCHAR},
      </if>
      <if test="rentCount != null">
        rent_count = #{rentCount,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.business.entity.StatisVehicleFlow">
    update statis_vehicle_flow
    set static_date = #{staticDate,jdbcType=VARCHAR},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      vehicle_vin = #{vehicleVin,jdbcType=VARCHAR},
      vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      vehicle_id_new_system = #{vehicleIdNewSystem,jdbcType=INTEGER},
      vehicle_serial_no_new_system = #{vehicleSerialNoNewSystem,jdbcType=VARCHAR},
      lease_purpose = #{leasePurpose,jdbcType=VARCHAR},
      account_city_code = #{accountCityCode,jdbcType=VARCHAR},
      account_city_name = #{accountCityName,jdbcType=VARCHAR},
      operate_struct_code = #{operateStructCode,jdbcType=VARCHAR},
      asset_struct_code = #{assetStructCode,jdbcType=VARCHAR},
      asset_struct_name = #{assetStructName,jdbcType=VARCHAR},
      belong_city_code = #{belongCityCode,jdbcType=VARCHAR},
      belong_city_name = #{belongCityName,jdbcType=VARCHAR},
      driver_city_code = #{driverCityCode,jdbcType=VARCHAR},
      driver_city_name = #{driverCityName,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      order_type = #{orderType,jdbcType=TINYINT},
      service_code = #{serviceCode,jdbcType=VARCHAR},
      rent_count = #{rentCount,jdbcType=DECIMAL},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>