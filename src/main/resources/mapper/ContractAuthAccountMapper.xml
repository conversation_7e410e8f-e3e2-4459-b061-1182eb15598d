<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.ContractAuthAccountMapper">
  <resultMap id="BaseResultMap" type="com.izu.business.entity.ContractAuthAccount">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="contract_id" jdbcType="INTEGER" property="contractId" />
    <result column="contract_code" jdbcType="VARCHAR" property="contractCode" />
    <result column="contract_title" jdbcType="VARCHAR" property="contractTitle" />
    <result column="contract_type" jdbcType="TINYINT" property="contractType" />
    <result column="standard_contract" jdbcType="INTEGER" property="standardContract" />
    <result column="finance_struct_code" jdbcType="VARCHAR" property="financeStructCode" />
    <result column="finance_struct_name" jdbcType="VARCHAR" property="financeStructName" />
    <result column="software_trial_period" jdbcType="TINYINT" property="softwareTrialPeriod" />
    <result column="trial_period_sdate" jdbcType="TIMESTAMP" property="trialPeriodSdate" />
    <result column="trial_period_edate" jdbcType="TIMESTAMP" property="trialPeriodEdate" />
    <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="viability" jdbcType="INTEGER" property="viability" />
    <result column="cz_contract_code" jdbcType="VARCHAR" property="czContractCode" />
    <result column="tax_identification_number" jdbcType="VARCHAR" property="taxIdentificationNumber" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
    <result column="bank_account" jdbcType="VARCHAR" property="bankAccount" />
    <result column="invoice_type" jdbcType="TINYINT" property="invoiceType" />
    <result column="invoice_category" jdbcType="TINYINT" property="invoiceCategory" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="contract_status" jdbcType="INTEGER" property="contractStatus" />
    <result column="actual_sign_date" jdbcType="TIMESTAMP" property="actualSignDate" />
    <result column="account_use_method" jdbcType="TINYINT" property="accountUseMethod" />
    <result column="bind_contract_code" jdbcType="VARCHAR" property="bindContractCode" />
    <result column="product_pack_code" jdbcType="VARCHAR" property="productPackCode" />
    <result column="product_pack_name" jdbcType="VARCHAR" property="productPackName" />
    <result column="payment_method" jdbcType="TINYINT" property="paymentMethod" />
    <result column="payment_cycle" jdbcType="TINYINT" property="paymentCycle" />
    <result column="start_date" jdbcType="DATE" property="startDate" />
    <result column="end_date" jdbcType="DATE" property="endDate" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="amount_per_vehicle" jdbcType="DECIMAL" property="amountPerVehicle" />
    <result column="vehicle_num" jdbcType="INTEGER" property="vehicleNum" />
    <result column="first_payment_date" jdbcType="DATE" property="firstPaymentDate" />
    <result column="sign_date" jdbcType="DATE" property="signDate" />
    <result column="apply_user_id" jdbcType="INTEGER" property="applyUserId" />
    <result column="apply_user_name" jdbcType="VARCHAR" property="applyUserName" />
    <result column="approved_time" jdbcType="TIMESTAMP" property="approvedTime" />
    <result column="contract_address" jdbcType="VARCHAR" property="contractAddress" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="maintain_id" jdbcType="VARCHAR" property="maintainId" />
    <result column="maintain_name" jdbcType="VARCHAR" property="maintainName" />
    <result column="contract_url" jdbcType="VARCHAR" property="contractUrl" />
    <result column="struct_code" jdbcType="VARCHAR" property="structCode" />
    <result column="struct_name" jdbcType="VARCHAR" property="structName" />
    <result column="contract_confirm_return_time" jdbcType="TIMESTAMP" property="contractConfirmReturnTime" />
    <result column="apply_id" jdbcType="INTEGER" property="applyId" />
    <result column="apply_start_time" jdbcType="TIMESTAMP" property="applyStartTime" />
    <result column="customer_id" jdbcType="INTEGER" property="customerId" />
    <result column="customer_code" jdbcType="VARCHAR" property="customerCode" />
    <result column="sign_sales_id" jdbcType="VARCHAR" property="signSalesId" />
    <result column="sign_sales_name" jdbcType="VARCHAR" property="signSalesName" />
    <result column="audit_status" jdbcType="INTEGER" property="auditStatus" />
    <result column="contract_sign_status" jdbcType="INTEGER" property="contractSignStatus" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="create_id" jdbcType="VARCHAR" property="createId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="update_id" jdbcType="VARCHAR" property="updateId" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="profit_share_flag" jdbcType="TINYINT" property="profitShareFlag" />
    <result column="supplement_remark" jdbcType="VARCHAR" property="supplementRemark" />
    <result column="admin_name" jdbcType="VARCHAR" property="adminName" />
    <result column="admin_phone" jdbcType="VARCHAR" property="adminPhone" />
    <result column="admin_email" jdbcType="VARCHAR" property="adminEmail" />
    <result column="charge_item" jdbcType="VARCHAR" property="chargeItem" />
    <result column="invoice_user_id" jdbcType="INTEGER" property="invoiceUserId" />
    <result column="invoice_user_name" jdbcType="VARCHAR" property="invoiceUserName" />
    <result column="contract_total_count" jdbcType="INTEGER" property="contractTotalCount" />
    <result column="contract_each_count" jdbcType="INTEGER" property="contractEachCount" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, contract_id, contract_code, contract_title, contract_type, standard_contract, 
    finance_struct_code, finance_struct_name, software_trial_period, trial_period_sdate, 
    trial_period_edate, supplier_code, supplier_name, viability, cz_contract_code, tax_identification_number, 
    address, phone, bank_name, bank_account, invoice_type, invoice_category, remark, 
    contract_status, actual_sign_date, account_use_method, bind_contract_code, product_pack_code, 
    product_pack_name, payment_method, payment_cycle, start_date, end_date, total_amount, 
    amount_per_vehicle, vehicle_num, first_payment_date, sign_date, apply_user_id, apply_user_name, 
    approved_time, contract_address, create_time, update_time, customer_name, maintain_id, 
    maintain_name, contract_url, struct_code, struct_name, contract_confirm_return_time, 
    apply_id, apply_start_time, customer_id, customer_code, sign_sales_id, sign_sales_name, 
    audit_status, contract_sign_status, company_id, company_name, create_id, create_name, 
    update_id, update_name, profit_share_flag, supplement_remark, admin_name, admin_phone, 
    admin_email, charge_item, invoice_user_id, invoice_user_name, contract_total_count, 
    contract_each_count
  </sql>
  <select id="selectByExample" parameterType="com.izu.business.entity.ContractAuthAccountExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from contract_auth_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from contract_auth_account
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from contract_auth_account
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.business.entity.ContractAuthAccount">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into contract_auth_account (contract_id, contract_code, contract_title, 
      contract_type, standard_contract, finance_struct_code, 
      finance_struct_name, software_trial_period, 
      trial_period_sdate, trial_period_edate, 
      supplier_code, supplier_name, viability, 
      cz_contract_code, tax_identification_number, 
      address, phone, bank_name, 
      bank_account, invoice_type, invoice_category, 
      remark, contract_status, actual_sign_date, 
      account_use_method, bind_contract_code, product_pack_code, 
      product_pack_name, payment_method, payment_cycle, 
      start_date, end_date, total_amount, 
      amount_per_vehicle, vehicle_num, first_payment_date, 
      sign_date, apply_user_id, apply_user_name, 
      approved_time, contract_address, create_time, 
      update_time, customer_name, maintain_id, 
      maintain_name, contract_url, struct_code, 
      struct_name, contract_confirm_return_time, 
      apply_id, apply_start_time, customer_id, 
      customer_code, sign_sales_id, sign_sales_name, 
      audit_status, contract_sign_status, company_id, 
      company_name, create_id, create_name, 
      update_id, update_name, profit_share_flag, 
      supplement_remark, admin_name, admin_phone, 
      admin_email, charge_item, invoice_user_id, 
      invoice_user_name, contract_total_count, contract_each_count
      )
    values (#{contractId,jdbcType=INTEGER}, #{contractCode,jdbcType=VARCHAR}, #{contractTitle,jdbcType=VARCHAR}, 
      #{contractType,jdbcType=TINYINT}, #{standardContract,jdbcType=INTEGER}, #{financeStructCode,jdbcType=VARCHAR}, 
      #{financeStructName,jdbcType=VARCHAR}, #{softwareTrialPeriod,jdbcType=TINYINT}, 
      #{trialPeriodSdate,jdbcType=TIMESTAMP}, #{trialPeriodEdate,jdbcType=TIMESTAMP}, 
      #{supplierCode,jdbcType=VARCHAR}, #{supplierName,jdbcType=VARCHAR}, #{viability,jdbcType=INTEGER}, 
      #{czContractCode,jdbcType=VARCHAR}, #{taxIdentificationNumber,jdbcType=VARCHAR}, 
      #{address,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR}, #{bankName,jdbcType=VARCHAR}, 
      #{bankAccount,jdbcType=VARCHAR}, #{invoiceType,jdbcType=TINYINT}, #{invoiceCategory,jdbcType=TINYINT}, 
      #{remark,jdbcType=VARCHAR}, #{contractStatus,jdbcType=INTEGER}, #{actualSignDate,jdbcType=TIMESTAMP}, 
      #{accountUseMethod,jdbcType=TINYINT}, #{bindContractCode,jdbcType=VARCHAR}, #{productPackCode,jdbcType=VARCHAR}, 
      #{productPackName,jdbcType=VARCHAR}, #{paymentMethod,jdbcType=TINYINT}, #{paymentCycle,jdbcType=TINYINT}, 
      #{startDate,jdbcType=DATE}, #{endDate,jdbcType=DATE}, #{totalAmount,jdbcType=DECIMAL}, 
      #{amountPerVehicle,jdbcType=DECIMAL}, #{vehicleNum,jdbcType=INTEGER}, #{firstPaymentDate,jdbcType=DATE}, 
      #{signDate,jdbcType=DATE}, #{applyUserId,jdbcType=INTEGER}, #{applyUserName,jdbcType=VARCHAR}, 
      #{approvedTime,jdbcType=TIMESTAMP}, #{contractAddress,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{customerName,jdbcType=VARCHAR}, #{maintainId,jdbcType=VARCHAR}, 
      #{maintainName,jdbcType=VARCHAR}, #{contractUrl,jdbcType=VARCHAR}, #{structCode,jdbcType=VARCHAR}, 
      #{structName,jdbcType=VARCHAR}, #{contractConfirmReturnTime,jdbcType=TIMESTAMP}, 
      #{applyId,jdbcType=INTEGER}, #{applyStartTime,jdbcType=TIMESTAMP}, #{customerId,jdbcType=INTEGER}, 
      #{customerCode,jdbcType=VARCHAR}, #{signSalesId,jdbcType=VARCHAR}, #{signSalesName,jdbcType=VARCHAR}, 
      #{auditStatus,jdbcType=INTEGER}, #{contractSignStatus,jdbcType=INTEGER}, #{companyId,jdbcType=VARCHAR}, 
      #{companyName,jdbcType=VARCHAR}, #{createId,jdbcType=VARCHAR}, #{createName,jdbcType=VARCHAR}, 
      #{updateId,jdbcType=VARCHAR}, #{updateName,jdbcType=VARCHAR}, #{profitShareFlag,jdbcType=TINYINT}, 
      #{supplementRemark,jdbcType=VARCHAR}, #{adminName,jdbcType=VARCHAR}, #{adminPhone,jdbcType=VARCHAR}, 
      #{adminEmail,jdbcType=VARCHAR}, #{chargeItem,jdbcType=VARCHAR}, #{invoiceUserId,jdbcType=INTEGER}, 
      #{invoiceUserName,jdbcType=VARCHAR}, #{contractTotalCount,jdbcType=INTEGER}, #{contractEachCount,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.business.entity.ContractAuthAccount">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into contract_auth_account
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="contractId != null">
        contract_id,
      </if>
      <if test="contractCode != null">
        contract_code,
      </if>
      <if test="contractTitle != null">
        contract_title,
      </if>
      <if test="contractType != null">
        contract_type,
      </if>
      <if test="standardContract != null">
        standard_contract,
      </if>
      <if test="financeStructCode != null">
        finance_struct_code,
      </if>
      <if test="financeStructName != null">
        finance_struct_name,
      </if>
      <if test="softwareTrialPeriod != null">
        software_trial_period,
      </if>
      <if test="trialPeriodSdate != null">
        trial_period_sdate,
      </if>
      <if test="trialPeriodEdate != null">
        trial_period_edate,
      </if>
      <if test="supplierCode != null">
        supplier_code,
      </if>
      <if test="supplierName != null">
        supplier_name,
      </if>
      <if test="viability != null">
        viability,
      </if>
      <if test="czContractCode != null">
        cz_contract_code,
      </if>
      <if test="taxIdentificationNumber != null">
        tax_identification_number,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="bankName != null">
        bank_name,
      </if>
      <if test="bankAccount != null">
        bank_account,
      </if>
      <if test="invoiceType != null">
        invoice_type,
      </if>
      <if test="invoiceCategory != null">
        invoice_category,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="contractStatus != null">
        contract_status,
      </if>
      <if test="actualSignDate != null">
        actual_sign_date,
      </if>
      <if test="accountUseMethod != null">
        account_use_method,
      </if>
      <if test="bindContractCode != null">
        bind_contract_code,
      </if>
      <if test="productPackCode != null">
        product_pack_code,
      </if>
      <if test="productPackName != null">
        product_pack_name,
      </if>
      <if test="paymentMethod != null">
        payment_method,
      </if>
      <if test="paymentCycle != null">
        payment_cycle,
      </if>
      <if test="startDate != null">
        start_date,
      </if>
      <if test="endDate != null">
        end_date,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="amountPerVehicle != null">
        amount_per_vehicle,
      </if>
      <if test="vehicleNum != null">
        vehicle_num,
      </if>
      <if test="firstPaymentDate != null">
        first_payment_date,
      </if>
      <if test="signDate != null">
        sign_date,
      </if>
      <if test="applyUserId != null">
        apply_user_id,
      </if>
      <if test="applyUserName != null">
        apply_user_name,
      </if>
      <if test="approvedTime != null">
        approved_time,
      </if>
      <if test="contractAddress != null">
        contract_address,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="maintainId != null">
        maintain_id,
      </if>
      <if test="maintainName != null">
        maintain_name,
      </if>
      <if test="contractUrl != null">
        contract_url,
      </if>
      <if test="structCode != null">
        struct_code,
      </if>
      <if test="structName != null">
        struct_name,
      </if>
      <if test="contractConfirmReturnTime != null">
        contract_confirm_return_time,
      </if>
      <if test="applyId != null">
        apply_id,
      </if>
      <if test="applyStartTime != null">
        apply_start_time,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="customerCode != null">
        customer_code,
      </if>
      <if test="signSalesId != null">
        sign_sales_id,
      </if>
      <if test="signSalesName != null">
        sign_sales_name,
      </if>
      <if test="auditStatus != null">
        audit_status,
      </if>
      <if test="contractSignStatus != null">
        contract_sign_status,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="createId != null">
        create_id,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="updateId != null">
        update_id,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="profitShareFlag != null">
        profit_share_flag,
      </if>
      <if test="supplementRemark != null">
        supplement_remark,
      </if>
      <if test="adminName != null">
        admin_name,
      </if>
      <if test="adminPhone != null">
        admin_phone,
      </if>
      <if test="adminEmail != null">
        admin_email,
      </if>
      <if test="chargeItem != null">
        charge_item,
      </if>
      <if test="invoiceUserId != null">
        invoice_user_id,
      </if>
      <if test="invoiceUserName != null">
        invoice_user_name,
      </if>
      <if test="contractTotalCount != null">
        contract_total_count,
      </if>
      <if test="contractEachCount != null">
        contract_each_count,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="contractId != null">
        #{contractId,jdbcType=INTEGER},
      </if>
      <if test="contractCode != null">
        #{contractCode,jdbcType=VARCHAR},
      </if>
      <if test="contractTitle != null">
        #{contractTitle,jdbcType=VARCHAR},
      </if>
      <if test="contractType != null">
        #{contractType,jdbcType=TINYINT},
      </if>
      <if test="standardContract != null">
        #{standardContract,jdbcType=INTEGER},
      </if>
      <if test="financeStructCode != null">
        #{financeStructCode,jdbcType=VARCHAR},
      </if>
      <if test="financeStructName != null">
        #{financeStructName,jdbcType=VARCHAR},
      </if>
      <if test="softwareTrialPeriod != null">
        #{softwareTrialPeriod,jdbcType=TINYINT},
      </if>
      <if test="trialPeriodSdate != null">
        #{trialPeriodSdate,jdbcType=TIMESTAMP},
      </if>
      <if test="trialPeriodEdate != null">
        #{trialPeriodEdate,jdbcType=TIMESTAMP},
      </if>
      <if test="supplierCode != null">
        #{supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="viability != null">
        #{viability,jdbcType=INTEGER},
      </if>
      <if test="czContractCode != null">
        #{czContractCode,jdbcType=VARCHAR},
      </if>
      <if test="taxIdentificationNumber != null">
        #{taxIdentificationNumber,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankAccount != null">
        #{bankAccount,jdbcType=VARCHAR},
      </if>
      <if test="invoiceType != null">
        #{invoiceType,jdbcType=TINYINT},
      </if>
      <if test="invoiceCategory != null">
        #{invoiceCategory,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="contractStatus != null">
        #{contractStatus,jdbcType=INTEGER},
      </if>
      <if test="actualSignDate != null">
        #{actualSignDate,jdbcType=TIMESTAMP},
      </if>
      <if test="accountUseMethod != null">
        #{accountUseMethod,jdbcType=TINYINT},
      </if>
      <if test="bindContractCode != null">
        #{bindContractCode,jdbcType=VARCHAR},
      </if>
      <if test="productPackCode != null">
        #{productPackCode,jdbcType=VARCHAR},
      </if>
      <if test="productPackName != null">
        #{productPackName,jdbcType=VARCHAR},
      </if>
      <if test="paymentMethod != null">
        #{paymentMethod,jdbcType=TINYINT},
      </if>
      <if test="paymentCycle != null">
        #{paymentCycle,jdbcType=TINYINT},
      </if>
      <if test="startDate != null">
        #{startDate,jdbcType=DATE},
      </if>
      <if test="endDate != null">
        #{endDate,jdbcType=DATE},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="amountPerVehicle != null">
        #{amountPerVehicle,jdbcType=DECIMAL},
      </if>
      <if test="vehicleNum != null">
        #{vehicleNum,jdbcType=INTEGER},
      </if>
      <if test="firstPaymentDate != null">
        #{firstPaymentDate,jdbcType=DATE},
      </if>
      <if test="signDate != null">
        #{signDate,jdbcType=DATE},
      </if>
      <if test="applyUserId != null">
        #{applyUserId,jdbcType=INTEGER},
      </if>
      <if test="applyUserName != null">
        #{applyUserName,jdbcType=VARCHAR},
      </if>
      <if test="approvedTime != null">
        #{approvedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contractAddress != null">
        #{contractAddress,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="maintainId != null">
        #{maintainId,jdbcType=VARCHAR},
      </if>
      <if test="maintainName != null">
        #{maintainName,jdbcType=VARCHAR},
      </if>
      <if test="contractUrl != null">
        #{contractUrl,jdbcType=VARCHAR},
      </if>
      <if test="structCode != null">
        #{structCode,jdbcType=VARCHAR},
      </if>
      <if test="structName != null">
        #{structName,jdbcType=VARCHAR},
      </if>
      <if test="contractConfirmReturnTime != null">
        #{contractConfirmReturnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="applyId != null">
        #{applyId,jdbcType=INTEGER},
      </if>
      <if test="applyStartTime != null">
        #{applyStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=INTEGER},
      </if>
      <if test="customerCode != null">
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="signSalesId != null">
        #{signSalesId,jdbcType=VARCHAR},
      </if>
      <if test="signSalesName != null">
        #{signSalesName,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="contractSignStatus != null">
        #{contractSignStatus,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="createId != null">
        #{createId,jdbcType=VARCHAR},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="updateId != null">
        #{updateId,jdbcType=VARCHAR},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="profitShareFlag != null">
        #{profitShareFlag,jdbcType=TINYINT},
      </if>
      <if test="supplementRemark != null">
        #{supplementRemark,jdbcType=VARCHAR},
      </if>
      <if test="adminName != null">
        #{adminName,jdbcType=VARCHAR},
      </if>
      <if test="adminPhone != null">
        #{adminPhone,jdbcType=VARCHAR},
      </if>
      <if test="adminEmail != null">
        #{adminEmail,jdbcType=VARCHAR},
      </if>
      <if test="chargeItem != null">
        #{chargeItem,jdbcType=VARCHAR},
      </if>
      <if test="invoiceUserId != null">
        #{invoiceUserId,jdbcType=INTEGER},
      </if>
      <if test="invoiceUserName != null">
        #{invoiceUserName,jdbcType=VARCHAR},
      </if>
      <if test="contractTotalCount != null">
        #{contractTotalCount,jdbcType=INTEGER},
      </if>
      <if test="contractEachCount != null">
        #{contractEachCount,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.business.entity.ContractAuthAccountExample" resultType="java.lang.Long">
    select count(*) from contract_auth_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update contract_auth_account
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=INTEGER},
      </if>
      <if test="row.contractId != null">
        contract_id = #{row.contractId,jdbcType=INTEGER},
      </if>
      <if test="row.contractCode != null">
        contract_code = #{row.contractCode,jdbcType=VARCHAR},
      </if>
      <if test="row.contractTitle != null">
        contract_title = #{row.contractTitle,jdbcType=VARCHAR},
      </if>
      <if test="row.contractType != null">
        contract_type = #{row.contractType,jdbcType=TINYINT},
      </if>
      <if test="row.standardContract != null">
        standard_contract = #{row.standardContract,jdbcType=INTEGER},
      </if>
      <if test="row.financeStructCode != null">
        finance_struct_code = #{row.financeStructCode,jdbcType=VARCHAR},
      </if>
      <if test="row.financeStructName != null">
        finance_struct_name = #{row.financeStructName,jdbcType=VARCHAR},
      </if>
      <if test="row.softwareTrialPeriod != null">
        software_trial_period = #{row.softwareTrialPeriod,jdbcType=TINYINT},
      </if>
      <if test="row.trialPeriodSdate != null">
        trial_period_sdate = #{row.trialPeriodSdate,jdbcType=TIMESTAMP},
      </if>
      <if test="row.trialPeriodEdate != null">
        trial_period_edate = #{row.trialPeriodEdate,jdbcType=TIMESTAMP},
      </if>
      <if test="row.supplierCode != null">
        supplier_code = #{row.supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="row.supplierName != null">
        supplier_name = #{row.supplierName,jdbcType=VARCHAR},
      </if>
      <if test="row.viability != null">
        viability = #{row.viability,jdbcType=INTEGER},
      </if>
      <if test="row.czContractCode != null">
        cz_contract_code = #{row.czContractCode,jdbcType=VARCHAR},
      </if>
      <if test="row.taxIdentificationNumber != null">
        tax_identification_number = #{row.taxIdentificationNumber,jdbcType=VARCHAR},
      </if>
      <if test="row.address != null">
        address = #{row.address,jdbcType=VARCHAR},
      </if>
      <if test="row.phone != null">
        phone = #{row.phone,jdbcType=VARCHAR},
      </if>
      <if test="row.bankName != null">
        bank_name = #{row.bankName,jdbcType=VARCHAR},
      </if>
      <if test="row.bankAccount != null">
        bank_account = #{row.bankAccount,jdbcType=VARCHAR},
      </if>
      <if test="row.invoiceType != null">
        invoice_type = #{row.invoiceType,jdbcType=TINYINT},
      </if>
      <if test="row.invoiceCategory != null">
        invoice_category = #{row.invoiceCategory,jdbcType=TINYINT},
      </if>
      <if test="row.remark != null">
        remark = #{row.remark,jdbcType=VARCHAR},
      </if>
      <if test="row.contractStatus != null">
        contract_status = #{row.contractStatus,jdbcType=INTEGER},
      </if>
      <if test="row.actualSignDate != null">
        actual_sign_date = #{row.actualSignDate,jdbcType=TIMESTAMP},
      </if>
      <if test="row.accountUseMethod != null">
        account_use_method = #{row.accountUseMethod,jdbcType=TINYINT},
      </if>
      <if test="row.bindContractCode != null">
        bind_contract_code = #{row.bindContractCode,jdbcType=VARCHAR},
      </if>
      <if test="row.productPackCode != null">
        product_pack_code = #{row.productPackCode,jdbcType=VARCHAR},
      </if>
      <if test="row.productPackName != null">
        product_pack_name = #{row.productPackName,jdbcType=VARCHAR},
      </if>
      <if test="row.paymentMethod != null">
        payment_method = #{row.paymentMethod,jdbcType=TINYINT},
      </if>
      <if test="row.paymentCycle != null">
        payment_cycle = #{row.paymentCycle,jdbcType=TINYINT},
      </if>
      <if test="row.startDate != null">
        start_date = #{row.startDate,jdbcType=DATE},
      </if>
      <if test="row.endDate != null">
        end_date = #{row.endDate,jdbcType=DATE},
      </if>
      <if test="row.totalAmount != null">
        total_amount = #{row.totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.amountPerVehicle != null">
        amount_per_vehicle = #{row.amountPerVehicle,jdbcType=DECIMAL},
      </if>
      <if test="row.vehicleNum != null">
        vehicle_num = #{row.vehicleNum,jdbcType=INTEGER},
      </if>
      <if test="row.firstPaymentDate != null">
        first_payment_date = #{row.firstPaymentDate,jdbcType=DATE},
      </if>
      <if test="row.signDate != null">
        sign_date = #{row.signDate,jdbcType=DATE},
      </if>
      <if test="row.applyUserId != null">
        apply_user_id = #{row.applyUserId,jdbcType=INTEGER},
      </if>
      <if test="row.applyUserName != null">
        apply_user_name = #{row.applyUserName,jdbcType=VARCHAR},
      </if>
      <if test="row.approvedTime != null">
        approved_time = #{row.approvedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.contractAddress != null">
        contract_address = #{row.contractAddress,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.customerName != null">
        customer_name = #{row.customerName,jdbcType=VARCHAR},
      </if>
      <if test="row.maintainId != null">
        maintain_id = #{row.maintainId,jdbcType=VARCHAR},
      </if>
      <if test="row.maintainName != null">
        maintain_name = #{row.maintainName,jdbcType=VARCHAR},
      </if>
      <if test="row.contractUrl != null">
        contract_url = #{row.contractUrl,jdbcType=VARCHAR},
      </if>
      <if test="row.structCode != null">
        struct_code = #{row.structCode,jdbcType=VARCHAR},
      </if>
      <if test="row.structName != null">
        struct_name = #{row.structName,jdbcType=VARCHAR},
      </if>
      <if test="row.contractConfirmReturnTime != null">
        contract_confirm_return_time = #{row.contractConfirmReturnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.applyId != null">
        apply_id = #{row.applyId,jdbcType=INTEGER},
      </if>
      <if test="row.applyStartTime != null">
        apply_start_time = #{row.applyStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.customerId != null">
        customer_id = #{row.customerId,jdbcType=INTEGER},
      </if>
      <if test="row.customerCode != null">
        customer_code = #{row.customerCode,jdbcType=VARCHAR},
      </if>
      <if test="row.signSalesId != null">
        sign_sales_id = #{row.signSalesId,jdbcType=VARCHAR},
      </if>
      <if test="row.signSalesName != null">
        sign_sales_name = #{row.signSalesName,jdbcType=VARCHAR},
      </if>
      <if test="row.auditStatus != null">
        audit_status = #{row.auditStatus,jdbcType=INTEGER},
      </if>
      <if test="row.contractSignStatus != null">
        contract_sign_status = #{row.contractSignStatus,jdbcType=INTEGER},
      </if>
      <if test="row.companyId != null">
        company_id = #{row.companyId,jdbcType=VARCHAR},
      </if>
      <if test="row.companyName != null">
        company_name = #{row.companyName,jdbcType=VARCHAR},
      </if>
      <if test="row.createId != null">
        create_id = #{row.createId,jdbcType=VARCHAR},
      </if>
      <if test="row.createName != null">
        create_name = #{row.createName,jdbcType=VARCHAR},
      </if>
      <if test="row.updateId != null">
        update_id = #{row.updateId,jdbcType=VARCHAR},
      </if>
      <if test="row.updateName != null">
        update_name = #{row.updateName,jdbcType=VARCHAR},
      </if>
      <if test="row.profitShareFlag != null">
        profit_share_flag = #{row.profitShareFlag,jdbcType=TINYINT},
      </if>
      <if test="row.supplementRemark != null">
        supplement_remark = #{row.supplementRemark,jdbcType=VARCHAR},
      </if>
      <if test="row.adminName != null">
        admin_name = #{row.adminName,jdbcType=VARCHAR},
      </if>
      <if test="row.adminPhone != null">
        admin_phone = #{row.adminPhone,jdbcType=VARCHAR},
      </if>
      <if test="row.adminEmail != null">
        admin_email = #{row.adminEmail,jdbcType=VARCHAR},
      </if>
      <if test="row.chargeItem != null">
        charge_item = #{row.chargeItem,jdbcType=VARCHAR},
      </if>
      <if test="row.invoiceUserId != null">
        invoice_user_id = #{row.invoiceUserId,jdbcType=INTEGER},
      </if>
      <if test="row.invoiceUserName != null">
        invoice_user_name = #{row.invoiceUserName,jdbcType=VARCHAR},
      </if>
      <if test="row.contractTotalCount != null">
        contract_total_count = #{row.contractTotalCount,jdbcType=INTEGER},
      </if>
      <if test="row.contractEachCount != null">
        contract_each_count = #{row.contractEachCount,jdbcType=INTEGER},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update contract_auth_account
    set id = #{row.id,jdbcType=INTEGER},
      contract_id = #{row.contractId,jdbcType=INTEGER},
      contract_code = #{row.contractCode,jdbcType=VARCHAR},
      contract_title = #{row.contractTitle,jdbcType=VARCHAR},
      contract_type = #{row.contractType,jdbcType=TINYINT},
      standard_contract = #{row.standardContract,jdbcType=INTEGER},
      finance_struct_code = #{row.financeStructCode,jdbcType=VARCHAR},
      finance_struct_name = #{row.financeStructName,jdbcType=VARCHAR},
      software_trial_period = #{row.softwareTrialPeriod,jdbcType=TINYINT},
      trial_period_sdate = #{row.trialPeriodSdate,jdbcType=TIMESTAMP},
      trial_period_edate = #{row.trialPeriodEdate,jdbcType=TIMESTAMP},
      supplier_code = #{row.supplierCode,jdbcType=VARCHAR},
      supplier_name = #{row.supplierName,jdbcType=VARCHAR},
      viability = #{row.viability,jdbcType=INTEGER},
      cz_contract_code = #{row.czContractCode,jdbcType=VARCHAR},
      tax_identification_number = #{row.taxIdentificationNumber,jdbcType=VARCHAR},
      address = #{row.address,jdbcType=VARCHAR},
      phone = #{row.phone,jdbcType=VARCHAR},
      bank_name = #{row.bankName,jdbcType=VARCHAR},
      bank_account = #{row.bankAccount,jdbcType=VARCHAR},
      invoice_type = #{row.invoiceType,jdbcType=TINYINT},
      invoice_category = #{row.invoiceCategory,jdbcType=TINYINT},
      remark = #{row.remark,jdbcType=VARCHAR},
      contract_status = #{row.contractStatus,jdbcType=INTEGER},
      actual_sign_date = #{row.actualSignDate,jdbcType=TIMESTAMP},
      account_use_method = #{row.accountUseMethod,jdbcType=TINYINT},
      bind_contract_code = #{row.bindContractCode,jdbcType=VARCHAR},
      product_pack_code = #{row.productPackCode,jdbcType=VARCHAR},
      product_pack_name = #{row.productPackName,jdbcType=VARCHAR},
      payment_method = #{row.paymentMethod,jdbcType=TINYINT},
      payment_cycle = #{row.paymentCycle,jdbcType=TINYINT},
      start_date = #{row.startDate,jdbcType=DATE},
      end_date = #{row.endDate,jdbcType=DATE},
      total_amount = #{row.totalAmount,jdbcType=DECIMAL},
      amount_per_vehicle = #{row.amountPerVehicle,jdbcType=DECIMAL},
      vehicle_num = #{row.vehicleNum,jdbcType=INTEGER},
      first_payment_date = #{row.firstPaymentDate,jdbcType=DATE},
      sign_date = #{row.signDate,jdbcType=DATE},
      apply_user_id = #{row.applyUserId,jdbcType=INTEGER},
      apply_user_name = #{row.applyUserName,jdbcType=VARCHAR},
      approved_time = #{row.approvedTime,jdbcType=TIMESTAMP},
      contract_address = #{row.contractAddress,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      customer_name = #{row.customerName,jdbcType=VARCHAR},
      maintain_id = #{row.maintainId,jdbcType=VARCHAR},
      maintain_name = #{row.maintainName,jdbcType=VARCHAR},
      contract_url = #{row.contractUrl,jdbcType=VARCHAR},
      struct_code = #{row.structCode,jdbcType=VARCHAR},
      struct_name = #{row.structName,jdbcType=VARCHAR},
      contract_confirm_return_time = #{row.contractConfirmReturnTime,jdbcType=TIMESTAMP},
      apply_id = #{row.applyId,jdbcType=INTEGER},
      apply_start_time = #{row.applyStartTime,jdbcType=TIMESTAMP},
      customer_id = #{row.customerId,jdbcType=INTEGER},
      customer_code = #{row.customerCode,jdbcType=VARCHAR},
      sign_sales_id = #{row.signSalesId,jdbcType=VARCHAR},
      sign_sales_name = #{row.signSalesName,jdbcType=VARCHAR},
      audit_status = #{row.auditStatus,jdbcType=INTEGER},
      contract_sign_status = #{row.contractSignStatus,jdbcType=INTEGER},
      company_id = #{row.companyId,jdbcType=VARCHAR},
      company_name = #{row.companyName,jdbcType=VARCHAR},
      create_id = #{row.createId,jdbcType=VARCHAR},
      create_name = #{row.createName,jdbcType=VARCHAR},
      update_id = #{row.updateId,jdbcType=VARCHAR},
      update_name = #{row.updateName,jdbcType=VARCHAR},
      profit_share_flag = #{row.profitShareFlag,jdbcType=TINYINT},
      supplement_remark = #{row.supplementRemark,jdbcType=VARCHAR},
      admin_name = #{row.adminName,jdbcType=VARCHAR},
      admin_phone = #{row.adminPhone,jdbcType=VARCHAR},
      admin_email = #{row.adminEmail,jdbcType=VARCHAR},
      charge_item = #{row.chargeItem,jdbcType=VARCHAR},
      invoice_user_id = #{row.invoiceUserId,jdbcType=INTEGER},
      invoice_user_name = #{row.invoiceUserName,jdbcType=VARCHAR},
      contract_total_count = #{row.contractTotalCount,jdbcType=INTEGER},
      contract_each_count = #{row.contractEachCount,jdbcType=INTEGER}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.business.entity.ContractAuthAccount">
    update contract_auth_account
    <set>
      <if test="contractId != null">
        contract_id = #{contractId,jdbcType=INTEGER},
      </if>
      <if test="contractCode != null">
        contract_code = #{contractCode,jdbcType=VARCHAR},
      </if>
      <if test="contractTitle != null">
        contract_title = #{contractTitle,jdbcType=VARCHAR},
      </if>
      <if test="contractType != null">
        contract_type = #{contractType,jdbcType=TINYINT},
      </if>
      <if test="standardContract != null">
        standard_contract = #{standardContract,jdbcType=INTEGER},
      </if>
      <if test="financeStructCode != null">
        finance_struct_code = #{financeStructCode,jdbcType=VARCHAR},
      </if>
      <if test="financeStructName != null">
        finance_struct_name = #{financeStructName,jdbcType=VARCHAR},
      </if>
      <if test="softwareTrialPeriod != null">
        software_trial_period = #{softwareTrialPeriod,jdbcType=TINYINT},
      </if>
      <if test="trialPeriodSdate != null">
        trial_period_sdate = #{trialPeriodSdate,jdbcType=TIMESTAMP},
      </if>
      <if test="trialPeriodEdate != null">
        trial_period_edate = #{trialPeriodEdate,jdbcType=TIMESTAMP},
      </if>
      <if test="supplierCode != null">
        supplier_code = #{supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        supplier_name = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="viability != null">
        viability = #{viability,jdbcType=INTEGER},
      </if>
      <if test="czContractCode != null">
        cz_contract_code = #{czContractCode,jdbcType=VARCHAR},
      </if>
      <if test="taxIdentificationNumber != null">
        tax_identification_number = #{taxIdentificationNumber,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        bank_name = #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankAccount != null">
        bank_account = #{bankAccount,jdbcType=VARCHAR},
      </if>
      <if test="invoiceType != null">
        invoice_type = #{invoiceType,jdbcType=TINYINT},
      </if>
      <if test="invoiceCategory != null">
        invoice_category = #{invoiceCategory,jdbcType=TINYINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="contractStatus != null">
        contract_status = #{contractStatus,jdbcType=INTEGER},
      </if>
      <if test="actualSignDate != null">
        actual_sign_date = #{actualSignDate,jdbcType=TIMESTAMP},
      </if>
      <if test="accountUseMethod != null">
        account_use_method = #{accountUseMethod,jdbcType=TINYINT},
      </if>
      <if test="bindContractCode != null">
        bind_contract_code = #{bindContractCode,jdbcType=VARCHAR},
      </if>
      <if test="productPackCode != null">
        product_pack_code = #{productPackCode,jdbcType=VARCHAR},
      </if>
      <if test="productPackName != null">
        product_pack_name = #{productPackName,jdbcType=VARCHAR},
      </if>
      <if test="paymentMethod != null">
        payment_method = #{paymentMethod,jdbcType=TINYINT},
      </if>
      <if test="paymentCycle != null">
        payment_cycle = #{paymentCycle,jdbcType=TINYINT},
      </if>
      <if test="startDate != null">
        start_date = #{startDate,jdbcType=DATE},
      </if>
      <if test="endDate != null">
        end_date = #{endDate,jdbcType=DATE},
      </if>
      <if test="totalAmount != null">
        total_amount = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="amountPerVehicle != null">
        amount_per_vehicle = #{amountPerVehicle,jdbcType=DECIMAL},
      </if>
      <if test="vehicleNum != null">
        vehicle_num = #{vehicleNum,jdbcType=INTEGER},
      </if>
      <if test="firstPaymentDate != null">
        first_payment_date = #{firstPaymentDate,jdbcType=DATE},
      </if>
      <if test="signDate != null">
        sign_date = #{signDate,jdbcType=DATE},
      </if>
      <if test="applyUserId != null">
        apply_user_id = #{applyUserId,jdbcType=INTEGER},
      </if>
      <if test="applyUserName != null">
        apply_user_name = #{applyUserName,jdbcType=VARCHAR},
      </if>
      <if test="approvedTime != null">
        approved_time = #{approvedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contractAddress != null">
        contract_address = #{contractAddress,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customerName != null">
        customer_name = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="maintainId != null">
        maintain_id = #{maintainId,jdbcType=VARCHAR},
      </if>
      <if test="maintainName != null">
        maintain_name = #{maintainName,jdbcType=VARCHAR},
      </if>
      <if test="contractUrl != null">
        contract_url = #{contractUrl,jdbcType=VARCHAR},
      </if>
      <if test="structCode != null">
        struct_code = #{structCode,jdbcType=VARCHAR},
      </if>
      <if test="structName != null">
        struct_name = #{structName,jdbcType=VARCHAR},
      </if>
      <if test="contractConfirmReturnTime != null">
        contract_confirm_return_time = #{contractConfirmReturnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="applyId != null">
        apply_id = #{applyId,jdbcType=INTEGER},
      </if>
      <if test="applyStartTime != null">
        apply_start_time = #{applyStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=INTEGER},
      </if>
      <if test="customerCode != null">
        customer_code = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="signSalesId != null">
        sign_sales_id = #{signSalesId,jdbcType=VARCHAR},
      </if>
      <if test="signSalesName != null">
        sign_sales_name = #{signSalesName,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        audit_status = #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="contractSignStatus != null">
        contract_sign_status = #{contractSignStatus,jdbcType=INTEGER},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="createId != null">
        create_id = #{createId,jdbcType=VARCHAR},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="updateId != null">
        update_id = #{updateId,jdbcType=VARCHAR},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="profitShareFlag != null">
        profit_share_flag = #{profitShareFlag,jdbcType=TINYINT},
      </if>
      <if test="supplementRemark != null">
        supplement_remark = #{supplementRemark,jdbcType=VARCHAR},
      </if>
      <if test="adminName != null">
        admin_name = #{adminName,jdbcType=VARCHAR},
      </if>
      <if test="adminPhone != null">
        admin_phone = #{adminPhone,jdbcType=VARCHAR},
      </if>
      <if test="adminEmail != null">
        admin_email = #{adminEmail,jdbcType=VARCHAR},
      </if>
      <if test="chargeItem != null">
        charge_item = #{chargeItem,jdbcType=VARCHAR},
      </if>
      <if test="invoiceUserId != null">
        invoice_user_id = #{invoiceUserId,jdbcType=INTEGER},
      </if>
      <if test="invoiceUserName != null">
        invoice_user_name = #{invoiceUserName,jdbcType=VARCHAR},
      </if>
      <if test="contractTotalCount != null">
        contract_total_count = #{contractTotalCount,jdbcType=INTEGER},
      </if>
      <if test="contractEachCount != null">
        contract_each_count = #{contractEachCount,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.business.entity.ContractAuthAccount">
    update contract_auth_account
    set contract_id = #{contractId,jdbcType=INTEGER},
      contract_code = #{contractCode,jdbcType=VARCHAR},
      contract_title = #{contractTitle,jdbcType=VARCHAR},
      contract_type = #{contractType,jdbcType=TINYINT},
      standard_contract = #{standardContract,jdbcType=INTEGER},
      finance_struct_code = #{financeStructCode,jdbcType=VARCHAR},
      finance_struct_name = #{financeStructName,jdbcType=VARCHAR},
      software_trial_period = #{softwareTrialPeriod,jdbcType=TINYINT},
      trial_period_sdate = #{trialPeriodSdate,jdbcType=TIMESTAMP},
      trial_period_edate = #{trialPeriodEdate,jdbcType=TIMESTAMP},
      supplier_code = #{supplierCode,jdbcType=VARCHAR},
      supplier_name = #{supplierName,jdbcType=VARCHAR},
      viability = #{viability,jdbcType=INTEGER},
      cz_contract_code = #{czContractCode,jdbcType=VARCHAR},
      tax_identification_number = #{taxIdentificationNumber,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      bank_name = #{bankName,jdbcType=VARCHAR},
      bank_account = #{bankAccount,jdbcType=VARCHAR},
      invoice_type = #{invoiceType,jdbcType=TINYINT},
      invoice_category = #{invoiceCategory,jdbcType=TINYINT},
      remark = #{remark,jdbcType=VARCHAR},
      contract_status = #{contractStatus,jdbcType=INTEGER},
      actual_sign_date = #{actualSignDate,jdbcType=TIMESTAMP},
      account_use_method = #{accountUseMethod,jdbcType=TINYINT},
      bind_contract_code = #{bindContractCode,jdbcType=VARCHAR},
      product_pack_code = #{productPackCode,jdbcType=VARCHAR},
      product_pack_name = #{productPackName,jdbcType=VARCHAR},
      payment_method = #{paymentMethod,jdbcType=TINYINT},
      payment_cycle = #{paymentCycle,jdbcType=TINYINT},
      start_date = #{startDate,jdbcType=DATE},
      end_date = #{endDate,jdbcType=DATE},
      total_amount = #{totalAmount,jdbcType=DECIMAL},
      amount_per_vehicle = #{amountPerVehicle,jdbcType=DECIMAL},
      vehicle_num = #{vehicleNum,jdbcType=INTEGER},
      first_payment_date = #{firstPaymentDate,jdbcType=DATE},
      sign_date = #{signDate,jdbcType=DATE},
      apply_user_id = #{applyUserId,jdbcType=INTEGER},
      apply_user_name = #{applyUserName,jdbcType=VARCHAR},
      approved_time = #{approvedTime,jdbcType=TIMESTAMP},
      contract_address = #{contractAddress,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      customer_name = #{customerName,jdbcType=VARCHAR},
      maintain_id = #{maintainId,jdbcType=VARCHAR},
      maintain_name = #{maintainName,jdbcType=VARCHAR},
      contract_url = #{contractUrl,jdbcType=VARCHAR},
      struct_code = #{structCode,jdbcType=VARCHAR},
      struct_name = #{structName,jdbcType=VARCHAR},
      contract_confirm_return_time = #{contractConfirmReturnTime,jdbcType=TIMESTAMP},
      apply_id = #{applyId,jdbcType=INTEGER},
      apply_start_time = #{applyStartTime,jdbcType=TIMESTAMP},
      customer_id = #{customerId,jdbcType=INTEGER},
      customer_code = #{customerCode,jdbcType=VARCHAR},
      sign_sales_id = #{signSalesId,jdbcType=VARCHAR},
      sign_sales_name = #{signSalesName,jdbcType=VARCHAR},
      audit_status = #{auditStatus,jdbcType=INTEGER},
      contract_sign_status = #{contractSignStatus,jdbcType=INTEGER},
      company_id = #{companyId,jdbcType=VARCHAR},
      company_name = #{companyName,jdbcType=VARCHAR},
      create_id = #{createId,jdbcType=VARCHAR},
      create_name = #{createName,jdbcType=VARCHAR},
      update_id = #{updateId,jdbcType=VARCHAR},
      update_name = #{updateName,jdbcType=VARCHAR},
      profit_share_flag = #{profitShareFlag,jdbcType=TINYINT},
      supplement_remark = #{supplementRemark,jdbcType=VARCHAR},
      admin_name = #{adminName,jdbcType=VARCHAR},
      admin_phone = #{adminPhone,jdbcType=VARCHAR},
      admin_email = #{adminEmail,jdbcType=VARCHAR},
      charge_item = #{chargeItem,jdbcType=VARCHAR},
      invoice_user_id = #{invoiceUserId,jdbcType=INTEGER},
      invoice_user_name = #{invoiceUserName,jdbcType=VARCHAR},
      contract_total_count = #{contractTotalCount,jdbcType=INTEGER},
      contract_each_count = #{contractEachCount,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>