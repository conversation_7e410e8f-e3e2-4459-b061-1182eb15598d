<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.StatisCollectFlowMapper">
  <resultMap id="BaseResultMap" type="com.izu.business.entity.StatisCollectFlow">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="statis_date" jdbcType="VARCHAR" property="statisDate" />
    <result column="account_city_code" jdbcType="VARCHAR" property="accountCityCode" />
    <result column="account_city_name" jdbcType="VARCHAR" property="accountCityName" />
    <result column="asset_struct_code" jdbcType="VARCHAR" property="assetStructCode" />
    <result column="asset_struct_name" jdbcType="VARCHAR" property="assetStructName" />
    <result column="income_amount" jdbcType="DECIMAL" property="incomeAmount" />
    <result column="order_amount" jdbcType="INTEGER" property="orderAmount" />
    <result column="vehicle_amount" jdbcType="INTEGER" property="vehicleAmount" />
    <result column="rent_amount" jdbcType="DECIMAL" property="rentAmount" />
    <result column="rented_rate" jdbcType="DECIMAL" property="rentedRate" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, statis_date, account_city_code, account_city_name, asset_struct_code, asset_struct_name, 
    income_amount, order_amount, vehicle_amount, rent_amount, rented_rate, create_time, 
    update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from statis_collect_flow
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from statis_collect_flow
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.business.entity.StatisCollectFlow">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into statis_collect_flow (statis_date, account_city_code, account_city_name, 
      asset_struct_code, asset_struct_name, income_amount, 
      order_amount, vehicle_amount, rent_amount, 
      rented_rate, create_time, update_time
      )
    values (#{statisDate,jdbcType=VARCHAR}, #{accountCityCode,jdbcType=VARCHAR}, #{accountCityName,jdbcType=VARCHAR}, 
      #{assetStructCode,jdbcType=VARCHAR}, #{assetStructName,jdbcType=VARCHAR}, #{incomeAmount,jdbcType=DECIMAL}, 
      #{orderAmount,jdbcType=INTEGER}, #{vehicleAmount,jdbcType=INTEGER}, #{rentAmount,jdbcType=DECIMAL}, 
      #{rentedRate,jdbcType=DECIMAL}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.business.entity.StatisCollectFlow">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into statis_collect_flow
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="statisDate != null">
        statis_date,
      </if>
      <if test="accountCityCode != null">
        account_city_code,
      </if>
      <if test="accountCityName != null">
        account_city_name,
      </if>
      <if test="assetStructCode != null">
        asset_struct_code,
      </if>
      <if test="assetStructName != null">
        asset_struct_name,
      </if>
      <if test="incomeAmount != null">
        income_amount,
      </if>
      <if test="orderAmount != null">
        order_amount,
      </if>
      <if test="vehicleAmount != null">
        vehicle_amount,
      </if>
      <if test="rentAmount != null">
        rent_amount,
      </if>
      <if test="rentedRate != null">
        rented_rate,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="statisDate != null">
        #{statisDate,jdbcType=VARCHAR},
      </if>
      <if test="accountCityCode != null">
        #{accountCityCode,jdbcType=VARCHAR},
      </if>
      <if test="accountCityName != null">
        #{accountCityName,jdbcType=VARCHAR},
      </if>
      <if test="assetStructCode != null">
        #{assetStructCode,jdbcType=VARCHAR},
      </if>
      <if test="assetStructName != null">
        #{assetStructName,jdbcType=VARCHAR},
      </if>
      <if test="incomeAmount != null">
        #{incomeAmount,jdbcType=DECIMAL},
      </if>
      <if test="orderAmount != null">
        #{orderAmount,jdbcType=INTEGER},
      </if>
      <if test="vehicleAmount != null">
        #{vehicleAmount,jdbcType=INTEGER},
      </if>
      <if test="rentAmount != null">
        #{rentAmount,jdbcType=DECIMAL},
      </if>
      <if test="rentedRate != null">
        #{rentedRate,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.business.entity.StatisCollectFlow">
    update statis_collect_flow
    <set>
      <if test="statisDate != null">
        statis_date = #{statisDate,jdbcType=VARCHAR},
      </if>
      <if test="accountCityCode != null">
        account_city_code = #{accountCityCode,jdbcType=VARCHAR},
      </if>
      <if test="accountCityName != null">
        account_city_name = #{accountCityName,jdbcType=VARCHAR},
      </if>
      <if test="assetStructCode != null">
        asset_struct_code = #{assetStructCode,jdbcType=VARCHAR},
      </if>
      <if test="assetStructName != null">
        asset_struct_name = #{assetStructName,jdbcType=VARCHAR},
      </if>
      <if test="incomeAmount != null">
        income_amount = #{incomeAmount,jdbcType=DECIMAL},
      </if>
      <if test="orderAmount != null">
        order_amount = #{orderAmount,jdbcType=INTEGER},
      </if>
      <if test="vehicleAmount != null">
        vehicle_amount = #{vehicleAmount,jdbcType=INTEGER},
      </if>
      <if test="rentAmount != null">
        rent_amount = #{rentAmount,jdbcType=DECIMAL},
      </if>
      <if test="rentedRate != null">
        rented_rate = #{rentedRate,jdbcType=DECIMAL},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.business.entity.StatisCollectFlow">
    update statis_collect_flow
    set statis_date = #{statisDate,jdbcType=VARCHAR},
      account_city_code = #{accountCityCode,jdbcType=VARCHAR},
      account_city_name = #{accountCityName,jdbcType=VARCHAR},
      asset_struct_code = #{assetStructCode,jdbcType=VARCHAR},
      asset_struct_name = #{assetStructName,jdbcType=VARCHAR},
      income_amount = #{incomeAmount,jdbcType=DECIMAL},
      order_amount = #{orderAmount,jdbcType=INTEGER},
      vehicle_amount = #{vehicleAmount,jdbcType=INTEGER},
      rent_amount = #{rentAmount,jdbcType=DECIMAL},
      rented_rate = #{rentedRate,jdbcType=DECIMAL},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>