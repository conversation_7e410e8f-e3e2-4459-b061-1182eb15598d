<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.ImAuditMessageMapper">
  <resultMap id="BaseResultMap" type="com.izu.business.entity.ImAuditMessage">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="message_id" jdbcType="VARCHAR" property="messageId" />
    <result column="from_user_code" jdbcType="VARCHAR" property="fromUserCode" />
    <result column="from_user_name" jdbcType="VARCHAR" property="fromUserName" />
    <result column="from_user_mobile" jdbcType="VARCHAR" property="fromUserMobile" />
    <result column="from_company_code" jdbcType="VARCHAR" property="fromCompanyCode" />
    <result column="to_user_code" jdbcType="VARCHAR" property="toUserCode" />
    <result column="to_user_name" jdbcType="VARCHAR" property="toUserName" />
    <result column="to_user_mobile" jdbcType="VARCHAR" property="toUserMobile" />
    <result column="to_company_code" jdbcType="VARCHAR" property="toCompanyCode" />
    <result column="message_type" jdbcType="TINYINT" property="messageType" />
    <result column="message_time" jdbcType="TIMESTAMP" property="messageTime" />
    <result column="risk_label" jdbcType="VARCHAR" property="riskLabel" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.izu.business.entity.ImAuditMessage">
    <result column="message_content" jdbcType="LONGVARCHAR" property="messageContent" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, message_id, from_user_code, from_user_name, from_user_mobile, from_company_code, 
    to_user_code, to_user_name, to_user_mobile, to_company_code, message_type, message_time, 
    risk_label, create_time, update_time
  </sql>
  <sql id="Blob_Column_List">
    message_content
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.izu.business.entity.ImAuditMessageExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from t_im_audit_message
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.izu.business.entity.ImAuditMessageExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_im_audit_message
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from t_im_audit_message
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from t_im_audit_message
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.business.entity.ImAuditMessageExample">
    delete from t_im_audit_message
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.business.entity.ImAuditMessage">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into t_im_audit_message (message_id, from_user_code, from_user_name, 
      from_user_mobile, from_company_code, to_user_code, 
      to_user_name, to_user_mobile, to_company_code, 
      message_type, message_time, risk_label, 
      create_time, update_time, message_content
      )
    values (#{messageId,jdbcType=VARCHAR}, #{fromUserCode,jdbcType=VARCHAR}, #{fromUserName,jdbcType=VARCHAR}, 
      #{fromUserMobile,jdbcType=VARCHAR}, #{fromCompanyCode,jdbcType=VARCHAR}, #{toUserCode,jdbcType=VARCHAR}, 
      #{toUserName,jdbcType=VARCHAR}, #{toUserMobile,jdbcType=VARCHAR}, #{toCompanyCode,jdbcType=VARCHAR}, 
      #{messageType,jdbcType=TINYINT}, #{messageTime,jdbcType=TIMESTAMP}, #{riskLabel,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{messageContent,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.business.entity.ImAuditMessage">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into t_im_audit_message
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="messageId != null">
        message_id,
      </if>
      <if test="fromUserCode != null">
        from_user_code,
      </if>
      <if test="fromUserName != null">
        from_user_name,
      </if>
      <if test="fromUserMobile != null">
        from_user_mobile,
      </if>
      <if test="fromCompanyCode != null">
        from_company_code,
      </if>
      <if test="toUserCode != null">
        to_user_code,
      </if>
      <if test="toUserName != null">
        to_user_name,
      </if>
      <if test="toUserMobile != null">
        to_user_mobile,
      </if>
      <if test="toCompanyCode != null">
        to_company_code,
      </if>
      <if test="messageType != null">
        message_type,
      </if>
      <if test="messageTime != null">
        message_time,
      </if>
      <if test="riskLabel != null">
        risk_label,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="messageContent != null">
        message_content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="messageId != null">
        #{messageId,jdbcType=VARCHAR},
      </if>
      <if test="fromUserCode != null">
        #{fromUserCode,jdbcType=VARCHAR},
      </if>
      <if test="fromUserName != null">
        #{fromUserName,jdbcType=VARCHAR},
      </if>
      <if test="fromUserMobile != null">
        #{fromUserMobile,jdbcType=VARCHAR},
      </if>
      <if test="fromCompanyCode != null">
        #{fromCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="toUserCode != null">
        #{toUserCode,jdbcType=VARCHAR},
      </if>
      <if test="toUserName != null">
        #{toUserName,jdbcType=VARCHAR},
      </if>
      <if test="toUserMobile != null">
        #{toUserMobile,jdbcType=VARCHAR},
      </if>
      <if test="toCompanyCode != null">
        #{toCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="messageType != null">
        #{messageType,jdbcType=TINYINT},
      </if>
      <if test="messageTime != null">
        #{messageTime,jdbcType=TIMESTAMP},
      </if>
      <if test="riskLabel != null">
        #{riskLabel,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="messageContent != null">
        #{messageContent,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.business.entity.ImAuditMessageExample" resultType="java.lang.Long">
    select count(*) from t_im_audit_message
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update t_im_audit_message
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=INTEGER},
      </if>
      <if test="row.messageId != null">
        message_id = #{row.messageId,jdbcType=VARCHAR},
      </if>
      <if test="row.fromUserCode != null">
        from_user_code = #{row.fromUserCode,jdbcType=VARCHAR},
      </if>
      <if test="row.fromUserName != null">
        from_user_name = #{row.fromUserName,jdbcType=VARCHAR},
      </if>
      <if test="row.fromUserMobile != null">
        from_user_mobile = #{row.fromUserMobile,jdbcType=VARCHAR},
      </if>
      <if test="row.fromCompanyCode != null">
        from_company_code = #{row.fromCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="row.toUserCode != null">
        to_user_code = #{row.toUserCode,jdbcType=VARCHAR},
      </if>
      <if test="row.toUserName != null">
        to_user_name = #{row.toUserName,jdbcType=VARCHAR},
      </if>
      <if test="row.toUserMobile != null">
        to_user_mobile = #{row.toUserMobile,jdbcType=VARCHAR},
      </if>
      <if test="row.toCompanyCode != null">
        to_company_code = #{row.toCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="row.messageType != null">
        message_type = #{row.messageType,jdbcType=TINYINT},
      </if>
      <if test="row.messageTime != null">
        message_time = #{row.messageTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.riskLabel != null">
        risk_label = #{row.riskLabel,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.messageContent != null">
        message_content = #{row.messageContent,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update t_im_audit_message
    set id = #{row.id,jdbcType=INTEGER},
      message_id = #{row.messageId,jdbcType=VARCHAR},
      from_user_code = #{row.fromUserCode,jdbcType=VARCHAR},
      from_user_name = #{row.fromUserName,jdbcType=VARCHAR},
      from_user_mobile = #{row.fromUserMobile,jdbcType=VARCHAR},
      from_company_code = #{row.fromCompanyCode,jdbcType=VARCHAR},
      to_user_code = #{row.toUserCode,jdbcType=VARCHAR},
      to_user_name = #{row.toUserName,jdbcType=VARCHAR},
      to_user_mobile = #{row.toUserMobile,jdbcType=VARCHAR},
      to_company_code = #{row.toCompanyCode,jdbcType=VARCHAR},
      message_type = #{row.messageType,jdbcType=TINYINT},
      message_time = #{row.messageTime,jdbcType=TIMESTAMP},
      risk_label = #{row.riskLabel,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      message_content = #{row.messageContent,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update t_im_audit_message
    set id = #{row.id,jdbcType=INTEGER},
      message_id = #{row.messageId,jdbcType=VARCHAR},
      from_user_code = #{row.fromUserCode,jdbcType=VARCHAR},
      from_user_name = #{row.fromUserName,jdbcType=VARCHAR},
      from_user_mobile = #{row.fromUserMobile,jdbcType=VARCHAR},
      from_company_code = #{row.fromCompanyCode,jdbcType=VARCHAR},
      to_user_code = #{row.toUserCode,jdbcType=VARCHAR},
      to_user_name = #{row.toUserName,jdbcType=VARCHAR},
      to_user_mobile = #{row.toUserMobile,jdbcType=VARCHAR},
      to_company_code = #{row.toCompanyCode,jdbcType=VARCHAR},
      message_type = #{row.messageType,jdbcType=TINYINT},
      message_time = #{row.messageTime,jdbcType=TIMESTAMP},
      risk_label = #{row.riskLabel,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.business.entity.ImAuditMessage">
    update t_im_audit_message
    <set>
      <if test="messageId != null">
        message_id = #{messageId,jdbcType=VARCHAR},
      </if>
      <if test="fromUserCode != null">
        from_user_code = #{fromUserCode,jdbcType=VARCHAR},
      </if>
      <if test="fromUserName != null">
        from_user_name = #{fromUserName,jdbcType=VARCHAR},
      </if>
      <if test="fromUserMobile != null">
        from_user_mobile = #{fromUserMobile,jdbcType=VARCHAR},
      </if>
      <if test="fromCompanyCode != null">
        from_company_code = #{fromCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="toUserCode != null">
        to_user_code = #{toUserCode,jdbcType=VARCHAR},
      </if>
      <if test="toUserName != null">
        to_user_name = #{toUserName,jdbcType=VARCHAR},
      </if>
      <if test="toUserMobile != null">
        to_user_mobile = #{toUserMobile,jdbcType=VARCHAR},
      </if>
      <if test="toCompanyCode != null">
        to_company_code = #{toCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="messageType != null">
        message_type = #{messageType,jdbcType=TINYINT},
      </if>
      <if test="messageTime != null">
        message_time = #{messageTime,jdbcType=TIMESTAMP},
      </if>
      <if test="riskLabel != null">
        risk_label = #{riskLabel,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="messageContent != null">
        message_content = #{messageContent,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.izu.business.entity.ImAuditMessage">
    update t_im_audit_message
    set message_id = #{messageId,jdbcType=VARCHAR},
      from_user_code = #{fromUserCode,jdbcType=VARCHAR},
      from_user_name = #{fromUserName,jdbcType=VARCHAR},
      from_user_mobile = #{fromUserMobile,jdbcType=VARCHAR},
      from_company_code = #{fromCompanyCode,jdbcType=VARCHAR},
      to_user_code = #{toUserCode,jdbcType=VARCHAR},
      to_user_name = #{toUserName,jdbcType=VARCHAR},
      to_user_mobile = #{toUserMobile,jdbcType=VARCHAR},
      to_company_code = #{toCompanyCode,jdbcType=VARCHAR},
      message_type = #{messageType,jdbcType=TINYINT},
      message_time = #{messageTime,jdbcType=TIMESTAMP},
      risk_label = #{riskLabel,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      message_content = #{messageContent,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.business.entity.ImAuditMessage">
    update t_im_audit_message
    set message_id = #{messageId,jdbcType=VARCHAR},
      from_user_code = #{fromUserCode,jdbcType=VARCHAR},
      from_user_name = #{fromUserName,jdbcType=VARCHAR},
      from_user_mobile = #{fromUserMobile,jdbcType=VARCHAR},
      from_company_code = #{fromCompanyCode,jdbcType=VARCHAR},
      to_user_code = #{toUserCode,jdbcType=VARCHAR},
      to_user_name = #{toUserName,jdbcType=VARCHAR},
      to_user_mobile = #{toUserMobile,jdbcType=VARCHAR},
      to_company_code = #{toCompanyCode,jdbcType=VARCHAR},
      message_type = #{messageType,jdbcType=TINYINT},
      message_time = #{messageTime,jdbcType=TIMESTAMP},
      risk_label = #{riskLabel,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>