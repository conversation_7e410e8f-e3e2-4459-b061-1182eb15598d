<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.SupplierExpenditureMapper">
  <resultMap id="BaseResultMap" type="com.izu.business.entity.SupplierExpenditure">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="expenditure_no" jdbcType="VARCHAR" property="expenditureNo" />
    <result column="expenditure_type" jdbcType="INTEGER" property="expenditureType" />
    <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="supplier_amount_total" jdbcType="DECIMAL" property="supplierAmountTotal" />
    <result column="supplier_amount_total_no_rate" jdbcType="DECIMAL" property="supplierAmountTotalNoRate" />
    <result column="supplier_rate" jdbcType="DECIMAL" property="supplierRate" />
    <result column="receipt_count" jdbcType="INTEGER" property="receiptCount" />
    <result column="supplier_invoice_type" jdbcType="INTEGER" property="supplierInvoiceType" />
    <result column="expenditure_status" jdbcType="TINYINT" property="expenditureStatus" />
    <result column="operate_buss_code" jdbcType="VARCHAR" property="operateBussCode" />
    <result column="operate_buss_name" jdbcType="VARCHAR" property="operateBussName" />
    <result column="item_description" jdbcType="VARCHAR" property="itemDescription" />
    <result column="push_result" jdbcType="VARCHAR" property="pushResult" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="creator_name" jdbcType="VARCHAR" property="creatorName" />
    <result column="creator_phone" jdbcType="VARCHAR" property="creatorPhone" />
    <result column="creator_dept_code" jdbcType="VARCHAR" property="creatorDeptCode" />
    <result column="creator_dept_name" jdbcType="VARCHAR" property="creatorDeptName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_id" jdbcType="INTEGER" property="updateId" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="update_phone" jdbcType="VARCHAR" property="updatePhone" />
    <result column="update_dept_code" jdbcType="VARCHAR" property="updateDeptCode" />
    <result column="update_dept_name" jdbcType="VARCHAR" property="updateDeptName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, expenditure_no, expenditure_type, supplier_code, supplier_name, supplier_amount_total, 
    supplier_amount_total_no_rate, supplier_rate, receipt_count, supplier_invoice_type, 
    expenditure_status, operate_buss_code, operate_buss_name, item_description, push_result, 
    creator_id, creator_name, creator_phone, creator_dept_code, creator_dept_name, create_time, 
    update_id, update_name, update_phone, update_dept_code, update_dept_name, update_time
  </sql>
  <select id="selectByExample" parameterType="com.izu.business.entity.SupplierExpenditureExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from supplier_expenditure
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from supplier_expenditure
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from supplier_expenditure
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.business.entity.SupplierExpenditureExample">
    delete from supplier_expenditure
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.business.entity.SupplierExpenditure">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into supplier_expenditure (expenditure_no, expenditure_type, supplier_code, 
      supplier_name, supplier_amount_total, supplier_amount_total_no_rate, 
      supplier_rate, receipt_count, supplier_invoice_type, 
      expenditure_status, operate_buss_code, operate_buss_name, 
      item_description, push_result, creator_id, 
      creator_name, creator_phone, creator_dept_code, 
      creator_dept_name, create_time, update_id, 
      update_name, update_phone, update_dept_code, 
      update_dept_name, update_time)
    values (#{expenditureNo,jdbcType=VARCHAR}, #{expenditureType,jdbcType=INTEGER}, #{supplierCode,jdbcType=VARCHAR}, 
      #{supplierName,jdbcType=VARCHAR}, #{supplierAmountTotal,jdbcType=DECIMAL}, #{supplierAmountTotalNoRate,jdbcType=DECIMAL}, 
      #{supplierRate,jdbcType=DECIMAL}, #{receiptCount,jdbcType=INTEGER}, #{supplierInvoiceType,jdbcType=INTEGER}, 
      #{expenditureStatus,jdbcType=TINYINT}, #{operateBussCode,jdbcType=VARCHAR}, #{operateBussName,jdbcType=VARCHAR}, 
      #{itemDescription,jdbcType=VARCHAR}, #{pushResult,jdbcType=VARCHAR}, #{creatorId,jdbcType=INTEGER}, 
      #{creatorName,jdbcType=VARCHAR}, #{creatorPhone,jdbcType=VARCHAR}, #{creatorDeptCode,jdbcType=VARCHAR}, 
      #{creatorDeptName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateId,jdbcType=INTEGER}, 
      #{updateName,jdbcType=VARCHAR}, #{updatePhone,jdbcType=VARCHAR}, #{updateDeptCode,jdbcType=VARCHAR}, 
      #{updateDeptName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.business.entity.SupplierExpenditure">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into supplier_expenditure
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="expenditureNo != null">
        expenditure_no,
      </if>
      <if test="expenditureType != null">
        expenditure_type,
      </if>
      <if test="supplierCode != null">
        supplier_code,
      </if>
      <if test="supplierName != null">
        supplier_name,
      </if>
      <if test="supplierAmountTotal != null">
        supplier_amount_total,
      </if>
      <if test="supplierAmountTotalNoRate != null">
        supplier_amount_total_no_rate,
      </if>
      <if test="supplierRate != null">
        supplier_rate,
      </if>
      <if test="receiptCount != null">
        receipt_count,
      </if>
      <if test="supplierInvoiceType != null">
        supplier_invoice_type,
      </if>
      <if test="expenditureStatus != null">
        expenditure_status,
      </if>
      <if test="operateBussCode != null">
        operate_buss_code,
      </if>
      <if test="operateBussName != null">
        operate_buss_name,
      </if>
      <if test="itemDescription != null">
        item_description,
      </if>
      <if test="pushResult != null">
        push_result,
      </if>
      <if test="creatorId != null">
        creator_id,
      </if>
      <if test="creatorName != null">
        creator_name,
      </if>
      <if test="creatorPhone != null">
        creator_phone,
      </if>
      <if test="creatorDeptCode != null">
        creator_dept_code,
      </if>
      <if test="creatorDeptName != null">
        creator_dept_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateId != null">
        update_id,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="updatePhone != null">
        update_phone,
      </if>
      <if test="updateDeptCode != null">
        update_dept_code,
      </if>
      <if test="updateDeptName != null">
        update_dept_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="expenditureNo != null">
        #{expenditureNo,jdbcType=VARCHAR},
      </if>
      <if test="expenditureType != null">
        #{expenditureType,jdbcType=INTEGER},
      </if>
      <if test="supplierCode != null">
        #{supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="supplierAmountTotal != null">
        #{supplierAmountTotal,jdbcType=DECIMAL},
      </if>
      <if test="supplierAmountTotalNoRate != null">
        #{supplierAmountTotalNoRate,jdbcType=DECIMAL},
      </if>
      <if test="supplierRate != null">
        #{supplierRate,jdbcType=DECIMAL},
      </if>
      <if test="receiptCount != null">
        #{receiptCount,jdbcType=INTEGER},
      </if>
      <if test="supplierInvoiceType != null">
        #{supplierInvoiceType,jdbcType=INTEGER},
      </if>
      <if test="expenditureStatus != null">
        #{expenditureStatus,jdbcType=TINYINT},
      </if>
      <if test="operateBussCode != null">
        #{operateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="operateBussName != null">
        #{operateBussName,jdbcType=VARCHAR},
      </if>
      <if test="itemDescription != null">
        #{itemDescription,jdbcType=VARCHAR},
      </if>
      <if test="pushResult != null">
        #{pushResult,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="creatorPhone != null">
        #{creatorPhone,jdbcType=VARCHAR},
      </if>
      <if test="creatorDeptCode != null">
        #{creatorDeptCode,jdbcType=VARCHAR},
      </if>
      <if test="creatorDeptName != null">
        #{creatorDeptName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updatePhone != null">
        #{updatePhone,jdbcType=VARCHAR},
      </if>
      <if test="updateDeptCode != null">
        #{updateDeptCode,jdbcType=VARCHAR},
      </if>
      <if test="updateDeptName != null">
        #{updateDeptName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.business.entity.SupplierExpenditureExample" resultType="java.lang.Long">
    select count(*) from supplier_expenditure
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update supplier_expenditure
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.expenditureNo != null">
        expenditure_no = #{row.expenditureNo,jdbcType=VARCHAR},
      </if>
      <if test="row.expenditureType != null">
        expenditure_type = #{row.expenditureType,jdbcType=INTEGER},
      </if>
      <if test="row.supplierCode != null">
        supplier_code = #{row.supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="row.supplierName != null">
        supplier_name = #{row.supplierName,jdbcType=VARCHAR},
      </if>
      <if test="row.supplierAmountTotal != null">
        supplier_amount_total = #{row.supplierAmountTotal,jdbcType=DECIMAL},
      </if>
      <if test="row.supplierAmountTotalNoRate != null">
        supplier_amount_total_no_rate = #{row.supplierAmountTotalNoRate,jdbcType=DECIMAL},
      </if>
      <if test="row.supplierRate != null">
        supplier_rate = #{row.supplierRate,jdbcType=DECIMAL},
      </if>
      <if test="row.receiptCount != null">
        receipt_count = #{row.receiptCount,jdbcType=INTEGER},
      </if>
      <if test="row.supplierInvoiceType != null">
        supplier_invoice_type = #{row.supplierInvoiceType,jdbcType=INTEGER},
      </if>
      <if test="row.expenditureStatus != null">
        expenditure_status = #{row.expenditureStatus,jdbcType=TINYINT},
      </if>
      <if test="row.operateBussCode != null">
        operate_buss_code = #{row.operateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="row.operateBussName != null">
        operate_buss_name = #{row.operateBussName,jdbcType=VARCHAR},
      </if>
      <if test="row.itemDescription != null">
        item_description = #{row.itemDescription,jdbcType=VARCHAR},
      </if>
      <if test="row.pushResult != null">
        push_result = #{row.pushResult,jdbcType=VARCHAR},
      </if>
      <if test="row.creatorId != null">
        creator_id = #{row.creatorId,jdbcType=INTEGER},
      </if>
      <if test="row.creatorName != null">
        creator_name = #{row.creatorName,jdbcType=VARCHAR},
      </if>
      <if test="row.creatorPhone != null">
        creator_phone = #{row.creatorPhone,jdbcType=VARCHAR},
      </if>
      <if test="row.creatorDeptCode != null">
        creator_dept_code = #{row.creatorDeptCode,jdbcType=VARCHAR},
      </if>
      <if test="row.creatorDeptName != null">
        creator_dept_name = #{row.creatorDeptName,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateId != null">
        update_id = #{row.updateId,jdbcType=INTEGER},
      </if>
      <if test="row.updateName != null">
        update_name = #{row.updateName,jdbcType=VARCHAR},
      </if>
      <if test="row.updatePhone != null">
        update_phone = #{row.updatePhone,jdbcType=VARCHAR},
      </if>
      <if test="row.updateDeptCode != null">
        update_dept_code = #{row.updateDeptCode,jdbcType=VARCHAR},
      </if>
      <if test="row.updateDeptName != null">
        update_dept_name = #{row.updateDeptName,jdbcType=VARCHAR},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update supplier_expenditure
    set id = #{row.id,jdbcType=BIGINT},
      expenditure_no = #{row.expenditureNo,jdbcType=VARCHAR},
      expenditure_type = #{row.expenditureType,jdbcType=INTEGER},
      supplier_code = #{row.supplierCode,jdbcType=VARCHAR},
      supplier_name = #{row.supplierName,jdbcType=VARCHAR},
      supplier_amount_total = #{row.supplierAmountTotal,jdbcType=DECIMAL},
      supplier_amount_total_no_rate = #{row.supplierAmountTotalNoRate,jdbcType=DECIMAL},
      supplier_rate = #{row.supplierRate,jdbcType=DECIMAL},
      receipt_count = #{row.receiptCount,jdbcType=INTEGER},
      supplier_invoice_type = #{row.supplierInvoiceType,jdbcType=INTEGER},
      expenditure_status = #{row.expenditureStatus,jdbcType=TINYINT},
      operate_buss_code = #{row.operateBussCode,jdbcType=VARCHAR},
      operate_buss_name = #{row.operateBussName,jdbcType=VARCHAR},
      item_description = #{row.itemDescription,jdbcType=VARCHAR},
      push_result = #{row.pushResult,jdbcType=VARCHAR},
      creator_id = #{row.creatorId,jdbcType=INTEGER},
      creator_name = #{row.creatorName,jdbcType=VARCHAR},
      creator_phone = #{row.creatorPhone,jdbcType=VARCHAR},
      creator_dept_code = #{row.creatorDeptCode,jdbcType=VARCHAR},
      creator_dept_name = #{row.creatorDeptName,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_id = #{row.updateId,jdbcType=INTEGER},
      update_name = #{row.updateName,jdbcType=VARCHAR},
      update_phone = #{row.updatePhone,jdbcType=VARCHAR},
      update_dept_code = #{row.updateDeptCode,jdbcType=VARCHAR},
      update_dept_name = #{row.updateDeptName,jdbcType=VARCHAR},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.business.entity.SupplierExpenditure">
    update supplier_expenditure
    <set>
      <if test="expenditureNo != null">
        expenditure_no = #{expenditureNo,jdbcType=VARCHAR},
      </if>
      <if test="expenditureType != null">
        expenditure_type = #{expenditureType,jdbcType=INTEGER},
      </if>
      <if test="supplierCode != null">
        supplier_code = #{supplierCode,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        supplier_name = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="supplierAmountTotal != null">
        supplier_amount_total = #{supplierAmountTotal,jdbcType=DECIMAL},
      </if>
      <if test="supplierAmountTotalNoRate != null">
        supplier_amount_total_no_rate = #{supplierAmountTotalNoRate,jdbcType=DECIMAL},
      </if>
      <if test="supplierRate != null">
        supplier_rate = #{supplierRate,jdbcType=DECIMAL},
      </if>
      <if test="receiptCount != null">
        receipt_count = #{receiptCount,jdbcType=INTEGER},
      </if>
      <if test="supplierInvoiceType != null">
        supplier_invoice_type = #{supplierInvoiceType,jdbcType=INTEGER},
      </if>
      <if test="expenditureStatus != null">
        expenditure_status = #{expenditureStatus,jdbcType=TINYINT},
      </if>
      <if test="operateBussCode != null">
        operate_buss_code = #{operateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="operateBussName != null">
        operate_buss_name = #{operateBussName,jdbcType=VARCHAR},
      </if>
      <if test="itemDescription != null">
        item_description = #{itemDescription,jdbcType=VARCHAR},
      </if>
      <if test="pushResult != null">
        push_result = #{pushResult,jdbcType=VARCHAR},
      </if>
      <if test="creatorId != null">
        creator_id = #{creatorId,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        creator_name = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="creatorPhone != null">
        creator_phone = #{creatorPhone,jdbcType=VARCHAR},
      </if>
      <if test="creatorDeptCode != null">
        creator_dept_code = #{creatorDeptCode,jdbcType=VARCHAR},
      </if>
      <if test="creatorDeptName != null">
        creator_dept_name = #{creatorDeptName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        update_id = #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updatePhone != null">
        update_phone = #{updatePhone,jdbcType=VARCHAR},
      </if>
      <if test="updateDeptCode != null">
        update_dept_code = #{updateDeptCode,jdbcType=VARCHAR},
      </if>
      <if test="updateDeptName != null">
        update_dept_name = #{updateDeptName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.business.entity.SupplierExpenditure">
    update supplier_expenditure
    set expenditure_no = #{expenditureNo,jdbcType=VARCHAR},
      expenditure_type = #{expenditureType,jdbcType=INTEGER},
      supplier_code = #{supplierCode,jdbcType=VARCHAR},
      supplier_name = #{supplierName,jdbcType=VARCHAR},
      supplier_amount_total = #{supplierAmountTotal,jdbcType=DECIMAL},
      supplier_amount_total_no_rate = #{supplierAmountTotalNoRate,jdbcType=DECIMAL},
      supplier_rate = #{supplierRate,jdbcType=DECIMAL},
      receipt_count = #{receiptCount,jdbcType=INTEGER},
      supplier_invoice_type = #{supplierInvoiceType,jdbcType=INTEGER},
      expenditure_status = #{expenditureStatus,jdbcType=TINYINT},
      operate_buss_code = #{operateBussCode,jdbcType=VARCHAR},
      operate_buss_name = #{operateBussName,jdbcType=VARCHAR},
      item_description = #{itemDescription,jdbcType=VARCHAR},
      push_result = #{pushResult,jdbcType=VARCHAR},
      creator_id = #{creatorId,jdbcType=INTEGER},
      creator_name = #{creatorName,jdbcType=VARCHAR},
      creator_phone = #{creatorPhone,jdbcType=VARCHAR},
      creator_dept_code = #{creatorDeptCode,jdbcType=VARCHAR},
      creator_dept_name = #{creatorDeptName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_id = #{updateId,jdbcType=INTEGER},
      update_name = #{updateName,jdbcType=VARCHAR},
      update_phone = #{updatePhone,jdbcType=VARCHAR},
      update_dept_code = #{updateDeptCode,jdbcType=VARCHAR},
      update_dept_name = #{updateDeptName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>