<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.MrCarBillingFileMapper">
  <resultMap id="BaseResultMap" type="com.izu.business.entity.MrCarBillingFile">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="bill_id" jdbcType="INTEGER" property="billId" />
    <result column="file_url" jdbcType="VARCHAR" property="fileUrl" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="file_real_name" jdbcType="VARCHAR" property="fileRealName" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="file_type" jdbcType="INTEGER" property="fileType" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, bill_id, file_url, file_name, file_real_name, status, file_type, create_time
  </sql>
  <select id="selectByExample" parameterType="com.izu.business.entity.MrCarBillingFileExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from mrcar_billing_file
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from mrcar_billing_file
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from mrcar_billing_file
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.business.entity.MrCarBillingFile">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mrcar_billing_file (bill_id, file_url, file_name, 
      file_real_name, status, file_type, 
      create_time)
    values (#{billId,jdbcType=INTEGER}, #{fileUrl,jdbcType=VARCHAR}, #{fileName,jdbcType=VARCHAR}, 
      #{fileRealName,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{fileType,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.business.entity.MrCarBillingFile">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into mrcar_billing_file
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="billId != null">
        bill_id,
      </if>
      <if test="fileUrl != null">
        file_url,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="fileRealName != null">
        file_real_name,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="fileType != null">
        file_type,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="billId != null">
        #{billId,jdbcType=INTEGER},
      </if>
      <if test="fileUrl != null">
        #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileRealName != null">
        #{fileRealName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="fileType != null">
        #{fileType,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.business.entity.MrCarBillingFileExample" resultType="java.lang.Long">
    select count(*) from mrcar_billing_file
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update mrcar_billing_file
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=INTEGER},
      </if>
      <if test="row.billId != null">
        bill_id = #{row.billId,jdbcType=INTEGER},
      </if>
      <if test="row.fileUrl != null">
        file_url = #{row.fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="row.fileName != null">
        file_name = #{row.fileName,jdbcType=VARCHAR},
      </if>
      <if test="row.fileRealName != null">
        file_real_name = #{row.fileRealName,jdbcType=VARCHAR},
      </if>
      <if test="row.status != null">
        status = #{row.status,jdbcType=INTEGER},
      </if>
      <if test="row.fileType != null">
        file_type = #{row.fileType,jdbcType=INTEGER},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update mrcar_billing_file
    set id = #{row.id,jdbcType=INTEGER},
      bill_id = #{row.billId,jdbcType=INTEGER},
      file_url = #{row.fileUrl,jdbcType=VARCHAR},
      file_name = #{row.fileName,jdbcType=VARCHAR},
      file_real_name = #{row.fileRealName,jdbcType=VARCHAR},
      status = #{row.status,jdbcType=INTEGER},
      file_type = #{row.fileType,jdbcType=INTEGER},
      create_time = #{row.createTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.business.entity.MrCarBillingFile">
    update mrcar_billing_file
    <set>
      <if test="billId != null">
        bill_id = #{billId,jdbcType=INTEGER},
      </if>
      <if test="fileUrl != null">
        file_url = #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileRealName != null">
        file_real_name = #{fileRealName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="fileType != null">
        file_type = #{fileType,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.business.entity.MrCarBillingFile">
    update mrcar_billing_file
    set bill_id = #{billId,jdbcType=INTEGER},
      file_url = #{fileUrl,jdbcType=VARCHAR},
      file_name = #{fileName,jdbcType=VARCHAR},
      file_real_name = #{fileRealName,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      file_type = #{fileType,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>