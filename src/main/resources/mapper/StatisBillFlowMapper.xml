<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.StatisBillFlowMapper">
  <resultMap id="BaseResultMap" type="com.izu.business.entity.StatisBillFlow">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="statis_date" jdbcType="VARCHAR" property="statisDate" />
    <result column="account_set_city_code" jdbcType="VARCHAR" property="accountSetCityCode" />
    <result column="account_set_city_name" jdbcType="VARCHAR" property="accountSetCityName" />
    <result column="bill_no" jdbcType="VARCHAR" property="billNo" />
    <result column="order_apply_no" jdbcType="VARCHAR" property="orderApplyNo" />
    <result column="bill_order_no" jdbcType="VARCHAR" property="billOrderNo" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="valuation_fee" jdbcType="DECIMAL" property="valuationFee" />
    <result column="attach_fee" jdbcType="DECIMAL" property="attachFee" />
    <result column="shouldpay_amount" jdbcType="DECIMAL" property="shouldpayAmount" />
    <result column="fixed_price_valid" jdbcType="BIT" property="fixedPriceValid" />
    <result column="fixed_price" jdbcType="DECIMAL" property="fixedPrice" />
    <result column="preferential_amount" jdbcType="DECIMAL" property="preferentialAmount" />
    <result column="waiver_amount" jdbcType="DECIMAL" property="waiverAmount" />
    <result column="other_amount" jdbcType="DECIMAL" property="otherAmount" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="vehicle_vin" jdbcType="VARCHAR" property="vehicleVin" />
    <result column="vehicle_license" jdbcType="VARCHAR" property="vehicleLicense" />
    <result column="vehicle_belonged_sys" jdbcType="VARCHAR" property="vehicleBelongedSys" />
    <result column="vehicle_id_new_system" jdbcType="INTEGER" property="vehicleIdNewSystem" />
    <result column="vehicle_serial_no_new_system" jdbcType="VARCHAR" property="vehicleSerialNoNewSystem" />
    <result column="driver_city_code" jdbcType="VARCHAR" property="driverCityCode" />
    <result column="driver_city_name" jdbcType="VARCHAR" property="driverCityName" />
    <result column="belong_city_code" jdbcType="VARCHAR" property="belongCityCode" />
    <result column="belong_city_name" jdbcType="VARCHAR" property="belongCityName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="order_type" jdbcType="TINYINT" property="orderType" />
    <result column="service_code" jdbcType="VARCHAR" property="serviceCode" />
    <result column="operate_struct_code" jdbcType="VARCHAR" property="operateStructCode" />
    <result column="operate_struct_name" jdbcType="VARCHAR" property="operateStructName" />
    <result column="operate_buss_name" jdbcType="VARCHAR" property="operateBussName" />
    <result column="operate_buss_code" jdbcType="VARCHAR" property="operateBussCode" />
  </resultMap>
  <sql id="Base_Column_List">
    id, statis_date, account_set_city_code, account_set_city_name, bill_no, order_apply_no, 
    bill_order_no, order_no, valuation_fee, attach_fee, shouldpay_amount, fixed_price_valid, 
    fixed_price, preferential_amount, waiver_amount, other_amount, vehicle_id, vehicle_vin, 
    vehicle_license, vehicle_belonged_sys, vehicle_id_new_system, vehicle_serial_no_new_system, 
    driver_city_code, driver_city_name, belong_city_code, belong_city_name, create_time, 
    update_time, order_type, service_code, operate_struct_code, operate_struct_name, 
    operate_buss_name, operate_buss_code
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from statis_bill_flow
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from statis_bill_flow
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.business.entity.StatisBillFlow">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into statis_bill_flow (statis_date, account_set_city_code, account_set_city_name, 
      bill_no, order_apply_no, bill_order_no, 
      order_no, valuation_fee, attach_fee, 
      shouldpay_amount, fixed_price_valid, fixed_price, 
      preferential_amount, waiver_amount, other_amount, 
      vehicle_id, vehicle_vin, vehicle_license, 
      vehicle_belonged_sys, vehicle_id_new_system, 
      vehicle_serial_no_new_system, driver_city_code, 
      driver_city_name, belong_city_code, belong_city_name, 
      create_time, update_time, order_type, 
      service_code, operate_struct_code, operate_struct_name, 
      operate_buss_name, operate_buss_code)
    values (#{statisDate,jdbcType=VARCHAR}, #{accountSetCityCode,jdbcType=VARCHAR}, #{accountSetCityName,jdbcType=VARCHAR}, 
      #{billNo,jdbcType=VARCHAR}, #{orderApplyNo,jdbcType=VARCHAR}, #{billOrderNo,jdbcType=VARCHAR}, 
      #{orderNo,jdbcType=VARCHAR}, #{valuationFee,jdbcType=DECIMAL}, #{attachFee,jdbcType=DECIMAL}, 
      #{shouldpayAmount,jdbcType=DECIMAL}, #{fixedPriceValid,jdbcType=BIT}, #{fixedPrice,jdbcType=DECIMAL}, 
      #{preferentialAmount,jdbcType=DECIMAL}, #{waiverAmount,jdbcType=DECIMAL}, #{otherAmount,jdbcType=DECIMAL}, 
      #{vehicleId,jdbcType=BIGINT}, #{vehicleVin,jdbcType=VARCHAR}, #{vehicleLicense,jdbcType=VARCHAR}, 
      #{vehicleBelongedSys,jdbcType=VARCHAR}, #{vehicleIdNewSystem,jdbcType=INTEGER}, 
      #{vehicleSerialNoNewSystem,jdbcType=VARCHAR}, #{driverCityCode,jdbcType=VARCHAR}, 
      #{driverCityName,jdbcType=VARCHAR}, #{belongCityCode,jdbcType=VARCHAR}, #{belongCityName,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{orderType,jdbcType=TINYINT}, 
      #{serviceCode,jdbcType=VARCHAR}, #{operateStructCode,jdbcType=VARCHAR}, #{operateStructName,jdbcType=VARCHAR}, 
      #{operateBussName,jdbcType=VARCHAR}, #{operateBussCode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.business.entity.StatisBillFlow">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into statis_bill_flow
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="statisDate != null">
        statis_date,
      </if>
      <if test="accountSetCityCode != null">
        account_set_city_code,
      </if>
      <if test="accountSetCityName != null">
        account_set_city_name,
      </if>
      <if test="billNo != null">
        bill_no,
      </if>
      <if test="orderApplyNo != null">
        order_apply_no,
      </if>
      <if test="billOrderNo != null">
        bill_order_no,
      </if>
      <if test="orderNo != null">
        order_no,
      </if>
      <if test="valuationFee != null">
        valuation_fee,
      </if>
      <if test="attachFee != null">
        attach_fee,
      </if>
      <if test="shouldpayAmount != null">
        shouldpay_amount,
      </if>
      <if test="fixedPriceValid != null">
        fixed_price_valid,
      </if>
      <if test="fixedPrice != null">
        fixed_price,
      </if>
      <if test="preferentialAmount != null">
        preferential_amount,
      </if>
      <if test="waiverAmount != null">
        waiver_amount,
      </if>
      <if test="otherAmount != null">
        other_amount,
      </if>
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="vehicleVin != null">
        vehicle_vin,
      </if>
      <if test="vehicleLicense != null">
        vehicle_license,
      </if>
      <if test="vehicleBelongedSys != null">
        vehicle_belonged_sys,
      </if>
      <if test="vehicleIdNewSystem != null">
        vehicle_id_new_system,
      </if>
      <if test="vehicleSerialNoNewSystem != null">
        vehicle_serial_no_new_system,
      </if>
      <if test="driverCityCode != null">
        driver_city_code,
      </if>
      <if test="driverCityName != null">
        driver_city_name,
      </if>
      <if test="belongCityCode != null">
        belong_city_code,
      </if>
      <if test="belongCityName != null">
        belong_city_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="serviceCode != null">
        service_code,
      </if>
      <if test="operateStructCode != null">
        operate_struct_code,
      </if>
      <if test="operateStructName != null">
        operate_struct_name,
      </if>
      <if test="operateBussName != null">
        operate_buss_name,
      </if>
      <if test="operateBussCode != null">
        operate_buss_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="statisDate != null">
        #{statisDate,jdbcType=VARCHAR},
      </if>
      <if test="accountSetCityCode != null">
        #{accountSetCityCode,jdbcType=VARCHAR},
      </if>
      <if test="accountSetCityName != null">
        #{accountSetCityName,jdbcType=VARCHAR},
      </if>
      <if test="billNo != null">
        #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="orderApplyNo != null">
        #{orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="billOrderNo != null">
        #{billOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="valuationFee != null">
        #{valuationFee,jdbcType=DECIMAL},
      </if>
      <if test="attachFee != null">
        #{attachFee,jdbcType=DECIMAL},
      </if>
      <if test="shouldpayAmount != null">
        #{shouldpayAmount,jdbcType=DECIMAL},
      </if>
      <if test="fixedPriceValid != null">
        #{fixedPriceValid,jdbcType=BIT},
      </if>
      <if test="fixedPrice != null">
        #{fixedPrice,jdbcType=DECIMAL},
      </if>
      <if test="preferentialAmount != null">
        #{preferentialAmount,jdbcType=DECIMAL},
      </if>
      <if test="waiverAmount != null">
        #{waiverAmount,jdbcType=DECIMAL},
      </if>
      <if test="otherAmount != null">
        #{otherAmount,jdbcType=DECIMAL},
      </if>
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleVin != null">
        #{vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="vehicleLicense != null">
        #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBelongedSys != null">
        #{vehicleBelongedSys,jdbcType=VARCHAR},
      </if>
      <if test="vehicleIdNewSystem != null">
        #{vehicleIdNewSystem,jdbcType=INTEGER},
      </if>
      <if test="vehicleSerialNoNewSystem != null">
        #{vehicleSerialNoNewSystem,jdbcType=VARCHAR},
      </if>
      <if test="driverCityCode != null">
        #{driverCityCode,jdbcType=VARCHAR},
      </if>
      <if test="driverCityName != null">
        #{driverCityName,jdbcType=VARCHAR},
      </if>
      <if test="belongCityCode != null">
        #{belongCityCode,jdbcType=VARCHAR},
      </if>
      <if test="belongCityName != null">
        #{belongCityName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=TINYINT},
      </if>
      <if test="serviceCode != null">
        #{serviceCode,jdbcType=VARCHAR},
      </if>
      <if test="operateStructCode != null">
        #{operateStructCode,jdbcType=VARCHAR},
      </if>
      <if test="operateStructName != null">
        #{operateStructName,jdbcType=VARCHAR},
      </if>
      <if test="operateBussName != null">
        #{operateBussName,jdbcType=VARCHAR},
      </if>
      <if test="operateBussCode != null">
        #{operateBussCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.business.entity.StatisBillFlow">
    update statis_bill_flow
    <set>
      <if test="statisDate != null">
        statis_date = #{statisDate,jdbcType=VARCHAR},
      </if>
      <if test="accountSetCityCode != null">
        account_set_city_code = #{accountSetCityCode,jdbcType=VARCHAR},
      </if>
      <if test="accountSetCityName != null">
        account_set_city_name = #{accountSetCityName,jdbcType=VARCHAR},
      </if>
      <if test="billNo != null">
        bill_no = #{billNo,jdbcType=VARCHAR},
      </if>
      <if test="orderApplyNo != null">
        order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      </if>
      <if test="billOrderNo != null">
        bill_order_no = #{billOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderNo != null">
        order_no = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="valuationFee != null">
        valuation_fee = #{valuationFee,jdbcType=DECIMAL},
      </if>
      <if test="attachFee != null">
        attach_fee = #{attachFee,jdbcType=DECIMAL},
      </if>
      <if test="shouldpayAmount != null">
        shouldpay_amount = #{shouldpayAmount,jdbcType=DECIMAL},
      </if>
      <if test="fixedPriceValid != null">
        fixed_price_valid = #{fixedPriceValid,jdbcType=BIT},
      </if>
      <if test="fixedPrice != null">
        fixed_price = #{fixedPrice,jdbcType=DECIMAL},
      </if>
      <if test="preferentialAmount != null">
        preferential_amount = #{preferentialAmount,jdbcType=DECIMAL},
      </if>
      <if test="waiverAmount != null">
        waiver_amount = #{waiverAmount,jdbcType=DECIMAL},
      </if>
      <if test="otherAmount != null">
        other_amount = #{otherAmount,jdbcType=DECIMAL},
      </if>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleVin != null">
        vehicle_vin = #{vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="vehicleLicense != null">
        vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="vehicleBelongedSys != null">
        vehicle_belonged_sys = #{vehicleBelongedSys,jdbcType=VARCHAR},
      </if>
      <if test="vehicleIdNewSystem != null">
        vehicle_id_new_system = #{vehicleIdNewSystem,jdbcType=INTEGER},
      </if>
      <if test="vehicleSerialNoNewSystem != null">
        vehicle_serial_no_new_system = #{vehicleSerialNoNewSystem,jdbcType=VARCHAR},
      </if>
      <if test="driverCityCode != null">
        driver_city_code = #{driverCityCode,jdbcType=VARCHAR},
      </if>
      <if test="driverCityName != null">
        driver_city_name = #{driverCityName,jdbcType=VARCHAR},
      </if>
      <if test="belongCityCode != null">
        belong_city_code = #{belongCityCode,jdbcType=VARCHAR},
      </if>
      <if test="belongCityName != null">
        belong_city_name = #{belongCityName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=TINYINT},
      </if>
      <if test="serviceCode != null">
        service_code = #{serviceCode,jdbcType=VARCHAR},
      </if>
      <if test="operateStructCode != null">
        operate_struct_code = #{operateStructCode,jdbcType=VARCHAR},
      </if>
      <if test="operateStructName != null">
        operate_struct_name = #{operateStructName,jdbcType=VARCHAR},
      </if>
      <if test="operateBussName != null">
        operate_buss_name = #{operateBussName,jdbcType=VARCHAR},
      </if>
      <if test="operateBussCode != null">
        operate_buss_code = #{operateBussCode,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.business.entity.StatisBillFlow">
    update statis_bill_flow
    set statis_date = #{statisDate,jdbcType=VARCHAR},
      account_set_city_code = #{accountSetCityCode,jdbcType=VARCHAR},
      account_set_city_name = #{accountSetCityName,jdbcType=VARCHAR},
      bill_no = #{billNo,jdbcType=VARCHAR},
      order_apply_no = #{orderApplyNo,jdbcType=VARCHAR},
      bill_order_no = #{billOrderNo,jdbcType=VARCHAR},
      order_no = #{orderNo,jdbcType=VARCHAR},
      valuation_fee = #{valuationFee,jdbcType=DECIMAL},
      attach_fee = #{attachFee,jdbcType=DECIMAL},
      shouldpay_amount = #{shouldpayAmount,jdbcType=DECIMAL},
      fixed_price_valid = #{fixedPriceValid,jdbcType=BIT},
      fixed_price = #{fixedPrice,jdbcType=DECIMAL},
      preferential_amount = #{preferentialAmount,jdbcType=DECIMAL},
      waiver_amount = #{waiverAmount,jdbcType=DECIMAL},
      other_amount = #{otherAmount,jdbcType=DECIMAL},
      vehicle_id = #{vehicleId,jdbcType=BIGINT},
      vehicle_vin = #{vehicleVin,jdbcType=VARCHAR},
      vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      vehicle_belonged_sys = #{vehicleBelongedSys,jdbcType=VARCHAR},
      vehicle_id_new_system = #{vehicleIdNewSystem,jdbcType=INTEGER},
      vehicle_serial_no_new_system = #{vehicleSerialNoNewSystem,jdbcType=VARCHAR},
      driver_city_code = #{driverCityCode,jdbcType=VARCHAR},
      driver_city_name = #{driverCityName,jdbcType=VARCHAR},
      belong_city_code = #{belongCityCode,jdbcType=VARCHAR},
      belong_city_name = #{belongCityName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      order_type = #{orderType,jdbcType=TINYINT},
      service_code = #{serviceCode,jdbcType=VARCHAR},
      operate_struct_code = #{operateStructCode,jdbcType=VARCHAR},
      operate_struct_name = #{operateStructName,jdbcType=VARCHAR},
      operate_buss_name = #{operateBussName,jdbcType=VARCHAR},
      operate_buss_code = #{operateBussCode,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>