<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.ContractOtherSubjectMapper">
  <resultMap id="BaseResultMap" type="com.izu.business.entity.ContractOtherSubject">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="contract_code" jdbcType="VARCHAR" property="contractCode" />
    <result column="operate_type" jdbcType="INTEGER" property="operateType" />
    <result column="operate_code" jdbcType="VARCHAR" property="operateCode" />
    <result column="operate_id" jdbcType="INTEGER" property="operateId" />
    <result column="operate_name" jdbcType="VARCHAR" property="operateName" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="legal_representative" jdbcType="VARCHAR" property="legalRepresentative" />
    <result column="bank_account_name" jdbcType="VARCHAR" property="bankAccountName" />
    <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
    <result column="bank_code" jdbcType="VARCHAR" property="bankCode" />
    <result column="bank_account_cst" jdbcType="VARCHAR" property="bankAccountCst" />
    <result column="authorized_name" jdbcType="VARCHAR" property="authorizedName" />
    <result column="authorized_id_card" jdbcType="VARCHAR" property="authorizedIdCard" />
    <result column="authorized_email" jdbcType="VARCHAR" property="authorizedEmail" />
    <result column="authorized_phone" jdbcType="VARCHAR" property="authorizedPhone" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, contract_code, operate_type, operate_code, operate_id, operate_name, address, 
    phone, legal_representative, bank_account_name, bank_name, bank_code, bank_account_cst, 
    authorized_name, authorized_id_card, authorized_email, authorized_phone, create_time, 
    update_time
  </sql>
  <select id="selectByExample" parameterType="com.izu.business.entity.ContractOtherSubjectExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from contract_other_subject
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from contract_other_subject
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from contract_other_subject
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.business.entity.ContractOtherSubject">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into contract_other_subject (contract_code, operate_type, operate_code, 
      operate_id, operate_name, address, 
      phone, legal_representative, bank_account_name, 
      bank_name, bank_code, bank_account_cst, 
      authorized_name, authorized_id_card, authorized_email, 
      authorized_phone, create_time, update_time
      )
    values (#{contractCode,jdbcType=VARCHAR}, #{operateType,jdbcType=INTEGER}, #{operateCode,jdbcType=VARCHAR}, 
      #{operateId,jdbcType=INTEGER}, #{operateName,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, 
      #{phone,jdbcType=VARCHAR}, #{legalRepresentative,jdbcType=VARCHAR}, #{bankAccountName,jdbcType=VARCHAR}, 
      #{bankName,jdbcType=VARCHAR}, #{bankCode,jdbcType=VARCHAR}, #{bankAccountCst,jdbcType=VARCHAR}, 
      #{authorizedName,jdbcType=VARCHAR}, #{authorizedIdCard,jdbcType=VARCHAR}, #{authorizedEmail,jdbcType=VARCHAR}, 
      #{authorizedPhone,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.business.entity.ContractOtherSubject">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into contract_other_subject
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="contractCode != null">
        contract_code,
      </if>
      <if test="operateType != null">
        operate_type,
      </if>
      <if test="operateCode != null">
        operate_code,
      </if>
      <if test="operateId != null">
        operate_id,
      </if>
      <if test="operateName != null">
        operate_name,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="legalRepresentative != null">
        legal_representative,
      </if>
      <if test="bankAccountName != null">
        bank_account_name,
      </if>
      <if test="bankName != null">
        bank_name,
      </if>
      <if test="bankCode != null">
        bank_code,
      </if>
      <if test="bankAccountCst != null">
        bank_account_cst,
      </if>
      <if test="authorizedName != null">
        authorized_name,
      </if>
      <if test="authorizedIdCard != null">
        authorized_id_card,
      </if>
      <if test="authorizedEmail != null">
        authorized_email,
      </if>
      <if test="authorizedPhone != null">
        authorized_phone,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="contractCode != null">
        #{contractCode,jdbcType=VARCHAR},
      </if>
      <if test="operateType != null">
        #{operateType,jdbcType=INTEGER},
      </if>
      <if test="operateCode != null">
        #{operateCode,jdbcType=VARCHAR},
      </if>
      <if test="operateId != null">
        #{operateId,jdbcType=INTEGER},
      </if>
      <if test="operateName != null">
        #{operateName,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="legalRepresentative != null">
        #{legalRepresentative,jdbcType=VARCHAR},
      </if>
      <if test="bankAccountName != null">
        #{bankAccountName,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankCode != null">
        #{bankCode,jdbcType=VARCHAR},
      </if>
      <if test="bankAccountCst != null">
        #{bankAccountCst,jdbcType=VARCHAR},
      </if>
      <if test="authorizedName != null">
        #{authorizedName,jdbcType=VARCHAR},
      </if>
      <if test="authorizedIdCard != null">
        #{authorizedIdCard,jdbcType=VARCHAR},
      </if>
      <if test="authorizedEmail != null">
        #{authorizedEmail,jdbcType=VARCHAR},
      </if>
      <if test="authorizedPhone != null">
        #{authorizedPhone,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.business.entity.ContractOtherSubjectExample" resultType="java.lang.Long">
    select count(*) from contract_other_subject
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update contract_other_subject
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=INTEGER},
      </if>
      <if test="row.contractCode != null">
        contract_code = #{row.contractCode,jdbcType=VARCHAR},
      </if>
      <if test="row.operateType != null">
        operate_type = #{row.operateType,jdbcType=INTEGER},
      </if>
      <if test="row.operateCode != null">
        operate_code = #{row.operateCode,jdbcType=VARCHAR},
      </if>
      <if test="row.operateId != null">
        operate_id = #{row.operateId,jdbcType=INTEGER},
      </if>
      <if test="row.operateName != null">
        operate_name = #{row.operateName,jdbcType=VARCHAR},
      </if>
      <if test="row.address != null">
        address = #{row.address,jdbcType=VARCHAR},
      </if>
      <if test="row.phone != null">
        phone = #{row.phone,jdbcType=VARCHAR},
      </if>
      <if test="row.legalRepresentative != null">
        legal_representative = #{row.legalRepresentative,jdbcType=VARCHAR},
      </if>
      <if test="row.bankAccountName != null">
        bank_account_name = #{row.bankAccountName,jdbcType=VARCHAR},
      </if>
      <if test="row.bankName != null">
        bank_name = #{row.bankName,jdbcType=VARCHAR},
      </if>
      <if test="row.bankCode != null">
        bank_code = #{row.bankCode,jdbcType=VARCHAR},
      </if>
      <if test="row.bankAccountCst != null">
        bank_account_cst = #{row.bankAccountCst,jdbcType=VARCHAR},
      </if>
      <if test="row.authorizedName != null">
        authorized_name = #{row.authorizedName,jdbcType=VARCHAR},
      </if>
      <if test="row.authorizedIdCard != null">
        authorized_id_card = #{row.authorizedIdCard,jdbcType=VARCHAR},
      </if>
      <if test="row.authorizedEmail != null">
        authorized_email = #{row.authorizedEmail,jdbcType=VARCHAR},
      </if>
      <if test="row.authorizedPhone != null">
        authorized_phone = #{row.authorizedPhone,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update contract_other_subject
    set id = #{row.id,jdbcType=INTEGER},
      contract_code = #{row.contractCode,jdbcType=VARCHAR},
      operate_type = #{row.operateType,jdbcType=INTEGER},
      operate_code = #{row.operateCode,jdbcType=VARCHAR},
      operate_id = #{row.operateId,jdbcType=INTEGER},
      operate_name = #{row.operateName,jdbcType=VARCHAR},
      address = #{row.address,jdbcType=VARCHAR},
      phone = #{row.phone,jdbcType=VARCHAR},
      legal_representative = #{row.legalRepresentative,jdbcType=VARCHAR},
      bank_account_name = #{row.bankAccountName,jdbcType=VARCHAR},
      bank_name = #{row.bankName,jdbcType=VARCHAR},
      bank_code = #{row.bankCode,jdbcType=VARCHAR},
      bank_account_cst = #{row.bankAccountCst,jdbcType=VARCHAR},
      authorized_name = #{row.authorizedName,jdbcType=VARCHAR},
      authorized_id_card = #{row.authorizedIdCard,jdbcType=VARCHAR},
      authorized_email = #{row.authorizedEmail,jdbcType=VARCHAR},
      authorized_phone = #{row.authorizedPhone,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.business.entity.ContractOtherSubject">
    update contract_other_subject
    <set>
      <if test="contractCode != null">
        contract_code = #{contractCode,jdbcType=VARCHAR},
      </if>
      <if test="operateType != null">
        operate_type = #{operateType,jdbcType=INTEGER},
      </if>
      <if test="operateCode != null">
        operate_code = #{operateCode,jdbcType=VARCHAR},
      </if>
      <if test="operateId != null">
        operate_id = #{operateId,jdbcType=INTEGER},
      </if>
      <if test="operateName != null">
        operate_name = #{operateName,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="legalRepresentative != null">
        legal_representative = #{legalRepresentative,jdbcType=VARCHAR},
      </if>
      <if test="bankAccountName != null">
        bank_account_name = #{bankAccountName,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        bank_name = #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankCode != null">
        bank_code = #{bankCode,jdbcType=VARCHAR},
      </if>
      <if test="bankAccountCst != null">
        bank_account_cst = #{bankAccountCst,jdbcType=VARCHAR},
      </if>
      <if test="authorizedName != null">
        authorized_name = #{authorizedName,jdbcType=VARCHAR},
      </if>
      <if test="authorizedIdCard != null">
        authorized_id_card = #{authorizedIdCard,jdbcType=VARCHAR},
      </if>
      <if test="authorizedEmail != null">
        authorized_email = #{authorizedEmail,jdbcType=VARCHAR},
      </if>
      <if test="authorizedPhone != null">
        authorized_phone = #{authorizedPhone,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.business.entity.ContractOtherSubject">
    update contract_other_subject
    set contract_code = #{contractCode,jdbcType=VARCHAR},
      operate_type = #{operateType,jdbcType=INTEGER},
      operate_code = #{operateCode,jdbcType=VARCHAR},
      operate_id = #{operateId,jdbcType=INTEGER},
      operate_name = #{operateName,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      legal_representative = #{legalRepresentative,jdbcType=VARCHAR},
      bank_account_name = #{bankAccountName,jdbcType=VARCHAR},
      bank_name = #{bankName,jdbcType=VARCHAR},
      bank_code = #{bankCode,jdbcType=VARCHAR},
      bank_account_cst = #{bankAccountCst,jdbcType=VARCHAR},
      authorized_name = #{authorizedName,jdbcType=VARCHAR},
      authorized_id_card = #{authorizedIdCard,jdbcType=VARCHAR},
      authorized_email = #{authorizedEmail,jdbcType=VARCHAR},
      authorized_phone = #{authorizedPhone,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>