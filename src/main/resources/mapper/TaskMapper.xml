<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.TaskMapper">
  <resultMap id="BaseResultMap" type="com.izu.business.entity.Task">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="task_type" jdbcType="VARCHAR" property="taskType" />
    <result column="data_start_date" jdbcType="DATE" property="dataStartDate" />
    <result column="data_end_date" jdbcType="DATE" property="dataEndDate" />
    <result column="run_status" jdbcType="INTEGER" property="runStatus" />
    <result column="run_start_time" jdbcType="TIMESTAMP" property="runStartTime" />
    <result column="run_end_time" jdbcType="TIMESTAMP" property="runEndTime" />
    <result column="last_heartbeat" jdbcType="TIMESTAMP" property="lastHeartbeat" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_id" jdbcType="INTEGER" property="createId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, company_id, company_name, task_type, data_start_date, data_end_date, run_status, 
    run_start_time, run_end_time, last_heartbeat, version, update_time, create_id, create_name, 
    create_time
  </sql>
  <select id="selectByExample" parameterType="com.izu.business.entity.TaskExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_task
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from t_task
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.business.entity.Task">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into t_task (company_id, company_name, task_type, 
      data_start_date, data_end_date, run_status, 
      run_start_time, run_end_time, last_heartbeat, 
      version, update_time, create_id, 
      create_name, create_time)
    values (#{companyId,jdbcType=INTEGER}, #{companyName,jdbcType=VARCHAR}, #{taskType,jdbcType=VARCHAR}, 
      #{dataStartDate,jdbcType=DATE}, #{dataEndDate,jdbcType=DATE}, #{runStatus,jdbcType=INTEGER}, 
      #{runStartTime,jdbcType=TIMESTAMP}, #{runEndTime,jdbcType=TIMESTAMP}, #{lastHeartbeat,jdbcType=TIMESTAMP}, 
      #{version,jdbcType=INTEGER}, #{updateTime,jdbcType=TIMESTAMP}, #{createId,jdbcType=INTEGER}, 
      #{createName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.business.entity.Task">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into t_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="taskType != null">
        task_type,
      </if>
      <if test="dataStartDate != null">
        data_start_date,
      </if>
      <if test="dataEndDate != null">
        data_end_date,
      </if>
      <if test="runStatus != null">
        run_status,
      </if>
      <if test="runStartTime != null">
        run_start_time,
      </if>
      <if test="runEndTime != null">
        run_end_time,
      </if>
      <if test="lastHeartbeat != null">
        last_heartbeat,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createId != null">
        create_id,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="taskType != null">
        #{taskType,jdbcType=VARCHAR},
      </if>
      <if test="dataStartDate != null">
        #{dataStartDate,jdbcType=DATE},
      </if>
      <if test="dataEndDate != null">
        #{dataEndDate,jdbcType=DATE},
      </if>
      <if test="runStatus != null">
        #{runStatus,jdbcType=INTEGER},
      </if>
      <if test="runStartTime != null">
        #{runStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="runEndTime != null">
        #{runEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastHeartbeat != null">
        #{lastHeartbeat,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createId != null">
        #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.business.entity.TaskExample" resultType="java.lang.Long">
    select count(*) from t_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update t_task
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=INTEGER},
      </if>
      <if test="row.companyId != null">
        company_id = #{row.companyId,jdbcType=INTEGER},
      </if>
      <if test="row.companyName != null">
        company_name = #{row.companyName,jdbcType=VARCHAR},
      </if>
      <if test="row.taskType != null">
        task_type = #{row.taskType,jdbcType=VARCHAR},
      </if>
      <if test="row.dataStartDate != null">
        data_start_date = #{row.dataStartDate,jdbcType=DATE},
      </if>
      <if test="row.dataEndDate != null">
        data_end_date = #{row.dataEndDate,jdbcType=DATE},
      </if>
      <if test="row.runStatus != null">
        run_status = #{row.runStatus,jdbcType=INTEGER},
      </if>
      <if test="row.runStartTime != null">
        run_start_time = #{row.runStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.runEndTime != null">
        run_end_time = #{row.runEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.lastHeartbeat != null">
        last_heartbeat = #{row.lastHeartbeat,jdbcType=TIMESTAMP},
      </if>
      <if test="row.version != null">
        version = #{row.version,jdbcType=INTEGER},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.createId != null">
        create_id = #{row.createId,jdbcType=INTEGER},
      </if>
      <if test="row.createName != null">
        create_name = #{row.createName,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update t_task
    set id = #{row.id,jdbcType=INTEGER},
      company_id = #{row.companyId,jdbcType=INTEGER},
      company_name = #{row.companyName,jdbcType=VARCHAR},
      task_type = #{row.taskType,jdbcType=VARCHAR},
      data_start_date = #{row.dataStartDate,jdbcType=DATE},
      data_end_date = #{row.dataEndDate,jdbcType=DATE},
      run_status = #{row.runStatus,jdbcType=INTEGER},
      run_start_time = #{row.runStartTime,jdbcType=TIMESTAMP},
      run_end_time = #{row.runEndTime,jdbcType=TIMESTAMP},
      last_heartbeat = #{row.lastHeartbeat,jdbcType=TIMESTAMP},
      version = #{row.version,jdbcType=INTEGER},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      create_id = #{row.createId,jdbcType=INTEGER},
      create_name = #{row.createName,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.business.entity.Task">
    update t_task
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="companyName != null">
        company_name = #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="taskType != null">
        task_type = #{taskType,jdbcType=VARCHAR},
      </if>
      <if test="dataStartDate != null">
        data_start_date = #{dataStartDate,jdbcType=DATE},
      </if>
      <if test="dataEndDate != null">
        data_end_date = #{dataEndDate,jdbcType=DATE},
      </if>
      <if test="runStatus != null">
        run_status = #{runStatus,jdbcType=INTEGER},
      </if>
      <if test="runStartTime != null">
        run_start_time = #{runStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="runEndTime != null">
        run_end_time = #{runEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastHeartbeat != null">
        last_heartbeat = #{lastHeartbeat,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createId != null">
        create_id = #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.business.entity.Task">
    update t_task
    set company_id = #{companyId,jdbcType=INTEGER},
      company_name = #{companyName,jdbcType=VARCHAR},
      task_type = #{taskType,jdbcType=VARCHAR},
      data_start_date = #{dataStartDate,jdbcType=DATE},
      data_end_date = #{dataEndDate,jdbcType=DATE},
      run_status = #{runStatus,jdbcType=INTEGER},
      run_start_time = #{runStartTime,jdbcType=TIMESTAMP},
      run_end_time = #{runEndTime,jdbcType=TIMESTAMP},
      last_heartbeat = #{lastHeartbeat,jdbcType=TIMESTAMP},
      version = #{version,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_id = #{createId,jdbcType=INTEGER},
      create_name = #{createName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>