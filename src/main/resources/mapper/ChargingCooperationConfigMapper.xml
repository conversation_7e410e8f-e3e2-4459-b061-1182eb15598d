<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.ChargingCooperationConfigMapper">
  <resultMap id="BaseResultMap" type="com.izu.user.entity.ChargingCooperationConfig">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="business_code" jdbcType="VARCHAR" property="businessCode" />
    <result column="business_name" jdbcType="VARCHAR" property="businessName" />
    <result column="pack_code" jdbcType="VARCHAR" property="packCode" />
    <result column="pack_name" jdbcType="VARCHAR" property="packName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="paid_service_type" jdbcType="TINYINT" property="paidServiceType" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, business_code, business_name, pack_code, pack_name, create_time, update_time, 
    paid_service_type
  </sql>
  <select id="selectByExample" parameterType="com.izu.user.entity.ChargingCooperationConfigExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from charging_cooperation_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from charging_cooperation_config
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from charging_cooperation_config
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.user.entity.ChargingCooperationConfigExample">
    delete from charging_cooperation_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.user.entity.ChargingCooperationConfig">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into charging_cooperation_config (business_code, business_name, pack_code, 
      pack_name, create_time, update_time, 
      paid_service_type)
    values (#{businessCode,jdbcType=VARCHAR}, #{businessName,jdbcType=VARCHAR}, #{packCode,jdbcType=VARCHAR}, 
      #{packName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{paidServiceType,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.user.entity.ChargingCooperationConfig">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into charging_cooperation_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessCode != null">
        business_code,
      </if>
      <if test="businessName != null">
        business_name,
      </if>
      <if test="packCode != null">
        pack_code,
      </if>
      <if test="packName != null">
        pack_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="paidServiceType != null">
        paid_service_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessCode != null">
        #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="businessName != null">
        #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="packCode != null">
        #{packCode,jdbcType=VARCHAR},
      </if>
      <if test="packName != null">
        #{packName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="paidServiceType != null">
        #{paidServiceType,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.user.entity.ChargingCooperationConfigExample" resultType="java.lang.Long">
    select count(*) from charging_cooperation_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update charging_cooperation_config
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=INTEGER},
      </if>
      <if test="row.businessCode != null">
        business_code = #{row.businessCode,jdbcType=VARCHAR},
      </if>
      <if test="row.businessName != null">
        business_name = #{row.businessName,jdbcType=VARCHAR},
      </if>
      <if test="row.packCode != null">
        pack_code = #{row.packCode,jdbcType=VARCHAR},
      </if>
      <if test="row.packName != null">
        pack_name = #{row.packName,jdbcType=VARCHAR},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.paidServiceType != null">
        paid_service_type = #{row.paidServiceType,jdbcType=TINYINT},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update charging_cooperation_config
    set id = #{row.id,jdbcType=INTEGER},
      business_code = #{row.businessCode,jdbcType=VARCHAR},
      business_name = #{row.businessName,jdbcType=VARCHAR},
      pack_code = #{row.packCode,jdbcType=VARCHAR},
      pack_name = #{row.packName,jdbcType=VARCHAR},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      paid_service_type = #{row.paidServiceType,jdbcType=TINYINT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.user.entity.ChargingCooperationConfig">
    update charging_cooperation_config
    <set>
      <if test="businessCode != null">
        business_code = #{businessCode,jdbcType=VARCHAR},
      </if>
      <if test="businessName != null">
        business_name = #{businessName,jdbcType=VARCHAR},
      </if>
      <if test="packCode != null">
        pack_code = #{packCode,jdbcType=VARCHAR},
      </if>
      <if test="packName != null">
        pack_name = #{packName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="paidServiceType != null">
        paid_service_type = #{paidServiceType,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.user.entity.ChargingCooperationConfig">
    update charging_cooperation_config
    set business_code = #{businessCode,jdbcType=VARCHAR},
      business_name = #{businessName,jdbcType=VARCHAR},
      pack_code = #{packCode,jdbcType=VARCHAR},
      pack_name = #{packName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      paid_service_type = #{paidServiceType,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>