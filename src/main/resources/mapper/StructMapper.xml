<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.StructMapper">
  <resultMap id="BaseResultMap" type="com.izu.user.entity.Struct">
    <id column="struct_id" jdbcType="INTEGER" property="structId" />
    <result column="struct_type" jdbcType="TINYINT" property="structType" />
    <result column="struct_name" jdbcType="VARCHAR" property="structName" />
    <result column="struct_intro" jdbcType="VARCHAR" property="structIntro" />
    <result column="parent_id" jdbcType="INTEGER" property="parentId" />
    <result column="struct_order" jdbcType="INTEGER" property="structOrder" />
    <result column="struct_status" jdbcType="TINYINT" property="structStatus" />
    <result column="default_flag" jdbcType="TINYINT" property="defaultFlag" />
    <result column="create_time" jdbcType="VARCHAR" property="createTime" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="budget_type" jdbcType="TINYINT" property="budgetType" />
    <result column="budget_amount" jdbcType="VARCHAR" property="budgetAmount" />
    <result column="budget_over" jdbcType="TINYINT" property="budgetOver" />
    <result column="struct_no" jdbcType="VARCHAR" property="structNo" />
    <result column="struct_old_no" jdbcType="VARCHAR" property="structOldNo" />
    <result column="asset_struct_id" jdbcType="VARCHAR" property="assetStructId" />
    <result column="update_time_d" jdbcType="VARCHAR" property="updateTimeD" />
    <result column="datacenter_struct_id" jdbcType="INTEGER" property="datacenterStructId" />
    <result column="datacenter_parent_id" jdbcType="INTEGER" property="datacenterParentId" />
    <result column="d_operator_id" jdbcType="INTEGER" property="dOperatorId" />
    <result column="d_operator_name" jdbcType="VARCHAR" property="dOperatorName" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
  </resultMap>
  <sql id="Base_Column_List">
    struct_id, struct_type, struct_name, struct_intro, parent_id, struct_order, struct_status, 
    default_flag, create_time, company_id, budget_type, budget_amount, budget_over, struct_no, 
    struct_old_no, asset_struct_id, update_time_d, datacenter_struct_id, datacenter_parent_id, 
    d_operator_id, d_operator_name, city_code, city_name
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_company_struct
    where struct_id = #{structId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from t_company_struct
    where struct_id = #{structId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.user.entity.Struct">
    <selectKey keyProperty="structId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into t_company_struct (struct_type, struct_name, struct_intro, 
      parent_id, struct_order, struct_status, 
      default_flag, create_time, company_id, 
      budget_type, budget_amount, budget_over, 
      struct_no, struct_old_no, asset_struct_id, 
      update_time_d, datacenter_struct_id, datacenter_parent_id, 
      d_operator_id, d_operator_name, city_code, 
      city_name)
    values (#{structType,jdbcType=TINYINT}, #{structName,jdbcType=VARCHAR}, #{structIntro,jdbcType=VARCHAR}, 
      #{parentId,jdbcType=INTEGER}, #{structOrder,jdbcType=INTEGER}, #{structStatus,jdbcType=TINYINT}, 
      #{defaultFlag,jdbcType=TINYINT}, #{createTime,jdbcType=VARCHAR}, #{companyId,jdbcType=INTEGER}, 
      #{budgetType,jdbcType=TINYINT}, #{budgetAmount,jdbcType=VARCHAR}, #{budgetOver,jdbcType=TINYINT}, 
      #{structNo,jdbcType=VARCHAR}, #{structOldNo,jdbcType=VARCHAR}, #{assetStructId,jdbcType=VARCHAR}, 
      #{updateTimeD,jdbcType=VARCHAR}, #{datacenterStructId,jdbcType=INTEGER}, #{datacenterParentId,jdbcType=INTEGER}, 
      #{dOperatorId,jdbcType=INTEGER}, #{dOperatorName,jdbcType=VARCHAR}, #{cityCode,jdbcType=VARCHAR}, 
      #{cityName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.user.entity.Struct">
    <selectKey keyProperty="structId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into t_company_struct
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="structType != null">
        struct_type,
      </if>
      <if test="structName != null">
        struct_name,
      </if>
      <if test="structIntro != null">
        struct_intro,
      </if>
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="structOrder != null">
        struct_order,
      </if>
      <if test="structStatus != null">
        struct_status,
      </if>
      <if test="defaultFlag != null">
        default_flag,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="budgetType != null">
        budget_type,
      </if>
      <if test="budgetAmount != null">
        budget_amount,
      </if>
      <if test="budgetOver != null">
        budget_over,
      </if>
      <if test="structNo != null">
        struct_no,
      </if>
      <if test="structOldNo != null">
        struct_old_no,
      </if>
      <if test="assetStructId != null">
        asset_struct_id,
      </if>
      <if test="updateTimeD != null">
        update_time_d,
      </if>
      <if test="datacenterStructId != null">
        datacenter_struct_id,
      </if>
      <if test="datacenterParentId != null">
        datacenter_parent_id,
      </if>
      <if test="dOperatorId != null">
        d_operator_id,
      </if>
      <if test="dOperatorName != null">
        d_operator_name,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="cityName != null">
        city_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="structType != null">
        #{structType,jdbcType=TINYINT},
      </if>
      <if test="structName != null">
        #{structName,jdbcType=VARCHAR},
      </if>
      <if test="structIntro != null">
        #{structIntro,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=INTEGER},
      </if>
      <if test="structOrder != null">
        #{structOrder,jdbcType=INTEGER},
      </if>
      <if test="structStatus != null">
        #{structStatus,jdbcType=TINYINT},
      </if>
      <if test="defaultFlag != null">
        #{defaultFlag,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=INTEGER},
      </if>
      <if test="budgetType != null">
        #{budgetType,jdbcType=TINYINT},
      </if>
      <if test="budgetAmount != null">
        #{budgetAmount,jdbcType=VARCHAR},
      </if>
      <if test="budgetOver != null">
        #{budgetOver,jdbcType=TINYINT},
      </if>
      <if test="structNo != null">
        #{structNo,jdbcType=VARCHAR},
      </if>
      <if test="structOldNo != null">
        #{structOldNo,jdbcType=VARCHAR},
      </if>
      <if test="assetStructId != null">
        #{assetStructId,jdbcType=VARCHAR},
      </if>
      <if test="updateTimeD != null">
        #{updateTimeD,jdbcType=VARCHAR},
      </if>
      <if test="datacenterStructId != null">
        #{datacenterStructId,jdbcType=INTEGER},
      </if>
      <if test="datacenterParentId != null">
        #{datacenterParentId,jdbcType=INTEGER},
      </if>
      <if test="dOperatorId != null">
        #{dOperatorId,jdbcType=INTEGER},
      </if>
      <if test="dOperatorName != null">
        #{dOperatorName,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.user.entity.Struct">
    update t_company_struct
    <set>
      <if test="structType != null">
        struct_type = #{structType,jdbcType=TINYINT},
      </if>
      <if test="structName != null">
        struct_name = #{structName,jdbcType=VARCHAR},
      </if>
      <if test="structIntro != null">
        struct_intro = #{structIntro,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=INTEGER},
      </if>
      <if test="structOrder != null">
        struct_order = #{structOrder,jdbcType=INTEGER},
      </if>
      <if test="structStatus != null">
        struct_status = #{structStatus,jdbcType=TINYINT},
      </if>
      <if test="defaultFlag != null">
        default_flag = #{defaultFlag,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="budgetType != null">
        budget_type = #{budgetType,jdbcType=TINYINT},
      </if>
      <if test="budgetAmount != null">
        budget_amount = #{budgetAmount,jdbcType=VARCHAR},
      </if>
      <if test="budgetOver != null">
        budget_over = #{budgetOver,jdbcType=TINYINT},
      </if>
      <if test="structNo != null">
        struct_no = #{structNo,jdbcType=VARCHAR},
      </if>
      <if test="structOldNo != null">
        struct_old_no = #{structOldNo,jdbcType=VARCHAR},
      </if>
      <if test="assetStructId != null">
        asset_struct_id = #{assetStructId,jdbcType=VARCHAR},
      </if>
      <if test="updateTimeD != null">
        update_time_d = #{updateTimeD,jdbcType=VARCHAR},
      </if>
      <if test="datacenterStructId != null">
        datacenter_struct_id = #{datacenterStructId,jdbcType=INTEGER},
      </if>
      <if test="datacenterParentId != null">
        datacenter_parent_id = #{datacenterParentId,jdbcType=INTEGER},
      </if>
      <if test="dOperatorId != null">
        d_operator_id = #{dOperatorId,jdbcType=INTEGER},
      </if>
      <if test="dOperatorName != null">
        d_operator_name = #{dOperatorName,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        city_code = #{cityCode,jdbcType=VARCHAR},
      </if>
      <if test="cityName != null">
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
    </set>
    where struct_id = #{structId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.user.entity.Struct">
    update t_company_struct
    set struct_type = #{structType,jdbcType=TINYINT},
      struct_name = #{structName,jdbcType=VARCHAR},
      struct_intro = #{structIntro,jdbcType=VARCHAR},
      parent_id = #{parentId,jdbcType=INTEGER},
      struct_order = #{structOrder,jdbcType=INTEGER},
      struct_status = #{structStatus,jdbcType=TINYINT},
      default_flag = #{defaultFlag,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=INTEGER},
      budget_type = #{budgetType,jdbcType=TINYINT},
      budget_amount = #{budgetAmount,jdbcType=VARCHAR},
      budget_over = #{budgetOver,jdbcType=TINYINT},
      struct_no = #{structNo,jdbcType=VARCHAR},
      struct_old_no = #{structOldNo,jdbcType=VARCHAR},
      asset_struct_id = #{assetStructId,jdbcType=VARCHAR},
      update_time_d = #{updateTimeD,jdbcType=VARCHAR},
      datacenter_struct_id = #{datacenterStructId,jdbcType=INTEGER},
      datacenter_parent_id = #{datacenterParentId,jdbcType=INTEGER},
      d_operator_id = #{dOperatorId,jdbcType=INTEGER},
      d_operator_name = #{dOperatorName,jdbcType=VARCHAR},
      city_code = #{cityCode,jdbcType=VARCHAR},
      city_name = #{cityName,jdbcType=VARCHAR}
    where struct_id = #{structId,jdbcType=INTEGER}
  </update>
</mapper>