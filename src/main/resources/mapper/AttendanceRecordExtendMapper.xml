<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.AttendanceRecordExtendMapper">
  <resultMap id="BaseResultMap" type="com.izu.business.entity.AttendanceRecordExtend">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="record_time" jdbcType="TIMESTAMP" property="recordTime" />
    <result column="attendance_record_id" jdbcType="INTEGER" property="attendanceRecordId" />
    <result column="record_type" jdbcType="TINYINT" property="recordType" />
    <result column="record_locate" jdbcType="VARCHAR" property="recordLocate" />
    <result column="record_locate_name" jdbcType="VARCHAR" property="recordLocateName" />
    <result column="seq_no" jdbcType="VARCHAR" property="seqNo" />
    <result column="process_instance_id" jdbcType="VARCHAR" property="processInstanceId" />
    <result column="out_sign_info" jdbcType="VARCHAR" property="outSignInfo" />
  </resultMap>
  <sql id="Base_Column_List">
    id, record_time, attendance_record_id, record_type, record_locate, record_locate_name, 
    seq_no, process_instance_id, out_sign_info
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from attendance_record_extend
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from attendance_record_extend
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.business.entity.AttendanceRecordExtend">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into attendance_record_extend (record_time, attendance_record_id, 
      record_type, record_locate, record_locate_name, 
      seq_no, process_instance_id, out_sign_info
      )
    values (#{recordTime,jdbcType=TIMESTAMP}, #{attendanceRecordId,jdbcType=INTEGER}, 
      #{recordType,jdbcType=TINYINT}, #{recordLocate,jdbcType=VARCHAR}, #{recordLocateName,jdbcType=VARCHAR}, 
      #{seqNo,jdbcType=VARCHAR}, #{processInstanceId,jdbcType=VARCHAR}, #{outSignInfo,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.business.entity.AttendanceRecordExtend">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into attendance_record_extend
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="recordTime != null">
        record_time,
      </if>
      <if test="attendanceRecordId != null">
        attendance_record_id,
      </if>
      <if test="recordType != null">
        record_type,
      </if>
      <if test="recordLocate != null">
        record_locate,
      </if>
      <if test="recordLocateName != null">
        record_locate_name,
      </if>
      <if test="seqNo != null">
        seq_no,
      </if>
      <if test="processInstanceId != null">
        process_instance_id,
      </if>
      <if test="outSignInfo != null">
        out_sign_info,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="recordTime != null">
        #{recordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="attendanceRecordId != null">
        #{attendanceRecordId,jdbcType=INTEGER},
      </if>
      <if test="recordType != null">
        #{recordType,jdbcType=TINYINT},
      </if>
      <if test="recordLocate != null">
        #{recordLocate,jdbcType=VARCHAR},
      </if>
      <if test="recordLocateName != null">
        #{recordLocateName,jdbcType=VARCHAR},
      </if>
      <if test="seqNo != null">
        #{seqNo,jdbcType=VARCHAR},
      </if>
      <if test="processInstanceId != null">
        #{processInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="outSignInfo != null">
        #{outSignInfo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.business.entity.AttendanceRecordExtend">
    update attendance_record_extend
    <set>
      <if test="recordTime != null">
        record_time = #{recordTime,jdbcType=TIMESTAMP},
      </if>
      <if test="attendanceRecordId != null">
        attendance_record_id = #{attendanceRecordId,jdbcType=INTEGER},
      </if>
      <if test="recordType != null">
        record_type = #{recordType,jdbcType=TINYINT},
      </if>
      <if test="recordLocate != null">
        record_locate = #{recordLocate,jdbcType=VARCHAR},
      </if>
      <if test="recordLocateName != null">
        record_locate_name = #{recordLocateName,jdbcType=VARCHAR},
      </if>
      <if test="seqNo != null">
        seq_no = #{seqNo,jdbcType=VARCHAR},
      </if>
      <if test="processInstanceId != null">
        process_instance_id = #{processInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="outSignInfo != null">
        out_sign_info = #{outSignInfo,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.business.entity.AttendanceRecordExtend">
    update attendance_record_extend
    set record_time = #{recordTime,jdbcType=TIMESTAMP},
      attendance_record_id = #{attendanceRecordId,jdbcType=INTEGER},
      record_type = #{recordType,jdbcType=TINYINT},
      record_locate = #{recordLocate,jdbcType=VARCHAR},
      record_locate_name = #{recordLocateName,jdbcType=VARCHAR},
      seq_no = #{seqNo,jdbcType=VARCHAR},
      process_instance_id = #{processInstanceId,jdbcType=VARCHAR},
      out_sign_info = #{outSignInfo,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>