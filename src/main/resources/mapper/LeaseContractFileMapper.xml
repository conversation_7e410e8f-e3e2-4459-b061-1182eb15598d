<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.LeaseContractFileMapper">
  <resultMap id="BaseResultMap" type="com.izu.business.entity.LeaseContractFile">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="operate_id" jdbcType="INTEGER" property="operateId" />
    <result column="operate_code" jdbcType="VARCHAR" property="operateCode" />
    <result column="file_url" jdbcType="VARCHAR" property="fileUrl" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="file_real_name" jdbcType="VARCHAR" property="fileRealName" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="operate_type" jdbcType="INTEGER" property="operateType" />
  </resultMap>
  <sql id="Base_Column_List">
    id, operate_id, operate_code, file_url, file_name, file_real_name, status, operate_type
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from lease_contract_file
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from lease_contract_file
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.business.entity.LeaseContractFile">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lease_contract_file (operate_id, operate_code, file_url,
    file_name, file_real_name, status,
    operate_type)
    values (#{operateId,jdbcType=INTEGER}, #{operateCode,jdbcType=VARCHAR}, #{fileUrl,jdbcType=VARCHAR},
    #{fileName,jdbcType=VARCHAR}, #{fileRealName,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER},
    #{operateType,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.business.entity.LeaseContractFile">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lease_contract_file
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="operateId != null">
        operate_id,
      </if>
      <if test="operateCode != null">
        operate_code,
      </if>
      <if test="fileUrl != null">
        file_url,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="fileRealName != null">
        file_real_name,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="operateType != null">
        operate_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="operateId != null">
        #{operateId,jdbcType=INTEGER},
      </if>
      <if test="operateCode != null">
        #{operateCode,jdbcType=VARCHAR},
      </if>
      <if test="fileUrl != null">
        #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileRealName != null">
        #{fileRealName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="operateType != null">
        #{operateType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.business.entity.LeaseContractFile">
    update lease_contract_file
    <set>
      <if test="operateId != null">
        operate_id = #{operateId,jdbcType=INTEGER},
      </if>
      <if test="operateCode != null">
        operate_code = #{operateCode,jdbcType=VARCHAR},
      </if>
      <if test="fileUrl != null">
        file_url = #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileRealName != null">
        file_real_name = #{fileRealName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="operateType != null">
        operate_type = #{operateType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.business.entity.LeaseContractFile">
    update lease_contract_file
    set operate_id = #{operateId,jdbcType=INTEGER},
      operate_code = #{operateCode,jdbcType=VARCHAR},
      file_url = #{fileUrl,jdbcType=VARCHAR},
      file_name = #{fileName,jdbcType=VARCHAR},
      file_real_name = #{fileRealName,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      operate_type = #{operateType,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>