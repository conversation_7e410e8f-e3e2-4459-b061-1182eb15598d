<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.DriverAllocationDetailMapper">
  <resultMap id="BaseResultMap" type="com.izu.user.entity.DriverAllocationDetail">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="allocation_id" jdbcType="INTEGER" property="allocationId" />
    <result column="driver_id" jdbcType="INTEGER" property="driverId" />
    <result column="driver_mobile" jdbcType="VARCHAR" property="driverMobile" />
    <result column="office_status" jdbcType="TINYINT" property="officeStatus" />
    <result column="working_status" jdbcType="TINYINT" property="workingStatus" />
    <result column="labor_company_name" jdbcType="VARCHAR" property="laborCompanyName" />
    <result column="labor_company_id" jdbcType="INTEGER" property="laborCompanyId" />
    <result column="belong_struct_code" jdbcType="VARCHAR" property="belongStructCode" />
    <result column="belong_struct_name" jdbcType="VARCHAR" property="belongStructName" />
    <result column="source_company_id" jdbcType="INTEGER" property="sourceCompanyId" />
    <result column="source_company_code" jdbcType="VARCHAR" property="sourceCompanyCode" />
    <result column="source_company_name" jdbcType="VARCHAR" property="sourceCompanyName" />
    <result column="source_department_name" jdbcType="VARCHAR" property="sourceDepartmentName" />
    <result column="source_department_id" jdbcType="INTEGER" property="sourceDepartmentId" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, allocation_id, driver_id, driver_mobile, office_status, working_status, labor_company_name, 
    labor_company_id, belong_struct_code, belong_struct_name, source_company_id, source_company_code, 
    source_company_name, source_department_name, source_department_id, created_time
  </sql>
  <select id="selectByExample" parameterType="com.izu.user.entity.DriverAllocationDetailExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_driver_allocation_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_driver_allocation_detail
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from t_driver_allocation_detail
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.user.entity.DriverAllocationDetailExample">
    delete from t_driver_allocation_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.user.entity.DriverAllocationDetail">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into t_driver_allocation_detail (allocation_id, driver_id, driver_mobile, 
      office_status, working_status, labor_company_name, 
      labor_company_id, belong_struct_code, belong_struct_name, 
      source_company_id, source_company_code, source_company_name, 
      source_department_name, source_department_id, 
      created_time)
    values (#{allocationId,jdbcType=INTEGER}, #{driverId,jdbcType=INTEGER}, #{driverMobile,jdbcType=VARCHAR}, 
      #{officeStatus,jdbcType=TINYINT}, #{workingStatus,jdbcType=TINYINT}, #{laborCompanyName,jdbcType=VARCHAR}, 
      #{laborCompanyId,jdbcType=INTEGER}, #{belongStructCode,jdbcType=VARCHAR}, #{belongStructName,jdbcType=VARCHAR}, 
      #{sourceCompanyId,jdbcType=INTEGER}, #{sourceCompanyCode,jdbcType=VARCHAR}, #{sourceCompanyName,jdbcType=VARCHAR}, 
      #{sourceDepartmentName,jdbcType=VARCHAR}, #{sourceDepartmentId,jdbcType=INTEGER}, 
      #{createdTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.user.entity.DriverAllocationDetail">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into t_driver_allocation_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="allocationId != null">
        allocation_id,
      </if>
      <if test="driverId != null">
        driver_id,
      </if>
      <if test="driverMobile != null">
        driver_mobile,
      </if>
      <if test="officeStatus != null">
        office_status,
      </if>
      <if test="workingStatus != null">
        working_status,
      </if>
      <if test="laborCompanyName != null">
        labor_company_name,
      </if>
      <if test="laborCompanyId != null">
        labor_company_id,
      </if>
      <if test="belongStructCode != null">
        belong_struct_code,
      </if>
      <if test="belongStructName != null">
        belong_struct_name,
      </if>
      <if test="sourceCompanyId != null">
        source_company_id,
      </if>
      <if test="sourceCompanyCode != null">
        source_company_code,
      </if>
      <if test="sourceCompanyName != null">
        source_company_name,
      </if>
      <if test="sourceDepartmentName != null">
        source_department_name,
      </if>
      <if test="sourceDepartmentId != null">
        source_department_id,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="allocationId != null">
        #{allocationId,jdbcType=INTEGER},
      </if>
      <if test="driverId != null">
        #{driverId,jdbcType=INTEGER},
      </if>
      <if test="driverMobile != null">
        #{driverMobile,jdbcType=VARCHAR},
      </if>
      <if test="officeStatus != null">
        #{officeStatus,jdbcType=TINYINT},
      </if>
      <if test="workingStatus != null">
        #{workingStatus,jdbcType=TINYINT},
      </if>
      <if test="laborCompanyName != null">
        #{laborCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="laborCompanyId != null">
        #{laborCompanyId,jdbcType=INTEGER},
      </if>
      <if test="belongStructCode != null">
        #{belongStructCode,jdbcType=VARCHAR},
      </if>
      <if test="belongStructName != null">
        #{belongStructName,jdbcType=VARCHAR},
      </if>
      <if test="sourceCompanyId != null">
        #{sourceCompanyId,jdbcType=INTEGER},
      </if>
      <if test="sourceCompanyCode != null">
        #{sourceCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="sourceCompanyName != null">
        #{sourceCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="sourceDepartmentName != null">
        #{sourceDepartmentName,jdbcType=VARCHAR},
      </if>
      <if test="sourceDepartmentId != null">
        #{sourceDepartmentId,jdbcType=INTEGER},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.user.entity.DriverAllocationDetailExample" resultType="java.lang.Long">
    select count(*) from t_driver_allocation_detail
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update t_driver_allocation_detail
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=INTEGER},
      </if>
      <if test="row.allocationId != null">
        allocation_id = #{row.allocationId,jdbcType=INTEGER},
      </if>
      <if test="row.driverId != null">
        driver_id = #{row.driverId,jdbcType=INTEGER},
      </if>
      <if test="row.driverMobile != null">
        driver_mobile = #{row.driverMobile,jdbcType=VARCHAR},
      </if>
      <if test="row.officeStatus != null">
        office_status = #{row.officeStatus,jdbcType=TINYINT},
      </if>
      <if test="row.workingStatus != null">
        working_status = #{row.workingStatus,jdbcType=TINYINT},
      </if>
      <if test="row.laborCompanyName != null">
        labor_company_name = #{row.laborCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="row.laborCompanyId != null">
        labor_company_id = #{row.laborCompanyId,jdbcType=INTEGER},
      </if>
      <if test="row.belongStructCode != null">
        belong_struct_code = #{row.belongStructCode,jdbcType=VARCHAR},
      </if>
      <if test="row.belongStructName != null">
        belong_struct_name = #{row.belongStructName,jdbcType=VARCHAR},
      </if>
      <if test="row.sourceCompanyId != null">
        source_company_id = #{row.sourceCompanyId,jdbcType=INTEGER},
      </if>
      <if test="row.sourceCompanyCode != null">
        source_company_code = #{row.sourceCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="row.sourceCompanyName != null">
        source_company_name = #{row.sourceCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="row.sourceDepartmentName != null">
        source_department_name = #{row.sourceDepartmentName,jdbcType=VARCHAR},
      </if>
      <if test="row.sourceDepartmentId != null">
        source_department_id = #{row.sourceDepartmentId,jdbcType=INTEGER},
      </if>
      <if test="row.createdTime != null">
        created_time = #{row.createdTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update t_driver_allocation_detail
    set id = #{row.id,jdbcType=INTEGER},
      allocation_id = #{row.allocationId,jdbcType=INTEGER},
      driver_id = #{row.driverId,jdbcType=INTEGER},
      driver_mobile = #{row.driverMobile,jdbcType=VARCHAR},
      office_status = #{row.officeStatus,jdbcType=TINYINT},
      working_status = #{row.workingStatus,jdbcType=TINYINT},
      labor_company_name = #{row.laborCompanyName,jdbcType=VARCHAR},
      labor_company_id = #{row.laborCompanyId,jdbcType=INTEGER},
      belong_struct_code = #{row.belongStructCode,jdbcType=VARCHAR},
      belong_struct_name = #{row.belongStructName,jdbcType=VARCHAR},
      source_company_id = #{row.sourceCompanyId,jdbcType=INTEGER},
      source_company_code = #{row.sourceCompanyCode,jdbcType=VARCHAR},
      source_company_name = #{row.sourceCompanyName,jdbcType=VARCHAR},
      source_department_name = #{row.sourceDepartmentName,jdbcType=VARCHAR},
      source_department_id = #{row.sourceDepartmentId,jdbcType=INTEGER},
      created_time = #{row.createdTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.user.entity.DriverAllocationDetail">
    update t_driver_allocation_detail
    <set>
      <if test="allocationId != null">
        allocation_id = #{allocationId,jdbcType=INTEGER},
      </if>
      <if test="driverId != null">
        driver_id = #{driverId,jdbcType=INTEGER},
      </if>
      <if test="driverMobile != null">
        driver_mobile = #{driverMobile,jdbcType=VARCHAR},
      </if>
      <if test="officeStatus != null">
        office_status = #{officeStatus,jdbcType=TINYINT},
      </if>
      <if test="workingStatus != null">
        working_status = #{workingStatus,jdbcType=TINYINT},
      </if>
      <if test="laborCompanyName != null">
        labor_company_name = #{laborCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="laborCompanyId != null">
        labor_company_id = #{laborCompanyId,jdbcType=INTEGER},
      </if>
      <if test="belongStructCode != null">
        belong_struct_code = #{belongStructCode,jdbcType=VARCHAR},
      </if>
      <if test="belongStructName != null">
        belong_struct_name = #{belongStructName,jdbcType=VARCHAR},
      </if>
      <if test="sourceCompanyId != null">
        source_company_id = #{sourceCompanyId,jdbcType=INTEGER},
      </if>
      <if test="sourceCompanyCode != null">
        source_company_code = #{sourceCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="sourceCompanyName != null">
        source_company_name = #{sourceCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="sourceDepartmentName != null">
        source_department_name = #{sourceDepartmentName,jdbcType=VARCHAR},
      </if>
      <if test="sourceDepartmentId != null">
        source_department_id = #{sourceDepartmentId,jdbcType=INTEGER},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.user.entity.DriverAllocationDetail">
    update t_driver_allocation_detail
    set allocation_id = #{allocationId,jdbcType=INTEGER},
      driver_id = #{driverId,jdbcType=INTEGER},
      driver_mobile = #{driverMobile,jdbcType=VARCHAR},
      office_status = #{officeStatus,jdbcType=TINYINT},
      working_status = #{workingStatus,jdbcType=TINYINT},
      labor_company_name = #{laborCompanyName,jdbcType=VARCHAR},
      labor_company_id = #{laborCompanyId,jdbcType=INTEGER},
      belong_struct_code = #{belongStructCode,jdbcType=VARCHAR},
      belong_struct_name = #{belongStructName,jdbcType=VARCHAR},
      source_company_id = #{sourceCompanyId,jdbcType=INTEGER},
      source_company_code = #{sourceCompanyCode,jdbcType=VARCHAR},
      source_company_name = #{sourceCompanyName,jdbcType=VARCHAR},
      source_department_name = #{sourceDepartmentName,jdbcType=VARCHAR},
      source_department_id = #{sourceDepartmentId,jdbcType=INTEGER},
      created_time = #{createdTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>