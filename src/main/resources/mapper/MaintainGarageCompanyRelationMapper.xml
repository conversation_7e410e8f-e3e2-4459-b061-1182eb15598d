<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.MaintainGarageCompanyRelationMapper">
  <resultMap id="BaseResultMap" type="com.izu.user.entity.MaintainGarageCompanyRelation">
    <id column="relation_id" jdbcType="INTEGER" property="relationId" />
    <result column="garage_no" jdbcType="VARCHAR" property="garageNo" />
    <result column="garage_name" jdbcType="VARCHAR" property="garageName" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="relation_company_id" jdbcType="INTEGER" property="relationCompanyId" />
    <result column="relation_company_code" jdbcType="VARCHAR" property="relationCompanyCode" />
    <result column="relation_company_name" jdbcType="VARCHAR" property="relationCompanyName" />
    <result column="relation_type" jdbcType="TINYINT" property="relationType" />
    <result column="relation_begin_date" jdbcType="DATE" property="relationBeginDate" />
    <result column="relation_end_date" jdbcType="DATE" property="relationEndDate" />
    <result column="relation_status" jdbcType="TINYINT" property="relationStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_id" jdbcType="INTEGER" property="createId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_id" jdbcType="INTEGER" property="updateId" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    relation_id, garage_no, garage_name, company_code, relation_company_id, relation_company_code, 
    relation_company_name, relation_type, relation_begin_date, relation_end_date, relation_status, 
    create_time, create_id, create_name, update_time, update_id, update_name
  </sql>
  <select id="selectByExample" parameterType="com.izu.user.entity.MaintainGarageCompanyRelationExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from maintain_garage_company_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from maintain_garage_company_relation
    where relation_id = #{relationId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from maintain_garage_company_relation
    where relation_id = #{relationId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.user.entity.MaintainGarageCompanyRelationExample">
    delete from maintain_garage_company_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.user.entity.MaintainGarageCompanyRelation">
    <selectKey keyProperty="relationId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into maintain_garage_company_relation (garage_no, garage_name, company_code, 
      relation_company_id, relation_company_code, 
      relation_company_name, relation_type, relation_begin_date, 
      relation_end_date, relation_status, create_time, 
      create_id, create_name, update_time, 
      update_id, update_name)
    values (#{garageNo,jdbcType=VARCHAR}, #{garageName,jdbcType=VARCHAR}, #{companyCode,jdbcType=VARCHAR}, 
      #{relationCompanyId,jdbcType=INTEGER}, #{relationCompanyCode,jdbcType=VARCHAR}, 
      #{relationCompanyName,jdbcType=VARCHAR}, #{relationType,jdbcType=TINYINT}, #{relationBeginDate,jdbcType=DATE}, 
      #{relationEndDate,jdbcType=DATE}, #{relationStatus,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, 
      #{createId,jdbcType=INTEGER}, #{createName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{updateId,jdbcType=INTEGER}, #{updateName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.user.entity.MaintainGarageCompanyRelation">
    <selectKey keyProperty="relationId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into maintain_garage_company_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="garageNo != null">
        garage_no,
      </if>
      <if test="garageName != null">
        garage_name,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="relationCompanyId != null">
        relation_company_id,
      </if>
      <if test="relationCompanyCode != null">
        relation_company_code,
      </if>
      <if test="relationCompanyName != null">
        relation_company_name,
      </if>
      <if test="relationType != null">
        relation_type,
      </if>
      <if test="relationBeginDate != null">
        relation_begin_date,
      </if>
      <if test="relationEndDate != null">
        relation_end_date,
      </if>
      <if test="relationStatus != null">
        relation_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="createId != null">
        create_id,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateId != null">
        update_id,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="garageNo != null">
        #{garageNo,jdbcType=VARCHAR},
      </if>
      <if test="garageName != null">
        #{garageName,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="relationCompanyId != null">
        #{relationCompanyId,jdbcType=INTEGER},
      </if>
      <if test="relationCompanyCode != null">
        #{relationCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="relationCompanyName != null">
        #{relationCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="relationType != null">
        #{relationType,jdbcType=TINYINT},
      </if>
      <if test="relationBeginDate != null">
        #{relationBeginDate,jdbcType=DATE},
      </if>
      <if test="relationEndDate != null">
        #{relationEndDate,jdbcType=DATE},
      </if>
      <if test="relationStatus != null">
        #{relationStatus,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createId != null">
        #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.user.entity.MaintainGarageCompanyRelationExample" resultType="java.lang.Long">
    select count(*) from maintain_garage_company_relation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update maintain_garage_company_relation
    <set>
      <if test="row.relationId != null">
        relation_id = #{row.relationId,jdbcType=INTEGER},
      </if>
      <if test="row.garageNo != null">
        garage_no = #{row.garageNo,jdbcType=VARCHAR},
      </if>
      <if test="row.garageName != null">
        garage_name = #{row.garageName,jdbcType=VARCHAR},
      </if>
      <if test="row.companyCode != null">
        company_code = #{row.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="row.relationCompanyId != null">
        relation_company_id = #{row.relationCompanyId,jdbcType=INTEGER},
      </if>
      <if test="row.relationCompanyCode != null">
        relation_company_code = #{row.relationCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="row.relationCompanyName != null">
        relation_company_name = #{row.relationCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="row.relationType != null">
        relation_type = #{row.relationType,jdbcType=TINYINT},
      </if>
      <if test="row.relationBeginDate != null">
        relation_begin_date = #{row.relationBeginDate,jdbcType=DATE},
      </if>
      <if test="row.relationEndDate != null">
        relation_end_date = #{row.relationEndDate,jdbcType=DATE},
      </if>
      <if test="row.relationStatus != null">
        relation_status = #{row.relationStatus,jdbcType=TINYINT},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.createId != null">
        create_id = #{row.createId,jdbcType=INTEGER},
      </if>
      <if test="row.createName != null">
        create_name = #{row.createName,jdbcType=VARCHAR},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateId != null">
        update_id = #{row.updateId,jdbcType=INTEGER},
      </if>
      <if test="row.updateName != null">
        update_name = #{row.updateName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update maintain_garage_company_relation
    set relation_id = #{row.relationId,jdbcType=INTEGER},
      garage_no = #{row.garageNo,jdbcType=VARCHAR},
      garage_name = #{row.garageName,jdbcType=VARCHAR},
      company_code = #{row.companyCode,jdbcType=VARCHAR},
      relation_company_id = #{row.relationCompanyId,jdbcType=INTEGER},
      relation_company_code = #{row.relationCompanyCode,jdbcType=VARCHAR},
      relation_company_name = #{row.relationCompanyName,jdbcType=VARCHAR},
      relation_type = #{row.relationType,jdbcType=TINYINT},
      relation_begin_date = #{row.relationBeginDate,jdbcType=DATE},
      relation_end_date = #{row.relationEndDate,jdbcType=DATE},
      relation_status = #{row.relationStatus,jdbcType=TINYINT},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      create_id = #{row.createId,jdbcType=INTEGER},
      create_name = #{row.createName,jdbcType=VARCHAR},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      update_id = #{row.updateId,jdbcType=INTEGER},
      update_name = #{row.updateName,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.user.entity.MaintainGarageCompanyRelation">
    update maintain_garage_company_relation
    <set>
      <if test="garageNo != null">
        garage_no = #{garageNo,jdbcType=VARCHAR},
      </if>
      <if test="garageName != null">
        garage_name = #{garageName,jdbcType=VARCHAR},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="relationCompanyId != null">
        relation_company_id = #{relationCompanyId,jdbcType=INTEGER},
      </if>
      <if test="relationCompanyCode != null">
        relation_company_code = #{relationCompanyCode,jdbcType=VARCHAR},
      </if>
      <if test="relationCompanyName != null">
        relation_company_name = #{relationCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="relationType != null">
        relation_type = #{relationType,jdbcType=TINYINT},
      </if>
      <if test="relationBeginDate != null">
        relation_begin_date = #{relationBeginDate,jdbcType=DATE},
      </if>
      <if test="relationEndDate != null">
        relation_end_date = #{relationEndDate,jdbcType=DATE},
      </if>
      <if test="relationStatus != null">
        relation_status = #{relationStatus,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createId != null">
        create_id = #{createId,jdbcType=INTEGER},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        update_id = #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
    </set>
    where relation_id = #{relationId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.user.entity.MaintainGarageCompanyRelation">
    update maintain_garage_company_relation
    set garage_no = #{garageNo,jdbcType=VARCHAR},
      garage_name = #{garageName,jdbcType=VARCHAR},
      company_code = #{companyCode,jdbcType=VARCHAR},
      relation_company_id = #{relationCompanyId,jdbcType=INTEGER},
      relation_company_code = #{relationCompanyCode,jdbcType=VARCHAR},
      relation_company_name = #{relationCompanyName,jdbcType=VARCHAR},
      relation_type = #{relationType,jdbcType=TINYINT},
      relation_begin_date = #{relationBeginDate,jdbcType=DATE},
      relation_end_date = #{relationEndDate,jdbcType=DATE},
      relation_status = #{relationStatus,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      create_id = #{createId,jdbcType=INTEGER},
      create_name = #{createName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_id = #{updateId,jdbcType=INTEGER},
      update_name = #{updateName,jdbcType=VARCHAR}
    where relation_id = #{relationId,jdbcType=INTEGER}
  </update>
</mapper>