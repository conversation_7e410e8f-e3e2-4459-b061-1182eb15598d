<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.StatisVehicleMileageMapper">
  <resultMap id="BaseResultMap" type="com.izu.business.entity.StatisVehicleMileage">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="vehicle_id" jdbcType="BIGINT" property="vehicleId" />
    <result column="vehicle_license" jdbcType="VARCHAR" property="vehicleLicense" />
    <result column="city_code" jdbcType="INTEGER" property="cityCode" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="total_mileage" jdbcType="DECIMAL" property="totalMileage" />
    <result column="statis_date" jdbcType="VARCHAR" property="statisDate" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="effective_mileage" jdbcType="DECIMAL" property="effectiveMileage" />
  </resultMap>
  <sql id="Base_Column_List">
    id, vehicle_id, vehicle_license, city_code, city_name, total_mileage, statis_date, 
    update_time, create_time, effective_mileage
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from statis_vehicle_mileage
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from statis_vehicle_mileage
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.izu.business.entity.StatisVehicleMileage">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into statis_vehicle_mileage (vehicle_id, vehicle_license, city_code, 
      city_name, total_mileage, statis_date, 
      update_time, create_time, effective_mileage
      )
    values (#{vehicleId,jdbcType=BIGINT}, #{vehicleLicense,jdbcType=VARCHAR}, #{cityCode,jdbcType=INTEGER}, 
      #{cityName,jdbcType=VARCHAR}, #{totalMileage,jdbcType=DECIMAL}, #{statisDate,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, #{effectiveMileage,jdbcType=DECIMAL}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.business.entity.StatisVehicleMileage">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into statis_vehicle_mileage
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="vehicleId != null">
        vehicle_id,
      </if>
      <if test="vehicleLicense != null">
        vehicle_license,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="cityName != null">
        city_name,
      </if>
      <if test="totalMileage != null">
        total_mileage,
      </if>
      <if test="statisDate != null">
        statis_date,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="effectiveMileage != null">
        effective_mileage,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="vehicleId != null">
        #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleLicense != null">
        #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=INTEGER},
      </if>
      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="totalMileage != null">
        #{totalMileage,jdbcType=DECIMAL},
      </if>
      <if test="statisDate != null">
        #{statisDate,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="effectiveMileage != null">
        #{effectiveMileage,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.business.entity.StatisVehicleMileage">
    update statis_vehicle_mileage
    <set>
      <if test="vehicleId != null">
        vehicle_id = #{vehicleId,jdbcType=BIGINT},
      </if>
      <if test="vehicleLicense != null">
        vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        city_code = #{cityCode,jdbcType=INTEGER},
      </if>
      <if test="cityName != null">
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="totalMileage != null">
        total_mileage = #{totalMileage,jdbcType=DECIMAL},
      </if>
      <if test="statisDate != null">
        statis_date = #{statisDate,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="effectiveMileage != null">
        effective_mileage = #{effectiveMileage,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.business.entity.StatisVehicleMileage">
    update statis_vehicle_mileage
    set vehicle_id = #{vehicleId,jdbcType=BIGINT},
      vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      city_code = #{cityCode,jdbcType=INTEGER},
      city_name = #{cityName,jdbcType=VARCHAR},
      total_mileage = #{totalMileage,jdbcType=DECIMAL},
      statis_date = #{statisDate,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      effective_mileage = #{effectiveMileage,jdbcType=DECIMAL}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>