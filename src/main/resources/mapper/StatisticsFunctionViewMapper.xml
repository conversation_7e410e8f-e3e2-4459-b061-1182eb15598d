<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.StatisticsFunctionViewMapper">
  <resultMap id="BaseResultMap" type="com.izu.business.entity.StatisticsFunctionView">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="customer_code" jdbcType="VARCHAR" property="customerCode" />
    <result column="perm_sys_code" jdbcType="VARCHAR" property="permSysCode" />
    <result column="menu_open_mode" jdbcType="TINYINT" property="menuOpenMode" />
    <result column="stat_date" jdbcType="DATE" property="statDate" />
    <result column="customer_pv" jdbcType="INTEGER" property="customerPv" />
    <result column="customer_uv" jdbcType="INTEGER" property="customerUv" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, customer_code, perm_sys_code, menu_open_mode, stat_date, customer_pv, customer_uv, 
    create_time
  </sql>
  <select id="selectByExample" parameterType="com.izu.business.entity.StatisticsFunctionViewExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from statistics_function_view
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from statistics_function_view
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from statistics_function_view
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.business.entity.StatisticsFunctionViewExample">
    delete from statistics_function_view
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.business.entity.StatisticsFunctionView">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into statistics_function_view (customer_code, perm_sys_code, menu_open_mode, 
      stat_date, customer_pv, customer_uv, 
      create_time)
    values (#{customerCode,jdbcType=VARCHAR}, #{permSysCode,jdbcType=VARCHAR}, #{menuOpenMode,jdbcType=TINYINT}, 
      #{statDate,jdbcType=DATE}, #{customerPv,jdbcType=INTEGER}, #{customerUv,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.business.entity.StatisticsFunctionView">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into statistics_function_view
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="customerCode != null">
        customer_code,
      </if>
      <if test="permSysCode != null">
        perm_sys_code,
      </if>
      <if test="menuOpenMode != null">
        menu_open_mode,
      </if>
      <if test="statDate != null">
        stat_date,
      </if>
      <if test="customerPv != null">
        customer_pv,
      </if>
      <if test="customerUv != null">
        customer_uv,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="customerCode != null">
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="permSysCode != null">
        #{permSysCode,jdbcType=VARCHAR},
      </if>
      <if test="menuOpenMode != null">
        #{menuOpenMode,jdbcType=TINYINT},
      </if>
      <if test="statDate != null">
        #{statDate,jdbcType=DATE},
      </if>
      <if test="customerPv != null">
        #{customerPv,jdbcType=INTEGER},
      </if>
      <if test="customerUv != null">
        #{customerUv,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.business.entity.StatisticsFunctionViewExample" resultType="java.lang.Long">
    select count(*) from statistics_function_view
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update statistics_function_view
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.customerCode != null">
        customer_code = #{record.customerCode,jdbcType=VARCHAR},
      </if>
      <if test="record.permSysCode != null">
        perm_sys_code = #{record.permSysCode,jdbcType=VARCHAR},
      </if>
      <if test="record.menuOpenMode != null">
        menu_open_mode = #{record.menuOpenMode,jdbcType=TINYINT},
      </if>
      <if test="record.statDate != null">
        stat_date = #{record.statDate,jdbcType=DATE},
      </if>
      <if test="record.customerPv != null">
        customer_pv = #{record.customerPv,jdbcType=INTEGER},
      </if>
      <if test="record.customerUv != null">
        customer_uv = #{record.customerUv,jdbcType=INTEGER},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update statistics_function_view
    set id = #{record.id,jdbcType=INTEGER},
      customer_code = #{record.customerCode,jdbcType=VARCHAR},
      perm_sys_code = #{record.permSysCode,jdbcType=VARCHAR},
      menu_open_mode = #{record.menuOpenMode,jdbcType=TINYINT},
      stat_date = #{record.statDate,jdbcType=DATE},
      customer_pv = #{record.customerPv,jdbcType=INTEGER},
      customer_uv = #{record.customerUv,jdbcType=INTEGER},
      create_time = #{record.createTime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.business.entity.StatisticsFunctionView">
    update statistics_function_view
    <set>
      <if test="customerCode != null">
        customer_code = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="permSysCode != null">
        perm_sys_code = #{permSysCode,jdbcType=VARCHAR},
      </if>
      <if test="menuOpenMode != null">
        menu_open_mode = #{menuOpenMode,jdbcType=TINYINT},
      </if>
      <if test="statDate != null">
        stat_date = #{statDate,jdbcType=DATE},
      </if>
      <if test="customerPv != null">
        customer_pv = #{customerPv,jdbcType=INTEGER},
      </if>
      <if test="customerUv != null">
        customer_uv = #{customerUv,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.business.entity.StatisticsFunctionView">
    update statistics_function_view
    set customer_code = #{customerCode,jdbcType=VARCHAR},
      perm_sys_code = #{permSysCode,jdbcType=VARCHAR},
      menu_open_mode = #{menuOpenMode,jdbcType=TINYINT},
      stat_date = #{statDate,jdbcType=DATE},
      customer_pv = #{customerPv,jdbcType=INTEGER},
      customer_uv = #{customerUv,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>