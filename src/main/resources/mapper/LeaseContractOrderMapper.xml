<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.LeaseContractOrderMapper">
  <resultMap id="BaseResultMap" type="com.izu.business.entity.LeaseContractOrder">
    <id column="order_id" jdbcType="INTEGER" property="orderId" />
    <result column="order_code" jdbcType="VARCHAR" property="orderCode" />
    <result column="buss_contract_id" jdbcType="INTEGER" property="bussContractId" />
    <result column="buss_contract_code" jdbcType="VARCHAR" property="bussContractCode" />
    <result column="customer_id" jdbcType="INTEGER" property="customerId" />
    <result column="customer_code" jdbcType="VARCHAR" property="customerCode" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="order_sdate" jdbcType="TIMESTAMP" property="orderSdate" />
    <result column="order_edate" jdbcType="TIMESTAMP" property="orderEdate" />
    <result column="last_return_time" jdbcType="TIMESTAMP" property="lastReturnTime" />
    <result column="lease_period" jdbcType="INTEGER" property="leasePeriod" />
    <result column="month_rent_amount" jdbcType="DECIMAL" property="monthRentAmount" />
    <result column="vehicle_deposit_amount" jdbcType="DECIMAL" property="vehicleDepositAmount" />
    <result column="vehicle_license" jdbcType="VARCHAR" property="vehicleLicense" />
    <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode" />
    <result column="vehicle_model_name" jdbcType="VARCHAR" property="vehicleModelName" />
    <result column="order_status" jdbcType="INTEGER" property="orderStatus" />
    <result column="buss_status" jdbcType="INTEGER" property="bussStatus" />
    <result column="settle_status" jdbcType="INTEGER" property="settleStatus" />
    <result column="maintain_staff_id" jdbcType="INTEGER" property="maintainStaffId" />
    <result column="maintain_staff_name" jdbcType="VARCHAR" property="maintainStaffName" />
    <result column="maintain_staff_mobile" jdbcType="VARCHAR" property="maintainStaffMobile" />
    <result column="social_credit_identifier" jdbcType="VARCHAR" property="socialCreditIdentifier" />
    <result column="sign_city_code" jdbcType="VARCHAR" property="signCityCode" />
    <result column="sign_city_name" jdbcType="VARCHAR" property="signCityName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    order_id, order_code, buss_contract_id, buss_contract_code, customer_id, customer_code, 
    customer_name, order_sdate, order_edate, last_return_time, lease_period, month_rent_amount, 
    vehicle_deposit_amount, vehicle_license, vehicle_model_code, vehicle_model_name, 
    order_status, buss_status, settle_status, maintain_staff_id, maintain_staff_name, 
    maintain_staff_mobile, social_credit_identifier, sign_city_code, sign_city_name
  </sql>
  <select id="selectByExample" parameterType="com.izu.business.entity.LeaseContractOrderExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from lease_contract_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lease_contract_order
    where order_id = #{orderId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from lease_contract_order
    where order_id = #{orderId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.business.entity.LeaseContractOrderExample">
    delete from lease_contract_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.business.entity.LeaseContractOrder">
    <selectKey keyProperty="orderId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lease_contract_order (order_code, buss_contract_id, buss_contract_code, 
      customer_id, customer_code, customer_name, 
      order_sdate, order_edate, last_return_time, 
      lease_period, month_rent_amount, vehicle_deposit_amount, 
      vehicle_license, vehicle_model_code, vehicle_model_name, 
      order_status, buss_status, settle_status, 
      maintain_staff_id, maintain_staff_name, maintain_staff_mobile, 
      social_credit_identifier, sign_city_code, sign_city_name
      )
    values (#{orderCode,jdbcType=VARCHAR}, #{bussContractId,jdbcType=INTEGER}, #{bussContractCode,jdbcType=VARCHAR}, 
      #{customerId,jdbcType=INTEGER}, #{customerCode,jdbcType=VARCHAR}, #{customerName,jdbcType=VARCHAR}, 
      #{orderSdate,jdbcType=TIMESTAMP}, #{orderEdate,jdbcType=TIMESTAMP}, #{lastReturnTime,jdbcType=TIMESTAMP}, 
      #{leasePeriod,jdbcType=INTEGER}, #{monthRentAmount,jdbcType=DECIMAL}, #{vehicleDepositAmount,jdbcType=DECIMAL}, 
      #{vehicleLicense,jdbcType=VARCHAR}, #{vehicleModelCode,jdbcType=VARCHAR}, #{vehicleModelName,jdbcType=VARCHAR}, 
      #{orderStatus,jdbcType=INTEGER}, #{bussStatus,jdbcType=INTEGER}, #{settleStatus,jdbcType=INTEGER}, 
      #{maintainStaffId,jdbcType=INTEGER}, #{maintainStaffName,jdbcType=VARCHAR}, #{maintainStaffMobile,jdbcType=VARCHAR}, 
      #{socialCreditIdentifier,jdbcType=VARCHAR}, #{signCityCode,jdbcType=VARCHAR}, #{signCityName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.business.entity.LeaseContractOrder">
    <selectKey keyProperty="orderId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lease_contract_order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderCode != null">
        order_code,
      </if>
      <if test="bussContractId != null">
        buss_contract_id,
      </if>
      <if test="bussContractCode != null">
        buss_contract_code,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="customerCode != null">
        customer_code,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="orderSdate != null">
        order_sdate,
      </if>
      <if test="orderEdate != null">
        order_edate,
      </if>
      <if test="lastReturnTime != null">
        last_return_time,
      </if>
      <if test="leasePeriod != null">
        lease_period,
      </if>
      <if test="monthRentAmount != null">
        month_rent_amount,
      </if>
      <if test="vehicleDepositAmount != null">
        vehicle_deposit_amount,
      </if>
      <if test="vehicleLicense != null">
        vehicle_license,
      </if>
      <if test="vehicleModelCode != null">
        vehicle_model_code,
      </if>
      <if test="vehicleModelName != null">
        vehicle_model_name,
      </if>
      <if test="orderStatus != null">
        order_status,
      </if>
      <if test="bussStatus != null">
        buss_status,
      </if>
      <if test="settleStatus != null">
        settle_status,
      </if>
      <if test="maintainStaffId != null">
        maintain_staff_id,
      </if>
      <if test="maintainStaffName != null">
        maintain_staff_name,
      </if>
      <if test="maintainStaffMobile != null">
        maintain_staff_mobile,
      </if>
      <if test="socialCreditIdentifier != null">
        social_credit_identifier,
      </if>
      <if test="signCityCode != null">
        sign_city_code,
      </if>
      <if test="signCityName != null">
        sign_city_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderCode != null">
        #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="bussContractId != null">
        #{bussContractId,jdbcType=INTEGER},
      </if>
      <if test="bussContractCode != null">
        #{bussContractCode,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=INTEGER},
      </if>
      <if test="customerCode != null">
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="orderSdate != null">
        #{orderSdate,jdbcType=TIMESTAMP},
      </if>
      <if test="orderEdate != null">
        #{orderEdate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastReturnTime != null">
        #{lastReturnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="leasePeriod != null">
        #{leasePeriod,jdbcType=INTEGER},
      </if>
      <if test="monthRentAmount != null">
        #{monthRentAmount,jdbcType=DECIMAL},
      </if>
      <if test="vehicleDepositAmount != null">
        #{vehicleDepositAmount,jdbcType=DECIMAL},
      </if>
      <if test="vehicleLicense != null">
        #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelCode != null">
        #{vehicleModelCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelName != null">
        #{vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="bussStatus != null">
        #{bussStatus,jdbcType=INTEGER},
      </if>
      <if test="settleStatus != null">
        #{settleStatus,jdbcType=INTEGER},
      </if>
      <if test="maintainStaffId != null">
        #{maintainStaffId,jdbcType=INTEGER},
      </if>
      <if test="maintainStaffName != null">
        #{maintainStaffName,jdbcType=VARCHAR},
      </if>
      <if test="maintainStaffMobile != null">
        #{maintainStaffMobile,jdbcType=VARCHAR},
      </if>
      <if test="socialCreditIdentifier != null">
        #{socialCreditIdentifier,jdbcType=VARCHAR},
      </if>
      <if test="signCityCode != null">
        #{signCityCode,jdbcType=VARCHAR},
      </if>
      <if test="signCityName != null">
        #{signCityName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.business.entity.LeaseContractOrderExample" resultType="java.lang.Long">
    select count(*) from lease_contract_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update lease_contract_order
    <set>
      <if test="row.orderId != null">
        order_id = #{row.orderId,jdbcType=INTEGER},
      </if>
      <if test="row.orderCode != null">
        order_code = #{row.orderCode,jdbcType=VARCHAR},
      </if>
      <if test="row.bussContractId != null">
        buss_contract_id = #{row.bussContractId,jdbcType=INTEGER},
      </if>
      <if test="row.bussContractCode != null">
        buss_contract_code = #{row.bussContractCode,jdbcType=VARCHAR},
      </if>
      <if test="row.customerId != null">
        customer_id = #{row.customerId,jdbcType=INTEGER},
      </if>
      <if test="row.customerCode != null">
        customer_code = #{row.customerCode,jdbcType=VARCHAR},
      </if>
      <if test="row.customerName != null">
        customer_name = #{row.customerName,jdbcType=VARCHAR},
      </if>
      <if test="row.orderSdate != null">
        order_sdate = #{row.orderSdate,jdbcType=TIMESTAMP},
      </if>
      <if test="row.orderEdate != null">
        order_edate = #{row.orderEdate,jdbcType=TIMESTAMP},
      </if>
      <if test="row.lastReturnTime != null">
        last_return_time = #{row.lastReturnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.leasePeriod != null">
        lease_period = #{row.leasePeriod,jdbcType=INTEGER},
      </if>
      <if test="row.monthRentAmount != null">
        month_rent_amount = #{row.monthRentAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.vehicleDepositAmount != null">
        vehicle_deposit_amount = #{row.vehicleDepositAmount,jdbcType=DECIMAL},
      </if>
      <if test="row.vehicleLicense != null">
        vehicle_license = #{row.vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleModelCode != null">
        vehicle_model_code = #{row.vehicleModelCode,jdbcType=VARCHAR},
      </if>
      <if test="row.vehicleModelName != null">
        vehicle_model_name = #{row.vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="row.orderStatus != null">
        order_status = #{row.orderStatus,jdbcType=INTEGER},
      </if>
      <if test="row.bussStatus != null">
        buss_status = #{row.bussStatus,jdbcType=INTEGER},
      </if>
      <if test="row.settleStatus != null">
        settle_status = #{row.settleStatus,jdbcType=INTEGER},
      </if>
      <if test="row.maintainStaffId != null">
        maintain_staff_id = #{row.maintainStaffId,jdbcType=INTEGER},
      </if>
      <if test="row.maintainStaffName != null">
        maintain_staff_name = #{row.maintainStaffName,jdbcType=VARCHAR},
      </if>
      <if test="row.maintainStaffMobile != null">
        maintain_staff_mobile = #{row.maintainStaffMobile,jdbcType=VARCHAR},
      </if>
      <if test="row.socialCreditIdentifier != null">
        social_credit_identifier = #{row.socialCreditIdentifier,jdbcType=VARCHAR},
      </if>
      <if test="row.signCityCode != null">
        sign_city_code = #{row.signCityCode,jdbcType=VARCHAR},
      </if>
      <if test="row.signCityName != null">
        sign_city_name = #{row.signCityName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update lease_contract_order
    set order_id = #{row.orderId,jdbcType=INTEGER},
      order_code = #{row.orderCode,jdbcType=VARCHAR},
      buss_contract_id = #{row.bussContractId,jdbcType=INTEGER},
      buss_contract_code = #{row.bussContractCode,jdbcType=VARCHAR},
      customer_id = #{row.customerId,jdbcType=INTEGER},
      customer_code = #{row.customerCode,jdbcType=VARCHAR},
      customer_name = #{row.customerName,jdbcType=VARCHAR},
      order_sdate = #{row.orderSdate,jdbcType=TIMESTAMP},
      order_edate = #{row.orderEdate,jdbcType=TIMESTAMP},
      last_return_time = #{row.lastReturnTime,jdbcType=TIMESTAMP},
      lease_period = #{row.leasePeriod,jdbcType=INTEGER},
      month_rent_amount = #{row.monthRentAmount,jdbcType=DECIMAL},
      vehicle_deposit_amount = #{row.vehicleDepositAmount,jdbcType=DECIMAL},
      vehicle_license = #{row.vehicleLicense,jdbcType=VARCHAR},
      vehicle_model_code = #{row.vehicleModelCode,jdbcType=VARCHAR},
      vehicle_model_name = #{row.vehicleModelName,jdbcType=VARCHAR},
      order_status = #{row.orderStatus,jdbcType=INTEGER},
      buss_status = #{row.bussStatus,jdbcType=INTEGER},
      settle_status = #{row.settleStatus,jdbcType=INTEGER},
      maintain_staff_id = #{row.maintainStaffId,jdbcType=INTEGER},
      maintain_staff_name = #{row.maintainStaffName,jdbcType=VARCHAR},
      maintain_staff_mobile = #{row.maintainStaffMobile,jdbcType=VARCHAR},
      social_credit_identifier = #{row.socialCreditIdentifier,jdbcType=VARCHAR},
      sign_city_code = #{row.signCityCode,jdbcType=VARCHAR},
      sign_city_name = #{row.signCityName,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.business.entity.LeaseContractOrder">
    update lease_contract_order
    <set>
      <if test="orderCode != null">
        order_code = #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="bussContractId != null">
        buss_contract_id = #{bussContractId,jdbcType=INTEGER},
      </if>
      <if test="bussContractCode != null">
        buss_contract_code = #{bussContractCode,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=INTEGER},
      </if>
      <if test="customerCode != null">
        customer_code = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="customerName != null">
        customer_name = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="orderSdate != null">
        order_sdate = #{orderSdate,jdbcType=TIMESTAMP},
      </if>
      <if test="orderEdate != null">
        order_edate = #{orderEdate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastReturnTime != null">
        last_return_time = #{lastReturnTime,jdbcType=TIMESTAMP},
      </if>
      <if test="leasePeriod != null">
        lease_period = #{leasePeriod,jdbcType=INTEGER},
      </if>
      <if test="monthRentAmount != null">
        month_rent_amount = #{monthRentAmount,jdbcType=DECIMAL},
      </if>
      <if test="vehicleDepositAmount != null">
        vehicle_deposit_amount = #{vehicleDepositAmount,jdbcType=DECIMAL},
      </if>
      <if test="vehicleLicense != null">
        vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelCode != null">
        vehicle_model_code = #{vehicleModelCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleModelName != null">
        vehicle_model_name = #{vehicleModelName,jdbcType=VARCHAR},
      </if>
      <if test="orderStatus != null">
        order_status = #{orderStatus,jdbcType=INTEGER},
      </if>
      <if test="bussStatus != null">
        buss_status = #{bussStatus,jdbcType=INTEGER},
      </if>
      <if test="settleStatus != null">
        settle_status = #{settleStatus,jdbcType=INTEGER},
      </if>
      <if test="maintainStaffId != null">
        maintain_staff_id = #{maintainStaffId,jdbcType=INTEGER},
      </if>
      <if test="maintainStaffName != null">
        maintain_staff_name = #{maintainStaffName,jdbcType=VARCHAR},
      </if>
      <if test="maintainStaffMobile != null">
        maintain_staff_mobile = #{maintainStaffMobile,jdbcType=VARCHAR},
      </if>
      <if test="socialCreditIdentifier != null">
        social_credit_identifier = #{socialCreditIdentifier,jdbcType=VARCHAR},
      </if>
      <if test="signCityCode != null">
        sign_city_code = #{signCityCode,jdbcType=VARCHAR},
      </if>
      <if test="signCityName != null">
        sign_city_name = #{signCityName,jdbcType=VARCHAR},
      </if>
    </set>
    where order_id = #{orderId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.business.entity.LeaseContractOrder">
    update lease_contract_order
    set order_code = #{orderCode,jdbcType=VARCHAR},
      buss_contract_id = #{bussContractId,jdbcType=INTEGER},
      buss_contract_code = #{bussContractCode,jdbcType=VARCHAR},
      customer_id = #{customerId,jdbcType=INTEGER},
      customer_code = #{customerCode,jdbcType=VARCHAR},
      customer_name = #{customerName,jdbcType=VARCHAR},
      order_sdate = #{orderSdate,jdbcType=TIMESTAMP},
      order_edate = #{orderEdate,jdbcType=TIMESTAMP},
      last_return_time = #{lastReturnTime,jdbcType=TIMESTAMP},
      lease_period = #{leasePeriod,jdbcType=INTEGER},
      month_rent_amount = #{monthRentAmount,jdbcType=DECIMAL},
      vehicle_deposit_amount = #{vehicleDepositAmount,jdbcType=DECIMAL},
      vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      vehicle_model_code = #{vehicleModelCode,jdbcType=VARCHAR},
      vehicle_model_name = #{vehicleModelName,jdbcType=VARCHAR},
      order_status = #{orderStatus,jdbcType=INTEGER},
      buss_status = #{bussStatus,jdbcType=INTEGER},
      settle_status = #{settleStatus,jdbcType=INTEGER},
      maintain_staff_id = #{maintainStaffId,jdbcType=INTEGER},
      maintain_staff_name = #{maintainStaffName,jdbcType=VARCHAR},
      maintain_staff_mobile = #{maintainStaffMobile,jdbcType=VARCHAR},
      social_credit_identifier = #{socialCreditIdentifier,jdbcType=VARCHAR},
      sign_city_code = #{signCityCode,jdbcType=VARCHAR},
      sign_city_name = #{signCityName,jdbcType=VARCHAR}
    where order_id = #{orderId,jdbcType=INTEGER}
  </update>
</mapper>