<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.FileInfoMapper">
  <resultMap id="BaseResultMap" type="com.izu.user.entity.FileInfo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="file_type" jdbcType="INTEGER" property="fileType" />
    <result column="relation_id" jdbcType="INTEGER" property="relationId" />
    <result column="relation_code" jdbcType="VARCHAR" property="relationCode" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="file_url" jdbcType="VARCHAR" property="fileUrl" />
    <result column="yn" jdbcType="TINYINT" property="yn" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, file_type, relation_id, relation_code, file_name, file_url, yn, create_time, 
    update_time
  </sql>
  <select id="selectByExample" parameterType="com.izu.user.entity.FileInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_file_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_file_info
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from t_file_info
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.user.entity.FileInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into t_file_info (file_type, relation_id, relation_code, 
      file_name, file_url, yn, 
      create_time, update_time)
    values (#{fileType,jdbcType=INTEGER}, #{relationId,jdbcType=INTEGER}, #{relationCode,jdbcType=VARCHAR}, 
      #{fileName,jdbcType=VARCHAR}, #{fileUrl,jdbcType=VARCHAR}, #{yn,jdbcType=TINYINT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.user.entity.FileInfo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into t_file_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="fileType != null">
        file_type,
      </if>
      <if test="relationId != null">
        relation_id,
      </if>
      <if test="relationCode != null">
        relation_code,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="fileUrl != null">
        file_url,
      </if>
      <if test="yn != null">
        yn,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="fileType != null">
        #{fileType,jdbcType=INTEGER},
      </if>
      <if test="relationId != null">
        #{relationId,jdbcType=INTEGER},
      </if>
      <if test="relationCode != null">
        #{relationCode,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileUrl != null">
        #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="yn != null">
        #{yn,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.user.entity.FileInfo">
    update t_file_info
    <set>
      <if test="fileType != null">
        file_type = #{fileType,jdbcType=INTEGER},
      </if>
      <if test="relationId != null">
        relation_id = #{relationId,jdbcType=INTEGER},
      </if>
      <if test="relationCode != null">
        relation_code = #{relationCode,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="fileUrl != null">
        file_url = #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="yn != null">
        yn = #{yn,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.user.entity.FileInfo">
    update t_file_info
    set file_type = #{fileType,jdbcType=INTEGER},
      relation_id = #{relationId,jdbcType=INTEGER},
      relation_code = #{relationCode,jdbcType=VARCHAR},
      file_name = #{fileName,jdbcType=VARCHAR},
      file_url = #{fileUrl,jdbcType=VARCHAR},
      yn = #{yn,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>