<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.LanguageMapper">
  <resultMap id="BaseResultMap" type="com.izu.user.entity.Language">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="mobile_phone" jdbcType="VARCHAR" property="mobilePhone" />
    <result column="SID" jdbcType="VARCHAR" property="sid" />
    <result column="language" jdbcType="VARCHAR" property="language" />
    <result column="yn" jdbcType="INTEGER" property="yn" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, mobile_phone, SID, language, yn, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_language
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from t_language
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.izu.user.entity.Language">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into t_language (mobile_phone, SID, language, 
      yn, create_time, update_time
      )
    values (#{mobilePhone,jdbcType=VARCHAR}, #{sid,jdbcType=VARCHAR}, #{language,jdbcType=VARCHAR}, 
      #{yn,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.user.entity.Language">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into t_language
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="mobilePhone != null">
        mobile_phone,
      </if>
      <if test="sid != null">
        SID,
      </if>
      <if test="language != null">
        language,
      </if>
      <if test="yn != null">
        yn,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="mobilePhone != null">
        #{mobilePhone,jdbcType=VARCHAR},
      </if>
      <if test="sid != null">
        #{sid,jdbcType=VARCHAR},
      </if>
      <if test="language != null">
        #{language,jdbcType=VARCHAR},
      </if>
      <if test="yn != null">
        #{yn,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.user.entity.Language">
    update t_language
    <set>
      <if test="mobilePhone != null">
        mobile_phone = #{mobilePhone,jdbcType=VARCHAR},
      </if>
      <if test="sid != null">
        SID = #{sid,jdbcType=VARCHAR},
      </if>
      <if test="language != null">
        language = #{language,jdbcType=VARCHAR},
      </if>
      <if test="yn != null">
        yn = #{yn,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.user.entity.Language">
    update t_language
    set mobile_phone = #{mobilePhone,jdbcType=VARCHAR},
      SID = #{sid,jdbcType=VARCHAR},
      language = #{language,jdbcType=VARCHAR},
      yn = #{yn,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>