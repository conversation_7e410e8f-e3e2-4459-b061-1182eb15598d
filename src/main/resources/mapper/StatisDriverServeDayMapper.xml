<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.StatisDriverServeDayMapper">
  <resultMap id="BaseResultMap" type="com.izu.business.entity.StatisDriverServeDay">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="driver_id" jdbcType="BIGINT" property="driverId" />
    <result column="driver_name" jdbcType="VARCHAR" property="driverName" />
    <result column="city_code" jdbcType="INTEGER" property="cityCode" />
    <result column="city_name" jdbcType="VARCHAR" property="cityName" />
    <result column="completed_order_num" jdbcType="INTEGER" property="completedOrderNum" />
    <result column="serve_period" jdbcType="DECIMAL" property="servePeriod" />
    <result column="effective_mileage" jdbcType="DECIMAL" property="effectiveMileage" />
    <result column="statis_date" jdbcType="VARCHAR" property="statisDate" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, driver_id, driver_name, city_code, city_name, completed_order_num, serve_period, 
    effective_mileage, statis_date, update_time, create_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from statis_driver_serve_day
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from statis_driver_serve_day
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.izu.business.entity.StatisDriverServeDay">
    insert into statis_driver_serve_day (id, driver_id, driver_name, 
      city_code, city_name, completed_order_num, 
      serve_period, effective_mileage, statis_date, 
      update_time, create_time)
    values (#{id,jdbcType=BIGINT}, #{driverId,jdbcType=BIGINT}, #{driverName,jdbcType=VARCHAR}, 
      #{cityCode,jdbcType=INTEGER}, #{cityName,jdbcType=VARCHAR}, #{completedOrderNum,jdbcType=INTEGER}, 
      #{servePeriod,jdbcType=DECIMAL}, #{effectiveMileage,jdbcType=DECIMAL}, #{statisDate,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.business.entity.StatisDriverServeDay">
    insert into statis_driver_serve_day
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="driverId != null">
        driver_id,
      </if>
      <if test="driverName != null">
        driver_name,
      </if>
      <if test="cityCode != null">
        city_code,
      </if>
      <if test="cityName != null">
        city_name,
      </if>
      <if test="completedOrderNum != null">
        completed_order_num,
      </if>
      <if test="servePeriod != null">
        serve_period,
      </if>
      <if test="effectiveMileage != null">
        effective_mileage,
      </if>
      <if test="statisDate != null">
        statis_date,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="driverId != null">
        #{driverId,jdbcType=BIGINT},
      </if>
      <if test="driverName != null">
        #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        #{cityCode,jdbcType=INTEGER},
      </if>
      <if test="cityName != null">
        #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="completedOrderNum != null">
        #{completedOrderNum,jdbcType=INTEGER},
      </if>
      <if test="servePeriod != null">
        #{servePeriod,jdbcType=DECIMAL},
      </if>
      <if test="effectiveMileage != null">
        #{effectiveMileage,jdbcType=DECIMAL},
      </if>
      <if test="statisDate != null">
        #{statisDate,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.business.entity.StatisDriverServeDay">
    update statis_driver_serve_day
    <set>
      <if test="driverId != null">
        driver_id = #{driverId,jdbcType=BIGINT},
      </if>
      <if test="driverName != null">
        driver_name = #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="cityCode != null">
        city_code = #{cityCode,jdbcType=INTEGER},
      </if>
      <if test="cityName != null">
        city_name = #{cityName,jdbcType=VARCHAR},
      </if>
      <if test="completedOrderNum != null">
        completed_order_num = #{completedOrderNum,jdbcType=INTEGER},
      </if>
      <if test="servePeriod != null">
        serve_period = #{servePeriod,jdbcType=DECIMAL},
      </if>
      <if test="effectiveMileage != null">
        effective_mileage = #{effectiveMileage,jdbcType=DECIMAL},
      </if>
      <if test="statisDate != null">
        statis_date = #{statisDate,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.business.entity.StatisDriverServeDay">
    update statis_driver_serve_day
    set driver_id = #{driverId,jdbcType=BIGINT},
      driver_name = #{driverName,jdbcType=VARCHAR},
      city_code = #{cityCode,jdbcType=INTEGER},
      city_name = #{cityName,jdbcType=VARCHAR},
      completed_order_num = #{completedOrderNum,jdbcType=INTEGER},
      serve_period = #{servePeriod,jdbcType=DECIMAL},
      effective_mileage = #{effectiveMileage,jdbcType=DECIMAL},
      statis_date = #{statisDate,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>