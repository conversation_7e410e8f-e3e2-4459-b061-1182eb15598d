<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.LeaseSettlementMapper">
  <resultMap id="BaseResultMap" type="com.izu.business.entity.LeaseSettlement">
    <id column="settlement_id" jdbcType="INTEGER" property="settlementId" />
    <result column="settlement_code" jdbcType="VARCHAR" property="settlementCode" />
    <result column="settlement_status" jdbcType="TINYINT" property="settlementStatus" />
    <result column="settlement_flag" jdbcType="VARCHAR" property="settlementFlag" />
    <result column="settlement_confirm_start_time" jdbcType="TIMESTAMP" property="settlementConfirmStartTime" />
    <result column="settlement_confirm_finish_time" jdbcType="TIMESTAMP" property="settlementConfirmFinishTime" />
    <result column="settlement_apply_id" jdbcType="VARCHAR" property="settlementApplyId" />
    <result column="settlement_apply_finish_time" jdbcType="TIMESTAMP" property="settlementApplyFinishTime" />
    <result column="create_id" jdbcType="VARCHAR" property="createId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_buss_code" jdbcType="VARCHAR" property="createBussCode" />
    <result column="create_buss_name" jdbcType="VARCHAR" property="createBussName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_id" jdbcType="VARCHAR" property="updateId" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="update_buss_code" jdbcType="VARCHAR" property="updateBussCode" />
    <result column="update_buss_name" jdbcType="VARCHAR" property="updateBussName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="contract_id" jdbcType="INTEGER" property="contractId" />
    <result column="contract_code" jdbcType="VARCHAR" property="contractCode" />
    <result column="order_id" jdbcType="INTEGER" property="orderId" />
    <result column="order_code" jdbcType="VARCHAR" property="orderCode" />
    <result column="vehicle_vin" jdbcType="VARCHAR" property="vehicleVin" />
    <result column="vehicle_license" jdbcType="VARCHAR" property="vehicleLicense" />
    <result column="vehicle_serial_no" jdbcType="VARCHAR" property="vehicleSerialNo" />
    <result column="belong_buss_code" jdbcType="VARCHAR" property="belongBussCode" />
    <result column="belong_buss_name" jdbcType="VARCHAR" property="belongBussName" />
    <result column="operate_buss_code" jdbcType="VARCHAR" property="operateBussCode" />
    <result column="operate_buss_name" jdbcType="VARCHAR" property="operateBussName" />
    <result column="customer_id" jdbcType="INTEGER" property="customerId" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="sign_subject_name" jdbcType="VARCHAR" property="signSubjectName" />
    <result column="sign_subject_tax_code" jdbcType="VARCHAR" property="signSubjectTaxCode" />
    <result column="deposit_yingshou_amount" jdbcType="DECIMAL" property="depositYingshouAmount" />
    <result column="deposit_received_amount" jdbcType="DECIMAL" property="depositReceivedAmount" />
    <result column="deposit_refund_amount" jdbcType="DECIMAL" property="depositRefundAmount" />
    <result column="deposit_carry_amount" jdbcType="DECIMAL" property="depositCarryAmount" />
    <result column="total_yingshou_amount" jdbcType="DECIMAL" property="totalYingshouAmount" />
    <result column="total_reduce_amount" jdbcType="DECIMAL" property="totalReduceAmount" />
    <result column="total_received_amount" jdbcType="DECIMAL" property="totalReceivedAmount" />
    <result column="total_contract_pay_amount" jdbcType="DECIMAL" property="totalContractPayAmount" />
    <result column="total_refund_amount" jdbcType="DECIMAL" property="totalRefundAmount" />
    <result column="penalty_yingshou_amount" jdbcType="DECIMAL" property="penaltyYingshouAmount" />
    <result column="arrears_amount" jdbcType="DECIMAL" property="arrearsAmount" />
    <result column="reduce_reason" jdbcType="VARCHAR" property="reduceReason" />
    <result column="refund_process_status" jdbcType="TINYINT" property="refundProcessStatus" />
  </resultMap>
  <sql id="Base_Column_List">
    settlement_id, settlement_code, settlement_status, settlement_flag, settlement_confirm_start_time, 
    settlement_confirm_finish_time, settlement_apply_id, settlement_apply_finish_time, 
    create_id, create_name, create_buss_code, create_buss_name, create_time, update_id, 
    update_name, update_buss_code, update_buss_name, update_time, contract_id, contract_code, 
    order_id, order_code, vehicle_vin, vehicle_license, vehicle_serial_no, belong_buss_code, 
    belong_buss_name, operate_buss_code, operate_buss_name, customer_id, customer_name, 
    sign_subject_name, sign_subject_tax_code, deposit_yingshou_amount, deposit_received_amount, 
    deposit_refund_amount, deposit_carry_amount, total_yingshou_amount, total_reduce_amount, 
    total_received_amount, total_contract_pay_amount, total_refund_amount, penalty_yingshou_amount, 
    arrears_amount, reduce_reason, refund_process_status
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from lease_settlement
    where settlement_id = #{settlementId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from lease_settlement
    where settlement_id = #{settlementId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.business.entity.LeaseSettlement">
    <selectKey keyProperty="settlementId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lease_settlement (settlement_code, settlement_status, 
      settlement_flag, settlement_confirm_start_time, 
      settlement_confirm_finish_time, settlement_apply_id, 
      settlement_apply_finish_time, create_id, 
      create_name, create_buss_code, create_buss_name, 
      create_time, update_id, update_name, 
      update_buss_code, update_buss_name, update_time, 
      contract_id, contract_code, order_id, 
      order_code, vehicle_vin, vehicle_license, 
      vehicle_serial_no, belong_buss_code, belong_buss_name, 
      operate_buss_code, operate_buss_name, customer_id, 
      customer_name, sign_subject_name, sign_subject_tax_code, 
      deposit_yingshou_amount, deposit_received_amount, 
      deposit_refund_amount, deposit_carry_amount, 
      total_yingshou_amount, total_reduce_amount, 
      total_received_amount, total_contract_pay_amount, 
      total_refund_amount, penalty_yingshou_amount, 
      arrears_amount, reduce_reason, refund_process_status
      )
    values (#{settlementCode,jdbcType=VARCHAR}, #{settlementStatus,jdbcType=TINYINT}, 
      #{settlementFlag,jdbcType=VARCHAR}, #{settlementConfirmStartTime,jdbcType=TIMESTAMP}, 
      #{settlementConfirmFinishTime,jdbcType=TIMESTAMP}, #{settlementApplyId,jdbcType=VARCHAR}, 
      #{settlementApplyFinishTime,jdbcType=TIMESTAMP}, #{createId,jdbcType=VARCHAR}, 
      #{createName,jdbcType=VARCHAR}, #{createBussCode,jdbcType=VARCHAR}, #{createBussName,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateId,jdbcType=VARCHAR}, #{updateName,jdbcType=VARCHAR}, 
      #{updateBussCode,jdbcType=VARCHAR}, #{updateBussName,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{contractId,jdbcType=INTEGER}, #{contractCode,jdbcType=VARCHAR}, #{orderId,jdbcType=INTEGER}, 
      #{orderCode,jdbcType=VARCHAR}, #{vehicleVin,jdbcType=VARCHAR}, #{vehicleLicense,jdbcType=VARCHAR}, 
      #{vehicleSerialNo,jdbcType=VARCHAR}, #{belongBussCode,jdbcType=VARCHAR}, #{belongBussName,jdbcType=VARCHAR}, 
      #{operateBussCode,jdbcType=VARCHAR}, #{operateBussName,jdbcType=VARCHAR}, #{customerId,jdbcType=INTEGER}, 
      #{customerName,jdbcType=VARCHAR}, #{signSubjectName,jdbcType=VARCHAR}, #{signSubjectTaxCode,jdbcType=VARCHAR}, 
      #{depositYingshouAmount,jdbcType=DECIMAL}, #{depositReceivedAmount,jdbcType=DECIMAL}, 
      #{depositRefundAmount,jdbcType=DECIMAL}, #{depositCarryAmount,jdbcType=DECIMAL}, 
      #{totalYingshouAmount,jdbcType=DECIMAL}, #{totalReduceAmount,jdbcType=DECIMAL}, 
      #{totalReceivedAmount,jdbcType=DECIMAL}, #{totalContractPayAmount,jdbcType=DECIMAL}, 
      #{totalRefundAmount,jdbcType=DECIMAL}, #{penaltyYingshouAmount,jdbcType=DECIMAL}, 
      #{arrearsAmount,jdbcType=DECIMAL}, #{reduceReason,jdbcType=VARCHAR}, #{refundProcessStatus,jdbcType=TINYINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.izu.business.entity.LeaseSettlement">
    <selectKey keyProperty="settlementId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into lease_settlement
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="settlementCode != null">
        settlement_code,
      </if>
      <if test="settlementStatus != null">
        settlement_status,
      </if>
      <if test="settlementFlag != null">
        settlement_flag,
      </if>
      <if test="settlementConfirmStartTime != null">
        settlement_confirm_start_time,
      </if>
      <if test="settlementConfirmFinishTime != null">
        settlement_confirm_finish_time,
      </if>
      <if test="settlementApplyId != null">
        settlement_apply_id,
      </if>
      <if test="settlementApplyFinishTime != null">
        settlement_apply_finish_time,
      </if>
      <if test="createId != null">
        create_id,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createBussCode != null">
        create_buss_code,
      </if>
      <if test="createBussName != null">
        create_buss_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateId != null">
        update_id,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
      <if test="updateBussCode != null">
        update_buss_code,
      </if>
      <if test="updateBussName != null">
        update_buss_name,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="contractId != null">
        contract_id,
      </if>
      <if test="contractCode != null">
        contract_code,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderCode != null">
        order_code,
      </if>
      <if test="vehicleVin != null">
        vehicle_vin,
      </if>
      <if test="vehicleLicense != null">
        vehicle_license,
      </if>
      <if test="vehicleSerialNo != null">
        vehicle_serial_no,
      </if>
      <if test="belongBussCode != null">
        belong_buss_code,
      </if>
      <if test="belongBussName != null">
        belong_buss_name,
      </if>
      <if test="operateBussCode != null">
        operate_buss_code,
      </if>
      <if test="operateBussName != null">
        operate_buss_name,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="customerName != null">
        customer_name,
      </if>
      <if test="signSubjectName != null">
        sign_subject_name,
      </if>
      <if test="signSubjectTaxCode != null">
        sign_subject_tax_code,
      </if>
      <if test="depositYingshouAmount != null">
        deposit_yingshou_amount,
      </if>
      <if test="depositReceivedAmount != null">
        deposit_received_amount,
      </if>
      <if test="depositRefundAmount != null">
        deposit_refund_amount,
      </if>
      <if test="depositCarryAmount != null">
        deposit_carry_amount,
      </if>
      <if test="totalYingshouAmount != null">
        total_yingshou_amount,
      </if>
      <if test="totalReduceAmount != null">
        total_reduce_amount,
      </if>
      <if test="totalReceivedAmount != null">
        total_received_amount,
      </if>
      <if test="totalContractPayAmount != null">
        total_contract_pay_amount,
      </if>
      <if test="totalRefundAmount != null">
        total_refund_amount,
      </if>
      <if test="penaltyYingshouAmount != null">
        penalty_yingshou_amount,
      </if>
      <if test="arrearsAmount != null">
        arrears_amount,
      </if>
      <if test="reduceReason != null">
        reduce_reason,
      </if>
      <if test="refundProcessStatus != null">
        refund_process_status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="settlementCode != null">
        #{settlementCode,jdbcType=VARCHAR},
      </if>
      <if test="settlementStatus != null">
        #{settlementStatus,jdbcType=TINYINT},
      </if>
      <if test="settlementFlag != null">
        #{settlementFlag,jdbcType=VARCHAR},
      </if>
      <if test="settlementConfirmStartTime != null">
        #{settlementConfirmStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="settlementConfirmFinishTime != null">
        #{settlementConfirmFinishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="settlementApplyId != null">
        #{settlementApplyId,jdbcType=VARCHAR},
      </if>
      <if test="settlementApplyFinishTime != null">
        #{settlementApplyFinishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createId != null">
        #{createId,jdbcType=VARCHAR},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createBussCode != null">
        #{createBussCode,jdbcType=VARCHAR},
      </if>
      <if test="createBussName != null">
        #{createBussName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        #{updateId,jdbcType=VARCHAR},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateBussCode != null">
        #{updateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="updateBussName != null">
        #{updateBussName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contractId != null">
        #{contractId,jdbcType=INTEGER},
      </if>
      <if test="contractCode != null">
        #{contractCode,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderCode != null">
        #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleVin != null">
        #{vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="vehicleLicense != null">
        #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="vehicleSerialNo != null">
        #{vehicleSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="belongBussCode != null">
        #{belongBussCode,jdbcType=VARCHAR},
      </if>
      <if test="belongBussName != null">
        #{belongBussName,jdbcType=VARCHAR},
      </if>
      <if test="operateBussCode != null">
        #{operateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="operateBussName != null">
        #{operateBussName,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=INTEGER},
      </if>
      <if test="customerName != null">
        #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="signSubjectName != null">
        #{signSubjectName,jdbcType=VARCHAR},
      </if>
      <if test="signSubjectTaxCode != null">
        #{signSubjectTaxCode,jdbcType=VARCHAR},
      </if>
      <if test="depositYingshouAmount != null">
        #{depositYingshouAmount,jdbcType=DECIMAL},
      </if>
      <if test="depositReceivedAmount != null">
        #{depositReceivedAmount,jdbcType=DECIMAL},
      </if>
      <if test="depositRefundAmount != null">
        #{depositRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="depositCarryAmount != null">
        #{depositCarryAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalYingshouAmount != null">
        #{totalYingshouAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalReduceAmount != null">
        #{totalReduceAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalReceivedAmount != null">
        #{totalReceivedAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalContractPayAmount != null">
        #{totalContractPayAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalRefundAmount != null">
        #{totalRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="penaltyYingshouAmount != null">
        #{penaltyYingshouAmount,jdbcType=DECIMAL},
      </if>
      <if test="arrearsAmount != null">
        #{arrearsAmount,jdbcType=DECIMAL},
      </if>
      <if test="reduceReason != null">
        #{reduceReason,jdbcType=VARCHAR},
      </if>
      <if test="refundProcessStatus != null">
        #{refundProcessStatus,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.business.entity.LeaseSettlement">
    update lease_settlement
    <set>
      <if test="settlementCode != null">
        settlement_code = #{settlementCode,jdbcType=VARCHAR},
      </if>
      <if test="settlementStatus != null">
        settlement_status = #{settlementStatus,jdbcType=TINYINT},
      </if>
      <if test="settlementFlag != null">
        settlement_flag = #{settlementFlag,jdbcType=VARCHAR},
      </if>
      <if test="settlementConfirmStartTime != null">
        settlement_confirm_start_time = #{settlementConfirmStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="settlementConfirmFinishTime != null">
        settlement_confirm_finish_time = #{settlementConfirmFinishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="settlementApplyId != null">
        settlement_apply_id = #{settlementApplyId,jdbcType=VARCHAR},
      </if>
      <if test="settlementApplyFinishTime != null">
        settlement_apply_finish_time = #{settlementApplyFinishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createId != null">
        create_id = #{createId,jdbcType=VARCHAR},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createBussCode != null">
        create_buss_code = #{createBussCode,jdbcType=VARCHAR},
      </if>
      <if test="createBussName != null">
        create_buss_name = #{createBussName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        update_id = #{updateId,jdbcType=VARCHAR},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
      <if test="updateBussCode != null">
        update_buss_code = #{updateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="updateBussName != null">
        update_buss_name = #{updateBussName,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="contractId != null">
        contract_id = #{contractId,jdbcType=INTEGER},
      </if>
      <if test="contractCode != null">
        contract_code = #{contractCode,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderCode != null">
        order_code = #{orderCode,jdbcType=VARCHAR},
      </if>
      <if test="vehicleVin != null">
        vehicle_vin = #{vehicleVin,jdbcType=VARCHAR},
      </if>
      <if test="vehicleLicense != null">
        vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      </if>
      <if test="vehicleSerialNo != null">
        vehicle_serial_no = #{vehicleSerialNo,jdbcType=VARCHAR},
      </if>
      <if test="belongBussCode != null">
        belong_buss_code = #{belongBussCode,jdbcType=VARCHAR},
      </if>
      <if test="belongBussName != null">
        belong_buss_name = #{belongBussName,jdbcType=VARCHAR},
      </if>
      <if test="operateBussCode != null">
        operate_buss_code = #{operateBussCode,jdbcType=VARCHAR},
      </if>
      <if test="operateBussName != null">
        operate_buss_name = #{operateBussName,jdbcType=VARCHAR},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=INTEGER},
      </if>
      <if test="customerName != null">
        customer_name = #{customerName,jdbcType=VARCHAR},
      </if>
      <if test="signSubjectName != null">
        sign_subject_name = #{signSubjectName,jdbcType=VARCHAR},
      </if>
      <if test="signSubjectTaxCode != null">
        sign_subject_tax_code = #{signSubjectTaxCode,jdbcType=VARCHAR},
      </if>
      <if test="depositYingshouAmount != null">
        deposit_yingshou_amount = #{depositYingshouAmount,jdbcType=DECIMAL},
      </if>
      <if test="depositReceivedAmount != null">
        deposit_received_amount = #{depositReceivedAmount,jdbcType=DECIMAL},
      </if>
      <if test="depositRefundAmount != null">
        deposit_refund_amount = #{depositRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="depositCarryAmount != null">
        deposit_carry_amount = #{depositCarryAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalYingshouAmount != null">
        total_yingshou_amount = #{totalYingshouAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalReduceAmount != null">
        total_reduce_amount = #{totalReduceAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalReceivedAmount != null">
        total_received_amount = #{totalReceivedAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalContractPayAmount != null">
        total_contract_pay_amount = #{totalContractPayAmount,jdbcType=DECIMAL},
      </if>
      <if test="totalRefundAmount != null">
        total_refund_amount = #{totalRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="penaltyYingshouAmount != null">
        penalty_yingshou_amount = #{penaltyYingshouAmount,jdbcType=DECIMAL},
      </if>
      <if test="arrearsAmount != null">
        arrears_amount = #{arrearsAmount,jdbcType=DECIMAL},
      </if>
      <if test="reduceReason != null">
        reduce_reason = #{reduceReason,jdbcType=VARCHAR},
      </if>
      <if test="refundProcessStatus != null">
        refund_process_status = #{refundProcessStatus,jdbcType=TINYINT},
      </if>
    </set>
    where settlement_id = #{settlementId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.business.entity.LeaseSettlement">
    update lease_settlement
    set settlement_code = #{settlementCode,jdbcType=VARCHAR},
      settlement_status = #{settlementStatus,jdbcType=TINYINT},
      settlement_flag = #{settlementFlag,jdbcType=VARCHAR},
      settlement_confirm_start_time = #{settlementConfirmStartTime,jdbcType=TIMESTAMP},
      settlement_confirm_finish_time = #{settlementConfirmFinishTime,jdbcType=TIMESTAMP},
      settlement_apply_id = #{settlementApplyId,jdbcType=VARCHAR},
      settlement_apply_finish_time = #{settlementApplyFinishTime,jdbcType=TIMESTAMP},
      create_id = #{createId,jdbcType=VARCHAR},
      create_name = #{createName,jdbcType=VARCHAR},
      create_buss_code = #{createBussCode,jdbcType=VARCHAR},
      create_buss_name = #{createBussName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_id = #{updateId,jdbcType=VARCHAR},
      update_name = #{updateName,jdbcType=VARCHAR},
      update_buss_code = #{updateBussCode,jdbcType=VARCHAR},
      update_buss_name = #{updateBussName,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      contract_id = #{contractId,jdbcType=INTEGER},
      contract_code = #{contractCode,jdbcType=VARCHAR},
      order_id = #{orderId,jdbcType=INTEGER},
      order_code = #{orderCode,jdbcType=VARCHAR},
      vehicle_vin = #{vehicleVin,jdbcType=VARCHAR},
      vehicle_license = #{vehicleLicense,jdbcType=VARCHAR},
      vehicle_serial_no = #{vehicleSerialNo,jdbcType=VARCHAR},
      belong_buss_code = #{belongBussCode,jdbcType=VARCHAR},
      belong_buss_name = #{belongBussName,jdbcType=VARCHAR},
      operate_buss_code = #{operateBussCode,jdbcType=VARCHAR},
      operate_buss_name = #{operateBussName,jdbcType=VARCHAR},
      customer_id = #{customerId,jdbcType=INTEGER},
      customer_name = #{customerName,jdbcType=VARCHAR},
      sign_subject_name = #{signSubjectName,jdbcType=VARCHAR},
      sign_subject_tax_code = #{signSubjectTaxCode,jdbcType=VARCHAR},
      deposit_yingshou_amount = #{depositYingshouAmount,jdbcType=DECIMAL},
      deposit_received_amount = #{depositReceivedAmount,jdbcType=DECIMAL},
      deposit_refund_amount = #{depositRefundAmount,jdbcType=DECIMAL},
      deposit_carry_amount = #{depositCarryAmount,jdbcType=DECIMAL},
      total_yingshou_amount = #{totalYingshouAmount,jdbcType=DECIMAL},
      total_reduce_amount = #{totalReduceAmount,jdbcType=DECIMAL},
      total_received_amount = #{totalReceivedAmount,jdbcType=DECIMAL},
      total_contract_pay_amount = #{totalContractPayAmount,jdbcType=DECIMAL},
      total_refund_amount = #{totalRefundAmount,jdbcType=DECIMAL},
      penalty_yingshou_amount = #{penaltyYingshouAmount,jdbcType=DECIMAL},
      arrears_amount = #{arrearsAmount,jdbcType=DECIMAL},
      reduce_reason = #{reduceReason,jdbcType=VARCHAR},
      refund_process_status = #{refundProcessStatus,jdbcType=TINYINT}
    where settlement_id = #{settlementId,jdbcType=INTEGER}
  </update>
</mapper>