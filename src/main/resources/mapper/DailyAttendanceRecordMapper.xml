<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.DailyAttendanceRecordMapper">
  <resultMap id="BaseResultMap" type="com.izu.business.entity.DailyAttendanceRecord">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="record_date" jdbcType="DATE" property="recordDate" />
    <result column="work_start_time" jdbcType="TIMESTAMP" property="workStartTime" />
    <result column="work_end_time" jdbcType="TIMESTAMP" property="workEndTime" />
    <result column="driver_name" jdbcType="VARCHAR" property="driverName" />
    <result column="driver_id" jdbcType="INTEGER" property="driverId" />
    <result column="start_face_auth" jdbcType="TINYINT" property="startFaceAuth" />
    <result column="actual_work_start_time" jdbcType="TIMESTAMP" property="actualWorkStartTime" />
    <result column="actual_work_end_time" jdbcType="TIMESTAMP" property="actualWorkEndTime" />
    <result column="start_locate" jdbcType="VARCHAR" property="startLocate" />
    <result column="start_locate_name" jdbcType="VARCHAR" property="startLocateName" />
    <result column="late_times" jdbcType="TINYINT" property="lateTimes" />
    <result column="lack_times" jdbcType="TINYINT" property="lackTimes" />
    <result column="early_times" jdbcType="INTEGER" property="earlyTimes" />
    <result column="absent_times" jdbcType="INTEGER" property="absentTimes" />
    <result column="out_range_times" jdbcType="INTEGER" property="outRangeTimes" />
    <result column="start_out_sign" jdbcType="TINYINT" property="startOutSign" />
    <result column="end_out_sign" jdbcType="TINYINT" property="endOutSign" />
    <result column="late_minutes" jdbcType="INTEGER" property="lateMinutes" />
    <result column="early_minutes" jdbcType="INTEGER" property="earlyMinutes" />
    <result column="end_locate" jdbcType="VARCHAR" property="endLocate" />
    <result column="end_locate_name" jdbcType="VARCHAR" property="endLocateName" />
    <result column="start_lack" jdbcType="TINYINT" property="startLack" />
    <result column="end_lack" jdbcType="TINYINT" property="endLack" />
    <result column="start_fence_name" jdbcType="VARCHAR" property="startFenceName" />
    <result column="end_fence_name" jdbcType="VARCHAR" property="endFenceName" />
    <result column="start_out_sign_info" jdbcType="VARCHAR" property="startOutSignInfo" />
    <result column="end_out_sign_info" jdbcType="VARCHAR" property="endOutSignInfo" />
    <result column="end_face_auth" jdbcType="TINYINT" property="endFaceAuth" />
    <result column="start_apply" jdbcType="TINYINT" property="startApply" />
    <result column="end_apply" jdbcType="TINYINT" property="endApply" />
    <result column="start_apply_time" jdbcType="TIMESTAMP" property="startApplyTime" />
    <result column="end_apply_time" jdbcType="TIMESTAMP" property="endApplyTime" />
    <result column="start_process_instance_id" jdbcType="VARCHAR" property="startProcessInstanceId" />
    <result column="end_process_instance_id" jdbcType="VARCHAR" property="endProcessInstanceId" />
    <result column="start_seq_no" jdbcType="VARCHAR" property="startSeqNo" />
    <result column="end_seq_no" jdbcType="VARCHAR" property="endSeqNo" />
  </resultMap>
  <sql id="Base_Column_List">
    id, record_date, work_start_time, work_end_time, driver_name, driver_id, start_face_auth, 
    actual_work_start_time, actual_work_end_time, start_locate, start_locate_name, late_times, 
    lack_times, early_times, absent_times, out_range_times, start_out_sign, end_out_sign, 
    late_minutes, early_minutes, end_locate, end_locate_name, start_lack, end_lack, start_fence_name, 
    end_fence_name, start_out_sign_info, end_out_sign_info, end_face_auth, start_apply, 
    end_apply, start_apply_time, end_apply_time, start_process_instance_id, end_process_instance_id, 
    start_seq_no, end_seq_no
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from daily_attendance_record
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from daily_attendance_record
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.izu.business.entity.DailyAttendanceRecord">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into daily_attendance_record (record_date, work_start_time, work_end_time, 
      driver_name, driver_id, start_face_auth, 
      actual_work_start_time, actual_work_end_time, 
      start_locate, start_locate_name, late_times, 
      lack_times, early_times, absent_times, 
      out_range_times, start_out_sign, end_out_sign, 
      late_minutes, early_minutes, end_locate, 
      end_locate_name, start_lack, end_lack, 
      start_fence_name, end_fence_name, start_out_sign_info, 
      end_out_sign_info, end_face_auth, start_apply, 
      end_apply, start_apply_time, end_apply_time, 
      start_process_instance_id, end_process_instance_id, 
      start_seq_no, end_seq_no)
    values (#{recordDate,jdbcType=DATE}, #{workStartTime,jdbcType=TIMESTAMP}, #{workEndTime,jdbcType=TIMESTAMP}, 
      #{driverName,jdbcType=VARCHAR}, #{driverId,jdbcType=INTEGER}, #{startFaceAuth,jdbcType=TINYINT}, 
      #{actualWorkStartTime,jdbcType=TIMESTAMP}, #{actualWorkEndTime,jdbcType=TIMESTAMP}, 
      #{startLocate,jdbcType=VARCHAR}, #{startLocateName,jdbcType=VARCHAR}, #{lateTimes,jdbcType=TINYINT}, 
      #{lackTimes,jdbcType=TINYINT}, #{earlyTimes,jdbcType=INTEGER}, #{absentTimes,jdbcType=INTEGER}, 
      #{outRangeTimes,jdbcType=INTEGER}, #{startOutSign,jdbcType=TINYINT}, #{endOutSign,jdbcType=TINYINT}, 
      #{lateMinutes,jdbcType=INTEGER}, #{earlyMinutes,jdbcType=INTEGER}, #{endLocate,jdbcType=VARCHAR}, 
      #{endLocateName,jdbcType=VARCHAR}, #{startLack,jdbcType=TINYINT}, #{endLack,jdbcType=TINYINT}, 
      #{startFenceName,jdbcType=VARCHAR}, #{endFenceName,jdbcType=VARCHAR}, #{startOutSignInfo,jdbcType=VARCHAR}, 
      #{endOutSignInfo,jdbcType=VARCHAR}, #{endFaceAuth,jdbcType=TINYINT}, #{startApply,jdbcType=TINYINT}, 
      #{endApply,jdbcType=TINYINT}, #{startApplyTime,jdbcType=TIMESTAMP}, #{endApplyTime,jdbcType=TIMESTAMP}, 
      #{startProcessInstanceId,jdbcType=VARCHAR}, #{endProcessInstanceId,jdbcType=VARCHAR}, 
      #{startSeqNo,jdbcType=VARCHAR}, #{endSeqNo,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.business.entity.DailyAttendanceRecord">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into daily_attendance_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="recordDate != null">
        record_date,
      </if>
      <if test="workStartTime != null">
        work_start_time,
      </if>
      <if test="workEndTime != null">
        work_end_time,
      </if>
      <if test="driverName != null">
        driver_name,
      </if>
      <if test="driverId != null">
        driver_id,
      </if>
      <if test="startFaceAuth != null">
        start_face_auth,
      </if>
      <if test="actualWorkStartTime != null">
        actual_work_start_time,
      </if>
      <if test="actualWorkEndTime != null">
        actual_work_end_time,
      </if>
      <if test="startLocate != null">
        start_locate,
      </if>
      <if test="startLocateName != null">
        start_locate_name,
      </if>
      <if test="lateTimes != null">
        late_times,
      </if>
      <if test="lackTimes != null">
        lack_times,
      </if>
      <if test="earlyTimes != null">
        early_times,
      </if>
      <if test="absentTimes != null">
        absent_times,
      </if>
      <if test="outRangeTimes != null">
        out_range_times,
      </if>
      <if test="startOutSign != null">
        start_out_sign,
      </if>
      <if test="endOutSign != null">
        end_out_sign,
      </if>
      <if test="lateMinutes != null">
        late_minutes,
      </if>
      <if test="earlyMinutes != null">
        early_minutes,
      </if>
      <if test="endLocate != null">
        end_locate,
      </if>
      <if test="endLocateName != null">
        end_locate_name,
      </if>
      <if test="startLack != null">
        start_lack,
      </if>
      <if test="endLack != null">
        end_lack,
      </if>
      <if test="startFenceName != null">
        start_fence_name,
      </if>
      <if test="endFenceName != null">
        end_fence_name,
      </if>
      <if test="startOutSignInfo != null">
        start_out_sign_info,
      </if>
      <if test="endOutSignInfo != null">
        end_out_sign_info,
      </if>
      <if test="endFaceAuth != null">
        end_face_auth,
      </if>
      <if test="startApply != null">
        start_apply,
      </if>
      <if test="endApply != null">
        end_apply,
      </if>
      <if test="startApplyTime != null">
        start_apply_time,
      </if>
      <if test="endApplyTime != null">
        end_apply_time,
      </if>
      <if test="startProcessInstanceId != null">
        start_process_instance_id,
      </if>
      <if test="endProcessInstanceId != null">
        end_process_instance_id,
      </if>
      <if test="startSeqNo != null">
        start_seq_no,
      </if>
      <if test="endSeqNo != null">
        end_seq_no,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="recordDate != null">
        #{recordDate,jdbcType=DATE},
      </if>
      <if test="workStartTime != null">
        #{workStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="workEndTime != null">
        #{workEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="driverName != null">
        #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="driverId != null">
        #{driverId,jdbcType=INTEGER},
      </if>
      <if test="startFaceAuth != null">
        #{startFaceAuth,jdbcType=TINYINT},
      </if>
      <if test="actualWorkStartTime != null">
        #{actualWorkStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="actualWorkEndTime != null">
        #{actualWorkEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="startLocate != null">
        #{startLocate,jdbcType=VARCHAR},
      </if>
      <if test="startLocateName != null">
        #{startLocateName,jdbcType=VARCHAR},
      </if>
      <if test="lateTimes != null">
        #{lateTimes,jdbcType=TINYINT},
      </if>
      <if test="lackTimes != null">
        #{lackTimes,jdbcType=TINYINT},
      </if>
      <if test="earlyTimes != null">
        #{earlyTimes,jdbcType=INTEGER},
      </if>
      <if test="absentTimes != null">
        #{absentTimes,jdbcType=INTEGER},
      </if>
      <if test="outRangeTimes != null">
        #{outRangeTimes,jdbcType=INTEGER},
      </if>
      <if test="startOutSign != null">
        #{startOutSign,jdbcType=TINYINT},
      </if>
      <if test="endOutSign != null">
        #{endOutSign,jdbcType=TINYINT},
      </if>
      <if test="lateMinutes != null">
        #{lateMinutes,jdbcType=INTEGER},
      </if>
      <if test="earlyMinutes != null">
        #{earlyMinutes,jdbcType=INTEGER},
      </if>
      <if test="endLocate != null">
        #{endLocate,jdbcType=VARCHAR},
      </if>
      <if test="endLocateName != null">
        #{endLocateName,jdbcType=VARCHAR},
      </if>
      <if test="startLack != null">
        #{startLack,jdbcType=TINYINT},
      </if>
      <if test="endLack != null">
        #{endLack,jdbcType=TINYINT},
      </if>
      <if test="startFenceName != null">
        #{startFenceName,jdbcType=VARCHAR},
      </if>
      <if test="endFenceName != null">
        #{endFenceName,jdbcType=VARCHAR},
      </if>
      <if test="startOutSignInfo != null">
        #{startOutSignInfo,jdbcType=VARCHAR},
      </if>
      <if test="endOutSignInfo != null">
        #{endOutSignInfo,jdbcType=VARCHAR},
      </if>
      <if test="endFaceAuth != null">
        #{endFaceAuth,jdbcType=TINYINT},
      </if>
      <if test="startApply != null">
        #{startApply,jdbcType=TINYINT},
      </if>
      <if test="endApply != null">
        #{endApply,jdbcType=TINYINT},
      </if>
      <if test="startApplyTime != null">
        #{startApplyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endApplyTime != null">
        #{endApplyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="startProcessInstanceId != null">
        #{startProcessInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="endProcessInstanceId != null">
        #{endProcessInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="startSeqNo != null">
        #{startSeqNo,jdbcType=VARCHAR},
      </if>
      <if test="endSeqNo != null">
        #{endSeqNo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.business.entity.DailyAttendanceRecord">
    update daily_attendance_record
    <set>
      <if test="recordDate != null">
        record_date = #{recordDate,jdbcType=DATE},
      </if>
      <if test="workStartTime != null">
        work_start_time = #{workStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="workEndTime != null">
        work_end_time = #{workEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="driverName != null">
        driver_name = #{driverName,jdbcType=VARCHAR},
      </if>
      <if test="driverId != null">
        driver_id = #{driverId,jdbcType=INTEGER},
      </if>
      <if test="startFaceAuth != null">
        start_face_auth = #{startFaceAuth,jdbcType=TINYINT},
      </if>
      <if test="actualWorkStartTime != null">
        actual_work_start_time = #{actualWorkStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="actualWorkEndTime != null">
        actual_work_end_time = #{actualWorkEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="startLocate != null">
        start_locate = #{startLocate,jdbcType=VARCHAR},
      </if>
      <if test="startLocateName != null">
        start_locate_name = #{startLocateName,jdbcType=VARCHAR},
      </if>
      <if test="lateTimes != null">
        late_times = #{lateTimes,jdbcType=TINYINT},
      </if>
      <if test="lackTimes != null">
        lack_times = #{lackTimes,jdbcType=TINYINT},
      </if>
      <if test="earlyTimes != null">
        early_times = #{earlyTimes,jdbcType=INTEGER},
      </if>
      <if test="absentTimes != null">
        absent_times = #{absentTimes,jdbcType=INTEGER},
      </if>
      <if test="outRangeTimes != null">
        out_range_times = #{outRangeTimes,jdbcType=INTEGER},
      </if>
      <if test="startOutSign != null">
        start_out_sign = #{startOutSign,jdbcType=TINYINT},
      </if>
      <if test="endOutSign != null">
        end_out_sign = #{endOutSign,jdbcType=TINYINT},
      </if>
      <if test="lateMinutes != null">
        late_minutes = #{lateMinutes,jdbcType=INTEGER},
      </if>
      <if test="earlyMinutes != null">
        early_minutes = #{earlyMinutes,jdbcType=INTEGER},
      </if>
      <if test="endLocate != null">
        end_locate = #{endLocate,jdbcType=VARCHAR},
      </if>
      <if test="endLocateName != null">
        end_locate_name = #{endLocateName,jdbcType=VARCHAR},
      </if>
      <if test="startLack != null">
        start_lack = #{startLack,jdbcType=TINYINT},
      </if>
      <if test="endLack != null">
        end_lack = #{endLack,jdbcType=TINYINT},
      </if>
      <if test="startFenceName != null">
        start_fence_name = #{startFenceName,jdbcType=VARCHAR},
      </if>
      <if test="endFenceName != null">
        end_fence_name = #{endFenceName,jdbcType=VARCHAR},
      </if>
      <if test="startOutSignInfo != null">
        start_out_sign_info = #{startOutSignInfo,jdbcType=VARCHAR},
      </if>
      <if test="endOutSignInfo != null">
        end_out_sign_info = #{endOutSignInfo,jdbcType=VARCHAR},
      </if>
      <if test="endFaceAuth != null">
        end_face_auth = #{endFaceAuth,jdbcType=TINYINT},
      </if>
      <if test="startApply != null">
        start_apply = #{startApply,jdbcType=TINYINT},
      </if>
      <if test="endApply != null">
        end_apply = #{endApply,jdbcType=TINYINT},
      </if>
      <if test="startApplyTime != null">
        start_apply_time = #{startApplyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="endApplyTime != null">
        end_apply_time = #{endApplyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="startProcessInstanceId != null">
        start_process_instance_id = #{startProcessInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="endProcessInstanceId != null">
        end_process_instance_id = #{endProcessInstanceId,jdbcType=VARCHAR},
      </if>
      <if test="startSeqNo != null">
        start_seq_no = #{startSeqNo,jdbcType=VARCHAR},
      </if>
      <if test="endSeqNo != null">
        end_seq_no = #{endSeqNo,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.business.entity.DailyAttendanceRecord">
    update daily_attendance_record
    set record_date = #{recordDate,jdbcType=DATE},
      work_start_time = #{workStartTime,jdbcType=TIMESTAMP},
      work_end_time = #{workEndTime,jdbcType=TIMESTAMP},
      driver_name = #{driverName,jdbcType=VARCHAR},
      driver_id = #{driverId,jdbcType=INTEGER},
      start_face_auth = #{startFaceAuth,jdbcType=TINYINT},
      actual_work_start_time = #{actualWorkStartTime,jdbcType=TIMESTAMP},
      actual_work_end_time = #{actualWorkEndTime,jdbcType=TIMESTAMP},
      start_locate = #{startLocate,jdbcType=VARCHAR},
      start_locate_name = #{startLocateName,jdbcType=VARCHAR},
      late_times = #{lateTimes,jdbcType=TINYINT},
      lack_times = #{lackTimes,jdbcType=TINYINT},
      early_times = #{earlyTimes,jdbcType=INTEGER},
      absent_times = #{absentTimes,jdbcType=INTEGER},
      out_range_times = #{outRangeTimes,jdbcType=INTEGER},
      start_out_sign = #{startOutSign,jdbcType=TINYINT},
      end_out_sign = #{endOutSign,jdbcType=TINYINT},
      late_minutes = #{lateMinutes,jdbcType=INTEGER},
      early_minutes = #{earlyMinutes,jdbcType=INTEGER},
      end_locate = #{endLocate,jdbcType=VARCHAR},
      end_locate_name = #{endLocateName,jdbcType=VARCHAR},
      start_lack = #{startLack,jdbcType=TINYINT},
      end_lack = #{endLack,jdbcType=TINYINT},
      start_fence_name = #{startFenceName,jdbcType=VARCHAR},
      end_fence_name = #{endFenceName,jdbcType=VARCHAR},
      start_out_sign_info = #{startOutSignInfo,jdbcType=VARCHAR},
      end_out_sign_info = #{endOutSignInfo,jdbcType=VARCHAR},
      end_face_auth = #{endFaceAuth,jdbcType=TINYINT},
      start_apply = #{startApply,jdbcType=TINYINT},
      end_apply = #{endApply,jdbcType=TINYINT},
      start_apply_time = #{startApplyTime,jdbcType=TIMESTAMP},
      end_apply_time = #{endApplyTime,jdbcType=TIMESTAMP},
      start_process_instance_id = #{startProcessInstanceId,jdbcType=VARCHAR},
      end_process_instance_id = #{endProcessInstanceId,jdbcType=VARCHAR},
      start_seq_no = #{startSeqNo,jdbcType=VARCHAR},
      end_seq_no = #{endSeqNo,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>