<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper.ProviderStaffMapper">
  <resultMap id="BaseResultMap" type="com.izu.user.entity.ProviderStaff">
    <id column="provider_staff_id" jdbcType="INTEGER" property="providerStaffId" />
    <result column="provider_staff_code" jdbcType="VARCHAR" property="providerStaffCode" />
    <result column="mgt_staff_id" jdbcType="INTEGER" property="mgtStaffId" />
    <result column="ding_user_id" jdbcType="VARCHAR" property="dingUserId" />
    <result column="staff_name" jdbcType="VARCHAR" property="staffName" />
    <result column="pinyin_name" jdbcType="VARCHAR" property="pinyinName" />
    <result column="gender" jdbcType="TINYINT" property="gender" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="id_card_num" jdbcType="VARCHAR" property="idCardNum" />
    <result column="head_icon" jdbcType="VARCHAR" property="headIcon" />
    <result column="position" jdbcType="VARCHAR" property="position" />
    <result column="account" jdbcType="VARCHAR" property="account" />
    <result column="login_password" jdbcType="VARCHAR" property="loginPassword" />
    <result column="salt" jdbcType="VARCHAR" property="salt" />
    <result column="provider_code" jdbcType="VARCHAR" property="providerCode" />
    <result column="provider_name" jdbcType="VARCHAR" property="providerName" />
    <result column="struct_ids" jdbcType="VARCHAR" property="structIds" />
    <result column="disccount_proportion" jdbcType="INTEGER" property="disccountProportion" />
    <result column="mgt_user_state" jdbcType="TINYINT" property="mgtUserState" />
    <result column="user_state" jdbcType="TINYINT" property="userState" />
    <result column="data_perm_type" jdbcType="TINYINT" property="dataPermType" />
    <result column="hired_date" jdbcType="TIMESTAMP" property="hiredDate" />
    <result column="leave_office_date" jdbcType="TIMESTAMP" property="leaveOfficeDate" />
    <result column="last_login_time" jdbcType="TIMESTAMP" property="lastLoginTime" />
    <result column="default_struct_code" jdbcType="VARCHAR" property="defaultStructCode" />
    <result column="default_struct_name" jdbcType="VARCHAR" property="defaultStructName" />
    <result column="driver_id" jdbcType="INTEGER" property="driverId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_id" jdbcType="INTEGER" property="updateId" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    provider_staff_id, provider_staff_code, mgt_staff_id, ding_user_id, staff_name, pinyin_name, 
    gender, mobile, email, id_card_num, head_icon, position, account, login_password, 
    salt, provider_code, provider_name, struct_ids, disccount_proportion, mgt_user_state, 
    user_state, data_perm_type, hired_date, leave_office_date, last_login_time, default_struct_code,
    default_struct_name, driver_id, create_time, update_time, update_id, update_name
  </sql>
  <select id="selectByExample" parameterType="com.izu.user.entity.ProviderStaffExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from t_provider_staff
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from t_provider_staff
    where provider_staff_id = #{providerStaffId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from t_provider_staff
    where provider_staff_id = #{providerStaffId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.izu.user.entity.ProviderStaffExample">
    delete from t_provider_staff
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.izu.user.entity.ProviderStaff">
    <selectKey keyProperty="providerStaffId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into t_provider_staff (provider_staff_code, mgt_staff_id, ding_user_id, 
      staff_name, pinyin_name, gender, 
      mobile, email, id_card_num, 
      head_icon, position, account, 
      login_password, salt, provider_code, 
      provider_name, struct_ids, disccount_proportion, 
      mgt_user_state, user_state, data_perm_type, 
      hired_date, leave_office_date, last_login_time,
      default_struct_code, default_struct_name, driver_id, 
      create_time, update_time, update_id, 
      update_name)
    values (#{providerStaffCode,jdbcType=VARCHAR}, #{mgtStaffId,jdbcType=INTEGER}, #{dingUserId,jdbcType=VARCHAR},
      #{staffName,jdbcType=VARCHAR}, #{pinyinName,jdbcType=VARCHAR}, #{gender,jdbcType=TINYINT}, 
      #{mobile,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, #{idCardNum,jdbcType=VARCHAR}, 
      #{headIcon,jdbcType=VARCHAR}, #{position,jdbcType=VARCHAR}, #{account,jdbcType=VARCHAR}, 
      #{loginPassword,jdbcType=VARCHAR}, #{salt,jdbcType=VARCHAR}, #{providerCode,jdbcType=VARCHAR}, 
      #{providerName,jdbcType=VARCHAR}, #{structIds,jdbcType=VARCHAR}, #{disccountProportion,jdbcType=INTEGER}, 
      #{mgtUserState,jdbcType=TINYINT}, #{userState,jdbcType=TINYINT}, #{dataPermType,jdbcType=TINYINT}, 
      #{hiredDate,jdbcType=TIMESTAMP}, #{leaveOfficeDate,jdbcType=TIMESTAMP}, #{lastLoginTime,jdbcType=TIMESTAMP},
      #{defaultStructCode,jdbcType=VARCHAR}, #{defaultStructName,jdbcType=VARCHAR}, #{driverId,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{updateId,jdbcType=INTEGER}, 
      #{updateName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.izu.user.entity.ProviderStaff">
    <selectKey keyProperty="providerStaffId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into t_provider_staff
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="providerStaffCode != null">
        provider_staff_code,
      </if>
      <if test="mgtStaffId != null">
        mgt_staff_id,
      </if>
      <if test="dingUserId != null">
        ding_user_id,
      </if>
      <if test="staffName != null">
        staff_name,
      </if>
      <if test="pinyinName != null">
        pinyin_name,
      </if>
      <if test="gender != null">
        gender,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="idCardNum != null">
        id_card_num,
      </if>
      <if test="headIcon != null">
        head_icon,
      </if>
      <if test="position != null">
        position,
      </if>
      <if test="account != null">
        account,
      </if>
      <if test="loginPassword != null">
        login_password,
      </if>
      <if test="salt != null">
        salt,
      </if>
      <if test="providerCode != null">
        provider_code,
      </if>
      <if test="providerName != null">
        provider_name,
      </if>
      <if test="structIds != null">
        struct_ids,
      </if>
      <if test="disccountProportion != null">
        disccount_proportion,
      </if>
      <if test="mgtUserState != null">
        mgt_user_state,
      </if>
      <if test="userState != null">
        user_state,
      </if>
      <if test="dataPermType != null">
        data_perm_type,
      </if>
      <if test="hiredDate != null">
        hired_date,
      </if>
      <if test="leaveOfficeDate != null">
        leave_office_date,
      </if>
      <if test="lastLoginTime != null">
        last_login_time,
      </if>
      <if test="defaultStructCode != null">
        default_struct_code,
      </if>
      <if test="defaultStructName != null">
        default_struct_name,
      </if>

      <if test="driverId != null">
        driver_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="updateId != null">
        update_id,
      </if>
      <if test="updateName != null">
        update_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="providerStaffCode != null">
        #{providerStaffCode,jdbcType=VARCHAR},
      </if>
      <if test="mgtStaffId != null">
        #{mgtStaffId,jdbcType=INTEGER},
      </if>
      <if test="dingUserId != null">
        #{dingUserId,jdbcType=VARCHAR},
      </if>
      <if test="staffName != null">
        #{staffName,jdbcType=VARCHAR},
      </if>
      <if test="pinyinName != null">
        #{pinyinName,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=TINYINT},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="idCardNum != null">
        #{idCardNum,jdbcType=VARCHAR},
      </if>
      <if test="headIcon != null">
        #{headIcon,jdbcType=VARCHAR},
      </if>
      <if test="position != null">
        #{position,jdbcType=VARCHAR},
      </if>
      <if test="account != null">
        #{account,jdbcType=VARCHAR},
      </if>
      <if test="loginPassword != null">
        #{loginPassword,jdbcType=VARCHAR},
      </if>
      <if test="salt != null">
        #{salt,jdbcType=VARCHAR},
      </if>
      <if test="providerCode != null">
        #{providerCode,jdbcType=VARCHAR},
      </if>
      <if test="providerName != null">
        #{providerName,jdbcType=VARCHAR},
      </if>
      <if test="structIds != null">
        #{structIds,jdbcType=VARCHAR},
      </if>
      <if test="disccountProportion != null">
        #{disccountProportion,jdbcType=INTEGER},
      </if>
      <if test="mgtUserState != null">
        #{mgtUserState,jdbcType=TINYINT},
      </if>
      <if test="userState != null">
        #{userState,jdbcType=TINYINT},
      </if>
      <if test="dataPermType != null">
        #{dataPermType,jdbcType=TINYINT},
      </if>
      <if test="hiredDate != null">
        #{hiredDate,jdbcType=TIMESTAMP},
      </if>
      <if test="leaveOfficeDate != null">
        #{leaveOfficeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastLoginTime != null">
        #{lastLoginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="defaultStructCode != null">
        #{defaultStructCode,jdbcType=VARCHAR},
      </if>
      <if test="defaultStructName != null">
        #{defaultStructName,jdbcType=VARCHAR},
      </if>

      <if test="driverId != null">
        #{driverId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        #{updateName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.izu.user.entity.ProviderStaffExample" resultType="java.lang.Long">
    select count(*) from t_provider_staff
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update t_provider_staff
    <set>
      <if test="row.providerStaffId != null">
        provider_staff_id = #{row.providerStaffId,jdbcType=INTEGER},
      </if>
      <if test="row.providerStaffCode != null">
        provider_staff_code = #{row.providerStaffCode,jdbcType=VARCHAR},
      </if>
      <if test="row.mgtStaffId != null">
        mgt_staff_id = #{row.mgtStaffId,jdbcType=INTEGER},
      </if>
      <if test="row.dingUserId != null">
        ding_user_id = #{row.dingUserId,jdbcType=VARCHAR},
      </if>
      <if test="row.staffName != null">
        staff_name = #{row.staffName,jdbcType=VARCHAR},
      </if>
      <if test="row.pinyinName != null">
        pinyin_name = #{row.pinyinName,jdbcType=VARCHAR},
      </if>
      <if test="row.gender != null">
        gender = #{row.gender,jdbcType=TINYINT},
      </if>
      <if test="row.mobile != null">
        mobile = #{row.mobile,jdbcType=VARCHAR},
      </if>
      <if test="row.email != null">
        email = #{row.email,jdbcType=VARCHAR},
      </if>
      <if test="row.idCardNum != null">
        id_card_num = #{row.idCardNum,jdbcType=VARCHAR},
      </if>
      <if test="row.headIcon != null">
        head_icon = #{row.headIcon,jdbcType=VARCHAR},
      </if>
      <if test="row.position != null">
        position = #{row.position,jdbcType=VARCHAR},
      </if>
      <if test="row.account != null">
        account = #{row.account,jdbcType=VARCHAR},
      </if>
      <if test="row.loginPassword != null">
        login_password = #{row.loginPassword,jdbcType=VARCHAR},
      </if>
      <if test="row.salt != null">
        salt = #{row.salt,jdbcType=VARCHAR},
      </if>
      <if test="row.providerCode != null">
        provider_code = #{row.providerCode,jdbcType=VARCHAR},
      </if>
      <if test="row.providerName != null">
        provider_name = #{row.providerName,jdbcType=VARCHAR},
      </if>
      <if test="row.structIds != null">
        struct_ids = #{row.structIds,jdbcType=VARCHAR},
      </if>
      <if test="row.disccountProportion != null">
        disccount_proportion = #{row.disccountProportion,jdbcType=INTEGER},
      </if>
      <if test="row.mgtUserState != null">
        mgt_user_state = #{row.mgtUserState,jdbcType=TINYINT},
      </if>
      <if test="row.userState != null">
        user_state = #{row.userState,jdbcType=TINYINT},
      </if>
      <if test="row.dataPermType != null">
        data_perm_type = #{row.dataPermType,jdbcType=TINYINT},
      </if>
      <if test="row.hiredDate != null">
        hired_date = #{row.hiredDate,jdbcType=TIMESTAMP},
      </if>
      <if test="row.leaveOfficeDate != null">
        leave_office_date = #{row.leaveOfficeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="row.lastLoginTime != null">
        last_login_time = #{row.lastLoginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.defaultStructCode != null">
        default_struct_code = #{row.defaultStructCode,jdbcType=VARCHAR},
      </if>
      <if test="row.defaultStructName != null">
        default_struct_name = #{row.defaultStructName,jdbcType=VARCHAR},
      </if>
      <if test="row.driverId != null">
        driver_id = #{row.driverId,jdbcType=INTEGER},
      </if>
      <if test="row.createTime != null">
        create_time = #{row.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateTime != null">
        update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updateId != null">
        update_id = #{row.updateId,jdbcType=INTEGER},
      </if>
      <if test="row.updateName != null">
        update_name = #{row.updateName,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update t_provider_staff
    set provider_staff_id = #{row.providerStaffId,jdbcType=INTEGER},
      provider_staff_code = #{row.providerStaffCode,jdbcType=VARCHAR},
      mgt_staff_id = #{row.mgtStaffId,jdbcType=INTEGER},
      ding_user_id = #{row.dingUserId,jdbcType=VARCHAR},
      staff_name = #{row.staffName,jdbcType=VARCHAR},
      pinyin_name = #{row.pinyinName,jdbcType=VARCHAR},
      gender = #{row.gender,jdbcType=TINYINT},
      mobile = #{row.mobile,jdbcType=VARCHAR},
      email = #{row.email,jdbcType=VARCHAR},
      id_card_num = #{row.idCardNum,jdbcType=VARCHAR},
      head_icon = #{row.headIcon,jdbcType=VARCHAR},
      position = #{row.position,jdbcType=VARCHAR},
      account = #{row.account,jdbcType=VARCHAR},
      login_password = #{row.loginPassword,jdbcType=VARCHAR},
      salt = #{row.salt,jdbcType=VARCHAR},
      provider_code = #{row.providerCode,jdbcType=VARCHAR},
      provider_name = #{row.providerName,jdbcType=VARCHAR},
      struct_ids = #{row.structIds,jdbcType=VARCHAR},
      disccount_proportion = #{row.disccountProportion,jdbcType=INTEGER},
      mgt_user_state = #{row.mgtUserState,jdbcType=TINYINT},
      user_state = #{row.userState,jdbcType=TINYINT},
      data_perm_type = #{row.dataPermType,jdbcType=TINYINT},
      hired_date = #{row.hiredDate,jdbcType=TIMESTAMP},
      leave_office_date = #{row.leaveOfficeDate,jdbcType=TIMESTAMP},
      last_login_time = #{row.lastLoginTime,jdbcType=TIMESTAMP},
      default_struct_code = #{row.defaultStructCode,jdbcType=VARCHAR},
      default_struct_name = #{row.defaultStructName,jdbcType=VARCHAR},
      driver_id = #{row.driverId,jdbcType=INTEGER},
      create_time = #{row.createTime,jdbcType=TIMESTAMP},
      update_time = #{row.updateTime,jdbcType=TIMESTAMP},
      update_id = #{row.updateId,jdbcType=INTEGER},
      update_name = #{row.updateName,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.izu.user.entity.ProviderStaff">
    update t_provider_staff
    <set>
      <if test="providerStaffCode != null">
        provider_staff_code = #{providerStaffCode,jdbcType=VARCHAR},
      </if>
      <if test="mgtStaffId != null">
        mgt_staff_id = #{mgtStaffId,jdbcType=INTEGER},
      </if>
      <if test="dingUserId != null">
        ding_user_id = #{dingUserId,jdbcType=VARCHAR},
      </if>
      <if test="staffName != null">
        staff_name = #{staffName,jdbcType=VARCHAR},
      </if>
      <if test="pinyinName != null">
        pinyin_name = #{pinyinName,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        gender = #{gender,jdbcType=TINYINT},
      </if>
      <if test="mobile != null">
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="idCardNum != null">
        id_card_num = #{idCardNum,jdbcType=VARCHAR},
      </if>
      <if test="headIcon != null">
        head_icon = #{headIcon,jdbcType=VARCHAR},
      </if>
      <if test="position != null">
        position = #{position,jdbcType=VARCHAR},
      </if>
      <if test="account != null">
        account = #{account,jdbcType=VARCHAR},
      </if>
      <if test="loginPassword != null">
        login_password = #{loginPassword,jdbcType=VARCHAR},
      </if>
      <if test="salt != null">
        salt = #{salt,jdbcType=VARCHAR},
      </if>
      <if test="providerCode != null">
        provider_code = #{providerCode,jdbcType=VARCHAR},
      </if>
      <if test="providerName != null">
        provider_name = #{providerName,jdbcType=VARCHAR},
      </if>
      <if test="structIds != null">
        struct_ids = #{structIds,jdbcType=VARCHAR},
      </if>
      <if test="disccountProportion != null">
        disccount_proportion = #{disccountProportion,jdbcType=INTEGER},
      </if>
      <if test="mgtUserState != null">
        mgt_user_state = #{mgtUserState,jdbcType=TINYINT},
      </if>
      <if test="userState != null">
        user_state = #{userState,jdbcType=TINYINT},
      </if>
      <if test="dataPermType != null">
        data_perm_type = #{dataPermType,jdbcType=TINYINT},
      </if>
      <if test="hiredDate != null">
        hired_date = #{hiredDate,jdbcType=TIMESTAMP},
      </if>
      <if test="leaveOfficeDate != null">
        leave_office_date = #{leaveOfficeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastLoginTime != null">
        last_login_time = #{lastLoginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="defaultStructCode != null">
        default_struct_code = #{defaultStructCode,jdbcType=VARCHAR},
      </if>
      <if test="defaultStructName != null">
        default_struct_name = #{defaultStructName,jdbcType=VARCHAR},
      </if>

      <if test="driverId != null">
        driver_id = #{driverId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateId != null">
        update_id = #{updateId,jdbcType=INTEGER},
      </if>
      <if test="updateName != null">
        update_name = #{updateName,jdbcType=VARCHAR},
      </if>
    </set>
    where provider_staff_id = #{providerStaffId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.izu.user.entity.ProviderStaff">
    update t_provider_staff
    set provider_staff_code = #{providerStaffCode,jdbcType=VARCHAR},
      mgt_staff_id = #{mgtStaffId,jdbcType=INTEGER},
      ding_user_id = #{dingUserId,jdbcType=VARCHAR},
      staff_name = #{staffName,jdbcType=VARCHAR},
      pinyin_name = #{pinyinName,jdbcType=VARCHAR},
      gender = #{gender,jdbcType=TINYINT},
      mobile = #{mobile,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      id_card_num = #{idCardNum,jdbcType=VARCHAR},
      head_icon = #{headIcon,jdbcType=VARCHAR},
      position = #{position,jdbcType=VARCHAR},
      account = #{account,jdbcType=VARCHAR},
      login_password = #{loginPassword,jdbcType=VARCHAR},
      salt = #{salt,jdbcType=VARCHAR},
      provider_code = #{providerCode,jdbcType=VARCHAR},
      provider_name = #{providerName,jdbcType=VARCHAR},
      struct_ids = #{structIds,jdbcType=VARCHAR},
      disccount_proportion = #{disccountProportion,jdbcType=INTEGER},
      mgt_user_state = #{mgtUserState,jdbcType=TINYINT},
      user_state = #{userState,jdbcType=TINYINT},
      data_perm_type = #{dataPermType,jdbcType=TINYINT},
      hired_date = #{hiredDate,jdbcType=TIMESTAMP},
      leave_office_date = #{leaveOfficeDate,jdbcType=TIMESTAMP},
      last_login_time = #{lastLoginTime,jdbcType=TIMESTAMP},
      default_struct_code = #{defaultStructCode,jdbcType=VARCHAR},
      default_struct_name = #{defaultStructName,jdbcType=VARCHAR},
      driver_id = #{driverId,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      update_id = #{updateId,jdbcType=INTEGER},
      update_name = #{updateName,jdbcType=VARCHAR}
    where provider_staff_id = #{providerStaffId,jdbcType=INTEGER}
  </update>
</mapper>