############## Hibernate Validation constraint \u6d88\u606f\u63d0\u793a\u6587\u5b57 BEGIN
validation.common.null=\u5fc5\u987b\u4e3a\u7a7a
validation.common.not.null=\u4e0d\u80fd\u4e3a\u7a7a
validation.common.assertTrue=\u5fc5\u987b\u4e3atrue
validation.common.assertFalse=\u5fc5\u987b\u4e3afalse
validation.common.min=\u5fc5\u987b\u5927\u4e8e\u7b49\u4e8e{value}
validation.common.max=\u5fc5\u987b\u5c0f\u4e8e\u7b49\u4e8e{value}
validation.common.decimalMin=\u5fc5\u987b\u5927\u4e8e\u7b49\u4e8e{value}
validation.common.decimalMax=\u5fc5\u987b\u5c0f\u4e8e\u7b49\u4e8e{value}
validation.common.size=\u5fc5\u987b\u5728\u6307\u5b9a\u8303\u56f4{min}\u81f3{max}\u4e4b\u95f4
validation.common.past=\u5fc5\u987b\u662f\u4e00\u4e2a\u8fc7\u53bb\u7684\u65e5\u671f
validation.common.future=\u5fc5\u987b\u662f\u4e00\u4e2a\u5c06\u6765\u7684\u65e5\u671f
validation.common.notBlank=\u4e0d\u80fd\u4e3a\u7a7a
validation.common.notEmpty=\u4e0d\u80fd\u4e3a\u7a7a
validation.common.email=\u5fc5\u987b\u662f\u6b63\u786e\u7684\u7535\u5b50\u90ae\u7bb1\u5730\u5740
validation.common.length=\u5fc5\u987b\u5728\u6307\u5b9a\u8303\u56f4{min}\u81f3{max}\u4e4b\u95f4
validation.common.range=\u5fc5\u987b\u5728\u6307\u5b9a\u8303\u56f4{min}\u81f3{max}\u4e4b\u95f4
validation.common.url=\u5fc5\u987b\u662f\u6b63\u786e\u7684URL
############## Hibernate Validation constraint \u6d88\u606f\u63d0\u793a\u6587\u5b57 END

############## \u7cfb\u7edf\u81ea\u5b9a\u4e49\u6269\u5c55\u7684constraint\u6d88\u606f\u63d0\u793a\u6587\u5b57 BEGIN
############## \u7cfb\u7edf\u81ea\u5b9a\u4e49\u6269\u5c55\u7684constraint\u6d88\u606f\u63d0\u793a\u6587\u5b57 END
