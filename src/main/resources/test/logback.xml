<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true">
    <property name="LOG_HOME" value="/data/logs"/>
    <property name="LOG_FILE_NAME" value="mrcar"/>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.mdc.TraceIdMDCPatternLogbackLayout">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %X{env}@%X{serverStartTime} [%-5level] %X{reqId}
                [%X{httpType}-%X{httpMethod}] %logger{36}%X{loginUser} %msg%n
            </pattern>
            </layout>
        </encoder>
<!--        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>-->
    </appender>
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/${LOG_FILE_NAME}/${LOG_FILE_NAME}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/${LOG_FILE_NAME}/${LOG_FILE_NAME}.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %X{env}@%X{serverStartTime} [%-5level] %X{reqId}
                [%X{httpType}-%X{httpMethod}] %logger{36}%X{loginUser} %msg%n
            </pattern>
        </encoder>
    </appender>

    <appender name="CUSTOM" class="com.izu.framework.web.RemoteCustomAppender"><!-- 如果是依赖web-spring-boot-starter，包路径应为com.izu.framework.logback -->
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %logger{36} %msg%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 仅允许 ERROR 级别的日志 -->
            <level>ERROR</level>
            <!-- 如果日志消息的级别与上述级别匹配（ERROR），则接受日志 -->
            <onMatch>ACCEPT</onMatch>
            <!-- 如果日志消息的级别不匹配（不是 ERROR），则拒绝日志 -->
            <onMismatch>DENY</onMismatch>
        </filter>
        <!-- 使用 Maven 构建时的项目名称作为项目名称 -->
        <projectName>${project.build.finalName}</projectName>
        <!-- 日志发送的服务器地址 -->
        <server>${panshi.log.server}</server>
    </appender>

    <!--这样可以避免输出一些spring框架的许多常见debug信息!-->
    <logger name="org.springframework" level="info"/>
    <logger name="org.json" level="error"/>
    <logger name="io.netty" level="info"/>
    <logger name="org.slf4j" level="info"/>
    <logger name="ch.qos.logback" level="info"/>

    <root level="INFO">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="FILE"/>
        <appender-ref ref="CUSTOM"/>
    </root>

    <!-- Rest接口性能监测BEGIN -->
    <appender name="Perf4JStatisticsFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/${LOG_FILE_NAME}/${LOG_FILE_NAME}-perf.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/${LOG_FILE_NAME}/${LOG_FILE_NAME}-perf.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%msg%n</pattern>
        </encoder>
    </appender>
    <appender name="coalescingStatisticsAppender" class="org.perf4j.logback.AsyncCoalescingStatisticsAppender">
        <timeSlice>3600000</timeSlice><!-- 每小时汇总一次 -->
        <appender-ref ref="Perf4JStatisticsFile"/>
    </appender>
    <logger name="org.perf4j.TimingLogger" level="info" additivity="false">
        <appender-ref ref="coalescingStatisticsAppender"/>
    </logger>
    <!-- Rest接口性能监测END -->

</configuration>