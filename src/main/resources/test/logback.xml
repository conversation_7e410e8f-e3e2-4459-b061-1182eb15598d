<configuration>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %X{env}@%X{serverStartTime} [%-5level] %X{reqId} [%X{httpType}-%X{httpMethod}] %logger{36} %msg%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
    </appender>
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/data/logs/${project.build.finalName}/${project.build.finalName}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/data/logs/${project.build.finalName}/${project.build.finalName}.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %X{env}@%X{serverStartTime} [%-5level] %X{reqId} [%X{httpType}-%X{httpMethod}] %logger{36} %msg%n</pattern>
        </encoder>
    </appender>
    <!-- 自定义 RemoteCustomAppender 处理日志 -->
    <appender name="CUSTOM" class="com.izu.framework.web.RemoteCustomAppender"><!-- 如果是依赖web-spring-boot-starter，包路径应为com.izu.framework.logback -->
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %logger{36} %msg%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <!-- 仅允许 ERROR 级别的日志 -->
            <level>ERROR</level>
            <!-- 如果日志消息的级别与上述级别匹配（ERROR），则接受日志 -->
            <onMatch>ACCEPT</onMatch>
            <!-- 如果日志消息的级别不匹配（不是 ERROR），则拒绝日志 -->
            <onMismatch>DENY</onMismatch>
        </filter>
        <!-- 使用 Maven 构建时的项目名称作为项目名称 -->
        <projectName>${project.build.finalName}</projectName>
        <!-- 日志发送的服务器地址 -->
        <server>${panshi.log.server}</server>
    </appender>

    <logger name="com.alibaba" level="INFO"/>
    <logger name="jdbc.sqltiming" level="INFO"/>
    <logger name="org.springframework.web" level="INFO"/>
    <logger name="org.springframework" level="INFO">
        <appender-ref ref="STDOUT"/>
    </logger>
    <logger name="com.ibatis" level="INFO" />
    <logger name="com.ibatis.common.jdbc.SimpleDataSource" level="INFO" />
    <logger name="com.ibatis.common.jdbc.ScriptRunner" level="INFO" />
    <logger name="com.ibatis.sqlmap.engine.impl.SqlMapClientDelegate" level="INFO" />
    <logger name="java.sql.Connection" level="INFO" />
    <logger name="java.sql.Statement" level="INFO" />
    <logger name="java.sql.PreparedStatement" level="INFO" />
    <logger name="java.sql.ResultSet" level="INFO" />

    <root level="INFO">
        <appender-ref ref="CUSTOM" /><!-- 新增部分 -->
        <appender-ref ref="STDOUT"/> <!-- 在本地开发时，需要打开注释 -->
        <appender-ref ref="FILE"/>
    </root>

    <!-- Rest接口性能监测BEGIN -->
    <appender name="Perf4JStatisticsFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/data/logs/${project.build.finalName}/${project.build.finalName}-perf.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/data/logs/${project.build.finalName}/${project.build.finalName}-perf.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%msg%n</pattern>
        </encoder>
    </appender>
    <appender name="coalescingStatisticsAppender" class="org.perf4j.logback.AsyncCoalescingStatisticsAppender">
        <timeSlice>3600000</timeSlice><!-- 每小时汇总一次 -->
        <appender-ref ref="Perf4JStatisticsFile"/>
    </appender>
    <logger name="org.perf4j.TimingLogger" level="info" additivity="false">
        <appender-ref ref="coalescingStatisticsAppender" />
    </logger>
    <!-- Rest接口性能监测END -->

</configuration>