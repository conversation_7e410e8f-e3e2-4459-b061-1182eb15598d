{"field_list": [{"filed_name": "申请编号", "filed_colume": "apply_code", "filed_type": 2}, {"filed_name": "订单编号", "filed_colume": "order_code", "filed_type": 2}, {"filed_name": "下单人姓名", "filed_colume": "customer_name", "filed_type": 2}, {"filed_name": "下单人电话", "filed_colume": "customer_mobile", "filed_type": 2}, {"filed_name": "用车类型", "filed_colume": "is_self", "filed_type": 6}, {"filed_name": "用车人姓名", "filed_colume": "vehicle_user_name", "filed_type": 2}, {"filed_name": "订单id", "filed_colume": "order_id", "filed_type": 2}, {"filed_name": "用车人电话", "filed_colume": "vehicle_user_mobile", "filed_type": 2}, {"filed_name": "下单时间", "filed_colume": "order_time", "filed_type": 4}, {"filed_name": "订单开始时间", "filed_colume": "order_stime", "filed_type": 4}, {"filed_name": "订单结束时间", "filed_colume": "order_etime", "filed_type": 4}, {"filed_name": "订单出发地", "filed_colume": "order_splace", "filed_type": 2}, {"filed_name": "订单出发地详细地址", "filed_colume": "order_splace_detail", "filed_type": 2}, {"filed_name": "订单目的地", "filed_colume": "order_eplace", "filed_type": 2}, {"filed_name": "订单目的地详细地址", "filed_colume": "order_eplace_detail", "filed_type": 2}, {"filed_name": "需要司机", "filed_colume": "is_driver", "filed_type": 6}, {"filed_name": "支付方式", "filed_colume": "pay_type", "filed_type": 6}, {"filed_name": "订单状态", "filed_colume": "order_status", "filed_type": 6}, {"filed_name": "审核人姓名", "filed_colume": "audit_staff_name", "filed_type": 2}, {"filed_name": "审核人手机号", "filed_colume": "audit_staff_mobile", "filed_type": 2}, {"filed_name": "审核时间", "filed_colume": "audit_time", "filed_type": 4}, {"filed_name": "调度人姓名", "filed_colume": "schedule_staff_name", "filed_type": 2}, {"filed_name": "调度时间", "filed_colume": "schedule_time", "filed_type": 2}, {"filed_name": "订单取消时间", "filed_colume": "cancel_time", "filed_type": 4}, {"filed_name": "接单车辆车架号", "filed_colume": "vehicle_vin", "filed_type": 2}, {"filed_name": "接单车辆车牌号", "filed_colume": "vehicle_license", "filed_type": 2}, {"filed_name": "接单车辆级别", "filed_colume": "vehicle_type", "filed_type": 6}, {"filed_name": "接单司机姓名", "filed_colume": "driver_name", "filed_type": 2}, {"filed_name": "接单司机手机号", "filed_colume": "driver_mobile", "filed_type": 2}, {"filed_name": "司机出发时间", "filed_colume": "driver_stime", "filed_type": 4}, {"filed_name": "司机到达时间", "filed_colume": "driver_arrive_time", "filed_type": 4}, {"filed_name": "开始计费时间", "filed_colume": "charging_time", "filed_type": 4}, {"filed_name": "乘客上车时间", "filed_colume": "get_on_time", "filed_type": 4}, {"filed_name": "行程结束时间", "filed_colume": "finish_time", "filed_type": 4}, {"filed_name": "结算时间", "filed_colume": "balance_time", "filed_type": 4}, {"filed_name": "策略类型", "filed_colume": "policy_type", "filed_type": 6}, {"filed_name": "订单总里程", "filed_colume": "total_mileage", "filed_type": 5}, {"filed_name": "订单总时长(小时)", "filed_colume": "total_time", "filed_type": 5}, {"filed_name": "总时长费（基础时长费+超时时长费）", "filed_colume": "total_time_fee", "filed_type": 5}, {"filed_name": "总里程费用（基础里程费+超时里程费）", "filed_colume": "total_mileage_fee", "filed_type": 5}, {"filed_name": "基础费", "filed_colume": "base_fee", "filed_type": 5}, {"filed_name": "基础里程", "filed_colume": "base_mileage", "filed_type": 5}, {"filed_name": "基础里程费", "filed_colume": "base_mileage_fee", "filed_type": 5}, {"filed_name": "基础时长(小时)", "filed_colume": "base_time", "filed_type": 5}, {"filed_name": "基础时长费", "filed_colume": "base_time_fee", "filed_type": 5}, {"filed_name": "超里程", "filed_colume": "over_mileage", "filed_type": 5}, {"filed_name": "超里程费", "filed_colume": "over_mileage_fee", "filed_type": 5}, {"filed_name": "超时时长(小时)", "filed_colume": "over_time", "filed_type": 5}, {"filed_name": "超时长费", "filed_colume": "over_time_fee", "filed_type": 5}, {"filed_name": "附加费总额度", "filed_colume": "attach_fee", "filed_type": 4}, {"filed_name": "其他费用", "filed_colume": "other_fee", "filed_type": 5}, {"filed_name": "减免费用", "filed_colume": "reduce_fee", "filed_type": 5}, {"filed_name": "订单调价原因", "filed_colume": "price_explain", "filed_type": 2}], "type_relationship": [{"type_id": 1, "relationship_id": [1, 3, 4]}, {"type_id": 2, "relationship_id": [1, 2, 5, 6]}, {"type_id": 3, "relationship_id": [1, 3, 4, 7]}, {"type_id": 4, "relationship_id": [1, 3, 4, 7]}, {"type_id": 5, "relationship_id": [1, 3, 4]}, {"type_id": 6, "relationship_id": [1, 2]}], "relationship": {"1": "等于", "2": "不等于", "3": "大于", "4": "小于", "5": "包含", "6": "不包含", "7": "等于范围", "8": "大于等于", "9": "小于等于"}, "dropdown_option": {"time_range": {"1": "今天", "2": "昨天", "3": "本周", "4": "上周", "5": "本月", "6": "上月", "7": "本年", "8": "去年", "9": "过去7天", "10": "过去30天"}, "order_status": [{"id": 2, "name": "调度中"}, {"id": 5, "name": "已过期"}, {"id": 6, "name": "司机未出发"}, {"id": 7, "name": "订单已取消"}, {"id": 8, "name": "司机已出发"}, {"id": 9, "name": "司机已到达"}, {"id": 10, "name": "行程中"}, {"id": 11, "name": "费用补录中"}, {"id": 12, "name": "待结算"}, {"id": 13, "name": "已完成"}], "is_self": [{"id": 1, "name": "本人"}, {"id": 2, "name": "他人"}], "is_driver": [{"id": 1, "name": "需要"}, {"id": 2, "name": "不需要"}], "pay_type": [{"id": 1, "name": "企业付款"}, {"id": 2, "name": "个人付款"}], "vehicle_type": [{"id": 1, "name": "标准型"}, {"id": 2, "name": "舒适型"}, {"id": 3, "name": "豪华型"}, {"id": 4, "name": "商务型"}, {"id": 5, "name": "尊贵型"}]}}