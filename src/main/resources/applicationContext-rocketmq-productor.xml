<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:cache="http://www.springframework.org/schema/cache"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd
		http://www.springframework.org/schema/cache http://www.springframework.org/schema/cache/spring-cache.xsd">

	<!-- 配置RocketMQ生产者 -->
	<bean id="locationTestDataMQProducer"  class="com.izu.framework.rocketmq.CommonRocketProducer" destroy-method="destroy" scope="singleton">
		<property name="nameServerGroup" value="mrcar_iot"/>     <!-- RocketMQ nameServer分组名称 -->
		<property name="producerGroup" value="mrcar_pc_iot"/>    <!-- 生产者所属分组名称 -->
		<property name="retryTimesWhenSendFailed" value="3"/><!-- 发送失败后重试的次数 -->
		<property name="sendMsgTimeout" value="6000"/>           <!-- 发送消息时的超时时间 -->
	</bean>
	
</beans>
