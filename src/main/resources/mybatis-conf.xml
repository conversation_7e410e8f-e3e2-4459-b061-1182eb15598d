<?xml version="1.0" encoding="UTF-8" ?>  
    <!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN"  
    "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
	<settings>
		<setting name="cacheEnabled" value="false" />
		<setting name="lazyLoadingEnabled" value="false" />
		<setting name="aggressiveLazyLoading" value="true" />
		<setting name="multipleResultSetsEnabled" value="true" />
		<setting name="useColumnLabel" value="true" />
		<setting name="autoMappingBehavior" value="FULL" />
		<setting name="defaultExecutorType" value="SIMPLE" />
		<setting name="defaultStatementTimeout" value="300" />
		<setting name="logImpl" value="${mybatis.logImpl}" />
	</settings>
	<typeHandlers>
		<!-- MySQL date类型转换 -->
		<typeHandler handler="com.izu.framework.database.mybatis.SyDateOnlyTypeHanlder" jdbcType="DATE"/>	
		<typeHandler handler="com.izu.framework.database.mybatis.SyDateOnlyTypeHanlder" javaType="java.util.Date" jdbcType="DATE"/>
		<!-- MySQL datetime类型转换 -->
		<typeHandler handler="com.izu.framework.database.mybatis.SyDateTypeHanlder" javaType="java.util.Date" />
		<typeHandler handler="com.izu.framework.database.mybatis.SyDateTypeHanlder" jdbcType="TIMESTAMP"/>
		<typeHandler handler="com.izu.framework.database.mybatis.SyDateTypeHanlder" javaType="java.util.Date" jdbcType="TIMESTAMP"/>
	</typeHandlers>

</configuration>