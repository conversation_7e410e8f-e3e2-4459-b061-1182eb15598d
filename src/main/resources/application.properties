server.tomcat.threads.max=1000
server.tomcat.max-connections=10000
server.tomcat.accept-count=200

nacos.config.bootstrap.enable=true
nacos.config.auto-refresh=true
nacos.config.max-retry=10
nacos.config.context-path=/

nacos.config.data-id=order.properties
nacos.config.group=order

nacos.config.server-addr=${izunacos.serverAddress}
nacos.config.username=${izunacos.username}
nacos.config.password=${izunacos.password}
nacos.config.namespace=${izunacos.namespace}

nacos.config.ext-config[0].data-ids=base.properties
nacos.config.ext-config[0].group=mrcar
nacos.config.ext-config[0].type=properties
nacos.config.ext-config[0].auto-refresh=true

# support circular reference
spring.main.allow-circular-references=true

spring.mvc.pathmatch.matching-strategy = ANT_PATH_MATCHER

# \u7981\u7528\u6570\u636E\u5E93\u5065\u5EB7\u68C0\u67E5
management.health.db.enabled=false
# \u7981\u7528Elasticsearch\u5065\u5EB7\u68C0\u67E5
management.health.elasticsearch.enabled=false
# \u66B4\u9732 prometheus, health \u7AEF\u70B9
management.endpoints.web.exposure.include=prometheus,health
# \u7981\u7528\u6240\u6709\u6307\u6807
management.metrics.enable.all=false
# \u542F\u7528 JVM \u5185\u5B58\u76F8\u5173\u6307\u6807
management.metrics.enable.jvm=true
# \u542F\u7528\u7CFB\u7EDF\u6307\u6807
management.metrics.enable.system=true
# \u542F\u7528\u8FDB\u7A0B\u6307\u6807
management.metrics.enable.process=true


server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true