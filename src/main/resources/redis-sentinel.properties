#?????????????????:default?
default.redis.sentinel.name=mymaster
default.redis.password=SByn3w4788ubf8bW
default.redis.connectionTimeout=5000
default.redis.soTimeout=5000
default.redis.database=0
default.redis.sentinel.connectString=mrcar-prd-redis-0-7.imrcar.com:26379,mrcar-prd-redis-0-8.imrcar.com:26379
#??????????????????:default?
default.redis.maxActive=120
default.redis.maxIdle=20
default.redis.minIdle=5
default.redis.maxWait=3000
default.redis.testOnBorrow=true
default.redis.testOnReturn=false
default.redis.testWhileIdle=true
default.redis.minEvictableIdleTimeMillis=300000
default.redis.softMinEvictableIdleTimeMillis=300000
default.redis.timeBetweenEvictionRunsMillis=180000
default.redis.numTestsPerEvictionRun=3
default.redis.maxExpireSeconds=604800
#####################################################################################
#?????????????????:session?
session.redis.sentinel.name=mymaster
session.redis.password=SByn3w4788ubf8bW
session.redis.connectionTimeout=5000
session.redis.soTimeout=5000
session.redis.database=0
session.redis.sentinel.connectString=mrcar-prd-redis-0-7.imrcar.com:26379,mrcar-prd-redis-0-8.imrcar.com:26379
session.redis.maxActive=120
session.redis.maxIdle=20
session.redis.minIdle=5
session.redis.maxWait=3000
session.redis.testOnBorrow=true
session.redis.testOnReturn=false
session.redis.testWhileIdle=true
session.redis.minEvictableIdleTimeMillis=300000
session.redis.softMinEvictableIdleTimeMillis=300000
session.redis.timeBetweenEvictionRunsMillis=180000
session.redis.numTestsPerEvictionRun=3
session.redis.maxExpireSeconds=2592000
#####################################################################################
#?????????????????:locker?
locker.redis.sentinel.name=mymaster
locker.redis.password=SByn3w4788ubf8bW
locker.redis.connectionTimeout=5000
locker.redis.soTimeout=5000
locker.redis.database=0
locker.redis.sentinel.connectString=mrcar-prd-redis-0-7.imrcar.com:26379,mrcar-prd-redis-0-8.imrcar.com:26379
locker.redis.maxActive=120
locker.redis.maxIdle=20
locker.redis.minIdle=5
locker.redis.maxWait=3000
locker.redis.testOnBorrow=true
locker.redis.testOnReturn=false
locker.redis.testWhileIdle=true
locker.redis.minEvictableIdleTimeMillis=300000
locker.redis.softMinEvictableIdleTimeMillis=300000
locker.redis.timeBetweenEvictionRunsMillis=180000
locker.redis.numTestsPerEvictionRun=3
locker.redis.maxExpireSeconds=604800
#####################################################################################
#?????????????????:mrcar?
mrcar.redis.sentinel.name=mymaster
mrcar.redis.password=SByn3w4788ubf8bW
mrcar.redis.connectionTimeout=5000
mrcar.redis.soTimeout=5000
mrcar.redis.database=0
mrcar.redis.sentinel.connectString=mrcar-prd-redis-0-7.imrcar.com:26379,mrcar-prd-redis-0-8.imrcar.com:26379
#??????????????????:mrcar?
mrcar.redis.maxActive=120
mrcar.redis.maxIdle=20
mrcar.redis.minIdle=5
mrcar.redis.maxWait=3000
mrcar.redis.testOnBorrow=true
mrcar.redis.testOnReturn=false
mrcar.redis.testWhileIdle=true
mrcar.redis.minEvictableIdleTimeMillis=300000
mrcar.redis.softMinEvictableIdleTimeMillis=300000
mrcar.redis.timeBetweenEvictionRunsMillis=180000
mrcar.redis.numTestsPerEvictionRun=3
mrcar.redis.maxExpireSeconds=604800