server.servlet.context-path=/mrcar
spring.servlet.multipart.max-file-size=100MB
spring.servlet.multipart.max-request-size=300MB

spring.profiles.active=@env.name@
server.tomcat.max-connections=10000
server.tomcat.threads.max=5000

swagger.enabled=false
diamond.enable=false

spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8

# ?????
aj.captcha.jigsaw=classpath:images/jigsaw
aj.captcha.pic-click=classpath:images/pic-click
aj.captcha.cache-type=redis
aj.captcha.type=default
aj.captcha.water-mark=
aj.captcha.water-font=WenQuanZhengHei.ttf
aj.captcha.font-type=WenQuanZhengHei.ttf
aj.captcha.slip-offset=5
aj.captcha.aes-status=true
aj.captcha.interference-options=2
aj.captcha.font-style=1
aj.captcha.font-size=25
aj.captcha.history-data-clear-enable=false
aj.captcha.req-frequency-limit-enable=false
aj.captcha.req-get-lock-limit=500
aj.captcha.req-get-lock-seconds=360
aj.captcha.req-get-minute-limit=30
aj.captcha.req-check-minute-limit=30
#aj.captcha.req-verify-minute-limit=30

#PC??????
loginOutOtherPC=0
#webSocket????PC
webSocketPortPC=8047
#????????
loginWhiteUserPC=***********,***********

#????????????????????????
slow.request.mailAlertAddrTo=<EMAIL>,<EMAIL>

slow.request.mailAlertAddrCc=<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>

video.window.row=2
video.window.col=2



#??????
session.globalSessionTimeout =86400
#?????? ????
mrCar.pc.locked_time=180

#??????????
business.order.remark=???,????,??,??,???

#??????
export.max.line=10000

#????goFunAppSecret
charge.gofun.app.secret=e8b2d3ddef3f18bd2bad18e569955284

#?????
mrcar.policy.downloadUrl=https://saas.imrcar.com/api/insurancePolicy/download?policyNum=replaceUrlNum&fileType=9
